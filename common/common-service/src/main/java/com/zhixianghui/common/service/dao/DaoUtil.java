package com.zhixianghui.common.service.dao;

import org.springframework.dao.DuplicateKeyException;

/**
 * Author: Cmf
 * Date: 2020.3.14
 * Time: 21:32
 * Description:
 */
public class DaoUtil {
    public static boolean isUniqueKeyRepeat(Throwable ex, String tableName, String uniqueKeyName) {
        if (ex == null || !(ex instanceof DuplicateKeyException)) {
            return false;
        }
        String errMsg = ex.getMessage();
        if (errMsg == null) {
            return false;
        }
        return errMsg.contains(tableName) && errMsg.contains("Duplicate entry") && errMsg.contains(String.format("for key '%s'", uniqueKeyName));
    }
}
