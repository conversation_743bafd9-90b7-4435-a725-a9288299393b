package com.zhixianghui.common.service.utils;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Map;
import java.util.Objects;

public class MybatisUtil {

    public static <T> void buildWhere(QueryWrapper<T> queryWrapper,Class<T> entityClass, Map<String,Object> param) {
        final Field[] fields = entityClass.getDeclaredFields();
        final Field[] superFields = entityClass.getSuperclass().getDeclaredFields();
        final Field[] allFields = ArrayUtil.append(fields, superFields);
        for (Field field : allFields) {
            if (Modifier.isStatic(field.getModifiers())) {
                continue;
            }
            final String fieldName = field.getName();
            final Class<?> type = field.getType();
            if (type == String.class) {
                queryWrapper.eq(StringUtils.isNotBlank((String)param.get(fieldName)), StrUtil.toUnderlineCase(fieldName).toUpperCase(), param.get(fieldName));
            }else {
                queryWrapper.eq(!Objects.isNull(param.get(fieldName)), StrUtil.toUnderlineCase(fieldName).toUpperCase(), param.get(fieldName));
            }
        }
    }

    public static <T> void buildWhereLowercase(QueryWrapper<T> queryWrapper,Class<T> entityClass, Map<String,Object> param) {
        final Field[] fields = entityClass.getDeclaredFields();
        final Field[] superFields = entityClass.getSuperclass().getDeclaredFields();
        final Field[] allFields = ArrayUtil.append(fields, superFields);
        for (Field field : allFields) {
            if (Modifier.isStatic(field.getModifiers())) {
                continue;
            }
            final String fieldName = field.getName();
            final Class<?> type = field.getType();
            if (type == String.class) {
                queryWrapper.eq(StringUtils.isNotBlank((String)param.get(fieldName)), StrUtil.toUnderlineCase(fieldName), param.get(fieldName));
            }else {
                queryWrapper.eq(!Objects.isNull(param.get(fieldName)), StrUtil.toUnderlineCase(fieldName), param.get(fieldName));
            }
        }
    }
}
