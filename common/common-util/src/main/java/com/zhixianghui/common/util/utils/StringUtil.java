package com.zhixianghui.common.util.utils;

import com.google.common.base.CaseFormat;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.UUID;

/**
 * Created by jo on 2017/8/12.
 */
public class StringUtil {

    public static boolean isEmpty(CharSequence value) {
        return value == null || value.toString().trim().length() == 0;
    }


    public static boolean isNotEmpty(CharSequence value) {
        return !isEmpty(value);
    }

    public static boolean isNoEmptyList(CharSequence ...values) {
        for (CharSequence value : values) {
            if(isEmpty(value))return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    public static String getUUIDStr() {
        return UUID.randomUUID().toString();
    }

    public static String getMD5UUIDStr() {
        return MD5Util.getMD5Hex(UUID.randomUUID().toString());
    }


    /**
     * 取字符串前n位，如果原字符串不足n位,则返回原字符串
     *
     * @param str .
     * @param n   .
     * @return
     */
    public static String subLeft(String str, int n) {
        if (str == null || str.length() <= n) {
            return str;
        } else {
            return str.substring(0, n);
        }
    }

    /**
     * 取字符串后n位，如果原字符串不足n位,则返回原字符串
     *
     * @param str .
     * @param n   .
     * @return
     */
    public static String subRight(String str, int n) {
        if (str == null || str.length() <= n) {
            return str;
        } else {
            return str.substring(str.length() - n);
        }
    }

    /**
     * 将 long 类型数据转为长度为 width 的字符串，位数不足则在前面补0
     * @param value     原数据
     * @param width     字符串长度
     */
    public static String longToString(long value, int width) {
        return String.format("%0"+ width + "d", value);
    }

    /**
     * 下换线转驼峰
     * @param source
     * @return
     */
    public static String underscoreToCamel(String source) {
        return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, source);
    }

    /**
     * 驼峰转下划线
     * @param source
     * @return
     */
    public static String camelToUnderscore(String source) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, source);
    }

    /**
     * 两个字符串的比较
     * null, " "  @return true
     * " ", " "   @return true
     * null, null @return true
     * a, a       @return true
     * null, a    @return false
     * a, b       @return false
     */
    public static boolean stringEqual(String target1, String target2) {
        if (StringUtils.isAllBlank(target1, target2)) {
            return true;
        }
        if (StringUtils.isAnyBlank(target1, target2)) {
            return false;
        }
        return target1.equals(target2);
    }

    public static String toString(Object obj, String defaultValue) {
        return obj == null? defaultValue: obj.toString();
    }

    public static boolean isDecimal(String source) {
        return NumberUtils.isParsable(source);
    }
}
