package com.zhixianghui.common.util.utils;

import java.math.BigDecimal;

public class NumberToCN {
    /**
     * 汉语中数字大写
     */
    private static final String[] CN_UPPER_NUMBER = {"零", "壹", "贰", "叁", "肆",
            "伍", "陆", "柒", "捌", "玖"};
    /**
     * 汉语中货币单位大写，这样的设计类似于占位符
     */
    private static final String[] CN_UPPER_MONETRAY_UNIT = {"分", "角", "元",
            "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿", "拾", "佰", "仟", "兆", "拾",
            "佰", "仟"};
    /**
     * 特殊字符：整
     */
    private static final String CN_FULL = "整";
    /**
     * 特殊字符：负
     */
    private static final String CN_NEGATIVE = "负";
    /**
     * 金额的精度，默认值为2
     */
    private static final int MONEY_PRECISION = 2;
    /**
     * 特殊字符：零元整
     */
    private static final String CN_ZEOR_FULL = "零元" + CN_FULL;

    /**
     * 把输入的金额转换为汉语中人民币的大写
     *
     * @param numberOfMoney 输入的金额
     * @return 对应的汉语大写
     */
    public static String number2CNMonetaryUnit(BigDecimal numberOfMoney) {
        StringBuilder sb = new StringBuilder();
        // 判断正数、0或负数
        int sigNum = numberOfMoney.signum();
        // 零元整的情况
        if (sigNum == 0) {
            return CN_ZEOR_FULL;
        }
        // 这里会进行金额的四舍五入
        long number = numberOfMoney.movePointRight(MONEY_PRECISION)
                .setScale(0, 4).abs().longValue();
        // 得到小数点后两位值
        long scale = number % 100;
        // 汉字下的数字数组的下标
        int numUnit = 0;
        // 货币单位数组的下标,0位置为分
        int numIndex = 0;
        boolean getZero = false;
        // 判断最后两位数, 一共有四种情况: 00 = 0(表示没有小数点), 01 = 1(第一位小数点为0), 10(只有一位小数点), 11(两位小数点且都不为0)
        if (scale <= 0) {
            numIndex = 2;
            number = number / 100;
            getZero = true;
        }
        if ((scale > 0) && (scale % 10 <= 0)) {
            numIndex = 1;
            number = number / 10;
            getZero = true;
        }
        int zeroSize = 0;
        while (number > 0) {
            // 每次获取到最后一个数
            numUnit = (int) (number % 10);
            if (numUnit > 0) {
                if ((numIndex == 9) && (zeroSize >= 3)) {
                    sb.insert(0, CN_UPPER_MONETRAY_UNIT[6]);
                }
                if ((numIndex == 13) && (zeroSize >= 3)) {
                    sb.insert(0, CN_UPPER_MONETRAY_UNIT[10]);
                }
                sb.insert(0, CN_UPPER_MONETRAY_UNIT[numIndex]);
                sb.insert(0, CN_UPPER_NUMBER[numUnit]);
                getZero = false;
                zeroSize = 0;
            } else {
                // 处理中间出现0的情况
                ++zeroSize;
                if (!getZero) {
                    sb.insert(0, CN_UPPER_NUMBER[numUnit]);
                }
                if (numIndex == 2) {
                    sb.insert(0, CN_UPPER_MONETRAY_UNIT[numIndex]);
                } else if (((numIndex - 2) % 4 == 0) && (number % 1000 > 0)) {
                    sb.insert(0, CN_UPPER_MONETRAY_UNIT[numIndex]);
                }
                getZero = true;
            }
            // 让number每次都去掉最后一个数
            number = number / 10;
            ++numIndex;
        }
        // 如果signum == -1，则说明输入的数字为负数，就在最前面追加特殊字符：负
        if (sigNum == -1) {
            sb.insert(0, CN_NEGATIVE);
        }
        // 输入的数字小数点后两位为"00"的情况，则要在最后追加特殊字符：整
        if (!(scale > 0)) {
            sb.append(CN_FULL);
        }
        return sb.toString();
    }

    public static String specification(BigDecimal number) {
        if (number == null) {
            return null;
        }
        String num = number.toString();
        String[] temp = null;
        if (num.contains(".")) {
            temp = num.split("\\.");
            num = temp[0];
        }
        if (num.length() < 4) {
            return number.toString();
        }
        char[] array = num.toCharArray();
        StringBuilder builder = new StringBuilder();
        int separate = 0;
        for (int i = array.length - 1; i >= 0; i--) {
            builder.insert(0, array[i]);
            separate++;
            if (separate % 3 == 0) {
                builder.insert(0, ",");
            }
        }
        if (temp != null) {
            return builder.append(".").append(temp[1]).toString();
        }
        return builder.toString();
    }

    public static void main(String[] args) {
//        double money = 300123;
//        BigDecimal numberOfMoney = new BigDecimal(money);
//        String s = NumberToCN.number2CNMonetaryUnit(numberOfMoney);
//        System.out.println("你输入的金额为：【" + money + "】   #--# [" + s + "]");
        System.out.println(specification(new BigDecimal("303939333")));
        System.out.println(specification(new BigDecimal("5432782")));
        System.out.println(specification(new BigDecimal("5432782.44")));
        System.out.println(specification(new BigDecimal("-5432782.44")));
        System.out.println(specification(null));
        System.out.println(specification(new BigDecimal("-22.44")));
        System.out.println(specification(new BigDecimal("22")));
        System.out.println(specification(new BigDecimal("2222.00")));
    }
}
