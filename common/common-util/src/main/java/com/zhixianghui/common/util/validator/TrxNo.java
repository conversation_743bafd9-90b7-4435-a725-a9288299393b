package com.zhixianghui.common.util.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Author: Cmf
 * Date: 2020.3.26
 * Time: 18:34
 * Description:流水号校验器（默认为6--32长度，非null，只包含数字，字母，下划线）
 */
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = TrxNoValidator.class)
@Target({ElementType.FIELD})
public @interface TrxNo {

    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    int minLen() default 6;

    int maxLen() default 32;

    String allowChar() default "[0-9a-zA-Z_]";

    boolean allowEmpty() default false;
}
