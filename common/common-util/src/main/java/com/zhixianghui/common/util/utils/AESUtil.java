package com.zhixianghui.common.util.utils;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Base64;

/**
 * AES加解密工具类
 * <AUTHOR>
 * @date 2018-12-15
 */
public class AESUtil {
    public final static String ALG_AES = "AES";
    private static final String ECB_MODE = "AES/ECB/PKCS5Padding";//算法/模式/补码方式

    static{
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    public static String encryptECB(String content, String secKey){
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance(ECB_MODE);
            SecretKeySpec secSpec = genSecretKeySpec(secKey);
            cipher.init(Cipher.ENCRYPT_MODE, secSpec);
            byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return CodeUtil.base64Encode(encrypted);
        }catch(Throwable e){
            throw CommonExceptions.UNEXPECT_ERROR.newWith("AES加密异常", e);
        }
    }

    public static String decryptECB(String content, String secKey){
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance(ECB_MODE);
            SecretKeySpec secSpec = genSecretKeySpec(secKey);
            cipher.init(Cipher.DECRYPT_MODE, secSpec);
            byte[] encrypted1 = CodeUtil.base64Decode(content);
            byte[] original = cipher.doFinal(encrypted1);
            return new String(original, StandardCharsets.UTF_8);
        }catch(Throwable e){
            throw CommonExceptions.UNEXPECT_ERROR.newWith("AES解密异常", e);
        }
    }

    /**
     * 生成密钥对象 密钥可支持16位或32位，如果是32位，可能会报：java.security.InvalidKeyException: Illegal key size 异常，此时需要更换JDK的local_policy.jar和US_export_policy.jar
     * @param secKey
     * @return
     * @throws Exception
     */
    public static SecretKeySpec genSecretKeySpec(String secKey) {
        if (secKey == null || (secKey.length() != 16)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("密钥长度须为16位");
        }
        return new SecretKeySpec(secKey.getBytes(StandardCharsets.UTF_8), ALG_AES);
    }


}
