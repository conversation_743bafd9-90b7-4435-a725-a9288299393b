package com.zhixianghui.common.util.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月04日 09:54:00
 */
@Slf4j
public class SignUtil {

    private static String getUnSignStr_2_0(TreeMap<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry entry : map.entrySet()) {
            if ("aes_key".equals(entry.getKey())
                    || "sign".equals(entry.getKey())) {
                continue;
            }
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return sb.toString();
    }

    /**
     * 算签
     * 汇聚报备、分账等新版本接口使用
     *
     * @param map
     * @param mchPrivateKeyStr
     * @return
     */
    public static String genSign_2_0(TreeMap<String, Object> map, String mchPrivateKeyStr) {
        String unSignStr = getUnSignStr_2_0(map);
        log.info("签名内容: {},签名结果: {},签名key:{}", JSONObject.toJSONString(map), unSignStr,mchPrivateKeyStr);
        try {
            return HEXUtil.encode(MD5Util.getMD5(unSignStr + "&key=" + mchPrivateKeyStr), true);
        } catch (Exception e) {
            log.error("算签异常, 待签名字符串：{}，异常信息：", unSignStr, e);
        }
        return null;
    }
}