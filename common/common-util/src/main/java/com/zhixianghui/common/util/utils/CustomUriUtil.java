package com.zhixianghui.common.util.utils;

import com.zhixianghui.common.statics.exception.CommonExceptions;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * Author: Cmf
 * Date: 2020.4.27
 * Time: 17:58
 * Description:
 */
public class CustomUriUtil {
    public static String buildRmqUri(String topic, String tag) {
        try {
            return String.format("rmq://?topic=%s&tag=%s",
                    URLEncoder.encode(topic, StandardCharsets.UTF_8.name()),
                    URLEncoder.encode(tag, StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("");
        }
    }


}
