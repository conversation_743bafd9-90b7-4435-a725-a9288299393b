package com.zhixianghui.common.util.utils;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Author: Cmf
 * Date: 2019/10/22
 * Time: 10:59
 * Description: 当字符串为null时,转换为null。若格式不正确时，抛出异常
 */
public class ConvertUtil {
    /**
     * 转换为金额（若字符串为null，则返回null）
     *
     * @param amountStr
     * @return 金额或null(若传入字符串为null)
     * @throws BizException 若格式不正确，抛出异常
     */
    public static BigDecimal toAmount(String amountStr) throws BizException {
        if (amountStr == null) {
            return null;
        } else if (ValidateUtil.isAmount(amountStr)) {
            return new BigDecimal(amountStr);
        } else {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("金额字符串格式不正确");
        }
    }

    /**
     * 转换成date(包括时间)（若为null，则返回null）
     *
     * @param dateTimeStr
     * @return date或null(若传入字符串为null)
     * @throws BizException 格式不正确时抛出异常
     */
    public static Date toDateTime(String dateTimeStr) throws BizException {
        if (dateTimeStr == null) {
            return null;
        } else if (ValidateUtil.isDateTime(dateTimeStr)) {
            return DateUtil.DATE_TIME_FORMATTER.parseDateTime(dateTimeStr).toDate();
        } else {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("日期&时间字符串格式不正确");
        }
    }

    /**
     * 转换成date（不包括时间）（若为null，则返回null）
     *
     * @param dateStr
     * @return date或null(若传入字符串为null)
     * @throws BizException 格式不正确时抛出异常
     */
    public static Date toDateOnly(String dateStr) throws BizException {
        if (dateStr == null) {
            return null;
        } else if (ValidateUtil.isDateOnly(dateStr)) {
            return DateUtil.DATE_FORMATTER.parseDateTime(dateStr).toDate();
        } else {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("日期字符串格式不正确");
        }
    }

}
