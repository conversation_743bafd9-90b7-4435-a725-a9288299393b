package com.zhixianghui.common.util.utils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class TimeRangeUtil {

    public static Date latestThreeMonthStartTime() {
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        final LocalDateTime startLocalDateTime = now.minusMonths(3).with(LocalTime.MIN);
        Date startTime = Date.from(startLocalDateTime.toInstant(ZoneOffset.ofHours(8)));

        return startTime;
    }

    public static Date latestOneYearStartTime(String dateTimeStr) {
        LocalDateTime now = LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        final LocalDateTime startLocalDateTime = now.minusYears(1).with(LocalTime.MIN);
        Date startTime = Date.from(startLocalDateTime.toInstant(ZoneOffset.ofHours(8)));

        return startTime;
    }

    public static Date latestOneYearStartTime() {
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        final LocalDateTime startLocalDateTime = now.minusYears(1).with(LocalTime.MIN);
        Date startTime = Date.from(startLocalDateTime.toInstant(ZoneOffset.ofHours(8)));

        return startTime;
    }

    public static String latestThreeMonthStartTimeStr() {
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        final LocalDateTime startLocalDateTime = now.minusMonths(3).with(LocalTime.MIN);
        String startTime = startLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        return startTime;
    }

    public static Date latestOneMonthStartTime() {
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        final LocalDateTime startLocalDateTime = now.minusMonths(1).with(LocalTime.MIN);
        Date startTime = Date.from(startLocalDateTime.toInstant(ZoneOffset.ofHours(8)));

        return startTime;
    }

    public static Date latestOneWeekStartTime() {
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        final LocalDateTime startLocalDateTime = now.minusDays(7).with(LocalTime.MIN);
        Date startTime = Date.from(startLocalDateTime.toInstant(ZoneOffset.ofHours(8)));

        return startTime;
    }

    public static String latestOneYearStartDate() {
        LocalDate now = LocalDate.now(ZoneId.of("Asia/Shanghai"));
        final LocalDate startDate = now.minusYears(1);
        final String dateStr = startDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
        return dateStr;
    }

    public static String latestOneMonthStartDate() {
        LocalDate now = LocalDate.now(ZoneId.of("Asia/Shanghai"));
        final LocalDate startDate = now.minusMonths(1);
        final String dateStr = startDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
        return dateStr;
    }

    public static String latestOneWeekStartDate() {
        LocalDate now = LocalDate.now(ZoneId.of("Asia/Shanghai"));
        final LocalDate startDate = now.minusWeeks(1);
        final String dateStr = startDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
        return dateStr;
    }

    public static Date endTime() {
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        final LocalDateTime endtime = now.with(LocalTime.MAX);

        return Date.from(endtime.toInstant(ZoneOffset.ofHours(8)));
    }

    public static String endTimeStr() {
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        final LocalDateTime startLocalDateTime = now.with(LocalTime.MAX);
        String startTime = startLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        return startTime;
    }

    public static void main(String[] args) {
        System.out.println(TimeRangeUtil.endTime());
        System.out.println(TimeRangeUtil.endTimeStr());
    }

}
