package com.zhixianghui.common.util.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPReply;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName FtpUtil
 * @Description TODO
 * @Date 2023/7/10 16:20
 */
@Slf4j
public class FtpUtil {

    public static FTPClient connection(String hostname,int port,String username,String password){
        FTPClient ftp = new FTPClient();
        try {
            ftp.connect(hostname,port);
            ftp.setControlEncoding("UTF-8");
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_NT);
            conf.setServerLanguageCode("zh");
            //登录ftp
            ftp.login(username,password);
            if (!FTPReply.isPositiveCompletion(ftp.getReplyCode())) {
                ftp.disconnect();
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
        return ftp;
    }

    public static void uploadFile(FTPClient ftpClient, String path, String directory, String fileName, InputStream inputStream){
        try {
            //让客户端告诉服务端开通一个端口用来数据传输（必须要 不然会一直卡死）
            ftpClient.enterLocalPassiveMode();
            //循环生成目录并进入
            stringPath(ftpClient,path,directory);
            //如果缺省该句 传输txt正常 但图片和其他格式的文件传输出现乱码
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            //开始上传
            ftpClient.storeFile(new String(fileName.getBytes("UTF-8"), StandardCharsets.ISO_8859_1), inputStream);
            //关闭输入流
            inputStream.close();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }

    public static void stringPath(FTPClient ftpClient, String path,String directory) throws IOException {
        List<String> string = new ArrayList<>();
        Arrays.stream((path+directory).split("/")).filter(StringUtils::isNotBlank).forEach(string::add);
        StringBuilder paths = new StringBuilder();
        for (String s : string) {
            paths.append("/").append(new String(s.getBytes("UTF-8"), StandardCharsets.ISO_8859_1));
            if(!ftpClient.changeWorkingDirectory(paths.toString())){
                ftpClient.makeDirectory(paths.toString());
            }
        }
        ftpClient.changeWorkingDirectory(paths.toString());
    }

    public static void ftpLogout(FTPClient ftpClient){
        try {
            ftpClient.logout();
        } catch (IOException e) {
            log.error(e.getMessage(),e);
        }
    };

    public static void main(String[] args) throws IOException {
        String hostname = "************";
        int port = 21;
        String username = "ftpuser";
        String password = "Hjzx@1234";
        String path = "/var/ftp/test";
        FTPClient ftpClient = FtpUtil.connection(hostname, port, username,password);
        //上传ftp文件
        FtpUtil.uploadFile(ftpClient, path, "/", "试用期目标制定和评定流程-正佳科技.xlsx", org.apache.commons.io.FileUtils.openInputStream(new File("D:\\正佳科技\\试用期目标制定和评定流程-正佳科技.xlsx")));
        //退出ftp
        FtpUtil.ftpLogout(ftpClient);
    }
}
