package com.zhixianghui.common.util.utils;

import net.lingala.zip4j.model.ZipParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

/**
 * Zip文件压缩工具类
 */
public class ZipUtil {

    private static final Logger log = LoggerFactory.getLogger(ZipUtil.class);


    /**
     * 压缩文件
     * @param srcfile File[] 需要压缩的文件列表
     * @param zipfile File 压缩后的文件
     */
    public static void zipFiles(List<File> srcfile, File zipfile) {
        byte[] buf = new byte[1024];
        try {
            // Create the ZIP file
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipfile));
            // Compress the files
            for (int i = 0; i < srcfile.size(); i++) {
                File file = srcfile.get(i);
                FileInputStream in = new FileInputStream(file);
                // Add ZIP entry to output stream.
                out.putNextEntry(new ZipEntry(file.getName()));
                // Transfer bytes from the file to the ZIP file
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                // Complete the entry
                out.closeEntry();
                in.close();
            }
            // Complete the ZIP file
            out.close();
        } catch (IOException e) {
            log.error("ZipUtil zipFiles exception:"+e);
        }
    }

    /**
     * 解压缩
     *
     * @param zipfile File 需要解压缩的文件
     * @param descDir String 解压后的目标目录
     */
    public static void unZipFiles(File zipfile, String descDir) {
        try {
            // Open the ZIP file
            ZipFile zf = new ZipFile(zipfile);
            for (Enumeration entries = zf.entries(); entries.hasMoreElements();) {
                // Get the entry name
                ZipEntry entry = ((ZipEntry) entries.nextElement());
                String zipEntryName = entry.getName();
                InputStream in = zf.getInputStream(entry);
                // System.out.println(zipEntryName);
                OutputStream out = new FileOutputStream(descDir + zipEntryName);
                byte[] buf1 = new byte[1024];
                int len;
                while ((len = in.read(buf1)) > 0) {
                    out.write(buf1, 0, len);
                }
                // Close the file and stream
                in.close();
                out.close();
            }
        } catch (IOException e) {
            log.error("ZipUtil unZipFiles exception:"+e);
        }
    }

    public static void main(String[] args) throws IOException {
        File dir = new File("D:\\正佳科技\\Test\\export\\54cd0b26a53d419f");
        File[] fs = dir.listFiles();
        System.out.println(fs[0].getAbsolutePath());
        final List<File> listFiles = (List<File>) org.apache.commons.io.FileUtils.listFiles(dir,null,true);
        Long start = System.currentTimeMillis();
        //File zip = zipFileKeepConstructPartition("D:\\正佳科技\\Test\\export\\54cd0b26a53d419f",listFiles);
        //File zip = zipFileKeepConstruct("D:\\正佳科技\\Test\\export\\54cd0b26a53d419f");
        File zip = new File("D:\\正佳科技\\Test\\export\\54cd0b26a53d419f.zip");
        zipFiles(listFiles,zip);
        Long end = System.currentTimeMillis();
        System.out.println(end - start);
        System.out.println(zip.getAbsolutePath());
    }

    public static File zipFileKeepConstructPartition(String folder,int num,List<File> fileList) throws IOException{
        File zipFile = new File(folder + "_" + num + ".zip");
        if (zipFile.exists()){
            zipFile.delete();
        }
        ZipOutputStream zipout = new ZipOutputStream(new FileOutputStream(zipFile));
        File dir = new File(folder);
        File[] fs = dir.listFiles();
        byte[] buf = null;
        if (fs != null){
            for (File f : fs) {
                zip(zipout,f,f.getName(),fileList);
            }
        }
        zipout.flush();
        zipout.close();
        return zipFile;
    }

    /**
     * 递归打包，保留结构
     * @param folder
     * @return
     * @throws IOException
     */
    public static File zipFileKeepConstruct(String folder) throws IOException{
        File zipFile = new File(folder + ".zip");
        if (zipFile.exists()){
            zipFile.delete();
        }
        ZipOutputStream zipout = new ZipOutputStream(new FileOutputStream(zipFile));
        File dir = new File(folder);
        File[] fs = dir.listFiles();
        byte[] buf = null;
        if (fs != null){
            for (File f : fs) {
                zip(zipout,f,f.getName());
            }
        }
        zipout.flush();
        zipout.close();
        return zipFile;
    }

    public static List<File> createSplitZipFileFromFolder(String folder) throws IOException{
        File zipFile = new File(folder + ".zip");
        if (zipFile.exists()){
            zipFile.delete();
        }
        final net.lingala.zip4j.ZipFile zipFileOrg = new net.lingala.zip4j.ZipFile(zipFile);

        zipFileOrg.createSplitZipFileFromFolder(new File(folder), new ZipParameters(), true, 52428800L);
        return zipFileOrg.getSplitZipFiles();
    }

    private static void zip(ZipOutputStream out, File f, String base,List<File> fileList) throws IOException{
        if (f.isDirectory()) {
            File[] fl = f.listFiles();
            out.putNextEntry(new ZipEntry(base + "/"));
            base = base.length() == 0 ? "" : base + "/";
            for (int i = 0; i < fl.length; i++) {
                if (fileList.contains(fl[i])){
                    zip(out, fl[i], base + fl[i].getName());
                }
            }
        } else {
            out.putNextEntry(new ZipEntry(base));
            FileInputStream in = new FileInputStream(f);
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            in.close();
        }
    }

    private static void zip(ZipOutputStream out, File f, String base) throws IOException{
        if (f.isDirectory()) {
            File[] fl = f.listFiles();
            out.putNextEntry(new ZipEntry(base + "/"));
            base = base.length() == 0 ? "" : base + "/";
            for (int i = 0; i < fl.length; i++) {
                zip(out, fl[i], base + fl[i].getName());
            }
        } else {
            out.putNextEntry(new ZipEntry(base));
            FileInputStream in = new FileInputStream(f);
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            in.close();
        }
    }

    /**
     * 将指定文件夹打包成zip
     * @param folder
     * @throws IOException
     */
    public static File zipFile(String folder) throws IOException {
        File zipFile = new File(folder + ".zip");
        if (zipFile.exists()) {
            zipFile.delete();
        }
        ZipOutputStream zipout = new ZipOutputStream(new FileOutputStream(zipFile));
        File dir = new File(folder);
        File[] fs = dir.listFiles();
        byte[] buf = null;
        if(fs!=null){
            for (File f : fs) {
                zipout.putNextEntry(new ZipEntry(f.getName()));
                FileInputStream fileInputStream = new FileInputStream(f);
                buf = new byte[2048];
                BufferedInputStream origin = new BufferedInputStream(fileInputStream,2048);
                int len;
                while ((len = origin.read(buf,0,2048))!=-1) {
                    zipout.write(buf,0,len);
                }
                zipout.flush();
                origin.close();
                fileInputStream.close();
            }
        }
        zipout.flush();
        zipout.close();

        return zipFile;
    }

    /**
     * zip方式压缩
     *
     * @param srcfile
     * @param zipfile
     */
    public static void zipPack(File[] srcfile, File zipfile) {
        byte[] buf = new byte[1024];
        try {
            // ZipOutputStream类：完成文件或文件夹的压缩
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipfile));
            for (int i = 0; i < srcfile.length; i++) {
                FileInputStream in = new FileInputStream(srcfile[i]);
                out.putNextEntry(new ZipEntry(srcfile[i].getName()));
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                out.closeEntry();
                in.close();
            }
            out.close();
        } catch (Exception e) {
            log.error("==>Exception:", e);
        }
    }
}
