package com.zhixianghui.common.util.utils;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class LogEncryUtil {

    private static final Logger logger = LoggerFactory.getLogger(LogEncryUtil.class);

    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final String UTF_8 = "UTF-8";
    private static final String AES = "AES";
    private static final String PASSWORD = "hjzx999@@@###***";//16个字符，256
    
    private static final String SIGNATURE = "LOG_ENCRYT:";//加密标志

    /**
     * aes256加密
     * @param str
     * @return
     */
    public static String encrypt(String str) {
        if (StringUtil.isNotEmpty(str)) {
            try {
                SecretKeySpec keySpec = new SecretKeySpec(PASSWORD.getBytes(UTF_8), AES);
                Cipher cipher = Cipher.getInstance(ALGORITHM);
                cipher.init(Cipher.ENCRYPT_MODE, keySpec);
                byte[] result = cipher.doFinal(str.getBytes(UTF_8));
                return Base64.encodeBase64String(result);
            } catch (Exception e) {
                logger.error("Aes256Encode error:", e);
                return null;
            }
        }
        return null;
    }

    /**
     * aes256解密
     * @param base64Str
     * @return
     */
    public static String decrypt(String base64Str) {
        if (StringUtil.isNotEmpty(base64Str)) {
            String result = null;
            try {
                SecretKeySpec keySpec = new SecretKeySpec(PASSWORD.getBytes(UTF_8), AES);
                Cipher cipher = Cipher.getInstance(ALGORITHM);
                cipher.init(Cipher.DECRYPT_MODE, keySpec);
                byte[] decoded = cipher.doFinal(Base64.decodeBase64(base64Str));
                result = new String(decoded, UTF_8);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return result;
        }
        return null;
    }
    
    /**
     * 对字符串其中一部份加密<br/>
     * @param str
     * @param startRegex 开始标志
     * @param endRegex   结束标志
     * @return
     */
    public static String encrytAndReplace(String str, String startRegex, String endRegex) {
    	if(StringUtil.isNotEmpty(str) && str.indexOf(startRegex) != -1) {
			String match = str.substring(str.indexOf(startRegex)+ startRegex.length(), str.lastIndexOf(endRegex));
			return str.replaceAll(match, SIGNATURE + encrypt(match));
		}
    	return str;
    }

    public static void main(String[] args) {
//        String s = "hello man大山";
//        String encryptStr = LogEncryUtil.encrypt(s);
//        System.out.println("after encrypt s = " + encryptStr);

//        String decryptStr = LogEncryUtil.decrypt("geXBhWD1WgSlmQp4DJXfRrQJYHF/TZYey39dUpOi41qSJmwzNFHHCBlIMqSjKsg5SqPpGGJTBZVb+fvYsQqIxoaDSc6wFCHPlg7Ge79Mx7TkC7LNrK6nvNAPk8jTmuHTafodtIXAGkuMzpLYtBK+ww6zElFoyfZw3T6823q9fbp1/LMDZSWhGqlFuwF6MGSYEBXXhwq1LmARUELvSKeox5KCsQ10ghoVBpQDqCj6OFu6kIKFjckZiq4oedj0+O3/FlPo7Cx94TQVFiBM/62rroaPLL8vWSTP/qjH5aYrFUs3/NVAe47Nwn33e2erezZR+n6KkwIji7peCOR5Vbjll8GSPkM9SdohANO+aANil/dfKScW/yDKoaVNz56eqrh3zBS9+GNBFdLImpZBoAkmD4CvCLpGdzeNqoP056azDkNXPyVnD/j58FMCBTWsfrxXSqPpGGJTBZVb+fvYsQqIxkY/PsRAcP0MuqIzInEOEKgYHJuIrskrGi/r0xXB74L2taagQYCn+fsfojJs3GDxmZCZiSIKDmCfkCsBAGYcppoadZ6tUJJhPSoqAypW93OxSUtrhfhA39dj6KsKkU8NkDboL3wN/YMVptBosqSRfwoS1k/Nk8iK2Eo42baSXZTKSole6jXq5ZCSmesl+Q3a2hTqm/12BqEGkjuCr+Lk/dhoFPmdDW2jdI7GRalQocSTYacFB9sodpLVeRIAtXUdXyGDbE71+Gf40JqCsRnadfcS1k/Nk8iK2Eo42baSXZTKay79EdXAsFnoIEH3DNYUQi4EUiFqG2Nq4SxREKjafpREzkOPqd4bBqTOgCeQfyQHKmOrkBR7A8onBr4Q6TTxdq886K36lUDI8LcCat3k1FdIRTUP4kFfYH03XFmy8it/7K6/F2JiGu70LEUUxf2KGcye9qi4NXfcQ4ZxdN6fXrAshNbwkydmTxLsqeGS4E1yWEoR/Ll68p/MVJmhetPW25qYTRS26GRrSvc/AYmoh0jjaqP4ixg9d9VAOPsVEWhAIvXqt/1QGLZrZGiGu/poz1qeORJsToxB5Mw+dn9ic/ec47GrkbNA+V1usQQZGB7ij1yinxSh6uVFlEEYmtvuK4ibQFVveCFnCniiMWwg86tyctNEw0WaC323n2KVyuwVsqabb3lMvPSjNqhaQj18yovh+6A8Jq0wllLLkSbIZEqmwxmHQ55HaordzcnLTRFhXLSqrGG0ZYvRy7dbNYfrCtaXtzEdkS7lYg01Ou0SUmpCFIaXUClxLyX+QD5jkIEzDkq1FWWT9Re7M0p0RsQf+s7CD23vbhm9VDzhTa170zlPFLNxH+OCrEu8jgHJn0UGiXjH6Gmf/1OWZNMLcB4zjat76+hGqUE8TVGeVpAKk6EdfgPb0kuxZELW+xUldasF3T4jZATZ1wrfwOPrfqterHWdjgJmu7ZBQ5HYlq2M37vZQkpHTa9b/uw8gHFYqYHxmI/t7f9WzCixji0WHj6El91AkfQzZ4bd/cD841yeuJ6O3L48Bvp8O7eay3NhrgZ4pcxLiQEd9r4anH4wxXaxhxR7NdYj4OwzWLssEMGlexzZIh35ODKRCd+Re8+GRUI7tbSl3LcoMQ7t67Av5jeKNY0ogtB+/dFTpqY3nJRlQBB8CFucfEDNk0gkd6DcDeytbEgiUv4vrc74PvEcnkj8bd/W67ogFB+Hs7RmHmEV4dOtd1Y1ERbL1aHx4jzAWlJZvCHj/iXbrcBYcZYrK1VFbbsx4wgHAQZAue3B0mV5x1TC0RzunU8DaAJceA7p1UKrSCSEiON1hranhWoNQxNKqtcRtlFYpAkfYFbStfQJaQMWFa8m08ZpTzcZfscU3mfO98/1OF0eP7c0errrOGJ7F2VbRT1xDZFrJNgr3Yj3u5Y=");
//        System.out.println("after decrypt s = " + decryptStr);
        
        String str = "<root><MsgHeader><MsgVer>1000</MsgVer><SndDt>2020-07-09T15:30:52</SndDt><Trxtyp>1001</Trxtyp><IssrId>49449202</IssrId><Drctn>11</Drctn><SignSN>4083498709</SignSN><EncSN></EncSN><MDAlgo>0</MDAlgo><SignEncAlgo>0</SignEncAlgo><EncAlgo>0</EncAlgo></MsgHeader><MsgBody><BizTp>100001</BizTp><BizFunc>112011</BizFunc><BizAssInf><IPNum>03</IPNum><IPMode>0</IPMode><IPMrchntExpRate></IPMrchntExpRate></BizAssInf><TrxInf><TrxId>****************</TrxId><TrxDtTm>2020-07-09T15:30:52</TrxDtTm><SettlmtDt></SettlmtDt><TrxAmt>CNY0.01</TrxAmt><TrxTrmTp>08</TrxTrmTp></TrxInf><PyerInf><PyerAcctIssrId></PyerAcctIssrId><PyerAcctId>6258091670811234</PyerAcctId><PyerAcctTp></PyerAcctTp><PyerNm>张三</PyerNm><IDTp>01</IDTp><IDNo>440800201001010015</IDNo><MobNo>13800138000</MobNo></PyerInf><PyeeInf><PyeeIssrId>49449202</PyeeIssrId><PyeeAcctIssrId>49449202</PyeeAcctIssrId><PyeeAreaNo>3930</PyeeAreaNo></PyeeInf><ChannelIssrInf><SgnNo>UP0306920149449202020UP0Z2026144000019D2020070839900203</SgnNo></ChannelIssrInf><OrdrInf><OrdrId>****************</OrdrId><OrdrDesc>testUnoin</OrdrDesc></OrdrInf><MrchntInf><MrchntNo>944123400090002</MrchntNo><MrchntTpId>0009</MrchntTpId><MrchntPltfrmNm>机构_测试</MrchntPltfrmNm></MrchntInf></MsgBody></root>{S:ocIwghGZrT4QPxNu4EYjmS8qetQv/Ah+Ei3Y2Mx7Nxh4zPmXV5dp12xgxseM6pAlxoHcucbVwGLh/m//uDhvrM0m8EpduTaMErttKVH9Nf8utyFJNKezCp2NQj4xUxIscJoalnCGe9fMfWRsWdb1w2ePeyWEHMd/TE5rocjCVDA1ppf5WVXB2mrNmQ/7CdqRf5zkCkMMMv0Qh0m9DbeVJQCxtg8HT0hk6vVkK8aoQzhis4rJkkRA0Hyh1eurs2ZIDlohlb7dNY128BP1VYiyQU9TbNswM8fqGRJG66/S/s7zs1NjFG823FG3JXiu+GPFCRC50ypAueLi1YGd8et7cA==},url:http://10.10.30.234:8099/payForUnion.do?channelUrl=http://9.234.51.101:80,bankOrderNo:****************\r\n";
        System.out.println("after decrypt s = " + encrytAndReplace(str, "<PyerInf>", "</PyerInf>"));
    }
}
