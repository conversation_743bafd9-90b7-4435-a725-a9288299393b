package com.zhixianghui.common.util.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Author: Cmf
 * Date: 2020.3.15
 * Time: 1:34
 * Description:金额格式校验注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = AmountValidator.class)
@Target({ElementType.FIELD})
public @interface Amount {
    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    boolean allowZero() default false;

    boolean allowNull() default false;
}
