package com.zhixianghui.common.util.validator;

import com.zhixianghui.common.util.utils.StringUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Author: Cmf
 * Date: 2020.3.26
 * Time: 18:35
 * Description:
 */
public class TrxNoValidator implements ConstraintValidator<TrxNo, String> {

    private boolean allowEmpty;
    private int minLen;
    private int maxLen;
    private String allowChar;

    @Override
    public void initialize(TrxNo constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
        this.minLen = constraintAnnotation.minLen();
        this.maxLen = constraintAnnotation.maxLen();
        this.allowChar = constraintAnnotation.allowChar();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        if (StringUtil.isEmpty(value)) {
            if (!allowEmpty) {
                context.buildConstraintViolationWithTemplate("不能为null或空").addConstraintViolation();
                return false;
            } else {
                return true;
            }
        } else {
            if (value.matches(String.format("%s{%d,%d}", allowChar, minLen, maxLen))) {
                return true;
            } else if (value.length() < minLen || value.length() > maxLen) {
                context.buildConstraintViolationWithTemplate("长度需要在{minLen}和{maxLen}之间").addConstraintViolation();
                return false;
            } else {
                context.buildConstraintViolationWithTemplate("含有不允许字符").addConstraintViolation();
                return false;
            }
        }
    }
}
