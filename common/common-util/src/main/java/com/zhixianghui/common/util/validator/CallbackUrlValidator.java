package com.zhixianghui.common.util.validator;

import com.zhixianghui.common.util.utils.StringUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Author: Cmf
 * Date: 2020.3.15
 * Time: 1:33
 * Description:回调URL格式校验
 */
public class CallbackUrlValidator implements ConstraintValidator<CallbackUrl, String> {

    @Override
    public void initialize(CallbackUrl constraintAnnotation) {

    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtil.isEmpty(s)) {
            return true;
        }
        if (s.length() > 200) {
            return false;
        }
        return s.startsWith("http://") || s.startsWith("https://");
    }

}
