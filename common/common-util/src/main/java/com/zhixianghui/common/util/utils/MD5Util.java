package com.zhixianghui.common.util.utils;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Description:
 * @author: chenyf
 * @Date: 2018/1/5
 */
public class MD5Util {
    private static Logger logger = LoggerFactory.getLogger(MD5Util.class);


    public static String getMD5Hex(String str) {
        return String.valueOf(Hex.encodeHex(getMD5(str), true));
    }

    //用于MD5混淆 不允许修改 by liaozhengxiang
    private static final String MIX_CHAR = "Zhixiang";

    /**
     * @param str
     * @return
     */
    public static byte[] getMD5(String str) {
        MessageDigest messageDigest;
        try {
            messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            if (StringUtil.isNotEmpty(str)) {
                messageDigest.update(str.getBytes("UTF-8"));
            }
        } catch (NoSuchAlgorithmException e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("生成MD5信息时异常", e);
        } catch (UnsupportedEncodingException e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("生成MD5信息时异常", e);
        }
        return messageDigest.digest();
    }

    /**
     * 混淆的MD5
     * <p>
     * 以避免常用的md5数据可以被反推出来
     * <p>
     * 根据一定规则对数据的MD5进行混淆
     * 用于敏感信息明文的MD5运算，不允许修改
     * 否则可能会造成原来的数据无法检索
     * </p>
     *
     * @param str
     * @return
     * @see <a>https://www.cmd5.com/</a>
     */
    public static String getMixMd5Str(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        String firstMd5 = DigestUtils.md5Hex(str);
        return DigestUtils.md5Hex(firstMd5 + MIX_CHAR).toLowerCase();
    }


}
