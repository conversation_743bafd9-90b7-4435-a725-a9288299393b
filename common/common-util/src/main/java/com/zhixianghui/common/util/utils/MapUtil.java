package com.zhixianghui.common.util.utils;

import java.util.HashMap;
import java.util.Map;

public class MapUtil {

    public static Map<String, Object> renameKeys(Map<String, Object> map,String prefix) {
        Map<String, Object> newMap = new HashMap<>();
        if (map == null) {
            return newMap;
        }

        map.forEach((key,value)->{
            newMap.put(prefix + key, value);
        });
        return newMap;
    }

}
