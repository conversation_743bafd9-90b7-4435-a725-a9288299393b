package com.zhixianghui.common.util.validator;


import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * hibernate validation 手机验证
 * 
 * <AUTHOR>
 *
 */
public class PhoneValidator implements ConstraintValidator<Phone, String> {

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		if (StringUtil.isNotEmpty(value)) {
			return ValidateUtil.isMobile(value);
		}
		return true;
	}
}
