package com.zhixianghui.common.util.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.ConvertUtilsBean;
import org.apache.commons.beanutils.Converter;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BeanUtil {
    /**
     * 两个对象之间的属性复制，把source的值复制给dest
     *
     * @param source
     * @param dest
     * @throws BizException
     */
    public static void copyProperties(Object source, Object dest) throws BizException {
        try {
            BeanUtils.copyProperties(dest, source);
        } catch (Exception e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("对象属性复制异常", e);
        }
    }


    /**
     * 将一个object对象转换成map,
     * 每一个field的名称为key,值为value
     *
     * @param object 当object为Null时,返回的结果一个size为0的map
     * @return map
     */
    public static Map<String, Object> toMap(Object object) {
        Map<String, Object> map = new HashMap<>();
        try {
            new BeanMap(object).forEach((k, v) -> {
                if (!k.equals("class")) {
                    map.put(k.toString(), v);
                }
            });
        } catch (Exception ex) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("bean转换成map失败", ex);
        }
        return map;
    }

    public static Map<String,Object> toMap(Object object,String keyPrefix){
        Map<String, Object> map = new HashMap<>();
        try {
            new BeanMap(object).forEach((k, v) -> {
                if (!k.equals("class")) {
                    map.put(keyPrefix + k.toString(), v);
                }
            });
        } catch (Exception ex) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("bean转换成map失败", ex);
        }
        return map;
    }

    /**
     * 将一个map转为对象
     */
    public static <T> T mapToObject(T t, Map<String, Object> map) {
        if (map == null) {
            return null;
        }

        // 注册BigDecimal转换器，允许处理空值
        ConvertUtilsBean convertUtilsBean = new ConvertUtilsBean();
        convertUtilsBean.register(new Converter() {
            @Override
            public <T> T convert(Class<T> type, Object value) {
                if (value == null) {
                    return null;
                }
                String stringValue = value.toString().trim();
                if (stringValue.isEmpty()) {
                    return null;
                }
                try {
                    return (T) new BigDecimal(stringValue);
                } catch (Exception e) {
                    return null;
                }
            }
        }, BigDecimal.class);

        // 使用自定义的BeanUtilsBean
        BeanUtilsBean beanUtilsBean = new BeanUtilsBean(convertUtilsBean);

        try {
            beanUtilsBean.populate(t, map);
            return t;
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("map转换成对象失败", e);
        }
    }


    public static <T> T toObject(Class<T> clazz,Object obj){
        String json = JSONObject.toJSONString(obj);
        return JSONObject.parseObject(json,clazz);
    }

    public static <T> List<T> toArray(Class<T> clazz, Object obj){
        String json = JSONArray.toJSONString(obj);
        return JSONArray.parseArray(json,clazz);
    }
}
