package com.zhixianghui.common.util.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE })  
@Retention(RetentionPolicy.RUNTIME)  
@Constraint(validatedBy = EmailValidator.class)  
public @interface Email {
	String message() default "电子邮件错误";  
	  
    Class<?>[] groups() default {};  
  
    Class<? extends Payload>[] payload() default {};  
}