package com.zhixianghui.common.util.utils;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.URL;

/**
 * 文件工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileUtils {
    /**
     * 传入文件夹路径，该方法能够实现创建整个路径
     *
     * @param path 文件夹路径，不包含文件名称及后缀名
     */
    public static void isDir(String path) {
        String[] paths = path.split("/");
        String filePath = "";
        for (int i = 0; i < paths.length; i++) {
            if (i == 0) {
                filePath = paths[0];
            } else {
                filePath += "/" + paths[i];
            }
            creatDir(filePath);
        }
    }

    /**
     * 该方法用来判断文件夹是否存在，如果不存在则创建，存在则什么都不做
     *
     * @param filePath
     */
    public static void creatDir(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            file.mkdir();
        }
    }

    /**
     * 删除目录及目录下文件
     *
     * @param dir
     * @return
     */
    public static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();// 递归删除目录中的子目录下
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }
        return dir.delete();
    }

    /**
     * 创建新文件（包括路径），如果已存在，返回原文件，
     *
     * @param filePath
     * @throws IOException
     */
    public static File createFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            if (!file.exists()) {
                file.createNewFile();
            }
            return file;
        } catch (Exception e) {
            log.error("创建文件：{} 出现异常：", filePath, e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创建文件失败" + e.getMessage());
        }
    }

    /**
     * 文件转字节数组
     * @param path 文件路径
     * @return 字节数组
     */
    public static byte[] fileToByteArray(String path) {
        byte[] buffer = null;
        File file = new File(path);
        try (
                FileInputStream fileInputStream = new FileInputStream(file);
             )
        {

            buffer = new byte[(int) file.length()];
            fileInputStream.read(buffer);
        } catch (IOException e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件转换字节数组出错" + e);
        }
        return buffer;
    }

    /**
     * 根据链接地址下载文件
     * @param downloadUrl 文件链接地址
     * @param path        下载存放文件地址 + 文件名
     * @return
     */
    public static byte[] download(String downloadUrl) throws IOException {
        URL url = null;
        DataInputStream dataInputStream = null;
        try {
            url = new URL(downloadUrl);
            dataInputStream = new DataInputStream(url.openStream());
            ByteArrayOutputStream output = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int length;

            while ((length = dataInputStream.read(buffer)) > 0) {
                output.write(buffer, 0, length);
            }
            return output.toByteArray();
        } finally {
            if (dataInputStream != null){
                dataInputStream.close();
            }
        }
    }


    public static byte[] file2byte(File file) {
        if (file == null) {
            return null;
        }
        FileInputStream fileInputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            fileInputStream = new FileInputStream(file);
            byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] b = new byte[1024];
            int n;
            while ((n = fileInputStream.read(b)) != -1) {
                byteArrayOutputStream.write(b, 0 , n);
            }
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
                if (byteArrayOutputStream != null) {
                    byteArrayOutputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static File byte2file(byte[] bytes, String fileFullPath) {
        if (bytes == null) {
            return null;
        }
        FileOutputStream fileOutputStream = null;
        try {
            File file = new File(fileFullPath);
            //判断文件是否存在
            if (!file.exists()) {
                createFile(fileFullPath);
            }
            fileOutputStream = new FileOutputStream(file);
            fileOutputStream.write(bytes);
            return file;
        } catch (Exception e) {
            log.error("转换异常：",e);
        } finally {
            try {
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            }  catch (IOException ioe) {
                log.error("关闭流异常：",ioe);
            }
        }
        return null;
    }

    /**
     * 获取文件后缀
     * @param fileName
     * @return
     */
    public static String getSuffix(String fileName){
        int index = fileName.lastIndexOf(".");
        return fileName.substring(index);
    }
}
