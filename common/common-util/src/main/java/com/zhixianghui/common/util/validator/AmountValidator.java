package com.zhixianghui.common.util.validator;

import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

/**
 * Author: Cmf
 * Date: 2020.3.15
 * Time: 1:33
 * Description:金额格式校验器
 */
public class AmountValidator implements ConstraintValidator<Amount, BigDecimal> {

    private boolean allowZero;
    private boolean allowNull;

    @Override
    public void initialize(Amount constraintAnnotation) {
        this.allowZero = constraintAnnotation.allowZero();
        this.allowNull = constraintAnnotation.allowNull();
    }

    @Override
    public boolean isValid(BigDecimal s, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        if (s == null) {
            if (!allowNull) {
                context.buildConstraintViolationWithTemplate("不能为null").addConstraintViolation();
                return false;
            } else {
                return true;
            }
        }
        boolean b = ValidateUtil.isAmount(s.toString());
        if (allowZero && (AmountUtil.lessThan(s, BigDecimal.ZERO) || !b)) {
            context.buildConstraintViolationWithTemplate("最多两位小数,且必须>=0").addConstraintViolation();
            return false;
        } else if (!allowZero && (AmountUtil.lessThanOrEqualTo(s, BigDecimal.ZERO) || !b)) {
            context.buildConstraintViolationWithTemplate("最多两位小数,且必须>0").addConstraintViolation();
            return false;
        }
        return true;
    }
}
