package com.zhixianghui.common.util.validator;

import com.zhixianghui.common.util.utils.ValidateUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;


/**
 * 电子邮件校验
 * <AUTHOR>
 *
 */
public class EmailValidator  implements ConstraintValidator<Email, String>{

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		if (ValidateUtil.isEmail(value)) {
			return true;
		}
		return false;
	}

}
