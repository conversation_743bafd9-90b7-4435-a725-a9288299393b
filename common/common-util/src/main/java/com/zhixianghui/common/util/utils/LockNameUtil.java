package com.zhixianghui.common.util.utils;

import java.util.HashSet;
import java.util.Set;

public class LockNameUtil {
    
    public static String getAccountInvoiceLockName(String userNo) {
        return "accountInvoiceLock-" + userNo;
    }

    public static Set<String> getAccountInvoiceLockName(Set<String> keys) {
        Set<String> lockNames = new HashSet<>(keys.size());
        for (String key : keys) {
            lockNames.add("accountInvoiceLock-" + key);
        }
        return lockNames;
    }
}
