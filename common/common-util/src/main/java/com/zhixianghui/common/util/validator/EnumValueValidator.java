package com.zhixianghui.common.util.validator;

import com.zhixianghui.common.util.utils.ValidateUtil;
import org.apache.commons.lang3.ArrayUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Objects;


/**
 * 枚举值校验
 * 字段值为空时，不做校验
 * <AUTHOR>
 */
public class EnumValueValidator implements ConstraintValidator<EnumValue, Object> {

    private int[] intValues;
    private String[] strValues;


    @Override
    public void initialize(EnumValue constraintAnnotation) {
        this.intValues = constraintAnnotation.intValues();
        this.strValues = constraintAnnotation.strValues();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (Objects.isNull(value)) {
            return true;
        }

        if (value instanceof String && ArrayUtils.contains(strValues, value)){
                return true;
        }

        if (value instanceof Integer && intValues.length > 0) {
            for (int intValue:intValues){
                if(Objects.equals(intValue, value)){
                    return true;
                }
            }
        }
        return false;
    }
}
