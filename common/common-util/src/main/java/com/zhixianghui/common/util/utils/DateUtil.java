package com.zhixianghui.common.util.utils;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.*;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: chenyf
 * @Date: 2018/1/5
 */
@Slf4j
public class DateUtil {
    public static final String DATE_ONLY_REGEX = "^[1-9]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$";
    public static final String DATE_TIME_REGEX = "^[1-9]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])(\\s+(20|21|22|23|[0-1]\\d):[0-5]\\d:[0-5]\\d)$";

    public static final DateTimeFormatter DATE_TIME_MILLS_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd");
    public static final DateTimeFormatter SLASH_DATE_FORMATTER = DateTimeFormat.forPattern("yyyy/MM/dd");
    public static final DateTimeFormatter COMPACT_DATE_FORMATTER = DateTimeFormat.forPattern("yyyyMMdd");
    public static final DateTimeFormatter SHORT_DATE_FORMATTER = DateTimeFormat.forPattern("yyMMdd");
    public static final DateTimeFormatter SHORTER_DATE_FORMATTER = DateTimeFormat.forPattern("yyyyMM");
    public static final DateTimeFormatter SHORTER_DATE_TIME_FORMATTER = DateTimeFormat.forPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter DATE_YEAR_MONTH = DateTimeFormat.forPattern("yyyy-MM");

    public static final String ZONE_ID="Asia/Shanghai";

    public static String dateYearMonthFormatter(Date date){
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(DATE_YEAR_MONTH);
    }

    /**
     *
     * @param date
     * @return  格式: yyyyMMddHHmmss
     */
    public static String shorterDateTimeFormatter(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(SHORTER_DATE_TIME_FORMATTER);
    }


    /**
     *
     * @param date  年-月-日
     * @return  Date格式的年月日
     */
    public static Date stringToDateTime(String date) {
        DateTime dateTime = DateTime.parse(date, DATE_FORMATTER);
        return dateTime.toDate();
    }


    /**
     *
     * @param date  年-月-日
     * @return  格式: 年月
     */
    public static String formatString(String date) {
        Date newDate = null;
        try {
            newDate = new SimpleDateFormat("yyyy-MM-dd").parse(date);
        } catch (ParseException e) {
            log.error("时间格式错误 : {}, {}", date, e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("时间格式错误");
        }
        return new SimpleDateFormat("yyyy-MM").format(newDate);
    }
    /**
     * 比较 source 和 target 大小，如果 source > target 则返回1，如果 source = target 则返回0，如果 source < target 则返回-1
     *
     * @param source
     * @param target
     * @param withUnit Calendar.YEAR/MONTH/DATE/HOUR/MINUTE/SECOND/MILLISECOND
     * @return
     */
    public static int compare(Date source, Date target, int withUnit) {
        Calendar dateCal = Calendar.getInstance();
        dateCal.setTime(source);

        Calendar otherDateCal = Calendar.getInstance();
        otherDateCal.setTime(target);
        switch (withUnit) {
            case Calendar.YEAR:
                dateCal.clear(Calendar.MONTH);
                otherDateCal.clear(Calendar.MONTH);
            case Calendar.MONTH:
                dateCal.set(Calendar.DATE, 1);
                otherDateCal.set(Calendar.DATE, 1);
            case Calendar.DATE:
                dateCal.set(Calendar.HOUR_OF_DAY, 0);
                otherDateCal.set(Calendar.HOUR_OF_DAY, 0);
            case Calendar.HOUR:
                dateCal.clear(Calendar.MINUTE);
                otherDateCal.clear(Calendar.MINUTE);
            case Calendar.MINUTE:
                dateCal.clear(Calendar.SECOND);
                otherDateCal.clear(Calendar.SECOND);
            case Calendar.SECOND:
                dateCal.clear(Calendar.MILLISECOND);
                otherDateCal.clear(Calendar.MILLISECOND);
            case Calendar.MILLISECOND:
                break;
            default:
                throw new IllegalArgumentException("withUnit 单位字段 " + withUnit + " 不合法！！");
        }
        return dateCal.compareTo(otherDateCal);
    }

    public static Date addDay(Date date, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        return calendar.getTime();
    }

    /**
     * 增加一天，精确到秒
     *
     * @param timeStamp 时间戳，单位是秒
     * @param day
     * @return
     */
    public static int addDay(int timeStamp, int day) {
        String timestamp = String.valueOf(addDay(new Date(timeStamp * 1000L), day).getTime() / 1000);
        return Integer.valueOf(timestamp);
    }

    public static Date addMinute(Date date, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minutes);
        return calendar.getTime();
    }

    public static Date addSecond(Date date, int seconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, seconds);
        return calendar.getTime();
    }

    public static String formatDateTime(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(DATE_TIME_FORMATTER);
    }

    public static String formatDateTimeWithStartTime(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString("yyyy-MM-dd 00:00:00");
    }

    public static String formatDateTimeWithEndTime(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString("yyyy-MM-dd 23:59:59");
    }

    public static String formatDateTimeMills(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(DATE_TIME_MILLS_FORMATTER);
    }

    public static String formatDate(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(DATE_FORMATTER);
    }

    public static String formatChineseDate(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString("yyyy年MM月dd日");
    }

    public static String formatSlashDate(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(SLASH_DATE_FORMATTER);
    }

    public static String formatShortDate(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(SHORT_DATE_FORMATTER);
    }
    public static String formatShorterDate(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(SHORTER_DATE_FORMATTER);
    }

    public static String formatYearMonth(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString("yyyy-MM");
    }

    public static Date yearMonthToDate(String yearMonth) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        return format.parse(yearMonth);
    }

    public static String formatCompactDate(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(COMPACT_DATE_FORMATTER);
    }

    public static Date convertDate(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        String dateStr = dateTime.toString(DATE_FORMATTER);
        return DATE_FORMATTER.parseDateTime(dateStr).toDate();
    }

    public static Date convertDateTime(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        String dateStr = dateTime.toString(DATE_TIME_FORMATTER);
        return DATE_TIME_FORMATTER.parseDateTime(dateStr).toDate();
    }

    public static LocalDateTime parseJodaDateTime(Date date) {
        return new LocalDateTime(date);
    }

    public static LocalDateTime parseJodaDateTime(Long dateTime) {
        return new LocalDateTime(dateTime);
    }

    /**
     * 取得没有毫秒时间戳的日期
     *
     * @return
     */
    public static Date getDateWithoutMills(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.withMillisOfSecond(0).toDate();
    }

    /**
     * 得到day的起始时间点
     *
     * @return
     */
    public static Date getDayStart(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.withTime(0, 0, 0, 0).toDate();
    }

    /**
     * 得到day的起始时间点，精确到秒
     *
     * @return
     */
    public static int getDayStartSecond(Date date) {
        String timestamp = String.valueOf(getDayStart(date).getTime() / 1000);
        return Integer.valueOf(timestamp);
    }

    /**
     * 得到day的终止时间点.
     *
     * @param date
     * @return
     */
    public static Date getDayEnd(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.withTime(23, 59, 59, 999).toDate();
    }

    public static long secondToMillSecond(int seconds) {
        return seconds * 1000L; //此处把秒乘以1000转成毫秒时一定要是Long型的1000，否则会使时间变小
    }

    /**
     * 月份加减
     * @param date
     * @param month
     * @return
     */
    public static Date addMonth(Date date, int month) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.plusMonths(month).toDate();
    }

    public static Date addYear(Date date,int year){
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.plusYears(year).toDate();
    }

    /**
     * 获取月份的第一天
     * @param date
     * @return
     */
    public static Date getFirstOfMonth(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.dayOfMonth().withMinimumValue().toDate();
    }

    /**
     * 获取月份最后一天
     * @param date
     * @return
     */
    public static Date getLastOfMonth(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.dayOfMonth().withMaximumValue().toDate();
    }

    /**
     * 获取当前年份的第一天
     * @return
     */
    public static Date getFirstOfYear(Date date) {
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.dayOfYear().withMinimumValue().toDate();
    }

    /**
     * 获取指定时期所在的月份
     * @param date
     * @return
     */
    public static int getMonth(Date date) {
        LocalDateTime dateTime = DateUtil.parseJodaDateTime(date);
        return dateTime.getMonthOfYear();
    }

    /**
     * 获取指定时期所在的年份
     * @param date
     * @return
     */
    public static String getYear(Date date) {
        LocalDateTime dateTime = DateUtil.parseJodaDateTime(date);
        return String.valueOf(dateTime.getYear());
    }

    public static int getYearToInt(Date date){
        LocalDateTime dateTime = DateUtil.parseJodaDateTime(date);
        return dateTime.getYear();
    }

    /**
     * 根据身份证号码获取年龄
     * @param idCard
     * @return
     */
    public static int getAgeByIdCard(String idCard) {
        if (StringUtil.isEmpty(idCard)) {
            return -1;
        }
        String brithDayStr;
        if (idCard.length() == 15) {
            brithDayStr = "19" + idCard.substring(6, 12);
        } else if (idCard.length() == 18) {
            brithDayStr = idCard.substring(6, 14);
        } else {//默认是合法身份证号，但不排除有意外发生
            return -1;
        }
        LocalDate birthday = DateTime.parse(brithDayStr, COMPACT_DATE_FORMATTER).toLocalDate();
        Period period = new Period(birthday, new LocalDate(), PeriodType.yearMonthDay());
        return period.getYears();
    }

    /**
     * 日期相减，获取相差天数
     * @param d1
     * @param d2
     * @return
     */
    public static Integer subtractDays(Date d1, Date d2){
        return Math.toIntExact((d1.getTime() - d2.getTime()) / 1000 / 60 / 60 / 24);
    }

    public static Date parse(String dataStr){
        return DATE_FORMATTER.parseDateTime(dataStr).toDate();
    }

    public static Date parseTime(String dataStr){
        return DATE_TIME_FORMATTER.parseDateTime(dataStr).toDate();
    }

    public static Date parseTimeWithMills(String dataStr){
        return  DATE_TIME_MILLS_FORMATTER.parseDateTime(dataStr).toDate();
    }

    public static Date getLastMonth(Date date) {
//        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, - 1); // 设置为上一个月

        date = calendar.getTime();
        return date;
    }

    /**
     * 获取两天之间的日期
     * @param 开始时间
     * @param 结束时间
     * @return 时间集合
     */
    public static List<Date> getBetweenDates(String startTime, String endTime) {
        Date startDate = DateUtil.stringToDateTime(startTime);
        Date endDate = DateUtil.stringToDateTime(endTime);
        return getBetweenDates(startDate,endDate);
    }

    /**
     * 获取两天之间的日期
     * @param 开始时间
     * @param 结束时间
     * @return 时间集合
     */
    public static List<Date> getBetweenDates(Date start, Date end) {
        end=addDay(end,1);
        List<Date> days = new ArrayList<Date>();
        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(start);
        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(end);
        while (tempStart.before(tempEnd)) {
            days.add(tempStart.getTime());
            tempStart.add(Calendar.DAY_OF_YEAR, 1);
        }
        return days;
    }

    /**
     * 日期集合转换
     * @param 日期集合
     * @return 日期字符串集合
     */
    public static List<String> changeStringList(List<Date> dates){
        return changeStringList(dates,"yyyy-MM-dd");
    }

    /**
     * 日期集合转换
     * @param 日期集合
     * @param 格式化参数
     * @return 日期字符串集合
     */
    public static List<String> changeStringList(List<Date> dates,String pattern){
        SimpleDateFormat sdf=new SimpleDateFormat(pattern);
        return dates.stream().map(sdf::format).collect(Collectors.toList());
    }

    public static Date getFirstDayOfLastMonth(String dateTime){
        return getFirstDayOfLastMonth(stringToDateTime(dateTime));
    }

    /**
     * 获取上个月第一天
     * @param 日期
     * @return 日期
     */
    public static Date getFirstDayOfLastMonth(Date date){
        Date lastMonth = getLastMonth(date);
        return getFirstOfMonth(lastMonth);
    }

    /**
     * 获取上个月最后一天
     * @param 日期
     * @return 日期
     */
    public static Date getLastDayOfLastMonth(String dateTime){
        return getLastDayOfLastMonth(stringToDateTime(dateTime));
    }

    public static Date getLastDayOfLastMonth(Date date){
        Date lastMonth = getLastMonth(date);
        return getLastOfMonth(lastMonth);
    }

    public static java.time.LocalDateTime now(){
        return java.time.LocalDateTime.now(ZoneId.of(ZONE_ID));
    }



    public static void main(String[] args) throws ParseException {

        System.out.println(isLastDayOfMonth(new Date()));
        System.out.println(isLastDayOfMonth(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-02-28 00:00:00")));
        System.out.println(isLastDayOfMonth(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-07-30 00:00:00")));
        System.out.println(isLastDayOfMonth(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-06-30 00:00:00")));

    }

    public static boolean isLastDayOfMonth(Date date) {
        final Date lastOfMonth = getLastOfMonth(date);
        final int day0 = cn.hutool.core.date.DateUtil.dayOfMonth(lastOfMonth);
        final int day1 = cn.hutool.core.date.DateUtil.dayOfMonth(date);
        if (day0 == day1) {
            return true;
        }
        return false;
    }

}
