package com.zhixianghui.common.util.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 枚举值校验
 * <AUTHOR>
 * @date 2020-09-07
 */
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnumValueValidator.class)
@Target({ElementType.FIELD})
public @interface EnumValue {
    String message() default "必须为指定值";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    int[] intValues() default {};

    String[] strValues() default {};
}
