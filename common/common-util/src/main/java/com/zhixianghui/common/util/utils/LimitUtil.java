package com.zhixianghui.common.util.utils;

import com.zhixianghui.common.statics.exception.CommonExceptions;

/**
 * 常用校验
 * <AUTHOR>
 * @date 2020/11/9
 **/
public class LimitUtil {

    /**
     * 参数为空则抛异常
     * @param param
     * @param errorMsg
     */
    public static void notEmpty(Object param, String errorMsg){
        if(ValidateUtil.isEmpty(param)){
           throw CommonExceptions.PARAM_INVALID.newWithErrMsg(errorMsg);
        }
    }


    /**
     * 参数不为空则抛异常
     * @param param
     * @param errorMsg
     */
    public static void empty(Object param, String errorMsg){
        if(!ValidateUtil.isEmpty(param)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg(errorMsg);
        }
    }
}
