package com.zhixianghui.common.util.utils;


import org.apache.commons.lang3.StringUtils;

/**
 * 数据脱敏工具类
 */
public class DesensitizeUtil {

    /**
     * 处理手机号码。eg：18688886666 -> 186****6666
     * 前三后四
     * @return 脱敏后的手机号码
     */
    public static String handleMobile(String originMobile) {
        if (StringUtil.isEmpty(originMobile)) {
            return originMobile;
        }
        String head = "";
        String tail = "";
        if (originMobile.length() >= 10) {
            head = originMobile.substring(0, 3);
            tail = originMobile.substring(originMobile.length()-4);
        } else if(originMobile.length() > 3){
            head = originMobile.substring(0, 3);
            tail = originMobile.substring(originMobile.length()-2);
        } else {
            head = originMobile.substring(0, 1);
            tail = "";
        }
        return head + "****" + tail;
    }

    /**
     * 处理身份证, 一般18位。eg：370022188805202836 -> 370022****2836
     * 处理银行卡号，一般16-19位。eg：6217002710000684874 -> 621700****84874
     * @return 脱敏后的身份证或银行卡号
     */
    public static String handleIdNumOrBankCardNo(String origin) {
        if (StringUtil.isEmpty(origin)) {
            return origin;
        }
        String head = "";
        String tail = "";
        if (origin.length() >= 10) {
            head = origin.substring(0, 6);
            tail = origin.substring(origin.length()-4);
        } else if(origin.length() > 3){
            head = origin.substring(0, 3);
            tail = origin.substring(origin.length()-2);
        } else {
            head = origin.substring(0, 1);
            tail = "";
        }
        return head + "****" + tail;
    }

    /**
     * 处理姓名。eg：1）李大牛 -> 李*牛
     *          2） 李牛     -> 李*
     *          3） 李超级牛  ->李*牛
     * @param originName 原姓名
     * @return 脱敏后的姓名
     */
    public static String handleNameDesenCenter(String originName) {
        if (StringUtil.isEmpty(originName)) {
            return originName;
        }

        String firstChar = originName.substring(0, 1);
        if(originName.length() <= 2){
            return firstChar + "*";
        }else{
            return firstChar + "*" +originName.substring(originName.length()-1);
        }
    }

    /**
     * 处理邮箱。eg：1）<EMAIL> -> 123****@qq.com
     * @return 脱敏后的邮箱
     */
    public static String handleEmailDesenCenter(String originName) {
        if (StringUtil.isEmpty(originName) || StringUtils.length(originName) <= 6) {
            return originName;
        }

        String head = originName.substring(0, 3);
        String tail = "";
        if(originName.contains("@")){
            tail = originName.substring(originName.indexOf("@"));
        } else {
            tail = originName.substring(originName.length()-3);
        }
        return head + "****" +tail;
    }

    public static void main(String[] args) {
        System.out.println(handleEmailDesenCenter("<EMAIL>"));
    }
    /**
     * 脱敏字符。根据数据的长度添加适当的*号
     *
     * @param originPlain 原串
     * @return 脱敏后的字符串
     */
    public static String handleDesenCenter(String originPlain) {
        if (StringUtil.isEmpty(originPlain)) {
            return originPlain;
        }
        String firstChar = originPlain.substring(0, 1);
        if (originPlain.length() <= 2) {
            return firstChar + getDesenStr(originPlain.length() - 1);
        } else if (originPlain.length() <= 9) {
            return firstChar + getDesenStr(originPlain.length() - 2) + originPlain.substring(originPlain.length() - 1);
        } else {
            return originPlain.substring(0, 3) + getDesenStr(4) + originPlain.substring(originPlain.length() - 4);
        }
    }

    private static String getDesenStr(int length) {

        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append("*");
        }
        return sb.toString();
    }

    /**
     * 隐藏卡号信息(和旧收单统一脱敏规则 前6后4)
     *
     * @param cardNo
     * @return
     */
    private static String maskCardNo(String cardNo) {
        if (StringUtil.isEmpty(cardNo) || cardNo.trim().length() <= 8) {
            return cardNo;
        }
        cardNo = cardNo.trim();
        int length = cardNo.length();
        String firstFourNo = cardNo.substring(0, 6);
        String lastFourNo = cardNo.substring(length - 4);
        StringBuffer mask = new StringBuffer("");
        for (int i = 0; i < length - 8; i++) {
            mask.append("*");
        }
        return firstFourNo + mask.toString() + lastFourNo;
    }

    /**
     * 隐藏银行卡号码(和旧收单统一脱敏规则 前6后4)
     *
     * @param bankCardNo
     * @return
     */
    public static String maskBankCardNo(String bankCardNo) {
        return maskCardNo(bankCardNo);
    }

    /**
     * 隐藏证件(和旧收单统一脱敏规则 前6后4)
     *
     * @param payerIdCardNo
     * @return
     */
    public static String maskPayerIdCardNo(String payerIdCardNo) {
        return maskCardNo(payerIdCardNo);
    }
}
