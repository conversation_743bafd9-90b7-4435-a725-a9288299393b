/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.zhixianghui.common.util.utils;

import com.zhixianghui.common.statics.exception.CommonExceptions;

/**
 * Util class for checking arguments.
 */
public class AssertUtil {

    private AssertUtil() {
    }

    public static void notEmpty(String string, String message) {
        if (StringUtil.isEmpty(string)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg(message);
        }
    }

    public static void notNull(Object object, String message) {
        if (object == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg(message);
        }
    }

    public static void assertState(boolean condition, String message) {
        if (!condition) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(message);
        }
    }
}
