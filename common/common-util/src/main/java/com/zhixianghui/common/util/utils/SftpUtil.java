package com.zhixianghui.common.util.utils;

import com.jcraft.jsch.*;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.Properties;
import java.util.Vector;

/**
 * ClassName: SftpUtil 连接类包括上传下载<br/>
 * Function: <br/>
 * date: 2014-2-24 上午10:28:10 <br/>
 * <AUTHOR>
 * @修改: Wushuicheng, 2014-04-24.
 */
@Slf4j
public class SftpUtil {

	/**
	 * 连接sftp服务器
	 * 
	 * @param host
	 *            主机
	 * @param port
	 *            端口
	 * @param username
	 *            用户名
	 * @param password
	 *            密码
	 * @return
	 */
	public static ChannelSftp connect(String host, int port, String username, String password) {
		ChannelSftp sftp = null;
		try {
			JSch jsch = new JSch();
//			jsch.getSession(username, host, port);
			Session sshSession = jsch.getSession(username, host, port);
			sshSession.setPassword(password);
			Properties sshConfig = new Properties();
			sshConfig.put("StrictHostKeyChecking", "no");
			sshSession.setConfig(sshConfig);
			sshSession.connect();
			Channel channel = sshSession.openChannel("sftp");
			channel.connect();
			sftp = (ChannelSftp) channel;
		} catch (Exception e) {
			log.error("sftp connect exception:", e);
		}
		return sftp;
	}

	/**
	 * 连接sftp服务器，会抛出异常
	 * @param host
	 * @param port
	 * @param username
	 * @param password
	 * @return
	 */
	public static ChannelSftp connectNotSafe(String host, int port, String username, String password) throws Exception {
		ChannelSftp sftp = null;
		try {
			JSch jsch = new JSch();
			Session sshSession = jsch.getSession(username, host, port);
			sshSession.setPassword(password);
			Properties sshConfig = new Properties();
			sshConfig.put("StrictHostKeyChecking", "no");
			sshSession.setConfig(sshConfig);
			sshSession.connect();
			Channel channel = sshSession.openChannel("sftp");
			channel.connect();
			sftp = (ChannelSftp) channel;
		} catch (Exception e) {
			log.error("sftp connect exception:", e);
			throw new Exception("获取sftp连接失败");
		}
		return sftp;
	}
	
	public static ChannelSftp connect2(String host, int port, String username, String privateKeyPath) {
		ChannelSftp sftp = null;
		try {
			JSch jsch = new JSch();
			//添加私钥 
			jsch.addIdentity(privateKeyPath); 
			Session sshSession = jsch.getSession(username, host, port);
			Properties sshConfig = new Properties();
			sshConfig.put("StrictHostKeyChecking", "no");
			sshSession.setConfig(sshConfig);
			sshSession.connect();
			Channel channel = sshSession.openChannel("sftp");
			channel.connect();
			sftp = (ChannelSftp) channel;
		} catch (Exception e) {
			log.error("sftp connect exception:", e);
		}
		return sftp;
	}

	/**
	 * 上传文件
	 * 
	 * @param directory
	 *            上传的目录
	 * @param uploadFile
	 *            要上传的文件
	 * @param channel
	 */
	public static void upload(String directory, String uploadFile, ChannelSftp channel) {
		FileInputStream fileInputStream = null;
		try {
			creatDir(directory, channel);
			channel.cd(directory);
			File file = new File(uploadFile);
			fileInputStream = new FileInputStream(uploadFile);
			channel.put(fileInputStream, file.getName());
			sftpClose(channel);
		} catch (Exception e) {
			log.error("sftp upload exception:", e);
		}finally {
			try {
				if(fileInputStream!=null){
					fileInputStream.close();
				}
			}catch (IOException e){
				log.error("流关闭异常,",e);
			}
			if (channel != null) {
				sftpClose(channel);
			}
		}
	}
	
	/**
     * 上传文件
     *
     * @param directory
     *            上传的目录
     * @param uploadFile
     *            要上传的文件
     * @param channel
     */
    public static void upload(String directory, File uploadFile, ChannelSftp channel) {
		FileInputStream fileInputStream = null;
        try {
            creatDir(directory, channel);
            channel.cd(directory);
			fileInputStream = new FileInputStream(uploadFile);
            channel.put(fileInputStream, uploadFile.getName());
            sftpClose(channel);
        } catch (Exception e) {
			log.error("sftp upload exception:", e);
        }finally {
			try {
				if(fileInputStream!=null){
					fileInputStream.close();
				}
			}catch (IOException e){
				log.error("流关闭异常,",e);
			}
            if (channel != null) {
                sftpClose(channel);
            }
        }
    }

	/**
	 * 上传文件，会抛出异常
	 *
	 * @param directory
	 *            上传的目录
	 * @param uploadFile
	 *            要上传的文件
	 * @param channel
	 */
	public static void uploadNotSafe(String directory, File uploadFile, ChannelSftp channel) throws Exception{
		FileInputStream fileInputStream = null;
		try {
			creatDir(directory, channel);
			channel.cd(directory);
			fileInputStream = new FileInputStream(uploadFile);
			channel.put(fileInputStream, uploadFile.getName());
			sftpClose(channel);
		} catch (Exception e) {
			log.error("sftp upload exception:", e);
			throw new Exception("上传到sftp失败");
		}finally {
			try {
				if(fileInputStream!=null){
					fileInputStream.close();
				}
			}catch (IOException e){
				log.error("流关闭异常,",e);
			}
			if (channel != null) {
				sftpClose(channel);
			}
		}
	}
    /**
     * 上传文件暂不关闭channel
     *
     * @param directory
     *            上传的目录
     * @param uploadFile
     *            要上传的文件
     * @param channel
     */
    public static void uploadNoClose(String directory, File uploadFile, String fileName, ChannelSftp channel) {
		FileInputStream fileInputStream = null;
		try {
			creatDir(directory, channel);
			fileInputStream = new FileInputStream(uploadFile);
			channel.put(fileInputStream, fileName);
        } catch (Exception e) {
            log.error("sftp upload exception:", e);
        }finally {
			try {
				if(fileInputStream!=null){
					fileInputStream.close();
				}
			}catch (IOException e){
				log.error("流关闭异常,",e);
			}
		}
    }
	
	/**
	 * 上传文件流
	 * 
	 * @param stream
	 * @param fileName
	 * @param channel
	 */
	public static void upload(String directory, InputStream stream, String fileName, ChannelSftp channel) {
		try {
			creatDir(directory, channel);
			channel.cd(directory);
			channel.put(stream, fileName);
			sftpClose(channel);
		} catch (Exception e) {
			log.error("sftp upload exception:", e);
		}finally {
			try {
				if(stream!=null){
					stream.close();
				}
			}catch (IOException e){
				log.error("流关闭异常,",e);
			}
			if (channel != null) {
				sftpClose(channel);
			}
		}
	}
	
	
	/**
	 * 上传文件
	 * 
	 * @param directory
	 *            上传的目录
	 * @param uploadFile
	 *            要上传的文件
	 * @param channel
	 * @throws SftpException 
	 */
	public static void upload(String directory, String fileName, InputStream uploadFile, ChannelSftp channel) throws SftpException {
		creatDir(directory, channel);
		channel.cd(directory);
		channel.put(uploadFile, fileName);
		sftpClose(channel);
	}

	/**
	 * 下载文件
	 * @param downloadFilePath
	 *            下载的文件
	 * @param saveFile
	 *            存在本地的路径
	 * @param channel
	 */
	public static void downloadNoClose(String downloadFilePath, File saveFile, ChannelSftp channel) {
		FileOutputStream fileOutputStream = null;
		try {
			fileOutputStream = new FileOutputStream(saveFile);
			channel.get(downloadFilePath, fileOutputStream);
		} catch (Exception e) {
			log.error("{}文件下载异常:", downloadFilePath, e);
			if(e.getMessage().contains("No such file")){
				throw CommonExceptions.PARAM_INVALID.newWithErrMsg("文件不存在");
			}
		}finally {
			try {
				if(fileOutputStream!=null){
					fileOutputStream.close();
				}
			}catch (IOException e){
				log.error("流关闭异常,",e);
			}
		}
	}

    /**
     * 下载文件
     * @param downloadFilePath
     *            下载的文件
     * @param saveFile
     *            存在本地的路径
     * @param channel
     */
    public static void download(String downloadFilePath, File saveFile, ChannelSftp channel) {
        try {
            channel.get(downloadFilePath, new FileOutputStream(saveFile));
        } catch (Exception e) {
            log.error("sftp download exception:", e);
        } finally {
            if(channel != null){
                sftpClose(channel);
            }
        }
    }

	/**
	 * 下载文件
	 * 
	 * @param directory
	 *            下载目录
	 * @param downloadFile
	 *            下载的文件
	 * @param saveFile
	 *            存在本地的路径
	 * @param channelSftp
	 */
	public static byte[] downloadGetByte(String directory, String downloadFile, String saveFile, ChannelSftp channelSftp) {
		try {
			channelSftp.cd(directory);
			File file = new File(saveFile);
			channelSftp.get(downloadFile, new FileOutputStream(file));
			return readFileByBytes(file.getPath());
		} catch (Exception e) {
			log.error("sftp downloadGetString excetpin:", e);
		}
		return null;
	}
	
	/**
	 * 以行为单位读取文件，常用于读面向行的格式化文件
	 */
	public static byte[] readFileByBytes(String fileName) {
		byte[] fileByte = null;
		FileInputStream fis = null;
		ByteArrayOutputStream bos = null;
		try {
			fis = new FileInputStream(fileName);
			// 以行为单位读取文件内容，一次读一整行
			bos = new ByteArrayOutputStream(fis.available());
			byte[] buffer = new byte[1024];
			int count = 0;
			while ((count = fis.read(buffer)) != -1) {
				bos.write(buffer, 0, count);
			}
			bos.flush();
			fileByte = bos.toByteArray();
		} catch (IOException e) {
			log.error("readFileByBytes IOException", e);
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (Exception e) {
					log.error(e.getLocalizedMessage(), e);
				}
			}
			if (bos != null) {
				try {
					bos.close();
				} catch (Exception e) {
					log.error(e.getLocalizedMessage(), e);
				}
			}
		}
		return fileByte;
	}
	
	/**
	 * 下载文件
	 * 
	 * @param directory
	 *            下载目录
	 * @param downloadFile
	 *            下载的文件
	 * @param saveFile
	 *            存在本地的路径
	 * @param channelSftp
	 */
	public static String downloadGetString(String directory, String downloadFile, String saveFile, ChannelSftp channelSftp) {
		try {
			channelSftp.cd(directory);
			File file = new File(saveFile);
			channelSftp.get(downloadFile, new FileOutputStream(file));
			return readFileByLines(file.getPath());
		} catch (Exception e) {
			log.error("sftp downloadGetString excetpin:", e);
		}
		return null;
	}

	/**
	 * 以行为单位读取文件，常用于读面向行的格式化文件
	 */
	public static String readFileByLines(String fileName) {
		StringBuffer sb = new StringBuffer();
		File file = new File(fileName);
		BufferedReader reader = null;
		try {
			// 以行为单位读取文件内容，一次读一整行
			reader = new BufferedReader(new FileReader(file));
			String tempString = null;
			// 一次读入一行，直到读入null为文件结束
			while ((tempString = reader.readLine()) != null) {
				// 显示行号
				sb.append(tempString);
			}
			reader.close();
		} catch (IOException e) {
			log.error("readFileByLines IOException", e);
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					log.error("BufferedReader close exception:", e);
				}
			}
		}
		return sb.toString();
	}

	/**
	 * 删除文件
	 * 
	 * @param directory
	 *            要删除文件所在目录
	 * @param deleteFile
	 *            要删除的文件
	 * @param sftp
	 */
	public void delete(String directory, String deleteFile, ChannelSftp sftp) {
		try {
			sftp.cd(directory);
			sftp.rm(deleteFile);
		} catch (Exception e) {
			log.error("delete Exception", e);
		}
	}

	/**
	 * 列出目录下的文件
	 * 
	 * @param directory
	 *            要列出的目录
	 * @param sftp
	 * @return
	 * @throws SftpException
	 */
	public static Vector listFiles(String directory, ChannelSftp sftp) throws SftpException {
		try{
			return sftp.ls(directory);
		} catch (SftpException e){
			if(e.getMessage().contains("No such file")){
				return null;
			} else {
				throw e;
			}
		}
	}

	/**
	 * 根据传入的目录创建文件夹
	 * 
	 * @param directory
	 * @param sftp
	 * @throws SftpException
	 */
	public static void creatDir(String directory, ChannelSftp sftp) throws SftpException {
		String[] dirArr = directory.split("/");
		StringBuffer tempStr = new StringBuffer("");
		for (int i = 1; i < dirArr.length; i++) {
			tempStr.append("/" + dirArr[i]);
			try {
				sftp.cd(tempStr.toString());
			} catch (SftpException e) {
				try {
					sftp.mkdir(tempStr.toString());
                    sftp.cd(tempStr.toString());
				} catch (Exception ex) {
					// 可能是由于并发创建文件，而失败。
					log.error("==>directory={} currentDir={} ", directory, tempStr, e);
					// 检查路径是否已存在。最终不存在再抛异常
					try {
						sftp.cd(tempStr.toString());
					} catch (SftpException exx) {
						log.error("not exit directory={}", directory);
						throw exx;
					}
				}
			}
		}
	}

	/**
	 * sftpClose:关闭Sftp <br/>
	 * 
	 * @param channel
	 */
	public static void sftpClose(ChannelSftp channel) {
		if (channel == null) {
			return;
		}
		Session session = null;
		try {
			session = channel.getSession();
		} catch (Exception e) {
			log.error("sftp channel getSession exception:", e);
		}
		//要先关闭channel，再关闭session
		try {
			channel.disconnect();
		} catch (Exception e) {
			log.error("sftp channel disconnect exception:", e);
		}
		try {
			if (session != null) {
				session.disconnect();
			}
		} catch (Exception e) {
			log.error("sftp session disconnect exception:", e);
		}
	}
	
	/***
	 * 连接SFTP服务器，根据文件路径读取文件文本内容.<br/>
	 * 
	 * @param dataFilePath
	 *            SFTP保存的文件全路径
	 * @return 文件内容.
	 */
	public static String getFileContentFormSFTP(final ChannelSftp channelSftp, final String dataFilePath) {
		String property = System.getProperty("user.dir") + File.separator + "temp/";
		FileUtils.isDir(property);
		String directory = dataFilePath.substring(0, dataFilePath.lastIndexOf("/")); // 文件路径
		String downloadFile = dataFilePath.substring(dataFilePath.lastIndexOf("/") + 1); // 文件名称
		String saveFile = property + "/" + downloadFile; // 保存文件路径
		log.info("==>从SFTP获取文件内容，源文件路径[" + dataFilePath + "], 保存本地的临时文件路径[" + saveFile + "]");
		return downloadGetString(directory, downloadFile, saveFile, channelSftp);
	}
	
	/**
	 * 从SFTP服务器上下载文件
	 * 
	 * @return
	 */
//	public static File downFileFromSFTP(ChannelSftp channelSftp, final String filePath) {
//		// 创建临时目录，用来存放下载的文件
//		StringBuffer tempFilePath = new StringBuffer(System.getProperty("user.dir")).append(File.separator).append("temp");
//		isDir(tempFilePath.toString());
//		String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
//		String tempPath = filePath.substring(0, filePath.lastIndexOf("/") + 1);
//
//		// 创建临时返回文件
//		String saveFile = tempFilePath + "/" + fileName;
//		File returnFile = new File(saveFile);
//		try {
//			download(tempPath, fileName, saveFile, channelSftp);
//		} catch (Exception e) {
//			log.error("==>对账文件下载失败：", e);
//		} finally {
//			if (channelSftp != null) {
//				sftpClose(channelSftp);
//			}
//		}
//		return returnFile;
//	}
	
	/**
	 * 传入文件夹路径，该方法能够实现创建整个路径
	 * 
	 * @param path
	 *            文件夹路径，不包含文件名称及后缀名
	 */
	public static void isDir(String path) {
		String[] paths = path.split("/");
		String filePath = "";
		for (int i = 0; i < paths.length; i++) {
			if (i == 0) {
				filePath = paths[0];
			} else {
				filePath += "/" + paths[i];
			}
			creatDir(filePath);
		}
	}

	/**
	 * 该方法用来判断文件夹是否存在，如果不存在则创建，存在则什么都不做
	 * 
	 * @param filePath
	 */
	public static void creatDir(String filePath) {
		File file = new File(filePath);
		if (!file.exists()) {
			file.mkdir();
		}
	}

	// 测试例子
	public static void main(String[] args) throws SftpException {
		SftpUtil sf2 = new SftpUtil();
		ChannelSftp stfp = connect2("**************", 8001, "hzzf_20200907", "D:\\doc\\同步\\跨境\\SFPT公私钥\\mykey_pri.ppk");
		stfp.cd("/KjShDetailCheck");
		
		SftpUtil sf = new SftpUtil();
		String host = "***********";
		int port = 3210;
		String username = "gwpayfast";
		String password = "gzzyzz.com";
		String directory = "/home/<USER>/";
		
		String downloadFile = "Result.txt";
		String saveFile = "F:\\123.txt";
		
		String uploadFile = "E:\\PINGANBANK-NET-B2C-GZ20140523clear.txt";
		// String deleteFile = "delete.txt";
		ChannelSftp sftp = connect(host, port, username, password);
		upload(directory, uploadFile, sftp);
//		sf.download(directory, downloadFile, saveFile, sftp);
		// sf.delete(directory, deleteFile, sftp);
		try {
//			sf.creatDir(directory, sftp);
			// sftp.cd(directory);
			// System.out.println("finished");
//			sf.sftpClose(sftp);
		} catch (Exception e) {
			log.error("Exception", e);
		} finally {
			if (sf != null){
				sftpClose(sftp);
			}
		}
	}

}
