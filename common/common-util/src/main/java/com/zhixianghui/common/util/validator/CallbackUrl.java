package com.zhixianghui.common.util.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Author: Cmf
 * Date: 2020.3.25
 * Time: 16:44
 * Description:
 */
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CallbackUrlValidator.class)
@Target({ElementType.FIELD})
public @interface CallbackUrl {
    String message() default "URL必须是http://或https://,且长度不能大于200";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
