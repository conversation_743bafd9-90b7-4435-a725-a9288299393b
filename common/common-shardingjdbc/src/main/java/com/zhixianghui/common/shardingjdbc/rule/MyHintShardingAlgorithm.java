package com.zhixianghui.common.shardingjdbc.rule;

import com.google.common.collect.Lists;
import org.apache.shardingsphere.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.hint.HintShardingValue;
import java.util.Collection;
@Deprecated
public class MyHintShardingAlgorithm implements HintShardingAlgorithm {

	@Override
	public Collection<String> doSharding(Collection collection, HintShardingValue hintShardingValue) {
		Collection<String> lists = Lists.newArrayList();
		return lists;
	}
}
