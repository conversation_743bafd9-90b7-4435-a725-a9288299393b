package com.zhixianghui.common.shardingjdbc.rule;

import com.google.common.collect.Range;
import com.zhixianghui.common.util.utils.DateUtil;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingValue;

import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;

/**
 * @description: 用于按时间分表的between操作
 * @author: xingguang li
 * @created: 2020/11/04 10:31
 */
public class MonthRangeShardingAlgorithm implements RangeShardingAlgorithm<Date> {

	@Override
	public Collection<String> doSharding(Collection<String> availableTargetNames,
										 RangeShardingValue<Date> shardingValue) {

		Collection<String> results = new LinkedHashSet<>(availableTargetNames.size());
		Range<Date> values = shardingValue.getValueRange();
		//参数传递过来的范围，进而比较范围
		Date beginDate = values.lowerEndpoint();
		Date endDate = values.upperEndpoint();
		//如果起止时间大小有误，直接返回
		if(beginDate.compareTo(endDate) > 0 ) {
			return results;
		}
		//如果起止时间一致，直接查找具体表名
		String beginDateStr = DateUtil.formatShorterDate(beginDate);
		String endDateStr = DateUtil.formatShorterDate(endDate);
		if(beginDateStr.equals(endDateStr)) {
			for (String availableTableName : availableTargetNames) {
				if (availableTableName.endsWith(beginDateStr)) {
					results.add(availableTableName);
				}
			}
			return results;
		}
		//起止时间不一致，则遍历时间取匹配表名
		//月份累加，直到达到截止月份
		while (DateUtil.formatShorterDate(beginDate).compareTo(endDateStr) <= 0 ) {
			String tableExt = DateUtil.formatShorterDate(beginDate);
			for (String availableTableName : availableTargetNames) {
				if (availableTableName.endsWith(tableExt)) {
					results.add(availableTableName);
				}
			}
			beginDate = DateUtil.parseJodaDateTime(beginDate).plusMonths(1).toDate();
		}
		return results;
	}

}
