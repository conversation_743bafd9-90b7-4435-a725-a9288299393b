package com.zhixianghui.common.shardingjdbc.rule;

import com.zhixianghui.common.util.utils.DateUtil;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import java.util.Collection;
import java.util.Date;

/**
 * @description: 用于按时间分表的=和in操作
 * @author: xingguang li
 * @created: 2020/11/04 10:31
 */
public class MonthTableShardingAlgorithm implements PreciseShardingAlgorithm<Date> {

	@Override
	public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Date> shardingValue) {
	    String tableExt = DateUtil.formatShorterDate(shardingValue.getValue());
        for (String availableTableName : availableTargetNames) {
            if (availableTableName.endsWith(tableExt)) {
                return availableTableName;
            }
        }
        return null;
	}

}
