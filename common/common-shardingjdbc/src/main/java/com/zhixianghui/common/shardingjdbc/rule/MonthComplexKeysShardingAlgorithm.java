package com.zhixianghui.common.shardingjdbc.rule;

import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.complex.ComplexKeysShardingValue;
import java.util.Collection;
import java.util.Date;

@Deprecated
public class MonthComplexKeysShardingAlgorithm implements ComplexKeysShardingAlgorithm<Date> {

    @Override
    public Collection<String> doSharding(Collection<String> collection, ComplexKeysShardingValue<Date> complexKeysShardingValue) {
        return null;
    }
}
