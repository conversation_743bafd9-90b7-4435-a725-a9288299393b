<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-common</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>

    <artifactId>common-shardingjdbc</artifactId>
    <description>由于分库分表的shardingjdbc解决方案</description>

    <properties>
        <sharding-jdbc.version>4.1.1</sharding-jdbc.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
            <version>${sharding-jdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-util</artifactId>

        </dependency>
    </dependencies>
</project>
