package com.zhixianghui.common.statics.enums.export;

import java.util.Arrays;

/**
 * 导出状态
 * <AUTHOR>
 * @date 2020/8/28
 **/
public enum ExportStatusEnum {
    CREATE(102, "已创建"),
    EXPORTING(103, "导出中"),
    SUCCESS(100, "成功"),
    FAIL(101, "失败"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    ExportStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static ExportStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
