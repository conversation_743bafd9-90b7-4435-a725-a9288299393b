package com.zhixianghui.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 商户认证状态枚举
 * <AUTHOR>
 * @date 2020-08-05
 */
public enum AuthStatusEnum {

    SUCCESS(100, "已认证"),
    FAIL(101, "认证失败"),
    UN_AUTH(102, "未认证"),
    APPROVAL(103, "认证审核中"),
    UN_QUOTE(104,"未成交")
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    AuthStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static AuthStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}