package com.zhixianghui.common.statics.enums.tradesync;

import java.util.Arrays;

/**
 * Author: Cmf
 * Date: 2020.3.14
 * Time: 20:43
 * Description:订单同步状态
 */
public enum OrderSyncStatusEnum {
    CREATED(1, "已创建"),
    SYNC_SUCCESS(2, "同步成功"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    OrderSyncStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static OrderSyncStatusEnum getEnum(int value) {
        return Arrays.stream(values())
                .filter(p -> p.value == value)
                .findFirst()
                .orElse(null);
    }
}
