package com.zhixianghui.common.statics.enums.merchant;

import com.zhixianghui.common.statics.enums.agent.AgentTypeEnum;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 身份证件类型
 * <AUTHOR>
 * @date 2020-08-05
 */
public enum CertificateTypeEnum {

    ID_CARD(100, "身份证"),
    PASSPORT(101, "护照(限境外人士)"),
    INLAND_PASS_MACAO(102, "来往内地通行证（澳门）"),
    INLAND_PASS_HONGKONG(103, "来往内地通行证（香港）"),
    INLAND_PASS_TAIWAN(104, "来往内地通行证（台湾）"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    CertificateTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static CertificateTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

    public static List<Integer> listValues(){
        return Arrays.stream(values()).map(CertificateTypeEnum::getValue).collect(Collectors.toList());
    }

    public static String getDesc(int value) {
        return getEnum(value).getDesc();
    }
}