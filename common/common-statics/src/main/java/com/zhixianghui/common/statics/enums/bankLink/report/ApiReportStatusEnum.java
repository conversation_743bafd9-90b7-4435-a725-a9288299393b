package com.zhixianghui.common.statics.enums.bankLink.report;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 渠道接口报备状态
 * @date 2020-10-30 16:35
 **/
@AllArgsConstructor
@Getter
@ToString
public enum ApiReportStatusEnum {
    /**
     * 成功
     */
    SUCCESS(100, "成功"),

    /**
     *失败
     */
    FAIL(101, "失败"),

    /**
     * 处理中
     */
    PROCESS(102, "处理中"),

    /**
     *未知
     */
    UN_KNOW(103, "未知")
    ;

    /**
     * 枚举值
     */
    private final int value;

    /**
     * 描述
     */
    private final String desc;

    public static ApiReportStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
