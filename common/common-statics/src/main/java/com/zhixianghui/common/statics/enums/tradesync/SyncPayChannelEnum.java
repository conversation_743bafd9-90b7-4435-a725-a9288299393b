package com.zhixianghui.common.statics.enums.tradesync;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * Author: Cmf
 * Date: 2020.3.14
 * Time: 20:31
 * Description:
 */
public enum SyncPayChannelEnum {
    ALIPAY_OFFICIAL("ALIPAY_OFFICIAL", "支付宝官方"),
    ;

    private String channel;
    private String desc;

    public String getChannel() {
        return channel;
    }

    public String getDesc() {
        return desc;
    }

    SyncPayChannelEnum(String channel, String desc) {
        this.channel = channel;
        this.desc = desc;
    }

    public static SyncPayChannelEnum getEnum(String channel) {
        return Stream.of(values())
                .filter(p -> Objects.equals(channel, p.getChannel()))
                .findFirst()
                .orElse(null);
    }

}
