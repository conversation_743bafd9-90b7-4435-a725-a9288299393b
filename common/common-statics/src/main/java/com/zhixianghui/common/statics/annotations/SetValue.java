package com.zhixianghui.common.statics.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Description //设置属性
 **/
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface SetValue {

    String className();

    String method();

    String[] paramField();

    boolean isCheckNull() default true;

    String targetField() default "";
}
