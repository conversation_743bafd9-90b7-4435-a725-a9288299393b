package com.zhixianghui.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 商户文件枚举
 * <AUTHOR>
 * @date 2020-08-05
 */
public enum MerchantFileTypeEnum {
    COMPANY_LEAFLET(100, "公司宣传资料"),
    ENTRUST_AGREEMENT(101, "委托代征协议"),
    AGREEMENT_TEMPLATE_TO_B(102, "B端协议模板"),
    AGREEMENT_TEMPLATE_TO_C(103, "C端协议模板"),
    BUSINESS_LICENSE(104, "营业执照"),
    ID_CARD_HEAD(105, "身份证头像面"),
    ID_CARD_EMBLEM(106, "身份证国徽面"),
    ID_CARD_COPY(107, "身份证复印件"),
    DOOR_PHOTO(108, "门头照"),
    WORK_INDOOR(109, "办公内景"),
    RECEPTION(111, "前台照片"),
    SUPPLEMENT_INFO(112, "补充信息"),
    PLATFORM_LOGO(113,"平台logo"),
    LOGIN_LOGO(114,"登录logo")
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    MerchantFileTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static MerchantFileTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}