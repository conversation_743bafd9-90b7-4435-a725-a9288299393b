package com.zhixianghui.common.statics.enums.invoice;

import java.util.Arrays;

/**
 * 开票金额类型
 * <AUTHOR>
 */
public enum InvoiceAmountTypeEnum {
    ORDER_AMOUNT("订单总额",1),
    GRANT_AMOUNT("实发金额",2),
    SERVICE_FEE("服务费",3),
    ;

    private String desc;
    private int value;

    InvoiceAmountTypeEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static InvoiceAmountTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
