package com.zhixianghui.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 商户状态枚举
 * <AUTHOR>
 * @date 2020-08-05
 */
public enum MchStatusEnum {

    ACTIVE(100, "已激活"),
    INACTIVE(101, "已冻结"),
    CREATE(102, "已创建"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    MchStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static MchStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}