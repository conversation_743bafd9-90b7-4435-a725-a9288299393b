package com.zhixianghui.common.statics.enums.common;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * Author: Cmf
 * Date: 2020.4.24
 * Time: 11:28
 * Description:
 */
public enum SuccessFailEnum {

    SUCCESS("success"),

    FAIL("fail");

    private final String code;

    public String getCode() {
        return code;
    }

    SuccessFailEnum(String code) {
        this.code = code;
    }

    public static SuccessFailEnum getEnum(String code) {
        return Stream.of(values()).filter(p -> Objects.equals(code, p.getCode())).findFirst().orElse(null);
    }
}
