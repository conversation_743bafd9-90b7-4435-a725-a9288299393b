package com.zhixianghui.common.statics.enums.common;

import java.util.Arrays;

public enum PublicStatusEnum {

    ACTIVE(100, "启用"),
    INACTIVE(101, "禁用");
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    PublicStatusEnum(int value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static PublicStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
