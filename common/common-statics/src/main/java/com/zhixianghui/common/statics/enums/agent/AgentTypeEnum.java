package com.zhixianghui.common.statics.enums.agent;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 合伙人类型枚举
 * @date 2021/2/2 10:30
 **/
@AllArgsConstructor
@Getter
@ToString
public enum AgentTypeEnum {
    /**
     * 个人
     */
    PERSON(100, "个人"),
    /**
     * 公司
     */
    COMPANY(101, "公司"),
    ;

    private final int value;
    private final String desc;

    public static AgentTypeEnum getEnum(int value) {
        return Arrays.stream(values())
                .filter(p -> p.value == value)
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(int value) {
        return getEnum(value).getDesc();
    }
}
