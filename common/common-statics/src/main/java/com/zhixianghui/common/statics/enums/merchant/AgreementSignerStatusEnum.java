package com.zhixianghui.common.statics.enums.merchant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName AgreementSignerStatusEnum
 * @Description TODO
 * @Date 2022/8/17 11:16
 */
@AllArgsConstructor
@Getter
@ToString
public enum AgreementSignerStatusEnum {

    SIGNED(100,2,"已签"),

    WAIT_SIGN(101,-1,"待签"),

    WAIT_AUTH(102,-2,"待审批"),

    REFUSE(103,4,"拒签"),

    FAIL(104,5,"失败");

    private int value;

    private int signResult;

    private String desc;

    public static AgreementSignerStatusEnum getEnum(int signResult) {
        return Arrays.stream(values()).filter(p -> p.signResult == signResult).findFirst().orElse(null);
    }
}
