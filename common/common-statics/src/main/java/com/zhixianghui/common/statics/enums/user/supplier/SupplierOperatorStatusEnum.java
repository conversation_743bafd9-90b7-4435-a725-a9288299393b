package com.zhixianghui.common.statics.enums.user.supplier;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 供应商后台操作员状态枚举
 * @date 2020-12-10 16:57
 **/
@Getter
@ToString
@AllArgsConstructor
public enum SupplierOperatorStatusEnum {
    /**
     * 激活
     */
    ACTIVE("激活", 1),
    /**
     * 冻结
     */
    INACTIVE("冻结", -1),
    ;

    private final String desc;
    private final int value;
}
