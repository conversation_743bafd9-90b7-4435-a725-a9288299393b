package com.zhixianghui.common.statics.enums.user.portal;

import java.util.Arrays;
import java.util.Objects;

/**
 * 商户后台员工类型
 */
public enum PortalStaffTypeEnum {

    /**
     * 超级管理员
     **/
    ADMIN("超级管理员", 1),

    /**
     * 普通用户
     **/
    USER("普通用户", 2),
    ;

    /**
     * 枚举值
     */
    private final int value;

    /**
     * 描述
     */
    private final String desc;


    PortalStaffTypeEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }


    public static PortalStaffTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> Objects.equals(value, p.getValue())).findFirst().orElse(null);
    }

}
