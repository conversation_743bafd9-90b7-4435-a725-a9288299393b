package com.zhixianghui.common.statics.annotations;

import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月29日 17:28:00
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface EnumDesc {
    String name();
    SystemTypeEnum type();
}
