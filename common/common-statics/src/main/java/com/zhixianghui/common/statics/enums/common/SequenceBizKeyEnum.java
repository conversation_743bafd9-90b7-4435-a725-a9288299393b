package com.zhixianghui.common.statics.enums.common;

/**
 * 序列号生成Key，不同业务Key值唯一, 长度不能超过10
 *
 * <AUTHOR>
 */
public enum SequenceBizKeyEnum {

    FORM_NO_SEQ("Form","form_no_seq",6),
    /**
     * 小程序用户编号
     */
    MINI_USER_NO_SEQ("MWX", "mini_user_no_seq", 9),

    /**
     * 小程序信息编号
     */
    MINI_NO_SEQ("MINI", "mini_user_no_seq", 9),

    /**
     * 合伙人月账单编号
     */
    AGENT_MONTH_BILL_SEQ("B", "agent_month_bill_seq", 9),
    /**
     * 用工企业编号
     */
    MERCHANT_EMPLOYER_SEQ("M", "merchant_employer_seq", 8),

    /**
     * 代征主体编号
     */
    MERCHANT_MAINSTAY_SEQ("S", "merchant_mainstay_seq", 6),

    /**
     * 代理商编号
     */
    MERCHANT_AGENT_SEQ("A", "merchant_agent_seq", 6),

    /**
     * 订单编号
     */
    ORDER_SEQ("G", "order_seq", 9),
    HY_ORDER_SEQ("HY", "order_seq", 9),

    /**
     * 订单明细编号
     */
    ORDER_ITEM_SEQ("E", "order_item_seq", 9),

    /**
     * 签约个人账号编号
     */
    SIGN_ACCOUNT_SEQ("SI", "sign_account_seq", 8),

    OPEN_USER_ID_SEQ("OU", "open_user_id_seq", 8),

    /**
     * 签约个人账号编号
     */
    AUTH_RECORD_SEQ("AUS", "auth_record_seq", 8),

    /**
     * 用户授权协议编号
     */
    AUTH_AGREEMENT_SEQ("AUG","auth_agreement_seq",9),

    /**
     * 打款流水
     */
    RECORD_ITEM_SEQ("R", "record_item_seq", 9),

    /**
     * 导出文件编号
     */
    EXPORT_FILE_SEQ("", "export_file_seq", 6),
    /**
     * 发票账户编号
     */
    INVOICE_ACCOUNT_NO_SEQ("6", "invoice_account_no_seq", 8),
    /**
     * 发票账务流水号
     */
    INVOICE_ACCOUNT_TRX_NO_SEQ("600", "invoice_account_trx_no_seq", 8),
    /**
     * 开票记录流水号
     */
    INVOICE_TRX_NO_SEQ("700", "invoice_trx_no_seq", 8),

    ALI_RECHARGE_NO_SEQ("800", "ali_recharge_no_seq", 8),

    WITHDRAW_NO_SEQ("800", "withdraw_no_seq", 9),

    QUOTE_FLOW_NO_SEQ("QF","quote_flow_no_seq",9),
    /**
     * 充值凭证编号
     */
    RECHARGE_CERTIFICATE_NO_SEQ("RC","recharge_certificate_no_seq",8),
    DOWNLOAD_CERTIFICATE_NO_SEQ("DC","download_certificate_no_seq",8),
    /**
     * 路由id
     */
    TRACE_ID("Tid","tid_no_seq",8),

    /**
     * 商户微信调账
     */
    WX_ADJUSTMENT("A", "wx_adjustment", 8),

    /**
     * 供应商君享汇调账
     */
    WX_MAINSTAY_ADJUSTMENT("B", "wx_mainstay_adjustment", 8),

    /**
     * 商户君享汇调账
     */
    AC_ADJUSTMENT("A", "ac_adjustment", 8),

    /**
     * 供应商君享汇调账
     */
    AC_MAINSTAY_ADJUSTMENT("B", "ac_mainstay_adjustment", 8),

    /**
     * 商户君享汇调账
     */
    CMB_ADJUSTMENT("A", "cmb_adjustment", 8),

    /**
     * 供应商君享汇调账
     */
    CMB_MAINSTAY_ADJUSTMENT("B", "cmb_mainstay_adjustment", 8),


    CMB_WITHDRAW_NO_SEQ("8", "withdraw_no_seq", 7),

    CKH_JOB_ID_SEQ("2", "ckh_job_id_seq", 7),

    CKH_ORDER_SEQ("C","ckh_order_seq:",9),
    CKH_OFFLINE_ORDER_SEQ("OF","ckh_offline_order_seq:",8),
    CKH_ORDER_ITEM_SEQ("T","ckh_order_item_seq:",9),
    CKH_OFFLINE_ORDER_ITEM_SEQ("OT","ckh_offline_order_item_seq:",8),

    CKH_FEE_ORDER_SEQ("F","ckh_fee_order_seq:",9),
    CKH_FEE_ORDER_ITEM_SEQ("Z","ckh_fee_order_item_seq:",9),

    AGREEMENT_SQL("AGE","agreement_id_seq:",9),

    PROXY_ORDER_NO_SEQ("PE", "proxy_order_no_seq", 9),

    PROXY_PAY_TRX_NO("PP","proxy_pay_trx_no",9),

    ZFT_PAY_TRX_NO_SEQ("PO", "zft_pay_trx_no_seq", 8),
    ZFT_PAY_ORDER_NO_SEQ("Z", "zft_pay_order_no_seq", 8),
    ZFT_SETTLE_TRX_NO_SEQ("ZS", "zft_settle_trx_no_seq", 8),
    TEXT_JXH_SEQ("JXH", "text_jxh_seq", 8),
    ;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * key值
     */
    private String key;

    /**
     * 序列号位数
     */
    private int width;

    SequenceBizKeyEnum(String prefix, String key, int width) {
        this.prefix = prefix;
        this.key = key;
        this.width = width;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getKey() {
        return key;
    }

    public int getWidth() {
        return width;
    }
}
