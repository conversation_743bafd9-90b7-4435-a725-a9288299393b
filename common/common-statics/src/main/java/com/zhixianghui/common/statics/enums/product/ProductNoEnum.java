package com.zhixianghui.common.statics.enums.product;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 产品类型枚举
 * @date 2020-11-17 14:29
 **/
@AllArgsConstructor
@Getter
@ToString
public enum ProductNoEnum {
    /**
     *智享汇
     */
    ZXH("ZXH", "智享汇","经营所得"),
    INVOICE("INVOICE", "发票开具",""),
    ACCOUNT_ADJUST("ACCOUNT_ADJUST", "账户调账",""),
    CKH("CKH","创客汇","经营所得"),
    JKH("JKH","聚客汇","综合所得"),
    ZFT("ZFT","直付通",""),
    CEP("CEP", "差额普","经营所得"),
    ;

    private final String value;
    private final String desc;
    private final String text;

    public static ProductNoEnum getEnum(String value) {
        return Arrays.stream(values()).filter(p -> p.value.equals(value)).findFirst().orElse(null);
    }
}
