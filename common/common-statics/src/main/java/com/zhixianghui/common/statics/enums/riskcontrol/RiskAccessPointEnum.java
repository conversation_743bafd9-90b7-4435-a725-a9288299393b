package com.zhixianghui.common.statics.enums.riskcontrol;

import lombok.Getter;

/**
 * 风控接入点枚举
 *
 * <AUTHOR>
 */
@Getter
public enum RiskAccessPointEnum {

    SETTLE(100, "智享汇", "结算"),

    INVOICE(101, "智享汇", "开票"),
    ;

    private int value;

    private String module;

    private String business;

    RiskAccessPointEnum(int value, String module, String business) {
        this.value = value;
        this.module = module;
        this.business = business;
    }
}
