package com.zhixianghui.common.statics.enums.user.agent;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 供应商后台功能类型枚举
 * @date 2020-12-10 16:42
 **/
@Getter
@ToString
@AllArgsConstructor
public enum AgentFunctionTypeEnum {
    /**
     * 菜单项
     */
    MENU_TYPE("菜单项", 1),

    /**
     * 操作项
     */
    ACTION_TYPE("操作项", 2),
    ;

    private final String desc;
    private final int value;
}
