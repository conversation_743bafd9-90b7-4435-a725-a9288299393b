package com.zhixianghui.common.statics.enums.export;

import java.util.Arrays;

/**
 * 文件类型
 * <AUTHOR>
 * @date 2020/8/28
 **/
public enum FileTypeEnum {
    EXECL(1),
    PDF(2),
    ZIP(3),
    PARAM(4),
    ;
    /**
     * 枚举值
     */
    private int value;

    FileTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static FileTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
