package com.zhixianghui.common.statics.enums.riskcontrol;

import lombok.Getter;

import java.util.Arrays;

/**
 * 管控原子枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ControlAtomEnum {

    PASS(100, "通过"),

    PENDING(200, "挂单"),

    REJECT(300, "拒绝"),

    TO_NEXT(-1,"判断下一个策略原子组")

    ;

    private int value;

    private String desc;

    ControlAtomEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ControlAtomEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
