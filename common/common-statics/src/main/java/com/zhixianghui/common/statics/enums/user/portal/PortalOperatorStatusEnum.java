package com.zhixianghui.common.statics.enums.user.portal;

import java.util.Arrays;

/**
 * 商户后台操作员状态枚举
 *
 * <AUTHOR>
 */
public enum PortalOperatorStatusEnum {

    ACTIVE("激活", 1),

    INACTIVE("冻结", -1),

    ;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 枚举值
     */
    private final int value;

    PortalOperatorStatusEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static PortalOperatorStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.getValue() == value).findFirst().orElse(null);
    }
}