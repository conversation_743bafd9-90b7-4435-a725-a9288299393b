package com.zhixianghui.common.statics.enums.message;

public enum EmailTemplateEnum {
    SUCCESS_INVOICE_EMPLOYER("success_invoice_employer.ftl", "用工企业开票成功通知"),
    APPLY_INVOICE_MAINSTAY("apply_invoice_mainstay.ftl", "代征主体开票申请通知"),
    APPLY_ALIPAY_REPORT("apply_alipay_report.ftl","支付宝签约申请通知"),
    HANGUP_NOTIFY("risk_hangup_notify.ftl","汇聚智享风控挂单通知"),
    RECHARGE_SUCCESS_NOTIFY("recharge_success_notify.ftl","智享汇综合服务平台充值到账通知"),
    SUCCESS_CREATE_MERCHANT("success_mail.ftl","审核通过提醒"),
    BALANCE_NOTIFY("balance_notify.ftl","余额不足提醒"),
    WAIT_FOR_APPROVAL("wait_for_approval.ftl","等待审批提醒"),
    REGISITER_APPLY("regisiter_apply.ftl","体验申请"),
    COMPLETE_APPROVAL("complete_approval.ftl","审批完成提醒"),
    HANGUP_EXPIRE_NOTIFY("risk_hangup_expire_notify.ftl","汇聚智享风控挂单处理结果通知"),
    RECEIPT_ORDERD("receipt_order.ftl","电子回单邮件")
    ;
    private String name;
    private String desc;

    private EmailTemplateEnum(String name, String desc){
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static EmailTemplateEnum getEnum(String account){
        EmailTemplateEnum[] values = EmailTemplateEnum.values();
        for(int i=0; i<values.length; i++){
            if(values[i].name().equals(account)){
                return values[i];
            }
        }
        return null;
    }
}
