package com.zhixianghui.common.statics.constants.redis;

public interface RedisKeysConstant {

    int MINUTE =  60;
    int HOUR =  60 * 60;
    int DAY =  60 * 60 * 24;
    int MONTH =  60 * 60 * 24 * 30;

    String CMB_BALANCE_CACHE_KEY_PREFIX = "balance:cmb:";
    String CMB_ACCOUNT_LOCK_KEY = "account:lock:";
    String JOB_IMPORT_EXCEL_LOCK_KEY = "job:import:excel:lock:key:";
    String USER_INFO_AUTH = "user:info:auth:";
    String FEE_ORDER_FILE_RETRY = "fee:order:file:retry:";
    String AUTH_INFO = "auth:info:";
    String SIGN_INFO = "sign:info:";
    String ESIGN_MCH_LOCK = "esign:mch:lock:";
    String INVOICE_OFFLINE_APPLY_CACHE_PREFIX = "inoivce:offlineApply:";
    String HANPUP_NOTIFY_CACHE_PREFIX = "riskcontrol:hangupNotify:";
    String API_BILL_PREFIX = "bill:";
}
