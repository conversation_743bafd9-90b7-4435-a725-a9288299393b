package com.zhixianghui.common.statics.enums.agent;

import com.zhixianghui.common.statics.enums.tradesync.OrderSyncStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/2 10:46
 **/
@AllArgsConstructor
@Getter
@ToString
public enum AgentStatusEnum {
    /**
     * 已激活
     */
    ACTIVE(100, "已激活"),
    /**
     *已冻结
     */
    INACTIVE(101, "已冻结"),
    /**
     *已创建
     */
    CREATE(102, "已创建"),
    /**
     *已清退
     */
    RETREAT(103, "已清退"),
    ;
    /**
     * 枚举值
     */
    private final int value;

    /**
     * 描述
     */
    private final String desc;

    public static AgentStatusEnum getEnum(int value) {
        return Arrays.stream(values())
                .filter(p -> p.value == value)
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(int value) {
        return getEnum(value).getDesc();
    }
}
