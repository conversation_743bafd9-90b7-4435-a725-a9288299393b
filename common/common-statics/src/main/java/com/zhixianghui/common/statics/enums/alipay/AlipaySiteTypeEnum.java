package com.zhixianghui.common.statics.enums.alipay;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName AlipayServiceTypeEnum
 * @Description TODO
 * @Date 2023/5/8 18:05
 */
@AllArgsConstructor
@Getter
public enum AlipaySiteTypeEnum {

    MINI_APPS("1","小程序支付","06"),

    APP("2","app支付","02"),

    WAP_PAY("3","wap支付","01"),

    PC_PAY("4","电脑支付","01");

    private String value;

    private String desc;

    private String siteType;

    public static AlipaySiteTypeEnum getEnum(String value) {
        return Arrays.stream(values()).filter(p -> p.value.equals(value)).findFirst().orElse(null);
    }

}
