package com.zhixianghui.common.statics.enums.merchant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 商户协议模板时间选项
 * @date 2020-09-09 17:57
 **/
@AllArgsConstructor
@Getter
@ToString
public enum AgreementTempTimeTypeEnum {
    /**
     *无
     */
    NULL(100, "无"),
    /**
     *一月
     */
    ONE_MONTH(101, "自发起日期一个月内"),
    /**
     *一年
     */
    ONE_YEAR(102, "一年"),
    ;

    private final int value;
    private final String desc;
}
