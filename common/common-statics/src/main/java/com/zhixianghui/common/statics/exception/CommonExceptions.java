package com.zhixianghui.common.statics.exception;

/**
 * Author: Cmf
 * Date: 2020.2.12
 * Time: 18:14
 * Description: 此处异常的对外错误码映射到ApiExceptions
 */
public class CommonExceptions {

    public static final BizException PARAM_INVALID = new BizException(100001,ApiExceptions.API_PARAM_FAIL.getApiErrorCode(),false);

    public static final BizException BIZ_INVALID = new BizException(100002, ApiExceptions.API_BIZ_FAIL.getApiErrorCode(),false);

    public static final BizException UNEXPECT_ERROR = new BizException(100003,ApiExceptions.API_COMMON_ERROR.getApiErrorCode(), false);

    public static final BizException DB_AFFECT_ROW_NOT_MATCH = new BizException(100004, ApiExceptions.API_BIZ_FAIL.getApiErrorCode(),false);

    public static final BizException COMMON_RETRY_ERROR = new BizException(100005, ApiExceptions.API_COMMON_ERROR.getApiErrorCode(),true);

    public static final BizException GET_LOCK_ERROR = new BizException(100006,ApiExceptions.API_COMMON_ERROR.getApiErrorCode(), false);

}
