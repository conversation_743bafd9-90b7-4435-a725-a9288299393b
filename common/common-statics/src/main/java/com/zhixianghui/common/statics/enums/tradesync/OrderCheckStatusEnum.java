package com.zhixianghui.common.statics.enums.tradesync;

import java.util.Arrays;

/**
 * Author: Cmf
 * Date: 2020.3.14
 * Time: 20:43
 * Description:订单核实状态
 */
public enum OrderCheckStatusEnum {
    UN_CHECKED(1, "未核实"),
    CHECKED(2, "已核实"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    OrderCheckStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static OrderCheckStatusEnum getEnum(int value) {
        return Arrays.stream(values())
                .filter(p -> p.value == value)
                .findFirst()
                .orElse(null);
    }
}
