package com.zhixianghui.common.statics.constants.redis;

/**
 * <AUTHOR>
 * @ClassName RedisLua
 * @Description TODO
 * @Date 2021/8/6 15:06
 */
public class RedisLua {

    public static final String GET_SORTED_SET =
            "local resultArray = redis.call('zrange',KEYS[1],ARGV[1],ARGV[2], 'WITHSCORES'); \n"+
            "if #resultArray > 0 then \n"+
                    "if resultArray[2] > ARGV[3] then \n"+
                    "return {} \n"+
                    "else \n" +
                    "return redis.call('zrangebyscore',KEYS[1],resultArray[2],ARGV[3]); \n"+
                    "end \n"+
            "else \n"+
            "return {} \n"+
            "end";

    public static final String DYNAMIC_MESSAGE_QUEUE =
            "local resultArray = redis.call('LPUSH',KEYS[1],ARGV[1]); \n" +
            "if resultArray > 1 then \n" +
                    "if resultArray > 10 then \n" +
                        "redis.call('RPOP',KEYS[1]); \n" +
                    "end \n"+
            "else \n" +
                    "redis.call('EXPIRE',KEYS[1],ARGV[2]); \n" +
            "end";

    public static final String PERSONAL_INCOME_TAX =
            "redis.call('del',KEYS[1]);\n"+
                    "for i=1,#ARGV,2 do\n" +
                    "redis.call('zadd',KEYS[1],ARGV[i],ARGV[i+1])\n" +
                    "end;";

    public static final String WITHDRAW_ALL_SCRIPT =
            "redis.call('hset',KEYS[1],ARGV[1],ARGV[2]);\n" +
            "return redis.call('DECR',KEYS[2]);";

}
