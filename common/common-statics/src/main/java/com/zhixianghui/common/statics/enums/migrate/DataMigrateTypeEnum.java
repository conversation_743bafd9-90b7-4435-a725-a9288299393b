package com.zhixianghui.common.statics.enums.migrate;

public enum DataMigrateTypeEnum {

    ACCOUNT_INVOICE_PROCESS_DETAIL(1, "发票账户账务处理明细"),
    ACCOUNT_INVOICE_PROCESS_PENDING(2, "发票账户待账务处理"),
    ACCOUNT_INVOICE_PROCESS_RESULT(3, "发票账户账务处理结果"),
    ACCOUNT_INVOICE_COMMON_UNIQUE(4, "发票账户账务唯一约束"),

    ;

    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    private DataMigrateTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static DataMigrateTypeEnum getEnum(String name) {
        DataMigrateTypeEnum[] values = DataMigrateTypeEnum.values();
        for (int i = 0; i < values.length; i++) {
            if (values[i].name().equals(name)) {
                return values[i];
            }
        }
        return null;
    }
}
