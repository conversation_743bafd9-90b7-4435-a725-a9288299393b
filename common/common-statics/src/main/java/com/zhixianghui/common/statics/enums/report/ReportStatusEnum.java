package com.zhixianghui.common.statics.enums.report;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 报备状态
 * @date 2020-10-15 10:39
 **/
@AllArgsConstructor
@Getter
@ToString
public enum ReportStatusEnum {
    /**
     *报备成功
     */
    SUCCESS(100, "报备成功"),
    /**
     *报备失败
     */
    FAIL(101,"报备失败"),
    /**
     *提交报备(仅提交记录 未请求报备接口)
     */
    COMMIT(102,"提交报备(仅提交记录)"),
    /**
     *通道受理成功
     */
    PROCESSING(103,"通道受理成功"),
    /**
     * 支付宝签约接口，签约中
     */
    CONTRACT(104,"签约中"),
    /**
     * 生成记账本中
     */
    CASHBOOK(105,"生成记账本中"),
    ;

    private final int value;
    private final String desc;
}
