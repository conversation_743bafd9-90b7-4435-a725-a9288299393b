package com.zhixianghui.common.statics.enums.riskcontrol;

import lombok.Getter;

/**
 * @description: 用于原子计算的vo
 * @author: xingguang li
 * @created: 2020/11/20 16:01
 */
@Getter
public enum PendingOrderOpeEnum {

    PASS(100, "通过"),

    REJECT(101, "拒绝"),

    PENDING(102, "待处理"),

    PROCESSED(103, "已处理"),

    ;

    private int value;

    private String desc;

    PendingOrderOpeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
