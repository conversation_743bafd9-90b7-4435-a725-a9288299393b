package com.zhixianghui.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 提供收入明细方式枚举
 * <AUTHOR>
 * @date 2020-08-05
 */
public enum ProvideIncomeDetailTypeEnum {

    PROVIDE(100, "是"),
    UN_PROVIDE(101, "否"),
    RANDOM_INSPECTION(102, "可接受抽查"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    ProvideIncomeDetailTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static ProvideIncomeDetailTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}