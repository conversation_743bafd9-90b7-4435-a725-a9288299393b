package com.zhixianghui.common.statics.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年04月02日 15:23:00
 */
@Getter
public class WxRepBizException extends BizException {

    private static final long serialVersionUID = -3572767306488814330L;

    private final String errorMsg;

    private final boolean isRetry;


    public WxRepBizException(String errorMsg, boolean isRetry) {
        super(100002, ApiExceptions.API_BIZ_FAIL.getApiErrorCode(),errorMsg,false);
        this.errorMsg=errorMsg;
        this.isRetry=isRetry;
    }

}