package com.zhixianghui.common.statics.exception;

/**
 * 通道请求异常类
 */
public class BanklinkExceptions {
    public static final BizException REQUEST_EXCEPTION = new BizException(101001, false);
    public static final BizException VERIFY_SIGN_FAIL = new BizException(101002, false);
    public static final BizException ORDER_NOT_EXSIT = new BizException(101003, false);
    public static final BizException ORDER_NO_REPEAT = new BizException(101004, false);
    public static final BizException ORDER_REFUND = new BizException(101007,false);
}
