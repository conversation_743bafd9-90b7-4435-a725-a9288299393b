package com.zhixianghui.common.statics.enums.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/15 11:19
 **/
@AllArgsConstructor
@Getter
@ToString
public enum SuccessFailCodeEnum {
    /**
     * 成功
     */
    SUCCESS(100, "成功"),
    /**
     * 失败
     */FAIL(101, "失败"),
    ;

    private final int value;
    private final String desc;
}
