package com.zhixianghui.common.statics.constants.common;

/**
 * Author: Cmf
 * Date: 2020.3.24
 * Time: 15:37
 * Description: 模块编码（每一个业务模块定义一个不同的编码，该编码由三位数字组成）
 * 该业务模块抛出的特定异常，以及该业务模块生成的流水号，使用该编码作为前缀
 */
public class ModuleCode {
    public static final String COMMON = "100";           //公用
    public static final String API = "110";              //对外API

    public static final String SEQUENCE = "210";         //编号生成
    public static final String QPS = "220";               //QPS

    public static final String TRADE_SYNC = "410";       //订单同步业务

    public static final String ACCOUNT_TRANSIT = "510";      //在途资金户账务

}
