package com.zhixianghui.common.statics.entity;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于各种费用计算，如销售 计费 成本等
 * <AUTHOR>
 * @date 2020/11/16
 **/
@Data
public class BaseCalculateEntity extends BaseEntity {
    /**
     * 公式类型
     * @see com.zhixianghui.common.statics.enums.fee.FormulaEnum
     */
    private Integer formulaType;

    /**
     * 固定金额手续费
     */
    private BigDecimal fixedFee;

    /**
     * 手续费比例
     */
    private BigDecimal feeRate;

    /**
     * 最低手续费
     */
    private BigDecimal minFee;

    /**
     * 最高手续费
     */
    private BigDecimal maxFee;

    /**
     * 计费类型
     * @see com.zhixianghui.common.statics.enums.fee.ChargeTypeEnum
     */
    private Integer chargeType;

    /**
     * 规则类型
     * @see com.zhixianghui.common.statics.enums.fee.RuleTypeEnum
     */
    private Integer ruleType;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 数据库不存在
     * 对应字段：ruleParam
     * 通过操作该字段操作ruleParam
     */
    private List<SpecialRuleDto> specialFeeRuleList = new ArrayList<>();




}
