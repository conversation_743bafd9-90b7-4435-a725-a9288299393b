package com.zhixianghui.common.statics.enums.common;

import java.util.Arrays;

/**
 * 参数比较类型
 */
public enum CompareTypeEnum {
    EQUAL(1, "等于"),
    LESS(2, "小于"),
    GREATER(3, "大于"),
    EQUAL_OR_LESS(4, "小于等于"),
    EQUAL_OR_GREATER(5, "大于等于")
    ;

    /**
     * 枚举值
     */
    private int value;
    /**
     * 描述
     */
    private String msg;

    public int getValue() {
        return value;
    }


    public String getMsg() {
        return msg;
    }


    CompareTypeEnum(int value, String desc) {
        this.value = value;
        this.msg = desc;
    }

    public static CompareTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

    public static String getMsg(int value) {
        return getEnum(value).getMsg();
    }
}
