package com.zhixianghui.common.statics.exception;

/**
 * Author: Cmf
 * Date: 2020.3.9
 * Time: 10:11
 * Description:
 */
public class ApiExceptions {
    public static BizException API_PARAM_NULL = new BizException(101000, "E110001", "参数缺失异常", false);
    public static BizException API_PARAM_FAIL = new BizException(101001, "E110002", "参数校验异常", false);
    public static BizException API_BIZ_FAIL = new BizException(101002, "E110003", "业务校验异常", false);
    public static BizException API_COMMON_ERROR = new BizException(101999, "E110999", "系统异常", false);

    public static BizException API_TRADE_AUTH_FAIL  = new BizException(101003, "E110004", "鉴权失败", false);
    public static BizException API_TRADE_ORDER_REPEAT  = new BizException(101004, "E110005", "订单号重复，已存在该订单号", false);
    public static BizException API_TRADE_RISK_HANG  = new BizException(101005, "E110006", "风控挂单", false);
    public static BizException API_TRADE_RISK_REJECT  = new BizException(101006, "E110007", "风控拒绝", false);
    public static BizException API_TRADE_CHANNEL_GRANT_FAIL  = new BizException(101007, "E110008", "通道发放失败", false);
    public static BizException API_TRADE_CARD_BIN_FAIL  = new BizException(101008, "E110009", "卡Bin获取失败", false);
    public static BizException API_TRADE_PRODUCT_NOT_OPENED_ERR  = new BizException(101009, "E110010", "产品未开通", false);

    public static BizException API_TRADE_MERCHANT_NOT_EXIST = new BizException(101010, "E110011", "商户不存在", false);
    public static BizException API_TRADE_MERCHANT_STATUS_FAIL = new BizException(101011, "E110012", "商户未激活", false);
    public static BizException API_TRADE_MAINSTAY_NOT_EXIST = new BizException(101012, "E110013", "代征主体不存在", false);
    public static BizException API_TRADE_MAINSTAY_STATUS_FAIL = new BizException(101013, "E110014", "代征主体未激活", false);
    public static BizException API_TRADE_CHANNEL_ERROR = new BizException(101014, "E110015", "内部通道配置有误", false);
    public static BizException API_TRADE_POSITION_NOT_EXIST = new BizException(101015, "E110016", "商户岗位信息不存在", false);
    public static BizException API_TRADE_ORDER_NOT_EXIST = new BizException(101016, "E110017", "订单明细不存在", false);
    public static BizException API_TRADE_QUERY_BALANCE_ERROR = new BizException(101017, "E110018", "通道查询余额异常", false);

    public static BizException API_SIGN_AUTH_FAIL = new BizException(101018, "E110019", "签约信息鉴权失败", false);
    public static BizException API_SIGN_CHANNEL_FAIL = new BizException(101019, "E110020", "签约渠道请求失败", false);
    public static BizException API_SIGN_FAIL = new BizException(101020, "E110021", "签约失败", false);
    public static BizException API_SIGN_TEMPLATE_FAIL = new BizException(101021, "E110022", "签约模板配置异常", false);
    public static BizException API_SIGN_RECORD_NOT_EXIST = new BizException(101022, "E110023", "签约记录不存在", false);
    public static BizException API_EMPTY_TEMPLATE = new BizException(101023, "101023", "模板不存在", false);
    public static BizException API_REQUIRED_PHOTO = new BizException(101024, "101024", "缺少身份证照片", false);
    public static BizException API_LIMIT_PHOTO = new BizException(101025, "101024", "身份证照片不能超过1M", false);

    public static BizException API_TRADE_AUTH_EXCEPTION  = new BizException(101026, "E110026", "鉴权失败", true);

    public static BizException API_TRADE_EMPLOYER_MAINSTAY_STATE_ERROR  = new BizException(101027, "E110027", "代征关系未激活,请联系客服", false);

    public static BizException API_NONE_IDENTITY_INFO  = new BizException(101028, "E110028", "身份信息不存在", false);
    public static BizException API_INVOICE_ACCOUNT_NOT_EXIST = new BizException(101029,"E110029","不存在激活的开票账户",false);
    public static BizException API_BILL_ACCEPT_ERROR = new BizException(101030,"E110030","电子回单下载失败",false);
    public static BizException API_AUDIT_FAIL_ERROR = new BizException(101031,"E110031","审核失败",false);

    public static BizException API_INVOICE_INFO_NOT_EXIST = new BizException(101032,"E110032","开票信息不存在",false);
    public static BizException API_INVOICE_EXPRESSINFO_NOT_EXIST = new BizException(101033,"E110033","快递信息不存在",false);
    public static BizException API_INVOICE_APPLYRESULT_EMPTY = new BizException(101035,"E110035","成功申请开票记录数为0",false);
}
