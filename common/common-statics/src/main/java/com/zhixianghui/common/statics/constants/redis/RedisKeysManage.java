package com.zhixianghui.common.statics.constants.redis;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月04日 10:09:00
 */
public class RedisKeysManage {

    public static String getJobImportExcelLockKey(Long keyName) {
        return RedisKeysConstant.JOB_IMPORT_EXCEL_LOCK_KEY + keyName;
    }

    public static String getUserInfoAuthKey(String keyName) {
        return RedisKeysConstant.USER_INFO_AUTH + keyName;
    }

    public static String getFeeOrderFileRetry(String keyName) {
        return RedisKeysConstant.FEE_ORDER_FILE_RETRY + keyName;
    }

    public static String getAuthInfoKey(String keyName) {
        return RedisKeysConstant.AUTH_INFO + keyName;
    }

    public static String getSignInfoKey(String keyName) {
        return RedisKeysConstant.SIGN_INFO + keyName;
    }

    public static String getEsignLockKey(String keyName) {
        return RedisKeysConstant.ESIGN_MCH_LOCK + keyName;
    }
}