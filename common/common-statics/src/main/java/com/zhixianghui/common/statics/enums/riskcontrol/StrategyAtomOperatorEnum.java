package com.zhixianghui.common.statics.enums.riskcontrol;

import lombok.Getter;

/**
 * 策略原子操作符枚举
 *
 * <AUTHOR> <PERSON>
 */
@Getter
public enum StrategyAtomOperatorEnum {

    GREATER_THAN(0, "大于", ">"),

    LESS_THAN(1, "小于", "<"),

    EQUAL(2, "等于", "=="),

    NOT_EQUAL(3, "不等于", "!="),

    GREATER_THAN_OR_EQUAL(4, "大于等于", ">="),

    LESS_THAN_OR_EQUAL(5, "小于等于", "<="),

    CONTAINS(6,"包含（打款备注用）",""),

    TRUE(7,"已完成","true"),

    FALSE(8,"未完成","false"),

    UN_FULL(9,"要素不齐全","false"),
    ;

    private int value;

    private String desc;

    private String operater;

    StrategyAtomOperatorEnum(int value, String desc, String operater) {
        this.value = value;
        this.desc = desc;
        this.operater = operater;
    }
}
