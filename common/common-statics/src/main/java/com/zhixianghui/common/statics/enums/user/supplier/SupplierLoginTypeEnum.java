package com.zhixianghui.common.statics.enums.user.supplier;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 供应商后台登录类型枚举
 * @date 2020-12-10 16:47
 **/
@Getter
@ToString
@AllArgsConstructor
public enum SupplierLoginTypeEnum {
    /**
     * 密码登录
     */
    PWD("密码登录", 0),

    /**
     * 短信登录
     */
    SMS("验证码登录", 1),
    ;

    private final String desc;
    private final int value;
}
