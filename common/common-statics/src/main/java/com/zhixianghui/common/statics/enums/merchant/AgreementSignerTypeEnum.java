package com.zhixianghui.common.statics.enums.merchant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 模板签署人类型
 * @date 2020-09-04 15:03
 **/
@AllArgsConstructor
@Getter
@ToString
public enum AgreementSignerTypeEnum {
    /**
     *甲方
     */
    FIRST_PARTY(100, "甲方"),
    /**
     *乙方
     */
    SECOND_PARTY(101, "乙方"),
    ;

    private final int value;
    private final String desc;
}
