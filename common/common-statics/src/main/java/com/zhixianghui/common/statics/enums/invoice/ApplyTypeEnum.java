package com.zhixianghui.common.statics.enums.invoice;

import java.util.Arrays;

/**
 * 开票申请方式
 * <AUTHOR>
 */
public enum ApplyTypeEnum {
    GRANT_AMOUNT("按发放金额开票",1),
    RECHARGE_AMOUNT("按充值金额开票",2)
    ;

    private String desc;
    private int value;

    ApplyTypeEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static ApplyTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
