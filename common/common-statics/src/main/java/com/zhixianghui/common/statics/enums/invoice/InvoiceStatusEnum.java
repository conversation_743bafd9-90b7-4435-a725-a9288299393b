package com.zhixianghui.common.statics.enums.invoice;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @title: InvoiceTypeEnum
 * @description: 发票类型枚举
 * @date 2019/10/1111:20 AM
 */
public enum InvoiceStatusEnum {
    WAIT_ISSUE("待开具",1),
    WAIT_SEND("待寄出",2),
    SENDED("已寄出",3),
    EXCEPTION("其他异常",4),
    ;

    private String desc;
    private int value;

    InvoiceStatusEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static InvoiceStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
