package com.zhixianghui.common.statics.enums.report;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 支付通道类型
 * @date 2020-09-29 14:55
 **/
@AllArgsConstructor
@Getter
@ToString
public enum ChannelTypeEnum {
    /**
     *银行卡
     */
    BANK(1, "银行卡"),
    /**
     *支付宝
     */
    ALIPAY(2, "支付宝"),
    /**
     *微信
     */
    WENXIN(3, "微信"),
    ;

    private final int value;
    private final String desc;

    public static ChannelTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

    public static String getDesc(int value) {
        return getEnum(value).getDesc();
    }
}