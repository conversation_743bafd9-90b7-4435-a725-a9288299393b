package com.zhixianghui.common.statics.enums.merchant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 商户协议状态枚举
 * @date 2020-09-03 16:23
 **/
@AllArgsConstructor
@Getter
@ToString
public enum AgreementStatusEnum {
    /**
     *已完成
     */
    FINISHED(100, "2","已完成"),
    /**
     *已取消
     */
    CANCELED(101, "3","已取消"),
    /**
     *已过期
     */
    EXPIRED(102, "5","已过期"),
    /**
     *待处理
     */
    SIGNING(103,"0" ,"签署中"),

    REFUSE(104,"7","已拒签"),

    END(105,"-1","已终止"),

    CREATING(106,"106","创建中");
    ;

    private final int value;
    private final String signResult;
    private final String desc;

    public static AgreementStatusEnum getEnum(String signResult) {
        return Arrays.stream(values()).filter(p -> p.signResult.equals(signResult)).findFirst().orElse(null);
    }
}
