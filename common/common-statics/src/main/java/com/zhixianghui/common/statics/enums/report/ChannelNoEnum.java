package com.zhixianghui.common.statics.enums.report;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 通道枚举
 * @date 2020-10-14 16:09
 **/
@AllArgsConstructor
@Getter
@ToString
public enum ChannelNoEnum {
    //运营后台的通道编号需与枚举名一致
    /**
     *汇聚支付
     */
    JOINPAY("汇聚支付"),
    ALIPAY("支付宝支付"),
    YISHUI("易税"),
    WXPAY("微信支付"),
    CMB("招商银行"),
    WXGATHERING("微信收款"),
    OUT_SYNC("外部支付"),
    JOINPAY_JXH("汇聚支付-君享汇")
    ;


    private final String desc;

    public static ChannelNoEnum getEnum(String value) {
        return Arrays.stream(values())
                .filter(p -> p.name().equals(value))
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(String value) {
        return getEnum(value).getDesc();
    }
}
