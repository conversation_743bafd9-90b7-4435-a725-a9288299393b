package com.zhixianghui.common.statics.enums.merchant;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 有效期类型
 * <AUTHOR>
 * @date 2020-08-05
 */
public enum ValidityDateTypeEnum {

    PERIOD(1, "区间有效"),
    FOREVER(2, "长期有效"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    ValidityDateTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static ValidityDateTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

    public static String getDesc(int value) {
        return getEnum(value).getDesc();
    }

    public static List<Integer> listValues(){
        return Arrays.stream(values()).map(ValidityDateTypeEnum::getValue).collect(Collectors.toList());
    }
}