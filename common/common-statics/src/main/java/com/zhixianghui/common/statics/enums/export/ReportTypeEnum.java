package com.zhixianghui.common.statics.enums.export;

import java.util.Arrays;

/**
 * 报表类型
 *
 * <AUTHOR>
 * @date 2020/8/28
 **/
public enum ReportTypeEnum {
    //预开发票记录导出
    INVOICE_PRE_EXPORT(68,"预开发票记录导出","invoicePreExport",FileTypeEnum.EXECL.getValue()),
    //运营后台商户计费订单合并汇总导出
    PMS_MERCHANT_MERGE_FEE(67,"商户计费订单合并导出","pmsMerchantMergeFeeExport",FileTypeEnum.EXECL.getValue()),
    //运营后台批量导出完税证明文件
    CMB_CHANGES_FUNDS_EXPORT(66,"招行资金变动记录导出","CmbChangesFundsExport",FileTypeEnum.EXECL.getValue()),
    CMB_INCOME_RECORD_EXPORT(65,"招行来账记录导出","CmbIncomeRecordExport",FileTypeEnum.EXECL.getValue()),
    PMS_TAX_CERTIFICATE(64,"完税证明文件导出","",FileTypeEnum.ZIP.getValue()),
    //运营后台供应商计费订单合并汇总导出
    PMS_SUPPLIER_MERGE_FEE(63,"供应商计费订单合并导出","pmsSupplierMergeFeeExport",FileTypeEnum.EXECL.getValue()),
    //运营后台合伙人计费订单合并汇总导出
    PMS_AGENT_MERGE_FEE(62,"合伙人计费订单合并导出","pmsAgentMergeFeeExport",FileTypeEnum.EXECL.getValue()),
    //运营后台销售计费订单合并汇总导出
    PMS_SALER_MERGE_FEE(61,"销售成本订单合并导出","pmsSalerMergeFeeExport",FileTypeEnum.EXECL.getValue()),
    //商户后台商户余额数据导出
    CK_ACCTDETAIL_EXPORT(60, "收支明细导出", "ckAcctdetailExport",FileTypeEnum.EXECL.getValue()),
    //商户后台商户余额数据导出
    EMPLOYER_BALANCE_EXPORT(59, "商户余额导出", "employerBalanceExportPms",FileTypeEnum.EXECL.getValue()),
    //供应商后台导出商户分析数据
    MERCHANT_STAT_INFO_EXPORT_SUPLIER(58, "商户分析数据导出", "merchantStatInfoExportSuplier", FileTypeEnum.EXECL.getValue()),
    //代开供应商报价导出
    EXPORT_INVOICE_QUOTE(57, "代开供应商报价导出", "InvoiceQuoteExportCommon", FileTypeEnum.EXECL.getValue()),
    //商户后台错误订单导出
    MERCHANT_OFFLINE_FAIL_ORDER_EXPORT(56,"导入失败详情","MerchantOfflineFailOrderEnum",FileTypeEnum.EXECL.getValue()),
    //商户后台导出外部订单
    MERCHANT_OFFLINE_EXPORT(55,"外部订单导出","MerchantOfflineExportEnum",FileTypeEnum.EXECL.getValue()),
    //供应商后台导出外部订单
    MAINSTAY_OFFLINE_EXPORT(54,"外部订单导出","MainstayOfflineExportEnum",FileTypeEnum.EXECL.getValue()),
    //运营后台导出外部订单
    PMS_OFFLINE_EXPORT(53,"外部订单导出","PmsOfflineExportEnum",FileTypeEnum.EXECL.getValue()),
    //商户后台导出企业电子回单列表
    MERCHANT_BUSINESS_RECEIPT_ORDER_EXPORT(52,"企业转账回单","BusinessReceiptOrderExportEnum",FileTypeEnum.PARAM.getValue()),
    //商户后台导出个人电子回单列表
    MERCHANT_PERSONNAL_RECEIPT_ORDER_EXPORT(51,"个人转账回单","PersonalReceiptOrderExportEnum",FileTypeEnum.PARAM.getValue()),
    //运营后台导出已处理挂单列表
    RISK_HANDLED_ORDER_EXPORT(50,"已处理挂单导出","RiskHandledOrderExportEnum",FileTypeEnum.EXECL.getValue()),
    //供应商后台导出协议
    AGREEMENT_SUPPLIER_EXPORT(49,"协议导出","",FileTypeEnum.PARAM.getValue()),
    //商户后台导出协议
    AGREEMENT_PORTAL_EXPORT(48,"协议导出","",FileTypeEnum.PARAM.getValue()),
    //运营后台导出协议
    AGREEMENT_PMS_EXPORT(47,"协议导出","agreementExportEnum",FileTypeEnum.PARAM.getValue()),
    //商户后台供应商后台开票导出
    MERCHANT_INVOICE_RECORD_INFO_EXPORT(46, "开票申请记录导出", "invoiceRecordInfoExport", FileTypeEnum.EXECL.getValue()),
    //供应商后台工单流程导出
    WORK_SUP_FLOW_TODO_EXPORT(44,"待处理工单","WorkSupplierOrderFlowEnum",FileTypeEnum.PARAM.getValue()),
    WORK_SUP_FLOW_HANDLE_EXPORT(45,"收到工单","WorkSupplierOrderFlowEnum",FileTypeEnum.PARAM.getValue()),
    //工单流程导出
    WORK_FLOW_TODO_EXPORT(41,"待处理工单","WorkOrderFlowEnum",FileTypeEnum.PARAM.getValue()),
    WORK_FLOW_HANDLE_EXPORT(42,"收到工单","WorkOrderFlowEnum",FileTypeEnum.PARAM.getValue()),
    WORK_FLOW_CARBON_EXPORT(43,"抄送工单","WorkOrderFlowEnum",FileTypeEnum.PARAM.getValue()),
    //账单订单明细导出
    FEE_ORDER_EXPORT(40, "账单订单明细", "feeOrderExport", FileTypeEnum.EXECL.getValue()),
    /**
     * 添加枚举时, 请遵守按value降序排序规则
     */
    //发票账务导出
    AGENT_EXPORT(39, "合伙人列表", "agentListExport", FileTypeEnum.EXECL.getValue()),
    EMPLOYER_MAINSTAY_RELATION_EXPORT(38, "代征关系", "employerMainstayRelationExport", FileTypeEnum.EXECL.getValue()),
    INVOICE_ACCOUNT_EXPORT(37, "发票账户", "invoiceAccountExportEnum", FileTypeEnum.EXECL.getValue()),
	WX_INCOME_EXPORT_ENUM(36,"微信来账通知","wxIncomeExportEnum",FileTypeEnum.EXECL.getValue()),
    DOWNLOAD_ID_CARD(35, "下载身份证", "downloadIdCard", FileTypeEnum.ZIP.getValue()),
    MERCHANT_INVOICE_RECORD_EXPORT(34, "开票申请记录导出", "invoiceRecordExport", FileTypeEnum.EXECL.getValue()),
    MERCHANT_STAT_INFO_EXPORT(33, "商户分析数据导出", "merchantStatInfoExport", FileTypeEnum.EXECL.getValue()),
    FREELANCE_STAT_Export(32, "自由职业者分析数据导出", "freelanceStatExport", FileTypeEnum.EXECL.getValue()),

    // 供应商后台商户信息详情导出
    MAINSTAY_MERCHANT_LIST(31,"商户信息列表","MainstayEmployerInfo",FileTypeEnum.EXECL.getValue()),
    MAINSTAY_MERCHANT_EXPORT(30,"商户信息","MainstayEmployerInfo",FileTypeEnum.EXECL.getValue()),
    // 运营后台商户信息列表导出
    MERCHANT_LIST_INFO_EXPORT(29,"商户信息列表","ExportEmployerInfo",FileTypeEnum.EXECL.getValue()),
    /**
     * 运营后台充值记录导出
     */
    DATA_DICTIONARY(28, "数据字典详情", "DataDictionary", FileTypeEnum.EXECL.getValue()),
    ORDER_ITEM_FAIL(27,"导入失败详情","OrderItemFail",FileTypeEnum.EXECL.getValue()),
    WITHDRAW_RECORD_SUPL(26,"提现记录信息","WithdrawExportSupl",FileTypeEnum.EXECL.getValue()),
    WITHDRAW_RECORD_EMP(25,"提现记录信息","WithdrawExportEmp",FileTypeEnum.EXECL.getValue()),
    WITHDRAW_RECORD_PMS(24,"提现记录信息","WithdrawExportPms",FileTypeEnum.EXECL.getValue()),
    TRADE_RECHARGE_RECORD_PMS(23, "充值记录信息", "RechargeRecordExportPms", FileTypeEnum.PARAM.getValue()),
    /**
     * 各个后台功能导出
     */
    AGENT_FUNCTION(22, "合伙人后台功能", "AgentFunction", FileTypeEnum.EXECL.getValue()),
    SUPPLIER_FUNCTION(21, "供应商后台功能", "SupplierFunction", FileTypeEnum.EXECL.getValue()),
    MERCHANT_FUNCTION(20, "商户后台功能", "MerchantFunction", FileTypeEnum.EXECL.getValue()),
    PMS_FUNCTION(19, "运营后台功能", "PmsFunction", FileTypeEnum.EXECL.getValue()),
    /**
     * 运营后台合伙人月账单信息导出
     */
    AGENT_MONTH_BILL_AGENT(18, "合伙人后台-合伙人月账单", "AgentMonthBillInfoAgent", FileTypeEnum.EXECL.getValue()),
    AGENT_MONTH_BILL_PMS(17, "运营后台-合伙人月账单", "AgentMonthBillInfoPms", FileTypeEnum.EXECL.getValue()),
    /**
     * 合伙人分润订单导出
     */
    AGENT_FEE_ORDER(16, "合伙人分润订单", "AgentFeeOrderExport", FileTypeEnum.EXECL.getValue()),
    /**
     * 合伙人信息导出
     */
    AGENT_DETAIL(15, "合伙人信息", "AgentDetailExport", FileTypeEnum.EXECL.getValue()),
    /**
     * 凭证文件导出
     */
    PAY_CERTIFICATE(14, "支付凭证", "", FileTypeEnum.ZIP.getValue()),
    /**
     * 供应商后台后台订单明细导出
     */
    SIGN_RECORD_SUP(13, "签约记录", "SignRecordExportSup", FileTypeEnum.EXECL.getValue()),
    /**
     * 商户后台后台订单明细导出
     */
    SIGN_RECORD_MER(12, "签约记录", "SignRecordExportMer", FileTypeEnum.EXECL.getValue()),
    /**
     * 运营后台后台订单明细导出
     */
    SIGN_RECORD_PMS(11, "签约记录", "SignRecordExportPms", FileTypeEnum.EXECL.getValue()),
    /**
     * 供应商后台订单明细导出
     */
    TRADE_ORDER_ITEM_SUP(10, "订单明细", "TradeOrderItemExportSup", FileTypeEnum.EXECL.getValue()),
    /**
     * 商户后台挂单订单明细导出
     */
    TRADE_HANG_ORDER_MER(9, "挂单订单明细", "TradeHangOrderExportMer", FileTypeEnum.EXECL.getValue()),
    /**
     * 供应商成本订单导出-运营后台/商户后台共用
     */
    VENDOR_FEE_ORDER(8, "供应商成本订单", "VendorFeeOrderExport", FileTypeEnum.EXECL.getValue()),
    /**
     * 商户计费订单导出-运营后台/商户后台共用
     */
    MERCHANT_FEE_ORDER(7, "商户计费订单", "MerchantFeeOrderExport", FileTypeEnum.EXECL.getValue()),
    /**
     * 销售计费订单导出-运营后台/商户后台共用
     */
    SALES_FEE_ORDER(6, "销售计费订单", "SalesFeeOrderExport", FileTypeEnum.EXECL.getValue()),
    /**
     * 运营后台打款流水导出
     */
    TRADE_RECORD_ITEM_PMS(5, "打款流水信息", "TradeRecordItemExportPms", FileTypeEnum.EXECL.getValue()),
    /**
     * 运营后台订单明细导出
     */
    TRADE_ORDER_ITEM_PMS(4, "订单明细", "TradeOrderItemExportPms", FileTypeEnum.EXECL.getValue()),
    /**
     * 商户后台订单明细导出
     */
    TRADE_ORDER_ITEM_MER(3, "订单明细", "TradeOrderItemExportMer", FileTypeEnum.EXECL.getValue()),
    /**
     * 后台协议归档导出
     */
    AGREEMENT_ARCHIVE_FILE(2, "协议归档信息", "AgreementArchiveFile", FileTypeEnum.ZIP.getValue()),
    // 运营后台商户信息导出
    MERCHANT_INFO_EXPORT(1, "商户信息", "ExportEmployerInfo", FileTypeEnum.EXECL.getValue()),

    IMPORT_INVOICE_QUOTE(-1, "代开供应商报价导入", "InvoiceQuoteImportCommon", FileTypeEnum.EXECL.getValue()),
    ;

    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String fileName;

    /**
     * 对应数据字典名
     */
    private String dataName;

    /**
     * 文件类型
     */
    private int fileType;

    ReportTypeEnum(int value, String fileName, String dataName,int fileType) {
        this.value = value;
        this.fileName = fileName;
        this.fileType = fileType;
        this.dataName = dataName;
    }

    public static ReportTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

    public String getFileName() {
        return fileName;
    }

    public int getValue() {
        return value;
    }

    public int getFileType() {
        return fileType;
    }

    public String getDataName() {
        return dataName;
    }
}
