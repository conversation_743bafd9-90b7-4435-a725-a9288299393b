package com.zhixianghui.common.statics.result;

import java.io.Serializable;

/**
 * Author: Cmf
 * Date: 2019/10/23
 * Time: 19:15
 * Description:
 */
public class RestResult<T> implements Serializable {
    private static final long serialVersionUID = -4110380189299470161L;
    public static final int SUCCESS = 20000;
    public static final int COMMON_ERROR = 20001;
    public static final int PERMISSION_DENY = 20002;
    public static final int INVALID_TOKEN = 20003;
    public static final int NO_SELECT_MCH = 20004;  // 商户后台，没有选择商户

    private T data;
    private int code;
    /**
     * message只用于存在错误信息，处理成功的提示信息请放在data中
     */
    private String message;

    private RestResult(T data, int code, String message) {
        this.data = data;
        this.code = code;
        this.message = message;
    }

    public static <T> RestResult<T> deny(String message) {
        return new RestResult<>(null, PERMISSION_DENY, message);
    }

    public static <T> RestResult<T> error(String message) {
        return new RestResult<>(null, COMMON_ERROR, message);
    }

    public static <T> RestResult<T> error(int code, String message) {
        return new RestResult<>(null, code, message);
    }

    public static <T> RestResult<T> error(T data, String message) {
        return new RestResult<>(data, COMMON_ERROR, message);
    }

    public static <T> RestResult<T> success(T data) {
        return new RestResult<>(data, SUCCESS, "");
    }

    public static RestResult unAuth(String message) {
        return new RestResult<>(null, INVALID_TOKEN, message);
    }

    public static RestResult noSelectMch(String message) {
        return new RestResult<>(null, NO_SELECT_MCH, message);
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
