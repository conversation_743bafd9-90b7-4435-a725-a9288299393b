package com.zhixianghui.common.statics.enums.invoice;

import java.util.*;

/**
 * 发票类型
 * <AUTHOR>
 */
public enum InvoiceTypeEnum {
    NORMAL("增值税普通发票",1),
    MAJOR("增值税专用发票",2),
    YISHUI_NORMAL("易税增值税普通发票", 3),
    YISHUI_MAJOR("易税增值税专用发票",4)
    ;

    private String desc;
    private int value;

    InvoiceTypeEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static InvoiceTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
