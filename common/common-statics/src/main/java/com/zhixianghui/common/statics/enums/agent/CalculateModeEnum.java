package com.zhixianghui.common.statics.enums.agent;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @ClassName CalculateModeEnum
 * @Description TODO
 * @Date 2022/7/29 14:32
 */
@Getter
@AllArgsConstructor
public enum CalculateModeEnum {

    FORMULA(100,"按公式独立计算"),

    PRICE_DIFFERENCE(101,"按差价计算");

    private int value;

    private String desc;
}
