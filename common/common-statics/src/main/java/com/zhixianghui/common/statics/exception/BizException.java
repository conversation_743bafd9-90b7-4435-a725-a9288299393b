package com.zhixianghui.common.statics.exception;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Author: Cmf
 * Date: 2020.2.12
 * Time: 17:50
 * Description:
 */
public class BizException extends RuntimeException {
    private int sysErrorCode;
    private String apiErrorCode;
    private String errMsg;
    private boolean mqRetry;
    private Map<String, String> extraInfo;

    public int getSysErrorCode() {
        return sysErrorCode;
    }

    public String getApiErrorCode() {
        return apiErrorCode;
    }


    public String getErrMsg() {
        return errMsg;
    }

    public boolean isMqRetry() {
        return mqRetry;
    }

    private BizException(int sysErrorCode, String apiErrorCode, String errorMsg, boolean mqRetry, Throwable throwable) {
        super(msg(sysErrorCode, apiErrorCode, errorMsg), throwable);
        this.sysErrorCode = sysErrorCode;
        this.apiErrorCode = apiErrorCode;
        this.errMsg = errorMsg;
        this.mqRetry = mqRetry;
    }


    BizException(int sysErrorCode, boolean mqRetry) {
        this(sysErrorCode, null, null, mqRetry, null);
    }

    BizException(int sysErrorCode, String apiErrorCode, String errorMsg, boolean mqRetry) {
        this(sysErrorCode, apiErrorCode, errorMsg, mqRetry, null);
    }

    BizException(int sysErrorCode, String apiErrorCode, boolean mqRetry) {
        this(sysErrorCode, apiErrorCode, null, mqRetry);
    }


    public BizException newWithErrMsg(String errMsg) {
        return new BizException(this.sysErrorCode, this.apiErrorCode, errMsg, this.mqRetry, this.getCause());
    }

    public BizException newWith(String errMsg, Throwable cause) {
        return new BizException(this.sysErrorCode, this.apiErrorCode, errMsg, this.mqRetry, cause);
    }

    private static String msg(int sysErrorCode, String apiErrorCode, String errorMsg) {
        String str = "sysErrorCode=" + sysErrorCode;
        if (apiErrorCode != null && !Objects.equals(apiErrorCode, "")) {
            str += ",apiErrorCode=" + apiErrorCode;
        }
        if (errorMsg != null && !Objects.equals(errorMsg, "")) {
            str += ",errorMsg=" + errorMsg;
        }
        return str;
    }

    public String getExtraInfo(String key) {
        if (this.extraInfo == null) {
            return null;
        } else {
            return extraInfo.get(key);
        }
    }

    public void addExtraInfo(String key, String value) {
        if (this.extraInfo == null) {
            this.extraInfo = new HashMap<>();
        }
        this.extraInfo.put(key, value);
    }


}
