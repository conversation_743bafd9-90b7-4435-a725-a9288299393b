package com.zhixianghui.common.statics.enums.bankLink.auth;

import java.util.Arrays;

/**
 * 鉴权类型
 *
 * <AUTHOR>
 * @date 2020/8/28
 **/
public enum AuthTypeEnum {
    IDCARD_NAME_BANKCARD(1, "身份证+姓名+银行卡"),
    IDCARD_NAME_PHONE(2, "身份证+姓名+手机号"),
    IDCARD_NAME(3, "身份证+姓名"),
    IDCARD_NAME_BANKCARD_PHONE(4, "身份证+姓名+银行卡+手机号")
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    AuthTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static AuthTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
