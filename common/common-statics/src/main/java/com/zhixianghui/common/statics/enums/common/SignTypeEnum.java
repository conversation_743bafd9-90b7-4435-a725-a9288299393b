package com.zhixianghui.common.statics.enums.common;

import java.util.Arrays;

/**
 * 签名算法类型
 */
public enum SignTypeEnum {
    RSA(1, "RSA"),
    RSA2(2, "RSA2"),

    ;

    /**
     * 枚举值
     */
    private int value;
    /**
     * 描述
     */
    private String msg;

    public int getValue() {
        return value;
    }


    public String getMsg() {
        return msg;
    }

    SignTypeEnum(int value, String desc) {
        this.value = value;
        this.msg = desc;
    }

    public static SignTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
