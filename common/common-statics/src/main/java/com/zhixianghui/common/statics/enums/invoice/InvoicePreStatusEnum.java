package com.zhixianghui.common.statics.enums.invoice;

import java.util.Arrays;


public enum InvoicePreStatusEnum {
    UN_FINNISH("未完成",1),
    FINNISH("已完成",2),
    PROCESSING("处理中",3),
    ;

    private String desc;
    private int value;

    InvoicePreStatusEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static InvoicePreStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
