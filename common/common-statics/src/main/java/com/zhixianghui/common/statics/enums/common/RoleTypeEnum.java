package com.zhixianghui.common.statics.enums.common;



/*
 * <AUTHOR>
 * @Date 2021/4/9 10:20
 */
public enum RoleTypeEnum {
    ADMIN(0, "超管"),
    PRESET(1, "预置"),
    CUSTOMIZE(2, "自定义"),
    PRESET_AGENT_NO(-1, "预置合伙人编号"),
    PRESET_EMPLOYER_NO(-1, "预置用工企业编号"),
    SUPPLIER_EMPLOYER_NO(-1, "预置供应商编号"),
    ;

    int type;
    String name;

    RoleTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
