package com.zhixianghui.common.statics.constants.rmqdest;

public class MessageMsgDest {
    public static final String TOPIC_ZFT_ROYALTY_NOTIFY = "topic-zft-royalty-notify";
    public static final String TAG_ZFT_ROYALTY_NOTIFY = "tag-zft-royalty-notify";
    public static final String TOPIC_ZFT_SETTLE_NOTIFY = "topic-zft-settle-notify";
    public static final String TAG_ZFT_SETTLE_NOTIFY = "tag-zft-settle-notify";

    public static final String TOPIC_ZFT_PAY_RESULT_CALLBACK = "topic_zft_pay_result_callback";
    public static final String TAG_ZFT_PAY_RESULT_CALLBACK = "tag_zft_pay_result_callback";

    public static final String TOPIC_CMB_ACCOUNT_BOOK_BALANCE_CHECK = "topic_cmb_account_book_balance_check";
    /**
     * 充值成功反查余额队列
     */
    public static final String TOPIC_RECHARGE_BALANCE = "topic_recharge_balance";
    public static final String TAG_RECHARGE_BALANCE = "tag_recharge_balance";

    public static final String TOPIC_HANGUP_MERCHANT_NOTIFY = "topic_hangup_merchant_notify";
    public static final String TAG_HANGUP_MERCHANT_NOTIFY = "tag_hangup_merchant_notify";
    public static final String TOPIC_CMB_BILL_TASK = "topic_cmb_bill_task";
    public static final String TOPIC_CMB_BILL_SAVE = "topic_cmb_bill_save";
    public static final String TAG_CMB_BILL_SAVE = "tag_cmb_bill_save";
    public static final String TOPIC_ORDER_DATA_SYNC_CK = "topic_order_data_sync";
    public static final String TAG_ORDER_DATA_SYNC_CK = "tag_order_data_sync";
    // 消息补偿
    public static final String TOPIC_MESSAGE_RECOUP = "topic-message-recoup";
    public static final String TOPIC_MERCHANT_INFO_ANALYZE = "topic-merchant-info-analyze";
    public static final String TOPIC_LAST_MONTH_MERCHANT_INFO_ANALYZE = "topic-last-month-merchant-info-analyze";
    public static final String TOPIC_FREELANCE_INFO_ANALYZE = "topic-freelance-info-analyze";
    public static final String TOPIC_LAST_MONTH_FREELANCE_INFO_ANALYZE = "topic-last-month-freelance-info-analyze";
    public static final String TOPIC_SEND_EMAIL_ASYNC = "topic-email-async";//异步发送email邮件的topic
    public static final String TAG_SEND_EMAIL_ASYNC = "email-async-text";//异步发送email邮件的tag


    public static final String TOPIC_TIMER_INSTANCE_STAGE_CHANGE = "topic-timer-instance-stage-change";//定时任务实例状态变更的topic
    public static final String TAG_TIMER_INSTANCE_CHANGE = "timer-instance-stage-change";//定时任务实例状态变更的tag

    public static final String TOPIC_APPROVAL_ASYNC = "topic-approval-async";//异步发送approval审批消息的topic
    public static final String TAG_APPROVAL_CREATE_MERCHANT_ASYNC = "approval-create-merchant-text";//approval的创建商户tag
    public static final String TAG_APPROVAL_CREATE_MERCHANT_NOTIFY = "tag_approval_create_merchant_notify";//approval的创建商户tag
    public static final String TAG_APPROVAL_MERCHANT_VERIFY_ASYNC = "approval-merchant-verify-text";//approval的主体认证tag
    public static final String TAG_APPROVAL_CANCEL_ASYNC = "approval-cancel-merchant-verify-text";//approval主体认证审批取消tag
    public static final String TAG_APPROVAL_AGENT_CANCEL_ASYNC = "approval-cancel-agent-verify-text";//approval合伙人审批取消tag
    public static final String TAG_APPROVAL_AUTO_AGREE_ASYNC = "approval-auto-agree-text";//approval异步自动同意流程
    public static final String TAG_APPROVAL_CREATE_AGENT_ASYNC = "approval-create-agent-text";//approval的创建合伙人tag
    public static final String TAG_APPROVAL_MODIFY_AGENT_ASYNC = "approval-modify-agent-text";//合伙人信息变更tag
    public static final String TAG_APPROVAL_QUOTE_CANDEL = "tag_approval_quote_cancel";
    public static final String TAG_EDIT_QUOTE_ASYNC = "tag_edit_quote_async";
    public static final String TAG_FLOW_ARRIVE_MAIL = "tag_flow_arrive_mail";

    @Deprecated
    public static final String TOPIC_MERCHANT_FULL_EDIT = "topic-merchant-full-anync";//异步发送商户修改消息

    @Deprecated
    public static final String TAG_MERCHANT_FULL_EDIT = "tag-merchant-full-anync";

    /**
     * 商户名称修改topic,tag
     */
    public static final String TOPIC_MERCHANT_BASE_EDIT = "topic-merchant-base-anync";
    public static final String TAG_MERCHANT_BASE_EDIT = "topic-merchant-base-anync";

    public static final String TOPIC_MAINSTAY_EDIT = "topic-mainstay-edit";
    public static final String TAG_MAINSTAY_EDIT = "tag-mainstay-edit";

    public static final String TOPIC_APPLY_NEW_MERCHANT = "topic_apply_new_merchant";
    public static final String TAG_APPLY_NEW_MERCHANT = "tag_apply_new_merchant";

    public static final String TOPIC_AGENT_BASE_EDIT = "topic-agent-base-edit";
    public static final String TAG_AGENT_BASE_EDIT = "tag-agent-base-edit";

    public static final String TOPIC_SIGN_NOTIFY = "topic-sign-notify";
    public static final String TAG_SIGN_NOTIFY = "tag-sign-notify";


    /**
     * 定时商户协议过期任务
     */
    public static final String AGREEMENT_EXPIRE_TASK = "topic-agreement-expire-task";
    /**
     * 合伙人分佣统计任务
     */
    public static final String AGENT_FEE_SUM_TASK = "topic-agent-fee-sum-expire-task";
    public static final String AGENT_FEE_SUM_LAST_MONTH_TASK = "topic-agent-fee-sum-expire-last-month-task";
    /**
     * 合伙人月账单统计任务
     */
    public static final String AGENT_MONTHLY_BILL_STATISTICS = "topic-agent-monthly-bill-statistics";
    public static final String AGENT_MONTHLY_BILL_GROUP = "agent-monthly-bill-group";

    /**
     * 异步发送报备消息的topic
     */
    public static final String TOPIC_REPORT_ASYNC = "topic-report-async";
    public static final String TAG_REPORT_NOTIFY_ASYNC = "report-notify-text";

    //修改汇聚报备信息tag
    public static final String TAG_REPORT_MODIFY_NOTIFY = "report-modify-notify";

    /**
     * 异步发送交易中心消息的topic
     */
    public static final String TOPIC_TRADE_ASYNC = "topic-trade-async";
    public static final String TAG_TRADE_ACCEPT = "trade-accept-text";
    public static final String TAG_ACCEPT_BATCH_COUNT = "trade-accept-batch-count-text";
    public static final String TAG_ACCEPT_NOT_BIZ_EXCEPTION = "trade-accept-not-biz-exception-text";
    public static final String TAG_TRADE_GRANT = "trade-grant-text";

    public static final String TAG_TRADE_CMB_BATCH_GRANT = "trade-grant-cmb-batch-text";
    public static final String TAG_TRADE_CMB_CHECK_GRANT = "tag_trade_cmb_check_grant";
    public static final String TOPIC_TRADE_CMB_ASYNC = "topic-trade-cmb-async";
    public static final String TAG_GRANT_NOTIFY = "trade-grant-notify-text";
    public static final String TAG_GRANT_BATCH_COUNT = "trade-grant-batch-count-text";
    public static final String TAG_GRANT_NOT_BIZ_EXCEPTION = "trade-grant-not-biz-exception-text";
    public static final String TAG_TRADE_RECHARGE = "tag-trade-recharge";

    /**
     * 创客汇交易中心消息topic
     */
    public static final String TOPIC_TRADE_ASYNC_CKH = "topic-trade-async-ckh";
    public static final String TAG_TRADE_ACCEPT_CKH = "trade-accept-text-ckh";
    public static final String TAG_ACCEPT_NOT_BIZ_EXCEPTION_CKH = "trade-accept-not-biz-exception-text-ckh";
    public static final String TAG_ACCEPT_BATCH_COUNT_CKH = "trade-accept-batch-count-text-ckh";
    public static final String TAG_TRADE_GRANT_CKH = "trade-grant-text-ckh";
    public static final String TAG_GRANT_NOT_BIZ_EXCEPTION_CKH = "trade-grant-not-biz-exception-text-ckh";
    public static final String TAG_GRANT_BATCH_COUNT_CKH = "trade-grant-batch-count-text-ckh";

    /**
     * 一键提现所有商户
     */
    public static final String TOPIC_ALL_MERCHANT_WITHDRAW = "topic-all-merchant-withdraw";

    /**
     * 创客汇账单topic
     */
    public static final String TOPIC_CKH_FEE_ASYNC = "topic-ckh-fee-async";
    public static final String TAG_CKH_FEE_EXCEPTION_RETRY = "tag-fee-exception-retry";
    public static final String TAG_CKH_FEE_BATCH_UPDATE = "tag-fee-batch-update";
    public static final String TAG_CKH_FEE_TRANSFER = "tag-fee-transfer";

    /**
     * 充值证书
     */
    public static final String TOPIC_TRADE_CERTIFICATE = "topic-trade-certificate";
    public static final String TAG_TRADE_CERTIFICATE = "tag-trade-certificate";
    public static final String TAG_DOWNLOAD_PAY_CERTIFICATE = "tag-download-pay-certificate";

    /**
     * 签约使用topic
     */
    public static final String TOPIC_SIGN_ASYNC = "topic-sign-async";

    /**
     * 协议签约使用的topic
     */
    public static final String TOPIC_AGREEMENT_SIGN_ASYNC = "topic-agreement-sign-async";
    public static final String TAG_AGREEMENT_SIGN_ASYNC = "tag-agreement-sign-async";

    /**
     * 预签约接口tag
     */
    public static final String TAG_PRE_SIGN = "trade-pre-sign-text";
    /**
     * 签约回调处理tag
     */
    public static final String TAG_SIGN_RECEIVE = "trade-sign-receive-text";
    /**
     * 预签约改手机tag
     */
    public static final String TAG_SIGN_MODIFY_INFO = "trade-sign-modify-info-text";

    /**
     * 异步商户通知topic
     */
    public static final String TOPIC_NOTIFY_MERCHANT = "topic-notify-merchant-async";
    public static final String TAG_NOTIFY_MERCHANT = "notify-merchant-text";


    // 订单完成topic
    public static final String TOPIC_ORDER_COMPLETE = "topic-order-complete-text";

    // 合伙人计费订单完成topic
    public static final String TOPIC_AGENT_ORDER_COMPLETE = "topic-agent-complete-text";

    /**
     * 风控后台处理订单的topic
     */

    public static final String TOPIC_RISKCONTROL_NOTIFY_TRADE = "topic-riskcontrol-notify-trade";
    /**
     * 通知交易风控审核通过
     */
    public static final String TAG_RISKCONTROL_NOTIFY_TRADE = "tag-riskcontrol-notify-trade";
    /**
     * 同一笔但再次被风控的tag
     */
    public static final String TAG_RISKCONTROL_NOTIFY_TRADE_AGAIN = "tag-riskcontrol-notify-trade-again";


    /**
     * 支付宝回调消息常量
     */

    public static final String TOPIC_ALIPAY_CALLBACK = "topic-alipay-callback";
    public static final String TAG_ALIPAY_CALLBACK_USERSIGN = "tag-alipay-callback-usersign";
    public static final String TAG_ALIPAY_CALLBACK_TRANSPAY = "tag-alipay-callback-transpay";
    public static final String TOPIC_ALIPAY_REPORT_DELAY = "topic-alipay-report-delay"; //延时查询报备状态的topic
    public static final String TAG_ALIPAY_REPORT_DELAY = "tag-alipay-report-delay";
    public static final String TOPIC_ALIPAY_REPORT_REPEAT = "topic-alipay-report-repeat"; //重复报备topic
    public static final String TAG_ALIPAU_REPROT_REPEAT = "topic-alipay-report-repeat";
    public static final String TAG_ALIPAY_CALLBACK_ENTRUST_TRANSFER = "tag-alipay-callback-entrust-transfer";
    public static final String TOPIC_ALIPAY_REFUND = "topic-alipay-refund";
    public static final String TAG_ALIPAY_REFUND = "tag-alipay-alipay-refund";
    public static final String TAG_ALIPAY_WITHDRAW = "tag-alipay-withdraw";
    public static final String TOPIC_ALIPAY_BUSY = "topic_alipay_busy";
    public static final String TAG_ALIPAY_BUSY_STEP_1 = "tag_alipay_busy_step_1";
    public static final String TAG_ALIPAY_BUSY_STEP_2 = "tag_alipay_busy_step_2";
    public static final String TAG_ALIPAY_CALLBACK_REFUND = "tag-alipay-callback-refund";

    /**
     * 刷新微信access_token
     */
    public static final String TOPIC_ACCESS_TOKEN_REFRESH = "topic_access_token_refresh";

    /**
     * 充值扫描任务的消息主体
     */
    public static final String TOPIC_RECHARGE_EXPIRE = "task_recharge_expire";

    // 同步支付数据
    public static final String TOPIC_SYNCHRONIZE_PAYMENT_DATA = "topic_rysc_synchronize_data";
    // 同步代征关系
    public static final String TOPIC_SYNCHRONIZE_RELATION_DATA = "topic_rysc_relation_data";

    // 代征主体:奔奔
    public static final String TAG_SUPPLIER_RYSC = "tag_supplier_rysc";

    /**
     * 发起代征主体审批流
     */
    public static final String TOPIC_CREATE_RELATION_FLOW = "topic_create_relation_flow";
    public static final String TAG_CREATE_RELATION_FLOW = "tag_create_relation_flow";

    /**
     * 审核通过后创建代征主体
     */
    public static final String TOPIC_AFTER_CREATE_RELATION_FLOW = "topic_after_create_relation_flow";
    public static final String TAG_AFTER_CREATE_RELATION_FLOW = "tag_after_create_relation_flow";

    /**
     * 修改负责人
     */
    public static final String TAG_CHANGE_PRINCIPAL = "tag_change_principal";

    /**
     * 修改账户信息
     */
    public static final String TAG_CHANGE_ACCOUNT = "tag_change_account";

    /**
     * 修改销售
     */
    public static final String TAG_CHANGE_SALER = "tag_change_saler";

    /**
     * 修改商户状态
     */
    public static final String TAG_MERCHANT_FREEZE = "tag_merchant_freeze";

    /**
     * 新建报价单
     */
    public static final String TOPIC_ADD_MERCHANT_QUOTE = "topic_add_merchant_quote";
    public static final String TAG_ADD_MERCHANT_QUOTE = "tag_add_merchant_quote";
    /**
     * 删除报价单
     */
    public static final String TAG_DELETE_MERCHANT_QUOTE = "tag_delete_merchant_quote";


    /**
     * 主体认证流程状态修改
     */
    public static final String TOPIC_MAIN_AUTH_CHANGE = "topic_main_auth_change";
    public static final String TAG_DELETE_MAIN_AUTH_FLOW = "tag_delete_main_auth_flow";
    public static final String TAG_APPROVAL_MERCHANT_VERIFY_DISAGREE_ASYNC = "tag-approval-merchant-verify-disagree-text";
    public static final String TAG_CONTINUE_MAIN_AUTH = "tag_continue_main_auth";


    public static final String TOPIC_ORDER_EXPIRE = "topic_order_expire";
    public static final String TOPIC_ORDER_GRANTING_EXPIRE = "topic_order_granting_expre";

    public static final String TOPIC_WX_WORK_ROBOT = "topic_wx_work_robot";
    public static final String TAG_WX_WORK_ROBOT = "topic_wx_work_robot";

    public static final String TOPIC_FEE_CHECK_TASK = "topic_fee_check_task";

    public static final String TOPIC_AUTO_REPORT = "topic_auto_report";
    public static final String TAG_AUTO_REPORT = "tag_auto_report";

    public static final String TOPIC_IP_INFO_HANDLE = "topic_ip_info";

    public static final String TOPIC_INVOICE_PROCESS = "topic_invoice_process";
    public static final String TAG_INVOICE_PROCESS = "tag_invoice_process";

    public static final String TOPIC_AGENT_QUOTE_EDIT = "topic_agent_quote_edit";
    public static final String TAG_AGENT_QUOTE_EDIT = "tag_agent_quote_edit";

    public static final String TOPIC_AGENT_QUOTE_EDIT_ROLLBACK = "topic_agent_quote_edit_rollback";
    public static final String TAG_AGENT_QUOTE_EDIT_ROLLBACK = "tag_agent_quote_edit_rollback";

    public static final String TOPIC_AGENT_QUOTE_EDIT_MODIFY = "topic_agent_quote_edit_modify";
    public static final String TAG_AGENT_QUOTE_EDIT_MODIFY = "tag_agent_quote_edit_modify";

    public static final String TOPIC_AGENT_MAININFO_EDIT = "topic_agent_maininfo_edit";
    public static final String TAG_AGENT_MAININFO_EDIT = "tag_agent_maininfo_edit";

    public static final String TOPIC_AGENT_BANKACCT_EDIT = "topic_agent_bankacct_edit";
    public static final String TAG_AGENT_BANKACCT_EDIT = "tag_agent_bankacct_edit";

    public static final String TOPIC_AGENT_SET_SELLER = "topic_agent_set_seller";
    public static final String TAG_AGENT_SET_SELLER = "tag_agent_set_seller";

    public static final String TOPIC_AGENT_SET_INVITER = "topic_agent_set_inviter";
    public static final String TAG_AGENT_SET_INVITER = "tag_agent_set_inviter";

    public static final String TOPIC_AGENT_SET_PRINCIPAL = "topic_agent_set_principal";
    public static final String TAG_AGENT_SET_PRINCIPAL = "tag_agent_set_principal";

    public static final String TOPIC_AGENT_QUOTE_DEL = "topic_agent_quote_del";
    public static final String TAG_AGENT_QUOTE_DEL = "tag_agent_quote_del";

    public static final String TOPIC_AGENT_BATCH_SET_INVITER_SELLER = "topic_agent_batch_set_inviter_seller";
    public static final String TAG_AGENT_BATCH_SET_INVITER_SELLER = "tag_agent_batch_set_inviter_seller";

    public static final String TOPIC_YISHUI = "topic_yishui";
    public static final String TAG_YISHUI_ADD_MERCHANT = "tag_yishui_add_merchant";
    public static final String TAG_YISHUI_EDIT_FEE = "tag_yishui_edit_fee";

    public static final String TOPIC_YISHUI_CALL_BACK = "topic_yishui_call_back";
    public static final String TAG_YISHUI_CALL_BACK = "tag_yishui_call_back";

    public static final String TOPIC_ALIPAY_BILL_TASK = "topic_alipay_bill_task";

    public static final String TOPIC_ALIPAY_BILL_QUERY = "topic_alipay_bill_query";
    public static final String TAG_ALIPAY_BILL_QUERY = "tag_alipay_bill_query";

    public static final String TOPIC_NOTICE_TIMER = "topic_notice_timer";

    //微信来账通知
    public static final String TOPIC_WX_INCOME_RECORD = "topic-wx-income-record-test";

    //微信来账通知异常处理
    public static final String TOPIC_WX_INCOME_RECORD_ERROR = "topic-wx-income-record-error-test";
    public static final String TAG_WX_INCOME_RECORD_ERROR = "tag-wx-income-record-error-test";

    //微信金额变动
    public static final String TOPIC_WX_AMOUNT_CHANGE = "topic-wx-amount-change";
    public static final String TAG_WX_AMOUNT_CHANGE = "tag-wx-amount-change";


    //微信支付查询
    public static final String TOPIC_WX_PAY_QUERY = "topic-wx-pay-query";
    public static final String TAG_WX_PAY_QUERY = "tag-wx-pay-query";
    public static final String TAG_WX_PAY_RETRY = "tag-wx-pay-retry";
    public static final String TAG_WX_PAY_NO_FOND_RETRY = "tag-wx-no-fond-retry";
    public static final String TAG_WX_PAY_CHANGE_FOUNDS = "tag-wx-no-change-founds";


    //微信支付调账
    public static final String TOPIC_WX_PAY_ADJUSTMENT = "topic-wx-pay-adjustment";
    public static final String TAG_WX_PAY_ADJUSTMENT = "tag-wx-pay-adjustment";

    //君享汇支付调账
    public static final String TOPIC_JXH_PAY_ADJUSTMENT = "topic-jxh-pay-adjustment";
    public static final String TAG_JXH_PAY_ADJUSTMENT = "tag-jxh-pay-adjustment";

    //微信报备
    public static final String TOPIC_WX_REPORT = "topic-wx-report";
    public static final String TAG_WX_REPORT = "tag-wx-report";

    public static final String TOPIC_WX_BILL_QUERY = "topic-wx-bill-query";
    public static final String TOPIC_WX_BILL_SAVE = "topic-wx-bill-save";

    public static final String TOPIC_JOINPAY_WITHDRAW = "topic-joinpay-withdraw";
    public static final String TAG_JOINPAY_WITHDRAW = "tag-joinpay-withdraw";

    //微信提现
    public static final String TOPIC_WX_WITHDRAW = "topic-wx-withdraw";
    public static final String TAG_WX_WITHDRAW = "tag-wx-withdraw";

    //微信提现订单查询
    public static final String TOPIC_WX_WITHDRAW_STATUS = "topic-wx-withdraw-status";
    public static final String TAG_WX_WITHDRAW_STATUS = "tag-wx-withdraw-status";

    //微信提现兜底定时任务
    public static final String TOPIC_WX_WITHDRAW_TIMER = "topic-wx-withdraw-timer";

    //订单退汇
    public static final String TOPIC_REEXCHANGE = "topic-reexchange";
    public static final String TAG_REEXCHANGE = "tag-reexchange";


    //订单退汇
    public static final String TOPIC_FORCE_DELETE = "topic-force-delete";
    public static final String TAG_COMMON_FORCE_DELETE = "tag-common-force-delete";
    public static final String TAG_FEE_FORCE_DELETE = "tag-fee-force-delete";

    // 线上签约
    public static final String TOPIC_FLOW_SIGN = "topic-flow-sign";
    public static final String TAG_FLOW_SIGN = "tag-flow-sign";
    public static final String SIGN_GROUP = "signGroup";
    public static final String TOPIC_FETCH_SIGN_URL = "topic-fetch-sign-url";
    public static final String TAG_FETCH_SIGN_URL = "tag-fetch-sign";


    //服务履约单-定时任务
    public static final String TOPIC_SERVICE_CONFIRM_TASK = "topic-service-confirm-task";
    //服务履约单-截止时间关闭
    public static final String TOPIC_SERVICE_CONFIRM_CLOSE = "topic-service-confirm-close";



    //招行订单重试
    public static final String TOPIC_CMB_RETRY = "topic_cmb_retry";
    public static final String TAG_CMB_RETRY = "tag_cmb_retry";

    public static final String TOPIC_CMB_CALLBACK_QUERY = "topic_cmb_callback_query";
    public static final String TAG_CMB_CALLBACK_QUERY = "tag_cmb_callback_query";

    public static final String TOPIC_CMB_REFUND = "topic_cmb_refund";
    public static final String TAG_CMB_REFUND = "tag_cmb_refund";
    public static final String TAG_CMB_ACCOUNT_REFUND = "tag_cmb_account_refund";

    public static final String TOPIC_CMB_ORDER_TO_LOCAL_TASK = "topic_cmb_order_to_local_task";

    public static final String TOPIC_CMB_WITHDRAW_CALLBACK_QUERY = "topic_cmb_withdraw_callback_query";
    public static final String TAG_CMB_WITHDRAW_CALLBACK_QUERY = "tag_cmb_withdraw_callback_query";


    //风控名单插入
    public static final String TOPIC_RISK_NAME_INSERT = "topic-risk-name-insert";
    public static final String TAG_RISK_NAME_INSERT = "tag-risk-name-insert";
    //分控名单同步
    public static final String TOPIC_RISK_NAME_SYNC = "topic-risk-name-sync";
    public static final String TAG_RISK_NAME_SYNC = "tag-risk-name-sync";

    //账单同步
    public static final String TOPIC_FEE_ORDER_SYNC = "topic-fee-order-sync";
    public static final String TAG_FEE_ORDER_SYNC = "tag-fee-order-sync";

    //外部订单账单同步
    public static final String TOPIC_FEE_OUTSIDE_ORDER = "topic-fee-outside-order";
    public static final String TAG_FEE_OUTSIDE_ORDER = "tag-fee-outside-order";

    //账单支付
    public static final String TOPIC_FEE_ORDER_ITEM_PAY = "topic-fee-order-item-pay";
    //账单个税支付
    public static final String TAG_FEE_ORDER_ITEM_TAX_PAY = "tag-fee-order-item-tax-pay";
    //账单服务费支付
    public static final String TAG_FEE_ORDER_ITEM_SERVICE_PAY = "tag-fee-order-item-service-pay";

    //账单凭证上传
    public static final String TOPIC_FEE_ORDER_CETIFICATE_UPLOAD = "topic-fee-order-cetificate-upload";
    public static final String TAG_FEE_ORDER_CETIFICATE_UPLOAD = "tag-fee-order-cetificate-upload";
    public static final String TAG_FEE_ORDER_ONLINE_CETIFICATE_APPLY = "tag-fee-order-online_cetificate-apply";
    public static final String TAG_FEE_ORDER_ONLINE_CETIFICATE_UPLOAD = "tag-fee-order-online_cetificate-upload";

    public static final String TOPIC_AUTH_INIT = "topic-auth-init";
    public static final String TAG_AUTH_INIT = "tag-auth-init";

    public static final String TOPIC_UPLOAD_JOB_FILE="topic-upload-job-file";
    public static final String TAG_UPLOAD_JOB_FILE="tag-upload-job-file";

    public static final String TOPIC_API_BILL_APPLY = "topic_api_bill_apply";
    public static final String TAG_API_BILL_APPLY = "tag_api_bill_apply";

    //支付宝入金
    public static final String TAG_ALIPAY_TRANS_INCOME = "tag_alipay_trans_income";

    //支付宝报备订阅接口
    public static final String TOPIC_ALIPAY_SUBCRIBE = "topic_alipay_subcribe";
    public static final String TAG_ALIPAY_SUBCRIBE = "tag_alipay_subcribe";

    public static final String TOPIC_JOB_CALLBACK = "topic_job_callback";
    public static final String TAG_JOB_CALLBACK = "tag_job_callback";

    //外部订单计费
    public static final String TOPIC_OFFLINE_ORDER_FEE = "topic_offline_order_fee";
    public static final String TAG_OFFLINE_ORDER_FEE = "tag_offline_order_fee";

    public static final String TOPIC_TRADE_CKH_OFFLINE = "topic_trade_ckh_offline";
    public static final String TAG_TRADE_CKH_OFFLINE = "tag_trade_ckh_offline";

    //导入线下签约人员
    public static final String TOPIC_OFFLINE_SIGNER = "topic_offline_signer";
    public static final String TAG_OFFLINE_SIGNER = "tag_offline_signer";

    //微信JSAPI支付回调
    public static final String TOPIC_WX_JSAPI_CALLBACK = "topic_wx_jsapi_callback";
    public static final String TAG_WX_JSAPI_CALLBACK = "tag_wx_jsapi_callback";

    //微信JSAPI关单
    public static final String TOPIC_WX_JSAPI_CLOSE = "topic_wx_jsapi_close";
    public static final String TAG_WX_JSAPI_CLOSE = "tag_wx_jsapi_close";

    //微信JSAPI退款回调
    public static final String TOPIC_WX_JSAPI_REFUND = "topic_wx_jsapi_refund";
    public static final String TAG_WX_JSAPI_REFUND = "tag_wx_jsapi_refund";

    //招行回调信息
    public static final String TOPIC_CMB_NOTIFY = "topic_cmb_notify";
    public static final String TAG_CMB_MAINTENANCE = "tag_cmb_maintenace";//维护通知
    public static final String TAG_CMB_ACCOUNT_CHANGE = "tag_cmb_account_change"; //账户变动通知
    public static final String TAG_CMB_REMIT_RESULT = "tag_cmb_remit_result"; //账户变动通知
    public static final String TAG_CMB_PAY2B_RESULT = "tag_cmb_pay2b_result"; //转账支付通知

    public static final String TOPIC_CMB_RECHARGE_BILL = "topic_cmb_recharge_bill";
    public static final String TAG_CMB_RECHARGE_BILL = "tag_cmb_recharge_bill";

    public static final String TOPIC_PUSH_TASK = "topic_push_task";

    //汇聚报备上传图片
    public static final String TOPIC_REPORT_UPLOAD = "topic_report_upload";
    public static final String TAG_REPORT_UPLOAD = "tag_report_upload";

    //计费订单统计同步消息
    public static final String TOPIC_FEE_SYNC = "topic_fee_sync";
    //商户计费订单同步tag
    public static final String TAG_MERCHANT_FEE_SYNC = "tag_merchant_fee_sync";
    //销售计费订单同步tag
    public static final String TAG_SALER_FEE_SYNC = "tag_saler_fee_sync";
    //供应商计费订单同步tag
    public static final String TAG_VENDOR_FEE_SYNC = "tag_vendor_fee_sync";
    //合伙人计费订单同步tag
    public static final String TAG_AGENT_FEE_SYNC = "tag_agent_fee_sync";

    public static final String TOPIC_CREATE_RELATION = "topic_create_relation";
    public static final String TAG_CREATE_RELATION = "tag_create_relation";

    public static final String TOPIC_ZFT_CHANNEL_AUDIT = "topic_zft_channel_audit";
    public static final String TAG_ZFT_CHANNEL_AUDIT = "tag_zft_channel_audit";
    public static final String TAG_ZFT_AUDIT_REFUSE_NOTIFY = "tag_zft_audit_refuse_notify";
    public static final String TAG_ZFT_AUDIT_PASS_NOTIFY = "tag_zft_audit_pass_notify";

    public static final String TOPIC_EMPLOYER_TRADE_CHECK_TASK = "topic_employer_trade_check_task";

    //C端获取电子回单
    public static final String TOPIC_MINIAPP_RECEIPT_ORDER = "topic_miniapp_receipt_order";
    public static final String TAG_MINIAPP_RECEIPT_ORDER = "tag_miniapp_receipt_order";

    public static final String TOPIC_MOCK_SUCCESS_CALL_BACK = "topic_mock_success_call_back";
    public static final String TAG_MOCK_SUCCESS_CALL_BACK = "tag_mock_success_call_back";

    //合伙人小程序首次登录注册用户
    public static final String TOPIC_AGENT_GET_OR_REGISTER = "topic_agent_get_or_register";
    public static final String TAG_AGENT_GET_OR_REGISTER = "tag_agent_get_or_register";

    //结算结果推送到河马
    public static final String TOPIC_PAY_CALLBACK_TO_HEMA = "topic_pay_callback_to_hema";
    public static final String TAG_PAY_CALLBACK_TO_HEMA = "tag_pay_callback_to_hema";

    public static final String TOPIC_SYNC_OUT_ORDER = "topic_sync_out_order";
    public static final String TAG_SYNC_OUT_ORDER = "tag_sync_out_order";

    public static final String TOPIC_BEE_ORDER_SYNC = "topic_bee_order_sync";

    /****
     * 充值入账的Topic
     */
    public static final String TOPIC_INCOME_ASYNC = "topic-income-async";

    /***
     * 充值入账的tag
     */
    public static final String TAG_INCOME_NOTIFY = "tag-income";


    /****
     * 君享汇代付回调的topic
     */
    public static final String TOPIC_JXH_SINGLEPAY_ASYNC = "topic-jxh-singlepay-async";

    /***
     * 君享汇代付回调的tag
     */
    public static final String TAG_JXH_SINGLEPAY = "tag-jxh-singlepay";



    public static final String TOPIC_JXH_PAY_QUERY = "topic-jxh-pay-query";
    public static final String TAG_JXH_PAY_QUERY = "tag-jxh-pay-query";
    public static final String TAG_JXH_PAY_RETRY = "tag-jxh-pay-retry";
    public static final String TAG_JXH_PAY_NO_FOND_RETRY = "tag-jxh-no-fond-retry";
    public static final String TAG_JXH_PAY_CHANGE_FOUNDS = "tag-jxh-no-change-founds";

    public static final String TOPIC_JXH_WITHDRAW = "topic-jxh-withdraw";
    public static final String TAG_JXH_WITHDRAW = "tag-jxh-withdraw";

    //微信提现订单查询
    public static final String TOPIC_JXH_WITHDRAW_STATUS = "topic-jxh-withdraw-status";
    public static final String TAG_JXH_WITHDRAW_STATUS = "tag-jxh-withdraw-status";

    public static final String TOPIC_JXH_WITHDRAW_ASYNC = "topic-jxh-withdraw-async";
    public static final String TAG_JXH_WITHDRAW_ASYNC = "tag-jxh-withdraw-async";

    public static final String TOPIC_AC_SETTLE_SYNC = "topic-ac-settle-sync";

    public static final String TOPIC_MAINSTAY_SETTLE_SYNC = "topic-mainstay-settle-sync";

    public static final String TOPIC_MAINSTAY_BALANCE_SYNC = "topic-mainstay-balance-sync";

    public static final String TOPIC_REPAIR_RECORD_SYNC = "topic-repair-record-sync";

    public static final String TOPIC_REFUND_SETTLE_SYNC = "topic-refund-settle-sync";

    public static final String TOPIC_ORDERREEXCHANGE_SYNC = "topic-orderreexchange-sync";

}
