package com.zhixianghui.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * C端用户签约比例等级枚举
 * <AUTHOR>
 * @date 2020-08-05
 */
public enum SignRateLevelEnum {

    LEVEL_1(100, "无法签署或30%以下"),
    LEVEL_2(101, "30%-70%"),
    LEVEL_3(102, "70%-100%"),
    LEVEL_4(103, "可先签后发（100%）"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    SignRateLevelEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer value) {
        return getEnum(value).getDesc();
    }

    public int getValue() {
        return value;
    }

    public static SignRateLevelEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}