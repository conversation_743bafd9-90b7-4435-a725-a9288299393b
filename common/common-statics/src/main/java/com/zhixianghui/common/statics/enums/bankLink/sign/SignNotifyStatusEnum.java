package com.zhixianghui.common.statics.enums.bankLink.sign;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @ClassName SignNotifyStatusEnum
 * @Description TODO
 * @Date 2022/8/19 10:00
 */
@AllArgsConstructor
@Getter
@ToString
public enum SignNotifyStatusEnum {

    SIGN_MISSON_COMPLETE("SIGN_MISSON_COMPLETE","签署任务完成通知"),

    SIGN_FLOW_COMPLETE("SIGN_FLOW_COMPLETE","流程结束回调通知");




    private String status;

    private String desc;
}
