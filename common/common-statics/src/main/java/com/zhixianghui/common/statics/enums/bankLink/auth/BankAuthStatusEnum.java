package com.zhixianghui.common.statics.enums.bankLink.auth;

import java.util.Arrays;

/**
 * 渠道鉴权状态
 * <AUTHOR>
 * @date 2020/8/28
 **/
public enum BankAuthStatusEnum {
    SUCCESS(100, "成功"),
    FAIL(101, "失败"),
    UN_KNOW(102, "未知"),
    SYSTEM_ERROR(103, "未知")
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    BankAuthStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static BankAuthStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
