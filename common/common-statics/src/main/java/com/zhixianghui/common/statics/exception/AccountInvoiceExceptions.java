package com.zhixianghui.common.statics.exception;

/**
 * Author: Cmf
 * Date: 2020.3.2
 * Time: 9:26
 * Description: 发票资金账务相关的异常
 */
public class AccountInvoiceExceptions {

    /**
     * 账户金额业务计算错误
     */
    public static final BizException ACCOUNT_AMOUNT_BIZ_CALC_ERROR = new BizException(510001, false);

    /**
     * 发票余额不足
     */
    public static final BizException INVOICE_AMOUNT_NOT_ENOUGH = new BizException(510002, false);

    /**
     * 待账务处理表的账务处理流水号重复
     */
    public static final BizException ACCOUNT_PROCESS_PENDING_PROCESS_NO_REPEAT = new BizException(510003, false);

    /**
     * 待账务处理记录已存在
     */
    public static final BizException ACCOUNT_PROCESS_PENDING_UNIQUE_KEY_REPEAT = new BizException(510004, false);
    /**
     * 账户记录不存在
     */
    public static final BizException ACCOUNT_RECORD_NOT_EXIT = new BizException(510005, false);
    /**
     * 账户状态处于"禁用"
     */
    public static final BizException ACCOUNT_STATUS_IS_INACTIVE = new BizException(510006, false);

    /**
     * 重复账务处理
     */
    public static final BizException ACCOUNT_PROCESS_REPEAT = new BizException(510007, false);
    /**
     * 锁账户失败
     */
    public static final BizException ACQUIRE_LOCK_FAIL = new BizException(510008, false);
}
