package com.zhixianghui.common.statics.enums.merchant;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/7/27 10:42
 */

public enum EditTypeEnum {
    EDIT_MERCHANT_BASE_INFO("editBaseInfo", "商户基本信息编辑"),
    EDIT_MERCHANT_COOPERATE_INFO("editMerchantCooperateInfo", "商户合作信息编辑"),
    EDIT_MAINSTAY_COOPERATE_INFO("editMainstayCooperateInfo", "代征主体合作信息编辑"),
    EDIT_MAIN_INFO("editMainInfo", "主体信息编辑"),
    EDIT_BUSINESS_INFO("editBusinessInfo", "经营信息编辑"),
    AGENT_CHANGE_STATUS("agentChangeStatus", "合伙人状态改变"),
    AGENT_EDIT_BASE_INFO("agentEditBaseInfo", "合伙人编辑基本信息"),
    AGENT_EDIT_BANK_INFO("agentEditBankInfo", "合伙人编辑银行账户信息"),
    AGENT_EDIT_MAIN_INFO("agentEditMainInfo", "合伙人编辑银行账户信息"),
    AGENT_EDIT_QUOTE_INFO("agentEditQuoteInfo", "合伙人编辑产品报价单信息"),
    AGENT_DELETE_QUOTE_INFO("agentDeleteQuoteInfo", "合伙人删除产品报价单信息"),
    AGENT_SET_SELLER("agentSetSeller", "合伙人设置销售"),
    AGENT_SET_INVITER("agentSetInviter", "合伙人设置邀请人"),
    AGENT_SET_PRINCIPAL("agentSetPrincipal", "合伙人设置负责人")
    ;

    String type;
    String describe;

     EditTypeEnum(String type, String describe) {
         this.type = type;
         this.describe = describe;
     }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }
}
