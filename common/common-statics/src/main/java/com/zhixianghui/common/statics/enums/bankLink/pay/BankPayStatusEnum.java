package com.zhixianghui.common.statics.enums.bankLink.pay;

import java.util.Arrays;

/**
 * 渠道付款订单状态
 * <AUTHOR>
 * @date 2020/8/28
 **/
public enum BankPayStatusEnum {
    SUCCESS(100, "成功"),
    FAIL(101, "失败"),
    PROCESS(102, "处理中"),
    UN_KNOW(103, "未知"),
    CMB_PROCESS(104, "招行专用-处理中"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    BankPayStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static BankPayStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
