package com.zhixianghui.common.statics.enums.merchant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @ClassName AgreementSignTypeEnum
 * @Description TODO
 * @Date 2022/8/16 16:30
 */
@AllArgsConstructor
@Getter
@ToString
public enum AgreementSignTypeEnum {

    ONLINE(100,"线上签署"),

    OFFLINE(101,"线下签署");

    private int value;

    private String desc;
}
