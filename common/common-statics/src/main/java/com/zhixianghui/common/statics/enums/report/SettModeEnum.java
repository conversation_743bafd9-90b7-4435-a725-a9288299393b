package com.zhixianghui.common.statics.enums.report;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 结算类型
 * @date 2020-09-29 14:55
 **/
@AllArgsConstructor
@Getter
@ToString
public enum SettModeEnum {
    /**
     *自动结算
     */
    AUTO(1, "自动结算"),
    /**
     *手动结算
     */
    MANUAL(2, "手动结算"),
    /**
     *手工+D0 结算
     */
    MANUAL_D0(3, "手工+D0 结算"),

    /**
     * 自动+D0 结算
     */
    AUTO_D0(4,"自动+D0 结算")
    ;

    private final int value;
    private final String desc;
}