package com.zhixianghui.common.statics.enums.riskcontrol;

import lombok.Getter;

import java.util.Arrays;

/**
 * 策略原子条件变量枚举
 *
 * <AUTHOR> <PERSON><PERSON>
 */
@Getter
public enum StrategyAtomVariableEnum {

    USER_MONTH_AMOUNT(100, "用户月金额(元)", "monthAmount",StrategyAtomVariableEnum.OPERATE_SCRIPT),

    USER_YEAR_AMOUNT(101, "用户年金额(元)", "yearAmount",StrategyAtomVariableEnum.OPERATE_SCRIPT),

    USER_AGE(200, "用户年龄(岁)", "age",StrategyAtomVariableEnum.OPERATE_SCRIPT),

    USER_POSTPONE_YEAR_AMOUT(300,"用户顺延年(元)","postponeYearAmount",StrategyAtomVariableEnum.OPERATE_SCRIPT),

    USER_KEYWORD(400,"打款备注","payRemark",StrategyAtomVariableEnum.REG_SCRIPT),

    USER_SIGN(500,"用户签约","sign",null),

    USER_AUTH(600,"用户认证","auth",null),

    THREE_ELEMENT(700,"三要素校验","threeElement",null),

    FOUR_ELEMENT(800,"四要素校验","fourElement",null),

    USER_PLATFORM_MONTH_AMOUNT(900,"平台月金额(元)","totalMonthAmount",StrategyAtomVariableEnum.OPERATE_SCRIPT),

    ORDER_ITEM_AMOUNT(1000,"单笔金额(元)","orderAmount",StrategyAtomVariableEnum.OPERATE_SCRIPT),

    MCH_DAILY_AMOUNT(1100,"商户日限额(元)","mchDailyAmount",StrategyAtomVariableEnum.OPERATE_SCRIPT),

    MCH_PLATFORM_DAILY_AMOUNT(1200,"商户平台日限额(元)","mchPlatformDailyAmount",StrategyAtomVariableEnum.OPERATE_SCRIPT),

    MCH_SINGLE_USER_COUNT(1300,"用户单日等额交易次数","mchSingleUserCount",StrategyAtomVariableEnum.OPERATE_SCRIPT);
    ;

    private int value;

    private String desc;

    private String field;

    private String script;

    StrategyAtomVariableEnum(int value, String desc, String field,String script) {
        this.value = value;
        this.desc = desc;
        this.field = field;
        this.script = script;
    }

    public static StrategyAtomVariableEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

    public static final String OPERATE_SCRIPT = "%s%s%s";

    public static final String REG_SCRIPT = "try{" +
            "var reg = new RegExp('%s','g');" +
            "reg.test('%s');" +
            "}catch(e){" +
            "false" +
            "}";
}
