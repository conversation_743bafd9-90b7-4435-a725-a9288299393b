package com.zhixianghui.common.statics.enums.merchant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 电签账户状态
 * @date 2021/1/14 18:21
 **/
@AllArgsConstructor
@Getter
@ToString
public enum SignAccountStatusEnum {
    /**
     *已激活
     */
    ACTIVATE(100, "已激活"),

    /**
     *待创建
     */
    WAIT_CREATE(200, "待创建"),
    ;

    private final int value;
    private final String desc;
}
