package com.zhixianghui.common.statics.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @描述: 基础实体类，包含各实体公用属性 .
 * @作者: WuShuicheng .
 * @创建时间: 2013-7-28,下午8:53:52 .
 * @版本: 1.0 .
 */
@Data
public class BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	protected Long id;
	protected Integer version;
	/**
	 * 创建时间
	 */
	protected Date createTime;
	/**
	 * 用于导出功能, 记录父级权限标识
	 */
	protected String parentPermissionFlag;
}
