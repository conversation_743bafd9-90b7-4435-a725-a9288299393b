package com.zhixianghui.common.statics.enums.bankLink.sign;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 签约通道状态枚举
 * @date 2021-01-06 11:13
 **/
@AllArgsConstructor
@Getter
@ToString
public enum SignChannelStatusEnum {

    /**
     * 成功
     */
    SUCCESS(100, "成功"),
    /**
     * 失败
     */
    FAIL(101, "失败"),
    /**
     * 未知
     */
    UN_KNOW(102, "未知")
    ;
    /**
     * 枚举值
     */
    private final int value;

    /**
     * 描述
     */
    private final String desc;
}
