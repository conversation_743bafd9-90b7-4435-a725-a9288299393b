package com.zhixianghui.common.statics.enums.invoice;

import java.util.Arrays;

/**
 * 发票账户处理状态
 * <AUTHOR>
 */
public enum AccountHandleStatusEnum {
    UN_HANDLE("未处理",1),
    DEBIT("已扣款",2),
    REFUND("已退回",3),
    ;

    private String desc;
    private int value;

    AccountHandleStatusEnum(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static AccountHandleStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
