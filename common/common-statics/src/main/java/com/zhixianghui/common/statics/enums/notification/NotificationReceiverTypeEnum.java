package com.zhixianghui.common.statics.enums.notification;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 接收人类型
 */
@Getter
@AllArgsConstructor
public enum NotificationReceiverTypeEnum {
    ALL_MERCHANT(100,"全部用户"),
    ALL_EMPLOYER(200, "仅商户"),
    ALL_MAINSTAY(300, "仅供应商"),
    SELECTED_MERCHANT(400,"指定用户"),
    ALL_AGENT(500, "全部合伙人"),
    SELECTED_AGENT(600, "指定合伙人"),;


    private Integer code;
    private String description;
}
