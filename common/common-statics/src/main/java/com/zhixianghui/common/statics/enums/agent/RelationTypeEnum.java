package com.zhixianghui.common.statics.enums.agent;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 合伙人商户关系
 * @date 2021/2/5 9:22
 **/
@AllArgsConstructor
@Getter
@ToString
public enum RelationTypeEnum {
    /**
     * 全部
     */
    ALL(100, "全部"),
    /**
     * 直接
     */
    DIRECT(101, "直接"),
    /**
     * 间接
     */
    INDIRECT(102, "间接"),
    ;

    private final int value;
    private final String desc;

    public static RelationTypeEnum getEnumByValue(int value) {
        return Arrays.stream(RelationTypeEnum.values()).filter(x->x.getValue()==value).findFirst().orElse(null);
    }
}
