package com.zhixianghui.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 商户状类型枚举
 * <AUTHOR>
 * @date 2020-08-05
 */
public enum MerchantTypeEnum {
    
    EMPLOYER(100, "用工企业"),
    MAINSTAY(101, "代征主体"),
    PLATFORM(102, "智享平台"),
    AGENT(103, "合伙人"),
    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    MerchantTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static MerchantTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}