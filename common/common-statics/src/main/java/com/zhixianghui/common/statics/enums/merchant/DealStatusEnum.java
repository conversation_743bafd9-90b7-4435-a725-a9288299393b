package com.zhixianghui.common.statics.enums.merchant;

/**
 * <AUTHOR>
 * @ClassName DealStatusEnum
 * @Description TODO
 * @Date 2022/8/11 9:47
 */
public enum DealStatusEnum {

    DEAL(100,"已成交"),

    UNDEAL(101,"未成交");

    private int value;

    private String desc;

    DealStatusEnum(int value,String desc){
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
