package com.zhixianghui.common.statics.enums.merchant;

import java.util.Arrays;

/**
 * 纳税人类型
 * <AUTHOR>
 * @date 2020-12-25
 */
public enum TaxPayerTypeEnum {

    GENERAL(1, "一般纳税人"),
    SMALL_SCALE(2, "小规模纳税人"),

    ;
    /**
     * 枚举值
     */
    private int value;

    /**
     * 描述
     */
    private String desc;

    TaxPayerTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public static TaxPayerTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}