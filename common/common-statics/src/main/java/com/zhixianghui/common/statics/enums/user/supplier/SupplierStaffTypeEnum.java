package com.zhixianghui.common.statics.enums.user.supplier;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 供应商后台员工类型
 * @date 2020-12-10 16:49
 **/
@Getter
@ToString
@AllArgsConstructor
public enum SupplierStaffTypeEnum {

    /**
     * 超级管理员
     **/
    ADMIN("超级管理员", 1),

    /**
     * 普通用户
     **/
    USER("普通用户", 2),
    ;

    private final String desc;
    private final int value;

}
