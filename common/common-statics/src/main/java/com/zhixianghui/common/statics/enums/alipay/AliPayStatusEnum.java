package com.zhixianghui.common.statics.enums.alipay;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;


/**
 * <AUTHOR>
 * @description 支付宝报备状态
 * @date 2021-04-01 10:31
 **/
@AllArgsConstructor
@Getter
@ToString
public enum AliPayStatusEnum {
    /**
     * 报备成功
     */
    SUCCESS("10000","报备成功"),

    /**
     * 服务异常
     */
    UNKNOW("20000","服务不可用"),

    /**
     * 授权权限不足
     */
    UNAUTH("20001","授权权限不足"),

    /**
     * 缺少必要参数
     */
    MISSING("40001","缺少必要参数"),

    /**
     * 非法参数
     */
    INVALID("40002","非法参数"),

    /**
     * 业务异常
     */
    BUSINESS_FAILED("40004","业务异常"),

    /**
     * IVS权限不足
     */
    ISV_PERMISSIONS("40006","ISV权限不足"),

    /**
     * 调用频次超限
     */
    LIMITED("40005","调用频次超限")
    ;

    /**
     * 枚举值
     */
    private final String value;

    /**
     * 描述
     */
    private final String desc;
}
