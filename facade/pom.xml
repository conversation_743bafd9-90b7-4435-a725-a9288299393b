<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-all</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>

    <artifactId>zhixianghui-facade</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>facade-account-invoice</module>
        <module>facade-banklink</module>
        <module>facade-common</module>
        <module>facade-export</module>
        <module>facade-timer</module>
        <module>facade-risk-control</module>
        <module>facade-trade</module>
        <module>facade-fee</module>
        <module>facade-merchant</module>
        <module>facade-notify</module>
        <module>facade-flow</module>
        <module>facade-data</module>
        <module>facade-employee</module>

        <module>facade-pay</module>
    </modules>

</project>
