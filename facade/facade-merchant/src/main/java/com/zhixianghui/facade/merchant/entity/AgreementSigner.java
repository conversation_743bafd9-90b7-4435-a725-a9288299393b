package com.zhixianghui.facade.merchant.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 协议签署人表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Data
public class AgreementSigner implements Serializable {

    private static final long serialVersionUID = 8357875131742822096L;

    private Long id;

    /**
     * 协议ID
     */
    private Long agreementId;

    /**
     * 签署人编号
     */
    private String signerNo;

    /**
     * 签署人商户名称
     */
    private String signerMchName;

    /**
     * 签署人名
     */
    private String signerName;

    /**
     * 签署人手机号
     */
    private String signerPhone;

    /**
     * 签署人类型
     */
    private Integer signerType;

    /**
     * 签署人状态
     */
    private Integer status;

    /**
     *签署人账号id
     */
    private String signerAccountId;

    /**
     * 签署机构id
     */
    private String signerAuthorizedAccountId;

    /**
     * 签署地址
     */
    private String signUrl;

    /**
     * 实际操作人
     */
    private String operatorPhone;

    /**
     * 实际操作人
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operatorTime;

    /**
     * 签署时间
     */
    private Date signTime;

}
