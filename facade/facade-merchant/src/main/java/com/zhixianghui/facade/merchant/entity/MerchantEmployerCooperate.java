/*
 * Powered By [joinPay.com]
 */

package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用工企业合作信息
 * <AUTHOR>
 */
@Data
public class MerchantEmployerCooperate implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private Integer version;
	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 更新人登录名
	 */
	private String updator;

	/**
	 * 商户编码
	 */
	private String mchNo;

	/**
	 * 行业类型编码
	 */
	private String industryTypeCode;

	/**
	 * 行业类型名称
	 */
	private String industryTypeName;

	/**
	 * 预计用工人数
	 */
	private Integer workerNum;

	/**
	 * 预计C端签约率等级
	 * @see com.zhixianghui.common.statics.enums.merchant.SignRateLevelEnum
	 */
	private Integer signRateLevel;

	/**
	 * 自由职业者月经营所得低于9.7万比例
	 */
	private BigDecimal workerMonthIncomeRate;

	/**
	 * 预计月资金流水
	 */
	private BigDecimal monthMoneySlip;

	/**
	 * 提供收入明细方式
	 * @see com.zhixianghui.common.statics.enums.merchant.ProvideIncomeDetailTypeEnum
	 */
	private Integer provideIncomeDetailType;

	/**
	 * 公司网站
	 */
	private String companyWebsite;

	/**
	 * 业务平台名称
	 */
	private String bizPlatformName;

	/**
	 * 其他信息，json格式
	 */
	private String jsonInfo;

	/**
	 * 行业类别
	 */
	private String professionType;

	/**
	 * 行业名称
	 */
	private String professionName;

	/**
	 * 资质文件编码
	 */
	private String qualificationCode;

	/**
	 * 资质文件地址
	 */
	private String qualificationUrl;

	/**
	 * 接入服务类型
	 */
	private String serviceType;

	/**
	 * 支付宝小程序名称
	 */
	private String alipayAppName;

	/**
	 * app名称
	 */
	private String appName;

	/**
	 * wap站点名称
	 */
	private String wapName;

	/**
	 * wap站点链接
	 */
	private String wapSite;

	/**
	 * pc站点名称
	 */
	private String pcName;

	/**
	 * pc站点链接
	 */
	private String pcSite;

	/**
	 * 支付宝smid
	 */
	private String smid;

}
