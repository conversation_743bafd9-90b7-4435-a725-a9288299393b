package com.zhixianghui.facade.merchant.enums;

import com.zhixianghui.common.statics.enums.merchant.ValidityDateTypeEnum;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/6/2 18:25
 */
public enum SelfDeclaredEnum {

    SELF(0, "自行申报"),
    PLATFORM(1, "平台代扣");

    private int value;
    private String desc;

    SelfDeclaredEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static SelfDeclaredEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

    public static String getDesc(int value) {
        return getEnum(value).getDesc();
    }
}
