package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerQuoteRateVo;

import java.util.List;
import java.util.Map;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-07-20
 */
public interface MerchantEmployerQuoteRateFacade {

    List<MerchantEmployerQuoteRateVo> listMerchantQuoteRate(String mchNo);

    Map<Long,List<MerchantEmployerQuoteRate>> groupByQuoteIds(List<Long> quoteIdList);
}
