package com.zhixianghui.facade.merchant.service.pms;

import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import java.util.List;

/**
 * 部门管理接口
 *
 * <AUTHOR> <PERSON>
 */
public interface PmsDepartmentFacade {

    List<PmsDepartment> listDepartment();

    List<PmsDepartment> listDepartmentByName(String departmentName);

    PmsDepartment getDepartmentById(long id);

    /**
     * 根据部门编号获取部门
     * @param number {@link PmsDepartmentNumberEnum#getNumber()}
     */
    PmsDepartment getDepartmentByNumber(String number);

    /**
     * 根据部门编号获取部门
     * @param numbers {@link PmsDepartmentNumberEnum#getNumber()}
     */
    List<PmsDepartment> getDepartmentByNumbers(List<String> numbers);

    /**
     * 创建部门
     */
    void createDepartment(PmsDepartment pmsDepartment) throws BizException;

    /**
     * 更新部门
     */
    void updateDepartment(PmsDepartment pmsDepartment) throws BizException;

    /**
     * 删除部门
     */
    void deleteDepartmentById(long id) throws BizException;

    /**
     * 查询未设置负责人的部门
     * @param departmentName    部门名称，可为 null
     */
    List<PmsDepartment> listWithoutLeader(String departmentName);

    /**
     * 为部门设置负责人
     */
    void assignLeader(long departmentId, long leaderId) throws BizException;

    /**
     * 获取子级部门id，包括传进来的id
     * @param id
     * @return
     */
    List<Long> listSubDepartment(Long id);

    /**
     * 是否为子级部门或同部门
     * @param parentId 父级部门id
     * @param subId 子级部门id
     * @return
     */
    boolean isSubOrSameDepartment(Long parentId, Long subId);
}
