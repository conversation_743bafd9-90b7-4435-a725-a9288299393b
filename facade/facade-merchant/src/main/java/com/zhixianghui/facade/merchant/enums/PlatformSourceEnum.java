package com.zhixianghui.facade.merchant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 审批流程来源
 * @date 2020-08-11 10:51
 **/
@AllArgsConstructor
@Getter
@ToString
public enum PlatformSourceEnum {
    /**
     *运营平台
     */
    OPERATION(1000, "运营平台"),

    /**
     *商户平台
     */
    MERCHANT(1001, "商户平台"),
    /**
     *供应商后台
     */
    SUPPLIER(1002, "供应商后台"),
    /**
     *合伙人后台
     */
    AGENT(1003, "合伙人后台"),

    ;
    private final int value;
    private final String desc;
}
