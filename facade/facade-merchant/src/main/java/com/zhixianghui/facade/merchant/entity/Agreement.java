package com.zhixianghui.facade.merchant.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 协议表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Data
public class Agreement implements Serializable {

    private static final long serialVersionUID = -4819690771312198477L;

    private Long id;

    private Integer version = 0;

    /**
     * 创建时间
     */
    protected Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime = new Date();

    /**
     * 任务主题
     */
    private String topic;

    /**
     * 创建者
     */
    private String createOperator;

    /**
     * 更新者
     */
    private String updateOperator;

    /**
     * 销售ID
     */
    private Long salerId;

    /**
     * 销售人名
     */
    private String salerName;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 协议过期时间
     */
    private Date expireTime;

    /**
     * 签约截止时间
     */
    private Date deadline;

    /**
     * 签署人名称(全)
     */
    private String signerName;

    /**
     * 协议状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 预留json
     */
    private String extInfo;

    /**
     * 签署类型
     */
    private Integer signType;


    /**
     * 签署模式
     */
    private Integer signMode;

    /**
     * 文件模板id
     */
    private String fileTemplateId;

    /**
     * 流程id
     */
    private String flowId;

    /**
     * 协议编号
     */
    private String agreementNo;

    /**
     * 失败原因
     */
    private String errorMsg;
}
