package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 城市库
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Regions implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String name;

    private Integer parentId;

    private String shortName;

    private Integer level;

    /**
     * 区号
     */
    private String cityCode;

    /**
     * 邮政
     */
    private String zipCode;

    private String mergerName;

    /**
     * 纬度
     */
    private String lng;

    /**
     * 经度
     */
    private String lat;

    /**
     * 拼音
     */
    private String pinyin;

    private Date createdAt;

    private Date updatedAt;


}
