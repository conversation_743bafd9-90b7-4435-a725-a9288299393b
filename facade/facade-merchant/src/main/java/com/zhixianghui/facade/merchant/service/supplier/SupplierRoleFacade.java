package com.zhixianghui.facade.merchant.service.supplier;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierRoleVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台角色表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
public interface SupplierRoleFacade {

    /**
     * 创建供应商后台角色
     * @param role 角色
     */
    void create(SupplierRole role);

    /**
     * 根据id查询供应商后台角色
     * @param mchNo 商户编号
     * @param id
     */
    SupplierRole getById(String mchNo, long id);

    /**
     * 更新供应商后台角色
     * @param role 角色
     */
    void update(SupplierRole role) throws BizException;

    /**
     * 根据id删除供应商后台角色
     * @param mchNo     商户编号
     * @param id        角色id
     */
    void deleteById(String mchNo, long id) throws BizException;

    /**
     * 为角色分配功能
     * @param mchNo           商户编号
     * @param roleId        角色id
     * @param functionIds   功能id
     */
    void updateFunction(String mchNo, long roleId, List<Long> functionIds) throws BizException;

    /**
     * 根据角色id获取其关联的功能
     * @param mchNo       商户编号
     * @param roleId    角色id
     */
    List<SupplierFunction> listFunctionByRoleId(String mchNo, long roleId) throws BizException;

    /**
     * 根据角色id获取其关联的操作员
     * @param mchNo       商户编号
     * @param roleId    角色id
     */
    List<SupplierStaffVO> listStaffByRoleId(String mchNo, long roleId) throws BizException;

    /**
     * 查询所有角色
     */
    List<SupplierRole> listAll(String mchNo);

    /**
     * 分页查询角色
     *
     * @param mchNo
     * @param paramMap
     * @param pageParam
     */
    PageResult<List<SupplierRoleVo>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 分页查询预置角色
     *
     * @param paramMap
     * @param pageParam
     */
    PageResult<List<SupplierRoleVo>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    Long count(SupplierRoleVo supplierRoleVo, String mchNo);
}
