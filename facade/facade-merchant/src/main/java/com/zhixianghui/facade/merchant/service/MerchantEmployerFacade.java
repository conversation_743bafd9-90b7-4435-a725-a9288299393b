package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 用工企业商户信息接口
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantEmployerFacade {

    /**
     * 获取详情信息
     * @param param
     * @param salerIds
     * @return
     */
    MerchantEmployerDetailVo getInfoVO(Map<String,Object> param, List<Long> salerIds);

    /**
     * 校验主体认证信息
     * @param authVo
     * @throws BizException
     */
    void validMainAuthVo(MerchantEmployerMainAuthVo authVo) throws BizException;

    List<MerchantEmployerDetailVo> getInfoVoList(Map<String,Object> param);

    void applyMerchant(MerchantEmployerAddVo vo);

    Map<String, Object> getCooperateInfo(String mchNo);

    Map<String, Object> getCooperateInfoWithSupplier(String mainstayNo,String mchNo);

    void appleMerchantV2(MerchantEmployerAddVoV2 v2);
}
