package com.zhixianghui.facade.merchant.service;


import com.zhixianghui.facade.merchant.entity.AgreementSigner;

import java.util.List;

/**
 * 协议签署人表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */

public interface AgreementSignerFacade {
    /**
     * 根据协议Id获取协议签署人
     * @param AgreementId 协议Id
     * @return 协议签署人列表
     */
    List<AgreementSigner> listByAgreementId(Long AgreementId);

    AgreementSigner getBySignNoAndId(String mchNo, Long id);

    List<AgreementSigner> listByAgreementIdWithSignUrl(Long id);
}
