package com.zhixianghui.facade.merchant.service.agent;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentStaff;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 供应商后台员工表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
public interface AgentStaffFacade {

    AgentStaff getByAgentNoAndPhone(String agentNo,String phone);

    /**
     * 根据id查询供应商员工
     * @param agentNo     商户编号
     * @param id        员工id
     */
    AgentStaffVO getById(String agentNo, long id);

    /**
     * 根据手机号查询供应商员工
     * @param agentNo		商户编号
     * @param phone		手机号
     */
    AgentStaffVO getByPhone(String agentNo, String phone);

    /**
     * 根据操作员id查询其关联的员工
     * @param id
     * @return
     */
    List<AgentStaffVO> listByOperatorId(long id);

    /**
     * 根据手机号查询其关联的员工
     * @param phone
     * @return
     */
    List<AgentStaffVO> listByPhone(String phone);

    /**
     * 获取超级管理员
     * @param agentNo     商户编号
     * @return
     */
    AgentStaffVO getAdmin(String agentNo) throws BizException;

    /**
     * 创建供应商员工
     * @param agentStaffVo   vo
     * @return                  员工id
     */
    long create(AgentStaffVO agentStaffVo) throws BizException;

    /**
     * 创建供应商员工并分配指定角色
     * @param agentStaffVo   vo
     * @param roleIds           角色id
     * @return                  员工id
     */
    long createAndAssignRole(AgentStaffVO agentStaffVo, List<Long> roleIds) throws BizException;

    /**
     * 更换超级管理员
     * @param agentNo             商户编号
     * @param newAdminPhone     新负责人手机号
     * @param newAdminName      新负责人姓名
     */
    void changeAdmin(String agentNo, String newAdminPhone, String newAdminName, String updator) throws BizException;

    /**
     * 为供应商员工更新角色
     * @param agentNo     商户编号
     * @param staffId   员工id
     * @param roleIds   角色id
     * @param roleIds   员工姓名
     */
    void updateRole(String agentNo, long staffId, List<Long> roleIds,String name) throws BizException;

    /**
     * 根据id删除供应商员工
     * @param agentNo     商户编号
     * @param id        员工id
     */
    void deleteById(String agentNo, long id);

    /**
     * 根据员工id获取其关联的角色
     * @param agentNo     商户编号
     * @param staffId   员工id
     */
    List<AgentRole> getRoleByStaffId(String agentNo, long staffId) throws BizException;

    /**
     * 查询员工所关联的功能
     * @param agentNo     商户编号
     * @param staffId        员工id
     */
    List<AgentFunction> listFunctionByStaffId(String agentNo, long staffId) throws BizException;

    /**
     * 分页查询员工
     *
     * @param paramMap
     * @param pageParam
     */
    PageResult<List<AgentStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam);


    /**
     * 分页查询员工
     * @param agentNo
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<AgentStaffVO>> listPage(String agentNo, Map<String, Object> paramMap, PageParam pageParam);

    boolean isAdmin(AgentStaffVO agentStaffVO);

    List<AgentFunction> listAllFunction();

    void getAndPutStaffCache(Long id);

    void update(AgentStaff agentStaff);

    void updateExtraInfoByAgentNo(AgentStaffVO agentStaff);
}
