package com.zhixianghui.facade.merchant.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合伙人基本信息表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Agent extends BaseEntity {

    private static final long serialVersionUID = 1630752895832620619L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 邀请人编号
     */
    private String inviterNo;

    /**
     * 邀请人名称
     */
    private String inviterName;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人状态
     */
    private Integer agentStatus;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 认证状态
     */
    private Integer authStatus;

    /**
     * 邀请数
     */
    private Integer invitationNum = 0;

    /**
     * 直属商户数
     */
    private Integer merNum = 0;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 备注
     */
    private String remark;

    /**
     * json字段
     */
    private String jsonInfo;
    /**
     * 代扣税比例
     */
    private BigDecimal withholdingTaxRatio;

    /**
     * 是否自行申报, 1是,0否
     */
    private Integer selfDeclared;

    /**
     * 创始人
     */
    private String founder;
}
