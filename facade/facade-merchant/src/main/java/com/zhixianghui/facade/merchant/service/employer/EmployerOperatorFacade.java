package com.zhixianghui.facade.merchant.service.employer;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerOperator;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;

import java.util.List;
import java.util.Map;

/**
 * 用工企业操作员接口
 *
 * <AUTHOR> <PERSON>
 */
public interface EmployerOperatorFacade{

    /**
     * 根据id获取操作员
     */
    EmployerOperator getById(long id);

    /**
     * 根据手机号码获取操作员
     */
    EmployerOperator getByPhone(String phone);

    /**
     * 根据id删除操作员
     * @param id
     */
    void deleteById(long id) throws BizException;

    /**
     * 更新操作员
     */
    void update(EmployerOperator operator) throws BizException;

    /**
     * 分页查询操作员
     */
    PageResult<List<EmployerOperatorVO>> listPage(Map<String, Object> paramMap, PageParam pageParam);
}
