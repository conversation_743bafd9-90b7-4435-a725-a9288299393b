package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合伙人证件表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentCredential extends BaseEntity {

    private static final long serialVersionUID = -7723075311076128243L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 证件姓名
     */
    private String name;

    /**
     * 证件类型
     */
    private Integer certificateType;

    /**
     * 证件号
     */
    private String certificateNumber;

    /**
     * 证件有效期起
     */
    private String certificateTermBegin;

    /**
     * 证件有效期止
     */
    private String certificateTermEnd;

    /**
     * 证件有效期类型
     */
    private Integer certificateValidityDateType;


}
