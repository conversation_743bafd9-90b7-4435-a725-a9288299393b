package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MerchantEmployerQuotePosition implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer version;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 报价单id
     */
    private Long quoteId;

    /**
     * 岗位信息id
     */
    private Long positionId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
