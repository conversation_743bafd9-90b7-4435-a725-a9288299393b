package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 商户银行账户表
 * <AUTHOR>
 */
@Data
public class MerchantBankAccount extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 更新人登录名
	 */
	private String updator;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 银行账户
	 */
	private String accountNo;
	/**
	 * 银行账户名
	 */
	private String accountName;

	/**
	 * 银行名称
	 */
	private String bankName;

	/**
	 * 银行行号
	 */
	private String bankChannelNo;

	/**
	 * 支付宝账号
	 */
	private String alipayAccountNo;
}
