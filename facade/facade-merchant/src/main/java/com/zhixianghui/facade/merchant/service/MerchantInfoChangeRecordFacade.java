package com.zhixianghui.facade.merchant.service;


import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;

import java.util.List;

/**
 * 商户信息变更记录表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-07-15
 */
public interface MerchantInfoChangeRecordFacade {

    <T> void record(T newInfo, T oldInfo, int source);

    List<MerchantInfoChangeRecord> list(Long flowId);

    List<MerchantInfoChangeRecord> list(String mchNo);

    MerchantInfoChangeRecord getLastChange(String mchNo);

    void deleteRecord(Long commonFlowId);

    void update(Long commonFlowId);
}
