package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;

import java.util.Map;

/**
 * 银行账户
 *
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantBankAccountFacade {


    /**
     * 获取详情信息
     *
     * @param mchNo
     * @return
     */
    MerchantBankAccount getByMchNo(String mchNo);

    /**
     * 更新信息
     *
     * @throws BizException
     */
    void update(MerchantBankAccount account) throws BizException;

    /**
     * 新增信息
     *
     * @throws BizException
     */
    void insert(MerchantBankAccount account) throws BizException;

    Map<String, Object> getAccountInfo(String mchNo);

    Map<String, Object> getAccountInfoOfMap(String mchNo);

    Map<String, Object> mainstayGetAccountInfo(String mainstayNo, String mchNo);
}
