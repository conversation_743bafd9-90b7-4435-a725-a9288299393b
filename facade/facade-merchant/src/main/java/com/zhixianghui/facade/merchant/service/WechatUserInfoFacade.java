package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.WechatUserInfo;
import com.zhixianghui.facade.merchant.vo.WxBindMobileReqVo;

public interface WechatUserInfoFacade {
    String getPhoneByOpenIdAndAppId(String openId, String appId);

    WechatUserInfo register(WxBindMobileReqVo wxBindMobileReqVo);

    WechatUserInfo getMiniUserByOpenIdAndAppId(String openId, String appId);
}
