package com.zhixianghui.facade.merchant.entity.user.pms;

import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作员操作日志表
 */
@Data
public class PmsOperateLog implements Serializable {

    private static final long serialVersionUID = 1L;

    //columns START
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 操作员登录名
     */
    private String operatorLoginName;

    /**
     * 操作类型
     *
     * @see OperateLogTypeEnum
     */
    private Integer operateType;

    /**
     * 来源
     * @see SystemTypeEnum
     */
    private Integer operateSource;

    /**
     * 操作状态（1:成功，-1:失败）
     *
     * @see PublicStatus
     */
    private Integer status;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 操作详细
     */
    private String detail;

    /**
     * 地址
     */
    private String address;

    //columns END
}
