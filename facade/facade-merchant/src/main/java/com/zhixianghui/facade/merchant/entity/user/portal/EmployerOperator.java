package com.zhixianghui.facade.merchant.entity.user.portal;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.facade.merchant.entity.user.FlowUserCache;
import com.zhixianghui.facade.merchant.enums.PlatformSourceEnum;
import lombok.Data;

import java.util.Date;

/**
 * 用工企业操作员表
 */
@Data
public class EmployerOperator extends FlowUserCache {

	private static final long serialVersionUID = 1L;

	private Long id;

	private Integer version;

	private String parentPermissionFlag;

	private Date createTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 状态
	 */
	private Integer status;

	/**
	 * 是否初始化密码
	 */
	private Integer isInitPwd;

	/**
	 * 随机盐
	 */
	private String salt;

	/**
	 * 登录密码
	 */
	private String pwd;

	/**
	 * 密码有效期
	 */
	private Date pwdValidTime;

	/**
	 * 最后修改密码时间
	 */
	private Date modPwdTime;

	/**
	 * 当前登录时间
	 */
	private Date curLoginTime;

	/**
	 * 上次登录时间
	 */
	private Date lastLoginTime;

	/**
	 * 连续输错密码次数
	 */
	private Integer pwdErrorCount;

	/**
	 * 最后输错密码时间
	 */
	private Date pwdErrorTime;

	/**
	 * 附加信息
	 */
	private Object extraInfo;


	/**
	 * 头像文件url
	 */
	private String headPortraitFileUrl;

	/**
	 * 动态私钥，不持久化
	 */
	private transient String privateKey;

	public EmployerOperator(){
		super.setCachePlatform(String.valueOf(PlatformSourceEnum.MERCHANT.getValue()));
	}

	//重写set方法
	public void setId(Long id){
		this.id = id;
		super.setCacheId(String.valueOf(id));
	}

	public void setName(String name){
		this.name = name;
		super.setCacheName(name);
	}

	public void setPhone(String phone){
		this.phone = phone;
		super.setCachePhone(phone);
	}
}
