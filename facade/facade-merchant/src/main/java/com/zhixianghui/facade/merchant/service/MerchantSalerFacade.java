package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;

import java.util.List;
import java.util.Map;

/**
 * 企业销售查询接口
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantSalerFacade {

    /**
     * 查询
     * @param mchNo
     * @return
     */
    MerchantSaler getByMchNo(String mchNo);

    List<MerchantSaler> getBatchMerchantSale(List<Long> saleId);

    void update(MerchantSaler toMerchantSaler) throws BizException;

    List<MerchantSaler> getBatchMerchantMchNO(List<String> mchNoList);

    MerchantSaler getOne(Long saleId);

    Map<String, Object> getSalerStatistics(Map<String,Object> thisMonth,Map<String,Object> lastMonth);
}
