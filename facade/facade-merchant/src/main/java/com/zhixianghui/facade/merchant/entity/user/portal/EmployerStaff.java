package com.zhixianghui.facade.merchant.entity.user.portal;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.facade.merchant.entity.user.FlowUserCache;
import com.zhixianghui.facade.merchant.enums.PlatformSourceEnum;
import lombok.Data;

import java.util.Date;

/**
 * 用工企业员工表
 */
@Data
public class EmployerStaff extends FlowUserCache {

	private static final long serialVersionUID = 1L;

	private Long id;

	private Integer version;

	private Date createTime;

	private String parentPermissionFlag;


	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 操作员id
	 */
	private Long operatorId;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 商户名称
	 */
	private String mchName;

	/**
	 * 员工类型
	 * @see com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum
	 */
	private Integer type;

	/**
	 * 创建者
	 */
	private String creator;

	/**
	 * 修改者
	 */
	private String updator;

	/**
	 * 附加信息
	 */
	private Object extraInfo;


	/**
	 * 员工姓名
	 */
	private String name;

	//重写无参构造方法
	public EmployerStaff(){
		//设置所属平台编号
		super.setCachePlatform(String.valueOf(PlatformSourceEnum.MERCHANT.getValue()));
	}

	//重写set方法，放入到父类中
	public void setName(String name){
		this.name = name;
		super.setCacheName(name);
	}

	public void setMchNo(String mchNo){
		this.mchNo = mchNo;
		super.setCacheNo(mchNo);
	}

	public void setId(Long id){
		this.id = id;
		super.setCacheId(String.valueOf(id));
	}

}
