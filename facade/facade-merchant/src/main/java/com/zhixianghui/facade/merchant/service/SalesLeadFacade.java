package com.zhixianghui.facade.merchant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.SalesLead;

public interface SalesLeadFacade {
    void save(SalesLead salesLead) throws BizException;

    void updateByCreateNo(SalesLead salesLead);

    IPage listByPage(IPage page, SalesLead salesLead);

    void update(SalesLead salesLead) throws BizException;

    SalesLead getById(Long id) throws BizException;
}
