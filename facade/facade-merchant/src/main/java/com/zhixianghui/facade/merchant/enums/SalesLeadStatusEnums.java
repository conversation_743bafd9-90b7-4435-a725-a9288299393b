package com.zhixianghui.facade.merchant.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="推荐客户状态",type = SystemTypeEnum.COMMON_MANAGEMENT)
public enum SalesLeadStatusEnums {


    DEAL_SUCCESS(100,"成功入驻"),

    DEAL_FAIL(101,"入驻失败"),
    CREATED(102,"已创建"),
    FOLLOWING(103,"跟进中"),

    ;

    private final Integer code;

    private final String desc;
}
