package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;

import java.util.List;

/**
 * 代征主体管理接口
 *
 * <AUTHOR>
 */
public interface MerchantMainstayFacade {

    /**
     * 创建代征主体
     * @param merchant  商户信息
     * @param saler     销售信息
     * @param fileList  文件列表
     */
    void createMainstay(Merchant merchant, MerchantSaler saler, List<MerchantFile> fileList);
}
