package com.zhixianghui.facade.merchant.entity;

import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-06-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tbl_personal_income_tax")
public class PersonalIncomeTax implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @Version
    private Integer version;

    /**
     * 起征点
     */
    private BigDecimal startPoint;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 速算扣除数
     */
    private BigDecimal deduction;

    /**
     * 级别
     */
    private Integer level;


    /**
     * 描述
     */
    private String description;

    @TableField(exist = false)
    private BigDecimal taxRatePct;
}
