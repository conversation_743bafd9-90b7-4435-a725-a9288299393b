package com.zhixianghui.facade.merchant.service.supplier;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 供应商后台员工表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
public interface SupplierStaffFacade {

    /**
     * 根据id查询供应商员工
     * @param mchNo     商户编号
     * @param id        员工id
     */
    SupplierStaffVO getById(String mchNo, long id);

    /**
     * 根据手机号查询供应商员工
     * @param mchNo		商户编号
     * @param phone		手机号
     */
    SupplierStaffVO getByPhone(String mchNo, String phone);

    /**
     * 根据操作员id查询其关联的员工
     * @param id
     * @return
     */
    List<SupplierStaffVO> listByOperatorId(long id);

    /**
     * 根据手机号查询其关联的员工
     * @param phone
     * @return
     */
    List<SupplierStaffVO> listByPhone(String phone);

    /**
     * 获取超级管理员
     * @param mchNo     商户编号
     * @return
     */
    SupplierStaffVO getAdmin(String mchNo) throws BizException;

    /**
     * 创建供应商员工
     * @param supplierStaffVo   vo
     * @return                  员工id
     */
    long create(SupplierStaffVO supplierStaffVo) throws BizException;

    /**
     * 创建供应商员工并分配指定角色
     * @param supplierStaffVo   vo
     * @param roleIds           角色id
     * @return                  员工id
     */
    long createAndAssignRole(SupplierStaffVO supplierStaffVo, List<Long> roleIds) throws BizException;

    /**
     * 更换超级管理员
     * @param mchNo             商户编号
     * @param newAdminPhone     新负责人手机号
     * @param newAdminName      新负责人姓名
     */
    void changeAdmin(String mchNo, String newAdminPhone, String newAdminName, String updator) throws BizException;

    /**
     * 为供应商员工更新角色
     * @param mchNo     商户编号
     * @param staffId   员工id
     * @param roleIds   角色id
     * @param roleIds   员工姓名
     */
    void updateRole(String mchNo, long staffId, List<Long> roleIds,String name) throws BizException;

    /**
     * 根据id删除供应商员工
     * @param mchNo     商户编号
     * @param id        员工id
     */
    void deleteById(String mchNo, long id);

    /**
     * 根据员工id获取其关联的角色
     * @param mchNo     商户编号
     * @param staffId   员工id
     */
    List<SupplierRole> getRoleByStaffId(String mchNo, long staffId) throws BizException;

    /**
     * 查询员工所关联的功能
     * @param mchNo     商户编号
     * @param staffId        员工id
     */
    List<SupplierFunction> listFunctionByStaffId(String mchNo, long staffId) throws BizException;

    /**
     * 分页查询员工
     *
     * @param paramMap
     * @param pageParam
     */
    PageResult<List<SupplierStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam);


    /**
     * 分页查询员工
     * @param mchNo
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<SupplierStaffVO>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam);

    boolean isAdmin(SupplierStaffVO attribute);

    List<SupplierFunction> listAllFunction();

    List<String> getDistinctStaffByRoleIdAndMainstayNo(String mainstayNo, List<Long> roleIds);

    void getAndPutStaffCache(Long id);

    void updateByMainStayNo(Merchant merchant,String currentOperator);
}
