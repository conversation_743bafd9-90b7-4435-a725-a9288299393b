package com.zhixianghui.facade.merchant.service.agent;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台操作员表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
public interface AgentOperatorFacade {

    /**
     * 根据id获取操作员
     */
    AgentOperator getById(long id);

    /**
     * 根据手机号码获取操作员
     */
    AgentOperator getByPhone(String phone);

    /**
     * 根据id删除操作员
     * @param id
     */
    void deleteById(long id) throws BizException;

    /**
     * 更新操作员
     */
    void update(AgentOperator operator) throws BizException;

    /**
     * 分页查询操作员
     */
    PageResult<List<AgentOperatorVO>> listPage(Map<String, Object> paramMap, PageParam pageParam);
}
