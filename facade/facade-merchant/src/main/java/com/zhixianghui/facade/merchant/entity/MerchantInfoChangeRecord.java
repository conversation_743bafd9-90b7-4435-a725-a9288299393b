package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商户信息变更记录表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MerchantInfoChangeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 流程ID
     */
    private Long businessId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 对象属性
     */
    private String objectKey;

    /**
     * 对象名称
     */
    private String objectName;

    /**
     * 原始字段值
     */
    private String originalValue;

    /**
     * 原始字段值
     */
    private String targetValue;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 操作者id
     */
    private Long operateId;

    /**
     * 操作者名称
     */
    private String operateName;

    /**
     * 来源, 走审批流程1, 否则0
     */
    private Integer source;

    /**
     * 操作,0修改,1删除
     */
    private Integer operate;


}
