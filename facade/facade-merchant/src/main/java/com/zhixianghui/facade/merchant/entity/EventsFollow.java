package com.zhixianghui.facade.merchant.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.Value;

/**
    * 动态追踪表
    */
@Data
@TableName(value = "tbl_events_follow")
public class EventsFollow implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 内容
     */
    @TableField(value = "EVENT_CONTENT")
    private String eventContent;

    /**
     * 动态类型
     */
    @TableField(value = "EVENT_TYPE")
    private Short eventType;

    /**
     * 动态类型描述
     */
    @TableField(value = "EVENT_TYPE_DESC")
    private String eventTypeDesc;

    /**
     * 商户编号
     */
    @TableField(value = "MCH_NO")
    private String mchNo;

    /**
     * 商户名称
     */
    @TableField(value = "MCH_NAME")
    private String mchName;

    /**
     * 商户类型
     */
    @TableField(value = "MCH_TYPE")
    private Short mchType;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    @Version
    private Integer version;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    @TableField(value="UPDATE_BY")
    private String updateBy;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_EVENT_CONTENT = "EVENT_CONTENT";

    public static final String COL_EVENT_TYPE = "EVENT_TYPE";

    public static final String COL_EVENT_TYPE_DESC = "EVENT_TYPE_DESC";

    public static final String COL_MCH_NO = "MCH_NO";

    public static final String COL_MCH_NAME = "MCH_NAME";

    public static final String COL_MCH_TYPE = "MCH_TYPE";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_UPDATE_BY = "UPDATE_BY";
}