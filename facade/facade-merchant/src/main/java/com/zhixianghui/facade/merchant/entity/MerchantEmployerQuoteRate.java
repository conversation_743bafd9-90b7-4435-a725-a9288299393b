package com.zhixianghui.facade.merchant.entity;

import java.math.BigDecimal;
import java.io.Serializable;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MerchantEmployerQuoteRate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 报价单ID
     */
    private Long quoteId;

    /**
     * 特殊计费规则
     */
    private String ruleParam;

    /**
     * 公式类型
     */
    private Integer formulaType;

    /**
     * 固定手续费
     */
    private BigDecimal fixedFee;

    /**
     * 费率
     */
    private BigDecimal rate;

    /**
     * 描述
     */
    private String description;


}
