package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.AgreementTemplate;

import java.util.List;
import java.util.Map;

/**
 * 协议模板表 Facade类
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */

public interface AgreementTemplateFacade {
    /**
     * 创建协议模板
     * @param agreementTemplate 协议模板
     */
    void createAgreementTemplate(AgreementTemplate agreementTemplate);

    /**
     * 更新协议模板
     * @param agreementTemplate 协议模板
     */
    void updateAgreementTemplate(AgreementTemplate agreementTemplate);

    /**
     * 分页查询协议模板
     * @param paramMap 查询参数
     * @param pageParam 分页参数
     * @return 协议模板
     */
    PageResult<List<AgreementTemplate>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 根据id获取协议模板
     * @param id 模板id
     * @return 模板
     */
    AgreementTemplate getById(Long id);

    List<AgreementTemplate> listAll();

    void deleteAll();
}
