package com.zhixianghui.facade.merchant.service.pms;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog;

import java.util.List;
import java.util.Map;

/**
 * Author: Cmf
 * Date: 2019/11/1
 * Time: 15:29
 * Description:
 */
public interface PmsOperateLogFacade {

    void createOperateLog(PmsOperateLog operateLog);

    PmsOperateLog createOperateLogGetkey(PmsOperateLog operateLog);

    PageResult<List<PmsOperateLog>> listOperateLogPage(Map<String, Object> paramMap, PageParam pageParam);

    PmsOperateLog getOperateLogById(Long id);

    void updateOperateLog(PmsOperateLog operateLog);
}
