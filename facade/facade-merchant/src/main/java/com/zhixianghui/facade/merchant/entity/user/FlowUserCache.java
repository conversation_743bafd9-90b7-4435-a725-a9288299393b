package com.zhixianghui.facade.merchant.entity.user;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Maps;
import com.zhixianghui.common.util.utils.StringUtil;
import lombok.Data;
import org.apache.commons.beanutils.BeanUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName OperatorCache
 * @Description 用户缓存类，流程引擎使用
 * @Date 2021/5/24 9:18
 */
@Data
public class FlowUserCache implements Serializable {

    private static final long serialVersionUID = -9029154239803378011L;

    private String cacheName;

    private String cacheId;

    private String cacheNo;

    private String cachePlatform;

    private String cachePhone;

    /**
     * 缓存Map
     * @return
     */
    @JsonIgnore
     public Map<String,String> getCacheMap(){
        Map<String,String> map = new HashMap<>();
        map.put("name",StringUtil.isEmpty(cacheName)? "" : cacheName);
        map.put("id",cacheId);
        map.put("no", StringUtil.isEmpty(cacheNo)? "": cacheNo);
        map.put("platform",cachePlatform);
        map.put("phone",StringUtil.isEmpty(cachePhone)? "": cachePhone);
        return map;
    }

}
