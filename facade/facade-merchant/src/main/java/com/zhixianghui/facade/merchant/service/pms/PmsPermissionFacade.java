package com.zhixianghui.facade.merchant.service.pms;


import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRole;

import java.util.List;
import java.util.Map;

/**
 * Author: Cmf
 * Date: 2019/10/10
 * Time: 11:31
 * Description:管理菜单和权限
 */
public interface PmsPermissionFacade {

    //<editor-fold desc="功能点管理">
    void createFunction(PmsFunction pmsFunction) throws BizException;

    /**
     * 删除Function以及其子Function,同时也会删除与这些function相关的role_function映射关联
     * 若存在菜单类的子Function，该方法会抛出异常
     *
     * @param functionId .
     */
    void deleteFunctionById(Long functionId) throws BizException;

    void updateFunction(PmsFunction pmsFunction);

    List<PmsFunction> listFunctionByOperatorId(Long operatorId) throws BizException;

    List<PmsFunction> listAllFunction(Map<String, Object> param, PageParam pageParam);
    List<PmsFunction> listAllFunction();

    List<Long> listFunctionIdsByRoleId(Long roleId);

    List<PmsFunction> listFunctionByRoleId(Long roleId);

    List<PmsFunction> listFunctionByParentId(Long id);

    PmsFunction getFunctionById(Long id);

    PmsFunction getFunctionWithParentInfo(Long id);
    //</editor-fold>


    //<editor-fold desc="角色管理">

    void createRole(PmsRole pmsRole) throws BizException;

    void deleteRoleById(Long id) throws BizException;

    void updateRole(PmsRole pmsRole) throws BizException;

    List<PmsRole> listAllRoles();

    PageResult<List<PmsRole>> listRolePage(Map<String, Object> paramMap, PageParam pageParam);

    List<PmsRole> listRolesByOperatorId(Long operatorId);

    PmsRole getRoleById(Long id);

    PmsRole getRoleByName(String roleName);

    void assignPermission(Long roleId, List<Long> functionIds) throws BizException;

    void export(PmsOperator operator);

    /**
     * 批量保存菜单
     * @param list 菜单列表
     */
    void saveFunction(List<PmsFunction> list);

    /**
     * 计算当前角色有多少名员工
     * @param roleId 角色id
     * @return
     */
    Long countEmployerByRoleId(Long roleId);

    String getPermissionFlag(Long parentId);

    PmsFunction getFunctionByFlag(String permissionFlag);

    //</editor-fold>


}
