package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;

/**
 * 商户开票信息接口
 *
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantInvoiceInfoFacade {

    /**
     * 根据商户编号查询
     *
     * @param mchNo 商户编码
     * @return
     */
    MerchantInvoiceInfo getByMchNo(String mchNo);

    /**
     * 更新开票信息
     *
     * @param invoiceInfo 开票信息
     */
    void update(MerchantInvoiceInfo invoiceInfo);

    void insert(MerchantInvoiceInfo invoiceInfo);
}
