package com.zhixianghui.facade.merchant.service.supplier;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.vo.FunctionVO;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台功能表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
public interface SupplierFunctionFacade {

    /**
     * 创建供应商后台功能
     * @param function  功能
     */
    void create(SupplierFunction function) throws BizException;

    /**
     * 根据id查询供应商后台功能
     * @param id
     */
    SupplierFunction getById(long id);

    /**
     * 编辑供应商后台功能
     * @param function  功能
     */
    void update(SupplierFunction function) throws BizException;

    /**
     * 根据id删除功能，并删除该功能与角色的映射
     * @param id
     */
    void deleteById(long id) throws BizException;

    /**
     * 查询所有功能
     */
    List<SupplierFunction> listAll();

    List<SupplierFunction> listAll(Map<String, Object> param, PageParam pageParam);


    /**
     * 导出功能
     * @param employerFunction
     */
    void export(FunctionVO employerFunction);

    /**
     *  批量插入
     * @param list
     */
    void saveFunction(List<SupplierFunction> list);

    String getPermissionFlag(Long parentId);
}
