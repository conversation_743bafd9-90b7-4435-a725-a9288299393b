package com.zhixianghui.facade.merchant.service.employer;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.vo.EmployerCooperateUpdateVo;

import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.merchant.vo.FunctionVO;

import java.util.List;
import java.util.Map;

/**
 * 用工企业员工接口
 *
 * <AUTHOR> <PERSON>
 */
public interface EmployerStaffFacade{

    /**
     * 根据id查询用工企业员工
     * @param mchNo     商户编号
     * @param id        员工id
     */
    EmployerStaffVO getById(String mchNo, long id);

    /**
     * 根据手机号查询用工企业员工
     * @param mchNo		商户编号
     * @param phone		手机号
     */
    EmployerStaffVO getByPhone(String mchNo, String phone);

    /**
     * 根据操作员id查询其关联的员工
     * @param id
     * @return
     */
    List<EmployerStaffVO> listByOperatorId(long id);

    /**
     * 根据手机号查询其关联的员工
     * @param phone
     * @return
     */
    List<EmployerStaffVO> listByPhone(String phone);

    /**
     * 获取超级管理员
     * @param mchNo     商户编号
     * @return
     */
    EmployerStaffVO getAdmin(String mchNo) throws BizException;

    /**
     * 创建用工企业员工
     * @param employerStaffVo   vo
     * @return                  员工id
     */
    long create(EmployerStaffVO employerStaffVo) throws BizException;

    /**
     * 创建用工企业员工并分配指定角色
     * @param employerStaffVo   vo
     * @param roleIds           角色id
     * @return                  员工id
     */
    long createAndAssignRole(EmployerStaffVO employerStaffVo, List<Long> roleIds) throws BizException;

    /**
     * 更换超级管理员
     * @param mchNo             商户编号
     * @param newAdminPhone     新负责人手机号
     * @param newAdminName      新负责人姓名
     */
    void changeAdmin(String mchNo, String newAdminPhone, String newAdminName, String updator) throws BizException;

    /**
     * 为用工企业员工更新角色
     * @param mchNo     商户编号
     * @param staffId   员工id
     * @param roleIds   角色id
     * @param roleIds   员工姓名
     */
    void updateRole(String mchNo, long staffId, List<Long> roleIds,String name) throws BizException;

    /**
     * 根据id删除用工企业员工
     * @param mchNo     商户编号
     * @param id        员工id
     */
    void deleteById(String mchNo, long id);

    /**
     * 根据员工id获取其关联的角色
     * @param mchNo     商户编号
     * @param staffId   员工id
     */
    List<EmployerRole> getRoleByStaffId(String mchNo, long staffId) throws BizException;

    /**
     * 查询员工所关联的功能
     * @param mchNo     商户编号
     * @param staffId        员工id
     */
    List<EmployerFunction> listFunctionByStaffId(String mchNo, long staffId) throws BizException;

    /**
     * 分页查询员工
     *
     * @param paramMap
     * @param pageParam
     */
    PageResult<List<EmployerStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam);


    /**
     * 分页查询员工
     * @param mchNo
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<EmployerStaffVO>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 根据商户号更新
     * @param vo 商户合作信息
     */
    void updateByMchNo(EmployerCooperateUpdateVo vo);

    /**
     * 根据角色id查询员工数量
     * @param roleId 角色id
     * @return 员工数量
     */
    Long countEmployerCount(Long roleId);

    boolean isAdmin(EmployerStaffVO employerStaffVO);

    List<EmployerFunction> listAllFunction();

    void getAndPutStaffCache(Long id);

    List<String> getDistinctStaffByRoleIdAndMchNo(String mchNo, List<Long> roleIds);
}
