package com.zhixianghui.facade.merchant.service.pms;


import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Author: Cmf
 * Date: 2019/10/10
 * Time: 11:14
 * Description:
 */
public interface PmsOperatorFacade {

    PmsOperator getOperatorById(long id);

    PmsOperator getOperatorByLoginName(String loginName);

    void deleteOperatorById(long id) throws BizException;

    void updateOperator(PmsOperator operator);

    void updateOperatorAndAssignRoles(PmsOperator operator, List<Long> roleIds) throws BizException;

	void insertOperatorAndAssignRoles(PmsOperator operator, List<Long> roleIds);

    void updateOperatorPwd(Long operatorId, String newPwd, boolean isChangedPwd) throws BizException;

    PageResult<List<PmsOperator>> listOperatorPage(Map<String, Object> paramMap, PageParam pageParam);

    List<PmsOperator> listBy(Map<String,Object> paramMap);

    /**
     * 查询部门负责人管理的员工
     * @param leaderId      负责人 id
     */
    List<PmsOperator> listByLeaderId(long leaderId) throws BizException;

    /**
     * 查询部门负责人管理的员工，并且递归查询下级部门
     * @param leaderId      负责人 id
     */
    List<PmsOperator> listByLeaderIdRecursive(long leaderId) throws BizException;

    /**
     * 根据部门id查询员工
     * @param departmentId  部门 id
     */
    List<PmsOperator> listByDepartmentId(long departmentId);

    /**
     * 根据部门id查询员工，并且递归查询下级部门
     * @param departmentId  部门 id
     */
    List<PmsOperator> listByDepartmentIdRecursive(long departmentId);

    /**
     * 根据部门编号查询员工
     * @param number    {@link PmsDepartmentNumberEnum#getNumber()}
     */
    List<PmsOperator> listByDepartmentNumber(String number);

    List<PmsOperator> listActiveByDepartmentNumber(String num);

    /**
     * 根据部门编号查询员工，并且递归查询下级部门
     * @param number    {@link PmsDepartmentNumberEnum#getNumber()}
     */
    List<PmsOperator> listByDepartmentNumberRecursive(String number);

    /**
     * 查询未配置所属部门的员工
     */
    List<PmsOperator> listWithoutDepartment();

    /**
     * 为员工配置所属部门
     * @param id            员工 id
     * @param departmentId  部门 id
     */
    void assignDepartment(long id, long departmentId) throws BizException;

    /**
     * 获取销售员工, 对于销售领导来说, 可以获取所有下属, 对于普通销售, 只能获取到自己的
     * @param id
     * @return
     */
    List<PmsOperator> getSale(Long id);

    List<PmsRoleOperator> listOperatorByRoleId(Integer roleId);

    /**
     *
     * @param id 操作员id
     * @return 是否为管理员角色
     */
    boolean isAdmin(Long id);

    /**
     *
     * @return 销售角色
     */
    Long getSaleRole();

    void getAndPutOperatorCache(Long id);

}
