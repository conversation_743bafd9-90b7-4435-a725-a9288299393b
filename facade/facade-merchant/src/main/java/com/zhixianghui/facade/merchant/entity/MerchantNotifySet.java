package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2023-07-05
 */
@Data
@TableName(value = "tbl_merchant_notify_set")
@EqualsAndHashCode(callSuper = false)
public class MerchantNotifySet implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 通知类型
     */
    private Integer notifyType;

    /**
     * 通知地址类型
     */
    private String notifyUrlType;

    /**
     * 通知地址
     */
    private String notifyUrl;

    /**
     * 启用状态
     */
    private Integer notifyStatus;


}
