package com.zhixianghui.facade.merchant.entity.user.supplier;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台角色功能关联表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierRoleFunction extends BaseEntity {

    private static final long serialVersionUID = 1476113901187554221L;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 功能id
     */
    private Long functionId;


}
