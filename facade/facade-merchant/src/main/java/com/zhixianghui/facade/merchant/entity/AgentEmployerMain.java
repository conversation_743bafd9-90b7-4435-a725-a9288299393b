package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合伙人-企业主体信息表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentEmployerMain extends BaseEntity {

    private static final long serialVersionUID = -756486467984697587L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 企业简称
     */
    private String shortName;

    /**
     * 税号/社会信用代码
     */
    private String taxNo;

    /**
     * 注册地址-省
     */
    private String registerAddrProvince;

    /**
     * 注册地址-市
     */
    private String registerAddrCity;

    /**
     * 注册地址-区/镇
     */
    private String registerAddrTown;

    /**
     * 注册地址-详细地址
     */
    private String registerAddrDetail;

    /**
     * 经营范围
     */
    private String managementScope;

    /**
     * 营业时间起
     */
    private String managementTermBegin;

    /**
     * 营业时间止
     */
    private String managementTermEnd;

    /**
     * 营业执照有效期类型
     */
    private Integer managementValidityDateType;


}
