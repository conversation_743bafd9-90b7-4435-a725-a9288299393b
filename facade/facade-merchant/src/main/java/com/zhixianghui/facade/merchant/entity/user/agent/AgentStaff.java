package com.zhixianghui.facade.merchant.entity.user.agent;

import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.facade.merchant.entity.user.FlowUserCache;
import com.zhixianghui.facade.merchant.enums.PlatformSourceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台员工表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentStaff extends FlowUserCache {

    private static final long serialVersionUID = -5932858100896906241L;

    private Long id;

    private Integer version;

    private Date createTime;

    private String parentPermissionFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作员id
     */
    private Long operatorId;

    /**
     * 商户编号
     */
    private String agentNo;

    /**
     * 商户名称
     */
    private String agentName;

    /**
     * 员工类型
     */
    private Integer type;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String updator;

    /**
     * 附加信息
     */
    private String extraInfo;

    /**
     * 员工姓名
     */
    private String name;

    //重写无参构造方法
    public AgentStaff(){
        //设置所属平台编号
        super.setCachePlatform(String.valueOf(PlatformSourceEnum.AGENT.getValue()));
    }

    //重写set方法，放入到父类中
    public void setName(String name){
        this.name = name;
        super.setCacheName(name);
    }

    public void setAgentNo(String agentNo){
        this.agentNo = agentNo;
        super.setCacheNo(agentNo);
    }

    public void setId(Long id){
        this.id = id;
        super.setCacheId(String.valueOf(id));
    }
}
