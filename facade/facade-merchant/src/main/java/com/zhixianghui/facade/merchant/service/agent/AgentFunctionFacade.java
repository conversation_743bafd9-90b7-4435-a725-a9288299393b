package com.zhixianghui.facade.merchant.service.agent;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.vo.FunctionVO;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台功能表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
public interface AgentFunctionFacade {

    /**
     * 创建供应商后台功能
     * @param function  功能
     */
    void create(AgentFunction function) throws BizException;

    /**
     * 根据id查询供应商后台功能
     * @param id
     */
    AgentFunction getById(long id);

    /**
     * 编辑供应商后台功能
     * @param function  功能
     */
    void update(AgentFunction function) throws BizException;

    /**
     * 根据id删除功能，并删除该功能与角色的映射
     * @param id
     */
    void deleteById(long id) throws BizException;

    /**
     * 查询所有功能
     */
    List<AgentFunction> listAll();
    List<AgentFunction> listAll(Map<String, Object> param, PageParam pageParam);

    /**
     * 导出所有功能
     * @param employerFunction
     */
    void export(FunctionVO employerFunction);

    /**
     *
     * @param list 合伙人功能列表
     */
    void saveFunction(List<AgentFunction> list);

    /**
     * 获取权限标识
     * @param parentId
     * @return
     */
    String getPermissionFlag(Long parentId);
}
