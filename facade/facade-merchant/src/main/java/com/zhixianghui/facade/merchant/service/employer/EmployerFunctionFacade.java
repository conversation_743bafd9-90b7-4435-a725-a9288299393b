package com.zhixianghui.facade.merchant.service.employer;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.vo.FunctionVO;

import java.util.List;
import java.util.Map;

/**
 * 用工企业功能接口
 *
 * <AUTHOR> G<PERSON>ng
 */
public interface EmployerFunctionFacade{

    /**
     * 创建用工企业后台功能
     * @param function  功能
     */
    void create(EmployerFunction function) throws BizException;

    /**
     * 根据id查询用工企业后台功能
     * @param id
     */
    EmployerFunction getById(long id);

    /**
     * 编辑用工企业后台功能
     * @param function  功能
     */
    void update(EmployerFunction function) throws BizException;

    /**
     * 根据id删除功能，并删除该功能与角色的映射
     * @param id
     */
    void deleteById(long id) throws BizException;

    /**
     * 查询所有功能
     */
    List<EmployerFunction> listAll();

    List<EmployerFunction> listAll(Map<String, Object> param, PageParam pageParam);


    /**
     * 导出功能
     * @param employerFunction
     */
    void export(FunctionVO employerFunction);

    /**
     *
     * @param param 带查询参数
     * @param pageParam 分页参数
     * @return 功能列表
     */
    PageResult<List<EmployerFunction>> listPage(Map<String, Object> param, PageParam pageParam);

    /**
     * 批量保存功能菜单
     * @param list
     */
    void saveFunction(List<EmployerFunction> list);

    String getPermissionFlag(Long parentId);
}
