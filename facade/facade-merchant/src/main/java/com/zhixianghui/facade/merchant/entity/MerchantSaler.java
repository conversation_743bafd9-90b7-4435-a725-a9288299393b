package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 商户销售信息表
 * <AUTHOR>
 */
@Data
public class MerchantSaler extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 更新人登录名
	 */
	private String updator;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 商户类型
	 * @see com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum
	 */
	private Integer merchantType;

	/**
	 * 销售用户id
	 */
	private Long salerId;

	/**
	 * 销售名称
	 */
	private String salerName;

	/**
	 * 销售部门id
	 */
	private Long saleDepartmentId;

	/**
	 * 销售部门名称
	 */
	private String saleDepartmentName;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 其他信息，json格式
	 */
	private String jsonInfo;

}
