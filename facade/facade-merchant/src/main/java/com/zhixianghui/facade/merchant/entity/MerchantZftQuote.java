package com.zhixianghui.facade.merchant.entity;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2023-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tbl_merchant_zft_quote")
public class MerchantZftQuote implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    private Integer version;

    private Long quoteId;

    private BigDecimal fixedFee;

    private Integer formulaType;

    private BigDecimal rate;

    private Integer canRefund;

    private Date createTime;

    private Date updateTime;
}
