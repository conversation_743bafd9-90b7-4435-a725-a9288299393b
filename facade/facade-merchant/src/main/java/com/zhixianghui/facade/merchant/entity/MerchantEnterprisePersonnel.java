package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-06-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MerchantEnterprisePersonnel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证
     */
    private String idCardNumber;

    /**
     * 职位
     */
    private String position;

    /**
     * 是否为法人
     */
    private Boolean isLegal;

    /**
     * 创建时间
     */
    private Date createTime;


}
