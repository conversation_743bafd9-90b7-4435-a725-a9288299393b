package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentSaler extends BaseEntity {

    private static final long serialVersionUID = -88917653557434085L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 销售用户id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 销售部门id
     */
    private Long saleDepartmentId;

    /**
     * 销售部门名称
     */
    private String saleDepartmentName;

    /**
     * 备注
     */
    private String remark;

    /**
     * json
     */
    private String jsonInfo;


}
