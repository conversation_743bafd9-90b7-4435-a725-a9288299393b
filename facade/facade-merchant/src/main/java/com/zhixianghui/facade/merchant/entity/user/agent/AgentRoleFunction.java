package com.zhixianghui.facade.merchant.entity.user.agent;

import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台角色功能关联表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentRoleFunction extends BaseEntity {

    private static final long serialVersionUID = -7729259185052057171L;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 功能id
     */
    private Long functionId;


}
