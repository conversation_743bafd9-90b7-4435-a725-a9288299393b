package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.vo.merchant.ListMerchantProductVo;
import com.zhixianghui.facade.merchant.vo.merchant.MainstaySimpleVo;

import java.util.List;
import java.util.Map;

/**
 * 报价单信息
 *
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantEmployerQuoteFacade {


    /**
     * 获取详情信息
     *
     * @param mchNo
     * @return
     */
    List<MerchantEmployerQuote> listByMchNo(String mchNo);

    List<MerchantEmployerQuote> listBy(Map<String, Object> params);

    List<MerchantEmployerQuote> getQuoteList(String mchNo,Map<String, Object> paramMap);

    MerchantEmployerQuote getById(Long id);

    void deleteById(Long id);

    List<Map<String,Object>> getQuoteByMchNo(String mchNo);

    List<ListMerchantProductVo> listMchProduct(Map<String, Object> param);

    List<MainstaySimpleVo> listOpenMainstayByEmployerNoAndProduct(Integer status, String productNo, String mchNo);

    List<MerchantEmployerQuote> listGroupByProductNo(Map<String, Object> paramMap);

    List<MerchantEmployerQuote> getQuoteListWithSupplier(String mchNo, Map<String, Object> paramMap);

    boolean isExistQuote(String mchNo, String mainstayNo, String productNo);
}
