package com.zhixianghui.facade.merchant.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 微信用户信息表
    */
@Data
@TableName(value = "tbl_wechat_user_info")
public class WechatUserInfo implements Serializable {
    /**
     * 自增主键
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 昵称
     */
    @TableField(value = "NICKNAME")
    private String nickname;

    /**
     * 微信openid
     */
    @TableField(value = "WX_OPEN_ID")
    private String wxOpenId;

    /**
     * 小程序openid
     */
    @TableField(value = "OPEN_ID")
    private String openId;

    /**
     * 微信平台unioid
     */
    @TableField(value = "UNIONID")
    private String unionid;

    /**
     * 头像
     */
    @TableField(value = "AVATAR")
    private String avatar;

    /**
     * 性别
     */
    @TableField(value = "GENDER")
    private Integer gender;

    /**
     * 微信平台 0 小程序 1 公众号
     */
    @TableField(value = "WX_PLAT")
    private Integer wxPlat;

    /**
     * 绑定手机号
     */
    @TableField(value = "MOBILE")
    private String mobile;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_AT")
    private Date updateAt;

    /**
     * 最近登录时间
     */
    @TableField(value = "LAST_LOGIN_AT")
    private Date lastLoginAt;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_AT")
    private Date createAt;

    /**
     * 微信appid
     */
    @TableField(value = "APP_ID")
    private String appId;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_NICKNAME = "NICKNAME";

    public static final String COL_WX_OPEN_ID = "WX_OPEN_ID";

    public static final String COL_OPEN_ID = "OPEN_ID";

    public static final String COL_UNIONID = "UNIONID";

    public static final String COL_AVATAR = "AVATAR";

    public static final String COL_GENDER = "GENDER";

    public static final String COL_WX_PLAT = "WX_PLAT";

    public static final String COL_MOBILE = "MOBILE";

    public static final String COL_UPDATE_AT = "UPDATE_AT";

    public static final String COL_LAST_LOGIN_AT = "LAST_LOGIN_AT";

    public static final String COL_CREATE_AT = "CREATE_AT";

    public static final String COL_APP_ID = "APP_ID";
}
