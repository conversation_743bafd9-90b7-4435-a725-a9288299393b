package com.zhixianghui.facade.merchant.entity.user.agent;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台角色表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentRole extends BaseEntity {

    private static final long serialVersionUID = 9116754987857834318L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商户编号
     */
    private String agentNo;

    /**
     * 角色名称
     */
    @JsonProperty("roleName")
    private String name;

    /**
     * 角色描述
     */
    private String remark;

    /**
     * 角色类型:0自定义,1预置
     */
    private Integer roleType;





}
