package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-06-24
 */
public interface MerchantCkhQuoteFacade {

    MerchantCkhQuote getFeeRate(String employerNo, String mainstayNo, String productNo);

    Integer getBalancedMode(MerchantEmployerQuote merchantEmployerQuote);
}
