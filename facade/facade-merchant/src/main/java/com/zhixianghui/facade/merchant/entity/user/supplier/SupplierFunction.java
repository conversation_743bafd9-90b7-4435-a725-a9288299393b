package com.zhixianghui.facade.merchant.entity.user.supplier;

import java.util.Date;
import java.io.Serializable;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台功能表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierFunction extends BaseEntity {

    private static final long serialVersionUID = -4375324437222769388L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 编号
     */
    private String number;

    /**
     * 上级功能id
     */
    private Long parentId;

    /**
     * 权限标识
     */
    private String permissionFlag;

    /**
     * 后端url
     */
    private String url;

    /**
     * 附加信息
     */
    private String extraInfo;


}
