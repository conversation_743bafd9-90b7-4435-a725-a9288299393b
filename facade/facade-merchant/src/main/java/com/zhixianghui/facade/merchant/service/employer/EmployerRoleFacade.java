package com.zhixianghui.facade.merchant.service.employer;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.vo.EmployerRoleVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;

import java.util.List;
import java.util.Map;

/**
 * 用工企业角色接口
 *
 * <AUTHOR> <PERSON>ng
 */
public interface EmployerRoleFacade{

    /**
     * 创建用工企业后台角色
     * @param role 角色
     */
    void create(EmployerRole role);

    /**
     * 根据id查询用工企业后台角色
     * @param mchNo 商户编号
     * @param id
     */
    EmployerRole getById(String mchNo, long id);

    /**
     * 更新用工企业后台角色
     * @param role 角色
     */
    void update(EmployerRole role) throws BizException;

    /**
     * 根据id删除用工企业后台角色
     * @param mchNo     商户编号
     * @param id        角色id
     */
    void deleteById(String mchNo, long id) throws BizException;

    /**
     * 为角色分配功能
     * @param mchNo           商户编号
     * @param roleId        角色id
     * @param functionIds   功能id
     */
    void updateFunction(String mchNo, long roleId, List<Long> functionIds) throws BizException;

    /**
     * 根据角色id获取其关联的功能
     * @param mchNo       商户编号
     * @param roleId    角色id
     */
    List<EmployerFunction> listFunctionByRoleId(String mchNo, long roleId) throws BizException;

    /**
     * 根据角色id获取其关联的操作员
     * @param mchNo       商户编号
     * @param roleId    角色id
     */
    List<EmployerStaffVO> listStaffByRoleId(String mchNo, long roleId) throws BizException;

    /**
     * 查询所有角色
     */
    List<EmployerRole> listAll(String mchNo);

    /**
     * 分页查询角色
     *
     * @param mchNo
     * @param paramMap
     * @param pageParam
     */
    PageResult<List<EmployerRoleVo>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 用于运营后台查询用工企业角色列表
     * @param map
     * @param newInstance
     * @return
     */
    PageResult<List<EmployerRoleVo>> listPage(Map<String, Object> map, PageParam newInstance);

    Long count(EmployerRoleVo employerRoleVo, String mchNo);
}
