package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商户报备通道表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MerchantChannel implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识, 0删除, 1未删除
     */
    private Integer deleteFlag;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 通道类型, 1接口报备
     */
    private Integer channelType;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 配置key
     */
    private String configKey;

    /**
     * 商户名称
     */
    private String mchName;


}
