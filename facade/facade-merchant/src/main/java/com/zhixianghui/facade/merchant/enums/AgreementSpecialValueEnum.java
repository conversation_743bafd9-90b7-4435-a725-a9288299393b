package com.zhixianghui.facade.merchant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName AgreementSpecialValueEnum
 * @Description TODO
 * @Date 2022/8/25 14:15
 */
@AllArgsConstructor
@Getter
public enum  AgreementSpecialValueEnum {

    MCH_PERSONNELS_NAME("mchPersonnelsName","董监高身份列表"),

    MCH_QUOTE_RATE("quoteRate","服务费比例"),

    MCH_WORK_CATEGORY_NAME("workCategoryName","岗位类型"),

    MCH_SERVICE_DESC("serviceDesc","服务描述"),

    MCH_INVOCENAME("invoiceCategoryName","发票类目"),

    MCH_SIGN_DATE("signDate","签署日期"),

    MCH_REGISTER_ADDR("registerAddr","注册地址"),

    MCH_MANAGEMENT_ADDR("mchManagementAddr","商户经营地址"),

    MAINSTAY_MANAGEMENT_ADDR("mainstayManagementAddr","供应商经营地址"),
    SPECIAL_BEGIN_DATE("beginDate","签署日期"),
    SPECIAL_END_DATE("endDate","签署有效截止日")

    ;



    private String value;

    private String desc;
}
