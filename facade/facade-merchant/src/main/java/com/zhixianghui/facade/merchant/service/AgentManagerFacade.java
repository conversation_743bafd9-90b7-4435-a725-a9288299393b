package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.dto.AgentBaseInfoDto;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.vo.agent.AgentAddVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 合伙人管理接口
 * @date 2021/2/2 17:38
 **/
public interface AgentManagerFacade {

    Long createAgent(Agent agent,AgentSaler agentSaler, String approvalContent);

    void batchSetInviter(List<String> agentNoList, String inviterNo, String updater);

    void batchSetSeller(List<String> agentNoList, Long sellerId, String realName);

    void changeStatus(Agent agent);

    void updateAgentDetail(AgentVo vo, Integer agentType, String loginName);

    void generateAgent(Agent agent, AgentSaler agentSaler);

    void saveAgentExtInfo(AgentAddVo addVo);

    void editAgentBaseInfo(AgentBaseInfoDto baseInfoDto, PmsOperator pmsOperator);
}
