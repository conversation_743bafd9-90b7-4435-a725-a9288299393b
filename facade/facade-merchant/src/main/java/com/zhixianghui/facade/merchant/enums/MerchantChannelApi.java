package com.zhixianghui.facade.merchant.enums;

/**
 * <AUTHOR>
 * @Date 2021/4/21 9:11
 */
public class MerchantChannelApi {
    /**
     * 代征主体 奔奔 成功信息
     */
    public static final String RYSC_SUCCESS_CODE = "200";
    /**
     * 代征主体 奔奔 平台id
     */
    public static final String RYSC_PLATFORM_ID = "RYSC_PLATFORM_ID";
    /**
     * 代征主体 奔奔 落地园区
     */
    public static final String RYSC_LANDING_PARK_ID = "RYSC_LANDING_PARK_ID";
    /**
     * 代征主体 奔奔 企业开户/修改 地址
     */
    public static final String RYSC_ADD_BUSINESSES_API = "RYSC_ADD_BUSINESSES";
    public static final String RYSC_ADDRESS_DOMAIN = "RYSC_ADDRESS";
    /**
     * 代征主体 奔奔 三方平台同步支付数据
     */
    public static final String RYSC_PAY_DATA_API = "RYSC_PAY_DATA";
    /**
     * 代征主体 奔奔 电子签模板
     */
    public static final String RYSC_SIGN_TEMP = "RYSC_SIGN_TEMP";
    /**
     * 代征主体 奔奔 支付渠道
     */
    public static final String RYSC_ZF_CODE = "RYSC_ZF_CODE";
    /**
     * 代征主体 奔奔 签约状态
     */
    public static final String RYSC_SIGN_STATUS = "RYSC_SIGN_STATUS";
    /**
     * 代征主体 奔奔 是否需要实名认证
     */
    public static final String RYSC_AUTH_ELEMENTS = "RYSC_AUTH_ELEMENTS";
    /**
     * 支付类型 0小额1大额
     */
    public static final String RYSC_PAY_QUOTE_TYPE = "RYSC_PAY_QUOTE_TYPE";
    /**
     * 服务费率
     */
    public static final String RSY_CHANNEL_SERVICE_RATE = "RSY_CHANNEL_SERVICE_RATE";

    public static final String RSY_RECEIVER_OTHER_INFO_URL = "RSY_RECEIVER_OTHER_INFO_URL";

    public static final String RSY_RECEIVER_CONTRACT_INFO_URL = "RSY_RECEIVER_CONTRACT_INFO_URL";

    public static final String RSY_IMAGE_BASE_URL = "RSY_IMAGE_BASE_URL";

    public static final String RSY_CERTFILE_BASE_URL = "RSY_CERTFILE_BASE_URL";
}
