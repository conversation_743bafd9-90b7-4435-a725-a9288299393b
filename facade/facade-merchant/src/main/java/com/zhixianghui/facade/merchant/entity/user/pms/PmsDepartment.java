package com.zhixianghui.facade.merchant.entity.user.pms;

import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentLeaderEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 部门，特殊值参考 {@link com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentEnum}
 *
 * <AUTHOR>
 */
@Data
public class PmsDepartment implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer version = 0;

    private Date createTime = new Date();

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 序号
     */
    private String number;

    /**
     * 上级部门id
     */
    private Long parentId;

    /**
     * 上级部门名称
     */
    private String parentName;

    /**
     * 部门负责人id，{@link PmsOperator#getId()}
     * 特殊值参考 {@link PmsDepartmentLeaderEnum#getId()}
     */
    private Long leaderId;

    /**
     * 部门负责人名称，{@link PmsOperator#getRealName()}
     * 特殊值参考 {@link PmsDepartmentLeaderEnum#getName()}
     */
    private String leaderName;
}
