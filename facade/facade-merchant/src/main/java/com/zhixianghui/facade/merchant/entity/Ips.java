package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Ips implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Integer id;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家ID
     */
    private String countryId;

    /**
     * 大区
     */
    private String area;

    /**
     * 大区ID
     */
    private Integer areaId;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份ID
     */
    private Integer provinceId;

    /**
     * 城市
     */
    private String city;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 地区
     */
    private String county;

    /**
     * 地区ID
     */
    private Integer countyId;

    private String isp;

    private Integer ispId;

    private Date createdAt;

    private Date updatedAt;


}
