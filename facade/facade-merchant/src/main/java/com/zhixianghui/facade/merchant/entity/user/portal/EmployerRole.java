package com.zhixianghui.facade.merchant.entity.user.portal;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 角色表
 */
@Data
public class EmployerRole extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 角色名称
	 */
	private String name;

	/**
	 * 角色描述
	 */
	private String remark;

	/**
	 * 角色类型:0自定义,1预置
	 */
	private Integer roleType;

}
