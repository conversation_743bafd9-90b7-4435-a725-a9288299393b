package com.zhixianghui.facade.merchant.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 合伙人电子名片
    */
@Data
@TableName(value = "tbl_agent_bussiness_ecard")
public class AgentBussinessEcard implements Serializable {
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @TableField(value = "ecard_name")
    private String ecardName;

    @TableField(value = "ecard_mobile")
    private String ecardMobile;

    @TableField(value = "company")
    private String company;

    @TableField(value = "`position`")
    private String position;

    @TableField(value = "email")
    private String email;

    @TableField(value = "wechat_id")
    private String wechatId;

    @TableField(value = "address")
    private String address;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "open_id")
    private String openId;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_ECARD_NAME = "ecard_name";

    public static final String COL_ECARD_MOBILE = "ecard_mobile";

    public static final String COL_COMPANY = "company";

    public static final String COL_POSITION = "position";

    public static final String COL_EMAIL = "email";

    public static final String COL_WECHAT_ID = "wechat_id";

    public static final String COL_ADDRESS = "address";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_OPEN_ID = "open_id";
}
