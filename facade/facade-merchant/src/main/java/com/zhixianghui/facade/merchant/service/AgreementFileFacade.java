package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.AgreementFile;

import java.util.List;
import java.util.Map;

/**
 * 协议文件表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */

public interface AgreementFileFacade {

    /**
     * 根据协议Id获取协议文件
     * @param AgreementId 协议Id
     * @return 协议文件列表
     */
    List<AgreementFile> listByAgreementId(Long AgreementId);

    /**
     * 获取满足条件的所有文件
     * @param paramMap 条件
     * @return 文件对象列表
     */
    List<AgreementFile> listBy(Map<String, Object> paramMap);

    void update(AgreementFile agreementFile);

    AgreementFile getByAgreementIdAndType(Long id, int type);
}
