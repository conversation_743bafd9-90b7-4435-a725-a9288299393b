package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.vo.RelevantAgent;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantLogoVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;

import java.util.List;
import java.util.Map;

/**
 * 商户接口
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantFacade {

    void update(Merchant merchant);

    Merchant getByMchNo(String mchNo);

    /**
     * 根据商编获取相关合伙人
     * @return 相关合伙人信息
     */
    RelevantAgent getRelevantAgentByMchNo(String mchNo);

    /**
     *
     * @param existAgent 是否存在合伙人
     * @return 对应商户号
     */
    List<String> listMerchantNo(Boolean existAgent);

    /**
     * 更新模板id到商户
     * @param templateId 模板id
     * @param mchNoList 商户列表
     */
    void updateTemplateId(String templateId, List<String> mchNoList);

    /**
     *
     * @param templateId 模板id
     * @param signTemplateType
     * @return 用户该模板的商户列表
     */
    List<Merchant> getByTemplateId(String templateId, Integer signTemplateType);

    /**
     *
     * @param mchNo 商户号
     * @return 模板id列表
     */
    List<String>  listTemplateIdByMchNo(List<String> mchNo);


    /**
     *
     * @param templateId
     */
    void delTemplateId(String templateId);

    void updateBaseInfo(MerchantUpdateVo merchantUpdateVo);

    Map<String, Object> getAgentData(Map<String, Object> nowDateMap, Map<String, Object> lastDateMap);


    void forceDelete(String mchNo);

    List<Merchant> getAllSupplier();

    void forceActive(String mchNo,String loginName);

    void changeLogo(MerchantLogoVo merchantLogoVo,String mchNo, String name);
}
