package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用工企业主体信息
 * <AUTHOR>
 */
@Data
public class MerchantEmployerMain extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * updateTime
	 */
	private java.util.Date updateTime;

	/**
	 * 登录名
	 */
	private String updator;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 企业简称
	 */
	private String shortName;

	/**
	 * 税号/社会信用代码
	 */
	private String taxNo;

	/**
	 * 注册地址-省
	 */
	private String registerAddrProvince;

	/**
	 * 注册地址-市
	 */
	private String registerAddrCity;

	/**
	 * 注册地址-区/镇
	 */
	private String registerAddrTown;

	/**
	 * 注册地址-详细地址
	 */
	private String registerAddrDetail;

	/**
	 * 注册资本
	 */
	private BigDecimal registerAmount;

	/**
	 * 经营范围
	 */
	private String managementScope;

	/**
	 * 营业有效期类型
	 * @see com.zhixianghui.common.statics.enums.merchant.ValidityDateTypeEnum
	 */
	private Integer managementValidityDateType;
	/**
	 * 营业时间起
	 */
	private String managementTermBegin;

	/**
	 * 营业时间止
	 */
	private String managementTermEnd;

	/**
	 * 法人证件类型
	 * @see com.zhixianghui.common.statics.enums.merchant.CertificateTypeEnum
	 */
	private Integer certificateType;

	/**
	 * 法人姓名
	 */
	private String legalPersonName;

	/**
	 * 法人证件号码
	 */
	private String certificateNumber;

	/**
	 * 营业有效期类型
	 * @see com.zhixianghui.common.statics.enums.merchant.ValidityDateTypeEnum
	 */
	private Integer certificateValidityDateType;

	/**
	 * 法人证件有效期起
	 */
	private String certificateTermBegin;

	/**
	 * 法人证件有效期止
	 */
	private String certificateTermEnd;

	/**
	 * 经营地址-省
	 */
	private String managementAddrProvince;

	/**
	 * 经营地址-市
	 */
	private String managementAddrCity;

	/**
	 * 经营地址-区/镇
	 */
	private String managementAddrTown;

	/**
	 * 经营地址-详细
	 */
	private String managementAddrDetail;

}
