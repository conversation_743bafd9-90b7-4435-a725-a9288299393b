/*
 * Powered By [joinPay.com]
 */

package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.merchant.SignAccountStatusEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 商户信息表
 * <AUTHOR>
 */
@Data
public class Merchant extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 激活时间
	 */
	private Date activeTime;

	/**
	 * 更新人登录名
	 */
	private String updator;

	/**
	 * 商户名称
	 */
	protected String mchName;

	/**
	 * 商户编号
	 */
	protected String mchNo;

	/**
	 * 商户状态
	 * @see com.zhixianghui.common.statics.enums.merchant.MchStatusEnum
	 */
	protected Integer mchStatus;

	/**
	 * 成交状态
	 * {@link com.zhixianghui.common.statics.enums.merchant.DealStatusEnum}
	 */
	protected Integer dealStatus;

	/**
	 * 认证状态
	 * @see com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum
	 */
	private Integer authStatus;

	/**
	 * 商户类型
	 * @see com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum
	 */
	protected Integer merchantType;

	/**
	 * 合伙人编号
	 */
	private String agentNo;

	/**
	 * 合伙人名称
	 */
	private String agentName;

	/**
	 * 联系电话
	 */
	private String contactPhone;

	/**
	 * 联系人名称
	 */
	private String contactName;

	/**
	 * 联系人邮箱
	 */
	private String contactEmail;

	/**
	 * 客服电话
	 */
	private String servicePhone;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 模板id
	 */
	protected String templateId;

	/**
	 * 创始人
	 */
	private String founder;

	/**
	 * 其他信息，json格式
	 */
	private String jsonInfo;
//	/**
//	 * 印章id
//	 */
//	private String assignedSealId;

	private String branchName;

	private String businessType;

	/**
	 * json操作该对象
	 */
	private JsonEntity jsonEntity = new JsonEntity();

	@Data
	@Accessors(chain = true)
	public static class JsonEntity implements Serializable {
		/**
		 * 签约渠道返回的代征主体组织Id
		 */
		private String signOrgId;

		/**
		 * 法人唯一标识 用于请求鉴权渠道和签约渠道
		 */
		private String legalNo;

		/**
		 * 签约渠道返回的代征主体创建人Id
		 */
		private String signAccountId;

		/**
		 * 电签账户状态
		 */
		private Integer signAccountStatus = SignAccountStatusEnum.WAIT_CREATE.getValue();
	}

	@Deprecated
	public String getJsonInfo() {
		return JsonUtil.toString(jsonEntity);
	}

	@Deprecated
	public void setJsonInfo(String jsonInfo) {
		this.jsonInfo = jsonInfo;
		this.jsonEntity = JsonUtil.toBean(jsonInfo,JsonEntity.class);
	}
}
