package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.vo.EmployerMainUpdateVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;

import java.util.Map;

/**
 * 用工企业主体信息
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantEmployerMainFacade {


    /**
     * 获取详情信息
     * @param mchNo
     * @return
     */
    MerchantEmployerMain getByMchNo(String mchNo);

    /**
     * 更新信息
     * @throws BizException
     */
    void updateOrInsert(EmployerMainUpdateVo vo, String loginName) throws IllegalArgumentException,BizException;

    Map<String, Object> getMainInfo(String mchNo);

    Map<String, Object> getBusinessInfo(String mchNo);

    void updateMain(MerchantUpdateVo merchantUpdateVo);

    void updateBusiness(MerchantUpdateVo merchantUpdateVo);
}
