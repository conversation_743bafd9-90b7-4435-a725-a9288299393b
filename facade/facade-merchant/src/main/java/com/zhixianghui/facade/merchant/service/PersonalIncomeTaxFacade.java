package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.PersonalIncomeTax;

import java.math.BigDecimal;
import java.util.List;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-06-22
 */
public interface PersonalIncomeTaxFacade {

    List<PersonalIncomeTax> listAll();

    PersonalIncomeTax getSuitTax(BigDecimal amount);
}
