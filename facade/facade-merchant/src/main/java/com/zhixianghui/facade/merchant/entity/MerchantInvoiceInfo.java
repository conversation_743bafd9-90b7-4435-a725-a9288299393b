package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 商户开票信息表
 * <AUTHOR>
 */
@Data
public class MerchantInvoiceInfo extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 修改时间
	 */
	private java.util.Date updateTime;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 企业名称
	 */
	private String mchName;

	/**
	 * 纳税人类型
	 * @see com.zhixianghui.common.statics.enums.merchant.TaxPayerTypeEnum
	 */
	private Integer taxPayerType;

	/**
	 * 税号
	 */
	private String taxNo;

	/**
	 * 注册地址-电话
	 */
	private String registerAddrInfo;

	/**
	 * 银行账户
	 */
	private String accountNo;

	/**
	 * 银行名称
	 */
	private String bankName;

	/**
	 * 默认发票类目编码
	 */
	private String defaultInvoiceCategoryCode;

	/**
	 * 默认发票类目名称
	 */
	private String defaultInvoiceCategoryName;

	/**
	 * 联系电话
	 */
	private String invoicePhone;

	/**
	 * 开票地址
	 */
	private String invoiceAddress;

}
