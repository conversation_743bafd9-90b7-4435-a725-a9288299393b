package com.zhixianghui.facade.merchant.entity;

import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合伙人银行账户表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentBankAccount extends BaseEntity {

    private static final long serialVersionUID = -9099198928363546370L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 银行卡账户
     */
    private String accountNo;

    /**
     * 银行账户名
     */
    private String accountName;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行行号（联行号)
     */
    private String bankChannelNo;


    /**
     * 支付宝账户
     */
    private String alipayAccount;
}
