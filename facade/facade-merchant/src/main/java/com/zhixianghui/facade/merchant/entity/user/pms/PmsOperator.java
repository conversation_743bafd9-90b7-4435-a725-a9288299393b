package com.zhixianghui.facade.merchant.entity.user.pms;

import com.zhixianghui.facade.merchant.entity.user.FlowUserCache;
import com.zhixianghui.facade.merchant.enums.PlatformSourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Author: Cmf
 * Date: 2019/10/9
 * Time: 18:09
 * Description: 操作员实体
 */
@Getter
@Setter
public class PmsOperator extends FlowUserCache implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private Integer version = 0;
    private Date createTime = new Date();
    private String loginName;// 登录名
    private String loginPwd; // 登录密码
    private String remark; // 描述
    private String realName; // 姓名
    private String mobileNo; // 手机号
    private Integer status; // 状态 PmsOperatorStatusEnum
    private Integer type; // 操作员类型 PmsOperatorTypeEnum（1:超级管理员，2:普通操作员），超级管理员由系统初始化时添加，不能删除

    /**
     * 部门特殊值参考 {@link com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentEnum}
     */
    private Long departmentId;  // 所属部门id
    private String departmentName;  // 所属部门名称

    private Date currLoginTime;// 当前登录时间
    private Date lastLoginTime;// 最后登录时间
    private Integer isChangedPwd;// 是否更改过密码         //todo 待删除
    private Integer pwdErrorCount; // 连续输错密码次数（连续5次输错就冻结帐号）
    private Date pwdErrorTime; // 最后输错密码的时间
    private Integer isLoginSmsVerify; //登录是否短信检验：100-是，101-否
    private Date validDate; //口令有效期
    private Date lastModPwdTime;// 最后一次修改密码时间
    private String creator; // 创建人
    private String updator;// 修改者，根据该字段进行判断审核时不能是同一个操作员
    private String historyPwd;// 历史密码（包括当前密码）

    //region 非数据库字段
    /**
     * 拥有的角色列表，非数据库字段
     */
    private List<Long> roleIds;
    //endregion

    //重写无参构造方法
    public PmsOperator(){
        //设置所属平台编号
        super.setCachePlatform(String.valueOf(PlatformSourceEnum.OPERATION.getValue()));
    }

    //重写set方法，放入到父类中
    public void setRealName(String realName){
        this.realName = realName;
        super.setCacheName(realName);
    }

    public void setMobileNo(String mobileNo){
        this.mobileNo = mobileNo;
        super.setCachePhone(mobileNo);
    }

    public void setId(Long id){
        this.id = id;
        super.setCacheId(String.valueOf(id));
    }

}
