package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.AgentProductQuote;

import java.util.List;
import java.util.Map;

/**
 * 合伙人银行账户表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
public interface AgentProductQuoteFacade {

    List<AgentProductQuote> getByAgentNo(String agentNo);

    List<AgentProductQuote> getByParams(Map<String, Object> params);

    void preSaveAgentQuote(AgentProductQuote quote);
}
