package com.zhixianghui.facade.merchant.entity.user.agent;

import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台功能表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentFunction extends BaseEntity {

    private static final long serialVersionUID = 7974193550616135929L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 编号
     */
    private String number;

    /**
     * 上级功能id
     */
    private Long parentId;

    /**
     * 权限标识
     */
    private String permissionFlag;

    /**
     * 后端url
     */
    private String url;

    /**
     * 附加信息
     */
    private String extraInfo;


}
