package com.zhixianghui.facade.merchant.entity.user.supplier;

import java.util.Date;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台角色表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierRole extends BaseEntity {

    private static final long serialVersionUID = -594445450597394212L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色描述
     */
    private String remark;

    /**
     * 角色类型:0自定义,1预置
     */
    private Integer roleType;


}
