package com.zhixianghui.facade.merchant.entity.user.portal;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.user.portal.PortalOperatorStatusEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 商户支付密码表
 */
@Data
public class MerchantTradePwd extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 状态 {@link PortalOperatorStatusEnum#getValue()}
	 */
	private Integer status;

	/**
	 * 是否初始化密码
	 */
	private Integer isInitPwd;

	/**
	 * 支付密码
	 */
	private String pwd;

	/**
	 * 密码输错次数
	 */
	private Integer pwdErrorCount;

	/**
	 * 最后输错密码时间
	 */
	private Date pwdErrorTime;

	/**
	 * 附加信息
	 */
	private String extraInfo;

	private JsonEntity jsonEntity = new JsonEntity();

	@Data
	public static class JsonEntity implements Serializable {
		/**
		 * 历史密码
		 */
		private List<String> historyPwdList = new ArrayList<>();

	}

	public Object getExtraInfo() {
		return JsonUtil.toString(this.jsonEntity);
	}

	public void setExtraInfo(String extraInfo) {
		this.extraInfo = extraInfo;
		this.jsonEntity = JsonUtil.toBean(extraInfo, JsonEntity.class);
	}
}
