package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.facade.merchant.vo.AgreementDto;
import com.zhixianghui.facade.merchant.vo.AgreementExportVo;
import com.zhixianghui.facade.merchant.vo.AgreementResVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 协议表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */

public interface AgreementFacade {
    /**
     * 发起协议
     *
     * @param agreementDto 协议对象
     */
    void createAgreement(AgreementDto agreementDto);

    /**
     * 编辑协议
     *
     * @param agreementId  协议id
     * @param deadline     签约截止时间
     * @param expireTime   过期时间
     * @param operatorName 操作人名
     */
    void editAgreement(Long agreementId, Date deadline, Date expireTime, String operatorName);

    /**
     * 更新协议状态
     *
     * @param agreementId  协议id
     * @param status       协议状态
     * @param description  备注
     * @param operatorName 操作人
     */
    void updateStatus(Long agreementId, Integer status, String description, String operatorName);

    /**
     * 分页查询协议
     *
     * @param paramMap  查询参数
     * @param pageParam 分页参数
     * @return 协议
     */
    PageResult<List<Agreement>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 归档文件
     *
     * @param files        文件
     * @param operatorName 归档人名
     */
    void archive(List<AgreementFile> files, String operatorName);

    /**
     * 根据id获取协议
     *
     * @param id 协议id
     * @return 协议对象
     */
    Agreement getAgreementById(Long id);

    PageResult<List<AgreementResVo>> listCustomPage(Map<String, Object> map, PageParam toPageParam);

    List<Agreement> getAgreementPageByMchNoAndMainstayNo(Map<String, Object> map);

    List<Agreement> listBy(Map<String, Object> paramMap);

    void cancelAgreement(List<Long> idsList);

    void deleteAgreement(List<Long> idList);

    void turnToOffline(List<Long> idList);

    void delay(Long id, Date deadLine);

    PageResult<List<AgreementExportVo>> getExportList(Map<String, Object> param, PageParam pageParam);

    PageResult<List<Agreement>> listPageByMerchant(Map<String, Object> paramMap, PageParam toPageParam);
}
