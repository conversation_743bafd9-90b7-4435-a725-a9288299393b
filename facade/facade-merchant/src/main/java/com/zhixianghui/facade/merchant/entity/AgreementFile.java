package com.zhixianghui.facade.merchant.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 协议文件表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Data
@Builder
@AllArgsConstructor
public class AgreementFile implements Serializable {

    private static final long serialVersionUID = -2033549811166046861L;

    private Long id;

    /**
     * 协议ID
     */
    private Long agreementId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 归档/协议
     */
    private Integer type;

    /**
     * 文件路径
     */
    private String fileUrl;

    public AgreementFile(){

    }
}
