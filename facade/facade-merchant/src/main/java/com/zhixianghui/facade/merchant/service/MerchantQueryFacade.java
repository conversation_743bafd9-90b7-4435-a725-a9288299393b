package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.vo.MerchantInfoVo;
import com.zhixianghui.facade.merchant.vo.MerchantSupplierInfoVo;
import com.zhixianghui.facade.merchant.vo.merchant.BranchVo;

import java.util.List;
import java.util.Map;

/**
 * 商户信息查询接口
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantQueryFacade {


    /**
     * 根据商户编号查询商户信息
     * @param mchNo
     * @return
     */
    Merchant getByMchNo(String mchNo);

    /**
     * 分页查询
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<Object>> listExtObjectPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 查询
     * @param paramMap
     * @return
     */
    List<Merchant> listBy(Map<String, Object> paramMap);

    PageResult<List<Merchant>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    PageResult<List<MerchantSupplierInfoVo>> listExtSupplierMerchantPage(Map<String, Object> paramMap, PageParam newInstance);

    Map<String, Object> getBaseInfo(String mchNo);

    MerchantInfoVo convert(String mchNo);

    List<MerchantInfoVo> listAll(Map<String,Object> paramMap);

    List<BranchVo> listByBranch(Map<String, Object> paramMap);

    List<Map<String,Object>> listByBranchEqual(Map<String, Object> paramMap);
}
