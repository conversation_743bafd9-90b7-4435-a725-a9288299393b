package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;

import java.util.List;
import java.util.Map;

/**
 * 岗位信息
 *
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantEmployerPositionFacade {


    /**
     * 获取详情信息
     *
     * @param mchNo
     * @return
     */
    List<MerchantEmployerPosition> listByMchNo(String mchNo);

    /**
     * 根据商户编号跟服务类别编码查询岗位信息
     * @param mchNo             商户编号
     * @param workCategoryCode  岗位类别编码
     * @return
     */
    MerchantEmployerPosition getByMchNoAndWorkCategoryCode(String mchNo, String workCategoryCode);

    List<MerchantEmployerPosition> listByMchNoWithQuote(Map<String,Object> paramMap);

    List<MerchantEmployerPosition> listByMchNoWithQuoteWithoutGroup(Map<String,Object> paramMap);
}
