package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.merchant.vo.EmployerCooperateUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerFullInfoUpdateVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;

/**
 * 用工企业商户合作信息接口
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantEmployerCooperateFacade {


    /**
     * 获取详情信息
     * @param mchNo
     * @return
     */
    MerchantEmployerCooperate getByMchNo(String mchNo);

    /**
     * 更新合作信息
     * @throws BizException
     */
    void update(EmployerCooperateUpdateVo vo, String loginName) throws BizException;

    void updateSpecifiedField(MerchantEmployerCooperate toMerchantEmployerCooperate) throws BizException;

    void updateAllMerchantMessage(EmployerFullInfoUpdateVo vo) throws BizException;

    void updateMainstayCooperate(MerchantUpdateVo merchantUpdateVo);

    void updateMerchantCooperate(MerchantUpdateVo merchantUpdateVo);
}
