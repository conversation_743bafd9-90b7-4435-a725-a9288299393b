package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.dto.common.RecordItemVo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantChannel;

import java.util.Date;

/**
 * 商户报备通道表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-04-20
 */
public interface MerchantChannelFacade {

    /**
     *
     * @param mchNo 商户号
     * @return 商户通道实体
     */
    MerchantChannel getByMchNo(String mchNo);

    void handle(MerchantChannel merchantChannel, Merchant employerNo);

    void synchronizeRecordItem(RecordItemVo recordItemVo);

    void synchronizeRecordItem(String beginTime, String endTime);

    void synchronizeRelation(String beginTime, String endTime);

    void syncSignInfo(Date startTime, Date endTime);

    void syncUserInfo(Date startTime, Date endTime);

    void rsyncRechargeInfoToBenben(Date startTime, Date endTime);
}
