package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 用工企业岗位信息表
 * <AUTHOR>
 */
@Data
public class MerchantEmployerPosition extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 更新人登录名
	 */
	private String updator;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 工作场所编码
	 */
	private String workplaceCode;

	/**
	 * 服务类别代码
	 */
	private String workCategoryCode;

	/**
	 * 服务类别编码
	 */
	private String workCategoryName;

	/**
	 * 企业从事业务
	 */
	private String businessDesc;

	/**
	 * 自由职业者服务描述
	 */
	private String serviceDesc;

	/**
	 * 收费规则描述
	 */
	private String chargeRuleDesc;

	private String extJson;

	private JsonEntity jsonEntity = new JsonEntity();

	private List<InvoiceCategoryVo> invoiceCategoryList;

	private Long originId;

	@Data
	public static class JsonEntity implements Serializable {
		private List<InvoiceCategoryVo> invoiceCategoryList = new ArrayList<>();
	}

	@Deprecated
	public String getExtJson() {
		return JsonUtil.toString(this.jsonEntity);
	}

	@Deprecated
	public void setExtJson(String extJson) {
		this.extJson = extJson;
		this.jsonEntity = JsonUtil.toBean(extJson, JsonEntity.class);
		this.invoiceCategoryList = jsonEntity.getInvoiceCategoryList();
	}
}
