package com.zhixianghui.facade.merchant.entity.user.supplier;

import java.util.Date;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.facade.merchant.entity.user.FlowUserCache;
import com.zhixianghui.facade.merchant.enums.PlatformSourceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台员工表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierStaff extends FlowUserCache {

    private static final long serialVersionUID = 8570133200779453863L;

    private Long id;

    private Integer version;

    private Date createTime;

    private String parentPermissionFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作员id
     */
    private Long operatorId;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 员工类型
     */
    private Integer type;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String updator;

    /**
     * 附加信息
     */
    private String extraInfo;

    /**
     * 员工姓名
     */
    private String name;

    //重写无参构造方法
    public SupplierStaff(){
        //设置所属平台编号
        super.setCachePlatform(String.valueOf(PlatformSourceEnum.SUPPLIER.getValue()));
    }

    //重写set方法，放入到父类中
    public void setName(String name){
        this.name = name;
        super.setCacheName(name);
    }

    public void setMchNo(String mchNo){
        this.mchNo = mchNo;
        super.setCacheNo(mchNo);
    }

    public void setId(Long id){
        this.id = id;
        super.setCacheId(String.valueOf(id));
    }

}
