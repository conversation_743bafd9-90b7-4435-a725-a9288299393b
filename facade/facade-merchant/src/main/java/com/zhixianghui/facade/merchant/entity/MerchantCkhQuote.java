package com.zhixianghui.facade.merchant.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tbl_merchant_ckh_quote")
public class MerchantCkhQuote implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @Version
    private Integer version;

    /**
     * 报价单id
     */
    private Long quoteId;

    /**
     * 个税承担方
     */
    private Integer taxPayer;

    /**
     * 个税承担比例
     */
    private BigDecimal taxRate;

    /**
     * 增值税率
     */
    private BigDecimal addedTaxRate;

    /**
     * 个税公式
     */
    private Integer taxFormula;

    /**
     * 个税类型描述
     */
    private String taxTypeDesc;

    /**
     * 服务费比例
     */
    private BigDecimal serviceFeeRate;

    /**
     * 结算模式
     */
    private Integer balancedMode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private BigDecimal taxRatePct;

    @TableField(exist = false)
    private BigDecimal addedTaxRatePct;

    @TableField(exist = false)
    private BigDecimal serviceFeeRatePct;

}
