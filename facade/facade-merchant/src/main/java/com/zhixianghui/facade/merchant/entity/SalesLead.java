package com.zhixianghui.facade.merchant.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 推荐客户
    */
@Data
@TableName(value = "tbl_sales_lead")
public class SalesLead implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField(value = "contact_name")
    private String contactName;

    @TableField(value = "contact_mobile")
    private String contactMobile;

    @TableField(value = "contact_email")
    private String contactEmail;

    @TableField(value = "company_name")
    private String companyName;

    @TableField(value = "contact_position")
    private String contactPosition;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "`status`")
    private Short status;

    @TableField(value = "ext_info")
    private String extInfo;

    @TableField(value = "agent_no")
    private String agentNo;

    @TableField(value = "agent_name")
    private String agentName;

    @TableField(value = "create_no")
    private String createNo;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_CONTACT_NAME = "contact_name";

    public static final String COL_CONTACT_MOBILE = "contact_mobile";

    public static final String COL_CONTACT_EMAIL = "contact_email";

    public static final String COL_COMPANY_NAME = "company_name";

    public static final String COL_CONTACT_POSITION = "contact_position";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_STATUS = "status";

    public static final String COL_EXT_INFO = "ext_info";

    public static final String COL_AGENT_NO = "agent_no";

    public static final String COL_AGENT_NAME = "agent_name";
}
