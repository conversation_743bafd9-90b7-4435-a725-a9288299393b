package com.zhixianghui.facade.merchant.service.agent;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.vo.agent.AgentRoleVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台角色表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
public interface AgentRoleFacade {

    /**
     * 创建供应商后台角色
     * @param role 角色
     */
    void create(Agent<PERSON>ole role);

    /**
     * 根据id查询供应商后台角色
     * @param agentNo 商户编号
     * @param id
     */
    AgentRole getById(String agentNo, long id);
    AgentRole getById(Long id);

    /**
     * 更新供应商后台角色
     * @param role 角色
     */
    void update(Agent<PERSON><PERSON> role) throws BizException;

    /**
     * 根据id删除供应商后台角色
     * @param agentNo     商户编号
     * @param id        角色id
     */
    void deleteById(String agentNo, long id) throws BizException;
    void deleteById(Long id);

    /**
     * 为角色分配功能
     * @param agentNo           商户编号
     * @param roleId        角色id
     * @param functionIds   功能id
     */
    void updateFunction(String agentNo, long roleId, List<Long> functionIds) throws BizException;

    /**
     * 用户后台为预置角色分配功能
     * @param roleId        角色id
     */
    void updateFunction(Long roleId, List<Long> functionIds);

    /**
     * 根据角色id获取其关联的功能
     * @param agentNo       商户编号
     * @param roleId    角色id
     */
    List<AgentFunction> listFunctionByRoleId(String agentNo, long roleId) throws BizException;

    /**
     * 用于运营后台查询
     * @param roleId 角色id
     * @return  合伙人预置角色的功能列表
     */
    List<AgentFunction> listFunctionByRoleId(Long roleId);

    /**
     * 根据角色id获取其关联的操作员
     * @param agentNo       商户编号
     * @param roleId    角色id
     */
    List<AgentStaffVO> listStaffByRoleId(String agentNo, long roleId) throws BizException;

    /**
     * 查询所有角色
     */
    List<AgentRole> listAll(String agentNo);

    /**
     * 分页查询角色
     *
     * @param agentNo
     * @param paramMap
     * @param pageParam
     */
    PageResult<List<AgentRoleVo>> listPage(String agentNo, Map<String, Object> paramMap, PageParam pageParam);

    /**
     *
     * @param map 查询参数
     * @param newInstance   分页参数
     * @return  合伙人预置角色列表
     */
    PageResult<List<AgentRoleVo>> listPage(Map<String, Object> map, PageParam newInstance);


    Long count(AgentRoleVo agentRoleVo, String agentNo);
}
