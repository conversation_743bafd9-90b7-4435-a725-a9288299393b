package com.zhixianghui.facade.merchant.entity.user.portal;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用工企业功能表
 */
@Data
public class EmployerFunction extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 类型
	 */
	private Integer type;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 编号
	 */
	private String number;

	/**
	 * 上级功能id
	 */
	private Long parentId;

	/**
	 * 权限标识
	 */
	private String permissionFlag;

	/**
	 * 后端url
	 */
	private String url;

	/**
	 * 附加信息
	 */
	private Object extraInfo;



}
