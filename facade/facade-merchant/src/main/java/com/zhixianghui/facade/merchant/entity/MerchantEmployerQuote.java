/*
 * Powered By [joinPay.com]
 */

package com.zhixianghui.facade.merchant.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品报价单
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MerchantEmployerQuote extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 更新人登录名
	 */
	private String updator;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 代征主体商户编号
	 */
	private String mainstayMchNo;

	/**
	 * 代征主体商户名称
	 */
	private String mainstayMchName;

	/**
	 * 供应商编号
	 */
	private String supplierNo;

	/**
	 * 供应商名称
	 */
	private String supplierName;

	/**
	 * 计费规则
	 */
	private String ruleParam;

	/**
	 * 公式类型
	 */
	private Integer formulaType;

	/**
	 * 费率
	 */
	private BigDecimal rate;

	/**
	 * 固定手续费
	 */
	private BigDecimal fixedFee;

	/**
	 * 审核状态
	 */
	private Integer status;

	/**
	 * 产品编码
	 */
	private String productNo;

	/**
	 * 产品名称
	 */
	private String productName;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * 流程业务标记
	 */
	private String flowBusinessKey;

	/**
	 * 子表
	 */
	private String quoteId;

	private List<MerchantEmployerQuoteRate> quoteRateList;

	private String mchName;

	private List<Long> positionList = new ArrayList<>();

	private List<String> positionNameList = new ArrayList<>();

	private MerchantCkhQuote merchantCkhQuote;

	private List<MerchantZftQuote> merchantZftQuote;

	private List<String> employerAccountInfo = new ArrayList<>();

	private List<InvoiceCategoryVo> invoiceCategoryList = new ArrayList<>();
}
