package com.zhixianghui.facade.merchant.entity.user.agent;

import java.util.Date;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台员工角色关联表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentStaffRole extends BaseEntity {

    private static final long serialVersionUID = -2605821794100160122L;

    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 角色id
     */
    private Long roleId;


}
