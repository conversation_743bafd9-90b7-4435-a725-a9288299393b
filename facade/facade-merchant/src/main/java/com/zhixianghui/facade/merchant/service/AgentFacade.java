package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import com.zhixianghui.facade.merchant.vo.WxAgentRegisterVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentDetailVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import com.zhixianghui.facade.merchant.vo.agent.SimpleAgentInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 合伙人基本信息表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
public interface AgentFacade {

    List<Agent> listBy(Map<String, Object> paramMap);

    Agent getByAgentNo(String agentNo);

    AgentDetailVo getDetailByAgentNo(String agentNo);

    AgentProductQuote getAgentProductQuote(Long id);

    List<SimpleAgentInfoVo> listAllSimpleAgentInfo();

    List<SimpleAgentInfoVo> listAllSimpleAgentInfoByParam(Map<String, Object> param);

    PageResult<List<Agent>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    PageResult<List<AgentResVo>> listVoPage(Map<String, Object> paramMap, PageParam pageParam);

    Agent getOne(Map<String, Object> paramMap);

    void update(Agent agent);

    List<SimpleAgentInfoVo> listNotRetreatAgentSimple(Long salerId);

    /**
     * 流程执行过程中, 被编辑后需要特殊的操作
     * @param extInfo   业务json
     * @param businessKey   业务关键信息
     * @param userName
     */
    void extraHandle(String extInfo, String businessKey, String userName);

    void createWxAgent(WxAgentRegisterVo wxAgentRegisterVo);
}
