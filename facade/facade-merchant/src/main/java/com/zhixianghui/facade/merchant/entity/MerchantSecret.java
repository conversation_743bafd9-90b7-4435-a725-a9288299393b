package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.facade.merchant.enums.NotifyUrlTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * @description: 商户秘钥信息
 * @author: xingguang li
 * @created: 2020/12/18 11:20
 */
@Data
public class MerchantSecret extends BaseEntity {
    private static final long serialVersionUID = -281077594000652374L;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 商户编号
     */
    private String merchantNo;
    /**
     * 签名方式
     * {@link SignTypeEnum#getValue()}
     */
    private Integer signType;
    /**
     * 平台公钥
     */
    private String platformPublicKey;
    /**
     * 平台私钥
     */
    private String platformPrivateKey;
    /**
     * 商户公钥
     */
    private String merchantPublicKey;

    /**
     * 回调地址类型
     */
    private NotifyUrlTypeEnum notifyUrlType;

    /**
     * 回调地址
     */
    private String notifyUrl;

    /**
     * 通知状态
     */
    private Integer notifyStatus;
}
