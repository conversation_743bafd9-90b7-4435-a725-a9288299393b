package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合伙人银行账户表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentProductQuote extends BaseEntity {

    private static final long serialVersionUID = 6454065873628882830L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 邀请奖励计费规则
     */
    private Integer calculateMode;

    /**
     * 公式类型
     */
    private Integer formulaType;

    /**
     * 固定金额手续费
     */
    private BigDecimal fixedFee;

    /**
     * 比例手续费
     */
    private BigDecimal feeRate;


    /**
     * 状态
     */
    private Integer status;

    /**
     * 审批流ID
     */
    private Long flowId;

    /**
     * 规则类型
     */
    private Integer ruleType;

    /**
     * 规则参数
     * 对应数据库字段，请通过specialFeeRuleList字段进行操作
     */
    private String ruleParam;

    /**
     * 二级佣金-公式类型
     */
    private Integer secondFormulaType;

    /**
     * 二级佣金-固定金额手续费
     */
    private BigDecimal secondFixedFee;

    /**
     * 二级佣金-费率
     */
    private BigDecimal secondFeeRate;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 最低手续费
     */
    private BigDecimal minFee;

    /**
     * 最高手续费
     */
    private BigDecimal maxFee;

    /**
     * 描述
     */
    private String description;

    /**
     * 真实分润比例
     */
    private Integer realProfitRatio;
}
