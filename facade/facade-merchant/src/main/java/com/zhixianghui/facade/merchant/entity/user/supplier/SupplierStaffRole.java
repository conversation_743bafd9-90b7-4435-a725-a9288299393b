package com.zhixianghui.facade.merchant.entity.user.supplier;

import java.util.Date;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商后台员工角色关联表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierStaffRole extends BaseEntity {

    private static final long serialVersionUID = -8074467641324352530L;

    /**
     * 员工id
     */
    private Long staffId;

    /**
     * 角色id
     */
    private Long roleId;

}
