package com.zhixianghui.facade.merchant.service;

import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;

/**
 * 商户信息缓存查询接口
 * <AUTHOR>
 * @date 2020/8/4
 */
public interface MerchantCacheFacade {

    /**
     * 根据商户编号查询商户信息接口
     * @param mchNo 商户编号
     * @return  商户信息
     */
    Merchant getByMchNo(String mchNo);

    /**
     * 根据商户编号查询商户信息接口
     * @param mchNo 商户编号
     * @return 商户销售信息
     */
    MerchantSaler getMerchantSalerByMchNo(String mchNo);

}
