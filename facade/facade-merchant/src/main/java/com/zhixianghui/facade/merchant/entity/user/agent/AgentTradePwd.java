package com.zhixianghui.facade.merchant.entity.user.agent;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 供应商支付密码表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgentTradePwd extends BaseEntity {

    private static final long serialVersionUID = -4329924544835813669L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 商户编号
     */
    private String agentNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否初始化密码
     */
    private Integer isInitPwd;

    /**
     * 支付密码
     */
    private String pwd;

    /**
     * 密码输错次数
     */
    private Integer pwdErrorCount;

    /**
     * 最后输错密码时间
     */
    private Date pwdErrorTime;

    /**
     * 附加信息
     */
    private String extraInfo;

    private JsonEntity jsonEntity = new JsonEntity();

    @Data
    public static class JsonEntity implements Serializable {
        /**
         * 历史密码
         */
        private List<String> historyPwdList = new ArrayList<>();

    }

    public Object getExtraInfo() {
        return JsonUtil.toString(this.jsonEntity);
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
        this.jsonEntity = JsonUtil.toBean(extraInfo, JsonEntity.class);
    }

}
