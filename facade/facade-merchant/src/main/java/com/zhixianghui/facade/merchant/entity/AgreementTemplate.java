package com.zhixianghui.facade.merchant.entity;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 协议模板表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Data
public class AgreementTemplate implements Serializable {

    private static final long serialVersionUID = 4775631941389163751L;

    private Long id;

    private Integer version = 0;

    private Date createTime = new Date();

    private Date updateTime;

    /**
     * 任务主题
     */
    private String topic;

    private String createOperator;

    private String updateOperator;

    /**
     * 过期时间选项
     */
    private Integer expireTimeType;

    /**
     * 截止时间选项
     */
    private Integer deadlineType;

    /**
     * 签署人模版信息
     */
    private String signerTemp;

    /**
     * 模版文件信息
     */
    private String fileTemp;

    /**
     * 额外信息
     */
    private String extInfo;

    /**
     * 流程模板id
     */
    private String flowTemplateId;

    /**
     * 文件模板id
     */
    private String fileTemplateId;

    /**
     * 模板文件地址
     */
    private String templateFileUrl;

}
