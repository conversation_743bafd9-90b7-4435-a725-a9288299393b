package com.zhixianghui.facade.merchant.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 发票邮寄地址表
 * <AUTHOR>
 */
@Data
public class MerchantExpressInfo extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 收件人
	 */
	private String consignee;

	/**
	 * 联系电话
	 */
	private String telephone;

	/**
	 * 省
	 */
	private String province;

	/**
	 * 市
	 */
	private String city;

	/**
	 * 区/县
	 */
	private String county;

	/**
	 * 详细地址
	 */
	private String address;

}
