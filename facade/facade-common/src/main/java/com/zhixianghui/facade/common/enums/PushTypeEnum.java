package com.zhixianghui.facade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName PushTypeEnum
 * @Description TODO
 * @Date 2023/2/22 16:40
 */
@AllArgsConstructor
@Getter
public enum PushTypeEnum {

    DAILY_ORDER(1,"日订单明细文件");

    private Integer value;

    private String desc;

    public static PushTypeEnum getEnum(int value) {
        return Arrays.stream(PushTypeEnum.values()).filter(p -> p.getValue().intValue() == value).findFirst().orElse(null);
    }
}
