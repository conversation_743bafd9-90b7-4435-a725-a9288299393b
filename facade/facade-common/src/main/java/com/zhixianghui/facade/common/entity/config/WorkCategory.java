package com.zhixianghui.facade.common.entity.config;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 自由职业者工作类目
 * <AUTHOR>
 */
@Data
public class WorkCategory extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新人
	 */
	private String updator;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 工作类目编码
	 */
	private String workCategoryCode;

	/**
	 * 工作类目名称
	 */
	private String workCategoryName;

	/**
	 * 父级服务类目id
	 */
	private Long parentId;

    /**
     * 工作岗位描述
     */
	private String workDesc;

	/**
	 * 企业从事业务
	 */
	private String businessDesc;

    /**
     * 所得计算描述
     */
    private String chargeRuleDesc;

	/**
	 * 数据库对应字段，勿直接操作，需要通过jsonEntity操作
	 */
	private String extJson;

    private JsonEntity jsonEntity = new JsonEntity();

    @Data
    public static class JsonEntity implements Serializable {
    	private List<InvoiceCategory> invoiceCategoryList = new ArrayList<>();
	}

	@Deprecated
	public String getExtJson() {
		return JsonUtil.toString(this.jsonEntity);
	}

	@Deprecated
	public void setExtJson(String extJson) {
		this.extJson = extJson;
		this.jsonEntity = JsonUtil.toBean(extJson, JsonEntity.class);
	}
}
