package com.zhixianghui.facade.common.service;

import com.zhixianghui.facade.common.entity.config.IndustryType;
import java.util.List;

/**
 * 自由职业者工作类目配置信息
 * <AUTHOR>
 * @date 2020/8/10
 **/
public interface IndustryTypeFacade {

    /**
     * 保存
     * @param industryType
     * @return
     */
    void insert(IndustryType industryType);

    /**
     * 更新
     * @param industryType
     * @return
     */
    void update(IndustryType industryType);

    /**
     * 删除
     * @param id
     * @return
     */
    void delete(long id);

    /**
     * 根据类目编码查询
     * @param industryTypeCode
     * @return
     */
    IndustryType getByCode(String industryTypeCode);

    /**
     * 查询子类目列表
     * @param parentId
     * @return
     */
    List<IndustryType> listSubIndustryType(Long parentId);

    /**
     * 查询所有
     * @return
     */
    List<IndustryType> listAll();
}
