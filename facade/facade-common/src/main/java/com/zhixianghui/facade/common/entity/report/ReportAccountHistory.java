package com.zhixianghui.facade.common.entity.report;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReportAccountHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 商户编号
     */
    private String employerNo;

    /***
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * '通道名称
     */
    private String payChannelName;

    /**
     * 通道类型
     */
    private Integer payChannelType;

    /**
     * 通道商户编号/记账本id
     */
    private String channelMerchantNo;

    /**
     * 支付宝协议号
     */
    private String agreementNo;

    /**
     * 支付宝用户id
     */
    private String alipayUserId;


    /**
     * 支付宝外标卡号
     */
    private String alipayCardNo;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 是否展示
     */
    private Integer isShow;

    /**
     * 标题
     */
    private String title;

    /**
     * 支付账户名称
     */
    private String employerName;


}
