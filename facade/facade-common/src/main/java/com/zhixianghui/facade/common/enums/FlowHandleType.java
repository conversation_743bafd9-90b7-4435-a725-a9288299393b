package com.zhixianghui.facade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 节点处理类型
 * @date 2020-08-10 14:50
 **/
@AllArgsConstructor
@Getter
@ToString
public enum FlowHandleType {
    /**
     *会签
     */
    COUNTERSIGN(1,"会签"),
    /**
     *或签
     */
    ORSIGN(2,"或签"),
    /**
     *提交审批
     */
    SUBMIT(3,"提交审批"),
    /**
     *编辑信息
     */
    EDIT(4,"编辑信息"),
    /**
     *撤回
     */
    RECALL(5,"撤回"),

    ;

    private final int value;
    private final String desc;
}
