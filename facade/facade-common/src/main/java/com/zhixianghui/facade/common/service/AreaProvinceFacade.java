package com.zhixianghui.facade.common.service;

import com.zhixianghui.facade.common.entity.config.AreaCity;
import com.zhixianghui.facade.common.entity.config.AreaMap;
import com.zhixianghui.facade.common.entity.config.AreaProvince;
import com.zhixianghui.facade.common.entity.config.AreaTown;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 地区管理--省份表 Facade类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-31
 */

public interface AreaProvinceFacade {

    List<AreaProvince> listProvinceBy(Map<String, Object> paramMap);

    List<AreaProvince> listAllProvince();

    AreaProvince getProvinceByCode(String code);

    AreaProvince getProvinceByName(String name);

    List<AreaCity> listCityBy(Map<String, Object> paramMap);

    List<AreaCity> listCityAll();

    List<AreaTown> listTownAll();

    AreaCity getCityByCode(String code);

    AreaCity getCityByName(String proviceNo, String cityName);

    List<AreaTown> listTownBy(Map<String, Object> paramMap);

    AreaTown getTownByCode(String code);

    AreaTown getTownByName(String cityNo, String name);

    void saveAreaMap(List<AreaMap> areaMaps);

    AreaMap getByZxhCode(String zxhCode);
}
