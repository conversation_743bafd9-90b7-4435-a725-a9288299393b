package com.zhixianghui.facade.common.entity.config;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-07-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BankOrganization implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 开户行号
     */
    private String bankCode;

    /**
     * 机构名
     */
    private String organizationName;


}
