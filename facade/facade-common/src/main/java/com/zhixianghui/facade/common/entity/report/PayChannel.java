package com.zhixianghui.facade.common.entity.report;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付通道表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PayChannel implements Serializable {

    private static final long serialVersionUID = -8254046325461390395L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名称
     */
    private String payChannelName;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateOperator;

    /**
     * 创建者
     */
    private String createOperator;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 账户名
     */
    private String accountName;
    /**
     * 账号
     */
    private String accountNo;
    /**
     * 银行编号
     */
    private String bankNo;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 支行
     */
    private String subBankName;
    /**
     * 联行号
     */
    private String joinBankNo;
    /**
     *开户地
     */
    private String bankAddress;
}
