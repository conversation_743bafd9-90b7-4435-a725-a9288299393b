package com.zhixianghui.facade.common.entity.config;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 银行卡BIN表
 * <AUTHOR>
 */
@Data
public class BankCardBin extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 修改人
	 */
	private String updator;

	/**
	 * 修改时间
	 */
	private java.util.Date updateTime;

	/**
	 * 卡BIN
	 */
	private String cardBin;

	/**
	 * 发卡行代码
	 */
	private String bankNo;

	/**
	 * 银行简码
	 */
	private String bankCode;

	/**
	 * 发卡行名称
	 */
	private String bankName;

	/**
	 * 卡名
	 */
	private String cardName;

	/**
	 * 卡种:1借记卡,2贷记卡,3准贷记卡,4预付费卡
	 */
	private Integer cardKind;

	/**
	 * 卡片长度
	 */
	private Integer cardLength;

	/**
	 * 状态:101无效、100有效
	 */
	private Integer status;

}
