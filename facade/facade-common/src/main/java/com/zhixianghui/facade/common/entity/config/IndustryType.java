package com.zhixianghui.facade.common.entity.config;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 行业类型
 * <AUTHOR>
 */
@Data
public class IndustryType extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新人
	 */
	private String updator;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 行业类型编码
	 */
	private String industryTypeCode;

	/**
	 * 行业类型名称
	 */
	private String industryTypeName;

	/**
	 * 父级行业类型ID
	 */
	private Long parentId;

}
