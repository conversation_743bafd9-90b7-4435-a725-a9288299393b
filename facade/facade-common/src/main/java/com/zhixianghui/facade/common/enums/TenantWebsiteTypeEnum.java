package com.zhixianghui.facade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName TenantWebsiteTypeEnum
 * @Description TODO
 * @Date 2023/3/28 9:39
 */
@Getter
@AllArgsConstructor
public enum TenantWebsiteTypeEnum {

    PLATFORM_WEBSITE(100,"平台域名","&copy; 2020 - 2021 Hjzxh.com <a href=\"https://beian.miit.gov.cn/\" target=\"_blank\">粤ICP备2020096931号</a>"),

    PRIVATE_WEBSITE(101,"私有域名","");

    private int value;

    private String desc;

    private String defaultIcp;
}
