package com.zhixianghui.facade.common.entity.config;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2023-06-07
 */
@Data
@TableName(value = "tbl_alipay_mcc")
@EqualsAndHashCode(callSuper = false)
public class AlipayMcc implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 行业类别
     */
    private String professionType;

    /**
     * 行业名称
     */
    private String professionName;

    /**
     * 特殊资质文件编码
     */
    private String qualificationCode;

    /**
     * 特殊资质文件类型
     */
    private String qualificationName;

    /**
     * 描述
     */
    private String description;


}
