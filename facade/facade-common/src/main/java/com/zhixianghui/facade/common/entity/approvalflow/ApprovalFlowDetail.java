package com.zhixianghui.facade.common.entity.approvalflow;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.enums.FlowHandleType;
import com.zhixianghui.facade.common.enums.HandleStatus;
import com.zhixianghui.facade.common.enums.PlatformSource;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 审批节点详情表
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-07
 */

@Data
public class ApprovalFlowDetail implements Serializable {

    private static final long serialVersionUID = -4942359930657923928L;

    private Long id;

    /**
     * 版本
     */
    private Integer version = 0;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 审批流程ID
     */
    private Long approvalFlowId;

    /**
     * 处理人ID
     */
    private Long handlerId;

    /**
     * 处理人名称
     */
    private String handlerName;

    /**
     * 节点类型(会签/或签) 参考{@link FlowHandleType}
     */
    private Integer handlerType;

    /**
     * 步骤序号
     */
    private Integer stepNum;

    /**
     * 节点状态 参考{@link HandleStatus}
     */
    private Integer status;

    /**
     * 执行节点操作的人(主要用于记录管理员)
     */
    private String operatorName;

    /**
     * 额外信息
     */
    private String extInfo;

    /**
     * 平台来源 参考{@link PlatformSource}
     */
    private Integer platform;

    /**
     * 是否已处理过(log)
     */
    private Boolean isHistory = Boolean.FALSE;

    /**
     *  审批意见
     */
    private String approvalOpinion;

    /**
     * 与jsonStr对应，库中存储字段为extInfo
     */
    private JsonEntity jsonEntity = new JsonEntity();

    @Data
    public static class JsonEntity implements Serializable {
        /**
         * 编辑前后的修改信息
         */
        String editDiffInfo;
    }

    @Deprecated
    public String getExtInfo() {
        return JsonUtil.toString(jsonEntity);
    }

    @Deprecated
    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
        this.jsonEntity = JsonUtil.toBean(extInfo,JsonEntity.class);
    }
}
