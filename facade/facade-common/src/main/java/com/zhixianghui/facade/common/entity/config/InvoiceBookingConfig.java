package com.zhixianghui.facade.common.entity.config;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
    * 预约开票配置表
    */
@Data
@TableName(value = "tbl_invoice_booking_config")
public class InvoiceBookingConfig implements Serializable {
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商户编号
     */
    @TableField(value = "employer_no")
    private String employerNo;

    /**
     * 商户名称
     */
    @TableField(value = "employer_name")
    private String employerName;

    /**
     * 代征主体编号
     */
    @TableField(value = "mainstay_no")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @TableField(value = "mainstay_name")
    private String mainstayName;

    /**
     * 开票周期
     */
    @TableField(value = "apply_period")
    private Integer applyPeriod;

    /**
     * 固定申请日期
     */
    @TableField(value = "apply_date")
    private Integer applyDate;

    /**
     * 开票模式
     */
    @TableField(value = "apply_type")
    private Integer applyType;

    /**
     * 开票类目
     */
    @TableField(value = "invoice_category_code")
    private String invoiceCategoryCode;

    /**
     * 发票类目名称
     */
    @TableField(value = "invoice_category_name")
    private String invoiceCategoryName;

    /**
     * 创建/更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建/更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    @TableField(value = "status")
    private Integer status;

    @TableField("invoice_type")
    private Integer invoiceType;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_EMPLOYER_NO = "employer_no";

    public static final String COL_EMPLOYER_NAME = "employer_name";

    public static final String COL_MAINSTAY_NO = "mainstay_no";

    public static final String COL_MAINSTAY_NAME = "mainstay_name";

    public static final String COL_APPLY_PERIOD = "apply_period";

    public static final String COL_APPLY_DATE = "apply_date";

    public static final String COL_APPLY_TYPE = "apply_type";

    public static final String COL_INVOICE_CATEGORY_CODE = "invoice_category_code";

    public static final String COL_INVOICE_CATEGORY_NAME = "invoice_category_name";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_UPDATE_BY = "update_by";

    public static final String COL_STATUS = "status";
}
