package com.zhixianghui.facade.common.entity.individualproxy;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.zhixianghui.facade.common.handler.IndividualProxyQuoteHandler;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
    * 代开供应商管理表
    */
@Data
@TableName(value = "tbl_mainstay_individual_proxy",autoResultMap = true)
public class MainstayIndividualProxy implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 代征主体编号
     */
    @TableField(value = "mainstay_no")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @TableField(value = "mainstay_name")
    private String mainstayName;

    /**
     * 版本号
     */
    @TableField(value = "version")
    @Version
    private Integer version;

    /**
     * 服务费比例
     */
    @TableField(value = "service_fee_ratio")
    private BigDecimal serviceFeeRatio;

    /**
     * 单笔开票金额最低
     */
    @TableField(value = "single_limit_min")
    private BigDecimal singleLimitMin;

    /**
     * 单笔开票金额最大
     */
    @TableField(value = "single_limit_max")
    private BigDecimal singleLimitMax;

    /**
     * 累计最低开票金额
     */
    @TableField(value = "accumulative_limit_min")
    private BigDecimal accumulativeLimitMin;

    /**
     * 累计最高开票金额
     */
    @TableField(value = "accumulative_limit_max")
    private BigDecimal accumulativeLimitMax;

    /**
     * 激活的类型 0：单笔金额 1 累计金额 2 兼有
     */
    @TableField(value = "active_limit_type")
    private Integer activeLimitType;

    /**
     * 年龄限制-最低
     */
    @TableField(value = "age_limit_min")
    private Integer ageLimitMin;

    /**
     * 年龄限制-最高
     */
    @TableField(value = "age_limit_max")
    private Integer ageLimitMax;

    /**
     * 是否限制年龄 0 不限 1 限制
     */
    @TableField(value = "acitive_age_limit")
    private Boolean acitiveAgeLimit;

    /**
     * 十万以下是否免征
     */
    @TableField(value = "levy_less_than_10w")
    private Boolean levyLessThan10w;

    /**
     * 业务凭证要求
     */
    @TableField(value = "requied_biz_vouchers", typeHandler = FastjsonTypeHandler.class)
    private List<Integer> requiedBizVouchers;

    /**
     * 是否地域限制 0 否 1 是
     */
    @TableField(value = "area_limit_status")
    private Boolean areaLimitStatus;

    /**
     * 限制开票地域
     */
    @TableField(value = "area_surport", typeHandler = FastjsonTypeHandler.class)
    private List<AreaItem> areaSurport;

    /**
     * 特殊说明
     */
    @TableField(value = "special_remark")
    private String specialRemark;

    /**
     * 赋税报价 详细字段见
     * {@link com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote}
     */
    @TableField(value = "tax_quote", typeHandler = IndividualProxyQuoteHandler.class)
    private List<IndividualProxyQuote> taxQuote;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    private Boolean status;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_MAINSTAY_NO = "mainstay_no";

    public static final String COL_MAINSTAY_NAME = "mainstay_name";

    public static final String COL_VERSION = "version";

    public static final String COL_SERVICE_FEE_RATIO = "service_fee_ratio";

    public static final String COL_SINGLE_LIMIT_MIN = "single_limit_min";

    public static final String COL_SINGLE_LIMIT_MAX = "single_limit_max";

    public static final String COL_ACCUMULATIVE_LIMIT_MIN = "accumulative_limit_min";

    public static final String COL_ACCUMULATIVE_LIMIT_MAX = "accumulative_limit_max";

    public static final String COL_ACTIVE_LIMIT_TYPE = "active_limit_type";

    public static final String COL_AGE_LIMIT_MIN = "age_limit_min";

    public static final String COL_AGE_LIMIT_MAX = "age_limit_max";

    public static final String COL_ACITIVE_AGE_LIMIT = "acitive_age_limit";

    public static final String COL_LEVY_LESS_THAN_10W = "levy_less_than_10w";

    public static final String COL_REQUIED_BIZ_VOUCHERS = "requied_biz_vouchers";

    public static final String COL_AREA_LIMIT_STATUS = "area_limit_status";

    public static final String COL_AREA_NOT_SURPORT = "area_not_surport";

    public static final String COL_SPECIAL_REMARK = "special_remark";

    public static final String COL_TAX_QUOTE = "tax_quote";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_CREATE_BY = "create_by";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_UPDATE_BY = "update_by";

    public static final String COL_STATUS = "status";

    public static void extendWhere(QueryWrapper<MainstayIndividualProxy> queryWrapper, Map<String,Object> param) {

        queryWrapper.between(param.get("createTimeBegin") != null && param.get("createTimeEnd") != null, COL_CREATE_TIME, param.get("createTimeBegin"), param.get("createTimeEnd"));
        queryWrapper.like(StringUtils.isNotBlank((String) param.get("mainstayName")), COL_MAINSTAY_NAME, param.get("mainstayName"));

        if (StringUtils.isNotBlank((String) param.get("orderField"))) {
            queryWrapper.orderByDesc(StrUtil.toUnderlineCase((String) param.get("orderField")).toUpperCase());
        }else {
            queryWrapper.orderByDesc(COL_ID);
        }
    }
}
