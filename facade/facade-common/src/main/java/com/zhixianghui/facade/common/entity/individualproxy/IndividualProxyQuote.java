package com.zhixianghui.facade.common.entity.individualproxy;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class IndividualProxyQuote implements Serializable {
    private static final long serialVersionUID = 4575663707053060526L;
    /**
     * 开票类目编码
     */
    @NotBlank(message = "开票类目编码不能为空")
    private String invoiceCategoryCode;
    /**
     * 开票类目名称
     */
    @NotBlank(message = "开票类目名称不能为空")
    private String invoiceCategoryName;
    /**
     * 个税比例
     */
    private BigDecimal taxRatio;
    /**
     * 增值税比例
     */
    private BigDecimal vatRatio;
    /**
     * 地方教育附加税
     */
    private BigDecimal localEduAddTaxRatio;
    /**
     * 教育附加税
     */
    private BigDecimal eduAddTaxRatio;
    /**
     * 城建税
     */
    private BigDecimal buildingTaxRatio;
    /**
     * 印花税
     */
    private BigDecimal stampDutyRatio;

    private List<IndividualProxyQuote> subcategorys;
}
