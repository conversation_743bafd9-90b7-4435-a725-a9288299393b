package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import java.util.List;
import java.util.Map;

/**
 * 发票类目配置信息
 * <AUTHOR>
 * @date 2020/8/10
 **/
public interface InvoiceCategoryFacade {

    /**
     * 保存
     * @param invoiceCategory
     * @return
     */
    void insert(InvoiceCategory invoiceCategory);

    /**
     * 更新
     * @param invoiceCategory
     * @return
     */
    void update(InvoiceCategory invoiceCategory);

    /**
     * 删除
     * @param id
     * @return
     */
    void delete(long id);

    /**
     * 根据类目编码查询
     * @param categoryCode
     * @return
     */
    InvoiceCategory getByCategoryCode(String categoryCode);

    /**
     * 列表查询
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<InvoiceCategory>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    List<InvoiceCategory> listAll();
}
