package com.zhixianghui.facade.common.entity.tenant;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2023-03-28
 */
@Data
@TableName(value = "tbl_tenant_website")
@EqualsAndHashCode(callSuper = false)
public class TenantWebsite implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long tenantId;

    private String website;

    private Integer siteType;

    private String icpNo;
}
