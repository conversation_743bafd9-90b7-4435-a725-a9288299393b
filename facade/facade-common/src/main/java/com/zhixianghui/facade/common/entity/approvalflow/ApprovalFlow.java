package com.zhixianghui.facade.common.entity.approvalflow;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 审批流程表
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-07
 */
@Data
public class ApprovalFlow implements Serializable {

    private static final long serialVersionUID = -141108321006763610L;

    private Long id;

    /**
     * 版本
     */
    private Integer version = 0;

    /**
     * 步骤序号
     */
    private Integer stepNum;

    /**
     * 发起人
     */
    private Long initiatorId;

    /**
     * 发起人名称
     */
    private String initiatorName;

    /**
     * 流程主题(创建商户等) {@link FlowTopicType}
     */
    private Integer flowTopicType;

    /**
     * 流程主题名称
     */
    private String flowTopicName;

    /**
     * 流程开始时间
     */
    private Date createTime = new Date();

    /**
     * 流程更新时间
     */
    private Date updateTime;

    /**
     * 流程结束时间
     */
    private Date endTime;

    /**
     * 流程状态 {@link FlowStatus}
     */
    private Integer status = FlowStatus.PENDING.getValue();

    /**
     * 额外信息(备注,审批信息等)
     */
    private String extInfo;

    /**
     * 平台来源
     */
    private Integer platform;

    /**
     * json字段
     */
    private String jsonInfo;
    /**
     * 处理者姓名
     */
    private String handleName;
    /**
     * 处理者id
     */
    private Long handleId;

    //-----json字段额外处理-----
    @Deprecated
    public String getJsonInfo() {
        return JsonUtil.toString(this.jsonEntity);
    }

    @Deprecated
    public void setJsonInfo(String jsonInfo) {
        this.jsonEntity = JsonUtil.toBean(jsonInfo,JsonEntity.class);
        this.jsonInfo = jsonInfo;
    }

    private JsonEntity jsonEntity = new JsonEntity();

    @Data
    public static class JsonEntity implements Serializable {

        private static final long serialVersionUID = 232145962792352238L;

        /**
         * 维度字段，用于区分审批权限 一般维度(发起人id+平台=仅发起人可见)不可用时的特殊处理
         * 如 合伙人维度使用合伙人编号，可登陆该合伙人的都可见
         */
        private String dimension;
    }


}
