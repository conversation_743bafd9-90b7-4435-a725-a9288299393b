package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.EmployerAccountInfoDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;

import java.util.List;
import java.util.Map;

/**
 * 用工企业账户表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
public interface EmployerAccountInfoFacade {

    /**
     * 分页查询用工企业账户
     * @param paramMap 查询参数
     * @param pageParam 分页参数
     * @return 分页信息
     */
    PageResult<List<EmployerAccountInfo>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    public PageResult<List<Map<String, String>>> groupByMch(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 获取指定供应商-用工企业信息
     * @param employerNo 用工企业编号
     * @param mainstayNo 供应商编号
     * @return 供应商-用工企业信息列表
     */
    List<EmployerAccountInfo> listByEmployerNoAndMainstayNo(String employerNo, String mainstayNo);

    /**
     * 批量更新
     * @param list 更新数据列表
     */
    void batchUpdate(List<EmployerAccountInfo> list);

    /**
     * 自定义分页查询用工企业账户
     * @param paramMap 查询参数
     * @param pageParam 分页参数
     * @return 分页信息
     */
    PageResult<List<EmployerAccountInfoDto>> listCustomPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 根据用工编号+代征主体编号+通道类型获取用工企业账户
     * @param employerNo 用工企业编号
     * @param mainstayNo 代征主体编号
     * @param channelType 通道类型
     * @return 用工企业账户
     */
    EmployerAccountInfo getByEmployerNoAndMainstayNoAndChannelType(String employerNo, String mainstayNo, Integer channelType);

    List<EmployerAccountInfo> listBy(Map<String, Object> paramMap);

    EmployerAccountInfo getByEmployerNoAndMainstayNoAndChannelNo(String mchNo, String mainstayNo, String channelNo);

    EmployerAccountInfo getByEmployerNoAndParentMchNoAndChannelNo(String mchNo, String parentMerchantNo, String channelNo);

    /**
     * 根据用工企业名称、代征主体账号+渠道编码获取
     * @param mchNo
     * @param parentMerchantNo
     * @param channelNo
     * @return
     */
    List<EmployerAccountInfo> getByEmployerNameAndParentMchNoAndChannelNo(String mchNo, String parentMerchantNo, String channelNo);

    void forceDelete(String mchNo);

    EmployerAccountInfo getOneBySubMerchantNoAndPayChannelNo(String payeeIdentity, String name);

    String getAlipayCardNo();

    void changeAccount(Long id, String employerNo,String updator) throws BizException;

    EmployerAccountInfo getById(Long id);
}
