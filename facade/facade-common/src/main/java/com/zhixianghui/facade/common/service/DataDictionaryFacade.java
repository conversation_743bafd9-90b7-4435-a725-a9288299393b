package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.vo.PortalDataDictionaryQueryVO;

import java.util.List;
import java.util.Map;

/**
 * DataDictionaryFacade
 *
 * <AUTHOR>
 * @date 2019/11/14
 */
public interface DataDictionaryFacade {

    DataDictionary getDataDictionaryById(long id);

    void deleteDataDictionaryById(long id);

    void createDataDictionary(DataDictionary dataDictionary);

    void updateDataDictionary(DataDictionary dataDictionary);

    PageResult<List<DataDictionary>> listDataDictionaryPage(Map<String, Object> paramMap, PageParam pageParam);

    List<DataDictionary> listAllDataDictionary();

    DataDictionary getDataDictionaryByName(String dataName);

    /**
     * 字典导出
     * @param id    字典主键
     * @param operator
     */
    void export(List<Long> id, String operator);

    /**
     *
     * @param idList    字典主键
     * @return  字典
     */
    List<DataDictionary> listDataDictionary(List<Long> idList);

    void create(List<DataDictionary> list);

    String getSystemConfig(String field);

    List<DataDictionary> list(PortalDataDictionaryQueryVO portalDataDictionaryQueryVO);
}
