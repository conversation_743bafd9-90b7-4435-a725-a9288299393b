package com.zhixianghui.facade.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-10 15:25
 **/
@Data
public class MainstayChannel implements Serializable {

    private static final long serialVersionUID = 8263289622818880038L;

    /**
     * 支付通道编号
     */
    private String payChannelNo;

    /**
     * 通道商户编号
     */
    private String channelMchNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateOperator;

    /**
     * 创建者
     */
    private String createOperator;
}
