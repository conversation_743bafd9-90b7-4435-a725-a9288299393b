package com.zhixianghui.facade.common.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 更新额外数据Vo
 * @date 2020-08-19 16:45
 **/
@Data
public class UpdateExtInfoVo implements Serializable {

    private static final long serialVersionUID = 4917972283135409322L;

    /**
     * 流程id
     */
    @NotNull(message = "流程id不能为空")
    private Long approvalFlowId;

    /**
     * 修改的信息
     */
    @NotNull(message = "修改的信息不能为空")
    private String extInfo;

    /**
     * 修改人id
     */
    private Long handlerId;

    /**
     * 修改人名称
     */
    private String handlerName;

    /**
     * 修改人来源
     */
    private Integer platform;

}
