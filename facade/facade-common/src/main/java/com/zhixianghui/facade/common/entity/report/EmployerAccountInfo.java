package com.zhixianghui.facade.common.entity.report;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 用工企业账户表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployerAccountInfo implements Serializable {

    private static final long serialVersionUID = -4402403991269616844L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 用工企业名称（出款账户名称）
     */
    private String employerName;

    /**
     * 用户企业名称（真实商户名称）
     */
    private String mchName;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 通道名称
     */
    private String payChannelName;
    private String channelName;

    /**
     * 支付通道编号
     */
    private String payChannelNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 子商户编号
     */
    private String subMerchantNo;

    /**
     * 父商户编号
     */
    private String parentMerchantNo;

    /**
     * 密钥
     */
    private String employerKey;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateOperator;

    /**
     * 创建者
     */
    private String createOperator;

    /**
     * 子商户协议号
     */
    private String subAgreementNo;

    /**
     * 父商户协议号
     */
    private String parentAgreementNo;

    /**
     * 父商户alipayId
     */
    private String parentAlipayUserId;

    /**
     * 子商户alipayId
     */
    private String subAlipayUserId;

    /**
     * 支付宝外标卡号
     */
    private String alipayCardNo;

    @JSONField(serialize = false)
    public EmployerAccountInfo getEmployerAccountInfo(){
        return this;
    }
}
