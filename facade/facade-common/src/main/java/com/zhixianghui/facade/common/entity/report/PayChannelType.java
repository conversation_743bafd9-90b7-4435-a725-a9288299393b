package com.zhixianghui.facade.common.entity.report;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PayChannelType implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道类型
     */
    private Integer type;


}
