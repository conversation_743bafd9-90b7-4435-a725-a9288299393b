package com.zhixianghui.facade.common.entity.notificaton;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
    * 消息记录表
    */
@Data
@TableName(value = "tbl_notification_record")

public class NotificationRecord implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    @Version
    private Integer version;

    /**
     * 创建时间
     */
    @TableField(value = "CREATION_TIME",fill = FieldFill.INSERT)
    private LocalDateTime creationTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 推送时间
     */
    @TableField(value = "PUBLISH_TIME")
    private LocalDateTime publishTime;

    /**
     * 通知类型
     */
    @TableField(value = "NOTIFICATION_TYPE")
    private Integer notificationType;

    /**
     * 通知标题
     */
    @TableField(value = "NOTIFICATION_TITLE")
    private String notificationTitle;

    /**
     * 通知内容
     */
    @TableField(value = "NOTIFICATION_CONTENT")
    private String notificationContent;

    /**
     * 接收人类型
     */
    @TableField(value = "NOTIFICATION_RECEIVER_TYPE")
    private Integer notificationReceiverType;

    /**
     * 接收人列表
     */
    @TableField(value = "NOTIFICATION_RECEIVERS")
    private String notificationReceivers;

    /**
     * 推送规则类型
     */
    @TableField(value = "PUBLISH_TYPE")
    private Integer publishType;

    /**
     * 推送时间段开始时间
     */
    @TableField(value = "PUSH_TIME")
    private Date pushTime;


    /**
     * 推送状态
     */
    @TableField(value = "PUBLISH_STATUS")
    private Integer publishStatus;

    @TableField(value = "DELETED")
    @TableLogic
    private Short deleted;

    @TableField(value = "POP")
    private boolean pop;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_CREATION_TIME = "CREATION_TIME";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_PUBLISH_TIME = "PUBLISH_TIME";

    public static final String COL_NOTIFICATION_TYPE = "NOTIFICATION_TYPE";

    public static final String COL_NOTIFICATION_TITLE = "NOTIFICATION_TITLE";

    public static final String COL_NOTIFICATION_CONTENT = "NOTIFICATION_CONTENT";

    public static final String COL_NOTIFICATION_RECEIVER_TYPE = "NOTIFICATION_RECEIVER_TYPE";

    public static final String COL_NOTIFICATION_RECEIVERS = "NOTIFICATION_RECEIVERS";

    public static final String COL_PUBLISH_TYPE = "PUBLISH_TYPE";

    public static final String COL_PUSH_TIME = "PUSH_TIME";

    public static final String COL_PUBLISH_STATUS = "PUBLISH_STATUS";
}
