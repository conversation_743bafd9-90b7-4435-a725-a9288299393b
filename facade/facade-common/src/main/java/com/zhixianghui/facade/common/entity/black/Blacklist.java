package com.zhixianghui.facade.common.entity.black;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2023-03-01
 */
@Data
@TableName(value = "tbl_blacklist")
@EqualsAndHashCode(callSuper = false)
public class Blacklist implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 主体编号
     */
    private String subjectNo;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 功能标记
     */
    private String tag;

    /**
     * 功能描述
     */
    private String tagDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;


}
