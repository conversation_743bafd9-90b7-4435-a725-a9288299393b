package com.zhixianghui.facade.common.service;

import com.zhixianghui.facade.common.entity.report.ReportAccountHistory;
import com.zhixianghui.facade.common.vo.ReportAccountHistoryVo;

import java.util.List;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-07-05
 */
public interface ReportAccountHistoryFacade {

    List<ReportAccountHistoryVo> getMerchantAccount(String employerNo, String mainstayNo);

    List<ReportAccountHistory> getByChannelType(String employerNo, String mainstayNo, Long channelType);

    void changeStatus(Long id, Integer isShow);

    void changeTitle(Long id, String title);

    ReportAccountHistory getByChannelMerchantNoAndPayChannelNo(String channelMerchantNo, String payChannelNo);
}
