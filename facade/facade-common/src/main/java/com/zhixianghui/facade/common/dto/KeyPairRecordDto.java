package com.zhixianghui.facade.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-28 18:39
 **/
@Data
public class KeyPairRecordDto implements Serializable {

    private static final long serialVersionUID = -1741413905918702717L;

    private Long id;
    private Integer version;
    /**
     * 创建时间
     */
    protected Date createTime;

    /**
     * 更新者
     */
    private String updator;

    /**
     * 渠道编号
     */
    private String channelNo;

    /**
     * 渠道商户号
     */
    private String channelMchNo;

    /**
     * 渠道商户私钥
     */
    private String mchPrivateKey;

    /**
     * 渠道商户公钥
     */
    private String mchPublicKey;

    /**
     * 渠道公钥
     */
    private String channelPublicKey;

    /**
     * 加密id
     * 必须在敏感信息之前设值
     */
    private Long encryptKeyId;

    private String channelLoginUser;

    private String channelPlatNo;
}
