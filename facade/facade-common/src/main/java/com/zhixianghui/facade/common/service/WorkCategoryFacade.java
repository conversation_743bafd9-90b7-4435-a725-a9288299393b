package com.zhixianghui.facade.common.service;

import com.zhixianghui.facade.common.entity.config.WorkCategory;

import java.util.List;

/**
 * 自由职业者工作类目配置信息
 * <AUTHOR>
 * @date 2020/8/10
 **/
public interface WorkCategoryFacade {

    /**
     * 保存
     * @param workCategory
     * @return
     */
    void insert(WorkCategory workCategory);

    /**
     * 更新
     * @param workCategory
     * @return
     */
    void update(WorkCategory workCategory);

    /**
     * 删除
     * @param id
     * @return
     */
    void delete(long id);

    /**
     * 根据类目编码查询
     * @param categoryCode
     * @return
     */
    WorkCategory getByCategoryCode(String categoryCode);

    /**
     * 查询子类目列表
     * @param parentId
     * @return
     */
    List<WorkCategory> listSubCategory(Long parentId);

    /**
     * 查询所有
     * @return
     */
    List<WorkCategory> listAll();
}
