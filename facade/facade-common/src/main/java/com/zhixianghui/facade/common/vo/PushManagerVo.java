package com.zhixianghui.facade.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName PushManagerVo
 * @Description TODO
 * @Date 2023/2/24 16:21
 */
@Data
public class PushManagerVo implements Serializable {

    private Integer id;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 推送类型
     */
    private Integer pushType;

    /**
     * sftp ip地址
     */
    private String sftpIp;

    /**
     * sftp端口号
     */
    private String sftpPort;

    /**
     * sftp路径
     */
    private String sftpPath;

    /**
     * 服务器类型
     */
    private Integer serverType;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
