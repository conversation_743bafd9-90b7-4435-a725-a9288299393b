package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.PushManagerDto;
import com.zhixianghui.facade.common.entity.push.PushManager;
import com.zhixianghui.facade.common.vo.PushManagerVo;

import java.util.List;
import java.util.Map;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-02-21
 */
public interface PushManagerFacade {

    void add(PushManagerDto pushManagerDto,String createBy);

    void deleteById(Long id);

    void update(PushManager pushManager, String realName);

    PageResult<List<PushManagerVo>> selectPage(PushManager pushManager, int pageSize, int pageCurrent);

    List<PushManager> listBy(Map<String,Object> paramMap);
}
