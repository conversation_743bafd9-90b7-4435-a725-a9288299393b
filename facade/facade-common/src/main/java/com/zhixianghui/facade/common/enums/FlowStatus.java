package com.zhixianghui.facade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 流程状态枚举
 * @date 2020-08-07 15:00
 **/
@AllArgsConstructor
@Getter
@ToString
public enum FlowStatus {
    /**
     *已完成
     */
    FINISHED(100, "已完成"),
    /**
     *待审批
     */
    PENDING(101, "待审批"),
    ;

    private final int value;
    private final String desc;
}
