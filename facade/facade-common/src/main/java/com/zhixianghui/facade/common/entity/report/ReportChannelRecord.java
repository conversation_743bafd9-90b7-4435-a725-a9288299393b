package com.zhixianghui.facade.common.entity.report;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 通道报备记录表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ReportChannelRecord implements Serializable {

    private static final long serialVersionUID = 922051381495917294L;

    private Long id;

    /**
     * 版本
     */
    private Integer version = 0;

    /**
     * 日志流水号(搭配各通道唯一标识)
     */
    private String serialNo;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 通道类型
     */
    private Integer merchantType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名称
     */
    private String payChannelName;

    /**
     * 报备人
     */
    private String reporter;

    /**
     * 报备状态(详细看通道信息)
     */
    private Integer status;

    /**
     * 通道返回信息
     */
    private String respData;

    /**
     * 简要错误描述
     */
    private String errMsg;


}
