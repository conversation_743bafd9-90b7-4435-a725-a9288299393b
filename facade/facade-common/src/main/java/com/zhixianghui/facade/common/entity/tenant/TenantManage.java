package com.zhixianghui.facade.common.entity.tenant;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2023-03-24
 */
@Data
@TableName(value = "tbl_tenant_manage")
@EqualsAndHashCode(callSuper = false)
public class TenantManage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Version
    private Integer version;

    /**
     * 租户编号
     */
    private String tenantNo;

    /**
     * 租户名称
     */
    private String tenantName;


    /**
     * 站点名称
     */
    private String webSiteName;

    /**
     * 合作开始时间
     */
    private Date startTime;

    /**
     * 合作截止时间
     */
    private Date endTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updator;


}
