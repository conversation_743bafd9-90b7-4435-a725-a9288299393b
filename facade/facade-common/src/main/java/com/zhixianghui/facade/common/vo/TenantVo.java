package com.zhixianghui.facade.common.vo;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TenantVo
 * @Description TODO
 * @Date 2023/3/28 9:23
 */
@Data
public class TenantVo implements Serializable {

    private Long id;

    @NotBlank(message = "租户编号不能为空")
    private String tenantNo;

    @NotBlank(message = "租户名称不能为空")
    private String tenantName;


    @NotBlank(message = "站点名称不能为空")
    private String webSiteName;

    @NotNull(message = "合作开始时间不能为空")
    private Date startTime;

    @NotNull(message = "合作结束时间不能为空")
    private Date endTime;

    private Date updateTime;

    private Integer status;

    @NotEmpty(message = "租户域名不能为空")
    @Valid
    private List<TenantLinkVo> link;
}
