package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.EmployerAccountInfoDto;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import java.util.List;
import java.util.Map;

/**
 * 代征关系表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
public interface EmployerMainstayRelationFacade {

    /**
     * 建立代征关系
     * @param employerMainstayRelation 代征关系
     */
    void create(EmployerMainstayRelation employerMainstayRelation);

    /**
     * 更新状态
     * @param id 代征关系id
     * @param status 状态
     * @param operator 更改者
     */
    void changeStatus(Long id, Integer status, String operator);

    /**
     * 编辑代征关系
     * @param employerMainstayRelation 代征关系
     */
    void updateIfNotNull(EmployerMainstayRelation employerMainstayRelation);

    /**
     * 删除代征关系
     * @param id 关系id
     */
    void deleteById(Long id);

    void deleteByMainstayNo(String mainstayNo);

    /**
     * 分页查询代征关系
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return 代征关系
     */
    PageResult<List<EmployerMainstayRelation>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 根据id获取代征关系
     * @param id 代征关系id
     * @return 代征关系
     */
    EmployerMainstayRelation getById(Long id);

    List<EmployerMainstayRelation> listBy(Map<String, Object> paramMap);

    void updateEmployerMainstayRelation(EmployerMainstayRelation employerMainstayRelation);

    EmployerMainstayRelation getByEmployerNoAndMchNo(String employerNo, String mainstayNo);

    EmployerMainstayRelation getByExternalEnterpriseSn(String externalEnterpriseSn);

    void batchUpdate(Integer status, List<Long> ids);
}
