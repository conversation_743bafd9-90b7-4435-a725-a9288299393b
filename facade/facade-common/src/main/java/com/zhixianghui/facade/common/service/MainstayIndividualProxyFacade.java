package com.zhixianghui.facade.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote;
import com.zhixianghui.facade.common.entity.individualproxy.MainstayIndividualProxy;

import java.util.List;
import java.util.Map;

public interface MainstayIndividualProxyFacade {
    MainstayIndividualProxy addIndividualProxyMainstay(MainstayIndividualProxy mainstayIndividualProxy) throws BizException;

    MainstayIndividualProxy updateIndividualProxyMainstay(MainstayIndividualProxy mainstayIndividualProxy) throws BizException;

    MainstayIndividualProxy updateStatus(Integer id, Boolean status) throws BizException;

    MainstayIndividualProxy addIndividualProxyQuote(Integer id,String parentCateCode, IndividualProxyQuote individualProxyQuote) throws BizException;

    MainstayIndividualProxy updateIndividualProxyQuote(long id, IndividualProxyQuote individualProxyQuote) throws BizException;

    MainstayIndividualProxy deleteIndividualProxyQuote(Integer id, String invoiceCategoryCode) throws BizException;

    Page<MainstayIndividualProxy> listPage(Page<MainstayIndividualProxy> page, Map<String, Object> paramMap);

    List<MainstayIndividualProxy> listAll(Map<String, Object> paramMap);

    MainstayIndividualProxy getByMainstayNo(String mainstayNo);
}
