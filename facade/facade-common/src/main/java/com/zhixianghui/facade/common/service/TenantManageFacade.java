package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.tenant.TenantManage;
import com.zhixianghui.facade.common.vo.TenantVo;

import java.util.List;
import java.util.Map;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-03-24
 */
public interface TenantManageFacade {

    void add(TenantVo tenantVo, String realName);

    void delete(Long id);

    void update(TenantVo tenantVo, String realName);

    void updateStatus(Long id, Integer status);

    PageResult<List<TenantVo>> selectPage(TenantManage tenantManage, int pageSize, int pageCurrent);

    Map<String,Object> getTenant(String uri);

    TenantManage getTenantByTenantNo(String mainstayNo);
}
