package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 审批流程表 Facade类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-07
 */

public interface ApprovalFlowFacade {

    /**
     * 创建审批流程
     * @param approvalFlow 创建审批流信息
     * return ApprovalInfoVo
     */
    ApprovalInfoVo createFlow(ApprovalFlow approvalFlow);

    /**
     * 根据平台和用户id获取审批流
     * @param handlerId 用户id
     * @param platformSource 平台来源 {@link PlatformSource}
     * @param flowParamMap 主流程筛选参数
     * @param pageParam 分页参数
     * @return 分页的审批流
     */
    PageResult<List<ApprovalFlow>> listByHandlerIdAndPlatformSource(Long handlerId, Integer platformSource, Map<String,Object> flowParamMap, PageParam pageParam);

    /**
     * 根据平台和用户id获取待用户审批的审批流
     * @param operatorId 用户id
     * @param platformSource 平台来源 {@link PlatformSource}
     * @param flowParamMap 主流程筛选参数
     * @param pageParam 分页参数
     * @return 分页的审批流
     */
    PageResult<List<ApprovalFlow>> listPendingByOperatorIdAndPlatformSource(Long operatorId, Integer platformSource, Map<String,Object> flowParamMap, PageParam pageParam);

    /**
     * 获取某用户发起的审批流
     * @param initiatorId  发起人id
     * @param platformSource  平台来源 {@link PlatformSource}
     * @param flowParamMap 其余筛选参数
     * @param pageParam 分页参数
     * @return 分页的审批流
     */
    PageResult<List<ApprovalFlow>> listByInitiatorIdAndPlatformSource(Long initiatorId, Integer platformSource, Map<String,Object> flowParamMap, PageParam pageParam);

    /**
     * 分页查询审批流
     * @param paramMap  过滤条件
     * @param pageParam 分页参数
     * @return 分页的审批流
     */
    PageResult<List<ApprovalFlow>> listPage(Map<String,Object> paramMap, PageParam pageParam);

    /**
     * 根据id获取审批流程
     * @param id  审批流程id
     * @param handlerId  查询人id（鉴权）
     * @param isAdmin 是否为管理员
     * @return 审批流程对象
     */
    ApprovalFlow getByIdAndHandlerId(Long id, Long handlerId, boolean isAdmin);

    /**
     * 根据id取消审批流程
     * @param id  审批流程id
     * @param handlerId  查询人id（鉴权）
     * @param handlerName  查询人姓名（记录）
     * @param platform  来源（记录）
     * @param isAdmin  是否超级管理员
     */
    void cancelApproval(Long id, Long handlerId, String handlerName, Integer platform, boolean isAdmin);

    /**
     * 详情id反查主审批流程
     * @param detailId 详情id
     * @return 审批流程对象
     */
    ApprovalFlow getByDetailId(Long detailId);

    void updateExtInfo(UpdateExtInfoVo updateExtInfoVo, boolean isAdmin);

    /**
     * 合伙人后台编辑信息
     * (权限校验需在web端)
     * @param updateExtInfoVo
     */
    void updateExtInfoForAgent(UpdateExtInfoVo updateExtInfoVo);

    ApprovalFlow getOne(Map<String, Object> paramMap);
}
