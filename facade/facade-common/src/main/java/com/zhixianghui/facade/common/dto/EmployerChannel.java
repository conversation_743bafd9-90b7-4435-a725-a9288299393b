package com.zhixianghui.facade.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-12 15:21
 **/
@Data
public class EmployerChannel implements Serializable {

    private static final long serialVersionUID = -5149763275517781652L;

    /**
     * 密钥
     */
    private String employerKey;

    /**
     * 通道名称
     */
    private String payChannelName;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 支付通道编号
     */
    private String payChannelNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 子商户编号
     */
    private String subMerchantNo;

    /**
     * 父商户编号
     */
    private String parentMerchantNo;

    /**
     * 外标卡号
     */
    private String alipayCardNo;
}
