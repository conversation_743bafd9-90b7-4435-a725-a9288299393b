package com.zhixianghui.facade.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PayChannelDto
 * @Description TODO
 * @Date 2021/6/25 11:52
 */
@Data
public class PayChannelDto implements Serializable {
    private static final long serialVersionUID = -4864663996794968949L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名称
     */
    private String payChannelName;

    /**
     * 通道类型
     */
    private List<Integer> channelType;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateOperator;

    /**
     * 创建者
     */
    private String createOperator;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 账户名
     */
    private String accountName;
    /**
     * 账号
     */
    private String accountNo;
    /**
     * 银行编号
     */
    private String bankNo;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 支行
     */
    private String subBankName;
    /**
     * 联行号
     */
    private String joinBankNo;
    /**
     *开户地
     */
    private String bankAddress;

    /**
     * 类型合并
     */
    private String typeConcat;
}
