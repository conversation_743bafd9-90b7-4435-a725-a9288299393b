package com.zhixianghui.facade.common.entity.config;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 发票类目配置
 * <AUTHOR>
 */
@Data
public class InvoiceCategory extends BaseEntity{

	private static final long serialVersionUID = 1L;

	private Date updateTime;
	/**
	 * 更新人
	 */
	private String updator;

	/**
	 * 发票类目编码
	 */
	private String invoiceCategoryCode;

	/**
	 * 发票类目名称
	 */
	private String invoiceCategoryName;

}
