package com.zhixianghui.facade.common.entity.config;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 银行信息表
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-31
 */
@Data
public class BankInfo implements Serializable {

    private static final long serialVersionUID = -8765980346498173106L;

    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 银行编号：银行简称，例如：工商银行(ICBC)
     */
    private String bankCode;

    /**
     * 银行行号(联行号)
     */
    private String bankChannelNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行开户行号
     */
    private String openingBankNo;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地区编号
     */
    private Integer cityCode;

}
