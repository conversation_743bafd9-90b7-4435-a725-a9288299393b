package com.zhixianghui.facade.common.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.util.utils.StringUtil;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-04 10:37
 **/
public class ContrastObjUtils {

    /**
     * 比较对象所有field
     * 限制是要有明确的class类型
     * @param oldBean 旧数据
     * @param newBean 新数据
     * @return 修改的值
     */
    public static String compareObj(Object oldBean, Object newBean) {
        StringBuilder result = new StringBuilder();
        try {
            Class clazz = oldBean.getClass();
            Field[] fields = oldBean.getClass().getDeclaredFields();
            int i = 1;

            // loop all field
            for (Field field : fields) {
                if ("serialVersionUID".equals(field.getName())) {
                    continue;
                }
                PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                Method getMethod = pd.getReadMethod();
                Object o1 = getMethod.invoke(oldBean);
                Object o2 = getMethod.invoke(newBean);
                if (o1 == null || o2 == null) {
                    continue;
                }
                if (!o1.toString().equals(o2.toString())) {
                    if (i != 1) {
                        result.append(";");
                    }
                    // 要显示的字段名
                    String fieldName = field.getName();

                    result.append(i).append("、").append(fieldName).append("，oldValue：").append(o1).append("，newValue：").append(o2);
                    i++;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result.toString();
    }

    public static String compareJsonObject(String oldJsonStr, String newJsonStr1) {
        //将字符串转换为json对象
        JSON oldJson = JSON.parseObject(oldJsonStr);
        JSON newJson = JSON.parseObject(newJsonStr1);
        //递归遍历json对象所有的key-value，将其封装成path:value格式进行比较
        Map<String, Object> oldMap = new LinkedHashMap<>();
        Map<String, Object> newMap = new LinkedHashMap<>();
        convertJsonToMap(oldJson, "", oldMap);
        convertJsonToMap(newJson, "", newMap);
        Map<String,Object> map = new HashMap<>();
        Map<String, Object> differenceMap = compareMap(oldMap, newMap,map);
        //将最终的比较结果把不相同的转换为json对象返回
        return convertMapToJson(differenceMap);
    }

    /**
     * 将json数据转换为map存储用于比较
     *
     * @param json json串
     * @param root 递归的根
     * @param resultMap map
     */
    private static void convertJsonToMap(Object json, String root, Map<String, Object> resultMap) {
        if (json instanceof JSONObject) {
            JSONObject jsonObject = ((JSONObject) json);
            for (Object key : jsonObject.keySet()) {
                Object value = jsonObject.get(key);
                String newRoot = "".equals(root) ? key + "" : root + "." + key;
                if (value instanceof JSONObject || value instanceof JSONArray) {
                    convertJsonToMap(value, newRoot, resultMap);
                } else {
                    resultMap.put(newRoot, value);
                }
            }
        } else if (json instanceof JSONArray) {
            JSONArray jsonArray = (JSONArray) json;
            for (int i = 0; i < jsonArray.size(); i++) {
                Object value = jsonArray.get(i);
                String newRoot = "".equals(root) ? "[" + i + "]" : root + ".[" + i + "]";
                if (value instanceof JSONObject || value instanceof JSONArray) {
                    convertJsonToMap(value, newRoot, resultMap);
                } else {
                    resultMap.put(newRoot, value);
                }
            }
        }
    }

    /**
     * 比较两个map，返回不同数据
     *
     * @param oldMap 旧数据
     * @param newMap 新数据
     * @return 区别
     */
    private static Map<String, Object> compareMap(Map<String, Object> oldMap, Map<String, Object> newMap,Map<String,Object> map) {
        //遍历newMap，将newMap的不同数据装进oldMap，同时删除oldMap中与newMap相同的数据
        compareNewToOld(oldMap, newMap,map);
        //将旧的有新的沒有的数据封装数据结构存在旧的里面
        //compareOldToNew(oldMap);
        return map;
    }

    /**
     * 将旧的有新的沒有的数据封装数据结构存在旧的里面
     * @param oldMap 旧数据
     */
    private static void compareOldToNew(Map<String, Object> oldMap) {
        //统一oldMap中newMap不存在的数据的数据结构，便于解析
        for (Map.Entry<String, Object> item : oldMap.entrySet()) {
            String key = item.getKey();
            Object value = item.getValue();
            if (!(value instanceof Map)) {
                Map<String, Object> differenceMap = new HashMap<>();
                differenceMap.put("oldValue", value);
                differenceMap.put("newValue", "");
                oldMap.put(key, differenceMap);
            }
        }
    }

    /**
     * 将新的map与旧的比较，并将数据统一存在旧的里面
     * @param oldMap 旧数据
     * @param newMap 新数据
     */
    private static void compareNewToOld(Map<String, Object> oldMap, Map<String, Object> newMap,Map<String,Object> map) {
        for (Map.Entry<String, Object> item : newMap.entrySet()) {
            String key = item.getKey();
            Object newValue = item.getValue();
            Map<String, Object> differenceMap = new HashMap<>();
            if (oldMap.containsKey(key)) {
                Object oldValue = oldMap.get(key);
                if ((newValue == null && oldValue == null) || (newValue != null && newValue.equals(oldValue))
                        || (oldValue == null && newValue != null && StringUtil.isEmpty(newValue.toString()))) {
                    oldMap.remove(key);
                } else {
                    differenceMap.put("oldValue", oldValue);
                    differenceMap.put("newValue", newValue);
                    map.put(key, differenceMap);
                }
            } else {
                if (newValue != null && StringUtil.isEmpty(newValue.toString())){
                    continue;
                }
                differenceMap.put("oldValue", "");
                differenceMap.put("newValue", newValue);
                map.put(key, differenceMap);
            }
        }
    }

    /**
     * 将已经找出不同数据的map根据key的层级结构封装成json返回
     * 如果不讲究json内数组格式要求，可以直接序列化map 无序调用此方法
     * @param map 要封装的map
     * @return 字符串
     */
    private static String convertMapToJson(Map<String, Object> map) {
        JSONObject resultJSONObject = new JSONObject();
        for (Map.Entry<String, Object> item : map.entrySet()) {
            String key = item.getKey();
            Object value = item.getValue();
            String[] paths = key.split("\\.");
            int i = 0;
            //用于深度标识
            Object remarkObject = null;
            int indexAll = paths.length - 1;
            while (i <= paths.length - 1) {
                String path = paths[i];
                if (i == 0) {
                    //初始化对象标识
                    if (resultJSONObject.containsKey(path)) {
                        remarkObject = resultJSONObject.get(path);
                    } else {
                        if (indexAll > i) {
                            if (paths[i + 1].matches("\\[[0-9]+]")) {
                                remarkObject = new JSONArray();
                            } else {
                                remarkObject = new JSONObject();
                            }
                            resultJSONObject.put(path, remarkObject);
                        } else {
                            resultJSONObject.put(path, value);
                        }
                    }
                    i++;
                    continue;
                }
                //匹配集合对象
                if (path.matches("\\[[0-9]+]")) {
                    int startIndex = path.lastIndexOf("[");
                    int endIndex = path.lastIndexOf("]");
                    int index = Integer.parseInt(path.substring(startIndex + 1, endIndex));
                    if (indexAll > i) {
                        if (paths[i + 1].matches("\\[[0-9]+]")) {
                            if (remarkObject instanceof JSONArray) {
                                while (((JSONArray) remarkObject).size() <= index) {
                                    if (((JSONArray) remarkObject).size() == index) {
                                        ((JSONArray) remarkObject).add(index, new JSONArray());
                                    } else {
                                        ((JSONArray) remarkObject).add(null);
                                    }
                                }
                            }
                        } else {
                            if (remarkObject instanceof JSONArray) {
                                while (((JSONArray) remarkObject).size() <= index) {
                                    if (((JSONArray) remarkObject).size() == index) {
                                        ((JSONArray) remarkObject).add(index, new JSONObject());
                                    } else {
                                        ((JSONArray) remarkObject).add(null);
                                    }
                                }
                            }
                        }
                        if (remarkObject instanceof JSONArray) {
                            remarkObject = ((JSONArray) remarkObject).get(index);
                        }
                    } else {
                        if (remarkObject instanceof JSONArray) {
                            while (((JSONArray) remarkObject).size() <= index) {
                                if (((JSONArray) remarkObject).size() == index) {
                                    ((JSONArray) remarkObject).add(index, value);
                                } else {
                                    ((JSONArray) remarkObject).add(null);
                                }
                            }
                        }
                    }
                } else {
                    if (indexAll > i) {
                        if (paths[i + 1].matches("\\[[0-9]+]")) {
                            if (remarkObject instanceof JSONObject) {
                                ((JSONObject) remarkObject).put(path, new JSONArray());
                            }
                        } else {
                            if (remarkObject instanceof JSONObject) {
                                ((JSONObject) remarkObject).put(path, new JSONObject());
                            }
                        }
                        if (remarkObject instanceof JSONObject) {
                            remarkObject = ((JSONObject) remarkObject).get(path);
                        }
                    } else {
                        if (remarkObject instanceof JSONObject) {
                            ((JSONObject) remarkObject).put(path, value);
                        }
                    }
                }
                i++;
            }
        }
        return JSON.toJSONString(resultJSONObject);
    }

    public static void main(String[] args){
        String oldStr= "{\"remark\": \"55\", \"mchName\": \"汉堡\", \"salerId\": 57, \"workerNum\": 6, \"contactName\": \"测试\", \"quoteVoList\": [{\"rate\": 6, \"mainstayMchNo\": \"S000005\", \"mainstayMchName\": \"测试\"}], \"contactPhone\": \"13112341234\", \"signRateLevel\": 101, \"companyWebsite\": \"\", \"monthMoneySlip\": 666, \"positionVoList\": [{\"serviceDesc\": \"负责课程的安排，在【 】平台上/指定地点实施授课培训，讲师有义务积极配合甲方完成培训服务（包括认真准备课件，提前了解授课对象的基本信息，了解学员的需求，迭代课程内容等），并提供必要的协助宣传\", \"workplaceCode\": \"100\", \"chargeRuleDesc\": \"根据讲师的不同资质级别，按照【 】元-【  】元/小时计算\", \"workCategoryCode\": \"0201\", \"workCategoryName\": \"经管/技术等社会科学专题培训讲师、线上线下答疑\", \"invoiceCategoryCode\": \"2\", \"invoiceCategoryName\": \"其他咨询服务*咨询服务费\"}], \"bizPlatformName\": \"\", \"industryTypeCode\": \"0201\", \"industryTypeName\": \"游戏设计开发与运营\", \"supplementFileUrls\": [], \"workerMonthIncomeRate\": 5, \"companyLeafletFileUrls\": [], \"provideIncomeDetailType\": 100}";
        String newStr= "{\"remark\": \"55\", \"mchName\": \"堡\", \"salerId\": 57, \"workerNum\": 6, \"contactName\": \"测试\", \"quoteVoList\": [{\"rate\": 10, \"mainstayMchName\": \"测试\"}], \"contactPhone\": \"1311234134\", \"signRateLevel\": 101, \"companyWebsite\": \"\", \"monthMoneySlip\": 666, \"positionVoList\": [{\"serviceDesc\": \"负责课程的安排，在【 】平台上/指定地点实施授课培训，讲师有义务积极配合甲方完成培训服务（包括认真准备课件，提前了解授课对象的基本信息，了解学员的需求，迭代课程内容等），并提供必要的协助宣传\", \"workplaceCode\": \"100\", \"chargeRuleDesc\": \"根据讲师的不同资质级别，按照【 】元-【  】元/小时计算\", \"workCategoryCode\": \"0201\", \"workCategoryName\": \"经管/技术等社会科学专题培训讲师、线上线下答疑\", \"invoiceCategoryCode\": \"2\", \"invoiceCategoryName\": \"其他咨询服务*咨询服务费\"}], \"bizPlatformName\": \"aa\", \"industryTypeCode\": \"0201\", \"industryTypeName\": \"游戏设与运营\", \"supplementFileUrls\": [{\"a\":10}], \"workerMonthIncomeRate\": 5, \"companyLeafletFileUrls\": [], \"provideIncomeDetailType\": 120}";
        System.out.println(ContrastObjUtils.compareJsonObject(oldStr,newStr));
    }
}
