package com.zhixianghui.facade.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
    * 消息记录表
    */
@Data
public class NotificationRecordDto implements Serializable {

    private static final long serialVersionUID = 4468184692617730723L;
    /**
     * 通知类型
     */
    @NotNull(message = "通知类型不能为空")
    private Integer notificationType;

    /**
     * 通知标题
     */
    @NotBlank(message = "通知标题不能为空")
    private String notificationTitle;

    /**
     * 通知内容
     */
    @NotBlank(message = "通知内容不能为空")
    private String notificationContent;

    /**
     * 接收人类型
     * @see com.zhixianghui.common.statics.enums.notification.NotificationReceiverTypeEnum
     */
    @NotNull(message = "接收人类型不能为空")
    private Integer notificationReceiverType;

    /**
     * 接收人列表
     */
    private String notificationReceivers;

    /**
     * 推送规则类型
     */
    @NotNull(message = "推送规则类型不能为空")
    private Integer publishType;

    /**
     * 推送时间段开始时间
     */
    private Date pushTime;

    private Date pushBeginDate;

    private Date pushEndDate;

    private boolean pop;
}
