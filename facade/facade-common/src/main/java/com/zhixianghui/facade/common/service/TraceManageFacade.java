package com.zhixianghui.facade.common.service;


import com.zhixianghui.common.statics.dto.rmq.MsgDto;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.common.entity.rocketmqmanage.TraceEntity;
import java.util.Date;
import java.util.List;

/**
 * ROCKETMQ消息跟踪管理服务
 */
public interface TraceManageFacade {
    List<TraceEntity> listTraceEntityPage(Date timeBegin, Date timeEnd) throws BizException;

    TraceEntity getTraceDetail(String msgId) throws BizException;

    MsgDto<?> getMessageContent(String topic, String msgId) throws BizException;

    List<TraceEntity> listTraceEntityByTrxNo(String trxNo) throws BizException;
}
