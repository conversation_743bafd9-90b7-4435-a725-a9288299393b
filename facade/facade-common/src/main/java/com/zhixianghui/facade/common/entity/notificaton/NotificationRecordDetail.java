package com.zhixianghui.facade.common.entity.notificaton;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
    * TBL_NOTIFICATION_RECORD_DETAIL
    */
@Data
@TableName(value = "tbl_notification_record_detail")
public class NotificationRecordDetail implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    @Version
    private Integer version;

    @TableField(value = "MCH_NO")
    private String mchNo;

    @TableField(value = "MCH_NAME")
    private String mchName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATION_TIME",fill = FieldFill.INSERT)
    private LocalDateTime creationTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 通知ID
     */
    @TableField(value = "NOTIFICATION_ID")
    private Long notificationId;

    /**
     * 通知类型
     */
    @TableField(value = "NOTIFICATION_TYPE")
    private Integer notificationType;

    /**
     * 通知标题
     */
    @TableField(value = "NOTIFICATION_TITLE")
    private String notificationTitle;

    /**
     * 已读状态 200 未读 100 已读
     */
    @TableField(value = "READ_STATUS")
    private Integer readStatus;

    /**
     * 已读时间
     */
    @TableField(value = "READ_TIME")
    private LocalDateTime readTime;

    /**
     * 已读操作员ID
     */
    @TableField(value = "READ_OPERATOR_ID")
    private Long readOperatorId;

    /**
     * 已读操作员名称
     */
    @TableField(value = "READ_OPERATOR_NAME")
    private String readOperatorName;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_CREATION_TIME = "CREATION_TIME";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_NOTIFICATION_ID = "NOTIFICATION_ID";

    public static final String COL_NOTIFICATION_TYPE = "NOTIFICATION_TYPE";

    public static final String COL_NOTIFICATION_TITLE = "NOTIFICATION_TITLE";

    public static final String COL_READ_STATUS = "READ_STATUS";

    public static final String COL_READ_TIME = "READ_TIME";

    public static final String COL_READ_OPERATOR_ID = "READ_OPERATOR_ID";

    public static final String COL_READ_OPERATOR_NAME = "READ_OPERATOR_NAME";
}
