package com.zhixianghui.facade.common.entity.push;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 *
 * <AUTHOR>
 * @Date 创建时间： 2023-02-21
 */
@Data
@TableName(value = "tbl_push_manager")
@EqualsAndHashCode(callSuper = false)
public class PushManager implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 推送类型
     */
    private Integer pushType;

    /**
     * sftp ip地址
     */
    private String sftpIp;

    /**
     * sftp端口号
     */
    private String sftpPort;

    /**
     * sftp路径
     */
    private String sftpPath;

    /**
     * 服务器类型
     */
    private Integer serverType;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;


}
