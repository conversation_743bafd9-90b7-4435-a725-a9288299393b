package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.black.Blacklist;
import com.zhixianghui.facade.common.vo.BlackListVo;

import java.util.List;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-03-01
 */
public interface BlacklistFacade {

    Blacklist getBlacklist(String tag, String subjectNo);

    void add(BlackListVo blackListVo, String realName);

    void deleteById(Long id);

    void update(Blacklist blacklist);

    PageResult<List<Blacklist>> selectPage(Blacklist blacklist, int pageSize, int pageCurrent);
}
