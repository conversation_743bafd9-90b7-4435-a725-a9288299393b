package com.zhixianghui.facade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 节点处理状态
 * @date 2020-08-10 16:17
 **/
@AllArgsConstructor
@Getter
@ToString
public enum HandleStatus {
    /**
     *同意
     */
    AGREE(100, "同意"),
    /**
     *不同意
     */
    DISAGREE(101, "不同意"),
    /**
     *异常
     */
    EXCEPTION(102, "异常"),
    /**
     *待审批
     */
    PENDING(103, "待审批"),
    ;

    private final int value;
    private final String desc;

}
