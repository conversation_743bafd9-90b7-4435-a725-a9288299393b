package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.config.BankInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 银行信息表 Facade类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-31
 */

public interface BankInfoFacade {
    /**
     *
     * @param paramMap 查询条件
     * @param pageParam 分页条件
     * @return 银行卡信息是列表
     */
    PageResult<List<BankInfo>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     *
     * @param bankInfo 银行卡信息
     */
    void insert(BankInfo bankInfo);

    /**
     * @param id 银行卡信息id
     */
    void del(Long id);

    /**
     *
     * @param id 银行卡id
     * @return 银行卡信息
     */
    BankInfo edit(Long id);

    /**
     * 待更新
     * @param bankInfo 银行卡信息
     */
    void update(BankInfo bankInfo);


    BankInfo getByBankChannelNo(String bankChannelNo);

    BankInfo getBankInfoById(Long id);
}
