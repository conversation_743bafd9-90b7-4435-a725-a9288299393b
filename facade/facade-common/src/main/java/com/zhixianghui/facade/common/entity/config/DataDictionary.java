package com.zhixianghui.facade.common.entity.config;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * longfenghua
 * 数据字典表
 */
@Data
public class DataDictionary implements Serializable {

    private static final long serialVersionUID = -7105773401118745797L;

    //columns START
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 数据名称
     */
    private String dataName;

    /**
     * json类型数据字典信息
     */
    private String dataInfo;

    /**
     * 描述
     */
    private String remark;

    /**
     * createTime
     */
    private java.util.Date createTime;

    /**
     * modifyTime
     */
    private java.util.Date modifyTime;

    /**
     * modifyOperator
     */
    private String modifyOperator;

    /**
     * 系统标识 {@link SystemTypeEnum}
     */
    private Integer systemType;

    //columns END

    /**
     * 数据库不存在
     * 对应字段：dataInfo
     * 通过操作该字段操作dataInfo
     */
    private List<Item> itemList = new ArrayList<>();

    /**
     * dao 入库使用
     * @return
     */
    @Deprecated
    public String getDataInfo() {
        return JsonUtil.toString(itemList);
    }

    /**
     * dao查询时解析
     * @param dataInfo
     */
    @Deprecated
    public void setDataInfo(String dataInfo) {
        this.dataInfo = dataInfo;
        itemList = JsonUtil.toList(dataInfo, Item.class);
    }

    private String[] properties = {"creator", "dataName", "dataInfo", "remark", "systemType"};
    public void build() {
        for (String property : properties) {
            Item item = new Item();
            item.setDesc(property);
            item.setCode(property);
            itemList.add(item);
        }
    }

    @Data
    public static class Item implements Serializable {
        private String flag;
        private String code;
        private String desc;
    }

}
