package com.zhixianghui.facade.common.service;

import com.zhixianghui.facade.common.entity.report.EmployerAccountNotifyConfig;

import java.util.List;

public interface EmployerAcctNotifyConfigFacade {
    void updateConfig(EmployerAccountNotifyConfig config);

    void addConfig(EmployerAccountNotifyConfig config);

    EmployerAccountNotifyConfig getConfig(String employerNo, String mainstayNo);

    List<EmployerAccountNotifyConfig> listNotifyConfig(String mainstayNo, String employerNo);
}
