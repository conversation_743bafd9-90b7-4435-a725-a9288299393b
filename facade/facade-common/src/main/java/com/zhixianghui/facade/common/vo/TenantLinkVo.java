package com.zhixianghui.facade.common.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName TenantLinkVo
 * @Description TODO
 * @Date 2023/3/28 9:32
 */
@Data
public class TenantLinkVo implements Serializable {

    @NotBlank(message = "域名不能为空")
    private String website;

    @NotNull(message = "域名类型不能为空")
    private Integer siteType;

    private String icpNo;
}
