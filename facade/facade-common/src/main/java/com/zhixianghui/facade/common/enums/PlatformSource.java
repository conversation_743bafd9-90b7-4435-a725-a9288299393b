package com.zhixianghui.facade.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 审批流程来源
 * @date 2020-08-11 10:51
 **/
@AllArgsConstructor
@Getter
@ToString
public enum PlatformSource {
    /**
     *运营平台
     */
    OPERATION(1000, "运营平台"),

    /**
     *商户平台
     */
    MERCHANT(1001, "商户平台"),
    /**
     *供应商后台
     */
    SUPPLIER(1002, "供应商后台"),
    /**
     *合伙人后台
     */
    AGENT(1003, "合伙人后台"),

    COMMON(1100,"通用后台"),
    ;
    private final int value;
    private final String desc;

    public static PlatformSource getEnum(int value){
        PlatformSource[] types = PlatformSource.values();
        for(int i=0; i<types.length; i++){
            if(value == types[i].getValue()){
                return types[i];
            }
        }
        return null;
    }
}
