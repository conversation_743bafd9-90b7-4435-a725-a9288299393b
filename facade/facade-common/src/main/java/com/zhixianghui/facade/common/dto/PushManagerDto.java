package com.zhixianghui.facade.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName PushManagerDto
 * @Description TODO
 * @Date 2023/2/21 16:12
 */
@Data
public class PushManagerDto implements Serializable {

    @NotBlank(message = "商户号不能为空")
    private String mchNo;

    @NotNull(message = "推送内容不能为空")
    private Integer pushType;

    @NotBlank(message = "sftp服务器地址不能为空")
    private String sftpIp;

    @NotBlank(message = "sftp服务器端口号不能为空")
    private String sftpPort;

    @NotBlank(message = "sftp服务器路径不能为空")
    private String sftpPath;

    @NotNull(message = "服务器类型不能为空")
    private Integer serverType;

    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "密码不能为空")
    private String password;

    private String callbackUrl;
}
