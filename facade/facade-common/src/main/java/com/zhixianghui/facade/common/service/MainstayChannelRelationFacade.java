package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.KeyPairRecordDto;
import com.zhixianghui.facade.common.dto.MainstayChannelRelationDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;

import java.util.List;
import java.util.Map;


/**
 * 供应商账户表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
public interface MainstayChannelRelationFacade {

    /**
     * 建立供应商-支付通道账户关系
     * @param mainstayChannelRelation 供应商-支付通道账户关系
     */
    void create(MainstayChannelRelation mainstayChannelRelation);


    /**
     * 批量建立供应商-支付通道账户关系
     * @param mainstayChannelRelationList 供应商-支付通道账户关系
     * @param keyPairRecordList 密钥
     */
    void batchCreate(List<MainstayChannelRelation> mainstayChannelRelationList,List<KeyPairRecordDto> keyPairRecordList);

    /**
     * 是否已经存在代征主体记录
     * @param mainstayNo 代征主体编号
     */
    boolean isExist(String mainstayNo);

    /**
     * 分页供应商-支付通道账户关系
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return 供应商-支付通道账户关系
     */
    PageResult<List<MainstayChannelRelationDto>> listCustomPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 批量更新
     * @param list 更新数据列表
     * @param keyPairRecordList 密钥
     */
    void batchUpdate(List<MainstayChannelRelation> list, List<KeyPairRecordDto> keyPairRecordList);

    /**
     * 批量查询
     * @param paramMap 查询参数
     * @return 数据列表
     */
    List<MainstayChannelRelation> listBy(Map<String, Object> paramMap);

    /**
     * 根据供应商编号获取供应商账户
     * @param mainstayNo 供应商编号
     * @return 供应商账户
     */
    List<MainstayChannelRelation> listByMainstayNo(String mainstayNo);

    /**
     * 根据通道商户号和通道编号获取供应商账户
     * @param channelMchNo 通道商户号
     * @param payChannelNo 通道编号
     * @return 供应商账户
     */
    MainstayChannelRelation getByChannelMchNoAndPayChannelNo(String channelMchNo, String payChannelNo);

    /**
     * 根据供应商编号删除供应商账户
     * @param mainstayNo 供应商编号
     */
    void deleteByMainstayNo(String mainstayNo);

    void update(MainstayChannelRelation mainstayChannelRelation);

    MainstayChannelRelation getByMainstayNoAndPayChannelNo(String mainstayNo, String name);

    List<Map<String, Object>> getAlipayMainstays();

    List<Map<String, Object>> getCmbainstays();
}
