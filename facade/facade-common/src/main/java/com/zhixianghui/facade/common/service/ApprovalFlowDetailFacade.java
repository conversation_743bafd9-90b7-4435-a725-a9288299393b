package com.zhixianghui.facade.common.service;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 审批节点详情表 Facade类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-07
 */

public interface ApprovalFlowDetailFacade {

    /**
     * 根据审批流id获取各个审批节点详情
     * @param approvalFlowId 审批流id
     * @param handlerId 查询人id（用于权限校验）
     * @param isAdmin  是否超级管理员
     * @return 审批节点详情List
     */
    List<ApprovalFlowDetail> listByApprovalFlowIdAndHandlerId(Long approvalFlowId, Long handlerId, boolean isAdmin);

    /**
     * 同意审批
     * @param approvalDetailId 审批节点
     * @param handlerId 审批人
     * @param platformSource 平台来源 {@link PlatformSource}
     * @param flowName 流程名 {@link FlowTopicType}
     * @param approvalOpinion 审批意见
     * @param isAdmin  是否超级管理员
     * @return 是否最后一步 （解耦:把最后一步具体业务操作下放到各个归属的系统）
     */
    ApprovalInfoVo agreeApprovalDetail(Long approvalDetailId, Long handlerId,
                                       Integer platformSource, String flowName,String approvalOpinion,
                                       boolean isAdmin);

    /**
     * 不同意审批（驳回到上一步）
     * @param approvalDetailId 审批节点
     * @param handlerId 审批人
     * @param platformSource 平台来源 {@link PlatformSource}
     * @param flowName 流程名 {@link FlowTopicType}
     * @param approvalOpinion 审批意见
     * @param isAdmin  是否超级管理员
     */
    void disAgreeApprovalDetail(Long approvalDetailId, Long handlerId,
                                Integer platformSource, String flowName,String approvalOpinion,
                                boolean isAdmin);

    List<ApprovalFlowDetail> listBy(Map<String, Object> paramMap, String sortColumns);
}
