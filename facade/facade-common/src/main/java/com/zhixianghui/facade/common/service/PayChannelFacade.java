package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.PayChannelDto;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.entity.report.PayChannelType;

import java.util.List;
import java.util.Map;

/**
 * 支付通道表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
public interface PayChannelFacade {

    /**
     * 新建通道
     * @param payChannel 通道
     */
    void create(PayChannel payChannel, List<PayChannelType> payChannelTypeList);

    /**
     * @param id 通道id
     * @param status 状态
     * @param operator 更改者
     */
    void changeStatus(Long id, Integer status, String operator);

    /**
     * 编辑通道
     * @param payChannel 通道
     */
    void updateIfNotNull(PayChannel payChannel,List<PayChannelType> payChannelTypeList);

    /**
     * 删除通道
     * @param id 通道id
     */
    void deleteById(Long id);

    /**
     * 分页查询通道
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return 通道
     */
    PageResult<List<PayChannel>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 根据通道id获取通道
     * @param id 通道id
     * @return 通道
     */
    PayChannel getById(Long id);

    /**
     * 获取全部通道信息
     * @return 全部通道信息列表
     */
    List<PayChannel> listAll();

    PayChannel getByChannelNo(String payChannelNo);

    PageResult<List<PayChannelDto>> listCustomPage(Map<String, Object> beanToMap, PageParam pageParam);
}
