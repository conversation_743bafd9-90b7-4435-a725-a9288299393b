package com.zhixianghui.facade.common.entity.report;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "tbl_employer_account_notify_config")
public class EmployerAccountNotifyConfig implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    @Version
    private Integer version;

    /**
     * 用工企业编号
     */
    @TableField(value = "EMPLOYER_NO")
    private String employerNo;

    /**
     * 用工企业名称
     */
    @TableField(value = "EMPLOYER_NAME")
    private String employerName;

    /**
     * 代征主体编号
     */
    @TableField(value = "MAINSTAY_NO")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @TableField(value = "MAINSTAY_NAME")
    private String mainstayName;

    /**
     * 创建人
     */
    @TableField(value = "CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 提醒金额
     */
    @TableField(value = "NOTIFY_AMOUNT")
    private BigDecimal notifyAmount;

    /**
     * 提醒时间
     */
    @TableField(value = "NOTIFY_TYPE")
    private Integer notifyType;

    /**
     * 提醒时间段开始时间
     */
    @TableField(value = "NOTIFY_TIME_START")
    private String notifyTimeStart;

    /**
     * 提醒时间段结束时间
     */
    @TableField(value = "NOTIFY_TIME_END")
    private String notifyTimeEnd;

    /**
     * 提醒次数
     */
    @TableField(value = "NOTIFY_TIMES")
    private Integer notifyTimes;

    @TableField(value = "STATUS")
    private Boolean status;

    /**
     * 接收账号
     */
    @TableField(value = "RECEIVE_ACCOUNT")
    private String receiveAccount;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_EMPLOYER_NO = "EMPLOYER_NO";

    public static final String COL_EMPLOYER_NAME = "EMPLOYER_NAME";

    public static final String COL_MAINSTAY_NO = "MAINSTAY_NO";

    public static final String COL_MAINSTAY_NAME = "MAINSTAY_NAME";

    public static final String COL_CREATE_BY = "CREATE_BY";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_UPDATE_BY = "UPDATE_BY";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_NOTIFY_AMOUNT = "NOTIFY_AMOUNT";

    public static final String COL_NOTIFY_TYPE = "NOTIFY_TYPE";

    public static final String COL_NOTIFY_TIME_START = "NOTIFY_TIME_START";

    public static final String COL_NOTIFY_TIME_END = "NOTIFY_TIME_END";

    public static final String COL_NOTIFY_TIMES = "NOTIFY_TIMES";

    public static final String COL_RECEIVE_ACCOUNT = "RECEIVE_ACCOUNT";

    public static final String COL_STATUS = "STATUS";
}