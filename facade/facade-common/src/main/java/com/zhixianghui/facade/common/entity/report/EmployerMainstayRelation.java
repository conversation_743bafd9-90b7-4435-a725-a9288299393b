package com.zhixianghui.facade.common.entity.report;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 代征关系表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EmployerMainstayRelation implements Serializable {

    private static final long serialVersionUID = -4722171801284813978L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 创建者
     */
    private String createOperator;

    /**
     * 更新者
     */
    private String updateOperator;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 1开户成功、0开户失败
     */
    private Integer accountStatus;

    /**
     * 参数信息
     */
    private String paramInfo;

    /**
     * 外部商户ID
     */
    private String externalEnterpriseId;

    /**
     * 外部商户SN
     */
    private String externalEnterpriseSn;

    /**
     * 外部商户状态
     */
    private Integer externalEnterpriseStatus;

    /**
     * 外部账户密码
     */
    private String externalPassword;

    /**
     * 外部账户帐号
     */
    private String externalUserName;

    /**
     * 是否有外部系统
     */
    private Boolean hasExternalSystem = false;

    public String getExternalPasswordDecrypt() {
        String externalPasswordDecrypt = AESUtil.decryptECB(getExternalPassword(), EncryptKeys.getEncryptKeyById(101L).getEncryptKeyStr());
        return externalPasswordDecrypt;
    }

    public void setExternalPasswordEncrypt(String externalPassword) {
        String passwordEncrypt = AESUtil.encryptECB(getExternalPassword(), EncryptKeys.getEncryptKeyById(101L).getEncryptKeyStr());
        this.setExternalPassword(passwordEncrypt);
    }
}
