package com.zhixianghui.facade.common.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 流程主题类型枚举
 * @date 2020-08-07 15:08
 **/
@AllArgsConstructor
@Getter
@ToString
public enum FlowTopicType {

    /**
     *创建商户
     */
    CREATE_MERCHANT(1, "CREATE_MERCHANT","创建商户"),

    /**
     *主体认证
     */
    MERCHANT_VERIFY(2, "MERCHANT_VERIFY","主体认证"),

    /**
     *创建合伙人
     */
    CREATE_AGENT(3, "CREATE_AGENT","创建合伙人"),

    ;

    private final int value;
    private final String name;
    private final String desc;

    public static String getNameByValue(int value) {
        FlowTopicType[] ary = FlowTopicType.values();
        Map<Integer,String> map = Maps.newHashMap();
        Arrays.stream(ary).forEach(
                x-> map.put(x.getValue(),x.getName())
        );
        return map.get(value);
    }

}
