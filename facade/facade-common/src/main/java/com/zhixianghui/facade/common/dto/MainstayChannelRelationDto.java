package com.zhixianghui.facade.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商账户表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MainstayChannelRelationDto implements Serializable {

    private static final long serialVersionUID = 6175170663962559300L;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 通道信息
     */
    private List<MainstayChannel> mainstayChannels;
}
