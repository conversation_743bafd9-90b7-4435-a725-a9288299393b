package com.zhixianghui.facade.common.entity.report;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商账户表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MainstayChannelRelation implements Serializable {

    private static final long serialVersionUID = 760919544157410107L;

    private Long id;

    private Integer version = 0;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 支付通道编号
     */
    private String payChannelNo;


    /**
     * 通道商户编号
     */
    private String channelMchNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateOperator;

    /**
     * 创建者
     */
    private String createOperator;

    /**
     * 协议号
     */
    private String agreementNo;

    /**
     * 支付宝用户id
     */
    private String alipayUserId;

    /**
     * 银行户名
     */
    private String accountName;

    /**
     * 银行账号
     */
    private String accountNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 支行
     */
    private String subBankName;

    /**
     * 开户行号
     */
    private String joinBankNo;

    /**
     * 开户地
     */
    private String bankAddress;

    /**
     * 来账通知时间
     */
    private String incomeRecordTime;

    /**
     * 支付宝外标卡号
     */
    private String alipayCardNo;

    public MainstayChannelRelation getMainstayChannelRelation(){
        return this;
    }
}
