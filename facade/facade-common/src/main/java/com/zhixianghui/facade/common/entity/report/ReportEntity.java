package com.zhixianghui.facade.common.entity.report;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReportEntity implements Serializable {

    private static final long serialVersionUID = 8681517903880984080L;

    /**
     * 用工企业编号
     */
    private String employerNo;
    /**
     * 商户名称
     */
    private String employerName;
    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 所属供应商名称
     */
    private String mainstayName;

    /**
     * 支付通道编号
     */
    private String payChannelNo;

    /**
     * 通道名称
     */
    private String payChannelName;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 报备人
     */
    private String reporter;

    /**
     * 报备日志id
     */
    private Long recordId;

    /**
     * 操作主体类型
     */
    private Integer merchantType;

    /**
     * 签约号
     */
    private String extAgreement;

}
