package com.zhixianghui.facade.common.entity.config;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
    * 区域代码映射表
    */
@Data
@TableName(value = "tbl_area_map")
public class AreaMap implements Serializable {
    /**
     * 智享汇区域代码
     */
    @TableId(value = "ZXH_CODE", type = IdType.INPUT)
    private String zxhCode;

    /**
     * 易税区域代码
     */
    @TableField(value = "YISHUI_CODE")
    private String yishuiCode;

    private static final long serialVersionUID = 1L;

    public static final String COL_ZXH_CODE = "ZXH_CODE";

    public static final String COL_YISHUI_CODE = "YISHUI_CODE";
}
