package com.zhixianghui.facade.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.common.dto.NotificationRecordDto;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.common.vo.NotificationDetailFullInfo;

import java.util.List;
import java.util.Map;

public interface NotificationFacade {

    void createNotification(NotificationRecordDto notificationRecordDto) throws BizException;

    IPage<NotificationDetailFullInfo> listNotificationRecordFullInfo(Page<Map<String, Object>> page, Map<String, Object> params) throws BizException;

    IPage<NotificationRecord> listNotifications(Page<Map<String, Object>> page, Map<String, Object> params) throws BizException;

    NotificationRecord getNotificationInfoById(Long notificationId) throws BizException;

    void updateNotificationDetailReadStatus(NotificationRecordDetail notificationRecordDetail) throws BizException;

    void updateNotification(NotificationRecord notificationRecord) throws BizException;

    NotificationRecordDetail getNotificationRecordDetailById(Long id) throws BizException;

    void deletNotificationByIds(List<Long> ids) throws BizException;
}
