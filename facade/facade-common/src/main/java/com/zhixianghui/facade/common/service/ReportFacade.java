package com.zhixianghui.facade.common.service;


import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.common.entity.report.ReportEntity;

public interface ReportFacade {
    /**
     * 进行通道报备
     * @param reportEntity 报备参数
     */
    void report(ReportEntity reportEntity) throws BizException;

    //ReportChannelRecord getRecordByEmployerNoAndMainstayNoAndChannelNo(String employerNo, String mainstayNo, String payChannelNo);

    /**
     * 解除签约
     * @param reportEntity
     */
    void unsign(ReportEntity reportEntity) throws BizException;

    String reportMerchantDiy(ReportEntity reportEntity);

    void modify(ReportEntity reportEntity);

    void uploadPic(ReportEntity reportEntity);

    void uploadPic(String employerNo, String mainstayNo);
}
