package com.zhixianghui.facade.common.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.report.ReportChannelRecord;
import java.util.List;
import java.util.Map;

/**
 * 通道报备记录表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-10-15
 */
public interface ReportChannelRecordFacade {

    /**
     * 根据日志标识获取通道报备记录
     * @param serialNo 日志标识
     * @return 通道报备记录
     */
    ReportChannelRecord getBySerialNo(String serialNo);

    ReportChannelRecord getById(Long id);

    /**
     * 更新通道报备记录
     * @param reportChannelRecord 通道报备记录
     */
    void update(ReportChannelRecord reportChannelRecord);

    /**
     * 分页查询通道报备记录
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return 通道报备记录列表
     */
    PageResult<List<ReportChannelRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    void delete(ReportChannelRecord reportChannelRecord);
}
