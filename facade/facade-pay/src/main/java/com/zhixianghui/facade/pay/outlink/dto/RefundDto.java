package com.zhixianghui.facade.pay.outlink.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RefundDto implements Serializable {

    private static final long serialVersionUID = 4639707130245559702L;
    @JSONField(name = "out_trade_no")
    private String outTradeNo;
    @JSONField(name = "trade_no")
    private String tradeNo;
    @JSONField(name = "refund_amount")
    private String refundAmount;
    @JSONField(name = "refund_reason")
    private String refundReason;
    @JSONField(name = "out_request_no")
    private String outRequestNo;
    @JSONField(name = "refund_royalty_parameters")
    private List<OpenApiRoyaltyDetailInfoPojo> royaltyParameters;


    public String toJsonString() {
        if (this == null) {
            return null;
        }
        return JSONObject.toJSONString(this);
    }
}
