package com.zhixianghui.facade.pay.outlink.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.zhixianghui.facade.pay.outlink.dto.GoodsInfo;
import lombok.Data;

/**
    * 支付申请记录表
    */
@Data
@TableName(value = "tbl_pay_apply_order",autoResultMap = true)
public class PayApplyOrder implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 支付订单号
     */
    @TableField(value = "pay_order_no")
    private String payOrderNo;

    /**
     * 订单金额
     */
    @TableField(value = "order_amount")
    private BigDecimal orderAmount;

    /**
     * 订单标题
     */
    @TableField(value = "subject")
    private String subject;

    /**
     * 商户的原始订单号
     */
    @TableField(value = "merchant_order_no")
    private String merchantOrderNo;
    /**
     * 商品ID
     */
    @TableField(value = "goods_id")
    private String goodsId;
    /**
     * 商品名称
     */
    @TableField(value = "goods_name")
    private String goodsName;
    /**
     * 商品单价
     */
    @TableField(value = "goods_price")
    private String goodsPrice;
    /**
     * 商品数量
     */
    @TableField(value = "goods_quantity")
    private Integer goodsQuantity;

    /**
     * 二级商户
     */
    @TableField(value = "sub_merchant_id")
    private String subMerchantId;

    /**
     * 商户编号
     */
    @TableField(value = "merchant_no")
    private String merchantNo;

    /**
     * 商户名称
     */
    @TableField(value = "merchant_name")
    private String merchantName;

    /**
     * 结算金额
     */
    @TableField(value = "settle_amount")
    private BigDecimal settleAmount;

    /**
     * 退款金额
     */
    @TableField(value = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 支付时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 支付完成时间
     */
    @TableField(value = "pay_complete_time")
    private Date payCompleteTime;

    /**
     * 外部订单金额
     */
    @TableField(value = "orig_total_amount")
    private BigDecimal origTotalAmount;

    /**
     * 公用回传参数
     */
    @TableField(value = "passback_params")
    private String passbackParams;

    /**
     * 支付状态
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 结算状态
     */
    @TableField(value = "settle_status")
    private Integer settleStatus;

    /**
     * 服务费
     */
    @TableField(value = "fee_amount")
    private BigDecimal feeAmount;

    /**
     * 商户实收金额
     */
    @TableField(value = "mch_amount")
    private BigDecimal mchAmount;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private LocalDateTime payTime;

    /**
     * 付款账户
     */
    @TableField(value = "payer_account")
    private String payerAccount;

    /**
     * 收款账户
     */
    @TableField(value = "payee_account")
    private String payeeAccount;

    /**
     * 回调地址
     */
    @TableField(value = "notify_url")
    private String notifyUrl;

    /**
     * 支付后返回页面URL
     */
    @TableField(value = "return_url")
    private String returnUrl;

    /**
     * 支付方式
     */
    @TableField(value = "pay_type")
    private String payType;

    /**
     * 退款状态
     */
    @TableField(value = "refund_status")
    private Integer refundStatus;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_PAY_ORDER_NO = "pay_order_no";

    public static final String COL_ORDER_AMOUNT = "order_amount";

    public static final String COL_SUBJECT = "subject";

    public static final String COL_MERCHANT_ORDER_NO = "merchant_order_no";

    public static final String COL_GOODS_DETAIL = "goods_detail";

    public static final String COL_GOODS_QUANTITY = "goods_quantity";

    public static final String COL_SUB_MERCHANT_ID = "sub_merchant_id";

    public static final String COL_MERCHANT_NO = "merchant_no";

    public static final String COL_MERCHANT_NAME = "merchant_name";

    public static final String COL_SETTLE_AMOUNT = "settle_amount";

    public static final String COL_REFUND_AMOUNT = "refund_amount";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_PAY_COMPLETE_TIME = "pay_complete_time";

    public static final String COL_ORIG_TOTAL_AMOUNT = "orig_total_amount";

    public static final String COL_PASSBACK_PARAMS = "passback_params";

    public static final String COL_PAY_STATUS = "pay_status";

    public static final String COL_SETTLE_STATUS = "settle_status";

    public static final String COL_FEE_AMOUNT = "fee_amount";

    public static final String COL_MCH_AMOUNT = "mch_amount";

    public static final String COL_PAY_TIME = "pay_time";

    public static final String COL_PAYER_ACCOUNT = "payer_account";

    public static final String COL_PAYEE_ACCOUNT = "payee_account";

    public static final String COL_NOTIFY_URL = "notify_url";

    public static final String COL_RETURN_URL = "return_url";

    public static final String COL_PAY_TYPE = "pay_type";

    public static final String COL_REFUND_STATUS = "refund_status";
}