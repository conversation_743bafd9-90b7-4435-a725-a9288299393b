package com.zhixianghui.facade.pay.outlink.dto;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SettleConfirmDto implements Serializable {
    private static final long serialVersionUID = 8998707054071133482L;

    /**
     * 确认结算请求流水号，开发者自行生成并保证唯一性，作为业务幂等性控制
     */
    @JSONField(name = "out_request_no")
    private String outRequestNo;

    /**
     * 支付宝交易号
     */
    @JSONField(name = "trade_no")
    private String tradeNo;

    @JSONField(name = "settle_info")
    private SettleInfo settleInfo;

    @JSONField(name = "extend_params")
    private SettleConfirmExtendParams extendParams;

    @JSONField(serialize = false)
    private String amount;
    @JSONField(serialize = false)
    private String settleEntityId;
    @Data
    class SettleInfo{
        @JSONField(name = "settle_detail_infos")
        private List<SettleDetailInfo> settleDetailInfos;
    }

    @Data
    class SettleDetailInfo {
        /**
         * 结算收款方的账户类型。
         */
        @JSONField(name = "trans_in_type")
        private String transInType="defaultSettle";

        /**
         * 结算主体标识。当结算主体类型为SecondMerchant时，为二级商户的SecondMerchantID
         */
        @JSONField(name = "settle_entity_id")
        private String settleEntityId;

        /**
         * 结算主体类型
         */
        @JSONField(name = "settle_entity_type")
        private String settleEntityType = "SecondMerchant";
        /**
         * 结算的金额，单位为元。在创建订单和支付接口时必须和交易金额相同。在结算确认接口时必须等于交易金额减去已退款金额。直付通账期模式下，
         * 如使用部分结算能力、传递了actual_amount字段，则忽略本字段的校验、可不传。
         */
        @JSONField(name = "amount")
        private String amount;
    }

    @Data
    class SettleConfirmExtendParams{
        @JSONField(name = "royalty_freeze")
        private String royaltyFreeze = "true";
    }
    public void setAmount(String amount) {
        this.amount = amount;
        if (this.settleInfo == null) {
            this.settleInfo = new SettleConfirmDto.SettleInfo();
        }
        if (this.settleInfo.settleDetailInfos == null) {
            this.settleInfo.settleDetailInfos = ListUtil.of(new SettleConfirmDto.SettleDetailInfo());
        }
        this.settleInfo.settleDetailInfos.get(0).setAmount(amount);
    }

    public void setSettleEntityId(String settleEntityId) {
        this.settleEntityId = settleEntityId;
        if (this.settleInfo == null) {
            this.settleInfo = new SettleConfirmDto.SettleInfo();
        }
        if (this.settleInfo.settleDetailInfos == null) {
            this.settleInfo.settleDetailInfos = ListUtil.of(new SettleConfirmDto.SettleDetailInfo());
        }
        this.settleInfo.settleDetailInfos.get(0).setSettleEntityId(settleEntityId);
    }

    public String toJsonString() {
        if (this == null) {
            return null;
        }
        return JSONObject.toJSONString(this);
    }
}
