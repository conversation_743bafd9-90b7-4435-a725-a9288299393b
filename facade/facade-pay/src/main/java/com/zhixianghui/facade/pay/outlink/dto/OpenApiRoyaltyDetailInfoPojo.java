package com.zhixianghui.facade.pay.outlink.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.alipay.api.internal.mapping.ApiField;
import lombok.Data;

import java.io.Serializable;

@Data
public class OpenApiRoyaltyDetailInfoPojo implements Serializable {
    private static final long serialVersionUID = 7106102892943670275L;

    @JSONField(name = "amount")
    private String amount;
    /**
     * 分账描述
     */
    @JSONField(name = "desc")
    private String desc;

    /**
     * 可选值：达人佣金、平台服务费、技术服务费、其他
     */
    @JSONField(name = "royalty_scene")
    private String royaltyScene="平台服务费";

    /**
     * 分账类型.
     普通分账为：transfer;
     补差为：replenish;
     为空默认为分账transfer;
     */
    @JSONField(name = "royalty_type")
    private String royaltyType="transfer";

    /**
     * 收入方账户。如果收入方账户类型为userId，本参数为收入方的支付宝账号对应的支付宝唯一用户号，以2088开头的纯16位数字；如果收入方类型为cardAliasNo，本参数为收入方在支付宝绑定的卡编号；如果收入方类型为loginName，本参数为收入方的支付宝登录号；
     */
    @JSONField(name = "trans_in")
    private String transIn;

    /**
     * 收入方账户类型。userId表示是支付宝账号对应的支付宝唯一用户号;cardAliasNo表示是卡编号;loginName表示是支付宝登录号；
     */
    @JSONField(name = "trans_in_type")
    private String transInType;

    @JSONField(name = "trans_out_type")
    private String trans_out_type;
    @JSONField(name = "trans_out")
    private String transOut;

}
