package com.zhixianghui.facade.pay.outlink.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class GoodsInfo {
    /**
     * 商品ID
     */
    @JSONField(name = "goods_id")
    private String goodsId;
    /**
     * 商品名称
     */
    @JSONField(name = "goods_name")
    private String goodsName;

    /**
     * 商品数量
     */
    @JSONField(name = "quantity")
    private String goodsQuantity;

    /**
     * 商品单价
     */
    @JSONField(name = "price")
    private String goodsPrice;
}
