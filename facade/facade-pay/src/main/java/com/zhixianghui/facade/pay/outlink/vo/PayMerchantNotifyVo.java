package com.zhixianghui.facade.pay.outlink.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.zhixianghui.facade.pay.outlink.dto.GoodsInfo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
    * 商户通知
    */
@Data
public class PayMerchantNotifyVo implements Serializable {
    private static final long serialVersionUID = 6447090026715182610L;

    /**
     * 支付流水号
     */
    private String payTrxNo;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 订单标题
     */
    private String subject;

    /**
     * 商户的原始订单号
     */
    private String merchantOrderNo;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 商户名称
     */
    private String merchantName;

    private LocalDateTime payTime;

    private String payerAccount;

    private String payerLoginAccount;

    /**
     * 支付完成时间
     */
    private LocalDateTime payCompleteTime;

    /**
     * 公用回传参数
     */
    private String passbackParams;

    private Integer payStatus;

    private BigDecimal feeAmount;

    private BigDecimal mchAmount;

}