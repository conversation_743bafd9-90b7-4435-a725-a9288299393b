package com.zhixianghui.facade.pay.outlink.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RoyaltyDto implements Serializable {
    private static final long serialVersionUID = -988374282539461428L;

    @JSONField(name = "out_request_no")
    private String outRequestNo;
    @JSONField(name = "trade_no")
    private String tradeNo;
    @JSONField(name = "royalty_mode")
    private String royaltyMode = "sync";
    @JSONField(name = "extend_params")
    private SettleExtendParams extendParams;
    @JSONField(name = "royalty_parameters")
    private List<OpenApiRoyaltyDetailInfoPojo> royaltyParameters;
    @Data
    class SettleExtendParams{
        @J<PERSON>NField(name = "royalty_finish")
        private String royaltyFinish = "true";
    }

    public String toJsonString() {
        if (this == null) {
            return null;
        }
        return JSONObject.toJSONString(this);
    }
}
