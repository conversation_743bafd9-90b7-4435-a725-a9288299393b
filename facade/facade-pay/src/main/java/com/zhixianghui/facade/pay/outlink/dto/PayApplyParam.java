package com.zhixianghui.facade.pay.outlink.dto;

import com.zhixianghui.facade.pay.outlink.enums.PayTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PayApplyParam {

    /**
     * 支付金额
     */
    @NotBlank(message = "支付金额不能为空")
    private String payAmount;

    /**
     * 订单标题，
     * 注意：不可使用特殊字符，如 /，=，& 等。
     */
    @NotBlank(message = "订单标题不能为空")
    private String paySubject;

    /**
     * 中止付款回调地址
     */
    private String quitUrl;


    /**
     * 订单超时时长
     * 取值范围：5m～15d。m-分钟，h-小时，d-天，1c-当天（1c-当天的情况下，无论交易何时创建，都在0点关闭）
     */
    private String timeExpire;

    /**
     * 商户订单号
     */
    @NotBlank(message = "商户订单号不能为空")
    private String merchantOrderNo;

    /**
     * 支付类型 {@link com.zhixianghui.facade.pay.outlink.enums.PayTypeEnum}
     */
    @NotNull(message = "支付类型不能为空")
    private PayTypeEnum payType;

    /**
     * 商品ID
     */
    private String goodsId;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品数量
     */
    private String goodsQuantity;

    /**
     * 商品单价
     */
    private String goodsPrice;

    /**
     * 商户编号
     */
    private String merchantNo;

    /**
     * 同步跳转地址，可实现支付成功后跳转到商家页面的功能
     */
    private String returnUrl;

    private String notifyUrl;

    public void setPayType(String payType) {
        this.payType = PayTypeEnum.valueOf(payType);
    }
}
