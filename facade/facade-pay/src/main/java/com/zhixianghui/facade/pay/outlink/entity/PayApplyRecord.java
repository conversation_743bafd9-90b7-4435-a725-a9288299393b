package com.zhixianghui.facade.pay.outlink.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.zhixianghui.facade.pay.outlink.dto.GoodsInfo;
import lombok.Data;

/**
    * 支付申请记录表
    */
@Data
@TableName(value = "tbl_pay_apply_record",autoResultMap = true)
public class PayApplyRecord implements Serializable {
    private static final long serialVersionUID = 3995108427225768612L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支付流水号
     */
    @TableField(value = "pay_trx_no")
    private String payTrxNo;

    /**
     * 支付金额
     */
    @TableField(value = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 订单标题
     */
    @TableField(value = "subject")
    private String subject;

    /**
     * 商户的原始订单号
     */
    @TableField(value = "merchant_order_no")
    private String merchantOrderNo;

//    /**
//     * 商品列表信息
//     */
//    @TableField(value = "goods_detail", typeHandler = FastjsonTypeHandler.class)
//    private List<GoodsInfo> goodsDetail;

    /**
     * 二级商户
     */
    @TableField(value = "sub_merchant_id")
    private String subMerchantId;

    /**
     * 商户编号
     */
    @TableField(value = "merchant_no")
    private String merchantNo;

    /**
     * 商户名称
     */
    @TableField(value = "merchant_name")
    private String merchantName;

    /**
     * 结算金额
     */
    @TableField(value = "settle_amount")
    private BigDecimal settleAmount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 支付时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(value = "channel_trade_no")
    private String channelTradeNo;

    @TableField(value = "pay_time")
    private LocalDateTime payTime;

    @TableField(value = "payer_account")
    private String payerAccount;

    @TableField(value = "payee_account")
    private String payeeAccount;

    @TableField(value = "payer_login_account")
    private String payerLoginAccount;

    /**
     * 支付完成时间
     */
    @TableField(value = "pay_complete_time")
    private LocalDateTime payCompleteTime;

    /**
     * 外部订单金额
     */
    @TableField(value = "orig_total_amount")
    private BigDecimal origTotalAmount;

    /**
     * 公用回传参数
     */
    @TableField(value = "passback_params")
    private String passbackParams;

    @TableField(value = "pay_status")
    private Integer payStatus;

    @TableField(value = "fee_amount")
    private BigDecimal feeAmount;

    @TableField(value = "mch_amount")
    private BigDecimal mchAmount;

    @TableField(value = "notify_url")
    private String notifyUrl;

    @TableField(value = "pay_type")
    private String payType;
    @TableField(value = "refund_status")
    private Integer refundStatus;
    @TableField(value = "settle_status")
    private Integer settleStatus;
    @TableField(value = "pay_order_no")
    private String payOrderNo;

    @TableField(value = "settle_trx_no")
    private String settleTrxNo;
    @TableField(value = "settle_time")
    private LocalDateTime settleTime;

    public static final String COL_ID = "id";

    public static final String COL_PAY_TRX_NO = "pay_trx_no";

    public static final String COL_PAY_AMOUNT = "pay_amount";

    public static final String COL_SUBJECT = "subject";

    public static final String COL_MERCHANT_ORDER_NO = "merchant_order_no";

    public static final String COL_GOODS_DETAIL = "goods_detail";

    public static final String COL_SUB_MERCHANT_ID = "sub_merchant_id";

    public static final String COL_MERCHANT_NO = "merchant_no";

    public static final String COL_MERCHANT_NAME = "merchant_name";

    public static final String COL_SETTLE_AMOUNT = "settle_amount";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_PAY_COMPLETE_TIME = "pay_complete_time";

    public static final String COL_ORIG_TOTAL_AMOUNT = "orig_total_amount";

    public static final String COL_PASSBACK_PARAMS = "passback_params";

    public static final String COL_PAY_STATUS = "pay_status";

    public static final String COL_FEE_AMOUNT = "fee_amount";

    public static final String COL_MCH_AMOUNT = "mch_amount";

    public static final String COL_CHANNEL_TRADE_NO = "channel_trade_no";

    public static final String COL_PAY_TIME = "pay_time";

    public static final String COL_PAYER_ACCOUNT = "payer_account";

    public static final String COL_PAYEE_ACCOUNT = "payee_account";

    public static final String COL_PAYER_LOGIN_ACCOUNT = "payer_login_account";
}