package com.zhixianghui.facade.pay.outlink.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.zhixianghui.facade.pay.outlink.enums.PayTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class PayApplyDto {

    /**
     * 支付流水号
     */
    @JSONField(name = "out_trade_no")
    private String payTrxNo;

    /**
     * 支付金额
     */
    @JSONField(name = "total_amount")
    private String payAmount;

    /**
     * 订单标题
     */
    @JSONField(name = "subject")
    private String paySubject;

    @JSONField(name = "product_code")
    private String productCode;

    /**
     * 中止付款回调地址
     */
    @JSONField(name = "quit_url")
    private String quitUrl;


    /**
     * 订单超时时长
     */
    @JSONField(name = "time_expire")
    private String timeExpire;

    /**
     * 商户订单号
     */
    @JSONField(name = "merchant_order_no")
    private String merchantOrderNo;


    /**
     * 订单包含的商品列表信息
     */
    @JSONField(name = "goods_detail")
    private List<GoodsInfo> goodsInfoList;

    /**
     * 二级商户信息，直付通场景下必传
     */
    @JSONField(name = "sub_merchant")
    private SubMerchant subMerchant;
    /**
     * 描述结算信息
     */
    @JSONField(name = "settle_info")
    private SettleInfo settleInfo;

    @JSONField(name = "extend_params")
    private ExtendParams extendParams = new ExtendParams();

    @JSONField(serialize = false)
    private String subMerchantId;

    /**
     * 商品ID
     */
    @JSONField(serialize = false)
    private String goodsId;
    /**
     * 商品名称
     */
    @JSONField(serialize = false)
    private String goodsName;

    /**
     * 商品数量
     */
    @JSONField(serialize = false)
    private String goodsQuantity;

    /**
     * 商品单价
     */
    @JSONField(serialize = false)
    private String goodsPrice;

    /**
     * 商户编号
     */
    @JSONField(serialize = false)
    private String merchantNo;
    /**
     * 商户名称
     */
    @JSONField(serialize = false)
    private String merchantName;
    @JSONField(serialize = false)
    private String sysServiceProviderId;
    @JSONField(serialize = false)
    private String origTotalAmount;
    @JSONField(serialize = false)
    private BigDecimal feeAmount;

    @JSONField(serialize = false)
    private BigDecimal mchAmount;

    @JSONField(serialize = false)
    private String returnUrl;

    @JSONField(serialize = false)
    private String notifyUrl;
    @JSONField(serialize = false)
    private String settleEntityId;
    @JSONField(serialize = false)
    private String payType;
    @Data
    class SubMerchant{
        /**
         * 二级商户信息，进件分配给平台商的 smid
         */
        @JSONField(name = "merchant_id")
        private String merchantId;
    }

    @Data
    class SettleDetailInfo {
        @JSONField(name = "trans_in_type")
        private String transInType = "defaultSettle";
        @JSONField(name = "settle_entity_type")
        private String settleEntityType="SecondMerchant";
        @JSONField(name = "amount")
        private String amount;
        @JSONField(name = "settle_entity_id")
        private String settleEntityId;
    }

    @Data
    class SettleInfo{
        @JSONField(name = "settle_detail_infos")
        private List<SettleDetailInfo> settleDetailInfos;
    }

    @Data
    class ExtendParams{
        @JSONField(name = "royalty_freeze")
        private String royaltyFreeze = "true";
        @JSONField(name = "sys_service_provider_id")
        private String sysServiceProviderId;
        @JSONField(name = "orig_total_amount")
        private String origTotalAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
        if (this.settleInfo == null) {
            this.settleInfo = new SettleInfo();
        }
        if (this.settleInfo.settleDetailInfos == null) {
            this.settleInfo.settleDetailInfos = ListUtil.of(new SettleDetailInfo());
        }
        this.settleInfo.settleDetailInfos.get(0).setAmount(payAmount);
    }

    public void setSubMerchantId(String subMerchantId) {
        this.subMerchantId = subMerchantId;
        if (this.subMerchant == null) {
            this.subMerchant = new SubMerchant();
        }
        this.subMerchant.setMerchantId(subMerchantId);
    }

    public void setSysServiceProviderId(String sysServiceProviderId) {
        this.sysServiceProviderId = sysServiceProviderId;
        if (this.extendParams == null) {
            this.extendParams = new ExtendParams();
        }
        this.extendParams.setSysServiceProviderId(sysServiceProviderId);
    }

    public void setOrigTotalAmount(String origTotalAmount) {
        this.origTotalAmount = origTotalAmount;
        if (this.extendParams == null) {
            this.extendParams = new ExtendParams();
        }
        this.extendParams.setOrigTotalAmount(origTotalAmount);
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
        if (this.goodsInfoList == null) {
            this.goodsInfoList = ListUtil.of(new GoodsInfo());
        }
        this.goodsInfoList.get(0).setGoodsId(goodsId);
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
        if (this.goodsInfoList == null) {
            this.goodsInfoList = ListUtil.of(new GoodsInfo());
        }
        this.goodsInfoList.get(0).setGoodsName(goodsName);
    }

    public void setGoodsQuantity(String goodsQuantity) {
        this.goodsQuantity = goodsQuantity;
        if (this.goodsInfoList == null) {
            this.goodsInfoList = ListUtil.of(new GoodsInfo());
        }
        this.goodsInfoList.get(0).setGoodsQuantity(goodsQuantity);
    }

    public void setGoodsPrice(String goodsPrice) {
        this.goodsPrice = goodsPrice;
        if (this.goodsInfoList == null) {
            this.goodsInfoList = ListUtil.of(new GoodsInfo());
        }
        this.goodsInfoList.get(0).setGoodsPrice(goodsPrice);
    }

    public void setSettleEntityId(String settleEntityId) {
        this.settleEntityId = settleEntityId;
        if (this.settleInfo == null) {
            this.settleInfo = new PayApplyDto.SettleInfo();
        }
        if (this.settleInfo.settleDetailInfos == null) {
            this.settleInfo.settleDetailInfos = ListUtil.of(new PayApplyDto.SettleDetailInfo());
        }
        this.settleInfo.settleDetailInfos.get(0).setSettleEntityId(settleEntityId);
    }

    public String toJsonString() {
        if (this == null) {
            return null;
        }
        return JSONObject.toJSONString(this);
    }

    public static void main(String[] args) {
        PayApplyParam payApplyParam = new PayApplyParam();
        payApplyParam.setPayAmount("100");
        payApplyParam.setPaySubject("测试");
        payApplyParam.setMerchantNo("cddas");
        payApplyParam.setGoodsName("冰棒");
        payApplyParam.setGoodsId("G0001");
        payApplyParam.setGoodsQuantity("100");
        payApplyParam.setGoodsPrice("10.00");
        payApplyParam.setPayType("H5");

        PayApplyDto payApplyDto = BeanUtil.copyProperties(payApplyParam, PayApplyDto.class);

        System.out.println(payApplyParam.getPayType() == PayTypeEnum.H5);

        System.out.println(payApplyDto.toJsonString());
    }
}
