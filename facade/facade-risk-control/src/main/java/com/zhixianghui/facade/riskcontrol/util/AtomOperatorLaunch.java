package com.zhixianghui.facade.riskcontrol.util;

import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomOperatorEnum;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 原子比较的实现
 * @author: xing<PERSON>ng li
 * @created: 2020/10/20 14:38
 */
public class AtomOperatorLaunch<T> {

    public static Map<String, AtomOperator> opMap = new HashMap<>();
    static {
        //Arrays.asList(StrategyAtomOperatorEnum.values()).stream().forEach();
    }
}
