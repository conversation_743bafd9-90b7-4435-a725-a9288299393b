package com.zhixianghui.facade.riskcontrol.entity;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.ValidateUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 基础隐私数据Item
 * @date 2020-11-14 22:30
 **/
@Data
public class BasePrivateItem implements Serializable {

    private static final long serialVersionUID = -7780043030446633667L;
    /**
     * 密钥ID
     */
    private Long encryptKeyId;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人姓名MD5
     */
    private String receiveNameMd5;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 持卡人身份证号MD5
     */
    private String receiveIdCardNoMd5;

    /**
     * 收款账户(卡号/支付宝账号)
     */
    private String receiveAccountNo;

    /**
     * 收款账户
     */
    private String receiveAccountNoMd5;

    /**
     * 银行预留手机号
     */
    private String receivePhoneNo;

    /**
     * 银行预留手机号MD5
     */
    private String receivePhoneNoMd5;

    public void setReceiveNameEncrypt(String receiveName) {
        if(StringUtils.isNotEmpty(receiveName)){
            this.receiveName = AESUtil.encryptECB(receiveName, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receiveName);
            this.receiveNameMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceiveNameDecrypt() {
        return AESUtil.decryptECB(this.receiveName, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setReceiveIdCardNoEncrypt(String receiveIdCardNo) {
        if(StringUtils.isNotEmpty(receiveIdCardNo)){
            this.receiveIdCardNo = AESUtil.encryptECB(receiveIdCardNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receiveIdCardNo);
            this.receiveIdCardNoMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceiveIdCardNoDecrypt() {
        return AESUtil.decryptECB(this.receiveIdCardNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setReceiveAccountNoEncrypt(String receiveAccountNo) {
        if(StringUtils.isNotEmpty(receiveAccountNo)) {
            this.receiveAccountNo = AESUtil.encryptECB(receiveAccountNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receiveAccountNo);
            this.receiveAccountNoMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceiveAccountNoDecrypt() {
        return AESUtil.decryptECB(this.receiveAccountNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setReceivePhoneNoEncrypt(String receivePhoneNo) {
        if(StringUtils.isNotEmpty(receivePhoneNo)) {
            this.receivePhoneNo = AESUtil.encryptECB(receivePhoneNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receivePhoneNo);
            this.receivePhoneNoMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceivePhoneNoDecrypt() {
        return AESUtil.decryptECB(this.receivePhoneNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    //脱敏

    /**
     * 收款人名脱敏
     * @return 脱敏的收款人名
     */
    public String getReceiveNameDesensitize(){
        String receiveName = getReceiveNameDecrypt();
        if(StringUtils.isBlank(receiveName)){
            return "";
        }
        return DesensitizeUtil.handleNameDesenCenter(receiveName);
    }

    /**
     * 身份证脱敏
     * @return 脱敏的身份证
     */
    public String getReceiveIdCardNoDesensitize(){
        String receiveIdCardNo = getReceiveIdCardNoDecrypt();
        if(StringUtils.isBlank(receiveIdCardNo)){
            return "";
        }
        return DesensitizeUtil.handleIdNumOrBankCardNo(receiveIdCardNo);
    }

    /**
     * 收款账号脱敏
     * @return 脱敏的收款账号
     */
    public String getReceiveAccountNoDesensitize(){
        //收款账号可能是银行卡、手机、邮箱
        String receiveAccountNo = getReceiveAccountNoDecrypt();
        if(StringUtils.isBlank(receiveAccountNo)){
            return "";
        }
        if(StringUtils.isNotEmpty(receiveAccountNo)){
            if(ValidateUtil.isMobile(receiveAccountNo)){
                return DesensitizeUtil.handleMobile(receiveAccountNo);
            } else if(ValidateUtil.isEmail(receiveAccountNo)){
                return DesensitizeUtil.handleEmailDesenCenter(receiveAccountNo);
            } else{
                return DesensitizeUtil.handleIdNumOrBankCardNo(receiveAccountNo);
            }
        }
        return null;
    }

    /**
     * 手机号脱敏
     * @return 脱敏的手机号
     */
    public String getReceivePhoneNoDesensitize(){
        String receivePhone = getReceivePhoneNoDecrypt();
        if(StringUtils.isBlank(receivePhone)){
            return "";
        }
        return DesensitizeUtil.handleMobile(receivePhone);
    }

}
