package com.zhixianghui.facade.riskcontrol.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 名单策略原子组表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class NameStrategyAtomGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商户名称
     */
    private String supplierName;

    /**
     * 商户编号
     */
    private String supplierNo;

    /**
     * 规则id
     */
    private Long nameManagementId;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 管控原子
     */
    private Integer controlAtom;

    /**
     * 描述
     */
    private String remark;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
