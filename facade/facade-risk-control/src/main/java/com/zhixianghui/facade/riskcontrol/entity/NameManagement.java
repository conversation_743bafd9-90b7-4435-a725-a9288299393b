package com.zhixianghui.facade.riskcontrol.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 名单管理表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class NameManagement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 用户纬度
     */
    private Integer userLatitude;

    /**
     * 用户名称
     */
    private String username;


    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 入库原因
     */
    private String warehousingReason;


    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 更新人登录名
     */
    private String updator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Long enterprisePersonnelId;


}
