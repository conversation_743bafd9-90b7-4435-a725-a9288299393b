package com.zhixianghui.facade.riskcontrol.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 策略原子
 */
@Data
public class StrategyAtom extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 策略原子组id
	 */
	private Long groupId;

	/**
	 * 条件变量
	 * {@link StrategyAtomVariableEnum#getValue()}
	 */
	private Integer variable;

	/**
	 * 运算符
	 * {@link StrategyAtomOperatorEnum#getValue()}
	 */
	private Integer operator;

	/**
	 * 常量值
	 */
	private String constant;
}
