package com.zhixianghui.facade.riskcontrol.util;

import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum;
import com.zhixianghui.facade.riskcontrol.entity.StrategyAtom;
import com.zhixianghui.facade.riskcontrol.vo.CalculateAtomVo;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

/**
 * @description: 用于表达式计算
 * @author: xingguang li
 * @created: 2020/10/20 15:05
 */
@Slf4j
public class CalculateEngine {

    private static ScriptEngineManager manager = null;

    private static ScriptEngineManager getEngineManager() {
        if (manager == null) {
            synchronized(ScriptEngineManager.class) {
                if (manager == null) {
                    manager = new ScriptEngineManager();
                }
            }
        }
        return manager;
    }

    /**
     * 计算字符串的脚本
     * @param scriptStr
     * @return
     */
    public static boolean calculate(String scriptStr){
        ScriptEngine engine = getEngineManager().getEngineByName("js");
        boolean result = false;
        try {
            Object obj = engine.eval(scriptStr);
            result = Boolean.valueOf(obj.toString());
        } catch (ScriptException e) {
            log.error("calculate has error:",e);
        }
        return result;
    }

    /**
     * 执行vo脚本引擎
     * @param calculateAtomVo
     * @return
     */
    public static boolean calculate(CalculateAtomVo calculateAtomVo, StrategyAtomVariableEnum strategyAtomVariableEnum, SettleRiskControlVo settleRiskControlVo){
        boolean isPass;
        switch (strategyAtomVariableEnum){
            case USER_SIGN: case USER_AUTH:
                isPass = Boolean.parseBoolean(calculateAtomVo.getLeft()) == Boolean.parseBoolean(calculateAtomVo.getOperator());
                break;
            case THREE_ELEMENT: case FOUR_ELEMENT:
                isPass = !Boolean.parseBoolean(calculateAtomVo.getLeft());
                break;
            case USER_KEYWORD:
                isPass = calculate(String.format(strategyAtomVariableEnum.getScript(),calculateAtomVo.getRight(),calculateAtomVo.getLeft()));
                break;
            default:
                isPass = calculate(String.format(strategyAtomVariableEnum.getScript(),calculateAtomVo.getLeft(),
                        calculateAtomVo.getOperator(),calculateAtomVo.getRight()));
                break;
        }
        return isPass;
    }

    public static void main(String[] args) {
//        String conditionalExpression = String.format("try{" +
//                "var reg = new RegExp('%s', 'g');" +
//                "reg.test('%s');" +
//                "}catch(e){" +
//                "false" +
//                "}","薪资|工资|奖金","这是工资喔");
//
//        System.out.println(calculate(conditionalExpression));
    }
}
