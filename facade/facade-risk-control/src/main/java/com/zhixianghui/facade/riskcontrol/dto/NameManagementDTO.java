package com.zhixianghui.facade.riskcontrol.dto;

import com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtom;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description: //TODO
 * date: 2021/11/5 9:50
 */
@Data
public class NameManagementDTO implements Serializable {

    private static final long serialVersionUID = -4698410962539275359L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 用户纬度
     */
    private Integer userLatitude;

    /**
     * 用户名称
     */
    private String username;


    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 入库原因
     */
    private String warehousingReason;


    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 更新人登录名
     */
    private String updator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    private List<NameStrategyAtomGroupDTO> nameStrategyAtomGroups;

    @Data
    public static class NameStrategyAtomGroupDTO implements Serializable{
        private static final long serialVersionUID = 3757969745721064145L;
        /**
         * 主键ID
         */
        private Long id;

        /**
         * 商户名称
         */
        private String supplierName;

        /**
         * 商户编号
         */
        private String supplierNo;

        /**
         * 规则id
         */
        private Long nameManagementId;

        /**
         * 权重
         */
        private Integer weight;

        /**
         * 管控原子
         */
        private Integer controlAtom;

        /**
         * 描述
         */
        private String remark;

        /**
         * 版本号
         */
        private Integer version = 0;


        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 更新时间
         */
        private Date updateTime;

        List<NameStrategyAtom> nameStrategyAtoms;
    }
}