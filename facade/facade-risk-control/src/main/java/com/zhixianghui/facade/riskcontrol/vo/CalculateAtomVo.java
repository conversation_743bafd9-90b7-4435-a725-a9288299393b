package com.zhixianghui.facade.riskcontrol.vo;

import java.io.Serializable;

/**
 * @description: 用于原子计算的vo
 * @author: xingguang li
 * @created: 2020/10/20 16:01
 */
public class CalculateAtomVo implements Serializable {

    private static final long serialVersionUID = 5131134712168559822L;

    public CalculateAtomVo(String left, String operator, String right) {
        this.left = left;
        this.operator = operator;
        this.right = right;
    }

    private String left;

    private String operator;

    private String right;

    public String getLeft() {
        return this.left;
    }

    public void setLeft(String left) {
        this.left = left;
    }

    public String getOperator() {
        return this.operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRight() {
        return this.right;
    }

    public void setRight(String right) {
        this.right = right;
    }
}
