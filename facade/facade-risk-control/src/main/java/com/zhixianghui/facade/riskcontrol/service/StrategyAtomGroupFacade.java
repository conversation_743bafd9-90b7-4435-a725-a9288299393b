package com.zhixianghui.facade.riskcontrol.service;

import com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup;

import java.util.List;

/**
 * 风控规则策略原子组接口
 *
 * <AUTHOR>
 */
public interface StrategyAtomGroupFacade {

    /**
     * 根据id获取策略原子组
     * @param id    原子组id
     * @return
     */
    StrategyAtomGroup getById(long id);

    /**
     * 查询风控规则的策略原子组，根据权重值排序
     * @param ruleId    风控规则id
     * @return
     */
    List<StrategyAtomGroup> listStrategyAtomGroup(long ruleId);

    /**
     * 创建策略原子组
     * @param strategyAtomGroup     策略原子组
     */
    void createStrategyAtomGroup(StrategyAtomGroup strategyAtomGroup, String operator);

    /**
     * 更新策略原子组
     * @param strategyAtomGroup     策略原子组
     */
    void updateStrategyAtomGroup(StrategyAtomGroup strategyAtomGroup, String operator);

    /**
     * 删除策略原子组
     * @param groupId  策略原子组id
     */
    void deleteStrategyAtomGroup(long groupId, String operator);
}
