package com.zhixianghui.facade.riskcontrol.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 风控规则接口
 *
 * <AUTHOR> <PERSON>
 */
public interface RiskControlRuleFacade {

    /**
     * 创建风控规则
     * @param riskControlRule   风控规则
     * @return 返回的是创建的风控规则id
     */
    long create(RiskControlRule riskControlRule) throws BizException;

    /**
     * 根据id获取风控规则
     * @param id    风控规则id
     * @return      风控规则
     */
    RiskControlRule getById(long id);

    /**
     * 分页查询
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<RiskControlRule>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 更新风控规则
     * @param riskControlRule   风控规则
     */
    void update(RiskControlRule riskControlRule) throws BizException;

    /**
     * 启用风控规则
     * @param ruleId    风控规则id
     */
    void enableRule(long ruleId) throws BizException;

    /**
     * 禁用风控规则
     * @param ruleId    风控规则id
     */
    void disableRule(long ruleId) throws BizException;

    /**
     * 根据id删除
     * @param id    风控规则id
     */
    void deleteById(long id);

    /**
     * 根据supplier或者type查询
     * @param supplierNo
     * @param type
     * @return
     */
    List<RiskControlRule> getBySupplierNoType(String supplierNo, Integer type);

    List<HashMap<String,String>> getSupplierNos();

    List<HashMap<String,String>> getNoConfigSuppliers();
}
