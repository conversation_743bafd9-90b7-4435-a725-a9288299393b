package com.zhixianghui.facade.riskcontrol.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: //TODO
 * date: 2021/11/5 14:58
 */
@Data
public class NameManagementQueryDTO implements Serializable {


    private static final long serialVersionUID = -5232037572667870844L;

    /**
     * 当前页数，默认为1，必须设置默认值，否则分页查询时容易报空指针异常
     */
    private int pageCurrent = 1;
    /**
     * 每页记录数，默认为20，必须设置默认值，否则分页查询时容易报空指针异常
     */
    private int pageSize = 20;
    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;


    /**
     * 用户名称
     */
    private String username;


    /**
     * 身份证号码
     */
    private String idCardNumber;


    private String supplierName;

    /**
     * 创建时间-开始
     */
    private String createTimeBegin;
    /**
     * 创建时间-结束
     */
    private String createTimeEnd;


}
