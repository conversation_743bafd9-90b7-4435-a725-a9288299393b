package com.zhixianghui.facade.riskcontrol.vo;

import com.zhixianghui.facade.riskcontrol.dto.NameManagementDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description: //TODO
 * date: 2021/11/5 14:53
 */
@Data
public class NameManagementVo implements Serializable {

    private static final long serialVersionUID = 3931471209646143632L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 用户纬度
     */
    private Integer userLatitude;

    /**
     * 用户名称
     */
    private String username;


    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 入库原因
     */
    private String warehousingReason;

    /**
     * 创建时间
     */
    private Date createTime;

    private String supplierNo;

    private String supplierName;

    private String controlAtom;

    private String controlAtomName;


    private List<NameManagementDTO.NameStrategyAtomGroupDTO> nameStrategyAtomGroups;

}