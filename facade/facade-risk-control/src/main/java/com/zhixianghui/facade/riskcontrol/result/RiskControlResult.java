package com.zhixianghui.facade.riskcontrol.result;

import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 被风控返回
 * @author: xingguang li
 * @created: 2020/11/10 09:55
 */
@Setter
@Getter
public class RiskControlResult implements Serializable {

    private static final long serialVersionUID = 2240768798102452156L;

    /**
     * 错误码 {@link ControlAtomEnum#getValue()}
     */
    private Integer code;
    /**
     * 被风控的描述
     */
    private String errMsg;

    /**
     * {@link com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum}
     */
    private List<String> controlType;

    public RiskControlResult(Integer code, String errMsg) {
        this.code = code;
        this.errMsg = errMsg;
    }

    public RiskControlResult(Integer code,String errMsg,List<String> controlType){
        this.code = code;
        this.controlType = controlType;
        this.errMsg = errMsg;
    }


    public static final RiskControlResult PASS = new RiskControlResult(
            ControlAtomEnum.PASS.getValue(), ControlAtomEnum.PASS.getDesc());

    public static final RiskControlResult TO_NEXT = new RiskControlResult(
            ControlAtomEnum.TO_NEXT.getValue(),ControlAtomEnum.TO_NEXT.getDesc());

    public static void main(String[] args) {
        RiskControlResult r1 = RiskControlResult.PASS;
        RiskControlResult r2 = RiskControlResult.PASS;
        System.out.println(r1.equals(r2));
    }
}
