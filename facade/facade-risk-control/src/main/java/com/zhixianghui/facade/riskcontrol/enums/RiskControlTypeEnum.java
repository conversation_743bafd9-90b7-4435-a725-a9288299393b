package com.zhixianghui.facade.riskcontrol.enums;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName RiskControlTypeEnum
 * @Description TODO
 * @Date 2021/11/10 9:31
 */
@AllArgsConstructor
public enum RiskControlTypeEnum {

    ROSTER(100,"特殊名单"),

    RULE(101,"规则");

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
