package com.zhixianghui.facade.riskcontrol.vo;

import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName GlobalAtomVo
 * @Description TODO
 * @Date 2021/11/9 16:32
 */
@Data
public class GlobalAtomVo implements Serializable {

    private static final long serialVersionUID = -6905541447166485916L;
    /**
     * 策略原子组id
     */
    private Long groupId;

    /**
     * 条件变量
     * {@link StrategyAtomVariableEnum#getValue()}
     */
    private Integer variable;

    /**
     * 运算符
     * {@link StrategyAtomOperatorEnum#getValue()}
     */
    private Integer operator;

    /**
     * 常量值
     */
    private String constant;
}
