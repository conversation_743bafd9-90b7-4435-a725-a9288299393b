package com.zhixianghui.facade.riskcontrol.entity;

import com.zhixianghui.common.statics.enums.riskcontrol.PendingOrderOpeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: xingguang li
 * @created: 2020/11/23 09:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RiskcontrolOrderItem extends BasePrivateItem {

    private static final long serialVersionUID = 3761889874222598025L;
    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 银行流水号
     */
    private String bankTrxNo;

    /**
     * 发放模式
     */
    private Integer launchWay;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名
     */
    private String channelName;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行简码
     */
    private String bankCode;

    /**
     * 订单明细实发金额
     */
    private BigDecimal orderItemNetAmount;

    /**
     * 订单明细代征主体服务费
     */
    private BigDecimal orderItemFee;

    /**
     * 订单明细(总)金额
     */
    private BigDecimal orderItemAmount;

    /**
     * 订单明细状态
     */
    private Integer orderItemStatus;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorDesc;

    /**
     * 重试次数
     */
    private Integer accessTimes;

    /**
     * 挂单是否已经通过
     */
    private Boolean isPassHangup;

    /**
     * 挂单处理人
     */
    private String hangupApprovalLoginName;

    /**
     * 备注
     */
    private String remark;

    /**
     * JSON数据
     */
    private String jsonStr;

    /**
     * 失败原因
     */
    private String failedReason;

    /**
     * 挂单类型，ControlAtomEnum.PENDING or ControlAtomEnum.REJECT
     */
    private Integer pendingType;

    /**
     * 风控状态，{@link PendingOrderOpeEnum#getValue()}
     */
    private Integer pendingStatus;

    private Date startTradeTime;

//    /**
//     * 收款账户
//     */
//    private String receiveAccountNo;
//
//    /**
//     * 持卡人姓名
//     */
//    private String receiveName;
//
//    /**
//     * 持卡人身份证号
//     */
//    private String receiveIdCardNo;
//
//    /**
//     * 预留手机号
//     */
//    private String receivePhoneNo;
}
