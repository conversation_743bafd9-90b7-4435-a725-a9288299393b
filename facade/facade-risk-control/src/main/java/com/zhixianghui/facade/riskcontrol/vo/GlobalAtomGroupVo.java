package com.zhixianghui.facade.riskcontrol.vo;

import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import com.zhixianghui.facade.riskcontrol.entity.StrategyAtom;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName GlobalAtomGroupVo
 * @Description TODO
 * @Date 2021/11/9 16:32
 */
@Data
public class GlobalAtomGroupVo implements Serializable {

    private static final long serialVersionUID = 1982531733929112225L;

    private Long id;

    /**
     * 特殊名单id/规则id
     */
    private Long objectId;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 管控原子
     * {@link ControlAtomEnum#getValue()}
     */
    private Integer controlAtom;

    /**
     * 描述
     */
    private String remark;

    /**
     * 策略原子列表，之间为 AND 关系
     */
    private List<GlobalAtomVo> atomVoList;
}
