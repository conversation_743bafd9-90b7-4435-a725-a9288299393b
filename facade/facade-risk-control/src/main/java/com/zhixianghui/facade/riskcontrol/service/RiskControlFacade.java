package com.zhixianghui.facade.riskcontrol.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> li
 * @date 2020/10/19 09:52
 * @desc 对外风控接口
 */
public interface RiskControlFacade {


    /**
     * 发放判断风控规则是否存在
     * @param employer
     * @param mainstayNo
     * @return
     */
    Boolean grantValidateExistRule(String employerNo,String mainstayNo);

    /**
     *针对结算的风控
     * @param settleRiskControlVo
     */
    RiskControlResult processSettle(SettleRiskControlVo settleRiskControlVo);

    /**
     * 针对结算的风控，对内非对外
     * @param settleRiskControlVo
     * @param ruleId
     * @param atomGroupId
     * @return
     */
    RiskControlResult processSettleInter(SettleRiskControlVo settleRiskControlVo,Integer controlType);

    /**
     * 分页挂单订单
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<RiskcontrolProcessDetail>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 更新
     * @param riskcontrolProcessDetail
     * @return
     */
    void updateProcessDetail(RiskcontrolProcessDetail riskcontrolProcessDetail);

    RiskcontrolProcessDetail getById(Long id);

    /**
     * 根据idlist查询
     * @param ids
     * @return
     */
    List<RiskcontrolProcessDetail> getByIds(List<Long> ids);

    /**
     * 根据idList更新其状态
     * @param param
     */
    void updateStatusByIdList(Map<String,Object> param);

    /**
     * 分页获取挂起的订单
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<RiskcontrolOrderItem>> listPagePendingOrder(Map<String, Object> paramMap, PageParam pageParam);

    List<RiskcontrolProcessDetail> listBy(Map<String,Object> params);

    RiskcontrolOrderItem getRiskOrderItemByPlatTrxNo(String platTrxNo);
}
