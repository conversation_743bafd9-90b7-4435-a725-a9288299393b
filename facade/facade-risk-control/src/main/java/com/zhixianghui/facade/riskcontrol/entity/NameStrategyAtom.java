package com.zhixianghui.facade.riskcontrol.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 名单策略原子表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class NameStrategyAtom implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 策略原子组id
     */
    private Long groupId;

    /**
     * 条件变量
     */
    private Integer variable;

    /**
     * 运算符
     */
    private Integer operator;

    /**
     * 常量值
     */
    private String constant;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建时间
     */
    private Date createTime;


}
