package com.zhixianghui.facade.riskcontrol.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.riskcontrol.RuleRangeOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RuleRangeVariableEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 规则适用范围
 */
@Data
public class RuleRange extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 规则id
	 */
	private Long ruleId;

	/**
	 * 条件变量
	 * {@link RuleRangeVariableEnum#getValue()}
	 */
	private Integer variable;

	/**
	 * 运算符
	 * {@link RuleRangeOperatorEnum#getValue()}
	 */
	private Integer operator;

	/**
	 * 常量值
	 */
	private BigDecimal constant;
}
