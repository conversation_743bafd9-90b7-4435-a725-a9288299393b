package com.zhixianghui.facade.riskcontrol.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskAccessPointEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskRuleTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 风控规则
 *
 * <AUTHOR> <PERSON>
 */
@Data
public class RiskControlRule extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updator;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态
     * {@link OpenOffEnum#getValue()}
     */
    private Integer status;

    /**
     * 风控接入点
     * {@link RiskAccessPointEnum#getValue()}
     */
    private Integer accessPoint;

    /**
     * 风控规则类型
     * {@link RiskRuleTypeEnum#getValue()}
     */
    private Integer type;

    /**
     * 供应商编号
     */
    private String supplierNos;

    /**
     * 供应商名称
     */
    private String supplierNames;

    /**
     *商户编号
     */
    private String employerNos;

    /**
     * 商户名称
     */
    private String employerNames;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 规则适用范围，范围间为 AND 关系
     */
    private List<RuleRange> ruleRangeList;
}
