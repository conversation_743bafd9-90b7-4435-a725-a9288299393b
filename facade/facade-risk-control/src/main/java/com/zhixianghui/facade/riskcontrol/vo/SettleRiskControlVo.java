package com.zhixianghui.facade.riskcontrol.vo;

import com.zhixianghui.common.statics.enums.riskcontrol.RiskAccessPointEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 结算风控vo，{@link RiskAccessPointEnum#SETTLE }
 *
 * <AUTHOR>
 */
@Data
public class SettleRiskControlVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @NotEmpty(message = "订单编号不能为空")
    private String orderNo;

    @NotEmpty(message = "流水号不能为空")
    private String platTrxNo;

    /**
     * 供应商编号
     */
    @NotEmpty(message = "供应商编号不能为空")
    private String supplierNo;

    /**
     * 用工企业编号
     */
    @NotEmpty(message = "用工企业编号不能为空")
    private String employerNo;

    /**
     * 本次发放金额
     */
    @NotNull(message = "发放金额不能为空")
    private BigDecimal orderAmount;

    /**
     * 用户身份证号码
     */
    @NotEmpty(message = "用户身份证号码不能为空")
    private String userIdCard;

    /**
     * 用户名称
     */
    @NotEmpty(message = "用户名称不能为空")
    private String userName;

    /**
     * 收款账号
     */
    private String receiveAccount;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 打款备注
     */
    private String payRemark;

    /**
     * 风控id
     */
    private Long objectId;

    /**
     * 策略原子组权重
     */
    private Long atomGroupId;

    /**
     * 风控权重（仅规则类型，特殊名单类型没有权重）
     */
    private Integer objectWeight;

    /**
     * 策略原子组权重
     */
    private Integer atomGroupWeight;

    private Date startTradeTime;
}
