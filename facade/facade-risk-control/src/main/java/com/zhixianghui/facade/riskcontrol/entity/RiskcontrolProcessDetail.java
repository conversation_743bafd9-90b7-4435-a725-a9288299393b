package com.zhixianghui.facade.riskcontrol.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.PendingOrderOpeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: xingguang li
 * @created: 2020/11/23 09:52
 */
@Data
public class RiskcontrolProcessDetail extends BaseEntity {

    private Date createDate = new Date();
    /**
     * 更新用户
     */
    private String updateUser;

    private Date updateTime;
    /**
     * 商户订单号
     */
    private String mchOrderNo;
    /**
     * 平台流水号
     */
    private String platTrxNo;
    /**
     * 用工企业编号
     */
    private String employerNo;
    /**
     * 用工企业名称
     */
    private String employerName;
    /**
     * 风控接入点
     */
    private Integer accessPoint;
    /**
     * 状态
     * {@link ControlAtomEnum#getValue()}
     */
    private Integer status;
    /**
     * 风控原因
     */
    private String failedReason;
    private Integer age;
    /**
     * 代征主体名称
     */
    private String mainstayName;
    /**
     * 实发金额
     */
    private BigDecimal orderAmount;
    /**
     * 操作
     * {@link PendingOrderOpeEnum#getValue()}
     */
    private Integer operation;
    /**
     * 风控规则id
     */
    private Long ruleId;
    /**
     * 风控策略原子组ID
     */
    private Long atomGroupId;

    /**
     * 风控类型
     * {@link com.zhixianghui.common.statics.enums.riskcontrol.RiskRuleTypeEnum}
     */
    private Integer controlType;

    private Long objectId;

    private Integer atomGroupWeight;

    private Integer objectWeight;
}
