package com.zhixianghui.facade.riskcontrol.service;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementDTO;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementQueryDTO;
import com.zhixianghui.facade.riskcontrol.entity.NameManagement;
import com.zhixianghui.facade.riskcontrol.vo.NameManagementVo;

import java.util.List;

/**
 * 名单管理表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-11-05
 */
public interface NameManagementFacade {

    void create(NameManagementDTO managementDTO);

    void update(NameManagementDTO managementDTO);

    void delete(String ids);


    PageResult<List<NameManagementVo>> listPage(NameManagementQueryDTO nameManagementQueryDTO);


    List<NameManagement> list(String mchNo);

}
