package com.zhixianghui.facade.riskcontrol.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 策略原子组
 *
 * <AUTHOR>
 */
@Data
public class StrategyAtomGroup extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 风控规则id
     * {@link RiskControlRule#getId()}
     */
    private Long ruleId;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 管控原子
     * {@link ControlAtomEnum#getValue()}
     */
    private Integer controlAtom;

    /**
     * 描述
     */
    private String remark;

    /**
     * 策略原子列表，之间为 AND 关系
     */
    private List<StrategyAtom> strategyAtomList;
}
