package com.zhixianghui.facade.banklink.vo.joinpay.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class ApplyAccountEditReq extends JoinpayBaseReq implements Serializable {

    /**
     * 请求接口
     * */
    private String method = "apply.updateAccount";

    /***
     * 收款公司昵称(仅支持汉字，字母和数字， 括号。 15 个字符以内) -- 新增时必传
     */
    private String payeeAccountNickname;
    /***
     * 收款账号
     */
    private String payeeAccountNo;
    /***
     * 指定付款人名称  -- 新增时必传
     * (支持汉字英文数字， 不支持特殊 字符)
     */
    private String payerAccountName;
    /***
     * 付款账号，仅支持英文数字  -- 新增时必传
     */
    private String payerAccountNo;
}
