package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024年4月9日11:25:45
 */
@Data
@Accessors(chain = true)
public class PsnAuthUrlReqVo extends EsignBaseReqVo implements Serializable {
    /**
     *  （如果不传个人的账号信息，则需要个人用户自主在页面填写手机号/邮箱进行验证码回填注册）
     */
    private PsnAuthConfig psnAuthConfig;
    /**
     *  个人授权配置项
     * 授权过期需重新授权；
     * 不传此参数默认页面仅实名认证，不需要用户授权。
     */
    private AuthorizeConfig authorizeConfig;
    /**
     *  认证完成重定向配置项
     */
    private RedirectConfig redirectConfig;

    /**
     * 接收回调通知的Web地址，通知开发者用户认证和授权的完成以及变更情况，
     */
    private String notifyUrl;

    /**
     * 指定客户端类型，默认值 ALL（注意参数值全部为英文大写）
     * ALL - 自动适配移动端或PC端
     * H5 - 移动端适配
     * PC - PC端适配
     */
    private String clientType;

    /**
     * AppScheme，用于支付宝人脸认证重定向时唤起指定App。
     * 示例值：esign://demo/realBack
     */
    private String appScheme;

    @Data
    @Accessors(chain = true)
    public static class PsnAuthConfig implements Serializable {
        /**
         * 个人用户账号标识（手机号或邮箱）
         * 【注】若未知用户的psnId/用户未实名，可以传此字段
         */
        private String psnAccount;
        /**
         * 个人账号ID
         * 【注】若已知用户的psnId/用户已实名，可以传此字段
         */
        private String psnId;

        /**
         * 个人身份附加信息
         */
        private PsnInfo psnInfo;
        /**
         * 个人实名认证页面配置项
         */
        private PsnAuthPageConfig psnAuthPageConfig;


    }

    @Data
    @Accessors(chain = true)
    public static class AuthorizeConfig implements Serializable {
        /**
         * 设置页面中权限范围，参数值如下：
         * 授权当前应用AppId获取用户的账号基本信息：
         * get_psn_identity_info - 授权允许获取个人用户的账号信息（姓名、手机号/邮箱、证件号等）
         * 授权当前应用AppId代用户发起合同签署：
         * psn_initiate_sign - 授权允许代表个人用户发起合同签署以及查询合同签署详情
         * 授权当前应用AppId获取用户资源管理权限：
         * manage_psn_resource - 授权允许获取个人用户的印章等资源的管理权限
         * 授权当前应用AppId存储用户的合同文件：
         * （用于平台专属云项目代客户发起合同签署场景）
         * psn_sign_file_storage - 授权个人合同文件存储到平台应用的本地服务器
         */
        private List<String> authorizedScopes;
    }

    @Data
    @Accessors(chain = true)
    public static class RedirectConfig implements Serializable {
        /**
         * 认证完成后跳转页面（需符合 https /http 协议地址）
         */
        private String redirectUrl;

        /**
         * 重定向跳转延迟时间，单位为秒。
         *
         * 授权模式下（authorizedScopes有具体的参数值）：默认延迟时间为 3秒。
         * 传 0 - 不展示授权结果页，认证完成直接跳转重定向地址
         * 传 其他数字 - 展示授权结果页，倒计时 x秒后，自动跳转重定向地址
         *
         * 实名模式下（authorizedScopes不传或者没有具体的参数值）：默认延迟时间为 5秒。
         * 传 0 - 不展示实名结果页，认证完成直接跳转重定向地址
         * 传 其他数字 - 展示实名结果页，倒计时5秒后，自动跳转重定向地址（只有5秒，没有其他秒数的控制）
         * 【注】当redirectUrl不传的情况下，该字段无需传入，认证完成结果页不跳转。
         */
        private String redirectDelayTime;

    }



    @Data
    @Accessors(chain = true)
    public static class PsnInfo implements Serializable {
        /**
         * 姓名
         */
        private String psnName;
        /**
         * 证件号码
         */
        private String psnIDCardNum;
        /**
         * 证件类型，可选值如下：
         * CRED_PSN_CH_IDCARD - 中国大陆居民身份证
         * CRED_PSN_CH_HONGKONG - 香港来往大陆通行证（回乡证）
         * CRED_PSN_CH_MACAO - 澳门来往大陆通行证（回乡证）
         * CRED_PSN_CH_TWCARD - 台湾来往大陆通行证（台胞证）
         * CRED_PSN_PASSPORT - 护照
         */
        private String psnIDCardType;
        /**
         * 个人手机号（运营商实名登记手机号或银行卡预留手机号，仅用于认证）
         */
        private String psnMobile;

        /**
         * 个人银行卡号
         */
        private String bankCardNum;
        /**
         * 是否校验：psnAccount（个人用户账号标识）或 psnId（个人账号ID）绑定的e签宝个人信息与传入的psnInfo（个人身份信息）中的信息一致
         * true - 校验
         * false - 不校验（默认值）
         * 【注】若传true，则校验信息是否一致。若信息一致或用户未在e签宝注册认证过则正常发起，若信息不一致则报错：“传入的%s和该用户在e签宝的个人信息不一致” ，其中%s可能为多个字段，包含：姓名、证件号、实名手机号、银行卡号
         */
        private Boolean psnIdentityVerify;
    }


    @Data
    @Accessors(chain = true)
    public static class PsnAuthPageConfig implements Serializable {
        /**
         * 设置页面中默认选择的实名认证方式，可选值如下：
         * PSN_FACE - 人脸识别认证（默认值）
         * PSN_MOBILE3 - 手机运营商三要素认证
         * PSN_BANKCARD4 - 银行卡四要素认证
         */
        private String psnDefaultAuthMode;
        /**
         * 设置页面中可选择的个人认证方式范围，若不传此参数，则可选择全部认证方式。
         * PSN_FACE - 人脸识别认证
         * PSN_MOBILE3 - 手机运营商三要素认证
         * PSN_BANKCARD4 - 银行卡四要素认证
         */
        private List<String> psnAvailableAuthModes;

        /**
         * 通过银行卡认证或运营商认证方式时，是否使用详情版（如指定则核验失败可返回具体不匹配信息），传空默认为普通版。
         * PSN_MOBILE3 - 手机运营商三要素认证
         * PSN_BANKCARD4 - 银行卡四要素认证
         * 【注】详情版：针对个人认证失败可以返回具体的不匹配信息，需要单独购买，具体购买方式请咨询e签宝商务人员；
         * 普通版：只返回信息比对核验失败，不会返回具体的不匹配信息。
         */

        private List<String> advancedVersion;

        /**
         * 设置页面中可编辑的个人信息字段，不传此参数，页面默认不允许编辑个人信息。
         * name - 姓名
         * IDCardNum - 证件号码（如果账号已实名，传了该字段，页面也是不可编辑更改的，因为证件号是唯一标识）
         * mobile - 个人手机号（仅针对实名认证手机号）
         * bankCardNum - 个人银行卡号
         */

        private List<String> psnEditableFields;

    }



    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
