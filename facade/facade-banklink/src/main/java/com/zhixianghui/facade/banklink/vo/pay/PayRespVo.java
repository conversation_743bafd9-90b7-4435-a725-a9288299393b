package com.zhixianghui.facade.banklink.vo.pay;

import lombok.Data;

import java.io.Serializable;

/**
 * 付款接口返回实体
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Data
public class PayRespVo implements Serializable {
    /**
     * 渠道请求订单号
     */
    private String bankOrderNo;

    /**
     * 请求订单批次号
     */

    private  String bankBatchNo;
    /**
     * 渠道流水号
     */
    private String bankTrxNo;
    /**
     * 渠道付款状态
     * @see com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum
     */
    private Integer bankPayStatus;
    /**
     * 业务码
     */
    private String bizCode;
    /**
     * 业务信息
     */
    private String bizMsg;
}
