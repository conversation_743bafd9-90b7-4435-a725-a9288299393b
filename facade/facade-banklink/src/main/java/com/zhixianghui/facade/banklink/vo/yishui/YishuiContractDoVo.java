package com.zhixianghui.facade.banklink.vo.yishui;

import lombok.Data;

import java.io.Serializable;

@Data
public class YishuiContractDoVo  implements Serializable {

    private String enterprise_professional_facilitator_id;
    private String enterprise_professional_facilitator_sn;
    private String enterprise_id;
    private String facilitator_id;
    private String professional_id;
    private String is_contract;
    private String contract_start_time;
    private String contract_end_time;
    private String is_auth;
    private String is_delete;
    private String protocol_img;
    private String contract_img;
    private String sign_img;
    private String bank_account;
    private String bank_code;
    private String bank_type;
    private String remarks;
    private String sign_time;
    private String created_time;
    private String updated_time;

}
