package com.zhixianghui.facade.banklink.vo.sign;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.utils.esign.ESignHelpUtil;
import com.zhixianghui.facade.banklink.utils.esign.HeaderConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description e签宝请求基类
 * @date 2021-01-07 17:03
 **/
@Data
public abstract class EsignBaseReqVo implements Serializable {
    /**
     * 构建e签宝常规请求头
     * @param createPersonUrl 接口url
     * @param projectId 项目id
     * @param projectSecret 项目key
     * @return 请求头
     * @throws Exception 异常
     */
    public Map<String, String> buildHeadersMap(String requestMethod, String createPersonUrl, String projectId, String projectSecret) throws Exception {
        String params = JsonUtil.toString(this);
        // 对请求Body体内的数据计算ContentMD5
        String contentMD5 = ESignHelpUtil.doContentMD5(params);
        //传入生成的bodyMd5,加上其他请求头部信息拼接成待签名字符串
        String plaintext = ESignHelpUtil.appendSignDataString(
                requestMethod,
                HeaderConstant.ACCEPT.getValue(),
                contentMD5,
                HeaderConstant.CONTENTTYPE_JSON.getValue(),
                HeaderConstant.DATE.getValue(),
                HeaderConstant.HEADERS.getValue(),
                createPersonUrl
        );

        // 计算请求签名值
        String reqSignature = ESignHelpUtil.doSignatureBase64(plaintext, projectSecret);
        //签名的请求头
        return ESignHelpUtil.buildCommHeader(projectId,contentMD5,reqSignature);
    }

    /**
     * 构建完整的url
     * @param url 完整的url
     */
    public abstract String buildFullUrl(String url);
}
