package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-13 10:21
 **/
@Data
public class SignFlowResDataVo implements Serializable {

    private static final long serialVersionUID = 5556966472080239770L;
    /**
     * 发起签署流程的应用Id
     */
    private String appId;

    /**
     * 是否自动归档
     */
    private Boolean autoArchive;

    /**
     * 文件主题
     */
    private String businessScene;

    /**
     * 流程配置信息
     */
    private ConfigInfo configInfo;

    /**
     * 文件有效截止日期 时间戳
     */
    private Integer contractValidity;

    /**
     * 文件到期前，提前多少小时提醒续签
     */
    private Integer contractRemind;

    /**
     * 流程Id
     */
    private String flowId;

    /**
     * 流程开始时间
     */
    private String flowStartTime;

    /**
     * 流程结束时间
     */
    private String flowEndTime;

    /**
     * 流程状态,
     *
     * 0-草稿
     *
     * 1-签署中
     *
     * 2-完成
     *
     * 3-撤销
     *
     * 5-过期（签署截至日志到期后触发）
     *
     * 7-拒签
     */
    private Integer flowStatus;

    /**
     * 流程描述, 如果流程已拒签或已撤回, 并且存在拒签或撤回原因, 流程描述显示为原因, 否则默认为流程状态描述
     */
    private String flowDesc;

    /**
     * 发起人账户id
     */
    private String initiatorAccountId;

    /**
     * 发起方主体id
     */
    private String initiatorAuthorizedAccountId;

    /**
     * 签署有效截止日期
     */
    private String signValidity;

}
