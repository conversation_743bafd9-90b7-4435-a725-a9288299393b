package com.zhixianghui.facade.banklink.vo.report;

import lombok.Data;

import java.util.Date;

@Data
public class AlipayReportResVo {

    /**
     *
     */
    private String alipayUserId;

    /**
     * 签约号
     */
    private String externalAgreementNo;

    /**
     * 协议号
     */
    private String agreementNo;

    /**
     * 协议签约时间
     */
    private Date signTime;

    /**
     * 协议失效时间
     */
    private Date invalidTime;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 供应商编号
     */
    private String mainStayNo;

    /**
     * 代征主体编号
     */
    private String merchantNo;

    /**
     * 签约主体标识
     */
    private String principalId;

    /**
     * 签约主体类型
     */
    private String pricipalType;

    /**
     * 响应编码
     */
    private String code;

    /**
     * 通道类型
     */
    private Integer outBizChannelType;
}
