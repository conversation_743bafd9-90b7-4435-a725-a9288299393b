package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2021/6/7 11:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class UploadFileReqVo extends CreateSignTemplateReqVo {

    private String filePath;

    public UploadFileReqVo() {}

    public UploadFileReqVo build(CreateFileUploadUrlVo createFileUploadUrlVo,String targetFileUrl){
        UploadFileReqVo vo = new UploadFileReqVo();
        vo.setContentMd5(createFileUploadUrlVo.getContentMd5());
        vo.setContentType(createFileUploadUrlVo.getContentType());
        vo.setFilePath(targetFileUrl);
        return vo;
    }

    public UploadFileReqVo build(CreateSignTemplateReqVo createSignTemplateReqVo, String targetFileUrl) {
        UploadFileReqVo vo = new UploadFileReqVo();
        vo.setContentMd5(createSignTemplateReqVo.getContentMd5());
        vo.setContentType(createSignTemplateReqVo.getContentType());
        vo.setFilePath(targetFileUrl);
        return vo;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
