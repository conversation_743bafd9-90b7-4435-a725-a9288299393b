package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class IssuingData implements Serializable {
    private static final long serialVersionUID = 1719371789335824997L;
    /**
     * 人员ID
     */
    private String professional_id;
    /**
     * 人员姓名
     */
    private String name;
    /**
     * 人员身份证
     */
    private String cer_code;
    /**
     * 预留手机号码
     */
    private String mobile;
    /**
     * 银行卡号
     */
    private String bank_code;
    /**
     * 付款金额
     */
    private String money;
    /**
     * 付款备注
     */
    private String remark;
    /**
     * 第三方订单号 可用于查询付款结果
     */
    private String request_no;
}
