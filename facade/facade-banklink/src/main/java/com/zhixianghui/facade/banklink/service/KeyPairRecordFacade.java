package com.zhixianghui.facade.banklink.service;

import com.zhixianghui.facade.banklink.entity.KeyPairRecord;

import java.util.List;

/**
 * 秘钥对信息记录
 * <AUTHOR>
 * @date 2020/10/27
 **/
public interface KeyPairRecordFacade {
    /**
     * 数据已经存在则更新，不存在则新增
     * @param recordList
     */
    void updateOrInsertIfNotExist(List<KeyPairRecord> recordList);

    /**
     * 根据渠道编号跟渠道商户号查询
     * @param channelNo     渠道编号
     * @param channelMchNo  渠道商户号
     * @return
     */
    KeyPairRecord getByChannelNoAndChannelMchNo(String channelNo, String channelMchNo);
}
