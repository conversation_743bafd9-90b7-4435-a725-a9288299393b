package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TemplateListInfoResDataVo
 * @Description TODO
 * @Date 2022/8/15 11:02
 */
@Data
@Accessors(chain = true)
public class FlowTemplateInfoResDataVo implements Serializable {

    //流程模板id
    private String docTemplateId;

    //流程名称
    private String docTemplateName;

}
