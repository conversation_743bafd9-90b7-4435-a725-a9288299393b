package com.zhixianghui.facade.banklink.vo.sign.createFlow;


import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.PsnAuthUrlReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:07
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CreateByFileReqVo extends EsignBaseReqVo {

    private static final long serialVersionUID = -9197392476573032411L;

    /**
     * 设置待签署文件信息
     * 流程中如需一次完成多份文件签署，可传入多个docs数组；
     * 当发起流程而相关签署需求无法确定时，允许不传docs（同时签署方参数signers也无需设置、是否自动开启autoStart必须设置为false），发起签署后再【追加签署文件】。
     */
    private List<CreateByFileDoc> docs;

    /**
     * 签署流程配置项
     */
    private SignFlowConfig signFlowConfig;

    /**
     * 签署方信息
     */
    private List<Signer> signers;


    @Data
    @Accessors(chain = true)
    public static class CreateByFileDoc implements Serializable {
        /**
         * 待签署文件ID
         */
        private String fileId;
        /**
         * 文件名称
         * 【注】文件名称不可含有以下9个特殊字符：/ \ : * " < > | ？以及所有emoji表情
         */
        private String fileName;

    }


    @Data
    @Accessors(chain = true)
    public static class SignFlowConfig implements Serializable {
        /**
         * 签署流程主题（将展示在签署通知和签署页的任务信息中）
         * 【注】主题名称不可含有以下9个特殊字符：/ \ : * " < > | ？以及所有emoji表情
         */
        private String signFlowTitle;
        /**
         * 签署截止时间， unix时间戳（毫秒）格式，
         * 补充说明：
         * 默认在签署流程创建后的90天时截止（指定值最大不能超过90天，只能指定90天内的时间戳）。签署中如需延期请调用【延期签署截止时间】接口。
         */
        private Long signFlowExpireTime;

        /**
         * 所有签署方签署完成后流程自动完结，默认值 false
         * true - 自动完结
         * false - 非自动完结，需调用【完结签署流程】接口完结
         * 补充说明：
         * 设置了自动完结的流程中不允许再追加签署区、抄送方，点击这里了解更多流程状态说明。
         */
        private Boolean autoFinish;
        /**
         * 接收相关回调通知的Web地址，详见【签署回调通知接收说明】。
         */
        private String notifyUrl;
        /**
         * 重定向配置项
         */
        private RedirectConfig redirectConfig;

        private AuthConfig authConfig;

    }

    @Data
    @Accessors(chain = true)
    public static class AuthConfig implements Serializable {
        /**
         * 签署完成后跳转页面（需符合 https /http 协议地址）
         */
        private List<String> psnAvailableAuthModes;


    }


    @Data
    @Accessors(chain = true)
    public static class RedirectConfig implements Serializable {
        /**
         * 签署完成后跳转页面（需符合 https /http 协议地址）
         */
        private String redirectUrl;


    }


    @Data
    @Accessors(chain = true)
    public static class Signer implements Serializable {

        /**
         * 签署人配置项
         */
        private SignConfig signConfig;

        /**
         * 设置签署方的通知方式
         */
        private NoticeConfig noticeConfig;

        /**
         * 签署方类型
         * 0 - 个人，1 -企业/机构，2 - 法定代表人，3 - 经办人
         * 若指定签署方为个人，则psnSignerInfo为必传项；
         * 若指定签署方为机构或法定代表人手动签署（autoSign参数为false）时，则orgSignerInfo为必传项；
         * 若指定签署方为经办人，在同级数组内必须还有机构类型存在，且orgSignerInfo为必传项，即：指定3 - 经办人签的前提是必须同时存在1 - 企业/机构，且经办人签属于企业合同，不在个人名下。
         */
        private Integer signerType;

        /**
         * 企业/机构签署方信息
         * 当签署主体为企业/机构用户、autoSign参数为false手动签章时，请必须传入此对象
         * 当企业/机构用户选择静默签署，autoSign参数为true自动落章时，此对象可以不传，后台会默认取appId所属主体企业盖章
         */
        private OrgSignerInfo orgSignerInfo;


        /**
         * 个人签署方信息
         * 当签署主体为个人时请传此对象
         */
        private PsnSignerInfo psnSignerInfo;

        /**
         * 签署区信息（设置签署方 盖章/签名/文字输入的区域）
         * 【注】指定了签署方signers的情况下，签署区必传
         *
         */
        private List<SignFields> signFields;
    }

    @Data
    @Accessors(chain = true)
    public static class SignConfig implements Serializable {
        /**
         *设置页面强制阅读倒计时时间，默认值为 0（单位：秒，最大值999）
         */
        private String forcedReadingTime;

        /**
         * 设置签署方的签署顺序
         * 按序签时支持传入顺序值 1 - 255（值小的先签署）
         * 同时签时，允许值重复
         */
        private String signOrder;

    }

    @Data
    @Accessors(chain = true)
    public static class NoticeConfig implements Serializable {

        /**
         * 通知类型，默认不通知（值为""空字符串），允许多种通知方式，请使用英文逗号分隔
         * 传空 - 不通知（默认值）
         * 1 - 短信通知（如果套餐内带“分项”字样，请确保开通【电子签名流量费（分项）认证】中的子项：【短信服务】，否则短信通知收不到）
         * 2 - 邮件通知
         * 补充说明：
         * 个人账号中需要绑定短信/邮件才有对应的通知方式。
         * 该通知是签署方维度的，只控制签署人的签署提醒短信，不控制流程的撤销、完成、抄送等短信通知。
         */
        private String noticeTypes;

    }



    @Data
    @Accessors(chain = true)
    public static class OrgSignerInfo implements Serializable {

        private String orgName;

        private String orgId;

        /**
         * 企业/机构签署方信息（将展示在机构认证页面）
         */
        private OrgInfo orgInfo;

        /**
         * 企业/机构经办人信息
         * 企业/机构手动签署（autoSign为false），经办人信息必传
         * 企业/机构自动落章（autoSign为true），请不要传该参数
         */
        private TransactorInfo transactorInfo;


    }

    @Data
    @Accessors(chain = true)
    public static class OrgInfo implements Serializable {
        /**
         *企业/机构证件号
         */
        private String orgIDCardNum;

        /**
         * 企业/机构证件类型，可选值如下：
         * CRED_ORG_USCC - 统一社会信用代码
         * CRED_ORG_REGCODE - 工商注册号
         */
        private String orgIDCardType;

    }

    @Data
    @Accessors(chain = true)
    public static class TransactorInfo implements Serializable {
        /**
         *经办人账号标识，手机号或邮箱
         * 【注】指定orgName时，该参数为必传项，为了保证签署人准确，建议配合姓名信息传入。
         */
        private String psnAccount;

        /**
         * 经办人身份信息
         */
        private PsnInfo psnInfo;

    }

    @Data
    @Accessors(chain = true)
    public static class PsnInfo implements Serializable {
        /**
         *经办人姓名
         */
        private String psnName;

        /**
         * 经办人证件号
         */
        private String psnIDCardNum;
        /**
         * 经办人证件类型，可选值如下：
         * CRED_PSN_CH_IDCARD - 中国大陆居民身份证（默认值）
         * CRED_PSN_CH_HONGKONG - 香港来往大陆通行证（回乡证）
         * CRED_PSN_CH_MACAO - 澳门来往大陆通行证（回乡证）
         * CRED_PSN_CH_TWCARD - 台湾来往大陆通行证（台胞证）
         * CRED_PSN_PASSPORT - 护照
         */
        private String psnIDCardType;
    }


    @Data
    @Accessors(chain = true)
    public static class PsnSignerInfo implements Serializable {
        /**
         *个人账号标识（手机号或邮箱）用于登录e签宝官网的凭证
         * 【注】个人用户签署时，该参数为必传项。为了保证签署人准确，建议配合姓名信息传入。
         */
        private String psnAccount;

        /**
         * 个人签署方身份信息
         * 补充说明：
         * 已实名用户，若传入的psnInfo与在e签宝绑定的psnAccount一致，则无需重复实名，签署页直接进行签署意愿认证；
         * 已实名用户，若传入的psnInfo与在e签宝绑定的psnAccount不一致，则接口将会报错，建议核实用户身份信息后重新发起流程；
         * 未实名用户，签署页将根据传入的身份信息进行用户实名认证。
         */
        private PsnInfo psnInfo;

    }

    @Data
    @Accessors(chain = true)
    public static class SignFields implements Serializable {
        /**
         *签署区所在待签署文件ID
         * 【注】这里的fileId需先添加在docs数组中，否则会报错“参数错误: 文件id不在签署流程中”。
         */
        private String fileId;

        /**
         * 开发者自定义业务编号
         */
        private String customBizNum;

        /**
         * 开发者自定义业务编号
         */
        private NormalSignFieldConfig normalSignFieldConfig;


    }


    @Data
    @Accessors(chain = true)
    public static class NormalSignFieldConfig implements Serializable {
        /**
         *个是否自由签章，默认值 false
         * true - 是，false - 否
         * 补充说明：
         * 自由签章 指不限制印章、签署位置、签章样式（单页、骑缝）、和签章个数。
         * 自由签章模式下，无需传normalSignFieldConfig对象下的其他参数。
         */
        private Boolean freeMode;

        /**
         *
         * true - 后台自动落章（无感知），false - 签署页手动签章
         * 补充说明：
         * 当签署方为个人时，不支持自动签章。
         * 当签署方为机构（且非应用Id所属企业），静默签署自动落章需先经过印章授权，点击查看印章授权规则。
         * 当签署方为应用Id所属主体企业自身静默签署时，支持后台自动落章。
         */
        private Boolean autoSign;


        /**
         * 指定印章id
         */
        private String assignedSealId;
        /**
         * 签章区尺寸（正方形的边长，单位为px）
         * 【注】不指定默认以印章原始大小展示
         */
        private Float signFieldSize;

        /**
         * 签章区样式
         * 1 - 单页签章，2 - 骑缝签章
         */
        private Integer signFieldStyle;
        /**
         * 签章区位置信息
         */
        private SignFieldPosition signFieldPosition;

    }


    @Data
    @Accessors(chain = true)
    public static class SignFieldPosition implements Serializable {
        /**
         *骑缝章模式选择
         * ALL - 全部页盖骑缝章，AssignedPages - 指定页码盖骑缝章
         * 补充说明：
         * 当signFieldStyle为1即单页签章时，该参数无需设置
         * 当signFieldStyle为2即骑缝签章时，可以指定该参数，默认值ALL
         */
        private String acrossPageMode;

        /**
         * 签章区所在页码
         * 补充说明：
         * （1）当signFieldStyle为1即单页签章时，只能传单个页码
         * （2）当signFieldStyle为2即骑缝签章时，且acrossPageMode为AssignedPages即指定页码范围时，连续页码可使用'-'指定页码范围，多个页码范围用逗号分隔，例如：1-3,6-10
         */
        private String positionPage;

        /**
         * 签章区所在X坐标（当signFieldStyle为2即骑缝签章时，该参数不生效，可不传值）
         * 【注】可选择如下方式可以确定坐标：
         * （1）开放平台拖章定位工具：【请点击】
         * （2）根据关键字辅助定位接口【请点击】
         */
        private Float positionX;
        /**
         * 签章区所在Y坐标
         */
        private Float positionY;

    }


    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
