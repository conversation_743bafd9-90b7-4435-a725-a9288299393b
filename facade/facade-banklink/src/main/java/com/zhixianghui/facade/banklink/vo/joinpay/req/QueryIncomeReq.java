package com.zhixianghui.facade.banklink.vo.joinpay.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryIncomeReq extends JoinpayBaseReq implements Serializable {

    private String method = "income.query";

    /***
     * 收款账号
     */
    private String payee_account_no;

    /***
     * 查询的起始时间，对应账号入账完成时间 格式 yyyy-MM-dd HH:mm:ss
     */
    private String start_time;

    /***
     * 查询的结束时间，对应账号入账完成时间 起始截止时间区间不能大于 7 天，
     * 查询最新 50 条记录，此字段可不填写，
     * page 固定赋值为 1。
     * 需要多数据分页，必填此字段，但是不能大于当前时间。 格式 yyyy-MM-dd HH:mm:ss
     */
    private String end_time;

    /***
     * 页码，从 1 开始, 每页默认为 50 条。
     * 页面最大 999 页, 若超过 999 页，可以缩短起止时间区间
     */
    private String page;
}
