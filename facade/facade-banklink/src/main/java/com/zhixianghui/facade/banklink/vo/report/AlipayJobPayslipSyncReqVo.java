package com.zhixianghui.facade.banklink.vo.report;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class AlipayJobPayslipSyncReqVo implements Serializable {
    //userId、openid、loginId(手机号/邮箱均可) 三选一即可
    private String userId;//支付宝用户的userId
    private String openId;//支付宝openid
    private String loginId;//支付宝登录号

    private String mchOrderNo;//商户订单号，仅用作日志跟踪
    private String platTrxNo;//平台流水号，对应支付宝 out_biz_no
    private String mainstayNo;//代征主体商户编号，仅用作日志跟踪
    private String mainstayName;//代征主体商户名称，对应支付宝 company_name
    private String mainstayCertNo;//代征主体企业信用代码（可选），对应支付宝 company_cert_no
    private BigDecimal amount;//实发金额，对应支付宝 amount
    private String completeTime;//订单完成时间，格式：yyyy-MM-dd HH:mm:ss，对应支付宝 salary_time
    private String cardNo;//用户收款账号（选传），对应支付宝 card_no
    private String channel = "ALIPAY"; //对应支付宝 channel，支付宝: ALIPAY、银行卡: BANK_CARD、微信: WEIXIN、其他: OTHER
    private String contactInfo;//客服电话（选传），对应支付宝 contact_info
    private String billDetailUrl;//账单详情地址（选传），对应支付宝 bill_detail_url
    private String bankCode;//付款银行简称（选传），对应支付宝 bank_code
    private String remark;//备注信息（选传），对应支付宝 remark
    private String userName;//用户姓名（选传），对应支付宝 user_name
}
