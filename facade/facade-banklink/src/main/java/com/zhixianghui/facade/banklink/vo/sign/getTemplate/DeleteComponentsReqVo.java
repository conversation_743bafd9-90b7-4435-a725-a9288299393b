package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2021/6/8 15:07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class DeleteComponentsReqVo extends EsignBaseReqVo {

    private String templateId;
    private String ids;

    public DeleteComponentsReqVo() {}

    @Override
    public String buildFullUrl(String url) {
        return String.join("/", url, this.ids);
    }
}
