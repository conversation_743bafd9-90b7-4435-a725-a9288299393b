package com.zhixianghui.facade.banklink.service.joinpay;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.banklink.vo.withdraw.SecurityVo;
import com.zhixianghui.facade.banklink.vo.withdraw.WithdrawQueryVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月04日 09:11:00
 */
public interface JoinPayFacade {

    JSONObject withdraw(SecurityVo securityVo);

    JSONObject query(WithdrawQueryVo withdrawQueryVo);

    Map<String, String> getMainstayAmount(MainstayAmountQueryDto amountQueryDto);
}