package com.zhixianghui.facade.banklink.vo.pay;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序收款
 * <AUTHOR>
 * @ClassName MiniAppPayVo
 * @Description TODO
 * @Date 2022/12/27 17:08
 */
@Data
public class MiniAppPayVo implements Serializable {

    /**
     * 打款流水号
     */
    private String orderNo;

    /**
     * 付款用户openid
     */
    private String payUserId;

    /**
     * 收款商户号
     */
    private String receiveMchId;

    /**
     * 金额（分）
     */
    private Long amount;

    /**
     * 交易结束时间 RFC3339
     */
    private String expireTime;

    /**
     * 描述
     */
    private String description;
}
