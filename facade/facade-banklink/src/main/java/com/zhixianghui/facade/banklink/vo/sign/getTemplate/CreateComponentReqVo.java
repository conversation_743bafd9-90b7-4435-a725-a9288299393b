package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import com.zhixianghui.facade.banklink.vo.sign.components.ComponentsResDataVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName CreateFileUploadUrl
 * @Description TODO
 * @Date 2022/8/24 17:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CreateComponentReqVo extends EsignBaseReqVo {

    /**
     * 控件ID列表
     */
    private List<ComponentsResDataVo.Components> components;


    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
