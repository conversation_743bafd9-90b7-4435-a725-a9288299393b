package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @description 通过模版创建文件
 * @date 2021-01-11 15:45
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateFileByTemplateReqVo extends EsignBaseReqVo {

    private static final long serialVersionUID = 6144589639369245015L;

    /**
     * 文件名称（必须带上文件扩展名，不然会导致后续发起流程校验过不去 示例：合同.pdf ）；
     */
    private String name;

    /**
     * 模板编号
     *
     * （1）正式环境可通过e签宝网站->企业模板下创建和查询
     *
     * （2）通过上传方式方式创建模板接口获取模板id和上传链接，文件流上传文件成功之后，模板id可用于这里
     */
    private String templateId;

    /**
     * 输入项填充内容，key:value 传入；可使用输入项组件id+填充内容，也可使用输入项组件key+填充内容方式填充
     *
     * 注意：E签宝官网获取的模板id，在通过模板创建文件的时候只支持输入项组件id+填充内容
     */
    private HashMap<String,String> simpleFormFields;

    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
