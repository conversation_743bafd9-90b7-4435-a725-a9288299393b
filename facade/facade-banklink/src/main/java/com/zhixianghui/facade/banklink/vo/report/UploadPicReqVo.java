package com.zhixianghui.facade.banklink.vo.report;

import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName UploadPicReqVo
 * @Description TODO
 * @Date 2023/3/10 10:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UploadPicReqVo extends BaseKeyPairReqVo {

    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 子商户编号
     */
    private String subMerchantNo;

    /**
     * 身份证
     */
    private String idCardFront;

    /**
     * 身份证国徽面
     */
    private String idCardBack;

    /**
     *  营业执照
     */
    private String license;

    /**
     * 其他图片2
     */
    private String other1;

    /**
     * 其他图片2
     */
    private String other2;

}
