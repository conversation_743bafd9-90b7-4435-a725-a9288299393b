package com.zhixianghui.facade.banklink.service.report;

import com.zhixianghui.facade.banklink.vo.report.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 通道报备接口
 * @date 2020-10-30 10:16
 **/
public interface ChannelReportFacade {

    /**
     * 进行通道报备
     * @param reportReqVo 报备参数
     * @return 通道响应对象
     */
    ReportResVo report(ReportReqVo reportReqVo);

    /**
     * 对回调结果进行验签并封装结果
     * @param reqVo
     * @return
     */
    ReportReceiveRespVo verifyAndHandleResult(ReportReceiveReqVo reqVo);

    ReportResVo modify(ReportReqVo reportReqVo);

    Map<String, Map<String,String>> cheackMchSignAndPicUpload(AltMchSignVo vo);
}
