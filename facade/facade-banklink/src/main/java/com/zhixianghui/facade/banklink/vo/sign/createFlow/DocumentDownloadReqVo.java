package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import com.alibaba.fastjson.annotation.JSONField;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 流程文档下载Req
 * @date 2021-01-13 16:02
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class DocumentDownloadReqVo extends EsignBaseReqVo {
    /**
     * 	流程id，该参数需放在请求地址里面，可以参考【请求示例】
     */
    @JSONField(serialize = false)
    private String signFlowId;

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{signFlowId}",signFlowId);
    }
}
