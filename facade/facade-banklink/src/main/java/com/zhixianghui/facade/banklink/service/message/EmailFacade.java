package com.zhixianghui.facade.banklink.service.message;

import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.exception.BizException;

/**
 * 邮件发送接口
 */
public interface EmailFacade {


    /**
     * 根据数据库配置发送群组邮件
     *
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param isHtml  是否是html邮件
     * @return
     */
    boolean sendToGroupSync(String groupKey, String subject, String content, boolean isHtml) throws BizException;

    /**
     * 根据数据库配置发送群组邮件
     *
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param isHtml  是否是html邮件
     * @return
     */
    boolean sendToGroupAsync(String groupKey, String subject, String content, boolean isHtml) throws BizException;

    /**
     * 使用freemarker模版引擎同步发送邮件
     *
     * @param param 邮件参数
     * @return
     */
    boolean sendSync(EmailParamDto param) throws BizException;

    /**
     * 使用freemarker模版引擎异步发送邮件
     *
     * @param param 邮件参数
     * @return
     */
    boolean sendAsync(EmailParamDto param) throws BizException;

}
