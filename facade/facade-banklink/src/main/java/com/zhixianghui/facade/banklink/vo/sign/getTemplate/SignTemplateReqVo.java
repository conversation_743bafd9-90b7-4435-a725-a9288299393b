package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.alibaba.fastjson.annotation.JSONField;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 查询模版详情Req
 * @date 2021-01-08 14:47
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class SignTemplateReqVo extends EsignBaseReqVo {

    private static final long serialVersionUID = 6800231078121025969L;

    @JSONField(serialize = false)
    private String templateId;

    @Override
    public String buildFullUrl(String url) {
        return String.join("/",url,templateId);
    }

}
