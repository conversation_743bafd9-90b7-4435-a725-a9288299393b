package com.zhixianghui.facade.banklink.vo.report;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AlipayJobPayslipSyncRspVo implements Serializable {
    private boolean success;//是否调用成功
    private String mchOrderNo;//商户订单号，仅用作日志跟踪
    private String platTrxNo;//平台流水号，对应支付宝 out_biz_no
    private String bizNo;// 支付宝流水号
    private String code;
    private String msg;
    private String subCode;
    private String subMsg;

    public boolean isSuccess() {
        return success;
    }

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
