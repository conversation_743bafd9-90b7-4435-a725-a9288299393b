package com.zhixianghui.facade.banklink.vo.sign;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 签约收到的回调Vo
 * @date 2021/1/20 10:09
 **/
@Data
public class SignReceiveRespVo implements Serializable {

    private static final long serialVersionUID = -2842174784545179336L;

    /**
     * 流程id
     */
    private String flowId;

    /**
     * 签署文件主题描述
     */
    private String businessScence;

    /**
     * 任务状态
     *
     * 2-已完成: 所有签署人完成签署；
     *
     * 3-已撤销: 发起方撤销签署任务；
     *
     *
     * 5-已过期: 签署截止日到期后触发；
     *
     * 7-已拒签
     */
    private Integer flowStatus;
}
