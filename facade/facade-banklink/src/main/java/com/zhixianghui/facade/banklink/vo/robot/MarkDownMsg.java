package com.zhixianghui.facade.banklink.vo.robot;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName MarkDownMsg
 * @Description TODO
 * @Date 2021/6/4 17:46
 */
@Data
public class MarkDownMsg implements Serializable {

    private static final long serialVersionUID = -3672377928488961643L;

    //机器人类型
    private int robotType;

    //唯一键，避免重复发送
    //注意各业务重复问题
    private String unikey;

    //markdown 格式字符串
    private String content;

    public String getRobotJson(){
        return String.format(("{\"msgtype\":\"markdown\",\"markdown\":{\"content\":\"%s\"}}"), this.content);
    }
}
