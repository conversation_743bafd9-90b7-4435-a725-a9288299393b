package com.zhixianghui.facade.banklink.vo.wx;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName WxIncomeRecordVo
 * @Description TODO
 * @Date 2021/12/8 15:25
 */
@Data
public class WxIncomeRecordVo {
    private Integer totalCount;

    private Integer offset;

    private Integer limit;

    private List<BodyData> data;

    @Data
    public static class BodyData {
        private String subMchid;

        private String accountType;

        private String incomeRecordType;

        private String incomeRecordId;

        private Long amount;

        private String successTime;

        private String bankName;

        private String bankAccountName;

        private String bankAccountNumber;

        private String rechargeRemark;
    }

    private LinksData links;

    @Data
    public static class LinksData {

        private String next;

        private String prev;

        private String self;
    }
}
