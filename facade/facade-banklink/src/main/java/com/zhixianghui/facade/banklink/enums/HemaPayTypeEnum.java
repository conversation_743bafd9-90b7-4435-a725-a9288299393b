package com.zhixianghui.facade.banklink.enums;

import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName HemaPayTypeEnum
 * @Description TODO
 * @Date 2023/10/8 16:15
 */
@Getter
@AllArgsConstructor
public enum HemaPayTypeEnum {

    BANK_CARD(0, ChannelTypeEnum.BANK.getValue(),"银行卡"),

    ALIPAY(1,ChannelTypeEnum.ALIPAY.getValue(),"支付宝"),

    WXPAY(2,ChannelTypeEnum.WENXIN.getValue(),"微信");

    private int value;

    private int channelType;

    private String desc;

    public static HemaPayTypeEnum getEnumByChannelType(int channelType) {
        return Arrays.stream(values()).filter(p -> p.channelType == channelType).findFirst().orElse(null);
    }
}
