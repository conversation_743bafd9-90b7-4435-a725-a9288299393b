package com.zhixianghui.facade.banklink.vo.sign.createFlow;


import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:07
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateFlowOneStepReqVo extends EsignBaseReqVo {

    private static final long serialVersionUID = -9197392476573032411L;

    /**
     * 附件信息
     */
    private ArrayList<Attachment> attachments;

    /**
     * 抄送人人列表
     */
    private ArrayList<Copier> copiers;

    /**
     * 待签文档信息，请把要签署的文档全部都通过该参数设置上
     */
    private ArrayList<Doc> docs;

    /**
     * 流程基本信息
     */
    private FlowInfo flowInfo;

    /**
     * 签署方信息
     */
    private ArrayList<Signer> signers;

    public CreateFlowOneStepReqVo(ArrayList<Doc> docs, FlowInfo flowInfo, ArrayList<Signer> signers) {
        this.docs = docs;
        this.flowInfo = flowInfo;
        this.signers = signers;
    }

    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
