package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/24 17:46
 */
@Data
@Accessors(chain = true)
public class GetPsnSealListResDataVo implements Serializable {

     private Integer total;

     private List<Seals> seals;

     @Data
     @Accessors(chain = true)
     public static class Seals implements Serializable {

          private String sealId;

          private String sealName;

          private String sealImageDownloadUrl;

     }
}
