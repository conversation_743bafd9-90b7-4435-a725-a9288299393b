package com.zhixianghui.facade.banklink.service.pay;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;

import java.net.SocketTimeoutException;

/**
 * <AUTHOR>
 * @ClassName CKHPayFacade
 * @Description TODO
 * @Date 2022/7/1 14:54
 */
public interface CKHPayFacade {


    PayRespVo pay(PayReqVo payReqVo) throws BizException;

    PayRespVo payServiceFee(PayReqVo payReqVo) throws BizException;

    PayRespVo queryPayOrder(String remitPlatTrxNo);
}
