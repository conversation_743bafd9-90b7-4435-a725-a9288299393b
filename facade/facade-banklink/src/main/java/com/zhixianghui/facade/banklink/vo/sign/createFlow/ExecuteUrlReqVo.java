package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import com.alibaba.fastjson.annotation.JSONField;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 获取签署地址Req
 * @date 2021-01-13 10:06
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ExecuteUrlReqVo extends EsignBaseReqVo {

    /**
     * 	流程id，该参数需放在请求地址里面，可以参考【请求示例】
     */
    @JSONField(serialize = false)
    private String flowId;

    /**
     * 签署人账号id
     */
    private String accountId;

    /**
     * 默认为空，返回的任务链接仅包含签署人本人需要签署的任务；  传入0，则返回的任务链接包含签署人“本人+所有代签机构”的签署任务；  传入指定机构id，则返回的任务链接包含签署人“本人+指定代签机构”的签署任务
     */
    private String organizeId;

    /**
     * 链接类型: 0-签署链接;1-预览链接;默认0
     */
    private Integer urlType;

    /**
     * app Scheme app内对接必传
     *
     * 示例：appScheme=esign://demo/signBack
     */
    private String appScheme;

    public ExecuteUrlReqVo(String flowId, String accountId) {
        this.flowId = flowId;
        this.accountId = accountId;
    }

    @Override
    public String buildFullUrl(String url) {
        StringBuilder fullUrl = new StringBuilder(url.replace("{flowId}", this.flowId));
        fullUrl.append("?accountId=").append(accountId);
        if(organizeId!=null){
            fullUrl.append("&organizeId=").append(organizeId);
        }
        if(urlType!=null){
            fullUrl.append("&urlType=").append(urlType);
        }
        if(appScheme!=null){
            fullUrl.append("&appScheme=").append(appScheme);
        }
        return fullUrl.toString();
    }
}
