package com.zhixianghui.facade.banklink.vo.notify;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.zhixianghui.common.statics.annotations.NotSign;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 商户实际的内容
 * @date 2020-12-22 10:00
 **/
@Data
@JSONType(naming= PropertyNamingStrategy.SnakeCase)
public class NotifyContent {
    private String respCode;

    private String data;

    private String randStr;
    /**
     * 签名类型 @see
     */
    private String signType;

    /**
     * 平台商户号
     */
    private String mchNo;

    @NotSign
    private String sign;
    /**
     * 敏感信息加密串 aes加密秘钥
     */
    @NotSign
    private String secKey = "";
}
