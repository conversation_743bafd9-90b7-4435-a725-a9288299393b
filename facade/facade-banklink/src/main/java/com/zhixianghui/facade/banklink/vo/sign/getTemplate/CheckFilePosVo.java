package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年08月02日 09:42:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CheckFilePosVo extends EsignBaseReqVo implements Serializable {

    private static final long serialVersionUID = -6694794783760134471L;

    private String fileId;

    private String keywords;

    @Override
    public String buildFullUrl(String url) {
        String replace = url.replace("{fileId}", fileId);
        replace=replace+"?keywords="+keywords;
        return replace;
    }
}