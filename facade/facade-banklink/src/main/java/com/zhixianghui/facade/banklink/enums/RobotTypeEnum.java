package com.zhixianghui.facade.banklink.enums;

/**
 * <AUTHOR>
 * @ClassName RobotTypeEnum
 * @Description TODO
 * @Date 2021/6/7 9:20
 */
public enum RobotTypeEnum {

    RISK_ROBOT(100,"风控机器人"),
    GRANTING_ROBOT(101,"发放中机器人"),
    TASK_NOTIFY_ROBOT(102,"定时任务机器人"),
    MERCHANT_ADD_ROBOT(103, "商户入网通知机器人"),
    RECHARGE_ROBOT(104,"充值异常提醒"),
    NOTICE_ROBOT(105,"企业内部通知机器人"),
    WX_INCOME_ROBOT(106,"微信来账通知机器人"),
    NOTIFICATION_ROBOT(107,"消息通知机器人"),
    WX_SALE_PROD_ROBOT(108,"产品业务沟通群机器人"),

    MERCHANT_RECOMMEND_ROBOT(109, "商户推荐通知机器人"),
    ;


    RobotTypeEnum(int type, String desc){
        this.type = type;
        this.desc = desc;
    }

    int type;

    String desc;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
