package com.zhixianghui.facade.banklink.vo.joinpay.result;

import lombok.Data;

import java.io.Serializable;

@Data
public class ApplyAcountResult implements Serializable {

    /***
     * mch_no
     */
    private String mch_no;

    /***
     * 收款公司名称
     */
    private String payee_account_name;

    /***
     * 收款公司账号
     */
    private String payee_account_no;

    /***
     * 账号状态(100.激活 101.冻结)
     */
    private String payee_account_no_status;

    /***
     * 固定值，支付机构备付金集中存管账户
     */
    private String payee_account_bank;

    /***
     * 固定值，广州市
     */
    private String payee_account_loc;

    /***
     * 固定值，广州汇聚支付-备付金账户
     */
    private String payee_account_banch;

    /***
     * 固定值，************
     */
    private String payee_account_bkno;

    /***
     * 昵称
     */
    private String payee_account_nickname;

    /***
     * 付款人名称
     */
    private String payer_account_name;

    /***
     * 付款账户
     */
    private String payer_account_no;


    /***
     * 错误码，见附录 7.1.2
     * JS000000 成功
     * JS100000 失败
     * JS200000 系统异常，受理未知                     请使用查询接口查询订单状态
     * JS999999 其他原因导致交易失败，请联系汇聚客服      请联系汇聚客服解决问题
     * JS100005 商户未激活                           请联系汇聚客服确认商户开通状态
     * JS100007 用户未开通此交易类型                   请联系汇聚客服确认产品开通状态
     * JS100010 商户不存在                           请检查商户编号是否有误，或联系汇聚 客服确认产品开通状态
     * JS180006 虚拟收款账号不能超过 100 万个           创建虚拟账户个数超限
     * JS180007 虚拟账号已冻结                        冻结状态无法修改信息
     */
    private String biz_code;


    private String biz_msg;

}
