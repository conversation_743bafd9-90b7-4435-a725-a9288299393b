package com.zhixianghui.facade.banklink.vo.sign.createOrganization;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.PsnAuthUrlReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.PsnIdentityInfoResDataVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024年4月9日11:25:45
 */
@Data
@Accessors(chain = true)
public class OrgAuthUrlReqVo extends EsignBaseReqVo implements Serializable {
    /**
     *  （如果不传个人的账号信息，则需要个人用户自主在页面填写手机号/邮箱进行验证码回填注册）
     */
    private OrgAuthConfig orgAuthConfig;
    /**
     * 机构授权配置项
     * 授权过期需重新授权；
     * 不传此参数默认页面仅实名认证，不需要用户授权。
     */
    private AuthorizeConfig authorizeConfig;
    /**
     *  认证完成重定向配置项
     */
    private RedirectConfig redirectConfig;

    /**
     * 当前经办人非企业管理员的情况下，是否需要为其获取企业全部印章的用印权限，默认false
     * true - 需要
     * false - 不需要
     * 补充说明：
     * 只在配置了authorizeConfig的授权模式下生效，纯实名模式不生效
     * 第一次为企业做实名的经办人会变成企业管理员，则不需要再额外获取用印权限
     */
    private Boolean transactorUseSeal;


    /**
     * 接收回调通知的Web地址，通知开发者用户认证和授权的完成以及变更情况，
     */
    private String notifyUrl;

    /**
     * 指定客户端类型，默认值 ALL（注意参数值全部为英文大写）
     * ALL - 自动适配移动端或PC端
     * H5 - 移动端适配
     * PC - PC端适配
     */
    private String clientType;

    /**
     * AppScheme，用于支付宝人脸认证重定向时唤起指定App。
     * 示例值：esign://demo/realBack
     */
    private String appScheme;

    @Data
    @Accessors(chain = true)
    public static class OrgAuthConfig implements Serializable {

        /**
         * 组织机构名称（机构账号标识）
         * （若未知机构的orgId/机构未实名，请传此字段）
         */
        private String orgName;

        /**
         * 机构账号ID
         * （若已知机构的orgId/机构已实名，请传此字段）
         */
        private String orgId;

        /**
         * 组织机构身份附加信息
         */
        private OrgInfo orgInfo;


        /**
         * 机构实名认证页面配置项
         */
        private OrgAuthPageConfig orgAuthPageConfig;


        /**
         * 经办人身份信息
         * （如果不传经办人个人的账号信息，则需要经办人自主在页面填写手机号/邮箱进行验证码回填注册）
         */
        private PsnAuthUrlReqVo.PsnInfo transactorInfo;



        /**
         * 经办人认证页面设置
         */
        private PsnAuthUrlReqVo.PsnAuthPageConfig transactorAuthPageConfig;


    }

    @Data
    @Accessors(chain = true)
    public static class AuthorizeConfig implements Serializable {
        /**
         * 设置页面中权限范围，参数值如下：
         * 授权当前应用AppId获取用户的账号基本信息：
         * get_psn_identity_info - 授权允许获取个人用户的账号信息（姓名、手机号/邮箱、证件号等）
         * 授权当前应用AppId代用户发起合同签署：
         * psn_initiate_sign - 授权允许代表个人用户发起合同签署以及查询合同签署详情
         * 授权当前应用AppId获取用户资源管理权限：
         * manage_psn_resource - 授权允许获取个人用户的印章等资源的管理权限
         * 授权当前应用AppId存储用户的合同文件：
         * （用于平台专属云项目代客户发起合同签署场景）
         * psn_sign_file_storage - 授权个人合同文件存储到平台应用的本地服务器
         */
        private List<String> authorizedScopes;
    }

    @Data
    @Accessors(chain = true)
    public static class RedirectConfig implements Serializable {
        /**
         * 认证完成后跳转页面（需符合 https /http 协议地址）
         */
        private String redirectUrl;

        /**
         * 重定向跳转延迟时间，单位为秒。
         *
         * 授权模式下（authorizedScopes有具体的参数值）：默认延迟时间为 3秒。
         * 传 0 - 不展示授权结果页，认证完成直接跳转重定向地址
         * 传 其他数字 - 展示授权结果页，倒计时 x秒后，自动跳转重定向地址
         *
         * 实名模式下（authorizedScopes不传或者没有具体的参数值）：默认延迟时间为 5秒。
         * 传 0 - 不展示实名结果页，认证完成直接跳转重定向地址
         * 传 其他数字 - 展示实名结果页，倒计时5秒后，自动跳转重定向地址（只有5秒，没有其他秒数的控制）
         * 【注】当redirectUrl不传的情况下，该字段无需传入，认证完成结果页不跳转。
         */
        private String redirectDelayTime;

    }



    @Data
    @Accessors(chain = true)
    public static class OrgInfo implements Serializable {
        /**
         * 组织机构证件号
         */
        private String orgIDCardNum;
        /**
         * 组织机构证件类型，可选值如下：
         * CRED_ORG_USCC - 统一社会信用代码
         * CRED_ORG_REGCODE - 工商注册号
         */
        private String orgIDCardType;

        /**
         * 法定代表人姓名
         */
        private String legalRepName;


        /**
         * 法定代表人证件号
         */
        private String legalRepIDCardNum;

        /**
         * 法定代表人证件类型，可选值如下：
         * CRED_PSN_CH_IDCARD - 中国大陆居民身份证
         * CRED_PSN_CH_HONGKONG - 香港来往大陆通行证（回乡证）
         * CRED_PSN_CH_MACAO - 澳门来往大陆通行证（回乡证）
         * CRED_PSN_CH_TWCARD - 台湾来往大陆通行证（台胞证）
         * CRED_PSN_PASSPORT - 护照
         */
        private String legalRepIDCardType;

        /**
         * 企业对公打款银行账户
         * 【注】仅限实名方式为对公账户打款认证时使用
         */
        private Boolean orgBankAccountNum;

    }


    @Data
    @Accessors(chain = true)
    public static class OrgAuthPageConfig implements Serializable {
        /**
         * 指定页面中默认选择的机构认证方式：
         * ORG_BANK_TRANSFER - 对公账户打款认证
         * ORG_ALIPAY_CREDIT - 企业支付宝认证
         * ORG_LEGALREP_INVOLVED - 法定代表人认证/法人授权书认证（如操作人为法定代表人本人操作则为法定代表人认证，如非法定代表人本人则为法人授权书认证）
         */
        private String orgDefaultAuthMode;


        /**
         * 设置页面中可选择的机构认证方式，若不传此参数，则可选择全部认证方式
         * ORG_BANK_TRANSFER - 对公账户打款认证
         * ORG_ALIPAY_CREDIT - 企业支付宝认证
         * ORG_LEGALREP_INVOLVED - 法定代表人认证/法人授权书认证（如操作人为法定代表人本人操作则为法定代表人认证，如非法定代表人本人则为法人授权书认证）
         */
        private List<String> orgAvailableAuthModes;



        /**
         * 设置页面中可编辑的信息，不传此参数，页面默认不允许编辑机构信息。
         * orgNum - 机构证件号（如果账号已实名，传了该字段，页面也是不可编辑更改的，因为证件号是唯一标识）
         * legalRepName - 法定代表人姓名
         * orgBankAccountNum - 企业对公打款银行账户
         */
        private List<String> orgEditableFields;

    }



    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
