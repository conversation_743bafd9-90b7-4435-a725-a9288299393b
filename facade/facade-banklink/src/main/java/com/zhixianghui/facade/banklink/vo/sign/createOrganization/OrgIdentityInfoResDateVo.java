package com.zhixianghui.facade.banklink.vo.sign.createOrganization;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024年4月9日11:25:45
 */
@Data
@Accessors(chain = true)
public class OrgIdentityInfoResDateVo extends EsignBaseReqVo implements Serializable {
    /**
     *  机构账号ID
     */
    private String orgId;

    /**
     * 组织机构名称
     */
    private String orgName;

    /**
     * 组织机构证件号
     */
    private String orgIDCardNum;

    /**
     * 组织机构证件类型（传orgIDCardNum时，该参数为必传）
     * CRED_ORG_USCC - 统一社会信用代码
     * CRED_ORG_REGCODE - 工商注册号
     */
    private String orgIDCardType;




    @Override
    public String buildFullUrl(String url) {
        Map<String, Object> map = BeanUtil.beanToMap(this,false,true);
        String decode = URLUtil.decode(HttpUtil.toParams(map));
        return url+"?"+decode;
    }
}
