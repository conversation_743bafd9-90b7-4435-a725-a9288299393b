package com.zhixianghui.facade.banklink.service.ocr;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.vo.ocr.BusinessLicenseOctVo;
import com.zhixianghui.facade.banklink.vo.ocr.IdcardOcrVo;
import com.zhixianghui.facade.banklink.vo.ocr.OcrRequestVo;

public interface OcrFacade {
    IdcardOcrVo idCardFontOcr(OcrRequestVo ocrRequestVo) throws BizException;

    BusinessLicenseOctVo businessLicenseOcr(OcrRequestVo ocrRequestVo) throws BizException ;
}
