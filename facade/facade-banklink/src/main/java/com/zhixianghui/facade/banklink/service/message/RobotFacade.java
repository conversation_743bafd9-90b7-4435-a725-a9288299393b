package com.zhixianghui.facade.banklink.service.message;

import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;

/**
 * <AUTHOR>
 * @ClassName RobotFacade
 * @Description 企业微机器人推送
 * @Date 2021/6/4 16:47
 */
public interface RobotFacade {

    /**
     * 异步推送到机器人
     * @return
     */
    boolean pushMarkDownAsync(MarkDownMsg markDownMsg);

    /**
     * 同步推送
     * @return
     */
    boolean pushMarkDownSync(MarkDownMsg markDownMsg);

    boolean sendFileMsg(RobotTypeEnum robotType, String fileName, String fileId);
}
