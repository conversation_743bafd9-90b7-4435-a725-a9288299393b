package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ProtocolTime implements Serializable {
    private static final long serialVersionUID = 8031933421267575639L;

    /**
     * 协议开始时间 时间戳精确到秒即可
     */
    private String protocol_start;

    /**
     *协议结束时间 时间精确到秒即可
     */
    private String protocol_end;

}
