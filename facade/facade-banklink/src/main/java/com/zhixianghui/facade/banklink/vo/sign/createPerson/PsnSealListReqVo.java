package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/8/24 17:37
 */
@Data
@Accessors(chain = true)
public class PsnSealListReqVo extends EsignBaseReqVo implements Serializable {
    /**
     *  账号id
     */
    private String psnId;
    private Integer pageNum = 1;

    /**
     * 每页显示的数量，最大值：20（默认值 20）
     */
    private Integer pageSize = 20;

    @Override
    public String buildFullUrl(String url) {
        Map<String, Object> map = BeanUtil.beanToMap(this,false,true);
        String decode = URLUtil.decode(HttpUtil.toParams(map));
        return url+"?"+decode;
    }
}
