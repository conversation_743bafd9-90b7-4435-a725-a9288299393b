package com.zhixianghui.facade.banklink.vo.yishui.req;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class EnterpriseRegionRate implements Serializable {
    private static final long serialVersionUID = -953059691884312317L;
    /**
     * 出款费率JSON对象
     */
    private List<EnterpriseRegionRateSend> send;
    /**
     * 默认空对象
     */
    private List<String> charge = Lists.newArrayList();
}
