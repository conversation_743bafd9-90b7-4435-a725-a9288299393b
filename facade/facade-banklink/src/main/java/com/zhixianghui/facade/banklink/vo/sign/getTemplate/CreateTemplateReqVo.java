package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/1 14:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CreateTemplateReqVo extends EsignBaseReqVo {

    protected String docTemplateName;

    protected String fileId;

    protected Integer docTemplateType;
    protected String redirectUrl;

    protected Boolean hiddenOriginComponents;

    protected List<String> customComponents;

    protected List<Integer> basicComponentsType;

    protected List<String> signerRoles;


    @Override
    public String buildFullUrl(String url) {
        return url;
    }


}
