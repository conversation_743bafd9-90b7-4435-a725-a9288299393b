package com.zhixianghui.facade.banklink.utils.esign;

import com.google.common.collect.Maps;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.util.Map;

/**
 * <AUTHOR>
 * @description e签宝辅助类
 * @date 2021-01-05 16:27
 **/
public class ESignHelpUtil {


    public static String getStringContentMD5(byte[] bytes) {
        // 获取文件MD5的二进制数组（128位）
        byte[] md5 = getFileMD5Bytes1282(bytes);
        // 对文件MD5的二进制数组进行base64编码
        return new String(Base64.encodeBase64(md5));
    }

    private static byte[] getFileMD5Bytes1282(byte[] bytes) {
        byte[] md5Arr = null;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(bytes);
            md5Arr = md5.digest();
        }catch (NoSuchAlgorithmException e){
            e.printStackTrace();
        }
        return md5Arr;
    }


    /***
     * 计算字符串的Content-MD5
     * @param str 文件路径
     * @return Base64 字符串
     */
    public static String getStringContentMD5(String str) {
        // 获取文件MD5的二进制数组（128位）
        byte[] bytes = getFileMD5Bytes1282(str);
        // 对文件MD5的二进制数组进行base64编码
        return new String(Base64.encodeBase64(bytes));
    }
    /***
     * 获取文件MD5-二进制数组（128位）
     *
     * @param filePath 文件路径
     * @return 件MD5-二进制数组
     * @throws IOException io异常
     */
    public static byte[] getFileMD5Bytes1282(String filePath) {
        byte[] md5Bytes = null;
        File file = new File(filePath);
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[1024];
            int length = -1;
            while ((length = fileInputStream.read(buffer, 0, 1024)) != -1) {
                md5.update(buffer, 0, length);
            }
            md5Bytes = md5.digest();
        } catch (IOException | NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return md5Bytes;
    }

    /***
     *
     * @param str 待计算的消息
     * @return MD5计算后摘要值的Base64编码(ContentMD5)
     * @throws Exception 加密过程中的异常信息
     */
    public static String doContentMD5(String str) throws Exception {
        byte[] md5Bytes = null;
        MessageDigest md5 = null;
        String contentMD5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            // 计算md5函数
            md5.update(str.getBytes(StandardCharsets.UTF_8));
            // 获取文件MD5的二进制数组（128位）
            md5Bytes = md5.digest();
            // 把MD5摘要后的二进制数组md5Bytes使用Base64进行编码（而不是对32位的16进制字符串进行编码）
            contentMD5 = new String(Base64.encodeBase64(md5Bytes), StandardCharsets.UTF_8);
        } catch (NoSuchAlgorithmException e) {
            String msg = MessageFormat.format("不支持此算法: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            throw ex;
        }
        return contentMD5;
    }

    /***
     * 计算请求签名值
     *
     * @param message 待计算的消息
     * @param secret 密钥
     * @return HmacSHA256计算后摘要值的Base64编码
     * @throws Exception 加密过程中的异常信息
     */
    public static String doSignatureBase64(String message, String secret) throws Exception {
        String algorithm = "HmacSHA256";
        Mac hmacSha256;
        String digestBase64 = null;
        try {
            hmacSha256 = Mac.getInstance(algorithm);
            byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
            byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, algorithm));
            // 使用HmacSHA256对二进制数据消息Bytes计算摘要
            byte[] digestBytes = hmacSha256.doFinal(messageBytes);
            // 把摘要后的结果digestBytes转换成十六进制的字符串
            // String digestBase64 = Hex.encodeHexString(digestBytes);
            // 把摘要后的结果digestBytes使用Base64进行编码
            digestBase64 = new String(Base64.encodeBase64(digestBytes), StandardCharsets.UTF_8);
        } catch (NoSuchAlgorithmException e) {
            String msg = MessageFormat.format("不支持此算法: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            throw ex;
        } catch (InvalidKeyException e) {
            String msg = MessageFormat.format("无效的密钥规范: {0}", e.getMessage());
            Exception ex = new Exception(msg);
            ex.initCause(e);
            throw ex;
        }
        return digestBase64;
    }

    /**
     * 拼接待签名字符串
     * @param method
     * @param accept
     * @param contentMD5
     * @param contentType
     * @param date
     * @param headers
     * @param url
     * @return
     */
    public static String appendSignDataString(String method,String accept,String contentMD5,String contentType,String date,String headers,String url){
        StringBuffer sb = new StringBuffer();
        sb.append(method).append("\n").append(accept).append("\n").append(contentMD5).append("\n")
                .append(contentType).append("\n").append(date).append("\n");
        if ("".equals(headers)) {
            sb.append(headers).append(url);
        } else {
            sb.append(headers).append("\n").append(url);
        }
        return new String(sb);
    }

    /**
     * 构建签名用请求头
     * @param projectId
     * @param contentMD5
     * @param reqSignature
     * @return
     */
    public static Map<String, String> buildCommHeader(String projectId, String contentMD5, String reqSignature) {
        Map<String, String> header = Maps.newHashMap();
        header.put("X-Tsign-Open-App-Id", projectId);
        header.put("X-Tsign-Open-Ca-Timestamp", String.valueOf(System.currentTimeMillis()));
        header.put("Accept",HeaderConstant.ACCEPT.getValue());
        header.put("X-Tsign-Open-Ca-Signature",reqSignature);
        header.put("Content-MD5",contentMD5);
        header.put("Content-Type", HeaderConstant.CONTENTTYPE_JSON.getValue());
        header.put("X-Tsign-Open-Auth-Mode", HeaderConstant.AUTHMODE.getValue());
        return header;
    }
}
