package com.zhixianghui.facade.banklink.service.wx;

import com.zhixianghui.facade.banklink.vo.wx.WxResVo;

/**
 * <AUTHOR>
 * @ClassName WxPayFacade
 * @Description TODO
 * @Date 2021/12/8 15:24
 */
public interface WxPayFacade {

    WxResVo getIncomeRecord(String subMchId,String date,Integer offset) throws Exception;

    WxResVo getOrder(String orderNo) throws Exception;

    WxResVo receiptAccept(String recordItemNo);

    WxResVo queryReceiptBill(String recordItemNo);

    byte[] downloadReceiptBill(String downloadUrl,String recordItemNo);

    WxResVo withdraw(String subMchId,String withdrawNo,Integer amount,String remark);

    WxResVo getWithdrawStatus(String subMchId,String withdrawNo);
}
