package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class EnterpriseRegionRateSend implements Serializable {
    private static final long serialVersionUID = 7348776697191539995L;

    /**
     * 阶梯费率开始金额
     */
    private String money_region_start;
    /**
     * 阶梯费率结束金额
     */
    private String money_region_end;
    /**
     * 协议出款费率
     */
    private String region_rate;
    /**
     * 默认为2
     */
    private String process;

}
