package com.zhixianghui.facade.banklink.vo.sign.signAuth;

import com.alibaba.fastjson.annotation.JSONField;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 静默签署授权
 * @date 2021-01-08 17:17
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class SignAuthReqVo extends EsignBaseReqVo {

    private static final long serialVersionUID = -8077087115683099269L;

    /**
     * 授权人id，即个人账号id或机构账号id,，该参数需放在请求地址里面，可以参考【请求示例】
     */
    @JSONField(serialize = false)
    private String accountId;

    /**
     * 授权截止时间, 格式为yyyy-MM-dd HH:mm:ss，默认无限期
     */
    private String deadline;

    public SignAuthReqVo(String accountId) {
        this.accountId = accountId;
    }

    @Override
    public String buildFullUrl(String url) {
        return String.join("/",url,accountId);
    }
}
