package com.zhixianghui.facade.banklink.vo.sign.components;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:13
 **/
@Data
@Accessors(chain = true)
public class ComponentsResDataVo implements Serializable {

    private static final long serialVersionUID = 8657827238138867072L;


    private List<Components> components;

    private Integer total;


    @Data
    @Accessors(chain = true)
    public static class Components implements Serializable {

        /**
         * e签宝控件ID
         */
        private String componentId;

        /**
         * 自定义业务编码
         * TemplateComponent 的id
         */
        private String customBizNum;

        /**
         * 控件名称
         */
        private String componentName;


        /**
         * 控件类型
         * 1, 文本
         * 2, 数字
         * 3, 日期
         * 8, 多行文本
         * 9, 复选
         * 10, 单选
         * 11, 图片
         * 14, 下拉选择控件
         * 15, 勾选框控件
         * 16, 身份证控件
         * 19, 手机号
         */
        private String componentType;


        /**
         * 控件默认值
         */
        private String componentDefaultValue;


        /**
         * 控件尺寸
         */
        private ComponentSize componentSize;

        /**
         * 控件字符样式
         */
        private ComponentTextFormat componentTextFormat;

        /**
         * 是否必填
         * true-必填
         * false-非必填
         */
        private Boolean required;
        /**
         * 控件特有属性
         */
        private ComponentSpecialAttribute componentSpecialAttribute;

    }




    @Data
    @Accessors(chain = true)
    public static class ComponentSize implements Serializable {
        /**
         * 控件宽度（矩形的左右边距距离，单位为px）
         */
        private String componentWidth;
        /**
         * 控件高度（矩形的上下边距距离，单位为px）
         */
        private String componentHeight;


    }

    @Data
    @Accessors(chain = true)
    public static class ComponentTextFormat implements Serializable {
        /**
         * 填充字体,默认1，
         * 1-宋体，2-新宋体，4-黑体，5-楷体
         */
        private String font;
        /**
         *填充字体大小，默认：12-小四
         * 42-初号
         * 36-小初
         * 26-一号
         * 24-小一
         * 22-二号
         * 19-小二
         * 16-三号
         * 15-小三
         * 14-四号
         * 12-小四
         * 10.5-五号
         * 9-小五
         */
        private String fontSize;

        /**
         *字体颜色，默认#000000黑色
         */
        private String textColor;

        /**
         *是否加粗，默认false
         * true-是
         * false-否
         */
        private Boolean bold;

        /**
         *是是否斜体，默认false
         * true-是
         * false-否
         */
        private Boolean italic;

        /**
         *水平对齐，默认：左对齐
         * LEFT-左对齐
         * CENTER-居中对齐
         * RIGHT-右对齐
         */
        private String horizontalAlignment;

        /**
         *垂直对齐，默认：顶对齐（适用于多行文本）
         * TOP-顶对齐
         * MIDDLE-居中对齐
         * BOTTOM-底对齐
         */
        private String verticalAlignment;

        /**
         *行间距，默认1.0 最多支持一位小数，最大值为2.0（适用于多行文本）
         */
        private Float textLineSpacing;

    }


    @Data
    @Accessors(chain = true)
    public static class ComponentSpecialAttribute implements Serializable {
        /**
         * 数字格式（数字控件），默认整数
         * 整数：0
         * 保留一位小数：0.0
         * 保留两位小数：0.00
         * 通用数字：ANY
         * （通用数字支持整数和小数）
         */
        private String numberFormat;
        /**
         * 日期格式（日期控件），默认yyyy/MM/dd
         * yyyy/MM/dd
         * yyyy-MM-dd
         * yyyy年MM月dd日
         */
        private String dateFormat;
        /**
         * 图片类型（图片控件）
         * IDCard_widthwise 身份证 横向 锁比例
         * IDCard_longitudinal 身份证 纵向 锁比例
         * other 其他 不锁比例
         */
        private String imageType;

        /**
         * 选项（下拉选择控件、单选控件、多选控件）
         */
        private List<Options> options;

        /**
         * 控件展示顺序（默认：1）
         */
        private Integer componentOrder;

        private String signerRole;

    }


    @Data
    @Accessors(chain = true)
    public static class Options implements Serializable {
        /**
         * 选项顺序
         */
        private Integer optionOrder;
        /**
         *选项内容
         */
        private String optionContent;
        /**
         *是否默认选中
         */
        private Boolean selected;

    }


}
