package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:38
 **/
@Data
@Accessors(chain = true)
public class SignDateBean implements Serializable {

    private static final long serialVersionUID = 728478044414057717L;

    /**
     * 签章日期字体大小
     */
    private Integer fontSize;
    /**
     * 签章日期格式，yyyy年MM月dd日
     */
    private String format;
    /**
     * 页码信息，autoExecute是否自动执行为true时，并且需要展示签署日期，则需要指定日期盖章页码 ，默认当前页
     */
    private Integer posPage;
    /**
     * x坐标 ，autoExecute是否自动执行为true时，并且需要展示签署日期，则需要指定日期盖章位置 ，默认为0
     */
    private float posX;
    /**
     * y坐标 ，autoExecute是否自动执行为true时，并且需要展示签署日期，则需要指定日期盖章位置 ，默认为0
     */
    private float posY;
}
