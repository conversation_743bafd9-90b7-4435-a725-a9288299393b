package com.zhixianghui.facade.banklink.vo;

import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import lombok.Data;

import java.io.Serializable;

/**
 * 请求父类
 * <AUTHOR>
 * @date 2020/10/27
 **/
@Data
public class BaseKeyPairReqVo implements Serializable {
    /**
     * 渠道编号
     */
    private String channelNo;

    private String channelName;
    /**
     * 渠道商户编号
     */
    private String channelMchNo;
    /**
     * 秘钥对信息
     * banklink服务内部使用，无需设值
     */
    private KeyPairRecord keyPairRecord;
}
