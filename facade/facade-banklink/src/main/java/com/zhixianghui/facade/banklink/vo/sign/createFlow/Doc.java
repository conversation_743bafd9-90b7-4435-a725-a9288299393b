package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:14
 **/
@Data
@Accessors(chain = true)
public class Doc implements Serializable {

    private static final long serialVersionUID = -4702006959630791516L;
    /**
     * 文档id
     */
    private String fileId;

    /**
     * 文件名称（必须带上文件扩展名，不然会导致后续发起流程校验过不去 示例：合同.pdf ）；
     *
     * 注意：
     *
     * （1）该字段的文件后缀名称和真实的文件后缀需要一致。比如上传的文件类型是word文件，那该参数需要传“xxx.docx”，不能是“xxx.pdf”
     *
     * （2）文件名称不支持以下9个字符：/ \ : * " < > | ？
     */
    private String fileName;

    /**
     * 是否加密，0-不加密，1-加密，默认0
     * 注意：只支持编辑加密的PDF文档，且签署区设置的时候只有平台自动签署和签署方自动签署场景支持对加密文件盖章，手动签署区不支持
     */
    private String encryption;

    /**
     * 文档密码, 如果encryption值为1, 文档密码不能为空，默认没有密码
     */
    private String filePassword;

}
