package com.zhixianghui.facade.banklink.vo.pay;

import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.TreeMap;

@Data
@Accessors(chain = true)
public class VoucherVo extends BaseKeyPairReqVo implements Serializable {
    // 888账号
    private String channelAccountNo;
    // 自己生成的订单号
    private String certificateNo;
    // 业务类型
    private String bizType;
    // 原业务 商户订单号
    private String rawOrderNo;
    // 原业务成功日期	时间格式YYYY-MM-DD
    private String rechargeTime;
    // 通知地址
    private String callbackUrl;
    //
    private String platTrxNo;

    public TreeMap<String, String> fixParam(VoucherVo voucherVo) {
        TreeMap<String, String> map = new TreeMap<>();
        // 商户编号	商户编号和商户密钥在汇聚商户后台获取
        map.put("pc01_MerchantNo", voucherVo.getChannelAccountNo());
        // 商户请求订单号	凭证申请订单号
        map.put("pc02_mchOrderNo", voucherVo.getCertificateNo());
        // 业务类型
        map.put("pc03_bizType", voucherVo.getBizType());
        // 原业务 商户订单号
        map.put("pc04_orgMchOrderNo", voucherVo.getRawOrderNo());
        // 原业务成功日期	时间格式YYYY-MM-DD
        map.put("pc05_orgOrderTime",  voucherVo.getRechargeTime());
        // 通知地址 凭证生成完成后，通知的地址
        map.put("pc06_notifyUrl", voucherVo.getCallbackUrl());

        return map;
    }

    public TreeMap<String, String> fixDownloadParam(VoucherVo voucherVo) {
        TreeMap<String, String> map = new TreeMap<String, String>();
        map.put("pc01_MerchantNo", voucherVo.getChannelAccountNo());
        // 商户请求订单号	凭证申请订单号
        map.put("pc02_mchOrderNo", voucherVo.getCertificateNo());

        map.put("pc03_platTrxNo", platTrxNo);
        return map;
    }
}
