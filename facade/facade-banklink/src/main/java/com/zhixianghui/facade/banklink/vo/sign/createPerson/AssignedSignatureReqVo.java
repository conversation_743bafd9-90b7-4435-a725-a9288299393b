package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.EsignPageVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/8/24 17:37
 */
@Data
@Accessors(chain = true)
public class AssignedSignatureReqVo extends EsignPageVo implements Serializable {
    /**
     *  账号id
     */
    private String orgId;

    private String sealBizTypes ;

    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
