package com.zhixianghui.facade.banklink.vo.jxh;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class JxhParam implements Serializable {

    private static final long serialVersionUID = 8162321206841792177L;
    private int status;
    private String errorCode;
    private String errorDesc;
    private String userNo;
    private String merchantOrderNo;
    private String platformSerialNo;
    private String receiverAccountNoEnc;
    private String receiverNameEnc;
    private int paidAmount;
    private BigDecimal fee;
    private String channelNo;
    private String channelName;
    private String hmac;

    private String employerNo;
    private String mchName;
    private String realPayerName;
    private String mainstayNo;
    private String platTrxNo;
    private String serviceFee;
}
