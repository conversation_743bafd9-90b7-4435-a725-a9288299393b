package com.zhixianghui.facade.banklink.vo.auth;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Data
public class AuthReqVo implements Serializable {
    /**
     * 请求渠道订单号
     */
    private String bankOrderNo;

    /**
     * 协议流水号
     */
    private String serialNo;

    /**
     * 姓名
     */
    private String name;
    /**
     * 银行账户
     */
    private String bankAccountNo;
    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 电话号码
     */
    private String phoneNo;

    /**
     * 鉴权类型
     * @see com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum
     */
    private Integer authType;

    /**
     * @see
     */
    private String authChannel;
}
