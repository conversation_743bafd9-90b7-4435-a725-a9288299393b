package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import lombok.Data;

import java.io.Serializable;

/**
 * 通过上传创建模板
 * <AUTHOR>
 * @Date 2021/5/31 10:55
 */

@Data
public class CreateTemplateResV3DataVo implements Serializable {
    /**
     *  合同模板ID（建议开发者保管，当制作合同模板的链接过期失效后，可用于再次获取该模板的编辑链接，以及之后填充控件内容）
     */
    private String docTemplateId;
    /**
     * 制作合同模板的页面短链接（有效期24小时
      */
    private String docTemplateCreateUrl;
    /**
     * 制作合同模板的页面长链接（有效期24小时）
     */
    private String docTemplateCreateLongUrl;


}
