package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *      * @param keyword         关键字（姓名，手机号，银行卡）
 *      * @param professional_sn 人员编号
 *      * @param is_auth         是否认证 留空查全部 0未认证 1已认证
 *      * @param is_contract     是否签约 留空查全部 0未签约 1已签约
 *      * @param page_start      页码
 *      * @param page_size       每页数据条数
 */
@Data
@Accessors(chain = true)
public class ContractListQueryVo implements Serializable {

    private static final long serialVersionUID = 6373405865618144339L;
    private String keyword;
    private String professional_sn;
    private String is_auth;
    private String is_contract;
    private Pagination pagination;

}
