package com.zhixianghui.facade.banklink.vo.sign;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description e签宝响应基类
 * @date 2021-01-07 17:03
 **/
@Data
@Slf4j
public class EsignResVo<T> implements Serializable {

    private static final long serialVersionUID = 7719513075539469315L;

    /**
     * 调用返回状态 {@link com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum}
     */
    private int respStatus;
    /**
     * 通道返回code
     */
    private int code;
    private int errCode;
    private String message;
    private T data;

    public EsignResVo<T> fillEsignResVo(JSONObject respObject, Class<T> clazz){
        if(respObject == null || respObject.getInteger("code") == null){
            this.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return this;
        }
        boolean isPrimitive = false;

        try {
            isPrimitive = ((Class) clazz.getField("TYPE").get(null)).isPrimitive();
        } catch (IllegalAccessException e) {
            log.error("解析访问出错",e);
            this.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            this.setCode(respObject.getInteger("code"));
            this.setMessage("系统类型解析错误");
            return this;
        } catch (NoSuchFieldException e) {
            //pass 基础类外无Type
        }

        if(isPrimitive){
            //data值返回包装类/基础类型
            T data = respObject.getObject("data",clazz);
            this.setData(data);
        }else {
            //返回json
            JSONObject data = respObject.getJSONObject("data");
            if(data != null){
                this.setData(data.toJavaObject(clazz));
            }
        }

        //53000000为账号已存在code 一样作为成功处理
        if(respObject.getInteger("code") != 0 && respObject.getInteger("code") != 53000000){
            this.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            this.setCode(respObject.getInteger("code"));
            this.setMessage(respObject.getString("message"));
            return this;
        }

        this.setRespStatus(SignChannelStatusEnum.SUCCESS.getValue());
        this.setCode(respObject.getInteger("code"));
        this.setMessage(respObject.getString("message"));
        return this;
    }

}
