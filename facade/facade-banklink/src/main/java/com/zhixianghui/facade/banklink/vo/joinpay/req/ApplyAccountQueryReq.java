package com.zhixianghui.facade.banklink.vo.joinpay.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class ApplyAccountQueryReq extends JoinpayBaseReq implements Serializable {

    /**
     * 请求接口
     * */
    private String method = "apply.queryAccount";

    /***
     * 收款账号
     */
    private String payeeAccountNo;

}
