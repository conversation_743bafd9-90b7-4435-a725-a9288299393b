package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.PsnAuthUrlReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description 通过模版创建文件
 * @date 2021-01-11 15:45
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor

public class CreateFileByTemplateReqVoV3 extends EsignBaseReqVo {

    private static final long serialVersionUID = 6144589639369245015L;

    /**
     * 填充后生成的文件名称（可自定义文件名称）
     * 【注】文件名称不可含有以下9个特殊字符：/ \ : * " < > | ？以及所有emoji表情
     */
    private String fileName;

    /**
     * 待填充的模板ID（通过【获取制作合同模板页面】接口获取）
     */
    private String docTemplateId;

    private List<Components>  components;

    @Data
    @Accessors(chain = true)
    public static class Components implements Serializable {
        /**
         * 控件ID（设置合同模板时由e签宝系统自动生成）
         */
        private String componentId;
        /**
         * 控件Key（设置合同模板时由用户自定义）
         */
        private String componentKey;

        /**
         * 控件填充值
         * 补充说明：
         * （1）可根据控件类型进行填充，点击查看填充值示例；
         * （2）填充动态表格控件时，若需新增一行数据时 insertRow 参数值必须传 true；
         * （3）点击查看如何填充动态表格。
         */
        private String componentValue;

    }


    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
