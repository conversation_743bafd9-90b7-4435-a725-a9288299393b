package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年08月10日 17:56:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class TemplateParamVo extends EsignBaseReqVo implements Serializable {

    private String templateId;

    private static final long serialVersionUID = 159170306088022194L;

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{templateId}", templateId);
    }
}