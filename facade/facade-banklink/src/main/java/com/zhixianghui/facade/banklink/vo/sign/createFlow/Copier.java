package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description \
 * @date 2021-01-11 16:14
 **/
@Data
public class Copier implements Serializable {

    private static final long serialVersionUID = -2104235044372849385L;
    
    /**
     * 参与人account id
     */
    private String copierAccountId;

    /**
     * 参与主体类型, 0-个人, 1-企业, 默认个人
     */
    private Integer copierIdentityAccountType;

    /**
     * 参与主体账号id
     */
    private String copierIdentityAccountId;
}
