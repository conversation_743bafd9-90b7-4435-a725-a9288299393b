package com.zhixianghui.facade.banklink.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "tbl_mock_merchant")
public class MockMerchant implements Serializable {
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @TableField(value = "employer_no")
    private String employerNo;

    @TableField(value = "employer_name")
    private String employerName;

    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_EMPLOYER_NO = "employer_no";

    public static final String COL_EMPLOYER_NAME = "employer_name";

    public static final String COL_CREATE_TIME = "create_time";
}
