package com.zhixianghui.facade.banklink.vo.yishui;

import lombok.Data;

import java.io.Serializable;

@Data
public class YishuiContractInfoVo  implements Serializable {

    private static final long serialVersionUID = 229826285339614305L;
    private String name;
    private String mobile;
    private String cer_code;
    private String bank_code;
    private String contract_start_time;
    private String contract_end_time;
    private String nickname;
    private String head_img;
    private String sign_time;
    private String open_id;
    private String professional_sn;
    private String is_auth;
    private String is_contract;
    private String protocol_img;
    private String contract_img;
    private String sign_img;
    private String professional_id;
    private String cer_front_img;
    private String cer_reverse_img;
    private String cer_face;
    private String is_professional_notice;
    private String pro_come_from;
    private String sign_date;

}
