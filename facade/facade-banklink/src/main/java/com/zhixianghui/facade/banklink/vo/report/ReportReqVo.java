package com.zhixianghui.facade.banklink.vo.report;

import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-30 15:13
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ReportReqVo extends BaseKeyPairReqVo {

    private static final long serialVersionUID = -6118430170541391657L;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 登录名（注册名）
     */
    private String loginName;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 企业简称
     */
    private String shortName;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 经营范围
     */
    private String managementScope;

    /**
     * 经营地址-详细
     */
    private String managementAddrDetail;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 法人证件号码
     */
    private String certificateNumber;

    /**
     * 法人证件有效期止
     */
    private String certificateTermEnd;

    /**
     * 税号/社会信用代码
     */
    private String taxNo;

    /**
     * 营业时间止
     */
    private String managementTermEnd;

    /**
     * 银行账户名
     */
    private String accountName;

    /**
     * 银行账户
     */
    private String accountNo;

    /**
     * 银行行号
     */
    private String bankChannelNo;

    /**
     * 商户通道编号
     */
    private String subMerchantNo;

}
