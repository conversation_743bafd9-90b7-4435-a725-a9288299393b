package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:32
 **/
@Data
@Accessors(chain = true)
public class Signfield implements Serializable {

    private static final long serialVersionUID = 4127751577301998907L;
    /**
     * 是否自动执行，默认false（如果自动签署，必须设置为true）
     */
    private boolean autoExecute;

    /**
     * 机构签约类别，当签约主体为机构时必传：2-机构盖章（如果是平台方自动签署，该字段必传，传入2）；
     *
     * 注：
     *
     * 1、签署主体是个人时，无需传入该参数；
     *
     * 2、平台用户自动签署时，无需传入该参数
     */
    private String actorIndentityType;

    /**
     * 文件fileId
     */
    private String fileId;

    /**
     * 印章id
     *
     * 需要注意的是：如果开通了实名签，企业签署这种场景不支持指定印章，个人签署场景是支持的
     */
    private String sealId;

    /**
     *
     */
    private ArrayList<String> sealIds;

    /**
     * 签署方式，个人签署时支持多种签署方式，0-手绘签名  ，1-模板印章签名，多种类型时逗号分割，为空不限制
     */
    private String sealType;

    /**
     * 签署类型，0-不限，1-单页签署，2-骑缝签署，默认1
     */
    private Integer signType;

    /**
     * 签署区位置信息 。
     *
     * signType为0时，本参数无效； signType为1时, 页码和XY坐标不能为空,；
     *
     * signType为2时, 页码和Y坐标不能为空
     */
    private PosBean posBean;

    /**
     * 签署区的宽度
     */
    private Integer width;

    /**
     * 是否需要添加签署日期，0-禁止 1-必须 2-不限制，默认0
     */
    private Integer signDateBeanType;

    /**
     * 签署日期信息
     */
    private SignDateBean signDateBean;
}
