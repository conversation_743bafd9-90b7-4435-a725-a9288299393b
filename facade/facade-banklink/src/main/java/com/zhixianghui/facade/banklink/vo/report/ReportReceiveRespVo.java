package com.zhixianghui.facade.banklink.vo.report;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 报备回调信息实体
 * @date 2020-11-02 11:49
 **/
@Data
public class ReportReceiveRespVo implements Serializable {

    /**
     * 报备返回父商户编号
     */
    private String parentMchNo;

    /**
     * 报备返回子商户编号
     */
    private String mchNo;

    /**
     * 报备状态
     * @see com.zhixianghui.common.statics.enums.bankLink.report.ApiReportStatusEnum
     */
    private Integer apiReportStatus;

    /**
     * 业务码
     */
    private String bizCode;

    /**
     * 业务信息
     */
    private String bizMsg;
}
