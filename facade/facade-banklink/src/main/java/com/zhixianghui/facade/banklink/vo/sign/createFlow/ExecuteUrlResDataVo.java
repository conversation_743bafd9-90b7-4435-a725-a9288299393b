package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-13 10:11
 **/
@Data
public class ExecuteUrlResDataVo implements Serializable {

    private static final long serialVersionUID = -2943587055462698737L;
    /**
     * 短链地址（30天有效）
     */
    private String shortUrl;

    /**
     * 长链地址(永久有效)
     */
    private String url;

    private boolean exist;

    private boolean resendExceedLimit;
}
