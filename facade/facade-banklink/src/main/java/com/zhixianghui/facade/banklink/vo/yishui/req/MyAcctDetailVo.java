package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *      * @param token
 *      * @param client_sn 人员编码
 *      * @param client_name 人员姓名
 *      * @param enterprise_order_ext_sn 订单编号
 *      * @param enterprise_order_sn 批次编号
 *      * @param op_type 1充值 2退款 3发放 4调账
 *      * @param page_start 当前页码
 *      * @param page_size 每页数据量
 */
@Data
@Accessors(chain = true)
public class MyAcctDetailVo implements Serializable {

    private static final long serialVersionUID = -6016098618357998901L;
    private String  client_sn;
    private String  client_name;
    private String  enterprise_order_ext_sn;
    private String  enterprise_order_sn;
    private String  op_type;
    private Pagination pagination;

}
