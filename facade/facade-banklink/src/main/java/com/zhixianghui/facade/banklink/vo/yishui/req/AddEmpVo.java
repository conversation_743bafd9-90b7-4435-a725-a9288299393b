package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class AddEmpVo implements Serializable {
    private static final long serialVersionUID = 4489732797354462749L;
    /**
     * 人员姓名
     */
    private String name;
    /**
     * 身份证号码
     */
    private String cer_code;
    /**
     * 银行卡号
     */
    private String bank_code;
    /**
     * 银行卡预留手机号
     */
    private String mobile;
    /**
     * 是否实时认证 1实时 0不实时 不实时时候人员会新增成功只是认证失败会查询到认证失败签约失败
     */
    private String has_auth;
    /**
     * 人员手写签名照片
     */
    private String sign_img;
    /**
     * 自由职业合作服务协议
     */
    private String protocol_img;
    /**
     * 诚信纳税承诺书、税务办理授权委托书
     */
    private String contract_img;
}
