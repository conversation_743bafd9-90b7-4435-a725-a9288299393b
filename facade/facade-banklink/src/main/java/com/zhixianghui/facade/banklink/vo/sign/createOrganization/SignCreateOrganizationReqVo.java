package com.zhixianghui.facade.banklink.vo.sign.createOrganization;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 机构账号创建
 * @date 2021-01-07 17:29
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class SignCreateOrganizationReqVo extends EsignBaseReqVo {

    private static final long serialVersionUID = 4631830362519574936L;

    /**
     * 机构唯一标识，可传入第三方平台机构id、企业证件号、企业邮箱等如果设置则作为账号唯一性字段，相同id不可重复创建。（个人用户与机构的唯一标识不可重复）
     */
    private String thirdPartyUserId;

    /**
     * 创建人个人账号id（调用个人账号创建接口返回的accountId）
     */
    private String creator;

    /**
     * 机构名称
     */
    private String name;

    /**
     * 证件类型，默认CRED_ORG_USCC
     */
    private String idType;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 企业法定代表人证件号
     */
    private String orgLegalIdNumber;

    /**
     * 企业法定代表人名称
     */
    private String orgLegalName;

    public SignCreateOrganizationReqVo(String thirdPartyUserId, String creator, String name, String idType, String idNumber) {
        this.thirdPartyUserId = thirdPartyUserId;
        this.creator = creator;
        this.name = name;
        this.idType = idType;
        this.idNumber = idNumber;
    }

    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
