package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class SaveEnterpriseDraftRate implements Serializable {
    private static final long serialVersionUID = 6002511031366123783L;

    /**
     *附属协议资源ID 资源上传接口返回的ID 多个数组多个ID
     */
    private List<String>  protocol_img_extend;
    /**
     *主协议资源ID 资源上传协议 主协议只支持一个 请传数组 内容一个ID即可
     */
    private List<String>  protocol_img;
    /**
     *协议时间
     */
    private ProtocolTime  protocol_time;

    /**
     *
     */
    private SaveEnterpriseDraftRate enterprise_region_rate;
    /**
     *1按月累计 2按季度累计 3按年度累计
     */
    private String  region_type;
    /**
     *企业草稿费率协议ID 列表有返回
     */
    private String  enterprise_facilitator_draft_id;
    /**
     *默认为字符串0.00
     */
    private String  standard_rate;

}
