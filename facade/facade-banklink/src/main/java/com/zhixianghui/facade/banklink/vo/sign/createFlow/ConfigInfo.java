package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 流程配置信息
 * @date 2021-01-13 10:28
 **/
@Data
public class ConfigInfo implements Serializable {
    private static final long serialVersionUID = 4407459175887313516L;
    /**
     * 通知开发者地址
     */
    private String noticeDeveloperUrl;

    /**
     * 通知方式，逗号分割，1-短信 2-邮件 3-支付宝 4-钉钉
     */
    private String noticeType;

    /**
     * 签署完成重定向地址
     */
    private String redirectUrl;
    /**
     * 签署平台，逗号分割， 1-开放服务h5，
     */
    private String signPlatform;
}
