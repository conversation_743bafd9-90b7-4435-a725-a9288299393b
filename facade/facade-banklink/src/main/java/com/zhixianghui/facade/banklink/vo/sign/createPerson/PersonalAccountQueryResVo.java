package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import lombok.Data;

import java.io.Serializable;

@Data
public class PersonalAccountQueryResVo implements Serializable {

    private static final long serialVersionUID = -2520138952995164176L;
    private String accountId;
    private String name;
    private String idType;
    private String idNumber;
    private String mobile;
    private String email;
    private String thirdPartyUserId;
    private String status;

}
