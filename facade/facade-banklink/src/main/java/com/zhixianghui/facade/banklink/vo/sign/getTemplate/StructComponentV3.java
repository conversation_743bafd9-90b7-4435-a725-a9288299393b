package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.components.ComponentsResDataVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 模板输入项
 * @date 2021-01-08 14:59
 **/
@Data
public class StructComponentV3 implements Serializable {

    private static final long serialVersionUID = -2208442009165777979L;

    private String componentId;
    private String componentKey;
    private String componentName;
    private boolean required;
    private int componentType;

    private ComponentPosition componentPosition;

    private ComponentsResDataVo.ComponentSpecialAttribute componentSpecialAttribute;

    /**
     * 控件尺寸
     */
    private ComponentsResDataVo.ComponentSize componentSize;

    private NormalSignField normalSignField;

    @Data
    @Accessors(chain = true)
    public static class NormalSignField implements Serializable {

        private Integer showSignDate;

        private Integer signFieldStyle;
        private Integer sealSpecs;

    }

}
