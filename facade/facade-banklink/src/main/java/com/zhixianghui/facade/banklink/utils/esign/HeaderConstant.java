package com.zhixianghui.facade.banklink.utils.esign;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description 头部信息常量
 * @date 2021-01-05 17:04
 **/
@Getter
@AllArgsConstructor
public enum HeaderConstant {
    ACCEPT("*/*"),
    DATE(""),
    HEADERS( ""),
    CONTENTTYPE_JSON("application/json; charset=UTF-8"),
    CONTENTTYPE_PDF("application/pdf"),
    CONTENTTYPE_STREAM("application/octet-stream"),
    AUTHMODE("Signature");

    private String value;
}
