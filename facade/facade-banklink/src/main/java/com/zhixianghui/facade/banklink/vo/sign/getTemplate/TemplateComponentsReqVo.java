package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/8 11:34
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class TemplateComponentsReqVo extends EsignBaseReqVo {
    private String templateId;
    private Object structComponent;

    public TemplateComponentsReqVo() {}

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{templateId}", templateId);
//        return url;
    }
}
