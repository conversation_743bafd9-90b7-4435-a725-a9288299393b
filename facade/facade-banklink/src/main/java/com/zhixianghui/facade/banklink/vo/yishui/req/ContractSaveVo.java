package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ContractSaveVo  implements Serializable {

    private static final long serialVersionUID = 511644982699769079L;
    /**
     * 签约ID
     */
    private String enterprise_professional_facilitator_id;
    /**
     * 姓名（认证失败才能修改）
     */
    private String name;
    /**
     * 手机号（认证失败才能修改）
     */
    private String mobile;
    /**
     * 身份证号（认证失败才能修改）
     */
    private String cer_code;
    /**
     * 身份证反面照URL
     */
    private String cer_reverse_img;
    /**
     * 银行卡号
     */
    private String bank_code;
    /**
     * 身份证反面照URL
     */
    private String cer_front_img;
    /**
     * 手写签名照URL
     */
    private String sign_img;
    /**
     * 	人员半身照URL
     */
    private String cer_face;
    /**
     * 自由职业者签约协议
     */
    private String protocol_img;
    /**
     * 自由职业者其他签约信息文件
     */
    private String contract_img;

}
