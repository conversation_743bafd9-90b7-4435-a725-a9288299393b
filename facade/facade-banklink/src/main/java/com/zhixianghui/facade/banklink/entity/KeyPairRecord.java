package com.zhixianghui.facade.banklink.entity;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import lombok.Data;

/**
 * 秘钥对记录
 * <AUTHOR>
 */
@Data
public class KeyPairRecord extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新者
	 */
	private String updator;

	/**
	 * 渠道编号
	 */
	private String channelNo;

	/**
	 * 渠道商户号
	 */
	private String channelMchNo;

	/**
	 * 渠道商户私钥
	 */
	private String mchPrivateKey;

	/**
	 * 渠道商户公钥
	 */
	private String mchPublicKey;

	/**
	 * 渠道公钥
	 */
	private String channelPublicKey;

	/**
	 * 加密id
     * 必须在敏感信息之前设值
	 */
	private Long encryptKeyId;

	private String channelLoginUser;

	private String channelPlatNo;

	public String getChannelLoginUserDecrypt() {
		return StringUtil.isEmpty(this.channelLoginUser)?"":AESUtil.decryptECB(this.channelLoginUser, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
	}

	public void setChannelLoginUserEncrypt(String channelLoginUser) {
		this.channelLoginUser = StringUtil.isEmpty(channelLoginUser)?"":AESUtil.encryptECB(channelLoginUser, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
	}

    public String getMchPrivateKeyDecrypt() {
        return StringUtil.isEmpty(this.mchPrivateKey)?"":AESUtil.decryptECB(this.mchPrivateKey, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setMchPrivateKeyEncrypt(String mchPrivateKey) {
        this.mchPrivateKey = StringUtil.isEmpty(mchPrivateKey)?"":AESUtil.encryptECB(mchPrivateKey, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public String getMchPublicKeyDecrypt() {
        return StringUtil.isEmpty(this.mchPublicKey)?"":AESUtil.decryptECB(this.mchPublicKey, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setMchPublicKeyEncrypt(String mchPublicKey) {
        this.mchPublicKey = StringUtil.isEmpty(mchPublicKey)?"":AESUtil.encryptECB(mchPublicKey, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public String getChannelPublicKeyDecrypt() {
        return StringUtil.isEmpty(this.channelPublicKey)?"":AESUtil.decryptECB(this.channelPublicKey, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setChannelPublicKeyEncrypt(String channelPublicKey) {
        this.channelPublicKey = StringUtil.isEmpty(channelPublicKey)?"": AESUtil.encryptECB(channelPublicKey, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void genEncryptKeyId(){
        this.encryptKeyId = EncryptKeys.getRandomEncryptKeyId();
    }
}
