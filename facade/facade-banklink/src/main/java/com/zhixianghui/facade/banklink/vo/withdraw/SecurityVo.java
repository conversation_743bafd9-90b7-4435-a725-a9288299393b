package com.zhixianghui.facade.banklink.vo.withdraw;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.SignUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月31日 10:43:00
 */
@Data
public class SecurityVo implements Serializable {
    private static final long serialVersionUID = 7658654111721924000L;

    private String version = "1.1";
    private String signType = "1";
    private String mchNo;

    private String callbackUrl;
    private BigDecimal settleFee = BigDecimal.ZERO;
    private BigDecimal settleAmount;
    private String altMchNo;
    private String productCode = "2";
    private String mchOrderNo;

    private KeyPairRecord keyPairRecord;


    public TreeMap<String, Object> getQuery() {
        //组装参数实体
        LinkedHashMap<String, Object> data = new LinkedHashMap<>();
        data.put("mch_order_no", getMchOrderNo());

        TreeMap<String, Object> map = new TreeMap<>();
        map.put("method", "altSettle.get");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("version", getVersion());
        map.put("sign_type", getSignType());
        map.put("mch_no", getMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map,keyPairRecord.getMchPrivateKeyDecrypt()));
//        map.put("sign", SignUtil.genSign_2_0(map, "d03e21ee22a54b21841fdb8a2f50d3f5"));
        return map;
    }


    public TreeMap<String, Object> getParam() {
        //组装参数实体
        LinkedHashMap<String, Object> data = new LinkedHashMap<>();
        data.put("callback_url", getCallbackUrl());
        // 平台服务费
        data.put("settle_fee", getSettleFee());
        // 结算金额
        data.put("settle_amount", getSettleAmount());
        // 分账方编号
        data.put("alt_mch_no", getAltMchNo());
        // 产品类型
        data.put("product_code", getProductCode());
        // 结算订单号
        data.put("mch_order_no", getMchOrderNo());


        TreeMap<String, Object> map = new TreeMap<>();
        map.put("method", "altSettle.launch");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("version", getVersion());
        map.put("sign_type", getSignType());
        map.put("mch_no", getMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map, keyPairRecord.getMchPrivateKeyDecrypt()));
//        map.put("sign", SignUtil.genSign_2_0(map,"d03e21ee22a54b21841fdb8a2f50d3f5"));
        return map;
    }


}