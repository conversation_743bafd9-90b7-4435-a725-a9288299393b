package com.zhixianghui.facade.banklink.service.pay;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.vo.pay.*;

/**
 * 出款支付
 * <AUTHOR>
 * @date 2020/10/26
 **/
public interface PayBankFacade {
    /**
     * 出款请求
     * @param reqVo
     * @return
     */
    PayRespVo pay(PayReqVo reqVo) throws BizException;

    void refundFee(String platTrxNo,String payChannelNo, String chanelAcctNo, String mchAcctBookNo, String feeAmt) throws BizException;

    /**
     * 查询订单结果 --汇聚支付
     * @param reqVo
     * @return
     */
    PayRespVo queryPayOrder(QueryPayOrderReqVo reqVo);

    /**
     * 对回调结果进行验签并封装结果
     * @param reqVo
     * @return
     */
    PayReceiveRespVo verifyAndHandleResult(PayReceiveReqVo reqVo);

    /**
     * 查看订单状态
     * @param queryPayOrderReqVo
     * @return
     */
    PayRespVo queryAliPayOrder(QueryPayOrderReqVo queryPayOrderReqVo);

    /**
     * 退款
     */
    void payRefund(PayReqVo payReqVo);

    /***
     * 处理君享汇下发异步通知
     * @param reqVo
     * @return
     */
    SinglePayReceiveRespVo verifySinglePayAndHandleResult(PayReceiveReqVo reqVo);
}
