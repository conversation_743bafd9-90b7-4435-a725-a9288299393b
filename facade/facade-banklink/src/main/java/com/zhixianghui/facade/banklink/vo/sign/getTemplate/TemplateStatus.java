package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2021/6/23 8:06
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class TemplateStatus extends EsignBaseReqVo {

    public TemplateStatus() {}

    private String templateId;

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{templateId}", templateId);
    }
}
