package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 通过上传创建模板
 * <AUTHOR>
 * @Date 2021/5/31 10:55
 */

@Data
public class CreateSignTemplateResDataVo implements Serializable {
    /**
     *  文件Id
     */
    private String templateId;
    /**
     * 文件直传地址, 可以重复使用，但是只能传一样的文件，有效期一小时
      */
    private String uploadUrl;

}
