package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/8/24 17:37
 */
@Data
@Accessors(chain = true)
public class PersonalSignatureReqVo extends EsignBaseReqVo implements Serializable {
    /**
     *  账号id
     */
    private String psnId;

    private String sealName = "个人签章";
    /**
     * 颜色, 默认红色
     */
    private String sealColor = "RED";
    /**
     * 类型
     */
    private String sealTemplateStyle = "RECTANGLE_NO_BORDER";

    private String sealSize = "20_10";

    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
