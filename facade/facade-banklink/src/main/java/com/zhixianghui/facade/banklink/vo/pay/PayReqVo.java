package com.zhixianghui.facade.banklink.vo.pay;

import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Data
public class PayReqVo extends BaseKeyPairReqVo {
    private static final long serialVersionUID = -4265742221342317048L;
    /**
     * 渠道出款账户
     * 如汇聚分账方账号
     */
    private String payerChannelAccountNo;
    /**
     * 请求渠道订单号
     */
    private String bankOrderNo;
    /**
     * 收款人姓名
     */
    private String receiveName;
    /**
     * 收款人账号
     */
    private String receiveAccountNo;
    /**
     * 实发金额
     */
    private String receiveAmount;
    /**
     * 服务费
     */
    private String serviceFee;
    /**
     * 总额
     */
    private String totalAmount;
    /**
     * 打款备注
     * 部分银行支持将该值显示给收款人
     */
    private String remitRemark;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 父账号
     */
    private String parentMerchantNo;


    /**
     * 子账号
     */
    private String subMerchantNo;

    /**
     * 收款方协议号
     */
    private String payeeAgreementNo;

    /**
     * 付款方协议号
     */
    private String payerAgreementNo;

    /**
     * 真实付款方名称-一般为用工企业名称
     */
    private String realPayerName;


    /**
     * 支付宝场景码
     */
    private String bizScene;
    /**
     * 支付宝产品码
     */
    private String productCode;

    private boolean retry;

    private int retryStep;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 商户名称
     */
    private String mchName;

    private Date createTime;

    private boolean doStepOne;

    private boolean doStepTwo;

    private String idCardNumber;

    private String employerNo;

    private String appid;

    private String mainstayNo;

    private boolean isUpdate;

    /**
     * 银行/渠道交易流水号
     */
    private String bankTrxNo;

    private String payerBankName;

    /**
     * 招行专用
     */
    private String feeTrxNo;

    private String payeeBankName;

    private String payBatchNo;


    /***
     * 君享汇增加对公出款
     * accountType：对私账户：201,对公账户：204
     */
    private Integer accountType;

    /***
     * 联行号，对公时必填
     */
    private String bankChannelNo;

}
