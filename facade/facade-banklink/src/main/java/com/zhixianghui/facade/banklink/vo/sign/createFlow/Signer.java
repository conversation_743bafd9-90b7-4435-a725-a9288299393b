package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:14
 **/
@Data
@Accessors(chain = true)
public class Signer implements Serializable {

    private static final long serialVersionUID = -8914178525738086204L;

    /**
     * 是否平台自动签署，默认false
     *
     * false-为对接平台的用户签署
     *
     * true-平台方自动签署
     */
    private boolean platformSign;

    /**
     * 签署方签署顺序，默认1,且不小于1，顺序越小越先处理
     */
    private Integer signOrder;

    /**
     * 签署方账号信息（平台方自动签署时，无需传入该参数）
     */
    private SignerAccount signerAccount;

    /**
     * 签署文件信息
     */
    private ArrayList<Signfield> signfields;

    /**
     * 第三方流水号
     */
    private String thirdOrderNo;
}
