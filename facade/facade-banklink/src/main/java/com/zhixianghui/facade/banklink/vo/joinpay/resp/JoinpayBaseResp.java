package com.zhixianghui.facade.banklink.vo.joinpay.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JoinpayBaseResp<T> implements Serializable {
    /***
     * 受理响应码
     */
    private String resp_code;

    /***
     * 订单业务码，见附录 7.1.2
     */
    private String biz_code;

    /***
     * 业务码描述，见附录 7.1.2
     */
    private String biz_msg;

    /***
     * 响应数据，详情参数参加表 4-18
     */
    private T data;

    /***
     * 随机字符串，增加签名的不可预测性
     */
    private String rand_str;

    /***
     * 签名类型：RSA=2
     */
    private String sign_type;

    /***
     * 商户编号
     */
    private String mch_no;

    /***
     * 签名串
     */
    private String sign;

    /***
     * 存放 RSA 加密后的 AES 密钥。
     * 明文对敏感信息加解密，原值（没有进行 RSA 加密前的数据）固定为 16 位数字或者字母的字 符串
     */
    private String aes_key;

}
