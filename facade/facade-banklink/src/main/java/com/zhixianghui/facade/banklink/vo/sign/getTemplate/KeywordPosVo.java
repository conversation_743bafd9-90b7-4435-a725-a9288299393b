package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年08月02日 10:51:00
 */
@Data
public class KeywordPosVo implements Serializable {
    private static final long serialVersionUID = 3971155447438801483L;
    private String keyword;
    private boolean searchResult;
    private List<Positions> positions;

    @Data
    public static class Positions implements Serializable {
        private static final long serialVersionUID = -7724827052182142345L;
        private int pageNum;
        private List<Coordinates> coordinates;
    }

    @Data
    public static class Coordinates implements Serializable {

        private static final long serialVersionUID = 7114339039878287589L;

        private float positonX;

        private float positonY;
    }
}