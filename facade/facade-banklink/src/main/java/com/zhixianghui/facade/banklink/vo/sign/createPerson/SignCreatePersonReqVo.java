package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 签约个人账户创建请求Vo
 * @date 2021-01-05 17:39
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class SignCreatePersonReqVo extends EsignBaseReqVo {

    private static final long serialVersionUID = 8785260189568914043L;

    /**
     * 用户唯一标识，可传入第三方平台的个人用户id、证件号、手机号、邮箱等，如果设置则作为账号唯一性字段，相同信息不可重复创建
     */
    private String thirdPartyUserId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 证件类型，默认CRED_PSN_CH_IDCARD  目前只接身份证
     *
     * （1）CRED_PSN_CH_IDCARD大陆身份证，默认值
     *
     * （2）CRED_PSN_CH_TWCARD台湾来往大陆通行证
     *
     * （3）CRED_PSN_CH_MACAO澳门来往大陆通行证
     *
     * （4）CRED_PSN_CH_HONGKONG香港来往大陆通行证（5）CRED_PSN_FOREIGN外籍证件
     *
     * （6）CRED_PSN_PASSPORT护照
     *
     * （7）CRED_PSN_CH_SOLDIER_IDCARD军官证
     *
     * （8）CRED_PSN_CH_SSCARD社会保障卡
     *
     * （9）CRED_PSN_CH_ARMED_POLICE_IDCARD武装警察身份证件
     *
     * （10）CRED_PSN_CH_RESIDENCE_BOOKLET户口簿（11）CRED_PSN_CH_TEMPORARY_IDCARD临时居民身份证
     *
     * （12）CRED_PSN_CH_GREEN_CARD外国人永久居留证（13）CRED_PSN_SHAREHOLDER_CODE股东代码证（14）CRED_PSN_POLICE_ID_CARD警官证
     *
     * （15）CRED_PSN_UNKNOWN未知类型
     */
    private String idType;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 手机号码，默认空，手机号为空时无法使用短信意愿认证
     */
    private String mobile;

    /**
     * 邮箱地址，默认空
     */
    private String email;

    public SignCreatePersonReqVo(String thirdPartyUserId, String name, String idType, String idNumber) {
        this.thirdPartyUserId = thirdPartyUserId;
        this.name = name;
        this.idType = idType;
        this.idNumber = idNumber;
    }

    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
