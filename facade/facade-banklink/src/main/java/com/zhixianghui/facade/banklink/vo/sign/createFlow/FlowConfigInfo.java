package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:26
 **/
@Data
@Accessors(chain = true)
public class FlowConfigInfo implements Serializable {

    private static final long serialVersionUID = 4645172420673323677L;
    /**
     * 通知开发者地址
     */
    private String noticeDeveloperUrl;

    /**
     * 通知方式，可选择多种通知方式，逗号分割，1-短信，2-邮件。 默认1
     *
     * 注：短信或者邮件获取到的签署链接，有效期默认30天；如果客户需要不通知，可以设置noticeType=""
     */
    private String noticeType;

    /**
     * 签署完成重定向地址
     */
    private String redirectUrl;

    /**
     * 签署平台，可选择多种签署平台，逗号分割，1-开放服务h5，2-支付宝签 ，默认值1，2
     */
    private String signPlatform;

    /**
     * 签署完成重定向跳转延迟时间，默认3。
     *
     * 0-不展示签署完成结果页，签署完成直接跳转重定向地址
     *
     * 3-展示签署完成结果页，倒计时3秒后，自动跳转重定向地址
     *
     * 注：当redirectUrl不传的情况下，该字段无需传入，默认签署完成结果页不跳转
     */
    private Integer redirectDelayTime;
}
