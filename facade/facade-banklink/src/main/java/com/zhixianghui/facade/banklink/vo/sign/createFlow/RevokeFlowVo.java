package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
public class RevokeFlowVo extends EsignBaseReqVo implements Serializable {
    /**
     *  流程id
     */
    private String flowId;

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{flowId}", flowId);
    }
}
