package com.zhixianghui.facade.banklink.vo.account;

import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 登录名反查子商户编号
 * @date 2020-11-11 17:44
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class SubMchNoQueryDto extends BaseKeyPairReqVo {

    private static final long serialVersionUID = -8492055363588983214L;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 通道类型
     */
    private Integer channelType;


}
