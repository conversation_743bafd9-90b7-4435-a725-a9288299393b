package com.zhixianghui.facade.banklink.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
    * 招行记账子单元交易记录
    */
@Data
@TableName(value = "tbl_cmb_orders")
public class CmbOrders implements Serializable {
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 帐号
     */
    @TableField(value = "accnbr")
    private String accnbr;

    @TableField(value = "autflg")
    private int autflg;

    /**
     * 记账子单元编号
     */
    @TableField(value = "dmanbr")
    private String dmanbr;

    /**
     * 记账子单元名称
     */
    @TableField(value = "dmanam")
    private String dmanam;

    /**
     * 记账流水号
     */
    @TableField(value = "trxnbr")
    private String trxnbr;

    /**
     * 交易金额
     */
    @TableField(value = "trxamt")
    private BigDecimal trxamt;

    /**
     * 交易方向
     */
    @TableField(value = "trxdir")
    private String trxdir;

    /**
     * 交易时间
     */
    @TableField(value = "trxtim")
    private String trxtim;

    /**
     * 收方/付方账号
     */
    @TableField(value = "rpyacc")
    private String rpyacc;

    /**
     * 收方/付方名称
     */
    @TableField(value = "rpynam")
    private String rpynam;

    /**
     * 交易摘要
     */
    @TableField(value = "trxtxt")
    private String trxtxt;

    /**
     * 原内部编号
     */
    @TableField(value = "narinn")
    private String narinn;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(value = "version")
    @Version
    private Integer version;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_ACCNBR = "accnbr";

    public static final String COL_DMANBR = "dmanbr";

    public static final String COL_DMANAM = "dmanam";

    public static final String COL_TRXNBR = "trxnbr";

    public static final String COL_TRXAMT = "trxamt";

    public static final String COL_TRXDIR = "trxdir";

    public static final String COL_TRXTIM = "trxtim";

    public static final String COL_RPYACC = "rpyacc";

    public static final String COL_RPYNAM = "rpynam";

    public static final String COL_TRXTXT = "trxtxt";

    public static final String COL_NARINN = "narinn";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_VERSION = "version";
}
