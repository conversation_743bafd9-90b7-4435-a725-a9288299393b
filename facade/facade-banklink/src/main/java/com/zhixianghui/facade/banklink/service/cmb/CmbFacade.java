package com.zhixianghui.facade.banklink.service.cmb;

import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.vo.pay.CmbPayeeVo;
import com.zhixianghui.facade.banklink.vo.pay.CmbPayerVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawReqVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawRespVo;

import java.util.List;

public interface CmbFacade {
    JSONObject createAccountBook(String accountNo, String accountBookNo, String accountBookName) throws BizException;

    JSONObject queryAccountBook(String accountNo, String accountBookNo) throws BizException;

    JSONObject payBatchQuery(String accountNo, String remitNo) throws BizException;

    JSONObject queryDailyAccountNoBalance(String bankBranch, String accountNo, String accountBookNo, String begdat, String enddat) throws BizException;

    WithdrawRespVo withdraw(WithdrawReqVo withdrawReqVo) throws BizException;

    JSONObject reconfileApply(String accountNo, String startDate, String endDate, String begidx);

    JSONObject getReconFileUrl(String accountNo, String taskId);

    String getAccountReconFileUrlSync(String accountNo, String tradeDate, String trxSeq);

    JSONObject payApplyBatch(String accountNo, Dict body) throws BizException;

    JSONObject queryPayBatchOrder(String batchNo, String accountNo);

    JSONObject payRecordDetailQuery(String accountNo, String reqnbr, String trxseq, String bthnbr);

    JSONObject pay2bQuery(String accountNo, String trxNo);

    JSONObject transferAccountBooks(String accountNo, String payerAccountBookNo, String payeeAccountBookNo, String amt, String remark, String trxNo) throws BizException;
}
