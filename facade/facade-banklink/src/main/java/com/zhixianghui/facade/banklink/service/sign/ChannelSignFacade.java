package com.zhixianghui.facade.banklink.service.sign;

import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.components.ComponentsResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDateVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.*;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 签约接口
 * @date 2021/1/14 17:20
 **/
public interface ChannelSignFacade {

    EsignResVo<CreateFileUploadUrlResVo> getUploadUrl(CreateFileUploadUrlVo createFileUploadUrlVo);

//    /**
//     * 流程延期接口
//     * @param delayFlowReqVo
//     * @return
//     */
//    EsignResVo delayFlow(DelayFlowReqVo delayFlowReqVo);

    /**
     * 撤回流程
     * @param revokeFlowVo
     * @return
     */
    EsignResVo revokeFlow(RevokeFlowVoV3 revokeFlowVo);

    /**
     * 个人账户创建
     *
     * @param signCreatePersonReqVo 个人账户创建reqVo
     * @return 个人账户创建结果
     */
//    EsignResVo<SignCreatePersonResDataVo> createPersonByThirdPartyUserId(SignCreatePersonReqVo signCreatePersonReqVo);

    /**
     * 机构账号创建
     *
     * @param signCreateOrganizationReqVo 机构账号创建reqVo
     * @return 机构账号创建结果
     */
//    EsignResVo<SignCreateOrganizationResDataVo> createOrganization(SignCreateOrganizationReqVo signCreateOrganizationReqVo);

    /**
     * 查询模板详情
     *
     * @param signTemplateReqVo 查询模板详情ReqVo
     * @return 查询模板详情结果
     */
    EsignResVo<SignTemplateResDataVoV3> getSignTemplate(SignTemplateReqVo signTemplateReqVo);

    /**
     * 设置静默签署
     *
     * @param signAuthReqVo 设置静默签署ReqVo
     * @return 设置静默签署结果
     */
//    EsignResVo<Boolean> signAuth(SignAuthReqVo signAuthReqVo);

    /**
     * 通过模板创建文件
     *
     * @param createFileByTemplateReqVo 通过模板创建文件ReqVo
     * @return 通过模板创建文件结果
     */
    EsignResVo<CreateFileByTemplateResDataVoV3> createFileByTemplate(CreateFileByTemplateReqVoV3 createFileByTemplateReqVo);


    /**
     * （精简版）基于文件发起签署
     *
     * @param createByFileReqVo （精简版）基于文件发起签署
     * @return （精简版）基于文件发起签署结果
     */
    EsignResVo<CreateByFileResDateVo> createByFile(CreateByFileReqVo createByFileReqVo);


    /**
     * 一步发起签署
     *
     * @param createFlowOneStepReqVo 一步发起签署ReqVo
     * @return 一步发起签署结果
     */
//    EsignResVo<CreateFlowOneStepResDataVo> createFlowOneStep(CreateFlowOneStepReqVo createFlowOneStepReqVo);

    /**
     * 获取签署地址
     *
     * @param executeUrlReqVo 获取签署地址ReqVo
     * @return 获取签署地址结果
     */
//    EsignResVo<ExecuteUrlResDataVo> getExecuteUrl(ExecuteUrlReqVo executeUrlReqVo);


    EsignResVo<ExecuteUrlResDataVo> getExecuteUrlV3(ExecuteUrlReqVoV3 executeUrlReqVo);

    /**
     * 签署流程查询
     *
     * @param signFlowReqVo 签署流程查询ReqVo
     * @return 签署流程查询结果
     */
//    EsignResVo<SignFlowResDataVo> getSignFlow(SignFlowReqVo signFlowReqVo);

    /**
     * 流程文档下载
     *
     * @param documentDownloadReqVo 流程文档下载ReqVo
     * @return 流程文档下载结果
     */
    EsignResVo<DocumentDownloadResDataVo> getDocumentDownloadUrl(DocumentDownloadReqVo documentDownloadReqVo);

    /**
     * 流程文档下载
     *
     * @param documentDownloadReqVo 流程文档下载ReqVo
     * @return 流程文档下载结果
     */
    EsignResVo<PreviewFileDownloadResDataVo> getPreviewDownloadUrl(PreviewFileDownloadReqVo documentDownloadReqVo);

    /**
     * 流程文档上传到fastdfs
     *
     * @param logFlag  日志标识
     * @param fileName 文件名
     * @param fileUrl  远程url
     * @return fastDfs地址
     * @throws IOException 文件IO异常
     */
    String downLoadFile(String logFlag, String fileName, String fileUrl) throws IOException;

//    EsignResVo<UpdatePersonInfoResDataVo> updatePersonInfo(UpdatePersonInfoReqVo updatePersonInfoReqVo);

    /**
     * 创建签约模板
     *
     * @return 文件上传地址
     */
    EsignResVo<CreateSignTemplateResV3DataVo> createSignTemplate(CreateSignTemplateReqVo createSignTemplateReqVo);

    EsignResVo<CreateTemplateResV3DataVo> createTemplate(CreateTemplateReqVo createTemplateReqVo);

    EsignResVo<EditTemplateResV3DataVo> editTemplate(EditTemplateReqVo createTemplateReqVo);

    /**
     * 文件上传
     *
     * @param uploadFileReqVo 请求参数
     * @param uploadUrl       文件上传地址
     * @param buffer
     */
    boolean uploadFile(UploadFileReqVo uploadFileReqVo, String uploadUrl, byte[] buffer);

    /**
     * 组件上传
     *
     * @param templateComponentsReqVo 组件
     * @return 组件id列表
     */
//    EsignResVo<List<String>> addTemplate(TemplateComponentsReqVo templateComponentsReqVo);



    /**
     * 查询模板是否可用
     *
     * @param reqVo      请求头
     * @param
     */
//    EsignResVo<TemplateStatusResDataVo> existTemplate(TemplateStatus reqVo);

    /**
     * 获取个人签章
     *
     * @param reqVo 请求头
     * @return
     */
    EsignResVo<GetPersonalSignatureResDataVo> personalSignature(PersonalSignatureReqVo reqVo);


//    List<KeywordPosVo> getPos(CheckFilePosVo checkFilePosVo);

//    AccountCreateResponse createPersonalAccount(SignCreatePersonReqVo signCreatePersonReqVo, boolean autoSign) throws BizException;

//    List<StructComponent> getTemplateInfo(String templateId);

    EsignResVo<TemplatePageResDataResVo> getTemplatePage(EsignPageVo esignPageVo);

    EsignResVo<OrgIdentityInfoResDataVo> getOrgIdentityInfoV3(OrgIdentityInfoResDateVo orgIdentityInfoResDateVo);

    EsignResVo<ComponentsResDataVo>  customComponentsList(EsignPageVo esignPageVo);

    EsignResVo<?> delComponentsList(DelComponentReqVo delComponentReqVo);

    EsignResVo<?> createComponentsList(CreateComponentReqVo reqVo);

    EsignResVo<PsnIdentityInfoResDataVo> getPsnIdentityInfoV3(PsnIdentityInfoReqVo setPsnAccount);

    EsignResVo<GetPsnSealListResDataVo> getPsnSealList(PsnSealListReqVo reqVo);
}
