package com.zhixianghui.facade.banklink.vo.ocr;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class BusinessLicenseOctVo implements Serializable {
    /**
     * 经营范围
     */
    private String bussinsesScope;
    /**
     * 组成形式
     */
    private String orgType;
    /**
     * 法人
     */
    private String legalPerson;
    /**
     * 证件编号
     */
    private String licenseNo;
    /**
     * 注册资本
     */
    private String registCapital;
    /**
     * 单位名称
     */
    private String companyName;
    /**
     * 有效期
     */
    private String validScope;
    /**
     * 社会信用代码
     */
    private String socialCreditCode;
    /**
     * 实收资本
     */
    private String paidCapital;
    /**
     * 核准日期
     */
    private String approvalDate;
    /**
     * 成立日期
     */
    private String foundDate;
    /**
     * 税务登记号
     */
    private String taxRegistNo;
    /**
     * 地址
     */
    private String address;
    /**
     * 登记机关
     */
    private String registOrganization;
    /**
     * 类型
     */
    private String category;
}
