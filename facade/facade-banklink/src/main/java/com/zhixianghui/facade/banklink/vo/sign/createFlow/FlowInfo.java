package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:14
 **/
@Data
@Accessors(chain = true)
public class FlowInfo implements Serializable {

    private static final long serialVersionUID = -3106811826338849064L;

    /**
     * 是否自动归档，默认false。
     */
    private boolean autoArchive;

    /**
     * 是否自动开启，默认false。
     */
    private boolean autoInitiate;

    /**
     * 文件主题
     *
     * 注：名称不支持以下9个字符：/ \ : * " < > | ？
     */
    private String businessScene;

    /**
     * 文件到期前，提前多少小时回调提醒续签，小时（时间区间：1小时——15天），默认不提醒
     */
    private Integer contractRemind;

    /**
     * 文件有效截止日期,毫秒，默认不失效
     */
    private Integer contractValidity;

    /**
     * 任务配置信息
     */
    private FlowConfigInfo flowConfigInfo;

    /**
     * 发起方账户id
     */
    private String initiatorAccountId;

    /**
     * 发起方主体id
     */
    private String initiatorAuthorizedAccountId;

    /**
     *
     */
    private String remark;

    /**
     * 签署截止时间
     */
    private Long signValidity;

}
