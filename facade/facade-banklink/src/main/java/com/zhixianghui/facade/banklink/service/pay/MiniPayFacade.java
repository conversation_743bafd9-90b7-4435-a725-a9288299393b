package com.zhixianghui.facade.banklink.service.pay;

import com.zhixianghui.facade.banklink.vo.pay.MiniAppPayVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;

/**
 * <AUTHOR>
 * @ClassName MiniPayFacade
 * @Description TODO
 * @Date 2022/12/28 9:59
 */
public interface MiniPayFacade {

    WxResVo jsapiPay(MiniAppPayVo miniAppPayVo);

    WxResVo closeOrder(String orderNo);

    WxResVo jsapiQuery(String orderNo);

    WxResVo jsapiRefundQuery(String refundOrderNo);

    WxResVo refund(String originOrderNo,String refundOrderNo,Long amount,String refundReason);
}
