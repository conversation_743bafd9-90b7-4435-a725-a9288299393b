package com.zhixianghui.facade.banklink.service.alipay;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawReqVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawRespVo;
import com.zhixianghui.facade.banklink.vo.report.AlipayJobPayslipSyncReqVo;
import com.zhixianghui.facade.banklink.vo.report.AlipayJobPayslipSyncRspVo;

import java.util.Map;


public interface AlipayFacade {

    AlipayJobPayslipSyncRspVo jobPayslipSync(AlipayJobPayslipSyncReqVo reqVo);

    /**
     * 回调验签接口
     * @param params
     * @return
     */
    boolean verifyNotify(Map<String,String> params);

    String recharge(String agreementNo, String accountBookId, String aliUserId, String amt, String outBizNo,String expireTime);

    /**
     * 协议签约
     * @param extAgreementNo
     * @return
     */
    String sign(String extAgreementNo,Integer channelType);

    /**
     * 创建记账本
     * @param agreementNo
     * @param merchantNo
     * @return
     */
    String accountBookCreate(String agreementNo,String merchantNo);

    /**
     * 取消签约
     * @param extAgreementNo
     * @param agreementNo
     * @return
     */
    String unsign(String extAgreementNo,String agreementNo);

    /**
     * 查询签约情况
     * @param extAgreementNo
     * @param agreementNo
     * @return
     */
    String agreementQuery(String extAgreementNo,String agreementNo);

	String transPayQuery(String outBizNo);

	/**
     * 查询记账本情况
     * @param agreementNo
     * @param accountBookId
     * @return
     */
    String accountBookQuery(String agreementNo,String accountBookId);

    void uniTransRefund(PayReqVo reqVo);

    String transCommonQuery(String outBizNo,String bizScene,String productCode);

    String transCommonQueryByOrderId(String orderId) throws Exception, BizException;

    /**
     * 鉴权请求
     * @param reqVo
     * @return
     */
    AuthRespVo auth(AuthReqVo reqVo);

    WithdrawRespVo withdraw(WithdrawReqVo withdrawReqVo) throws BizException;

    String billApply(String key,String agreementNo) throws Exception, BizException;

    String billQuery(String fileId, String agreementNo) throws Exception, BizException;

    void subscribe(String agreementNo, String accountBookId) throws Exception;
}
