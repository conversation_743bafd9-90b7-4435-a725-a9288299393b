package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import com.alibaba.fastjson.annotation.JSONField;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 获取签署地址Req
 * @date 2021-01-13 10:06
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ExecuteUrlReqVoV3 extends EsignBaseReqVo {


    /**
     * 签署流程id
     */
    private String signFlowId;

    /**
     * 是否需要登录打开链接（默认值 false）
     * true - 需登录打开链接，false - 免登录
     */
    private Boolean needLogin;

    /**
     * 链接类型（默认值 2）
     * 1 - 预览链接（仅限查看，不能签署）， 2 - 签署链接
     */
    private Integer urlType;


    /**
     *
     * 个人签署方（机构签署传经办人信息）
     * 当获取签署链接场景，需传入当前流程流转到的签署操作人信息。
     * psnAccount与psnId二选一传入（必须与发起签署时的账号保持一致）
     * 【注】大多数场景必传字段，如不传该参数，后台默认自动带入appId对应主体信息，获取平台方预览合同地址
      */
    private Operator operator;


    /**
     *机构签署方
     * 一个流程中存在经办人代多个机构签署时，通过此参数分别获取对应机构的签署链接；
     * orgId与orgName二选一传入（必须与发起签署时账号保持一致）
     */
    private Organization organization;


    /**
     * 重定向配置项
     */
    private RedirectConfig  redirectConfig;

    /**
     *
     * 指定客户端类型，当urlType为2（签署链接）时生效
     * H5 - 移动端适配
     * PC - PC端适配
     * ALL - 自动适配移动端或PC端（默认值）
     * 【注】参数值均为大写的英文
     */
    private String clientType;

    /**
     * AppScheme，用于唤起App。
     * 示例值：esign://demo/signBack
     */
    private String appScheme;



    @Data
    @Accessors(chain = true)
    public static class Operator implements Serializable {
        /**
         *个人账号标识（手机号或邮箱）用于登录e签宝官网的凭证
         * 【注】个人用户签署时，该参数为必传项。为了保证签署人准确，建议配合姓名信息传入。
         */
        private String psnAccount;

        /**
         * 签署操作人账号ID（个人账号ID）
         */
        private String psnId;

    }

    @Data
    @Accessors(chain = true)
    public static class Organization implements Serializable {
        /**
         *  机构账号ID
         */
        private String orgId;

        /**
         * 机构名称
         */
        private String orgName;

    }


    @Data
    @Accessors(chain = true)
    public static class RedirectConfig implements Serializable {
        /**
         *  签署完成后跳转页面（需符合 https /http 协议地址）
         */
        private String redirectUrl;

        /**
         * 操作完成重定向跳转延迟时间，单位秒（可选值0、3，默认值为 3）
         * 传0时，签署完成直接跳转重定向地址；
         * 传3时，展示签署完成结果页，倒计时3秒后，自动跳转重定向地址。
         * 【注】当redirectUrl不传的情况下，该字段无需传入，签署完成不跳转
         */
        private String redirectDelayTime;

    }

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{signFlowId}", signFlowId);
    }
}
