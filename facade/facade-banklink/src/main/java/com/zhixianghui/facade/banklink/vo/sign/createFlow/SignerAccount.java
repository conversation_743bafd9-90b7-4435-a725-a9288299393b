package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:32
 **/
@Data
@Accessors(chain = true)
public class SignerAccount implements Serializable {

    private static final long serialVersionUID = -4548143435533965471L;

    /**
     * 签署操作人个人账号标识，即操作本次签署的个人
     *
     * 注：平台用户自动签署时，该参数需要传入签署主体账号id
     */
    private String signerAccountId;
    /**
     * 签约主体账号标识，即本次签署对应任务的归属方，默认是签署操作人个人
     *
     * 注：平台用户自动签署时，无需传入该参数
     */
    private String authorizedAccountId;

    /**
     * 通知方式
     */
    private String noticeType;
}
