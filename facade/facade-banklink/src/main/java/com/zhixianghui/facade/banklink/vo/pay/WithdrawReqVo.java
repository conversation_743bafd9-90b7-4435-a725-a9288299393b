package com.zhixianghui.facade.banklink.vo.pay;

import lombok.Data;

import java.io.Serializable;

@Data
public class WithdrawReqVo implements Serializable {
    private static final long serialVersionUID = 5917692566245148210L;

    /**
     * 请求渠道订单号
     */
    private String bankOrderNo;
    /**
     * 收款人姓名
     */
    private String receiveName;
    /**
     * 收款人账号
     */
    private String receiveAccountNo;
    /**
     * 实发金额
     */
    private String receiveAmount;

    /**
     * 打款备注
     * 部分银行支持将该值显示给收款人
     */
    private String remitRemark;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 付款方记账本
     */
    private String payerAccountBookId;

    /**
     * 付款方协议号
     */
    private String payerAgreementNo;

    /**
     * 真实付款方名称-一般为用工企业名称
     */
    private String realPayerName;

    /**
     * 支付宝场景码
     */
    private String bizScene;

    /**
     * 支付宝产品码
     */
    private String productCode;

    /**
     * 机构名
     */
    private String organizationName;

    /**
     * 支行号
     */
    private String bankChannelNo;

    private String payeeBankName;
}
