package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-08 14:48
 **/
@Data
public class SignTemplateResDataVo  implements Serializable {

    private static final long serialVersionUID = -1082801494215524207L;

    private String templateId;
    private String templateName;
    private String downloadUrl;
    private long fileSize;
    private long createTime;
    private long updateTime;
    private List<StructComponent> structComponents;
}
