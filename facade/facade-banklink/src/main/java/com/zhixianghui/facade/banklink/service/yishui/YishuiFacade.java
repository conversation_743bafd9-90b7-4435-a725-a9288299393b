package com.zhixianghui.facade.banklink.service.yishui;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.facade.banklink.vo.yishui.*;
import com.zhixianghui.facade.banklink.vo.yishui.req.*;

import java.util.Map;

public interface YishuiFacade {
    String getYishuiToken(String username, String password, String enterprise_sn);

    String getAgentToken();

    String getUploadUrl();

    YiResponse<Map<String, String>> saveEnterpriseDetail(SaveEnterpriseDetailVo saveVo);

    YiResponse saveEnterpriseDraftRate(SaveEnterpriseDraftRate draftRate);

    YiResponse<Map<String, Object>> getEnterpriseDetail(String enterpriseId);

    YiResponse<Map<String, Object>> getEnterpriseDetailByName(String enterpriseName);

    YiResponse<YishuiAddEmpVo> addEmployee(RequestVo<AddEmpVo> requestVo, int channelType);

    YiResponse<YishuiContractListVo> contractList(RequestVo<ContractListQueryVo> requestVo);

    YiResponse<YishuiContractInfoVo> contractInfo(RequestVo<String> requestVo);

    YiResponse<YishuiFastIssuing> fastIssuing(RequestVo<FastIssuingVo> requestVo);

    YiResponse changeOrderStatus(RequestVo<ChangeOrderReqVo> requestVo);

    YiResponse<Map<String, Object>> findOrderFromRequestNo(RequestVo<String> requestVo);

    YiResponse contractSave(RequestVo<ContractSaveVo> requestVo);

    YiResponse<String> addBank(RequestVo<AddBankVo> requestVo);

    void orderBatchSubmit(String outBatchNo);

    JSONObject getFundInfo(String employerNo);
}
