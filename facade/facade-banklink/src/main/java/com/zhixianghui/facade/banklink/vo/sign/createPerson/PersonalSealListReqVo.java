package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonalSealListReqVo extends EsignBaseReqVo {
    private static final long serialVersionUID = -6826271590824128101L;
    private String accountId;
    private Integer offset = 1;
    private Integer size = 100;

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{accountId}", this.accountId)+"/offset="+this.offset+"&size="+this.size;
    }
}
