package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName DelayFlowReqVo
 * @Description TODO
 * @Date 2022/8/19 17:08
 */
@Data
public class DelayFlowReqVo extends EsignBaseReqVo implements Serializable {

    /**
     * 流程id
     */
    private String signFlowId;

    /**
     * 超时时间
     */
    private Long signFlowExpireTime;

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{signFlowId}", signFlowId);
    }
}
