package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/8/24 17:46
 */
@Data
public class PsnIdentityInfoResDataVo implements Serializable {

     private Boolean authorizeUserInfo;
     private Integer realnameStatus;
     private String psnId;
     private PsnAccount psnAccount;
     private PsnInfo psnInfo;


     @Data
     public static class PsnAccount implements Serializable {
          private String accountMobile;
          private String accountEmail;

     }

     @Data
     public static class PsnInfo implements Serializable {
          private String psnName;
          private String psnNationality;
          private String psnIDCardNum;
          private String psnIDCardType;
          private String bankCardNum;
          private String psnMobile;

     }
}
