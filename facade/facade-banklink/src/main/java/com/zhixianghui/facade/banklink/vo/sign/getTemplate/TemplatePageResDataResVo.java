package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TemplateResDataResVo
 * @Description TODO
 * @Date 2022/8/15 11:36
 */
@Data
@Accessors(chain = true)
public class TemplatePageResDataResVo implements Serializable {

    private Integer total;

    private List<FlowTemplateInfoResDataVo> docTemplates;
}
