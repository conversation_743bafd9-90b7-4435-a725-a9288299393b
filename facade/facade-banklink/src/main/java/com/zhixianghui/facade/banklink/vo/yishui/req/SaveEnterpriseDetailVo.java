package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class SaveEnterpriseDetailVo implements Serializable {
    private static final long serialVersionUID = -3834150774121601519L;

    /**
     * 企业名称
     */
    private String enterprise_name;
    /**
     *企业的社会信用代码
     */
    private String enterprise_code;
    /**
     *企业法人姓名
     */
    private String legal_person;
    /**
     *法人身份证号码
     */
    private String certificate;
    /**
     *开票类型 ２增值税普通发票　３增值税专用发票
     */
    private String invoice_type;
    /**
     *企业联系人
     */
    private String contact_name;

    /**
     *	联系人手机号码
     */
    private String mobile;

    /**
     *省份ID 详见地区接口
     */
    private String province;

    /**
     *	市区ID 详见地区接口
     */
    private String city;

    /**
     *区域ID 详见地区接口
     */
    private String district;

    /**
     *默认0
     */
    private String client_manager_id;

    /**
     *您的代理ID
     */
    private String agent_id;

    /**
     *纳税人性质 1一般纳税人 2小规模纳税人
     */
    private String taxpayer_type;

    /**
     *此处默认为空
     */
    private String checked_mobile;

    /**
     *公司经营地址
     */
    private String address;

    /**
     *营业执照照片HTTP访问地址
     */
    private String business_licence_img;

    /**
     *企业法人身份证照片HTTP访问地址
     */
    private String certificate_img;

    /**
     *默认0
     */
    private String resource_manager_id="0";

    /**
     *默认0
     */
    private String group_id="0";

    /**
     *企业API账号名称 此处在系统必须唯一 不唯一接口会返回相应错误提示
     */
    private String enterprise_user_name;

    /**
     *企业ID 新增默认为0 修改为相应企业的ID
     */
    private String enterprise_id;

    /**
     *开户行
     */
    private String bank_name;


    /**
     *开户行账号
     */
    private String bank_sn;

}
