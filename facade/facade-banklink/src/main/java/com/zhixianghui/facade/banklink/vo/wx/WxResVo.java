package com.zhixianghui.facade.banklink.vo.wx;

import com.zhixianghui.common.statics.enums.wx.WxCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName WxResVo
 * @Description TODO
 * @Date 2021/12/3 11:56
 */
@Data
@AllArgsConstructor
public class WxResVo implements Serializable {

    //wx支付状态码
    private Integer code;

    //响应JSON
    //jackJson
    private String responBody;

    public boolean isSuccess() {
        return checkCode(WxCodeEnum.SUCCESS.getCode());
    }

//    public boolean isNeedRetryFail() {
//        return checkCode(WxCodeEnum.SYSTEM_ERROR.getCode())
//                || checkCode(WxCodeEnum.NOT_ENOUGH.getCode())
//                || checkCode(WxCodeEnum.FREQUENCY_LIMITED.getCode());
//    }

    // 系统错误 【请勿更换商家转账批次单号，请使用相同参数再次调用API。否则可能造成资金损失】
    public boolean isSystemError() {
        return checkCode(WxCodeEnum.SYSTEM_ERROR.getCode());
    }

    // 请求频繁
    public boolean isFrequently() {
        return checkCode(WxCodeEnum.FREQUENCY_LIMITED.getCode());
    }

    public boolean isNotFound() {
        return checkCode(WxCodeEnum.NOT_FOUND.getCode());
    }

    private boolean checkCode(int code) {
        return this.code == code;
    }
}
