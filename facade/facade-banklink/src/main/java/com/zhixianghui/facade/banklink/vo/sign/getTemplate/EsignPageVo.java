package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName EsignPageVo
 * @Description TODO
 * @Date 2022/8/12 16:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EsignPageVo extends EsignBaseReqVo implements Serializable {

    private Integer pageNum = 1;

    /**
     * 每页显示的数量，最大值：20（默认值 20）
     */
    private Integer pageSize = 20;

    @Override
    public String buildFullUrl(String url) {
        return url + "?pageNum=" + pageNum + "&pageSize=" + pageSize;
    }
}
