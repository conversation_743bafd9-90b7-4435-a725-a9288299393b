package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-11 16:37
 **/
@Data
@Accessors(chain = true)
public class PosBean implements Serializable {

    private static final long serialVersionUID = -6522576179136542133L;

    /**
     * 页码信息，当签署区signType为2时, 页码可以'-'分割指定页码范围, 其他情况只能是数字
     */
    private String posPage;
    /**
     * x坐标
     */
    private float posX;
    /**
     * y坐标
     */
    private float posY;
}
