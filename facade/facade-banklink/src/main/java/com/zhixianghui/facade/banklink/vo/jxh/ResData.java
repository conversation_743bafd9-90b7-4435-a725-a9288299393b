package com.zhixianghui.facade.banklink.vo.jxh;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ResData implements Serializable {

    private int status;
    private String errorCode;
    // 订单回查时，异常信息为该字段
    private String errorDesc;
    // 异步回调时，异常信息为该字段
    private String errorCodeDesc;
    private String userNo;
    private String merchantOrderNo;
    private String platformSerialNo;
    private String receiverAccountNoEnc;
    private String receiverNameEnc;
    private int paidAmount;
    private BigDecimal fee;
    private String hmac;

    public ResStatus getStatus() {
        ResStatus resStatus = null;
        switch (this.status) {
            case 205:
                resStatus = ResStatus.SUCCESS;
                break;
            case 201:
            case 202:
            case 203:
            case 210:
            case 211:
            case 212:
            case 213:
                resStatus = ResStatus.PROCESSING;
                break;
            case 214:
                resStatus = ResStatus.NOT_FOND;
                break;
            case 204:
                resStatus = ResStatus.FAIL;
                break;
            default:
                resStatus = ResStatus.PROCESSING;
        }
        return resStatus;
    }
}