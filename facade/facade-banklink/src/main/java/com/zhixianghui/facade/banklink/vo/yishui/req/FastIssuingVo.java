package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class FastIssuingVo implements Serializable {

    private static final long serialVersionUID = -7276180031618845719L;
    /**
     * 回调通知地址
     */
    private String notify_url;
    /**
     * 第三方批次编号（订单创建结果通知会带过来）
     */
    private String trade_number;
    /**
     * 付款信息 多维数组
     */
    private List<IssuingData> issuing_data;

}
