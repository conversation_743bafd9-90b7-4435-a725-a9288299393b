package com.zhixianghui.facade.banklink.service.account;

import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.banklink.vo.account.SubMchNoQueryDto;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 查询余额
 * @date 2020-11-02 10:31
 **/
public interface AccountQueryFacade {
    /**
    * 去通道获取余额
    * @param amountQueryDto 信息
    * @return 余额
    */
    String getAmount(AmountQueryDto amountQueryDto);

    Map<String, String> getMainstayAmount(MainstayAmountQueryDto mainstayAmountQueryDto);

    /**
     * 去通道获取子商户编号
     * @param subMchNoQueryDto 信息
     * @return 子商户编号
     */
    String getSubMchNo(SubMchNoQueryDto subMchNoQueryDto);

}
