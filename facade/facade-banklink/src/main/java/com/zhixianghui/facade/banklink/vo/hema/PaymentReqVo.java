package com.zhixianghui.facade.banklink.vo.hema;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName PaymentReqVo
 * @Description TODO
 * @Date 2023/10/7 10:38
 */
@Data
public class PaymentReqVo implements Serializable {

    /**
     * 商户订单号
     */
    private String thirdOrderId;

    /**
     * 第三方业务订单号id
     */
    private String thirdBizOrderId;

    /**
     * 收款人姓名
     */
    private String payeeName;

    /**
     * 收款人身份证号码
     */
    private String payeeIdCard;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 收款账户
     */
    private String payeeAccount;

    /**
     * 收款金额
     */
    private BigDecimal amount;

    /**
     * 微信appid
     */
    private String wxAppId;

    /**
     * 代征主体id
     */
    private Integer mainstayId = 9;

    /**
     * 发票编码
     */
    private String invoiceCode = "XDFW,TGFWF";

    /**
     * 检测失败是否需要回调，固定为0
     */
    private String extNeedCallBack = "0";


}
