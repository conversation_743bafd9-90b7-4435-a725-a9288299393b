package com.zhixianghui.facade.banklink.vo.notify;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.zhixianghui.common.statics.annotations.NotSign;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-21 15:56
 **/
@Data
@JSONType(naming= PropertyNamingStrategy.SnakeCase)
public class MerchantNotifyParam implements Serializable {
    @NotSign
    private static final long serialVersionUID = -1675284395311704369L;

    private NotifyContent notifyContent;

    /**
     * 通知地址 必传
     */
    @NotSign
    private String notifyUrl;

}
