package com.zhixianghui.facade.banklink.vo.yishui.req;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

@Data
@Accessors(chain = true)
public class OrderListVo implements Serializable {

    private static final long serialVersionUID = -2463092904831768976L;
    /**
     * 批次编号
     */
    private String enterprise_order_sn;
    /**
     * 批次类型 空查全部 1大额批次 0小额批次
     */
    private String states;
    /**
     * 批次状态 空查全部 0：未审核、1：企业内部审核通过、2：服务商审核通过、3：已部分打款、4：已全部打款、5：审核不通过、6：全部打款失败
     */
    private String status;
    /**
     * 付款原因
     */
    private String reason;
    /**
     * 提交时间 开始时间 时间戳精确到秒
     */
    private String check_time_start;
    /**
     * 提交时间 结束时间 时间戳精确到秒
     */
    private String check_time_end;
    private Pagination pagination;

}
