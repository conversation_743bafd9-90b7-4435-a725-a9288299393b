package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonalAccountQueryReqVo extends EsignBaseReqVo {
    private static final long serialVersionUID = -1139052586175452215L;

    private String thirdPartyUserId;

    @Override
    public String buildFullUrl(String url) {
        return url+"?thirdPartyUserId="+this.thirdPartyUserId;
    }
}
