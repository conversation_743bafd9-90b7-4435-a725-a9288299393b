package com.zhixianghui.facade.banklink.vo.account;

import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-21 16:23
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AmountQueryDto extends BaseKeyPairReqVo {

    private static final long serialVersionUID = -1387460123263193500L;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 子商户编号
     */
    private String subMerchantNo;

    /**
     * 协议号(支付宝专用)
     */
    private String agreementNo;

}
