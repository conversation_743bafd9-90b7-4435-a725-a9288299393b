package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/8/24 17:46
 */
@Data
@Accessors(chain = true)
public class PsnAuthUrlResDataVo implements Serializable {

     /**
      * 流程id
      */
     private String authFlowId;

     /**
      * 长连接
      */
     private String authUrl;

     /**
      * 短连接
      */
     private String authShortUrl;


}
