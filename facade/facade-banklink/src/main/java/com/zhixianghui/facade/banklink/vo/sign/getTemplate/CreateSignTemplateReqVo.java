package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2021/6/1 14:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateSignTemplateReqVo extends EsignBaseReqVo {

    protected String contentMd5;
    protected String contentType;
    private String fileName;
    private Boolean convertToPDF;
    private Long fileSize;

    public CreateSignTemplateReqVo() {

    }
    @Override
    public String buildFullUrl(String url) {
        return url;
    }


}
