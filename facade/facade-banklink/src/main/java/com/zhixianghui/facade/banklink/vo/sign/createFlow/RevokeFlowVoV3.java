package com.zhixianghui.facade.banklink.vo.sign.createFlow;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
public class RevokeFlowVoV3 extends EsignBaseReqVo implements Serializable {
    /**
     *  流程id
     */
    private String signFlowId;

    @Override
    public String buildFullUrl(String url) {
        return url.replace("{signFlowId}", signFlowId);
    }
}
