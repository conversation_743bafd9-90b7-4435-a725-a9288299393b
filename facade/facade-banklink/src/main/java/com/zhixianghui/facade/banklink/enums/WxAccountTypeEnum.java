package com.zhixianghui.facade.banklink.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName WxAccountTypeEnum
 * @Description TODO
 * @Date 2022/12/26 10:32
 */
@Getter
@AllArgsConstructor
public enum WxAccountTypeEnum {

    OPERATION(100,"OPERATION","运营账户"),

    BASIC(101,"BASIC","基本账户"),

    FEES(102,"FEES","手续费账户");

    private int value;

    private String code;

    private String desc;
}
