package com.zhixianghui.facade.banklink.vo.sign.createPerson;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024年4月9日11:25:45
 */
@Data
@Accessors(chain = true)
public class PsnIdentityInfoReqVo extends EsignBaseReqVo implements Serializable {
    /**
     *  个人账号ID
     */
    private String psnId;
    /**
     *  个人账号标识（手机号或邮箱）
     */
    private String psnAccount;
    /**
     *  个人用户的证件号
     */
    private String psnIDCardNum;
    /**
     * 个人证件号类型 （传psnIDCardNum时，证件类型为必传项）
     * CRED_PSN_CH_IDCARD - 中国大陆居民身份证
     * CRED_PSN_CH_HONGKONG - 香港来往大陆通行证
     * CRED_PSN_CH_MACAO - 澳门来往大陆通行证
     * CRED_PSN_CH_TWCARD - 台湾来往大陆通行证
     * CRED_PSN_PASSPORT - 护照
     */
    private String psnIDCardType;

    @Override
    public String buildFullUrl(String url) {
        Map<String, Object> map = BeanUtil.beanToMap(this,false,true);
        String decode = URLUtil.decode(HttpUtil.toParams(map));
        return url+"?"+decode;
    }
}
