package com.zhixianghui.facade.banklink.vo.sign.getTemplate;

import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName CreateFileUploadUrl
 * @Description TODO
 * @Date 2022/8/24 17:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateFileUploadUrlVo extends EsignBaseReqVo {

    /**
     * md5链接
     */
    private String contentMd5;

    /**
     * MIME类型
     */
    private String contentType;

    /**
     * 是否转换pdf
     */
    private Boolean convertToPDF;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;


    @Override
    public String buildFullUrl(String url) {
        return url;
    }
}
