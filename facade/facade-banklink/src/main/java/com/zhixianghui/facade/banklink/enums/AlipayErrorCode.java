package com.zhixianghui.facade.banklink.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AlipayErrorCode {

    /**
     * 成功
     */
    SUCCESS(10000, "成功"),
    /**
     * 服务不可用
     */
    UNKOWN_ERROR(20000,"服务不可用"),

    /**
     * 查询：订单不存在
     * 发放：业务异常
     */
    BIZ_ERROR(40004,"订单不存在或业务异常"),


    MAINSTAY_BALANCE_NOT_ENOUGH(40005,"供应商余额不足"),
    ;
    private int code;
    private String message;

}
