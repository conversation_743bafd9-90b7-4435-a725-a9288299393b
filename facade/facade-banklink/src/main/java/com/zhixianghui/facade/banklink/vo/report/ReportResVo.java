package com.zhixianghui.facade.banklink.vo.report;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-30 16:40
 **/
@Data
public class ReportResVo implements Serializable {

    private static final long serialVersionUID = 2713203826351208085L;

    /**
     * 报备响应状态
     * @see com.zhixianghui.common.statics.enums.bankLink.report.ApiReportStatusEnum
     */
    private Integer apiReportStatus;

    /**
     * 业务码
     */
    private String bizCode;

    /**
     * 业务信息
     */
    private String bizMsg;

    /**
     * 报备返回子商户编号
     */
    private String mchNo;

    /***
     * 返回的data数据
     */
    private String data;

}
