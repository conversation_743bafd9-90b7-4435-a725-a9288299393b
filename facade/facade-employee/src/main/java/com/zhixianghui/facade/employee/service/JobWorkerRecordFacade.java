package com.zhixianghui.facade.employee.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.employee.dto.JobWorkerApprovedDto;
import com.zhixianghui.facade.employee.dto.JobWorkerWebQueryDto;
import com.zhixianghui.facade.employee.dto.WorkerRecordQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.vo.JobWorkerDetailVo;
import com.zhixianghui.facade.employee.vo.JobWorkerUpdateVo;

import java.util.List;
import java.util.Map;

public interface JobWorkerRecordFacade {
    Page<JobWorkerRecord> workerRecordPage(Page<JobWorkerRecord> page, WorkerRecordQueryDto workerRecordQueryDto) throws BizException;

    List<JobWorkerRecord> listBy(Map<String,Object> map);

    JobWorkerRecord findById(Long id) throws BizException;

    void update(JobWorkerRecord jobWorkerRecord) throws BizException;


    int batchUpdateJobWorkerStatus(List<Long> ids, Integer jobWorkerStatus);

    void approved(JobWorkerApprovedDto jobWorkerApprovedDto);

    JobWorkerDetailVo selectJobAndWorkerDetailById(Long id);

    void completeJobWorker(List<Long> workerIds);

    int batchUpdateStatus(List<Long> ids, JobWorkerUpdateVo jobWorkerUpdateVo);

    void batchApprovedSuccess(List<Long> ids);

    void submit(JobWorkerWebQueryDto workerWebQueryDto);

    void delete(Long id);

    JobWorkerRecord getByJobIdAndIdCard(String jobId,String idCard);

    Integer countEmployedWorker(String jobId);
	JobWorkerRecord getByJobIdAndPhoneNo(String jobId,String phoneNo);

    JobWorkerRecord addWorkerApi(Job job, String name, String idCardNo, String phoneNo, String mchNo) throws BizException;}
