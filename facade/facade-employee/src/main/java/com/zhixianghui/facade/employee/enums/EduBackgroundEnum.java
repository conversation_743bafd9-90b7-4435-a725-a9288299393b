package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月28日 17:01:00
 */
@Getter
@AllArgsConstructor
@EnumDesc(name="学历背景",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum EduBackgroundEnum implements BaseEnumInterFace {
    NO_LIMIT(-1, "不限"),
    JUNIO<PERSON>(100, "初中"),
    HIGH(200, "高中"),
    SECONDARY(300, "中专"),
    JUNIOR_COLLEGE(400, "大专"),
    UNDERGRADUATE(500, "本科"),
    MASTER(600, "硕士"),
    DOCTOR(700, "博士"),
    ;

    private final Integer code;
    private final String desc;
}