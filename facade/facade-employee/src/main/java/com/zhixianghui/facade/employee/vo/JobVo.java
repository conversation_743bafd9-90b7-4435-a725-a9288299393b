package com.zhixianghui.facade.employee.vo;

import com.baomidou.mybatisplus.annotation.Version;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月20日 10:24:00
 */
@Data
public class JobVo implements Serializable {

    private static final long serialVersionUID = -8688166301448002689L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    /**
     * 行业类型代码
     */
    private String industryCode;

    /**
     * 行业类型名称
     */
    private String industryName;

    /**
     * 任务编号
     */
    private String jobId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 商户名称
     */
    private String employerName;

    /**
     * 任务标签
     */
    private String jobTag;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobAvalDateTypeEnum
     * 任务有效日期类型
     */
    private Integer jobAvalDateType;

    /**
     * 任务开始日期
     */
    private LocalDate jobStartDate;

    /**
     * 任务结束日期
     */
    private LocalDate jobFinishDate;

    /**
     * 工作时段开始时间
     */
    private LocalTime jobTimeStart;

    /**
     * 工作时段结束时间
     */
    private LocalTime jobTimeEnd;

    /**
     * 任务省份
     */
    private Integer jobProvinceNo;

    /**
     * 任务省份名称
     */
    private String jobProvinceName;

    /**
     * 任务城市
     */
    private Integer jobCityNo;

    /**
     * 任务城市名称
     */
    private String jobCityName;

    private Integer jobAreaNo;

    /**
     * 任务城市名称
     */
    private String jobAreaName;

    /**
     * 详细地址
     */
    private String jobAddress;

    /**
     * 招收人数
     */
    private Integer workerNum;

    private Integer employedNum;

    /**
     * @see com.zhixianghui.facade.employee.enums.RewardTypeEnum
     * 报酬类型
     */
    private Integer rewardType;

    /**
     * @see com.zhixianghui.facade.employee.enums.PayTypeEnum
     * 计费类型 按日计费，按件计费
     */
    private Integer payType;

    /**
     * 金额
     */
    private BigDecimal rewardAmount;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyAmount;

    /**
     * @see com.zhixianghui.facade.employee.enums.WorkerGenderEnum
     * 性别
     */
    private Integer workerGender;

    /**
     * @see com.zhixianghui.facade.employee.enums.WorkerAgeLimitTypeEnum
     * 年龄限制类型
     */
    private Integer workerAgeLimitType;

    /**
     * 最低年龄
     */
    private Integer workerAgeLimitMin;

    /**
     * 最高年龄
     */
    private Integer workerAgeLimitMax;

    /**
     * 学历要求
     */
    private Integer eduBackground;

    /**
     * 专业技能要求
     */
    private String professionalSkill;

    /**
     * 任务描述
     */
    private String jobDescribe;

    /**
     * 交付标准
     */
    private String deliveryStandard;

    /**
     * @see com.zhixianghui.facade.employee.enums.AcceptModeEnum
     * 接单模式
     */
    private Integer acceptMode;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String createBy;

    private String updateBy;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobStatusEnum
     * 任务状态
     */
    private Integer jobStatus;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobVisibilityEnum
     * 任务可见性
     */
    private Integer scope;

    private String workCategoryCode;

    private String workCategoryName;

    private String mainstayNo;

    private String mainstayName;

    private List<JobWorkerRecord> jobWorkerRecords;

}
