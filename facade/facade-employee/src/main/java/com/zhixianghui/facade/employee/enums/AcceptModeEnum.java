package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="接单模式",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum AcceptModeEnum implements BaseEnumInterFace {

    MANUAL(100, "手动"),
    AUTOMATIC(200, "自动"),;

    private final Integer code;
    private final String desc;
}
