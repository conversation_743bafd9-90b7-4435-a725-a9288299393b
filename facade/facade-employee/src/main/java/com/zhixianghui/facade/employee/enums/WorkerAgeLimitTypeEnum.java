package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="年龄限制",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum WorkerAgeLimitTypeEnum implements BaseEnumInterFace {

    LIMIT(100, "限制"),
    NO_LIMIE(200, "不限"),;

    private final Integer code;
    private final String desc;
}
