package com.zhixianghui.facade.employee.dto;

import cn.hutool.core.bean.BeanUtil;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class WorkerRecordQueryDto implements Serializable {

    private static final long serialVersionUID = -6599191123466993087L;
    private Long id;
    private String workerName;
    private String workerPhone;
    private String jobName;
    private String jobId;
    private Integer jobStatus;
    private Integer settleStatus;
    private Integer deliveryStatus;
    private List<Integer> deliverStatusList;
    private List<Long> ids;
    private String mainstayNo;

    private String employerNo;
    public JobWorkerRecord toJobWorkerRecord() {
        JobWorkerRecord jobWorkerRecord = new JobWorkerRecord();
        BeanUtil.copyProperties(this,jobWorkerRecord);
        return jobWorkerRecord;
    }

    public Map<String, Object> toMap() {
        return BeanUtil.beanToMap(this);
    }
}
