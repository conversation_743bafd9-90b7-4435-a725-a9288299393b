package com.zhixianghui.facade.employee.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
    * 收票地址表
    */
@Data
@TableName(value = "tbl_invoice_post_address")
public class InvoicePostAddress implements Serializable {
    private static final long serialVersionUID = 3116474934807309702L;
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 收件人
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 手机号码
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 省份编号
     */
    @TableField(value = "province_no")
    private String provinceNo;

    /**
     * 省份名称
     */
    @TableField(value = "province_name")
    private String provinceName;

    /**
     * 城市编号
     */
    @TableField(value = "city_no")
    private String cityNo;

    /**
     * 城市名称
     */
    @TableField(value = "city_name")
    private String cityName;

    /**
     * 区域编号
     */
    @TableField(value = "area_no")
    private String areaNo;

    /**
     * 区域名称
     */
    @TableField(value = "area_name")
    private String areaName;

    /**
     * 详细地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 是否默认
     */
    @TableField(value = "is_default")
    private Boolean isDefault = Boolean.TRUE;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 身份证号码-用于区分小程序用户
     */
    @TableField(value = "id_card_no")
    private String idCardNo;


    public static final String COL_ID = "id";

    public static final String COL_NAME = "name";

    public static final String COL_MOBILE = "mobile";

    public static final String COL_PROVINCE_NO = "province_no";

    public static final String COL_PROVINCE_NAME = "province_name";

    public static final String COL_CITY_NO = "city_no";

    public static final String COL_CITY_NAME = "city_name";

    public static final String COL_AREA_NO = "area_no";

    public static final String COL_AREA_NAME = "area_name";

    public static final String COL_ADDRESS = "address";

    public static final String COL_IS_DEFAULT = "is_default";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_ID_CARD_NO = "id_card_no";
}
