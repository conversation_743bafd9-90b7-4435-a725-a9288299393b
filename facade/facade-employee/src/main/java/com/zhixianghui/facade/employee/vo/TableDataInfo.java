package com.zhixianghui.facade.employee.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月15日 16:15:00
 */
@Data
public class TableDataInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private long totalRecord;
    private List<?> rows;


    /**
     * 表格数据对象
     */
    public TableDataInfo() {
    }

    /**
     * 分页
     *
     * @param list  列表数据
     * @param total 总记录数
     */
    public TableDataInfo(List<?> list, int totalRecord) {
        this.rows = list;
        this.totalRecord = totalRecord;
    }

}