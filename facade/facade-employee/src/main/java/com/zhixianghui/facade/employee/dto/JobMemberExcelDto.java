package com.zhixianghui.facade.employee.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class JobMemberExcelDto implements Serializable {
    private static final long serialVersionUID = -4602456623685878741L;
    private String workerName;
    private String workerIdcard;
    private String workerPhone;

//    public JobMemberDto toJobMemberDto() {
//        JobMemberDto jobMemberDto = new JobMemberDto();
//        BeanUtil.copyProperties(this, jobMemberDto);
//        jobMemberDto.setGender(IdcardUtil.getGenderByIdCard(this.workerIdcard));
//        jobMemberDto.setAge(DateUtil.ageOfNow(IdcardUtil.getBirth(this.workerIdcard)));
//
//        return jobMemberDto;
//    }
}
