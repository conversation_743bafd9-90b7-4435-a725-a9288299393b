package com.zhixianghui.facade.employee.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
    * 任务表
    */
@Data
@TableName(value = "tbl_job")
public class Job implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "version")
    @Version
    private Integer version;

    /**
     * 行业类型代码
     */
    @TableField(value = "industry_code")
    private String industryCode;

    /**
     * 行业类型名称
     */
    @TableField(value = "industry_name")
    private String industryName;

    @TableField(value = "work_category_code")
    private String workCategoryCode;

    @TableField(value = "work_category_name")
    private String workCategoryName;

    /**
     * 任务编号
     */
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 任务名称
     */
    @TableField(value = "job_name")
    private String jobName;

    /**
     * 商户编号
     */
    @TableField(value = "employer_no")
    private String employerNo;

    /**
     * 商户名称
     */
    @TableField(value = "employer_name")
    private String employerName;

    /**
     * 任务标签
     */
    @TableField(value = "job_tag")
    private String jobTag;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobAvalDateTypeEnum
     * 任务有效日期类型
     */
    @TableField(value = "job_aval_date_type")
    private Integer jobAvalDateType;

    /**
     * 任务开始日期
     */
    @TableField(value = "job_start_date")
    private LocalDate jobStartDate;

    /**
     * 任务结束日期
     */
    @TableField(value = "job_finish_date")
    private LocalDate jobFinishDate;

    /**
     * 工作时段开始时间
     */
    @TableField(value = "job_time_start")
    private LocalTime jobTimeStart;

    /**
     * 工作时段结束时间
     */
    @TableField(value = "job_time_end")
    private LocalTime jobTimeEnd;

    /**
     * 任务省份
     */
    @TableField(value = "job_province_no")
    private Integer jobProvinceNo;

    /**
     * 任务省份名称
     */
    @TableField(value = "job_province_name")
    private String jobProvinceName;

    /**
     * 任务城市
     */
    @TableField(value = "job_city_no")
    private Integer jobCityNo;

    /**
     * 任务城市名称
     */
    @TableField(value = "job_city_name")
    private String jobCityName;


    @TableField(value = "job_area_no")
    private Integer jobAreaNo;

    /**
     * 任务城市名称
     */
    @TableField(value = "job_area_name")
    private String jobAreaName;


    /**
     * 详细地址
     */
    @TableField(value = "job_address")
    private String jobAddress;

    /**
     * 招收人数
     */
    @TableField(value = "worker_num")
    private Integer workerNum;

    @TableField(value =" employed_num")
    private Integer employedNum;

    /**
     * @see com.zhixianghui.facade.employee.enums.RewardTypeEnum
     * 报酬类型
     */
    @TableField(value = "reward_type")
    private Integer rewardType;

    /**
     * @see com.zhixianghui.facade.employee.enums.PayTypeEnum
     * 计费类型 按日计费，按件计费
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 金额
     */
    @TableField(value = "reward_amount")
    private BigDecimal rewardAmount;

    /**
     * 补贴金额
     */
    @TableField(value = "subsidy_amount")
    private BigDecimal subsidyAmount;

    /**
     * @see com.zhixianghui.facade.employee.enums.WorkerGenderEnum
     * 性别
     */
    @TableField(value = "worker_gender")
    private Integer workerGender;

    /**
     * @see com.zhixianghui.facade.employee.enums.WorkerAgeLimitTypeEnum
     * 年龄限制类型
     */
    @TableField(value = "worker_age_limit_type")
    private Integer workerAgeLimitType;

    /**
     * 最低年龄
     */
    @TableField(value = "worker_age_limit_min")
    private Integer workerAgeLimitMin;

    /**
     * 最高年龄
     */
    @TableField(value = "worker_age_limit_max")
    private Integer workerAgeLimitMax;

    /**
     * 学历要求
     */
    @TableField(value = "edu_background")
    private Integer eduBackground;

    /**
     * 专业技能要求
     */
    @TableField(value = "professional_skill")
    private String professionalSkill;

    /**
     * 任务描述
     */
    @TableField(value = "job_describe")
    private String jobDescribe;

    /**
     * 交付标准
     */
    @TableField(value = "delivery_standard")
    private String deliveryStandard;

    /**
     * @see com.zhixianghui.facade.employee.enums.AcceptModeEnum
     * 接单模式
     */
    @TableField(value = "accept_mode")
    private Integer acceptMode;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(value = "create_by")
    private String createBy;

    @TableField(value = "update_by")
    private String updateBy;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobStatusEnum
     * 任务状态
     */
    @TableField(value = "job_status")
    private Integer jobStatus;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobVisibilityEnum
     * 任务可见性
     */
    @TableField("scope")
    private Integer scope;

    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;

    @TableField("mainstay_no")
    private String mainstayNo;

    @TableField("mainstay_name")
    private String mainstayName;

    @TableField("app_code_url")
    private String appCodeUrl;

    @TableField("callback_url")
    private String callbackUrl;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_VERSION = "version";

    public static final String COL_INDUSTRY_CODE = "industry_code";

    public static final String COL_INDUSTRY_NAME = "industry_name";

    public static final String COL_JOB_ID = "job_id";

    public static final String COL_JOB_NAME = "job_name";

    public static final String COL_EMPLOYER_NO = "employer_no";

    public static final String COL_EMPLOYER_NAME = "employer_name";

    public static final String COL_JOB_TAG = "job_tag";

    public static final String COL_JOB_AVAL_DATE_TYPE = "job_aval_date_type";

    public static final String COL_JOB_START_DATE = "job_start_date";

    public static final String COL_JOB_FINISH_DATE = "job_finish_date";

    public static final String COL_JOB_TIME_START = "job_time_start";

    public static final String COL_JOB_TIME_END = "job_time_end";

    public static final String COL_JOB_PROVINCE_NO = "job_province_no";

    public static final String COL_JOB_PROVINCE_NAME = "job_province_name";

    public static final String COL_JOB_CITY_NO = "job_city_no";

    public static final String COL_JOB_CITY_NAME = "job_city_name";

    public static final String COL_JOB_AREA_NO = "job_area_no";

    public static final String COL_JOB_AREA_NAME = "job_area_name";

    public static final String COL_JOB_ADDRESS = "job_address";

    public static final String COL_WORKER_NUM = "worker_num";

    public static final String COL_REWARD_TYPE = "reward_type";

    public static final String COL_PAY_TYPE = "pay_type";

    public static final String COL_REWARD_AMOUNT = "reward_amount";

    public static final String COL_SUBSIDY_AMOUNT = "subsidy_amount";

    public static final String COL_WORKER_GENDER = "worker_gender";

    public static final String COL_WORKER_AGE_LIMIT_TYPE = "worker_age_limit_type";

    public static final String COL_WORKER_AGE_LIMIT_MIN = "worker_age_limit_min";

    public static final String COL_WORKER_AGE_LIMIT_MAX = "worker_age_limit_max";

    public static final String COL_EDU_BACKGROUND = "edu_background";

    public static final String COL_PROFESSIONAL_SKILL = "professional_skill";

    public static final String COL_JOB_DESCRIBE = "job_describe";

    public static final String COL_DELIVERY_STANDARD = "delivery_standard";

    public static final String COL_ACCEPT_MODE = "accept_mode";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_CREATE_BY = "create_by";

    public static final String COL_UPDATE_BY = "update_by";

    public static final String COL_WORK_CATEGORY_NAME = "work_category_name";

    public static final String COL_WORK_CATEGORY_CODE = "work_category_code";
}
