package com.zhixianghui.facade.employee.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发票抬头表
 */
@Data
@TableName(value = "tbl_invoice_title_info")
public class InvoiceTitleInfo implements Serializable {
    private static final long serialVersionUID = 6929577525479120087L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 税号
     */
    @TableField(value = "tax_no")
    private String taxNo;

    /**
     * 公司名称
     */
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 公司地址
     */
    @TableField(value = "company_address")
    private String companyAddress;

    /**
     * 开户行
     */
    @TableField(value = "bank_name")
    private String bankName;

    /**
     * 银行账号
     */
    @TableField(value = "bank_account_no")
    private String bankAccountNo;

    /**
     * 联系电话
     */
    @TableField(value = "contact_mobile")
    private String contactMobile;

    /**
     * 是否默认
     */
    @TableField(value = "is_default")
    private Boolean isDefault = Boolean.TRUE;

    /**
     * 身份证号码
     */
    @TableField(value = "id_card_no")
    private String idCardNo;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    public static final String COL_ID = "id";

    public static final String COL_TAX_NO = "tax_no";

    public static final String COL_COMPANY_NAME = "company_name";

    public static final String COL_COMPANY_ADDRESS = "company_address";

    public static final String COL_BANK_NAME = "bank_name";

    public static final String COL_BANK_ACCOUNT_NO = "bank_account_no";

    public static final String COL_CONTACT_MOBILE = "contact_mobile";

    public static final String COL_IS_DEFAULT = "is_default";

    public static final String COL_ID_CARD_NO = "id_card_no";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";
}
