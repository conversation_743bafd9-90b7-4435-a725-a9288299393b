package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name = "雇员性别", type = SystemTypeEnum.CKH_MANAGEMENT)
public enum WorkerGenderEnum implements BaseEnumInterFace {

    MALE(1, "男性"),
    FEMALE(0, "女性"),
    NO_LIMIT(2, "不限"),
    ;

    private final Integer code;
    private final String desc;
}
