package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="结算类型",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum SettleStatusEnum implements BaseEnumInterFace {

    PENDING_SETTLEMENT(200, "待结算"),
    SETTLED(100, "已结算"),
    ;

    private final Integer code;
    private final String desc;
}
