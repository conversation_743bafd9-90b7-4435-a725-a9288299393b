package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月05日 16:46:00
 */
@Getter
@AllArgsConstructor
@EnumDesc(name = "是否指派",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum  IsAssignEnum implements BaseEnumInterFace {

    YES(100,"指派"),

    NO(200,"非指派");

    private final Integer code;

    private final String desc;

}