package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="报酬类型",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum RewardTypeEnum implements BaseEnumInterFace {

    FIX_AMOUNT(100, "固定金额"),
    DISCUSS(200, "面议"),;

    private final Integer code;
    private final String desc;
}
