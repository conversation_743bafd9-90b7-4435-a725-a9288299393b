package com.zhixianghui.facade.employee.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月16日 14:31:00
 */
@Data
public class JobWorkerDetailVo extends JobWorkerVo implements Serializable {
    private static final long serialVersionUID = 7605827163101003767L;
    private String workerPhone;

    private String jobDescribe;

    private String deliveryStandard;

    private LocalDateTime jobAcceptTime;

    private LocalDateTime jobFinishTime;

    private LocalDateTime createTime;

    private String attachment;

    private List<String> attachments;

    private String deliveryContent;

    private String failReason;

}
