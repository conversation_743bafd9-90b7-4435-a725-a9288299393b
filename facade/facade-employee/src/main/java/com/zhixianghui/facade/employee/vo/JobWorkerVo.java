package com.zhixianghui.facade.employee.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zhixianghui.common.statics.annotations.SetValue;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月16日 10:21:00
 */
@Data
public class JobWorkerVo implements Serializable {
    private static final long serialVersionUID = 903498741377151727L;

    /**
     * 主键ID
     */
    protected Long id;

    protected String industryCode;

    protected String industryName;

    protected String jobId;

    protected String jobName;

    protected String jobTag;

    protected Integer jobAvalDateType;

    protected String jobProvinceName;

    protected String jobCityName;

    protected Integer payType;

    protected Integer rewardType;

    protected BigDecimal rewardAmount;

    protected BigDecimal subsidyAmount;

    protected Integer jobStatus;

    protected Long workerId;

    protected String workerPhone;

    protected String workerIdcard;

    protected Integer workerJobStatus;

    @TableField(value = "auth_status")
    @SetValue(className = "com.zhixianghui.service.employee.biz.UtilBiz", method = "getAuthStatusWithNoMd5", paramField = "workerIdcard")
    protected Integer authStatus;

    protected Integer deliveryStatus;

    protected Integer settleStatus;


}
