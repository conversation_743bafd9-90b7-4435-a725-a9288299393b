package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="雇员状态",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum JobWorkerStatusEnum implements BaseEnumInterFace {

    WAIT_RECEIVED(0,"待接受"),
    WAIT_EMPLOY(100, "待录用"),
    REJECTED(200, "已拒绝"),
    PROCESSING(300, "进行中"),
//    EXAMINING(400, "审核中"),
    COMPLETED(400, "已完成"),
    FAIL_AUTH_FAIL(500, "导入失败[鉴权失败]"),
    ;

    private final Integer code;
    private final String desc;
}
