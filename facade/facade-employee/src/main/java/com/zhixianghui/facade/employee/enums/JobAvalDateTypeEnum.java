package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="工作有效类型",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum JobAvalDateTypeEnum implements BaseEnumInterFace {

    LONG_TIME(100, "长期"),
    SHORT_TIME(200, "短期"),;

    private final Integer code;
    private final String desc;
}
