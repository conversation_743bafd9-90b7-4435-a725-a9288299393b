package com.zhixianghui.facade.employee.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.zhixianghui.common.statics.annotations.SetValue;
import com.zhixianghui.common.statics.constants.encry.EncryptKeyConstant;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 雇员信息表
 */
@Data
@TableName(value = "tbl_job_worker_record")
public class JobWorkerRecord implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "version")
    @Version
    private Integer version;

    /**
     * 任务ID
     */
    @TableField(value = "job_id")
    private String jobId;

    /**
     * 任务名称
     */
    @TableField(value = "job_name")
    private String jobName;

    /**
     * 商户编号
     */
    @TableField(value = "employer_no")
    private String employerNo;

    /**
     * 商户名称
     */
    @TableField(value = "employer_name")
    private String employerName;

    /**
     * 雇员姓名
     */
    @TableField(value = "worker_name")
    private String workerName;

    /**
     * 手机号
     */
    @TableField(value = "worker_phone")
    private String workerPhone;

    /**
     * 身份证
     */
    @TableField(value = "worker_idcard")
    private String workerIdcard;

    /**
     * 性别
     */
    @TableField(value = "gender")
    private Integer gender;

    /**
     * 学历
     */
    @TableField(value = "edu_background")
    private Integer eduBackground;

    /**
     * 认证状态
     */
    @TableField(value = "auth_status")
    @SetValue(className = "com.zhixianghui.service.employee.biz.UtilBiz", method = "getAuthStatus", paramField = "workerIdCardMd5")
    private Integer authStatus;

    /**
     * 任务状态
     */
    @TableField(value = "job_status")
    private Integer jobStatus;

    /**
     * 交付状态
     */
    @TableField(value = "delivery_status")
    private Integer deliveryStatus;

    /**
     * 结算状态
     */
    @TableField(value = "settle_status")
    private Integer settleStatus;

    /**
     * 结算状态
     */
    @TableField(value = "is_assign")
    private Integer isAssign;


    /**
     * 接受任务时间
     */
    @TableField(value = "job_accept_time")
    private LocalDateTime jobAcceptTime;

    /**
     * 完成任务时间
     */
    @TableField(value = "job_finish_time")
    private LocalDateTime jobFinishTime;

    @TableField(value = "job_settle_time")
    private LocalDateTime jobSettleTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 交付附件
     */
    @TableField(value = "attachment")
    private String attachment;

    /**
     * 交付内容
     */
    @TableField(value = "delivery_content")
    private String deliveryContent;

    /**
     * 交付内容
     */
    @TableField(value = "fail_reason")
    private String failReason;

    /**
     * 成果验收单
     */
    @TableField(value = "result_signature_url")
    private String resultSignatureUrl;

    @TableField("mainstay_no")
    private String mainstayNo;

    @TableField("mainstay_name")
    private String mainstayName;

    @TableField("mini_open_id")
    private String miniOpenId;


    @TableField("mini_app_id")
    private String miniAppId;
    /**
     * 交付物明细表
     */
    @TableField(value = "deliver_signature_url")
    private String deliverSignatureUrl;

    @TableField(exist = false)
    private boolean signStatus;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_VERSION = "version";

    public static final String COL_JOB_ID = "job_id";

    public static final String COL_JOB_NAME = "job_name";

    public static final String COL_EMPLOYER_NO = "employer_no";

    public static final String COL_EMPLOYER_NAME = "employer_name";

    public static final String COL_WORKER_NAME = "worker_name";

    public static final String COL_WORKER_PHONE = "worker_phone";

    public static final String COL_WORKER_IDCARD = "worker_idcard";

    public static final String COL_GENDER = "gender";

    public static final String COL_EDU_BACKGROUND = "edu_background";

    public static final String COL_AUTH_STATUS = "auth_status";

    public static final String COL_JOB_STATUS = "job_status";

    public static final String COL_DELIVERY_STATUS = "delivery_status";

    public static final String COL_SETTLE_STATUS = "settle_status";

    public static final String COL_IS_ASSIGN = "is_assign";

    public static final String COL_JOB_ACCEPT_TIME = "job_accept_time";

    public static final String COL_JOB_FINISH_TIME = "job_finish_time";

    public static final String COL_JOB_SETTLE_TIME = "job_settle_time";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_ATTACHMENT = "attachment";

    public static final String COL_DELIVERY_CONTENT = "delivery_content";

    public static final String COL_FAIL_REASON = "fail_reason";

    public static final String COL_RESULT_SIGNATURE_URL = "result_signature_url";

    public static final String COL_DELIVER_SIGNATURE_URL = "deliver_signature_url";

    public String getWorkerIdcard() {
        return AESUtil.decryptECB(this.workerIdcard, EncryptKeyConstant.AUTH_REDIS_AES_KEY);
    }

    public void setWorkerIdcard(String workerIdcard) {
        this.workerIdCardMd5 = MD5Util.getMixMd5Str(workerIdcard);
        this.workerIdcard = AESUtil.encryptECB(workerIdcard, EncryptKeyConstant.AUTH_REDIS_AES_KEY);
    }

    @TableField(exist = false)
    private String workerIdCardMd5;

}
