package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="交付类型",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum DeliveryStatusEnum implements BaseEnumInterFace {

    NOT_DELIVERED(100, "未交付"),
    TO_BE_CONFIRMED(200, "待确认"),
    FAIL(300, "审核失败"),
    DELIVERED(400, "已交付"),;

    private final Integer code;
    private final String desc;
}
