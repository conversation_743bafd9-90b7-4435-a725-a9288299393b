package com.zhixianghui.facade.employee.dto;

import cn.hutool.core.bean.BeanUtil;
import com.zhixianghui.facade.employee.entity.Job;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
public class JobQueryDto implements Serializable {

    private static final long serialVersionUID = 5607059918984492791L;

    private String jobId;

    private String jobName;

    private String industryCode;

    /**
     * @see com.zhixianghui.facade.employee.enums.AcceptModeEnum
     */
    private Integer acceptMode;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobStatusEnum
     */
    private Integer jobStatus;

    private LocalDateTime createTimeBegin;

    private LocalDateTime createTimeEnd;

    private String employerNo;

    private String employerNameLike;

    public Map<String, Object> toMap() {
        return BeanUtil.beanToMap(this);
    }

    public Job toJob() {
        Job job = new Job();
        BeanUtil.copyProperties(this,job);
        return job;
    }
}
