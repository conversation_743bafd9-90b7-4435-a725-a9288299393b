package com.zhixianghui.facade.employee.dto;

import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.employee.entity.Job;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 任务表
 */
@Data
public class JobDto implements Serializable {

    private static final long serialVersionUID = 6792957118829802771L;

    private Long id;

    private Integer version;
    /**
     * 行业类型代码
     */
    @NotBlank(message = "行业类型代码不能为空")
    private String industryCode;

    /**
     * 行业类型名称
     */
    @NotBlank(message = "行业类型名称不能为空")
    private String industryName;

    @NotBlank(message = "岗位类目编码不能为空")
    private String workCategoryCode;

    @NotBlank(message = "岗位类目名称不能为空")
    private String workCategoryName;

    private Integer jobStatus;
    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String jobName;

    private String jobId;
    /**
     * 商户编号
     */
//    @NotBlank(message = "商户编号不能为空")
    private String employerNo;

    /**
     * 商户名称
     */
//    @NotBlank(message = "商户名称不能为空")
    private String employerName;

    /**
     * 任务标签
     */
    private String jobTag;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobAvalDateTypeEnum
     * 任务有效日期类型
     */
    @NotNull(message = "任务有效日期类型不能为空")
    private Integer jobAvalDateType;

    /**
     * 任务开始日期
     */
//    @NotNull(message = "任务开始日期不能为空")
    private LocalDate jobStartDate;

    /**
     * 任务结束日期
     */
//    @NotNull(message = "任务结束日期不能为空")
    private LocalDate jobFinishDate;

    /**
     * 工作时段开始时间
     */
    @NotNull(message = "工作时段开始时间不能为空")
//    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalTime jobTimeStart;

    /**
     * 工作时段结束时间
     */
    @NotNull(message = "工作时段结束时间不能为空")
//    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalTime jobTimeEnd;

    /**
     * 任务省份
     */
    private Integer jobProvinceNo;

    /**
     * 任务省份名称
     */
    @NotBlank(message = "任务省份名称不能为空")
    private String jobProvinceName;

    /**
     * 任务城市
     */
    private Integer jobCityNo;

    /**
     * 任务城市名称
     */
    @NotBlank(message = "任务城市名称不能为空")
    private String jobCityName;


    /**
     * 任务城市
     */
    private Integer jobAreaNo;

    /**
     * 任务城市名称
     */
    @NotBlank(message = "任务区名称不能为空")
    private String jobAreaName;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String jobAddress;

    /**
     * 招收人数
     */
    @NotNull(message = "招收人数不能为空")
    private Integer workerNum;

    /**
     * @see com.zhixianghui.facade.employee.enums.RewardTypeEnum
     * 报酬类型
     */
    @NotNull(message = "报酬类型不能为空")
    private Integer rewardType;

    /**
     * @see com.zhixianghui.facade.employee.enums.PayTypeEnum
     * 计费类型 按日计费，按件计费
     */
//    @NotNull(message = "计费类型不能为空")
    private Integer payType;

    /**
     * 金额
     */
//    @NotNull(message = "金额不能为空")
    private BigDecimal rewardAmount;

    /**
     * 补贴金额
     */
    private BigDecimal subsidyAmount;

    /**
     * @see com.zhixianghui.facade.employee.enums.WorkerGenderEnum
     * 性别
     */
    @NotNull(message = "性别不能为空")
    private Integer workerGender;

    /**
     * @see com.zhixianghui.facade.employee.enums.WorkerAgeLimitTypeEnum
     * 年龄限制类型
     */
    @NotNull(message = "年龄限制类型不能为空")
    private Integer workerAgeLimitType;

    /**
     * 最低年龄
     */
//    @NotNull(message = "最低年龄不能为空")
    private Integer workerAgeLimitMin;


    /**
     * 学历要求
     */
    @NotNull(message = "学历要求不能为空")
    private Integer eduBackground;

    /**
     * 最高年龄
     */
//    @NotNull(message = "最高年龄不能为空")
    private Integer workerAgeLimitMax;
    /**
     * 专业技能要求
     */
    private String professionalSkill;

    /**
     * 任务描述
     */
    private String jobDescribe;

    /**
     * 交付标准
     */
    private String deliveryStandard;

    /**
     * @see com.zhixianghui.facade.employee.enums.AcceptModeEnum
     * 接单模式
     */
    @NotNull(message = "接单模式不能为空")
    private Integer acceptMode;

    /**
     * @see com.zhixianghui.facade.employee.enums.JobVisibilityEnum
     */
    @NotNull(message = "任务可见性不能为空")
    private Integer scope;

    private String mainstayNo;

    private String mainstayName;

    private boolean auto;

    private String callbackUrl;

    public Job toJobEntity() {
        Job job = new Job();
        BeanUtil.copyProperties(this, job);
        return job;
    }

}
