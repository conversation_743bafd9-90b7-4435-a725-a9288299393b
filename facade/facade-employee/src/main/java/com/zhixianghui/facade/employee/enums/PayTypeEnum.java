package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="计费类型",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum PayTypeEnum implements BaseEnumInterFace {

    PAY_DAILY(100, "按日计费","件"),
    PAY_COUNT(200, "按件计费","日"),;

    private final Integer code;
    private final String desc;
    private final String unit;
}
