package com.zhixianghui.facade.employee.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 发票抬头表
 */
@Data
public class InvoiceTitleInfoDto implements Serializable {

    private static final long serialVersionUID = 3654563178370017569L;

    /**
     * 税号
     */
    @NotBlank(message = "税号不能为空")
    private String taxNo;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    private String companyName;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccountNo;

    /**
     * 联系电话
     */
    private String contactMobile;

    /**
     * 是否默认
     */
    private Boolean isDefault;

    /**
     * 身份证号码
     */
    private String idCardNo;

}
