package com.zhixianghui.facade.employee.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
    * 收票地址表
    */
@Data
public class InvoicePostAddressDto implements Serializable {
    private static final long serialVersionUID = -1291123126772034566L;
    /**
     * 收件人
     */
    @NotBlank(message = "收件人不能为空")
    private String name;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    private String mobile;

    /**
     * 省份编号
     */
    @NotBlank(message = "省份编号为空")
    private String provinceNo;

    /**
     * 省份名称
     */
    @NotBlank(message = "省份名称不能为空")
    private String provinceName;

    /**
     * 城市编号
     */
    @NotBlank(message = "城市编号不能为空")
    private String cityNo;

    /**
     * 城市名称
     */
    @NotBlank(message = "城市名称不能为空")
    private String cityName;

    /**
     * 区域编号
     */
    @NotBlank(message = "区域编号不能为空")
    private String areaNo;

    /**
     * 区域名称
     */
    @NotBlank(message = "区域名称不能为空")
    private String areaName;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String address;

    /**
     * 是否默认
     */
    private Boolean isDefault = Boolean.TRUE;

}
