package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="工作状态",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum JobStatusEnum implements BaseEnumInterFace {

    NEW(100, "待发布"),
    PROCESSING(200, "进行中"),
    COMPLETED(300, "已完成"),
    REJECTED(400, "拒绝"),

    ;

    private final Integer code;
    private final String desc;
}
