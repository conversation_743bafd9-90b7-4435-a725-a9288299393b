package com.zhixianghui.facade.employee.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumDesc(name="任务可见性",type = SystemTypeEnum.CKH_MANAGEMENT)
public enum JobVisibilityEnum implements BaseEnumInterFace {

    ONLY_INVITED(100, "仅受邀请人可见"),
    PUBLIC(200, "全部人员可见"),;

    private final Integer code;
    private final String desc;
}
