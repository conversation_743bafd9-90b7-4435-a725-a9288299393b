package com.zhixianghui.facade.employee.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.employee.dto.JobDto;
import com.zhixianghui.facade.employee.dto.JobQueryDto;
import com.zhixianghui.facade.employee.dto.JobWebQueryDto;
import com.zhixianghui.facade.employee.dto.SignedJobMemberDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.enums.ApproveEvent;
import com.zhixianghui.facade.employee.vo.JobVo;

import java.util.List;
import java.util.Map;

public interface JobFacade {
    Job addJob(JobDto jobDto) throws BizException;

    Job addJobApi(JobDto jobDto);

    Page<Job> pageJob(Page page, Map<String, Object> param)  throws BizException;

    Page<Job> pageJob(Page page, JobQueryDto jobQueryDto) throws BizException;

    JobVo getJobById(Long id) throws BizException;

    Job getById(Long id);


    Job getJobByJobId(String jobId) throws BizException;

    void deleteById(Long id);

    Job updateById(JobDto jobDto);

    void approved(Long[] ids, ApproveEvent event, String updator);

    List<Job> list(JobWebQueryDto jobWebQueryDto);

    List<Job> listJobListOnGrant(Map<String, Object> param);

    void completeJob(Long id);


    void uploadJobMember(Long id, String name, byte[] file2byte);

    void uploadJobMember(SignedJobMemberDto jobMemberDto);

    String getQrCode(Long id);

}
