package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.SalesFeeRule;

import java.util.List;
import java.util.Map;

/**
 * 销售成本规则
 *
 * <AUTHOR>
 * @date 2020/9/11
 **/
public interface SalesFeeRuleFacade {
    void insert(SalesFeeRule rule)throws BizException;

    void update(SalesFeeRule rule)throws BizException;

    void delete(Long id, String loginName)throws BizException;

    PageResult<List<SalesFeeRule>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    SalesFeeRule getById(Long id);
}
