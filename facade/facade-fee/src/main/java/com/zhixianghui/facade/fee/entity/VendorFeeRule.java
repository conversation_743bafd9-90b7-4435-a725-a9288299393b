package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.entity.BaseCalculateEntity;
import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class VendorFeeRule extends BaseCalculateEntity {
    /**
     * 供应商编号
     */
    private String vendorNo;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;
    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新操作人
     */
    private String updateBy;

    /**
     * 是否已删除
     */
    private Boolean removed = false;

    /**
     * 规则参数
     */
    private String ruleParam;

    /**
     * 数据库不存在
     * 对应字段：ruleParam
     * 通过操作该字段操作ruleParam
     */
    private List<SpecialRuleDto> specialFeeRuleList = new ArrayList<>();

    /**
     * dao 入库使用
     * @return
     */
    @Deprecated
    public String getRuleParam() {
        return JsonUtil.toString(specialFeeRuleList);
    }

    /**
     * dao查询时解析
     * @param ruleParam
     */
    @Deprecated
    public void setRuleParam(String ruleParam) {
        this.ruleParam = ruleParam;
        specialFeeRuleList = JsonUtil.toList(ruleParam, SpecialRuleDto.class);
    }

    public Boolean getRemoved() {
        return removed;
    }

    public void setRemoved(Boolean removed) {
        this.removed = removed;
    }
}