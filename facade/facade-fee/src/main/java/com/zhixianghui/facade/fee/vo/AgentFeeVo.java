package com.zhixianghui.facade.fee.vo;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商户计费vo
 * <AUTHOR>
 * @date 2020/11/18
 **/
@Data
public class AgentFeeVo implements Serializable {
    /**
     * 一级合伙人编号
     */
    private String oneLevelAgentNo;
    /**
     * 一级合伙人姓名
     */
    private String oneLevelAgentName;
    /**
     * 一级合伙人状态
     */
    private Integer oneLevelAgentStatus;
    /**
     * 一级合伙人是否开通产品
     */
    private Boolean oneLeveOpen;
    /**
     * 二级合伙人编号
     */
    private String secondLevelAgentNo;
    /**
     * 二级合伙人姓名
     */
    private String secondLevelAgentName;
    /**
     * 二级合伙人状态
     */
    private Integer secondLevelAgentStatus;
    /**
     * 二级合伙人是否开通产品
     */
    private Boolean twoLeveOpen;
    /**
     * 订单交易时间
     */
    private java.util.Date tradeTime;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 产品供应商编号
     */
    private String vendorNo;

    /**
     * 产品供应商名称
     */
    private String vendorName;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 订单金额
     */
    private java.math.BigDecimal orderAmount;

    /**
     * 订单手续费
     */
    private BigDecimal orderFee;

    private Integer orderType;

    /**
     * 备注
     */
    private String remark;

    private List<SpecialRuleParamDto> dtoList;
}
