package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 销售毛利订单表
 * <AUTHOR>
 */
@Data
public class SalesFeeOrder extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 订单交易时间
	 */
	private java.util.Date tradeTime;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 商户名称
	 */
	private String mchName;

	/**
	 * 产品供应商编号
	 */
	private String vendorNo;

	/**
	 * 产品供应商名称
	 */
	private String vendorName;

	/**
	 * 产品编号
	 */
	private String productNo;

	/**
	 * 产品名称
	 */
	private String productName;

	/**
	 * 商户订单号
	 */
	private String mchOrderNo;

	/**
	 * 平台流水号
	 */
	private String platTrxNo;

	/**
	 * 订单金额
	 */
	private java.math.BigDecimal orderAmount;

	/**
	 * 订单商户手续费
	 */
	private BigDecimal orderFee;

	/**
	 * 销售成本
	 */
	private java.math.BigDecimal salesFee;

	/**
	 * 销售毛利
	 */
	private java.math.BigDecimal salesProfit;

	/**
	 * 计算规则
	 */
	private String calculateFormula;

	/**
	 * 商户类型
	 */
	private Integer merchantType;

	/**
	 * 订单类型
	 */
	private Integer orderType;

	/**
	 * 部门id
	 */
	private Long departmentId;

	/**
	 * 部门名称
	 */
	private String departmentName;

	/**
	 * 销售id
	 */
	private Long salerId;

	/**
	 * 销售名称
	 */
	private String salerName;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 是否存在合伙人
	 */
	private Boolean existAgent;

	/**
	 * 合伙人分润
	 */
	private BigDecimal agentProfit;

	/**
	 * 合伙人二级分润
	 */
	private BigDecimal agentSecondProfit;

	/**
	 * 其他信息，json
	 * 请通过extJsonInfo字段进行操作
	 */
	private String extJson;

	private ExtJsonInfo extJsonInfo = new ExtJsonInfo();

	@Data
	public static class ExtJsonInfo implements Serializable {
		private Boolean hasAgent;
		private String agentNo;
		private String agentName;
	}

	public String getExtJson() {
		return JsonUtil.toString(extJsonInfo);
	}

	public void setExtJson(String extJson) {
		this.extJson = extJson;
		this.extJsonInfo = JsonUtil.toBean(extJson, ExtJsonInfo.class);
	}

	public void setExistAgent(Integer existAgent) {
		if (existAgent == null) {
			return;
		}
		if (existAgent == 1) {
			this.existAgent = true;
		}
		if (existAgent == 0) {
			this.existAgent = false;
		}
	}

}
