package com.zhixianghui.facade.fee.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.facade.fee.entity.AgentMonthBill;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/24 9:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentMonthBillVo extends PageQueryVo implements Serializable {
    private static Logger logger = LoggerFactory.getLogger(AgentMonthBillVo.class);
    private List<Long> idList;
    private Long id;
    /**
     * 系统类型
     */
    private Integer systemType;


    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 订单交易时间
     */
    private String billDate;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 个税
     */
    private BigDecimal tax;

    /**
     * 代扣税比例
     */
    private BigDecimal taxPercent;
    private BigDecimal withholdingTaxRatio;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 销售用户id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 备注
     */
    private String note;

    /**
     * 未结算状态:100, 待人工结算101, 已人工结算102, 不结算103, 结算失败104, 已自动结算105
     */
    private Integer settlementStatus;
    @JsonIgnore
    private final int[] settlementStatusArray = new int[]{100, 101, 102, 103, 104, 105};

    /**
     * 企业编号
     */
    private Long enterpriseId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识, 0删除, 1未删除
     */
    private Integer deleteFlag;

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 合伙人月收益
     */
    private BigDecimal agentMonthProfit;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作姓名
     */
    private String operateName;

    /**
     * 是否自行申报, 1是,0否
     */
    private Integer selfDeclared;

    private String idCardNo;
    private String bankCardNo;
    private String mobile;
    public List<AgentMonthBill> toAgentMonthBill(AgentMonthBillVo vo, List<AgentMonthBill> agentMonthBillList) {
        agentMonthBillList.forEach(agentMonthBill -> {
            agentMonthBill.setUpdateTime(new Date());
                    if (vo.getSettlementStatus() != null && legal(vo.getSettlementStatus())) {
                        agentMonthBill.setSettlementStatus(vo.getSettlementStatus());
                    }
                    if (StringUtils.isNotBlank(vo.getNote()) ||
                            (StringUtils.isNotBlank(agentMonthBill.getNote()) && StringUtils.isBlank(vo.getNote()))) {
                        agentMonthBill.setNote(vo.getNote());
                    }
                    if (vo.getSettlementAmount() != null) {
                        logger.info("[合伙人结算金额修改]合伙人:{},结算单编号:{},结算日期:{},由{}改为{}",vo.getAgentNo(),vo.getBillNo(),vo.getBillDate(),agentMonthBill.getSettlementAmount(), vo.getSettlementAmount());
                        agentMonthBill.setSettlementAmount(vo.getSettlementAmount());
                    }
        }
        );

       return agentMonthBillList;

    }

    private boolean legal(Integer settlementStatus) {
        return Arrays.stream(settlementStatusArray).anyMatch(item -> item == settlementStatus);
    }

}
