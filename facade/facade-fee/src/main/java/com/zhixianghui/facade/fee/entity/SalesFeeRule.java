package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.entity.BaseCalculateEntity;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * <AUTHOR>
 */
@Data
public class SalesFeeRule extends BaseCalculateEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * updateTime
	 */
	private java.util.Date updateTime;

	/**
	 * updator
	 */
	private String updator;

	/**
	 * 部门id
	 */
	private Long departmentId;

	/**
	 * 部门名称
	 */
	private String departmentName;

	/**
	 * 产品编码
	 */
	private String productNo;

	/**
	 * 产品名称
	 */
	private String productName;

    /**
     * 状态
     * @see com.zhixianghui.common.statics.constants.common.PublicStatus
     */
	private Integer status;

    /**
     * 备注
	 */
	private String description;

	/**
	 * 是否已删除
	 */
	private Boolean removed = false;

	/**
    /**
     * 特殊计费规则参数
     * 数据库字段，请通过 specialFeeRuleList 来操作修改相应值
     */
    private String ruleParam;

    private List<SpecialRuleDto> specialFeeRuleList = new ArrayList<>();

    /**
     * dao 入库使用
     * @return
     */
    @Deprecated
    public String getRuleParam() {
        return JsonUtil.toString(specialFeeRuleList);
    }

    /**
     * dao查询时解析
     * @param ruleParam
     */
    @Deprecated
    public void setRuleParam(String ruleParam) {
        this.ruleParam = ruleParam;
        specialFeeRuleList = JsonUtil.toList(ruleParam, SpecialRuleDto.class);
    }
}
