package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 合伙人分佣统计表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentFeeSum extends BaseEntity {

    /**
     * 订单交易时间
     */
    private String tradeDay;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 邀请人编号
     */
    private String inviterNo;

    /**
     * 邀请人名称
     */
    private String inviterName;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 订单金额(实发金额)
     */
    private BigDecimal totalNetAmount = BigDecimal.ZERO;

    /**
     * 总收益
     */
    private BigDecimal totalProfit = BigDecimal.ZERO;

    /**
     * 交易分润
     */
    private BigDecimal tradeProfit = BigDecimal.ZERO;

    /**
     * 邀请奖励
     */
    private BigDecimal inviteReward = BigDecimal.ZERO;

    /**
     * 销售id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;


}
