package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.AgentProductRelation;

import java.util.List;
import java.util.Map;

/**
 * 合伙人产品关系表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-21
 */
public interface AgentProductRelationFacade {
    /**
     * 合伙人产品开通查询接口
     * @param paramMap  查询参数
     * @param pageParam 分页参数
     */
    PageResult<List<AgentProductRelation>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 删除接口
     * @param id    主键
     */
    void delete(long id,String optUser) throws BizException;

    /**
     * 修改接口
     * @param relation  产品开通信息
     */
    void update(AgentProductRelation relation) throws BizException;

    /**
     * 产品功能
     * @param relation  产品开源信息
     */
    void insert(AgentProductRelation relation) throws BizException;

    /**
     * 批量插入
     * @param relationList  产品开通信息列表
     * @throws BizException 业务异常
     */
    void insert(List<AgentProductRelation> relationList) throws BizException;

    /**
     * 查看接口
     * @param id    主键
     */
    AgentProductRelation getById(long id);

    /**
     * 产品开通判断
     * @param agentNo   合伙人编号
     * @param productNo 产品编号
     */
    boolean isOpenProduct(String agentNo, String productNo);

    List<AgentProductRelation> listByAgentNoAndStatus(String agentNo, Integer status);
}
