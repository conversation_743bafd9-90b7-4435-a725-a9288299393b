package com.zhixianghui.facade.fee.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商计费统计vo
 *
 * <AUTHOR>
 * @date 2021/1/19
 **/
@Data
public class SalesFeeSumVo implements Serializable {
    /**
     * 总笔数
     */
    private Integer totalNum = 0;

    /**
     * 总分润费用
     */
    private BigDecimal totalProfit = BigDecimal.ZERO;

    /**
     * 总订单金额
     */
    private BigDecimal totalOrderAmount = BigDecimal.ZERO;

    /**
     * 销售毛利
     */
    private BigDecimal totalSalesProfit = BigDecimal.ZERO;

    /**
     * 一级合伙人分润总和
     */
    private BigDecimal agentProfit = BigDecimal.ZERO;

    /**
     * 二级合伙人分润总和
     */
    private BigDecimal agentSecondProfit = BigDecimal.ZERO;

    /**
     * 合伙人分润总和
     */
    private BigDecimal totalAgentProfit = BigDecimal.ZERO;

    public BigDecimal getTotalAgentProfit() {
        return this.agentProfit.add(this.agentSecondProfit);
    }
}
