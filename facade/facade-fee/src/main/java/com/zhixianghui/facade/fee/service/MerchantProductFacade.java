package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.MerchantProductRelation;

import java.util.List;
import java.util.Map;

public interface MerchantProductFacade {
    /**
     * 供应商成本计费查询接口
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<MerchantProductRelation>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 删除接口
     * @param id
     */
    void delete(long id,String optUser) throws BizException;

    /**
     * 修改接口
     * @param merchantProductRelation
     */
    void update(MerchantProductRelation merchantProductRelation) throws BizException;

    /**
     * 产品功能
     * @param merchantProductRelation
     */
    void insert(MerchantProductRelation merchantProductRelation) throws BizException;

    /**
     * 查看接口
     * @param id
     * @return
     */
    MerchantProductRelation getById(long id);

    void insert(List<MerchantProductRelation> merchantProductRelations) throws BizException;

    /**
     * 产品开通判断
     * @param mchNo
     * @param productNo
     * @return
     */
    boolean isOpenProduct(String mchNo, String productNo);
}
