package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.SalesFeeOrder;
import com.zhixianghui.facade.fee.vo.SalesFeeStatisticVo;
import com.zhixianghui.facade.fee.vo.SalesFeeSumVo;

import java.util.List;
import java.util.Map;

public interface SalesFeeOrderQueryFacade {
    /**
     * 分页查询接口
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<SalesFeeOrder>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    SalesFeeOrder getByPlatTrxNo(String platTrxNo);

    /**
     * 统计
     * @param paramMap
     * @return
     */
    SalesFeeSumVo sumSalesFeeOrder(Map<String, Object> paramMap);

    Map<String, Object> getSalerStatistics(Map<String, Object> nowDate, Map<String, Object> lastDate);

    PageResult<List<SalesFeeStatisticVo>> salerFeeStatistics(Map<String, Object> paramMap, PageParam pageParam);

    List<Map<String, Object>> listSalerOrdersGroupedByMch(Map<String, Object> paramMap);
    PageResult<List<SalesFeeOrder>> listPageGroup(Map<String, Object> paramMap, PageParam pageParam);
}
