package com.zhixianghui.facade.fee.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商计费统计vo
 **/
@Data
public class MerchantFeeStatisticVo implements Serializable {
    private String mchNo;
    private String mchName;
    private String vendorNo;
    private String vendorName;
    private String productNo;
    private String productName;
    private BigDecimal totalNetAmount = BigDecimal.ZERO;
    private BigDecimal totalOrderFee = BigDecimal.ZERO;
}
