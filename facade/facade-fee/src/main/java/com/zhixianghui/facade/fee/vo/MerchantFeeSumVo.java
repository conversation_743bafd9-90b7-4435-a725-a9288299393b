package com.zhixianghui.facade.fee.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商户计费统计vo
 * <AUTHOR>
 * @date 2021/1/19
 **/
@Data
public class MerchantFeeSumVo implements Serializable {
    /**
     * 总笔数
     */
    private Integer totalNum = 0;

    /**
     * 总手续费
     */
    private BigDecimal totalFee = BigDecimal.ZERO;

    /**
     * 总订单金额
     */
    private BigDecimal totalOrderAmount = BigDecimal.ZERO;
}
