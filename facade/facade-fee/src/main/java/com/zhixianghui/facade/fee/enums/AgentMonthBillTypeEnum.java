package com.zhixianghui.facade.fee.enums;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/3/23 19:57
 */
@AllArgsConstructor
@Getter
@ToString
public enum AgentMonthBillTypeEnum {
    /**
     * 已人工结算100, 不待人工结算101, 结算102, 结算失败103, 已自动结算104, 未结算状态:105
     */

    MANUALLY_SETTLED(100, "已人工结算"),
    PENDING_MANUAL_SETTLEMENT(101, "待人工结算"),
    NO_SETTLEMENT(102, "不结算"),
    SETTLEMENT_FAILED(103, "结算失败"),
    AUTOMATICALLY_SETTLED(104, "已自动结算"),
    UN_SETTLEMENT(105, "未结算");

    int status;
    String text;

    public static String getTextByValue(int value) {
        AgentMonthBillTypeEnum agentMonthBillTypeEnum = Arrays.stream(AgentMonthBillTypeEnum.values()).filter(x->x.getStatus() == value).findFirst().orElse(null);
        if (agentMonthBillTypeEnum == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("状态参数异常");
        }
        return agentMonthBillTypeEnum.getText();
    }

}
