package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.AgentFeeRule;

import java.util.List;
import java.util.Map;

/**
 * 合伙人计费规则表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-21
 */
public interface AgentFeeRuleFacade {
    /**
     * 合伙人成本计费查询接口
     * @param paramMap  查询参数
     * @param pageParam 分页参数
     */
    PageResult<List<AgentFeeRule>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    List<AgentFeeRule> listByParam(Map<String, Object> paramMap);

    /**
     * 合伙人成本计费查询接口
     * @param agentNo 合伙人账号
     * @param productNo 产品编号
     * @return 合伙人计费规则
     */
    AgentFeeRule listPage(String agentNo, String productNo);

    /**
     * 删除接口
     * @param id 主键
     * @param optUser 用户登录名
     */
    void deleteById(long id,String optUser) throws BizException;

    void deleteBy(String agentNo, String productNo, Integer priority);

    void deleteByParam(Map<String, Object> param,String opt);

    /**
     * 修改接口
     * @param agentFeeRule 计费规则信息
     */
    void update(AgentFeeRule agentFeeRule)throws BizException;

    /**
     * 增加规则
     * @param agentFeeRule 计费规则信息
     */
    void save(AgentFeeRule agentFeeRule) throws BizException;
    void save(List<AgentFeeRule> agentFeeRuleList);

    /**
     * 查看接口
     */
    AgentFeeRule getById(long id);

    void updateRealProfitRatio(AgentFeeRule rule);
}
