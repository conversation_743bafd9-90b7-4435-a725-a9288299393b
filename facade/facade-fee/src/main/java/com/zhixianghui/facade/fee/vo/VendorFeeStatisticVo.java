package com.zhixianghui.facade.fee.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商计费统计vo
 **/
@Data
public class VendorFeeStatisticVo implements Serializable {
    private String mchNo;
    private String mchName;
    private String vendorNo;
    private String vendorName;
    private String productNo;
    private String productName;
    private BigDecimal totalNetAmount = BigDecimal.ZERO;
    private BigDecimal totalVendorFee = BigDecimal.ZERO;
    private BigDecimal orderFee = BigDecimal.ZERO;
    private Boolean existAgent = false;
}
