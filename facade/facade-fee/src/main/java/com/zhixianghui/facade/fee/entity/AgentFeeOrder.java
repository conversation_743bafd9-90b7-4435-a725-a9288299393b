package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合伙人计费订单表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentFeeOrder extends BaseEntity {

    /**
     * 订单交易时间
     */
    private Date tradeTime;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 产品供应商编号
     */
    private String vendorNo;

    /**
     * 产品供应商名称
     */
    private String vendorName;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 邀请人编号
     */
    private String inviterNo;

    /**
     * 邀请人名称
     */
    private String inviterName;

    /**
     * @see com.zhixianghui.common.statics.enums.agent.AgentTypeEnum
     */
    private Integer agentType;

    /**
     * 奖励类型
     * @see com.zhixianghui.common.statics.enums.fee.RewardTypeEnum
     */
    private Integer rewardType;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单商户手续费
     */
    private BigDecimal orderFee;

    /**
     * 合伙人成本
     */
    private BigDecimal agentCost;

    /**
     * 合伙人毛利
     */
    private BigDecimal agentProfit;

    /**
     * 计算规则
     */
    private String calculateFormula;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 销售id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 其他信息，json
     */
    private String extJson;

    /**
     * 合伙人费率
     */
    private BigDecimal agentFeeRate;

    /**
     * 自己申报
     */
    private Integer selfDeclare;

    /**
     * 商户费率
     */
    private BigDecimal merchantFeeRate;

    /**
     * 真实分润比例
     */
    private Integer realProfitRatio;

}
