package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.dto.fee.CalculateResultDto;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.fee.vo.SalesFeeVo;
import com.zhixianghui.facade.fee.vo.VendorFeeVo;

/**
 * 销售计费接口
 * <AUTHOR>
 * @date 2020/11/9
 **/
public interface SalesFeeCalculateFacade {

    /**
     * 创建销售计费订单
     * @param vo 销售计费订单实体
     * @return
     */
    CalculateResultDto calculateAndSave(SalesFeeVo vo) throws BizException;
}
