package com.zhixianghui.facade.fee.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合伙人分佣统计表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentFeeOrderSumGroupVo implements Serializable {

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 邀请人编号
     */
    private String inviterNo;

    /**
     * 邀请人名称
     */
    private String inviterName;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 订单金额(实发金额)
     */
    private BigDecimal totalNetAmount;

    /**
     * 总收益
     */
    private BigDecimal totalProfit;

    /**
     * 奖励类型
     * @see com.zhixianghui.common.statics.enums.fee.RewardTypeEnum
     */
    private Integer rewardType;
    /**
     * 销售id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

}
