package com.zhixianghui.facade.fee.vo;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 成本计费vo
 * <AUTHOR>
 * @date 2020/11/18
 **/
@Data
public class VendorFeeVo implements Serializable {
    /**
     * 订单交易时间
     */
    private java.util.Date tradeTime;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String vendorNo;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 请求渠道订单号
     */
    private String channelOrderNo;

    /**
     * 渠道流水号
     */
    private String channelTrxNo;

    /**
     * 订单金额
     */
    private java.math.BigDecimal orderAmount;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 备注
     */
    private String remark;


    private List<SpecialRuleParamDto> dtoList;

    /**
     * 是否存在合伙人
     */
    private Integer existAgent;

    private BigDecimal orderItemFee;
}
