package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.AgentFeeOrder;
import com.zhixianghui.facade.fee.vo.AgentFeeOrderSumVo;
import com.zhixianghui.facade.fee.vo.AgentFeeStatisticVo;

import java.util.List;
import java.util.Map;

public interface AgentFeeOrderQueryFacade {
    /**
     * 分页查询接口
     * @param paramMap  查询参数
     * @param pageParam 分页参数
     */
    PageResult<List<AgentFeeOrder>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    List<AgentFeeOrder> getByPlatTrxNo(String platTrxNo);

    /**
     * 合伙人分润订单金额统计
     * @param paramMap  查询参数
     */
    AgentFeeOrderSumVo sumOrder(Map<String, Object> paramMap);

    PageResult<List<AgentFeeStatisticVo>> agentFeeStatistics(Map<String, Object> paramMap, PageParam pageParam);

    Map<String, Object> getAgentOrder(Map<String, Object> nowDateMap, Map<String, Object> lastDateMap);

    AgentFeeOrderSumVo countOrder(Map<String, Object> paramMap);
}
