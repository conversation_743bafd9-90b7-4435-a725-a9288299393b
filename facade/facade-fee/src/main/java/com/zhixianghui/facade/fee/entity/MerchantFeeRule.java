package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.entity.BaseCalculateEntity;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class MerchantFeeRule extends BaseCalculateEntity {
    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 规则类型
     * @see com.zhixianghui.common.statics.enums.fee.RuleTypeEnum
     */
    private Integer ruleType;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 状态
     * @see com.zhixianghui.common.statics.enums.common.PublicStatusEnum
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新操作人
     */
    private String updateBy;

    /**
     * 流程业务标记
     */
    private String flowBusinessKey;

    /**
     * 是否已删除
     */
    private Boolean removed = false;

    /**
     * 规则参数
     * 数据库使用
     */
    @Deprecated
    private String ruleParam;


    /**
     * dao 入库使用
     * @return
     */
    @Deprecated
    public String getRuleParam() {
        return JsonUtil.toString(this.getSpecialFeeRuleList());
    }

    /**
     * dao查询时解析
     * @param ruleParam
     */
    @Deprecated
    public void setRuleParam(String ruleParam) {
        this.ruleParam = ruleParam;
        this.setSpecialFeeRuleList(JsonUtil.toList(ruleParam, SpecialRuleDto.class));
    }

    public Boolean getRemoved() {
        return removed;
    }

    public void setRemoved(Boolean removed) {
        this.removed = removed;
    }
}