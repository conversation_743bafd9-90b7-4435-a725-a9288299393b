package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.AgentMonthBill;
import com.zhixianghui.facade.fee.vo.AgentFeeSumVo;
import com.zhixianghui.facade.fee.vo.AgentMonthBillVo;

import java.util.List;
import java.util.Map;

/**
 * 合伙人月账单表 Facade类
 *
 * <AUTHOR>
 * @date 2021-03-23
 */
public interface AgentMonthBillFacade {
    /**
     *
     * @param paramMap 待查询参数
     * @param newInstance   分页参数
     * @return  月账单列表
     */
    PageResult<List<AgentMonthBillVo>> listPage(Map<String, Object> paramMap, PageParam newInstance);

    /**
     * @param agentMonthBillList 待更新参数
     */
    void check(List<AgentMonthBill> agentMonthBillList);

    /**
     *
     * @param id    主键
     * @return  某个合伙人的月账单
     */
    AgentMonthBill getById(Long id);

    /**
     * @param agentMonthBillVo  待导出的实体
     */
    void exportMonthBill(AgentMonthBillVo agentMonthBillVo);

    /**
     *
     * @param idList id列表
     * @return 月账单数据
     */
    List<AgentMonthBill> getBatchById(List<Long> idList);

    List<Map<String,Object>> listMap(Map<String, Object> paramMap);

    Map<String,Object> getTotalProfitByAgent(String agentNo);
}
