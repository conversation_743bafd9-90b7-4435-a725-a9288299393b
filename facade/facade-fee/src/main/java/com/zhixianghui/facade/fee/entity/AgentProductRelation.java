package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 合伙人产品关系表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentProductRelation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新操作人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否已删除 0：正常 1 删除标记
     */
    private Boolean removed;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;


}
