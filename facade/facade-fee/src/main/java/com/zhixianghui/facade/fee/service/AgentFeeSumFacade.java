package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.vo.AgentFeeSumVo;

import java.util.List;
import java.util.Map;

/**
 * 合伙人分佣统计表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-25
 */
public interface AgentFeeSumFacade {

    PageResult<List<AgentFeeSumVo>> listSumPage(Map<String, Object> paramMap, PageParam newInstance);

    AgentFeeSumVo getSumByAgentNo(Map<String, Object> paramMap);
}
