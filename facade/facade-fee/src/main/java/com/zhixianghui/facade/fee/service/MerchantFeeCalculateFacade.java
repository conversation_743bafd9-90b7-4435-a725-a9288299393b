package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.dto.fee.CalculateResultDto;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.fee.vo.MerchantFeeVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商户计费接口
 * <AUTHOR>
 * @date 2020/11/9
 **/
public interface MerchantFeeCalculateFacade {
    /**
     * 商户产品计费
     * @param mchNo         商户编号
     * @param productNo     产品编号
     * @param orderAmount   订单金额
     * @param dtoList       特殊计费规则参数
     * @param logFlag       日志标识
     * @return
     */
    CalculateResultDto calculateFee(String mchNo,
                                    String productNo,
                                    BigDecimal orderAmount,
                                    List<SpecialRuleParamDto> dtoList,
                                    String logFlag) throws BizException;

    /**
     * 创建商户产品计费订单
     * @param vo 商户计费订单实体
     * @return
     */
    //CalculateResultDto calculateAndSave(MerchantFeeVo vo) throws BizException;
}
