package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.MerchantFeeOrder;
import com.zhixianghui.facade.fee.vo.MerchantFeeStatisticVo;
import com.zhixianghui.facade.fee.vo.MerchantFeeSumVo;

import java.util.List;
import java.util.Map;

public interface MerchantFeeOrderQueryFacade {
    /**
     * 分页查询接口
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return  商户计费订单
     */
    PageResult<List<MerchantFeeOrder>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 统计
     * @param paramMap 查询条件
     * @return  商户计费实体
     */
    MerchantFeeSumVo sumMerchantFeeOrder(Map<String, Object> paramMap);

    PageResult<List<MerchantFeeStatisticVo>> merchantFeeStatistics(Map<String, Object> paramMap, PageParam pageParam);

    void updateBatchMerchantFeeOrder(List<MerchantFeeOrder> merchantFeeOrderList);

    Map<String, Object> getMerchantOrder(Map<String, Object> nowDateMap, Map<String, Object> lastDateMap);

    MerchantFeeOrder getByPlatTrxNo(String platTrxNo);
}
