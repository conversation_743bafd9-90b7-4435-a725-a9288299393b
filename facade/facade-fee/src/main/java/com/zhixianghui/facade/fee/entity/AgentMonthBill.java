package com.zhixianghui.facade.fee.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合伙人月账单表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentMonthBill implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 订单交易时间
     */
    private String billDate;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 个税
     */
    private BigDecimal tax;

    /**
     * 代扣税比例
     */
    private BigDecimal withholdingTaxRatio;

    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;

    /**
     * 销售用户id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 备注
     */
    private String note;

    /**
     * 未结算状态:100, 待人工结算101, 已人工结算102, 不结算103, 结算失败104, 已自动结算105
     */
    private Integer settlementStatus;

    /**
     * 企业编号
     */
    private Long enterpriseId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标识, 0删除, 1未删除
     */
    private Integer deleteFlag;

    /**
     * 账单编号
     */
    private String billNo;

    /**
     * 合伙人月收益
     */
    private BigDecimal agentMonthProfit;


}
