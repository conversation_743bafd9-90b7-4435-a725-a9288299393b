package com.zhixianghui.facade.fee.vo;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售计费vo
 * <AUTHOR>
 * @date 2020/11/18
 **/
@Data
public class SalesFeeVo implements Serializable {
    /**
     * 订单交易时间
     */
    private java.util.Date tradeTime;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 产品供应商编号
     */
    private String vendorNo;

    /**
     * 产品供应商名称
     */
    private String vendorName;
    /**
     * 合伙人编号
     */
    private String agentNo;
    /**
     * 合伙人名称
     */
    private String agentName;
    /**
     * 是否有合伙人
     */
    private Boolean hasAgent;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 订单金额
     */
    private java.math.BigDecimal orderAmount;

    /**
     * 订单手续费
     * 如果存在合伙人，该值为合伙人一级分佣成本
     * 如果不存在合伙人，该值为订单商户手续费
     */
    private BigDecimal orderFee;

    /**
     * 合伙人分润
     */
    private BigDecimal firstAgentProfit;

    /**
     *  二级合伙人分润
     */
    private BigDecimal secondAgentProfit;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 备注
     */
    private String remark;

    private List<SpecialRuleParamDto> dtoList;

    /**
     * 是否存在合伙人
     */
    private Integer existAgent;
}
