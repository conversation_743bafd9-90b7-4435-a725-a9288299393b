package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 产品计费订单表
 * <AUTHOR>
 */
@Data
public class MerchantFeeOrder extends BaseEntity {
	/**
	 * 订单交易时间
	 */
	private java.util.Date tradeTime;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 商户名称
	 */
	private String mchName;

	/**
	 * 产品供应商编号
	 */
	private String vendorNo;

	/**
	 * 产品供应商名称
	 */
	private String vendorName;

	/**
	 * 商户类型
	 */
	private Integer merchantType;

	/**
	 * 产品编号
	 */
	private String productNo;

	/**
	 * 产品名称
	 */
	private String productName;

	/**
	 * 商户订单号
	 */
	private String mchOrderNo;

	/**
	 * 平台流水号
	 */
	private String platTrxNo;

	/**
	 * 订单金额
	 */
	private java.math.BigDecimal orderAmount;

	/**
	 * 订单手续费
	 */
	private java.math.BigDecimal orderFee;

	/**
	 * 计算规则
	 */
	private String calculateFormula;

	/**
	 * 订单类型
	 */
	private Integer orderType;

	/**
	 *  销售id
	 */
	private Long salerId;

	/**
	 * 销售名称
	 */
	private String salerName;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 规则参数
	 */
	private String extJson;

	/**
	 * 销售部门id
	 */
	private Long departmentId;

	/**
	 * 销售部门名称
	 */
	private String departmentName;

	/**
	 * 是否存在合伙人
	 */
	private Boolean existAgent;

	public void setExistAgent(Integer existAgent) {
		if (existAgent == null) {
			return;
		}
		if (existAgent == 1) {
			this.existAgent = true;
		}
		if (existAgent == 0) {
			this.existAgent = false;
		}
	}

}