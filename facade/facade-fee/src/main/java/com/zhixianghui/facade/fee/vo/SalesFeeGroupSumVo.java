package com.zhixianghui.facade.fee.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName SalesFeeGroupSumVo
 * @Description TODO
 * @Date 2022/4/19 14:51
 */
@Data
public class SalesFeeGroupSumVo implements Serializable {

    private String mchNo;

    private String mchName;

    private String vendorNo;

    private String vendorName;

    private String productNo;

    private String productName;

    private String salerName;

    private String departmentName;

    private BigDecimal totalOrderAmount = BigDecimal.ZERO;

    private BigDecimal totalSalesFee = BigDecimal.ZERO;

    private BigDecimal totalSalesProfit = BigDecimal.ZERO;

    private BigDecimal totalOrderFee = BigDecimal.ZERO;
}
