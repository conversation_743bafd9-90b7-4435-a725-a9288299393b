package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 合伙人计费规则表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentFeeRule extends BaseEntity {

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新操作人
     */
    private String updateBy;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 真实分润比例
     */
    private Integer realProfitRatio;

    /**
     * 邀请奖励计算方式
     */
    private Integer calculateMode;

    /**
     * 规则类型
     */
    private Integer ruleType;

    /**
     * 一级佣金成本-公式类型
     */
    private Integer firstFormulaType;

    /**
     * 一级佣金成本-固定金额手续费
     */
    private BigDecimal firstFixedFee;

    /**
     * 一级佣金成本-比例手续费
     */
    private BigDecimal firstFeeRate;

    /**
     * 二级佣金-公式类型
     */
    private Integer secondFormulaType;

    /**
     * 二级佣金-固定金额手续费
     */
    private BigDecimal secondFixedFee;

    /**
     * 二级佣金-费率
     */
    private BigDecimal secondFeeRate;

    /**
     * 最低手续费
     */
    private BigDecimal minFee = new BigDecimal("0.01");

    /**
     * 最高手续费
     */
    private BigDecimal maxFee = new BigDecimal("99999999");

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否已删除 0：正常 1 删除标记
     */
    private Boolean removed;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 规则参数
     * 对应数据库字段，请通过specialFeeRuleList字段进行操作
     */
    private String ruleParam;

    /**
     * 数据库不存在
     * 对应字段：ruleParam
     * 通过操作该字段操作ruleParam
     */
    private List<SpecialRuleDto> specialFeeRuleList = new ArrayList<>();

    /**
     * dao 入库使用
     * @return
     */
    @Deprecated
    public String getRuleParam() {
        return JsonUtil.toString(this.getSpecialFeeRuleList());
    }

    /**
     * dao查询时解析
     * @param ruleParam
     */
    @Deprecated
    public void setRuleParam(String ruleParam) {
        this.ruleParam = ruleParam;
        this.setSpecialFeeRuleList(JsonUtil.toList(ruleParam, SpecialRuleDto.class));
    }
}
