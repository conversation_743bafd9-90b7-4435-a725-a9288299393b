package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.VendorFeeOrder;
import com.zhixianghui.facade.fee.vo.VendorFeeStatisticVo;
import com.zhixianghui.facade.fee.vo.VendorFeeSumVo;

import java.util.List;
import java.util.Map;

public interface VendorFeeOrderQueryFacade {
    /**
     * 分页查询接口
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<VendorFeeOrder>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 统计
     * @param paramMap
     * @return
     */
    VendorFeeSumVo sumVendorFeeOrder(Map<String, Object> paramMap);

    PageResult<List<VendorFeeStatisticVo>> vendorFeeStatistics(Map<String, Object> paramMap, PageParam pageParam);

    Map<String, Object> getSupplyOrder(Map<String, Object> nowDateMap, Map<String, Object> lastDateMap);

    VendorFeeOrder getByPlatTrxNo(String platTrxNo);
}
