package com.zhixianghui.facade.fee.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商计费统计vo
 **/
@Data
public class SalesFeeStatisticVo implements Serializable {
    private String employerNo;
    private String employerName;
    private String mainstayNo;
    private String mainstayName;
    private String productNo;
    private String productName;
    private Long departmentId;
    private String departmentName;
    private Long salerId;
    private String salerName;
    private BigDecimal totalNetAmount = BigDecimal.ZERO;
    private BigDecimal totalMerchantFee = BigDecimal.ZERO;
    private BigDecimal totalSalesFee = BigDecimal.ZERO;
    private BigDecimal salesProfit = BigDecimal.ZERO;
    private Integer orderCount = 0;
}
