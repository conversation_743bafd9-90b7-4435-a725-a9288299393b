package com.zhixianghui.facade.fee.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 合伙人分佣统计
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentFeeOrderSumVo implements Serializable {

    /**
     * 订单金额(实发金额)
     */
    private BigDecimal totalNetAmount;

    /**
     * 总收益
     */
    private BigDecimal totalProfit;

    /**
     * 合伙人总成本
     */
    private BigDecimal totalAgentCost;

    /**
     * 订单数量
     */
    private Integer totalCount;
}
