package com.zhixianghui.facade.fee.vo;

import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.fee.entity.AgentMonthBill;
import com.zhixianghui.facade.fee.enums.AgentMonthBillTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 合伙人分佣统计表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentFeeSumVo implements Serializable {

    /**
     * 代扣税比例
     */
    private BigDecimal taxPercent;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 邀请人编号
     */
    private String inviterNo;

    /**
     * 邀请人名称
     */
    private String inviterName;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 订单金额(实发金额)
     */
    private String totalNetAmount;

    /**
     * 总收益
     */
    private String totalProfit;

    /**
     * 交易分润
     */
    private String totalTradeProfit;

    /**
     * 邀请奖励
     */
    private String totalInviteReward;

    /**
     * 销售id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;
    /**
     * 交易时间
     */
    private String tradeDay;

    public AgentMonthBill toAgentMonthBill(AgentFeeSumVo agentFeeSumVo) {
        AgentMonthBill agentMonthBill = new AgentMonthBill();
        agentMonthBill.setAgentNo(agentFeeSumVo.getAgentNo());
        agentMonthBill.setAgentName(agentFeeSumVo.getAgentName());
        agentMonthBill.setAgentType(agentFeeSumVo.getAgentType());
        agentMonthBill.setBillDate(DateUtil.formatString(agentFeeSumVo.getTradeDay()));
        agentMonthBill.setCreateTime(new Date());
        agentMonthBill.setUpdateTime(new Date());
        agentMonthBill.setSalerId(agentFeeSumVo.getSalerId());
        agentMonthBill.setSalerName(agentFeeSumVo.getSalerName());
        agentMonthBill.setSettlementStatus(AgentMonthBillTypeEnum.PENDING_MANUAL_SETTLEMENT.getStatus());
        return agentMonthBill;
    }
}
