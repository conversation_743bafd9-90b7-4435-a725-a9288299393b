package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.Vendor;

import java.util.List;
import java.util.Map;

public interface VendorFacade {


    /**
     * 新增供应商
     * @param vendor
     */
    void addVendor(Vendor vendor) throws BizException;

    /**
     * 供应商查询接口
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<Vendor>> getVendors(Map<String, Object> paramMap, PageParam pageParam);
    List<Vendor> getVendors();

    /**
     * 供应商删除接口
     * @param id
     */
    void deleteById(long id,String optUser) throws BizException;

    /**
     * 供应商修改接口
     * @param vendor
     */
    void update(Vendor vendor) throws BizException;

    List<Vendor> getAllVendors();

    Vendor getVendorByNo(String vendorNo) throws BizException;

    Vendor getVendorByNoAll(String vendorNo);

    Vendor getVendorById(Long id);

    Vendor getVendorByNoAndProduct(String vendorNo,String productNo);



}
