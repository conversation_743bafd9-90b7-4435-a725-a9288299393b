package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.dto.fee.CalculateResultDto;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.fee.vo.MerchantFeeVo;
import com.zhixianghui.facade.fee.vo.VendorFeeVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 成本计费接口
 * <AUTHOR>
 * @date 2020/11/9
 **/
public interface VendorFeeCalculateFacade {

    /**
     * 创建成本计费订单
     * @param vo 成本计费订单实体
     * @return
     */
    CalculateResultDto calculateAndSave(VendorFeeVo vo) throws BizException;
}
