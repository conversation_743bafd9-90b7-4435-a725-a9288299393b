package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.VendorFeeRule;

import java.util.List;
import java.util.Map;

public interface VendorFeeRuleFacade {
    /**
     * 供应商成本计费查询接口
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<VendorFeeRule>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 删除接口
     * @param id
     */
    void delete(long id,String optUser) throws BizException;

    /**
     * 修改接口
     * @param vendorFeeRule
     */
    void update(VendorFeeRule vendorFeeRule) throws BizException;

    /**
     * 产品功能
     * @param vendorFeeRule
     */
    void addVendorFeeRelation(VendorFeeRule vendorFeeRule) throws BizException;

    /**
     * 查看接口
     * @param id
     * @return
     */
    VendorFeeRule getById(long id);
}
