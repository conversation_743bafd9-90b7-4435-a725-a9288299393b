package com.zhixianghui.facade.fee.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AgentFeeResultVo implements Serializable {
    /**
     *  一级合伙人编号
     */
    private String firstAgentNo;
    /**
     *  一级合伙人名称
     */
    private String firstAgentName;
    /**
     *  一级合伙人成本
     */
    private BigDecimal firstAgentCost;
    /**
     *  一级合伙人分润
     */
    private BigDecimal firstAgentProfit;
    /**
     *  二级合伙人编号
     */
    private String secondAgentNo;
    /**
     *  二级合伙人名称
     */
    private String secondAgentName;
    /**
     *  二级合伙人成本
     */
    private BigDecimal secondAgentCost;
    /**
     *  二级合伙人分润
     */
    private BigDecimal secondAgentProfit;
}
