package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.Product;

import java.util.List;
import java.util.Map;

/**
 * 产品功能管理接口
 */
public interface ProductFunctionFacade {

    /**
     * 产品查询接口
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<Product>> getProductFunction(Map<String, Object> paramMap, PageParam pageParam);

    List<Product> getAllProductFunction();

    /**
     * 产品删除接口
     * @param id
     */
    void deleteById(long id,String optUser) throws BizException;

    /**
     * 产品修改接口
     * @param product
     */
    void update(Product product) throws BizException;

    /**
     * 新增产品功能
     * @param product
     */
    void addProductFunction(Product product) throws BizException;

    Product getProductById(Long id);

    Product getProductByProductNo(String productNo) throws BizException;
}
