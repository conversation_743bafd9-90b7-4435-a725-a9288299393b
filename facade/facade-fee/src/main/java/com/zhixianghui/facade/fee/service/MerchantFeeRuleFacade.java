package com.zhixianghui.facade.fee.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.fee.entity.MerchantFeeRule;

import java.util.List;
import java.util.Map;

public interface MerchantFeeRuleFacade {

    /**
     * 供应商成本计费查询接口
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<MerchantFeeRule>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    List<MerchantFeeRule> listBy(Map<String, Object> paramMap);

    /**
     * 删除接口
     * @param id
     */
    void deleteById(long id,String optUser) throws BizException;

    /**
     * 修改接口
     * @param merchantFeeRule
     */
    void update(MerchantFeeRule merchantFeeRule)throws BizException;

    /**
     * 产品功能
     * @param merchantFeeRule
     */
    void addMerchantFeeRelation(MerchantFeeRule merchantFeeRule)throws BizException;

    /**
     * 查看接口
     * @param id
     * @return
     */
    MerchantFeeRule getById(long id);


    void forceDelete(String mchNo);

}
