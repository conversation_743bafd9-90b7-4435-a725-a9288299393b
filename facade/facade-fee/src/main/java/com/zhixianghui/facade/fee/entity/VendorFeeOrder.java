package com.zhixianghui.facade.fee.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商成本订单表
 * <AUTHOR>
 */
@Data
public class VendorFeeOrder extends BaseEntity{

	/**
	 * 订单交易时间
	 */
	private java.util.Date tradeTime;

	/**
	 * 商户编号
	 */
	private String mchNo;

	/**
	 * 商户名称
	 */
	private String mchName;

	/**
	 * 供应商编号
	 */
	private String vendorNo;

	/**
	 * 供应商名称
	 */
	private String vendorName;

	/**
	 * 产品编号
	 */
	private String productNo;

	/**
	 * 产品名称
	 */
	private String productName;

	/**
	 * 商户订单号
	 */
	private String mchOrderNo;

	/**
	 * 平台流水号
	 */
	private String platTrxNo;

	/**
	 * 请求渠道订单号
	 */
	private String channelOrderNo;

	/**
	 * 请求渠道流水号
	 */
	private String channelTrxNo;

	/**
	 * 订单金额
	 */
	private java.math.BigDecimal orderAmount;

	/**
	 * 供应商手续费
	 */
	private java.math.BigDecimal vendorFee;

	/**
	 * 计算公式
	 */
	private String calculateFormula;

	/**
	 * 商户类型
	 */
	private Integer merchantType;

	/**
	 * 订单类型
	 */
	private Integer orderType;

	/**
	 * 销售id
	 */
	private Long salerId;


	/**
	 * 销售名称
	 */
	private String salerName;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 其他信息，json
	 */
	private String extJson;

	/**
	 * 是否存在合伙人
	 */
	private Boolean existAgent;

	/**
	 * 商户计费
	 */
	private BigDecimal orderFee;

	public void setExistAgent(Integer existAgent) {
		if (existAgent == null) {
			return;
		}
		if (existAgent == 1) {
			this.existAgent = true;
		}
		if (existAgent == 0) {
			this.existAgent = false;
		}
	}
}
