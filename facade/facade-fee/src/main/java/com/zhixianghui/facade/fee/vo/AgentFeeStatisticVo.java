package com.zhixianghui.facade.fee.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 供应商计费统计vo
 **/
@Data
public class AgentFeeStatisticVo implements Serializable {
    private String mchNo;
    private String mchName;
    private String vendorNo;
    private String vendorName;
    private String productNo;
    private String productName;
    private String agentNo;
    private String agentName;
    private Integer rewardType;
    private Integer realProfitRatio;
    private BigDecimal agentFeeRate;
    private BigDecimal totalNetAmount = BigDecimal.ZERO;
    private BigDecimal totalOrderFee = BigDecimal.ZERO;
    private BigDecimal agentCost = BigDecimal.ZERO;
    private BigDecimal agentProfit = BigDecimal.ZERO;
}
