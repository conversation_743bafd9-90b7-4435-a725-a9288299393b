package com.zhixianghui.facade.account.invoice.service;

import com.zhixianghui.common.statics.enums.account.AccountStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.account.invoice.entity.AccountInvoice;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Date: 2020.11.04
 * Description: 发票账户管理
 */
public interface AccountInvoiceManageFacade {

    /**
     * 发票账户状态变更操作.
     *
     * @param accountNo     账户编号.
     * @param accountStatus 账户状态 {@link AccountStatusEnum}
     * @param desc          变更操作说明.
     * @throws BizException .
     */
    void changeAccountStatus(String accountNo, int accountStatus, String desc) throws BizException;


    /**
     * 查询发票账户信息
     *
     * @param paramMap  查询参数
     * @param pageParam 分页参数
     * @throws BizException
     */
    PageResult<List<AccountInvoice>> listAccountPage(Map<String, Object> paramMap, PageParam pageParam) throws BizException;


    /***AccountTransitBizException
     * 根据用户编号获取发票账户信息
     * @param accountNo 账户编号.
     * @return 查询到的账户信息.
     * @throws BizException .
     */
    AccountInvoice getAccountByAccountNo(String accountNo) throws BizException;

    AccountInvoice getAccountByMap(Map<String, Object> paramMap) throws BizException;

    Map<String, Object> statisticsAccountInvoice(Map<String, Object> paramMap);
    
    /**
     * 更新账户信息
     *
     * @param accountInvoice 账户信息
     */
    void updateAccount(AccountInvoice accountInvoice);
}
