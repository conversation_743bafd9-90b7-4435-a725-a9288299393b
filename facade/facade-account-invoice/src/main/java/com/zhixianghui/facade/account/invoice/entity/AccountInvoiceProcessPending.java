package com.zhixianghui.facade.account.invoice.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.BeanUtil;
import lombok.Data;

/**
 * 发票账户待账务处理表
 * <AUTHOR>
 */
@Data
public class AccountInvoiceProcessPending extends BaseEntity{

	/**
	 * 修改时间
	 */
	private java.util.Date modifyTime;

	/**
	 * 账务处理流水号
	 */
	private String accountProcessNo;

	/**
	 * 处理阶段(1=待处理 2=处理中 3=已处理)
	 */
	private Integer processStage;

	/**
	 * 账务处理数据的唯一编码
	 */
	private String dataUnqKey;

	/**
	 * 账务请求数据(JSON格式)
	 */
	private Object requestDto;

	/**
	 * 账务处理数据(JSON LIST格式)
	 */
	private Object processDtoList;

	/**
	 * 备注
	 */
	private String remark;

	//region 非数据库字段
	/**
	 * 数据是否在历史表
	 */
	private boolean inHistory;
	//endregion

	/**
	 * 根据待账务处理历史创建一个待账务处理实体
	 *
	 * @param history 待账务处理历史
	 * @return .
	 */
	public static AccountInvoiceProcessPending newFromHistory(AccountInvoiceProcessPendingHistory history) {
		if (history == null) {
			return null;
		} else {
			AccountInvoiceProcessPending pending = new AccountInvoiceProcessPending();
			BeanUtil.copyProperties(history, pending);
			pending.inHistory = true; //赋值需要在copyProperties后面
			return pending;
		}
	}

}
