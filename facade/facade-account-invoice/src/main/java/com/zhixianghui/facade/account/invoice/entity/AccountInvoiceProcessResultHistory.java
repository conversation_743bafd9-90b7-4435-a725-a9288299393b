package com.zhixianghui.facade.account.invoice.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 在途账户账务处理结果归档表
 */
@Data
public class AccountInvoiceProcessResultHistory extends BaseEntity {
    /**
     * 账务处理流水号
     */
    private String accountProcessNo;

    /**
     * 处理结果(1=成功 -1=失败)
     */
    private Integer processResult;

    /**
     * 错误码(0=无异常 1=系统异常 其他=具体业务异常)
     */
    private Integer errorCode;

    /**
     * 审核阶段(1=不审核 2=待审核 3=已审核)
     */
    private Integer auditStage;

    /**
     * 回调阶段(1=待回调 2=已回调 3=不回调)
     */
    private Integer callbackStage;

    /**
     * 备注/异常描述
     */
    private String remark;

    /**
     * 是否来自异步账务处理(1=是 -1=否)
     */
    private Integer isFromAsync;

    /**
     * 账务请求数据(JSON格式)
     */
    private String requestDto;

    /**
     * 账务处理的数据(JSON格式)
     */
    private String processDtoList;

    /**
     * 迁移时间
     */
    private java.util.Date migrateTime;

    //columns END
}
