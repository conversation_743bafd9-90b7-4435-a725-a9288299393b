package com.zhixianghui.facade.account.invoice.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 发票账户表
 * <AUTHOR>
 */
@Data
public class AccountInvoice extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 修改时间
	 */
	private java.util.Date modifyTime;

	/**
	 * 账户编号
	 */
	private String accountNo;

	/**
	 * 用工企业商户编号
	 */
	private String employerMchNo;

	/**
	 * 用工企业商户名称
	 */
	private String employerMchName;

	/**
	 * 代征主体商户编号
	 */
	private String mainstayMchNo;

	/**
	 * 代征主体商户名称
	 */
	private String mainstayMchName;

	/**
	 * 账户状态
	 */
	private Integer status;

	/**
	 * 待开票余额
	 */
	private java.math.BigDecimal invoiceAmount;

	/**
	 * 冻结金额
	 */
	private java.math.BigDecimal frozenAmount;

	/**
	 * 预开票金额
	 */
	private java.math.BigDecimal invoicePreAmount;

}
