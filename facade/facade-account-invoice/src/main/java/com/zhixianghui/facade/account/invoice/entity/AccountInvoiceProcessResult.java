package com.zhixianghui.facade.account.invoice.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.BeanUtil;
import lombok.Data;

/**
 * 发票账户账务处理结果表
 * <AUTHOR>
 */
@Data
public class AccountInvoiceProcessResult extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 账务处理流水号
	 */
	private String accountProcessNo;

	/**
	 * 处理结果(1=成功 -1=失败)
	 */
	private Integer processResult;

	/**
	 * 错误码(0=无异常 1=系统异常 其他=具体业务异常)
	 */
	private Integer errorCode;

	/**
	 * 审核阶段(1=不审核 2=待审核 3=已审核)
	 */
	private Integer auditStage;

	/**
	 * 回调阶段(1=待回调 2=已回调 3=不回调)
	 */
	private Integer callbackStage;

	/**
	 * 备注/异常描述
	 */
	private String remark;

	/**
	 * 是否来自异步账务处理(1=是 -1=否)
	 */
	private Integer isFromAsync;

	/**
	 * 账务请求数据(JSON格式)
	 */
	private Object requestDto;

	/**
	 * 账务处理的数据(JSON LIST格式)
	 */
	private Object processDtoList;

	//region 非数据库字段
	/**
	 * 数据是否在历史表
	 */
	private boolean inHistory;
	//endregion
	
	/**
	 * 根据账务处理结果历史创建一个账务处理结果实体
	 *
	 * @param history 账务处理结果历史
	 * @return .
	 */
	public static AccountInvoiceProcessResult newFromHistory(AccountInvoiceProcessResultHistory history) {
		if (history == null) {
			return null;
		} else {
			AccountInvoiceProcessResult result = new AccountInvoiceProcessResult();
			BeanUtil.copyProperties(history, result);
			result.inHistory = true; //赋值需要在copyProperties后面
			return result;
		}
	}
}
