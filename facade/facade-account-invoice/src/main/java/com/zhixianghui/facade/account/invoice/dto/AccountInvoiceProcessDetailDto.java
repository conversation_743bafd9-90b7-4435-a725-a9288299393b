package com.zhixianghui.facade.account.invoice.dto;

import com.zhixianghui.common.statics.enums.account.AccountDebitCommitStageEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AccountInvoiceProcessDetailDto implements Serializable {
    private static final long serialVersionUID = -1636546569587411315L;

    /**
     * 账户编号
     */
    private String accountNo;
    /**
     * 用工企业编号
     */
    private String employerMchNo;
    /**
     * 代征主体编号
     */
    private String mainstayMchNo;

    private String trxNo;
    private Integer processType;
    /**
     * 变动金额
     */
    private BigDecimal alterAmount;
    /**
     * 发票余额变动金额
     */
    private BigDecimal alterInvoiceAmount;
    /**
     * 冻结金额变动金额
     */
    private BigDecimal alterFrozenAmount;
    /**
     * 扣款确认状态：1：待确认，2：确认扣款，3：确认退回，4：无须确认【若不是扣款的账务明细，设置为4】
     * {@link AccountDebitCommitStageEnum}
     */
    private Integer debitCommitStage;
}
