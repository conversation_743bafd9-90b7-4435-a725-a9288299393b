package com.zhixianghui.facade.account.invoice.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 发票账户待账务处理归档表
 */
@Data
public class AccountInvoiceProcessPendingHistory  extends BaseEntity {

    /**
     * 修改时间
     */
    private java.util.Date modifyTime;

    /**
     * 账务处理流水号
     */
    private String accountProcessNo;

    /**
     * 处理阶段(1=待处理 2=处理中 3=已处理)
     */
    private Integer processStage;

    /**
     * 账务处理数据的唯一编码
     */
    private String dataUnqKey;

    /**
     * 账务请求数据(JSON格式)
     */
    private String requestDto;

    /**
     * 账务处理数据(JSON LIST格式)
     */
    private String processDtoList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 迁移时间
     */
    private java.util.Date migrateTime;

    //columns END
}
