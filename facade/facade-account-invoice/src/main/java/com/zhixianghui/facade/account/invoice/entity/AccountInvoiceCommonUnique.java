package com.zhixianghui.facade.account.invoice.entity;

import com.zhixianghui.common.statics.annotations.PK;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AccountInvoiceCommonUnique implements Serializable {
    private static final long serialVersionUID = 43493635241245483L;

    @PK
    private Long id;
    //创建时间
    private Date createTime = new Date();
    private String uniqueKey;
}
