package com.zhixianghui.facade.account.invoice.entity;

import com.zhixianghui.common.statics.annotations.PK;
import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.account.AccountDebitCommitStageEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 发票账户账务处理明细归档表
 */
@Data
public class AccountInvoiceProcessDetailHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 账户编号
     */
    private String accountNo;
    /**
     * 用工企业商户编号
     */
    private String employerMchNo;

    /**
     * 代征主体商户编号
     */
    private String mainstayMchNo;

    /**
     * 发票类目编号
     */
    private String invoiceCategoryCode;


    /**
     * 发票余额
     */
    private java.math.BigDecimal invoiceAmount;
    /**
     * 冻结余额
     */
    private BigDecimal frozenAmount;

    /**
     * 变动金额(交易金额)
     */
    private java.math.BigDecimal alterAmount;

    /**
     * 发票余额变动金额
     */
    private java.math.BigDecimal alterInvoiceAmount;
    /**
     * 变动冻结余额
     */
    private java.math.BigDecimal alterFrozenAmount;

    /**
     * 账务处理流水号
     */
    private String accountProcessNo;

    /**
     * 交易流水号
     */
    private String trxNo;

    /**
     * 商户订单号
     */
    private String mchTrxNo;

    /**
     * 订单交易时间
     */
    private java.util.Date trxTime;

    /**
     * 业务类型
     * @see com.zhixianghui.common.statics.enums.product.ProductNoEnum
     */
    private String productNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 账务处理类型
     */
    private Integer processType;

    /**
     * 扣款确认状态：1：待确认，2：确认扣款，3：确认退回，4：无须确认【若不是扣款的账务明细，设置为4】
     * {@link AccountDebitCommitStageEnum}
     */
    private Integer debitCommitStage;

    /**
     * 附加信息
     */
    private String extraInfo;

    /**
     * 迁移时间
     */
    private java.util.Date migrateTime;
}
