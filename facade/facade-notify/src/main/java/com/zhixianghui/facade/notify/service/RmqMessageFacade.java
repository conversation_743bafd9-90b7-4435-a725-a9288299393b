package com.zhixianghui.facade.notify.service;


import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.notify.entity.RmqMessageRecord;

import java.util.List;
import java.util.Map;

/**
 * @description: RocketMq消息管理
 * @author: xingguang li
 * @created: 2020/09/09 11:07
 */
public interface RmqMessageFacade {

    void addMessage(RmqMessageRecord mqMessageRecord);

    PageResult<List<RmqMessageRecord>> listRmqMessagePage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 补发消息
     * @param id 消息数据库id
     * @return 布尔
     */
    boolean reissueMessage(Long id);

    /**
     * 批量补发
     * @param idList 消息id列表
     */
    void batchReissueMessage(List<Long> idList);

    void batchReissueMessage(Map<String,Object> param);
}
