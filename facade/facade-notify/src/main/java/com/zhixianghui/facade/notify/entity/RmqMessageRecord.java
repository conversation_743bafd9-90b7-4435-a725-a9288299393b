package com.zhixianghui.facade.notify.entity;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: mq消息记录
 * @author: xingguang li
 * @created: 2020/09/09 11:39
 */
public class RmqMessageRecord implements Serializable {
    private static final long serialVersionUID = 1362597754860834794L;

    private Long id;

    private Integer version;

    private Date createTime = new Date();

    private Date createDate = new Date();

    /**
     * 消息topic名称
     * {@link MessageMsgDest}
     */
    private String topic;

    /**
     * mq返回id
     */
    private String msgId;

    /**
     * 消息的tags属性
     */
    private String tags;

    /**
     * 消息的keys属性
     */
    private String keys;

    /**
     * 消息体
     */
    private String message;

    /** 商户编号 **/
    private String merchantNo;

    /** 流水号 **/
    private String trxNo;

    /**
     * 通知类型
     * {@link NotifyTypeEnum#getValue()}
     */
    private Integer notifyType;

    public RmqMessageRecord() {
    }

    public RmqMessageRecord(String topic, String tags, String message, String merchantNo, String trxNo, Integer notifyType) {
        this.topic = topic;
        this.tags = tags;
        this.message = message;
        this.merchantNo = merchantNo;
        this.trxNo = trxNo;
        this.notifyType = notifyType;
        this.version = 1;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getMsgId() {
        return this.msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getKeys() {
        return keys;
    }

    public void setKeys(String keys) {
        this.keys = keys;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getTrxNo() {
        return trxNo;
    }

    public void setTrxNo(String trxNo) {
        this.trxNo = trxNo;
    }

    public Integer getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(Integer notifyType) {
        this.notifyType = notifyType;
    }
}
