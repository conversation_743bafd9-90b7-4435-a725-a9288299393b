package com.zhixianghui.facade.notify.entity;

import java.io.Serializable;

/**
 * 唯一性
 */
public class Unique implements Serializable {

	private static final long serialVersionUID = -2139352469707568933L;
	private long id;

	/**
	 * 唯一键
	 */
	private String uniqueKey;


	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getUniqueKey() {
		return uniqueKey;
	}

	public void setUniqueKey(String uniqueKey) {
		this.uniqueKey = uniqueKey;
	}
}
