package com.zhixianghui.facade.notify.enums;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @description: 所有通知类型
 * @author: xingguang li
 * @created: 2020/11/02 11:50
 */
@AllArgsConstructor
@Getter
@ToString
public enum NotifyTypeEnum {

    /**
     * 报备回调
     */
    REPORT_RECEIVE("报备回调",1, MessageMsgDest.TOPIC_REPORT_ASYNC),

    /**
     * 交易通知
     */
    TRADE_NOTIFY("交易通知",2, MessageMsgDest.TOPIC_TRADE_ASYNC),
    /*
     *
     */
    TRADE_CERTIFICATE("交易证书",2, MessageMsgDest.TOPIC_TRADE_CERTIFICATE),

    /**
     * 审批通知
     */
    APPROVAL_NOTIFY("审批通知",3, MessageMsgDest.TOPIC_APPROVAL_ASYNC),

    /**
     * 订单完成
     */
    ORDER_COMPLETE("订单完成",4, MessageMsgDest.TOPIC_ORDER_COMPLETE),

    /**
     * 通知商户
     */
    MERCHANT_NOTIFY("通知商户",5, MessageMsgDest.TOPIC_NOTIFY_MERCHANT),

    /**
     * 签约通知
     */
    SIGN_NOTIFY("签约通知",6, MessageMsgDest.TOPIC_SIGN_ASYNC),
    /**
     * 合伙人分润处理完成
     */
    AGENT_COMPLETE("合伙人分润完成通知",7, MessageMsgDest.TOPIC_AGENT_ORDER_COMPLETE),

    MERCHANT_CHANGE("商户修改基本信息",8,MessageMsgDest.TOPIC_MERCHANT_FULL_EDIT),

    ALI_CALLBACK_MSG("支付宝回调消息",9,MessageMsgDest.TOPIC_ALIPAY_CALLBACK),

    ALIPAY_REFUND_MSG("支付宝退款消息",10,MessageMsgDest.TOPIC_ALIPAY_REFUND),

    ALI_DELAY_SEARCH("延迟查询报备状态消息",11,MessageMsgDest.TOPIC_ALIPAY_REPORT_DELAY),

    ALI_REPORT_REPEAT("支付宝重复报备消息",12,MessageMsgDest.TOPIC_ALIPAY_REPORT_REPEAT),

    RYSC_SYNCHRONIZE_PAYMENT_DATA("奔奔代征主体同步支付数据", 13,  MessageMsgDest.TOPIC_SYNCHRONIZE_PAYMENT_DATA),

    CREATE_RELATION_FLOW("发起建立代征关系审批流",14,MessageMsgDest.TOPIC_CREATE_RELATION_FLOW),

    WX_WORK_ROBOT("微信机器人",15,MessageMsgDest.TOPIC_WX_WORK_ROBOT),

    ALIPAY_BUSY_RETRY("支付宝系统繁忙重试",16,MessageMsgDest.TOPIC_ALIPAY_BUSY),

    AUTO_REPORT_AFTER_AUTH("认证之后自动报备",17,MessageMsgDest.TOPIC_AUTO_REPORT),

    INVOICE_PROCESS("发票账务处理", 18, MessageMsgDest.TOPIC_INVOICE_PROCESS),

    APPLY_NEW_MERCHANT("创建商户", 19, MessageMsgDest.TOPIC_APPLY_NEW_MERCHANT),
    AGENT_QUOTE_EDIT("合伙人报价单编辑", 20, MessageMsgDest.TOPIC_AGENT_QUOTE_EDIT),
    AGENT_MAININFO_EDIT("合伙人主体信息编辑", 21, MessageMsgDest.TOPIC_AGENT_MAININFO_EDIT),
    AGENT_BANKACCT_EDIT("合伙人主体信息编辑", 22, MessageMsgDest.TOPIC_AGENT_BANKACCT_EDIT),
    MERCHANT_QUPTE_ADD("创建报价单",23,MessageMsgDest.TOPIC_ADD_MERCHANT_QUOTE),
    AGENT_SET_SELLER("合伙人销售设置",24,MessageMsgDest.TOPIC_AGENT_SET_SELLER),
    AGENT_SET_INVITER("合伙人销售设置",25,MessageMsgDest.TOPIC_AGENT_SET_INVITER),
    AGENT_SET_PRINCIPAL("合伙人负责人设置",26,MessageMsgDest.TOPIC_AGENT_SET_PRINCIPAL),
    AGENT_QUOTE_DEL("删除合伙人报价单",27,MessageMsgDest.TOPIC_AGENT_QUOTE_DEL),
    AGENT_BATCH_SET_INVITER_SELLER("批量设置合伙人销售邀请人",28,MessageMsgDest.TOPIC_AGENT_BATCH_SET_INVITER_SELLER),
    YISHUI_NOTIFY("易税供应商相关的消息",29,MessageMsgDest.TOPIC_YISHUI),
    YISHUI_CALLBACK_NOTIFY("易税供应商回调",30,MessageMsgDest.TOPIC_YISHUI_CALL_BACK),
    ALIPAY_BILL_QUERY("支付宝回单查询",31,MessageMsgDest.TOPIC_ALIPAY_BILL_QUERY),
    AGENT_BASE_INFO_EDIT("合伙人基本信息编辑",32,MessageMsgDest.TOPIC_AGENT_BASE_EDIT),
    WX_AMOUNT_CHANGE("微信账户变动",33,MessageMsgDest.TOPIC_WX_AMOUNT_CHANGE),
    WX_CHANNEL_REPORT("微信报备",34,MessageMsgDest.TOPIC_WX_REPORT),

    ORDER_DATA_SYNC_CK("同步订单数据到ClickHouse",35, MessageMsgDest.TOPIC_ORDER_DATA_SYNC_CK),
    WX_WITHDRAW("微信提现",36,MessageMsgDest.TOPIC_WX_WITHDRAW),

    CMB_BUSY_RETRY("招行系统繁忙重试",37,MessageMsgDest.TOPIC_CMB_RETRY),
    CMB_CALL_BACK_QUERY("招行反查订单",38,MessageMsgDest.TOPIC_CMB_CALLBACK_QUERY),
    CMB_REFUND_MSG("招行退款消息",39,MessageMsgDest.TOPIC_CMB_REFUND),
    CMB_CALL_WITHDRAW_BACK_QUERY("招行提现反查订单",40,MessageMsgDest.TOPIC_CMB_WITHDRAW_CALLBACK_QUERY),

    RISK_SYNC("风控名单同步",41,MessageMsgDest.TOPIC_RISK_NAME_SYNC),

    CMB_BILL("下载保存招行回单", 42, MessageMsgDest.TAG_CMB_BILL_SAVE),

    HANGUP_MERCHANT_NOTIFY("挂单通知商户", 43, MessageMsgDest.TOPIC_HANGUP_MERCHANT_NOTIFY),

    JOB_WORKER_FILE("雇员文件上传", 44, MessageMsgDest.TOPIC_UPLOAD_JOB_FILE),

    RECHARGE_BALANCE("充值余额", 45, MessageMsgDest.TOPIC_RECHARGE_BALANCE),

    /**
     * 交易通知
     */
    TRADE_NOTIFY_OFFLINE("外部订单交易通知",46, MessageMsgDest.TOPIC_TRADE_CKH_OFFLINE),

    CMB_RECHARGE_BILL("招行充值订单回单下载", 47, MessageMsgDest.TOPIC_CMB_RECHARGE_BILL),

    CREATE_RELATION_ANNO("创建代征关系",48,MessageMsgDest.TOPIC_CREATE_RELATION),
    /**
     * 交易通知
     */
    TRADE_CMB_BATCH_NOTIFY("交易通知",49, MessageMsgDest.TOPIC_TRADE_CMB_ASYNC),

    TRADE_ZFT_PAY_RESULT_NOTIFY("直付通支付结果通知",50,MessageMsgDest.TOPIC_ZFT_PAY_RESULT_CALLBACK),
    TRADE_MOCK_SUCCESS_CALL_BACK_NOTIFY("模拟发放成功结果通知",51,MessageMsgDest.TOPIC_MOCK_SUCCESS_CALL_BACK),
    AGENT_FIRST_LOGIN("合伙人小程序首次登录",52,MessageMsgDest.TOPIC_AGENT_GET_OR_REGISTER),
    TOPIC_SYNC_OUT_ORDER("外部订单确认处理队列",53,MessageMsgDest.TOPIC_SYNC_OUT_ORDER),


    /**
     * 入账通知
     */
    INCOME_NOTIFY("入账通知",54, MessageMsgDest.TOPIC_INCOME_ASYNC),

    /***
     * 君享汇单笔出款通知
     */
    JXH_SINGLEPAY_NOTIFY("出款通知",54, MessageMsgDest.TOPIC_JXH_SINGLEPAY_ASYNC),

    JXH_WITHDRAW("君享汇提现",55,MessageMsgDest.TOPIC_JXH_WITHDRAW),
    ;

    /** 枚举值 */
    private int value;
    /** 描述 */
    private String desc;
    /**
     * topic名字
     * {@link MessageMsgDest}
     */
    private String topic;
    /** 消息VO类 */
    private Class<?> voClass;

    private NotifyTypeEnum(String desc, int value, String topic) {
        this(value, desc, topic, null);
    }
}
