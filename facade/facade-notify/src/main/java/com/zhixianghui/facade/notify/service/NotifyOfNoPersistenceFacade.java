package com.zhixianghui.facade.notify.service;

import com.zhixianghui.common.statics.dto.rmq.MsgDto;

import java.util.List;
import java.util.function.Consumer;

/**
 *@desc mq通知服务，不需要持久化
 * <AUTHOR> li
 * @date 2020-09-18
 */
public interface NotifyOfNoPersistenceFacade {

    /**
     * 同步发送单个消息
     * @param msg
     * @return
     */
    boolean sendOne(MsgDto msg);

    /**
     * 带回调的发送单个消息
     * @param msg
     * @param onFail
     */
    void sendOne(MsgDto msg, Consumer<MsgDto> onFail);

    /**
     * 不等结果的发送单个结果
     * @param msg
     */
    void sendOneWay(MsgDto msg);

    /**
     *不等结果的发送单个结果，有回调处理
     * @param msg
     * @param callback 消息发送成功或失败之后的回调函数，如果不需要处理回调则设置为null即可
     */
    void sendOneAsync(MsgDto msg, Consumer<MsgDto> callback);

    /**
     * 发送批量消息，适合同一个业务事件有多个业务系统需要做不同业务处理的时候使用
     * @param destination 目的地，如果只有topic，则只传topic名称即可，如果还有tags，则拼接成 topic:tags 的形式
     * @param msgList
     * @return
     */
    boolean sendBatch(String destination, List<? extends MsgDto> msgList);

    /**
     * 发送批量消息，一批次的消息只能发送给同一个topic，但tags可以不一样
     * @see #sendBatch(String, List)
     * @param msgList
     * @return
     */
    boolean sendBatch(List<? extends MsgDto> msgList);

    /**
     * 发送事务消息
     * @param msg
     * @return
     */
    boolean sendTrans(MsgDto msg);
}
