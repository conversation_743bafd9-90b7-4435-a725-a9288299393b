package com.zhixianghui.facade.notify.service;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;

/**
 *@desc mq通知服务
 * <AUTHOR> li
 * @date 2020-09-18
 */
public interface NotifyFacade {

    /**
     * 同步发送单个消息，适用于交易过程中发送
     * @param topic {@link MessageMsgDest}
     * @param merchantNo 商编
     * @param trxNo 流水号
     * @param notifyType 通知类型 {@link NotifyTypeEnum#getValue()}
     * @param tags mq的tags
     * @param msg 消息体
     * @return
     */
    boolean sendOne(String topic, String merchantNo, String trxNo, int notifyType, String tags, String msg);

    /**
     * 发送单个消息，适用于单纯发mq消息
     * @param topic {@link MessageMsgDest}
     * @param notifyType 通知类型 {@link NotifyTypeEnum#getValue()}
     * @param tags
     * @param msg
     * @return
     */
    boolean sendOne(String topic, int notifyType, String tags, String msg);

    /**
     * 同步发送单个消息，适用于交易过程中发送
     * @param topic {@link MessageMsgDest}
     * @param merchantNo 商编
     * @param trxNo 流水号
     * @param notifyType 通知类型 {@link NotifyTypeEnum#getValue()}
     * @param tags mq的tags
     * @param msg 消息体
     * @param delayLevel 延迟的级别，{@link MsgDelayLevelEnum#getValue()}
     * @return
     */
    boolean sendOne(String topic, String merchantNo, String trxNo, int notifyType, String tags, String msg, int delayLevel);

    /**
     * 延时发送消息
     * @param topic
     * @param notifyType
     * @param tags
     * @param msg
     * @param delayLevel
     * @return
     */
    boolean sendOne(String topic,int notifyType,String tags,String msg,int delayLevel);
}
