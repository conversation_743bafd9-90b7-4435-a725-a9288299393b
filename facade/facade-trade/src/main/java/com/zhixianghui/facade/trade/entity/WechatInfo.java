package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微信信息表
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-02-27
 */
@Data
@TableName(value = "tbl_wechat_info")
@EqualsAndHashCode(callSuper = false)
public class WechatInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 微信openid
     */
    private String wxOpenId;

    /**
     * 小程序openid
     */
    private String miniOpenId;

    /**
     * 微信平台unioid
     */
    private String wxUnionid;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 最近登录时间
     */
    private Date lastLoginAt;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 公众号关注时间
     */
    private Date wxSubscribeAt;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 在微信授权手机号登录时关联该字段
     */
    private String userNo;

    /**
     * appid
     */
    private String appId;

}
