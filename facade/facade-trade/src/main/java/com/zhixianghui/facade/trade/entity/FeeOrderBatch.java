package com.zhixianghui.facade.trade.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FeeOrderBatch implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer version;

    /**
     * 账单批次号
     */
    private String feeBatchNo;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8" )
    private Date createDate;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品编号
     */
    private String productName;

    /**
     * 发放批次数
     */
    private Integer orderBatchCount;

    /**
     * 订单名称数
     */
    private Integer orderItemCount;

    /**
     * 成功笔数
     */
    private Integer successCount;

    /**
     * 失败笔数
     */
    private Integer failCount;

    /**
     * 成功任务金额
     */
    private BigDecimal taskAmount;

    /**
     * 成功实发金额
     */
    private BigDecimal orderNetAmount;

    /**
     * 个税合计
     */
    private BigDecimal taxAmount;

    /**
     * 服务费合计
     */
    private BigDecimal feeAmount;

    /**
     * 电子回单url
     */
    private String receiptUrl;

    /**
     * 结算模式
     */
    private Integer balancedMode;

    /**
     * 账单状态
     */
    private Integer status;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 订单来源
     */
    private Integer feeSource;
}
