package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@ToString
public enum IndividualProxyOrderStatus {

    COMPLETED(100,"已寄出"),
    WAITE_PAY(200, "待付款"),
    WAITE_INVOICE(300,"待开票"),
    WAITE_POST(400,"待寄出"),
    CANCELLED(500, "已取消"),
    ;
    private final int value;
    private final String desc;
}

