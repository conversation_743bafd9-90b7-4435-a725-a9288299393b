package com.zhixianghui.facade.trade.vo;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月05日 10:07:00
 */
@Data
public class ChangesFundsVo implements Serializable {


    private static final long serialVersionUID = -2521896235171596036L;
    private Long id;

    @Version
    private Integer version = 0;

    /**
     * 唯一键
     */
    private String logKey;

    /**
     * 所属商户编号
     */
    private String mchNo;

    /**
     * 所属商户名称
     */
    private String mchName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 资金变动类型
     */
    private Integer amountChangeType;

    /**
     * 变动金额
     */
    private BigDecimal amount;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 变动前金额
     */
    private BigDecimal beforeAmount;

    /**
     * 变动前冻结金额
     */
    private BigDecimal beforeFrozenAmount;

    /**
     * 变动后金额
     */
    private BigDecimal afterAmount;

    /**
     * 变动后冻结金额
     */
    private BigDecimal afterFrozenAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private String operator;


}
