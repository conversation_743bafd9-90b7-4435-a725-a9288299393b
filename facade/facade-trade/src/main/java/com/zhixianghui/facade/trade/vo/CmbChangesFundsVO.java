package com.zhixianghui.facade.trade.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024/8/29 11:17
 */
@Data
public class CmbChangesFundsVO implements Serializable {
    private static final long serialVersionUID = 175258793645167747L;

    /**
     * ID
     */
    private Long id;

    /**
     * 唯一健
     */
    private String logKey;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 所属商户编号
     */
    private String mchNo;

    /**
     * 所属商户名称
     */
    private String mchName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 批次号
     */
    private String platBatchNo;

    /**
     * 资金变动类型
     */
    private Integer amountChangeType;

    /**
     * 变动金额
     */
    private BigDecimal amount;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 变动前金额
     */
    private BigDecimal beforeAmount;

    /**
     * 变动前冻结金额
     */
    private BigDecimal beforeFrozenAmount;

    /**
     * 变动后金额
     */
    private BigDecimal afterAmount;

    /**
     * 变动后冻结金额
     */
    private BigDecimal afterFrozenAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private String operator;


}
