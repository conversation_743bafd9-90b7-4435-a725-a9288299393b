package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.dto.FeeOrderBatchVo;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.entity.FeeOrderItem;
import com.zhixianghui.facade.trade.vo.FeeOrderVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-07-05
 */
public interface FeeOrderBatchFacade {

    PageResult listPage(FeeOrderVo feeOrderVo, PageParam pageParam);

    Map<String, Object> getStatistics(FeeOrderVo feeOrderVo);

    FeeOrderBatchVo selectOrderItem(String feeBatchNo);

    Map<String,Object> payFee(String feeItemNo);


    FeeOrderItem getByItemNo(String feeItemNo);

    Map<String,Object> getOffLineItem(String feeBatchNo);

    void complete(FeeOrderBatchVo feeOrderBatchVo);

    FeeOrderBatch getByBatchNo(String feeBatchNo);

    List<Map<String, Object>> export(Map<String, Object> param);

    boolean isExistNotPay(Map<String, Object> paramMap);

    void update(FeeOrderBatch feeOrderBatch);

    /**
     *
     * @param mchNo 商户编号
     * @param mainstayNo 供应商编号
     * @param completeDate 完成时间 yyyy-MM-dd
     * @param feeSource {@link com.zhixianghui.facade.trade.enums.FeeSourceEnum}
     * @return
     */
    boolean isGenerateFee(String mchNo, String mainstayNo, Date completeDate,Integer feeSource);

    boolean hasNotpayFee(String startTime, String endTime, String employerNo, String mainstayNo, Integer feeSource);
}
