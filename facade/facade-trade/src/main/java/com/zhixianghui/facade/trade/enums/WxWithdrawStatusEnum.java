package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName WxWithdrawStatusEnum
 * @Description TODO
 * @Date 2022/1/12 11:42
 */
@AllArgsConstructor
public enum WxWithdrawStatusEnum {

    CREATE_SUCCESS("CREATE_SUCCESS","受理成功"),

    SUCCESS("SUCCESS","提现成功"),

    FAIL("FAIL","提现失败"),

    REFUND("REFUND","提现退票"),

    CLOSE("CLOSE","关单"),

    INIT("INIT","业务单已创建"),
    ;

    private String value;

    private String desc;

    public static WxWithdrawStatusEnum getEnum(String value) {
        return Arrays.stream(values())
                .filter(p -> p.value.equals(value))
                .findFirst()
                .orElse(null);
    }

    public String getDesc() {
        return desc;
    }

    public String getValue() {
        return value;
    }
}
