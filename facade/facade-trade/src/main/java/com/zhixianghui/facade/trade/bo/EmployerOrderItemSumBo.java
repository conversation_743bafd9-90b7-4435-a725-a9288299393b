package com.zhixianghui.facade.trade.bo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
public class EmployerOrderItemSumBo implements Serializable {
    private static final long serialVersionUID = -8974913885730545552L;

    private OrderItemSumBo sumBo;
    private OrderItemSumBo successItemSumBo;
    private OrderItemSumBo failItemSumBo;
    private OrderItemSumBo proccessingSumBo;
}
