package com.zhixianghui.facade.trade.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

@Data
public class UpdateInvoiceDetailDto implements Serializable {

    private static final long serialVersionUID = 8491371593700637399L;
    /**
     * 平台流水号
     */
    private String idCardNo;

    /**
     * 发票流水号
     */
    private String invoiceTrxNo;
    /**
     * 开票状态
     */
    private Integer invoiceStatus;
    /**
     * 发票文件地址列表
     */
    private List<String> invoiceFileUrls;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    @Length(max = 200, message = "异常原因反馈长度不能超过200")
    private String errorDesc;
    // 快递单号
    @Length(max = 200, message = "快递单号长度不能超过200")
    private String expressNo;
}
