package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RechargeTypeEnum {


    TRANSFER_RECHARGE(1, "转账充值"),
    BANK_NET_RECHARGE(2, "网银充值"),
    WX_OFFLINERECHARGE(3,"企业直收");

    private final Integer code;
    private final String message;

    public static RechargeTypeEnum getEnum(int code) {
        return Arrays.stream(values()).filter(p -> p.code == code).findFirst().orElse(null);
    }
}
