package com.zhixianghui.facade.trade.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class InvoiceRecordListQueryDto {
    /**
     * 创建开始时间
     */
    private String createTimeBegin;

    /**
     * 创建结束时间
     */
    private String createTimeEnd;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 供应商编号
     */
    @JSONField(name = "mainstayMchNo")
    private String mainstayNo;

    /**
     * 订单来源	1、平台订单；2、外部订单
     */
    @JSONField(name = "source")
    private Integer invoiceSource;

    /**
     * 发票类型	1、增值税普通发票；2、增值税专用发票
     */
    private Integer invoiceType;


    /**
     * 当前页码
     */
    private Integer currentPage=1;
}
