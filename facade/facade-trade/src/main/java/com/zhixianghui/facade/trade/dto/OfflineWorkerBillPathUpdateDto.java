package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class OfflineWorkerBillPathUpdateDto implements Serializable {
    private static final long serialVersionUID = 1848276116571623375L;

    @NotBlank(message = "平台流水号不能为空")
    private String platTrxNo;
    @NotNull(message = "回单列表不能为空")
    @NotEmpty(message = "回单列表不能为空")
    private List<String> workerBillFilePath;
}
