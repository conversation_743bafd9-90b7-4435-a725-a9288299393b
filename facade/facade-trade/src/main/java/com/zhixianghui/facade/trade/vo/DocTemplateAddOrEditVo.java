package com.zhixianghui.facade.trade.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/6 17:35
 */
@Data
@Accessors(chain = true)
public class DocTemplateAddOrEditVo implements Serializable {

    protected String docTemplateId;
    protected String docTemplateCreateUrl;

    protected String docTemplateCreateLongUrl;
}
