package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.ValidateUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * 易税人员表
 */
@Data
@TableName(value = "tbl_yishui_users")
@Accessors(chain = true)
public class YishuiUsers implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 身份证号码
     */
    @TableField(value = "cer_code")
    private String cerCode;

    @TableField(value = "cer_code_md5")
    private String cerCodeMd5;

    /**
     * （预留）手机号
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 银行卡号或支付宝账号
     */
    @TableField(value = "account_no")
    private String accountNo;

    @TableField(value = "account_no_md5")
    private String accountNoMd5;

    /**
     * 诚信纳税承诺书、税务办理授权委托书
     */
    @TableField(value = "contract_img")
    private String contractImg;

    /**
     * 签约ID
     */
    @TableField(value = "enterprise_professional_facilitator_id")
    private String enterpriseProfessionalFacilitatorId;

    /**
     * 人员ID
     */
    @TableField(value = "professional_id")
    private String professionalId;

    /**
     * 人员编号
     */
    @TableField(value = "professional_sn")
    private String professionalSn;

    /**
     * 通道编号
     */
    @TableField(value = "channel_type")
    private Integer channelType;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 人员手写签名照片
     */
    @TableField(value = "sign_img")
    private String signImg;

    /**
     * 自由职业合作服务协议
     */
    @TableField(value = "protocol_img")
    private String protocolImg;

    @TableField(value = "encrypt_key_id")
    private Integer encryptKeyId;

    public void setCerCodeEncrypt(String cerCode) {
        if(StringUtils.isNotEmpty(cerCode)){
            this.cerCode = AESUtil.encryptECB(cerCode, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(cerCode);
            this.cerCodeMd5 = md5Str==null?"":md5Str;
        }
    }

    public void setAccountNoEncrypt(String accountNo) {
        if(StringUtils.isNotEmpty(accountNo)){
            this.accountNo = AESUtil.encryptECB(accountNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(accountNo);
            this.accountNoMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getCertCodeDecrypt() {
        return AESUtil.decryptECB(this.cerCode, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public String getAccountNoDecrypt() {
        return AESUtil.decryptECB(this.accountNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public String getCertCodeDesensitize(){
        String certCodeDecrypt = getCertCodeDecrypt();
        if(StringUtils.isBlank(certCodeDecrypt)){
            return "";
        }
        return DesensitizeUtil.handleIdNumOrBankCardNo(certCodeDecrypt);
    }

    public String getAccountNoDesensitize(){
        //收款账号可能是银行卡、手机、邮箱
        String accountNoDecrypt = getAccountNoDecrypt();
        if(StringUtils.isBlank(accountNoDecrypt)){
            return "";
        }
        if(StringUtils.isNotEmpty(accountNoDecrypt)){
            if(ValidateUtil.isMobile(accountNoDecrypt)){
                return DesensitizeUtil.handleMobile(accountNoDecrypt);
            } else if(ValidateUtil.isEmail(accountNoDecrypt)){
                return DesensitizeUtil.handleEmailDesenCenter(accountNoDecrypt);
            } else{
                return DesensitizeUtil.handleIdNumOrBankCardNo(accountNoDecrypt);
            }
        }
        return null;
    }

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_NAME = "name";

    public static final String COL_CER_CODE = "cer_code";

    public static final String COL_CER_CODE_MD5 = "cer_code_md5";

    public static final String COL_MOBILE = "mobile";

    public static final String COL_ACCOUNT_NO = "account_no";

    public static final String COL_ACCOUNT_NO_MD5 = "account_no_md5";

    public static final String COL_CONTRACT_IMG = "contract_img";

    public static final String COL_ENTERPRISE_PROFESSIONAL_FACILITATOR_ID = "enterprise_professional_facilitator_id";

    public static final String COL_PROFESSIONAL_ID = "professional_id";

    public static final String COL_PROFESSIONAL_SN = "professional_sn";

    public static final String COL_CHANNEL_TYPE = "channel_type";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_SIGN_IMG = "sign_img";

    public static final String COL_PROTOCOL_IMG = "protocol_img";

    public static final String COL_ENCRYPT_KEY_ID = "encrypt_key_id";
}
