package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.OrderItemFail;
import com.zhixianghui.facade.trade.vo.AuthInfoVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单明细表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
public interface OrderItemFacade {
    /**
     * 批量添加订单明细
     * @param itemList 订单明细列表
     */
    void batchInsert(List<OrderItem> itemList);

    /**
     * 分页查询订单明细
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return 分页结果
     */
    PageResult<List<OrderItem>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 根据流水号查询
     * @param platTrxNo 流水号
     * @return 订单明细
     */
    OrderItem getByPlatTrxNo(String platTrxNo);

    /**
     * 统计满足条件订单明细总条数
     * @param paramMap 条件
     * @return 总条数
     */
    Long countOrderItem(Map<String, Object> paramMap);

    /**
     * 统计订单明细
     * @param beanToMap
     * @return
     */
    OrderItemSumBo sumOrderItem(Map<String, Object> beanToMap);

    public Long countOrderItemByMchOrderNo(String employerNo, String mchOrderNo);

    void save(OrderItemFail itemFail);

    List<OrderItemFail> getByPlatBatchNo(String platBatchNo);

    PageResult<List<OrderItemFail>> pageByPlatBatchNo(String platBatchNo, PageParam pageParam);

    Long countOrder(Map<String, Object> param);

    Map<String, BigDecimal> mapAmountWithMch(Map<String, Object> nowDateMap);

    BigDecimal sumOrderItemWaitInvoiceAmount(Map<String, Object> paramMap);

    AuthInfoVo getAuthInfo(OrderItem orderItem);

    void authInit(String platBatchNo);

    void update(OrderItem item);

    void rejectOrderItem(String platTrxNo, String loginName);

    OrderItem getByMchOrderNo(String mchOrderNo);

    List<OrderItem> listBy(Map<String, Object> paramMap);
}
