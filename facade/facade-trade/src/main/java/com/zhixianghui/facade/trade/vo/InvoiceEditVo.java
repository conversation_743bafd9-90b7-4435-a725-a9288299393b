package com.zhixianghui.facade.trade.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 **/
@Data
public class InvoiceEditVo implements Serializable {
    /**
     * 流水号
     */
    @NotEmpty(message = "流水号不能为空")
    private String trxNo;
    /**
     * 发票类型
     * @see com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum
     */
    @NotNull(message = "发票类型不能为空")
    private Integer invoiceType;
    /**
     * 发票类目编码
     */
    @NotEmpty(message = "发票类目编码不能为空")
    private String invoiceCategoryCode;
    /**
     * 发票类目名称
     */
    @NotEmpty(message = "发票类目名称不能为空")
    private String invoiceCategoryName;
    /**
     * 备注
     */
    @Length(max = 200,message = "备注长度不能超过200")
    private String remark;
}
