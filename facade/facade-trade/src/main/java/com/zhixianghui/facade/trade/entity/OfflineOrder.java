package com.zhixianghui.facade.trade.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
    * 订单批次表
    */
@Data
@TableName(value = "tbl_offline_order")
public class OfflineOrder implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    @Version
    private Integer version;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 确认发放时间
     */
    @TableField(value = "CONFIRM_TIME")
    private Date confirmTime;

    /**
     * 完成时间
     */
    @TableField(value = "COMPLETE_TIME")
    private Date completeTime;

    /**
     * 商户批次号
     */
    @TableField(value = "MCH_BATCH_NO")
    private String mchBatchNo;

    /**
     * 批次名
     */
    @TableField(value = "BATCH_NAME")
    private String batchName;

    /**
     * 平台批次号
     */
    @TableField(value = "PLAT_BATCH_NO")
    private String platBatchNo;

    /**
     * 产品编号
     */
    @TableField(value = "PRODUCT_NO")
    private String productNo;

    /**
     * 产品名称
     */
    @TableField(value = "PRODUCT_NAME")
    private String productName;

    /**
     * 任务id
     */
    @TableField(value = "JOB_ID")
    private String jobId;

    /**
     * 任务名称
     */
    @TableField(value = "JOB_NAME")
    private String jobName;

    /**
     * 用工企业编号
     */
    @TableField(value = "EMPLOYER_NO")
    private String employerNo;

    /**
     * 用工企业名称
     */
    @TableField(value = "EMPLOYER_NAME")
    private String employerName;

    /**
     * 代征主体编号
     */
    @TableField(value = "MAINSTAY_NO")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @TableField(value = "MAINSTAY_NAME")
    private String mainstayName;

    /**
     * 批次状态
     */
    @TableField(value = "BATCH_STATUS")
    private Integer batchStatus;

    /**
     * 自由职业者服务编号
     */
    @TableField(value = "WORK_CATEGORY_CODE")
    private String workCategoryCode;

    /**
     * 自由职业者服务名称
     */
    @TableField(value = "WORK_CATEGORY_NAME")
    private String workCategoryName;

    /**
     * 自由职业者服务描述
     */
    @TableField(value = "SERVICE_DESC")
    private String serviceDesc;

    /**
     * 请求笔数
     */
    @TableField(value = "REQUEST_COUNT")
    private Integer requestCount=0;

    /**
     * 请求（总）任务金额
     */
    @TableField(value = "REQUEST_TASK_AMOUNT")
    private BigDecimal requestTaskAmount=BigDecimal.ZERO;

    /**
     * 请求(总)实发金额
     */
    @TableField(value = "REQUEST_NET_AMOUNT")
    private BigDecimal requestNetAmount = BigDecimal.ZERO;

    /**
     * 已受理笔数
     */
    @TableField(value = "ACCEPTED_COUNT")
    private Integer acceptedCount=0;

    /**
     * 受理（总）任务金额
     */
    @TableField(value = "ACCEPTED_TASK_AMOUNT")
    private BigDecimal acceptedTaskAmount = BigDecimal.ZERO;

    /**
     * 已受理(总)实发金额
     */
    @TableField(value = "ACCEPTED_NET_AMOUNT")
    private BigDecimal acceptedNetAmount=BigDecimal.ZERO;

    /**
     * 已受理代征主体服务费
     */
    @TableField(value = "ACCEPTED_FEE")
    private BigDecimal acceptedFee=BigDecimal.ZERO;

    /**
     * 已受理(总)订单金额
     */
    @TableField(value = "ACCEPTED_ORDER_AMOUNT")
    private BigDecimal acceptedOrderAmount=BigDecimal.ZERO;

    /**
     * 受理（总）个税金额
     */
    @TableField(value = "ACCEPTED_TAX_AMOUNT")
    private BigDecimal acceptedTaxAmount=BigDecimal.ZERO;

    /**
     * 成功笔数
     */
    @TableField(value = "SUCCESS_COUNT")
    private Integer successCount=0;

    /**
     * 成功（总）任务金额
     */
    @TableField(value = "SUCCESS_TASK_AMOUNT")
    private BigDecimal successTaskAmount=BigDecimal.ZERO;

    /**
     * 成功实发金额
     */
    @TableField(value = "SUCCESS_NET_AMOUNT")
    private BigDecimal successNetAmount=BigDecimal.ZERO;

    /**
     * 成功代征主体服务费
     */
    @TableField(value = "SUCCESS_FEE")
    private BigDecimal successFee=BigDecimal.ZERO;

    /**
     * 成功（总）个税金额
     */
    @TableField(value = "SUCCESS_TAX_AMOUNT")
    private BigDecimal successTaxAmount=BigDecimal.ZERO;

    /**
     * 失败笔数
     */
    @TableField(value = "FAIL_COUNT")
    private Integer failCount=0;

    /**
     * 失败任务金额
     */
    @TableField(value = "FAIL_TASK_AMOUNT")
    private BigDecimal failTaskAmount=BigDecimal.ZERO;

    /**
     * 失败实发金额
     */
    @TableField(value = "FAIL_NET_AMOUNT")
    private BigDecimal failNetAmount=BigDecimal.ZERO;

    /**
     * 错误编码
     */
    @TableField(value = "ERROR_CODE")
    private String errorCode;

    /**
     * 错误描述
     */
    @TableField(value = "ERROR_DESC")
    private String errorDesc;

    /**
     * 订单发起方式
     */
    @TableField(value = "LAUNCH_WAY")
    private Integer launchWay;

    /**
     * 回调地址
     */
    @TableField(value = "CALLBACK_URL")
    private String callbackUrl;

    /**
     * JSON数据
     */
    @TableField(value = "JSON_STR")
    private String jsonStr;

    /**
     * 是否删除，0=未删除，1=删除
     */
    @TableField(value = "IS_DELETE")
    private Integer isDelete=0;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_CONFIRM_TIME = "CONFIRM_TIME";

    public static final String COL_COMPLETE_TIME = "COMPLETE_TIME";

    public static final String COL_MCH_BATCH_NO = "MCH_BATCH_NO";

    public static final String COL_BATCH_NAME = "BATCH_NAME";

    public static final String COL_PLAT_BATCH_NO = "PLAT_BATCH_NO";

    public static final String COL_PRODUCT_NO = "PRODUCT_NO";

    public static final String COL_PRODUCT_NAME = "PRODUCT_NAME";

    public static final String COL_JOB_ID = "JOB_ID";

    public static final String COL_JOB_NAME = "JOB_NAME";

    public static final String COL_EMPLOYER_NO = "EMPLOYER_NO";

    public static final String COL_EMPLOYER_NAME = "EMPLOYER_NAME";

    public static final String COL_MAINSTAY_NO = "MAINSTAY_NO";

    public static final String COL_MAINSTAY_NAME = "MAINSTAY_NAME";

    public static final String COL_BATCH_STATUS = "BATCH_STATUS";

    public static final String COL_WORK_CATEGORY_CODE = "WORK_CATEGORY_CODE";

    public static final String COL_WORK_CATEGORY_NAME = "WORK_CATEGORY_NAME";

    public static final String COL_SERVICE_DESC = "SERVICE_DESC";

    public static final String COL_REQUEST_COUNT = "REQUEST_COUNT";

    public static final String COL_REQUEST_TASK_AMOUNT = "REQUEST_TASK_AMOUNT";

    public static final String COL_REQUEST_NET_AMOUNT = "REQUEST_NET_AMOUNT";

    public static final String COL_ACCEPTED_COUNT = "ACCEPTED_COUNT";

    public static final String COL_ACCEPTED_TASK_AMOUNT = "ACCEPTED_TASK_AMOUNT";

    public static final String COL_ACCEPTED_NET_AMOUNT = "ACCEPTED_NET_AMOUNT";

    public static final String COL_ACCEPTED_FEE = "ACCEPTED_FEE";

    public static final String COL_ACCEPTED_ORDER_AMOUNT = "ACCEPTED_ORDER_AMOUNT";

    public static final String COL_ACCEPTED_TAX_AMOUNT = "ACCEPTED_TAX_AMOUNT";

    public static final String COL_SUCCESS_COUNT = "SUCCESS_COUNT";

    public static final String COL_SUCCESS_TASK_AMOUNT = "SUCCESS_TASK_AMOUNT";

    public static final String COL_SUCCESS_NET_AMOUNT = "SUCCESS_NET_AMOUNT";

    public static final String COL_SUCCESS_FEE = "SUCCESS_FEE";

    public static final String COL_SUCCESS_TAX_AMOUNT = "SUCCESS_TAX_AMOUNT";

    public static final String COL_FAIL_COUNT = "FAIL_COUNT";

    public static final String COL_FAIL_TASK_AMOUNT = "FAIL_TASK_AMOUNT";

    public static final String COL_FAIL_NET_AMOUNT = "FAIL_NET_AMOUNT";

    public static final String COL_ERROR_CODE = "ERROR_CODE";

    public static final String COL_ERROR_DESC = "ERROR_DESC";

    public static final String COL_LAUNCH_WAY = "LAUNCH_WAY";

    public static final String COL_CALLBACK_URL = "CALLBACK_URL";

    public static final String COL_JSON_STR = "JSON_STR";

    public static final String COL_IS_DELETE = "IS_DELETE";

    public static void extendWhere(QueryWrapper<OfflineOrder> queryWrapper, Map<String,Object> param) {
        queryWrapper.between(param.get("createTimeBegin") != null && param.get("createTimeEnd") != null, COL_CREATE_TIME, param.get("createTimeBegin"), param.get("createTimeEnd"));
        queryWrapper.between(param.get("completeTimeBegin") != null && param.get("completeTimeEnd") != null, COL_COMPLETE_TIME, param.get("completeTimeBegin"), param.get("completeTimeEnd"));
        queryWrapper.between(param.get("createBeginDate") != null && param.get("createEndDate") != null, COL_CREATE_TIME, param.get("createBeginDate"), param.get("createEndDate"));
        queryWrapper.between(param.get("completeBeginDate") != null && param.get("completeEndDate") != null, COL_COMPLETE_TIME, param.get("completeBeginDate"), param.get("completeEndDate"));
        queryWrapper.like(StringUtils.isNotBlank((String) param.get("employerNameLike")), COL_EMPLOYER_NAME, param.get("employerNameLike"));
        queryWrapper.like(StringUtils.isNotBlank((String) param.get("jobNameLike")), COL_JOB_NAME, param.get("jobNameLike"));
        queryWrapper.like(StringUtils.isNotBlank((String) param.get("batchNameLike")), COL_BATCH_NAME, param.get("batchNameLike"));

        if (StringUtils.isNotBlank((String) param.get("orderField"))) {
            queryWrapper.orderByDesc(StrUtil.toUnderlineCase((String) param.get("orderField")).toUpperCase());
        }else {
            queryWrapper.orderByDesc(COL_ID);
        }
    }
}
