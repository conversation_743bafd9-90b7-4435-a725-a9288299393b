package com.zhixianghui.facade.trade.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName CmbAccountChangeVo
 * @Description TODO
 * @Date 2023/2/10 16:48
 */
@Data
public class CmbAccountChangeVo implements Serializable {

    /**
     * 账号
     */
    private String accnbr;

    /**
     * 户名
     */
    private String accnam;

    /**
     * 交易日期
     */
    private String trsdat;

    /**
     * 交易时间
     */
    private String trstim;

    /**
     * 币种
     */
    private String c_ccynbr;

    /**
     * 交易金额
     */
    private String c_trsamt;

    /**
     * 收/付方账户账号
     */
    private String rpyacc;

    /**
     * 收/付方账户名称
     */
    private String rpynam;

    /**
     * 流水号
     */
    private String refnbr;

    /**
     * 摘要
     */
    private String naryur;

    /**
     * 交易分析码
     */
    private String trscod;

    /**
     * 起息日
     */
    private String vltdat;

    /**
     * 收/付方开户行名
     */
    private String rpybnk;

    /**
     * 企业识别码
     */
    private String frmcod;

    /**
     * 用于标识收/付方帐号和母/子公司的信息。为空表示付方帐号和子公司；为“1”表示收方帐号和子公司；为“2”表示收方帐号和母公司；为“3”表示原收方帐号和子公司
     */
    private String infflg;
}
