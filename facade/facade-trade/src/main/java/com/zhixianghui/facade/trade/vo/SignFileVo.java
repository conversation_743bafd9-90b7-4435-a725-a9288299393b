package com.zhixianghui.facade.trade.vo;

import com.zhixianghui.facade.trade.entity.SignRecord;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/9/1 16:38
 */
@Data
public class SignFileVo {
    // 这里直接设置了生产上的地址
//    private static final String DOMAIN_PREFIX = "https://static.hjzxh.com/";
    private String protocolImg;
    private String contractImg;
    private String signImg;
    private String cerReverseImg;
    private String cerFrontImg;

    public static class Builder {
        private SignRecord signRecord;
        public Builder(SignRecord signRecord) {
            this.signRecord = signRecord;
        }

        public SignFileVo build() {
            SignFileVo fileVo = new SignFileVo();
            if (StringUtils.isNotBlank(signRecord.getFileUrl())) {
                fileVo.setContractImg(signRecord.getFileUrl());
                fileVo.setProtocolImg(signRecord.getFileUrl());
            }
            if (StringUtils.isNotBlank(signRecord.getPersonalSignature())) {
                fileVo.setSignImg(signRecord.getPersonalSignature());
            }
            if (StringUtils.isNotBlank(signRecord.getIdCardFrontUrl())) {
                fileVo.setCerFrontImg(signRecord.getIdCardFrontUrl());
            }
            if (StringUtils.isNotBlank(signRecord.getIdCardBackUrl())) {
                fileVo.setCerReverseImg(signRecord.getIdCardBackUrl());

            }
            return fileVo;
        }
    }
}
