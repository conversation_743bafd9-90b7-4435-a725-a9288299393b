package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 商户平台订单映射
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-06
 */
@Data
public class MchPlatTrxNo implements Serializable {

    private static final long serialVersionUID = -1920963626829923336L;

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 渠道流水号
     */
    private String channelTrxNo;
}
