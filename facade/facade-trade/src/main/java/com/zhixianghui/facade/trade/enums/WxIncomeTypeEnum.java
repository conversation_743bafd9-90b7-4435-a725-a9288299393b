package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName WxIncomeTypeEnum
 * @Description TODO
 * @Date 2021/12/10 15:29
 */
@AllArgsConstructor
public enum  WxIncomeTypeEnum {

    OFFLINERECHARGE(100,"OFFLINERECHARGE","转账充值"),

    ENTERPRISEDIRECTREVENUE(101,"ENTERPRISEDIRECTREVENUE","企业直收");

    private int value;

    private String type;

    private String desc;

    public static WxIncomeTypeEnum getEnum(String type) {
        return Arrays.stream(values()).filter(p -> p.type.equals(type)).findFirst().orElse(null);
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
