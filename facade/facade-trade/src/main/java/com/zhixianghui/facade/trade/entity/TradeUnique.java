package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TradeUnique implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 唯一键
     */
    private String uniqueKey;

    /**
     * 创建时间
     */
    private Date createTime = new Date();


}
