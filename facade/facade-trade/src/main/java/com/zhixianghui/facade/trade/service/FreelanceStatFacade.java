package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;

import java.util.List;
import java.util.Map;

/**
 * 自由职业者月统计表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-08-02
 */
public interface FreelanceStatFacade {


    List<FreelanceStat> freelanceStat(Map<String, Object> param);

    PageResult<List<FreelanceStat>> freelanceStat(Map<String, Object> param, PageParam pageParam);

    PageResult<List<Map<String, Object>>> freelanceStat2(Map<String, Object> param, PageParam pageParam);

    FreelanceStatVo count(Map<String, Object> param);

    Integer freelanceCount(Map<String, Object> param);

    void insert(List<FreelanceStat> list);

    List<FreelanceStatVo> list(Map<String, Object> param, PageParam pageParam);

    PageResult<List<FreelanceStat>> listPage(Map<String, Object> param, PageParam newInstance);

    FreelanceStat getById(Long id);

    void update(List<FreelanceStat> data);

    void update(FreelanceStat stat);

    FreelanceStatVo merchantStatCount(Map<String, Object> param);

    PageResult<List<UserInfo>> idCardList(Map<String, Object> paramMap, PageParam pageParam);
}
