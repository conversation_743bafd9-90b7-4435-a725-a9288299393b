package com.zhixianghui.facade.trade.service;

import com.zhixianghui.facade.trade.entity.AcRechargeAccount;

/***
 * 商户的充值目标账户facade
 */
public interface AcRechargeAccountFacade {

    public void insert(AcRechargeAccount model);

    /***
     * 根据商户号供应商号查询充值账号
     * @param mchNo
     * @param mainstayNo
     * @param payChannelNo
     * @return
     */
    public AcRechargeAccount getRechargeAccountByMchNoAndMainstayNo(String mchNo, String mainstayNo, String payChannelNo);

    /***
     * 检查是否存在如不存在则创建
     * @param model
     */
    void checkExistOrInsert(AcRechargeAccount model);

    void updateAccountInfo(Long accountId, String accountNo, String accountName);

    long getCountByModel(AcRechargeAccount model);
}
