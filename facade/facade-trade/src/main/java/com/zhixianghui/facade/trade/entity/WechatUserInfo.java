package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;
import java.util.Optional;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-02-27
 */
@Data
@TableName(value = "tbl_wechat_user_info")
@EqualsAndHashCode(callSuper = false)
public class WechatUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 二要素鉴权状态
     */
    private Integer authStatus;

    /**
     * 状态,1成功, 0禁用
     */
    private Integer status;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 最近登录时间
     */
    private Date lastLoginAt;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 用户编号
     */
    private String userNo;

    private String wxUserNo;

    /**
     * 身份证号码
     */
    private String receiveIdCardNo;

    /**
     * 身份证号码MD5
     */
    private String receiveIdCardNoMd5;

    /**
     * 密钥ID
     */
    private Long encryptKeyId;

    @TableField(exist = false)
    private String appId;

    @TableField(exist = false)
    private String openId;

    public void setReceiveIdCardNo(String receiveIdCardNo) {
        this.receiveIdCardNo = receiveIdCardNo;
        this.receiveIdCardNoMd5 = MD5Util.getMixMd5Str(receiveIdCardNo);
    }

    public String getReceiveIdCardDecrypt() {
        return Optional.ofNullable(this.receiveIdCardNo)
                .map(e->AESUtil.decryptECB(this.receiveIdCardNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr()))
                .orElse(null);
    }


}
