package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName WxIncomeRecordEnum
 * @Description TODO
 * @Date 2021/12/8 17:27
 */
@AllArgsConstructor
public enum WxIncomeRecordEnum {

    ACTIVE(100,"已入账"),

    PENDING(101,"待处理"),

    CLOSE(102,"已取消"),

    INCOMING(103,"入账中");

    private int value;

    private String desc;

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
