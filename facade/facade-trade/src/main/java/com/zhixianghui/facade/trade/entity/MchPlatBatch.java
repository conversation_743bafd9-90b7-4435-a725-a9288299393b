package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 商户平台批次映射
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-06
 */
@Data
public class MchPlatBatch implements Serializable {

    private static final long serialVersionUID = 4575296534452897722L;

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;


}
