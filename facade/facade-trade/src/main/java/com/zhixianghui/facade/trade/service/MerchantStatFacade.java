package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.MerchantStat;
import com.zhixianghui.facade.trade.vo.MerchantStatVo;

import java.util.List;
import java.util.Map;

/**
 * 商户月统计表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-08-02
 */
public interface MerchantStatFacade {

    List<MerchantStat> merchantStat(Map<String, Object> param);

    PageResult<List<MerchantStat>> merchantStat(Map<String, Object> param, PageParam pageParam);

    MerchantStatVo count(Map<String, Object> stringObjectMap);

    void insert(List<MerchantStat> result);

    List<MerchantStatVo> list(Map<String, Object> param, PageParam pageParam);

    List<MerchantStatVo> groupBySaleId(Map<String, Object> saleIds);
}
