package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.dto.ChangesFoundsDTO;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.vo.ChangesFundsVo;

import java.util.List;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-12-09
 */
public interface ChangesFundsFacade {

    void insert(ChangesFunds changesFunds);

    PageResult<List<ChangesFundsVo>> listPage(ChangesFoundsDTO changesFoundsDTO);
}
