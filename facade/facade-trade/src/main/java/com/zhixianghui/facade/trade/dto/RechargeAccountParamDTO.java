package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/21 16:36 
 */
@Data
public class RechargeAccountParamDTO implements Serializable {
    private static final long serialVersionUID = -7155767055129320581L;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 充值金额
     */
    private BigDecimal amount;
}
