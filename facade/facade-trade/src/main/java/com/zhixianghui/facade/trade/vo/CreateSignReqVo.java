package com.zhixianghui.facade.trade.vo;

import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 发起签约Req
 * @date 2021/1/15 16:50
 **/
@Data
public class CreateSignReqVo {
    /**
     * 用户唯一标识
     */
    @NotBlank(message = "user_id 不能为空")
    private String userId;

    /**
     * 签约模式
     */
    private Integer signType = ChannelSignTypeEnum.MSG.getValue();

    /**
     * 重定向地址
     */
    private String redirectUrl;

    /**
     * 回调通知地址
     */
    private String callbackUrl;
}
