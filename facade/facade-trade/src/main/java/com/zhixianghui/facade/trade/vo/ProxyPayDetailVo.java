package com.zhixianghui.facade.trade.vo;


import lombok.Getter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
public class ProxyPayDetailVo implements Serializable {
    private static final long serialVersionUID = 482588818322221887L;
    private BigDecimal taxAmount = BigDecimal.ZERO;
    private BigDecimal vatAmount = BigDecimal.ZERO;
    private BigDecimal localEduAddTaxAmount = BigDecimal.ZERO;
    private BigDecimal eduAddTaxAmount = BigDecimal.ZERO;
    private BigDecimal buildingTaxAmount = BigDecimal.ZERO;
    private BigDecimal stampDutyAmount = BigDecimal.ZERO;
    private BigDecimal serviceFeeAmount = BigDecimal.ZERO;

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = this.taxAmount.add(taxAmount);
    }

    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = this.vatAmount.add(vatAmount);
    }

    public void setLocalEduAddTaxAmount(BigDecimal localEduAddTaxAmount) {
        this.localEduAddTaxAmount = this.localEduAddTaxAmount.add(localEduAddTaxAmount);
    }

    public void setEduAddTaxAmount(BigDecimal eduAddTaxAmount) {
        this.eduAddTaxAmount = this.localEduAddTaxAmount.add(localEduAddTaxAmount);
    }

    public void setBuildingTaxAmount(BigDecimal buildingTaxAmount) {
        this.buildingTaxAmount = this.buildingTaxAmount.add(buildingTaxAmount);
    }

    public void setStampDutyAmount(BigDecimal stampDutyAmount) {
        this.stampDutyAmount = this.stampDutyAmount.add(stampDutyAmount);
    }

    public void setServiceFeeAmount(BigDecimal serviceFeeAmount) {
        this.serviceFeeAmount = this.serviceFeeAmount.add(serviceFeeAmount);
    }

}
