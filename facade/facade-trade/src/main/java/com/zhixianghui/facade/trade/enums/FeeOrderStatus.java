package com.zhixianghui.facade.trade.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.interfaces.BaseEnumInterFace;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月07日 11:55:00
 */
@Getter
@AllArgsConstructor
@EnumDesc(name = "交付类型", type = SystemTypeEnum.CKH_MANAGEMENT)
public enum FeeOrderStatus implements BaseEnumInterFace {

    NO_PAY(100, "待付款"),
    PAYING(200, "付款中"),
    FAIL(300, "付款失败"),
    SUCCESS(400, "已付款");


    private final Integer code;
    private final String desc;
}