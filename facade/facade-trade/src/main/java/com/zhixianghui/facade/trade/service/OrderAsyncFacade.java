package com.zhixianghui.facade.trade.service;

import com.zhixianghui.facade.trade.entity.Order;

/**
 * <AUTHOR>
 * @description 提供异步调用facade
 * @date 2020-11-26 11:35
 **/
public interface OrderAsyncFacade {
    /**
     * 开始发放批次订单
     * @param order 批次订单
     */
    void startGrant(Order order);

    /**
     * 重新发放
     * @param order 批次订单
     */
    void grantAgain(Order order);

    /**
     * 重新受理
     * @param order 批次订单
     */
    void acceptAgain(Order order);
}
