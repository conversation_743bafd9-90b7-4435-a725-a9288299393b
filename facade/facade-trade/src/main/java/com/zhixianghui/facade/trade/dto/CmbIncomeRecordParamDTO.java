package com.zhixianghui.facade.trade.dto;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/22 15:43
 */
@Data
public class CmbIncomeRecordParamDTO extends PageQueryVo {
    private static final long serialVersionUID = -1715838937612853140L;

    /**
     * 通道交易流水号
     */
    private String channelTrxNo;

    /**
     * 用工企业编号
     */
    private String mchNo;

    /**
     * 用工企业名称
     */
    private String mchName;

    /**
     * 收款方账号
     */
    private String payeeAccountNo;


    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 审核状态 CmbIncomeStateEnum
     */
    private Integer state;

    /**
     * 完成开始时间
     */
    private Date finishStartTime;

    /**
     * 完成结束时间
     */
    private Date finishEndTime;

}
