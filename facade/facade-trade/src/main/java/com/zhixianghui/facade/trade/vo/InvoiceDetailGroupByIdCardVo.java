package com.zhixianghui.facade.trade.vo;

import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class InvoiceDetailGroupByIdCardVo implements Serializable {
    private static final long serialVersionUID = 8256976805838596133L;
    private Integer invoiceType;
    private String invoiceTrxNo;
    private BigDecimal invoiceAmount;
    private String receiveIdCardNo;
    private String receiveIdCardNoDesensitize;
    private String receiveAccountNo;
    private String receiveAccountNoDesensitize;
    private String receivePhoneNo;
    private String receivePhoneNoDesensitize;
    private String receiveName;
    private String receiveNameDesensitize;
    private Integer invoiceStatus;
    private List<String> invoiceFileUrlList;
    private String expressNo;
    private String workerBillFilePath;

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
        this.receiveNameDesensitize = receiveName == null ? "" : DesensitizeUtil.handleNameDesenCenter(receiveName);
    }

    public void setReceiveIdCardNo(String receiveIdCardNo) {
        this.receiveIdCardNo = receiveIdCardNo;
        this.receiveIdCardNoDesensitize = receiveIdCardNo == null ? "" : DesensitizeUtil.handleIdNumOrBankCardNo(receiveIdCardNo);
    }

    public void setReceiveAccountNo(String receiveAccountNo) {
        this.receiveAccountNo = receiveAccountNo;
        if (receiveAccountNo == null) {
            this.receiveAccountNoDesensitize = "";
        }else {
            if(ValidateUtil.isMobile(receiveAccountNo)){
                this.receiveAccountNoDesensitize = DesensitizeUtil.handleMobile(receiveAccountNo);
            } else if(ValidateUtil.isEmail(receiveAccountNo)){
                this.receiveAccountNoDesensitize = DesensitizeUtil.handleEmailDesenCenter(receiveAccountNo);
            } else{
                this.receiveAccountNoDesensitize = DesensitizeUtil.handleIdNumOrBankCardNo(receiveAccountNo);
            }
        }
    }

    public void setReceivePhoneNo(String receivePhoneNo) {
        this.receivePhoneNo = receivePhoneNo;
        this.receivePhoneNoDesensitize = receivePhoneNo == null ? "" : DesensitizeUtil.handleMobile(receivePhoneNo);
    }
}
