package com.zhixianghui.facade.trade.service;


import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.entity.AcIncomeRecord;

import java.util.List;
import java.util.Map;


/**
 * 来账记录信息Service接口
 * <AUTHOR> @date 2024-04-01
 */
public interface AcIncomeRecordFacade {

    //新增来账记录信息
    void insert(AcIncomeRecord AcIncomeRecord) throws BizException;

    //修改来账记录信息
    void update(AcIncomeRecord AcIncomeRecord) throws BizException;

    //通过id查看来账记录信息
    AcIncomeRecord getById(Long id);
    
    // 根据条件查询列表
    public List<AcIncomeRecord> listBy(Map<String, Object> paramMap);

    //条件分页查询来账记录信息列表
//    PageResult<List<AcIncomeRecord>> listPage(Map<String, Object> paramMap, PageQuery pageQuery);

}
