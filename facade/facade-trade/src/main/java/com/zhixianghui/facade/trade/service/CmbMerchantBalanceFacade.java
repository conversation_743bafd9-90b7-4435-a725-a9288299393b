package com.zhixianghui.facade.trade.service;

import com.zhixianghui.facade.trade.dto.AdjustAccountParamDTO;
import com.zhixianghui.facade.trade.dto.CmbAccountQueryDTO;
import com.zhixianghui.facade.trade.dto.CmbCreateAccountDTO;
import com.zhixianghui.facade.trade.entity.CmbMerchantBalance;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/22 14:28
 */
public interface CmbMerchantBalanceFacade {

    /**
     * 创建招行账户
     *
     * @param cmbCreateAccount
     */
    void createMerchantBalance(CmbCreateAccountDTO cmbCreateAccount);

    /**
     * 招行账户调账
     *
     * @param adjustmentDTO
     */
    void adjustAccount(AdjustAccountParamDTO adjustmentDTO);


    /**
     * 查询招行本地账户
     *
     * @param cmbAccountQueryDTO
     * @return
     */
    CmbMerchantBalance getCmbMerchantBalance(CmbAccountQueryDTO cmbAccountQueryDTO);

    /**
     * 查询余额
     *
     * @param param
     * @return
     */
    BigDecimal getAmount(CmbAccountQueryDTO param);
}
