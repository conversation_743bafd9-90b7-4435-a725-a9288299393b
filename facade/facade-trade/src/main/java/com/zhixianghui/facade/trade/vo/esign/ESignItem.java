package com.zhixianghui.facade.trade.vo.esign;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 盖章属性
 * @createTime 2022年07月29日 09:43:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
public class ESignItem implements Serializable {
    private static final long serialVersionUID = -1112668291145498059L;

    //e签宝属性
    @Builder.Default
    private String label = "签署区";
    private String mchNo;
    private String signOrgId;

    private String signerMchName;
    private String signerName;
    private String signerPhone;


    private String signAccountId;
    @Builder.Default
    private String page = "1";
    @Builder.Default
    private int signType = 1;

    /**
     * 是否需要自动签署
     */
    private boolean autoExecute = true;

    /**
     * 以下三种方式找到坐标定位，选其一
    */
    //1.如果通过e签宝控制台创建模板，通过key去签署
    private String key;


    //2.查询关键字，可以通过偏移量调整误差（当盖章位置需要动态修改时使用此属性）
    private String keywords;
    @Builder.Default
    private float xOffset = 0;//x轴偏移量
    @Builder.Default
    private float yOffset = 0;//y轴偏移量

    //3.直接通过xy轴坐标定位
    @Builder.Default
    private float xPos = 0;
    @Builder.Default
    private float yPos = 0;

    @Builder.Default
    private float width = 120;
    @Builder.Default
    private float height = 120;


    public ESignItem(String mchNo,String key) {
        this.mchNo = mchNo;
        this.key = key;
    }

    public ESignItem(String mchNo, float xPos, float yPos) {
        this.mchNo = mchNo;
        this.xPos = xPos;
        this.yPos = yPos;
    }

    public ESignItem(String label, String mchNo, float xPos, float yPos) {
        this(mchNo, xPos, yPos);
        this.label = label;
    }
}