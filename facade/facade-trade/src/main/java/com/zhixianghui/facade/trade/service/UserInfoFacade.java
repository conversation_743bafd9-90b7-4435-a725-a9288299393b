package com.zhixianghui.facade.trade.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.dto.UserInfoQueryDto;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.vo.UserAuthVo;

/**
 * <AUTHOR>
 * @ClassName UserInfoFacade
 * @Description TODO
 * @Date 2022/2/21 11:58
 */
public interface UserInfoFacade {

    //获取收款用户信息
    UserInfo getByIdCardNoMd5(String idCardNoMd5);

    UserAuthVo getAuthInfo(String phone);

    IPage<UserInfo> userInfoIPage(IPage<UserInfo> page, UserInfoQueryDto userInfoQueryDto);

    void deleteId(Long id);

    /**
     * 是否已认证
     * @param idcardNoMd5
     * @return
     * @throws BizException
     */
    boolean isVerified(String idcardNoMd5) throws BizException;

    void addUserInfo(UserInfo userInfo) throws BizException;
}
