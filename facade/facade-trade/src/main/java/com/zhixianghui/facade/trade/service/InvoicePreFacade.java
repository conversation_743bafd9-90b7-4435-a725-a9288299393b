package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;

import java.util.List;
import java.util.Map;

/**
 * 预开发票记录
 */
public interface InvoicePreFacade {
    
    /**
     * 根据用工企业编号和代征主体编号查询
     * @param employerMchNo 用工企业编号
     * @param mainstayMchNo 代征主体编号
     * @return
     */
    InvoicePreRecord getByEmployerAndMainstay(String employerMchNo, String mainstayMchNo);


    InvoicePreRecord getByEmployerByMap(Map<String, Object> map);

    /**
     * 分页查询
     * @param paramMap  查询参数
     * @param pageParam 页码参数
     * @return
     */
    PageResult<List<InvoicePreRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 创建预开发票记录
     * @param record 预开发票记录
     * @return
     */
    void createPreInvoice(InvoicePreRecord record) throws BizException;

    /**
     * 更新预开发票记录
     * @param record 预开发票记录
     */
    void updatePreInvoice(InvoicePreRecord record) throws BizException;

    /**
     * 删除预开发票记录
     * @param id 记录ID
     */
    void deletePreInvoice(Long id) throws BizException;

    /**
     * 统计预开票金额
     * @param paramMap 参数
     * @return
     */
    Map<String, Object> countInvoiceAmount(Map<String, Object> paramMap);

    /**
     * 检查是否存在指定条件的预开票记录
     * @param paramMap 查询条件
     * @return true-存在，false-不存在
     */
    boolean existRecord(Map<String, Object> paramMap);
}