package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
    * 收款用户信息表
    */
@Data
@TableName(value = "tbl_user_info")
@Accessors(chain = true)
public class UserInfo implements Serializable {
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    @Version
    private Integer version;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 姓名
     */
    @TableField(value = "RECEIVE_NAME")
    private String receiveName;


    /**
     * 姓名MD5
     */
    @TableField(value = "RECEIVE_NAME_MD5")
    private String receiveNameMd5;

    /**
     * 身份证号码
     */
    @TableField(value = "RECEIVE_ID_CARD_NO")
    private String receiveIdCardNo;


    /**
     * 持卡人身份证号MD5
     */
    @TableField(value = "RECEIVE_ID_CARD_NO_MD5")
    private String receiveIdCardNoMd5;

    /**
     * 身份证背面地址
     */
    @TableField(value = "ID_CARD_BACK_URL")
    private String idCardBackUrl;

    /**
     * 身份证正面地址
     */
    @TableField(value = "ID_CARD_FRONT_URL")
    private String idCardFrontUrl;

    /**
     * 个人签章
     */
    @TableField(value = "PERSONAL_SIGNATURE")
    private String personalSignature;

    /**
     * 半身照地址
     */
    @TableField(value = "CER_FACE_URL")
    private String cerFaceUrl;

    /**
     * 身份证复印件地址
     */
    @TableField(value = "ID_CARD_COPY_URL")
    private String idCardCopyUrl;

    /**
     * 密钥ID
     */
    @TableField(value = "ENCRYPT_KEY_ID")
    private Long encryptKeyId;

    @TableField(value = "OPEN_USER_ID")
    private String openUserId;
    // 自定义
    @TableField(exist = false)
    private String employerName;
    @TableField(exist = false)
    private String employerNo;

    public UserInfo buildUserInfo(SignRecord signRecord) {
        this.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        this.cerFaceUrl = signRecord.getCerFaceUrl();
        this.createTime = new Date();
        this.updateTime = this.createTime;
        this.idCardBackUrl = signRecord.getIdCardBackUrl();
        this.idCardFrontUrl = signRecord.getIdCardFrontUrl();
        this.idCardCopyUrl = signRecord.getIdCardCopyUrl();
        this.setReceiveIdCardNoEncrypt(signRecord.getReceiveIdCardNoDecrypt());
        this.setReceiveNameEncrypt(signRecord.getReceiveNameDecrypt());
        this.setPersonalSignature(signRecord.getPersonalSignature());
        return this;
    }

    public UserInfo buildUserInfo(OrderItem orderItem) {
        this.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
//        this.userId = signRecord.getUserId();
//        this.cerFaceUrl = signRecord.getCerFaceUrl();
        this.createTime = new Date();
        this.updateTime = this.createTime;
//        this.idCardBackUrl = orderItem.getIdCardBackUrl();
//        this.idCardFrontUrl = signRecord.getIdCardFrontUrl();
//        this.idCardCopyUrl = signRecord.getIdCardCopyUrl();
        this.setReceiveIdCardNoEncrypt(orderItem.getReceiveIdCardNoDecrypt());
        this.setReceiveNameEncrypt(orderItem.getReceiveNameDecrypt());
        return this;
    }

    public void setReceiveNameEncrypt(String receiveName) {
        if(StringUtils.isNotEmpty(receiveName)){
            this.receiveName = AESUtil.encryptECB(receiveName, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receiveName);
            this.receiveNameMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceiveNameDecrypt() {
        return AESUtil.decryptECB(this.receiveName, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setReceiveIdCardNoEncrypt(String receiveIdCardNo) {
        if(StringUtils.isNotEmpty(receiveIdCardNo)){
            this.receiveIdCardNo = AESUtil.encryptECB(receiveIdCardNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receiveIdCardNo);
            this.receiveIdCardNoMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceiveIdCardNoDecrypt() {
        return AESUtil.decryptECB(this.receiveIdCardNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_RECEIVE_NAME = "RECEIVE_NAME";

    public static final String COL_RECEIVE_NAME_MD5 = "RECEIVE_NAME_MD5";

    public static final String COL_RECEIVE_ID_CARD_NO = "RECEIVE_ID_CARD_NO";

    public static final String COL_RECEIVE_ID_CARD_NO_MD5 = "RECEIVE_ID_CARD_NO_MD5";

    public static final String COL_ID_CARD_BACK_URL = "ID_CARD_BACK_URL";

    public static final String COL_ID_CARD_FRONT_URL = "ID_CARD_FRONT_URL";

    public static final String COL_PERSONAL_SIGNATURE = "PERSONAL_SIGNATURE";

    public static final String COL_RECEIVE_ACCOUNT_NO = "RECEIVE_ACCOUNT_NO";

    public static final String COL_RECEIVE_ACCOUNT_NO_MD5 = "RECEIVE_ACCOUNT_NO_MD5";

    public static final String COL_CER_FACE_URL = "CER_FACE_URL";

    public static final String COL_ID_CARD_COPY_URL = "ID_CARD_COPY_URL";

    public static final String COL_ENCRYPT_KEY_ID = "ENCRYPT_KEY_ID";

    public static final String COL_OPEN_USER_ID = "OPEN_USER_ID";
}
