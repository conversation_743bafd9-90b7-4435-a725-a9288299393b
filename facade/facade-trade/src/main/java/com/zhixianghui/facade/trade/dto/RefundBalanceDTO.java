package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/30 14:28
 */
@Data
public class RefundBalanceDTO implements Serializable {
    private static final long serialVersionUID = 5284104218616465374L;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 平台批次号，资金下发不为空
     */
    private String platBatchNo;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 退款服务费
     */
    private BigDecimal feeAmount;

}
