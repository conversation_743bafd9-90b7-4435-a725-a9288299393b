package com.zhixianghui.facade.trade.service;


import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.TemplateComponent;
import com.zhixianghui.facade.trade.vo.DocTemplateAddOrEditVo;
import com.zhixianghui.facade.trade.vo.SignCustomizeTemplateVo;
import com.zhixianghui.facade.trade.vo.SignTemplateResVo;


import java.util.List;

/**
 * 模板id表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-06-07
 */
public interface SignTemplateFacade {

    /**
     * 添加模板
     * @param queryVo 模板参数
     */
    DocTemplateAddOrEditVo addTemplate(SignCustomizeTemplateVo queryVo);

    /**
     * 上传文件
     * @return 上传是否成功
     */
//    SignCustomizeTemplateVo uploadTemplate(SignCustomizeTemplateVo queryVo);

    /**
     * 模板修改
     * @param queryVo 待修改模板
     * @return 修改是否成功
     */
    DocTemplateAddOrEditVo modify(SignCustomizeTemplateVo queryVo,Boolean isChangeFile);

    /**
     * @param queryVo 查询参数
     * @return 模板列表
     */
    PageResult<List<SignTemplateResVo>> list(SignCustomizeTemplateVo queryVo);

    /**
     * @param id 模板id
     * @return 删除是否成功
     */
    boolean del(Long id);

    List<TemplateComponent> componentList();
}
