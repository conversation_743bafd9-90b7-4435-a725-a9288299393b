package com.zhixianghui.facade.trade.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WxIncomeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer version;

    /**
     * 特约商户id
     */
    private String subMchId;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 银行来账类型
     */
    private Integer incomeType;

    /**
     * 银行来账微信单号
     */
    private String incomeRecordId;

    /**
     * 交易金额
     */
    private Long amount;

    /**
     * 银行来账完成时间
     */
    private Date successTime;

    /**
     * 付款银行名称
     */
    private String bankName;

    /**
     * 付款银行账户名
     */
    private String bankAccountName;

    /**
     * 付款银行卡号
     */
    private String bankAccountNumber;

    /**
     * 所属商户编号
     */
    private String mchNo;

    /**
     * 所属商户名称
     */
    private String mchName;

    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 银行备注
     */
    private String rechargeRemark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date udpateTime;

    /**
     * 更新人
     */
    private String updator;

    /**
     *银行出款回单
     */
    private String payImgUrl;

    /**
     * 金额（元）
     * 不参与db
     */
    private BigDecimal amountYuan;
}
