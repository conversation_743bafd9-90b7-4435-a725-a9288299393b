package com.zhixianghui.facade.trade.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月12日 15:10:00
 */
@Data
public class FeeOrderSumVo implements Serializable {
    private static final long serialVersionUID = -6846331150006061980L;
    /**
     * 成功实发金额
     */
    private BigDecimal orderNetAmount = BigDecimal.ZERO;

    /**
     * 个税合计
     */
    private BigDecimal taxAmount = BigDecimal.ZERO;

    /**
     * 服务费合计
     */
    private BigDecimal feeAmount = BigDecimal.ZERO;
    ;
}