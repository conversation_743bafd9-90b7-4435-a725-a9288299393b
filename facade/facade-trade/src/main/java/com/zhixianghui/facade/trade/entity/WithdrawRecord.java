package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 取现记录表
    */
@Data
@TableName(value = "tbl_withdraw_record")
public class WithdrawRecord implements Serializable {
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 取现流水号
     */
    @TableField(value = "WITHDRAW_NO")
    private String withdrawNo;

    /**
     * 用工企业编号
     */
    @TableField(value = "EMPLOYER_NO")
    private String employerNo;

    /**
     * 代征主体编号
     */
    @TableField(value = "MAINSTAY_NO")
    private String mainstayNo;

    /**
     * 商户类型
     */
    @TableField(value = "MERCHANT_TYPE")
    private Integer merchantType;

    /**
     * 通道类型
     */
    @TableField(value = "CHANNEL_TYPE")
    private Integer channelType;

    /**
     * 通道编号
     */
    @TableField(value = "CHANNEL_NO")
    private String channelNo;

    /**
     * 金额
     */
    @TableField(value = "AMOUNT")
    private BigDecimal amount;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 版本号
     */
    @Version
    @TableField(value = "VERSION")
    private Integer version;

    /**
     * 用工企业名称
     */
    @TableField(value = "EMPLOYER_NAME")
    private String employerName;

    /**
     * 代征主体名称
     */
    @TableField(value = "MAINSTAY_NAME")
    private String mainstayName;

    /**
     * 收款人账号
     */
    @TableField(value = "RECEIVE_ACCT_NO")
    private String receiveAcctNo;

    /**
     * 收款人证件类型
     */
    @TableField(value = "RECEIVE_ID_TYPE")
    private Integer receiveIdType;

    /**
     * 收款人证件号码
     */
    @TableField(value = "RECWIVE_ID_NO")
    private String recwiveIdNo;

    /**
     * 收款人账户类型
     */
    @TableField(value = "RECEIVE_ACCT_TYPE")
    private Integer receiveAcctType;

    /**
     * 收款人姓名
     */
    @TableField(value = "RECEIVE_NAME")
    private String receiveName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 提现状态
     */
    @TableField(value = "WITHDRAW_STATUS")
    private Integer withdrawStatus;

    @TableField(value = "ERROR_CODE")
    private String errorCode;

    @TableField(value = "ERROR_MSG")
    private String errorMsg;

    @TableField(exist = false)
    private String channelName;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_WITHDRAW_NO = "WITHDRAW_NO";

    public static final String COL_EMPLOYER_NO = "EMPLOYER_NO";

    public static final String COL_MAINSTAY_NO = "MAINSTAY_NO";

    public static final String COL_MERCHANT_TYPE = "MERCHANT_TYPE";

    public static final String COL_CHANNEL_TYPE = "CHANNEL_TYPE";

    public static final String COL_CHANNEL_NO = "CHANNEL_NO";

    public static final String COL_AMOUNT = "AMOUNT";

    public static final String COL_REMARK = "REMARK";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_EMPLOYER_NAME = "EMPLOYER_NAME";

    public static final String COL_MAINSTAY_NAME = "MAINSTAY_NAME";

    public static final String COL_RECEIVE_ACCT_NO = "RECEIVE_ACCT_NO";

    public static final String COL_RECEIVE_ID_TYPE = "RECEIVE_ID_TYPE";

    public static final String COL_RECWIVE_ID_NO = "RECWIVE_ID_NO";

    public static final String COL_RECEIVE_ACCT_TYPE = "RECEIVE_ACCT_TYPE";

    public static final String COL_RECEIVE_NAME = "RECEIVE_NAME";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_WITHDRAW_STATUS = "WITHDRAW_STATUS";

    public static final String COL_ERROR_CODE = "ERROR_CODE";

    public static final String COL_ERROR_MSG = "ERROR_MSG";
}