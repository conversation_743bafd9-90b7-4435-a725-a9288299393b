package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.vo.SignRecordVo;

import java.util.List;
import java.util.Map;

/**
 * 签约信息表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-01-14
 */
public interface SignRecordFacade {
    /**
     * 获取签约信息
     * @param paramMap 查询条件
     * @return 签约信息
     */
    SignRecord getOne(Map<String, Object> paramMap)  throws BizException;

    List<SignRecord> listRecords(Map<String, Object> paramMap);

    /**
     * 插入记录
     * @param record 签约记录
     */
    void insert(SignRecord record)  throws BizException;

    /**
     * 更新记录
     * @param record 签约记录
     */
    void update(SignRecord record)  throws BizException;

    /**
     * 分页查询
     * @param paramMap 条件
     * @param pageParam 分页参数
     * @return 签约记录列表
     */
    PageResult<List<SignRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam)  throws BizException;

    /**
     * 预签约
     * @param record 签约记录
     * @param isNeedSendSms
     */
    void preSign(SignRecord record, boolean isNeedSendSms)  throws BizException;

    SignRecord getById(Long signId);

    boolean resend(SignRecord record) throws BizException;

    SignRecord isExist(SignRecordVo item)  throws BizException;

    boolean modify(SignRecordVo record)  throws BizException;

    boolean auth(SignRecord record) throws BizException;

    boolean isExistTemplate(String mainstayNo, String mchNo) throws BizException;

    void startSign(SignRecord build, boolean isNeedSendSms) throws BizException;

    void addSignImages(SignRecord signRecord) throws BizException;

    void resetSmsSendFrequency(SignRecord record) throws BizException;

    void addUserImage(UserInfo userInfo) throws BizException;

    void deleteById(Long id) throws BizException;

    /**
     * 是否签约接口
     * @param mainstayNo
     * @param employerNo
     * @param idCardMd5
     * @return
     * @throws BizException
     */
    boolean isSigned(String mainstayNo, String employerNo, String idCardMd5) throws BizException;
}
