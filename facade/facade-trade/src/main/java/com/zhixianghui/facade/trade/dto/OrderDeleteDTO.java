package com.zhixianghui.facade.trade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Description: //TODO
 * date: 2021/11/8 17:00
 */
@Data
public class OrderDeleteDTO implements Serializable {

    private static final long serialVersionUID = 8189157334236783862L;
    private String platBatchNo;

    private Date createTime;

//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginDate;

//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    private Integer isDelete;

    private Date updateTime;
}