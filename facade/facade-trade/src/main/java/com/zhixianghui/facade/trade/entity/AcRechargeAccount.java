package com.zhixianghui.facade.trade.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 充值账户信息
 * <AUTHOR>
 * @date : 2024/04/03 10:15:47
 **/
@Data
public class AcRechargeAccount implements Serializable {
    // 主键
    private Long id;

    // 创建时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 商户号
    private String mchNo;

    // 商户名称
    private String mchName;

    // 通道编号
    private String payChannelNo;

    // 通道编号
    private String payChannelName;

    // 供应商编号
    private String mainstayNo;

    // 供应商名称
    private String mainstayName;

    // 收款公司名称
    private String accountName;

    // 收款账号
    private String accountNo;

    // 银行名称
    private String accountBank;

    // 开户地信息
    private String accountLoc;

    // 支行信息
    private String accountBanch;

    // 联行号
    private String accountBkno;

    // 状态(100-激活；101-冻结)
    private Integer state;

    private static final long serialVersionUID = 1L;
}