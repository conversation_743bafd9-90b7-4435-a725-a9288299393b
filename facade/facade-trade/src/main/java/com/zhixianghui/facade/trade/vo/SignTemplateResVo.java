package com.zhixianghui.facade.trade.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zhixianghui.facade.trade.entity.SignTemplate;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/9 14:53
 */
@Data
public class SignTemplateResVo extends SignTemplate {

    /**
     * ID
     */
    protected Long id;

    /**
     * 版本号
     */
    protected Integer version;

    /**
     * 创建时间
     */
    protected Date createTime;

    /**
     * 更新时间
     */
    protected Date updateTime;

    /**
     * 模板id
     */
    protected String templateId;

    /**
     * 文件上传地址
     */
    protected String uploadUrl;

    /**
     * 组件id列表
     */
    protected String componentId;

    /**
     * 删除标识,1删除,0未删除
     */
    private Boolean deleteFlag;

    /**
     * 签署截止日期, 单位月
     */
    protected Integer signingDeadline;

    /**
     * 协议到期时间, 单位年
     */
    protected Integer agreementExpiresTime;

    /**
     * 签署类型，100 用工企业指定，101代征主体指定
     */
    protected Integer signTemplateType;

    /**
     * 源文件路径
     */
    protected String sourceFileUrl;

    /**
     * 目标文件路径
     */
    protected String targetFileUrl;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 组件明细
     */
    @JsonProperty("structComponent")
    private List<StructComponent> style;

    private List<MerchantInfo> merchantList;
    private List<MerchantInfo> employerList;

}
