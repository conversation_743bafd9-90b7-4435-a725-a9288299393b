package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 打款流水状态
 * @date 2020-11-09 17:24
 **/
@AllArgsConstructor
@Getter
@ToString
public enum RecordItemStatusEnum {
    /**
     * 已打款
     * 支付通道返回打款成功
     */
    PAY_SUCCESS(100,"已打款" ),
    /**
     * 打款失败
     * 支付通道返回打款失败
     */
    PAY_FAIL(200,"打款失败" ),
    /**
     * 已创建
     * 交易流水创建
     */
    PAY_CREATED(300,"已创建" ),

    /**
     * 退款中
     */
    REFUNDING(400,"退款中"),

    /**
     * 打款失败（已退款，退汇或者冲正)
     * ⽀付被退回
     */
    PAY_SPECIAL_FAIL(700,"打款失败（已退款，退汇或者冲正)" ),

    ;

    private final int value;
    private final String desc;
}
