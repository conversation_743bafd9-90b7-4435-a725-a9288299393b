package com.zhixianghui.facade.trade.vo.pay;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 无法引用banklink的PayRespVo，该实体用来中转
 * @createTime 2021年12月15日 16:09:00
 */
@Data
public class WxPayResVo implements Serializable {
    private static final long serialVersionUID = 3302224851955865035L;
    /**
     * 渠道请求订单号
     */
    private String bankOrderNo;
    /**
     * 渠道流水号
     */
    private String bankTrxNo;
    /**
     * 渠道付款状态
     * @see com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum
     */
    private Integer bankPayStatus;
    /**
     * 业务码
     */
    private String bizCode;
    /**
     * 业务信息
     */
    private String bizMsg;
}
