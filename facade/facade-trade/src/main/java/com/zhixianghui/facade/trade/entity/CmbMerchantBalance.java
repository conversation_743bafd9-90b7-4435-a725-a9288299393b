package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/16 15:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "tbl_cmb_merchant_balance")
public class CmbMerchantBalance implements Serializable {
    private static final long serialVersionUID = -7354423848091472134L;
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 商户编号
     */
    @TableField(value = "mch_no")
    private String mchNo;

    /**
     * 商户名称
     */
    @TableField(value = "mch_name")
    private String mchName;

    /**
     * 供应商编号
     */
    @TableField(value = "mainstay_no")
    private String mainstayNo;

    /**
     * 供应商名称
     */
    @TableField(value = "mainstay_name")
    private String mainstayName;

    /**
     * 总金额
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 冻结金额
     */
    @TableField(value = "freeze_amount")
    private BigDecimal freezeAmount;

    /**
     * 商户类型
     */
    @TableField(value = "merchant_type")
    private Integer merchantType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}
