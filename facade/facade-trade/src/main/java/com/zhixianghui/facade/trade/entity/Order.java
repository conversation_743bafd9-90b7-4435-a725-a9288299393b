package com.zhixianghui.facade.trade.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.Data;

/**
 * 订单批次表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Data
public class Order implements Serializable {

    private static final long serialVersionUID = -4556596464083296858L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 确认发放时间
     */
    private Date confirmTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 批次名
     */
    private String batchName;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 任务编号
     */
    private String jobId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 通道类型(发放方式) {@link com.zhixianghui.common.statics.enums.report.ChannelTypeEnum}
     */
    private Integer channelType;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名
     */
    private String channelName;

    /**
     * 批次状态 {@link com.zhixianghui.facade.trade.enums.OrderStatusEnum}
     */
    private Integer batchStatus;

    /**
     * 自由职业者服务编号
     */
    private String workCategoryCode;

    /**
     * 自由职业者服务名称
     */
    private String workCategoryName;

    /**
     * 自由职业者服务描述
     */
    private String serviceDesc;

    /**
     * 请求笔数
     */
    private Integer requestCount = 0;

    /**
     * 请求（总）任务金额
     */
    private BigDecimal requestTaskAmount = BigDecimal.ZERO;

    /**
     * 请求(总)实发金额
     */
    private BigDecimal requestNetAmount = BigDecimal.ZERO;

    /**
     * 已受理笔数
     */
    private Integer acceptedCount = 0;

    /**
     * 已受理（总）任务金额
     */
    private BigDecimal acceptedTaskAmount = BigDecimal.ZERO;

    /**
     * 已受理(总)实发金额
     */
    private BigDecimal acceptedNetAmount = BigDecimal.ZERO;

    /**
     * 已受理代征主体服务费
     */
    private BigDecimal acceptedFee = BigDecimal.ZERO;

    /**
     * 已受理(总)订单金额
     */
    private BigDecimal acceptedOrderAmount = BigDecimal.ZERO;

    /**
     * 已受理（总）个税金额
     */
    private BigDecimal acceptedTaxAmount = BigDecimal.ZERO;

    /**
     * 成功笔数
     */
    private Integer successCount = 0;

    /**
     * 成功（总）任务金额
     */
    private BigDecimal successTaskAmount = BigDecimal.ZERO;

    /**
     * 成功实发金额
     */
    private BigDecimal successNetAmount = BigDecimal.ZERO;

    /**
     * 成功代征主体服务费
     */
    private BigDecimal successFee = BigDecimal.ZERO;

    /**
     * 成功个税金额
     */
    private BigDecimal successTaxAmount = BigDecimal.ZERO;

    /**
     * 失败笔数
     */
    private Integer failCount = 0;

    /**
     * 失败任务金额
     */
    private BigDecimal failTaskAmount = BigDecimal.ZERO;

    /**
     * 失败实发金额
     */
    private BigDecimal failNetAmount = BigDecimal.ZERO;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorDesc;

    /**
     * 订单发起方式
     */
    private Integer launchWay;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * JSON数据
     */
    private String jsonStr;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * json字段的处理
     */
    private JsonEntity jsonEntity = new JsonEntity();

    @Data
    public static class JsonEntity implements Serializable {
        /**
         * 签名类型
         */
        String signType;
    }

    @Deprecated
    public String getJsonStr() {
        return JsonUtil.toString(jsonEntity);
    }

    @Deprecated
    public void setJsonStr(String jsonStr) {
        this.jsonStr = jsonStr;
        this.jsonEntity = JsonUtil.toBean(jsonStr,JsonEntity.class);
    }
}
