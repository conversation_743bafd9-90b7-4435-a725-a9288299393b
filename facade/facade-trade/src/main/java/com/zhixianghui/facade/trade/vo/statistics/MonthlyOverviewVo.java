package com.zhixianghui.facade.trade.vo.statistics;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年11月23日 09:41:00
 */
@Data
public class MonthlyOverviewVo implements Serializable {

    private static final long serialVersionUID = 3474642837814254694L;
    private String dateRange;

    private Item order;

    private Item merchant;

    private Item user;

    private List<Integer> xAxis;

    private Money money;

    private List<BigDecimal> moneys;

    private List<BigDecimal> lastMoneys;


    @Data
    @Accessors(chain = true)
    public static class Item implements Serializable{
        private static final long serialVersionUID = 3024257189236369178L;
        private Integer num;
        private Integer lastNum;
        private Integer subtractNum;
    }

    @Data
    @Accessors(chain = true)
    public static class Money implements Serializable{
        private static final long serialVersionUID = 8534148015010403281L;
        private BigDecimal money;
        private BigDecimal lastMoney;
        private BigDecimal subtractMoney;
    }
}