package com.zhixianghui.facade.trade.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@AllArgsConstructor
@Getter
public enum AdjustAccountTypeEnum {

    CMB_ADD(0,"增加"),

    CMB_SUB(1,"减少");

    private int value;

    private String desc;

    public static AdjustAccountTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
