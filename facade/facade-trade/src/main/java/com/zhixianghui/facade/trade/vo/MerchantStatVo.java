package com.zhixianghui.facade.trade.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/8/2 10:07
 */
@Data
public class MerchantStatVo implements Serializable {

    private Long id;
    private String employerNo;
    private String employerName;
    private String mainstayNo;
    private String mainstayName;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal orderItemNetAmount;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal orderItemAmount;
    private Long orderAmount;
    private Long receiverNumber;
    private Date createTime;
    private String createDate;
    private Long saleId;
    private String saleName;
    private String agentName;

    private Date firstOrderTime;

    // 总计
    private Integer employerCount;
    private String freelanceCount;
    private Long orderCount;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal orderItemNetAmountSum;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal orderAmountSum;


}
