package com.zhixianghui.facade.trade.utils;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;

import java.util.Random;

public class AcUtil {

    private static final String LOG_KEY_PREFIX = "AC_LOG_KEY";//幂等key前缀

    public static String getRedisLockKey(AcChangeFunds acChangeFunds) {
      return getRedisLockKey(acChangeFunds.getMchNo(), acChangeFunds.getMainstayNo(), acChangeFunds.getMerchantType(), acChangeFunds.getPayChannelNo());
    }

    public static String getRedisLockKey(String employerNo, String mainstayNo, Integer merchantType, String payChannelNo) {
        if (merchantType == MerchantTypeEnum.EMPLOYER.getValue()) {
            return TradeConstant.AC_AMOUNT_LOCK_KEY + "::" + employerNo + "::" + mainstayNo + "::" + payChannelNo;
        }
        return TradeConstant.AC_AMOUNT_LOCK_KEY + "::" + mainstayNo +"::"+payChannelNo;
    }

    public static String getLogKey(AcChangeFunds acChangeFunds) {
        return getLogKey(acChangeFunds.getPlatTrxNo(),acChangeFunds.getAmountChangeType(),acChangeFunds.getMerchantType(),acChangeFunds.getPayChannelNo());
    }

    public static String getLogKey(String platTrxNo, int type, int merchantType, String payChannelNo) {
        return LOG_KEY_PREFIX + "::" + platTrxNo + "::" + type + "::" + merchantType+"::"+payChannelNo;
    }

    public static int getRandomNumber(int ...nums) {
        Random random = new Random();
        int index = random.nextInt(nums.length);
        return nums[index];
    }
}
