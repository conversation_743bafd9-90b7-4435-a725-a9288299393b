package com.zhixianghui.facade.trade.vo;

import com.zhixianghui.facade.trade.entity.ProxyOrderItem;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class IndividualProxyOrderVo implements Serializable {

    private static final long serialVersionUID = -6985946153300143047L;
    private Long id;

    /**
     * 服务商编号
     */
    private String mainstayNo;

    /**
     * 服务商名称
     */
    private String mainstayName;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付流水号
     */
    private String payTrxNo;

    /**
     * 支付开始时间
     */
    private LocalDateTime payBeginTime;

    /**
     * 支付完成时间
     */
    private LocalDateTime payCompleteTime;

    /**
     * 订单完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 是否退款
     */
    private Boolean isRefund = Boolean.FALSE;

    /**
     * 退款开始时间
     */
    private LocalDateTime refundBeginTime;

    /**
     * 退款完成时间
     */
    private LocalDateTime refundCompleteTime;

    /**
     * 退款原因
     */
    private Integer refundReason;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 付款状态
     */
    private Integer payStatus;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 发票信息
     */
    private List<ProxyOrderItem> invoiceDetail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资金流水
     */
    private List<String> fundFlowFile;

    /**
     * 业务合同
     */
    private List<String> businessContractFile;

    /**
     * 委托协议
     */
    private List<String> entrustAgreementFile;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 抬头ID
     */
    private Long invoiceTitleId;

    /**
     * 抬头税号
     */
    private String invoiceTitleTaxNo;

    /**
     * 抬头公司名称
     */
    private String invoiceTitleCompanyName;

    /**
     * 开票人
     */
    private String invoiceApplicant;

    /**
     * 收票地址id
     */
    private Long addressId;

    /**
     * 收票人详细地址
     */
    private String addressDetail;

    /**
     * 收票人姓名
     */
    private String addressName;

    /**
     * 收票人电话
     */
    private String addressMobile;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private ProxyPayDetailVo feeDetail;

    private List<String> invoiceUrl;

    private String expressNo;

    private LocalDateTime expireTime;

    private String errorRemark;

    private String userName;

    private String userIdcardNo;

    private String userMobile;

    private List<String> userIdcardImg;
}
