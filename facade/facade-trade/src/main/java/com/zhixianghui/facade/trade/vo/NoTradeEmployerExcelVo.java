package com.zhixianghui.facade.trade.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class NoTradeEmployerExcelVo {
    @ExcelProperty(value = "商户编号")
    private String mchNo;
    @ExcelProperty(value = "商户名称")
    private String mchName;
    @ExcelProperty(value = "商户激活时间")
    private String activeTime;
    @ExcelProperty(value = "平台最后发放时间")
    private String lastOrderTime;
    @ExcelProperty(value = "休眠时长(天)")
    private Long sleepDate;
}
