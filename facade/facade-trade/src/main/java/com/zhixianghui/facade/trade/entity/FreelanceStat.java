package com.zhixianghui.facade.trade.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自由职业者月统计表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-08-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FreelanceStat implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 持卡人身份证号MD5
     */
    private String receiveIdCardNoMd5;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 订单明细实发金额
     */
    private BigDecimal orderItemNetAmount;

    /**
     * 平台总接单量
     */
    private Long receiverOrder;

    /**
     * 条件接单量
     */
    private Long conditionOrder;

    /**
     * 是否签约
     */
    private Integer signRecord;

    /**
     * 是否上传身份证
     */
    private Integer idCard;

    /**
     * 密钥ID
     */
    private Long encryptKeyId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建日期
     */
    private String createDate;

    private Date updateTime;

    private String receivePhoneNo;

    private String receiveNameMd5;

    private Long signId;



}
