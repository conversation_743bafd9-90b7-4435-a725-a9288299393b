package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.WxIncomeRecord;

import java.util.List;
import java.util.Map;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-12-07
 */
public interface WxIncomeRecordFacade {

    WxIncomeRecord getById(Long id);

    void update(WxIncomeRecord wxIncomeRecord);

    PageResult<List<WxIncomeRecord>> listPage(Map<String, Object> paramMap, PageParam toPageParam);
}
