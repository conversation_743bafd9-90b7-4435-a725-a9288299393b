package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 导入失败记录表
    */
@Data
@TableName(value = "tbl_order_item_fail")
public class OrderItemFail extends BasePrivateItem {
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 平台批次号
     */
    @TableField(value = "PLAT_BATCH_NO")
    private String platBatchNo;

    /**
     * 错误行
     */
    @TableField(value = "LINE")
    private Integer line;

    /**
     * 错误详情
     */
    @TableField(value = "ERROR_DESC")
    private String errorDesc;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    private Short version;

    /**
     * 商户订单号
     */
    @TableField(value = "MCH_ORDER_NO")
    private String mchOrderNo;
//    /**
//     * 收款账户
//     */
//    @TableField(value = "RECEIVE_ACCOUNT_NO")
//    private String receiveAccountNo;
//
//    /**
//     * 收款人姓名
//     */
//    @TableField(value = "RECEIVE_NAME")
//    private String receiveName;
//
//    /**
//     * 收款人身份证号码
//     */
//    @TableField(value = "RECEIVE_ID_CARD_NO")
//    private String receiveIdCardNo;
//
//    /**
//     * 收款人手机号
//     */
//    @TableField(value = "RECEIVE_PHONE_NO")
//    private String receivePhoneNo;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_PLAT_BATCH_NO = "PLAT_BATCH_NO";

    public static final String COL_LINE = "LINE";

    public static final String COL_RECEIVE_ACCOUNT_NO = "RECEIVE_ACCOUNT_NO";

    public static final String COL_RECEIVE_NAME = "RECEIVE_NAME";

    public static final String COL_RECEIVE_ID_CARD_NO = "RECEIVE_ID_CARD_NO";

    public static final String COL_RECEIVE_PHONE_NO = "RECEIVE_PHONE_NO";

    public static final String COL_ERROR_DESC = "ERROR_DESC";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_MCH_ORDER_NO = "MCH_ORDER_NO";
}