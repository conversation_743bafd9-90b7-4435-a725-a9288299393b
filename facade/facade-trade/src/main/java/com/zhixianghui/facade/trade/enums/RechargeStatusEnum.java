package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RechargeStatusEnum {

    NEW(0, "新增"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败");

    private final Integer code;
    private final String message;

    public static RechargeStatusEnum getEnum(int code) {
        return Arrays.stream(values()).filter(p -> p.code == code).findFirst().orElse(null);
    }
}
