package com.zhixianghui.facade.trade.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:30
 */
@Data
public class CmbChangesFundsParamDTO implements Serializable {
    private static final long serialVersionUID = 7375128000558573678L;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 平台批次号，资金下发不为空
     */
    private String platBatchNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 资金变动类型
     */
    private Integer amountChangeType;

    /**
     * 唯一键序列号
     */
    private String logKeyNo;


}
