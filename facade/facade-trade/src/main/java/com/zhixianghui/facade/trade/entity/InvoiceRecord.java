package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.util.utils.StringUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 发票申请记录表
 * <AUTHOR>
 */
@Data
public class InvoiceRecord extends BaseEntity{

	private static final long serialVersionUID = 1L;

	/**
	 * 更新时间
	 */
	private java.util.Date updateTime;

	/**
	 * 完成时间
	 */
	private java.util.Date completeTime;

	/**
	 * 产品编号
	 */
	private String productNo;

	/**
	 * 产品名称
	 */
	private String productName;

	/**
	 * 用工企业编号
	 */
	private String employerMchNo;

	/**
	 * 用工企业名称
	 */
	private String employerMchName;

	/**
	 * 代征主体编号
	 */
	private String mainstayMchNo;

	/**
	 * 代征主体名称
	 */
	private String mainstayMchName;

	/**
	 * 发票流水号
	 */
	private String trxNo;

	/**
	 * 发票类型
	 * @see com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum
	 */
	private Integer invoiceType;

	/**
	 * 申请类型
	 * @see com.zhixianghui.common.statics.enums.invoice.ApplyTypeEnum
	 */
	private Integer applyType;

	/**
	 * 开票金额类型
	 * @see com.zhixianghui.common.statics.enums.invoice.InvoiceAmountTypeEnum
	 */
	private Integer amountType;

	/**
	 * 开票金额
	 */
	private java.math.BigDecimal invoiceAmount;

	/**
	 * 预开票金额
	 */
	private java.math.BigDecimal invoicePreAmount;

	/**
	 * 预开票Ids
	 */
	private String invoicePreIds;

	/**
	 * 开票交易时间段起始时间
	 */
	private String tradeCompleteDayBegin;

	/**
	 * 开票交易时间段终止时间
	 */
	private String tradeCompleteDayEnd;

	/**
	 * 发票类目编码
	 */
	private String invoiceCategoryCode;

	/**
	 * 发票类目名称
	 */
	private String invoiceCategoryName;

	/**
	 * 发票开具状态
	 * @see com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum
	 */
	private Integer invoiceStatus;

	/**
	 * 发票账务处理状态
	 * @see com.zhixianghui.common.statics.enums.invoice.AccountHandleStatusEnum
	 */
	private Integer accountHandleStatus;

	/**
	 * 发票收件人
	 */
	private String expressConsignee;

	/**
	 * 收件人联系电话
	 */
	private String expressTelephone;

	/**
	 * 邮寄地址-省
	 */
	private String province;

	/**
	 * 邮寄地址-市
	 */
	private String city;

	/**
	 * 邮寄地址-区/县
	 */
	private String county;

	/**
	 * 邮寄地址-详细地址
	 */
	private String address;

	/**
	 * 快递公司
	 */
	private String expressCompany;

	/**
	 * 快递单号
	 */
	private String expressNo;

	/**
	 * 纳税人类型
	 */
	private Integer taxPayerType;

	/**
	 * 税号
	 */
	private String taxNo;

	/**
	 * 注册地址-电话
	 */
	private String registerAddrInfo;

	/**
	 * 银行账户
	 */
	private String accountNo;

	/**
	 * 银行名称
	 */
	private String bankName;

	/**
	 * 发票影像文件url
	 */
	private String invoiceFileUrl;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * json格式，其他信息
	 */
	private Object jsonInfo;

	/**
	 * 错误描述
	 */
	private String errorDesc;

	private String jobId;

	private String jobName;

	private List<String> invoiceFileUrlList;

	private Integer source;

	private String workerBillFilePath;

	private String serviceFeeBillPath;

	private BigDecimal serviceFeeAmount;

	private Integer payMethod;

	private Integer category;

	private String workCategoryCode;

	private String workCategoryName;

	public void setInvoiceFileUrl(String invoiceFileUrl){
		this.invoiceFileUrl = invoiceFileUrl;
		if (StringUtil.isNotEmpty(invoiceFileUrl)){
			this.invoiceFileUrlList = Arrays.asList(invoiceFileUrl.split(","));
		}
	}

	@TableField(exist = false)
	private String postAddress;
}
