package com.zhixianghui.facade.trade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ProxyOrderQueryDto implements Serializable {
    private static final long serialVersionUID = -683426284728823658L;
    private String mainstayNo;
    private String mainstayNameLike;
    private String orderNo;
    private String invoiceTitleCompanyNameLike;
    private String invoiceApplicant;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTimeBegin;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTimeEnd;
    private String idCardNo;
    private Integer orderStatus;
    private Integer current;
    private Integer size;
}
