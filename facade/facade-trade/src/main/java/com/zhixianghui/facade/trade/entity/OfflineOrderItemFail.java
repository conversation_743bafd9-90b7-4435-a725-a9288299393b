package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
    * 导入失败记录表
    */
@Data
@TableName(value = "tbl_offline_order_item_fail")
public class OfflineOrderItemFail extends BasePrivateItem implements Serializable {
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 平台批次号
     */
    @TableField(value = "PLAT_BATCH_NO")
    private String platBatchNo;

    /**
     * 错误行
     */
    @TableField(value = "LINE")
    private Integer line;


    /**
     * 错误详情
     */
    @TableField(value = "ERROR_DESC")
    private String errorDesc;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    private Integer version;

    /**
     * 银行预留手机号MD5
     */
    @TableField(value = "RECEIVE_PHONE_NO_MD5")
    private String receivePhoneNoMd5;

    /**
     * 商户订单号
     */
    @TableField(value = "MCH_ORDER_NO")
    private String mchOrderNo;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_PLAT_BATCH_NO = "PLAT_BATCH_NO";

    public static final String COL_LINE = "LINE";

    public static final String COL_RECEIVE_ACCOUNT_NO = "RECEIVE_ACCOUNT_NO";

    public static final String COL_RECEIVE_NAME = "RECEIVE_NAME";

    public static final String COL_RECEIVE_ID_CARD_NO = "RECEIVE_ID_CARD_NO";

    public static final String COL_RECEIVE_PHONE_NO = "RECEIVE_PHONE_NO";

    public static final String COL_ERROR_DESC = "ERROR_DESC";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_RECEIVE_NAME_MD5 = "RECEIVE_NAME_MD5";

    public static final String COL_RECEIVE_ID_CARD_NO_MD5 = "RECEIVE_ID_CARD_NO_MD5";

    public static final String COL_RECEIVE_ACCOUNT_NO_MD5 = "RECEIVE_ACCOUNT_NO_MD5";

    public static final String COL_RECEIVE_PHONE_NO_MD5 = "RECEIVE_PHONE_NO_MD5";

    public static final String COL_ENCRYPT_KEY_ID = "ENCRYPT_KEY_ID";

    public static final String COL_MCH_ORDER_NO = "MCH_ORDER_NO";
}
