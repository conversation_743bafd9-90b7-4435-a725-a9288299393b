package com.zhixianghui.facade.trade.dto;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月23日 10:23:00
 */
@Data
public class ChangesFoundsDTO implements Serializable {
    private static final long serialVersionUID = -1528837912499969795L;

    /**
     * 当前页数，默认为1，必须设置默认值，否则分页查询时容易报空指针异常
     */
    private int pageCurrent = 1;
    /**
     * 每页记录数，默认为20，必须设置默认值，否则分页查询时容易报空指针异常
     */
    private int pageSize = 20;

    /**
     * 唯一键
     */
    private String logKey;

    /**
     * 所属商户编号
     */
    private String mchNo;

    /**
     * 所属商户名称
     */
    private String mchName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 资金变动类型
     */
    private Integer amountChangeType;

    /**
     * 创建时间
     */
    private String createTimeBegin;

    /**
     * 创建时间
     */
    private String createTimeEnd;

}