package com.zhixianghui.facade.trade.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 **/
@Data
public class InvoiceUpdateVo implements Serializable {
    /**
     * 状态
     */
    @NotNull(message = "发票状态不能为空")
    private Integer invoiceStatus;
    /**
     * 流水号
     */
    @NotEmpty(message = "流水号不能为空")
    private String trxNo;
    // 发票影印文件
    private String invoiceFileUrl;
    // 异常原因描述
    @Length(max = 200, message = "异常原因反馈长度不能超过200")
    private String errorDesc;
    // 快递单号
    @Length(max = 200, message = "快递单号长度不能超过200")
    private String expressNo;

    private List<String> invoiceFileUrlList;

    public String getInvoiceFileUrl(){
        if (invoiceFileUrlList.size() > 0){
            this.invoiceFileUrl = StringUtils.join(invoiceFileUrlList,",");
        }
        return this.invoiceFileUrl;
    }
}
