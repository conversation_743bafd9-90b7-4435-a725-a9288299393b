package com.zhixianghui.facade.trade.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.entity.TaxCertificateRecord;

import java.util.List;
import java.util.Map;

public interface TaxCertificateRecordFacade {
    TaxCertificateRecord save(TaxCertificateRecord taxCertificateRecord) throws BizException;

    TaxCertificateRecord update(TaxCertificateRecord taxCertificateRecord) throws BizException;

    TaxCertificateRecord getById(Integer id) throws BizException;

    Page<TaxCertificateRecord> listPage(Page<TaxCertificateRecord> page, Map<String, Object> paramMap) throws  BizException;

    List<TaxCertificateRecord> list(Map<String, Object> paramMap) throws BizException;

    boolean delete(Integer id) throws BizException;
}
