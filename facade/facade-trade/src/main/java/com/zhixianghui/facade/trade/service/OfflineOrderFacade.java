package com.zhixianghui.facade.trade.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.entity.OfflineOrder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface OfflineOrderFacade {
    void saveOrder(OfflineOrder offlineOrder) throws BizException;

    OfflineOrder getById(Long id) throws BizException;

    Page<OfflineOrder> pageOrder(Page<OfflineOrder> page, Map<String, Object> param) throws BizException;

    List<OfflineOrder> listBy(Map<String, Object> param) throws BizException;

    OfflineOrder getByPlatBatchNo(String platBatchNo) throws BizException;

    OfflineOrder getOne(Map<String, Object> param) throws BizException;

    void update(OfflineOrder offlineOrder) throws BizException;

    void startAccept(OfflineOrder offlineOrder) throws BizException;

    void startGrant(String platBatchNo) throws BizException;

    BigDecimal sumWaitInvoiceAmount(Map<String, Object> paramMap);

    void cancelBatchOrder(OfflineOrder order);

    void delete(OrderDeleteDTO orderDeleteDTO);
}
