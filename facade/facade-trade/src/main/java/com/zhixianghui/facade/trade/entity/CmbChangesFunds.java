package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/16 15:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "tbl_cmb_changes_funds")
public class CmbChangesFunds implements Serializable {
    private static final long serialVersionUID = -109601213796587341L;
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 唯一键
     */
    @TableField(value = "log_key")
    private String logKey;

    /**
     * 商户类型
     */
    @TableField(value = "merchant_type")
    private Integer merchantType;

    /**
     * 商户编号
     */
    @TableField(value = "mch_no")
    private String mchNo;

    /**
     * 商户名称
     */
    @TableField(value = "mch_name")
    private String mchName;

    /**
     * 供应商编号
     */
    @TableField(value = "mainstay_no")
    private String mainstayNo;

    /**
     * 供应商名称
     */
    @TableField(value = "mainstay_name")
    private String mainstayName;

    /**
     * 订单号
     */
    @TableField(value = "plat_trx_no")
    private String platTrxNo;

    /**
     * 平台批次号，资金下发不为空
     */
    @TableField(value = "plat_batch_no")
    private String platBatchNo;


    /**
     * 资金变动类型
     */
    @TableField(value = "amount_change_type")
    private Integer amountChangeType;


    /**
     * 变动金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 变动前金额
     */
    @TableField(value = "before_amount")
    private BigDecimal beforeAmount;

    /**
     * 变动后金额
     */
    @TableField(value = "after_amount")
    private BigDecimal afterAmount;

    /**
     * 冻结金额
     */
    @TableField(value = "frozen_amount")
    private BigDecimal frozenAmount;


    /**
     * 变动前冻结金额
     */
    @TableField(value = "before_frozen_amount")
    private BigDecimal beforeFrozenAmount;


    /**
     * 变动后冻结金额
     */
    @TableField(value = "after_frozen_amount")
    private BigDecimal afterFrozenAmount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 操作人
     */
    @TableField(value = "operator")
    private String operator;

}
