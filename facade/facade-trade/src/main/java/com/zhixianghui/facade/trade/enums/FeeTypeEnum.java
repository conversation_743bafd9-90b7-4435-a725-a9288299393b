package com.zhixianghui.facade.trade.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName FeeTypeEnum
 * @Description TODO
 * @Date 2022/7/7 15:42
 */
@Getter
@AllArgsConstructor
@EnumDesc(name = "交付类型", type = SystemTypeEnum.CKH_MANAGEMENT)
public enum  FeeTypeEnum {

    TAX_ORDER(100,"个税账单"),

    SERVICE_FEE_ORDER(101,"服务费账单");

    private final Integer code;
    private final String desc;

    public static FeeTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.code.intValue() == value).findFirst().orElse(null);
    }
}
