package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.ValidateUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
    * 开票明细表
    */
@Data
@TableName(value = "tbl_invoice_record_detail",autoResultMap = true)
public class InvoiceRecordDetail implements Serializable {
    private static final long serialVersionUID = -7339077676933789629L;
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 发票类型
     */
    @TableField(value = "INVOICE_TYPE")
    private Integer invoiceType;

    /**
     * 发票流水号
     */
    @TableField(value = "INVOICE_TRX_NO")
    private String invoiceTrxNo;

    /**
     * 平台流水号
     */
    @TableField(value = "PLAT_TRX_NO")
    private String platTrxNo;

    /**
     * 产品编号
     */
    @TableField(value = "PRODUCT_NO")
    private String productNo;

    /**
     * 产品名称
     */
    @TableField(value = "PRODUCT_NAME")
    private String productName;

    /**
     * 开票金额
     */
    @TableField(value = "INVOICE_AMOUNT")
    private BigDecimal invoiceAmount;

    /**
     * 发票影像文件url
     */
    @TableField(value = "INVOICE_FILE_URL", typeHandler = FastjsonTypeHandler.class)
    private List<String> invoiceFileUrl;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 用工企业编号
     */
    @TableField(value = "EMPLOYER_MCH_NO")
    private String employerMchNo;

    /**
     * 用工企业名称
     */
    @TableField(value = "EMPLOYER_MCH_NAME")
    private String employerMchName;

    /**
     * 代征主体编号
     */
    @TableField(value = "MAINSTAY_MCH_NO")
    private String mainstayMchNo;

    /**
     * 代征主体名称
     */
    @TableField(value = "MAINSTAY_MCH_NAME")
    private String mainstayMchName;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 创建时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 完成时间
     */
    @TableField(value = "COMPLETE_TIME")
    private Date completeTime;

    /**
     * 状态
     */
    @TableField(value = "INVOICE_STATUS")
    private Integer invoiceStatus;

    /**
     * 持卡人姓名
     */
    @TableField(value = "RECEIVE_NAME")
    private String receiveName;

    /**
     * 持卡人姓名MD5
     */
    @TableField(value = "RECEIVE_NAME_MD5")
    private String receiveNameMd5;

    /**
     * 持卡人身份证号
     */
    @TableField(value = "RECEIVE_ID_CARD_NO")
    private String receiveIdCardNo;

    /**
     * 持卡人身份证号MD5
     */
    @TableField(value = "RECEIVE_ID_CARD_NO_MD5")
    private String receiveIdCardNoMd5;

    /**
     * 收款账户
     */
    @TableField(value = "RECEIVE_ACCOUNT_NO")
    private String receiveAccountNo;

    /**
     * 收款账户MD5
     */
    @TableField(value = "RECEIVE_ACCOUNT_NO_MD5")
    private String receiveAccountNoMd5;

    /**
     * 银行预留手机号
     */
    @TableField(value = "RECEIVE_PHONE_NO")
    private String receivePhoneNo;

    /**
     * 银行预留手机号MD5
     */
    @TableField(value = "RECEIVE_PHONE_NO_MD5")
    private String receivePhoneNoMd5;

    /**
     * 密钥ID
     */
    @TableField(value = "ENCRYPT_KEY_ID")
    private Long encryptKeyId;

    @TableField(value = "ACCOUNT_HANDLE_STATUS")
    private Integer accountHandleStatus;

    @TableField(exist = false)
    private List<String> invoiceFileUrlList;

    @TableField(value = "ERROR_DESC")
    private String errorDesc;

    @TableField(value = "EXPRESS_COMPANY")
    private String expressCompany;

    @TableField(value = "EXPRESS_NO")
    private String expressNo;

    public void setInvoiceFileUrl(List<String> invoiceFileUrl) {
        this.invoiceFileUrl = invoiceFileUrl;
        this.invoiceFileUrlList = invoiceFileUrl;
    }

    @TableField(value = "WORKER_BILL_FILE_PATH")
    private String workerBillFilePath;

    public void setReceiveNameEncrypt(String receiveName) {
        if(StringUtils.isNotEmpty(receiveName)){
            this.receiveName = AESUtil.encryptECB(receiveName, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receiveName);
            this.receiveNameMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceiveNameDecrypt() {
        return AESUtil.decryptECB(this.receiveName, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setReceiveIdCardNoEncrypt(String receiveIdCardNo) {
        if(StringUtils.isNotEmpty(receiveIdCardNo)){
            this.receiveIdCardNo = AESUtil.encryptECB(receiveIdCardNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receiveIdCardNo);
            this.receiveIdCardNoMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceiveIdCardNoDecrypt() {
        return AESUtil.decryptECB(this.receiveIdCardNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setReceiveAccountNoEncrypt(String receiveAccountNo) {
        if(StringUtils.isNotEmpty(receiveAccountNo)) {
            this.receiveAccountNo = AESUtil.encryptECB(receiveAccountNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receiveAccountNo);
            this.receiveAccountNoMd5 = md5Str==null?"":md5Str;
        }
    }



    public String getReceiveAccountNoDecrypt() {
        return AESUtil.decryptECB(this.receiveAccountNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    public void setReceivePhoneNoEncrypt(String receivePhoneNo) {
        if(StringUtils.isNotEmpty(receivePhoneNo)) {
            this.receivePhoneNo = AESUtil.encryptECB(receivePhoneNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
            String md5Str = MD5Util.getMixMd5Str(receivePhoneNo);
            this.receivePhoneNoMd5 = md5Str==null?"":md5Str;
        }
    }

    public String getReceivePhoneNoDecrypt() {
        return AESUtil.decryptECB(this.receivePhoneNo, EncryptKeys.getEncryptKeyById(this.getEncryptKeyId()).getEncryptKeyStr());
    }

    //脱敏

    /**
     * 收款人名脱敏
     * @return 脱敏的收款人名
     */
    public String getReceiveNameDesensitize(){
        String receiveName = getReceiveNameDecrypt();
        if(StringUtils.isBlank(receiveName)){
            return "";
        }
        return DesensitizeUtil.handleNameDesenCenter(receiveName);
    }

    /**
     * 身份证脱敏
     * @return 脱敏的身份证
     */
    public String getReceiveIdCardNoDesensitize(){
        String receiveIdCardNo = getReceiveIdCardNoDecrypt();
        if(StringUtils.isBlank(receiveIdCardNo)){
            return "";
        }
        return DesensitizeUtil.handleIdNumOrBankCardNo(receiveIdCardNo);
    }

    /**
     * 收款账号脱敏
     * @return 脱敏的收款账号
     */
    public String getReceiveAccountNoDesensitize(){
        //收款账号可能是银行卡、手机、邮箱
        String receiveAccountNo = getReceiveAccountNoDecrypt();
        if(StringUtils.isBlank(receiveAccountNo)){
            return "";
        }
        if(StringUtils.isNotEmpty(receiveAccountNo)){
            if(ValidateUtil.isMobile(receiveAccountNo)){
                return DesensitizeUtil.handleMobile(receiveAccountNo);
            } else if(ValidateUtil.isEmail(receiveAccountNo)){
                return DesensitizeUtil.handleEmailDesenCenter(receiveAccountNo);
            } else{
                return DesensitizeUtil.handleIdNumOrBankCardNo(receiveAccountNo);
            }
        }
        return null;
    }

    /**
     * 手机号脱敏
     * @return 脱敏的手机号
     */
    public String getReceivePhoneNoDesensitize(){
        String receivePhone = getReceivePhoneNoDecrypt();
        if(StringUtils.isBlank(receivePhone)){
            return "";
        }
        return DesensitizeUtil.handleMobile(receivePhone);
    }

    public static final String COL_ID = "ID";

    public static final String COL_INVOICE_TYPE = "INVOICE_TYPE";

    public static final String COL_INVOICE_TRX_NO = "INVOICE_TRX_NO";

    public static final String COL_PLAT_TRX_NO = "PLAT_TRX_NO";

    public static final String COL_PRODUCT_NO = "PRODUCT_NO";

    public static final String COL_PRODUCT_NAME = "PRODUCT_NAME";

    public static final String COL_INVOICE_AMOUNT = "INVOICE_AMOUNT";

    public static final String COL_INVOICE_FILE_URL = "INVOICE_FILE_URL";

    public static final String COL_REMARK = "REMARK";

    public static final String COL_EMPLOYER_MCH_NO = "EMPLOYER_MCH_NO";

    public static final String COL_EMPLOYER_MCH_NAME = "EMPLOYER_MCH_NAME";

    public static final String COL_MAINSTAY_MCH_NO = "MAINSTAY_MCH_NO";

    public static final String COL_MAINSTAY_MCH_NAME = "MAINSTAY_MCH_NAME";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_COMPLETE_TIME = "COMPLETE_TIME";

    public static final String COL_INVOICE_STATUS = "INVOICE_STATUS";

    public static final String COL_RECEIVE_NAME = "RECEIVE_NAME";

    public static final String COL_RECEIVE_NAME_MD5 = "RECEIVE_NAME_MD5";

    public static final String COL_RECEIVE_ID_CARD_NO = "RECEIVE_ID_CARD_NO";

    public static final String COL_RECEIVE_ID_CARD_NO_MD5 = "RECEIVE_ID_CARD_NO_MD5";

    public static final String COL_RECEIVE_ACCOUNT_NO = "RECEIVE_ACCOUNT_NO";

    public static final String COL_RECEIVE_ACCOUNT_NO_MD5 = "RECEIVE_ACCOUNT_NO_MD5";

    public static final String COL_RECEIVE_PHONE_NO = "RECEIVE_PHONE_NO";

    public static final String COL_RECEIVE_PHONE_NO_MD5 = "RECEIVE_PHONE_NO_MD5";

    public static final String COL_ENCRYPT_KEY_ID = "ENCRYPT_KEY_ID";
}
