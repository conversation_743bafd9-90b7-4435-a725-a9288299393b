package com.zhixianghui.facade.trade.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月12日 10:01:00
 */
@Data
public class FeeOrderVo implements Serializable {
    private static final long serialVersionUID = 6457837788886226700L;

    private String feeBatchNo;

    private String employerName;


    private String employerNo;

    /**
     * 供应商名称
     */
    private String mainstayNo;
    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 账单状态
     */
    private Integer status;

    /**
     * 结算模式
     */
    private Integer balancedMode;

    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8" )
    private Date startDate;
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8" )
    private Date endDate;

    private String sortColumns;

    private String feeSource;
}