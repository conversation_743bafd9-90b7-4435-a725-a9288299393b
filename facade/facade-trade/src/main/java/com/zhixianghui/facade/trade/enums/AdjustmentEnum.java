package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月05日 10:32:00
 */
@AllArgsConstructor
@Getter
@ToString
public enum AdjustmentEnum {

    ADD(1,"增加"),

    DEL(2,"减少");

    private final Integer value;
    private final String desc;


    public static AdjustmentEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

}