package com.zhixianghui.facade.trade.dto;

import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OfflineInvoiceApplyDto implements Serializable {
    private static final long serialVersionUID = 2272753154914732838L;
    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    /**
     * 任务ID
     */
    private String jobId;

    /**
     * 开票人员名单
     */
    @NotNull(message = "开票人员名单缺失")
    /**
     * 开票人员名单
     */
    private List<InvoiceWorkerItem> invoiceWorkerItems;

    @NotEmpty(message = "代征主体编号 不能为空")
    /**
     * 代征主体编号
     */
    private String mainstayMchNo;

    @NotEmpty(message = "代征主体名称 不能为空")
    /**
     * 代征主体名称
     */
    private String mainstayMchName;

    @NotNull(message = "发票类型 不能为空")
    @EnumValue(intValues = {1,2}, message = "发票类型 有误")
    /**
     * 发票类型
     */
    private Integer invoiceType;

    @NotEmpty(message = "发票类目编码 不能为空")
    /**
     * 发票类目编码
     */
    private String invoiceCategoryCode;

    @NotEmpty(message = "发票类目名称 不能为空")
    /**
     * 发票类目名称
     */
    private String invoiceCategoryName;

    @Length(max = 200, message = "开票说明长度不能超过200")
    /**
     * 开票说明
     */
    private String remark;

    @NotNull(message = "上传的文件不能为空")
    /**
     * 文件ID
     */
    private String workerBillFilePath;

    private String employerNo;

    private String employerName;

    /**
     * 服务费凭证路径
     */
    private String serviceFeeBillPath;

    /**
     * 服务端
     */
    private BigDecimal serviceFeeAmount;

    /**
     * 支付方式
     */
    private Integer payMethod;


    /**
     * false 未支付
     * true 已支付
     */
    /**
     * 支付状态
     */
    private boolean payed;
}
