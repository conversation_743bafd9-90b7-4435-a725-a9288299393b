package com.zhixianghui.facade.trade.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单明细统计
 * <AUTHOR>
 * @date 2021/1/19
 **/
@Data
public class OrderItemSumBo implements Serializable {
    /**
     * 总实发金额
     */
    private BigDecimal totalNetAmount = BigDecimal.ZERO;

    /**
     * 总手续费
     */
    private BigDecimal totalFee = BigDecimal.ZERO;

    /**
     * 总订单金额
     */
    private BigDecimal totalOrderAmount = BigDecimal.ZERO;

    /**
     * 总订单笔数
     */
    private Integer totalNum = 0;
}
