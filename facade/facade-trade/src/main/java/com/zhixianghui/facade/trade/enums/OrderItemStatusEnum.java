package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 订单明细状态
 * @date 2020-11-05 10:15
 **/
@AllArgsConstructor
@Getter
@ToString
public enum OrderItemStatusEnum {
    /**
     *已发放
     */
    GRANT_SUCCESS(100,"已发放"),
    /**
     *失败
     */
    GRANT_FAIL(200,"失败"),
    /**
     *已创建
     */
    CREATE(300, "已创建"),
    /**
     *已受理（等待发放）
     */
    ACCEPTED(400, "已受理（等待发放）"),
    /**
     *发放中
     */
    GRANTING(500,"发放中"),
    /**
     *挂单（暂停处理）
     */
    GRANT_HANG(600,"挂单（暂停处理）"),
    /**
     *发放失败（已退款，退汇或者冲正)
     */
    GRANT_SPECIAL_FAIL(700,"发放失败（已退款，退汇或者冲正)"),
    /**
     *取消发放
     */
    GRANT_CANCEL(800,"取消发放"),
    ;

    private final int value;
    private final String desc;

    public static OrderItemStatusEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
