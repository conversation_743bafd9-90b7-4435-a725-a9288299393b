package com.zhixianghui.facade.trade.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.trade.dto.CmbIncomeRecordParamDTO;
import com.zhixianghui.facade.trade.dto.IdParamDTO;
import com.zhixianghui.facade.trade.vo.CmbIncomeAuditVO;
import com.zhixianghui.facade.trade.vo.CmbIncomeRecordVO;
import com.zhixianghui.facade.trade.vo.MerchantInfoVO;

import javax.validation.Valid;
import java.util.List;

public interface CmbIncomeRecordFacade {

    /**
     * 审核来账记录
     *
     * @param param
     * @return
     */
    RestResult<String> auditCmbIncomeRecord(@Valid CmbIncomeAuditVO param);

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    IPage<CmbIncomeRecordVO> listPage(CmbIncomeRecordParamDTO param);

    /**
     * 查新来账商户信息
     *
     * @param param
     * @return
     */
    List<MerchantInfoVO> listMerchantInfo(IdParamDTO param);

    /**
     * 导出来账记录
     *
     * @param param
     * @param loginName
     */
    void exportCmbIncomeRecord(CmbIncomeRecordParamDTO param, String loginName);
}
