package com.zhixianghui.facade.trade.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName FeeTypeEnum
 * @Description TODO
 * @Date 2022/7/7 15:42
 */
@Getter
@AllArgsConstructor
@EnumDesc(name = "开票来源", type = SystemTypeEnum.CKH_MANAGEMENT)
public enum InvoiceSourceEnum {

    ON_LINE(1,"在线"),

    OFF_LINE(2,"离线");

    private final Integer code;
    private final String desc;

    public static InvoiceSourceEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.code.intValue() == value).findFirst().orElse(null);
    }
}
