package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/19 15:16
 */
@Data
public class DeductionCommissionDTO implements Serializable {

    private static final long serialVersionUID = -6390643970821375093L;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 批次号
     */
    private String platBatchNo;

    /**
     * 服务费
     */
    private BigDecimal feeAmount;

}
