package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/19 14:51
 */
@Data
public class FreezeBalanceDTO implements Serializable {
    private static final long serialVersionUID = 5964425384322784946L;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 金额
     */
    private BigDecimal frozenAmount;
}
