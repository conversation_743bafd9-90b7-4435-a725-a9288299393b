package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.RechargeRecord;

import java.util.List;
import java.util.Map;

public interface RechargeQueryFacade {

    /**
     * 分页查询充值列表
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<RechargeRecord>> list(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 根据充值单号查询
     * @return
     */
    RechargeRecord getByRechargeId(String rechargeId);

    /**
     * 根据主键查询
     * @return
     */
    RechargeRecord getById(String id);

    void updateById(RechargeRecord rechargeRecord);

    RechargeRecord getLastestRecord(String employerNo, String mainstayNo);

    Map<String, Object> countRechargeAmount(Map<String, Object> paramMap);

    List<RechargeRecord> listBy(Map<String, Object> paramMap);

    Map<String, Object> sumRechargeRecord(Map<String, Object> paramMap);
}
