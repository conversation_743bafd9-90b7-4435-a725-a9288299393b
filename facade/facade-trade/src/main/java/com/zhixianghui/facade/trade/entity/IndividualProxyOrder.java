package com.zhixianghui.facade.trade.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.zhixianghui.facade.trade.handler.ProxyOrderInvoiceHandler;
import com.zhixianghui.facade.trade.vo.ProxyPayDetailVo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@TableName(value = "tbl_individual_proxy_order")
public class IndividualProxyOrder implements Serializable {
    private static final long serialVersionUID = -6961314556462862109L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务商编号
     */
    @TableField(value = "mainstay_no")
    private String mainstayNo;

    /**
     * 服务商名称
     */
    @TableField(value = "mainstay_name")
    private String mainstayName;

    /**
     * 版本号
     */
    @TableField(value = "version")
    @Version
    private Integer version;

    /**
     * 订单号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 支付流水号
     */
    @TableField(value = "pay_trx_no")
    private String payTrxNo;

    /**
     * 支付开始时间
     */
    @TableField(value = "pay_begin_time")
    private LocalDateTime payBeginTime;

    /**
     * 支付完成时间
     */
    @TableField(value = "pay_complete_time")
    private LocalDateTime payCompleteTime;

    /**
     * 订单完成时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;

    /**
     * 是否退款
     */
    @TableField(value = "is_refund")
    private Boolean isRefund = Boolean.FALSE;

    /**
     * 退款开始时间
     */
    @TableField(value = "refund_begin_time")
    private LocalDateTime refundBeginTime;

    /**
     * 退款完成时间
     */
    @TableField(value = "refund_complete_time")
    private LocalDateTime refundCompleteTime;

    /**
     * 退款原因
     */
    @TableField(value = "refund_reason")
    private Integer refundReason;

    /**
     * 退款单号
     */
    @TableField(value = "refund_no")
    private String refundNo;

    /**
     * 订单状态
     */
    @TableField(value = "order_status")
    private Integer orderStatus;

    /**
     * 付款状态
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 发票状态
     */
    @TableField(value = "invoice_status")
    private Integer invoiceStatus;

    /**
     * 发票信息
     */
    @TableField(value = "invoice_detail",typeHandler = ProxyOrderInvoiceHandler.class)
    private List<ProxyOrderItem> invoiceDetail;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 资金流水
     */
    @TableField(value = "fund_flow_file",typeHandler = FastjsonTypeHandler.class)
    private List<String> fundFlowFile;

    /**
     * 业务合同
     */
    @TableField(value = "business_contract_file",typeHandler = FastjsonTypeHandler.class)
    private List<String> businessContractFile;

    /**
     * 委托协议
     */
    @TableField(value = "entrust_agreement_file",typeHandler = FastjsonTypeHandler.class)
    private List<String> entrustAgreementFile;

    /**
     * 订单金额
     */
    @TableField(value = "order_amount")
    private BigDecimal orderAmount;

    /**
     * 支付金额
     */
    @TableField(value = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 身份证号码
     */
    @TableField(value = "id_card_no")
    private String idCardNo;

    /**
     * 发票类型
     */
    @TableField(value = "invoice_type")
    private Integer invoiceType;

    /**
     * 抬头ID
     */
    @TableField(value = "invoice_title_id")
    private Long invoiceTitleId;

    /**
     * 抬头税号
     */
    @TableField(value = "invoice_title_tax_no")
    private String invoiceTitleTaxNo;

    /**
     * 抬头公司名称
     */
    @TableField(value = "invoice_title_company_name")
    private String invoiceTitleCompanyName;

    /**
     * 开票人
     */
    @TableField(value = "invoice_applicant")
    private String invoiceApplicant;

    /**
     * 收票地址id
     */
    @TableField(value = "address_id")
    private Long addressId;

    /**
     * 收票人详细地址
     */
    @TableField(value = "address_detail")
    private String addressDetail;

    /**
     * 收票人姓名
     */
    @TableField(value = "address_name")
    private String addressName;

    /**
     * 收票人电话
     */
    @TableField(value = "address_mobile")
    private String addressMobile;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(value = "fee_detail", typeHandler = FastjsonTypeHandler.class)
    private ProxyPayDetailVo feeDetail;

    @TableField(value = "invoice_url", typeHandler = FastjsonTypeHandler.class)
    private List<String> invoiceUrl;

    @TableField(value = "express_no")
    private String expressNo;

    @TableField(value = "expire_time")
    private LocalDateTime expireTime;

    @TableField(value = "error_remark")
    private String errorRemark;

    @TableField(value = "user_mobile")
    private String userMobile;

    public static final String COL_ID = "id";

    public static final String COL_MAINSTAY_NO = "mainstay_no";

    public static final String COL_MAINSTAY_NAME = "mainstay_name";

    public static final String COL_VERSION = "version";

    public static final String COL_ORDER_NO = "order_no";

    public static final String COL_PAY_TRX_NO = "pay_trx_no";

    public static final String COL_PAY_BEGIN_TIME = "pay_begin_time";

    public static final String COL_PAY_COMPLETE_TIME = "pay_complete_time";

    public static final String COL_COMPLETE_TIME = "complete_time";

    public static final String COL_IS_REFUND = "is_refund";

    public static final String COL_REFUND_BEGIN_TIME = "refund_begin_time";

    public static final String COL_REFUND_COMPLETE_TIME = "refund_complete_time";

    public static final String COL_REFUND_NO = "refund_no";

    public static final String COL_REFUND_REASON = "refund_reason";

    public static final String COL_ORDER_STATUS = "order_status";

    public static final String COL_PAY_STATUS = "pay_status";

    public static final String COL_INVOICE_STATUS = "invoice_status";

    public static final String COL_INVOICE_DETAIL = "invoice_detail";

    public static final String COL_REMARK = "remark";

    public static final String COL_FUND_FLOW_FILE = "fund_flow_file";

    public static final String COL_BUSINESS_CONTRACT_FILE = "business_contract_file";

    public static final String COL_ENTRUST_AGREEMENT_FILE = "entrust_agreement_file";

    public static final String COL_ORDER_AMOUNT = "order_amount";

    public static final String COL_PAY_AMOUNT = "pay_amount";

    public static final String COL_ID_CARD_NO = "id_card_no";

    public static final String COL_INVOICE_TYPE = "invoice_type";

    public static final String COL_INVOICE_TITLE_ID = "invoice_title_id";

    public static final String COL_INVOICE_TITLE_TAX_NO = "invoice_title_tax_no";

    public static final String COL_INVOICE_TITLE_COMPANY_NAME = "invoice_title_company_name";

    public static final String COL_INVOICE_APPLICANT = "invoice_applicant";

    public static final String COL_ADDRESS_ID = "address_id";

    public static final String COL_ADDRESS_DETAIL = "address_detail";

    public static final String COL_ADDRESS_NAME = "address_name";

    public static final String COL_ADDRESS_MOBILE = "address_mobile";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";
    public static final String COL_FEE_DETAIL = "fee_detail";
    public static final String COL_EXPIRE_TIME = "expire_time";
    public static final String COL_USER_MOBILE = "user_mobile";

    public static void extendWhere(QueryWrapper<IndividualProxyOrder> queryWrapper, Map<String,Object> param) {
        queryWrapper.between(param.get("createTimeBegin") != null && param.get("createTimeEnd") != null, COL_CREATE_TIME, param.get("createTimeBegin"), param.get("createTimeEnd"));
        queryWrapper.like(StringUtils.isNotBlank((String) param.get("mainstayNameLike")), COL_MAINSTAY_NAME, param.get("mainstayNameLike"));
        queryWrapper.like(StringUtils.isNotBlank((String) param.get("invoiceTitleCompanyNameLike")), COL_INVOICE_TITLE_COMPANY_NAME, param.get("invoiceTitleCompanyNameLike"));

        if (StringUtils.isNotBlank((String) param.get("orderField"))) {
            queryWrapper.orderByDesc(StrUtil.toUnderlineCase((String) param.get("orderField")).toUpperCase());
        }else {
            queryWrapper.orderByDesc(COL_ID);
        }
    }
}
