package com.zhixianghui.facade.trade.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
@EnumDesc(name = "发票类别", type = SystemTypeEnum.CKH_MANAGEMENT)
public enum InvoiceCategoryEnum {
    SERVICE_FEE_INVOICE(1, "服务费发票"),
    NATURE_PERSON_INVOICE(2, "自然人代开")
    ;

    private final Integer code;
    private final String desc;

    public static InvoiceCategoryEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.code.intValue() == value).findFirst().orElse(null);
    }
}
