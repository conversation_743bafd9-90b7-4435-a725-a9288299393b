package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName WxJsApiRefundStatusEnum
 * @Description TODO
 * @Date 2022/12/30 15:02
 */
@Getter
@AllArgsConstructor
public enum  WxJsApiRefundStatusEnum {

    SUCCESS("SUCCESS","退款成功"),

    CLOSED("CLOSED","退款关闭"),

    ABNORMAL("ABNORMAL","退款异常");

    private String value;

    private String desc;
}
