package com.zhixianghui.facade.trade.utils;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;

public class CmbUtil {

    private static final String LOG_KEY_PREFIX = "LOG_KEY";//幂等key前缀

    public static final int LOCK_WAIT_TIME = 60000;//锁等待时间

    public static final long LOCK_LEASE_TIME = 60000;//锁过期时间

    public static String getRedisLockKey(String employerNo, String mainstayNo, Integer merchantType) {
        if (merchantType == MerchantTypeEnum.EMPLOYER.getValue()) {
            return TradeConstant.WITHHOLDING_AMOUNT_LOCK_KEY + "::" + employerNo + "::" + mainstayNo;
        }
        return TradeConstant.WITHHOLDING_AMOUNT_LOCK_KEY + "::" + mainstayNo;
    }

    public static String getLogKey(String platTrxNo, Integer amountChangeType, Integer merchantType) {
        return LOG_KEY_PREFIX + "::" + platTrxNo + "::" + amountChangeType + "::" + merchantType;
    }
}
