package com.zhixianghui.facade.trade.utils;

import com.zhixianghui.common.util.utils.DateUtil;

/**
 * 凭证工具类
 *
 * <AUTHOR>
 * @date 2021/1/29
 **/
public class CertificateUtil {

    private final static String TRANSFER_PREFIX = "transfer";
    private final static String PAY_PREFIX = "payment";
    private final static String FILE_SUFFIX = ".pdf";
    private final static String FILE_PATH = "/upload/trade_file/pay/";

    //账单目录前缀
    private static final String FEE_NAME_PREFIX = "fee/upload/trade_file";
    /**
     * 获取文件路径
     *
     * @param sftpDir   sftp 目录
     * @param mchNo     商户编号（用工企业或代征主体）
     * @param platTrxNo （流水号）
     * @return 文件路径
     */
    public static String getFilePath(String sftpDir, String mchNo, String platTrxNo) {
        String dayPath = DateUtil.formatSlashDate(TrxNoDateUtil.getDateFromTrxNo(platTrxNo));
        return sftpDir + mchNo + FILE_PATH + dayPath + "/";
    }

    /**
     * 转账文件名
     *
     * @param mainstayName 代征主体名称
     * @param employerNo   用工企业编号
     * @param platTrxNo    （流水号）
     * @return 文件路径
     */
    public static String getTransferFileName(String mainstayName, String employerNo, String platTrxNo) {
        return TRANSFER_PREFIX + "_" + mainstayName + "_" + employerNo + "_" + platTrxNo + FILE_SUFFIX;
    }

    /**
     * 付款凭证文件名
     *
     * @param receiveName 收款人名称
     * @param mainstayNo   代征主体编号
     * @param platTrxNo    （流水号）
     * @return 文件路径
     */
    public static String getPayFileName(String receiveName, String mainstayNo, String platTrxNo) {
        return PAY_PREFIX  + "_" + receiveName + "_" + mainstayNo + "_" + platTrxNo + FILE_SUFFIX;
    }

    /**
     * 获取账单文件路径
     * @param sftpDir
     * @param mchNo
     * @param batchNo
     * @return
     */
    public static String getFeeFilePath(String sftpDir, String mchNo, String batchNo) {
        String dayPath = DateUtil.formatSlashDate(TrxNoDateUtil.getDateFromTrxNo(batchNo));
        return sftpDir + FEE_NAME_PREFIX + "/" + mchNo +  "/"  + dayPath + "/";
    }


    /**
     * 创客汇付款凭证文件名
     * @param receiveName 收款人姓名
     * @param employerNo  商户编号
     * @param platTrxNo  流水号
     * @return
     */
    public static String getCKHPayFileName(String receiveName,String employerNo,String platTrxNo){
        return PAY_PREFIX + "_" + receiveName + "_" + employerNo + "_" + platTrxNo + FILE_SUFFIX;
    }

}
