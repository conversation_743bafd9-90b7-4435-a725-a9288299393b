package com.zhixianghui.facade.trade.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName ServiceConfirmVo
 * @Description TODO
 * @Date 2022/3/22 15:22
 */
@Data
public class ServiceConfirmVo implements Serializable {

    private String employerNo;

    private String employerName;

    private String date;

    private String workCategoryCode;

    private String workCategoryName;

    private String serviceDesc;

    private String chargeRuleDesc;

    private String mainstayNo;

    private String mainstayName;

    private Integer countNum;

    private BigDecimal totalAmount;

    private Date completeBeginDate;

    private Date completeEndDate;

    private Long salerId;

    private String salerName;

    private String agentNo;

    private String agentName;

}
