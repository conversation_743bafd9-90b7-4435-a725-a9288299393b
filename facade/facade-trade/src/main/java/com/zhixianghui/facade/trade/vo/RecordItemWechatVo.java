package com.zhixianghui.facade.trade.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName RecordItemWechatVo
 * @Description TODO
 * @Date 2023/6/21 15:11
 */
@Data
public class RecordItemWechatVo implements Serializable {

    private Integer processStatus;

    private Integer channelType;

    private String remitPlatTrxNo;

    private String remark;

    private Date createTime;

    private Date completeTime;

    private BigDecimal orderNetAmount;

    private String receiveAccountNo;
}
