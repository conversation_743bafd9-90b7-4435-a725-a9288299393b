package com.zhixianghui.facade.trade.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date : 2024/04/01 11:11:23
 **/
@Data
@Accessors(chain = true)
public class AcChangeFunds implements Serializable {
    private Long id;

    private Integer version;

    // 唯一键
    private String logKey;

    private Integer merchantType;

    // 所属商户编号
    private String mchNo;

    // 所属商户名称
    private String mchName;

    // 代征主体编号
    private String mainstayNo;

    // 代征主体名称
    private String mainstayName;

    // 订单号
    private String platTrxNo;

    // 资金变动类型
    private Integer amountChangeType;

    // 变动金额
    private Long amount;

    // 冻结金额
    private Long frozenAmount;


    private Long settleAmount;

    // 变动前金额
    private Long beforeAmount;

    // 变动前冻结金额
    private Long beforeFrozenAmount;

    // 变动后金额
    private Long afterAmount;

    // 变动后冻结金额
    private Long afterFrozenAmount;

    private Long beforeSettleAmount;

    private Long afterSettleAmount;

    // 创建时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 操作人
    private String operator;

    // 通道编号
    private String payChannelNo;

    // 通道名称
    private String payChannelName;

    private static final long serialVersionUID = 1L;
}