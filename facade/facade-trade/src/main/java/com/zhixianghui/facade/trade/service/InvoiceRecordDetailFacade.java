package com.zhixianghui.facade.trade.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.trade.dto.InvoiceRecordDetailDto;
import com.zhixianghui.facade.trade.dto.UpdateInvoiceDetailDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecordDetail;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;

import java.util.List;

public interface InvoiceRecordDetailFacade {
    IPage<InvoiceRecordDetail> invoiceRecordDetailPage(IPage<InvoiceRecordDetail> page, InvoiceRecordDetailDto dto);

    List<InvoiceRecordDetail> listInvoiceDetailIdCards(InvoiceRecordDetailDto dto);

    void updateInvoiceRecordDetail(UpdateInvoiceDetailDto dto);

    InvoiceRecordDetail getInvoiceRecordDetailByPlatTrxNo(String invoiceTrxNo,String platTrxNo);

    List<InvoiceRecordDetail> listInvoiceRecordDetailByInvoiceTrxNo(String invoiceTrxNo);

    List<InvoiceRecordDetail> listInvoiceRecordDetailByInvoiceTrxNoAndIdCardNo(String invoiceTrxNo, String idCard);

    IPage<InvoiceDetailGroupByIdCardVo> listInvoiceDetailGroupByIdCard(Page<InvoiceDetailGroupByIdCardVo> page, String trxNo);
}
