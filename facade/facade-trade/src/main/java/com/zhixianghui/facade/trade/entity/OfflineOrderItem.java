package com.zhixianghui.facade.trade.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
    * 订单明细表
    */
@Data
@TableName(value = "tbl_offline_order_item",autoResultMap = true)
public class OfflineOrderItem extends  BasePrivateItem implements Serializable {
    /**
     * ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    private Integer version;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 完成时间
     */
    @TableField(value = "COMPLETE_TIME")
    private Date completeTime;

    /**
     * 商户批次号
     */
    @TableField(value = "MCH_BATCH_NO")
    private String mchBatchNo;

    /**
     * 平台批次号
     */
    @TableField(value = "PLAT_BATCH_NO")
    private String platBatchNo;

    /**
     * 商户订单号
     */
    @TableField(value = "MCH_ORDER_NO")
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    @TableField(value = "PLAT_TRX_NO")
    private String platTrxNo;

    /**
     * 发放方式
     */
    @TableField(value = "LAUNCH_WAY")
    private Integer launchWay;

    /**
     * 产品编号
     */
    @TableField(value = "PRODUCT_NO")
    private String productNo;

    /**
     * 产品名称
     */
    @TableField(value = "PRODUCT_NAME")
    private String productName;

    /**
     * 用工企业编号
     */
    @TableField(value = "EMPLOYER_NO")
    private String employerNo;

    /**
     * 用工企业名称
     */
    @TableField(value = "EMPLOYER_NAME")
    private String employerName;

    /**
     * 代征主体编号
     */
    @TableField(value = "MAINSTAY_NO")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @TableField(value = "MAINSTAY_NAME")
    private String mainstayName;

    /**
     * 个税承担方
     */
    @TableField(value = "TAX_PAYER")
    private Integer taxPayer;

    /**
     * 银行名称
     */
    @TableField(value = "BANK_NAME")
    private String bankName;

    /**
     * 银行简码
     */
    @TableField(value = "BANK_CODE")
    private String bankCode;

    /**
     * 订单明细实发金额
     */
    @TableField(value = "ORDER_ITEM_NET_AMOUNT")
    private BigDecimal orderItemNetAmount;

    /**
     * 订单明细任务金额
     */
    @TableField(value = "ORDER_ITEM_TASK_AMOUNT")
    private BigDecimal orderItemTaskAmount;

    /**
     * 订单明细代征主体服务费
     */
    @TableField(value = "ORDER_ITEM_FEE")
    private BigDecimal orderItemFee;

    /**
     * 订单明细(总)金额
     */
    @TableField(value = "ORDER_ITEM_AMOUNT")
    private BigDecimal orderItemAmount;

    /**
     * 个税金额
     */
    @TableField(value = "ORDER_ITEM_TAX_AMOUNT")
    private BigDecimal orderItemTaxAmount = BigDecimal.ZERO;

    /**
     * 订单明细状态
     */
    @TableField(value = "ORDER_ITEM_STATUS")
    private Integer orderItemStatus;

    /**
     * 错误码
     */
    @TableField(value = "ERROR_CODE")
    private String errorCode;

    /**
     * 错误描述
     */
    @TableField(value = "ERROR_DESC")
    private String errorDesc;

    /**
     * 受理重试次数
     */
    @TableField(value = "ACCESS_TIMES")
    private Integer accessTimes;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * JSON数据
     */
    @TableField(value = "JSON_STR")
    private String jsonStr;

    /**
     * 微信APPID
     */
    @TableField(value = "APPID")
    private String appid;

    /**
     * 是否删除，0=未删除，1=删除
     */
    @TableField(value = "IS_DELETE")
    private Integer isDelete = 0;

    /**
     * 商户备忘
     */
    @TableField(value = "MEMO")
    private String memo;

    /**
     * 任务id
     */
    @TableField(value = "JOB_ID")
    private String jobId;

    /**
     * 任务名称
     */
    @TableField(value = "JOB_NAME")
    private String jobName;

    /**
     * 自由职业者服务编号
     */
    @TableField(value = "WORK_CATEGORY_CODE")
    private String workCategoryCode;

    /**
     * 自由职业者服务名称
     */
    @TableField(value = "WORK_CATEGORY_NAME")
    private String workCategoryName;

    @TableField(exist = false)
    private Integer authStatus;

    @TableField(exist = false)
    private Integer signStatus;
    /**
     * 回单路径
     */
    @TableField(value = "WORKER_BILL_FILE_PATH", typeHandler = FastjsonTypeHandler.class)
    private List<String> workerBillFilePath;

    /**
     * 挂单是否已经通过
     */
    @TableField(value = "IS_PASS_HANGUP")
    private Boolean isPassHangup;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_COMPLETE_TIME = "COMPLETE_TIME";

    public static final String COL_MCH_BATCH_NO = "MCH_BATCH_NO";

    public static final String COL_PLAT_BATCH_NO = "PLAT_BATCH_NO";

    public static final String COL_MCH_ORDER_NO = "MCH_ORDER_NO";

    public static final String COL_PLAT_TRX_NO = "PLAT_TRX_NO";

    public static final String COL_LAUNCH_WAY = "LAUNCH_WAY";

    public static final String COL_PRODUCT_NO = "PRODUCT_NO";

    public static final String COL_PRODUCT_NAME = "PRODUCT_NAME";

    public static final String COL_EMPLOYER_NO = "EMPLOYER_NO";

    public static final String COL_EMPLOYER_NAME = "EMPLOYER_NAME";

    public static final String COL_MAINSTAY_NO = "MAINSTAY_NO";

    public static final String COL_MAINSTAY_NAME = "MAINSTAY_NAME";

    public static final String COL_TAX_PAYER = "TAX_PAYER";

    public static final String COL_RECEIVE_NAME = "RECEIVE_NAME";

    public static final String COL_RECEIVE_NAME_MD5 = "RECEIVE_NAME_MD5";

    public static final String COL_RECEIVE_ID_CARD_NO = "RECEIVE_ID_CARD_NO";

    public static final String COL_RECEIVE_ID_CARD_NO_MD5 = "RECEIVE_ID_CARD_NO_MD5";

    public static final String COL_RECEIVE_ACCOUNT_NO = "RECEIVE_ACCOUNT_NO";

    public static final String COL_RECEIVE_ACCOUNT_NO_MD5 = "RECEIVE_ACCOUNT_NO_MD5";

    public static final String COL_RECEIVE_PHONE_NO = "RECEIVE_PHONE_NO";

    public static final String COL_RECEIVE_PHONE_NO_MD5 = "RECEIVE_PHONE_NO_MD5";

    public static final String COL_BANK_NAME = "BANK_NAME";

    public static final String COL_BANK_CODE = "BANK_CODE";

    public static final String COL_ORDER_ITEM_NET_AMOUNT = "ORDER_ITEM_NET_AMOUNT";

    public static final String COL_ORDER_ITEM_TASK_AMOUNT = "ORDER_ITEM_TASK_AMOUNT";

    public static final String COL_ORDER_ITEM_FEE = "ORDER_ITEM_FEE";

    public static final String COL_ORDER_ITEM_AMOUNT = "ORDER_ITEM_AMOUNT";

    public static final String COL_ORDER_ITEM_TAX_AMOUNT = "ORDER_ITEM_TAX_AMOUNT";

    public static final String COL_ORDER_ITEM_STATUS = "ORDER_ITEM_STATUS";

    public static final String COL_ERROR_CODE = "ERROR_CODE";

    public static final String COL_ERROR_DESC = "ERROR_DESC";

    public static final String COL_ENCRYPT_KEY_ID = "ENCRYPT_KEY_ID";

    public static final String COL_ACCESS_TIMES = "ACCESS_TIMES";

    public static final String COL_REMARK = "REMARK";

    public static final String COL_JSON_STR = "JSON_STR";

    public static final String COL_APPID = "APPID";

    public static final String COL_IS_DELETE = "IS_DELETE";

    public static final String COL_MEMO = "MEMO";

    public static final String COL_JOB_ID = "JOB_ID";

    public static final String COL_JOB_NAME = "JOB_NAME";

    public static final String COL_WORK_CATEGORY_CODE = "WORK_CATEGORY_CODE";

    public static final String COL_WORK_CATEGORY_NAME = "WORK_CATEGORY_NAME";

    public static final String COL_WORKER_BILL_FILE_PATH = "WORKER_BILL_FILE_PATH";


    public static void extendWhere(QueryWrapper<OfflineOrderItem> queryWrapper, Map<String,Object> param) {
        queryWrapper.between(param.get("createTimeBegin") != null && param.get("createTimeEnd") != null, COL_CREATE_TIME, param.get("createTimeBegin"), param.get("createTimeEnd"));
        queryWrapper.between(param.get("completeTimeBegin") != null && param.get("completeTimeEnd") != null, COL_COMPLETE_TIME, param.get("completeTimeBegin"), param.get("completeTimeEnd"));
        queryWrapper.between(param.get("createBeginDate") != null && param.get("createEndDate") != null, COL_CREATE_TIME, param.get("createBeginDate"), param.get("createEndDate"));
        queryWrapper.between(param.get("completeBeginDate") != null && param.get("completeEndDate") != null, COL_COMPLETE_TIME, param.get("completeBeginDate"), param.get("completeEndDate"));
        queryWrapper.between(StringUtils.isNotBlank((String)param.get("orderItemNetAmountMin")) &&  StringUtils.isNotBlank((String)param.get("orderItemNetAmountMax")), COL_ORDER_ITEM_NET_AMOUNT, param.get("orderItemNetAmountMin"), param.get("orderItemNetAmountMax"));
        queryWrapper.like(StringUtils.isNotBlank((String) param.get("employerNameLike")), COL_EMPLOYER_NAME, param.get("employerNameLike"));
        queryWrapper.like(StringUtils.isNotBlank((String) param.get("jobNameLike")), COL_JOB_NAME, param.get("jobNameLike"));
        queryWrapper.in(param.get("employerList")!=null && !((List) param.get("employerList")).isEmpty(), COL_EMPLOYER_NO, (List<String>)param.get("employerList"));
        queryWrapper.in(param.get("orderItemStatusList") != null && !((List) param.get("orderItemStatusList")).isEmpty(), COL_ORDER_ITEM_STATUS, (List<Integer>) param.get("orderItemStatusList"));

        if (StringUtils.isNotBlank((String) param.get("orderField"))) {
            queryWrapper.orderByDesc(StrUtil.toUnderlineCase((String) param.get("orderField")).toUpperCase());
        }else {
            queryWrapper.orderByDesc(COL_ID);
        }
    }
}
