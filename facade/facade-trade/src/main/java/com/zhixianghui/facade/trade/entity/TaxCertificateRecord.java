package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
    * 完税证明记录
    */
@Data
@TableName(value = "tbl_tax_certificate_record",autoResultMap = true)
public class TaxCertificateRecord implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户编号
     */
    @TableField(value = "employer_no")
    private String employerNo;

    /**
     * 商户名称
     */
    @TableField(value = "employer_name")
    private String employerName;

    /**
     * 代征主体编号
     */
    @TableField(value = "mainstay_no")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @TableField(value = "mainstay_name")
    private String mainstayName;

    /**
     * 开始时间
     */
    @TableField(value = "date_begin")
    private LocalDate dateBegin;

    /**
     * 结束时间
     */
    @TableField(value = "date_end")
    private LocalDate dateEnd;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 正面路径
     */
    @TableField(value = "certificate_file_path",typeHandler = FastjsonTypeHandler.class)
    private List<String> certificateFilePath;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_EMPLOYER_NO = "employer_no";

    public static final String COL_EMPLOYER_NAME = "employer_name";

    public static final String COL_MAINSTAY_NO = "mainstay_no";

    public static final String COL_MAINSTAY_NAME = "mainstay_name";

    public static final String COL_DATE_BEGIN = "date_begin";

    public static final String COL_DATE_END = "date_end";

    public static final String COL_REMARK = "remark";

    public static final String COL_CERTIFICATE_FILE_PATH = "certificate_file_path";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_CREATE_BY = "create_by";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_UPDATE_BY = "update_by";
}
