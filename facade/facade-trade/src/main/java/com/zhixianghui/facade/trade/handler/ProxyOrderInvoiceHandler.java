package com.zhixianghui.facade.trade.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.zhixianghui.facade.trade.entity.ProxyOrderItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

@Slf4j
@MappedTypes({List.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class ProxyOrderInvoiceHandler extends AbstractJsonTypeHandler<List<ProxyOrderItem>> {
    private final Class<ProxyOrderItem> type;

    public ProxyOrderInvoiceHandler(Class<ProxyOrderItem> type) {
        if (log.isTraceEnabled()) {
            log.trace("FastjsonTypeHandler(" + type + ")");
        }

        Assert.notNull(type, "Type argument cannot be null", new Object[0]);
        this.type = type;
    }

    @Override
    protected List<ProxyOrderItem> parse(String json) {
        final JSONArray array = JSON.parseArray(json);
        final List<ProxyOrderItem> proxyQuotes = array.toJavaList(ProxyOrderItem.class);
        return proxyQuotes;
    }

    @Override
    protected String toJson(List<ProxyOrderItem> obj) {
        return JSON.toJSONString(obj, new SerializerFeature[]{SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullListAsEmpty, SerializerFeature.WriteNullStringAsEmpty});
    }
}
