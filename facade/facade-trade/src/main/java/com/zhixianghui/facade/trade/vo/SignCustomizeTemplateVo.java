package com.zhixianghui.facade.trade.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/6 17:35
 */
@Data
public class SignCustomizeTemplateVo extends PageQueryVo {

    protected String templateId;
    protected String protocolName;
    @JsonIgnore
    protected byte[] bytes;
    @JsonIgnore
    private String localUrl;
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    protected List<StructComponent> structComponent;
    protected String sourceFileUrl;
    protected String targetFileUrl;
    private List<String> mchNo;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String templateName;
    protected List<MerchantInfo> merchantList;
    protected List<MerchantInfo> employerList;
    protected Long id;


    /**
     * @see com.zhixianghui.service.trade.enums.SignTemplateTypeEnum
     */
    protected Integer signTemplateType;

    protected List<String> mchNoList;

    public SignCustomizeTemplateVo() {

    }
    public SignCustomizeTemplateVo(String sourceFileUrl, String targetFileUrl, String localUrl) {
        this.sourceFileUrl = sourceFileUrl;
        this.targetFileUrl = targetFileUrl;
        this.localUrl = localUrl;
    }
}
