package com.zhixianghui.facade.trade.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date : 2024/04/01 11:12:04
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AcMerchantBalance implements Serializable {

    private Long id;

    private Integer version;

    // 商户编号
    private String mchNo;

    // 商户名称
    private String mchName;

    // 供应商编号
    private String mainstayNo;

    // 供应商名称
    private String mainstayName;

    // 通道编号
    private String payChannelNo;

    // 通道名称
    private String payChannelName;

    // 总金额
    private Long totalAmount;

    // 冻结金额
    private Long freezeAmount;

    private Long settleAmount;

    // 商户类型
    private Integer merchantType;

    // 创建时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 更新时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}