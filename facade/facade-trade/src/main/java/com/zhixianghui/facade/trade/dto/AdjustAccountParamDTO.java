package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/22 14:25 
 */
@Data
public class AdjustAccountParamDTO implements Serializable {
    private static final long serialVersionUID = 2854136141217351001L;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 总金额
     */
    private BigDecimal amount;

    /**
     * 操作 AdjustAccountTypeEnum
     */
    private Integer type;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 商户类型
     */
    private Integer merchantType;
}
