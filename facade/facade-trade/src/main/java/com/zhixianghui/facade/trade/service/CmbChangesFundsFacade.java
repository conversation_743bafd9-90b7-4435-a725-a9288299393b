package com.zhixianghui.facade.trade.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.facade.trade.dto.CmbChangesFoundsQueryDTO;
import com.zhixianghui.facade.trade.vo.CmbChangesFundsVO;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:13
 */
public interface CmbChangesFundsFacade {

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    IPage<CmbChangesFundsVO> listPage(CmbChangesFoundsQueryDTO param);

    /**
     * 导出
     * @param param
     * @param loginName
     */
    void exportCmbChangesFunds(CmbChangesFoundsQueryDTO param, String loginName);
}
