package com.zhixianghui.facade.trade.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 统计OrderItem使用Business Object
 * @date 2020-11-08 11:08
 **/
@Data
public class OrderItemCountBo implements Serializable {

    private static final long serialVersionUID = -8784547332279698932L;

    /**
     * 已受理笔数
     */
    private Integer acceptedCount;

    /**
     * 已受理（总）金额
     */
    private BigDecimal acceptedItemAmount;

    /**
     * 已受理（总）任务金额
     */
    private BigDecimal acceptedTaskAmount;

    /**
     * 已受理(总)实发金额
     */
    private BigDecimal acceptedNetAmount;

    /**
     * 已受理代征主体服务费
     */
    private BigDecimal acceptedFee;

    /**
     * 已受理（总）个税金额
     */
    private BigDecimal acceptedTaxAmount;

    /**
     * 已受理(总)订单金额
     */
    private BigDecimal acceptedOrderAmount;

    /**
     * 成功笔数
     */
    private Integer successCount;

    /**
     * 成功（总）金额
     */
    private BigDecimal successTaskAmount;

    /**
     * 成功实发金额
     */
    private BigDecimal successNetAmount;

    /**
     * 成功代征主体服务费
     */
    private BigDecimal successFee;

    /**
     * 成功个税金额
     */
    private BigDecimal successTaxAmount;

    /**
     * 失败笔数
     */
    private Integer failCount;

    /**
     * 失败实发金额
     */
    private BigDecimal failNetAmount;

    /**
     * 失败任务金额
     */
    private BigDecimal failTaskAmount;
}
