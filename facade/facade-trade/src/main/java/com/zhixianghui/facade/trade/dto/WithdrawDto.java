package com.zhixianghui.facade.trade.dto;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class WithdrawDto implements Serializable {

    private static final long serialVersionUID = -3755146857297620744L;
    /**
     * 用工企业编号
     */
    private String employerNo;
    /**
     * 代征主体编号
     */
    private String mainstayNo;
    /**
     * 商户类型
     */
    private Integer merchantType;
    /**
     * 通道类型
     */
    private Integer channelType;
    /**
     * 通道编号
     */
    private String channelNo;
    /**
     * 金额
     */
    private String amount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 支付密码
     */
    private String payPasswd;
}
