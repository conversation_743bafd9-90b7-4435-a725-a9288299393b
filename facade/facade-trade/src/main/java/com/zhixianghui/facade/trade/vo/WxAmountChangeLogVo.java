package com.zhixianghui.facade.trade.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName WxAmountChangeLogVo
 * @Description TODO
 * @Date 2021/12/9 11:26
 */
@Data
@Accessors(chain = true)
public class WxAmountChangeLogVo implements Serializable {

    private String subMchId;

    private Long incomeId;

    private String handler;

    private Integer changeType;

    private Long totalAmount = 0L;

    private Long freezeAmount = 0L;

    private String logKey;

    private Date successTime;

    private String remark;

    private String payImgUrl;

    private Integer incomeType;
}
