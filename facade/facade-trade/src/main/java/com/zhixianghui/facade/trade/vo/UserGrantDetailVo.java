package com.zhixianghui.facade.trade.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName UserGrantDetailVo
 * @Description TODO
 * @Date 2023/6/20 15:32
 */
@Data
public class UserGrantDetailVo {

    private String employerName;

    private Integer channelType;

    private String remitPlatTrxNo;

    private Date completeTime;

    private BigDecimal orderNetAmount;

}
