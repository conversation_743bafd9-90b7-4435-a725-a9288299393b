package com.zhixianghui.facade.trade.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/18 15:32
 */

@Data
@Accessors(chain = true)
public class FlowSignDto implements Serializable {
    private Long commitFlowId;
    private String merchantNo;
    private String mainstayNo;
    private String fileUrl;

    private float xPosA = 317;
    private float yPosA = 305;
    private float xPosB = 317;
    private float yPosB = 153;
    private float width = 120;
    private float height = 120;

}
