package com.zhixianghui.facade.trade.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/22 15:33
 */
@Data
public class CmbIncomeRecordVO implements Serializable {

    private static final long serialVersionUID = -6335695146462715695L;

    /**
     * ID
     */
    private Long id;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 银行来账完成时间
     */
    private Date successTime;

    /**
     * 收款方账号
     */
    private String payeeAccountNo;

    /**
     * 收款方名称
     */
    private String payeeAccountName;

    /**
     * 通道交易流水号
     */
    private String channelTrxNo;

    /**
     * 付款银行名称
     */
    private String bankName;

    /**
     * 付款银行账户名
     */
    private String bankAccountName;

    /**
     * 付款银行卡号
     */
    private String bankAccountNumber;

    /**
     * 审核状态 CmbIncomeStateEnum
     */
    private Integer state;

    /**
     * 银行备注
     */
    private String rechargeRemark;
}
