package com.zhixianghui.facade.trade.vo.pay;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月15日 15:45:00
 */
@Data
public class WxPayParam implements Serializable {

    private static final long serialVersionUID = 6732491675846282526L;

    private String spMchid;
    private String outBatchNo;
    private String batchId;
    private String appid;
    private String outDetailNo;
    private String detailId;
    private String detailStatus;
    private Long transferAmount;
    private String transferRemark;
    private String failReason;
    private String openid;
    private String username;
    private String initiateTime;
    private String updateTime;

    private String employerNo;
    private String mchName;
    private String realPayerName;
    private String mainstayNo;
    private String platTrxNo;
    private String serviceFee;

}