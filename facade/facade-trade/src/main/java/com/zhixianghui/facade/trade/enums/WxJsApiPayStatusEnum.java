package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName WxPayNotifyTypeEnum
 * @Description TODO
 * @Date 2022/12/29 9:43
 */
@Getter
@AllArgsConstructor
public enum WxJsApiPayStatusEnum {

    SUCCESS("SUCCESS","成功"),

    REFUND("REFUND","转入退款"),

    NOTPAY("NOTPAY","未支付"),

    CLOSE("CLOSED","已关闭");

    private String status;

    private String desc;


}
