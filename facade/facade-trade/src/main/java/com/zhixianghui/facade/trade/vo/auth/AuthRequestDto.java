package com.zhixianghui.facade.trade.vo.auth;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Data
public class AuthRequestDto implements Serializable {
    private static final long serialVersionUID = 8542717074662188725L;


    /**
     * 姓名
     */
    @NotBlank
    private String name;
    /**
     * 银行账户
     */
    private String bankAccountNo;
    /**
     * 身份证号
     */
    @NotBlank
    private String idCardNo;

    /**
     * 电话号码
     */
    private String phoneNo;

    /**
     * 鉴权类型
     * @see com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum
     */
    private Integer authType;
}
