package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模板组件表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-06-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TemplateComponent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 用工企业编号
     */
    private String label;

    /**
     * 是否必填
     */
    private Integer required;

    /**
     * 样式
     */
    private String style;

    /**
     * 是否为预置类型
     */
    private Integer preset;

    /**
     * 输入项组件类型，1-单行文本，2-数字，3-日期，6-签约区，8-多行文本，11-图片
     */
    private Integer type;

    /**
     * 限制组件左右上下拖动
     */
    private String handles;

    /**
     * 是否可以缩放
     */
    private Integer resizable;


}
