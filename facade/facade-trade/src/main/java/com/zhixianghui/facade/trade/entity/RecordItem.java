package com.zhixianghui.facade.trade.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.ValidateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * 打款交易流水表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecordItem extends BasePrivateItem {

    private static final long serialVersionUID = -325409387900264363L;

    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 打款请求流水号
     */
    private String remitPlatTrxNo;

    /**
     * 渠道流水号
     */
    private String channelTrxNo;

    /**
     * 发放模式(接口/后台)
     */
    private Integer launchWay;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 通道类型（发放方式）
     */
    private Integer channelType;

    /**
     * 通道商户编号
     */
    private String channelMchNo;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名
     */
    private String channelName;


    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行简码
     */
    private String bankCode;

    /**
     * 任务金额
     */
    private BigDecimal orderTaskAmount;

    /**
     * 个税金额
     */
    private BigDecimal orderTaxAmount;

    /**
     * 打款明细实发金额
     */
    private BigDecimal orderNetAmount;

    /**
     * 打款明细代征主体服务费
     */
    private BigDecimal orderFee;

    /**
     * 打款明细(总)金额
     */
    private BigDecimal orderAmount;

    /**
     * 自由职业者服务编号
     */
    private String workCategoryCode;

    /**
     * 自由职业者服务名称
     */
    private String workCategoryName;

    /**
     * 处理状态
     */
    private Integer processStatus;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * JSON数据
     */
    private String jsonStr;

}
