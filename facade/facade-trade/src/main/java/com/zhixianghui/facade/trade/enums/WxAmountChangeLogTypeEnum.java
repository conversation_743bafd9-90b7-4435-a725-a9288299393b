package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName WxAmountChangeLogEnum
 * @Description TODO
 * @Date 2021/12/9 11:35
 */
@AllArgsConstructor
public enum  WxAmountChangeLogTypeEnum {

    DEPOSIT(100,"充值入账"),

    FROZEN(200,"冻结余额"),

    PAYMENT(300,"打款成功"),

    REFUND(400,"退款"),

    ADJUSTMENT(500,"调账"),

    WITHDRAW(600,"提现"),

    SETTLE_FROZEN(700,"结算冻结"),

    SETTLE_SUCCESS(800,"结算完成"),

    FAIL_REPAIR(900,"供应商余额异常修复"),

    REFUND_SETTLE(1000,"供应商余额异常修复"),

    UN_FROZEN(1100,"解冻余额"),
    ;

    private int value;

    private String desc;

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static WxAmountChangeLogTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
