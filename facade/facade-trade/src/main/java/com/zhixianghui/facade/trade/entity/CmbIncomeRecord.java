package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/16 15:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "tbl_cmb_income_record")
public class CmbIncomeRecord implements Serializable {
    private static final long serialVersionUID = -5522822569393327945L;
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 商户编号
     */
    @TableField(value = "mch_no")
    private String mchNo;

    /**
     * 商户名称
     */
    @TableField(value = "mch_name")
    private String mchName;

    /**
     * 供应商编号
     */
    @TableField(value = "mainstay_no")
    private String mainstayNo;

    /**
     * 供应商名称
     */
    @TableField(value = "mainstay_name")
    private String mainstayName;

    /**
     * 交易金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 银行来账完成时间
     */
    @TableField(value = "success_time")
    private Date successTime;

    /**
     * 收款方账号
     */
    @TableField(value = "payee_account_no")
    private String payeeAccountNo;

    /**
     * 收款方名称
     */
    @TableField(value = "payee_account_name")
    private String payeeAccountName;

    /**
     * 业务参考号
     */
    @TableField(value = "business_no")
    private String businessNo;

    /**
     * 通道交易流水号
     */
    @TableField(value = "channel_trx_no")
    private String channelTrxNo;

    /**
     * 付款银行名称
     */
    @TableField(value = "bank_name")
    private String bankName;

    /**
     * 付款银行账户名
     */
    @TableField(value = "bank_account_name")
    private String bankAccountName;

    /**
     * 付款银行卡号
     */
    @TableField(value = "bank_account_number")
    private String bankAccountNumber;

    /**
     * 审核状态 CmbIncomeStateEnum
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 银行备注
     */
    @TableField(value = "recharge_remark")
    private String rechargeRemark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;


    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(value = "updator")
    private String updator;
}
