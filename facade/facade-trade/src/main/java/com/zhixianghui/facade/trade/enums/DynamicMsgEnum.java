package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName DynamicMsgEnum
 * @Description TODO
 * @Date 2022/5/6 15:36
 */
@AllArgsConstructor
@Getter
public enum DynamicMsgEnum {

    CHARGE("充值","充值成功。本次充值金额为￥%s，订单号：%s"),

    WITHDRAW("提现","提现成功。本次提现金额为￥%s，订单号：%s"),

    BATCH_GRANT("批次发放","%s 发放完成，成功%s笔，失败%s笔"),

    INVOICE("发票","发票已寄出。流水号:%s");

    private String desc;

    private String msg;
}
