package com.zhixianghui.facade.trade.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 金额工具
 * @createTime 2021年12月08日 11:58:00
 */
public class AmountUtil {

    //100
    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);

    /**
     * 分转换成元
     * @param fen
     * @return
     */
    public static BigDecimal changeToYuan(Long fen) {
        return changeToYuan(BigDecimal.valueOf(fen));
    }

    public static BigDecimal changeToYuan(String fen) {
        return changeToYuan(Long.valueOf(fen));
    }

    public static BigDecimal changeToYuan(BigDecimal fen) {
        return fen.divide(HUNDRED);
    }

    public static String changeToYuanInString(String fen) {
        return String.valueOf(changeToYuan(fen));
    }


    /**
     * 元转换成分
     * @param fen
     * @return
     */

    public static Long changeToFen(String fen) {
        return changeToFen(new BigDecimal(fen));
    }

    public static Long changeToFen(BigDecimal fen) {
        return fen.multiply(HUNDRED).longValue();
    }

    public static String changeToFenInString(String fen) {
        return String.valueOf(changeToFen(new BigDecimal(fen)));
    }

}