package com.zhixianghui.facade.trade.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.dto.OfflineInvoiceApplyDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.facade.trade.vo.InvoiceEditVo;
import com.zhixianghui.facade.trade.vo.InvoiceUpdateVo;

import java.util.List;
import java.util.Map;

/**
 * 开票记录
 * <AUTHOR>
 * @date 2020-12-28
 */
public interface InvoiceFacade {
    /**
     * 根据流水号查询
     * @param trxNo 流水号
     * @return
     */
    InvoiceRecord getByTrxNo(String trxNo);

    /**
     * 分页查询
     * @param paramMap  查询参数
     * @param pageParam 页码参数
     * @return
     */
    PageResult<List<InvoiceRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 更新
     */
    void updateStatus(InvoiceUpdateVo vo) throws BizException;

    /**
     * 更新
     */
    void updateInvoiceRecord(InvoiceEditVo vo) throws BizException;

    /**
     * 获取申请开票交易时间段的起始时间
     * @param employerMchNo     用工商户编号
     * @param mainstayMchNo     代征商户编号
     * @return
     */
    String getApplyTradeCompleteDayBegin(String employerMchNo, String mainstayMchNo,Integer invoiceType,String jobId,
                                         Integer source,String workCategoryCode, String productNo, Integer amountType) throws BizException;

    /**
     * 开票申请
     * @param record 开票申请信息
     */
    void applyInvoice(InvoiceRecord record,String jobId) throws BizException;

    void applyInvoiceOrderItem(InvoiceRecord record,String jobId);

    Map<String, Object> countInvoiceAmount(Map<String, Object> paramMap);

    IPage<InvoiceDetailGroupByIdCardVo> listInvoiceDetailGroupByIdCard(Page<InvoiceDetailGroupByIdCardVo> page, String invoiceTrxNo) throws BizException;

    String applyOffline(OfflineInvoiceApplyDto applyDto) throws BizException;
}
