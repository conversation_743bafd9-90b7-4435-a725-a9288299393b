package com.zhixianghui.facade.trade.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单明细表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderItem extends BasePrivateItem {

    private static final long serialVersionUID = -6440070752419970902L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 发放模式
     */
    private Integer launchWay;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名
     */
    private String channelName;

    /**
     * 个税承担方
     */
    private Integer taxPayer;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行简码
     */
    private String bankCode;

    /**
     * 订单明细任务金额
     */
    private BigDecimal orderItemTaskAmount;

    /**
     * 订单明细实发金额
     */
    private BigDecimal orderItemNetAmount;

    /**
     * 订单明细代征主体服务费
     */
    private BigDecimal orderItemFee;

    /**
     * 订单明细(总)金额
     */
    private BigDecimal orderItemAmount;

    /**
     * 订单明细个税金额
     */
    private BigDecimal orderItemTaxAmount;

    /**
     * 订单明细状态
     */
    private Integer orderItemStatus;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorDesc;

    /**
     * 重试次数
     */
    private Integer accessTimes;

    /**
     * 挂单是否已经通过
     */
    private Boolean isPassHangup = Boolean.FALSE;

    /**
     * 挂单处理人
     */
    private String hangupApprovalLoginName;

    /**
     * 备注
     */
    private String remark;

    /**
     * JSON数据
     */
    private String jsonStr;

    /**
     * 微信appid
     */
    private String appid;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 商户备忘录
     */
    private String memo;

    private String jobId;

    private String jobName;

    /**
     * 自由职业者服务编号
     */
    private String workCategoryCode;

    /**
     * 自由职业者服务名称
     */
    private String workCategoryName;

    /**
     * 渠道流水号
     */
    private String channelTrxNo;
}
