package com.zhixianghui.facade.trade.vo;

import com.zhixianghui.common.util.validator.Email;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName WeChatReceiptVo
 * @Description TODO
 * @Date 2023/6/21 16:13
 */
@Data
public class WeChatReceiptVo implements Serializable {

    @NotEmpty(message = "邮箱不能为空")
    @Email(message = "邮箱格式有误")
    @Length(max = 50, message = "邮箱过长，不能超过50个字符")
    private String email;

    @NotEmpty(message = "订单号不能为空")
    private String remitPlatTrxNo;

    private String channelNo;
}
