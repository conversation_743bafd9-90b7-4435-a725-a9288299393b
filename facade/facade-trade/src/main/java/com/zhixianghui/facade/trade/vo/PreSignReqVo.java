package com.zhixianghui.facade.trade.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 预签约接口ReqVo
 * @date 2021-01-14 09:53
 **/
@Data
public class PreSignReqVo {
    /**
     * 姓名
     */
    @NotBlank(message = "name 姓名不能为空")
    private String name;

    /**
     * 身份证号
     */
    @NotBlank(message = "id_card_no 身份证号码不能为空")
    private String idCardNo;

    /**
     * 电话号码
     */
    @NotBlank(message = "phone_no 手机号码不能为空")
    private String phoneNo;

    /**
     * 代征主体编号
     */
    @NotBlank(message = "mainstay_no 代征主体编号不能为空")
    private String mainstayNo;

    /**
     * 身份证背面照链接
     */
    private String idCardBackUrl;

    /**
     * 身份证正面照链接
     */
    private String idCardFrontUrl;

    /**
     * 半身照
     */
    private String cerFaceUrl;

    /**
     * 银行卡编号
     */
    private String bankCardCode;
}
