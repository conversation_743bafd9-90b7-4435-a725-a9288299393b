package com.zhixianghui.facade.trade.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/8/2 9:59
 */
@Data
public class FreelanceStatVo implements Serializable {
    private static final long serialVersionUID = 1978467568921620520L;
    private Long id;
    private Long signId;
    private String receiveName;
    private String receiveIdCardNo;
    private String employerNo;
    private String employerName;
    private String mainstayName;
    private String mainstayNo;
    private BigDecimal orderItemNetAmount;
    private Long receiverOrder;
    private Long conditionOrder;
    private Integer signRecord;
    private String signDescribe;
    private Integer idCard;
    private String idCardDescribe;
    private Integer phone;
    private Date createTime;
    private String createDate;
    private String idCardBackUrl;
    private String  idCardFrontUrl;
    private String idCardCopyUrl;
    private String signStatus;
    private String  cerFaceUrl;
    private String receiveAccountNo;
    private String receivePhoneNo;

    // 总计
    private Integer freelanceCount;
    private Long orderCount;
    private BigDecimal orderItemNetAmountSum;

}
