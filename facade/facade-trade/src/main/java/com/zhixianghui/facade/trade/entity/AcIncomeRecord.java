package com.zhixianghui.facade.trade.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date : 2024/04/01 11:10:17
 **/
@Data
public class AcIncomeRecord implements Serializable {
    // 主键
    private Long id;

    // 创建时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 商户号
    private String mchNo;

    // 商户名称
    private String mchName;


    // 通道编号
    private String payChannelNo;

    // 通道编号
    private String payChannelName;

    // 渠道业务流水号
    private String trxNo;

    // 支付方式
    private String payNo;

    // 渠道交易状态
    private String channelState;

    // 平台入账状态(100-入账成功;101-入账失败) IncomeStateEnum
    private Integer state;

    // 来账金额
    private BigDecimal receiveAmount;

    // 交易手续费
    private BigDecimal incomeFee;

    // 实际入账金额
    private BigDecimal incomeAmount;

    // 入账时间
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    // 渠道入账流水号
    private String incomeTrxNo;

    // 付款人账号
    private String payerAccountNo;

    // 付款人名称
    private String payerAccountName;

    // 付款人银行
    private String payerAccountBank;

    // 收款人账号
    private String payeeAccountNo;

    // 收款人名称
    private String payeeAccountName;

    // 附言信息
    private String remark;

    private static final long serialVersionUID = 1L;
}