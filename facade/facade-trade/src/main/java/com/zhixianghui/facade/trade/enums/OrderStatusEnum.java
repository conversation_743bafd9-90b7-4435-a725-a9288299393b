package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 批次订单状态
 * @date 2020-11-04 14:37
 **/
@AllArgsConstructor
@Getter
@ToString
public enum OrderStatusEnum {
    /**
     *发放完成
     */
    FINISH_GRANT(100,"发放完成"),
    /**
     *关闭
     */
    CLOSED_GRANT(101,"关闭"),
    /**
     *导入中(受理中)
     */
    IMPORTING(102, "导入中"),
    /**
     *待发放
     */
    PENDING_GRANT(103, "待发放"),
    /**
     *发放中
     */
    GRANTING(104,"发放中"),

    /**
     * 导入失败
     */
    IMPORT_FAIL(105, "导入失败"),
    ;

    private final int value;
    private final String desc;
}
