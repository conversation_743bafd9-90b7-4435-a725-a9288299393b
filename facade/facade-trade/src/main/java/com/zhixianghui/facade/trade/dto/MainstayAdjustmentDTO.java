package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月21日 16:20:00
 */
@Data
public class MainstayAdjustmentDTO implements Serializable {
    private static final long serialVersionUID = 1447382563497444158L;

    /**
     * 供应商编号
     */
    @NotBlank(message = "供应商编号不能为空")
    private String mainstayNo;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    private String mainstayName;

    /**
     * 总金额
     */
    private BigDecimal amount;

    private String logKey;

    private String operator;

    private Integer type;
}