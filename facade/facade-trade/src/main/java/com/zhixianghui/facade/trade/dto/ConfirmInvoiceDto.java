package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class ConfirmInvoiceDto implements Serializable {
    private static final long serialVersionUID = 7654690295661830420L;

    @NotNull
    private Long id;
    private Integer status; // 只有action为1时候需要传
    private List<String> invoiceUrl; // 只有action为1时候需要传
    private String expressNo; // 只有action为1时候需要传
    private Integer action;//1 确认开票，2，取消订单 3. 退款

    private Integer refundReason;
    private String errorRemark;
}
