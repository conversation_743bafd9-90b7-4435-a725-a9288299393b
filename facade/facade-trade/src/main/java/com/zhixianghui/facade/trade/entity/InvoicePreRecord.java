package com.zhixianghui.facade.trade.entity;

import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

/**
 * 预开发票记录表
 */
@Data
public class InvoicePreRecord extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /**
     * 更新时间
     */
    private java.util.Date updateTime;

    /**
     * 完成时间
     */
    private java.util.Date completeTime;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 用工企业编号
     */
    private String employerMchNo;

    /**
     * 用工企业名称
     */
    private String employerMchName;

    /**
     * 代征主体编号
     */
    private String mainstayMchNo;

    /**
     * 代征主体名称
     */
    private String mainstayMchName;

    /**
     * 岗位类目
     */
    private String workCategoryCode;

    /**
     * 岗位类目名称
     */
    private String workCategoryName;

    /**
     * 开票金额
     */
    private java.math.BigDecimal invoiceAmount;

    /**
     * 发票类目编码
     */
    private String invoiceCategoryCode;

    /**
     * 发票类目名称
     */
    private String invoiceCategoryName;

    /**
     * 发票状态
     * @see com.zhixianghui.common.statics.enums.invoice.InvoicePreStatusEnum
     */
    private Integer invoiceStatus;

    /**
     * 发票影像文件url
     */
    private String invoiceFileUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * json格式，其他信息
     */
    private Object jsonInfo;

    /**
     * 错误描述
     */
    private String errorDesc;
} 