package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.dto.OfflineWorkerBillPathUpdateDto;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OfflineOrderItemFail;
import com.zhixianghui.facade.trade.vo.AuthInfoVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface OfflineOrderItemFacade {

    void batchInsert(List<OfflineOrderItem> itemList);

    OrderItemSumBo sumOrderItem(Map<String, Object> beanToMap);

    Long countOrderItemByMchOrderNo(String employerNo, String mchOrderNo);

    PageResult<List<OfflineOrderItem>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    List<OfflineOrderItemFail> getByPlatBatchNo(String platBatchNo);

    PageResult<List<OfflineOrderItemFail>> pageByPlatBatchNo(String platBatchNo, PageParam pageParam);

    AuthInfoVo getAuthInfo(OfflineOrderItem orderItem);

    Long countOrder(Map<String, Object> param);

    Long countOrderItem(Map<String, Object> paramMap);

    void saveFail(OfflineOrderItemFail itemFail);

    List<String> listPlatTrxNoByBatchNo(Map<String,Object> paramMap);

    void cancelOrderItem(String platBatchNo);

    void authInit(String platBatchNo);

    OfflineOrderItem uploadWorkerBillPath(OfflineWorkerBillPathUpdateDto dto);

    void deleteOrderItem(String platTrxNo) throws BizException;

    BigDecimal sumOrderItemWaitInvoiceAmount(Map<String, Object> paramMap);

    OfflineOrderItem getOrderItemByPlatTrxNo(String platTrxNo);
}
