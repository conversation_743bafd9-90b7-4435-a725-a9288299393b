package com.zhixianghui.facade.trade.dto;

import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.facade.trade.entity.ProxyOrderItem;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class IndividualProxyOrderDto implements Serializable {
    private static final long serialVersionUID = 3498115561004917083L;
    private Long id;

    /**
     * 服务商编号
     */
    @NotBlank(message = "服务商编号不能为空")
    private String mainstayNo;

    /**
     * 服务商名称
     */
    @NotBlank(message = "服务商名称不能为空")
    private String mainstayName;


    /**
     * 发票信息
     */
    @NotEmpty(message = "发票信息不能为空")
    private List<ProxyOrderItem> invoiceDetail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资金流水
     */
    @NotEmpty(message = "资金流水材料不能为空")
    private List<String> fundFlowFile;

    /**
     * 业务合同
     */
    @NotEmpty(message = "业务合同材料不能为空")
    private List<String> businessContractFile;

    /**
     * 委托协议
     */
    @NotEmpty(message = "委托协议材料为空")
    private List<String> entrustAgreementFile;

    /**
     * 发票类型
     */
    @NotNull(message = "发票类型不能为空")
    private Integer invoiceType = InvoiceTypeEnum.NORMAL.getValue();

    /**
     * 抬头ID
     */
    @NotNull(message = "抬头ID不能为空")
    private Long invoiceTitleId;

    /**
     * 抬头税号
     */
    @NotBlank(message = "抬头税号不能为空")
    private String invoiceTitleTaxNo;

    /**
     * 抬头公司名称
     */
    @NotBlank(message = "抬头公司名称不能为空")
    private String invoiceTitleCompanyName;

    /**
     * 开票人
     */
    @NotBlank(message = "开票人不能为空")
    private String invoiceApplicant;

    /**
     * 收票地址id
     */
    @NotNull(message = "收票地址id不能为空")
    private Long addressId;

    /**
     * 收票人详细地址
     */
    @NotBlank(message = "收票人详细地址不能为空")
    private String addressDetail;

    /**
     * 收票人姓名
     */
    @NotBlank(message = "收票人姓名不能为空")
    private String addressName;

    /**
     * 收票人电话
     */
    @NotBlank(message = "收票人电话不能为空")
    private String addressMobile;

}
