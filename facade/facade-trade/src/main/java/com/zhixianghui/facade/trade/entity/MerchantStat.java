package com.zhixianghui.facade.trade.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商户月统计表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-08-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MerchantStat implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 订单明细实发金额
     */
    private BigDecimal orderItemNetAmount;

    /**
     * 订单明细(总)金额
     */
    private BigDecimal orderItemAmount;

    /**
     * 订单数量
     */
    private Long orderAmount;

    /**
     * 发放人数
     */
    private Long receiverNumber;

    /**
     * 密钥ID
     */
    private Long encryptKeyId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建日期
     */
    private String createDate;
    
    private Date updateTime;

    /**
     * 销售ID
     */
    private Long saleId;

    /**
     * 销售姓名
     */
    private String saleName;


    private Date firstOrderTime;
}
