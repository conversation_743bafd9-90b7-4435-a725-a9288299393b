package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WxMerchantBalance implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer version;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 总金额
     */
    private Long totalAmount;

    /**
     *商户类型
     */
    private Integer merchantType;

    /**
     * 冻结金额
     */
    private Long freezeAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
