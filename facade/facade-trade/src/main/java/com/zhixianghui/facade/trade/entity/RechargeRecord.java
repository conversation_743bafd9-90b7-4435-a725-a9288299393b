package com.zhixianghui.facade.trade.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
    * 充值记录表
    */
@Data
@TableName(value = "tbl_recharge_record")
@Accessors(chain = true)
public class RechargeRecord implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    /**
     * 版本号
     */
    @TableField(value = "VERSION")
    @Version
    private Integer version;

    /**
     * 充值ID
     */
    @TableField(value = "RECHARGE_ORDER_ID")
    private String rechargeOrderId;

    /**
     * 充值金额
     */
    @TableField(value = "RECHARGE_AMOUNT")
    private BigDecimal rechargeAmount;

    /**
     * 过期时间
     */
    @TableField(value = "EXPIRE_TIME")
    private Date expireTime;

    /**
     * 收款方ID
     */
    @TableField(value = "PAYEE_IDENTITY")
    private String payeeIdentity;

    /**
     * 收款方ID类型
     */
    @TableField(value = "PAYEE_IDENTITY_TYPE")
    private String payeeIdentityType;

    /**
     * 收款方协议号
     */
    @TableField(value = "PAYEE_AGREEMENT_NO")
    private String payeeAgreementNo;

    /**
     * 通道ID
     */
    @TableField(value = "CHANNEL_ORDER_ID")
    private String channelOrderId;

    /**
     * 充值时间
     */
    @TableField(value = "TRANS_PAY_TIME")
    private Date transPayTime;

    /**
     * 商户编号
     */
    @TableField(value = "EMPLOYER_NO")
    private String employerNo;

    /**
     * 商户名称
     */
    @TableField(value = "EMPLOYER_NAME")
    private String employerName;

    /**
     * 代征主体NO
     */
    @TableField(value = "MAINSTAY_NO")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @TableField(value = "MAINSTAY_NAME")
    private String mainstayName;

    /**
     * 记账本ID
     */
    @TableField(value = "ACCOUNT_BOOK_ID")
    private String accountBookId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField(value = "CREATE_BY")
    private String createBy;

    /**
     * 通道编号
     */
    @TableField(value = "CHANNEL_CODE")
    private String channelCode;

    /**
     * 通道名称
     */
    @TableField(value = "CHANNEL_NAME")
    private String channelName;

    /**
     * 通道类型
     */
    @TableField(value = "CHANNEL_TYPE")
    private Short channelType;

    /**
     * 充值状态
     */
    @TableField(value = "RECHARGE_STATUS")
    private Short rechargeStatus;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 充值类型
     */
    @TableField(value = "RECHARGE_TYPE")
    private Integer rechargeType;
    /**
     * 渠道打款流水号
     */
    @TableField(value = "CHANNEL_TRX_NO")
    private String channelTrxNo;

    /**
     * 回单地址
     */
    @TableField(value = "RECEIPT_URL")
    private String receiptUrl;

    /**
     * 付款方名称
     */
    @TableField(value = "PAYER_NAME")
    private String payerName;

    /**
     * 付款方账号
     */
    @TableField(value = "PAYER_IDENTITY")
    private String payerIdentity;

    /**
     * 付款方账号类型
     */
    @TableField(value = "PAYER_IDENTITY_TYPE")
    private String payerIdentityType;

    /**
     * 付款方机构
     */
    @TableField(value = "PAYER_BANK_NAME")
    private String payerBankName;

    /**
     * 收款方名称
     */
    @TableField(value = "PAYEE_NAME")
    private String payeeName;

    @TableField(value = "CURRENT_BALANCE")
    private BigDecimal currentBalance;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "ID";

    public static final String COL_VERSION = "VERSION";

    public static final String COL_RECHARGE_ORDER_ID = "RECHARGE_ORDER_ID";

    public static final String COL_RECHARGE_AMOUNT = "RECHARGE_AMOUNT";

    public static final String COL_EXPIRE_TIME = "EXPIRE_TIME";

    public static final String COL_PAYEE_IDENTITY = "PAYEE_IDENTITY";

    public static final String COL_PAYEE_IDENTITY_TYPE = "PAYEE_IDENTITY_TYPE";

    public static final String COL_PAYEE_AGREEMENT_NO = "PAYEE_AGREEMENT_NO";

    public static final String COL_CHANNEL_ORDER_ID = "CHANNEL_ORDER_ID";

    public static final String COL_TRANS_PAY_TIME = "TRANS_PAY_TIME";

    public static final String COL_EMPLOYER_NO = "EMPLOYER_NO";

    public static final String COL_EMPLOYER_NAME = "EMPLOYER_NAME";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_UPDATE_TIME = "UPDATE_TIME";

    public static final String COL_CREATE_BY = "CREATE_BY";

    public static final String COL_CHANNEL_CODE = "CHANNEL_CODE";

    public static final String COL_CHANNEL_NAME = "CHANNEL_NAME";

    public static final String COL_CHANNEL_TYPE = "CHANNEL_TYPE";

    public static final String COL_MAINSTAY_NO = "MAINSTAY_NO";

    public static final String COL_MAINSTAY_NAME = "MAINSTAY_NAME";

    public static final String COL_ACCOUNT_BOOK_ID = "ACCOUNT_BOOK_ID";

    public static final String COL_RECHARGE_STATUS = "RECHARGE_STATUS";

    public static final String COL_CHANNEL_TRX_NO = "CHANNEL_TRX_NO";

    public static final String COL_REMARK = "REMARK";

    public static final String COL_RECHARGE_TYPE = "RECHARGE_TYPE";

    public static final String COL_RECEIPT_URL = "RECEIPT_URL";

    public static final String COL_PAYER_NAME = "PAYER_NAME";

    public static final String COL_PAYER_IDENTITY = "PAYER_IDENTITY";

    public static final String COL_PAYER_IDENTITY_TYPE = "PAYER_IDENTITY_TYPE";

    public static final String COL_PAYER_BANK_NAME = "PAYER_BANK_NAME";

    public static final String COL_PAYEE_NAME = "PAYEE_NAME";
}
