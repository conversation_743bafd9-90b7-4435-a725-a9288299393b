package com.zhixianghui.facade.trade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年11月23日 11:30:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DateQueryDTO implements Serializable {
    private static final long serialVersionUID = 5690190620137264354L;

    private Date beginDate;

    private Date endDate;

    private Date completeBeginDate;

    private Date completeEndDate;
}