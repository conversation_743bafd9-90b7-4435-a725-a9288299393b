package com.zhixianghui.facade.trade.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.dto.ConfirmInvoiceDto;
import com.zhixianghui.facade.trade.dto.IndividualProxyOrderDto;
import com.zhixianghui.facade.trade.dto.ProxyOrderQueryDto;
import com.zhixianghui.facade.trade.entity.IndividualProxyOrder;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;

public interface IndividualProxyOrderFacade {
    IndividualProxyOrder applyInvoice(IndividualProxyOrderDto dto, WeChatLoginVo weChatLoginVo) throws BizException;

    Page<IndividualProxyOrder> listPage(ProxyOrderQueryDto proxyOrderQueryDto,Page page);

    IndividualProxyOrder getProxyOrderById(Long id, String idcardNo);

    IndividualProxyOrder getProxyOrderById(Long id);

    void confirmInvoice(ConfirmInvoiceDto confirmInvoiceDto) throws BizException;

    IndividualProxyOrder cancelProxyOrderById(Long id, String idcardNo);

    String prePay(String orderNo, WeChatLoginVo weChatLoginVo) throws BizException;
}
