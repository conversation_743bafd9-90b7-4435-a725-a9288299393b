package com.zhixianghui.facade.trade.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProxyOrderItem implements Serializable {
    private static final long serialVersionUID = 5473152668365351940L;

    private String invoiceCategoryCode;
    private String invoiceCategoryName;
    private BigDecimal compositeTaxRatio;
    private String specification;
    private Integer unitType;
    private BigDecimal quantity;
    private BigDecimal univalent;
    private BigDecimal invoiceAmount;
}
