package com.zhixianghui.facade.trade.utils;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.enums.wx.WxCodeEnum;
import com.zhixianghui.common.statics.enums.wx.WxDetailStatusEnum;
import com.zhixianghui.common.statics.enums.wx.WxFailReasonEnum;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月13日 11:13:00
 */
public class WxUtil {

    private static final String LOG_KEY_PREFIX = "LOG_KEY";//幂等key前缀

    public static final int LOCK_WAIT_TIME = 60000;//锁等待时间

    public static final long LOCK_LEASE_TIME = 60000;//锁过期时间

    private static final HashMap<Integer, String> CODE_MAP = new HashMap();

    private static final HashMap<String, String> FAIL_REASON_MAP = new HashMap();

    private static final HashMap<Integer, Integer> RETRY_TIMES = new HashMap<>();

    static {
        for (WxCodeEnum wxCodeEnum : WxCodeEnum.values()) {
            CODE_MAP.put(wxCodeEnum.getCode(), wxCodeEnum.getDesc());
        }

        for (WxFailReasonEnum wxFailReasonEnum : WxFailReasonEnum.values()) {
            FAIL_REASON_MAP.put(wxFailReasonEnum.name(), wxFailReasonEnum.getDesc());
        }

        RETRY_TIMES.put(1, MsgDelayLevelEnum.S_10.getValue());
        RETRY_TIMES.put(2, MsgDelayLevelEnum.S_30.getValue());
        RETRY_TIMES.put(3, MsgDelayLevelEnum.M_1.getValue());
        RETRY_TIMES.put(4, MsgDelayLevelEnum.M_5.getValue());
        RETRY_TIMES.put(5, MsgDelayLevelEnum.M_30.getValue());

    }

    public static String getRedisLockKey(String employerNo, String mainstayNo, Integer merchantType) {
        if (merchantType == MerchantTypeEnum.EMPLOYER.getValue()) {
            return TradeConstant.WITHHOLDING_AMOUNT_LOCK_KEY + "::" + employerNo + "::" + mainstayNo;
        }
        return TradeConstant.WITHHOLDING_AMOUNT_LOCK_KEY + "::" + mainstayNo;
    }

    public static String getLogKey(String platTrxNo, int type,MerchantTypeEnum merchantType) {
        return LOG_KEY_PREFIX + "::" + platTrxNo + "::" + type + "::" + merchantType.getValue();
    }

    public static String getRedisRetryKey(String platTrxNo) {
        return TradeConstant.WX_PAY_QUERY_RETRY_REDIS_KEY + "::" + platTrxNo;
    }

    public static HashMap<Integer, String> getCodeMap() {
        return CODE_MAP;
    }

    public static HashMap<String, String> getFailReasonMap() {
        return FAIL_REASON_MAP;
    }

    public static String getCodeDesc(Integer Code) {
        return getCodeMap().get(Code);
    }

    public static String getFailReason(String name) {
        return getFailReasonMap().get(name);
    }

    public static boolean isPaySuccess(String name) {
        return name.equals(WxDetailStatusEnum.SUCCESS.name());
    }

    public static boolean isPayProcessing(String name) {
        return name.equals(WxDetailStatusEnum.PROCESSING.name());
    }

    public static boolean isPayFail(String name) {
        return name.equals(WxDetailStatusEnum.FAIL.name());
    }


    public static int getDelayTime(int count) {
        return RETRY_TIMES.get(count);
    }

}