package com.zhixianghui.facade.trade.vo.esign;

import com.google.common.collect.Maps;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.File;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月29日 11:20:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
public class ESign implements Serializable {
    private static final long serialVersionUID = -1112668291145498059L;
    //日志编号，方便查询日志，建议为业务编号
    private String logNo;
    //文件名称
    private String fileName;
    //流程名称
    private String flowName;
    //doc模板byte数组
    private byte[] fileByte;
    //doc模板路径，fileByte为空时使用此路径
    private String fileUrl;
    //签署组件，可以为多个
    private List<ESignItem> eSignItems;

    @Builder.Default
    private boolean isAddComponents = Boolean.FALSE;

    //执行属性
    private File templateFile;
    private String fileId;
    private String templateFileUrl;
    private String flowId;
    private List<StructComponent> components;
    private Long signValidity;

    //模板id，如果e签宝后台创建模板则创建时传入，接口创建无须传入
    private String templateId;
    //模板映射map
    @Builder.Default
    private HashMap<String, String> simpleFormFields = Maps.newHashMap();

    //返回的签署文件URL
    private String returnFileUrl;
}