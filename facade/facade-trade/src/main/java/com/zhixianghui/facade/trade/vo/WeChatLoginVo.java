package com.zhixianghui.facade.trade.vo;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月14日 17:41:00
 */
@Data
@Accessors(chain = true)
public class WeChatLoginVo implements Serializable {

    private static final long serialVersionUID = 5570709459975520137L;

    private Long id;

    private String realName;

    private String mobile;

    private Integer authStatus;

    private Integer status;

    private String userNo;

    private String wxUserNo;

    private String idCard;

    private Integer gender;

    private String miniOpenId;

    private String miniAppId;

    public static void main(String[] args) {
        final WeChatLoginVo chatLoginVo = new WeChatLoginVo()
                .setIdCard("530427198707100534")
                .setGender(1)
                .setRealName("普海涛")
                .setMobile("13928760902")
                .setAuthStatus(100)
                .setStatus(100)
                .setUserNo("U00001")
                .setWxUserNo("WU00001");
        System.out.println(JsonUtil.toString(chatLoginVo));
        System.out.println(MD5Util.getMD5Hex("pht2112211133"));
    }
}
