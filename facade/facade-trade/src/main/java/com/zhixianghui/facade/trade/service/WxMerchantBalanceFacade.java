package com.zhixianghui.facade.trade.service;

import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;

/**
 *  Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-12-07
 */
public interface WxMerchantBalanceFacade {

    Long getAmount(WxMerchantBalance wxMerchantBalance);

    WxMerchantBalance getOne(WxMerchantBalance wxMerchantBalance);

    void update(WxMerchantBalance wxMerchantBalance);

    //调账
    void adjustment(AdjustmentDTO adjustmentDTO);
}
