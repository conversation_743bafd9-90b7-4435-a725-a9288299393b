package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模板id表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SignTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    protected Long id;

    /**
     * 版本号
     */
    protected Integer version;

    /**
     * 创建时间
     */
    protected Date createTime;

    /**
     * 更新时间
     */
    protected Date updateTime;

    /**
     * 模板id
     */
    protected String templateId;

    /**
     * 文件上传地址
     */
    protected String uploadUrl;

    /**
     * 组件id列表
     */
    protected String componentId;

    /**
     * 删除标识,1删除,0未删除
     */
    private Boolean deleteFlag;

    /**
     * 组件明细
     */
    protected String structComponent;

    /**
     * 签署截止日期, 单位月
     */
    protected Integer signingDeadline;

    /**
     * 协议到期时间, 单位年
     */
    protected Integer agreementExpiresTime;

    /**
     * 签署类型，100 用工企业指定，101代征主体指定
     */
    protected Integer signTemplateType;

    /**
     * 源文件路径
     */
    protected String sourceFileUrl;

    /**
     * 目标文件路径
     */
    protected String targetFileUrl;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     *
     */
    private List<StructComponent> style;

    public void setStructComponent(String structComponent) {
        this.structComponent = structComponent;
        style = JSONArray.parseArray(structComponent, StructComponent.class);
    }

}
