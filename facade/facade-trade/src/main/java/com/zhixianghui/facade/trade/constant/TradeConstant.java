package com.zhixianghui.facade.trade.constant;

/**
 *  交易中心-常量
 */
public class TradeConstant {
    public static final String CMB_BATCH_GRANT_LOCK = "CMB_BATCH_GRANT_LOCK:";
    public static final String WITHDRAW_ALL_NUM = "WITHDRAW:ALL:NUM:";

    public static final String WITHDRAW_ALL_MERCHNAT = "WITHDRAW:ALL:MAP:";

    public static final String GRANT_HANGUP_NOTIFY_MCH_REDIS_PREFIX = "GRANT_HANGUP_NOTIFY_MCH_REDIS_PREFIX";

    /**
     * 风控分布式锁key前缀
     */
    public static final String RCMS_LOCK_KEY = "RCMS_LOCK_KEY";

    /**
     * 预签约分布式锁key前缀
     */
    public static final String PRE_SIGN_LOCK_KEY = "PRE_SIGN_LOCK_KEY";

    /**
     * 预签约改手机分布式锁key前缀
     */
    public static final String PRE_SIGN_MODIFY_LOCK_KEY = "PRE_SIGN_MODIFY_LOCK_KEY";

    /**
     * 鉴权分布式锁key前缀
     */
    public static final String AUTH_LOCK_KEY = "AUTH_LOCK_KEY";

    /**
     * 发起签约分布式锁key前缀
     */
    public static final String CREATE_SIGN_LOCK_KEY = "CREATE_SIGN_LOCK_KEY";

    /**
     * 获取个人印章分布式锁key前缀
     */
    public static final String CREATE_PERSONAL_SIGNATURE_LOCK_KEY = "CREATE_SIGN_LOCK_KEY";

    /**
     * 鉴权结果缓存Key前缀
     */
    public static final String AUTH_RESULT_KEY = "AUTH_RESULT_KEY";

    /**
     * 分组处理，每组通知数量
     */
    public static final Integer NOTIFY_NUM_PER_GROUP = 100;

    /**
     * 分组处理，每组查询数量
     */
    public static final Integer SELECT_NUM_PER_GROUP = 1000;

    /**
     * 最大受理重试次数
     */
    public static final Integer MAX_ACCESS_TIMES = 2;

    /**
     * 受理次数redis前缀
     */
    public static final String ACCEPT_TIME_REDIS_PREFIX = "ACCEPT_TIMES:";

    /**
     * 发放次数redis前缀
     */
    public static final String GRANT_TIME_REDIS_PREFIX = "GRANT_TIMES:";

    /**
     * 发放通知商户redis前缀
     */
    public static final String GRANT_NOTIFY_MCH_REDIS_PREFIX = "GRANT_NOTIFY_MCH:";

    /**
     * 批量发放通知商户redis前缀
     */
    public static final String GRANT_NOTIFY_MCH_BATCH_REDIS_PREFIX = "GRANT_NOTIFY_MCH_BATCH:";

    /**
     * 充值回调通知商户redis前缀
     */
    public static final String RECHARGE_NOTIFY_MCH_REDIS_PREFIX = "RECHARGE_NOTIFY_MCH:";


    /**
     * 受理失败通知商户redis前缀
     */
    public static final String ACCEPT_FAIL_NOTIFY_MCH_REDIS_PREFIX = "ACCEPT_FAIL_NOTIFY_MCH:";

    /**
     * 最大发放重试次数
     */
    public static final int MAX_GRANT_TIMES = 5;


    public static final String INVOICE_LOCK_KEY = "INVOICE_LOCK:";

    /**
     * 查询map有该标识说明订单不存在
     */
    public static final String ORDER_NOT_EXIST_FLAG = "not_exist";

    /**
     * 订单批次过期队列
     */
    public static final String ORDER_BATCH_ZSET_KEY = "ORDER_BATCH_ZSET_KEY:";

    /**
     * 订单批次过期异常次数
     */
    public static final String ORDER_BATCH_ZSET_EXCEPTION = "ORDER_BATCH_ZSET_EXCEPTION:";

    /**
     *  订单号发放中队列
     */
    public static final String ORDER_GRANTING_ZSET_KEY = "ORDER_GRANTING_ZSET_KEY:";

    /**
     * 发放中队列监控时间（分钟）
     */
    public static final int GRANTING_WATCH_MAX_TIME = 30;

    /**
     * 风控临时记录缓存前缀
     */
    public static final String RISK_ORDER_TMP = "RISK_ORDER_TMP";

    /**
     * 账单支付次数
     */
    public static final String FEE_RETRY_PREFIX = "FEE:RETRY:TIMES:";


    /**
     * 订单金额预扣锁
     */
    public static final String WITHHOLDING_AMOUNT_LOCK_KEY = "WITHHOLDING_AMOUNT_LOCK_KEY";


    public static final String WX_PAY_QUERY_RETRY_REDIS_KEY = "WX_PAY_QUERY_RETRY_REDIS_KEY";


    /***
     * 来账信息入账锁
     */
    public static final String INCOME_RECORD_REDIS_KEY = "INCOME_RECORD";


    public static final Integer WX_PAY_QUERY_RETRY_COUNT = 5;

    public static final String DYNAMIC_MSG_KEY = "DYNAMIC:MSG:";

    public static final Integer DYNAMIC_EXPIRE = 60 * 60 * 24;

    public static final Integer JXH_PAY_QUERY_RETRY_COUNT = 5;

    public static final String AC_AMOUNT_LOCK_KEY = "AC_AMOUNT_LOCK_KEY";

    /**
     * 本地账户退款重试次数前缀
     */
    public static final String ACCOUNT_REFUND_TIMES_PRE_KEY = "ACCOUNT_REFUND_TIMES_PRE_KEY:";

    /**
     * 本地账户退款最大重试次数
     */
    public static final Integer MAX_ACCOUNT_REFUND_TIMES = 5;
}
