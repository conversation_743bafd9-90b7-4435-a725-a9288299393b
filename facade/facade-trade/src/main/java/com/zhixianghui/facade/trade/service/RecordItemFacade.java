package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.bo.RecordItemGroupBo;
import com.zhixianghui.facade.trade.dto.RiskControlAmountDto;
import com.zhixianghui.facade.trade.dto.RiskControlNumDto;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.vo.ServiceConfirmVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 打款交易流水表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
public interface RecordItemFacade {

    /**
     * 退回本地冻结金额
     * @param platTrxNo
     * @throws BizException
     */
    void refundLocalFrozenAmount(String platTrxNo) throws BizException;

    /**
     * 分页查询订单明细
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return 分页结果
     */
    PageResult<List<RecordItem>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 获取发放累计金额
     * @param beginDate 开始时间，一般比completeBeginTime前，由于定位分表所在表
     * @param endDate 截止时间，由于定位分表所在表
     * @param updateBeginTime 统计开始时间
     * @param updateEndTime 统计截止时间
     * @param mainstayNo 代征主体no
     * @param receiveIdCardNo 接收者身份证
     * @return
     */
    BigDecimal getSumAmount(Date beginDate, Date endDate, Date updateBeginTime, Date updateEndTime, String mainstayNo, String receiveIdCardNo);

    /**
     * 获取idcard + mainstay的组合
     * @param beginDate 开始日期
     * @param endDate 截止日期
     * @param offset 分页起游标
     * @param pageSize 分页size
     * @return
     */
    List<RecordItemGroupBo> getIdcardNoMainstayGroup(Date beginDate, Date endDate,Date startDateTime,Date endDateTime, int offset, int pageSize);

    /**
     * 反查通道并处理
     * @param remitPlatTrxNo 打款流水号
     * @return 对应状态的提示
     */
    String reverseQuery(String remitPlatTrxNo);

    /**
     * 统计流水总条数
     * @param paramMap 条件
     * @return 总条数
     */
    Long countRecordItem(Map<String, Object> paramMap);

    /**
     * 根据支付渠道流水号查询
     * @param channelTrxNo
     * @return
     */
    RecordItem getByChannelTrxNo(String channelTrxNo);

    /**
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param mchNo 商户列表
     * @param subTableBeginDate
     * @param subTableEndDate
     * @return 订单记录
     */
    List<RecordItem> listByTime(String beginTime, String endTime, Date subTableBeginDate, Date subTableEndDate, String mchNo, PageParam pageParam);


    /**
     * 根据打款流水号查询
     * @param remitPlatTrxNo
     * @return
     */
    RecordItem getByRemitPlatTrxNo(String remitPlatTrxNo);

    /**
     * 更新打款流水表
     * @param recordItem
     * @return
     */
    void update(RecordItem recordItem);

    List<RecordItem> listByTime(Date beginTime, Date endTime, String mchNo, PageParam pageParam);

    Map<String, Object> coreIndexStatistics(Map<String, Object> paramMap);

    List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> paramMap);

    List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> paramMap);

    Map<String, Object> coreIndexReceivedUserCount(Map<String, Object> paramMap);

    List<Map<String, Object>> coreIndexDailyDetailReceivedUserCount(Map<String, Object> paramMap);

    List<Map<String, Object>> coreIndexDetailMonthlyReceivedUserCount(Map<String, Object> paramMap);

    List<ServiceConfirmVo> getWorkCategoryCode(Map<String, Object> paramMap, int offset, Integer pageSize);

    List<RecordItem> listByOffset(Map<String, Object> paramMap, int offset, int maxSize);

    Integer getWorkCategoryCodeCount(Map<String, Object> paramMap);

    Map<String, RiskControlAmountDto> getSumAmountGroup(Date beginDate, Date endDate, Date updateBeginTime,
                                                        Date updateEndTime, String receiveIdCardNo);

    RecordItem getByPlatTrxNo(String platTrxNo);

    Map<String, RiskControlAmountDto> getMchAmountGroup(Date queryStartDate, Date queryEndDate, Date timeStrOfZero, Date date, String employerNo);

    RecordItem getOne(Map<String, Object> paramMap);

    Map<String, RiskControlNumDto> getCountTradeTimes(String platTrxNo,String employerNo, Date endTime, Date startTime, String idCardNo, String userName, String receiveAccount, BigDecimal orderAmount);
}
