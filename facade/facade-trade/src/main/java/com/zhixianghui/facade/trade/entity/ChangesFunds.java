package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ChangesFunds implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @Version
    private Integer version = 0;

    /**
     * 唯一键
     */
    private String logKey;

    /**
     * 所属商户编号
     */
    private String mchNo;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 所属商户名称
     */
    private String mchName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 订单号
     */
    private String platTrxNo;

    /**
     * 资金变动类型
     */
    private Integer amountChangeType;

    /**
     * 变动金额
     */
    private Long amount;

    /**
     * 冻结金额
     */
    private Long frozenAmount;

    /**
     * 变动前金额
     */
    private Long beforeAmount;

    /**
     * 变动前冻结金额
     */
    private Long beforeFrozenAmount;

    /**
     * 变动后金额
     */
    private Long afterAmount;

    /**
     * 变动后冻结金额
     */
    private Long afterFrozenAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人
     */
    private String operator;


}
