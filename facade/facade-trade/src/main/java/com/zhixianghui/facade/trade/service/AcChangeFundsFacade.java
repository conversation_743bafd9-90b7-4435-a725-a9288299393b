package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.common.statics.result.PageResult;
import java.util.List;
import java.util.Map;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.dto.ChangesFoundsDTO;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.vo.ChangesFundsVo;

/**
 * 账户资金变动Service接口
 * <AUTHOR> @date 2024-04-01
 */
public interface AcChangeFundsFacade {

//    //新增账户资金变动
//    void insert(AcChangeFunds AcChangeFunds) throws BizException;
//
//    //修改账户资金变动
//    void update(AcChangeFunds AcChangeFunds) throws BizException;
//
//    //通过id查看账户资金变动
//    AcChangeFunds getById(Long id);
//
//    // 根据条件查询列表
//    public List<AcChangeFunds> listBy(Map<String, Object> paramMap);

    //条件分页查询账户资金变动列表
    PageResult<List<ChangesFundsVo>> listPage(ChangesFoundsDTO changesFoundsDTO);

}
