package com.zhixianghui.facade.trade.vo;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.SignSmsEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.SignRecord;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/4/25 11:02
 */
@Data
public class SignRecordVo implements Serializable {
    private static final long serialVersionUID = -3195469362162953492L;
    private String name;
    private String idCard;
    private String phone;
    private String bankCardCode;
    private String employerNo;
    private String employerName;
    private String mainstayNo;
    private String mainstayName;
    private Long id;
    private String signUrl;
    private String cooperateName;
    private Integer signStatus;
    private String fileUrl;

    public PreSignReqVo toPreSignReqVo(SignRecordVo recordVo) {
        PreSignReqVo reqVo = new PreSignReqVo();
        reqVo.setIdCardNo(recordVo.getIdCard());
        reqVo.setName(recordVo.getName());
        reqVo.setPhoneNo(recordVo.getPhone());
        reqVo.setMainstayNo(recordVo.getMainstayNo());
        return reqVo;
    }

    public CreateSignReqVo toCreateSignReqVo(SignRecord record) {
        CreateSignReqVo createSignReqVo = new CreateSignReqVo();
        createSignReqVo.setUserId(record.getUserId());
        return createSignReqVo;

    }

    public SignRecord toSignRecord(SignRecordVo signRecordVo, String userId, String mainstayNo, String employerName, String employerNo, Integer sms) {
        SignRecord signRecord = new SignRecord();
        signRecord.setCreateTime(new Date());
        signRecord.setUpdateTime(new Date());
        signRecord.setUserId(userId);
        signRecord.setEmployerNo(employerNo);
        signRecord.setEmployerName(employerName);
        signRecord.setMainstayNo(mainstayNo);
        signRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
        signRecord.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        signRecord.setReceiveNameEncrypt(signRecordVo.getName());
        signRecord.setReceiveIdCardNoEncrypt(signRecordVo.getIdCard());
        signRecord.setReceivePhoneNoEncrypt(signRecordVo.getPhone());
        signRecord.getJsonEntity().setSignType(String.valueOf(SignTypeEnum.RSA.getValue()));
        signRecord.setSmsSendFrequency(0);
        signRecord.setSignType(ChannelSignTypeEnum.MSG.getValue());
        signRecord.setReceiveAccountNoEncrypt(signRecordVo.getBankCardCode());
        return signRecord;

    }

    public SignRecord build(FreelanceStat freelanceStat) {
        SignRecord signRecord = new SignRecord();
        signRecord.setCreateTime(new Date());
        signRecord.setUpdateTime(new Date());
        signRecord.setMainstayNo(freelanceStat.getMainstayNo());
        signRecord.setEmployerNo(freelanceStat.getEmployerNo());
        signRecord.setEmployerName(freelanceStat.getEmployerName());
        signRecord.setMainstayName(freelanceStat.getMainstayName());
        signRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
        signRecord.setEncryptKeyId(freelanceStat.getEncryptKeyId());
//        signRecord.setReceiveIdCardNo(freelanceStat.getReceiveIdCardNo());
        signRecord.setReceiveIdCardNoEncrypt(AESUtil.decryptECB(freelanceStat.getReceiveIdCardNo(),  EncryptKeys.getEncryptKeyById(freelanceStat.getEncryptKeyId()).getEncryptKeyStr()));
//        signRecord.setReceiveName(freelanceStat.getReceiveName());
        signRecord.setReceiveNameEncrypt(AESUtil.decryptECB(freelanceStat.getReceiveName(),  EncryptKeys.getEncryptKeyById(freelanceStat.getEncryptKeyId()).getEncryptKeyStr()));
//        signRecord.setReceivePhoneNo(freelanceStat.getReceivePhoneNo());
        signRecord.getJsonEntity().setSignType(String.valueOf(SignTypeEnum.RSA.getValue()));
        signRecord.setSmsSendFrequency(0);
        return signRecord;
    }



}
