package com.zhixianghui.facade.trade.vo;

import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.facade.trade.entity.UserInfo;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月01日 14:42:00
 */
@Data
@Accessors(chain = true)
public class UserAuthVo  implements Serializable {
    private static final long serialVersionUID = 7366275495984092137L;

    private String phone;

    private String name;

    private String idCardNo;

    private String idCardBackUrl;

    private String idCardFrontUrl;

    private String idCardCopyUrl;

    private Integer AuthStatus;

    public Integer getAuthStatus(){
        if (StringUtils.isNotBlank(idCardCopyUrl) || (StringUtils.isNotBlank(idCardBackUrl) && StringUtils.isNotBlank(idCardFrontUrl))){
            return AuthStatusEnum.SUCCESS.getValue();
        }else{
            return AuthStatusEnum.UN_AUTH.getValue();
        }
    }
}