package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class AcMerchantBalanceAddDto implements Serializable {

    // 商户编号
    private String mchNo;

    // 商户名称
    private String mchName;

    // 供应商编号
    private String mainstayNo;

    // 供应商名称
    private String mainstayName;

    // 通道编号
    private String payChannelNo;

    // 通道名称
    private String payChannelName;

    // 商户类型 com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum
    private Integer merchantType;

}
