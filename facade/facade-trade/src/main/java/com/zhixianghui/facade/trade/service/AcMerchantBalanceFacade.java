package com.zhixianghui.facade.trade.service;


import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.dto.AcMerchantBalanceAddDto;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.dto.MainstayAdjustmentDTO;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;

import java.util.List;
import java.util.Map;


/**
 * 账户资金变动Service接口
 * <AUTHOR> @date 2024-04-01
 */
public interface AcMerchantBalanceFacade {

    boolean createMerchantBalance(AcMerchantBalanceAddDto dto);

    /***
     * 获取本地账户余额
     * @param acMerchantBalance
     * @return
     */
    Long getAmount(AcMerchantBalance acMerchantBalance);

    //新增账户
//    void insert(AcMerchantBalance AcMerchantBalance) throws BizException;

//    //修改账户资金变动
//    void update(AcMerchantBalance AcMerchantBalance) throws BizException;
//    //通过id查看账户资金变动
//    AcMerchantBalance getById(Long id);
//    // 根据条件查询列表
//    public List<AcMerchantBalance> listBy(Map<String, Object> paramMap);

    //条件分页查询账户资金变动列表
//    PageResult<List<AcMerchantBalance>> listPage(Map<String, Object> paramMap, PageQuery pageQuery);

    void mainstayBalanceSyncOne(String mainstayNo);

    //调账
    void adjustment(AdjustmentDTO adjustmentDTO);

}
