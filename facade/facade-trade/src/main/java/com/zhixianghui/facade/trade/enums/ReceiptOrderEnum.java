package com.zhixianghui.facade.trade.enums;

import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName ReceiptOrderEnum
 * @Description TODO
 * @Date 2022/9/19 10:52
 */
@AllArgsConstructor
@Getter
public enum  ReceiptOrderEnum {

    PERSONAL_RECEIPT(100,ReportTypeEnum.MERCHANT_PERSONNAL_RECEIPT_ORDER_EXPORT,"个人电子回单"),

    BUSINESS_RECEIPT(101,ReportTypeEnum.MERCHANT_BUSINESS_RECEIPT_ORDER_EXPORT,"企业电子回单");

    private Integer value;

    private ReportTypeEnum reportTypeEnum;

    private String desc;

    public static ReceiptOrderEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }

}
