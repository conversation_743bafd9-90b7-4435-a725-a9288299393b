package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单批次表 Facade类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
public interface OrderFacade {
    /**
     * 创建批次订单
     * @param order 批次订单对象
     * @return 批次订单id
     */
    Long insert(Order order);

    /**
     * 开始受理批次订单
     * @param order 批次订单
     */
    void startAccept(Order order);

    /**
     * 分页查询批次订单
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return 批次订单
     */
    PageResult<List<Order>> listPage(Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 更新批次订单
     * @param order 批次订单
     */
    void update(Order order);

    /**
     * 根据条件获取批次订单
     * @param paramMap 条件
     * @return 批次订单
     */
    Order getOne(Map<String, Object> paramMap);

    /**
     * 取消发放
     * @param order 批次订单
     */
    void cancelBatchOrder(Order order);

    void cancelOrderItem(String platBatchNo);

    /**
     * 统计满足条件的订单批次条数
     * @param paramMap 条件
     * @return 条数
     */
    Long countOrder(Map<String, Object> paramMap);

    /**
     * 根据条件查询待开发票金额
     * @param paramMap 条件
     * @return 待开发票金额
     */
    BigDecimal sumWaitInvoiceAmount(Map<String, Object> paramMap);

    /**
     * 根据条件查询待开发票金额
     * @param paramMap 条件
     * @return 待开发票金额
     */
    BigDecimal sumWaitCepInvoiceAmount(Integer amountType, Map<String, Object> paramMap);

    BigDecimal sumWaitCkhInvoiceAmount(Map<String, Object> paramMap);

    /**
     * 添加充值记录
     * @param rechargeRecord
     * @return
     */
    RechargeRecord addRechargeRecord(RechargeRecord rechargeRecord);

    RechargeRecord upDateRechargeRecord(RechargeRecord rechargeRecord);

    WithdrawRecord Withdraw(WithdrawDto withdrawDto) throws BizException;

    void updateWithdrawRecord(String outBizNo,SuccessFailEnum successFailEnum, String failReason);

    void delete(OrderDeleteDTO orderDeleteDTO);

    Order getByPlatBatchNo(String platBatchNo);

    boolean isExistNotCompleteOrder(String employerNo, String mainstayNo);
}
