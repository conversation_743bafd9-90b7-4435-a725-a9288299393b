package com.zhixianghui.facade.trade.dto;

import com.zhixianghui.common.util.utils.MD5Util;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserInfoQueryDto implements Serializable {
    private static final long serialVersionUID = 1613374169606317172L;
    private String receiveName;
    private String receiveNameMd5;
    private String receiveIdCardNo;
    private String receiveIdCardNoMd5;

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
        this.receiveNameMd5 = MD5Util.getMixMd5Str(receiveName);
    }

    public void setReceiveIdCardNo(String receiveIdCardNo) {
        this.receiveIdCardNo = receiveIdCardNo;
        this.receiveIdCardNoMd5 = MD5Util.getMixMd5Str(receiveIdCardNo);
    }
}
