package com.zhixianghui.facade.trade.entity;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.trade.enums.SignerTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 签约信息表
 *
 * <AUTHOR>
 * @version 创建时间： 2021-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SignRecord extends BasePrivateItem {

    private static final long serialVersionUID = 1005333933680043392L;

    private Long id;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 该用户唯一标识
     */
    private String userId;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 信息鉴权状态
     */
    private Integer infoStatus;

    /**
     * 签约状态
     */
    private Integer signStatus;

    /**
     * 错误信息
     */
    private String errMsg;

    /**
     * 错误码
     */
    private String errCode;

    /**
     * E签宝用户ID
     */
    private String accountId;

    /**
     * E签宝文件ID
     */
    private String fileId;

    /**
     * E签宝流程ID
     */
    private String flowId;

    /**
     * 签约文件本地URL
     */
    private String fileUrl;

    /**
     * 签约模式
     */
    private Integer signType;

    /**
     * 重定向地址
     */
    private String redirectUrl;

    /**
     * 回调通知地址
     */
    private String notifyUrl;

    /**
     * 操作状态
     */
    private Integer operateStatus;

    /**
     * 操作信息
     */
    private String operateMsg;

    /**
     * 发送次数
     */
    private Integer smsSendFrequency;

    /**
     * 短信发送时间
     */
    private Date smsSendTime;

    /**
     * 身份证背面地址
     */
    private String idCardBackUrl;

    /**
     * 身份证正面地址
     */
    private String idCardFrontUrl;

    private String idCardCopyUrl;

    /**
     * 证件类型，对应枚举
     * @see com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum
     */
    private Integer idCardType;

    /**
     * 半身照
     */
    private String cerFaceUrl;

    /**
     * 个人签章
     */
    private String personalSignature;

    /**
     * JSON
     */
    private String jsonStr;

    /**
     * 全局个人用户ID
     */
    private String openUserId;

    /**
     * json字段的处理
     */
    private JsonEntity jsonEntity = new JsonEntity();

    /**
     * url签约模式，保存签约地址
     */
    private String signUrl;

    /**
     * 签署类型
     */
    private Integer signerType = SignerTypeEnum.ONLINE_SIGN.getValue();

    @Data
    public static class JsonEntity implements Serializable {
        private static final long serialVersionUID = -162362353726273481L;
        /**
         * 签名类型
         */
        String signType;
    }

    @Deprecated
    public String getJsonStr() {
        return JsonUtil.toString(jsonEntity);
    }

    @Deprecated
    public void setJsonStr(String jsonStr) {
        this.jsonStr = jsonStr;
        this.jsonEntity = JsonUtil.toBean(jsonStr, JsonEntity.class);
    }
}
