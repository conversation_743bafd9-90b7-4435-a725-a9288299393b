package com.zhixianghui.facade.trade.service;

import com.zhixianghui.common.statics.dto.withdraw.WithdrawRecordQueryDto;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;

import java.util.List;
import java.util.Map;

public interface WithdrawRecordFacade {
    PageResult<List<WithdrawRecord>> listWithRecordPage(WithdrawRecordQueryDto withdrawRecordQueryDto, PageParam pageParam);

    PageResult<List<WithdrawRecord>> listWithRecordPage(Map<String, Object> paramMap, PageParam pageParam);

    Map<String, Object> sumWithdrawRecord(Map<String, Object> paramMap);

    WithdrawRecord getByWithdrawRecordNo(String withdrawNo);
}
