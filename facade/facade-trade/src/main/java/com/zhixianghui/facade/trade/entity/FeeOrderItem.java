package com.zhixianghui.facade.trade.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FeeOrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer version;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 账单批次号
     */
    private String feeBatchNo;

    /**
     * 账单明细号
     */
    private String feeItemNo;

    /**
     * 账单类型
     */
    private Integer feeType;

    private String employerNo;

    private String employerName;

    private String mainstayNo;

    private String mainstayName;

    private BigDecimal payAmount;

    /**
     * 凭证地址
     */
    private String cetificateUrl;

    /**
     * 发起支付时间
     */
    private Date payTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 支付状态
     */
    private Integer status;

    /**
     * 账单来源
     */
    private Integer feeSource;
}
