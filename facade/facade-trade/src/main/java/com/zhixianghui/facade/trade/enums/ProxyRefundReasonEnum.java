package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName ProxyRefundReasonEnum
 * @Description TODO
 * @Date 2023/1/4 18:10
 */
@Getter
@AllArgsConstructor
public enum ProxyRefundReasonEnum {

    INCONSISTENT(100,"开票类目与实际业务不符"),

    SINGLE_OUT_LIMIT(101,"单笔开票金额超限"),

    TOTLE_OUT_LIMIT(102,"累计开票金额超限"),

    WRONG_TITLE(103,"错误的发票抬头"),

    WITHOUT_CREDENTIAL(104,"凭证文件缺失/不实"),

    OTHERS(105,"其他");

    private int value;

    private String desc;
}
