package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

@AllArgsConstructor
@Getter
@ToString
public enum IdentityTypeEnum {

    /**
     * 支付宝用户ID
     */
    ALIPAY_USER_ID("ALIPAY_USER_ID","支付宝用户ID"),
    /**
     * 记账本ID
     */
    ACCOUNT_BOOK_ID("ACCOUNT_BOOK_ID","记账本ID"),
    /**
     * 支付宝登录账号
     */
    ALIPAY_LOGON_ID("ALIPAY_LOGON_ID","支付宝登录账号"),

    /**
     * 微信特约商户号
     */
    WX_MERCHANT_NO("WX_SUBMCH_ID","微信特约商户号"),

    /**
     * 银行账号
     */
    BANK_ACCOUNT_NO("BANK_ACCOUNT_NO","银行账号"),

    /**
     * 银行账号
     */
    CMB_ACCOUNTBOOK_ID("CMB_ACCOUNTBOOK_ID","招行记账本"),

    JOINPAY_ACCOUNT("JOINPAY_ACCOUNT","汇聚支付备付金账号")
    ;

    private final String value;
    private final String desc;

}
