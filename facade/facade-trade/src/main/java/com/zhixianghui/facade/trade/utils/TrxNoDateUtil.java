package com.zhixianghui.facade.trade.utils;

import com.zhixianghui.common.util.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 获取分表字段日期
 * @date 2020-11-09 10:54
 **/
public class TrxNoDateUtil {
    /**
     * 项目上线 最小时间
     */
    public static Date MIN_DATE = DateUtil.DATE_TIME_FORMATTER.parseDateTime("2020-11-01 00:00:00").toDate();

    /**
     * 平台号解析出日期字符串
     * @param trxNo 平台号
     * @return 日期字符串
     */
    public static String getDateStrFromTrxNo(String trxNo){
        if(StringUtils.isBlank(trxNo)){
            return null;
        }
        String shortDate = trxNo.substring(1,9);
        return DateUtil.COMPACT_DATE_FORMATTER.parseDateTime(shortDate).toString(DateUtil.DATE_FORMATTER);
    }

    /**
     * 平台号解析出日期字符串（加减月份）
     * @param trxNo 平台号
     * @param addMonth 加减月份
     * @return 日期字符串
     */
    public static String getDateStrFromTrxNo(String trxNo,int addMonth){
        if(StringUtils.isBlank(trxNo)){
            return null;
        }
        String shortDate = trxNo.substring(1,9);
        DateTime needDate = DateUtil.COMPACT_DATE_FORMATTER.parseDateTime(shortDate).plusMonths(addMonth);
        return needDate.toString(DateUtil.DATE_FORMATTER);
    }

    /**
     * 平台号解析出日期
     * @param trxNo 平台号
     * @return 日期
     */
    public static Date getDateFromTrxNo(String trxNo){
        if(StringUtils.isBlank(trxNo)){
            return null;
        }
        String shortDate = trxNo.substring(1,9);
        return DateUtil.COMPACT_DATE_FORMATTER.parseDateTime(shortDate).toDate();
    }

    /**
     * 平台号解析出日期
     * @param trxNo 平台号
     * @param addMonth 加减月份
     * @return 日期
     */
    public static Date getDateFromTrxNo(String trxNo,int addMonth){
        if(StringUtils.isBlank(trxNo)){
            return null;
        }
        String shortDate = trxNo.substring(1,9);
        DateTime needDate = DateUtil.COMPACT_DATE_FORMATTER.parseDateTime(shortDate).plusMonths(addMonth);
        return needDate.toDate();
    }

    /**
     * 平台号解析出日期
     * @param trxNo 平台号
     * @param addDay 加减天数
     * @return 日期
     */
    public static Date getDateFromTrxNoAddDay(String trxNo,int addDay){
        if(StringUtils.isBlank(trxNo)){
            return null;
        }
        String shortDate = trxNo.substring(1,9);
        DateTime needDate = DateUtil.COMPACT_DATE_FORMATTER.parseDateTime(shortDate).plusDays(addDay);
        return needDate.toDate();
    }

    /**
     * 平台号解析出日期字符串（加减天数）
     * @param trxNo 平台号
     * @param addDay 加减天数
     * @return 日期字符串
     */
    public static String getDateStrFromTrxNoAddDay(String trxNo,int addDay){
        if(StringUtils.isBlank(trxNo)){
            return null;
        }
        String shortDate = trxNo.substring(1,9);
        DateTime needDate = DateUtil.COMPACT_DATE_FORMATTER.parseDateTime(shortDate).plusDays(addDay);
        return needDate.toString(DateUtil.DATE_FORMATTER);
    }


    /**
     * 根据查询条件内的时间区间填充分库分表字段
     * @param paramMap 包含时间区间查询条件的查询map
     */
    public static void putCreateDateByMapDate(Map<String,Object> paramMap){
        //是否有查询时间
        Date createBeginDate = (Date) paramMap.get("createBeginDate");
        Date createEndDate = (Date) paramMap.get("createEndDate");
        Date completeBeginDate = (Date) paramMap.get("completeBeginDate");
        Date completeEndDate = (Date) paramMap.get("completeEndDate");

        if(createBeginDate!= null && createEndDate!=null){
            paramMap.put("beginDate",createBeginDate);
            paramMap.put("endDate",createEndDate);
            return;
        }

        if(completeBeginDate!= null && completeEndDate!=null){
            paramMap.put("beginDate", TrxNoDateUtil.compareMinWithEndDate(DateUtil.addMonth(completeBeginDate, -3)));
            paramMap.put("endDate",completeEndDate);
        }
    }

    /**
     * 与上线最小日期比较,返回大的值
     * @param compareDate 要比较的值
     * @return 较大的值
     */
    public static Date compareMinDate(Date compareDate){
        Date minDate = DateUtil.DATE_TIME_FORMATTER.parseDateTime("2020-11-01 00:00:00").toDate();
        return DateUtil.compare(compareDate,minDate, Calendar.SECOND) > 0?compareDate:minDate;
    }

    /**
     * 与上线最小日期比较,返回大的值(最大为当前时间)
     * @param compareDate 要比较的值
     * @return 较大的值
     */
    public static Date compareMinWithEndDate(Date compareDate){
        Date minDate = DateUtil.DATE_TIME_FORMATTER.parseDateTime("2020-11-01 00:00:00").toDate();
        Date bigDate = DateUtil.compare(compareDate,minDate, Calendar.SECOND) > 0?compareDate:minDate;
        Date now = new Date();
        return DateUtil.compare(bigDate,now, Calendar.SECOND) > 0?now:bigDate;
    }

    public static void main(String[] args) {
        System.out.println(getDateStrFromTrxNo("O20201105000000001"));
        System.out.println(getDateStrFromTrxNo("O20201105000000001",3));
        System.out.println(getDateStrFromTrxNo("O20200105000000001",-2));
        System.out.println(getDateStrFromTrxNoAddDay("O20200131000000001",1));
        System.out.println(getDateStrFromTrxNoAddDay("O20200131000000001",-1));

        System.out.println("==================compare Date==============");
        System.out.println(compareMinDate(getDateFromTrxNo("O20191105000000001")));
        System.out.println(compareMinDate(getDateFromTrxNo("O20201105000000001")));
        System.out.println(compareMinDate(getDateFromTrxNo("O20121105000000001")));
        System.out.println(compareMinDate(getDateFromTrxNo("O20251105000000001")));
    }
}
