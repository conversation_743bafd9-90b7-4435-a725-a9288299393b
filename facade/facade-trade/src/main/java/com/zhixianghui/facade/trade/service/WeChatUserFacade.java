package com.zhixianghui.facade.trade.service;

import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.entity.WechatInfo;
import com.zhixianghui.facade.trade.entity.WechatUserInfo;
import com.zhixianghui.facade.trade.vo.WxBindMobileReqVo;

/**
 * <AUTHOR>
 * @date 2022/2/14 10:54
 */
public interface WeChatUserFacade {

    WechatUserInfo exist(String userNo);

    UserInfo getUserInfoByPhone(String phone);

    String getAppCode(Long jobId);

    WechatUserInfo getOrRegister(String phone, String code);

    String getPhoneByOpenIdAndAppId(String openId, String appId);

    WechatUserInfo register(WxBindMobileReqVo wxBindMobileReqVo);

    WechatInfo getMiniUserByOpenIdAndAppId(String openId, String appId);
}
