package com.zhixianghui.facade.trade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @description 订单发起方式
 * @date 2020-11-05 10:50
 **/
@AllArgsConstructor
@Getter
@ToString
public enum LaunchWayEnum {
    /**
     * 用工企业后台
     */
    EMPLOYER(100,"用工企业后台" ),
    /**
     * API接口
     */
    API(101,"API接口" ),

    ;

    private final int value;
    private final String desc;

}
