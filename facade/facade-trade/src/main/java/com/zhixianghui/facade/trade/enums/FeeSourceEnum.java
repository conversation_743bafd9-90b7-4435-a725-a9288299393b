package com.zhixianghui.facade.trade.enums;

import com.zhixianghui.common.statics.annotations.EnumDesc;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName FeeSourceEnum
 * @Description TODO
 * @Date 2022/11/18 11:35
 */
@Getter
@AllArgsConstructor
@EnumDesc(name = "交付类型", type = SystemTypeEnum.CKH_MANAGEMENT)
public enum FeeSourceEnum {


    PLATFORM_ORDER(100,"平台订单"),

    OUTSIDE_ORDER(101,"外部订单");

    private int value;

    private String desc;

}
