package com.zhixianghui.facade.trade.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 鉴权成功记录表
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-06
 */
@Data
public class AuthRecord implements Serializable {

    private static final long serialVersionUID = 1723563342893372733L;

    /**
     * id
     */
    private Long id;

    /**
     * 鉴权请求号
     */
    private String authNo;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 版本
     */
    private Integer version = 0;

    /**
     * 持卡人身份证号码md5
     */
    private String receiveIdCardNoMd5;

    /**
     * 收款账户MD5
     */
    private String receiveAccountNoMd5;

    /**
     * 持卡人姓名MD5
     */
    private String receiveNameMd5;

    /**
     * 手机号MD5
     */
    private String receivePhoneNoMd5;

    /**
     * 鉴权类型
     * @see com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum
     */
    private Integer authType;

    private String protocolVersion;

    private String protocolNo;

    /**
     * 鉴权通道编号 {@link com.zhixianghui.common.statics.enums.auth.AuthChannelEnum}
     */
    private String authChannel;
}
