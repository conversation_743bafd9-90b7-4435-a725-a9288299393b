package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class InvoiceWorkerItem implements Serializable {
    private static final long serialVersionUID = 46660275290493187L;

    /**
     * 姓名
     */
    private String workerName;
    /**
     * 身份证号码
     */
    private String workerIdcard;
    /**
     * 手机号
     */
    private String workerPhone;
    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;

    private boolean signStatus;

    private Integer authStatus;

    protected Integer jobStatus;
}
