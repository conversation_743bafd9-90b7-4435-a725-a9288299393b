package com.zhixianghui.facade.trade.dto;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024/8/29 11:16 
 */
@Data
public class CmbChangesFoundsQueryDTO extends PageQueryVo {

    private static final long serialVersionUID = -5651061658838833411L;

    /**
     * 唯一健
     */
    private String logKey;

    /**
     * 所属商户编号
     */
    private String mchNo;

    /**
     * 所属商户名称
     */
    private String mchName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 流水号
     */
    private String platTrxNo;

    /**
     * 批次号
     */
    private String platBatchNo;

    /**
     * 资金变动类型
     */
    private Integer amountChangeType;

    /**
     * 创建时间
     */
    private Date createTimeBegin;

    /**
     * 创建时间
     */
    private Date createTimeEnd;

}