package com.zhixianghui.facade.trade.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Data
public class AuthReqDto implements Serializable {

    private static final long serialVersionUID = -4749410679183747662L;
    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;
    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号码不能为空")
    private String idCardNo;
    /**
     * 银行账户
     */
    private String bankAccountNo;

    /**
     * 电话号码
     */
    private String phoneNo;

    /**
     * 鉴权类型
     * @see com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum
     */
    @NotNull(message = "鉴权类型不能为空")
    private Integer authType;

    /**
     * 鉴权通道
     * @see com.zhixianghui.common.statics.enums.auth.AuthChannelEnum
     */
    @NotNull(message = "通道类型不能为空")
    private String authChannel;
}
