package com.zhixianghui.facade.flow.vo.work;

import com.zhixianghui.facade.flow.enums.ComponentTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SignComponent
 * @Description TODO
 * @Date 2022/4/19 16:42
 */
@Data
public class SignComponent extends BaseComponent{

    /**
     * 线上签约文案
     */
    private String tips;

    /**
     * 上传组件文案
     */
    private String uploadText;

    /**
     * 模板文件地址
     */
    private String templateFileUrl;

    /**
     * 上传文件地址
     */
    private List<String> urls;

    /**
     * 签约文件地址
     */
    private String signFileUrl;

    /**
     * 校验区
     */
    private List<String> field;

    /**
     * 模板文件名称
     */
    private String templateFileName;

    private String value;

    public SignComponent(){
        this.type = ComponentTypeEnum.SIGN_COMPONENT.getType();
    }
}
