package com.zhixianghui.facade.flow.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName DataObjectEnum
 * @Description TODO
 * @Date 2021/5/7 15:44
 */
public enum  DataObjectEnum {

    /**
     * 主体认证
     */
    MAIN_AUTH(100,"用工企业","MerchantFlowType",100,"主体信息认证"),
    /**
     * 创建合伙人
     */
    CREATE_AGENT(101,"合伙人","MerchantFlowType",101,"合伙人信息认证"),
    CREATE_MERCHANT(102,"供应商","MerchantFlowType",102,"供应商信息认证"),
            ;

    DataObjectEnum(int value,String desc,String triggerEnumName,int triggerValue,String triggerDesc){
        this.value = value;
        this.desc = desc;
        this.triggerEnumName = triggerEnumName;
        this.triggerValue = triggerValue;
        this.triggerDesc = triggerDesc;
    }

    public static DataObjectEnum getEnum(int value,int triggerValue) {
        return Arrays.stream(values()).filter(p -> (p.value == value) && (p.triggerValue == triggerValue)).findFirst().orElse(null);
    }

    private  int value;
    private  String desc;
    private String triggerEnumName;
    private int triggerValue;
    private String triggerDesc;

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }

    public String getTriggerEnumName() {
        return triggerEnumName;
    }

    public int getTriggerValue() {
        return triggerValue;
    }

    public String getTriggerDesc() {
        return triggerDesc;
    }
}
