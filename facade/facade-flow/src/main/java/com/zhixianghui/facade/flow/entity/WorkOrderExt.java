package com.zhixianghui.facade.flow.entity;

import java.util.Date;
import java.io.Serializable;

import com.zhixianghui.facade.flow.vo.work.BaseExtObj;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-04-01
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class WorkOrderExt extends BaseExtObj implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer version;

    /**
     * 工单编号
     */
    private String workOrderNo;

    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 商户名称
     */
    private String employerName;

    /**
     * 销售id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 开始调单时间
     */
    private String startWorkTime;

    /**
     * 调单结束时间
     */
    private String endWorkTime;

    /**
     * 调单范围
     */
    private String workRangeUrl;

    /**
     * 发起调单日期
     */
    private Date createDate;

    /**
     * 截止时间
     */
    private Date endDate;


}
