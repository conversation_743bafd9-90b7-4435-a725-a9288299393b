package com.zhixianghui.facade.flow.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.flow.dto.CommonFlowLogVo;
import com.zhixianghui.facade.flow.dto.CommonFlowVo;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.entity.CommonFlowLog;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.vo.req.CommonFlowEditVo;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.res.TaskResVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.flow.vo.req.TaskHandleVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowFacede
 * @Description 流程主体服务
 * @Date 2021/4/25 9:03
 */
public interface FlowFacade {

    /**
     * 查询用户收到的审批
     * @param currentUserId  当前用户id
     * @param platform  当前平台
     * @param isAdmin  是否管理员
     * @param paramMap  过滤条件
     * @param pageParam  分页
     * @return
     */
    PageResult<List<Map<String,Object>>> handleList(Long currentUserId,Integer platform,Boolean isAdmin,Map<String,Object> paramMap,PageParam pageParam);

    /**
     * 查询用户提交的申请
     * @param currentUserId 当前用户id
     * @param platform  平台来源
     * @param isAdmin 是否管理员
     * @param paramMap  过滤条件
     * @param pageParam  分页
     * @return
     */
    PageResult<List<Map<String,Object>>> sendList(Long currentUserId,Integer platform,Boolean isAdmin,Map<String,Object> paramMap,PageParam pageParam);

    /**
     * 查询待审批列表
     * @param flowUserVo 当前用户
     * @param isAdmin  是否管理员
     * @param parmaMap  过滤条件
     * @param pageParam  分页
     * @return
     */
    PageResult<List<Map<String,Object>>> todoList(FlowUserVo flowUserVo,Boolean isAdmin,Map<String,Object> parmaMap, PageParam pageParam);

    /**
     * 查询抄送列表
     * @param currentUserId
     * @param platform
     * @param paramMap
     * @param pageParam
     * @return
     */
    PageResult<List<Map<String, Object>>> carbonList(Long currentUserId, Integer platform, Map<String, Object> paramMap, PageParam pageParam);

    /**
     * 根据流程定义key启动流程
     * @param processVo 流程基础数据
     * @param createFlowUser 流程发起人
     * @param userMap 下一个环节参与者信息，key = 表达式名，value = List -> 平台id:参与者id:参与者姓名
     * @param flowParam  流程变量
     */
    CommonFlow startProcessByProcessDefinitionKey(ProcessVo processVo,FlowUserVo createFlowUser, Map<String,List<FlowUserVo>> userMap,Map<String,Object> flowParam) throws BizException;

    /**
     * 修改业务数据
     * @param id 通用流程表id
     * @param extInfo  业务json数据
     * @param flowUserVo  操作人
     */
    void editBusinessVariable(CommonFlowEditVo commonFlowEditVo,FlowUserVo flowUserVo,Boolean isAdmin) throws BizException;

    /**
     * 完成任务
     * @param taskHandleVo  流程基础属性
     * @param currentUser  处理人
     */
    void executeTask(TaskHandleVo taskHandleVo,FlowUserVo currentUser,Boolean isAdmin) throws BizException;

    /**
     * 更换审批人
     * @param taskHandleVo 流程基础属性
     * @param nextUser 交给谁去处理
     */
    void transferTask(CommonFlowEditVo commonFlowEditVo, FlowUserVo currentUser, FlowUserVo nextUser) throws BizException;

    /**
     * 撤回流程实例
     * @param commonFlowId 流程通用记录
     * @param currentUser 当前用户
     */
    void deleteProcessInstance(Long commonFlowId,FlowUserVo currentUser,Boolean isAdmin,String reason) throws BizException;

    /**
     * 获取操作日志
     * @param  commonflowid
     * @return
     */
    List<CommonFlowLogVo> getDetailByCommonFlowId(Long commonFlowId, Integer platform);

    /**
     * 获取流程详情
     * @param commonFlowId
     * @return
     */
    CommonFlowVo getCommonFlowById(Long commonFlowId, String taskId, FlowUserVo flowUserVo,boolean isAdmin,Integer platform);

    CommonFlow getByRootProcessInstanceId(String processInstanceId);

    List<CommonFlow> getCommonFlowByParam(Map<String,Object> paramMap);

    /**
     * 判断是否存在尚未完成的流程
     * @param businessKey
     * @param flowTypeEnum
     * @return
     */
    boolean isExistNotFinishedFlow(FlowTypeEnum flowTypeEnum,String ... businessKey);

    boolean isExistNotFinishedUpdateQuoteFlow(FlowTypeEnum flowTypeEnum,String  businessKey);

    /**
     * 回复流程
     * @param commonFlowEditVo
     */
    void reply(CommonFlowEditVo commonFlowEditVo,FlowUserVo flowUserVo);

    /**
     * 流程信息
     * @param commonFlowId
     * @return
     */
    CommonFlow getId(Long commonFlowId);

    boolean isExistTask(FlowUserVo flowUserVo);

    PageResult<List<Map<String,Object>>> workOrderPage(Integer platform,Map<String, Object> paramMap, PageParam toPageParam);

    List<CommonFlow> listBy(Map<String, Object> paramMap);

    void update(CommonFlow commonFlow);

    void deleteByCommonFlowId(Long commonFlowId);
}
