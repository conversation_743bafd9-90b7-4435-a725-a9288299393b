package com.zhixianghui.facade.flow.vo.work;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.flow.enums.ComponentTypeEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName WorkForm
 * @Description TODO
 * @Date 2022/3/31 14:30
 */
@Data
public class WorkForm {

    private String title;

    private String desc;

    private List<BaseComponent> component = new ArrayList<>();

    public WorkForm(){}

    public WorkForm(String title,String desc,BaseComponent ... components){
        this.title = title;
        this.desc = desc;
        for (BaseComponent com : components) {
            this.component.add(com);
        }
    }

    public static void main(String[] args) {
        String json = "{\"desc\": \"表单描述文本\", \"title\": \"表单标题文本\", \"component\": [{\"type\": \"el-upload\", \"field\": \"field1\", \"title\": \"上传文件\", \"value\": []}, {\"type\": \"el-input\", \"field\": \"field2\", \"title\": \"单行文本\", \"value\": \"\"}, {\"type\": \"el-textarea\", \"field\": \"field3\", \"title\": \"多行文本\", \"value\": \"\"}, {\"type\": \"el-checkbox-group\", \"field\": \"field6\", \"title\": \"多选框\", \"value\": [], \"options\": [{\"key\": \"1652254802087-0\", \"label\": \"选项1\", \"value\": \"1\"}, {\"key\": \"1652254802087-1\", \"label\": \"选项2\", \"value\": \"2\"}, {\"key\": \"1652254802087-2\", \"label\": \"选项3\", \"value\": \"3\"}]}, {\"type\": \"el-select\", \"field\": \"field7\", \"title\": \"下拉选择框\", \"value\": \"\", \"options\": [{\"key\": \"1652254802857-0\", \"label\": \"选项1\", \"value\": \"1\"}, {\"key\": \"1652254802857-1\", \"label\": \"选项2\", \"value\": \"2\"}, {\"key\": \"1652254802857-2\", \"label\": \"选项3\", \"value\": \"3\"}]}, {\"tips\": \"E签宝正在调用贵司数字证书，点击提交后即可自动完成签约\", \"type\": \"sign-upload\", \"urls\": [], \"field\": [\"field9\", \"field10\"], \"title\": \"签约方式\", \"value\": \"1\", \"elemType\": \"custom\", \"fieldNum\": 2, \"uploadText\": \"提交确认单\", \"templateFileUrl\": \"\", \"templateFileName\": \"模板文件名\"}, {\"type\": \"el-table\", \"field\": \"field8\", \"title\": \"表格\", \"value\": [{\"1\": \"\", \"2\": \"\", \"3\": \"\", \"rowTitle\": \"行0\"}, {\"1\": \"\", \"2\": \"\", \"3\": \"\", \"rowTitle\": \"行1\"}, {\"1\": \"\", \"2\": \"\", \"3\": \"\", \"rowTitle\": \"行2\"}], \"header\": [{\"prop\": \"rowTitle\", \"title\": \"列0\"}, {\"prop\": \"1\", \"title\": \"列1\"}, {\"prop\": \"2\", \"title\": \"列2\"}, {\"prop\": \"3\", \"title\": \"列3\"}]}, {\"type\": \"el-radio-group\", \"field\": \"field5\", \"title\": \"单选框\", \"value\": \"\", \"options\": [{\"key\": \"1652254801378-0\", \"label\": \"选项1\", \"value\": \"1\"}, {\"key\": \"1652254801378-1\", \"label\": \"选项2\", \"value\": \"2\"}, {\"key\": \"1652254801378-2\", \"label\": \"选项3\", \"value\": \"3\"}]}, {\"type\": \"text\", \"field\": \"field4\", \"title\": \"只读文本\", \"value\": \"只读文本内容\"}]}";
        JSONObject jsonObject = JSON.parseObject(json);
        List<BaseComponent> baseComponentList = new ArrayList<>();
        jsonObject.getJSONArray("component").stream().forEach(x->{
            JSONObject component = (JSONObject) x;
            if (component.getString("type").equals(ComponentTypeEnum.SIGN_COMPONENT.getType())){
                component.put("signFileUrl","哈哈哈哈");
            }
        });
        System.out.println(jsonObject.toJSONString());
    }

    /**
     * 没有对应组件文件时会丢失属性
     * @param json
     */
    public void transferBean(String json){
        JSONObject jsonObject = JSON.parseObject(json);
        jsonObject.getJSONArray("component").stream().forEach(x->{
            JSONObject componentJson = (JSONObject) x;
            String type = componentJson.getString("type");
            ComponentTypeEnum componentTypeEnum = ComponentTypeEnum.getEnum(type);
            BaseComponent baseComponent;
            if (componentTypeEnum != null){
                baseComponent = (BaseComponent) JSON.toJavaObject(componentJson,componentTypeEnum.getClazz());
            }else{
                baseComponent = JSON.toJavaObject(componentJson,BaseComponent.class);
            }
            this.component.add(baseComponent);
        });
        this.title = jsonObject.getString("title");
        this.desc = jsonObject.getString("desc");
    }
}
