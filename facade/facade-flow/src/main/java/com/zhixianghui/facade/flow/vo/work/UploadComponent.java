package com.zhixianghui.facade.flow.vo.work;

import com.zhixianghui.facade.flow.enums.ComponentTypeEnum;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName UploadComponent
 * @Description TODO
 * @Date 2022/3/31 14:19
 */
@Data
public class UploadComponent extends BaseComponent {

    private String field;

    private String templateFileUrl;

    private List<String> value = new ArrayList<>();

    public UploadComponent(String title, String descText, String templateFileUrl, String field,String ... uploadFileUrl){
        this.title = title;
        this.descText = descText;
        this.templateFileUrl = templateFileUrl;
        this.type = ComponentTypeEnum.UPLOAD_COMPONENT.getType();
        this.field = field;
        if (uploadFileUrl != null){
            this.value.addAll(Arrays.asList(uploadFileUrl));
        }
    }
}
