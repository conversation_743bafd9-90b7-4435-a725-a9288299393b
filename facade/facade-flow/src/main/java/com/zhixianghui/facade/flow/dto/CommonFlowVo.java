package com.zhixianghui.facade.flow.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName CommonFlowVo
 * @Description TODO
 * @Date 2021/5/18 11:40
 */
@Data
public class CommonFlowVo implements Serializable {
    private Long id;

    /**
     * 版本
     */
    private Integer version = 0;

    /**
     * 发起人
     */
    private Long initiatorId;

    /**
     * 发起人名称
     */
    private String initiatorName;

    /**
     * 流程主题(创建商户等)
     */
    private String flowTopicType;

    /**
     * 流程主题名称
     */
    private String flowTopicName;

    /**
     * 流程开始时间
     */
    private Date createTime;

    /**
     * 流程更新时间
     */
    private Date updateTime;

    /**
     * 流程结束时间
     */
    private Date endTime;

    /**
     * 流程状态
     */
    private Integer status;

    /**
     * 额外信息(备注,审批信息等)
     */
    private String extInfo;

    /**
     * 当前处理人id
     */
    private String currentUserId;

    /**
     * 当前处理人
     */
    private String currentUserName;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 平台来源
     */
    private Integer platform;

    /**
     * 业务编码
     */
    private String businessKey;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否为对应处理用户
     */
    private Boolean isHandleUser;

    /**
     * 是否可以修改业务参数
     */
    private Boolean isEdit;

    /**
     * 是否为创建用户
     */
    private Boolean isInitUser;

    /**
     * 是否可以变更审批人
     */
    private Boolean isChangHandler;

    /**
     * 是否可以驳回
     */
    private Boolean isBack;

    /**
     * 引用编号
     */
    private String referenceNo;

    /**
     * 引用名称
     */
    private String referenceName;
}
