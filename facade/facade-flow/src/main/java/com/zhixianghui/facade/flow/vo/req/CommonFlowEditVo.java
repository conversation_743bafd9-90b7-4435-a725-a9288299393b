package com.zhixianghui.facade.flow.vo.req;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName CommonFlowEditVo
 * @Description TODO
 * @Date 2021/5/19 16:51
 */
@Data
public class CommonFlowEditVo implements Serializable {
    private static final long serialVersionUID = -5786779808452533537L;

    /**
     * 流程表id
     */
    private Long commonFlowId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 业务参数
     */
    @NotEmpty(groups = IEditBusinessVariable.class,message = "业务参数不能为空")
    private String extInfo;

    /**
     * 下一步处理人id
     */
    @NotNull(groups = ITransferUser.class,message = "请选择变更人")
    private Long nextUserId;

    /**
     * 审批备注
     */
    @Length(max = 150, message = "审批意见/备注不允许超过150字")
    private String remark;

    /**
     * 回复意见
     */
    @Length(groups = IReply.class,max = 2000,message = "回复不允许超过2000个字符")
    private String content;
}
