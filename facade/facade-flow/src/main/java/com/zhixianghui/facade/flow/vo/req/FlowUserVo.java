package com.zhixianghui.facade.flow.vo.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName FlowUserVo
 * @Description 参与者vo
 * @Date 2021/4/27 14:58
 */
@Data
public class FlowUserVo implements Serializable {

    private static final long serialVersionUID = -7177047891185937329L;
    /**
     * 平台来源
     */
    private Integer platform;

    /**
     * 参与者id
     */
    private Long userId;

    /**
     * 参与者姓名
     */
    private String userName;

    /**
     * 商户号、合伙人号等
     */
    private String no;

    /**
     * 非智享系统用户
     */
    private String externalId;

    public static String getPlatformStr(Integer platform){
        return Integer.toString(platform);
    }
}
