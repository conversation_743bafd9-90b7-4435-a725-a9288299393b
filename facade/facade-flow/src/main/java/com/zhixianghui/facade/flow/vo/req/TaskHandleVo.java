package com.zhixianghui.facade.flow.vo.req;

import com.zhixianghui.common.util.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName TaskHandleVo
 * @Description 任务流转Vo
 * @Date 2021/4/25 17:37
 */
@Data
public class TaskHandleVo implements Serializable {

    private static final long serialVersionUID = 1130110470892962898L;
    /**
     * 流程通用表id
     */
    private Long commonFlowId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 审核意见
     */
    private String opinion;

    /**
     * 处理状态
     */
    private Integer status;

    /**
     * 下一步参与者
     */
    private Map<String, List<FlowUserVo>> participant;

    /**
     * 流程条件
     */
    private Map<String,Object> condition;
}
