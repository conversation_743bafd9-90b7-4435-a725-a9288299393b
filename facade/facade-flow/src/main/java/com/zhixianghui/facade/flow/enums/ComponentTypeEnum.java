package com.zhixianghui.facade.flow.enums;

import com.zhixianghui.facade.flow.vo.work.*;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName ComponentTypeEnum
 * @Description TODO
 * @Date 2022/3/31 14:21
 */
@AllArgsConstructor
public enum ComponentTypeEnum {

    UPLOAD_COMPONENT("el-upload","上传组件", UploadComponent.class),

    SIGN_COMPONENT("sign-upload","签约组件", SignComponent.class),

    TEXT_COMPONENT("text","只读文本", TextComponent.class),

    RADIO_COMPONENT("el-radio-group","单选组件", RadioComponent.class),

    TEXTAREA_COMPONENT("el-textarea","多行文本", TextAreaComponent.class),

    INPUT_COMPONENT("el-input","单行文本",InputComponent.class);



    private String type;

    private String desc;

    private Class clazz;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public Class getClazz() {
        return clazz;
    }

    public static ComponentTypeEnum getEnum(String type) {
        return Arrays.stream(values()).filter(p -> p.type.equals(type)).findFirst().orElse(null);
    }
}
