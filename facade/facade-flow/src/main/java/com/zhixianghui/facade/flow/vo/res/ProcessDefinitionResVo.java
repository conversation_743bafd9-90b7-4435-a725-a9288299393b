package com.zhixianghui.facade.flow.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName ProcessDefinitionResVo
 * @Description TODO
 * @Date 2021/4/23 15:43
 */
@Data
public class ProcessDefinitionResVo implements Serializable {

    private static final long serialVersionUID = -5008935372643507384L;
    /**
     * 流程定义id
     */
    private String id;

    /**
     * 流程定义key
     */
    private String key;

    /**
     * 流程类型
     */
    private String category;

    /**
     * 流程定义名称
     */
    private String name;

    /**
     * 流程资源名称
     */
    private String resourceName;

    /**
     * 流程版本号
     */
    private int version;

    /**
     * 是否被挂起
     */
    private Integer suspensionState;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 描述
     */
    private String description;

    /**
     * 关联对象
     */
    private String dataObj;

    /**
     * 触发动作
     */
    private String triggerAct;

    /**
     * 所属平台
     */
    private String platform;

    /**
     *创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;
}
