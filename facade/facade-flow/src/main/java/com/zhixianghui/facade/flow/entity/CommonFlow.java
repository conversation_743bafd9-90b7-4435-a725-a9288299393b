package com.zhixianghui.facade.flow.entity;

import java.util.Date;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.zhixianghui.common.util.utils.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CommonFlow implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 版本
     */
    private Integer version = 0;

    /**
     * 发起人
     */
    private Long initiatorId;

    /**
     * 发起人名称
     */
    private String initiatorName;


    private Integer workType;

    /**
     * 流程主题(创建商户等)
     */
    private String flowTopicType;

    /**
     * 流程主题名称
     */
    private String flowTopicName;

    /**
     * 流程开始时间
     */
    private Date createTime;

    /**
     * 流程更新时间
     */
    private Date updateTime;

    /**
     * 流程结束时间
     */
    private Date endTime;

    /**
     * 流程状态
     */
    private Integer status;

    /**
     * 额外信息(备注,审批信息等)
     */
    private String extInfo;

    /**
     * 当前处理人id
     */
    private String currentUserId;

    /**
     * 当前处理人
     */
    private String currentUserName;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 平台来源
     */
    private Integer platform;

    /**
     * 业务编码
     */
    private String businessKey;

    /**
     * 备注
     */
    private String remark;

    /**
     * 当前节点抿成
     */
    private String taskName;

    /**
     * 步骤
     */
    private Integer step;

    public Map<String,Object> buildReturn(){
        Map<String,Object> maps = new HashMap<>();
        maps.put("commonFlowId", this.getId());
        maps.put("createTime", DateUtil.formatDateTime(this.getCreateTime()));
        maps.put("submitName", this.getInitiatorName());
        return maps;
    }
}
