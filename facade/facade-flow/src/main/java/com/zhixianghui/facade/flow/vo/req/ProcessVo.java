package com.zhixianghui.facade.flow.vo.req;

import com.zhixianghui.facade.flow.enums.WorkTypeEnum;
import com.zhixianghui.facade.flow.vo.work.BaseExtObj;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ProcessVo
 * @Description TODO
 * @Date 2021/4/25 11:32
 */
@Data
public class ProcessVo implements Serializable {

    private static final long serialVersionUID = -2855960209455347192L;
    /**
     * 流程定义key
     */
    private String processDefinitionKey;

    /**
     * 流程类型
     */
    private String flowTopicType;

    /**
     * 流程主题
     */
    private String flowTopicName;

    /**
     * 业务参数
     */
    private String extInfo;

    /**
     * 用作外部业务关联
     */
    private String businessKey;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工作流类型,默认审批流
     */
    private Integer workType = WorkTypeEnum.AUDIT_FLOW.getValue();

    /**
     * 扩展字段
     */
    private BaseExtObj extObj;

    /**
     * 抄送人
     */
    private List<FlowUserVo> carbonCopyList = new ArrayList<>();


}
