package com.zhixianghui.facade.flow.vo.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName WorkOrderVo
 * @Description TODO
 * @Date 2022/4/19 9:36
 */
@Data
public class WorkOrderVo implements Serializable {

    @NotBlank(message = "用工企业不能为空")
    private String mchNo;

    private String mainstayNo;

    private String workRangeUrl;

    @NotNull(message = "调单期间不能为空")
    private String startWorkTime;

    @NotNull(message = "调单期间")
    private String endWorkTime;

    @NotNull(message = "截止日期不能为空")
    private Date endDate;

    @NotBlank(message = "表单不能为空")
    private String formJson;
}
