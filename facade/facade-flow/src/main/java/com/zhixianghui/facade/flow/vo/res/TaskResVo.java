package com.zhixianghui.facade.flow.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName TaskVo
 * @Description TODO
 * @Date 2021/4/27 9:44
 */
@Data
public class TaskResVo implements Serializable {

    private static final long serialVersionUID = -5862968320818919917L;

    /**
     * 流程表id
     */
    private Long commonFlowId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 发起人id
     */
    private Long initiatorId;

    /**
     * 发起人名称
     */
    private String initiatorName;

    /**
     * 流程类型
     */
    private String flowTopicType;

    /**
     * 流程主题
     */
    private String flowTopicName;

    /**
     * 发起时间
     */
    private Date createTime;

    /**
     * 流程状态
     */
    private Integer status;

    /**
     * 来源平台
     */
    private Integer platform;
}
