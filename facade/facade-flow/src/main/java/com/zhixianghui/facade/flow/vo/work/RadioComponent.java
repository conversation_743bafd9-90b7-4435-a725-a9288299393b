package com.zhixianghui.facade.flow.vo.work;

import com.zhixianghui.facade.flow.enums.ComponentTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName RadioComponent
 * @Description TODO
 * @Date 2022/4/27 18:23
 */
@Data
public class RadioComponent extends BaseComponent{

    private String value;

    private List<RadioOptions> options;

    public RadioComponent(){
        this.type = ComponentTypeEnum.RADIO_COMPONENT.getType();
    }

    @Data
    public static class RadioOptions implements Serializable {
        private String key;

        private String label;

        private String value;
    }
}
