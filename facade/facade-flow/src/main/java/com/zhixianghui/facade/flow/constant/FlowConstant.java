package com.zhixianghui.facade.flow.constant;

/**
 * <AUTHOR>
 * @ClassName FlowConstant
 * @Description 流程图属性常量
 * @Date 2021/5/25 9:12
 */
public class FlowConstant {


    /**
     * 业务数据
     */
    public static final String EXT_OBJ = "extObj";

    /**
     * 是否为超管发起流程
     * 是：100，否：101
     */
    public static final String IS_ADMIN = "isAdmin";

    /**
     * 流程控制变量，是否同意，同意：100，驳回：101。
     * 需要在流程图中控制流程走向的地方进行配置
     */
    public static final String FLOW_STATUS = "flowStatus";

    /**
     * 流程表单属性id，是否可以编辑，默认不可编辑，可以编辑的节点需要在流程图进行配置
     */
    public final static String EDIT_FORM_ID = "editBusinessVariable";

    /**
     * 流程表单属性id，是否可以驳回，默认不可驳回，可以驳回的节点需要在流程图进行配置
     */
    public final static String BACK_FORM_ID = "canBackVariable";

    /**
     * 流程发起人，需要在流程开始节点发起人属性配置为此值
     */
    public final static String APPLY_USER_ID = "applyUserId";

    /**
     * 设置流程启用自动跳过节点功能，需要配置自动跳过表达式
     */
    public final static String SKIP_EXPRESSION_ID = "_FLOWABLE_SKIP_EXPRESSION_ENABLED";

    /**
     * 流程图缓存key
     */
    public static final String IMAGE_KEY = "FLOW:IMAGE:KEY:";

    /**
     * map默认topic
     */
    public static final String DEFAULT_TOPIC = "DEFALUT_TOPIC";

    /**
     * map默认topic
     */
    public static final String DEFAULT_TAG = "DEFAULT_TAG";

    /**
     * 流程图缓存key过期时间
     */
    public static int IMAGE_EXPIRE = 3*24*60*60;

    public static final String CHANGE_USER = "变更处理人";

    public static final String EDIT_INFO = "编辑信息";

    public static final String REFERENCE_NO = "referenceNo";

    public static final String REFERENCE_NAME = "referenceName";

    /**
     * 外部通道审批
     */
    public static final String EXTERNAL_ID = "external";

    public static final String EXTERNAL_NAME = "通道审批";


}
