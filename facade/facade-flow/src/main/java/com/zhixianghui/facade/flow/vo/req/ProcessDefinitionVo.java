package com.zhixianghui.facade.flow.vo.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName ProcessDefinitionVo
 * @Description TODO
 * @Date 2021/4/23 15:43
 */
@Data
public class ProcessDefinitionVo implements Serializable {

    private static final long serialVersionUID = -4109892853103023224L;
    /**
     * 流程定义id
     */
    private String id;

    /**
     * 流程定义key
     */
    private String key;

    /**
     * 流程类型
     */
    private String category;

    /**
     * 流程定义名称
     */
    private String name;

    /**
     * 流程资源名称
     */
    private String resourceName;

    /**
     * 流程版本号
     */
    private int version;

    /**
     * 是否被挂起
     */
    private int suspensionState;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 描述
     */
    private String description;
}
