package com.zhixianghui.facade.flow.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批节点详情表
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CommonFlowLog implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 审批流程ID
     */
    private Long commonFlowId;

    /**
     * 版本
     */
    private Integer version = 0;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 处理人ID
     */
    private Long handlerId;

    /**
     * 处理人名称
     */
    private String handlerName;

    /**
     * 节点名称
     */
    private String taskName;

    /**
     * 节点状态
     */
    private Integer status;

    /**
     * 额外信息
     */
    private String extInfo;

    /**
     * 审批意见
     */
    private String approvalOpinion;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 平台来源
     */
    private Integer platform;

    /**
     * 花费时间
     */
    private Long spentTime;

    /**
     * 步骤
     */
    private Integer step;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 属于那个商户的流程
     */
    private String belong;
}
