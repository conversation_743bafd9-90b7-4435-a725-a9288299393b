package com.zhixianghui.facade.flow.dto;

import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowStartDto
 * @Description TODO
 * @Date 2021/5/8 11:08
 */
@Data
public class FlowStartDto<T> implements Serializable {

    private static final long serialVersionUID = -7232863048788912368L;

    /**
     * 参与者
     */
    private Map<String, List<FlowUserVo>> participant;

    /**
     * 流程控制参数
     */
    private Map<String,Object> condition;

    /**
     * 业务数据接收类
     */
    @Valid
    private T extObj;

    /**
     * 审批流备注
     */
    @Length(max = 150, message = "审批意见/备注不允许超过150字")
    private String remark;

    private String businessKey;

    private String flowTopicName;
}
