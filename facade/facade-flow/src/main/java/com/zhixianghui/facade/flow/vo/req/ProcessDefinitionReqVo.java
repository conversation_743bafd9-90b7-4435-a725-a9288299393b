package com.zhixianghui.facade.flow.vo.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName ProcessDefinitionReqVo
 * @Description TODO
 * @Date 2021/4/29 17:09
 */
@Data
public class ProcessDefinitionReqVo implements Serializable {


    private static final long serialVersionUID = -5228651774865720799L;
    /**
     * 流程类型
     */
    private String category;

    /**
     * 流程定义key
     */
    private String key;

    /**
     * 流程定义名称
     */
    private String name;

    /**
     * 多租户id
     */
    private String tenantId;
}
