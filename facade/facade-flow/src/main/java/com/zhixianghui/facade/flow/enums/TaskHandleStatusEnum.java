package com.zhixianghui.facade.flow.enums;

/**
 * <AUTHOR>
 * @ClassName TaskHandleStatusEnum
 * @Description TODO
 * @Date 2021/4/29 15:33
 */
public enum  TaskHandleStatusEnum {

    /**
     *同意
     */
    AGREE(100, "同意"),
    /**
     *不同意
     */
    DISAGREE(101, "不同意"),
    /**
     *异常
     */
    EXCEPTION(102, "异常"),
    /**
     *待审批
     */
    PENDING(103, "待审批"),
    ;

    TaskHandleStatusEnum(int value,String desc){
        this.value = value;
        this.desc = desc;
    }

    private int value;
    private String desc;

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
