package com.zhixianghui.facade.flow.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2021-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ActReProcdef implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private Integer rev;

    private String category;

    private String name;

    private String key;

    private Integer version;

    private String deploymentId;

    private String resourceName;

    private String dgrmResourceName;

    private String description;

    private Integer hasStartFormKey;

    private Integer hasGraphicalNotation;

    private Integer suspensionState;

    private String tenantId;

    private String engineVersion;

    private String derivedFrom;

    private String derivedFromRoot;

    private Integer derivedVersion;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;


}
