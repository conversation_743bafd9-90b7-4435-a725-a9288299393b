package com.zhixianghui.facade.flow.service;

import com.zhixianghui.common.statics.exception.BizException;

import java.io.IOException;

/**
 * <AUTHOR>
 * @ClassName FlowImageFacade
 * @Description TODO
 * @Date 2021/4/23 14:16
 */
public interface FlowImageFacade {

    /**
     * 根据流程定义获取流程图
     * @param processDefinitionId
     * @return
     * @throws BizException
     * @throws IOException
     */
    byte[] getDefinitionImage(String processDefinitionId) throws BizException, IOException;

    /**
     * 根据当前流程实例获取流程追踪图
     * @param processInstanceId
     * @return
     * @throws BizException
     * @throws IOException
     */
    String getInstanceImage(String processInstanceId) throws BizException,IOException;
}
