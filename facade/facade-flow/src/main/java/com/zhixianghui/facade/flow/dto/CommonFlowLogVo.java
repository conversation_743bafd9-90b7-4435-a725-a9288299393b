package com.zhixianghui.facade.flow.dto;

import com.zhixianghui.facade.flow.entity.CommonFlowLog;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName CommonFlowLogVo
 * @Description TODO
 * @Date 2021/6/16 16:03
 */
@Data
public class CommonFlowLogVo implements Serializable {

    /**
     * 步骤编号
     */
    private Integer step;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 每个步骤（节点）下对应的日志
     */
    private List<CommonFlowLog> commonFlowLogList;
}
