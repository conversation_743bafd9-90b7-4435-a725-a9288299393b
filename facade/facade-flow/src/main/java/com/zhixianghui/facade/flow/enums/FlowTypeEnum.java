package com.zhixianghui.facade.flow.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName
 * @Description TODO
 * @Date 2021/5/7 15:44
 */
public enum  FlowTypeEnum {

    OTHER(0,0,"其它", 0,"", "其它"),

    /**
     * 运营平台发起-用工企业对象
     */
    PMS_MCH_APPLY(1000,100,"DataObjectEnum",100,"MerchantFlowType","商户入驻"),

    @Deprecated
    PMS_EDIT_MERCHANT(1000,100,"DataObjectEnum",101,"MerchantFlowType", "商户信息编辑"),
    @Deprecated
    PMS_EMPLOY_MAINSTAY_RELATION_APPLY(1000,100,"DataObjectEnum",102,"MerchantFlowType","新建代征关系"),

    PMS_MCH_MAIN_AUTH(1000,100,"DataObjectEnum",104,"MerchantFlowType","主体认证"),
    PMS_CHANGE_SALER(1000,100,"DataObjectEnum",105,"MerchantFlowType","用工企业变更销售/合伙人"),
    PMS_MERCHANT_FREEZE(1000,100,"DataObjectEnum",106,"MerchantFlowType","变更状态"),
    PMS_CHANGE_ACCOUNT(1000,100,"DataObjectEnum",107,"MerchantFlowType","变更账户信息"),
    PMS_MCH_CREATE_PRODUCT(1000,100, "DataObjectEnum", 109, "MerchantFlowType","新建/编辑报价单"),
    //PMS_NEW_QUPTE(1000,100,"DataObjectEnum",109,"MerchantFlowType","变更报价单"),
    PMS_MCH_DELETE_PRODUCT(1000,100,"DataObjectEnum",110,"MerchantFlowType","删除报价单"),
    PMS_MCH_CKH_PRODUCT(1000,100,"DataObjectEnum",112,"MerchantFlowType","新建/编辑报价单"),
    PMS_MCH_DELETE_CKH_PRODUCT(1000,100,"DataObjectEnum",113,"MerchantFlowType","删除报价单"),

    /**
     * 运营平台发起-合伙人对象
     */
    PMS_AGENT_CREATE(1000,101, "DataObjectEnum", 100, "AgentFlowType", "新增合伙人"),
    PMS_AGENT_MODIFY(1000,101, "DataObjectEnum", 101, "AgentFlowType", "合伙人信息变更"),
    AGENT_MAINiNFO_EDIT(1000,101,"DataObjectEnum",102,"AgentMainInfoEditType","合伙人主体认证"),
    PMS_AGENT_QUOTE_EDIT(1000,101, "DataObjectEnum", 103, "AgentFlowType", "合伙人产品报价单"),
    AGENT_BANKACCT_EDIT(1000,101,"DataObjectEnum",104,"agentBankAcctEditType","合伙人银行卡信息修改"),
    AGENT_SET_SELLER(1000,101,"DataObjectEnum",105,"agentSellerSet","合伙人销售变更"),
    AGENT_SET_INVITER(1000,101,"DataObjectEnum",106,"agentInviter","合伙人邀请人设置"),
    AGENT_SET_PRINCIPAL(1000,101,"DataObjectEnum",107,"agentPrincipalSet","合伙人负责人设置"),
    PMS_AGENT_QUOTE_DEL(1000,101, "DataObjectEnum", 108, "AgentFlowType", "删除合伙人产品报价单"),
    AGENT_BATCH_SET_INVITER_SELLER(1000,101,"DataObjectEnum",109,"agentBatchSellerInviterSet","合伙人销售和邀请人批量变更"),

    /**
     * 运营后台发起-供应商对象
     */
    PMS_MAINSTAY_MAIN_AUTH(1000,102,"DataObjectEnum",100,"MainstayFlowType","主体认证"),
    PMS_MAINSTAY_CHANGE_ACCOUNT(1000,102,"DataObjectEnum",101,"MainstayFlowType","变更账户信息"),
    PMS_MAINSTAY_FREEZE(1000,102,"DataObjectEnum",103,"MainstayFlowType","变更状态"),
    PMS_MAINSTAY_CHANGE_SALER(1000,102 ,"DataObjectEnum" , 104,"MainstayFlowType" , "代征主体变更销售"),

    /**
     * 商户后台-用工企业对象
     */
    MERCHANT_MCH_MAIN_AUTH(1001,100,"DataObjectEnum",104,"MerchantFlowType","主体认证"),

    /**
     * 供应商后台-合伙人对象
     */
    MAINSTAY_MCH_MAIN_AUTH(1002,102,"DataObjectEnum",100,"MainstayFlowType","主体认证"),


    /**
     * 合伙人后台-用工企业对象
     */
    AGENT_MCH_APPLY(1003,100,"DataObjectEnum",100,"MerchantFlowType","商户入驻"),

    /**
     * 通用平台-供应商对象
     */
    COMMON_MAINSTAY_CHANGE_PRINCIPAL(1100,102,"DataObjectEnum",102,"MainstayFlowType","变更负责人"),
    /**
     * 通用平台-商户对象
     */
    COMMON_CHANGE_PRINCIPAL(1100,100,"DataObjectEnum",108,"MerchantFlowType","变更负责人"),
    COMMON_WORK_ORDER(1100,100,"DataObjectEnum",111,"MerchantFlowType","工单流程")
    ;

    FlowTypeEnum(int platform,int dataObjValue,String dataObjDesc,int triggerValue,String triggerDesc,String desc){
        this.platform = platform;
        this.dataObjValue = dataObjValue;
        this.dataObjDesc = dataObjDesc;
        this.triggerValue = triggerValue;
        this.triggerDesc = triggerDesc;
        this.desc = desc;
    }

    /**
     * 平台
     */
    private int platform;
    /**
     * {@link DataObjectEnum}
     *  数据对象（用工企业、合伙人、供应商）
     */
    private int dataObjValue;
    /**
     * {@link DataObjectEnum}
     *  数据对象枚举类（用工企业、合伙人、供应商）
     */
    private String dataObjDesc;
    /**
     * 触发动作, 新增、编辑、
     */
    private int triggerValue;
    /**
     * 触发动作枚举类
     */
    private String triggerDesc;
    private String desc;

    public String getDesc() {
        return desc;
    }

    public int getPlatform() {
        return platform;
    }

    public int getDataObjValue() {
        return dataObjValue;
    }

    public String getDataObjDesc() {
        return dataObjDesc;
    }

    public int getTriggerValue() {
        return triggerValue;
    }

    public String getTriggerDesc() {
        return triggerDesc;
    }

    public String getFlowTopicType(){
        String type = platform + ":" + dataObjValue + ":" + triggerValue;
        return type;
    }

    public static FlowTypeEnum getEnum(int platform,int dataObjValue,int triggerValue) {
        return Arrays.stream(values()).filter(p -> (p.platform == p.platform) && (p.dataObjValue == dataObjValue) && (p.triggerValue == triggerValue)).findFirst().orElse(null);
    }
}
