package com.zhixianghui.facade.flow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @ClassName TaskHandleTypeEnum
 * @Description TODO
 * @Date 2021/4/28 10:16
 */
public enum TaskHandleTypeEnum {

    /**
     *提交审批
     */
    SUBMIT(1,"提交审批"),
    /**
     *编辑信息
     */
    EDIT(2,"编辑信息"),
    /**
     *撤回
     */
    RECALL(3,"撤回"),

    /**
     * 变更处理人
     */
    TRANSFER(4,"变更处理人"),

    RELPY(5,"回复");

    TaskHandleTypeEnum(int value,String desc){
        this.value = value;
        this.desc = desc;
    }

    private int value;
    private String desc;

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
