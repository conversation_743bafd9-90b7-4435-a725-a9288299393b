package com.zhixianghui.facade.flow.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.flow.vo.req.ProcessDefinitionReqVo;
import com.zhixianghui.facade.flow.vo.res.ProcessDefinitionResVo;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ProcessDefinitionFacade
 * @Description 流程定义基础服务
 * @Date 2021/4/23 11:58
 */
public interface ProcessDefinitionFacade {

    void deploy(String name,String platform,String dataObj,String triggerAct,byte[] bytes,String originalFilename,String tenantId,String currentUserName,String description) throws IOException;

    PageResult<List<ProcessDefinitionResVo>> listProcessDefinition(Map<String,Object> paramMap, PageParam pageParam);

    void updateProcessDefinitionState(String id,Integer state) throws BizException;

    void delete(String id) throws BizException;

    ProcessDefinitionResVo getById(String id);

    void update(String id,String dataObj, String triggerAct, String name, String platform, String desc, String tenantId,String currentUserName);
}
