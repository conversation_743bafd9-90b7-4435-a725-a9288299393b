package com.zhixianghui.facade.flow.enums;

/**
 * <AUTHOR>
 * @ClassName FlowStatusEnum
 * @Description TODO
 * @Date 2021/4/29 15:46
 */
public enum FlowStatusEnum {

    /**
     *已完成
     */
    FINISHED(100, "已完成"),
    /**
     *待审批
     */
    PENDING(101, "待审批"),

    /**
     * 已取消
     */
    CANCELED(102,"已取消"),
    ;

    FlowStatusEnum(int value,String desc){
        this.value = value;
        this.desc = desc;
    }

    private  int value;
    private  String desc;

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }
}
