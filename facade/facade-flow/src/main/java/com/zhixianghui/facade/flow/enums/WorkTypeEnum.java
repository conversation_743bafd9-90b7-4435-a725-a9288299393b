package com.zhixianghui.facade.flow.enums;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @ClassName WorkTypeEnum
 * @Description TODO
 * @Date 2022/3/30 11:10
 */
@AllArgsConstructor
public enum WorkTypeEnum {

    AUDIT_FLOW(100,"审批流"),

    WORK_FLOW(101,"工作流");

    private Integer value;

    private String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
