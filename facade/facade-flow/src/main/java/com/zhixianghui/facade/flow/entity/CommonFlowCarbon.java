package com.zhixianghui.facade.flow.entity;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 *
 * <AUTHOR>
 * @Date 创建时间： 2022-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CommonFlowCarbon implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Integer version;

    /**
     * 流程id
     */
    private Long commonFlowId;

    /**
     * 抄送人id
     */
    private Long userId;

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 创建时间
     */
    private Date createTime;


}
