package com.zhixianghui.facade.timer.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.timer.entity.ScheduleJob;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2017/8/20.
 */
public interface QuartzFacade {

    /**
     * 直接触发任务的消息通知，实例处于挂起状态中，但需要对个别任务触发时使用（实行 蓝绿发布 策略时可能会有用）
     * @param jobGroup
     * @param jobName
     * @return
     */
    boolean sendJobNotify(String jobGroup, String jobName, String operator)  throws BizException;

    /**
     * 添加任务
     * @param scheduleJob
     * @return
     */
    Long add(ScheduleJob scheduleJob, String operator) throws BizException;

    /**
     * 重新安排定时任务，即update任务
     * @param scheduleJob
     * @return
     */
    boolean rescheduleJob(ScheduleJob scheduleJob, String operator) throws BizException;

    /**
     * 删除任务
     * @param jobGroup
     * @param jobName
     * @return
     */
    boolean delete(String jobGroup, String jobName, String operator) throws BizException;

    /**
     * 暂停任务
     * @param jobGroup
     * @param jobName
     * @return
     */
    boolean pauseJob(String jobGroup, String jobName, String operator) throws BizException;

    /**
     * 恢复被暂停的任务
     * @param jobGroup
     * @param jobName
     * @return
     */
    boolean resumeJob(String jobGroup, String jobName, String operator) throws BizException;

    /**
     * 立即触发任务，若实例处于挂起状态，则操作无效
     * @param jobGroup
     * @param jobName
     * @return
     */
    boolean triggerJob(String jobGroup, String jobName, String operator) throws BizException;

    ScheduleJob getJobByName(String jobGroup, String jobName)  throws BizException;

    PageResult<List<ScheduleJob>> listPage(Map<String, Object> paramMap, PageParam pageParam)  throws BizException;
}
