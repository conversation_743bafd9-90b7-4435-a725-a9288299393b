package com.zhixianghui.api.base.params;

import com.zhixianghui.common.statics.annotations.NotSign;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName JobFileVo
 * @Description TODO
 * @Date 2022/10/27 9:45
 */
@Data
public class FileParam implements Serializable {

    private String fileName;

    private String fileBody;

    /**
     * 临时存储，非上传参数
     */
    private byte[] fileBytes;
}
