package com.zhixianghui.api.base.webflux;

import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.reactive.error.DefaultErrorAttributes;
import org.springframework.web.reactive.function.server.ServerRequest;

import java.util.HashMap;
import java.util.Map;

/**
 * 使用webflux时的全局异常处理器，负责决定返回什么响应体
 */
public class GlobalErrorAttributes extends DefaultErrorAttributes {

    public Map<String, Object> getErrorAttributes(ServerRequest request, boolean includeStackTrace) {
        Throwable ex = getError(request);
        ResponseDto<?> responseDto = getResponseDto(ex);
        Map<String,Object> resultMap = new HashMap<>();
        BeanUtil.toMap(responseDto).forEach((s, o) -> {
            resultMap.put(StringUtil.camelToUnderscore(s), o);
        });
        return resultMap;
    }

    private ResponseDto<?> getResponseDto(Throwable ex) {
        if (ex instanceof BizException) {
            BizException e = (BizException) ex;
            if (StringUtil.isEmpty(e.getApiErrorCode())) {
                return ResponseDto.fail(ApiExceptions.API_COMMON_ERROR.getApiErrorCode(), ApiExceptions.API_COMMON_ERROR.getErrMsg());
            } else {
                return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
            }
        } else {
            LoggerFactory.getLogger(this.getClass()).info("***********************unknown*******************");
            return ResponseDto.unknown();
        }
    }
}
