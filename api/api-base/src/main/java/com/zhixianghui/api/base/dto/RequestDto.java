package com.zhixianghui.api.base.dto;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.api.base.params.FileParam;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商户请求参数的VO，主要用作Controller的入参
 *
 * <AUTHOR>
 * @date 2018-12-15
 */
public class RequestDto<T> {
    private String version;

    private String mchNo;
    @Valid
    @NotNull
    private T data;
    private String secKey;
    private String signType;

    /**
     * 身份证正面照
     */
    private String idCardFrontPhoto;

    /**
     * 半身照
     */
    private String cerFacePhoto;

    private List<FileParam> fileList;

    public String getIdCardFrontPhoto() {
        return idCardFrontPhoto;
    }

    public void setIdCardFrontPhoto(String idCardFrontPhoto) {
        this.idCardFrontPhoto = idCardFrontPhoto;
    }

    public String getIdCardBackPhoto() {
        return idCardBackPhoto;
    }

    public void setIdCardBackPhoto(String idCardBackPhoto) {
        this.idCardBackPhoto = idCardBackPhoto;
    }

    /**
     * 身份证背面照
     */
    private String idCardBackPhoto;


    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMchNo() {
        return mchNo;
    }

    public void setMchNo(String mchNo) {
        this.mchNo = mchNo;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getSecKey() {
        return secKey;
    }

    public void setSecKey(String secKey) {
        this.secKey = secKey;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getCerFacePhoto() {
        return cerFacePhoto;
    }

    public void setCerFacePhoto(String cerFacePhoto) {
        this.cerFacePhoto = cerFacePhoto;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public List<FileParam> getFileList() {
        return fileList;
    }

    public void setFileList(List<FileParam> fileList) {
        this.fileList = fileList;
    }
}
