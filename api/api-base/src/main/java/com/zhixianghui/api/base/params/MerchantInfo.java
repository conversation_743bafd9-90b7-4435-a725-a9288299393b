package com.zhixianghui.api.base.params;

import com.zhixianghui.api.base.constants.IPValidKeyConst;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2018-12-15
 */
@Getter
@Setter
public class MerchantInfo implements Serializable {
    private static final long serialVersionUID = 6654122563255411254L;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 商户状态
     */
    private Integer mchStatus;

    /**
     * 签名类型
     * {@link SignTypeEnum}
     */
    private int signType;

    /**
     * 验签的密钥，当signType为MD5时，此属性的值为平台和商户共享的密钥，当signType为RSA时，此属性的值为商户放在平台用以验签的RSA公钥
     */
    private String signValidKey;

    /**
     * 生成签名的密钥，当signType为MD5时，此属性的值为平台和商户共享的密钥，当signType为RSA时，此属性的值为平台的私钥
     */
    private String signGenKey;

    /**
     * 平台接收请求时，对secKey进行解密的私钥(平台的私钥)
     */
    private String secKeyDecryptKey;

    /**
     * 汇聚响应时，对secKey进行加密的公钥(商户的公钥)
     */
    private String secKeyEncryptKey;

    /**
     * 如果需要进行IP校验，则需要设置值，此Map的key参照 {@link IPValidKeyConst}
     */
    private Map<String, String> ipValidMap;

}
