package com.zhixianghui.api.base.params;


import com.zhixianghui.common.statics.annotations.NotSign;
import com.zhixianghui.common.statics.enums.api.CallbackTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 异步回调商户的请求数据
 */
@Getter
@Setter
public class CallbackReqParam {
    /**
     * 回调类型
     * {@link CallbackTypeEnum}
     */
    private String callbackType;
    private String mchNo;
    private String data;
    private String randStr;
    private String signType;
    @NotSign
    private String sign;
    @NotSign
    private String secKey;

}
