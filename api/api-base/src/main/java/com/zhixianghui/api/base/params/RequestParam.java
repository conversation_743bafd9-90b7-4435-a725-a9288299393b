package com.zhixianghui.api.base.params;

import com.zhixianghui.common.statics.annotations.NotSign;

import java.util.List;

/**
 * 用户的请求参数
 * <AUTHOR>
 * @date 2018-12-15
 */
public class RequestParam {
    private String method;
    private String version;

    private String mchNo;
    private String data;
    private String randStr;
    private String signType;
    @NotSign
    private String sign;
    @NotSign
    private String secKey;
    @NotSign
    private String idCardFrontPhoto;
    @NotSign
    private String idCardBackPhoto;
    @NotSign
    private String cerFacePhoto;
    @NotSign
    private List<FileParam> fileList;


    public String getIdCardFrontPhoto() {
        return idCardFrontPhoto;
    }

    public void setIdCardFrontPhoto(String idCardFrontPhoto) {
        this.idCardFrontPhoto = idCardFrontPhoto;
    }

    public String getIdCardBackPhoto() {
        return idCardBackPhoto;
    }

    public void setIdCardBackPhoto(String idCardBackPhoto) {
        this.idCardBackPhoto = idCardBackPhoto;
    }

    public String getCerFacePhoto() {
        return cerFacePhoto;
    }

    public void setCerFacePhoto(String cerFacePhoto) {
        this.cerFacePhoto = cerFacePhoto;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMchNo() {
        return mchNo;
    }

    public void setMchNo(String mchNo) {
        this.mchNo = mchNo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getRandStr() {
        return randStr;
    }

    public void setRandStr(String randStr) {
        this.randStr = randStr;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getSecKey() {
        return secKey;
    }

    public void setSecKey(String secKey) {
        this.secKey = secKey;
    }

    public List<FileParam> getFileList() {
        return fileList;
    }

    public void setFileList(List<FileParam> fileList) {
        this.fileList = fileList;
    }
}
