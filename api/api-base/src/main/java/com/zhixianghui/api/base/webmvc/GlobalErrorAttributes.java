package com.zhixianghui.api.base.webmvc;

import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import org.slf4j.LoggerFactory;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.HashMap;
import java.util.Map;

/**
 * 使用webmvc时的全局异常处理器，负责决定返回什么响应体
 */
public class GlobalErrorAttributes extends DefaultErrorAttributes {

    @Override
    public Map<String, Object> getErrorAttributes(WebRequest request, boolean includeStackTrace) {
        Throwable ex = getError(request);
        ResponseDto<?> responseDto = getResponseDto(ex);
        Map<String,Object> resultMap = new HashMap<>();
        BeanUtil.toMap(responseDto).forEach((s, o) -> {
            resultMap.put(StringUtil.camelToUnderscore(s), o);
        });
        return resultMap;
    }

    private ResponseDto<?> getResponseDto(Throwable ex) {
        StringBuilder errMsg = new StringBuilder();
        if (ex instanceof BizException) {
            BizException e = (BizException) ex;
            if (StringUtil.isEmpty(e.getApiErrorCode())) {
                return ResponseDto.fail(ApiExceptions.API_COMMON_ERROR.getApiErrorCode(), e.getErrMsg());
            } else {
                return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
            }
        } else if (isRequestFrameworkException(ex, errMsg)) {
            return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), errMsg.toString());
        } else {
            LoggerFactory.getLogger(this.getClass()).info("请求异常：{}", ex.getCause());
            return ResponseDto.unknown();
        }
    }

    private boolean isRequestFrameworkException(Throwable ex, StringBuilder strBuilder) {
        if (ex instanceof NoHandlerFoundException) { //path路径不存在时
            strBuilder.append("请求路径错误，请检查method参数");
            return true;
        } else if (ex instanceof MethodArgumentNotValidException) { //hibernate-validator 参数校验不通过时
            FieldError fieldError = ((MethodArgumentNotValidException) ex).getBindingResult().getFieldError();
            String field = fieldError.getField();
            String msg = fieldError.getDefaultMessage();
            strBuilder.append("验参失败，" + field + ":" + msg + "");
            return true;
        } else if (ex instanceof HttpMessageNotReadableException) {//参数类型转换错误时
            strBuilder.append("请求参数无法转换或读取，请检查参数类型");
            return true;
        } else if (ex instanceof HttpMediaTypeNotSupportedException) {//用户传入的MediaType与系统在方法上设置的不一致时
            strBuilder.append("请参照接口文档选择合适的请求MediaType");
            return true;
        } else if (ex instanceof HttpRequestMethodNotSupportedException) {//用户请求方式与系统在方法上设置的不一致时，如：方法要求POST但用法使用GET请求
            strBuilder.append("请参照接口文档选择合适的HTTP Method");
            return true;
        } else if (ex instanceof HttpMediaTypeNotAcceptableException) {
            strBuilder.append("请参照接口文档选择合适的响应MediaType");
            return true;
        } else if (ex instanceof MissingPathVariableException) {
            strBuilder.append("请参照接口文档传入合适的uri参数");
            return true;
        } else if (ex instanceof MissingServletRequestParameterException) {
            strBuilder.append("请参照接口文档传入合适的请求参数");
            return true;
        } else if (ex instanceof ConversionNotSupportedException) {
            strBuilder.append("参数类型错误，请详细阅读相关接口文档");
            return true;
        } else if (ex instanceof TypeMismatchException) {
            strBuilder.append("参数类型错误，请详细阅读相关接口文档");
            return true;
        } else if (ex instanceof MissingServletRequestPartException) {
            strBuilder.append("系统不支持form-data或请求体内容有误");
            return true;
        }
        return false;
    }
}
