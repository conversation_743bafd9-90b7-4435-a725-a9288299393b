package com.zhixianghui.api.base.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.NotSign;
import com.zhixianghui.api.base.params.MerchantInfo;
import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.base.params.ResponseParam;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.TreeMap;

/**
 * 签名、验签的工具类
 *
 * <AUTHOR>
 * @date 2018-12-15
 */
@Slf4j
public class SignUtil {

    public final static String SIGN_SEPARATOR = "&";//分隔符
    public final static String SIGN_EQUAL = "=";//等于号

    /**
     * 验证签名
     *
     * @param requestParam
     * @param merchantInfo
     * @return
     */
    public static boolean verify(RequestParam requestParam, MerchantInfo merchantInfo) {
        String signStr = getSortedString(requestParam);
        if (String.valueOf(SignTypeEnum.RSA.getValue()).equals(requestParam.getSignType())) {
            boolean verifyResult = RSAUtil.verify(signStr, merchantInfo.getSignValidKey(), requestParam.getSign(), true);
            if(!verifyResult){
                log.info("验签失败，待签名字符串为：{}", signStr);
            }
            return verifyResult;
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("验签失败，未预期的签名类型：" + requestParam.getSignType());
        }
    }

    /**
     * 生成签名
     *
     * @param responseParam
     * @param merchantInfo
     * @return
     */
    public static void sign(ResponseParam responseParam, MerchantInfo merchantInfo) {
        if (responseParam == null) {
            return;
        } else if (StringUtil.isEmpty(responseParam.getMchNo()) || StringUtil.isEmpty(responseParam.getSignType())) {
            responseParam.setSign("");
            return;
        }
        if (StringUtil.isEmpty(responseParam.getRandStr())) {
            responseParam.setRandStr(RandomUtil.get32LenStr());
        }

        String signStr = getSortedString(responseParam);
        String signResult;
        if (String.valueOf(SignTypeEnum.RSA.getValue()).equals(responseParam.getSignType())) {
            signResult = RSAUtil.sign(signStr, merchantInfo.getSignGenKey(), true);
        } else {
            //抛出签名失败的异常
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签名失败，未预期的签名类型：" + responseParam.getSignType());
        }

        responseParam.setSign(signResult);
    }

    public static String sign(Object obj, int signType, String priKey) {
        String signStr = getSortedString(obj);
        String signResult;
        if (signType == SignTypeEnum.RSA.getValue()) {
            signResult = RSAUtil.sign(signStr, priKey, true);
        } else {
            //抛出签名失败的异常
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签名失败，未预期的签名类型：" + signType);
        }
        return signResult;
    }

    /**
     * 验签
     *
     * @param obj       待验签对象
     * @param signedStr 已签名的字符串
     * @param signType  签名类型
     * @param pubKey    验签公钥
     * @return
     */
    public static boolean verify(Object obj, String signedStr, int signType, String pubKey) {
        String signStr = getSortedString(obj);
        if (signType == SignTypeEnum.RSA.getValue()) {
            boolean verifyResult = RSAUtil.verify(signStr, pubKey, signedStr, true);
            if(!verifyResult){
                log.info("验签失败，待签名字符串为：{}", signStr);
            }
            return verifyResult;
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("验签失败，未预期的签名类型：" + pubKey);
        }
    }

    private static String getSortedString(Object obj) {
        Field[] fields = obj.getClass().getDeclaredFields();
        //treeMap里的key 是按照字典序排序的
        Map<String, Object> signMap = new TreeMap<>();
        for (Field filed : fields) {
            String name = filed.getName();
            NotSign notSign = filed.getAnnotation(NotSign.class);
            if (notSign != null) {//不参与签名或验签的参数直接跳过
                continue;
            }

            Object value;
            try {
                filed.setAccessible(true);
                value = filed.get(obj);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            //转成原始下划线
            signMap.put(StringUtil.camelToUnderscore(name), value);
        }

        StringBuilder content = new StringBuilder();
        signMap.forEach((key, value) -> {
            String signValue;
            if (value == null) {
                signValue = "";
            } else if (value instanceof String) {
                signValue = (String) value;
            } else {
                signValue = JsonUtil.toString(value);
            }

            content.append(key).append(SIGN_EQUAL);
            content.append(signValue);
            content.append(SIGN_SEPARATOR);
        });
        content.deleteCharAt(content.lastIndexOf(SIGN_SEPARATOR));

        return content.toString();
    }




    public static void main(String[] args) {
        //组装参数实体
        Map<String, Object> data = Maps.newHashMap();
        data.put("mch_order_no", "E20201215004572152");
        RequestParam requestParam =new RequestParam();
        requestParam.setMethod("zxh.queryDetail");
        requestParam.setVersion("1");
        requestParam.setMchNo("M00000108");
        requestParam.setData(JSON.toJSONString(data));
        requestParam.setRandStr(RandomUtil.get32LenStr());
        requestParam.setSignType("1");
        requestParam.setSecKey("");
        requestParam.setSign(sign(requestParam, 1,"MIICWwIBAAKBgQCJkRGUkcrCHR18UY4015laNgk6n3fKPBR0ZgWpY9KA5Q8CiDTlBNtvJLQY0Vm+3vbRb3Rmup8wCJoN2wS67OW32xrr7jEawC3Y5SvQaUM44JM1hHaQqOXblxIsD8wmdpI7bOwziZhiTATnV1SGUjBI78fW+i0bXJiDpYpKsHCg+QIDAQABAoGAFChfi5t7ZaCaN/kZXeaJED4icbvOnqHZcMmcTcW/TDzZoLTbD1imsNgPfc17buvBU09syTrSZa70/U/DwdeXEiGKoFNCXBtXAquducYT5X/M0uiCMTKZWVKu4LHAX0IkH1tlTQ7/w2Tt8foo3VhwCXY7rPEM+jnbj97WCVH3SpMCQQDlACj8rBDiwaH0IO2/u/N9tyoUfRCfb9SlHSMMDbG7dMlRxbwmhlZrqrdBb8GIx1VTKROkyx8mbjOj6smttIc7AkEAmckxJCkHK+50sPADQ/4vcQYndHEKuKIU0vv4Mu5bAFOUO2pXzF1txtcuIdu8aZ43euBj2jHGpH13NsJWUWq9WwJATq3oZy/jOVWYBF3P8eFsvlPRRGrX7D+rCvPg18JYv3RXux26EG51gNaN0hDIqOr72O+zDRqw4C5a1cXUdzcggwJASiYOsp+xnIYB75oaCPuTBkzwUx16DOHhZoUk+/GfIP4rd6ZjpN7bYkc4MA0RwF8P4wg0PO1JGOCBzX5QPjtYBwJAdJHQzmnFCk3x26X4oVkv75F4jd1Lfz2ewiwEZEEVxY75C9VsdF51eosD9D2bMgIjY8rJQRW+k4f7OoOUgdxpaw=="));
        System.out.println(JsonUtil.toString(requestParam));
    }

}
