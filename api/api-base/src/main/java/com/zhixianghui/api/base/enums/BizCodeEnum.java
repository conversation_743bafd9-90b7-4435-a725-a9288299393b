package com.zhixianghui.api.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 汇聚智享正常响应码
 * @author: xingguang li
 * @created: 2020/12/28 09:48
 */
@AllArgsConstructor
@Getter
@ToString
public enum BizCodeEnum {

    SUCCESS("HS000000", "成功"),
    IN_PROCESS("HS200000", "处理中"),
    HAS_BEEN_COMPLETED("HS400000", "已经成功签约过"),
    RESEND_EXCEED_LIMIT("HS500000", "发送短信24h小时内不能超过3次"),
    ;
    /**
     * 错误码
     */
    private String code;
    /**
     * 描述
     */
    private String msg;
}
