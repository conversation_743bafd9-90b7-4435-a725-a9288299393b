package com.zhixianghui.api.base.dto;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.enums.common.ApiRespCodeEnum;
import com.zhixianghui.common.util.utils.StringUtil;

import java.util.Map;

/**
 * 响应给商户的VO，主要用作Controller的出参
 *
 * <AUTHOR>
 * @date 2018-12-15
 */
public class ResponseDto<T> {
    private String respCode;
    private T data;
    private String secKey = "";


    public static <T> ResponseDto<T> success(T data, String secKey) {
        ResponseDto<T> vo = new ResponseDto<>();
        vo.setRespCode(ApiRespCodeEnum.SUCCESS.getCode());
        vo.setData(data);
        vo.setSecKey(StringUtil.isEmpty(secKey) ? "" : secKey);
        return vo;
    }

    public static <T> ResponseDto<T> success(T data) {
        return success(data, "");
    }

    public static ResponseDto fail(String bizErrCode, String bizErrMsg) {
        ResponseDto<ApiBizBaseDto> vo = new ResponseDto<>();
        vo.setRespCode(ApiRespCodeEnum.FAIL.getCode());
        vo.setData(new ApiBizBaseDto(bizErrCode, bizErrMsg));
        vo.setSecKey("");
        return vo;
    }

    public static ResponseDto unknown() {
        ResponseDto<Map<String, Object>> vo = new ResponseDto<>();
        vo.setRespCode(ApiRespCodeEnum.UNKNOWN.getCode());
        vo.setData(null);
        vo.setSecKey("");
        return vo;
    }


    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getSecKey() {
        return secKey;
    }

    public void setSecKey(String secKey) {
        this.secKey = secKey;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
