package com.zhixianghui.api.base.dto;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * Author: Cmf
 * Date: 2020.3.6
 * Time: 11:21
 * Description:对外API处理失败时响应的data内容
 */
@JsonNaming(com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy.class)
@JSONType(naming= com.alibaba.fastjson.PropertyNamingStrategy.SnakeCase)
public class ApiBizBaseDto {
    protected String bizErrCode;
    protected String bizErrMsg;

    public String getBizErrCode() {
        return bizErrCode;
    }

    public void setBizErrCode(String bizErrCode) {
        this.bizErrCode = bizErrCode;
    }

    public String getBizErrMsg() {
        return bizErrMsg;
    }

    public void setBizErrMsg(String bizErrMsg) {
        this.bizErrMsg = bizErrMsg;
    }

    public ApiBizBaseDto(String bizErrCode, String bizErrMsg) {
        this.bizErrCode = bizErrCode;
        this.bizErrMsg = bizErrMsg;
    }

    protected ApiBizBaseDto() {
    }
}
