package com.zhixianghui.api.base.params;

import lombok.Getter;
import lombok.Setter;

/**
 * 异步回调时商户侧的响应对象
 */
@Getter
@Setter
public class CallbackRespParam {
    public final static String SUCCESS = "success";//成功
    public final static String RETRY = "retry";//重试，当http调用失败时，需要重试
    public final static String FAIL = "fail";//失败,如返回码正常，但数据有误

    /**
     * 商户响应的状态：success/retry/fail
     */
    private String status;
    /**
     * 商户响应的信息
     */
    private String message;
    /**
     * 网络异常等异常描述
     */
    private String errMsg;
    /**
     * 响应数据的签名，目前仅做预留
     */
    private String sign;
}
