package com.zhixianghui.api.base.params;

import com.zhixianghui.common.statics.annotations.NotSign;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import com.zhixianghui.common.statics.enums.common.ApiRespCodeEnum;
import com.zhixianghui.common.util.utils.JsonUtil;

import java.util.Collections;

/**
 * 响应用户请求的参数
 *
 * <AUTHOR>
 * @date 2018-12-15
 */
public class ResponseParam {
    private String respCode;
    private String mchNo;
    private String data;
    private String randStr;
    private String signType;
    @NotSign
    private String sign;
    @NotSign
    private String secKey;

    public static ResponseParam unknown(String mchNo) {
        ResponseParam responseParam = new ResponseParam();
        responseParam.setRespCode(ApiRespCodeEnum.UNKNOWN.getCode());
        responseParam.setMchNo(mchNo);
        responseParam.setData(JsonUtil.toString(Collections.emptyMap()));
        responseParam.setSecKey("");
        return responseParam;
    }

    public static ResponseParam fail(String mchNo, String bizErrCode, String bizErrMsg) {
        ResponseParam responseParam = new ResponseParam();
        responseParam.setRespCode(ApiRespCodeEnum.FAIL.getCode());
        responseParam.setMchNo(mchNo);
        responseParam.setData(JsonUtil.toString(new ApiBizBaseDto(bizErrCode, bizErrMsg)));
        responseParam.setSecKey("");
        return responseParam;
    }

    public String getRespCode() {
        return respCode;
    }

    public void setRespCode(String respCode) {
        this.respCode = respCode;
    }

    public String getMchNo() {
        return mchNo;
    }

    public void setMchNo(String mchNo) {
        this.mchNo = mchNo;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getRandStr() {
        return randStr;
    }

    public void setRandStr(String randStr) {
        this.randStr = randStr;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public String getSecKey() {
        return secKey;
    }

    public void setSecKey(String secKey) {
        this.secKey = secKey;
    }
}
