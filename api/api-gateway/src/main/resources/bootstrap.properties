spring.application.name=api-gateway
spring.cloud.nacos.config.server-addr=@nacosAddr@
spring.cloud.nacos.config.namespace=@nacosNamespace@
spring.cloud.nacos.config.file-extension=properties

#\u8BBE\u7F6E\u5171\u4EAB\u7684\u914D\u7F6E\u6587\u4EF6
spring.cloud.nacos.config.shared-dataids=dubbo.properties,redis.properties
#spring.cloud.nacos.config.refreshable-dataids=

# 项目启动nacos配置信息打印级别，info会打印出配置信息
logging.level.com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder=warn