package com.zhixianghui.api.gateway.handler;

import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.base.params.ResponseParam;
import com.zhixianghui.api.gateway.config.conts.ReqCacheKey;
import com.zhixianghui.api.gateway.exceptions.GatewayException;
import com.zhixianghui.api.gateway.helper.RequestHelper;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.ResourceProperties;
import org.springframework.boot.autoconfigure.web.reactive.error.DefaultErrorWebExceptionHandler;
import org.springframework.boot.web.reactive.error.ErrorAttributes;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.cloud.gateway.support.TimeoutException;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.*;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 全局异常处理器
 * @date 2019-02-23
 */
public class GatewayExceptionHandler extends DefaultErrorWebExceptionHandler {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    protected static final String HTTP_STATUS_KEY = "httpStatus";

    @Autowired
    RequestHelper requestHelper;

    public GatewayExceptionHandler(ErrorAttributes errorAttributes, ResourceProperties resourceProperties,
                                   ErrorProperties errorProperties, ApplicationContext applicationContext) {
        super(errorAttributes, resourceProperties, errorProperties, applicationContext);
    }

    /**
     * 指定响应处理方法为JSON处理的方法
     *
     * @param errorAttributes .
     */
    @Override
    protected RouterFunction<ServerResponse> getRoutingFunction(ErrorAttributes errorAttributes) {
        return RouterFunctions.route(RequestPredicates.all(), this::renderErrorResponse);
    }

    @Override
    protected Mono<ServerResponse> renderErrorResponse(ServerRequest request) {
        Map<String, Object> error = getErrorAttributes(request, false);

        return ServerResponse.status(HttpStatus.OK)
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .body(BodyInserters.fromObject(error))
                .doOnNext((resp) -> logError(request));
    }

    /**
     * 获取异常属性
     */
    @Override
    protected Map<String, Object> getErrorAttributes(ServerRequest request, boolean includeStackTrace) {
        Throwable error = super.getError(request);

        //当异常发生在读请求体的body之前时，此值为null，这种情况多半是因为路径参数不对，为避免有人恶意攻击，此处不再读取用户的商户信息来进行加签，直接返回空串
        RequestParam requestParam = (RequestParam) request.attributes().get(ReqCacheKey.CACHE_REQUEST_BODY_OBJECT_KEY);
        String version = requestParam != null ? requestParam.getVersion() : "";

        ResponseParam response = this.buildResponseParam(requestParam, error);
        response.setSignType(requestParam == null ? "" : requestParam.getSignType() == null ? "" : requestParam.getSignType());
        try {
            requestHelper.signAndEncrypt(response);
        } catch (Throwable e) {
            if (response.getSign() == null) {
                response.setSign("");
            }
            logger.error("网关异常处理器中，添加签名时出现异常 RequestParam = {} ResponseParam = {}", JsonUtil.toString(requestParam), JsonUtil.toString(response), e);
        }

        //把响应内容转成Map
        Map<String, Object> respMap = new HashMap<>();
        BeanUtil.toMap(response).forEach((s, o) -> {
            respMap.put(StringUtil.camelToUnderscore(s), o);
        });
        return respMap;
    }

    protected void logError(ServerRequest request) {
        Throwable ex = super.getError(request);
        RequestParam requestParam = (RequestParam) request.attributes().get(ReqCacheKey.CACHE_REQUEST_BODY_OBJECT_KEY);
        if (ex instanceof GatewayException) {
            logger.error("网关处理过程中出现业务判断异常 Exception = {} RequestParam = {}", ((GatewayException) ex).toMsg(), JsonUtil.toString(requestParam));
        } else {
            logger.error("网关处理过程中出现未预期异常 RequestParam = {} ", JsonUtil.toString(requestParam), ex);
        }
    }


    private ResponseParam buildResponseParam(RequestParam requestParam, Throwable ex) {
        logger.error("出现错误", ex);

        String mchNo = requestParam == null ? "" : requestParam.getMchNo();

        if (ex instanceof GatewayException) {
            return ResponseParam.fail(mchNo, ((GatewayException) ex).getErrorCode(), ((GatewayException) ex).getErrorMsg());
        } else if (ex instanceof TimeoutException) {
            return ResponseParam.unknown(mchNo);
        } else if (ex instanceof NotFoundException) { //后端服务无法从注册中心被发现时
            return ResponseParam.fail(mchNo, GatewayException.SERVICE_NOT_AVAILABLE.getErrorCode(), GatewayException.SERVICE_NOT_AVAILABLE.getErrorMsg());
        } else if (ex instanceof ResponseStatusException) { //访问没有配置的route path时
            return ResponseParam.fail(mchNo, GatewayException.SERVICE_NOT_FOUND.getErrorCode(), GatewayException.SERVICE_NOT_FOUND.getErrorMsg());
        } else {
            LoggerFactory.getLogger(this.getClass()).info("************************known********************");
            return ResponseParam.unknown(mchNo);
        }
    }
}
