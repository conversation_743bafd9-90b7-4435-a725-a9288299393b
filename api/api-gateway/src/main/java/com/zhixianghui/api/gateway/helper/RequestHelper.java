package com.zhixianghui.api.gateway.helper;

import com.zhixianghui.api.base.params.MerchantInfo;
import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.base.params.ResponseParam;
import com.zhixianghui.api.base.service.MchService;
import com.zhixianghui.api.base.utils.SignUtil;
import com.zhixianghui.api.gateway.exceptions.GatewayException;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 签名、验签的组件，需要使用此组件的项目，需要自己配置这个@Bean，同时自己实现UserService、ValidFailService 然后再通过Spring进行依赖注入
 * @date 2019-02-20
 */
@Component
public final class RequestHelper {
    private Logger logger = LoggerFactory.getLogger(RequestHelper.class);
    @Autowired
    private MchService mchService;

    /**
     * IP校验
     *
     * @param ip
     * @param ipValidKey
     * @param requestParam
     * @return
     */
    public boolean ipVerify(String ip, String ipValidKey, RequestParam requestParam) {
        if (requestParam == null || StringUtil.isEmpty(requestParam.getMchNo())) {
            return false;
        }

        MerchantInfo merchantInfo = mchService.getMerchantInfo(requestParam.getMchNo());
        Map<String, String> ipMap = merchantInfo.getIpValidMap();
        String expectIp = ipMap == null ? null : ipMap.get(ipValidKey);//预期的Ip
        logger.debug("==>gateway ipVerify mchIp:{},expectIp:{},mchNo:{}", ip, expectIp, requestParam.getMchNo());
        //为空说明不需要检验IP
        if (StringUtil.isEmpty(expectIp)) {
            return true;
        } else {
            return expectIp.contains(ip);
        }
    }

    /**
     * 签名校验
     *
     * @param requestParam
     * @return
     */
    public boolean signVerify(RequestParam requestParam) {
        if (requestParam == null || StringUtil.isEmpty(requestParam.getMchNo())) {
            return false;
        }
        MerchantInfo merchantInfo = mchService.getMerchantInfo(requestParam.getMchNo());
        try {
            return SignUtil.verify(requestParam, merchantInfo);
        } catch (Throwable e) {
            logger.error("验签失败，因为验签时出现异常 RequestParam = {} e:", JsonUtil.toString(requestParam), e);
            return false;
        }
    }

    /**
     * 商户信息校验
     *
     * @param requestParam
     * @return
     */
    public boolean mchVerify(RequestParam requestParam) {
        if (requestParam == null || StringUtil.isEmpty(requestParam.getMchNo())) {
            return false;
        }

        MerchantInfo merchantInfo = mchService.getMerchantInfo(requestParam.getMchNo());

        if (merchantInfo == null) {
            throw GatewayException.MCH_VALID_ERROR.newWithErrMsg("商户验证失败：未找到该商户信息");
        }

        //判断商户的激活状态
        if (PublicStatus.ACTIVE == merchantInfo.getMchStatus()) {
            return true;
        } else {
            throw GatewayException.MCH_VALID_ERROR.newWithErrMsg("商户状态未被激活");
        }
    }


    /**
     * 给aes_key解密
     *
     * @param requestParam
     */
    public String secKeyDecrypt(RequestParam requestParam) {
        if (requestParam == null || StringUtil.isEmpty(requestParam.getMchNo())) {
            throw GatewayException.PARAM_CHECK_ERROR.newWithErrMsg("商户号为空");
        }
        if (StringUtil.isEmpty(requestParam.getSecKey())) {
            return "";
        }

        MerchantInfo merchantInfo = mchService.getMerchantInfo(requestParam.getMchNo());
        if (merchantInfo == null || StringUtil.isEmpty(merchantInfo.getSecKeyDecryptKey())) {
            throw GatewayException.MCH_VALID_ERROR.newWithErrMsg("无法获取商户密钥信息");
        }
        //使用平台敏感信息key解密RSA私钥解密
        return RSAUtil.decryptByPrivateKey(requestParam.getSecKey(), merchantInfo.getSecKeyDecryptKey());
    }

    /**
     * 生成签名,如果有secKey，使用商户提供的公钥对其进行加密
     *
     * @param responseParam
     */
    public void signAndEncrypt(ResponseParam responseParam) {

        MerchantInfo merchantInfo = null;

        if (StringUtil.isEmpty(responseParam.getMchNo())
                || (merchantInfo = mchService.getMerchantInfo(responseParam.getMchNo())) == null) {
            if (StringUtil.isEmpty(responseParam.getMchNo())) {
                responseParam.setMchNo("");
            }
            responseParam.setRandStr("");
            responseParam.setSign("");
            responseParam.setSignType("");
            return;
        }
        if (StringUtil.isEmpty(responseParam.getSignType())) {
            responseParam.setSignType(String.valueOf(merchantInfo.getSignType()));
        }

        //如果加密失败，定会抛出异常，此时签名串就为空值，客户端就会验签失败，所以，加密这一步骤要放在加签名之前
        if (StringUtil.isNotEmpty(responseParam.getSecKey())) {
            if (Integer.parseInt(responseParam.getSignType()) == SignTypeEnum.RSA.getValue()) {
            try {
                responseParam.setSecKey(RSAUtil.encryptByPublicKey(responseParam.getSecKey(), merchantInfo.getSecKeyEncryptKey()));
            } catch (Exception e) {
                logger.error("secKey加密错误，responseParam:{}, e:", JsonUtil.toString(responseParam), e);
            }
            } else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不支持的签名算法类型");
            }
        }

        SignUtil.sign(responseParam, merchantInfo);
    }
}
