package com.zhixianghui.api.gateway.config;

import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import java.util.Arrays;
import java.util.List;

@ConfigurationProperties(value = "api.gateway.config")
@SpringBootConfiguration
public class PropertiesConfig {
    private String ipBlackListPattern = "";
    private String offBuss = "";
    private List<String> filterUrls;
    private List<String> urlWhiteList;
    private String urlWhite;

    public void setUrlWhite(String urlWhite) {
        String[] array = urlWhite.split(",");
        this.urlWhiteList = Arrays.asList(array);
    }

    public String getUrlWhite() {
        return urlWhite;
    }
    public List<String> getUrlWhiteList() {
        return urlWhiteList;
    }

    public String getIpBlackListPattern() {
        return ipBlackListPattern;
    }

    public void setIpBlackListPattern(String ipBlackListPattern) {
        this.ipBlackListPattern = ipBlackListPattern;
    }

    public String getOffBuss() {
        return offBuss;
    }

    public void setOffBuss(String offBuss) {
        this.offBuss = offBuss;
    }

    public List<String> getFilterUrls() {
        return filterUrls;
    }

    public void setFilterUrls(List<String> filterUrls) {
        this.filterUrls = filterUrls;
    }
}
