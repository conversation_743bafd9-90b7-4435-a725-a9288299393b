package com.zhixianghui.api.gateway.filters.global;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.api.base.constants.HttpHeaderKey;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.gateway.config.PropertiesConfig;
import com.zhixianghui.api.gateway.config.conts.FilterOrder;
import com.zhixianghui.api.gateway.exceptions.GatewayException;
import com.zhixianghui.api.gateway.filters.cache.CachedBodyOutputMessage;
import com.zhixianghui.api.gateway.helper.RequestHelper;
import com.zhixianghui.api.gateway.utils.CheckUtil;
import com.zhixianghui.api.gateway.utils.IPUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @description 修改请求体，包括：sec_key解密 等
 * @date 2019-02-23
 */
@Component
@Log4j2
public class RequestModifyFilter extends AbstractGlobalFilter {
    @Autowired
    RequestHelper requestHelper;
    @Autowired
    private PropertiesConfig propertiesConfig;
    @Autowired
    private CheckUtil checkUtil;

    /**
     * 设置当前过滤器的执行顺序：本过滤器在全局过滤器中的顺序建议为第4个
     *
     * @return
     */
    @Override
    public int getOrder() {
        return FilterOrder.REQUEST_DECRYPT_FILTER;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        RequestParam requestParam = (RequestParam) exchange.getAttributes().get(CACHE_REQUEST_BODY_OBJECT_KEY);
        ServerHttpRequest req = exchange.getRequest();
        String originalPath = req.getURI().getPath();
        if (ObjectUtils.isNotEmpty(requestParam)&&StringUtil.isNotEmpty(requestParam.getMethod()) && isPass(requestParam.getMethod(), propertiesConfig.getUrlWhiteList())) {
            return chain.filter(exchange);
        }
        if(checkUtil.checkFilter(originalPath)){
            return chain.filter(exchange);
        }
        //1.设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.putAll(exchange.getRequest().getHeaders());
        headers.set(HttpHeaderKey.REQUEST_MCH_IP_KEY, IPUtil.getIpAddr(exchange.getRequest()));//将请求IP放到请求头里
        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE);//强制设置为application/json;charset=UTF-8
        headers.remove(HttpHeaders.CONTENT_LENGTH);


        //3.创建转发到后端服务的请求体
        RequestDto<JSONObject> requestDto = createRequestDto(requestParam);

        Mono<String> modifiedBody = Mono.just(JsonUtil.toString(requestDto));

        //5.更新转发到后端服务的body
        BodyInserter bodyInserter = BodyInserters.fromPublisher(modifiedBody, String.class);
        CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, headers);
        return bodyInserter.insert(outputMessage, new BodyInserterContext())
                .then(Mono.defer(() -> {
                    ServerHttpRequestDecorator decorator = new ServerHttpRequestDecorator(exchange.getRequest()) {
                        @Override
                        public HttpHeaders getHeaders() {
                            long contentLength = headers.getContentLength();
                            HttpHeaders httpHeaders = new HttpHeaders();
                            httpHeaders.putAll(headers);
                            if (contentLength > 0) {
                                httpHeaders.setContentLength(contentLength);
                            } else {
                                httpHeaders.set(HttpHeaders.TRANSFER_ENCODING, "chunked");
                            }
                            return httpHeaders;
                        }

                        @Override
                        public Flux<DataBuffer> getBody() {
                            return outputMessage.getBody();
                        }
                    };
                    return chain.filter(exchange.mutate().request(decorator).build());
                }));
    }

    private String secKeyDecrypt(RequestParam requestParam) {
        if (StringUtil.isEmpty(requestParam.getSecKey())) {
            return "";
        }
        try {
            return requestHelper.secKeyDecrypt(requestParam);
        } catch (Throwable ex) {
            log.error("sec_key解密失败：", ex);
            throw GatewayException.PARAM_CHECK_ERROR.newWithErrMsg("sec_key解密失败");
        }
    }

    private RequestDto<JSONObject> createRequestDto(RequestParam requestParam) {
        //把string类型的data转换成JSONObject，是为了让后端服务能够实现参数自动注入
        RequestDto<JSONObject> requestDto = new RequestDto<>();
        requestDto.setVersion(requestParam.getVersion());
        requestDto.setMchNo(requestParam.getMchNo());
        requestDto.setData(convertJsonObj(requestParam.getData()));
        requestDto.setSecKey(secKeyDecrypt(requestParam));
        requestDto.setSignType(requestParam.getSignType());
        requestDto.setIdCardBackPhoto(requestParam.getIdCardBackPhoto());
        requestDto.setIdCardFrontPhoto(requestParam.getIdCardFrontPhoto());
        requestDto.setCerFacePhoto(requestParam.getCerFacePhoto());
        requestDto.setFileList(requestParam.getFileList());
        return requestDto;
    }

    /**
     * 对key做下划线转驼峰
     * @param source
     * @return
     */
    private JSONObject convertJsonObj(String source) {
        if (StringUtils.isBlank(source)) {
            return null;
        }
        JSONObject obj = JSON.parseObject(source);
        JSONObject resultObj = new JSONObject();
        obj.forEach((s, o) -> {resultObj.put(StringUtil.underscoreToCamel(s), o);});
        return resultObj;
    }


}
