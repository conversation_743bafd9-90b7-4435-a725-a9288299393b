package com.zhixianghui.api.gateway.filters.global;

import com.zhixianghui.api.base.constants.HttpHeaderKey;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.base.params.ResponseParam;
import com.zhixianghui.api.base.utils.ResponseUtil;
import com.zhixianghui.api.gateway.config.PropertiesConfig;
import com.zhixianghui.api.gateway.config.conts.FilterOrder;
import com.zhixianghui.api.gateway.filters.cache.CachedBodyOutputMessage;
import com.zhixianghui.api.gateway.helper.RequestHelper;
import com.zhixianghui.api.gateway.utils.CheckUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR;

/**
 * <AUTHOR>
 * @description 响应体修改，包括：给响应体加签名 等
 * @date 2019-02-23
 */
@Component
public class ResponseModifyFilter extends AbstractGlobalFilter {
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	RequestHelper requestHelper;
	@Autowired
	private PropertiesConfig propertiesConfig;
	@Autowired
	private CheckUtil checkUtil;

	/**
	 * 设置当前过滤器的执行顺序：本过滤器在全局过滤器中的顺序为倒数第1个，在响应给用户之前给响应参数加上签名
     *
	 * @return
	 */
	@Override
	public int getOrder() {
		return FilterOrder.RESPONSE_MODIFY_FILTER;
	}

	@Override
	public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
		RequestParam requestParam = (RequestParam) exchange.getAttributes().get(CACHE_REQUEST_BODY_OBJECT_KEY);
		ServerHttpRequest req = exchange.getRequest();
		String originalPath = req.getURI().getPath();
		if (ObjectUtils.isNotEmpty(requestParam)&&StringUtil.isNotEmpty(requestParam.getMethod()) && isPass(requestParam.getMethod(), propertiesConfig.getUrlWhiteList())) {
			return chain.filter(exchange);
		}
		if(checkUtil.checkFilter(originalPath)){
			return chain.filter(exchange);
		}
		ServerHttpResponseDecorator responseDecorator = new ServerHttpResponseDecorator(exchange.getResponse()) {
			public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
				String originalResponseContentType = exchange.getAttribute(ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR);
				HttpHeaders httpHeaders = new HttpHeaders();
				httpHeaders.add(HttpHeaders.CONTENT_TYPE, originalResponseContentType);

				ClientResponse clientResponse = ClientResponse.create(exchange.getResponse().getStatusCode())
						.headers((headers) ->
						{ headers.putAll(httpHeaders); })
						.body(Flux.from(body))
						.build();

				Mono modifiedBody = clientResponse.bodyToMono(String.class)
						.flatMap((originalBody) -> {
							return Mono.just(buildNewResponseBody(exchange, originalBody, httpHeaders)
							);
						});

				BodyInserter bodyInserter = BodyInserters.fromPublisher(modifiedBody, String.class);
				CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, exchange.getResponse().getHeaders());
				return bodyInserter.insert(outputMessage, new BodyInserterContext()).then(Mono.defer(() -> {
					Flux<DataBuffer> messageBody = outputMessage.getBody();
					HttpHeaders headers = this.getDelegate().getHeaders();
					if (!headers.containsKey("Transfer-Encoding")) {
						messageBody = messageBody.doOnNext((data) -> {
							headers.setContentLength((long)data.readableByteCount());
						});
					}

					return this.getDelegate().writeWith(messageBody);
				}));
			}

			public Mono<Void> writeAndFlushWith(Publisher<? extends Publisher<? extends DataBuffer>> body) {
				return this.writeWith(Flux.from(body).flatMapSequential((p) -> {
					return p;
				}));
			}
		};
		return chain.filter(exchange.mutate().response(responseDecorator).build());
	}

	public String buildNewResponseBody(ServerWebExchange exchange, String originalBody, HttpHeaders httpHeaders){
		String respOriginalBody = httpHeaders.getFirst(HttpHeaderKey.RESP_ORIGINAL_BODY_KEY);
		//关闭对报文的加签，目前暂时网关支付返回页面需要关闭
		if (StringUtil.isNotEmpty(respOriginalBody) && "true".equals(respOriginalBody)) {
			//删除自定义的header，不能展示给页面或商户
			httpHeaders.remove(HttpHeaderKey.RESP_ORIGINAL_BODY_KEY);
			return originalBody;
		}

		RequestParam requestParam = (RequestParam) exchange.getAttributes().get(CACHE_REQUEST_BODY_OBJECT_KEY);
		ResponseDto<?> responseDto = JsonUtil.toBean(originalBody, ResponseDto.class);
		if(responseDto == null){
			return originalBody;//响应体为空，则原样返回
		}

		ResponseParam responseParam = new ResponseParam();
		responseParam.setRespCode(responseDto.getRespCode());
		responseParam.setMchNo(requestParam.getMchNo());
		responseParam.setData(JsonUtil.toString(responseDto.getData()));
		responseParam.setSignType(requestParam.getSignType());
		responseParam.setSecKey(responseDto.getSecKey());

		try{
			ResponseUtil.fillAcceptUnknownIfEmpty(responseParam);
			//添加签名
			requestHelper.signAndEncrypt(responseParam);
		}catch (Throwable e){
			logger.error("给响应信息添加签名时异常 RequestParam = {} ResponseParam = {}", JsonUtil.toString(requestParam), JsonUtil.toString(responseParam), e);
		}

		responseParam.setSign(responseParam.getSign() == null ? "" : responseParam.getSign());
		//返回结果转成下划线
		Map<String,Object> respMap = new HashMap<>();
		BeanUtil.toMap(responseParam).forEach((s, o) -> {
			respMap.put(StringUtil.camelToUnderscore(s), o);
		});
		return JsonUtil.toString(respMap);//ResponseParam.data中的数据会被加上反斜杠转义符
	}
}
