package com.zhixianghui.api.gateway.fallback;

import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.base.params.ResponseParam;
import com.zhixianghui.api.gateway.config.conts.ReqCacheKey;
import com.zhixianghui.api.gateway.helper.RequestHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * hystrix的熔断、降级时会进入到此处
 *
 * <AUTHOR>
 */
@RestController
public class FallbackController {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private RequestHelper requestHelper;

    /**
     * 有配置Hystrix过滤器时，发生熔断或降级时会进入此方法
     *
     * @param exchange
     * @return
     */
    @RequestMapping("/fallback")
    public Mono<ResponseParam> fallback(ServerWebExchange exchange) {
        logger.info("*************************fallback************************");
        Exception exception = exchange.getAttribute(ServerWebExchangeUtils.HYSTRIX_EXECUTION_EXCEPTION_ATTR);
        logger.info("fallback", exception);

        RequestParam requestParam = (RequestParam) exchange.getAttributes().get(ReqCacheKey.CACHE_REQUEST_BODY_OBJECT_KEY);
        String mchNo = requestParam == null ? "" : requestParam.getMchNo();
        String signType = requestParam == null ? "" : requestParam.getSignType();
        String version = requestParam == null ? "" : requestParam.getVersion();

        //因为调用后端服务超时的时候也会进入fallback，此时，我们并不知道业务处理结果，所以，统一返回"受理未知"响应信息
        ResponseParam responseParam = ResponseParam.unknown(mchNo);
        responseParam.setSignType(signType);
        requestHelper.signAndEncrypt(responseParam);

        return Mono.just(responseParam);
    }
}
