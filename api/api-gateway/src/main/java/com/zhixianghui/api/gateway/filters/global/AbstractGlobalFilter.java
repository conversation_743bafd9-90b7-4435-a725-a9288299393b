package com.zhixianghui.api.gateway.filters.global;

import com.zhixianghui.api.gateway.config.conts.ReqCacheKey;
import com.zhixianghui.api.gateway.exceptions.GatewayException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 全局过滤器抽象类，负责处理子类全局过滤器的一些公共逻辑
 * @date 2019-02-23
 */
public abstract class AbstractGlobalFilter implements GlobalFilter, Ordered {
    protected static final String CACHE_REQUEST_BODY_OBJECT_KEY = ReqCacheKey.CACHE_REQUEST_BODY_OBJECT_KEY;

    //todo 检查这个方法
    protected String subPathEnd(String path, String pattern, int count) {
        if (count > 3) {//避免商户不规范传入url时进入死循环
            throw GatewayException.PARAM_CHECK_ERROR.newWithErrMsg("请求路径不正确");
        } else if (path.endsWith(pattern)) {
            path = path.substring(0, path.length() - 1);
            return subPathEnd(path, pattern, count + 1);
        } else {
            return path;
        }
    }

    protected boolean isPass(String path, List<String> urlWhite) {
        if (StringUtils.isBlank(path) || CollectionUtils.isEmpty(urlWhite)) {
            return false;
        }
        for (String pattern : urlWhite) {
            if (path.startsWith(pattern)) {
                return true;
            }
        }
        return false;
    }
}
