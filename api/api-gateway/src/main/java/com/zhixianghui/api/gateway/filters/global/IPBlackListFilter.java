package com.zhixianghui.api.gateway.filters.global;

import com.zhixianghui.api.gateway.config.PropertiesConfig;
import com.zhixianghui.api.gateway.config.conts.FilterOrder;
import com.zhixianghui.api.gateway.exceptions.GatewayException;
import com.zhixianghui.api.gateway.utils.IPUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description IP黑名单过滤器
 * @date 2019-02-23
 */
@Component
public class IPBlackListFilter extends AbstractGlobalFilter {
    private Logger logger = LoggerFactory.getLogger(IPBlackListFilter.class);
    @Autowired
    private PropertiesConfig propertiesConfig;

    @Override
    public int getOrder() {
        return FilterOrder.IP_BLACKLIST_FILTER;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String ip = IPUtil.getIpAddr(exchange.getRequest());
        if (StringUtil.isEmpty(propertiesConfig.getIpBlackListPattern()) || isPass(propertiesConfig.getIpBlackListPattern(), ip)) {
            return chain.filter(exchange);
        }

        logger.warn("ip = {} 被列为黑名单，禁止访问！ pattern = {}", ip, propertiesConfig.getIpBlackListPattern());
        throw GatewayException.IP_BLACK_LIST;
    }

    private boolean isPass(String pattern, String ip) {
        return !Pattern.matches(pattern, ip);
    }
}
