package com.zhixianghui.api.gateway.config;

import com.zhixianghui.api.gateway.filters.gateway.IPValidGatewayFilterFactory;
import com.zhixianghui.api.gateway.filters.gateway.RateLimiterGatewayFilterFactory;
import com.zhixianghui.api.gateway.ratelimit.PathKeyResolver;
import com.zhixianghui.api.gateway.ratelimit.PathRedisRateLimiter;
import com.zhixianghui.api.gateway.ratelimit.SimpleRateLimiter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.validation.Validator;

import java.util.List;

@SpringBootConfiguration
public class GatewayConfig {

    @Bean
    public IPValidGatewayFilterFactory ipValidGatewayFilterFactory() {
        return new IPValidGatewayFilterFactory();
    }

    @Primary
    @Bean
    public PathRedisRateLimiter pathRedisRateLimiter(ReactiveRedisTemplate<String, String> redisTemplate,
                                                     @Qualifier(PathRedisRateLimiter.REDIS_SCRIPT_NAME) RedisScript<List<Long>> redisScript,
                                                     Validator validator) {
        return new PathRedisRateLimiter(redisTemplate, redisScript, validator);
    }

    @Bean
    public RateLimiterGatewayFilterFactory rateLimiterGatewayFilterFactory(PathRedisRateLimiter pathRedisRateLimiter, PathKeyResolver pathKeyResolver) {
        return new RateLimiterGatewayFilterFactory(pathRedisRateLimiter, pathKeyResolver);
    }

    @Bean("pathKeyResolver")
    public PathKeyResolver pathKeyResolver() {
        return new PathKeyResolver();
    }

    @Bean
    public SimpleRateLimiter simpleRateLimiter(RedisTemplate<String, String> redisTemplate, RedisScript<Long> simpleRateLimiterScript) {
        return new SimpleRateLimiter(redisTemplate, simpleRateLimiterScript);
    }

    @Bean("simpleRateLimiterScript")
    public RedisScript simpleRateLimiterScript() {
        DefaultRedisScript redisScript = new DefaultRedisScript<>();
        redisScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("scripts/simple_rate_limiter.lua")));
        redisScript.setResultType(Long.class);
        return redisScript;
    }
}
