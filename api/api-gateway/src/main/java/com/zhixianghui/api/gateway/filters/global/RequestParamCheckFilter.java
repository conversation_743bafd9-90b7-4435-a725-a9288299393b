package com.zhixianghui.api.gateway.filters.global;

import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.gateway.config.PropertiesConfig;
import com.zhixianghui.api.gateway.config.conts.FilterOrder;
import com.zhixianghui.api.gateway.exceptions.GatewayException;
import com.zhixianghui.api.gateway.utils.CheckUtil;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @description 请求参数校验，这个过滤器必须是在第2个，不然，后续的过滤器可能会会获取到错误的参数，或者因为某个参数为null而报空指针
 * @date 2019-02-23
 */
@Component
public class RequestParamCheckFilter extends AbstractGlobalFilter {
    @Autowired
    private CheckUtil checkUtil;
    @Autowired
    private PropertiesConfig propertiesConfig;

    /**
     * 设置当前过滤器的执行顺序：本过滤器在全局过滤器中的顺序必须为第2个，不然，后续的过滤器拿取参数时可能会出现空指针异常
     *
     * @return
     */
    @Override
    public int getOrder() {
        return FilterOrder.REQUEST_PARAM_CHECK_FILTER;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        RequestParam requestParam = (RequestParam) exchange.getAttributes().get(CACHE_REQUEST_BODY_OBJECT_KEY);
        ServerHttpRequest req = exchange.getRequest();
        String originalPath = req.getURI().getPath();

        if (ObjectUtils.isNotEmpty(requestParam)&&StringUtil.isNotEmpty(requestParam.getMethod()) && isPass(requestParam.getMethod(), propertiesConfig.getUrlWhiteList())) {
            return chain.filter(exchange);
        }
        if(checkUtil.checkFilter(originalPath)){
            return chain.filter(exchange);
        }
        String msg = requestPathValid(originalPath);
        if (StringUtil.isEmpty(msg)) {
            msg = paramValid(requestParam);
        }

        if (StringUtil.isEmpty(msg)) {
            return chain.filter(exchange);
        } else {
            //抛出异常，由全局异常处理器来处理响应信息
            throw GatewayException.PARAM_CHECK_ERROR.newWithErrMsg(msg);
        }
    }

    public String requestPathValid(String requestPath) {
        if (StringUtil.isEmpty(requestPath) || "/".equals(requestPath.trim())) {
            return "请求路径不能为空";
        } else {
            return "";
        }
    }

    public String paramValid(RequestParam requestParam) {
        if (requestParam == null) {
            return "参数请求体为空！";
        } else if (StringUtil.isEmpty(requestParam.getMethod())) {
            return "method 为空！";
        } else if (StringUtil.isEmpty(requestParam.getVersion())) {
            return "version 为空！";
        } else if (StringUtil.isEmpty(requestParam.getData())) {
            return "data 为空！";
        } else if (StringUtil.isEmpty(requestParam.getRandStr())) {
            return "rand_str 为空！";
        } else if (StringUtil.isEmpty(requestParam.getSignType())) {
            return "sign_type 为空！";
        } else if (StringUtil.isEmpty(requestParam.getMchNo())) {
            return "mch_no 为空！";
        } else if (StringUtil.isEmpty(requestParam.getSign())) {
            return "sign 为空！";
        }

        if (!ValidateUtil.isStrLengthValid(requestParam.getMethod(), 1, 64)) {
            return "method 的长度必须为1--64！";
        } else if (!ValidateUtil.isStrLengthValid(requestParam.getVersion(), 1, 5)) {
            return "version 的长度必须为1--5！";
        } else if (!ValidateUtil.isStrLengthValid(requestParam.getRandStr(), 32, 32)) {
            return "rand_str 的长度须为32！";
        } else if (!ValidateUtil.isStrLengthValid(requestParam.getSignType(), 1, 5)) {
            return "sign_type 的长度必须为1--5！";
        } else if (!ValidateUtil.isJson(requestParam.getData())) {
            return "data 必须为json格式";
        }

        if (!ValidateUtil.isInteger(requestParam.getSignType()) || SignTypeEnum.getEnum(Integer.parseInt(requestParam.getSignType())) == null) {
            return "sign_type 非法参数值 !!";
        }
        return "";
    }
}
