package com.zhixianghui.api.gateway.filters.global;

import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.base.service.ValidService;
import com.zhixianghui.api.gateway.config.PropertiesConfig;
import com.zhixianghui.api.gateway.config.conts.FilterOrder;
import com.zhixianghui.api.gateway.exceptions.GatewayException;
import com.zhixianghui.api.gateway.helper.RequestHelper;
import com.zhixianghui.api.gateway.utils.CheckUtil;
import com.zhixianghui.api.gateway.utils.IPUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @description 请求体鉴权校验，包括：签名校验 等等
 * @date 2019-02-23
 */
@Component
public class RequestAuthFilter extends AbstractGlobalFilter {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    RequestHelper requestHelper;
    @Autowired
    ValidService validFailService;
    @Autowired
    private PropertiesConfig propertiesConfig;
    @Autowired
    private CheckUtil checkUtil;

    /**
     * 设置当前过滤器的执行顺序：本过滤器在全局过滤器中的顺序建议为第3个，因为，如果鉴权不通过，就没有必要进行后续的过滤器处理了
     *
     * @return
     */
    @Override
    public int getOrder() {
        return FilterOrder.REQUEST_AUTH_FILTER;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        RequestParam requestParam = (RequestParam) exchange.getAttributes().get(CACHE_REQUEST_BODY_OBJECT_KEY);
        ServerHttpRequest req = exchange.getRequest();
        String originalPath = req.getURI().getPath();
        if (ObjectUtils.isNotEmpty(requestParam)&&StringUtil.isNotEmpty(requestParam.getMethod()) && isPass(requestParam.getMethod(), propertiesConfig.getUrlWhiteList())) {
            return chain.filter(exchange);
        }
        if(checkUtil.checkFilter(originalPath)){
            return chain.filter(exchange);
        }
        boolean isVerifyOk = false;//默认为false，勿改此默认值

        Throwable cause = null;

        //1.商户状态校验
        try {
            isVerifyOk = requestHelper.mchVerify(requestParam);
        } catch (Throwable e) {
            logger.error("商户状态不可用 RequestParam = {}", JsonUtil.toString(requestParam), e);
            throw e;
        }
        //2.签名校验
        try {
            isVerifyOk = requestHelper.signVerify(requestParam);
        } catch (Throwable e) {
            cause = e;
            logger.error("签名校验失败 RequestParam = {}", JsonUtil.toString(requestParam), e);
        }

        //3.如果校验失败，则进行日志打印、邮件通知等处理
        if (!isVerifyOk) {
            try {
                Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
                validFailService.afterSignValidFail(route.getId(), IPUtil.getIpAddr(exchange.getRequest()), requestParam, cause);
            } catch (Throwable e) {
                logger.error("验签失败，验签失败后处理器有异常 RequestParam = {}", JsonUtil.toString(requestParam), e);
            }
        }

        if (isVerifyOk) {
            return chain.filter(exchange);
        } else {
            //抛出异常，由全局异常处理器来处理响应信息
            throw GatewayException.SIGN_VALID_ERROR;
        }
    }
}
