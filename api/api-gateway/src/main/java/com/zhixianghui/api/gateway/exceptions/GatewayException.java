package com.zhixianghui.api.gateway.exceptions;

/**
 * @Description: 网关专用异常类
 * @author: chenyf
 * @Date: 2019/11/01
 */
public class GatewayException extends RuntimeException {
    protected String errorCode;
    private String errorMsg;

    public static GatewayException SIGN_VALID_ERROR = new GatewayException("HS100001", "验签失败");
    public static GatewayException PARAM_CHECK_ERROR = new GatewayException("HS100002", "报文参数错误");
    public static GatewayException UNKNOWN_RESULT = new GatewayException("HS100003", "未知结果");
    public static GatewayException RATE_LIMIT_ERROR = new GatewayException("HS100004", "被限流");
    public static GatewayException IP_BLACK_LIST = new GatewayException("HS100005", "IP被列出黑名单");
    public static GatewayException REQUEST_FORBID = new GatewayException("HS100006", "请求失限");
    public static GatewayException SERVICE_FALLBACK = new GatewayException("HS100007", "服务降级");
    public static GatewayException SERVICE_NOT_AVAILABLE = new GatewayException("HS100008", "服务不可用");
    public static GatewayException SERVICE_NOT_FOUND = new GatewayException("HS100009", "服务不存在");
    public static GatewayException MCH_VALID_ERROR = new GatewayException("HS100010", "商户验证失败");
    public static GatewayException REQUEST_DATA_INVALID = new GatewayException("HS100011", "请求数据有误");


    private GatewayException(String errorCode, String errorMsg) {
        super(errorCode + "," + errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }


    public GatewayException newWithErrMsg(String errorMsg) {
        return new GatewayException(this.errorCode, errorMsg);
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public String toMsg() {
        return "{errorCode:" + errorCode + ", errorMsg:" + errorMsg + "}";
    }
}
