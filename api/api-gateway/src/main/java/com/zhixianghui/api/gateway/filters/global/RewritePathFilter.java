package com.zhixianghui.api.gateway.filters.global;

import com.zhixianghui.api.gateway.config.conts.FilterOrder;
import com.zhixianghui.api.gateway.utils.CheckUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.api.gateway.config.conts.ReqCacheKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import com.zhixianghui.api.base.params.RequestParam;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;

/**
 * @description 重写请求的path路径
 * <AUTHOR>
 * @date 2019-02-23
 */
@Component
@Slf4j
public class RewritePathFilter extends AbstractGlobalFilter {

    public static Map<String,String> pathMap = new HashMap<>();
    static {
        pathMap.put("ckh.accountBalance","zxh.accountBalance");
        pathMap.put("ckh.preSign","zxhSign.preSign");
        pathMap.put("ckh.createSign","zxhSign.createSign");
        pathMap.put("ckh.signQuery","zxhSign.signQuery");
        pathMap.put("ckh.invoiceAccount","zxh.invoiceAccount");
    }

    @Autowired
    private CheckUtil checkUtil;
    /**
     * 设置当前过滤器的执行顺序：本过滤器在全局过滤器中的顺序建议为第5个，目前来说是在转发到后端服务之前的最后一个动作
     * @return
     */
    @Override
    public int getOrder() {
        return FilterOrder.REWRITE_PATH_FILTER;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        RequestParam requestParam = (RequestParam) exchange.getAttributes().get(CACHE_REQUEST_BODY_OBJECT_KEY);
        ServerHttpRequest req = exchange.getRequest();
        String originalPath = req.getURI().getPath();
        log.info("gateway-当前请求路径: {}", originalPath);
        if(checkUtil.checkFilter(originalPath)){
            return chain.filter(exchange);
        }
        // 方法参数解析到路径上
        String method = getMethod(requestParam.getMethod());
        String newPath = getPathFromMethod(method);
        ServerHttpRequest request = req.mutate()
                .path(newPath)
                .build();

        newPath = subPathEnd(newPath, "/", 0);
        URI uri = request.getURI();

        exchange.getAttributes().put(ReqCacheKey.GATEWAY_ORIGINAL_REQUEST_PATH_ATTR, originalPath);
        exchange.getAttributes().put(ReqCacheKey.GATEWAY_ORIGINAL_REQUEST_FULL_PATH_ATTR, originalPath + newPath);
        exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, uri);
        return chain.filter(exchange.mutate().request(request).build());
    }

    /**
     * 替换method
     * @param method
     * @return
     */
    private String getMethod(String method) {
        return pathMap.getOrDefault(method,method);
    }

    private String getPathFromMethod(String method){
        String path = "/";
        if(StringUtil.isNotEmpty(method)){
            path = method.replace(".", "/");
            if(! path.startsWith("/")){
                path = "/" + path;
            }
        }
        return path;
    }
}
