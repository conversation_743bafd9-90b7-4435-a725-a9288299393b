package com.zhixianghui.api.gateway.utils;

import com.zhixianghui.api.gateway.config.PropertiesConfig;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月15日 17:17:00
 */
@Component
public class CheckUtil {
    @Autowired
    private PropertiesConfig propertiesConfig;

    public boolean checkFilter(String path){
        List<String> filterUrls = propertiesConfig.getFilterUrls();
        if(!CollectionUtils.isEmpty(filterUrls)){
            for(String url:filterUrls){
                if(path.startsWith(url)){
                    return true;
                }
            }
        }
        return false;
    }
}