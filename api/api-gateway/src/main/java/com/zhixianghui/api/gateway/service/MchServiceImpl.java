package com.zhixianghui.api.gateway.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.zhixianghui.api.base.params.MerchantInfo;
import com.zhixianghui.api.base.service.MchService;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @description 获取商户信息的实现类，为提高性能，建议本地加入缓存
 * @author: chenyf
 * @Date: 2019-02-24
 */
@Component
public class MchServiceImpl implements MchService {
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private MerchantSecretFacade merchantSecretFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    private Cache<String, MerchantInfo> cache;//使用本地缓存，避免网络传输的开销

    public MchServiceImpl() {
        this.cache = CacheBuilder.newBuilder()
                .expireAfterWrite(60, TimeUnit.SECONDS)//过期时间
                .maximumSize(10000)
                .initialCapacity(50)
                .concurrencyLevel(10)
                .build();
    }

    /**
     * 根据商户编号获取商户信息
     *
     * @param mchNo
     * @return
     */
    @Override
    public MerchantInfo getMerchantInfo(String mchNo) {
        MerchantInfo merchantInfo = this.getFromBizService(mchNo);
//        if (merchantInfo == null) {
//            merchantInfo = this.getFromBizService(mchNo);
//            this.storeToCache(mchNo, merchantInfo);
//        }
        return merchantInfo;
    }


    private MerchantInfo getFromCache(String key) {
        if (cache != null) {
            return cache.getIfPresent(key);
        }
        return null;
    }

    private void storeToCache(String key, MerchantInfo merchantInfo) {
        if (cache != null && merchantInfo != null) {
            cache.put(key, merchantInfo);
        }
    }

    /**
     * @param mchNo
     * @return
     */
    private MerchantInfo getFromBizService(String mchNo) {
        MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(mchNo);
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        if (merchantSecret == null || merchant == null) {
            return null;
        }
        MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setMchNo(mchNo);
        merchantInfo.setMchName(merchant.getMchName());
        merchantInfo.setMchStatus(merchant.getMchStatus());
        merchantInfo.setSignType(merchantSecret.getSignType());
        //默认使用这个共享密钥作为签名、验签、加密、解密
        if (merchantSecret.getSignType() == SignTypeEnum.RSA.getValue()) {
            merchantInfo.setSignValidKey(merchantSecret.getMerchantPublicKey());//验签用商户公钥
            merchantInfo.setSignGenKey(merchantSecret.getPlatformPrivateKey());//签名用系统密钥
            merchantInfo.setSecKeyDecryptKey(merchantSecret.getPlatformPrivateKey());//解密用系统密钥
            merchantInfo.setSecKeyEncryptKey(merchantSecret.getMerchantPublicKey());//加密用商户公钥
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未支持的签名类型: " + merchantSecret.getSignType());
        }

        return merchantInfo;
    }
}
