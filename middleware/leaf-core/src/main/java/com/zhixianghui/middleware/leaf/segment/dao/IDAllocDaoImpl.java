package com.zhixianghui.middleware.leaf.segment.dao;

import com.zhixianghui.middleware.leaf.segment.model.LeafAlloc;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;

import javax.sql.DataSource;
import java.util.List;

public class IDAllocDaoImpl implements IDAllocDao {
    private String mapperNamespace;
    SqlSessionFactory sqlSessionFactory;

    public IDAllocDaoImpl(DataSource dataSource) {
        TransactionFactory transactionFactory = new JdbcTransactionFactory();
        Environment environment = new Environment("development", transactionFactory, dataSource);
        Configuration configuration = new Configuration(environment);
        configuration.addMapper(IDAllocMapper.class);
        sqlSessionFactory = new SqlSessionFactoryBuilder().build(configuration);
        //设置Mapper的命名空间
        mapperNamespace = getClass().getPackage().getName() + "." + IDAllocMapper.class.getSimpleName() + ".";
    }

    @Override
    public List<LeafAlloc> getAllLeafAllocs() {
        SqlSession sqlSession = sqlSessionFactory.openSession(false);
        try {
            return sqlSession.selectList(mapperNamespace + "getAllLeafAllocs");
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public LeafAlloc updateMaxIdAndGetLeafAlloc(String tag) {
        SqlSession sqlSession = sqlSessionFactory.openSession();
        try {
            sqlSession.update(mapperNamespace + "updateMaxId", tag);
            LeafAlloc result = sqlSession.selectOne(mapperNamespace + "getLeafAlloc", tag);
            sqlSession.commit();
            return result;
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public LeafAlloc updateMaxIdByCustomStepAndGetLeafAlloc(LeafAlloc leafAlloc) {
        SqlSession sqlSession = sqlSessionFactory.openSession();
        try {
            sqlSession.update(mapperNamespace + "updateMaxIdByCustomStep", leafAlloc);
            LeafAlloc result = sqlSession.selectOne(mapperNamespace + "getLeafAlloc", leafAlloc.getKey());
            sqlSession.commit();
            return result;
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public List<String> getAllTags() {
        SqlSession sqlSession = sqlSessionFactory.openSession(false);
        try {
            return sqlSession.selectList(mapperNamespace + "getAllTags");
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public int addBizTag(LeafAlloc leafAlloc){
        SqlSession sqlSession = sqlSessionFactory.openSession(false);
        try {
            int count = sqlSession.insert(mapperNamespace + "addBizTag", leafAlloc);
            sqlSession.commit();
            return count;
        } finally {
            sqlSession.close();
        }
    }
}
