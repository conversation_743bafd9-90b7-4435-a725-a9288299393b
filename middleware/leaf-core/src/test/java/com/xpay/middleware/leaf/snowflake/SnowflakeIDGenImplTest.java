package com.zhixianghui.middleware.leaf.snowflake;

import com.zhixianghui.middleware.leaf.IDGen;
import com.zhixianghui.middleware.leaf.common.PropertyFactory;
import com.zhixianghui.middleware.leaf.common.Result;
import com.zhixianghui.middleware.leaf.snowflake.SnowflakeIDGenImpl;
import org.junit.Test;

import java.util.Properties;

public class SnowflakeIDGenImplTest {
    @Test
    public void testGetId() {
        Properties properties = PropertyFactory.getProperties();

        IDGen idGen = new SnowflakeIDGenImpl(properties.getProperty("leaf.zk.list"), 8080, "leafName");
        for (int i = 1; i < 1000; ++i) {
            Result r = idGen.get("a");
            System.out.println(r);
        }
    }
}
