<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-middleware</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>

    <groupId>org.apache.rocketmq</groupId>
    <artifactId>rocketmq-spring-all</artifactId>
    <version>*******</version>
    <packaging>pom</packaging>

    <name>Apache RocketMQ Spring Boot ${project.version}</name>
    <description>Build for Apache RocketMQ Spring Boot Support</description>
    <url>https://github.com/apache/rocketmq-spring</url>


    <scm>
        <url>**************:apache/rocketmq-spring.git</url>
        <connection>scm:git:**************:apache/rocketmq-spring.git</connection>
        <developerConnection>scm:git:**************:apache/rocketmq-spring.git</developerConnection>
        <tag>HEAD</tag>
    </scm>

    <mailingLists>
        <mailingList>
            <name>Development List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
        </mailingList>
        <mailingList>
            <name>User List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
        </mailingList>
        <mailingList>
            <name>Commits List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
        </mailingList>
    </mailingLists>


    <organization>
        <name>Apache Software Foundation</name>
        <url>http://www.apache.org</url>
    </organization>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
            <comments>A business-friendly OSS license</comments>
        </license>
    </licenses>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.17</version>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <configuration>
                            <excludes>src/main/resources</excludes>
                            <configLocation>style/rmq_checkstyle.xml</configLocation>
                            <encoding>UTF-8</encoding>
                            <consoleOutput>true</consoleOutput>
                            <failsOnError>true</failsOnError>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.8</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <destFile>${project.build.directory}/jacoco.exec</destFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-prepare-agent-integration</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>prepare-agent-integration</goal>
                        </goals>
                        <configuration>
                            <destFile>${project.build.directory}/jacoco-it.exec</destFile>
                            <propertyName>failsafeArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report-integration</id>
                        <goals>
                            <goal>report-integration</goal>
                        </goals>
                    </execution>
                </executions>
           </plugin>
           <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.19.1</version>
                <configuration>
                    <skipAfterFailureCount>1</skipAfterFailureCount>
                    <forkCount>1</forkCount>
                    <reuseForks>true</reuseForks>
                    <excludes>
                        <exclude>**/IT*.java</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.4</version>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.0.2</version>
            </plugin>
           <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
                <version>0.12</version>
                <configuration>
                    <excludes>
                        <exclude>.gitignore</exclude>
                        <exclude>.travis.yml</exclude>
                        <exclude>CONTRIBUTING.md</exclude>
                        <exclude>**/README.md</exclude>
                        <exclude>**/README_zh_CN.md</exclude>
                        <exclude>.github/**</exclude>
                        <exclude>src/test/resources/certs/*</exclude>
                        <exclude>src/test/**/*.log</exclude>
                        <exclude>src/test/resources/META-INF/service/*</exclude>
                        <exclude>**/target/**</exclude>
                        <exclude>*/*.iml</exclude>
                        <exclude>**/*/spring.factories</exclude>
                        <exclude>**/application.properties</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.eluder.coveralls</groupId>
                <artifactId>coveralls-maven-plugin</artifactId>
                <version>4.3.0</version>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>sonar-apache</id>
            <properties>
                <!-- URL of the ASF SonarQube server -->
                <sonar.host.url>https://builds.apache.org/analysis</sonar.host.url>
            </properties>
        </profile>
    </profiles>

    <modules>
        <module>rocketmq-spring-boot-parent</module>
        <module>rocketmq-spring-boot</module>
        <module>rocketmq-spring-boot-starter</module>
        <!-- Note: The samples need to mvn compiple in its own directory
                <module>rocketmq-spring-boot-samples</module>
        -->
    </modules>
</project>
