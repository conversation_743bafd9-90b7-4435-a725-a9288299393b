<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<component name="CopyrightManager">
    <settings default="Apache">
        <module2copyright>
            <element module="All" copyright="Apache"/>
        </module2copyright>
        <LanguageOptions name="GSP">
            <option name="fileTypeOverride" value="3"/>
            <option name="prefixLines" value="false"/>
        </LanguageOptions>
        <LanguageOptions name="HTML">
            <option name="fileTypeOverride" value="3"/>
            <option name="prefixLines" value="false"/>
        </LanguageOptions>
        <LanguageOptions name="JAV<PERSON>">
            <option name="fileTypeOverride" value="3" />
            <option name="addBlankAfter" value="false" />
        </LanguageOptions>
        <LanguageOptions name="JSP">
            <option name="fileTypeOverride" value="3"/>
            <option name="prefixLines" value="false"/>
        </LanguageOptions>
        <LanguageOptions name="JSPX">
            <option name="fileTypeOverride" value="3"/>
            <option name="prefixLines" value="false"/>
        </LanguageOptions>
        <LanguageOptions name="MXML">
            <option name="fileTypeOverride" value="3"/>
            <option name="prefixLines" value="false"/>
        </LanguageOptions>
        <LanguageOptions name="Properties">
            <option name="fileTypeOverride" value="3"/>
            <option name="block" value="false"/>
        </LanguageOptions>
        <LanguageOptions name="SPI">
            <option name="fileTypeOverride" value="3"/>
            <option name="block" value="false"/>
        </LanguageOptions>
        <LanguageOptions name="XML">
            <option name="fileTypeOverride" value="3"/>
            <option name="prefixLines" value="false"/>
        </LanguageOptions>
        <LanguageOptions name="__TEMPLATE__">
            <option name="separateBefore" value="true"/>
            <option name="lenBefore" value="1"/>
        </LanguageOptions>
    </settings>
</component>