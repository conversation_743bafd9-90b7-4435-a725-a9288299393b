<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<code_scheme name="rocketmq">
    <option name="USE_SAME_INDENTS" value="true"/>
    <option name="IGNORE_SAME_INDENTS_FOR_LANGUAGES" value="true"/>
    <option name="OTHER_INDENT_OPTIONS">
        <value>
            <option name="INDENT_SIZE" value="4"/>
            <option name="CONTINUATION_INDENT_SIZE" value="4"/>
            <option name="TAB_SIZE" value="4"/>
            <option name="USE_TAB_CHARACTER" value="false"/>
            <option name="SMART_TABS" value="false"/>
            <option name="LABEL_INDENT_SIZE" value="0"/>
            <option name="LABEL_INDENT_ABSOLUTE" value="false"/>
            <option name="USE_RELATIVE_INDENTS" value="false"/>
        </value>
    </option>
    <option name="PREFER_LONGER_NAMES" value="false"/>
    <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1000"/>
    <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="1000"/>
    <option name="PACKAGES_TO_USE_IMPORT_ON_DEMAND">
        <value/>
    </option>
    <option name="IMPORT_LAYOUT_TABLE">
        <value>
            <package name="" withSubpackages="true" static="false"/>
            <emptyLine/>
            <package name="" withSubpackages="true" static="true"/>
        </value>
    </option>
    <option name="JD_ALIGN_PARAM_COMMENTS" value="false"/>
    <option name="JD_ALIGN_EXCEPTION_COMMENTS" value="false"/>
    <option name="JD_P_AT_EMPTY_LINES" value="false"/>
    <option name="JD_KEEP_INVALID_TAGS" value="false"/>
    <option name="JD_DO_NOT_WRAP_ONE_LINE_COMMENTS" value="true"/>
    <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false"/>
    <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1"/>
    <option name="KEEP_BLANK_LINES_IN_CODE" value="1"/>
    <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1"/>
    <option name="ELSE_ON_NEW_LINE" value="true"/>
    <option name="WHILE_ON_NEW_LINE" value="true"/>
    <option name="CATCH_ON_NEW_LINE" value="true"/>
    <option name="FINALLY_ON_NEW_LINE" value="true"/>
    <option name="ALIGN_MULTILINE_PARAMETERS" value="false"/>
    <option name="ALIGN_MULTILINE_FOR" value="false"/>
    <option name="SPACE_AFTER_TYPE_CAST" value="false"/>
    <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true"/>
    <option name="METHOD_PARAMETERS_WRAP" value="1"/>
    <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true"/>
    <option name="LABELED_STATEMENT_WRAP" value="1"/>
    <option name="WRAP_COMMENTS" value="true"/>
    <option name="METHOD_ANNOTATION_WRAP" value="1"/>
    <option name="CLASS_ANNOTATION_WRAP" value="1"/>
    <option name="FIELD_ANNOTATION_WRAP" value="1"/>
    <JavaCodeStyleSettings>
        <option name="CLASS_NAMES_IN_JAVADOC" value="3"/>
    </JavaCodeStyleSettings>
    <XML>
        <option name="XML_LEGACY_SETTINGS_IMPORTED" value="true"/>
    </XML>
    <ADDITIONAL_INDENT_OPTIONS fileType="haml">
        <option name="INDENT_SIZE" value="2"/>
    </ADDITIONAL_INDENT_OPTIONS>
    <codeStyleSettings language="Groovy">
        <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false"/>
        <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1"/>
        <option name="KEEP_BLANK_LINES_IN_CODE" value="1"/>
        <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1"/>
        <option name="ELSE_ON_NEW_LINE" value="true"/>
        <option name="CATCH_ON_NEW_LINE" value="true"/>
        <option name="FINALLY_ON_NEW_LINE" value="true"/>
        <option name="ALIGN_MULTILINE_PARAMETERS" value="false"/>
        <option name="ALIGN_MULTILINE_FOR" value="false"/>
        <option name="SPACE_AFTER_TYPE_CAST" value="false"/>
        <option name="METHOD_PARAMETERS_WRAP" value="1"/>
        <option name="METHOD_ANNOTATION_WRAP" value="1"/>
        <option name="CLASS_ANNOTATION_WRAP" value="1"/>
        <option name="FIELD_ANNOTATION_WRAP" value="1"/>
        <option name="PARENT_SETTINGS_INSTALLED" value="true"/>
        <indentOptions>
            <option name="CONTINUATION_INDENT_SIZE" value="4"/>
        </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="HOCON">
        <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1"/>
        <option name="PARENT_SETTINGS_INSTALLED" value="true"/>
    </codeStyleSettings>
    <codeStyleSettings language="JAVA">
        <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false"/>
        <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1"/>
        <option name="KEEP_BLANK_LINES_IN_CODE" value="1"/>
        <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1"/>
        <option name="ELSE_ON_NEW_LINE" value="true"/>
        <option name="WHILE_ON_NEW_LINE" value="true"/>
        <option name="CATCH_ON_NEW_LINE" value="true"/>
        <option name="FINALLY_ON_NEW_LINE" value="true"/>
        <option name="ALIGN_MULTILINE_PARAMETERS" value="false"/>
        <option name="ALIGN_MULTILINE_FOR" value="false"/>
        <option name="SPACE_AFTER_TYPE_CAST" value="false"/>
        <option name="SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE" value="true"/>
        <option name="METHOD_PARAMETERS_WRAP" value="1"/>
        <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true"/>
        <option name="LABELED_STATEMENT_WRAP" value="1"/>
        <option name="METHOD_ANNOTATION_WRAP" value="1"/>
        <option name="CLASS_ANNOTATION_WRAP" value="1"/>
        <option name="FIELD_ANNOTATION_WRAP" value="1"/>
        <option name="PARENT_SETTINGS_INSTALLED" value="true"/>
        <indentOptions>
            <option name="CONTINUATION_INDENT_SIZE" value="4"/>
        </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JSON">
        <option name="KEEP_BLANK_LINES_IN_CODE" value="1"/>
        <option name="PARENT_SETTINGS_INSTALLED" value="true"/>
    </codeStyleSettings>
    <codeStyleSettings language="Scala">
        <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1"/>
        <option name="KEEP_BLANK_LINES_IN_CODE" value="1"/>
        <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="1"/>
        <option name="ELSE_ON_NEW_LINE" value="true"/>
        <option name="WHILE_ON_NEW_LINE" value="true"/>
        <option name="CATCH_ON_NEW_LINE" value="true"/>
        <option name="FINALLY_ON_NEW_LINE" value="true"/>
        <option name="ALIGN_MULTILINE_PARAMETERS" value="false"/>
        <option name="ALIGN_MULTILINE_FOR" value="false"/>
        <option name="METHOD_PARAMETERS_WRAP" value="1"/>
        <option name="METHOD_ANNOTATION_WRAP" value="1"/>
        <option name="CLASS_ANNOTATION_WRAP" value="1"/>
        <option name="FIELD_ANNOTATION_WRAP" value="1"/>
        <option name="PARENT_SETTINGS_INSTALLED" value="true"/>
        <indentOptions>
            <option name="INDENT_SIZE" value="4"/>
            <option name="CONTINUATION_INDENT_SIZE" value="4"/>
            <option name="TAB_SIZE" value="4"/>
        </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="XML">
        <indentOptions>
            <option name="CONTINUATION_INDENT_SIZE" value="4"/>
        </indentOptions>
    </codeStyleSettings>
</code_scheme>