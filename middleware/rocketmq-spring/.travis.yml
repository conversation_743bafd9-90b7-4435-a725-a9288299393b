dist: trusty

notifications:
  email:
    recipients:
      - <EMAIL>
  on_success: change
  on_failure: always

language: java

jdk:
  - oraclejdk8

matrix:
  include:
  # On OSX, run with default JDK only.
  # - os: osx
  # On Linux, run with specific JDKs only.
  - os: linux
    env: CUSTOM_JDK="oraclejdk8"

before_install:
  - echo 'MAVEN_OPTS="$MAVEN_OPTS -Xmx1024m -XX:MaxPermSize=512m -XX:+BytecodeVerificationLocal"' >> ~/.mavenrc
  - cat ~/.mavenrc
  - if [ "$TRAVIS_OS_NAME" == "osx" ]; then export JAVA_HOME=$(/usr/libexec/java_home); fi
  - if [ "$TRAVIS_OS_NAME" == "linux" ]; then jdk_switcher use "$CUSTOM_JDK"; fi

script:
  - travis_retry mvn -B clean apache-rat:check
  - travis_retry mvn -B package jacoco:report coveralls:report

after_success:
  # - mvn clean install -Pit-test
  - mvn sonar:sonar -Psonar-apache
