package com.zhixianghui.sdktest;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.ds.simple.SimpleDataSource;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.sdk.api.entity.Request;
import com.zhixianghui.sdk.api.entity.Response;
import com.zhixianghui.sdk.api.entity.SecretKey;
import com.zhixianghui.sdk.api.enums.ChannelType;
import com.zhixianghui.sdk.api.enums.SignType;
import com.zhixianghui.sdk.api.utils.*;

import javax.sql.DataSource;
import java.io.File;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class ApiTestor {

    /**
     * 如需切换环境和商户，请先检查配置枚举是否已经配置，未配置则先配置，然后在此处指定环境与商户
     */
    private static ApiConfig config = ApiConfig.PROD_M00000136;
    /**
     * 配置文件由默认供应商配置，如有切换供应商需求，可修改配置枚举或者直接在此处修改
     */
    private static String MAINSTAY_NO = config.getMainstay();
    private static SecretKey buildSecretKey() {
        SecretKey secretKey = new SecretKey();
        secretKey.setSecKeyDecryptKey(config.getMchPrivateKey());
        secretKey.setSecKeyEncryptKey(config.getPlatPublicKey());
        secretKey.setReqSignKey(config.getMchPrivateKey());
        secretKey.setRespVerifyKey(config.getPlatPublicKey());
        return secretKey;
    }


    public static void main(String[] args) {
//        SecretKey secretKey = buildSecretKey();
//        System.out.println(RSAUtil.encryptByPublicKey("ABCDEFGHIJKLMNOP", secretKey.getSecKeyEncryptKey()));
//        syncTradeData();
//        queryOrderDetail();
//        accountBalance();
        confirmSyncTradeData();
//        preSign();
//        createSign();
//        signQuery();

//        invoiceRecordListQuery();
//        invoiceRecordInfoQuery();
//        singleGrant();

//        syncDataFeeFix();
//        uploadPic();
//        cheackMchSignAndPicUpload();
//        testSign();
//
//        Map<Integer, String> secret = new HashMap<>();
//        secret.put(100, "hjzx66688866666@");
//        secret.put(101, "hjzx12345688888@");
//        secret.put(102, "hjzx99999666666@");
//        Set<String> phoneList = initPhone();
//        // 读取excel
//        final List<List<Object>> lists = ExcelUtil.getReader("/Users/<USER>/Downloads/S000039.xlsx").read(1);
//        lists.forEach(it -> {
//            final String id = Convert.toStr(it.get(0));
//            final int keyId = Convert.toInt(it.get(39));
//            String phone = phoneList.iterator().next();
//            String phoneAes = AESUtil.encryptECB(phone, secret.get(keyId));
//            String md5 = MD5Util.getMixMd5Str(phoneAes);
//            System.out.println("update tbl_order_item set version = 100, ENCRYPT_KEY_ID = "
//                    + keyId + ", RECEIVE_PHONE_NO = '" + phoneAes + "', RECEIVE_PHONE_NO_MD5 = '" + md5 + "' where id = " + id + ";");
//        });
    }

    public static void confirmSyncTradeData() {

        // keys SyncTradeData:M00*
        // SyncTradeDataBackUp*

        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("employer_no", "M00000592");
        param.put("mainstay_no", "S000065");

//        param.put("employer_no", config.getEmployerNo());
//        param.put("mainstay_no", config.getMainstay());

        Request request = new Request();
        request.setData(param);
        request.setMethod("zxh.confirmSyncTradeData");
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());
        request.setVersion("1.0");
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());

        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSON.toJSONString(response));
    }

    private static Set<String> initPhone() {
        Set<String> set = new HashSet<>();
        while (set.size() < 3774) {
            set.add(generatePhoneNumber());
        }
        return set;
    }

    private static String generatePhoneNumber() {
        String[] start = {"130", "131", "132", "133", "134", "150", "151", "155", "158", "166", "180", "181", "184", "185", "188"};
        return start[(int) (Math.random() * start.length)]+(10000000+(int)(Math.random()*(99999999-10000000+1)));
    }

    public static void invoiceRecordInfoQuery() {
        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("trx_no", "70000000430X");
//        param.put("mainstayNo", MAINSTAY);


        Request request = new Request();
        request.setData(param);
        request.setMethod("ckh.invoiceRecordInfoQuery");
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());
        request.setVersion("1.0");
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());
        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSONUtil.toJsonPrettyStr(response));
        final String secKey = response.getSec_key();
        final cn.hutool.json.JSONObject data = JSONUtil.parseObj(response.getData());
        final JSONArray items = data.getJSONArray("invoice_items");
        for (Object item : items) {
            final String idCardNo = ((cn.hutool.json.JSONObject) item).getStr("receive_name");
            System.out.println(AESUtil.decryptECB(idCardNo, secKey));
        }
    }

    public static void invoiceRecordListQuery() {
        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("currentPage", 1);
//        param.put("mainstayNo", MAINSTAY);


        Request request = new Request();
        request.setData(param);
        request.setMethod("ckh.invoiceRecordListQuery");
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());
        request.setVersion("1.0");
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());
        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSONUtil.toJsonPrettyStr(response));
    }

    /**
     * 查询余额接口示例
     */
    public static void accountBalance() {

        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("mainstay_no", MAINSTAY_NO);

        Request request = new Request();
        request.setData(param);
        request.setMethod("zxh.accountBalance");
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());
        request.setVersion("1.0");
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());

        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSON.toJSONString(response));


    }

    public static void syncDataFeeFix() {

        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("employer_no", "M00000173");
        param.put("order_item_status", 100);
        param.put("mainstay_no", "S000014");
        param.put("plat_trx_no", "E20231116003137673");
        param.put("create_begin_date", "2023-11-16 00:00:00");
        param.put("create_end_date", "2023-11-16 23:59:59");

        Request request = new Request();
        request.setData(param);
        request.setMethod("zxh.syncDataFeeFix");
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());
        request.setVersion("1.0");
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());

        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSON.toJSONString(response));


    }

    /**
     * 实时发放接口（单笔）
     */
    public static void singleGrant() {
        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("mch_order_no", RandomUtil.get16LenStr() + System.currentTimeMillis()); // 商户订单号
        param.put("mainstay_no", MAINSTAY_NO); // 代征主体编号
        param.put("channel_type", ChannelType.ALIPAY.getCode()); // 发放类型
        param.put("receive_name", AESUtil.encryptECB("韦胜宝", config.getSecKey())); //收款人真实姓名
        param.put("receive_id_card_no", AESUtil.encryptECB("******************", config.getSecKey())); //收款人身份证号码
        param.put("receive_account_no", AESUtil.encryptECB("****************", config.getSecKey())); //收款人账号
        param.put("receive_phone_no", AESUtil.encryptECB("***********",config.getSecKey())); //收款人手机号，如果为银行卡发放，必须为银行预留手机号，如果其他发放类型，必须为与身份证对应的实名手机号
        param.put("order_item_net_amount", "0.1"); //收款金额
        param.put("remark", "发放测试"); //备注
        param.put("callback_url", "https://notify-dev.hjzxh.com/notifyMerchantReceive/testReceive"); //回调地址

        Request request = new Request();
        request.setMethod("zxh.singleGrant");
        request.setVersion("1.0");
        request.setData(param);
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());

        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSON.toJSONString(response));
    }

    public static void syncTradeData() {

        int pageSize = 500;
        SecretKey secretKey = buildSecretKey();
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (int i = 0; i < 100000; i++) {
            Map<String, Object> param = new HashMap<>();
            param.put("mch_order_no", RandomUtil.get16LenStr() + System.currentTimeMillis()); // 商户订单号
            param.put("mainstay_no", MAINSTAY_NO); // 代征主体编号
            param.put("channel_type", ChannelType.BANK_CARD.getCode()); // 发放类型
            param.put("receive_name", AESUtil.encryptECB("小梅", config.getSecKey())); //收款人真实姓名
            param.put("receive_id_card_no", AESUtil.encryptECB("361181198506104636", config.getSecKey())); //收款人身份证号码
            param.put("receive_account_no", AESUtil.encryptECB("6230580000048295914", config.getSecKey())); //收款人账号
            param.put("receive_phone_no", AESUtil.encryptECB("***********",config.getSecKey())); //收款人手机号，如果为银行卡发放，必须为银行预留手机号，如果其他发放类型，必须为与身份证对应的实名手机号
            param.put("order_item_net_amount", "100"); //收款金额
            param.put("remark", "发放测试"); //备注
            param.put("complete_time", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss")); //完成时间
            mapList.add(param);
        }

//        param.put("callback_url", "https://notify-dev.hjzxh.com/notifyMerchantReceive/testReceive"); //回调地址
        int totalPage = (mapList.size() / pageSize) ;
        int a = (mapList.size() % pageSize) ;
        int curPage = 0;
        String batchNo = RandomUtil.get16LenStr();
        do {
            List<Map<String, Object>> maps = null;
            if (curPage == totalPage) {
                maps = mapList.subList(curPage * pageSize, mapList.size());
            }else {
                maps = mapList.subList(curPage * pageSize, curPage * pageSize + pageSize);
            }

            if (maps == null || maps.isEmpty()) {
                break;
            }
            if (a == 0 && curPage == totalPage - 1) {
                totalPage = totalPage - 1;
            }

            Map<String, Object> reqParam = new HashMap<>();
            reqParam.put("batch_no", batchNo);
            reqParam.put("mainstay_no", MAINSTAY_NO);
            reqParam.put("channel_type", 1);
            reqParam.put("has_next", curPage == totalPage ? 0 : 1);
            reqParam.put("items", maps);

            Request request = new Request();
            request.setMethod("zxh.syncTradeData");
            request.setVersion("1.0");
            request.setData(reqParam);
            request.setRand_str(RandomUtil.get32LenStr());
            request.setSign_type(SignType.RSA.getValue());
            request.setMch_no(config.getEmployerNo());
            request.setSec_key(config.getSecKey());

            final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
            System.out.println(JSON.toJSONString(response));

            curPage++;
        } while (curPage <= totalPage);

    }

    /**
     * 查询订单详情接口示例
     */
    private static void queryOrderDetail() {

        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("mch_order_no", "E202109151841484");

        Request request = new Request();
        request.setData(param);
        request.setMethod("zxh.queryDetail");
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());
        request.setVersion("1.0");
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());

        final ExecutorService executorService = Executors.newFixedThreadPool(100);

        for (int i = 0; i < 10; i++) {
            executorService.submit(() -> {
                for (int j = 0; j < 10000; j++) {
                    final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);

                    String data = response.getData();
                    JSONObject dataJson = JSON.parseObject(data);

                    /**
                     * 将收款人信息从解密出明文
                     */
                    final String receive_name = dataJson.getString("receive_name");
                    final String receiveName = AESUtil.decryptECB(receive_name, response.getSec_key());
                    final String receive_id_card_no = dataJson.getString("receive_id_card_no");
                    final String receiveIdCardNo = AESUtil.decryptECB(receive_id_card_no, response.getSec_key());
                    final String receive_account_no = dataJson.getString("receive_account_no");
                    final String receiveAccountNo = AESUtil.decryptECB(receive_account_no, response.getSec_key());
                    final String receive_phone_no = dataJson.getString("receive_phone_no");
                    final String receivePhoneNo = AESUtil.decryptECB(receive_phone_no, response.getSec_key());
                    System.out.println(receiveName);
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }

            });
        }


    }

    public static void preSign() {
//        final String pic = Base64.encode(new File("C:\\Users\\<USER>\\Pictures\\logo.png"));
//        System.out.println(pic);
        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("mainstay_no", MAINSTAY_NO); // 代征主体编号
        param.put("name", AESUtil.encryptECB("占铖", config.getSecKey())); //真实姓名
        param.put("id_card_no", AESUtil.encryptECB("******************", config.getSecKey())); //身份证号码
        param.put("phone_no", AESUtil.encryptECB("13897564362", config.getSecKey())); //手机号

        Request request = new Request();
        request.setMethod("zxhSign.preSign");
        request.setVersion("1.0");
        request.setData(param);
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());
        request.setIdCardFrontPhoto("");
        request.setIdCardBackPhoto("");
        request.setCerFacePhoto("");

        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSONUtil.toJsonPrettyStr(response));
    }

    public static void createSign() {

        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("user_id", "da60e60450c78e06bd77045b826819a1");
        param.put("sign_type", "104");
        param.put("callback_url", "https://notify-dev.hjzxh.com/notifyMerchantReceive/testReceive");

        Request request = new Request();
        request.setMethod("zxhSign.createSign");
        request.setVersion("1.0");
        request.setData(param);
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());

        long time = System.currentTimeMillis();
        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(System.currentTimeMillis()-time);
        System.out.println(JSON.toJSONString(response));
    }

    public static void signQuery() {

        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("user_id", "2b88e8b4e7729698bebd5ad5655f62de");

        Request request = new Request();
        request.setMethod("zxhSign.signQuery");
        request.setVersion("1.0");
        request.setData(param);
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());

        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSON.toJSONString(response));
    }

    public static void uploadPic() {
        SecretKey secretKey = buildSecretKey();
//        List<List<Object>> lists = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\灵活用工分账方名单.xls").read(1);
        List<List<Object>> lists = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\签约和图片上传失败的商户.xlsx").read(1);
        lists.forEach(item->{
            String sub_merchant_no = String.valueOf(item.get(0));
//            String status = String.valueOf(item.get(7));
            if (StrUtil.isNotBlank(sub_merchant_no)
//                    && StrUtil.equals("已激活", status)
            ) {
                System.out.println("开始处理 "+sub_merchant_no);
                Map<String, Object> param = new HashMap<>();
                param.put("sub_merchant_no", sub_merchant_no);

                Request request = new Request();
                request.setMethod("zxhSign.uploadPic");
                request.setVersion("1.0");
                request.setData(param);
                request.setRand_str(RandomUtil.get32LenStr());
                request.setSign_type(SignType.RSA.getValue());
                request.setMch_no(config.getEmployerNo());
                request.setSec_key(config.getSecKey());

                final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
                System.out.println(JSON.toJSONString(response));
                System.out.println("完成处理 "+sub_merchant_no);

                try {
                    TimeUnit.SECONDS.sleep(1L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }

        });
    }

    public static void cheackMchSignAndPicUpload() {
        DataSource ds = new SimpleDataSource("****************************************************************************************************************************************************", "hjzx", "ZIzUQrA&bk1I");
        Db db = DbUtil.use(ds);
        SecretKey secretKey = buildSecretKey();
//        List<List<Object>> lists = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\灵活用工分账方名单.xls").read(1);
        List<List<Object>> lists = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\签约和图片上传失败商户.xlsx").read(1);
        lists.forEach(item->{
            String sub_merchant_no = String.valueOf(item.get(0));
//            String status = String.valueOf(item.get(7));
//            String mstname = String.valueOf(item.get(3));
//                System.out.println("开始处理 "+sub_merchant_no);
                Map<String, Object> param = new HashMap<>();
                param.put("sub_merchant_no", sub_merchant_no);

                Request request = new Request();
                request.setMethod("zxhSign.cheackMchSignAndPicUploadApi");
                request.setVersion("1.0");
                request.setData(param);
                request.setRand_str(RandomUtil.get32LenStr());
                request.setSign_type(SignType.RSA.getValue());
                request.setMch_no(config.getEmployerNo());
                request.setSec_key(config.getSecKey());

                final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
                if (response.getData() != null) {
                    cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(response.getData());
                    if (jsonObject.getJSONObject("employerInfo") == null) {
//                        System.out.println(sub_merchant_no + "," + jsonObject.getStr("biz_err_msg"));
                    }else {
                        String empNo = jsonObject.getJSONObject("employerInfo").getStr("employerNo");

                        try {
                            Entity entity = db.queryOne("select * from tbl_merchant where mch_no='" + empNo+"'");
                            if (entity.getInt("MCH_STATUS") == 100) {
                                String empName = jsonObject.getJSONObject("employerInfo").getStr("employerName");
                                String mstName = jsonObject.getJSONObject("employerInfo").getStr("mainstayName");
                                String signStatus = jsonObject.getJSONObject("altMchSignInfo").getStr("altMchSignStatus");
                                String errorMsg = jsonObject.getJSONObject("altMchSignInfo").getStr("errorMsg");
                                String approveNote = jsonObject.getJSONObject("altMchPicUploadInfo").getStr("approveNote");
                                String approveStatusDesc = jsonObject.getJSONObject("altMchPicUploadInfo").getStr("approveStatusDesc");
                                String s = "\""+sub_merchant_no+"\"," + empNo + "," + empName + "," + mstName + "," + (StrUtil.equals("100", signStatus) ? "签约成功" : "签约失败：" + errorMsg) + "," + approveStatusDesc + "," + approveNote;
                                if (!s.contains("签约成功,审批通过") && !StrUtil.containsAny("高倾","协众","中孚")) {
                                    System.out.println(s);
                                }
                            }
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }else {
                    System.out.println("\""+sub_merchant_no+"\","+response.getResp_code());
                }


//                try {
//                    TimeUnit.SECONDS.sleep(1L);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }


        });
    }

    public static void testSign(){
        String aa = RSAUtil.sign("aaaass", "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBANQ/U23/6TX5DIeBY6RcjyCHxA2ynxGz3EqmALvmTyJYtLSihs89pCcpCtgZtyeA3JPdLiLVifRWpkVKIqfUQz/KG+XE7Ya9o+zaYRYqsLPJglE8ssWGDwmEMtlVicrLyxyyh+AID2kig0uOeUUYcYnr49sWux5g7woPkiWwB0AzAgMBAAECgYEAr++yQ6tvcIr6ZNTPeO40wl1oGYaoLu9tuv8x+o+lpy3q1RUoNF+/U6rEz285kGhT78nXNY7W5RBykVsrsPzVpGZ5Uyph1CvO8KvxVe7zVK9bZMTIr3xmPAF3Npi1W9s4f5UFje+YK8Ot6jDx1GECU8JdjuF105M6fkJKLwqlZMECQQD6m36sjbVKNFNy9D0PWJuRH2uyBXCBlkDW2MZGX0p9wQmZz8XVyWBKQKstKTCO5p9D2cNmLTPOwK2fLQdxRsiTAkEA2NCFAsI1xQ772J3TDNCKP0aDbdvWWSsxUlhpIsxJ5JpYUtwehr9HFPUMFQwtky0W69W91rFqG0mWQGSCOUiN4QJBALy5oPm9dgs9wKJPQDRwXCc77FSSOBm+13F0qkyQsn5lgx9sEuZe0r7YWJCX9lOHZeUo9zfZTqyi+Z1nwbUSwzUCQQDWkyfQXxyP9hBRhYDGAe2QSdzw8MzmWtTpRg5nHf9Te11GRAsCGHPkzdAzVGYWbl1s8ZB71gHJF77X46DtyFyBAkBNRC3MMrDWTIyo2YUQNLwntJiXhWOjaMCiLUZQQ0vCEn4qjRl5fDC3KADfJ8MjzPz0eYVT4n+dWOMN0bMizPNv");
        System.out.println(RSAUtil.verify("aaaass", "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDUP1Nt/+k1+QyHgWOkXI8gh8QNsp8Rs9xKpgC75k8iWLS0oobPPaQnKQrYGbcngNyT3S4i1Yn0VqZFSiKn1EM/yhvlxO2GvaPs2mEWKrCzyYJRPLLFhg8JhDLZVYnKy8scsofgCA9pIoNLjnlFGHGJ6+PbFrseYO8KD5IlsAdAMwIDAQAB", aa));
        System.out.println(aa);
    }

    public static void getReceipt(){
        SecretKey secretKey = buildSecretKey();

        Map<String, Object> param = new HashMap<>();
        param.put("email", "<EMAIL>");
        param.put("remitPlatTrxNo", "R20230116001404587");
        param.put("channelNo", "<EMAIL>");

        Request request = new Request();
        request.setMethod("weChat.getReceipt");
        request.setVersion("1.0");
        request.setData(param);
        request.setRand_str(RandomUtil.get32LenStr());
        request.setSign_type(SignType.RSA.getValue());
        request.setMch_no(config.getEmployerNo());
        request.setSec_key(config.getSecKey());

        final Response response = RequestUtil.doRequest(config.getHost(), request, secretKey);
        System.out.println(JSON.toJSONString(response));
    }
}
