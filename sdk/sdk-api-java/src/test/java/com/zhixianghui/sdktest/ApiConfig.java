package com.zhixianghui.sdktest;

public enum ApiConfig {
    PROD_M00000136("M00000136","S000001","https://api.hjzxh.com/trade","1111111111111111","MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCH4zbDhSFu8AJWQKFuWHomfhlhiVtyX+F+KdfRLJad+znnyXEMLJDZlMn9ORQNXBnuiBrsLOpxnzRFMMevPdHpqxwGz2elFuDc9UXWak1LlMvxUe46o745IrGO9tbQy2xwgoksbVb4j+SHEbPCRcYEerWmYwq8FUCyMlZ4FvujCQIDAQAB","MIICXAIBAAKBgQCYBBtYrFMsEJrXeNea78GElAvftjIl8lZSVYtEU/HlWRfar7KpeL9kX8TGcTQAkMGbohFxezfnNUzuFwlKPs0mELAQsWuK35cyWRT7NRKmeiaR9vlXZqynPz+XRxUNVb+1vGQutOi+qJNToLg0DNO+XwPtISlMfvSHm9+fJpWTTQIDAQABAoGAChejtSRjtTY5AXstXUTzpRirHSQcejZ3kqnJQDUOhzp+Ae/OPcAdfM164+3aFPuHTwzYeZ9Dm98HB3uSC1LWDclirSPEgQ7cBKUdWeAccIpAknIRCaLJwt5TAXgufREXfE87Nn4vtiUtTyKW6iTAiCNQ9Cym54BTmLg7YB8UAmECQQDm8xe1kbhUDf2D6gl9gIWLydsAvKmgzycc0kRWN0BKop2HhFhWJs6WSUoMfem+9CgyQcwF8AdjHt0LK3v6AI/FAkEAqIE42s4u1si4gI9wP5VinINjbzyFEWN+IQW5XXtpKa0psqMiPxdZg5UnalUxyevFuV+bc6ayplTPd1bcIZJl6QJBAN7ZFrmnIJxK26NcQ0gMqdBXOLGfWnbHLNCMvFANBEaAX00SkUKgM5ukoIUpQlF1uwuznbDlCdvFPjm2/ewgepkCQGhzMXrwM7iP005dm4bOYsIB77EUIqgUtCpkEfMQfK+I10AUPzY8kxAtdLFE/gfcTkQKL6IQwsy35aJkBTAKP4ECQGH7BTMvZlEOF/eZdLHRKKzsE/46Z040O9nsgNeOJPem1pYsBcqe46IfOoWmStUyjwHvxXvM06bXTTSTcjF6E0c="),
    TEST_*********("*********","S000003","https://api-dev.hjzxh.com/trade","1111111111111111","MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCPBB2K2ZA4k3FSiA/vmNEpnnOSz54Yb2oTmUMqww8SbBfJO/B4tyEpDzdDoTsIHjizML0aMmLhVPdB61/UiRKFPKHAdVPEqBIHeER7pagNdblw3c9RHSLx9HyFbfrGu3GK3jz6MWyQ2ntKM6r7WG62GUGiFR+XU7YwlGs+2pKWnwIDAQAB","MIICWwIBAAKBgQCJ/9kExN7WYJCGOKjrSIhxSUuFBkS4/B93dS8ctpnMHbmy775i24dqw/cuth4LYwVr36nOk0h+2SfNgDgFPpuT9iSj5prei6DZ5IoHu3Hb4ANfaD1bJ3l1slPXz8NGuY/JIK2ueeTz7+nXYbGoNY6rwvyNBZepoOaTU06Z10CwVwIDAQABAoGAeyGaFjNDUa0fx2NO9YDmnY50n+ET+KqduQ0KWGhlmkb7prlb+wE+Nvlb2IYPEMHt2G7dKMEp8mT4qtg5JBTU40KpQxSMPA+Q+XH1OLXp6FlC0uAvNkpd3yM2OW8gU2pfi9Kg9OGkcJDAlAm4C1hbLyY10jghF4+DwxdaDgbQVSECQQDVPoXHr4ljmAMKXYHA2k78dqWoIVxoiQB5xisOKJwRTI+OV6C0xEGhMndOmq545D5P0d7gl+OAkWnCHApV2GRzAkEApashpnGFMPF9+ABz7ag2GxCNHjlryGUPYxUxc8IOaIIHZEuMjyJVOpc0CmvU1q07RgAbpxPF+8oKOb2CCQ7vjQJAM/08TITlcBydkSde/Q+8dhzPIoCPsQTF0uqXkKoh8q3ByihfC/NVNOHC6K0ZoGu1LWyzc/pUAwJw0FxZwLgzNQJAXO8vJUuxR89wttqqLbua/nu+biIQEI6AnziYUTOfGYk31E99Ph4oaYHAs8fh0bvD8HTHDmbNPX4NVm60r5gtUQJAJdPhxU8X0HE+qBFnH7SRt0XszFYyNtbL7acz7JzrNmqRmFL2CvpcpO0EIMXTLon3T7YCuO2MJt0KL6T+kNoBLw==")   ,
    TEST_M00000421("M00000421","S000003","https://api-dev.hjzxh.com/trade","ABCDEFGHIJKLMNOP",
            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCmlvupij5/FnXeLRyIKhHVwhCRVkf1rvmfKRX1yKZ5mFt7sCNvuq0uLpBwZQOokgAEfZ3xEpq0acj9mXjdu4+69DXWk3a+w2rSOEb7qBi3WMpz5J68z/QBqqlwKXtPZJgECAHdbqDQ3sVz4hUtN6Xbaj415AmVYEaKolnb3cmdCwIDAQAB",
            "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAJM9L30CJeS9QOoZWIooa7Ov1g5oKP2DSnWQcpvExE/S3JYHYqByBrVFsy9+UofOARHQmKUuMSvpTZdkIhC17gg/mqHwGavbNpE9lVwoa2F9Uriq1oUKnNTCEcAXIXIZ+bUt/qd0UTjLA6MjK6jphDX2WjPLml66Gm14Anu2iQRbAgMBAAECgYEAi+fqtM0StqwB/69/yx+nX0Eok93zfRiA6v8Ld+nbY8IIKmLwG2bd6udE4U8WGsbri0L7mJEw1fnE6aKTH+/PMf8jQoJjmyq7YhRbLwjdGWq5PMtA4AGX0ZK1r7yBP39o+XMeX/bq98Dhe1eQNmWOlhrmqpHYatvbpBRZswdmGfkCQQDlfyR1acVDtvFMfBY2TeCfsqM9BlHFTbSFe29L7TygmgYmEp0z7JiIxTaoS/tQ5fE3lAiQdYjpRm4FTVAojQIfAkEApD4r4gCnUa4qV2ojQvsW8beEm2iyw+XPuM9qIwnEguFiWGLRG7+eY4/dTvNFXlqlJg3P7vGX+2pRgrEgpd5ORQJAR7TDMoB651545Jn84rjQj3VkdcPMtuZBmldn/gRBJjkZ5Ll6Lugk/M8J9enPu4YtKV1yk5h1z9V2uOdgPVtZpwJBAJh6B29XP/56fGCO7pF+XXyl7PwCJQPs0/00wcophUAkUZLQmmTybe7sXn6vJhVEfdFoPUQNEhWcTHPSUQbr7SkCQQDh5N5f+fDShNcoJ50VGUpIsUD4lAf4GKuYPKiMsVni4vV1bTJMUWWZJKx8qvIEbfo9i7ZTiWlndryoTr8oZO/p")    ,
    TEST_*********("*********","S000060","http://10.10.10.104:19099/trade","ABCDEFGHIJKLMNOP","MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHBE+6kcxgnvylT0DiIMRL9uQMgq2mk/oxbPB+mnN8Va3NzIZ32dhxgz462cVvXvGfL6i/VNOxLfMcStJEakDWpyX/xjJQ0vzxrsjYdLi8SnWq8PpD/E8RhSP3hGzAkbpzTlSYdLERDVSjPH+GZolfLw3fbndB9653zaBWP8qiYQIDAQAB","MIICWwIBAAKBgQCZY88n0cqY3bCtz6DUahccGpP3s2aeek5wIUZ6kxP2JjyEJP0t3+OdnFgE4wZtPkJvc0dNxHSk1DIzabVyTkChPML6WyZHCr8KPCCOOL/msjO5D52bbON01LIKV0CvnCMXO12cDk68M3mf8JS2E8FXGioI34bfmYqxSoToMCOSlQIDAQABAoGAGQK7PmTowfAz5MNcGAaMahqzUcpAy0pqC8KBOW9N6+7kIy/c0GQXOxXJDFLwM1PZhU6oq7eCxXWMEfIEkfTMeLuvoiHr8+wRUttx5Xl5VcszoqEW6K6vnGLBSlr9vqBQjPTUjI+8qo8LyA7j2C0vZkM3294HTgnxgySb8ZIy1CkCQQCefEW/d+9bOkOxOJEsT7JY1JaVSKvB81nPdLzsGTtn88RnDQTJH+ZPxUBjAPXBkCdvhim5r3EbFh+DhkM3OATbAkEA98TpwSWmaAzf6wzDhRy1mx9FPtWvL7LAQ6FRiEhtZhUJZLBiRX0qm1j59ge5NItGsq/Gqrl7+b8k6mymEikpTwJAQtGkWoCHgxACoEJ4OpVzXS5sOo6EE+a009mlGWnd+Qr2oHgFr4JqiPFiHxu1gaF0bvnCt833QvNF4B8IUcylhwJAJ6ajof1cfBpTW1JXl93YUlLWyg94UbHRgwE2AxFUMJn9PtfbYjN9tg6vMehag0YmskHfEUNNy9IDHmD00sMzkQJAS5s2kUqdAcBj8/G+uKjulG/ObkO4BNs/9w+Pccnm9zpa67BQ1QuqDOjTpzwQUzoCksCPjeTlvcQ6TdRDprlwvA==");


    private String employerNo;
    private String mainstay ;
    private String host;
    private String secKey;
    private String platPublicKey;
    private String mchPrivateKey;

    ApiConfig(String employerNo, String mainstay, String host, String secKey, String platPublicKey, String mchPrivateKey) {
        this.employerNo = employerNo;
        this.mainstay = mainstay;
        this.host = host;
        this.secKey = secKey;
        this.platPublicKey = platPublicKey;
        this.mchPrivateKey = mchPrivateKey;
    }

    public String getEmployerNo() {
        return employerNo;
    }

    public String getMainstay() {
        return mainstay;
    }

    public String getHost() {
        return host;
    }

    public String getSecKey() {
        return secKey;
    }

    public String getPlatPublicKey() {
        return platPublicKey;
    }

    public String getMchPrivateKey() {
        return mchPrivateKey;
    }
}
