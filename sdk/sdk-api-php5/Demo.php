<?php
/**
 * 测试样例类
 *
 * PHP VERSION = PHP 5.6
 */
require "Autoload.php";

use zhi<PERSON><PERSON><PERSON>\Request;
use zhixia<PERSON><PERSON>\SecretKey;
use zhi<PERSON><PERSON><PERSON>\RequestUtil;
use zhixia<PERSON><PERSON>\RandomUtil;
use zhixia<PERSON><PERSON>\AESUtil;

//平台公钥
$platPublicKey = "-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCM3URE1UGDxtQw3iuUnraEwVytyWDwjSt/dd0Pbev8ax6MEzozwbyKXIIoFTowpTG1PMjmXlYFVb/zDxbj5mwQAg/2CTj7Z6sbjOWNAV2JTROuomrxag4dg7rXZu1WWkGrMuZbpyNLzcFPIgHJ/jkHnEKEC8+WygSklhjF9BUOqwIDAQAB
-----END PUBLIC KEY-----";

//商户私钥
$mchPrivateKey = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

$secKey = RandomUtil::randomStr(16);

$data = [];
$data["mch_no_trade"] = "***************";
$data["mch_order_no"] = "*************";
$data["order_amount"] = "10.02";
$data["mch_req_time"] = date('Y-m-d H:i:s', time());
$data["order_desc"] = "测试sdk-php5";
$data["callback_url"] = "http://10.10.10.37:8080";
$data["callback_param"] = null;
$data["bank_card_no"] = AESUtil::encryptECB("6222600260001072444", $secKey);//加密

$request = new Request();
$request->setMethod("fastPay.agreement.pay");
$request->setVersion("1.0");
$request->setMchNo("***************");
$request->setSignType("2");
$request->setRandStr(RandomUtil::randomStr(32));
$request->setData($data);
$request->setSecKey($secKey);//rsa有效

$secretKey = new SecretKey();
$secretKey->setReqSignKey($mchPrivateKey);//签名：使用商户私钥
$secretKey->setRespVerifyKey($platPublicKey);//验签：使用平台公钥
$secretKey->setSecKeyEncryptKey($platPublicKey);//sec_key加密：使用平台公钥
$secretKey->setSecKeyDecryptKey($mchPrivateKey);//sec_key解密：使用商户私钥

$url = "https://api.joinpay.com/fastpay";
try {
    $response = RequestUtil::doRequest($url, $request, $secretKey);
    if ($response->isSuccess()) {//受理成功
        $dataArr = json_decode($response->getData(), true);
        if($dataArr["order_status"] == "P1000"){//订单交易成功
            echo "SUCCESS, Response = ";
            print_r($response);
        }else{
            echo "FAIL OR PROCESSING OR UNKNOWN, Response = ";
            print_r($dataArr);
        }
    }else{
        echo "受理失败, Response = ";
        print_r($response);
    }
} catch (Exception $e) {
    print_r($e);
}

