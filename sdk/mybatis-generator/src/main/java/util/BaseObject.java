package util;


import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * @Author: 
 * @Description: ${description}
 * @Date: 2019/12/9 17:16
 * @Version: 1.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BaseObject implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -2970365983589030299L;

	public String toString(){
        return JsonUtils.toJson(this);
    }
}
