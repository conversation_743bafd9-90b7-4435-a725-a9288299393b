package util;

import org.springframework.stereotype.Component;

@Component
public class Constants {

	/**
	 * UTF-8 字符集
	 */
	public static final String UTF8 = "UTF-8";

	// 新增
	public final static String METHOD_ADD = "add";

	// 查询单条详情
	public final static String METHOD_INFO = "selectByPrimaryKey";

	// 条件分页查询
	public final static String METHOD_LIST = "list";

	// 修改
	public final static String METHOD_UPDATE = "update";

	// 删除
	public final static String METHOD_DEL = "del";

	// 参数错误状态码
	public final static String PARAM_ERROR = "99999999";

	// redis 缓存时间
	public final static int REDIS_TIME_OUT = 60 * 30;

	public final static String YYYY_MM_DD = "yyyy-MM-dd";

}
