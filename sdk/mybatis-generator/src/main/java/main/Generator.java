package main;

import org.mybatis.generator.api.MyBatisGenerator;
import org.mybatis.generator.config.Configuration;
import org.mybatis.generator.config.xml.ConfigurationParser;
import org.mybatis.generator.internal.DefaultShellCallback;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @ Author :  @ Date : 2019/5/23 16:39
 **/
public class Generator {
	public static void main(String[] args) throws Exception {

		/****=============需要更改的地方START=============*****/

		String functionName = "账户资金变动"; // 类描述
		String ClassName = "AcChangeFunds"; // 类名
		String className = "AcChangeFunds"; // 类名驼峰
		String permissionName = "ac:changeFunds"; // 权限开头
		String requestUrl = "/ac/changeFunds"; // controller请求路径
//		String path = System.getProperty("user.dir") + "/src/main/java/classfile/";// System.getProperty("user.dir") + "/src/main/java/classfile/";
		String path = "/Users/<USER>/svn_works/DEV/src/main/java/classfile/";
		/****=============需要更改的地方END=============*****/


		// MBG 执行过程中的警告信息
		List<String> warnings = new ArrayList<String>();
		// 当生成的代码重复时，覆盖原代码
		boolean overwrite = true;
		// 读取我们的 MBG 配置文件
		InputStream is = Generator.class.getResourceAsStream("/generatorConfig.xml");
		ConfigurationParser cp = new ConfigurationParser(warnings);
		Configuration config = cp.parseConfiguration(is);
		is.close();
		DefaultShellCallback callback = new DefaultShellCallback(overwrite);
		// 创建 MBG
		MyBatisGenerator myBatisGenerator = new MyBatisGenerator(config, callback, warnings);
		// 执行生成代码
		myBatisGenerator.generate(null);
		// 获取保存的注释信息
		List<String> request = CommentGenerator.request;
		List<String> response = CommentGenerator.response;
		ClassCreate.generate(path, functionName, ClassName, className, request, response, permissionName, requestUrl);
		// 输出警告信息
		for (String warning : warnings) {
			System.out.println(warning);
		}
	}
}
