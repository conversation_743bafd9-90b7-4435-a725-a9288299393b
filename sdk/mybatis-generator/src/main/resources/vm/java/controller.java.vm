package classfile;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;
import com.xpay.web.api.common.model.UserModel;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;

import com.xpay.web.pms.web.controller.BaseController;
import com.xpay.common.statics.annotations.Permission;
import com.xpay.web.pms.web.vo.common.QueryParamVo;
import com.xpay.common.statics.result.PageResult;
import com.xpay.common.statics.result.RestResult;
import com.xpay.common.utils.BeanUtil;
import java.util.List;
import java.util.Map;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * ${functionName}Controller
 * <AUTHOR> @date ${date}
 */
@Slf4j
@Api(tags = "${functionName}")
@RestController
@RequestMapping("${requestUrl}")
public class ${ClassName}Controller extends BaseController {

    @Reference
    private ${ClassName}Facade ${className}Facade;
	
    /**
     * @api 条件分页查询${functionName}列表
     * @apiName listByPage
     * @apiGroup ${functionName}
     * @apiVersion 0.1.0
     * @apiDescription ${functionName}
     */
    @Permission("${permissionName}:list")
    @ApiOperation(value = "条件分页查询${functionName}列表", notes = "条件分页查询${functionName}列表")
    @PostMapping("/listByPage")
    public RestResult<PageResult<List<${ClassName}>>> listByPage(@RequestBody @Valid QueryParamVo queryVo) {
		PageQuery pageQuery = PageQuery.newInstance(queryVo.getCurrentPage(), queryVo.getPageSize());
		Map<String, Object> map = BeanUtil.toMap(queryVo);
		PageResult<List<${ClassName}>> pageResult = ${className}Facade.listPage(map, pageQuery);
		return RestResult.success(pageResult);
	}
	
	
    @ApiOperation(value = "新增${functionName}", notes = "新增${functionName}")
    @Permission("${permissionName}:add")
    @PostMapping("/add")
    public RestResult<String> add(@RequestBody @Valid ${ClassName} ${className}AddVo, @CurrentUser UserModel userModel) {
        try {
	        ${ClassName} dbModel = new ${ClassName}();
			BeanUtil.copyProperties(${className}AddVo, dbModel);
			${className}Facade.insert(dbModel);
			logAdd("新增${functionName}成功", userModel);
			return RestResult.success("添加成功!");
        } catch (BizException e2) {
			log.error("新增${functionName}业务异常", e2);
			logAdd("新增${functionName}异常，异常信息：" + e2.getErrMsg(), userModel);
			return RestResult.error("新增${functionName}系统异常：" + e2.getErrMsg());
		} catch (Exception e) {
        	log.error("新增${functionName}系统异常", e);
			return RestResult.error("新增${functionName}系统异常");
        }
    }

    @ApiOperation(value = "修改${functionName}", notes = "修改${functionName}")
    @Permission("${permissionName}:edit")
    @PostMapping("/edit")
    public RestResult<String> edit(@RequestBody @Valid ${ClassName} ${className}EditVo, @CurrentUser UserModel userModel) {
        if (StrUtil.isEmptyIfStr(${className}EditVo.getId())) {
        	return RestResult.error("id不能为空！");
        }
        try {
			${ClassName} dbModel = ${className}Facade.getById(${className}EditVo.getId());
			if (dbModel == null) { 
				return RestResult.error("${functionName}不存在！");
			}
			
			BeanUtil.copyProperties(${className}EditVo, dbModel);
			dbModel.setLastOperatorId(userModel.getId());
			${className}Facade.update(dbModel);
			logEdit("修改${functionName}成功", userModel);
			return RestResult.success("修改成功!");
		} catch (BizException e2) {
			log.error("修改${functionName}业务异常", e2);
			logEdit("修改${functionName}异常，异常信息：" + e2.getErrMsg(), userModel);
			return RestResult.error("修改${functionName}系统异常：" + e2.getErrMsg());
		} catch (Exception e) {
        	log.error("修改${functionName}系统异常", e);
			return RestResult.error("修改${functionName}系统异常");
        }
    }


    /**
     * @api  通过id查看${functionName}详细信息
     * @apiName getById
     * @apiGroup ${functionName}
     * @apiVersion 0.1.0
     * @apiDescription 通过id查看${functionName}详细信息
     */
    @Permission("${permissionName}:view")
    @ApiOperation(value = "通过id查看${functionName}详细信息", notes = "通过id查看${functionName}详细信息")
    @GetMapping("/getById")
    public RestResult<${ClassName}> getById(@RequestParam @NotEmpty Long id) {
		${ClassName} ${className} = ${className}Facade.getById(id);
		if(${className} == null) {
			return RestResult.error("信息不存在");
		}
		return RestResult.success(${className});
	}

}
