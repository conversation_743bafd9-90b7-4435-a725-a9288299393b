package classfile;

import com.xpay.common.statics.params.PageQuery;
import com.xpay.common.statics.result.PageResult;
import com.xpay.common.statics.exception.BizException;
import java.util.List;
import java.util.Map;

import model.${ClassName};

/**
 * ${functionName}Service接口
 * <AUTHOR> @date ${date}
 */
public interface ${ClassName}Facade {

    //新增${functionName}
    void insert(${ClassName} ${className}) throws BizException;

    //修改${functionName}
    void update(${ClassName} ${className}) throws BizException;

    //通过id查看${functionName}
    ${ClassName} getById(Long id);
    
    // 根据条件查询列表
    public List<${ClassName}> listBy(Map<String, Object> paramMap);

    //条件分页查询${functionName}列表
    PageResult<List<${ClassName}>> listPage(Map<String, Object> paramMap, PageQuery pageQuery);

}
