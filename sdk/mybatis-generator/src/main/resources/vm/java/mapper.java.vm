package classfile;

import org.apache.ibatis.annotations.Param;
import util.QueryParam;
import java.util.List;
import model.${ClassName};


/**
 * ${functionName}Mapper接口
 * <AUTHOR> sq
 * @date ${date}
 */
public interface ${ClassName}Mapper extends BaseMapper<${ClassName}> {

    //新增${functionName}
    int insert(${ClassName} ${className});

    //修改${functionName}
    int update(${ClassName} ${className});

    //通过id查看${functionName}
    ${ClassName} findById(@Param("id") Integer id);

    //通过id删除${functionName}
    int deleteById(@Param("id") Integer id);

    //条件分页查询${functionName}
    List<${ClassName}> findList(QueryParam param);
}
