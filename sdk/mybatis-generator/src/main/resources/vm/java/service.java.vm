package classfile;

import com.xpay.common.statics.params.PageQuery;
import com.xpay.common.statics.result.PageResult;
import model.${ClassName};

/**
 * ${functionName}Service接口
 * <AUTHOR> sq
 * @date ${date}
 */
public interface ${ClassName}Service {

    //新增${functionName}
    int insert(${ClassName} ${className}) throws BizExcption;;

    //修改${functionName}
    int update(${ClassName} ${className}) throws BizException;

    //通过id查看${functionName}
    ${ClassName} findById(Integer id);

    //条件分页查询${functionName}列表
    PageResult<List<${ClassName}>> listByPage(Map<String, Object> paramMap, PageQuery pageQuery);

    //通过id删除${functionName}信息
    int deleteById(Integer id);





}
