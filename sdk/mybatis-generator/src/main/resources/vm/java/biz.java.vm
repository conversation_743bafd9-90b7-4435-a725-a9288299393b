package classfile;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.xpay.common.statics.params.PageQuery;
import com.xpay.common.statics.result.PageResult;


/**
 * ${functionName}biz业务实现类
 * <AUTHOR> @date ${date}
 */
@Service
public class ${ClassName}Biz {
	
	@Autowired
	private ${ClassName}Dao ${className}Dao;
	
	//新增${functionName}
    public void insert(${ClassName} ${className}) {
		${className}Dao.insert(${className});
	}

    //修改${functionName}
    public void update(${ClassName} ${className}) {
        ${className}Dao.update(${className});
    }

    //通过id查看${functionName}
    public ${ClassName} getById(Long id) {
        return ${className}Dao.selectByPrimaryKey(id);
    }
    
	public List<${ClassName}> listBy(Map<String, Object> paramMap) {
		return ${className}Dao.listBy(paramMap);
	}

    //条件分页查询${functionName}
    public PageResult<List<${ClassName}>> listPage(Map<String, Object> paramMap, PageQuery pageQuery) {
		return ${className}Dao.listPage(paramMap, pageQuery);
	}
	
	
}
