package classfile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.xpay.common.statics.params.PageQuery;
import com.xpay.common.statics.result.PageResult;
import java.util.List;
import java.util.Map;

import model.${ClassName};

/**
 * ${functionName}Service业务层处理
 * <AUTHOR> @date ${date}
 */
@Service
public class ${ClassName}FacadeImpl implements ${ClassName}Facade {
    
    @Autowired
    private ${ClassName}Biz ${className}Biz;

    //新增${functionName}
    @Override
    public void insert(${ClassName} ${className}) {
		${className}Biz.insert(${className});
	}

    //修改${functionName}
    @Override
    public void update(${ClassName} ${className}) {
        ${className}Biz.update(${className});
    }

    //通过id查看${functionName}
    @Override
    public ${ClassName} getById(Long id) {
        return ${className}Biz.getById(id);
    }
    
    @Override
	public List<${ClassName}> listBy(Map<String, Object> paramMap) {
		return ${className}Biz.listBy(paramMap);
	}

    //条件分页查询${functionName}
    @Override
    public PageResult<List<${ClassName}>> listPage(Map<String, Object> paramMap, PageQuery pageQuery) {
		return ${className}Biz.listPage(paramMap, pageQuery);
	}
    
}
