<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
  PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!--数据库驱动-->
    <properties resource="generator.properties"/> <!--添加配置数据源文件-->
    <context id="DB2Tables" targetRuntime="MyBatis3" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 格式化java代码 -->
	    <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>
	    <!-- 格式化XML代码 -->
	    <property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter"/>
        <!-- 为模型生成序列化方法-->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- 为生成的Java模型创建一个toString方法 -->
        <!--<plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>-->

        <!--可以自定义生成model的代码注释,-->
        <!--生成mapper.xml时覆盖原文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
		
		<plugin type="main.LombokPlugin">
            <property name="hasLombok" value="false"/>
        </plugin>
	
		<!-- 是否去除自动生成的注释 true：是 ： false:否 -->
        <commentGenerator type="main.CommentGenerator">
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>
        
        <!--数据库链接地址账号密码-->
        <jdbcConnection driverClass="${jdbc.driverClass}"
                          connectionURL="${jdbc.connectionURL}"
                          userId="${jdbc.userId}"
                         password="${jdbc.password}">
                     <!--解决mysql驱动升级到8.0后不生成指定数据库代码的问题-->
                    <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!--生成Model类存放位置-->
        <javaModelGenerator targetPackage="classfile.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
           <!-- <property name="trimStrings" value="true"/>-->
        </javaModelGenerator>
        <!--指定生成mapper.xml的路径-->
        <sqlMapGenerator targetPackage="classfile.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!--生成Dao类存放位置-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="classfile.dao" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        
        <!--
        	tableName  数据库对应表名
        	domainObjectName  对应的实体名称
         -->
        <table tableName="${generate.tableName}"
               enableCountByExample="false" enableUpdateByExample="false" 
               enableDeleteByExample="false" enableSelectByExample="false" 
               selectByExampleQueryId="false">
            <!--添加此配置: 不使用默认驼峰格式-->
            <!--<property name="useActualColumnNames" value="true"/>-->

            <!--<generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        </table>
    </context>
</generatorConfiguration>