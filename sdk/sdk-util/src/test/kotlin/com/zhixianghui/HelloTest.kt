package com.zhixianghui

import cn.hutool.crypto.digest.DigestUtil
import com.zhixianghui.common.util.utils.RSAUtil
import com.zhixianghui.common.util.utils.RandomUtil
import org.junit.Test
import java.util.Base64
import kotlin.test.assertEquals

class HelloTest {

    @Test
    fun signTest(): Unit {
        println(Base64.getEncoder().encodeToString("7cb257e68d23ebab74bc848ef658a594".toByteArray()))

    }


}
