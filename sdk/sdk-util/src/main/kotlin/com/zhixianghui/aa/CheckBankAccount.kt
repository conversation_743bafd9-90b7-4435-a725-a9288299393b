package com.zhixianghui.aa

import cn.hutool.db.Db
import cn.hutool.db.DbUtil
import cn.hutool.db.ds.simple.SimpleDataSource
import cn.hutool.json.JSONUtil
import com.alibaba.excel.EasyExcel
import javax.sql.DataSource

class CheckBankAccount {

    fun readExcel(db:Db){


       val readBuilder = EasyExcel.read("C:\\Users\\<USER>\\Documents\\WXWork\\****************\\Cache\\File\\2023-11\\分账方结算卡2.xlsx")
        val maps = readBuilder.doReadAllSync<Map<String, String>>()
        val acctMaps = maps.map {
            val parseObj = JSONUtil.parseObj(it)
            return@map Triple(parseObj.getStr("0"),parseObj.getStr("2") , parseObj.getStr("3"))
        }
        acctMaps.forEach {
            val joinpayMchNo = it.first
            val joinpayPlatMchNo = it.second
            val joinpayBankAcct = it.third

            val entity = db.queryOne("select EMPLOYER_NO,EMPLOYER_NAME,t.MAINSTAY_NAME from tbl_employer_account_info t where t.STATUS =100 and t.SUB_MERCHANT_NO =$joinpayMchNo and t.PARENT_MERCHANT_NO =$joinpayPlatMchNo and PAY_CHANNEL_NO ='JOINPAY'")

            if (entity != null) {
                val empNo = entity.get("EMPLOYER_NO")
                val empName = entity.get("EMPLOYER_NAME")
                val mstName = entity.get("MAINSTAY_NAME")

                val entity1 =
                    db.queryOne("select t.ACCOUNT_NO,t.ACCOUNT_NAME  from merchant.tbl_merchant_bank_account t where t.MCH_NO ='$empNo'")
                if (entity1!=null) {
                    val zxAcctNo = entity1.getStr("ACCOUNT_NO").replace(" ","").trim()
                    if (zxAcctNo != joinpayBankAcct) {
                        println("$empNo $empName $mstName $zxAcctNo $joinpayBankAcct")
                    }
                }
            }

        }

    }

}

fun main() {
    val ds: DataSource = SimpleDataSource("*******************************************************************************************************************************************************", "hjzx", "ZIzUQrA&bk1I")
    val db = DbUtil.use(ds)
    CheckBankAccount().readExcel(db)
}
