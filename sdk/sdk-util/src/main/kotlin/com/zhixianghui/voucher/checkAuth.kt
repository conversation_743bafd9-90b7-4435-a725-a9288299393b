package com.zhixianghui.voucher

import cn.hutool.db.DbUtil
import cn.hutool.db.Entity
import cn.hutool.db.Page
import cn.hutool.db.PageResult
import cn.hutool.db.ds.simple.SimpleDataSource
import cn.hutool.db.handler.PageResultHandler
import com.zhixianghui.applyAlipayRemitVoucher
import javax.sql.DataSource

fun queryDataFromDb() {

    val sql:String = """
            select  t.RECEIVE_NAME ,t.RECEIVE_ID_CARD_NO ,t.RECEIVE_ACCOUNT_NO,t.ENCRYPT_KEY_ID  from trade.tbl_order_item t
            where 
             t.ORDER_ITEM_STATUS =100
             and t.CREATE_TIME >='2023-06-01 00:00:00'
             and t.CREATE_TIME <='2023-06-13 23:59:59'
             and t.RECEIVE_ID_CARD_NO_MD5 not in (
             select a.RECEIVE_ID_CARD_NO_MD5  from trade.tbl_auth_record a
             where a.AUTH_TYPE = 1
             )
    """.trimIndent()

    val ds: DataSource = SimpleDataSource("jdbc:mysql://************:8306/trade?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai&useInformationSchema=false", "hjzx", "ZIzUQrA&bk1I")
    val db = DbUtil.use(ds)
    var currentPage = 0
    var pageResult: PageResult<Entity>
    val size = 1000
    do {
        val page = Page.of(currentPage, size)
        pageResult = db.page(sql,page, PageResultHandler(PageResult(page.pageNumber, page.pageSize), false))

        if (pageResult != null && !pageResult.isEmpty()) {
            println()
        }
        currentPage ++
    }while (pageResult!=null && !pageResult.isEmpty())

}