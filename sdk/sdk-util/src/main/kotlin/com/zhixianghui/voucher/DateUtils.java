package com.zhixianghui.voucher;

import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Calendar;
import java.util.Date;

public class DateUtils {
    public static final DateTimeFormatter DATE_TIME_MILLS_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd");

    /**
     * 把string格式的时间转换为Date类型，dateStr如：2018-08-08
     *
     * @param dateStr
     * @return
     */
    public static Date parseDateSafe(String dateStr) {
        try {
            return DateTime.parse(dateStr, DATE_FORMATTER).toDate();
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException(dateStr + " 不合法！！");
        }
    }

    public static String formatDateTime(Date date) {
        if (date == null) {
            return "";
        }
        LocalDateTime dateTime = parseJodaDateTime(date);
        return dateTime.toString(DATE_TIME_FORMATTER);
    }

    public static LocalDateTime parseJodaDateTime(Date date) {
        return new LocalDateTime(date);
    }

    /**
     * 得到当月起始时间
     *
     * @param date
     * @return
     */
    public static Date getMonthStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 得到month的终止时间点.
     *
     * @param date
     * @return
     */
    public static Date getMonthEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.MILLISECOND, -1);
        return calendar.getTime();
    }
}
