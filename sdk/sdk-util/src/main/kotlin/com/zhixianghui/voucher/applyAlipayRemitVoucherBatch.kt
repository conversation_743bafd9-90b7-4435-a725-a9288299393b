package com.zhixianghui

import cn.hutool.core.io.FileUtil
import cn.hutool.core.lang.Tuple
import cn.hutool.core.thread.ThreadUtil
import cn.hutool.db.DbUtil
import cn.hutool.db.Entity
import cn.hutool.db.Page
import cn.hutool.db.PageResult
import cn.hutool.db.ds.simple.SimpleDataSource
import cn.hutool.db.handler.PageResultHandler
import cn.hutool.http.HttpUtil
import cn.hutool.json.JSONUtil
import com.zhixianghui.aliApi.AliPayService
import java.util.LinkedList
import java.util.Queue
import java.util.concurrent.TimeUnit
import javax.sql.DataSource

val aliPayService = AliPayService()
val datas:Queue<Map<String,String>> = LinkedList()
//val agreementNo = "20215327714266704118"
//val parentAgreementNo = "20215214734919748880"
val baseDir = "E://支付宝回单"


fun applyAlipayRemitVoucher(queryData:ArrayList<Entity>,page: Int) {

    queryData.forEach { recordItem->
            if (!FileUtil.exist(baseDir + "/" + recordItem.getStr("PLAT_TRX_NO")+"/")) {
                var employerNo = recordItem.getStr("EMPLOYER_NO")
                var mainstayNo = recordItem.getStr("MAINSTAY_NO")

                var account = queryAccount(employerNo, mainstayNo)
                if (account == null) {
                    println("查询不到支付宝账户信息")
                    return@forEach
                }
                val agreementNo = account?.getStr("SUB_AGREEMENT_NO")
                val parentAgreementNo = account?.getStr("PARENT_AGREEMENT_NO")

                if (agreementNo ==null || parentAgreementNo == null) {
                    println("查询不到支付宝账户信息")
                    return@forEach
                }

                var body0 = aliPayService.transCommonQuery(
                    recordItem.getStr("REMIT_PLAT_TRX_NO"),
                    "ENTRUST_ALLOCATION",
                    "SINGLE_TRANSFER_NO_PWD"
                )
                println(body0)
                val data0 = JSONUtil.parseObj(body0).getJSONObject("alipay_fund_trans_common_query_response")
                val payFundOrderId0 = data0.getStr("pay_fund_order_id")

                val body1: String? = aliPayService.transCommonQueryByOrderId(recordItem.getStr("CHANNEL_TRX_NO"))
                val data1 = JSONUtil.parseObj(body1).getJSONObject("alipay_fund_trans_common_query_response")
                val payFundOrderId1 = data1.getStr("pay_fund_order_id")

                //开始发送回单申请
                val fileId0: String? = aliPayService.billApply(payFundOrderId0, agreementNo)
                val fileId1: String? = aliPayService.billApply(payFundOrderId1, parentAgreementNo)

                if (fileId0 != null && fileId1 != null) {
//                    datas.put(recordItem.getStr("PLAT_TRX_NO"), Pair(fileId0, fileId1))
//                     = Triple(recordItem.getStr("PLAT_TRX_NO"), fileId0, fileId1)

                    var todoItem = mapOf( "platTrxNo" to recordItem.getStr("PLAT_TRX_NO"),"fileId0" to fileId0,"fileId1" to fileId1,"agreementNo" to agreementNo,"parentAgreementNo" to parentAgreementNo )
                    datas.add(todoItem)
                }
            }
    }

    TimeUnit.SECONDS.sleep(600L)
    ThreadUtil.execAsync {
        download()
    }


}

fun queryAccount(employerNo:String,mainstayNo: String):Entity?{
    val sql:String = """
        select * from hjzx_common.tbl_employer_account_info t
        where EMPLOYER_NO ='${employerNo}'  and MAINSTAY_NO ='${mainstayNo}' and  PAY_CHANNEL_NO ='ALIPAY'
    """.trimIndent()

    val ds: DataSource = SimpleDataSource("*************************************************************************************************************************************************", "hjzx", "ZIzUQrA&bk1I")
    val db = DbUtil.use(ds)
    var entity = db.queryOne(sql)
    return entity
}


fun queryAlipayData() {

    val sql:String = """
        SELECT
          *
        FROM
          tbl_record_item AS t
        WHERE
          EMPLOYER_NO = 'M00000293'
          AND t.PROCESS_STATUS = 100
          AND t.PAY_CHANNEL_NO = 'ALIPAY'
          AND ((COMPLETE_TIME BETWEEN '2024-04-01 00:00:00' AND '2024-06-01 00:00:00') OR (COMPLETE_TIME BETWEEN '2024-09-01 00:00:00' AND '2025-01-01 00:00:00'))
    """.trimIndent()

    val ds: DataSource = SimpleDataSource("*************************************************************************************************************************************************", "hjzx", "ZIzUQrA&bk1I")
    val db = DbUtil.use(ds)
    var currentPage = 0
    var pageResult:PageResult<Entity>
    val size = 1000
    do {
        val page = Page.of(currentPage, size)
        pageResult = db.page(sql,page, PageResultHandler(PageResult(page.pageNumber, page.pageSize), false))

        if (pageResult != null && !pageResult.isEmpty()) {
            println("处理第${currentPage}页")
            applyAlipayRemitVoucher(pageResult,currentPage)
        }
        currentPage ++
    }while (pageResult!=null && !pageResult.isEmpty())

}

fun download(){
    var item:Map<String,String> ?= null
    do {
        item = datas.poll()
        if (item != null) {
            val t = item.get("platTrxNo")
            var fileId0 = item.get("fileId0")
            var fileId1 = item.get("fileId1")
            var agreementNo = item.get("agreementNo")
            var parentAgreementNo = item.get("parentAgreementNo")
            if ( fileId0 !=null && fileId1 != null
                && agreementNo !=null && parentAgreementNo != null) {
                val fileUrl0: String? = aliPayService.billQuery(fileId0, agreementNo)
                val fileUrl1: String? = aliPayService.billQuery(fileId1, parentAgreementNo)
                try {
                    HttpUtil.downloadFile(fileUrl0, baseDir + "/" + t + "/0_" + t + ".pdf")
                    HttpUtil.downloadFile(fileUrl1, baseDir + "/" + t + "/1_" + t + ".pdf")
                } catch (e: Exception) {
                    println("$t 下载出错:${e.message}")
                }
            }

        }else{
            println("未从队列中获取到数据")
        }

    }while (item != null)
}

fun main() {
    try {
        queryAlipayData()
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

