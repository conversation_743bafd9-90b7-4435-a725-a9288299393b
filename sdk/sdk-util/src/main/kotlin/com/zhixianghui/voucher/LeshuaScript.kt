package com.zhixianghui.voucher

import cn.hutool.db.DbUtil
import cn.hutool.db.Entity
import cn.hutool.db.Page
import cn.hutool.db.PageResult
import cn.hutool.db.ds.simple.SimpleDataSource
import cn.hutool.db.handler.PageResultHandler
import cn.hutool.http.Header
import cn.hutool.http.HttpRequest
import cn.hutool.json.JSONObject
import cn.hutool.json.JSONUtil
import sun.misc.BASE64Decoder
import java.io.*
import java.util.concurrent.TimeUnit
import javax.sql.DataSource


fun downloadLeshua(flowNo:String,tradeDate:String,platTrxNo:String,empName:String){
    val file: File = File("E:\\乐刷回单\\${empName}\\${tradeDate}\\${platTrxNo}-${flowNo}.pdf")
    val path = file.parentFile
    if (!path.exists()) {
        path.mkdirs()
    }
    if (file.exists()) {
        println("回单 ${platTrxNo}-${flowNo}.pdf 已经存在")
        return
    }

    val downloadParam: MutableMap<String, Any> = HashMap()
    downloadParam["source"] = "HJZX"
    downloadParam["flowNo"] = flowNo
    downloadParam["tradeDate"] = tradeDate

    val post: String = HttpRequest.post("https://open-baseweb.leshuazf.com/external-api/invoiceFlow/download")
        .header(Header.CONTENT_TYPE,"application/json")
        .body(JSONUtil.toJsonStr(downloadParam)) //表单内容
        .timeout(20000) //超时，毫秒
        .execute().body()
    var jsonObject:JSONObject? = null
    try {
        jsonObject = JSONUtil.parseObj(post)
//        println("${flowNo} ${jsonObject.toString()}")
    } catch (e: Exception) {
        println("下载失败，原始报文:${post}")
        return
    }
    val fileData = jsonObject?.getJSONObject("data")?.getStr("fileData")
    val decoder = BASE64Decoder()
    val bytes = decoder.decodeBuffer(fileData)

    val byteInputStream = ByteArrayInputStream(bytes)
    val bis = BufferedInputStream(byteInputStream)

    val fos = FileOutputStream(file)
    val bos = BufferedOutputStream(fos)
    val buffer = ByteArray(1024)
    var length: Int = bis.read(buffer)
    while (length != -1) {
        bos.write(buffer, 0, length)
        length = bis.read(buffer)
    }
    bos.flush()
    TimeUnit.MILLISECONDS.sleep(800)
}

fun queryLeshuaData() {

    val sql:String = """
        SELECT
          *
        FROM
          tbl_order_item AS t
        WHERE
          t.EMPLOYER_NO IN ('M00000592')
          AND t.MAINSTAY_NO = 'S000039'
          AND t.PAY_CHANNEL_NO = 'OUT_SYNC'
          AND t.CREATE_TIME >= '2024-12-01 00:00:00'
          AND t.CREATE_TIME <= '2024-12-31 23:59:59'
    """.trimIndent()

//        val sql:String = """
//         select
//            *
//        from
//            tbl_order_item as t
//        where
//            t.PLAT_TRX_NO = 'E20250326007752253'
//    """.trimIndent()

    val ds: DataSource = SimpleDataSource("*************************************************************************************************************************************************", "hjzx", "ZIzUQrA&bk1I")
    val db = DbUtil.use(ds)
    var currentPage = 0
    val pageSiz = 100
    var pageResult: PageResult<Entity>



    do {
        val page = Page.of(currentPage, pageSiz)
        pageResult = db.page(sql,page, PageResultHandler(PageResult(page.pageNumber, page.pageSize), false))
        currentPage ++
        if (pageResult != null && !pageResult.isEmpty()) {

            pageResult.forEach { entity ->
                    try {
                        downloadLeshua(
                            entity.getStr("MCH_ORDER_NO"),
                            entity.getStr("COMPLETE_TIME").substring(0, 10),
                            entity.getStr("PLAT_TRX_NO"),
                            entity.getStr("EMPLOYER_NAME")
                        )
                    } catch (e: Exception) {
//                        println("回单 ${entity.getStr("MCH_ORDER_NO")} 下载失败")
                    }
            }


        }
    }while (pageResult!=null && !pageResult.isEmpty())

}

fun main() {
    queryLeshuaData()
}
