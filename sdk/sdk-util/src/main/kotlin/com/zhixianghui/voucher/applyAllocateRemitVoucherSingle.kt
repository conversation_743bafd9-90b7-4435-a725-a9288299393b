package com.zhixianghui

import cn.hutool.core.io.FileUtil
import cn.hutool.core.util.RandomUtil
import cn.hutool.core.util.ZipUtil
import cn.hutool.crypto.digest.DigestUtil
import cn.hutool.db.DbUtil
import cn.hutool.db.ds.simple.SimpleDataSource
import cn.hutool.http.HttpUtil
import cn.hutool.json.JSONObject
import cn.hutool.json.JSONUtil
import com.zhixianghui.voucher.MainstayEnum
import com.zhixianghui.voucher.pusHost
import com.zhixianghui.voucher.queryHost
import java.io.File
import java.util.concurrent.TimeUnit
import java.util.zip.ZipFile
import javax.sql.DataSource

val file_path_single = "E:\\huidan\\凭证下载\\"

fun applyAllocateRemitVoucherSingle(zxPlatTrxNo: String) {
    val dataSingle = queryDataSingle(zxPlatTrxNo)
    val item = JSONUtil.parseObj(dataSingle)
    if (item !is JSONObject) {
        println("不是JSONObject格式")
        return
    }

    val mainstayNo = item.getStr("mainstay_no")
    val remitPlatTrxNo = item.getStr("remit_plat_trx_no")
    val pc05_orgOrderTime = item.getStr("create_date")

    val (pc01_MerchantNo, key) = MainstayEnum.getByMainstayNo(mainstayNo)
    val pc03_bizType = "ALLOCATE_BANK_TRANSFER"
    val pc06_notifyUrl = "https://banknotify.joinpay.com/test/hello"

    val pc04_orgMchOrderNo = remitPlatTrxNo
    val pc02_mchOrderNo = RandomUtil.randomString(16)
    val sign_str =
        pc01_MerchantNo + pc02_mchOrderNo + pc03_bizType + pc04_orgMchOrderNo + pc05_orgOrderTime + pc06_notifyUrl + key
    val hmac = DigestUtil.md5Hex(sign_str)

    val params = mapOf(
        "pc01_MerchantNo" to pc01_MerchantNo,
        "pc02_mchOrderNo" to pc02_mchOrderNo,
        "pc03_bizType" to pc03_bizType,
        "pc04_orgMchOrderNo" to pc04_orgMchOrderNo,
        "pc05_orgOrderTime" to pc05_orgOrderTime,
        "pc06_notifyUrl" to pc06_notifyUrl,
        "hmac" to hmac
    )

    println("申请生成：${zxPlatTrxNo}")
    var pusResult: String = HttpUtil.post(pusHost, params)
    val resultObj = JSONUtil.parseObj(pusResult)
    val code = resultObj.getInt("code")
    if (code != null && code == 0) {
        println("申请成功：${zxPlatTrxNo}")
    } else {
        println("申请失败：${pusResult}")
    }
    var data = resultObj.getJSONObject("data")
    var mchOrderNo = data.getStr("mchOrderNo")
    var platTrxNo = data.getStr("platTrxNo")
    queryVoucherSingle(mchOrderNo, platTrxNo, pc01_MerchantNo, key, item)
}

fun queryVoucherSingle(mchOrderNo: String, platTrxNo: String, pc01_MerchantNo: String, key: String, item: JSONObject) {
    val pc02_mchOrderNo = mchOrderNo
    val pc03_platTrxNo = platTrxNo
    val sign_str = pc01_MerchantNo + pc02_mchOrderNo + pc03_platTrxNo + key

    val hmac = DigestUtil.md5Hex(sign_str)

    val params = mapOf(
        "pc01_MerchantNo" to pc01_MerchantNo,
        "pc02_mchOrderNo" to pc02_mchOrderNo,
        "pc03_platTrxNo" to pc03_platTrxNo,
        "hmac" to hmac
    )

    var times = 0
    var fileUrl: String?
    do {
        TimeUnit.SECONDS.sleep(2)//等待通道端生成文件
        val qryResult: String = HttpUtil.post(queryHost, params)//查询通道端的文件下载地址
        val resultObj = JSONUtil.parseObj(qryResult)
        fileUrl = resultObj.getJSONObject("data").getStr("fileUrl")
        times++
    } while (times < 5 && (fileUrl == null || fileUrl == ""))

    platTrxNo.let { it1 ->
        fileUrl?.let { it2 -> downloadVoucherSingle(it2, it1, item) }
    }
}

fun downloadVoucherSingle(url: String, zxPlatTrxNo: String, item: JSONObject) {
    val zipFileName = file_path_single + url.substring(url.lastIndexOf("/"))
    val dir = url.substring(url.lastIndexOf("/")).split(".")[0]
    val handledDir = file_path_single + "${item.getStr("employer_name")}\\${item.getStr("mainstay_name")}\\${
        item.getStr("create_date").substring(0, 7)
    }\\"
    val unzipDir = file_path_single + dir

    try {
        HttpUtil.downloadFile(url, zipFileName)
    } catch (e: Exception) {
        try {
            HttpUtil.downloadFile(url, zipFileName)
        } catch (e: Exception) {
            println("下载失败：${zxPlatTrxNo}，${e.message}")
            return
        }
    }
    ZipUtil.unzip(ZipFile(zipFileName), File(unzipDir))
    val list = FileUtil.listFileNames(unzipDir)
    list.forEach {
        FileUtil.move(File(unzipDir + "/" + it), File("${handledDir}/${zxPlatTrxNo}/$it"), true)
    }
    println("下载成功：${zxPlatTrxNo}")
    FileUtil.del(unzipDir)
    FileUtil.del(zipFileName)
}

fun queryDataSingle(platTrxNo: String): Map<*, *> {
    val sql: String = """
        select 
            * 
        from 
            trade.tbl_record_item t 
        where
        t.RECEIVE_ID_CARD_NO_MD5 = lower(md5(******************))
            t.PLAT_TRX_NO = '${platTrxNo}'
            AND t.PROCESS_STATUS = 100
    """.trimIndent()

    println(sql)
    val ds: DataSource = SimpleDataSource(
        "*************************************************************************************************************************************************",
        "hjzx",
        "ZIzUQrA&bk1I"
    )
    val data = DbUtil.use(ds).queryOne(sql)
    return data
}

fun main() {
    try {
        applyAllocateRemitVoucherSingle("E20240126004400064")
    } catch (e: Exception) {
        e.printStackTrace()
    }
}
