package com.zhixianghui

import cn.hutool.core.io.FileUtil
import cn.hutool.core.io.IoUtil
import cn.hutool.db.DbUtil
import cn.hutool.db.ds.simple.SimpleDataSource
import cn.hutool.json.JSONUtil
import com.zhixianghui.common.statics.constants.common.EncryptKeys
import com.zhixianghui.common.util.utils.AESUtil
import com.zhixianghui.common.util.utils.SftpUtil
import java.io.File
import java.io.FileOutputStream
import javax.sql.DataSource

val MCH_MAPPING = mapOf<String, String>(
    "M00000325" to "333114100001504",
    "M00000175" to "333114100001503"
)

val channelMchNo = "888111100007408"

fun parse() {
    val list = queryDataJinzhigang()
    val channelSftp = SftpUtil.connect("39.108.86.224", 9022, "sftpadmin", "eExT03sXeTZrcag")
    println("待处理订单数：${list.size}")
    var index = 0
    list.forEach { item->
        index++
        println(index)
        val recordItem = JSONUtil.parseObj(item)
        val employerNo = recordItem.getStr("EMPLOYER_NO")
        val channelTrxNo = recordItem.getStr("CHANNEL_TRX_NO")
        val platTrxNo = recordItem.getStr("PLAT_TRX_NO")
        val mainstayName = recordItem.getStr("MAINSTAY_NAME")
        val createDate = recordItem.getStr("CREATE_DATE")
        val targetPath = getTargetPath(mainstayName, platTrxNo, createDate)

        val receiveName = AESUtil.decryptECB(
            recordItem.getStr("RECEIVE_NAME"),
            EncryptKeys.getEncryptKeyById(recordItem.getLong("ENCRYPT_KEY_ID")).encryptKeyStr
        )

        try {
            val inputStreamOne = channelSftp.get(getSftpPathOne(createDate, mainstayName, employerNo, channelTrxNo))
            val inputStreamTwo = channelSftp.get(getSftpPathTwo(createDate, receiveName, channelTrxNo))

            if (!FileUtil.isDirectory(targetPath)) {
                FileUtil.mkdir(targetPath)
            }
            IoUtil.copy (inputStreamOne, FileOutputStream(File(targetPath+"1_${channelTrxNo}.pdf")))
            IoUtil.copy(inputStreamTwo, FileOutputStream(File(targetPath+"2_${channelTrxNo}.pdf")))
        } catch (e: Exception) {
            println(e.message)
        }

    }

    channelSftp.disconnect()
}

fun getSftpPathOne(createDate: String, mainstayName: String, employerNo: String, channelTrxNo: String): String {
    val split = createDate.split("-")
    val sftpPath =
        "/${channelMchNo}/upload/trade_file/allocatePay/${split[0]}/${split[1]}/${split[2]}/1_${mainstayName}_${
            MCH_MAPPING.get(employerNo)
        }_${channelTrxNo}.pdf"

    return sftpPath
}

fun getSftpPathTwo(createDate: String, receiveName: String, channelTrxNo: String): String {
    val split = createDate.split("-")
    val sftpPath =
        "/${channelMchNo}/upload/trade_file/allocatePay/${split[0]}/${split[1]}/${split[2]}/2_${receiveName}_${channelMchNo}_${channelTrxNo}.pdf"

    return sftpPath
}

fun getSourcePath(createDate: String) : String{
    val split = createDate.split("-")
    val path = "C:\\Users\\<USER>\\Desktop/allocatePay/${split[0]}/${split[1]}/${split[2]}/"
    return path
}

fun getTargetPath(mainstayName:String,plateTrxno:String, createDate: String): String {
    val split = createDate.split("-")
    val path = "D:/今之港电子回单/${mainstayName}/${split[0]}/${split[1]}/${split[2]}/${plateTrxno}/"
    return path
}


fun queryDataJinzhigang():MutableList<Map<*,*>>{

    val sql:String = """
         select 
            * 
        from 
            trade.tbl_record_item t 
        where
            t.EMPLOYER_NO IN (
            'M00000325',
            'M00000175'
            )
            AND t.CREATE_TIME>='2022-06-01 00:00:00'
            AND T.CREATE_TIME<'2022-12-01 00:00:00'
            AND MAINSTAY_NO = 'S000001'
            AND t.PROCESS_STATUS =100
            and t.PAY_CHANNEL_NO ='JOINPAY'
    """.trimIndent()

    val ds: DataSource = SimpleDataSource("*************************************************************************************************************************************************", "hjzx", "ZIzUQrA&bk1I")
    val list = DbUtil.use(ds).query(sql, Map::class.java)

    return list
}

fun main() {
    try {
        parse()
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

