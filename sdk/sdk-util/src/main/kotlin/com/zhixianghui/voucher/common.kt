package com.zhixianghui.voucher

val pusHost="https://www.joinpay.com/trade/voucherPush.action"
val queryHost="https://www.joinpay.com/trade/voucherQuery.action"

enum class MainstayEnum(val mainstayNo:String, val channelMchNo:String,val channelMchKey:String){
    BAOLING("S000001","888111100007408","d03e21ee22a54b21841fdb8a2f50d3f5"),
    DIANBING("S000004","888111200009939","********************************"),
    YIZ<PERSON>("S000014","888113200009090","65b42a83251645bba29c0c2ec96b5484"),
    DEXING( "S000025", "888117800001941","121639855d1c4405b1c9fb39436daaf5"),
    GAOQING("S000021", "888117400000095","7e5da669d684474ea14320199ecf1489"),
    ANHUI_JICHENG("S000030", "888120000008028","ea8af246346e4097b6109e7e0d659de2"),
    CHENGRUN("S000031", "888120000004916","99cb59eec8674725a73c567a3fa7cfa0"),
    MUJING("S000039", "888121000008500","4517ca48b2894a48a8749e1c8384d48a"),
    MUJING2("S000041","888121000008500","4517ca48b2894a48a8749e1c8384d48a"),
    QUANXUN("S000019", "888115600002950","7e52804b216347de95157504634a6bd8"),
    ZHOUKOU("S000052", "888121700005403","a96632f5a14642ea99132cebc020ffd5"),
    YUNSHAN("S000038", "888120800004730","3d08b6700d604d189c1eafb98368df08"),
    YUANCAI("S000067","888122500005298","3302b1abb9eb4e3ab660b1e929b1deec"),
    ZHUBAN("S000062","888122600001211","cc543fedf9784afcbb336eb2e86c1410")
    ;

    operator fun component1(): String {
        return this.channelMchNo
    }

    operator fun component2(): String {
        return this.channelMchKey
    }

    companion object instance{
        fun getByMainstayNo(mainstayNo: String):MainstayEnum{
            val values = MainstayEnum.values()
            for (value in values) {
                if (value.mainstayNo == mainstayNo) {
                    return value
                }
            }
            throw java.lang.RuntimeException("枚举不存在")
        }
    }

}
