package com.zhixianghui

import cn.hutool.core.date.DateUtil
import cn.hutool.core.io.FileUtil
import cn.hutool.core.util.RandomUtil
import cn.hutool.core.util.ZipUtil
import cn.hutool.crypto.digest.DigestUtil
import cn.hutool.db.DbUtil
import cn.hutool.db.Entity
import cn.hutool.db.Page
import cn.hutool.db.PageResult
import cn.hutool.db.ds.simple.SimpleDataSource
import cn.hutool.db.handler.PageResultHandler
import cn.hutool.http.HttpUtil
import cn.hutool.json.JSONObject
import cn.hutool.json.JSONUtil
import com.alibaba.excel.EasyExcel
import com.zhixianghui.voucher.DateUtils
import com.zhixianghui.voucher.MainstayEnum
import com.zhixianghui.voucher.pusHost
import com.zhixianghui.voucher.queryHost
import java.io.File
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.zip.ZipFile
import javax.sql.DataSource

val pageSize = 10
val file_path2 = "E:\\huidan\\凭证下载0806\\"

fun applyAllocateRemitVoucher2(queryData: List<Entity>) {
    val pc03_bizType = "ALLOCATE_BANK_TRANSFER"
    val pc06_notifyUrl = "https://banknotify.joinpay.com/test/hello"
    println("本批次将要处理：${queryData.size}条数据")
    val startTime = System.currentTimeMillis()
    for (it in queryData) {
        val item = JSONUtil.parseObj(it)
        if (item !is JSONObject) {
            println("不是JSONObject格式")
            continue
        }

        val dir = file_path2 + "${item.getStr("EMPLOYER_NAME")}\\${item.getStr("MAINSTAY_NAME")}\\${
            item.getStr("CREATE_DATE").substring(0, 7)
        }\\"
        if (!FileUtil.exist(dir)) {
            FileUtil.mkdir(dir)
        }

        val zxPlatTrxNo = item.getStr("PLAT_TRX_NO")
        if (FileUtil.exist("${dir}${zxPlatTrxNo}")) {
            println("${zxPlatTrxNo}已存在")
            continue
        }

        val pc04_orgMchOrderNo = item.getStr("REMIT_PLAT_TRX_NO")
        val pc02_mchOrderNo = RandomUtil.randomString(16)
        val pc05_orgOrderTime = item.getStr("CREATE_DATE")
        val mainstayNo = item.getStr("MAINSTAY_NO")

        val (pc01_MerchantNo, key) = MainstayEnum.getByMainstayNo(mainstayNo)

        val sign_str =
            pc01_MerchantNo + pc02_mchOrderNo + pc03_bizType + pc04_orgMchOrderNo + pc05_orgOrderTime + pc06_notifyUrl + key
        val hmac = DigestUtil.md5Hex(sign_str)

        val params = mapOf(
            "pc01_MerchantNo" to pc01_MerchantNo,
            "pc02_mchOrderNo" to pc02_mchOrderNo,
            "pc03_bizType" to pc03_bizType,
            "pc04_orgMchOrderNo" to pc04_orgMchOrderNo,
            "pc05_orgOrderTime" to pc05_orgOrderTime,
            "pc06_notifyUrl" to pc06_notifyUrl,
            "hmac" to hmac
        )

        println("申请生成：${zxPlatTrxNo}")
        var pusResult: String = HttpUtil.post(pusHost, params)
        val resultObj = JSONUtil.parseObj(pusResult)
        val code = resultObj.getInt("code")
        if (code != null && code == 0) {
            println("申请成功：${zxPlatTrxNo}")
        } else {
            println("申请失败：${pusResult}")
        }


        val data = resultObj.getJSONObject("data")
        var mchOrderNo = data.getStr("mchOrderNo")
        var platTrxNo = data.getStr("platTrxNo")

        val responseData = mapOf<String, String>(
            "mchOrderNo" to mchOrderNo,
            "platTrxNo" to platTrxNo,
            "zxPlatTrxNo" to zxPlatTrxNo,
            "mainstayNo" to item.getStr("MAINSTAY_NO"),
            "MAINSTAY_NAME" to item.getStr("MAINSTAY_NAME"),
            "CREATE_DATE" to item.getStr("CREATE_DATE"),
            "EMPLOYER_NAME" to item.getStr("EMPLOYER_NAME"),
            "RECEIVE_ID_CARD_NO" to item.getStr("RECEIVE_ID_CARD_NO"),
            "ENCRYPT_KEY_ID" to item.getStr("ENCRYPT_KEY_ID")
        )
        queryVoucher(responseData)
    }
    val endTime = System.currentTimeMillis()
    println("本批次处理总耗时：${(endTime - startTime) / 1000}秒，共${queryData.size}条数据，每条耗时${((endTime - startTime) / 1000)/queryData.size}秒")
}

fun queryVoucher2(it: Map<String, String>) {
    val mchOrderNo = it["mchOrderNo"]
    val platTrxNo = it["platTrxNo"]
    val zxPlatTrxNo = it["zxPlatTrxNo"]
    val mainstayNo = it["mainstayNo"] ?: throw RuntimeException("代征主体不能为空")

    val pc02_mchOrderNo = mchOrderNo
    val pc03_platTrxNo = platTrxNo
    val (pc01_MerchantNo, key) = MainstayEnum.getByMainstayNo(mainstayNo)
    val sign_str = pc01_MerchantNo + pc02_mchOrderNo + pc03_platTrxNo + key
    val hmac = DigestUtil.md5Hex(sign_str)

    val params = mapOf(
        "pc01_MerchantNo" to pc01_MerchantNo,
        "pc02_mchOrderNo" to pc02_mchOrderNo,
        "pc03_platTrxNo" to pc03_platTrxNo,
        "hmac" to hmac
    )

    var times = 0
    var fileUrl: String?
    do {
        TimeUnit.SECONDS.sleep(2)//等待通道端生成文件
        val qryResult: String = HttpUtil.post(queryHost, params)//查询通道端的文件下载地址
        val resultObj = JSONUtil.parseObj(qryResult)
        fileUrl = resultObj.getJSONObject("data").getStr("fileUrl")
        times ++
    } while (times < 5 && (fileUrl == null || fileUrl == ""))

    zxPlatTrxNo?.let { it1 ->
        fileUrl?.let { it2 -> downloadVoucher(it2, it1, JSONUtil.parseObj(it)) }
    }
}

fun downloadVoucher2(url: String, zxPlatTrxNo: String, item: JSONObject) {
    val zipFileName = file_path2 + url.substring(url.lastIndexOf("/"))
    val dir = url.substring(url.lastIndexOf("/")).split(".")[0]
    val handledDir = file_path2 + "${item.getStr("EMPLOYER_NAME")}\\${item.getStr("MAINSTAY_NAME")}\\${
        item.getStr("CREATE_DATE").substring(0, 7)
    }\\"
    val unzipDir = file_path2 + dir

    try {
        HttpUtil.downloadFile(url, zipFileName)
    } catch (e: Exception) {
        try {
            HttpUtil.downloadFile(url, zipFileName)
        } catch (e: Exception) {
            println("下载失败：${zxPlatTrxNo}，${e.message}")
            return
        }
    }
    ZipUtil.unzip(ZipFile(zipFileName), File(unzipDir))
    val list = FileUtil.listFileNames(unzipDir)
    list.forEach {
        FileUtil.move(File(unzipDir + "/" + it), File("${handledDir}/${zxPlatTrxNo}/$it"), true)
    }
    println("下载成功：${zxPlatTrxNo}")
    FileUtil.del(unzipDir)
    FileUtil.del(zipFileName)
}

fun queryData2(employerNo: String, month: String) {
    val monthDate = DateUtils.parseDateSafe("${month}-01")
    val monthStart = DateUtils.formatDateTime(DateUtils.getMonthStart(monthDate))
    val monthEnd = DateUtils.formatDateTime(DateUtils.getMonthEnd(monthDate))
    val sql: String = """
         select * from tbl_record_item as t where
           t.CREATE_TIME BETWEEN '${monthStart}' AND '${monthEnd}'
           AND t.PROCESS_STATUS = 100
           AND t.EMPLOYER_NO = '${employerNo}'
           AND t.PAY_CHANNEL_NO = 'JOINPAY'
    """.trimIndent()

    val ds: DataSource = SimpleDataSource(
        "*************************************************************************************************************************************************",
        "hjzx",
        "ZIzUQrA&bk1I"
    )
    val db = DbUtil.use(ds)
    var currentPage = 0
    var pageResult: PageResult<Entity>

    do {
        val page = Page.of(currentPage, pageSize)
        pageResult = db.page(sql, page, PageResultHandler(PageResult(page.pageNumber, page.pageSize), false))
        currentPage++
        if (pageResult != null && !pageResult.isEmpty()) {
            applyAllocateRemitVoucher(pageResult)
        }
    } while (pageResult != null && !pageResult.isEmpty())
}

fun queryDataNew2() {
    val file = File("C:\\Users\\<USER>\\Desktop\\需要提供电子回单-河南宝岭.xlsx")
    val objects = EasyExcel.read(file).sheet(1).doReadSync<Any>()
    objects.forEach {
        val item = JSONUtil.parseObj(it)
        val beginOfMonth = DateUtil.format(
            DateUtil.beginOfMonth(DateUtil.parse(item.getStr("0"), "yyyy年MM月")).toJdkDate(),
            "yyyy-MM-dd HH:mm:ss"
        )
        val endOfMonth = DateUtil.format(
            DateUtil.endOfMonth(DateUtil.parse(item.getStr("0"), "yyyy年MM月")).toJdkDate(),
            "yyyy-MM-dd HH:mm:ss"
        )

        val mainstayName = item.getStr("1")
        val recvName = item.getStr("2")
        val mchName = item.getStr("3")

        val sql: String = """
select  * from tbl_record_item t
where 
    t.RECEIVE_NAME_MD5 =lower(md5(concat(md5('${recvName}'),'Zhixiang')))
    and t.MAINSTAY_NAME  like '河南宝岭%'
    and t.PROCESS_STATUS = 100
    and t.EMPLOYER_NAME  = '${mchName}'
    and t.COMPLETE_TIME >='${beginOfMonth}'
    and t.COMPLETE_TIME <='${endOfMonth}'
    and t.PAY_CHANNEL_NO = 'JOINPAY'
    """.trimIndent()

        val ds: DataSource = SimpleDataSource(
            "*************************************************************************************************************************************************",
            "hjzx",
            "ZIzUQrA&bk1I"
        )
        val db = DbUtil.use(ds)

        val entities = db.query(sql)
        if (entities.isEmpty()) {
            println(recvName + " 没有数据")
        } else {
//            println(JSONUtil.toJsonStr(entities))
            applyAllocateRemitVoucherNew2(entities)
        }

    }
}

fun applyAllocateRemitVoucherNew2(queryData: List<Entity>) {
    val pc03_bizType = "ALLOCATE_BANK_TRANSFER"
    val pc06_notifyUrl = "https://banknotify.joinpay.com/test/hello"
    queryData.forEach {
        val item = JSONUtil.parseObj(it)
        if (item is JSONObject) {
            val dir = file_path2 + "${item.getStr("employer_name")}\\${item.getStr("mainstay_name")}\\${
                item.getStr("create_date").substring(0, 7)
            }\\"

            if (!FileUtil.exist(dir)) {
                FileUtil.mkdir(dir)
            }

            if (!FileUtil.exist("${dir}${item.getStr("plat_trx_no")}")) {
                val pc04_orgMchOrderNo = item.getStr("remit_plat_trx_no")
                val pc02_mchOrderNo = RandomUtil.randomString(16)
                val pc05_orgOrderTime = item.getStr("create_date")
                val mainstayNo = item.getStr("mainstay_no")

                val (pc01_MerchantNo, key) = MainstayEnum.getByMainstayNo(mainstayNo)

                val sign_str =
                    pc01_MerchantNo + pc02_mchOrderNo + pc03_bizType + pc04_orgMchOrderNo + pc05_orgOrderTime + pc06_notifyUrl + key
                val hmac = DigestUtil.md5Hex(sign_str)

                val params = mapOf(
                    "pc01_MerchantNo" to pc01_MerchantNo,
                    "pc02_mchOrderNo" to pc02_mchOrderNo,
                    "pc03_bizType" to pc03_bizType,
                    "pc04_orgMchOrderNo" to pc04_orgMchOrderNo,
                    "pc05_orgOrderTime" to pc05_orgOrderTime,
                    "pc06_notifyUrl" to pc06_notifyUrl,
                    "hmac" to hmac
                )

                var post: String = HttpUtil.post(pusHost, params)

                val data = JSONUtil.parseObj(post).getJSONObject("data")
                TimeUnit.MILLISECONDS.sleep(500)
                var mchOrderNo = data.getStr("mchOrderNo")
                var platTrxNo = data.getStr("platTrxNo")

                val responseData = mapOf<String, String>(
                    "mchOrderNo" to mchOrderNo,
                    "platTrxNo" to platTrxNo,
                    "zxPlatTrxNo" to item.getStr("plat_trx_no"),
                    "mainstayNo" to item.getStr("mainstay_no"),
                    "MAINSTAY_NAME" to item.getStr("mainstay_name"),
                    "CREATE_DATE" to item.getStr("create_date"),
                    "EMPLOYER_NAME" to item.getStr("employer_name"),
                    "RECEIVE_ID_CARD_NO" to item.getStr("receive_id_card_no"),
                    "ENCRYPT_KEY_ID" to item.getStr("encrypt_key_id")
                )
                TimeUnit.SECONDS.sleep(10)
                try {
                    queryVoucher(responseData)
                } catch (e: Exception) {
                    println("${item.getStr("plat_trx_no")} 下载失败：${post}")
                }
            } else {
//                println(item.getStr("plat_trx_no")+"已存在")
            }
        }
    }
}


fun queryDataFormIdCard2(idCard: String, date: String, ORDER_NET_AMOUNT: String) {
//    val monthDate = DateUtils.parseDateSafe("${month}-01")
//    val monthStart = DateUtils.formatDateTime(DateUtils.getMonthStart(monthDate))
//    val monthEnd = DateUtils.formatDateTime(DateUtils.getMonthEnd(monthDate))
    val sql: String = """
        SELECT
         *
        FROM
        trade.tbl_record_item t
        WHERE
        t.RECEIVE_ID_CARD_NO_MD5 = LOWER(MD5(CONCAT(MD5('${idCard}'), 'Zhixiang')))
        AND COMPLETE_TIME > '2024-01-01 00:00:00' AND COMPLETE_TIME < '2025-01-01 00:00:00'
        AND t.PROCESS_STATUS = 100
        AND t.PAY_CHANNEL_NO IN ('JOINPAY','JOINPAY_JXH')
        AND MAINSTAY_NO = 'S000025'
        AND CREATE_DATE = '${date}'
    """.trimIndent()

//        val sql: String = """
//        SELECT
//         *
//        FROM
//        trade.tbl_record_item t
//        WHERE
//        PLAT_TRX_NO = 'E20240426005591158'
//    """.trimIndent()

    val ds: DataSource = SimpleDataSource(
        "*************************************************************************************************************************************************",
        "hjzx",
        "ZIzUQrA&bk1I"
    )
    val db = DbUtil.use(ds)
    var currentPage = 0
    var pageResult: PageResult<Entity>

    do {
        val page = Page.of(currentPage, pageSize)
        pageResult = db.page(sql, page, PageResultHandler(PageResult(page.pageNumber, page.pageSize), false))
        if (pageResult.isEmpty() && currentPage == 0) {
            println("没有数据: ${idCard}-${ORDER_NET_AMOUNT}")
        }
        currentPage++
        if (pageResult != null && !pageResult.isEmpty()) {
            applyAllocateRemitVoucher(pageResult)
        }
    } while (pageResult != null && !pageResult.isEmpty())
}

fun applyRe2(idCard: String, date: String, orderNetAmount: String, queryData: List<Entity>) {
    for (it in queryData){
        println("查询流水号,$idCard,$date,$orderNetAmount,${it.getStr("PLAT_TRX_NO")}")
    }

}

fun main() {
    val employerNo = "*********"
    val months = arrayOf("2024-05","2024-06")
    val countDown = CountDownLatch(months.size)
    val executorService = Executors.newFixedThreadPool(months.size)
    for (month in months) {
        executorService.submit {
            try {
//                queryData(employerNo, month)
                val csvFile = File("E:\\huidan\\0311.csv")
                if (FileUtil.exist(csvFile)) {
                    // Use UTF-8 encoding explicitly to handle Chinese characters
                    val csvLines = FileUtil.readLines(csvFile, "UTF-8")
                    val headerLine = csvLines.firstOrNull()
                    if (headerLine != null) {
                        // Process lines starting from the second line (skipping header)
                        csvLines.drop(1).forEach { line ->
                            if (line.isNotBlank()) {
                                try {
                                    // Split the CSV line by comma
                                    val regex = ",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)".toRegex()
                                    val columns = line.split(regex)
                                    if (columns.isNotEmpty()) {
                                        // Use the correct index for ID card number
                                        val idCard = columns[1].trim()
                                        // Remove thousands separator commas from amount
                                        val amount = columns[5].trim()
                                        val date = DateUtil.format(DateUtil.parse(columns[3].trim(), "yyyy/M/d"), "yyyy-MM-dd")
                                        println("Processing : $idCard - $date - $amount")
                                        queryDataFormIdCard2(idCard,date,amount)
                                    }
                                } catch (e: Exception) {
                                    println("Error processing line ${csvLines.indexOf(line) + 1}: $line - ${e.message}")
                                }
                            }
                        }
                    }
                } else {
                    println("CSV file not found: ${csvFile.absolutePath}")
//                    queryDataFormIdCard("******************","1111") // Fallback to default
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                countDown.countDown()
                println("${month}已处理完毕 <-------------")
            }
        }
    }
    countDown.await()
}

