package com.zhixianghui.voucher

import cn.hutool.core.io.IoUtil
import cn.hutool.core.lang.Tuple
import cn.hutool.core.util.RandomUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.db.Db
import cn.hutool.db.DbUtil
import cn.hutool.db.ds.simple.SimpleDataSource
import cn.hutool.poi.excel.ExcelUtil
import com.zhixianghui.common.statics.constants.common.EncryptKeys
import com.zhixianghui.common.util.utils.AESUtil
import com.zhixianghui.common.util.utils.MD5Util
import java.io.FileReader
import javax.sql.DataSource

val  start = arrayOf("133", "149", "153", "173", "177",

    "180", "181", "189", "199", "130", "131", "132",

    "145", "155", "156", "166", "171", "175", "176", "185", "186", "166", "134", "135",

    "136", "137", "138", "139", "147", "150", "151", "152", "157", "158", "159", "172",

    "178", "182", "183", "184", "187", "188", "198", "170", "171");

fun main() {
    val addPhoneSrcipt = AddPhoneSrcipt()
    val datas = addPhoneSrcipt.readFile("C:\\Users\\<USER>\\Documents\\WXWork\\1688850676062838\\Cache\\File\\2024-09\\方联代开匹配手机号 2024.5-7-线下提供(1).xlsx")
    val ds: DataSource = SimpleDataSource("*************************************************************************************************************************************************", "hjzx", "ZIzUQrA&bk1I")
    val db = DbUtil.use(ds)
    datas.forEach {
        val trxNo = it.get<String>(0)
        val phoneNo = it.get<String>(1)
        println("${trxNo} ${phoneNo}")
        addPhoneSrcipt.updataDbData(trxNo, phoneNo, db)
    }
}

class AddPhoneSrcipt {

    fun readFile(path:String):List<Tuple>{
        val lines: List<String> = ArrayList()
//        val s:List<String> = IoUtil.readLines(FileReader(path),lines)
        var s = ExcelUtil.getReader(path).read(1)


        val datas: java.util.ArrayList<Tuple> = java.util.ArrayList()
        s.forEach {
//            val lineItemArray = it.split("\t")
            if (it.size==2) {
//                println("${it.get(0)} ${it.get(1)}")
                val tuple = Tuple(it.get(0).toString().trim(), it.get(1))
                datas.add(tuple)
            }else{
//                println("${it.get(0)}")
//                val tuple = Tuple(lineItemArray[0],genPhone())
//                datas.add(tuple)
            }
        }
        return datas
    }

    fun genPhone():String{

        val phoneFirstNum = start[(Math.random() * start.size).toInt()]
        var end = StrUtil.join("", RandomUtil.randomInts(4))
        val phone = "${phoneFirstNum}****${end}"
        return phone
    }

    fun updataDbData(trxNo: String, phoneNo: String, db: Db) {
        val data = db.queryOne("""
            select t.ENCRYPT_KEY_ID,t.RECEIVE_PHONE_NO_MD5  from 
            	tbl_order_item t
            where
            	MAINSTAY_NO = 'S000039'
            	and 
            EMPLOYER_NO ='M00000685'
            	and 
            MCH_ORDER_NO = '${trxNo}'
            and CREATE_TIME >= '2024-05-01 00:00:00'
            and CREATE_TIME <= '2024-07-31 23:59:59'
        """.trimIndent())
        if (StrUtil.isNotBlank(data.getStr("RECEIVE_PHONE_NO_MD5"))) {
            return
        }

        val dataRecord = db.queryOne("""
            select t.ENCRYPT_KEY_ID  from 
            	tbl_order_item t
            where
            	MAINSTAY_NO = 'S000039'
            	and 
            EMPLOYER_NO ='M00000685'
            	and 
            MCH_ORDER_NO = '${trxNo}'
            and CREATE_TIME >= '2024-05-01 00:00:00'
            and CREATE_TIME <= '2024-07-31 23:59:59'
        """.trimIndent())


        val phoneEncrypt = AESUtil.encryptECB(phoneNo, EncryptKeys.getEncryptKeyById(data.getLong("ENCRYPT_KEY_ID")).encryptKeyStr)
        var phoneMd5 = MD5Util.getMixMd5Str(phoneNo)


        val phoneEncrypt1 = AESUtil.encryptECB(phoneNo, EncryptKeys.getEncryptKeyById(dataRecord.getLong("ENCRYPT_KEY_ID")).encryptKeyStr)
        var phoneMd51 = MD5Util.getMixMd5Str(phoneNo)

        val sql ="""
            update
            	tbl_order_item t
            set
            	t.RECEIVE_PHONE_NO = '${phoneEncrypt}',
            	t.RECEIVE_PHONE_NO_MD5 = '${phoneMd5}'
            where
            	MAINSTAY_NO = 'S000039'
            	and 
            EMPLOYER_NO ='M00000685'
            	and 
            MCH_ORDER_NO = '${trxNo}'
                and CREATE_TIME >= '2024-05-01 00:00:00'
                and CREATE_TIME <= '2024-07-31 23:59:59'
        """.trimIndent()

        val sqlRecord = """
            update tbl_record_item  t set 
            	t.RECEIVE_PHONE_NO = '${phoneEncrypt1}',
            	t.RECEIVE_PHONE_NO_MD5 ='${phoneMd51}'
            where 
            	MAINSTAY_NO = 'S000039'
            	and 
            EMPLOYER_NO ='M00000685'
            	and 
            MCH_ORDER_NO = '${trxNo}'
                and CREATE_TIME >= '2024-05-01 00:00:00'
                and CREATE_TIME <= '2024-07-31 23:59:59'
        """.trimIndent()
        db.tx {
            it.execute(sql)
            it.execute(sqlRecord)
            println("更新数据[${trxNo}]完成")
        }
    }
}


