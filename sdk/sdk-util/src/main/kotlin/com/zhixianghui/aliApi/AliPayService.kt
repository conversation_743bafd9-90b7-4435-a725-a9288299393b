package com.zhixianghui.aliApi

import cn.hutool.json.JSONUtil
import cn.hutool.setting.dialect.Props
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.api.AlipayApiException
import com.alipay.api.AlipayClient
import com.alipay.api.CertAlipayRequest
import com.alipay.api.DefaultAlipayClient
import com.alipay.api.request.*
import com.alipay.api.response.*
import com.zhixianghui.aliPayService
import com.zhixianghui.common.statics.dto.banklink.ParticipantsInfo
import com.zhixianghui.common.statics.exception.BizException
import com.zhixianghui.common.statics.exception.CommonExceptions
import lombok.extern.slf4j.Slf4j
import org.apache.commons.lang3.StringUtils
val env = "alipay_config.properties"
//val env = "alipay_config_cy.properties"
fun main() {
//    val response = AliPayService().accountBookQuery("20226013899043530771", "****************")?.printJsonPrettyStr()
//    val response0 = AliPayService().billApply("20230918110070101506510062119112","20235407032407177882")?.print()
//    AliPayService().transCommonQuery("*************************","ENTRUST_TRANSFER","SINGLE_TRANSFER_NO_PWD")?.printJsonPrettyStr()
//    AliPayService().transCommonQuery("R20230206001522995","ENTRUST_ALLOCATION","SINGLE_TRANSFER_NO_PWD")?.printJsonPrettyStr()
//    AliPayService().billQuery("*********","20235407032407177882")?.print()
//    val transCommonQueryByOrderId = AliPayService().transCommonQueryByOrderId("20230918110070101506510062119112")?.printJsonPrettyStr()
//    AliPayService().bizFundAgentQuery("20225416881659413991")?.printJsonPrettyStr()

//    withdrawToBankCard()

//    testTranstoPersonal()
    AliPayService().accountBookQuery("20235601980980813333","****************")?.printJsonPrettyStr()
}

fun withdrawToBankCard() {
    val payee = ParticipantsInfo()
    payee.identity = "***************"
    payee.organizationName = "招商银行"
    payee.name = "广州乡伴网络科技有限公司"
    payee.bankChannelNo = "************"

    val payer = ParticipantsInfo()
    payer.agreementNo = "20235407032407177882"
    payer.identity = "****************"
    AliPayService().withdrawToBank(
        bankOrderNo = "WD20230910001",
        receiveAmount = "43249.66",
        remitRemark = "广州乡伴网络科技有限公司支付宝余额人工提现到银行卡",
        payee,
        payer
    )
}

fun testTranstoPersonal() {
    val payee = ParticipantsInfo()
    payee.identity = "<EMAIL>"
    payee.name = "广州乡伴网络科技有限公司"

    val payer = ParticipantsInfo()
    payer.agreementNo = "20225409824866906006"
    payer.accountbookSceneCode = "SATF_FUND_BOOK"
    payer.identity = "****************"

    AliPayService().transferToAlipay(
        outBizNo = "*********************",
        amt = "13451.07", title = "乡伴提现",
        realPayerName = "广州乡伴网络科技有限公司",
        payee = payee,
        payer = payer
    )
}

@Slf4j
class AliPayService {


    fun loadConfig():AlipayConfig{
        val props = Props(env)
        val configBean:AlipayConfig = props.toBean(AlipayConfig::class.java)
        return configBean
    }

    fun getRequest():CertAlipayRequest{
        val alipayConfig = loadConfig()
        val certAlipayRequest = CertAlipayRequest()
        certAlipayRequest.privateKey = alipayConfig.merchantPrivateKey
        certAlipayRequest.appId = alipayConfig.appId
        certAlipayRequest.signType = alipayConfig.signType
        certAlipayRequest.serverUrl = alipayConfig.serverUrl
        certAlipayRequest.alipayPublicCertPath = alipayConfig.alipayCertPath
        certAlipayRequest.certPath = alipayConfig.appCertPath
        certAlipayRequest.charset = alipayConfig.charSet
        certAlipayRequest.rootCertPath = alipayConfig.alipayRootCertPath

        return certAlipayRequest
    }

    @Throws(AlipayApiException::class)
    fun bizFundAgentQuery(agreementNo: String):String? {
        val certAlipayRequest = getRequest()
        val alipayClient: AlipayClient = DefaultAlipayClient(certAlipayRequest)

        val bizParams = mapOf(
            "start_time" to "2023-02-06 15:00:00",
            "end_time" to "2023-02-06 16:00:00",
            "page_no" to "1",
            "page_size" to "1000",
            "agreement_no" to agreementNo
        )

        val bizfundagentQueryRequest = AlipayDataBillBizfundagentQueryRequest()
        bizfundagentQueryRequest.bizContent = JSON.toJSONString(bizParams)
        val response = alipayClient.certificateExecute(bizfundagentQueryRequest)
        return response.body
    }

    @Throws(AlipayApiException::class)
    fun transCommonQueryByOrderId(orderId: String?): String? {
        val certAlipayRequest = getRequest()

        val alipayClient: AlipayClient = DefaultAlipayClient(certAlipayRequest)
        val bizParams = mapOf("order_id" to orderId)

        val request = AlipayFundTransCommonQueryRequest()
        request.bizContent = JSONObject.toJSONString(bizParams) //设置业务参数
        val response = alipayClient.certificateExecute(request)
        return if (response.isSuccess) {
            response.body
        } else if (StringUtils.equals(
                "INVALID_PARAMETER",
                response.subCode
            ) && StringUtils.equals("参数有误order_id格式不正确！", response.subMsg)
        ) {
            val bizParams2 = mapOf("pay_fund_order_id" to orderId)
            request.bizContent = JSONObject.toJSONString(bizParams2) //设置业务参数
            val response2 = alipayClient.certificateExecute(request)
            if (response2.isSuccess) {
                response2.body
            } else {
                throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("外部订单号查询支付宝转账记录异常：" + response2.subMsg)
            }
        } else {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("外部订单号查询支付宝转账记录异常：" + response.subMsg)
        }
    }

    @Throws(AlipayApiException::class)
    fun accountBookQuery(agreementNo: String, accountBookId: String): String? {
        val certAlipayRequest = getRequest()

        val alipayClient: AlipayClient = DefaultAlipayClient(certAlipayRequest)

        val extInfo = mapOf("agreement_no" to agreementNo)
        val bizParams = mapOf(
            "account_book_id" to accountBookId,
            "scene_code" to "SATF_FUND_BOOK",
            "ext_info" to JSONObject.toJSONString(extInfo)
        )
        val request = AlipayFundAccountbookQueryRequest()
        request.bizContent = JSONObject.toJSONString(bizParams) //设置业务参数
        val response = alipayClient.certificateExecute(request) //通过alipayClient调用API，获得对应的response类
        return response.body
    }

    /**
     * 支付宝账单通用查询
     */
    @Throws(AlipayApiException::class)
    fun transCommonQuery(outBizNo: String, bizScene: String, productCode: String): String? {
        val alipayClient: AlipayClient = DefaultAlipayClient(getRequest())
        val bizParams = mapOf("product_code" to productCode,
            "biz_scene" to bizScene,
            "out_biz_no" to outBizNo)
        val request = AlipayFundTransCommonQueryRequest()
        request.bizContent = JSONObject.toJSONString(bizParams) //设置业务参数
        val response = alipayClient.certificateExecute(request) //通过alipayClient调用API，获得对应的response类
        return response.body
    }

    /**
     * 记账本充值订单查询
     * @param outBizNo
     * @return
     * @throws AlipayApiException
     */
    @Throws(AlipayApiException::class)
    fun transQuery(outBizNo: String): String? {
        val alipayClient: AlipayClient = DefaultAlipayClient(getRequest())
        val bizParams: MutableMap<String, String> = HashMap()
        bizParams["product_code"] = "FUND_ACCOUNT_BOOK"
        bizParams["biz_scene"] = "COMANAGER_SATF_DEPOSIT"
        bizParams["out_biz_no"] = outBizNo
        val request = AlipayFundTransCommonQueryRequest()
        request.bizContent = JSONObject.toJSONString(bizParams) //设置业务参数
        val response = alipayClient.certificateExecute(request) //通过alipayClient调用API，获得对应的response类
        return response.body
    }

    @Throws(AlipayApiException::class)
    fun billApply(key: String, agreementNo: String): String? {
        val alipayClient: AlipayClient = DefaultAlipayClient(getRequest())
        val bizParams: MutableMap<String,String> = HashMap()
        bizParams["type"] = "FUND_DETAIL"
        bizParams["key"] = key
        bizParams["agreement_no"] = agreementNo
        val req = AlipayDataBillEreceiptagentApplyRequest()
        req.bizContent = JSON.toJSONString(bizParams)
        val response = alipayClient.certificateExecute(req)
        println(JSONUtil.toJsonStr(response))
        return response.fileId
    }

    @Throws(AlipayApiException::class, BizException::class)
    fun billQuery(fileId: String, agreementNo: String): String? {
        val alipayClient: AlipayClient = DefaultAlipayClient(getRequest())
        val bizParams = mapOf("file_id" to fileId,"agreement_no" to agreementNo)
        val req = AlipayDataBillAccountbookereceiptQueryRequest()
        req.bizContent = JSON.toJSONString(bizParams)
        val response = alipayClient.certificateExecute(req)
        println(JSONUtil.toJsonPrettyStr(response))
        return response.downloadUrl
    }

    @Throws(AlipayApiException::class)
    fun uniTransfer(
        outBizNo: String?,
        amt: String?,
        title: String?,
        payee: ParticipantsInfo,
        payer: ParticipantsInfo,
        remark: String?
    ): String? {
        val alipayClient: AlipayClient = DefaultAlipayClient(getRequest())
        val extInfo: MutableMap<String, Any> = HashMap()
        extInfo["agreement_no"] = payee.agreementNo
        extInfo["accountbook_scene_code"] = "SATF_FUND_BOOK"
        val payeeInfo: MutableMap<String, Any> = HashMap()
        payeeInfo["identity"] = payee.identity
        payeeInfo["identity_type"] = "ACCOUNT_BOOK_ID"
        payeeInfo["ext_info"] = JSON.toJSONString(extInfo)
        val payerExtInfo: MutableMap<String, Any> = HashMap()
        payerExtInfo["agreement_no"] = payer.agreementNo
        payerExtInfo["accountbook_scene_code"] = payer.accountbookSceneCode
        val payerInfo: MutableMap<String, Any> = HashMap()
        payerInfo["identity"] = payer.identity
        payerInfo["identity_type"] = "ACCOUNT_BOOK_ID"
        payerInfo["ext_info"] = JSON.toJSONString(payerExtInfo)
        val bizParams = mapOf(
            "out_biz_no" to outBizNo,
            "trans_amount" to amt,
            "product_code" to "SINGLE_TRANSFER_NO_PWD",
            "biz_scene" to "ENTRUST_ALLOCATION",
            "payee_info" to payeeInfo,
            "order_title" to title,
            "payer_info" to payerInfo,
            "remark" to remark
        )

        val request = AlipayFundTransUniTransferRequest()
        request.bizContent = JSON.toJSONString(bizParams)
        val response = alipayClient.certificateExecute(request)
        println(JSONUtil.toJsonPrettyStr(response))
        return response.body
    }

    @Throws(AlipayApiException::class)
    fun transferToAlipay(
        outBizNo: String?,
        amt: String?,
        title: String?,
        realPayerName: String,
        payee: ParticipantsInfo,
        payer: ParticipantsInfo
    ): String? {
        val alipayClient: AlipayClient = DefaultAlipayClient(getRequest())
        val payeeInfo: MutableMap<String, Any> = HashMap()
        payeeInfo["identity"] = payee.identity
        payeeInfo["identity_type"] = "ALIPAY_LOGON_ID"
        payeeInfo["name"] = payee.name
        val payerExtInfo: MutableMap<String, Any> = HashMap()
        payerExtInfo["agreement_no"] = payer.agreementNo
        payerExtInfo["accountbook_scene_code"] = payer.accountbookSceneCode
        val payerInfo: MutableMap<String, Any> = HashMap()
        payerInfo["identity"] = payer.identity
        payerInfo["identity_type"] = "ACCOUNT_BOOK_ID"
        payerInfo["ext_info"] = JSON.toJSONString(payerExtInfo)
        val bizParams: MutableMap<String, Any?> = HashMap()
        bizParams["out_biz_no"] = outBizNo
        bizParams["trans_amount"] = amt
        bizParams["product_code"] = "SINGLE_TRANSFER_NO_PWD"
        bizParams["biz_scene"] = "ENTRUST_TRANSFER"
        bizParams["payee_info"] = payeeInfo
        bizParams["order_title"] = title
        bizParams["payer_info"] = payerInfo
        bizParams["remark"] = title
        bizParams["business_params"] = "{\"payer_show_name\":\"$realPayerName\"}"
        val request = AlipayFundTransUniTransferRequest()
        request.bizContent = JSON.toJSONString(bizParams)
        request.notifyUrl = "https://notify-dev.hjzxh.com/callback/ali"
        val response = alipayClient.certificateExecute(request)
        println(JSONUtil.toJsonPrettyStr(response.body))
        return response.body
    }

    /**
     * 提现到银行卡
     * @param bankOrderNo
     * @param receiveAmount
     * @param remitRemark
     * @param payee
     * @param payer
     * @return
     * @throws AlipayApiException
     */
    @Throws(AlipayApiException::class)
    fun withdrawToBank(
        bankOrderNo: String?,
        receiveAmount: String?,
        remitRemark: String?,
        payee: ParticipantsInfo,
        payer: ParticipantsInfo
    ): String? {
        val alipayClient: AlipayClient = DefaultAlipayClient(getRequest())
        val bankCardExt: MutableMap<String, String> = HashMap()
        bankCardExt["account_type"] = "1"
        bankCardExt["inst_name"] = payee.organizationName
        bankCardExt["bank_code"] = payee.bankChannelNo
        val payeeInfoMap: MutableMap<String, Any> = HashMap()
        payeeInfoMap["identity"] = payee.identity
        payeeInfoMap["identity_type"] = "BANKCARD_ACCOUNT"
        payeeInfoMap["name"] = payee.name
        payeeInfoMap["bankcard_ext_info"] = bankCardExt
        val payerExtInfoMap: MutableMap<String, String> = HashMap()
        payerExtInfoMap["agreement_no"] = payer.agreementNo
        payerExtInfoMap["accountbook_scene_code"] = "SATF_FUND_BOOK"
        val payerInfoMap: MutableMap<String, String> = HashMap()
        payerInfoMap["identity"] = payer.identity
        payerInfoMap["identity_type"] = "ACCOUNT_BOOK_ID"
        payerInfoMap["ext_info"] = JSON.toJSONString(payerExtInfoMap)
        val bizParams: MutableMap<String, Any?> = HashMap()
        bizParams["out_biz_no"] = bankOrderNo
        bizParams["trans_amount"] = receiveAmount
        bizParams["product_code"] = "SINGLE_TRANSFER_NO_PWD"
        bizParams["biz_scene"] = "ENTRUST_TRANSFER"
        bizParams["order_title"] = remitRemark
        bizParams["remark"] = remitRemark
        val businessParams: MutableMap<String, Any?> = HashMap()
        businessParams["withdraw_timeliness"] = "T0"
        bizParams["business_params"] = JSON.toJSONString(businessParams)
        bizParams["payee_info"] = payeeInfoMap
        bizParams["payer_info"] = payerInfoMap
        val request = AlipayFundTransUniTransferRequest()
        request.bizContent = JSON.toJSONString(bizParams)
        request.notifyUrl = "https://notify-dev.hjzxh.com/callback/ali"
        val response = alipayClient.certificateExecute(request)
        println(response.body)
        return response.body
    }
}

fun String.printJsonPrettyStr(){
    println("\n|------------------------美化输出json字符串·开始----------------------------|")
    println(JSONUtil.toJsonPrettyStr(this))
    println("|--------------------------美化输出json字符串·结束----------------------------|")
}

fun String.print() {
    println(this)
}
