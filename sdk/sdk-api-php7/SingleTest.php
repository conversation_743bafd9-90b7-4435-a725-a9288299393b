<?php
/**
 * 测试入口类 -> 单笔交易
 *
 * PHP VERSION = PHP 7.2.20
 */
require "Autoload.php";

use zhi<PERSON><PERSON><PERSON>\Request;
use zhixia<PERSON><PERSON>\SecretKey;
use zhixia<PERSON><PERSON>\RequestUtil;
use zhixia<PERSON><PERSON>\RandomUtil;
use zhixia<PERSON><PERSON>\AESUtil;

//平台公钥
$platPublicKey = "-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCmlvupij5/FnXeLRyIKhHVwhCRVkf1rvmfKRX1yKZ5mFt7sCNvuq0uLpBwZQOokgAEfZ3xEpq0acj9mXjdu4+69DXWk3a+w2rSOEb7qBi3WMpz5J68z/QBqqlwKXtPZJgECAHdbqDQ3sVz4hUtN6Xbaj415AmVYEaKolnb3cmdCwIDAQAB
-----END PUBLIC KEY-----";

//商户私钥
$mchPrivateKey = "-----B<PERSON>IN RSA PRIVATE KEY-----
MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAJM9L30CJeS9QOoZWIooa7Ov1g5oKP2DSnWQcpvExE/S3JYHYqByBrVFsy9+UofOARHQmKUuMSvpTZdkIhC17gg/mqHwGavbNpE9lVwoa2F9Uriq1oUKnNTCEcAXIXIZ+bUt/qd0UTjLA6MjK6jphDX2WjPLml66Gm14Anu2iQRbAgMBAAECgYEAi+fqtM0StqwB/69/yx+nX0Eok93zfRiA6v8Ld+nbY8IIKmLwG2bd6udE4U8WGsbri0L7mJEw1fnE6aKTH+/PMf8jQoJjmyq7YhRbLwjdGWq5PMtA4AGX0ZK1r7yBP39o+XMeX/bq98Dhe1eQNmWOlhrmqpHYatvbpBRZswdmGfkCQQDlfyR1acVDtvFMfBY2TeCfsqM9BlHFTbSFe29L7TygmgYmEp0z7JiIxTaoS/tQ5fE3lAiQdYjpRm4FTVAojQIfAkEApD4r4gCnUa4qV2ojQvsW8beEm2iyw+XPuM9qIwnEguFiWGLRG7+eY4/dTvNFXlqlJg3P7vGX+2pRgrEgpd5ORQJAR7TDMoB651545Jn84rjQj3VkdcPMtuZBmldn/gRBJjkZ5Ll6Lugk/M8J9enPu4YtKV1yk5h1z9V2uOdgPVtZpwJBAJh6B29XP/56fGCO7pF+XXyl7PwCJQPs0/00wcophUAkUZLQmmTybe7sXn6vJhVEfdFoPUQNEhWcTHPSUQbr7SkCQQDh5N5f+fDShNcoJ50VGUpIsUD4lAf4GKuYPKiMsVni4vV1bTJMUWWZJKx8qvIEbfo9i7ZTiWlndryoTr8oZO/p
-----END RSA PRIVATE KEY-----";
$maxCount = 5;
$totalCount = 0;
$totalAmount = 0;
$secKey = RandomUtil::randomStr(16);

$data = [];
$data['mch_order_no'] = RandomUtil::randomStr(32);
$data['mainstay_no'] = 'S000003';
$data['channel_type'] = '1';
$data['receive_name'] = AESUtil::encryptECB('张三', $secKey);
$data['receive_id_card_no'] = AESUtil::encryptECB('******************', $secKey);
$data['receive_account_no'] = AESUtil::encryptECB('****************', $secKey);
$data['receive_phone_no'] = AESUtil::encryptECB('***********', $secKey);
$data['order_item_net_amount'] = '0.12';
$data['callback_url'] = 'http://www.baidu.com';

//加密

$dataStr = json_encode($data, JSON_UNESCAPED_UNICODE); //JSON_UNESCAPED_UNICODE避免中文转码

$request = new Request();
$request->setMethod("zxh.singleGrant");
$request->setVersion("1.0");
$request->setMchNo("M00000421");
$request->setSignType("1");
$request->setRandStr(RandomUtil::randomStr(32));
$request->setData($dataStr);
$request->setSecKey($secKey);//rsa有效

$secretKey = new SecretKey();
$secretKey->setReqSignKey($mchPrivateKey);//签名：使用商户私钥
$secretKey->setRespVerifyKey($platPublicKey);//验签：使用平台公钥
$secretKey->setSecKeyEncryptKey($platPublicKey);//sec_key加密：使用平台公钥
$secretKey->setSecKeyDecryptKey($mchPrivateKey);//sec_key解密：使用商户私钥


print_r($request);
$url = "https://api-dev.hjzxh.com/trade";
try {
    $response = RequestUtil::doRequest($url, $request, $secretKey);

    echo "响应数据为 Response = ";
    print_r($response);
} catch (Exception $e) {
    print_r($e);
}

