package com.zhixianghui.service.riskcontrol;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.riskcontrol.entity.StrategyAtom;
import com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup;
import com.zhixianghui.service.riskcontrol.core.biz.StrategyAtomGroupBiz;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@SpringBootTest(classes = ServiceRiskControlApp.class)
@RunWith(SpringRunner.class)
@Slf4j
public class StrategyAtomGroupTest {

    @Autowired
    private StrategyAtomGroupBiz strategyAtomGroupBiz;

    @Test
    public void create() {
//        StrategyAtomGroup strategyAtomGroup = new StrategyAtomGroup();
//        strategyAtomGroup.setCreateTime(new Date());
//        strategyAtomGroup.setWeight(222);
//        strategyAtomGroup.setControlAtom(ControlAtomEnum.PENDING.getValue());
//        strategyAtomGroup.setRemark("222222222");
//        strategyAtomGroup.setRuleId(1L);
//
//        List<StrategyAtom> atomList = new ArrayList<>();
//
//        StrategyAtom atom1 = new StrategyAtom();
//        atom1.setCreateTime(new Date());
//        atom1.setVariable(StrategyAtomVariableEnum.USER_AGE.getValue());
//        atom1.setOperator(StrategyAtomOperatorEnum.GREATER_THAN.getValue());
//        atom1.setConstant(new BigDecimal("10"));
//        atomList.add(atom1);
//
//        StrategyAtom atom2 = new StrategyAtom();
//        atom2.setCreateTime(new Date());
//        atom2.setVariable(StrategyAtomVariableEnum.USER_MONTH_AMOUNT.getValue());
//        atom2.setOperator(StrategyAtomOperatorEnum.GREATER_THAN.getValue());
//        atom2.setConstant(new BigDecimal("100"));
//
//        atomList.add(atom2);
//
//        strategyAtomGroup.setStrategyAtomList(atomList);
//        strategyAtomGroupBiz.create(strategyAtomGroup, "");
    }

    @Test
    public void listByRuleId() {
        log.info(JsonUtil.toString(strategyAtomGroupBiz.listByRuleId(1), SerializerFeature.PrettyFormat));
    }

    @Test
    public void getById() {
        log.info(JsonUtil.toString(strategyAtomGroupBiz.getById(2), SerializerFeature.PrettyFormat));
    }

    @Test
    public void deleteById() {
        log.info(JsonUtil.toString(strategyAtomGroupBiz.getById(4), SerializerFeature.PrettyFormat));

        //strategyAtomGroupBiz.deleteById(4);

        log.info(JsonUtil.toString(strategyAtomGroupBiz.getById(4), SerializerFeature.PrettyFormat));
    }

    @Test
    public void update() {
//        StrategyAtomGroup strategyAtomGroup = strategyAtomGroupBiz.getById(1);
//        strategyAtomGroup.setControlAtom(ControlAtomEnum.REJECT.getValue());
//        strategyAtomGroup.setWeight(333);
//        strategyAtomGroup.setRemark("333");
//
//        List<StrategyAtom> atomList = new ArrayList<>();
//
//        StrategyAtom atom1 = new StrategyAtom();
//        atom1.setCreateTime(new Date());
//        atom1.setVariable(StrategyAtomVariableEnum.USER_AGE.getValue());
//        atom1.setOperator(StrategyAtomOperatorEnum.GREATER_THAN.getValue());
//        atom1.setConstant(new BigDecimal("9"));
//        atomList.add(atom1);
//
//        StrategyAtom atom2 = new StrategyAtom();
//        atom2.setCreateTime(new Date());
//        atom2.setVariable(StrategyAtomVariableEnum.USER_MONTH_AMOUNT.getValue());
//        atom2.setOperator(StrategyAtomOperatorEnum.GREATER_THAN.getValue());
//        atom2.setConstant(new BigDecimal("99"));
//        atomList.add(atom2);
//
//        strategyAtomGroup.setStrategyAtomList(atomList);
//
//        strategyAtomGroupBiz.update(strategyAtomGroup, "");
//
//        log.info(JsonUtil.toString(strategyAtomGroupBiz.getById(1), SerializerFeature.PrettyFormat));
    }
}
