package com.zhixianghui.service.riskcontrol;

import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.service.riskcontrol.core.biz.RiskControlBiz;
import com.zhixianghui.service.riskcontrol.core.biz.RiskcontrolProcessBiz;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;

/**
 * @description: 风控测试类
 * @author: xingguang li
 * @created: 2020/11/17 09:24
 */
@SpringBootTest(classes = ServiceRiskControlApp.class)
@RunWith(SpringRunner.class)
@Slf4j
public class RiskControlTest {

    @Autowired
    private RiskControlBiz riskControlBiz;
    @Autowired
    private RiskcontrolProcessBiz riskcontrolProcessBiz;

    @Test
    public void testProcess(){
        SettleRiskControlVo settleRiskControlVo = new SettleRiskControlVo();
        settleRiskControlVo.setEmployerNo("M00000031");
        settleRiskControlVo.setOrderAmount(new BigDecimal(222));
        settleRiskControlVo.setOrderNo("I20201117000000524");
        settleRiskControlVo.setSupplierNo("S000015");
        settleRiskControlVo.setUserIdCard("110101199003077715");
        settleRiskControlVo.setUserName("张三");
        settleRiskControlVo.setPlatTrxNo("I20201122000985458");
        riskControlBiz.processRiskControl(settleRiskControlVo);
//        Map<String,Object> param = new HashMap<>();
//        PageResult<List<RiskcontrolOrderItem>> lists = riskcontrolProcessBiz.listPagePendingOrder(param, PageParam.newInstance(1,10));
//        log.info(JsonUtil.toString(lists));
    }
}
