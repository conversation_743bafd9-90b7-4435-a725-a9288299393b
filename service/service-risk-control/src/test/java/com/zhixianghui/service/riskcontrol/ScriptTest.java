package com.zhixianghui.service.riskcontrol;

import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum;
import org.junit.Test;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

/**
 * @description: test
 * @author: xingguang li
 * @created: 2020/10/20 14:54
 */
public class ScriptTest {

    @Test
    public void scriptTest() throws ScriptException {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        Object result = engine.eval("1>2");
        System.out.println(Boolean.valueOf(result.toString()).equals(false));
        System.out.println(StrategyAtomVariableEnum.values());
    }
}
