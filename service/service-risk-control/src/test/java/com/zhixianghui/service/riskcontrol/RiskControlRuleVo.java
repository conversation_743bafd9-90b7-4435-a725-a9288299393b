package com.zhixianghui.service.riskcontrol;

import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskAccessPointEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskRuleTypeEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RuleRangeOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RuleRangeVariableEnum;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class RiskControlRuleVo {

    private Long id;

    /**
     * 风控规则名称
     */
    @NotEmpty(message = "规则名称不能为空")
    private String name;

    /**
     * 风控规则状态
     * {@link OpenOffEnum#getValue()}
     */
    @NotNull(message = "规则状态不能为空")
    private Integer status;

    /**
     * 风控规则接入点
     * {@link RiskAccessPointEnum#getValue()}
     */
    @NotNull(message = "风控接入点不能为空")
    private Integer accessPoint;

    /**
     * 风控规则类型
     * {@link RiskRuleTypeEnum#getValue()}
     */
    @NotNull(message = "规则类型不能为空")
    private Integer type;

    /**
     * 供应商编号
     */
    private String supplierNos;

    /**
     * 供应商名称
     */
    private String supplierNames;

    /**
     * 规则适用范围
     */
    @Valid
    private List<RuleRangeVo> ruleRangeList;

    @Data
    public static class RuleRangeVo {

        /**
         * 条件变量
         * {@link RuleRangeVariableEnum#getValue()}
         */
        @NotNull(message = "规则范围变量不能为空")
        private Integer variable;

        /**
         * 运算符
         * {@link RuleRangeOperatorEnum#getValue()}
         */
        @NotNull(message = "规则范围运算符不能为空")
        private Integer operator;

        /**
         * 常量值
         */
        @NotNull(message = "规则范围常量值不能为空")
        @Digits(integer = 10, fraction = 2, message = "规则范围常量值格式错误")
        private BigDecimal constant;
    }
}
