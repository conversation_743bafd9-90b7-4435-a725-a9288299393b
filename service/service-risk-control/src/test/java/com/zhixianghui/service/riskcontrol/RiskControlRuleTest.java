package com.zhixianghui.service.riskcontrol;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskAccessPointEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskRuleTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import com.zhixianghui.facade.riskcontrol.entity.RuleRange;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.service.riskcontrol.core.biz.RiskControlRuleBiz;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest(classes = ServiceRiskControlApp.class)
@RunWith(SpringRunner.class)
@Slf4j
public class RiskControlRuleTest {

    @Autowired
    private RiskControlRuleBiz riskControlRuleBiz;

    @Test
    public void testReturn(){
        String ss = getStr(3);
        System.out.println(ss);
    }

    private String getStr(int i){
        List<String> list = new ArrayList<>();
        list.add("1");
        list.add("2");
        list.add("3");
        list.add("4");
        list.add("5");
        for (int j = 3;j>0;j--){
            list.forEach(item->{
                if (item.equals("3")){
                    System.out.println("hahaha");
                    return;
                }
            });
            System.out.println("inner");
        }
        return "yes";
    }

    @Test
    public void test(){
        String str = "{\"id\":8,\"version\":0,\"createTime\":\"2020-10-22 16:22:26\",\"updateTime\":null,\"updator\":\"\",\"name\":\"1111\",\"status\":\"100\",\"accessPoint\":\"100\",\"type\":\"0\",\"supplierNos\":\"S000015,S000014\",\"supplierNames\":\"啊啊啊,1111\",\"ruleRangeList\":[]}";
        RiskControlRuleVo vo = JsonUtil.toBean(str, RiskControlRuleVo.class);
        RiskControlRule riskControlRule = new RiskControlRule();
        riskControlRule.setId(vo.getId());
        riskControlRule.setName(vo.getName());
        riskControlRule.setStatus(vo.getStatus());
        riskControlRule.setAccessPoint(vo.getAccessPoint());
        riskControlRule.setType(vo.getType());
        riskControlRule.setSupplierNos(vo.getSupplierNos());
        riskControlRule.setSupplierNames(vo.getSupplierNames());
        riskControlRule.setUpdator("guangge");
        if (!CollectionUtils.isEmpty(vo.getRuleRangeList())) {
            riskControlRule.setRuleRangeList(vo.getRuleRangeList().stream().map((ruleRangeVo) -> {
                RuleRange ruleRange = new RuleRange();
                ruleRange.setCreateTime(new Date());
                ruleRange.setVariable(ruleRangeVo.getVariable());
                ruleRange.setOperator(ruleRangeVo.getOperator());
                ruleRange.setConstant(ruleRangeVo.getConstant());
                return ruleRange;
            }).collect(Collectors.toList()));
        }
        riskControlRuleBiz.update(riskControlRule);
    }

    @Test
    public void create() {
        RiskControlRule rule = new RiskControlRule();
        rule.setName("测试1");
        rule.setCreateTime(new Date());
        rule.setStatus(OpenOffEnum.OPEN.getValue());
        rule.setAccessPoint(RiskAccessPointEnum.SETTLE.getValue());
        rule.setType(RiskRuleTypeEnum.SPECIAL.getValue());
        rule.setSupplierNos("S000001,S000002");
        rule.setSupplierNames("test1,test2");
        riskControlRuleBiz.create(rule);
    }

    @Test
    public void createWithRange() {
        RiskControlRule rule = new RiskControlRule();
        rule.setName("测试适用范围2");
        rule.setCreateTime(new Date());
        rule.setStatus(OpenOffEnum.OPEN.getValue());
        rule.setAccessPoint(RiskAccessPointEnum.SETTLE.getValue());
        rule.setType(RiskRuleTypeEnum.SPECIAL.getValue());
        rule.setSupplierNos("S000001,S000002");
        rule.setSupplierNames("test1,test2");

        RuleRange ruleRange1 = new RuleRange();
        ruleRange1.setCreateTime(new Date());
        ruleRange1.setVariable(1);
        ruleRange1.setOperator(1);
        ruleRange1.setConstant(BigDecimal.ONE);

        RuleRange ruleRange2 = new RuleRange();
        ruleRange2.setCreateTime(new Date());
        ruleRange2.setVariable(2);
        ruleRange2.setOperator(2);
        ruleRange2.setConstant(BigDecimal.TEN);

        rule.setRuleRangeList(Arrays.asList(ruleRange1, ruleRange2));
        riskControlRuleBiz.create(rule);
    }

    @Test
    public void createGeneral() {
        RiskControlRule rule = new RiskControlRule();
        rule.setName("通用规则");
        rule.setCreateTime(new Date());
        rule.setStatus(OpenOffEnum.OPEN.getValue());
        rule.setAccessPoint(RiskAccessPointEnum.SETTLE.getValue());
        rule.setType(RiskRuleTypeEnum.GENERAL.getValue());
        riskControlRuleBiz.create(rule);
    }

    @Test
    public void listPage() {
        Map<String, Object> paramMap = new HashMap<>();
        log.info(JsonUtil.toString(riskControlRuleBiz.listPage(paramMap, PageParam.newInstance(1, 10)),
                SerializerFeature.PrettyFormat));

        log.info("========================================================");

        paramMap.put("id", 3);
        log.info(JsonUtil.toString(riskControlRuleBiz.listPage(paramMap, PageParam.newInstance(1, 10)),
                SerializerFeature.PrettyFormat));

        log.info("========================================================");

        paramMap.clear();
        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        log.info(JsonUtil.toString(riskControlRuleBiz.listPage(paramMap, PageParam.newInstance(1, 10)),
                SerializerFeature.PrettyFormat));
    }

    @Test
    public void getById() {
        log.info(JsonUtil.toString(riskControlRuleBiz.getById(1), SerializerFeature.PrettyFormat));

        log.info("========================================================");

        log.info(JsonUtil.toString(riskControlRuleBiz.getById(2), SerializerFeature.PrettyFormat));

        log.info("========================================================");

        log.info(JsonUtil.toString(riskControlRuleBiz.getById(3), SerializerFeature.PrettyFormat));
    }

    @Test
    public void update() {
        RiskControlRule rule = riskControlRuleBiz.getById(3);
        rule.setName("测试适用范围1");
        rule.setStatus(OpenOffEnum.OPEN.getValue());
        rule.setAccessPoint(RiskAccessPointEnum.INVOICE.getValue());
        rule.setSupplierNos("S000002");
        rule.setSupplierNames("test2");

        RuleRange ruleRange1 = new RuleRange();
        ruleRange1.setCreateTime(new Date());
        ruleRange1.setVariable(3);
        ruleRange1.setOperator(3);
        ruleRange1.setConstant(BigDecimal.ZERO);

        RuleRange ruleRange2 = new RuleRange();
        ruleRange2.setCreateTime(new Date());
        ruleRange2.setVariable(4);
        ruleRange2.setOperator(4);
        ruleRange2.setConstant(BigDecimal.TEN);

        rule.setRuleRangeList(Arrays.asList(ruleRange1, ruleRange2));
        riskControlRuleBiz.update(rule);
    }

    @Test
    public void deleteById() {
        riskControlRuleBiz.deleteById(5);
    }

    @Test
    public void enableRule() {
        riskControlRuleBiz.enableRule(3);
    }

    @Test
    public void disableRule() {
        riskControlRuleBiz.disableRule(3);
    }

    @Test
    public void matchRule() {
        SettleRiskControlVo settleRiskControlVo = new SettleRiskControlVo();
        settleRiskControlVo.setEmployerNo("");
        settleRiskControlVo.setOrderNo("");
        settleRiskControlVo.setSupplierNo("S000001");
        settleRiskControlVo.setUserIdCard("");
        settleRiskControlVo.setUserName("");
        log.info(JsonUtil.toString(riskControlRuleBiz.matchRule(settleRiskControlVo),
                SerializerFeature.PrettyFormat));

        log.info("========================================================");

        settleRiskControlVo.setSupplierNo("S100001");
        log.info(JsonUtil.toString(riskControlRuleBiz.matchRule(settleRiskControlVo),
                SerializerFeature.PrettyFormat));
    }
}