<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtomGroup">
    <sql id="table">tbl_name_strategy_atom_group</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtomGroup">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="supplier_name" property="supplierName" jdbcType="VARCHAR"/>
        <result column="supplier_no" property="supplierNo" jdbcType="VARCHAR"/>
        <result column="name_management_id" property="nameManagementId" jdbcType="BIGINT"/>
        <result column="weight" property="weight" jdbcType="INTEGER"/>
        <result column="control_atom" property="controlAtom" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, supplier_name, supplier_no, name_management_id, weight, control_atom, remark, version, create_time, update_time
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtomGroup">
        INSERT INTO <include refid="table" /> (
            supplier_name,
            supplier_no,
            name_management_id,
            weight,
            control_atom,
            remark,
            version,
            create_time,
            update_time
        ) VALUES (
            #{supplierName,jdbcType=VARCHAR},
            #{supplierNo,jdbcType=VARCHAR},
            #{nameManagementId,jdbcType=BIGINT},
            #{weight,jdbcType=INTEGER},
            #{controlAtom,jdbcType=INTEGER},
            #{remark,jdbcType=VARCHAR},
            #{version,jdbcType=INTEGER},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            supplier_name,
            supplier_no,
            name_management_id,
            weight,
            control_atom,
            remark,
            version,
            create_time,
            update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.supplierName,jdbcType=VARCHAR},
            #{item.supplierNo,jdbcType=VARCHAR},
            #{item.nameManagementId,jdbcType=BIGINT},
            #{item.weight,jdbcType=INTEGER},
            #{item.controlAtom,jdbcType=INTEGER},
            #{item.remark,jdbcType=VARCHAR},
            #{item.version,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtomGroup">
        UPDATE <include refid="table" /> SET
            supplier_name = #{supplierName,jdbcType=VARCHAR},
            supplier_no = #{supplierNo,jdbcType=VARCHAR},
            name_management_id = #{nameManagementId,jdbcType=BIGINT},
            weight = #{weight,jdbcType=INTEGER},
            control_atom = #{controlAtom,jdbcType=INTEGER},
            remark = #{remark,jdbcType=VARCHAR},
            version = #{version,jdbcType=INTEGER} + 1,
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtomGroup">
        UPDATE <include refid="table" />
        <set>
            <if test="supplierName != null">
                supplier_name = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="supplierNo != null">
                supplier_no = #{supplierNo,jdbcType=VARCHAR},
            </if>
            <if test="nameManagementId != null">
                name_management_id = #{nameManagementId,jdbcType=BIGINT},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=INTEGER},
            </if>
            <if test="controlAtom != null">
                control_atom = #{controlAtom,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER} + 1,
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="supplierName != null and supplierName !=''">
            and supplier_name = #{supplierName,jdbcType=VARCHAR}
        </if>
        <if test="supplierNo != null and supplierNo !=''">
            and supplier_no = #{supplierNo,jdbcType=VARCHAR}
        </if>
        <if test="nameManagementId != null">
            and name_management_id = #{nameManagementId,jdbcType=BIGINT}
        </if>
        <if test="weight != null">
            and weight = #{weight,jdbcType=INTEGER}
        </if>
        <if test="controlAtom != null">
            and control_atom = #{controlAtom,jdbcType=INTEGER}
        </if>
        <if test="remark != null and remark !=''">
            and remark = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=INTEGER}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
    </sql>


    <delete id="deleteByIdList">
        DELETE FROM <include refid="table" /> WHERE id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <resultMap id="NameStrategyMap" type="com.zhixianghui.facade.riskcontrol.vo.GlobalAtomGroupVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="supplier_no" property="supplierNo" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="INTEGER"/>
        <result column="control_atom" property="controlAtom" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="name_management_id" property="objectId" jdbcType="BIGINT"/>
        <collection property="atomVoList" ofType="com.zhixianghui.facade.riskcontrol.vo.GlobalAtomVo" javaType="java.util.ArrayList">
            <result column="atom_group_id" property="groupId"/>
            <result column="atom_variable" property="variable"/>
            <result column="atom_operator" property="operator"/>
            <result column="atom_constant" property="constant"/>
        </collection>
    </resultMap>

    <select id="listByNameManagementId" parameterType="java.util.Map" resultMap="NameStrategyMap">
        select
        t1.id as id,
        t1.supplier_no as supplier_no,
        t1.name_management_id as name_management_id,
        t1.weight as weight,
        t1.control_atom as control_atom,
        t1.remark as remark,
        t2.group_id as atom_group_id,
        t2.variable as atom_variable,
        t2.operator as atom_operator,
        t2.constant as atom_constant
        from tbl_name_strategy_atom_group t1 left outer join tbl_name_strategy_atom t2 on ( t1.id = t2.group_id)
        where t1.name_management_id = #{nameManagementId,jdbcType=BIGINT}
        and (t1.supplier_no = #{supplierNo,jdbcType=VARCHAR} or t1.supplier_no = #{defaultSupplierNo,jdbcType=VARCHAR})
        order by t1.weight desc
    </select>

</mapper>
