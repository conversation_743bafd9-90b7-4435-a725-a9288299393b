<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.riskcontrol.entity.NameManagement">
    <sql id="table">tbl_name_management</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.riskcontrol.entity.NameManagement">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
        <result column="user_latitude" property="userLatitude" jdbcType="INTEGER"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="id_card_number" property="idCardNumber" jdbcType="VARCHAR"/>
        <result column="warehousing_reason" property="warehousingReason" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="updator" property="updator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="enterprise_personnel_id" property="enterprisePersonnelId" jdbcType="BIGINT"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mch_no, mch_name, user_latitude, username, id_card_number, warehousing_reason, version, updator, create_time, update_time,enterprise_personnel_id
    </sql>


    <select id="listByQuery" resultType="com.zhixianghui.facade.riskcontrol.vo.NameManagementVo">
        SELECT tnm.*,tnsag.supplier_no,tnsag.supplier_name,tnsag.control_atom FROM `tbl_name_management` tnm
        inner join (select name_management_id,GROUP_CONCAT(supplier_no) supplier_no,GROUP_CONCAT(supplier_name) supplier_name,GROUP_CONCAT(control_atom) control_atom
        from
        (select name_management_id,supplier_no,supplier_name,control_atom
        from tbl_name_strategy_atom_group
        <where>
            <if test="supplierName!=null and supplierName!= ''">
                and supplier_name=#{supplierName}
            </if>
        </where>
        group by name_management_id,supplier_no,supplier_name,control_atom) t
        group by name_management_id) tnsag
        on tnm.id=tnsag.name_management_id
        <where>
            <if test="mchNo!=null and mchNo!= ''">
                and tnm.mch_no=#{mchNo}
            </if>
            <if test="mchName!=null and mchName!= ''">
                and tnm.mch_name=#{mchName}
            </if>
            <if test="username!=null and username!= ''">
                and tnm.username like concat('%',#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="idCardNumber!=null and idCardNumber!= ''">
                and tnm.id_card_number=#{idCardNumber}
            </if>
            <if test="createTimeBegin != null and createTimeBegin !=''">
                and tnm.create_time >= #{createTimeBegin}
            </if>
            <if test="createTimeEnd != null and createTimeEnd !=''">
                and tnm.create_time &lt;= #{createTimeEnd}
            </if>
        </where>
        order by tnm.create_time desc
    </select>


    <select id="listByQueryCount" resultType="java.lang.Long">
        SELECT count(1) FROM `tbl_name_management` tnm
        inner join (select name_management_id,GROUP_CONCAT(supplier_no) supplier_no,GROUP_CONCAT(supplier_name) supplier_name,GROUP_CONCAT(control_atom) control_atom
        from
        (select name_management_id,supplier_no,supplier_name,control_atom
        from tbl_name_strategy_atom_group
        <where>
            <if test="supplierName!=null and supplierName!= ''">
                and supplier_name=#{supplierName}
            </if>
        </where>
        group by name_management_id,supplier_no,supplier_name,control_atom) t
        group by name_management_id) tnsag
        on tnm.id=tnsag.name_management_id
        <where>
            <if test="mchNo!=null and mchNo!= ''">
                and tnm.mch_no=#{mchNo}
            </if>
            <if test="mchName!=null and mchName!= ''">
                and tnm.mch_name=#{mchName}
            </if>
            <if test="username!=null and username!= ''">
                and tnm.username like concat('%',#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="idCardNumber!=null and idCardNumber!= ''">
                and tnm.id_card_number=#{idCardNumber}
            </if>
            <if test="createTimeBegin != null and createTimeBegin !=''">
                and tnm.create_time >= #{createTimeBegin}
            </if>
            <if test="createTimeEnd != null and createTimeEnd !=''">
                and tnm.create_time &lt;= #{createTimeEnd}
            </if>
        </where>
        order by tnm.create_time desc
    </select>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameManagement">
        INSERT INTO <include refid="table" /> (
            mch_no,
            mch_name,
            user_latitude,
            username,
            id_card_number,
            warehousing_reason,
            version,
            updator,
            create_time,
            update_time,
            enterprise_personnel_id
        ) VALUES (
            #{mchNo,jdbcType=VARCHAR},
            #{mchName,jdbcType=VARCHAR},
            #{userLatitude,jdbcType=INTEGER},
            #{username,jdbcType=VARCHAR},
            #{idCardNumber,jdbcType=VARCHAR},
            #{warehousingReason,jdbcType=VARCHAR},
            #{version,jdbcType=INTEGER},
            #{updator,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{enterprisePersonnelId,jdbcType=BIGINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            mch_no,
            mch_name,
            user_latitude,
            username,
            id_card_number,
            warehousing_reason,
            version,
            updator,
            create_time,
            update_time,
            enterprise_personnel_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.mchName,jdbcType=VARCHAR},
            #{item.userLatitude,jdbcType=INTEGER},
            #{item.username,jdbcType=VARCHAR},
            #{item.idCardNumber,jdbcType=VARCHAR},
            #{item.warehousingReason,jdbcType=VARCHAR},
            #{item.version,jdbcType=INTEGER},
            #{item.updator,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.enterprisePersonnelId,jdbcType=BIGINT}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameManagement">
        UPDATE <include refid="table" /> SET
            mch_no = #{mchNo,jdbcType=VARCHAR},
            mch_name = #{mchName,jdbcType=VARCHAR},
            user_latitude = #{userLatitude,jdbcType=INTEGER},
            username = #{username,jdbcType=VARCHAR},
            id_card_number = #{idCardNumber,jdbcType=VARCHAR},
            warehousing_reason = #{warehousingReason,jdbcType=VARCHAR},
            version = #{version,jdbcType=INTEGER} + 1,
            updator = #{updator,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            enterprise_personnel_id = #{enterprisePersonnelId,jdbcType=BIGINT}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameManagement">
        UPDATE <include refid="table" />
        <set>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                mch_name = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="userLatitude != null">
                user_latitude = #{userLatitude,jdbcType=INTEGER},
            </if>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="idCardNumber != null">
                id_card_number = #{idCardNumber,jdbcType=VARCHAR},
            </if>
            <if test="warehousingReason != null">
                warehousing_reason = #{warehousingReason,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER} + 1,
            </if>
            <if test="updator != null">
                updator = #{updator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="enterprisePersonnelId != null">
                enterprise_personnel_id = #{enterprisePersonnelId,jdbcType=BIGINT}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE
    </delete>

    <select id="matchName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table"/>
        where mch_no = #{mchNo} and
        (
            (user_latitude = 100 and username = #{username}) or
            (user_latitude = 200 and id_card_number = #{idCardNumber}) or
            (user_latitude = 300 and username = #{username} and id_card_number = #{idCardNumber})
        )
        order by user_latitude desc
    </select>

    <select id="selectByMchNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from <include refid="table"/>
        where mch_no=#{mchNo}
    </select>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and mch_no = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="mchName != null and mchName !=''">
            and mch_name = #{mchName,jdbcType=VARCHAR}
        </if>
        <if test="userLatitude != null">
            and user_latitude = #{userLatitude,jdbcType=INTEGER}
        </if>
        <if test="username != null and username !=''">
            and username = #{username,jdbcType=VARCHAR}
        </if>
        <if test="idCardNumber != null and idCardNumber !=''">
            and id_card_number = #{idCardNumber,jdbcType=VARCHAR}
        </if>
        <if test="warehousingReason != null and warehousingReason !=''">
            and warehousing_reason = #{warehousingReason,jdbcType=VARCHAR}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=INTEGER}
        </if>
        <if test="updator != null and updator !=''">
            and updator = #{updator,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="enterprisePersonnelId != null">
            and `enterprise_personnel_id` = #{enterprisePersonnelId,jdbcType=BIGINT}
        </if>
    </sql>

    <delete id="deleteByIdList">
        DELETE FROM <include refid="table" /> WHERE id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
