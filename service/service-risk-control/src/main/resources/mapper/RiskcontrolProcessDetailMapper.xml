<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail">
	<sql id="table"> tbl_riskcontrol_process_detail </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="create_date" property="createDate" jdbcType="DATE"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
		<result column="mch_order_no" property="mchOrderNo" jdbcType="VARCHAR"/>
		<result column="plat_trx_no" property="platTrxNo" jdbcType="VARCHAR"/>
		<result column="employer_no" property="employerNo" jdbcType="VARCHAR"/>
		<result column="employer_name" property="employerName" jdbcType="VARCHAR"/>
		<result column="access_point" property="accessPoint" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="age" property="age" jdbcType="INTEGER"/>
		<result column="failed_reason" property="failedReason" jdbcType="VARCHAR"/>
		<result column="mainstay_name" property="mainstayName" jdbcType="VARCHAR"/>
		<result column="order_amount" property="orderAmount" jdbcType="DECIMAL"/>
		<result column="operation" property="operation" jdbcType="INTEGER"/>
		<result column="RULE_ID" property="ruleId" jdbcType="BIGINT"/>
		<result column="ATOM_GROUP_ID" property="atomGroupId" jdbcType="BIGINT"/>
		<result column="CONTROL_TYPE" property="controlType" jdbcType="INTEGER"/>
		<result column="OBJECT_ID" property="objectId" jdbcType="BIGINT"/>
		<result column="ATOM_GROUP_WEIGHT" property="atomGroupWeight" jdbcType="INTEGER"/>
		<result column="OBJECT_WEIGHT" property="objectWeight" jdbcType="INTEGER"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		*
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	create_date ,
			update_time ,
			update_user ,
			mch_order_no ,
			plat_trx_no ,
			employer_no ,
			employer_name ,
			access_point ,
			`status` ,
			failed_reason ,
			age ,
			mainstay_name ,
			order_amount ,
			operation ,
			rule_id ,
			atom_group_id,
			control_type,
			object_id,
			atom_group_weight,
			object_weight
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{createDate,jdbcType=DATE},
			#{updateTime,jdbcType=TIMESTAMP},
			#{updateUser,jdbcType=VARCHAR},
			#{mchOrderNo,jdbcType=VARCHAR},
			#{platTrxNo,jdbcType=VARCHAR},
			#{employerNo,jdbcType=VARCHAR},
			#{employerName,jdbcType=VARCHAR},
			#{accessPoint,jdbcType=INTEGER},
			#{status,jdbcType=INTEGER},
			#{failedReason,jdbcType=VARCHAR},
			#{age,jdbcType=INTEGER},
			#{mainstayName,jdbcType=VARCHAR},
			#{orderAmount,jdbcType=DECIMAL},
			#{operation,jdbcType=INTEGER},
			#{ruleId,jdbcType=BIGINT},
			#{atomGroupId,jdbcType=BIGINT},
			#{controlType,jdbcType=INTEGER},
			#{objectId,jdbcType=BIGINT},
			#{atomGroupWeight,jdbcType=INTEGER},
			#{objectWeight,jdbcType=INTEGER}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
			version ,
			create_time ,
			create_date ,
			update_time ,
			update_user ,
			mch_order_no ,
			plat_trx_no ,
			employer_no ,
			employer_name ,
			access_point ,
			`status` ,
			failed_reason ,
			age ,
			mainstay_name ,
			order_amount ,
			operation ,
			rule_id ,
			atom_group_id,
			control_type,
			object_id,
			atom_group_weight,
			object_weight
        ) VALUES
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{createDate,jdbcType=DATE},
			#{updateTime,jdbcType=TIMESTAMP},
			#{updateUser,jdbcType=VARCHAR},
			#{mchOrderNo,jdbcType=VARCHAR},
			#{platTrxNo,jdbcType=VARCHAR},
			#{employerNo,jdbcType=VARCHAR},
			#{employerName,jdbcType=VARCHAR},
			#{accessPoint,jdbcType=INTEGER},
			#{status,jdbcType=INTEGER},
			#{failedReason,jdbcType=VARCHAR},
			#{age,jdbcType=INTEGER},
			#{mainstayName,jdbcType=VARCHAR},
			#{orderAmount,jdbcType=DECIMAL},
			#{operation,jdbcType=INTEGER},
			#{ruleId,jdbcType=BIGINT},
			#{atomGroupId,jdbcType=BIGINT},
			#{controlType,jdbcType=INTEGER},
			#{objectId,jdbcType=BIGINT},
			#{atomGroupWeight,jdbcType=INTEGER},
			#{objectWeight,jdbcType=INTEGER}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			update_user = #{updateUser,jdbcType=VARCHAR},
			mch_order_no = #{mchOrderNo,jdbcType=VARCHAR},
			plat_trx_no = #{platTrxNo,jdbcType=VARCHAR},
			employer_no = #{employerNo,jdbcType=VARCHAR},
			employer_name = #{employerName,jdbcType=VARCHAR},
			access_point = #{accessPoint,jdbcType=INTEGER},
			`status` = #{status,jdbcType=INTEGER},
			failed_reason = #{failedReasonPortal,jdbcType=VARCHAR},
			age = #{age,jdbcType=INTEGER},
			mainstay_name = #{mainstayName,jdbcType=VARCHAR},
			order_amount = #{orderAmount,jdbcType=DECIMAL},
			operation = #{operation,jdbcType=INTEGER},
			rule_id = #{ruleId,jdbcType=BIGINT},
			atom_group_id = #{atomGroupId,jdbcType=BIGINT},
			control_type = #{controlType,jdbcType=INTEGER},
			object_id = #{objectId,jdbcType=BIGINT},
			atom_group_weight = #{atomGroupWeight,jdbcType=INTEGER},
			object_weight = #{objectWeight,jdbcType=INTEGER}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="updateTime != null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUser != null and updateUser != ''">
				update_user = #{updateUser,jdbcType=VARCHAR},
			</if>
			<if test="mchOrderNo != null and mchOrderNo != ''">
				mch_order_no = #{mchOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="platTrxNo != null and platTrxNo != ''">
				plat_trx_no = #{platTrxNo,jdbcType=VARCHAR},
			</if>
			<if test="employerNo != null and employerNo != ''">
				employer_no = #{employerNo,jdbcType=VARCHAR},
			</if>
			<if test="employerName != null and employerName != ''">
				employer_name = #{employerName,jdbcType=VARCHAR},
			</if>
			<if test="updateUser != null">
				access_point = #{accessPoint,jdbcType=INTEGER},
			</if>
			<if test="status != null">
				`status` = #{status,jdbcType=INTEGER},
			</if>
			<if test="age != null">
				`age` = #{age,jdbcType=INTEGER},
			</if>
			<if test="failedReason != null and failedReason != ''">
				failed_reason = #{failedReason,jdbcType=VARCHAR},
			</if>
			<if test="mainstayName != null and mainstayName != ''">
				mainstay_name = #{mainstayName,jdbcType=VARCHAR},
			</if>
			<if test="orderAmount != null">
				order_amount = #{orderAmount,jdbcType=VARCHAR},
			</if>
			<if test="operation != null">
				operation = #{operation,jdbcType=INTEGER},
			</if>
			<if test="ruleId != null">
				rule_id = #{ruleId,jdbcType=INTEGER},
			</if>
			<if test="atomGroupId != null">
				atom_group_id = #{atomGroupId,jdbcType=INTEGER},
			</if>
			<if test="controlType != null">
				control_type = #{controlType,jdbcType=INTEGER},
			</if>
			<if test="objectId != null">
				object_id = #{objectId,jdbcType=BIGINT},
			</if>
			<if test="atomGroupWeight != null">
				atom_group_weight = #{atomGroupWeight,jdbcType=INTEGER},
			</if>
			<if test="objectWeight != null">
				object_weight = #{objectWeight,jdbcType=INTEGER},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		ORDER BY ID DESC
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE id = #{id,jdbcType=BIGINT}
	</select>

    <select id="listByIdList" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE id in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item,jdbcType=BIGINT}
		</foreach>
	</select>

	<update id="updateStatusByIdList" parameterType="java.util.Map">
		update <include refid="table" />
		set
			VERSION = version +1,
			operation = #{operation,jdbcType=INTEGER},
			update_time = now(),
			update_user = #{updateUser,jdbcType=VARCHAR}
		where id in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item,jdbcType=BIGINT}
		</foreach>
	</update>
	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<!--按规则id删除-->
	<delete id="deleteByRuleId">
		DELETE FROM <include refid="table" /> WHERE rule_id = #{ruleId,jdbcType=BIGINT}
	</delete>

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateUser != null and updateUser != ''">
			and update_user = #{updateUser,jdbcType=VARCHAR}
		</if>
		<if test="mchOrderNo != null and mchOrderNo != ''">
			and mch_order_no = #{mchOrderNo,jdbcType=VARCHAR}
		</if>
		<if test="platTrxNo != null and platTrxNo != ''">
			and plat_trx_no = #{platTrxNo,jdbcType=VARCHAR}
		</if>
		<if test="employerNo != null and employerNo != ''">
			and employer_no = #{employerNo,jdbcType=VARCHAR}
		</if>
		<if test="employerName != null and employerName != ''">
			and employer_name = #{employerName,jdbcType=VARCHAR}
		</if>
		<if test="employerNameLike != null and employerNameLike != ''">
			and employer_name like concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
		</if>
		<if test="accessPoint != null">
			and access_point = #{accessPoint,jdbcType=INTEGER}
		</if>
		<if test="status != null">
			and `status` = #{status,jdbcType=INTEGER}
		</if>
		<if test="operation != null">
			and operation = #{operation,jdbcType=INTEGER}
		</if>
		<if test="beginDate != null and endDate != null" >
			and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
		</if>
		<if test="objectId != null">
			and object_id = #{objectId,jdbcType=BIGINT}
		</if>
		<if test="controlType != null">
			and control_type = #{controlType,jdbcType=INTEGER}
		</if>
		<!--自定义-->
		<if test="notOperation != null">
			and operation <![CDATA[ <> ]]> #{notOperation,jdbcType=INTEGER}
		</if>
		<if test="employerNameLike != null and employerNameLike != ''">
			and employer_name like concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
		</if>
		<if test="createTimeBegin != null and createTimeEnd != null" >
			and CREATE_TIME <![CDATA[ > ]]> #{createTimeBegin,jdbcType=TIMESTAMP}
			and CREATE_TIME <![CDATA[ <= ]]>  #{createTimeEnd,jdbcType=TIMESTAMP}
		</if>
	</sql>
</mapper>

