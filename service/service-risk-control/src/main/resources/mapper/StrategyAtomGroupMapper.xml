<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup">
	<!-- 用于版本控制 -->
	<sql id="table">
		<trim prefix=" ">
			strategy_atom_group<include refid="RiskVersionXml.version"/>
		</trim>
	</sql>
	<sql id="atomTable">
		<trim prefix=" ">
			strategy_atom<include refid="RiskVersionXml.version"/>
		</trim>
	</sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
		<result column="weight" property="weight" jdbcType="INTEGER"/>
		<result column="control_atom" property="controlAtom" jdbcType="INTEGER"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<collection property="strategyAtomList" ofType="com.zhixianghui.facade.riskcontrol.entity.StrategyAtom"
					javaType="java.util.ArrayList" resultMap="strategyAtomResult" columnPrefix="atom_" />
	</resultMap>

	<resultMap id="strategyAtomResult" type="com.zhixianghui.facade.riskcontrol.entity.StrategyAtom">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="group_id" property="groupId" jdbcType="BIGINT"/>
		<result column="variable" property="variable" jdbcType="INTEGER"/>
		<result column="operator" property="operator" jdbcType="INTEGER"/>
		<result column="constant" property="constant" jdbcType="DECIMAL"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		update_time,
		rule_id,
		weight,
		control_atom,
		remark
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	rule_id ,
        	weight ,
        	control_atom ,
        	remark
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{ruleId,jdbcType=BIGINT},
			#{weight,jdbcType=INTEGER},
			#{controlAtom,jdbcType=INTEGER},
			#{remark,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	rule_id ,
        	weight ,
        	control_atom ,
        	remark
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.ruleId,jdbcType=BIGINT},
			#{item.weight,jdbcType=INTEGER},
			#{item.controlAtom,jdbcType=INTEGER},
			#{item.remark,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			rule_id = #{ruleId,jdbcType=BIGINT},
			weight = #{weight,jdbcType=INTEGER},
			control_atom = #{controlAtom,jdbcType=INTEGER},
			remark = #{remark,jdbcType=VARCHAR}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="ruleId != null">
				rule_id =#{ruleId,jdbcType=BIGINT},
			</if>
			<if test="weight != null">
				weight =#{weight,jdbcType=INTEGER},
			</if>
			<if test="controlAtom != null">
				control_atom =#{controlAtom,jdbcType=INTEGER},
			</if>
			<if test="remark != null">
				remark =#{remark,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE id = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and update_time = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="ruleId != null">
			and rule_id = #{ruleId,jdbcType=BIGINT}
		</if>
		<if test="weight != null">
			and weight = #{weight,jdbcType=INTEGER}
		</if>
		<if test="controlAtom != null">
			and control_atom = #{controlAtom,jdbcType=INTEGER}
		</if>
		<if test="remark != null">
			and remark = #{remark,jdbcType=VARCHAR}
		</if>
	</sql>

	<!--按组id删除-->
	<delete id="deleteByRuleId">
		DELETE FROM <include refid="table" /> WHERE rule_id = #{ruleId,jdbcType=BIGINT}
	</delete>

	<resultMap id="StrategyAtomMap" type="com.zhixianghui.facade.riskcontrol.vo.GlobalAtomGroupVo">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="weight" property="weight" jdbcType="INTEGER"/>
		<result column="control_atom" property="controlAtom" jdbcType="INTEGER"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="rule_id" property="objectId" jdbcType="BIGINT"/>
		<collection property="atomVoList" ofType="com.zhixianghui.facade.riskcontrol.vo.GlobalAtomVo" javaType="java.util.ArrayList">
			<result column="atom_group_id" property="groupId"/>
			<result column="atom_variable" property="variable"/>
			<result column="atom_operator" property="operator"/>
			<result column="atom_constant" property="constant"/>
		</collection>
	</resultMap>

	<select id="listGlobalAtomByRuleId" parameterType="java.util.Map" resultMap="StrategyAtomMap">
		select
		t1.id as id,
		t1.weight as weight,
		t1.control_atom as control_atom,
		t1.remark as remark,
		t1.rule_id as rule_id,
		t2.group_id as atom_group_id,
		t2.variable as atom_variable,
		t2.operator as atom_operator,
		t2.constant as atom_constant
		from <include refid="table"/> t1 left outer join <include refid="atomTable"/> t2 on ( t1.id = t2.group_id)
		<where>
			<if test="ruleId != null">
				and t1.rule_id = #{ruleId,jdbcType=BIGINT}
			</if>
		</where>
		order by t1.weight desc
	</select>

	<!-- 根据规则id查询 -->
	<select id="listByRuleId" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
			t1.id as id,
			t1.version as version,
			t1.create_time as create_time,
			t1.update_time as update_time,
			t1.rule_id as rule_id,
			t1.weight as weight,
			t1.control_atom as control_atom,
			t1.remark as remark,
			t2.id as atom_id,
			t2.version as atom_version,
			t2.create_time as atom_create_time,
			t2.group_id as atom_group_id,
			t2.variable as atom_variable,
			t2.operator as atom_operator,
			t2.constant as atom_constant
		from <include refid="table"/> t1 left outer join <include refid="atomTable"/> t2 on ( t1.id = t2.group_id)
		<where>
			<if test="ruleId != null">
				and t1.rule_id = #{ruleId,jdbcType=BIGINT}
			</if>
		</where>
		order by t1.weight desc
	</select>
</mapper>

