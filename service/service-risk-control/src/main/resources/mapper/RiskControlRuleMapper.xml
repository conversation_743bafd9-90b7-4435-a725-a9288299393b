<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.riskcontrol.entity.RiskControlRule">
	<!-- 用于版本控制 -->
	<sql id="table">
		<trim prefix=" ">
		risk_control_rule<include refid="RiskVersionXml.version"/>
		</trim>
 	</sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.riskcontrol.entity.RiskControlRule">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="updator" property="updator" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="access_point" property="accessPoint" jdbcType="INTEGER"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="supplier_nos" property="supplierNos" jdbcType="VARCHAR"/>
		<result column="supplier_names" property="supplierNames" jdbcType="VARCHAR"/>
		<result column="employer_nos" property="employerNos" jdbcType="VARCHAR"/>
		<result column="employer_names" property="employerNames" jdbcType="VARCHAR"/>
		<result column="weight" property="weight" jdbcType="INTEGER"/>
		<collection property="ruleRangeList" ofType="com.zhixianghui.facade.riskcontrol.entity.RuleRange"
					javaType="java.util.ArrayList" resultMap="ruleRangeResult" columnPrefix="range_" />
	</resultMap>

	<resultMap id="ruleRangeResult" type="com.zhixianghui.facade.riskcontrol.entity.RuleRange">
		<id column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="rule_id" property="ruleId" jdbcType="BIGINT"/>
		<result column="variable" property="variable" jdbcType="INTEGER"/>
		<result column="operator" property="operator" jdbcType="INTEGER"/>
		<result column="constant" property="constant" jdbcType="DECIMAL"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		update_time,
		updator,
		`name`,
		`status`,
		access_point,
		`type`,
		supplier_nos,
		supplier_names,
		employer_nos,
		employer_names,
		weight
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskControlRule">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
			updator ,
			`name` ,
			`status` ,
        	access_point ,
			`type` ,
			supplier_nos ,
			supplier_names,
			employer_nos,
			employer_names,
			weight
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{updator,jdbcType=VARCHAR},
			#{name,jdbcType=VARCHAR},
			#{status,jdbcType=INTEGER},
			#{accessPoint,jdbcType=INTEGER},
			#{type,jdbcType=INTEGER},
			#{supplierNos,jdbcType=VARCHAR},
			#{supplierNames,jdbcType=VARCHAR},
			#{employerNos,jdbcType=VARCHAR},
			#{employerNames,jdbcType=VARCHAR},
			#{weight,jdbcType=INTEGER}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
			updator ,
			`name` ,
			`status` ,
			access_point ,
			`type` ,
			supplier_nos ,
			supplier_names,
			employer_nos,
			employer_names,
			weight
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.updator,jdbcType=VARCHAR},
			#{item.name,jdbcType=VARCHAR},
			#{item.status,jdbcType=INTEGER},
			#{item.accessPoint,jdbcType=INTEGER},
			#{item.type,jdbcType=INTEGER},
			#{supplierNos,jdbcType=VARCHAR},
			#{supplierNames,jdbcType=VARCHAR},
			#{employerNos,jdbcType=VARCHAR},
			#{employerNames,jdbcType=VARCHAR},
			#{weight,jdbcType=INTEGER}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskControlRule">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			updator = #{updator,jdbcType=VARCHAR},
			`name` = #{name,jdbcType=VARCHAR},
			`status` = #{status,jdbcType=INTEGER},
			access_point = #{accessPoint,jdbcType=INTEGER},
			`type` = #{type,jdbcType=INTEGER},
			supplier_nos = #{supplierNos,jdbcType=VARCHAR},
			supplier_names = #{supplierNames,jdbcType=VARCHAR},
			employer_nos = #{employerNos,jdbcType=VARCHAR},
			employer_names = #{employerNames,jdbcType=VARCHAR},
			weight = #{weight,jdbcType=INTEGER}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskControlRule">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updator != null">
				updator =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="name != null">
				`name` =#{name,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				`status` =#{status,jdbcType=INTEGER},
			</if>
			<if test="accessPoint != null">
				access_point =#{accessPoint,jdbcType=INTEGER},
			</if>
			<if test="type != null">
				`type` =#{type,jdbcType=INTEGER},
			</if>
			<if test="supplierNos != null">
				supplier_nos =#{supplierNos,jdbcType=VARCHAR},
			</if>
			<if test="supplierNames != null">
				supplier_names =#{supplierNames,jdbcType=VARCHAR},
			</if>
			<if test="employer_nos != null">
				employer_nos = #{employerNos,jdbcType=VARCHAR},
			</if>
			<if test="employer_names != null">
				employer_names = #{employerNames,jdbcType=VARCHAR},
			</if>
			<if test="weight != null">
				weight = #{weight,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" /> 
		WHERE id = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updator != null">
			and updator = #{updator,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and update_time = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="name != null">
			and `name` = #{name,jdbcType=VARCHAR}
		</if>
		<if test="status != null">
			and `status` = #{status,jdbcType=INTEGER}
		</if>
		<if test="accessPoint != null">
			and access_point = #{accessPoint,jdbcType=INTEGER}
		</if>
		<if test="type != null">
			and `type` = #{type,jdbcType=INTEGER}
		</if>
		<if test="supplierNos != null">
			and supplier_nos = #{supplierNos,jdbcType=VARCHAR}
		</if>
		<if test="supplierNames != null">
			and supplier_names = #{supplierNames,jdbcType=VARCHAR}
		</if>
		<if test="employerNos != null">
			and employer_nos = #{employerNos,jdbcType=VARCHAR}
		</if>
		<if test="employerNames != null">
			and employer_names = #{employerNames,jdbcType=VARCHAR}
		</if>
		<if test="weight != null">
			and weight = #{weight,jdbcType=INTEGER}
		</if>
	</sql>

	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
			t1.id as id,
			t1.version as version,
			t1.create_time as create_time,
			t1.update_time as update_time,
			t1.updator as updator,
			t1.name as name,
			t1.status as status,
			t1.access_point as access_point,
			t1.type as type,
			t1.supplier_nos as supplier_nos,
			t1.supplier_names as supplier_names,
			t1.employer_nos as employer_nos,
			t1.employer_names as employer_names,
			t1.weight as weight,
			t2.id as range_id,
			t2.version as range_version,
			t2.create_time as range_create_time,
			t2.rule_id as range_rule_id,
			t2.variable as range_variable,
			t2.operator as range_operator,
			t2.constant as range_constant
		from <include refid="table"/> t1 left outer join rule_range t2 on ( t1.id = t2.rule_id )
		<where>
			<if test="id != null">
				and t1.id = #{id,jdbcType=BIGINT}
			</if>
			<if test="name != null">
				and t1.name like concat('%',#{name,jdbcType=VARCHAR},'%')
			</if>
			<if test="status != null">
				and t1.status = #{status,jdbcType=INTEGER}
			</if>
			<if test="type != null">
				and t1.type = #{type,jdbcType=INTEGER}
			</if>
			<if test="supplierNo != null">
				and FIND_IN_SET( #{supplierNo,jdbcType=VARCHAR}, t1.supplier_nos )
			</if>
			<if test="employerNoListQuery != null and employerNoListQuery != ''">
				and FIND_IN_SET( #{employerNoListQuery,jdbcType=VARCHAR}, t1.employer_nos )
			</if>
			<if test="nowId != null">
				and t1.id != #{nowId}
			</if>
		</where>
		<choose>
			<when test="sortColumns != null and sortColumns !='' ">
				<![CDATA[ ORDER BY ${sortColumns} ]]>
			</when>
			<otherwise>
				<![CDATA[ ORDER BY t1.id DESC ]]>
			</otherwise>
		</choose>
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from <include refid="table"></include>
		<where>
			<if test="id != null">
				and id = #{id,jdbcType=BIGINT}
			</if>
			<if test="name != null">
				and `name` like concat('%',#{name,jdbcType=VARCHAR},'%')
			</if>
			<if test="status != null">
				and `status` = #{status,jdbcType=INTEGER}
			</if>
			<if test="type != null">
				and type = #{type,jdbcType=INTEGER}
			</if>
			<if test="supplierNo != null">
				and FIND_IN_SET( #{supplierNo,jdbcType=VARCHAR}, supplier_nos )
			</if>
		</where>
	</select>

	<select id="listByRiskControl" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		t1.id as id,
		t1.version as version,
		t1.create_time as create_time,
		t1.update_time as update_time,
		t1.updator as updator,
		t1.name as name,
		t1.status as status,
		t1.access_point as access_point,
		t1.type as type,
		t1.supplier_nos as supplier_nos,
		t1.supplier_names as supplier_names,
		t1.employer_nos as employer_nos,
		t1.employer_names as employer_names,
		t1.weight as weight,
		t2.id as range_id,
		t2.version as range_version,
		t2.create_time as range_create_time,
		t2.rule_id as range_rule_id,
		t2.variable as range_variable,
		t2.operator as range_operator,
		t2.constant as range_constant
		from <include refid="table"></include> t1 left outer join rule_range t2 on ( t1.id = t2.rule_id )
		<where>
		<if test="type != null">
			and t1.type = #{type,jdbcType=INTEGER}
		</if>
		<if test="supplierNo != null and supplierNo != ''">
			and FIND_IN_SET( #{supplierNo,jdbcType=VARCHAR}, t1.supplier_nos )
		</if>
		<if test="employerNo != null and employerNo != ''">
			and (ISNULL(t1.employer_nos) or LENGTH(trim(t1.employer_nos)) = 0 or FIND_IN_SET(#{employerNo,jdbcType=VARCHAR},t1.employer_nos))
		</if>
		<if test="status != null">
			and t1.status = #{status,jdbcType=BIGINT}
		</if>
		<if test="accessPoint != null">
			and t1.access_point = #{accessPoint,jdbcType=VARCHAR}
		</if>
		</where>
		order by t1.weight desc
	</select>

	<!-- 根据供应商编号筛选查询 -->
	<select id="listBySupplierNo" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
			t1.id as id,
			t1.version as version,
			t1.create_time as create_time,
			t1.update_time as update_time,
			t1.updator as updator,
			t1.name as name,
			t1.status as status,
			t1.access_point as access_point,
			t1.type as type,
			t1.supplier_nos as supplier_nos,
			t1.supplier_names as supplier_names,
			t1.employer_nos as employer_nos,
			t1.employer_names as employer_names,
			t1.weight as weight,
			t2.id as range_id,
			t2.version as range_version,
			t2.create_time as range_create_time,
			t2.rule_id as range_rule_id,
			t2.variable as range_variable,
			t2.operator as range_operator,
			t2.constant as range_constant
		from <include refid="table"></include> t1 left outer join rule_range t2 on ( t1.id = t2.rule_id )
		where FIND_IN_SET( #{supplierNo,jdbcType=VARCHAR}, t1.supplier_nos )
		<if test="status != null">
			and t1.status = #{status,jdbcType=BIGINT}
		</if>
		<if test="accessPoint != null">
			and t1.access_point = #{accessPoint,jdbcType=VARCHAR}
		</if>
		<if test="type != null">
			and t1.type = #{type,jdbcType=INTEGER}
		</if>
	</select>

	<select id="selectList" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		t1.id as id,
		t1.version as version,
		t1.create_time as create_time,
		t1.update_time as update_time,
		t1.updator as updator,
		t1.name as name,
		t1.status as status,
		t1.access_point as access_point,
		t1.type as type,
		t1.supplier_nos as supplier_nos,
		t1.supplier_names as supplier_names,
		t1.employer_nos as employer_nos,
		t1.employer_names as employer_names,
		t1.weight as weight,
		t2.id as range_id,
		t2.version as range_version,
		t2.create_time as range_create_time,
		t2.rule_id as range_rule_id,
		t2.variable as range_variable,
		t2.operator as range_operator,
		t2.constant as range_constant
		from <include refid="table"></include> t1 left outer join rule_range t2 on ( t1.id = t2.rule_id )
		<where>
			<if test="status != null">
				and t1.status = #{status,jdbcType=BIGINT}
			</if>
			<if test="accessPoint != null">
				and t1.access_point = #{accessPoint,jdbcType=VARCHAR}
			</if>
			<if test="type != null">
				and t1.type = #{type,jdbcType=INTEGER}
			</if>
		</where>
	</select>

	<!-- 根据条件查询一条风控规则 -->
	<select id="selectOne" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
			t1.id as id,
			t1.version as version,
			t1.create_time as create_time,
			t1.update_time as update_time,
			t1.updator as updator,
			t1.name as name,
			t1.status as status,
			t1.access_point as access_point,
			t1.type as type,
			t1.supplier_nos as supplier_nos,
			t1.supplier_names as supplier_names,
			t1.employer_nos as employer_nos,
			t1.employer_names as employer_names,
			t1.weight as weight,
			t2.id as range_id,
			t2.version as range_version,
			t2.create_time as range_create_time,
			t2.rule_id as range_rule_id,
			t2.variable as range_variable,
			t2.operator as range_operator,
			t2.constant as range_constant
		from <include refid="table"></include> t1 left outer join rule_range t2 on ( t1.id = t2.rule_id )
		<where>
			<if test="status != null">
				and t1.status = #{status,jdbcType=BIGINT}
			</if>
			<if test="accessPoint != null">
				and t1.access_point = #{accessPoint,jdbcType=VARCHAR}
			</if>
			<if test="type != null">
				and t1.type = #{type,jdbcType=INTEGER}
			</if>
		</where>
		limit 1
	</select>

	<!--更新-->
	<update id="updateStatus" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskControlRule">
		UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			updator = #{updator,jdbcType=VARCHAR},
			`status` = #{status,jdbcType=INTEGER}
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<select id="getBySupplierNos" parameterType="java.util.Map" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		from <include refid="table" />
		<where>
			<if test="supplierNo != null">
				and FIND_IN_SET( #{supplierNo,jdbcType=VARCHAR}, supplier_nos )
			</if>
			<if test="type != null">
				and type = #{type,jdbcType=INTEGER}
			</if>
		</where>
	</select>


	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" /> as m
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
</mapper>

