<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem">
    <sql id="table"> tbl_riskcontrol_order_item </sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="DATE"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="COMPLETE_TIME" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="MCH_BATCH_NO" property="mchBatchNo" jdbcType="VARCHAR"/>
        <result column="PLAT_BATCH_NO" property="platBatchNo" jdbcType="VARCHAR"/>
        <result column="MCH_ORDER_NO" property="mchOrderNo" jdbcType="VARCHAR"/>
        <result column="PLAT_TRX_NO" property="platTrxNo" jdbcType="VARCHAR"/>
        <result column="BANK_TRX_NO" property="bankTrxNo" jdbcType="VARCHAR"/>
        <result column="LAUNCH_WAY" property="launchWay" jdbcType="SMALLINT"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_NAME" property="channelName" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME" property="receiveName" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME_MD5" property="receiveNameMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO" property="receiveIdCardNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO_MD5" property="receiveIdCardNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO" property="receiveAccountNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO_MD5" property="receiveAccountNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO" property="receivePhoneNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO_MD5" property="receivePhoneNoMd5" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
        <result column="ORDER_ITEM_NET_AMOUNT" property="orderItemNetAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_FEE" property="orderItemFee" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_AMOUNT" property="orderItemAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_STATUS" property="orderItemStatus" jdbcType="SMALLINT"/>
        <result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR"/>
        <result column="ERROR_DESC" property="errorDesc" jdbcType="VARCHAR"/>
        <result column="ENCRYPT_KEY_ID" property="encryptKeyId" jdbcType="BIGINT"/>
        <result column="ACCESS_TIMES" property="accessTimes" jdbcType="SMALLINT"/>
        <result column="IS_PASS_HANGUP" property="isPassHangup" jdbcType="BIT"/>
        <result column="HANGUP_APPROVAL_LOGIN_NAME" property="hangupApprovalLoginName" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="JSON_STR" property="jsonStr" jdbcType="OTHER"/>
        <result column="FAILED_REASON" property="failedReason" jdbcType="VARCHAR"/>
        <result column="PENDING_TYPE" property="pendingType" jdbcType="SMALLINT"/>
        <result column="PENDING_STATUS" property="pendingStatus" jdbcType="SMALLINT"/>
        <result column="START_TRADE_TIME" property="startTradeTime" jdbcType="TIMESTAMP"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_DATE, CREATE_TIME, UPDATE_TIME, COMPLETE_TIME, MCH_BATCH_NO, PLAT_BATCH_NO,
        MCH_ORDER_NO, PLAT_TRX_NO, BANK_TRX_NO, LAUNCH_WAY, EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NO, MAINSTAY_NAME,
        CHANNEL_TYPE, PAY_CHANNEL_NO, CHANNEL_NAME, RECEIVE_NAME, RECEIVE_NAME_MD5, RECEIVE_ID_CARD_NO, RECEIVE_ID_CARD_NO_MD5,
        RECEIVE_ACCOUNT_NO, RECEIVE_ACCOUNT_NO_MD5, RECEIVE_PHONE_NO, RECEIVE_PHONE_NO_MD5, BANK_NAME, BANK_CODE,
        ORDER_ITEM_NET_AMOUNT, ORDER_ITEM_FEE, ORDER_ITEM_AMOUNT, ORDER_ITEM_STATUS, ERROR_CODE, ERROR_DESC, ENCRYPT_KEY_ID,
        ACCESS_TIMES, IS_PASS_HANGUP, HANGUP_APPROVAL_LOGIN_NAME, REMARK, JSON_STR, FAILED_REASON , PENDING_TYPE , PENDING_STATUS,START_TRADE_TIME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_DATE,
            CREATE_TIME,
            UPDATE_TIME,
            COMPLETE_TIME,
            MCH_BATCH_NO,
            PLAT_BATCH_NO,
            MCH_ORDER_NO,
            PLAT_TRX_NO,
            BANK_TRX_NO,
            LAUNCH_WAY,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            CHANNEL_TYPE,
            PAY_CHANNEL_NO,
            CHANNEL_NAME,
            RECEIVE_NAME,
            RECEIVE_NAME_MD5,
            RECEIVE_ID_CARD_NO,
            RECEIVE_ID_CARD_NO_MD5,
            RECEIVE_ACCOUNT_NO,
            RECEIVE_ACCOUNT_NO_MD5,
            RECEIVE_PHONE_NO,
            RECEIVE_PHONE_NO_MD5,
            BANK_NAME,
            BANK_CODE,
            ORDER_ITEM_NET_AMOUNT,
            ORDER_ITEM_FEE,
            ORDER_ITEM_AMOUNT,
            ORDER_ITEM_STATUS,
            ERROR_CODE,
            ERROR_DESC,
            ENCRYPT_KEY_ID,
            ACCESS_TIMES,
            IS_PASS_HANGUP,
            HANGUP_APPROVAL_LOGIN_NAME,
            REMARK,
            JSON_STR,
            FAILED_REASON,
            PENDING_TYPE,
            PENDING_STATUS,
            START_TRADE_TIME
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{createDate,jdbcType=DATE},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{completeTime,jdbcType=TIMESTAMP},
        #{mchBatchNo,jdbcType=VARCHAR},
        #{platBatchNo,jdbcType=VARCHAR},
        #{mchOrderNo,jdbcType=VARCHAR},
        #{platTrxNo,jdbcType=VARCHAR},
        #{bankTrxNo,jdbcType=VARCHAR},
        #{launchWay,jdbcType=SMALLINT},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{channelType,jdbcType=SMALLINT},
        #{payChannelNo,jdbcType=VARCHAR},
        #{channelName,jdbcType=VARCHAR},
        #{receiveName,jdbcType=VARCHAR},
        #{receiveNameMd5,jdbcType=VARCHAR},
        #{receiveIdCardNo,jdbcType=VARCHAR},
        #{receiveIdCardNoMd5,jdbcType=VARCHAR},
        #{receiveAccountNo,jdbcType=VARCHAR},
        #{receiveAccountNoMd5,jdbcType=VARCHAR},
        #{receivePhoneNo,jdbcType=VARCHAR},
        #{receivePhoneNoMd5,jdbcType=VARCHAR},
        #{bankName,jdbcType=VARCHAR},
        #{bankCode,jdbcType=VARCHAR},
        #{orderItemNetAmount,jdbcType=DECIMAL},
        #{orderItemFee,jdbcType=DECIMAL},
        #{orderItemAmount,jdbcType=DECIMAL},
        #{orderItemStatus,jdbcType=SMALLINT},
        #{errorCode,jdbcType=VARCHAR},
        #{errorDesc,jdbcType=VARCHAR},
        #{encryptKeyId,jdbcType=BIGINT},
        #{accessTimes,jdbcType=SMALLINT},
        #{isPassHangup,jdbcType=BIT},
        #{hangupApprovalLoginName,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{jsonStr,jdbcType=OTHER},
        #{failedReason,jdbcType=VARCHAR},
        #{pendingType,jdbcType=SMALLINT},
        #{pendingStatus,jdbcType=SMALLINT},
        #{startTradeTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            CREATE_DATE,
            CREATE_TIME,
            UPDATE_TIME,
            COMPLETE_TIME,
            MCH_BATCH_NO,
            PLAT_BATCH_NO,
            MCH_ORDER_NO,
            PLAT_TRX_NO,
            BANK_TRX_NO,
            LAUNCH_WAY,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            CHANNEL_TYPE,
            PAY_CHANNEL_NO,
            CHANNEL_NAME,
            RECEIVE_NAME,
            RECEIVE_NAME_MD5,
            RECEIVE_ID_CARD_NO,
            RECEIVE_ID_CARD_NO_MD5,
            RECEIVE_ACCOUNT_NO,
            RECEIVE_ACCOUNT_NO_MD5,
            RECEIVE_PHONE_NO,
            RECEIVE_PHONE_NO_MD5,
            BANK_NAME,
            BANK_CODE,
            ORDER_ITEM_NET_AMOUNT,
            ORDER_ITEM_FEE,
            ORDER_ITEM_AMOUNT,
            ORDER_ITEM_STATUS,
            ERROR_CODE,
            ERROR_DESC,
            ENCRYPT_KEY_ID,
            ACCESS_TIMES,
            IS_PASS_HANGUP,
            HANGUP_APPROVAL_LOGIN_NAME,
            REMARK,
            JSON_STR,
            FAILED_REASON,
            PENDING_TYPE,
            PENDING_STATUS,
            START_TRADE_TIME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.createDate,jdbcType=DATE},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.completeTime,jdbcType=TIMESTAMP},
            #{item.mchBatchNo,jdbcType=VARCHAR},
            #{item.platBatchNo,jdbcType=VARCHAR},
            #{item.mchOrderNo,jdbcType=VARCHAR},
            #{item.platTrxNo,jdbcType=VARCHAR},
            #{item.bankTrxNo,jdbcType=VARCHAR},
            #{item.launchWay,jdbcType=SMALLINT},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.channelType,jdbcType=SMALLINT},
            #{item.payChannelNo,jdbcType=VARCHAR},
            #{item.channelName,jdbcType=VARCHAR},
            #{item.receiveName,jdbcType=VARCHAR},
            #{item.receiveNameMd5,jdbcType=VARCHAR},
            #{item.receiveIdCardNo,jdbcType=VARCHAR},
            #{item.receiveIdCardNoMd5,jdbcType=VARCHAR},
            #{item.receiveAccountNo,jdbcType=VARCHAR},
            #{item.receiveAccountNoMd5,jdbcType=VARCHAR},
            #{item.receivePhoneNo,jdbcType=VARCHAR},
            #{item.receivePhoneNoMd5,jdbcType=VARCHAR},
            #{item.bankName,jdbcType=VARCHAR},
            #{item.bankCode,jdbcType=VARCHAR},
            #{item.orderItemNetAmount,jdbcType=DECIMAL},
            #{item.orderItemFee,jdbcType=DECIMAL},
            #{item.orderItemAmount,jdbcType=DECIMAL},
            #{item.orderItemStatus,jdbcType=SMALLINT},
            #{item.errorCode,jdbcType=VARCHAR},
            #{item.errorDesc,jdbcType=VARCHAR},
            #{item.encryptKeyId,jdbcType=BIGINT},
            #{item.accessTimes,jdbcType=SMALLINT},
            #{item.isPassHangup,jdbcType=BIT},
            #{item.hangupApprovalLoginName,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.jsonStr,jdbcType=OTHER},
            #{failedReason,jdbcType=VARCHAR},
            #{pendingType,jdbcType=SMALLINT},
            #{pendingStatus,jdbcType=SMALLINT},
            #{startTradeTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=SMALLINT} +1,
                CREATE_DATE = #{createDate,jdbcType=DATE},
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
                MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR},
                PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR},
                MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR},
                PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR},
                BANK_TRX_NO = #{bankTrxNo,jdbcType=VARCHAR},
                LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT},
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
                CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
                CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
                RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
                RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR},
                RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR},
                RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
                RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR},
                RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
                RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR},
                RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
                BANK_NAME = #{bankName,jdbcType=VARCHAR},
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
                ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL},
                ORDER_ITEM_FEE = #{orderItemFee,jdbcType=DECIMAL},
                ORDER_ITEM_AMOUNT = #{orderItemAmount,jdbcType=DECIMAL},
                ORDER_ITEM_STATUS = #{orderItemStatus,jdbcType=SMALLINT},
                ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
                ERROR_DESC = #{errorDesc,jdbcType=VARCHAR},
                ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT},
                ACCESS_TIMES = #{accessTimes,jdbcType=SMALLINT},
                IS_PASS_HANGUP = #{isPassHangup,jdbcType=BIT},
                HANGUP_APPROVAL_LOGIN_NAME = #{hangupApprovalLoginName,jdbcType=VARCHAR},
                REMARK = #{remark,jdbcType=VARCHAR},
                JSON_STR = #{jsonStr,jdbcType=OTHER},
                FAILED_REASON = #{failedReason,jdbcType=VARCHAR},
                PENDING_TYPE = #{pendingType,jdbcType=SMALLINT},
                PENDING_STATUS = #{pendingStatus,jdbcType=SMALLINT},
                START_TRADE_TIME = #{startTradeTime,jdbcType=TIMESTAMP}
        WHERE PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <if test="sortColumns != null and sortColumns !='' ">
            <![CDATA[ ORDER BY ${sortColumns} ]]>
        </if>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateStatusByPlatNoList" parameterType="java.util.Map">
        update <include refid="table" />
        set
        VERSION = version +1,
        PENDING_STATUS = #{operation,jdbcType=SMALLINT},
        update_time = now()
        where PLAT_TRX_NO in
        <foreach collection="platTrxNoList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getByPlatTrxNo" parameterType="string" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        where PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR}

    </select>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="maxId != null">
                and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=SMALLINT}
            </if>
            <if test="createDate != null">
                and CREATE_DATE = #{createDate,jdbcType=DATE}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="completeTime != null">
                and COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP}
            </if>
            <if test="mchBatchNo != null and mchBatchNo !=''">
                and MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="platBatchNo != null and platBatchNo !=''">
                and PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="mchOrderNo != null and mchOrderNo !=''">
                and MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="platTrxNo != null and platTrxNo !=''">
                and PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR}
            </if>
            <if test="bankTrxNo != null and bankTrxNo !=''">
                and BANK_TRX_NO = #{bankTrxNo,jdbcType=VARCHAR}
            </if>
            <if test="launchWay != null">
                and LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT}
            </if>
            <if test="employerNo != null and employerNo !=''">
                and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="employerName != null and employerName !=''">
                and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
            </if>
            <if test="channelType != null">
                and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
            </if>
            <if test="payChannelNo != null and payChannelNo !=''">
                and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="channelName != null and channelName !=''">
                and CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
            </if>
            <if test="receiveName != null and receiveName !=''">
                and RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR}
            </if>
            <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
                and RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNo != null and receiveIdCardNo !=''">
                and RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
                and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="receiveAccountNo != null and receiveAccountNo !=''">
                and RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 !=''">
                and RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="receivePhoneNo != null and receivePhoneNo !=''">
                and RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR}
            </if>
            <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 !=''">
                and RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="bankName != null and bankName !=''">
                and BANK_NAME = #{bankName,jdbcType=VARCHAR}
            </if>
            <if test="bankCode != null and bankCode !=''">
                and BANK_CODE = #{bankCode,jdbcType=VARCHAR}
            </if>
            <if test="orderItemNetAmount != null">
                and ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL}
            </if>
            <if test="orderItemFee != null">
                and ORDER_ITEM_FEE = #{orderItemFee,jdbcType=DECIMAL}
            </if>
            <if test="orderItemAmount != null">
                and ORDER_ITEM_AMOUNT = #{orderItemAmount,jdbcType=DECIMAL}
            </if>
            <if test="orderItemStatus != null">
                and ORDER_ITEM_STATUS = #{orderItemStatus,jdbcType=SMALLINT}
            </if>
            <if test="errorCode != null and errorCode !=''">
                and ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
            </if>
            <if test="errorDesc != null and errorDesc !=''">
                and ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
            </if>
            <if test="encryptKeyId != null">
                and ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT}
            </if>
            <if test="accessTimes != null">
                and ACCESS_TIMES = #{accessTimes,jdbcType=SMALLINT}
            </if>
            <if test="isPassHangup != null">
                and IS_PASS_HANGUP = #{isPassHangup,jdbcType=BIT}
            </if>
            <if test="hangupApprovalLoginName != null and hangupApprovalLoginName !=''">
                and HANGUP_APPROVAL_LOGIN_NAME = #{hangupApprovalLoginName,jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark !=''">
                and REMARK = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="jsonStr != null and jsonStr !=''">
                and JSON_STR = #{jsonStr,jdbcType=OTHER}
            </if>
            <if test="pendingType != null">
                and PENDING_TYPE = #{pendingType,jdbcType=SMALLINT}
            </if>
            <if test="pendingStatus != null">
                and PENDING_STATUS = #{pendingStatus,jdbcType=SMALLINT}
            </if>

            <!--  自定义  -->
            <if test="overId != null and overId !=''">
                and ID > #{overId,jdbcType=BIGINT}
            </if>
            <if test="employerNameLike != null and employerNameLike !=''">
                and EMPLOYER_NAME LIKE concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="createBeginDate != null and createEndDate != null" >
                and CREATE_DATE between #{createBeginDate,jdbcType=DATE} and #{createEndDate,jdbcType=DATE}
                and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME between #{completeBeginDate,jdbcType=TIMESTAMP} and #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="orderItemNetAmountMin != null and orderItemNetAmountMax != null" >
                and ORDER_ITEM_NET_AMOUNT between #{orderItemNetAmountMin,jdbcType=DECIMAL} and #{orderItemNetAmountMax,jdbcType=DECIMAL}
            </if>

            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
    </sql>

</mapper>
