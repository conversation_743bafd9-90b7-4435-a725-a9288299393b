<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtom">
    <sql id="table">tbl_name_strategy_atom</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtom">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="group_id" property="groupId" jdbcType="BIGINT"/>
        <result column="variable" property="variable" jdbcType="INTEGER"/>
        <result column="operator" property="operator" jdbcType="INTEGER"/>
        <result column="constant" property="constant" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, group_id, variable, operator, constant, version, create_time
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtom">
        INSERT INTO <include refid="table" /> (
            group_id,
            variable,
            operator,
            constant,
            version,
            create_time
        ) VALUES (
            #{groupId,jdbcType=BIGINT},
            #{variable,jdbcType=INTEGER},
            #{operator,jdbcType=INTEGER},
            #{constant,jdbcType=VARCHAR},
            #{version,jdbcType=INTEGER},
            #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            group_id,
            variable,
            operator,
            constant,
            version,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.groupId,jdbcType=BIGINT},
            #{item.variable,jdbcType=INTEGER},
            #{item.operator,jdbcType=INTEGER},
            #{item.constant,jdbcType=VARCHAR},
            #{item.version,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtom">
        UPDATE <include refid="table" /> SET
            group_id = #{groupId,jdbcType=BIGINT},
            variable = #{variable,jdbcType=INTEGER},
            operator = #{operator,jdbcType=INTEGER},
            constant = #{constant,jdbcType=VARCHAR},
            version = #{version,jdbcType=INTEGER} + 1,
            create_time = #{createTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtom">
        UPDATE <include refid="table" />
        <set>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=BIGINT},
            </if>
            <if test="variable != null">
                variable = #{variable,jdbcType=INTEGER},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=INTEGER},
            </if>
            <if test="constant != null">
                constant = #{constant,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER} + 1,
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="groupId != null">
            and group_id = #{groupId,jdbcType=BIGINT}
        </if>
        <if test="variable != null">
            and variable = #{variable,jdbcType=INTEGER}
        </if>
        <if test="operator != null">
            and operator = #{operator,jdbcType=INTEGER}
        </if>
        <if test="constant != null">
            and constant = #{constant,jdbcType=VARCHAR}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=INTEGER}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <delete id="deleteByGroup">
        DELETE FROM <include refid="table" />
        WHERE group_id = #{groupId}
    </delete>

    <delete id="deleteByGroupId">
        DELETE FROM <include refid="table" /> WHERE group_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
