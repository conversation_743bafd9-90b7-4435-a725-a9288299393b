<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.riskcontrol.entity.StrategyAtom">
	<sql id="table">
		<trim prefix=" ">
			strategy_atom<include refid="RiskVersionXml.version"/>
		</trim>
 	</sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.riskcontrol.entity.StrategyAtom">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="group_id" property="groupId" jdbcType="BIGINT"/>
		<result column="variable" property="variable" jdbcType="INTEGER"/>
		<result column="operator" property="operator" jdbcType="INTEGER"/>
		<result column="constant" property="constant" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		group_id,
		variable,
		operator,
		constant
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.riskcontrol.entity.StrategyAtom">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	group_id ,
        	variable ,
        	operator ,
        	constant 
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{groupId,jdbcType=BIGINT},
			#{variable,jdbcType=INTEGER},
			#{operator,jdbcType=INTEGER},
			#{constant,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	group_id ,
        	variable ,
        	operator ,
        	constant 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.groupId,jdbcType=BIGINT},
			#{item.variable,jdbcType=INTEGER},
			#{item.operator,jdbcType=INTEGER},
			#{item.constant,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.riskcontrol.entity.StrategyAtom">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			group_id = #{groupId,jdbcType=BIGINT},
			variable = #{variable,jdbcType=INTEGER},
			operator = #{operator,jdbcType=INTEGER},
			constant = #{constant,jdbcType=VARCHAR}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.riskcontrol.entity.StrategyAtom">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="groupId != null">
				group_id =#{groupId,jdbcType=BIGINT},
			</if>
			<if test="variable != null">
				variable =#{variable,jdbcType=INTEGER},
			</if>
			<if test="operator != null">
				operator =#{operator,jdbcType=INTEGER},
			</if>
			<if test="constant != null">
				constant =#{constant,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE id = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<!--按组id删除-->
	<delete id="deleteByGroupId">
		DELETE FROM <include refid="table" /> WHERE group_id = #{groupId,jdbcType=BIGINT}
	</delete>

	<!--按组id列表删除-->
	<delete id="deleteByGroupIds">
		DELETE FROM <include refid="table" /> WHERE group_id in
		<foreach item="item" collection="list" open="(" separator="," close=")">
			#{item,jdbcType=BIGINT}
		</foreach>
	</delete>

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="groupId != null">
			and group_id = #{groupId,jdbcType=BIGINT}
		</if>
		<if test="variable != null">
			and variable = #{variable,jdbcType=INTEGER}
		</if>
		<if test="operator != null">
			and operator = #{operator,jdbcType=INTEGER}
		</if>
		<if test="constant != null">
			and constant = #{constant,jdbcType=VARCHAR}
		</if>
	</sql>
</mapper>

