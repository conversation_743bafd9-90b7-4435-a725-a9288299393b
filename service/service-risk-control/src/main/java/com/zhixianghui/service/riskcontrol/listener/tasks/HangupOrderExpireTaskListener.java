package com.zhixianghui.service.riskcontrol.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.dto.rmq.MsgDto;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.PendingOrderOpeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.notify.service.NotifyOfNoPersistenceFacade;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.service.riskcontrol.core.biz.RiskcontrolProcessBiz;
import com.zhixianghui.service.riskcontrol.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RocketMQMessageListener(topic = "topic_hangup_order_expire_task",consumeThreadMax = 1,consumerGroup = "HangupOrderExpireTaskConsumer")
public class HangupOrderExpireTaskListener extends TaskRocketMQListener<JSONObject> {
    @Override
    public void runTask(JSONObject jsonParam) {
        this.handleOrder();
    }

    @Autowired
    private RiskcontrolProcessBiz riskcontrolProcessBiz;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private NotifyOfNoPersistenceFacade notifyOfNoPersistenceFacade;
    @Reference
    private RiskControlFacade riskControlFacade;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;


    private void handleOrder() {


        final Date startTime = DateUtils.addHours(new Date(), -96);
        final Date endTime = DateUtils.addHours(new Date(), -48);
        Map<String, Object> param = new HashMap<>();
        param.put("operation", PendingOrderOpeEnum.PENDING.getValue());
        param.put("createTimeBegin", startTime);
        param.put("createTimeEnd", endTime);
        PageParam pageParam = PageParam.newInstance(1, 100);

        log.info("开始扫描挂单-时间段:[{}--{}]", DateUtil.formatDateTime(startTime), DateUtil.formatDateTime(endTime));
        final PageResult<List<RiskcontrolProcessDetail>> pageResult = riskcontrolProcessBiz.listPage(param, pageParam);
        if (pageResult != null && pageResult.getData() != null && !pageResult.getData().isEmpty()) {
            final List<RiskcontrolProcessDetail> processDetails = pageResult.getData();
            if (CollectionUtils.isEmpty(processDetails)) {
                log.info("此时间段无待处理挂单");
                return;
            }

            //拼装platTrxNo
            StringBuilder sb = new StringBuilder();
            List<String> platTrxNoList = new ArrayList<>();
            processDetails.stream().forEach(it->{
                sb.append(String.format("%s,",it.getPlatTrxNo()));
                platTrxNoList.add(it.getPlatTrxNo());
            });
            final List<Long> idList = processDetails.stream().map(it -> it.getId()).collect(Collectors.toList());

            //更新明细的状态
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("list", idList.size() == 0 ? null : idList);
            paramMap.put("platTrxNoList", platTrxNoList.size() == 0 ? null : platTrxNoList);
            paramMap.put("operation", PendingOrderOpeEnum.REJECT.getValue());
            paramMap.put("updateUser", "system");
            riskControlFacade.updateStatusByIdList(paramMap);

            if (StringUtils.isEmpty(sb) || CollectionUtils.isEmpty(platTrxNoList)) {
                return;
            }
            String platTrxNos = sb.substring(0, sb.length() - 1);
            //发mq的方式通知到交易中心
            MsgDto<Map<String, Object>> msgDto = new MsgDto<>();
            msgDto.setTopic(MessageMsgDest.TOPIC_RISKCONTROL_NOTIFY_TRADE);
            msgDto.setTags(MessageMsgDest.TAG_RISKCONTROL_NOTIFY_TRADE);
            msgDto.setTrxNo(null);
            Map<String, Object> jsonParam = new HashMap<>();
            jsonParam.put("operation", PendingOrderOpeEnum.REJECT.getValue());
            jsonParam.put("operatorName", "system");
            jsonParam.put("platTrxNos", platTrxNos);
            msgDto.setJsonParam(jsonParam);
            notifyOfNoPersistenceFacade.sendOne(msgDto);

            processDetails.forEach(processDetail->{
                this.sendRiskEmail(processDetail);
            });
        }else {
            log.info("此时间段无待处理挂单");
        }
        log.info("完成扫描挂单-时间段:[{}--{}]", DateUtil.formatDateTime(startTime), DateUtil.formatDateTime(endTime));
    }

    private void sendRiskEmail(RiskcontrolProcessDetail riskcontrolProcessDetail) {
        try {
            if (riskcontrolProcessDetail.getOperation().intValue() == PendingOrderOpeEnum.PENDING.getValue()) {
                final Merchant merchant = merchantQueryFacade.getByMchNo(riskcontrolProcessDetail.getEmployerNo());
                if (merchant == null || org.apache.commons.lang3.StringUtils.isBlank(merchant.getContactEmail())) {
                    return;
                }

                final OrderItem orderItem = orderItemFacade.getByPlatTrxNo(riskcontrolProcessDetail.getPlatTrxNo());

                EmailParamDto dto = new EmailParamDto();
                Map<String, Object> tplParam = new HashMap<>();
                dto.setSubject(EmailTemplateEnum.HANGUP_EXPIRE_NOTIFY.getDesc());
                dto.setHtmlFormat(true);
                dto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
                dto.setTo(merchant.getContactEmail());
                dto.setTpl(EmailTemplateEnum.HANGUP_EXPIRE_NOTIFY.getName());


                tplParam.put("merchantOrderNo", riskcontrolProcessDetail.getMchOrderNo());
                tplParam.put("platTrxNo", riskcontrolProcessDetail.getPlatTrxNo());
                tplParam.put("revName", orderItem.getReceiveNameDecrypt());
                tplParam.put("amount", orderItem.getOrderItemNetAmount());
                tplParam.put("reason", riskcontrolProcessDetail.getFailedReason());
                tplParam.put("hangupMessage", "挂起订单已取消发放");
                tplParam.put("subHangupMessage","挂起订单超过48小时未处理，自动取消发放");
                dto.setTplParam(tplParam);
                emailFacade.sendAsync(dto);

            }
        } catch (Exception e) {
            log.error("发送风控通知邮件异常:[{}-{}]", riskcontrolProcessDetail.getEmployerNo(), riskcontrolProcessDetail.getPlatTrxNo());
        }
    }
}
