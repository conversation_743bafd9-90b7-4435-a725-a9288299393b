package com.zhixianghui.service.riskcontrol.core.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhixianghui.facade.riskcontrol.entity.RuleRange;
import com.zhixianghui.service.riskcontrol.core.dao.RuleRangeDao;

@Service
public class RuleRangeBiz{
	
	@Autowired
	private RuleRangeDao ruleRangeDao;

	public RuleRange getById(long id){
		return ruleRangeDao.getById(id);
	}

	public void deleteById(long id){
		ruleRangeDao.deleteById(id);
	}
}
