package com.zhixianghui.service.riskcontrol.facade;

import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskAccessPointEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.service.riskcontrol.core.biz.RiskControlBiz;
import com.zhixianghui.service.riskcontrol.core.biz.RiskControlRuleBiz;
import com.zhixianghui.service.riskcontrol.core.biz.RiskcontrolProcessBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 对外风控接口实现
 * @author: xingguang li
 * @created: 2020/10/19 09:56
 */
@Service
public class RiskControlFacadeImpl implements RiskControlFacade {

    @Autowired
    private RiskControlRuleBiz riskControlRuleBiz;
    @Autowired
    private RiskcontrolProcessBiz riskcontrolProcessBiz;
    @Autowired
    private RiskControlBiz riskControlBiz;

    @Override
    public Boolean grantValidateExistRule(String employerNo, String mainstayNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",employerNo);
        paramMap.put("supplierNo",mainstayNo);
        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        paramMap.put("accessPoint", RiskAccessPointEnum.SETTLE.getValue());
        List<RiskControlRule> list = riskControlRuleBiz.listByRiskControl(paramMap);
        if (list.size() > 0 ){
            return true;
        }else{
            return false;
        }
    }

    @Override
    public RiskControlResult processSettle(SettleRiskControlVo settleRiskControlVo) {
        return riskControlBiz.processRiskControl(settleRiskControlVo);
    }

    @Override
    public RiskControlResult processSettleInter(SettleRiskControlVo settleRiskControlVo, Integer controlType) {
        return riskControlBiz.processRiskControl(settleRiskControlVo, controlType);
    }

    @Override
    public PageResult<List<RiskcontrolProcessDetail>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return riskcontrolProcessBiz.listPage(paramMap, pageParam);
    }

    @Override
    public void updateProcessDetail(RiskcontrolProcessDetail riskcontrolProcessDetail) {
        riskcontrolProcessBiz.update(riskcontrolProcessDetail);
    }

    @Override
    public RiskcontrolProcessDetail getById(Long id) {
        return riskcontrolProcessBiz.getById(id);
    }

    @Override
    public List<RiskcontrolProcessDetail> getByIds(List<Long> ids) {
        return riskcontrolProcessBiz.listByIdList(ids);
    }

    @Override
    public void updateStatusByIdList(Map<String,Object> param) {
        riskcontrolProcessBiz.updateStatusByIdList(param);
    }

    @Override
    public PageResult<List<RiskcontrolOrderItem>> listPagePendingOrder(Map<String, Object> paramMap, PageParam pageParam) {
        return riskcontrolProcessBiz.listPagePendingOrder(paramMap, pageParam);
    }

    @Override
    public List<RiskcontrolProcessDetail> listBy(Map<String, Object> params) {
        return riskcontrolProcessBiz.listBy(params);
    }

    @Override
    public RiskcontrolOrderItem getRiskOrderItemByPlatTrxNo(String platTrxNo) {
        return riskcontrolProcessBiz.getRiskOrderItemByPlatTrxNo(platTrxNo);
    }
}
