package com.zhixianghui.service.riskcontrol.core.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhixianghui.facade.riskcontrol.entity.StrategyAtom;
import com.zhixianghui.service.riskcontrol.core.dao.StrategyAtomDao;

@Service
public class StrategyAtomBiz{
	
	@Autowired
	private StrategyAtomDao strategyAtomDao;

	public StrategyAtom getById(long id){
		return strategyAtomDao.getById(id);
	}

	public void deleteById(long id){
		strategyAtomDao.deleteById(id);
	}
}
