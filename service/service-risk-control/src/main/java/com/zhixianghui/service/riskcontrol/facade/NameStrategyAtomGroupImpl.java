package com.zhixianghui.service.riskcontrol.facade;

import com.zhixianghui.facade.riskcontrol.service.NameStrategyAtomGroupFacade;
import com.zhixianghui.service.riskcontrol.core.biz.NameStrategyAtomGroupBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 名单策略原子组表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-11-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NameStrategyAtomGroupImpl implements NameStrategyAtomGroupFacade {

    private final NameStrategyAtomGroupBiz biz;
}
