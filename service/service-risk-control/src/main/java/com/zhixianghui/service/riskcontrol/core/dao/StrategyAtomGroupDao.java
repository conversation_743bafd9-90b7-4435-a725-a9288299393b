package com.zhixianghui.service.riskcontrol.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup;
import com.zhixianghui.facade.riskcontrol.vo.GlobalAtomGroupVo;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;


@Repository
public class StrategyAtomGroupDao extends MyBatisDao<StrategyAtomGroup, Long>{

    /**
     * 查询风控规则对应的策略原子组列表，并按权重排序
     * @param ruleId
     * @return
     */
    public List<StrategyAtomGroup> listByRuleId(long ruleId) {
        return listBy("listByRuleId", Collections.singletonMap("ruleId", ruleId));
    }

    /**
     * 根据风控规则id删除
     * @param ruleId
     */
    public void deleteByRuleId(long ruleId) {
        this.getSqlSession().delete(fillSqlId("deleteByRuleId"), ruleId);
    }

    public List<GlobalAtomGroupVo> listGlobalAtomByRuleId(long ruleId) {
        return listBy("listGlobalAtomByRuleId",Collections.singletonMap("ruleId",ruleId));
    }
}
