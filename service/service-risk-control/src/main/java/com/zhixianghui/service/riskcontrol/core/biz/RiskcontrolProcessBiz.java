package com.zhixianghui.service.riskcontrol.core.biz;

import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.PendingOrderOpeEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskAccessPointEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.export.dto.GrantUserDto;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail;
import com.zhixianghui.facade.riskcontrol.enums.RiskControlTypeEnum;
import com.zhixianghui.facade.riskcontrol.vo.GlobalAtomGroupVo;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.service.riskcontrol.core.dao.RiskcontrolOrderItemDao;
import com.zhixianghui.service.riskcontrol.core.dao.RiskcontrolProcessDetailDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 被风控后处理biz
 * @author: xingguang li
 * @created: 2020/11/23 11:41
 */
@Service
@Slf4j
public class RiskcontrolProcessBiz {

    @Autowired
    private RiskcontrolOrderItemDao riskcontrolOrderItemDao;
    @Autowired
    private RiskcontrolProcessDetailDao riskcontrolProcessDetailDao;

    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("###,##0.00");

    private static final String SYSTEM_CONFUSE = "系统拒绝";
    private static final String SYSTEM_PASS = "系统通过";

    @Transactional(rollbackFor = Exception.class)
    public void controledAction(OrderItem orderItem, RiskAccessPointEnum riskAccessPointEnum, GlobalAtomGroupVo globalAtomGroupVo, GrantUserDto grantUserDto, RiskControlTypeEnum type, SettleRiskControlVo settleRiskControlVo,Boolean containsPlatform) {
        if (orderItem == null) {
            return;
        }
        int operation = 0;
        String updator = null;
        //如果管控原子为拒绝，则需要设置为已处理状态
        if (globalAtomGroupVo.getControlAtom() == ControlAtomEnum.REJECT.getValue()){
            operation = PendingOrderOpeEnum.REJECT.getValue();
            updator = SYSTEM_CONFUSE;
        }else if(globalAtomGroupVo.getControlAtom() == ControlAtomEnum.PASS.getValue()){
            operation = PendingOrderOpeEnum.PASS.getValue();
            updator = SYSTEM_PASS;
        }else{
            operation = PendingOrderOpeEnum.PENDING.getValue();
        }

        //把订单信息复制一份到风控这边，方便连表查询
        //已经存在的情况下则修改即可。
        RiskcontrolOrderItem riskcontrolOrderItem = riskcontrolOrderItemDao.getByPlatTrxNo(orderItem.getPlatTrxNo());
        if (riskcontrolOrderItem == null) {
            riskcontrolOrderItem = new RiskcontrolOrderItem();
            BeanUtil.copyProperties(orderItem, riskcontrolOrderItem);
            riskcontrolOrderItem.setPendingType(globalAtomGroupVo.getControlAtom());
            riskcontrolOrderItem.setFailedReason(globalAtomGroupVo.getRemark());
            riskcontrolOrderItem.setPendingStatus(operation);
            riskcontrolOrderItem.setStartTradeTime(settleRiskControlVo.getStartTradeTime());
            if (orderItem.getPlatTrxNo().contains("OT")) {
                riskcontrolOrderItem.setCreateDate(DateUtil.convertDate(orderItem.getCreateTime()));
                riskcontrolOrderItem.setChannelType(-1);
                riskcontrolOrderItem.setPayChannelNo("");
                riskcontrolOrderItem.setChannelName("");
            }
            riskcontrolOrderItemDao.insert(riskcontrolOrderItem);
        } else {
            riskcontrolOrderItem.setPendingType(globalAtomGroupVo.getControlAtom());
            riskcontrolOrderItem.setFailedReason(globalAtomGroupVo.getRemark());
            riskcontrolOrderItem.setPendingStatus(operation);
            riskcontrolOrderItemDao.update(riskcontrolOrderItem);
        }

        RiskcontrolProcessDetail riskcontrolProcessDetail = new RiskcontrolProcessDetail();
        riskcontrolProcessDetail.setCreateTime(new Date());
        riskcontrolProcessDetail.setAccessPoint(riskAccessPointEnum.getValue());
        riskcontrolProcessDetail.setEmployerName(orderItem.getEmployerName());
        riskcontrolProcessDetail.setEmployerNo(orderItem.getEmployerNo());
        riskcontrolProcessDetail.setMchOrderNo(orderItem.getMchOrderNo());
        riskcontrolProcessDetail.setPlatTrxNo(orderItem.getPlatTrxNo());
        riskcontrolProcessDetail.setUpdateTime(new Date());
        riskcontrolProcessDetail.setStatus(globalAtomGroupVo.getControlAtom());
        riskcontrolProcessDetail.setOperation(operation);

        String reason = "";
        if (containsPlatform){
            reason = String.format("此订单因：【%s】被风控，命中了风控类型：【%s】,对应风控类型id：【%s】,当前年龄：【%s】，月金额：【%s】," +
                            "平台月金额【%s】，年金额：【%s】，顺延年金额：【%s】，商户供应商日金额：【%s】，商户平台日金额：【%s】，24H内等额交易次数：【%s】，打款备注：【%s】",
                    globalAtomGroupVo.getRemark(),type.getDesc(),globalAtomGroupVo.getObjectId(), grantUserDto.getAge(),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getTotalMonthAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getTotalMonthAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getTotalYearAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getTotalPostponeYearAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getMchDailyAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getMchPlatformDailyAmount())),
                    grantUserDto.getMchSingleUserCount(),
                    settleRiskControlVo.getPayRemark());
        }else{
            reason = String.format("此订单因：【%s】被风控，命中了风控类型：【%s】,对应风控类型id：【%s】,当前年龄：【%s】，月金额：【%s】," +
                            "平台月金额【%s】，年金额：【%s】，顺延年金额：【%s】，商户供应商日金额：【%s】，商户平台日金额：【%s】，24H内等额交易次数：【%s】,打款备注：【%s】",
                    globalAtomGroupVo.getRemark(),type.getDesc(),globalAtomGroupVo.getObjectId(), grantUserDto.getAge(),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getMonthAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getTotalMonthAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getYearAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getPostponeYearAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getMchDailyAmount())),
                    DECIMAL_FORMAT.format(new BigDecimal(grantUserDto.getMchPlatformDailyAmount())),
                    grantUserDto.getMchSingleUserCount(),
                    settleRiskControlVo.getPayRemark());
        }
        riskcontrolProcessDetail.setFailedReason(reason);
        riskcontrolProcessDetail.setAge(grantUserDto.getAge());
        riskcontrolProcessDetail.setMainstayName(orderItem.getMainstayName());
        riskcontrolProcessDetail.setOrderAmount(orderItem.getOrderItemNetAmount());
        riskcontrolProcessDetail.setAtomGroupId(globalAtomGroupVo.getId());
        riskcontrolProcessDetail.setObjectId(globalAtomGroupVo.getObjectId());
        riskcontrolProcessDetail.setControlType(type.getType());
        riskcontrolProcessDetail.setAtomGroupWeight(globalAtomGroupVo.getWeight());
        riskcontrolProcessDetail.setUpdateUser(updator);
        riskcontrolProcessDetailDao.insert(riskcontrolProcessDetail);
    }

    public PageResult<List<RiskcontrolProcessDetail>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return riskcontrolProcessDetailDao.listPage(paramMap, pageParam);
    }

    public void update(RiskcontrolProcessDetail riskcontrolProcessDetail) {
        riskcontrolProcessDetailDao.update(riskcontrolProcessDetail);
    }

    public RiskcontrolProcessDetail getById(Long id) {
        return riskcontrolProcessDetailDao.getById(id);
    }

    public List<RiskcontrolProcessDetail> listByIdList(List<Long> idList) {
        return riskcontrolProcessDetailDao.listByIdList(idList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByIdList(Map<String,Object> param) {
        if (param.get("list") != null) {
            riskcontrolProcessDetailDao.updateStatusByIdList(param);
        }
        if (param.get("platTrxNoList") != null) {
            riskcontrolOrderItemDao.updateStatusByPlatNoList(param);
        }
    }

    public PageResult<List<RiskcontrolOrderItem>> listPagePendingOrder(Map<String, Object> paramMap, PageParam pageParam) {
        if (paramMap == null) {
            paramMap = new HashMap<>();
        }
        //挂单的type是ControlAtomEnum.PENDING
        paramMap.put("pendingType", ControlAtomEnum.PENDING.getValue());
        paramMap.put("pendingStatus", PendingOrderOpeEnum.PENDING.getValue());
        return riskcontrolOrderItemDao.listPage(paramMap, pageParam);
    }

    public List<RiskcontrolProcessDetail> listBy(Map<String,Object> params){
        return riskcontrolProcessDetailDao.listBy(params);
    }

    public static void main(String[] args) {
//        String format = new DecimalFormat("###,##0.00").format(new BigDecimal("123912337.068"));
//        System.out.println(format);
        DecimalFormat decimalFormat = new DecimalFormat("###,##0.00");

        System.out.println(decimalFormat.format(new BigDecimal("19283719827.312")));
        System.out.println(decimalFormat.format(new BigDecimal("123912337.068")));
        System.out.println(decimalFormat.format(new BigDecimal("0.00")));
        System.out.println(decimalFormat.format(new BigDecimal("0")));
        System.out.println(decimalFormat.format(new BigDecimal("0.12")));
        System.out.println(decimalFormat.format(new BigDecimal("-1")));
    }

    public RiskcontrolOrderItem getRiskOrderItemByPlatTrxNo(String platTrxNo) {
        return riskcontrolOrderItemDao.getByPlatTrxNo(platTrxNo);
    }
}
