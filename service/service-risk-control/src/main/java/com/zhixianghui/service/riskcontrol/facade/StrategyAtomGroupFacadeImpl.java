package com.zhixianghui.service.riskcontrol.facade;

import com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup;
import com.zhixianghui.facade.riskcontrol.service.StrategyAtomGroupFacade;
import com.zhixianghui.service.riskcontrol.core.biz.StrategyAtomGroupBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 风控规则策略原子组实现
 *
 * <AUTHOR> <PERSON><PERSON>
 */
@Service
public class StrategyAtomGroupFacadeImpl implements StrategyAtomGroupFacade {

    @Autowired
    private StrategyAtomGroupBiz strategyAtomGroupBiz;

    /**
     * 根据id获取策略原子组
     *
     * @param id 原子组id
     * @return
     */
    @Override
    public StrategyAtomGroup getById(long id) {
        return strategyAtomGroupBiz.getById(id);
    }

    /**
     * 查询风控规则的策略原子组，根据权重值排序
     *
     * @param ruleId 风控规则id
     * @return
     */
    @Override
    public List<StrategyAtomGroup> listStrategyAtomGroup(long ruleId) {
        return strategyAtomGroupBiz.listByRuleId(ruleId);
    }

    /**
     * 创建策略原子组
     *
     * @param strategyAtomGroup 策略原子组
     */
    @Override
    public void createStrategyAtomGroup(StrategyAtomGroup strategyAtomGroup, String operator) {
        strategyAtomGroupBiz.create(strategyAtomGroup, operator);
    }

    /**
     * 更新策略原子组
     *
     * @param strategyAtomGroup 策略原子组
     */
    @Override
    public void updateStrategyAtomGroup(StrategyAtomGroup strategyAtomGroup, String operator) {
        strategyAtomGroupBiz.update(strategyAtomGroup, operator);
    }

    /**
     * 删除策略原子组
     *
     * @param groupId 策略原子组id
     */
    @Override
    public void deleteStrategyAtomGroup(long groupId, String operator) {
        strategyAtomGroupBiz.deleteById(groupId, operator);
    }
}
