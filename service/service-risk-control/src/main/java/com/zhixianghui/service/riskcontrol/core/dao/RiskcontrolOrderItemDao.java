package com.zhixianghui.service.riskcontrol.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: xingguang li
 * @created: 2020/11/23 11:23
 */
@Repository
public class RiskcontrolOrderItemDao extends MyBatisDao<RiskcontrolOrderItem, Long> {

    public void updateStatusByPlatNoList(Map<String,Object> param) {
        this.getSqlSession().update(fillSqlId("updateStatusByPlatNoList"), param);
    }

    public RiskcontrolOrderItem getByPlatTrxNo(String platTrxNo) {
        Map<String,Object> param = new HashMap<>();
        param.put("platTrxNo", platTrxNo);
        return this.getSqlSession().selectOne(fillSqlId("getByPlatTrxNo"), param);
    }
}
