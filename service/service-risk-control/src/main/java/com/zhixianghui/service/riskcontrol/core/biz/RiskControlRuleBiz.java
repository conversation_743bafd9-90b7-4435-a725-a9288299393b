package com.zhixianghui.service.riskcontrol.core.biz;

import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskAccessPointEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskRuleTypeEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RuleRangeOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RuleRangeVariableEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import com.zhixianghui.facade.riskcontrol.entity.RuleRange;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.service.riskcontrol.core.dao.RiskControlRuleDao;
import com.zhixianghui.service.riskcontrol.core.dao.RuleRangeDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RiskControlRuleBiz {

    @Autowired
    private RiskControlRuleDao riskControlRuleDao;

    @Autowired
    private RuleRangeDao ruleRangeDao;

    @Autowired
    private StrategyAtomGroupBiz strategyAtomGroupBiz;
    @Reference
    private MerchantFacade merchantFacade;

    private static final Map<Integer, RuleRangeVariableEnum> ruleRangeVariableEnumMap;

    private static final Map<Integer, RuleRangeOperatorEnum> ruleRangeOperatorEnumMap;

    static {
        ruleRangeVariableEnumMap = new HashMap<>();
        for (RuleRangeVariableEnum variableEnum : RuleRangeVariableEnum.values()) {
            ruleRangeVariableEnumMap.put(variableEnum.getValue(), variableEnum);
        }

        ruleRangeOperatorEnumMap = new HashMap<>();
        for (RuleRangeOperatorEnum operatorEnum : RuleRangeOperatorEnum.values()) {
            ruleRangeOperatorEnumMap.put(operatorEnum.getValue(), operatorEnum);
        }
    }


    /**
     * 根据id查询风控规则
     *
     * @param id
     * @return
     */
    public RiskControlRule getById(long id) {
        RiskControlRule rule = riskControlRuleDao.getById(id);
        if (rule != null) {
            rule.setRuleRangeList(ruleRangeDao.listByRuleId(id));
        }
        return rule;
    }

    /**
     * 分页查询
     *
     * @param paramMap
     * @param pageParam
     * @return
     */
    public PageResult<List<RiskControlRule>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return riskControlRuleDao.listPage(paramMap, pageParam);
    }

    /**
     * 根据id删除
     *
     * @param id
     */
    @Transactional
    public void deleteById(long id) {
        riskControlRuleDao.deleteById(id);
        ruleRangeDao.deleteByRuleId(id);
        strategyAtomGroupBiz.deleteByRuleId(id);
    }

    /**
     * 创建风控规则
     *
     * @param rule 风控规则
     */
    @Transactional
    public long create(RiskControlRule rule) throws BizException {
        validateRule(rule);  // 校验规则适用范围
        setDefaultWeight(rule);
        riskControlRuleDao.insert(rule);
        if (rule.getRuleRangeList() != null && rule.getRuleRangeList().size() > 0) {
            for (RuleRange ruleRange : rule.getRuleRangeList()) {
                ruleRange.setRuleId(rule.getId());  // 设置规则id
            }
            ruleRangeDao.insert(rule.getRuleRangeList());
        }
        return rule.getId();
    }

    /**
     * 设置默认权重
     * @param rule
     */
    private void setDefaultWeight(RiskControlRule rule) {
        if (rule.getType().intValue() == RiskRuleTypeEnum.SPECIAL.getValue()){
            rule.setWeight(NumberUtils.INTEGER_ONE);
        }else{
            rule.setWeight(NumberUtils.INTEGER_ZERO);
        }
    }

    /**
     * 更新风控规则
     *
     * @param rule 风控规则
     */
    @Transactional
    public void update(RiskControlRule rule) throws BizException {
        RiskControlRule originRule = getById(rule.getId());
        if (originRule == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("风控规则不存在");
        }
        if (rule.getType() != null && !rule.getType().equals(originRule.getType())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("规则类型不允许修改");
        }
        setDefaultWeight(rule);

        // 设置字段
        originRule.setName(rule.getName());
        originRule.setStatus(rule.getStatus());
        originRule.setAccessPoint(rule.getAccessPoint());
        originRule.setUpdateTime(new Date());
        originRule.setUpdator(rule.getUpdator());
        originRule.setEmployerNames(rule.getEmployerNames());
        originRule.setEmployerNos(rule.getEmployerNos());
        originRule.setWeight(rule.getWeight());
        originRule.setSupplierNos(rule.getSupplierNos());
        originRule.setSupplierNames(rule.getSupplierNames());
        originRule.setRuleRangeList(rule.getRuleRangeList());

        // 校验规则
        validateRule(originRule);

        // 更新风控规则
        riskControlRuleDao.update(originRule);
        // 更新规则适用范围，先删除再插入
        ruleRangeDao.deleteByRuleId(rule.getId());
        if (originRule.getRuleRangeList() != null && originRule.getRuleRangeList().size() > 0) {
            for (RuleRange ruleRange : originRule.getRuleRangeList()) {
                ruleRange.setRuleId(rule.getId());  // 设置规则id
            }
            ruleRangeDao.insert(rule.getRuleRangeList());
        }
    }

    /**
     * 启用风控规则
     *
     * @param ruleId 风控规则id
     */
    public void enableRule(long ruleId) throws BizException {
        updateStatus(ruleId, OpenOffEnum.OPEN.getValue());
    }

    /**
     * 禁用风控规则
     *
     * @param ruleId 风控规则id
     */
    public void disableRule(long ruleId) throws BizException {
        updateStatus(ruleId, OpenOffEnum.OFF.getValue());
    }

    /**
     * 更新风控规则状态
     *
     * @param ruleId 规则id
     * @param status 状态
     */
    private void updateStatus(long ruleId, int status) throws BizException {
        RiskControlRule rule = riskControlRuleDao.getById(ruleId);
        if (rule == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("风控规则不存在");
        }
        rule.setStatus(status);
        rule.setUpdateTime(new Date());
        if (riskControlRuleDao.updateStatus(rule) == 0) {
            log.error("更新风控规则状态失败，数据库更新返回0, id:{}", ruleId);
            if (status == OpenOffEnum.OPEN.getValue()) {
                throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("启用失败");
            } else {
                throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("禁用失败");
            }
        }
    }

    /**
     * 匹配规则
     *
     * @param settleRiskControlVo
     * @return
     */
    public RiskControlRule matchRule(SettleRiskControlVo settleRiskControlVo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("supplierNo", settleRiskControlVo.getSupplierNo());
        paramMap.put("accessPoint", RiskAccessPointEnum.SETTLE.getValue());
        paramMap.put("employerNo", settleRiskControlVo.getEmployerNo());
        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        //查询所有规则
        List<RiskControlRule> rules = riskControlRuleDao.listByRiskControl(paramMap);
        if (rules.size() == 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("没有配置对应的规则，交易失败");
        }
        rules = rules.stream().sorted(Comparator.comparing(RiskControlRule::getWeight).reversed()).collect(Collectors.toList());
        //只需要第一条规则
        return rules.get(0);
    }

    /**
     * 校验规则
     *
     * @param rule
     * @throws BizException
     */
    private void validateRule(RiskControlRule rule) throws BizException {
        // 特殊规则，供应商字段不能为空
        if (StringUtil.isEmpty(rule.getSupplierNos()) || StringUtil.isEmpty(rule.getSupplierNames())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商字段不能为空");
        }

        if (rule.getRuleRangeList() != null) {
            // 条件变量和操作符校验
            for (RuleRange ruleRange : rule.getRuleRangeList()) {
                RuleRangeVariableEnum variableEnum = ruleRangeVariableEnumMap.get(ruleRange.getVariable());
                if (variableEnum == null) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("条件变量错误");
                }
                RuleRangeOperatorEnum operatorEnum = ruleRangeOperatorEnumMap.get(ruleRange.getOperator());
                if (operatorEnum == null) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("运算符错误");
                }
            }
        }
    }


    /**
     * 规则范围的计算匹配
     *
     * @param ranges 当前的规则的范围，范围是选填的
     */
    private boolean calculateRuleRange(List<RuleRange> ranges) {
        //全部范围满足and的关系才可以
        //TODO
        if (CollectionUtils.isEmpty(ranges)) {
            return false;
        }
        return false;
    }

    public List<RiskControlRule> getBySupplierNo(String supplierNo, Integer type) {
        Map<String, Object> param = new HashMap<>();
        param.put("supplierNo", supplierNo);
        param.put("type", type);
        return riskControlRuleDao.listBy("getBySupplierNos", param);
    }


	public List<HashMap<String,String>> getSupplierNos(){
        List<Merchant> suppliers = merchantFacade.getAllSupplier();
        HashMap<String,String> suppliersDicts=new HashMap<>();
        suppliers.forEach(e->{
            suppliersDicts.put(e.getMchNo(),e.getMchName());
        });
        List<RiskControlRule> riskControlRules = riskControlRuleDao.listAll();
        List<HashMap<String,String>> dict=new ArrayList<>();
        riskControlRules.forEach(e->{
            String supplierNos = e.getSupplierNos();
            if(StringUtils.isNoneBlank(supplierNos)){
                String[] nos = supplierNos.split(",");
                for (String no : nos) {
                    List<HashMap<String, String>> supplierNo = dict.stream()
                            .filter(item -> item.get("supplierNo").equals(no)).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(supplierNo)){
                        continue;
                    }
                    HashMap<String, String> item = new HashMap<>();
                    if (suppliersDicts.containsKey(no)) {
                        item.put("supplierNo", no);
                        item.put("supplierName", suppliersDicts.get(no));
                        dict.add(item);
                    }
                }
            }
        });
        return dict;
    }

    public List<HashMap<String,String>> getNoConfigSuppliers() {
        List<Merchant> suppliers = merchantFacade.getAllSupplier();
        HashMap<String,String> suppliersDicts=new HashMap<>();
        suppliers.forEach(e->{
            suppliersDicts.put(e.getMchNo(),e.getMchName());
        });

        List<RiskControlRule> riskControlRules = riskControlRuleDao.listAll();
        riskControlRules.forEach(e->{
            String supplierNos = e.getSupplierNos();
            if(StringUtils.isNoneBlank(supplierNos)) {
                String[] nos = supplierNos.split(",");
                for (String no : nos) {
                    suppliersDicts.remove(no);
                }
            }
        });
        List<HashMap<String,String>> list=new ArrayList<>();
        suppliersDicts.forEach((k,v)->{
            HashMap<String,String> item=new HashMap<>();
            item.put("supplierNo", k);
            item.put("supplierName", v);
            list.add(item);
        });
        return list;
    }

    public List<RiskControlRule> listByRiskControl(Map<String, Object> paramMap) {
        return riskControlRuleDao.listByRiskControl(paramMap);
    }
}
