package com.zhixianghui.service.riskcontrol.core.biz;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.redis.RedisKeysConstant;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.notification.NotificationReceiverTypeEnum;
import com.zhixianghui.common.statics.enums.notification.NotificationTypeEnum;
import com.zhixianghui.common.statics.enums.notification.PublishTypeEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.RiskAccessPointEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.dto.NotificationRecordDto;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.NotificationFacade;
import com.zhixianghui.facade.export.dto.GrantUserDto;
import com.zhixianghui.facade.export.service.StatisticsGrantFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.riskcontrol.constant.SupplierSpecialEnum;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import com.zhixianghui.facade.riskcontrol.enums.RiskControlTypeEnum;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.riskcontrol.util.CalculateEngine;
import com.zhixianghui.facade.riskcontrol.vo.CalculateAtomVo;
import com.zhixianghui.facade.riskcontrol.vo.GlobalAtomGroupVo;
import com.zhixianghui.facade.riskcontrol.vo.GlobalAtomVo;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @description: 风控规则处理类
 * @author: xingguang li
 * @created: 2020/10/20 10:18
 */
@Service
@Slf4j
public class RiskControlBiz {

    @Autowired
    private RiskControlRuleBiz riskControlRuleBiz;
    @Autowired
    private StrategyAtomGroupBiz strategyAtomGroupBiz;
    @Autowired
    private RedisClient redisClient;
    @Reference
    private StatisticsGrantFacade statisticsGrantFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Autowired
    private RiskcontrolProcessBiz riskcontrolProcessBiz;
    @Reference
    private RobotFacade robotFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private NotificationFacade notificationFacade;
    @Reference
    private OfflineOrderItemFacade offlineOrderItemFacade;

    /**
     * 风控处理方法
     * @param settleRiskControlVo
     */
    public RiskControlResult processRiskControl(SettleRiskControlVo settleRiskControlVo) {
        //获取名单配置
        List<GlobalAtomGroupVo> globalAtomGroupVos = nameManagementBiz.matchNameManagement(settleRiskControlVo);
        //说明名单存在，直接走名单判断
        if (globalAtomGroupVos != null){
            Boolean containsPlatform = globalAtomGroupVos.stream().anyMatch(x->SupplierSpecialEnum.PLATFORM.getNo().equals(x.getSupplierNo()));
            //查询出用户的发放统计信息
            GrantUserDto grantUserDto = statisticsGrantFacade.get(settleRiskControlVo.getStartTradeTime(),settleRiskControlVo.getPlatTrxNo(),settleRiskControlVo.getEmployerNo(),settleRiskControlVo.getUserName(),
                    settleRiskControlVo.getUserIdCard(), settleRiskControlVo.getSupplierNo(), settleRiskControlVo.getOrderAmount(),settleRiskControlVo.getSupplierNo(),settleRiskControlVo.getReceiveAccount(),settleRiskControlVo.getPhone());
            log.info("调用风控结算服务，入参：{}，风控类型：{},查询到统计发放数据：{}",
                    JsonUtil.toString(settleRiskControlVo),
                    RiskControlTypeEnum.ROSTER,
                    JsonUtil.toString(grantUserDto));
            return atomGroupValidate(settleRiskControlVo,globalAtomGroupVos,grantUserDto,RiskControlTypeEnum.ROSTER,null,containsPlatform);
        }else{
            //List<RiskControlRule> rules = riskControlRuleBiz.matchRule(settleRiskControlVo);
            RiskControlRule rule = riskControlRuleBiz.matchRule(settleRiskControlVo);
            //查询出用户的发放统计信息
            GrantUserDto grantUserDto = statisticsGrantFacade.get(settleRiskControlVo.getStartTradeTime(),settleRiskControlVo.getPlatTrxNo(),settleRiskControlVo.getEmployerNo(),settleRiskControlVo.getUserName(),
                    settleRiskControlVo.getUserIdCard(), rule.getSupplierNos(), settleRiskControlVo.getOrderAmount(),settleRiskControlVo.getSupplierNo(),settleRiskControlVo.getReceiveAccount(),settleRiskControlVo.getPhone());
            //一条规则有多个策略原子组，每个原子组之间是or的关系
            //每个原子组有多个策略原子，一个组内的多个原子是and的关系
            List<GlobalAtomGroupVo> atomGroups = strategyAtomGroupBiz.listGlobalAtomByRuleId(rule.getId());
            RiskControlResult riskControlResult = atomGroupValidate(settleRiskControlVo,atomGroups,grantUserDto,RiskControlTypeEnum.RULE,rule.getWeight(),false);
            if (!riskControlResult.equals(RiskControlResult.PASS)){
                return riskControlResult;
            }
        }
        return RiskControlResult.PASS;
    }

    @Autowired
    private NameManagementBiz nameManagementBiz;

    private RiskControlResult atomGroupValidate(SettleRiskControlVo settleRiskControlVo, List<GlobalAtomGroupVo> globalAtomGroupVos,
                                                GrantUserDto grantUserDto,RiskControlTypeEnum type,Integer ruleWeight,Boolean containsPlatform) {
        for (GlobalAtomGroupVo atomGroup : globalAtomGroupVos) {
            //如果该策略组下面的原子组为空，则继续下一个原子组
            if (CollectionUtils.isEmpty(atomGroup.getAtomVoList())) {
                continue;
            }
            //针对每一个原子组下的所有原子进行and
            RiskControlResult controlResult = calculateAtoms(atomGroup, grantUserDto, settleRiskControlVo,containsPlatform);
            if (controlResult.getCode().intValue() != RiskControlResult.TO_NEXT.getCode().intValue()) {
                //此处被风控，需记录该笔订单
                OrderItem orderItem;
                if (settleRiskControlVo.getPlatTrxNo().startsWith("OT")) {
                    final OfflineOrderItem item = offlineOrderItemFacade.getOrderItemByPlatTrxNo(settleRiskControlVo.getPlatTrxNo());
                    orderItem = new OrderItem();
                    BeanUtil.copyProperties(item, orderItem);
                }else {
                    orderItem = orderItemFacade.getByPlatTrxNo(settleRiskControlVo.getPlatTrxNo());
                }
                riskcontrolProcessBiz.controledAction(orderItem, RiskAccessPointEnum.SETTLE, atomGroup, grantUserDto,type,settleRiskControlVo,containsPlatform);
                log.info("被风控拦截，原因：{},请求信息：{}，命中的原子组：{}，当前用户统计：{}",
                        controlResult.getErrMsg(),
                        JsonUtil.toString(settleRiskControlVo),
                        JsonUtil.toString(atomGroup),
                        JsonUtil.toString(grantUserDto));

                pushWxRobot(orderItem,grantUserDto,settleRiskControlVo,controlResult);
                sendRiskEmail(controlResult, orderItem);
                sendInnerNotification(controlResult, orderItem);
                notifyHangupToMerchant(orderItem.getPlatTrxNo(), orderItem.getEmployerNo(), controlResult.getErrMsg());
                return controlResult;
            }
        }
        return RiskControlResult.PASS;
    }

    private void sendRiskEmail(RiskControlResult riskControlResult,OrderItem orderItem) {
        try {
            String cacheKey = RedisKeysConstant.HANPUP_NOTIFY_CACHE_PREFIX + orderItem.getEmployerNo();
            final String lock = redisClient.get(cacheKey);
            if (StringUtils.isNotBlank(lock)) {
                return;
            }
            final boolean set = redisClient.set(cacheKey, orderItem.getPlatTrxNo(), 3600);

            if (riskControlResult.getCode().intValue() == ControlAtomEnum.PENDING.getValue()) {
                final Merchant merchant = merchantQueryFacade.getByMchNo(orderItem.getEmployerNo());
                if (merchant == null || StringUtils.isBlank(merchant.getContactEmail())) {
                    return;
                }

                EmailParamDto dto = new EmailParamDto();
                Map<String, Object> tplParam = new HashMap<>();
                dto.setSubject(EmailTemplateEnum.HANGUP_NOTIFY.getDesc());
                dto.setHtmlFormat(true);
                dto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
                dto.setTo(merchant.getContactEmail());
                dto.setTpl(EmailTemplateEnum.HANGUP_NOTIFY.getName());


                tplParam.put("platBatchNo", orderItem.getPlatBatchNo());
                tplParam.put("platTrxNo", orderItem.getPlatTrxNo());
                tplParam.put("empNo", orderItem.getEmployerNo());
                tplParam.put("empName", orderItem.getEmployerName());
                tplParam.put("reason", riskControlResult.getErrMsg());
                tplParam.put("hangupMessage", "你有订单被挂起");
                tplParam.put("subHangupMessage","请收到邮件48小时内完成处理");
                dto.setTplParam(tplParam);

                emailFacade.sendAsync(dto);
            }
        } catch (Exception e) {
            log.error("发送风控通知邮件异常:[{}-{}]", orderItem.getEmployerNo(), orderItem.getPlatTrxNo());
        }
    }

    private void sendInnerNotification(RiskControlResult riskControlResult, OrderItem orderItem) {
        try {
            if (riskControlResult.getCode().intValue() == ControlAtomEnum.PENDING.getValue()) {
                NotificationRecordDto recordDto = new NotificationRecordDto();
                recordDto.setNotificationReceivers(JSON.toJSONString(Lists.newArrayList(orderItem.getEmployerNo())));
                recordDto.setNotificationTitle("挂单通知");
                recordDto.setNotificationType(NotificationTypeEnum.SYSTEM_NOTICE.getCode());
                recordDto.setNotificationReceiverType(NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode());
                recordDto.setPublishType(PublishTypeEnum.PUSH_NOW.getCode());
                recordDto.setPushTime(new Date());

                String notificationContent = StrUtil.format(
                        "<p><h2>你有一笔订单被挂起，请在48小时内进行处理</h2><p> 请前往 交易->挂起订单 进行处理<br>" +
                        "<h3>订单详情</h3><br>" +
                        "商户订单号：{}<br>" +
                        "姓名：{}<br>" +
                        "实发金额：￥{}<br>" +
                        "挂单原因：{}",
                        orderItem.getMchOrderNo(),
                        orderItem.getReceiveNameDecrypt(),
                        orderItem.getOrderItemNetAmount(),
                        riskControlResult.getErrMsg());

                recordDto.setNotificationContent(notificationContent);
                notificationFacade.createNotification(recordDto);
            }
        } catch (Exception e) {
            log.error("发送风控通知邮件异常:[{}-{}]", orderItem.getEmployerNo(), orderItem.getPlatTrxNo());
        }
    }

    private void notifyHangupToMerchant(String platTrxNo,String employerNo,String msg) {
        final String notifyMchnos = dataDictionaryFacade.getSystemConfig("hangup_notify_mchnos");
        if (notifyMchnos != null && notifyMchnos.split(",").length > 0) {
            final String[] mchnos = notifyMchnos.split(",");
            if (ArrayUtil.contains(mchnos, employerNo)) {
                JSONObject msgContent = new JSONObject();
                msgContent.put("platTrxNo", platTrxNo);
                msgContent.put("employerNo", employerNo);
                msgContent.put("msg", msg);
                notifyFacade.sendOne(MessageMsgDest.TOPIC_HANGUP_MERCHANT_NOTIFY,
                        employerNo, platTrxNo, NotifyTypeEnum.HANGUP_MERCHANT_NOTIFY.getValue(),
                        MessageMsgDest.TAG_HANGUP_MERCHANT_NOTIFY, msgContent.toJSONString(), MsgDelayLevelEnum.S_10.getValue());
            }
        }
    }

    private void pushWxRobot(OrderItem orderItem,GrantUserDto grantUserDto,SettleRiskControlVo settleRiskControlVo, RiskControlResult controlResult) {
        //推送企业微信机器人
        MarkDownMsg markDownMsg = new MarkDownMsg();
        markDownMsg.setUnikey(settleRiskControlVo.getOrderNo());
        markDownMsg.setRobotType(RobotTypeEnum.RISK_ROBOT.getType());

        StringBuffer sb = new StringBuffer("#### 风控提醒\\n ");
        sb.append("> 平台流水号：").append(settleRiskControlVo.getOrderNo())
                .append("\\n > 商户名称：").append(orderItem.getEmployerName())
                .append("\\n > 风控原因：").append(controlResult.getErrMsg())
                .append("\\n > 用户名称：").append(orderItem.getReceiveNameDecrypt())
                .append("\\n > 用户年龄：").append(grantUserDto.getAge())
                .append("\\n > 实发金额：").append(settleRiskControlVo.getOrderAmount())
                .append("\\n > 创建时间：").append(DateUtil.formatDateTime(new Date()))
                .append("\\n > 风控处理状态：").append(ControlAtomEnum.getEnum(controlResult.getCode()).getDesc());
        markDownMsg.setContent(sb.toString());
        robotFacade.pushMarkDownAsync(markDownMsg);
    }

    private RiskControlResult atomGroupValidateAgain(SettleRiskControlVo settleRiskControlVo, List<GlobalAtomGroupVo> globalAtomGroupVos,
                                                     GrantUserDto grantUserDto,Boolean containsPlatform,RiskControlTypeEnum type,boolean isSameRule){
        for (GlobalAtomGroupVo atomGroup : globalAtomGroupVos) {
            //如果该策略组下面的原子组为空，则继续下一个原子组
            if (CollectionUtils.isEmpty(atomGroup.getAtomVoList())) {
                continue;
            }
            //针对每一个原子组下的所有原子进行and
            RiskControlResult controlResult = calculateAtoms(atomGroup, grantUserDto, settleRiskControlVo,containsPlatform);
            if (controlResult.getCode().intValue() != RiskControlResult.TO_NEXT.getCode().intValue()) {
                //此次需要比较的是比已知传入的atomgroup的权重小的才满足，>=的是之前已经被风控了的，则此次不被风控
                //StrategyAtomGroup strategyAtomGroup = strategyAtomGroupBiz.getById(atomGroup.getObjectId());
                if ((isSameRule && atomGroup.getWeight() < settleRiskControlVo.getAtomGroupWeight()) || !isSameRule) {
                    //此处被风控，需记录该笔订单
                    OrderItem orderItem = orderItemFacade.getByPlatTrxNo(settleRiskControlVo.getPlatTrxNo());
                    riskcontrolProcessBiz.controledAction(orderItem, RiskAccessPointEnum.SETTLE, atomGroup, grantUserDto,type,settleRiskControlVo,containsPlatform);
                    log.info("后台操作继续被风控拦截，原因：{},请求信息：{}，命中的原子组：{}，当前用户统计：{}",
                            controlResult.getErrMsg(),
                            JsonUtil.toString(settleRiskControlVo),
                            JsonUtil.toString(atomGroup),
                            JsonUtil.toString(grantUserDto));
                    return controlResult;
                }
            }
        }
        return RiskControlResult.PASS;
    }

    /**
     * 风控校验，内部
     * @param settleRiskControlVo
     * @return
     */
    public RiskControlResult processRiskControl(SettleRiskControlVo settleRiskControlVo,Integer controlType) {
        //根据不同的风控类型走不同的逻辑
        if (controlType == RiskControlTypeEnum.ROSTER.getType()){
            //获取名单配置
            List<GlobalAtomGroupVo> globalAtomGroupVos = nameManagementBiz.matchNameManagement(settleRiskControlVo);
            if (globalAtomGroupVos.size() == 0){
                return RiskControlResult.PASS;
            }
            Boolean containsPlatform = globalAtomGroupVos.stream().anyMatch(x->SupplierSpecialEnum.PLATFORM.getNo().equals(x.getSupplierNo()));
            //查询出用户的发放统计信息
            GrantUserDto grantUserDto = statisticsGrantFacade.get(settleRiskControlVo.getStartTradeTime(),settleRiskControlVo.getPlatTrxNo(),settleRiskControlVo.getEmployerNo(),settleRiskControlVo.getUserName(),
                    settleRiskControlVo.getUserIdCard(), settleRiskControlVo.getSupplierNo(), settleRiskControlVo.getOrderAmount(),settleRiskControlVo.getSupplierNo(),settleRiskControlVo.getReceiveAccount(),settleRiskControlVo.getPhone());
            log.info("调用风控结算服务，入参：{}，风控类型：{},查询到统计发放数据：{}",
                    JsonUtil.toString(settleRiskControlVo),
                    RiskControlTypeEnum.ROSTER,
                    JsonUtil.toString(grantUserDto));
            //验证风控原子组，特殊名单外部没有权重，不需要判断
            return atomGroupValidateAgain(settleRiskControlVo,globalAtomGroupVos,grantUserDto,containsPlatform,RiskControlTypeEnum.ROSTER,true);
        }else{
            RiskControlRule rule = riskControlRuleBiz.matchRule(settleRiskControlVo);
            if (rule == null){
                return RiskControlResult.PASS;
            }
            //查询出用户的发放统计信息
            GrantUserDto grantUserDto = statisticsGrantFacade.get(settleRiskControlVo.getStartTradeTime(),settleRiskControlVo.getPlatTrxNo(),settleRiskControlVo.getEmployerNo(),settleRiskControlVo.getUserName(),
                    settleRiskControlVo.getUserIdCard(),rule.getSupplierNos() , settleRiskControlVo.getOrderAmount(),settleRiskControlVo.getSupplierNo(),settleRiskControlVo.getReceiveAccount(),settleRiskControlVo.getPhone());
            //一条规则有多个策略原子组，每个原子组之间是or的关系
            //每个原子组有多个策略原子，一个组内的多个原子是and的关系
            RiskControlResult riskControlResult = RiskControlResult.PASS;
                //同样的规则也要继续走风控校验，并且需要继续过滤权重
                if (rule.getId() == settleRiskControlVo.getObjectId()){
                    List<GlobalAtomGroupVo> atomGroups = strategyAtomGroupBiz.listGlobalAtomByRuleId(rule.getId());
                    riskControlResult = atomGroupValidateAgain(settleRiskControlVo,atomGroups,grantUserDto,false,RiskControlTypeEnum.RULE,true);
                }else {
                    //规则发生变化，重新走风控措施
                    List<GlobalAtomGroupVo> atomGroups = strategyAtomGroupBiz.listGlobalAtomByRuleId(rule.getId());
                    riskControlResult = atomGroupValidateAgain(settleRiskControlVo,atomGroups,grantUserDto,false,RiskControlTypeEnum.RULE,false);
                }

                if (!RiskControlResult.PASS.equals(riskControlResult)){
                    return riskControlResult;
                }
        }
        //遍历所有策略都没有匹配到，则通过
        return RiskControlResult.PASS;
    }

    /**
     * 针对每个原子组的处理
     * @param grantUserDto 用户统计信息
     * @param settleRiskControlVo
     */
    private RiskControlResult calculateAtoms(GlobalAtomGroupVo globalAtomGroupVo, GrantUserDto grantUserDto, SettleRiskControlVo settleRiskControlVo,Boolean containsPlatform) {
        //设置打款备注，方便后面过脚本
        if (StringUtils.isNotEmpty(settleRiskControlVo.getPayRemark())){
            grantUserDto.setPayRemark(settleRiskControlVo.getPayRemark());
        }else{
            grantUserDto.setPayRemark("");
        }
        //将grantUserData转成map，方便后续根据key操作
        Map<String,Object> grantUserDataMap = BeanUtil.toMap(grantUserDto);
        StrategyAtomVariableEnum[] variableEnums = StrategyAtomVariableEnum.values();
        if (containsPlatform){
            grantUserDataMap.put("monthAmount",grantUserDto.getTotalMonthAmount());
            grantUserDataMap.put("yearAmount",grantUserDto.getTotalYearAmount());
            grantUserDataMap.put("postponeYearAmount",grantUserDto.getTotalPostponeYearAmount());
        }
        int riskCount = 0;//由于统计被风控的次数
        List<String> controlTypeList = new ArrayList<>();
        for (GlobalAtomVo atom : globalAtomGroupVo.getAtomVoList()) {
            //atom的左中右分别是表达式的三个字段，中和右都是确定的，需要通过转换出来的是左
            //下面的对枚举遍历然后与grantUserDataMap的匹配操作就是根据key查出左的值
            for (StrategyAtomVariableEnum item : variableEnums) {
                if (atom.getVariable().equals(item.getValue())) {
                    String left = grantUserDataMap.get(item.getField()).toString();
                    String middle = Arrays.asList(StrategyAtomOperatorEnum.values()).stream().filter(ope->ope.getValue()==atom.getOperator()).findFirst().get().getOperater();
                    CalculateAtomVo calculateAtomVo = new CalculateAtomVo(
                            left, middle, atom.getConstant());
                    //一旦有不满足的，则退出验证，代表本个原子组不被风控
                    if (!CalculateEngine.calculate(calculateAtomVo,item,settleRiskControlVo)) {
                        return RiskControlResult.TO_NEXT;
                    } else {
                        controlTypeList.add(String.valueOf(item.getValue()));
                        riskCount++;
                    }
                }
            }
        }
        //如果被风控满足次数等于group下atom总个数，则说明全部满足
        if (riskCount == globalAtomGroupVo.getAtomVoList().size()) {
            return new RiskControlResult(globalAtomGroupVo.getControlAtom(), globalAtomGroupVo.getRemark(),controlTypeList);
        }
        return RiskControlResult.TO_NEXT;
    }

}
