package com.zhixianghui.service.riskcontrol.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.riskcontrol.entity.RuleRange;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;


@Repository
public class RuleRangeDao extends MyBatisDao<RuleRange, Long>{

    /**
     * 根据规则id查询
     */
    public List<RuleRange> listByRuleId(long ruleId) {
        return listBy(Collections.singletonMap("ruleId", ruleId));
    }

    /**
     * 根据规则id删除
     */
    public void deleteByRuleId(long ruleId) {
        this.getSqlSession().delete(fillSqlId("deleteByRuleId"), ruleId);
    }
}
