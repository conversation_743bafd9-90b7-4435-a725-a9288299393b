package com.zhixianghui.service.riskcontrol.facade;

import com.zhixianghui.facade.riskcontrol.service.NameStrategyAtomFacade;
import com.zhixianghui.service.riskcontrol.core.biz.NameStrategyAtomBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 名单策略原子表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-11-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NameStrategyAtomImpl implements NameStrategyAtomFacade {

    private final NameStrategyAtomBiz biz;
}
