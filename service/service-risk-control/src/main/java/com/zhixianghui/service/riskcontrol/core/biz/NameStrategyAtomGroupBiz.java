package com.zhixianghui.service.riskcontrol.core.biz;

import com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtomGroup;
import com.zhixianghui.service.riskcontrol.core.dao.NameStrategyAtomGroupDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
* 名单策略原子组表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-11-05
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NameStrategyAtomGroupBiz {

    private final NameStrategyAtomGroupDao namestrategyatomgroupDao;

}