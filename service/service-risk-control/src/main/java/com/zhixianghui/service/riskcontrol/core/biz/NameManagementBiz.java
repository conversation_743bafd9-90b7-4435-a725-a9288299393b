package com.zhixianghui.service.riskcontrol.core.biz;

import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.dto.EnterprisePersonnelDto;
import com.zhixianghui.facade.merchant.entity.MerchantEnterprisePersonnel;
import com.zhixianghui.facade.riskcontrol.constant.SupplierSpecialEnum;
import com.zhixianghui.facade.riskcontrol.constant.UserLatitudeEnum;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementDTO;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementQueryDTO;
import com.zhixianghui.facade.riskcontrol.entity.NameManagement;
import com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtom;
import com.zhixianghui.facade.riskcontrol.entity.NameStrategyAtomGroup;
import com.zhixianghui.facade.riskcontrol.vo.GlobalAtomGroupVo;
import com.zhixianghui.facade.riskcontrol.vo.NameManagementVo;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.service.riskcontrol.core.dao.NameManagementDao;
import com.zhixianghui.service.riskcontrol.core.dao.NameStrategyAtomDao;
import com.zhixianghui.service.riskcontrol.core.dao.NameStrategyAtomGroupDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 名单管理表Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2021-11-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class NameManagementBiz {

    private final NameManagementDao namemanagementDao;

    private final NameStrategyAtomGroupDao namestrategyatomgroupDao;

    private final NameStrategyAtomDao namestrategyatomDao;


    @Transactional(rollbackFor = Exception.class)
    public void create(NameManagementDTO managementDTO) {
        Date date = new Date();
        log.info("添加名单入参-------->{}", managementDTO);
        checkCreate(managementDTO,Boolean.FALSE);
//        checkRuleType(managementDTO);
        NameManagement nameManagement = BeanUtil.toObject(NameManagement.class, managementDTO);
        nameManagement.setUpdateTime(date);
        nameManagement.setCreateTime(date);
        log.info("查看名单实体---------->{}", nameManagement);
        namemanagementDao.insert(nameManagement);
        insert(managementDTO, nameManagement.getId());
    }

    public void checkCreate(NameManagementDTO managementDTO,boolean isUpdate) {
        HashMap<String, Object> map = new HashMap();
        map.put("mchNo", managementDTO.getMchNo());
        map.put("userLatitude", managementDTO.getUserLatitude());
        map.put("username", managementDTO.getUsername());
        map.put("idCardNumber", managementDTO.getIdCardNumber());
        log.info("校验参数------------------->{}",map);
        NameManagement nameManagement = namemanagementDao.getOne(map);
        log.info("校验结果------------------->{}",nameManagement);
        if (!ObjectUtils.isEmpty(nameManagement)){
            if(isUpdate){
                if(managementDTO.getId().equals(nameManagement.getId())){
                    return;
                }
            }
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该身份在此商户已存在");
        }
    }


//    public void checkRuleType(NameManagementDTO managementDTO){
//        List<NameManagementDTO.NameStrategyAtomGroupDTO> nameStrategyAtomGroups = managementDTO.getNameStrategyAtomGroups();
//        if (CollectionUtils.isEmpty(nameStrategyAtomGroups)) {
//            return;
//        }
//        if(managementDTO.getRuleType()== RuleTypeEnum.GENERAL.getValue()){
//            List<NameManagementDTO.NameStrategyAtomGroupDTO> list =
//                    nameStrategyAtomGroups.stream().filter(e -> !StringUtils.isEmpty(e.getSupplierNo())).collect(Collectors.toList());
//            if(list.size()>0){
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通用规则不允许选择供应商");
//            }
//        }
//    }

    @Transactional(rollbackFor = Exception.class)
    public void insert(NameManagementDTO managementDTO, Long id) {
        Date date = new Date();
        List<NameManagementDTO.NameStrategyAtomGroupDTO> nameStrategyAtomGroups = managementDTO.getNameStrategyAtomGroups();
        log.info("查看策略原子组---------->{}", nameStrategyAtomGroups);
        if (CollectionUtils.isEmpty(nameStrategyAtomGroups)) {
            return;
        }

        nameStrategyAtomGroups.forEach(e -> {
            e.setId(null);
            e.setNameManagementId(id);
            e.setCreateTime(date);
            e.setUpdateTime(date);
            NameStrategyAtomGroup nameStrategyAtomGroup = BeanUtil.toObject(NameStrategyAtomGroup.class, e);
            namestrategyatomgroupDao.insert(nameStrategyAtomGroup);
            List<NameStrategyAtom> nameStrategyAtoms = e.getNameStrategyAtoms();
            nameStrategyAtoms.forEach(n -> {
                n.setId(null);
                n.setGroupId(nameStrategyAtomGroup.getId());
                n.setCreateTime(date);
            });
            log.info("查看策略原子---------->{}", nameStrategyAtoms);
            namestrategyatomDao.insert(nameStrategyAtoms);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(NameManagementDTO managementDTO) {
        Date date = new Date();
        log.info("修改名单入参-------->{}", managementDTO);
        checkCreate(managementDTO,Boolean.TRUE);
//        checkRuleType(managementDTO);
        NameManagement nameManagement = BeanUtil.toObject(NameManagement.class, managementDTO);
        log.info("修改名单实体---------->{}", nameManagement);
        NameManagement old = namemanagementDao.getById(nameManagement.getId());
        Integer version = old.getVersion();
        nameManagement.setUpdateTime(date);
        nameManagement.setVersion(version);
        namemanagementDao.updateIfNotNull(nameManagement);
        List<NameManagementDTO.NameStrategyAtomGroupDTO> nameStrategyAtomGroups = managementDTO.getNameStrategyAtomGroups();
        log.info("查看策略原子组---------->{}", nameStrategyAtomGroups);
        deleteSubset(nameManagement.getId());
        insert(managementDTO, nameManagement.getId());
    }

    //存在性能问题，后面可优化
    @Transactional(rollbackFor = Exception.class)
    public void delete(String ids) {
        log.info("删除名单ids---------->{}", ids);
        String[] idsArr = ids.split(",");
        for (String s : idsArr) {
            Long id = null;
            try {
                id = Long.parseLong(s);
            } catch (NumberFormatException e) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数格式异常");
            }
            namemanagementDao.deleteById(id);
            deleteSubset(id);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void deleteSubset(Long id) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("nameManagementId", id);
        List<NameStrategyAtomGroup> groups = namestrategyatomgroupDao.listBy(params);
        if (!CollectionUtils.isEmpty(groups)) {
            List<Long> groupIds = groups.stream().map(NameStrategyAtomGroup::getId).collect(Collectors.toList());
            namestrategyatomgroupDao.deleteByIdList(groupIds);
            namestrategyatomDao.deleteBy("deleteByGroupId", groupIds);
        }
    }

    public PageResult<List<NameManagementVo>> listPage(NameManagementQueryDTO nameManagementQueryDTO) {
        PageResult<List<NameManagementVo>> listPageResult = namemanagementDao.listByQuery(nameManagementQueryDTO);
        log.info("分页查询结果---------------->{}", listPageResult.getData());
        List<NameManagementVo> totalRecord = listPageResult.getData();
        log.info("开始格式化数据------------->{}", totalRecord);
        totalRecord.forEach(e -> {
            HashMap<String, Object> map = new HashMap<>();
            map.put("nameManagementId", e.getId());
            List<NameStrategyAtomGroup> groups = namestrategyatomgroupDao.listBy(map,"weight DESC");
            log.info("策略原子组查询------------->{}", groups);
            List<NameManagementDTO.NameStrategyAtomGroupDTO> list = getGroups(groups);
            e.setNameStrategyAtomGroups(list);
            list.forEach(g -> {
                HashMap<String, Object> param = new HashMap<>();
                param.put("groupId", g.getId());
                List<NameStrategyAtom> nameStrategyAtoms = namestrategyatomDao.listBy(param);
                log.info("策略原子查询------------->{}", nameStrategyAtoms);
                g.setNameStrategyAtoms(nameStrategyAtoms);
            });

            if (!StringUtils.isEmpty(e.getSupplierName())) {
                e.setSupplierName(distinct(e.getSupplierName()));
                log.info("供应商名称去从------------->{}", e.getSupplierName());
            }
            if (!StringUtils.isEmpty(e.getControlAtom())) {
                e.setControlAtom(distinct(e.getControlAtom()));
                e.setControlAtomName(changeCN(e.getControlAtom()));
                log.info("管控原子名称去从------------->{}", e.getSupplierName());
            }
        });
        log.info("格式化结束------------->{}", totalRecord);
        return listPageResult;
    }

    /**
     * 获取管控原子组
     */
    public List<NameManagementDTO.NameStrategyAtomGroupDTO> getGroups(List<NameStrategyAtomGroup> groups) {
        List<NameManagementDTO.NameStrategyAtomGroupDTO> list = new ArrayList<>();
        groups.forEach(g -> {
            NameManagementDTO.NameStrategyAtomGroupDTO group = BeanUtil.toObject(NameManagementDTO.NameStrategyAtomGroupDTO.class, g);
            list.add(group);
        });
        return list;
    }

    /**
     * list去从
     */
    public String distinct(String names) {
        String[] arr = names.split(",");
        List<String> lists = Arrays.asList(arr);
        return lists.stream().distinct().collect(Collectors.joining(","));
    }

    /**
     * 管控原子中文转换
     */
    public String changeCN(String controlAtom) {
        String[] items = controlAtom.split(",");
        if (items.length == 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("管控原子查询异常");
        }
        List<String> controlAtomList = new ArrayList<>();
        HashMap<Integer, String> maps = NameManagementBiz.getMaps();
        for (int i = 0; i < items.length; i++) {
            int atom = Integer.parseInt(items[i]);
            String name = maps.get(atom);
            if (!StringUtil.isEmpty(name)) {
                controlAtomList.add(name);
            }
        }
        Collections.sort(controlAtomList);
        return String.join(",", controlAtomList);
    }

    /**
     * 获取管控原子枚举
     */
    public static HashMap<Integer, String> getMaps() {
        HashMap<Integer, String> map = new HashMap<>();
        for (ControlAtomEnum controlAtomEnum : ControlAtomEnum.values()) {
            map.put(controlAtomEnum.getValue(), controlAtomEnum.getDesc());
        }
        return map;
    }


    public List<GlobalAtomGroupVo> matchNameManagement(SettleRiskControlVo settleRiskControlVo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",settleRiskControlVo.getEmployerNo());
        paramMap.put("username",settleRiskControlVo.getUserName());
        paramMap.put("idCardNumber", settleRiskControlVo.getUserIdCard());
        List<NameManagement> nameManagementList = namemanagementDao.listBy("matchName",paramMap);
        if (nameManagementList.size() == 0){
            return null;
        }
        NameManagement nameManagement = nameManagementList.get(0);
        paramMap.clear();
        paramMap.put("nameManagementId",nameManagement.getId());
        paramMap.put("supplierNo",settleRiskControlVo.getSupplierNo());
        paramMap.put("defaultSupplierNo", SupplierSpecialEnum.PLATFORM.getNo());
        List<GlobalAtomGroupVo> globalAtomGroupVos = namestrategyatomgroupDao.
                listBy("listByNameManagementId",paramMap);
        if (globalAtomGroupVos.size() == 0){
            return null;
        }
        return globalAtomGroupVos;
    }


    @Transactional(rollbackFor = Exception.class)
    public void sync(List<EnterprisePersonnelDto> personnelDtos){
        if(CollectionUtils.isEmpty(personnelDtos)){
            return;
        }
        String mchNo = personnelDtos.get(0).getMchNo();
        List<NameManagement> names = namemanagementDao.getByMchNo(mchNo);
        personnelDtos.forEach(e->{
            NameManagement nameManagement = namemanagementDao.getOne(Collections.singletonMap("enterprisePersonnelId", e.getId()));
            if(ObjectUtils.isEmpty(nameManagement)){
                insertName(e);
            }else {
                nameManagement.setUsername(e.getName());
                nameManagement.setUpdateTime(new Date());
                nameManagement.setIdCardNumber(e.getIdCardNumber());
                if(StringUtil.isEmpty(nameManagement.getIdCardNumber())){
                    nameManagement.setUserLatitude(UserLatitudeEnum.USERNAME.getValue());
                }else {
                    nameManagement.setUserLatitude(UserLatitudeEnum.USERNAME_AND_NUMBER.getValue());
                }
                namemanagementDao.update(nameManagement);
            }
        });
        List<Long> deleteItem = names.stream().filter(e -> {
            for (EnterprisePersonnelDto item : personnelDtos) {
                if (item.getId().equals(e.getEnterprisePersonnelId())) {
                    return false;
                }
            }
            return true;
        }).map(NameManagement::getId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(deleteItem)){
            deleteNames(deleteItem);
        }

    }

    private void deleteNames(List<Long> deleteItem) {
        deleteItem.forEach(id->{
            List<NameStrategyAtomGroup> groups = namestrategyatomgroupDao.listBy(Collections.singletonMap("nameManagementId", id));
            groups.forEach(e->{
                namestrategyatomDao.deleteBy("deleteByGroup", e.getId());
                namestrategyatomgroupDao.deleteById(e.getId());
            });
        });
        namemanagementDao.deleteByIdList(deleteItem);
    }


    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<EnterprisePersonnelDto> personnelDtos){
        personnelDtos.forEach(this::insertName);
    }

    public void insertName(EnterprisePersonnelDto personnel){
        Date date=new Date();
        NameManagement nameManagement=BeanUtil.toObject(NameManagement.class, personnel);
        nameManagement.setVersion(0);
        nameManagement.setCreateTime(date);
        nameManagement.setUpdateTime(date);
        nameManagement.setUsername(personnel.getName());
        nameManagement.setEnterprisePersonnelId(personnel.getId());
        if(StringUtil.isEmpty(nameManagement.getIdCardNumber())){
            nameManagement.setUserLatitude(UserLatitudeEnum.USERNAME.getValue());
        }else {
            nameManagement.setUserLatitude(UserLatitudeEnum.USERNAME_AND_NUMBER.getValue());
        }
        nameManagement.setWarehousingReason("报税风险：疑似董监高人员，请联系客户经理处理");
        namemanagementDao.insert(nameManagement);
        NameStrategyAtomGroup nameStrategyAtomGroup = insertGroup(nameManagement.getId());
        insetStrategyAtom(nameStrategyAtomGroup.getId());
    }

    private NameStrategyAtomGroup insertGroup(Long id) {
        Date date=new Date();
        NameStrategyAtomGroup nameStrategyAtomGroup=new NameStrategyAtomGroup();
        nameStrategyAtomGroup.setSupplierName(SupplierSpecialEnum.PLATFORM.getName());
        nameStrategyAtomGroup.setSupplierNo(SupplierSpecialEnum.PLATFORM.getNo());
        nameStrategyAtomGroup.setWeight(1);
        nameStrategyAtomGroup.setControlAtom(ControlAtomEnum.PENDING.getValue());
        nameStrategyAtomGroup.setRemark("报税风险：疑似董监高人员，请联系客户经理处理");
        nameStrategyAtomGroup.setCreateTime(date);
        nameStrategyAtomGroup.setUpdateTime(date);
        nameStrategyAtomGroup.setNameManagementId(id);
        namestrategyatomgroupDao.insert(nameStrategyAtomGroup);
        return nameStrategyAtomGroup;
    }

    private void insetStrategyAtom(Long id) {
        Date date=new Date();
        NameStrategyAtom nameStrategyAtom=new NameStrategyAtom();
        nameStrategyAtom.setGroupId(id);
        nameStrategyAtom.setCreateTime(date);
        nameStrategyAtom.setVersion(0);
        nameStrategyAtom.setConstant("100");
        nameStrategyAtom.setVariable(StrategyAtomVariableEnum.USER_MONTH_AMOUNT.getValue());
        nameStrategyAtom.setOperator(StrategyAtomOperatorEnum.GREATER_THAN.getValue());
        namestrategyatomDao.insert(nameStrategyAtom);
    }

    public List<NameManagement> list(String mchNo) {
        return namemanagementDao.getByMchNo(mchNo);
    }
}