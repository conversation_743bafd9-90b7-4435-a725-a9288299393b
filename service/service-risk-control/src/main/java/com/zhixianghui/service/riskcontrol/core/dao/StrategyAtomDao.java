package com.zhixianghui.service.riskcontrol.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.riskcontrol.entity.StrategyAtom;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;


@Repository
public class StrategyAtomDao extends MyBatisDao<StrategyAtom, Long>{

    /**
     * 根据原子组id查询
     */
    public List<StrategyAtom> listByGroupId(long groupId) {
        return listBy(Collections.singletonMap("groupId", groupId));
    }

    /**
     * 根据原子组id删除
     */
    public void deleteByGroupId(long groupId) {
        this.getSqlSession().delete(fillSqlId("deleteByGroupId"), groupId);
    }

    /**
     * 根据原子组id列表删除
     */
    public void deleteByGroupIds(List<Long> groupIds) {
        this.getSqlSession().delete(fillSqlId("deleteByGroupIds"), groupIds);
    }
}
