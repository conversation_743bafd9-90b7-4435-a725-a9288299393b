package com.zhixianghui.service.riskcontrol;

import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * 风控服务
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
public class ServiceRiskControlApp {
    public static void main(String[] args) {
        new SpringApplicationBuilder(ServiceRiskControlApp.class).web(WebApplicationType.NONE).run(args);
    }
}
