package com.zhixianghui.service.riskcontrol.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: xingguang li
 * @created: 2020/11/23 11:23
 */
@Repository
public class RiskcontrolProcessDetailDao extends MyBatisDao<RiskcontrolProcessDetail, Long> {

    public PageResult<List<RiskcontrolProcessDetail>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return listPage("listPage", "listPageCount", paramMap, pageParam);
    }

    public void updateStatusByIdList(Map<String,Object> param) {
        this.getSqlSession().update(fillSqlId("updateStatusByIdList"), param);
    }
}
