package com.zhixianghui.service.riskcontrol.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import com.zhixianghui.facade.riskcontrol.service.RiskControlRuleFacade;
import com.zhixianghui.service.riskcontrol.core.biz.RiskControlRuleBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RiskControlRuleFacadeImpl implements RiskControlRuleFacade {

    @Autowired
    private RiskControlRuleBiz riskControlRuleBiz;

    /**
     * 创建风控规则
     *
     * @param riskControlRule 风控规则
     */
    @Override
    public long create(RiskControlRule riskControlRule) throws BizException {
        return riskControlRuleBiz.create(riskControlRule);
    }

    /**
     * 根据id获取风控规则
     *
     * @param id 风控规则id
     * @return 风控规则
     */
    @Override
    public RiskControlRule getById(long id) {
        return riskControlRuleBiz.getById(id);
    }

    /**
     * 分页查询
     *
     * @param paramMap
     * @param pageParam
     * @return
     */
    @Override
    public PageResult<List<RiskControlRule>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return riskControlRuleBiz.listPage(paramMap, pageParam);
    }

    /**
     * 更新风控规则
     *
     * @param riskControlRule 风控规则
     */
    @Override
    public void update(RiskControlRule riskControlRule) throws BizException {
        riskControlRuleBiz.update(riskControlRule);
    }

    /**
     * 启用风控规则
     *
     * @param ruleId 风控规则id
     */
    @Override
    public void enableRule(long ruleId) throws BizException {
        riskControlRuleBiz.enableRule(ruleId);
    }

    /**
     * 禁用风控规则
     *
     * @param ruleId 风控规则id
     */
    @Override
    public void disableRule(long ruleId) throws BizException {
        riskControlRuleBiz.disableRule(ruleId);
    }

    /**
     * 根据id删除
     *
     * @param id 风控规则id
     */
    @Override
    public void deleteById(long id) {
        riskControlRuleBiz.deleteById(id);
    }

    @Override
    public List<RiskControlRule> getBySupplierNoType(String supplierNo, Integer type) {
        return riskControlRuleBiz.getBySupplierNo(supplierNo, type);
    }

    @Override
    public List<HashMap<String, String>> getSupplierNos() {
        return riskControlRuleBiz.getSupplierNos();
    }

    @Override
    public List<HashMap<String,String>> getNoConfigSuppliers() {
        return riskControlRuleBiz.getNoConfigSuppliers();
    }
}
