package com.zhixianghui.service.riskcontrol.facade;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementDTO;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementQueryDTO;
import com.zhixianghui.facade.riskcontrol.entity.NameManagement;
import com.zhixianghui.facade.riskcontrol.service.NameManagementFacade;
import com.zhixianghui.facade.riskcontrol.vo.NameManagementVo;
import com.zhixianghui.service.riskcontrol.core.biz.NameManagementBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 名单管理表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-11-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NameManagementImpl implements NameManagementFacade {

    private final NameManagementBiz biz;


    @Override
    public void create(NameManagementDTO managementDTO) {
        biz.create(managementDTO);
    }

    @Override
    public void update(NameManagementDTO managementDTO) {
        biz.update(managementDTO);
    }

    @Override
    public void delete(String ids) {
        biz.delete(ids);
    }

    @Override
    public PageResult<List<NameManagementVo>> listPage(NameManagementQueryDTO nameManagementQueryDTO) {
        return biz.listPage(nameManagementQueryDTO);
    }

    @Override
    public List<NameManagement> list(String mchNo) {
        return biz.list(mchNo);
    }
}
