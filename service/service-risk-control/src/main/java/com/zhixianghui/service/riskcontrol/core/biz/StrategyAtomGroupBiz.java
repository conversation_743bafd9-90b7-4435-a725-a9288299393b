package com.zhixianghui.service.riskcontrol.core.biz;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import com.zhixianghui.facade.riskcontrol.entity.StrategyAtom;
import com.zhixianghui.facade.riskcontrol.vo.GlobalAtomGroupVo;
import com.zhixianghui.service.riskcontrol.core.dao.RiskControlRuleDao;
import com.zhixianghui.service.riskcontrol.core.dao.StrategyAtomDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zhixianghui.facade.riskcontrol.entity.StrategyAtomGroup;
import com.zhixianghui.service.riskcontrol.core.dao.StrategyAtomGroupDao;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class StrategyAtomGroupBiz{
	
	@Autowired
	private StrategyAtomGroupDao strategyAtomGroupDao;

	@Autowired
	private StrategyAtomDao strategyAtomDao;

	@Autowired
	private RiskControlRuleDao riskControlRuleDao;

	/**
	 * 根据id获取策略原子组
	 *
	 * @param id 原子组id
	 * @return
	 */
	public StrategyAtomGroup getById(long id) {
		StrategyAtomGroup group = strategyAtomGroupDao.getById(id);
		if (group != null) {
			group.setStrategyAtomList(strategyAtomDao.listByGroupId(id));
		}
		return group;
	}

	/**
	 * 查询风控规则的策略原子组，根据权重值排序
	 *
	 * @param ruleId 风控规则id
	 * @return
	 */
	public List<StrategyAtomGroup> listByRuleId(long ruleId) {
		return strategyAtomGroupDao.listByRuleId(ruleId);
	}

	public List<GlobalAtomGroupVo> listGlobalAtomByRuleId(long ruleId) {
		return strategyAtomGroupDao.listGlobalAtomByRuleId(ruleId);
	}

	/**
	 * 删除风控规则下所有的策略原子组
	 * @param ruleId
	 */
	@Transactional
	public void deleteByRuleId(long ruleId) {
		List<Long> groupIds = strategyAtomGroupDao.listByRuleId(ruleId)
				.stream().map(StrategyAtomGroup::getId).collect(Collectors.toList());
		strategyAtomGroupDao.deleteByRuleId(ruleId);
		if (groupIds.size() > 0) {
			strategyAtomDao.deleteByGroupIds(groupIds);
		}
	}

	/**
	 * 删除策略原子组
	 *
	 * @param id 策略原子组id
	 */
	@Transactional
	public void deleteById(long id, String operator){
		StrategyAtomGroup strategyAtomGroup = strategyAtomGroupDao.getById(id);
		strategyAtomGroupDao.deleteById(id);
		strategyAtomDao.deleteByGroupId(id);
		//更新风控规则的操作人
		RiskControlRule riskControlRule = riskControlRuleDao.getById(strategyAtomGroup.getRuleId());
		riskControlRule.setUpdator(operator);
		riskControlRule.setUpdateTime(new Date());
		riskControlRuleDao.update(riskControlRule);
	}

	/**
	 * 创建策略原子组
	 *
	 * @param strategyAtomGroup 策略原子组
	 */
	@Transactional
	public void create(StrategyAtomGroup strategyAtomGroup, String operator) {
		strategyAtomGroupDao.insert(strategyAtomGroup);
		if (strategyAtomGroup.getStrategyAtomList() != null && strategyAtomGroup.getStrategyAtomList().size() > 0) {
			for (StrategyAtom strategyAtom : strategyAtomGroup.getStrategyAtomList()) {
				strategyAtom.setGroupId(strategyAtomGroup.getId());  // 设置策略组id
			}
			strategyAtomDao.insert(strategyAtomGroup.getStrategyAtomList());
		}
		//更新风控规则的操作人
		RiskControlRule riskControlRule = riskControlRuleDao.getById(strategyAtomGroup.getRuleId());
		riskControlRule.setUpdator(operator);
		riskControlRule.setUpdateTime(new Date());
		riskControlRuleDao.update(riskControlRule);
	}

	/**
	 * 更新策略原子组
	 *
	 * @param strategyAtomGroup 策略原子组
	 */
	@Transactional
	public void update(StrategyAtomGroup strategyAtomGroup, String operator) {
		StrategyAtomGroup originGroup = strategyAtomGroupDao.getById(strategyAtomGroup.getId());
		if (originGroup == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("策略原子组不存在");
		}
		originGroup.setWeight(strategyAtomGroup.getWeight());
		originGroup.setControlAtom(strategyAtomGroup.getControlAtom());
		originGroup.setRemark(strategyAtomGroup.getRemark());
		originGroup.setUpdateTime(new Date());
		strategyAtomGroupDao.update(originGroup);

		// 先删除旧的策略原子再插入新的
		strategyAtomDao.deleteByGroupId(originGroup.getId());
		if (strategyAtomGroup.getStrategyAtomList() != null && strategyAtomGroup.getStrategyAtomList().size() > 0) {
			for (StrategyAtom strategyAtom : strategyAtomGroup.getStrategyAtomList()) {
				strategyAtom.setGroupId(originGroup.getId());
			}
			strategyAtomDao.insert(strategyAtomGroup.getStrategyAtomList());
		}

		//更新风控规则的操作人
		RiskControlRule riskControlRule = riskControlRuleDao.getById(strategyAtomGroup.getRuleId());
		riskControlRule.setUpdator(operator);
		riskControlRule.setUpdateTime(new Date());
		riskControlRuleDao.update(riskControlRule);
	}
}
