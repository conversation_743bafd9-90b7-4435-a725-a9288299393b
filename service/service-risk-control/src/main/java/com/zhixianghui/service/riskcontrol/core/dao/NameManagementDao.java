package com.zhixianghui.service.riskcontrol.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementQueryDTO;
import com.zhixianghui.facade.riskcontrol.entity.NameManagement;
import com.zhixianghui.facade.riskcontrol.vo.NameManagementVo;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 名单管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@Repository
public class NameManagementDao extends MyBatisDao<NameManagement, Long> {

    public PageResult<List<NameManagementVo>> listByQuery(NameManagementQueryDTO nameManagementQueryDTO){
        PageParam pageParam = new PageParam();
        pageParam.setPageSize(nameManagementQueryDTO.getPageSize());
        pageParam.setPageCurrent(nameManagementQueryDTO.getPageCurrent());
        return this.listPage("listByQuery","listByQueryCount", BeanUtil.toMap(nameManagementQueryDTO),pageParam);
    }

    public List<NameManagement> getByMchNo(String mchNo){
        return this.listBy("selectByMchNo", Collections.singletonMap("mchNo", mchNo));
    }
}
