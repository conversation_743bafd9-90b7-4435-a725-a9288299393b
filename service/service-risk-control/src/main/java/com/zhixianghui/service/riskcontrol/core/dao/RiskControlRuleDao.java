package com.zhixianghui.service.riskcontrol.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class RiskControlRuleDao extends MyBatisDao<RiskControlRule, Long>{

    /**
     * 分页查询
     */
    public PageResult<List<RiskControlRule>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return listPage("listPage", "listPageCount", paramMap, pageParam);
    }

    /**
     * 更新状态
     */
    public int updateStatus(RiskControlRule rule) {
        return this.getSqlSession().update(fillSqlId("updateStatus"), rule);
    }

    /**
     * 根据适用范围”供应商属于“过滤出符合的规则
     */
    public List<RiskControlRule> listBySupplierNo(Map<String, Object> paramMap) {
        return listBy("listBySupplierNo", paramMap);
    }

    public RiskControlRule selectOne(Map<String, Object> paramMap) {
        return getOne("selectOne", paramMap);
    }

    public List<RiskControlRule> listByRiskControl(Map<String, Object> paramMap) {
        return listBy("listByRiskControl",paramMap);
    }
}
