package com.zhixianghui.service.riskcontrol.listener;

import com.alibaba.fastjson.JSONArray;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.risk.NameManagementAuthDTO;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.dto.EnterprisePersonnelDto;
import com.zhixianghui.service.riskcontrol.core.biz.NameManagementBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月06日 14:25:00
 */
@Component
@Slf4j
public class MameManagementListener {
    @Autowired
    private NameManagementBiz nameManagementBiz;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_RISK_NAME_INSERT, selectorExpression = MessageMsgDest.TAG_RISK_NAME_INSERT, consumeThreadMax = 1, consumerGroup = "createNameConsume")
    public class CreateNameMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("名单参数不能为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            List<EnterprisePersonnelDto> personnelDtos = JSONArray.parseArray(msg, EnterprisePersonnelDto.class);
            log.info("同步企业人员信息，数据:{}",personnelDtos);
            nameManagementBiz.batchInsert(personnelDtos);
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_RISK_NAME_SYNC, selectorExpression = MessageMsgDest.TAG_RISK_NAME_SYNC, consumeThreadMax = 1, consumerGroup = "syncNameConsume")
    public class SyncNameMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("名单参数不能为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            List<EnterprisePersonnelDto> personnelDtos = JSONArray.parseArray(msg, EnterprisePersonnelDto.class);
            log.info("同步企业人员信息，数据:{}",personnelDtos);
            nameManagementBiz.sync(personnelDtos);
        }
    }
}