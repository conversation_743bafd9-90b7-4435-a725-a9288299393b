<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-service</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>service-risk-control</artifactId>


    <build>
        <finalName>${appName}</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-service</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-risk-control</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-export</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-trade</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-merchant</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-banklink</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-notify</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency> <!-- 把nacos作为配置中心时需要引入的依赖 -->
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>generic-service-starter</artifactId>

        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-tools</artifactId>
            <version>${rocketmq.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>component-starter</artifactId>

        </dependency>

        <!-- redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!-- redis -->
    </dependencies>
</project>
