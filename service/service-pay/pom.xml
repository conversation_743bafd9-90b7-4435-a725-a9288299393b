<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-service</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>service-pay</artifactId>

    <description>汇付通服务</description>

    <build>
        <finalName>${appName}</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <properties>
        <alipay.version>2.2.1</alipay.version>
        <httpclient.version>3.0</httpclient.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>component-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>component-starter</artifactId>
        </dependency>

        <dependency> <!-- 把nacos作为配置中心时需要引入的依赖 -->
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-tools</artifactId>
            <version>${rocketmq.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-service</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!-- facade -->
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-trade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-notify</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-banklink</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>api-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-merchant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-flow</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-fee</artifactId>
        </dependency>
        <!--fastdfs-->
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>fastdfs-client</artifactId>
            <version>${fastdfs-client.version}</version>
        </dependency>
        <!--fsatdfs-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-easysdk</artifactId>
            <version>${alipay.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.17.0.ALL</version>
        </dependency>
    </dependencies>

</project>
