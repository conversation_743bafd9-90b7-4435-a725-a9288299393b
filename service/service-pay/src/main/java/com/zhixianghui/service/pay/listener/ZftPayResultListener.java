package com.zhixianghui.service.pay.listener;

import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.pay.order.core.biz.PayProcessBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName FlowChannelAuditListener
 * @Description TODO
 * @Date 2023/5/10 11:41
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ZFT_PAY_RESULT_CALLBACK,selectorExpression = MessageMsgDest.TAG_ZFT_PAY_RESULT_CALLBACK,consumeThreadMax = 5,consumerGroup = "zftPayResultConsumerGroup")
public class ZftPayResultListener extends BaseRocketMQListener<String> {
    @Autowired
    private PayProcessBiz payProcessBiz;
    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        payProcessBiz.handlePayResult(JSONUtil.parseObj(jsonParam));
    }
}
