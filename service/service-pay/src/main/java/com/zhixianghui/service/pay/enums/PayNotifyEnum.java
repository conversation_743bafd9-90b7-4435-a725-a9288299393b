package com.zhixianghui.service.pay.enums;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PayNotifyEnum {

    ALIPAY_REQUERY_MSG("topic-pay-alipay-trade-requery", "tag-pay-alipay-trade-requery", 10001),
    ALIPAY_SETTLE_NOTIFY(MessageMsgDest.TOPIC_ZFT_SETTLE_NOTIFY, MessageMsgDest.TAG_ZFT_SETTLE_NOTIFY, 10002),
    ALIPAY_ROYALTY_NOTIFY(MessageMsgDest.TOPIC_ZFT_ROYALTY_NOTIFY, MessageMsgDest.TAG_ZFT_ROYALTY_NOTIFY, 10003),
    ;


    private String topicName;
    private String tagExpress;

    private Integer notifyType;

}
