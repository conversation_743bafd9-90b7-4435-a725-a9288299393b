package com.zhixianghui.service.pay.order.core.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyRecord;
import com.zhixianghui.service.pay.order.core.mapper.PayApplyRecordMapper;
@Service
public class PayApplyRecordService extends ServiceImpl<PayApplyRecordMapper, PayApplyRecord> {
    public PayApplyRecord getByPaytrxNo(String trxNo) {
        PayApplyRecord applyRecord = this.getOne(new QueryWrapper<PayApplyRecord>().eq(PayApplyRecord.COL_PAY_TRX_NO, trxNo));
        return applyRecord;
    }

    public PayApplyRecord getByPayOrderNo(String orderNo) {
        PayApplyRecord applyRecord = this.getOne(new QueryWrapper<PayApplyRecord>().eq("pay_order_no", orderNo));
        return applyRecord;
    }
}
