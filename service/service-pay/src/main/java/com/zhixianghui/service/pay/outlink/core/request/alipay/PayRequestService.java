package com.zhixianghui.service.pay.outlink.core.request.alipay;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.*;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.pay.outlink.dto.PayApplyDto;
import com.zhixianghui.facade.pay.outlink.dto.RoyaltyDto;
import com.zhixianghui.facade.pay.outlink.dto.SettleConfirmDto;
import com.zhixianghui.service.pay.config.AlipayConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class PayRequestService {
    final private CertAlipayRequest certAlipayRequest;
    final private AlipayConfig alipayConfig;

    private static final BigDecimal TO_ALIPAY_LIMIT = new BigDecimal("100000");
    private static final BigDecimal TO_BANKCARD_LIMIT = new BigDecimal("50000");


    /**
     * 创建直付通商户
     * @param paramMap
     * @return
     * @throws AlipayApiException
     */
    public String ZFTCreate(Map<String,Object> paramMap) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        String bizContent = JsonUtil.toString(paramMap);
        log.info("创建直付通商户，请求参数：[{}]",bizContent);
        AntMerchantExpandIndirectZftCreateRequest request = new AntMerchantExpandIndirectZftCreateRequest();
        request.setBizContent(bizContent);
        AntMerchantExpandIndirectZftCreateResponse response = alipayClient.execute(request);
        log.info("创建直付通商户返回结果：{}",JsonUtil.toString(response));
        if (response.isSuccess()){
            return response.getOrderId();
        }else{
            log.error("创建直付通商户异常：{}",JsonUtil.toString(response));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户创建失败：" + response.getSubMsg());
        }
    }

    /**
     * 直付通图片上传接口
     * @param image
     * @return
     * @throws AlipayApiException
     */
    public String uploadImage(byte[] image,String fileName) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        AntMerchantExpandIndirectImageUploadRequest request = new AntMerchantExpandIndirectImageUploadRequest();
        FileItem fileItem = new FileItem(fileName,image);
        request.setImageContent(fileItem);
        request.setImageType(FilenameUtils.getExtension(fileName));
        AntMerchantExpandIndirectImageUploadResponse response = alipayClient.execute(request);
        log.info("支付宝上传图片返回结果:{}", JSONUtil.toJsonStr(response));
        if (response.isSuccess()){
            return response.getImageId();
        }else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付宝资质图片上传失败：" + response.getSubMsg());
        }
    }

    public String payApply(PayApplyDto payApplyDto) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        String biz_content = payApplyDto.toJsonString();

        AlipayTradeWapPayRequest alipayTradeWapPayRequest = new AlipayTradeWapPayRequest();
        alipayTradeWapPayRequest.setBizContent(biz_content);
        alipayTradeWapPayRequest.setNotifyUrl(alipayConfig.getNotifyUrl());
        alipayTradeWapPayRequest.setReturnUrl(payApplyDto.getReturnUrl());
        log.info("[直付通]-H5支付请求参数：{}",JsonUtil.toString(biz_content));
        AlipayTradeWapPayResponse alipayTradeWapPayResponse = alipayClient.pageExecute(alipayTradeWapPayRequest);
        log.info("[直付通]-H5支付响应参数：{}",JsonUtil.toString(alipayTradeWapPayResponse.getBody()));
        if (alipayTradeWapPayResponse.isSuccess()){
            return alipayTradeWapPayResponse.getBody();
        }else{
            log.error("[直付通]-H5支付请求异常：{}",JsonUtil.toString(alipayTradeWapPayResponse));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[直付通]-申请支付失败：" + alipayTradeWapPayResponse.getSubMsg());
        }
    }

    public String settleConfirm(SettleConfirmDto settleConfirmDto) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        String biz_content = settleConfirmDto.toJsonString();

        AlipayTradeSettleConfirmRequest  alipayRequest = new AlipayTradeSettleConfirmRequest();
        alipayRequest.setBizContent(biz_content);
        log.info("[直付通]-结算确认请求参数：{}",JsonUtil.toString(biz_content));
        AlipayTradeSettleConfirmResponse alipayResponse = alipayClient.execute(alipayRequest);
        log.info("[直付通]-结算确认响应参数：{}",JsonUtil.toString(alipayResponse.getBody()));
        if (alipayResponse.isSuccess()){
            return alipayResponse.getBody();
        }else{
            log.error("[直付通]结算确认请求异常：{}",JsonUtil.toString(alipayResponse));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[直付通]-结算确认失败：" + alipayResponse.getSubMsg());
        }
    }

    public String royalty(RoyaltyDto royaltyDto) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        String biz_content = royaltyDto.toJsonString();

        AlipayTradeOrderSettleRequest  alipayRequest = new AlipayTradeOrderSettleRequest();
        alipayRequest.setBizContent(biz_content);
        log.info("[直付通]-分账请求参数：{}",JsonUtil.toString(biz_content));
        AlipayTradeOrderSettleResponse alipayResponse = alipayClient.execute(alipayRequest);
        log.info("[直付通]-分账响应参数：{}",JsonUtil.toString(alipayResponse.getBody()));
        if (alipayResponse.isSuccess()){
            return alipayResponse.getBody();
        }else{
            log.error("[直付通]-分账请求异常：{}",JsonUtil.toString(alipayResponse));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[直付通]-分账失败：" + alipayResponse.getSubMsg());
        }
    }

    public String refund(RoyaltyDto royaltyDto) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        String biz_content = royaltyDto.toJsonString();

        AlipayTradeOrderSettleRequest  alipayRequest = new AlipayTradeOrderSettleRequest();
        alipayRequest.setBizContent(biz_content);
        log.info("[直付通]-分账请求参数：{}",JsonUtil.toString(biz_content));
        AlipayTradeOrderSettleResponse alipayResponse = alipayClient.execute(alipayRequest);
        log.info("[直付通]-分账响应参数：{}",JsonUtil.toString(alipayResponse.getBody()));
        if (alipayResponse.isSuccess()){
            return alipayResponse.getBody();
        }else{
            log.error("[直付通]-分账请求异常：{}",JsonUtil.toString(alipayResponse));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[直付通]-分账失败：" + alipayResponse.getSubMsg());
        }
    }

    public String payOrderQuery(String outTradeNo) throws AlipayApiException{
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", outTradeNo);
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        request.setBizContent(bizContent.toJSONString());
        AlipayTradeQueryResponse tradeQueryResponse = alipayClient.execute(request);
        if (tradeQueryResponse.isSuccess()) {
            return tradeQueryResponse.getBody();
        }else {
            log.error("查询订单请求请求异常：{}",JsonUtil.toString(tradeQueryResponse));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询订单请求请求失败：" + tradeQueryResponse.getSubMsg());
        }
    }

    public boolean verify(Map<String,String> params) {

        try {
            boolean checkResult = AlipaySignature.rsaCheckV1(params, alipayConfig.getAlipayPublicKey(), certAlipayRequest.getCharset(),certAlipayRequest.getSignType());
            return checkResult;
        } catch (AlipayApiException e) {
            log.error("回调验签失败",e);
            return false;
        }

    }
}
