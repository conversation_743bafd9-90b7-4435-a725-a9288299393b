package com.zhixianghui.service.pay.order.core.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.service.pay.order.core.mapper.PayApplyOrderMapper;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyOrder;
@Service
public class PayApplyOrderService extends ServiceImpl<PayApplyOrderMapper, PayApplyOrder> {

    public PayApplyOrder getByOrderNo(String payOrderNo) {
        PayApplyOrder applyOrder = this.getOne(new QueryWrapper<PayApplyOrder>().eq(PayApplyOrder.COL_PAY_ORDER_NO, payOrderNo));
        return applyOrder;
    }

}
