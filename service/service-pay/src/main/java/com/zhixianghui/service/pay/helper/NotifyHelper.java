package com.zhixianghui.service.pay.helper;

import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.pay.enums.PayNotifyEnum;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

@Service
public class NotifyHelper {
    @Reference
    private NotifyFacade notifyFacade;

    public boolean sendMessage(PayNotifyEnum payNotifyEnum,String content) {
        return this.sendMessage(payNotifyEnum, null, null, content);
    }

    public boolean sendMessage(PayNotifyEnum payNotifyEnum,String merchantNo,String trxNo,String content) {
        return notifyFacade.sendOne(payNotifyEnum.getTopicName(), merchantNo, trxNo, payNotifyEnum.getNotifyType().intValue(), payNotifyEnum.getTagExpress(), content);
    }

    public boolean sendMessage(PayNotifyEnum payNotifyEnum,String merchantNo,String trxNo,String content, MsgDelayLevelEnum delayLevel) {
        return notifyFacade.sendOne(payNotifyEnum.getTopicName(), merchantNo, trxNo, payNotifyEnum.getNotifyType().intValue(), payNotifyEnum.getTagExpress(), content, delayLevel == null ? null : delayLevel.getValue());
    }
}
