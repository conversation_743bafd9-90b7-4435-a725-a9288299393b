package com.zhixianghui.service.pay.order.core.biz;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.api.AlipayApiException;
import com.zhixianghui.common.statics.dto.fee.CalculateResultDto;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.merchant.service.MerchantEmployerCooperateFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.pay.order.constant.PayRedisKeys;
import com.zhixianghui.facade.pay.order.enums.PayOrderStatusEnum;
import com.zhixianghui.facade.pay.order.enums.PaySettleStatusEnum;
import com.zhixianghui.facade.pay.outlink.constant.AlipayConstant;
import com.zhixianghui.facade.pay.outlink.dto.*;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyOrder;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyRecord;
import com.zhixianghui.facade.pay.outlink.enums.PayStatusEnum;
import com.zhixianghui.facade.pay.outlink.enums.PayTypeEnum;
import com.zhixianghui.service.pay.enums.PayNotifyEnum;
import com.zhixianghui.service.pay.helper.NotifyHelper;
import com.zhixianghui.service.pay.order.core.service.PayApplyOrderService;
import com.zhixianghui.service.pay.order.core.service.PayApplyRecordService;
import com.zhixianghui.service.pay.outlink.core.request.alipay.PayRequestService;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class PayProcessBiz {
    @Autowired
    private PayRequestService payRequestService;
    @Autowired
    private PayRecordBiz payRecordBiz;
    @Autowired
    private NotifyHelper notifyHelper;
    @Resource
    private RedisClient redisClient;
    @Resource
    private TradeNotifyBiz tradeNotifyBiz;
    @Resource
    private PayApplyRecordService payApplyRecordService;
    @Resource
    private PayApplyOrderService payApplyOrderService;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private MerchantEmployerCooperateFacade cooperateFacade;
    @Reference
    private MerchantFacade merchantFacade;


    @Transactional(rollbackFor = Exception.class)
    public String pay(PayApplyParam payApplyParam) throws Exception {
        Merchant merchant = getMerchant(payApplyParam.getMerchantNo());
        MerchantEmployerCooperate employerCooperate = getMerchantEmployerCooperate(payApplyParam.getMerchantNo());

        String rediskey = PayRedisKeys.PAY_APPLY_RESULT + payApplyParam.getMerchantNo() + ":" + payApplyParam.getMerchantOrderNo();
        String applyCache = redisClient.get(rediskey);
        if (applyCache != null) {
            PayApplyRecord applyRecord = payRecordBiz.getByMchNoAndMchOrderNo(payApplyParam.getMerchantNo(), payApplyParam.getMerchantOrderNo());
            if (applyRecord != null && applyRecord.getPayStatus().intValue() == PayStatusEnum.NEW.getCode().intValue()) {
                applyRecord.setPayStatus(PayStatusEnum.PAYING.getCode());
                payRecordBiz.getPayApplyRecordService().updateById(applyRecord);
            }
            return applyCache;
        }

        CalculateResultDto calculateResultDto = payRecordBiz.calculateFee(payApplyParam.getMerchantOrderNo(), payApplyParam.getPayAmount(), payApplyParam.getMerchantNo());

        PayApplyOrder payApplyOrder = payRecordBiz.processPayOrder(payApplyParam, merchant, employerCooperate, calculateResultDto);

        PayApplyDto payApplyDto = payRecordBiz.processPayRecord(payApplyParam, merchant, employerCooperate, calculateResultDto,payApplyOrder);

        //3. 请求收款接口
        if (payApplyParam.getPayType() == PayTypeEnum.H5) {
            payApplyDto.setProductCode("QUICK_WAP_WAY");
        } else if (payApplyParam.getPayType() == PayTypeEnum.APP) {
            payApplyDto.setProductCode("QUICK_MSECURITY_PAY");
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持");
        }
        String applyResult = null;
        try {
            applyResult = payRequestService.payApply(payApplyDto);
        }catch (Exception e) {
            log.warn("[申请支付接口]-[{}]:通道请求异常-{}",payApplyDto.getPayTrxNo(),e.getMessage());
            throw ApiExceptions.API_TRADE_CHANNEL_ERROR.newWithErrMsg("通道请求异常");
        }

        PayApplyRecord applyRecord = payRecordBiz.getByMchNoAndMchOrderNo(payApplyDto.getMerchantNo(), payApplyDto.getMerchantOrderNo());
        applyRecord.setPayStatus(PayStatusEnum.PAYING.getCode());
        payRecordBiz.getPayApplyRecordService().updateById(applyRecord);

        redisClient.set(rediskey, applyResult, 900);

        //2. 发送反查消息
        notifyHelper.sendMessage(PayNotifyEnum.ALIPAY_REQUERY_MSG, payApplyParam.getMerchantNo(), payApplyDto.getPayTrxNo(), payApplyDto.getPayTrxNo());
        return applyResult;
    }


    public MerchantEmployerCooperate getMerchantEmployerCooperate(String merchantNo) {
        MerchantEmployerCooperate cooperate = cooperateFacade.getByMchNo(merchantNo);
        if (cooperate == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合作信息查询为空");
        }
        String smid = cooperate.getSmid();
        if (smid == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未找到匹配的二级商户号");
        }
        return cooperate;
    }

    public Merchant getMerchant(String mchNo) {
        Merchant merchant = merchantFacade.getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        return merchant;
    }



    public void handlePayResult(JSONObject backParamJson) {
        PayApplyRecord payRecord = payApplyRecordService.getByPaytrxNo(backParamJson.getStr("out_trade_no"));
        PayApplyOrder applyOrder = payApplyOrderService.getByOrderNo(payRecord.getPayOrderNo());
        switch (backParamJson.getStr("trade_status")) {
            /**
             * 状态说明
             * • WAIT_BUYER_PAY：交易创建，等待买家付款。
             * • TRADE_CLOSED：在指定时间段内未支付时关闭的交易或在交易完成全额退款成功时关闭的交易。
             * • TRADE_SUCCESS：商家签约的产品支持退款功能的前提下，买家付款成功。
             * • TRADE_FINISHED：商家签约的产品不支持退款功能的前提下，买家付款成功。或者，商家签约的产品支持退款功能的前提下，交易已经成功并且已经超过可退款期限。
             */
            case "TRADE_SUCCESS":{
                this.success(applyOrder,payRecord, backParamJson);
                break;
            }
            case "TRADE_CLOSED":{
                this.close(applyOrder,payRecord, backParamJson);
                break;
            }
            case "TRADE_FINISHED":{
                this.finish(applyOrder,payRecord, backParamJson);
                break;
            }
            default:{return;}
        }
        //通知商户
        tradeNotifyBiz.notifyMerchant(payRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleSettleApply(String payOrderNo) {

        log.info("[直付通结算]-订单结算开始,订单号-{}", payOrderNo);

        PayApplyOrder applyOrder = payApplyOrderService.getByOrderNo(payOrderNo);
        PayApplyRecord applyRecord = payApplyRecordService.getByPayOrderNo(payOrderNo);
        if (applyOrder == null || applyRecord == null) {
            log.error("[直付通结算]-找不到订单-{}", payOrderNo);
        }
        String settleTrxNo;
        if (applyRecord.getSettleTrxNo() == null) {
            settleTrxNo = sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.ZFT_SETTLE_TRX_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ZFT_SETTLE_TRX_NO_SEQ.getKey(), SequenceBizKeyEnum.ZFT_SETTLE_TRX_NO_SEQ.getWidth());
        }else {
            settleTrxNo = applyRecord.getSettleTrxNo();
        }

        applyRecord.setSettleTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
        applyRecord.setSettleTrxNo(settleTrxNo);
        applyRecord.setSettleAmount(applyOrder.getOrderAmount());
        applyRecord.setSettleStatus(PaySettleStatusEnum.SETTLING.getValue());
        payApplyRecordService.updateById(applyRecord);

        SettleConfirmDto settleConfirmDto = new SettleConfirmDto();
        settleConfirmDto.setOutRequestNo(settleTrxNo);
        settleConfirmDto.setTradeNo(applyRecord.getChannelTradeNo());
        settleConfirmDto.setSettleEntityId(applyRecord.getSubMerchantId());
        settleConfirmDto.setAmount(applyOrder.getOrderAmount().toPlainString());

        try {
            String settleConfirmResult = payRequestService.settleConfirm(settleConfirmDto);
            JSONObject settleRstJson = JSONUtil.parseObj(settleConfirmResult);
            if (settleRstJson.getInt("code").intValue() == 10000) {
//                PayApplyRecord record = payApplyRecordService.getByPayOrderNo(payOrderNo);
//                record.setSettleStatus(PaySettleStatusEnum.SUCCESS.getValue());
//                payApplyRecordService.updateById(record);

                tradeNotifyBiz.royaltyNotify(applyOrder.getMerchantNo(),applyOrder.getPayOrderNo());
            }else {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("结算异常");
            }
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public void handleRoyalty(String payOrderNo) {
        log.info("[直付通结算]-订单结算开始,订单号-{}", payOrderNo);

        PayApplyOrder applyOrder = payApplyOrderService.getByOrderNo(payOrderNo);
        PayApplyRecord applyRecord = payApplyRecordService.getByPayOrderNo(payOrderNo);
        if (applyOrder == null || applyRecord == null) {
            log.error("[直付通结算]-找不到订单-{}", payOrderNo);
        }
        String settleTrxNo = applyRecord.getSettleTrxNo();

        List<OpenApiRoyaltyDetailInfoPojo> parameters = new ArrayList<>();
        OpenApiRoyaltyDetailInfoPojo royaltyDetailInfoPojo = new OpenApiRoyaltyDetailInfoPojo();
        royaltyDetailInfoPojo.setAmount(applyRecord.getFeeAmount().toPlainString());
        royaltyDetailInfoPojo.setDesc("服务费");
        royaltyDetailInfoPojo.setTransIn(AlipayConstant.PLAT_ALIPAY_USER_ID);
        royaltyDetailInfoPojo.setTransInType("userId");

        parameters.add(royaltyDetailInfoPojo);
        RoyaltyDto royaltyDto = new RoyaltyDto();
        royaltyDto.setOutRequestNo(settleTrxNo);
        royaltyDto.setTradeNo(applyRecord.getChannelTradeNo());
        royaltyDto.setRoyaltyParameters(parameters);

        try {
            String royalty = payRequestService.royalty(royaltyDto);
            JSONObject parseObj = JSONUtil.parseObj(royalty);
            if (parseObj.getInt("code").intValue() == 10000) {

            }else {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("分账异常");
            }
        }catch (Exception e) {

        }

    }
    public void success(PayApplyOrder applyOrder,PayApplyRecord payRecord,JSONObject backParamJson) {
        LocalDateTime gmtPayment = DateUtil.parseLocalDateTime(backParamJson.getStr("gmt_payment"), "yyyy-MM-dd HH:mm:ss");
        payRecord.setPayTime(gmtPayment);
        payRecord.setChannelTradeNo(backParamJson.getStr("trade_no"));
        payRecord.setPayerAccount(backParamJson.getStr("buyer_id"));
        payRecord.setPayerLoginAccount("buyer_logon_id");
        payRecord.setPayeeAccount(backParamJson.getStr("seller_id"));

        payRecord.setPayStatus(PayStatusEnum.PAY_SUCCESS.getCode());
        payApplyRecordService.updateById(payRecord);

        applyOrder.setPayTime(gmtPayment);
        applyOrder.setPayerAccount(backParamJson.getStr("buyer_id"));
        applyOrder.setPayeeAccount(backParamJson.getStr("seller_id"));
        applyOrder.setPayStatus(PayOrderStatusEnum.SUCCESS.getValue());
        payApplyOrderService.updateById(applyOrder);

        //发送结算通知
        try {
            tradeNotifyBiz.settleNotify(applyOrder.getMerchantNo(), applyOrder.getPayOrderNo());
        } catch (Exception e) {
            log.info("[直付通结算]-结算通知发送失败，订单号-[{}]",applyOrder.getPayOrderNo());
        }
    }


    public void close(PayApplyOrder applyOrder,PayApplyRecord payRecord,JSONObject backParamJson) {
        payRecord.setPayCompleteTime(DateUtil.parseLocalDateTime(backParamJson.getStr("gmt_close"),"yyyy-MM-dd HH:mm:ss"));
        payRecord.setPayStatus(PayStatusEnum.PAY_CLOSE.getCode());
        payApplyRecordService.updateById(payRecord);
        
        applyOrder.setPayStatus(PayOrderStatusEnum.CLOSED.getValue());
        payApplyOrderService.updateById(applyOrder);
    }

    public void finish(PayApplyOrder applyOrder,PayApplyRecord payRecord,JSONObject backParamJson) {

        payRecord.setPayStatus(PayStatusEnum.PAY_FINISH.getCode());
        payApplyRecordService.updateById(payRecord);
        
        applyOrder.setPayStatus(PayOrderStatusEnum.FINISH.getValue());
        payApplyOrderService.updateById(applyOrder);
    }
}
