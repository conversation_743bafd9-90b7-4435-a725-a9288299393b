package com.zhixianghui.service.pay.order.core.biz;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.api.base.utils.SignUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.ApiRespCodeEnum;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.notify.NotifyContent;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyOrder;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyRecord;
import com.zhixianghui.facade.pay.outlink.vo.PayMerchantNotifyVo;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.service.pay.enums.PayNotifyEnum;
import com.zhixianghui.service.pay.helper.NotifyHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TradeNotifyBiz {

    @Reference
    private NotifyFacade notifyFacade;
    @Resource
    private NotifyHelper notifyHelper;
    @Reference
    private MerchantSecretFacade merchantSecretFacade;

    public void notifyMerchant(PayApplyRecord payApplyRecord) {
        MerchantNotifyParam merchantNotifyParam = this.fillPayResultNotifyParam(payApplyRecord);
        this.notifyMerchant(merchantNotifyParam, payApplyRecord.getPayTrxNo());
    }

    public void settleNotify(String merchantNo,String payOrderNo) {
        notifyHelper.sendMessage(PayNotifyEnum.ALIPAY_SETTLE_NOTIFY, merchantNo, payOrderNo, payOrderNo, MsgDelayLevelEnum.M_1);
    }

    public void royaltyNotify(String merchantNo,String payOrderNo) {
        notifyHelper.sendMessage(PayNotifyEnum.ALIPAY_ROYALTY_NOTIFY, merchantNo, payOrderNo, payOrderNo, MsgDelayLevelEnum.S_10);
    }

    /**
     * 通知商户
     * @param merchantNotifyParam 通知商户的信息参数实体
     */
    public void notifyMerchant(MerchantNotifyParam merchantNotifyParam,String payTrxNo){
        //一个批次只发一次 用上商户号和批次号唯一 手动补发不持久化
        notifyFacade.sendOne(MessageMsgDest.TOPIC_NOTIFY_MERCHANT,
                merchantNotifyParam.getNotifyContent().getMchNo(),
                payTrxNo, NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_NOTIFY_MERCHANT,
                JsonUtil.toString(merchantNotifyParam));
    }

    public MerchantNotifyParam fillPayResultNotifyParam(PayApplyRecord applyRecord) {
        String secKey = UUIDUitl.generateString(16);
        PayMerchantNotifyVo notifyMchResVo = new PayMerchantNotifyVo();
        BeanUtil.copyProperties(applyRecord,notifyMchResVo);

        return fillNotifyParam(applyRecord.getMerchantNo(), applyRecord.getNotifyUrl(), SignTypeEnum.RSA.getValue()+"",secKey,JsonUtil.toString(notifyMchResVo));
    }

    public MerchantNotifyParam fillNotifyParam(String employerNo, String notifyUrl, String signType,String secKey,String data) {
        MerchantNotifyParam merchantNotifyParam = new MerchantNotifyParam();
        merchantNotifyParam.setNotifyContent(new NotifyContent());
        merchantNotifyParam.getNotifyContent().setRespCode(ApiRespCodeEnum.SUCCESS.getCode());
        merchantNotifyParam.getNotifyContent().setData(data);
        merchantNotifyParam.getNotifyContent().setRandStr(UUIDUitl.generateString(32));
        merchantNotifyParam.getNotifyContent().setSignType(signType);
        merchantNotifyParam.getNotifyContent().setMchNo(employerNo);
        MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(employerNo);
        String priKey = merchantSecret.getPlatformPrivateKey();
        String sign = SignUtil.sign(merchantNotifyParam.getNotifyContent(), Integer.parseInt(merchantNotifyParam.getNotifyContent().getSignType()),priKey);
        merchantNotifyParam.getNotifyContent().setSign(sign);
        merchantNotifyParam.getNotifyContent().setSecKey(RSAUtil.encryptByPublicKey(secKey,merchantSecret.getMerchantPublicKey()));
        merchantNotifyParam.setNotifyUrl(notifyUrl);
        return merchantNotifyParam;
    }
}
