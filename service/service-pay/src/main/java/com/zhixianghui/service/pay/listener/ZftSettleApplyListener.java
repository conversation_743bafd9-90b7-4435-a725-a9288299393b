package com.zhixianghui.service.pay.listener;

import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.pay.order.core.biz.PayProcessBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ZFT_SETTLE_NOTIFY,selectorExpression = MessageMsgDest.TAG_ZFT_SETTLE_NOTIFY,consumeThreadMax = 5,consumerGroup = "zftSettleApplyConsumerGroup")
public class ZftSettleApplyListener extends BaseRocketMQListener<String> {
    @Autowired
    private PayProcessBiz payProcessBiz;
    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String payOrderNo) {
        payProcessBiz.handleSettleApply(payOrderNo);
    }
}
