package com.zhixianghui.service.pay.outlink.core.biz;

import cn.hutool.core.io.IoUtil;
import com.alipay.api.AlipayApiException;
import com.zhixianghui.common.statics.enums.alipay.AlipaySiteTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.enums.AlipaySettleRule;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.enums.TaskHandleStatusEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.TaskHandleVo;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.service.pay.outlink.core.request.alipay.PayRequestService;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName ZFTChannelAuditBiz
 * @Description TODO
 * @Date 2023/5/10 11:45
 */
@Slf4j
@Service
public class ZFTChannelAuditBiz {

    @Reference
    private MerchantEmployerCooperateFacade merchantEmployerCooperateFacade;

    @Reference
    private MerchantFileFacade merchantFileFacade;

    @Reference
    private MerchantFacade merchantFacade;

    @Autowired
    private FastdfsClient fastdfsClient;

    @Autowired
    private PayRequestService payRequestService;

    @Reference
    private MerchantEmployerMainFacade merchantEmployerMainFacade;

    @Reference
    private MerchantBankAccountFacade merchantBankAccountFacade;

    @Reference
    private FlowFacade flowFacade;

    public void create(Map<String, Object> infoMap) {
        String mchNo = (String) infoMap.get("mchNo");
        String taskId = (String) infoMap.get("taskId");
        Integer commonFlowId = (Integer) infoMap.get("commonFlowId");
        try {
            Map<String,Object> reqParam = handleReqParam(mchNo);
            String orderId = "";
            try {
                orderId = payRequestService.ZFTCreate(reqParam);
            }catch (AlipayApiException e){
                log.error("直付通商户创建失败",e);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付宝直付通商户接口调用失败：" + e.getErrMsg());
            }
            log.info("直付通创建接口调用成功，流程任务id:[{}]，商户编号：[{}],创建orderId：[{}]：",taskId,mchNo,orderId);
        }catch (BizException e){
            TaskHandleVo taskHandleVo = new TaskHandleVo();
            taskHandleVo.setCommonFlowId(commonFlowId.longValue());
            taskHandleVo.setTaskId(taskId);
            taskHandleVo.setOpinion(e.getErrMsg());
            taskHandleVo.setStatus(TaskHandleStatusEnum.DISAGREE.getValue());
            FlowUserVo flowUserVo = new FlowUserVo();
            flowUserVo.setUserName(FlowConstant.EXTERNAL_NAME);
            flowUserVo.setExternalId(FlowConstant.EXTERNAL_ID);
            flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
            flowFacade.executeTask(taskHandleVo,flowUserVo,false);
        }
    }

    /**
     * 处理参数
     * @return
     */
    private Map<String, Object> handleReqParam(String mchNo) {
        //获取营业执照
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",mchNo);
        paramMap.put("fileType", MerchantFileTypeEnum.BUSINESS_LICENSE.getValue());
        List<MerchantFile> file = merchantFileFacade.listBy(paramMap);
        if (file == null || file.size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("营业执照不存在");
        }
        //上传营业执照
        String businessLicense = uploadImage(file.get(0).getFileUrl());
        //获取特殊行业资质文件
        MerchantEmployerCooperate payInfo = merchantEmployerCooperateFacade.getByMchNo(mchNo);
        if (payInfo == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户直付信息为空");
        }

        List<String> qualificationFile = new ArrayList<>();
        if (StringUtils.isNotBlank(payInfo.getQualificationCode())){
            String[] fileUrls = payInfo.getQualificationUrl().split(",");
            for (int i = 0; i < fileUrls.length; i++) {
                qualificationFile.add(uploadImage(fileUrls[i]));
            }
        }

        //获取法人照片
        Merchant merchant = merchantFacade.getByMchNo(payInfo.getMchNo());
        MerchantEmployerMain main = merchantEmployerMainFacade.getByMchNo(payInfo.getMchNo());
        MerchantBankAccount account = merchantBankAccountFacade.getByMchNo(payInfo.getMchNo());

        Map<String,Object> map = new HashMap<>();
        map.put("external_id",payInfo.getMchNo());
        map.put("merchant_type","01");
        map.put("name",merchant.getMchName());
        map.put("alias_name",merchant.getMchName());
        map.put("mcc",payInfo.getProfessionType());
        map.put("cert_no",main.getTaxNo());
        map.put("cert_type","201");
        map.put("cert_image",businessLicense);
        map.put("legal_name",main.getLegalPersonName());
        map.put("legal_cert_no",main.getCertificateNumber());
        map.put("legal_cert_front_image","");
        map.put("legal_cert_back_image","");
        map.put("alipay_logon_id",account.getAlipayAccountNo());
        map.put("binding_alipay_logon_id",account.getAlipayAccountNo());
        //联系人
        Map<String,Object> contactInfo = new HashMap<>();
        contactInfo.put("name",merchant.getContactName());
        contactInfo.put("mobile",merchant.getContactPhone());
        map.put("contact_infos",new ArrayList<Map<String,Object>>(){{add(contactInfo);}});
        //特殊行业资质图片
        if (StringUtils.isNotBlank(payInfo.getQualificationCode())){
            List<Map<String,Object>> qualificationList = new ArrayList<>();
            for (String fileId : qualificationFile) {
                Map<String,Object> fileMap = new HashMap<>();
                fileMap.put("industry_qualification_type",payInfo.getQualificationCode());
                fileMap.put("industry_qualification_image",fileId);
                qualificationList.add(fileMap);
            }
            map.put("qualifications",qualificationList);
        }
        //结算规则
        Map<String,Object> settleRule = new HashMap<>();
        settleRule.put("default_settle_type", AlipaySettleRule.ALIPAY_ACCOUNT.getValue());
        settleRule.put("default_settle_target",account.getAlipayAccountNo());
        map.put("default_settle_rule",settleRule);
        //服务
        String[] services = payInfo.getServiceType().split(",");
        String[] servicesName = new String[services.length];
        List<Map<String,Object>> serviceMap = new ArrayList<>();
        for (int i = 0; i < services.length; i++) {
            Map<String,Object> service = new HashMap<>();
            AlipaySiteTypeEnum alipaySiteTypeEnum = AlipaySiteTypeEnum.getEnum(services[i]);
            //服务名
            servicesName[i] = alipaySiteTypeEnum.getDesc();
            service.put("site_type",alipaySiteTypeEnum.getSiteType());
            switch (alipaySiteTypeEnum){
                case MINI_APPS:
                    service.put("site_name",payInfo.getAlipayAppName());
                    break;
                case APP:
                    service.put("site_name",payInfo.getAppName());
                    break;
                case WAP_PAY:
                    service.put("site_name",payInfo.getWapName());
                    service.put("site_url",payInfo.getWapSite());
                    break;
                case PC_PAY:
                    service.put("site_name",payInfo.getPcName());
                    service.put("site_url",payInfo.getPcSite());
                    break;
            }
            serviceMap.add(service);
        }
        map.put("service",servicesName);
        map.put("sites",serviceMap);
        return map;
    }

    //文件类型
    private static List<String> FILE_EXT = Arrays.asList(new String[]{"bmp","jpg","png","jpeg","gif"});
    //图片大小限制
    private static final long LIMIT_SIZE = 10 * 1048576L;

    /**
     * 上传图片
     * @param fileUrl
     * @return
     */
    private String uploadImage(String fileUrl){
        String ext = FilenameUtils.getExtension(fileUrl);
        if (!FILE_EXT.contains(ext)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("营业执照、资质图片仅支持bmp,jpg,png,jpeg,gif格式");
        }
        InputStream inputStream = fastdfsClient.downloadFile(fileUrl);
        byte[] readBytes = IoUtil.readBytes(inputStream);
        if (readBytes.length > LIMIT_SIZE){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("营业执照、资质图片最大为10M");
        }

        try {
            return payRequestService.uploadImage(readBytes,FilenameUtils.getName(fileUrl));
        }catch (AlipayApiException e){
            log.error("直付通图片上传失败",e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("营业执照、资质图片上传失败：" + e.getErrMsg());
        }
    }
}
