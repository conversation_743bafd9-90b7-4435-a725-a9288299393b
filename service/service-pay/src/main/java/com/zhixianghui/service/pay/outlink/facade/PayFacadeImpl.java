package com.zhixianghui.service.pay.outlink.facade;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyRecord;
import com.zhixianghui.facade.pay.outlink.service.PayFacade;
import com.zhixianghui.service.pay.order.core.service.PayApplyRecordService;
import com.zhixianghui.service.pay.outlink.core.request.alipay.PayRequestService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Service
public class PayFacadeImpl implements PayFacade {
    @Autowired
    private PayRequestService payRequestService;
    @Autowired
    private PayApplyRecordService payApplyRecordService;

    @Override
    public boolean verifyNotify(Map<String, String> params) {
        return payRequestService.verify(params);
    }

    @Override
    public PayApplyRecord getByPaytrxNo(String trxNo) {
        PayApplyRecord applyRecord = payApplyRecordService.getByPaytrxNo(trxNo);
        return applyRecord;
    }

}
