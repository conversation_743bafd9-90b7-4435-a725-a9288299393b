package com.zhixianghui.service.pay.order.core.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.dto.fee.CalculateResultDto;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.fee.ProductFeeSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.fee.service.MerchantFeeCalculateFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.pay.order.enums.PayOrderStatusEnum;
import com.zhixianghui.facade.pay.order.enums.PayRefundStatusEnum;
import com.zhixianghui.facade.pay.order.enums.PaySettleStatusEnum;
import com.zhixianghui.facade.pay.outlink.constant.AlipayConstant;
import com.zhixianghui.facade.pay.outlink.dto.PayApplyDto;
import com.zhixianghui.facade.pay.outlink.dto.PayApplyParam;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyOrder;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyRecord;
import com.zhixianghui.facade.pay.outlink.enums.PayStatusEnum;
import com.zhixianghui.service.pay.order.core.service.PayApplyOrderService;
import com.zhixianghui.service.pay.order.core.service.PayApplyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
@Slf4j
public class PayRecordBiz {

    @Resource
    private PayApplyRecordService payApplyRecordService;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private MerchantFeeCalculateFacade productFeeCalculateFacade;
    @Resource
    private PayApplyOrderService payApplyOrderService;

    public PayApplyOrder processPayOrder(PayApplyParam payApplyParam,
                                Merchant merchant,
                                MerchantEmployerCooperate employerCooperate,
                                CalculateResultDto calculateResultDto
    ) {
        int count = payApplyOrderService.count(
                new QueryWrapper<PayApplyOrder>()
                        .eq("merchant_order_no", payApplyParam.getMerchantOrderNo())
                        .eq("merchant_no", payApplyParam.getMerchantNo())
        );
        if (count > 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg(StrUtil.format("订单号[{}]重复", payApplyParam.getMerchantOrderNo()));
        }

        String payOrderNo = this.getPayOrderNo();
        PayApplyOrder payOrder = new PayApplyOrder();
        BeanUtil.copyProperties(payApplyParam, payOrder);
        payOrder.setPayOrderNo(payOrderNo);
        payOrder.setOrderAmount(new BigDecimal(payApplyParam.getPayAmount()));
        payOrder.setSubMerchantId(employerCooperate.getSmid());
        payOrder.setMerchantName(merchant.getMchName());
        payOrder.setPayType(payApplyParam.getPayType().name());
        payOrder.setPayStatus(PayOrderStatusEnum.NEW.getValue());
        payOrder.setSettleStatus(PaySettleStatusEnum.UN_SETTLE.getValue());
        payOrder.setRefundStatus(PayRefundStatusEnum.NO_REFUND.getValue());
        payOrder.setSubject(payApplyParam.getPaySubject());
        BigDecimal orderFee = calculateResultDto.getOrderFee();
        payOrder.setFeeAmount(orderFee);
        payOrder.setMchAmount(payOrder.getOrderAmount().subtract(orderFee));

        payApplyOrderService.save(payOrder);
        return payOrder;
    }

    public PayApplyDto processPayRecord(PayApplyParam payApplyParam,
                                        Merchant merchant,
                                        MerchantEmployerCooperate employerCooperate,
                                        CalculateResultDto calculateResultDto,
                                        PayApplyOrder payApplyOrder
    ) {
        String payTrxNo = getPayTrxNo();
        PayApplyDto payApplyDto = BeanUtil.copyProperties(payApplyParam, PayApplyDto.class);
        payApplyDto.setPayTrxNo(payTrxNo);
        payApplyDto.setSubMerchantId(employerCooperate.getSmid());
        payApplyDto.setMerchantName(merchant.getMchName());
        payApplyDto.setPayType(payApplyParam.getPayType().name());
        BigDecimal orderFee = calculateResultDto.getOrderFee();
        payApplyDto.setFeeAmount(orderFee);
        payApplyDto.setMchAmount(new BigDecimal(payApplyDto.getPayAmount()).subtract(orderFee));
        payApplyDto.setSettleEntityId(employerCooperate.getSmid());
        payApplyDto.setSysServiceProviderId(AlipayConstant.PLAT_ALIPAY_USER_ID);
        //1. 保存到支付记录表
        this.savePayrecordItem(payApplyDto,payApplyOrder.getPayOrderNo());
        return payApplyDto;
    }

    public void savePayrecordItem(PayApplyDto payApplyDto,String payOrderNo) {
        int count = payApplyRecordService.count(
                new QueryWrapper<PayApplyRecord>()
                        .eq("merchant_order_no", payApplyDto.getMerchantOrderNo())
                        .eq("merchant_no", payApplyDto.getMerchantNo())
        );
        if (count > 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg(StrUtil.format("订单号[{}]重复", payApplyDto.getMerchantOrderNo()));
        }

        PayApplyRecord applyRecord = BeanUtil.copyProperties(payApplyDto, PayApplyRecord.class);
        applyRecord.setSettleAmount(new BigDecimal(payApplyDto.getPayAmount()));
        applyRecord.setOrigTotalAmount(new BigDecimal(payApplyDto.getPayAmount()));
        applyRecord.setPayStatus(PayStatusEnum.NEW.getCode());
        applyRecord.setSettleStatus(PaySettleStatusEnum.UN_SETTLE.getValue());
        applyRecord.setRefundStatus(PayRefundStatusEnum.NO_REFUND.getValue());
        applyRecord.setSubject(payApplyDto.getPaySubject());
        applyRecord.setMerchantName(payApplyDto.getMerchantName());
        applyRecord.setPayOrderNo(payOrderNo);
        payApplyRecordService.save(applyRecord);
    }

    public PayApplyRecordService getPayApplyRecordService() {
        return this.payApplyRecordService;
    }

    public PayApplyRecord getByMchNoAndMchOrderNo(String mchNo, String mchOrderNo) {
        PayApplyRecord applyRecord = payApplyRecordService.getOne(new QueryWrapper<PayApplyRecord>()
                .eq("merchant_order_no", mchOrderNo)
                .eq("merchant_no", mchNo));
        return applyRecord;
    }

    public CalculateResultDto calculateFee(String merchantOrderNo,String payAmount,String merchantNo) {
        log.info("[直付通收款: {}]==>订单开始计费", merchantOrderNo);
        //实发金额
        SpecialRuleParamDto param = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),payAmount);
        List<SpecialRuleParamDto> ruleReqList = Lists.newArrayList(param);
        CalculateResultDto calculateResultDto = productFeeCalculateFacade.calculateFee(merchantNo, ProductNoEnum.ZFT.getValue(), new BigDecimal(payAmount), ruleReqList, merchantOrderNo);
        log.info("[直付通收款: {}]==>订单计费结束,计费结果:{}",  merchantOrderNo, JSON.toJSONString(calculateResultDto));
        return calculateResultDto;
    }

    private String getPayTrxNo() {
        String payTrxNo = sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.ZFT_PAY_TRX_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ZFT_PAY_TRX_NO_SEQ.getKey(), SequenceBizKeyEnum.ZFT_PAY_TRX_NO_SEQ.getWidth());
        return payTrxNo;
    }

    private String getPayOrderNo() {
        String payTrxNo = sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.ZFT_PAY_ORDER_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ZFT_PAY_ORDER_NO_SEQ.getKey(), SequenceBizKeyEnum.ZFT_PAY_ORDER_NO_SEQ.getWidth());
        return payTrxNo;
    }
}
