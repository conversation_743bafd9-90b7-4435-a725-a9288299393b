package com.zhixianghui.service.pay.listener;

import cn.hutool.core.lang.TypeReference;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.service.pay.outlink.core.biz.ZFTChannelAuditBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowChannelAuditListener
 * @Description TODO
 * @Date 2023/5/10 11:41
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ZFT_CHANNEL_AUDIT,selectorExpression = MessageMsgDest.TAG_ZFT_CHANNEL_AUDIT,consumeThreadMax = 5,consumerGroup = "channelAuditConsumerGroup")
public class FlowChannelAuditListener extends BaseRocketMQListener<String> {

    @Autowired
    private ZFTChannelAuditBiz zftChannelAuditBiz;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        Map<String,Object> infoMap = JsonUtil.toBean(jsonParam, new TypeReference<Map<String,Object>>(){});
        zftChannelAuditBiz.create(infoMap);
    }
}
