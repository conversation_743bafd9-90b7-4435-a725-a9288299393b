package com.zhixianghui.service.pay.order.controllers;

import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.pay.outlink.dto.PayApplyParam;
import com.zhixianghui.service.pay.order.core.biz.PayProcessBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("ZFT")
@Slf4j
public class ZFTPayController {

    @Autowired
    private PayProcessBiz zFTPayProcessBiz;

    /**
     * 提交支付订单接口
     *
     * @param requestDto
     * @return
     * @throws Exception
     */
    @PostMapping("payApply")
    public ResponseDto<String> payApply(@Valid @RequestBody RequestDto<PayApplyParam> requestDto) throws Exception {
        String mchNo = requestDto.getMchNo();
        PayApplyParam payApplyParam = requestDto.getData();

        String logFlag = String.join("-", mchNo, payApplyParam.getMerchantOrderNo());
        log.info("[{}]==>支付申请开始：{}", logFlag, JsonUtil.toString(requestDto));
        return ResponseDto.success(zFTPayProcessBiz.pay(payApplyParam), null);
    }

    @PostMapping("payApplyTest")
    public ResponseDto<String> payApplyTest(@Valid @RequestBody PayApplyParam payApplyParam) throws Exception {
        return ResponseDto.success(zFTPayProcessBiz.pay(payApplyParam), null);
    }

    @PostMapping("refund")
    public ResponseDto<String> refund() {

        return ResponseDto.success(null, null);
    }
}
