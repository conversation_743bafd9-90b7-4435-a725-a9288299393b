package com.zhixianghui.service.pay;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 交易中心服务
 */
@EnableCaching
@EnableDiscoveryClient
@SpringBootApplication
public class ServicePayApp {
    public static void main(String[] args) {
        System.setProperty("druid.mysql.usePingMethod","false");
        new SpringApplicationBuilder(ServicePayApp.class).run(args);
    }
}
