spring.application.name=service-pay
spring.cloud.nacos.config.server-addr=@nacosAddr@
spring.cloud.nacos.config.namespace=@nacosNamespace@
spring.cloud.nacos.config.file-extension=properties

spring.cloud.nacos.discovery.namespace=@nacosNamespace@


spring.cloud.nacos.config.shared-dataids=rocketmq.properties,dubbo.properties,redis.properties,db.properties,fastdfs.properties

logging.level.com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder=info
logging.level.com.zhixianghui.service.employee.mapper=debug