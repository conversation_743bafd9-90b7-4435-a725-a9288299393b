<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.pay.order.core.mapper.PayApplyRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.pay.outlink.entity.PayApplyRecord">
    <!--@mbg.generated-->
    <!--@Table tbl_pay_apply_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pay_trx_no" jdbcType="VARCHAR" property="payTrxNo" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="subject" jdbcType="VARCHAR" property="subject" />
    <result column="merchant_order_no" jdbcType="VARCHAR" property="merchantOrderNo" />
    <result column="sub_merchant_id" jdbcType="VARCHAR" property="subMerchantId" />
    <result column="merchant_no" jdbcType="VARCHAR" property="merchantNo" />
    <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
    <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_complete_time" jdbcType="TIMESTAMP" property="payCompleteTime" />
    <result column="orig_total_amount" jdbcType="DECIMAL" property="origTotalAmount" />
    <result column="passback_params" jdbcType="VARCHAR" property="passbackParams" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount" />
    <result column="mch_amount" jdbcType="DECIMAL" property="mchAmount" />
    <result column="channel_trade_no" jdbcType="VARCHAR" property="channelTradeNo" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="payer_account" jdbcType="VARCHAR" property="payerAccount" />
    <result column="payee_account" jdbcType="VARCHAR" property="payeeAccount" />
    <result column="payer_login_account" jdbcType="VARCHAR" property="payerLoginAccount" />
    <result column="notify_url" jdbcType="VARCHAR" property="notifyUrl" />
    <result column="pay_type" jdbcType="VARCHAR" property="payType" />
    <result column="refund_status" jdbcType="VARCHAR" property="refundStatus" />
    <result column="settle_status" jdbcType="VARCHAR" property="settleStatus" />
    <result column="settle_trx_no" jdbcType="VARCHAR" property="settleTrxNo" />
    <result column="settle_time" jdbcType="VARCHAR" property="settleTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, pay_trx_no, pay_amount, subject, merchant_order_no, goods_detail, sub_merchant_id,
    merchant_no, merchant_name, settle_amount, create_time, update_time, pay_complete_time,
    orig_total_amount, passback_params, pay_status, fee_amount, mch_amount, channel_trade_no,
    pay_time, payer_account, payee_account, payer_login_account, notify_url,pay_type,refund_status,
    settle_status,settle_trx_no,settle_time
  </sql>
</mapper>