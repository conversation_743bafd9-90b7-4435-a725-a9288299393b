spring.application.name=service-merchant
spring.cloud.nacos.config.server-addr=@nacosAddr@
spring.cloud.nacos.config.namespace=@nacosNamespace@
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.discovery.namespace=@nacosNamespace@

spring.cloud.nacos.config.shared-dataids=dubbo.properties,db.properties,rocketmq.properties,redis.properties,fastdfs.properties


logging.level.com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder=warn

logging.level.root=warn
