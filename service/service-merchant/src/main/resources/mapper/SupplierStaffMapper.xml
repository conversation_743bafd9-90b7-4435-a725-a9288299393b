<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaff">
	<sql id="table"> tbl_supplier_staff </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaff">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="operator_id" property="operatorId" jdbcType="BIGINT"/>
		<result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
		<result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="SMALLINT"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="updator" property="updator" jdbcType="VARCHAR"/>
		<result column="extra_info" property="extraInfo" jdbcType="OTHER"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		update_time,
		operator_id,
		mch_no,
		mch_name,
		type,
		creator,
		updator,
		extra_info,
		name
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaff">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	operator_id ,
        	mch_no ,
			mch_name ,
        	type ,
        	creator ,
        	updator ,
        	extra_info,
        	name
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{operatorId,jdbcType=BIGINT},
			#{mchNo,jdbcType=VARCHAR},
			#{mchName,jdbcType=VARCHAR},
			#{type,jdbcType=SMALLINT},
			#{creator,jdbcType=VARCHAR},
			#{updator,jdbcType=VARCHAR},
			#{extraInfo,jdbcType=OTHER},
			#{name,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	operator_id ,
        	mch_no ,
			mch_name ,
        	type ,
        	creator ,
        	updator ,
        	extra_info,name
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.operatorId,jdbcType=BIGINT},
			#{item.mchNo,jdbcType=VARCHAR},
			#{item.mchName,jdbcType=VARCHAR},
			#{item.type,jdbcType=SMALLINT},
			#{item.creator,jdbcType=VARCHAR},
			#{item.updator,jdbcType=VARCHAR},
			#{item.extraInfo,jdbcType=OTHER},
			#{item.name,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaff">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			operator_id = #{operatorId,jdbcType=BIGINT},
			mch_no = #{mchNo,jdbcType=VARCHAR},
			mch_name = #{mchName,jdbcType=VARCHAR},
			type = #{type,jdbcType=SMALLINT},
			creator = #{creator,jdbcType=VARCHAR},
			updator = #{updator,jdbcType=VARCHAR},
			extra_info = #{extraInfo,jdbcType=OTHER},
			name = #{name,jdbcType=VARCHAR}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaff">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="operatorId != null">
				operator_id =#{operatorId,jdbcType=BIGINT},
			</if>
			<if test="mchNo != null">
				mch_no =#{mchNo,jdbcType=VARCHAR},
			</if>
			<if test="mchName != null">
				mch_name =#{mchName,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type =#{type,jdbcType=SMALLINT},
			</if>
			<if test="creator != null">
				creator =#{creator,jdbcType=VARCHAR},
			</if>
			<if test="updator != null">
				updator =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="extraInfo != null">
				extra_info =#{extraInfo,jdbcType=OTHER},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		tbl_supplier_staff t1
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE id = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and t1.id = #{id,jdbcType=BIGINT}
		</if>
		<if test="creator != null">
			and t1.creator = #{creator,jdbcType=VARCHAR}
		</if>
		<if test="updator != null">
			and t1.updator = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="createTimeBegin != null">
			and t1.create_time <![CDATA[ >= ]]> #{createTimeBegin,jdbcType=TIMESTAMP}
		</if>
		<if test="createTimeEnd != null">
			and t1.create_time <![CDATA[ <= ]]> #{createTimeEnd,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and t1.update_time = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="operatorId != null">
			and t1.operator_id = #{operatorId,jdbcType=BIGINT}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and t1.mch_no = #{mchNo,jdbcType=VARCHAR}
		</if>

		<if test="mchNameLike != null and mchNameLike !=''">
			and t1.mch_name like concat('%',#{mchNameLike,jdbcType=VARCHAR},'%')
		</if>

		<if test="mchName != null and mchName !='' ">
			and t1.mch_name = #{mchName,jdbcType=VARCHAR}
		</if>
		<if test="type != null">
			and t1.type = #{type,jdbcType=SMALLINT}
		</if>

		<if test="phone != null  and phone !='' ">
			and t2.phone = #{phone,jdbcType=VARCHAR}
		</if>

		<if test="nameLike != null and nameLike !=''">
			and t1.name like concat('%',#{nameLike,jdbcType=VARCHAR},'%')
		</if>

		<if test="name != null and name !=''">
			and t1.name = #{name,jdbcType=VARCHAR}
		</if>
	</sql>

	<!--更新-->
	<update id="updateTypeById" parameterType="java.util.Map">
		UPDATE <include refid="table" /> SET
			VERSION = VERSION + 1,
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			type = #{type,jdbcType=SMALLINT},
			updator = #{updator,jdbcType=VARCHAR}
		WHERE id = #{id,jdbcType=BIGINT}
	</update>

	<!--按操作员id删除-->
	<delete id="deleteByOperatorId">
		DELETE FROM <include refid="table" /> WHERE operator_id = #{operatorId,jdbcType=BIGINT}
	</delete>

	<!--按商户编号和id删除-->
	<delete id="deleteByMchNoAndId" parameterType="java.util.Map">
		DELETE FROM <include refid="table" />
		WHERE id = #{id,jdbcType=BIGINT} and mch_no = #{mchNo,jdbcType=VARCHAR}
	</delete>

	<!-- 员工VO -->
	<resultMap id="SupplierStaffVO" type="com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="phone" property="phone" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
		<result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
		<result column="type" property="type" jdbcType="SMALLINT"/>
		<result column="creator" property="creator" jdbcType="VARCHAR"/>
		<result column="updator" property="updator" jdbcType="VARCHAR"/>
		<result column="role_name" property="roleName" jdbcType="VARCHAR"/>
		<result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
	</resultMap>

	<!-- 员工VO公共列，tbl_supplier_staff 和 tbl_supplier_operator 连表-->
	<sql id="Staff_Vo_Column_List">
		t1.id as id,
		t1.create_time as create_time,
		t1.update_time as update_time,
		t2.phone as phone,
		t1.name as name,
		t2.name as operator_name,
		t1.mch_no as mch_no,
		t1.mch_name as mch_name,
		t1.type as type,
		t1.creator as creator,
		t1.updator as updator
	</sql>

	<!-- 员工VO公共列，tbl_supplier_staff 和 tbl_supplier_operator、tbl_supplier_staff、tbl_supplier_staff_role 连表-->
	<sql id="Staff_Role_Vo_Column_List">
		t1.id as id,
		t1.create_time as create_time,
		t1.update_time as update_time,
		t2.phone as phone,
		t1.name as name,
		t2.name as operator_name,
		t1.mch_no as mch_no,
		t1.mch_name as mch_name,
		t1.type as type,
		t1.creator as creator,
		t1.updator as updator,
		GROUP_CONCAT(d.`name` order by d.`name` desc ) as role_name

	</sql>

	<select id="getVOByMchNoAndId" parameterType="java.util.Map" resultMap="SupplierStaffVO">
		select <include refid="Staff_Vo_Column_List" />
		from tbl_supplier_staff t1 join tbl_supplier_operator t2 on t1.operator_id = t2.id
		where t1.id = #{id,jdbcType=BIGINT} and t1.mch_no = #{mchNo,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getVOByMchAndPhone" parameterType="java.util.Map" resultMap="SupplierStaffVO">
		select <include refid="Staff_Vo_Column_List" />
		from tbl_supplier_staff t1 join tbl_supplier_operator t2 on t1.operator_id = t2.id
		where t2.phone = #{phone,jdbcType=VARCHAR} and t1.mch_no = #{mchNo,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getVOByMchNoAndType" parameterType="java.util.Map" resultMap="SupplierStaffVO">
		select <include refid="Staff_Vo_Column_List" />
		from tbl_supplier_staff t1 join tbl_supplier_operator t2 on t1.operator_id = t2.id
		where t1.type = #{type,jdbcType=SMALLINT} and t1.mch_no = #{mchNo,jdbcType=VARCHAR}
	</select>

	<select id="listVOByOperatorId" parameterType="java.util.Map" resultMap="SupplierStaffVO">
		select <include refid="Staff_Vo_Column_List" />
		from tbl_supplier_staff t1 join tbl_supplier_operator t2 on t1.operator_id = t2.id
		where t1.operator_id = #{operatorId,jdbcType=BIGINT}
	</select>

	<select id="listVOByPhone" parameterType="java.util.Map" resultMap="SupplierStaffVO">
		select <include refid="Staff_Vo_Column_List" />
		from tbl_supplier_staff t1 join tbl_supplier_operator t2 on t1.operator_id = t2.id
		where t2.phone = #{phone,jdbcType=VARCHAR}
	</select>

	<!-- 根据角色id查询员工 -->
	<select id="listStaffVOByRoleId" parameterType="java.util.Map" resultMap="SupplierStaffVO">
		select <include refid="Staff_Vo_Column_List" />
		from tbl_supplier_staff t1
			join tbl_supplier_operator t2 on t1.operator_id = t2.id
			join tbl_supplier_staff_role t3 on t3.staff_id = t1.id
		where t3.role_id = #{roleId,jdbcType=BIGINT}
	</select>

	<!-- 分页查询员工 -->
	<select id="listStaffVOPage" parameterType="java.util.Map" resultMap="SupplierStaffVO">
		select <include refid="Staff_Vo_Column_List" />
		from tbl_supplier_staff t1 join tbl_supplier_operator t2 on t1.operator_id = t2.id
		<where>
			<include refid="condition_sql" />
		</where>
		order by t1.id desc
	</select>


	<!-- 分页查询员工计算总记录数 -->
	<select id="listStaffVOPageCount" parameterType="java.util.Map" resultType="long">
		select count(1)
		from tbl_supplier_staff t1 join tbl_supplier_operator t2 on t1.operator_id = t2.id
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询员工 包含角色名 -->
	<select id="listStaffRoleVOPage" parameterType="java.util.Map" resultMap="SupplierStaffVO">
		select <include refid="Staff_Role_Vo_Column_List" />
		from tbl_supplier_staff t1

		<if test="roleIds != null and roleIds.size() != 0">
			inner join (
			SELECT f.staff_id FROM tbl_supplier_staff_role f
			left join  tbl_supplier_staff t on f.role_id =t.id
			where f.role_id in
			<foreach collection="roleIds" item="item" index="index" open="(" separator="," close=")">
				#{item,jdbcType=BIGINT}
			</foreach>
			group by f.staff_id having count(f.staff_id) >= #{idNum}
			) as z on t1.id = z.staff_id

		</if>

		inner join tbl_supplier_operator t2 on t1.operator_id  =t2.id
		left join tbl_supplier_staff_role c on t1.id = c.staff_id
		left join tbl_supplier_role  d on c.role_id = d.id
		<where>
			<include refid="condition_sql" />
		</where>
		group by t1.id
		order by t1.id desc
	</select>


	<!-- 分页查询员工 包含角色名 -->
	<select id="listStaffRoleVOPageCount" parameterType="java.util.Map" resultType="long">
		select count(DISTINCT t1.id)
		from tbl_supplier_staff t1
		<if test="roleIds != null and roleIds.size() != 0">
			inner join (
			SELECT f.staff_id FROM tbl_supplier_staff_role f
			left join  tbl_supplier_staff t on f.role_id =t.id
			where f.role_id in
			<foreach collection="roleIds" item="item" index="index" open="(" separator="," close=")">
				#{item,jdbcType=BIGINT}
			</foreach>
			group by f.staff_id having count(f.staff_id) >= #{idNum}
			) as z on t1.id = z.staff_id
		</if>
		inner join tbl_supplier_operator t2 on t1.operator_id  =t2.id
		left join tbl_supplier_staff_role c on t1.id = c.staff_id
		left join tbl_supplier_role  d on c.role_id = d.id
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<select id="getDistinctStaffByRoleIdAndMainstayNo" resultType="String">
		SELECT distinct(s.id) as id FROM `tbl_supplier_staff` s left join
		tbl_supplier_staff_role r on s.id = r.staff_id
		where s.mch_no = #{mchNo,jdbcType=VARCHAR}
		<if test="list != null and list.size() > 0">
			and r.role_id in
			<foreach collection="list" item="item" open="(" separator="," close=")" index="index">
				#{item,jdbcType=BIGINT}
			</foreach>
		</if>
	</select>

</mapper>

