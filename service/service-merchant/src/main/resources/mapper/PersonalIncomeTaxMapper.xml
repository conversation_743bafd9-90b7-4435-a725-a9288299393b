<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.PersonalIncomeTaxMapper">
    <sql id="table">tbl_personal_income_tax</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.PersonalIncomeTax">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="start_point" property="startPoint" jdbcType="DECIMAL"/>
        <result column="tax_rate" property="taxRate" jdbcType="DECIMAL"/>
        <result column="deduction" property="deduction" jdbcType="DECIMAL"/>
        <result column="level" property="level" jdbcType="SMALLINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, start_point, tax_rate, deduction, level,description
    </sql>


    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            version,
            start_point,
            tax_rate,
            deduction,
            level,
            description
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.startPoint,jdbcType=DECIMAL},
            #{item.taxRate,jdbcType=DECIMAL},
            #{item.deduction,jdbcType=DECIMAL},
            #{item.level,jdbcType=SMALLINT},
            #{item.description,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.PersonalIncomeTax">
        UPDATE <include refid="table" /> SET
            version = #{version,jdbcType=SMALLINT},
            start_point = #{startPoint,jdbcType=DECIMAL},
            tax_rate = #{taxRate,jdbcType=DECIMAL},
            deduction = #{deduction,jdbcType=DECIMAL},
            level = #{level,jdbcType=SMALLINT},
            description = #{description,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.PersonalIncomeTax">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="startPoint != null">
                start_point = #{startPoint,jdbcType=DECIMAL},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="deduction != null">
                deduction = #{deduction,jdbcType=DECIMAL},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="startPoint != null">
            and start_point = #{startPoint,jdbcType=DECIMAL}
        </if>
        <if test="taxRate != null">
            and tax_rate = #{taxRate,jdbcType=DECIMAL}
        </if>
        <if test="deduction != null">
            and deduction = #{deduction,jdbcType=DECIMAL}
        </if>
        <if test="level != null">
            and level = #{level,jdbcType=SMALLINT}
        </if>
    </sql>

</mapper>
