<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate">
    <sql id="table">tbl_merchant_employer_quote_rate</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="QUOTE_ID" property="quoteId" jdbcType="BIGINT"/>
        <result column="RULE_PARAM" property="ruleParam" jdbcType="VARCHAR"/>
        <result column="FORMULA_TYPE" property="formulaType" jdbcType="SMALLINT"/>
        <result column="FIXED_FEE" property="fixedFee" jdbcType="DECIMAL"/>
        <result column="RATE" property="rate" jdbcType="DECIMAL"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, QUOTE_ID, RULE_PARAM, FORMULA_TYPE, FIXED_FEE, RATE, DESCRIPTION, CREATE_TIME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate">
        INSERT INTO <include refid="table" /> (
            VERSION,
            QUOTE_ID,
            RULE_PARAM,
            FORMULA_TYPE,
            FIXED_FEE,
            RATE,
            DESCRIPTION,
            CREATE_TIME
        ) VALUES (
            0,
            #{quoteId,jdbcType=BIGINT},
            #{ruleParam,jdbcType=VARCHAR},
            #{formulaType,jdbcType=SMALLINT},
            #{fixedFee,jdbcType=DECIMAL},
            #{rate,jdbcType=DECIMAL},
            #{description,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            QUOTE_ID,
            RULE_PARAM,
            FORMULA_TYPE,
            FIXED_FEE,
            RATE,
            DESCRIPTION,
            CREATE_TIME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            0,
            #{item.quoteId,jdbcType=BIGINT},
            #{item.ruleParam,jdbcType=VARCHAR},
            #{item.formulaType,jdbcType=SMALLINT},
            #{item.fixedFee,jdbcType=DECIMAL},
            #{item.rate,jdbcType=DECIMAL},
            #{item.description,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} +1,
            QUOTE_ID = #{quoteId,jdbcType=BIGINT},
            RULE_PARAM = #{ruleParam,jdbcType=VARCHAR},
            FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT},
            FIXED_FEE = #{fixedFee,jdbcType=DECIMAL},
            RATE = #{rate,jdbcType=DECIMAL},
            DESCRIPTION = #{description,jdbcType=VARCHAR},
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="quoteId != null">
                QUOTE_ID = #{quoteId,jdbcType=BIGINT},
            </if>
            <if test="ruleParam != null">
                RULE_PARAM = #{ruleParam,jdbcType=VARCHAR},
            </if>
            <if test="formulaType != null">
                FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT},
            </if>
            <if test="fixedFee != null">
                FIXED_FEE = #{fixedFee,jdbcType=DECIMAL},
            </if>
            <if test="rate != null">
                RATE = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!--条件删除-->
    <delete id="deleteBy" parameterType="java.util.Map">
        DELETE FROM <include refid="table" />
        where
        <trim  prefixOverrides="AND">
            <include refid="condition_sql" />
        </trim>
    </delete>
    
    <delete id="deleteByQuoteId">
        DELETE FROM <include refid="table"/> WHERE QUOTE_ID = #{quoteId,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="quoteId != null">
            and QUOTE_ID = #{quoteId,jdbcType=BIGINT}
        </if>
        <if test="quoteIdList != null and quoteIdList.size() > 0">
            and QUOTE_ID in
            <foreach collection="quoteIdList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="ruleParam != null and ruleParam !=''">
            and RULE_PARAM = #{ruleParam,jdbcType=VARCHAR}
        </if>
        <if test="formulaType != null">
            and FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT}
        </if>
        <if test="fixedFee != null">
            and FIXED_FEE = #{fixedFee,jdbcType=DECIMAL}
        </if>
        <if test="rate != null">
            and RATE = #{rate,jdbcType=DECIMAL}
        </if>
        <if test="description != null and description !=''">
            and DESCRIPTION = #{description,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

</mapper>
