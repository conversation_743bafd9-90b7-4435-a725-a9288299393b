<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator">
    <sql id="pms_operator"> TBL_PMS_OPERATOR </sql>
    <sql id="pms_role_operator"> TBL_PMS_ROLE_OPERATOR </sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="LOGIN_NAME" property="loginName" jdbcType="VARCHAR"/>
        <result column="LOGIN_PWD" property="loginPwd" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="REAL_NAME" property="realName" jdbcType="VARCHAR"/>
        <result column="MOBILE_NO" property="mobileNo" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="TYPE" property="type" jdbcType="SMALLINT"/>
        <result column="CURR_LOGIN_TIME" property="currLoginTime" jdbcType="TIMESTAMP"/>
        <result column="LAST_LOGIN_TIME" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="IS_CHANGED_PWD" property="isChangedPwd" jdbcType="SMALLINT"/>
        <result column="PWD_ERROR_COUNT" property="pwdErrorCount" jdbcType="SMALLINT"/>
        <result column="PWD_ERROR_TIME" property="pwdErrorTime" jdbcType="TIMESTAMP"/>
        <result column="IS_LOGIN_SMS_VERIFY" property="isLoginSmsVerify" jdbcType="SMALLINT"/>
        <result column="VALID_DATE" property="validDate" jdbcType="DATE"/>
        <result column="LAST_MOD_PWD_TIME" property="lastModPwdTime" jdbcType="TIMESTAMP"/>
        <result column="CREATOR" property="creator" jdbcType="VARCHAR"/>
        <result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
        <result column="HISTORY_PWD" property="historyPwd" jdbcType="VARCHAR"/>
        <result column="DEPARTMENT_ID" property="departmentId" jdbcType="BIGINT"/>
        <result column="DEPARTMENT_NAME" property="departmentName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
        ID,
		VERSION,
		CREATE_TIME,
		LOGIN_NAME,
		LOGIN_PWD,
		REMARK,
		REAL_NAME,
		MOBILE_NO,
		STATUS,
		TYPE,
		CURR_LOGIN_TIME,
		LAST_LOGIN_TIME,
		IS_CHANGED_PWD,
		PWD_ERROR_COUNT,
		PWD_ERROR_TIME,
		IS_LOGIN_SMS_VERIFY,
		VALID_DATE,
		LAST_MOD_PWD_TIME,
		CREATOR,
		UPDATOR,
		HISTORY_PWD,
        DEPARTMENT_ID,
        DEPARTMENT_NAME
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        INSERT INTO
        <include refid="pms_operator"/>
        (
        VERSION,
        CREATE_TIME,
        LOGIN_NAME,
        LOGIN_PWD,
        REMARK,
        REAL_NAME,
        MOBILE_NO,
        STATUS,
        TYPE,
        CURR_LOGIN_TIME,
        LAST_LOGIN_TIME,
        IS_CHANGED_PWD,
        PWD_ERROR_COUNT,
        PWD_ERROR_TIME,
        IS_LOGIN_SMS_VERIFY,
        VALID_DATE,
        LAST_MOD_PWD_TIME,
        CREATOR,
        UPDATOR,
        HISTORY_PWD,
        DEPARTMENT_ID,
        DEPARTMENT_NAME
        ) VALUES (
        0,
        #{createTime,jdbcType=TIMESTAMP},
        #{loginName,jdbcType=VARCHAR},
        #{loginPwd,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{realName,jdbcType=VARCHAR},
        #{mobileNo,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        #{type,jdbcType=SMALLINT},
        #{currLoginTime,jdbcType=TIMESTAMP},
        #{lastLoginTime,jdbcType=TIMESTAMP},
        #{isChangedPwd,jdbcType=SMALLINT},
        #{pwdErrorCount,jdbcType=SMALLINT},
        #{pwdErrorTime,jdbcType=TIMESTAMP},
        #{isLoginSmsVerify,jdbcType=SMALLINT},
        #{validDate,jdbcType=DATE},
        #{lastModPwdTime,jdbcType=TIMESTAMP},
        #{creator,jdbcType=VARCHAR},
        #{updator,jdbcType=VARCHAR},
        #{historyPwd,jdbcType=VARCHAR},
        #{departmentId,jdbcType=BIGINT},
        #{departmentName,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="pms_operator"/>
        (
        VERSION,
        CREATE_TIME,
        LOGIN_NAME,
        LOGIN_PWD,
        REMARK,
        REAL_NAME,
        MOBILE_NO,
        STATUS,
        TYPE,
        CURR_LOGIN_TIME,
        LAST_LOGIN_TIME,
        IS_CHANGED_PWD,
        PWD_ERROR_COUNT,
        PWD_ERROR_TIME,
        IS_LOGIN_SMS_VERIFY,
        VALID_DATE,
        LAST_MOD_PWD_TIME,
        CREATOR,
        UPDATOR,
        HISTORY_PWD,
        DEPARTMENT_ID,
        DEPARTMENT_NAME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.loginName,jdbcType=VARCHAR},
            #{item.loginPwd,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.realName,jdbcType=VARCHAR},
            #{item.mobileNo,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            #{item.type,jdbcType=SMALLINT},
            #{item.currLoginTime,jdbcType=TIMESTAMP},
            #{item.lastLoginTime,jdbcType=TIMESTAMP},
            #{item.isChangedPwd,jdbcType=SMALLINT},
            #{item.pwdErrorCount,jdbcType=SMALLINT},
            #{item.pwdErrorTime,jdbcType=TIMESTAMP},
            #{item.isLoginSmsVerify,jdbcType=SMALLINT},
            #{item.validDate,jdbcType=DATE},
            #{item.lastModPwdTime,jdbcType=TIMESTAMP},
            #{item.creator,jdbcType=VARCHAR},
            #{item.updator,jdbcType=VARCHAR},
            #{item.historyPwd,jdbcType=VARCHAR},
            #{departmentId,jdbcType=BIGINT},
            #{departmentName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator">
        UPDATE
        <include refid="pms_operator"/>
        <set>
            VERSION = #{version,jdbcType=INTEGER} + 1,
            LOGIN_NAME = #{loginName,jdbcType=VARCHAR},
            LOGIN_PWD = #{loginPwd,jdbcType=VARCHAR},
            REMARK = #{remark,jdbcType=VARCHAR},
            REAL_NAME = #{realName,jdbcType=VARCHAR},
            MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},
            STATUS = #{status,jdbcType=VARCHAR},
            TYPE = #{type,jdbcType=SMALLINT},
            CURR_LOGIN_TIME = #{currLoginTime,jdbcType=TIMESTAMP},
            LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=TIMESTAMP},
            IS_CHANGED_PWD = #{isChangedPwd,jdbcType=SMALLINT},
            PWD_ERROR_COUNT = #{pwdErrorCount,jdbcType=SMALLINT},
            PWD_ERROR_TIME = #{pwdErrorTime,jdbcType=TIMESTAMP},
            IS_LOGIN_SMS_VERIFY = #{isLoginSmsVerify,jdbcType=SMALLINT},
            VALID_DATE = #{validDate,jdbcType=DATE},
            LAST_MOD_PWD_TIME = #{lastModPwdTime,jdbcType=TIMESTAMP},
            CREATOR = #{creator,jdbcType=VARCHAR},
            UPDATOR = #{updator,jdbcType=VARCHAR},
            HISTORY_PWD = #{historyPwd,jdbcType=VARCHAR},
            DEPARTMENT_ID = #{departmentId,jdbcType=BIGINT},
            DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR}
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator">
        UPDATE
        <include refid="pms_operator"/>
        <set>
            VERSION = #{version,jdbcType=INTEGER} + 1,
            <if test="loginName != null">
                LOGIN_NAME = #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="loginPwd != null">
                LOGIN_PWD = #{loginPwd,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="realName != null">
                REAL_NAME = #{realName,jdbcType=VARCHAR},
            </if>
            <if test="mobileNo != null">
                MOBILE_NO = #{mobileNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                TYPE = #{type,jdbcType=SMALLINT},
            </if>
            <if test="currLoginTime != null">
                CURR_LOGIN_TIME = #{currLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastLoginTime != null">
                LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isChangedPwd != null">
                IS_CHANGED_PWD = #{isChangedPwd,jdbcType=SMALLINT},
            </if>
            <if test="pwdErrorCount != null">
                PWD_ERROR_COUNT = #{pwdErrorCount,jdbcType=SMALLINT},
            </if>
            <if test="pwdErrorTime != null">
                PWD_ERROR_TIME = #{pwdErrorTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isLoginSmsVerify != null">
                IS_LOGIN_SMS_VERIFY = #{isLoginSmsVerify,jdbcType=SMALLINT},
            </if>
            <if test="validDate != null">
                VALID_DATE = #{validDate,jdbcType=DATE},
            </if>
            <if test="lastModPwdTime != null">
                LAST_MOD_PWD_TIME = #{lastModPwdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="updator != null">
                UPDATOR = #{updator,jdbcType=VARCHAR},
            </if>
            <if test="historyPwd != null">
                HISTORY_PWD = #{historyPwd,jdbcType=VARCHAR}
            </if>
            <if test="departmentId != null">
                DEPARTMENT_ID = #{departmentId,jdbcType=BIGINT}
            </if>
            <if test="departmentName != null">
                DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 多条件组合查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="pms_operator"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 根据多条件组合查询，计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT count(ID) FROM
        <include refid="pms_operator"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 按查询条件删除 -->
    <delete id="deleteBy">
        DELETE FROM
        <include refid="pms_operator"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </delete>

    <!-- 根据多个id查询 -->
    <select id="listByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="pms_operator"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="pms_operator"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 按id主键删除 -->
    <delete id="deleteById">
        DELETE FROM
        <include refid="pms_operator"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 按多个id主键删除 -->
    <delete id="deleteByIdList" parameterType="list">
        DELETE FROM
        <include refid="pms_operator"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </delete>

    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

    <sql id="condition_sql">
        <if test="id != null ">
            AND ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            AND ID IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="departmentIds != null and departmentIds.size() > 0">
            AND DEPARTMENT_ID IN
            <foreach collection="departmentIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="version != null ">
            AND VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="createTime != null ">
            AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="loginName != null and loginName != ''">
            AND LOGIN_NAME = #{loginName,jdbcType=VARCHAR}
        </if>
        <if test="loginPwd != null and loginPwd !='' ">
            AND LOGIN_PWD = #{loginPwd,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark !='' ">
            AND REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="realName != null and realName !='' ">
            AND REAL_NAME =  #{realName,jdbcType=VARCHAR}
        </if>

        <if test="realNameLike != null and realNameLike !='' ">
            AND REAL_NAME like  concat('%',#{realNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="mobileNo != null and mobileNo !='' ">
            AND MOBILE_NO = #{mobileNo,jdbcType=VARCHAR}
        </if>
        <if test="status != null and status !='' ">
            AND STATUS = #{status,jdbcType=VARCHAR}
        </if>
        <if test="type != null and type !='' ">
            AND TYPE = #{type,jdbcType=SMALLINT}
        </if>
        <if test="currLoginTime != null ">
            AND CURR_LOGIN_TIME = #{currLoginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="lastLoginTime != null ">
            AND LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="isChangedPwd != null ">
            AND IS_CHANGED_PWD = #{isChangedPwd,jdbcType=SMALLINT}
        </if>
        <if test="pwdErrorCount != null ">
            AND PWD_ERROR_COUNT = #{pwdErrorCount,jdbcType=SMALLINT}
        </if>
        <if test="pwdErrorTime != null ">
            AND PWD_ERROR_TIME = #{pwdErrorTime,jdbcType=TIMESTAMP}
        </if>
        <if test="isLoginSmsVerify != null ">
            AND IS_LOGIN_SMS_VERIFY = #{isLoginSmsVerify,jdbcType=SMALLINT}
        </if>
        <if test="validDate != null ">
            AND VALID_DATE = #{validDate,jdbcType=DATE}
        </if>
        <if test="lastModPwdTime != null ">
            AND LAST_MOD_PWD_TIME = #{lastModPwdTime,jdbcType=TIMESTAMP}
        </if>
        <if test="creator != null and creator !='' ">
            AND CREATOR = #{creator,jdbcType=VARCHAR}
        </if>
        <if test="updator != null and updator !='' ">
            AND UPDATOR = #{updator,jdbcType=VARCHAR}
        </if>
        <if test="historyPwd != null and historyPwd !='' ">
            AND HISTORY_PWD = #{historyPwd,jdbcType=VARCHAR}
        </if>
        <if test="departmentId != null">
            AND DEPARTMENT_ID = #{departmentId,jdbcType=BIGINT}
        </if>
        <if test="departmentName != null">
            AND DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR}
        </if>
    </sql>

    <update id="reallocateDepartment" parameterType="java.util.Map">
        UPDATE
        <include refid="pms_operator"/>
        SET
            VERSION = VERSION + 1,
            DEPARTMENT_ID = #{newDepartmentId,jdbcType=BIGINT},
            DEPARTMENT_NAME = #{newDepartmentName,jdbcType=BIGINT}
        WHERE DEPARTMENT_ID = #{oldDepartmentId,jdbcType=BIGINT}
    </update>
</mapper>

