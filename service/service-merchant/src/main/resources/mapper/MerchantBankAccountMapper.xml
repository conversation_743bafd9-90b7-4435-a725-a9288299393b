<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantBankAccount">
	<sql id="table"> tbl_merchant_bank_account </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantBankAccount">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR"/>
		<result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"/>
		<result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
		<result column="BANK_CHANNEL_NO" property="bankChannelNo" jdbcType="VARCHAR"/>
		<result column="ALIPAY_ACCOUNT_NO" property="alipayAccountNo" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		UPDATE_TIME,
		VERSION,
		UPDATOR,
		MCH_NO,
		ACCOUNT_NO,
		ACCOUNT_NAME,
		BANK_NAME,
		BANK_CHANNEL_NO,
		ALIPAY_ACCOUNT_NO
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantBankAccount">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
        	ACCOUNT_NO ,
        	ACCOUNT_NAME ,
        	BANK_NAME ,
        	BANK_CHANNEL_NO,
        	ALIPAY_ACCOUNT_NO
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			0,
			#{updator,jdbcType=VARCHAR},
			#{mchNo,jdbcType=VARCHAR},
			#{accountNo,jdbcType=VARCHAR},
			#{accountName,jdbcType=VARCHAR},
			#{bankName,jdbcType=VARCHAR},
			#{bankChannelNo,jdbcType=VARCHAR},
			#{alipayAccountNo,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
        	ACCOUNT_NO ,
        	ACCOUNT_NAME ,
        	BANK_NAME ,
        	BANK_CHANNEL_NO,
			ALIPAY_ACCOUNT_NO
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			0,
			#{item.updator,jdbcType=VARCHAR},
			#{item.mchNo,jdbcType=VARCHAR},
			#{item.accountNo,jdbcType=VARCHAR},
			#{item.accountName,jdbcType=VARCHAR},
			#{item.bankName,jdbcType=VARCHAR},
			#{item.bankChannelNo,jdbcType=VARCHAR},
			#{item.alipayAccountNo,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantBankAccount">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
			ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
			BANK_NAME = #{bankName,jdbcType=VARCHAR},
			BANK_CHANNEL_NO = #{bankChannelNo,jdbcType=VARCHAR},
			ALIPAY_ACCOUNT_NO = #{alipayAccountNo,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantBankAccount">
		UPDATE <include refid="table" />
		<set>
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="updator != null">
				UPDATOR =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="mchNo != null">
				MCH_NO =#{mchNo,jdbcType=VARCHAR},
			</if>
			<if test="accountNo != null">
				ACCOUNT_NO =#{accountNo,jdbcType=VARCHAR},
			</if>
			<if test="accountName != null">
				ACCOUNT_NAME =#{accountName,jdbcType=VARCHAR},
			</if>
			<if test="bankName != null">
				BANK_NAME =#{bankName,jdbcType=VARCHAR},
			</if>
			<if test="bankChannelNo != null">
				BANK_CHANNEL_NO =#{bankChannelNo,jdbcType=VARCHAR},
			</if>
			<if test="alipayAccountNo != null">
				ALIPAY_ACCOUNT_NO = #{alipayAccountNo,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="accountNo != null and accountNo !=''">
			and ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR}
		</if>
		<if test="accountName != null and accountName !=''">
			and ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR}
		</if>
		<if test="bankName != null and bankName !=''">
			and BANK_NAME = #{bankName,jdbcType=VARCHAR}
		</if>
		<if test="bankChannelNo != null and bankChannelNo !=''">
			and BANK_CHANNEL_NO = #{bankChannelNo,jdbcType=VARCHAR}
		</if>
		<if test="alipayAccountNo != null and alipayAccountNo != ''">
			and ALIPAY_ACCOUNT_NO = #{alipayAccountNo,jdbcType=VARCHAR}
		</if>
	</sql>
</mapper>

