<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.AgreementSigner">
    <sql id="table">tbl_agreement_signer</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgreementSigner">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="AGREEMENT_ID" property="agreementId" jdbcType="BIGINT"/>
        <result column="SIGNER_NO" property="signerNo" jdbcType="VARCHAR"/>
        <result column="SIGNER_MCH_NAME" property="signerMchName" jdbcType="VARCHAR"/>
        <result column="SIGNER_NAME" property="signerName" jdbcType="VARCHAR"/>
        <result column="SIGNER_PHONE" property="signerPhone" jdbcType="VARCHAR"/>
        <result column="SIGNER_TYPE" property="signerType" jdbcType="SMALLINT"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="SIGNER_ACCOUNT_ID" property="signerAccountId" jdbcType="VARCHAR"/>
        <result column="SIGNER_AUTHORIZED_ACCOUNT_ID" property="signerAuthorizedAccountId" jdbcType="VARCHAR"/>
        <result column="SIGN_URL" property="signUrl" jdbcType="VARCHAR"/>
        <result column="OPERATOR_PHONE" property="operatorPhone" jdbcType="VARCHAR"/>
        <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"/>
        <result column="OPERATOR_TIME" property="operatorTime" jdbcType="TIMESTAMP"/>
        <result column="SIGN_TIME" property="signTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AGREEMENT_ID, SIGNER_NO,SIGNER_MCH_NAME, SIGNER_NAME,SIGNER_PHONE,SIGNER_TYPE,STATUS,SIGNER_ACCOUNT_ID,SIGNER_AUTHORIZED_ACCOUNT_ID,SIGN_URL,OPERATOR_PHONE,OPERATOR_NAME,OPERATOR_TIME,SIGN_TIME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.zhixianghui.facade.merchant.entity.AgreementSigner">
        INSERT INTO
        <include refid="table"/>
        (
        AGREEMENT_ID,
        SIGNER_NO,
        SIGNER_MCH_NAME,
        SIGNER_NAME,
        SIGNER_PHONE,
        SIGNER_TYPE,
        STATUS,
        SIGNER_ACCOUNT_ID,
        SIGNER_AUTHORIZED_ACCOUNT_ID,
        SIGN_URL,
        OPERATOR_PHONE,
        OPERATOR_NAME,
        OPERATOR_TIME,
        SIGN_TIME
        ) VALUES (
        #{agreementId,jdbcType=BIGINT},
        #{signerNo,jdbcType=VARCHAR},
        #{signerMchName,jdbcType=VARCHAR},
        #{signerName,jdbcType=VARCHAR},
        #{signerPhone,jdbcType=VARCHAR},
        #{signerType,jdbcType=SMALLINT},
        #{status,jdbcType=SMALLINT},
        #{signerAccountId,jdbcType=VARCHAR},
        #{signerAuthorizedAccountId,jdbcType=VARCHAR},
        #{signUrl,jdbcType=VARCHAR},
        #{operatorPhone,jdbcType=VARCHAR},
        #{operatorName,jdbcType=VARCHAR},
        #{operatorTime,jdbcType=TIMESTAMP},
        #{signTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        AGREEMENT_ID,
        SIGNER_NO,
        SIGNER_MCH_NAME,
        SIGNER_NAME,
        SIGNER_PHONE,
        SIGNER_TYPE,
        STATUS,
        SIGNER_ACCOUNT_ID,
        SIGNER_AUTHORIZED_ACCOUNT_ID,
        SIGN_URL,
        OPERATOR_PHONE,
        OPERATOR_NAME,
        OPERATOR_TIME,
        SIGN_TIME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.agreementId,jdbcType=BIGINT},
            #{item.signerNo,jdbcType=VARCHAR},
            #{item.signerMchName,jdbcType=VARCHAR},
            #{item.signerName,jdbcType=VARCHAR},
            #{item.signerPhone,jdbcType=VARCHAR},
            #{item.signerType,jdbcType=SMALLINT},
            #{item.status,jdbcType=SMALLINT},
            #{item.signerAccountId,jdbcType=VARCHAR},
            #{item.signerAuthorizedAccountId,jdbcType=VARCHAR},
            #{item.signUrl,jdbcType=VARCHAR},
            #{item.operatorPhone,jdbcType=VARCHAR},
            #{item.operatorName,jdbcType=VARCHAR},
            #{item.operatorTime,jdbcType=TIMESTAMP},
            #{item.signTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgreementSigner">
        UPDATE
        <include refid="table"/>
        SET
        AGREEMENT_ID = #{agreementId,jdbcType=BIGINT},
        SIGNER_NO = #{signerNo,jdbcType=VARCHAR},
        SIGNER_MCH_NAME = #{signerMchName,jdbcType=VARCHAR},
        SIGNER_NAME = #{signerName,jdbcType=VARCHAR},
        SIGNER_PHONE = #{signerPhone,jdbcType=VARCHAR},
        SIGNER_TYPE = #{signerType,jdbcType=SMALLINT},
        STATUS = #{status,jdbcType=SMALLINT},
        SIGNER_ACCOUNT_ID = #{signerAccountId,jdbcType=VARCHAR},
        SIGNER_AUTHORIZED_ACCOUNT_ID = #{signerAuthorizedAccountId,jdbcType=VARCHAR},
        SIGN_URL = #{signUrl,jdbcType=VARCHAR},
        OPERATOR_PHONE = #{operatorPhone,jdbcType=VARCHAR},
        OPERATOR_NAME = #{operatorName,jdbcType=VARCHAR},
        OPERATOR_TIME = #{operatorTime,jdbcType=TIMESTAMP},
        SIGN_TIME = #{signTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgreementSigner">
        UPDATE
        <include refid="table"/>
        <set>
            <if test="agreementId != null">
                AGREEMENT_ID = #{agreementId,jdbcType=BIGINT},
            </if>
            <if test="signerNo != null">
                SIGNER_NO = #{signerNo,jdbcType=VARCHAR},
            </if>
            <if test="signerMchName != null">
                SIGNER_MCH_NAME = #{signerMchName,jdbcType=VARCHAR}
            </if>
            <if test="signerName != null">
                SIGNER_NAME = #{signerName,jdbcType=VARCHAR}
            </if>
            <if test="signerPhone != null">
                SIGNER_PHONE = #{signerPhone,jdbcType=VARCHAR}
            </if>
            <if test="signerType != null">
                SIGNER_TYPE = #{signerType,jdbcType=SMALLINT}
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=SMALLINT}
            </if>
            <if test="signerAccountId != null">
                SIGNER_ACCOUNT_ID = #{signerAccountId,jdbcType=VARCHAR}
            </if>
            <if test="signerAuthorizedAccountId != null">
                SIGNER_AUTHORIZED_ACCOUNT_ID = #{signerAuthorizedAccountId,jdbcType=VARCHAR}
            </if>
            <if test="signUrl != null">
                SIGN_URL = #{signUrl,jdbcType=VARCHAR}
            </if>
            <if test="operatorPhone != null">
                OPERATOR_PHONE = #{operatorPhone,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null">
                OPERATOR_NAME = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="operatorTime != null">
                OPERATOR_TIME = #{operatorTime,jdbcType=VARCHAR}
            </if>
            <if test="signTime != null">
                SIGN_TIME = #{signTime,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <select id="deleteByAgreementId" parameterType="java.util.Map" resultType="int">
        DELETE FROM
        <include refid="table" />
        where AGREEMENT_ID = #{agreementId}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="agreementId != null">
            and AGREEMENT_ID = #{agreementId,jdbcType=BIGINT}
        </if>
        <if test="agreementIdList != null and agreementIdList.size() > 0">
            and AGREEMENT_ID in
            <foreach collection="agreementIdList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="signerNo != null and signerNo !=''">
            and SIGNER_NO = #{signerNo,jdbcType=VARCHAR}
        </if>
        <if test="signerMchName != null and signerMchName != ''">
            and SIGNER_MCH_NAME = #{signerMchName,jdbcType=VARCHAR}
        </if>
        <if test="signerName != null and signerName !=''">
            and SIGNER_NAME = #{signerName,jdbcType=VARCHAR}
        </if>
        <if test="signerPhone != null and signerPhone != ''">
            and SIGNER_PHONE = #{signerPhone,jdbcType=VARCHAR}
        </if>
        <if test="signerType != null">
            and SIGNER_TYPE = #{signerType,jdbcType=SMALLINT}
        </if>
        <if test="status != null">
            and STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="signerAccountId != null and signerAccountId != ''">
            and SIGNER_ACCOUNT_ID = #{signerAccountId,jdbcType=VARCHAR}
        </if>
        <if test="signerAuthorizedAccountId != null and signerAuthorizedAccountId != ''">
            and SIGNER_AUTHORIZED_ACCOUNT_ID = #{signerAuthorizedAccountId,jdbcType=VARCHAR}
        </if>
        <if test="signUrl != null and signUrl != ''">
            and SIGN_URL = #{signUrl,jdbcType=VARCHAR}
        </if>
        <if test="operatorPhone != null and operatorPhone != ''">
            and OPERATOR_PHONE = #{operatorPhone,jdbcType=VARCHAR}
        </if>
        <if test="operatorName != null and operatorName != ''">
            and OPERATOR_NAME = #{operatorName,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="exportList" parameterType="java.util.Map" resultType="com.zhixianghui.facade.merchant.vo.AgreementSignerExportVo">
        SELECT
        AGREEMENT_ID as agreementId,
        SIGNER_MCH_NAME as signerMchName,
        SIGNER_NO as signerNo,
        SIGNER_NAME as signerName,
        SIGNER_PHONE as signerPhone,
        SIGNER_TYPE as signerType,
        STATUS as signerStatus
        FROM <include refid="table"/>
        WHERE AGREEMENT_ID in
        <foreach collection="agreementIdList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>
