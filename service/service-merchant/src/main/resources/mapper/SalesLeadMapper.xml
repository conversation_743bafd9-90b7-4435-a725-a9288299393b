<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.SalesLeadMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.SalesLead">
    <!--@mbg.generated-->
    <!--@Table tbl_sales_lead-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile" />
    <result column="contact_email" jdbcType="VARCHAR" property="contactEmail" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="contact_position" jdbcType="VARCHAR" property="contactPosition" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="agent_no" jdbcType="VARCHAR" property="agentNo" />
    <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
    <result column="create_no" jdbcType="VARCHAR" property="createNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, contact_name, contact_mobile, contact_email, company_name, contact_position,
    create_time, `status`, ext_info, agent_no, agent_name,create_no
  </sql>
</mapper>
