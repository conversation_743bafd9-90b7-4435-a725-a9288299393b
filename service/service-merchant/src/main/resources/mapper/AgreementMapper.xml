<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.Agreement">
    <sql id="table">tbl_agreement</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.Agreement">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="TOPIC" property="topic" jdbcType="VARCHAR"/>
        <result column="CREATE_OPERATOR" property="createOperator" jdbcType="VARCHAR"/>
        <result column="UPDATE_OPERATOR" property="updateOperator" jdbcType="VARCHAR"/>
        <result column="SALER_ID" property="salerId" jdbcType="BIGINT"/>
        <result column="SALER_NAME" property="salerName" jdbcType="VARCHAR"/>
        <result column="FINISH_TIME" property="finishTime" jdbcType="TIMESTAMP"/>
        <result column="EXPIRE_TIME" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="DEADLINE" property="deadline" jdbcType="TIMESTAMP"/>
        <result column="SIGNER_NAME" property="signerName" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="EXT_INFO" property="extInfo" jdbcType="OTHER"/>
        <result column="SIGN_TYPE" property="signType" jdbcType="SMALLINT"/>
        <result column="SIGN_MODE" property="signMode" jdbcType="SMALLINT"/>
        <result column="FILE_TEMPLATE_ID" property="fileTemplateId" jdbcType="VARCHAR"/>
        <result column="FLOW_ID" property="flowId" jdbcType="VARCHAR"/>
        <result column="AGREEMENT_NO" property="agreementNo" jdbcType="VARCHAR"/>
        <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, TOPIC, CREATE_OPERATOR, UPDATE_OPERATOR, SALER_ID, SALER_NAME, FINISH_TIME, EXPIRE_TIME, DEADLINE, SIGNER_NAME, STATUS, DESCRIPTION, EXT_INFO,SIGN_TYPE,SIGN_MODE,FILE_TEMPLATE_ID,FLOW_ID,AGREEMENT_NO,ERROR_MSG
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.zhixianghui.facade.merchant.entity.Agreement">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        CREATE_TIME,
        UPDATE_TIME,
        TOPIC,
        CREATE_OPERATOR,
        UPDATE_OPERATOR,
        SALER_ID,
        SALER_NAME,
        FINISH_TIME,
        EXPIRE_TIME,
        DEADLINE,
        SIGNER_NAME,
        STATUS,
        DESCRIPTION,
        EXT_INFO,
        SIGN_TYPE,
        SIGN_MODE,
        FILE_TEMPLATE_ID,
        FLOW_ID,
        AGREEMENT_NO,
        ERROR_MSG
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{topic,jdbcType=VARCHAR},
        #{createOperator,jdbcType=VARCHAR},
        #{updateOperator,jdbcType=VARCHAR},
        #{salerId,jdbcType=BIGINT},
        #{salerName,jdbcType=VARCHAR},
        #{finishTime,jdbcType=TIMESTAMP},
        #{expireTime,jdbcType=TIMESTAMP},
        #{deadline,jdbcType=TIMESTAMP},
        #{signerName,jdbcType=VARCHAR},
        #{status,jdbcType=SMALLINT},
        #{description,jdbcType=VARCHAR},
        #{extInfo,jdbcType=OTHER},
        #{signType,jdbcType=SMALLINT},
        #{signMode,jdbcType=SMALLINT},
        #{fileTemplateId,jdbcType=VARCHAR},
        #{flowId,jdbcType=VARCHAR},
        #{agreementNo,jdbcType=VARCHAR},
        #{errorMsg,jdbcType=VARCHAR}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.Agreement">
        UPDATE
        <include refid="table"/>
        SET
        VERSION = #{version,jdbcType=SMALLINT} +1,
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        TOPIC = #{topic,jdbcType=VARCHAR},
        CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
        UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
        SALER_ID = #{salerId,jdbcType=BIGINT},
        SALER_NAME = #{salerName,jdbcType=VARCHAR},
        FINISH_TIME = #{finishTime,jdbcType=TIMESTAMP},
        EXPIRE_TIME = #{expireTime,jdbcType=TIMESTAMP},
        DEADLINE = #{deadline,jdbcType=TIMESTAMP},
        SIGNER_NAME = #{signerName,jdbcType=VARCHAR},
        STATUS = #{status,jdbcType=SMALLINT},
        DESCRIPTION = #{description,jdbcType=VARCHAR},
        EXT_INFO = #{extInfo,jdbcType=OTHER},
        SIGN_TYPE = #{signType,jdbcType=SMALLINT},
        SIGN_MODE = #{signMode,jdbcType=SMALLINT},
        FILE_TEMPLATE_ID = #{fileTemplateId,jdbcType=VARCHAR},
        FLOW_ID = #{flowId,jdbcType=VARCHAR},
        AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR},
        ERROR_MSG = #{errorMsg,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.Agreement">
        UPDATE
        <include refid="table"/>
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="topic != null">
                TOPIC = #{topic,jdbcType=VARCHAR},
            </if>
            <if test="createOperator != null">
                CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
            </if>
            <if test="updateOperator != null">
                UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
            </if>
            <if test="salerId != null">
                SALER_ID = #{salerId,jdbcType=BIGINT},
            </if>
            <if test="salerName != null">
                SALER_NAME = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="finishTime != null">
                FINISH_TIME = #{finishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                EXPIRE_TIME = #{expireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deadline != null">
                DEADLINE = #{deadline,jdbcType=TIMESTAMP},
            </if>
            <if test="signerName != null">
                SIGNER_NAME = #{signerName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="extInfo != null">
                EXT_INFO = #{extInfo,jdbcType=OTHER}
            </if>
            <if test="signType != null">
                SIGN_TYPE = #{signType,jdbcType=SMALLINT}
            </if>
            <if test="signMode != null">
                SIGN_MODE = #{signMode,jdbcType=SMALLINT}
            </if>
            <if test="fileTemplateId != null">
                FILE_TEMPLATE_ID = #{fileTemplateId,jdbcType=VARCHAR}
            </if>
            <if test="flowId != null">
                FLOW_ID = #{flowId,jdbcType=VARCHAR}
            </if>
            <if test="agreementNo != null">
                AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR}
            </if>
            <if test="errorMsg != null">
                ERROR_MSG = #{errorMsg,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <if test="( signerNoList != null and signerNoList.size()>0 ) or (signNameLike != null and signNameLike != '')">
            JOIN (
            SELECT AGREEMENT_ID FROM tbl_agreement_signer
            <where>
                <if test="signNameLike != null and signNameLike != ''">
                    and SIGNER_NAME like concat('%',#{signNameLike,jdbcType=VARCHAR},'%')
                </if>
                <if test="signerNoList != null and signerNoList.size()>0">
                    and SIGNER_NO IN
                    <foreach item="item" index="index" collection="signerNoList" open="(" separator="," close=")">
                        "${item}"
                    </foreach>
                </if>
            </where>
            GROUP BY AGREEMENT_ID)a
            on<include refid="table"/>.ID = a.AGREEMENT_ID
        </if>
        <if test="mchNoList != null and mchNoList.size() > 0">
            JOIN (
            SELECT AGREEMENT_ID FROM tbl_agreement_signer
                where SIGNER_TYPE = 100 and SIGNER_NO IN
                <foreach item="item" index="index" collection="mchNoList" open="(" separator="," close=")">
                    "${item}"
                </foreach>
            GROUP BY AGREEMENT_ID) b
            on<include refid="table"/>.ID = b.AGREEMENT_ID
        </if>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 分页查询-->
    <select id="countByList" parameterType="java.util.Map" resultType="long">
        SELECT
         count(1)
        FROM
        <include refid="table"/>
        <if test="( signerNoList != null and signerNoList.size()>0 ) or (signNameLike != null and signNameLike != '')">
            JOIN (
            SELECT AGREEMENT_ID FROM tbl_agreement_signer
            <where>
                <if test="signNameLike != null and signNameLike != ''">
                    and SIGNER_NAME like concat('%',#{signNameLike,jdbcType=VARCHAR},'%')
                </if>
                <if test="signerNoList != null and signerNoList.size()>0">
                    and SIGNER_NO IN
                    <foreach item="item" index="index" collection="signerNoList" open="(" separator="," close=")">
                        "${item}"
                    </foreach>
                </if>
            </where>
            GROUP BY AGREEMENT_ID)a
            on<include refid="table"/>.ID = a.AGREEMENT_ID
        </if>
        <if test="mchNoList != null and mchNoList.size() > 0">
            JOIN (
            SELECT AGREEMENT_ID FROM tbl_agreement_signer
            where SIGNER_TYPE = 100 and SIGNER_NO IN
            <foreach item="item" index="index" collection="mchNoList" open="(" separator="," close=")">
                "${item}"
            </foreach>
            GROUP BY AGREEMENT_ID) b
            on<include refid="table"/>.ID = b.AGREEMENT_ID
        </if>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>


    <!-- 其他后台分页查询-->
    <select id="listPageByMerchant" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <if test="( signerNoList != null and signerNoList.size()>0 ) or (signNameLike != null and signNameLike != '')">
            JOIN (
            SELECT AGREEMENT_ID FROM tbl_agreement_signer
            <where>
                <if test="signNameLike != null and signNameLike != ''">
                    and SIGNER_NAME like concat('%',#{signNameLike,jdbcType=VARCHAR},'%')
                </if>
                <if test="signerNoList != null and signerNoList.size()>0">
                    and SIGNER_NO IN
                    <foreach item="item" index="index" collection="signerNoList" open="(" separator="," close=")">
                        "${item}"
                    </foreach>
                </if>
            </where>
            GROUP BY AGREEMENT_ID
            <choose>
                <when test="signerNoList != null and signerNoList.size() == 1">
                    HAVING COUNT(AGREEMENT_ID) = 1
                </when>
                <otherwise>
                    HAVING COUNT(AGREEMENT_ID) = 2
                </otherwise>
            </choose>)a
            on<include refid="table"/>.ID = a.AGREEMENT_ID
        </if>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <select id="countByMerchant" parameterType="java.util.Map" resultType="long">
        SELECT
        count(1)
        FROM
        <include refid="table"/>
        <if test="( signerNoList != null and signerNoList.size()>0 ) or (signNameLike != null and signNameLike != '')">
            JOIN (
            SELECT AGREEMENT_ID FROM tbl_agreement_signer
            <where>
                <if test="signNameLike != null and signNameLike != ''">
                    and SIGNER_NAME like concat('%',#{signNameLike,jdbcType=VARCHAR},'%')
                </if>
                <if test="signerNoList != null and signerNoList.size()>0">
                    and SIGNER_NO IN
                    <foreach item="item" index="index" collection="signerNoList" open="(" separator="," close=")">
                        "${item}"
                    </foreach>
                </if>
            </where>
            GROUP BY AGREEMENT_ID
            <choose>
                <when test="signerNoList != null and signerNoList.size() == 1">
                    HAVING COUNT(AGREEMENT_ID) = 1
                </when>
                <otherwise>
                    HAVING COUNT(AGREEMENT_ID) = 2
                </otherwise>
            </choose>)a
            on<include refid="table"/>.ID = a.AGREEMENT_ID
        </if>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>


    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="topic != null and topic !=''">
            and TOPIC = #{topic,jdbcType=VARCHAR}
        </if>
        <if test="topicLike != null and topicLike !=''">
            and TOPIC like "%"#{topicLike,jdbcType=VARCHAR}"%"
        </if>
        <if test="createOperator != null and createOperator !=''">
            and CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
        </if>
        <if test="updateOperator != null and updateOperator !=''">
            and UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        </if>
        <if test="salerId != null">
            and SALER_ID = #{salerId,jdbcType=BIGINT}
        </if>
        <if test="salerName != null and salerName !=''">
            and SALER_NAME = #{salerName,jdbcType=VARCHAR}
        </if>
        <if test="finishTime != null">
            and FINISH_TIME = #{finishTime,jdbcType=TIMESTAMP}
        </if>
        <if test="expireTime != null">
            and EXPIRE_TIME = #{expireTime,jdbcType=TIMESTAMP}
        </if>
        <if test="deadline != null">
            and DEADLINE = #{deadline,jdbcType=TIMESTAMP}
        </if>
        <if test="signerName != null and signerName !=''">
            and SIGNER_NAME = #{signerName,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="description != null and description !=''">
            and DESCRIPTION = #{description,jdbcType=VARCHAR}
        </if>
        <if test="extInfo != null and extInfo !=''">
            and EXT_INFO = #{extInfo,jdbcType=OTHER}
        </if>
        <if test="signType != null">
            and SIGN_TYPE = #{signType,jdbcType=SMALLINT}
        </if>
        <if test="signMode != null">
            and SIGN_MODE = #{signMode,jdbcType=SMALLINT}
        </if>
        <if test="fileTemplateId != null and fileTemplateId != ''">
            and FILE_TEMPLATE_ID = #{fileTemplateId,jdbcType=VARCHAR}
        </if>
        <if test="flowId != null and flowId != ''">
            and FLOW_ID = #{flowId,jdbcType=VARCHAR}
        </if>
        <if test="agreementNo != null and agreementNo != ''">
            and AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR}
        </if>
        <!-- 以下自定义 -->
        <if test="beginDate != null and endDate != null">
            and CREATE_TIME between #{beginDate} and #{endDate}
        </if>
        <if test="beginDeadLine != null and beginDeadLine != '' and endDeadLine != null and endDeadLine != ''">
            and DEADLINE between #{beginDeadLine} and #{endDeadLine}
        </if>
        <if test="beginFinishTime != null and beginFinishTime != '' and endFinishTime != null and endFinishTime != ''">
            and FINISH_TIME between #{beginFinishTime} and #{endFinishTime}
        </if>
        <if test="idList != null and idList.size() > 0">
            and ID in
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="agreementIdList != null and agreementIdList.size() > 0">
            and ID in
            <foreach collection="agreementIdList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </sql>

    <sql id="join_condition_sql">
        <if test="id != null">
            and t1.ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and t1.VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and t1.CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and t1.UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="topic != null and topic !=''">
            and t1.TOPIC = #{topic,jdbcType=VARCHAR}
        </if>
        <if test="topicLike != null and topicLike !=''">
            and t1.TOPIC like "%"#{topicLike,jdbcType=VARCHAR}"%"
        </if>
        <if test="createOperator != null and createOperator !=''">
            and t1.CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
        </if>
        <if test="updateOperator != null and updateOperator !=''">
            and t1.UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        </if>
        <if test="salerId != null">
            and t1.SALER_ID = #{salerId,jdbcType=BIGINT}
        </if>
        <if test="salerName != null and salerName !=''">
            and t1.SALER_NAME = #{salerName,jdbcType=VARCHAR}
        </if>
        <if test="finishTime != null">
            and t1.FINISH_TIME = #{finishTime,jdbcType=TIMESTAMP}
        </if>
        <if test="expireTime != null">
            and t1.EXPIRE_TIME = #{expireTime,jdbcType=TIMESTAMP}
        </if>
        <if test="deadline != null">
            and t1.DEADLINE = #{deadline,jdbcType=TIMESTAMP}
        </if>
        <if test="signerName != null and signerName !=''">
            and t1.SIGNER_NAME = #{signerName,jdbcType=VARCHAR}
        </if>
        <if test="signNameLike != null and signNameLike != ''">
            and t1.SIGNER_NAME like concat('%',#{signNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="status != null">
            and t1.STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="description != null and description !=''">
            and t1.DESCRIPTION = #{description,jdbcType=VARCHAR}
        </if>
        <if test="extInfo != null and extInfo !=''">
            and t1.EXT_INFO = #{extInfo,jdbcType=OTHER}
        </if>
        <if test="signType != null">
            and t1.SIGN_TYPE = #{signType,jdbcType=SMALLINT}
        </if>
        <if test="signMode != null">
            and t1.SIGN_MODE = #{signMode,jdbcType=SMALLINT}
        </if>
        <if test="fileTemplateId != null and fileTemplateId != ''">
            and t1.FILE_TEMPLATE_ID = #{fileTemplateId,jdbcType=VARCHAR}
        </if>
        <if test="flowId != null and flowId != ''">
            and t1.FLOW_ID = #{flowId,jdbcType=VARCHAR}
        </if>
        <if test="agreementNo != null and agreementNo != ''">
            and t1.AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR}
        </if>
        <if test="idList != null and idList.size() > 0">
            and t1.ID in
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <!-- 以下自定义 -->
        <if test="beginDate != null and beginDate != '' and endDate != null and endDate != ''">
            and t1.CREATE_TIME between #{beginDate} and #{endDate}
        </if>
        <if test="mchList != null and mchList.size() > 0">
            and t2.SIGNER_NO in
            <foreach collection="mchList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="signerNoList != null and signerNoList.size() > 0">
            and t2.SIGNER_NO in
            <foreach collection="signerNoList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="maxId != null">
            and t1.id <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
    </sql>

    <update id="expireAgreement" parameterType="java.util.Map">
        UPDATE
        <include refid="table"/>
        SET STATUS = #{expireStatus,jdbcType=SMALLINT}
        WHERE EXPIRE_TIME &lt; #{nowDate,jdbcType=VARCHAR}
        AND STATUS != #{finishStatus,jdbcType=SMALLINT}
        AND STATUS != #{expireStatus,jdbcType=SMALLINT}
    </update>

    <resultMap id="exportMap" type="com.zhixianghui.facade.merchant.vo.AgreementExportVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="topic" property="topic" jdbcType="VARCHAR"/>
        <result column="finishTime" property="finishTime" jdbcType="TIMESTAMP"/>
        <result column="expireTime" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="deadline" property="deadline" jdbcType="TIMESTAMP"/>
        <result column="salerName" property="salerName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="agreementNo" property="agreementNo" jdbcType="VARCHAR"/>
        <collection property="signerVoList" javaType="list" ofType="com.zhixianghui.facade.merchant.vo.AgreementSignerExportVo">
            <result property="signerMchName" column="signerMchName" jdbcType="VARCHAR"/>
            <result property="signerNo" column="signerNo" jdbcType="VARCHAR"/>
            <result property="signerName" column="signerName" jdbcType="VARCHAR"/>
            <result property="signerPhone" column="signerPhone" jdbcType="VARCHAR"/>
            <result property="signerType" column="signerType" jdbcType="SMALLINT"/>
            <result property="signerStatus" column="signerStatus" jdbcType="SMALLINT"/>
        </collection>
    </resultMap>

    <select id="exportIdList" parameterType="java.util.Map" resultType="java.lang.Long">
        select t1.ID as id from tbl_agreement t1 left join tbl_agreement_signer t2 on t1.ID = t2.AGREEMENT_ID
        <where>
            <include refid="join_condition_sql"/>
        </where>
        group by t1.ID order by t1.ID
    </select>
    <select id="exportList" parameterType="java.util.Map" resultType="com.zhixianghui.facade.merchant.vo.AgreementExportVo">
        select
        ID as id,
        CREATE_TIME as createTime,
        UPDATE_TIME as updateTime,
        TOPIC as topic,
        FINISH_TIME as finishTime,
        EXPIRE_TIME as expireTime,
        DEADLINE as deadLine,
        SALER_NAME as salerName,
        STATUS as status,
        AGREEMENT_NO as agreementNo
        from tbl_agreement
        where ID in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        order by ID
    </select>

    <!--自定义分页-->
    <select id="listCustomPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        select DISTINCT(t1.id) as id,
        t1.VERSION,
        t1.CREATE_TIME,
        t1.UPDATE_TIME,
        t1.TOPIC,
        t1.CREATE_OPERATOR,
        t1.UPDATE_OPERATOR,
        t1.FINISH_TIME,
        t1.EXPIRE_TIME,
        t1.DEADLINE,
        t1.SIGNER_NAME,
        t1.STATUS,
        t1.DESCRIPTION,
        t1.EXT_INFO,
        t1.FILE_TEMPLATE_ID,
        t1.FLOW_ID,
        t1.AGREEMENT_NO from tbl_agreement t1 join (
        select t2.* from tbl_agreement_signer t1 left join tbl_agreement_signer t2 on t1.AGREEMENT_ID = t2.AGREEMENT_ID
        WHERE t1.SIGNER_NO = #{mainstayNo}) t2 on t1.ID = t2.AGREEMENT_ID
        <where>
            <include refid="join_condition_sql"/>
        </where>
        order by t1.create_time desc
    </select>

    <select id="customCountBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(DISTINCT(t1.id)) FROM
        tbl_agreement t1 join (
        select t2.* from tbl_agreement_signer t1 left join tbl_agreement_signer t2 on t1.AGREEMENT_ID = t2.AGREEMENT_ID
        WHERE t1.SIGNER_NO = #{mainstayNo}) t2 on t1.ID = t2.AGREEMENT_ID
        <where>
            <include refid="join_condition_sql"/>
        </where>
    </select>

    <select id="getPageByMchNoAndMainstayNo" parameterType="java.util.Map" resultMap="BaseResultMap">
        select t3.AGREEMENT_NO,t3.TOPIC,t3.SIGNER_NAME,t3.CREATE_TIME,t3.DEADLINE,t3.FINISH_TIME,t3.EXPIRE_TIME,t3.STATUS,t3.DESCRIPTION
        from tbl_agreement_signer t1 JOIN tbl_agreement_signer t2 on t1.AGREEMENT_ID = t2.AGREEMENT_ID
        left join tbl_agreement t3 on t1.AGREEMENT_ID = t3.ID
        <where>
            <if test="mchNo != null and mchNo != ''">
                t1.SIGNER_NO = #{mchNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNo != null and mainstayNo != ''">
                and t2.SIGNER_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY t3.id
        ORDER BY t3.id desc
    </select>

    <select id="mchPageBy" parameterType="java.util.Map" resultType="long">
        select count(id) from (select t3.id from tbl_agreement_signer t1 JOIN tbl_agreement_signer t2 on t1.AGREEMENT_ID
        = t2.AGREEMENT_ID
        left join tbl_agreement t3 on t1.AGREEMENT_ID = t3.ID
        <where>
            <if test="mchNo != null and mchNo != ''">
                t1.SIGNER_NO = #{mchNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNo != null and mainstayNo != ''">
                and t2.SIGNER_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
        </where>
        group by t3.id) t
    </select>
</mapper>
