<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord">
    <sql id="table">tbl_merchant_info_change_record</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="BUSINESS_ID" property="businessId" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="OBJECT_KEY" property="objectKey" jdbcType="VARCHAR"/>
        <result column="OBJECT_NAME" property="objectName" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_VALUE" property="originalValue" jdbcType="VARCHAR"/>
        <result column="TARGET_VALUE" property="targetValue" jdbcType="VARCHAR"/>
        <result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
        <result column="OPERATE_ID" property="operateId" jdbcType="BIGINT"/>
        <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"/>
        <result column="SOURCE" property="source" jdbcType="SMALLINT"/>
        <result column="OPERATE" property="operate" jdbcType="SMALLINT"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, BUSINESS_ID, CREATE_TIME, OBJECT_KEY, OBJECT_NAME, ORIGINAL_VALUE, TARGET_VALUE, MCH_NO, OPERATE_ID, OPERATE_NAME, SOURCE, OPERATE
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord">
        INSERT INTO <include refid="table" /> (
            VERSION,
            BUSINESS_ID,
            CREATE_TIME,
            OBJECT_KEY,
            OBJECT_NAME,
            ORIGINAL_VALUE,
            TARGET_VALUE,
            MCH_NO,
            OPERATE_ID,
            OPERATE_NAME,
            SOURCE,
            OPERATE
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{businessId,jdbcType=BIGINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{objectKey,jdbcType=VARCHAR},
            #{objectName,jdbcType=VARCHAR},
            #{originalValue,jdbcType=VARCHAR},
            #{targetValue,jdbcType=VARCHAR},
            #{mchNo,jdbcType=VARCHAR},
            #{operateId,jdbcType=BIGINT},
            #{operateName,jdbcType=VARCHAR},
            #{source,jdbcType=SMALLINT},
            #{operate,jdbcType=SMALLINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            BUSINESS_ID,
            CREATE_TIME,
            OBJECT_KEY,
            OBJECT_NAME,
            ORIGINAL_VALUE,
            TARGET_VALUE,
            MCH_NO,
            OPERATE_ID,
            OPERATE_NAME,
            SOURCE,
            OPERATE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.businessId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.objectKey,jdbcType=VARCHAR},
            #{item.objectName,jdbcType=VARCHAR},
            #{item.originalValue,jdbcType=VARCHAR},
            #{item.targetValue,jdbcType=VARCHAR},
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.operateId,jdbcType=BIGINT},
            #{item.operateName,jdbcType=VARCHAR},
            #{item.source,jdbcType=SMALLINT},
            #{item.operate,jdbcType=SMALLINT}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} +1,
            BUSINESS_ID = #{businessId,jdbcType=BIGINT},
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            OBJECT_KEY = #{objectKey,jdbcType=VARCHAR},
            OBJECT_NAME = #{objectName,jdbcType=VARCHAR},
            ORIGINAL_VALUE = #{originalValue,jdbcType=VARCHAR},
            TARGET_VALUE = #{targetValue,jdbcType=VARCHAR},
            MCH_NO = #{mchNo,jdbcType=VARCHAR},
            OPERATE_ID = #{operateId,jdbcType=BIGINT},
            OPERATE_NAME = #{operateName,jdbcType=VARCHAR},
            SOURCE = #{source,jdbcType=SMALLINT},
            OPERATE = #{operate,jdbcType=SMALLINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="businessId != null">
                BUSINESS_ID = #{businessId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="objectKey != null">
                OBJECT_KEY = #{objectKey,jdbcType=VARCHAR},
            </if>
            <if test="objectName != null">
                OBJECT_NAME = #{objectName,jdbcType=VARCHAR},
            </if>
            <if test="originalValue != null">
                ORIGINAL_VALUE = #{originalValue,jdbcType=VARCHAR},
            </if>
            <if test="targetValue != null">
                TARGET_VALUE = #{targetValue,jdbcType=VARCHAR},
            </if>
            <if test="mchNo != null">
                MCH_NO = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="operateId != null">
                OPERATE_ID = #{operateId,jdbcType=BIGINT},
            </if>
            <if test="operateName != null">
                OPERATE_NAME = #{operateName,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                SOURCE = #{source,jdbcType=SMALLINT},
            </if>
            <if test="operate != null">
                OPERATE = #{operate,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <select id="getLastChange" resultMap="BaseResultMap">
        select * from tbl_merchant_info_change_record where MCH_NO = #{mchNo,jdbcType=VARCHAR} order by CREATE_TIME desc limit 1
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByBusinessId">
        DELETE FROM <include refid="table" /> WHERE BUSINESS_ID = #{businessId,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="businessId != null">
            and BUSINESS_ID = #{businessId,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="objectKey != null and objectKey !=''">
            and OBJECT_KEY = #{objectKey,jdbcType=VARCHAR}
        </if>
        <if test="objectName != null and objectName !=''">
            and OBJECT_NAME = #{objectName,jdbcType=VARCHAR}
        </if>
        <if test="originalValue != null and originalValue !=''">
            and ORIGINAL_VALUE = #{originalValue,jdbcType=VARCHAR}
        </if>
        <if test="targetValue != null and targetValue !=''">
            and TARGET_VALUE = #{targetValue,jdbcType=VARCHAR}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and MCH_NO = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="operateId != null">
            and OPERATE_ID = #{operateId,jdbcType=BIGINT}
        </if>
        <if test="operateName != null and operateName !=''">
            and OPERATE_NAME = #{operateName,jdbcType=VARCHAR}
        </if>
        <if test="source != null">
            and SOURCE = #{source,jdbcType=SMALLINT}
        </if>
        <if test="operate != null">
            and OPERATE = #{operate,jdbcType=SMALLINT}
        </if>
    </sql>

</mapper>
