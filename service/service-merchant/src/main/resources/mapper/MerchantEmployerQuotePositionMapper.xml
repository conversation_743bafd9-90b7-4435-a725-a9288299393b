<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuotePosition">
    <sql id="table">tbl_merchant_employer_quote_position</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuotePosition">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="quote_id" property="quoteId" jdbcType="BIGINT"/>
        <result column="position_id" property="positionId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, mch_no, quote_id, position_id, create_time, update_time
    </sql>


    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            version,
            mch_no,
            quote_id,
            position_id,
            create_time,
            update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            0,
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.quoteId,jdbcType=BIGINT},
            #{item.positionId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuotePosition">
        UPDATE <include refid="table" /> SET
            version = #{version,jdbcType=SMALLINT},
            mch_no = #{mchNo,jdbcType=VARCHAR},
            quote_id = #{quoteId,jdbcType=BIGINT},
            position_id = #{positionId,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuotePosition">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="quoteId != null">
                quote_id = #{quoteId,jdbcType=BIGINT},
            </if>
            <if test="positionId != null">
                position_id = #{positionId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and mch_no = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="quoteId != null">
            and quote_id = #{quoteId,jdbcType=BIGINT}
        </if>
        <if test="positionId != null">
            and position_id = #{positionId,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <delete id="deleteByQuoteId">
        DELETE FROM <include refid="table"/> WHERE QUOTE_ID = #{quoteId,jdbcType=BIGINT}
    </delete>

</mapper>
