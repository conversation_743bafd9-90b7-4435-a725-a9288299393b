<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog">
    <sql id="table"> tbl_pms_operate_log </sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="OPERATOR_LOGIN_NAME" property="operatorLoginName" jdbcType="VARCHAR"/>
        <result column="OPERATE_TYPE" property="operateType" jdbcType="SMALLINT"/>
        <result column="OPERATE_SOURCE" property="operateSource" jdbcType="SMALLINT"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="IP" property="ip" jdbcType="VARCHAR"/>
        <result column="CONTENT" property="content" jdbcType="VARCHAR"/>
        <result column="DETAIL" property="detail" jdbcType="VARCHAR"/>
        <result column="ADDRESS" property="address" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
        ID,
		CREATE_TIME,
		OPERATOR_LOGIN_NAME,
		OPERATE_TYPE,
		OPERATE_SOURCE,
		STATUS,
		IP,
		CONTENT,
        DETAIL,
        ADDRESS
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog">
        INSERT INTO
        <include refid="table"/>
        (
        CREATE_TIME,
        OPERATOR_LOGIN_NAME,
        OPERATE_TYPE,
        OPERATE_SOURCE,
        STATUS,
        IP,
        CONTENT,
        DETAIL,
        ADDRESS
        ) VALUES (
        #{createTime,jdbcType=TIMESTAMP},
        #{operatorLoginName,jdbcType=VARCHAR},
        #{operateType,jdbcType=SMALLINT},
        #{operateSource,jdbcType=SMALLINT},
        #{status,jdbcType=SMALLINT},
        #{ip,jdbcType=VARCHAR},
        #{content,jdbcType=VARCHAR},
        #{detail,jdbcType=VARCHAR},
        #{address,jdbcType=VARCHAR}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog">
        UPDATE <include refid="table" /> SET
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        OPERATOR_LOGIN_NAME = #{operatorLoginName,jdbcType=VARCHAR},
        OPERATE_TYPE = #{operateType,jdbcType=SMALLINT},
        OPERATE_SOURCE = #{operateSource,jdbcType=SMALLINT},
        STATUS = #{status,jdbcType=SMALLINT},
        IP = #{ip,jdbcType=VARCHAR},
        CONTENT = #{content,jdbcType=VARCHAR},
        DETAIL = #{detail,jdbcType=VARCHAR},
        ADDRESS = #{address,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog">
        <!--@mbg.generated-->
        update
        <include refid="table"/>
        <set>
            <if test="operatorLoginName != null ">
                OPERATOR_LOGIN_NAME = #{operatorLoginName,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null ">
                OPERATE_TYPE = #{operateType,jdbcType=SMALLINT},
            </if>
            <if test="operateSource != null ">
                OPERATE_SOURCE = #{operateSource,jdbcType=SMALLINT},
            </if>
            <if test="status != null ">
                STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="ip != null ">
                IP = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="content != null ">
                CONTENT = #{content,jdbcType=VARCHAR},
            </if>
            <if test="detail != null ">
                DETAIL = #{detail,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR}
            </if>
        </set>
        where ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 插入记录 -->
    <insert id="insertGetkey" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog"  keyProperty="id" useGeneratedKeys="true" >
        INSERT INTO
        <include refid="table"/>
        (
        CREATE_TIME,
        OPERATOR_LOGIN_NAME,
        OPERATE_TYPE,
        OPERATE_SOURCE,
        STATUS,
        IP,
        CONTENT,
        DETAIL,
        ADDRESS
        ) VALUES (
        #{createTime,jdbcType=TIMESTAMP},
        #{operatorLoginName,jdbcType=VARCHAR},
        #{operateType,jdbcType=SMALLINT},
        #{operateSource,jdbcType=SMALLINT},
        #{status,jdbcType=SMALLINT},
        #{ip,jdbcType=VARCHAR},
        #{content,jdbcType=VARCHAR},
        #{detail,jdbcType=VARCHAR},
        #{address,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 多条件组合查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 根据多条件组合查询，计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT count(ID) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 按查询条件删除 -->
    <delete id="deleteBy">
        DELETE FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </delete>

    <!-- 根据多个id查询 -->
    <select id="listByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 按id主键删除 -->
    <delete id="deleteById">
        DELETE FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 按多个id主键删除 -->
    <delete id="deleteByIdList" parameterType="list">
        DELETE FROM
        <include refid="table"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </delete>

    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

    <sql id="condition_sql">
        <if test="id != null ">
            AND ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            AND ID IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="createTimeBegin != null ">
            AND CREATE_TIME >= #{createTimeBegin,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEnd != null ">
            AND CREATE_TIME <![CDATA[<=]]> #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="operatorLoginName != null and operatorLoginName !='' ">
            AND OPERATOR_LOGIN_NAME = #{operatorLoginName,jdbcType=VARCHAR}
        </if>
        <if test="operatorLoginNameLike != null and operatorLoginNameLike !='' ">
            AND OPERATOR_LOGIN_NAME like  concat('%',#{operatorLoginNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="operateType != null ">
            AND OPERATE_TYPE = #{operateType,jdbcType=SMALLINT}
        </if>
        <if test="operateSource != null ">
            AND OPERATE_SOURCE = #{operateSource,jdbcType=SMALLINT}
        </if>
        <if test="status != null ">
            AND STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="ip != null and ip !='' ">
            AND IP = #{ip,jdbcType=VARCHAR}
        </if>
        <if test="content != null and content !='' ">
            AND CONTENT = #{content,jdbcType=VARCHAR}
        </if>
        <if test="detail != null and detail !='' ">
            AND DETAIL concat('%',#{detail,jdbcType=VARCHAR},'%')
        </if>
        <if test="address != null and address !=''">
            and ADDRESS = #{address,jdbcType=VARCHAR}
        </if>
    </sql>
</mapper>

