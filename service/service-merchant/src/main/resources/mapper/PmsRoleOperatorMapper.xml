<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator">
    <sql id="table"> tbl_pms_role_operator </sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="ROLE_ID" property="roleId" jdbcType="BIGINT"/>
        <result column="OPERATOR_ID" property="operatorId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
        ID,
		ROLE_ID,
		OPERATOR_ID
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator">
        INSERT INTO
        <include refid="table"/>
        (
        ROLE_ID,
        OPERATOR_ID
        ) VALUES (
        #{roleId,jdbcType=BIGINT},
        #{operatorId,jdbcType=BIGINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        ROLE_ID,
        OPERATOR_ID
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleId,jdbcType=BIGINT},
            #{item.operatorId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator">
        UPDATE
        <include refid="table"/>
        <set>
            ROLE_ID = #{roleId,jdbcType=BIGINT},
            OPERATOR_ID = #{operatorId,jdbcType=BIGINT}
        </set>
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator">
        UPDATE
        <include refid="table"/>
        <set>
            <if test="roleId != null">
                ROLE_ID = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="operatorId != null">
                OPERATOR_ID = #{operatorId,jdbcType=BIGINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <select id="admin" parameterType="java.util.Map" resultMap="BaseResultMap">
         select tbl_pms_role_operator.*, tbl_pms_role.ROLE_TYPE
         from tbl_pms_role join tbl_pms_role_operator
         on tbl_pms_role.id = tbl_pms_role_operator.ROLE_ID
         where tbl_pms_role_operator.OPERATOR_ID = #{operatorId,jdbcType=BIGINT}
         and tbl_pms_role.ROLE_TYPE=0
    </select>

    <!-- 多条件组合查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 根据多条件组合查询，计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT count(ID) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <select id="countEmployerByRoleId" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(DISTINCT (ID)) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>


    <!-- 按查询条件删除 -->
    <delete id="deleteBy">
        DELETE FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </delete>

    <!-- 根据多个id查询 -->
    <select id="listByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 按id主键删除 -->
    <delete id="deleteById">
        DELETE FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 按多个id主键删除 -->
    <delete id="deleteByIdList" parameterType="list">
        DELETE FROM
        <include refid="table"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </delete>

    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

    <sql id="condition_sql">
        <if test="id != null ">
            AND ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            AND ID IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="roleId != null ">
            AND ROLE_ID = #{roleId,jdbcType=BIGINT}
        </if>
        <if test="operatorId != null ">
            AND OPERATOR_ID = #{operatorId,jdbcType=BIGINT}
        </if>
    </sql>

    <delete id="deleteByRoleId" parameterType="long">
        delete from
        <include refid="table"/>
        where ROLE_ID = #{roleId}
    </delete>

    <delete id="deleteByOperatorId" parameterType="long">
        delete from
        <include refid="table"/>
        where OPERATOR_ID = #{operatorId}
    </delete>

    <delete id="deleteByRoleIdAndOperatorId" parameterType="java.util.Map">
        delete from
        <include refid="table"/>
        where ROLE_ID = #{roleId} and OPERATOR_ID = #{operatorId}
    </delete>


</mapper>

