<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.AgentBankAccount">
    <sql id="table">tbl_agent_bank_account</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgentBankAccount">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="UPDATER" property="updater" jdbcType="VARCHAR"/>
        <result column="AGENT_NO" property="agentNo" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="BANK_CHANNEL_NO" property="bankChannelNo" jdbcType="VARCHAR"/>
        <result column="ALIPAY_ACCOUNT" property="alipayAccount" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_TIME, UPDATE_TIME, VERSION, UPDATER, AGENT_NO, ACCOUNT_NO, ACCOUNT_NAME, BANK_NAME, BANK_CHANNEL_NO,ALIPAY_ACCOUNT
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.AgentBankAccount">
        INSERT INTO <include refid="table" /> (
            CREATE_TIME,
            UPDATE_TIME,
            VERSION,
            UPDATER,
            AGENT_NO,
            ACCOUNT_NO,
            ACCOUNT_NAME,
            BANK_NAME,
            BANK_CHANNEL_NO,
            ALIPAY_ACCOUNT
        ) VALUES (
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            0,
            #{updater,jdbcType=VARCHAR},
            #{agentNo,jdbcType=VARCHAR},
            #{accountNo,jdbcType=VARCHAR},
            #{accountName,jdbcType=VARCHAR},
            #{bankName,jdbcType=VARCHAR},
            #{bankChannelNo,jdbcType=VARCHAR},
            #{alipayAccount,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            CREATE_TIME,
            UPDATE_TIME,
            VERSION,
            UPDATER,
            AGENT_NO,
            ACCOUNT_NO,
            ACCOUNT_NAME,
            BANK_NAME,
            BANK_CHANNEL_NO,
            ALIPAY_ACCOUNT
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            0,
            #{item.updater,jdbcType=VARCHAR},
            #{item.agentNo,jdbcType=VARCHAR},
            #{item.accountNo,jdbcType=VARCHAR},
            #{item.accountName,jdbcType=VARCHAR},
            #{item.bankName,jdbcType=VARCHAR},
            #{item.bankChannelNo,jdbcType=VARCHAR},
            #{item.alipayAccount,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgentBankAccount">
        UPDATE <include refid="table" /> SET
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            VERSION = #{version,jdbcType=SMALLINT} +1,
            UPDATER = #{updater,jdbcType=VARCHAR},
            AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
            ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
            BANK_NAME = #{bankName,jdbcType=VARCHAR},
            BANK_CHANNEL_NO = #{bankChannelNo,jdbcType=VARCHAR},
            ALIPAY_ACCOUNT = #{alipayAccount,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgentBankAccount">
        UPDATE <include refid="table" />
        <set>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null">
                ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null">
                ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                BANK_NAME = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankChannelNo != null">
                BANK_CHANNEL_NO = #{bankChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="alipayAccount != null">
                ALIPAY_ACCOUNT = #{alipayAccount,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="deleteBy" parameterType="java.util.Map" resultType="int">
        DELETE FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="updater != null and updater !=''">
            and UPDATER = #{updater,jdbcType=VARCHAR}
        </if>
        <if test="agentNo != null and agentNo !=''">
            and AGENT_NO = #{agentNo,jdbcType=VARCHAR}
        </if>
        <if test="accountNo != null and accountNo !=''">
            and ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR}
        </if>
        <if test="accountName != null and accountName !=''">
            and ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR}
        </if>
        <if test="bankName != null and bankName !=''">
            and BANK_NAME = #{bankName,jdbcType=VARCHAR}
        </if>
        <if test="bankChannelNo != null and bankChannelNo !=''">
            and BANK_CHANNEL_NO = #{bankChannelNo,jdbcType=VARCHAR}
        </if>
        <if test="alipayAccount != null and alipayAccount != ''">
            and ALIPAY_ACCOUNT = #{alipayAccount,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
