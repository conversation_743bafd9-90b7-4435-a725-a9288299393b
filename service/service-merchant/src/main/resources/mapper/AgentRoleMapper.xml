<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.agent.AgentRole">
	<sql id="table"> tbl_agent_role </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.agent.AgentRole">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="agent_no" property="agentNo" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="role_type" property="roleType" jdbcType="INTEGER"/>
	</resultMap>


	<!-- 用于返回的bean对象 -->
	<resultMap id="AgentRoleVo" type="com.zhixianghui.facade.merchant.vo.agent.AgentRoleVo">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="agent_no" property="agentNo" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="roleName" property="roleName" jdbcType="VARCHAR"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="employerNumber" property="employerNumber" jdbcType="INTEGER"/>
		<result column="role_type" property="roleType" jdbcType="INTEGER"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		update_time,
		agent_no,
		name,
		remark,
		role_type
	</sql>

	<!-- 用于select查询公用抽取的列Vo -->
	<sql id="Base_Column_List_Vo">
				tbl_agent_role.id,
		tbl_agent_role.version,
		tbl_agent_role.	create_time,
		tbl_agent_role.	update_time,
		tbl_agent_role.role_type,
		agent_no,
		name,
		remark
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.agent.AgentRole">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	agent_no ,
        	name ,
        	remark,
			role_type
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{agentNo,jdbcType=VARCHAR},
			#{name,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{roleType,jdbcType=INTEGER}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	agent_no ,
        	name ,
        	remark,
			role_type
        ) VALUES
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.agentNo,jdbcType=VARCHAR},
			#{item.name,jdbcType=VARCHAR},
			#{item.remark,jdbcType=VARCHAR},
			#{item.roleType,jdbcType=INTEGER}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.agent.AgentRole">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			agent_no = #{agentNo,jdbcType=VARCHAR},
			name = #{name,jdbcType=VARCHAR},
			remark = #{remark,jdbcType=VARCHAR},
			role_type = #{roleType,jdbcType=INTEGER}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.agent.AgentRole">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="agentNo != null">
				agent_no =#{agentNo,jdbcType=VARCHAR},
			</if>
			<if test="name != null">
				name =#{name,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				remark =#{remark,jdbcType=VARCHAR},
			</if>
			<if test="roleType != null">
				role_type = #{roleType,jdbcType=INTEGER}
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<select id="pmsListPage" parameterType="java.util.Map" resultMap="AgentRoleVo">
		select tbl_agent_role.id,
		tbl_agent_role.version,
		tbl_agent_role.	create_time,
		tbl_agent_role.	update_time,
		tbl_agent_role.role_type,
		agent_no,
		name as roleName,
		remark,
		count(tbl_agent_staff_role.role_id) AS employerNumber
		from
		<include refid="table" />
		LEFT JOIN tbl_agent_staff_role ON tbl_agent_role.id =tbl_agent_staff_role.role_id
		<where>
			agent_no = "-1"
			<if test="name != null">
				and name = #{name,jdbcType=VARCHAR}
			</if>
			<if test="remark != null">
				and remark = #{remark,jdbcType=VARCHAR}
			</if>
			<if test="roleType != null">
				and role_type = #{roleType,jdbcType=INTEGER}
			</if>
			<if test="roleType == null">
				and role_type != 2
			</if>
		</where>
		GROUP BY tbl_agent_role.id
		ORDER BY role_type ASC , id DESC

	</select>

	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="AgentRoleVo">
		select <include refid="Base_Column_List_Vo" />
		,count(tbl_agent_staff_role.role_id) AS employerNumber
		from
		<include refid="table" />
		LEFT JOIN tbl_agent_staff_role ON tbl_agent_role.id =tbl_agent_staff_role.role_id
		<where>
			<include refid="condition_sql" />
		</where>
		GROUP BY tbl_agent_role.id
		ORDER BY role_type ASC , id DESC

	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE id = #{id,jdbcType=BIGINT}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and update_time = #{updateTime,jdbcType=TIMESTAMP}
		</if>

		<if test="agentNo != null and agentNo !='' ">
			and (agent_no = #{agentNo,jdbcType=VARCHAR} or  agent_no = "-1")
		</if>
		<if test="name != null and name !='' ">
			and name like concat('%',#{name,jdbcType=VARCHAR},'%')
		</if>
		<if test="remark != null">
			and remark = #{remark,jdbcType=VARCHAR}
		</if>
		<if test="roleType != null">
			and role_type = #{roleType,jdbcType=INTEGER}
		</if>
	</sql>

	<!-- 分页查询统计 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from <include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<select id="countAgentRole" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM tbl_agent_role where role_type != 2

    </select>

	<select id="countByPresetRole" parameterType="java.util.Map" resultType="long">
		select count(id) from tbl_agent_staff where  agent_no = #{agentNo,jdbcType=VARCHAR}
		and id in(
					select staff_id from tbl_agent_staff_role where role_id in(
						select id from portal_employer_role where id=#{roleId,jdbcType=BIGINT}
						)
						)
	</select>

	<!--根据id和商户编号更新-->
	<update id="updateByIdAndAgentNo" parameterType="com.zhixianghui.facade.merchant.entity.user.agent.AgentRole">
		UPDATE <include refid="table" /> SET
		VERSION = #{version,jdbcType=INTEGER} + 1,
		create_time = #{createTime,jdbcType=TIMESTAMP},
		update_time = #{updateTime,jdbcType=TIMESTAMP},
		name = #{name,jdbcType=VARCHAR},
		remark = #{remark,jdbcType=VARCHAR}
		WHERE id = #{id,jdbcType=BIGINT} and agent_no = #{agentNo,jdbcType=VARCHAR} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<select id="listByStaffId" parameterType="java.util.Map" resultMap="BaseResultMap">
		SELECT
			t1.id as id,
			t1.version as version,
			t1.create_time as create_time,
			t1.update_time as update_time,
			t1.agent_no as agent_no,
			t1.name as name,
			t1.remark as remark,
			t1.role_type as roleType
		FROM tbl_agent_role t1
			join tbl_agent_staff_role t2 on t1.id = t2.role_id
		WHERE t2.staff_id = #{staffId,jdbcType=BIGINT}
	</select>
</mapper>

