<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment">
	<sql id="table"> tbl_pms_department </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="INTEGER"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="DEPARTMENT_NAME" property="departmentName" jdbcType="VARCHAR"/>
		<result column="NUMBER" property="number" jdbcType="VARCHAR"/>
		<result column="PARENT_ID" property="parentId" jdbcType="BIGINT"/>
		<result column="PARENT_NAME" property="parentName" jdbcType="VARCHAR"/>
		<result column="LEADER_ID" property="leaderId" jdbcType="BIGINT"/>
		<result column="LEADER_NAME" property="leaderName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		VERSION,
		CREATE_TIME,
		DEPARTMENT_NAME,
		NUMBER,
		PARENT_ID,
		PARENT_NAME,
		LEADER_ID,
		LEADER_NAME
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	CREATE_TIME ,
        	DEPARTMENT_NAME ,
			NUMBER,
        	PARENT_ID ,
        	PARENT_NAME ,
        	LEADER_ID ,
        	LEADER_NAME 
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{departmentName,jdbcType=VARCHAR},
			#{number,jdbcType=VARCHAR},
			#{parentId,jdbcType=BIGINT},
			#{parentName,jdbcType=VARCHAR},
			#{leaderId,jdbcType=BIGINT},
			#{leaderName,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	CREATE_TIME ,
        	DEPARTMENT_NAME ,
			NUMBER,
        	PARENT_ID ,
        	PARENT_NAME ,
        	LEADER_ID ,
        	LEADER_NAME 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.departmentName,jdbcType=VARCHAR},
			#{number,jdbcType=VARCHAR},
			#{item.parentId,jdbcType=BIGINT},
			#{item.parentName,jdbcType=VARCHAR},
			#{item.leaderId,jdbcType=BIGINT},
			#{item.leaderName,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
			NUMBER = #{number,jdbcType=VARCHAR},
			PARENT_ID = #{parentId,jdbcType=BIGINT},
			PARENT_NAME = #{parentName,jdbcType=VARCHAR},
			LEADER_ID = #{leaderId,jdbcType=BIGINT},
			LEADER_NAME = #{leaderName,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="departmentName != null">
				DEPARTMENT_NAME =#{departmentName,jdbcType=VARCHAR},
			</if>
			<if test="number != null">
				NUMBER =#{number,jdbcType=VARCHAR},
			</if>
			<if test="parentId != null">
				PARENT_ID =#{parentId,jdbcType=BIGINT},
			</if>
			<if test="parentName != null">
				PARENT_NAME =#{parentName,jdbcType=VARCHAR},
			</if>
			<if test="leaderId != null">
				LEADER_ID =#{leaderId,jdbcType=BIGINT},
			</if>
			<if test="leaderName != null">
				LEADER_NAME =#{leaderName,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		<![CDATA[ ORDER BY NUMBER ASC ]]>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>



	<!-- 根据序号查询总记录数 -->
	<select id="countByNumber" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(1) from
		<include refid="table" />
		<where>
			NUMBER = #{number,jdbcType=VARCHAR}
		</where>
	</select>

	<!-- 按条件统计 -->
	<select id="countBy" parameterType="java.util.Map" resultType="java.lang.Long">
		select count(*) from <include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="departmentName != null and departmentName !=''">
			and DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR}
		</if>
		<if test="number != null">
			and NUMBER = #{number,jdbcType=VARCHAR}
		</if>
		<if test="numbers != null and numbers.size() > 0">
			and NUMBER IN
			<foreach collection="numbers" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
		</if>
		<if test="parentId != null">
			and PARENT_ID = #{parentId,jdbcType=BIGINT}
		</if>
		<if test="parentName != null and parentName !=''">
			and PARENT_NAME = #{parentName,jdbcType=VARCHAR}
		</if>
		<if test="leaderId != null">
			and LEADER_ID = #{leaderId,jdbcType=BIGINT}
		</if>
		<if test="leaderName != null">
			and LEADER_NAME = #{leaderName,jdbcType=VARCHAR}
		</if>
	</sql>
</mapper>

