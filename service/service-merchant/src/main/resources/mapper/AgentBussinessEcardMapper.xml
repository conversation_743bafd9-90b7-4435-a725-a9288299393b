<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.AgentBussinessEcardMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgentBussinessEcard">
    <!--@mbg.generated-->
    <!--@Table tbl_agent_bussiness_ecard-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ecard_name" jdbcType="VARCHAR" property="ecardName" />
    <result column="ecard_mobile" jdbcType="VARCHAR" property="ecardMobile" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="wechat_id" jdbcType="VARCHAR" property="wechatId" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, ecard_name, ecard_mobile, company, `position`, email, wechat_id, address, create_time,
    update_time, open_id
  </sql>
</mapper>
