<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote">
	<sql id="table"> tbl_merchant_employer_quote </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="MAINSTAY_MCH_NO" property="mainstayMchNo" jdbcType="VARCHAR"/>
		<result column="MAINSTAY_MCH_NAME" property="mainstayMchName" jdbcType="VARCHAR"/>
		<result column="SUPPLIER_NO" property="supplierNo" jdbcType="VARCHAR"/>
		<result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR"/>
		<result column="RULE_PARAM" property="ruleParam" jdbcType="VARCHAR"/>
		<result column="FORMULA_TYPE" property="formulaType" jdbcType="SMALLINT"/>
		<result column="FIXED_FEE" property="fixedFee" jdbcType="DECIMAL"/>
		<result column="RATE" property="rate" jdbcType="DECIMAL"/>
		<result column="STATUS" property="status" jdbcType="SMALLINT"/>
		<result column="PRODUCT_NO" property="productNo" jdbcType="VARCHAR"/>
		<result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
		<result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
		<result column="FLOW_BUSINESS_KEY" property="flowBusinessKey" jdbcType="VARCHAR"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		VERSION,
		UPDATE_TIME,
		UPDATOR,
		MCH_NO,
		MAINSTAY_MCH_NO,
		MAINSTAY_MCH_NAME,
		SUPPLIER_NO,
		SUPPLIER_NAME,
		RULE_PARAM,
		FORMULA_TYPE,
		FIXED_FEE,
		RATE,
		STATUS,
		PRODUCT_NO,
		PRODUCT_NAME,
		DESCRIPTION,
		FLOW_BUSINESS_KEY
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
			UPDATE_TIME,
			UPDATOR,
        	MCH_NO ,
        	MAINSTAY_MCH_NO ,
        	MAINSTAY_MCH_NAME ,
        	SUPPLIER_NO,
        	SUPPLIER_NAME,
        	RULE_PARAM,
			FORMULA_TYPE,
			FIXED_FEE,
        	RATE,
        	STATUS,
        	PRODUCT_NO,
        	PRODUCT_NAME,
        	DESCRIPTION,
        	FLOW_BUSINESS_KEY
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			0,
			#{updateTime,jdbcType=TIMESTAMP},
			#{updator,jdbcType=VARCHAR},
			#{mchNo,jdbcType=VARCHAR},
			#{mainstayMchNo,jdbcType=VARCHAR},
			#{mainstayMchName,jdbcType=VARCHAR},
			#{supplierNo,jdbcType=VARCHAR},
			#{supplierName,jdbcType=VARCHAR},
			#{ruleParam,jdbcType=VARCHAR},
			#{formulaType,jdbcType=SMALLINT},
			#{fixedFee,jdbcType=DECIMAL},
			#{rate,jdbcType=DECIMAL},
			#{status,jdbcType=SMALLINT},
			#{productNo,jdbcType=VARCHAR},
			#{productName,jdbcType=VARCHAR},
			#{description,jdbcType=VARCHAR},
			#{flowBusinessKey,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
			UPDATE_TIME,
			UPDATOR,
        	MCH_NO ,
        	MAINSTAY_MCH_NO ,
        	MAINSTAY_MCH_NAME ,
        	SUPPLIER_NO,
        	SUPPLIER_NAME,
        	RULE_PARAM,
			FORMULA_TYPE,
			FIXED_FEE,
        	RATE,
        	STATUS,
        	PRODUCT_NO,
        	PRODUCT_NAME,
        	DESCRIPTION,
        	FLOW_BUSINESS_KEY
        ) VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.createTime,jdbcType=TIMESTAMP},
			0,
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.updator,jdbcType=VARCHAR},
			#{item.mchNo,jdbcType=VARCHAR},
			#{item.mainstayMchNo,jdbcType=VARCHAR},
			#{item.mainstayMchName,jdbcType=VARCHAR},
			#{item.supplierNo,jdbcType=VARCHAR},
			#{item.supplierName,jdbcType=VARCHAR},
			#{item.ruleParam,jdbcType=VARCHAR},
			#{item.formulaType,jdbcType=SMALLINT},
			#{item.fixedFee,jdbcType=DECIMAL},
			#{item.rate,jdbcType=DECIMAL},
			#{item.status,jdbcType=SMALLINT},
			#{item.productNo,jdbcType=VARCHAR},
			#{item.productName,jdbcType=VARCHAR},
			#{item.description,jdbcType=VARCHAR},
			#{item.flowBusinessKey,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			UPDATOR = #{updator,jdbcType=VARCHAR},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			MAINSTAY_MCH_NO = #{mainstayMchNo,jdbcType=VARCHAR},
			MAINSTAY_MCH_NAME = #{mainstayMchName,jdbcType=VARCHAR},
			SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR},
			SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
			RULE_PARAM = #{ruleParam,jdbcType=VARCHAR},
			FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT},
			FIXED_FEE = #{fixedFee,jdbcType=DECIMAL},
			RATE = #{rate,jdbcType=DECIMAL},
			STATUS = #{status,jdbcType=SMALLINT},
			PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
			PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
			DESCRIPTION = #{description,jdbcType=VARCHAR},
			FLOW_BUSINESS_KEY = #{flowBusinessKey,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<select id="listGroupByProductNo" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		group by product_no,mainstay_mch_no
	</select>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE ID = #{id,jdbcType=BIGINT}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!--条件删除-->
	<delete id="deleteBy" parameterType="java.util.Map">
		DELETE FROM <include refid="table" />
		where
		<trim  prefixOverrides="AND">
			<include refid="condition_sql" />
		</trim>
	</delete>
	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="productNo != null and productNo != ''">
			and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="mainstayMchNo != null and mainstayMchNo !=''">
			and MAINSTAY_MCH_NO = #{mainstayMchNo,jdbcType=VARCHAR}
		</if>
		<if test="mainstayMchName != null and mainstayMchName !=''">
			and MAINSTAY_MCH_NAME = #{mainstayMchName,jdbcType=VARCHAR}
		</if>
		<if test="supplierNo != null and supplierNo != ''">
			and SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
		</if>
		<if test="supplierName != null and supplierName != ''">
			and SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR}
		</if>
		<if test="formulaType != null">
			and FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT}
		</if>
		<if test="fixedFee != null">
			and FIXED_FEE = #{fixedFee,jdbcType=DECIMAL}
		</if>
		<if test="rate != null">
			and RATE = #{rate,jdbcType=DECIMAL}
		</if>
		<if test="status != null">
			and STATUS = #{status,jdbcType=SMALLINT}
		</if>
		<if test="flowBusinessKey != null and flowBusinessKey != ''">
			and FLOW_BUSINESS_KEY = #{flowBusinessKey,jdbcType=VARCHAR}
		</if>
	</sql>

	<resultMap id="joinResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote">
		<id column="ID" property="id" jdbcType="BIGINT"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="MAINSTAY_MCH_NO" property="mainstayMchNo" jdbcType="VARCHAR"/>
		<result column="MAINSTAY_MCH_NAME" property="mainstayMchName" jdbcType="VARCHAR"/>
		<result column="SUPPLIER_NO" property="supplierNo" jdbcType="VARCHAR"/>
		<result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR"/>
		<result column="STATUS" property="status" jdbcType="SMALLINT"/>
		<result column="PRODUCT_NO" property="productNo" jdbcType="VARCHAR"/>
		<result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
		<collection property="quoteRateList" ofType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate">
			<result column="RULE_PARAM" property="ruleParam" jdbcType="VARCHAR"/>
			<result column="FORMULA_TYPE" property="formulaType" jdbcType="SMALLINT"/>
			<result column="FIXED_FEE" property="fixedFee" jdbcType="DECIMAL"/>
			<result column="RATE" property="rate" jdbcType="DECIMAL"/>
			<result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
		</collection>
	</resultMap>

	<select id="getQuoteByMchNo" parameterType="java.util.Map" resultType="java.util.Map">
		select tb1.MAINSTAY_MCH_NO as mainstayMchNo,tb1.MAINSTAY_MCH_NAME as mainstayMchName,tb2.FORMULA_TYPE as formulaType,tb2.FIXED_FEE as fixedFee,tb2.RATE as rate
		 from tbl_merchant_employer_quote tb1
		left join tbl_merchant_employer_quote_rate tb2 on tb1.id = tb2.QUOTE_ID
		where tb1.mch_no = #{mchNo} and tb1.status = #{status}
	</select>

	<select id="getQuoteList" parameterType="java.util.Map" resultMap="joinResultMap">
		select tb1.ID,tb1.MCH_NO,tb1.MAINSTAY_MCH_NO,tb1.MAINSTAY_MCH_NAME,tb1.SUPPLIER_NO,tb1.SUPPLIER_NAME,tb1.STATUS,tb1.PRODUCT_NO,tb1.PRODUCT_NAME,
		tb2.RULE_PARAM,tb2.FORMULA_TYPE,tb2.FIXED_FEE,tb2.RATE,tb2.DESCRIPTION
		 from tbl_merchant_employer_quote tb1
		left join tbl_merchant_employer_quote_rate tb2 on tb1.id = tb2.QUOTE_ID
		<where>
			<include refid="join_condition_sql" />
		</where>
		order by tb1.ID
	</select>

	<sql id="join_condition_sql">
		<if test="mchNo != null and mchNo !=''">
			and tb1.MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="supplierNo != null and supplierNo !=''">
			and tb1.SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
		</if>
		<if test="status != null">
			and tb1.STATUS = #{status,jdbcType=SMALLINT}
		</if>
	</sql>

	<select id="listMchProduct" parameterType="java.util.Map" resultType="com.zhixianghui.facade.merchant.vo.merchant.ListMerchantProductVo">
		select
			t.PRODUCT_NO as productNo,
			t.PRODUCT_NAME as productName
		from tbl_merchant_employer_quote as t
		where
			t.STATUS = 100
			and t.MCH_NO = #{mchNo,jdbcType=VARCHAR}
		group by t.PRODUCT_NO,t.PRODUCT_NAME
	</select>
	<select id="listMainstayByEmployerNoAndProduct" parameterType="java.util.Map" resultType="com.zhixianghui.facade.merchant.vo.merchant.MainstaySimpleVo">
		select
			t.MAINSTAY_MCH_NO as mainstayNo,
			t.MAINSTAY_MCH_NAME as mainstayName
		from <include refid="table"/> t
		<where>
			<if test="status != null">
				and t.STATUS = #{status,jdbcType=INTEGER}
			</if>
			<if test="mchNo !=null and mchNo !=''">
				and t.MCH_NO = #{mchNo,jdbcType=VARCHAR}
			</if>
			<if test="productNo !=null and productNo !=''">
				and t.PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
</mapper>

