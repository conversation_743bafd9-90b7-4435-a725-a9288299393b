<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantChannel">
    <sql id="table">tbl_merchant_channel</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantChannel">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="SMALLINT"/>
        <result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
        <result column="CONFIG_VALUE" property="configValue" jdbcType="VARCHAR"/>
        <result column="CONFIG_KEY" property="configKey" jdbcType="VARCHAR"/>
        <result column="MCH_NAME" property="mchName" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, DELETE_FLAG, MCH_NO, CHANNEL_TYPE, CONFIG_VALUE, CONFIG_KEY, MCH_NAME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantChannel">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            DELETE_FLAG,
            MCH_NO,
            CHANNEL_TYPE,
            CONFIG_VALUE,
            CONFIG_KEY,
            MCH_NAME
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{deleteFlag,jdbcType=SMALLINT},
            #{mchNo,jdbcType=VARCHAR},
            #{channelType,jdbcType=SMALLINT},
            #{configValue,jdbcType=VARCHAR},
            #{configKey,jdbcType=VARCHAR},
            #{mchName,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            DELETE_FLAG,
            MCH_NO,
            CHANNEL_TYPE,
            CONFIG_VALUE,
            CONFIG_KEY,
            MCH_NAME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=SMALLINT},
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.channelType,jdbcType=SMALLINT},
            #{item.configValue,jdbcType=VARCHAR},
            #{item.configKey,jdbcType=VARCHAR},
            #{item.mchName,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantChannel">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} +1,
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            DELETE_FLAG = #{deleteFlag,jdbcType=SMALLINT},
            MCH_NO = #{mchNo,jdbcType=VARCHAR},
            CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
            CONFIG_VALUE = #{configValue,jdbcType=VARCHAR},
            CONFIG_KEY = #{configKey,jdbcType=VARCHAR},
            MCH_NAME = #{mchName,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantChannel">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                DELETE_FLAG = #{deleteFlag,jdbcType=SMALLINT},
            </if>
            <if test="mchNo != null">
                MCH_NO = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null">
                CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
            </if>
            <if test="configValue != null">
                CONFIG_VALUE = #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="configKey != null">
                CONFIG_KEY = #{configKey,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                MCH_NAME = #{mchName,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="deleteFlag != null">
            and DELETE_FLAG = #{deleteFlag,jdbcType=SMALLINT}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and MCH_NO = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="channelType != null">
            and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
        </if>
        <if test="configValue != null and configValue !=''">
            and CONFIG_VALUE = #{configValue,jdbcType=VARCHAR}
        </if>
        <if test="configKey != null and configKey !=''">
            and CONFIG_KEY = #{configKey,jdbcType=VARCHAR}
        </if>
        <if test="mchName != null and mchName !=''">
            and MCH_NAME = #{mchName,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
