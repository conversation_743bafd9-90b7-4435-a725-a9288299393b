<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.Ips">
    <sql id="table">tbl_ips</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.Ips">
        <id column="ID" property="id" jdbcType="INTEGER"/>

        <result column="IP" property="ip" jdbcType="VARCHAR"/>
        <result column="COUNTRY" property="country" jdbcType="VARCHAR"/>
        <result column="COUNTRY_ID" property="countryId" jdbcType="VARCHAR"/>
        <result column="AREA" property="area" jdbcType="VARCHAR"/>
        <result column="AREA_ID" property="areaId" jdbcType="INTEGER"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="PROVINCE_ID" property="provinceId" jdbcType="INTEGER"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="CITY_ID" property="cityId" jdbcType="INTEGER"/>
        <result column="COUNTY" property="county" jdbcType="VARCHAR"/>
        <result column="COUNTY_ID" property="countyId" jdbcType="INTEGER"/>
        <result column="ISP" property="isp" jdbcType="VARCHAR"/>
        <result column="ISP_ID" property="ispId" jdbcType="INTEGER"/>
        <result column="CREATED_AT" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_AT" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, IP, COUNTRY, COUNTRY_ID, AREA, AREA_ID, PROVINCE, PROVINCE_ID, CITY, CITY_ID, COUNTY, COUNTY_ID, ISP, ISP_ID, CREATED_AT, UPDATED_AT
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.Ips">
        INSERT INTO <include refid="table" /> (
            IP,
            COUNTRY,
            COUNTRY_ID,
            AREA,
            AREA_ID,
            PROVINCE,
            PROVINCE_ID,
            CITY,
            CITY_ID,
            COUNTY,
            COUNTY_ID,
            ISP,
            ISP_ID,
            CREATED_AT,
            UPDATED_AT
        ) VALUES (
            #{ip,jdbcType=VARCHAR},
            #{country,jdbcType=VARCHAR},
            #{countryId,jdbcType=VARCHAR},
            #{area,jdbcType=VARCHAR},
            #{areaId,jdbcType=INTEGER},
            #{province,jdbcType=VARCHAR},
            #{provinceId,jdbcType=INTEGER},
            #{city,jdbcType=VARCHAR},
            #{cityId,jdbcType=INTEGER},
            #{county,jdbcType=VARCHAR},
            #{countyId,jdbcType=INTEGER},
            #{isp,jdbcType=VARCHAR},
            #{ispId,jdbcType=INTEGER},
            #{createdAt,jdbcType=TIMESTAMP},
            #{updatedAt,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            IP,
            COUNTRY,
            COUNTRY_ID,
            AREA,
            AREA_ID,
            PROVINCE,
            PROVINCE_ID,
            CITY,
            CITY_ID,
            COUNTY,
            COUNTY_ID,
            ISP,
            ISP_ID,
            CREATED_AT,
            UPDATED_AT
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.ip,jdbcType=VARCHAR},
            #{item.country,jdbcType=VARCHAR},
            #{item.countryId,jdbcType=VARCHAR},
            #{item.area,jdbcType=VARCHAR},
            #{item.areaId,jdbcType=INTEGER},
            #{item.province,jdbcType=VARCHAR},
            #{item.provinceId,jdbcType=INTEGER},
            #{item.city,jdbcType=VARCHAR},
            #{item.cityId,jdbcType=INTEGER},
            #{item.county,jdbcType=VARCHAR},
            #{item.countyId,jdbcType=INTEGER},
            #{item.isp,jdbcType=VARCHAR},
            #{item.ispId,jdbcType=INTEGER},
            #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedAt,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.Ips">
        UPDATE <include refid="table" /> SET
            IP = #{ip,jdbcType=VARCHAR},
            COUNTRY = #{country,jdbcType=VARCHAR},
            COUNTRY_ID = #{countryId,jdbcType=VARCHAR},
            AREA = #{area,jdbcType=VARCHAR},
            AREA_ID = #{areaId,jdbcType=INTEGER},
            PROVINCE = #{province,jdbcType=VARCHAR},
            PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
            CITY = #{city,jdbcType=VARCHAR},
            CITY_ID = #{cityId,jdbcType=INTEGER},
            COUNTY = #{county,jdbcType=VARCHAR},
            COUNTY_ID = #{countyId,jdbcType=INTEGER},
            ISP = #{isp,jdbcType=VARCHAR},
            ISP_ID = #{ispId,jdbcType=INTEGER},
            CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
            UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.Ips">
        UPDATE <include refid="table" />
        <set>
            <if test="ip != null">
                IP = #{ip,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                COUNTRY = #{country,jdbcType=VARCHAR},
            </if>
            <if test="countryId != null">
                COUNTRY_ID = #{countryId,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                AREA = #{area,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                AREA_ID = #{areaId,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="city != null">
                CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                CITY_ID = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="county != null">
                COUNTY = #{county,jdbcType=VARCHAR},
            </if>
            <if test="countyId != null">
                COUNTY_ID = #{countyId,jdbcType=INTEGER},
            </if>
            <if test="isp != null">
                ISP = #{isp,jdbcType=VARCHAR},
            </if>
            <if test="ispId != null">
                ISP_ID = #{ispId,jdbcType=INTEGER},
            </if>
            <if test="createdAt != null">
                CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=INTEGER}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=INTEGER}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=INTEGER}
        </if>
        <if test="ip != null and ip !=''">
            and IP = #{ip,jdbcType=VARCHAR}
        </if>
        <if test="country != null and country !=''">
            and COUNTRY = #{country,jdbcType=VARCHAR}
        </if>
        <if test="countryId != null and countryId !=''">
            and COUNTRY_ID = #{countryId,jdbcType=VARCHAR}
        </if>
        <if test="area != null and area !=''">
            and AREA = #{area,jdbcType=VARCHAR}
        </if>
        <if test="areaId != null">
            and AREA_ID = #{areaId,jdbcType=INTEGER}
        </if>
        <if test="province != null and province !=''">
            and PROVINCE = #{province,jdbcType=VARCHAR}
        </if>
        <if test="provinceId != null">
            and PROVINCE_ID = #{provinceId,jdbcType=INTEGER}
        </if>
        <if test="city != null and city !=''">
            and CITY = #{city,jdbcType=VARCHAR}
        </if>
        <if test="cityId != null">
            and CITY_ID = #{cityId,jdbcType=INTEGER}
        </if>
        <if test="county != null and county !=''">
            and COUNTY = #{county,jdbcType=VARCHAR}
        </if>
        <if test="countyId != null">
            and COUNTY_ID = #{countyId,jdbcType=INTEGER}
        </if>
        <if test="isp != null and isp !=''">
            and ISP = #{isp,jdbcType=VARCHAR}
        </if>
        <if test="ispId != null">
            and ISP_ID = #{ispId,jdbcType=INTEGER}
        </if>
        <if test="createdAt != null">
            and CREATED_AT = #{createdAt,jdbcType=TIMESTAMP}
        </if>
        <if test="updatedAt != null">
            and UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP}
        </if>
    </sql>

</mapper>
