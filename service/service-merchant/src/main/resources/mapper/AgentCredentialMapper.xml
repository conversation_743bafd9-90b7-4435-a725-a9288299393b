<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.AgentCredential">
    <sql id="table">tbl_agent_credential</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgentCredential">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="UPDATER" property="updater" jdbcType="VARCHAR"/>
        <result column="AGENT_NO" property="agentNo" jdbcType="VARCHAR"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_TYPE" property="certificateType" jdbcType="SMALLINT"/>
        <result column="CERTIFICATE_NUMBER" property="certificateNumber" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_TERM_BEGIN" property="certificateTermBegin" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_TERM_END" property="certificateTermEnd" jdbcType="VARCHAR"/>
        <result column="CERTIFICATE_VALIDITY_DATE_TYPE" property="certificateValidityDateType" jdbcType="SMALLINT"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_TIME, UPDATE_TIME, VERSION, UPDATER, AGENT_NO, NAME, CERTIFICATE_TYPE, CERTIFICATE_NUMBER, CERTIFICATE_TERM_BEGIN, CERTIFICATE_TERM_END, CERTIFICATE_VALIDITY_DATE_TYPE
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.AgentCredential">
        INSERT INTO <include refid="table" /> (
            CREATE_TIME,
            UPDATE_TIME,
            VERSION,
            UPDATER,
            AGENT_NO,
            NAME,
            CERTIFICATE_TYPE,
            CERTIFICATE_NUMBER,
            CERTIFICATE_TERM_BEGIN,
            CERTIFICATE_TERM_END,
            CERTIFICATE_VALIDITY_DATE_TYPE
        ) VALUES (
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            0,
            #{updater,jdbcType=VARCHAR},
            #{agentNo,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{certificateType,jdbcType=SMALLINT},
            #{certificateNumber,jdbcType=VARCHAR},
            #{certificateTermBegin,jdbcType=VARCHAR},
            #{certificateTermEnd,jdbcType=VARCHAR},
            #{certificateValidityDateType,jdbcType=SMALLINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            CREATE_TIME,
            UPDATE_TIME,
            VERSION,
            UPDATER,
            AGENT_NO,
            NAME,
            CERTIFICATE_TYPE,
            CERTIFICATE_NUMBER,
            CERTIFICATE_TERM_BEGIN,
            CERTIFICATE_TERM_END,
            CERTIFICATE_VALIDITY_DATE_TYPE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            0,
            #{item.updater,jdbcType=VARCHAR},
            #{item.agentNo,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR},
            #{item.certificateType,jdbcType=SMALLINT},
            #{item.certificateNumber,jdbcType=VARCHAR},
            #{item.certificateTermBegin,jdbcType=VARCHAR},
            #{item.certificateTermEnd,jdbcType=VARCHAR},
            #{item.certificateValidityDateType,jdbcType=SMALLINT}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgentCredential">
        UPDATE <include refid="table" /> SET
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            VERSION = #{version,jdbcType=SMALLINT} +1,
            UPDATER = #{updater,jdbcType=VARCHAR},
            AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            NAME = #{name,jdbcType=VARCHAR},
            CERTIFICATE_TYPE = #{certificateType,jdbcType=SMALLINT},
            CERTIFICATE_NUMBER = #{certificateNumber,jdbcType=VARCHAR},
            CERTIFICATE_TERM_BEGIN = #{certificateTermBegin,jdbcType=VARCHAR},
            CERTIFICATE_TERM_END = #{certificateTermEnd,jdbcType=VARCHAR},
            CERTIFICATE_VALIDITY_DATE_TYPE = #{certificateValidityDateType,jdbcType=SMALLINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgentCredential">
        UPDATE <include refid="table" />
        <set>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="certificateType != null">
                CERTIFICATE_TYPE = #{certificateType,jdbcType=SMALLINT},
            </if>
            <if test="certificateNumber != null">
                CERTIFICATE_NUMBER = #{certificateNumber,jdbcType=VARCHAR},
            </if>
            <if test="certificateTermBegin != null">
                CERTIFICATE_TERM_BEGIN = #{certificateTermBegin,jdbcType=VARCHAR},
            </if>
            <if test="certificateTermEnd != null">
                CERTIFICATE_TERM_END = #{certificateTermEnd,jdbcType=VARCHAR},
            </if>
            <if test="certificateValidityDateType != null">
                CERTIFICATE_VALIDITY_DATE_TYPE = #{certificateValidityDateType,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="deleteBy" parameterType="java.util.Map" resultType="int">
        delete FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="updater != null and updater !=''">
            and UPDATER = #{updater,jdbcType=VARCHAR}
        </if>
        <if test="agentNo != null and agentNo !=''">
            and AGENT_NO = #{agentNo,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name !=''">
            and NAME = #{name,jdbcType=VARCHAR}
        </if>
        <if test="certificateType != null">
            and CERTIFICATE_TYPE = #{certificateType,jdbcType=SMALLINT}
        </if>
        <if test="certificateNumber != null and certificateNumber !=''">
            and CERTIFICATE_NUMBER = #{certificateNumber,jdbcType=VARCHAR}
        </if>
        <if test="certificateTermBegin != null and certificateTermBegin !=''">
            and CERTIFICATE_TERM_BEGIN = #{certificateTermBegin,jdbcType=VARCHAR}
        </if>
        <if test="certificateTermEnd != null and certificateTermEnd !=''">
            and CERTIFICATE_TERM_END = #{certificateTermEnd,jdbcType=VARCHAR}
        </if>
        <if test="certificateValidityDateType != null">
            and CERTIFICATE_VALIDITY_DATE_TYPE = #{certificateValidityDateType,jdbcType=SMALLINT}
        </if>
    </sql>

</mapper>
