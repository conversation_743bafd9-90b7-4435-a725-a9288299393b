<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.Regions">
    <sql id="table">tbl_regions</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.Regions">
        <id column="ID" property="id" jdbcType="INTEGER"/>

        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="INTEGER"/>
        <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR"/>
        <result column="LEVEL" property="level" jdbcType="SMALLINT"/>
        <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"/>
        <result column="ZIP_CODE" property="zipCode" jdbcType="VARCHAR"/>
        <result column="MERGER_NAME" property="mergerName" jdbcType="VARCHAR"/>
        <result column="LNG" property="lng" jdbcType="VARCHAR"/>
        <result column="LAT" property="lat" jdbcType="VARCHAR"/>
        <result column="PINYIN" property="pinyin" jdbcType="VARCHAR"/>
        <result column="CREATED_AT" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_AT" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, PARENT_ID, SHORT_NAME, LEVEL, CITY_CODE, ZIP_CODE, MERGER_NAME, LNG, LAT, PINYIN, CREATED_AT, UPDATED_AT
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.Regions">
        INSERT INTO <include refid="table" /> (
            NAME,
            PARENT_ID,
            SHORT_NAME,
            LEVEL,
            CITY_CODE,
            ZIP_CODE,
            MERGER_NAME,
            LNG,
            LAT,
            PINYIN,
            CREATED_AT,
            UPDATED_AT
        ) VALUES (
            #{name,jdbcType=VARCHAR},
            #{parentId,jdbcType=INTEGER},
            #{shortName,jdbcType=VARCHAR},
            #{level,jdbcType=SMALLINT},
            #{cityCode,jdbcType=VARCHAR},
            #{zipCode,jdbcType=VARCHAR},
            #{mergerName,jdbcType=VARCHAR},
            #{lng,jdbcType=VARCHAR},
            #{lat,jdbcType=VARCHAR},
            #{pinyin,jdbcType=VARCHAR},
            #{createdAt,jdbcType=TIMESTAMP},
            #{updatedAt,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            NAME,
            PARENT_ID,
            SHORT_NAME,
            LEVEL,
            CITY_CODE,
            ZIP_CODE,
            MERGER_NAME,
            LNG,
            LAT,
            PINYIN,
            CREATED_AT,
            UPDATED_AT
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.name,jdbcType=VARCHAR},
            #{item.parentId,jdbcType=INTEGER},
            #{item.shortName,jdbcType=VARCHAR},
            #{item.level,jdbcType=SMALLINT},
            #{item.cityCode,jdbcType=VARCHAR},
            #{item.zipCode,jdbcType=VARCHAR},
            #{item.mergerName,jdbcType=VARCHAR},
            #{item.lng,jdbcType=VARCHAR},
            #{item.lat,jdbcType=VARCHAR},
            #{item.pinyin,jdbcType=VARCHAR},
            #{item.createdAt,jdbcType=TIMESTAMP},
            #{item.updatedAt,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.Regions">
        UPDATE <include refid="table" /> SET
            NAME = #{name,jdbcType=VARCHAR},
            PARENT_ID = #{parentId,jdbcType=INTEGER},
            SHORT_NAME = #{shortName,jdbcType=VARCHAR},
            LEVEL = #{level,jdbcType=SMALLINT},
            CITY_CODE = #{cityCode,jdbcType=VARCHAR},
            ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
            MERGER_NAME = #{mergerName,jdbcType=VARCHAR},
            LNG = #{lng,jdbcType=VARCHAR},
            LAT = #{lat,jdbcType=VARCHAR},
            PINYIN = #{pinyin,jdbcType=VARCHAR},
            CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
            UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.Regions">
        UPDATE <include refid="table" />
        <set>
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                PARENT_ID = #{parentId,jdbcType=INTEGER},
            </if>
            <if test="shortName != null">
                SHORT_NAME = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                LEVEL = #{level,jdbcType=SMALLINT},
            </if>
            <if test="cityCode != null">
                CITY_CODE = #{cityCode,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="mergerName != null">
                MERGER_NAME = #{mergerName,jdbcType=VARCHAR},
            </if>
            <if test="lng != null">
                LNG = #{lng,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                LAT = #{lat,jdbcType=VARCHAR},
            </if>
            <if test="pinyin != null">
                PINYIN = #{pinyin,jdbcType=VARCHAR},
            </if>
            <if test="createdAt != null">
                CREATED_AT = #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=INTEGER}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=INTEGER}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=INTEGER}
        </if>
        <if test="name != null and name !=''">
            and NAME = #{name,jdbcType=VARCHAR}
        </if>
        <if test="parentId != null">
            and PARENT_ID = #{parentId,jdbcType=INTEGER}
        </if>
        <if test="shortName != null and shortName !=''">
            and SHORT_NAME = #{shortName,jdbcType=VARCHAR}
        </if>
        <if test="level != null">
            and LEVEL = #{level,jdbcType=SMALLINT}
        </if>
        <if test="cityCode != null and cityCode !=''">
            and CITY_CODE = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="zipCode != null and zipCode !=''">
            and ZIP_CODE = #{zipCode,jdbcType=VARCHAR}
        </if>
        <if test="mergerName != null and mergerName !=''">
            and MERGER_NAME = #{mergerName,jdbcType=VARCHAR}
        </if>
        <if test="lng != null and lng !=''">
            and LNG = #{lng,jdbcType=VARCHAR}
        </if>
        <if test="lat != null and lat !=''">
            and LAT = #{lat,jdbcType=VARCHAR}
        </if>
        <if test="pinyin != null and pinyin !=''">
            and PINYIN = #{pinyin,jdbcType=VARCHAR}
        </if>
        <if test="createdAt != null">
            and CREATED_AT = #{createdAt,jdbcType=TIMESTAMP}
        </if>
        <if test="updatedAt != null">
            and UPDATED_AT = #{updatedAt,jdbcType=TIMESTAMP}
        </if>
    </sql>

</mapper>
