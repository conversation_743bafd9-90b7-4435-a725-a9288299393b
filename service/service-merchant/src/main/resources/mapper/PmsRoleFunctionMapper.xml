<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleFunction">
    <sql id="table"> tbl_pms_role_function </sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleFunction">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="ROLE_ID" property="roleId" jdbcType="BIGINT"/>
        <result column="FUNCTION_ID" property="functionId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
        ID,
		ROLE_ID,
		FUNCTION_ID
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleFunction">
        INSERT INTO
        <include refid="table"/>
        (
        ROLE_ID,
        FUNCTION_ID
        ) VALUES (
        #{roleId,jdbcType=BIGINT},
        #{functionId,jdbcType=BIGINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        ROLE_ID,
        FUNCTION_ID
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.roleId,jdbcType=BIGINT},
            #{item.functionId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleFunction">
        UPDATE
        <include refid="table"/>
        <set>
            ROLE_ID = #{roleId,jdbcType=BIGINT},
            FUNCTION_ID = #{functionId,jdbcType=BIGINT}
        </set>
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleFunction">
        UPDATE
        <include refid="table"/>
        <set>
            <if test="roleId != null">
                ROLE_ID = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="functionId != null">
                FUNCTION_ID = #{functionId,jdbcType=BIGINT},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 多条件组合查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 根据多条件组合查询，计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT count(ID) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 按查询条件删除 -->
    <delete id="deleteBy">
        DELETE FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </delete>

    <!-- 根据多个id查询 -->
    <select id="listByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 按id主键删除 -->
    <delete id="deleteById">
        DELETE FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 按多个id主键删除 -->
    <delete id="deleteByIdList" parameterType="list">
        DELETE FROM
        <include refid="table"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </delete>

    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

    <sql id="condition_sql">
        <if test="id != null ">
            AND ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            AND ID IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="roleId != null ">
            AND ROLE_ID = #{roleId,jdbcType=BIGINT}
        </if>
        <if test="functionId != null ">
            AND FUNCTION_ID = #{functionId,jdbcType=BIGINT}
        </if>
    </sql>

    <delete id="deleteByRoleId" parameterType="long">
        DELETE FROM
        <include refid="table"/>
        WHERE ROLE_ID = #{roleId}
    </delete>

    <delete id="deleteByFunctionId" parameterType="long">
        DELETE FROM
        <include refid="table"/>
        WHERE FUNCTION_ID = #{functionId}
    </delete>

    <delete id="deleteByFunctionIdList" parameterType="list">
        DELETE FROM
        <include refid="table"/>
        WHERE FUNCTION_ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </delete>

</mapper>

