<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.AgentSaler">
    <sql id="table">tbl_agent_saler</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgentSaler">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="UPDATER" property="updater" jdbcType="VARCHAR"/>
        <result column="AGENT_NO" property="agentNo" jdbcType="VARCHAR"/>
        <result column="AGENT_TYPE" property="agentType" jdbcType="SMALLINT"/>
        <result column="SALER_ID" property="salerId" jdbcType="BIGINT"/>
        <result column="SALER_NAME" property="salerName" jdbcType="VARCHAR"/>
        <result column="SALE_DEPARTMENT_ID" property="saleDepartmentId" jdbcType="BIGINT"/>
        <result column="SALE_DEPARTMENT_NAME" property="saleDepartmentName" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="JSON_INFO" property="jsonInfo" jdbcType="OTHER"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_TIME, UPDATE_TIME, VERSION, UPDATER, AGENT_NO, AGENT_TYPE, SALER_ID, SALER_NAME, SALE_DEPARTMENT_ID, SALE_DEPARTMENT_NAME, REMARK, JSON_INFO
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.AgentSaler">
        INSERT INTO <include refid="table" /> (
            CREATE_TIME,
            UPDATE_TIME,
            VERSION,
            UPDATER,
            AGENT_NO,
            AGENT_TYPE,
            SALER_ID,
            SALER_NAME,
            SALE_DEPARTMENT_ID,
            SALE_DEPARTMENT_NAME,
            REMARK,
            JSON_INFO
        ) VALUES (
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            0,
            #{updater,jdbcType=VARCHAR},
            #{agentNo,jdbcType=VARCHAR},
            #{agentType,jdbcType=SMALLINT},
            #{salerId,jdbcType=BIGINT},
            #{salerName,jdbcType=VARCHAR},
            #{saleDepartmentId,jdbcType=BIGINT},
            #{saleDepartmentName,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR},
            #{jsonInfo,jdbcType=OTHER}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            CREATE_TIME,
            UPDATE_TIME,
            VERSION,
            UPDATER,
            AGENT_NO,
            AGENT_TYPE,
            SALER_ID,
            SALER_NAME,
            SALE_DEPARTMENT_ID,
            SALE_DEPARTMENT_NAME,
            REMARK,
            JSON_INFO
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            0,
            #{item.updater,jdbcType=VARCHAR},
            #{item.agentNo,jdbcType=VARCHAR},
            #{item.agentType,jdbcType=SMALLINT},
            #{item.salerId,jdbcType=BIGINT},
            #{item.salerName,jdbcType=VARCHAR},
            #{item.saleDepartmentId,jdbcType=BIGINT},
            #{item.saleDepartmentName,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.jsonInfo,jdbcType=OTHER}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgentSaler">
        UPDATE <include refid="table" /> SET
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            VERSION = #{version,jdbcType=SMALLINT} +1,
            UPDATER = #{updater,jdbcType=VARCHAR},
            AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            AGENT_TYPE = #{agentType,jdbcType=SMALLINT},
            SALER_ID = #{salerId,jdbcType=BIGINT},
            SALER_NAME = #{salerName,jdbcType=VARCHAR},
            SALE_DEPARTMENT_ID = #{saleDepartmentId,jdbcType=BIGINT},
            SALE_DEPARTMENT_NAME = #{saleDepartmentName,jdbcType=VARCHAR},
            REMARK = #{remark,jdbcType=VARCHAR},
            JSON_INFO = #{jsonInfo,jdbcType=OTHER}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgentSaler">
        UPDATE <include refid="table" />
        <set>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="agentType != null">
                AGENT_TYPE = #{agentType,jdbcType=SMALLINT},
            </if>
            <if test="salerId != null">
                SALER_ID = #{salerId,jdbcType=BIGINT},
            </if>
            <if test="salerName != null">
                SALER_NAME = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="saleDepartmentId != null">
                SALE_DEPARTMENT_ID = #{saleDepartmentId,jdbcType=BIGINT},
            </if>
            <if test="saleDepartmentName != null">
                SALE_DEPARTMENT_NAME = #{saleDepartmentName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="jsonInfo != null">
                JSON_INFO = #{jsonInfo,jdbcType=OTHER}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="updater != null and updater !=''">
            and UPDATER = #{updater,jdbcType=VARCHAR}
        </if>
        <if test="agentNo != null and agentNo !=''">
            and AGENT_NO = #{agentNo,jdbcType=VARCHAR}
        </if>
        <if test="agentType != null">
            and AGENT_TYPE = #{agentType,jdbcType=SMALLINT}
        </if>
        <if test="salerId != null">
            and SALER_ID = #{salerId,jdbcType=BIGINT}
        </if>
        <if test="salerName != null and salerName !=''">
            and SALER_NAME = #{salerName,jdbcType=VARCHAR}
        </if>
        <if test="saleDepartmentId != null">
            and SALE_DEPARTMENT_ID = #{saleDepartmentId,jdbcType=BIGINT}
        </if>
        <if test="saleDepartmentName != null and saleDepartmentName !=''">
            and SALE_DEPARTMENT_NAME = #{saleDepartmentName,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark !=''">
            and REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="jsonInfo != null and jsonInfo !=''">
            and JSON_INFO = #{jsonInfo,jdbcType=OTHER}
        </if>
        <if test="agentNoList != null and agentNoList.size() > 0">
            and AGENT_NO in
            <foreach collection="agentNoList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
        </if>
    </sql>

</mapper>
