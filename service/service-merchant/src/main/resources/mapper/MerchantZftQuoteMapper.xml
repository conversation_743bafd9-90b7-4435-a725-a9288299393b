<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.MerchantZftQuoteMapper">
    <sql id="table">tbl_merchant_zft_quote</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantZftQuote">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="quote_id" property="quoteId" jdbcType="BIGINT"/>
        <result column="fixed_fee" property="fixedFee" jdbcType="DECIMAL"/>
        <result column="formula_type" property="formulaType" jdbcType="SMALLINT"/>
        <result column="rate" property="rate" jdbcType="DECIMAL"/>
        <result column="can_refund" property="canRefund" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, quote_id, fixed_fee, formula_type, rate, can_refund,create_time,update_time
    </sql>


</mapper>
