<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantExpressInfo">
	<sql id="table"> tbl_merchant_express_info </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantExpressInfo">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="CONSIGNEE" property="consignee" jdbcType="VARCHAR"/>
		<result column="TELEPHONE" property="telephone" jdbcType="VARCHAR"/>
		<result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
		<result column="CITY" property="city" jdbcType="VARCHAR"/>
		<result column="COUNTY" property="county" jdbcType="VARCHAR"/>
		<result column="ADDRESS" property="address" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		VERSION,
		CREATE_TIME,
		UPDATE_TIME,
		MCH_NO,
		CONSIGNEE,
		TELEPHONE,
		PROVINCE,
		CITY,
		COUNTY,
		ADDRESS
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantExpressInfo">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	MCH_NO ,
        	CONSIGNEE ,
        	TELEPHONE ,
        	PROVINCE ,
        	CITY ,
        	COUNTY ,
        	ADDRESS 
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{mchNo,jdbcType=VARCHAR},
			#{consignee,jdbcType=VARCHAR},
			#{telephone,jdbcType=VARCHAR},
			#{province,jdbcType=VARCHAR},
			#{city,jdbcType=VARCHAR},
			#{county,jdbcType=VARCHAR},
			#{address,jdbcType=VARCHAR}
        )
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantExpressInfo">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			CONSIGNEE = #{consignee,jdbcType=VARCHAR},
			TELEPHONE = #{telephone,jdbcType=VARCHAR},
			PROVINCE = #{province,jdbcType=VARCHAR},
			CITY = #{city,jdbcType=VARCHAR},
			COUNTY = #{county,jdbcType=VARCHAR},
			ADDRESS = #{address,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="consignee != null and consignee !=''">
			and CONSIGNEE = #{consignee,jdbcType=VARCHAR}
		</if>
		<if test="telephone != null and telephone !=''">
			and TELEPHONE = #{telephone,jdbcType=VARCHAR}
		</if>
		<if test="province != null and province !=''">
			and PROVINCE = #{province,jdbcType=VARCHAR}
		</if>
		<if test="city != null and city !=''">
			and CITY = #{city,jdbcType=VARCHAR}
		</if>
		<if test="county != null and county !=''">
			and COUNTY = #{county,jdbcType=VARCHAR}
		</if>
		<if test="address != null and address !=''">
			and ADDRESS = #{address,jdbcType=VARCHAR}
		</if>
	</sql>
</mapper>

