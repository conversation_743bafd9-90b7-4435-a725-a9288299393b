<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo">
	<sql id="table"> tbl_merchant_invoice_info </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="MCH_NAME" property="mchName" jdbcType="VARCHAR"/>
		<result column="TAX_PAYER_TYPE" property="taxPayerType" jdbcType="SMALLINT"/>
		<result column="TAX_NO" property="taxNo" jdbcType="VARCHAR"/>
		<result column="REGISTER_ADDR_INFO" property="registerAddrInfo" jdbcType="VARCHAR"/>
		<result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR"/>
		<result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
		<result column="DEFAULT_INVOICE_CATEGORY_CODE" property="defaultInvoiceCategoryCode" jdbcType="VARCHAR"/>
		<result column="DEFAULT_INVOICE_CATEGORY_NAME" property="defaultInvoiceCategoryName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		VERSION,
		UPDATE_TIME,
		MCH_NO,
		MCH_NAME,
		TAX_PAYER_TYPE,
		TAX_NO,
		REGISTER_ADDR_INFO,
		ACCOUNT_NO,
		BANK_NAME,
		DEFAULT_INVOICE_CATEGORY_CODE,
		DEFAULT_INVOICE_CATEGORY_NAME
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
        	UPDATE_TIME ,
        	MCH_NO ,
        	MCH_NAME ,
        	TAX_PAYER_TYPE ,
        	TAX_NO ,
        	REGISTER_ADDR_INFO ,
        	ACCOUNT_NO ,
        	BANK_NAME ,
        	DEFAULT_INVOICE_CATEGORY_CODE ,
        	DEFAULT_INVOICE_CATEGORY_NAME 
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			0,
			#{updateTime,jdbcType=TIMESTAMP},
			#{mchNo,jdbcType=VARCHAR},
			#{mchName,jdbcType=VARCHAR},
			#{taxPayerType,jdbcType=SMALLINT},
			#{taxNo,jdbcType=VARCHAR},
			#{registerAddrInfo,jdbcType=VARCHAR},
			#{accountNo,jdbcType=VARCHAR},
			#{bankName,jdbcType=VARCHAR},
			#{defaultInvoiceCategoryCode,jdbcType=VARCHAR},
			#{defaultInvoiceCategoryName,jdbcType=VARCHAR}
        )
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			MCH_NAME = #{mchName,jdbcType=VARCHAR},
			TAX_PAYER_TYPE = #{taxPayerType,jdbcType=SMALLINT},
			TAX_NO = #{taxNo,jdbcType=VARCHAR},
			REGISTER_ADDR_INFO = #{registerAddrInfo,jdbcType=VARCHAR},
			ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
			BANK_NAME = #{bankName,jdbcType=VARCHAR},
			DEFAULT_INVOICE_CATEGORY_CODE = #{defaultInvoiceCategoryCode,jdbcType=VARCHAR},
			DEFAULT_INVOICE_CATEGORY_NAME = #{defaultInvoiceCategoryName,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		order by id desc
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="mchName != null and mchName !=''">
			and MCH_NAME = #{mchName,jdbcType=VARCHAR}
		</if>
		<if test="taxPayerType != null">
			and TAX_PAYER_TYPE = #{taxPayerType,jdbcType=SMALLINT}
		</if>
		<if test="taxNo != null and taxNo !=''">
			and TAX_NO = #{taxNo,jdbcType=VARCHAR}
		</if>
		<if test="registerAddrInfo != null and registerAddrInfo !=''">
			and REGISTER_ADDR_INFO = #{registerAddrInfo,jdbcType=VARCHAR}
		</if>
		<if test="accountNo != null and accountNo !=''">
			and ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR}
		</if>
		<if test="bankName != null and bankName !=''">
			and BANK_NAME = #{bankName,jdbcType=VARCHAR}
		</if>
		<if test="defaultInvoiceCategoryCode != null and defaultInvoiceCategoryCode !=''">
			and DEFAULT_INVOICE_CATEGORY_CODE = #{defaultInvoiceCategoryCode,jdbcType=VARCHAR}
		</if>
		<if test="defaultInvoiceCategoryName != null and defaultInvoiceCategoryName !=''">
			and DEFAULT_INVOICE_CATEGORY_NAME = #{defaultInvoiceCategoryName,jdbcType=VARCHAR}
		</if>
	</sql>
</mapper>

