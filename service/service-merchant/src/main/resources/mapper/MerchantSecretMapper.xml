<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantSecret">
	<sql id="table"> tbl_merchant_secret </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantSecret">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="SIGN_TYPE" property="signType" jdbcType="SMALLINT"/>
		<result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"/>
		<result column="PLATFORM_PUBLIC_KEY" property="platformPublicKey" jdbcType="VARCHAR"/>
		<result column="PLATFORM_PRIVATE_KEY" property="platformPrivateKey" jdbcType="VARCHAR"/>
		<result column="MERCHANT_PUBLIC_KEY" property="merchantPublicKey" jdbcType="VARCHAR"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		MODIFY_TIME,
		VERSION,
		SIGN_TYPE,
		MERCHANT_NO,
		PLATFORM_PUBLIC_KEY,
		PLATFORM_PRIVATE_KEY,
		MERCHANT_PUBLIC_KEY,
		NOTIFY_URL_TYPE,
		NOTIFY_URL,
		NOTIFY_STATUS
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantSecret">
		INSERT INTO <include refid="table" /> (
			CREATE_TIME,
			MODIFY_TIME,
			VERSION,
			SIGN_TYPE,
			MERCHANT_NO,
			PLATFORM_PUBLIC_KEY,
			PLATFORM_PRIVATE_KEY,
			MERCHANT_PUBLIC_KEY,
			NOTIFY_URL_TYPE,
			NOTIFY_URL,
			NOTIFY_STATUS
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			#{modifyTime,jdbcType=TIMESTAMP},
			0,
			#{signType,jdbcType=SMALLINT},
			#{merchantNo,jdbcType=VARCHAR},
			#{platformPublicKey,jdbcType=VARCHAR},
			#{platformPrivateKey,jdbcType=VARCHAR},
			#{merchantPublicKey,jdbcType=VARCHAR},
			#{notifyUrlType,jdbcType=VARCHAR},
			#{notifyUrl,jdbcType=VARCHAR},
			#{notifyStatus,jdbcType=SMALLINT}
        )
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantSecret">
        UPDATE <include refid="table" /> SET
			MODIFY_TIME = now(),
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			SIGN_TYPE = #{signType,jdbcType=SMALLINT},
			MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},
			PLATFORM_PUBLIC_KEY = #{platformPublicKey,jdbcType=VARCHAR},
			PLATFORM_PRIVATE_KEY = #{platformPrivateKey,jdbcType=VARCHAR},
			MERCHANT_PUBLIC_KEY = #{merchantPublicKey,jdbcType=VARCHAR},
			NOTIFY_URL_TYPE = #{notifyUrlType,jdbcType=VARCHAR},
			NOTIFY_URL = 	  #{notifyUrl,jdbcType=VARCHAR},
			NOTIFY_STATUS =   #{notifyStatus,jdbcType=SMALLINT}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="merchantNo != null and merchantNo !=''">
			and MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
		</if>
		<if test="signType != null">
			and SIGN_TYPE = #{signType,jdbcType=SMALLINT}
		</if>
	</sql>
</mapper>

