<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.AgreementTemplate">
    <sql id="table">tbl_agreement_template</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgreementTemplate">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="TOPIC" property="topic" jdbcType="VARCHAR"/>
        <result column="EXPIRE_TIME_TYPE" property="expireTimeType" jdbcType="SMALLINT"/>
        <result column="DEADLINE_TYPE" property="deadlineType" jdbcType="SMALLINT"/>
        <result column="CREATE_OPERATOR" property="createOperator" jdbcType="VARCHAR"/>
        <result column="UPDATE_OPERATOR" property="updateOperator" jdbcType="VARCHAR"/>
        <result column="SIGNER_TEMP" property="signerTemp" jdbcType="OTHER"/>
        <result column="FILE_TEMP" property="fileTemp" jdbcType="OTHER"/>
        <result column="EXT_INFO" property="extInfo" jdbcType="OTHER"/>
        <result column="FLOW_TEMPLATE_ID" property="flowTemplateId" jdbcType="VARCHAR"/>
        <result column="FILE_TEMPLATE_ID" property="fileTemplateId" jdbcType="VARCHAR"/>
        <result column="TEMPLATE_FILE_URL" property="templateFileUrl" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, TOPIC, EXPIRE_TIME_TYPE, DEADLINE_TYPE, CREATE_OPERATOR, UPDATE_OPERATOR, SIGNER_TEMP, FILE_TEMP, EXT_INFO,FLOW_TEMPLATE_ID,FILE_TEMPLATE_ID,TEMPLATE_FILE_URL
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.AgreementTemplate">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            TOPIC,
            EXPIRE_TIME_TYPE,
            DEADLINE_TYPE,
            CREATE_OPERATOR,
            UPDATE_OPERATOR,
            SIGNER_TEMP,
            FILE_TEMP,
            EXT_INFO,
            FLOW_TEMPLATE_ID,
            FILE_TEMPLATE_ID,
            TEMPLATE_FILE_URL
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{topic,jdbcType=VARCHAR},
        #{expireTimeType,jdbcType=SMALLINT},
        #{deadlineType,jdbcType=SMALLINT},
        #{createOperator,jdbcType=VARCHAR},
        #{updateOperator,jdbcType=VARCHAR},
        #{signerTemp,jdbcType=OTHER},
        #{fileTemp,jdbcType=OTHER},
        #{extInfo,jdbcType=OTHER},
        #{flowTemplateId,jdbcType=VARCHAR},
        #{fileTemplateId,jdbcType=VARCHAR},
        #{templateFileUrl,jdbcType=VARCHAR}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgreementTemplate">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=SMALLINT} +1,
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                TOPIC = #{topic,jdbcType=VARCHAR},
                EXPIRE_TIME_TYPE = #{expireTimeType,jdbcType=SMALLINT},
                DEADLINE_TYPE = #{deadlineType,jdbcType=SMALLINT},
                CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
                UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
                SIGNER_TEMP = #{signerTemp,jdbcType=OTHER},
                FILE_TEMP = #{fileTemp,jdbcType=OTHER},
                EXT_INFO = #{extInfo,jdbcType=OTHER},
                FLOW_TEMPLATE_ID = #{flowTemplateId,jdbcType=VARCHAR},
                FILE_TEMPLATE_ID = #{fileTemplateId,jdbcType=VARCHAR},
                TEMPLATE_FILE_URL = #{templateFileUrl,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgreementTemplate">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                    VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="topic != null">
                    TOPIC = #{topic,jdbcType=VARCHAR},
            </if>
            <if test="expireTimeType != null">
                    EXPIRE_TIME_TYPE = #{expireTimeType,jdbcType=SMALLINT},
            </if>
            <if test="deadlineType != null">
                    DEADLINE_TYPE = #{deadlineType,jdbcType=SMALLINT},
            </if>
            <if test="createOperator != null">
                    CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
            </if>
            <if test="updateOperator != null">
                    UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
            </if>
            <if test="signerTemp != null">
                    SIGNER_TEMP = #{signerTemp,jdbcType=OTHER},
            </if>
            <if test="fileTemp != null">
                    FILE_TEMP = #{fileTemp,jdbcType=OTHER},
            </if>
            <if test="extInfo != null">
                    EXT_INFO = #{extInfo,jdbcType=OTHER}
            </if>
            <if test="flowTemplateId != null">
                    FLOW_TEMPLATE_ID = #{flowTemplateId,jdbcType=VARCHAR}
            </if>
            <if test="fileTemplateId != null">
                    FILE_TEMPLATE_ID = #{fileTemplateId,jdbcType=VARCHAR}
            </if>
            <if test="templateFileUrl != null">
                    TEMPLATE_FILE_URL = #{templateFileUrl,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=SMALLINT}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="topic != null and topic !=''">
                and TOPIC =  #{topic,jdbcType=VARCHAR}
            </if>
            <if test="topicLike != null and topicLike !=''">
                and TOPIC  like concat('%',#{topicLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="expireTimeType != null">
                and EXPIRE_TIME_TYPE = #{expireTimeType,jdbcType=SMALLINT}
            </if>
            <if test="deadlineType != null">
                and DEADLINE_TYPE = #{deadlineType,jdbcType=SMALLINT}
            </if>
            <if test="createOperator != null and createOperator !=''">
                and CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
            </if>
            <if test="updateOperator != null and updateOperator !=''">
                and UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
            </if>
            <if test="signerTemp != null and signerTemp !=''">
                and SIGNER_TEMP = #{signerTemp,jdbcType=OTHER}
            </if>
            <if test="fileTemp != null and fileTemp !=''">
                and FILE_TEMP = #{fileTemp,jdbcType=OTHER}
            </if>
            <if test="extInfo != null and extInfo !=''">
                and EXT_INFO = #{extInfo,jdbcType=OTHER}
            </if>
            <if test="flowTemplateId != null and flowTemplateId != ''">
                and FLOW_TEMPLATE_ID = #{flowTemplateId,jdbcType=VARCHAR}
            </if>
            <if test="fileTemplateId != null and fileTemplateId != ''">
                and FILE_TEMPLATE_ID = #{fileTemplateId,jdbcType=VARCHAR}
            </if>
            <if test="templateFileUrl != null and templateFileUrl != null">
                and TEMPLATE_FILE_URL = #{templateFileUrl,jdbcType=VARCHAR}
            </if>

            <!-- 以下自定义 -->
            <if test="signerNoList != null and signerNoList.size()>0">
                AND JSON_CONTAINS(SIGNER_TEMP -> '$[*].signerNo',
                    <foreach item="item" index="index" collection="signerNoList" open="'[" separator="," close="]'">"${item}"</foreach>
                )
            </if>
    </sql>

    <delete id="deleteAll">
        delete from <include refid="table"/>
    </delete>

</mapper>
