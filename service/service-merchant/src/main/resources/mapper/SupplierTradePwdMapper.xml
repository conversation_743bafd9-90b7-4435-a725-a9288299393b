<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd">
	<sql id="table"> tbl_supplier_trade_pwd </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="SMALLINT"/>
		<result column="is_init_pwd" property="isInitPwd" jdbcType="SMALLINT"/>
		<result column="pwd" property="pwd" jdbcType="VARCHAR"/>
		<result column="pwd_error_count" property="pwdErrorCount" jdbcType="SMALLINT"/>
		<result column="pwd_error_time" property="pwdErrorTime" jdbcType="TIMESTAMP"/>
		<result column="extra_info" property="extraInfo" jdbcType="OTHER"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		update_time,
		mch_no,
		status,
		is_init_pwd,
		pwd,
		pwd_error_count,
		pwd_error_time,
		extra_info
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	mch_no ,
        	status ,
        	is_init_pwd ,
        	pwd ,
        	pwd_error_count ,
        	pwd_error_time ,
        	extra_info 
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{mchNo,jdbcType=VARCHAR},
			#{status,jdbcType=SMALLINT},
			#{isInitPwd,jdbcType=SMALLINT},
			#{pwd,jdbcType=VARCHAR},
			#{pwdErrorCount,jdbcType=SMALLINT},
			#{pwdErrorTime,jdbcType=TIMESTAMP},
			#{extraInfo,jdbcType=OTHER}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	mch_no ,
        	status ,
        	is_init_pwd ,
        	pwd ,
        	pwd_error_count ,
        	pwd_error_time ,
        	extra_info 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.mchNo,jdbcType=VARCHAR},
			#{item.status,jdbcType=SMALLINT},
			#{item.isInitPwd,jdbcType=SMALLINT},
			#{item.pwd,jdbcType=VARCHAR},
			#{item.pwdErrorCount,jdbcType=SMALLINT},
			#{item.pwdErrorTime,jdbcType=TIMESTAMP},
			#{item.extraInfo,jdbcType=OTHER}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			mch_no = #{mchNo,jdbcType=VARCHAR},
			status = #{status,jdbcType=SMALLINT},
			is_init_pwd = #{isInitPwd,jdbcType=SMALLINT},
			pwd = #{pwd,jdbcType=VARCHAR},
			pwd_error_count = #{pwdErrorCount,jdbcType=SMALLINT},
			pwd_error_time = #{pwdErrorTime,jdbcType=TIMESTAMP},
			extra_info = #{extraInfo,jdbcType=OTHER}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="mchNo != null">
				mch_no =#{mchNo,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status =#{status,jdbcType=SMALLINT},
			</if>
			<if test="isInitPwd != null">
				is_init_pwd =#{isInitPwd,jdbcType=SMALLINT},
			</if>
			<if test="pwd != null">
				pwd =#{pwd,jdbcType=VARCHAR},
			</if>
			<if test="pwdErrorCount != null">
				pwd_error_count =#{pwdErrorCount,jdbcType=SMALLINT},
			</if>
			<if test="pwdErrorTime != null">
				pwd_error_time =#{pwdErrorTime,jdbcType=TIMESTAMP},
			</if>
			<if test="extraInfo != null">
				extra_info =#{extraInfo,jdbcType=OTHER},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE id = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and update_time = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="mchNo != null">
			and mch_no = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="status != null">
			and status = #{status,jdbcType=SMALLINT}
		</if>
		<if test="isInitPwd != null">
			and is_init_pwd = #{isInitPwd,jdbcType=SMALLINT}
		</if>
		<if test="pwd != null">
			and pwd = #{pwd,jdbcType=VARCHAR}
		</if>
		<if test="pwdErrorCount != null">
			and pwd_error_count = #{pwdErrorCount,jdbcType=SMALLINT}
		</if>
		<if test="pwdErrorTime != null">
			and pwd_error_time = #{pwdErrorTime,jdbcType=TIMESTAMP}
		</if>
		<if test="extraInfo != null">
			and extra_info = #{extraInfo,jdbcType=OTHER}
		</if>
	</sql>

	<!-- 根据商户号查询 -->
	<select id="getByMchNo"	resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE mch_no = #{mchNo,jdbcType=VARCHAR} limit 1
	</select>
</mapper>

