<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition">
	<sql id="table"> tbl_merchant_employer_position </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="WORKPLACE_CODE" property="workplaceCode" jdbcType="VARCHAR"/>
		<result column="WORK_CATEGORY_CODE" property="workCategoryCode" jdbcType="VARCHAR"/>
		<result column="WORK_CATEGORY_NAME" property="workCategoryName" jdbcType="VARCHAR"/>
		<result column="BUSINESS_DESC" property="businessDesc" jdbcType="VARCHAR"/>
		<result column="SERVICE_DESC" property="serviceDesc" jdbcType="VARCHAR"/>
		<result column="CHARGE_RULE_DESC" property="chargeRuleDesc" jdbcType="VARCHAR"/>
		<result column="EXT_JSON" property="extJson" jdbcType="VARCHAR"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		UPDATE_TIME,
		VERSION,
		UPDATOR,
		MCH_NO,
		WORKPLACE_CODE,
		WORK_CATEGORY_CODE,
		WORK_CATEGORY_NAME,
		BUSINESS_DESC,
		SERVICE_DESC,
		CHARGE_RULE_DESC,
		EXT_JSON
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
        	WORKPLACE_CODE ,
        	WORK_CATEGORY_CODE ,
        	WORK_CATEGORY_NAME ,
			BUSINESS_DESC,
        	SERVICE_DESC ,
        	CHARGE_RULE_DESC ,
        	EXT_JSON
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			0,
			#{updator,jdbcType=VARCHAR},
			#{mchNo,jdbcType=VARCHAR},
			#{workplaceCode,jdbcType=VARCHAR},
			#{workCategoryCode,jdbcType=VARCHAR},
			#{workCategoryName,jdbcType=VARCHAR},
		    #{businessDesc,jdbcType=VARCHAR},
			#{serviceDesc,jdbcType=VARCHAR},
			#{chargeRuleDesc,jdbcType=VARCHAR},
			#{extJson,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
        	WORKPLACE_CODE ,
        	WORK_CATEGORY_CODE ,
        	WORK_CATEGORY_NAME ,
		    BUSINESS_DESC ,
        	SERVICE_DESC ,
        	CHARGE_RULE_DESC ,
        	EXT_JSON
        ) VALUES
		<foreach collection="list" item="item" separator=",">
			(
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			0,
			#{item.updator,jdbcType=VARCHAR},
			#{item.mchNo,jdbcType=VARCHAR},
			#{item.workplaceCode,jdbcType=VARCHAR},
			#{item.workCategoryCode,jdbcType=VARCHAR},
			#{item.workCategoryName,jdbcType=VARCHAR},
		    #{item.businessDesc,jdbcType=VARCHAR},
			#{item.serviceDesc,jdbcType=VARCHAR},
			#{item.chargeRuleDesc,jdbcType=VARCHAR},
			#{item.extJson,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			WORKPLACE_CODE = #{workplaceCode,jdbcType=VARCHAR},
			WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR},
			WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR},
            BUSINESS_DESC = #{businessDesc,jdbcType=VARCHAR},
			SERVICE_DESC = #{serviceDesc,jdbcType=VARCHAR},
			CHARGE_RULE_DESC = #{chargeRuleDesc,jdbcType=VARCHAR},
			EXT_JSON = #{extJson,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE ID = #{id,jdbcType=BIGINT}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!--条件删除-->
	<delete id="deleteBy" parameterType="java.util.Map">
		DELETE FROM <include refid="table" />
		where
		<trim  prefixOverrides="AND">
			<include refid="condition_sql" />
		</trim>
	</delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="workplaceCode != null">
			and WORKPLACE_CODE = #{workplaceCode,jdbcType=VARCHAR}
		</if>
		<if test="workCategoryCode != null">
			and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
		</if>
		<if test="workCategoryName != null and workCategoryName !=''">
			and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
		</if>
		<if test="businessDesc != null and businessDesc !=''">
			and BUSINESS_DESC = #{businessDesc,jdbcType=VARCHAR}
		</if>
		<if test="serviceDesc != null and serviceDesc !=''">
			and SERVICE_DESC = #{serviceDesc,jdbcType=VARCHAR}
		</if>
		<if test="chargeRuleDesc != null and chargeRuleDesc !=''">
			and CHARGE_RULE_DESC = #{chargeRuleDesc,jdbcType=VARCHAR}
		</if>
	</sql>

	<select id="listByMchNoWithQuote" parameterType="java.util.Map" resultMap="BaseResultMap">
		select c.* from tbl_merchant_employer_quote a,
		tbl_merchant_employer_quote_position b,
		tbl_merchant_employer_position c
		where a.ID = b.quote_id and b.position_id = c.ID
		<if test="mchNo != null and mchNo != ''">
			and a.mch_no = #{mchNo}
		</if>
		<if test="mainstayNo != null and mainstayNo != ''">
			and a.MAINSTAY_MCH_NO = #{mainstayNo}
		</if>
		<if test="quoteId != null">
			and a.id = #{quoteId}
		</if>
		<if test="status != null">
			and a.status = #{status}
		</if>
		<if test="workCategoryCode != null and workCategoryCode != ''">
			and c.WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
		</if>
		group by c.id
	</select>

	<select id="listByMchNoWithQuoteWithoutGroup" parameterType="java.util.Map" resultMap="BaseResultMap">
		select c.* from tbl_merchant_employer_quote a,
		tbl_merchant_employer_quote_position b,
		tbl_merchant_employer_position c
		where a.ID = b.quote_id and b.position_id = c.ID
		<if test="mchNo != null and mchNo != ''">
			and a.mch_no = #{mchNo}
		</if>
		<if test="mainstayNo != null and mainstayNo != ''">
			and a.MAINSTAY_MCH_NO = #{mainstayNo}
		</if>
		<if test="status != null">
			and a.status = #{status}
		</if>
		<if test="productNo != null and productNo != ''">
			and a.product_no = #{productNo}
		</if>
		order by a.ID asc
	</select>

	<select id="joinWithQuoteId" parameterType="java.util.Map" resultMap="BaseResultMap">
		select a.* from tbl_merchant_employer_position a,tbl_merchant_employer_quote_position b where a.ID = b.position_id
		<if test="quoteId != null">
			and b.quote_id = #{quoteId}
		</if>
		<if test="mchNo != null and mchNo != ''">
			and a.mch_no = #{mchNo}
		</if>
	</select>
</mapper>

