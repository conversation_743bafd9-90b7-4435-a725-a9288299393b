<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.AgreementFile">
    <sql id="table">tbl_agreement_file</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgreementFile">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="AGREEMENT_ID" property="agreementId" jdbcType="BIGINT"/>
        <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR"/>
        <result column="TYPE" property="type" jdbcType="SMALLINT"/>
        <result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AGREEMENT_ID, FILE_NAME, TYPE, FILE_URL
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.AgreementFile">
        INSERT INTO <include refid="table" /> (
            AGREEMENT_ID,
            FILE_NAME,
            TYPE,
            FILE_URL
        ) VALUES (
        #{agreementId,jdbcType=BIGINT},
        #{fileName,jdbcType=VARCHAR},
        #{type,jdbcType=SMALLINT},
        #{fileUrl,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO <include refid="table" /> (
        AGREEMENT_ID,
        FILE_NAME,
        TYPE,
        FILE_URL
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.agreementId,jdbcType=BIGINT},
            #{item.fileName,jdbcType=VARCHAR},
            #{item.type,jdbcType=SMALLINT},
            #{item.fileUrl,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgreementFile">
        UPDATE <include refid="table" /> SET
                AGREEMENT_ID = #{agreementId,jdbcType=BIGINT},
                FILE_NAME = #{fileName,jdbcType=VARCHAR},
                TYPE = #{type,jdbcType=SMALLINT},
                FILE_URL = #{fileUrl,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgreementFile">
        UPDATE <include refid="table" />
        <set>
            <if test="agreementId != null">
                AGREEMENT_ID = #{agreementId,jdbcType=BIGINT},
            </if>
            <if test="fileName != null">
                FILE_NAME = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                    TYPE = #{type,jdbcType=SMALLINT},
            </if>
            <if test="fileUrl != null">
                    FILE_URL = #{fileUrl,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <select id="deleteByAgreementId" parameterType="java.util.Map" resultType="int">
        DELETE FROM
        <include refid="table" />
        where AGREEMENT_ID = #{agreementId}
    </select>


    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="agreementId != null">
                and AGREEMENT_ID = #{agreementId,jdbcType=BIGINT}
            </if>
            <if test="fileName != null and fileName !=''">
                and FILE_NAME = #{fileName,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TYPE = #{type,jdbcType=SMALLINT}
            </if>
            <if test="fileUrl != null and fileUrl !=''">
                and FILE_URL = #{fileUrl,jdbcType=VARCHAR}
            </if>
            <if test="agreementIdList != null and agreementIdList.size() > 0">
                AND AGREEMENT_ID IN
                <foreach collection="agreementIdList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
            </if>
    </sql>

</mapper>
