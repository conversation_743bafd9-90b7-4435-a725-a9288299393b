<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.Merchant">
	<sql id="table"> tbl_merchant </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.Merchant">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="ACTIVE_TIME" property="activeTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="MCH_NAME" property="mchName" jdbcType="VARCHAR"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="MCH_STATUS" property="mchStatus" jdbcType="SMALLINT"/>
		<result column="DEAL_STATUS" property="dealStatus" jdbcType="SMALLINT"/>
		<result column="AUTH_STATUS" property="authStatus" jdbcType="SMALLINT"/>
		<result column="MERCHANT_TYPE" property="merchantType" jdbcType="SMALLINT"/>
		<result column="AGENT_NO" property="agentNo" jdbcType="VARCHAR"/>
		<result column="AGENT_NAME" property="agentName" jdbcType="VARCHAR"/>
		<result column="CONTACT_PHONE" property="contactPhone" jdbcType="VARCHAR"/>
		<result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR"/>
		<result column="CONTACT_EMAIL" property="contactEmail" jdbcType="VARCHAR"/>
		<result column="SERVICE_PHONE" property="servicePhone" jdbcType="VARCHAR"/>
		<result column="REMARK" property="remark" jdbcType="VARCHAR"/>
		<result column="JSON_INFO" property="jsonInfo" jdbcType="OTHER"/>
		<result column="TEMPLATE_ID" property="templateId" jdbcType="VARCHAR" />
		<result column="FOUNDER" property="founder" jdbcType="VARCHAR"/>
		<result column="BRANCH_NAME" property="branchName" jdbcType="VARCHAR"/>
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		UPDATE_TIME,
		ACTIVE_TIME,
		VERSION,
		UPDATOR,
		MCH_NAME,
		MCH_NO,
		MCH_STATUS,
		DEAL_STATUS,
		AUTH_STATUS,
		MERCHANT_TYPE,
		AGENT_NO,
		AGENT_NAME,
		CONTACT_PHONE,
		CONTACT_NAME,
		CONTACT_EMAIL,
		SERVICE_PHONE,
		REMARK,
		JSON_INFO,
		TEMPLATE_ID,
		FOUNDER,
		BRANCH_NAME,
		BUSINESS_TYPE
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.Merchant">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	ACTIVE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NAME ,
        	MCH_NO ,
        	MCH_STATUS ,
        	DEAL_STATUS,
			AUTH_STATUS ,
        	MERCHANT_TYPE ,
			AGENT_NO,
			AGENT_NAME,
			CONTACT_PHONE,
			CONTACT_NAME,
			CONTACT_EMAIL,
			SERVICE_PHONE,
        	REMARK ,
        	JSON_INFO,
			FOUNDER,
			BRANCH_NAME,
			BUSINESS_TYPE
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{activeTime,jdbcType=TIMESTAMP},
			0,
			#{updator,jdbcType=VARCHAR},
			#{mchName,jdbcType=VARCHAR},
			#{mchNo,jdbcType=VARCHAR},
			#{mchStatus,jdbcType=SMALLINT},
			#{dealStatus,jdbcType=SMALLINT},
			#{authStatus,jdbcType=SMALLINT},
			#{merchantType,jdbcType=SMALLINT},
			#{agentNo,jdbcType=VARCHAR},
			#{agentName,jdbcType=VARCHAR},
			#{contactPhone,jdbcType=VARCHAR},
			#{contactName,jdbcType=VARCHAR},
			#{contactEmail,jdbcType=VARCHAR},
			#{servicePhone,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{jsonInfo,jdbcType=OTHER},
			#{founder,jdbcType=VARCHAR},
			#{branchName,jdbcType=VARCHAR},
			#{businessType,jdbcType=VARCHAR}
		)
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.Merchant">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			ACTIVE_TIME = #{activeTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			MCH_NAME = #{mchName,jdbcType=VARCHAR},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			MCH_STATUS = #{mchStatus,jdbcType=SMALLINT},
			DEAL_STATUS = #{dealStatus,jdbcType=SMALLINT},
			AUTH_STATUS = #{authStatus,jdbcType=SMALLINT},
			MERCHANT_TYPE = #{merchantType,jdbcType=SMALLINT},
			AGENT_NO = #{agentNo,jdbcType=VARCHAR},
			AGENT_NAME = #{agentName,jdbcType=VARCHAR},
			CONTACT_PHONE =	#{contactPhone,jdbcType=VARCHAR},
			CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
			CONTACT_EMAIL = #{contactEmail,jdbcType=VARCHAR},
			SERVICE_PHONE = #{servicePhone,jdbcType=VARCHAR},
			REMARK = #{remark,jdbcType=VARCHAR},
			BRANCH_NAME = #{branchName,jdbcType=VARCHAR},
			JSON_INFO = #{jsonInfo,jdbcType=OTHER},
			BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" /> as m
		<where>
			<include refid="condition_sql" />
		</where>
		order by m.id desc
	</select>

	<select id="listMerchantNo" parameterType="java.util.Map" resultType="String">
		select MCH_NO from
		<include refid="table" />
		<where>
			MERCHANT_TYPE = 100
			<if test="existAgent == true">
				and AGENT_NO is not null
			</if>
			<if test="existAgent == false">
				and AGENT_NO is null
			</if>
		</where>

	</select>

	<resultMap id="branchMap" type="com.zhixianghui.facade.merchant.vo.merchant.BranchVo">
		<id property="branchName" column="branchName" jdbcType="VARCHAR"/>
		<collection property="merchant" javaType="list"  ofType="com.zhixianghui.facade.merchant.vo.MerchantInfoVo">
			<result property="mchName" column="mchName" jdbcType="VARCHAR"/>
			<result property="mchNo" column="mchNo" jdbcType="VARCHAR"/>
		</collection>
	</resultMap>

	<select id="listByBranch" parameterType="java.util.Map" resultMap="branchMap">
		select
		m.MCH_NAME as mchName,
		m.MCH_NO as mchNo,
		m.branch_name as branchName
		from
		tbl_merchant m
		left join tbl_merchant_saler s on s.mch_no = m.mch_no
		<where>
			m.branch_name is not null
			<include refid="condition_sql" />
		</where>
		order by m.id desc
	</select>

	<select id="listByBranchEqual" parameterType="java.util.Map" resultType="java.util.Map">
		select
		m.MCH_NAME as mchName,
		m.MCH_NO as mchNo,
		m.branch_name as branchName
		from
		tbl_merchant m
		left join tbl_merchant_saler s on s.mch_no = m.mch_no
		<where>
			m.branch_name is not null
			<if test="branchName != null and branchName != ''">
				and m.BRANCH_NAME  = #{branchName,jdbcType=VARCHAR}
			</if>
			<if test="mchNos != null and mchNos.size() > 0">
				and m.MCH_NO in
				<foreach collection="mchNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
			</if>
			<if test="salerId != null">
				and s.SALER_ID = #{salerId,jdbcType=BIGINT}
			</if>
		</where>
		order by m.id desc
	</select>

    <!-- 分页查询 -->
    <select id="listExtBy" parameterType="java.util.Map" resultType="com.zhixianghui.facade.merchant.vo.MerchantInfoVo">
        select
        m.MCH_NAME as mchName,
        m.MCH_NO as mchNo,
        m.MERCHANT_TYPE as merchantType,
		m.AGENT_NO as agentNo,
		m.AGENT_NAME as agentName,
        m.MCH_STATUS as mchStatus,
        m.DEAL_STATUS as dealStatus,
        m.AUTH_STATUS as authStatus,
        m.CREATE_TIME as createTime,
        m.ACTIVE_TIME as activeTime,
		m.CONTACT_PHONE as contactPhone,
		m.CONTACT_NAME as contactName,
		m.BRANCH_NAME as branchName,
		m.remark as remark,
		m.BUSINESS_TYPE as businessType,
		t.REGISTER_ADDR_PROVINCE as registerAddrProvince,
		t.REGISTER_ADDR_CITY as registerAddrCity,
		t.REGISTER_ADDR_TOWN as registerAddrTown,
		t.REGISTER_ADDR_DETAIL as registerAddrDetail,
		IFNULL(a.quoteCount,0) as quoteCount,
		m.JSON_INFO->'$.signAccountStatus' as signAccountStatus,
        s.SALER_ID as salerId,
        s.SALER_NAME as salerName,
        s.SALE_DEPARTMENT_ID as saleDepartmentId,
        s.SALE_DEPARTMENT_NAME as saleDepartmentName
        from
        tbl_merchant m
        left join tbl_merchant_saler s on s.mch_no = m.mch_no
        left join tbl_merchant_employer_main t on m.mch_no = t.mch_no
		left join (select mch_no,count(1) as quoteCount FROM tbl_merchant_employer_quote eq where eq.status = 100 GROUP BY eq.MCH_NO) a on m.MCH_NO = a.mch_no
		<if test="quoteMainstayNo !=null and quoteMainstayNo !=''">
			left join tbl_merchant_employer_quote q on q.MCH_NO = m.MCH_NO
		</if>
		<where>
            <include refid="condition_sql" />
        </where>
		order by m.id desc
    </select>

	<!-- 分页查询 -->
	<select id="supplierMerchantListPage" parameterType="java.util.Map" resultType="com.zhixianghui.facade.merchant.vo.MerchantSupplierInfoVo">
		select
		m.id as id,
		m.MCH_NAME as mchName,
		m.MCH_NO as mchNo,
		m.MERCHANT_TYPE as merchantType,
		m.MCH_STATUS as mchStatus,
		m.DEAL_STATUS as dealStatus,
		m.AUTH_STATUS as authStatus,
		m.CREATE_TIME as createTime,
		m.ACTIVE_TIME as activeTime,
		m.JSON_INFO->'$.signAccountStatus' as signAccountStatus
		from
		tbl_merchant m
		<where>
			<include refid="condition_sql" />
		</where>
		order by m.id desc
	</select>

	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" /> as m
		<where>
			<include refid="condition_sql" />
		</where>
		order by m.id desc
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
        tbl_merchant m
        left join tbl_merchant_saler s on s.mch_no = m.mch_no
		<if test="quoteMainstayNo !=null and quoteMainstayNo !=''">
			left join tbl_merchant_employer_quote q on q.MCH_NO = m.MCH_NO
		</if>
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<select id="selectDateActiveMch" parameterType="java.util.Map" resultType="long">
		select count(m.ID) from tbl_merchant m left join tbl_merchant_saler s
		on m.MCH_NO = s.MCH_NO
		<where>
			<if test="merchantType != null">
				and m.MERCHANT_TYPE = #{merchantType}
			</if>
			<if test="authStatus != null">
				and m.AUTH_STATUS = #{authStatus}
			</if>
			<if test="completeBeginDate != null and completeEndDate != null ">
				and m.ACTIVE_TIME between #{completeBeginDate} and #{completeEndDate}
			</if>
			<if test="salerIds != null and salerIds.size() > 0">
				and s.SALER_ID in
				<foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
			</if>
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" /> as m
		WHERE m.ID = #{id,jdbcType=BIGINT}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!--条件删除-->
	<delete id="deleteBy" parameterType="java.util.Map">
		DELETE FROM <include refid="table" />
		where
		<trim  prefixOverrides="AND">
			<if test="mchNo != null and mchNo !=''">
				and MCH_NO = #{mchNo,jdbcType=VARCHAR}
			</if>
		</trim>
	</delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and m.ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="maxId != null">
			and m.id <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
		</if>
        <if test="createTimeBegin !=null and createTimeBegin!=''">
            AND m.CREATE_TIME >= #{createTimeBegin,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeEnd !=null and createTimeEnd!=''">
            AND m.CREATE_TIME <![CDATA[<=]]> #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
		<if test="mchName != null and mchName !=''">
			and m.MCH_NAME = #{mchName,jdbcType=VARCHAR}
		</if>
        <if test="mchNameLike != null and mchNameLike !=''">
            and m.MCH_NAME like CONCAT(CONCAT('%', #{mchNameLike,jdbcType=VARCHAR}), '%')
        </if>
		<if test="mchNo != null and mchNo !=''">
			and m.MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="mchNos != null and mchNos.size() > 0">
			and m.MCH_NO in
			<foreach collection="mchNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
		</if>
		<if test="mchStatus != null">
			and m.MCH_STATUS = #{mchStatus,jdbcType=SMALLINT}
		</if>
		<if test="mchStatusList != null and mchStatusList.size() > 0">
			and m.MCH_STATUS in
			<foreach collection="mchStatusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=SMALLINT}</foreach>
		</if>
		<if test="dealStatus != null">
			and m.DEAL_STATUS = #{dealStatus,jdbcType=SMALLINT}
		</if>
		<if test="authStatus != null">
			and m.AUTH_STATUS = #{authStatus,jdbcType=SMALLINT}
		</if>
		<if test="merchantType != null">
			and m.MERCHANT_TYPE = #{merchantType,jdbcType=SMALLINT}
		</if>
		<if test="agentNo != null and agentNo !=''">
			and m.AGENT_NO = #{agentNo,jdbcType=VARCHAR}
		</if>
		<if test="agentName != null and agentName !=''">
			and m.AGENT_NAME = #{agentName,jdbcType=VARCHAR}
		</if>
		<if test="agentNameLike != null and agentNameLike !=''">
			and m.AGENT_NAME like CONCAT('%', #{agentNameLike,jdbcType=VARCHAR}, '%')
		</if>
		<if test="templateId != null">
			and TEMPLATE_ID is null
		</if>
		<if test="departmentId != null">
			and s.SALE_DEPARTMENT_ID = #{departmentId, jdbcType=BIGINT}
		</if>
		<if test="agentNos != null and agentNos.size() > 0">
			and m.AGENT_NO in
			<foreach collection="agentNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
		</if>
        <if test="salerIds != null and salerIds.size() > 0">
            and s.SALER_ID in
            <foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
		<if test="salerId != null">
			and s.SALER_ID = #{salerId,jdbcType=BIGINT}
		</if>
		<if test="supplierNo != null and supplierNo != ''">
			and IFNULL(meq.SUPPLIER_NO,#{supplierNo,jdbcType=VARCHAR}) = #{supplierNo,jdbcType=VARCHAR}
		</if>
		<if test="branchName != null and branchName != ''">
			and m.BRANCH_NAME  like CONCAT(CONCAT('%', #{branchName,jdbcType=VARCHAR}), '%')
		</if>
		<if test="remark !=null and remark != ''">
			and m.REMARK like CONCAT(CONCAT('%', #{remark,jdbcType=VARCHAR}), '%')
		</if>
		<if test="quoteMainstayNo !=null and quoteMainstayNo !=''">
			and meq.MAINSTAY_MCH_NO = #{quoteMainstayNo,jdbcType=VARCHAR}
		</if>
		<if test="businessType != null and businessType != ''">
			and find_in_set(#{businessType,jdbcType=VARCHAR},m.BUSINESS_TYPE)
		</if>
	</sql>

	<update id="clearAsAgent" parameterType="string">
		UPDATE <include refid="table"/>
		SET
			AGENT_NO =null,
			AGENT_NAME =null,
			VERSION = VERSION + 1
		WHERE AGENT_NO = #{agentNo,jdbcType=BIGINT}
	</update>

	<update id="delTemplateId" parameterType="java.util.Map">
		UPDATE <include refid="table"/> SET
		TEMPLATE_ID = ""
		WHERE TEMPLATE_ID = #{templateId, jdbcType=VARCHAR}
	</update>

	<update id="updateTemplateId" parameterType="java.util.Map">
		UPDATE <include refid="table"/> SET
			TEMPLATE_ID = #{templateId, jdbcType=VARCHAR}
		WHERE
		<if test="mchNoList != null and mchNoList.size() > 0">
			MCH_NO in
			<foreach collection="mchNoList" item="item" index="index" open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
	</update>

	<select id="listMerchantByTemplateId" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
			MCH_NAME,MCH_NO,MERCHANT_TYPE
		from
			<include refid="table" />
		where
			TEMPLATE_ID = #{templateId, jdbcType=VARCHAR}
			and MERCHANT_TYPE = #{merchantType, jdbcType=VARCHAR}
		order by ID desc
	</select>

	<select id="listMerchantAllMessage" parameterType="java.util.Map" resultMap="allMessageResultMap">
		select
		<include refid="ALL_MESSAGE_COLUMN" />
		from tbl_merchant m
		left join tbl_merchant_employer_cooperate mec on m.MCH_NO = mec.MCH_NO
		left join tbl_merchant_employer_main mem on m.MCH_NO = mem.MCH_NO
		left join tbl_merchant_bank_account mba on m.MCH_NO = mba.MCH_NO
		left join tbl_merchant_invoice_info mii on m.MCH_NO = mii.MCH_NO
		left join tbl_merchant_file mf on m.MCH_NO = mf.MCH_NO
		left join tbl_merchant_employer_position mep on m.MCH_NO = mep.MCH_NO
		left join tbl_merchant_saler s on m.MCH_NO = s.mch_no
		left join tbl_merchant_employer_quote meq on m.MCH_NO = meq.MCH_NO
		left join tbl_merchant_employer_quote_rate meqr on meq.ID = meqr.QUOTE_ID
		<where>
			<include refid="condition_sql" />
		</where>
		order by m.id desc
	</select>

	<resultMap id="allMessageResultMap" type="com.zhixianghui.facade.merchant.vo.ExportMerchantInfo" autoMapping="true">
		<id column="merchant.mchNo" property="merchant.mchNo"/>
		<collection property="position" ofType="com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition"
					javaType="java.util.ArrayList">
			<result column="workplaceCode" property="workplaceCode"/>
			<result column="workCategoryCode" property="workCategoryCode"/>
			<result column="workCategoryName" property="workCategoryName"/>
			<result column="serviceDesc" property="serviceDesc"/>
			<result column="chargeRuleDesc" property="chargeRuleDesc"/>
			<result column="extJson" property="extJson"/>
		</collection>

		<collection property="merchantEmployerQuoteList" ofType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote"
					javaType="java.util.ArrayList">
			<id column="quoteId" property="quoteId"/>
			<result column="mainstayMchNo" property="mainstayMchNo"/>
			<result column="mainstayMchName" property="mainstayMchName"/>
			<result column="productName" property="productName"/>
			<result column="meqStatus" property="status"/>
			<collection property="quoteRateList" ofType="com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate">
				<result column="formulaType" property="formulaType"/>
				<result column="fixedFee" property="fixedFee"/>
				<result column="rate" property="rate"/>
			</collection>
		</collection>

		<collection property="merchantFileList" ofType="com.zhixianghui.facade.merchant.entity.MerchantFile"
					javaType="java.util.ArrayList">
			<result column="fileType" property="fileType"/>
			<result column="fileUrl" property="fileUrl"/>
		</collection>
	</resultMap>

	<sql id="ALL_MESSAGE_COLUMN">
		m.mch_no as "merchant.mchNo",
		m.mch_name as "merchant.mchName",
		m.merchant_type as "merchant.merchantType",
		m.mch_status as "merchant.mchStatus",
		m.deal_status as "merchant.dealStatus",
		m.auth_status as "merchant.authStatus",
		m.contact_phone as "merchant.contactPhone",
		m.contact_name as "merchant.contactName",
		m.remark as "merchant.remark",
		m.contact_email as "merchant.contactEmail",
		m.service_phone as "merchant.servicePhone",

		s.SALER_ID as "saler.salerId",

		mec.WORKER_NUM as "cooperate.workerNum",
		mec.SIGN_RATE_LEVEL as "cooperate.signRateLevel",
		mec.WORKER_MONTH_INCOME_RATE as "cooperate.workerMonthIncomeRate",
		mec.MONTH_MONEY_SLIP as "cooperate.monthMoneySlip",
		mec.PROVIDE_INCOME_DETAIL_TYPE as "cooperate.provideIncomeDetailType",
		mec.COMPANY_WEBSITE as "cooperate.companyWebsite",
		mec.BIZ_PLATFORM_NAME as "cooperate.bizPlatformName",
		mec.INDUSTRY_TYPE_CODE as "cooperate.industryTypeCode",
		mec.INDUSTRY_TYPE_NAME as "cooperate.industryTypeName",

		mem.SHORT_NAME as "main.shortName",
		mem.TAX_NO as "main.taxNo",
		mem.REGISTER_ADDR_PROVINCE as "main.registerAddrProvince",
		mem.REGISTER_ADDR_CITY as "main.registerAddrCity",
		mem.REGISTER_ADDR_TOWN as "main.registerAddrTown",
		mem.REGISTER_ADDR_DETAIL as "main.registerAddrDetail",
		mem.REGISTER_AMOUNT as "main.registerAmount",
		mem.MANAGEMENT_SCOPE as "main.managementScope",
		mem.MANAGEMENT_TERM_BEGIN as "main.managementTermBegin",
		mem.MANAGEMENT_TERM_END as "main.managementTermEnd",
		mem.MANAGEMENT_VALIDITY_DATE_TYPE as "main.managementValidityDateType",
		mem.CERTIFICATE_TYPE as "main.certificateType",
		mem.LEGAL_PERSON_NAME as "main.legalPersonName",
		mem.CERTIFICATE_NUMBER as "main.certificateNumber",
		mem.CERTIFICATE_TERM_BEGIN as "main.certificateTermBegin",
		mem.CERTIFICATE_TERM_END as "main.certificateTermEnd",
		mem.CERTIFICATE_VALIDITY_DATE_TYPE as "main.certificateValidityDateType",
		mem.MANAGEMENT_ADDR_PROVINCE as "main.managementAddrProvince",
		mem.MANAGEMENT_ADDR_CITY as "main.managementAddrCity",
		mem.MANAGEMENT_ADDR_TOWN as "main.managementAddrTown",
		mem.MANAGEMENT_ADDR_DETAIL as "main.managementAddrDetail",

		mba.ACCOUNT_NO as "account.accountNo",
		mba.ACCOUNT_NAME as "account.accountName",
		mba.BANK_NAME as "account.bankName",
		mba.BANK_CHANNEL_NO as "account.bankChannelNo",

		mii.MCH_NAME as "invoice.mchName",
		mii.TAX_PAYER_TYPE as "invoice.taxPayerType",
		mii.TAX_NO as "invoice.taxNo",
		mii.REGISTER_ADDR_INFO as "invoice.registerAddrInfo",
		mii.ACCOUNT_NO as "invoice.accountNo",
		mii.BANK_NAME as "invoice.bankName",
		mii.DEFAULT_INVOICE_CATEGORY_CODE as "invoice.defaultInvoiceCategoryCode",
		mii.DEFAULT_INVOICE_CATEGORY_NAME as "invoice.defaultInvoiceCategoryName",

		meq.ID as "quoteId",
		meq.PRODUCT_NAME as "productName",
		meq.MAINSTAY_MCH_NO as "mainstayMchNo",
		meq.MAINSTAY_MCH_NAME as "mainstayMchName",
		meq.STATUS as "meqStatus",
		meqr.QUOTE_ID as "quoteJoinId",
		meqr.FORMULA_TYPE as "formulaType",
		meqr.FIXED_FEE as "fixedFee",
		meqr.RATE as "rate",

		mf.FILE_TYPE as "fileType",
		mf.FILE_URL as "fileUrl",

		mep.WORKPLACE_CODE as "workplaceCode",
		mep.WORK_CATEGORY_CODE as "workCategoryCode",
		mep.WORK_CATEGORY_NAME as "workCategoryName",
		mep.SERVICE_DESC as "serviceDesc",
		mep.CHARGE_RULE_DESC as "chargeRuleDesc",
		mep.EXT_JSON as "extJson"
	</sql>

	<update id="freezeMerchantExpire" parameterType="java.util.Map">
		update <include refid="table"/> set MCH_STATUS = 101,UPDATE_TIME = #{updateTime} where AUTH_STATUS = 104 and UPDATE_TIME <![CDATA[<= ]]> #{updateEndTime}
	</update>

	<update id="updateAgentNameByAgentNo" parameterType="java.util.Map">
		update <include refid="table"/> set agent_name = #{agentName},version = version + 1 where agent_no = #{agentNo};
	</update>

	<select id="getAgentCount" resultType="java.util.Map">
		select m.agent_no as agentNo,count(m.ID) as num from
		tbl_merchant m left join tbl_merchant_saler s
		on m.MCH_NO = s.MCH_NO
		where IFNULL(m.agent_no,'') != ''
			<if test="merchantType != null">
				and m.MERCHANT_TYPE = #{merchantType}
			</if>
			<if test="authStatus != null">
				and m.AUTH_STATUS = #{authStatus}
			</if>
			<if test="salerIds != null and salerIds.size() > 0">
				and s.SALER_ID in
				<foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
			</if>
			<if test="completeBeginDate != null and completeEndDate != null ">
				and m.ACTIVE_TIME between #{completeBeginDate} and #{completeEndDate}
			</if>
		group by m.agent_no
	</select>


	<select id="selectAgentDateActiveMch" resultType="long">
		select count(m.ID) from tbl_merchant m left join tbl_merchant_saler s
		on m.MCH_NO = s.MCH_NO
		where IFNULL(m.agent_no,'') != ''
		<if test="merchantType != null">
			and m.MERCHANT_TYPE = #{merchantType}
		</if>
		<if test="authStatus != null">
			and m.AUTH_STATUS = #{authStatus}
		</if>
		<if test="salerIds != null and salerIds.size() > 0">
			and s.SALER_ID in
			<foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
		</if>
		<if test="completeBeginDate != null and completeEndDate != null ">
			and m.ACTIVE_TIME between #{completeBeginDate} and #{completeEndDate}
		</if>
	</select>


	<select id="selectNoPersonnel" parameterType="java.util.Map" resultMap="BaseResultMap">
		SELECT * FROM tbl_merchant m,tbl_merchant_employer_main mem
		where  m.MCH_NO=mem.MCH_NO
		and m.MCH_STATUS=100 and m.AUTH_STATUS  = 100
		and m.MERCHANT_TYPE = 100 and m.MCH_NO
		not in (select mch_no from tbl_merchant_enterprise_personnel group by mch_no )
	</select>

	<select id="queryPromotion" parameterType="java.util.Map" resultType="com.zhixianghui.service.merchant.core.vo.PromotionDiffVo">
		select
			t.UPDATE_TIME  latestTime,
			timestampdiff(day,
						  t.UPDATE_TIME,
						  now()) diffday
		from
			merchant.tbl_merchant_employer_quote  t
		where
			t.MCH_NO  in (
				select a.MCH_NO  from merchant.tbl_merchant a where a.AGENT_NO = #{agentNo} and MCH_STATUS != 101
			)
		  and
			t.STATUS =100
		order by
			t.UPDATE_TIME desc
		limit 1
	</select>

	<resultMap id="DictionaryResultMap" type="com.zhixianghui.facade.common.entity.config.DataDictionary">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="CREATOR" property="creator" jdbcType="VARCHAR"/>
		<result column="DATA_NAME" property="dataName" jdbcType="VARCHAR"/>
		<result column="DATA_INFO" property="dataInfo" jdbcType="OTHER"/>
		<result column="REMARK" property="remark" jdbcType="VARCHAR"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
		<result column="MODIFY_OPERATOR" property="modifyOperator" jdbcType="VARCHAR"/>
		<result column="SYSTEM_TYPE" property="systemType" jdbcType="SMALLINT"/>
	</resultMap>

	<sql id="Ditionary_Base_Column_List">
		ID,
		VERSION,
		CREATOR,
		DATA_NAME,
		DATA_INFO,
		REMARK,
		CREATE_TIME,
		MODIFY_TIME,
		MODIFY_OPERATOR,
		SYSTEM_TYPE
	</sql>

	<select id="getDataDictionaryByName" parameterType="java.util.Map" resultMap="DictionaryResultMap">
		select <include refid="Ditionary_Base_Column_List" />
		from hjzx_common.tbl_data_dictionary
		where
		DATA_NAME = #{dataName,jdbcType=VARCHAR}
	</select>
</mapper>

