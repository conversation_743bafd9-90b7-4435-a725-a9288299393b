<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction">
    <sql id="table"> tbl_pms_function </sql>
    <sql id="pms_role_function">tbl_pms_role_function</sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="NUMBER" property="number" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="BIGINT"/>
        <result column="PERMISSION_FLAG" property="permissionFlag" jdbcType="VARCHAR"/>
        <result column="FUNCTION_TYPE" property="functionType" jdbcType="SMALLINT"/>
        <result column="URL" property="url" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
        ID,
		VERSION,
		CREATE_TIME,
		NAME,
		NUMBER,
		PARENT_ID,
		PERMISSION_FLAG,
		FUNCTION_TYPE,
		URL
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        CREATE_TIME,
        NAME,
        NUMBER,
        PARENT_ID,
        PERMISSION_FLAG,
        FUNCTION_TYPE,
        URL
        ) VALUES (
        0,
        #{createTime,jdbcType=TIMESTAMP},
        #{name,jdbcType=VARCHAR},
        #{number,jdbcType=VARCHAR},
        #{parentId,jdbcType=BIGINT},
        #{permissionFlag,jdbcType=VARCHAR},
        #{functionType,jdbcType=SMALLINT},
        #{url,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        CREATE_TIME,
        NAME,
        NUMBER,
        PARENT_ID,
        PERMISSION_FLAG,
        FUNCTION_TYPE,
        URL
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.name,jdbcType=VARCHAR},
            #{item.number,jdbcType=VARCHAR},
            #{item.parentId,jdbcType=BIGINT},
            #{item.permissionFlag,jdbcType=VARCHAR},
            #{item.functionType,jdbcType=SMALLINT},
            #{item.url,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="importFile" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        CREATE_TIME,
        `NAME`,
        `NUMBER`,
        PARENT_ID,
        PERMISSION_FLAG,
        FUNCTION_TYPE,
        URL
        ) VALUES
        (
        0,
        #{createTime,jdbcType=TIMESTAMP},
        #{name,jdbcType=VARCHAR},
        #{number,jdbcType=VARCHAR},
        #{parentId,jdbcType=BIGINT},
        #{permissionFlag,jdbcType=VARCHAR},
        #{functionType,jdbcType=SMALLINT},
        #{url,jdbcType=VARCHAR}
        )
        ON DUPLICATE KEY UPDATE
        update_time = CURRENT_TIMESTAMP,
        `number` = values(`number`),
        `parent_id` = values(`parent_id`)
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction">
        UPDATE
        <include refid="table"/>
        <set>
            VERSION = #{version,jdbcType=INTEGER} + 1,
            NAME = #{name,jdbcType=VARCHAR},
            NUMBER = #{number,jdbcType=VARCHAR},
            PARENT_ID = #{parentId,jdbcType=BIGINT},
            PERMISSION_FLAG = #{permissionFlag,jdbcType=VARCHAR},
            FUNCTION_TYPE = #{functionType,jdbcType=SMALLINT},
            URL = #{url,jdbcType=VARCHAR}
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction">
        UPDATE
        <include refid="table"/>
        <set>
            VERSION = #{version,jdbcType=INTEGER} + 1,
            <if test="name != null">
                NAME = #{name,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                NUMBER = #{number,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                PARENT_ID = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="permissionFlag != null">
                PERMISSION_FLAG = #{permissionFlag,jdbcType=VARCHAR},
            </if>
            <if test="functionType != null">
                FUNCTION_TYPE = #{functionType,jdbcType=SMALLINT},
            </if>
            <if test="url != null">
                URL = #{url,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 多条件组合查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        ORDER BY `NUMBER` ASC
    </select>

    <!-- 根据多条件组合查询，计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT count(ID) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 按查询条件删除 -->
    <delete id="deleteBy">
        DELETE FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </delete>

    <!-- 根据多个id查询 -->
    <select id="listByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据id查询 -->
    <select id="getByFlag" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE  PERMISSION_FLAG = #{permissionFlag,jdbcType=VARCHAR}
    </select>

    <select id="getPermissionFlag" resultType="String">
        SELECT permission_flag FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 按id主键删除 -->
    <delete id="deleteById">
        DELETE FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 按多个id主键删除 -->
    <delete id="deleteByIdList" parameterType="list">
        DELETE FROM
        <include refid="table"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </delete>

    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

    <sql id="condition_sql">
        <if test="id != null ">
            AND ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            AND ID IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="version != null ">
            AND VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="createTime != null ">
            AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="name != null and name !='' ">
            AND NAME = #{name,jdbcType=VARCHAR}
        </if>
        <if test="maxId != null">
            AND ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
        <if test="number != null and number !='' ">
            AND NUMBER = #{number,jdbcType=VARCHAR}
        </if>
        <if test="parentId != null ">
            AND PARENT_ID = #{parentId,jdbcType=BIGINT}
        </if>
        <if test="permissionFlag != null and permissionFlag !='' ">
            AND PERMISSION_FLAG = #{permissionFlag,jdbcType=VARCHAR}
        </if>
        <if test="functionType != null ">
            AND FUNCTION_TYPE = #{functionType,jdbcType=SMALLINT}
        </if>
        <if test="url != null and url !='' ">
            AND URL = #{url,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="listByRoleIds" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT DISTINCT * FROM (
        SELECT F.* FROM
        <include refid="pms_role_function"/>
        RF
        INNER JOIN
        <include refid="table"/>
        F
        ON RF.FUNCTION_ID=F.ID
        where RF.ROLE_ID IN
        <foreach item="item" index="index" collection="roleIds" open="(" separator="," close=")">#{item}</foreach>
        )
        AS FT ORDER BY FT.number ASC
    </select>

    <select id="listByParentId" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT * FROM
        <include refid="table"/>
        WHERE PARENT_ID = #{parentId,jdbcType=BIGINT}
        ORDER BY NUMBER ASC
    </select>

</mapper>

