<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.pms.PmsRole">
    <!-- Pms权限管理：角色表 -->
    <sql id="pms_role"> TBL_PMS_ROLE </sql>
    <sql id="pms_role_action"> TBL_PMS_ROLE_ACTION </sql>


    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.pms.PmsRole">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="ROLE_NAME" property="roleName" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="ROLE_TYPE" property="roleType" jdbcType="INTEGER"/>

    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
        ID,
		VERSION,
		CREATE_TIME,
		ROLE_NAME,
		REMARK,
		ROLE_TYPE
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRole">
        INSERT INTO
        <include refid="pms_role"/>
        (
        VERSION,
        CREATE_TIME,
        ROLE_NAME,
        REMARK,
        ROLE_TYPE
        ) VALUES (
        0,
        #{createTime,jdbcType=TIMESTAMP},
        #{roleName,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{roleType,jdbcType=INTEGER}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="pms_role"/>
        (
        VERSION,
        CREATE_TIME,
        ROLE_NAME,
        REMARK,
        ROLE_TYPE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.roleName,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.roleType,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRole">
        UPDATE
        <include refid="pms_role"/>
        <set>
            VERSION = #{version,jdbcType=INTEGER} + 1,
            ROLE_NAME = #{roleName,jdbcType=VARCHAR},
            REMARK = #{remark,jdbcType=VARCHAR},
            ROLE_TYPE = #{roleType,jdbcType=INTEGER}
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.pms.PmsRole">
        UPDATE
        <include refid="pms_role"/>
        <set>
            VERSION = #{version,jdbcType=INTEGER} + 1,
            <if test="roleName != null">
                ROLE_NAME = #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="roleType != null">
                ROLE_TYPE = #{roleType,jdbcType=INTEGER}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 多条件组合查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="pms_role"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ROLE_TYPE ASC , ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 根据多条件组合查询，计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT count(ID) FROM
        <include refid="pms_role"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 按查询条件删除 -->
    <delete id="deleteBy">
        DELETE FROM
        <include refid="pms_role"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </delete>

    <!-- 根据多个id查询 -->
    <select id="listByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="pms_role"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="pms_role"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 按id主键删除 -->
    <delete id="deleteById">
        DELETE FROM
        <include refid="pms_role"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 按多个id主键删除 -->
    <delete id="deleteByIdList" parameterType="list">
        DELETE FROM
        <include refid="pms_role"/>
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </delete>

    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

    <sql id="condition_sql">
        <if test="id != null ">
            AND ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            AND ID IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="version != null ">
            AND VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="createTime != null ">
            AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="roleName != null and roleName !='' ">
            AND ROLE_NAME like concat('%',#{roleName,jdbcType=VARCHAR},'%')
        </if>
        <if test="remark != null and remark !='' ">
            AND REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="roleType != null">
            and ROLE_TYPE = #{roleType,jdbcType=INTEGER}
        </if>
    </sql>

    <select id="listByActionId" parameterType="java.util.Map" resultMap="BaseResultMap">
        select R.* from
        <include refid="pms_role"/>
        R
        LEFT JOIN
        <include refid="pms_role_action"/>
        RA
        ON R.ID = RA.ROLE_ID where RA.ACTION_ID = #{actionId}
        order by R.ID ASC
    </select>
</mapper>

