<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantFile">
	<sql id="table"> tbl_merchant_file </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantFile">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="MCH_NAME" property="mchName" jdbcType="VARCHAR"/>
		<result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR"/>
		<result column="FILE_TYPE" property="fileType" jdbcType="SMALLINT"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		VERSION,
		UPDATOR,
		MCH_NO,
		MCH_NAME,
		FILE_URL,
		FILE_TYPE
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantFile">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
        	MCH_NAME ,
        	FILE_URL ,
        	FILE_TYPE 
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			0,
			#{updator,jdbcType=VARCHAR},
			#{mchNo,jdbcType=VARCHAR},
			#{mchName,jdbcType=VARCHAR},
			#{fileUrl,jdbcType=VARCHAR},
			#{fileType,jdbcType=SMALLINT}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
        	MCH_NAME ,
        	FILE_URL ,
        	FILE_TYPE 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			#{item.createTime,jdbcType=TIMESTAMP},
			0,
			#{item.updator,jdbcType=VARCHAR},
			#{item.mchNo,jdbcType=VARCHAR},
			#{item.mchName,jdbcType=VARCHAR},
			#{item.fileUrl,jdbcType=VARCHAR},
			#{item.fileType,jdbcType=SMALLINT}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantFile">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			MCH_NAME = #{mchName,jdbcType=VARCHAR},
			FILE_URL = #{fileUrl,jdbcType=VARCHAR},
			FILE_TYPE = #{fileType,jdbcType=SMALLINT}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantFile">
		UPDATE <include refid="table" />
		<set>
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="updator != null">
				UPDATOR =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="mchNo != null">
				MCH_NO =#{mchNo,jdbcType=VARCHAR},
			</if>
			<if test="mchName != null">
				MCH_NAME =#{mchName,jdbcType=VARCHAR},
			</if>
			<if test="fileUrl != null">
				FILE_URL =#{fileUrl,jdbcType=VARCHAR},
			</if>
			<if test="fileType != null">
				FILE_TYPE =#{fileType,jdbcType=SMALLINT},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!--条件删除-->
	<delete id="deleteBy" parameterType="java.util.Map">
		DELETE FROM <include refid="table" />
		where
		<trim  prefixOverrides="AND">
			<include refid="condition_sql" />
		</trim>
	</delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="mchName != null and mchName !=''">
			and MCH_NAME = #{mchName,jdbcType=VARCHAR}
		</if>
		<if test="fileUrl != null and fileUrl !=''">
			and FILE_URL = #{fileUrl,jdbcType=VARCHAR}
		</if>
		<if test="fileType != null">
			and FILE_TYPE = #{fileType,jdbcType=SMALLINT}
		</if>
	</sql>
</mapper>

