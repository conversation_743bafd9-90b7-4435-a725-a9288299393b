<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.WechatUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.WechatUserInfo">
    <!--@mbg.generated-->
    <!--@Table tbl_wechat_user_info-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="NICKNAME" jdbcType="VARCHAR" property="nickname" />
    <result column="WX_OPEN_ID" jdbcType="VARCHAR" property="wxOpenId" />
    <result column="OPEN_ID" jdbcType="VARCHAR" property="openId" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="AVATAR" jdbcType="VARCHAR" property="avatar" />
    <result column="GENDER" jdbcType="INTEGER" property="gender" />
    <result column="WX_PLAT" jdbcType="INTEGER" property="wxPlat" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="UPDATE_AT" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="LAST_LOGIN_AT" jdbcType="TIMESTAMP" property="lastLoginAt" />
    <result column="CREATE_AT" jdbcType="TIMESTAMP" property="createAt" />
    <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, NICKNAME, WX_OPEN_ID, OPEN_ID, UNIONID, AVATAR, GENDER, WX_PLAT, MOBILE, UPDATE_AT,
    LAST_LOGIN_AT, CREATE_AT, APP_ID
  </sql>
</mapper>
