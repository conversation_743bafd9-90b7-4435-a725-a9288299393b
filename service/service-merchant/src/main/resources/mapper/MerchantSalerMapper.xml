<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantSaler">
	<sql id="table"> tbl_merchant_saler </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantSaler">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="MERCHANT_TYPE" property="merchantType" jdbcType="SMALLINT"/>
		<result column="SALER_ID" property="salerId" jdbcType="BIGINT"/>
		<result column="SALER_NAME" property="salerName" jdbcType="VARCHAR"/>
		<result column="SALE_DEPARTMENT_ID" property="saleDepartmentId" jdbcType="BIGINT"/>
		<result column="SALE_DEPARTMENT_NAME" property="saleDepartmentName" jdbcType="VARCHAR"/>
		<result column="REMARK" property="remark" jdbcType="VARCHAR"/>
		<result column="JSON_INFO" property="jsonInfo" jdbcType="OTHER"/>

	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		UPDATE_TIME,
		VERSION,
		UPDATOR,
		MCH_NO,
		MERCHANT_TYPE,
		SALER_ID,
		SALER_NAME,
		SALE_DEPARTMENT_ID,
		SALE_DEPARTMENT_NAME,
		REMARK,
		JSON_INFO
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantSaler">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
			MERCHANT_TYPE,
        	SALER_ID ,
        	SALER_NAME ,
        	SALE_DEPARTMENT_ID ,
        	SALE_DEPARTMENT_NAME ,
        	REMARK ,
        	JSON_INFO
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			0,
			#{updator,jdbcType=VARCHAR},
			#{mchNo,jdbcType=VARCHAR},
			#{merchantType,jdbcType=VARCHAR},
			#{salerId,jdbcType=BIGINT},
			#{salerName,jdbcType=VARCHAR},
			#{saleDepartmentId,jdbcType=BIGINT},
			#{saleDepartmentName,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{jsonInfo,jdbcType=OTHER}
        )
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantSaler">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			MERCHANT_TYPE = #{merchantType,jdbcType=VARCHAR},
			SALER_ID = #{salerId,jdbcType=BIGINT},
			SALER_NAME = #{salerName,jdbcType=VARCHAR},
			SALE_DEPARTMENT_ID = #{saleDepartmentId,jdbcType=BIGINT},
			SALE_DEPARTMENT_NAME = #{saleDepartmentName,jdbcType=VARCHAR},
			REMARK = #{remark,jdbcType=VARCHAR},
			JSON_INFO = #{jsonInfo,jdbcType=OTHER}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE ID = #{id,jdbcType=BIGINT}
	</select>

	<select id="getByAgentNo" resultMap="BaseResultMap">
		select sale.* from tbl_merchant mer left join tbl_merchant_saler sale on mer.MCH_NO = sale.MCH_NO
		where mer.AGENT_NO = #{agentNo,jdbcType=VARCHAR}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!--条件删除-->
	<delete id="deleteBy" parameterType="java.util.Map">
		DELETE FROM <include refid="table" />
		where
		<trim  prefixOverrides="AND">
			<include refid="condition_sql" />
		</trim>
	</delete>

	<select id="getSalerCount" resultType="java.util.HashMap">
		select a.saler_id as salerId,count(b.id) as num from merchant.tbl_merchant_saler a left join merchant.tbl_merchant b on a.mch_no = b.mch_no
		<where>
			<if test="merchantType != null">
				and b.MERCHANT_TYPE = #{merchantType}
			</if>
			<if test="authStatus != null">
				and b.AUTH_STATUS = #{authStatus}
			</if>
			<if test="salerIds != null">
				and a.SALER_ID in
				<foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">
					#{item,jdbcType=BIGINT}
				</foreach>
			</if>
			<if test="completeBeginDate != null and completeEndDate != null ">
				and b.ACTIVE_TIME between #{completeBeginDate} and #{completeEndDate}
			</if>
		</where>
		group by a.saler_id
	</select>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="merchantType != null and merchantType !=''">
			and MERCHANT_TYPE = #{merchantType,jdbcType=VARCHAR}
		</if>
		<if test="salerId != null">
			and SALER_ID = #{salerId,jdbcType=BIGINT}
		</if>
		<if test="salerIds != null">
			and SALER_ID in
			<foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">
			#{item,jdbcType=BIGINT}
			</foreach>
		</if>
		<if test="salerName != null and salerName !=''">
			and SALER_NAME = #{salerName,jdbcType=VARCHAR}
		</if>
		<if test="saleDepartmentId != null">
			and SALE_DEPARTMENT_ID = #{saleDepartmentId,jdbcType=BIGINT}
		</if>
		<if test="saleDepartmentName != null and saleDepartmentName !=''">
			and SALE_DEPARTMENT_NAME = #{saleDepartmentName,jdbcType=VARCHAR}
		</if>
		<if test="remark != null and remark !=''">
			and REMARK = #{remark,jdbcType=VARCHAR}
		</if>
		<if test="jsonInfo != null">
			and JSON_INFO = #{jsonInfo,jdbcType=OTHER}
		</if>
		<if test="mchNos != null and mchNos.size() > 0">
			and MCH_NO in
			<foreach collection="mchNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
		</if>
	</sql>
</mapper>

