<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.AgentProductQuote">
    <sql id="table">tbl_agent_product_quote</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgentProductQuote">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="UPDATER" property="updater" jdbcType="VARCHAR"/>
        <result column="AGENT_NO" property="agentNo" jdbcType="VARCHAR"/>
        <result column="AGENT_NAME" property="agentName" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NO" property="productNo" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="CALCULATE_MODE" property="calculateMode" jdbcType="SMALLINT"/>
        <result column="FORMULA_TYPE" property="formulaType" jdbcType="SMALLINT"/>
        <result column="FIXED_FEE" property="fixedFee" jdbcType="DECIMAL"/>
        <result column="FEE_RATE" property="feeRate" jdbcType="DECIMAL"/>
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <result column="FLOW_ID" property="flowId" jdbcType="BIGINT"/>
        <result column="RULE_PARAM" property="ruleParam" jdbcType="OTHER"/>
        <result column="RULE_TYPE" property="ruleType" jdbcType="SMALLINT"/>
        <result column="SECOND_FORMULA_TYPE" property="secondFormulaType" jdbcType="SMALLINT"/>
        <result column="SECOND_FIXED_FEE" property="secondFixedFee" jdbcType="DECIMAL"/>
        <result column="SECOND_FEE_RATE" property="secondFeeRate" jdbcType="DECIMAL"/>
        <result column="PRIORITY" property="priority" jdbcType="SMALLINT"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="REAL_PROFIT_RATIO" property="realProfitRatio" jdbcType="INTEGER"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_TIME, UPDATE_TIME, VERSION, UPDATER, AGENT_NO, AGENT_NAME, PRODUCT_NO, PRODUCT_NAME, MAINSTAY_NO, MAINSTAY_NAME,CALCULATE_MODE, FORMULA_TYPE, FIXED_FEE, FEE_RATE,STATUS,FLOW_ID,RULE_PARAM,RULE_TYPE,SECOND_FORMULA_TYPE, SECOND_FIXED_FEE, SECOND_FEE_RATE,PRIORITY, MIN_FEE, MAX_FEE, DESCRIPTION,REAL_PROFIT_RATIO
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.AgentProductQuote">
        INSERT INTO <include refid="table" /> (
        CREATE_TIME,
        UPDATE_TIME,
        VERSION,
        UPDATER,
        AGENT_NO,
        AGENT_NAME,
        PRODUCT_NO,
        PRODUCT_NAME,
        MAINSTAY_NO,
        MAINSTAY_NAME,
        CALCULATE_MODE,
        FORMULA_TYPE,
        FIXED_FEE,
        FEE_RATE,
        STATUS,
        FLOW_ID,
        RULE_PARAM,
        RULE_TYPE,
        SECOND_FORMULA_TYPE,
        SECOND_FIXED_FEE,
        SECOND_FEE_RATE,
        PRIORITY,
        MIN_FEE,
        MAX_FEE,
        DESCRIPTION,
        REAL_PROFIT_RATIO
        ) VALUES (
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        0,
        #{updater,jdbcType=VARCHAR},
        #{agentNo,jdbcType=VARCHAR},
        #{agentName,jdbcType=VARCHAR},
        #{productNo,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{calculateMode,jdbcType=SMALLINT},
        #{formulaType,jdbcType=SMALLINT},
        #{fixedFee,jdbcType=DECIMAL},
        #{feeRate,jdbcType=DECIMAL},
        #{status,jdbcType=INTEGER},
        #{flowId,jdbcType=BIGINT},
        #{ruleParam,jdbcType=OTHER},
        #{ruleType,jdbcType=SMALLINT},
        #{secondFormulaType,jdbcType=SMALLINT},
        #{secondFixedFee,jdbcType=DECIMAL},
        #{secondFeeRate,jdbcType=DECIMAL},
        #{priority,jdbcType=SMALLINT},
        #{minFee,jdbcType=DECIMAL},
        #{maxFee,jdbcType=DECIMAL},
        #{description,jdbcType=VARCHAR},
        #{realProfitRatio,jdbcType=INTEGER}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        CREATE_TIME,
        UPDATE_TIME,
        VERSION,
        UPDATER,
        AGENT_NO,
        AGENT_NAME,
        PRODUCT_NO,
        PRODUCT_NAME,
        MAINSTAY_NO,
        MAINSTAY_NAME,
        CALCULATE_MODE,
        FORMULA_TYPE,
        FIXED_FEE,
        FEE_RATE,
        STATUS,
        FLOW_ID,
        RULE_PARAM,
        RULE_TYPE,
        SECOND_FORMULA_TYPE,
        SECOND_FIXED_FEE,
        SECOND_FEE_RATE,
        PRIORITY,
        MIN_FEE,
        MAX_FEE,
        DESCRIPTION
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            0,
            #{item.updater,jdbcType=VARCHAR},
            #{item.agentNo,jdbcType=VARCHAR},
            #{item.agentName,jdbcType=VARCHAR},
            #{item.productNo,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.calculateMode,jdbcType=SMALLINT},
            #{item.formulaType,jdbcType=SMALLINT},
            #{item.fixedFee,jdbcType=DECIMAL},
            #{item.feeRate,jdbcType=DECIMAL},
            #{status,jdbcType=INTEGER},
            #{flowId,jdbcType=BIGINT},
            #{ruleParam,jdbcType=OTHER},
            #{ruleType,jdbcType=SMALLINT},
            #{item.secondFormulaType,jdbcType=SMALLINT},
            #{item.secondFixedFee,jdbcType=DECIMAL},
            #{item.secondFeeRate,jdbcType=DECIMAL},
            #{item.priority,jdbcType=SMALLINT},
            #{item.minFee,jdbcType=DECIMAL},
            #{item.maxFee,jdbcType=DECIMAL},
            #{item.description,jdbcType=VARCHAR},
            #{item.realProfitRatio,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgentProductQuote">
        UPDATE <include refid="table" /> SET
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        VERSION = #{version,jdbcType=SMALLINT} +1,
        UPDATER = #{updater,jdbcType=VARCHAR},
        AGENT_NO = #{agentNo,jdbcType=VARCHAR},
        AGENT_NAME = #{agentName,jdbcType=VARCHAR},
        PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
        PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
        MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
        MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
        CALCULATE_MODE = #{calculateMode,jdbcType=SMALLINT},
        FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT},
        FIXED_FEE = #{fixedFee,jdbcType=DECIMAL},
        FEE_RATE = #{feeRate,jdbcType=DECIMAL},
        STATUS = #{status,jdbcType=INTEGER},
        FLOW_ID = #{flowId,jdbcType=BIGINT},
        RULE_PARAM = #{ruleParam,jdbcType=OTHER},
        RULE_TYPE = #{ruleType,jdbcType=SMALLINT},
        SECOND_FORMULA_TYPE = #{secondFormulaType,jdbcType=SMALLINT},
        SECOND_FIXED_FEE = #{secondFixedFee,jdbcType=DECIMAL},
        SECOND_FEE_RATE = #{secondFeeRate,jdbcType=DECIMAL},
        PRIORITY = #{priority,jdbcType=SMALLINT},
        MIN_FEE = #{minFee,jdbcType=DECIMAL},
        MAX_FEE = #{maxFee,jdbcType=DECIMAL},
        DESCRIPTION = #{description,jdbcType=VARCHAR},
        REAL_PROFIT_RATIO = #{realProfitRatio,jdbcType=INTEGER}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgentProductQuote">
        UPDATE <include refid="table" />
        <set>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="agentName != null">
                AGENT_NAME = #{agentName,jdbcType=VARCHAR},
            </if>
            <if test="productNo != null">
                PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="calculateMode != null">
                CALCULATE_MODE = #{calculateMode,jdbcType=SMALLINT},
            </if>
            <if test="formulaType != null">
                FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT},
            </if>
            <if test="fixedFee != null">
                FIXED_FEE = #{fixedFee,jdbcType=DECIMAL},
            </if>
            <if test="feeRate != null">
                FEE_RATE = #{feeRate,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=INTEGER},
            </if>
            <if test="flowId != null">
                FLOW_ID = #{flowId,jdbcType=BIGINT},
            </if>
            <if test="ruleParam != null">
                RULE_PARAM = #{ruleParam,jdbcType=OTHER},
            </if>
            <if test="ruleType != null">
                RULE_TYPE = #{ruleType,jdbcType=SMALLINT},
            </if>
            <if test="secondFormulaType != null">
                SECOND_FORMULA_TYPE = #{secondFormulaType,jdbcType=SMALLINT},
            </if>
            <if test="secondFixedFee != null">
                SECOND_FIXED_FEE = #{secondFixedFee,jdbcType=DECIMAL},
            </if>
            <if test="secondFeeRate != null">
                SECOND_FEE_RATE = #{secondFeeRate,jdbcType=DECIMAL},
            </if>
            <if test="priority != null">
                PRIORITY = #{priority,jdbcType=SMALLINT},
            </if>
            <if test="minFee != null">
                MIN_FEE = #{minFee,jdbcType=DECIMAL},
            </if>
            <if test="maxFee != null">
                MAX_FEE = #{maxFee,jdbcType=DECIMAL},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="realProfitRatio !=null ">
                REAL_PROFIT_RATIO = #{realProfitRatio,jdbcType=INTEGER}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="deleteBy" parameterType="java.util.Map" resultType="long">
        delete FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>
<!--    <delete id="deleteBy">-->
<!--        DELETE FROM <include refid="table" />-->
<!--        <where>-->
<!--            <include refid="condition_sql" />-->
<!--        </where>-->
<!--    </delete>-->

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="updater != null and updater !=''">
            and UPDATER = #{updater,jdbcType=VARCHAR}
        </if>
        <if test="agentNo != null and agentNo !=''">
            and AGENT_NO = #{agentNo,jdbcType=VARCHAR}
        </if>
        <if test="agentName != null and agentName !=''">
            and AGENT_NAME = #{agentName,jdbcType=VARCHAR}
        </if>
        <if test="productNo != null and productNo !=''">
            and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
        </if>
        <if test="productName != null and productName !=''">
            and PRODUCT_NAME = #{productName,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="calculateMode != null">
            and CALCULATE_MODE = #{calculateMode,jdbcType=SMALLINT}
        </if>
        <if test="formulaType != null">
            and FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT}
        </if>
        <if test="fixedFee != null">
            and FIXED_FEE = #{fixedFee,jdbcType=DECIMAL}
        </if>
        <if test="feeRate != null">
            and FEE_RATE = #{feeRate,jdbcType=DECIMAL}
        </if>
        <if test="status != null">
            and STATUS = #{status,jdbcType=INTEGER}
        </if>
        <if test="flowId != null">
            and FLOW_ID = #{flowId,jdbcType=BIGINT}
        </if>
        <if test="ruleType != null">
            and RULE_TYPE = #{ruleType,jdbcType=SMALLINT}
        </if>
        <if test="ruleParam != null and ruleParam !=''">
            and RULE_PARAM = #{ruleParam,jdbcType=OTHER}
        </if>
        <if test="secondFormulaType != null">
            and SECOND_FORMULA_TYPE = #{secondFormulaType,jdbcType=SMALLINT}
        </if>
        <if test="secondFixedFee != null">
            and SECOND_FIXED_FEE = #{secondFixedFee,jdbcType=DECIMAL}
        </if>
        <if test="secondFeeRate != null and secondFeeRate !=''">
            and SECOND_FEE_RATE = #{secondFeeRate,jdbcType=DECIMAL}
        </if>
        <if test="priority != null">
            and PRIORITY = #{priority,jdbcType=SMALLINT}
        </if>
        <if test="minFee != null">
            and MIN_FEE = #{minFee,jdbcType=DECIMAL}
        </if>
        <if test="maxFee != null">
            and MAX_FEE = #{maxFee,jdbcType=DECIMAL}
        </if>
    </sql>

    <update id="updateByAgentNo" parameterType="java.util.Map">
        update <include refid="table"/>
        <include refid="updateSql"/>
        where agent_no = #{agentNo}
        <if test="version != null">
            and version = #{version}
        </if>
    </update>

    <sql id="updateSql">
        <set>
            VERSION = version +1,
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="agentName != null">
                AGENT_NAME = #{agentName,jdbcType=VARCHAR},
            </if>
            <if test="productNo != null">
                PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="calculateMode != null">
                CALCULATE_MODE = #{calculateMode,jdbcType=SMALLINT},
            </if>
            <if test="formulaType != null">
                FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT},
            </if>
            <if test="fixedFee != null">
                FIXED_FEE = #{fixedFee,jdbcType=DECIMAL},
            </if>
            <if test="feeRate != null">
                FEE_RATE = #{feeRate,jdbcType=DECIMAL},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=INTEGER},
            </if>
            <if test="flowId != null">
                FLOW_ID = #{flowId,jdbcType=BIGINT},
            </if>
            <if test="ruleParam != null">
                RULE_PARAM = #{ruleParam,jdbcType=OTHER},
            </if>
            <if test="ruleType != null">
                RULE_TYPE = #{ruleType,jdbcType=SMALLINT},
            </if>
            <if test="secondFormulaType != null">
                SECOND_FORMULA_TYPE = #{secondFormulaType,jdbcType=SMALLINT},
            </if>
            <if test="secondFixedFee != null">
                SECOND_FIXED_FEE = #{secondFixedFee,jdbcType=DECIMAL},
            </if>
            <if test="secondFeeRate != null">
                SECOND_FEE_RATE = #{secondFeeRate,jdbcType=DECIMAL},
            </if>
            <if test="priority != null">
                PRIORITY = #{priority,jdbcType=SMALLINT},
            </if>
            <if test="minFee != null">
                MIN_FEE = #{minFee,jdbcType=DECIMAL},
            </if>
            <if test="maxFee != null">
                MAX_FEE = #{maxFee,jdbcType=DECIMAL},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="realProfitRatio != null ">
                REAL_PROFIT_RATIO = #{realProfitRatio,jdbcType=INTEGER}
            </if>
        </set>
    </sql>
</mapper>
