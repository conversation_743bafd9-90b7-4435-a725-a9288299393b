<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.MerchantCkhQuoteMapper">
    <sql id="table">tbl_merchant_ckh_quote</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantCkhQuote">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="quote_id" property="quoteId" jdbcType="BIGINT"/>
        <result column="tax_payer" property="taxPayer" jdbcType="SMALLINT"/>
        <result column="tax_rate" property="taxRate" jdbcType="DECIMAL"/>
        <result column="added_tax_rate" property="addedTaxRate" jdbcType="DECIMAL"/>
        <result column="tax_formula" property="taxFormula" jdbcType="SMALLINT"/>
        <result column="tax_type_desc" property="taxTypeDesc" jdbcType="VARCHAR"/>
        <result column="service_fee_rate" property="serviceFeeRate" jdbcType="DECIMAL"/>
        <result column="balanced_mode" property="balancedMode" jdbcType="SMALLINT"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, quote_id, tax_payer, tax_rate, added_tax_rate, tax_formula, tax_type_desc,service_fee_rate, balanced_mode, status, create_time, update_time
    </sql>

    <select id="getFeeRate" parameterType="java.util.Map" resultMap="BaseResultMap">
        select t2.* from
        tbl_merchant_employer_quote t1,tbl_merchant_ckh_quote t2
        where t1.id = t2.quote_id
        <if test="param.mchNo != null and param.mchNo != ''">
            and t1.mch_no = #{param.mchNo}
        </if>
        <if test="param.mainstayMchNo != null and param.mainstayMchNo != ''">
            and t1.mainstay_mch_no = #{param.mainstayMchNo}
        </if>
        <if test="param.productNo != null and param.productNo != ''">
            and t1.product_no = #{param.productNo}
        </if>
        <if test="param.status != null">
            and t1.status = #{param.status}
        </if>
        limit 1
    </select>


    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            version,
            quote_id,
            tax_payer,
            tax_rate,
            added_tax_rate,
            tax_formula,
            tax_type_desc,
            service_fee_rate,
            balanced_mode,
            status,
            create_time,
            update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.quoteId,jdbcType=BIGINT},
            #{item.taxPayer,jdbcType=SMALLINT},
            #{item.taxRate,jdbcType=DECIMAL},
            #{item.addedTaxRate,jdbcType=DECIMAL},
            #{item.taxFormula,jdbcType=SMALLINT},
            #{item.taxTypeDesc,jdbcType=VARCHAR},
            #{item.serviceFeeRate,jdbcType=DECIMAL},
            #{item.balancedMode,jdbcType=SMALLINT},
            #{item.status,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantCkhQuote">
        UPDATE <include refid="table" /> SET
            version = #{version,jdbcType=SMALLINT},
            quote_id = #{quoteId,jdbcType=BIGINT},
            tax_payer = #{taxPayer,jdbcType=SMALLINT},
            tax_rate = #{taxRate,jdbcType=DECIMAL},
            added_tax_rate = #{addedTaxRate,jdbcType=DECIMAL},
            tax_formula = #{taxFormula,jdbcType=SMALLINT},
            tax_type_desc = #{taxTypeDesc,jdbcType=VARCHAR},
            service_fee_rate = #{serviceFeeRate,jdbcType=DECIMAL},
            balanced_mode = #{balancedMode,jdbcType=SMALLINT},
            status = #{status,jdbcType=SMALLINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantCkhQuote">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="quoteId != null">
                quote_id = #{quoteId,jdbcType=BIGINT},
            </if>
            <if test="taxPayer != null">
                tax_payer = #{taxPayer,jdbcType=SMALLINT},
            </if>
            <if test="taxRate != null">
                tax_rate = #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="addedTaxRate != null">
                added_tax_rate = #{addedTaxRate,jdbcType=DECIMAL},
            </if>
            <if test="taxFormula != null">
                tax_formula = #{taxFormula,jdbcType=SMALLINT},
            </if>
            <if test="taxTypeDesc != null">
                tax_type_desc = #{taxTypeDesc,jdbcType=VARCHAR},
            </if>
            <if test="serviceFeeRate != null">
                service_fee_rate = #{serviceFeeRate,jdbcType=DECIMAL},
            </if>
            <if test="balancedMode != null">
                balanced_mode = #{balancedMode,jdbcType=SMALLINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="quoteId != null">
            and quote_id = #{quoteId,jdbcType=BIGINT}
        </if>
        <if test="taxPayer != null">
            and tax_payer = #{taxPayer,jdbcType=SMALLINT}
        </if>
        <if test="taxRate != null">
            and tax_rate = #{taxRate,jdbcType=DECIMAL}
        </if>
        <if test="addedTaxRate != null">
            and added_tax_rate = #{addedTaxRate,jdbcType=DECIMAL}
        </if>
        <if test="taxFormula != null">
            and tax_formula = #{taxFormula,jdbcType=SMALLINT}
        </if>
        <if test="serviceFeeRate != null">
            and service_fee_rate = #{serviceFeeRate,jdbcType=DECIMAL}
        </if>
        <if test="balancedMode != null">
            and balanced_mode = #{balancedMode,jdbcType=SMALLINT}
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <!-- 获取结算模式-->
    <select id="getBalancedMode" resultType="java.lang.Integer">
        select mcq.balanced_mode from tbl_merchant_employer_quote meq,tbl_merchant_ckh_quote mcq
        where meq.id=mcq.quote_id
        and meq.MCH_NO=#{mchNo}
        and meq.MAINSTAY_MCH_NO=#{mainstayMchNo}
        and PRODUCT_NO=#{productNo}
        and `STATUS`=#{status}
    </select>

</mapper>
