<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.EventsFollowMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.EventsFollow">
    <!--@mbg.generated-->
    <!--@Table tbl_events_follow-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="EVENT_CONTENT" jdbcType="LONGVARCHAR" property="eventContent" />
    <result column="EVENT_TYPE" jdbcType="SMALLINT" property="eventType" />
    <result column="EVENT_TYPE_DESC" jdbcType="VARCHAR" property="eventTypeDesc" />
    <result column="MCH_NO" jdbcType="VARCHAR" property="mchNo" />
    <result column="MCH_NAME" jdbcType="VARCHAR" property="mchName" />
    <result column="MCH_TYPE" jdbcType="SMALLINT" property="mchType" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_BY" jdbcType="TIMESTAMP" property="updateBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, EVENT_CONTENT, EVENT_TYPE, EVENT_TYPE_DESC, MCH_NO, MCH_NAME, MCH_TYPE, CREATE_TIME, 
    VERSION, UPDATE_TIME, UPDATE_BY
  </sql>
</mapper>