<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.AgreementComponentMapper">
    <sql id="table">tbl_agreement_component</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgreementComponent">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="LABEL" property="label" jdbcType="VARCHAR"/>
        <result column="REQUIRED" property="required" jdbcType="SMALLINT"/>
        <result column="STYLE" property="style" jdbcType="OTHER"/>
        <result column="PRESET" property="preset" jdbcType="SMALLINT"/>
        <result column="TYPE" property="type" jdbcType="INTEGER"/>
        <result column="HANDLES" property="handles" jdbcType="OTHER"/>
        <result column="RESIZABLE" property="resizable" jdbcType="TINYINT"/>
        <result column="AXIS" property="axis" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, LABEL, REQUIRED, STYLE, PRESET, TYPE, HANDLES, RESIZABLE,AXIS
    </sql>


    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            LABEL,
            REQUIRED,
            STYLE,
            PRESET,
            TYPE,
            HANDLES,
            RESIZABLE,
            AXIS
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.label,jdbcType=VARCHAR},
            #{item.required,jdbcType=SMALLINT},
            #{item.style,jdbcType=OTHER},
            #{item.preset,jdbcType=SMALLINT},
            #{item.type,jdbcType=INTEGER},
            #{item.handles,jdbcType=OTHER},
            #{item.resizable,jdbcType=TINYINT},
            #{item.axis,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgreementComponent">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} +1,
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            LABEL = #{label,jdbcType=VARCHAR},
            REQUIRED = #{required,jdbcType=SMALLINT},
            STYLE = #{style,jdbcType=OTHER},
            PRESET = #{preset,jdbcType=SMALLINT},
            TYPE = #{type,jdbcType=INTEGER},
            HANDLES = #{handles,jdbcType=OTHER},
            RESIZABLE = #{resizable,jdbcType=TINYINT},
            AXIS = #{axis,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgreementComponent">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="label != null">
                LABEL = #{label,jdbcType=VARCHAR},
            </if>
            <if test="required != null">
                REQUIRED = #{required,jdbcType=SMALLINT},
            </if>
            <if test="style != null">
                STYLE = #{style,jdbcType=OTHER},
            </if>
            <if test="preset != null">
                PRESET = #{preset,jdbcType=SMALLINT},
            </if>
            <if test="type != null">
                TYPE = #{type,jdbcType=INTEGER},
            </if>
            <if test="handles != null">
                HANDLES = #{handles,jdbcType=OTHER},
            </if>
            <if test="resizable != null">
                RESIZABLE = #{resizable,jdbcType=TINYINT}
            </if>
            <if test="axis != null and axis != ''">
                AXIS = #{axis,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="label != null and label !=''">
            and LABEL = #{label,jdbcType=VARCHAR}
        </if>
        <if test="required != null">
            and REQUIRED = #{required,jdbcType=SMALLINT}
        </if>
        <if test="style != null and style !=''">
            and STYLE = #{style,jdbcType=OTHER}
        </if>
        <if test="preset != null">
            and PRESET = #{preset,jdbcType=SMALLINT}
        </if>
        <if test="type != null">
            and TYPE = #{type,jdbcType=INTEGER}
        </if>
        <if test="handles != null and handles !=''">
            and HANDLES = #{handles,jdbcType=OTHER}
        </if>
        <if test="resizable != null">
            and RESIZABLE = #{resizable,jdbcType=TINYINT}
        </if>
        <if test="axis != null">
            and AXIS = #{axis,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
