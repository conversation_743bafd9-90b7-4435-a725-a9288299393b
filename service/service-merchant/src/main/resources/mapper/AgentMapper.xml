<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.Agent">
    <sql id="table">
        tbl_agent
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.Agent">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="ACTIVE_TIME" property="activeTime" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="UPDATER" property="updater" jdbcType="VARCHAR"/>
        <result column="AGENT_NAME" property="agentName" jdbcType="VARCHAR"/>
        <result column="INVITER_NO" property="inviterNo" jdbcType="VARCHAR"/>
        <result column="INVITER_NAME" property="inviterName" jdbcType="VARCHAR"/>
        <result column="AGENT_NO" property="agentNo" jdbcType="VARCHAR"/>
        <result column="AGENT_STATUS" property="agentStatus" jdbcType="SMALLINT"/>
        <result column="AGENT_TYPE" property="agentType" jdbcType="SMALLINT"/>
        <result column="AUTH_STATUS" property="authStatus" jdbcType="SMALLINT"/>
        <result column="INVITATION_NUM" property="invitationNum" jdbcType="INTEGER"/>
        <result column="MER_NUM" property="merNum" jdbcType="INTEGER"/>
        <result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR"/>
        <result column="CONTACT_PHONE" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="CONTACT_EMAIL" property="contactEmail" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="JSON_INFO" property="jsonInfo" jdbcType="OTHER"/>
        <result column="WITHHOLDING_TAX_RATIO" property="withholdingTaxRatio" jdbcType="DECIMAL"/>
        <result column="SELF_DECLARED" property="selfDeclared" jdbcType="SMALLINT"/>
        <result column="FOUNDER" property="founder" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID,
        CREATE_TIME,
        UPDATE_TIME,
        ACTIVE_TIME,
        VERSION,
        UPDATER,
        AGENT_NAME,
        INVITER_NO,
        INVITER_NAME,
        AGENT_NO,
        AGENT_STATUS,
        AGENT_TYPE,
        AUTH_STATUS,
        INVITATION_NUM,
        MER_NUM,
        CONTACT_NAME,
        CONTACT_PHONE,
        CONTACT_EMAIL,
        REMARK,
        JSON_INFO,
        WITHHOLDING_TAX_RATIO,
        SELF_DECLARED,
        FOUNDER
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.zhixianghui.facade.merchant.entity.Agent">
        INSERT INTO
        <include refid="table"/>
        (
        CREATE_TIME,
        UPDATE_TIME,
        ACTIVE_TIME,
        VERSION,
        UPDATER,
        AGENT_NAME,
        INVITER_NO,
        INVITER_NAME,
        AGENT_NO,
        AGENT_STATUS,
        AGENT_TYPE,
        AUTH_STATUS,
        INVITATION_NUM,
        MER_NUM,
        CONTACT_NAME,
        CONTACT_PHONE,
        CONTACT_EMAIL,
        REMARK,
        JSON_INFO,
        WITHHOLDING_TAX_RATIO,
        SELF_DECLARED,
        FOUNDER
        ) VALUES (
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{activeTime,jdbcType=TIMESTAMP},
        0,
        #{updater,jdbcType=VARCHAR},
        #{agentName,jdbcType=VARCHAR},
        #{inviterNo,jdbcType=VARCHAR},
        #{inviterName,jdbcType=VARCHAR},
        #{agentNo,jdbcType=VARCHAR},
        #{agentStatus,jdbcType=SMALLINT},
        #{agentType,jdbcType=SMALLINT},
        #{authStatus,jdbcType=SMALLINT},
        #{invitationNum,jdbcType=INTEGER},
        #{merNum,jdbcType=INTEGER},
        #{contactName,jdbcType=VARCHAR},
        #{contactPhone,jdbcType=VARCHAR},
        #{contactEmail,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{jsonInfo,jdbcType=OTHER},
        #{withholdingTaxRatio,jdbcType=DECIMAL},
        #{selfDeclared,jdbcType=SMALLINT},
        #{founder,jdbcType=VARCHAR}
        )
        on duplicate key
        update
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        ACTIVE_TIME = #{activeTime,jdbcType=TIMESTAMP},
        VERSION = 0,
        UPDATER = #{updater,jdbcType=VARCHAR},
        AGENT_NAME = #{agentName,jdbcType=VARCHAR},
        INVITER_NO = #{inviterNo,jdbcType=VARCHAR},
        INVITER_NAME = #{inviterName,jdbcType=VARCHAR},
        AGENT_NO = #{agentNo,jdbcType=VARCHAR},
        AGENT_STATUS = #{agentStatus,jdbcType=SMALLINT},
        AGENT_TYPE = #{agentType,jdbcType=SMALLINT},
        AUTH_STATUS = #{authStatus,jdbcType=SMALLINT},
        INVITATION_NUM = #{invitationNum,jdbcType=INTEGER},
        MER_NUM = #{merNum,jdbcType=INTEGER},
        CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
        CONTACT_PHONE = #{contactPhone,jdbcType=VARCHAR},
        CONTACT_EMAIL = #{contactEmail,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        JSON_INFO = #{jsonInfo,jdbcType=OTHER},
        WITHHOLDING_TAX_RATIO = #{withholdingTaxRatio,jdbcType=DECIMAL}
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        CREATE_TIME,
        UPDATE_TIME,
        ACTIVE_TIME,
        VERSION,
        UPDATER,
        AGENT_NAME,
        INVITER_NO,
        INVITER_NAME,
        AGENT_NO,
        AGENT_STATUS,
        AGENT_TYPE,
        AUTH_STATUS,
        INVITATION_NUM,
        MER_NUM,
        CONTACT_NAME,
        CONTACT_PHONE,
        CONTACT_EMAIL,
        REMARK,
        JSON_INFO,
        WITHHOLDING_TAX_RATIO,
        SELF_DECLARED
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.activeTime,jdbcType=TIMESTAMP},
            0,
            #{item.updater,jdbcType=VARCHAR},
            #{item.agentName,jdbcType=VARCHAR},
            #{item.inviterNo,jdbcType=VARCHAR},
            #{item.inviterName,jdbcType=VARCHAR},
            #{item.agentNo,jdbcType=VARCHAR},
            #{item.agentStatus,jdbcType=SMALLINT},
            #{item.agentType,jdbcType=SMALLINT},
            #{item.authStatus,jdbcType=SMALLINT},
            #{item.invitationNum,jdbcType=INTEGER},
            #{item.merNum,jdbcType=INTEGER},
            #{item.contactName,jdbcType=VARCHAR},
            #{item.contactPhone,jdbcType=VARCHAR},
            #{item.contactEmail,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.jsonInfo,jdbcType=OTHER},
            #{item.withholdingTaxRatio,jdbcType=DECIMAL},
            #{item.selfDeclared,jdbcType=SMALLINT}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.Agent">
        UPDATE
        <include refid="table"/>
        SET
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        ACTIVE_TIME = #{activeTime,jdbcType=TIMESTAMP},
        VERSION = #{version,jdbcType=SMALLINT} +1,
        UPDATER = #{updater,jdbcType=VARCHAR},
        AGENT_NAME = #{agentName,jdbcType=VARCHAR},
        INVITER_NO = #{inviterNo,jdbcType=VARCHAR},
        INVITER_NAME = #{inviterName,jdbcType=VARCHAR},
        AGENT_NO = #{agentNo,jdbcType=VARCHAR},
        AGENT_STATUS = #{agentStatus,jdbcType=SMALLINT},
        AGENT_TYPE = #{agentType,jdbcType=SMALLINT},
        AUTH_STATUS = #{authStatus,jdbcType=SMALLINT},
        INVITATION_NUM = #{invitationNum,jdbcType=INTEGER},
        MER_NUM = #{merNum,jdbcType=INTEGER},
        CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
        CONTACT_PHONE = #{contactPhone,jdbcType=VARCHAR},
        CONTACT_EMAIL = #{contactEmail,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        JSON_INFO = #{jsonInfo,jdbcType=OTHER},
        WITHHOLDING_TAX_RATIO = #{withholdingTaxRatio,jdbcType=DECIMAL},
        SELF_DECLARED = #{selfDeclared,jdbcType=SMALLINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.Agent">
        UPDATE
        <include refid="table"/>
        <set>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="activeTime != null">
                ACTIVE_TIME = #{activeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="agentName != null">
                AGENT_NAME = #{agentName,jdbcType=VARCHAR},
            </if>
            <if test="inviterNo != null">
                INVITER_NO = #{inviterNo,jdbcType=VARCHAR},
            </if>
            <if test="inviterName != null">
                INVITER_NAME = #{inviterName,jdbcType=VARCHAR},
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="agentStatus != null">
                AGENT_STATUS = #{agentStatus,jdbcType=SMALLINT},
            </if>
            <if test="agentType != null">
                AGENT_TYPE = #{agentType,jdbcType=SMALLINT},
            </if>
            <if test="authStatus != null">
                AUTH_STATUS = #{authStatus,jdbcType=SMALLINT},
            </if>
            <if test="invitationNum != null">
                INVITATION_NUM = #{invitationNum,jdbcType=INTEGER},
            </if>
            <if test="merNum != null">
                MER_NUM = #{merNum,jdbcType=INTEGER},
            </if>
            <if test="contactName != null">
                CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null">
                CONTACT_PHONE = #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null">
                CONTACT_EMAIL = #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="jsonInfo != null">
                JSON_INFO = #{jsonInfo,jdbcType=OTHER},
            </if>
            <if test="withholdingTaxRatio != null">
                WITHHOLDING_TAX_RATIO = #{withholdingTaxRatio,jdbcType=DECIMAL},
            </if>
            <if test="selfDeclared != null">
                SELF_DECLARED = #{selfDeclared,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        as a
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns != ''">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table"/>
        as a
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM
        <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and a.ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and a.CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and a.UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="activeTime != null">
            and a.ACTIVE_TIME = #{activeTime,jdbcType=TIMESTAMP}
        </if>
        <if test="version != null">
            and a.VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="updater != null and updater != ''">
            and a.UPDATER = #{updater,jdbcType=VARCHAR}
        </if>
        <if test="agentName != null and agentName != ''">
            and a.AGENT_NAME = #{agentName,jdbcType=VARCHAR}
        </if>
        <if test="agentNameLike != null and agentNameLike != ''">
            and a.AGENT_NAME like concat('%',#{agentNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="inviterNo != null and inviterNo != ''">
            and a.INVITER_NO = #{inviterNo,jdbcType=VARCHAR}
        </if>
        <if test="inviterName != null and inviterName != ''">
            and a.INVITER_NAME = #{inviterName,jdbcType=VARCHAR}
        </if>
        <if test="inviterNameLike != null and inviterNameLike != ''">
            and a.INVITER_NAME like concat('%',#{inviterNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="agentNo != null and agentNo != ''">
            and a.AGENT_NO = #{agentNo,jdbcType=VARCHAR}
        </if>
        <if test="agentNoList != null and agentNoList.size() > 0">
            and a.AGENT_NO in
            <foreach collection="agentNoList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="ignoreAgentStatusList != null and ignoreAgentStatusList.size() > 0">
            and a.AGENT_STATUS not in
            <foreach collection="ignoreAgentStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="agentStatus != null">
            and a.AGENT_STATUS = #{agentStatus,jdbcType=SMALLINT}
        </if>
        <if test="agentType != null">
            and a.AGENT_TYPE = #{agentType,jdbcType=SMALLINT}
        </if>
        <if test="authStatus != null">
            and a.AUTH_STATUS = #{authStatus,jdbcType=SMALLINT}
        </if>
        <if test="invitationNum != null">
            and a.INVITATION_NUM = #{invitationNum,jdbcType=INTEGER}
        </if>
        <if test="merNum != null">
            and a.MER_NUM = #{merNum,jdbcType=INTEGER}
        </if>
        <if test="contactName != null and contactName != ''">
            and a.CONTACT_NAME = #{contactName,jdbcType=VARCHAR}
        </if>
        <if test="contactPhone != null and contactPhone != ''">
            and a.CONTACT_PHONE = #{contactPhone,jdbcType=VARCHAR}
        </if>
        <if test="contactEmail != null and contactEmail != ''">
            and a.CONTACT_EMAIL = #{contactEmail,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark != ''">
            and a.REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="jsonInfo != null and jsonInfo != ''">
            and a.JSON_INFO = #{jsonInfo,jdbcType=OTHER}
        </if>
        <if test="createBeginTime != null and createEndTime != null">
            and a.CREATE_TIME between #{createBeginTime,jdbcType=TIMESTAMP} and #{createEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="withholdingTaxRatio != null">
            and a.WITHHOLDING_TAX_RATIO = #{withholdingTaxRatio,jdbcType=DECIMAL}
        </if>
        <if test="selfDeclared != null">
            and SELF_DECLARED = #{selfDeclared,jdbcType=SMALLINT}
        </if>
        <if test="maxId != null">
            and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
    </sql>


    <select id="listVoPage" parameterType="java.util.Map"
            resultType="com.zhixianghui.facade.merchant.vo.agent.AgentResVo">
        SELECT
        a.AGENT_NO as agentNo,
        a.AGENT_NAME as agentName,
        a.AGENT_TYPE as agentType,
        a.AGENT_STATUS as agentStatus,
        a.INVITER_NO as inviterNo,
        a.INVITER_NAME as inviterName,
        a.INVITATION_NUM as invitationNum,
        a.MER_NUM as merNum,
        a.WITHHOLDING_TAX_RATIO as taxPercent,
        a.SELF_DECLARED as selfDeclared,
        s.SALER_ID as salerId,
        s.SALER_NAME as salerName,
        a.CREATE_TIME as createTime
        FROM
        tbl_agent a
        LEFT JOIN tbl_agent_saler s on a.AGENT_NO = s.AGENT_NO
        <where>
            <include refid="condition_sql"/>
            <if test="salerIds != null and salerIds.size() > 0">
                and s.SALER_ID in
                <foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="salerId != null">
                and s.SALER_ID = #{salerId,jdbcType=BIGINT}
            </if>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns != ''">
                <![CDATA[ ORDER BY a.${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY a.ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <select id="countVoPage" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table"/>
        as a
        LEFT JOIN tbl_agent_saler s on a.AGENT_NO = s.AGENT_NO
        <where>
            <include refid="condition_sql"/>
            <if test="salerIds != null and salerIds.size() > 0">
                and s.SALER_ID in
                <foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="salerId != null">
                and s.SALER_ID = #{salerId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <update id="updateInvitationNum" parameterType="string">
        UPDATE
        <include refid="table"/>
        SET
        INVITATION_NUM = (SELECT b.countNum FROM (SELECT COUNT(*) as countNum FROM tbl_agent WHERE
        INVITER_NO=#{agentNo,jdbcType=BIGINT})as b),
        VERSION = VERSION + 1
        WHERE AGENT_NO = #{agentNo,jdbcType=BIGINT}
    </update>

    <update id="updateMerNum" parameterType="string">
        UPDATE
        <include refid="table"/>
        SET
        MER_NUM = (SELECT b.countNum FROM (SELECT COUNT(*) as countNum FROM tbl_merchant WHERE
        AGENT_NO=#{agentNo,jdbcType=BIGINT})as b),
        VERSION = VERSION + 1
        WHERE AGENT_NO = #{agentNo,jdbcType=BIGINT}
    </update>

    <update id="clearAsInviter" parameterType="string">
        UPDATE
        <include refid="table"/>
        SET
        INVITER_NO =null,
        INVITER_NAME =null,
        VERSION = VERSION + 1
        WHERE INVITER_NO = #{agentNo,jdbcType=BIGINT}
    </update>

    <update id="closeExpireTrialAgent" parameterType="map">

        update tbl_agent t set t.UPDATE_TIME = #{updateTime},t.AGENT_STATUS = 103
        where t.AUTH_STATUS = #{authStatus} AND t.AGENT_STATUS = #{authStatus} AND t.UPDATE_TIME <![CDATA[<= ]]> #{updateEndTime}

    </update>
</mapper>
