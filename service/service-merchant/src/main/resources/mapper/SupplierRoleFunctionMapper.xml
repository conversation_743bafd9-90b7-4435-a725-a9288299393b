<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRoleFunction">
	<sql id="table"> tbl_supplier_role_function </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRoleFunction">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="role_id" property="roleId" jdbcType="BIGINT"/>
		<result column="function_id" property="functionId" jdbcType="BIGINT"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		role_id,
		function_id
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRoleFunction">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	role_id ,
        	function_id 
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{roleId,jdbcType=BIGINT},
			#{functionId,jdbcType=BIGINT}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	role_id ,
        	function_id 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.roleId,jdbcType=BIGINT},
			#{item.functionId,jdbcType=BIGINT}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRoleFunction">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			role_id = #{roleId,jdbcType=BIGINT},
			function_id = #{functionId,jdbcType=BIGINT}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRoleFunction">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="roleId != null">
				role_id =#{roleId,jdbcType=BIGINT},
			</if>
			<if test="functionId != null">
				function_id =#{functionId,jdbcType=BIGINT},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE id = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById" parameterType="long">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="roleId != null">
			and role_id = #{roleId,jdbcType=BIGINT}
		</if>
		<if test="functionId != null">
			and function_id = #{functionId,jdbcType=BIGINT}
		</if>
	</sql>

	<!--按角色id删除-->
	<delete id="deleteByRoleId" parameterType="long">
		DELETE FROM <include refid="table" /> WHERE role_id = #{role_id,jdbcType=BIGINT}
	</delete>

	<!--按功能id删除-->
	<delete id="deleteByFunctionIds" parameterType="list">
		DELETE FROM <include refid="table" />
		WHERE function_id IN
		<foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
	</delete>
</mapper>

