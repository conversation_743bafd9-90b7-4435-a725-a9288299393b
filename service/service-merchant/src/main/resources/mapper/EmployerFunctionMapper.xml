<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction">
	<sql id="table"> portal_employer_function </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="type" property="type" jdbcType="SMALLINT"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="number" property="number" jdbcType="VARCHAR"/>
		<result column="parent_id" property="parentId" jdbcType="BIGINT"/>
		<result column="permission_flag" property="permissionFlag" jdbcType="VARCHAR"/>
		<result column="url" property="url" jdbcType="VARCHAR"/>
		<result column="extra_info" property="extraInfo" jdbcType="OTHER"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		update_time,
		type,
		name,
		number,
		parent_id,
		permission_flag,
		url,
		extra_info
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	type ,
        	name ,
        	number ,
        	parent_id ,
        	permission_flag ,
        	url ,
        	extra_info 
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{type,jdbcType=SMALLINT},
			#{name,jdbcType=VARCHAR},
			#{number,jdbcType=VARCHAR},
			#{parentId,jdbcType=BIGINT},
			#{permissionFlag,jdbcType=VARCHAR},
			#{url,jdbcType=VARCHAR},
			#{extraInfo,jdbcType=OTHER}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	type ,
        	name ,
        	number ,
        	parent_id ,
        	permission_flag ,
        	url ,
        	extra_info 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.type,jdbcType=SMALLINT},
			#{item.name,jdbcType=VARCHAR},
			#{item.number,jdbcType=VARCHAR},
			#{item.parentId,jdbcType=BIGINT},
			#{item.permissionFlag,jdbcType=VARCHAR},
			#{item.url,jdbcType=VARCHAR},
			#{item.extraInfo,jdbcType=OTHER}
			)
		</foreach>
	</insert>

	<insert id="importFile" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction">
		INSERT INTO <include refid="table" /> (
		version ,
		create_time ,
		update_time ,
		`type` ,
		`name` ,
		`number` ,
		parent_id ,
		permission_flag ,
		url ,
		extra_info
		) VALUES
		(
		0,
		#{createTime,jdbcType=TIMESTAMP},
		#{updateTime,jdbcType=TIMESTAMP},
		#{type,jdbcType=SMALLINT},
		#{name,jdbcType=VARCHAR},
		#{number,jdbcType=VARCHAR},
		#{parentId,jdbcType=BIGINT},
		#{permissionFlag,jdbcType=VARCHAR},
		#{url,jdbcType=VARCHAR},
		#{extraInfo,jdbcType=OTHER}
		)
		ON DUPLICATE KEY UPDATE
		update_time = CURRENT_TIMESTAMP,
		`number` = values(`number`),
		`parent_id` = values(`parent_id`)

	</insert>


	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			type = #{type,jdbcType=SMALLINT},
			name = #{name,jdbcType=VARCHAR},
			number = #{number,jdbcType=VARCHAR},
			parent_id = #{parentId,jdbcType=BIGINT},
			permission_flag = #{permissionFlag,jdbcType=VARCHAR},
			url = #{url,jdbcType=VARCHAR},
			extra_info = #{extraInfo,jdbcType=OTHER}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="type != null">
				type =#{type,jdbcType=SMALLINT},
			</if>
			<if test="name != null">
				name =#{name,jdbcType=VARCHAR},
			</if>
			<if test="number != null">
				number =#{number,jdbcType=VARCHAR},
			</if>
			<if test="parentId != null">
				parent_id =#{parentId,jdbcType=BIGINT},
			</if>
			<if test="permissionFlag != null">
				permission_flag =#{permissionFlag,jdbcType=VARCHAR},
			</if>
			<if test="url != null">
				url =#{url,jdbcType=VARCHAR},
			</if>
			<if test="extraInfo != null">
				extra_info =#{extraInfo,jdbcType=OTHER},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		order by `number` asc
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE id = #{id,jdbcType=BIGINT}  
	</select>

	<select id="getPermissionFlag" resultType="String">
		SELECT permission_flag FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="maxId != null">
			and id <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and update_time = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="type != null">
			and type = #{type,jdbcType=SMALLINT}
		</if>
		<if test="name != null">
			and name = #{name,jdbcType=VARCHAR}
		</if>
		<if test="number != null">
			and number = #{number,jdbcType=VARCHAR}
		</if>
		<if test="parentId != null">
			and parent_id = #{parentId,jdbcType=BIGINT}
		</if>
		<if test="permissionFlag != null">
			and permission_flag = #{permissionFlag,jdbcType=VARCHAR}
		</if>
		<if test="url != null">
			and url = #{url,jdbcType=VARCHAR}
		</if>
		<if test="extraInfo != null">
			and extra_info = #{extraInfo,jdbcType=OTHER}
		</if>
	</sql>

	<!--按多个id删除-->
	<delete id="deleteByIds" parameterType="list">
		DELETE FROM <include refid="table" />
		WHERE id IN
		<foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
	</delete>

	<!-- 根据上级功能id查询 -->
	<select id="listByParentId" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		where parent_id = #{parentId,jdbcType=BIGINT}
	</select>

	<!-- 根据查询 -->
	<select id="listByRoleId" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
			t1.id as id,
			t1.version as version,
			t1.create_time as create_time,
			t1.update_time as update_time,
			t1.type as type,
			t1.name as name,
			t1.number as number,
			t1.parent_id as parent_id,
			t1.permission_flag as permission_flag,
			t1.url as url,
			t1.extra_info as extra_info
		from
		portal_employer_function t1
			join portal_employer_role_function t2 on t1.id = t2.function_id
		where t2.role_id = #{roleId,jdbcType=BIGINT}
	</select>

	<!-- 根据查询 -->
	<select id="listByStaffId" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
			DISTINCT(t1.id) as id,
			t1.version as version,
			t1.create_time as create_time,
			t1.update_time as update_time,
			t1.type as type,
			t1.name as name,
			t1.number as number,
			t1.parent_id as parent_id,
			t1.permission_flag as permission_flag,
			t1.url as url,
			t1.extra_info as extra_info
		from
		portal_employer_function t1
			join portal_employer_role_function t2 on t1.id = t2.function_id
			join portal_employer_staff_role t3 on t2.role_id = t3.role_id
		where t3.staff_id = #{staffId,jdbcType=BIGINT}
	</select>
</mapper>

