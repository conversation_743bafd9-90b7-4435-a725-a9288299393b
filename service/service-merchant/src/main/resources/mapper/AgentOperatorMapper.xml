<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator">
	<sql id="table"> tbl_agent_operator </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="version" property="version" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="phone" property="phone" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="SMALLINT"/>
		<result column="is_init_pwd" property="isInitPwd" jdbcType="SMALLINT"/>
		<result column="salt" property="salt" jdbcType="VARCHAR"/>
		<result column="pwd" property="pwd" jdbcType="VARCHAR"/>
		<result column="pwd_valid_time" property="pwdValidTime" jdbcType="TIMESTAMP"/>
		<result column="mod_pwd_time" property="modPwdTime" jdbcType="TIMESTAMP"/>
		<result column="cur_login_time" property="curLoginTime" jdbcType="TIMESTAMP"/>
		<result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
		<result column="pwd_error_count" property="pwdErrorCount" jdbcType="SMALLINT"/>
		<result column="pwd_error_time" property="pwdErrorTime" jdbcType="TIMESTAMP"/>
		<result column="extra_info" property="extraInfo" jdbcType="OTHER"/>
		<result column="head_portrait_file_url" property="headPortraitFileUrl" jdbcType="VARCHAR"/>


	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		id,
		version,
		create_time,
		update_time,
		phone,
		name,
		status,
		is_init_pwd,
		salt,
		pwd,
		pwd_valid_time,
		mod_pwd_time,
		cur_login_time,
		last_login_time,
		pwd_error_count,
		pwd_error_time,
		extra_info,
		head_portrait_file_url
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	phone ,
        	name ,
        	status ,
        	is_init_pwd ,
        	salt ,
        	pwd ,
        	pwd_valid_time ,
        	mod_pwd_time ,
        	cur_login_time ,
        	last_login_time ,
        	pwd_error_count ,
        	pwd_error_time ,
        	extra_info ,
        	head_portrait_file_url
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{phone,jdbcType=VARCHAR},
			#{name,jdbcType=VARCHAR},
			#{status,jdbcType=SMALLINT},
			#{isInitPwd,jdbcType=SMALLINT},
			#{salt,jdbcType=VARCHAR},
			#{pwd,jdbcType=VARCHAR},
			#{pwdValidTime,jdbcType=TIMESTAMP},
			#{modPwdTime,jdbcType=TIMESTAMP},
			#{curLoginTime,jdbcType=TIMESTAMP},
			#{lastLoginTime,jdbcType=TIMESTAMP},
			#{pwdErrorCount,jdbcType=SMALLINT},
			#{pwdErrorTime,jdbcType=TIMESTAMP},
			#{extraInfo,jdbcType=OTHER},
			#{headPortraitFileUrl,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	version ,
        	create_time ,
        	update_time ,
        	phone ,
        	name ,
        	status ,
        	is_init_pwd ,
        	salt ,
        	pwd ,
        	pwd_valid_time ,
        	mod_pwd_time ,
        	cur_login_time ,
        	last_login_time ,
        	pwd_error_count ,
        	pwd_error_time ,
        	extra_info ,
			head_portrait_file_url
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.phone,jdbcType=VARCHAR},
			#{item.name,jdbcType=VARCHAR},
			#{item.status,jdbcType=SMALLINT},
			#{item.isInitPwd,jdbcType=SMALLINT},
			#{item.salt,jdbcType=VARCHAR},
			#{item.pwd,jdbcType=VARCHAR},
			#{item.pwdValidTime,jdbcType=TIMESTAMP},
			#{item.modPwdTime,jdbcType=TIMESTAMP},
			#{item.curLoginTime,jdbcType=TIMESTAMP},
			#{item.lastLoginTime,jdbcType=TIMESTAMP},
			#{item.pwdErrorCount,jdbcType=SMALLINT},
			#{item.pwdErrorTime,jdbcType=TIMESTAMP},
			#{item.extraInfo,jdbcType=OTHER}
			#{item.headPortraitFileUrl,jdbcType=VARCHAR},
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=INTEGER} + 1,
			create_time = #{createTime,jdbcType=TIMESTAMP},
			update_time = #{updateTime,jdbcType=TIMESTAMP},
			phone = #{phone,jdbcType=VARCHAR},
			name = #{name,jdbcType=VARCHAR},
			status = #{status,jdbcType=SMALLINT},
			is_init_pwd = #{isInitPwd,jdbcType=SMALLINT},
			salt = #{salt,jdbcType=VARCHAR},
			pwd = #{pwd,jdbcType=VARCHAR},
			pwd_valid_time = #{pwdValidTime,jdbcType=TIMESTAMP},
			mod_pwd_time = #{modPwdTime,jdbcType=TIMESTAMP},
			cur_login_time = #{curLoginTime,jdbcType=TIMESTAMP},
			last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
			pwd_error_count = #{pwdErrorCount,jdbcType=SMALLINT},
			pwd_error_time = #{pwdErrorTime,jdbcType=TIMESTAMP},
			extra_info = #{extraInfo,jdbcType=OTHER},
			head_portrait_file_url = #{headPortraitFileUrl,jdbcType=VARCHAR}
         WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=VARCHAR}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=INTEGER} +1,
			<if test="createTime != null">
				create_time =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				update_time =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="phone != null">
				phone =#{phone,jdbcType=VARCHAR},
			</if>
			<if test="name != null">
				name =#{name,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status =#{status,jdbcType=SMALLINT},
			</if>
			<if test="isInitPwd != null">
				is_init_pwd =#{isInitPwd,jdbcType=SMALLINT},
			</if>
			<if test="salt != null">
				salt =#{salt,jdbcType=VARCHAR},
			</if>
			<if test="pwd != null">
				pwd =#{pwd,jdbcType=VARCHAR},
			</if>
			<if test="pwdValidTime != null">
				pwd_valid_time =#{pwdValidTime,jdbcType=TIMESTAMP},
			</if>
			<if test="modPwdTime != null">
				mod_pwd_time =#{modPwdTime,jdbcType=TIMESTAMP},
			</if>
			<if test="curLoginTime != null">
				cur_login_time =#{curLoginTime,jdbcType=TIMESTAMP},
			</if>
			<if test="lastLoginTime != null">
				last_login_time =#{lastLoginTime,jdbcType=TIMESTAMP},
			</if>
			<if test="pwdErrorCount != null">
				pwd_error_count =#{pwdErrorCount,jdbcType=SMALLINT},
			</if>
			<if test="pwdErrorTime != null">
				pwd_error_time =#{pwdErrorTime,jdbcType=TIMESTAMP},
			</if>
			<if test="extraInfo != null">
				extra_info =#{extraInfo,jdbcType=OTHER},
			</if>
			<if test="headPortraitFileUrl != null">
				head_portrait_file_url =#{headPortraitFileUrl,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="listPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE id = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and id = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and version = #{version,jdbcType=INTEGER}
		</if>
		<if test="createTime != null">
			and create_time = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="createTimeBegin != null">
			and create_time <![CDATA[ >= ]]> #{createTimeBegin,jdbcType=TIMESTAMP}
		</if>
		<if test="createTimeEnd != null">
			and create_time <![CDATA[ <= ]]> #{createTimeEnd,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and update_time = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="phone != null">
			and phone = #{phone,jdbcType=VARCHAR}
		</if>
		<if test="name != null">
			and name = #{name,jdbcType=VARCHAR}
		</if>
		<if test="nameLike != null and nameLike !='' ">
			and name like concat('%',#{nameLike,jdbcType=VARCHAR},'%')
		</if>
		<if test="status != null">
			and status = #{status,jdbcType=SMALLINT}
		</if>
		<if test="isInitPwd != null">
			and is_init_pwd = #{isInitPwd,jdbcType=SMALLINT}
		</if>
		<if test="salt != null">
			and salt = #{salt,jdbcType=VARCHAR}
		</if>
		<if test="pwd != null">
			and pwd = #{pwd,jdbcType=VARCHAR}
		</if>
		<if test="pwdValidTime != null">
			and pwd_valid_time = #{pwdValidTime,jdbcType=TIMESTAMP}
		</if>
		<if test="modPwdTime != null">
			and mod_pwd_time = #{modPwdTime,jdbcType=TIMESTAMP}
		</if>
		<if test="curLoginTime != null">
			and cur_login_time = #{curLoginTime,jdbcType=TIMESTAMP}
		</if>
		<if test="lastLoginTime != null">
			and last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP}
		</if>
		<if test="pwdErrorCount != null">
			and pwd_error_count = #{pwdErrorCount,jdbcType=SMALLINT}
		</if>
		<if test="pwdErrorTime != null">
			and pwd_error_time = #{pwdErrorTime,jdbcType=TIMESTAMP}
		</if>
		<if test="extraInfo != null">
			and extra_info = #{extraInfo,jdbcType=OTHER}
		</if>
		<if test="head_portrait_file_url != null">
			and head_portrait_file_url = #{headPortraitFileUrl,jdbcType=VARCHAR}
		</if>
	</sql>

	<!-- 根据手机号查询 -->
	<select id="getByPhone" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE phone = #{phone,jdbcType=VARCHAR} limit 1
	</select>

	<!-- 操作员VO -->
	<resultMap id="AgentOperatorVO" type="com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO">
		<result column="id" property="id" jdbcType="BIGINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="phone" property="phone" jdbcType="VARCHAR"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="SMALLINT"/>
		<result column="head_portrait_file_url" property="headPortraitFileUrl" jdbcType="VARCHAR"/>
	</resultMap>

	<!-- 分页查询 -->
	<select id="listOperatorVoPage" parameterType="java.util.Map" resultMap="AgentOperatorVO">
		select
			id,
			create_time,
			update_time,
			phone,
			name,
			status,
			head_portrait_file_url
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		order by create_time desc
	</select>

	<!-- 分页查询计数 -->
	<select id="listOperatorVoPageCount" parameterType="java.util.Map" resultType="long">
		select count(1) from <include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
</mapper>

