<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantEnterprisePersonnel">
    <sql id="table">tbl_merchant_enterprise_personnel</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantEnterprisePersonnel">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="id_card_number" property="idCardNumber" jdbcType="VARCHAR"/>
        <result column="position" property="position" jdbcType="VARCHAR"/>
        <result column="is_legal" property="isLegal" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mch_no, `name`, id_card_number, `position`, is_legal, create_time
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEnterprisePersonnel">
        INSERT INTO <include refid="table" /> (
            mch_no,
            `name`,
            id_card_number,
            `position`,
            is_legal,
            create_time
        ) VALUES (
            #{mchNo,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{idCardNumber,jdbcType=VARCHAR},
            #{position,jdbcType=VARCHAR},
            #{isLegal,jdbcType=SMALLINT},
            #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            mch_no,
            `name`,
            id_card_number,
            `position`,
            is_legal,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR},
            #{item.idCardNumber,jdbcType=VARCHAR},
            #{item.position,jdbcType=VARCHAR},
            #{item.isLegal,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEnterprisePersonnel">
        UPDATE <include refid="table" /> SET
            mch_no = #{mchNo,jdbcType=VARCHAR},
            `name` = #{name,jdbcType=VARCHAR},
            id_card_number = #{idCardNumber,jdbcType=VARCHAR},
            `position` = #{position,jdbcType=VARCHAR},
            is_legal = #{isLegal,jdbcType=SMALLINT},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!--更新-->
    <update id="updateDto" parameterType="com.zhixianghui.facade.merchant.dto.EnterprisePersonnelDto">
        UPDATE <include refid="table" />
        <set>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="idCardNumber != null">
                id_card_number = #{idCardNumber,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                `position` = #{position,jdbcType=VARCHAR},
            </if>
            <if test="isLegal != null">
                is_legal = #{isLegal,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEnterprisePersonnel">
        UPDATE <include refid="table" />
        <set>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="idCardNumber != null">
                id_card_number = #{idCardNumber,jdbcType=VARCHAR},
            </if>
            <if test="position != null">
                `position` = #{position,jdbcType=VARCHAR},
            </if>
            <if test="isLegal != null">
                is_legal = #{isLegal,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        order by create_time
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByIdList" parameterType="list">
        DELETE FROM <include refid="table" /> WHERE id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and mch_no = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name !=''">
            and `name` = #{name,jdbcType=VARCHAR}
        </if>
        <if test="idCardNumber != null and idCardNumber !=''">
            and id_card_number = #{idCardNumber,jdbcType=VARCHAR}
        </if>
        <if test="position != null and position !=''">
            and `position` = #{position,jdbcType=VARCHAR}
        </if>
        <if test="isLegal != null">
            and is_legal = #{isLegal,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

</mapper>
