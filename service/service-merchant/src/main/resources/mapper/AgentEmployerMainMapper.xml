<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.merchant.entity.AgentEmployerMain">
    <sql id="table">tbl_agent_employer_main</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.AgentEmployerMain">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATER" property="updater" jdbcType="VARCHAR"/>
        <result column="AGENT_NO" property="agentNo" jdbcType="VARCHAR"/>
        <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR"/>
        <result column="TAX_NO" property="taxNo" jdbcType="VARCHAR"/>
        <result column="REGISTER_ADDR_PROVINCE" property="registerAddrProvince" jdbcType="VARCHAR"/>
        <result column="REGISTER_ADDR_CITY" property="registerAddrCity" jdbcType="VARCHAR"/>
        <result column="REGISTER_ADDR_TOWN" property="registerAddrTown" jdbcType="VARCHAR"/>
        <result column="REGISTER_ADDR_DETAIL" property="registerAddrDetail" jdbcType="VARCHAR"/>
        <result column="MANAGEMENT_SCOPE" property="managementScope" jdbcType="VARCHAR"/>
        <result column="MANAGEMENT_TERM_BEGIN" property="managementTermBegin" jdbcType="VARCHAR"/>
        <result column="MANAGEMENT_TERM_END" property="managementTermEnd" jdbcType="VARCHAR"/>
        <result column="MANAGEMENT_VALIDITY_DATE_TYPE" property="managementValidityDateType" jdbcType="SMALLINT"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, UPDATER, AGENT_NO, SHORT_NAME, TAX_NO, REGISTER_ADDR_PROVINCE, REGISTER_ADDR_CITY, REGISTER_ADDR_TOWN, REGISTER_ADDR_DETAIL, MANAGEMENT_SCOPE, MANAGEMENT_TERM_BEGIN, MANAGEMENT_TERM_END, MANAGEMENT_VALIDITY_DATE_TYPE
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.AgentEmployerMain">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            UPDATER,
            AGENT_NO,
            SHORT_NAME,
            TAX_NO,
            REGISTER_ADDR_PROVINCE,
            REGISTER_ADDR_CITY,
            REGISTER_ADDR_TOWN,
            REGISTER_ADDR_DETAIL,
            MANAGEMENT_SCOPE,
            MANAGEMENT_TERM_BEGIN,
            MANAGEMENT_TERM_END,
            MANAGEMENT_VALIDITY_DATE_TYPE
        ) VALUES (
            0,
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{updater,jdbcType=VARCHAR},
            #{agentNo,jdbcType=VARCHAR},
            #{shortName,jdbcType=VARCHAR},
            #{taxNo,jdbcType=VARCHAR},
            #{registerAddrProvince,jdbcType=VARCHAR},
            #{registerAddrCity,jdbcType=VARCHAR},
            #{registerAddrTown,jdbcType=VARCHAR},
            #{registerAddrDetail,jdbcType=VARCHAR},
            #{managementScope,jdbcType=VARCHAR},
            #{managementTermBegin,jdbcType=VARCHAR},
            #{managementTermEnd,jdbcType=VARCHAR},
            #{managementValidityDateType,jdbcType=SMALLINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            UPDATER,
            AGENT_NO,
            SHORT_NAME,
            TAX_NO,
            REGISTER_ADDR_PROVINCE,
            REGISTER_ADDR_CITY,
            REGISTER_ADDR_TOWN,
            REGISTER_ADDR_DETAIL,
            MANAGEMENT_SCOPE,
            MANAGEMENT_TERM_BEGIN,
            MANAGEMENT_TERM_END,
            MANAGEMENT_VALIDITY_DATE_TYPE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            0,
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updater,jdbcType=VARCHAR},
            #{item.agentNo,jdbcType=VARCHAR},
            #{item.shortName,jdbcType=VARCHAR},
            #{item.taxNo,jdbcType=VARCHAR},
            #{item.registerAddrProvince,jdbcType=VARCHAR},
            #{item.registerAddrCity,jdbcType=VARCHAR},
            #{item.registerAddrTown,jdbcType=VARCHAR},
            #{item.registerAddrDetail,jdbcType=VARCHAR},
            #{item.managementScope,jdbcType=VARCHAR},
            #{item.managementTermBegin,jdbcType=VARCHAR},
            #{item.managementTermEnd,jdbcType=VARCHAR},
            #{item.managementValidityDateType,jdbcType=SMALLINT}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.merchant.entity.AgentEmployerMain">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} +1,
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            UPDATER = #{updater,jdbcType=VARCHAR},
            AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            SHORT_NAME = #{shortName,jdbcType=VARCHAR},
            TAX_NO = #{taxNo,jdbcType=VARCHAR},
            REGISTER_ADDR_PROVINCE = #{registerAddrProvince,jdbcType=VARCHAR},
            REGISTER_ADDR_CITY = #{registerAddrCity,jdbcType=VARCHAR},
            REGISTER_ADDR_TOWN = #{registerAddrTown,jdbcType=VARCHAR},
            REGISTER_ADDR_DETAIL = #{registerAddrDetail,jdbcType=VARCHAR},
            MANAGEMENT_SCOPE = #{managementScope,jdbcType=VARCHAR},
            MANAGEMENT_TERM_BEGIN = #{managementTermBegin,jdbcType=VARCHAR},
            MANAGEMENT_TERM_END = #{managementTermEnd,jdbcType=VARCHAR},
            MANAGEMENT_VALIDITY_DATE_TYPE = #{managementValidityDateType,jdbcType=SMALLINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.AgentEmployerMain">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                SHORT_NAME = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="taxNo != null">
                TAX_NO = #{taxNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddrProvince != null">
                REGISTER_ADDR_PROVINCE = #{registerAddrProvince,jdbcType=VARCHAR},
            </if>
            <if test="registerAddrCity != null">
                REGISTER_ADDR_CITY = #{registerAddrCity,jdbcType=VARCHAR},
            </if>
            <if test="registerAddrTown != null">
                REGISTER_ADDR_TOWN = #{registerAddrTown,jdbcType=VARCHAR},
            </if>
            <if test="registerAddrDetail != null">
                REGISTER_ADDR_DETAIL = #{registerAddrDetail,jdbcType=VARCHAR},
            </if>
            <if test="managementScope != null">
                MANAGEMENT_SCOPE = #{managementScope,jdbcType=VARCHAR},
            </if>
            <if test="managementTermBegin != null">
                MANAGEMENT_TERM_BEGIN = #{managementTermBegin,jdbcType=VARCHAR},
            </if>
            <if test="managementTermEnd != null">
                MANAGEMENT_TERM_END = #{managementTermEnd,jdbcType=VARCHAR},
            </if>
            <if test="managementValidityDateType != null">
                MANAGEMENT_VALIDITY_DATE_TYPE = #{managementValidityDateType,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="deleteBy" parameterType="java.util.Map" resultType="int">
        delete FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updater != null and updater !=''">
            and UPDATER = #{updater,jdbcType=VARCHAR}
        </if>
        <if test="agentNo != null and agentNo !=''">
            and AGENT_NO = #{agentNo,jdbcType=VARCHAR}
        </if>
        <if test="shortName != null and shortName !=''">
            and SHORT_NAME = #{shortName,jdbcType=VARCHAR}
        </if>
        <if test="taxNo != null and taxNo !=''">
            and TAX_NO = #{taxNo,jdbcType=VARCHAR}
        </if>
        <if test="registerAddrProvince != null and registerAddrProvince !=''">
            and REGISTER_ADDR_PROVINCE = #{registerAddrProvince,jdbcType=VARCHAR}
        </if>
        <if test="registerAddrCity != null and registerAddrCity !=''">
            and REGISTER_ADDR_CITY = #{registerAddrCity,jdbcType=VARCHAR}
        </if>
        <if test="registerAddrTown != null and registerAddrTown !=''">
            and REGISTER_ADDR_TOWN = #{registerAddrTown,jdbcType=VARCHAR}
        </if>
        <if test="registerAddrDetail != null and registerAddrDetail !=''">
            and REGISTER_ADDR_DETAIL = #{registerAddrDetail,jdbcType=VARCHAR}
        </if>
        <if test="managementScope != null and managementScope !=''">
            and MANAGEMENT_SCOPE = #{managementScope,jdbcType=VARCHAR}
        </if>
        <if test="managementTermBegin != null and managementTermBegin !=''">
            and MANAGEMENT_TERM_BEGIN = #{managementTermBegin,jdbcType=VARCHAR}
        </if>
        <if test="managementTermEnd != null and managementTermEnd !=''">
            and MANAGEMENT_TERM_END = #{managementTermEnd,jdbcType=VARCHAR}
        </if>
        <if test="managementValidityDateType != null">
            and MANAGEMENT_VALIDITY_DATE_TYPE = #{managementValidityDateType,jdbcType=SMALLINT}
        </if>
    </sql>

</mapper>
