<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantEmployerMain">
	<sql id="table"> tbl_merchant_employer_main </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantEmployerMain">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR"/>
		<result column="TAX_NO" property="taxNo" jdbcType="VARCHAR"/>
		<result column="REGISTER_ADDR_PROVINCE" property="registerAddrProvince" jdbcType="VARCHAR"/>
		<result column="REGISTER_ADDR_CITY" property="registerAddrCity" jdbcType="VARCHAR"/>
		<result column="REGISTER_ADDR_TOWN" property="registerAddrTown" jdbcType="VARCHAR"/>
		<result column="REGISTER_ADDR_DETAIL" property="registerAddrDetail" jdbcType="VARCHAR"/>
		<result column="REGISTER_AMOUNT" property="registerAmount" jdbcType="DECIMAL"/>
		<result column="MANAGEMENT_SCOPE" property="managementScope" jdbcType="VARCHAR"/>
		<result column="MANAGEMENT_TERM_BEGIN" property="managementTermBegin" jdbcType="VARCHAR"/>
		<result column="MANAGEMENT_TERM_END" property="managementTermEnd" jdbcType="VARCHAR"/>
		<result column="MANAGEMENT_VALIDITY_DATE_TYPE" property="managementValidityDateType" jdbcType="SMALLINT"/>
		<result column="CERTIFICATE_TYPE" property="certificateType" jdbcType="SMALLINT"/>
		<result column="LEGAL_PERSON_NAME" property="legalPersonName" jdbcType="VARCHAR"/>
		<result column="CERTIFICATE_NUMBER" property="certificateNumber" jdbcType="VARCHAR"/>
		<result column="CERTIFICATE_TERM_BEGIN" property="certificateTermBegin" jdbcType="VARCHAR"/>
		<result column="CERTIFICATE_TERM_END" property="certificateTermEnd" jdbcType="VARCHAR"/>
		<result column="CERTIFICATE_VALIDITY_DATE_TYPE" property="certificateValidityDateType" jdbcType="SMALLINT"/>
		<result column="MANAGEMENT_ADDR_PROVINCE" property="managementAddrProvince" jdbcType="VARCHAR"/>
		<result column="MANAGEMENT_ADDR_CITY" property="managementAddrCity" jdbcType="VARCHAR"/>
		<result column="MANAGEMENT_ADDR_TOWN" property="managementAddrTown" jdbcType="VARCHAR"/>
		<result column="MANAGEMENT_ADDR_DETAIL" property="managementAddrDetail" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		VERSION,
		CREATE_TIME,
		UPDATE_TIME,
		UPDATOR,
		MCH_NO,
		SHORT_NAME,
		TAX_NO,
		REGISTER_ADDR_PROVINCE,
		REGISTER_ADDR_CITY,
		REGISTER_ADDR_TOWN,
		REGISTER_ADDR_DETAIL,
		REGISTER_AMOUNT,
		MANAGEMENT_SCOPE,
		MANAGEMENT_TERM_BEGIN,
		MANAGEMENT_TERM_END,
		MANAGEMENT_VALIDITY_DATE_TYPE,
		CERTIFICATE_TYPE,
		LEGAL_PERSON_NAME,
		CERTIFICATE_NUMBER,
		CERTIFICATE_TERM_BEGIN,
		CERTIFICATE_TERM_END,
		CERTIFICATE_VALIDITY_DATE_TYPE,
		MANAGEMENT_ADDR_PROVINCE,
		MANAGEMENT_ADDR_CITY,
		MANAGEMENT_ADDR_TOWN,
		MANAGEMENT_ADDR_DETAIL
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerMain">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	UPDATOR ,
        	MCH_NO ,
        	SHORT_NAME ,
        	TAX_NO ,
        	REGISTER_ADDR_PROVINCE ,
        	REGISTER_ADDR_CITY ,
        	REGISTER_ADDR_TOWN ,
        	REGISTER_ADDR_DETAIL ,
        	REGISTER_AMOUNT ,
        	MANAGEMENT_SCOPE ,
        	MANAGEMENT_TERM_BEGIN ,
        	MANAGEMENT_TERM_END ,
        	MANAGEMENT_VALIDITY_DATE_TYPE ,
        	CERTIFICATE_TYPE ,
        	LEGAL_PERSON_NAME ,
        	CERTIFICATE_NUMBER ,
        	CERTIFICATE_TERM_BEGIN ,
        	CERTIFICATE_TERM_END ,
        	CERTIFICATE_VALIDITY_DATE_TYPE ,
        	MANAGEMENT_ADDR_PROVINCE ,
        	MANAGEMENT_ADDR_CITY ,
        	MANAGEMENT_ADDR_TOWN ,
        	MANAGEMENT_ADDR_DETAIL 
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{updator,jdbcType=VARCHAR},
			#{mchNo,jdbcType=VARCHAR},
			#{shortName,jdbcType=VARCHAR},
			#{taxNo,jdbcType=VARCHAR},
			#{registerAddrProvince,jdbcType=VARCHAR},
			#{registerAddrCity,jdbcType=VARCHAR},
			#{registerAddrTown,jdbcType=VARCHAR},
			#{registerAddrDetail,jdbcType=VARCHAR},
			#{registerAmount,jdbcType=DECIMAL},
			#{managementScope,jdbcType=VARCHAR},
			#{managementTermBegin,jdbcType=VARCHAR},
			#{managementTermEnd,jdbcType=VARCHAR},
			#{managementValidityDateType,jdbcType=SMALLINT},
			#{certificateType,jdbcType=SMALLINT},
			#{legalPersonName,jdbcType=VARCHAR},
			#{certificateNumber,jdbcType=VARCHAR},
			#{certificateTermBegin,jdbcType=VARCHAR},
			#{certificateTermEnd,jdbcType=VARCHAR},
			#{certificateValidityDateType,jdbcType=SMALLINT},
			#{managementAddrProvince,jdbcType=VARCHAR},
			#{managementAddrCity,jdbcType=VARCHAR},
			#{managementAddrTown,jdbcType=VARCHAR},
			#{managementAddrDetail,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	UPDATOR ,
        	MCH_NO ,
        	SHORT_NAME ,
        	TAX_NO ,
        	REGISTER_ADDR_PROVINCE ,
        	REGISTER_ADDR_CITY ,
        	REGISTER_ADDR_TOWN ,
        	REGISTER_ADDR_DETAIL ,
        	REGISTER_AMOUNT ,
        	MANAGEMENT_SCOPE ,
        	MANAGEMENT_TERM_BEGIN ,
        	MANAGEMENT_TERM_END ,
        	MANAGEMENT_VALIDITY_DATE_TYPE ,
        	CERTIFICATE_TYPE ,
        	LEGAL_PERSON_NAME ,
        	CERTIFICATE_NUMBER ,
        	CERTIFICATE_TERM_BEGIN ,
        	CERTIFICATE_TERM_END ,
        	CERTIFICATE_VALIDITY_DATE_TYPE ,
        	MANAGEMENT_ADDR_PROVINCE ,
        	MANAGEMENT_ADDR_CITY ,
        	MANAGEMENT_ADDR_TOWN ,
        	MANAGEMENT_ADDR_DETAIL 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.updator,jdbcType=VARCHAR},
			#{item.mchNo,jdbcType=VARCHAR},
			#{item.shortName,jdbcType=VARCHAR},
			#{item.taxNo,jdbcType=VARCHAR},
			#{item.registerAddrProvince,jdbcType=VARCHAR},
			#{item.registerAddrCity,jdbcType=VARCHAR},
			#{item.registerAddrTown,jdbcType=VARCHAR},
			#{item.registerAddrDetail,jdbcType=VARCHAR},
			#{item.registerAmount,jdbcType=DECIMAL},
			#{item.managementScope,jdbcType=VARCHAR},
			#{item.managementTermBegin,jdbcType=VARCHAR},
			#{item.managementTermEnd,jdbcType=VARCHAR},
			#{item.managementValidityDateType,jdbcType=SMALLINT},
			#{item.certificateType,jdbcType=SMALLINT},
			#{item.legalPersonName,jdbcType=VARCHAR},
			#{item.certificateNumber,jdbcType=VARCHAR},
			#{item.certificateTermBegin,jdbcType=VARCHAR},
			#{item.certificateTermEnd,jdbcType=VARCHAR},
			#{item.certificateValidityDateType,jdbcType=SMALLINT},
			#{item.managementAddrProvince,jdbcType=VARCHAR},
			#{item.managementAddrCity,jdbcType=VARCHAR},
			#{item.managementAddrTown,jdbcType=VARCHAR},
			#{item.managementAddrDetail,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerMain">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			UPDATOR = #{updator,jdbcType=VARCHAR},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			SHORT_NAME = #{shortName,jdbcType=VARCHAR},
			TAX_NO = #{taxNo,jdbcType=VARCHAR},
			REGISTER_ADDR_PROVINCE = #{registerAddrProvince,jdbcType=VARCHAR},
			REGISTER_ADDR_CITY = #{registerAddrCity,jdbcType=VARCHAR},
			REGISTER_ADDR_TOWN = #{registerAddrTown,jdbcType=VARCHAR},
			REGISTER_ADDR_DETAIL = #{registerAddrDetail,jdbcType=VARCHAR},
			REGISTER_AMOUNT = #{registerAmount,jdbcType=DECIMAL},
			MANAGEMENT_SCOPE = #{managementScope,jdbcType=VARCHAR},
			MANAGEMENT_TERM_BEGIN = #{managementTermBegin,jdbcType=VARCHAR},
			MANAGEMENT_TERM_END = #{managementTermEnd,jdbcType=VARCHAR},
			MANAGEMENT_VALIDITY_DATE_TYPE = #{managementValidityDateType,jdbcType=SMALLINT},
			CERTIFICATE_TYPE = #{certificateType,jdbcType=SMALLINT},
			LEGAL_PERSON_NAME = #{legalPersonName,jdbcType=VARCHAR},
			CERTIFICATE_NUMBER = #{certificateNumber,jdbcType=VARCHAR},
			CERTIFICATE_TERM_BEGIN = #{certificateTermBegin,jdbcType=VARCHAR},
			CERTIFICATE_TERM_END = #{certificateTermEnd,jdbcType=VARCHAR},
			CERTIFICATE_VALIDITY_DATE_TYPE = #{certificateValidityDateType,jdbcType=SMALLINT},
			MANAGEMENT_ADDR_PROVINCE = #{managementAddrProvince,jdbcType=VARCHAR},
			MANAGEMENT_ADDR_CITY = #{managementAddrCity,jdbcType=VARCHAR},
			MANAGEMENT_ADDR_TOWN = #{managementAddrTown,jdbcType=VARCHAR},
			MANAGEMENT_ADDR_DETAIL = #{managementAddrDetail,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerMain">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updator != null">
				UPDATOR =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="mchNo != null">
				MCH_NO =#{mchNo,jdbcType=VARCHAR},
			</if>
			<if test="shortName != null">
				SHORT_NAME =#{shortName,jdbcType=VARCHAR},
			</if>
			<if test="taxNo != null">
				TAX_NO =#{taxNo,jdbcType=VARCHAR},
			</if>
			<if test="registerAddrProvince != null">
				REGISTER_ADDR_PROVINCE =#{registerAddrProvince,jdbcType=VARCHAR},
			</if>
			<if test="registerAddrCity != null">
				REGISTER_ADDR_CITY =#{registerAddrCity,jdbcType=VARCHAR},
			</if>
			<if test="registerAddrTown != null">
				REGISTER_ADDR_TOWN =#{registerAddrTown,jdbcType=VARCHAR},
			</if>
			<if test="registerAddrDetail != null">
				REGISTER_ADDR_DETAIL =#{registerAddrDetail,jdbcType=VARCHAR},
			</if>
			<if test="registerAmount != null">
				REGISTER_AMOUNT =#{registerAmount,jdbcType=DECIMAL},
			</if>
			<if test="managementScope != null">
				MANAGEMENT_SCOPE =#{managementScope,jdbcType=VARCHAR},
			</if>
			<if test="managementTermBegin != null">
				MANAGEMENT_TERM_BEGIN =#{managementTermBegin,jdbcType=VARCHAR},
			</if>
			<if test="managementTermEnd != null">
				MANAGEMENT_TERM_END =#{managementTermEnd,jdbcType=VARCHAR},
			</if>
			<if test="managementValidityDateType != null">
				MANAGEMENT_VALIDITY_DATE_TYPE =#{managementValidityDateType,jdbcType=SMALLINT},
			</if>
			<if test="certificateType != null">
				CERTIFICATE_TYPE =#{certificateType,jdbcType=SMALLINT},
			</if>
			<if test="legalPersonName != null">
				LEGAL_PERSON_NAME =#{legalPersonName,jdbcType=VARCHAR},
			</if>
			<if test="certificateNumber != null">
				CERTIFICATE_NUMBER =#{certificateNumber,jdbcType=VARCHAR},
			</if>
			<if test="certificateTermBegin != null">
				CERTIFICATE_TERM_BEGIN =#{certificateTermBegin,jdbcType=VARCHAR},
			</if>
			<if test="certificateTermEnd != null">
				CERTIFICATE_TERM_END =#{certificateTermEnd,jdbcType=VARCHAR},
			</if>
			<if test="certificateValidityDateType != null">
				CERTIFICATE_VALIDITY_DATE_TYPE =#{certificateValidityDateType,jdbcType=SMALLINT},
			</if>
			<if test="managementAddrProvince != null">
				MANAGEMENT_ADDR_PROVINCE =#{managementAddrProvince,jdbcType=VARCHAR},
			</if>
			<if test="managementAddrCity != null">
				MANAGEMENT_ADDR_CITY =#{managementAddrCity,jdbcType=VARCHAR},
			</if>
			<if test="managementAddrTown != null">
				MANAGEMENT_ADDR_TOWN =#{managementAddrTown,jdbcType=VARCHAR},
			</if>
			<if test="managementAddrDetail != null">
				MANAGEMENT_ADDR_DETAIL =#{managementAddrDetail,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="shortName != null and shortName !=''">
			and SHORT_NAME = #{shortName,jdbcType=VARCHAR}
		</if>
		<if test="taxNo != null and taxNo !=''">
			and TAX_NO = #{taxNo,jdbcType=VARCHAR}
		</if>
		<if test="registerAddrProvince != null and registerAddrProvince !=''">
			and REGISTER_ADDR_PROVINCE = #{registerAddrProvince,jdbcType=VARCHAR}
		</if>
		<if test="registerAddrCity != null and registerAddrCity !=''">
			and REGISTER_ADDR_CITY = #{registerAddrCity,jdbcType=VARCHAR}
		</if>
		<if test="registerAddrTown != null and registerAddrTown !=''">
			and REGISTER_ADDR_TOWN = #{registerAddrTown,jdbcType=VARCHAR}
		</if>
		<if test="registerAddrDetail != null and registerAddrDetail !=''">
			and REGISTER_ADDR_DETAIL = #{registerAddrDetail,jdbcType=VARCHAR}
		</if>
		<if test="registerAmount != null">
			and REGISTER_AMOUNT = #{registerAmount,jdbcType=DECIMAL}
		</if>
		<if test="managementScope != null and managementScope !=''">
			and MANAGEMENT_SCOPE = #{managementScope,jdbcType=VARCHAR}
		</if>
		<if test="managementTermBegin != null and managementTermBegin !=''">
			and MANAGEMENT_TERM_BEGIN = #{managementTermBegin,jdbcType=VARCHAR}
		</if>
		<if test="managementTermEnd != null and managementTermEnd !=''">
			and MANAGEMENT_TERM_END = #{managementTermEnd,jdbcType=VARCHAR}
		</if>
		<if test="managementValidityDateType != null">
			and MANAGEMENT_VALIDITY_DATE_TYPE = #{managementValidityDateType,jdbcType=SMALLINT}
		</if>
		<if test="certificateType != null">
			and CERTIFICATE_TYPE = #{certificateType,jdbcType=SMALLINT}
		</if>
		<if test="legalPersonName != null and legalPersonName !=''">
			and LEGAL_PERSON_NAME = #{legalPersonName,jdbcType=VARCHAR}
		</if>
		<if test="certificateNumber != null and certificateNumber !=''">
			and CERTIFICATE_NUMBER = #{certificateNumber,jdbcType=VARCHAR}
		</if>
		<if test="certificateTermBegin != null and certificateTermBegin !=''">
			and CERTIFICATE_TERM_BEGIN = #{certificateTermBegin,jdbcType=VARCHAR}
		</if>
		<if test="certificateTermEnd != null and certificateTermEnd !=''">
			and CERTIFICATE_TERM_END = #{certificateTermEnd,jdbcType=VARCHAR}
		</if>
		<if test="certificateValidityDateType != null">
			and CERTIFICATE_VALIDITY_DATE_TYPE = #{certificateValidityDateType,jdbcType=SMALLINT}
		</if>
		<if test="managementAddrProvince != null and managementAddrProvince !=''">
			and MANAGEMENT_ADDR_PROVINCE = #{managementAddrProvince,jdbcType=VARCHAR}
		</if>
		<if test="managementAddrCity != null and managementAddrCity !=''">
			and MANAGEMENT_ADDR_CITY = #{managementAddrCity,jdbcType=VARCHAR}
		</if>
		<if test="managementAddrTown != null and managementAddrTown !=''">
			and MANAGEMENT_ADDR_TOWN = #{managementAddrTown,jdbcType=VARCHAR}
		</if>
		<if test="managementAddrDetail != null and managementAddrDetail !=''">
			and MANAGEMENT_ADDR_DETAIL = #{managementAddrDetail,jdbcType=VARCHAR}
		</if>
	</sql>
</mapper>

