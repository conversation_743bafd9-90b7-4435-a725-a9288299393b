<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.merchant.core.dao.mapper.MerchantNotifySetMapper">
    <sql id="table">tbl_merchant_notify</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantNotifySet">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="notify_type" property="notifyType" jdbcType="SMALLINT"/>
        <result column="notify_url_type" property="notifyUrlType" jdbcType="VARCHAR"/>
        <result column="notify_url" property="notifyUrl" jdbcType="VARCHAR"/>
        <result column="notify_status" property="notifyStatus" jdbcType="SMALLINT"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, create_time, mch_no, notify_type, notify_url_type, notify_url, notify_status
    </sql>

</mapper>
