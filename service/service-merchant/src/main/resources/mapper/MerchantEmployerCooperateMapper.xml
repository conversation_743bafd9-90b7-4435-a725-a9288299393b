<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate">
	<sql id="table"> tbl_merchant_employer_cooperate </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="INDUSTRY_TYPE_CODE" property="industryTypeCode" jdbcType="VARCHAR"/>
		<result column="INDUSTRY_TYPE_NAME" property="industryTypeName" jdbcType="VARCHAR"/>
		<result column="WORKER_NUM" property="workerNum" jdbcType="SMALLINT"/>
		<result column="SIGN_RATE_LEVEL" property="signRateLevel" jdbcType="SMALLINT"/>
		<result column="WORKER_MONTH_INCOME_RATE" property="workerMonthIncomeRate" jdbcType="DECIMAL"/>
		<result column="MONTH_MONEY_SLIP" property="monthMoneySlip" jdbcType="DECIMAL"/>
		<result column="PROVIDE_INCOME_DETAIL_TYPE" property="provideIncomeDetailType" jdbcType="SMALLINT"/>
		<result column="COMPANY_WEBSITE" property="companyWebsite" jdbcType="VARCHAR"/>
		<result column="BIZ_PLATFORM_NAME" property="bizPlatformName" jdbcType="VARCHAR"/>
		<result column="JSON_INFO" property="jsonInfo" jdbcType="OTHER"/>
		<result column="PROFESSION_TYPE" property="professionType" jdbcType="VARCHAR"/>
		<result column="PROFESSION_NAME" property="professionName" jdbcType="VARCHAR"/>
		<result column="QUALIFICATION_CODE" property="qualificationCode" jdbcType="VARCHAR"/>
		<result column="QUALIFICATION_URL" property="qualificationUrl" jdbcType="VARCHAR"/>
		<result column="SERVICE_TYPE" property="serviceType" jdbcType="VARCHAR"/>
		<result column="ALIPAY_APP_NAME" property="alipayAppName" jdbcType="VARCHAR"/>
		<result column="APP_NAME" property="appName" jdbcType="VARCHAR"/>
		<result column="WAP_NAME" property="wapName" jdbcType="VARCHAR"/>
		<result column="WAP_SITE" property="wapSite" jdbcType="VARCHAR"/>
		<result column="PC_NAME" property="pcName" jdbcType="VARCHAR"/>
		<result column="PC_SITE" property="pcSite" jdbcType="VARCHAR"/>
		<result column="SMID" property="smid" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		UPDATE_TIME,
		VERSION,
		UPDATOR,
		MCH_NO,
		INDUSTRY_TYPE_CODE,
		INDUSTRY_TYPE_NAME,
		WORKER_NUM,
		SIGN_RATE_LEVEL,
		WORKER_MONTH_INCOME_RATE,
		MONTH_MONEY_SLIP,
		PROVIDE_INCOME_DETAIL_TYPE,
		COMPANY_WEBSITE,
		BIZ_PLATFORM_NAME,
		JSON_INFO,
		PROFESSION_TYPE,
		PROFESSION_NAME,
		QUALIFICATION_CODE,
		QUALIFICATION_URL,
		SERVICE_TYPE,
		ALIPAY_APP_NAME,
		APP_NAME,
		WAP_NAME,
		WAP_SITE,
		PC_NAME,
		PC_SITE,
		SMID
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
        	INDUSTRY_TYPE_CODE ,
        	INDUSTRY_TYPE_NAME ,
        	WORKER_NUM ,
        	SIGN_RATE_LEVEL ,
        	WORKER_MONTH_INCOME_RATE ,
        	MONTH_MONEY_SLIP ,
        	PROVIDE_INCOME_DETAIL_TYPE ,
        	COMPANY_WEBSITE ,
        	BIZ_PLATFORM_NAME ,
        	JSON_INFO,
			PROFESSION_TYPE,
			PROFESSION_NAME,
			QUALIFICATION_CODE,
			QUALIFICATION_URL,
			SERVICE_TYPE,
			ALIPAY_APP_NAME,
			APP_NAME,
			WAP_NAME,
			WAP_SITE,
			PC_NAME,
			PC_SITE,
			SMID
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			0,
			#{updator,jdbcType=VARCHAR},
			#{mchNo,jdbcType=VARCHAR},
			#{industryTypeCode,jdbcType=VARCHAR},
			#{industryTypeName,jdbcType=VARCHAR},
			#{workerNum,jdbcType=SMALLINT},
			#{signRateLevel,jdbcType=SMALLINT},
			#{workerMonthIncomeRate,jdbcType=DECIMAL},
			#{monthMoneySlip,jdbcType=DECIMAL},
			#{provideIncomeDetailType,jdbcType=SMALLINT},
			#{companyWebsite,jdbcType=VARCHAR},
			#{bizPlatformName,jdbcType=VARCHAR},
			#{jsonInfo,jdbcType=OTHER},
			#{professionType,jdbcType=VARCHAR},
			#{professionName,jdbcType=VARCHAR},
			#{qualificationCode,jdbcType=VARCHAR},
			#{qualificationUrl,jdbcType=VARCHAR},
			#{serviceType,jdbcType=VARCHAR},
			#{alipayAppName,jdbcType=VARCHAR},
			#{appName,jdbcType=VARCHAR},
			#{wapName,jdbcType=VARCHAR},
			#{wapSite,jdbcType=VARCHAR},
			#{pcName,jdbcType=VARCHAR},
			#{pcSite,jdbcType=VARCHAR},
			#{smid,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	MCH_NO ,
        	INDUSTRY_TYPE_CODE ,
        	INDUSTRY_TYPE_NAME ,
        	WORKER_NUM ,
        	SIGN_RATE_LEVEL ,
        	WORKER_MONTH_INCOME_RATE ,
        	MONTH_MONEY_SLIP ,
        	PROVIDE_INCOME_DETAIL_TYPE ,
        	COMPANY_WEBSITE ,
        	BIZ_PLATFORM_NAME ,
        	JSON_INFO,
			PROFESSION_TYPE,
			PROFESSION_NAME,
			QUALIFICATION_CODE,
			QUALIFICATION_URL,
			SERVICE_TYPE,
			ALIPAY_APP_NAME,
			APP_NAME,
			WAP_NAME,
			WAP_SITE,
			PC_NAME,
			PC_SITE,
			SMID
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			0,
			#{item.updator,jdbcType=VARCHAR},
			#{item.mchNo,jdbcType=VARCHAR},
			#{item.industryTypeCode,jdbcType=VARCHAR},
			#{item.industryTypeName,jdbcType=VARCHAR},
			#{item.workerNum,jdbcType=SMALLINT},
			#{item.signRateLevel,jdbcType=SMALLINT},
			#{item.workerMonthIncomeRate,jdbcType=DECIMAL},
			#{item.monthMoneySlip,jdbcType=DECIMAL},
			#{item.provideIncomeDetailType,jdbcType=SMALLINT},
			#{item.companyWebsite,jdbcType=VARCHAR},
			#{item.bizPlatformName,jdbcType=VARCHAR},
			#{item.jsonInfo,jdbcType=OTHER},
			#{item.professionType,jdbcType=VARCHAR},
			#{item.professionName,jdbcType=VARCHAR},
			#{item.qualificationCode,jdbcType=VARCHAR},
			#{item.qualificationUrl,jdbcType=VARCHAR},
			#{item.serviceType,jdbcType=VARCHAR},
			#{item.alipayAppName,jdbcType=VARCHAR},
			#{item.appName,jdbcType=VARCHAR},
			#{item.wapName,jdbcType=VARCHAR},
			#{item.wapSite,jdbcType=VARCHAR},
			#{item.pcName,jdbcType=VARCHAR},
			#{item.pcSite,jdbcType=VARCHAR},
			#{item.smid,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			INDUSTRY_TYPE_CODE = #{industryTypeCode,jdbcType=VARCHAR},
			INDUSTRY_TYPE_NAME = #{industryTypeName,jdbcType=VARCHAR},
			WORKER_NUM = #{workerNum,jdbcType=SMALLINT},
			SIGN_RATE_LEVEL = #{signRateLevel,jdbcType=SMALLINT},
			WORKER_MONTH_INCOME_RATE = #{workerMonthIncomeRate,jdbcType=DECIMAL},
			MONTH_MONEY_SLIP = #{monthMoneySlip,jdbcType=DECIMAL},
			PROVIDE_INCOME_DETAIL_TYPE = #{provideIncomeDetailType,jdbcType=SMALLINT},
			COMPANY_WEBSITE = #{companyWebsite,jdbcType=VARCHAR},
			BIZ_PLATFORM_NAME = #{bizPlatformName,jdbcType=VARCHAR},
			JSON_INFO = #{jsonInfo,jdbcType=OTHER},
			PROFESSION_TYPE = #{professionType,jdbcType=VARCHAR},
			PROFESSION_NAME = #{professionName,jdbcType=VARCHAR},
			QUALIFICATION_CODE = #{qualificationCode,jdbcType=VARCHAR},
			QUALIFICATION_URL = #{qualificationUrl,jdbcType=VARCHAR},
			SERVICE_TYPE = #{serviceType,jdbcType=VARCHAR},
			ALIPAY_APP_NAME = #{alipayAppName,jdbcType=VARCHAR},
			APP_NAME = #{appName,jdbcType=VARCHAR},
			WAP_NAME = #{wapName,jdbcType=VARCHAR},
			WAP_SITE = #{wapSite,jdbcType=VARCHAR},
			PC_NAME = #{pcName,jdbcType=VARCHAR},
			PC_SITE = #{pcSite,jdbcType=VARCHAR},
			SMID = #{smid,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate">
		UPDATE <include refid="table" />
		<set>
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="updator != null">
				UPDATOR =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="mchNo != null">
				MCH_NO =#{mchNo,jdbcType=VARCHAR},
			</if>
			<if test="industryTypeCode != null">
				INDUSTRY_TYPE_CODE =#{industryTypeCode,jdbcType=VARCHAR},
			</if>
			<if test="industryTypeName != null">
				INDUSTRY_TYPE_NAME =#{industryTypeName,jdbcType=VARCHAR},
			</if>
			<if test="workerNum != null">
				WORKER_NUM =#{workerNum,jdbcType=SMALLINT},
			</if>
			<if test="signRateLevel != null">
				SIGN_RATE_LEVEL =#{signRateLevel,jdbcType=SMALLINT},
			</if>
			<if test="workerMonthIncomeRate != null">
				WORKER_MONTH_INCOME_RATE =#{workerMonthIncomeRate,jdbcType=DECIMAL},
			</if>
			<if test="monthMoneySlip != null">
				MONTH_MONEY_SLIP =#{monthMoneySlip,jdbcType=DECIMAL},
			</if>
			<if test="provideIncomeDetailType != null">
				PROVIDE_INCOME_DETAIL_TYPE =#{provideIncomeDetailType,jdbcType=SMALLINT},
			</if>
			<if test="companyWebsite != null">
				COMPANY_WEBSITE =#{companyWebsite,jdbcType=VARCHAR},
			</if>
			<if test="bizPlatformName != null">
				BIZ_PLATFORM_NAME =#{bizPlatformName,jdbcType=VARCHAR},
			</if>
			<if test="jsonInfo != null">
				JSON_INFO =#{jsonInfo,jdbcType=OTHER},
			</if>
			<if test="professionType != null">
				PROFESSION_TYPE = #{professionType,jdbcType=VARCHAR},
			</if>
			<if test="professionName != null">
				PROFESSION_NAME = #{professionName,jdbcType=VARCHAR},
			</if>
			<if test="qualificationCode != null">
				QUALIFICATION_CODE = #{qualificationCode,jdbcType=VARCHAR},
			</if>
			<if test="qualificationUrl != null">
				QUALIFICATION_URL = #{qualificationUrl,jdbcType=VARCHAR},
			</if>
			<if test="serviceType != null">
				SERVICE_TYPE = #{serviceType,jdbcType=VARCHAR},
			</if>
			<if test="alipayAppName != null">
				ALIPAY_APP_NAME = #{alipayAppName,jdbcType=VARCHAR},
			</if>
			<if test="appName != null">
				APP_NAME = #{appName,jdbcType=VARCHAR},
			</if>
			<if test="wapName != null">
				WAP_NAME = #{wapName,jdbcType=VARCHAR},
			</if>
			<if test="wapSite != null">
				WAP_SITE = #{wapSite,jdbcType=VARCHAR},
			</if>
			<if test="pcName != null">
				PC_NAME = #{pcName,jdbcType=VARCHAR},
			</if>
			<if test="pcSite != null">
				PC_SITE = #{pcSite,jdbcType=VARCHAR},
			</if>
			<if test="smid != null">
				SMID = #{smid,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!--条件删除-->
	<delete id="deleteBy" parameterType="java.util.Map">
		DELETE FROM <include refid="table" />
		where
		<trim  prefixOverrides="AND">
			<include refid="condition_sql" />
		</trim>
	</delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="industryTypeCode != null and industryTypeCode !=''">
			and INDUSTRY_TYPE_CODE = #{industryTypeCode,jdbcType=VARCHAR}
		</if>
		<if test="industryTypeName != null and industryTypeName !=''">
			and INDUSTRY_TYPE_NAME = #{industryTypeName,jdbcType=VARCHAR}
		</if>
		<if test="workerNum != null">
			and WORKER_NUM = #{workerNum,jdbcType=SMALLINT}
		</if>
		<if test="signRateLevel != null">
			and SIGN_RATE_LEVEL = #{signRateLevel,jdbcType=SMALLINT}
		</if>
		<if test="workerMonthIncomeRate != null">
			and WORKER_MONTH_INCOME_RATE = #{workerMonthIncomeRate,jdbcType=DECIMAL}
		</if>
		<if test="monthMoneySlip != null">
			and MONTH_MONEY_SLIP = #{monthMoneySlip,jdbcType=DECIMAL}
		</if>
		<if test="provideIncomeDetailType != null">
			and PROVIDE_INCOME_DETAIL_TYPE = #{provideIncomeDetailType,jdbcType=SMALLINT}
		</if>
		<if test="companyWebsite != null and companyWebsite !=''">
			and COMPANY_WEBSITE = #{companyWebsite,jdbcType=VARCHAR}
		</if>
		<if test="bizPlatformName != null and bizPlatformName !=''">
			and BIZ_PLATFORM_NAME = #{bizPlatformName,jdbcType=VARCHAR}
		</if>
		<if test="jsonInfo != null">
			and JSON_INFO = #{jsonInfo,jdbcType=OTHER}
		</if>
		<if test="professionType != null and professionType !=''">
			and PROFESSION_TYPE = #{professionType,jdbcType=VARCHAR}
		</if>
		<if test="professionName != null and professionName !=''">
			and PROFESSION_NAME = #{professionName,jdbcType=VARCHAR}
		</if>
		<if test="qualificationCode != null and qualificationCode !=''">
			and QUALIFICATION_CODE = #{qualificationCode,jdbcType=VARCHAR}
		</if>
		<if test="qualificationUrl != null and qualificationUrl !=''">
			and QUALIFICATION_URL = #{qualificationUrl,jdbcType=VARCHAR}
		</if>
		<if test="serviceType != null and serviceType !=''">
			and SERVICE_TYPE = #{serviceType,jdbcType=VARCHAR}
		</if>
		<if test="alipayAppName != null and alipayAppName !=''">
			and ALIPAY_APP_NAME = #{alipayAppName,jdbcType=VARCHAR}
		</if>
		<if test="appName != null and appName !=''">
			and APP_NAME = #{appName,jdbcType=VARCHAR}
		</if>
		<if test="wapName != null and wapName !=''">
			and WAP_NAME = #{wapName,jdbcType=VARCHAR}
		</if>
		<if test="wapSite != null and wapSite !=''">
			and WAP_SITE = #{wapSite,jdbcType=VARCHAR}
		</if>
		<if test="pcName != null and pcName !=''">
			and PC_NAME = #{pcName,jdbcType=VARCHAR}
		</if>
		<if test="pcSite != null and pcSite !=''">
			and PC_SITE = #{pcSite,jdbcType=VARCHAR}
		</if>
		<if test="smid != null and smid != ''">
			and SMID = #{smid,jdbcType=VARCHAR}
		</if>
	</sql>
</mapper>

