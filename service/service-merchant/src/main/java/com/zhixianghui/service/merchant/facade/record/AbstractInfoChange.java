package com.zhixianghui.service.merchant.facade.record;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.agent.AgentTypeEnum;
import com.zhixianghui.common.statics.enums.fee.FormulaEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.CertificateTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.SignRateLevelEnum;
import com.zhixianghui.common.statics.enums.merchant.ValidityDateTypeEnum;
import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.enums.ChangeSourceEnum;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.enums.SelfDeclaredEnum;
import com.zhixianghui.facade.merchant.vo.record.BaseInfoVo;
import com.zhixianghui.service.merchant.core.vo.MerchantInfoChangeRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @Date 2021/7/19 9:09
 */
@Slf4j
public abstract class AbstractInfoChange<T> {

    private static final List<Object> PACKAGING_CLASS = new ArrayList<>();
    private static final List<String> IGNORE_FIELD = new ArrayList<>();
    private static final String NUMERIC_REGEX = "(^[+-]?([0-9]*\\.?[0-9]+|[0-9]+\\.?[0-9]*)([eE][+-]?[0-9]+)?$)";
    protected static final Map<String, Function<Integer, String>> SPECIAL_FIELD = new HashMap<>();
    private static final String LIST_TYPE = "java.util.List";
    private static final String MAP_TYPE = "java.util.Map";
    protected static final String STRING_TYPE = "class java.lang.String";

    static {
        PACKAGING_CLASS.add(Byte.class);
        PACKAGING_CLASS.add(Short.class);
        PACKAGING_CLASS.add(Integer.class);
        PACKAGING_CLASS.add(Long.class);
        PACKAGING_CLASS.add(Float.class);
        PACKAGING_CLASS.add(Double.class);
        PACKAGING_CLASS.add(Character.class);
        PACKAGING_CLASS.add(Boolean.class);
        PACKAGING_CLASS.add(String.class);

        SPECIAL_FIELD.put("agentStatus", AgentStatusEnum::getDesc);
        SPECIAL_FIELD.put("agentType", AgentTypeEnum :: getDesc);
        SPECIAL_FIELD.put("certificateType", CertificateTypeEnum :: getDesc);
        SPECIAL_FIELD.put("certificateValidityDateType", ValidityDateTypeEnum::getDesc);
        SPECIAL_FIELD.put("managementValidityDateType", ValidityDateTypeEnum::getDesc);
        SPECIAL_FIELD.put("selfDeclared", SelfDeclaredEnum :: getDesc);
        SPECIAL_FIELD.put("signRateLevel", SignRateLevelEnum:: getDesc);
        SPECIAL_FIELD.put("formulaType", FormulaEnum:: getDesc);
        SPECIAL_FIELD.put("secondFormulaType", FormulaEnum:: getDesc);
        SPECIAL_FIELD.put("ruleType", RuleTypeEnum:: getDesc);


        IGNORE_FIELD.add("loginName");
        IGNORE_FIELD.add("loginId");
        IGNORE_FIELD.add("flowId");
        IGNORE_FIELD.add("serialVersionUID");
        IGNORE_FIELD.add("updator");
    }

    protected void logInfoAndCheck(T newInfo, T oldInfo, int source, Long businessId) {
        log.info("旧数据 : {}, 新数据 : {}", JSONObject.toJSON(oldInfo), JSONObject.toJSON(newInfo));
        if (ChangeSourceEnum.FLOW.getSource() == source) {
            Assert.notNull(businessId, "审批流程, 修改记录的业务id不能为空");
        }
    }


    protected MerchantInfoChangeRecord handleString(
            Map<String, String> description,
            Map.Entry<String, Object> entry,
            Map<String, Object> newMap,
            BaseInfoVo baseInfoVo,
            Integer source,
            String mchNo
    ) {
        String newResult = (String) newMap.get(entry.getKey());
        String oldResult = (String) entry.getValue();
        if (StringUtils.isBlank((String) entry.getValue()) && StringUtils.isBlank((String) newMap.get(entry.getKey()))) {
            return null;
        }
        if (newResult.equals(oldResult) || equalsNumeric(newResult, oldResult)) {
            return null;
        }
        if (StringUtils.isBlank(newResult) && StringUtils.isNotBlank(oldResult)) {
            // 删除
            return new MerchantInfoChangeRecordVo().build(
                    description, OperationEnum.DELETE.getOperation(), entry, newMap, baseInfoVo, source, mchNo
            );
        }
        if (StringUtils.isBlank(oldResult) && StringUtils.isNotBlank(newResult)) {
            // 增加
            return new MerchantInfoChangeRecordVo().build(
                    description, OperationEnum.ADD.getOperation(), entry, newMap, baseInfoVo, source, mchNo
            );
        }
        return new MerchantInfoChangeRecordVo().build(
                description, OperationEnum.MODIFY.getOperation(), entry, newMap, baseInfoVo, source, mchNo
        );
    }

    protected static boolean equalsNumeric(String newResult, String oldResult) {
        // ?:0或1个, *:0或多个, +:1或多个
        if(!newResult.matches(NUMERIC_REGEX) || !oldResult.matches(NUMERIC_REGEX)) {
            return false;
        }
        return new BigDecimal(newResult).compareTo(new BigDecimal(oldResult)) == 0;
    }

    protected List<MerchantInfoChangeRecord> handleStringList(
            List<String> odlList,
            List<String> newList,
            Map.Entry<String, Object> entry,
            Map<String, String> description,
            BaseInfoVo infoVo,
            String mchNo,
            int source
    ) {
        List<MerchantInfoChangeRecord> recordList = new ArrayList<>();
        if (CollectionUtils.isEmpty(odlList)) {
            for (String s : newList) {
                recordList.add(new MerchantInfoChangeRecordVo().build(
                        entry.getKey(), description.get(entry.getKey()), OperationEnum.ADD.getOperation(), s, infoVo, source, mchNo
                ));
            }
            return recordList;
        }
        if (CollectionUtils.isEmpty(newList)) {
            for (String s : odlList) {
                recordList.add(new MerchantInfoChangeRecordVo().build(
                        entry.getKey(), description.get(entry.getKey()), OperationEnum.DELETE.getOperation(), s, infoVo, source, mchNo
                ));
            }
            return recordList;
        }

        List<String> delete = new ArrayList<>();
        for (String s : odlList) {
            if (newList.contains(s)) {
                delete.add(s);
                continue;
            }
            // 删除
            recordList.add(new MerchantInfoChangeRecordVo().build(
                    entry.getKey(), description.get(entry.getKey()), OperationEnum.DELETE.getOperation(), s, infoVo,  source, mchNo
            ));
        }
        newList.removeAll(delete);
        if (CollectionUtils.isEmpty(newList)) {
            return recordList;
        }
        for (String s : newList) {
            recordList.add(new MerchantInfoChangeRecordVo().build(
                    entry.getKey(),description.get(entry.getKey()), OperationEnum.ADD.getOperation(), s, infoVo,  source, mchNo
            ));
        }
        return recordList;
    }

    protected Map<String, Object> reflection(T oldInfo) {
        Map<String, Object> infoMap = new HashMap<>();
        conversion(oldInfo, infoMap);
        return infoMap;
    }

    protected void conversion(Object info, Map<String, Object> infoMap) {
        Class<?> c = info.getClass();
        Field[] fields = c.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Class<?> type = field.getType();
                // 判断类型并将属性值转换为 String
                if (type.isPrimitive() || PACKAGING_CLASS.contains(type) || BigDecimal.class.equals(type)) {
                    if (IGNORE_FIELD.contains(field.getName())) {
                        continue;
                    }
                    if (field.get(info) == null) {
                        infoMap.put(field.getName(), "");
                    } else {
                        infoMap.put(field.getName(), String.valueOf(field.get(info)));
                        specialHandling(field.getName(), infoMap);
                    }
                }
                if (type.toString().contains(LIST_TYPE) || type.toString().contains(MAP_TYPE)) {
                    infoMap.put(field.getName(), field.get(info));
                }
            } catch (Exception e) {
                log.error("反射获取数据出错 {} : ", JSONObject.toJSON(field), e);
                handleException();
            }
        }
    }

    // 有些特殊字段, 例如枚举需要特殊处理
    protected void specialHandling(String name, Map<String, Object> infoMap) {}

    protected abstract void compareAndRecord(Map<String, Object> oldMap, Map<String, Object> newMap, T newInfo, int source);
    protected abstract void handleException();
    protected abstract void getDictionary();

}
