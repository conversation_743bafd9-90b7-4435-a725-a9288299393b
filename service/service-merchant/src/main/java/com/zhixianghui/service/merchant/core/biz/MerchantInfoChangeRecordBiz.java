package com.zhixianghui.service.merchant.core.biz;


import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.service.merchant.core.dao.MerchantInfoChangeRecordDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;

/**
* 商户信息变更记录表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-07-19
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantInfoChangeRecordBiz {

    private final MerchantInfoChangeRecordDao changeRecordDao;

    public void insert(List<MerchantInfoChangeRecord> changeList) {
        changeRecordDao.insert(changeList);
    }

    public List<MerchantInfoChangeRecord> list(Long flowId) {
        return changeRecordDao.listBy(new HashMap<String, Object>() {{
            put("businessId", flowId);
        }});
    }
    public List<MerchantInfoChangeRecord> list(String mchNo) {
        return changeRecordDao.listBy(new HashMap<String, Object>() {{
            put("mchNo", mchNo);
        }});
    }


    public MerchantInfoChangeRecord getLastChange(String mchNo) {
        return changeRecordDao.getOne("getLastChange", new HashMap<String, Object>() {{
            put("mchNo", mchNo);
        }});
    }

    public void deleteRecord(Long commonFlowId) {
        changeRecordDao.deleteBy("deleteByBusinessId", new HashMap<String, Object>() {{ put("businessId", commonFlowId); }});
    }

    public void insert(MerchantInfoChangeRecord record) {
        changeRecordDao.insert(record);
    }

    public void update(List<MerchantInfoChangeRecord> list) {
        changeRecordDao.update(list);
    }
}