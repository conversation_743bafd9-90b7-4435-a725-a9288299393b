package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuotePositionDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuotePosition;
import lombok.RequiredArgsConstructor;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2022-06-06
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantEmployerQuotePositionBiz {

    private final MerchantEmployerQuotePositionDao merchantemployerquotepositionDao;
}