package com.zhixianghui.service.merchant.core.biz;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.merchant.entity.MerchantZftQuote;
import com.zhixianghui.service.merchant.core.dao.mapper.MerchantZftQuoteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-05-09
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantZftQuoteBiz extends ServiceImpl<MerchantZftQuoteMapper, MerchantZftQuote> {

    private final MerchantZftQuoteMapper merchantzftquoteDao;
}