package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.AgreementSigner;
import com.zhixianghui.facade.merchant.service.AgreementSignerFacade;
import com.zhixianghui.service.merchant.core.biz.AgreementSignerBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 协议签署人表 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementSignerImpl implements AgreementSignerFacade {

    private final AgreementSignerBiz biz;

    @Override
    public List<AgreementSigner> listByAgreementId(Long AgreementId) {
        return biz.listByAgreementId(AgreementId);
    }

    @Override
    public AgreementSigner getBySignNoAndId(String mchNo, Long id) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("signerNo",mchNo);
        paramMap.put("agreementId",id);
        return biz.getOne(paramMap);
    }

    @Override
    public List<AgreementSigner> listByAgreementIdWithSignUrl(Long id) {
        return biz.listByAgreementIdWithSignUrl(id);
    }
}
