package com.zhixianghui.service.merchant.core.dao.user.agent;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 供应商后台功能表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class AgentFunctionDao extends MyBatisDao<AgentFunction, Long> {
    public void deleteByIds(List<Long> ids) {
        super.deleteBy("deleteByIds", ids);
    }

    public List<AgentFunction> listByParentId(long parentId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", parentId);
        return super.listBy("listByParentId", paramMap, "number asc");
    }

    public List<AgentFunction> listByRoleId(long roleId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("roleId", roleId);
        return super.listBy("listByRoleId", paramMap, "number asc");
    }

    public List<AgentFunction> listByStaffId(long staffId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("staffId", staffId);
        return super.listBy("listByStaffId", paramMap, "number asc");
    }
}
