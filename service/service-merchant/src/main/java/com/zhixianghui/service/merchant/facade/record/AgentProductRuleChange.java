package com.zhixianghui.service.merchant.facade.record;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.CompareTypeEnum;
import com.zhixianghui.common.statics.enums.fee.SalesSpecialRuleTypeEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.vo.agent.SpecialFeeRuleVo;
import com.zhixianghui.facade.merchant.vo.record.AgentProductFeeVo;
import com.zhixianghui.service.merchant.core.biz.DictionaryBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantInfoChangeRecordBiz;
import com.zhixianghui.service.merchant.core.vo.MerchantInfoChangeRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/7/19 15:41
 */
@Slf4j
@Service("AgentProductRuleChange")
public class AgentProductRuleChange extends AbstractInfoChange<AgentProductFeeVo> {
    private static final String DICTIONARY_NAME = "AgentProductRuleChange";
    private static final String RULE_TYPE = "ruleParam";
    private static DataDictionary dataDictionary;
    private static Map<String, String> description;
    @Autowired
    private MerchantInfoChangeRecordBiz changeRecordBiz;
    @Autowired
    private DictionaryBiz dictionaryBiz;

    @PostConstruct
    @Override
    protected void getDictionary() {
        dataDictionary = dictionaryBiz.getDataDictionaryByName(DICTIONARY_NAME);
        description = dataDictionary.getItemList().stream().collect(Collectors.toMap(DataDictionary.Item::getCode, DataDictionary.Item::getDesc));
    }

    @Override
    protected void handleException() {

    }

    @Override
    protected void specialHandling(String name, Map<String, Object> infoMap) {
        if (!SPECIAL_FIELD.containsKey(name) || StringUtils.isBlank((String) infoMap.get(name))) {
            return;
        }
        infoMap.put(name, SPECIAL_FIELD.get(name).apply(Integer.valueOf((String) infoMap.get(name))));
    }

    public void handle(AgentProductFeeVo newInfo, AgentProductFeeVo oldInfo, int source) {
        logInfoAndCheck(newInfo, oldInfo, source, newInfo.getFlowId());
        Map<String, Object> oldMap = reflection(oldInfo);
        Map<String, Object> newMap = reflection(newInfo);
        compareAndRecord(oldMap, newMap, newInfo, source);
    }

     protected void compareAndRecord(Map<String, Object> oldMap, Map<String, Object> newMap, AgentProductFeeVo newInfo, int source) {
        List<MerchantInfoChangeRecord> changeList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : oldMap.entrySet()) {
            if (!newMap.containsKey(entry.getKey())) {
                log.warn("合伙人产品报价单属性发生变化 : {}", entry.getKey());
                continue;
            }
            if (newMap.get(entry.getKey()) instanceof String) {
                MerchantInfoChangeRecord record = handleString(description, entry, newMap, newInfo, source, newInfo.getAgentNo());
                if (record == null) {
                    continue;
                }
                changeList.add(record);

            }
            if (newMap.get(entry.getKey()) instanceof List) {
                List<?> result = (List<?>) newMap.get(entry.getKey());
                if (CollectionUtils.isEmpty(result) && CollectionUtils.isEmpty((List<?>) entry.getValue())) {
                    continue;
                }
                Object o = !CollectionUtils.isEmpty(result) ? result.get(0) : ((List<?>) entry.getValue()).get(0);
                if (o instanceof SpecialFeeRuleVo) {
                    List<SpecialFeeRuleVo> odlList = JsonUtil.toList(JSONObject.toJSON(entry.getValue()), SpecialFeeRuleVo.class);
                    List<SpecialFeeRuleVo> newList = JsonUtil.toList(JSONObject.toJSON(newMap.get(entry.getKey())), SpecialFeeRuleVo.class);
                    handleProductQuote(odlList, newList, changeList, newInfo, source);
                }
            }
        }
        log.info(JSONObject.toJSONString(changeList));
        if (CollectionUtils.isEmpty(changeList)) {
            return;
        }
        changeRecordBiz.insert(changeList);
    }

    private void handleProductQuote(List<SpecialFeeRuleVo> odlList, List<SpecialFeeRuleVo> newList, List<MerchantInfoChangeRecord> changeList, AgentProductFeeVo newInfo, int source) {
        if (CollectionUtils.isEmpty(odlList) && CollectionUtils.isEmpty(newList)) {
            return;
        }
        List<String> oldRule = null;
        if (!CollectionUtils.isEmpty(odlList)) {
            oldRule = translateRule(odlList);
        }
        List<String> newRule = null;
        if (!CollectionUtils.isEmpty(newList)) {
            newRule = translateRule(newList);
        }
        if (CollectionUtils.isEmpty(oldRule) && CollectionUtils.isEmpty(newRule)) {
            return;
        }
        if (CollectionUtils.isEmpty(oldRule) && !CollectionUtils.isEmpty(newRule)) {
            for (String rule : newRule) {
                changeList.add(new MerchantInfoChangeRecordVo().build(
                        RULE_TYPE, description.get(RULE_TYPE), OperationEnum.ADD.getOperation(), rule, newInfo, source, newInfo.getAgentNo()
                ));
            }
            return;
        }
        if (CollectionUtils.isEmpty(newRule)  && !CollectionUtils.isEmpty(oldRule)) {
            for (String rule : oldRule) {
                changeList.add(new MerchantInfoChangeRecordVo().build(
                        RULE_TYPE, description.get(RULE_TYPE), OperationEnum.DELETE.getOperation(), rule, newInfo, source, newInfo.getAgentNo()
                ));
            }
            return;
        }
        Assert.notNull(oldRule, "老规则不能为空");
        Assert.notNull(newRule, "新规则不能为空");
        List<String> delete = new ArrayList<>();
        for (String rule : oldRule) {
            if (newRule.contains(rule)) {
                delete.add(rule);
                continue;
            }
            if (!newRule.contains(rule)) {
                // 删除
                changeList.add(new MerchantInfoChangeRecordVo().build(
                        RULE_TYPE, description.get(RULE_TYPE), OperationEnum.DELETE.getOperation(), rule, newInfo, source, newInfo.getAgentNo()
                ));
            }
        }
        newRule.removeAll(delete);
        if (CollectionUtils.isEmpty(newRule)) {
            return;
        }
        for (String rule : newRule) {
            changeList.add(new MerchantInfoChangeRecordVo().build(
                    RULE_TYPE, description.get(RULE_TYPE), OperationEnum.ADD.getOperation(), rule, newInfo, source, newInfo.getAgentNo()
            ));
        }


    }

    private List<String> translateRule(List<SpecialFeeRuleVo> ruleList) {
        List<String> infoList = new ArrayList<>();
        for (SpecialFeeRuleVo specialFeeRuleVo : ruleList) {
            String info = SalesSpecialRuleTypeEnum.getDesc(specialFeeRuleVo.getSpecialRuleType()) +
                    CompareTypeEnum.getMsg(specialFeeRuleVo.getCompareType()) +
                    specialFeeRuleVo.getValue();
            infoList.add(info);
        }
        return infoList;
    }
}
