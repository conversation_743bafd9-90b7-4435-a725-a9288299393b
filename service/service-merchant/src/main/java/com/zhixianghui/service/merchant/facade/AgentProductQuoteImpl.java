package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import com.zhixianghui.facade.merchant.service.AgentProductQuoteFacade;
import com.zhixianghui.service.merchant.core.biz.AgentProductQuoteBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 合伙人银行账户表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentProductQuoteImpl implements AgentProductQuoteFacade {

    private final AgentProductQuoteBiz biz;

    @Override
    public List<AgentProductQuote> getByAgentNo(String agentNo) {
        return biz.getByAgentNo(agentNo);
    }

    @Override
    public List<AgentProductQuote> getByParams(Map<String, Object> params) {
        return biz.getByParams(params);
    }

    @Override
    public void preSaveAgentQuote(AgentProductQuote quote) {
        biz.preSaveAgentQuote(quote);
    }
}
