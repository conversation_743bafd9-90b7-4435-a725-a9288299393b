package com.zhixianghui.service.merchant.core.biz;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.facade.banklink.service.yishui.YishuiFacade;
import com.zhixianghui.facade.banklink.vo.yishui.YiResponse;
import com.zhixianghui.facade.banklink.vo.yishui.req.SaveEnterpriseDetailVo;
import com.zhixianghui.facade.common.entity.config.AreaCity;
import com.zhixianghui.facade.common.entity.config.AreaMap;
import com.zhixianghui.facade.common.entity.config.AreaProvince;
import com.zhixianghui.facade.common.entity.config.AreaTown;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.entity.report.ReportChannelRecord;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.common.service.*;
import com.zhixianghui.facade.fee.service.MerchantFeeRuleFacade;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class YishuiBiz {

    @Autowired
    private MerchantBiz merchantBiz;
    @Autowired
    private MerchantEmployerMainBiz merchantEmployerMainBiz;
    @Reference
    private YishuiFacade yishuiFacade;
    @Autowired
    private MerchantInvoiceInfoBiz merchantInvoiceInfoBiz;
    @Autowired
    private MerchantFileBiz merchantFileBiz;
    @Autowired
    private MerchantBankAccountBiz merchantBankAccountBiz;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private AreaProvinceFacade areaProvinceFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MerchantFeeRuleFacade merchantFeeRuleFacade;
    @Reference
    private EmployerAccountInfoFacade accountInfoFacade;
    @Reference
    private ReportChannelRecordFacade reportChannelRecordFacade;
    @Autowired
    private FastdfsClient fastdfsClient;

    @Transactional(rollbackFor = Exception.class)
    public void addMerchant(ReportEntity reportEntity) {
        String employerNo = reportEntity.getEmployerNo();
        Merchant merchant = merchantBiz.getByMchNo(employerNo);
        MerchantEmployerMain merchantEmployerMain = merchantEmployerMainBiz.getByMchNo(employerNo);
        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoBiz.getByMchNo(employerNo);
        List<MerchantFile> merchantFileList = merchantFileBiz.listByMchNo(employerNo);
        MerchantBankAccount bankAccount = merchantBankAccountBiz.getByMchNo(employerNo);


        String idHeadUrl = null;
        String bussinessLicenseUrl = null;
        for (MerchantFile merchantFile : merchantFileList) {
            if (merchantFile.getFileType() == MerchantFileTypeEnum.ID_CARD_HEAD.getValue()) {
                idHeadUrl = merchantFile.getFileUrl();
            } else if (merchantFile.getFileType() == MerchantFileTypeEnum.BUSINESS_LICENSE.getValue()) {
                bussinessLicenseUrl = merchantFile.getFileUrl();
            } else if (merchantFile.getFileType() == MerchantFileTypeEnum.ID_CARD_COPY.getValue()) {
                idHeadUrl = merchantFile.getFileUrl();
            }
        }
        if (StringUtils.isAnyBlank(idHeadUrl, bussinessLicenseUrl)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("法人身份证件或者营业执照图片缺失");
        }

        AreaProvince areaProvince = areaProvinceFacade.getProvinceByName(merchantEmployerMain.getManagementAddrProvince());
        String provinceNo = areaProvince.getProvinceNo();
        AreaCity areaCity = areaProvinceFacade.getCityByName(provinceNo, merchantEmployerMain.getManagementAddrCity());
        String cityNo = areaCity.getCityNo();
        AreaTown areaTown = areaProvinceFacade.getTownByName(cityNo, merchantEmployerMain.getManagementAddrTown());
        String townNo = areaTown.getTownNo();

        AreaMap yishuiProvice = areaProvinceFacade.getByZxhCode(provinceNo);
        AreaMap yishuiCity = areaProvinceFacade.getByZxhCode(cityNo);
        AreaMap yishuiTown = areaProvinceFacade.getByZxhCode(townNo);

        String agentId = dataDictionaryFacade.getSystemConfig("yishui_agentId");

        EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo, reportEntity.getMainstayNo());

        String enterpriseId = "0";
        if (StringUtils.isBlank(relation.getExternalEnterpriseId())) {
            log.info(StrUtil.format("本地enterpriseId为空，检查易税是否存在商户:{}", reportEntity.getEmployerName()));
            YiResponse<Map<String, Object>> enterpriseDetailByName = yishuiFacade.getEnterpriseDetailByName(merchant.getMchName());
            if (enterpriseDetailByName != null && enterpriseDetailByName.getCode() == 200) {
                Map<String, Object> data = enterpriseDetailByName.getData();
                if (data != null && data.size() > 0) {
                    if (!Objects.isNull(String.valueOf(data.get("enterprise_id")))) {
                        enterpriseId = String.valueOf(data.get("enterprise_id"));
                    }
                }
            }
        } else {
            log.info(StrUtil.format("本地enterpriseId不为空：{}-商户:{}--", relation.getExternalEnterpriseId(), reportEntity.getEmployerName()));
            enterpriseId = relation.getExternalEnterpriseId();
        }
        //上传图片
        try {
            idHeadUrl = this.uploadFileToYishui(idHeadUrl);
            bussinessLicenseUrl = this.uploadFileToYishui(bussinessLicenseUrl);
        } catch (IOException e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("文件上传失败");
        }

        SaveEnterpriseDetailVo saveEnterpriseDetailVo = new SaveEnterpriseDetailVo();

        saveEnterpriseDetailVo.setEnterprise_id(enterpriseId)
                .setMobile(merchant.getContactPhone())
                .setContact_name(merchant.getContactName())
                .setTaxpayer_type(merchantInvoiceInfo.getTaxPayerType().toString())
                .setInvoice_type(String.valueOf(InvoiceTypeEnum.YISHUI_MAJOR.getValue()))
                .setCertificate(merchantEmployerMain.getCertificateNumber())
                .setCertificate_img(idHeadUrl)
                .setBusiness_licence_img(bussinessLicenseUrl)
                .setLegal_person(merchantEmployerMain.getLegalPersonName())
                .setResource_manager_id("0")
                .setEnterprise_user_name(employerNo)
                .setEnterprise_code(merchantEmployerMain.getTaxNo())
                .setGroup_id("0")
                .setChecked_mobile(merchant.getContactPhone())
                .setBank_sn(bankAccount.getAccountNo())
                .setBank_name(bankAccount.getBankName())
                .setEnterprise_name(merchant.getMchName())
                .setAgent_id(agentId)
                .setProvince(yishuiProvice.getYishuiCode())
                .setCity(yishuiCity.getYishuiCode())
                .setDistrict(yishuiTown.getYishuiCode())
                .setAddress(merchantEmployerMain.getManagementAddrDetail());

        YiResponse<Map<String, String>> response = yishuiFacade.saveEnterpriseDetail(saveEnterpriseDetailVo);
        String defaultYishuiPassword = dataDictionaryFacade.getSystemConfig("defaultYishuiPassword");
        if (response.getCode() != 200) {

            if (StringUtils.equals("您无权限修改企业基本资料", response.getMsg())) {
                YiResponse<Map<String, Object>> enterpriseDetailByName = yishuiFacade.getEnterpriseDetailByName(merchant.getMchName());
                String enterprise_id = String.valueOf(enterpriseDetailByName.getData().get("enterprise_id"));
                String enterprise_sn = String.valueOf(enterpriseDetailByName.getData().get("enterprise_sn"));

                relation.setExternalEnterpriseId(enterprise_id);
                relation.setExternalEnterpriseSn(enterprise_sn);
                relation.setExternalEnterpriseStatus(CommonStatusEnum.ACTIVE.getValue());
                relation.setAccountStatus(1);
                relation.setUpdateTime(new Date());
                relation.setUpdateOperator(reportEntity.getReporter());

                if (StringUtils.isNotBlank(relation.getExternalPassword())) {
                    relation.setExternalPassword(AESUtil.decryptECB(defaultYishuiPassword, EncryptKeys.getEncryptKeyById(101L).getEncryptKeyStr()));
                }
                relation.setExternalUserName(reportEntity.getEmployerNo());
                relation.setHasExternalSystem(Boolean.TRUE);
                employerMainstayRelationFacade.updateIfNotNull(relation);

                EmployerAccountInfo employerAccountInfo = accountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo, reportEntity.getMainstayNo(), reportEntity.getChannelType());
                employerAccountInfo.setSubMerchantNo(enterprise_sn);
                employerAccountInfo.setUpdateTime(new Date());
                employerAccountInfo.setUpdateOperator(reportEntity.getReporter());
                employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
                employerAccountInfo.setParentMerchantNo(reportEntity.getMainstayNo());
                employerAccountInfo.setPayChannelNo(reportEntity.getPayChannelNo());
                accountInfoFacade.batchUpdate(Lists.newArrayList(employerAccountInfo));

                ReportChannelRecord reportChannelRecord = reportChannelRecordFacade.getById(reportEntity.getRecordId());
                reportChannelRecord.setSerialNo(ChannelNoEnum.YISHUI.name() + enterprise_sn + reportEntity.getChannelType());
                reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
                reportChannelRecord.setUpdateTime(new Date());
                reportChannelRecord.setRespData(JSONUtil.toJsonStr(enterpriseDetailByName.getData()));
                reportChannelRecordFacade.update(reportChannelRecord);

                return;
            }

            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(response.getMsg());
        }
        String enterprise_id = String.valueOf(response.getData().get("enterprise_id"));
        String enterprise_sn = String.valueOf(response.getData().get("enterprise_sn"));

        relation.setExternalEnterpriseId(enterprise_id);
        relation.setExternalEnterpriseSn(enterprise_sn);
        relation.setExternalEnterpriseStatus(CommonStatusEnum.INACTIVE.getValue());
        relation.setAccountStatus(1);
        relation.setUpdateTime(new Date());
        relation.setUpdateOperator(reportEntity.getReporter());
        if (StringUtils.isNotBlank(relation.getExternalPassword())) {
            relation.setExternalPassword(AESUtil.decryptECB(defaultYishuiPassword, EncryptKeys.getEncryptKeyById(101L).getEncryptKeyStr()));
        }
        relation.setExternalUserName(reportEntity.getEmployerNo());
        relation.setHasExternalSystem(Boolean.TRUE);
        employerMainstayRelationFacade.updateIfNotNull(relation);

        EmployerAccountInfo employerAccountInfo = accountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo, reportEntity.getMainstayNo(), reportEntity.getChannelType());
        employerAccountInfo.setSubMerchantNo(enterprise_sn);
        employerAccountInfo.setUpdateTime(new Date());
        employerAccountInfo.setUpdateOperator(reportEntity.getReporter());
        employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
        employerAccountInfo.setParentMerchantNo(reportEntity.getMainstayNo());
        employerAccountInfo.setPayChannelNo(reportEntity.getPayChannelNo());
        accountInfoFacade.batchUpdate(Lists.newArrayList(employerAccountInfo));

        ReportChannelRecord reportChannelRecord = reportChannelRecordFacade.getById(reportEntity.getRecordId());
        reportChannelRecord.setSerialNo(ChannelNoEnum.YISHUI.name() + enterprise_sn + reportEntity.getChannelType());
        reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecord.setRespData(JSONUtil.toJsonStr(response.getData()));
        reportChannelRecordFacade.update(reportChannelRecord);
    }

    /**
     * public void editFee(ReportEntity reportEntity) {
     * <p>
     * Map<String, Object> queryParam = MapUtil.builder(new HashMap<String, Object>())
     * .put("mchNo", reportEntity.getEmployerNo()).build();
     * List<MerchantFeeRule> merchantFeeRules = merchantFeeRuleFacade.listBy(queryParam);
     * for (MerchantFeeRule merchantFeeRule : merchantFeeRules) {
     * if (StringUtils.equals(merchantFeeRule.getProductNo(), "ZXH")) {
     * List<SpecialRuleDto> specialFeeRuleList = merchantFeeRule.getSpecialFeeRuleList();
     * for (SpecialRuleDto specialRuleDto : specialFeeRuleList) {
     * if (specialRuleDto.getSpecialRuleType() == ProductFeeSpecialRuleTypeEnum.VERDOR_NO.getValue()&&StringUtils.equals(reportEntity.getMainstayNo(), specialRuleDto.getValue())) {
     * // 选出合适的计费规则
     * // 对选出的计费规则进行校验
     * if (merchantFeeRule.getFormulaType() == FormulaEnum.FIXED.getValue()) {
     * throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商不治之固定金额费率");
     * }
     * <p>
     * //拼装参数
     * SaveEnterpriseDraftRate saveEnterpriseDraftRate = new SaveEnterpriseDraftRate();
     * saveEnterpriseDraftRate.setEnterprise_facilitator_draft_id("")
     * .setRegion_type("1")
     * .setStandard_rate("0.00");
     * <p>
     * YiResponse yiResponse = yishuiFacade.saveEnterpriseDraftRate(saveEnterpriseDraftRate);
     * if (yiResponse.getCode() != 200) {
     * throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("编辑计费规则到易税失败:{}", yiResponse.getMsg()));
     * }
     * }
     * }
     * }
     * }
     * }
     **/

    public String uploadFileToYishui(String fileId) throws IOException {
        InputStream inputStream = fastdfsClient.downloadFile(fileId);
        String originalFileName = fastdfsClient.getOriginalFileName(fileId);
        String uploadUrl = yishuiFacade.getUploadUrl();
        OkHttpClient httpClient = new OkHttpClient.Builder().build();

        okhttp3.RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("token", yishuiFacade.getAgentToken())
                .addFormDataPart("file", originalFileName,
                        okhttp3.RequestBody.create(MediaType.parse("multipart/form-data"), IoUtil.readBytes(inputStream)))
                .build();

        Request request = new Request.Builder()
                .url(uploadUrl)
                .post(requestBody)
                .build();
        Response response = httpClient.newCall(request).execute();
        if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
        String string = response.body().string();
        JSONObject jsonObject = JSON.parseObject(string);
        if (jsonObject.getIntValue("code") != 200) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("文件上传失败:",jsonObject.getString("msg")));
        }
        String fileUrl = jsonObject.getJSONObject("data").getString("resource_href");
        return fileUrl;
    }

}
