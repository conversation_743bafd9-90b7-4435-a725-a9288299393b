package com.zhixianghui.service.merchant.core.biz;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.merchant.*;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowStatusEnum;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerStaff;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaff;
import com.zhixianghui.facade.merchant.vo.MerchantInfoVo;
import com.zhixianghui.facade.merchant.vo.MerchantSupplierInfoVo;
import com.zhixianghui.facade.merchant.vo.RelevantAgent;
import com.zhixianghui.facade.merchant.vo.merchant.BranchVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantLogoVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.service.merchant.core.dao.*;
import com.zhixianghui.service.merchant.core.dao.user.employer.EmployerRoleDao;
import com.zhixianghui.service.merchant.core.dao.user.employer.EmployerStaffDao;
import com.zhixianghui.service.merchant.core.dao.user.supplier.SupplierStaffDao;
import com.zhixianghui.service.merchant.helper.CacheBiz;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 商户信息
 *
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
@Slf4j
public class MerchantBiz {
    @Autowired
    private MerchantDao merchantDao;
    @Autowired
    private MerchantSalerDao merchantSalerDao;
    @Autowired
    private MerchantEmployerCooperateDao merchantEmployerCooperateDao;
    @Autowired
    private MerchantEmployerPositionDao merchantEmployerPositionDao;
    @Autowired
    private MerchantEmployerQuoteDao quoteDao;
    @Autowired
    private MerchantEmployerQuoteRateDao merchantEmployerQuoteRateDao;
    @Autowired
    private MerchantFileDao merchantFileDao;
    @Autowired
    private EmployerStaffDao employerStaffDao;
    @Autowired
    private MerchantTradePwdDao merchantTradePwdDao;
    @Autowired
    private EmployerRoleDao employerRoleDao;
    @Autowired
    private MerchantBankAccountDao merchantBankAccountDao;


    @Autowired
    private MerchantSecretDao merchantSecretDao;
    @Autowired
    private AgentDao agentDao;
    @Autowired
    private MerchantInvoiceInfoDao merchantInvoiceInfoDao;
    @Autowired
    private SupplierStaffDao supplierStaffDao;
    @Autowired
    private AgentBiz agentBiz;
    @Autowired
    private CacheBiz cacheBiz;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private FlowFacade flowFacade;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;

    @Value("${merchant.platform.link}")
    private String merchantLink;
    @Value("${mainstay.platform.link}")
    private String mainstayLink;
    @Value("${agent.platform.link}")
    private String agentLink;
    @Value("${pms.platform.link}")
    private String pmsLink;

    public Merchant getByMchNo(String mchNo) {
        return merchantDao.getByMchNo(mchNo);
    }

    public PageResult<List<MerchantSupplierInfoVo>> listExtSupplierMerchantPage(Map<String, Object> paramMap, PageParam pageParam) {
        return merchantDao.listPage("supplierMerchantListPage", paramMap, pageParam);
    }

    public PageResult<List<Object>> listExtObjectPage(Map<String, Object> paramMap, PageParam pageParam) {
        return merchantDao.listExtObjectPage(paramMap, pageParam);
    }

    public List<Merchant> listBy(Map<String, Object> paramMap) {
        return merchantDao.listBy(paramMap);
    }

    public void update(Merchant merchant) {
        merchantDao.update(merchant);
    }


    public MerchantSecret getSercretByMchNo(String merchantNo) {
        if (StringUtil.isEmpty(merchantNo)) {
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("merchantNo", merchantNo);
        List<MerchantSecret> merchantSecrets = merchantSecretDao.listBy(param);
        return CollectionUtils.isEmpty(merchantSecrets) ? null : merchantSecrets.get(0);
    }

    public void insertSercet(MerchantSecret merchantSecret) {
        merchantSecretDao.insert(merchantSecret);
    }

    public void updateSecret(MerchantSecret merchantSecret) {
        merchantSecretDao.update(merchantSecret);
    }

    public PageResult<List<Merchant>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return merchantDao.listPage(paramMap, pageParam);
    }

    public RelevantAgent getRelevantAgentByMchNo(String mchNo) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        RelevantAgent relevantAgent = new RelevantAgent();
        relevantAgent.setOneLevelAgentNo(merchant.getAgentNo());
        relevantAgent.setOneLevelAgentName(merchant.getAgentName());
        if (StringUtils.isNotBlank(merchant.getAgentNo())) {
            Agent agent = agentDao.getByAgentNo(merchant.getAgentNo());
            relevantAgent.setOneLevelAgentStatus(agent.getAgentStatus());
            relevantAgent.setTwoLevelAgentNo(agent.getInviterNo());
            relevantAgent.setTwoLevelAgentName(agent.getInviterName());
            if (StringUtils.isNotBlank(agent.getInviterNo())) {
                Agent upAgent = agentDao.getByAgentNo(agent.getInviterNo());
                relevantAgent.setTwoLevelAgentStatus(upAgent.getAgentStatus());
            }
        }
        return relevantAgent;
    }


    public List<String> listMerchantNo(Boolean existAgent) {
        return merchantDao.listBy("listMerchantNo", new HashMap<String, Object>() {
            private static final long serialVersionUID = 1243579376783571394L;

            {
            put("existAgent", existAgent);
        }});
    }

    public void updateTemplateId(String templateId, List<String> mchNoList) {
        merchantDao.update("updateTemplateId", new HashMap<String, Object>() {
            private static final long serialVersionUID = 1686605008773920723L;

            {
            put("templateId", templateId);
            put("mchNoList", mchNoList);
        }});
    }

    public List<Merchant> listMerchantByTemplateId(String templateId, Integer signTemplateType) {
        return merchantDao.listBy("listMerchantByTemplateId", new HashMap<String, Object>() {
            private static final long serialVersionUID = -8123031127291368157L;

            {
            put("templateId", templateId);
            put("merchantType", signTemplateType);
        }});
    }

    public List<Merchant> listTemplateIdByMchNo(List<String> mchNo) {
        return merchantDao.listBy(new HashMap<String, Object>() {
            private static final long serialVersionUID = 39316446480556759L;

            {
            put("mchNos", mchNo);
        }});
    }

    public void delTemplateId(String templateId) {
        merchantDao.update("delTemplateId", new HashMap<String, Object>() {
            private static final long serialVersionUID = 8209566388479014426L;

            {
            put("templateId", templateId);
        }});
    }

    public Map<String, Object> getBaseInfo(String mchNo) {
        return BeanUtil.toMap(convert(mchNo));
    }

    public MerchantInfoVo convert(String mchNo) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        MerchantInfoVo merchantInfoVo = new MerchantInfoVo();
        BeanUtils.copyProperties(merchant, merchantInfoVo);

        //获取商户所属销售
        MerchantSaler merchantSaler = merchantSalerDao.getByMchNo(mchNo);
        if (merchantSaler != null) {
            merchantInfoVo.setSalerId(merchantSaler.getSalerId());
            merchantInfoVo.setSalerName(merchantSaler.getSalerName());
        }

        if (merchant.getMerchantType().intValue() == MerchantTypeEnum.MAINSTAY.getValue()){
            List<MerchantFile> list = merchantFileDao.listBy(new HashMap<String,Object>(){{put("mchNo",merchant.getMchNo());}});
            for (MerchantFile merchantFile : list) {
                if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.PLATFORM_LOGO.getValue()){
                    merchantInfoVo.setPlatformLogoUrl(merchantFile.getFileUrl());
                    continue;
                }
                if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.LOGIN_LOGO.getValue()){
                    merchantInfoVo.setLoginLogoUrl(merchantFile.getFileUrl());
                }
            }
        }
        return merchantInfoVo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBaseInfo(MerchantUpdateVo merchantUpdateVo) {
        Merchant merchant = getByMchNo(merchantUpdateVo.getMchNo());
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        merchantUpdateVo.buildMerchantBase(merchant);
        update(merchant);

        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoDao.getByMchNo(merchant.getMchNo());
        if (merchantInvoiceInfo != null) {
            merchantInvoiceInfo.setMchName(merchant.getMchName());
            merchantInvoiceInfoDao.update(merchantInvoiceInfo);
        }

        if (merchant.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) {
            updateMerchantBaseInfo(merchant);
        } else if (merchant.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()) {
            updateMainstayBaseInfo(merchant);
            //更新logo图片
            updateLogoFile(merchantUpdateVo);
        }
        //更新账户表
        MerchantBankAccount merchantBankAccount = merchantBankAccountDao.getByMchNo(merchant.getMchNo());
        if (merchantBankAccount != null){
            merchantBankAccount.setAccountName(merchant.getMchName());
            merchantBankAccountDao.update(merchantBankAccount);
        }
    }

    private void updateLogoFile(MerchantUpdateVo merchantUpdateVo) {
        List<MerchantFile> merchantFileList = new ArrayList<>();
        //平台logo
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",merchantUpdateVo.getMchNo());
        paramMap.put("fileType",MerchantFileTypeEnum.PLATFORM_LOGO.getValue());
        merchantFileDao.deleteBy(paramMap);
        if (StringUtils.isNotBlank(merchantUpdateVo.getPlatformLogoUrl())){
            MerchantFile platformLogo = new MerchantFile();
            platformLogo.setUpdator(merchantUpdateVo.getUpdator());
            platformLogo.setMchNo(merchantUpdateVo.getMchNo());
            platformLogo.setMchName(merchantUpdateVo.getMchName());
            platformLogo.setFileUrl(merchantUpdateVo.getPlatformLogoUrl());
            platformLogo.setFileType(MerchantFileTypeEnum.PLATFORM_LOGO.getValue());
            platformLogo.setVersion(0);
            platformLogo.setCreateTime(new Date());
            merchantFileList.add(platformLogo);
        }

        paramMap.put("fileType",MerchantFileTypeEnum.LOGIN_LOGO.getValue());
        merchantFileDao.deleteBy(paramMap);

        if (StringUtils.isNotBlank(merchantUpdateVo.getLoginLogoUrl())){
            MerchantFile loginLogo = new MerchantFile();
            loginLogo.setUpdator(merchantUpdateVo.getUpdator());
            loginLogo.setMchNo(merchantUpdateVo.getMchNo());
            loginLogo.setMchName(merchantUpdateVo.getMchName());
            loginLogo.setFileUrl(merchantUpdateVo.getLoginLogoUrl());
            loginLogo.setFileType(MerchantFileTypeEnum.LOGIN_LOGO.getValue());
            loginLogo.setVersion(0);
            loginLogo.setCreateTime(new Date());
            merchantFileList.add(loginLogo);
        }

        if (merchantFileList.size() > 0){
            merchantFileDao.insert(merchantFileList);
        }
    }

    private void updateMainstayBaseInfo(Merchant merchant) {
        Map<String, Object> paramMap = new HashMap<String, Object>() {
            private static final long serialVersionUID = -1164560477551944412L;

            {
            put("mchNo", merchant.getMchNo());
        }};
        List<SupplierStaff> supplierStaffList = supplierStaffDao.listBy(paramMap);
        supplierStaffList.forEach(x -> x.setMchName(merchant.getMchName()));
        supplierStaffDao.update(supplierStaffList);
        //修改报价单
        List<MerchantEmployerQuote> quoteList = quoteDao.listBy(new HashMap<String, Object>() {
            private static final long serialVersionUID = 4466983088683056211L;

            {
            put("mainstayMchNo", merchant.getMchNo());
        }});
        quoteList.stream().forEach(x -> x.setMainstayMchName(merchant.getMchName()));
        quoteDao.update(quoteList);
    }

    private void updateMerchantBaseInfo(Merchant merchant) {
        Map<String, Object> paramMap = new HashMap<String, Object>() {
            private static final long serialVersionUID = 3946352697289878262L;

            {
            put("mchNo", merchant.getMchNo());
        }};
        List<EmployerStaff> employerStaffList = employerStaffDao.listBy(paramMap);
        employerStaffList.forEach(x -> x.setMchName(merchant.getMchName()));
        employerStaffDao.update(employerStaffList);
    }

    public void sendFlowMail(String jsonParam) {
        JSONObject jsonObject = JSON.parseObject(jsonParam);
        CommonFlow commonFLow = JsonUtil.toBean(jsonObject.getJSONObject("commonFlow").toJSONString(), CommonFlow.class);
        Map<String, Object> user = jsonObject.getJSONObject("user").getInnerMap();
        user.entrySet().stream().forEach(x -> {
            PlatformSource platformSource = PlatformSource.getEnum(Integer.parseInt(x.getKey()));
            JSONArray array = (JSONArray) x.getValue();
            sendMail(commonFLow, platformSource, JSONArray.parseArray(array.toJSONString(), String.class));
        });
    }

    private void sendMail(CommonFlow commonFlow, PlatformSource platformSource, List<String> value) {
        String mail = null;
        String userName = "";
        String link = "";
        for (String no : value) {
            switch (platformSource) {
                case OPERATION:
                    PmsOperator pmsOperator = cacheBiz.getPmsOperatorById(no);
                    if (pmsOperator != null && ValidateUtil.isEmail(pmsOperator.getLoginName())) {
                        mail = pmsOperator.getLoginName();
                        userName = pmsOperator.getRealName();
                        link = pmsLink;
                    }
                    break;
                case MERCHANT:
                    Merchant merchant = cacheBiz.getByMchNo(no);
                    if (merchant != null) {
                        mail = merchant.getContactEmail();
                        userName = merchant.getMchName();
                        link = merchantLink;
                    }
                    break;
                case SUPPLIER:
                    Merchant mainstay = cacheBiz.getByMchNo(no);
                    if (mainstay != null) {
                        mail = mainstay.getContactEmail();
                        userName = mainstay.getMchName();
                        link = mainstayLink;
                    }
                    break;
                case AGENT:
                    Agent agent = cacheBiz.getByAgentNo(no);
                    if (agent != null) {
                        mail = agent.getContactEmail();
                        userName = agent.getAgentName();
                        link = agentLink;
                    }
                    break;
            }

            if (mail != null) {
                Map<String, Object> map = new HashMap<>();
                EmailParamDto emailParamDto = new EmailParamDto();
                emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
                emailParamDto.setTo(mail);
                map.put("flowTopicName", commonFlow.getFlowTopicName());
                map.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
                map.put("commonFlowId", commonFlow.getId());
                map.put("platformUrl", link);
                if (commonFlow.getStatus().intValue() == FlowStatusEnum.FINISHED.getValue()){
                    emailParamDto.setTpl(EmailTemplateEnum.COMPLETE_APPROVAL.getName());
                    emailParamDto.setSubject("【智享汇】审批完成提醒");
                    map.put("endTime", DateUtil.formatDateTime(commonFlow.getEndTime()));
                }else{
                    emailParamDto.setTpl(EmailTemplateEnum.WAIT_FOR_APPROVAL.getName());
                    emailParamDto.setSubject("【智享汇】您有一项待办等待处理");
                    map.put("username", userName);
                    map.put("nowTime", DateUtil.formatDateTime(new Date()));
                }
                emailParamDto.setTplParam(map);
                emailParamDto.setHtmlFormat(true);
                emailFacade.sendAsync(emailParamDto);
            }
        }
    }

    public void freezeMerchantExpire(Map<String, Object> maps) {
        merchantDao.update("freezeMerchantExpire", maps);
    }

    public List<MerchantInfoVo> listAll(Map<String, Object> paramMap) {
        return merchantDao.listBy("listExtBy", paramMap);
    }

    public Map<String, Object> getAgentData(Map<String, Object> thisMonth, Map<String, Object> lastMonth) {
        Map<String, Object> map = new HashMap<>();
        //获取共计签约商户
        getActiveMch(thisMonth, lastMonth, map);
        //获取各个销售自己的商户数量
        getMerchantCount(thisMonth, lastMonth, map);
        return map;
    }

    private void getMerchantCount(Map<String, Object> thisMonth, Map<String, Object> lastMonth, Map<String, Object> map) {
        Map<String, Object> thisMonthCount = merchantDao.getAgentCount(thisMonth);
        Map<String, Object> lastMonthCount = merchantDao.getAgentCount(lastMonth);
        map.put("thisMonthCount", thisMonthCount);
        map.put("lastMonthCount", lastMonthCount);
    }

    private void getActiveMch(Map<String, Object> thisMonth, Map<String, Object> lastMonth, Map<String, Object> map) {
        thisMonth.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        thisMonth.put("authStatus", AuthStatusEnum.SUCCESS.getValue());
        Long thisMonthCount = merchantDao.selectAgentDateActiveMch(thisMonth);

        lastMonth.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        lastMonth.put("authStatus", AuthStatusEnum.SUCCESS.getValue());
        Long lastMonthCount = merchantDao.selectAgentDateActiveMch(lastMonth);
        map.put("thisMonthTotalCount", thisMonthCount);
        map.put("lastMonthTotalCount", lastMonthCount);
    }

    public void forceDelete(String mchNo) {
        Merchant merchant = getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("系统异常，商户查询不存在");
        }
        String quoteId = "";
        List<String> ids = null;
        log.error("[{}]商户信息：{}", mchNo, merchant);
        if (merchant.getMchStatus() == MchStatusEnum.ACTIVE.getValue()
                || merchant.getAuthStatus() == AuthStatusEnum.SUCCESS.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该商户已激活");
        }
        if (merchant.getAuthStatus() == AuthStatusEnum.APPROVAL.getValue() ||
                merchant.getAuthStatus() == AuthStatusEnum.FAIL.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该商户存在未完成的审批流程");
        } else {
            boolean isExistFlowSale = flowFacade.isExistNotFinishedFlow(FlowTypeEnum.PMS_CHANGE_SALER, mchNo);
            boolean isExistFlowPrincipal = flowFacade.isExistNotFinishedFlow(FlowTypeEnum.COMMON_CHANGE_PRINCIPAL, mchNo);
            List<MerchantEmployerQuote> quote = quoteDao.listByMchNo(mchNo);
            ids = quote.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
            String[] quoteIds = new String[ids.size()];
            ids.toArray(quoteIds);
            boolean isExistFlowQuote = false;
            if (!ObjectUtils.isEmpty(quote)) {
                isExistFlowQuote = flowFacade.isExistNotFinishedFlow(FlowTypeEnum.PMS_MCH_DELETE_PRODUCT, quoteIds);
            }
            boolean isExistFlowDeleteQuote = flowFacade.isExistNotFinishedUpdateQuoteFlow(FlowTypeEnum.PMS_MCH_CREATE_PRODUCT, mchNo);
            boolean ifExistFlowFreeze = flowFacade.isExistNotFinishedFlow(FlowTypeEnum.PMS_MERCHANT_FREEZE, mchNo);
            if (isExistFlowSale || isExistFlowPrincipal || isExistFlowQuote || isExistFlowDeleteQuote || ifExistFlowFreeze) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该商户存在审批流程");
            }

        }
        //开启事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        merchantDao.deleteBy(param);
        merchantSalerDao.deleteBy(param);
        merchantEmployerCooperateDao.deleteBy(param);
        merchantEmployerPositionDao.deleteBy(param);
        quoteDao.deleteBy(param);
        ids.forEach(e -> {
            Map<String, Object> quoteRateParam = new HashMap<>();
            quoteRateParam.put("quoteId", e);
            merchantEmployerQuoteRateDao.deleteBy(quoteRateParam);
        });
        merchantFileDao.deleteBy(param);
        employerStaffDao.deleteBy(param);
        merchantTradePwdDao.deleteBy(param);


        //更新合伙人统计
        Agent agent = null;
        if (merchant.getAgentNo() != null) {
            agent = agentBiz.getByAgentNo(merchant.getAgentNo());
        }
        if(agent!=null){
            RLock rLock = redisLock.tryLock("DELETE::" + merchant.getAgentNo(), WxUtil.LOCK_WAIT_TIME, WxUtil.LOCK_LEASE_TIME);
            try {
                if (rLock == null) {
                    log.error("[删除商户: {}]==>删除合伙人超时", mchNo);
                    throw new RuntimeException("删除合伙人超时");
                }
                agent.setMerNum(agent.getMerNum() - 1);
                agentBiz.update(agent);
                platformTransactionManager.commit(transaction);
            } catch (Exception e) {
                platformTransactionManager.rollback(transaction);
                throw e;
            } finally {
                redisLock.unlock(rLock);
            }
        }else {
            platformTransactionManager.commit(transaction);
        }

        notifyFacade.sendOne(MessageMsgDest.TOPIC_FORCE_DELETE, UUIDUitl.generateString(10), "C::" + mchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_COMMON_FORCE_DELETE, mchNo, MsgDelayLevelEnum.S_1.getValue());

        notifyFacade.sendOne(MessageMsgDest.TOPIC_FORCE_DELETE, UUIDUitl.generateString(10), "F::" + mchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_FEE_FORCE_DELETE, mchNo, MsgDelayLevelEnum.S_1.getValue());
    }

    public List<BranchVo> listByBranch(Map<String, Object> paramMap) {
        return merchantDao.listBy("listByBranch",paramMap);
    }

    public List<Map<String,Object>> listByBranchEqual(Map<String, Object> paramMap) {
        return merchantDao.listBy("listByBranchEqual",paramMap);
    }

    public List<Merchant> getAllSupplier(){
        HashMap<String,Object> params=new HashMap<>();
        params.put("mchStatus",MchStatusEnum.ACTIVE.getValue());
        params.put("authStatus",AuthStatusEnum.SUCCESS.getValue());
        params.put("merchantType",MerchantTypeEnum.MAINSTAY.getValue());
        return merchantDao.listBy(params);
    }

    public List<Merchant> getAllEmployer(){
        HashMap<String,Object> params=new HashMap<>();
        params.put("mchStatus",MchStatusEnum.ACTIVE.getValue());
        params.put("authStatus",AuthStatusEnum.SUCCESS.getValue());
        params.put("merchantType",MerchantTypeEnum.EMPLOYER.getValue());
        return merchantDao.listBy(params);
    }


    public List<Merchant> selectNoPersonnel(){
        HashMap<String,Object> params=new HashMap<>();
        return merchantDao.getSqlSession().selectList("selectNoPersonnel",params);
    }

    public void forceActive(String mchNo,String updator) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("激活失败，商户不存在");
        }

        //商户已激活，无需再次修改
        if (merchant.getMchStatus().intValue() == MchStatusEnum.ACTIVE.getValue()){
            return;
        }

        if (merchant.getAuthStatus().intValue() != AuthStatusEnum.SUCCESS.getValue() &&
        merchant.getMchStatus().intValue() != MchStatusEnum.CREATE.getValue() &&
        merchant.getDealStatus().intValue() != DealStatusEnum.DEAL.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户当前状态无法激活");
        }

        merchant.setMchStatus(MchStatusEnum.ACTIVE.getValue());
        merchant.setActiveTime(new Date());
        merchant.setUpdateTime(new Date());
        merchant.setUpdator(updator);

        merchantDao.update(merchant);
    }

    public void changeLogo(MerchantLogoVo merchantLogoVo,String mchNo, String name) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        MerchantUpdateVo merchantUpdateVo = new MerchantUpdateVo();
        merchantUpdateVo.setUpdator(name);
        merchantUpdateVo.setMchNo(mchNo);
        merchantUpdateVo.setMchName(merchant.getMchName());
        merchantUpdateVo.setPlatformLogoUrl(merchantLogoVo.getPlatformLogoUrl());
        merchantUpdateVo.setLoginLogoUrl(merchantLogoVo.getLoginLogoUrl());
        updateLogoFile(merchantUpdateVo);
    }
}
