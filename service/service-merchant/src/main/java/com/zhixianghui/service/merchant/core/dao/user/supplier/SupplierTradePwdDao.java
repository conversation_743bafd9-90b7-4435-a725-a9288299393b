package com.zhixianghui.service.merchant.core.dao.user.supplier;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd;
import org.springframework.stereotype.Repository;

/**
 * 供应商支付密码表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-14
 */
@Repository
public class SupplierTradePwdDao extends MyBatisDao<SupplierTradePwd,Long> {
    public SupplierTradePwd getByMchNo(String mchNo) {
        return super.getOne("getByMchNo", mchNo);
    }
}
