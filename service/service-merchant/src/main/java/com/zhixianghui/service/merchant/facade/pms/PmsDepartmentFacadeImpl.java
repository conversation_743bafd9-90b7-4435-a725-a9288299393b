package com.zhixianghui.service.merchant.facade.pms;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsDepartmentBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
public class PmsDepartmentFacadeImpl implements PmsDepartmentFacade {

    @Autowired
    private PmsDepartmentBiz pmsDepartmentBiz;

    @Override
    public List<PmsDepartment> listDepartment() {
        return pmsDepartmentBiz.listDepartment();
    }

    @Override
    public List<PmsDepartment> listDepartmentByName(String departmentName) {
        return pmsDepartmentBiz.listDepartmentByName(departmentName);
    }

    @Override
    public PmsDepartment getDepartmentById(long id) {
        return pmsDepartmentBiz.getDepartmentById(id);
    }

    @Override
    public PmsDepartment getDepartmentByNumber(String number) {
        return pmsDepartmentBiz.getDepartmentByNumber(number);
    }

    @Override
    public List<PmsDepartment> getDepartmentByNumbers(List<String> numbers) {
        return pmsDepartmentBiz.getDepartmentByNumbers(numbers);
    }

    @Override
    public void createDepartment(PmsDepartment pmsDepartment) throws BizException {
        pmsDepartmentBiz.createDepartment(pmsDepartment);
    }

    @Override
    public void updateDepartment(PmsDepartment pmsDepartment) throws BizException {
        pmsDepartmentBiz.updateDepartment(pmsDepartment);
    }

    @Override
    public void deleteDepartmentById(long id) throws BizException {
        pmsDepartmentBiz.deleteDepartmentById(id);
    }

    @Override
    public List<PmsDepartment> listWithoutLeader(String departmentName) {
        return pmsDepartmentBiz.listWithoutLeader(departmentName);
    }

    @Override
    public void assignLeader(long departmentId, long leaderId) throws BizException {
        pmsDepartmentBiz.assignLeader(departmentId, leaderId);
    }

    @Override
    public List<Long> listSubDepartment(Long id) {
        return pmsDepartmentBiz.listSubDepartment(id);
    }

    @Override
    public boolean isSubOrSameDepartment(Long parentId, Long subId) {
        return pmsDepartmentBiz.isSubOrSameDepartment(parentId, subId);
    }
}
