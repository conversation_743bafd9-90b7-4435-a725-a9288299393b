package com.zhixianghui.service.merchant;

import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;


/**
 * 商户信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableCaching
public class ServiceMerchantApp {
    public static void main(String[] args) {
        new SpringApplicationBuilder(ServiceMerchantApp.class).web(WebApplicationType.NONE).run(args);
    }
}
