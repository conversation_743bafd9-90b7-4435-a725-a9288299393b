package com.zhixianghui.service.merchant.core.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.facade.merchant.entity.AgreementComponent;
import com.zhixianghui.service.merchant.core.dao.mapper.AgreementComponentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
* 模板组件表Biz
*
* <AUTHOR>
* @version 创建时间： 2022-09-02
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementComponentBiz {

    private final AgreementComponentMapper agreementComponentMapper;

    public List<AgreementComponent> getAll() {
        return agreementComponentMapper.selectList(new QueryWrapper<>());
    }
}