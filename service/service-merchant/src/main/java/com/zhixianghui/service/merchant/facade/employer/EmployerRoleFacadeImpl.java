package com.zhixianghui.service.merchant.facade.employer;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.service.employer.EmployerRoleFacade;
import com.zhixianghui.facade.merchant.vo.EmployerRoleVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.service.merchant.core.biz.user.portal.EmployerRoleBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class EmployerRoleFacadeImpl implements EmployerRoleFacade {
	
	@Autowired
	private EmployerRoleBiz employerRoleBiz;

	/**
	 * 创建用工企业后台角色
	 *
	 * @param role 角色
	 */
	@Override
	public void create(EmployerRole role) {
		employerRoleBiz.create(role);
	}

	/**
	 * 根据id查询用工企业后台角色
	 *
	 * @param mchNo 商户编号
	 * @param id
	 */
	@Override
	public EmployerRole getById(String mchNo, long id) {
		return employerRoleBiz.getById(mchNo, id);
	}

	/**
	 * 更新用工企业后台角色
	 *
	 * @param role 角色
	 */
	@Override
	public void update(EmployerRole role) throws BizException {
		employerRoleBiz.update(role);
	}

	/**
	 * 根据id删除用工企业后台角色
	 *
	 * @param mchNo  商户编号
	 * @param id	 角色id
	 */
	@Override
	public void deleteById(String mchNo, long id) throws BizException {
		employerRoleBiz.deleteById(mchNo, id);
	}

	/**
	 * 为角色分配功能
	 *
	 * @param mchNo         商户编号
	 * @param roleId      角色id
	 * @param functionIds 功能id
	 */
	@Override
	public void updateFunction(String mchNo, long roleId, List<Long> functionIds) throws BizException {
		employerRoleBiz.updateFunction(mchNo, roleId, functionIds);
	}

	/**
	 * 根据角色id获取其关联的功能
	 *
	 * @param mchNo    商户编号
	 * @param roleId 角色id
	 */
	@Override
	public List<EmployerFunction> listFunctionByRoleId(String mchNo, long roleId) throws BizException {
		return employerRoleBiz.listFunctionByRoleId(mchNo, roleId);
	}

	/**
	 * 根据角色id获取其关联的操作员
	 *
	 * @param mchNo    商户编号
	 * @param roleId 角色id
	 */
	@Override
	public List<EmployerStaffVO> listStaffByRoleId(String mchNo, long roleId) throws BizException {
		return employerRoleBiz.listStaffByRoleId(mchNo, roleId);
	}

	/**
	 * 查询所有角色
	 */
	@Override
	public List<EmployerRole> listAll(String mchNo) {
		return employerRoleBiz.listAll(mchNo);
	}

	/**
	 * 分页查询角色
	 *
	 * @param mchNo
	 * @param paramMap
	 * @param pageParam
	 */
	@Override
	public PageResult<List<EmployerRoleVo>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam) {
		return employerRoleBiz.listPage(mchNo, paramMap, pageParam);
	}

	@Override
	public PageResult<List<EmployerRoleVo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
		return employerRoleBiz.listPage(paramMap, pageParam);
	}

	@Override
	public Long count(EmployerRoleVo employerRoleVo, String mchNo) {
		return employerRoleBiz.count(employerRoleVo, mchNo);

	}
}
