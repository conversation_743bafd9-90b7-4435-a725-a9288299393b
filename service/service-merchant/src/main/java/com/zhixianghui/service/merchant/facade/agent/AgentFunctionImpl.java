package com.zhixianghui.service.merchant.facade.agent;


import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.service.agent.AgentFunctionFacade;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentFunctionBiz;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台功能表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentFunctionImpl implements AgentFunctionFacade {

    private final AgentFunctionBiz biz;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 创建用工企业后台功能
     *
     * @param function 功能
     */
    @Override
    public void create(AgentFunction function) throws BizException {
        biz.create(function);
    }

    /**
     * 根据id查询用工企业后台功能
     *
     * @param id
     */
    @Override
    public AgentFunction getById(long id){
        return biz.getById(id);
    }

    /**
     * 编辑用工企业后台功能
     *
     * @param function 功能
     */
    @Override
    public void update(AgentFunction function) throws BizException {
        biz.update(function);
    }

    /**
     * 根据id删除功能，并删除该功能与角色的映射
     *
     * @param id
     */
    @Override
    public void deleteById(long id) throws BizException {
        biz.deleteById(id);
    }

    /**
     * 查询所有功能
     */
    @Override
    public List<AgentFunction> listAll() {
        return biz.listAll();
    }

    @Override
    public List<AgentFunction> listAll(Map<String, Object> param, PageParam pageParam) {
        return biz.listAll(param, pageParam);
    }

    @Override
    public void export(FunctionVO functionVO) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(functionVO.getOperateName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGENT_FUNCTION.getFileName());
        record.setReportType(ReportTypeEnum.AGENT_FUNCTION.getValue());
        Map<String, Object> paramMap = BeanUtil.toMap(functionVO);
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.AGENT_FUNCTION.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }

    @Override
    public void saveFunction(List<AgentFunction> list) {
        biz.saveFunction(list);
    }

    @Override
    public String getPermissionFlag(Long parentId) {
        return biz.getPermissionFlag(parentId);
    }
}
