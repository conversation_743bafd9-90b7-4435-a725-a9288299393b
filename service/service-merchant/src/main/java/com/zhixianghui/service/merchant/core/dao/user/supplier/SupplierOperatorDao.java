package com.zhixianghui.service.merchant.core.dao.user.supplier;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierOperator;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台操作员表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class SupplierOperatorDao extends MyBatisDao<SupplierOperator, Long> {
    public SupplierOperator getByPhone(String phone) {
        return super.getOne("getByPhone", phone);
    }

    public PageResult<List<SupplierOperatorVO>> listOperatorVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        return super.listPage("listOperatorVoPage", "listOperatorVoPageCount", paramMap, pageParam);
    }
}
