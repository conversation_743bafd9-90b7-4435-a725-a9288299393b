package com.zhixianghui.service.merchant.core.biz.user.supplier;

import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalFunctionTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.service.merchant.core.dao.user.supplier.SupplierFunctionDao;
import com.zhixianghui.service.merchant.core.dao.user.supplier.SupplierRoleFunctionDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* 供应商后台功能表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierFunctionBiz {

    private final SupplierFunctionDao supplierFunctionDao;
    private final SupplierRoleFunctionDao supplierRoleFunctionDao;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 创建用工企业后台功能
     *
     * @param function 功能
     */
    public void create(SupplierFunction function) throws BizException {
        if (function.getCreateTime() == null) {
            function.setCreateTime(new Date());
        }
        if (function.getParentId() == null) {
            function.setParentId(0L);
        } else {
            SupplierFunction parent = supplierFunctionDao.getById(function.getParentId());
            if (parent == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能父节点不存在");
            } else if (parent.getType() != PortalFunctionTypeEnum.MENU_TYPE.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("父节点必须是菜单类型");
            }
        }

        try {
            supplierFunctionDao.insert(function);
        } catch (DuplicateKeyException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("权限标识重复");
        } catch (Exception e) {
            log.error("创建用工企业后台功能：系统异常", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("系统异常");
        }
    }

    /**
     * 根据id查询用工企业后台功能
     *
     * @param id
     */
    public SupplierFunction getById(long id) {
        return supplierFunctionDao.getById(id);
    }

    /**
     * 编辑用工企业后台功能
     *
     * @param function 功能
     */
    public void update(SupplierFunction function) throws BizException {
        supplierFunctionDao.update(function);
    }

    /**
     * 根据id删除功能，并删除该功能与角色的映射
     *
     * @param id
     */
    @Transactional
    public void deleteById(long id) throws BizException {
        SupplierFunction function = supplierFunctionDao.getById(id);
        if (function == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能不存在");
        }

        List<SupplierFunction> subFunctions = supplierFunctionDao.listByParentId(id);
        if (subFunctions.stream().anyMatch(p -> p.getType() == PortalFunctionTypeEnum.MENU_TYPE.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("存在子菜单，该菜单不能直接删除");
        }

        List<Long> deleteIds = subFunctions.stream().map(SupplierFunction::getId).collect(Collectors.toList());
        deleteIds.add(id);
        supplierFunctionDao.deleteByIds(deleteIds);				// 删除功能及其子功能
        supplierRoleFunctionDao.deleteByFunctionIds(deleteIds);	// 删除功能与角色的映射
    }

    /**
     * 查询所有功能
     */
    public List<SupplierFunction> listAll() {
        return supplierFunctionDao.listAll("number asc");
    }

    public List<SupplierFunction> listAll(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<SupplierFunction>> result = supplierFunctionDao.listPage(param, pageParam);
        return result == null ? null : result.getData();
    }

    public void export(FunctionVO functionVO) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(functionVO.getOperateName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.SUPPLIER_FUNCTION.getFileName());
        record.setReportType(ReportTypeEnum.SUPPLIER_FUNCTION.getValue());
        Map<String, Object> paramMap = BeanUtil.toMap(functionVO);
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.SUPPLIER_FUNCTION.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }

    public void saveFunction(List<SupplierFunction> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (SupplierFunction item : list) {
            if (StringUtils.isBlank(item.getParentPermissionFlag())) {
                item.setParentId(0L);
                supplierFunctionDao.insert("importFile", item);
                continue;
            }
            SupplierFunction supplierFunction = supplierFunctionDao.getOne(new HashMap<String, Object>(){{
                put("permissionFlag", item.getParentPermissionFlag());
            }});
            if (supplierFunction != null) {
                item.setParentId(supplierFunction.getId());
            }
            if (item.getParentId() == null) {
                log.error("parentId不能为空: [{}]", item.toString());
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("parentId不能为空");
            }
            supplierFunctionDao.insert("importFile", item);
        }

    }


    public String getPermissionFlag(Long parentId) {
        return supplierFunctionDao.getOne("getPermissionFlag", new HashMap<String, Object>(){{
            put("id", parentId);
        }});
    }
}