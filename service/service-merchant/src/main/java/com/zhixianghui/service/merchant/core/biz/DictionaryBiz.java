package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.service.merchant.core.dao.MerchantDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
public class DictionaryBiz {

    @Autowired
    private MerchantDao merchantDao;
    public DataDictionary getDataDictionaryByName(String dataName) {
        if (StringUtil.isEmpty(dataName)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("dataName不能为空");
        }
        return merchantDao.getDataDictionaryByName(Collections.singletonMap("dataName", dataName));
    }
}
