package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.vo.EmployerMainUpdateVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerMainBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;


/**
 * 用工商户合作信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantEmployerMainFacadeImpl implements MerchantEmployerMainFacade {

    @Autowired
    private MerchantEmployerMainBiz mainBiz;

    @Override
    public MerchantEmployerMain getByMchNo(String mchNo) {
        return mainBiz.getByMchNo(mchNo);
    }

    @Override
    public void updateOrInsert(EmployerMainUpdateVo vo, String loginName) throws BizException {
        mainBiz.updateOrInsert(vo, loginName);
    }

    @Override
    public Map<String, Object> getMainInfo(String mchNo) {
        return mainBiz.getMainInfo(mchNo);
    }

    @Override
    public Map<String, Object> getBusinessInfo(String mchNo) {
        return mainBiz.getBusinessInfo(mchNo);
    }

    @Override
    public void updateMain(MerchantUpdateVo merchantUpdateVo) {
        mainBiz.updateMain(merchantUpdateVo);
    }

    @Override
    public void updateBusiness(MerchantUpdateVo merchantUpdateVo) {
        mainBiz.updateBusiness(merchantUpdateVo);
    }
}
