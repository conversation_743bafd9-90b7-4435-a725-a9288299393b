package com.zhixianghui.service.merchant.core.biz.user.agent;

import com.zhixianghui.service.merchant.core.dao.user.agent.AgentStaffRoleDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
* 供应商后台员工角色关联表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentStaffRoleBiz {

    private final AgentStaffRoleDao agentStaffRoleDao;

    public Long countEmployerCount(Long roleId) {
        return agentStaffRoleDao.countBy("countEmployerByRoleId",
                new HashMap<String, Object>(){{ put("roleId", roleId);
        }});
    }
}