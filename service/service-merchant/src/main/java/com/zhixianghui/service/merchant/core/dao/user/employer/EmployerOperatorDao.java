package com.zhixianghui.service.merchant.core.dao.user.employer;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerOperator;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


@Repository
public class EmployerOperatorDao extends MyBatisDao<EmployerOperator, Long>{

    public EmployerOperator getByPhone(String phone) {
        return super.getOne("getByPhone", phone);
    }

    public PageResult<List<EmployerOperatorVO>> listOperatorVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        return super.listPage("listOperatorVoPage", "listOperatorVoPageCount", paramMap, pageParam);
    }
}
