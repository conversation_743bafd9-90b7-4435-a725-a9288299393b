package com.zhixianghui.service.merchant.core.biz.user.supplier;

import com.zhixianghui.service.merchant.core.dao.user.supplier.SupplierStaffRoleDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;

/**
* 供应商后台员工角色关联表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierStaffRoleBiz {

    private final SupplierStaffRoleDao supplierStaffRoleDao;

    public Long countEmployerCount(Long roleId) {
        return supplierStaffRoleDao.countBy("countEmployerByRoleId",
                new HashMap<String, Object>(){{ put("roleId", roleId);
                }});
    }
}