package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.service.merchant.core.dao.MerchantInvoiceInfoDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商户开票信息
 *
 * <AUTHOR> G<PERSON>
 */
@Slf4j
@Component
public class MerchantInvoiceInfoBiz {

    @Autowired
    private MerchantInvoiceInfoDao invoiceInfoDao;

    public MerchantInvoiceInfo getByMchNo(String mchNo){
        return invoiceInfoDao.getByMchNo(mchNo);
    }

    public void update(MerchantInvoiceInfo invoiceInfo){
        invoiceInfoDao.update(invoiceInfo);
    }

    public void insert(MerchantInvoiceInfo invoiceInfo) {
        invoiceInfoDao.insert(invoiceInfo);
    }
}
