package com.zhixianghui.service.merchant.facade;


import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.dto.common.RecordItemVo;
import com.zhixianghui.common.statics.enums.account.AccountStatusEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.report.MerchantChannelTypeEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.IndustryTypeFacade;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.enums.MerchantChannelApi;
import com.zhixianghui.facade.merchant.service.MerchantChannelFacade;
import com.zhixianghui.facade.merchant.util.SignUtil;
import com.zhixianghui.facade.merchant.vo.MerchantChannelVo;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.service.merchant.core.biz.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商户报备通道表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-04-20
 */
@Slf4j
@Service(timeout = 20000)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantChannelImpl implements MerchantChannelFacade {

    private final MerchantChannelBiz biz;
    private final MerchantEmployerQuoteBiz merchantEmployerQuoteBiz;
    private final MerchantEmployerMainBiz merchantEmployerMainBiz;
    private final MerchantBankAccountBiz merchantBankAccountBiz;
    private final MerchantEmployerPositionBiz merchantEmployerPositionBiz;
    private final MerchantEmployerCooperateBiz cooperateBiz;
    private final MerchantBiz merchantBiz;

    @Reference
    private IndustryTypeFacade industryTypeFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private RecordItemFacade recordItemFacade;
    @Reference
    private EmployerAccountInfoFacade accountInfoFacade;

    private final RestTemplate restTemplate;


    @Override
    public MerchantChannel getByMchNo(String mchNo) {
        return biz.getByMchNo(mchNo);
    }


    @Override
    public void synchronizeRelation(String beginTime, String endTime) {
        // 查找出需要同步数据的商户号
        List<String> merchantNoList = biz.listAll();
        log.info("同步代征主体关联信息 : {}", JSONObject.toJSON(merchantNoList));
        // 查找该商户号对应的记录
        for (String item : merchantNoList) {
            MerchantChannel temp = biz.getByMchNo(item);
            List<EmployerMainstayRelation> result = employerMainstayRelationFacade.listBy(
                    new HashMap<String, Object>() {
                        private static final long serialVersionUID = -2478556360827772897L;

                        { put("mainstayNo", item); }}
            );

            for (EmployerMainstayRelation relation : result) {
                try {
                    log.info("同步用工企业信息 : {}", relation);
                    handle(temp, merchantBiz.getByMchNo(relation.getEmployerNo()));
                } catch (Exception e) {
                    log.error("[{}]企业同步信息异常 :", relation.getEmployerName(), e);
                }
            }
        }
        log.info("同步代征主体关联信息 : {}-{}", beginTime, endTime);
    }

    /**
     * 处理企业开户/修改 地址
     * @param merchantChannel 代征主体通道信息
     * @param employer 用工企业信息
     */

    @Override
    public void handle(MerchantChannel merchantChannel, Merchant employer) {
        // 获取该代征主体的所有配置
        Map<String, String> map = biz.listByMcnNo(merchantChannel.getMchNo()).stream().collect(
                Collectors.toMap(MerchantChannel ::getConfigKey, MerchantChannel:: getConfigValue)
        );
        String flag = employer.getMchName() + "-" + System.currentTimeMillis();
        // 查询用工企业费率信息
        List<MerchantEmployerQuote> quote = merchantEmployerQuoteBiz.getQuoteList(employer.getMchNo(),
                MapUtil.builder(new HashMap<String, Object>())
                        .put("supplierNo", merchantChannel.getMchNo())
                        .put("status", CommonStatusEnum.ACTIVE.getValue()).build());
        if (CollectionUtils.isEmpty(quote) || CollectionUtils.isEmpty(quote.get(0).getQuoteRateList()) || Objects.isNull(quote.get(0).getQuoteRateList().get(0).getRate())) {
           log.error("产品费率信息为空, 请先创建 mchName:{} mchNo:{} supplierNo:{} MerchantEmployerQuote:{}", employer.getMchName(), employer.getMchNo(), merchantChannel.getMchNo(), JsonUtil.toString(quote));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "-产品费率信息为空, 请先创建");
        }
        // 查询用工企业主体信息
        MerchantEmployerMain employerMain = merchantEmployerMainBiz.getByMchNo(employer.getMchNo());
        // 查询用工企业账户信息
        MerchantBankAccount account = merchantBankAccountBiz.getByMchNo(employer.getMchNo());
        // 查询商户岗位信息表
        List<MerchantEmployerPosition> positionList = merchantEmployerPositionBiz.listByMchNo(employer.getMchNo());
        // 查询合作信息
        MerchantEmployerCooperate cooperate = cooperateBiz.getByMchNo(employer.getMchNo());

        // 查询代征关系表
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employer.getMchNo(), merchantChannel.getMchNo());

        // 构造请求参数
        MerchantChannelVo merchantChannelVo = new MerchantChannelVo().toAddBusinesses(
                positionList, cooperate, quote.get(0), employer, employerMain, account, map
        );

        if (StringUtils.isNotBlank(employerMainstayRelation.getParamInfo())
        && StringUtils.isNotBlank(JSON.parseObject(employerMainstayRelation.getParamInfo()).getString("number"))){
//            merchantChannelVo.setNumber(JSON.parseObject(employerMainstayRelation.getParamInfo()).getString("number"));
            return;
        }

        merchantChannelVo.setServiceRate(String.valueOf(new BigDecimal(merchantChannelVo.getServiceRate()).divide(new BigDecimal(100), 2)));
        String address = map.get(MerchantChannelApi.RYSC_ADDRESS_DOMAIN) + map.get(MerchantChannelApi.RYSC_ADD_BUSINESSES_API);
        ResponseEntity<String> responseEntity = null;

        log.info("[{}]请求参数 : {}", flag, JSONObject.toJSONString(merchantChannelVo));

        try {
            responseEntity = restTemplate.postForEntity(address,
                    JSONObject.toJSONString(merchantChannelVo), String.class
            );
        } catch (RestClientException e) {
            log.error("[{}]奔奔代征主体======>处理企业开户/修改请求异常:", flag, e);
        }
        log.info("[{}]奔奔代征主体======>处理企业开户/修改, 响应数据 : {}", flag, JSONObject.toJSON(responseEntity));
        if (responseEntity == null || StringUtils.isBlank(responseEntity.getBody())) {
            updateEmployerMainstayRelation(null, employer.getMchNo(), merchantChannel.getMchNo(), false);
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg(flag + "-奔奔代征主体======>处理企业开户/修改," +
                    merchantChannelVo.getName() + "企业已经存在...");
        }
        MerchantChannelVo response = JSONObject.parseObject(responseEntity.getBody(), MerchantChannelVo.class);
        if (response == null) {
            updateEmployerMainstayRelation(response, employer.getMchNo(), merchantChannel.getMchNo(), false);
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg(flag + "奔奔代征主体======>处理企业开户/修改," +
                    merchantChannelVo.getName() + "响应为空");
        }
        if (!MerchantChannelApi.RYSC_SUCCESS_CODE.equals(response.getCode())) {
            if (response.getMsg().contains("企业已经存在")) {
                log.warn("[{}]奔奔代征主体======>处理企业开户/修改 : {} 企业已经存在...", flag, merchantChannelVo.getName());
                return;
            }
            response.setServerRate(new BigDecimal(merchantChannelVo.getChannelServiceRate()));
            updateEmployerMainstayRelation(response, employer.getMchNo(), merchantChannel.getMchNo(), false);
            return;
        }
        // 更新代征关系所属表
        response.setServerRate(new BigDecimal(merchantChannelVo.getChannelServiceRate()));
        updateEmployerMainstayRelation(response, employer.getMchNo(), merchantChannel.getMchNo(), true);
    }

    private void updateEmployerMainstayRelation(MerchantChannelVo response, String employerNo, String supplierNo, boolean success) {
        EmployerMainstayRelation temp = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo, supplierNo);
//        if (temp != null && temp.getAccountStatus() == MerchantChannelTypeEnum.SUCCESS.getType()) {
//            return;
//        }
        EmployerMainstayRelation employerMainstayRelation = new EmployerMainstayRelation();
        employerMainstayRelation.setEmployerNo(employerNo);
        employerMainstayRelation.setMainstayNo(supplierNo);
        employerMainstayRelation.setUpdateTime(new Date());
        if (!success) {
            employerMainstayRelation.setAccountStatus(MerchantChannelTypeEnum.FAIL.getType());
            employerMainstayRelationFacade.updateEmployerMainstayRelation(employerMainstayRelation);
            return;
        }
        employerMainstayRelation.setParamInfo(JSONObject.toJSONString(response));
        employerMainstayRelation.setAccountStatus(MerchantChannelTypeEnum.SUCCESS.getType());
        employerMainstayRelationFacade.updateEmployerMainstayRelation(employerMainstayRelation);
    }


    private static final int PAGE = 1;
    private static final int PAGE_SIZE = 500;
    @Override
    public void synchronizeRecordItem(String beginTime, String endTime) {
        Date subTableBeginDate = DateUtil.parseTime(beginTime);
        Date subTableEndDate = DateUtil.parseTime(endTime);
        // 查找出需要同步数据的商户号
        List<String> merchantNoList = biz.listAll();
        log.info("待同步商户号 : {}", JSONObject.toJSON(merchantNoList));
        // 查找该商户号对应的记录
        for (String item : merchantNoList) {
            int currentPage = PAGE;
            List<RecordItem> resultList = recordItemFacade.listByTime(beginTime, endTime, subTableBeginDate, subTableEndDate, item, PageParam.newInstance(currentPage, PAGE_SIZE));
            while (!CollectionUtils.isEmpty(resultList)) {
                List<RecordItemVo> recordItemVoList = new ArrayList<>(resultList.size());
                for (RecordItem recordItem : resultList) {
                    RecordItemVo recordItemVo = new RecordItemVo();
                    BeanUtil.copyProperties(recordItem, recordItemVo);
                    recordItemVoList.add(recordItemVo);
                }
                for (RecordItemVo recordItemVo : recordItemVoList) {
                    try {
                        synchronizeRecordItem(recordItemVo);
                    } catch (Exception e) {
                        log.info("同步订单失败 : ", e);
                    }
                }
                currentPage++;
                resultList = recordItemFacade.listByTime(beginTime, endTime, subTableBeginDate, subTableEndDate, item, PageParam.newInstance(currentPage, PAGE_SIZE));
            }
        }
        log.info("同步订单结束 : {}-{}", beginTime, endTime);
    }

    @Override
    public void synchronizeRecordItem(RecordItemVo recordItemVo) {
        log.info("用工企业 : {}, 代征主体 : {}, 开始走接口同步数据 :{}", recordItemVo.getEmployerNo(), recordItemVo.getMainstayNo(), JSONObject.toJSON(recordItemVo));
        if (StringUtils.isAnyBlank(recordItemVo.getEmployerNo(), recordItemVo.getMainstayNo())) {
            log.error("待同步支付数据的渠道的用工企业id或代征主体id为空 : {}", Objects.requireNonNull(recordItemVo).toString());
            return;
        }
        // 查询代征所属关系
        EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(recordItemVo.getEmployerNo(), recordItemVo.getMainstayNo());
        if (relation.getAccountStatus() == null || relation.getAccountStatus() != AccountStatusEnum.ACTIVE.getValue()) {
            return;
        }
        MerchantChannelVo merchantChannelVo = JSONObject.parseObject(relation.getParamInfo(), MerchantChannelVo.class);
        if (merchantChannelVo == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("该代征主体缺少必要参数...");
        }

        Map<String, String> map = biz.listByMcnNo(recordItemVo.getMainstayNo()).stream().collect(
                Collectors.toMap(MerchantChannel :: getConfigKey, MerchantChannel:: getConfigValue)
        );

        merchantChannelVo.setServerRate(new BigDecimal(map.get(MerchantChannelApi.RSY_CHANNEL_SERVICE_RATE)));
        MerchantChannelVo vo = toSynchronizeRecordItem(recordItemVo, merchantChannelVo);

        String certFileUrl = map.get(MerchantChannelApi.RSY_CERTFILE_BASE_URL) + "?platTrxNo=" + AESUtil.encryptECB(recordItemVo.getPlatTrxNo(),EncryptKeys.getEncryptKeyById(100L).getEncryptKeyStr()) + "&mainstayNo=" + recordItemVo.getMainstayNo();
        vo.getBizContent().get(0).setImageInfo(certFileUrl);

        vo.setCompany_number(map.get(MerchantChannelApi.RYSC_LANDING_PARK_ID));
        log.info("请求参数:" + JSONObject.toJSONString(vo));
        String address = map.get(MerchantChannelApi.RYSC_ADDRESS_DOMAIN) + map.get(MerchantChannelApi.RYSC_PAY_DATA_API);
        ResponseEntity<String> responseEntity = null;
        try {
            responseEntity = restTemplate.postForEntity(address,
                    JSONObject.toJSONString(vo), String.class
            );
        } catch (RestClientException e) {
            log.error("[{}]奔奔代征主体======>同步支付数据请求异常: {}", vo.getOutTradeNo(), e);
            return;
        }
        log.info("[{}]奔奔代征主体======>同步支付数据成功 : {}", vo.getOutTradeNo(), JSONObject.toJSON(responseEntity));
    }

    /**
     * 设置请求参数
     * @param recordItemVo
     * @param merchantChannelVo
     * @return
     */
    private static final int DEFAULT_LENGTH = 10;
    private MerchantChannelVo toSynchronizeRecordItem(RecordItemVo recordItemVo, MerchantChannelVo merchantChannelVo) {
        // 解密
        String bankCode = AESUtil.decryptECB(recordItemVo.getReceiveAccountNo(),
                EncryptKeys.getEncryptKeyById(recordItemVo.getEncryptKeyId()).getEncryptKeyStr()
        );
        recordItemVo.setReceiveAccountNo(bankCode);
        String receiveName = AESUtil.decryptECB(recordItemVo.getReceiveName(),
                EncryptKeys.getEncryptKeyById(recordItemVo.getEncryptKeyId()).getEncryptKeyStr()
        );
        recordItemVo.setReceiveName(receiveName);
        String receiveIdCardNo = AESUtil.decryptECB(recordItemVo.getReceiveIdCardNo(),
                EncryptKeys.getEncryptKeyById(recordItemVo.getEncryptKeyId()).getEncryptKeyStr()
        );
        recordItemVo.setReceiveIdCardNo(receiveIdCardNo);
        String receivePhoneNo = AESUtil.decryptECB(recordItemVo.getReceivePhoneNo(),
                EncryptKeys.getEncryptKeyById(recordItemVo.getEncryptKeyId()).getEncryptKeyStr()
        );
        recordItemVo.setReceivePhoneNo(receivePhoneNo);
        merchantChannelVo.setNonceStr(RandomStringUtils.randomAlphanumeric(DEFAULT_LENGTH));
        MerchantChannelVo vo = new MerchantChannelVo().toSynchronizeRecordItem(recordItemVo, merchantChannelVo);
        vo.setSign(SignUtil.sign(vo, merchantChannelVo.getAppsecret()));
        return vo;
    }

    @Reference
    private SignRecordFacade signRecordFacade;
    @Override
    public void syncSignInfo(Date startTime, Date endTime){

        // 查找出需要同步数据的商户号
        List<String> merchantNoList = biz.listAll();
        log.info("待同步商户号 : {}", JSONObject.toJSON(merchantNoList));
        for (String mainstayNo : merchantNoList) {
            Map<String, String> map = biz.listByMcnNo(mainstayNo).stream().collect(
                    Collectors.toMap(MerchantChannel :: getConfigKey, MerchantChannel:: getConfigValue)
            );

            // 查询待同步签约信息
            Map<String, Object> paramMap = MapUtil.builder(new HashMap<String, Object>())
                    .put("mainstayNo", mainstayNo)
                    .put("signStatus", SignStatusEnum.SIGN_SUCCESS.getValue())
                    .put("createBeginDate", startTime)
                    .put("createEndDate", endTime)
                    .build();
            log.info("查询签约记录参数:{}", JSONUtil.toJsonPrettyStr(paramMap));
            PageResult<List<SignRecord>> signRecordPage = signRecordFacade.listPage(paramMap, PageParam.newInstance(1, 1000));

            if (signRecordPage!=null){
                List<SignRecord> signRecordList = signRecordPage.getData();
                if (signRecordList != null && !signRecordList.isEmpty()) {
                    for (SignRecord signRecord : signRecordList) {
                        log.info("同步签约数据{}到{}:\n{}",signRecord.getReceiveIdCardNoDecrypt(),signRecord.getMainstayName(), JSONUtil.toJsonPrettyStr(signRecord));
                        EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(signRecord.getEmployerNo(), signRecord.getMainstayNo());
                        if (relation.getAccountStatus() == null || relation.getAccountStatus() != AccountStatusEnum.ACTIVE.getValue()) {
                            continue;
                        }
                        syncSignInfo(map,
                                JSONUtil.parseObj(relation.getParamInfo()),
                                signRecord.getReceiveIdCardNoDecrypt(),
                                signRecord.getReceiveNameDecrypt(),
                                signRecord.getUpdateTime().getTime(),
                                signRecord.getFileUrl());
                    }
                }
            }

        }
    }

    @Reference
    private UserInfoFacade userInfoFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Override
    public void syncUserInfo(Date startTime, Date endTime){

        // 查找出需要同步数据的商户号
        List<String> merchantNoList = biz.listAll();
        log.info("待同步商户号 : {}", JSONObject.toJSON(merchantNoList));
        for (String mainstayNo : merchantNoList) {
            Map<String, String> map = biz.listByMcnNo(mainstayNo).stream().collect(
                    Collectors.toMap(MerchantChannel :: getConfigKey, MerchantChannel:: getConfigValue)
            );

            // 查询待同步签约信息
            Map<String, Object> paramMap = MapUtil.builder(new HashMap<String, Object>())
                    .put("mainstayNo", mainstayNo)
                    .put("createBeginDate", startTime)
                    .put("createEndDate", endTime)
                    .build();
            PageResult<List<SignRecord>> signRecordPage = signRecordFacade.listPage(paramMap, PageParam.newInstance(1, 10000));
            if (signRecordPage!=null){
                List<SignRecord> signRecordList = signRecordPage.getData();
                if (signRecordList != null && !signRecordList.isEmpty()) {
                    for (SignRecord signRecord : signRecordList) {
                        log.info("同步签约数据{}到{}:\n{}",signRecord.getReceiveIdCardNoDecrypt(),signRecord.getMainstayName(), JSONUtil.toJsonPrettyStr(signRecord));
                        EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(signRecord.getEmployerNo(), signRecord.getMainstayNo());
                        if (relation.getAccountStatus() == null || relation.getAccountStatus() != AccountStatusEnum.ACTIVE.getValue()) {
                            continue;
                        }
                        if (StringUtils.isNotBlank(signRecord.getIdCardFrontUrl())) {
                            uploadIdImages(map,JSONUtil.parseObj(relation.getParamInfo()),signRecord.getReceiveIdCardNoDecrypt(),signRecord.getReceiveNameDecrypt(),signRecord.getIdCardFrontUrl(),signRecord.getIdCardBackUrl());
                        } else if (StringUtils.isNotBlank(signRecord.getIdCardCopyUrl())) {
                            uploadIdImages(map,JSONUtil.parseObj(relation.getParamInfo()),signRecord.getReceiveIdCardNoDecrypt(),signRecord.getReceiveNameDecrypt(),signRecord.getIdCardCopyUrl(),signRecord.getIdCardCopyUrl());
                        }else {
                            UserInfo userInfo = userInfoFacade.getByIdCardNoMd5(signRecord.getReceiveIdCardNoMd5());
                            if (userInfo != null) {
                                uploadIdImages(map,JSONUtil.parseObj(relation.getParamInfo()),userInfo.getReceiveIdCardNoDecrypt(),userInfo.getReceiveNameDecrypt(),userInfo.getIdCardFrontUrl(),userInfo.getIdCardBackUrl());
                            }
                        }
                    }
                }
            }

            Map<String, Object> orderItemParam = MapUtil.builder(new HashMap<String, Object>())
                    .put("mainstayNo", mainstayNo)
                    .put("processStatus", RecordItemStatusEnum.PAY_SUCCESS.getValue())
                    .put("createBeginDate", startTime)
                    .put("createEndDate", endTime)
                    .build();
            int page = 1;
            int pageSize = 100;
            PageResult<List<RecordItem>> orderItemPage = null;
            do {
                PageParam pageParam = PageParam.newInstance(page, pageSize);
                orderItemPage = recordItemFacade.listPage(orderItemParam, pageParam);
                if (orderItemPage != null) {
                    List<RecordItem> orderItems = orderItemPage.getData();
                    if (orderItems != null && !orderItems.isEmpty()) {
                        for (RecordItem orderItem : orderItems) {
                            EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(orderItem.getEmployerNo(), orderItem.getMainstayNo());
                            if (relation.getAccountStatus() == null || relation.getAccountStatus() != AccountStatusEnum.ACTIVE.getValue()) {
                                continue;
                            }
                            String receiveIdCardNoMd5 = orderItem.getReceiveIdCardNoMd5();
                            UserInfo userInfo = userInfoFacade.getByIdCardNoMd5(receiveIdCardNoMd5);
                            if (userInfo != null) {
                                uploadIdImages(map,JSONUtil.parseObj(relation.getParamInfo()),userInfo.getReceiveIdCardNoDecrypt(),userInfo.getReceiveNameDecrypt(),userInfo.getIdCardFrontUrl(),userInfo.getIdCardBackUrl());
                            }
                        }
                    }
                }
            } while (orderItemPage != null && orderItemPage.getData() != null && orderItemPage.getData().size() == pageSize);
        }
    }

    private void uploadIdImages(Map<String, String> map,Map<String, Object> paramInfo,String idNo,String name,String frontUrl,String backUrl){
        cn.hutool.json.JSONObject param = new cn.hutool.json.JSONObject();
        param.putOnce("appId", paramInfo.get("appId"));
        param.putOnce("ecid", paramInfo.get("ecid"));
        param.putOnce("mtchId", paramInfo.get("mtchId"));
        param.putOnce("idno", idNo);
        param.putOnce("name", name);
        param.putOnce("nonceStr", RandomUtil.get16LenStr());
        param.putOnce("signType", "MD5");

        String signStr = signParam(param, String.valueOf(paramInfo.get("appsecret")));
        param.putOnce("sign", signStr);
        param.putOnce("frontImg", map.get(MerchantChannelApi.RSY_IMAGE_BASE_URL)+backUrl);
        param.putOnce("backImg", map.get(MerchantChannelApi.RSY_IMAGE_BASE_URL)+frontUrl);

        String address = map.get(MerchantChannelApi.RYSC_ADDRESS_DOMAIN) + map.get(MerchantChannelApi.RSY_RECEIVER_OTHER_INFO_URL);
        log.info("【uploadIdImages】请求请求路径：{},\n参数:{}", address, JSONUtil.toJsonPrettyStr(param));
        try {
            ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(address,
                    param.toString(), String.class
            );
            log.info("【uploadIdImages】请求请求返参:{}",JSONUtil.toJsonPrettyStr(stringResponseEntity));
            String body = stringResponseEntity.getBody();
            if (body != null) {
                JSONObject resultJSON = JSONObject.parseObject(body);
                if (resultJSON.getIntValue("code") != 200) {
                    log.error("同步身份证数据数据请求失败：{}",resultJSON.getString("msg"));
                    if (resultJSON.getString("msg") != null && StringUtils.contains(resultJSON.getString("msg"), "自由职业者信息不存在")) {
                        return;
                    }else {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("同步身份证数据数据请求失败:" + resultJSON.getString("msg"));
                    }
                }
            }
        } catch (RestClientException e) {
            log.error("奔奔代征主体======>同步身份证数据数据请求异常: {}",  e);
            return;
        }
    }

    private void syncSignInfo(Map<String, String> map,Map<String, Object> paramInfo,String idNo,String name,Long signTime,String contractInfo) {
        {
            cn.hutool.json.JSONObject param = new cn.hutool.json.JSONObject();
            param.putOnce("appId", paramInfo.get("appId"));
            param.putOnce("ecid", paramInfo.get("ecid"));
            param.putOnce("mtchId", paramInfo.get("mtchId"));
            param.putOnce("idno", idNo);
            param.putOnce("name", name);
            param.putOnce("signTime", signTime);
            param.putOnce("nonceStr", RandomUtil.get16LenStr());
            param.putOnce("signType", "MD5");


            String signStr = signParam(param, String.valueOf(paramInfo.get("appsecret")));
            param.putOnce("sign", signStr);
            param.putOnce("type", 2);
            param.putOnce("contractInfo", map.get(MerchantChannelApi.RSY_IMAGE_BASE_URL)+contractInfo);
            String address = map.get(MerchantChannelApi.RYSC_ADDRESS_DOMAIN) + map.get(MerchantChannelApi.RSY_RECEIVER_CONTRACT_INFO_URL);
            log.info("【syncSignInfo】请求请求路径：{},\n参数:{}", address, JSONUtil.toJsonPrettyStr(param));
            try {
                ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(address,
                        param.toString(), String.class
                );
                log.info("【syncSignInfo】请求请求返参:{}", JSONUtil.toJsonPrettyStr(stringResponseEntity));
                String body = stringResponseEntity.getBody();
                if (body != null) {
                    JSONObject resultJSON = JSONObject.parseObject(body);
                    if (resultJSON.getIntValue("code") != 200) {
                        log.error("同步签约数据数据请求失败：{}",resultJSON.getString("msg"));
                        if (resultJSON.getString("msg") != null && StringUtils.contains(resultJSON.getString("msg"), "自由职业者信息不存在")) {
                            return;
                        }else {
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("同步签约数据数据请求失败:" + resultJSON.getString("msg"));
                        }
                    }
                }
            } catch (RestClientException e) {
                log.error("奔奔代征主体======>同步签约数据数据请求异常: {}",  e);
                return;
            }
        }
    }

    private String signParam(Map<String, Object> params,String key) {
        String sortJoin = MapUtil.sortJoin(params, "&", "=", true)+"&key="+key;
        log.info("【奔奔签约】待加密字符串：{}", sortJoin);
        String sign = MD5Util.getMD5Hex(sortJoin).toUpperCase();
        return sign;
    }

    @Override
    public void rsyncRechargeInfoToBenben(Date beginTime, Date endTime){
        log.info("开始同步商户转供应商数据-时间段：{}--{}", beginTime, endTime);

        // 查找出需要同步数据的商户号
        List<String> merchantNoList = biz.listAll();
        log.info("待同步商户号 : {}", JSONObject.toJSON(merchantNoList));
        // 查找该商户号对应的记录
        for (String item : merchantNoList) {
            int currentPage = PAGE;
            int pageSize = 500;
            List<RecordItem> resultList = recordItemFacade.listByTime(beginTime, endTime, item, PageParam.newInstance(currentPage, pageSize));
            while (!CollectionUtils.isEmpty(resultList)) {
                for (RecordItem recordItem : resultList) {
                    try {
                        EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(recordItem.getEmployerNo(), recordItem.getMainstayNo());
                        if (relation.getAccountStatus() == null || relation.getAccountStatus() != AccountStatusEnum.ACTIVE.getValue()) {
                            continue;
                        }
                        final EmployerAccountInfo accountInfo = accountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(recordItem.getEmployerNo(), recordItem.getMainstayNo(), recordItem.getPayChannelNo());
                        if (accountInfo == null) {
                            continue;
                        }
                        final cn.hutool.json.JSONObject paramInfo = JSONUtil.parseObj(relation.getParamInfo());

                        Map<String, String> map = biz.listByMcnNo(recordItem.getMainstayNo()).stream().collect(
                                Collectors.toMap(MerchantChannel :: getConfigKey, MerchantChannel:: getConfigValue)
                        );

                        cn.hutool.json.JSONObject param = new cn.hutool.json.JSONObject();
                        param.putOnce("appId", paramInfo.get("appId"));
                        param.putOnce("ecid", paramInfo.get("ecid"));
                        param.putOnce("mtchId", paramInfo.get("mtchId"));
                        param.putOnce("currency", "CNY");
                        param.putOnce("nonceStr", RandomUtil.get16LenStr());
                        param.putOnce("signType", "MD5");
                        param.putOnce("txId", recordItem.getRemitPlatTrxNo());
                        param.putOnce("remitAmount", recordItem.getOrderAmount().toPlainString());
                        param.putOnce("payeeCardName", recordItem.getMainstayName());
                        param.putOnce("payeeCardNo", accountInfo.getParentMerchantNo());
                        param.putOnce("payerCardName", recordItem.getEmployerName());
                        param.putOnce("payerCardNo", accountInfo.getSubMerchantNo());

                        String signStr = signParam(param, String.valueOf(paramInfo.get("appsecret")));
                        param.putOnce("sign", signStr);
                        param.putOnce("company_number", map.get(MerchantChannelApi.RYSC_LANDING_PARK_ID));
                        param.putOnce("gmt_create", DateUtil.formatDateTime(recordItem.getCreateTime()));
                        String address = map.get(MerchantChannelApi.RYSC_ADDRESS_DOMAIN) +"/fw/synchronous/recharge";
                        log.info("【rsyncRechargeInfoToBenben】请求请求路径：{},\n参数:{}", address, JSONUtil.toJsonPrettyStr(param));
                        try {
                            ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(address,
                                    param.toString(), String.class
                            );
                            log.info("【rsyncRechargeInfoToBenben】[{}]-请求请求返参:{}", recordItem.getRemitPlatTrxNo(),JSONUtil.toJsonPrettyStr(stringResponseEntity));
                            String body = stringResponseEntity.getBody();
                            if (body != null) {
                                JSONObject resultJSON = JSONObject.parseObject(body);
                                if (resultJSON.getIntValue("code") != 200) {
                                    log.error("[同步失败]同步转账到代征主体数据请求失败[{}-{}]：{}",recordItem.getEmployerNo(),recordItem.getRemitPlatTrxNo(), resultJSON.getString("msg"));
                                    log.error("[同步失败]原始参数[{}]：{}",recordItem.getRemitPlatTrxNo(),JSONUtil.toJsonPrettyStr(param));
                                    continue;
                                }
                                log.info("同步转账到代征主体数据成功-[{}]-mainstayNo:{},employerNo:{}",recordItem.getRemitPlatTrxNo(),recordItem.getMainstayNo(),recordItem.getEmployerNo());
                            }
                        } catch (RestClientException e) {
                            log.error("【rsyncRechargeInfoToBenben】奔奔代征主体======>同步订单请求异常: {}",  e);
                            continue;
                        }
                    } catch (Exception e) {
                        log.info("【rsyncRechargeInfoToBenben】同步订单失败 : ", e);
                    }
                }
                currentPage++;
                resultList = recordItemFacade.listByTime(beginTime, endTime, item, PageParam.newInstance(currentPage, pageSize));
            }
        }
        log.info("【rsyncRechargeInfoToBenben】同步订单结束 : {}-{}", beginTime, endTime);
    }
}
