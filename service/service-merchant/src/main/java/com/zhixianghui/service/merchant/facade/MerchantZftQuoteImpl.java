package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.service.MerchantZftQuoteFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantZftQuoteBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-05-09
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantZftQuoteImpl implements MerchantZftQuoteFacade {

    private final MerchantZftQuoteBiz biz;
}
