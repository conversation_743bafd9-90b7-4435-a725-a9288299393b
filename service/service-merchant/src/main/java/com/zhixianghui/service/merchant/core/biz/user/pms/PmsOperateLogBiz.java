package com.zhixianghui.service.merchant.core.biz.user.pms;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.IPUtil;
import com.zhixianghui.facade.merchant.entity.Ips;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog;
import com.zhixianghui.service.merchant.core.dao.IpsDao;
import com.zhixianghui.service.merchant.core.dao.user.pms.PmsOperateLogDao;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Author: Cmf
 * Date: 2019/11/1
 * Time: 15:49
 * Description:
 */
@Service
public class PmsOperateLogBiz {
    private static final Logger LOGGER = LoggerFactory.getLogger(PmsOperateLogBiz.class);
    @Autowired
    private PmsOperateLogDao pmsOperateLogDao;
    @Autowired
    private IpsDao ipsDao;

    public void createOperateLog(PmsOperateLog operateLog) {
        pmsOperateLogDao.insert(operateLog);
    }

    public PmsOperateLog createOperateLogGetkey(PmsOperateLog operateLog) {
        return pmsOperateLogDao.createLog(operateLog);
    }


    public PageResult<List<PmsOperateLog>> listOperateLogPage(Map<String, Object> paramMap, PageParam pageParam) {
        return pmsOperateLogDao.listPage(paramMap, pageParam);
    }

    public PmsOperateLog getOperateLogById(Long id) {
        return pmsOperateLogDao.getById(id);
    }

    public void updateOperateLog(PmsOperateLog operateLog){
        pmsOperateLogDao.updateIfNotNull(operateLog);
    }


    private static final int PAGE = 1;
    private static final int PAGE_SIZE = 500;
    public void ipInfoHandle(String id) {
        int currentPage = PAGE;

        PageResult<List<PmsOperateLog>> result = pmsOperateLogDao.listPage(new HashMap<String, Object>() {{
            put("id", StringUtils.isBlank(id) ? null : id);
        }}, PageParam.newInstance(currentPage, PAGE_SIZE));

        while (result != null && !CollectionUtils.isEmpty(result.getData())) {
            List<PmsOperateLog> operateLogList = result.getData();
            for (PmsOperateLog pmsOperateLog : operateLogList) {
                try {
                    pmsOperateLog.setAddress(buildAddress(pmsOperateLog));
                    LOGGER.info("更新ip : {}, address : {}", pmsOperateLog.getIp(), pmsOperateLog.getAddress());

                } catch (Exception e) {
                    LOGGER.error("设置地址异常 : ",  e);
                }
            }
            try {
                pmsOperateLogDao.update(operateLogList);
            } catch (Exception e) {
                LOGGER.error("地址信息更新失败 : ",  e);
            }
            currentPage++;
            result = pmsOperateLogDao.listPage(new HashMap<String, Object>() {{
                put("id", StringUtils.isBlank(id) ? null : id);
            }}, PageParam.newInstance(currentPage, PAGE_SIZE));
        }
    }

    @Value("${ip.url}")
    private String ipUrl;
    @Value("${ip.key}")
    private String key;
    private static int SUCCESS_CODE = 0;
    private static final String INTERNAL_IP = "内网ip";

    public String buildAddress(PmsOperateLog pmsOperateLog) {
        if (StringUtils.isBlank(pmsOperateLog.getIp())) {
            LOGGER.warn("ip 地址为空 : {}", JSONObject.toJSON(pmsOperateLog));
            return null;
        }
        if ("0:0:0:0:0:0:0:1".equals(pmsOperateLog.getIp())) {
            return INTERNAL_IP;
        }
        String ip = pmsOperateLog.getIp().substring(0, pmsOperateLog.getIp().lastIndexOf(".") +  1);
        Ips ips = ipsDao.getOne(new HashMap<String, Object>() {{
            put("ip", StringUtils.isBlank(ip) ? pmsOperateLog.getIp() : ip);
        }});
        if (ips != null) {
            return handleWithRecord(ips, pmsOperateLog, ip);
        }
        StringBuilder builder = new StringBuilder(ipUrl);
        builder.append("?ip=").append(pmsOperateLog.getIp()).append("&key=").append(key);
        JSONObject jsonObject = null;
        try {
            LOGGER.info("ip  请求地址 : {}", builder.toString());
            jsonObject = IPUtil.getIpInfo(builder.toString());
        } catch (Exception e) {
            LOGGER.warn("ip信息请求失败 : {}", JSONObject.toJSON(pmsOperateLog), e);
            return null;
        }
        if (SUCCESS_CODE != jsonObject.getInteger("status") || !jsonObject.containsKey("result")) {
            LOGGER.warn("ip可能为内网ip : {}", jsonObject.toJSONString());
            saveInternalIp(ip);
            return INTERNAL_IP;
        }
        StringBuilder address = new StringBuilder();
        JSONObject result = jsonObject.getJSONObject("result");
        if (result.containsKey("ad_info")) {
            JSONObject addressInfo = result.getJSONObject("ad_info");
            if (addressInfo.containsKey("nation")) {
                address.append(addressInfo.getString("nation"));
            }
            if (addressInfo.containsKey("province")) {
                address.append(",").append(addressInfo.getString("province"));
            }
            if (addressInfo.containsKey("city")) {
                address.append(",").append(addressInfo.getString("city"));
            }
            if (addressInfo.containsKey("district")) {
                address.append(",").append(addressInfo.getString("district"));
            }
        }

        // 异步保存信息
        CompletableFuture.runAsync(() -> {
            saveIp(result, ip);
        });
        return address.toString();
    }


    private void saveInternalIp(String ip) {
        Ips newIps = new Ips();
        newIps.setIp(ip);
        newIps.setCountry(INTERNAL_IP);
        newIps.setCountryId("-1");
        newIps.setProvince(INTERNAL_IP);
        newIps.setCity(INTERNAL_IP);
        newIps.setCounty(INTERNAL_IP);
        ipsDao.insert(newIps);
    }

    private String handleWithRecord(Ips ips, PmsOperateLog pmsOperateLog, String ip) {
        StringBuilder address = new StringBuilder();
        if ("127.0.0.".equals(ip) || ips.getCountry().contains(INTERNAL_IP)) {
            pmsOperateLog.setAddress(ips.getCountry());
            return INTERNAL_IP;
        }
        if (StringUtils.isNotBlank(ips.getCountry())) {
            address.append(ips.getCountry());
        }
        if (StringUtils.isNotBlank(ips.getProvince())) {
            address.append(",").append(ips.getProvince());
        }
        if (StringUtils.isNotBlank(ips.getCity())) {
            address.append(",").append(ips.getCity());
        }
        if (StringUtils.isNotBlank(ips.getCounty())) {
            address.append(",").append(ips.getCounty());
        }
        return address.toString();
    }

    private void saveIp(JSONObject result, String ip) {
        Ips ips = new Ips();
        ips.setIp(ip);
        if (result.containsKey("ad_info")) {
            JSONObject addressInfo = result.getJSONObject("ad_info");
            String aDcode = addressInfo.getString("adcode");
            boolean flag = true;
            if (addressInfo.containsKey("district")) {
                ips.setCounty(addressInfo.getString("district"));
                assert aDcode != null;
                ips.setCountyId(Integer.valueOf(aDcode));
                flag = false;
            }
            if (addressInfo.containsKey("city")) {
                ips.setCity(addressInfo.getString("city"));
                ips.setCityId(flag ? Integer.parseInt(aDcode) : 0);
                flag = false;
            }
            if (addressInfo.containsKey("province")) {
                ips.setProvince(addressInfo.getString("province"));
                ips.setProvinceId(flag ? Integer.parseInt(aDcode) : 0);
                flag = false;
            }
            if (addressInfo.containsKey("nation")) {
                ips.setCountry(addressInfo.getString("nation"));
                ips.setCountryId(flag ? aDcode : "CN");
            }
            ips.setCreatedAt(new Date());
            ips.setUpdatedAt(new Date());
            ipsDao.insert(ips);
        }
    }
}