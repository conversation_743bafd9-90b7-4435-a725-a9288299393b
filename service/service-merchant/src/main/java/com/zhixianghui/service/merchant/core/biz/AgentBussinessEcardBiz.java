package com.zhixianghui.service.merchant.core.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.util.utils.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.merchant.entity.AgentBussinessEcard;
import com.zhixianghui.service.merchant.core.dao.mapper.AgentBussinessEcardMapper;

import java.util.Date;

@Service
public class AgentBussinessEcardBiz extends ServiceImpl<AgentBussinessEcardMapper, AgentBussinessEcard> {

    public AgentBussinessEcard  getAgentBussinessEcardByOpenId(String openId){

        AgentBussinessEcard agentBussinessEcard = this.getOne(new QueryWrapper<AgentBussinessEcard>().eq(AgentBussinessEcard.COL_OPEN_ID, openId));

        return agentBussinessEcard;
    }

    public AgentBussinessEcard  modifyAgentEcard(AgentBussinessEcard bussinessEcard){

        AgentBussinessEcard ecard = this.getAgentBussinessEcardByOpenId(bussinessEcard.getOpenId());
        if (ecard == null) {
            bussinessEcard.setCreateTime(new Date());
            bussinessEcard.setUpdateTime(new Date());
            this.save(bussinessEcard);
        }else {
            Long id = ecard.getId();
            Date createTime = ecard.getCreateTime();
            BeanUtil.copyProperties(bussinessEcard, ecard);
            ecard.setUpdateTime(new Date());
            ecard.setId(id);
            ecard.setCreateTime(createTime);
            this.updateById(ecard);
        }
        AgentBussinessEcard newCardData = this.getAgentBussinessEcardByOpenId(bussinessEcard.getOpenId());
        return newCardData;
    }

}
