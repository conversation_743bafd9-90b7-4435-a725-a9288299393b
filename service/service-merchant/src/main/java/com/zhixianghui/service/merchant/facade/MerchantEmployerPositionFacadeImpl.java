package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerPositionBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerQuoteBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
public class MerchantEmployerPositionFacadeImpl implements MerchantEmployerPositionFacade {
    @Autowired
    private MerchantEmployerPositionBiz positionBiz;

    @Override
    public List<MerchantEmployerPosition> listByMchNo(String mchNo) {
        return positionBiz.listByMchNo(mchNo);
    }

    @Override
    public MerchantEmployerPosition getByMchNoAndWorkCategoryCode(String mchNo, String workCategoryCode) {
        return positionBiz.getByMchNoAndWorkCategoryCode(mchNo, workCategoryCode);
    }

    @Override
    public List<MerchantEmployerPosition> listByMchNoWithQuote(Map<String,Object> paramMap) {
        return positionBiz.listByMchNoWithQuote(paramMap);
    }

    @Override
    public List<MerchantEmployerPosition> listByMchNoWithQuoteWithoutGroup(Map<String,Object> paramMap) {
        return positionBiz.listByMchNoWithQuoteWithoutGroup(paramMap);
    }
}
