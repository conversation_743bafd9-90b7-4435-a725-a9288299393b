package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.facade.merchant.service.AgreementFileFacade;
import com.zhixianghui.service.merchant.core.biz.AgreementFileBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 协议文件表 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementFileImpl implements AgreementFileFacade {

    private final AgreementFileBiz biz;

    @Override
    public List<AgreementFile> listByAgreementId(Long AgreementId) {
        return biz.listByAgreementId(AgreementId);
    }

    @Override
    public List<AgreementFile> listBy(Map<String, Object> paramMap) {
        return biz.listBy(paramMap);
    }

    @Override
    public void update(AgreementFile agreementFile) {
        biz.update(agreementFile);
    }

    @Override
    public AgreementFile getByAgreementIdAndType(Long id, int type) {
        return biz.getByAgreementIdAndType(id,type);
    }
}
