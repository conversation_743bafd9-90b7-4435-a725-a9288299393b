package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.AgentEmployerMain;
import org.springframework.stereotype.Repository;

import java.util.Collections;

/**
 * 合伙人-企业主体信息表 Mapper 接口
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Repository
public class AgentEmployerMainDao extends MyBatisDao<AgentEmployerMain, Long> {

    public AgentEmployerMain getByAgentNo(String agentNo) {
        LimitUtil.notEmpty(agentNo, "合伙人编号不能为空");
        return getOne(Collections.singletonMap("agentNo", agentNo));
    }
}
