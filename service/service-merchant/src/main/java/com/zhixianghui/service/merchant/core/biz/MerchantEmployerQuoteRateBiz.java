package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.common.statics.enums.fee.FormulaEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerQuoteRateVo;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuoteDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuoteRateDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2021-07-20
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantEmployerQuoteRateBiz {

    private final MerchantEmployerQuoteDao merchantEmployerQuoteDao;
    private final MerchantEmployerQuoteRateDao merchantemployerquoterateDao;

    public List<MerchantEmployerQuoteRateVo> listMerchantQuoteRate(String mchNo) {
        if (StringUtil.isEmpty(mchNo)) {
            return new ArrayList<>();
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);
        List<MerchantEmployerQuote> quoteList = merchantEmployerQuoteDao.listBy(paramMap);
        if (quoteList == null || quoteList.isEmpty()) {
            return new ArrayList<>();
        }

        Map<Long,MerchantEmployerQuote> quoteMap = new HashMap<>();
        for (MerchantEmployerQuote quote : quoteList) {
            quoteMap.put(quote.getId(), quote);
        }

        List<Long> quoteIdList = quoteList.stream().map(MerchantEmployerQuote::getId).collect(Collectors.toList());
        paramMap.clear();
        paramMap.put("quoteIdList", quoteIdList);
        List<MerchantEmployerQuoteRate> quoteRateList = merchantemployerquoterateDao.listBy(paramMap);
        if (quoteRateList == null || quoteRateList.isEmpty()) {
            return new ArrayList<>();
        }

//        Map<Long,List<MerchantEmployerQuoteRate>> quoteRateMap = this.groupByQuoteIds(quoteIdList);

        List<MerchantEmployerQuoteRateVo> quoteRateVoList = new ArrayList<>();
        for (MerchantEmployerQuoteRate quoteRate : quoteRateList) {
            MerchantEmployerQuote quote = quoteMap.getOrDefault(quoteRate.getQuoteId(), new MerchantEmployerQuote());

            MerchantEmployerQuoteRateVo quoteRateVo = new MerchantEmployerQuoteRateVo();
            quoteRateVo.setProductNo(quote.getProductNo());
            quoteRateVo.setProductName(quote.getProductName());
            quoteRateVo.setEmployerNo(quote.getMchNo());
            quoteRateVo.setEmployerName(quote.getMchName());
            quoteRateVo.setMainstayMchNo(quote.getMainstayMchNo());
            quoteRateVo.setMainstayMchName(quote.getMainstayMchName());
//            quoteRateVo.setPositionList();
//            quoteRateVo.setInvoiceList();
            quoteRateVo.setFormulaType(quoteRate.getFormulaType());
            quoteRateVo.setFixedFee(quoteRate.getFixedFee());
            quoteRateVo.setRate(quoteRate.getRate());
            quoteRateVo.setRuleParam(quote.getRuleParam());
            quoteRateVo.setStatus(quote.getStatus());
            quoteRateVoList.add(quoteRateVo);
        }

        return quoteRateVoList;
    }

    public Map<Long, List<MerchantEmployerQuoteRate>> groupByQuoteIds(List<Long> quoteIdList) {
        Map<Long, List<MerchantEmployerQuoteRate>> resultMap = new HashMap<>();
        if (quoteIdList == null || quoteIdList.isEmpty()) {
            return resultMap;
        }

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("quoteIdList", quoteIdList);
        List<MerchantEmployerQuoteRate> quoteRateList = merchantemployerquoterateDao.listBy(paramMap);
        if (quoteRateList == null || quoteRateList.isEmpty()) {
            return resultMap;
        }
        for (MerchantEmployerQuoteRate quoteRate : quoteRateList) {
            Long quoteId = quoteRate.getQuoteId();
            List<MerchantEmployerQuoteRate> quoteRateGroups = resultMap.computeIfAbsent(quoteId, k -> new ArrayList<>());
            quoteRateGroups.add(quoteRate);
        }
        return resultMap;
    }
}