package com.zhixianghui.service.merchant.core.biz.user.portal;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalOperatorStatusEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.portal.*;
import com.zhixianghui.facade.merchant.vo.EmployerCooperateUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.service.merchant.core.dao.*;
import com.zhixianghui.service.merchant.core.dao.user.employer.*;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class EmployerStaffBiz{
	
	@Autowired
	private EmployerStaffDao employerStaffDao;

	@Autowired
	private EmployerOperatorDao employerOperatorDao;

	@Autowired
	private EmployerRoleDao employerRoleDao;

	@Autowired
	private EmployerFunctionDao employerFunctionDao;

	@Autowired
	private EmployerStaffRoleDao employerStaffRoleDao;

	@Autowired
	private MerchantTradePwdDao merchantTradePwdDao;

	@Autowired
	private MerchantDao merchantDao;

	@Autowired
	private RedisClient redisClient;

	/**
	 * 根据id查询用工企业员工
	 *
	 * @param mchNo 	商户编号
	 * @param id    	员工id
	 */
	public EmployerStaffVO getById(String mchNo, long id) {
		if (StringUtil.isEmpty(mchNo)) return null;
		return employerStaffDao.getVOByMchNoAndId(mchNo, id);
	}

	/**
	 * 根据手机号查询用工企业员工
	 * @param mchNo		商户编号
	 * @param phone		手机号
	 */
	public EmployerStaffVO getByPhone(String mchNo, String phone) {
		if (StringUtil.isEmpty(mchNo)) return null;
		return employerStaffDao.getVOByMchAndPhone(mchNo, phone);
	}

	/**
	 * 根据操作员id查询其关联的员工
	 *
	 * @param id
	 * @return
	 */
	public List<EmployerStaffVO> listByOperatorId(long id) {
		return employerStaffDao.listVOByOperatorId(id);
	}

	/**
	 * 根据手机号查询其关联的员工
	 *
	 * @param phone
	 * @return
	 */
	public List<EmployerStaffVO> listByPhone(String phone) {
		return employerStaffDao.listVOByPhone(phone);
	}

	/**
	 * 获取超级管理员
	 *
	 * @param mchNo 商户编号
	 * @return
	 */
	public EmployerStaffVO getAdmin(String mchNo) throws BizException {
		if (StringUtil.isEmpty(mchNo)) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户编号不能为空");
		}

		List<EmployerStaffVO> staffs = employerStaffDao.getVOByMchNoAndType(mchNo, PortalStaffTypeEnum.ADMIN.getValue());
		if (staffs == null || staffs.isEmpty()) {
			return null;
		} else if(staffs.size() > 1) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("存在多个超级管理员");
		} else {
			return staffs.get(0);
		}
	}

	/**
	 * 创建用工企业员工
	 *
	 * @param employerStaffVo vo
	 */
	@Transactional
	public long create(EmployerStaffVO employerStaffVo) throws BizException {
		EmployerStaffVO staffVO = employerStaffDao.getVOByMchAndPhone(employerStaffVo.getMchNo(), employerStaffVo.getPhone());
		if (staffVO != null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工已存在");
		}
		if (employerStaffVo.getType() == PortalStaffTypeEnum.ADMIN.getValue() && getAdmin(employerStaffVo.getMchNo()) != null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("已存在超级管理员");
		}

		EmployerOperator operator = employerOperatorDao.getByPhone(employerStaffVo.getPhone());
		// 如果不存在操作员帐号，则先创建操作员
		if (operator == null) {
			operator = createOperator(employerStaffVo);
		}

		MerchantTradePwd tradePwd = merchantTradePwdDao.getByMchNo(employerStaffVo.getMchNo());
		// 如果不存在商户支付，则先创建
		if (tradePwd == null) {
			createMchTradePwd(employerStaffVo);
		}

		// 创建员工
		EmployerStaff staff = createStaff(operator.getId(), employerStaffVo);

		//写入缓存
		redisClient.hset(PlatformSource.MERCHANT.getValue() + ":" + operator.getId(),operator.getCacheMap());
		redisClient.hset(PlatformSource.MERCHANT.getValue() + ":" + staff.getId() + ":" + staff.getMchNo(),staff.getCacheMap());

		return staff.getId();
	}

	/**
	 * 创建用工企业员工并分配指定角色
	 *
	 * @param employerStaffVo	vo
	 * @param roleIds			角色id
	 */
	@Transactional
	public long createAndAssignRole(EmployerStaffVO employerStaffVo, List<Long> roleIds) throws BizException {
		long staffId = create(employerStaffVo);

		// 为员工分配角色
		assignRole(employerStaffVo.getMchNo(), staffId, roleIds);
		return staffId;
	}

	/**
	 * 更换超级管理员
	 *
	 * @param mchNo         商户编号
	 * @param newAdminPhone 新负责人手机号
	 * @param newAdminName      新负责人姓名
	 */
	@Transactional
	public void changeAdmin(String mchNo, String newAdminPhone, String newAdminName, String updator) throws BizException {
		EmployerStaffVO oldAdmin = getAdmin(mchNo);
		if (oldAdmin == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("超级管理员不存在");
		}

		// 原管理员绑定关系直接删除
		employerStaffDao.deleteByMchNoAndId(mchNo, oldAdmin.getId());
		redisClient.del(PlatformSource.MERCHANT.getValue() + ":" + oldAdmin.getId() + ":" + mchNo);

		EmployerStaffVO newAdmin = getByPhone(mchNo, newAdminPhone);
		if (newAdmin == null) {  // 新负责人帐号不存在则直接创建
			EmployerStaffVO employerStaffVO = new EmployerStaffVO();
			employerStaffVO.setCreateTime(new Date());
			employerStaffVO.setCreator(updator);
			employerStaffVO.setMchNo(mchNo);
			employerStaffVO.setMchName(oldAdmin.getMchName());
			employerStaffVO.setPhone(newAdminPhone);
			employerStaffVO.setName(newAdminName);
			employerStaffVO.setType(PortalStaffTypeEnum.ADMIN.getValue());
			create(employerStaffVO);
		} else {  // 新负责人修改为超级管理员
			employerStaffDao.updateTypeById(newAdmin.getId(), PortalStaffTypeEnum.ADMIN.getValue(), updator);
		}
	}

	/**
	 * 为用工企业员工更新角色
	 *
	 * @param mchNo   商户编号
	 * @param staffId 员工id
	 * @param roleIds 角色id
	 */
	@Transactional
	public void updateRole(String mchNo, long staffId, List<Long> roleIds,String name) throws BizException {
		if (getById(mchNo, staffId) == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
		}

		// 删除员工原有的角色
		employerStaffRoleDao.deleteByStaffId(staffId);

		//更新员工姓名
		EmployerStaff employerStaff = employerStaffDao.getById(staffId);
		employerStaff.setName(name);
		employerStaffDao.update(employerStaff);

		// 分配角色
		assignRole(mchNo, staffId, roleIds);

		redisClient.hset(PlatformSource.MERCHANT.getValue() + ":" + employerStaff.getId() + ":" + employerStaff.getMchNo(),employerStaff.getCacheMap());
	}

	/**
	 * 根据id删除用工企业员工
	 *
	 * @param mchNo 商户编号
	 * @param id    员工id
	 */
	@Transactional(rollbackFor = Exception.class)
	public void deleteById(String mchNo, long id) {
		EmployerStaffVO staff = getById(mchNo, id);
		if (staff == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
		} else if (staff.getType() == PortalStaffTypeEnum.ADMIN.getValue()) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("超级管理员不能删除");
		}
		employerStaffDao.deleteByMchNoAndId(mchNo, id);
		employerStaffRoleDao.deleteByStaffId(staff.getId());

		redisClient.del(PlatformSource.MERCHANT.getValue() + ":" + id + ":" + mchNo);
	}

	/**
	 * 根据员工id获取其关联的角色
	 *
	 * @param mchNo   商户编号
	 * @param staffId 员工id
	 */
	public List<EmployerRole> getRoleByStaffId(String mchNo, long staffId) throws BizException {
		if (getById(mchNo, staffId) == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
		}
		return employerRoleDao.listByStaffId(staffId);
	}

	/**
	 * 查询员工所关联的功能
	 *
	 * @param mchNo   商户编号
	 * @param staffId 员工id
	 */
	public List<EmployerFunction> listFunctionByStaffId(String mchNo, long staffId) throws BizException {
		EmployerStaffVO staffVO = getById(mchNo, staffId);
		if (staffVO == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
		} else if (staffVO.getType() == PortalStaffTypeEnum.ADMIN.getValue()) {
			return employerFunctionDao.listAll("number asc");
		} else {
			return employerFunctionDao.listByStaffId(staffId);
		}
	}

	/**
	 * 分页查询员工
	 *
	 * @param paramMap
	 * @param pageParam
	 */
	public PageResult<List<EmployerStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
		return employerStaffDao.listStaffVOPage(paramMap, pageParam);
	}

	/**
	 * 分页查询员工
	 * @param mchNo
	 * @param paramMap
	 * @param pageParam
	 * @return
	 */
	public PageResult<List<EmployerStaffVO>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam) {
		if (paramMap == null) {
			paramMap = new HashMap<>();
		}
		paramMap.put("mchNo", mchNo);
		return employerStaffDao.listStaffVOPage(paramMap, pageParam);
	}

	/**
	 * 创建操作员
	 */
	private EmployerOperator createOperator(EmployerStaffVO employerStaffVO) {
		EmployerOperator operator = new EmployerOperator();
		operator.setCreateTime(new Date());
		operator.setPhone(employerStaffVO.getPhone());
		operator.setName(StringUtil.isEmpty(employerStaffVO.getName()) ? employerStaffVO.getPhone() : employerStaffVO.getName());  // 没传姓名则用手机号替代
		operator.setStatus(PortalOperatorStatusEnum.ACTIVE.getValue());
		operator.setIsInitPwd(PortalInitPwdStatusEnum.NO_INIT.getValue());
		operator.setSalt(null);
		operator.setPwd(null);
		operator.setModPwdTime(null);
		operator.setCurLoginTime(null);
		operator.setLastLoginTime(null);
		operator.setPwdErrorCount(0);
		operator.setPwdErrorTime(null);
		employerOperatorDao.insert(operator);
		return operator;
	}

	/**
	 * 创建员工
	 */
	private EmployerStaff createStaff(Long operatorId, EmployerStaffVO employerStaffVO) {
		EmployerStaff staff = new EmployerStaff();
		staff.setCreateTime(new Date());
		staff.setOperatorId(operatorId);
		staff.setMchNo(employerStaffVO.getMchNo());
		staff.setMchName(employerStaffVO.getMchName());
		staff.setType(employerStaffVO.getType());
		staff.setCreator(employerStaffVO.getCreator());
		staff.setName(employerStaffVO.getName());
		employerStaffDao.insert(staff);
		return staff;
	}

	/**
	 * 创建商户支付密码
	 */
	private MerchantTradePwd createMchTradePwd(EmployerStaffVO employerStaffVO) {
		MerchantTradePwd tradePwd = new MerchantTradePwd();
		tradePwd.setCreateTime(new Date());
		tradePwd.setMchNo(employerStaffVO.getMchNo());
		tradePwd.setStatus(PortalOperatorStatusEnum.ACTIVE.getValue());
		tradePwd.setIsInitPwd(PortalInitPwdStatusEnum.NO_INIT.getValue());
		tradePwd.setPwd(null);
		tradePwd.setPwdErrorCount(0);
		tradePwd.setPwdErrorTime(null);
		merchantTradePwdDao.insert(tradePwd);
		return tradePwd;
	}

	/**
	 * 为员工分配角色
	 */
	private void assignRole(String mchNo, long staffId, List<Long> roleIds) {
		if (roleIds != null && !roleIds.isEmpty()) {
			// 校验角色是否都属于该商户下
			Set<Long> roleIdSet = employerRoleDao.listByMchNo(mchNo)
					.stream().map(EmployerRole::getId).collect(Collectors.toSet());

			if (roleIds.stream().anyMatch(roleId -> !roleIdSet.contains(roleId))) {
				throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色id错误");
			}

			// 分配角色
			List<EmployerStaffRole> staffRoles = roleIds.stream().map(roleId -> {
				EmployerStaffRole staffRole = new EmployerStaffRole();
				staffRole.setCreateTime(new Date());
				staffRole.setStaffId(staffId);
				staffRole.setRoleId(roleId);
				return staffRole;
			}).collect(Collectors.toList());
			employerStaffRoleDao.insert(staffRoles);
		}
	}

    public void updateByMchNo(EmployerCooperateUpdateVo vo) {
		Map<String,Object> paramMap = Maps.newHashMap();
		paramMap.put("mchNo",vo.getMchNo());
		//更新员工信息，注意版本
		List<EmployerStaff> employerStaffList = employerStaffDao.listBy(paramMap);
		employerStaffList.stream().
				forEach(employerStaff -> {
					employerStaff.setMchName(vo.getMchName());
					employerStaff.setUpdateTime(new Date());
					employerStaff.setUpdator(vo.getCurrentOperator());
				});
		employerStaffDao.update(employerStaffList);
    }

	public Long countEmployerCount(Long roleId) {
		return employerStaffRoleDao.countBy("countEmployerByRoleId",
				new HashMap<String, Long>(){{ put("roleId", roleId);
				}});
	}

	public boolean isAdmin(EmployerStaffVO employerStaffVO) {
		List<EmployerStaffRole> list = employerStaffRoleDao.listBy(new HashMap<String, Object>() {{
			put("staffId", employerStaffVO.getId());
		}});
		if (CollectionUtils.isEmpty(list)) {
			return false;
		}

		for (EmployerStaffRole item : list) {
			EmployerRole employerRole = employerRoleDao.getById(item.getRoleId());
			if (employerRole.getRoleType() == RoleTypeEnum.ADMIN.getType()) {
				return true;
			}
		}

		return false;

	}

	public List<EmployerFunction> listAllFunction() {
		return employerFunctionDao.listAll();
	}

    public List<EmployerStaff> getAll() {
		return employerStaffDao.listAll();
    }

	public void getAndPutStaffCache(Long id) {
		EmployerStaff employerStaff = employerStaffDao.getById(id);
		if (employerStaff != null){
			redisClient.hset(PlatformSource.MERCHANT.getValue() + ":" + employerStaff.getId() + ":" + employerStaff.getMchNo(),employerStaff.getCacheMap());
		}
	}

	public void changeAdminAfterFlow(String mchNo, String contactPhone, String contactName, String updator) {
		Merchant merchant = merchantDao.getByMchNo(mchNo);
		if (merchant == null){
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商不存在");
		}
		merchant.setContactPhone(contactPhone);
		merchant.setContactName(contactName);
		merchantDao.update(merchant);
		changeAdmin(mchNo, contactPhone, contactName, updator);
	}

    public List<String> getDistinctStaffByRoleIdAndMchNo(String mchNo, List<Long> roleIds) {
		Map<String,Object> paramMap = Maps.newHashMap();
		paramMap.put("mchNo",mchNo);
		paramMap.put("list",roleIds);
		return employerStaffDao.getDistinctStaffByRoleIdAndMchNo(paramMap);
    }
}
