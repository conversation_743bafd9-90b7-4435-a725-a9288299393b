package com.zhixianghui.service.merchant.facade.record;

import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.vo.record.MerchantProductFeeVo;
import com.zhixianghui.service.merchant.core.biz.MerchantInfoChangeRecordBiz;
import com.zhixianghui.service.merchant.core.vo.MerchantInfoChangeRecordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/8/3 15:58
 */
@Service("MerchantProductRuleChange")
public class MerchantProductRuleChange extends AbstractInfoChange<MerchantProductFeeVo> {
    private static final String FIELD_NAME = "商户产品报价单";
    private static final String FIELD_KEY = "productQuote";

    @Autowired
    private MerchantInfoChangeRecordBiz changeRecordBiz;

    public void handle(MerchantProductFeeVo newInfo, MerchantProductFeeVo oldInfo, int source) {
        MerchantInfoChangeRecord record = new MerchantInfoChangeRecord();
        record.setCreateTime(new Date());
        record.setBusinessId(newInfo.getFlowId());
        record.setMchNo(newInfo.getMchNo());
        record.setObjectName(FIELD_NAME);
        record.setObjectKey(FIELD_KEY);
        record.setSource(source);
        record.setOperate(newInfo.getOperator());
        record.setOperateId(newInfo.getLoginId());
        record.setOperateName(newInfo.getLoginName());
        record.setVersion(0);
        changeRecordBiz.insert(record);
    }

    @Override
    protected void compareAndRecord(Map<String, Object> oldMap, Map<String, Object> newMap, MerchantProductFeeVo newInfo, int source) {

    }

    @Override
    protected void specialHandling(String name, Map<String, Object> infoMap) {

    }

    @Override
    protected void handleException() {

    }

    @Override
    protected void getDictionary() {

    }


}
