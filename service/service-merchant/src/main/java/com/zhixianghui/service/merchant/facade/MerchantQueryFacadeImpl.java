package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.MerchantInfoVo;
import com.zhixianghui.facade.merchant.vo.MerchantSupplierInfoVo;
import com.zhixianghui.facade.merchant.vo.merchant.BranchVo;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


/**
 * 商户信息查询
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantQueryFacadeImpl implements MerchantQueryFacade {
    @Autowired
    private MerchantBiz merchantBiz;

    @Override
    public Merchant getByMchNo(String mchNo) {
        return merchantBiz.getByMchNo(mchNo);
    }

    @Override
    public PageResult<List<Object>> listExtObjectPage(Map<String, Object> paramMap, PageParam pageParam) {
        return merchantBiz.listExtObjectPage(paramMap, pageParam);
    }

    @Override
    public List<Merchant> listBy(Map<String, Object> paramMap) {
        return merchantBiz.listBy(paramMap);
    }

    @Override
    public PageResult<List<Merchant>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return merchantBiz.listPage(paramMap,pageParam);
    }

    @Override
    public PageResult<List<MerchantSupplierInfoVo>> listExtSupplierMerchantPage(Map<String, Object> paramMap, PageParam pageParam) {
        return merchantBiz.listExtSupplierMerchantPage(paramMap,pageParam);
    }

    @Override
    public Map<String, Object> getBaseInfo(String mchNo) {
        return merchantBiz.getBaseInfo(mchNo);
    }

    @Override
    public MerchantInfoVo convert(String mchNo) {
        return merchantBiz.convert(mchNo);
    }

    @Override
    public List<MerchantInfoVo> listAll(Map<String, Object> paramMap) {
        return merchantBiz.listAll(paramMap);
    }

    @Override
    public List<BranchVo> listByBranch(Map<String, Object> paramMap) {
        return merchantBiz.listByBranch(paramMap);
    }

    @Override
    public List<Map<String,Object>> listByBranchEqual(Map<String, Object> paramMap) {
        return merchantBiz.listByBranchEqual(paramMap);
    }
}
