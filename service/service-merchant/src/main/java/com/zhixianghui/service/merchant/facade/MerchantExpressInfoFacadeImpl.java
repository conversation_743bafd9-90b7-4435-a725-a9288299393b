package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.service.MerchantExpressInfoFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantExpressInfoBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 商户开票信息
 *
 * <AUTHOR>
 * @date 2020/12/25
 **/
@Service
public class MerchantExpressInfoFacadeImpl implements MerchantExpressInfoFacade {
    @Autowired
    private MerchantExpressInfoBiz expressInfoBiz;

    @Override
    public MerchantExpressInfo getByMchNo(String mchNo) {
        return expressInfoBiz.getByMchNo(mchNo);
    }

    @Override
    public void update(MerchantExpressInfo invoiceInfo) {
        expressInfoBiz.update(invoiceInfo);
    }

    @Override
    public void insert(MerchantExpressInfo info) {
        expressInfoBiz.insert(info);
    }
}
