package com.zhixianghui.service.merchant.core.adapt;

import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName AbstractQuoteHandle
 * @Description TODO
 * @Date 2022/6/24 17:05
 */
public interface QuoteHandlerInterface {

    void createAfterFlow(MerchantFlowVo merchantFlowVo);

    void delete(Long quoteId);

    void create(MerchantFlowVo merchantFlowVo);

    void cancel(MerchantEmployerQuote merchantEmployerQuote);

    void editQuote(MerchantFlowVo merchantFlowVo);
}
