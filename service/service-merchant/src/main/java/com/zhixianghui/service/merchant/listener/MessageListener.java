package com.zhixianghui.service.merchant.listener;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.service.MerchantChannelFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffFacade;
import com.zhixianghui.facade.merchant.vo.EmployerFullInfoUpdateVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerAddVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerInsertVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerMainAuthVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentVo;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.service.merchant.core.adapt.AdaptHelper;
import com.zhixianghui.service.merchant.core.adapt.QuoteHandlerInterface;
import com.zhixianghui.service.merchant.core.biz.*;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperateLogBiz;
import com.zhixianghui.service.merchant.core.biz.user.portal.EmployerStaffBiz;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierStaffBiz;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuoteDao;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class MessageListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageListener.class);
    @Autowired
    private MerchantEmployerBiz employerBiz;
    @Autowired
    private AgreementBiz agreementBiz;
    @Autowired
    private AgentManagerBiz agentManagerBiz;
    @Reference
    private MerchantChannelFacade merchantChannelFacade;
    @Autowired
    private MerchantEmployerCooperateBiz merchantEmployerCooperateBiz;
    @Autowired
    private PmsOperateLogBiz pmsOperateLogBiz;
    @Reference
    private EmployerStaffFacade employerStaffFacade;
    @Reference
    private SupplierStaffFacade supplierStaffFacade;
    @Autowired
    private EmployerStaffBiz employerStaffBiz;
    @Autowired
    private SupplierStaffBiz supplierStaffBiz;
    @Autowired
    private MerchantBiz merchantBiz;
    @Autowired
    private YishuiBiz yishuiBiz;
    @Autowired
    private AdaptHelper adaptHelper;
    @Autowired
    private MerchantEmployerQuoteDao quoteDao;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MERCHANT_FULL_EDIT, selectorExpression = MessageMsgDest.TAG_MERCHANT_FULL_EDIT, consumeThreadMax = 3, consumerGroup = "merchantFullEditGroup")
    public class MerchantFullEditListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("EmployerFullInfoUpdateVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            merchantEmployerCooperateBiz.updateAllMerchantMessage(JsonUtil.toBean(msg, EmployerFullInfoUpdateVo.class));
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC, selectorExpression = MessageMsgDest.TAG_APPROVAL_CREATE_MERCHANT_ASYNC, consumeThreadMax = 3, consumerGroup = "createEmployerConsume")
    public class CreateEmployerMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("MerchantEmployerInsertVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            MerchantEmployerAddVo insertVo = JsonUtil.toBean(msg, MerchantEmployerAddVo.class);
            employerBiz.applyMerchant(insertVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPLY_NEW_MERCHANT, selectorExpression = MessageMsgDest.TAG_APPLY_NEW_MERCHANT, consumeThreadMax = 3, consumerGroup = "applyNewMerchentConsume")
    public class ApplyNewMerchantMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("MerchantEmployerInsertVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            MerchantEmployerAddVo insertVo = JsonUtil.toBean(msg, MerchantEmployerAddVo.class);
            employerBiz.applyMerchant(insertVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC, selectorExpression = MessageMsgDest.TAG_APPROVAL_CREATE_AGENT_ASYNC, consumeThreadMax = 3, consumerGroup = "createAgentConsume")
    public class CreateAgentMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("AgentInsertVo 不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            AgentVo vo = JsonUtil.toBean(msg, AgentVo.class);
            agentManagerBiz.activeAgent(vo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC, selectorExpression = MessageMsgDest.TAG_APPROVAL_MODIFY_AGENT_ASYNC, consumeThreadMax = 3, consumerGroup = "modifyAgentConsume")
    public class ModifyAgentMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("AgentInsertVo 不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            AgentVo vo = JsonUtil.toBean(msg, AgentVo.class);
            agentManagerBiz.updateAgentDetail(vo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC, selectorExpression = MessageMsgDest.TAG_APPROVAL_MERCHANT_VERIFY_ASYNC, consumeThreadMax = 3, consumerGroup = "mainAuthConsume")
    public class MainAuthMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("MerchantEmployerMainAuthVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            MerchantEmployerMainAuthVo vo = JsonUtil.toBean(msg, MerchantEmployerMainAuthVo.class);
            employerBiz.employerMainAuth(vo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC, selectorExpression = MessageMsgDest.TAG_APPROVAL_CANCEL_ASYNC, consumeThreadMax = 1, consumerGroup = "mainAuthCancelConsume")
    public class MainAuthCancelMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("MerchantEmployerMainAuthVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            MerchantEmployerMainAuthVo vo = JsonUtil.toBean(msg, MerchantEmployerMainAuthVo.class);
            employerBiz.mainAuthCancel(vo.getMchNo());
        }
    }

    @Component
    @RocketMQMessageListener(
            topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,
            selectorExpression = MessageMsgDest.TAG_APPROVAL_AGENT_CANCEL_ASYNC,
            consumeThreadMax = 1,
            consumerGroup = "agentCancelConsume"
    )
    public class AgentCancelMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("取消合伙人认证的请求参数不能为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            // agentNo-updater
            String[] agentNo = msg.split("-");
            Agent agent = agentManagerBiz.getByAgentNo(agentNo[0]);
            agent.setAgentStatus(AgentStatusEnum.RETREAT.getValue());
            agent.setUpdater(agentNo[1]);
            agent.setUpdateTime(new Date());
            agentManagerBiz.changeStatus(agent);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MAIN_AUTH_CHANGE, selectorExpression = MessageMsgDest.TAG_CONTINUE_MAIN_AUTH, consumeThreadMax = 3, consumerGroup = "mainAuthContinueConsumer")
    public class MainAuthContinueMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("业务标识不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            employerBiz.mainAuthContinue(msg);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MAIN_AUTH_CHANGE, selectorExpression = MessageMsgDest.TAG_DELETE_MAIN_AUTH_FLOW, consumeThreadMax = 3, consumerGroup = "mainAuthDeleteConsumer")
    public class MainAuthDeleteMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("业务标识不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            employerBiz.mainAuthDelete(msg);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MAIN_AUTH_CHANGE, selectorExpression = MessageMsgDest.TAG_APPROVAL_MERCHANT_VERIFY_DISAGREE_ASYNC, consumeThreadMax = 3, consumerGroup = "mainAuthDisagreeConsume")
    public class MainAuthDisAgreeMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("MerchantEmployerMainAuthVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            MerchantEmployerMainAuthVo vo = JsonUtil.toBean(msg, MerchantEmployerMainAuthVo.class);
            employerBiz.mainAuthDisAgree(vo.getMchNo());
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.AGREEMENT_EXPIRE_TASK, consumeThreadMax = 1, consumerGroup = "agreementConsume")
    public class AgreementExpireListener extends TaskRocketMQListener<JSONObject> {

        @Override
        public void runTask(JSONObject msg) {
            agreementBiz.expireAgreement();
        }
    }

    @Component
    @RocketMQMessageListener(
            topic = MessageMsgDest.TOPIC_SYNCHRONIZE_PAYMENT_DATA,
            consumeThreadMax = 1,
            consumerGroup = "synchronizePaymentDataGroup"
    )
    public class SynchronizePaymentDataListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String message) {
            if (StringUtil.isEmpty(message)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("时间不能为空");
            }
        }


        @SuppressWarnings("rawtypes")
        @Override
        public void consumeMessage(String message) {
            HashMap paramMap = JSON.parseObject(message, HashMap.class);
            String beginTime = (String) paramMap.get("beginTime");
            String endTime = (String) paramMap.get("endTime");

            if (StringUtils.isBlank(endTime)) {
                endTime = DateUtil.formatDateTime(
                        DateUtil.getDayEnd(DateUtil.addDay(new Date(), -1))
                );
            }
            if (StringUtils.isBlank(beginTime)) {
                beginTime = DateUtil.formatDateTime(
                        DateUtil.getDayStart(DateUtil.addDay(new Date(), -1))
                );
            }
            LOGGER.info("同步订单记录时间 : {}-{}", beginTime, endTime);
            merchantChannelFacade.synchronizeRecordItem(beginTime, endTime);
        }
    }

    @Component
    @RocketMQMessageListener(
            topic = MessageMsgDest.TOPIC_SYNCHRONIZE_RELATION_DATA,
            consumeThreadMax = 1,
            consumerGroup = "synchronizeRelationDataGroup"
    )
    public class SynchronizeRelationDataListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String message) {
            if (StringUtil.isEmpty(message)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("时间不能为空");
            }
        }


        @SuppressWarnings("rawtypes")
        @Override
        public void consumeMessage(String message) {
            HashMap paramMap = JSON.parseObject(message, HashMap.class);
            String beginTime = (String) paramMap.get("beginTime");
            String endTime = (String) paramMap.get("endTime");

            if (StringUtils.isBlank(endTime)) {
                endTime = DateUtil.formatDateTimeMills(
                        DateUtil.getDayEnd(DateUtil.addDay(new Date(), -1))
                );
            }
            if (StringUtils.isBlank(beginTime)) {
                beginTime = DateUtil.formatDateTimeMills(
                        DateUtil.getDayStart(DateUtil.addDay(new Date(), -1))
                );
            }
            LOGGER.info("同步代征关系数据 : {}-{}", beginTime, endTime);
            merchantChannelFacade.synchronizeRelation(beginTime, endTime);
        }
    }

    @Component
    @RocketMQMessageListener(
            topic = MessageMsgDest.TOPIC_IP_INFO_HANDLE,
            consumeThreadMax = 1,
            consumerGroup = "ipInfoHandleGroup"
    )
    public class IpInfoHandle extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String id) {

        }

        @Override
        public void consumeMessage(String id) {
            LOGGER.info("处理id 地址信息 : {}", id);
            pmsOperateLogBiz.ipInfoHandle(JSONObject.parseObject(id).getString("id"));
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC, selectorExpression = MessageMsgDest.TAG_APPROVAL_CREATE_MERCHANT_NOTIFY, consumeThreadMax = 3, consumerGroup = "AddMerchantNotifyConsume")
    public class AddMerchantNotifyListener extends BaseRocketMQListener<String> {

        @Reference
        private RobotFacade robotFacade;
        @Reference
        private FlowFacade flowFacade;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam, CommonFlow.class);

            if (commonFlow.getId() == null) {
                commonFlow = flowFacade.getByRootProcessInstanceId(commonFlow.getProcessInstanceId());
            }

            final String extInfo = commonFlow.getExtInfo();
            final MerchantEmployerInsertVo merchantEmployerInsertVo = JSONObject.parseObject(extInfo, MerchantEmployerInsertVo.class);

            StringBuffer sb = new StringBuffer("#### 商户入网提醒\\n ");

            final String remark = commonFlow.getRemark() == null ? "" : commonFlow.getRemark();

            sb.append("> 流程ID：").append(commonFlow.getId())
                    .append("\\n > 流程名称：").append(commonFlow.getFlowTopicName())
                    .append("\\n > 商户名称：").append(merchantEmployerInsertVo.getMchName())
                    .append("\\n > 发起人：").append(commonFlow.getInitiatorName())
                    .append("\\n > 创建时间：").append(DateUtil.formatDateTime(commonFlow.getCreateTime()))
                    .append("\\n > 审批备注：").append(remark);

            MarkDownMsg markDownMsg = new MarkDownMsg();
            markDownMsg.setUnikey(IdUtil.fastUUID());
            markDownMsg.setRobotType(RobotTypeEnum.MERCHANT_ADD_ROBOT.getType());
            markDownMsg.setContent(sb.toString());
            robotFacade.pushMarkDownAsync(markDownMsg);
        }
    }

    /**
     * 修改负责人
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_CHANGE_PRINCIPAL,consumeThreadMax = 3,consumerGroup = "changePrincipalGroup")
    public class ChangePrincipalListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JSONObject.parseObject(jsonParam,MerchantFlowVo.class);
            if (merchantFlowVo.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
                employerStaffBiz.changeAdminAfterFlow(merchantFlowVo.getMchNo(),merchantFlowVo.getContactPhone(),merchantFlowVo.getContactName(),merchantFlowVo.getUpdator());
            }else{
                supplierStaffBiz.changeAdminAfterFlow(merchantFlowVo.getMchNo(),merchantFlowVo.getContactPhone(),merchantFlowVo.getContactName(),merchantFlowVo.getUpdator());
            }
        }
    }

    /**
     * 修改商户状态
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_MERCHANT_FREEZE,consumeThreadMax = 3,consumerGroup = "merchantFreezeGroup")
    public class MerchantFreezeListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JSONObject.parseObject(jsonParam,MerchantFlowVo.class);
            employerBiz.updateStatus(merchantFlowVo);
        }
    }

    /**
     * 修改销售/合伙人
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_CHANGE_SALER,consumeThreadMax = 3,consumerGroup = "merchantChangeSaler")
    public class MerchantChangeSalerListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JSONObject.parseObject(jsonParam,MerchantFlowVo.class);
            employerBiz.changeSalerAndAgent(merchantFlowVo);
        }
    }

    /**
     * 修改账户信息
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_CHANGE_ACCOUNT,consumeThreadMax = 3,consumerGroup = "merchantChangeAccount")
    public class MerchantChangeAccountListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JSONObject.parseObject(jsonParam,MerchantFlowVo.class);
            employerBiz.changeAccount(merchantFlowVo);
        }
    }

    /**
     * 修改合伙人产品报价单信息
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_QUOTE_EDIT,selectorExpression = MessageMsgDest.TAG_AGENT_QUOTE_EDIT,consumeThreadMax = 1,consumerGroup = "agentQuoteEditConsumer")
    public class AgentQuoteEditListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam, CommonFlow.class);
            agentManagerBiz.activeAgent(commonFlow);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_QUOTE_EDIT_ROLLBACK,selectorExpression = MessageMsgDest.TAG_AGENT_QUOTE_EDIT_ROLLBACK,consumeThreadMax = 1,consumerGroup = "cancelAgentQuoteConsumer")
    public class CancelAgentQuoteListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String flowId) {
            agentManagerBiz.agentQuoteDelByFlowId(flowId);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_QUOTE_EDIT_MODIFY,selectorExpression = MessageMsgDest.TAG_AGENT_QUOTE_EDIT_MODIFY,consumeThreadMax = 1,consumerGroup = "modifyAgentQuoteConsumer")
    public class ModifyAgentQuoteListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String flowId) {
            agentManagerBiz.agentQuoteFlowModify(flowId);
        }
    }

    /**
     * 修改合伙人主体信息信息
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_MAININFO_EDIT,selectorExpression = MessageMsgDest.TAG_AGENT_MAININFO_EDIT,consumeThreadMax = 1,consumerGroup = "agentMainInfoEditConsumer")
    public class AgentMainInfoEditListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam, CommonFlow.class);
            agentManagerBiz.editAgentMainfo(commonFlow);
        }
    }

    /**
     * 修改合伙人银行账户
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_BANKACCT_EDIT,selectorExpression = MessageMsgDest.TAG_AGENT_BANKACCT_EDIT,consumeThreadMax = 1,consumerGroup = "agentBankAcctEditConsumer")
    public class AgentBankAcctEditListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam, CommonFlow.class);
            log.info("修改合伙人银行账号:{}",jsonParam);
            agentManagerBiz.editBankAcct(commonFlow);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_ADD_MERCHANT_QUOTE,consumeThreadMax = 3,consumerGroup = "afterFlowCreateQuote")
    public class CreateMerchantQuoteAfterFlowListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JSONObject.parseObject(jsonParam,MerchantFlowVo.class);
            QuoteHandlerInterface quoteHandlerInterface = adaptHelper.getHandler(merchantFlowVo.getProductNo());
            quoteHandlerInterface.createAfterFlow(merchantFlowVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ADD_MERCHANT_QUOTE,selectorExpression = MessageMsgDest.TAG_ADD_MERCHANT_QUOTE,consumeThreadMax = 3,consumerGroup = "newMerchantQuoteGroup")
    public class CreateMerchantQuoteViewListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JSONObject.parseObject(jsonParam,MerchantFlowVo.class);
            QuoteHandlerInterface quoteHandlerInterface = adaptHelper.getHandler(merchantFlowVo.getProductNo());
            quoteHandlerInterface.create(merchantFlowVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_SET_SELLER,selectorExpression = MessageMsgDest.TAG_AGENT_SET_SELLER,consumeThreadMax = 3,consumerGroup = "setAgentSellerGroup")
    public class AgentSetSellerListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam,CommonFlow.class);

            agentManagerBiz.setAgentSeller(commonFlow);

        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_SET_INVITER,selectorExpression = MessageMsgDest.TAG_AGENT_SET_INVITER,consumeThreadMax = 3,consumerGroup = "setAgentInviterGroup")
    public class AgentSetInviterListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam,CommonFlow.class);

            agentManagerBiz.setAgentInviter(commonFlow);

        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_SET_PRINCIPAL,selectorExpression = MessageMsgDest.TAG_AGENT_SET_PRINCIPAL,consumeThreadMax = 3,consumerGroup = "setPrincipalGroup")
    public class AgentSetPrincipalListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam,CommonFlow.class);

            agentManagerBiz.setAgentPrincipal(commonFlow);

        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_EDIT_QUOTE_ASYNC,consumeThreadMax = 1,consumerGroup = "editQuoteConsumer")
    public class EditQuoteListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JSONObject.parseObject(jsonParam,MerchantFlowVo.class);
            QuoteHandlerInterface quoteHandlerInterface = adaptHelper.getHandler(merchantFlowVo.getProductNo());
            quoteHandlerInterface.editQuote(merchantFlowVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_APPROVAL_QUOTE_CANDEL,consumeThreadMax = 1,consumerGroup = "cancelQuoteConsumer")
    public class CancelQuoteListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String flowId) {
            Map<String,Object> map = new HashMap<>();
            map.put("flowBusinessKey",flowId);
            MerchantEmployerQuote merchantEmployerQuote = quoteDao.getOne(map);
            if (merchantEmployerQuote == null){
                log.info("报价单不存在");
                return;
            }
            QuoteHandlerInterface quoteHandlerInterface = adaptHelper.getHandler(merchantEmployerQuote.getProductNo());
            quoteHandlerInterface.cancel(merchantEmployerQuote);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_DELETE_MERCHANT_QUOTE,consumeThreadMax = 1,consumerGroup = "deleteQuoteConsumer")
    public class DeleteQuoteListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JsonUtil.toBean(jsonParam,MerchantFlowVo.class);
            QuoteHandlerInterface quoteHandlerInterface = adaptHelper.getHandler(merchantFlowVo.getProductNo());
            quoteHandlerInterface.delete(merchantFlowVo.getQuoteId());
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_FLOW_ARRIVE_MAIL,consumeThreadMax = 3,consumerGroup = "flowMailConsumer")
    public class FlowMailListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            merchantBiz.sendFlowMail(jsonParam);
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_QUOTE_DEL,selectorExpression = MessageMsgDest.TAG_AGENT_QUOTE_DEL,consumeThreadMax = 3,consumerGroup = "agentQuoteDelConsumer")
    public class agentQuoteDelListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam,CommonFlow.class);

            agentManagerBiz.agentQuoteDel(commonFlow);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_BATCH_SET_INVITER_SELLER,selectorExpression = MessageMsgDest.TAG_AGENT_BATCH_SET_INVITER_SELLER,consumeThreadMax = 3,consumerGroup = "agentBacthEditConsumer")
    public class agentBatchEditSalerInviterListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            CommonFlow commonFlow = JSONObject.parseObject(jsonParam,CommonFlow.class);
            agentManagerBiz.agentBatchEditSalerInviter(commonFlow);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_YISHUI,selectorExpression = MessageMsgDest.TAG_YISHUI_ADD_MERCHANT,consumeThreadMax = 1,consumerGroup = "yishuiAddMchConsumer")
    public class yishuiAddMerchantListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            ReportEntity reportEntity = JSONUtil.toBean(jsonParam, ReportEntity.class);
            yishuiBiz.addMerchant(reportEntity);
        }
    }

    /**
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_YISHUI,selectorExpression = MessageMsgDest.TAG_YISHUI_EDIT_FEE,consumeThreadMax = 1,consumerGroup = "yishuiEditFeeConsumer")
    public class yishuiEditFeeListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            ReportEntity reportEntity = JSONUtil.toBean(jsonParam, ReportEntity.class);
            yishuiBiz.editFee(reportEntity);
        }
    }**/

}
