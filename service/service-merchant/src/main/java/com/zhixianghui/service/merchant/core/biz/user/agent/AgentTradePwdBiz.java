package com.zhixianghui.service.merchant.core.biz.user.agent;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentTradePwd;
import com.zhixianghui.service.merchant.core.dao.user.agent.AgentTradePwdDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* 供应商支付密码表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-14
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentTradePwdBiz {

    private final AgentTradePwdDao agentTradePwdDao;

    /**
     * 根据商户编号查询
     *
     * @param agentNo
     */
    public AgentTradePwd getByAgentNo(String agentNo) {
        return agentTradePwdDao.getByAgentNo(agentNo);
    }

    /**
     * 创建
     *
     * @param tradePwd
     */
    public void create(AgentTradePwd tradePwd) throws BizException {
        if (tradePwd.getCreateTime() == null) {
            tradePwd.setCreateTime(new Date());
        }
        try {
            agentTradePwdDao.insert(tradePwd);
        } catch (DuplicateKeyException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付密码已存在");
        }
    }

    /**
     * 更新
     *
     * @param tradePwd
     */
    public void update(AgentTradePwd tradePwd) {
        agentTradePwdDao.update(tradePwd);
    }
}