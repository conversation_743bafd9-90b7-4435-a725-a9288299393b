package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.AgentCredential;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 合伙人基本信息表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Repository
public class AgentDao extends MyBatisDao<Agent,Long> {

    public PageResult<List<AgentResVo>> listVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        return listPage("listVoPage","countVoPage",paramMap,pageParam);
    }

    public List<AgentResVo> listVoAll(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("listVoPage"), paramMap);
    }

	public Agent getByAgentNo(String agentNo) {
        LimitUtil.notEmpty(agentNo, "合伙人编号不能为空");
        return getOne(Collections.singletonMap("agentNo", agentNo));
    }

	public void updateInvitationNum(String agentNo) {
        int result = update("updateInvitationNum",agentNo);
        if (result != 1) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("数据更新成功的记录数不为1");
        }
    }

    public void updateMerNum(String agentNo) {
        int result = update("updateMerNum",agentNo);
        if (result != 1) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("数据更新成功的记录数不为1");
        }
    }

    /**
     * 作为邀请方被清除
     * 传入合伙人下级的邀请人将被清除
     * @param agentNo 合伙人编号
     */
    public void clearAsInviter(String agentNo) {
        LimitUtil.notEmpty(agentNo, "合伙人编号不能为空");
        update("clearAsInviter",agentNo);
    }
}
