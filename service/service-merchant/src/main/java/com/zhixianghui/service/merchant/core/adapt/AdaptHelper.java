package com.zhixianghui.service.merchant.core.adapt;

import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName AdaptHelper
 * @Description TODO
 * @Date 2022/6/24 16:48
 */
@Component
public class AdaptHelper {

    @Autowired
    private ZXHQuoteHandler zxhQuoteHandler;

    @Autowired
    private CKHQuoteHandler ckhQuoteHandler;

    @Autowired
    private ZFTQuoteHandler zftQuoteHandler;

    @Autowired
    private CEPQuoteHandler cepQuoteHandler;

    public QuoteHandlerInterface getHandler(String productNo){
        ProductNoEnum productNoEnum = ProductNoEnum.getEnum(productNo);
        if (productNoEnum == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(productNo + "产品不存在");
        }
        switch (productNoEnum){
            case ZXH:
                return zxhQuoteHandler;
            case CKH:
                return ckhQuoteHandler;
            case ZFT:
                return zftQuoteHandler;
            case CEP:
                return cepQuoteHandler;
            default:
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("产品未开通或产品不存在");
        }
    }
}
