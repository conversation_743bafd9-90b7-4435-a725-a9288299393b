package com.zhixianghui.service.merchant.core.dao.user.agent;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentStaff;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 供应商后台员工表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class AgentStaffDao extends MyBatisDao<AgentStaff,Long> {
    public List<AgentStaff> listByOperatorId(long operatorId) {
        return super.listBy(Collections.singletonMap("operatorId", operatorId));
    }

    public void updateTypeById(Long id, int type, String updator) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("type", type);
        param.put("updator", updator);
        param.put("update_time", new Date());
        super.update("updateTypeById", param);
    }

    public void deleteByOperatorId(long operatorId) {
        super.deleteBy("deleteByOperatorId", operatorId);
    }

    public void deleteByAgentNoAndId(String agentNo, long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("agentNo", agentNo);
        param.put("id", id);
        super.deleteBy("deleteByAgentNoAndId", param);
    }

    public AgentStaffVO getVOByAgentNoAndId(String agentNo, long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("agentNo", agentNo);
        param.put("id", id);
        return getOne("getVOByAgentNoAndId", param);
    }

    public AgentStaffVO getVOByAgentAndPhone(String agentNo, String phone) {
        Map<String, Object> param = new HashMap<>();
        param.put("agentNo", agentNo);
        param.put("phone", phone);
        return getOne("getVOByAgentAndPhone", param);
    }

    public List<AgentStaffVO> getVOByAgentNoAndType(String agentNo, int type) {
        Map<String, Object> param = new HashMap<>();
        param.put("agentNo", agentNo);
        param.put("type", type);
        return listBy("getVOByAgentNoAndType", param);
    }

    public List<AgentStaffVO> listVOByOperatorId(Long id) {
        return listBy("listVOByOperatorId", Collections.singletonMap("operatorId", id));
    }

    public List<AgentStaffVO> listVOByPhone(String phone) {
        return listBy("listVOByPhone", Collections.singletonMap("phone", phone));
    }

    public List<AgentStaffVO> listStaffVOByRoleId(long roleId) {
        return listBy("listStaffVOByRoleId", Collections.singletonMap("roleId", roleId));
    }

    public PageResult<List<AgentStaffVO>> listStaffVOPage(Map<String, Object> paramMap, PageParam pageParam) {
        return listPage("listStaffRoleVOPage", "listStaffRoleVOPageCount", paramMap, pageParam);
    }
}
