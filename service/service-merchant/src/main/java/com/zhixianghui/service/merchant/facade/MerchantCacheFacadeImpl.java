package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.service.MerchantCacheFacade;
import com.zhixianghui.service.merchant.helper.CacheBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 商户信息缓存查询接口
 * <AUTHOR>
 * @date 2020/11/26
 **/
@Service
public class MerchantCacheFacadeImpl implements MerchantCacheFacade {
    @Autowired
    private CacheBiz cacheBiz;

    @Override
    public Merchant getByMchNo(String mchNo) {
        return cacheBiz.getByMchNo(mchNo);
    }

    @Override
    public MerchantSaler getMerchantSalerByMchNo(String mchNo) {
        return cacheBiz.getMerchantSalerByMchNo(mchNo);
    }

}
