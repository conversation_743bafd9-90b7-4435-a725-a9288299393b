package com.zhixianghui.service.merchant.facade.pms;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog;
import com.zhixianghui.facade.merchant.service.pms.PmsOperateLogFacade;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperateLogBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * Author: Cmf
 * Date: 2019/11/1
 * Time: 15:48
 * Description:
 */
@Service
public class PmsOperateLogFacadeImpl implements PmsOperateLogFacade {
    @Autowired
    private PmsOperateLogBiz pmsOperateLogBiz;

    @Override
    public void createOperateLog(PmsOperateLog operateLog) {
        String address = pmsOperateLogBiz.buildAddress(operateLog);
        operateLog.setAddress(address);
        pmsOperateLogBiz.createOperateLog(operateLog);
    }

    @Override
    public PmsOperateLog createOperateLogGetkey(PmsOperateLog operateLog) {
        return pmsOperateLogBiz.createOperateLogGetkey(operateLog);
    }

    @Override
    public PageResult<List<PmsOperateLog>> listOperateLogPage(Map<String, Object> paramMap, PageParam pageParam) {
        return pmsOperateLogBiz.listOperateLogPage(paramMap, pageParam);
    }

    @Override
    public PmsOperateLog getOperateLogById(Long id) {
        return pmsOperateLogBiz.getOperateLogById(id);
    }

    @Override
    public void updateOperateLog(PmsOperateLog operateLog){
        pmsOperateLogBiz.updateOperateLog(operateLog);
    }
}
