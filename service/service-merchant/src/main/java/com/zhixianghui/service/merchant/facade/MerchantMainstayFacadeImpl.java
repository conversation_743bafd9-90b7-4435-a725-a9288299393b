package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.service.MerchantMainstayFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantMainstayBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
public class MerchantMainstayFacadeImpl implements MerchantMainstayFacade {

    @Autowired
    private MerchantMainstayBiz merchantMainstayBiz;

    @Override
    public void createMainstay(Merchant merchant, MerchantSaler saler, List<MerchantFile> fileList) {
        merchantMainstayBiz.createMainstay(merchant, saler, fileList);
    }
}
