package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;


/**
 * 商户银行账号
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Repository
public class MerchantBankAccountDao extends MyBatisDao<MerchantBankAccount, Long> {

    public MerchantBankAccount getByMchNo(String mchNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        return getOne(param);
    }
}
