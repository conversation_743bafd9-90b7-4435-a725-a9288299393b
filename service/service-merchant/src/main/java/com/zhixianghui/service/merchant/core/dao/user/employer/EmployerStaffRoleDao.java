package com.zhixianghui.service.merchant.core.dao.user.employer;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerStaffRole;
import org.springframework.stereotype.Repository;


@Repository
public class EmployerStaffRoleDao extends MyBatisDao<EmployerStaffRole, Long>{

    public void deleteByStaffId(long staffId) {
        super.deleteBy("deleteByStaffId", staffId);
    }

    public void deleteByRoleId(long roleId) {
        super.deleteBy("deleteByRoleId", roleId);
    }
}
