package com.zhixianghui.service.merchant.core.biz;


import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.MerchantBusinessEnum;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.EmployerCooperateUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerFullInfoUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerMainUpdateVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.service.merchant.core.dao.*;
import com.zhixianghui.service.merchant.core.util.BuildVoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;


/**
 * 用工商户合作信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
@Slf4j
public class MerchantEmployerCooperateBiz {
    @Autowired
    private MerchantEmployerCooperateDao cooperateDao;
    @Autowired
    private MerchantEmployerPositionDao positionDao;
    @Autowired
    private MerchantEmployerQuoteDao quoteDao;
    @Autowired
    private MerchantDao merchantDao;
    @Autowired
    private MerchantSalerDao merchantSalerDao;
    @Autowired
    private AgentDao agentDao;
    @Autowired
    private MerchantFileBiz fileBiz;
    @Autowired
    private AgentManagerBiz agentManagerBiz;
    @Autowired
    private MerchantEmployerQuotePositionDao merchantEmployerQuotePositionDao;
    @Reference
    private PmsOperatorFacade operatorFacade;
    @Reference
    private EmployerStaffFacade employerStaffFacade;
    @Reference
    private MerchantEmployerMainFacade mainFacade;

    public MerchantEmployerCooperate getByMchNo(String mchNo) {
        return cooperateDao.getByMchNo(mchNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(EmployerCooperateUpdateVo vo, String loginName){
        MerchantEmployerCooperate cooperate = cooperateDao.getByMchNo(vo.getMchNo());
        Merchant merchant = merchantDao.getByMchNo(vo.getMchNo());
        String oldAgentNo = merchant.getAgentNo();
        Agent agent = null;
        if(StringUtils.isNotBlank(vo.getAgentNo())){
            //设置了合伙人
            agent = agentDao.getByAgentNo(vo.getAgentNo());
            LimitUtil.notEmpty(agent,"指定合伙人不存在");
            if(Objects.equals(agent.getAgentStatus(), AgentStatusEnum.RETREAT.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("指定合伙人已清退，不允许操作");
            }
            merchant.setAgentNo(agent.getAgentNo());
            merchant.setAgentName(agent.getAgentName());
        }else {
            merchant.setAgentNo(null);
            merchant.setAgentName(null);
        }

        //更新用工企业员工表信息
        employerStaffFacade.updateByMchNo(vo);

        Assert.notNull(cooperate, "商户编号错误");
        log.info("更新用工企业合作信息：{}", JsonUtil.toString(vo));

        // 更新商户基本信息
        merchant.setServicePhone(vo.getServicePhone());
        merchant.setContactEmail(vo.getContactEmail());
        merchant.setMchName(vo.getMchName());
        merchant.setRemark(vo.getRemark());
        merchant.setUpdateTime(new Date());
        merchant.setUpdator(loginName);

        merchantDao.update(merchant);
        //旧的去除
        agentManagerBiz.updateMerNum(oldAgentNo);
        //新的增加
        agentManagerBiz.updateMerNum(merchant.getAgentNo());
        log.info("{}==>更新用工企业基本信息", vo.getMchNo());
        //合伙人关系数统计 要在商户update后 否则不准
        if(agent != null){
            agentManagerBiz.updateMerNum(agent.getAgentNo());
        }
        // 更新商户销售
        MerchantSaler merchantSaler = merchantSalerDao.getByMchNo(vo.getMchNo());
        if(!Objects.equals(merchantSaler.getSalerId(), vo.getSalerId())){
            PmsOperator operator = operatorFacade.getOperatorById(vo.getSalerId());
            merchantSaler.setUpdateTime(new Date());
            merchantSaler.setUpdator(loginName);
            merchantSaler.setSalerId(vo.getSalerId());
            merchantSaler.setSalerName(operator.getRealName());
            merchantSaler.setSaleDepartmentId(operator.getDepartmentId());
            merchantSaler.setSaleDepartmentName(operator.getDepartmentName());
            merchantSalerDao.update(merchantSaler);
            log.info("{}==>更新商户销售信息", vo.getMchNo());
        }

        // 更新文字信息
        cooperate.setUpdateTime(new Date());
        cooperate.setUpdator(loginName);
        cooperate.setIndustryTypeCode(vo.getIndustryTypeCode());
        cooperate.setIndustryTypeName(vo.getIndustryTypeName());
        cooperate.setWorkerNum(vo.getWorkerNum());
        cooperate.setSignRateLevel(vo.getSignRateLevel());
        cooperate.setWorkerMonthIncomeRate(vo.getWorkerMonthIncomeRate());
        cooperate.setMonthMoneySlip(vo.getMonthMoneySlip());
        cooperate.setProvideIncomeDetailType(vo.getProvideIncomeDetailType());
        cooperate.setCompanyWebsite(vo.getCompanyWebsite());
        cooperate.setBizPlatformName(vo.getBizPlatformName());
        cooperateDao.update(cooperate);
        log.info("{}==>更新合作信息表", vo.getMchNo());

        // 更新岗位信息, 删除原来信息再重新保存
        positionDao.deleteByMchNo(merchant.getMchNo());
        List<MerchantEmployerPosition> positions = BuildVoUtil.fillEmployerPosition(vo.getPositionVoList(), loginName,vo.getMchNo());
        positionDao.insert(positions);
        log.info("{}==>更新岗位信息表", vo.getMchNo());

        // 更新报价单, 删除原来信息再重新保存
        quoteDao.deleteByMchNo(merchant.getMchNo());
        List<MerchantEmployerQuote> quotes = BuildVoUtil.fillEmployerQuote(vo, loginName);
        quoteDao.insert(quotes);
        log.info("{}==>更新报价单", vo.getMchNo());

        // 更新公司宣传文件记录
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.COMPANY_LEAFLET.getValue(), vo.getCompanyLeafletFileUrls());

        // 更新补充信息文件记录
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue(), vo.getSupplementFileUrls());
        log.info("{}==>更新文件记录", vo.getMchNo());
    }

    public void updateSpecifiedField(MerchantEmployerCooperate cooperate) {
        cooperateDao.update(cooperate);
        log.info("{}==>更新合作信息表", cooperate.getMchNo());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAllMerchantMessage(EmployerFullInfoUpdateVo vo) {
        //更新商户合作信息
        EmployerCooperateUpdateVo employerCooperateUpdateVo = new EmployerCooperateUpdateVo();
        BeanUtils.copyProperties(vo,employerCooperateUpdateVo);
        update(employerCooperateUpdateVo,vo.getCurrentOperator());

        //更新商户主体信息
        EmployerMainUpdateVo employerMainUpdateVo = new EmployerMainUpdateVo();
        BeanUtils.copyProperties(vo,employerMainUpdateVo);
        mainFacade.updateOrInsert(employerMainUpdateVo,vo.getCurrentOperator());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateMainstayCooperate(MerchantUpdateVo merchantUpdateVo) {
        Merchant merchant = merchantDao.getByMchNo(merchantUpdateVo.getMchNo());
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        // 更新委托协议
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(),
                merchantUpdateVo.getUpdator(), MerchantFileTypeEnum.ENTRUST_AGREEMENT.getValue(), Arrays.asList(merchantUpdateVo.getEntrustAgreementFileUrl()));
        // B端协议模板
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(),
                merchantUpdateVo.getUpdator(), MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_B.getValue(), Arrays.asList(merchantUpdateVo.getAgreementTemplate2BFileUrl()));
        // C端协议模板
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(),
                merchantUpdateVo.getUpdator(), MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_C.getValue(), Arrays.asList(merchantUpdateVo.getAgreementTemplate2CFileUrl()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateMerchantCooperate(MerchantUpdateVo merchantUpdateVo) {
        Merchant merchant = merchantDao.getByMchNo(merchantUpdateVo.getMchNo());
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        //更新业务类型
        merchant.setBusinessType(String.join(",",merchantUpdateVo.getBusinessType()));
        merchantDao.update(merchant);
        //更新合作信息
        MerchantEmployerCooperate merchantEmployerCooperate = cooperateDao.getByMchNo(merchantUpdateVo.getMchNo());
        merchantUpdateVo.buildMerchantCooperate(merchantEmployerCooperate);
        cooperateDao.update(merchantEmployerCooperate);

        //包含灵工业务才需要处理
        if (merchantUpdateVo.getBusinessType().contains(MerchantBusinessEnum.FLEXIBLE_SERVICE.getValue())){
            //修改不删除
            positionDao.deleteByMchNo(merchantUpdateVo.getMchNo());
            List<MerchantEmployerPosition> positions = BuildVoUtil.fillEmployerPosition(merchantUpdateVo.getPosition(), merchantUpdateVo.getUpdator(), merchantUpdateVo.getMchNo());
            positionDao.insert(positions);

            //修改报价单关联数据
            for (MerchantEmployerPosition position : positions) {
                Map<String,Object> paramMap = new HashMap<>();
                if (position.getOriginId() == null){
                    continue;
                }
                paramMap.put("positionId",position.getOriginId());
                paramMap.put("mchNo",merchantUpdateVo.getMchNo());
                List<MerchantEmployerQuotePosition> employerQuotePositionList = merchantEmployerQuotePositionDao.listBy(paramMap);
                if (employerQuotePositionList.size() == 0){
                    continue;
                }
                //更新报价单关联表
                employerQuotePositionList.stream().forEach(x->x.setPositionId(position.getId()));
                merchantEmployerQuotePositionDao.update(employerQuotePositionList);
            }

            //宣传文件
            fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(),
                    merchantUpdateVo.getUpdator(), MerchantFileTypeEnum.COMPANY_LEAFLET.getValue(), merchantUpdateVo.getCompanyLeafletFileUrls());
            //补充文件
            fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(),
                    merchantUpdateVo.getUpdator(), MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue(), merchantUpdateVo.getSupplementFileUrls());
        }

    }
}
