package com.zhixianghui.service.merchant.core.dao.user.pms;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;

import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 部门表数据访问层接口实现类
 *
 * <AUTHOR>
 */
@Repository("pmsDepartmentDao")
public class PmsDepartmentDao extends MyBatisDao<PmsDepartment, Long> {

    /**
     *  根据序号查总数
     * @param paramMap
     * @return
     */
    public Long getCountByNumber( Map<String, Object> paramMap) {
        return this.countBy("countByNumber", paramMap);
    }
}
