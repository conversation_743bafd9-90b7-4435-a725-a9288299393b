package com.zhixianghui.service.merchant.core.dao.user.employer;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.vo.EmployerRoleVo;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;


@Repository
public class EmployerRoleDao extends MyBatisDao<EmployerRole, Long>{

    public void updateByIdAndMchNo(EmployerRole role) {
        super.update("updateByIdAndMchNo", role);
    }

    public List<EmployerRole> listByMchNo(String mchNo) {
        return super.listBy(Collections.singletonMap("mchNo", mchNo));
    }

    public List<EmployerRole> listByStaffId(long staffId) {
        return super.listBy("listByStaffId", Collections.singletonMap("staffId", staffId));
    }

    public PageResult<List<EmployerRoleVo>> listEmployerRoleVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        return super.listPage("listPage", "listPageCount", paramMap, pageParam);

    }
}
