package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.AgreementTemplate;
import com.zhixianghui.facade.merchant.service.AgreementTemplateFacade;
import com.zhixianghui.service.merchant.core.biz.AgreementTemplateBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 协议模板表 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementTemplateImpl implements AgreementTemplateFacade {

    private final AgreementTemplateBiz biz;

    @Override
    public void createAgreementTemplate(AgreementTemplate agreementTemplate) {
        biz.createAgreementTemplate(agreementTemplate);
    }

    @Override
    public void updateAgreementTemplate(AgreementTemplate agreementTemplate) {
        biz.updateAgreementTemplate(agreementTemplate);
    }

    @Override
    public PageResult<List<AgreementTemplate>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public AgreementTemplate getById(Long id) {
        return biz.getById(id);
    }

    @Override
    public List<AgreementTemplate> listAll() {
        return biz.listAll();
    }

    @Override
    public void deleteAll() {
        biz.deleteAll();
    }
}
