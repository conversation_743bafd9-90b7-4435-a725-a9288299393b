package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 商户文件信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Repository
public class MerchantFileDao extends MyBatisDao<MerchantFile, Long> {

    public List<MerchantFile> listByMchNoAndFileType(String mchNo, Integer fileType) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("fileType", fileType);
        return listBy(param);
    }
}
