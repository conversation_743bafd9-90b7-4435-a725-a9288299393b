package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteRateFacade;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerQuoteRateVo;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerQuoteRateBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-07-20
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantEmployerQuoteRateImpl implements MerchantEmployerQuoteRateFacade {

    private final MerchantEmployerQuoteRateBiz biz;

    public List<MerchantEmployerQuoteRateVo> listMerchantQuoteRate(String mchNo) {
        return biz.listMerchantQuoteRate(mchNo);
    }

    @Override
    public Map<Long, List<MerchantEmployerQuoteRate>> groupByQuoteIds(List<Long> quoteIdList) {
        return biz.groupByQuoteIds(quoteIdList);
    }
}
