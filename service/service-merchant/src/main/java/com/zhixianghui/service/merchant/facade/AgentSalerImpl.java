package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.AgentSaler;
import com.zhixianghui.facade.merchant.service.AgentSalerFacade;
import com.zhixianghui.service.merchant.core.biz.AgentSalerBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentSalerImpl implements AgentSalerFacade {

    private final AgentSalerBiz biz;

    @Override
    public AgentSaler getByAgentNo(String agentNo) {
        return biz.getByAgentNo(agentNo);
    }
}
