package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.service.MerchantBankAccountFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantBankAccountBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;


/**
 * 用工商户合作信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantBankAccountFacadeImpl implements MerchantBankAccountFacade {

    @Autowired
    private MerchantBankAccountBiz accountBiz;

    @Override
    public MerchantBankAccount getByMchNo(String mchNo) {
        return accountBiz.getByMchNo(mchNo);
    }

    @Override
    public void update(MerchantBankAccount cooperate) throws BizException {
        accountBiz.update(cooperate);
    }

    @Override
    public void insert(MerchantBankAccount account) throws BizException {
        accountBiz.insert(account);
    }

    @Override
    public Map<String, Object> getAccountInfo(String mchNo) {
        return accountBiz.getAccountInfo(mchNo);
    }

    @Override
    public Map<String, Object> getAccountInfoOfMap(String mchNo) {
        return accountBiz.getAccountInfoOfMap(mchNo);
    }

    @Override
    public Map<String, Object> mainstayGetAccountInfo(String mainstayNo, String mchNo) {
        return accountBiz.mainstayGetAccountInfo(mainstayNo,mchNo);
    }
}
