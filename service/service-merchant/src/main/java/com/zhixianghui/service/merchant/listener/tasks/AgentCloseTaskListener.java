package com.zhixianghui.service.merchant.listener.tasks;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.service.merchant.core.biz.AgentBiz;
import com.zhixianghui.service.merchant.core.biz.AgentManagerBiz;
import com.zhixianghui.service.merchant.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Component
@RocketMQMessageListener(topic = "topic-agent-close-task",consumeThreadMax = 1,consumerGroup = "AgentCloseTaskGroup")
@Slf4j
public class AgentCloseTaskListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private AgentBiz agentBiz;

    @Override
    public void runTask(JSONObject jsonParam) {

        int trialDays = jsonParam.getInteger("trialDays");
        Date now = new Date();
        Date date = DateUtil.addDay(now, trialDays);

        Map<String, Object> parmap = new HashMap();
        parmap.put("authStatus", AuthStatusEnum.UN_AUTH.getValue());
        parmap.put("agentStatus", AgentStatusEnum.CREATE.getValue());
        parmap.put("updateEndTime", date);
        parmap.put("updateTime", now);

        agentBiz.closeExpireTrialAgent(parmap);
    }
}
