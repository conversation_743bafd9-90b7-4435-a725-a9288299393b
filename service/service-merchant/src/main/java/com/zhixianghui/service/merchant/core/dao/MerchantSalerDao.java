package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import org.apache.ibatis.annotations.MapKey;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 商户销售信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Repository
public class MerchantSalerDao extends MyBatisDao<MerchantSaler, Long> {
    public MerchantSaler getByMchNo(String mchNo){
        if(StringUtil.isEmpty(mchNo)){
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        return super.getOne(param);
    }

    public Map<String, Object> getSalerCount(Map<String, Object> thisMonth) {
        return this.getSqlSession().selectMap(fillSqlId("getSalerCount"),thisMonth,"salerId");
    }
}
