package com.zhixianghui.service.merchant.facade.agent;

import com.zhixianghui.facade.merchant.service.agent.AgentStaffRoleFacade;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentStaffRoleBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 供应商后台员工角色关联表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentStaffRoleImpl implements AgentStaffRoleFacade {

    private final AgentStaffRoleBiz biz;

    @Override
    public Long countEmployerCount(Long id) {

        return biz.countEmployerCount(id);
    }
}
