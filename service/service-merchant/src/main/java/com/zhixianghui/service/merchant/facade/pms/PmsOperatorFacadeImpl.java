package com.zhixianghui.service.merchant.facade.pms;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperatorBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * Author: Cmf
 * Date: 2019/10/10
 * Time: 11:24
 * Description:
 */
@Service
public class PmsOperatorFacadeImpl implements PmsOperatorFacade {

    @Autowired
    private PmsOperatorBiz pmsOperatorBiz;

    @Override
    public PmsOperator getOperatorById(long id) {
        return pmsOperatorBiz.getOperatorById(id);
    }

    @Override
    public PmsOperator getOperatorByLoginName(String loginName) {
        return pmsOperatorBiz.getOperatorByLoginName(loginName);
    }

    @Override
    public void deleteOperatorById(long id) throws BizException {
        pmsOperatorBiz.deleteOperatorById(id);
    }

    @Override
    public void updateOperator(PmsOperator operator) {
        pmsOperatorBiz.updateOperator(operator);
    }

    @Override
    public void updateOperatorAndAssignRoles(PmsOperator operator, List<Long> roleIds) throws BizException {
        pmsOperatorBiz.updateOperatorAndAssignRoles(operator, roleIds);
    }
	@Override
	public void insertOperatorAndAssignRoles(PmsOperator operator, List<Long> roleIds) {
		pmsOperatorBiz.insertOperatorAndAssignRoles(operator, roleIds);
	}

    @Override
    public void updateOperatorPwd(Long operatorId, String newPwd, boolean isChangedPwd) throws BizException {
        pmsOperatorBiz.updateOperatorPwd(operatorId, newPwd, isChangedPwd);

    }

    @Override
    public PageResult<List<PmsOperator>> listOperatorPage(Map<String, Object> paramMap, PageParam pageParam) {
        return pmsOperatorBiz.listOperatorPage(paramMap, pageParam);
    }

    @Override
    public List<PmsOperator> listBy(Map<String, Object> paramMap) {
        return pmsOperatorBiz.listBy(paramMap);
    }

    @Override
    public List<PmsOperator> listByLeaderId(long leaderId) throws BizException {
        return pmsOperatorBiz.listByLeaderId(leaderId);
    }

    @Override
    public List<PmsOperator> listByLeaderIdRecursive(long leaderId) throws BizException {
        return pmsOperatorBiz.listByLeaderIdRecursive(leaderId);
    }

    @Override
    public List<PmsOperator> listByDepartmentId(long departmentId) {
        return pmsOperatorBiz.listByDepartmentId(departmentId);
    }

    @Override
    public List<PmsOperator> listByDepartmentIdRecursive(long departmentId) {
        return pmsOperatorBiz.listByDepartmentIdRecursive(departmentId);
    }

    @Override
    public List<PmsOperator> listByDepartmentNumber(String number) {
        return pmsOperatorBiz.listByDepartmentNumber(number);
    }

    @Override
    public List<PmsOperator> listActiveByDepartmentNumber(String num) {
        return pmsOperatorBiz.listActiveByDepartmentNumber(num);
    }

    @Override
    public List<PmsOperator> listByDepartmentNumberRecursive(String number) {
        return pmsOperatorBiz.listByDepartmentNumberRecursive(number);
    }

    @Override
    public List<PmsOperator> listWithoutDepartment() {
        return pmsOperatorBiz.listWithoutDepartment();
    }

    @Override
    public void assignDepartment(long id, long departmentId) throws BizException {
        pmsOperatorBiz.assignDepartment(id, departmentId);
    }

    @Override
    public List<PmsOperator> getSale(Long id) {
        return pmsOperatorBiz.getSale(id);
    }

    @Override
    public List<PmsRoleOperator> listOperatorByRoleId(Integer roleId) {
        return pmsOperatorBiz.listOperatorByRoleId(roleId);
    }

    @Override
    public boolean isAdmin(Long id) {
        return pmsOperatorBiz.isAdmin(id);
    }

    @Override
    public Long getSaleRole() {
        return pmsOperatorBiz.getSaleRole();
    }

    @Override
    public void getAndPutOperatorCache(Long id) {
        pmsOperatorBiz.getAndPutOperatorCache(id);
    }
}
