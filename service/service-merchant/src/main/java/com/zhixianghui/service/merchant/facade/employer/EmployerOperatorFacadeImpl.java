package com.zhixianghui.service.merchant.facade.employer;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerOperator;
import com.zhixianghui.facade.merchant.service.employer.EmployerOperatorFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.service.merchant.core.biz.user.portal.EmployerOperatorBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class EmployerOperatorFacadeImpl implements EmployerOperatorFacade {
	
	@Autowired
	private EmployerOperatorBiz employerOperatorBiz;

	/**
	 * 根据id获取操作员
	 *
	 * @param id
	 */
	@Override
	public EmployerOperator getById(long id) {
		return employerOperatorBiz.getById(id);
	}

	/**
	 * 根据手机号码获取操作员
	 *
	 * @param phone
	 */
	@Override
	public EmployerOperator getByPhone(String phone) {
		return employerOperatorBiz.getByPhone(phone);
	}

	/**
	 * 根据id删除操作员
	 *
	 * @param id
	 */
	@Override
	public void deleteById(long id) throws BizException {
		employerOperatorBiz.deleteById(id);
	}

	/**
	 * 更新操作员
	 *
	 * @param operator
	 */
	@Override
	public void update(EmployerOperator operator) throws BizException {
		employerOperatorBiz.update(operator);
	}

	/**
	 * 分页查询操作员
	 *
	 * @param paramMap
	 * @param pageParam
	 */
	@Override
	public PageResult<List<EmployerOperatorVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
		return employerOperatorBiz.listPage(paramMap, pageParam);
	}
}
