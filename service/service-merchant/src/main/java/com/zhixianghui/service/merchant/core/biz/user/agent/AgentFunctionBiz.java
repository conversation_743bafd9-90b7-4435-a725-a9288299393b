package com.zhixianghui.service.merchant.core.biz.user.agent;

import com.zhixianghui.common.statics.enums.user.portal.PortalFunctionTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.service.merchant.core.dao.user.agent.AgentFunctionDao;
import com.zhixianghui.service.merchant.core.dao.user.agent.AgentRoleFunctionDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* 供应商后台功能表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentFunctionBiz {

    private final AgentFunctionDao agentFunctionDao;
    private final AgentRoleFunctionDao agentRoleFunctionDao;

    /**
     * 创建用工企业后台功能
     *
     * @param function 功能
     */
    public void create(AgentFunction function) throws BizException {
        if (function.getCreateTime() == null) {
            function.setCreateTime(new Date());
        }
        if (function.getParentId() == null) {
            function.setParentId(0L);
        } else {
            AgentFunction parent = agentFunctionDao.getById(function.getParentId());
            if (parent == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能父节点不存在");
            } else if (parent.getType() != PortalFunctionTypeEnum.MENU_TYPE.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("父节点必须是菜单类型");
            }
        }

        try {
            agentFunctionDao.insert(function);
        } catch (DuplicateKeyException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("权限标识重复");
        } catch (Exception e) {
            log.error("创建用工企业后台功能：系统异常", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("系统异常");
        }
    }

    /**
     * 根据id查询用工企业后台功能
     *
     * @param id
     */
    public AgentFunction getById(long id) {
        return agentFunctionDao.getById(id);
    }

    /**
     * 编辑用工企业后台功能
     *
     * @param function 功能
     */
    public void update(AgentFunction function) throws BizException {
        agentFunctionDao.update(function);
    }

    /**
     * 根据id删除功能，并删除该功能与角色的映射
     *
     * @param id
     */
    @Transactional
    public void deleteById(long id) throws BizException {
        AgentFunction function = agentFunctionDao.getById(id);
        if (function == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能不存在");
        }

        List<AgentFunction> subFunctions = agentFunctionDao.listByParentId(id);
        if (subFunctions.stream().anyMatch(p -> p.getType() == PortalFunctionTypeEnum.MENU_TYPE.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("存在子菜单，该菜单不能直接删除");
        }

        List<Long> deleteIds = subFunctions.stream().map(AgentFunction::getId).collect(Collectors.toList());
        deleteIds.add(id);
        agentFunctionDao.deleteByIds(deleteIds);				// 删除功能及其子功能
        agentRoleFunctionDao.deleteByFunctionIds(deleteIds);	// 删除功能与角色的映射
    }

    /**
     * 查询所有功能
     */
    public List<AgentFunction> listAll() {
        return agentFunctionDao.listAll("number asc");
    }

    public List<AgentFunction> listAll(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<AgentFunction>> result = agentFunctionDao.listPage(param, pageParam);
        return result == null ? null : result.getData();
    }

    public void saveFunction(List<AgentFunction> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (AgentFunction item : list) {
            if (StringUtils.isBlank(item.getParentPermissionFlag())) {
                item.setParentId(0L);
                agentFunctionDao.insert("importFile", item);
                continue;
            }
            AgentFunction agentFunction = agentFunctionDao.getOne(new HashMap<String, Object>(){{
                put("permissionFlag", item.getParentPermissionFlag());
            }});
            if (agentFunction != null) {
                item.setParentId(agentFunction.getId());
            }
            if (item.getParentId() == null) {
                log.error("parentId不能为空: [{}]", item.toString());
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("parentId不能为空");
            }
            agentFunctionDao.insert("importFile", item);
        }
    }


    public String getPermissionFlag(Long parentId) {
        return agentFunctionDao.getOne("getPermissionFlag", new HashMap<String, Object>(){{
            put("id", parentId);
        }});
    }
}