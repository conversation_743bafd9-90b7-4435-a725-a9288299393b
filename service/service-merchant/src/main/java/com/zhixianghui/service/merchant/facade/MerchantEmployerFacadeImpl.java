package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.service.MerchantEmployerFacade;
import com.zhixianghui.facade.merchant.vo.*;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerBiz;
import com.zhixianghui.service.merchant.helper.MerchantHelper;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


/**
 * 用工商户信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantEmployerFacadeImpl implements MerchantEmployerFacade {
    @Autowired
    private MerchantEmployerBiz employerBiz;
    @Autowired
    private MerchantHelper merchantHelper;
    @Autowired
    private MerchantEmployerBiz merchantEmployerBiz;

    @Override
    public MerchantEmployerDetailVo getInfoVO(Map<String,Object> param, List<Long> salerIds) {
        return employerBiz.getInfoVO(param, salerIds);
    }

    @Override
    public void validMainAuthVo(MerchantEmployerMainAuthVo authVo) throws BizException {
        merchantHelper.validMainAuthVo(authVo);
    }

    @Override
    public List<MerchantEmployerDetailVo> getInfoVoList(Map<String,Object> paramMap) {
        return employerBiz.getInfoVoList(paramMap);
    }

    @Override
    public void applyMerchant(MerchantEmployerAddVo vo) {
        employerBiz.applyMerchant(vo);
    }

    @Override
    public Map<String, Object> getCooperateInfo(String mchNo) {
        return employerBiz.getCooperateInfo(mchNo);
    }

    @Override
    public Map<String, Object> getCooperateInfoWithSupplier(String mainstayNo,String mchNo) {
        return employerBiz.getCooperateInfoWithSupplier(mainstayNo,mchNo);
    }

    @Override
    public void appleMerchantV2(MerchantEmployerAddVoV2 v2) {
        employerBiz.applyMerchantV2(v2);
    }

}
