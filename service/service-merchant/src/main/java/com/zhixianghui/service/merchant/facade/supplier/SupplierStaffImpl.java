package com.zhixianghui.service.merchant.facade.supplier;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierStaffBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台员工表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierStaffImpl implements SupplierStaffFacade {

    private final SupplierStaffBiz biz;

    /**
     * 根据id查询供应商员工
     *
     * @param mchNo 商户编号
     * @param id    员工id
     */
    @Override
    public SupplierStaffVO getById(String mchNo, long id) {
        return biz.getById(mchNo, id);
    }

    /**
     * 根据手机号查询供应商员工
     *
     * @param mchNo 商户编号
     * @param phone 手机号
     */
    @Override
    public SupplierStaffVO getByPhone(String mchNo, String phone) {
        return biz.getByPhone(mchNo, phone);
    }

    /**
     * 根据操作员id查询其关联的员工
     *
     * @param id
     * @return
     */
    @Override
    public List<SupplierStaffVO> listByOperatorId(long id) {
        return biz.listByOperatorId(id);
    }

    /**
     * 根据手机号查询其关联的员工
     *
     * @param phone
     * @return
     */
    @Override
    public List<SupplierStaffVO> listByPhone(String phone) {
        return biz.listByPhone(phone);
    }

    /**
     * 获取超级管理员
     *
     * @param mchNo 商户编号
     * @return
     */
    @Override
    public SupplierStaffVO getAdmin(String mchNo) throws BizException {
        return biz.getAdmin(mchNo);
    }

    /**
     * 创建供应商员工
     *
     * @param supplierStaffVo vo
     */
    @Override
    public long create(SupplierStaffVO supplierStaffVo) throws BizException {
        return biz.create(supplierStaffVo);
    }

    /**
     * 创建供应商员工并分配指定角色
     *
     * @param supplierStaffVo
     * @param roleIds
     */
    @Override
    public long createAndAssignRole(SupplierStaffVO supplierStaffVo, List<Long> roleIds) throws BizException {
        return biz.createAndAssignRole(supplierStaffVo, roleIds);
    }

    /**
     * 更换超级管理员
     *
     * @param mchNo         	商户编号
     * @param newAdminPhone 	新负责人手机号
     * @param newAdminName      新负责人姓名
     */
    @Override
    public void changeAdmin(String mchNo, String newAdminPhone, String newAdminName, String updator) throws BizException {
        biz.changeAdmin(mchNo, newAdminPhone, newAdminName, updator);
    }

    /**
     * 为供应商员工更新角色
     * @param mchNo     商户编号
     * @param staffId   员工id
     * @param roleIds   角色id
     */
    @Override
    public void updateRole(String mchNo, long staffId, List<Long> roleIds,String name) throws BizException {
        biz.updateRole(mchNo, staffId, roleIds,name);
    }

    /**
     * 根据id删除供应商员工
     *
     * @param mchNo 商户编号
     * @param id    员工id
     */
    @Override
    public void deleteById(String mchNo, long id) {
        biz.deleteById(mchNo, id);
    }

    /**
     * 根据员工id获取其关联的角色
     *
     * @param mchNo   商户编号
     * @param staffId 员工id
     */
    @Override
    public List<SupplierRole> getRoleByStaffId(String mchNo, long staffId) throws BizException {
        return biz.getRoleByStaffId(mchNo, staffId);
    }

    /**
     * 查询员工所关联的功能
     *
     * @param mchNo   商户编号
     * @param staffId 员工id
     */
    @Override
    public List<SupplierFunction> listFunctionByStaffId(String mchNo, long staffId) throws BizException {
        return biz.listFunctionByStaffId(mchNo, staffId);
    }

    /**
     * 分页查询员工
     *
     * @param paramMap
     * @param pageParam
     */
    @Override
    public PageResult<List<SupplierStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    /**
     * 分页查询员工
     *
     * @param mchNo
     * @param paramMap
     * @param pageParam
     * @return
     */
    @Override
    public PageResult<List<SupplierStaffVO>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(mchNo, paramMap, pageParam);
    }

    @Override
    public boolean isAdmin(SupplierStaffVO supplierStaffVO) {
        return biz.isAdmin(supplierStaffVO);
    }

    @Override
    public List<SupplierFunction> listAllFunction() {
        return biz.listAllFunction();

    }

    @Override
    public List<String> getDistinctStaffByRoleIdAndMainstayNo(String mainstayNo, List<Long> roleIds) {
        return biz.getDistinctStaffByRoleIdAndMainstayNo(mainstayNo,roleIds);
    }

    @Override
    public void getAndPutStaffCache(Long id) {
        biz.getAndPutStaffCache(id);
    }

    @Override
    public void updateByMainStayNo(Merchant merchant,String currentOperaotr) {
        biz.updateByMainStayNo(merchant,currentOperaotr);
    }
}
