package com.zhixianghui.service.merchant.core.biz;


import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.*;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.report.MerchantChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.dto.EnterprisePersonnelDto;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.MerchantBusinessEnum;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.*;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperatorBiz;
import com.zhixianghui.service.merchant.core.dao.*;
import com.zhixianghui.service.merchant.core.util.BuildVoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 用工企业商户信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
@Slf4j
public class MerchantEmployerBiz {

    @Value("${merchant.platform.link}")
    private String merchantLink;


    @Reference
    private EmailFacade emailFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private PmsOperatorFacade operatorFacade;
    @Reference
    private EmployerStaffFacade employerStaffFacade;
    @Autowired
    private MerchantDao merchantDao;
    @Autowired
    private MerchantSalerDao merchantSalerDao;
    @Autowired
    private MerchantEmployerCooperateDao employerDetailDao;
    @Autowired
    private MerchantEmployerPositionDao employerPositionDao;
    @Autowired
    private MerchantEmployerQuoteDao employerQuoteDao;
    @Autowired
    private MerchantFileDao merchantFileDao;
    @Autowired
    private MerchantBankAccountDao accountDao;
    @Autowired
    private MerchantEmployerMainDao mainDao;
    @Autowired
    private MerchantInvoiceInfoDao invoiceInfoDao;
    @Autowired
    private AgentDao agentDao;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private RobotFacade robotFacade;
    @Autowired
    private PmsOperatorBiz pmsOperatorBiz;
    @Autowired
    private MerchantEnterprisePersonnelBiz merchantEnterprisePersonnelBiz;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    private String generateBillNo() {
        String batchId = sequenceFacade.nextRedisId(
                "", SequenceBizKeyEnum.QUOTE_FLOW_NO_SEQ.getKey(), SequenceBizKeyEnum.QUOTE_FLOW_NO_SEQ.getWidth()
        );
        Date batchTime = new Date();
        return SequenceBizKeyEnum.AGENT_MONTH_BILL_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
    }


    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void createMerchant(MerchantEmployerInsertVo vo) {
        String logFlag = vo.getMchNo() + "-" + vo.getMchName();
        log.info("[{}]==>创建用工企业开始:{}", logFlag, JsonUtil.toString(vo));

        // 销售员
        PmsOperator operator = operatorFacade.getOperatorById(vo.getSalerId());

        // 保存商户信息表
        Merchant merchant = BuildVoUtil.fillMerchant(vo, operator.getLoginName());
        //检查合伙人
        if(StringUtils.isNotBlank(merchant.getAgentNo())){
            Agent upAgent = agentDao.getOne(Collections.singletonMap("agentNo",merchant.getAgentNo()));
            if(upAgent != null && upAgent.getAgentStatus() == AgentStatusEnum.RETREAT.getValue()){
                //已清退的 即使指定了 merchant不应该存
                merchant.setAgentNo(null);
                merchant.setAgentName(null);
            }
        }
        merchantDao.insert(merchant);
        log.info("[{}]==>保存商户信息表", logFlag);

        // 保存商户销售表
        MerchantSaler merchantSaler = BuildVoUtil.fillMerchantSaler(operator, vo.getMchNo(),operator.getLoginName());
        merchantSalerDao.insert(merchantSaler);
        log.info("[{}]==>保存商户销售表", logFlag);

        // 保存合作信息
        MerchantEmployerCooperate cooperation = BuildVoUtil.fillEmployerDetail(vo, operator.getLoginName());
        employerDetailDao.insert(cooperation);
        log.info("[{}]==>保存合作信息表", logFlag);

        // 保存岗位信息
        if(CollectionUtils.isNotEmpty(vo.getPositionVoList())){
            List<MerchantEmployerPosition> positions = BuildVoUtil.fillEmployerPosition(vo.getPositionVoList(), operator.getLoginName(),vo.getMchNo());
            employerPositionDao.insert(positions);
            log.info("[{}]==>保存岗位信息表", logFlag);
        }

        //生成流程业务key
        Map<String,String> flowbusinessMap  = new HashMap<>();
        //保存报价单信息
        if(CollectionUtils.isNotEmpty(vo.getQuoteVoList())){
            List<MerchantEmployerQuote> quoteList = new ArrayList<>();
            vo.getQuoteVoList().forEach(x -> {
                String no = StringUtil.isEmpty(flowbusinessMap.get(x.getMainstayMchNo())) ? generateBillNo() : flowbusinessMap.get(x.getMainstayMchNo());
                MerchantEmployerQuote quote = BuildVoUtil.fillQuote(x,operator.getRealName(),vo.getMchNo(),no);
                flowbusinessMap.put(x.getMainstayMchNo(),no);
                quoteList.add(quote);
            });
            employerQuoteDao.insert(quoteList);
            log.info("[{}]==>保存报价单信息",logFlag);
        }

        // 保存
        if(CollectionUtils.isNotEmpty(vo.getCompanyLeafletFileUrls()) || CollectionUtils.isNotEmpty(vo.getSupplementFileUrls())){
            List<MerchantFile> files = BuildVoUtil.fillMerchantFile(vo, operator);
            merchantFileDao.insert(files);
            log.info("[{}]==>保存商户文件", logFlag);
        }

        // 创建管理员
        log.info("[{}]==>创建管理员", logFlag);
        EmployerStaffVO employerStaffVo = BuildVoUtil.fillEmployerStaffVO(vo, operator.getLoginName());
        employerStaffFacade.create(employerStaffVo);

        //更新合伙人的商户数 起始代码检验合伙人已确定合伙人存在
        if(StringUtils.isNotBlank(merchant.getAgentNo())){
            agentDao.updateMerNum(merchant.getAgentNo());
        }

        vo.setSalerName(operator.getRealName());
        //根据报价单构建流程参数
        List<MerchantEmployerInsertVo> employerInsertVoList = buildFlowParam(vo);
        //提交代征主体审核
        notifyFacade.sendOne(MessageMsgDest.TOPIC_CREATE_RELATION_FLOW, NotifyTypeEnum.CREATE_RELATION_FLOW.getValue(),MessageMsgDest.TAG_CREATE_RELATION_FLOW,JsonUtil.toString(employerInsertVoList));
    }

    @Transactional(rollbackFor = Exception.class)
    public void applyMerchant(MerchantEmployerAddVo vo) {
        String logFlag = vo.getMchNo() + "-" + vo.getMchName();
        log.info("[{}]==>创建用工企业开始:{}", logFlag, JsonUtil.toString(vo));

        // 销售员
        PmsOperator operator = operatorFacade.getOperatorById(vo.getSalerId());

        if (Objects.isNull(operator)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("销售不存在");
        }

        // 保存商户信息表
        Merchant merchant = BuildVoUtil.fillMerchant(vo);
        //检查合伙人
        if(StringUtils.isNotBlank(merchant.getAgentNo())){
            Agent upAgent = agentDao.getOne(Collections.singletonMap("agentNo",merchant.getAgentNo()));
            if(upAgent != null && upAgent.getAgentStatus() == AgentStatusEnum.RETREAT.getValue()){
                //已清退的 即使指定了 merchant不应该存
                merchant.setAgentNo(null);
                merchant.setAgentName(null);
            }
        }
        merchantDao.insert(merchant);

        log.info("[{}]==>保存商户信息表", logFlag);

        // 保存商户销售表
        MerchantSaler merchantSaler = BuildVoUtil.fillMerchantSaler(operator, vo.getMchNo(), vo.getPmsOperator().getLoginName());
        merchantSalerDao.insert(merchantSaler);
        log.info("[{}]==>保存商户销售表", logFlag);

        // 保存合作信息
        MerchantEmployerCooperate cooperation = BuildVoUtil.fillEmployerDetail(vo);
        employerDetailDao.insert(cooperation);
        log.info("[{}]==>保存合作信息表", logFlag);

        // 保存岗位信息
        if(CollectionUtils.isNotEmpty(vo.getPositionVoList())){
            List<MerchantEmployerPosition> positions = BuildVoUtil.fillEmployerPosition(vo.getPositionVoList(),vo.getPmsOperator().getLoginName(),vo.getMchNo());
            employerPositionDao.insert(positions);
            log.info("[{}]==>保存岗位信息表", logFlag);
        }



        // 保存
        if(CollectionUtils.isNotEmpty(vo.getCompanyLeafletFileUrls()) || CollectionUtils.isNotEmpty(vo.getSupplementFileUrls())){
            List<MerchantFile> files = BuildVoUtil.fillMerchantFile(vo);
            merchantFileDao.insert(files);
            log.info("[{}]==>保存商户文件", logFlag);
        }

        // 创建管理员
        log.info("[{}]==>创建管理员", logFlag);
        EmployerStaffVO employerStaffVo = BuildVoUtil.fillEmployerStaffVO(vo);
        employerStaffFacade.create(employerStaffVo);

        //更新合伙人的商户数 起始代码检验合伙人已确定合伙人存在
        if(StringUtils.isNotBlank(merchant.getAgentNo())){
            agentDao.updateMerNum(merchant.getAgentNo());
        }

        //发送邮件通知商户
        EmailParamDto emailParamDto = new EmailParamDto();
        emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        emailParamDto.setTo(merchant.getContactEmail());
        emailParamDto.setSubject("【智享汇】智享汇综合服务平台帐号开通");
        emailParamDto.setTpl(EmailTemplateEnum.SUCCESS_CREATE_MERCHANT.getName());
        Map<String,Object> map = new HashMap<>();
        map.put("mchType",MerchantTypeEnum.EMPLOYER.getDesc());
        map.put("platformUrl",merchantLink);

        map.put("merchantName",merchant.getMchName());
        map.put("username",merchant.getContactName());
        map.put("phone",merchant.getContactPhone());
        emailParamDto.setTplParam(map);
        emailParamDto.setHtmlFormat(true);
        //异步发送邮件
        emailFacade.sendAsync(emailParamDto);

        //todo 创建系统信息和修改记录

        //机器人通知
        StringBuffer sb = new StringBuffer("#### 商户创建提醒\\n ");
        sb.append("\\n > 商户名称：").append(vo.getMchName())
                .append("\\n > 商户补充说明：").append(vo.getRemark() == null ? "" : vo.getRemark())
                .append("\\n > 发起人：").append(vo.getPmsOperator().getRealName())
                .append("\\n > 创建时间：").append(DateUtil.formatDate(merchant.getCreateTime()));

        MarkDownMsg markDownMsg = new MarkDownMsg();
        markDownMsg.setUnikey(IdUtil.fastUUID());
        markDownMsg.setRobotType(RobotTypeEnum.MERCHANT_ADD_ROBOT.getType());
        markDownMsg.setContent(sb.toString());
        robotFacade.pushMarkDownAsync(markDownMsg);
    }

    private List<MerchantEmployerInsertVo> buildFlowParam(MerchantEmployerInsertVo vo) {
        Map<String,MerchantEmployerInsertVo> map = new HashMap<>();
        List<MerchantEmployerInsertVo> list = vo.getQuoteVoList().stream().map(x->{
            MerchantEmployerInsertVo merchantEmployerInsertVo = map.get(x.getMainstayMchNo());
            if (merchantEmployerInsertVo == null){
                merchantEmployerInsertVo = new MerchantEmployerInsertVo();
                BeanUtils.copyProperties(vo,merchantEmployerInsertVo);
                merchantEmployerInsertVo.setQuoteVoList(new ArrayList<MerchantEmployerQuoteVo>(){{add(x);}});
                map.put(x.getMainstayMchNo(),merchantEmployerInsertVo);
                return merchantEmployerInsertVo;
            }else{
                merchantEmployerInsertVo.getQuoteVoList().add(x);
                return null;
            }
        }).filter(x -> x != null).collect(Collectors.toList());
        return list;
    }

    /**
     * 用工企业主体信息认证
     * @param authVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void employerMainAuth(MerchantEmployerMainAuthVo authVo) {
        log.info("{}==>主体信息认证", JsonUtil.toString(authVo));
        // 商户信息表更新
        Merchant mch = merchantDao.getByMchNo(authVo.getMchNo());
        BuildVoUtil.fillMerchant(authVo, mch);
        merchantDao.update(mch);
        log.info("{}==>商户信息表更新", authVo.getMchNo());

        // 保存银行卡信息
        MerchantBankAccount account = BuildVoUtil.fillMerchantBankAccount(authVo, mch);
        accountDao.insert(account);
        log.info("{}==>保存银行卡信息", authVo.getMchNo());

        // 保存主体信息
        MerchantEmployerMain main = BuildVoUtil.fillMerchantEmployerMain(authVo, mch);
        mainDao.insert(main);
        log.info("{}==>保存主体信息", authVo.getMchNo());

        // 保存文件信息
        List<MerchantFile> fileList = BuildVoUtil.fillMerchantFile(authVo, mch.getMchName());
        merchantFileDao.insert(fileList);
        log.info("{}==>保存文件信息", authVo.getMchNo());

        MerchantInvoiceInfo invoiceInfo = BuildVoUtil.fillMerchantInvoiceInfo(mch, main, account,authVo);
        invoiceInfoDao.insert(invoiceInfo);


        //保存企业人员
        authVo.getPersonnels().forEach(personnel->{
            personnel.setMchNo(authVo.getMchNo());
            personnel.setMchName(authVo.getMchName());
            personnel.setUpdator(authVo.getLegalPersonName());
        });
        List<EnterprisePersonnelDto> personnels = merchantEnterprisePersonnelBiz.batchInsert(authVo.getPersonnels());

        notifyFacade.sendOne(MessageMsgDest.TOPIC_RISK_NAME_INSERT, NotifyTypeEnum.CREATE_RELATION_FLOW.getValue()
                ,MessageMsgDest.TAG_RISK_NAME_INSERT, JSONArray.toJSONString(personnels));

        //不需要自动报备
        //autoReport(mch, authVo);


    }

    /**
     * 获取详情
     * @param param
     * @return
     */
    public MerchantEmployerDetailVo getInfoVO(Map<String,Object> param, List<Long> salerIds) {
        String mchNo = (String) param.get("mchNo");
        if(StringUtil.isEmpty(mchNo)){
            return null;
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",mchNo);
        paramMap.put("salerIds", salerIds);
        MerchantSaler merchantSaler = merchantSalerDao.getOne(paramMap);
        if(Objects.isNull(merchantSaler)){
            return null;
        }
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        MerchantEmployerCooperate detail = employerDetailDao.getByMchNo(mchNo);
        List<MerchantEmployerPosition> positions = employerPositionDao.listByMchNo(mchNo);
        param.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        List<MerchantEmployerQuote> quotes = employerQuoteDao.listBy("getQuoteList",param);
        MerchantEmployerMain main = mainDao.getByMchNo(mchNo);
        MerchantBankAccount account = accountDao.getByMchNo(mchNo);
        List<MerchantFile> fileList = merchantFileDao.listByMchNoAndFileType(mchNo, null);
        MerchantInvoiceInfo invoiceInfo = invoiceInfoDao.getByMchNo(mchNo);

        return BuildVoUtil.fillMerchantEmployerDetailVO(merchant,
                merchantSaler,
                detail,
                main,
                account,
                positions,
                quotes,
                fileList,
                invoiceInfo);
    }

    public void mainAuthDelete(String mchNo) {
        log.info("{} 商户主体认证审核主动撤回",mchNo);
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        if(!Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.SUCCESS.getValue())){
            merchant.setAuthStatus(AuthStatusEnum.UN_AUTH.getValue());
            merchant.setMchStatus(MchStatusEnum.CREATE.getValue());
        }
        merchantDao.update(merchant);
    }

    public void mainAuthDisAgree(String mchNo) {
        log.info("{} 商户主体认证审核流程驳回给发起人，认证状态改为认证失败，商户状态改为已创建",mchNo);
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        if(Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.APPROVAL.getValue())){
            merchant.setAuthStatus(AuthStatusEnum.FAIL.getValue());
            merchant.setMchStatus(MchStatusEnum.CREATE.getValue());
        }
        merchantDao.update(merchant);
    }

    public void mainAuthCancel(String mchNo) {
        log.info("{} 商户主体认证审核流程取消，认证状态改为未认证，商户状态改为已创建",mchNo);
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        if(Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.APPROVAL.getValue()) ||
        Objects.equals(merchant.getAuthStatus(),AuthStatusEnum.FAIL.getValue())){
            merchant.setAuthStatus(AuthStatusEnum.UN_AUTH.getValue());
            merchant.setMchStatus(MchStatusEnum.CREATE.getValue());
        }
        merchantDao.update(merchant);
    }

    public void mainAuthContinue(String mchNo) {
        log.info("{} 商户主体认证审核流程重新提交，认证状态改为创建审核中，商户状态改为已创建",mchNo);
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        if(Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.FAIL.getValue())){
            merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
            merchant.setMchStatus(MchStatusEnum.CREATE.getValue());
        }
        merchantDao.update(merchant);
    }

    public List<MerchantEmployerDetailVo> getInfoVoList(Map<String,Object> paramMap) {
        List<ExportMerchantInfo> exportMerchantInfoList = merchantDao.listBy("listMerchantAllMessage",paramMap);
        return exportMerchantInfoList.stream().map(x-> BuildVoUtil.fillMerchantEmployerDetailVO(x.getMerchant(),
                x.getSaler(),
                x.getCooperate(),
                x.getMain(),
                x.getAccount(),
                x.getPosition(),
                x.getMerchantEmployerQuoteList(),
                x.getMerchantFileList(),
                x.getInvoice())).collect(Collectors.toList());
    }

    private void autoReport(Merchant merchant, MerchantEmployerMainAuthVo mainAuthVo) {

        final Map<String, List<Map<String,Object>>> accounts = mainAuthVo.getAccounts();

        //检查代征关系是否建立
        accounts.forEach((mainstayNo, value) -> {
            // 查询代征关系表
            final EmployerMainstayRelation emRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(merchant.getMchNo(), mainstayNo);
            if (emRelation == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征关系未建立:代征主体编号["+mainstayNo+"]");
            }
        });

        accounts.forEach((mainstayNo,value)->{

            for (Map<String, Object> account : value) {
                Integer channeltype = (Integer) account.get("channelType");
                String channelName = (String) account.get("channelName");
                String payChannelNo = (String) account.get("payChannelNo");
                String payChannelName = (String) account.get("payChannelName");

                final Merchant mainstay = merchantDao.getByMchNo(mainstayNo);

                ReportEntity reportEntity = new ReportEntity();
                reportEntity.setEmployerNo(merchant.getMchNo());
                reportEntity.setMerchantType(merchant.getMerchantType());
                reportEntity.setReporter(mainAuthVo.getOperatorLoginName());
                reportEntity.setEmployerName(merchant.getMchName());
                reportEntity.setMainstayNo(mainstayNo);
                reportEntity.setMainstayName(mainstay.getMchName());
                reportEntity.setChannelType(channeltype);
                reportEntity.setPayChannelName(payChannelName);
                reportEntity.setPayChannelNo(payChannelNo);
                notifyFacade.sendOne(MessageMsgDest.TOPIC_AUTO_REPORT, mainAuthVo.getMchNo(), mainAuthVo.getContactPhone(), NotifyTypeEnum.AUTO_REPORT_AFTER_AUTH.getValue(), MessageMsgDest.TAG_AUTO_REPORT, JSON.toJSONString(reportEntity), MsgDelayLevelEnum.S_5.getValue());

            }
        });


    }

    public Map<String, Object> getCooperateInfo(String mchNo) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        List<MerchantFile> merchantFileList = merchantFileDao.listByMchNoAndFileType(mchNo,null);

        Map<String,Object> maps = new HashMap<>();
        //获取商户合作信息
        if (merchant.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            MerchantEmployerCooperate merchantEmployerCooperate = employerDetailDao.getByMchNo(mchNo);
            maps = BeanUtil.toMap(merchantEmployerCooperate);
            maps.put("businessType",merchant.getBusinessType().split(","));
            //处理serviceType
            if (merchantEmployerCooperate.getServiceType() != null){
                maps.put("serviceType",merchantEmployerCooperate.getServiceType().split(","));
            }

            //获取商户岗位信息
            List<MerchantEmployerPosition> merchantEmployerPositionList = employerPositionDao.listByMchNo(mchNo);
            maps.put("position",merchantEmployerPositionList);
            //对外宣传资料、补充信息
            List<String> companyLeafletFileUrls = new ArrayList<>();
            List<String> supplementFileUrls = new ArrayList<>();

            merchantFileList.stream().forEach(x->{
                if (x.getFileType() == MerchantFileTypeEnum.COMPANY_LEAFLET.getValue()){
                    companyLeafletFileUrls.add(x.getFileUrl());
                }else if (x.getFileType() == MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue()){
                    supplementFileUrls.add(x.getFileUrl());
                }
            });
            maps.put("companyLeafletFileUrls",companyLeafletFileUrls);
            maps.put("supplementFileUrls",supplementFileUrls);
        }else{
            for (MerchantFile merchantFile : merchantFileList) {
                if (merchantFile.getFileType() == MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_B.getValue()){
                    maps.put("agreementTemplate2BFileUrl",merchantFile.getFileUrl());
                }else if (merchantFile.getFileType() == MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_C.getValue()){
                    maps.put("agreementTemplate2CFileUrl",merchantFile.getFileUrl());
                }else if (merchantFile.getFileType() == MerchantFileTypeEnum.ENTRUST_AGREEMENT.getValue()){
                    maps.put("entrustAgreementFileUrl",merchantFile.getFileUrl());
                }
            }
        }

        return maps;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(MerchantFlowVo merchantFlowVo) {
        Merchant merchant = merchantDao.getByMchNo(merchantFlowVo.getMchNo());
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        //已激活状态不需要改回去已创建状态
        if (merchant.getMchStatus() == MchStatusEnum.ACTIVE.getValue() && merchantFlowVo.getMchStatus() == MchStatusEnum.CREATE.getValue()){
            return ;
        }

        merchant.setMchStatus(merchantFlowVo.getMchStatus());
        merchant.setUpdator(merchantFlowVo.getUpdator());
        merchantDao.update(merchant);
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeSalerAndAgent(MerchantFlowVo merchantFlowVo) {
        Merchant merchant = merchantDao.getByMchNo(merchantFlowVo.getMchNo());
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        //只有用工企业才有合伙人
        if (merchant.getMerchantType().intValue() == MerchantTypeEnum.EMPLOYER.getValue()){
            String oldAgentNo = merchant.getAgentNo();
            Agent agent;
            if(StringUtils.isNotBlank(merchantFlowVo.getAgentNo())){
                //设置了合伙人
                agent = agentDao.getByAgentNo(merchantFlowVo.getAgentNo());
                LimitUtil.notEmpty(agent,"指定合伙人不存在");
                if(Objects.equals(agent.getAgentStatus(), AgentStatusEnum.RETREAT.getValue())){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("指定合伙人已清退，不允许操作");
                }
                merchant.setAgentNo(agent.getAgentNo());
                merchant.setAgentName(agent.getAgentName());
            }else {
                merchant.setAgentNo(null);
                merchant.setAgentName(null);
            }
            merchant.setUpdator(merchantFlowVo.getUpdator());
            //保存合伙人
            merchantDao.update(merchant);

            //更新合伙人表商户关系数量
            if (StringUtil.isNotEmpty(merchant.getAgentNo())){
                agentDao.updateMerNum(merchant.getAgentNo());
            }


            //更新合伙人表商户关系数量
            if (StringUtil.isNotEmpty(oldAgentNo)){
                agentDao.updateMerNum(oldAgentNo);
            }
        }

        // 更新商户销售
        MerchantSaler merchantSaler = merchantSalerDao.getByMchNo(merchantFlowVo.getMchNo());
        if(!Objects.equals(merchantSaler.getSalerId(), merchantFlowVo.getSalerId())){
            PmsOperator operator = operatorFacade.getOperatorById(merchantFlowVo.getSalerId());
            merchantSaler.setUpdateTime(new Date());
            merchantSaler.setUpdator(merchantFlowVo.getUpdator());
            merchantSaler.setSalerId(merchantFlowVo.getSalerId());
            merchantSaler.setSalerName(operator.getRealName());
            merchantSaler.setSaleDepartmentId(operator.getDepartmentId());
            merchantSaler.setSaleDepartmentName(operator.getDepartmentName());
            merchantSalerDao.update(merchantSaler);
            log.info("{}==>更新商户销售信息", merchantFlowVo.getMchNo());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void changeAccount(MerchantFlowVo merchantFlowVo) {
        //修改账户信息
        MerchantBankAccount merchantBankAccount = accountDao.getByMchNo(merchantFlowVo.getMchNo());
        if (merchantBankAccount == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账户信息为空");
        }

        BeanUtil.copyProperties(merchantFlowVo.getBankAccount(),merchantBankAccount);
        merchantBankAccount.setUpdator(merchantFlowVo.getUpdator());
        accountDao.update(merchantBankAccount);


    }

    public Map<String, Object> getCooperateInfoWithSupplier(String mainstayNo, String mchNo) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        List<MerchantFile> merchantFileList = merchantFileDao.listByMchNoAndFileType(mchNo,null);

        Map<String,Object> maps = new HashMap<>();
        //获取商户合作信息
        if (merchant.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            MerchantEmployerCooperate merchantEmployerCooperate = employerDetailDao.getByMchNo(mchNo);
            maps = BeanUtil.toMap(merchantEmployerCooperate);
            //获取商户岗位信息
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("mchNo",mchNo);
            paramMap.put("mainstayNo",mainstayNo);
            List<MerchantEmployerPosition> merchantEmployerPositionList = employerPositionDao.listByMchNoWithQuote(paramMap);
            maps.put("position",merchantEmployerPositionList);
            //对外宣传资料、补充信息
            List<String> companyLeafletFileUrls = new ArrayList<>();
            List<String> supplementFileUrls = new ArrayList<>();

            merchantFileList.stream().forEach(x->{
                if (x.getFileType() == MerchantFileTypeEnum.COMPANY_LEAFLET.getValue()){
                    companyLeafletFileUrls.add(x.getFileUrl());
                }else if (x.getFileType() == MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue()){
                    supplementFileUrls.add(x.getFileUrl());
                }
            });
            maps.put("companyLeafletFileUrls",companyLeafletFileUrls);
            maps.put("supplementFileUrls",supplementFileUrls);
        }else{
            for (MerchantFile merchantFile : merchantFileList) {
                if (merchantFile.getFileType() == MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_B.getValue()){
                    maps.put("agreementTemplate2BFileUrl",merchantFile.getFileUrl());
                }else if (merchantFile.getFileType() == MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_C.getValue()){
                    maps.put("agreementTemplate2CFileUrl",merchantFile.getFileUrl());
                }else if (merchantFile.getFileType() == MerchantFileTypeEnum.ENTRUST_AGREEMENT.getValue()){
                    maps.put("entrustAgreementFileUrl",merchantFile.getFileUrl());
                }
            }
        }
        return maps;
    }

    @Transactional(rollbackFor = Exception.class)
    public void applyMerchantAnon(MerchantEmployerAddVo merchantEmployerAddVo,String mainstayNo) {
        applyMerchant(merchantEmployerAddVo);
        //如果是就发消息建立代征关系
        if (StringUtils.isNotBlank(mainstayNo)){
            Merchant mainstay = merchantDao.getByMchNo(mainstayNo);
            if (mainstay != null){
                createRelation(merchantEmployerAddVo,mainstay);
            }
        }
    }

    private void createRelation(MerchantEmployerAddVo merchantEmployerAddVo,Merchant mainstay) {
        EmployerMainstayRelation employerMainstayRelation = new EmployerMainstayRelation();
        employerMainstayRelation.setEmployerNo(merchantEmployerAddVo.getMchNo());
        employerMainstayRelation.setEmployerName(merchantEmployerAddVo.getMchName());
        employerMainstayRelation.setMainstayNo(mainstay.getMchNo());
        employerMainstayRelation.setMainstayName(mainstay.getMchName());
        employerMainstayRelation.setStatus(OpenOffEnum.OPEN.getValue());
        employerMainstayRelation.setCreateOperator(merchantEmployerAddVo.getPmsOperator().getRealName());
        employerMainstayRelation.setUpdateTime(new Date());
        employerMainstayRelation.setUpdateOperator(merchantEmployerAddVo.getPmsOperator().getRealName());
        employerMainstayRelation.setAccountStatus(MerchantChannelTypeEnum.SUCCESS.getType());
        employerMainstayRelation.setHasExternalSystem(Boolean.FALSE);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_CREATE_RELATION,UUIDUitl.generateString(10),merchantEmployerAddVo.getMchNo(),
                NotifyTypeEnum.CREATE_RELATION_ANNO.getValue(),MessageMsgDest.TAG_CREATE_RELATION,JsonUtil.toString(employerMainstayRelation));
    }

    @Transactional(rollbackFor = Exception.class)
    public void applyMerchantV2(MerchantEmployerAddVoV2 v2) {
        MerchantEmployerAddVo merchantEmployerAddVo = JsonUtil.toBean(JsonUtil.toString(v2),MerchantEmployerAddVo.class);
        applyMerchant(merchantEmployerAddVo);
    }

}
