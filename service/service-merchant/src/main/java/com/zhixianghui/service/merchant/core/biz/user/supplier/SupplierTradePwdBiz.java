package com.zhixianghui.service.merchant.core.biz.user.supplier;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd;
import com.zhixianghui.service.merchant.core.dao.user.supplier.SupplierTradePwdDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.Date;

/**
* 供应商支付密码表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-14
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierTradePwdBiz {

    private final SupplierTradePwdDao supplierTradePwdDao;

    /**
     * 根据商户编号查询
     *
     * @param mchNo
     */
    public SupplierTradePwd getByMchNo(String mchNo) {
        return supplierTradePwdDao.getByMchNo(mchNo);
    }

    /**
     * 创建
     *
     * @param tradePwd
     */
    public void create(SupplierTradePwd tradePwd) throws BizException {
        if (tradePwd.getCreateTime() == null) {
            tradePwd.setCreateTime(new Date());
        }
        try {
            supplierTradePwdDao.insert(tradePwd);
        } catch (DuplicateKeyException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付密码已存在");
        }
    }

    /**
     * 更新
     *
     * @param tradePwd
     */
    public void update(SupplierTradePwd tradePwd) {
        supplierTradePwdDao.update(tradePwd);
    }
}