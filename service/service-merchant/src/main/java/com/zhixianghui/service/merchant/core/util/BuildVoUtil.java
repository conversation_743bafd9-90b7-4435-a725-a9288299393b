package com.zhixianghui.service.merchant.core.util;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.agent.AgentTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.DealStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.AgentBankAccount;
import com.zhixianghui.facade.merchant.entity.AgentCredential;
import com.zhixianghui.facade.merchant.entity.AgentEmployerMain;
import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import com.zhixianghui.facade.merchant.entity.AgentSaler;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuotePosition;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.entity.MerchantZftQuote;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.QuoteAuditEnum;
import com.zhixianghui.facade.merchant.vo.EmployerCooperateUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerAddVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerDetailVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerInsertVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerMainAuthVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerQuoteVo;
import com.zhixianghui.facade.merchant.vo.MerchantPositionVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentVo;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/17
 **/
public class BuildVoUtil {

    public static Merchant fillMerchant(MerchantEmployerInsertVo vo, String loginName) {
        Merchant merchant = new Merchant();
        merchant.setUpdateTime(new Date());
        merchant.setUpdator(loginName);
        merchant.setMchName(vo.getMchName());
        merchant.setMchNo(vo.getMchNo());
        merchant.setMchStatus(MchStatusEnum.CREATE.getValue());
        merchant.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        merchant.setAuthStatus(AuthStatusEnum.UN_AUTH.getValue());
        merchant.setAgentNo(vo.getAgentNo());
        merchant.setAgentName(vo.getAgentName());
        merchant.setRemark(vo.getRemark());
        merchant.setVersion(0);
        merchant.setCreateTime(new Date());
        merchant.setContactPhone(vo.getContactPhone());
        merchant.setContactName(vo.getContactName());
        return merchant;
    }

    public static Merchant fillMerchant(MerchantEmployerAddVo vo) {
        Merchant merchant = new Merchant();
        merchant.setUpdateTime(new Date());
        merchant.setUpdator(vo.getPmsOperator().getLoginName());
        merchant.setMchName(vo.getMchName());
        merchant.setMchNo(vo.getMchNo());
        merchant.setMchStatus(MchStatusEnum.CREATE.getValue());
        merchant.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        merchant.setAuthStatus(AuthStatusEnum.UN_AUTH.getValue());
        merchant.setDealStatus(DealStatusEnum.UNDEAL.getValue());
        merchant.setAgentNo(vo.getAgentNo());
        merchant.setAgentName(vo.getAgentName());
        merchant.setRemark(vo.getRemark());
        merchant.setVersion(0);
        merchant.setCreateTime(new Date());
        merchant.setContactPhone(vo.getContactPhone());
        merchant.setContactName(vo.getContactName());
        merchant.setFounder(vo.getPmsOperator().getRealName());
        merchant.setContactEmail(vo.getContactEmail());
        merchant.setBranchName(vo.getBranchName());
        merchant.setBusinessType(String.join(",",vo.getBusinessType()));
        return merchant;
    }

    public static MerchantSaler fillMerchantSaler(PmsOperator operator, String mchNo,String updateBy) {
        MerchantSaler saler = new MerchantSaler();
        saler.setUpdateTime(new Date());
        saler.setUpdator(operator.getLoginName());
        saler.setMchNo(mchNo);
        saler.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        saler.setSalerId(operator.getId());
        saler.setSalerName(operator.getRealName());
        saler.setSaleDepartmentId(operator.getDepartmentId());
        saler.setSaleDepartmentName(operator.getDepartmentName());
        saler.setVersion(0);
        saler.setCreateTime(new Date());

        return saler;
    }

    public static MerchantEmployerCooperate fillEmployerDetail(MerchantEmployerInsertVo vo, String loginName) {
        MerchantEmployerCooperate detail = new MerchantEmployerCooperate();
        detail.setUpdateTime(new Date());
        detail.setUpdator(loginName);
        detail.setMchNo(vo.getMchNo());
        detail.setIndustryTypeCode(vo.getIndustryTypeCode());
        detail.setIndustryTypeName(vo.getIndustryTypeName());
        detail.setWorkerNum(vo.getWorkerNum());
        detail.setSignRateLevel(vo.getSignRateLevel());
        detail.setWorkerMonthIncomeRate(vo.getWorkerMonthIncomeRate());
        detail.setMonthMoneySlip(vo.getMonthMoneySlip());
        detail.setProvideIncomeDetailType(vo.getProvideIncomeDetailType());
        detail.setCompanyWebsite(vo.getCompanyWebsite());
        detail.setBizPlatformName(vo.getBizPlatformName());
        detail.setVersion(0);
        detail.setCreateTime(new Date());

        return detail;
    }

    public static MerchantEmployerCooperate fillEmployerDetail(MerchantEmployerAddVo vo) {
        MerchantEmployerCooperate detail = new MerchantEmployerCooperate();
        detail.setUpdateTime(new Date());
        detail.setUpdator(vo.getPmsOperator().getLoginName());
        detail.setMchNo(vo.getMchNo());
        detail.setIndustryTypeCode(vo.getIndustryTypeCode());
        detail.setIndustryTypeName(vo.getIndustryTypeName());
        detail.setWorkerNum(vo.getWorkerNum());
        detail.setSignRateLevel(vo.getSignRateLevel());
        detail.setWorkerMonthIncomeRate(vo.getWorkerMonthIncomeRate());
        detail.setMonthMoneySlip(vo.getMonthMoneySlip());
        detail.setProvideIncomeDetailType(vo.getProvideIncomeDetailType());
        detail.setCompanyWebsite(vo.getCompanyWebsite());
        detail.setBizPlatformName(vo.getBizPlatformName());
        detail.setVersion(0);
        detail.setCreateTime(new Date());
        detail.setProfessionType(vo.getProfessionType());
        detail.setProfessionName(vo.getProfessionName());
        detail.setQualificationCode(vo.getQualificationCode());
        if (vo.getQualificationUrl() != null){
            detail.setQualificationUrl(String.join(",",vo.getQualificationUrl()));
        }
        if (vo.getServiceType() != null){
            detail.setServiceType(String.join(",",vo.getServiceType()));
        }
        detail.setAlipayAppName(vo.getAlipayAppName());
        detail.setAppName(vo.getAppName());
        detail.setWapName(vo.getWapName());
        detail.setWapSite(vo.getWapSite());
        detail.setPcName(vo.getPcName());
        detail.setPcSite(vo.getPcSite());
        return detail;
    }

    public static List<MerchantEmployerPosition> fillEmployerPosition(List<MerchantPositionVo> voList, String loginName,String mchNo) {
        List<MerchantEmployerPosition> positionList = new ArrayList<>();
        voList.forEach(x -> {
            MerchantEmployerPosition position = fillMerchantEmployerPosition(x, loginName, mchNo);
            positionList.add(position);
        });
        return positionList;
    }

    private static MerchantEmployerPosition fillMerchantEmployerPosition(MerchantPositionVo positionVo, String loginName, String mchNo) {
        MerchantEmployerPosition position = new MerchantEmployerPosition();
        position.setUpdateTime(new Date());
        position.setUpdator(loginName);
        position.setMchNo(mchNo);
        position.setWorkplaceCode(positionVo.getWorkplaceCode());
        position.setWorkCategoryCode(positionVo.getWorkCategoryCode());
        position.setWorkCategoryName(positionVo.getWorkCategoryName());
        position.setBusinessDesc(positionVo.getBusinessDesc());
        position.setServiceDesc(positionVo.getServiceDesc());
        position.setChargeRuleDesc(positionVo.getChargeRuleDesc());
        position.setVersion(0);
        position.setCreateTime(new Date());

        position.getJsonEntity().setInvoiceCategoryList(positionVo.getInvoiceCategoryList());
        position.setOriginId(positionVo.getId());
        return position;
    }

    public static List<MerchantEmployerQuoteRate> fillQuoteRateList(MerchantFlowVo merchantFlowVo, MerchantEmployerQuote merchantEmployerQuote) {
        List<MerchantEmployerQuoteRate> merchantEmployerQuoteRateList = new ArrayList<>();
        merchantFlowVo.getQuoteRateList().forEach(x->{
            MerchantEmployerQuoteRate merchantEmployerQuoteRate = new MerchantEmployerQuoteRate();
            BeanUtil.copyProperties(x,merchantEmployerQuoteRate);
            merchantEmployerQuoteRate.setQuoteId(merchantEmployerQuote.getId());
            merchantEmployerQuoteRate.setCreateTime(new Date());
            merchantEmployerQuoteRate.setRuleParam(JSON.toJSONString(x.getRuleParam()));
            merchantEmployerQuoteRateList.add(merchantEmployerQuoteRate);
        });
        return merchantEmployerQuoteRateList;
    }

    public static MerchantEmployerQuote fillEmployerQuote(MerchantFlowVo merchantFlowVo, Vendor vendor,Integer status){
        MerchantEmployerQuote merchantEmployerQuote = new MerchantEmployerQuote();
        merchantEmployerQuote.setMchNo(merchantFlowVo.getMchNo());
        //注意报价单的代征主体是mainstay,fee的代征主体是vendor
        if (vendor != null){
            merchantEmployerQuote.setMainstayMchName(vendor.getVendorName());
            merchantEmployerQuote.setMainstayMchNo(vendor.getVendorNo());
            merchantEmployerQuote.setSupplierNo(vendor.getSupplierNo());
            merchantEmployerQuote.setSupplierName(vendor.getSupplierName());
        }
        merchantEmployerQuote.setProductNo(merchantFlowVo.getProductNo());
        merchantEmployerQuote.setProductName(merchantFlowVo.getProductName());
        merchantEmployerQuote.setFlowBusinessKey(Long.toString(merchantFlowVo.getFlowId()));
        merchantEmployerQuote.setStatus(status);
        merchantEmployerQuote.setCreateTime(new Date());
        merchantEmployerQuote.setUpdateTime(new Date());
        merchantEmployerQuote.setUpdator(merchantFlowVo.getUpdator());
        return merchantEmployerQuote;
    }

    public static List<MerchantEmployerQuote> fillEmployerQuote(MerchantEmployerInsertVo vo, String loginName) {
        List<MerchantEmployerQuote> quoteList = new ArrayList<>();
        vo.getQuoteVoList().forEach(x -> {
            MerchantEmployerQuote quote = fillMerchantEmployerQuote(x, loginName, vo.getMchNo());
            quoteList.add(quote);
        });
        return quoteList;
    }

    public static MerchantEmployerDetailVo fillMerchantEmployerDetailVO(Merchant merchant,
                                                                        MerchantSaler merchantSaler,
                                                                        MerchantEmployerCooperate cooperate,
                                                                        MerchantEmployerMain main,
                                                                        MerchantBankAccount account,
                                                                        List<MerchantEmployerPosition> positions,
                                                                        List<MerchantEmployerQuote> quotes,
                                                                        List<MerchantFile> fileList,
                                                                        MerchantInvoiceInfo invoiceInfo) {
        MerchantEmployerDetailVo infoVO = new MerchantEmployerDetailVo();
        if (merchantSaler != null){
            infoVO.setSalerId(merchantSaler.getSalerId());
        }
        infoVO.setMchNo(merchant.getMchNo());
        infoVO.setMchName(merchant.getMchName());
        infoVO.setMerchantType(merchant.getMerchantType());
        infoVO.setMchStatus(merchant.getMchStatus());
        infoVO.setAuthStatus(merchant.getAuthStatus());
        infoVO.setContactPhone(merchant.getContactPhone());
        infoVO.setContactName(merchant.getContactName());
        infoVO.setRemark(merchant.getRemark());
        infoVO.setContactEmail(merchant.getContactEmail());
        infoVO.setServicePhone(merchant.getServicePhone());
        infoVO.setBranchName(merchant.getBranchName());

        if(cooperate != null){
            infoVO.setWorkerNum(cooperate.getWorkerNum());
            infoVO.setSignRateLevel(cooperate.getSignRateLevel());
            infoVO.setWorkerMonthIncomeRate(cooperate.getWorkerMonthIncomeRate());
            infoVO.setMonthMoneySlip(cooperate.getMonthMoneySlip());
            infoVO.setProvideIncomeDetailType(cooperate.getProvideIncomeDetailType());
            infoVO.setCompanyWebsite(cooperate.getCompanyWebsite());
            infoVO.setBizPlatformName(cooperate.getBizPlatformName());
            infoVO.setIndustryTypeCode(cooperate.getIndustryTypeCode());
            infoVO.setIndustryTypeName(cooperate.getIndustryTypeName());
        }

        if(main != null){
            infoVO.setShortName(main.getShortName());
            infoVO.setTaxNo(main.getTaxNo());
            infoVO.setRegisterAddrProvince(main.getRegisterAddrProvince());
            infoVO.setRegisterAddrCity(main.getRegisterAddrCity());
            infoVO.setRegisterAddrTown(main.getRegisterAddrTown());
            infoVO.setRegisterAddrDetail(main.getRegisterAddrDetail());
            infoVO.setRegisterAmount(main.getRegisterAmount());
            infoVO.setManagementScope(main.getManagementScope());
            infoVO.setManagementTermBegin(main.getManagementTermBegin());
            infoVO.setManagementTermEnd(main.getManagementTermEnd());
            infoVO.setManagementValidityDateType(main.getManagementValidityDateType());
            infoVO.setCertificateType(main.getCertificateType());
            infoVO.setLegalPersonName(main.getLegalPersonName());
            infoVO.setCertificateNumber(main.getCertificateNumber());
            infoVO.setCertificateValidityDateType(main.getCertificateValidityDateType());
            infoVO.setCertificateTermBegin(main.getCertificateTermBegin());
            infoVO.setCertificateTermEnd(main.getCertificateTermEnd());
            infoVO.setManagementAddrProvince(main.getManagementAddrProvince());
            infoVO.setManagementAddrCity(main.getManagementAddrCity());
            infoVO.setManagementAddrTown(main.getManagementAddrTown());
            infoVO.setManagementAddrDetail(main.getManagementAddrDetail());
        }

        if(account != null){
            infoVO.setAccountNo(account.getAccountNo());
            infoVO.setBankName(account.getBankName());
            infoVO.setBankChannelNo(account.getBankChannelNo());
            infoVO.setAccountName(account.getAccountName());
        }

        if(CollectionUtils.isNotEmpty(positions)){
            positions.forEach(x -> {
                MerchantPositionVo vo = new MerchantPositionVo();
                vo.setWorkplaceCode(x.getWorkplaceCode());
                vo.setWorkCategoryCode(x.getWorkCategoryCode());
                vo.setWorkCategoryName(x.getWorkCategoryName());
                vo.setServiceDesc(x.getServiceDesc());
                vo.setChargeRuleDesc(x.getChargeRuleDesc());
                vo.setInvoiceCategoryList(x.getJsonEntity().getInvoiceCategoryList());
                infoVO.getInvoiceCategoryList().addAll(vo.getInvoiceCategoryList());
                infoVO.getPositionVoList().add(vo);
            });
        }

        if(CollectionUtils.isNotEmpty(quotes)){
            //报价单结构改变
            quotes.stream().filter(x->x.getStatus().intValue() == MerchantQuoteStatusEnum.ACTIVE.getValue()).forEach(x->{
                if (StringUtils.equals(x.getProductNo(), ProductNoEnum.CKH.getValue())){
                    MerchantEmployerQuoteVo quoteVo = new MerchantEmployerQuoteVo();
                    quoteVo.setId(x.getId());
                    quoteVo.setMainstayMchNo(x.getMainstayMchNo());
                    quoteVo.setMainstayMchName(x.getMainstayMchName());
                    quoteVo.setStatus(x.getStatus());
                    quoteVo.setProductName(x.getProductName());
                    quoteVo.setProductNo(x.getProductNo());
                    infoVO.getQuoteVoList().add(quoteVo);
                }else{
                    x.getQuoteRateList().forEach(k->{
                        MerchantEmployerQuoteVo quoteVo = new MerchantEmployerQuoteVo();
                        quoteVo.setId(x.getId());
                        quoteVo.setMainstayMchNo(x.getMainstayMchNo());
                        quoteVo.setMainstayMchName(x.getMainstayMchName());
                        quoteVo.setRate(k.getRate());
                        quoteVo.setFixedFee(k.getFixedFee());
                        quoteVo.setFormulaType(k.getFormulaType());
                        quoteVo.setStatus(x.getStatus());
                        quoteVo.setProductName(x.getProductName());
                        quoteVo.setProductNo(x.getProductNo());
                        infoVO.getQuoteVoList().add(quoteVo);
                    });
                }
            });
        }

        if(CollectionUtils.isNotEmpty(fileList)){
            fileList.forEach(x -> {
                if (Objects.equals(x.getFileType(), MerchantFileTypeEnum.COMPANY_LEAFLET.getValue())) {
                    infoVO.getCompanyLeafletFileUrls().add(x.getFileUrl());
                } else if(Objects.equals(x.getFileType(), MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue())){
                    infoVO.getSupplementFileUrls().add(x.getFileUrl());
                } else if (Objects.equals(x.getFileType(), MerchantFileTypeEnum.BUSINESS_LICENSE.getValue())) {
                    infoVO.setBusinessLicenseFileUrl(x.getFileUrl());
                } else if (Objects.equals(x.getFileType(), MerchantFileTypeEnum.ID_CARD_COPY.getValue())) {
                    infoVO.setIdCardCopyFileUrl(x.getFileUrl());
                } else if (Objects.equals(x.getFileType(), MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue())) {
                    infoVO.setIdCardEmblemFileUrl(x.getFileUrl());
                } else if (Objects.equals(x.getFileType(), MerchantFileTypeEnum.ID_CARD_HEAD.getValue())) {
                    infoVO.setIdCardHeadFileUrl(x.getFileUrl());
                } else if (Objects.equals(x.getFileType(), MerchantFileTypeEnum.DOOR_PHOTO.getValue())) {
                    infoVO.setDoorPhotoFileUrl(x.getFileUrl());
                } else if (Objects.equals(x.getFileType(), MerchantFileTypeEnum.WORK_INDOOR.getValue())) {
                    infoVO.setWorkIndoorFileUrl(x.getFileUrl());
                } else if (Objects.equals(x.getFileType(), MerchantFileTypeEnum.RECEPTION.getValue())) {
                    infoVO.setReceptionFileUrl(x.getFileUrl());
                }
            });
        }

        if (invoiceInfo != null){
            infoVO.setInvoiceMchName(invoiceInfo.getMchName());
            infoVO.setTaxPayerType(invoiceInfo.getTaxPayerType());
            infoVO.setInvoiceTaxNo(invoiceInfo.getTaxNo());
            infoVO.setInvoiceRegisterAddrInfo(invoiceInfo.getRegisterAddrInfo());
            infoVO.setInvoiceAccountNo(invoiceInfo.getAccountNo());
            infoVO.setInvoiceBankName(invoiceInfo.getBankName());
            infoVO.setDefaultInvoiceCategoryCode(invoiceInfo.getDefaultInvoiceCategoryCode());
            infoVO.setDefaultInvoiceCategoryName(invoiceInfo.getDefaultInvoiceCategoryName());
            if (StringUtils.isNotBlank(invoiceInfo.getRegisterAddrInfo())) {
                String[] info = invoiceInfo.getRegisterAddrInfo().split(" ");
                if (info != null) {
                    infoVO.setInvoiceAddress(info.length >= 1 ? info[0] : "");
                    infoVO.setInvoicePhone(info.length >= 2 ? info[1] : "");
                }
            }
        }
        return infoVO;
    }

    public static List<MerchantFile> fillMerchantFile(MerchantEmployerInsertVo vo, PmsOperator operator) {
        List<MerchantFile> merchantFiles = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(vo.getSupplementFileUrls())){
            vo.getSupplementFileUrls().forEach(x -> {
                MerchantFile file = new MerchantFile();
                file.setUpdator(operator.getLoginName());
                file.setMchNo(vo.getMchNo());
                file.setMchName(vo.getMchName());
                file.setFileUrl(x);
                file.setFileType(MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue());
                file.setVersion(0);
                file.setCreateTime(new Date());
                merchantFiles.add(file);
            });
        }

        if(CollectionUtils.isNotEmpty(vo.getCompanyLeafletFileUrls())){
            vo.getCompanyLeafletFileUrls().forEach(x -> {
                MerchantFile file = new MerchantFile();
                file.setUpdator(operator.getLoginName());
                file.setMchNo(vo.getMchNo());
                file.setMchName(vo.getMchName());
                file.setFileUrl(x);
                file.setFileType(MerchantFileTypeEnum.COMPANY_LEAFLET.getValue());
                file.setVersion(0);
                file.setCreateTime(new Date());
                merchantFiles.add(file);
            });
        }
        return merchantFiles;
    }

    public static List<MerchantFile> fillMerchantFile(MerchantEmployerAddVo vo) {
        List<MerchantFile> merchantFiles = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(vo.getSupplementFileUrls())){
            vo.getSupplementFileUrls().forEach(x -> {
                MerchantFile file = new MerchantFile();
                file.setUpdator(vo.getPmsOperator().getLoginName());
                file.setMchNo(vo.getMchNo());
                file.setMchName(vo.getMchName());
                file.setFileUrl(x);
                file.setFileType(MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue());
                file.setVersion(0);
                file.setCreateTime(new Date());
                merchantFiles.add(file);
            });
        }

        if(CollectionUtils.isNotEmpty(vo.getCompanyLeafletFileUrls())){
            vo.getCompanyLeafletFileUrls().forEach(x -> {
                MerchantFile file = new MerchantFile();
                file.setUpdator(vo.getPmsOperator().getLoginName());
                file.setMchNo(vo.getMchNo());
                file.setMchName(vo.getMchName());
                file.setFileUrl(x);
                file.setFileType(MerchantFileTypeEnum.COMPANY_LEAFLET.getValue());
                file.setVersion(0);
                file.setCreateTime(new Date());
                merchantFiles.add(file);
            });
        }
        return merchantFiles;
    }

    public static MerchantEmployerMain fillMerchantEmployerMain(MerchantEmployerMainAuthVo authVo, Merchant mch) {
        MerchantEmployerMain main = new MerchantEmployerMain();
        main.setUpdateTime(new Date());
        main.setUpdator(authVo.getOperatorLoginName());
        main.setMchNo(mch.getMchNo());
        main.setShortName(authVo.getShortName());
        main.setTaxNo(authVo.getTaxNo());
        main.setRegisterAddrProvince(authVo.getRegisterAddrProvince());
        main.setRegisterAddrCity(authVo.getRegisterAddrCity());
        main.setRegisterAddrTown(authVo.getRegisterAddrTown());
        main.setRegisterAddrDetail(authVo.getRegisterAddrDetail());
        main.setRegisterAmount(authVo.getRegisterAmount());
        main.setManagementScope(authVo.getManagementScope());
        main.setManagementTermBegin(authVo.getManagementTermBegin());
        main.setManagementTermEnd(authVo.getManagementTermEnd());
        main.setManagementValidityDateType(authVo.getManagementValidityDateType());
        main.setCertificateType(authVo.getCertificateType());
        main.setLegalPersonName(authVo.getLegalPersonName());
        main.setCertificateNumber(authVo.getCertificateNumber());
        main.setCertificateValidityDateType(authVo.getCertificateValidityDateType());
        main.setCertificateTermBegin(authVo.getCertificateTermBegin());
        main.setCertificateTermEnd(authVo.getCertificateTermEnd());
        main.setManagementAddrProvince(authVo.getManagementAddrProvince());
        main.setManagementAddrCity(authVo.getManagementAddrCity());
        main.setManagementAddrTown(authVo.getManagementAddrTown());
        main.setManagementAddrDetail(authVo.getManagementAddrDetail());
        main.setVersion(0);
        main.setCreateTime(new Date());
        return main;
    }

    public static MerchantBankAccount fillMerchantBankAccount(MerchantEmployerMainAuthVo authVo,  Merchant mch) {
        MerchantBankAccount account = new MerchantBankAccount();
        account.setUpdateTime(new Date());
        account.setUpdator(authVo.getOperatorLoginName());
        account.setMchNo(authVo.getMchNo());
        account.setAccountNo(authVo.getAccountNo());
        account.setAccountName(mch.getMchName());
        account.setBankName(authVo.getBankName());
        account.setBankChannelNo(authVo.getBankChannelNo());
        account.setAlipayAccountNo(authVo.getAlipayAccountNo());
        account.setVersion(0);
        account.setCreateTime(new Date());
        return account;
    }

    public static void fillMerchant(MerchantEmployerMainAuthVo authVo, Merchant mch) {
        mch.setAuthStatus(AuthStatusEnum.SUCCESS.getValue());
        if (authVo.getMerchantType().intValue() == MerchantTypeEnum.MAINSTAY.getValue()){
            mch.setMchStatus(MchStatusEnum.ACTIVE.getValue());
            mch.setActiveTime(new Date());
        }
        //mch.setContactEmail(authVo.getContactEmail());
        mch.setServicePhone(authVo.getServicePhone());
        mch.setUpdateTime(new Date());
        mch.setUpdator(authVo.getOperatorLoginName());
    }

    public static void fillRejectMerchant(MerchantEmployerMainAuthVo authVo, Merchant mch) {
        mch.setAuthStatus(AuthStatusEnum.FAIL.getValue());
        mch.setMchStatus(MchStatusEnum.CREATE.getValue());
        //mch.setContactEmail(authVo.getContactEmail());
        mch.setServicePhone(authVo.getServicePhone());
        mch.setUpdateTime(new Date());
        mch.setActiveTime(new Date());
        mch.setUpdator(authVo.getOperatorLoginName());
    }


    public static List<MerchantFile> fillMerchantFile(MerchantEmployerMainAuthVo authVo, String mchName) {
        List<MerchantFile> fileList = new ArrayList<>();

        MerchantFile businessLicense = fillMerchant(authVo.getOperatorLoginName(), authVo.getMchNo(),
                mchName, authVo.getBusinessLicenseFileUrl(), MerchantFileTypeEnum.BUSINESS_LICENSE.getValue());
        fileList.add(businessLicense);

        MerchantFile doorPhoto = fillMerchant(authVo.getOperatorLoginName(), authVo.getMchNo(),
                mchName, authVo.getDoorPhotoFileUrl(), MerchantFileTypeEnum.DOOR_PHOTO.getValue());
        fileList.add(doorPhoto);

        MerchantFile workIndoor = fillMerchant(authVo.getOperatorLoginName(), authVo.getMchNo(),
                mchName, authVo.getWorkIndoorFileUrl(), MerchantFileTypeEnum.WORK_INDOOR.getValue());
        fileList.add(workIndoor);

        MerchantFile reception = fillMerchant(authVo.getOperatorLoginName(), authVo.getMchNo(),
                mchName, authVo.getReceptionFileUrl(), MerchantFileTypeEnum.RECEPTION.getValue());
        fileList.add(reception);

        if (StringUtil.isNotEmpty(authVo.getIdCardHeadFileUrl())) {
            MerchantFile idCardHead = fillMerchant(authVo.getOperatorLoginName(), authVo.getMchNo(),
                    mchName, authVo.getIdCardHeadFileUrl(), MerchantFileTypeEnum.ID_CARD_HEAD.getValue());
            fileList.add(idCardHead);
        }

        if (StringUtil.isNotEmpty(authVo.getIdCardEmblemFileUrl())) {
            MerchantFile idCardEmblem = fillMerchant(authVo.getOperatorLoginName(), authVo.getMchNo(),
                    mchName, authVo.getIdCardEmblemFileUrl(), MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue());
            fileList.add(idCardEmblem);
        }

        if (StringUtil.isNotEmpty(authVo.getIdCardCopyFileUrl())) {
            MerchantFile idCardCopy = fillMerchant(authVo.getOperatorLoginName(), authVo.getMchNo(),
                    mchName, authVo.getIdCardCopyFileUrl(), MerchantFileTypeEnum.ID_CARD_COPY.getValue());
            fileList.add(idCardCopy);
        }

        return fileList;

    }

    private static MerchantFile fillMerchant(String updator, String mchNo, String mchName, String fileUrl, int merchantFileType) {
        MerchantFile file = new MerchantFile();
        file.setUpdator(updator);
        file.setMchNo(mchNo);
        file.setMchName(mchName);
        file.setFileUrl(fileUrl);
        file.setFileType(merchantFileType);
        file.setVersion(0);
        file.setCreateTime(new Date());
        return file;
    }

    private static MerchantEmployerQuote fillMerchantEmployerQuote(MerchantEmployerQuoteVo quoteVo, String loginName, String mchNo) {
        MerchantEmployerQuote quote = fillQuote(quoteVo,loginName,mchNo,null);
        return quote;
    }

    public static MerchantEmployerQuote fillQuote(MerchantEmployerQuoteVo vo, String loginName, String mchNo,String flowBusinessKey) {
        MerchantEmployerQuote quote = new MerchantEmployerQuote();
        vo.setFlowBusinessKey(flowBusinessKey);
        quote.setUpdateTime(new Date());
        quote.setUpdator(loginName);
        quote.setMchNo(mchNo);
        quote.setMainstayMchNo(vo.getMainstayMchNo());
        quote.setMainstayMchName(vo.getMainstayMchName());
        quote.setFormulaType(vo.getFormulaType());
        quote.setFixedFee(vo.getFixedFee());
        quote.setRate(vo.getRate());
        quote.setStatus(QuoteAuditEnum.PENGDING.getValue());
        if (vo.getRuleParam() != null && vo.getRuleParam().size() > 0){
            quote.setRuleParam(JsonUtil.toString(vo.getRuleParam()));
        }
        quote.setProductNo(vo.getProductNo());
        quote.setProductName(vo.getProductName());
        quote.setDescription(vo.getDescription());
        quote.setFlowBusinessKey(vo.getFlowBusinessKey());
        quote.setVersion(0);
        quote.setCreateTime(new Date());
        return quote;
    }

    public static List<MerchantEmployerQuote> fillEmployerQuote(EmployerCooperateUpdateVo vo, String loginName) {
        List<MerchantEmployerQuote> quoteList = new ArrayList<>();
        vo.getQuoteVoList().forEach(x -> {
            MerchantEmployerQuote quote = fillMerchantEmployerQuote(x, loginName, vo.getMchNo());
            quoteList.add(quote);
        });
        return quoteList;
    }

    public static EmployerStaffVO fillEmployerStaffVO(MerchantEmployerInsertVo vo, String loginName) {
        EmployerStaffVO staffVO = new EmployerStaffVO();
        staffVO.setCreateTime(new Date());
        staffVO.setUpdateTime(new Date());
        staffVO.setPhone(vo.getContactPhone());
        staffVO.setName(vo.getContactName());
        staffVO.setMchNo(vo.getMchNo());
        staffVO.setMchName(vo.getMchName());
        staffVO.setType(PortalStaffTypeEnum.ADMIN.getValue());
        staffVO.setCreator(loginName);
        staffVO.setUpdator(loginName);
        return staffVO;
    }

    public static EmployerStaffVO fillEmployerStaffVO(MerchantEmployerAddVo vo) {
        EmployerStaffVO staffVO = new EmployerStaffVO();
        staffVO.setCreateTime(new Date());
        staffVO.setUpdateTime(new Date());
        staffVO.setPhone(vo.getContactPhone());
        staffVO.setName(vo.getContactName());
        staffVO.setMchNo(vo.getMchNo());
        staffVO.setMchName(vo.getMchName());
        staffVO.setType(PortalStaffTypeEnum.ADMIN.getValue());
        staffVO.setCreator(vo.getPmsOperator().getLoginName());
        staffVO.setUpdator(vo.getPmsOperator().getLoginName());
        return staffVO;
    }

    public static SupplierStaffVO fillSupplierStaffVO(Merchant merchant, String loginName) {
        SupplierStaffVO staffVO = new SupplierStaffVO();
        staffVO.setCreateTime(new Date());
        staffVO.setUpdateTime(new Date());
        staffVO.setPhone(merchant.getContactPhone());
        staffVO.setName(merchant.getContactName());
        staffVO.setMchNo(merchant.getMchNo());
        staffVO.setMchName(merchant.getMchName());
        // 设置管理员
        staffVO.setType(PortalStaffTypeEnum.ADMIN.getValue());
        staffVO.setCreator(loginName);
        staffVO.setUpdator(loginName);
        return staffVO;
    }

    public static AgentStaffVO fillSupplierStaffVO(Agent agent, String loginName) {
        AgentStaffVO staffVO = new AgentStaffVO();
        staffVO.setCreateTime(new Date());
        staffVO.setUpdateTime(new Date());
        staffVO.setPhone(agent.getContactPhone());
        if (agent.getAgentType() != null && AgentTypeEnum.PERSON.getValue() == agent.getAgentType()) {
            staffVO.setName(agent.getAgentName());
        } else {
            staffVO.setName(agent.getContactName());
        }
        staffVO.setAgentNo(agent.getAgentNo());
        staffVO.setAgentName(agent.getAgentName());
        // 设置为管理员
        staffVO.setType(PortalStaffTypeEnum.ADMIN.getValue());
        staffVO.setCreator(loginName);
        staffVO.setUpdator(loginName);
        return staffVO;
    }

    public static MerchantInvoiceInfo fillMerchantInvoiceInfo(Merchant mch, MerchantEmployerMain main, MerchantBankAccount account, MerchantEmployerMainAuthVo authVo) {
        MerchantInvoiceInfo info =  new MerchantInvoiceInfo();
        info.setUpdateTime(new Date());
        info.setMchNo(mch.getMchNo());
        info.setMchName(mch.getMchName());
        info.setTaxPayerType(authVo.getTaxPayerType());
        info.setTaxNo(main.getTaxNo());

        info.setRegisterAddrInfo(authVo.getInvoiceAddress() + " " + authVo.getInvoicePhone());
        info.setAccountNo(authVo.getInvoiceAccountNo());
        info.setBankName(authVo.getInvoiceBankName());
        info.setVersion(0);
        info.setCreateTime(new Date());
        info.setDefaultInvoiceCategoryCode(authVo.getDefaultInvoiceCategoryCode());
        info.setDefaultInvoiceCategoryName(authVo.getDefaultInvoiceCategoryName());
        return info;
    }

    public static AgentEmployerMain fillAgentEmployerMain(AgentVo vo,Integer agentType, Date cur,String updater){
        AgentEmployerMain agentEmployerMain = null;
        if(agentType.equals(AgentTypeEnum.COMPANY.getValue())){
            //主体信息
            agentEmployerMain = new AgentEmployerMain();
            agentEmployerMain.setUpdateTime(cur);
            agentEmployerMain.setCreateTime(cur);
            agentEmployerMain.setUpdater(updater);
            BeanUtils.copyProperties(vo,agentEmployerMain);
        }
        return agentEmployerMain;
    }

    public static AgentBankAccount fillAgentBankAccount(AgentVo vo, Date cur,String updater){
        AgentBankAccount agentBankAccount = new AgentBankAccount();
        agentBankAccount.setUpdateTime(cur);
        agentBankAccount.setCreateTime(cur);
        agentBankAccount.setUpdater(updater);
        agentBankAccount.setAccountName(vo.getAccountNo());
        BeanUtils.copyProperties(vo,agentBankAccount);
        return agentBankAccount;
    }

    public static AgentBankAccount fillAgentBankAccount(AgentBankAccount vo, Date cur,String updater){
        vo.setUpdateTime(cur);
        vo.setCreateTime(cur);
        vo.setUpdater(updater);
        return vo;
    }

    public static AgentCredential fillAgentCredential(AgentVo vo, Date cur,String updater){
        AgentCredential agentCredential = new AgentCredential();
        agentCredential.setUpdateTime(cur);
        agentCredential.setCreateTime(cur);
        agentCredential.setUpdater(updater);
        BeanUtils.copyProperties(vo,agentCredential);
        return agentCredential;
    }

    public static void fillAgentFile(List<MerchantFile> fileList,AgentVo vo,Integer agentType, Date cur,String updater) {
        if(agentType.equals(AgentTypeEnum.COMPANY.getValue())){
            MerchantFile businessLicense = new MerchantFile();
            businessLicense.setUpdator(updater);
            businessLicense.setMchNo(vo.getAgentNo());
            businessLicense.setMchName(vo.getAgentName());
            businessLicense.setFileUrl(vo.getBusinessLicenseFileUrl());
            businessLicense.setFileType(MerchantFileTypeEnum.BUSINESS_LICENSE.getValue());
            businessLicense.setCreateTime(cur);
            fileList.add(businessLicense);
        }

        if(StringUtils.isNotBlank(vo.getIdCardEmblemFileUrl())){
            MerchantFile emblemFile = new MerchantFile();
            emblemFile.setUpdator(updater);
            emblemFile.setMchNo(vo.getAgentNo());
            emblemFile.setMchName(vo.getAgentName());
            emblemFile.setFileUrl(vo.getIdCardEmblemFileUrl());
            emblemFile.setFileType(MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue());
            emblemFile.setCreateTime(cur);
            fileList.add(emblemFile);
        }
        if(StringUtils.isNotBlank(vo.getIdCardHeadFileUrl())){
            MerchantFile headFile = new MerchantFile();
            headFile.setUpdator(updater);
            headFile.setMchNo(vo.getAgentNo());
            headFile.setMchName(vo.getAgentName());
            headFile.setFileUrl(vo.getIdCardHeadFileUrl());
            headFile.setFileType(MerchantFileTypeEnum.ID_CARD_HEAD.getValue());
            headFile.setCreateTime(cur);
            fileList.add(headFile);
        }
        if(StringUtils.isNotBlank(vo.getIdCardCopyFileUrl())){
            MerchantFile copyFile = new MerchantFile();
            copyFile.setUpdator(updater);
            copyFile.setMchNo(vo.getAgentNo());
            copyFile.setMchName(vo.getAgentName());
            copyFile.setFileUrl(vo.getIdCardCopyFileUrl());
            copyFile.setFileType(MerchantFileTypeEnum.ID_CARD_COPY.getValue());
            copyFile.setCreateTime(cur);
            fileList.add(copyFile);
        }
    }

    public static List<AgentProductQuote> fillAgentQuoteList(AgentVo vo,Date cur,String updater) {
        return vo.getQuoteVoList().stream().map(
                quoteVo ->{
                    AgentProductQuote agentProductQuote = new AgentProductQuote();
                    agentProductQuote.setUpdateTime(cur);
                    agentProductQuote.setCreateTime(cur);
                    agentProductQuote.setUpdater(updater);
                    BeanUtils.copyProperties(quoteVo,agentProductQuote);
                    BeanUtils.copyProperties(vo,agentProductQuote);
                    return agentProductQuote;
                }
        ).collect(Collectors.toList());
    }

    public static void fillAgent(Agent agent, AgentVo vo, Date cur) {
        BeanUtils.copyProperties(vo, agent);

        agent.setAgentStatus(AgentStatusEnum.ACTIVE.getValue());
        agent.setAuthStatus(AuthStatusEnum.SUCCESS.getValue());
        agent.setActiveTime(cur);
        agent.setUpdateTime(cur);
    }

    public static ApprovalFlow fillAgentApprovalFlow(Agent agent,AgentSaler agentSaler,String approvalContent){
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setVersion(0);
        approvalFlow.setStepNum(0);
        approvalFlow.setInitiatorId(agentSaler.getSalerId());
        approvalFlow.setInitiatorName(agentSaler.getSalerName());
        approvalFlow.setFlowTopicType(FlowTopicType.CREATE_AGENT.getValue());
        approvalFlow.setFlowTopicName(String.join("-", FlowTopicType.CREATE_AGENT.getDesc(), agent.getAgentName()));
        approvalFlow.setCreateTime(new Date());
        approvalFlow.setUpdateTime(new Date());
        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
        approvalFlow.setExtInfo(approvalContent);
        approvalFlow.setPlatform(PlatformSource.OPERATION.getValue());
        return approvalFlow;
    }


    public static List<MerchantEmployerQuotePosition> fillQuotePositionList(MerchantFlowVo merchantFlowVo, MerchantEmployerQuote merchantEmployerQuote) {
        List<MerchantEmployerQuotePosition> merchantEmployerQuotePositionList = new ArrayList<>();
        if (merchantFlowVo.getPositionList() == null){
            return merchantEmployerQuotePositionList;
        }

        merchantFlowVo.getPositionList().forEach(x->{
            MerchantEmployerQuotePosition merchantEmployerQuotePosition = new MerchantEmployerQuotePosition();
            merchantEmployerQuotePosition.setMchNo(merchantFlowVo.getMchNo());
            merchantEmployerQuotePosition.setPositionId(x);
            merchantEmployerQuotePosition.setQuoteId(merchantEmployerQuote.getId());
            merchantEmployerQuotePosition.setCreateTime(new Date());
            merchantEmployerQuotePosition.setUpdateTime(merchantEmployerQuotePosition.getCreateTime());
            merchantEmployerQuotePositionList.add(merchantEmployerQuotePosition);
        });
        return merchantEmployerQuotePositionList;
    }

    public static MerchantCkhQuote fillCkhQuote(MerchantFlowVo merchantFlowVo, MerchantEmployerQuote merchantEmployerQuote) {
        MerchantCkhQuote merchantCkhQuote = new MerchantCkhQuote();
        BeanUtils.copyProperties(merchantFlowVo.getMerchantCkhQuote(),merchantCkhQuote);
        merchantCkhQuote.setQuoteId(merchantEmployerQuote.getId());
        BigDecimal hundred = new BigDecimal(100);
        merchantCkhQuote.setVersion(0);
        merchantCkhQuote.setCreateTime(new Date());
        merchantCkhQuote.setUpdateTime(new Date());
        //TODO 增值税设置为0，个税承担比例设置为100
        merchantCkhQuote.setTaxRate(BigDecimal.ONE);
//        merchantCkhQuote.setAddedTaxRate(AmountUtil.div(merchantFlowVo.getMerchantCkhQuote().getAddedTaxRatePct(),hundred));
//        merchantCkhQuote.setTaxRate(BigDecimal.ZERO);
        merchantCkhQuote.setAddedTaxRate(BigDecimal.ZERO);
        merchantCkhQuote.setServiceFeeRate(AmountUtil.div(merchantFlowVo.getMerchantCkhQuote().getServiceFeeRatePct(),hundred));
        return merchantCkhQuote;
    }

    public static List<MerchantZftQuote> fillZftQuote(MerchantFlowVo merchantFlowVo, MerchantEmployerQuote merchantEmployerQuote) {
        List<MerchantZftQuote> merchantZftQuoteList = new ArrayList<>();
        merchantFlowVo.getMerchantZftQuote().forEach(x->{
            MerchantZftQuote merchantZftQuote = new MerchantZftQuote();
            BeanUtil.copyProperties(x,merchantZftQuote);
            merchantZftQuote.setQuoteId(merchantEmployerQuote.getId());
            merchantZftQuote.setCreateTime(new Date());
            merchantZftQuote.setUpdateTime(new Date());
            merchantZftQuoteList.add(merchantZftQuote);
        });
        return merchantZftQuoteList;
    }
}
