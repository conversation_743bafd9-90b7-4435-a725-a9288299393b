package com.zhixianghui.service.merchant.facade.supplier;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.service.supplier.SupplierRoleFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierRoleVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierRoleBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台角色表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierRoleImpl implements SupplierRoleFacade {

    private final SupplierRoleBiz biz;

    /**
     * 创建用工企业后台角色
     *
     * @param role 角色
     */
    @Override
    public void create(SupplierRole role) {
        biz.create(role);
    }

    /**
     * 根据id查询用工企业后台角色
     *
     * @param mchNo 商户编号
     * @param id
     */
    @Override
    public SupplierRole getById(String mchNo, long id) {
        return biz.getById(mchNo, id);
    }

    /**
     * 更新用工企业后台角色
     *
     * @param role 角色
     */
    @Override
    public void update(SupplierRole role) throws BizException {
        biz.update(role);
    }

    /**
     * 根据id删除用工企业后台角色
     *
     * @param mchNo  商户编号
     * @param id	 角色id
     */
    @Override
    public void deleteById(String mchNo, long id) throws BizException {
        biz.deleteById(mchNo, id);
    }

    /**
     * 为角色分配功能
     *
     * @param mchNo         商户编号
     * @param roleId      角色id
     * @param functionIds 功能id
     */
    @Override
    public void updateFunction(String mchNo, long roleId, List<Long> functionIds) throws BizException {
        biz.updateFunction(mchNo, roleId, functionIds);
    }

    /**
     * 根据角色id获取其关联的功能
     *
     * @param mchNo    商户编号
     * @param roleId 角色id
     */
    @Override
    public List<SupplierFunction> listFunctionByRoleId(String mchNo, long roleId) throws BizException {
        return biz.listFunctionByRoleId(mchNo, roleId);
    }

    /**
     * 根据角色id获取其关联的操作员
     *
     * @param mchNo    商户编号
     * @param roleId 角色id
     */
    @Override
    public List<SupplierStaffVO> listStaffByRoleId(String mchNo, long roleId) throws BizException {
        return biz.listStaffByRoleId(mchNo, roleId);
    }

    /**
     * 查询所有角色
     */
    @Override
    public List<SupplierRole> listAll(String mchNo) {
        return biz.listAll(mchNo);
    }

    /**
     * 分页查询角色
     *
     * @param mchNo
     * @param paramMap
     * @param pageParam
     */
    @Override
    public PageResult<List<SupplierRoleVo>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(mchNo, paramMap, pageParam);
    }

    @Override
    public PageResult<List<SupplierRoleVo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);

    }

    @Override
    public Long count(SupplierRoleVo supplierRoleVo, String mchNo) {
        return biz.count(supplierRoleVo, mchNo);
    }
}
