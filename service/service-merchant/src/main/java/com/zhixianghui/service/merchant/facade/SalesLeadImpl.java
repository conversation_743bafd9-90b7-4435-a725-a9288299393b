package com.zhixianghui.service.merchant.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.SalesLead;
import com.zhixianghui.facade.merchant.service.SalesLeadFacade;
import com.zhixianghui.service.merchant.core.biz.SalesLeadBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class SalesLeadImpl implements SalesLeadFacade {

    @Autowired
    private SalesLeadBiz salesLeadBiz;

    @Override
    public void save(SalesLead salesLead) throws BizException {
        salesLeadBiz.save(salesLead);
    }

    @Override
    public void updateByCreateNo(SalesLead salesLead) {
        salesLeadBiz.updateByCreateNo(salesLead);
    }

    @Override
    public IPage<SalesLead> listByPage(IPage page, SalesLead salesLead){
        return salesLeadBiz.listByPage(page, salesLead);
    }

    @Override
    public void update(SalesLead salesLead) throws BizException {
        salesLeadBiz.updateById(salesLead);
    }

    @Override
    public SalesLead getById(Long id) throws BizException {
        return salesLeadBiz.getById(id);
    }
}
