package com.zhixianghui.service.merchant.facade;

import com.alibaba.excel.util.CollectionUtils;
import com.zhixianghui.facade.merchant.entity.PersonalIncomeTax;
import com.zhixianghui.facade.merchant.service.PersonalIncomeTaxFacade;
import com.zhixianghui.service.merchant.core.biz.PersonalIncomeTaxBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-06-22
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PersonalIncomeTaxImpl implements PersonalIncomeTaxFacade {

    private final PersonalIncomeTaxBiz biz;

    @Override
    public List<PersonalIncomeTax> listAll() {
        return biz.listAll();
    }

    @Override
    public PersonalIncomeTax getSuitTax(BigDecimal amount) {
        return biz.getSuitTax(amount);
    }
}
