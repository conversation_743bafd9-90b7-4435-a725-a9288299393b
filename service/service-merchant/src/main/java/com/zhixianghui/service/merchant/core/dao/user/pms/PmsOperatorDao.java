package com.zhixianghui.service.merchant.core.dao.user.pms;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Author: Cmf
 * Date: 2019/10/10
 * Time: 14:50
 * Description: 操作员表数据访问层接口实现类
 */
@Repository("pmsOperatorDao")
public class PmsOperatorDao extends MyBatisDao<PmsOperator, Long> {

    /**
     * 根据操作员登录名获取操作员信息.
     *
     * @param loginName     登录名
     */
    public PmsOperator findByLoginName(String loginName) {
        if (StringUtil.isEmpty(loginName)) {
            return null;
        }
        return getOne(Collections.singletonMap("loginName", loginName));
    }

    /**
     * 将部门下的所有员工分配到另一个部门
     * @param oldDepartmentId       旧部门 id
     * @param newDepartmentId       新部门 id
     * @param newDepartmentName     新部门名称
     */
    public void reallocateDepartment(long oldDepartmentId, long newDepartmentId, String newDepartmentName) {
        Map<String, Object> map = new HashMap<>();
        map.put("oldDepartmentId", oldDepartmentId);
        map.put("newDepartmentId", newDepartmentId);
        map.put("newDepartmentName", newDepartmentName);
        super.update(fillSqlId("reallocateDepartment"), map);
    }
}
