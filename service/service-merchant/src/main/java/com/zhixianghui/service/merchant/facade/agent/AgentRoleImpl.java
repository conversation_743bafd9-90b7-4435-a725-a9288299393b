package com.zhixianghui.service.merchant.facade.agent;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.service.agent.AgentRoleFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentRoleVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentRoleBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台角色表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentRoleImpl implements AgentRoleFacade {

    private final AgentRoleBiz biz;

    /**
     * 创建用工企业后台角色
     *
     * @param role 角色
     */
    @Override
    public void create(AgentRole role) {
        biz.create(role);
    }

    /**
     * 根据id查询用工企业后台角色
     *
     * @param agentNo 商户编号
     * @param id
     */
    @Override
    public AgentRole getById(String agentNo, long id) {
        return biz.getById(agentNo, id);
    }

    @Override
    public AgentRole getById(Long id) {
        return biz.getById(id);
    }

    /**
     * 更新用工企业后台角色
     *
     * @param role 角色
     */
    @Override
    public void update(AgentRole role) throws BizException {
        biz.update(role);
    }

    /**
     * 根据id删除用工企业后台角色
     *
     * @param agentNo  商户编号
     * @param id	 角色id
     */
    @Override
    public void deleteById(String agentNo, long id) throws BizException {
        biz.deleteById(agentNo, id);
    }

    @Override
    public void deleteById(Long id) {
        biz.deleteById(id);
    }

    /**
     * 为角色分配功能
     *
     * @param agentNo         商户编号
     * @param roleId      角色id
     * @param functionIds 功能id
     */
    @Override
    public void updateFunction(String agentNo, long roleId, List<Long> functionIds) throws BizException {
        biz.updateFunction(agentNo, roleId, functionIds);
    }

    @Override
    public void updateFunction(Long roleId, List<Long> functionIds) {
        biz.updateFunction(roleId, functionIds);
    }

    /**
     * 根据角色id获取其关联的功能
     *
     * @param agentNo    商户编号
     * @param roleId 角色id
     */
    @Override
    public List<AgentFunction> listFunctionByRoleId(String agentNo, long roleId) throws BizException {
        return biz.listFunctionByRoleId(agentNo, roleId);
    }

    @Override
    public List<AgentFunction> listFunctionByRoleId(Long roleId) {
        return biz.listFunctionByRoleId(roleId);
    }

    /**
     * 根据角色id获取其关联的操作员
     *
     * @param agentNo    商户编号
     * @param roleId 角色id
     */
    @Override
    public List<AgentStaffVO> listStaffByRoleId(String agentNo, long roleId) throws BizException {
        return biz.listStaffByRoleId(agentNo, roleId);
    }

    /**
     * 查询所有角色
     */
    @Override
    public List<AgentRole> listAll(String agentNo) {
        return biz.listAll(agentNo);
    }

    /**
     * 分页查询角色
     *
     * @param agentNo
     * @param paramMap
     * @param pageParam
     */
    @Override
    public PageResult<List<AgentRoleVo>> listPage(String agentNo, Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(agentNo, paramMap, pageParam);
    }

    @Override
    public PageResult<List<AgentRoleVo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public Long count(AgentRoleVo agentRoleVo, String agentNo) {
        return biz.count(agentRoleVo, agentNo);

    }
}
