package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.service.MerchantEnterprisePersonnelFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantEnterprisePersonnelBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-06-07
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantEnterprisePersonnelImpl implements MerchantEnterprisePersonnelFacade {

    private final MerchantEnterprisePersonnelBiz biz;

    @Override
    public void syncAllMerchant() {
        biz.syncAllMerchant();
    }
}
