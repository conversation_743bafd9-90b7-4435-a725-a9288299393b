package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.dto.EnterprisePersonnelDto;
import com.zhixianghui.facade.merchant.entity.MerchantEnterprisePersonnel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 *  Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2022-06-07
 */
@Repository
public class MerchantEnterprisePersonnelDao extends MyBatisDao<MerchantEnterprisePersonnel,Long> {

    public List<MerchantEnterprisePersonnel> getListByMchNo(String mchNo){
        List<MerchantEnterprisePersonnel> personnels = this.listBy(Collections.singletonMap("mchNo", mchNo));
        this.getSqlSession().clearCache();
        return personnels;
    }

    public void insertDto(List<EnterprisePersonnelDto> list) {
        int insertedRow = this.getSqlSession().insert(fillSqlId("batchInsert"), list);
        if (insertedRow != list.size()) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("数据插入的记录数与预期不匹配，预期:" + list.size() + ",实际插入:" + insertedRow);
        }
    }

    public void insertDto(EnterprisePersonnelDto enterprisePersonnelDto) {
        int insertedRow = this.getSqlSession().insert(fillSqlId("insert"), enterprisePersonnelDto);
        if (insertedRow < 0) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("数据插入的记录数不为1");
        }
    }

    public void updateDto(EnterprisePersonnelDto enterprisePersonnelDto) {
        int result = this.getSqlSession().update(fillSqlId("updateDto"), enterprisePersonnelDto);
        if (result != 1) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("数据更新成功的记录数不为1");
        }
    }
}
