package com.zhixianghui.service.merchant.facade;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.EventsFollow;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.EventFollowFacade;
import com.zhixianghui.service.merchant.core.biz.EventsFollowBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class EventFollowImpl implements EventFollowFacade {
    @Autowired
    private EventsFollowBiz eventsFollowBiz;
    @Autowired
    private MerchantBiz merchantBiz;

    @Override
    public List<EventsFollow> getEventFollowByMchNo(String mchNo) {
        return eventsFollowBiz.list(new QueryWrapper<EventsFollow>().eq(EventsFollow.COL_MCH_NO,mchNo).orderByDesc(EventsFollow.COL_UPDATE_TIME));

    }

    @Override
    public EventsFollow getEventFollowById(Long id) {
        return eventsFollowBiz.getById(id);
    }

    @Override
    public void addEventFollowRecord(EventsFollow eventsFollow) {
        log.info(JSON.toJSONString(eventsFollow));

        final Merchant merchant = merchantBiz.getByMchNo(eventsFollow.getMchNo());
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        Date now = new Date();
        eventsFollow.setCreateTime(now);
        eventsFollow.setUpdateTime(now);
        eventsFollow.setMchName(merchant.getMchName());
        eventsFollow.setMchType(merchant.getMerchantType().shortValue());
        eventsFollowBiz.save(eventsFollow);
    }

    @Override
    public void updateEventFollowRecord(Long id, String content) {
        final EventsFollow eventsFollow = eventsFollowBiz.getById(id);
        eventsFollow.setEventContent(content);
        eventsFollow.setUpdateTime(new Date());
        eventsFollowBiz.updateById(eventsFollow);
    }
}
