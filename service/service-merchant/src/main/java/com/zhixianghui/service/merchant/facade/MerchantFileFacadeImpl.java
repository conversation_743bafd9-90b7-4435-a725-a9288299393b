package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.service.MerchantFileFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantFileBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
public class MerchantFileFacadeImpl implements MerchantFileFacade {
    @Autowired
    private MerchantFileBiz merchantFileBiz;

    @Override
    public void insert(MerchantFile file) {
        merchantFileBiz.insert(file);
    }

    @Override
    public void modifyFile(String mchNo, String mchName, String loginName, Integer fileType, List<String> fileUrls) {
        merchantFileBiz.modifyFile(mchNo, mchName, loginName, fileType, fileUrls);
    }

    @Override
    public List<MerchantFile> listBy(Map<String, Object> paramMap) {
        return  merchantFileBiz.listBy(paramMap);
    }

    @Override
    public List<MerchantFile> listByMchNo(String mchNo) {
        return merchantFileBiz.listByMchNo(mchNo);
    }
}
