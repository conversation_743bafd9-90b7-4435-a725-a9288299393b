package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.dto.AgentBaseInfoDto;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.AgentManagerFacade;

import com.zhixianghui.facade.merchant.vo.agent.AgentAddVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentVo;
import com.zhixianghui.service.merchant.core.biz.AgentManagerBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/2 17:39
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentManagerImpl implements AgentManagerFacade {

    private final AgentManagerBiz biz;

    @Override
    public void generateAgent(Agent agent, AgentSaler agentSaler) {
        biz.generateAgent(agent, agentSaler);
    }

    @Override
    public Long createAgent(Agent agent, AgentSaler agentSaler, String approvalContent) {

        return biz.createAgent(agent,agentSaler,approvalContent);
    }

    @Override
    public void batchSetInviter(List<String> agentNoList, String inviterNo, String updater) {
        biz.batchSetInviter(agentNoList,inviterNo,updater);
    }

    @Override
    public void batchSetSeller(List<String> agentNoList, Long sellerId, String updater) {
        biz.batchSetSeller(agentNoList,sellerId,updater);
    }

    @Override
    public void changeStatus(Agent agent) {
        biz.changeStatus(agent);
    }

    @Override
    public void updateAgentDetail(AgentVo vo, Integer agentType, String loginName){
        vo.setAgentType(agentType);
        vo.setLoginName(loginName);
        biz.updateAgentDetail(vo);
    }

    @Override
    public void saveAgentExtInfo(AgentAddVo addVo) {
        biz.saveAgentExtInfo(addVo);
    }

    @Override
    public void editAgentBaseInfo(AgentBaseInfoDto baseInfoDto, PmsOperator pmsOperator) {
        biz.editAgentBaseInfo(baseInfoDto,pmsOperator);
    }
}
