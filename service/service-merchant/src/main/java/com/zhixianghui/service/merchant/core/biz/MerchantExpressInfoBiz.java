package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.service.merchant.core.dao.MerchantExpressInfoDao;
import com.zhixianghui.service.merchant.core.dao.MerchantInvoiceInfoDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商户开票信息
 *
 * <AUTHOR> <PERSON>
 */
@Slf4j
@Component
public class MerchantExpressInfoBiz {

    @Autowired
    private MerchantExpressInfoDao expressInfoDao;

    public MerchantExpressInfo getByMchNo(String mchNo){
        return expressInfoDao.getByMchNo(mchNo);
    }

    public void update(MerchantExpressInfo invoiceInfo){
        expressInfoDao.update(invoiceInfo);
    }

    public void insert(MerchantExpressInfo invoiceInfo){
        expressInfoDao.insert(invoiceInfo);
    }
}
