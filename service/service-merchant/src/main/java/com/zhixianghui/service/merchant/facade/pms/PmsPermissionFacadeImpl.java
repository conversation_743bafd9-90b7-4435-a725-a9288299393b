package com.zhixianghui.service.merchant.facade.pms;


import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRole;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperatorBiz;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsPermissionBiz;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: Cmf
 * Date: 2019/10/10
 * Time: 11:42
 * Description:
 */
@Service
public class PmsPermissionFacadeImpl implements PmsPermissionFacade {
    @Autowired
    private PmsPermissionBiz pmsPermissionBiz;
    @Autowired
    private PmsOperatorBiz pmsOperatorBiz;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    //<editor-fold desc="功能点管理">
    @Override
    public void createFunction(PmsFunction pmsFunction) throws BizException {
        pmsPermissionBiz.createFunction(pmsFunction);
    }

    @Override
    public void deleteFunctionById(Long functionId) throws BizException {
        pmsPermissionBiz.deleteFunctionById(functionId);
    }

    @Override
    public void updateFunction(PmsFunction pmsFunction) {
        pmsPermissionBiz.updateFunction(pmsFunction);
    }

    @Override
    public List<PmsFunction> listFunctionByOperatorId(Long operatorId) throws BizException {
        return pmsPermissionBiz.listFunctionByOperatorId(operatorId);
    }

    @Override
    public List<PmsFunction> listAllFunction(Map<String, Object> param, PageParam pageParam) {
        return pmsPermissionBiz.listAllFunction(param, pageParam);
    }

    @Override
    public List<PmsFunction> listAllFunction() {
        return pmsPermissionBiz.listAllFunction();
    }

    @Override
    public List<Long> listFunctionIdsByRoleId(Long roleId) {
        return pmsPermissionBiz.listFunctionIdsByRoleId(roleId);
    }

    @Override
    public List<PmsFunction> listFunctionByRoleId(Long roleId) {
        return pmsPermissionBiz.listFunctionByRoleId(roleId);
    }

    @Override
    public List<PmsFunction> listFunctionByParentId(Long parentId) {
        return pmsPermissionBiz.listFunctionByParentId(parentId);
    }

    @Override
    public PmsFunction getFunctionById(Long id) {
        return pmsPermissionBiz.getFunctionById(id);
    }

    @Override
    public PmsFunction getFunctionWithParentInfo(Long id) {
        return pmsPermissionBiz.getFunctionWithParentInfo(id);
    }
    //</editor-fold>


    //<editor-fold desc="角色管理">
    @Override
    public void createRole(PmsRole pmsRole) throws BizException {
        pmsPermissionBiz.createRole(pmsRole);
    }

    @Override
    public void deleteRoleById(Long id) throws BizException {
        pmsPermissionBiz.deleteRoleById(id);
    }

    @Override
    public void updateRole(PmsRole pmsRole) throws BizException {
        pmsPermissionBiz.updateRole(pmsRole);
    }

    @Override
    public List<PmsRole> listAllRoles() {
        return pmsPermissionBiz.listAllRoles();
    }

    @Override
    public PageResult<List<PmsRole>> listRolePage(Map<String, Object> paramMap, PageParam pageParam) {
        return pmsPermissionBiz.listRolePage(paramMap, pageParam);
    }

    @Override
    public List<PmsRole> listRolesByOperatorId(Long operatorId) {
        return pmsPermissionBiz.listRolesByOperatorId(operatorId);
    }

    @Override
    public PmsRole getRoleById(Long id) {
        return pmsPermissionBiz.getRoleById(id);
    }

    @Override
    public PmsRole getRoleByName(String roleName) {
        return pmsPermissionBiz.getRoleByName(roleName);
    }

    @Override
    public void assignPermission(Long roleId, List<Long> functionIds) throws BizException {
        pmsPermissionBiz.assignPermission(roleId, functionIds);
    }

    @Override
    public void export(PmsOperator operator) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.PMS_FUNCTION.getFileName());
        record.setReportType(ReportTypeEnum.PMS_FUNCTION.getValue());
        record.setParamJson(JsonUtil.toString(new HashMap<>()));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.PMS_FUNCTION.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }

    @Override
    public void saveFunction(List<PmsFunction> list) {
        pmsPermissionBiz.saveFunction(list);
    }

    @Override
    public Long countEmployerByRoleId(Long roleId) {
        return pmsOperatorBiz.countEmployerByRoleId(roleId);
    }

    @Override
    public String getPermissionFlag(Long parentId) {
        return pmsPermissionBiz.getPermissionFlag(parentId);
    }

    @Override
    public PmsFunction getFunctionByFlag(String permissionFlag) {
        return pmsPermissionBiz.getFunctionByFlag(permissionFlag);

    }

    //</editor-fold>

}
