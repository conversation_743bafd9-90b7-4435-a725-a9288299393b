package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.facade.merchant.entity.AgreementSigner;
import com.zhixianghui.service.merchant.core.dao.AgreementSignerDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
* <p>
* 协议签署人表Biz
* </p>
*
* <AUTHOR>
* @version 创建时间： 2020-09-02
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementSignerBiz {

    private final AgreementSignerDao agreementSignerDao;

    public List<AgreementSigner> listByAgreementId(Long agreementId) {
        List<AgreementSigner> agreementSigners = agreementSignerDao.listBy(Collections.singletonMap("agreementId",agreementId));
        agreementSigners.forEach(x-> x.setSignUrl(""));
        return agreementSigners;
    }

    public AgreementSigner getOne(Map<String, Object> paramMap) {
        return agreementSignerDao.getOne(paramMap);
    }

    public List<AgreementSigner> listByAgreementIdWithSignUrl(Long id) {
        List<AgreementSigner> agreementSigners = agreementSignerDao.listBy(Collections.singletonMap("agreementId",id));
        return agreementSigners;
    }
}