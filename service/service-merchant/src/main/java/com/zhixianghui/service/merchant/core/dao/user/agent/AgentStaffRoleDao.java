package com.zhixianghui.service.merchant.core.dao.user.agent;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentStaffRole;
import org.springframework.stereotype.Repository;

/**
 * 供应商后台员工角色关联表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class AgentStaffRoleDao extends MyBatisDao<AgentStaffRole,Long> {
    public void deleteByStaffId(long staffId) {
        super.deleteBy("deleteByStaffId", staffId);
    }

    public void deleteByRoleId(long roleId) {
        super.deleteBy("deleteByRoleId", roleId);
    }
}
