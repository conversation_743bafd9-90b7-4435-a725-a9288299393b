package com.zhixianghui.service.merchant.core.biz.user.agent;

import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentStaff;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.service.merchant.core.dao.user.agent.AgentOperatorDao;
import com.zhixianghui.service.merchant.core.dao.user.agent.AgentStaffDao;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
* 供应商后台操作员表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentOperatorBiz {

    private final AgentOperatorDao agentOperatorDao;
    private final AgentStaffDao agentStaffDao;
    private final RedisClient redisClient;
    /**
     * 根据id获取操作员
     *
     * @param id
     */
    public AgentOperator getById(long id) {
        return agentOperatorDao.getById(id);
    }

    /**
     * 根据手机号码获取操作员
     *
     * @param phone
     */
    public AgentOperator getByPhone(String phone) {
        return agentOperatorDao.getByPhone(phone);
    }

    /**
     * 根据id删除操作员，并删除其在所有用工企业下关联的员工
     *
     * @param id
     */
    @Transactional
    public void deleteById(long id) throws BizException {
        // 查询操作员关联的员工
        List<AgentStaff> staffs = agentStaffDao.listByOperatorId(id);
        if (staffs.stream().anyMatch(staff -> staff.getType() == PortalStaffTypeEnum.ADMIN.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("操作员属于某商户的超级管理员，无法删除");
        }

        agentOperatorDao.deleteById(id);
        agentStaffDao.deleteByOperatorId(id);  // 删除操作员关联的员工

        redisClient.del(PlatformSource.AGENT.getValue() + ":" + id);

        staffs.stream().forEach(agentStaff -> redisClient.del(PlatformSource.AGENT.getValue() + ":" + agentStaff.getId() + ":" + agentStaff.getAgentNo()));
    }

    /**
     * 更新操作员
     *
     * @param operator
     */
    public void update(AgentOperator operator) throws BizException {
        agentOperatorDao.update(operator);
        operator = agentOperatorDao.getById(operator.getId());
        redisClient.hset(PlatformSource.AGENT.getValue() + ":" + operator.getId(),operator.getCacheMap());
    }

    /**
     * 分页查询操作员
     *
     * @param paramMap
     * @param pageParam
     */
    public PageResult<List<AgentOperatorVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return agentOperatorDao.listOperatorVoPage(paramMap, pageParam);
    }

    public List<AgentOperator> getAll() {
        return agentOperatorDao.listAll();
    }
}