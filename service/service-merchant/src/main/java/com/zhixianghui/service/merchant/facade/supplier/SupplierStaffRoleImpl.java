package com.zhixianghui.service.merchant.facade.supplier;

import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffRoleFacade;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierStaffRoleBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 供应商后台员工角色关联表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierStaffRoleImpl implements SupplierStaffRoleFacade {

    private final SupplierStaffRoleBiz biz;

    @Override
    public Long countEmployerCount(Long roleId) {
        return biz.countEmployerCount(roleId);
    }
}
