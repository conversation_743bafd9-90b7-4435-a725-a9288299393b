package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.AgentCredential;
import com.zhixianghui.facade.merchant.service.AgentCredentialFacade;
import com.zhixianghui.service.merchant.core.biz.AgentCredentialBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 合伙人证件表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentCredentialImpl implements AgentCredentialFacade {

    private final AgentCredentialBiz biz;

    @Override
    public AgentCredential getAgentCredential(String agengNo) {
        return biz.agentCredential(agengNo);
    }
}
