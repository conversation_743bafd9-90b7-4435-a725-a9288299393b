package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.service.merchant.core.dao.AgentEmployerMainDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.merchant.entity.AgentEmployerMain;
import lombok.RequiredArgsConstructor;

/**
* 合伙人-企业主体信息表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-02-01
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentEmployerMainBiz {

    private final AgentEmployerMainDao agentemployermainDao;

    public AgentEmployerMain getByAgentNo(String agentNo) {
        return agentemployermainDao.getByAgentNo(agentNo);
    }
}