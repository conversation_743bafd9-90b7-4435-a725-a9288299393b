package com.zhixianghui.service.merchant.core.dao.user.employer;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRoleFunction;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class EmployerRoleFunctionDao extends MyBatisDao<EmployerRoleFunction, Long>{

    public void deleteByRoleId(long roleId) {
        super.deleteBy("deleteByRoleId", roleId);
    }

    public void deleteByFunctionIds(List<Long> functionIds) {
        super.deleteBy("deleteByFunctionIds", functionIds);
    }
}
