package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.service.merchant.core.dao.AgreementFileDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <p>
* 协议文件表Biz
* </p>
*
* <AUTHOR>
* @version 创建时间： 2020-09-02
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementFileBiz {

    private final AgreementFileDao agreementFileDao;

    public List<AgreementFile> listByAgreementId(Long agreementId) {
        return agreementFileDao.listBy(Collections.singletonMap("agreementId", agreementId));
    }

    public List<AgreementFile> listBy(Map<String, Object> paramMap) {
        return agreementFileDao.listBy(paramMap);
    }

    public void update(AgreementFile agreementFile) {
         agreementFileDao.update(agreementFile);
    }

    public AgreementFile getByAgreementIdAndType(Long id, int type) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("agreementId",id);
        paramMap.put("type",type);
        AgreementFile agreementFile = agreementFileDao.getOne(paramMap);
        return agreementFile;
    }
}