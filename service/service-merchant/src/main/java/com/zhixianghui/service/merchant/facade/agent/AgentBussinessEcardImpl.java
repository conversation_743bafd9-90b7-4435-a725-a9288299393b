package com.zhixianghui.service.merchant.facade.agent;

import com.zhixianghui.facade.merchant.entity.AgentBussinessEcard;
import com.zhixianghui.facade.merchant.service.agent.AgentBussinessEcardFacade;
import com.zhixianghui.service.merchant.core.biz.AgentBussinessEcardBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class AgentBussinessEcardImpl implements AgentBussinessEcardFacade {

    @Autowired
    private AgentBussinessEcardBiz agentBussinessEcardBiz;

    @Override
    public AgentBussinessEcard getAgentBussinessEcardByOpenId(String openId){
        return agentBussinessEcardBiz.getAgentBussinessEcardByOpenId(openId);
    }

    @Override
    public AgentBussinessEcard  modifyAgentEcard(AgentBussinessEcard bussinessEcard){
        return agentBussinessEcardBiz.modifyAgentEcard(bussinessEcard);
    }
}
