package com.zhixianghui.service.merchant.core.biz.user.portal;

import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalFunctionTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.service.merchant.core.dao.user.employer.EmployerFunctionDao;
import com.zhixianghui.service.merchant.core.dao.user.employer.EmployerRoleFunctionDao;

import lombok.extern.log4j.Log4j2;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.stream.Collectors;

@Service
@Log4j2
public class EmployerFunctionBiz{
	
	@Autowired
	private EmployerFunctionDao employerFunctionDao;

	@Autowired
	private EmployerRoleFunctionDao employerRoleFunctionDao;

	@Reference
	private ExportRecordFacade exportRecordFacade;

	@Reference
	private DataDictionaryFacade dataDictionaryFacade;

	/**
	 * 创建用工企业后台功能
	 *
	 * @param function 功能
	 */
	public void create(EmployerFunction function) throws BizException {
		if (function.getCreateTime() == null) {
			function.setCreateTime(new Date());
		}
		if (function.getParentId() == null) {
			function.setParentId(0L);
		} else {
			EmployerFunction parent = employerFunctionDao.getById(function.getParentId());
			if (parent == null) {
				throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能父节点不存在");
			} else if (parent.getType() != PortalFunctionTypeEnum.MENU_TYPE.getValue()) {
				throw CommonExceptions.BIZ_INVALID.newWithErrMsg("父节点必须是菜单类型");
			}
		}

		try {
			employerFunctionDao.insert(function);
		} catch (DuplicateKeyException e) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("权限标识重复");
		} catch (Exception e) {
			log.error("创建用工企业后台功能：系统异常", e);
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("系统异常");
		}
	}

	/**
	 * 根据id查询用工企业后台功能
	 *
	 * @param id
	 */
	public EmployerFunction getById(long id) {
		return employerFunctionDao.getById(id);
	}

	/**
	 * 编辑用工企业后台功能
	 *
	 * @param function 功能
	 */
	public void update(EmployerFunction function) throws BizException {
		employerFunctionDao.update(function);
	}

	/**
	 * 根据id删除功能，并删除该功能与角色的映射
	 *
	 * @param id
	 */
	@Transactional
	public void deleteById(long id) throws BizException {
		EmployerFunction function = employerFunctionDao.getById(id);
		if (function == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能不存在");
		}

		List<EmployerFunction> subFunctions = employerFunctionDao.listByParentId(id);
		if (subFunctions.stream().anyMatch(p -> p.getType() == PortalFunctionTypeEnum.MENU_TYPE.getValue())) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("存在子菜单，该菜单不能直接删除");
		}

		List<Long> deleteIds = subFunctions.stream().map(EmployerFunction::getId).collect(Collectors.toList());
		deleteIds.add(id);
		employerFunctionDao.deleteByIds(deleteIds);				// 删除功能及其子功能
		employerRoleFunctionDao.deleteByFunctionIds(deleteIds);	// 删除功能与角色的映射
	}

	/**
	 * 查询所有功能
	 */
	public List<EmployerFunction> listAll() {
		return employerFunctionDao.listAll("number asc");
	}

	public List<EmployerFunction> listAll(Map<String, Object> param, PageParam pageParam) {
		PageResult<List<EmployerFunction>> result = employerFunctionDao.listPage(param, pageParam);
		return result == null ? null : result.getData();
	}

	public void export(FunctionVO employerFunction) {
		String fileNo = exportRecordFacade.genFileNo();
		ExportRecord record = ExportRecord.newDefaultInstance();
		record.setFileNo(fileNo);
		record.setOperatorLoginName(employerFunction.getOperateName());
		record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
		record.setFileName(ReportTypeEnum.MERCHANT_FUNCTION.getFileName());
		record.setReportType(ReportTypeEnum.MERCHANT_FUNCTION.getValue());
		Map<String, Object> paramMap = BeanUtil.toMap(employerFunction);
		record.setParamJson(JsonUtil.toString(paramMap));

		DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_FUNCTION.getDataName());
		if(dataDictionary == null){
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
		}
		List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
		dataInfo.forEach(x -> {
			ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
			info.setDataName(x.getFlag());
			info.setFieldCode(x.getCode());
			info.setFieldDesc(x.getDesc());
			record.getFieldInfoList().add(info);
		});
		exportRecordFacade.insert(record);
	}

	public PageResult<List<EmployerFunction>> listPage(Map<String, Object> param, PageParam pageParam) {
		return employerFunctionDao.listPage(param, pageParam);
	}

	public void saveFunction(List<EmployerFunction> list) {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (EmployerFunction item : list) {
			if (StringUtils.isBlank(item.getParentPermissionFlag())) {
				item.setParentId(0L);
				employerFunctionDao.insert("importFile", item);
				continue;
			}
			EmployerFunction employerFunction = employerFunctionDao.getOne(new HashMap<String, Object>(){{
				put("permissionFlag", item.getParentPermissionFlag());
			}});
			if (employerFunction != null) {
				item.setParentId(employerFunction.getId());
			}
			if (item.getParentId() == null) {
				log.error("parentId不能为空: [{}]", item.toString());
				throw CommonExceptions.PARAM_INVALID.newWithErrMsg("parentId不能为空");
			}
			employerFunctionDao.insert("importFile", item);
		}
	}


    public String getPermissionFlag(Long parentId) {
		return employerFunctionDao.getOne("getPermissionFlag", new HashMap<String, Object>(){{
			put("id", parentId);
		}});
    }
}
