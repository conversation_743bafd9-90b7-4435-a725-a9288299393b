package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.AgentBankAccount;
import com.zhixianghui.facade.merchant.service.AgentBankAccountFacade;
import com.zhixianghui.service.merchant.core.biz.AgentBankAccountBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 合伙人银行账户表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentBankAccountImpl implements AgentBankAccountFacade {

    private final AgentBankAccountBiz biz;

    @Override
    public AgentBankAccount getByAgentNo(String agentNo) {
        return biz.getByAgentNo(agentNo);
    }

}
