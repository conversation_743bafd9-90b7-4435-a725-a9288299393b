package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.WechatUserInfo;
import com.zhixianghui.facade.merchant.service.WechatUserInfoFacade;
import com.zhixianghui.facade.merchant.vo.WxBindMobileReqVo;
import com.zhixianghui.service.merchant.core.biz.WechatUserInfoService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class WechatUserInfoFacadeImpl implements WechatUserInfoFacade {
    @Autowired
    private WechatUserInfoService wechatUserInfoService;

    @Override
    public String getPhoneByOpenIdAndAppId(String openId, String appId) {
        return wechatUserInfoService.getPhoneByOpenIdAndAppId(openId, appId);
    }

    @Override
    public WechatUserInfo register(WxBindMobileReqVo wxBindMobileReqVo) {
        return wechatUserInfoService.register(wxBindMobileReqVo);
    }

    @Override
    public WechatUserInfo getMiniUserByOpenIdAndAppId(String openId, String appId) {
        return wechatUserInfoService.getMiniUserByOpenIdAndAppId(openId, appId);
    }
}
