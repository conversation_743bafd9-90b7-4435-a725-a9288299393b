package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.service.MerchantInfoChangeRecordFacade;
import com.zhixianghui.facade.merchant.vo.record.AgentBaseInfoVo;
import com.zhixianghui.facade.merchant.vo.record.AgentProductFeeVo;
import com.zhixianghui.facade.merchant.vo.record.MerchantBaseInfoVo;
import com.zhixianghui.facade.merchant.vo.record.MerchantProductFeeVo;
import com.zhixianghui.service.merchant.core.biz.MerchantInfoChangeRecordBiz;
import com.zhixianghui.service.merchant.facade.record.AgentInfoChange;
import com.zhixianghui.service.merchant.facade.record.AgentProductRuleChange;
import com.zhixianghui.service.merchant.facade.record.MerchantInfoChange;
import com.zhixianghui.service.merchant.facade.record.MerchantProductRuleChange;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;
import javax.annotation.Resource;
import java.util.List;

/**
 * 商户信息变更记录表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantInfoChangeRecordImpl implements MerchantInfoChangeRecordFacade {

    @Resource(name = "AgentInfoChange")
    private AgentInfoChange agentInfoChange;
    @Resource(name = "AgentProductRuleChange")
    private AgentProductRuleChange agentProductRuleChange;
    @Resource(name = "MerchantInfoChange")
    private MerchantInfoChange merchantInfoChange;
    @Resource(name = "MerchantProductRuleChange")
    private MerchantProductRuleChange merchantProductRuleChange;
    @Autowired
    private MerchantInfoChangeRecordBiz changeRecordBiz;


    @Override
    public List<MerchantInfoChangeRecord> list(Long flowId) {
        return changeRecordBiz.list(flowId);
    }

    @Override
    public List<MerchantInfoChangeRecord> list(String mchNo) {
        return changeRecordBiz.list(mchNo);
    }

    @Override
    public MerchantInfoChangeRecord getLastChange(String mchNo) {
        return changeRecordBiz.getLastChange(mchNo);
    }

    @Override
    public void deleteRecord(Long commonFlowId) {
        changeRecordBiz.deleteRecord(commonFlowId);
    }

    @Override
    public void update(Long commonFlowId) {
        List<MerchantInfoChangeRecord> list = list(commonFlowId);
        for (MerchantInfoChangeRecord record : list) {
            record.setOperate(OperationEnum.REVOCATION.getOperation());
        }
        changeRecordBiz.update(list);
    }

    @Override
    public <T> void record(T newInfo, T oldInfo, int source) {
        if (!newInfo.getClass().isAssignableFrom(oldInfo.getClass())) {
            log.error("旧数据类型 : {}, 新数据类型 : {}", oldInfo.getClass(), newInfo.getClass());
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("数据类型不一致");
        }
        if (AgentBaseInfoVo.class.isAssignableFrom(newInfo.getClass())) {
            agentInfoChange.handle((AgentBaseInfoVo) newInfo, (AgentBaseInfoVo) oldInfo, source);
            return;
        }
        if (AgentProductFeeVo.class.isAssignableFrom(newInfo.getClass())) {
            agentProductRuleChange.handle((AgentProductFeeVo) newInfo, (AgentProductFeeVo) oldInfo, source);
            return;
        }
        if (MerchantBaseInfoVo.class.isAssignableFrom(newInfo.getClass())) {
            merchantInfoChange.handle((MerchantBaseInfoVo) newInfo, (MerchantBaseInfoVo) oldInfo, source);
            return;
        }
        merchantProductRuleChange.handle((MerchantProductFeeVo) newInfo, (MerchantProductFeeVo) oldInfo, source);
    }
}
