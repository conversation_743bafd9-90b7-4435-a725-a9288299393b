package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.merchant.service.MerchantEmployerCooperateFacade;
import com.zhixianghui.facade.merchant.vo.EmployerCooperateUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerFullInfoUpdateVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerCooperateBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 用工商户合作信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantEmployerCooperateFacadeImpl implements MerchantEmployerCooperateFacade {

    @Autowired
    private MerchantEmployerCooperateBiz cooperateBiz;

    @Override
    public MerchantEmployerCooperate getByMchNo(String mchNo) {
        return cooperateBiz.getByMchNo(mchNo);
    }

    @Override
    public void update(EmployerCooperateUpdateVo vo, String loginName) throws BizException {
        cooperateBiz.update(vo, loginName);
    }


    @Override
    public void updateSpecifiedField(MerchantEmployerCooperate cooperate) {
        cooperateBiz.updateSpecifiedField(cooperate);
    }

    @Override
    public void updateAllMerchantMessage(EmployerFullInfoUpdateVo vo) throws BizException {
        cooperateBiz.updateAllMerchantMessage(vo);
    }

    @Override
    public void updateMainstayCooperate(MerchantUpdateVo merchantUpdateVo) {
        cooperateBiz.updateMainstayCooperate(merchantUpdateVo);
    }

    @Override
    public void updateMerchantCooperate(MerchantUpdateVo merchantUpdateVo) {
        cooperateBiz.updateMerchantCooperate(merchantUpdateVo);
    }
}
