package com.zhixianghui.service.merchant.core.biz.user.supplier;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.enums.user.supplier.SupplierInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.supplier.SupplierOperatorStatusEnum;
import com.zhixianghui.common.statics.enums.user.supplier.SupplierStaffTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.supplier.*;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.service.merchant.core.dao.MerchantDao;
import com.zhixianghui.service.merchant.core.dao.user.supplier.*;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* 供应商后台员工表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierStaffBiz {

    private final SupplierStaffDao supplierStaffDao;
    private final SupplierOperatorDao supplierOperatorDao;
    private final SupplierRoleDao supplierRoleDao;
    private final SupplierFunctionDao supplierFunctionDao;
    private final SupplierStaffRoleDao supplierStaffRoleDao;
    private final  SupplierTradePwdDao supplierTradePwdDao;
    private final MerchantDao merchantDao;
    private final RedisClient redisClient;

    /**
     * 根据id查询用工企业员工
     *
     * @param mchNo 	商户编号
     * @param id    	员工id
     */
    public SupplierStaffVO getById(String mchNo, long id) {
        if (StringUtil.isEmpty(mchNo)) {
            return null;
        }
        return supplierStaffDao.getVOByMchNoAndId(mchNo, id);
    }

    /**
     * 根据手机号查询用工企业员工
     * @param mchNo		商户编号
     * @param phone		手机号
     */
    public SupplierStaffVO getByPhone(String mchNo, String phone) {
        if (StringUtil.isEmpty(mchNo)) {
            return null;
        }
        return supplierStaffDao.getVOByMchAndPhone(mchNo, phone);
    }

    /**
     * 根据操作员id查询其关联的员工
     *
     * @param id
     * @return
     */
    public List<SupplierStaffVO> listByOperatorId(long id) {
        return supplierStaffDao.listVOByOperatorId(id);
    }

    /**
     * 根据手机号查询其关联的员工
     *
     * @param phone
     * @return
     */
    public List<SupplierStaffVO> listByPhone(String phone) {
        return supplierStaffDao.listVOByPhone(phone);
    }

    /**
     * 获取超级管理员
     *
     * @param mchNo 商户编号
     * @return
     */
    public SupplierStaffVO getAdmin(String mchNo) throws BizException {
        if (StringUtil.isEmpty(mchNo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户编号不能为空");
        }

        List<SupplierStaffVO> staffs = supplierStaffDao.getVOByMchNoAndType(mchNo, SupplierStaffTypeEnum.ADMIN.getValue());
        if (staffs == null || staffs.isEmpty()) {
            return null;
        } else if(staffs.size() > 1) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("存在多个超级管理员");
        } else {
            return staffs.get(0);
        }
    }

    /**
     * 创建用工企业员工
     *
     * @param supplierStaffVo vo
     */
    @Transactional
    public long create(SupplierStaffVO supplierStaffVo) throws BizException {
        SupplierStaffVO staffVO = supplierStaffDao.getVOByMchAndPhone(supplierStaffVo.getMchNo(), supplierStaffVo.getPhone());
        if (staffVO != null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工已存在");
        }
        if (supplierStaffVo.getType() == SupplierStaffTypeEnum.ADMIN.getValue() && getAdmin(supplierStaffVo.getMchNo()) != null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("已存在超级管理员");
        }

        SupplierOperator operator = supplierOperatorDao.getByPhone(supplierStaffVo.getPhone());
        // 如果不存在操作员帐号，则先创建操作员
        if (operator == null) {
            operator = createOperator(supplierStaffVo);
        }

        SupplierTradePwd tradePwd = supplierTradePwdDao.getByMchNo(supplierStaffVo.getMchNo());
        // 如果不存在商户支付，则先创建
        if (tradePwd == null) {
            createMchTradePwd(supplierStaffVo);
        }

        // 创建员工
        SupplierStaff staff = createStaff(operator.getId(), supplierStaffVo);

        //写入缓存
        redisClient.hset(PlatformSource.SUPPLIER.getValue() + ":" + operator.getId(),operator.getCacheMap());
        redisClient.hset(PlatformSource.SUPPLIER.getValue() + ":" + staff.getId() + ":" + staff.getMchNo(),staff.getCacheMap());

        return staff.getId();
    }

    /**
     * 创建用工企业员工并分配指定角色
     *
     * @param supplierStaffVo	vo
     * @param roleIds			角色id
     */
    @Transactional
    public long createAndAssignRole(SupplierStaffVO supplierStaffVo, List<Long> roleIds) throws BizException {
        long staffId = create(supplierStaffVo);

        // 为员工分配角色
        assignRole(supplierStaffVo.getMchNo(), staffId, roleIds);
        return staffId;
    }

    /**
     * 更换超级管理员
     *
     * @param mchNo         商户编号
     * @param newAdminPhone 新负责人手机号
     * @param newAdminName      新负责人姓名
     */
    @Transactional
    public void changeAdmin(String mchNo, String newAdminPhone, String newAdminName, String updator) throws BizException {
        SupplierStaffVO oldAdmin = getAdmin(mchNo);
        if (oldAdmin == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("超级管理员不存在");
        }

        // 原管理员绑定关系直接删除
        supplierStaffDao.deleteByMchNoAndId(mchNo, oldAdmin.getId());
        redisClient.del(PlatformSource.SUPPLIER.getValue() + ":" + oldAdmin.getId() + ":" + mchNo);

        SupplierStaffVO newAdmin = getByPhone(mchNo, newAdminPhone);
        if (newAdmin == null) {  // 新负责人帐号不存在则直接创建
            SupplierStaffVO supplierStaffVO = new SupplierStaffVO();
            supplierStaffVO.setCreateTime(new Date());
            supplierStaffVO.setCreator(updator);
            supplierStaffVO.setMchNo(mchNo);
            supplierStaffVO.setMchName(oldAdmin.getMchName());
            supplierStaffVO.setPhone(newAdminPhone);
            supplierStaffVO.setName(newAdminName);
            supplierStaffVO.setType(SupplierStaffTypeEnum.ADMIN.getValue());
            create(supplierStaffVO);
        } else {  // 新负责人修改为超级管理员
            supplierStaffDao.updateTypeById(newAdmin.getId(), SupplierStaffTypeEnum.ADMIN.getValue(), updator);
        }
    }

    /**
     * 为用工企业员工更新角色
     *
     * @param mchNo   商户编号
     * @param staffId 员工id
     * @param roleIds 角色id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(String mchNo, long staffId, List<Long> roleIds,String name) throws BizException {
        if (getById(mchNo, staffId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }

        // 删除员工原有的角色
        supplierStaffRoleDao.deleteByStaffId(staffId);

        //更新员工姓名
        SupplierStaff supplierStaff = supplierStaffDao.getById(staffId);
        supplierStaff.setName(name);
        supplierStaffDao.update(supplierStaff);

        // 分配角色
        assignRole(mchNo, staffId, roleIds);

        redisClient.hset(PlatformSource.SUPPLIER.getValue() + ":" + supplierStaff.getId() + ":" + supplierStaff.getMchNo(),supplierStaff.getCacheMap());
    }

    /**
     * 根据id删除用工企业员工
     *
     * @param mchNo 商户编号
     * @param id    员工id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String mchNo, long id) {
        SupplierStaffVO staff = getById(mchNo, id);
        if (staff == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        } else if (staff.getType() == SupplierStaffTypeEnum.ADMIN.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("超级管理员不能删除");
        }
        supplierStaffDao.deleteByMchNoAndId(mchNo, id);
        supplierStaffRoleDao.deleteByStaffId(staff.getId());

        redisClient.del(PlatformSource.SUPPLIER.getValue() + ":" + id + ":" + mchNo);
    }

    /**
     * 根据员工id获取其关联的角色
     *
     * @param mchNo   商户编号
     * @param staffId 员工id
     */
    public List<SupplierRole> getRoleByStaffId(String mchNo, long staffId) throws BizException {
        if (getById(mchNo, staffId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }
        return supplierRoleDao.listByStaffId(staffId);
    }

    /**
     * 查询员工所关联的功能
     *
     * @param mchNo   商户编号
     * @param staffId 员工id
     */
    public List<SupplierFunction> listFunctionByStaffId(String mchNo, long staffId) throws BizException {
        SupplierStaffVO staffVO = getById(mchNo, staffId);
        if (staffVO == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        } else if (staffVO.getType() == SupplierStaffTypeEnum.ADMIN.getValue()) {
            return supplierFunctionDao.listAll("number asc");
        } else {
            return supplierFunctionDao.listByStaffId(staffId);
        }
    }

    /**
     * 分页查询员工
     *
     * @param paramMap
     * @param pageParam
     */
    public PageResult<List<SupplierStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return supplierStaffDao.listStaffVOPage(paramMap, pageParam);
    }

    /**
     * 分页查询员工
     * @param mchNo
     * @param paramMap
     * @param pageParam
     * @return
     */
    public PageResult<List<SupplierStaffVO>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam) {
        if (paramMap == null) {
            paramMap = new HashMap<>();
        }
        paramMap.put("mchNo", mchNo);
        return supplierStaffDao.listStaffVOPage(paramMap, pageParam);
    }

    /**
     * 创建操作员
     */
    private SupplierOperator createOperator(SupplierStaffVO supplierStaffVO) {
        SupplierOperator operator = new SupplierOperator();
        operator.setCreateTime(new Date());
        operator.setPhone(supplierStaffVO.getPhone());
        operator.setName(StringUtil.isEmpty(supplierStaffVO.getName()) ? supplierStaffVO.getPhone() : supplierStaffVO.getName());  // 没传姓名则用手机号替代
        operator.setStatus(SupplierOperatorStatusEnum.ACTIVE.getValue());
        operator.setIsInitPwd(SupplierInitPwdStatusEnum.NO_INIT.getValue());
        operator.setSalt(null);
        operator.setPwd(null);
        operator.setModPwdTime(null);
        operator.setCurLoginTime(null);
        operator.setLastLoginTime(null);
        operator.setPwdErrorCount(0);
        operator.setPwdErrorTime(null);
        supplierOperatorDao.insert(operator);
        return operator;
    }

    /**
     * 创建员工
     */
    private SupplierStaff createStaff(Long operatorId, SupplierStaffVO supplierStaffVO) {
        SupplierStaff staff = new SupplierStaff();
        staff.setCreateTime(new Date());
        staff.setOperatorId(operatorId);
        staff.setMchNo(supplierStaffVO.getMchNo());
        staff.setMchName(supplierStaffVO.getMchName());
        // 设置供应商员工的类型
        staff.setType(supplierStaffVO.getType());
        staff.setCreator(supplierStaffVO.getCreator());
        staff.setName(supplierStaffVO.getName());
        supplierStaffDao.insert(staff);
        return staff;
    }

    /**
     * 创建商户支付密码
     */
    private SupplierTradePwd createMchTradePwd(SupplierStaffVO supplierStaffVO) {
        SupplierTradePwd tradePwd = new SupplierTradePwd();
        tradePwd.setCreateTime(new Date());
        tradePwd.setMchNo(supplierStaffVO.getMchNo());
        tradePwd.setStatus(SupplierOperatorStatusEnum.ACTIVE.getValue());
        tradePwd.setIsInitPwd(SupplierInitPwdStatusEnum.NO_INIT.getValue());
        tradePwd.setPwd(null);
        tradePwd.setPwdErrorCount(0);
        tradePwd.setPwdErrorTime(null);
        supplierTradePwdDao.insert(tradePwd);
        return tradePwd;
    }

    /**
     * 为员工分配角色
     */
    private void assignRole(String mchNo, long staffId, List<Long> roleIds) {
        if (roleIds != null && !roleIds.isEmpty()) {
            // 校验角色是否都属于该商户下
            Set<Long> roleIdSet = supplierRoleDao.listByMchNo(mchNo)
                    .stream().map(SupplierRole::getId).collect(Collectors.toSet());

            if (roleIds.stream().anyMatch(roleId -> !roleIdSet.contains(roleId))) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色id错误");
            }

            // 分配角色
            List<SupplierStaffRole> staffRoles = roleIds.stream().map(roleId -> {
                SupplierStaffRole staffRole = new SupplierStaffRole();
                staffRole.setCreateTime(new Date());
                staffRole.setStaffId(staffId);
                staffRole.setRoleId(roleId);
                return staffRole;
            }).collect(Collectors.toList());
            supplierStaffRoleDao.insert(staffRoles);
        }
    }

    public boolean isAdmin(SupplierStaffVO supplierStaffVO) {
        List<SupplierStaffRole> list = supplierStaffRoleDao.listBy(new HashMap<String, Object>() {{
            put("staffId", supplierStaffVO.getId());
        }});
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        for (SupplierStaffRole item : list) {
            SupplierRole supplierRole = supplierRoleDao.getById(item.getRoleId());
            if (supplierRole.getRoleType() == RoleTypeEnum.ADMIN.getType()) {
                return true;
            }
        }

        return false;
    }

    public List<SupplierFunction> listAllFunction() {
        return supplierFunctionDao.listAll();
    }

    public List<SupplierStaff> getAll() {
        return supplierStaffDao.listAll();
    }


    public List<String> getDistinctStaffByRoleIdAndMainstayNo(String mainstayNo, List<Long> roleIds) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mchNo",mainstayNo);
        paramMap.put("list",roleIds);
        return supplierStaffDao.getDistinctStaffByRoleIdAndMainstayNo(paramMap);
    }

    public void getAndPutStaffCache(Long id) {
        SupplierStaff supplierStaff = supplierStaffDao.getById(id);
        if (supplierStaff != null){
            redisClient.hset(PlatformSource.SUPPLIER.getValue() + ":" + supplierStaff.getId() + ":" + supplierStaff.getMchNo(),supplierStaff.getCacheMap());
        }
    }

    public void updateByMainStayNo(Merchant merchant,String currentOperator) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mchNo",merchant.getMchNo());
        //更新员工信息，注意版本
        List<SupplierStaff> supplierStaffList = supplierStaffDao.listBy(paramMap);
        supplierStaffList.stream().
                forEach(supplierStaff -> {
                    supplierStaff.setMchName(merchant.getMchName());
                    supplierStaff.setUpdateTime(new Date());
                    supplierStaff.setUpdator(currentOperator);
                });
        supplierStaffDao.update(supplierStaffList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeAdminAfterFlow(String mchNo, String contactPhone, String contactName, String updator) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商不存在");
        }
        merchant.setContactPhone(contactPhone);
        merchant.setContactName(contactName);
        merchantDao.update(merchant);
        changeAdmin(mchNo,contactPhone,contactName,updator);
    }
}