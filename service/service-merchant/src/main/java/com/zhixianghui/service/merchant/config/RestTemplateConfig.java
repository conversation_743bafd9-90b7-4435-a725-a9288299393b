package com.zhixianghui.service.merchant.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.GsonHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        this.customSetHttpMessageConverter(restTemplate);
        return restTemplate;
    }

    private void customSetHttpMessageConverter(RestTemplate restTemplate) {
        List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
        for (int i = 0; i < messageConverters.size(); i++) {

            //  1.重新设置StringHttpMessageConverter字符集为UTF-8，解决中文乱码问题
            if (StringHttpMessageConverter.class == messageConverters.get(i).getClass()) {
                restTemplate.getMessageConverters().set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }

            if (MappingJackson2HttpMessageConverter.class == messageConverters.get(i).getClass()) {
                GsonHttpMessageConverter gsonConverter = new GsonHttpMessageConverter();
                List<MediaType> supportedMediaTypes = new ArrayList<>();
                supportedMediaTypes.add(MediaType.APPLICATION_JSON);
                supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
                supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
                supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
                supportedMediaTypes.add(MediaType.APPLICATION_PDF);
                supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
                supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
                supportedMediaTypes.add(MediaType.APPLICATION_XML);
                supportedMediaTypes.add(MediaType.IMAGE_GIF);
                supportedMediaTypes.add(MediaType.IMAGE_JPEG);
                supportedMediaTypes.add(MediaType.IMAGE_PNG);
                supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
                supportedMediaTypes.add(MediaType.TEXT_HTML);
                supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
                supportedMediaTypes.add(MediaType.TEXT_PLAIN);
                supportedMediaTypes.add(MediaType.TEXT_XML);
                gsonConverter.setSupportedMediaTypes(supportedMediaTypes);
                restTemplate.getMessageConverters().set(i, gsonConverter);
            }
        }

    }
}
