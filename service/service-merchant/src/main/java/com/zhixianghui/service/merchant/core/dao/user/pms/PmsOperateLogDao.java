package com.zhixianghui.service.merchant.core.dao.user.pms;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog;
import org.springframework.stereotype.Repository;

/**
 * Author: Cmf
 * Date: 2019/11/1
 * Time: 15:49
 * Description:
 */
@Repository("pmsOperateLogDao")
public class PmsOperateLogDao extends MyBatisDao<PmsOperateLog, Long> {

    public PmsOperateLog createLog(PmsOperateLog operateLog){
        int insertedRow =this.getSqlSession().insert(fillSqlId("insertGetkey"), operateLog);
        if (insertedRow != 1) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("数据插入的记录数不为1");
        }
        return operateLog;
    }

}
