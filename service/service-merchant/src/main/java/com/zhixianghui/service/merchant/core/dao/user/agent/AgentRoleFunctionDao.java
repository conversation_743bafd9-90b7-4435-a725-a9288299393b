package com.zhixianghui.service.merchant.core.dao.user.agent;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRoleFunction;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 供应商后台角色功能关联表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class AgentRoleFunctionDao extends MyBatisDao<AgentRoleFunction,Long> {
    public void deleteByRoleId(long roleId) {
        super.deleteBy("deleteByRoleId", roleId);
    }

    public void deleteByFunctionIds(List<Long> functionIds) {
        super.deleteBy("deleteByFunctionIds", functionIds);
    }
}
