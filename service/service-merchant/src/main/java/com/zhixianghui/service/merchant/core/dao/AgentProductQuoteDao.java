package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 合伙人银行账户表 Mapper 接口
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Repository
public class AgentProductQuoteDao extends MyBatisDao<AgentProductQuote, Long> {
    public List<AgentProductQuote> listByAgentNo(String agentNo) {
        LimitUtil.notEmpty(agentNo, "合伙人编号不能为空");
        return listBy(Collections.singletonMap("agentNo", agentNo));
    }

    public void deleteByAgentNo(String agentNo) {
        LimitUtil.notEmpty(agentNo, "合伙人编号不能为空");
        deleteBy(Collections.singletonMap("agentNo", agentNo));
    }
}
