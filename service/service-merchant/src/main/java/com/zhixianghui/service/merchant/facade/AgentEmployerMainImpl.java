package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.AgentEmployerMain;
import com.zhixianghui.facade.merchant.service.AgentEmployerMainFacade;
import com.zhixianghui.service.merchant.core.biz.AgentEmployerMainBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 合伙人-企业主体信息表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentEmployerMainImpl implements AgentEmployerMainFacade {

    private final AgentEmployerMainBiz biz;

    @Override
    public AgentEmployerMain getByAgentNo(String agengNo) {
        return biz.getByAgentNo(agengNo);
    }
}
