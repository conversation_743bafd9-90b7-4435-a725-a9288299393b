package com.zhixianghui.service.merchant.facade.agent;

import com.zhixianghui.facade.merchant.entity.user.agent.AgentTradePwd;
import com.zhixianghui.facade.merchant.service.agent.AgentTradePwdFacade;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentTradePwdBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 供应商支付密码表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-14
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentTradePwdImpl implements AgentTradePwdFacade {

    private final AgentTradePwdBiz biz;

    /**
     * 根据商户编号查询
     *
     * @param agentNo
     */
    @Override
    public AgentTradePwd getByAgentNo(String agentNo) {
        return biz.getByAgentNo(agentNo);
    }

    /**
     * 创建
     *
     * @param tradePwd
     */
    @Override
    public void create(AgentTradePwd tradePwd) {
        biz.create(tradePwd);
    }


    /**
     * 更新
     *
     * @param tradePwd
     */
    @Override
    public void update(AgentTradePwd tradePwd) {
        biz.update(tradePwd);
    }
}
