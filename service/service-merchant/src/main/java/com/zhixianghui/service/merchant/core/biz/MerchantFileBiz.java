package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.service.merchant.core.dao.MerchantFileDao;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商户文件
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
@Slf4j
public class MerchantFileBiz {

    @Autowired
    private MerchantFileDao merchantFileDao;
    @Autowired
    private FastdfsClient fastdfsClient;

    public void insert(MerchantFile file) {
        merchantFileDao.insert(file);
    }

    public void insert(List<MerchantFile> fileList) {
        merchantFileDao.insert(fileList);
    }

    public void update(MerchantFile file) {
        merchantFileDao.update(file);
    }

    public List<MerchantFile> listBy(Map<String, Object> paramMap) {
        return merchantFileDao.listBy(paramMap);
    }

    public List<MerchantFile> listByMchNoAndFileType(String mchNo, Integer fileType) {
        Assert.hasText(mchNo, "商户编号不能为空");
        Assert.notNull(fileType, "文件类型不能为空");
        return  merchantFileDao.listByMchNoAndFileType(mchNo, fileType);
    }

    public void delete(List<MerchantFile> needDeleteList) {
        needDeleteList.forEach(x -> delete(x));
    }

    public void delete(MerchantFile file) {
        // 删除记录
        merchantFileDao.deleteById(file.getId());
    }

    /**
     * 更换文件
     * @param mchNo     商户号
     * @param mchName   商户名称
     * @param loginName 登录名
     * @param fileType  文件类型
     * @param fileUrls  文件路径
     */

    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;

    public void modifyFile(String mchNo, String mchName, String loginName, Integer fileType, List<String> fileUrls){
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            reallyHandle(mchNo, mchName, loginName, fileType, fileUrls);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("处理文件出错, 开始回滚");
        }
        platformTransactionManager.commit(transaction);
    }

    private void reallyHandle(String mchNo, String mchName, String loginName, Integer fileType, List<String> fileUrls) {
        List<MerchantFile> fileList = listByMchNoAndFileType(mchNo, fileType);
        if(CollectionUtils.isEmpty(fileUrls)){
            // 新上传的文件为空，则将原来文件全部删除
            delete(fileList);
        } else {
            List<String> needUploadUrlList = fileUrls;
            if(CollectionUtils.isNotEmpty(fileList)){
                List<String> fileSrcDB = fileList.stream().map(MerchantFile::getFileUrl).collect(Collectors.toList());
                needUploadUrlList = fileUrls.stream().filter(x -> !fileSrcDB.contains(x)).collect(Collectors.toList());

                // 待删除文件
                List<MerchantFile> needDeleteList = fileList.stream().filter(x -> !fileUrls.contains(x.getFileUrl()))
                        .collect(Collectors.toList());
                delete(needDeleteList);
            }
            log.info("更新人:" + loginName);
            // 待保存文件记录
            if(CollectionUtils.isNotEmpty(needUploadUrlList)){
                for(String fileUrl:needUploadUrlList){
                    if(StringUtil.isEmpty(fileUrl)){
                        continue;
                    }
                    MerchantFile file = new MerchantFile();
                    file.setUpdator(loginName);
                    file.setMchNo(mchNo);
                    file.setMchName(mchName);
                    file.setFileUrl(fileUrl);
                    file.setFileType(fileType);
                    file.setVersion(0);
                    file.setCreateTime(new Date());
                    merchantFileDao.insert(file);
                }
            }
        }
    }

    public List<MerchantFile> listByMchNo(String mchNo) {
        Assert.hasText(mchNo, "商户编号不能为空");
        return  merchantFileDao.listByMchNoAndFileType(mchNo, null);
    }
}
