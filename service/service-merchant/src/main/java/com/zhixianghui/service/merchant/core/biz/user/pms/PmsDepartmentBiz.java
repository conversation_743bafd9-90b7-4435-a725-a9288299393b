package com.zhixianghui.service.merchant.core.biz.user.pms;

import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentLeaderEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.service.merchant.core.dao.user.pms.PmsDepartmentDao;
import com.zhixianghui.service.merchant.core.dao.user.pms.PmsOperatorDao;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class PmsDepartmentBiz {

    @Autowired
    private PmsDepartmentDao pmsDepartmentDao;

    @Autowired
    private PmsOperatorDao pmsOperatorDao;

    /**
     * 全公司部门
     */
    private static PmsDepartment COMPANY = new PmsDepartment();

    static {
        COMPANY.setId(PmsDepartmentEnum.COMPANY.getId());
        COMPANY.setDepartmentName(PmsDepartmentEnum.COMPANY.getName());
        COMPANY.setLeaderId(PmsDepartmentLeaderEnum.UNALLOCATED.getId());
        COMPANY.setLeaderName(PmsDepartmentLeaderEnum.UNALLOCATED.getName());
    }

    public List<PmsDepartment> listDepartment() {
        List<PmsDepartment> pmsDepartments = pmsDepartmentDao.listAll();
        pmsDepartments.add(COMPANY);
        return pmsDepartments;
    }

    /**
     * 获取子级部门id，包括传进来的id
     * @param id
     * @return
     */
    public List<Long> listSubDepartment(Long id) {
        List<PmsDepartment> pmsDepartments = pmsDepartmentDao.listAll();
        List<Long> subDepartmentList = findSubDepartment(pmsDepartments, id);
        subDepartmentList.add(id);
        return subDepartmentList;
    }

    /**
     * 是否为子级部门或同部门
     * @param parentId 父级部门id
     * @param subId 子级部门id
     * @return
     */
    public boolean isSubOrSameDepartment(Long parentId, Long subId){
        LimitUtil.notEmpty(parentId, "parentId 不能为空");
        LimitUtil.notEmpty(subId, "subId 不能为空");
        if(parentId.equals(subId)){
            return true;
        }
        List<Long> subIds = listSubDepartment(parentId);
        return ArrayUtils.contains(subIds.toArray(), subId);
    }

    public List<PmsDepartment> listDepartmentByName(String departmentName) {
        return pmsDepartmentDao.listBy(Collections.singletonMap("departmentName", departmentName));
    }

    public PmsDepartment getDepartmentById(long id) {
        return pmsDepartmentDao.getById(id);
    }

    /**
     * 根据部门编号获取部门
     * @param number {@link PmsDepartmentNumberEnum#getNumber()}
     */
    public PmsDepartment getDepartmentByNumber(String number) {
        return pmsDepartmentDao.getOne(Collections.singletonMap("number", number));
    }

    /**
     * 根据部门编号获取部门
     * @param numbers {@link PmsDepartmentNumberEnum#getNumber()}
     */
    public List<PmsDepartment> getDepartmentByNumbers(List<String> numbers) {
        return pmsDepartmentDao.listBy(Collections.singletonMap("numbers", numbers));
    }

    /**
     * 创建部门
     */
    @Transactional
    public void createDepartment(PmsDepartment pmsDepartment) {
        // 设置上级部门
        if (pmsDepartment.getParentId() == null || pmsDepartment.getParentId() == PmsDepartmentEnum.COMPANY.getId()) {
            // 没有指定上级部门则默认上级部门为全公司
            pmsDepartment.setParentId(PmsDepartmentEnum.COMPANY.getId());
            pmsDepartment.setParentName(PmsDepartmentEnum.COMPANY.getName());
        } else {
            PmsDepartment parent = pmsDepartmentDao.getById(pmsDepartment.getParentId());
            if (parent == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("上级部门不存在");
            }
            pmsDepartment.setParentName(parent.getDepartmentName());
        }

        // 校验部门名称
        Map<String, Object> param = new HashMap<>();
        param.put("parentId", pmsDepartment.getParentId());
        param.put("departmentName", pmsDepartment.getDepartmentName());
        if (pmsDepartmentDao.countBy(param) > 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("部门名称重复");
        }

        // 检验            这次要修改的部门编号已经存在 不允许修改
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("number", pmsDepartment.getNumber());
        Long total = pmsDepartmentDao.getCountByNumber(paramMap);
        if (total>0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("序号已经存在，请重新编辑序号");
        }

        // 校验部门负责人
        PmsOperator leader = null;
        if (pmsDepartment.getLeaderId() == null || pmsDepartment.getLeaderId() == PmsDepartmentLeaderEnum.UNALLOCATED.getId()) {
            // 部门负责人为暂未指定
            pmsDepartment.setLeaderId(PmsDepartmentLeaderEnum.UNALLOCATED.getId());
            pmsDepartment.setLeaderName(PmsDepartmentLeaderEnum.UNALLOCATED.getName());
        } else {
            leader = pmsOperatorDao.getById(pmsDepartment.getLeaderId());
            if (leader == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("指定部门负责人不存在");
            }
            if (leader.getDepartmentId() != PmsDepartmentEnum.COMPANY.getId()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("指定部门负责人已经属于其他部门");
            }
            pmsDepartment.setLeaderName(leader.getRealName());
        }

        pmsDepartmentDao.insert(pmsDepartment);
        // 更新负责人的所属部门
        if (leader != null) {
            leader.setDepartmentId(pmsDepartment.getId());
            leader.setDepartmentName(pmsDepartment.getDepartmentName());
            pmsOperatorDao.update(leader);
        }
    }

    /**
     * 更新部门
     */
    public void updateDepartment(PmsDepartment department) {
        if (department.getId() == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("部门id不能为空");
        }

        PmsDepartment updateOne = pmsDepartmentDao.getById(department.getId());
        // 上级部门不允许修改
        updateOne.setParentId(null);
        updateOne.setParentName(null);

        //这次要修改的部门编号已经存在 不允许修改
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("number", department.getNumber());
        Long total = pmsDepartmentDao.getCountByNumber(paramMap);
        if (department.getId() !=updateOne.getId() && total>0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("序号已经存在，请重新编辑序号");
        }

        updateOne.setDepartmentName(department.getDepartmentName());
        updateOne.setNumber(department.getNumber());
        if (department.getLeaderId() == null) {  // 取消负责人
            updateOne.setLeaderId(PmsDepartmentLeaderEnum.UNALLOCATED.getId());
            updateOne.setLeaderName(PmsDepartmentLeaderEnum.UNALLOCATED.getName());
        } else {  // 校验负责人
            PmsOperator leader = validateLeader(department);
            updateOne.setLeaderId(department.getLeaderId());
            updateOne.setLeaderName(leader.getRealName());
        }
        pmsDepartmentDao.updateIfNotNull(updateOne);
    }

    /**
     * 删除部门
     */
    @Transactional
    public void deleteDepartmentById(long id) {
        if (pmsDepartmentDao.countBy(Collections.singletonMap("parentId", id)) > 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该部门有下级部门，无法删除");
        }
        pmsDepartmentDao.deleteById(id);
        // 原部门员工归入未分配部门中
        pmsOperatorDao.reallocateDepartment(id, PmsDepartmentEnum.UNALLOCATED.getId(), PmsDepartmentEnum.UNALLOCATED.getName());
    }

    /**
     * 查询未设置负责人的部门
     * @param departmentName    部门名称，可为 null
     */
    public List<PmsDepartment> listWithoutLeader(String departmentName) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("leaderId", PmsDepartmentLeaderEnum.UNALLOCATED.getId());
        if (StringUtil.isNotEmpty(departmentName)) {
            paramMap.put("departmentName", departmentName);
        }
        return pmsDepartmentDao.listBy(paramMap);
    }

    /**
     * 为部门设置负责人
     */
    public void assignLeader(long departmentId, long leaderId) {
        PmsDepartment department = pmsDepartmentDao.getById(departmentId);
        if (department == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("部门不存在");
        }

        department.setLeaderId(leaderId);
        PmsOperator leader = validateLeader(department);
        department.setLeaderName(leader.getRealName());
        pmsDepartmentDao.updateIfNotNull(department);
    }

    /**
     * 为部门移除负责人
     */
    public void removeLeader(long departmentId) {
        PmsDepartment department = pmsDepartmentDao.getById(departmentId);
        if (department == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("部门不存在");
        }

        department.setLeaderId(PmsDepartmentLeaderEnum.UNALLOCATED.getId());
        department.setLeaderName(PmsDepartmentLeaderEnum.UNALLOCATED.getName());
        pmsDepartmentDao.updateIfNotNull(department);
    }

    /**
     * 校验部门负责人
     */
    private PmsOperator validateLeader(PmsDepartment department) {
        PmsOperator leader = pmsOperatorDao.getById(department.getLeaderId());
        if (leader == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("指定负责人不存在");
        }
        if (!department.getId().equals(leader.getDepartmentId())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("指定负责人不属于该部门");
        }
        return leader;
    }

    /**
     * 获取子部门id
     * @param pmsDepartments
     * @param parentId
     * @return
     */
    private List<Long> findSubDepartment(List<PmsDepartment> pmsDepartments, Long parentId){
        List<Long> subDepartmentList = new ArrayList<>();
        for(PmsDepartment department : pmsDepartments){
            if(Objects.equals(department.getParentId(), parentId)){
                subDepartmentList.add(department.getId());
                subDepartmentList.addAll(findSubDepartment(pmsDepartments, department.getId()));
            }
        }
        return subDepartmentList;
    }
}
