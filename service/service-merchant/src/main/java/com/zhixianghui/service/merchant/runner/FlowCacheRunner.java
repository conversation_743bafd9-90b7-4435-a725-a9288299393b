package com.zhixianghui.service.merchant.runner;

import com.google.common.collect.Maps;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierOperator;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentOperatorBiz;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentStaffBiz;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperatorBiz;
import com.zhixianghui.service.merchant.core.biz.user.portal.EmployerOperatorBiz;
import com.zhixianghui.service.merchant.core.biz.user.portal.EmployerStaffBiz;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierOperatorBiz;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierStaffBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName  CacheRunner
 * @Description 缓存预热
 * @Date 2021/5/10 16:37
 */
@Component
public class FlowCacheRunner implements ApplicationRunner {

    @Autowired
    private SupplierOperatorBiz supplierOperatorBiz;

    @Autowired
    private EmployerOperatorBiz employerOperatorBiz;

    @Autowired
    private AgentOperatorBiz agentOperatorBiz;

    @Autowired
    private PmsOperatorBiz pmsOperatorBiz;

    @Autowired
    private AgentStaffBiz agentStaffBiz;

    @Autowired
    private EmployerStaffBiz employerStaffBiz;

    @Autowired
    private SupplierStaffBiz supplierStaffBiz;

    @Autowired
    private RedisClient redisClient;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        operatorCache();
        staffCache();
    }

    /**
     * 员工数据
     */
    private void staffCache() {
        agentStaffBiz.getAll().stream().forEach(agentStaff -> {
            redisClient.hset(PlatformSource.AGENT.getValue() + ":" + agentStaff.getId() + ":" + agentStaff.getAgentNo(),agentStaff.getCacheMap());
        });

        employerStaffBiz.getAll().stream().forEach(employerStaff -> {
            redisClient.hset(PlatformSource.MERCHANT.getValue() + ":" + employerStaff.getId() + ":" + employerStaff.getMchNo(),employerStaff.getCacheMap());
        });

        supplierStaffBiz.getAll().stream().forEach(supplierStaff -> {
            redisClient.hset(PlatformSource.SUPPLIER.getValue() + ":" + supplierStaff.getId() + ":" + supplierStaff.getMchNo(),supplierStaff.getCacheMap());
        });
    }

    /**
     * 操作员数据
     */
    private void operatorCache() {
        pmsOperatorBiz.getAll().stream().forEach(pmsOperator -> {
            try {
                redisClient.hset(PlatformSource.OPERATION.getValue() + ":" + pmsOperator.getId(),pmsOperator.getCacheMap());
            }catch (Exception e){
                e.printStackTrace();
            }
        });

        supplierOperatorBiz.getAll().stream().forEach(supplierOperator -> {
            redisClient.hset(PlatformSource.SUPPLIER.getValue() + ":" + supplierOperator.getId(),supplierOperator.getCacheMap());
        });

        agentOperatorBiz.getAll().stream().forEach(agentOperator -> {
            redisClient.hset(PlatformSource.AGENT.getValue() + ":" + agentOperator.getId(),agentOperator.getCacheMap());
        });

        employerOperatorBiz.getAll().stream().forEach(employerOperator -> {
            redisClient.hset(PlatformSource.MERCHANT.getValue() + ":" + employerOperator.getId(),employerOperator.getCacheMap());
        });
    }
}
