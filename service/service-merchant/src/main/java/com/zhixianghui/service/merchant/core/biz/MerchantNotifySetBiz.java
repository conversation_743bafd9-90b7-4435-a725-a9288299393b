package com.zhixianghui.service.merchant.core.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.merchant.entity.MerchantNotifySet;
import com.zhixianghui.facade.merchant.enums.MerchantNotifySetTypeEnum;
import com.zhixianghui.service.merchant.core.dao.mapper.MerchantNotifySetMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-07-05
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantNotifySetBiz extends ServiceImpl<MerchantNotifySetMapper, MerchantNotifySet> {

    public MerchantNotifySet getMchNoAndType(String mchNo, Integer value) {
        return this.getOne(new QueryWrapper<MerchantNotifySet>().lambda().
                eq(MerchantNotifySet::getMchNo,mchNo).eq(MerchantNotifySet::getNotifyType,value));
    }

    public List<MerchantNotifySet> getByMchNo(String mchNo) {
        return this.list(new QueryWrapper<MerchantNotifySet>().lambda().eq(MerchantNotifySet::getMchNo,mchNo));
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(String mchNo, List<MerchantNotifySet> merchantNotifySetList) {
        for (MerchantNotifySet merchantNotifySet : merchantNotifySetList) {
            merchantNotifySet.setCreateTime(new Date());
            merchantNotifySet.setVersion(0);
            merchantNotifySet.setMchNo(mchNo);
        }
        this.remove(new QueryWrapper<MerchantNotifySet>().lambda().eq(MerchantNotifySet::getMchNo,mchNo));
        this.saveBatch(merchantNotifySetList);
    }
}