package com.zhixianghui.service.merchant.helper;

import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.service.merchant.core.biz.AgentBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantSalerBiz;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperatorBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * 缓存
 * <AUTHOR>
 * @date 2020/9/7
 **/
@Component
public class CacheBiz {

    @Autowired
    private MerchantBiz merchantBiz;
    @Autowired
    private MerchantSalerBiz merchantSalerBiz;
    @Autowired
    private AgentBiz agentBiz;
    @Autowired
    private PmsOperatorBiz pmsOperatorBiz;

    @Cacheable(value = "redisCache", key = "targetClass + ':' + methodName + ':' + #mchNo")
    public Merchant getByMchNo(String mchNo){
        return merchantBiz.getByMchNo(mchNo);
    }

    @Cacheable(value = "redisCache", key = "targetClass + ':' + methodName + ':' + #mchNo")
    public MerchantSaler getMerchantSalerByMchNo(String mchNo){
        return merchantSalerBiz.getByMchNo(mchNo);
    }

    @Cacheable(value = "redisCache",key = "targetClass + ':' + methodName + ':' + #id")
    public PmsOperator getPmsOperatorById(String id){
        return pmsOperatorBiz.getOperatorById(Long.parseLong(id));
    }

    @Cacheable(value = "redisCache", key = "targetClass + ':' + methodName + ':' + #agentNo")
    public Agent getByAgentNo(String agentNo){
        return agentBiz.getByAgentNo(agentNo);
    }
}
