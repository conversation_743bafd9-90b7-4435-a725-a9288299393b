package com.zhixianghui.service.merchant.core.dao;

import com.google.common.collect.Maps;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.service.merchant.core.vo.PromotionDiffVo;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;


/**
 * 商户信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Repository
public class MerchantDao extends MyBatisDao<Merchant, Long> {

   public Merchant getByMchNo(String mchNo){
       if(StringUtil.isEmpty(mchNo)){
           return null;
       }
       Map<String, Object> param = new HashMap<>();
       param.put("mchNo", mchNo);
       return super.getOne(param);
   }

    /**
     * 作为合伙人被清除
     * 传入合伙人的直属商户合伙人关联将被清除
     * @param agentNo 合伙人编号
     */
    public void clearAsAgent(String agentNo) {
        LimitUtil.notEmpty(agentNo, "合伙人编号不能为空");
        update("clearAsAgent",agentNo);
    }

    public Long selectDateActiveMch(Map<String, Object> thisMonth) {
        return this.getSqlSession().selectOne(fillSqlId("selectDateActiveMch"),thisMonth);
    }

    public Map<String, Object> getAgentCount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectMap(fillSqlId("getAgentCount"),paramMap,"agentNo");
    }

    public Long selectAgentDateActiveMch(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("selectAgentDateActiveMch"),paramMap);
    }

    public PromotionDiffVo queryPromotion(String agentNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agentNo", agentNo);
        return this.getSqlSession().selectOne(fillSqlId("queryPromotion"),paramMap);
    }

    public DataDictionary getDataDictionaryByName(Map<String,Object> paramMap){
        return this.getSqlSession().selectOne(fillSqlId("getDataDictionaryByName"),paramMap);
    }
}
