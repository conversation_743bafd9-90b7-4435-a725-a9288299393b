package com.zhixianghui.service.merchant.facade.supplier;

import com.zhixianghui.facade.merchant.service.supplier.SupplierRoleFunctionFacade;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierRoleFunctionBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 供应商后台角色功能关联表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierRoleFunctionImpl implements SupplierRoleFunctionFacade {

    private final SupplierRoleFunctionBiz biz;
}
