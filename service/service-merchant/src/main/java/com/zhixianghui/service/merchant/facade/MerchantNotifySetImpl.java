package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantNotifySet;
import com.zhixianghui.facade.merchant.service.MerchantNotifySetFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantNotifySetBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-07-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantNotifySetImpl implements MerchantNotifySetFacade {

    private final MerchantNotifySetBiz biz;

    @Override
    public MerchantNotifySet getByMchNoAndType(String mchNo, Integer value) {
        return biz.getMchNoAndType(mchNo,value);
    }

    @Override
    public List<MerchantNotifySet> getByMchNo(String mchNo) {
        return biz.getByMchNo(mchNo);
    }

    @Override
    public void save(String mchNo, List<MerchantNotifySet> merchantNotifySetList) {
        biz.save(mchNo,merchantNotifySetList);
    }

}
