package com.zhixianghui.service.merchant.core.biz;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.service.merchant.core.dao.mapper.SalesLeadMapper;
import com.zhixianghui.facade.merchant.entity.SalesLead;
@Service
public class SalesLeadBiz extends ServiceImpl<SalesLeadMapper, SalesLead> {

    public void updateByCreateNo(SalesLead salesLead) {
        SalesLead oldData = this.getOne(new QueryWrapper<SalesLead>().eq("create_no", salesLead.getCreateNo()));
        salesLead.setId(oldData.getId());
        this.updateById(salesLead);
    }


    public IPage<SalesLead> listByPage(IPage page,SalesLead salesLead){

        int count = this.count(new QueryWrapper<SalesLead>()
                .eq(StrUtil.isNotBlank(salesLead.getAgentNo()), "agent_no", salesLead.getAgentNo())
                .like(StrUtil.isNotBlank(salesLead.getAgentName()), "agent_name", salesLead.getAgentName())
                .eq(StrUtil.isNotBlank(salesLead.getContactMobile()), salesLead.COL_CONTACT_MOBILE, salesLead.getContactMobile()));


        IPage<SalesLead> pageResult = this.page(page, new QueryWrapper<SalesLead>()
                .eq(StrUtil.isNotBlank(salesLead.getAgentNo()), "agent_no", salesLead.getAgentNo())
                .like(StrUtil.isNotBlank(salesLead.getAgentName()), "agent_name", salesLead.getAgentName())
                .eq(StrUtil.isNotBlank(salesLead.getContactMobile()), salesLead.COL_CONTACT_MOBILE, salesLead.getContactMobile())
                .orderByDesc(SalesLead.COL_CREATE_TIME)
        );

        pageResult.setTotal(count);
        return pageResult;
    }
}
