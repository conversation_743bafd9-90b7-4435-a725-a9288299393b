package com.zhixianghui.service.merchant.core.biz.user.portal;

import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRoleFunction;
import com.zhixianghui.facade.merchant.vo.EmployerRoleVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.service.merchant.core.dao.user.employer.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class EmployerRoleBiz{
	
	@Autowired
	private EmployerRoleDao employerRoleDao;

	@Autowired
	private EmployerFunctionDao employerFunctionDao;

	@Autowired
	private EmployerRoleFunctionDao employerRoleFunctionDao;

	@Autowired
	private EmployerStaffRoleDao employerStaffRoleDao;

	@Autowired
	private EmployerStaffDao employerStaffDao;

	/**
	 * 创建用工企业后台角色
	 *
	 * @param role 角色
	 */
	public void create(EmployerRole role) {
		if (role.getCreateTime() == null) {
			role.setCreateTime(new Date());
		}
		employerRoleDao.insert(role);
	}

	/**
	 * 根据id查询用工企业后台角色
	 *
	 * @param mchNo 商户编号
	 * @param id
	 */
	public EmployerRole getById(String mchNo, long id) {
		if (StringUtil.isEmpty(mchNo)) return null;

		EmployerRole role = employerRoleDao.getById(id);
		if (role == null) {
			return null;
		}
		if (String.valueOf(RoleTypeEnum.PRESET_EMPLOYER_NO.getType() ).equals(role.getMchNo())) {
			return role;
		}
		if (mchNo.equals(role.getMchNo())) {
			return role;
		}
		return null;
	}

	/**
	 * 更新用工企业后台角色
	 *
	 * @param role 角色
	 */
	public void update(EmployerRole role) throws BizException {
		if (role == null || role.getMchNo() == null || role.getId() == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户编号和id不能为空");
		}
		if (getById(role.getMchNo(), role.getId()) == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
		}

		employerRoleDao.updateByIdAndMchNo(role);
	}

	/**
	 * 根据id删除用工企业后台角色，并删除其与功能的关联
	 *
	 * @param mchNo  商户编号
	 * @param id	 角色id
	 */
	@Transactional
	public void deleteById(String mchNo, long id) throws BizException {
		if (getById(mchNo, id) == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
		}

		employerRoleDao.deleteById(id);  // 删除角色
		employerRoleFunctionDao.deleteByRoleId(id);  // 删除该角色与功能的关联
		employerStaffRoleDao.deleteByRoleId(id);  // 删除该角色与员工的关联
	}

	/**
	 * 为角色分配功能
	 *
	 * @param mchNo         商户编号
	 * @param roleId      角色id
	 * @param functionIds 功能id
	 */
	@Transactional
	public void updateFunction(String mchNo, long roleId, List<Long> functionIds) throws BizException {
		if (getById(mchNo, roleId) == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
		}

		// 先删除角色关联的所有功能
		employerRoleFunctionDao.deleteByRoleId(roleId);

		// 添加功能
		assignFunction(roleId, functionIds);
	}

	/**
	 * 根据角色id获取其关联的功能
	 *
	 * @param mchNo    商户编号
	 * @param roleId 角色id
	 */
	public List<EmployerFunction> listFunctionByRoleId(String mchNo, long roleId) throws BizException {
		if (getById(mchNo, roleId) == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
		}

		return employerFunctionDao.listByRoleId(roleId);
	}

	/**
	 * 根据角色id获取其关联的操作员
	 *
	 * @param mchNo    商户编号
	 * @param roleId 角色id
	 */
	public List<EmployerStaffVO> listStaffByRoleId(String mchNo, long roleId) throws BizException {
		if (getById(mchNo, roleId) == null) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
		}

		return employerStaffDao.listStaffVOByRoleId(roleId);
	}

	/**
	 * 查询所有角色
	 */
	public List<EmployerRole> listAll(String mchNo) {
		return employerRoleDao.listBy(Collections.singletonMap("mchNo", mchNo));
	}

	/**
	 * 分页查询角色
	 *
	 * @param mchNo
	 * @param paramMap
	 * @param pageParam
	 */
	public PageResult<List<EmployerRoleVo>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam) {
		if (paramMap == null) {
			paramMap = new HashMap<>();
		}
		paramMap.put("mchNo", mchNo);
		return employerRoleDao.listEmployerRoleVoPage(paramMap, pageParam);
	}

	public PageResult<List<EmployerRoleVo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
		if (paramMap == null) {
			paramMap = new HashMap<>();
		}
		return employerRoleDao.listPage("pmsListPage", "countEmployerRole", paramMap, pageParam);


	}

	/**
	 * 为角色分配功能
	 */
	private void assignFunction(long roleId, List<Long> functionIds) {
		if (functionIds != null && !functionIds.isEmpty()) {
			Set<Long> allFunctionIdSet = employerFunctionDao.listAll().stream().map(EmployerFunction::getId).collect(Collectors.toSet());
			// 校验功能id是否正确
			if (functionIds.stream().anyMatch(functionId -> !allFunctionIdSet.contains(functionId))) {
				throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能id错误");
			}

			List<EmployerRoleFunction> roleFunctions = functionIds.stream().map(functionId -> {
				EmployerRoleFunction roleFunction = new EmployerRoleFunction();
				roleFunction.setCreateTime(new Date());
				roleFunction.setRoleId(roleId);
				roleFunction.setFunctionId(functionId);
				return roleFunction;
			}).collect(Collectors.toList());
			employerRoleFunctionDao.insert(roleFunctions);
		}
	}


	public Long count(EmployerRoleVo employerRoleVo, String mchNo) {
		HashMap<String, Object> map = new HashMap<String, Object>(){{
			put("mchNo", mchNo);
			put("roleId", employerRoleVo.getId());
		}};
		return employerRoleDao.countBy("countByPresetRole", map);
	}
}
