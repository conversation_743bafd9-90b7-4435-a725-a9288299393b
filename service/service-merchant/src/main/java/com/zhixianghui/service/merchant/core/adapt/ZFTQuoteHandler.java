package com.zhixianghui.service.merchant.core.adapt;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.enums.merchant.DealStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.service.merchant.core.biz.MerchantZftQuoteBiz;
import com.zhixianghui.service.merchant.core.dao.MerchantDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerCooperateDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuoteDao;
import com.zhixianghui.service.merchant.core.dao.mapper.MerchantZftQuoteMapper;
import com.zhixianghui.service.merchant.core.util.BuildVoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ZFTQuoteHandler
 * @Description TODO
 * @Date 2023/5/9 11:21
 */
@Slf4j
@Component
public class ZFTQuoteHandler implements QuoteHandlerInterface {

    @Autowired
    private MerchantEmployerQuoteDao quoteDao;

    @Autowired
    private MerchantZftQuoteMapper merchantZftQuoteMapper;

    @Autowired
    private MerchantDao merchantDao;

    @Autowired
    private MerchantEmployerCooperateDao merchantEmployerCooperateDao;

    @Override
    public void createAfterFlow(MerchantFlowVo merchantFlowVo) {
        //删除旧的报价单（包括未生效的）
        Map<String,Object> maps = new HashMap<>();
        maps.put("productNo",merchantFlowVo.getProductNo());
        maps.put("mchNo",merchantFlowVo.getMchNo());
        quoteDao.listBy(maps).forEach(x-> merchantZftQuoteMapper.delete(new QueryWrapper<MerchantZftQuote>().lambda().eq(MerchantZftQuote::getQuoteId,x.getId())));
        quoteDao.deleteBy(maps);
        merchantFlowVo.setFlowId(merchantFlowVo.getCommonFlowId());

        //更新商户状态
        Merchant merchant = merchantDao.getByMchNo(merchantFlowVo.getMchNo());
        if (merchant.getDealStatus().intValue() == DealStatusEnum.UNDEAL.getValue()){
            merchant.setDealStatus(DealStatusEnum.DEAL.getValue());
            merchant.setUpdateTime(new Date());
            merchant.setUpdator(merchantFlowVo.getUpdator());
            merchantDao.update(merchant);
        }
        //增加新报价单
        this.createQuote(merchantFlowVo,MerchantQuoteStatusEnum.ACTIVE.getValue());

        //修改合作信息，增加smid
        MerchantEmployerCooperate merchantEmployerCooperate = merchantEmployerCooperateDao.getByMchNo(merchantFlowVo.getMchNo());
        merchantEmployerCooperate.setSmid(merchantFlowVo.getSmid());
        merchantEmployerCooperateDao.update(merchantEmployerCooperate);
    }

    @Override
    public void delete(Long quoteId) {
        quoteDao.deleteById(quoteId);
        merchantZftQuoteMapper.delete(new QueryWrapper<MerchantZftQuote>().lambda().eq(MerchantZftQuote::getQuoteId,quoteId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MerchantFlowVo merchantFlowVo) {
        this.createQuote(merchantFlowVo, MerchantQuoteStatusEnum.INACTIVE.getValue());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(MerchantEmployerQuote merchantEmployerQuote) {
        if (merchantEmployerQuote.getStatus().intValue() == MerchantQuoteStatusEnum.ACTIVE.getValue()){
            log.info("报价单已生效，无法删除");
            return;
        }
        this.delete(merchantEmployerQuote.getId());
    }

    @Override
    public void editQuote(MerchantFlowVo merchantFlowVo) {
        Map<String,Object> map = new HashMap<String,Object>(){{put("flowBusinessKey",merchantFlowVo.getCommonFlowId());}};
        MerchantEmployerQuote merchantEmployerQuote = quoteDao.getOne(map);

        if (merchantEmployerQuote == null){
            log.info("报价单不存在");
            return;
        }

        if (merchantEmployerQuote.getStatus().intValue() == MerchantQuoteStatusEnum.ACTIVE.getValue()){
            log.info("报价单已生效，无法修改");
            return;
        }
        merchantZftQuoteMapper.delete(new QueryWrapper<MerchantZftQuote>().lambda().eq(MerchantZftQuote::getQuoteId,merchantEmployerQuote.getId()));
        //只需要修改子表
        List<MerchantZftQuote> merchantZftQuoteList = BuildVoUtil.fillZftQuote(merchantFlowVo,merchantEmployerQuote);
        for (MerchantZftQuote merchantZftQuote : merchantZftQuoteList) {
            merchantZftQuoteMapper.insert(merchantZftQuote);
        }
    }

    private void createQuote(MerchantFlowVo merchantFlowVo, int status) {
        Map<String,Object> map = new HashMap<>();
        map.put("flowBusinessKey",merchantFlowVo.getFlowId());
        List<MerchantEmployerQuote> merchantEmployerQuoteList = quoteDao.listBy(map);
        if (merchantEmployerQuoteList != null && merchantEmployerQuoteList.size() > 0){
            log.info("报价单已存在...");
            return;
        }
        MerchantEmployerQuote merchantEmployerQuote = BuildVoUtil.fillEmployerQuote(merchantFlowVo,null, status);
        quoteDao.insert(merchantEmployerQuote);
        List<MerchantZftQuote> merchantZftQuoteList = BuildVoUtil.fillZftQuote(merchantFlowVo,merchantEmployerQuote);
        for (MerchantZftQuote merchantZftQuote : merchantZftQuoteList) {
            merchantZftQuoteMapper.insert(merchantZftQuote);
        }
    }
}
