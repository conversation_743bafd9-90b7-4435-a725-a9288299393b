package com.zhixianghui.service.merchant.core.biz;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.agent.AgentTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.fee.FormulaEnum;
import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentStaffTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.facade.fee.entity.AgentFeeRule;
import com.zhixianghui.facade.fee.entity.AgentProductRelation;
import com.zhixianghui.facade.fee.service.AgentFeeRuleFacade;
import com.zhixianghui.facade.fee.service.AgentProductRelationFacade;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.merchant.dto.AgentBaseInfoDto;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentStaff;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.PlatformSourceEnum;
import com.zhixianghui.facade.merchant.vo.WxAgentRegisterVo;
import com.zhixianghui.facade.merchant.vo.agent.*;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentOperatorBiz;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentStaffBiz;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperatorBiz;
import com.zhixianghui.service.merchant.core.dao.*;
import com.zhixianghui.service.merchant.core.dao.user.agent.AgentStaffDao;
import com.zhixianghui.service.merchant.core.util.BuildVoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.MERCHANT_AGENT_SEQ;


/**
 * <AUTHOR>
 * @description
 * @date 2021/2/2 17:40
 **/
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentManagerBiz {

    @Value("${agent.platform.link}")
    private String agentLink;

    private final AgentDao agentDao;
    private final AgentEmployerMainDao agentEmployerMainDao;
    private final AgentCredentialDao agentCredentialDao;
    private final AgentBankAccountDao agentBankAccountDao;
    private final AgentProductQuoteDao agentProductQuoteDao;
    private final MerchantFileBiz merchantFileBiz;
    private final AgentSalerDao agentSalerDao;
    private final MerchantDao merchantDao;
    private final MerchantSalerDao merchantSalerDao;
    private final AgentStaffDao agentStaffDao;
    private final AgentOperatorBiz agentOperatorBiz;

    private final AgentStaffBiz agentStaffBiz;
    private final PmsOperatorBiz pmsOperatorBiz;
    private final AgentProductQuoteBiz agentProductQuoteBiz;


    @Reference
    private ApprovalFlowFacade approvalFlowFacade;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private AgentFeeRuleFacade agentFeeRuleFacade;
    @Reference
    private AgentProductRelationFacade relationFacade;
    @Reference
    private RobotFacade robotFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    private static final String DEFAULT_SALER = "DEFAULT_SALER";


    @Transactional
    public void generateAgent(Agent agent, AgentSaler agentSaler) {
        log.info("[{}] 创建合伙人,名称:{},销售:{}", agent.getAgentNo(), agent.getAgentName(), agentSaler.getSalerName());
        //校验上级合伙人
        if (StringUtils.isNotBlank(agent.getInviterNo())) {
            Agent upAgent = agentDao.getByAgentNo(agent.getInviterNo());
            LimitUtil.notEmpty(upAgent, "指定邀请的合伙人不存在");
            if (Objects.equals(upAgent.getAgentStatus(), AgentStatusEnum.RETREAT.getValue())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("指定邀请的合伙人已被清退，请重新选择");
            }
        }

        agentDao.insert(agent);
        agentSalerDao.insert(agentSaler);
        //更新上级邀请数
        updateInvitationNum(agent.getInviterNo());

    }


    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public Long createAgent(Agent agent, AgentSaler agentSaler, String approvalContent) {
        log.info("[{}] 创建合伙人,名称:{},销售:{}", agent.getAgentNo(), agent.getAgentName(), agentSaler.getSalerName());

        //校验上级合伙人
        if (StringUtils.isNotBlank(agent.getInviterNo())) {
            Agent upAgent = agentDao.getByAgentNo(agent.getInviterNo());
            LimitUtil.notEmpty(upAgent, "指定邀请的合伙人不存在");
            if (Objects.equals(upAgent.getAgentStatus(), AgentStatusEnum.RETREAT.getValue())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("指定邀请的合伙人已被清退，请重新选择");
            }
        }

        agentDao.insert(agent);
        agentSalerDao.insert(agentSaler);
        //更新上级邀请数
        updateInvitationNum(agent.getInviterNo());
        //发起审批
        ApprovalFlow approvalFlow = BuildVoUtil.fillAgentApprovalFlow(agent, agentSaler, approvalContent);
        ApprovalInfoVo approvalInfoVo = approvalFlowFacade.createFlow(approvalFlow);
        return approvalInfoVo.getId();
    }


    @Transactional(rollbackFor = Exception.class)
    public void activeAgent(CommonFlow commonFlow) {
        String extInfo = commonFlow.getExtInfo();
        List<AgentProductQuoteVo> agentProductQuoteVos = JSON.parseArray(extInfo, AgentProductQuoteVo.class);

        for (AgentProductQuoteVo agentProductQuoteVo : agentProductQuoteVos) {
            if (!Objects.isNull(agentProductQuoteVo.getId())) {
                try {
                    AgentProductQuote quote = agentProductQuoteDao.getById(agentProductQuoteVo.getId());
                    if (quote != null) {
                        this.agentQuoteDel(quote.getAgentNo(),quote.getProductNo(),quote.getMainstayNo(), quote.getRuleType(), commonFlow.getInitiatorName());
                    }
                    agentProductQuoteDao.deleteById(agentProductQuoteVo.getId());
                } catch (BizException bizException) {
                    if (bizException.getSysErrorCode() == 100004) {
                        //do nothing
                    }else {
                        throw bizException;
                    }
                }
            }
            String agentNo = agentProductQuoteVo.getAgentNo();
            this.agentQuoteDel(agentNo, agentProductQuoteVo.getProductNo(), agentProductQuoteVo.getMainstayNo(),agentProductQuoteVo.getRuleType(), commonFlow.getInitiatorName());
        }

        for (AgentProductQuoteVo agentProductQuoteVo : agentProductQuoteVos) {
            List<AgentFeeRule> agentFeeRuleList = new ArrayList<>();
            List<AgentProductRelation> productQuoteList = new ArrayList<>();


            checkFeeRule(agentProductQuoteVo);
            AgentFeeRule rule = new AgentFeeRule();
            rule.setVersion(0);
            rule.setStatus(PublicStatus.ACTIVE);
            rule.setUpdateBy(commonFlow.getInitiatorName());
            rule.setCreateBy(commonFlow.getInitiatorName());
            rule.setCreateTime(new Date());
            rule.setRemoved(RemovedEnum.NORMAL.getValue());
            fillFeeRule(rule, agentProductQuoteVo);
            agentFeeRuleList.add(rule);
            AgentProductRelation relation = new AgentProductRelation();
            fillProduct(relation, agentProductQuoteVo,commonFlow);
            productQuoteList.add(relation);

            try {
                AgentProductQuote agentProductQuote = fillParam(agentProductQuoteVo, commonFlow.getInitiatorName(), commonFlow.getId());
                agentProductQuoteDao.insert(agentProductQuote);
            } catch (Exception e) {
                log.error("[添加产品报价记录]异常", e);
                throw CommonExceptions.BIZ_INVALID.newWith("[添加产品报价记录]异常", e);
            }

            agentFeeRuleFacade.save(agentFeeRuleList);
            relationFacade.insert(productQuoteList);

        }
        if (Objects.isNull(agentProductQuoteVos.get(0).getId())) {
            // 首次激活成功后台发送邮件提醒
            Agent agent = this.getByAgentNo(agentProductQuoteVos.get(0).getAgentNo());
            boolean isSendMail = false;
            if (agent.getAgentStatus().intValue() == AgentStatusEnum.CREATE.getValue()){
                isSendMail = true;
            }
            {
                Date cur = new Date();
                agent.setAgentStatus(AgentStatusEnum.ACTIVE.getValue());
                agent.setAuthStatus(AuthStatusEnum.SUCCESS.getValue());
                agent.setActiveTime(cur);
                agent.setUpdateTime(cur);
                agentDao.update(agent);
            }

            if (isSendMail && !StringUtil.isEmpty(agent.getContactEmail())) {
                EmailParamDto emailParamDto = new EmailParamDto();
                emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
                emailParamDto.setTo(agent.getContactEmail());
                emailParamDto.setSubject("【智享汇】智享汇综合服务平台帐号开通");
                emailParamDto.setTpl(EmailTemplateEnum.SUCCESS_CREATE_MERCHANT.getName());
                Map<String, Object> map = new HashMap<>();
                map.put("mchType", "合伙人");
                map.put("platformUrl", agentLink);
                map.put("merchantName", agent.getAgentName());
                map.put("username", agent.getAgentName());
                map.put("phone", agent.getContactPhone());
                emailParamDto.setTplParam(map);
                emailParamDto.setHtmlFormat(true);
                //异步发送邮件
                emailFacade.sendAsync(emailParamDto);
            }
        }
    }
    private AgentProductQuote fillParam(AgentProductQuoteVo quoteVo,String opt,Long flowId) {

        if (StringUtils.isNotBlank(quoteVo.getMainstayNo())&&StringUtils.isBlank(quoteVo.getMainstayName())) {
            Merchant maintay = merchantDao.getByMchNo(quoteVo.getMainstayNo());
            quoteVo.setMainstayName(maintay.getMchName());
        }

        AgentProductQuote agentProductQuote = new AgentProductQuote();
            agentProductQuote.setAgentNo(quoteVo.getAgentNo());
            agentProductQuote.setAgentName(quoteVo.getAgentName());
            agentProductQuote.setProductNo(quoteVo.getProductNo());
            agentProductQuote.setFeeRate(quoteVo.getFeeRate());
            agentProductQuote.setFixedFee(quoteVo.getFixedFee());
            agentProductQuote.setFormulaType(quoteVo.getFormulaType());
            agentProductQuote.setMainstayName(quoteVo.getMainstayName());
            agentProductQuote.setUpdater(opt);
            agentProductQuote.setMainstayNo(quoteVo.getMainstayNo());
            agentProductQuote.setStatus(CommonStatusEnum.ACTIVE.getValue());
            agentProductQuote.setFlowId(flowId);
            agentProductQuote.setCalculateMode(quoteVo.getCalculateMode());
            agentProductQuote.setProductName(quoteVo.getProductName());
            agentProductQuote.setRuleType(quoteVo.getRuleType());
            agentProductQuote.setRuleParam(JSONObject.toJSONString(quoteVo.getRuleParam()));
            agentProductQuote.setMainstayNo(quoteVo.getMainstayNo());
            agentProductQuote.setMainstayName(quoteVo.getMainstayName());
            agentProductQuote.setPriority(quoteVo.getPriority());
            agentProductQuote.setSecondFeeRate(quoteVo.getSecondFeeRate());
            agentProductQuote.setSecondFixedFee(quoteVo.getSecondFixedFee());
            agentProductQuote.setSecondFormulaType(quoteVo.getSecondFormulaType());
            agentProductQuote.setMaxFee(quoteVo.getMaxFee());
            agentProductQuote.setMinFee(quoteVo.getMinFee());
            agentProductQuote.setDescription(quoteVo.getDescription());
            agentProductQuote.setRealProfitRatio(quoteVo.getRealProfitRatio());

            Date now = new Date();
            agentProductQuote.setUpdateTime(now);
            agentProductQuote.setCreateTime(now);
            agentProductQuote.setVersion(0);

        return agentProductQuote;
    }

    @Transactional(rollbackFor = Exception.class)
    public void activeAgent(AgentVo vo) {
        log.info("激活合伙人:{}", JsonUtil.toString(vo));
        Agent agent = agentDao.getOne(Collections.singletonMap("agentNo", vo.getAgentNo()));
        if (agent == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询不到相应的合伙人,agentNo:" + vo.getAgentNo());
        }
        if (Objects.equals(agent.getAgentStatus(), AgentStatusEnum.ACTIVE.getValue())) {
            log.info("[{}]==>合伙人已激活不做处理", vo.getAgentNo());
            return;
        }
        Date cur = new Date();

        Integer agentType = agent.getAgentType();
        String updater = agent.getUpdater();

        String oldInviterNo = null;
        String newInviterNo = null;
        // 邀请人如果改了 旧的邀请人和新的邀请人要重新统计一遍
        if (!Objects.equals(agent.getInviterNo(), vo.getInviterNo())) {
            oldInviterNo = agent.getInviterNo();
            newInviterNo = vo.getInviterNo();
        }

        // 合伙人
        BuildVoUtil.fillAgent(agent, vo, cur);
        // 添加合伙人产品、添加合伙人计费规则
        List<AgentProductQuoteVo> list = vo.getQuoteVoList();
        List<AgentFeeRule> agentFeeRuleList = new ArrayList<>();
        List<AgentProductRelation> productQuoteList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (AgentProductQuoteVo quoteVo : list) {
                checkFeeRule(quoteVo);
                AgentFeeRule rule = new AgentFeeRule();
                rule.setVersion(0);
                rule.setStatus(PublicStatus.ACTIVE);
                rule.setUpdateBy(vo.getLoginName());
                rule.setCreateBy(vo.getLoginName());
                rule.setCreateTime(new Date());
                rule.setRemoved(RemovedEnum.NORMAL.getValue());
                fillFeeRule(rule, quoteVo, vo);
                agentFeeRuleList.add(rule);
                AgentProductRelation relation = new AgentProductRelation();
                fillProduct(relation, quoteVo, vo);
                productQuoteList.add(relation);
            }
            agentFeeRuleFacade.save(agentFeeRuleList);
            relationFacade.insert(productQuoteList);
        }


        // 主体
        AgentEmployerMain agentEmployerMain = BuildVoUtil.fillAgentEmployerMain(vo, agentType, cur, updater);
        // 银行卡信息
        AgentBankAccount agentBankAccount = BuildVoUtil.fillAgentBankAccount(vo, cur, updater);
        // 证件信息
        AgentCredential agentCredential = BuildVoUtil.fillAgentCredential(vo, cur, updater);
        // 报价表
        List<AgentProductQuote> quoteList = BuildVoUtil.fillAgentQuoteList(vo, cur, updater);
        // 文件处理
        List<MerchantFile> fileList = Lists.newArrayList();
        BuildVoUtil.fillAgentFile(fileList, vo, agentType, cur, updater);
        // 合伙人后台
        AgentStaffVO agentStaffVo = BuildVoUtil.fillSupplierStaffVO(agent, updater);
        activeAgent(agent, agentEmployerMain, agentCredential, agentBankAccount, quoteList, fileList, agentStaffVo, oldInviterNo, newInviterNo);

        // 激活成功后台发送邮件提醒
        if (!StringUtil.isEmpty(agent.getContactEmail())) {
            EmailParamDto emailParamDto = new EmailParamDto();
            emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
            emailParamDto.setTo(agent.getContactEmail());
            emailParamDto.setSubject("【智享汇】智享汇综合服务平台帐号开通");
            emailParamDto.setTpl(EmailTemplateEnum.SUCCESS_CREATE_MERCHANT.getName());
            Map<String, Object> map = new HashMap<>();
            map.put("mchType", "合伙人");
            map.put("platformUrl", agentLink);
            map.put("merchantName", agent.getAgentName());
            map.put("username", agent.getAgentName());
            map.put("phone", agent.getContactPhone());
            emailParamDto.setTplParam(map);
            emailParamDto.setHtmlFormat(true);
            //异步发送邮件
            emailFacade.sendAsync(emailParamDto);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveAgentExtInfo(AgentAddVo addVo) {
        log.info("合伙人附加信息:{}", JsonUtil.toString(addVo));
        Agent agent = agentDao.getOne(Collections.singletonMap("agentNo", addVo.getAgentNo()));
        if (agent == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询不到相应的合伙人,agentNo:" + addVo.getAgentNo());
        }
        if (Objects.equals(agent.getAgentStatus(), AgentStatusEnum.ACTIVE.getValue())) {
            log.info("[{}]==>合伙人已激活不做处理", addVo.getAgentNo());
            return;
        }

        AgentVo vo = new AgentVo();
        BeanUtil.copyProperties(addVo, vo);

        Date cur = new Date();

        Integer agentType = agent.getAgentType();
        String updater = agent.getUpdater();

        String oldInviterNo = null;
        String newInviterNo = null;
        // 邀请人如果改了 旧的邀请人和新的邀请人要重新统计一遍
        if (!Objects.equals(agent.getInviterNo(), vo.getInviterNo())) {
            oldInviterNo = agent.getInviterNo();
            newInviterNo = vo.getInviterNo();
        }

        // 合伙人
        {
            BeanUtils.copyProperties(vo, agent);
            agent.setAgentStatus(AgentStatusEnum.CREATE.getValue());
            agent.setAuthStatus(AuthStatusEnum.UN_AUTH.getValue());
            agent.setActiveTime(cur);
            agent.setUpdateTime(cur);
        }

        // 主体
        AgentEmployerMain agentEmployerMain = BuildVoUtil.fillAgentEmployerMain(vo, agentType, cur, updater);
        // 银行卡信息
        AgentBankAccount agentBankAccount = BuildVoUtil.fillAgentBankAccount(vo, cur, updater);
        // 证件信息
        AgentCredential agentCredential = BuildVoUtil.fillAgentCredential(vo, cur, updater);

        // 文件处理
        List<MerchantFile> fileList = Lists.newArrayList();
        BuildVoUtil.fillAgentFile(fileList, vo, agentType, cur, updater);
        // 合伙人后台
        AgentStaffVO agentStaffVo = BuildVoUtil.fillSupplierStaffVO(agent, updater);
        saveAgentInfo(agent, agentEmployerMain, agentCredential, agentBankAccount, null, fileList, agentStaffVo, oldInviterNo, newInviterNo);

        // 激活成功后台发送邮件提醒
        if (!StringUtil.isEmpty(agent.getContactEmail())) {
            EmailParamDto emailParamDto = new EmailParamDto();
            emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
            emailParamDto.setTo(agent.getContactEmail());
            emailParamDto.setSubject("【智享汇】智享汇综合服务平台帐号开通");
            emailParamDto.setTpl(EmailTemplateEnum.SUCCESS_CREATE_MERCHANT.getName());
            Map<String, Object> map = new HashMap<>();
            map.put("mchType", "合伙人");
            map.put("platformUrl", agentLink);
            map.put("merchantName", agent.getAgentName());
            map.put("username", agent.getAgentName());
            map.put("phone", agent.getContactPhone());
            emailParamDto.setTplParam(map);
            emailParamDto.setHtmlFormat(true);
            //异步发送邮件
            emailFacade.sendAsync(emailParamDto);
        }
    }

    private void fillProduct(AgentProductRelation relation, AgentProductQuoteVo quoteVo, AgentVo agentVo) {
        relation.setAgentNo(agentVo.getAgentNo());
        relation.setAgentName(agentVo.getAgentName());
        relation.setProductNo(quoteVo.getProductNo());
        relation.setProductName(quoteVo.getProductName());
        relation.setStatus(PublicStatus.ACTIVE);
        relation.setRemoved(RemovedEnum.NORMAL.getValue());
        relation.setUpdateBy(agentVo.getLoginName());
        relation.setUpdateTime(new Date());
        relation.setCreateBy(agentVo.getLoginName());
        relation.setCreateTime(new Date());
    }

    private void fillProduct(AgentProductRelation relation, AgentProductQuoteVo quoteVo,CommonFlow commonFlow) {
        relation.setAgentNo(quoteVo.getAgentNo());
        relation.setAgentName(quoteVo.getAgentName());
        relation.setProductNo(quoteVo.getProductNo());
        relation.setProductName(quoteVo.getProductName());
        relation.setStatus(PublicStatus.ACTIVE);
        relation.setRemoved(RemovedEnum.NORMAL.getValue());
        relation.setUpdateBy(commonFlow.getInitiatorName());
        relation.setUpdateTime(new Date());
        relation.setCreateBy(commonFlow.getInitiatorName());
        relation.setCreateTime(new Date());
    }

    private void fillFeeRule(AgentFeeRule rule, AgentProductQuoteVo vo, AgentVo agentVo) {
        rule.setAgentNo(agentVo.getAgentNo());
        rule.setAgentName(agentVo.getAgentName());
        rule.setProductNo(vo.getProductNo());
        rule.setProductName(vo.getProductName());
        rule.setDescription(vo.getDescription());
        rule.setUpdateTime(new Date());
        rule.setRuleType(vo.getRuleType());
        rule.setMaxFee(vo.getMaxFee());
        rule.setMinFee(vo.getMinFee());
        rule.setPriority(vo.getPriority());
        rule.setFirstFormulaType(vo.getFormulaType());
        rule.setSecondFormulaType(vo.getSecondFormulaType() == null ? FormulaEnum.RATE.getValue() : vo.getSecondFormulaType());
        // 一级分佣按比例收费
        if (vo.getFormulaType() == FormulaEnum.RATE.getValue()) {
            rule.setFirstFixedFee(null);
            rule.setFirstFeeRate(AmountUtil.div(vo.getFeeRate(), new BigDecimal(100)));
        }
        // 一级分佣按笔收费
        else if (vo.getFormulaType() == FormulaEnum.FIXED.getValue()) {
            rule.setFirstFixedFee(vo.getFixedFee());
            rule.setFirstFeeRate(null);
        }
        // 一级分佣按笔+比例收费
        else if (vo.getFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()) {
            rule.setFirstFixedFee(vo.getFixedFee());
            rule.setFirstFeeRate(AmountUtil.div(vo.getFeeRate(), new BigDecimal(100)));
        }

        // 特殊计费设置特殊匹配规则
        rule.setSpecialFeeRuleList(new ArrayList<>());
        if (vo.getRuleType() == RuleTypeEnum.SPECIAL.getValue() && org.apache.commons.collections.CollectionUtils.isNotEmpty(vo.getRuleParam())) {
            vo.getRuleParam().forEach(x -> {
                SpecialRuleDto specialRuleDto = new SpecialRuleDto();
                specialRuleDto.setSpecialRuleType(x.getSpecialRuleType());
                specialRuleDto.setCompareType(x.getCompareType());
                specialRuleDto.setValue(x.getValue());
                rule.getSpecialFeeRuleList().add(specialRuleDto);
            });
        }
        if (vo.getSecondFormulaType() == null) {
            return;
        }
        // 二级分佣按比例收费
        if (vo.getSecondFormulaType() == FormulaEnum.RATE.getValue()) {
            rule.setSecondFixedFee(null);
            rule.setSecondFeeRate(AmountUtil.div(vo.getSecondFeeRate(), new BigDecimal(100)));
        }
        // 二级分佣按笔收费
        else if (vo.getSecondFormulaType() == FormulaEnum.FIXED.getValue()) {
            rule.setSecondFixedFee(vo.getSecondFixedFee());
            rule.setSecondFeeRate(null);
        }
        // 二级分佣按笔+比例收费
        else if (vo.getSecondFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()) {
            rule.setSecondFixedFee(vo.getSecondFixedFee());
            rule.setSecondFeeRate(AmountUtil.div(vo.getSecondFeeRate(), new BigDecimal(100)));
        }


    }

    private void fillFeeRule(AgentFeeRule rule, AgentProductQuoteVo vo) {
        rule.setAgentNo(vo.getAgentNo());
        rule.setAgentName(vo.getAgentName());
        rule.setProductNo(vo.getProductNo());
        rule.setProductName(vo.getProductName());
        rule.setDescription(vo.getDescription());
        rule.setUpdateTime(new Date());
        rule.setCalculateMode(vo.getCalculateMode());
        rule.setRuleType(vo.getRuleType());
        rule.setMaxFee(vo.getMaxFee());
        rule.setMinFee(vo.getMinFee());
        rule.setPriority(vo.getPriority());
        rule.setFirstFormulaType(vo.getFormulaType());
        rule.setSecondFormulaType(vo.getSecondFormulaType() == null ? FormulaEnum.RATE.getValue() : vo.getSecondFormulaType());
        rule.setRealProfitRatio(vo.getRealProfitRatio());
        // 一级分佣按比例收费
        if (vo.getFormulaType() == FormulaEnum.RATE.getValue()) {
            rule.setFirstFixedFee(null);
            rule.setFirstFeeRate(AmountUtil.div(vo.getFeeRate(), new BigDecimal(100)));
        }
        // 一级分佣按笔收费
        else if (vo.getFormulaType() == FormulaEnum.FIXED.getValue()) {
            rule.setFirstFixedFee(vo.getFixedFee());
            rule.setFirstFeeRate(null);
        }
        // 一级分佣按笔+比例收费
        else if (vo.getFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()) {
            rule.setFirstFixedFee(vo.getFixedFee());
            rule.setFirstFeeRate(AmountUtil.div(vo.getFeeRate(), new BigDecimal(100)));
        }

        // 特殊计费设置特殊匹配规则
        rule.setSpecialFeeRuleList(new ArrayList<>());
        if (vo.getRuleType() == RuleTypeEnum.SPECIAL.getValue() && org.apache.commons.collections.CollectionUtils.isNotEmpty(vo.getRuleParam())) {
            vo.getRuleParam().forEach(x -> {
                SpecialRuleDto specialRuleDto = new SpecialRuleDto();
                specialRuleDto.setSpecialRuleType(x.getSpecialRuleType());
                specialRuleDto.setCompareType(x.getCompareType());
                specialRuleDto.setValue(x.getValue());
                rule.getSpecialFeeRuleList().add(specialRuleDto);
            });
        }
        if (vo.getSecondFormulaType() == null) {
            return;
        }
        // 二级分佣按比例收费
        if (vo.getSecondFormulaType() == FormulaEnum.RATE.getValue()) {
            rule.setSecondFixedFee(null);
            rule.setSecondFeeRate(AmountUtil.div(vo.getSecondFeeRate(), new BigDecimal(100)));
        }
        // 二级分佣按笔收费
        else if (vo.getSecondFormulaType() == FormulaEnum.FIXED.getValue()) {
            rule.setSecondFixedFee(vo.getSecondFixedFee());
            rule.setSecondFeeRate(null);
        }
        // 二级分佣按笔+比例收费
        else if (vo.getSecondFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()) {
            rule.setSecondFixedFee(vo.getSecondFixedFee());
            rule.setSecondFeeRate(AmountUtil.div(vo.getSecondFeeRate(), new BigDecimal(100)));
        }


    }

    private void checkFeeRule(AgentProductQuoteVo vo) {
        Assert.notNull(vo.getRuleType(), "规则参数不能为空");
        Assert.notNull(vo.getFormulaType(), "一级公式类型不能为空");
        if (Objects.equals(vo.getRuleType(), RuleTypeEnum.GENERAL.getValue())
                && vo.getPriority() != 1) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("通用规则优先级只能为1");
        }
        if (vo.getRuleType() == RuleTypeEnum.SPECIAL.getValue()) {
            LimitUtil.notEmpty(vo.getRuleParam(), "特殊规则不能为空");
        }

        // 一级分佣按比例收费
        if (vo.getFormulaType() == FormulaEnum.RATE.getValue()) {
            LimitUtil.notEmpty(vo.getFeeRate(), "一级分佣手续费费率不能为空");
        }
        // 一级分佣按笔收费
        else if (vo.getFormulaType() == FormulaEnum.FIXED.getValue()) {
            LimitUtil.notEmpty(vo.getFixedFee(), "一级分佣固定手续费不能为空");

        }
        // 一级分佣按笔+比例收费
        else if (vo.getFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()) {
            LimitUtil.notEmpty(vo.getFixedFee(), "一级分佣固定手续费不能为空");
            LimitUtil.notEmpty(vo.getFeeRate(), "一级分佣手续费费率不能为空");
        }

        // 二级分佣按比例收费
        if (vo.getSecondFormulaType() == null) {
            return;
        }
        if (vo.getSecondFormulaType() == FormulaEnum.RATE.getValue()) {
            LimitUtil.notEmpty(vo.getSecondFeeRate(), "二级分佣手续费费率不能为空");
        }
        // 二级分佣按笔收费
        else if (vo.getSecondFormulaType() == FormulaEnum.FIXED.getValue()) {
            LimitUtil.notEmpty(vo.getSecondFixedFee(), "二级分佣固定手续费不能为空");

        }
        // 二级分佣按笔+比例收费
        else if (vo.getSecondFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()) {
            LimitUtil.notEmpty(vo.getSecondFixedFee(), "二级分佣固定手续费不能为空");
            LimitUtil.notEmpty(vo.getSecondFeeRate(), "二级分佣手续费费率不能为空");
        }
    }

    public void activeAgent(Agent agent, AgentEmployerMain agentEmployerMain, AgentCredential agentCredential,
                            AgentBankAccount agentBankAccount, List<AgentProductQuote> quoteList,
                            List<MerchantFile> fileList, AgentStaffVO agentStaffVo, String oldInviterNo, String newInviterNo) {
        String logFlag = agent.getAgentNo() + "-" + agent.getAgentName();
        agentDao.update(agent);
        if (agentEmployerMain != null && Objects.equals(agent.getAgentType(), AgentTypeEnum.COMPANY.getValue())) {
            if (agentEmployerMainDao.getByAgentNo(agentEmployerMain.getAgentNo()) == null) {
                agentEmployerMainDao.insert(agentEmployerMain);
            } else {
                log.warn("[{}]==>保存主体信息表, 重复数据 : {}", logFlag, JSONObject.toJSON(agentEmployerMain));
            }
            log.info("[{}]==>保存主体信息表", logFlag);
        }
        if (agentCredential != null) {
            if (agentCredentialDao.getByAgentNo(agentCredential.getAgentNo()) == null) {
                agentCredentialDao.insert(agentCredential);
            } else {
                log.warn("[{}]==>保存证件信息表, 重复数据 : {}", logFlag, JSONObject.toJSON(agentEmployerMain));
            }

            log.info("[{}]==>保存证件信息表", logFlag);
        }
        if (agentBankAccount != null) {
            if (agentBankAccountDao.getByAgentNo(agentBankAccount.getAgentNo()) == null) {
                agentBankAccountDao.insert(agentBankAccount);
            } else {
                log.warn("[{}]==>保存银行卡信息, 重复数据 : {}", logFlag, JSONObject.toJSON(agentBankAccount));
            }
            log.info("[{}]==>保存银行卡信息", logFlag);
        }
        if (!CollectionUtils.isEmpty(quoteList)) {
            for (AgentProductQuote agentProductQuote : quoteList) {
                try {
                    agentProductQuoteDao.insert(agentProductQuote);
                } catch (Exception e) {
                    log.error("[{}]重复数据: {}", logFlag, JSONObject.toJSON(agentProductQuote), e);
                }
            }
            log.info("[{}]==>保存报价表", logFlag);
        }
        if (!CollectionUtils.isEmpty(fileList)) {
            merchantFileBiz.insert(fileList);
            log.info("[{}]==>保存文件表", logFlag);
        }

        //更新邀请数
        updateInvitationNum(oldInviterNo);
        updateInvitationNum(newInviterNo);
        //创建合伙人后台管理员
        agentStaffBiz.create(agentStaffVo);
    }

    public void saveAgentInfo(Agent agent, AgentEmployerMain agentEmployerMain, AgentCredential agentCredential,
                            AgentBankAccount agentBankAccount, List<AgentProductQuote> quoteList,
                            List<MerchantFile> fileList, AgentStaffVO agentStaffVo, String oldInviterNo, String newInviterNo) {
        String logFlag = agent.getAgentNo() + "-" + agent.getAgentName();
        agentDao.update(agent);
        if (agentEmployerMain != null && Objects.equals(agent.getAgentType(), AgentTypeEnum.COMPANY.getValue())) {
            if (agentEmployerMainDao.getByAgentNo(agentEmployerMain.getAgentNo()) == null) {
                agentEmployerMainDao.insert(agentEmployerMain);
            } else {
                log.warn("[{}]==>保存主体信息表, 重复数据 : {}", logFlag, JSONObject.toJSON(agentEmployerMain));
            }
            log.info("[{}]==>保存主体信息表", logFlag);
        }
        if (agentCredential != null) {
            if (agentCredentialDao.getByAgentNo(agentCredential.getAgentNo()) == null) {
                agentCredentialDao.insert(agentCredential);
            } else {
                log.warn("[{}]==>保存证件信息表, 重复数据 : {}", logFlag, JSONObject.toJSON(agentEmployerMain));
            }

            log.info("[{}]==>保存证件信息表", logFlag);
        }
        if (agentBankAccount != null) {
            if (agentBankAccountDao.getByAgentNo(agentBankAccount.getAgentNo()) == null) {
                agentBankAccountDao.insert(agentBankAccount);
            } else {
                log.warn("[{}]==>保存银行卡信息, 重复数据 : {}", logFlag, JSONObject.toJSON(agentBankAccount));
            }
            log.info("[{}]==>保存银行卡信息", logFlag);
        }

        if (!CollectionUtils.isEmpty(fileList)) {
            for (MerchantFile merchantFile : fileList) {
                List<MerchantFile> merchantFileList = merchantFileBiz.listByMchNoAndFileType(agent.getAgentNo(), merchantFile.getFileType());
                merchantFileBiz.delete(merchantFileList);
            }
            merchantFileBiz.insert(fileList);
        }

        //更新邀请数
        updateInvitationNum(oldInviterNo);
        updateInvitationNum(newInviterNo);
        //创建合伙人后台管理员
        agentStaffBiz.create(agentStaffVo);
    }

    public void updateInvitationNum(String agentNo) {
        if (StringUtils.isBlank(agentNo)) {
            return;
        }
        Agent agent = agentDao.getOne(Collections.singletonMap("agentNo", agentNo));
        if (agent == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("更新邀请数失败,找不到合伙人 agentNo:" + agentNo);
        }
        agentDao.updateInvitationNum(agentNo);
    }

    public void updateMerNum(String agentNo) {
        if (StringUtils.isBlank(agentNo)) {
            return;
        }
        Agent agent = agentDao.getOne(Collections.singletonMap("agentNo", agentNo));
        if (agent == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("更新直属商户数失败,找不到合伙人 agentNo:" + agentNo);
        }
        agentDao.updateMerNum(agentNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSetInviter(List<String> agentNoList, String inviterNo, String updater) {
        Agent inviter = null;
        //有设置的邀请方 校验
        if (StringUtils.isNotBlank(inviterNo)) {
            inviter = agentDao.getOne(Collections.singletonMap("agentNo", inviterNo));
            if (inviter == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询不到要设置的邀请方,inviterNo:" + inviterNo);
            }
            if (inviter.getAgentStatus() == AgentStatusEnum.RETREAT.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("要设置的邀请方已被清退,inviterNo:" + inviterNo);
            }
            if (inviter.getAgentStatus() == AgentStatusEnum.CREATE.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("要设置的邀请方为已创建状态,无法设为邀请方,inviterNo:" + inviterNo);
            }

            //设置的邀请方的上级邀请方 不可以是被设置方
            String upInviterNo = inviter.getInviterNo();
            if (upInviterNo != null && agentNoList.contains(upInviterNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不能互为邀请方,合伙人:[" + upInviterNo + "]已是[" + inviterNo + "]的邀请方");
            }
        }

        //查询要设置的合伙人
        List<Agent> agentList = agentDao.listBy(Collections.singletonMap("agentNoList", agentNoList));
        if (CollectionUtils.isEmpty(agentList)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询不到相应的合伙人");
        }

        //oldInviterList 保存旧的邀请人 统计也要更新
        List<String> oldInviterNoList = agentList.stream()
                .map(Agent::getInviterNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        String inviterAgentNo = inviter == null ? null : inviter.getAgentNo();
        String inviterAgentName = inviter == null ? null : inviter.getAgentName();
        agentList.forEach(
                agent -> {
                    agent.setInviterNo(inviterAgentNo);
                    agent.setInviterName(inviterAgentName);
                    agent.setUpdater(updater);
                    agent.setUpdateTime(new Date());
                }
        );
        agentDao.update(agentList);
        updateInvitationNum(inviterNo);
        //旧邀请人的统计更新
        oldInviterNoList.forEach(this::updateInvitationNum);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSetSeller(List<String> agentNoList, Long sellerId, String updater) {
        //查询要设置的合伙人-销售关系
        List<AgentSaler> list = agentSalerDao.listBy(Collections.singletonMap("agentNoList", agentNoList));
        if (CollectionUtils.isEmpty(list)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询不到相应的合伙人");
        }
        // 销售的信息
        PmsOperator operator = pmsOperatorBiz.getOperatorById(sellerId);
        if (operator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询要设置的销售,sellerId:" + sellerId);
        }
        list.forEach(
                agentSaler -> {
                    agentSaler.setSalerId(operator.getId());
                    agentSaler.setSalerName(operator.getRealName());
                    agentSaler.setSaleDepartmentId(operator.getDepartmentId());
                    agentSaler.setSaleDepartmentName(operator.getDepartmentName());
                    agentSaler.setUpdater(updater);
                    agentSaler.setUpdateTime(new Date());
                }
        );
        agentSalerDao.update(list);
        //todo 后续优化成连表查询
        List<Merchant> merchantList = merchantDao.listBy(Collections.singletonMap("agentNos", agentNoList));
        if (!CollectionUtils.isEmpty(merchantList)) {
            List<String> mchNos = merchantList.stream().map(Merchant::getMchNo).collect(Collectors.toList());
            List<MerchantSaler> merSalerList = merchantSalerDao.listBy(Collections.singletonMap("mchNos", mchNos));
            merSalerList.forEach(
                    merSaler -> {
                        merSaler.setSalerId(operator.getId());
                        merSaler.setSalerName(operator.getRealName());
                        merSaler.setSaleDepartmentId(operator.getDepartmentId());
                        merSaler.setSaleDepartmentName(operator.getDepartmentName());
                        merSaler.setUpdator(updater);
                        merSaler.setUpdateTime(new Date());
                    }
            );
            merchantSalerDao.update(merSalerList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(Agent agent) {
        if (Objects.equals(agent.getAgentStatus(), AgentStatusEnum.RETREAT.getValue())) {
            //清退 需要断开所有关系
            String inviterNo = agent.getInviterNo();
            String agentNo = agent.getAgentNo();
            agent.setInviterNo(null);
            agent.setInviterName(null);
            agent.setInvitationNum(0);
            agent.setMerNum(0);
            agentDao.update(agent);
            agent.setVersion(agent.getVersion() + 1);
            //下级商户、合伙人指向清空
            agentDao.clearAsInviter(agentNo);
            merchantDao.clearAsAgent(agentNo);
            //上级合伙人重新统计 先update agent
            updateInvitationNum(inviterNo);
        }
        agentDao.update(agent);
    }

    /**
     * 更新合伙人信息
     * 文件删除部分不做事务
     *
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAgentDetail(AgentVo vo) {
        log.info("更新人:" + vo.toString());
        Agent agent = agentDao.getByAgentNo(vo.getAgentNo());
        LimitUtil.notEmpty(agent, "合伙人编号不存在");
        String oldAgentNo = agent.getInviterNo();
        String inviterNo = vo.getInviterNo();
        if (StringUtils.isNotBlank(inviterNo)) {
            Agent upAgent = agentDao.getByAgentNo(inviterNo);
            LimitUtil.notEmpty(upAgent, "要设置的邀请方不存在");
            if (vo.getAgentNo().equals(upAgent.getInviterNo())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不能互为邀请方,合伙人:[" + vo.getAgentNo() + "]已是[" + vo.getInviterNo() + "]的邀请方");
            }
        }

        BeanUtil.copyProperties(vo, agent);
        agent.setUpdateTime(new Date());
        if (StringUtils.isNotBlank(vo.getTaxPercent())) {
            agent.setWithholdingTaxRatio(new BigDecimal(vo.getTaxPercent()));
        }
        agent.setUpdater(vo.getLoginName());
        agent.setAgentType(vo.getAgentType());
        agentDao.update(agent);
        //旧的去除
        updateInvitationNum(oldAgentNo);
        //新的增加
        updateInvitationNum(agent.getAgentNo());
        AgentBankAccount agentBankAccount = agentBankAccountDao.getByAgentNo(vo.getAgentNo());
        if (agentBankAccount != null) {
            BeanUtil.copyProperties(vo, agentBankAccount);
            agentBankAccount.setUpdateTime(new Date());
            agentBankAccount.setUpdater(vo.getLoginName());
            agentBankAccountDao.update(agentBankAccount);
        }


        AgentEmployerMain agentEmployerMain = agentEmployerMainDao.getByAgentNo(vo.getAgentNo());
        if (agentEmployerMain != null) {
            BeanUtil.copyProperties(vo, agentEmployerMain);
            agentEmployerMain.setUpdateTime(new Date());
            agentEmployerMain.setUpdater(vo.getLoginName());
            agentEmployerMainDao.update(agentEmployerMain);
        } else if (AgentTypeEnum.COMPANY.getValue() == vo.getAgentType()) {
            //主体信息
            agentEmployerMain = new AgentEmployerMain();
            agentEmployerMain.setUpdateTime(new Date());
            agentEmployerMain.setCreateTime(new Date());
            agentEmployerMain.setUpdater(vo.getLoginName());
            BeanUtils.copyProperties(vo, agentEmployerMain);
            agentEmployerMainDao.insert(agentEmployerMain);
        }

        AgentCredential agentCredential = agentCredentialDao.getByAgentNo(vo.getAgentNo());
        if (agentCredential != null) {
            BeanUtil.copyProperties(vo, agentCredential);
            agentCredential.setUpdateTime(new Date());
            agentCredential.setUpdater(vo.getLoginName());
            agentCredentialDao.update(agentCredential);
        }


        AgentSaler agentSaler = agentSalerDao.getByAgentNo(vo.getAgentNo());
        if (agentSaler != null) {
            BeanUtil.copyProperties(vo, agentSaler);
            agentSaler.setUpdateTime(new Date());
            agentSaler.setUpdater(vo.getLoginName());
            agentSalerDao.update(agentSaler);
        }
        if (!CollectionUtils.isEmpty(agentProductQuoteDao.listByAgentNo(vo.getAgentNo()))) {
            // 报价表
            List<AgentProductQuote> quoteList = vo.getQuoteVoList().stream().map(
                    quoteVo -> {
                        AgentProductQuote agentProductQuote = new AgentProductQuote();
                        agentProductQuote.setUpdateTime(new Date());
                        agentProductQuote.setCreateTime(new Date());
                        agentProductQuote.setUpdater(vo.getLoginName());
                        BeanUtils.copyProperties(quoteVo, agentProductQuote);
                        return agentProductQuote;
                    }
            ).collect(Collectors.toList());
            agentProductQuoteDao.deleteByAgentNo(vo.getAgentNo());
            agentProductQuoteDao.insert(quoteList);
        }


        try {
            if (StringUtils.isNotBlank(vo.getBusinessLicenseFileUrl())) {
                merchantFileBiz.modifyFile(vo.getAgentNo(), vo.getAgentName(), vo.getLoginName(), MerchantFileTypeEnum.BUSINESS_LICENSE.getValue(), Collections.singletonList(vo.getBusinessLicenseFileUrl()));
            }
            if (StringUtils.isNotBlank(vo.getIdCardEmblemFileUrl())) {
                merchantFileBiz.modifyFile(vo.getAgentNo(), vo.getAgentName(), vo.getLoginName(), MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue(), Collections.singletonList(vo.getIdCardEmblemFileUrl()));
            }
            if (StringUtils.isNotBlank(vo.getIdCardHeadFileUrl())) {
                merchantFileBiz.modifyFile(vo.getAgentNo(), vo.getAgentName(), vo.getLoginName(), MerchantFileTypeEnum.ID_CARD_HEAD.getValue(), Collections.singletonList(vo.getIdCardHeadFileUrl()));
            }
            if (StringUtils.isNotBlank(vo.getIdCardCopyFileUrl())) {
                merchantFileBiz.modifyFile(vo.getAgentNo(), vo.getAgentName(), vo.getLoginName(), MerchantFileTypeEnum.ID_CARD_COPY.getValue(), Collections.singletonList(vo.getIdCardCopyFileUrl()));
            }
        } catch (Exception e) {
            log.error("{} 合伙人文件更新失败：", vo.getAgentNo(), e);
        }
    }

    public Agent getByAgentNo(String agentNo) {
        return agentDao.getByAgentNo(agentNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void editAgentBaseInfo(AgentBaseInfoDto agentBaseInfoDto,PmsOperator pmsOperator) {
        Agent agent = agentDao.getByAgentNo(agentBaseInfoDto.getAgentNo());

        String oldInviterNo = null;
        String newInviterNo = null;
        // 邀请人如果改了 旧的邀请人和新的邀请人要重新统计一遍
        if (!Objects.equals(agent.getInviterNo(), agentBaseInfoDto.getInviterNo())) {
            oldInviterNo = agent.getInviterNo();
            newInviterNo = agentBaseInfoDto.getInviterNo();

            //更新邀请数
            updateInvitationNum(oldInviterNo);
            updateInvitationNum(newInviterNo);
        }

        BeanUtil.copyProperties(agentBaseInfoDto, agent);
        agent.setUpdater(pmsOperator.getLoginName());
        agent.setUpdateTime(new Date());

        agentDao.update(agent);

        AgentSaler agentSaler = agentSalerDao.getByAgentNo(agentBaseInfoDto.getAgentNo());
        PmsOperator operator = pmsOperatorBiz.getOperatorById(agentBaseInfoDto.getSalerId());

        agentSaler.setSalerId(agentBaseInfoDto.getSalerId());
        agentSaler.setSalerName(agentBaseInfoDto.getSalerName());
        agentSaler.setSaleDepartmentId(operator.getDepartmentId());
        agentSaler.setSaleDepartmentName(operator.getDepartmentName());
        agentSaler.setUpdater(pmsOperator.getLoginName());
        agentSaler.setUpdateTime(new Date());

        agentSalerDao.update(agentSaler);

        //修改其他表里的合伙人名称
        updateAgentName(agent.getAgentNo(),agent.getAgentName());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAgentName(String agentNo, String agentName) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("agentNo",agentNo);
        paramMap.put("agentName",agentName);
        //修改报价单
        agentProductQuoteBiz.updateByAgentNo(paramMap);
        //修改商户信息
        merchantDao.update("updateAgentNameByAgentNo",paramMap);
        //修改员工信息表
        agentStaffDao.update("updateByAgentNo",paramMap);
        //修改银行卡账户名称
        AgentBankAccount agentBankAccount = agentBankAccountDao.getByAgentNo(agentNo);
        if (agentBankAccount != null){
            agentBankAccount.setAccountName(agentName);
            agentBankAccountDao.update(agentBankAccount);
        }
        //修改其他表
        notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_BASE_EDIT, NotifyTypeEnum.AGENT_BASE_INFO_EDIT.getValue(),MessageMsgDest.TAG_AGENT_BASE_EDIT,JSON.toJSONString(paramMap));
    }

    @Transactional(rollbackFor = Exception.class)
    public void editAgentMainfo(CommonFlow commonFlow) {

        String extInfo = commonFlow.getExtInfo();
        AgentMainVo vo = JsonUtil.toBean(extInfo, AgentMainVo.class);

        Agent agent = agentDao.getByAgentNo(vo.getAgentNo());
        AgentVo agentVo = new AgentVo();
        BeanUtil.copyProperties(vo,agentVo);
        agentVo.setAgentType(agent.getAgentType());
        agentVo.setAgentName(agent.getAgentName());

        // 主体
        AgentEmployerMain agentEmployerMain = BuildVoUtil.fillAgentEmployerMain(agentVo, agent.getAgentType(), new Date(), commonFlow.getInitiatorName());
        // 证件信息
        AgentCredential agentCredential = BuildVoUtil.fillAgentCredential(agentVo, new Date(), commonFlow.getInitiatorName());
        // 文件处理
        List<MerchantFile> fileList = Lists.newArrayList();
        BuildVoUtil.fillAgentFile(fileList, agentVo, agent.getAgentType(),new Date(), commonFlow.getInitiatorName());

        if (!CollectionUtils.isEmpty(fileList)) {
            for (MerchantFile merchantFile : fileList) {
                List<MerchantFile> merchantFileList = merchantFileBiz.listByMchNoAndFileType(agent.getAgentNo(), merchantFile.getFileType());
                merchantFileBiz.delete(merchantFileList);
            }
            merchantFileBiz.insert(fileList);
        }

        if (agentEmployerMain != null && Objects.equals(agent.getAgentType(), AgentTypeEnum.COMPANY.getValue())) {
            if (agentEmployerMainDao.getByAgentNo(agentEmployerMain.getAgentNo()) == null) {
                agentEmployerMainDao.insert(agentEmployerMain);
            }else {
                Map<String, Object> param = new HashMap<>();
                param.put("agentNo", agent.getAgentNo());
                agentEmployerMainDao.deleteBy(param);
                agentEmployerMainDao.insert(agentEmployerMain);
            }
        }
        if (agentCredential != null) {
            if (agentCredentialDao.getByAgentNo(agentCredential.getAgentNo()) == null) {
                agentCredentialDao.insert(agentCredential);
            }else {
                Map<String, Object> param = new HashMap<>();
                param.put("agentNo", agent.getAgentNo());
                agentCredentialDao.deleteBy(param);
                agentCredentialDao.insert(agentCredential);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void editBankAcct(CommonFlow commonFlow) {
        String extInfo = commonFlow.getExtInfo();
        AgentBankAccount agentBankAccount = JsonUtil.toBean(extInfo, AgentBankAccount.class);

        Agent agent = agentDao.getByAgentNo(agentBankAccount.getAgentNo());
        if (agent == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【修改合伙人账户】合伙人" + agentBankAccount.getAgentNo() + "不存在");
        }

        // 银行卡信息
        AgentBankAccount agentBankAccountNew = BuildVoUtil.fillAgentBankAccount(agentBankAccount, new Date(), commonFlow.getInitiatorName());
        log.info("agentBankAccountNew:{}", JSON.toJSONString(agentBankAccountNew));
        if (agentBankAccountNew != null) {
            if (agentBankAccountDao.getByAgentNo(agentBankAccountNew.getAgentNo()) == null) {
                log.info("之前没有账户信息，直接插入");
                agentBankAccountDao.insert(agentBankAccountNew);
            } else {
                log.info("之前存在账户信息，先删除再插入");
                Map<String, Object> param = new HashMap<>();
                param.put("agentNo", agent.getAgentNo());
                log.info("删除旧数据");
                agentBankAccountDao.deleteBy(param);
                log.info("插入新数据");
                agentBankAccountDao.insert(agentBankAccountNew);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void setAgentSeller(CommonFlow commonFlow) {

        SetSellerVo setSellerVo = JSONUtil.toBean(commonFlow.getExtInfo(), SetSellerVo.class);

        AgentSaler agentSaler = agentSalerDao.getByAgentNo(setSellerVo.getAgentNo());

        PmsOperator operator = pmsOperatorBiz.getOperatorById(setSellerVo.getSellerId());

        agentSaler.setSalerId(setSellerVo.getSellerId());
        agentSaler.setSalerName(operator.getRealName());
        agentSaler.setSaleDepartmentName(operator.getDepartmentName());
        agentSaler.setSaleDepartmentId(operator.getDepartmentId());
        agentSaler.setUpdater(commonFlow.getInitiatorName());
        agentSaler.setUpdateTime(new Date());

        agentSalerDao.update(agentSaler);

        //涉及到递归问题，人工处理

//        //更换该合伙人下边的销售
//        List<Agent> subAgents = agentDao.listBy(MapUtil.builder(new HashMap<String, Object>()).put("inviterNo", setSellerVo.getAgentNo()).build());
//        if (subAgents != null) {
//            List<AgentSaler> salers = new ArrayList<>();
//            for (Agent subAgent : subAgents) {
//                AgentSaler subAgentSaler = agentSalerDao.getByAgentNo(subAgent.getAgentNo());
//                subAgentSaler.setSalerId(setSellerVo.getSellerId());
//                subAgentSaler.setSalerName(operator.getRealName());
//                subAgentSaler.setSaleDepartmentName(operator.getDepartmentName());
//                subAgentSaler.setSaleDepartmentId(operator.getDepartmentId());
//                subAgentSaler.setUpdater(commonFlow.getInitiatorName());
//                subAgentSaler.setUpdateTime(new Date());
//
//                salers.add(subAgentSaler);
//            }
//            if (!salers.isEmpty()) {
//                agentSalerDao.update(salers);
//            }
//        }

        changeMerchantSaler(agentSaler);

    }

    @Transactional(rollbackFor = Exception.class)
    void changeMerchantSaler(AgentSaler agentSaler) {
        List<MerchantSaler> merchantSalerList = merchantSalerDao.listBy("getByAgentNo",new HashMap<String,Object>(){
            private static final long serialVersionUID = 5425570602031949731L;

            {put("agentNo",agentSaler.getAgentNo());}});
        merchantSalerList.forEach(x->{
            x.setSaleDepartmentId(agentSaler.getSaleDepartmentId());
            x.setSaleDepartmentName(agentSaler.getSaleDepartmentName());
            x.setSalerId(agentSaler.getSalerId());
            x.setSalerName(agentSaler.getSalerName());
            x.setUpdateTime(agentSaler.getUpdateTime());
            x.setUpdator(agentSaler.getUpdater());
        });
        merchantSalerDao.update(merchantSalerList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void setAgentInviter(CommonFlow commonFlow) {
        SetInviterVo inviterVo = JSONUtil.toBean(commonFlow.getExtInfo(), SetInviterVo.class);
        Agent agent = agentDao.getByAgentNo(inviterVo.getAgentNo());

        Agent inviterAgent = agentDao.getByAgentNo(inviterVo.getInviterNo());
        if (Objects.isNull(inviterAgent)) {
            log.error("邀请人信息异常:邀请人 {}", inviterVo.getInviterNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("邀请人信息异常:邀请人不存在");
        }

        agent.setUpdateTime(new Date());
        agent.setUpdater(commonFlow.getInitiatorName());
        agent.setInviterNo(inviterVo.getInviterNo());
        agent.setInviterName(inviterAgent.getAgentName());

        agentDao.update(agent);

        //更新邀请数
        updateInvitationNum(inviterVo.getInviterNo());
        updateInvitationNum(inviterVo.getBeforeInviterNo());
    }

    @Transactional(rollbackFor = Exception.class)
    public void setAgentPrincipal(CommonFlow commonFlow) {
        PrincipalEditVo principalEditVo = JSONUtil.toBean(commonFlow.getExtInfo(), PrincipalEditVo.class);
        Agent agent = agentDao.getByAgentNo(principalEditVo.getAgentNo());

        //更新操作员职工表
        //1. 获取操作员
        AgentOperator agentOperator = agentOperatorBiz.getByPhone(principalEditVo.getContactPhone());
        if (agentOperator == null) {
            //去掉老的staff
            AgentStaffVO staffVO = agentStaffBiz.getByPhone(agent.getAgentNo(), agent.getContactPhone());
            agentStaffBiz.forceDelete(agent.getAgentNo(),staffVO.getId());

            //操作员不存在，则创建
            AgentStaffVO agentStaffVO = new AgentStaffVO();
            agentStaffVO.setAgentNo(principalEditVo.getAgentNo())
                    .setAgentName(agent.getAgentName())
                    .setCreateTime(new Date())
                    .setName(principalEditVo.getContactName())
                    .setOperatorName(principalEditVo.getContactName())
                    .setPhone(principalEditVo.getContactPhone())
                    .setType(AgentStaffTypeEnum.ADMIN.getValue())
                    .setUpdateTime(new Date())
                    .setUpdator(commonFlow.getInitiatorName())
                    .setCreator(commonFlow.getInitiatorName());
            agentStaffBiz.create(agentStaffVO);

        } else {
            //2. 获取该合伙人所属职工
            String contactPhone = agent.getContactPhone();
            AgentStaffVO staffVO = agentStaffBiz.getByPhone(agent.getAgentNo(), contactPhone);
            AgentStaff agentStaff = agentStaffDao.getById(staffVO.getId());
            agentStaff.setUpdateTime(new Date());
            agentStaff.setUpdator(commonFlow.getInitiatorName());
            agentStaff.setOperatorId(agentOperator.getId());
            agentStaffDao.update(agentStaff);
        }

        agent.setUpdateTime(new Date());
        agent.setUpdater(commonFlow.getInitiatorName());
        agent.setContactPhone(principalEditVo.getContactPhone());
        agent.setContactName(principalEditVo.getContactName());
        agent.setContactEmail(principalEditVo.getContactEmail());

        agentDao.update(agent);
    }

    @Transactional(rollbackFor = Exception.class)
    public void agentQuoteDel(CommonFlow commonFlow) {
        AgentQuoteDelVo agentQuoteDelVo = JSONUtil.toBean(commonFlow.getExtInfo(), AgentQuoteDelVo.class);

        List<Map<String, Object>> quoteRateList = (List<Map<String, Object>>) agentQuoteDelVo.getDetail().get("quoteRateList");

        for (Map<String, Object> item : quoteRateList) {
            Integer id = (Integer) item.get("id");

            AgentProductQuote agentProductQuote = agentProductQuoteDao.getById(Long.valueOf(id));
            if (agentProductQuote.getRuleType() == 0) {
                // 删除计费规则
                Map<String, Object> delParam = new HashMap<>();
                delParam.put("agentNo", agentQuoteDelVo.getAgentNo());
                delParam.put("productNo", agentQuoteDelVo.getProductNo());
                delParam.put("ruleType", agentProductQuote.getRuleType());
                agentFeeRuleFacade.deleteByParam(delParam,commonFlow.getInitiatorName());
            }else {

                String mainstayNo = null;
                String ruleParam = agentProductQuote.getRuleParam();

                List<SpecialFeeRuleVo> ruleObjects = JSON.parseArray(ruleParam, SpecialFeeRuleVo.class);
                for (SpecialFeeRuleVo ruleObject : ruleObjects) {
                    if (ruleObject.getSpecialRuleType() == 1) {
                        mainstayNo = ruleObject.getValue();
                    }
                }
                Map<String, Object> param = MapUtil.builder(new HashMap<String, Object>())
                        .put("agentNo", agentQuoteDelVo.getAgentNo())
                        .put("productNo", agentQuoteDelVo.getProductNo())
                        .put("mainstayNo", agentQuoteDelVo.getMainstayNo())
                        .put("ruleType", agentQuoteDelVo.getRuleType()).build();
                List<AgentFeeRule> agentFeeRuleList = agentFeeRuleFacade.listByParam(param);

                for (AgentFeeRule agentFeeRule : agentFeeRuleList) {
                    List<SpecialRuleDto> specialFeeRuleList = agentFeeRule.getSpecialFeeRuleList();
                    for (SpecialRuleDto specialRuleDto : specialFeeRuleList) {
                        if (specialRuleDto.getSpecialRuleType() == 1 && StringUtils.equals(specialRuleDto.getValue(),mainstayNo)) {
                            Long fid = agentFeeRule.getId();
                            agentFeeRuleFacade.deleteById(fid, commonFlow.getInitiatorName());
                        }
                    }
                }
            }


            agentProductQuoteDao.deleteById(Long.valueOf(id));

        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void agentQuoteDel(String agentNo,String productNo,String mainstayNo,Integer ruleType,String opt) {

        Map<String, Object> param = MapUtil.builder(new HashMap<String, Object>())
                .put("agentNo", agentNo)
                .put("productNo", productNo)
                .put("mainstayNo",mainstayNo)
                .put("ruleType",ruleType).build();

        List<AgentProductQuote> agentProductQuotes = agentProductQuoteDao.listBy(param);
        for (AgentProductQuote agentProductQuote : agentProductQuotes) {
            if (agentProductQuote.getRuleType() == 0) {
                Map<String, Object> delParam = new HashMap<>();
                delParam.put("agentNo", agentNo);
                delParam.put("productNo", productNo);
                delParam.put("ruleType", agentProductQuote.getRuleType());
                // 删除计费规则
                agentFeeRuleFacade.deleteByParam(delParam,opt);
            }else {
                List<AgentFeeRule> agentFeeRuleList = agentFeeRuleFacade.listByParam(param);

                for (AgentFeeRule agentFeeRule : agentFeeRuleList) {
                    List<SpecialRuleDto> specialFeeRuleList = agentFeeRule.getSpecialFeeRuleList();
                    for (SpecialRuleDto specialRuleDto : specialFeeRuleList) {
                        if (specialRuleDto.getSpecialRuleType() == 1 && StringUtils.equals(specialRuleDto.getValue(),mainstayNo)) {
                            Long id = agentFeeRule.getId();
                            agentFeeRuleFacade.deleteById(id, opt);
                        }
                    }
                }
            }
        }
//        param.put("status", CommonStatusEnum.INACTIVE.getValue());
        agentProductQuoteDao.deleteBy(param);
    }

    @Transactional(rollbackFor = Exception.class)
    public void agentQuoteDelByFlowId(String flowId) {

        Map<String, Object> param = MapUtil.builder(new HashMap<String, Object>()).put("flowId", flowId).build();
        List<AgentProductQuote> quote = agentProductQuoteDao.listBy(param);
        if (CollectionUtils.isEmpty(quote)) {
            log.info("报价单不存在，无需删除");
            return;
        }
        for (AgentProductQuote agentProductQuote : quote) {
            if (agentProductQuote.getStatus() == CommonStatusEnum.ACTIVE.getValue()) {
                log.info("报价单已生效，无法删除");
                return;
            }
            agentProductQuoteDao.deleteById(agentProductQuote.getId());
        }


    }

    @Transactional(rollbackFor = Exception.class)
    public void agentQuoteFlowModify(String param) {

        CommonFlow commonFlow = JSONObject.parseObject(param, CommonFlow.class);

        Map<String, Object> sqlParam = MapUtil.builder(new HashMap<String, Object>()).put("flowId", commonFlow.getId()).build();
        List<AgentProductQuote> quote = agentProductQuoteDao.listBy(sqlParam);
        if (CollectionUtils.isEmpty(quote)) {
            log.info("报价单不存在，无需删除");
            return;
        }
        for (AgentProductQuote item : quote) {
            if (item.getStatus() == CommonStatusEnum.ACTIVE.getValue()) {
                log.info("报价单已生效，无法删除");
                continue;
            }
            agentProductQuoteDao.deleteById(item.getId());
        }

        List<AgentProductQuoteVo> agentProductQuoteVoList = JSONArray.parseArray(commonFlow.getExtInfo(),AgentProductQuoteVo.class);
        for (AgentProductQuoteVo quoteVo : agentProductQuoteVoList) {
            AgentProductQuote agentProductQuote = new AgentProductQuote();
            agentProductQuote.setAgentNo(quoteVo.getAgentNo());
            agentProductQuote.setAgentName(quoteVo.getAgentName());
            agentProductQuote.setProductNo(quoteVo.getProductNo());
            agentProductQuote.setFeeRate(quoteVo.getFeeRate());
            agentProductQuote.setFixedFee(quoteVo.getFixedFee());
            agentProductQuote.setFormulaType(quoteVo.getFormulaType());
            agentProductQuote.setCalculateMode(quoteVo.getCalculateMode());
            agentProductQuote.setMainstayName(quoteVo.getMainstayName());
            agentProductQuote.setUpdater(commonFlow.getInitiatorName());
            agentProductQuote.setMainstayNo(quoteVo.getMainstayNo());
            agentProductQuote.setStatus(CommonStatusEnum.INACTIVE.getValue());
            agentProductQuote.setFlowId(commonFlow.getId());
            agentProductQuote.setProductName(quoteVo.getProductName());
            agentProductQuote.setRuleType(quoteVo.getRuleType());
            agentProductQuote.setRuleParam(JSONObject.toJSONString(quoteVo.getRuleParam()));
            agentProductQuote.setMainstayNo(quoteVo.getMainstayNo());
            agentProductQuote.setMainstayName(quoteVo.getMainstayName());
            agentProductQuote.setPriority(quoteVo.getPriority());
            agentProductQuote.setSecondFeeRate(quoteVo.getSecondFeeRate());
            agentProductQuote.setSecondFixedFee(quoteVo.getSecondFixedFee());
            agentProductQuote.setSecondFormulaType(quoteVo.getSecondFormulaType());
            agentProductQuote.setMaxFee(quoteVo.getMaxFee());
            agentProductQuote.setMinFee(quoteVo.getMinFee());
            agentProductQuote.setDescription(quoteVo.getDescription());
            agentProductQuote.setRealProfitRatio(quoteVo.getRealProfitRatio());

            Date now = new Date();
            agentProductQuote.setUpdateTime(now);
            agentProductQuote.setCreateTime(now);
            agentProductQuote.setVersion(0);

            agentProductQuoteBiz.preSaveAgentQuote(agentProductQuote);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void agentBatchEditSalerInviter(CommonFlow commonFlow) {
        AgentBatchSetVo agentBatchSetVo = JSONUtil.toBean(commonFlow.getExtInfo(), AgentBatchSetVo.class);

        String setNo = agentBatchSetVo.getSetNo();
        String setName = agentBatchSetVo.getSetName();

        String businessKey = commonFlow.getBusinessKey();

        for (AgentBatchSetItem agentItem : agentBatchSetVo.getAgentNoList()) {

            if (StringUtils.equals("batchSetInviter", businessKey)) {

                Agent agent = agentDao.getByAgentNo(agentItem.getAgentNo());

                agent.setUpdateTime(new Date());
                agent.setUpdater(commonFlow.getInitiatorName());
                agent.setInviterNo(setNo);
                agent.setInviterName(setName);

                agentDao.update(agent);

                //更新邀请数
                updateInvitationNum(setNo);
                updateInvitationNum(agentItem.getBeforeNo());
            }
            if (StringUtils.equals("batchSetSeller", businessKey)) {

                AgentSaler agentSaler = agentSalerDao.getByAgentNo(agentItem.getAgentNo());

                PmsOperator operator = pmsOperatorBiz.getOperatorById(Long.valueOf(setNo));

                agentSaler.setSalerId(Long.valueOf(setNo));
                agentSaler.setSalerName(operator.getRealName());
                agentSaler.setSaleDepartmentName(operator.getDepartmentName());
                agentSaler.setSaleDepartmentId(operator.getDepartmentId());
                agentSaler.setUpdater(commonFlow.getInitiatorName());
                agentSaler.setUpdateTime(new Date());

                agentSalerDao.update(agentSaler);
                //涉及到递归问题，人工处理

//                //更换该合伙人下边的销售
//                List<Agent> subAgents = agentDao.listBy(MapUtil.builder(new HashMap<String, Object>()).put("inviterNo", agentItem.getAgentNo()).build());
//                if (subAgents != null) {
//                    List<AgentSaler> salers = new ArrayList<>();
//                    for (Agent subAgent : subAgents) {
//                        AgentSaler subAgentSaler = agentSalerDao.getByAgentNo(subAgent.getAgentNo());
//                        subAgentSaler.setSalerId(Long.valueOf(setNo));
//                        subAgentSaler.setSalerName(operator.getRealName());
//                        subAgentSaler.setSaleDepartmentName(operator.getDepartmentName());
//                        subAgentSaler.setSaleDepartmentId(operator.getDepartmentId());
//                        subAgentSaler.setUpdater(commonFlow.getInitiatorName());
//                        subAgentSaler.setUpdateTime(new Date());
//
//                        salers.add(subAgentSaler);
//                    }
//                    if (!salers.isEmpty()) {
//                        agentSalerDao.update(salers);
//                    }
//                }
                //更新商户销售
                changeMerchantSaler(agentSaler);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void createWxAgent(WxAgentRegisterVo wxAgentRegisterVo) {
        // 生成合伙人编号
        String agentNo = sequenceFacade.nextRedisId(MERCHANT_AGENT_SEQ.getPrefix(),
                MERCHANT_AGENT_SEQ.getKey(), MERCHANT_AGENT_SEQ.getWidth());
        Date date = new Date();
        Agent agent = new Agent();
        agent.setAgentNo(agentNo);
        agent.setAgentName(wxAgentRegisterVo.getName());
        agent.setAgentType(wxAgentRegisterVo.getAgentType());
        agent.setAuthStatus(AuthStatusEnum.UN_AUTH.getValue());
        agent.setAgentStatus(AgentStatusEnum.CREATE.getValue());
        agent.setCreateTime(date);
        agent.setUpdateTime(date);
        agent.setContactEmail(wxAgentRegisterVo.getEmail());
        agent.setContactName(wxAgentRegisterVo.getName());
        agent.setContactPhone(wxAgentRegisterVo.getPhone());
        agent.setFounder(wxAgentRegisterVo.getName());
        agent.setUpdater(wxAgentRegisterVo.getName());
        agent.setInvitationNum(0);
        agent.setMerNum(0);
        agentDao.insert(agent);
        //创建员工
        AgentStaffVO agentStaffVO = new AgentStaffVO();
        agentStaffVO.setAgentNo(agentNo)
                .setAgentName(agent.getAgentName())
                .setCreateTime(date)
                .setName(agent.getContactName())
                .setOperatorName(agent.getContactName())
                .setPhone(agent.getContactPhone())
                .setType(AgentStaffTypeEnum.ADMIN.getValue())
                .setUpdateTime(date)
                .setUpdator(agent.getAgentName())
                .setCreator(agent.getAgentName());
        agentStaffBiz.create(agentStaffVO);
        String defaultSaler = dataDictionaryFacade.getSystemConfig(DEFAULT_SALER);
        PmsOperator saler = pmsOperatorBiz.getOperatorById(Long.parseLong(defaultSaler));
        //设置销售
        AgentSaler agentSaler = new AgentSaler();
        agentSaler.setCreateTime(date);
        agentSaler.setUpdater(agent.getAgentName());
        agentSaler.setAgentNo(agent.getAgentNo());
        agentSaler.setAgentType(agent.getAgentType());
        agentSaler.setSalerId(saler.getId());
        agentSaler.setSalerName(saler.getRealName());
        agentSaler.setSaleDepartmentId(saler.getDepartmentId());
        agentSaler.setSaleDepartmentName(saler.getDepartmentName());
        agentSalerDao.insert(agentSaler);
    }
}

