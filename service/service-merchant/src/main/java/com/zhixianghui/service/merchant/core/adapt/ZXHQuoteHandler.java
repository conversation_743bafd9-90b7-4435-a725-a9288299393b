package com.zhixianghui.service.merchant.core.adapt;

import com.zhixianghui.common.statics.enums.merchant.DealStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuotePosition;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuoteRate;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.service.merchant.core.dao.MerchantDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuoteDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuotePositionDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuoteRateDao;
import com.zhixianghui.service.merchant.core.util.BuildVoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MerchantQuoteHandler
 * @Description TODO
 * @Date 2022/6/24 18:14
 */
@Slf4j
@Component
public class ZXHQuoteHandler implements QuoteHandlerInterface{

    @Autowired
    private MerchantEmployerQuoteDao quoteDao;

    @Reference
    private VendorFacade vendorFacade;

    @Autowired
    private MerchantEmployerQuoteRateDao merchantEmployerQuoteRateDao;

    @Autowired
    private MerchantEmployerQuotePositionDao merchantEmployerQuotePositionDao;

    @Autowired
    private MerchantDao merchantDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAfterFlow(MerchantFlowVo merchantFlowVo) {
        //删除旧的报价单（包括未生效的）
        Map<String,Object> maps = new HashMap<>();
        maps.put("productNo",merchantFlowVo.getProductNo());
        maps.put("mchNo",merchantFlowVo.getMchNo());
        maps.put("mainstayMchNo",merchantFlowVo.getMainstayMchNo());
        quoteDao.listBy(maps).forEach(x-> {
            merchantEmployerQuoteRateDao.deleteBy("deleteByQuoteId",x.getId());
            merchantEmployerQuotePositionDao.deleteBy("deleteByQuoteId",x.getId());
        });
        quoteDao.deleteBy(maps);
        merchantFlowVo.setFlowId(merchantFlowVo.getCommonFlowId());
        //更新商户状态
        Merchant merchant = merchantDao.getByMchNo(merchantFlowVo.getMchNo());
        if (merchant.getDealStatus().intValue() == DealStatusEnum.UNDEAL.getValue()){
            merchant.setDealStatus(DealStatusEnum.DEAL.getValue());
            merchant.setUpdateTime(new Date());
            merchant.setUpdator(merchantFlowVo.getUpdator());
            merchantDao.update(merchant);
        }
        //增加新报价单
        createQuote(merchantFlowVo,MerchantQuoteStatusEnum.ACTIVE.getValue());
    }

    @Override
    public void delete(Long quoteId) {
        quoteDao.deleteById(quoteId);
        Map<String,Object> map = new HashMap<String,Object>(){{put("quoteId",quoteId);}};
        merchantEmployerQuoteRateDao.deleteBy("deleteByQuoteId",map);
        merchantEmployerQuotePositionDao.deleteBy("deleteByQuoteId",map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MerchantFlowVo merchantFlowVo) {
        this.createQuote(merchantFlowVo, MerchantQuoteStatusEnum.INACTIVE.getValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(MerchantEmployerQuote merchantEmployerQuote) {
        if (merchantEmployerQuote.getStatus().intValue() == MerchantQuoteStatusEnum.ACTIVE.getValue()){
            log.info("报价单已生效，无法删除");
            return;
        }
        this.delete(merchantEmployerQuote.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editQuote(MerchantFlowVo merchantFlowVo) {
        Map<String,Object> map = new HashMap<String,Object>(){{put("flowBusinessKey",merchantFlowVo.getCommonFlowId());}};
        MerchantEmployerQuote merchantEmployerQuote = quoteDao.getOne(map);

        if (merchantEmployerQuote == null){
            log.info("报价单不存在");
            return;
        }

        if (merchantEmployerQuote.getStatus().intValue() == MerchantQuoteStatusEnum.ACTIVE.getValue()){
            log.info("报价单已生效，无法修改");
            return;
        }
        Map<String,Object> paramMap = new HashMap<String,Object>(){{put("quoteId",merchantEmployerQuote.getId());}};
        merchantEmployerQuoteRateDao.deleteBy("deleteByQuoteId",paramMap);
        merchantEmployerQuotePositionDao.deleteBy("deleteByQuoteId",paramMap);
        //只需要修改子表
        List<MerchantEmployerQuoteRate> merchantEmployerQuoteRateList = BuildVoUtil.fillQuoteRateList(merchantFlowVo,merchantEmployerQuote);
        merchantEmployerQuoteRateDao.insert(merchantEmployerQuoteRateList);
        List<MerchantEmployerQuotePosition> merchantEmployerQuotePositionList = BuildVoUtil.fillQuotePositionList(merchantFlowVo,merchantEmployerQuote);
        merchantEmployerQuotePositionDao.insert(merchantEmployerQuotePositionList);
    }

    private void createQuote(MerchantFlowVo merchantFlowVo,Integer status) {
        Map<String,Object> map = new HashMap<>();
        map.put("flowBusinessKey",merchantFlowVo.getFlowId());
        List<MerchantEmployerQuote> merchantEmployerQuoteList = quoteDao.listBy(map);
        if (merchantEmployerQuoteList != null && merchantEmployerQuoteList.size() > 0){
            log.info("报价单已存在...");
            return;
        }
        //从供应商-代征主体映射表取出对应的供应商编号、名称
        Vendor vendor = vendorFacade.getVendorByNoAndProduct(merchantFlowVo.getMainstayMchNo(),merchantFlowVo.getProductNo());
        MerchantEmployerQuote merchantEmployerQuote = BuildVoUtil.fillEmployerQuote(merchantFlowVo,vendor, status);
        quoteDao.insert(merchantEmployerQuote);
        List<MerchantEmployerQuoteRate> merchantEmployerQuoteRateList = BuildVoUtil.fillQuoteRateList(merchantFlowVo,merchantEmployerQuote);
        merchantEmployerQuoteRateDao.insert(merchantEmployerQuoteRateList);
        List<MerchantEmployerQuotePosition> merchantEmployerQuotePositionList = BuildVoUtil.fillQuotePositionList(merchantFlowVo,merchantEmployerQuote);
        if (merchantEmployerQuotePositionList.size() > 0){
            merchantEmployerQuotePositionDao.insert(merchantEmployerQuotePositionList);
        }
    }
}
