package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.service.MerchantCkhQuoteFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantCkhQuoteBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-06-24
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantCkhQuoteImpl implements MerchantCkhQuoteFacade {

    private final MerchantCkhQuoteBiz biz;

    @Override
    public MerchantCkhQuote getFeeRate(String employerNo, String mainstayNo, String productNo) {
        return biz.getFeeRate(employerNo,mainstayNo,productNo);
    }

    @Override
    public Integer getBalancedMode(MerchantEmployerQuote merchantEmployerQuote) {
        return biz.getBalancedMode(merchantEmployerQuote);
    }
}
