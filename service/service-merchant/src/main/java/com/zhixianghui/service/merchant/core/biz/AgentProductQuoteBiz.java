package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import com.zhixianghui.service.merchant.core.dao.AgentProductQuoteDao;
import com.zhixianghui.starter.comp.properties.MailProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 合伙人银行账户表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-02-01
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentProductQuoteBiz {

    private final AgentProductQuoteDao agentProductQuoteDao;

    public List<AgentProductQuote> getByAgentNo(String agentNo) {
        return agentProductQuoteDao.listByAgentNo(agentNo);
    }

    public List<AgentProductQuote> getByParams(Map<String, Object> params) {
        return agentProductQuoteDao.listBy(params);
    }

    public void preSaveAgentQuote(AgentProductQuote quote) {
        agentProductQuoteDao.insert(quote);
    }

    public void updateByAgentNo(Map<String,Object> param) {
        agentProductQuoteDao.update("updateByAgentNo",param);
    }
}
