package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.vo.merchant.ListMerchantProductVo;
import com.zhixianghui.facade.merchant.vo.merchant.MainstaySimpleVo;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerQuoteBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
public class MerchantEmployerQuoteFacadeImpl implements MerchantEmployerQuoteFacade {
    @Autowired
    private MerchantEmployerQuoteBiz quoteBiz;

    @Override
    public List<MerchantEmployerQuote> listByMchNo(String mchNo) {
        return quoteBiz.listByMchNo(mchNo);
    }

    @Override
    public List<MerchantEmployerQuote> listBy(Map<String, Object> params) {
        return quoteBiz.listBy(params);
    }

    @Override
    public List<MerchantEmployerQuote> getQuoteList(String mchNo,Map<String, Object> paramMap) {
        return quoteBiz.getQuoteList(mchNo,paramMap);
    }

    @Override
    public MerchantEmployerQuote getById(Long id) {
        return quoteBiz.getById(id);
    }

    @Override
    public void deleteById(Long id) {
        quoteBiz.deleteById(id);
    }

    @Override
    public List<Map<String, Object>> getQuoteByMchNo(String mchNo) {
        return quoteBiz.getQuoteByMchNo(mchNo);
    }

    @Override
    public List<ListMerchantProductVo> listMchProduct(Map<String, Object> param) {
        return quoteBiz.listMchProduct(param);
    }

    @Override
    public List<MainstaySimpleVo> listOpenMainstayByEmployerNoAndProduct(Integer status, String productNo, String mchNo) {
        return quoteBiz.listMainstayByEmployerNoAndProduct(status, productNo, mchNo);
    }

    @Override
    public List<MerchantEmployerQuote> listGroupByProductNo(Map<String, Object> paramMap) {
        return quoteBiz.listGroupByProductNo(paramMap);
    }

    @Override
    public List<MerchantEmployerQuote> getQuoteListWithSupplier(String mchNo, Map<String, Object> paramMap) {
        return quoteBiz.getQuoteListWithSupplier(mchNo,paramMap);
    }

    @Override
    public boolean isExistQuote(String mchNo, String mainstayNo, String productNo) {
        return quoteBiz.isExistQuote(mchNo,mainstayNo,productNo);
    }
}
