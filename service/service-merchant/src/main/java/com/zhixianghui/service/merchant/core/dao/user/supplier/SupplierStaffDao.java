package com.zhixianghui.service.merchant.core.dao.user.supplier;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaff;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 供应商后台员工表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class SupplierStaffDao extends MyBatisDao<SupplierStaff,Long> {
    public List<SupplierStaff> listByOperatorId(long operatorId) {
        return super.listBy(Collections.singletonMap("operatorId", operatorId));
    }

    public void updateTypeById(Long id, int type, String updator) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("type", type);
        param.put("updator", updator);
        param.put("update_time", new Date());
        super.update("updateTypeById", param);
    }

    public void deleteByOperatorId(long operatorId) {
        super.deleteBy("deleteByOperatorId", operatorId);
    }

    public void deleteByMchNoAndId(String mchNo, long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("id", id);
        super.deleteBy("deleteByMchNoAndId", param);
    }

    public SupplierStaffVO getVOByMchNoAndId(String mchNo, long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("id", id);
        return getOne("getVOByMchNoAndId", param);
    }

    public SupplierStaffVO getVOByMchAndPhone(String mchNo, String phone) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("phone", phone);
        return getOne("getVOByMchAndPhone", param);
    }

    public List<SupplierStaffVO> getVOByMchNoAndType(String mchNo, int type) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("type", type);
        return listBy("getVOByMchNoAndType", param);
    }

    public List<SupplierStaffVO> listVOByOperatorId(Long id) {
        return listBy("listVOByOperatorId", Collections.singletonMap("operatorId", id));
    }

    public List<SupplierStaffVO> listVOByPhone(String phone) {
        return listBy("listVOByPhone", Collections.singletonMap("phone", phone));
    }

    public List<SupplierStaffVO> listStaffVOByRoleId(long roleId) {
        return listBy("listStaffVOByRoleId", Collections.singletonMap("roleId", roleId));
    }

    public PageResult<List<SupplierStaffVO>> listStaffVOPage(Map<String, Object> paramMap, PageParam pageParam) {
        return listPage("listStaffRoleVOPage", "listStaffRoleVOPageCount", paramMap, pageParam);
    }

    public List<String> getDistinctStaffByRoleIdAndMainstayNo(Map<String, Object> paramMap) {
        return listBy("getDistinctStaffByRoleIdAndMainstayNo",paramMap);
    }
}
