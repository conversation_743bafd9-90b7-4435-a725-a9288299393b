package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.AgreementTemplate;
import com.zhixianghui.service.merchant.core.dao.AgreementTemplateDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
* 协议模板表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-09-02
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementTemplateBiz {

    private final AgreementTemplateDao agreementTemplateDao;

    public void createAgreementTemplate(AgreementTemplate agreementTemplate) {
        agreementTemplateDao.insert(agreementTemplate);
    }

    public void updateAgreementTemplate(AgreementTemplate agreementTemplate) {
        if(agreementTemplate.getId() == null){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("更新未指定id");
        }
        agreementTemplateDao.updateIfNotNull(agreementTemplate);
    }

    public PageResult<List<AgreementTemplate>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return agreementTemplateDao.listPage(paramMap, pageParam);
    }

    public AgreementTemplate getById(Long id) {
        return agreementTemplateDao.getById(id);
    }

    public List<AgreementTemplate> listAll() {
        return agreementTemplateDao.listAll();
    }

    public void deleteAll() {
        agreementTemplateDao.deleteBy("deleteAll",null);
    }
}