package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.IDCardUtils;
import com.zhixianghui.facade.merchant.dto.EnterprisePersonnelDto;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerMainDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEnterprisePersonnelDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.merchant.entity.MerchantEnterprisePersonnel;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2022-06-07
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantEnterprisePersonnelBiz {

    private final MerchantEnterprisePersonnelDao merchantenterprisepersonnelDao;

    private final MerchantBiz merchantBiz;

    private final MerchantEmployerMainDao mainDao;

    public List<MerchantEnterprisePersonnel> getList(String mchNo) {
        return merchantenterprisepersonnelDao.getListByMchNo(mchNo);
    }


    public List<EnterprisePersonnelDto> batchInsert(List<EnterprisePersonnelDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("企业人员列表不能为空");
        }
        list.forEach(e -> e.setCreateTime(new Date()));
        merchantenterprisepersonnelDao.insertDto(list);
        log.info("企业人员插入完成，更新完后的数据:{}", list);
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePersonnel(List<EnterprisePersonnelDto> personnels, String mchNo) {
        List<MerchantEnterprisePersonnel> merchantEnterprisePersonnels = this.getList(mchNo);
        log.info("企业人员查询:{}",merchantEnterprisePersonnels);
        personnels.forEach(e -> {
            if (e.getId() == null) {
                e.setCreateTime(new Date());
                merchantenterprisepersonnelDao.insertDto(e);
            } else {
                merchantenterprisepersonnelDao.updateDto(e);
            }
        });
        log.info("更新企业人员信息:{}", personnels);
        List<Long> deleteItem = merchantEnterprisePersonnels.stream().filter(e -> {
            for (EnterprisePersonnelDto item : personnels) {
                if (item.getId().equals(e.getId())) {
                    return false;
                }
            }
            return true;
        }).map(MerchantEnterprisePersonnel::getId).collect(Collectors.toList());
        log.info("更新企业人员信息,删除信息:{}", deleteItem);
        if (!CollectionUtils.isEmpty(deleteItem)) {
            merchantenterprisepersonnelDao.deleteByIdList(deleteItem);
        }
    }


    public void check(List<EnterprisePersonnelDto> personnels) {
        if (CollectionUtils.isEmpty(personnels)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("企业人员不能为空");
        }
        personnels.forEach(e -> {
            if (e.getIsLegal()) {
                if (StringUtils.isBlank(e.getName()) || StringUtils.isBlank(e.getIdCardNumber())) {
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("法人姓名和身份证不能为空");
                }
            } else {
                if (StringUtils.isBlank(e.getName())) {
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("企业人员姓名不能为空");
                }
            }
            if(!e.getIsLegal()&&StringUtils.isNoneBlank(e.getIdCardNumber())&& !IDCardUtils.verifi(e.getIdCardNumber())){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("身份证号不合法");
            }
        });

        List<String> keys = personnels.stream().map(item -> {
            String name = item.getName();
            String idCardNumber = StringUtils.isBlank(item.getIdCardNumber()) ? "-1" : item.getIdCardNumber();
            return name + "::" + idCardNumber;
        }).collect(Collectors.toList());
        if (keys.size() != new HashSet<String>(keys).size()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("名称和身份证不能重复");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncAllMerchant() {
        log.info("开始同步");
        List<Merchant> merchants = merchantBiz.selectNoPersonnel();
        List<MerchantEnterprisePersonnel> personnels = merchants.stream().map(item -> {
            MerchantEmployerMain main = mainDao.getByMchNo(item.getMchNo());
            MerchantEnterprisePersonnel personnel = new MerchantEnterprisePersonnel();
            personnel.setMchNo(item.getMchNo());
            personnel.setName(main.getLegalPersonName());
            personnel.setIdCardNumber(main.getCertificateNumber());
            personnel.setIsLegal(Boolean.TRUE);
            personnel.setCreateTime(new Date());
            return personnel;
        }).collect(Collectors.toList());
        log.info("初始化结果，{}",personnels);
        personnels.forEach(e->{
            HashMap<String,Object> params=new HashMap<>();
            params.put("mchNo",e.getMchNo());
            params.put("name",e.getName());
            params.put("idCardNumber",e.getIdCardNumber());
            params.put("isLegal",e.getIsLegal());
            MerchantEnterprisePersonnel personnel = merchantenterprisepersonnelDao.getOne(params);
            if(ObjectUtils.isEmpty(personnel)){
                merchantenterprisepersonnelDao.insert(e);
            }
        });
    }
}