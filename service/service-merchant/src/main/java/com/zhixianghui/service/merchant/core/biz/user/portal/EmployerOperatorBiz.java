package com.zhixianghui.service.merchant.core.biz.user.portal;

import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerOperator;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerStaff;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.service.merchant.core.dao.user.employer.EmployerOperatorDao;
import com.zhixianghui.service.merchant.core.dao.user.employer.EmployerStaffDao;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class EmployerOperatorBiz{
	
	@Autowired
	private EmployerOperatorDao employerOperatorDao;

	@Autowired
	private EmployerStaffDao employerStaffDao;

	@Autowired
	private RedisClient redisClient;

	/**
	 * 根据id获取操作员
	 *
	 * @param id
	 */
	public EmployerOperator getById(long id) {
		return employerOperatorDao.getById(id);
	}

	/**
	 * 根据手机号码获取操作员
	 *
	 * @param phone
	 */
	public EmployerOperator getByPhone(String phone) {
		return employerOperatorDao.getByPhone(phone);
	}

	/**
	 * 根据id删除操作员，并删除其在所有用工企业下关联的员工
	 *
	 * @param id
	 */
	@Transactional
	public void deleteById(long id) throws BizException {
		// 查询操作员关联的员工
		List<EmployerStaff> staffs = employerStaffDao.listByOperatorId(id);
		if (staffs.stream().anyMatch(staff -> staff.getType() == PortalStaffTypeEnum.ADMIN.getValue())) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("操作员属于某商户的超级管理员，无法删除");
		}

		employerOperatorDao.deleteById(id);
		employerStaffDao.deleteByOperatorId(id);  // 删除操作员关联的员工

		redisClient.del(PlatformSource.MERCHANT.getValue() + ":" + id);

		staffs.stream().forEach(employerStaff -> redisClient.del(PlatformSource.MERCHANT.getValue() + ":" + employerStaff.getId() + ":" + employerStaff.getMchNo()));
	}

	/**
	 * 更新操作员
	 *
	 * @param operator
	 */
	public void update(EmployerOperator operator) throws BizException {
		employerOperatorDao.update(operator);
		redisClient.hset(PlatformSource.MERCHANT.getValue() + ":" + operator.getId(),operator.getCacheMap());
	}

	/**
	 * 分页查询操作员
	 *
	 * @param paramMap
	 * @param pageParam
	 */
	public PageResult<List<EmployerOperatorVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
		return employerOperatorDao.listOperatorVoPage(paramMap, pageParam);
	}

    public List<EmployerOperator> getAll() {
		return employerOperatorDao.listAll();
    }
}
