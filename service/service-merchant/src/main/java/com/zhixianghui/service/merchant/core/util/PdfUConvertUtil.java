package com.zhixianghui.service.merchant.core.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @ClassName PdfUtil
 * @Description TODO
 * @Date 2022/8/24 14:56
 */
public class PdfUConvertUtil {

    public static byte[] convertToPNG(InputStream inputStream) throws IOException {
        PDDocument document = null;
        ByteArrayOutputStream os = null;
        try {
            os = new ByteArrayOutputStream();
            document = PDDocument.load(inputStream);
            PDFRenderer renderer = new PDFRenderer(document);
            //固定只取第一页
            BufferedImage bufferedImage = renderer.renderImage(0);
            ImageIO.write(bufferedImage,"png",os);
            return os.toByteArray();
        }finally {
            if (document != null){
                document.close();
            }
            if (os != null){
                os.close();
            }
        }


    }
}
