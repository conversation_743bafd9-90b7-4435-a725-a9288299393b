package com.zhixianghui.service.merchant.helper;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.ValidityDateTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.IDCardUtils;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerMainAuthVo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantEnterprisePersonnelBiz;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/7
 **/
@Component
public class MerchantHelper {
    @Autowired
    private MerchantBiz merchantBiz;
    @Autowired
    private MerchantEnterprisePersonnelBiz merchantEnterprisePersonnelBiz;

    /**
     * 校验用工企业主体认证信息
     *
     * @param authVo 主体认证信息
     */
    public void validMainAuthVo(MerchantEmployerMainAuthVo authVo) {
        if (StringUtil.isEmpty(authVo.getIdCardCopyFileUrl())) {
            if (StringUtil.isEmpty(authVo.getIdCardHeadFileUrl())
                    && StringUtil.isEmpty(authVo.getIdCardEmblemFileUrl())) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("身份证明文件必须上传");
            }
            if (StringUtil.isEmpty(authVo.getIdCardHeadFileUrl())
                    ^ StringUtil.isEmpty(authVo.getIdCardEmblemFileUrl())) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("身份证明文件必须上传完整正反面");
            }
        }

        // 台湾、香港、澳门不用填市镇等，其他必填
        if (!authVo.getManagementAddrProvince().contains("台湾")
                && !authVo.getManagementAddrProvince().contains("香港")
                && !authVo.getManagementAddrProvince().contains("澳门")) {
            LimitUtil.notEmpty(authVo.getManagementAddrCity(), "经营地址-市 不能为空");
            LimitUtil.notEmpty(authVo.getManagementAddrTown(), "经营地址-区镇 不能为空");
        }
        if (!authVo.getRegisterAddrProvince().contains("台湾")
                && !authVo.getRegisterAddrProvince().contains("香港")
                && !authVo.getRegisterAddrProvince().contains("澳门")) {
            LimitUtil.notEmpty(authVo.getRegisterAddrCity(), "注册地址-市 不能为空");
            LimitUtil.notEmpty(authVo.getRegisterAddrTown(), "注册地址-区镇 不能为空");
        }

        Merchant mch = merchantBiz.getByMchNo(authVo.getMchNo());
        if (mch == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("商户号有误");
        }

        if (Objects.equals(ValidityDateTypeEnum.PERIOD.getValue(), authVo.getManagementValidityDateType())
                && (StringUtil.isEmpty(authVo.getManagementTermBegin()) || StringUtil.isEmpty(authVo.getManagementTermEnd()))) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("营业执照为区间有效时必须选择起止时间");
        }
        if (Objects.equals(ValidityDateTypeEnum.FOREVER.getValue(), authVo.getManagementValidityDateType())
                && StringUtil.isEmpty(authVo.getManagementTermBegin())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("营业执照为长期有效时必须选择起始时间");
        }
        if (Objects.equals(ValidityDateTypeEnum.PERIOD.getValue(), authVo.getCertificateValidityDateType())
                && (StringUtil.isEmpty(authVo.getCertificateTermBegin()) || StringUtil.isEmpty(authVo.getCertificateTermEnd()))) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("身份证件为区间有效时必须选择起止时间");
        }
        if (Objects.equals(ValidityDateTypeEnum.FOREVER.getValue(), authVo.getCertificateValidityDateType())
                && StringUtil.isEmpty(authVo.getCertificateTermBegin())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("身份证件为区间有效时必须选择起始时间");
        }

        if (authVo.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) {
            merchantEnterprisePersonnelBiz.check(authVo.getPersonnels());
        }

    }
}
