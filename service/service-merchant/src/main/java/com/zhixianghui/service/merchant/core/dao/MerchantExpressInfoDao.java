package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;


/**
 * 商户发票邮寄信息
 * <AUTHOR>
 * @date 2020/12/25
 **/
@Repository
public class MerchantExpressInfoDao extends MyBatisDao<MerchantExpressInfo, Long> {

   public MerchantExpressInfo getByMchNo(String mchNo){
       LimitUtil.notEmpty(mchNo, "商户编码不能为空");
       Map<String, Object> param = new HashMap<>();
       param.put("mchNo", mchNo);
       return super.getOne(param);
   }
}
