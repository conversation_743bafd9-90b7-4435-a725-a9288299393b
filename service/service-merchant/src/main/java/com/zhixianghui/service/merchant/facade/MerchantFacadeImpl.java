package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.vo.RelevantAgent;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantLogoVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商户服务
 * <AUTHOR>
 * @date 2020/9/14
 **/
@Service
public class MerchantFacadeImpl implements MerchantFacade {

    @Autowired
    private MerchantBiz merchantBiz;

    @Override
    public void update(Merchant merchant) {
        merchantBiz.update(merchant);
    }

    @Override
    public Merchant getByMchNo(String mchNo){
        return merchantBiz.getByMchNo(mchNo);
    }

    @Override
    public RelevantAgent getRelevantAgentByMchNo(String mchNo) {
        return merchantBiz.getRelevantAgentByMchNo(mchNo);
    }

    @Override
    public List<String> listMerchantNo(Boolean existAgent) {
        return merchantBiz.listMerchantNo(existAgent);
    }

    @Override
    public void updateTemplateId(String templateId, List<String> mchNoList) {
        merchantBiz.updateTemplateId(templateId, mchNoList);
    }

    @Override
    public List<Merchant> getByTemplateId(String templateId, Integer signTemplateType) {
        return merchantBiz.listMerchantByTemplateId(templateId, signTemplateType);
    }

    @Override
    public List<String> listTemplateIdByMchNo(List<String> mchNo) {
        List<Merchant> result = merchantBiz.listTemplateIdByMchNo(mchNo);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.stream().map(Merchant :: getTemplateId).collect(Collectors.toList());
    }

    @Override
    public void delTemplateId(String templateId) {
        merchantBiz.delTemplateId(templateId);
    }

    @Override
    public void updateBaseInfo(MerchantUpdateVo merchantUpdateVo) {
        merchantBiz.updateBaseInfo(merchantUpdateVo);
    }

    @Override
    public Map<String, Object> getAgentData(Map<String, Object> nowDateMap, Map<String, Object> lastDateMap) {
        return merchantBiz.getAgentData(nowDateMap,lastDateMap);
    }

    @Override
    public void forceDelete(String mchNo) {
        merchantBiz.forceDelete(mchNo);
    }

    @Override
    public List<Merchant> getAllSupplier() {
        return merchantBiz.getAllSupplier();
    }

    @Override
    public void forceActive(String mchNo,String updator) {
        merchantBiz.forceActive(mchNo,updator);
    }

    @Override
    public void changeLogo(MerchantLogoVo merchantLogoVo,String mchNo, String name) {
        merchantBiz.changeLogo(merchantLogoVo,mchNo,name);
    }
}
