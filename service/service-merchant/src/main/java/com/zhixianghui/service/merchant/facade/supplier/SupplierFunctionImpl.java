package com.zhixianghui.service.merchant.facade.supplier;


import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.service.supplier.SupplierFunctionFacade;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierFunctionBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台功能表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierFunctionImpl implements SupplierFunctionFacade {

    private final SupplierFunctionBiz biz;

    /**
     * 创建用工企业后台功能
     *
     * @param function 功能
     */
    @Override
    public void create(SupplierFunction function) throws BizException {
        biz.create(function);
    }

    /**
     * 根据id查询用工企业后台功能
     *
     * @param id
     */
    @Override
    public SupplierFunction getById(long id){
        return biz.getById(id);
    }

    /**
     * 编辑用工企业后台功能
     *
     * @param function 功能
     */
    @Override
    public void update(SupplierFunction function) throws BizException {
        biz.update(function);
    }

    /**
     * 根据id删除功能，并删除该功能与角色的映射
     *
     * @param id
     */
    @Override
    public void deleteById(long id) throws BizException {
        biz.deleteById(id);
    }

    /**
     * 查询所有功能
     */
    @Override
    public List<SupplierFunction> listAll() {
        return biz.listAll();
    }

    @Override
    public List<SupplierFunction> listAll(Map<String, Object> param, PageParam pageParam) {
        return biz.listAll(param, pageParam);

    }

    @Override
    public void export(FunctionVO employerFunction) {
        biz.export(employerFunction);
    }

    @Override
    public void saveFunction(List<SupplierFunction> list) {
        biz.saveFunction(list);
    }

    @Override
    public String getPermissionFlag(Long parentId) {
        return biz.getPermissionFlag(parentId);
    }
}
