package com.zhixianghui.service.merchant.core.dao.user.agent;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台操作员表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class AgentOperatorDao extends MyBatisDao<AgentOperator, Long> {
    public AgentOperator getByPhone(String phone) {
        return super.getOne("getByPhone", phone);
    }

    public PageResult<List<AgentOperatorVO>> listOperatorVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        return super.listPage("listOperatorVoPage", "listOperatorVoPageCount", paramMap, pageParam);
    }
}
