package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.facade.merchant.service.AgreementFacade;
import com.zhixianghui.facade.merchant.vo.*;
import com.zhixianghui.service.merchant.core.biz.AgreementBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 协议表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementImpl implements AgreementFacade {

    private final AgreementBiz biz;

    @Override
    public void createAgreement(AgreementDto agreementDto) {
        biz.createAgreement(agreementDto);
    }

    @Override
    public void editAgreement(Long agreementId, Date deadline, Date expireTime, String operatorName) {
        biz.editAgreement(agreementId, deadline, expireTime, operatorName);
    }

    @Override
    public void updateStatus(Long agreementId, Integer status, String description, String operatorName) {
        biz.updateStatus(agreementId, status, description, operatorName);
    }

    @Override
    public PageResult<List<Agreement>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public void archive(List<AgreementFile> files, String operatorName) {
        biz.archive(files,operatorName);
    }

    @Override
    public Agreement getAgreementById(Long id) {
        return biz.getAgreementById(id);
    }

    @Override
    public PageResult<List<AgreementResVo>> listCustomPage(Map<String,Object> map, PageParam toPageParam) {
        return biz.listCustomPage(map,toPageParam);
    }

    @Override
    public List<Agreement> getAgreementPageByMchNoAndMainstayNo(Map<String,Object> map) {
        return biz.getAgreementPageByMchNoAndMainstayNo(map);
    }

    @Override
    public List<Agreement> listBy(Map<String, Object> paramMap) {
        return biz.listBy(paramMap);
    }

    @Override
    public void cancelAgreement(List<Long> idsList) {
        biz.cancelAgreement(idsList);
    }

    @Override
    public void deleteAgreement(List<Long> idList) {
        biz.deleteAgreement(idList);
    }

    @Override
    public void turnToOffline(List<Long> idList) {
        biz.turnToOffline(idList);
    }

    @Override
    public void delay(Long id, Date deadLine) {
        biz.delay(id,deadLine);
    }

    @Override
    public PageResult<List<AgreementExportVo>> getExportList(Map<String, Object> param, PageParam pageParam) {
        return biz.getExportList(param,pageParam);
    }

    @Override
    public PageResult<List<Agreement>> listPageByMerchant(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPageByMerchant(paramMap, pageParam);
    }

}
