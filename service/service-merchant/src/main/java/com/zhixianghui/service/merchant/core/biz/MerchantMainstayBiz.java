package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.service.merchant.core.biz.user.pms.PmsOperatorBiz;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierStaffBiz;
import com.zhixianghui.service.merchant.core.dao.MerchantDao;
import com.zhixianghui.service.merchant.core.dao.MerchantFileDao;
import com.zhixianghui.service.merchant.core.dao.MerchantSalerDao;
import com.zhixianghui.service.merchant.core.util.BuildVoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代征主体
 *
 * <AUTHOR> Guangsheng
 */
@Slf4j
@Component
public class MerchantMainstayBiz {

    @Reference
    private SequenceFacade sequenceFacade;

    @Autowired
    private MerchantDao merchantDao;

    @Autowired
    private MerchantSalerDao merchantSalerDao;

    @Autowired
    private MerchantFileDao merchantFileDao;

    @Autowired
    private SupplierStaffBiz supplierStaffBiz;

    @Autowired
    private PmsOperatorBiz pmsOperatorBiz;

    @Reference
    private EmailFacade emailFacade;

    @Value("${mainstay.platform.link}")
    private String mainstayLink;

    /**
     * 创建代征主体
     * @param merchant  商户信息
     * @param saler     销售信息
     * @param fileList  文件列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void createMainstay(Merchant merchant, MerchantSaler saler, List<MerchantFile> fileList) {

        // 销售员
        PmsOperator operator = pmsOperatorBiz.getOperatorById(saler.getSalerId());

        // 保存商户信息表
        merchantDao.insert(merchant);

        // 保存销售信息
        merchantSalerDao.insert(saler);

        // 商户文件保存
        merchantFileDao.insert(fileList);

        //创建供应商后台管理员
        SupplierStaffVO supplierStaffVo = BuildVoUtil.fillSupplierStaffVO(merchant, operator.getLoginName());
        supplierStaffBiz.create(supplierStaffVo);

        //发送邮件通知商户
        EmailParamDto emailParamDto = new EmailParamDto();
        emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        emailParamDto.setTo(merchant.getContactEmail());
        emailParamDto.setSubject("【智享汇】智享汇综合服务平台帐号开通");
        emailParamDto.setTpl(EmailTemplateEnum.SUCCESS_CREATE_MERCHANT.getName());
        Map<String,Object> map = new HashMap<>();
        //判断商户类型
        map.put("mchType",MerchantTypeEnum.MAINSTAY.getDesc());
        map.put("platformUrl",mainstayLink);
        map.put("merchantName",merchant.getMchName());
        map.put("username",merchant.getContactName());
        map.put("phone",merchant.getContactPhone());
        emailParamDto.setTplParam(map);
        emailParamDto.setHtmlFormat(true);


        //异步发送邮件
        emailFacade.sendAsync(emailParamDto);
    }
}
