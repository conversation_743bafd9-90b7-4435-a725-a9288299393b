package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;


/**
 * 商户开票信息
 * <AUTHOR>
 * @date 2020/12/25
 **/
@Repository
public class MerchantInvoiceInfoDao extends MyBatisDao<MerchantInvoiceInfo, Long> {

   public MerchantInvoiceInfo getByMchNo(String mchNo){
       LimitUtil.notEmpty(mchNo, "商户编码不能为空");
       Map<String, Object> param = new HashMap<>();
       param.put("mchNo", mchNo);
       return super.getOne(param);
   }
}
