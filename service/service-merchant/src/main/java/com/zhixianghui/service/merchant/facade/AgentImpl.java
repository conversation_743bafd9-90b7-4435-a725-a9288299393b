package com.zhixianghui.service.merchant.facade;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.vo.WxAgentRegisterVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentDetailVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentVo;
import com.zhixianghui.facade.merchant.vo.agent.SimpleAgentInfoVo;
import com.zhixianghui.service.merchant.core.biz.AgentBiz;
import com.zhixianghui.service.merchant.core.biz.AgentManagerBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 合伙人基本信息表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentImpl implements AgentFacade {

    private final AgentBiz biz;
    private final AgentManagerBiz agentManagerBiz;

    @Override
    public List<Agent> listBy(Map<String, Object> paramMap) {
        return biz.listBy(paramMap);
    }

    @Override
    public Agent getByAgentNo(String agentNo) {
        return biz.getByAgentNo(agentNo);
    }

    @Override
    public AgentDetailVo getDetailByAgentNo(String agentNo) {
        return biz.getDetailByAgentNo(agentNo);
    }

    @Override
    public AgentProductQuote getAgentProductQuote(Long id) {
        return biz.getAgentProductQuote(id);
    }

    @Override
    public List<SimpleAgentInfoVo> listAllSimpleAgentInfo() {
        return biz.listAllSimpleAgentInfo();
    }

    @Override
    public List<SimpleAgentInfoVo> listAllSimpleAgentInfoByParam(Map<String,Object> param) {
        return biz.listAllSimpleAgentInfoByParam(param);
    }

    @Override
    public PageResult<List<Agent>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public PageResult<List<AgentResVo>> listVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listVoPage(paramMap, pageParam);
    }

    @Override
    public Agent getOne(Map<String, Object> paramMap) {
        return biz.getOne(paramMap);
    }

    @Override
    public void update(Agent agent) {
        biz.update(agent);
    }

    @Override
    public List<SimpleAgentInfoVo> listNotRetreatAgentSimple(Long salerId) {
        return biz.listNotRetreatAgentSimple(salerId);
    }

    @Override
    public void extraHandle(String extInfo, String businessKey, String userName) {
        AgentVo agentVo = JSON.parseObject(extInfo, AgentVo.class);
        agentVo.setLoginName(userName);
        agentManagerBiz.updateAgentDetail(agentVo);
    }

    @Override
    public void createWxAgent(WxAgentRegisterVo wxAgentRegisterVo) {
        agentManagerBiz.createWxAgent(wxAgentRegisterVo);
    }
}
