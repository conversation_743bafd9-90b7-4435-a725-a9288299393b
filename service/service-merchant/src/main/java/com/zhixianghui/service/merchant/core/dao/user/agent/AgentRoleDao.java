package com.zhixianghui.service.merchant.core.dao.user.agent;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.vo.agent.AgentRoleVo;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 供应商后台角色表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class AgentRoleDao extends MyBatisDao<AgentRole, Long> {
    public void updateByIdAndAgentNo(AgentRole role) {
        super.update("updateByIdAndAgentNo", role);
    }

    public List<AgentRole> listByAgentNo(String agentNo) {
        return super.listBy(Collections.singletonMap("agentNo", agentNo));
    }

    public List<AgentRole> listByStaffId(long staffId) {
        return super.listBy("listByStaffId", Collections.singletonMap("staffId", staffId));
    }

    public PageResult<List<AgentRoleVo>> listAgentRoleVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        return super.listPage("listPage", "listPageCount", paramMap, pageParam);

    }
}
