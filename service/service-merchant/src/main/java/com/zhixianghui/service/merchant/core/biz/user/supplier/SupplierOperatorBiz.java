package com.zhixianghui.service.merchant.core.biz.user.supplier;

import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierOperator;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaff;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.service.merchant.core.dao.user.supplier.SupplierOperatorDao;
import com.zhixianghui.service.merchant.core.dao.user.supplier.SupplierStaffDao;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
* 供应商后台操作员表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierOperatorBiz {

    private final SupplierOperatorDao supplierOperatorDao;
    private final SupplierStaffDao supplierStaffDao;
    private final RedisClient redisClient;

    /**
     * 根据id获取操作员
     *
     * @param id
     */
    public SupplierOperator getById(long id) {
        return supplierOperatorDao.getById(id);
    }

    /**
     * 根据手机号码获取操作员
     *
     * @param phone
     */
    public SupplierOperator getByPhone(String phone) {
        return supplierOperatorDao.getByPhone(phone);
    }

    /**
     * 根据id删除操作员，并删除其在所有用工企业下关联的员工
     *
     * @param id
     */
    @Transactional
    public void deleteById(long id) throws BizException {
        // 查询操作员关联的员工
        List<SupplierStaff> staffs = supplierStaffDao.listByOperatorId(id);
        if (staffs.stream().anyMatch(staff -> staff.getType() == PortalStaffTypeEnum.ADMIN.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("操作员属于某商户的超级管理员，无法删除");
        }

        supplierOperatorDao.deleteById(id);
        supplierStaffDao.deleteByOperatorId(id);  // 删除操作员关联的员工

        redisClient.del(PlatformSource.SUPPLIER.getValue() + ":" + id);

        staffs.stream().forEach(supplierStaff -> redisClient.del(PlatformSource.SUPPLIER.getValue() + ":" + supplierStaff.getId() + ":" + supplierStaff.getMchNo()));
    }

    /**
     * 更新操作员
     *
     * @param operator
     */
    public void update(SupplierOperator operator) throws BizException {
        supplierOperatorDao.update(operator);
        redisClient.hset(PlatformSource.SUPPLIER.getValue() + ":" + operator.getId(),operator.getCacheMap());
    }

    /**
     * 分页查询操作员
     *
     * @param paramMap
     * @param pageParam
     */
    public PageResult<List<SupplierOperatorVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return supplierOperatorDao.listOperatorVoPage(paramMap, pageParam);
    }

    public List<SupplierOperator> getAll() {
        return supplierOperatorDao.listAll();
    }
}