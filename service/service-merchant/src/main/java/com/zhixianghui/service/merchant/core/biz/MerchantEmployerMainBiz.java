package com.zhixianghui.service.merchant.core.biz;


import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.CertificateTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.dto.EnterprisePersonnelDto;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.service.MerchantBankAccountFacade;
import com.zhixianghui.facade.merchant.vo.EmployerMainUpdateVo;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.riskcontrol.entity.NameManagement;
import com.zhixianghui.facade.riskcontrol.service.NameManagementFacade;
import com.zhixianghui.service.merchant.core.dao.MerchantDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerMainDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerPositionDao;
import com.zhixianghui.service.merchant.core.dao.MerchantInvoiceInfoDao;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 用工商户主体信息
 *
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantEmployerMainBiz {
    @Autowired
    private MerchantEmployerMainDao mainDao;
    @Autowired
    private MerchantDao merchantDao;
    @Autowired
    private MerchantFileBiz fileBiz;
    @Autowired
    private MerchantInvoiceInfoDao merchantInvoiceInfoDao;
    @Reference
    private MerchantBankAccountFacade accountFacade;
    @Autowired
    private MerchantEmployerPositionDao merchantEmployerPositionDao;
    @Autowired
    private MerchantEnterprisePersonnelBiz merchantEnterprisePersonnelBiz;
    @Reference
    private NotifyFacade notifyFacade;

    public MerchantEmployerMain getByMchNo(String mchNo) {
        return mainDao.getByMchNo(mchNo);
    }

    /**
     * 涉及到删除文件的操作，此处不能设置事务
     */
    public void updateOrInsert(EmployerMainUpdateVo vo, String loginName) {
        Date nowDate = new Date();
        // 不存在则新增
        MerchantEmployerMain main = mainDao.getByMchNo(vo.getMchNo());
        //获取企业名称
        Merchant merchant = merchantDao.getByMchNo(vo.getMchNo());
        if (main == null) {
            main = new MerchantEmployerMain();
            main.setCreateTime(nowDate);
            main.setVersion(0);
        }
        // 文字信息更新
        main.setUpdateTime(nowDate);
        main.setUpdator(loginName);
        main.setMchNo(vo.getMchNo());
        main.setShortName(vo.getShortName());
        main.setTaxNo(vo.getTaxNo());
        main.setRegisterAddrProvince(vo.getRegisterAddrProvince());
        main.setRegisterAddrCity(vo.getRegisterAddrCity());
        main.setRegisterAddrTown(vo.getRegisterAddrTown());
        main.setRegisterAddrDetail(vo.getRegisterAddrDetail());
        main.setRegisterAmount(vo.getRegisterAmount());
        main.setManagementScope(vo.getManagementScope());
        main.setManagementValidityDateType(vo.getManagementValidityDateType());
        main.setManagementTermBegin(vo.getManagementTermBegin());
        main.setManagementTermEnd(vo.getManagementTermEnd());
        main.setCertificateType(vo.getCertificateType());
        main.setLegalPersonName(vo.getLegalPersonName());
        main.setCertificateNumber(vo.getCertificateNumber());
        main.setCertificateValidityDateType(vo.getCertificateValidityDateType());
        main.setCertificateTermBegin(vo.getCertificateTermBegin());
        main.setCertificateTermEnd(vo.getCertificateTermEnd());
        main.setManagementAddrProvince(vo.getManagementAddrProvince());
        main.setManagementAddrCity(vo.getManagementAddrCity());
        main.setManagementAddrTown(vo.getManagementAddrTown());
        main.setManagementAddrDetail(vo.getManagementAddrDetail());
        if (main.getId() == null) {
            mainDao.insert(main);
        } else {
            mainDao.update(main);
        }

        //供应商更新银行卡信息使用另外一个接口
        if (merchant.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) {
            //更新银行卡信息
            MerchantBankAccount account = accountFacade.getByMchNo(vo.getMchNo());
            if (account == null) {
                account = new MerchantBankAccount();
                account.setCreateTime(new Date());
                account.setVersion(0);
            }
            account.setUpdateTime(new Date());
            account.setUpdator(loginName);
            account.setMchNo(vo.getMchNo());
            account.setAccountNo(vo.getAccountNo());
            account.setAccountName(merchant.getMchName());
            account.setBankName(vo.getBankName());
            account.setBankChannelNo(vo.getBankChannelNo());
            if (account.getId() == null) {
                accountFacade.insert(account);
            } else {
                accountFacade.update(account);
            }
        }

        //获取企业开票信息
        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoDao.getByMchNo(vo.getMchNo());
        if (merchantInvoiceInfo == null) {
            merchantInvoiceInfo = new MerchantInvoiceInfo();
            merchantInvoiceInfo.setVersion(0);
            merchantInvoiceInfo.setCreateTime(nowDate);
        }
        merchantInvoiceInfo.setUpdateTime(nowDate);
        merchantInvoiceInfo.setMchNo(vo.getMchNo());
        merchantInvoiceInfo.setMchName(merchant.getMchName());
        merchantInvoiceInfo.setTaxPayerType(vo.getTaxPayerType());
        merchantInvoiceInfo.setTaxNo(vo.getTaxNo());

        merchantInvoiceInfo.setRegisterAddrInfo(vo.getInvoiceAddress() + " " + vo.getInvoicePhone());
        merchantInvoiceInfo.setAccountNo(vo.getInvoiceAccountNo());
        merchantInvoiceInfo.setBankName(vo.getInvoiceBankName());
        merchantInvoiceInfo.setDefaultInvoiceCategoryCode(vo.getDefaultInvoiceCategoryCode());
        merchantInvoiceInfo.setDefaultInvoiceCategoryName(vo.getDefaultInvoiceCategoryName());
        if (merchantInvoiceInfo.getId() == null) {
            merchantInvoiceInfoDao.insert(merchantInvoiceInfo);
        } else {
            merchantInvoiceInfoDao.update(merchantInvoiceInfo);
        }

        // 文件处理
        // 营业执照
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.BUSINESS_LICENSE.getValue(), Lists.newArrayList(vo.getBusinessLicenseFileUrl()));

        // 身份证头像面
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.ID_CARD_HEAD.getValue(), Lists.newArrayList(vo.getIdCardHeadFileUrl()));

        // 身份证国徽面
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue(), Lists.newArrayList(vo.getIdCardEmblemFileUrl()));

        // 身份证复印件
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.ID_CARD_COPY.getValue(), Lists.newArrayList(vo.getIdCardCopyFileUrl()));

        // 门头照
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.DOOR_PHOTO.getValue(), Lists.newArrayList(vo.getDoorPhotoFileUrl()));

        // 办公室内景
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.WORK_INDOOR.getValue(), Lists.newArrayList(vo.getWorkIndoorFileUrl()));

        // 前台照片
        fileBiz.modifyFile(vo.getMchNo(), merchant.getMchName(), loginName,
                MerchantFileTypeEnum.RECEPTION.getValue(), Lists.newArrayList(vo.getReceptionFileUrl()));
    }

    public Map<String, Object> getMainInfo(String mchNo) {
        MerchantEmployerMain main = mainDao.getByMchNo(mchNo);
        Map<String, Object> maps = BeanUtil.toMap(main);
        fileBiz.listByMchNo(mchNo).stream().forEach(x -> {
            if (x.getFileType() == MerchantFileTypeEnum.BUSINESS_LICENSE.getValue()) {
                maps.put("businessLicenseFileUrl", x.getFileUrl());
            } else if (x.getFileType() == MerchantFileTypeEnum.ID_CARD_HEAD.getValue()) {
                maps.put("idCardHeadFileUrl", x.getFileUrl());
            } else if (x.getFileType() == MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue()) {
                maps.put("idCardEmblemFileUrl", x.getFileUrl());
            } else if (x.getFileType() == MerchantFileTypeEnum.ID_CARD_COPY.getValue()) {
                maps.put("idCardCopyFileUrl", x.getFileUrl());
            }
        });
        return maps;
    }

    public Map<String, Object> getBusinessInfo(String mchNo) {
        MerchantEmployerMain main = mainDao.getByMchNo(mchNo);
        Map<String, Object> maps = BeanUtil.toMap(main);
        fileBiz.listByMchNo(mchNo).stream().forEach(x -> {
            if (x.getFileType() == MerchantFileTypeEnum.DOOR_PHOTO.getValue()) {
                maps.put("doorPhotoFileUrl", x.getFileUrl());
            } else if (x.getFileType() == MerchantFileTypeEnum.WORK_INDOOR.getValue()) {
                maps.put("workIndoorFileUrl", x.getFileUrl());
            } else if (x.getFileType() == MerchantFileTypeEnum.RECEPTION.getValue()) {
                maps.put("receptionFileUrl", x.getFileUrl());
            }
        });

        //获取建议发票类目
        List<MerchantEmployerPosition> merchantEmployerPositionList = merchantEmployerPositionDao.listByMchNo(mchNo);
        List<InvoiceCategoryVo> invoiceCategoryVoList = new ArrayList<>();
        merchantEmployerPositionList.stream().forEach(x -> {
            invoiceCategoryVoList.addAll(x.getJsonEntity().getInvoiceCategoryList());
        });

        // 去重
        List<InvoiceCategoryVo> uniqueInvoiceCategoryVoList = invoiceCategoryVoList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InvoiceCategoryVo::getInvoiceCategoryCode))), ArrayList::new)
        );
        maps.put("invoiceCategoryList", uniqueInvoiceCategoryVoList);

        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoDao.getByMchNo(mchNo);
        maps.put("invoice", merchantInvoiceInfo);
        if (merchantInvoiceInfo != null) {
            String result = merchantInvoiceInfo.getRegisterAddrInfo();
            if (StringUtils.isNotBlank(result)) {
                String[] info = result.split(" ");
                maps.put("invoicePhone", info[1]);
                maps.put("invoiceAddress", info[0]);
            }
            maps.put("defaultInvoiceCategoryName", merchantInvoiceInfo.getDefaultInvoiceCategoryName());
            maps.put("taxPayerType", merchantInvoiceInfo.getTaxPayerType());
            maps.put("invoiceAccountNo", merchantInvoiceInfo.getAccountNo());
            maps.put("invoiceBankName", merchantInvoiceInfo.getBankName());
        }

        List<MerchantEnterprisePersonnel> list = merchantEnterprisePersonnelBiz.getList(mchNo);
        maps.put("personnels", list);

        //联系人信息
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        maps.put("contactPhone", merchant.getContactPhone());
        maps.put("contactName", merchant.getContactName());
        maps.put("servicePhone", merchant.getServicePhone());
        return maps;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateMain(MerchantUpdateVo merchantUpdateVo) {
        Merchant merchant = merchantDao.getByMchNo(merchantUpdateVo.getMchNo());
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        MerchantEmployerMain employerMain = mainDao.getByMchNo(merchantUpdateVo.getMchNo());
        if (employerMain == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户尚未进行主体认证");
        }
        merchantUpdateVo.buildMerchantMain(employerMain);
        mainDao.update(employerMain);
        //修改税号
        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoDao.getByMchNo(merchantUpdateVo.getMchNo());
        if (merchantInvoiceInfo != null) {
            merchantInvoiceInfo.setTaxNo(merchantUpdateVo.getTaxNo());
            merchantInvoiceInfoDao.update(merchantInvoiceInfo);
        }

        List<MerchantEnterprisePersonnel> merchantEnterprisePersonnels = merchantEnterprisePersonnelBiz.getList(merchantUpdateVo.getMchNo());
        merchantEnterprisePersonnels.forEach(e -> {
            if (e.getIsLegal()) {
                e.setIdCardNumber(merchantUpdateVo.getCertificateNumber());
                e.setName(merchantUpdateVo.getLegalPersonName());
            }
        });
        if (CollectionUtils.isEmpty(merchantEnterprisePersonnels)) {
            MerchantEnterprisePersonnel merchantEnterprisePersonnel = new MerchantEnterprisePersonnel();
            merchantEnterprisePersonnel.setMchNo(merchantUpdateVo.getMchNo());
            merchantEnterprisePersonnel.setIdCardNumber(merchantUpdateVo.getCertificateNumber());
            merchantEnterprisePersonnel.setName(merchantUpdateVo.getLegalPersonName());
            merchantEnterprisePersonnel.setIsLegal(Boolean.TRUE);
            merchantEnterprisePersonnel.setCreateTime(new Date());
            merchantEnterprisePersonnels.add(merchantEnterprisePersonnel);
        }
        List<EnterprisePersonnelDto> personnels = merchantEnterprisePersonnels.stream().map(e -> {
            EnterprisePersonnelDto personnel = BeanUtil.toObject(EnterprisePersonnelDto.class, e);
            personnel.setUpdator(merchantUpdateVo.getUpdator());
            personnel.setMchName(merchant.getMchName());
            return personnel;
        }).collect(Collectors.toList());

        merchantEnterprisePersonnelBiz.updatePersonnel(personnels, merchantUpdateVo.getMchNo());
        notifyFacade.sendOne(MessageMsgDest.TOPIC_RISK_NAME_SYNC, NotifyTypeEnum.RISK_SYNC.getValue()
                , MessageMsgDest.TAG_RISK_NAME_SYNC, JSONArray.toJSONString(personnels));


        // 营业执照
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(), merchantUpdateVo.getUpdator(),
                MerchantFileTypeEnum.BUSINESS_LICENSE.getValue(), Lists.newArrayList(merchantUpdateVo.getBusinessLicenseFileUrl()));
        // 身份证头像面
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(), merchantUpdateVo.getUpdator(),
                MerchantFileTypeEnum.ID_CARD_HEAD.getValue(), Lists.newArrayList(merchantUpdateVo.getIdCardHeadFileUrl()));
        // 身份证国徽面
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(), merchantUpdateVo.getUpdator(),
                MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue(), Lists.newArrayList(merchantUpdateVo.getIdCardEmblemFileUrl()));
        // 身份证复印件
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(), merchantUpdateVo.getUpdator(),
                MerchantFileTypeEnum.ID_CARD_COPY.getValue(), Lists.newArrayList(merchantUpdateVo.getIdCardCopyFileUrl()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBusiness(MerchantUpdateVo merchantUpdateVo) {
        Merchant merchant = merchantDao.getByMchNo(merchantUpdateVo.getMchNo());
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        MerchantEmployerMain employerMain = mainDao.getByMchNo(merchantUpdateVo.getMchNo());
        if (employerMain == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户尚未进行主体认证");
        }
        if (CollectionUtils.isEmpty(merchantUpdateVo.getPersonnels())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("企业人员不能为空");
        }

        merchantEnterprisePersonnelBiz.check(merchantUpdateVo.getPersonnels());

        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoDao.getByMchNo(merchantUpdateVo.getMchNo());
        merchantUpdateVo.buildMerchantBusiness(merchant, employerMain, merchantInvoiceInfo);
        merchantDao.update(merchant);
        mainDao.update(employerMain);
        if (merchantInvoiceInfo.getId() == null) {
            merchantInvoiceInfoDao.insert(merchantInvoiceInfo);
        } else {
            merchantInvoiceInfoDao.update(merchantInvoiceInfo);
        }

        merchantUpdateVo.getPersonnels().forEach(personnel -> {
            personnel.setMchNo(merchant.getMchNo());
            personnel.setMchName(merchant.getMchName());
            personnel.setUpdator(merchantUpdateVo.getUpdator());
        });
        merchantEnterprisePersonnelBiz.updatePersonnel(merchantUpdateVo.getPersonnels(), merchantUpdateVo.getMchNo());

        notifyFacade.sendOne(MessageMsgDest.TOPIC_RISK_NAME_SYNC, NotifyTypeEnum.RISK_SYNC.getValue()
                , MessageMsgDest.TAG_RISK_NAME_SYNC, JSONArray.toJSONString(merchantUpdateVo.getPersonnels()));
        // 门头照
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(), merchantUpdateVo.getUpdator(),
                MerchantFileTypeEnum.DOOR_PHOTO.getValue(), Lists.newArrayList(merchantUpdateVo.getDoorPhotoFileUrl()));
        // 办公室内景
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(), merchantUpdateVo.getUpdator(),
                MerchantFileTypeEnum.WORK_INDOOR.getValue(), Lists.newArrayList(merchantUpdateVo.getWorkIndoorFileUrl()));
        // 前台照片
        fileBiz.modifyFile(merchantUpdateVo.getMchNo(), merchant.getMchName(), merchantUpdateVo.getUpdator(),
                MerchantFileTypeEnum.RECEPTION.getValue(), Lists.newArrayList(merchantUpdateVo.getReceptionFileUrl()));
    }
}
