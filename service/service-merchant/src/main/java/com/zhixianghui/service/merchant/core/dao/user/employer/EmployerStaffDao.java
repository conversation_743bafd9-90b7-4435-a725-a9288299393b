package com.zhixianghui.service.merchant.core.dao.user.employer;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerStaff;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import org.springframework.stereotype.Repository;

import java.util.*;


@Repository
public class EmployerStaffDao extends MyBatisDao<EmployerStaff, Long>{

    public List<EmployerStaff> listByOperatorId(long operatorId) {
        return super.listBy(Collections.singletonMap("operatorId", operatorId));
    }

    public void updateTypeById(Long id, int type, String updator) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        param.put("type", type);
        param.put("updator", updator);
        param.put("update_time", new Date());
        super.update("updateTypeById", param);
    }

    public void deleteByOperatorId(long operatorId) {
        super.deleteBy("deleteByOperatorId", operatorId);
    }

    public void deleteByMchNoAndId(String mchNo, long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("id", id);
        super.deleteBy("deleteByMchNoAndId", param);
    }

    public EmployerStaffVO getVOByMchNoAndId(String mchNo, long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("id", id);
        return getOne("getVOByMchNoAndId", param);
    }

    public EmployerStaffVO getVOByMchAndPhone(String mchNo, String phone) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("phone", phone);
        return getOne("getVOByMchAndPhone", param);
    }

    public List<EmployerStaffVO> getVOByMchNoAndType(String mchNo, int type) {
        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", mchNo);
        param.put("type", type);
        return listBy("getVOByMchNoAndType", param);
    }

    public List<EmployerStaffVO> listVOByOperatorId(Long id) {
        return listBy("listVOByOperatorId", Collections.singletonMap("operatorId", id));
    }

    public List<EmployerStaffVO> listVOByPhone(String phone) {
        return listBy("listVOByPhone", Collections.singletonMap("phone", phone));
    }

    public List<EmployerStaffVO> listStaffVOByRoleId(long roleId) {
        return listBy("listStaffVOByRoleId", Collections.singletonMap("roleId", roleId));
    }

    public PageResult<List<EmployerStaffVO>> listStaffVOPage(Map<String, Object> paramMap, PageParam pageParam) {
        return listPage("listStaffRoleVOPage", "listStaffRoleVOPageCount", paramMap, pageParam);
    }

    public List<String> getDistinctStaffByRoleIdAndMchNo(Map<String, Object> paramMap) {
        return listBy("getDistinctStaffByRoleIdAndMchNo",paramMap);
    }
}