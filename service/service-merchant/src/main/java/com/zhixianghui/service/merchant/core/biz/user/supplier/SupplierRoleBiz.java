package com.zhixianghui.service.merchant.core.biz.user.supplier;

import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRoleFunction;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierRoleVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.service.merchant.core.dao.user.supplier.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* 供应商后台角色表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierRoleBiz {

    private final SupplierRoleDao supplierRoleDao;
    private final SupplierFunctionDao supplierFunctionDao;
    private final SupplierRoleFunctionDao supplierRoleFunctionDao;
    private final SupplierStaffRoleDao supplierStaffRoleDao;
    private final SupplierStaffDao supplierStaffDao;

    /**
     * 创建用工企业后台角色
     *
     * @param role 角色
     */
    public void create(SupplierRole role) {
        if (role.getCreateTime() == null) {
            role.setCreateTime(new Date());
        }
        supplierRoleDao.insert(role);
    }

    /**
     * 根据id查询用工企业后台角色
     *
     * @param mchNo 商户编号
     * @param id
     */
    public SupplierRole getById(String mchNo, long id) {
        if (StringUtil.isEmpty(mchNo)) {
            return null;
        }

        SupplierRole role = supplierRoleDao.getById(id);
        if (role == null) {
            return null;
        }
        if (String.valueOf(RoleTypeEnum.SUPPLIER_EMPLOYER_NO.getType()).equals(role.getMchNo())) {
            return role;
        }
        if (mchNo.equals(role.getMchNo())) {
            return role;
        }
        return null;
    }


    /**
     * 更新用工企业后台角色
     *
     * @param role 角色
     */
    public void update(SupplierRole role) throws BizException {
        if (role == null || role.getMchNo() == null || role.getId() == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户编号和id不能为空");
        }
        if (getById(role.getMchNo(), role.getId()) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        supplierRoleDao.updateByIdAndMchNo(role);
    }

    /**
     * 根据id删除用工企业后台角色，并删除其与功能的关联
     *
     * @param mchNo  商户编号
     * @param id	 角色id
     */
    @Transactional
    public void deleteById(String mchNo, long id) throws BizException {
        if (getById(mchNo, id) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        supplierRoleDao.deleteById(id);  // 删除角色
        supplierRoleFunctionDao.deleteByRoleId(id);  // 删除该角色与功能的关联
        supplierStaffRoleDao.deleteByRoleId(id);  // 删除该角色与员工的关联
    }

    /**
     * 为角色分配功能
     *
     * @param mchNo         商户编号
     * @param roleId      角色id
     * @param functionIds 功能id
     */
    @Transactional
    public void updateFunction(String mchNo, long roleId, List<Long> functionIds) throws BizException {
        if (getById(mchNo, roleId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        // 先删除角色关联的所有功能
        supplierRoleFunctionDao.deleteByRoleId(roleId);

        // 添加功能
        assignFunction(roleId, functionIds);
    }

    /**
     * 根据角色id获取其关联的功能
     *
     * @param mchNo    商户编号
     * @param roleId 角色id
     */
    public List<SupplierFunction> listFunctionByRoleId(String mchNo, long roleId) throws BizException {
        if (getById(mchNo, roleId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        return supplierFunctionDao.listByRoleId(roleId);
    }

    /**
     * 根据角色id获取其关联的操作员
     *
     * @param mchNo    商户编号
     * @param roleId 角色id
     */
    public List<SupplierStaffVO> listStaffByRoleId(String mchNo, long roleId) throws BizException {
        if (getById(mchNo, roleId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        return supplierStaffDao.listStaffVOByRoleId(roleId);
    }

    /**
     * 查询所有角色
     */
    public List<SupplierRole> listAll(String mchNo) {
        return supplierRoleDao.listBy(Collections.singletonMap("mchNo", mchNo));
    }

    /**
     * 分页查询角色
     *
     * @param mchNo
     * @param paramMap
     * @param pageParam
     */
    public PageResult<List<SupplierRoleVo>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam) {
        if (paramMap == null) {
            paramMap = new HashMap<>();
        }
        paramMap.put("mchNo", mchNo);
        return supplierRoleDao.listSupplierRoleVoPage(paramMap, pageParam);
    }

    /**
     * 为角色分配功能
     */
    private void assignFunction(long roleId, List<Long> functionIds) {
        if (functionIds != null && !functionIds.isEmpty()) {
            Set<Long> allFunctionIdSet = supplierFunctionDao.listAll().stream().map(SupplierFunction::getId).collect(Collectors.toSet());
            // 校验功能id是否正确
            if (functionIds.stream().anyMatch(functionId -> !allFunctionIdSet.contains(functionId))) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能id错误");
            }

            List<SupplierRoleFunction> roleFunctions = functionIds.stream().map(functionId -> {
                SupplierRoleFunction roleFunction = new SupplierRoleFunction();
                roleFunction.setCreateTime(new Date());
                roleFunction.setRoleId(roleId);
                roleFunction.setFunctionId(functionId);
                return roleFunction;
            }).collect(Collectors.toList());
            supplierRoleFunctionDao.insert(roleFunctions);
        }
    }

    public PageResult<List<SupplierRoleVo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return supplierRoleDao.listPage("pmsListPage", "countSupplierRole", paramMap, pageParam);
    }

    public Long count(SupplierRoleVo supplierRoleVo, String mchNo) {
        HashMap<String, Object> map = new HashMap<String, Object>(){{
           put("mchNo", mchNo);
           put("roleId", supplierRoleVo.getId());
        }};
        return supplierRoleDao.countBy("countByPresetRole", map);
    }
}