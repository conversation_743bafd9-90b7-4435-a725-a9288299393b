package com.zhixianghui.service.merchant.core.biz;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignNotifyStatusEnum;
import com.zhixianghui.common.statics.enums.common.SuccessFailCodeEnum;
import com.zhixianghui.common.statics.enums.fee.ProductTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.*;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.sign.ChannelSignFacade;
import com.zhixianghui.facade.banklink.utils.esign.ESignHelpUtil;
import com.zhixianghui.facade.banklink.utils.esign.HeaderConstant;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.CreateFileUploadUrlResVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.CreateFileUploadUrlVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.StructComponent;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.UploadFileReqVo;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.enums.AgreementComponentTypeEnum;
import com.zhixianghui.facade.merchant.enums.AgreementSignModeEnum;
import com.zhixianghui.facade.merchant.enums.AgreementSpecialValueEnum;
import com.zhixianghui.facade.merchant.vo.*;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.service.ESignFacade;
import com.zhixianghui.facade.trade.vo.esign.ESign;
import com.zhixianghui.facade.trade.vo.esign.ESignItem;
import com.zhixianghui.service.merchant.core.dao.AgreementDao;
import com.zhixianghui.service.merchant.core.dao.AgreementFileDao;
import com.zhixianghui.service.merchant.core.dao.AgreementSignerDao;
import com.zhixianghui.service.merchant.core.util.PdfUConvertUtil;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.joda.time.LocalDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.AGREEMENT_SQL;

/**
* <p>
* 协议表Biz
* </p>
*
* <AUTHOR>
* @version 创建时间： 2020-09-02
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementBiz {

    private final AgreementDao agreementDao;
    private final AgreementFileDao agreementFileDao;
    private final AgreementSignerDao agreementSignerDao;
    private final FastdfsClient fastdfsClient;
    private final MerchantBiz merchantBiz;
    private final MerchantEmployerBiz merchantEmployerBiz;
    private final MerchantEnterprisePersonnelBiz merchantEnterprisePersonnelBiz;
    private final MerchantEmployerPositionBiz merchantEmployerPositionBiz;
    private final MerchantCkhQuoteBiz merchantCkhQuoteBiz;

    @Reference
    private ChannelSignFacade channelSignFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private ESignFacade eSignFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private NotifyFacade notifyFacade;

    private static final String AGREEMENT_NAME = "共享经济综合服务合同";
    private static final String DATA_DICTIONARY_NAME = "AgreementComponentEnum";
    private static final ThreadLocal<DateFormat> df = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    @Transactional(rollbackFor = Exception.class)
    public void createAgreement(AgreementDto agreementDto) {
        //生成协议号
        String legalNo = sequenceFacade.nextRedisId(AGREEMENT_SQL.getPrefix(), AGREEMENT_SQL.getKey(), AGREEMENT_SQL.getWidth());
        agreementDto.setAgreementNo(legalNo);

        Agreement agreement = new Agreement();
        BeanUtils.copyProperties(agreementDto, agreement);
        //线下签署直接为签署中
        if (agreementDto.getSignType().intValue() == AgreementSignTypeEnum.OFFLINE.getValue()){
            agreement.setStatus(AgreementStatusEnum.SIGNING.getValue());
        }
        //协议信息
        agreementDao.insert(agreement);
        Long agreementId = agreement.getId();

        //协议签署人信息
        List<AgreementSigner> signerList = agreementDto.getSignerList();
        if(signerList == null || signerList.isEmpty()){throw CommonExceptions.PARAM_INVALID.newWithErrMsg("协议签署人为空"); }
        signerList.forEach(signer -> {
            signer.setAgreementId(agreementId);
            signer.setStatus(AgreementSignerStatusEnum.WAIT_SIGN.getValue());
            //如果是单方签署且不属于签署方，设置为已签
            if (agreementDto.getSignMode().intValue() == AgreementSignModeEnum.SINGLE_SIGN.getValue() && signer.getSignerType().intValue() != agreementDto.getSingleSignerType().intValue()){
                signer.setStatus(AgreementSignerStatusEnum.SIGNED.getValue());
            }
        });
        agreementSignerDao.insert(agreementDto.getSignerList());

        //协议文件信息
        if (agreementDto.getSignType().intValue() == AgreementSignTypeEnum.OFFLINE.getValue()){
            List<AgreementFile> fileList = agreementDto.getFileList();
            if(fileList == null || fileList.isEmpty()){throw CommonExceptions.PARAM_INVALID.newWithErrMsg("协议文件为空"); }
            fileList.forEach(file -> file.setAgreementId(agreementId));
            agreementFileDao.insert(fileList);
        }

        if (agreementDto.getSignType().intValue() == AgreementSignTypeEnum.OFFLINE.getValue()){
            return;
        }

        //线上签约
        notifyFacade.sendOne(MessageMsgDest.TOPIC_AGREEMENT_SIGN_ASYNC,
                NotifyTypeEnum.SIGN_NOTIFY.getValue(),
                MessageMsgDest.TAG_AGREEMENT_SIGN_ASYNC,
                JsonUtil.toString(agreementDto));
    }

    private ESign handleTemplateSign(AgreementDto agreementDto,String legalNo) {
        //线上签约处理
        List<ESignItem> eSignItemList = new ArrayList<>();
        AgreementSigner mchSigner = null,mainstaySigner = null;
        for (AgreementSigner x : agreementDto.getSignerList()) {

            //已签的跳过
            if (x.getStatus().intValue() == AgreementSignerStatusEnum.SIGNED.getValue()){
                continue;
            }

            ESignItem eSignItem;
            if (x.getSignerType().intValue() == AgreementSignerTypeEnum.FIRST_PARTY.getValue()){
                mchSigner = x;
                eSignItem = ESignItem.builder().autoExecute(false).mchNo(x.getSignerNo()).signerMchName(x.getSignerMchName()).signerName(x.getSignerName()).signerPhone(x.getSignerPhone()).key("mchSignArea").build();
            }else{
                mainstaySigner = x;
                eSignItem = ESignItem.builder().autoExecute(false).mchNo(x.getSignerNo()).signerMchName(x.getSignerMchName()).signerName(x.getSignerName()).signerPhone(x.getSignerPhone()).key("supplierSignArea").build();
            }
            eSignItemList.add(eSignItem);
        }
        HashMap<String,String> simpleField = getSignField(agreementDto,mchSigner,mainstaySigner);

        ESign sign = eSignFacade.sign(ESign.builder()
                .logNo(legalNo)
                .templateId(agreementDto.getFileTemplateId())
                .flowName(AGREEMENT_NAME)
                .fileName(AGREEMENT_NAME)
                .eSignItems(eSignItemList)
                .signValidity(agreementDto.getDeadline().getTime())
                .simpleFormFields(simpleField)
                .build());
        return sign;
    }

    private HashMap<String, String> getSignField(AgreementDto agreementDto, AgreementSigner mchSigner, AgreementSigner mainstaySigner) {
        Map<String,Object> paramMap = new HashMap<>();

        Map<String,Object> dataMap = new HashMap<>();
        MerchantEmployerDetailVo merchant = null;
        MerchantEmployerDetailVo mainstay = null;
        //商户详细信息
        if(ObjectUtil.isNotEmpty(mchSigner)){
            paramMap.put("mchNo",mchSigner.getSignerNo());
            merchant = merchantEmployerBiz.getInfoVO(paramMap,null);
            dataMap.putAll(BeanUtil.toMap(merchant,"M_"));
            dataMap.putAll(BeanUtil.toMap(mchSigner,"SIGNER1_"));
        }
        //供应商详细信息
        if(ObjectUtil.isNotEmpty(mainstaySigner)) {
            paramMap.put("mchNo", mainstaySigner.getSignerNo());
             mainstay = merchantEmployerBiz.getInfoVO(paramMap, null);
            dataMap.putAll(BeanUtil.toMap(mainstay, "S_"));
            dataMap.putAll(BeanUtil.toMap(mainstaySigner, "SIGNER2_"));
        }
        dataMap.putAll(BeanUtil.toMap(agreementDto,"A_"));

        //获取字典
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(DATA_DICTIONARY_NAME);
        List<HashMap> dictionaryItems = JsonUtil.toList(dataDictionary.getDataInfo(), HashMap.class);
        HashMap<String,String> signField = new HashMap<>();
        for (Map itemMap : dictionaryItems) {
            String componentKey = (String) itemMap.get("code");
            String componentType = (String) itemMap.get("desc");
            String componentCode = (String) itemMap.get("flag");
            Object value = dataMap.get(componentCode);
            // 日期格式化
            if(value instanceof Date){
                value = df.get().format(value);
            }
            String dataValue = value == null ? "" : value.toString();
            if (componentType.equals(AgreementComponentTypeEnum.KEY.getValue())){
                //普通类型
                signField.put(componentKey,dataValue);
            }else if (componentType.equals(AgreementComponentTypeEnum.FILE.getValue())){
                //文件类型
                String extension = FilenameUtils.getExtension(dataValue);
                byte[] bytes = getFile(dataValue,extension);
                String fileId = handleImgComponent(bytes,extension);
                signField.put(componentKey,fileId);
            }else if (componentType.equals(AgreementComponentTypeEnum.SPECIAL.getValue())){
                String fieldValue = handleSpecialValue(merchant,mainstay,componentCode);
                signField.put(componentKey,fieldValue);
            }else{
                signField.put(componentKey,componentType);
            }
        }
        return signField;
    }

    private String handleSpecialValue(MerchantEmployerDetailVo merchant,MerchantEmployerDetailVo mainstay, String componentCode){
        String value = "";
        if (componentCode.equals(AgreementSpecialValueEnum.MCH_PERSONNELS_NAME.getValue())&&ObjectUtil.isNotEmpty(merchant)){
            //董监高
            List<MerchantEnterprisePersonnel> merchantEnterprisePersonnelList = merchantEnterprisePersonnelBiz.getList(merchant.getMchNo());
            if (merchantEnterprisePersonnelList.size() > 0){
                value = String.join(",",merchantEnterprisePersonnelList.stream().map(MerchantEnterprisePersonnel::getName).collect(Collectors.toList()));
            }
        } else if (componentCode.equals(AgreementSpecialValueEnum.MCH_QUOTE_RATE.getValue())&&ObjectUtil.isNotEmpty(merchant)&&ObjectUtil.isNotEmpty(mainstay)){
            MerchantEmployerQuoteVo merchantEmployerQuoteVo = merchant.getQuoteVoList().stream().filter(
                    x->x.getMainstayMchNo().equals(mainstay.getMchNo())).findFirst().orElse(null);
            if (merchantEmployerQuoteVo != null){
                if (merchantEmployerQuoteVo.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
                    value = merchantEmployerQuoteVo.getRate().toString();
                }else if (merchantEmployerQuoteVo.getProductNo().equals(ProductNoEnum.CKH.getValue())){
                    value = merchantCkhQuoteBiz.getFeeRate(merchant.getMchNo(),mainstay.getMchNo(), ProductNoEnum.CKH.getValue()).getServiceFeeRate().multiply(new BigDecimal("100")).toPlainString();
                }
            }
        } else if(componentCode.equals(AgreementSpecialValueEnum.MCH_WORK_CATEGORY_NAME.getValue())&&ObjectUtil.isNotEmpty(merchant)&&ObjectUtil.isNotEmpty(mainstay)){
            MerchantEmployerQuoteVo merchantEmployerQuoteVo = merchant.getQuoteVoList().stream().filter(
                    x->x.getMainstayMchNo().equals(mainstay.getMchNo())).findFirst().orElse(null);
            log.info("E签宝签约，merchant查询结果：{}", JSONUtil.toJsonPrettyStr(merchant));
            log.info("E签宝签约，报价单查询结果：{}", JSONUtil.toJsonPrettyStr(merchantEmployerQuoteVo));
            //查询关联的岗位类目
            List<MerchantEmployerPosition> merchantEmployerPositionList = merchantEmployerPositionBiz.listByMchNoWithQuote(
                    new HashMap<String,Object>(){{put("quoteId",merchantEmployerQuoteVo.getId());}});
            if (merchantEmployerPositionList.size() > 0){
                value = String.join("；",merchantEmployerPositionList.stream().map(MerchantEmployerPosition::getWorkCategoryName).collect(Collectors.toList()));
            }
        } else if(componentCode.equals(AgreementSpecialValueEnum.MCH_SERVICE_DESC.getValue())&&ObjectUtil.isNotEmpty(merchant)&&ObjectUtil.isNotEmpty(mainstay)){
            MerchantEmployerQuoteVo merchantEmployerQuoteVo = merchant.getQuoteVoList().stream().filter(
                    x->x.getMainstayMchNo().equals(mainstay.getMchNo())).findFirst().orElse(null);
            //查询关联的岗位类目
            if(ObjectUtil.isNotEmpty(merchantEmployerQuoteVo)) {
                List<MerchantEmployerPosition> merchantEmployerPositionList = merchantEmployerPositionBiz.listByMchNoWithQuote(
                        new HashMap<String, Object>() {{
                            put("quoteId", merchantEmployerQuoteVo.getId());
                        }});
                for (int i = 0; i < merchantEmployerPositionList.size(); i++) {
                    value += (i + 1) + "." + merchantEmployerPositionList.get(i).getServiceDesc() + "；";
                }
            }
        } else if (componentCode.equals(AgreementSpecialValueEnum.MCH_INVOCENAME.getValue())&&ObjectUtil.isNotEmpty(merchant)&&ObjectUtil.isNotEmpty(mainstay)){
            MerchantEmployerQuoteVo merchantEmployerQuoteVo = merchant.getQuoteVoList().stream().filter(
                    x->x.getMainstayMchNo().equals(mainstay.getMchNo())).findFirst().orElse(null);
            //查询关联的岗位类目
            if(ObjectUtil.isNotEmpty(merchantEmployerQuoteVo)){
                List<MerchantEmployerPosition> merchantEmployerPositionList = merchantEmployerPositionBiz.listByMchNoWithQuote(
                        new HashMap<String,Object>(){{put("quoteId",merchantEmployerQuoteVo.getId());}});
                for (MerchantEmployerPosition merchantEmployerPosition : merchantEmployerPositionList) {
                    for (InvoiceCategoryVo invoiceCategoryVo : merchantEmployerPosition.getInvoiceCategoryList()) {
                        value += invoiceCategoryVo.getInvoiceCategoryName()+"；";
                    }
                }
            }
        }else if (componentCode.equals(AgreementSpecialValueEnum.MCH_SIGN_DATE.getValue())){
            value = cn.hutool.core.date.DateUtil.format(new Date(),"yyyy年MM月");
        }else if (componentCode.equals(AgreementSpecialValueEnum.MCH_REGISTER_ADDR.getValue())&&ObjectUtil.isNotEmpty(merchant)){
            value = merchant.getRegisterAddrProvince() +
                    merchant.getRegisterAddrCity() +
                    merchant.getRegisterAddrTown() +
                    merchant.getRegisterAddrDetail();
        }else if (componentCode.equals(AgreementSpecialValueEnum.MCH_MANAGEMENT_ADDR.getValue())&&ObjectUtil.isNotEmpty(merchant)){
            value = merchant.getManagementAddrProvince() +
                    merchant.getManagementAddrCity() +
                    merchant.getManagementAddrTown() +
                    merchant.getManagementAddrDetail();
        }else if (componentCode.equals(AgreementSpecialValueEnum.MAINSTAY_MANAGEMENT_ADDR.getValue())&&ObjectUtil.isNotEmpty(mainstay)){
            value = mainstay.getManagementAddrProvince() +
                    mainstay.getManagementAddrCity() +
                    mainstay.getManagementAddrTown() +
                    mainstay.getManagementAddrDetail();
        }
        else if (componentCode.equals(AgreementSpecialValueEnum.SPECIAL_BEGIN_DATE.getValue())){
            value = DateUtil.formatChineseDate(new Date());
        }
        else if (componentCode.equals(AgreementSpecialValueEnum.SPECIAL_END_DATE.getValue())){
            value = DateUtil.formatChineseDate(DateUtil.addYear(new Date(), 1));
        }
        return value;
    }

    /**
     * 上传图片到e签宝处理
     * @param bytes
     * @return
     */
    public String handleImgComponent(byte[] bytes,String extension) {
        String md5 = ESignHelpUtil.getStringContentMD5(bytes);
        CreateFileUploadUrlVo createFileUploadUrlVo = new CreateFileUploadUrlVo(md5,
                HeaderConstant.CONTENTTYPE_STREAM.getValue(),
                false,
                UUIDUitl.generateMixString(10) + "." + extension,
                Long.valueOf(bytes.length));
        EsignResVo<CreateFileUploadUrlResVo> resVo = channelSignFacade.getUploadUrl(createFileUploadUrlVo);
        if (resVo.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取下载链接失败");
        }
        //上传文件
        UploadFileReqVo uploadFileReqVo = new UploadFileReqVo().build(createFileUploadUrlVo,resVo.getData().getFileUploadUrl());
        boolean success = channelSignFacade.uploadFile(uploadFileReqVo,resVo.getData().getFileUploadUrl(),bytes);
        if (!success){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝上传文件出错");
        }
        return resVo.getData().getFileId();
    }

    /**
     * 读取营业执照
     * @param dataValue
     * @return
     */
    public byte[] getFile(String dataValue,String extension) {
        ByteArrayOutputStream output = null;
        try {
            byte[] bytes;
            InputStream inputStream = fastdfsClient.downloadFile(dataValue);
            if (extension.equals("pdf")){
                bytes = PdfUConvertUtil.convertToPNG(inputStream);
            }else{
                output = new ByteArrayOutputStream();
                byte[] buffer = new byte[4096];
                int n = 0;
                while (-1 != (n = inputStream.read(buffer))) {
                    output.write(buffer, 0, n);
                }
                bytes = output.toByteArray();
            }
            return bytes;
        }catch (IOException e){
            log.error("IOException：" + e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("处理文件控件异常");
        }finally {
            if (output != null){
                try {
                    output.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void editAgreement(Long agreementId, Date deadline, Date expireTime, String operatorName) {
        Agreement agreement = agreementDao.getById(agreementId);
        if(agreement == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此协议 agreementId:" + agreementId);
        }
        agreement.setDeadline(deadline);
        agreement.setExpireTime(expireTime);
        agreement.setUpdateTime(new Date());
        agreement.setUpdateOperator(operatorName);
        agreementDao.updateIfNotNull(agreement);
    }

    public void updateStatus(Long agreementId, Integer status, String description, String operatorName) {
        Agreement agreement = agreementDao.getById(agreementId);
        if(agreement == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此协议 agreementId:" + agreementId);
        }
        //已完成或者已取消 不许变更状态
        if(agreement.getStatus().equals(AgreementStatusEnum.FINISHED.getValue()) ||
           agreement.getStatus().equals(AgreementStatusEnum.CANCELED.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议状态为已完成或已取消，不允许变更，agreementId：" + agreementId);
        }
        if(StringUtils.isNotEmpty(description)){
            agreement.setDescription(description);
        }
        agreement.setStatus(status);
        agreement.setUpdateTime(new Date());
        agreement.setUpdateOperator(operatorName);
        agreementDao.update(agreement);
    }

    public PageResult<List<Agreement>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return agreementDao.listPage("listBy","countByList",paramMap, pageParam);
    }

    @Transactional(rollbackFor = Exception.class)
    public void archive(List<AgreementFile> files, String operatorName) {
        if(ObjectUtil.isEmpty(files)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("归档文件为空");
        }
        Long agreementId = files.get(0).getAgreementId();
        if(agreementId == null){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("归档文件对应的协议id为空");
        }
        Agreement agreement = agreementDao.getById(agreementId);
        if(agreement == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此协议 agreementId:" + agreementId);
        }
        if (agreement.getStatus().equals(AgreementStatusEnum.FINISHED.getValue())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("此协议已归档，请勿重新操作");
        }
        //设置归档状态
        agreement.setStatus(AgreementStatusEnum.FINISHED.getValue());
        agreement.setUpdateTime(new Date());
        agreement.setUpdateOperator(operatorName);
        agreement.setFinishTime(new Date());
        agreementDao.update(agreement);
        //更新签署方为已签
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("agreementId",agreement.getId());
        List<AgreementSigner> agreementSignerList = agreementSignerDao.listBy(paramMap);
        AgreementSigner agreementSigner = null;
        for (AgreementSigner x : agreementSignerList) {
            if (x.getSignerType().intValue() == AgreementSignerTypeEnum.FIRST_PARTY.getValue()){
                agreementSigner = x;
            }
            x.setStatus(AgreementSignerStatusEnum.SIGNED.getValue());
            x.setSignTime(new Date());
            agreementSignerDao.update(x);
        }
        //加归档文件
        agreementFileDao.insert(files);
        //激活商户

        merchantBiz.forceActive(agreementSigner.getSignerNo(),agreement.getCreateOperator());
    }

    public Agreement getAgreementById(Long id) {
        return agreementDao.getById(id);
    }

    public void expireAgreement(){
        agreementDao.expireAgreement();
    }

    public PageResult<List<AgreementResVo>> listCustomPage(Map<String,Object> map, PageParam toPageParam) {
        PageResult<List<Agreement>> pageResult = agreementDao.listPage("listCustomPage","customCountBy",map,toPageParam);
        List<AgreementResVo> list = pageResult.getData().stream().map(
                agreement -> {
                    AgreementResVo agreementResVo = new AgreementResVo();
                    BeanUtils.copyProperties(agreement,agreementResVo);
                    return agreementResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord());
    }


    public List<Agreement> getAgreementPageByMchNoAndMainstayNo(Map<String,Object> map) {
        //PageResult<List<Agreement>> pageResult = agreementDao.listPage("getPageByMchNoAndMainstayNo","mchPageBy",map,toPageParam);
        List<Agreement> result = agreementDao.listBy("getPageByMchNoAndMainstayNo",map);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void signAsync(AgreementDto agreementDto) {
        ESign eSign;
        try {
            if (StringUtils.isNotBlank(agreementDto.getFileTemplateId())){
                eSign = handleTemplateSign(agreementDto,agreementDto.getAgreementNo());
            }else{
                eSign = handleDirectSign(agreementDto,agreementDto.getAgreementNo());
            }
        } catch (BizException ex) {
            log.error("E签宝生成协议异常，协议号：[{}]",agreementDto.getAgreementNo());
            Agreement agreement = agreementDao.getOne(new HashMap<String,Object>(){{put("agreementNo",agreementDto.getAgreementNo());}});
            agreement.setStatus(AgreementStatusEnum.CANCELED.getValue());
            agreement.setErrorMsg(ex.getErrMsg());
            agreementDao.update(agreement);
            return;
        }catch (Exception e){
            log.error("E签宝生成协议异常，协议号：[{"+agreementDto.getAgreementNo()+"}]------",e.getMessage());
            log.error("E签宝生成协议异常---",e);
            Agreement agreement = agreementDao.getOne(new HashMap<String,Object>(){{put("agreementNo",agreementDto.getAgreementNo());}});
            agreement.setStatus(AgreementStatusEnum.CANCELED.getValue());
            agreement.setErrorMsg("未知错误");
            agreementDao.update(agreement);
            return;
        }

        //更新
        Agreement agreement = agreementDao.getOne(new HashMap<String,Object>(){{put("agreementNo",agreementDto.getAgreementNo());}});
        agreement.setFlowId(eSign.getFlowId());
        agreement.setFileTemplateId(agreementDto.getFileTemplateId());
        agreement.setStatus(AgreementStatusEnum.SIGNING.getValue());
        agreementDao.update(agreement);

        List<AgreementSigner> signers = agreementSignerDao.listBy(new HashMap<String,Object>(){{ put("agreementId",agreement.getId());}});
        signers.forEach(x->{
            eSign.getESignItems().forEach(e->{
                if (e.getMchNo().equals(x.getSignerNo())){

//                    x.setSignerAccountId(e.getSignAccountId());
//                    x.setSignerAuthorizedAccountId(e.getSignOrgId());
//                    //查询签署地址
//                    ExecuteUrlReqVo executeUrlReqVo = new ExecuteUrlReqVo(eSign.getFlowId(),e.getSignAccountId());
//                    executeUrlReqVo.setOrganizeId(e.getSignOrgId());
                    ;
                    EsignResVo<ExecuteUrlResDataVo> resVo =  channelSignFacade.getExecuteUrlV3(new ExecuteUrlReqVoV3().setSignFlowId(eSign.getFlowId())
                            .setOperator(new ExecuteUrlReqVoV3.Operator()
                                    .setPsnAccount(e.getSignerPhone()))
                            .setOrganization(new ExecuteUrlReqVoV3.Organization()
                                    .setOrgName(x.getSignerMchName())));

                    if (resVo.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()){
                        log.error("获取协议签署链接失败，协议签署方：[{}]，流程id：[{}]",e.getMchNo(),eSign.getFlowId());
                        return;
                    }
                    x.setSignUrl(resVo.getData().getUrl());
                }
            });
            agreementSignerDao.update(x);
        });

        String fileUrl = downLoadSignFile(eSign.getTemplateFileUrl(),agreement.getAgreementNo() + "-" + AGREEMENT_NAME +  ".pdf",agreement.getAgreementNo());
        //插入数据
        AgreementFile agreementFile = AgreementFile.builder()
                .type(AgreementFileTypeEnum.COMMON_FILE.getValue())
                .agreementId(agreement.getId())
                .fileName(eSign.getFileName() + ".pdf")
                .fileUrl(fileUrl)
                .build();
        agreementFileDao.insert(agreementFile);
    }

    private ESign handleDirectSign(AgreementDto agreementDto, String agreementNo) {
        AgreementFile file = agreementDto.getFileList().stream().filter(
                x->x.getType().intValue() == AgreementFileTypeEnum.COMMON_FILE.getValue()).findFirst().orElse(null);

        List<StructComponent> components = JsonUtil.toBean(agreementDto.getComponentsJson(),new TypeReference<List<StructComponent>>(){});

        List<ESignItem> eSignItemList = new ArrayList<>();
        AgreementSigner mchSigner = null,mainstaySigner = null;
        for (AgreementSigner agreementSigner : agreementDto.getSignerList()) {
            if (agreementSigner.getSignerType().intValue() == AgreementSignerTypeEnum.FIRST_PARTY.getValue()){
                mchSigner = agreementSigner;
            }else if (agreementSigner.getSignerType().intValue() == AgreementSignerTypeEnum.SECOND_PARTY.getValue()){
                mainstaySigner = agreementSigner;
            }
        }


        //根据组件生成eSignItem
        for (StructComponent component : components) {
            ESignItem eSignItem;
            eSignItem = ESignItem.builder().autoExecute(false)
                    .height(component.getContext().getStyle().getHeight())
                    .width(component.getContext().getStyle().getWidth())
                    .label(component.getContext().getLabel())
                    .xPos(component.getContext().getPos().getX())
                    .yPos(component.getContext().getPos().getY()).build();
            if (component.getContext().getLabel().equals("甲方签署区")){
                eSignItem.setMchNo(mchSigner.getSignerNo());
                eSignItem.setSignerMchName(mchSigner.getSignerMchName()).setSignerPhone(mchSigner.getSignerPhone()).setSignerName(mchSigner.getSignerName());
                eSignItem.setPage(component.getContext().getPos().getPage());
                eSignItemList.add(eSignItem);
            }else if(component.getContext().getLabel().equals("乙方签署区")){
                eSignItem.setMchNo(mainstaySigner.getSignerNo());
                eSignItem.setSignerMchName(mainstaySigner.getSignerMchName()).setSignerPhone(mainstaySigner.getSignerPhone()).setSignerName(mainstaySigner.getSignerName());
                eSignItem.setPage(component.getContext().getPos().getPage());
                eSignItemList.add(eSignItem);
            }else if (component.getContext().getLabel().equals("甲方骑缝章")){
                eSignItem.setMchNo(mchSigner.getSignerNo());
                eSignItem.setSignerMchName(mchSigner.getSignerMchName()).setSignerPhone(mchSigner.getSignerPhone()).setSignerName(mchSigner.getSignerName());
                eSignItem.setSignType(2);
                eSignItem.setPage(component.getContext().getPos().getStartPage() + "-" + component.getContext().getPos().getEndPage());
                eSignItemList.add(eSignItem);
            }else if (component.getContext().getLabel().equals("乙方骑缝章")){
                eSignItem.setMchNo(mainstaySigner.getSignerNo());
                eSignItem.setSignerMchName(mainstaySigner.getSignerMchName()).setSignerPhone(mainstaySigner.getSignerPhone()).setSignerName(mainstaySigner.getSignerName());
                eSignItem.setPage(component.getContext().getPos().getStartPage() + "-" + component.getContext().getPos().getEndPage());
                eSignItem.setSignType(2);
                eSignItemList.add(eSignItem);
            }
        }

        ESign sign = eSignFacade.sign(ESign.builder()
                .logNo(agreementNo)
                .fileUrl(file.getFileUrl())
                .flowName(AGREEMENT_NAME)
                .fileName(AGREEMENT_NAME)
                .eSignItems(eSignItemList)
                .signValidity(agreementDto.getDeadline().getTime())
                .build());
        return sign;
    }

    private String downLoadSignFile(String fileUrl,String fileName,String agreementNo) {
        byte[] templateFile;
        //下载模板文件并上传
        try {
            templateFile = FileUtils.download(fileUrl);
        } catch (IOException e) {
            log.error("协议文件下载失败，协议号：[{}]",agreementNo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件下载失败");
        }
        //上传模板文件到fastdfs
        return fastdfsClient.uploadFile(templateFile,fileName);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleNotify(Map<String, Object> json) {
        //查询是否属于签约的流程
        String flowId = (String) json.get("signFlowId");
        Agreement agreement = agreementDao.getOne(new HashMap<String,Object>(){{put("flowId",flowId);}});
        if (agreement == null){
            //不属于签约的流程，直接跳过
            return;
        }
        if (agreement.getStatus().intValue() != AgreementStatusEnum.SIGNING.getValue()){
            log.info("协议不处于签署中，直接跳过，协议：[{}]",JsonUtil.toString(agreement));
            return;
        }

        if (agreement.getSignType().intValue() != AgreementSignTypeEnum.ONLINE.getValue()){
            return;
        }

        String status = (String) json.get("action");
        if (status.equals(SignNotifyStatusEnum.SIGN_MISSON_COMPLETE.getStatus())){
            changeSignerStatus(agreement,json);
        }else if (status.equals(SignNotifyStatusEnum.SIGN_FLOW_COMPLETE.getStatus())){
            changeAgreementStatus(agreement,json);
        }
    }

    /**
     * 回调通知更新协议状态
     * @param json
     */
    private void changeAgreementStatus(Agreement agreement,Map<String, Object> json) {
        String flowStatus = (String) json.get("signFlowStatus");
        DateTime finishTime = cn.hutool.core.date.DateUtil.date((Long) json.get("signFlowFinishTime"));
        AgreementStatusEnum agreementStatusEnum = AgreementStatusEnum.getEnum(flowStatus);
        agreement.setStatus(agreementStatusEnum.getValue());
        agreement.setFinishTime(finishTime);
        agreementDao.update(agreement);

        if (agreementStatusEnum.getValue() != AgreementStatusEnum.FINISHED.getValue()){
            return;
        }
        //下载归档文件
        EsignResVo<DocumentDownloadResDataVo> resVo = channelSignFacade.getDocumentDownloadUrl(new DocumentDownloadReqVo(agreement.getFlowId()));
        if (resVo.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()){
            log.error("下载归档文件失败，返回参数：[{}]，协议流程id：[{}]",JsonUtil.toString(resVo),agreement.getFlowId());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("下载归档文件失败");
        }
        //上传归档文件到fastdfs
        String fileUrl = downLoadSignFile(resVo.getData().getFiles().get(0).getDownloadUrl(),
                resVo.getData().getFiles().get(0).getFileName()+".pdf",
                agreement.getAgreementNo());

        AgreementFile agreementFile = AgreementFile.builder()
                .type(AgreementFileTypeEnum.ARCHIVE.getValue())
                .fileUrl(fileUrl)
                .fileName(resVo.getData().getFiles().get(0).getFileName()+".pdf")
                .agreementId(agreement.getId())
                .build();
        agreementFileDao.insert(agreementFile);
        //激活商户
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("agreementId",agreement.getId());
        paramMap.put("signerType",AgreementSignerTypeEnum.FIRST_PARTY.getValue());
        AgreementSigner agreementSigner = agreementSignerDao.getOne(paramMap);
        merchantBiz.forceActive(agreementSigner.getSignerNo(),agreement.getCreateOperator());
    }

    /**
     * 回调通知更新签署人状态
     * @param json
     */
    private void changeSignerStatus(Agreement agreement,Map<String, Object> json) {


//        String signFlowId = (String) json.get("signFlowId");


        String accountMobile = JSONUtil.parseObj(json.get("operator")).getJSONObject("psnAccount").getStr("accountMobile");
        String orgName = JSONUtil.parseObj(json.get("organization")).getStr("orgName");

//        String accountId = (String) json.get("accountId");
//        String orgId = (String) json.get("authorizedAccountId");
        Integer signResult = (Integer) json.get("signResult");

        DateTime operateTime = cn.hutool.core.date.DateUtil.date((Long) json.get("operateTime"));

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("agreementId",agreement.getId());
        paramMap.put("signerMchName",orgName);
        paramMap.put("signerPhone",accountMobile);
        List<AgreementSigner> agreementSignerList = agreementSignerDao.listBy(paramMap);
        //更新签署状态
        agreementSignerList.stream().forEach(x->{
            if (x.getStatus().intValue() != AgreementSignerStatusEnum.WAIT_SIGN.getValue()){
                return;
            }
            x.setStatus(AgreementSignerStatusEnum.getEnum(signResult).getValue());
            x.setSignTime(operateTime);
            agreementSignerDao.update(x);
        });
    }

    public List<Agreement> listBy(Map<String, Object> paramMap) {
        return agreementDao.listBy(paramMap);
    }

    /**
     * 批量撤回
     * @param idsList
     */
    public void cancelAgreement(List<Long> idsList) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("idList",idsList);
        List<Agreement> agreementList = agreementDao.listBy(paramMap);
        agreementList.stream().forEach(x->{
            if (x.getStatus().intValue() != AgreementStatusEnum.SIGNING.getValue()){
                return;
            }

            if (x.getSignType().intValue() == AgreementSignTypeEnum.ONLINE.getValue()){
                revokeEsignFlow(x.getFlowId());
            }
            x.setStatus(AgreementStatusEnum.CANCELED.getValue());
            agreementDao.update(x);
        });
    }

    private void revokeEsignFlow(String flowId) {
        RevokeFlowVoV3 revokeFlowVo = new RevokeFlowVoV3();
        revokeFlowVo.setSignFlowId(flowId);
        EsignResVo resVo = channelSignFacade.revokeFlow(revokeFlowVo);
        if (resVo.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()){
            log.error("协议撤回失败，协议id：[{}]",flowId);
            return;
        }
    }

    /**
     * 批量删除
     * @param idList
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAgreement(List<Long> idList) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("idList",idList);
        List<Agreement> agreementList = agreementDao.listBy(paramMap);
        agreementList.stream().forEach(x->{
            agreementDao.deleteById(x.getId());
            Map<String,Object> map = new HashMap<>();
            map.put("agreementId",x.getId());
            agreementSignerDao.deleteBy("deleteByAgreementId",map);
            agreementFileDao.deleteBy("deleteByAgreementId",map);
        });
    }

    /**
     * 转线下签署
     * @param idList
     */
    public void turnToOffline(List<Long> idList) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("idList",idList);
        List<Agreement> agreementList = agreementDao.listBy(paramMap);
        agreementList.stream().forEach(x->{
            if (x.getSignType().intValue() != AgreementSignTypeEnum.ONLINE.getValue()){
                return;
            }
            if (x.getStatus().intValue() != AgreementStatusEnum.SIGNING.getValue()){
                return;
            }
            x.setSignType(AgreementSignTypeEnum.OFFLINE.getValue());
            agreementDao.update(x);
            revokeEsignFlow(x.getFlowId());
        });
    }

    /**
     * 流程延期接口
     * @param id
     * @param deadLine
     */
    public void delay(Long id, Date deadLine) {
        Agreement agreement = agreementDao.getById(id);
        if (agreement == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议不存在");
        }

        if (agreement.getSignType().intValue() == AgreementSignTypeEnum.ONLINE.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("线上发起协议签署不支持延期");
        }
        agreement.setStatus(AgreementStatusEnum.SIGNING.getValue());
        agreement.setDeadline(deadLine);
        agreement.setUpdateTime(new Date());
        agreementDao.update(agreement);
    }

    public PageResult<List<AgreementExportVo>> getExportList(Map<String, Object> paramMap, PageParam pageParam) {
        pageParam.setIsNeedTotalRecord(false);//不需要总记录数
        //1.根据条件查询出当前分页的协议id
        PageResult<List<Long>> pageResult = agreementDao.listPage("exportIdList", "", paramMap, pageParam);
        List<Long> agreementIdList = pageResult.getData();
        if (agreementIdList == null || agreementIdList.isEmpty()) {
            return PageResult.newInstance(pageParam,new ArrayList<>());
        }
        //2.根据协议id查询出协议信息
        List<AgreementExportVo> agreementVoList = agreementDao.listBy("exportList", Collections.singletonMap("idList", agreementIdList));
        //3.根据协议id查询出签署人信息
        List<AgreementSignerExportVo> signerVoList = agreementSignerDao.listBy("exportList",
                Collections.singletonMap("agreementIdList", agreementIdList));
        Map<Long, List<AgreementSignerExportVo>> signerVoMap = signerVoList.stream().collect(Collectors.groupingBy(AgreementSignerExportVo::getAgreementId));
        //4.将签署人信息放入协议信息中
        for (AgreementExportVo agreementVo : agreementVoList) {
            List<AgreementSignerExportVo> signerVoList1 = signerVoMap.get(agreementVo.getId());
            if (signerVoList1 == null || signerVoList1.isEmpty()) {
                agreementVo.setSignerVoList(new ArrayList<>());
                continue;
            }
            for (AgreementSignerExportVo signerVo : signerVoList1) {
                if (signerVo.getSignerType() == AgreementSignerTypeEnum.FIRST_PARTY.getValue()) {
                    agreementVo.setFirstSignerMchName(signerVo.getSignerMchName());
                    agreementVo.setFirstSignerName(signerVo.getSignerName());
                    agreementVo.setFirstSignerNo(signerVo.getSignerNo());
                    agreementVo.setFirstSignerPhone(signerVo.getSignerPhone());
                    agreementVo.setFirstSignerStatus(signerVo.getSignerStatus());
                    agreementVo.setFirstSignerType(signerVo.getSignerType());
                } else {
                    agreementVo.setSecondSignerMchName(signerVo.getSignerMchName());
                    agreementVo.setSecondSignerName(signerVo.getSignerName());
                    agreementVo.setSecondSignerNo(signerVo.getSignerNo());
                    agreementVo.setSecondSignerPhone(signerVo.getSignerPhone());
                    agreementVo.setSecondSignerStatus(signerVo.getSignerStatus());
                    agreementVo.setSecondSignerType(signerVo.getSignerType());
                }
            }
            agreementVo.setSignerVoList(signerVoList1);
        }
        return PageResult.newInstance(pageParam, agreementVoList);
    }

    public PageResult<List<Agreement>> listPageByMerchant(Map<String, Object> paramMap, PageParam pageParam) {
        return agreementDao.listPage("listPageByMerchant","countByMerchant",paramMap,pageParam);
    }
}
