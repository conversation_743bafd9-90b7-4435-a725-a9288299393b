package com.zhixianghui.service.merchant.core.biz.user.agent;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentOperatorStatusEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentStaffTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.user.agent.*;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierStaffRole;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.service.merchant.core.dao.user.agent.*;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* 供应商后台员工表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentStaffBiz {

    private final AgentStaffDao agentStaffDao;
    private final AgentOperatorDao agentOperatorDao;
    private final AgentRoleDao agentRoleDao;
    private final AgentFunctionDao agentFunctionDao;
    private final AgentStaffRoleDao agentStaffRoleDao;
    private final  AgentTradePwdDao agentTradePwdDao;
    private final RedisClient redisClient;

    /**
     * 根据id查询用工企业员工
     *
     * @param agentNo 	合伙人编号
     * @param id    	员工id
     */
    public AgentStaffVO getById(String agentNo, long id) {
        if (StringUtil.isEmpty(agentNo)) {
            return null;
        }
        return agentStaffDao.getVOByAgentNoAndId(agentNo, id);
    }

    /**
     * 根据手机号查询用工企业员工
     * @param agentNo		合伙人编号
     * @param phone		手机号
     */
    public AgentStaffVO getByPhone(String agentNo, String phone) {
        if (StringUtil.isEmpty(agentNo)) {
            return null;
        }
        return agentStaffDao.getVOByAgentAndPhone(agentNo, phone);
    }

    /**
     * 根据操作员id查询其关联的员工
     *
     * @param id
     * @return
     */
    public List<AgentStaffVO> listByOperatorId(long id) {
        return agentStaffDao.listVOByOperatorId(id);
    }

    /**
     * 根据手机号查询其关联的员工
     *
     * @param phone
     * @return
     */
    public List<AgentStaffVO> listByPhone(String phone) {
        return agentStaffDao.listVOByPhone(phone);
    }

    /**
     * 获取超级管理员
     *
     * @param agentNo 合伙人编号
     * @return
     */
    public AgentStaffVO getAdmin(String agentNo) throws BizException {
        if (StringUtil.isEmpty(agentNo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人编号不能为空");
        }

        List<AgentStaffVO> staffs = agentStaffDao.getVOByAgentNoAndType(agentNo, AgentStaffTypeEnum.ADMIN.getValue());
        if (staffs == null || staffs.isEmpty()) {
            return null;
        } else if(staffs.size() > 1) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("存在多个超级管理员");
        } else {
            return staffs.get(0);
        }
    }

    /**
     * 创建用工企业员工
     *
     * @param agentStaffVo vo
     */
    @Transactional
    public long create(AgentStaffVO agentStaffVo) throws BizException {
        AgentStaffVO staffVO = agentStaffDao.getVOByAgentAndPhone(agentStaffVo.getAgentNo(), agentStaffVo.getPhone());
        if (staffVO != null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工已存在");
        }
        if (agentStaffVo.getType() == AgentStaffTypeEnum.ADMIN.getValue() && getAdmin(agentStaffVo.getAgentNo()) != null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("已存在超级管理员");
        }

        AgentOperator operator = agentOperatorDao.getByPhone(agentStaffVo.getPhone());
        // 如果不存在操作员帐号，则先创建操作员
        if (operator == null) {
            operator = createOperator(agentStaffVo);
        }

        AgentTradePwd tradePwd = agentTradePwdDao.getByAgentNo(agentStaffVo.getAgentNo());
        // 如果不存在商户支付，则先创建
        if (tradePwd == null) {
            createAgentTradePwd(agentStaffVo);
        }

        // 创建员工
        AgentStaff staff = createStaff(operator.getId(), agentStaffVo);

        //写入缓存
        redisClient.hset(PlatformSource.AGENT.getValue() + ":" + operator.getId(),operator.getCacheMap());
        redisClient.hset(PlatformSource.AGENT.getValue() + ":" + staff.getId() + ":" + staff.getAgentNo(),staff.getCacheMap());

        return staff.getId();
    }

    /**
     * 创建用工企业员工并分配指定角色
     *
     * @param agentStaffVo	vo
     * @param roleIds			角色id
     */
    @Transactional
    public long createAndAssignRole(AgentStaffVO agentStaffVo, List<Long> roleIds) throws BizException {
        long staffId = create(agentStaffVo);

        // 为员工分配角色
        assignRole(agentStaffVo.getAgentNo(), staffId, roleIds);
        return staffId;
    }

    /**
     * 更换超级管理员
     *
     * @param agentNo         合伙人编号
     * @param newAdminPhone 新负责人手机号
     * @param newAdminName      新负责人姓名
     */
    @Transactional
    public void changeAdmin(String agentNo, String newAdminPhone, String newAdminName, String updator) throws BizException {
        AgentStaffVO oldAdmin = getAdmin(agentNo);
        if (oldAdmin == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("超级管理员不存在");
        }

        // 原管理员绑定关系直接删除
        agentStaffDao.deleteByAgentNoAndId(agentNo, oldAdmin.getId());
        redisClient.del(PlatformSource.AGENT.getValue() + ":" + oldAdmin.getId() + ":" + agentNo);

        AgentStaffVO newAdmin = getByPhone(agentNo, newAdminPhone);
        if (newAdmin == null) {  // 新负责人帐号不存在则直接创建
            AgentStaffVO agentStaffVO = new AgentStaffVO();
            agentStaffVO.setCreateTime(new Date());
            agentStaffVO.setCreator(updator);
            agentStaffVO.setAgentNo(agentNo);
            agentStaffVO.setAgentName(oldAdmin.getAgentName());
            agentStaffVO.setPhone(newAdminPhone);
            agentStaffVO.setName(newAdminName);
            agentStaffVO.setType(AgentStaffTypeEnum.ADMIN.getValue());
            create(agentStaffVO);
        } else {  // 新负责人修改为超级管理员
            agentStaffDao.updateTypeById(newAdmin.getId(), AgentStaffTypeEnum.ADMIN.getValue(), updator);
        }
    }

    /**
     * 为用工企业员工更新角色
     *
     * @param agentNo   合伙人编号
     * @param staffId 员工id
     * @param roleIds 角色id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRole(String agentNo, long staffId, List<Long> roleIds,String name) throws BizException {
        if (getById(agentNo, staffId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }

        // 删除员工原有的角色
        agentStaffRoleDao.deleteByStaffId(staffId);

        //更新员工姓名
        AgentStaff agentStaff = agentStaffDao.getById(staffId);
        agentStaff.setName(name);
        agentStaffDao.update(agentStaff);
        // 分配角色
        assignRole(agentNo, staffId, roleIds);

        redisClient.hset(PlatformSource.AGENT.getValue() + ":" + agentStaff.getId() + ":" + agentStaff.getAgentNo(),agentStaff.getCacheMap());
    }

    /**
     * 根据id删除用工企业员工
     *
     * @param agentNo 合伙人编号
     * @param id    员工id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String agentNo, long id) {
        AgentStaffVO staff = getById(agentNo, id);
        if (staff == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        } else if (staff.getType() == AgentStaffTypeEnum.ADMIN.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("超级管理员不能删除");
        }
        agentStaffDao.deleteByAgentNoAndId(agentNo, id);
        agentStaffRoleDao.deleteByStaffId(staff.getId());

        redisClient.del(PlatformSource.AGENT.getValue() + ":" + id + ":" + agentNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void forceDelete(String agentNo, Long id) {
        agentStaffDao.deleteByAgentNoAndId(agentNo, id);
        agentStaffRoleDao.deleteByStaffId(id);

        redisClient.del(PlatformSource.AGENT.getValue() + ":" + id + ":" + agentNo);
    }

    /**
     * 根据员工id获取其关联的角色
     *
     * @param agentNo   合伙人编号
     * @param staffId 员工id
     */
    public List<AgentRole> getRoleByStaffId(String agentNo, long staffId) throws BizException {
        if (getById(agentNo, staffId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }
        return agentRoleDao.listByStaffId(staffId);
    }

    /**
     * 查询员工所关联的功能
     *
     * @param agentNo   合伙人编号
     * @param staffId 员工id
     */
    public List<AgentFunction> listFunctionByStaffId(String agentNo, long staffId) throws BizException {
        AgentStaffVO staffVO = getById(agentNo, staffId);
        if (staffVO == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        } else if (staffVO.getType() == AgentStaffTypeEnum.ADMIN.getValue()) {
            return agentFunctionDao.listAll("number asc");
        } else {
            return agentFunctionDao.listByStaffId(staffId);
        }
    }

    /**
     * 分页查询员工
     *
     * @param paramMap
     * @param pageParam
     */
    public PageResult<List<AgentStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return agentStaffDao.listStaffVOPage(paramMap, pageParam);
    }

    /**
     * 分页查询员工
     * @param agentNo
     * @param paramMap
     * @param pageParam
     * @return
     */
    public PageResult<List<AgentStaffVO>> listPage(String agentNo, Map<String, Object> paramMap, PageParam pageParam) {
        if (paramMap == null) {
            paramMap = new HashMap<>();
        }
        paramMap.put("agentNo", agentNo);
        return agentStaffDao.listStaffVOPage(paramMap, pageParam);
    }

    /**
     * 创建操作员
     */
    private AgentOperator createOperator(AgentStaffVO agentStaffVO) {
        AgentOperator operator = new AgentOperator();
        operator.setCreateTime(new Date());
        operator.setPhone(agentStaffVO.getPhone());
        operator.setName(StringUtil.isEmpty(agentStaffVO.getName()) ? agentStaffVO.getPhone() : agentStaffVO.getName());  // 没传姓名则用手机号替代
        operator.setStatus(AgentOperatorStatusEnum.ACTIVE.getValue());
        operator.setIsInitPwd(AgentInitPwdStatusEnum.NO_INIT.getValue());
        operator.setSalt(null);
        operator.setPwd(null);
        operator.setModPwdTime(null);
        operator.setCurLoginTime(null);
        operator.setLastLoginTime(null);
        operator.setPwdErrorCount(0);
        operator.setPwdErrorTime(null);
        agentOperatorDao.insert(operator);
        return operator;
    }

    /**
     * 创建员工
     */
    private AgentStaff createStaff(Long operatorId, AgentStaffVO agentStaffVO) {

        AgentStaff staff = new AgentStaff();
        staff.setCreateTime(new Date());
        staff.setOperatorId(operatorId);
        staff.setAgentNo(agentStaffVO.getAgentNo());
        staff.setAgentName(agentStaffVO.getAgentName());

        staff.setType(agentStaffVO.getType());
        staff.setCreator(agentStaffVO.getCreator());
        staff.setName(agentStaffVO.getName());
        log.info("插入合伙人员工 : {}", JSONObject.toJSON(staff));
        agentStaffDao.insert(staff);
        return staff;
    }

    /**
     * 创建商户支付密码
     */
    private AgentTradePwd createAgentTradePwd(AgentStaffVO agentStaffVO) {
        AgentTradePwd tradePwd = new AgentTradePwd();
        tradePwd.setCreateTime(new Date());
        tradePwd.setAgentNo(agentStaffVO.getAgentNo());
        tradePwd.setStatus(AgentOperatorStatusEnum.ACTIVE.getValue());
        tradePwd.setIsInitPwd(AgentInitPwdStatusEnum.NO_INIT.getValue());
        tradePwd.setPwd(null);
        tradePwd.setPwdErrorCount(0);
        tradePwd.setPwdErrorTime(null);
        agentTradePwdDao.insert(tradePwd);
        return tradePwd;
    }

    /**
     * 为员工分配角色
     */
    private void assignRole(String agentNo, long staffId, List<Long> roleIds) {
        if (roleIds != null && !roleIds.isEmpty()) {
            // 校验角色是否都属于该商户下
            Set<Long> roleIdSet = agentRoleDao.listByAgentNo(agentNo)
                    .stream().map(AgentRole::getId).collect(Collectors.toSet());

            if (roleIds.stream().anyMatch(roleId -> !roleIdSet.contains(roleId))) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色id错误");
            }

            // 分配角色
            List<AgentStaffRole> staffRoles = roleIds.stream().map(roleId -> {
                AgentStaffRole staffRole = new AgentStaffRole();
                staffRole.setCreateTime(new Date());
                staffRole.setStaffId(staffId);
                staffRole.setRoleId(roleId);
                return staffRole;
            }).collect(Collectors.toList());
            agentStaffRoleDao.insert(staffRoles);
        }
    }

    public boolean isAdmin(AgentStaffVO agentStaffVO) {
        List<AgentStaffRole> list = agentStaffRoleDao.listBy(new HashMap<String, Object>() {{
            put("staffId", agentStaffVO.getId());
        }});
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }

        for (AgentStaffRole item : list) {
            AgentRole agentRole = agentRoleDao.getById(item.getRoleId());
            if (agentRole.getRoleType() == RoleTypeEnum.ADMIN.getType()) {
                return true;
            }
        }
        return false;
    }

    public List<AgentFunction> listAllFunction() {
        return agentFunctionDao.listAll();
    }

    public List<AgentStaff> getAll() {
        return agentStaffDao.listAll();
    }

    public void getAndPutStaffCache(Long id) {
        AgentStaff agentStaff = agentStaffDao.getById(id);
        if (agentStaff != null){
            redisClient.hset(PlatformSource.AGENT.getValue() + ":" + agentStaff.getId() + ":" + agentStaff.getAgentNo(),agentStaff.getCacheMap());
        }
    }


    public AgentStaff getByAgentNoAndPhone(String agentNo, String phone) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("agentNo",agentNo);
        paramMap.put("phone",phone);
        return agentStaffDao.getOne(paramMap);
    }

    public void update(AgentStaff agentStaff) {
        agentStaffDao.update(agentStaff);
    }

    public void updateExtraInfoByAgentNo(AgentStaffVO agentStaff) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("extraInfo",agentStaff.getExtraInfo());
        paramMap.put("agentNo",agentStaff.getAgentNo());
        agentStaffDao.update("updateExtraInfoByAgentNo",paramMap);
    }
}
