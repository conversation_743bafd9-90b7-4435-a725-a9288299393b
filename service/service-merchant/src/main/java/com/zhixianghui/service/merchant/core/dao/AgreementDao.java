package com.zhixianghui.service.merchant.core.dao;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.merchant.AgreementStatusEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.common.service.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.Map;

/**
 * 协议表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-09-02
 */
@Repository
public class AgreementDao extends MyBatisDao<Agreement, Long> {
    public void expireAgreement() {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("expireStatus",AgreementStatusEnum.EXPIRED.getValue());
        paramMap.put("finishStatus",AgreementStatusEnum.FINISHED.getValue());
        paramMap.put("nowDate", DateUtil.formatDate(new Date()));
        super.getSqlSession().update("expireAgreement", paramMap);
    }

}
