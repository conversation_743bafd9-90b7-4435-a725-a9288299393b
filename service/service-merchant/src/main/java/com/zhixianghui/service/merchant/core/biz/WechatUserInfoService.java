package com.zhixianghui.service.merchant.core.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.WechatUserInfo;
import com.zhixianghui.facade.merchant.vo.WxBindMobileReqVo;
import com.zhixianghui.service.merchant.core.dao.mapper.WechatUserInfoMapper;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class WechatUserInfoService extends ServiceImpl<WechatUserInfoMapper, WechatUserInfo> {

    @Reference
    private SequenceFacade sequenceFacade;

    public String getPhoneByOpenIdAndAppId(String openId, String appId) {
        WechatUserInfo wechatInfo = this.baseMapper.selectOne(
                new QueryWrapper<WechatUserInfo>().eq("OPEN_ID", openId)
                        .eq("APP_ID", appId)
        );
        if (wechatInfo == null) {
            return null;
        }else {
            return wechatInfo.getMobile();
        }
    }

    public WechatUserInfo register(WxBindMobileReqVo wxBindMobileReqVo) {

        WechatUserInfo wechatUserInfo = this.baseMapper.selectOne(
                new QueryWrapper<WechatUserInfo>().eq("OPEN_ID", wxBindMobileReqVo.getOpenId())
                        .eq("APP_ID", wxBindMobileReqVo.getAppId())
        );
        if (wechatUserInfo == null) {
            wechatUserInfo = new WechatUserInfo();
            wechatUserInfo.setOpenId(wxBindMobileReqVo.getOpenId());
            wechatUserInfo.setCreateAt(new Date());
            wechatUserInfo.setLastLoginAt(new Date());
            wechatUserInfo.setUpdateAt(new Date());
            wechatUserInfo.setAppId(wxBindMobileReqVo.getAppId());
            wechatUserInfo.setNickname(wxBindMobileReqVo.getNickName());
            wechatUserInfo.setGender(wxBindMobileReqVo.getGender());
            wechatUserInfo.setAvatar(wxBindMobileReqVo.getAvatarUrl());
            wechatUserInfo.setMobile(wxBindMobileReqVo.getMobile());
            wechatUserInfo.setWxPlat(wxBindMobileReqVo.getWxPlat());
            this.save(wechatUserInfo);
        }else {
            wechatUserInfo.setNickname(wxBindMobileReqVo.getNickName());
            wechatUserInfo.setGender(wxBindMobileReqVo.getGender());
            wechatUserInfo.setAvatar(wxBindMobileReqVo.getAvatarUrl());
            wechatUserInfo.setMobile(wxBindMobileReqVo.getMobile());
            this.updateById(wechatUserInfo);
        }
        return wechatUserInfo;
    }

    public WechatUserInfo getMiniUserByOpenIdAndAppId(String openId, String appId) {
        WechatUserInfo wechatInfo = this.baseMapper.selectOne(
                new QueryWrapper<WechatUserInfo>().eq("OPEN_ID", openId)
                        .eq("APP_ID", appId)
        );
        return wechatInfo;
    }

}
