package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.AgreementComponent;
import com.zhixianghui.facade.merchant.service.AgreementComponentFacade;
import com.zhixianghui.service.merchant.core.biz.AgreementComponentBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 模板组件表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-09-02
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementComponentImpl implements AgreementComponentFacade {

    private final AgreementComponentBiz biz;

    @Override
    public List<AgreementComponent> getAll() {
        return biz.getAll();
    }
}
