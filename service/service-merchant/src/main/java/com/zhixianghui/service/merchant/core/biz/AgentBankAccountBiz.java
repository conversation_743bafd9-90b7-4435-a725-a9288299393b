package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.facade.merchant.entity.AgentBankAccount;
import com.zhixianghui.service.merchant.core.dao.AgentBankAccountDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* 合伙人银行账户表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-02-01
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentBankAccountBiz {

    private final AgentBankAccountDao agentbankaccountDao;

    public AgentBankAccount getByAgentNo(String agentNo) {
        AgentBankAccount agentBankAccount = agentbankaccountDao.getByAgentNo(agentNo);
        return agentBankAccount;
    }
}