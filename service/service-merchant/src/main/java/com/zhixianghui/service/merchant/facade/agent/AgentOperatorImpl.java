package com.zhixianghui.service.merchant.facade.agent;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.service.agent.AgentOperatorFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentOperatorBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台操作员表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentOperatorImpl implements AgentOperatorFacade {

    private final AgentOperatorBiz biz;

    /**
     * 根据id获取操作员
     *
     * @param id
     */
    @Override
    public AgentOperator getById(long id) {
        return biz.getById(id);
    }

    /**
     * 根据手机号码获取操作员
     *
     * @param phone
     */
    @Override
    public AgentOperator getByPhone(String phone) {
        return biz.getByPhone(phone);
    }

    /**
     * 根据id删除操作员
     *
     * @param id
     */
    @Override
    public void deleteById(long id) throws BizException {
        biz.deleteById(id);
    }

    /**
     * 更新操作员
     *
     * @param operator
     */
    @Override
    public void update(AgentOperator operator) throws BizException {
        biz.update(operator);
    }

    /**
     * 分页查询操作员
     *
     * @param paramMap
     * @param pageParam
     */
    @Override
    public PageResult<List<AgentOperatorVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }
}
