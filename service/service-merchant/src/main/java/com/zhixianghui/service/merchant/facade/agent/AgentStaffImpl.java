package com.zhixianghui.service.merchant.facade.agent;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentStaff;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.service.merchant.core.biz.user.agent.AgentStaffBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 供应商后台员工表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentStaffImpl implements AgentStaffFacade {

    private final AgentStaffBiz biz;

    @Override
    public AgentStaff getByAgentNoAndPhone(String agentNo, String phone) {
        return biz.getByAgentNoAndPhone(agentNo,phone);
    }

    /**
     * 根据id查询供应商员工
     *
     * @param agentNo 商户编号
     * @param id    员工id
     */
    @Override
    public AgentStaffVO getById(String agentNo, long id) {
        return biz.getById(agentNo, id);
    }

    /**
     * 根据手机号查询供应商员工
     *
     * @param agentNo 商户编号
     * @param phone 手机号
     */
    @Override
    public AgentStaffVO getByPhone(String agentNo, String phone) {
        return biz.getByPhone(agentNo, phone);
    }

    /**
     * 根据操作员id查询其关联的员工
     *
     * @param id
     * @return
     */
    @Override
    public List<AgentStaffVO> listByOperatorId(long id) {
        return biz.listByOperatorId(id);
    }

    /**
     * 根据手机号查询其关联的员工
     *
     * @param phone
     * @return
     */
    @Override
    public List<AgentStaffVO> listByPhone(String phone) {
        return biz.listByPhone(phone);
    }

    /**
     * 获取超级管理员
     *
     * @param agentNo 商户编号
     * @return
     */
    @Override
    public AgentStaffVO getAdmin(String agentNo) throws BizException {
        return biz.getAdmin(agentNo);
    }

    /**
     * 创建供应商员工
     *
     * @param agentStaffVo vo
     */
    @Override
    public long create(AgentStaffVO agentStaffVo) throws BizException {
        return biz.create(agentStaffVo);
    }

    /**
     * 创建供应商员工并分配指定角色
     *
     * @param agentStaffVo
     * @param roleIds
     */
    @Override
    public long createAndAssignRole(AgentStaffVO agentStaffVo, List<Long> roleIds) throws BizException {
        return biz.createAndAssignRole(agentStaffVo, roleIds);
    }

    /**
     * 更换超级管理员
     *
     * @param agentNo         	商户编号
     * @param newAdminPhone 	新负责人手机号
     * @param newAdminName      新负责人姓名
     */
    @Override
    public void changeAdmin(String agentNo, String newAdminPhone, String newAdminName, String updator) throws BizException {
        biz.changeAdmin(agentNo, newAdminPhone, newAdminName, updator);
    }

    /**
     * 为供应商员工更新角色
     * @param agentNo     商户编号
     * @param staffId   员工id
     * @param roleIds   角色id
     */
    @Override
    public void updateRole(String agentNo, long staffId, List<Long> roleIds,String name) throws BizException {
        biz.updateRole(agentNo, staffId, roleIds,name);
    }

    /**
     * 根据id删除供应商员工
     *
     * @param agentNo 商户编号
     * @param id    员工id
     */
    @Override
    public void deleteById(String agentNo, long id) {
        biz.deleteById(agentNo, id);
    }

    /**
     * 根据员工id获取其关联的角色
     *
     * @param agentNo   商户编号
     * @param staffId 员工id
     */
    @Override
    public List<AgentRole> getRoleByStaffId(String agentNo, long staffId) throws BizException {
        return biz.getRoleByStaffId(agentNo, staffId);
    }

    /**
     * 查询员工所关联的功能
     *
     * @param agentNo   商户编号
     * @param staffId 员工id
     */
    @Override
    public List<AgentFunction> listFunctionByStaffId(String agentNo, long staffId) throws BizException {
        return biz.listFunctionByStaffId(agentNo, staffId);
    }

    /**
     * 分页查询员工
     *
     * @param paramMap
     * @param pageParam
     */
    @Override
    public PageResult<List<AgentStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    /**
     * 分页查询员工
     *
     * @param agentNo
     * @param paramMap
     * @param pageParam
     * @return
     */
    @Override
    public PageResult<List<AgentStaffVO>> listPage(String agentNo, Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(agentNo, paramMap, pageParam);
    }

    @Override
    public boolean isAdmin(AgentStaffVO agentStaffVO) {
        return biz.isAdmin(agentStaffVO);
    }

    @Override
    public List<AgentFunction> listAllFunction() {
        return biz.listAllFunction();
    }

    @Override
    public void getAndPutStaffCache(Long id) {
        biz.getAndPutStaffCache(id);
    }

    @Override
    public void update(AgentStaff agentStaff) {
        biz.update(agentStaff);
    }

    @Override
    public void updateExtraInfoByAgentNo(AgentStaffVO agentStaff) {
        biz.updateExtraInfoByAgentNo(agentStaff);
    }
}
