package com.zhixianghui.service.merchant.facade.record;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.vo.record.AgentBaseInfoVo;
import com.zhixianghui.service.merchant.core.biz.DictionaryBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantInfoChangeRecordBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2021/7/19 9:08
 */
@Slf4j
@Service("AgentInfoChange")
public class AgentInfoChange extends AbstractInfoChange<AgentBaseInfoVo> {
    private static final String DICTIONARY_NAME = "AgentInfoChange";
    private static DataDictionary dataDictionary;
    private static Map<String, String> description;

    @Autowired
    private DictionaryBiz dictionaryBiz;
    @Autowired
    private MerchantInfoChangeRecordBiz changeRecordBiz;

    public void handle(AgentBaseInfoVo newInfo, AgentBaseInfoVo oldInfo, int source) {
        logInfoAndCheck(newInfo, oldInfo, source, newInfo.getFlowId());
        Map<String, Object> oldMap = reflection(oldInfo);
        Map<String, Object> newMap = reflection(newInfo);
        compareAndRecord(oldMap, newMap, newInfo, source);

    }

    @PostConstruct
    @Override
    protected void getDictionary() {
        dataDictionary = dictionaryBiz.getDataDictionaryByName(DICTIONARY_NAME);
        description = dataDictionary.getItemList().stream().collect(Collectors.toMap(DataDictionary.Item::getCode, DataDictionary.Item::getDesc));
    }

    @Override
    protected void handleException() {

    }

    @Override
    protected void specialHandling(String name, Map<String, Object> infoMap) {
        if (!SPECIAL_FIELD.containsKey(name) || StringUtils.isBlank((String) infoMap.get(name))) {
            return;
        }
        infoMap.put(name, SPECIAL_FIELD.get(name).apply(Integer.valueOf((String) infoMap.get(name))));
    }

    protected void compareAndRecord(Map<String, Object> oldMap, Map<String, Object> newMap, AgentBaseInfoVo newInfo, int source) {
        List<MerchantInfoChangeRecord> changeList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : oldMap.entrySet()) {
            if (!newMap.containsKey(entry.getKey())) {
                log.warn("合伙人类属性发生变化 : {}", entry.getKey());
                continue;
            }
            if (newMap.get(entry.getKey()) instanceof String) {
                MerchantInfoChangeRecord record = handleString(description, entry, newMap, newInfo, source, newInfo.getAgentNo());
                if (record == null) {
                    continue;
                }
                changeList.add(record);
            }
        }
        log.info(JSONObject.toJSONString(changeList));
        if (CollectionUtils.isEmpty(changeList)) {
            return;
        }
        changeRecordBiz.insert(changeList);
    }
}
