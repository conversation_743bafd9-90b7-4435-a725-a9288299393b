package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.service.merchant.core.dao.AgentSalerDao;
import com.zhixianghui.service.merchant.facade.AgentSalerImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.merchant.entity.AgentSaler;
import lombok.RequiredArgsConstructor;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2021-02-01
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentSalerBiz {

    private final AgentSalerDao agentsalerDao;

    public AgentSaler getByAgentNo(String agentNo) {
        return agentsalerDao.getByAgentNo(agentNo);
    }

}