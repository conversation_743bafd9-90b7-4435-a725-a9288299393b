package com.zhixianghui.service.merchant.core.dao.user.employer;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class EmployerFunctionDao extends MyBatisDao<EmployerFunction, Long>{

    public void deleteByIds(List<Long> ids) {
        super.deleteBy("deleteByIds", ids);
    }

    public List<EmployerFunction> listByParentId(long parentId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", parentId);
        return super.listBy("listByParentId", paramMap, "number asc");
    }

    public List<EmployerFunction> listByRoleId(long roleId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("roleId", roleId);
        return super.listBy("listByRoleId", paramMap, "number asc");
    }

    public List<EmployerFunction> listByStaffId(long staffId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("staffId", staffId);
        return super.listBy("listByStaffId", paramMap, "number asc");
    }
}
