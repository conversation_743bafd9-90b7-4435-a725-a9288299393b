package com.zhixianghui.service.merchant.core.biz;


import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.service.merchant.core.dao.MerchantDao;
import com.zhixianghui.service.merchant.core.dao.MerchantSalerDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 商户信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantSalerBiz {
    @Autowired
    private MerchantSalerDao merchantSalerDao;

    @Autowired
    private MerchantDao merchantDao;

    public MerchantSaler getByMchNo(String mchNo) {
        LimitUtil.notEmpty(mchNo, "商户编号不能为空");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);
        return merchantSalerDao.getOne(paramMap);
    }

    public List<MerchantSaler> getBatchMerchantSaler(List<Long> saleId) {
        LimitUtil.notEmpty(saleId, "销售编号不能为空");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("salerIds", saleId);
        return merchantSalerDao.listBy(paramMap);
    }

    public void update(MerchantSaler merchantSaler) {
        merchantSalerDao.update(merchantSaler);
    }

    public List<MerchantSaler> getMerchantSaleByMchNo(List<String> mchNoList) {
        LimitUtil.notEmpty(mchNoList, "商户编号不能为空");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNos", mchNoList);
        return merchantSalerDao.listBy(paramMap);

    }

    public MerchantSaler getOne(Long saleId) {
        Map<String, Object> paramMap = new HashMap() {{
            put("salerId", saleId);
        }};
        List<MerchantSaler> list = merchantSalerDao.listBy(paramMap);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public Map<String, Object> getSalerStatistics(Map<String, Object> thisMonth,Map<String,Object> lastMonth) {
        Map<String,Object> map = new HashMap<>();
        //获取共计签约商户
        getActiveMch(thisMonth,lastMonth,map);
        //获取各个销售自己的商户数量
        getMerchantCount(thisMonth,lastMonth,map);
        return map;
    }

    public void getMerchantCount(Map<String, Object> thisMonth, Map<String, Object> lastMonth, Map<String, Object> map) {
        Map<String,Object> thisMonthCount = merchantSalerDao.getSalerCount(thisMonth);
        Map<String,Object> lastMonthCount = merchantSalerDao.getSalerCount(lastMonth);
        map.put("thisMonthCount",thisMonthCount);
        map.put("lastMonthCount",lastMonthCount);

    }

    private void getActiveMch(Map<String, Object> thisMonth, Map<String, Object> lastMonth,Map<String,Object> map) {
        thisMonth.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        thisMonth.put("authStatus", AuthStatusEnum.SUCCESS.getValue());
        Long thisMonthCount = merchantDao.selectDateActiveMch(thisMonth);

        lastMonth.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        lastMonth.put("authStatus", AuthStatusEnum.SUCCESS.getValue());
        Long lastMonthCount = merchantDao.selectDateActiveMch(lastMonth);
        map.put("thisMonthTotalCount",thisMonthCount);
        map.put("lastMonthTotalCount",lastMonthCount);
    }

}
