package com.zhixianghui.service.merchant.facade.record;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import com.zhixianghui.facade.merchant.vo.MerchantPositionVo;
import com.zhixianghui.facade.merchant.vo.record.MerchantAccountVo;
import com.zhixianghui.facade.merchant.vo.record.MerchantBaseInfoVo;
import com.zhixianghui.facade.merchant.vo.record.RecordMerchantPositionVo;
import com.zhixianghui.service.merchant.core.biz.DictionaryBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantInfoChangeRecordBiz;
import com.zhixianghui.service.merchant.core.vo.MerchantInfoChangeRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/7/21 9:18
 */
@Slf4j
@Service("MerchantInfoChange")
public class MerchantInfoChange extends AbstractInfoChange<MerchantBaseInfoVo> {
    private static final String DICTIONARY_NAME = "MerchantInfoChange";
    private static final String WORK_DICTIONARY_NAME = "WorkPlaceEnum";
    private static final String CHANNEL_TYPE_FIELD = "channelType";
    private static final String CHANNEL_TYPE_NAME = "通道类型";
    private static final String PAY_CHANNEL_NAME_FIELD = "payChannelName";
    private static final String PAY_CHANNEL_NAME = "支付通道名称";
    private static final String PAY_CHANNEL_NO_FIELD = "payChannelNo";
    private static final String PAY_CHANNEL_NO_NAME = "支付通道编号";
    private static final String MAINSTAY_FIELD = "代征主体";
    private static DataDictionary dataDictionary;
    private static Map<String, String> description;

    @Autowired
    private DictionaryBiz dictionaryBiz;
    @Autowired
    private MerchantInfoChangeRecordBiz changeRecordBiz;

    public void handle(MerchantBaseInfoVo newInfo, MerchantBaseInfoVo oldInfo, int source) {
        logInfoAndCheck(newInfo, oldInfo, source, newInfo.getFlowId());
        Map<String, Object> oldMap = reflection(oldInfo);
        Map<String, Object> newMap = reflection(newInfo);
        compareAndRecord(oldMap, newMap, newInfo, source);

    }

    @Override
    protected void handleException() {

    }

    @PostConstruct
    @Override
    protected void getDictionary() {
        dataDictionary = dictionaryBiz.getDataDictionaryByName(DICTIONARY_NAME);
        description = dataDictionary.getItemList().stream().collect(Collectors.toMap(DataDictionary.Item::getCode, DataDictionary.Item::getDesc));
        dataDictionary = dictionaryBiz.getDataDictionaryByName(WORK_DICTIONARY_NAME);
        description.putAll(dataDictionary.getItemList().stream().collect(Collectors.toMap(DataDictionary.Item::getCode, DataDictionary.Item::getDesc)));

    }

    @Override
    protected void specialHandling(String name, Map<String, Object> infoMap) {
        if (!SPECIAL_FIELD.containsKey(name) || StringUtils.isBlank((String) infoMap.get(name))) {
            return;
        }
        infoMap.put(name, SPECIAL_FIELD.get(name).apply(Integer.valueOf((String) infoMap.get(name))));
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void compareAndRecord(Map<String, Object> oldMap, Map<String, Object> newMap, MerchantBaseInfoVo newInfo, int source) {
        List<MerchantInfoChangeRecord> changeList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : oldMap.entrySet()) {
            if (!newMap.containsKey(entry.getKey())) {
                log.warn("商户信息属性发生变化 : {}", entry.getKey());
                continue;
            }
            if (newMap.get(entry.getKey()) instanceof String) {
                MerchantInfoChangeRecord record = handleString(description, entry, newMap, newInfo, source, newInfo.getMchNo());
                if (record == null) {
                    continue;
                }
                changeList.add(record);
            }
            if (newMap.get(entry.getKey()) instanceof List) {
                List<?> result = (List<?>) newMap.get(entry.getKey());
                if (CollectionUtils.isEmpty(result) && CollectionUtils.isEmpty((Collection<?>) entry.getValue())) {
                    continue;
                }
                Object o = !CollectionUtils.isEmpty(result) ? result.get(0) : ((List<?>) entry.getValue()).get(0);
                if (o instanceof String) {
                    List<String> odlList = JSON.parseArray(JSON.toJSONString(entry.getValue()), String.class);
                    List<String> newList = JSON.parseArray(JSON.toJSONString(newMap.get(entry.getKey())), String.class);
                    changeList.addAll(handleStringList(odlList, newList, entry, description, newInfo, newInfo.getMchNo(), source));
                }
                if (o instanceof MerchantPositionVo) {
                    List<MerchantPositionVo> odlList = JSON.parseArray(JSON.toJSONString(entry.getValue()), MerchantPositionVo.class);
                    List<MerchantPositionVo> newList = JSON.parseArray(JSON.toJSONString(newMap.get(entry.getKey())), MerchantPositionVo.class);
                    changeList.addAll(handlePositionInfo(odlList, newList, description, newInfo, source));
                }
            }
            if (newMap.get(entry.getKey()) instanceof Map) {
                try {
                    Map<String, List<Map<String,Object>>> oldAccountInfo = (Map<String, List<Map<String, Object>>>) entry.getValue();
                    Map<String, List<Map<String,Object>>> newAccountInfo = (Map<String, List<Map<String,Object>>>) newMap.get(entry.getKey());
                    changeList.addAll(handleAccountInfo(oldAccountInfo, newAccountInfo, entry, description, newInfo, source));
                } catch (Exception e) {
                    log.error("非账号信息对应的数据格式 : {}-{}", JSONObject.toJSON(entry.getValue()), JSONObject.toJSON(newMap.get(entry.getKey())), e);
                }
            }

        }
        log.info(JSONObject.toJSONString(changeList));
        if (CollectionUtils.isEmpty(changeList)) {
            return;
        }
        changeRecordBiz.insert(changeList);
    }

    private List<MerchantInfoChangeRecord> handleAccountInfo(Map<String, List<Map<String,Object>>> oldAccountInfo, Map<String, List<Map<String,Object>>> newAccountInfo, Map.Entry<String, Object> entry, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        MerchantAccountVo oldData = MerchantAccountVo.newInstance();
        getAccountInfo(oldData, oldAccountInfo);
        MerchantAccountVo newData = MerchantAccountVo.newInstance();
        getAccountInfo(newData, newAccountInfo);

        List<MerchantInfoChangeRecord> changeList = new ArrayList<>();
        handleChannelType(oldData, newData, changeList, newInfo, source);
        handleChannelName(oldData, newData, changeList, newInfo, source);
        handleChannelNo(oldData, newData, changeList, newInfo, source);

        return changeList;
    }

    public static void main(String[] args) {
        System.out.println(String.join("2", "代征主体:", "支付通道编号"));
    }
    private void handleChannelNo(MerchantAccountVo oldData, MerchantAccountVo newData, List<MerchantInfoChangeRecord> changeList, MerchantBaseInfoVo newInfo, int source) {
        if (CollectionUtils.isEmpty(oldData.getPayChannelNo()) && CollectionUtils.isEmpty(newData.getPayChannelNo())) {
            return;
        }
        Map<String, String> channelNoDescribe = new HashMap<String, String>();
        Map.Entry<String, Object> entry = MerchantAccountVo.newInstance().newEntry("payChannelNo", null);
        if (CollectionUtils.isEmpty(oldData.getPayChannelNo()) && !CollectionUtils.isEmpty(newData.getPayChannelNo())) {
            newData.getPayChannelNo().forEach((k, v) -> {
                channelNoDescribe.put(PAY_CHANNEL_NO_FIELD, String.join(k, MAINSTAY_FIELD, PAY_CHANNEL_NO_NAME));
                changeList.addAll(handleStringList(
                        new ArrayList<>(), v, entry, channelNoDescribe, newInfo, newInfo.getMchNo(), source
                ));
            });
            return;
        }
        for (Map.Entry<String, List<String>> map : oldData.getPayChannelNo().entrySet()) {
            channelNoDescribe.put(PAY_CHANNEL_NO_FIELD, String.join(map.getKey(), MAINSTAY_FIELD, PAY_CHANNEL_NO_NAME));
            if (!newData.getPayChannelNo().containsKey(map.getKey())) {
                changeList.addAll(handleStringList(
                        map.getValue(), new ArrayList<>(), entry, channelNoDescribe, newInfo, newInfo.getMchNo(), source
                ));
                continue;
            }
            changeList.addAll(handleStringList(
                    map.getValue(), newData.getPayChannelNo().get(map.getKey()), entry, channelNoDescribe, newInfo, newInfo.getMchNo(), source
            ));
            newData.getPayChannelNo().remove(map.getKey());
        }
        if (!CollectionUtils.isEmpty(newData.getPayChannelNo())) {
            newData.getPayChannelNo().forEach((k, v) -> {
                channelNoDescribe.put(PAY_CHANNEL_NO_FIELD, String.join(k, MAINSTAY_FIELD, PAY_CHANNEL_NO_NAME));
                changeList.addAll(handleStringList(new ArrayList<>(), v, entry, channelNoDescribe, newInfo, newInfo.getMchNo(), source));
            });
        }
    }

    private void handleChannelName(MerchantAccountVo oldData, MerchantAccountVo newData, List<MerchantInfoChangeRecord> changeList, MerchantBaseInfoVo newInfo, int source) {
        if (CollectionUtils.isEmpty(oldData.getPayChannelName()) && CollectionUtils.isEmpty(newData.getPayChannelName())) {
            return;
        }
        Map<String, String> channelNameDescribe = new HashMap<String, String>();
        Map.Entry<String, Object> entry = MerchantAccountVo.newInstance().newEntry("payChannelName", null);
        if (CollectionUtils.isEmpty(oldData.getPayChannelName()) && !CollectionUtils.isEmpty(newData.getPayChannelName())) {
            newData.getPayChannelName().forEach((k, v) -> {
                channelNameDescribe.put(PAY_CHANNEL_NAME_FIELD,  String.join(k, MAINSTAY_FIELD, PAY_CHANNEL_NAME));
                changeList.addAll(handleStringList(new ArrayList<>(), v, entry, channelNameDescribe, newInfo, newInfo.getMchNo(), source));
            });
            return;
        }
        for (Map.Entry<String, List<String>> map : oldData.getPayChannelName().entrySet()) {
            channelNameDescribe.put(PAY_CHANNEL_NAME_FIELD,  String.join(map.getKey(), MAINSTAY_FIELD, PAY_CHANNEL_NAME));
            if (!newData.getPayChannelName().containsKey(map.getKey())) {
                changeList.addAll(handleStringList(
                        map.getValue(), new ArrayList<>(), entry, channelNameDescribe, newInfo, newInfo.getMchNo(), source
                ));
                continue;
            }
            log.info("支付通道名称：{}-{}",  map.getValue(), newData.getPayChannelName().get(map.getKey()));
            changeList.addAll(handleStringList(map.getValue(), newData.getPayChannelName().get(map.getKey()), entry, channelNameDescribe, newInfo, newInfo.getMchNo(), source));
            newData.getPayChannelName().remove(map.getKey());
        }
        if (!CollectionUtils.isEmpty(newData.getPayChannelName())) {
            newData.getPayChannelName().forEach((k, v) -> {
                channelNameDescribe.put(PAY_CHANNEL_NAME_FIELD, String.join(k, MAINSTAY_FIELD, PAY_CHANNEL_NAME));
                changeList.addAll(handleStringList(new ArrayList<>(), v, entry, channelNameDescribe, newInfo, newInfo.getMchNo(), source));
            });
        }
    }

    private void handleChannelType(MerchantAccountVo oldData, MerchantAccountVo newData, List<MerchantInfoChangeRecord> changeList, MerchantBaseInfoVo newInfo, int source) {
        if (CollectionUtils.isEmpty(oldData.getChannelType()) && CollectionUtils.isEmpty(newData.getChannelType())) {
            return;
        }
        Map<String, String> channelTypeDescribe = new HashMap<String, String>();
        Map.Entry<String, Object> entry = MerchantAccountVo.newInstance().newEntry("channelType", null);
        if (CollectionUtils.isEmpty(oldData.getChannelType()) && !CollectionUtils.isEmpty(newData.getChannelType())) {
            newData.getPayChannelNo().forEach((k, v) -> {
                channelTypeDescribe.put(CHANNEL_TYPE_FIELD, String.join(k, MAINSTAY_FIELD, CHANNEL_TYPE_NAME));
                changeList.addAll(handleStringList(new ArrayList<>(), v, entry, channelTypeDescribe, newInfo, newInfo.getMchNo(), source));
            });
            return;
        }
        for (Map.Entry<String, List<String>> map : oldData.getChannelType().entrySet()) {
            channelTypeDescribe.put(CHANNEL_TYPE_FIELD, String.join(map.getKey(), MAINSTAY_FIELD, CHANNEL_TYPE_NAME));
            if (!newData.getChannelType().containsKey(map.getKey())) {
                changeList.addAll(handleStringList(map.getValue(), new ArrayList<>(), entry, channelTypeDescribe, newInfo, newInfo.getMchNo(), source));
                continue;
            }
            changeList.addAll(handleStringList(
                    map.getValue(), newData.getChannelType().get(map.getKey()), entry, channelTypeDescribe, newInfo, newInfo.getMchNo(), source
            ));
            newData.getChannelType().remove(map.getKey());
        }
        if (!CollectionUtils.isEmpty(newData.getChannelType())) {
            newData.getChannelType().forEach((k, v) -> {
                channelTypeDescribe.put(CHANNEL_TYPE_FIELD, String.join(k, MAINSTAY_FIELD, CHANNEL_TYPE_NAME));
                changeList.addAll(handleStringList(new ArrayList<>(), v, entry, channelTypeDescribe, newInfo, newInfo.getMchNo(), source));
            });
        }
    }

    private void getAccountInfo(MerchantAccountVo data, Map<String, List<Map<String,Object>>> accountInfo) {
        if (CollectionUtils.isEmpty(accountInfo)) {
            return;
        }
        for (Map.Entry<String, List<Map<String,Object>>> map : accountInfo.entrySet()) {
            List<EmployerAccountInfo> result = JSON.parseArray(JSON.toJSONString(map.getValue()), EmployerAccountInfo.class);
            for (EmployerAccountInfo info : result) {
                if (!data.getChannelType().containsKey(map.getKey())) {
                    data.getChannelType().put(map.getKey(), new ArrayList<>());
                }
                if (!data.getPayChannelName().containsKey(map.getKey())) {
                    data.getPayChannelName().put(map.getKey(), new ArrayList<>());
                }
                if (!data.getPayChannelNo().containsKey(map.getKey())) {
                    data.getPayChannelNo().put(map.getKey(), new ArrayList<>());
                }
                data.getChannelType().get(map.getKey()).add(ChannelTypeEnum.getDesc(info.getChannelType()));
                data.getPayChannelName().get(map.getKey()).add(StringUtils.isBlank(info.getPayChannelName()) ? info.getChannelName() : info.getPayChannelName());
                data.getPayChannelNo().get(map.getKey()).add(info.getPayChannelNo());
            }
        }
        log.info("渠道类型名称: {}", JSONObject.toJSON(data.getChannelType()));
        log.info("支付渠道名称: {}", JSONObject.toJSON(data.getPayChannelName()));
        log.info("支付渠道编号: {}", JSONObject.toJSON(data.getPayChannelNo()));
    }


    private List<MerchantInfoChangeRecord> handlePositionInfo(
            List<MerchantPositionVo> odlList,
            List<MerchantPositionVo> newList,
            Map<String, String> description,
            MerchantBaseInfoVo newInfo,
            int source
    ) {
        List<MerchantInfoChangeRecord> changeList = new ArrayList<>();
        RecordMerchantPositionVo oldInfo = RecordMerchantPositionVo.newInstance();
        if (CollectionUtils.isEmpty(odlList)) {
            handleAdd(oldInfo, newList, changeList, description, newInfo, source);
            return changeList;
        }
        if (CollectionUtils.isEmpty(newList)) {
            initOldInfo(oldInfo, odlList, description);
            handleDelete(oldInfo, changeList, description, newInfo, source);
            return changeList;
        }
        initOldInfo(oldInfo, odlList, description);
        handleAdd(oldInfo, newList, changeList, description, newInfo, source);
        handleDelete(oldInfo, changeList, description, newInfo, source);
        return changeList;
    }

    private void initOldInfo(RecordMerchantPositionVo oldInfo, List<MerchantPositionVo> odlList, Map<String, String> description) {
        for (MerchantPositionVo merchantPositionVo : odlList) {
            oldInfo.getWorkCategoryName().add(merchantPositionVo.getWorkCategoryName());
            oldInfo.getServiceDesc().add(merchantPositionVo.getServiceDesc());
            oldInfo.getChargeRuleDesc().add(merchantPositionVo.getChargeRuleDesc());
            oldInfo.getWorkplaceCode().add(description.get(merchantPositionVo.getWorkplaceCode()));
            if (CollectionUtils.isEmpty(merchantPositionVo.getInvoiceCategoryList())) {
                continue;
            }
            for (InvoiceCategoryVo invoiceCategoryVo : merchantPositionVo.getInvoiceCategoryList()) {
                oldInfo.getInvoiceCategoryName().add(invoiceCategoryVo.getInvoiceCategoryName());
            }
        }
    }

    private void handleAdd(RecordMerchantPositionVo oldInfo, List<MerchantPositionVo> newList, List<MerchantInfoChangeRecord> changeList, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        for (MerchantPositionVo merchantPositionVo : newList) {
            workCategoryNameConvert(oldInfo, merchantPositionVo.getWorkCategoryName(), changeList, "workCategoryName", description, newInfo, source);
            serviceDescConvert(oldInfo, merchantPositionVo.getServiceDesc(), changeList, "serviceDesc", description, newInfo, source);
            chargeRuleDescConvert(oldInfo, merchantPositionVo.getChargeRuleDesc(), changeList, "chargeRuleDesc", description, newInfo, source);
            workPlaceCodeConvert(oldInfo, merchantPositionVo.getWorkplaceCode(), changeList, "workPlaceCode", description, newInfo, source);
            if (CollectionUtils.isEmpty(merchantPositionVo.getInvoiceCategoryList())) {
                continue;
            }
            for (InvoiceCategoryVo invoiceCategoryVo : merchantPositionVo.getInvoiceCategoryList()) {
                invoiceNameConvert(oldInfo, invoiceCategoryVo.getInvoiceCategoryName(), changeList, "invoiceCategoryName", description, newInfo, source);
            }
        }
    }

    private void handleDelete(RecordMerchantPositionVo oldInfo, List<MerchantInfoChangeRecord> changeList, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        if (!CollectionUtils.isEmpty(oldInfo.getChargeRuleDesc())) {
            delete(oldInfo.getChargeRuleDesc(), changeList, "chargeRuleDesc", description, newInfo, source);
        }
        if (!CollectionUtils.isEmpty(oldInfo.getInvoiceCategoryName())) {
            delete(oldInfo.getInvoiceCategoryName(), changeList, "invoiceCategoryName", description, newInfo, source);
        }
        if (!CollectionUtils.isEmpty(oldInfo.getServiceDesc())) {
            delete(oldInfo.getServiceDesc(), changeList, "serviceDesc", description, newInfo, source);
        }
        if (!CollectionUtils.isEmpty(oldInfo.getWorkCategoryName())) {
            delete(oldInfo.getWorkCategoryName(), changeList, "workCategoryName", description, newInfo, source);
        }
        if (!CollectionUtils.isEmpty(oldInfo.getWorkplaceCode())) {
            delete(oldInfo.getWorkplaceCode(), changeList, "workPlaceCode", description, newInfo, source);
        }
    }

    private void delete(List<String> info, List<MerchantInfoChangeRecord> changeList, String attributeName, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        for (String s : info) {
            changeList.add(new MerchantInfoChangeRecordVo().build(
                    attributeName, description.get(attributeName), OperationEnum.DELETE.getOperation(), s, newInfo, source, newInfo.getMchNo()
            ));
        }

    }

    private void invoiceNameConvert(RecordMerchantPositionVo oldInfo, String invoiceCategoryName, List<MerchantInfoChangeRecord> changeList, String attributeName, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        if (oldInfo.getInvoiceCategoryName().contains(invoiceCategoryName)) {
            oldInfo.getInvoiceCategoryName().remove(invoiceCategoryName);
            return;
        }
        changeList.add(new MerchantInfoChangeRecordVo().build(
                attributeName, description.get(attributeName), OperationEnum.ADD.getOperation(), invoiceCategoryName, newInfo, source, newInfo.getMchNo()
        ));
    }

    private void chargeRuleDescConvert(RecordMerchantPositionVo oldInfo, String chargeRuleDesc, List<MerchantInfoChangeRecord> changeList, String attributeName, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        if (oldInfo.getChargeRuleDesc().contains(chargeRuleDesc)) {
            oldInfo.getChargeRuleDesc().remove(chargeRuleDesc);
            return;
        }
        changeList.add(new MerchantInfoChangeRecordVo().build(
                attributeName, description.get(attributeName), OperationEnum.ADD.getOperation(), chargeRuleDesc, newInfo, source, newInfo.getMchNo()
        ));
    }

    private void serviceDescConvert(RecordMerchantPositionVo oldInfo, String serviceDesc, List<MerchantInfoChangeRecord> changeList, String attributeName, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        if (oldInfo.getServiceDesc().contains(serviceDesc)) {
            oldInfo.getServiceDesc().remove(serviceDesc);
            return;
        }
        changeList.add(new MerchantInfoChangeRecordVo().build(
                attributeName, description.get(attributeName), OperationEnum.ADD.getOperation(), serviceDesc, newInfo, source, newInfo.getMchNo()
        ));
    }

    private void workCategoryNameConvert(RecordMerchantPositionVo oldInfo, String workplaceName, List<MerchantInfoChangeRecord> changeList, String attributeName, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        if (oldInfo.getWorkCategoryName().contains(workplaceName)) {
            oldInfo.getWorkCategoryName().remove(workplaceName);
            return;
        }
        changeList.add(new MerchantInfoChangeRecordVo().build(
                attributeName, description.get(attributeName), OperationEnum.ADD.getOperation(), workplaceName, newInfo, source, newInfo.getMchNo()
        ));
    }

    private void workPlaceCodeConvert(RecordMerchantPositionVo oldInfo, String workPlaceCode, List<MerchantInfoChangeRecord> changeList, String attributeName, Map<String, String> description, MerchantBaseInfoVo newInfo, int source) {
        if (oldInfo.getWorkplaceCode().contains(description.get(workPlaceCode))) {
            oldInfo.getWorkplaceCode().remove(description.get(workPlaceCode));
            return;
        }
        changeList.add(new MerchantInfoChangeRecordVo().build(
                attributeName, description.get(attributeName), OperationEnum.ADD.getOperation(), description.get(workPlaceCode), newInfo, source, newInfo.getMchNo()
        ));
    }
}
