package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerPositionDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报价单
 *
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
@Slf4j
public class MerchantEmployerPositionBiz {

    @Autowired
    private MerchantEmployerPositionDao positionDao;

    public List<MerchantEmployerPosition> listByMchNo(String mchNo) {
        Assert.hasText(mchNo, "商户编号不能为空");
        return positionDao.listByMchNo(mchNo);
    }

    public MerchantEmployerPosition getByMchNoAndWorkCategoryCode(String mchNo, String workCategoryCode) {
        LimitUtil.notEmpty(mchNo, "商户编号不能为空");
        LimitUtil.notEmpty(workCategoryCode, "服务类别编码不能为空");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);
        paramMap.put("workCategoryCode", workCategoryCode);
        List<MerchantEmployerPosition> positionList = positionDao.listBy(paramMap);
        return CollectionUtils.isEmpty(positionList) ? null : positionList.get(0);
    }

    public List<MerchantEmployerPosition> listByMchNoWithQuote(Map<String,Object> paramMap) {
        return positionDao.listByMchNoWithQuote(paramMap);
    }

    public List<MerchantEmployerPosition> listByMchNoWithQuoteWithoutGroup(Map<String, Object> paramMap) {
        return positionDao.listByMchNoWithQuoteWithoutGroup(paramMap);
    }
}
