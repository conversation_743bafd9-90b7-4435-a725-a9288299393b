package com.zhixianghui.service.merchant.core.biz;


import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.service.merchant.core.dao.mapper.MerchantCkhQuoteMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2022-06-24
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantCkhQuoteBiz {

    private final MerchantCkhQuoteMapper merchantCkhQuoteMapper;

    public MerchantCkhQuote getFeeRate(String employerNo, String mainstayNo, String productNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", employerNo);
        paramMap.put("mainstayMchNo", mainstayNo);
        paramMap.put("productNo", productNo);
        paramMap.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        return merchantCkhQuoteMapper.getFeeRate(paramMap);
    }

    public Integer getBalancedMode(MerchantEmployerQuote merchantEmployerQuote) {
        return merchantCkhQuoteMapper.getBalancedMode(merchantEmployerQuote);
    }
}