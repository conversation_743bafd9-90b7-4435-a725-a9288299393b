package com.zhixianghui.service.merchant.core.biz;


import com.zhixianghui.facade.merchant.entity.MerchantChannel;
import com.zhixianghui.service.merchant.core.dao.MerchantChannelDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
* 商户报备通道表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-04-20
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantChannelBiz {

    private final MerchantChannelDao merchantchannelDao;

    public MerchantChannel getByMchNo(String mchNo) {
        List<MerchantChannel> list = merchantchannelDao.listBy(new HashMap<String, Object>() {{
            put("mchNo", mchNo);
        }});
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public List<MerchantChannel> listByMcnNo(String mchNo) {
        return merchantchannelDao.listBy(new HashMap<String, Object>() {{
            put("mchNo", mchNo);
        }});
    }

    public List<String> listAll() {
        return merchantchannelDao.listAll().stream().map(MerchantChannel::getMchNo).distinct().collect(Collectors.toList());
    }
}