package com.zhixianghui.service.merchant.facade.employer;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.vo.EmployerCooperateUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerFullInfoUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.service.merchant.core.biz.user.portal.EmployerStaffBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class EmployerStaffFacadeImpl implements EmployerStaffFacade {
	
	@Autowired
	private EmployerStaffBiz employerStaffBiz;

	/**
	 * 根据id查询用工企业员工
	 *
	 * @param mchNo 商户编号
	 * @param id    员工id
	 */
	@Override
	public EmployerStaffVO getById(String mchNo, long id) {
		return employerStaffBiz.getById(mchNo, id);
	}

	/**
	 * 根据手机号查询用工企业员工
	 *
	 * @param mchNo 商户编号
	 * @param phone 手机号
	 */
	@Override
	public EmployerStaffVO getByPhone(String mchNo, String phone) {
		return employerStaffBiz.getByPhone(mchNo, phone);
	}

	/**
	 * 根据操作员id查询其关联的员工
	 *
	 * @param id
	 * @return
	 */
	@Override
	public List<EmployerStaffVO> listByOperatorId(long id) {
		return employerStaffBiz.listByOperatorId(id);
	}

	/**
	 * 根据手机号查询其关联的员工
	 *
	 * @param phone
	 * @return
	 */
	@Override
	public List<EmployerStaffVO> listByPhone(String phone) {
		return employerStaffBiz.listByPhone(phone);
	}

	/**
	 * 获取超级管理员
	 *
	 * @param mchNo 商户编号
	 * @return
	 */
	@Override
	public EmployerStaffVO getAdmin(String mchNo) throws BizException {
		return employerStaffBiz.getAdmin(mchNo);
	}

	/**
	 * 创建用工企业员工
	 *
	 * @param employerStaffVo vo
	 */
	@Override
	public long create(EmployerStaffVO employerStaffVo) throws BizException {
		return employerStaffBiz.create(employerStaffVo);
	}

	/**
	 * 创建用工企业员工并分配指定角色
	 *
	 * @param employerStaffVo
	 * @param roleIds
	 */
	@Override
	public long createAndAssignRole(EmployerStaffVO employerStaffVo, List<Long> roleIds) throws BizException {
		return employerStaffBiz.createAndAssignRole(employerStaffVo, roleIds);
	}

	/**
	 * 更换超级管理员
	 *
	 * @param mchNo         	商户编号
	 * @param newAdminPhone 	新负责人手机号
	 * @param newAdminName      新负责人姓名
	 */
	@Override
	public void changeAdmin(String mchNo, String newAdminPhone, String newAdminName, String updator) throws BizException {
		employerStaffBiz.changeAdmin(mchNo, newAdminPhone, newAdminName, updator);
	}

	/**
	 * 为用工企业员工更新角色
	 * @param mchNo     商户编号
	 * @param staffId   员工id
	 * @param roleIds   角色id
	 */
	@Override
	public void updateRole(String mchNo, long staffId, List<Long> roleIds,String name) throws BizException {
		employerStaffBiz.updateRole(mchNo, staffId, roleIds,name);
	}

	/**
	 * 根据id删除用工企业员工
	 *
	 * @param mchNo 商户编号
	 * @param id    员工id
	 */
	@Override
	public void deleteById(String mchNo, long id) {
		employerStaffBiz.deleteById(mchNo, id);
	}

	/**
	 * 根据员工id获取其关联的角色
	 *
	 * @param mchNo   商户编号
	 * @param staffId 员工id
	 */
	@Override
	public List<EmployerRole> getRoleByStaffId(String mchNo, long staffId) throws BizException {
		return employerStaffBiz.getRoleByStaffId(mchNo, staffId);
	}

	/**
	 * 查询员工所关联的功能
	 *
	 * @param mchNo   商户编号
	 * @param staffId 员工id
	 */
	@Override
	public List<EmployerFunction> listFunctionByStaffId(String mchNo, long staffId) throws BizException {
		return employerStaffBiz.listFunctionByStaffId(mchNo, staffId);
	}

	/**
	 * 分页查询员工
	 *
	 * @param paramMap
	 * @param pageParam
	 */
	@Override
	public PageResult<List<EmployerStaffVO>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
		return employerStaffBiz.listPage(paramMap, pageParam);
	}

	/**
	 * 分页查询员工
	 *
	 * @param mchNo
	 * @param paramMap
	 * @param pageParam
	 * @return
	 */
	@Override
	public PageResult<List<EmployerStaffVO>> listPage(String mchNo, Map<String, Object> paramMap, PageParam pageParam) {
		return employerStaffBiz.listPage(mchNo, paramMap, pageParam);
	}

	@Override
	public void updateByMchNo(EmployerCooperateUpdateVo vo) {
		employerStaffBiz.updateByMchNo(vo);
	}

	@Override
	public Long countEmployerCount(Long roleId) {
		return employerStaffBiz.countEmployerCount(roleId);
	}

	@Override
	public boolean isAdmin(EmployerStaffVO employerStaffVO) {
		return employerStaffBiz.isAdmin(employerStaffVO);
	}

	@Override
	public List<EmployerFunction> listAllFunction() {
		return employerStaffBiz.listAllFunction();

	}

	@Override
	public void getAndPutStaffCache(Long id) {
		employerStaffBiz.getAndPutStaffCache(id);
	}

	@Override
	public List<String> getDistinctStaffByRoleIdAndMchNo(String mchNo, List<Long> roleIds) {
		return employerStaffBiz.getDistinctStaffByRoleIdAndMchNo(mchNo,roleIds);
	}
}
