package com.zhixianghui.service.merchant.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.vo.AgreementDto;
import com.zhixianghui.service.merchant.core.biz.AgreementBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AgreementSignListener
 * @Description TODO
 * @Date 2022/8/17 18:25
 */
@Component
public class AgreementSignListener {

    @Autowired
    private AgreementBiz agreementBiz;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_SIGN_NOTIFY,selectorExpression = MessageMsgDest.TAG_SIGN_NOTIFY,consumeThreadMax = 5,consumerGroup = "agreementSignNotifyGroup")
    public class AgreementNotifyListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            Map<String,Object> paramMap = JsonUtil.toBean(jsonParam,Map.class);
            agreementBiz.handleNotify(paramMap);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGREEMENT_SIGN_ASYNC, selectorExpression = MessageMsgDest.TAG_AGREEMENT_SIGN_ASYNC, consumeThreadMax = 3, consumerGroup = "agreementCreateGroup")
    public class AgreementCreateListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            AgreementDto agreementDto = JsonUtil.toBean(jsonParam,AgreementDto.class);
            agreementBiz.signAsync(agreementDto);
        }
    }
}
