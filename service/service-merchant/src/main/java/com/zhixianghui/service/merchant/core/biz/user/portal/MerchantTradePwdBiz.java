package com.zhixianghui.service.merchant.core.biz.user.portal;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.portal.MerchantTradePwd;
import com.zhixianghui.service.merchant.core.dao.MerchantTradePwdDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class MerchantTradePwdBiz{
	
	@Autowired
	private MerchantTradePwdDao merchantTradePwdDao;

	/**
	 * 根据商户编号查询
	 *
	 * @param mchNo
	 */
	public MerchantTradePwd getByMchNo(String mchNo) {
		return merchantTradePwdDao.getByMchNo(mchNo);
	}

	/**
	 * 创建
	 *
	 * @param tradePwd
	 */
	public void create(MerchantTradePwd tradePwd) throws BizException {
		if (tradePwd.getCreateTime() == null) {
			tradePwd.setCreateTime(new Date());
		}
		try {
			merchantTradePwdDao.insert(tradePwd);
		} catch (DuplicateKeyException e) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付密码已存在");
		}
	}

	/**
	 * 更新
	 *
	 * @param tradePwd
	 */
	public void update(MerchantTradePwd tradePwd) {
		merchantTradePwdDao.update(tradePwd);
	}
}
