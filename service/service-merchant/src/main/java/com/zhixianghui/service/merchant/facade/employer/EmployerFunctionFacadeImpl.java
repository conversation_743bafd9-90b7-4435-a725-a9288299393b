package com.zhixianghui.service.merchant.facade.employer;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.service.employer.EmployerFunctionFacade;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.service.merchant.core.biz.user.portal.EmployerFunctionBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class EmployerFunctionFacadeImpl implements EmployerFunctionFacade {
	
	@Autowired
	private EmployerFunctionBiz employerFunctionBiz;

	/**
	 * 创建用工企业后台功能
	 *
	 * @param function 功能
	 */
	@Override
	public void create(EmployerFunction function) throws BizException {
		employerFunctionBiz.create(function);
	}

	/**
	 * 根据id查询用工企业后台功能
	 *
	 * @param id
	 */
	@Override
	public EmployerFunction getById(long id){
		return employerFunctionBiz.getById(id);
	}

	/**
	 * 编辑用工企业后台功能
	 *
	 * @param function 功能
	 */
	@Override
	public void update(EmployerFunction function) throws BizException {
		employerFunctionBiz.update(function);
	}

	/**
	 * 根据id删除功能，并删除该功能与角色的映射
	 *
	 * @param id
	 */
	@Override
	public void deleteById(long id) throws BizException {
		employerFunctionBiz.deleteById(id);
	}

	/**
	 * 查询所有功能
	 */
	@Override
	public List<EmployerFunction> listAll() {
		return employerFunctionBiz.listAll();
	}

	@Override
	public List<EmployerFunction> listAll(Map<String, Object> param, PageParam pageParam) {
		return employerFunctionBiz.listAll(param, pageParam);
	}

	@Override
	public void export(FunctionVO employerFunction) {
		employerFunctionBiz.export(employerFunction);
	}

	@Override
	public PageResult<List<EmployerFunction>> listPage(Map<String, Object> param, PageParam pageParam) {
		return employerFunctionBiz.listPage(param, pageParam);
	}

	@Override
	public void saveFunction(List<EmployerFunction> list) {
		employerFunctionBiz.saveFunction(list);
	}

	@Override
	public String getPermissionFlag(Long parentId) {
		return employerFunctionBiz.getPermissionFlag(parentId);
	}
}
