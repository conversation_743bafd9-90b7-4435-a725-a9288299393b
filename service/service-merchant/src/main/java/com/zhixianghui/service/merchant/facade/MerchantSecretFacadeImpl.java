package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description:
 * @author: xingguang li
 * @created: 2020/12/18 14:44
 */
@Service
public class MerchantSecretFacadeImpl implements MerchantSecretFacade {

    @Autowired
    private MerchantBiz merchantBiz;

    @Override
    public void insert(MerchantSecret merchantSecret) {
        merchantBiz.insertSercet(merchantSecret);
    }

    @Override
    public void update(MerchantSecret merchantSecret) {
        merchantBiz.updateSecret(merchantSecret);
    }

    @Override
    public MerchantSecret getByMchNo(String merchantNo) {
        return merchantBiz.getSercretByMchNo(merchantNo);
    }
}
