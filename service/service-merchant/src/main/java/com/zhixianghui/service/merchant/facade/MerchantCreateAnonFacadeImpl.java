package com.zhixianghui.service.merchant.facade;

import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantCreateAnonFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.CreateLinkReqVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerAddVo;
import com.zhixianghui.service.merchant.core.biz.AgentBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerBiz;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.apache.commons.codec.CharEncoding.UTF_8;

/**
 * <AUTHOR>
 * @ClassName MerchantCreateAnonFacade
 * @Description TODO
 * @Date 2023/4/17 15:31
 */
@Service
public class MerchantCreateAnonFacadeImpl implements MerchantCreateAnonFacade {

    @Autowired
    private AgentBiz agentBiz;

    @Reference
    private DataDictionaryFacade dictionaryFacade;

    @Autowired
    PmsOperatorFacade pmsOperatorFacade;

    @Autowired
    private MerchantEmployerBiz merchantEmployerBiz;

    @Autowired
    private MerchantBiz merchantBiz;


    private static final String SECRET = "m26K2Ygw9qoU2x0z";

    private static final String MAINSTAY_SALER_DICT_NAME = "MainstaySalerMap";

    private static final String DICT_NAME = "H5_URL";

    @Override
    public String getURL(CreateLinkReqVo reqVo) {
        if (StringUtils.isNotBlank(reqVo.getAgentNo())){
            Agent agent = agentBiz.getByAgentNo(reqVo.getAgentNo());
            if(agent == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人不存在");
            }
        }

        if(reqVo.getSalerId() != null){
            DataDictionary dataDictionary = dictionaryFacade.getDataDictionaryByName(MAINSTAY_SALER_DICT_NAME);
            Map<String,String> map = new HashMap<>();
            if (dataDictionary != null){
                map = dataDictionary.getItemList().stream().collect(Collectors.toMap(DataDictionary.Item::getFlag,DataDictionary.Item::getCode));
            }
            reqVo.setMainstayNo(map.getOrDefault(Long.toString(reqVo.getSalerId()),""));
        }

        String json = JsonUtil.toString(reqVo);
        String param;
        try {
            param = URLEncoder.encode(AESUtil.encryptECB(json,SECRET), UTF_8);
        } catch (UnsupportedEncodingException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("生成链接失败");
        }

        //读字典获取文件前缀
        String urlPre = dictionaryFacade.getSystemConfig(DICT_NAME);
        //拼接url
        String result = urlPre + "?p=" +param;
        return result;
    }

    @Override
    public void applyMerchant(String p,MerchantEmployerAddVo merchantEmployerAddVo) {
        //解密
        String param = AESUtil.decryptECB(p,SECRET);
        CreateLinkReqVo createLinkReqVo = JSONUtil.toBean(param,CreateLinkReqVo.class);

        //获取销售、合伙人信息
        PmsOperator pmsOperator = pmsOperatorFacade.getOperatorById(createLinkReqVo.getSalerId());
        if (pmsOperator == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户不存在");
        }
        merchantEmployerAddVo.setPmsOperator(pmsOperator);
        merchantEmployerAddVo.setSalerId(pmsOperator.getId());
        merchantEmployerAddVo.setSalerName(pmsOperator.getRealName());

        if (StringUtils.isNotBlank(createLinkReqVo.getAgentNo())){
            Agent agent = agentBiz.getByAgentNo(createLinkReqVo.getAgentNo());
            if (agent == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该用户不存在");
            }
            merchantEmployerAddVo.setAgentNo(agent.getAgentNo());
            merchantEmployerAddVo.setAgentName(agent.getAgentName());
        }

        //默认设置百分比为100
        merchantEmployerAddVo.setWorkerMonthIncomeRate(new BigDecimal(100));
        merchantEmployerBiz.applyMerchantAnon(merchantEmployerAddVo,createLinkReqVo.getMainstayNo());
    }
}
