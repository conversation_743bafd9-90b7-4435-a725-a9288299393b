package com.zhixianghui.service.merchant.listener.tasks;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.facade.merchant.service.MerchantChannelFacade;
import com.zhixianghui.service.merchant.listener.TaskRocketMQListener;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName MerchantCloseTaskListener
 * @Description TODO
 * @Date 2021/8/5 17:13
 */
@Component
@RocketMQMessageListener(topic = "topic_rysc_idinfo_data",consumeThreadMax = 1,consumerGroup = "rsyncIdInfoConsumer")
public class SyncIDInfoToMainstayTaskListener extends TaskRocketMQListener<JSONObject> {
    @Reference
    private MerchantChannelFacade merchantChannelFacade;

    @Override
    public void runTask(JSONObject jsonParam) {
        String startTimeStr = jsonParam.getString("startTime");
        String endTimeStr = jsonParam.getString("endTime");

        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isAnyEmpty(startTimeStr, endTimeStr)) {
            startTime = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1).toJdkDate()).toJdkDate();
            endTime = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -1).toJdkDate()).toJdkDate();
        }else {
            startTime = DateUtil.parse(startTimeStr, "yyyy-MM-dd HH:mm:ss").toJdkDate();
            endTime = DateUtil.parse(endTimeStr, "yyyy-MM-dd HH:mm:ss").toJdkDate();
        }
        merchantChannelFacade.syncUserInfo(startTime, endTime);
    }
}
