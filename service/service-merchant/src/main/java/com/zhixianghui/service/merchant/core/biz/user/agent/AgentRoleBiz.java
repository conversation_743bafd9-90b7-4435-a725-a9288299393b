package com.zhixianghui.service.merchant.core.biz.user.agent;

import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRoleFunction;
import com.zhixianghui.facade.merchant.vo.agent.AgentRoleVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.service.merchant.core.dao.user.agent.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* 供应商后台角色表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentRoleBiz {

    private final AgentRoleDao agentRoleDao;
    private final AgentFunctionDao agentFunctionDao;
    private final AgentRoleFunctionDao agentRoleFunctionDao;
    private final AgentStaffRoleDao agentStaffRoleDao;
    private final AgentStaffDao agentStaffDao;

    /**
     * 创建用工企业后台角色
     *
     * @param role 角色
     */
    public void create(AgentRole role) {
        if (role.getCreateTime() == null) {
            role.setCreateTime(new Date());
        }
        agentRoleDao.insert(role);
    }

    /**
     * 根据id查询用工企业后台角色
     *
     * @param agentNo 商户编号
     * @param id
     */
    public AgentRole getById(String agentNo, long id) {
        if (StringUtil.isEmpty(agentNo)) {
            return null;
        }

        AgentRole role = agentRoleDao.getById(id);
        if (role == null) {
            return null;
        }
        if (String.valueOf(RoleTypeEnum.PRESET_AGENT_NO.getType()).equals(role.getAgentNo())) {
            return role;
        }
        if (agentNo.equals(role.getAgentNo())) {
            return role;
        }
        return null;
    }

    public AgentRole getById(Long id) {
        return agentRoleDao.getById(id);
    }

    /**
     * 更新用工企业后台角色
     *
     * @param role 角色
     */
    public void update(AgentRole role) throws BizException {
        if (role == null || role.getAgentNo() == null || role.getId() == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户编号和id不能为空");
        }
        if (getById(role.getAgentNo(), role.getId()) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        agentRoleDao.updateByIdAndAgentNo(role);
    }

    /**
     * 根据id删除用工企业后台角色，并删除其与功能的关联
     *
     * @param agentNo  商户编号
     * @param id	 角色id
     */
    @Transactional
    public void deleteById(String agentNo, long id) throws BizException {
        if (getById(agentNo, id) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        agentRoleDao.deleteById(id);  // 删除角色
        agentRoleFunctionDao.deleteByRoleId(id);  // 删除该角色与功能的关联
        agentStaffRoleDao.deleteByRoleId(id);  // 删除该角色与员工的关联
    }

    @Transactional
    public void deleteById(Long roleId) {
        agentRoleDao.deleteById(roleId);  // 删除角色
        agentRoleFunctionDao.deleteByRoleId(roleId);  // 删除该角色与功能的关联
        agentStaffRoleDao.deleteByRoleId(roleId);  // 删除该角色与员工的关联
    }

    /**
     * 为角色分配功能
     *
     * @param agentNo         商户编号
     * @param roleId      角色id
     * @param functionIds 功能id
     */
    @Transactional
    public void updateFunction(String agentNo, long roleId, List<Long> functionIds) throws BizException {
        if (getById(agentNo, roleId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        // 先删除角色关联的所有功能
        agentRoleFunctionDao.deleteByRoleId(roleId);

        // 添加功能
        assignFunction(roleId, functionIds);
    }

    public void updateFunction(Long roleId, List<Long> functionIds) {
        // 先删除角色关联的所有功能
        agentRoleFunctionDao.deleteByRoleId(roleId);

        // 添加功能
        assignFunction(roleId, functionIds);
    }

    /**
     * 根据角色id获取其关联的功能
     *
     * @param agentNo    商户编号
     * @param roleId 角色id
     */
    public List<AgentFunction> listFunctionByRoleId(String agentNo, long roleId) throws BizException {
        if (getById(agentNo, roleId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        return agentFunctionDao.listByRoleId(roleId);
    }

    public List<AgentFunction> listFunctionByRoleId(Long roleId) {
        return agentFunctionDao.listByRoleId(roleId);
    }

    /**
     * 根据角色id获取其关联的操作员
     *
     * @param agentNo    商户编号
     * @param roleId 角色id
     */
    public List<AgentStaffVO> listStaffByRoleId(String agentNo, long roleId) throws BizException {
        if (getById(agentNo, roleId) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }

        return agentStaffDao.listStaffVOByRoleId(roleId);
    }

    /**
     * 查询所有角色
     */
    public List<AgentRole> listAll(String agentNo) {
        return agentRoleDao.listBy(Collections.singletonMap("agentNo", agentNo));
    }

    /**
     * 分页查询角色
     *
     * @param agentNo
     * @param paramMap
     * @param pageParam
     */
    public PageResult<List<AgentRoleVo>> listPage(String agentNo, Map<String, Object> paramMap, PageParam pageParam) {
        if (paramMap == null) {
            paramMap = new HashMap<>();
        }
        paramMap.put("agentNo", agentNo);
        return agentRoleDao.listAgentRoleVoPage(paramMap, pageParam);
    }

    public PageResult<List<AgentRoleVo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        if (paramMap == null) {
            paramMap = new HashMap<>();
        }
        return agentRoleDao.listPage("pmsListPage", "countAgentRole", paramMap, pageParam);
    }

    /**
     * 为角色分配功能
     */
    private void assignFunction(long roleId, List<Long> functionIds) {
        if (functionIds != null && !functionIds.isEmpty()) {
            Set<Long> allFunctionIdSet = agentFunctionDao.listAll().stream().map(AgentFunction::getId).collect(Collectors.toSet());
            // 校验功能id是否正确
            if (functionIds.stream().anyMatch(functionId -> !allFunctionIdSet.contains(functionId))) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("功能id错误");
            }

            List<AgentRoleFunction> roleFunctions = functionIds.stream().map(functionId -> {
                AgentRoleFunction roleFunction = new AgentRoleFunction();
                roleFunction.setCreateTime(new Date());
                roleFunction.setRoleId(roleId);
                roleFunction.setFunctionId(functionId);
                return roleFunction;
            }).collect(Collectors.toList());
            agentRoleFunctionDao.insert(roleFunctions);
        }
    }


    public Long count(AgentRoleVo agentRoleVo, String agentNo) {
        HashMap<String, Object> map = new HashMap<String, Object>(){{
            put("agentNo", agentNo);
            put("roleId", agentRoleVo.getId());
        }};
        return agentRoleDao.countBy("countByPresetRole", map);
    }
}