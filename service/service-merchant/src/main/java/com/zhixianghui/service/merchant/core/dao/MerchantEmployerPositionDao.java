package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 用工商户岗位信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Repository
public class MerchantEmployerPositionDao extends MyBatisDao<MerchantEmployerPosition, Long> {

    public List<MerchantEmployerPosition> listByMchNo(String mchNo){
        Assert.notNull(mchNo, "商户编号不能为空");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);
        return listBy(paramMap);
    }

    public void deleteByMchNo(String mchNo) {
        Assert.notNull(mchNo, "商户编号不能为空");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);
        deleteBy(paramMap);
    }

    public List<MerchantEmployerPosition> listByMchNoWithQuote(Map<String,Object> paramMap) {
        return this.listBy("listByMchNoWithQuote",paramMap);
    }

    public List<MerchantEmployerPosition> listByMchNoWithQuoteWithoutGroup(Map<String, Object> paramMap) {
        return this.listBy("listByMchNoWithQuoteWithoutGroup",paramMap);
    }
}
