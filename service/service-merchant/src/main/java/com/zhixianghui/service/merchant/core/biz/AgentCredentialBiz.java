package com.zhixianghui.service.merchant.core.biz;

import com.zhixianghui.service.merchant.core.dao.AgentCredentialDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.merchant.entity.AgentCredential;
import lombok.RequiredArgsConstructor;

/**
* 合伙人证件表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-02-01
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentCredentialBiz {

    private final AgentCredentialDao agentcredentialDao;

    public AgentCredential agentCredential(String agentNo) {
        AgentCredential credential = agentcredentialDao.getByAgentNo(agentNo);
        return credential;
    }
}