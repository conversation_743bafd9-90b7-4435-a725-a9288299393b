package com.zhixianghui.service.merchant.core.dao.user.agent;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentTradePwd;
import org.springframework.stereotype.Repository;

/**
 * 供应商支付密码表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-14
 */
@Repository
public class AgentTradePwdDao extends MyBatisDao<AgentTradePwd,Long> {
    public AgentTradePwd getByAgentNo(String agentNo) {
        return super.getOne("getByAgentNo", agentNo);
    }
}
