package com.zhixianghui.service.merchant.core.dao.user.supplier;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierRoleVo;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 供应商后台角色表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-12-11
 */
@Repository
public class SupplierRoleDao extends MyBatisDao<SupplierRole, Long> {
    public void updateByIdAndMchNo(SupplierRole role) {
        super.update("updateByIdAndMchNo", role);
    }

    public List<SupplierRole> listByMchNo(String mchNo) {
        return super.listBy(Collections.singletonMap("mchNo", mchNo));
    }

    public List<SupplierRole> listByStaffId(long staffId) {
        return super.listBy("listByStaffId", Collections.singletonMap("staffId", staffId));
    }

    public PageResult<List<SupplierRoleVo>> listSupplierRoleVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        return super.listPage("listPage", "listPageCount", paramMap, pageParam);

    }
}
