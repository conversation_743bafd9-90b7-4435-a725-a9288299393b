package com.zhixianghui.service.merchant.core.biz;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.constants.redis.RedisLua;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.facade.merchant.entity.PersonalIncomeTax;
import com.zhixianghui.service.merchant.core.dao.mapper.PersonalIncomeTaxMapper;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2022-06-22
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PersonalIncomeTaxBiz {

    private final PersonalIncomeTaxMapper personalincometaxMapper;

    private final String INCOME_TAX_PREFIX ="personal:income:tax";

    private final RedisClient redisClient;

    public List<PersonalIncomeTax> listAll() {
        return personalincometaxMapper.selectList(new QueryWrapper<PersonalIncomeTax>().lambda().orderByAsc(PersonalIncomeTax::getLevel)).stream().map(x->{
            x.setTaxRatePct(AmountUtil.mul(x.getTaxRate(),new BigDecimal(100)));
            return x;
        }).collect(Collectors.toList());
    }

    public PersonalIncomeTax getSuitTax(BigDecimal amount) {
        PersonalIncomeTax personalIncomeTax = new PersonalIncomeTax();
        String amountStr = "(" + amount.toString();
        Set<String> set = redisClient.zrevrangeByScoreLimit(INCOME_TAX_PREFIX,amountStr,"0",0,1);
        if (CollectionUtils.isEmpty(set)){
            List<PersonalIncomeTax> personalIncomeTaxList = listAll();
            List<String> arr = new ArrayList<>();
            for (PersonalIncomeTax x : personalIncomeTaxList) {
                arr.add(x.getStartPoint().toString());
                arr.add(x.getDeduction().toString() + "-" + x.getTaxRate().toString());
            }
            redisClient.evalLua(RedisLua.PERSONAL_INCOME_TAX,INCOME_TAX_PREFIX,new ArrayList<String>(){{add(INCOME_TAX_PREFIX);}},arr);
        }else{
            String[] arr;
            for (String str : set) {
                arr = str.split("-");
                personalIncomeTax.setDeduction(new BigDecimal(arr[0]));
                personalIncomeTax.setTaxRate(new BigDecimal(arr[1]));
                break;
            }
        }
        return personalIncomeTax;
    }
}