package com.zhixianghui.service.merchant.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import com.zhixianghui.service.merchant.listener.TaskRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MerchantCloseTaskListener
 * @Description TODO
 * @Date 2021/8/5 17:13
 */
@Component
@RocketMQMessageListener(topic = "merchant-close-task",consumeThreadMax = 1,consumerGroup = "merchantCloseConsumer")
public class MerchantCloseTaskListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private MerchantBiz merchantBiz;

    @Override
    public void runTask(JSONObject jsonParam) {
        int trialDays = jsonParam.getInteger("trialDays");
        Date now = new Date();
        Date date = DateUtil.addDay(now, trialDays);

        Map<String,Object> maps = new HashMap<>();
        maps.put("updateTime",now);
        maps.put("updateEndTime",date);
        merchantBiz.freezeMerchantExpire(maps);
    }
}
