package com.zhixianghui.service.merchant.core.dao.mapper;

import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-24
 */
@Mapper
public interface MerchantCkhQuoteMapper extends BaseMapper<MerchantCkhQuote> {

    MerchantCkhQuote getFeeRate(@Param("param") Map<String, Object> paramMap);

    Integer getBalancedMode(MerchantEmployerQuote merchantEmployerQuote);
}
