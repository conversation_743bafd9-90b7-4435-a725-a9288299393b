package com.zhixianghui.service.merchant.core.biz;

import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.vo.agent.AgentDetailVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentProductQuoteVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import com.zhixianghui.facade.merchant.vo.agent.SimpleAgentInfoVo;
import com.zhixianghui.service.merchant.core.dao.*;
import com.zhixianghui.service.merchant.core.vo.PromotionDiffVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合伙人基本信息表Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentBiz {

    private final AgentDao agentDao;
    private final AgentSalerDao agentSalerDao;
    private final AgentBankAccountDao agentBankAccountDao;
    private final AgentCredentialDao agentCredentialDao;
    private final AgentEmployerMainDao agentEmployerMainDao;
    private final AgentProductQuoteDao agentProductQuoteDao;
    private final MerchantFileDao merchantFileDao;
    private final MerchantDao merchantDao;

    public List<Agent> listBy(Map<String, Object> paramMap) {
        return agentDao.listBy(paramMap);
    }

    public Agent getByAgentNo(String agentNo) {
        return agentDao.getOne(Collections.singletonMap("agentNo", agentNo));
    }

    public List<SimpleAgentInfoVo> listAllSimpleAgentInfo() {
        List<Agent> list = agentDao.listAll();
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return list.stream().map(
                agent -> {
                    SimpleAgentInfoVo simpleAgentInfoVo = new SimpleAgentInfoVo();
                    BeanUtils.copyProperties(agent, simpleAgentInfoVo);
                    return simpleAgentInfoVo;
                }
        ).collect(Collectors.toList());
    }

    public List<SimpleAgentInfoVo> listAllSimpleAgentInfoByParam(Map<String,Object> param) {
        param.put("1", 1);
        List<Agent> list = agentDao.listBy(param);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return list.stream().map(
                agent -> {
                    SimpleAgentInfoVo simpleAgentInfoVo = new SimpleAgentInfoVo();
                    BeanUtils.copyProperties(agent, simpleAgentInfoVo);
                    return simpleAgentInfoVo;
                }
        ).collect(Collectors.toList());
    }

    public PageResult<List<Agent>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return agentDao.listPage(paramMap, pageParam);
    }

    public PageResult<List<AgentResVo>> listVoPage(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<AgentResVo>> pageResult = agentDao.listVoPage(paramMap, pageParam);
        if (pageResult != null && pageResult.getData() != null) {
            for (AgentResVo resVo : pageResult.getData()) {
                //遍历查询推广时间
                PromotionDiffVo promotion = merchantDao.queryPromotion(resVo.getAgentNo());
                if (promotion != null) {
                    resVo.setLatestPromotionTime(promotion.getLatestTime());
                    resVo.setPromotionDiff(promotion.getDiffday());
                }

            }
        }
        return pageResult;
    }

    public Agent getOne(Map<String, Object> paramMap) {
        return agentDao.getOne(paramMap);
    }

    public AgentProductQuote getAgentProductQuote(Long id) {
        return agentProductQuoteDao.getById(id);
    }
    /**
     * 查询合伙人详细信息
     *
     * @param agentNo 合伙人编号
     * @return
     */
    public AgentDetailVo getDetailByAgentNo(String agentNo) {
        LimitUtil.notEmpty(agentNo, "合伙人编号不能为空");

        Agent agent = getByAgentNo(agentNo);
        LimitUtil.notEmpty(agent, "合伙人编号不存在");
        AgentSaler agentSaler = agentSalerDao.getByAgentNo(agentNo);
        AgentBankAccount agentBankAccount = agentBankAccountDao.getByAgentNo(agentNo);
        AgentEmployerMain agentEmployerMain = agentEmployerMainDao.getByAgentNo(agentNo);
        AgentCredential agentCredential = agentCredentialDao.getByAgentNo(agentNo);
        List<AgentProductQuote> quoteList = agentProductQuoteDao.listByAgentNo(agentNo);
        List<MerchantFile> fileList = merchantFileDao.listByMchNoAndFileType(agentNo, null);

        AgentDetailVo agentDetailVo = new AgentDetailVo();
        if (agent != null) {
            BeanUtils.copyProperties(agent, agentDetailVo);
            agentDetailVo.setTaxPercent(agent.getWithholdingTaxRatio() == null ?
                    "0" : agent.getWithholdingTaxRatio().toString());
        }
        if (agentBankAccount != null) {
            BeanUtils.copyProperties(agentBankAccount, agentDetailVo);
        }
        if (agentCredential != null) {
            BeanUtils.copyProperties(agentCredential, agentDetailVo);
        }
        if (agentSaler != null) {
            BeanUtils.copyProperties(agentSaler, agentDetailVo);
        }
        if (agentEmployerMain != null) {
            BeanUtils.copyProperties(agentEmployerMain, agentDetailVo);
        }

        if (!CollectionUtils.isEmpty(quoteList)) {
            List<AgentProductQuoteVo> quoteVos = quoteList.stream().map(quote -> {
                AgentProductQuoteVo vo = new AgentProductQuoteVo();
                BeanUtils.copyProperties(quote, vo);
                return vo;
            }).collect(Collectors.toList());
            agentDetailVo.setQuoteVoList(quoteVos);
        }

        if (!CollectionUtils.isEmpty(fileList)) {
            fileList.forEach(file -> {
                if (file.getFileType() == MerchantFileTypeEnum.BUSINESS_LICENSE.getValue()) {
                    agentDetailVo.setBusinessLicenseFileUrl(file.getFileUrl());
                } else if (file.getFileType() == MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue()) {
                    agentDetailVo.setIdCardEmblemFileUrl(file.getFileUrl());
                } else if (file.getFileType() == MerchantFileTypeEnum.ID_CARD_HEAD.getValue()) {
                    agentDetailVo.setIdCardHeadFileUrl(file.getFileUrl());
                } else if (file.getFileType() == MerchantFileTypeEnum.ID_CARD_COPY.getValue()) {
                    agentDetailVo.setIdCardCopyFileUrl(file.getFileUrl());
                }
            });
        }

        return agentDetailVo;
    }

    public void update(Agent agent) {
        agentDao.update(agent);
    }

    public List<SimpleAgentInfoVo> listNotRetreatAgentSimple(Long salerId) {
        Map<String, Object> paramMap = null;
        if (salerId != null) {
            paramMap = new HashMap<>(1);
            paramMap.put("salerId", salerId);
        }
        List<AgentResVo> list = agentDao.listVoAll(paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        return list.stream().filter(
                agent -> agent.getAgentStatus() != AgentStatusEnum.RETREAT.getValue()
                        && agent.getAgentStatus() != AgentStatusEnum.CREATE.getValue()
        ).map(
                agent -> {
                    SimpleAgentInfoVo simpleAgentInfoVo = new SimpleAgentInfoVo();
                    BeanUtils.copyProperties(agent, simpleAgentInfoVo);
                    return simpleAgentInfoVo;
                }
        ).collect(Collectors.toList());
    }

    public void closeExpireTrialAgent(Map<String, Object> paramMap) {
        agentDao.update("closeExpireTrialAgent", paramMap);
    }
}
