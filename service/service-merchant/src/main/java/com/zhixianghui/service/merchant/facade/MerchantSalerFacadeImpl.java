package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.service.MerchantSalerFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantSalerBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


/**
 * 商户信息查询
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantSalerFacadeImpl implements MerchantSalerFacade {
    @Autowired
    private MerchantSalerBiz merchantSalerBiz;

    @Override
    public MerchantSaler getByMchNo(String mchNo) {
        return merchantSalerBiz.getByMchNo(mchNo);
    }

    @Override
    public List<MerchantSaler> getBatchMerchantSale(List<Long> saleId) {
        return merchantSalerBiz.getBatchMerchantSaler(saleId);
    }

    @Override
    public void update(MerchantSaler merchantSaler) {
        merchantSalerBiz.update(merchantSaler);
    }

    @Override
    public List<MerchantSaler> getBatchMerchantMchNO(List<String> mchNoList) {

        return merchantSalerBiz.getMerchantSaleByMchNo(mchNoList);
    }

    @Override
    public MerchantSaler getOne(Long saleId) {
        return merchantSalerBiz.getOne(saleId);
    }

    @Override
    public Map<String, Object> getSalerStatistics(Map<String, Object> thisMonth,Map<String,Object> lastMonth) {
        return merchantSalerBiz.getSalerStatistics(thisMonth,lastMonth);
    }
}
