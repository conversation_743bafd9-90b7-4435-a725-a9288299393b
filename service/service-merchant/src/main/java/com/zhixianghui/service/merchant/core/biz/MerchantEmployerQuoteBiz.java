package com.zhixianghui.service.merchant.core.biz;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.vo.merchant.ListMerchantProductVo;
import com.zhixianghui.facade.merchant.vo.merchant.MainstaySimpleVo;
import com.zhixianghui.service.merchant.core.dao.*;
import com.zhixianghui.service.merchant.core.dao.mapper.MerchantCkhQuoteMapper;
import com.zhixianghui.service.merchant.core.dao.mapper.MerchantZftQuoteMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报价单
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
@Slf4j
public class MerchantEmployerQuoteBiz {

    @Autowired
    private MerchantZftQuoteMapper merchantZftQuoteMapper;

    @Autowired
    private MerchantCkhQuoteMapper merchantCkhQuoteMapper;
    @Autowired
    private MerchantEmployerQuoteDao quoteDao;
    @Reference
    private VendorFacade vendorFacade;
    @Autowired
    private MerchantEmployerQuoteRateDao merchantEmployerQuoteRateDao;
    @Autowired
    private MerchantDao merchantDao;
    @Autowired
    private MerchantEmployerQuotePositionDao merchantEmployerQuotePositionDao;

    @Autowired
    private MerchantEmployerPositionDao merchantEmployerPositionDao;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    public List<MerchantEmployerQuote> listByMchNo(String mchNo) {
        Assert.hasText(mchNo, "商户编号不能为空");
        return  quoteDao.listByMchNo(mchNo);
    }

    public List<MerchantEmployerQuote> getByMainstayAndMchNo(MerchantChannel merchantChannel, Merchant employer) {
        return quoteDao.listBy(new HashMap<String, Object>() {{
            put("mchNo", employer.getMchNo());
            put("mainstayMchNo", merchantChannel.getMchNo());
        }});
    }

    public void update(MerchantEmployerQuote merchantEmployerQuote) {
        quoteDao.update(merchantEmployerQuote);
    }

    public void updateMainstay(Merchant merchant) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mainstayMchNo",merchant.getMchNo());
        List<MerchantEmployerQuote> merchantEmployerQuoteList = quoteDao.listBy(paramMap);
        merchantEmployerQuoteList.stream().forEach(x->{
            x.setMainstayMchName(merchant.getMchName());
        });
        quoteDao.update(merchantEmployerQuoteList);
    }

    public List<MerchantEmployerQuote> listBy(Map<String, Object> params) {
        return quoteDao.listBy(params);
    }

    public List<MerchantEmployerQuote> getQuoteList(String mchNo,Map<String, Object> paramMap) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        List<MerchantEmployerQuote> quoteList = quoteDao.listBy("getQuoteList",paramMap);
        Map<String,Object> map = new HashMap<>();
        quoteList.forEach(x->{
            x.setMchName(merchant.getMchName());
            //查询关联的岗位信息
            map.put("quoteId",x.getId());
            List<MerchantEmployerPosition> positionList = merchantEmployerPositionDao.listBy("joinWithQuoteId",map);
            if (positionList.size() > 0) {
                positionList.stream().forEach(j->{
                    x.getPositionList().add(j.getId());
                    x.getPositionNameList().add(j.getWorkCategoryName());
                    x.getInvoiceCategoryList().addAll(j.getJsonEntity().getInvoiceCategoryList());
                });
            }
            if (x.getProductNo().equals(ProductNoEnum.CKH.getValue())){
                MerchantCkhQuote merchantCkhQuote = merchantCkhQuoteMapper.selectOne(new QueryWrapper<MerchantCkhQuote>().lambda().eq(MerchantCkhQuote::getQuoteId,x.getId()));
                if (merchantCkhQuote != null){
                    BigDecimal hundred = new BigDecimal(100);
                    merchantCkhQuote.setAddedTaxRatePct(AmountUtil.mul(merchantCkhQuote.getAddedTaxRate(),hundred,2));
                    merchantCkhQuote.setTaxRatePct(AmountUtil.mul(merchantCkhQuote.getTaxRate(),hundred,2));
                    merchantCkhQuote.setServiceFeeRatePct(AmountUtil.mul(merchantCkhQuote.getServiceFeeRate(),hundred,2));
                }
                x.setMerchantCkhQuote(merchantCkhQuote);
            } else if (x.getProductNo().equals(ProductNoEnum.ZFT.getValue())){
                List<MerchantZftQuote> merchantZftQuoteList = merchantZftQuoteMapper.selectList(new QueryWrapper<MerchantZftQuote>().lambda().eq(MerchantZftQuote::getQuoteId,x.getId()));
                x.setMerchantZftQuote(merchantZftQuoteList);
            } else if (x.getProductNo().equals(ProductNoEnum.CEP.getValue())) {

            }

            //获取账户信息
            List<EmployerAccountInfo> employerAccountInfos = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(mchNo, x.getMainstayMchNo());
            List<EmployerAccountInfo> collect = employerAccountInfos.stream().filter(it -> it.getStatus().intValue() == CommonStatusEnum.ACTIVE.getValue()).collect(Collectors.toList());

            if (collect != null && !collect.isEmpty()) {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("mainstayNo", x.getMainstayMchNo());
                dataMap.put("mainstayName", x.getMainstayMchName());
                dataMap.put("employerAccountInfo", collect);
                x.getEmployerAccountInfo().add(JSON.toJSONString(dataMap));
            }
        });
        return quoteList;
    }

    public MerchantEmployerQuote getById(Long id) {
        return quoteDao.getById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        quoteDao.deleteById(id);
        Map<String,Object> map = new HashMap<String,Object>(){{put("quoteId",id);}};
        merchantEmployerQuoteRateDao.deleteBy("deleteByQuoteId",map);
        merchantEmployerQuotePositionDao.deleteBy("deleteByQuoteId",map);
    }

    public List<Map<String, Object>> getQuoteByMchNo(String mchNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",mchNo);
        paramMap.put("status",MerchantQuoteStatusEnum.ACTIVE.getValue());
        List<Map<String,Object>> quoteList = quoteDao.listBy("getQuoteByMchNo",paramMap);
        return quoteList;
    }
    public List<ListMerchantProductVo> listMchProduct(Map<String, Object> param) {
        return quoteDao.listMchProduct(param);
    }

    public List<MainstaySimpleVo> listMainstayByEmployerNoAndProduct(Integer status, String productNo, String mchNo) {
        Map param = new HashMap();
        if (status != null) {
            param.put("status", status);
        }
        if (StringUtils.isNotBlank(productNo)) {
            param.put("productNo", productNo);
        }
        param.put("mchNo", mchNo);

        return quoteDao.listMainstayByEmployerNoAndProduct(param);
    }

    public List<MerchantEmployerQuote> listGroupByProductNo(Map<String, Object> paramMap) {
        return quoteDao.listBy("listGroupByProductNo",paramMap);
    }

    public List<MerchantEmployerQuote> getQuoteListWithSupplier(String mchNo, Map<String, Object> paramMap) {
        Merchant merchant = merchantDao.getByMchNo(mchNo);
        List<MerchantEmployerQuote> quoteList = quoteDao.listBy("getQuoteList",paramMap);
        Map<String,Object> map = new HashMap<>();
        quoteList.forEach(x->{
            x.setMchName(merchant.getMchName());
            //查询关联的岗位信息
            map.put("quoteId",x.getId());
            List<MerchantEmployerPosition> positionList = merchantEmployerPositionDao.listBy("joinWithQuoteId",map);
            if (positionList.size() > 0){
                positionList.stream().forEach(j->{
                    x.getPositionList().add(j.getId());
                    x.getPositionNameList().add(j.getWorkCategoryName());
                });
            }
            if (x.getProductNo().equals(ProductNoEnum.CKH.getValue())){
                MerchantCkhQuote merchantCkhQuote = merchantCkhQuoteMapper.selectOne(new QueryWrapper<MerchantCkhQuote>().lambda().eq(MerchantCkhQuote::getQuoteId,x.getId()));
                if (merchantCkhQuote != null){
                    BigDecimal hundred = new BigDecimal(100);
                    merchantCkhQuote.setAddedTaxRatePct(AmountUtil.mul(merchantCkhQuote.getAddedTaxRate(),hundred,2));
                    merchantCkhQuote.setTaxRatePct(AmountUtil.mul(merchantCkhQuote.getTaxRate(),hundred,2));
                    merchantCkhQuote.setServiceFeeRatePct(AmountUtil.mul(merchantCkhQuote.getServiceFeeRate(),hundred,2));
                }
                x.setMerchantCkhQuote(merchantCkhQuote);
            }
        });
        return quoteList;
    }

    public boolean isExistQuote(String mchNo, String mainstayNo, String productNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",mchNo);
        paramMap.put("mainstayMchNo",mainstayNo);
        paramMap.put("productNo",productNo);
        paramMap.put("status",MerchantQuoteStatusEnum.ACTIVE.getValue());
        List<MerchantEmployerQuote> quoteList = quoteDao.listBy(paramMap);
        if (quoteList.size() > 0){
            return true;
        }else{
            return false;
        }
    }
}
