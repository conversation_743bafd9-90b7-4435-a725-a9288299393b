package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.service.MerchantInvoiceInfoFacade;
import com.zhixianghui.service.merchant.core.biz.MerchantInvoiceInfoBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 商户开票信息
 *
 * <AUTHOR>
 * @date 2020/12/25
 **/
@Service
public class MerchantInvoiceInfoFacadeImpl implements MerchantInvoiceInfoFacade {
    @Autowired
    private MerchantInvoiceInfoBiz invoiceInfoBiz;

    @Override
    public MerchantInvoiceInfo getByMchNo(String mchNo) {
        return invoiceInfoBiz.getByMchNo(mchNo);
    }

    @Override
    public void update(MerchantInvoiceInfo invoiceInfo) {
        invoiceInfoBiz.update(invoiceInfo);
    }

    @Override
    public void insert(MerchantInvoiceInfo invoiceInfo) {
        invoiceInfoBiz.insert(invoiceInfo);
    }
}
