package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.vo.merchant.ListMerchantProductVo;
import com.zhixianghui.facade.merchant.vo.merchant.MainstaySimpleVo;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 商户产品费率信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Repository
public class MerchantEmployerQuoteDao extends MyBatisDao<MerchantEmployerQuote, Long> {
    public List<MerchantEmployerQuote> listByMchNo(String mchNo){
        Assert.notNull(mchNo, "商户编号不能为空");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);
        return listBy(paramMap);
    }

    public void deleteByMchNo(String mchNo) {
        Assert.notNull(mchNo, "商户编号不能为空");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);
        deleteBy(paramMap);
    }

    public List<ListMerchantProductVo> listMchProduct(Map<String, Object> param) {
        final List<ListMerchantProductVo> listMchProduct = this.listBy("listMchProduct", param);
        return listMchProduct;
    }

    public List<MainstaySimpleVo> listMainstayByEmployerNoAndProduct(Map<String, Object> param) {
        return this.listBy("listMainstayByEmployerNoAndProduct", param);
    }
}
