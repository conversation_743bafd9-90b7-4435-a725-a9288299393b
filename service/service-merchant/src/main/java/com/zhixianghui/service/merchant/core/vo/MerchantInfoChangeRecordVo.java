package com.zhixianghui.service.merchant.core.vo;


import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.vo.record.BaseInfoVo;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/7/19 14:15
 */
public class MerchantInfoChangeRecordVo extends MerchantInfoChangeRecord {

    public MerchantInfoChangeRecord build(
            Map<String, String> description,
            Integer operation,
            Map.Entry<String, Object> entry,
            Map<String, Object> newMap,
            BaseInfoVo infoVo,
            Integer source,
            String mchNo
    ) {
        MerchantInfoChangeRecord record = new MerchantInfoChangeRecord();
        record.setCreateTime(new Date());
        record.setBusinessId(infoVo.getFlowId());
        record.setMchNo(mchNo);
        record.setObjectKey(entry.getKey());
        record.setObjectName(description.get(entry.getKey()));
        record.setOriginalValue((String) entry.getValue());
        record.setTargetValue((String) newMap.get(entry.getKey()));
        record.setSource(source);
        record.setOperate(operation);
        record.setOperateId(infoVo.getLoginId());
        record.setOperateName(infoVo.getLoginName());
        record.setVersion(0);
        return record;
    }

    // 只有增加或者删除
    public MerchantInfoChangeRecord build(
            String attributeNameKey,
            String attributeName,
            Integer operation,
            String rule,
            BaseInfoVo infoVo,
            Integer source,
            String mchNo
    ) {
        MerchantInfoChangeRecord record = new MerchantInfoChangeRecord();
        record.setCreateTime(new Date());
        record.setBusinessId(infoVo.getFlowId());
        record.setMchNo(mchNo);
        record.setObjectKey(attributeNameKey);
        record.setObjectName(attributeName);

        if (operation == OperationEnum.ADD.getOperation()) {
            record.setTargetValue(rule);
        } else if (operation == OperationEnum.DELETE.getOperation()) {
            record.setOriginalValue(rule);
        }
        record.setSource(source);
        record.setOperate(operation);
        record.setOperateId(infoVo.getLoginId());
        record.setOperateName(infoVo.getLoginName());
        record.setVersion(0);
        return record;
    }



}
