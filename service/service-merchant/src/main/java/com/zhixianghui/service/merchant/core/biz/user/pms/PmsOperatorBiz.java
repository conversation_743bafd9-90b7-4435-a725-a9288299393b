package com.zhixianghui.service.merchant.core.biz.user.pms;

import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentLeaderEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRole;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator;
import com.zhixianghui.service.merchant.core.dao.user.pms.PmsDepartmentDao;
import com.zhixianghui.service.merchant.core.dao.user.pms.PmsOperatorDao;
import com.zhixianghui.service.merchant.core.dao.user.pms.PmsRoleDao;
import com.zhixianghui.service.merchant.core.dao.user.pms.PmsRoleOperatorDao;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author: Cmf
 * Date: 2019/10/11
 * Time: 15:53
 * Description:
 */
@Service
public class PmsOperatorBiz {
    public static final int HISTORY_PWD_MAX_COUNT = 4;


    @Autowired
    private PmsOperatorDao pmsOperatorDao;

    @Autowired
    private PmsDepartmentDao pmsDepartmentDao;

    @Autowired
    private PmsRoleOperatorDao pmsRoleOperatorDao;

    @Autowired
    private PmsDepartmentBiz pmsDepartmentBiz;

    @Autowired
    private PmsRoleDao pmsRoleDao;

    @Autowired
    private RedisClient redisClient;

    public PmsOperator getOperatorById(Long id) {
        return pmsOperatorDao.getById(id);
    }

    public PmsOperator getOperatorByLoginName(String loginName) {
        PmsOperator operator = pmsOperatorDao.findByLoginName(loginName);

        if (operator == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户不存在");
        }

        List<Long> roleIds = pmsRoleOperatorDao.listByOperatorId(operator.getId()).stream().map(PmsRoleOperator::getRoleId).collect(Collectors.toList());
        if (roleIds.size() == 0) {
            roleIds = Collections.emptyList();
        }
        operator.setRoleIds(roleIds);
        return operator;
    }

    public void updateOperator(PmsOperator operator) {
        pmsOperatorDao.update(operator);
        //新增员工插入缓存
        redisClient.hset(PlatformSource.OPERATION.getValue() + ":" + operator.getId(),operator.getCacheMap());

    }

    public void updateOperatorPwd(Long operatorId, String newPwd, boolean isChangedPwd) {
        PmsOperator pmsOperator = pmsOperatorDao.getById(operatorId);
        String historyPwd = pmsOperator.getHistoryPwd();
        if (StringUtil.isNotEmpty(historyPwd)) {
            if (historyPwd.contains(newPwd)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("新密码不能与前四次历史密码相同");
            }
            String[] historyPwdArray = historyPwd.split(",");
            if (historyPwdArray.length >= HISTORY_PWD_MAX_COUNT) {
                historyPwd = historyPwd.substring(historyPwd.indexOf(",") + 1);
            }
            historyPwd = historyPwd + "," + newPwd;
        } else {
            historyPwd = newPwd;
        }
        pmsOperator.setLoginPwd(newPwd);
        pmsOperator.setPwdErrorCount(0); // 密码错误次数重置为0
        pmsOperator.setIsChangedPwd(isChangedPwd ? PublicStatus.ACTIVE : PublicStatus.INACTIVE); // 设置密码为已修改过
        pmsOperator.setLastModPwdTime(new Date());// 最后密码修改时间
        pmsOperator.setHistoryPwd(historyPwd);// 更新历史密码
        pmsOperatorDao.update(pmsOperator);
    }

    public PageResult<List<PmsOperator>> listOperatorPage(Map<String, Object> paramMap, PageParam pageParam) {
        return pmsOperatorDao.listPage(paramMap, pageParam);
    }

    @Transactional
    public void deleteOperatorById(long id) {
        PmsOperator operator = pmsOperatorDao.getById(id);
        if (operator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }

        // 移除该员工的部门负责人身份
        PmsDepartment department = pmsDepartmentDao.getById(operator.getDepartmentId());
        if (department != null && department.getLeaderId() == id) {
            department.setLeaderId(PmsDepartmentLeaderEnum.UNALLOCATED.getId());
            department.setLeaderName(PmsDepartmentLeaderEnum.UNALLOCATED.getName());
        }
        // 删除员工和权限
        pmsOperatorDao.deleteById(id);
        pmsRoleOperatorDao.deleteByOperatorId(id);
        //新增员工插入缓存
        redisClient.del(PlatformSource.OPERATION.getValue() + ":" + operator.getId());
    }

    @Transactional
    public void updateOperatorAndAssignRoles(PmsOperator operator, List<Long> roleIds) {
        PmsOperator origin = pmsOperatorDao.getById(operator.getId());
        if (origin == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }

        setDepartmentInfo(operator, operator.getDepartmentId());
        pmsOperatorDao.update(operator);

        // 部门发生变更
        if (origin.getDepartmentId() != PmsDepartmentEnum.UNALLOCATED.getId()
                && origin.getDepartmentId() != PmsDepartmentEnum.COMPANY.getId()
                && !origin.getDepartmentId().equals(operator.getDepartmentId())) {
            // 员工已经是其他部门的负责人，则其他部门负责人设为未指定
            PmsDepartment originDepartment = pmsDepartmentDao.getById(origin.getDepartmentId());
            if (originDepartment.getLeaderId() == operator.getId()) {
                originDepartment.setLeaderId(PmsDepartmentLeaderEnum.UNALLOCATED.getId());
                originDepartment.setLeaderName("");
                pmsDepartmentDao.update(originDepartment);
            }
        }
        // 重新设置权限
        pmsRoleOperatorDao.deleteByOperatorId(operator.getId());
        if (roleIds != null && roleIds.size() > 0) {
            List<PmsRoleOperator> roleOperatorList = roleIds.stream().map(p -> {
                PmsRoleOperator pmsRoleOperator = new PmsRoleOperator();
                pmsRoleOperator.setRoleId(p);
                pmsRoleOperator.setOperatorId(operator.getId());
                return pmsRoleOperator;
            }).collect(Collectors.toList());
            pmsRoleOperatorDao.insert(roleOperatorList);
        }

        //新增员工插入缓存
        redisClient.hset(PlatformSource.OPERATION.getValue() + ":" + operator.getId(),operator.getCacheMap());
    }

    @Transactional
    public void insertOperatorAndAssignRoles(PmsOperator operator, List<Long> roleIds) {
        setDepartmentInfo(operator, operator.getDepartmentId());
        try {
            pmsOperatorDao.insert(operator);
        } catch (DuplicateKeyException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号已存在");
        }

        if (roleIds != null && roleIds.size() > 0) {
            List<PmsRoleOperator> roleOperatorList = roleIds.stream().map(p -> {
                PmsRoleOperator pmsRoleOperator = new PmsRoleOperator();
                pmsRoleOperator.setRoleId(p);
                pmsRoleOperator.setOperatorId(operator.getId());
                return pmsRoleOperator;
            }).collect(Collectors.toList());
            pmsRoleOperatorDao.insert(roleOperatorList);
        }

        //新增员工插入缓存
        redisClient.hset(PlatformSource.OPERATION.getValue() + ":" + operator.getId(),operator.getCacheMap());
    }

    /**
     * 查询部门负责人管理的员工
     * @param leaderId      负责人 id
     */
    public List<PmsOperator> listByLeaderId(long leaderId) {
        PmsOperator leader = pmsOperatorDao.getById(leaderId);
        if (leader == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }
        if (leader.getDepartmentId() == PmsDepartmentEnum.COMPANY.getId()
                || leader.getDepartmentId() == PmsDepartmentEnum.UNALLOCATED.getId()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不是负责人");
        }
        PmsDepartment department = pmsDepartmentDao.getById(leader.getDepartmentId());
        if (department == null || department.getLeaderId() != leaderId) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不是负责人");
        }

        return listByDepartmentId(department.getId());
    }

    /**
     * 查询部门负责人管理的员工，并且递归查询下级部门
     * @param leaderId      负责人 id
     */
    public List<PmsOperator> listByLeaderIdRecursive(long leaderId) {
        PmsOperator leader = pmsOperatorDao.getById(leaderId);
        if (leader == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }
        if (leader.getDepartmentId() == PmsDepartmentEnum.COMPANY.getId()
                || leader.getDepartmentId() == PmsDepartmentEnum.UNALLOCATED.getId()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不是负责人");
        }
        PmsDepartment department = pmsDepartmentDao.getById(leader.getDepartmentId());
        if (department == null || department.getLeaderId() != leaderId) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不是负责人");
            List<PmsOperator> list = new ArrayList<>();
            list.add(leader);
            return list;
        }
        return listByDepartmentIdRecursive(department.getId());
    }

    public List<PmsOperator> getSale(long leaderId) {
        PmsOperator leader = pmsOperatorDao.getById(leaderId);
        if (leader == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }
        if (leader.getDepartmentId() == PmsDepartmentEnum.COMPANY.getId()
                || leader.getDepartmentId() == PmsDepartmentEnum.UNALLOCATED.getId()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工审核未通过...");
        }
        PmsDepartment department = pmsDepartmentDao.getById(leader.getDepartmentId());
        if (department == null) {
           throw CommonExceptions.BIZ_INVALID.newWithErrMsg("部门不存在...");
        }
        if (department.getLeaderId() == leaderId) {
            return listByDepartmentIdRecursive(department.getId());
        }
        List<PmsOperator> list = new ArrayList<>();
        list.add(leader);
        return list;
    }

    /**
     * 根据部门id查询员工
     * @param departmentId  部门 id
     */
    public List<PmsOperator> listByDepartmentId(long departmentId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("departmentId", departmentId);
        return pmsOperatorDao.listBy(paramMap);
    }

    /**
     * 根据部门id查询员工，并且递归查询下级部门
     * @param departmentId  部门 id
     */
    public List<PmsOperator> listByDepartmentIdRecursive(long departmentId) {
        // 查询所有部门并按照上级部门 id 分组
        Map<Long, List<PmsDepartment>> map = pmsDepartmentDao.listAll().stream().collect(Collectors.groupingBy(PmsDepartment::getParentId));
        // 查询该部门及其所有下级部门的 id 集合
        List<Long> departmentIds = new ArrayList<>(Collections.singletonList(departmentId));
        Queue<Long> queue = new LinkedList<>(Collections.singletonList(departmentId));

        while (!queue.isEmpty()) {
            List<PmsDepartment> children = map.remove(queue.poll());
            if (children != null) {
                children.forEach((department) -> {
                    departmentIds.add(department.getId());
                    queue.add(department.getId());
                });
            }
        }

        Map<String, Object> param = new HashMap<>();
        param.put("departmentIds", departmentIds);
        return pmsOperatorDao.listBy(param);
    }

    /**
     * 根据部门编号查询员工
     * @param number    {@link PmsDepartmentNumberEnum#getNumber()}
     */
    public List<PmsOperator> listByDepartmentNumber(String number) {
        PmsDepartment department = pmsDepartmentBiz.getDepartmentByNumber(number);
        if (department == null) {
            return new ArrayList<>();
        } else {
            return listByDepartmentId(department.getId());
        }
    }

    /**
     * 根据部门编号查询员工，并且递归查询下级部门
     * @param number    {@link PmsDepartmentNumberEnum#getNumber()}
     */
    public List<PmsOperator> listByDepartmentNumberRecursive(String number) {
        PmsDepartment department = pmsDepartmentBiz.getDepartmentByNumber(number);
        if (department == null) {
            return new ArrayList<>();
        } else {
            return listByDepartmentIdRecursive(department.getId());
        }
    }
    /**
     * 查询未配置所属部门的员工
     */
    public List<PmsOperator> listWithoutDepartment() {
        Map<String, Object> param = new HashMap<>();
        param.put("departmentId", PmsDepartmentEnum.UNALLOCATED.getId());
        return pmsOperatorDao.listBy(param);
    }

    /**
     * 为员工配置所属部门
     * @param id            员工 id
     * @param departmentId  部门 id
     */
    @Transactional
    public void assignDepartment(long id, long departmentId) {
        PmsOperator operator = pmsOperatorDao.getById(id);
        if (operator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("员工不存在");
        }
        // 员工原本属于该部门，无需操作
        if (operator.getDepartmentId() == departmentId) {
            return;
        }

        // 员工已经是其他部门的负责人，则其他部门负责人设为未指定
        PmsDepartment originDepartment = pmsDepartmentDao.getById(operator.getDepartmentId());
        if (originDepartment != null && originDepartment.getId() != departmentId
                && originDepartment.getLeaderId() == id) {
            originDepartment.setLeaderId(PmsDepartmentLeaderEnum.UNALLOCATED.getId());
            originDepartment.setLeaderName(PmsDepartmentLeaderEnum.UNALLOCATED.getName());
            pmsDepartmentDao.update(originDepartment);
        }

        // 更新部门信息
        setDepartmentInfo(operator, departmentId);
        pmsOperatorDao.update(operator);
    }

    /**
     * 设置完整部门信息
     */
    private void setDepartmentInfo(PmsOperator operator, Long departmentId) {
        if (departmentId == null || departmentId == PmsDepartmentEnum.UNALLOCATED.getId()) {  // 未配置部门
            operator.setDepartmentId(PmsDepartmentEnum.UNALLOCATED.getId());
            operator.setDepartmentName(PmsDepartmentEnum.UNALLOCATED.getName());
        } else if (departmentId == PmsDepartmentEnum.COMPANY.getId()) {  // 配置为全公司
            operator.setDepartmentId(PmsDepartmentEnum.COMPANY.getId());
            operator.setDepartmentName(PmsDepartmentEnum.COMPANY.getName());
        } else {
            PmsDepartment department = pmsDepartmentDao.getById(departmentId);
            if (department == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("部门不存在");
            }
            operator.setDepartmentId(departmentId);
            operator.setDepartmentName(department.getDepartmentName());
        }
    }

    public List<PmsRoleOperator> listOperatorByRoleId(Integer roleId) {
        return pmsRoleOperatorDao.listBy(new HashMap<String, Object>(){{
            put("roleId", roleId);
        }});
    }

    public Long countEmployerByRoleId(Long roleId) {
        return pmsRoleOperatorDao.countBy("countEmployerByRoleId", new HashMap<String, Object>(){{ put("roleId", roleId);}});
    }

    public boolean isAdmin(Long id) {
        List<PmsRoleOperator> list = pmsRoleOperatorDao.listBy("admin", new HashMap<String, Object>(){{ put("operatorId", id);}});
        return !CollectionUtils.isEmpty(list);
    }

    public Long getSaleRole() {
        List<PmsRole> list = pmsRoleDao.listBy(new HashMap<String, Object>() {{
            put("roleName", "销售");
            put("roleType", RoleTypeEnum.PRESET.getType());
        }});
        return CollectionUtils.isEmpty(list) ? null : list.get(0).getId();
    }   public List<PmsOperator> getAll() {
        List<PmsOperator> pmsOperatorList = pmsOperatorDao.listAll();
        return pmsOperatorList;
    }

    public void getAndPutOperatorCache(Long id) {
        PmsOperator pmsOperator = pmsOperatorDao.getById(id);
        if (pmsOperator != null){
            redisClient.hset(PlatformSource.OPERATION.getValue() + ":" + pmsOperator.getId(),pmsOperator.getCacheMap());
        }
    }

    public List<PmsOperator> listBy(Map<String, Object> paramMap) {
        return pmsOperatorDao.listBy(paramMap);
    }

    public List<PmsOperator> listActiveByDepartmentNumber(String num) {
        PmsDepartment department = pmsDepartmentBiz.getDepartmentByNumber(num);
        if (department == null) {
            return new ArrayList<>();
        } else {
            return listActiveByDepartmentId(department.getId());
        }
    }

    private List<PmsOperator> listActiveByDepartmentId(Long id) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("departmentId", id);
        paramMap.put("status", PmsOperatorStatusEnum.ACTIVE.getValue());
        return pmsOperatorDao.listBy(paramMap);
    }
}
