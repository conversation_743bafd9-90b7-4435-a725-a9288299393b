package com.zhixianghui.service.merchant.core.biz.user.supplier;

import com.zhixianghui.service.merchant.core.dao.user.supplier.SupplierRoleFunctionDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
* 供应商后台角色功能关联表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-12-11
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierRoleFunctionBiz {

    private final SupplierRoleFunctionDao mapper;
}