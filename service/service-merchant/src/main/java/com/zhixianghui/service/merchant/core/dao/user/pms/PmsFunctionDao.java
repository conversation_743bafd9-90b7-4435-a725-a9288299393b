package com.zhixianghui.service.merchant.core.dao.user.pms;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * Author: Cmf
 * Date: 2019/10/22
 * Time: 20:22
 * Description:
 */
@Repository("pmsFunctionDao")
public class PmsFunctionDao extends MyBatisDao<PmsFunction, Long> {
    public List<PmsFunction> listByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.size() == 0) {
            return Collections.emptyList();
        }
        return super.listBy("listByRoleIds", Collections.singletonMap("roleIds", roleIds));
    }

    public List<PmsFunction> listByParentId(Long parentId) {
        if (parentId == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("parentId不能为null");
        }
        return super.listBy("listByParentId", Collections.singletonMap("parentId", parentId));
    }
}
