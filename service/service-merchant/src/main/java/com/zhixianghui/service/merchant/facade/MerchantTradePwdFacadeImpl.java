package com.zhixianghui.service.merchant.facade;

import com.zhixianghui.facade.merchant.entity.user.portal.MerchantTradePwd;
import com.zhixianghui.facade.merchant.service.MerchantTradePwdFacade;
import com.zhixianghui.service.merchant.core.biz.user.portal.MerchantTradePwdBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class MerchantTradePwdFacadeImpl implements MerchantTradePwdFacade {
	
	@Autowired
	private MerchantTradePwdBiz merchantTradePwdBiz;

	/**
	 * 根据商户编号查询
	 *
	 * @param mchNo
	 */
	@Override
	public MerchantTradePwd getByMchNo(String mchNo) {
		return merchantTradePwdBiz.getByMchNo(mchNo);
	}

	/**
	 * 创建
	 *
	 * @param tradePwd
	 */
	@Override
	public void create(MerchantTradePwd tradePwd) {
		merchantTradePwdBiz.create(tradePwd);
	}


	/**
	 * 更新
	 *
	 * @param tradePwd
	 */
	@Override
	public void update(MerchantTradePwd tradePwd) {
		merchantTradePwdBiz.update(tradePwd);
	}
}
