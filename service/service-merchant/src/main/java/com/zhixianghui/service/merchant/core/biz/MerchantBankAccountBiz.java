package com.zhixianghui.service.merchant.core.biz;


import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.service.merchant.core.dao.MerchantBankAccountDao;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerCooperateDao;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 商户银行账户信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
public class MerchantBankAccountBiz {
    @Autowired
    private MerchantBankAccountDao accountDao;

    @Reference
    private MerchantEmployerQuoteFacade quoteFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private MerchantFacade merchantFacade;

    public MerchantBankAccount getByMchNo(String mchNo) {
        return accountDao.getByMchNo(mchNo);
    }

    public void update(MerchantBankAccount cooperate){
        accountDao.update(cooperate);
    }

    public void insert(MerchantBankAccount account) {
        accountDao.insert(account);
    }

    public Map<String, Object> mainstayGetAccountInfo(String mainstayNo, String mchNo) {
        Map<String,Object> maps = new HashMap<>();
        MerchantBankAccount merchantBankAccount = accountDao.getByMchNo(mchNo);
        maps.put("bankAccount",merchantBankAccount);
        return maps;
    }

    public Map<String, Object> getAccountInfo(String mchNo) {
        Map<String,Object> maps = new HashMap<>();
        MerchantBankAccount merchantBankAccount = accountDao.getByMchNo(mchNo);
        maps.put("bankAccount",merchantBankAccount);
//        List<String> employerAccountList = new ArrayList<>();
        Merchant merchant = merchantFacade.getByMchNo(mchNo);
        if (merchant.getMerchantType().intValue() == MerchantTypeEnum.EMPLOYER.getValue()){
            maps.put("businessType",merchant.getBusinessType().split(","));
        }
        // 报价单信息
        Map<String,Object> quoteMap = new HashMap<>();
        quoteMap.put("mchNo",mchNo);
        quoteMap.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
//        List<MerchantEmployerQuote> quoteList = quoteFacade.listBy(quoteMap);

//        Map<String, String> map = new HashMap<>();
//        for (MerchantEmployerQuote merchantEmployerQuote : quoteList) {
//            map.put(merchantEmployerQuote.getMainstayMchNo(), merchantEmployerQuote.getMainstayMchName());
//        }
//
//        map.forEach((mainstayNo,mainstayName)->{
//
//            List<EmployerAccountInfo> employerAccountInfos = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(mchNo, mainstayNo);
//
//            List<EmployerAccountInfo> collect = employerAccountInfos.stream().filter(it -> it.getStatus().intValue() == CommonStatusEnum.ACTIVE.getValue()).collect(Collectors.toList());
//
//            if (collect != null && !collect.isEmpty()) {
//                Map<String, Object> dataMap = new HashMap<>();
//                dataMap.put("mainstayNo", mainstayNo);
//                dataMap.put("mainstayName", mainstayName);
//                dataMap.put("employerAccountInfo", collect);
//                employerAccountList.add(JSON.toJSONString(dataMap));
//            }
//        });
//
//        maps.put("employerAccountInfo",employerAccountList);
        return maps;
    }

    public Map<String, Object> getAccountInfoOfMap(String mchNo) {
        Map<String,Object> maps = new HashMap<>();
        MerchantBankAccount merchantBankAccount = accountDao.getByMchNo(mchNo);
        maps.put("bankAccount",merchantBankAccount);

        List<Map<String, Object>> employerAccountList = new ArrayList<>();

        // 报价单信息
        List<MerchantEmployerQuote> quoteList = quoteFacade.listByMchNo(mchNo);

        Map<String, String> map = new HashMap<>();
        for (MerchantEmployerQuote merchantEmployerQuote : quoteList) {
            map.put(merchantEmployerQuote.getMainstayMchNo(), merchantEmployerQuote.getMainstayMchName());
        }
        map.forEach((mainstayNo,mainstayName)->{
            List<EmployerAccountInfo> employerAccountInfos = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(mchNo, mainstayNo);
            List<EmployerAccountInfo> collect = employerAccountInfos.stream().filter(it -> it.getStatus() == CommonStatusEnum.ACTIVE.getValue()).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("mainstayNo", mainstayNo);
                dataMap.put("mainstayName", mainstayName);
                dataMap.put("employerAccountInfo", collect);
                employerAccountList.add(dataMap);
            }
        });

        maps.put("employerAccountInfo",employerAccountList);
        return maps;
    }
}
