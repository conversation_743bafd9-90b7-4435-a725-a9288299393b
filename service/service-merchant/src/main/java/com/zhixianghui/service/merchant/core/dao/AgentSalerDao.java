package com.zhixianghui.service.merchant.core.dao;

import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.AgentCredential;
import com.zhixianghui.facade.merchant.entity.AgentSaler;
import com.zhixianghui.common.service.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.Collections;

/**
 *  Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2021-02-01
 */
@Repository
public class AgentSalerDao extends MyBatisDao<AgentSaler,Long> {
    public AgentSaler getByAgentNo(String agentNo) {
        LimitUtil.notEmpty(agentNo, "合伙人编号不能为空");
        return getOne(Collections.singletonMap("agentNo", agentNo));
    }

}
