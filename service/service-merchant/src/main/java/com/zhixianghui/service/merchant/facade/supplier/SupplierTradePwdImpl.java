package com.zhixianghui.service.merchant.facade.supplier;

import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd;
import com.zhixianghui.facade.merchant.service.supplier.SupplierTradePwdFacade;
import com.zhixianghui.service.merchant.core.biz.user.supplier.SupplierTradePwdBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 供应商支付密码表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-12-14
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SupplierTradePwdImpl implements SupplierTradePwdFacade {

    private final SupplierTradePwdBiz biz;

    /**
     * 根据商户编号查询
     *
     * @param mchNo
     */
    @Override
    public SupplierTradePwd getByMchNo(String mchNo) {
        return biz.getByMchNo(mchNo);
    }

    /**
     * 创建
     *
     * @param tradePwd
     */
    @Override
    public void create(SupplierTradePwd tradePwd) {
        biz.create(tradePwd);
    }


    /**
     * 更新
     *
     * @param tradePwd
     */
    @Override
    public void update(SupplierTradePwd tradePwd) {
        biz.update(tradePwd);
    }
}
