package com.zhixianghui.merchant;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerQuoteDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
public class TsMerchantEmployerQuote {
    @Autowired
    private MerchantEmployerQuoteDao dao;

    @Test
    public void insert(){
        MerchantEmployerQuote x = new MerchantEmployerQuote();
        x.setUpdateTime(new Date());
        x.setUpdator("");
        x.setMchNo("");
        x.setMainstayMchNo("");
        x.setMainstayMchName("");
        x.setRate(new BigDecimal(1));
        x.setVersion(0);
        x.setCreateTime(new Date());

        List<MerchantEmployerQuote> list = new ArrayList<>();
        list.add(x);
        list.add(x);
        dao.insert(list);
    }

    @Test
    public void get(){
        MerchantEmployerQuote x = dao.getById(1L);
        System.out.println(JSON.toJSONString(x));
    }
}
