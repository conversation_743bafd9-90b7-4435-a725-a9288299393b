package com.zhixianghui.merchant;

import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
public class PmsOperatorTest {

    @Autowired
    private PmsOperatorFacade pmsOperatorFacade;

    @Test
    public void testParent(){
    }

    @Test
    public void testCreate() {
        System.out.println("============================== create ==============================");
        for (int i = 0; i < 5; ++i) {
            PmsOperator operator = new PmsOperator();
            operator.setLoginName("test" + i);
            operator.setLoginPwd("test" + i);
            operator.setRemark("test" + i);
            operator.setRealName("test" + i);
            operator.setMobileNo("test" + i);
            operator.setStatus(PmsOperatorStatusEnum.ACTIVE.getValue());
            operator.setType(PmsOperatorTypeEnum.USER.getValue());
            operator.setIsChangedPwd(100);
            operator.setDepartmentId((long) i);
            pmsOperatorFacade.insertOperatorAndAssignRoles(operator, Arrays.asList(23L));
        }
    }

    @Test
    public void testList() {
        List<PmsOperator> pmsList = pmsOperatorFacade.listByDepartmentId(0);
        System.out.println(pmsList);


        System.out.println("============================== list by department id:0 ==============================");
        System.out.println(JsonUtil.toString(pmsOperatorFacade.listByDepartmentId(0)));
        System.out.println();

        System.out.println("============================== list by department id:1 ==============================");
        System.out.println(JsonUtil.toString(pmsOperatorFacade.listByDepartmentId(1)));
        System.out.println();

        System.out.println("============================== list by department recursive id:0 ==============================");
        System.out.println(JsonUtil.toString(pmsOperatorFacade.listByDepartmentIdRecursive(0)));
        System.out.println();

        System.out.println("============================== list without department ==============================");
        System.out.println(JsonUtil.toString(pmsOperatorFacade.listWithoutDepartment()));
        System.out.println();
    }

    @Test
    public void testAssign() {
        System.out.println("============================== before assign department, id:40, department id:4 ==============================");
        System.out.println(JsonUtil.toString(pmsOperatorFacade.getOperatorById(40)));

        pmsOperatorFacade.assignDepartment(40, 4);

        System.out.println("============================== after assign department, id:40, department id:4 ==============================");
        System.out.println(JsonUtil.toString(pmsOperatorFacade.getOperatorById(40)));

        System.out.println("============================== before assign department, id:41, department id:5 ==============================");
        System.out.println(JsonUtil.toString(pmsOperatorFacade.getOperatorById(41)));

        pmsOperatorFacade.assignDepartment(41, 5);

        System.out.println("============================== after assign department, id:41, department id:5 ==============================");
        System.out.println(JsonUtil.toString(pmsOperatorFacade.getOperatorById(41)));
    }
}
