package com.zhixianghui.merchant;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.user.portal.MerchantTradePwd;
import com.zhixianghui.facade.merchant.service.MerchantTradePwdFacade;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
@Log4j2
public class MerchantTradePwdTest {

    @Autowired
    private MerchantTradePwdFacade merchantTradePwdFacade;

    @Test
    public void testGetByMchNo() {
        MerchantTradePwd tradePwd = merchantTradePwdFacade.getByMchNo("M00000005");
        log.info(JsonUtil.toString(tradePwd));
    }

    @Test
    public void testUpdate() {
        MerchantTradePwd tradePwd = merchantTradePwdFacade.getByMchNo("M00000005");
        tradePwd.setPwdErrorCount(1);
        merchantTradePwdFacade.update(tradePwd);
        log.info(JsonUtil.toString(merchantTradePwdFacade.getByMchNo("M00000005")));
    }
}
