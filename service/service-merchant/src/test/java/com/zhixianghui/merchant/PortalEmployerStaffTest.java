package com.zhixianghui.merchant;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
@Log4j2
public class PortalEmployerStaffTest {

    @Autowired
    private EmployerStaffFacade employerStaffFacade;

    @Test
    public void testCreateAdmin() {
        EmployerStaffVO employerStaffVO = new EmployerStaffVO();
        employerStaffVO.setPhone("18888888888");
        employerStaffVO.setName("华农");
        employerStaffVO.setMchNo("M00000005");
        employerStaffVO.setType(PortalStaffTypeEnum.ADMIN.getValue());
        employerStaffVO.setCreator("system");
        employerStaffFacade.create(employerStaffVO);
    }

    @Test
    public void testCreateAndAssign() {
        EmployerStaffVO employerStaffVO = new EmployerStaffVO();
        employerStaffVO.setPhone("17777777777");
        employerStaffVO.setName("兄弟");
        employerStaffVO.setMchNo("M00000005");
        employerStaffVO.setType(PortalStaffTypeEnum.USER.getValue());
        employerStaffVO.setCreator("system");
        employerStaffFacade.createAndAssignRole(employerStaffVO, Arrays.asList(1L, 2L));
    }

    @Test
    public void testGetById() {
        log.info(JsonUtil.toString(employerStaffFacade.getById("M00000005", 1)));
        log.info(JsonUtil.toString(employerStaffFacade.getById("M00000005", 2)));
    }

    @Test
    public void testGetByPhone() {
        log.info(JsonUtil.toString(employerStaffFacade.getByPhone("M00000005", "11111111111")));
        log.info(JsonUtil.toString(employerStaffFacade.getByPhone("M00000005", "18888888888")));
    }

    @Test
    public void testGetAdmin() {
        log.info(JsonUtil.toString(employerStaffFacade.getAdmin("M00000005")));
        log.info(JsonUtil.toString(employerStaffFacade.getAdmin("M00000006")));
    }

    @Test
    public void testChangeAdmin() {
        employerStaffFacade.changeAdmin("M00000005", "16666666666", "aaa", "system");

        log.info(JsonUtil.toString(employerStaffFacade.getAdmin("M00000005")));
    }

    @Test
    public void testUpdateRole() {
        employerStaffFacade.updateRole("M00000005", 1, Arrays.asList(1L),"YG0000005");

        log.info(JsonUtil.toString(employerStaffFacade.getRoleByStaffId("M00000005", 1)));
        System.out.println();
        log.info(JsonUtil.toString(employerStaffFacade.getRoleByStaffId("M00000005", 2)));
        System.out.println();
        log.info(JsonUtil.toString(employerStaffFacade.getRoleByStaffId("M00000005", 4)));
    }

    @Test
    public void testDeleteById() {
        employerStaffFacade.deleteById("M00000005", 4);
    }

    @Test
    public void testListFunctionByStaffId() {
        log.info(JsonUtil.toString(employerStaffFacade.listFunctionByStaffId("M00000005", 1)));
        System.out.println();
        log.info(JsonUtil.toString(employerStaffFacade.listFunctionByStaffId("M00000005", 2)));
        System.out.println();
        log.info(JsonUtil.toString(employerStaffFacade.listFunctionByStaffId("M00000005", 4)));
    }

    @Test
    public void listPage() {
        log.info(JSON.toJSONString(employerStaffFacade.listPage(new HashMap<>(), PageParam.newInstance(1, 10))));

        System.out.println();

        Map<String, Object> map = new HashMap<>();
        map.put("type", PortalStaffTypeEnum.ADMIN.getValue());
        log.info(JsonUtil.toString(employerStaffFacade.listPage("M00000005", map, PageParam.newInstance(1, 10))));
    }
}
