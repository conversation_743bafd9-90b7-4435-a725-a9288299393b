package com.zhixianghui.merchant;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
public class PmsDepartmentTest {

    @Autowired
    private PmsDepartmentFacade pmsDepartmentFacade;

    @Test
    public void testCreate() {
//        System.out.println("============================== create ==============================");
//        for (int i = 0; i < 5; ++i) {
//            PmsDepartment department = new PmsDepartment();
//            department.setNumber("0" + i);
//            department.setParentId(0L);
//            department.setDepartmentName("部门" + i);
//            pmsDepartmentFacade.createDepartment(department);
//        }
//        System.out.println();

        System.out.println("============================== create ==============================");

        PmsDepartment test = new PmsDepartment();
        test.setNumber("10");
        test.setParentId(3L);
        test.setDepartmentName("test");
        test.setLeaderId(1L);
        pmsDepartmentFacade.createDepartment(test);
    }

    @Test
    public void testList() {
        System.out.println("============================== list ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.listDepartment()));
        System.out.println();

        System.out.println("============================== list by name: 部门1 ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.listDepartmentByName("部门1")));
        System.out.println();

        System.out.println("============================== get by id: 2 ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.getDepartmentById(2)));
        System.out.println();

        System.out.println("============================== get by number: 03 ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.getDepartmentByNumber("03")));
        System.out.println();

        System.out.println("============================== get by number: 02,03 ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.getDepartmentByNumbers(Arrays.asList("02", "03"))));
        System.out.println();

        System.out.println("============================== list without leader ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.listWithoutLeader(null)));
        System.out.println();
    }

    @Test
    public void testUpdate() {
        PmsDepartment department = pmsDepartmentFacade.getDepartmentById(1);
        System.out.println("============================== before update id: 1 ==============================");
        System.out.println(JsonUtil.toString(department));
        System.out.println();

        department.setDepartmentName("销售部");
        department.setNumber("0000");
        department.setLeaderId(39L);
        pmsDepartmentFacade.updateDepartment(department);

        System.out.println("============================== after update id: 1 ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.getDepartmentById(1)));
        System.out.println();


        System.out.println("============================== before update id: 2 ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.getDepartmentById(2)));
        System.out.println();

        pmsDepartmentFacade.assignLeader(2, 40);

        System.out.println("============================== after update id: 2 ==============================");
        System.out.println(JsonUtil.toString(pmsDepartmentFacade.getDepartmentById(2)));
        System.out.println();
    }

    @Test
    public void testDeleteDepartmentById() {
        pmsDepartmentFacade.deleteDepartmentById(6);
        System.out.println("============================== after delete id: 3 ==============================");
        System.out.println(pmsDepartmentFacade.getDepartmentById(6));
    }
}
