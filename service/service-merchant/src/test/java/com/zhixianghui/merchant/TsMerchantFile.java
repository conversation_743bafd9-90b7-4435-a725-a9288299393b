package com.zhixianghui.merchant;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import com.zhixianghui.service.merchant.core.biz.MerchantFileBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
public class TsMerchantFile {
    @Autowired
    private MerchantFileBiz biz;

    @Test
    public void insert(){
        MerchantFile file = new MerchantFile();
        file.setUpdator("");
        file.setMchNo("8888");
        file.setMchName("");
        file.setFileUrl("/a/b/c.jpg");
        file.setFileType(0);
        file.setVersion(0);
        file.setCreateTime(new Date());

        biz.insert(file);
    }

    @Test
    public void get(){
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("mchNo", "8888");
        List<MerchantFile> x = biz.listBy(paramMap);
        System.out.println(JSON.toJSONString(x));
    }
}
