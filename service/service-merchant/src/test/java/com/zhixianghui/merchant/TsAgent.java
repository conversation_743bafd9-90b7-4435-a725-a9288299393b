package com.zhixianghui.merchant;

import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.vo.agent.AgentVo;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import com.zhixianghui.service.merchant.core.biz.AgentManagerBiz;
import com.zhixianghui.service.merchant.core.dao.AgentDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/22 16:54
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceMerchantApp.class)
public class TsAgent {
    @Autowired
    private AgentManagerBiz agentManagerBiz;
    @Autowired
    private AgentDao agentDao;

    @Test
    public void changeStatus(){
        Agent agent = agentDao.getByAgentNo("A000015");
        agent.setAgentStatus(AgentStatusEnum.RETREAT.getValue());
        agentManagerBiz.changeStatus(agent);

    }

    @Test
    public void testTransaction(){
        Agent agent = agentDao.getByAgentNo("A000015");
        AgentVo agentVo = new AgentVo();
        BeanUtil.copyProperties(agent, agentVo);
        agentManagerBiz.updateAgentDetail(agentVo);
    }
}
