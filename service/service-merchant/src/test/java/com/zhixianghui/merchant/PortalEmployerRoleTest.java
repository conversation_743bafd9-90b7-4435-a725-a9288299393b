package com.zhixianghui.merchant;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.service.employer.EmployerRoleFacade;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;

@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
@Log4j2
public class PortalEmployerRoleTest {

    @Autowired
    private EmployerRoleFacade employerRoleFacade;

    @Test
    public void testCreate() {
        EmployerRole role = new EmployerRole();
        role.setMchNo("M00000005");
        role.setName("角色1");
        role.setRemark("角色1");
        role.setCreateTime(new Date());
        employerRoleFacade.create(role);

        EmployerRole role1 = new EmployerRole();
        role1.setMchNo("M00000005");
        role1.setName("角色2");
        role1.setRemark("角色2");
        role1.setCreateTime(new Date());
        employerRoleFacade.create(role1);
    }

    @Test
    public void testGetById() {
        log.info(JsonUtil.toString(employerRoleFacade.getById("M00000005", 1)));
        System.out.println();
        log.info(JsonUtil.toString(employerRoleFacade.getById("M00000005", 2)));
    }

    @Test
    public void testUpdateFunction() {
        employerRoleFacade.updateFunction("M00000005", 1, Arrays.asList(1L));
        System.out.println();
        employerRoleFacade.updateFunction("M00000005", 2, Arrays.asList(2L));
    }

    @Test
    public void testListFunctionByRoleId() {
        log.info(JsonUtil.toString(employerRoleFacade.listFunctionByRoleId("M00000005", 1)));
        System.out.println();
        log.info(JsonUtil.toString(employerRoleFacade.listFunctionByRoleId("M00000005", 2)));
    }

    @Test
    public void testListStaffByRoleId() {
        log.info(JsonUtil.toString(employerRoleFacade.listStaffByRoleId("M00000005", 1)));
        System.out.println();
        log.info(JsonUtil.toString(employerRoleFacade.listStaffByRoleId("M00000005", 2)));
    }

    @Test
    public void testListAll() {
        log.info(JsonUtil.toString(employerRoleFacade.listAll("M00000005")));
    }

    @Test
    public void testListPage() {
        log.info(JsonUtil.toString(employerRoleFacade.listPage("M00000005", new HashMap<>(), PageParam.newInstance(1, 10))));
    }
}
