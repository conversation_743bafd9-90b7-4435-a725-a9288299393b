package com.zhixianghui.merchant;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerPositionDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
public class TsMerchantEmployerPosition {
    @Autowired
    private MerchantEmployerPositionDao dao;

    @Test
    public void insert(){
        MerchantEmployerPosition x = new MerchantEmployerPosition();
        x.setUpdateTime(new Date());
        x.setUpdator("");
        x.setMchNo("");
        x.setWorkplaceCode("0");
        x.setWorkCategoryCode("0");
        x.setWorkCategoryName("");
        x.setServiceDesc("");
        x.setChargeRuleDesc("");
        x.setVersion(0);
        x.setCreateTime(new Date());

        dao.insert(x);
    }

    @Test
    public void get(){
        MerchantEmployerPosition x = dao.getById(1L);
        System.out.println(JSON.toJSONString(x));
    }
}
