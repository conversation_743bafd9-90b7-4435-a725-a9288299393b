package com.zhixianghui.merchant;

import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.service.MerchantMainstayFacade;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceMerchantApp.class)
public class TsMerchantMainstay {

    @Autowired
    private MerchantMainstayFacade merchantMainstayFacade;

    @Test
    public void testCreate() {
        Merchant merchant = new Merchant();
        merchant.setVersion(0);
        merchant.setCreateTime(new Date());
        merchant.setUpdateTime(new Date());
        merchant.setUpdator("");
        merchant.setMchName("测试测试");
        merchant.setMchStatus(MchStatusEnum.CREATE.getValue());
        merchant.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        merchant.setRemark("111111");
        merchant.setJsonInfo("{}");


        MerchantSaler saler = new MerchantSaler();
        saler.setVersion(0);
        saler.setCreateTime(new Date());
        saler.setUpdateTime(new Date());
        saler.setUpdator("");
        saler.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        saler.setSalerId(37L);
        saler.setSalerName("chenyufeng");
        saler.setSaleDepartmentId(2L);
        saler.setSaleDepartmentName("销售部");

//        merchantMainstayFacade.createMainstay(merchant, detail, sales);
    }
}
