package com.zhixianghui.merchant;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.AgreementTemplate;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import com.zhixianghui.service.merchant.core.dao.AgreementDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-07 18:15
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceMerchantApp.class)
public class TsAgreement {
    @Autowired
    private AgreementDao dao;

    @Test
    public void listBy(){
        Map<String, Object> paramMap = Maps.newHashMap();
        List<String> signerNoList = Lists.newArrayList();
        signerNoList.add("abc");
        signerNoList.add("fff");
        paramMap.put("signerNoList",signerNoList);
        List<Agreement> list = dao.listBy(paramMap);
        System.out.println(list);
    }

    @Test
    public void expireAgreement(){
        dao.expireAgreement();
    }
}
