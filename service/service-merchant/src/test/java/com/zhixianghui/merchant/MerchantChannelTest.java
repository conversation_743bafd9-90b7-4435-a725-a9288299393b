package com.zhixianghui.merchant;

import com.zhixianghui.common.statics.dto.common.RecordItemVo;
import com.zhixianghui.common.statics.enums.report.MerchantChannelTypeEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantChannel;
import com.zhixianghui.facade.merchant.service.MerchantChannelFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.service.merchant.ServiceMerchantApp;

import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Date 2021/4/21 14:33
 */

@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
public class MerchantChannelTest {

    @Reference
    private MerchantChannelFacade merchantChannelFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private RecordItemFacade recordItemFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Autowired
    private RestTemplate restTemplate;

    @Test
    public void testIp() {
//        restTemplate.post
    }
    @Test
    public void testChannel() {
        // 获取代征主体商户号
        MerchantChannel merchantChannel = merchantChannelFacade.getByMchNo("S000003");
        if (merchantChannel.getChannelType() != MerchantChannelTypeEnum.INTERFACE.getType()) {
            return;
        }
        Merchant merchant = merchantFacade.getByMchNo("*********");
        try {
            merchantChannelFacade.handle(merchantChannel, merchant);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSynchronize() {
        OrderItem orderItem = orderItemFacade.getByPlatTrxNo("E20210409001824376");
        RecordItem recordItem = recordItemFacade.getByChannelTrxNo("20210409110070001506370062480291");
        recordItem.setBankName(orderItem.getBankName());
        recordItem.setBankCode(orderItem.getBankCode());
        recordItem.setEncryptKeyId(orderItem.getEncryptKeyId());
        recordItem.setReceiveName(orderItem.getReceiveName());
        recordItem.setReceiveIdCardNo(orderItem.getReceiveIdCardNo());
        recordItem.setReceivePhoneNo(orderItem.getReceivePhoneNo());
        RecordItemVo recordItemVo = new RecordItemVo();
        BeanUtil.copyProperties(recordItem, recordItemVo);
        merchantChannelFacade.synchronizeRecordItem(recordItemVo);
    }
}
