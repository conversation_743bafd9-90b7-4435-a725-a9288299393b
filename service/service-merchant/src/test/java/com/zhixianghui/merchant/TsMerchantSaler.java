package com.zhixianghui.merchant;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import com.zhixianghui.service.merchant.core.dao.MerchantSalerDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2020/8/6
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceMerchantApp.class)
public class TsMerchantSaler {

    @Autowired
    MerchantSalerDao dao;

    @Test
    public void tsInsert(){
        MerchantSaler s = new MerchantSaler();
        s.setUpdateTime(new Date());
        s.setUpdator("");
        s.setMchNo(System.currentTimeMillis()+"");
        s.setMerchantType(0);
        s.setSalerId(0L);
        s.setSalerName("");
        s.setSaleDepartmentId(0L);
        s.setSaleDepartmentName("");
        s.setRemark("");
        s.setJsonInfo("{}");
        s.setId(0L);
        s.setVersion(0);
        s.setCreateTime(new Date());
        dao.insert(s);

    }

    @Test
    public void get(){
        PageResult x = dao.listExtObjectPage(new HashMap<>(), PageParam.newInstance(1,10));
        System.out.println(JsonUtil.toString(x));
    }
}
