package com.zhixianghui.merchant;
import java.math.BigDecimal;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.ProvideIncomeDetailTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.SignRateLevelEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.vo.*;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import com.zhixianghui.service.merchant.core.biz.MerchantBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerBiz;
import com.zhixianghui.service.merchant.core.biz.MerchantEmployerMainBiz;
import com.zhixianghui.service.merchant.core.dao.MerchantDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/8/6
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceMerchantApp.class)
public class TsMerchant {

    @Autowired
    MerchantDao merchantDao;
    @Autowired
    MerchantBiz merchantBiz;
    @Autowired
    MerchantEmployerBiz employerBiz;
    @Autowired
    MerchantEmployerBiz biz;

    @Test
    public void tsListPage(){
        String msg = "{\"mchNo\": \"M00000026\", \"taxNo\": \"1111\", \"bankName\": \"111111\", \"accountNo\": \"1111111\", \"shortName\": \"啦\", \"contactEmail\": \"<EMAIL>\", \"servicePhone\": \"***********\", \"bankChannelNo\": \"111111\", \"registerAmount\": 10000, \"certificateType\": 100, \"legalPersonName\": \"啦啦啦\", \"managementScope\": \"啦啦啦啦啦\", \"doorPhotoFileUrl\": \"hjzx/M00/00/03/CgoKa19qti-ABVbwAAFjqA-U4E4163.jpg\", \"receptionFileUrl\": \"hjzx/M00/00/03/CgoKa19qtjaAXI5vAAFjqA-U4E4064.jpg\", \"registerAddrCity\": \"150100\", \"registerAddrTown\": \"150101\", \"certificateNumber\": \"111111\", \"idCardHeadFileUrl\": \"hjzx/M00/00/03/CgoKa19qth2AJF1bAAFjqA-U4E4458.jpg\", \"managementTermEnd\": \"2020-09-11\", \"operatorLoginName\": \"啦啦啦\", \"workIndoorFileUrl\": \"hjzx/M00/00/03/CgoKa19qtjKAfoDBAAFjqA-U4E4129.jpg\", \"certificateTermEnd\": \"2020-09-11\", \"managementAddrCity\": \"150100\", \"managementAddrTown\": \"150101\", \"registerAddrDetail\": \"啦啦啦阿里\", \"idCardEmblemFileUrl\": \"hjzx/M00/00/03/CgoKa19qtiGARQ9dAAFjqA-U4E4910.jpg\", \"managementTermBegin\": \"2020-09-01\", \"certificateTermBegin\": \"2020-09-08\", \"managementAddrDetail\": \"啦啦啦啦啦\", \"registerAddrProvince\": \"150000\", \"businessLicenseFileUrl\": \"hjzx/M00/00/03/CgoKa19qthCAb-gkAAFjqA-U4E4342.jpg\", \"managementAddrProvince\": \"150000\", \"managementValidityDateType\": 1, \"certificateValidityDateType\": 1}";
        MerchantEmployerMainAuthVo vo = JsonUtil.toBean(msg, MerchantEmployerMainAuthVo.class);
        employerBiz.employerMainAuth(vo);
    }

    @Test
    public void tsInsert(){
        Merchant info = new Merchant();
        info.setUpdateTime(new Date());
        info.setUpdator("");
        info.setMchName("卖豆腐的");
        info.setMchNo("1234");
        info.setMchStatus(MchStatusEnum.ACTIVE.getValue());
        info.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        info.setRemark("");
        info.setJsonInfo("{}");
        info.setVersion(0);
        info.setCreateTime(new Date());

        merchantDao.insert(info);
    }

    @Test
    public void tsInsertEmployer(){
        MerchantEmployerInsertVo vo = new MerchantEmployerInsertVo();
        vo.setSalerId(49L);
        vo.setMchName("123");
        vo.setRemark("");
        vo.setWorkerNum(10);
        vo.setSignRateLevel(SignRateLevelEnum.LEVEL_1.getValue());
        vo.setWorkerMonthIncomeRate(new BigDecimal("0"));
        vo.setMonthMoneySlip(new BigDecimal("0"));
        vo.setProvideIncomeDetailType(ProvideIncomeDetailTypeEnum.PROVIDE.getValue());
        vo.setCompanyWebsite("www.baidu.com");
        vo.setBizPlatformName("汇聚");
        vo.setContactPhone("*********");
        vo.setContactName("啊啊");

        MerchantPositionVo positionVo = new MerchantPositionVo();
        positionVo.setWorkplaceCode("");
        positionVo.setWorkCategoryCode("");
        positionVo.setWorkCategoryName("");
        positionVo.setServiceDesc("发发发");
        positionVo.setChargeRuleDesc("发发发");
        vo.setPositionVoList(Lists.newArrayList(positionVo));

        MerchantEmployerQuoteVo quoteVo = new MerchantEmployerQuoteVo();
        quoteVo.setMainstayMchNo("122121");
        quoteVo.setMainstayMchName("南沙代征点");
        quoteVo.setRate(new BigDecimal(5));
        vo.setQuoteVoList(Lists.newArrayList(quoteVo));

        biz.createMerchant(vo);
    }

    @Test
    public void get(){
        Merchant info = merchantDao.getByMchNo("M00000089");
        System.out.println(JSON.toJSONString(info));
//        info.setMchName("卖彩票的");
//        merchantDao.update(info);


    }

    @Test
    public void getRelevantAgentByMchNo(){
        String mchNo = "M00000051";
        RelevantAgent relevantAgent = merchantBiz.getRelevantAgentByMchNo(mchNo);
        System.out.println(relevantAgent);
    }
}
