package com.zhixianghui.merchant;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerOperator;
import com.zhixianghui.facade.merchant.service.employer.EmployerOperatorFacade;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;

@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
@Log4j2
public class PortalEmployerOperatorTest {

    @Autowired
    private EmployerOperatorFacade employerOperatorFacade;

    @Test
    public void testGetById() {
        log.info(JsonUtil.toString(employerOperatorFacade.getById(2)));
        System.out.println();
        log.info(JsonUtil.toString(employerOperatorFacade.getById(3)));
        System.out.println();
        log.info(JsonUtil.toString(employerOperatorFacade.getById(4)));
    }

    @Test
    public void testGetByPhone() {
        log.info(JsonUtil.toString(employerOperatorFacade.getByPhone("18888888888")));
        System.out.println();
        log.info(JsonUtil.toString(employerOperatorFacade.getByPhone("17777777777")));
        System.out.println();
        log.info(JsonUtil.toString(employerOperatorFacade.getByPhone("16666666666")));
        System.out.println();
        log.info(JsonUtil.toString(employerOperatorFacade.getByPhone("15555555555")));
    }

    @Test
    public void testUpdate() {
        EmployerOperator operator = employerOperatorFacade.getById(4);
        operator.setName("66666666661");
        employerOperatorFacade.update(operator);
        log.info(JsonUtil.toString(employerOperatorFacade.getById(4)));
    }

    @Test
    public void testListPage() {
        log.info(JsonUtil.toString(employerOperatorFacade.listPage(new HashMap<>(), PageParam.newInstance(1, 10))));
    }
}
