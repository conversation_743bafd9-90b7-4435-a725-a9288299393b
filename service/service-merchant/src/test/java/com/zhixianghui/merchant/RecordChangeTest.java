package com.zhixianghui.merchant;

import com.zhixianghui.facade.merchant.enums.ChangeSourceEnum;
import com.zhixianghui.facade.merchant.service.MerchantInfoChangeRecordFacade;
import com.zhixianghui.facade.merchant.vo.record.MerchantBaseInfoVo;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/7/26 10:38
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceMerchantApp.class)
public class RecordChangeTest {

    @Autowired
    MerchantInfoChangeRecordFacade recordFacade;

    @Test
    public void testMerchantChange() {
        MerchantBaseInfoVo oldInfo = new MerchantBaseInfoVo();
        oldInfo.setLoginName("德玛西亚");
        oldInfo.setLoginId(222L);
        oldInfo.setMchNo("M0000000008");
        oldInfo.setMchName("你忍一下");
//        oldInfo.setFileUrl(Arrays.asList("aaa.com", "bbb.com"));

        MerchantBaseInfoVo newInfo = new MerchantBaseInfoVo();
        newInfo.setLoginName("德玛西亚");
        newInfo.setLoginId(222L);
        newInfo.setMchNo("M0000000001");
        newInfo.setMchName("你忍一下");
//        newInfo.setFileUrl(Arrays.asList("aaa.com", "ccc.com"));
        newInfo.setFlowId(1131L);

        recordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
    }
}
