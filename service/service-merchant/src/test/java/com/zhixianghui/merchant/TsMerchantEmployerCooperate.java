package com.zhixianghui.merchant;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.enums.merchant.SignRateLevelEnum;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import com.zhixianghui.service.merchant.core.dao.MerchantEmployerCooperateDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
public class TsMerchantEmployerCooperate {
    @Autowired
    private MerchantEmployerCooperateDao detailDao;

    @Test
    public void insert(){
        MerchantEmployerCooperate detail = new MerchantEmployerCooperate();
        detail.setUpdateTime(new Date());
        detail.setUpdator("");
        detail.setMchNo("");
        detail.setWorkerNum(0);
        detail.setSignRateLevel(SignRateLevelEnum.LEVEL_1.getValue());
        detail.setWorkerMonthIncomeRate(new BigDecimal(1));
        detail.setMonthMoneySlip(new BigDecimal(1));
        detail.setProvideIncomeDetailType(0);
        detail.setCompanyWebsite("");
        detail.setBizPlatformName("");
        detail.setJsonInfo("{}");
        detail.setVersion(0);
        detail.setCreateTime(new Date());

        detailDao.insert(detail);
    }

    @Test
    public void get(){
        MerchantEmployerCooperate detail = detailDao.getById(1L);
        System.out.println(JSON.toJSONString(detail));
    }
}
