package com.zhixianghui.merchant;

import com.zhixianghui.common.statics.enums.user.portal.PortalFunctionTypeEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.service.employer.EmployerFunctionFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerRoleFacade;
import com.zhixianghui.service.merchant.ServiceMerchantApp;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(classes = ServiceMerchantApp.class)
@RunWith(SpringRunner.class)
@Log4j2
public class PortalEmployerFunctionTest {

    @Autowired
    private EmployerFunctionFacade employerFunctionFacade;

    @Autowired
    private EmployerRoleFacade employerRoleFacade;

    @Test
    public void create() {
        EmployerFunction function1 = new EmployerFunction();
        function1.setCreateTime(new Date());
        function1.setType(PortalFunctionTypeEnum.MENU_TYPE.getValue());
        function1.setName("菜单1");
        function1.setNumber("01");
        function1.setPermissionFlag("function1");
        function1.setUrl("function1");
        employerFunctionFacade.create(function1);

        EmployerFunction function2 = new EmployerFunction();
        function2.setCreateTime(new Date());
        function2.setType(PortalFunctionTypeEnum.MENU_TYPE.getValue());
        function2.setName("菜单2");
        function2.setNumber("02");
        function2.setPermissionFlag("function2");
        function2.setUrl("function2");
        employerFunctionFacade.create(function2);
    }

    @Test
    public void testGetById() {
        log.info(JsonUtil.toString(employerFunctionFacade.getById(1)));
        System.out.println();
        log.info(JsonUtil.toString(employerFunctionFacade.getById(2)));
        System.out.println();
        log.info(JsonUtil.toString(employerFunctionFacade.getById(3)));
    }

    @Test
    public void testUpdate() {
        EmployerFunction function = employerFunctionFacade.getById(1);
        function.setName("菜单111");
        employerFunctionFacade.update(function);

        log.info(JsonUtil.toString(employerFunctionFacade.getById(1)));
    }

    @Test
    public void testDeleteById() {
        EmployerFunction function = new EmployerFunction();
        function.setCreateTime(new Date());
        function.setName("菜单3");
        function.setUrl("333");
        function.setPermissionFlag("function3");
        function.setNumber("03");
        function.setType(PortalFunctionTypeEnum.MENU_TYPE.getValue());
        employerFunctionFacade.create(function);

        long roleId = 1L;
        List<EmployerFunction> functions = employerRoleFacade.listFunctionByRoleId("M00000005", roleId);
        List<Long> functionIds = functions.stream().map(EmployerFunction::getId).collect(Collectors.toList());
        log.info("before update, functionId: {}", JsonUtil.toString(functionIds));

        functionIds.add(function.getId());
        employerRoleFacade.updateFunction("M00000005", roleId, functionIds);
        log.info("after update, functionId: {}", JsonUtil.toString(employerRoleFacade.listFunctionByRoleId("M00000005", roleId).
                stream().map(EmployerFunction::getId).collect(Collectors.toList())));

        employerFunctionFacade.deleteById(function.getId());
        log.info("after delete, functionId: {}", JsonUtil.toString(employerRoleFacade.listFunctionByRoleId("M00000005", roleId).
                stream().map(EmployerFunction::getId).collect(Collectors.toList())));
    }

    @Test
    public void testListAll() {
        log.info(JsonUtil.toString(employerFunctionFacade.listAll()));
    }
}
