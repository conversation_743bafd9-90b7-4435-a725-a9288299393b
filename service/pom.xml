<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-all</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>

    <artifactId>zhixianghui-service</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>service-account-invoice</module>
        <module>service-banklink</module>
        <module>service-common</module>
        <module>service-export</module>
        <module>service-timer</module>
        <module>service-risk-control</module>
        <module>service-trade</module>
        <module>service-fee</module>
        <module>service-merchant</module>
        <module>service-notify</module>
        <module>service-flow</module>
        <module>service-data</module>
        <module>service-employee</module>
        <module>service-pay</module>
    </modules>

</project>
