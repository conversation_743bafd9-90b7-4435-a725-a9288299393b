<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-service</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>service-common</artifactId>
    <description>基础接口服务</description>

    <build>
        <finalName>${appName}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!--spring begin-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- 把nacos作为配置中心时需要引入的依赖 -->
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-tools</artifactId>
            <version>${rocketmq.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--spring end-->

        <!-- facade begin -->
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-common</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-notify</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-merchant</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-employee</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-banklink</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-account-invoice</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-trade</artifactId>

        </dependency>
        <!-- facade end -->

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>leaf-core</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>component-starter</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-service</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-fee</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-export</artifactId>
            <version>1.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>
</project>
