spring.application.name=service-common
spring.cloud.nacos.config.server-addr=@nacosAddr@
spring.cloud.nacos.config.namespace=@nacosNamespace@
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.discovery.namespace=@nacosNamespace@

spring.cloud.nacos.config.shared-dataids=dubbo.properties,db.properties,redis.properties,rocketmq.properties
spring.cloud.nacos.config.refreshable-dataids=dubbo.properties,db.properties,redis.properties,rocketmq.properties


logging.level.com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder=info

