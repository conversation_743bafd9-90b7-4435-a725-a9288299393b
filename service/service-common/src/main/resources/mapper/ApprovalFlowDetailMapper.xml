<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail">
    <sql id="table">tbl_approval_flow_detail</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="APPROVAL_FLOW_ID" property="approvalFlowId" jdbcType="BIGINT"/>
        <result column="HANDLER_ID" property="handlerId" jdbcType="BIGINT"/>
        <result column="HANDLER_NAME" property="handlerName" jdbcType="VARCHAR"/>
        <result column="HANDLER_TYPE" property="handlerType" jdbcType="SMALLINT"/>
        <result column="STEP_NUM" property="stepNum" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"/>
        <result column="EXT_INFO" property="extInfo" jdbcType="OTHER"/>
        <result column="PLATFORM" property="platform" jdbcType="SMALLINT"/>
        <result column="IS_HISTORY" property="isHistory" jdbcType="BIT"/>
        <result column="APPROVAL_OPINION" property="approvalOpinion" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, APPROVAL_FLOW_ID, HANDLER_ID, HANDLER_NAME, HANDLER_TYPE, STEP_NUM, STATUS, OPERATOR_NAME, EXT_INFO, PLATFORM, IS_HISTORY,APPROVAL_OPINION
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            APPROVAL_FLOW_ID,
            HANDLER_ID,
            HANDLER_NAME,
            HANDLER_TYPE,
            STEP_NUM,
            STATUS,
            OPERATOR_NAME,
            EXT_INFO,
            PLATFORM,
            IS_HISTORY,
            APPROVAL_OPINION
        ) VALUES (
        #{version,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{approvalFlowId,jdbcType=BIGINT},
        #{handlerId,jdbcType=BIGINT},
        #{handlerName,jdbcType=VARCHAR},
        #{handlerType,jdbcType=SMALLINT},
        #{stepNum,jdbcType=INTEGER},
        #{status,jdbcType=SMALLINT},
        #{operatorName,jdbcType=VARCHAR},
        #{extInfo,jdbcType=OTHER},
        #{platform,jdbcType=SMALLINT},
        #{isHistory,jdbcType=BIT},
        #{approvalOpinion,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            APPROVAL_FLOW_ID,
            HANDLER_ID,
            HANDLER_NAME,
            HANDLER_TYPE,
            STEP_NUM,
            STATUS,
            OPERATOR_NAME,
            EXT_INFO,
            PLATFORM,
            IS_HISTORY,
            APPROVAL_OPINION
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.approvalFlowId,jdbcType=BIGINT},
            #{item.handlerId,jdbcType=BIGINT},
            #{item.handlerName,jdbcType=VARCHAR},
            #{item.handlerType,jdbcType=SMALLINT},
            #{item.stepNum,jdbcType=INTEGER},
            #{item.status,jdbcType=SMALLINT},
            #{item.operatorName,jdbcType=VARCHAR},
            #{item.extInfo,jdbcType=OTHER},
            #{item.platform,jdbcType=SMALLINT},
            #{item.isHistory,jdbcType=BIT},
            #{item.approvalOpinion,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=INTEGER} +1,
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                APPROVAL_FLOW_ID = #{approvalFlowId,jdbcType=BIGINT},
                HANDLER_ID = #{handlerId,jdbcType=BIGINT},
                HANDLER_NAME = #{handlerName,jdbcType=VARCHAR},
                HANDLER_TYPE = #{handlerType,jdbcType=SMALLINT},
                STEP_NUM = #{stepNum,jdbcType=INTEGER},
                STATUS = #{status,jdbcType=SMALLINT},
                OPERATOR_NAME = #{operatorName,jdbcType=VARCHAR},
                EXT_INFO = #{extInfo,jdbcType=OTHER},
                PLATFORM = #{platform,jdbcType=SMALLINT},
                IS_HISTORY = #{isHistory,jdbcType=BIT},
                APPROVAL_OPINION = #{approvalOpinion,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                    VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="approvalFlowId != null">
                    APPROVAL_FLOW_ID = #{approvalFlowId,jdbcType=BIGINT},
            </if>
            <if test="handlerId != null">
                    HANDLER_ID = #{handlerId,jdbcType=BIGINT},
            </if>
            <if test="handlerName != null">
                    HANDLER_NAME = #{handlerName,jdbcType=VARCHAR},
            </if>
            <if test="handlerType != null">
                    HANDLER_TYPE = #{handlerType,jdbcType=SMALLINT},
            </if>
            <if test="stepNum != null">
                    STEP_NUM = #{stepNum,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                    STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="operatorName != null">
                    OPERATOR_NAME = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="extInfo != null">
                    EXT_INFO = #{extInfo,jdbcType=OTHER},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=SMALLINT}
            </if>
            <if test="isHistory != null">
                IS_HISTORY = #{isHistory,jdbcType=BIT}
            </if>
            <if test="approvalOpinion != null">
                APPROVAL_OPINION = #{approvalOpinion,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="listPageCount" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=INTEGER}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="approvalFlowId != null">
                and APPROVAL_FLOW_ID = #{approvalFlowId,jdbcType=BIGINT}
            </if>
            <if test="handlerId != null">
                and HANDLER_ID = #{handlerId,jdbcType=BIGINT}
            </if>
            <if test="handlerName != null and handlerName !=''">
                and HANDLER_NAME = #{handlerName,jdbcType=VARCHAR}
            </if>
            <if test="handlerType != null">
                and HANDLER_TYPE = #{handlerType,jdbcType=SMALLINT}
            </if>
            <if test="stepNum != null">
                and STEP_NUM = #{stepNum,jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and STATUS = #{status,jdbcType=SMALLINT}
            </if>
            <if test="operatorName != null and operatorName !=''">
                and OPERATOR_NAME = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="extInfo != null and extInfo !=''">
                and EXT_INFO = #{extInfo,jdbcType=OTHER}
            </if>
            <if test="platform != null">
                and PLATFORM = #{platform,jdbcType=SMALLINT}
            </if>
            <if test="isHistory != null">
                and IS_HISTORY = #{isHistory,jdbcType=BIT}
            </if>
            <if test="approvalOpinion != null and approvalOpinion !=''">
                and APPROVAL_OPINION = #{approvalOpinion,jdbcType=VARCHAR}
            </if>
    </sql>

    <update id="cancelApprovalDetail" parameterType="long">
        UPDATE <include refid="table" />
        SET VERSION=VERSION+1,IS_HISTORY=TRUE
        WHERE APPROVAL_FLOW_ID = #{approvalFlowId} AND IS_HISTORY=FALSE
    </update>

</mapper>
