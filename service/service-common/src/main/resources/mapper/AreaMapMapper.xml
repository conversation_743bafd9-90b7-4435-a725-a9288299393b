<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.AreaMapMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.AreaMap">
    <!--@mbg.generated-->
    <!--@Table tbl_area_map-->
    <id column="ZXH_CODE" jdbcType="VARCHAR" property="zxhCode" />
    <result column="YISHUI_CODE" jdbcType="VARCHAR" property="yishuiCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ZXH_CODE, YISHUI_CODE
  </sql>
</mapper>
