<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.common.entity.config.WorkCategory">
	<sql id="table"> tbl_work_category </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.WorkCategory">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="WORK_CATEGORY_CODE" property="workCategoryCode" jdbcType="VARCHAR"/>
		<result column="WORK_CATEGORY_NAME" property="workCategoryName" jdbcType="VARCHAR"/>
		<result column="PARENT_ID" property="parentId" jdbcType="BIGINT"/>
		<result column="EXT_JSON" property="extJson" jdbcType="VARCHAR"/>
		<result column="WORK_DESC" property="workDesc" jdbcType="VARCHAR"/>
		<result column="BUSINESS_DESC" property="businessDesc" jdbcType="VARCHAR"/>
		<result column="CHARGE_RULE_DESC" property="chargeRuleDesc" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		VERSION,
		UPDATOR,
		UPDATE_TIME,
		WORK_CATEGORY_CODE,
		WORK_CATEGORY_NAME,
		PARENT_ID,
		EXT_JSON,
		WORK_DESC,
		BUSINESS_DESC,
		CHARGE_RULE_DESC
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.config.WorkCategory">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	UPDATE_TIME ,
        	WORK_CATEGORY_CODE ,
        	WORK_CATEGORY_NAME ,
        	PARENT_ID ,
			BUSINESS_DESC ,
        	EXT_JSON ,
        	WORK_DESC ,
        	CHARGE_RULE_DESC 
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			0,
			#{updator,jdbcType=VARCHAR},
			#{updateTime,jdbcType=TIMESTAMP},
			#{workCategoryCode,jdbcType=VARCHAR},
			#{workCategoryName,jdbcType=VARCHAR},
			#{parentId,jdbcType=BIGINT},
		    #{businessDesc,jdbcType=VARCHAR},
			#{extJson,jdbcType=VARCHAR},
			#{workDesc,jdbcType=VARCHAR},
			#{chargeRuleDesc,jdbcType=VARCHAR}
        )
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.common.entity.config.WorkCategory">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR},
			WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR},
			PARENT_ID = #{parentId,jdbcType=BIGINT},
			EXT_JSON = #{extJson,jdbcType=VARCHAR},
			WORK_DESC = #{workDesc,jdbcType=VARCHAR},
			CHARGE_RULE_DESC = #{chargeRuleDesc,jdbcType=VARCHAR},
			BUSINESS_DESC = #{businessDesc,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="workCategoryCode != null and workCategoryCode !=''">
			and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
		</if>
		<if test="workCategoryName != null and workCategoryName !=''">
			and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
		</if>
		<if test="parentId != null">
			and PARENT_ID = #{parentId,jdbcType=BIGINT}
		</if>
		<if test="workDesc != null and workDesc !=''">
			and WORK_DESC = #{workDesc,jdbcType=VARCHAR}
		</if>
		<if test="chargeRuleDesc != null and chargeRuleDesc !=''">
			and CHARGE_RULE_DESC = #{chargeRuleDesc,jdbcType=VARCHAR}
		</if>
		<if test="businessDesc != null and businessDesc !=''">
			and BUSINESS_DESC = #{businessDesc,jdbcType=VARCHAR}
		</if>
	</sql>
</mapper>

