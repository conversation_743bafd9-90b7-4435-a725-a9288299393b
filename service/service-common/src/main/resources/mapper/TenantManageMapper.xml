<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.TenantManageMapper">
    <sql id="table">tbl_tenant_manage</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.tenant.TenantManage">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="tenant_no" property="tenantNo" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="web_site_name" property="webSiteName" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="updator" property="updator" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, tenant_no, tenant_name,web_site_name, start_time, end_time, status, create_time, creator, update_time, updator
    </sql>

    <select id="selectByWebsite" resultType="java.util.Map">
        select m.id,m.tenant_no as tenantNo,m.web_site_name as webSiteName,w.icp_no as icpNo from tbl_tenant_manage m left join tbl_tenant_website w on m.id = w.tenant_id
        where w.website = #{uri} and m.status = #{status} limit 1
    </select>

</mapper>
