<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.report.PayChannel">
    <sql id="table">tbl_pay_channel</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.report.PayChannel">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_NAME" property="payChannelName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_OPERATOR" property="updateOperator" jdbcType="VARCHAR"/>
        <result column="CREATE_OPERATOR" property="createOperator" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR"/>
        <result column="BANK_NO" property="bankNo" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="SUB_BANK_NAME" property="subBankName" jdbcType="VARCHAR"/>
        <result column="JOIN_BANK_NO" property="joinBankNo" jdbcType="VARCHAR"/>
        <result column="BANK_ADDRESS" property="bankAddress" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, PAY_CHANNEL_NO, PAY_CHANNEL_NAME, CHANNEL_TYPE, CREATE_TIME, UPDATE_TIME, UPDATE_OPERATOR, CREATE_OPERATOR, STATUS, DESCRIPTION,
        ACCOUNT_NAME,ACCOUNT_NO,BANK_NO,BANK_NAME,SUB_BANK_NAME,JOIN_BANK_NO,BANK_ADDRESS
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.report.PayChannel">
        INSERT INTO <include refid="table" /> (
            VERSION,
            PAY_CHANNEL_NO,
            PAY_CHANNEL_NAME,
            CHANNEL_TYPE,
            CREATE_TIME,
            UPDATE_TIME,
            UPDATE_OPERATOR,
            CREATE_OPERATOR,
            STATUS,
            DESCRIPTION,
            ACCOUNT_NAME,
            ACCOUNT_NO,
            BANK_NO,
            BANK_NAME,
            SUB_BANK_NAME,
            JOIN_BANK_NO,
            BANK_ADDRESS
        ) VALUES (
        #{version,jdbcType=INTEGER},
        #{payChannelNo,jdbcType=VARCHAR},
        #{payChannelName,jdbcType=VARCHAR},
        #{channelType,jdbcType=SMALLINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{updateOperator,jdbcType=VARCHAR},
        #{createOperator,jdbcType=VARCHAR},
        #{status,jdbcType=SMALLINT},
        #{description,jdbcType=VARCHAR},
        #{accountName,jdbcType=VARCHAR},
        #{accountNo,jdbcType=VARCHAR},
        #{bankNo,jdbcType=VARCHAR},
        #{bankName,jdbcType=VARCHAR},
        #{subBankName,jdbcType=VARCHAR},
        #{joinBankNo,jdbcType=VARCHAR},
        #{bankAddress,jdbcType=VARCHAR}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.report.PayChannel">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=INTEGER} +1,
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
                PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},
                CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
                CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
                STATUS = #{status,jdbcType=SMALLINT},
                DESCRIPTION = #{description,jdbcType=VARCHAR},
                ACCOUNT_NAME =   #{accountName,jdbcType=VARCHAR},
                ACCOUNT_NO =     #{accountNo,jdbcType=VARCHAR},
                BANK_NO =       #{bankNo,jdbcType=VARCHAR},
                BANK_NAME =      #{bankName,jdbcType=VARCHAR},
                SUB_BANK_NAME =   #{subBankName,jdbcType=VARCHAR},
                JOIN_BANK_NO =   #{joinBankNo,jdbcType=VARCHAR},
                BANK_ADDRESS = #{bankAddress,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.report.PayChannel">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                    VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="payChannelNo != null">
                    PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                    PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null">
                    CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOperator != null">
                    UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
            </if>
            <if test="createOperator != null">
                    CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                    STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="description != null">
                    DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null and accountName !=''">
                ACCOUNT_NAME =   #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null and accountNo !=''">
                ACCOUNT_NO =     #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="bankNo != null and bankNo !=''">
                BANK_NO =       #{bankNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName !=''">
                BANK_NAME =      #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="subBankName != null and subBankName !=''">
                SUB_BANK_NAME =   #{subBankName,jdbcType=VARCHAR},
            </if>
            <if test="joinBankNo != null and joinBankNo !=''">
                JOIN_BANK_NO =   #{joinBankNo,jdbcType=VARCHAR},
            </if>
            <if test="bankAddress != null and bankAddress !=''">
                BANK_ADDRESS = #{bankAddress,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=INTEGER}
            </if>
            <if test="payChannelNo != null and payChannelNo !=''">
                and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="payChannelName != null and payChannelName !=''">
                and PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR}
            </if>
            <if test="payChannelNameLike != null and payChannelNameLike !=''">
                and PAY_CHANNEL_NAME like concat('%',#{payChannelNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="channelType != null">
                and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateOperator != null and updateOperator !=''">
                and UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
            </if>
            <if test="createOperator != null and createOperator !=''">
                and CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and STATUS = #{status,jdbcType=SMALLINT}
            </if>
            <if test="description != null and description !=''">
                and DESCRIPTION = #{description,jdbcType=VARCHAR}
            </if>
    </sql>

    <select id="listCustomPage" parameterType="java.util.Map" resultType="com.zhixianghui.facade.common.dto.PayChannelDto">
        select
            pc.ID,
            pc.VERSION,
            pc.PAY_CHANNEL_NO,
            pc.PAY_CHANNEL_NAME,
            pc.CREATE_TIME,
            pc.UPDATE_TIME,
            pc.UPDATE_OPERATOR,
            pc.CREATE_OPERATOR,
            pc.STATUS,
            pc.DESCRIPTION,
            pc.ACCOUNT_NAME,
            pc.ACCOUNT_NO,
            pc.BANK_NO,
            pc.BANK_NAME,
            pc.SUB_BANK_NAME,
            pc.JOIN_BANK_NO,
            pc.BANK_ADDRESS,
            group_concat(pct.TYPE) as typeConcat
         from
        <include refid="table"/> pc
        left join tbl_pay_channel_type pct on pc.PAY_CHANNEL_NO = pct.PAY_CHANNEL_NO
        <where>
            <if test="payChannelNo != null and payChannelNo != ''">
                and pc.PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="payChannelNameLike != null and payChannelNameLike != ''">
                AND pc.PAY_CHANNEL_NAME like concat('%',#{payChannelNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="channelType != null and channelType != ''">
                and pct.TYPE = #{channelType,jdbcType=SMALLINT}
            </if>
        </where>
        group by pc.PAY_CHANNEL_NO
        order by pc.id desc
    </select>

    <select id="customCountBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(DISTINCT (pc.ID)) FROM
        <include refid="table"/> pc left join tbl_pay_channel_type pct on pc.PAY_CHANNEL_NO = pct.PAY_CHANNEL_NO
        <where>
            <if test="payChannelNo != null and payChannelNo != ''">
                and pc.PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="payChannelNameLike != null and payChannelNameLike != ''">
                AND pc.PAY_CHANNEL_NAME like concat('%',#{payChannelNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="channelType != null and channelType != ''">
                and pct.TYPE = #{channelType,jdbcType=SMALLINT}
            </if>
        </where>
    </select>
</mapper>
