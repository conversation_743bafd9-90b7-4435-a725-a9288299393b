<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.report.PayChannelType">
    <sql id="table">tbl_pay_channel_type</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.report.PayChannelType">
        <id column="ID" property="id" jdbcType="INTEGER"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="TYPE" property="type" jdbcType="INTEGER"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PAY_CHANNEL_NO, TYPE
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.report.PayChannelType">
        INSERT INTO <include refid="table" /> (
            PAY_CHANNEL_NO,
            TYPE
        ) VALUES (
            #{payChannelNo,jdbcType=VARCHAR},
            #{type,jdbcType=INTEGER}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            PAY_CHANNEL_NO,
            TYPE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.payChannelNo,jdbcType=VARCHAR},
            #{item.type,jdbcType=INTEGER}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.report.PayChannelType">
        UPDATE <include refid="table" /> SET
            PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
            TYPE = #{type,jdbcType=INTEGER}
        WHERE ID = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.report.PayChannelType">
        UPDATE <include refid="table" />
        <set>
            <if test="payChannelNo != null">
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                TYPE = #{type,jdbcType=INTEGER}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=INTEGER}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=INTEGER}
    </delete>

    <delete id="deleteByChannelNo" parameterType="java.util.Map">
        DELETE FROM <include refid="table"/> where pay_channel_no = #{payChannelNo,jdbcType=VARCHAR}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=INTEGER}
        </if>
        <if test="payChannelNo != null and payChannelNo !=''">
            and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
        </if>
        <if test="type != null">
            and TYPE = #{type,jdbcType=INTEGER}
        </if>
    </sql>

</mapper>
