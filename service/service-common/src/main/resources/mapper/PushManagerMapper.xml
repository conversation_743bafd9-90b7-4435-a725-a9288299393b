<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.PushManagerMapper">
    <sql id="table">tbl_push_manager</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.push.PushManager">
        <id column="id" property="id" jdbcType="INTEGER"/>

        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="push_type" property="pushType" jdbcType="SMALLINT"/>
        <result column="sftp_ip" property="sftpIp" jdbcType="VARCHAR"/>
        <result column="sftp_port" property="sftpPort" jdbcType="VARCHAR"/>
        <result column="sftp_path" property="sftpPath" jdbcType="VARCHAR"/>
        <result column="server_type" property="serverType" jdbcType="SMALLINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="callback_url" property="callbackUrl" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mch_no, push_type, sftp_ip, sftp_port, sftp_path,server_type, username, password, callback_url, create_by, create_time, update_by, update_time
    </sql>


    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            mch_no,
            push_type,
            sftp_ip,
            sftp_port,
            sftp_path,
            server_type,
            username,
            password,
            callback_url,
            create_by,
            create_time,
            update_by,
            update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.pushType,jdbcType=SMALLINT},
            #{item.sftpIp,jdbcType=VARCHAR},
            #{item.sftpPort,jdbcType=VARCHAR},
            #{item.sftpPath,jdbcType=VARCHAR},
            #{item.serverType,jdbcType=SMALLINT},
            #{item.username,jdbcType=VARCHAR},
            #{item.password,jdbcType=VARCHAR},
            #{item.callbackUrl,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.push.PushManager">
        UPDATE <include refid="table" /> SET
            mch_no = #{mchNo,jdbcType=VARCHAR},
            push_type = #{pushType,jdbcType=SMALLINT},
            sftp_ip = #{sftpIp,jdbcType=VARCHAR},
            sftp_port = #{sftpPort,jdbcType=VARCHAR},
            sftp_path = #{sftpPath,jdbcType=VARCHAR},
            server_type = #{serverType,jdbcType=SMALLINT},
            username = #{username,jdbcType=VARCHAR},
            password = #{password,jdbcType=VARCHAR},
            callback_url = #{callbackUrl,jdbcType=VARCHAR},
            create_by = #{createBy,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_by = #{updateBy,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.push.PushManager">
        UPDATE <include refid="table" />
        <set>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="pushType != null">
                push_type = #{pushType,jdbcType=SMALLINT},
            </if>
            <if test="sftpIp != null">
                sftp_ip = #{sftpIp,jdbcType=VARCHAR},
            </if>
            <if test="sftpPort != null">
                sftp_port = #{sftpPort,jdbcType=VARCHAR},
            </if>
            <if test="sftpPath != null">
                sftp_path = #{sftpPath,jdbcType=VARCHAR},
            </if>
            <if test="serverType != null">
                server_type = #{serverType,jdbcType=SMALLINT},
            </if>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="callbackUrl != null">
                callback_url = #{callbackUrl,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=INTEGER}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and mch_no = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="pushType != null">
            and push_type = #{pushType,jdbcType=SMALLINT}
        </if>
        <if test="sftpIp != null and sftpIp !=''">
            and sftp_ip = #{sftpIp,jdbcType=VARCHAR}
        </if>
        <if test="sftpPort != null and sftpPort !=''">
            and sftp_port = #{sftpPort,jdbcType=VARCHAR}
        </if>
        <if test="sftpPath != null and sftpPath !=''">
            and sftp_path = #{sftpPath,jdbcType=VARCHAR}
        </if>
        <if test="serverType != null and serverType != ''">
            and server_type = #{serverType,jdbcType=SMALLINT}
        </if>
        <if test="username != null and username !=''">
            and username = #{username,jdbcType=VARCHAR}
        </if>
        <if test="password != null and password !=''">
            and password = #{password,jdbcType=VARCHAR}
        </if>
        <if test="callbackUrl != null and callbackUrl !=''">
            and callback_url = #{callbackUrl,jdbcType=VARCHAR}
        </if>
        <if test="createBy != null and createBy !=''">
            and create_by = #{createBy,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateBy != null and updateBy !=''">
            and update_by = #{updateBy,jdbcType=VARCHAR}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

</mapper>
