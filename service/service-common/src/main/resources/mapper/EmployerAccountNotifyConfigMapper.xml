<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.EmployerAccountNotifyConfigMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.report.EmployerAccountNotifyConfig">
    <!--@mbg.generated-->
    <!--@Table tbl_employer_account_notify_config-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
    <result column="EMPLOYER_NO" jdbcType="VARCHAR" property="employerNo" />
    <result column="EMPLOYER_NAME" jdbcType="VARCHAR" property="employerName" />
    <result column="MAINSTAY_NO" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="MAINSTAY_NAME" jdbcType="VARCHAR" property="mainstayName" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="NOTIFY_AMOUNT" jdbcType="DECIMAL" property="notifyAmount" />
    <result column="NOTIFY_TYPE" jdbcType="INTEGER" property="notifyType" />
    <result column="NOTIFY_TIME_START" jdbcType="TIME" property="notifyTimeStart" />
    <result column="NOTIFY_TIME_END" jdbcType="TIME" property="notifyTimeEnd" />
    <result column="NOTIFY_TIMES" jdbcType="INTEGER" property="notifyTimes" />
    <result column="RECEIVE_ACCOUNT" jdbcType="VARCHAR" property="receiveAccount" />
    <result column="STATUS" jdbcType="BOOLEAN" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, VERSION, EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NO, MAINSTAY_NAME, CREATE_BY, CREATE_TIME, 
    UPDATE_BY, UPDATE_TIME, NOTIFY_AMOUNT, NOTIFY_TYPE, NOTIFY_TIME_START, NOTIFY_TIME_END, 
    NOTIFY_TIMES, RECEIVE_ACCOUNT,STATUS
  </sql>
</mapper>