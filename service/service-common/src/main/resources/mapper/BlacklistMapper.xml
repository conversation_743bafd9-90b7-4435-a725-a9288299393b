<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.BlacklistMapper">
    <sql id="table">tbl_blacklist</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.black.Blacklist">
        <id column="id" property="id" jdbcType="INTEGER"/>

        <result column="subject_no" property="subjectNo" jdbcType="VARCHAR"/>
        <result column="subject_name" property="subjectName" jdbcType="VARCHAR"/>
        <result column="tag" property="tag" jdbcType="VARCHAR"/>
        <result column="tag_desc" property="tagDesc" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, subject_no, subject_name, tag, tag_desc, create_time, create_by
    </sql>


    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            subject_no,
            subject_name,
            tag,
            tag_desc,
            create_time,
            create_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.subjectNo,jdbcType=VARCHAR},
            #{item.subjectName,jdbcType=VARCHAR},
            #{item.tag,jdbcType=VARCHAR},
            #{item.tagDesc,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createBy,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.black.Blacklist">
        UPDATE <include refid="table" /> SET
            subject_no = #{subjectNo,jdbcType=VARCHAR},
            subject_name = #{subjectName,jdbcType=VARCHAR},
            tag = #{tag,jdbcType=VARCHAR},
            tag_desc = #{tagDesc,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            create_by = #{createBy,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.black.Blacklist">
        UPDATE <include refid="table" />
        <set>
            <if test="subjectNo != null">
                subject_no = #{subjectNo,jdbcType=VARCHAR},
            </if>
            <if test="subjectName != null">
                subject_name = #{subjectName,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                tag = #{tag,jdbcType=VARCHAR},
            </if>
            <if test="tagDesc != null">
                tag_desc = #{tagDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=INTEGER}
        </if>
        <if test="subjectNo != null and subjectNo !=''">
            and subject_no = #{subjectNo,jdbcType=VARCHAR}
        </if>
        <if test="subjectName != null and subjectName !=''">
            and subject_name = #{subjectName,jdbcType=VARCHAR}
        </if>
        <if test="tag != null and tag !=''">
            and tag = #{tag,jdbcType=VARCHAR}
        </if>
        <if test="tagDesc != null and tagDesc !=''">
            and tag_desc = #{tagDesc,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createBy != null and createBy !=''">
            and create_by = #{createBy,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
