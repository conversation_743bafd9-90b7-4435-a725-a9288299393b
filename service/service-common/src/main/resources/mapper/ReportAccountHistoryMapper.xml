<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.report.ReportAccountHistory">
    <sql id="table">tbl_report_account_history</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.report.ReportAccountHistory">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MERCHANT_TYPE" property="merchantType" jdbcType="SMALLINT"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_NAME" property="payChannelName" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_TYPE" property="payChannelType" jdbcType="SMALLINT"/>
        <result column="CHANNEL_MERCHANT_NO" property="channelMerchantNo" jdbcType="VARCHAR"/>
        <result column="AGREEMENT_NO" property="agreementNo" jdbcType="VARCHAR"/>
        <result column="ALIPAY_USER_ID" property="alipayUserId" jdbcType="VARCHAR"/>
        <result column="ALIPAY_CARD_NO" property="alipayCardNo" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="IS_SHOW" property="isShow" jdbcType="SMALLINT"/>
        <result column="TITLE" property="title" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID,VERSION, EMPLOYER_NO,MAINSTAY_NO, MERCHANT_TYPE, PAY_CHANNEL_NO, PAY_CHANNEL_NAME, PAY_CHANNEL_TYPE, CHANNEL_MERCHANT_NO, AGREEMENT_NO, ALIPAY_USER_ID, ALIPAY_CARD_NO,CREATE_TIME,IS_SHOW,TITLE,EMPLOYER_NAME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.report.ReportAccountHistory">
        INSERT INTO <include refid="table" /> (
            VERSION,
            EMPLOYER_NO,
            MAINSTAY_NO,
            MERCHANT_TYPE,
            PAY_CHANNEL_NO,
            PAY_CHANNEL_NAME,
            PAY_CHANNEL_TYPE,
            CHANNEL_MERCHANT_NO,
            AGREEMENT_NO,
            ALIPAY_USER_ID,
            ALIPAY_CARD_NO,
            CREATE_TIME,
            IS_SHOW,
            TITLE,
            EMPLOYER_NAME
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{employerNo,jdbcType=VARCHAR},
            #{mainstayNo,jdbcType=VARCHAR},
            #{merchantType,jdbcType=SMALLINT},
            #{payChannelNo,jdbcType=VARCHAR},
            #{payChannelName,jdbcType=VARCHAR},
            #{payChannelType,jdbcType=SMALLINT},
            #{channelMerchantNo,jdbcType=VARCHAR},
            #{agreementNo,jdbcType=VARCHAR},
            #{alipayUserId,jdbcType=VARCHAR},
            #{alipayCardNo,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{isShow,jdbcType=SMALLINT},
            #{title,jdbcType=VARCHAR},
            #{employerName,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            EMPLOYER_NO,
            MAINSTAY_NO,
            MERCHANT_TYPE,
            PAY_CHANNEL_NO,
            PAY_CHANNEL_NAME,
            PAY_CHANNEL_TYPE,
            CHANNEL_MERCHANT_NO,
            AGREEMENT_NO,
            ALIPAY_USER_ID,
            ALIPAY_CARD_NO,
            CREATE_TIME,
            IS_SHOW,
            TITLE,
            EMPLOYER_NAME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.merchantType,jdbcType=SMALLINT},
            #{item.payChannelNo,jdbcType=VARCHAR},
            #{item.payChannelName,jdbcType=VARCHAR},
            #{item.payChannelType,jdbcType=SMALLINT},
            #{item.channelMerchantNo,jdbcType=VARCHAR},
            #{item.agreementNo,jdbcType=VARCHAR},
            #{item.alipayUserId,jdbcType=VARCHAR},
            #{item.alipayCardNo,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.isShow,jdbcType=SMALLINT},
            #{item.title,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.report.ReportAccountHistory">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} + 1,
            EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            MERCHANT_TYPE = #{merchantType,jdbcType=SMALLINT},
            PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
            PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},
            PAY_CHANNEL_TYPE = #{payChannelType,jdbcType=SMALLINT},
            CHANNEL_MERCHANT_NO = #{channelMerchantNo,jdbcType=VARCHAR},
            AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR},
            ALIPAY_USER_ID = #{alipayUserId,jdbcType=VARCHAR},
            ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR},
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            IS_SHOW = #{isShow,jdbcType=SMALLINT},
            title = #{title,jdbcType=VARCHAR},
            EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.report.ReportAccountHistory">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="employerNo != null">
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantType != null">
                MERCHANT_TYPE = #{merchantType,jdbcType=SMALLINT},
            </if>
            <if test="payChannelNo != null">
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelType != null">
                PAY_CHANNEL_TYPE = #{payChannelType,jdbcType=SMALLINT},
            </if>
            <if test="channelMerchantNo != null">
                CHANNEL_MERCHANT_NO = #{channelMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR},
            </if>
            <if test="alipayUserId != null">
                ALIPAY_USER_ID = #{alipayUserId,jdbcType=VARCHAR},
            </if>
            <if test="alipayCardNo != null and alipayCardNo != ''">
                ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isShow != null">
                IS_SHOW = #{isShow,jdbcType=SMALLINT}
            </if>
            <if test="title != null and title != ''">
                title = #{title,jdbcType=VARCHAR}
            </if>
            <if test="employerName != null and employerName != ''">
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="merchantType != null">
            and MERCHANT_TYPE = #{merchantType,jdbcType=SMALLINT}
        </if>
        <if test="payChannelNo != null and payChannelNo !=''">
            and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
        </if>
        <if test="payChannelName != null and payChannelName !=''">
            and PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR}
        </if>
        <if test="payChannelType != null">
            and PAY_CHANNEL_TYPE = #{payChannelType,jdbcType=SMALLINT}
        </if>
        <if test="channelMerchantNo != null and channelMerchantNo !=''">
            and CHANNEL_MERCHANT_NO = #{channelMerchantNo,jdbcType=VARCHAR}
        </if>
        <if test="agreementNo != null and agreementNo !=''">
            and AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR}
        </if>
        <if test="alipayUserId != null and alipayUserId !=''">
            and ALIPAY_USER_ID = #{alipayUserId,jdbcType=VARCHAR}
        </if>
        <if test="alipayCardNo != null and alipayCardNo != ''">
            and ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="isShow != null">
            and IS_SHOW = #{isShow,jdbcType=SMALLINT}
        </if>
        <if test="title != null and title != ''">
            and TITLE = #{title,jdbcType=VARCHAR}
        </if>
        <if test="employerName != null and employerName != ''">
            and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
