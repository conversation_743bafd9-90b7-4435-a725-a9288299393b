<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.config.BankOrganization">
    <sql id="table">tbl_bank_organization</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.BankOrganization">
        <id column="ID" property="id" jdbcType="INTEGER"/>

        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
        <result column="ORGANIZATION_NAME" property="organizationName" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, BANK_CODE, ORGANIZATION_NAME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.config.BankOrganization">
        INSERT INTO <include refid="table" /> (
            BANK_CODE,
            ORGANIZATION_NAME
        ) VALUES (
            #{bankCode,jdbcType=VARCHAR},
            #{organizationName,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            BANK_CODE,
            ORGANIZATION_NAME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.bankCode,jdbcType=VARCHAR},
            #{item.organizationName,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.config.BankOrganization">
        UPDATE <include refid="table" /> SET
            BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            ORGANIZATION_NAME = #{organizationName,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.config.BankOrganization">
        UPDATE <include refid="table" />
        <set>
            <if test="openingBankNo != null">
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="organizationName != null">
                ORGANIZATION_NAME = #{organizationName,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=INTEGER} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=INTEGER}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=INTEGER}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=INTEGER}
        </if>
        <if test="bankCode != null and bankCode !=''">
            and BANK_CODE = #{bankCode,jdbcType=VARCHAR}
        </if>
        <if test="organizationName != null and organizationName !=''">
            and ORGANIZATION_NAME = #{organizationName,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
