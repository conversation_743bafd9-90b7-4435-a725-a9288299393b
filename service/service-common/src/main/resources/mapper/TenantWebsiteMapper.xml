<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.TenantWebsiteMapper">
    <sql id="table">tbl_tenant_website</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.tenant.TenantWebsite">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="website" property="website" jdbcType="VARCHAR"/>
        <result column="site_type" property="siteType" jdbcType="SMALLINT"/>
        <result column="icp_no" property="icpNo" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, website, site_type,icp_no
    </sql>

    <select id="getVoListByTenantId" resultType="com.zhixianghui.facade.common.vo.TenantLinkVo">
        select website as website,site_type as siteType,icp_no as icpNo from <include refid="table"/> where tenant_id = #{tenantId}
    </select>

</mapper>
