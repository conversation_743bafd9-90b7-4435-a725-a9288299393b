<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation">
    <sql id="table">tbl_employer_mainstay_relation</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="CREATE_OPERATOR" property="createOperator" jdbcType="VARCHAR"/>
        <result column="UPDATE_OPERATOR" property="updateOperator" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_STATUS" property="accountStatus" jdbcType="SMALLINT"/>
        <result column="PARAM_INFO" property="paramInfo" jdbcType="OTHER"/>
        <result column="EXTERNAL_ENTERPRISE_ID" property="externalEnterpriseId" jdbcType="VARCHAR"/>
        <result column="EXTERNAL_ENTERPRISE_SN" property="externalEnterpriseSn" jdbcType="VARCHAR"/>
        <result column="EXTERNAL_ENTERPRISE_STATUS" property="externalEnterpriseStatus" jdbcType="VARCHAR"/>
        <result column="EXTERNAL_PASSWORD" property="externalPassword" jdbcType="VARCHAR"/>
        <result column="EXTERNAL_USER_NAME" property="externalUserName" jdbcType="VARCHAR"/>
        <result column="HAS_EXTERNAL_SYSTEM" property="hasExternalSystem" jdbcType="BOOLEAN"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, EMPLOYER_NAME, EMPLOYER_NO, MAINSTAY_NAME,
        MAINSTAY_NO, CREATE_OPERATOR, UPDATE_OPERATOR, CREATE_TIME, UPDATE_TIME,
        STATUS, DESCRIPTION, ACCOUNT_STATUS, PARAM_INFO,EXTERNAL_ENTERPRISE_ID,
        EXTERNAL_ENTERPRISE_SN,EXTERNAL_ENTERPRISE_STATUS,EXTERNAL_PASSWORD,
        EXTERNAL_USER_NAME,HAS_EXTERNAL_SYSTEM
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation">
        INSERT INTO <include refid="table" /> (
            VERSION,
            EMPLOYER_NAME,
            EMPLOYER_NO,
            MAINSTAY_NAME,
            MAINSTAY_NO,
            CREATE_OPERATOR,
            UPDATE_OPERATOR,
            CREATE_TIME,
            UPDATE_TIME,
            STATUS,
            DESCRIPTION,
            ACCOUNT_STATUS,
            PARAM_INFO,
            EXTERNAL_ENTERPRISE_ID,
            EXTERNAL_ENTERPRISE_SN,
            EXTERNAL_ENTERPRISE_STATUS,
            EXTERNAL_PASSWORD,
            EXTERNAL_USER_NAME,
            HAS_EXTERNAL_SYSTEM
        ) VALUES (
        #{version,jdbcType=INTEGER},
        #{employerName,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{createOperator,jdbcType=VARCHAR},
        #{updateOperator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{status,jdbcType=SMALLINT},
        #{description,jdbcType=VARCHAR},
        #{accountStatus,jdbcType=SMALLINT},
        #{paramInfo,jdbcType=OTHER},
        #{externalEnterpriseId,jdbcType=VARCHAR},
        #{externalEnterpriseSn,jdbcType=VARCHAR},
        #{externalEnterpriseStatus,jdbcType=VARCHAR},
        #{externalPassword,jdbcType=VARCHAR},
        #{externalUserName,jdbcType=VARCHAR},
        #{hasExternalSystem,jdbcType=BOOLEAN}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=INTEGER} +1,
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
                CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
                UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                STATUS = #{status,jdbcType=SMALLINT},
                DESCRIPTION = #{description,jdbcType=VARCHAR},
                ACCOUNT_STATUS = #{accountStatus,jdbcType=SMALLINT},
                PARAM_INFO = #{paramInfo,jdbcType=OTHER},
                EXTERNAL_ENTERPRISE_ID = #{externalEnterpriseId,jdbcType=VARCHAR},
                EXTERNAL_ENTERPRISE_SN = #{externalEnterpriseSn,jdbcType=VARCHAR},
                EXTERNAL_ENTERPRISE_STATUS = #{externalEnterpriseStatus,jdbcType=INTEGER},
                EXTERNAL_PASSWORD = #{externalPassword,jdbcType=VARCHAR},
                EXTERNAL_USER_NAME = #{externalUserName,jdbcType=VARCHAR},
                HAS_EXTERNAL_SYSTEM = #{hasExternalSystem,jdbcType=BOOLEAN}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <update id="updateEmployerMainstayRelation"
            parameterType="com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation">
        UPDATE <include refid="table" /> SET
        ACCOUNT_STATUS = #{accountStatus,jdbcType=SMALLINT},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        <if test="paramInfo != null">
            ,PARAM_INFO = #{paramInfo,jdbcType=OTHER}
        </if>
        WHERE EMPLOYER_NO = #{employerNo,jdbcType=BIGINT} and MAINSTAY_NO = #{mainstayNo, jdbcType=BIGINT}
    </update>


    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null and version !='' ">
                VERSION = #{version,jdbcType=INTEGER} + 1,
            </if>
            <if test="employerName != null and employerName != ''">
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
            </if>
            <if test="employerNo != null and employerNo != '' ">
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null and mainstayName != '' ">
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null and mainstayNo !='' ">
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="createOperator != null and createOperator !='' ">
                 CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
            </if>
            <if test="updateOperator != null and updateOperator !='' ">
                UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null  ">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null  ">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status!='' ">
                STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="description != null and description!='' ">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="accountStatus != null">
                ACCOUNT_STATUS = #{accountStatus,jdbcType=SMALLINT},
            </if>
            <if test="paramInfo != null">
                PARAM_INFO = #{paramInfo,jdbcType=OTHER},
            </if>
            <if test="externalEnterpriseId != null and externalEnterpriseId!=''">
                EXTERNAL_ENTERPRISE_ID = #{externalEnterpriseId,jdbcType=VARCHAR},
            </if>
            <if test="externalEnterpriseSn != null and externalEnterpriseSn!=''">
                EXTERNAL_ENTERPRISE_SN = #{externalEnterpriseSn,jdbcType=VARCHAR},
            </if>
            <if test="externalEnterpriseStatus != null">
                EXTERNAL_ENTERPRISE_STATUS = #{externalEnterpriseStatus,jdbcType=INTEGER},
            </if>
            <if test="externalPassword != null and externalPassword !=''">
                EXTERNAL_PASSWORD = #{externalPassword,jdbcType=VARCHAR},
            </if>
            <if test="externalUserName != null and externalUserName!=''">
                EXTERNAL_USER_NAME = #{externalUserName,jdbcType=VARCHAR},
            </if>
            <if test="hasExternalSystem != null">
                HAS_EXTERNAL_SYSTEM = #{hasExternalSystem,jdbcType=BOOLEAN}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!--条件删除-->
    <delete id="deleteBy" parameterType="java.util.Map">
        DELETE FROM <include refid="table" />
        where
        <trim  prefixOverrides="AND">
            <include refid="condition_sql" />
        </trim>
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=INTEGER}
            </if>
            <if test="employerName != null and employerName !=''">
                and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
            </if>
            <if test="employerNameLike != null and employerNameLike !=''">
                and EMPLOYER_NAME like concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="employerNo != null and employerNo !=''">
                and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNameLike != null and mainstayNameLike !=''">
                and MAINSTAY_NAME like concat('%',#{mainstayNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="createOperator != null and createOperator !=''">
                and CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
            </if>
            <if test="updateOperator != null and updateOperator !=''">
                and UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null " >
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null  ">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null and status !='' ">
                and STATUS = #{status,jdbcType=SMALLINT}
            </if>
            <if test="description != null and description !=''">
                and DESCRIPTION = #{description,jdbcType=VARCHAR}
            </if>
            <if test="accountStatus != null">
                and ACCOUNT_STATUS = #{accountStatus,jdbcType=SMALLINT}
            </if>
            <if test="paramInfo != null and paramInfo !=''">
                and PARAM_INFO = #{paramInfo,jdbcType=OTHER}
            </if>
            <if test="externalEnterpriseId != null and externalEnterpriseId !=''">
                and EXTERNAL_ENTERPRISE_ID = #{externalEnterpriseId,jdbcType=VARCHAR}
            </if>
            <if test="externalEnterpriseSn != null and externalEnterpriseSn !=''">
                and EXTERNAL_ENTERPRISE_SN = #{externalEnterpriseSn,jdbcType=VARCHAR}
            </if>
            <if test="externalEnterpriseStatus != null">
                and EXTERNAL_ENTERPRISE_STATUS = #{externalEnterpriseStatus,jdbcType=INTEGER}
            </if>
            <if test="externalPassword != null and externalPassword !=''">
               and EXTERNAL_PASSWORD = #{externalPassword,jdbcType=VARCHAR}
            </if>
            <if test="externalUserName != null and externalUserName!=''">
               and EXTERNAL_USER_NAME = #{externalUserName,jdbcType=VARCHAR}
            </if>
            <if test="hasExternalSystem != null">
               and HAS_EXTERNAL_SYSTEM = #{hasExternalSystem,jdbcType=BOOLEAN}
            </if>
            <if test="maxId != null">
                and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
            </if>
    </sql>

    <update id="batchUpdate" parameterType="java.util.Map">
        update <include refid="table"/> set status = #{status}
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>
