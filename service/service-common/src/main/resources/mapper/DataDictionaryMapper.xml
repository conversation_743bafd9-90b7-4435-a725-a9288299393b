<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.common.entity.config.DataDictionary">
	<sql id="table"> tbl_data_dictionary </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.DataDictionary">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="CREATOR" property="creator" jdbcType="VARCHAR"/>
		<result column="DATA_NAME" property="dataName" jdbcType="VARCHAR"/>
		<result column="DATA_INFO" property="dataInfo" jdbcType="OTHER"/>
		<result column="REMARK" property="remark" jdbcType="VARCHAR"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
		<result column="MODIFY_OPERATOR" property="modifyOperator" jdbcType="VARCHAR"/>
		<result column="SYSTEM_TYPE" property="systemType" jdbcType="SMALLINT"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		VERSION,
		CREATOR,
		DATA_NAME,
		DATA_INFO,
		REMARK,
		CREATE_TIME,
		MODIFY_TIME,
		MODIFY_OPERATOR,
		SYSTEM_TYPE
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.config.DataDictionary">
		INSERT INTO <include refid="table" /> (
        	VERSION,
        	CREATOR,
        	DATA_NAME,
        	DATA_INFO,
        	REMARK,
        	CREATE_TIME,
        	MODIFY_TIME,
        	MODIFY_OPERATOR,
        	SYSTEM_TYPE
        ) VALUES (
			0,
			#{creator,jdbcType=VARCHAR},
			#{dataName,jdbcType=VARCHAR},
			#{dataInfo,jdbcType=OTHER},
			#{remark,jdbcType=VARCHAR},
			#{createTime,jdbcType=TIMESTAMP},
			#{modifyTime,jdbcType=TIMESTAMP},
			#{modifyOperator,jdbcType=VARCHAR},
			#{systemType,jdbcType=SMALLINT}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	VERSION,
        	CREATOR,
        	DATA_NAME,
        	DATA_INFO,
        	REMARK,
        	CREATE_TIME,
        	MODIFY_TIME,
        	MODIFY_OPERATOR,
        	SYSTEM_TYPE
        ) VALUES
			<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.creator,jdbcType=VARCHAR},
			#{item.dataName,jdbcType=VARCHAR},
			#{item.dataInfo,jdbcType=OTHER},
			#{item.remark,jdbcType=VARCHAR},
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.modifyTime,jdbcType=TIMESTAMP},
			#{item.modifyOperator,jdbcType=VARCHAR},
			#{item.systemType,jdbcType=SMALLINT}
			)
			</foreach>
	</insert>

	<!-- 更新 -->
	<update id="update" parameterType="com.zhixianghui.facade.common.entity.config.DataDictionary">
        UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			CREATOR = #{creator,jdbcType=VARCHAR},
			DATA_NAME = #{dataName,jdbcType=VARCHAR},
			DATA_INFO = #{dataInfo,jdbcType=OTHER},
			REMARK = #{remark,jdbcType=VARCHAR},
			MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
			MODIFY_OPERATOR = #{modifyOperator,jdbcType=VARCHAR},
			SYSTEM_TYPE = #{systemType,jdbcType=SMALLINT}
		</set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.config.DataDictionary">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=VARCHAR},
			</if>
			<if test="dataName != null">
				DATA_NAME = #{dataName,jdbcType=VARCHAR},
			</if>
			<if test="dataInfo != null">
				DATA_INFO = #{dataInfo,jdbcType=OTHER},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="modifyTime != null">
				MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyOperator != null">
				MODIFY_OPERATOR = #{modifyOperator,jdbcType=VARCHAR},
			</if>
			<if test="systemType != null">
				SYSTEM_TYPE = #{systemType,jdbcType=SMALLINT}
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 多条件组合查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		<choose>
			<when test="sortColumns != null and sortColumns !='' ">
				<![CDATA[ ORDER BY ${sortColumns} ]]>
			</when>
			<otherwise>
				<![CDATA[ ORDER BY ID DESC ]]>
			</otherwise>
		</choose>
	</select>

	<!-- 根据多条件组合查询，计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		SELECT count(ID) FROM
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 按查询条件删除 -->
	<delete id="deleteBy">
        DELETE FROM <include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
    </delete>

	<!-- 根据多个id查询 -->
    <select id="listByIdList" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE ID IN
		<foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE ID = #{id,jdbcType=BIGINT}
	</select>

	<!-- 按id主键删除 -->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- 按多个id主键删除 -->
	<delete id="deleteByIdList" parameterType="list">
		DELETE FROM <include refid="table" />
		WHERE ID IN
		<foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
	</delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null ">
			AND ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="idList != null and idList.size() > 0">
			AND ID IN <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
		</if>
		<if test="version != null ">
			AND VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="creator != null and creator !='' ">
			AND CREATOR = #{creator,jdbcType=VARCHAR}
		</if>
		<if test="dataName != null and dataName !='' ">
			AND DATA_NAME = #{dataName,jdbcType=VARCHAR}
		</if>
		<if test="dataInfo != null ">
			AND DATA_INFO = #{dataInfo,jdbcType=OTHER}
		</if>
		<if test="remark != null and remark !='' ">
			AND REMARK like concat('%',#{remark,jdbcType=VARCHAR},'%')
		</if>
		<if test="createTime != null ">
			AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="modifyTime != null ">
			AND MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP}
		</if>
		<if test="modifyOperator != null and modifyOperator !='' ">
			AND MODIFY_OPERATOR = #{modifyOperator,jdbcType=VARCHAR}
		</if>
		<if test="systemType != null ">
			AND SYSTEM_TYPE = #{systemType,jdbcType=SMALLINT}
		</if>
		<if test="dataNameLike != null and dataNameLike !=''">
			and DATA_NAME like "%"#{dataNameLike,jdbcType=VARCHAR}"%"
		</if>
	</sql>
</mapper>

