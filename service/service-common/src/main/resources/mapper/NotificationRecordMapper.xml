<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.NotificationRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.notificaton.NotificationRecord">
    <!--@mbg.generated-->
    <!--@Table tbl_notification_record-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
    <result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="PUBLISH_TIME" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="NOTIFICATION_TYPE" jdbcType="INTEGER" property="notificationType" />
    <result column="NOTIFICATION_TITLE" jdbcType="VARCHAR" property="notificationTitle" />
    <result column="NOTIFICATION_CONTENT" jdbcType="LONGVARCHAR" property="notificationContent" />
    <result column="NOTIFICATION_RECEIVER_TYPE" jdbcType="INTEGER" property="notificationReceiverType" />
    <result column="NOTIFICATION_RECEIVERS" jdbcType="VARCHAR" property="notificationReceivers" />
    <result column="PUBLISH_TYPE" jdbcType="INTEGER" property="publishType" />
    <result column="PUBLISH_STATUS" jdbcType="INTEGER" property="publishStatus" />
    <result column="PUSH_TIME" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="DELETED" jdbcType="TINYINT" property="deleted" />
    <result column="POP" jdbcType="TINYINT" property="pop" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, VERSION, CREATION_TIME, UPDATE_TIME, PUBLISH_TIME, NOTIFICATION_TYPE, NOTIFICATION_TITLE,
    NOTIFICATION_CONTENT, NOTIFICATION_RECEIVER_TYPE, NOTIFICATION_RECEIVERS, PUBLISH_TYPE,
    PUBLISH_STATUS, PUSH_TIME,DELETED,POP
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tbl_notification_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="VERSION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATION_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.creationTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="PUBLISH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.publishTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_TITLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationTitle,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationContent,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_RECEIVER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationReceiverType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_RECEIVERS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationReceivers,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PUBLISH_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.publishType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PUBLISH_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.publishStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PUSH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.pushTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tbl_notification_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="VERSION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATION_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creationTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.creationTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUBLISH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.publishTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.publishTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.notificationType != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_TITLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.notificationTitle != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationTitle,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.notificationContent != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationContent,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_RECEIVER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.notificationReceiverType != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationReceiverType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_RECEIVERS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.notificationReceivers != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationReceivers,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUBLISH_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.publishType != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.publishType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUBLISH_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.publishStatus != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.publishStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUSH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.pushTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_notification_record
    (VERSION, CREATION_TIME, UPDATE_TIME, PUBLISH_TIME, NOTIFICATION_TYPE, NOTIFICATION_TITLE,
    NOTIFICATION_CONTENT, NOTIFICATION_RECEIVER_TYPE, NOTIFICATION_RECEIVERS, PUBLISH_TYPE,
    PUBLISH_STATUS, PUSH_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.version,jdbcType=INTEGER}, #{item.creationTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.publishTime,jdbcType=TIMESTAMP}, #{item.notificationType,jdbcType=INTEGER},
      #{item.notificationTitle,jdbcType=VARCHAR}, #{item.notificationContent,jdbcType=LONGVARCHAR},
      #{item.notificationReceiverType,jdbcType=INTEGER}, #{item.notificationReceivers,jdbcType=VARCHAR},
      #{item.publishType,jdbcType=INTEGER}, #{item.publishStatus,jdbcType=INTEGER}, #{item.pushTime,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="ID" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.notificaton.NotificationRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_notification_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      VERSION,
      CREATION_TIME,
      UPDATE_TIME,
      PUBLISH_TIME,
      NOTIFICATION_TYPE,
      NOTIFICATION_TITLE,
      NOTIFICATION_CONTENT,
      NOTIFICATION_RECEIVER_TYPE,
      NOTIFICATION_RECEIVERS,
      PUBLISH_TYPE,
      PUBLISH_STATUS,
      PUSH_TIME,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{version,jdbcType=INTEGER},
      #{creationTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP},
      #{publishTime,jdbcType=TIMESTAMP},
      #{notificationType,jdbcType=INTEGER},
      #{notificationTitle,jdbcType=VARCHAR},
      #{notificationContent,jdbcType=LONGVARCHAR},
      #{notificationReceiverType,jdbcType=INTEGER},
      #{notificationReceivers,jdbcType=VARCHAR},
      #{publishType,jdbcType=INTEGER},
      #{publishStatus,jdbcType=INTEGER},
      #{pushTime,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=BIGINT},
      </if>
      VERSION = #{version,jdbcType=INTEGER},
      CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      PUBLISH_TIME = #{publishTime,jdbcType=TIMESTAMP},
      NOTIFICATION_TYPE = #{notificationType,jdbcType=INTEGER},
      NOTIFICATION_TITLE = #{notificationTitle,jdbcType=VARCHAR},
      NOTIFICATION_CONTENT = #{notificationContent,jdbcType=LONGVARCHAR},
      NOTIFICATION_RECEIVER_TYPE = #{notificationReceiverType,jdbcType=INTEGER},
      NOTIFICATION_RECEIVERS = #{notificationReceivers,jdbcType=VARCHAR},
      PUBLISH_TYPE = #{publishType,jdbcType=INTEGER},
      PUBLISH_STATUS = #{publishStatus,jdbcType=INTEGER},
      PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="ID" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.notificaton.NotificationRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_notification_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="version != null">
        VERSION,
      </if>
      <if test="creationTime != null">
        CREATION_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="publishTime != null">
        PUBLISH_TIME,
      </if>
      <if test="notificationType != null">
        NOTIFICATION_TYPE,
      </if>
      <if test="notificationTitle != null and notificationTitle != ''">
        NOTIFICATION_TITLE,
      </if>
      <if test="notificationContent != null and notificationContent != ''">
        NOTIFICATION_CONTENT,
      </if>
      <if test="notificationReceiverType != null">
        NOTIFICATION_RECEIVER_TYPE,
      </if>
      <if test="notificationReceivers != null and notificationReceivers != ''">
        NOTIFICATION_RECEIVERS,
      </if>
      <if test="publishType != null">
        PUBLISH_TYPE,
      </if>
      <if test="publishStatus != null">
        PUBLISH_STATUS,
      </if>
      <if test="pushTime != null">
        PUSH_TIME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notificationType != null">
        #{notificationType,jdbcType=INTEGER},
      </if>
      <if test="notificationTitle != null and notificationTitle != ''">
        #{notificationTitle,jdbcType=VARCHAR},
      </if>
      <if test="notificationContent != null and notificationContent != ''">
        #{notificationContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="notificationReceiverType != null">
        #{notificationReceiverType,jdbcType=INTEGER},
      </if>
      <if test="notificationReceivers != null and notificationReceivers != ''">
        #{notificationReceivers,jdbcType=VARCHAR},
      </if>
      <if test="publishType != null">
        #{publishType,jdbcType=INTEGER},
      </if>
      <if test="publishStatus != null">
        #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        VERSION = #{version,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null">
        PUBLISH_TIME = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notificationType != null">
        NOTIFICATION_TYPE = #{notificationType,jdbcType=INTEGER},
      </if>
      <if test="notificationTitle != null and notificationTitle != ''">
        NOTIFICATION_TITLE = #{notificationTitle,jdbcType=VARCHAR},
      </if>
      <if test="notificationContent != null and notificationContent != ''">
        NOTIFICATION_CONTENT = #{notificationContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="notificationReceiverType != null">
        NOTIFICATION_RECEIVER_TYPE = #{notificationReceiverType,jdbcType=INTEGER},
      </if>
      <if test="notificationReceivers != null and notificationReceivers != ''">
        NOTIFICATION_RECEIVERS = #{notificationReceivers,jdbcType=VARCHAR},
      </if>
      <if test="publishType != null">
        PUBLISH_TYPE = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="publishStatus != null">
        PUBLISH_STATUS = #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="pushTime != null">
        PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="listNotifications" parameterType="java.util.Map" resultType="com.zhixianghui.facade.common.entity.notificaton.NotificationRecord">
    select
    <include refid="Base_Column_List">
    </include>
    from tbl_notification_record
    <where>
      DELETED = 0
      <if test="param.notificationType != null">
        and NOTIFICATION_TYPE = #{param.notificationType,jdbcType=SMALLINT}
      </if>
      <if test="param.notificationReceiverType != null">
        and NOTIFICATION_RECEIVER_TYPE = #{param.notificationReceiverType,jdbcType=SMALLINT}
      </if>
      <if test="param.notificationReceivers != null  and param.notificationReceivers != ''">
        and <![CDATA[ ( ]]>
        NOTIFICATION_RECEIVER_TYPE=100
        or
        NOTIFICATION_RECEIVER_TYPE=#{param.notificationReceiverTypeSub,jdbcType=SMALLINT}
        or
        JSON_CONTAINS(NOTIFICATION_RECEIVERS->"$[*]",JSON_ARRAY(#{param.notificationReceivers}))
        <![CDATA[ ) ]]>
      </if>
      <if test="param.publishType !=null">
        and PUBLISH_TYPE = #{param.publishType,jdbcType=SMALLINT}
      </if>
      <if test="param.notificationTitle !=null and param.notificationTitle !=''">
        and NOTIFICATION_TITLE LIKE concat('%',#{param.notificationTitle,jdbcType=VARCHAR},'%')
      </if>
      <if test="param.pushBeginDate != null and param.pushEndDate != null" >
        and PUSH_TIME between #{param.pushBeginDate,jdbcType=TIMESTAMP} and #{param.pushEndDate,jdbcType=TIMESTAMP}
      </if>
    </where>
    <choose>
      <when test="param.sortColumns != null and param.sortColumns !='' ">
        <![CDATA[ ORDER BY ${param.sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY PUSH_TIME DESC ]]>
      </otherwise>
    </choose>
  </select>
</mapper>
