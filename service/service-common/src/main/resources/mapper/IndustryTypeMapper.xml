<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.common.entity.config.IndustryType">
	<sql id="table"> tbl_industry_type </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.IndustryType">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="INDUSTRY_TYPE_CODE" property="industryTypeCode" jdbcType="VARCHAR"/>
		<result column="INDUSTRY_TYPE_NAME" property="industryTypeName" jdbcType="VARCHAR"/>
		<result column="PARENT_ID" property="parentId" jdbcType="BIGINT"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		VERSION,
		UPDATOR,
		UPDATE_TIME,
		INDUSTRY_TYPE_CODE,
		INDUSTRY_TYPE_NAME,
		PARENT_ID
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.config.IndustryType">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	UPDATE_TIME ,
        	INDUSTRY_TYPE_CODE ,
        	INDUSTRY_TYPE_NAME ,
        	PARENT_ID 
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			0,
			#{updator,jdbcType=VARCHAR},
			#{updateTime,jdbcType=TIMESTAMP},
			#{industryTypeCode,jdbcType=VARCHAR},
			#{industryTypeName,jdbcType=VARCHAR},
			#{parentId,jdbcType=BIGINT}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
        	UPDATOR ,
        	UPDATE_TIME ,
        	INDUSTRY_TYPE_CODE ,
        	INDUSTRY_TYPE_NAME ,
        	PARENT_ID 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			#{item.createTime,jdbcType=TIMESTAMP},
			0,
			#{item.updator,jdbcType=VARCHAR},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.industryTypeCode,jdbcType=VARCHAR},
			#{item.industryTypeName,jdbcType=VARCHAR},
			#{item.parentId,jdbcType=BIGINT}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.common.entity.config.IndustryType">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			INDUSTRY_TYPE_CODE = #{industryTypeCode,jdbcType=VARCHAR},
			INDUSTRY_TYPE_NAME = #{industryTypeName,jdbcType=VARCHAR},
			PARENT_ID = #{parentId,jdbcType=BIGINT}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.config.IndustryType">
		UPDATE <include refid="table" />
		<set>
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="updator != null">
				UPDATOR =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="industryTypeCode != null">
				INDUSTRY_TYPE_CODE =#{industryTypeCode,jdbcType=VARCHAR},
			</if>
			<if test="industryTypeName != null">
				INDUSTRY_TYPE_NAME =#{industryTypeName,jdbcType=VARCHAR},
			</if>
			<if test="parentId != null">
				PARENT_ID =#{parentId,jdbcType=BIGINT},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="industryTypeCode != null and industryTypeCode !=''">
			and INDUSTRY_TYPE_CODE = #{industryTypeCode,jdbcType=VARCHAR}
		</if>
		<if test="industryTypeName != null and industryTypeName !=''">
			and INDUSTRY_TYPE_NAME = #{industryTypeName,jdbcType=VARCHAR}
		</if>
		<if test="parentId != null">
			and PARENT_ID = #{parentId,jdbcType=BIGINT}
		</if>
	</sql>
</mapper>

