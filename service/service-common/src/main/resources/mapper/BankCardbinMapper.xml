<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.common.entity.config.BankCardBin">
	<sql id="table"> tbl_bank_card_bin </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.BankCardBin">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="CARD_BIN" property="cardBin" jdbcType="VARCHAR"/>
		<result column="BANK_NO" property="bankNo" jdbcType="VARCHAR"/>
		<result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
		<result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
		<result column="CARD_NAME" property="cardName" jdbcType="VARCHAR"/>
		<result column="CARD_KIND" property="cardKind" jdbcType="SMALLINT"/>
		<result column="CARD_LENGTH" property="cardLength" jdbcType="SMALLINT"/>
		<result column="STATUS" property="status" jdbcType="SMALLINT"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		VERSION,
		UPDATOR,
		UPDATE_TIME,
		CARD_BIN,
		BANK_NO,
		BANK_CODE,
		BANK_NAME,
		CARD_NAME,
		CARD_KIND,
		CARD_LENGTH,
		STATUS
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.config.BankCardBin">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	UPDATOR ,
        	UPDATE_TIME ,
        	CARD_BIN ,
        	BANK_NO ,
        	BANK_CODE ,
        	BANK_NAME ,
        	CARD_NAME ,
        	CARD_KIND ,
        	CARD_LENGTH ,
        	STATUS 
        ) VALUES (
			0,
			#{updator,jdbcType=VARCHAR},
			#{updateTime,jdbcType=TIMESTAMP},
			#{cardBin,jdbcType=VARCHAR},
			#{bankNo,jdbcType=VARCHAR},
			#{bankCode,jdbcType=VARCHAR},
			#{bankName,jdbcType=VARCHAR},
			#{cardName,jdbcType=VARCHAR},
			#{cardKind,jdbcType=SMALLINT},
			#{cardLength,jdbcType=SMALLINT},
			#{status,jdbcType=SMALLINT}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	UPDATOR ,
        	UPDATE_TIME ,
        	CARD_BIN ,
        	BANK_NO ,
        	BANK_CODE ,
        	BANK_NAME ,
        	CARD_NAME ,
        	CARD_KIND ,
        	CARD_LENGTH ,
        	STATUS 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.updator,jdbcType=VARCHAR},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.cardBin,jdbcType=VARCHAR},
			#{item.bankNo,jdbcType=VARCHAR},
			#{item.bankCode,jdbcType=VARCHAR},
			#{item.bankName,jdbcType=VARCHAR},
			#{item.cardName,jdbcType=VARCHAR},
			#{item.cardKind,jdbcType=SMALLINT},
			#{item.cardLength,jdbcType=SMALLINT},
			#{item.status,jdbcType=SMALLINT}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.common.entity.config.BankCardBin">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			CARD_BIN = #{cardBin,jdbcType=VARCHAR},
			BANK_NO = #{bankNo,jdbcType=VARCHAR},
			BANK_CODE = #{bankCode,jdbcType=VARCHAR},
			BANK_NAME = #{bankName,jdbcType=VARCHAR},
			CARD_NAME = #{cardName,jdbcType=VARCHAR},
			CARD_KIND = #{cardKind,jdbcType=SMALLINT},
			CARD_LENGTH = #{cardLength,jdbcType=SMALLINT},
			STATUS = #{status,jdbcType=SMALLINT}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.config.BankCardBin">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="updator != null">
				UPDATOR =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="cardBin != null">
				CARD_BIN =#{cardBin,jdbcType=VARCHAR},
			</if>
			<if test="bankNo != null">
				BANK_NO =#{bankNo,jdbcType=VARCHAR},
			</if>
			<if test="bankCode != null">
				BANK_CODE =#{bankCode,jdbcType=VARCHAR},
			</if>
			<if test="bankName != null">
				BANK_NAME =#{bankName,jdbcType=VARCHAR},
			</if>
			<if test="cardName != null">
				CARD_NAME =#{cardName,jdbcType=VARCHAR},
			</if>
			<if test="cardKind != null">
				CARD_KIND =#{cardKind,jdbcType=SMALLINT},
			</if>
			<if test="cardLength != null">
				CARD_LENGTH =#{cardLength,jdbcType=SMALLINT},
			</if>
			<if test="status != null">
				STATUS =#{status,jdbcType=SMALLINT},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		order by id desc
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		order by id deac
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="cardBin != null and cardBin !=''">
			and CARD_BIN = #{cardBin,jdbcType=VARCHAR}
		</if>
		<if test="bankNo != null and bankNo !=''">
			and BANK_NO = #{bankNo,jdbcType=VARCHAR}
		</if>
		<if test="bankCode != null and bankCode !=''">
			and BANK_CODE = #{bankCode,jdbcType=VARCHAR}
		</if>
		<if test="bankName != null and bankName !=''">
			and BANK_NAME = #{bankName,jdbcType=VARCHAR}
		</if>
		<if test="cardName != null and cardName !=''">
			and CARD_NAME = #{cardName,jdbcType=VARCHAR}
		</if>
		<if test="cardKind != null">
			and CARD_KIND = #{cardKind,jdbcType=SMALLINT}
		</if>
		<if test="cardLength != null">
			and CARD_LENGTH = #{cardLength,jdbcType=SMALLINT}
		</if>
		<if test="status != null">
			and STATUS = #{status,jdbcType=SMALLINT}
		</if>
	</sql>
</mapper>

