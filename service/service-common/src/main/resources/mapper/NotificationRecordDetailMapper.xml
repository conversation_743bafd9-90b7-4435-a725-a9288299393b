<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.NotificationRecordDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail">
    <!--@mbg.generated-->
    <!--@Table tbl_notification_record_detail-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
    <result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="NOTIFICATION_ID" jdbcType="BIGINT" property="notificationId" />
    <result column="NOTIFICATION_TYPE" jdbcType="INTEGER" property="notificationType" />
    <result column="NOTIFICATION_TITLE" jdbcType="VARCHAR" property="notificationTitle" />
    <result column="READ_STATUS" jdbcType="INTEGER" property="readStatus" />
    <result column="READ_TIME" jdbcType="TIMESTAMP" property="readTime" />
    <result column="READ_OPERATOR_ID" jdbcType="BIGINT" property="readOperatorId" />
    <result column="READ_OPERATOR_NAME" jdbcType="VARCHAR" property="readOperatorName" />
    <result column="MCH_NO" jdbcType="VARCHAR" property="mchNo" />
    <result column="MCH_NAME" jdbcType="VARCHAR" property="mchName" />
  </resultMap>

  <resultMap id="detailFullInfoResult" type="com.zhixianghui.facade.common.vo.NotificationDetailFullInfo">
    <association property="notificationRecordDetail" javaType="com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail">
      <id column="ID" jdbcType="BIGINT" property="id" />
      <result column="VERSION" jdbcType="INTEGER" property="version" />
      <result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
      <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
      <result column="NOTIFICATION_ID" jdbcType="BIGINT" property="notificationId" />
      <result column="NOTIFICATION_TYPE" jdbcType="INTEGER" property="notificationType" />
      <result column="NOTIFICATION_TITLE" jdbcType="VARCHAR" property="notificationTitle" />
      <result column="READ_STATUS" jdbcType="INTEGER" property="readStatus" />
      <result column="READ_TIME" jdbcType="TIMESTAMP" property="readTime" />
      <result column="READ_OPERATOR_ID" jdbcType="BIGINT" property="readOperatorId" />
      <result column="READ_OPERATOR_NAME" jdbcType="VARCHAR" property="readOperatorName" />
      <result column="MCH_NO" jdbcType="VARCHAR" property="mchNo" />
      <result column="MCH_NAME" jdbcType="VARCHAR" property="mchName" />
    </association>
    <association property="notificationRecord" javaType="com.zhixianghui.facade.common.entity.notificaton.NotificationRecord">
      <id column="ID" jdbcType="BIGINT" property="id" />
      <result column="VERSION" jdbcType="INTEGER" property="version" />
      <result column="CREATION_TIME" jdbcType="TIMESTAMP" property="creationTime" />
      <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
      <result column="PUBLISH_TIME" jdbcType="TIMESTAMP" property="publishTime" />
      <result column="NOTIFICATION_TYPE" jdbcType="INTEGER" property="notificationType" />
      <result column="NOTIFICATION_TITLE" jdbcType="VARCHAR" property="notificationTitle" />
      <result column="NOTIFICATION_CONTENT" jdbcType="LONGVARCHAR" property="notificationContent" />
      <result column="NOTIFICATION_RECEIVER_TYPE" jdbcType="INTEGER" property="notificationReceiverType" />
      <result column="NOTIFICATION_RECEIVERS" jdbcType="VARCHAR" property="notificationReceivers" />
      <result column="PUBLISH_TYPE" jdbcType="INTEGER" property="publishType" />
      <result column="PUBLISH_STATUS" jdbcType="INTEGER" property="publishStatus" />
      <result column="PUSH_TIME" jdbcType="TIMESTAMP" property="pushTime" />
      <result column="DELETED" jdbcType="TINYINT" property="deleted" />
      <result column="POP" jdbcType="TINYINT" property="pop" />
    </association>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, VERSION, CREATION_TIME, UPDATE_TIME, NOTIFICATION_ID, NOTIFICATION_TYPE, NOTIFICATION_TITLE,
    READ_STATUS, READ_TIME, READ_OPERATOR_ID, READ_OPERATOR_NAME, MCH_NO, MCH_NAME
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tbl_notification_record_detail
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="VERSION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATION_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.creationTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_TITLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationTitle,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="READ_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.readStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="READ_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.readTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="READ_OPERATOR_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.readOperatorId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="READ_OPERATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.readOperatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MCH_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.mchNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MCH_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=BIGINT} then #{item.mchName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tbl_notification_record_detail
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="VERSION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATION_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creationTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.creationTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.notificationId != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.notificationType != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOTIFICATION_TITLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.notificationTitle != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.notificationTitle,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="READ_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.readStatus != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.readStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="READ_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.readTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.readTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="READ_OPERATOR_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.readOperatorId != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.readOperatorId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="READ_OPERATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.readOperatorName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.readOperatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MCH_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mchNo != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.mchNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MCH_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mchName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.mchName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_notification_record_detail
    (VERSION, CREATION_TIME, UPDATE_TIME, NOTIFICATION_ID, NOTIFICATION_TYPE, NOTIFICATION_TITLE,
    READ_STATUS, READ_TIME, READ_OPERATOR_ID, READ_OPERATOR_NAME, MCH_NO, MCH_NAME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.version,jdbcType=INTEGER}, #{item.creationTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.notificationId,jdbcType=BIGINT}, #{item.notificationType,jdbcType=INTEGER},
      #{item.notificationTitle,jdbcType=VARCHAR}, #{item.readStatus,jdbcType=INTEGER},
      #{item.readTime,jdbcType=TIMESTAMP}, #{item.readOperatorId,jdbcType=BIGINT}, #{item.readOperatorName,jdbcType=VARCHAR},
      #{item.mchNo,jdbcType=VARCHAR}, #{item.mchName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="ID" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_notification_record_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      VERSION,
      CREATION_TIME,
      UPDATE_TIME,
      NOTIFICATION_ID,
      NOTIFICATION_TYPE,
      NOTIFICATION_TITLE,
      READ_STATUS,
      READ_TIME,
      READ_OPERATOR_ID,
      READ_OPERATOR_NAME,
      MCH_NO,
      MCH_NAME,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{version,jdbcType=INTEGER},
      #{creationTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP},
      #{notificationId,jdbcType=BIGINT},
      #{notificationType,jdbcType=INTEGER},
      #{notificationTitle,jdbcType=VARCHAR},
      #{readStatus,jdbcType=INTEGER},
      #{readTime,jdbcType=TIMESTAMP},
      #{readOperatorId,jdbcType=BIGINT},
      #{readOperatorName,jdbcType=VARCHAR},
      #{mchNo,jdbcType=VARCHAR},
      #{mchName,jdbcType=VARCHAR},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=BIGINT},
      </if>
      VERSION = #{version,jdbcType=INTEGER},
      CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      NOTIFICATION_ID = #{notificationId,jdbcType=BIGINT},
      NOTIFICATION_TYPE = #{notificationType,jdbcType=INTEGER},
      NOTIFICATION_TITLE = #{notificationTitle,jdbcType=VARCHAR},
      READ_STATUS = #{readStatus,jdbcType=INTEGER},
      READ_TIME = #{readTime,jdbcType=TIMESTAMP},
      READ_OPERATOR_ID = #{readOperatorId,jdbcType=BIGINT},
      READ_OPERATOR_NAME = #{readOperatorName,jdbcType=VARCHAR},
      MCH_NO = #{mchNo,jdbcType=VARCHAR},
      MCH_NAME = #{mchName,jdbcType=VARCHAR},
    </trim>
  </insert>

  <insert id="insertOrUpdateSelective" keyColumn="ID" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_notification_record_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="version != null">
        VERSION,
      </if>
      <if test="creationTime != null">
        CREATION_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="notificationId != null">
        NOTIFICATION_ID,
      </if>
      <if test="notificationType != null">
        NOTIFICATION_TYPE,
      </if>
      <if test="notificationTitle != null and notificationTitle != ''">
        NOTIFICATION_TITLE,
      </if>
      <if test="readStatus != null">
        READ_STATUS,
      </if>
      <if test="readTime != null">
        READ_TIME,
      </if>
      <if test="readOperatorId != null">
        READ_OPERATOR_ID,
      </if>
      <if test="readOperatorName != null and readOperatorName != ''">
        READ_OPERATOR_NAME,
      </if>
      <if test="mchNo != null and mchNo != ''">
        MCH_NO,
      </if>
      <if test="mchName != null and mchName != ''">
        MCH_NAME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notificationId != null">
        #{notificationId,jdbcType=BIGINT},
      </if>
      <if test="notificationType != null">
        #{notificationType,jdbcType=INTEGER},
      </if>
      <if test="notificationTitle != null and notificationTitle != ''">
        #{notificationTitle,jdbcType=VARCHAR},
      </if>
      <if test="readStatus != null">
        #{readStatus,jdbcType=INTEGER},
      </if>
      <if test="readTime != null">
        #{readTime,jdbcType=TIMESTAMP},
      </if>
      <if test="readOperatorId != null">
        #{readOperatorId,jdbcType=BIGINT},
      </if>
      <if test="readOperatorName != null and readOperatorName != ''">
        #{readOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="mchNo != null and mchNo != ''">
        #{mchNo,jdbcType=VARCHAR},
      </if>
      <if test="mchName != null and mchName != ''">
        #{mchName,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        VERSION = #{version,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        CREATION_TIME = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notificationId != null">
        NOTIFICATION_ID = #{notificationId,jdbcType=BIGINT},
      </if>
      <if test="notificationType != null">
        NOTIFICATION_TYPE = #{notificationType,jdbcType=INTEGER},
      </if>
      <if test="notificationTitle != null and notificationTitle != ''">
        NOTIFICATION_TITLE = #{notificationTitle,jdbcType=VARCHAR},
      </if>
      <if test="readStatus != null">
        READ_STATUS = #{readStatus,jdbcType=INTEGER},
      </if>
      <if test="readTime != null">
        READ_TIME = #{readTime,jdbcType=TIMESTAMP},
      </if>
      <if test="readOperatorId != null">
        READ_OPERATOR_ID = #{readOperatorId,jdbcType=BIGINT},
      </if>
      <if test="readOperatorName != null and readOperatorName != ''">
        READ_OPERATOR_NAME = #{readOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="mchNo != null and mchNo != ''">
        MCH_NO = #{mchNo,jdbcType=VARCHAR},
      </if>
      <if test="mchName != null and mchName != ''">
        MCH_NAME = #{mchName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="listNotificationRecordFullInfo" parameterType="java.util.Map" resultMap="detailFullInfoResult">
   select
    a.*,
    b.*
    from
    tbl_notification_record_detail a
    inner join tbl_notification_record b on
    a.NOTIFICATION_ID = b.ID
    <where>
      b.DELETED = 0
      and b.NOTIFICATION_RECEIVER_TYPE
      in <foreach collection="param.notificationReceiverType" item="item" index="index" open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    <if test="param.mchNo != null and param.mchNo != ''">
     and  a.MCH_NO = #{param.mchNo,jdbcType=VARCHAR}
    </if>
      <if test="param.readStatus != null">
       and a.READ_STATUS = #{param.readStatus,jdbcType=INTEGER}
      </if>
      <if test="param.publishStatus != null">
        and b.PUBLISH_STATUS = #{param.publishStatus}
      </if>
    </where>
    <choose>
      <when test="param.sortColumns != null and param.sortColumns !='' ">
        <![CDATA[ ORDER BY ${param.sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY a.ID DESC ]]>
      </otherwise>
    </choose>
  </select>
</mapper>
