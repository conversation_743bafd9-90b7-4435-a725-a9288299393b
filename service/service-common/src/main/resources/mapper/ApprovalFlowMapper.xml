<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow">
    <sql id="table">tbl_approval_flow</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="STEP_NUM" property="stepNum" jdbcType="INTEGER"/>
        <result column="INITIATOR_ID" property="initiatorId" jdbcType="BIGINT"/>
        <result column="INITIATOR_NAME" property="initiatorName" jdbcType="VARCHAR"/>
        <result column="FLOW_TOPIC_TYPE" property="flowTopicType" jdbcType="SMALLINT"/>
        <result column="FLOW_TOPIC_NAME" property="flowTopicName" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="EXT_INFO" property="extInfo" jdbcType="OTHER"/>
        <result column="PLATFORM" property="platform" jdbcType="SMALLINT"/>
        <result column="JSON_INFO" property="jsonInfo" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, VERSION, STEP_NUM, INITIATOR_ID, INITIATOR_NAME, FLOW_TOPIC_TYPE, FLOW_TOPIC_NAME, CREATE_TIME, UPDATE_TIME, END_TIME, STATUS, EXT_INFO, PLATFORM, JSON_INFO
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow">
        INSERT INTO <include refid="table" /> (
        VERSION,
        STEP_NUM,
        INITIATOR_ID,
        INITIATOR_NAME,
        FLOW_TOPIC_TYPE,
        FLOW_TOPIC_NAME,
        CREATE_TIME,
        UPDATE_TIME,
        END_TIME,
        STATUS,
        EXT_INFO,
        PLATFORM,
        JSON_INFO
        ) VALUES (
        #{version,jdbcType=INTEGER},
        #{stepNum,jdbcType=INTEGER},
        #{initiatorId,jdbcType=BIGINT},
        #{initiatorName,jdbcType=VARCHAR},
        #{flowTopicType,jdbcType=SMALLINT},
        #{flowTopicName,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{status,jdbcType=SMALLINT},
        #{extInfo,jdbcType=OTHER},
        #{platform,jdbcType=SMALLINT},
        #{jsonInfo,jdbcType=OTHER}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        STEP_NUM,
        INITIATOR_ID,
        INITIATOR_NAME,
        FLOW_TOPIC_TYPE,
        FLOW_TOPIC_NAME,
        CREATE_TIME,
        UPDATE_TIME,
        END_TIME,
        STATUS,
        EXT_INFO,
        PLATFORM,
        JSON_INFO
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.stepNum,jdbcType=INTEGER},
            #{item.initiatorId,jdbcType=BIGINT},
            #{item.initiatorName,jdbcType=VARCHAR},
            #{item.flowTopicType,jdbcType=SMALLINT},
            #{item.flowTopicName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.endTime,jdbcType=TIMESTAMP},
            #{item.status,jdbcType=SMALLINT},
            #{item.extInfo,jdbcType=OTHER},
            #{item.platform,jdbcType=SMALLINT},
            #{item.jsonInfo,jdbcType=OTHER}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow">
        UPDATE <include refid="table" /> SET
        VERSION = #{version,jdbcType=INTEGER} +1,
        STEP_NUM = #{stepNum,jdbcType=INTEGER},
        INITIATOR_ID = #{initiatorId,jdbcType=BIGINT},
        INITIATOR_NAME = #{initiatorName,jdbcType=VARCHAR},
        FLOW_TOPIC_TYPE = #{flowTopicType,jdbcType=SMALLINT},
        FLOW_TOPIC_NAME = #{flowTopicName,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
        STATUS = #{status,jdbcType=SMALLINT},
        EXT_INFO = #{extInfo,jdbcType=OTHER},
        PLATFORM = #{platform,jdbcType=SMALLINT},
        JSON_INFO = #{jsonInfo,jdbcType=OTHER}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="stepNum != null">
                STEP_NUM = #{stepNum,jdbcType=INTEGER},
            </if>
            <if test="initiatorId != null">
                INITIATOR_ID = #{initiatorId,jdbcType=BIGINT},
            </if>
            <if test="initiatorName != null">
                INITIATOR_NAME = #{initiatorName,jdbcType=VARCHAR},
            </if>
            <if test="flowTopicType != null">
                FLOW_TOPIC_TYPE = #{flowTopicType,jdbcType=SMALLINT},
            </if>
            <if test="flowTopicName != null">
                FLOW_TOPIC_NAME = #{flowTopicName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="extInfo != null">
                EXT_INFO = #{extInfo,jdbcType=OTHER},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=SMALLINT},
            </if>
            <if test="jsonInfo != null">
                JSON_INFO = #{jsonInfo,jdbcType=OTHER}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="idList != null and idList.size() > 0">
                and ID IN
                <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=INTEGER}
            </if>
            <if test="stepNum != null">
                and STEP_NUM = #{stepNum,jdbcType=INTEGER}
            </if>
            <if test="initiatorId != null">
                and INITIATOR_ID = #{initiatorId,jdbcType=BIGINT}
            </if>
            <if test="initiatorName != null and initiatorName !=''">
                and INITIATOR_NAME = #{initiatorName,jdbcType=VARCHAR}
            </if>
            <if test="initiatorNameLike != null and initiatorNameLike !=''">
                and INITIATOR_NAME like concat('%',#{initiatorNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="flowTopicType != null">
                and FLOW_TOPIC_TYPE = #{flowTopicType,jdbcType=SMALLINT}
            </if>
            <if test="flowTopicName != null and flowTopicName !=''">
                and FLOW_TOPIC_NAME = #{flowTopicName,jdbcType=VARCHAR}
            </if>
            <if test="flowTopicNameLike != null and flowTopicNameLike !=''">
                and FLOW_TOPIC_NAME like concat('%',#{flowTopicNameLike,jdbcType=VARCHAR},'%')
            </if>

            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and END_TIME = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null">
                and STATUS = #{status,jdbcType=SMALLINT}
            </if>
            <if test="extInfo != null and extInfo !=''">
                and EXT_INFO = #{extInfo,jdbcType=OTHER}
            </if>
            <if test="platform != null">
                and PLATFORM = #{platform,jdbcType=SMALLINT}
            </if>
            <if test="jsonInfo != null and jsonInfo !=''">
                and JSON_INFO = #{jsonInfo,jdbcType=OTHER}
            </if>
            <if test="beginDate != null and endDate != null and beginDate != '' and endDate != ''">
                AND CREATE_TIME BETWEEN  #{beginDate} AND #{endDate}
            </if>
            <if test="mchNo!= null and mchNo !=''">
                and EXT_INFO->'$.mchNo' = #{mchNo,jdbcType=VARCHAR}
            </if>
            <if test="dimension != null and dimension !=''">
                and JSON_INFO->'$.dimension' = #{dimension, jdbcType=VARCHAR}
            </if>
    </sql>

</mapper>
