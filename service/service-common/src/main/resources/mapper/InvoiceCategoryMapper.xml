<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.common.entity.config.InvoiceCategory">
	<sql id="table"> tbl_invoice_category </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.InvoiceCategory">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="INVOICE_CATEGORY_CODE" property="invoiceCategoryCode" jdbcType="VARCHAR"/>
		<result column="INVOICE_CATEGORY_NAME" property="invoiceCategoryName" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		VERSION,
		UPDATE_TIME,
		UPDATOR,
		INVOICE_CATEGORY_CODE,
		INVOICE_CATEGORY_NAME
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.config.InvoiceCategory">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
        	UPDATE_TIME ,
        	UPDATOR ,
        	INVOICE_CATEGORY_CODE ,
        	INVOICE_CATEGORY_NAME 
        ) VALUES (
			#{createTime,jdbcType=TIMESTAMP},
			0,
			#{updateTime,jdbcType=TIMESTAMP},
			#{updator,jdbcType=VARCHAR},
			#{invoiceCategoryCode,jdbcType=VARCHAR},
			#{invoiceCategoryName,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	CREATE_TIME ,
        	VERSION ,
        	UPDATE_TIME ,
        	UPDATOR ,
        	INVOICE_CATEGORY_CODE ,
        	INVOICE_CATEGORY_NAME 
        ) VALUES 
		<foreach collection="list" item="item" separator=",">
			(
			#{item.createTime,jdbcType=TIMESTAMP},
			0,
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.updator,jdbcType=VARCHAR},
			#{item.invoiceCategoryCode,jdbcType=VARCHAR},
			#{item.invoiceCategoryName,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.common.entity.config.InvoiceCategory">
        UPDATE <include refid="table" /> SET
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			UPDATOR = #{updator,jdbcType=VARCHAR},
			INVOICE_CATEGORY_CODE = #{invoiceCategoryCode,jdbcType=VARCHAR},
			INVOICE_CATEGORY_NAME = #{invoiceCategoryName,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.config.InvoiceCategory">
		UPDATE <include refid="table" />
		<set>
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="updateTime != null">
				UPDATE_TIME =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updator != null">
				UPDATOR =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="invoiceCategoryCode != null">
				INVOICE_CATEGORY_CODE =#{invoiceCategoryCode,jdbcType=VARCHAR},
			</if>
			<if test="invoiceCategoryName != null">
				INVOICE_CATEGORY_NAME =#{invoiceCategoryName,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>
	
	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		<choose>
			<when test="sortColumns != null and sortColumns !='' ">
				<![CDATA[ ORDER BY ${sortColumns} ]]>
			</when>
			<otherwise>
				<![CDATA[ ORDER BY ID DESC ]]>
			</otherwise>
		</choose>
	</select>
	
	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>
	
	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" /> 
		FROM <include refid="table" /> 
		WHERE ID = #{id,jdbcType=BIGINT}  
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="invoiceCategoryCode != null and invoiceCategoryCode !=''">
			and INVOICE_CATEGORY_CODE = #{invoiceCategoryCode,jdbcType=VARCHAR}
		</if>
		<if test="invoiceCategoryName != null and invoiceCategoryName !=''">
			and INVOICE_CATEGORY_NAME = #{invoiceCategoryName,jdbcType=VARCHAR}
		</if>
		<if test="invoiceCategoryNameLike != null and invoiceCategoryNameLike !=''">
			and INVOICE_CATEGORY_NAME like CONCAT(CONCAT('%', #{invoiceCategoryNameLike,jdbcType=VARCHAR}), '%')
		</if>
	</sql>
</mapper>

