<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.InvoiceBookingConfigMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.InvoiceBookingConfig">
    <!--@mbg.generated-->
    <!--@Table tbl_invoice_booking_config-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employer_no" jdbcType="VARCHAR" property="employerNo" />
    <result column="employer_name" jdbcType="VARCHAR" property="employerName" />
    <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName" />
    <result column="apply_period" jdbcType="INTEGER" property="applyPeriod" />
    <result column="apply_date" jdbcType="INTEGER" property="applyDate" />
    <result column="apply_type" jdbcType="INTEGER" property="applyType" />
    <result column="invoice_category_code" jdbcType="VARCHAR" property="invoiceCategoryCode" />
    <result column="invoice_category_name" jdbcType="VARCHAR" property="invoiceCategoryName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, employer_no, employer_name, mainstay_no, mainstay_name, apply_period, apply_date,
    apply_type, invoice_category_code, invoice_category_name, update_time, update_by,status,invoice_type
  </sql>
</mapper>
