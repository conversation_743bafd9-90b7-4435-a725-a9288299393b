<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.AlipayMccMapper">
    <sql id="table">tbl_alipay_mcc</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.AlipayMcc">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="profession_type" property="professionType" jdbcType="VARCHAR"/>
        <result column="profession_name" property="professionName" jdbcType="VARCHAR"/>
        <result column="qualification_code" property="qualificationCode" jdbcType="VARCHAR"/>
        <result column="qualification_name" property="qualificationName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, profession_type, profession_name, qualification_code, qualification_name, description
    </sql>

    <resultMap id="alipayMccMap" type="com.zhixianghui.facade.common.vo.AlipayMccVo">
        <id column="profession_type" property="professionType"/>
        <result column="profession_name" property="professionName"/>
        <result column="description" property="description"/>
        <collection property="qualification" ofType="com.zhixianghui.facade.common.vo.QualificationVo" javaType="java.util.ArrayList">
            <result column="qualification_code" property="qualificationCode"/>
            <result column="qualification_name" property="qualificationName"/>
        </collection>
    </resultMap>

    <select id="listAll" resultMap="alipayMccMap">
        select * from <include refid="table"/> order by profession_type asc
    </select>


</mapper>
