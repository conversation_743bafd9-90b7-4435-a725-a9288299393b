<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.report.EmployerAccountInfo">
    <sql id="table">tbl_employer_account_info</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.report.EmployerAccountInfo">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="MCH_NAME" property="mchName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_NAME" property="payChannelName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="SUB_MERCHANT_NO" property="subMerchantNo" jdbcType="VARCHAR"/>
        <result column="PARENT_MERCHANT_NO" property="parentMerchantNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_KEY" property="employerKey" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_OPERATOR" property="updateOperator" jdbcType="VARCHAR"/>
        <result column="CREATE_OPERATOR" property="createOperator" jdbcType="VARCHAR"/>
        <result column="PARENT_AGREEMENT_NO" property="parentAgreementNo" jdbcType="VARCHAR"/>
        <result column="SUB_AGREEMENT_NO" property="subAgreementNo" jdbcType="VARCHAR"/>
        <result column="PARENT_ALIPAY_USER_ID" property="parentAlipayUserId" jdbcType="VARCHAR"/>
        <result column="SUB_ALIPAY_USER_ID" property="subAlipayUserId" jdbcType="VARCHAR"/>
        <result column="ALIPAY_CARD_NO" property="alipayCardNo" jdbcType="VARCHAR"/>
    </resultMap>

    <!--  自定义分页dto  -->
    <resultMap id="PageResultMap" type="com.zhixianghui.facade.common.dto.EmployerAccountInfoDto">
        <id column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <id column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MCH_NAME" property="mchName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <collection property="employerChannels" ofType="com.zhixianghui.facade.common.dto.EmployerChannel">
            <result column="EMPLOYER_KEY" property="employerKey" jdbcType="VARCHAR"/>
            <result column="PAY_CHANNEL_NAME" property="payChannelName" jdbcType="VARCHAR"/>
            <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
            <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
            <result column="STATUS" property="status" jdbcType="SMALLINT"/>
            <result column="SUB_MERCHANT_NO" property="subMerchantNo" jdbcType="VARCHAR"/>
            <result column="PARENT_MERCHANT_NO" property="parentMerchantNo" jdbcType="VARCHAR"/>
            <result column="ALIPAY_CARD_NO" property="alipayCardNo" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, MCH_NAME,EMPLOYER_NAME, EMPLOYER_NO, MAINSTAY_NAME, MAINSTAY_NO, PAY_CHANNEL_NAME, CHANNEL_TYPE, PAY_CHANNEL_NO, STATUS, SUB_MERCHANT_NO, PARENT_MERCHANT_NO, EMPLOYER_KEY, CREATE_TIME, UPDATE_TIME, UPDATE_OPERATOR, CREATE_OPERATOR,PARENT_AGREEMENT_NO,SUB_AGREEMENT_NO,PARENT_ALIPAY_USER_ID,SUB_ALIPAY_USER_ID,ALIPAY_CARD_NO
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.report.EmployerAccountInfo">
        INSERT INTO <include refid="table" /> (
        VERSION,
        MCH_NAME,
        EMPLOYER_NAME,
        EMPLOYER_NO,
        MAINSTAY_NAME,
        MAINSTAY_NO,
        PAY_CHANNEL_NAME,
        CHANNEL_TYPE,
        PAY_CHANNEL_NO,
        STATUS,
        SUB_MERCHANT_NO,
        PARENT_MERCHANT_NO,
        EMPLOYER_KEY,
        CREATE_TIME,
        UPDATE_TIME,
        UPDATE_OPERATOR,
        CREATE_OPERATOR,
        PARENT_AGREEMENT_NO,
        SUB_AGREEMENT_NO,
        PARENT_ALIPAY_USER_ID,
        SUB_ALIPAY_USER_ID,
        ALIPAY_CARD_NO
        ) VALUES (
        #{version,jdbcType=INTEGER},
        #{mchName,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{payChannelName,jdbcType=VARCHAR},
        #{channelType,jdbcType=SMALLINT},
        #{payChannelNo,jdbcType=VARCHAR},
        #{status,jdbcType=SMALLINT},
        #{subMerchantNo,jdbcType=VARCHAR},
        #{parentMerchantNo,jdbcType=VARCHAR},
        #{employerKey,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{updateOperator,jdbcType=VARCHAR},
        #{createOperator,jdbcType=VARCHAR},
        #{parentAgreementNo,jdbcType=VARCHAR},
        #{subAgreementNo,jdbcType=VARCHAR},
        #{parentAlipayUserId,jdbcType=VARCHAR},
        #{subAlipayUserId,jdbcType=VARCHAR},
        #{alipayCardNo,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO <include refid="table" /> (
        VERSION,
        MCH_NAME,
        EMPLOYER_NAME,
        EMPLOYER_NO,
        MAINSTAY_NAME,
        MAINSTAY_NO,
        PAY_CHANNEL_NAME,
        CHANNEL_TYPE,
        PAY_CHANNEL_NO,
        STATUS,
        SUB_MERCHANT_NO,
        PARENT_MERCHANT_NO,
        EMPLOYER_KEY,
        CREATE_TIME,
        UPDATE_TIME,
        UPDATE_OPERATOR,
        CREATE_OPERATOR,
        PARENT_AGREEMENT_NO,
        SUB_AGREEMENT_NO,
        PARENT_ALIPAY_USER_ID,
        SUB_ALIPAY_USER_ID,
        ALIPAY_CARD_NO
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.mchName,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.payChannelName,jdbcType=VARCHAR},
            #{item.channelType,jdbcType=SMALLINT},
            #{item.payChannelNo,jdbcType=VARCHAR},
            #{item.status,jdbcType=SMALLINT},
            #{item.subMerchantNo,jdbcType=VARCHAR},
            #{item.parentMerchantNo,jdbcType=VARCHAR},
            #{item.employerKey,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateOperator,jdbcType=VARCHAR},
            #{item.createOperator,jdbcType=VARCHAR},
            #{item.parentAgreementNo,jdbcType=VARCHAR},
            #{item.subAgreementNo,jdbcType=VARCHAR},
            #{item.parentAlipayUserId,jdbcType=VARCHAR},
            #{item.subAlipayUserId,jdbcType=VARCHAR},
            #{item.alipayCardNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.report.EmployerAccountInfo">
        UPDATE <include refid="table" /> SET
        VERSION = #{version,jdbcType=INTEGER} +1,
        MCH_NAME = #{mchName,jdbcType=VARCHAR},
        EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
        EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
        MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
        MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
        PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},
        CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
        PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
        STATUS = #{status,jdbcType=SMALLINT},
        SUB_MERCHANT_NO = #{subMerchantNo,jdbcType=VARCHAR},
        PARENT_MERCHANT_NO = #{parentMerchantNo,jdbcType=VARCHAR},
        EMPLOYER_KEY = #{employerKey,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
        CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
        PARENT_AGREEMENT_NO = #{parentAgreementNo,jdbcType=VARCHAR},
        SUB_AGREEMENT_NO = #{subAgreementNo,jdbcType=VARCHAR},
        PARENT_ALIPAY_USER_ID = #{parentAlipayUserId,jdbcType=VARCHAR},
        SUB_ALIPAY_USER_ID = #{subAlipayUserId,jdbcType=VARCHAR},
        ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.report.EmployerAccountInfo">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="mchName != null">
                MCH_NAME = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="employerName != null">
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
            </if>
            <if test="employerNo != null">
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null">
                CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
            </if>
            <if test="payChannelNo != null">
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="subMerchantNo != null">
                SUB_MERCHANT_NO = #{subMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="parentMerchantNo != null">
                PARENT_MERCHANT_NO = #{parentMerchantNo,jdbcType=VARCHAR},
            </if>
            <if test="employerKey != null">
                EMPLOYER_KEY = #{employerKey,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOperator != null">
                UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
            </if>
            <if test="createOperator != null">
                CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
            </if>
            <if test="parentAgreementNo != null">
                PARENT_AGREEMENT_NO = #{parentAgreementNo,jdbcType=VARCHAR},
            </if>
            <if test="subAgreementNo != null">
                SUB_AGREEMENT_NO = #{subAgreementNo,jdbcType=VARCHAR},
            </if>
            <if test="parentAlipayUserId != null">
                PARENT_ALIPAY_USER_ID = #{parentAlipayUserId,jdbcType=VARCHAR},
            </if>
            <if test="subAlipayUserId != null">
                SUB_ALIPAY_USER_ID = #{subAlipayUserId,jdbcType=VARCHAR}
            </if>
            <if test="alipayCardNo != null and alipayCardNo != ''">
                ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 自定义分页查询 -->
    <select id="listCustomPage" parameterType="java.util.Map" resultMap="PageResultMap">
        <choose>
            <when test="channelNo != null and channelNo != ''">
                select a.* from tbl_employer_account_info a ,(select EMPLOYER_NO,MAINSTAY_NO from tbl_employer_account_info where PAY_CHANNEL_NO = #{channelNo}
                GROUP BY EMPLOYER_NO,MAINSTAY_NO) b where a.EMPLOYER_NO = b.EMPLOYER_NO and a.MAINSTAY_NO = b.MAINSTAY_NO
                <include refid="join_condition_sql" />
            </when>
            <otherwise>
                SELECT
                <include refid="Base_Column_List" />
                FROM
                <include refid="table" />
                <where>
                    <include refid="condition_sql" />
                </where>
            </otherwise>
        </choose>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        <choose>
            <when test="channelNo != null and channelNo != ''">
                select count(1) from tbl_employer_account_info a ,(select EMPLOYER_NO,MAINSTAY_NO from tbl_employer_account_info where PAY_CHANNEL_NO = #{channelNo}
                GROUP BY EMPLOYER_NO,MAINSTAY_NO) b where a.EMPLOYER_NO = b.EMPLOYER_NO and a.MAINSTAY_NO = b.MAINSTAY_NO
                <include refid="join_condition_sql" /> order by id desc
            </when>
            <otherwise>
                SELECT COUNT(1) FROM
                <include refid="table" />
                <where>
                    <include refid="condition_sql" />
                </where>
            </otherwise>
        </choose>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!--条件删除-->
    <delete id="deleteBy" parameterType="java.util.Map">
        DELETE FROM <include refid="table" />
        where
        <trim  prefixOverrides="AND">
            <include refid="condition_sql" />
        </trim>
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="mchName != null and mchName != ''">
            and MCH_NAME = #{mchName,jdbcType=VARCHAR}
        </if>
        <if test="mchNameLike != null and mchNameLike != ''">
            and MCH_NAME like concat('%',#{mchNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
        </if>
        <if test="employerNameLike != null and employerNameLike !=''">
            and EMPLOYER_NAME like concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNameLike != null and mainstayNameLike !=''">
            and MAINSTAY_NAME like concat('%',#{mainstayNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="payChannelName != null and payChannelName !=''">
            and PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR}
        </if>
        <if test="payChannelNameLike != null and payChannelNameLike !=''">
            and PAY_CHANNEL_NAME like concat('%',#{payChannelNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="channelType != null">
            and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
        </if>
        <if test="payChannelNo != null and payChannelNo !=''">
            and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="subMerchantNo != null and subMerchantNo !=''">
            and SUB_MERCHANT_NO = #{subMerchantNo,jdbcType=VARCHAR}
        </if>
        <if test="parentMerchantNo != null and parentMerchantNo !=''">
            and PARENT_MERCHANT_NO = #{parentMerchantNo,jdbcType=VARCHAR}
        </if>
        <if test="employerKey != null and employerKey !=''">
            and EMPLOYER_KEY = #{employerKey,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateOperator != null and updateOperator !=''">
            and UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        </if>
        <if test="createOperator != null and createOperator !=''">
            and CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
        </if>
        <if test="parentAgreementNo != null and parentAgreementNo !=''">
            and PARENT_AGREEMENT_NO = #{parentAgreementNo,jdbcType=VARCHAR}
        </if>
        <if test="subAgreementNo != null and subAgreementNo !=''">
            and SUB_AGREEMENT_NO = #{subAgreementNo,jdbcType=VARCHAR}
        </if>
        <if test="parentAlipayUserId != null and parentAlipayUserId !=''">
            and PARENT_ALIPAY_USER_ID = #{parentAlipayUserId,jdbcType=VARCHAR}
        </if>
        <if test="subAlipayUserId != null and subAlipayUserId !=''">
            and SUB_ALIPAY_USER_ID = #{subAlipayUserId,jdbcType=VARCHAR}
        </if>
        <if test="alipayCardNo != null and alipayCardNo != ''">
            and ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="join_condition_sql">
        <if test="id != null">
            and a.ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and a.VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="mchName != null and mchName != ''">
            and a.MCH_NAME = #{mchName,jdbcType=VARCHAR}
        </if>
        <if test="mchNameLike != null and mchNameLike != ''">
            and a.MCH_NAME like concat('%',#{mchNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="employerName != null and employerName !=''">
            and a.EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
        </if>
        <if test="employerNameLike != null and employerNameLike !=''">
            and a.EMPLOYER_NAME like concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="employerNo != null and employerNo !=''">
            and a.EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and a.MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNameLike != null and mainstayNameLike !=''">
            and a.MAINSTAY_NAME like concat('%',#{mainstayNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and a.MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="payChannelName != null and payChannelName !=''">
            and a.PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR}
        </if>
        <if test="payChannelNameLike != null and payChannelNameLike !=''">
            and a.PAY_CHANNEL_NAME like concat('%',#{payChannelNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="channelType != null">
            and a.CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
        </if>
        <if test="payChannelNo != null and payChannelNo !=''">
            and a.PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and a.STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="subMerchantNo != null and subMerchantNo !=''">
            and a.SUB_MERCHANT_NO = #{subMerchantNo,jdbcType=VARCHAR}
        </if>
        <if test="parentMerchantNo != null and parentMerchantNo !=''">
            and a.PARENT_MERCHANT_NO = #{parentMerchantNo,jdbcType=VARCHAR}
        </if>
        <if test="employerKey != null and employerKey !=''">
            and a.EMPLOYER_KEY = #{employerKey,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and a.CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and a.UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateOperator != null and updateOperator !=''">
            and a.UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        </if>
        <if test="createOperator != null and createOperator !=''">
            and a.CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
        </if>
        <if test="parentAgreementNo != null and parentAgreementNo !=''">
            and a.PARENT_AGREEMENT_NO = #{parentAgreementNo,jdbcType=VARCHAR}
        </if>
        <if test="subAgreementNo != null and subAgreementNo !=''">
            and a.SUB_AGREEMENT_NO = #{subAgreementNo,jdbcType=VARCHAR}
        </if>
        <if test="parentAlipayUserId != null and parentAlipayUserId !=''">
            and a.PARENT_ALIPAY_USER_ID = #{parentAlipayUserId,jdbcType=VARCHAR}
        </if>
        <if test="subAlipayUserId != null and subAlipayUserId !=''">
            and a.SUB_ALIPAY_USER_ID = #{subAlipayUserId,jdbcType=VARCHAR}
        </if>
        <if test="alipayCardNo != null and alipayCardNo != ''">
            and a.ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR}
        </if>
    </sql>

    <update id="updateStatusByMainstayNoAndEmployerNo" parameterType="java.util.Map" >
        UPDATE <include refid="table" />
        SET STATUS = #{status,jdbcType=SMALLINT},
        UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        WHERE EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR} AND MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
    </update>

    <update id="updateStatusByPayChannelNo" parameterType="java.util.Map" >
        UPDATE <include refid="table" />
        SET STATUS = #{status,jdbcType=SMALLINT},
        UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        WHERE PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
    </update>

    <update id="updateStatusByMainstayNoAndPayChannelNo" parameterType="java.util.Map" >
        UPDATE <include refid="table" />
        SET STATUS = #{status,jdbcType=SMALLINT},
        UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        WHERE PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR} AND MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
    </update>

    <update id="clearInfoByPayChannelNo" parameterType="java.util.Map" >
        UPDATE <include refid="table" />
        SET STATUS = #{status,jdbcType=SMALLINT},
        PAY_CHANNEL_NO = "",
        SUB_MERCHANT_NO="",
        PARENT_MERCHANT_NO="",
        EMPLOYER_KEY=""
        WHERE PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
    </update>

    <update id="clearInfoByMainstayNo" parameterType="java.util.Map" >
        UPDATE <include refid="table" />
        SET STATUS = #{status,jdbcType=SMALLINT},
        PAY_CHANNEL_NAME= "",
        PAY_CHANNEL_NO = "",
        SUB_MERCHANT_NO="",
        PARENT_MERCHANT_NO="",
        EMPLOYER_KEY=""
        WHERE MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
    </update>

    <delete id="deleteByMainstayNoAndEmployerNo" parameterType="java.util.Map">
        DELETE FROM <include refid="table" /> WHERE EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR} AND MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
    </delete>

    <select id="groupByMch" parameterType="java.util.Map" resultType="java.util.Map">
        select
        EMPLOYER_NO employerNo,
        EMPLOYER_NAME employerName
        from tbl_employer_account_info t
        <where>
            <if test="employerLike != null and employerLike != ''">
                and (
                EMPLOYER_NO like concat('%',#{employerLike,jdbcType=VARCHAR},'%')
                or EMPLOYER_NAME like concat('%',#{employerLike,jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="mainstayLike != null and mainstayLike != ''">
                and (
                MAINSTAY_NAME like concat('%',#{mainstayLike,jdbcType=VARCHAR},'%')
                or MAINSTAY_NO like concat('%',#{mainstayLike,jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null">
                and STATUS = #{status,jdbcType=INTEGER}
            </if>
        </where>
        group by EMPLOYER_NO,EMPLOYER_NAME
    </select>

    <select id="groupByMchCount" parameterType="java.util.Map" resultType="long">
        select count(1) from
        (select
        EMPLOYER_NO employerNo,
        EMPLOYER_NAME employerName
        from tbl_employer_account_info t
        <where>
            <if test="employerLike != null and employerLike != ''">
                and (
                EMPLOYER_NO like concat('%',#{employerLike,jdbcType=VARCHAR},'%')
                or EMPLOYER_NAME like concat('%',#{employerLike,jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="mainstayLike != null and mainstayLike != ''">
                and (
                MAINSTAY_NAME like concat('%',#{mainstayLike,jdbcType=VARCHAR},'%')
                or MAINSTAY_NO like concat('%',#{mainstayLike,jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null">
                and STATUS = #{status,jdbcType=INTEGER}
            </if>
        </where>
        group by EMPLOYER_NO,EMPLOYER_NAME) as tmp
    </select>
</mapper>
