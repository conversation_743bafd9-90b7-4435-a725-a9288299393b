<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.config.AreaCity">
    <sql id="table">tbl_area_city</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.AreaCity">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="PROVINCE_NO" property="provinceNo" jdbcType="VARCHAR"/>
        <result column="CITY_NO" property="cityNo" jdbcType="VARCHAR"/>
        <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"/>
        <result column="ORDERS" property="orders" jdbcType="SMALLINT"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PROVINCE_NO, CITY_NO, CITY_NAME, ORDERS
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.config.AreaCity">
        INSERT INTO <include refid="table" /> (
            PROVINCE_NO,
            CITY_NO,
            CITY_NAME,
            ORDERS
        ) VALUES (
        #{provinceNo,jdbcType=VARCHAR},
        #{cityNo,jdbcType=VARCHAR},
        #{cityName,jdbcType=VARCHAR},
        #{orders,jdbcType=SMALLINT}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.config.AreaCity">
        UPDATE <include refid="table" /> SET
                PROVINCE_NO = #{provinceNo,jdbcType=VARCHAR},
                CITY_NO = #{cityNo,jdbcType=VARCHAR},
                CITY_NAME = #{cityName,jdbcType=VARCHAR},
                ORDERS = #{orders,jdbcType=SMALLINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.config.AreaCity">
        UPDATE <include refid="table" />
        <set>
            <if test="provinceNo != null">
                    PROVINCE_NO = #{provinceNo,jdbcType=VARCHAR},
            </if>
            <if test="cityNo != null">
                    CITY_NO = #{cityNo,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                    CITY_NAME = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="orders != null">
                    ORDERS = #{orders,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="provinceNo != null and provinceNo !=''">
                and PROVINCE_NO = #{provinceNo,jdbcType=VARCHAR}
            </if>
            <if test="cityNo != null and cityNo !=''">
                and CITY_NO = #{cityNo,jdbcType=VARCHAR}
            </if>
            <if test="cityName != null and cityName !=''">
                and CITY_NAME = #{cityName,jdbcType=VARCHAR}
            </if>
            <if test="orders != null">
                and ORDERS = #{orders,jdbcType=SMALLINT}
            </if>
    </sql>

</mapper>
