<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.common.core.dao.mapper.MainstayIndividualProxyMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.individualproxy.MainstayIndividualProxy">
    <!--@mbg.generated-->
    <!--@Table tbl_mainstay_individual_proxy-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="service_fee_ratio" jdbcType="DECIMAL" property="serviceFeeRatio" />
    <result column="single_limit_min" jdbcType="DECIMAL" property="singleLimitMin" />
    <result column="single_limit_max" jdbcType="DECIMAL" property="singleLimitMax" />
    <result column="accumulative_limit_min" jdbcType="DECIMAL" property="accumulativeLimitMin" />
    <result column="accumulative_limit_max" jdbcType="DECIMAL" property="accumulativeLimitMax" />
    <result column="active_limit_type" jdbcType="INTEGER" property="activeLimitType" />
    <result column="age_limit_min" jdbcType="INTEGER" property="ageLimitMin" />
    <result column="age_limit_max" jdbcType="INTEGER" property="ageLimitMax" />
    <result column="acitive_age_limit" jdbcType="BOOLEAN" property="acitiveAgeLimit" />
    <result column="levy_less_than_10w" jdbcType="BOOLEAN" property="levyLessThan10w" />
    <result column="requied_biz_vouchers" jdbcType="VARCHAR" property="requiedBizVouchers" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="area_limit_status" jdbcType="BOOLEAN" property="areaLimitStatus" />
    <result column="area_surport" jdbcType="VARCHAR" property="areaSurport" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="special_remark" jdbcType="LONGVARCHAR" property="specialRemark" />
    <result column="tax_quote" jdbcType="VARCHAR" property="taxQuote" typeHandler="com.zhixianghui.facade.common.handler.IndividualProxyQuoteHandler"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mainstay_no, mainstay_name, version, service_fee_ratio, single_limit_min, single_limit_max,
    accumulative_limit_min, accumulative_limit_max, active_limit_type, age_limit_min,
    age_limit_max, acitive_age_limit, levy_less_than_10w, requied_biz_vouchers, area_limit_status,
    area_surport, special_remark, tax_quote, create_time, create_by, update_time,
    update_by, `status`
  </sql>
</mapper>
