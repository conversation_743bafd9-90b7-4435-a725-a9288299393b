<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.config.BankInfo">
    <sql id="table">tbl_bank_info</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.config.BankInfo">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
        <result column="BANK_CHANNEL_NO" property="bankChannelNo" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="OPENING_BANK_NO" property="openingBankNo" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="CITY_CODE" property="cityCode" jdbcType="SMALLINT"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, BANK_CODE, BANK_CHANNEL_NO, BANK_NAME, OPENING_BANK_NO, PROVINCE, CITY, CITY_CODE
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.config.BankInfo">
        INSERT INTO <include refid="table" /> (
            VERSION,
            BANK_CODE,
            BANK_CHANNEL_NO,
            BANK_NAME,
            OPENING_BANK_NO,
            PROVINCE,
            CITY,
            CITY_CODE
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{bankCode,jdbcType=VARCHAR},
        #{bankChannelNo,jdbcType=VARCHAR},
        #{bankName,jdbcType=VARCHAR},
        #{openingBankNo,jdbcType=VARCHAR},
        #{province,jdbcType=VARCHAR},
        #{city,jdbcType=VARCHAR},
        #{cityCode,jdbcType=SMALLINT}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.config.BankInfo">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=SMALLINT} +1,
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
                BANK_CHANNEL_NO = #{bankChannelNo,jdbcType=VARCHAR},
                BANK_NAME = #{bankName,jdbcType=VARCHAR},
                OPENING_BANK_NO = #{openingBankNo,jdbcType=VARCHAR},
                PROVINCE = #{province,jdbcType=VARCHAR},
                CITY = #{city,jdbcType=VARCHAR},
                CITY_CODE = #{cityCode,jdbcType=SMALLINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.config.BankInfo">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                    VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="bankCode != null">
                    BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankChannelNo != null">
                    BANK_CHANNEL_NO = #{bankChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                    BANK_NAME = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="openingBankNo != null">
                    OPENING_BANK_NO = #{openingBankNo,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                    PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                    CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="cityCode != null">
                    CITY_CODE = #{cityCode,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=SMALLINT}
            </if>
            <if test="bankCode != null and bankCode !=''">
                and BANK_CODE = #{bankCode,jdbcType=VARCHAR}
            </if>
            <if test="bankChannelNo != null and bankChannelNo !=''">
                and BANK_CHANNEL_NO = #{bankChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="bankName != null and bankName !=''">
                and BANK_NAME = #{bankName,jdbcType=VARCHAR}
            </if>
            <if test="openingBankNo != null and openingBankNo !=''">
                and OPENING_BANK_NO = #{openingBankNo,jdbcType=VARCHAR}
            </if>
            <if test="province != null and province !=''">
                and PROVINCE = #{province,jdbcType=VARCHAR}
            </if>
            <if test="city != null and city !=''">
                and CITY = #{city,jdbcType=VARCHAR}
            </if>
            <if test="cityCode != null">
                and CITY_CODE = #{cityCode,jdbcType=SMALLINT}
            </if>
            <if test="bankNameLike != null and bankNameLike !='' ">
                AND BANK_NAME LIKE CONCAT('%', CONCAT(#{bankNameLike}, '%'))
            </if>
            <if test="cityLike != null and cityLike !='' ">
                AND CITY LIKE CONCAT('%', CONCAT(#{cityLike}, '%'))
            </if>
    </sql>

</mapper>
