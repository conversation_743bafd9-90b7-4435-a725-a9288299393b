<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.report.ReportChannelRecord">
    <sql id="table">tbl_report_channel_record</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.report.ReportChannelRecord">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="MERCHANT_TYPE" property="merchantType" jdbcType="SMALLINT"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_NAME" property="payChannelName" jdbcType="VARCHAR"/>
        <result column="REPORTER" property="reporter" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="RESP_DATA" property="respData" jdbcType="OTHER"/>
        <result column="ERR_MSG" property="errMsg" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, SERIAL_NO, CREATE_TIME, UPDATE_TIME, MERCHANT_TYPE,MAINSTAY_NO, MAINSTAY_NAME, EMPLOYER_NO, EMPLOYER_NAME, CHANNEL_TYPE, PAY_CHANNEL_NO, PAY_CHANNEL_NAME, REPORTER, STATUS, RESP_DATA, ERR_MSG
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.report.ReportChannelRecord">
        INSERT INTO <include refid="table" /> (
            VERSION,
            SERIAL_NO,
            CREATE_TIME,
            UPDATE_TIME,
            MERCHANT_TYPE,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            CHANNEL_TYPE,
            PAY_CHANNEL_NO,
            PAY_CHANNEL_NAME,
            REPORTER,
            STATUS,
            RESP_DATA,
            ERR_MSG
        ) VALUES (
        #{version,jdbcType=INTEGER},
        #{serialNo,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{merchantType,jdbcType=SMALLINT},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{channelType,jdbcType=SMALLINT},
        #{payChannelNo,jdbcType=VARCHAR},
        #{payChannelName,jdbcType=VARCHAR},
        #{reporter,jdbcType=VARCHAR},
        #{status,jdbcType=SMALLINT},
        #{respData,jdbcType=OTHER},
        #{errMsg,jdbcType=VARCHAR}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.report.ReportChannelRecord">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=INTEGER} +1,
                SERIAL_NO = #{serialNo,jdbcType=VARCHAR},
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                MERCHANT_TYPE = #{merchantType,jdbcType=SMALLINT},
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
                CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
                PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},
                REPORTER = #{reporter,jdbcType=VARCHAR},
                STATUS = #{status,jdbcType=SMALLINT},
                RESP_DATA = #{respData,jdbcType=OTHER},
                ERR_MSG = #{errMsg,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.report.ReportChannelRecord">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                    VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="serialNo != null">
                    SERIAL_NO = #{serialNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="merchantType != null">
                    MERCHANT_TYPE = #{merchantType,jdbcType=SMALLINT},
            </if>
            <if test="mainstayNo != null">
                    MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                    MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="employerNo != null">
                    EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="employerName != null">
                    EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null">
                    CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
            </if>
            <if test="payChannelNo != null">
                    PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                    PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="reporter != null">
                    REPORTER = #{reporter,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                    STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="respData != null">
                    RESP_DATA = #{respData,jdbcType=OTHER},
            </if>
            <if test="errMsg != null">
                    ERR_MSG = #{errMsg,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteBySerialNo">
        delete from <include refid="table"/> where serial_no = #{serialNo,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByEmployerNoAndMainstayNoAndPayChannelNoAndType">
        DELETE FROM <include refid="table"/> WHERE
            MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            and MERCHANT_TYPE = #{merchantType,jdbcType=SMALLINT}
        <if test="employerNo != null and employerNo != ''">
            and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="channelType != null">
            and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
        </if>
    </delete>

    <delete id="deleteByEmployerNoAndMainstayNoAndPayChannelNo">
        DELETE FROM <include refid="table"/> WHERE EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR} and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR} and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=INTEGER}
            </if>
            <if test="serialNo != null and serialNo !=''">
                and SERIAL_NO = #{serialNo,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="merchantType != null">
                and MERCHANT_TYPE  = #{merchantType,jdbcType=SMALLINT}
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNameLike != null and mainstayNameLike !=''">
                and MAINSTAY_NAME LIKE CONCAT('%',#{mainstayNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="employerNo != null and employerNo !=''">
                and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="employerName != null and employerName !=''">
                and EMPLOYER_NAME LIKE CONCAT('%',#{employerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="employerNameLike != null and employerNameLike !=''">
                and EMPLOYER_NAME LIKE CONCAT('%',#{employerNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="channelType != null">
                and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
            </if>
            <if test="payChannelNo != null and payChannelNo !=''">
                and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="payChannelName != null and payChannelName !=''">
                and PAY_CHANNEL_NAME LIKE CONCAT('%',#{payChannelName,jdbcType=VARCHAR},'%')
            </if>
            <if test="reporter != null and reporter !=''">
                and REPORTER = #{reporter,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and STATUS = #{status,jdbcType=SMALLINT}
            </if>
            <if test="respData != null and respData !=''">
                and RESP_DATA = #{respData,jdbcType=OTHER}
            </if>
            <if test="errMsg != null and errMsg !=''">
                and ERR_MSG = #{errMsg,jdbcType=VARCHAR}
            </if>
            <!-- 以下自定义 -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_TIME between #{beginDate} and #{endDate}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and STATUS in
                <foreach collection="statusList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=SMALLINT}
                </foreach>
            </if>
    </sql>

</mapper>
