<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.common.entity.report.MainstayChannelRelation">
    <sql id="table">tbl_mainstay_channel_relation</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.common.entity.report.MainstayChannelRelation">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_MCH_NO" property="channelMchNo" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_OPERATOR" property="updateOperator" jdbcType="VARCHAR"/>
        <result column="CREATE_OPERATOR" property="createOperator" jdbcType="VARCHAR"/>
        <result column="AGREEMENT_NO" property="agreementNo" jdbcType="VARCHAR"/>
        <result column="ALIPAY_USER_ID" property="alipayUserId" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="SUB_BANK_NAME" property="subBankName" jdbcType="VARCHAR"/>
        <result column="JOIN_BANK_NO" property="joinBankNo" jdbcType="VARCHAR"/>
        <result column="BANK_ADDRESS" property="bankAddress" jdbcType="VARCHAR"/>
        <result column="ALIPAY_CARD_NO" property="alipayCardNo" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="billResultMap" type="java.util.Map">
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="AGREEMENT_NO" property="agreementNo" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="cmbBillResultMap" type="java.util.Map">
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_MCH_NO" property="channelMchNo" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, MAINSTAY_NAME, MAINSTAY_NO, PAY_CHANNEL_NO, CHANNEL_MCH_NO, STATUS, CREATE_TIME, UPDATE_TIME, UPDATE_OPERATOR, CREATE_OPERATOR,AGREEMENT_NO,ALIPAY_USER_ID,ACCOUNT_NAME,ACCOUNT_NO,BANK_NAME,SUB_BANK_NAME,JOIN_BANK_NO,BANK_ADDRESS,ALIPAY_CARD_NO
    </sql>

    <resultMap id="PageResultMap" type="com.zhixianghui.facade.common.dto.MainstayChannelRelationDto">
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <collection property="mainstayChannels" ofType="com.zhixianghui.facade.common.dto.MainstayChannel">
            <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
            <result column="CHANNEL_MCH_NO" property="channelMchNo" jdbcType="VARCHAR"/>
            <result column="STATUS" property="status" jdbcType="SMALLINT"/>
            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
            <result column="UPDATE_OPERATOR" property="updateOperator" jdbcType="VARCHAR"/>
            <result column="CREATE_OPERATOR" property="createOperator" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.common.entity.report.MainstayChannelRelation">
        INSERT INTO <include refid="table" /> (
            VERSION,
            MAINSTAY_NAME,
            MAINSTAY_NO,
            PAY_CHANNEL_NO,
            CHANNEL_MCH_NO,
            STATUS,
            CREATE_TIME,
            UPDATE_TIME,
            UPDATE_OPERATOR,
            CREATE_OPERATOR,
            AGREEMENT_NO,
            ALIPAY_USER_ID,
            ACCOUNT_NAME,
            ACCOUNT_NO,
            BANK_NAME,
            SUB_BANK_NAME,
            JOIN_BANK_NO,
            BANK_ADDRESS,
            ALIPAY_CARD_NO
        ) VALUES (
        #{version,jdbcType=INTEGER},
        #{mainstayName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{payChannelNo,jdbcType=VARCHAR},
        #{channelMchNo,jdbcType=VARCHAR},
        #{status,jdbcType=SMALLINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{updateOperator,jdbcType=VARCHAR},
        #{createOperator,jdbcType=VARCHAR},
        #{agreementNo,jdbcType=VARCHAR},
        #{alipayUserId,jdbcType=VARCHAR}，
        #{accountName,jdbcType=VARCHAR},
        #{accountNo,jdbcType=VARCHAR},
        #{bankName,jdbcType=VARCHAR},
        #{subBankName,jdbcType=VARCHAR},
        #{joinBankNo,jdbcType=VARCHAR},
        #{bankAddress,jdbcType=VARCHAR},
        #{alipayCardNo,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO <include refid="table" /> (
        VERSION,
        MAINSTAY_NAME,
        MAINSTAY_NO,
        PAY_CHANNEL_NO,
        CHANNEL_MCH_NO,
        STATUS,
        CREATE_TIME,
        UPDATE_TIME,
        UPDATE_OPERATOR,
        CREATE_OPERATOR,
        AGREEMENT_NO,
        ALIPAY_USER_ID,
        ACCOUNT_NAME,
        ACCOUNT_NO,
        BANK_NAME,
        SUB_BANK_NAME,
        JOIN_BANK_NO,
        BANK_ADDRESS,
        ALIPAY_CARD_NO
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.payChannelNo,jdbcType=VARCHAR},
            #{item.channelMchNo,jdbcType=VARCHAR},
            #{item.status,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateOperator,jdbcType=VARCHAR},
            #{item.createOperator,jdbcType=VARCHAR},
            #{item.agreementNo,jdbcType=VARCHAR},
            #{item.alipayUserId,jdbcType=VARCHAR},
            #{item.accountName,jdbcType=VARCHAR},
            #{item.accountNo,jdbcType=VARCHAR},
            #{item.bankName,jdbcType=VARCHAR},
            #{item.subBankName,jdbcType=VARCHAR},
            #{item.joinBankNo,jdbcType=VARCHAR},
            #{item.bankAddress,jdbcType=VARCHAR},
            #{item.alipayCardNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.common.entity.report.MainstayChannelRelation">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=INTEGER} +1,
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
                CHANNEL_MCH_NO = #{channelMchNo,jdbcType=VARCHAR},
                STATUS = #{status,jdbcType=SMALLINT},
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
                CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
                AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR},
                ALIPAY_USER_ID = #{alipayUserId,jdbcType=VARCHAR},
                ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
                ACCOUNT_NO =#{accountNo,jdbcType=VARCHAR},
                BANK_NAME = #{bankName,jdbcType=VARCHAR},
                SUB_BANK_NAME = #{subBankName,jdbcType=VARCHAR},
                JOIN_BANK_NO = #{joinBankNo,jdbcType=VARCHAR},
                BANK_ADDRESS = #{bankAddress,jdbcType=VARCHAR},
                ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.common.entity.report.MainstayChannelRelation">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                    VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="mainstayName != null">
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                    MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelNo != null">
                    PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="channelMchNo != null">
                    CHANNEL_MCH_NO = #{channelMchNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                    UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOperator != null">
                    UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR},
            </if>
            <if test="createOperator != null">
                    CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR},
            </if>
            <if test="agreementNo != null">
                    AGREEMENT_NO = #{agreementNo,jdbcType=VARCHAR},
            </if>
            <if test="alipayUserId != null">
                    ALIPAY_USER_ID = #{alipayUserId,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null and accountName != ''">
                    ACCOUNT_NAME = #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null and accountNo != ''">
                    ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != ''">
                    BANK_NAME = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="subBankName != null and subBankName != ''">
                SUB_BANK_NAME = #{subBankName,jdbcType=VARCHAR},
            </if>
            <if test="joinBankNo != null and joinBankNo != ''">
                JOIN_BANK_NO = #{joinBankNo,jdbcType=VARCHAR},
            </if>
            <if test="bankAddress != null and bankAddress != ''">
                BANK_ADDRESS = #{bankAddress,jdbcType=VARCHAR},
            </if>
            <if test="alipayCardNo != null and alipayCardNo != ''">
                ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <resultMap type="java.lang.Integer" id="dataMap">
        <result column="data"/>
    </resultMap>

    <resultMap type="java.lang.Integer" id="count">
        <result column="total"/>
    </resultMap>

    <!-- 自定义分页查询 -->
    <select id="listCustomPage" parameterType="java.util.Map" resultMap="PageResultMap">
        SELECT
        <choose>
            <when test="payChannelNoList != null and payChannelNoList.size()>0">
                ID, VERSION, MAINSTAY_NAME, a.MAINSTAY_NO, PAY_CHANNEL_NO, CHANNEL_MCH_NO, STATUS, CREATE_TIME, UPDATE_TIME, UPDATE_OPERATOR, CREATE_OPERATOR,ALIPAY_CARD_NO
            </when>
            <otherwise>
                ID, VERSION, MAINSTAY_NAME, MAINSTAY_NO, PAY_CHANNEL_NO, CHANNEL_MCH_NO, STATUS, CREATE_TIME, UPDATE_TIME, UPDATE_OPERATOR, CREATE_OPERATOR,ALIPAY_CARD_NO
            </otherwise>
        </choose>
        FROM
        <include refid="table" /> b
        <if test="payChannelNoList != null and payChannelNoList.size()>0">
            JOIN (
            SELECT MAINSTAY_NO FROM <include refid="table" />
            WHERE PAY_CHANNEL_NO IN
            <foreach item="item" index="index" collection="payChannelNoList" open="(" separator="," close=")">"${item}"</foreach> AND `STATUS` = #{openStatus,jdbcType=SMALLINT}
            GROUP BY MAINSTAY_NO
            HAVING COUNT(MAINSTAY_NO) = ${payChannelNoList.size}) a
            on b.MAINSTAY_NO = a.MAINSTAY_NO
        </if>
        <where>
            <include refid="join_condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY b.${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY b.ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 自定义分页查询时计算总记录数 -->
    <select id="customCountBy" parameterType="java.util.Map" resultMap="dataMap">
        SELECT COUNT(1) as data FROM
        <include refid="table" /> b
        <if test="payChannelNoList != null and payChannelNoList.size()>0">
            JOIN (
            SELECT MAINSTAY_NO FROM <include refid="table" />
            WHERE PAY_CHANNEL_NO IN
            <foreach item="item" index="index" collection="payChannelNoList" open="(" separator="," close=")">"${item}"</foreach> AND `STATUS` = #{openStatus,jdbcType=SMALLINT}
            GROUP BY MAINSTAY_NO
            HAVING COUNT(MAINSTAY_NO) = ${payChannelNoList.size})a
            on b.MAINSTAY_NO = a.MAINSTAY_NO
        </if>
        <where>
            <include refid="join_condition_sql" />
        </where>
        <choose>
            <when test="payChannelNoList != null and payChannelNoList.size()>0">
                GROUP BY a.MAINSTAY_NO;
            </when>
            <otherwise>
                GROUP BY b.MAINSTAY_NO;
            </otherwise>
        </choose>
--         SELECT FOUND_ROWS() AS total;
    </select>


    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 按查询条件删除 -->
    <delete id="deleteBy">
        DELETE FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=INTEGER}
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNameLike != null and mainstayNameLike !=''">
                and MAINSTAY_NAME like concat('%', #{mainstayNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="payChannelNo != null and payChannelNo !=''">
                and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="channelMchNo != null and channelMchNo !=''">
                and CHANNEL_MCH_NO = #{channelMchNo,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and STATUS = #{status,jdbcType=SMALLINT}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateOperator != null and updateOperator !=''">
                and UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
            </if>
            <if test="createOperator != null and createOperator !=''">
                and CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
            </if>
            <if test="alipayCardNo != null and alipayCardNo != ''">
                and ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR}
            </if>
    </sql>

    <sql id="join_condition_sql">
        <if test="id != null">
            and b.ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and b.VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and b.MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNameLike != null and mainstayNameLike !=''">
            and b.MAINSTAY_NAME like concat('%', #{mainstayNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and b.MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="payChannelNo != null and payChannelNo !=''">
            and b.PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
        </if>
        <if test="channelMchNo != null and channelMchNo !=''">
            and b.CHANNEL_MCH_NO = #{channelMchNo,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and b.STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and b.CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and b.UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateOperator != null and updateOperator !=''">
            and b.UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        </if>
        <if test="createOperator != null and createOperator !=''">
            and b.CREATE_OPERATOR = #{createOperator,jdbcType=VARCHAR}
        </if>
        <if test="alipayCardNo != null and alipayCardNo != ''">
            and b.ALIPAY_CARD_NO = #{alipayCardNo,jdbcType=VARCHAR}
        </if>
    </sql>

    <update id="updateStatusByPayChannelNo" parameterType="java.util.Map" >
        UPDATE <include refid="table" />
        SET STATUS = #{status,jdbcType=SMALLINT},
        UPDATE_OPERATOR = #{updateOperator,jdbcType=VARCHAR}
        WHERE PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
    </update>

    <select id="listAllMainstay" resultMap="BaseResultMap">
        SELECT DISTINCT(MAINSTAY_NO),MAINSTAY_NAME
        FROM <include refid="table" />
    </select>

    <select id="getAlipayMainstays" parameterType="java.util.Map" resultMap="billResultMap">
        select t.MAINSTAY_NO,t.AGREEMENT_NO from tbl_mainstay_channel_relation t where t.PAY_CHANNEL_NO='ALIPAY' and t.STATUS =100 and t.AGREEMENT_NO is not null GROUP BY t.MAINSTAY_NO,t.AGREEMENT_NO
    </select>

    <select id="getCmbainstays" parameterType="java.util.Map" resultMap="cmbBillResultMap">
        select t.MAINSTAY_NO,t.CHANNEL_MCH_NO from tbl_mainstay_channel_relation t where t.PAY_CHANNEL_NO='CMB' and t.STATUS =100 GROUP BY t.MAINSTAY_NO,t.CHANNEL_MCH_NO
    </select>
</mapper>
