package com.zhixianghui.service.common.service;

import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.exception.SequenceExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.middleware.leaf.common.Result;
import com.zhixianghui.middleware.leaf.common.Status;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service("SequenceService")
public class SequenceService {
    public final static String SNOW_BIZ_KEY = "key";
    @Autowired
    private SnowflakeService snowflakeService;
    @Autowired
    private SegmentService segmentService;
    @Autowired
    private RedisService redisService;

    public Long nextSnowId() {
        return get(SNOW_BIZ_KEY, snowflakeService.getId());
    }

    public List<Long> nextSnowId(int count) {
        List<Long> idList = new ArrayList<>(count);
        while (count-- > 0) {
            idList.add(nextSnowId());
        }
        return idList;
    }

    public String nextSnowId(String prefix, boolean isWithDate) {
        Long id = nextSnowId();
        if (isWithDate) {
            return prefix + DateUtil.formatCompactDate(new Date()) + id;
        } else {
            return prefix + id;
        }
    }

    public List<String> nextSnowId(int count, String prefix, boolean isWithDate) {
        List<String> idStrList = new ArrayList<>(count);
        while (count-- > 0) {
            idStrList.add(nextSnowId(prefix, isWithDate));
        }
        return idStrList;
    }

    public Long nextSegmentId(String bizKey) {
        return get(bizKey, segmentService.getId(bizKey));
    }

    /**
     * 使用数据库批量生成Id序列号
     *
     * @param bizKey
     * @param count
     * @return
     * @see #nextSegmentId(String)
     */
    public List<Long> nextSegmentId(String bizKey, int count) {
        List<Long> idList = new ArrayList<>(count);
        while (count-- > 0) {
            idList.add(nextSegmentId(bizKey));
        }
        return idList;
    }

    /**
     * 使用数据库生成指定长度的ID序列号，长度不足补0，并添加前缀
     * @param prefix    前缀
     * @param bizTag    业务标识(不要使用中文)
     * @param width     序列号长度
     */
    public String nextSegmentId(String prefix, String bizTag, int width) {
        Long id = nextSegmentId(bizTag);
        return prefix + StringUtil.longToString(id, width);
    }

    /**
     * 使用数据库批量生成指定长度的ID序列号，长度不足补0，并添加前缀
     * @param prefix    前缀
     * @param bizTag    业务标识(不要使用中文)
     * @param width     序列号长度
     * @param count     生成序列号个数
     */
    public List<String> nextSegmentId(String prefix, String bizTag, int width, int count) {
        List<String> idList = new ArrayList<>(count);
        while (count-- > 0) {
            idList.add(nextSegmentId(prefix, bizTag, width));
        }
        return idList;
    }

    /**
     * 使用 Redis 生成 ID 序列号
     * 特点：
     *      1、使用 Redis 的 INCR 命令和 Lua 脚本来保证原子性，并发度取决于 Redis Server 的性能
     *      2、保证绝对单调递增，不会出现数值空洞，最大值是long类型的最大长度
     *      3、Redis 如果是主从模式，主从切换后可能会产生重复 ID
     * @param bizTag    业务标识（不要使用中文）
     */
    public Long nextRedisId(String bizTag) {
        return get(bizTag, redisService.getId(bizTag));
    }

    /**
     * 使用 Redis 生成 ID 序列号，超过给定长度则重置为 1
     * @see #nextRedisId(String)
     * @param bizTag    业务标识（不要使用中文）
     * @param width     序列号长度，取值范围 [1, 10]
     */
    public Long nextRedisId(String bizTag, int width) {
        return get(bizTag, redisService.getId(bizTag, width));
    }

    public Long nextRedisId(String bizTag, int width,int count) {
        return get(bizTag, redisService.getId(bizTag, width,count));
    }

    /**
     * 使用 Redis 生成 ID 序列号，超过给定长度则重置为 1，并转化为字符串，长度不足补0，并添加前缀
     * @param prefix    前缀
     * @param bizTag    业务标识(不要使用中文)
     * @param width     序列号长度，取值范围 [1, 10]
     */
    public String nextRedisId(String prefix, String bizTag, int width) {
        Long id = nextRedisId(bizTag, width);
        return prefix + StringUtil.longToString(id, width);
    }

    /**
     * 使用 Redis 生成 ID 序列号，超过给定长度则重置为 1，并转化为字符串，长度不足补0，并添加前缀和日期
     * @param prefix    前缀
     * @param bizTag    业务标识(不要使用中文)
     * @param width     序列号长度，取值范围 [1, 10]
     */
    public String nextRedisIdWithDate(String prefix, String bizTag, int width) {
        Long id = nextRedisId(bizTag, width);
        return prefix + DateUtil.formatShortDate(new Date()) + StringUtil.longToString(id, width);
    }

    private long get(String key, Result id) {
        Result result;
        if (key == null || key.isEmpty()) {
            throw SequenceExceptions.SEQUENCE_COMMON_EXCEPTION.newWithErrMsg("noKey");
        }

        result = id;
        if (result.getStatus().equals(Status.EXCEPTION)) {
            throw SequenceExceptions.SEQUENCE_COMMON_EXCEPTION.newWithErrMsg(result.toString());
        }
        return result.getId();
    }

    public List<String> nextRedisIdList(String bizTag, int width, int count) {
        Long id = nextRedisId(bizTag, width,count);
        List<String> idList = Lists.newLinkedList();
        for(int i = 0; i < count; i++){
            long curId = id - i;
            String nextId = StringUtil.longToString(curId, width);
            //小的放前，且LinkedList
            idList.add(0,nextId);
        }
        return idList;
    }
}
