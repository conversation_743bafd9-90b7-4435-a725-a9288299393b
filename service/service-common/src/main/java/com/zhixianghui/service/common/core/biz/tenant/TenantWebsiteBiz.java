package com.zhixianghui.service.common.core.biz.tenant;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.common.entity.tenant.TenantWebsite;
import com.zhixianghui.facade.common.vo.TenantLinkVo;
import com.zhixianghui.service.common.core.dao.mapper.TenantWebsiteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-03-28
*/
@Service
public class TenantWebsiteBiz extends ServiceImpl<TenantWebsiteMapper, TenantWebsite> {

    @Autowired
    private TenantWebsiteMapper tenantWebsiteMapper;

    public List<TenantLinkVo> getListByTenantId(Long id) {
        return tenantWebsiteMapper.getVoListByTenantId(id);
    }
}