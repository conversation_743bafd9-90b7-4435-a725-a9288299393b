package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.common.service.ReportFacade;
import com.zhixianghui.service.common.core.biz.report.ReportChannelRecordBiz;
import com.zhixianghui.service.common.core.biz.report.external.AliPayReportBiz;
import com.zhixianghui.service.common.core.biz.report.external.ReportBiz;
import com.zhixianghui.service.common.core.biz.report.external.ReportNotifyBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReportFacadeImpl implements ReportFacade {

    private final ReportBiz reportBiz;
    private final ReportChannelRecordBiz reportChannelRecordBiz;
    private final ReportNotifyBiz reportNotifyBiz;

//    @Override
//    public ReportChannelRecord getRecordByEmployerNoAndMainstayNoAndChannelNo(String employerNo, String mainstayNo, String payChannelNo) {
//        return reportChannelRecordBiz.getRecordByEmployerNoAndMainstayNoAndChannelNo(employerNo,mainstayNo,payChannelNo);
//    }

    @Override
    public void unsign(ReportEntity reportEntity) {
        reportBiz.unsign(reportEntity);
    }

    @Override
    public void
    report(ReportEntity reportEntity){
        reportBiz.report(reportEntity);
    }

    @Autowired
    private AliPayReportBiz aliPayReportBiz;

    @Override
    public String reportMerchantDiy(ReportEntity reportEntity) {
        return aliPayReportBiz.reportMerchantDiy(reportEntity);
    }

    @Override
    public void modify(ReportEntity reportEntity) {
        reportBiz.modify(reportEntity);
    }

    @Override
    public void uploadPic(ReportEntity reportEntity) {
        reportNotifyBiz.uploadPic(reportEntity.getEmployerNo(),reportEntity.getMainstayNo());
    }

    @Override
    public void uploadPic(String employerNo, String mainstayNo) {
        reportNotifyBiz.uploadPic(employerNo,mainstayNo);
    }
}
