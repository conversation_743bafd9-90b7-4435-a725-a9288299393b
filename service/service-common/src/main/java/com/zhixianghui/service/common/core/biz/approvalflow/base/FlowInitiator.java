package com.zhixianghui.service.common.core.biz.approvalflow.base;


import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowHandleType;
import com.zhixianghui.facade.common.enums.HandleStatus;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.CommonBiz;
import com.zhixianghui.service.common.core.biz.approvalflow.responsibilitychain.Approver;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDetailDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;


/**
 * <AUTHOR>
 * @description 创建处理流程
 * @date 2020-08-12 09:57
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FlowInitiator extends Approver {

    private final CommonBiz commonBiz;
    private final ApprovalFlowDetailDao approvalFlowDetailDao;

    @Override
    public ApprovalInfoVo handleFlow(ApprovalFlow approvalFlow) {
        int step = 0;
        //初次提交初始化的情况
        if(approvalFlow.getStepNum().equals(step) && approvalFlow.getId() == null){
            log.info("---审批流程初始化---");
            approvalFlow.setStepNum(step + 1);
            approvalFlow = commonBiz.createApprovalFlow(approvalFlow);
            return this.nextHandle(approvalFlow);
        }else if(approvalFlow.getStepNum().equals(step) && approvalFlow.getId() != null) {
            //驳回到初始化的情况
            if(ObjectUtils.isEmpty(commonBiz.listByFlowIdAndStepNumAndIsHistory(approvalFlow.getId(), step,false))){
                ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
                approvalFlowDetail.setCreateTime(new Date());
                approvalFlowDetail.setUpdateTime(new Date());
                approvalFlowDetail.setApprovalFlowId(approvalFlow.getId());
                approvalFlowDetail.setHandlerId(approvalFlow.getInitiatorId());
                approvalFlowDetail.setHandlerName(approvalFlow.getInitiatorName());
                approvalFlowDetail.setHandlerType(FlowHandleType.SUBMIT.getValue());
                approvalFlowDetail.setStepNum(0);
                approvalFlowDetail.setStatus(HandleStatus.PENDING.getValue());
                approvalFlowDetail.setOperatorName(approvalFlow.getInitiatorName());
                approvalFlowDetail.setPlatform(approvalFlow.getPlatform());
                approvalFlowDetail.setIsHistory(false);
                approvalFlowDetailDao.insert(approvalFlowDetail);
            }
            return ApprovalInfoVo.builder().id(approvalFlow.getId()).isLast(false).build();

        }else{
            return this.nextHandle(approvalFlow);
        }
    }

}
