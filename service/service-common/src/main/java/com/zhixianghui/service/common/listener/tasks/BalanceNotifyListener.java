package com.zhixianghui.service.common.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.service.common.core.biz.report.EmployerAccountNotifyConfigBiz;
import com.zhixianghui.service.common.listener.TaskRocketMQListener;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 余额提醒定时任务
 */
@Component
@RocketMQMessageListener(topic = "topic-balance-notify-task",consumeThreadMax = 1,consumerGroup = "BalanceNotifyGroup")
@Slf4j
public class BalanceNotifyListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private EmployerAccountNotifyConfigBiz notifyConfigBiz;

    @Override
    public void runTask(JSONObject jsonParam) {

        try {
            log.info("定时余额提醒任务[开始]");
            notifyConfigBiz.doBalanceNotify();
            log.info("定时余额提醒任务[结束]");
        } catch (Exception e) {
            log.info("余额提醒定时任务执行异常", e);
            throw e;
        }

    }
}