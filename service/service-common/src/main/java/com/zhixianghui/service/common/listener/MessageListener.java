package com.zhixianghui.service.common.listener;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.report.AlipayReportResVo;
import com.zhixianghui.facade.banklink.vo.report.ReportReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.constants.ReportConstants;

import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.merchant.vo.EmployerFullInfoUpdateVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerInsertVo;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.facade.trade.service.RechargeQueryFacade;
import com.zhixianghui.service.common.core.biz.approvalflow.ApprovalFlowDetailBiz;
import com.zhixianghui.service.common.core.biz.report.EmployerAccountInfoBiz;
import com.zhixianghui.service.common.core.biz.report.EmployerMainstayRelationBiz;
import com.zhixianghui.service.common.core.biz.report.external.ReportBiz;
import com.zhixianghui.service.common.core.biz.report.external.ReportNotifyBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.zhixianghui.facade.common.entity.report.ReportEntity;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MessageListener {

    @Autowired
    private EmployerAccountInfoBiz employerAccountInfoBiz;

    @Autowired
    private ReportNotifyBiz reportNotifyBiz;
    @Autowired
    private ApprovalFlowDetailBiz approvalFlowDetailBiz;
    @Reference
    private RechargeQueryFacade rechargeQueryFacade;
    @Autowired
    private EmployerMainstayRelationBiz employerMainstayRelationBiz;
    @Autowired
    private ReportBiz reportBiz;
    @Reference
    private RobotFacade robotFacade;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_SUBCRIBE,selectorExpression = MessageMsgDest.TAG_ALIPAY_SUBCRIBE,consumeThreadMax = 5,consumerGroup = "subcribeConsumer")
    public class SubcribeListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            Map<String,Object> infoMap = JsonUtil.toBean(jsonParam, new TypeReference<Map<String,Object>>(){});
            try {
                reportNotifyBiz.subcribe(infoMap);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_NOTICE_TIMER,consumeThreadMax = 1,consumerGroup = "commonNoticeTimer")
    public class NoticeTimerListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MarkDownMsg markDownMsg = new MarkDownMsg();
            markDownMsg.setUnikey(UUID.randomUUID().toString());
            markDownMsg.setRobotType(RobotTypeEnum.NOTICE_ROBOT.getType());
            Date date = new Date();
            int month = DateUtil.getMonth(date);
            Date endDate = DateUtil.addMonth(date,-3);
            int endMonth = DateUtil.getMonth(endDate);
            int day = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
            StringBuffer sb = new StringBuffer("#### 【费用报销提醒】 \\n");
            sb.append(String.format("各位，今天是<font color=\\\"warning\\\">%s月%s日</font>，需要报销的发票日期不能在<font color=\\\"warning\\\">%s月%s日</font>之前。如有在<font color=\\\"warning\\\">%s月%s日</font>之前的发票不给予通过，请合理安排费用报销时间。\\n",month,day,endMonth,day,endMonth,day))
                    .append("（灵活用工报销规定：费用报销请尽量在当月提交，最迟不能超过<font color=\\\"warning\\\">90天</font>）");
            markDownMsg.setContent(sb.toString());
            robotFacade.pushMarkDownAsync(markDownMsg);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_REPORT_REPEAT,selectorExpression = MessageMsgDest.TAG_ALIPAU_REPROT_REPEAT,consumeThreadMax = 2,consumerGroup = "alipayReportRepeat")
    public class AliPayReportRepeat extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("AlipayReportResVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            AlipayReportResVo alipayReportResVo = JsonUtil.toBean(msg, AlipayReportResVo.class);
            reportNotifyBiz.doRepeatReport(alipayReportResVo);
        }
    }

    /**
     * 延时查询支付宝报备情况
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_REPORT_DELAY,selectorExpression = MessageMsgDest.TAG_ALIPAY_REPORT_DELAY,consumeThreadMax = 2,consumerGroup = "alipayDelaySearch")
    public class AliPayDelaySearchListen extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String msg) {
            if (msg == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("reportEntity不能为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            ReportEntity reportEntity = JsonUtil.toBean(msg, ReportEntity.class);
            reportNotifyBiz.getReportData(reportEntity);
        }
    }

    /**
     * 支付宝报备回调监听
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_CALLBACK,selectorExpression = MessageMsgDest.TAG_ALIPAY_CALLBACK_USERSIGN,consumeThreadMax = 8,consumerGroup = "alipayConsumerGroup")
    public class AliPayMessageListen extends BaseRocketMQListener<String>{
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("AlipayReportResVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            AlipayReportResVo alipayReportResVo = JsonUtil.toBean(msg, AlipayReportResVo.class);
            reportNotifyBiz.notifySuccess(alipayReportResVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MAINSTAY_EDIT,selectorExpression = MessageMsgDest.TAG_MAINSTAY_EDIT,consumeThreadMax = 1,consumerGroup = "commonBaseMainstayConsumer")
    public class MainstayUpdateMessageListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {
            if (StringUtil.isEmpty(jsonParam)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("EmployerFullInfoUpdateVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantUpdateVo merchantUpdateVo = JsonUtil.toBean(jsonParam, MerchantUpdateVo.class);
            employerAccountInfoBiz.merchantMessageChange(merchantUpdateVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MERCHANT_BASE_EDIT,selectorExpression = MessageMsgDest.TAG_MERCHANT_BASE_EDIT,consumeThreadMax = 1,consumerGroup = "commonMerchantBaseConsumer")
    public class MerchantFullEditMessageListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {
            if (StringUtil.isEmpty(jsonParam)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("EmployerFullInfoUpdateVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantUpdateVo merchantUpdateVo = JsonUtil.toBean(jsonParam, MerchantUpdateVo.class);
            employerAccountInfoBiz.merchantMessageChange(merchantUpdateVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_REPORT_ASYNC,selectorExpression = MessageMsgDest.TAG_REPORT_MODIFY_NOTIFY,consumeThreadMax = 3,consumerGroup = "reportModifyConsumer")
    public class ReportModifyNotifyListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            ReportReceiveRespVo receiveRespVo = JsonUtil.toBean(jsonParam, ReportReceiveRespVo.class);
            reportNotifyBiz.motifySuccess(receiveRespVo);
        }
    }

    /**
     * 报备回调监听
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_REPORT_ASYNC, selectorExpression = MessageMsgDest.TAG_REPORT_NOTIFY_ASYNC ,consumeThreadMax = 3, consumerGroup = "reportNotifyConsumer")
    public class ReportNotifyMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("ReportReceiveRespVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            ReportReceiveRespVo receiveRespVo = JsonUtil.toBean(msg, ReportReceiveRespVo.class);
            reportNotifyBiz.notifySuccess(receiveRespVo);
        }
    }


    /**
     * 审批流异步 自动同意流程
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC, selectorExpression = MessageMsgDest.TAG_APPROVAL_AUTO_AGREE_ASYNC ,consumeThreadMax = 3, consumerGroup = "approvalConsumer")
    public class ApprovalAutoAgreeMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("审批流异步:自动同意流程 msg不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            Map<String,Object> infoMap = JsonUtil.toBean(msg, new TypeReference<Map<String,Object>>(){});
            Long approvalDetailId =  Long.valueOf(String.valueOf(infoMap.get("approvalDetailId")));
            Long handlerId = Long.valueOf(String.valueOf(infoMap.get("handlerId")));
            Integer platformSource =  Integer.valueOf(String.valueOf(infoMap.get("platformSource")));
            String flowName = (String) infoMap.get("flowName");
            String approvalOpinion = (String) infoMap.get("approvalOpinion");
            approvalFlowDetailBiz.agreeApprovalDetail(approvalDetailId,handlerId,platformSource,flowName,approvalOpinion,false);
        }
    }

    /**
     * 产品报价单通过后续操作
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_ADD_MERCHANT_QUOTE,consumeThreadMax = 3,consumerGroup = "commonAfterCreateRelation")
    public class CommonAfterCreateFlowListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {
            if (StringUtil.isEmpty(jsonParam)){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("merchantFlowVo不能为空");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            MerchantFlowVo merchantFlowVo = JsonUtil.toBean(jsonParam,MerchantFlowVo.class);
            employerMainstayRelationBiz.afterFlow(merchantFlowVo);
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AUTO_REPORT,selectorExpression = MessageMsgDest.TAG_AUTO_REPORT,consumeThreadMax = 1,consumerGroup = "AutoReportAfterAuthConsumer")
    public class AutoReportAfterAuthListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {
            if (StringUtil.isEmpty(jsonParam)){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("msg不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            final ReportEntity reportEntity = JsonUtil.toBean(jsonParam, ReportEntity.class);
            reportBiz.report(reportEntity);
        }
    }

//    @Component
//    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_APPROVAL_ASYNC,selectorExpression = MessageMsgDest.TAG_CHANGE_ACCOUNT,consumeThreadMax = 1,consumerGroup = "commonChangeAccount")
//    public class CommonChangeAccountListener extends BaseRocketMQListener<String>{
//        @Override
//        public void validateJsonParam(String jsonParam) {
//
//        }
//
//        @Override
//        public void consumeMessage(String jsonParam) {
//            MerchantFlowVo merchantFlowVo = JSONUtil.toBean(jsonParam,MerchantFlowVo.class);
//            employerAccountInfoBiz.updateAccount(merchantFlowVo);
//        }
//    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CREATE_RELATION,selectorExpression = MessageMsgDest.TAG_CREATE_RELATION,consumeThreadMax = 3,consumerGroup = "annoCreateRelation")
    public class AnnoCreateRelationListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            EmployerMainstayRelation employerMainstayRelation = JsonUtil.toBean(jsonParam,EmployerMainstayRelation.class);
            employerMainstayRelationBiz.create(employerMainstayRelation);
        }
    }
    
}
