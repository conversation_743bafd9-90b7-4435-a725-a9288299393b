/*
 * Powered By [joinPay.com]
 */
package com.zhixianghui.service.common.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class BankCardBinDao extends MyBatisDao<BankCardBin, Long> {

    public BankCardBin getByCardBin(String cardBin, Integer status, Integer cardLength) {
        Map<String, Object> param = new HashMap<>();
        param.put("cardBin", cardBin);
        param.put("status", status);
        param.put("cardLength", cardLength);
        List<BankCardBin> bankCardBins = listBy(param);
        return CollectionUtils.isEmpty(bankCardBins) ? null:bankCardBins.get(0);
    }
}
