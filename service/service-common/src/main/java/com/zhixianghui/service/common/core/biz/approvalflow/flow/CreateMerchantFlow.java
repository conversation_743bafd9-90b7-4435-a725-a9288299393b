package com.zhixianghui.service.common.core.biz.approvalflow.flow;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.base.BaseFlow;
import com.zhixianghui.service.common.core.biz.approvalflow.base.FlowInitiator;
import com.zhixianghui.service.common.core.biz.approvalflow.responsibilitychain.Approver;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 定义创建商户审批流程链
 * @date 2020-08-12 09:54
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CreateMerchantFlow extends BaseFlow {

    private final FlowInitiator flowInitiator;
    private final Approver createMerchantApprover1;

    @Override
    public ApprovalInfoVo handleFlow(ApprovalFlow approvalFlow){
        flowInitiator
                .setApprover(createMerchantApprover1);
        return flowInitiator.handleFlow(approvalFlow);
    }

}
