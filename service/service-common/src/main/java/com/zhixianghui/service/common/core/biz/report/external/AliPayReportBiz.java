package com.zhixianghui.service.common.core.biz.report.external;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.common.constants.ReportConstants;
import com.zhixianghui.facade.common.dto.ReportEditDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.ReportChannelRecord;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AliPayReportBiz extends AbstractReportBiz {

    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private NotifyFacade notifyFacade;

    private final RedisClient redisClient;

    @Override
    public void reportDetail(ReportEntity reportEntity) throws Exception {
        //验证商户参数是否正确，并返回签约号等数据
        Map<String,Object> map = validMerchantNo(reportEntity);
        String mailBody = null;
        String extAgreementNo = (String) map.get("extAgreementNo");

        //判断邮件信息是否为空
        Merchant merchant = (Merchant) map.get("merchant");
        if (StringUtil.isEmpty(merchant.getContactEmail())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("商户联系人邮件信息为空，无法发送签约邮件");
        }


        if (StringUtil.isEmpty(extAgreementNo)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数有误");
        }

        try {
            //反查报备信息，判断是否已经申请报备过
            String resBody = alipayFacade.agreementQuery(extAgreementNo,null);
            JSONObject resObj = JSONObject.parseObject(resBody).getJSONObject(ReportConstants.ALIPAY_USER_AGREEMENT_QUERY_RESPONSE);
            String code = resObj.getString("code");
            if (code.equals("10000")){
                //说明已经报备过，执行报备签约后续的操作：回填数据，创建（查询）记账本等
                reportRepeat(resObj,reportEntity,extAgreementNo);
            }else{
                mailBody = alipayFacade.sign(extAgreementNo,reportEntity.getChannelType());
                afterReport(reportEntity,mailBody,extAgreementNo,merchant);
            }
        }catch (BizException ex){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(ex.getErrMsg());
        }
    }

    public String reportMerchantDiy(ReportEntity reportEntity) {
        Long recordId = doRecord(reportEntity);
        reportEntity.setRecordId(recordId);
        Map<String,Object> map = validMerchantNo(reportEntity);
        String mailBody = null;
        String extAgreementNo = (String) map.get("extAgreementNo");
        try {
            //反查报备信息，判断是否已经申请报备过
            String resBody = alipayFacade.agreementQuery(extAgreementNo,null);
            JSONObject resObj = JSONObject.parseObject(resBody).getJSONObject(ReportConstants.ALIPAY_USER_AGREEMENT_QUERY_RESPONSE);
            String code = resObj.getString("code");
            if (code.equals("10000")){
                //说明已经报备过，执行报备签约后续的操作：回填数据，创建（查询）记账本等
                reportRepeat(resObj,reportEntity,extAgreementNo);
                return "10000";
            }else{
                mailBody = alipayFacade.sign(extAgreementNo,reportEntity.getChannelType());
                return mailBody;
            }
        }catch (BizException ex){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(ex.getErrMsg());
        }
    }

    /**
     * 重复报备处理
     * @param reportEntity
     */
    private void reportRepeat(JSONObject resObj, ReportEntity reportEntity,String extAgreementNo) {
        String channelType = reportEntity.getChannelType() == null ? "" : reportEntity.getChannelType().toString();
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        reportChannelRecord.setSerialNo(ChannelNoEnum.ALIPAY.name() + extAgreementNo + channelType);
        reportChannelRecord.setStatus(ReportStatusEnum.CONTRACT.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecordBiz.update(reportChannelRecord);
        //
        resObj.put("out_biz_channel_type",reportEntity.getChannelType());
        notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REPORT_REPEAT,NotifyTypeEnum.ALI_REPORT_REPEAT.getValue(),MessageMsgDest.TAG_ALIPAU_REPROT_REPEAT,resObj.toJSONString());
    }

    /**
     * 报备后操作
     * @param reportEntity
     * @param resUrl
     */
    private void afterReport(ReportEntity reportEntity, String resUrl,String extAgreementNo,Merchant merchant) {
        String channelType = reportEntity.getChannelType() == null ? "" : reportEntity.getChannelType().toString();
        //签约邮件发送成功，填写报备记录
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        //加上通道类型唯一
        reportChannelRecord.setSerialNo(ChannelNoEnum.ALIPAY.name() + extAgreementNo + channelType);
        reportChannelRecord.setStatus(ReportStatusEnum.CONTRACT.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecordBiz.update(reportChannelRecord);

        Long acceptTimes = getSearchTimes(extAgreementNo);
        //
        reportEntity.setExtAgreement(extAgreementNo);
        //签约成功才会进行回调，发送延时消息进行查询
        notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REPORT_DELAY, NotifyTypeEnum.ALI_DELAY_SEARCH.getValue(),MessageMsgDest.TAG_ALIPAY_REPORT_DELAY, JsonUtil.toString(reportEntity), MsgDelayLevelEnum.M_30.getValue());

        //不同通道类型均只需要签约一次
        //邮件发送时间限制，限制5分钟发送一次
        String hasbeenSend = redisClient.get(ReportConstants.REPORT_MAIL_KEY + extAgreementNo);
        if (!StringUtil.isEmpty(hasbeenSend)){
            return;
        }
        boolean isSend = sendEmail(resUrl,merchant);
        if (!isSend){
            log.info("签约邮件发送失败");
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("签约邮件发送失败");
        }
        //设置邮件发送限制
        redisClient.set(ReportConstants.REPORT_MAIL_KEY + extAgreementNo,extAgreementNo,ReportConstants.REPORT_MAIL_LIMIT);
    }

    public Long getSearchTimes(String extAgreementNo) {
        Long searchTimes = 0L;
        try {
            searchTimes = redisClient.incr(ReportConstants.REPORT_DELAY_SEARCH_TIMES + extAgreementNo);
            redisClient.expire(ReportConstants.REPORT_DELAY_SEARCH_TIMES + extAgreementNo,5*60*60);
        }catch (Exception e){
            log.error("延时查询支付宝报备结果异常，查询停止", extAgreementNo, e);
        }
        return searchTimes;
    }

    /**
     * 异步发送签约邮件
     * @param resUrl
     * @return
     */
    private boolean sendEmail(String resUrl,Merchant merchant) {
        EmailParamDto emailParamDto = new EmailParamDto();
        emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        emailParamDto.setTo(merchant.getContactEmail());
        emailParamDto.setSubject("【智享汇】请确认专属账户的开通");
        emailParamDto.setTpl(EmailTemplateEnum.APPLY_ALIPAY_REPORT.getName());
        Map<String,Object> map = new HashMap<>();
        map.put("alipayUrl",resUrl);
        map.put("merchantName",merchant.getMchName());
        emailParamDto.setTplParam(map);
        emailParamDto.setHtmlFormat(true);
        //异步发送邮件
        return emailFacade.sendAsync(emailParamDto);
    }

    /**
     * 数据校验并返回商户签约号
     * @param reportEntity
     * @return
     */
    private Map<String,Object> validMerchantNo(ReportEntity reportEntity) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer extAgreementNo = null;
        //报备类型为供应商类型
        if (reportEntity.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()){
            Merchant merchant = merchantQueryFacade.getByMchNo(reportEntity.getMainstayNo());
            if (merchant == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到供应商编号为[" + reportEntity.getMainstayNo() + "]的供应商");
            }
            if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商未激活,供应商编号为[" + reportEntity.getMainstayNo() + "]的供应商");
            }
            extAgreementNo = new StringBuffer(merchant.getMchNo());
            map.put("merchant",merchant);
        }

        //报备类型为商户类型报备
        if (reportEntity.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            //由于前面跳过此校验，因此需要重新校验
            //校验供应商-通道
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(), reportEntity.getPayChannelNo());
            if(mainstayChannelRelation == null || mainstayChannelRelation.getStatus().equals(OpenOffEnum.OFF.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商-通道(供应商账户)不存在或者状态未开启 请检查！MainstayNo:" + reportEntity.getMainstayNo() + ", PayChannelNo:"+ reportEntity.getPayChannelNo());
            }
            //判断商户是否存在
            Merchant merchant = merchantQueryFacade.getByMchNo(reportEntity.getEmployerNo());
            if (merchant == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到商户编号为[" + reportEntity.getEmployerNo() + "]的商户");
            }
            extAgreementNo = new StringBuffer(merchant.getMchNo());
            extAgreementNo.append(reportEntity.getMainstayNo());
            map.put("merchant",merchant);
        }
        map.put("extAgreementNo",extAgreementNo.toString());
        return map;
    }

    protected Long doRecord(ReportEntity reportEntity) {
        //清除报备日志-目的只为保留一个记录
        reportChannelRecordBiz.deleteByEmployerNoAndMainstayNoAndPayChannelNoAndType(
                reportEntity.getEmployerNo(),reportEntity.getMainstayNo(),reportEntity.getPayChannelNo(),reportEntity.getChannelType(),reportEntity.getMerchantType()
        );

//        reportChannelRecordBiz.deleteByEmployerNoAndMainstayNoAndPayChannelNo(
//                reportEntity.getEmployerNo(),reportEntity.getMainstayNo(),reportEntity.getPayChannelNo());

        ReportChannelRecord reportChannelRecord = new ReportChannelRecord();
        //初始记录为编号 不唯一
        BeanUtils.copyProperties(reportEntity,reportChannelRecord);
        reportChannelRecord.setSerialNo(ChannelNoEnum.ALIPAY.name() + reportEntity.getEmployerNo()+reportEntity.getMainstayNo() + ChannelTypeEnum.ALIPAY.getValue());
        reportChannelRecord.setStatus(ReportStatusEnum.CONTRACT.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecordBiz.insert(reportChannelRecord);
        return reportChannelRecord.getId();
    }

    @Override
    protected boolean modify(ReportEditDto reportDto, EmployerAccountInfo employerAccountInfo) {
        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持修改报备信息");
    }
}
