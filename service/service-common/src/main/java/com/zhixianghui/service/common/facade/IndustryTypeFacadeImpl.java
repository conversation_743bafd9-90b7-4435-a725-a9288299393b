package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.config.IndustryType;
import com.zhixianghui.facade.common.service.IndustryTypeFacade;
import com.zhixianghui.service.common.core.biz.config.IndustryTypeBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 行业类型
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
public class IndustryTypeFacadeImpl implements IndustryTypeFacade {
    @Autowired
    private IndustryTypeBiz industryTypeBiz;

    @Override
    public void insert(IndustryType industryType) {
        industryTypeBiz.insert(industryType);
    }

    @Override
    public void update(IndustryType industryType) {
        industryTypeBiz.update(industryType);
    }

    @Override
    public void delete(long id) {
        industryTypeBiz.delete(id);
    }

    @Override
    public IndustryType getByCode(String industryTypeCode) {
        return industryTypeBiz.getByCode(industryTypeCode);
    }

    @Override
    public List<IndustryType> listSubIndustryType(Long parentId) {
        return industryTypeBiz.listSubIndustryType(parentId);
    }

    @Override
    public List<IndustryType> listAll() {
        return industryTypeBiz.listAll();
    }

}
