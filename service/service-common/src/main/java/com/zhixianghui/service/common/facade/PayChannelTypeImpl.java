package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.report.PayChannelType;
import com.zhixianghui.facade.common.service.PayChannelTypeFacade;
import com.zhixianghui.service.common.core.biz.report.PayChannelTypeBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-06-25
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PayChannelTypeImpl implements PayChannelTypeFacade {

    private final PayChannelTypeBiz biz;

    @Override
    public List<PayChannelType> getListByChannelNo(String payChannelNo) {
        return biz.getListByChannelNo(payChannelNo);
    }
}
