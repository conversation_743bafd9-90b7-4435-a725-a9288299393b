package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.PayChannelDto;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.entity.report.PayChannelType;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.service.common.core.biz.report.PayChannelBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 支付通道表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PayChannelImpl implements PayChannelFacade {

    private final PayChannelBiz biz;

    @Override
    public void create(PayChannel payChannel, List<PayChannelType> payChannelTypeList) {
        biz.create(payChannel,payChannelTypeList);
    }

    @Override
    public void changeStatus(Long id, Integer status, String operator) {
        biz.changeStatus(id,status,operator);
    }

    @Override
    public void updateIfNotNull(PayChannel payChannel,List<PayChannelType> payChannelTypeList) {
        biz.updateIfNotNull(payChannel,payChannelTypeList);
    }

    @Override
    public void deleteById(Long id) {
        biz.deleteById(id);
    }

    @Override
    public PageResult<List<PayChannel>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public PayChannel getById(Long id) {
        return biz.getById(id);
    }

    @Override
    public List<PayChannel> listAll() {
        return biz.listAll();
    }

    @Override
    public PayChannel getByChannelNo(String payChannelNo) {
        return biz.getByChannelNo(payChannelNo);
    }

    @Override
    public PageResult<List<PayChannelDto>> listCustomPage(Map<String, Object> beanToMap, PageParam pageParam) {
        return biz.listCustomPage(beanToMap,pageParam);
    }
}
