package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.ApprovalFlowBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 审批流程表 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-07
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApprovalFlowImpl implements ApprovalFlowFacade {

    private final ApprovalFlowBiz biz;

    @Override
    public ApprovalInfoVo createFlow(ApprovalFlow approvalFlow) {
        return this.biz.createFlow(approvalFlow);
    }

    @Override
    public PageResult<List<ApprovalFlow>> listByHandlerIdAndPlatformSource(Long handlerId, Integer platformSource, Map<String, Object> flowParamMap, PageParam pageParam) {
        return biz.listByHandlerIdAndPlatformSource(handlerId, platformSource,flowParamMap,pageParam);
    }

    @Override
    public PageResult<List<ApprovalFlow>> listPendingByOperatorIdAndPlatformSource(Long operatorId, Integer platformSource, Map<String, Object> flowParamMap, PageParam pageParam) {
        return biz.listPendingByOperatorIdAndPlatformSource(operatorId, platformSource,flowParamMap,pageParam);
    }

    @Override
    public PageResult<List<ApprovalFlow>> listByInitiatorIdAndPlatformSource(Long initiatorId, Integer platformSource, Map<String, Object> flowParamMap, PageParam pageParam) {
        return biz.listByInitiatorIdAndPlatformSource(initiatorId,platformSource,flowParamMap,pageParam);
    }


    @Override
    public PageResult<List<ApprovalFlow>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public ApprovalFlow getByIdAndHandlerId(Long id, Long handlerId, boolean isAdmin) {
        return biz.getByIdAndHandlerId(id, handlerId,isAdmin);
    }

    @Override
    public void cancelApproval(Long id, Long handlerId, String handlerName, Integer platform, boolean isAdmin) {
        biz.cancelApproval(id, handlerId,handlerName, platform,isAdmin);
    }

    @Override
    public ApprovalFlow getByDetailId(Long detailId) {
        return biz.getByDetailId(detailId);
    }

    @Override
    public void updateExtInfo(UpdateExtInfoVo updateExtInfoVo, boolean isAdmin) {
        biz.updateExtInfo(updateExtInfoVo,isAdmin);
    }

    @Override
    public void updateExtInfoForAgent(UpdateExtInfoVo updateExtInfoVo) {
        biz.updateExtInfoForAgent(updateExtInfoVo);
    }

    @Override
    public ApprovalFlow getOne(Map<String, Object> paramMap) {
        return biz.getOne(paramMap);
    }

}
