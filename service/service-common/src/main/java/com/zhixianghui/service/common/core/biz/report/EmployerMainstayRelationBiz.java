package com.zhixianghui.service.common.core.biz.report;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.MerchantChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantChannel;
import com.zhixianghui.facade.merchant.service.MerchantChannelFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.common.core.dao.EmployerAccountInfoDao;
import com.zhixianghui.service.common.core.dao.EmployerMainstayRelationDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 代征关系表Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmployerMainstayRelationBiz {


    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantChannelFacade merchantChannelFacade;
    @Reference
    private VendorFacade vendorFacade;
    @Reference
    private NotifyFacade notifyFacade;

    private final EmployerMainstayRelationDao employerMainstayRelationDao;
    private final EmployerAccountInfoDao employerAccountInfoDao;

    /**
     * 流程结束后续操作
     */
    public void afterFlow(MerchantFlowVo merchantFlowVo) {
        String employerNo = merchantFlowVo.getMchNo();

        Vendor vendor = vendorFacade.getVendorByNo(merchantFlowVo.getMainstayMchNo());

        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到用工企业编号为[" + employerNo + "]的商户");
        }
//        if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())) {

//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户状态未激活,编号为[" + employerNo + "]的商户");
//        }
        Merchant mainstay = merchantQueryFacade.getByMchNo(vendor.getSupplierNo());
        if (mainstay == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到编号为[" + vendor.getSupplierNo() + "]的代征主体");
        }

        if (getByEmployerNoAndMainstayNo(employerNo, mainstay.getMchNo()) == null) {
            EmployerMainstayRelation employerMainstayRelation = new EmployerMainstayRelation();
            employerMainstayRelation.setEmployerName(merchant.getMchName());
            employerMainstayRelation.setEmployerNo(merchant.getMchNo());
            employerMainstayRelation.setMainstayName(mainstay.getMchName());
            employerMainstayRelation.setMainstayNo(mainstay.getMchNo());
            employerMainstayRelation.setCreateOperator(merchantFlowVo.getUpdator());
            employerMainstayRelation.setUpdateTime(new Date());
            employerMainstayRelation.setUpdateOperator(merchantFlowVo.getUpdator());
            employerMainstayRelation.setStatus(OpenOffEnum.OPEN.getValue());
            create(employerMainstayRelation);
        }

        final Map<String, List<Map<String,Object>>> accounts = merchantFlowVo.getAccounts();
        //自动报备
        accounts.forEach((mainstayNo,value)->{
            for (Map<String, Object> account : value) {
                String channeltypeStr = (String) account.get("channelType");
                String channelName = (String) account.get("channelName");
                String payChannelNo = (String) account.get("payChannelNo");
                String payChannelName = (String) account.get("payChannelName");

                //不报备支付宝通道
                if (payChannelNo.equals(ChannelNoEnum.ALIPAY.name())){
                    continue;
                }

                Integer channeltype = Integer.parseInt(channeltypeStr);
                final EmployerAccountInfo employerAccountInfo = this.getByEmployerNoAndMainstayNoAndChannelType(merchant.getMchNo(), mainstayNo, channeltype);

                if (employerAccountInfo == null || !StringUtils.equals(employerAccountInfo.getPayChannelNo(), payChannelNo) || employerAccountInfo.getStatus().intValue() != OpenOffEnum.OPEN.getValue()) {
                    ReportEntity reportEntity = new ReportEntity();
                    reportEntity.setEmployerNo(merchant.getMchNo());
                    reportEntity.setMerchantType(merchant.getMerchantType());
                    reportEntity.setReporter(merchantFlowVo.getUpdator());
                    reportEntity.setEmployerName(merchant.getMchName());
                    reportEntity.setMainstayNo(mainstayNo);
                    reportEntity.setMainstayName(mainstay.getMchName());
                    reportEntity.setChannelType(channeltype);
                    reportEntity.setPayChannelName(payChannelName);
                    reportEntity.setPayChannelNo(payChannelNo);
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_AUTO_REPORT, merchant.getMchNo(), merchant.getContactPhone(), NotifyTypeEnum.AUTO_REPORT_AFTER_AUTH.getValue(), MessageMsgDest.TAG_AUTO_REPORT, JSON.toJSONString(reportEntity), MsgDelayLevelEnum.S_5.getValue());
                }
            }
        });

        MerchantChannel merchantChannel = merchantChannelFacade.getByMchNo(mainstay.getMchNo());
        if (merchantChannel == null || merchantChannel.getChannelType() != MerchantChannelTypeEnum.INTERFACE.getType()) {
            return;
        }
        // 如果是走接口报备的代征主体, 还需要同步数据给代征主体
        merchantChannelFacade.handle(merchantChannel, merchant);
    }

    private EmployerAccountInfo getByEmployerNoAndMainstayNoAndChannelType(String mchNo, String mainstayNo, Integer channeltype) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", mchNo);
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("channelType", channeltype);
        return employerAccountInfoDao.getOne(paramMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void create(EmployerMainstayRelation employerMainstayRelation) {
        try {
            if (StringUtils.isNotBlank(employerMainstayRelation.getExternalPassword())) {
                employerMainstayRelation.setExternalPasswordEncrypt(employerMainstayRelation.getExternalPassword());
            }
            employerMainstayRelationDao.insert(employerMainstayRelation);
            //建立用工企业支付账户记录 每类通道建立一条
            List<EmployerAccountInfo> list = Arrays.stream(ChannelTypeEnum.values()).map(
                    channelType -> {
                        EmployerAccountInfo employerAccountInfo = new EmployerAccountInfo();
                        employerAccountInfo.setMchName(employerMainstayRelation.getEmployerName());
                        employerAccountInfo.setEmployerName(employerMainstayRelation.getEmployerName());
                        employerAccountInfo.setEmployerNo(employerMainstayRelation.getEmployerNo());
                        employerAccountInfo.setMainstayName(employerMainstayRelation.getMainstayName());
                        employerAccountInfo.setMainstayNo(employerMainstayRelation.getMainstayNo());
                        employerAccountInfo.setChannelType(channelType.getValue());
                        employerAccountInfo.setPayChannelNo("");
                        employerAccountInfo.setStatus(OpenOffEnum.OFF.getValue());
                        employerAccountInfo.setSubMerchantNo("");
                        employerAccountInfo.setParentMerchantNo("");
                        employerAccountInfo.setEmployerKey("");
                        employerAccountInfo.setUpdateTime(new Date());
                        employerAccountInfo.setUpdateOperator(employerMainstayRelation.getUpdateOperator());
                        employerAccountInfo.setCreateOperator(employerMainstayRelation.getCreateOperator());
                        employerAccountInfo.setParentAgreementNo("");
                        employerAccountInfo.setParentAlipayUserId("");
                        employerAccountInfo.setSubAgreementNo("");
                        employerAccountInfo.setSubAlipayUserId("");
                        return employerAccountInfo;
                    }
            ).collect(Collectors.toList());

            for (EmployerAccountInfo employerAccountInfo : list) {
                try {
                    employerAccountInfoDao.insert(employerAccountInfo);
                }catch (DuplicateKeyException e){
                    log.info("供应商：【{}】，商户：【{}】，支付方式：【{}】已存在，略过",employerMainstayRelation.getMainstayNo(),employerMainstayRelation.getEmployerNo(),employerAccountInfo.getChannelType());
                    continue;
                }
            }
        } catch (DuplicateKeyException e) {
            log.error("DuplicateKeyException", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征关系已存在不允许重复创建");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(Long id, Integer status, String operator) {
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationDao.getById(id);
        if (ObjectUtils.isEmpty(employerMainstayRelation)) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此代征关系，id：" + id);
        }
        if (!employerMainstayRelation.getStatus().equals(status)) {
            employerMainstayRelation.setStatus(status);
            employerMainstayRelation.setUpdateTime(new Date());
            employerMainstayRelation.setUpdateOperator(operator);
            employerMainstayRelationDao.update(employerMainstayRelation);
            //如果是禁用的话，要把对应的用工企业都禁用
//            if (status.equals(OpenOffEnum.OFF.getValue())) {
//                employerAccountInfoDao.updateStatusByMainstayNoAndEmployerNo(employerMainstayRelation.getMainstayNo(), employerMainstayRelation.getEmployerNo(), status, operator);
//            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateIfNotNull(EmployerMainstayRelation employerMainstayRelation) {
        employerMainstayRelation.setExternalPasswordEncrypt(employerMainstayRelation.getExternalPassword());
        employerMainstayRelationDao.updateIfNotNull(employerMainstayRelation);
        //更新为禁用时，要把对应的用工企业都禁用
        Integer status = employerMainstayRelation.getStatus();
        if (status.equals(OpenOffEnum.OFF.getValue())) {
            employerAccountInfoDao.updateStatusByMainstayNoAndEmployerNo(employerMainstayRelation.getMainstayNo(), employerMainstayRelation.getEmployerNo(), status, employerMainstayRelation.getUpdateOperator());
        }
    }

    public PageResult<List<EmployerMainstayRelation>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return employerMainstayRelationDao.listPage(paramMap, pageParam);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationDao.getById(id);
        if (employerMainstayRelation == null) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此代征关系，id:" + id);
        }
        // 代征关系解除, 先删除用工企业支付账户记录
//        employerAccountInfoDao.deleteByMainstayNoAndEmployerNo(employerMainstayRelation.getMainstayNo(), employerMainstayRelation.getEmployerNo());
        employerMainstayRelationDao.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByMainstayNo(String mainstayNo) {
        List<EmployerMainstayRelation> mainstayRelations = employerMainstayRelationDao.listBy(MapUtil.builder(new HashMap<String, Object>()).put("mainstayNo", mainstayNo).build());
        for (EmployerMainstayRelation mainstayRelation : mainstayRelations) {
            this.deleteById(mainstayRelation.getId());
        }
    }

    public EmployerMainstayRelation getById(Long id) {
        return employerMainstayRelationDao.getById(id);
    }

    public EmployerMainstayRelation getByEmployerNoAndMainstayNo(String employerNo, String mainstayNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", employerNo);
        paramMap.put("mainstayNo", mainstayNo);
        return employerMainstayRelationDao.getOne(paramMap);
    }

    public EmployerMainstayRelation getByExternalEnterpriseSn(String externalEnterpriseSn) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("externalEnterpriseSn", externalEnterpriseSn);
        return employerMainstayRelationDao.getOne(paramMap);
    }

    public List<EmployerMainstayRelation> listBy(Map<String, Object> paramMap) {
        return employerMainstayRelationDao.listBy(paramMap);
    }

    public void updateEmployerMainstayRelation(EmployerMainstayRelation employerMainstayRelation) {
        employerMainstayRelationDao.update("updateEmployerMainstayRelation", employerMainstayRelation);
    }

    public void batchUpdate(Integer status, List<Long> ids) {
        employerMainstayRelationDao.batchUpdate(status,ids);
    }
}
