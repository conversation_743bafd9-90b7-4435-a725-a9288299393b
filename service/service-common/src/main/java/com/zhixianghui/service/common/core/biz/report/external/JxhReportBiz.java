package com.zhixianghui.service.common.core.biz.report.external;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.enums.bankLink.report.ApiReportStatusEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.service.report.ChannelReportFacade;
import com.zhixianghui.facade.banklink.vo.joinpay.result.ApplyAcountResult;
import com.zhixianghui.facade.banklink.vo.report.ReportReqVo;
import com.zhixianghui.facade.banklink.vo.report.ReportResVo;
import com.zhixianghui.facade.common.dto.ReportEditDto;
import com.zhixianghui.facade.common.entity.report.*;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.service.MerchantBankAccountFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.trade.dto.AcMerchantBalanceAddDto;
import com.zhixianghui.facade.trade.entity.AcRechargeAccount;
import com.zhixianghui.facade.trade.service.AcMerchantBalanceFacade;
import com.zhixianghui.facade.trade.service.AcRechargeAccountFacade;
import com.zhixianghui.service.common.core.biz.report.EmployerAccountInfoBiz;
import com.zhixianghui.service.common.core.biz.report.ReportAccountHistoryBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/***
 * 君享汇报备接口
 */
@Slf4j
@Service
public class JxhReportBiz extends AbstractReportBiz {
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantEmployerMainFacade employerMainFacade;
    @Reference
    private MerchantBankAccountFacade merchantBankAccountFacade;
    @Reference
    private ChannelReportFacade channelReportFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private AcRechargeAccountFacade acRechargeAccountFacade;
    @Reference
    private AcMerchantBalanceFacade acMerchantBalanceFacade;
    @Autowired
    private ReportAccountHistoryBiz reportAccountHistoryBiz;
    @Autowired
    private EmployerAccountInfoBiz employerAccountInfoBiz;

    @Override
    protected void reportDetail(ReportEntity reportEntity) throws Exception {
        // 检验是否存在重复报备
        validateRepeatReportState(reportEntity);
        String employerNo = reportEntity.getEmployerNo();

        //1.获取商户信息
        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到商户编号为[" + employerNo + "]的商户");
        }
        //2. 获取用工企业信息
        MerchantEmployerMain merchantEmployerMain = employerMainFacade.getByMchNo(employerNo);

        //3. 获取商户银行帐户表
        MerchantBankAccount merchantBankAccount = merchantBankAccountFacade.getByMchNo(employerNo);

        //4. 获取供应商服账户
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(), reportEntity.getPayChannelNo());

        //5. 调用bankLink接口报备AcRechargeAccount
        ReportReqVo reportReqVo = fillReportReqVo(reportEntity, mainstayChannelRelation, merchantEmployerMain, merchant, merchantBankAccount);
        ReportResVo reportResVo = channelReportFacade.report(reportReqVo);
        log.info("君享汇新增虚拟账号的返回结果：" + JsonUtil.toString(reportResVo));
        //6. 报备后续处理
        afterReport(reportEntity, reportResVo, reportReqVo, mainstayChannelRelation);
    }

    @Override
    protected boolean modify(ReportEditDto editDto, EmployerAccountInfo employerAccountInfo) {
        //1.参数校验
        String employerNo = editDto.getEmployerNo();
        String mainstayNo = editDto.getMainstayNo();
        String channelNo = ChannelNoEnum.JOINPAY_JXH.name();
        if (StringUtil.isEmpty(employerNo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户编号不能为空");
        } else if (StringUtil.isEmpty(mainstayNo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体编号不能为空");
        }
        //2.获取商户信息
        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到商户编号为[" + employerNo + "]的商户");
        }
        //3. 获取充值账户信息
        AcRechargeAccount rechargeAccount = acRechargeAccountFacade.getRechargeAccountByMchNoAndMainstayNo(employerNo, mainstayNo, channelNo);
        if (rechargeAccount == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未查询到该商户的充值账户信息，请确认当前商户是否已成功报备");
        }
        //4. 获取用工企业信息
        MerchantEmployerMain merchantEmployerMain = employerMainFacade.getByMchNo(employerNo);
        //5. 获取商户银行帐户表
        MerchantBankAccount merchantBankAccount = merchantBankAccountFacade.getByMchNo(employerNo);
        //6. 获取供应商-通道关系
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(mainstayNo, channelNo);
        ReportReqVo reportReqVo = this.fillReportReqVo(editDto, mainstayChannelRelation, merchantEmployerMain, merchant,
                merchantBankAccount, employerAccountInfo);
        //7.请求通道修改账户信息
        ReportResVo reportResVo = channelReportFacade.modify(reportReqVo);
        log.info("君享汇修改虚拟账号的返回结果：{}", JsonUtil.toString(reportResVo));
        //8.如果通道修改成功，则更新充值账户的信息、商户银行账户表
        return this.handleEditResult(reportResVo, employerNo, mainstayNo, rechargeAccount.getId(), employerAccountInfo);
    }

    private boolean handleEditResult(ReportResVo reportResVo, String employerNo, String mainstayNo,
                                     Long rechargeAccountId, EmployerAccountInfo employerAccountInfo) {
        boolean isOk = false;
        String serialNo = ReportBiz.MODIFY_PRE + employerNo + mainstayNo;
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getBySerialNo(serialNo);
        if (reportResVo.getApiReportStatus() == ApiReportStatusEnum.SUCCESS.getValue()) {
            reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
            reportChannelRecord.setUpdateTime(new Date());
            isOk = true;
        } else {
            log.info("君享汇-受理失败，失败原因：{}" , reportResVo.getBizMsg());
            reportChannelRecord.setStatus(ReportStatusEnum.FAIL.getValue());
            reportChannelRecord.setUpdateTime(new Date());
            reportChannelRecord.setErrMsg(reportResVo.getBizMsg());
        }
        reportChannelRecordBiz.update(reportChannelRecord);
        if (!isOk) {
            return false;
        } else {
            return true;
        }

//        try {
//            ApplyAcountResult result = JsonUtil.toBean(reportResVo.getData(), ApplyAcountResult.class);
//            //1.更新充值账户的信息
//            String accountName = result.getPayee_account_name();
//            acRechargeAccountFacade.updateAccountInfo(rechargeAccountId, null, accountName);
//            //2.更新商户银行帐户表
//            MerchantBankAccount merchantBankAccount = merchantBankAccountFacade.getByMchNo(employerNo);
//            merchantBankAccount.setAccountName(accountName);
//            merchantBankAccountFacade.update(merchantBankAccount);
//            //3.更新用工企业出款账户名称
//            employerAccountInfo.setEmployerName(accountName);
//            employerAccountInfoBiz.update(employerAccountInfo);
//            return true;
//        } catch (Exception e) {
//            log.error("君享汇通道侧虚拟账户修改成功，但本地账户信息修改出现异常 ReportResVo: {}", JsonUtil.toString(reportResVo), e);
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道侧修改成功，但本地账户信息更新失败");
//        }
    }

    /***
     * 检测是否重复报备
     * @param reportEntity
     */
    private void validateRepeatReportState(ReportEntity reportEntity) {
        // 检查是否存在虚拟账号数据，如果存在则不允许报备
        AcRechargeAccount model = new AcRechargeAccount();
        model.setPayChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
        model.setMchNo(reportEntity.getEmployerNo());
        model.setMainstayNo(reportEntity.getMainstayNo());
        long count = acRechargeAccountFacade.getCountByModel(model);
        if (count > 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("存在已报备的数据，请勿重复报备");
        }
    }

    private ReportReqVo fillReportReqVo(ReportEntity reportEntity,
                                        MainstayChannelRelation mainstayChannelRelation,
                                        MerchantEmployerMain merchantEmployerMain,
                                        Merchant merchant,
                                        MerchantBankAccount merchantBankAccount) {
        ReportReqVo reportReqVo = new ReportReqVo();
        reportReqVo.setChannelNo(reportEntity.getPayChannelNo());
        reportReqVo.setChannelMchNo(mainstayChannelRelation.getChannelMchNo());
        reportReqVo.setChannelType(ChannelTypeEnum.BANK.getValue());
        reportReqVo.setLoginName(merchantEmployerMain.getMchNo() + mainstayChannelRelation.getMainstayNo());
        reportReqVo.setMchName(merchant.getMchName());
        reportReqVo.setAccountName(merchantBankAccount.getAccountName());
        reportReqVo.setAccountNo(merchantBankAccount.getAccountNo());
        reportReqVo.setBankChannelNo(merchantBankAccount.getBankChannelNo());
        return reportReqVo;
    }

    private ReportReqVo fillReportReqVo(ReportEditDto editDto,
                                        MainstayChannelRelation mainstayChannelRelation,
                                        MerchantEmployerMain merchantEmployerMain,
                                        Merchant merchant,
                                        MerchantBankAccount merchantBankAccount,
                                        EmployerAccountInfo employerAccountInfo) {
        ReportReqVo reportReqVo = new ReportReqVo();
        reportReqVo.setChannelNo(editDto.getPayChannelNo());
        reportReqVo.setChannelMchNo(mainstayChannelRelation.getChannelMchNo());
        reportReqVo.setChannelType(ChannelTypeEnum.BANK.getValue());
        reportReqVo.setLoginName(merchantEmployerMain.getMchNo() + mainstayChannelRelation.getMainstayNo());
        reportReqVo.setMchName(merchant.getMchName());
//        reportReqVo.setAccountNo(merchantBankAccount.getAccountNo());
        reportReqVo.setAccountName(employerAccountInfo.getMchName());
        reportReqVo.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
        reportReqVo.setBankChannelNo(merchantBankAccount.getBankChannelNo());
        return reportReqVo;
    }

    /***
     * 报备后的操作
     * @param reportEntity
     * @param mainstayChannelRelation
     */
    private void afterReport(ReportEntity reportEntity,
                             ReportResVo reportResVo,
                             ReportReqVo reportReqVo,
                             MainstayChannelRelation mainstayChannelRelation) {
        Integer reportStatus = reportResVo.getApiReportStatus();
        if (reportStatus.equals(ApiReportStatusEnum.SUCCESS.getValue())) {
            doSuccess(reportEntity, reportResVo, reportReqVo);
        } else if (reportStatus.equals(ApiReportStatusEnum.FAIL.getValue())) {
            log.info("君享汇-受理失败，失败原因：{}", reportResVo.getBizMsg());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("受理失败" + reportResVo.getBizMsg());
        } else if (reportStatus.equals(ApiReportStatusEnum.UN_KNOW.getValue())) {
            log.info("君享汇-受理未知，失败原因：{}", reportResVo.getBizMsg());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("受理未知" + reportResVo.getBizMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    void doSuccess(ReportEntity reportEntity,
                   ReportResVo reportResVo,
                   ReportReqVo reportReqVo) {
        log.info("君享汇-报备成功, 更新报备记录, reportEntity: {}, reportResVo: {}", JsonUtil.toString(reportEntity), JsonUtil.toString(reportResVo));
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        String mchNo = null;
        if (reportResVo.getData() != null) {
            ApplyAcountResult result = JSONUtil.toBean(reportResVo.getData(), ApplyAcountResult.class);
            mchNo = result.getMch_no();
        }
        String serialNo = ChannelNoEnum.JOINPAY_JXH.name() + mchNo;
        reportChannelRecord.setSerialNo(serialNo);
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
        reportChannelRecord.setRespData(reportResVo.getData());
        reportChannelRecordBiz.update(reportChannelRecord);
        ApplyAcountResult result = JsonUtil.toBean(reportResVo.getData(), ApplyAcountResult.class);
        // 保存报备后返回的数据
        AcRechargeAccount model = new AcRechargeAccount();
        model.setPayChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
        model.setPayChannelName(ChannelNoEnum.JOINPAY_JXH.getDesc());
        model.setMchNo(reportChannelRecord.getEmployerNo());
        model.setMchName(reportChannelRecord.getEmployerName());
        model.setMainstayNo(reportChannelRecord.getMainstayNo());
        model.setMainstayName(reportChannelRecord.getMainstayName());
        model.setState(Convert.toInt(result.getPayee_account_no_status()));
        model.setAccountNo(result.getPayee_account_no());
        model.setAccountName(result.getPayee_account_name());
        model.setAccountBanch(result.getPayee_account_banch());
        model.setAccountLoc(result.getPayee_account_loc());
        model.setAccountBkno(result.getPayee_account_bkno());
        model.setAccountBank(result.getPayee_account_bank());
        acRechargeAccountFacade.checkExistOrInsert(model);
        log.info("君享汇-报备成功, 将充值账号保存到本地供商户查询");

        PayChannel payChannel = payChannelBiz.getByChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
        log.info("支付渠道[{}]的状态[{}]", ChannelNoEnum.JOINPAY_JXH.name(), payChannel.getStatus());
        //查询特约商户号
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(),
                ChannelNoEnum.JOINPAY_JXH.name());
        log.info("报备完成流程，根据供应商编号[{}]支付通道[{}]查询通道关系数据：{}", reportEntity.getMainstayNo(), ChannelNoEnum.JOINPAY_JXH.name(),
                JsonUtil.toString(mainstayChannelRelation));
        if (reportEntity.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) {
            if (payChannel.getStatus() == OpenOffEnum.OPEN.getValue()) {
                mainstayChannelRelation.setStatus(OpenOffEnum.OPEN.getValue());
                // 创建本地账户信息
                AcMerchantBalanceAddDto balance = new AcMerchantBalanceAddDto();
                balance.setMainstayName(model.getMainstayName());
                balance.setMainstayNo(model.getMainstayNo());
                balance.setMchNo(model.getMchNo());
                balance.setMchName(model.getMchName());
                balance.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
                balance.setPayChannelNo(model.getPayChannelNo());
                balance.setPayChannelName(model.getPayChannelName());
                acMerchantBalanceFacade.createMerchantBalance(balance);
            } else {
                mainstayChannelRelation.setStatus(OpenOffEnum.OFF.getValue());
            }
            mainstayChannelRelationBiz.update(mainstayChannelRelation);
            log.info("君享汇-报备成功, 创建本地账户并且修改渠道关联数据成功");
        }

        EmployerAccountInfo employerAccountInfo =
                employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(
                        reportChannelRecord.getEmployerNo(),
                        reportChannelRecord.getMainstayNo(),
                        reportChannelRecord.getChannelType());

        if (payChannel.getStatus().equals(OpenOffEnum.OPEN.getValue())
                && mainstayChannelRelation.getStatus().equals(OpenOffEnum.OPEN.getValue())) {
            employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
        } else {
            employerAccountInfo.setStatus(OpenOffEnum.OFF.getValue());
        }

        employerAccountInfo.setUpdateTime(new Date());
        employerAccountInfo.setUpdateOperator(reportEntity.getReporter());
        employerAccountInfo.setParentMerchantNo(mainstayChannelRelation.getChannelMchNo());
        employerAccountInfo.setSubMerchantNo(result.getPayee_account_no());
        employerAccountInfo.setPayChannelName(reportChannelRecord.getPayChannelName());
        employerAccountInfo.setPayChannelNo(reportChannelRecord.getPayChannelNo());
        employerAccountInfo.setSubAgreementNo("");
        employerAccountInfo.setSubAlipayUserId("");
        employerAccountInfoBiz.update(employerAccountInfo);
    }
}
