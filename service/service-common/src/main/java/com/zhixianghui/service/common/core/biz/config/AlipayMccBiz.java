package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.facade.common.vo.AlipayMccVo;
import com.zhixianghui.service.common.core.dao.mapper.AlipayMccMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-06-07
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AlipayMccBiz {

    private final AlipayMccMapper alipayMccMapper;

    public List<AlipayMccVo> listAll() {
        return alipayMccMapper.listAll();
    }
}