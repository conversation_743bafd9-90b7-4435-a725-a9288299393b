package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.black.Blacklist;
import com.zhixianghui.facade.common.service.BlacklistFacade;
import com.zhixianghui.facade.common.vo.BlackListVo;
import com.zhixianghui.service.common.core.biz.black.BlacklistBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-03-01
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BlacklistImpl implements BlacklistFacade {

    private final BlacklistBiz biz;

    @Override
    public Blacklist getBlacklist(String tag, String subjectNo) {
        return biz.getBlacklist(tag,subjectNo);
    }

    @Override
    public void add(BlackListVo blackListVo, String realName) {
        biz.add(blackListVo,realName);
    }

    @Override
    public void deleteById(Long id) {
        biz.deleteById(id);
    }

    @Override
    public void update(Blacklist blacklist) {
        biz.update(blacklist);
    }

    @Override
    public PageResult<List<Blacklist>> selectPage(Blacklist blacklist, int pageSize, int pageCurrent) {
        return biz.listPage(blacklist,pageSize,pageCurrent);
    }
}
