package com.zhixianghui.service.common.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote;
import com.zhixianghui.facade.common.entity.individualproxy.MainstayIndividualProxy;
import com.zhixianghui.facade.common.service.MainstayIndividualProxyFacade;
import com.zhixianghui.service.common.core.biz.individualproxy.MainstayIndividualProxyBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class MainstayIndividualProxyImpl implements MainstayIndividualProxyFacade {
    @Autowired
    private MainstayIndividualProxyBiz biz;
    @Override
    public MainstayIndividualProxy addIndividualProxyMainstay(MainstayIndividualProxy mainstayIndividualProxy) throws BizException {
        return biz.addIndividualProxyMainstay(mainstayIndividualProxy);
    }

    @Override
    public MainstayIndividualProxy updateIndividualProxyMainstay(MainstayIndividualProxy mainstayIndividualProxy) throws BizException {
        return biz.updateIndividualProxyMainstay(mainstayIndividualProxy);
    }

    @Override
    public MainstayIndividualProxy updateStatus(Integer id, Boolean status) throws BizException{
        return biz.updateStatus(id, status);
    }

    @Override
    public MainstayIndividualProxy addIndividualProxyQuote(Integer id,String parentCateCode, IndividualProxyQuote individualProxyQuote) throws BizException {
        return biz.addIndividualProxyQuote(id,parentCateCode, individualProxyQuote);
    }

    @Override
    public MainstayIndividualProxy updateIndividualProxyQuote(long id, IndividualProxyQuote individualProxyQuote) throws BizException {
        return biz.updateIndividualProxyQuote(id, individualProxyQuote);
    }

    @Override
    public MainstayIndividualProxy deleteIndividualProxyQuote(Integer id, String invoiceCategoryCode) throws BizException {
        return biz.deleteIndividualProxyQuote(id, invoiceCategoryCode);
    }

    @Override
    public Page<MainstayIndividualProxy> listPage(Page<MainstayIndividualProxy> page, Map<String, Object> paramMap) {
        return biz.listPage(page, paramMap);
    }

    @Override
    public List<MainstayIndividualProxy> listAll(Map<String, Object> paramMap) {
        return biz.listAll(paramMap);
    }

    @Override
    public MainstayIndividualProxy getByMainstayNo(String mainstayNo) {
        return biz.getByMainstayNo(mainstayNo);
    }
}
