package com.zhixianghui.service.common.core.dao;


import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.common.entity.globallock.GlobalLock;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class GlobalLockDao extends MyBatisDao<GlobalLock, Long> {

    public GlobalLock getByResourceId(String resourceId) {
        Map<String, Object> param = new HashMap<>(3);
        param.put("resourceId", resourceId);
        return getOne("getByResourceId", param);
    }


}
