package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.AccountInfoCacheFacade;
import com.zhixianghui.service.common.core.biz.cache.CacheBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @ClassName AccountInfoCacheFacade
 * @Description TODO
 * @Date 2022/12/27 15:58
 */
@Service
public class AccountInfoCacheFacadeImpl implements AccountInfoCacheFacade {

    @Autowired
    private CacheBiz cacheBiz;

    @Override
    public MainstayChannelRelation getMainstayAccountInfo(String mainstayNo,String payChannelNo) {
        return cacheBiz.getMainstayAccountInfo(mainstayNo,payChannelNo);
    }
}
