package com.zhixianghui.service.common;

import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * 数据统计服务
 *
 * <AUTHOR> <PERSON>
 */
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
public class ServiceCommonApp {
    public static void main(String[] args) {
        new SpringApplicationBuilder(ServiceCommonApp.class).web(WebApplicationType.NONE).run(args);
    }
}
