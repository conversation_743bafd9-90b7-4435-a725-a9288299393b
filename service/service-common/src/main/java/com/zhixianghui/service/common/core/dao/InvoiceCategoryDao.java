/*
 * Powered By [joinPay.com]
 */
package com.zhixianghui.service.common.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.Map;

/**
 * 发票类目
 * <AUTHOR>
 * @date 2020-08-10
 */
@Repository
public class InvoiceCategoryDao extends MyBatisDao<InvoiceCategory, Long> {

    /**
     * 发票类目查询
     * @param categoryCode
     * @return
     */
    public InvoiceCategory getByCategoryCode(String categoryCode) {
        if (StringUtil.isEmpty(categoryCode)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("categoryCode不能为空");
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("invoiceCategoryCode", categoryCode);
        return super.getOne(paramMap);
    }
}
