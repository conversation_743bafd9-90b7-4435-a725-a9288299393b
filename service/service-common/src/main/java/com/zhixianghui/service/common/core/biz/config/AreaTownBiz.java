package com.zhixianghui.service.common.core.biz.config;


import cn.hutool.core.map.MapUtil;
import com.zhixianghui.facade.common.entity.config.AreaTown;
import com.zhixianghui.service.common.core.dao.AreaTownDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <p>
* 地区管理--镇区表Biz
* </p>
*
* <AUTHOR>
* @version 创建时间： 2020-08-31
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AreaTownBiz{

    private final AreaTownDao areaProvinceDao;

    public List<AreaTown> listBy(Map<String, Object> paramMap) {
        return areaProvinceDao.listBy(paramMap);
    }

    public List<AreaTown> listTownAll() {
        return areaProvinceDao.listAll();
    }

    public AreaTown getByTownNo(String townNo) {
        return areaProvinceDao.getOne(Collections.singletonMap("townNo", townNo));
    }

    public AreaTown getByTownName(String cityNo, String townName) {
        return areaProvinceDao.getOne(MapUtil.builder(new HashMap<String, Object>()).put("townName", townName).put("cityNo", cityNo).build());
    }
}
