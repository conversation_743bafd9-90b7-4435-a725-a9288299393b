package com.zhixianghui.service.common.core.biz.individualproxy;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.service.utils.MybatisUtil;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote;
import com.zhixianghui.facade.common.entity.individualproxy.MainstayIndividualProxy;
import com.zhixianghui.service.common.core.dao.mapper.MainstayIndividualProxyMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class MainstayIndividualProxyBiz extends ServiceImpl<MainstayIndividualProxyMapper,MainstayIndividualProxy> {

    public MainstayIndividualProxy addIndividualProxyMainstay(MainstayIndividualProxy mainstayIndividualProxy) {
        this.save(mainstayIndividualProxy);
        return mainstayIndividualProxy;
    }

    public MainstayIndividualProxy updateIndividualProxyMainstay(MainstayIndividualProxy mainstayIndividualProxy) {
        this.updateById(mainstayIndividualProxy);
        mainstayIndividualProxy = this.getById(mainstayIndividualProxy.getId());
        return mainstayIndividualProxy;
    }

    public MainstayIndividualProxy updateStatus(Integer id, Boolean status) {
        final MainstayIndividualProxy individualProxy = this.getById(id);
        individualProxy.setStatus(status);
        individualProxy.setUpdateTime(LocalDateTimeUtil.now());
        this.updateById(individualProxy);
        return individualProxy;
    }

    public MainstayIndividualProxy addIndividualProxyQuote(int id,String parentCateCode, IndividualProxyQuote individualProxyQuote) {
        final MainstayIndividualProxy individualProxy = this.getById(id);
        List<IndividualProxyQuote> taxQuote = individualProxy.getTaxQuote();
        if (taxQuote == null) {
            taxQuote = new ArrayList<IndividualProxyQuote>();
        }
        if (parentCateCode == null) {
            for (IndividualProxyQuote proxyQuote : taxQuote) {
                if (StringUtils.equals(proxyQuote.getInvoiceCategoryCode(), individualProxyQuote.getInvoiceCategoryCode())) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该类目已有报价单");
                }
            }

            taxQuote.add(individualProxyQuote);
            individualProxy.setTaxQuote(taxQuote);
            this.updateById(individualProxy);
        }else {
            for (IndividualProxyQuote proxyQuote : taxQuote) {
                if (StringUtils.equals(proxyQuote.getInvoiceCategoryCode(), individualProxyQuote.getInvoiceCategoryCode())) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该类目已有报价单");
                }
                List<IndividualProxyQuote> subcategorys = proxyQuote.getSubcategorys();
                if (StringUtils.equals(proxyQuote.getInvoiceCategoryCode(), parentCateCode)) {
                    if (subcategorys == null) {
                        subcategorys = new ArrayList<IndividualProxyQuote>();
                    }
                    for (IndividualProxyQuote subcategory : subcategorys) {
                        if (StringUtils.equals(subcategory.getInvoiceCategoryCode(), individualProxyQuote.getInvoiceCategoryCode())) {
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该类目已有报价单");
                        }
                    }
                    subcategorys.add(individualProxyQuote);
                }
                proxyQuote.setSubcategorys(subcategorys);
            }
            this.updateById(individualProxy);
        }

        return individualProxy;
    }

    public MainstayIndividualProxy updateIndividualProxyQuote(long id, IndividualProxyQuote individualProxyQuote) {
        final MainstayIndividualProxy individualProxy = this.getById(id);
        List<IndividualProxyQuote> taxQuote = individualProxy.getTaxQuote();
        if (taxQuote == null) {
            taxQuote = new ArrayList<IndividualProxyQuote>();
            taxQuote.add(individualProxyQuote);
        }else {
            for (IndividualProxyQuote proxyQuote : taxQuote) {
                if (StringUtils.equals(proxyQuote.getInvoiceCategoryCode(), individualProxyQuote.getInvoiceCategoryCode())) {
                    BeanUtil.copyProperties(individualProxyQuote, proxyQuote);
                    break;
                }

                final List<IndividualProxyQuote> subcategorys = proxyQuote.getSubcategorys();
                if (subcategorys != null && !subcategorys.isEmpty()) {
                    for (IndividualProxyQuote quote : subcategorys) {
                        if (StringUtils.equals(quote.getInvoiceCategoryCode(), individualProxyQuote.getInvoiceCategoryCode())) {
                            BeanUtil.copyProperties(individualProxyQuote, quote);
                            break;
                        }
                    }
                }
            }
        }
        this.updateById(individualProxy);
        return individualProxy;
    }

    public MainstayIndividualProxy deleteIndividualProxyQuote(Integer id, String invoiceCategoryCode) {
        final MainstayIndividualProxy individualProxy = this.getById(id);
        List<IndividualProxyQuote> taxQuote = individualProxy.getTaxQuote();
        final JSONArray jsonArray = JSONUtil.parseArray(taxQuote);
        for (int i = 0; i < jsonArray.size(); i++) {
            final JSONObject quoteJSON = jsonArray.getJSONObject(i);
            if (StringUtils.equals(quoteJSON.getStr("invoiceCategoryCode"), invoiceCategoryCode)) {
                jsonArray.remove(i);
            }else {
                final JSONArray subcategorys = quoteJSON.getJSONArray("subcategorys");
                if (subcategorys != null && !subcategorys.isEmpty()) {
                    for (int j = 0; j < subcategorys.size(); j++) {
                        final JSONObject subcategorysJSONObject = subcategorys.getJSONObject(j);
                        if (StringUtils.equals(subcategorysJSONObject.getStr("invoiceCategoryCode"), invoiceCategoryCode)) {
                            subcategorys.remove(i);
                        }
                    }
                }
            }
        }
        final List<IndividualProxyQuote> individualProxyQuotes = JSONUtil.toList(jsonArray, IndividualProxyQuote.class);
        individualProxy.setTaxQuote(individualProxyQuotes);
        this.updateById(individualProxy);
        return individualProxy;
    }

    public Page<MainstayIndividualProxy> listPage(Page<MainstayIndividualProxy> page, Map<String, Object> paramMap) {
        QueryWrapper<MainstayIndividualProxy> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, MainstayIndividualProxy.class, paramMap);
        MainstayIndividualProxy.extendWhere(queryWrapper, paramMap);
        return this.page(page, queryWrapper);
    }

    public List<MainstayIndividualProxy> listAll(Map<String, Object> paramMap) {
        QueryWrapper<MainstayIndividualProxy> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, MainstayIndividualProxy.class, paramMap);
        MainstayIndividualProxy.extendWhere(queryWrapper, paramMap);
        return this.list(queryWrapper);
    }

    public MainstayIndividualProxy getByMainstayNo(String mainstayNo) {
        QueryWrapper<MainstayIndividualProxy> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(MainstayIndividualProxy.COL_MAINSTAY_NO, mainstayNo);
        return this.getOne(queryWrapper);
    }
}
