package com.zhixianghui.service.common.core.dao.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
@InterceptorIgnore(tenantLine = "1")
public interface NotificationRecordMapper extends BaseMapper<NotificationRecord> {
    int updateBatch(List<NotificationRecord> list);

    int updateBatchSelective(List<NotificationRecord> list);

    int batchInsert(@Param("list") List<NotificationRecord> list);

    int insertOrUpdate(NotificationRecord record);

    int insertOrUpdateSelective(NotificationRecord record);

    IPage<NotificationRecord> listNotifications(Page<Map<String, Object>> page,@Param("param") Map<String, Object> params);
}
