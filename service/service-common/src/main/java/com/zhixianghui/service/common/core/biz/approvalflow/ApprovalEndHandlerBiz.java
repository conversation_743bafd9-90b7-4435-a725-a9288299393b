package com.zhixianghui.service.common.core.biz.approvalflow;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 运营平台审批各审批完成后操作 //结束流程如果多个服务调用需要分布式事务
 * @date 2020-08-18 09:29
 **/
@Service
@Slf4j
public class ApprovalEndHandlerBiz {

    @Reference
    private NotifyFacade notifyFacade;

    public void endHandle(ApprovalFlow approvalFlow) {
        Integer flowTopicType = approvalFlow.getFlowTopicType();
        if (flowTopicType.equals(FlowTopicType.CREATE_MERCHANT.getValue())) {
            endCreateMerchant(approvalFlow);
        } else if (flowTopicType.equals(FlowTopicType.MERCHANT_VERIFY.getValue())) {
            endMerchantVerify(approvalFlow);
        } else if (flowTopicType.equals(FlowTopicType.CREATE_AGENT.getValue())) {
            endCreateAgent(approvalFlow);
        }
    }

    public void cancelHandle(ApprovalFlow approvalFlow) {
        Integer flowTopicType = approvalFlow.getFlowTopicType();
        if (flowTopicType == null) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("该审批的类型为null: " + JSONObject.toJSONString(approvalFlow));
        }
        if (FlowTopicType.MERCHANT_VERIFY.getValue() == flowTopicType) {
            cancelMerchantVerify(approvalFlow);
        }
        if (FlowTopicType.CREATE_AGENT.getValue() == flowTopicType) {
            cancelAgent(approvalFlow);
        }
    }

    private void cancelAgent(ApprovalFlow approvalFlow) {
//        log.info("取消合伙人认证审批流程,发送消息");
//        notifyFacade.sendOne(
//                MessageMsgDest.TOPIC_APPROVAL_ASYNC,
//                NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),
//                MessageMsgDest.TAG_APPROVAL_AGENT_CANCEL_ASYNC,
//                JSONObject.toJSONString(approvalFlow)
//        );
    }

    private void cancelMerchantVerify(ApprovalFlow approvalFlow) {
        log.info("取消主题认证审批流程,发送消息");
        String content = approvalFlow.getExtInfo();
        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_CANCEL_ASYNC, content);
    }

    /**
     * 创建商户审批流程结束操作
     * 进行商户创建
     *
     * @param approvalFlow 审批对象
     */
    private void endCreateMerchant(ApprovalFlow approvalFlow) {
        log.info("创建商户审批流程结束,发送消息");
        String content = approvalFlow.getExtInfo();
        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_CREATE_MERCHANT_ASYNC, content);
    }

    private void endMerchantVerify(ApprovalFlow approvalFlow) {
        log.info("主题认证审批流程结束，发送消息");
        String content = approvalFlow.getExtInfo();
        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_MERCHANT_VERIFY_ASYNC, content);
    }

    private void endCreateAgent(ApprovalFlow approvalFlow) {
        log.info("创建合伙人审批流程结束，发送消息 激活合伙人");
        String content = approvalFlow.getExtInfo();
        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_CREATE_AGENT_ASYNC, content);
    }

}
