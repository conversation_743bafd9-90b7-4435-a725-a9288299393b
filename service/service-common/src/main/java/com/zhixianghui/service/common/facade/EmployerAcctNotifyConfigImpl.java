package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.report.EmployerAccountNotifyConfig;
import com.zhixianghui.facade.common.service.EmployerAcctNotifyConfigFacade;
import com.zhixianghui.service.common.core.biz.report.EmployerAccountNotifyConfigBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmployerAcctNotifyConfigImpl implements EmployerAcctNotifyConfigFacade {

    final private EmployerAccountNotifyConfigBiz configBiz;

    @Override
    public void updateConfig(EmployerAccountNotifyConfig config) {
        configBiz.updateById(config);
    }

    @Override
    public void addConfig(EmployerAccountNotifyConfig config) {
        configBiz.save(config);
    }

    @Override
    public EmployerAccountNotifyConfig getConfig(String mainstayNo, String employerNo) {
        return configBiz.getConfig(mainstayNo, employerNo);
    }

    @Override
    public List<EmployerAccountNotifyConfig> listNotifyConfig(String employerNo, String mainstayNo) {
        return configBiz.listNotifyConfig(employerNo, mainstayNo);
    }
}
