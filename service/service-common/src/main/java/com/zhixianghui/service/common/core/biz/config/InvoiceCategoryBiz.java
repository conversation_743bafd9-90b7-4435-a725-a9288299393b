package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import com.zhixianghui.service.common.core.dao.InvoiceCategoryDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发票类目
 *
 * <AUTHOR>
 * @date 2020-08-10
 */
@Service
public class InvoiceCategoryBiz {
    @Autowired
    private InvoiceCategoryDao invoiceCategoryDao;

    public void insert(InvoiceCategory invoiceCategory) {
        invoiceCategoryDao.insert(invoiceCategory);
    }

    public void update(InvoiceCategory invoiceCategory) {
        invoiceCategoryDao.update(invoiceCategory);
    }

    public void delete(long id) {
        invoiceCategoryDao.deleteById(id);
    }

    public InvoiceCategory getByCategoryCode(String categoryCode) {
        return invoiceCategoryDao.getByCategoryCode(categoryCode);
    }

    public PageResult<List<InvoiceCategory>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return invoiceCategoryDao.listPage(paramMap, pageParam);
    }

    public List<InvoiceCategory> listAll() {
        return invoiceCategoryDao.listAll("id");
    }
}
