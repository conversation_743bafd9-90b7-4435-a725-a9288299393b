package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.vo.PortalDataDictionaryQueryVO;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.service.common.core.biz.config.DataDictionaryBiz;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DataDictionaryFacadeImpl
 *
 * <AUTHOR>
 * @date 2019/11/14
 */
@Service
public class DataDictionaryFacadeImpl implements DataDictionaryFacade {
    @Autowired
    private DataDictionaryBiz dataDictionaryBiz;
    @Reference
    private ExportRecordFacade exportRecordFacade;

    @Override
    public void export(List<Long> idList, String operator) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator);
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.DATA_DICTIONARY.getFileName());
        record.setReportType(ReportTypeEnum.DATA_DICTIONARY.getValue());
        record.setParamJson(JsonUtil.toString(new HashMap<String, Object>() {{
            put("idList", idList);
        }}));
        DataDictionary dataDictionary = new DataDictionary();
        dataDictionary.build();
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }

    @Override
    public List<DataDictionary> listDataDictionary(List<Long> idList) {
        return dataDictionaryBiz.listDataDictionary(idList);
    }

    @Override
    public DataDictionary getDataDictionaryById(long id) {
        return dataDictionaryBiz.getDataDictionaryById(id);
    }

    @Override
    public void deleteDataDictionaryById(long id) {
        dataDictionaryBiz.deleteDataDictionaryById(id);
    }

    @Override
    public void createDataDictionary(DataDictionary dataDictionary) {
        dataDictionaryBiz.createDataDictionary(dataDictionary);
    }

    @Override
    public void updateDataDictionary(DataDictionary dataDictionary) {
        dataDictionaryBiz.updateDataDictionary(dataDictionary);
    }

    @Override
    public PageResult<List<DataDictionary>> listDataDictionaryPage(Map<String, Object> paramMap, PageParam pageParam) {
        return dataDictionaryBiz.listDataDictionaryPage(paramMap, pageParam);
    }

    @Override
    public List<DataDictionary> listAllDataDictionary() {
        return dataDictionaryBiz.listAllDataDictionary();
    }

    @Override
    public DataDictionary getDataDictionaryByName(String dataName) {
        return dataDictionaryBiz.getDataDictionaryByName(dataName);
    }

    @Override
    public void create(List<DataDictionary> list) {
        dataDictionaryBiz.create(list);
    }

    @Override
    public String getSystemConfig(String field) {
        return dataDictionaryBiz.getSystemConfig(field);
    }

    @Override
    public List<DataDictionary> list(PortalDataDictionaryQueryVO portalDataDictionaryQueryVO) {
        return dataDictionaryBiz.list(portalDataDictionaryQueryVO);
    }
}
