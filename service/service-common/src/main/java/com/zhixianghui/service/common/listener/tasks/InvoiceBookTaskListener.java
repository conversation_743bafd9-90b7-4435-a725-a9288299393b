package com.zhixianghui.service.common.listener.tasks;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.AccountHandleStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceAmountTypeEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.common.entity.config.InvoiceBookingConfig;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.service.MerchantExpressInfoFacade;
import com.zhixianghui.facade.merchant.service.MerchantInvoiceInfoFacade;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.enums.InvoiceSourceEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.service.common.core.service.InvoiceBookingConfigService;
import com.zhixianghui.service.common.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 余额提醒定时任务
 */
@Component
@RocketMQMessageListener(topic = "topic-invoice-book-task",consumeThreadMax = 1,consumerGroup = "invoiceBookGroup")
@Slf4j
public class InvoiceBookTaskListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private InvoiceBookingConfigService configService;
    @Reference
    private InvoiceFacade invoiceFacade;
    @Reference
    private OrderFacade orderFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private MerchantInvoiceInfoFacade merchantInvoiceInfoFacade;
    @Reference
    private MerchantExpressInfoFacade merchantExpressInfoFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private VendorFacade vendorFacade;
    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;



    @Override
    public void runTask(JSONObject jsonParam) {

        try {
            //0. 时间处理
            final LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
            final int dayOfMonth = now.getDayOfMonth();

            final boolean isLastDay = DateUtil.isLastDayOfMonth(new Date());

            //1. 扫描出需要的当日申请开票的配置
            List<InvoiceBookingConfig> list;
            if (isLastDay) {
                list  = configService.list(new QueryWrapper<InvoiceBookingConfig>()
                        .eq(InvoiceBookingConfig.COL_STATUS, CommonStatusEnum.ACTIVE.getValue())
                        .ge(InvoiceBookingConfig.COL_APPLY_DATE, dayOfMonth)
                );
            }else {
                list = configService.list(new QueryWrapper<InvoiceBookingConfig>()
                        .eq(InvoiceBookingConfig.COL_STATUS, CommonStatusEnum.ACTIVE.getValue())
                        .eq(InvoiceBookingConfig.COL_APPLY_DATE, dayOfMonth)
                );
            }

            //2.申请开票 申请普票与专票
            for (InvoiceBookingConfig bookingConfig : list) {
                final String mainstayNo = bookingConfig.getMainstayNo();

                log.info("商户:{},供应商:{}({})-开始自动申请开票", bookingConfig.getEmployerName(), mainstayNo, bookingConfig.getMainstayName());

                final Vendor vendor = vendorFacade.getVendorByNo(mainstayNo);
                if (vendor == null) {
                    continue;
                }
                switch (vendor.getProductNo()) {
                    case "ZXH": {
                        this.applyMajarInvoice(bookingConfig,"ZXH","智享汇", InvoiceAmountTypeEnum.ORDER_AMOUNT.getValue());
                        break;
                    }
                    case "CEP": {
                        this.applyMajarInvoice(bookingConfig,"CEP","差额普", InvoiceAmountTypeEnum.GRANT_AMOUNT.getValue());
                        this.applyMajarInvoice(bookingConfig,"CEP","差额普", InvoiceAmountTypeEnum.SERVICE_FEE.getValue());
                        break;
                    }
                    case "CKH":{
//                        this.applyNormalInvoice(bookingConfig,"CKH","创客汇");
//                        bookingConfig.setInvoiceType(2);
//                        this.applyMajarInvoice(bookingConfig,"CKH","创客汇");
                        break;
                    }
                    case "JKH":{
                        break;
                    }
                }

            }

        } catch (Exception e) {
            log.info("余额提醒定时任务执行异常", e);
            throw e;
        }

    }

    private void applyMajarInvoice(InvoiceBookingConfig bookingConfig,String productNo,String productName,Integer amountType) {
        final String mainstayNo = bookingConfig.getMainstayNo();
        final String employerNo = bookingConfig.getEmployerNo();

        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoFacade.getByMchNo(employerNo);
        if (merchantInvoiceInfo == null) {
            log.info("商户:{},供应商:{}({})-没有发票信息，终止开票", bookingConfig.getEmployerName(), mainstayNo, bookingConfig.getMainstayName());
            return;
        }
        MerchantExpressInfo merchantExpressInfo = merchantExpressInfoFacade.getByMchNo(employerNo);
        if (merchantExpressInfo == null) {
            log.info("商户:{},供应商:{}({})-没有快递信息，终止开票", bookingConfig.getEmployerName(), mainstayNo, bookingConfig.getMainstayName());
            return;
        }

        List<MerchantEmployerPosition> employerPositions = merchantEmployerPositionFacade.listByMchNo(employerNo);

        if (employerPositions != null) {
            log.info(JSONUtil.toJsonPrettyStr(employerPositions));
            for (MerchantEmployerPosition employerPosition : employerPositions) {

                String applyTradeCompleteDayBegin;
                final Vendor vendor = vendorFacade.getVendorByNo(bookingConfig.getMainstayNo());
                if (StringUtils.equals(vendor.getProductNo(), ProductNoEnum.ZXH.getValue())) {
                    applyTradeCompleteDayBegin = invoiceFacade.getApplyTradeCompleteDayBegin(employerNo, mainstayNo,
                            null,null,InvoiceSourceEnum.ON_LINE.getCode(),employerPosition.getWorkCategoryCode(),
                            productNo,amountType);
                } else if (StringUtils.equals(vendor.getProductNo(), ProductNoEnum.CEP.getValue())) {
                    applyTradeCompleteDayBegin = invoiceFacade.getApplyTradeCompleteDayBegin(employerNo, mainstayNo,
                            null,null,InvoiceSourceEnum.ON_LINE.getCode(),employerPosition.getWorkCategoryCode(),
                            productNo,amountType);
                } else if (StringUtils.equals(vendor.getProductNo(), ProductNoEnum.CKH.getValue())) {
                    continue;
                } else {
                    continue;
                }
                log.info("{}-{}-{}-{}:开始申请开票", employerNo, mainstayNo, employerPosition.getWorkCategoryName(), applyTradeCompleteDayBegin);

                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("employerNo", employerNo);
                paramMap.put("mainstayNo", mainstayNo);
                paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
                paramMap.put("completeBeginDate", DateUtil.parseTime(applyTradeCompleteDayBegin+ " 00:00:00"));
                paramMap.put("completeEndDate", DateUtil.getDayEnd(DateUtil.addDay(new Date(),-1)));

                // 加入创建时间以优化查询数组，创建时间范围为完成时间往前提3个月
                paramMap.put("createBeginDate", DateUtil.addMonth(DateUtil.parseTime(applyTradeCompleteDayBegin + " 00:00:00"), -3));
                paramMap.put("createEndDate", DateUtil.getDayEnd(DateUtil.addDay(new Date(),-1)));
                paramMap.put("workCategoryCode", employerPosition.getWorkCategoryCode());
                paramMap.put("ignoreZeroAmt", "true");
                BigDecimal invoiceAmount;
                if (StringUtils.equals(vendor.getProductNo(), ProductNoEnum.ZXH.getValue())) {
                    invoiceAmount = orderFacade.sumWaitInvoiceAmount(paramMap);
                } else if (StringUtils.equals(vendor.getProductNo(), ProductNoEnum.CEP.getValue())) {
                    invoiceAmount = orderFacade.sumWaitCepInvoiceAmount(amountType, paramMap);
                } else if (StringUtils.equals(vendor.getProductNo(), ProductNoEnum.CKH.getValue())) {
                    invoiceAmount = orderFacade.sumWaitCkhInvoiceAmount(paramMap);
                }else {
                    continue;
                }

                if (invoiceAmount == null || invoiceAmount.compareTo(BigDecimal.ZERO)==0) {
                    log.info("{}-{}-{}-{}:待开票金额为0，终止开票", employerNo, mainstayNo, employerPosition.getWorkCategoryName(), applyTradeCompleteDayBegin);
                    continue;
                }

                log.info("[{}-{}-{}:待开票金额-{}]",employerNo,mainstayNo,applyTradeCompleteDayBegin,invoiceAmount);

                String trxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getPrefix(),
                        SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getKey(),
                        SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getWidth());


                InvoiceRecord invoiceRecord = fillInvoiceRecord(bookingConfig,
                        employerPosition,
                        merchantInvoiceInfo,
                        merchantExpressInfo,
                        productNo,
                        productName,
                        bookingConfig.getInvoiceType(),
                        amountType,
                        invoiceAmount,
                        applyTradeCompleteDayBegin,
                        DateUtil.formatDate(DateUtil.getDayEnd(DateUtil.addDay(new Date(),-1))),
                        trxNo);

                invoiceFacade.applyInvoice(invoiceRecord,null);
            }
        }
    }

    private InvoiceRecord fillInvoiceRecord(InvoiceBookingConfig bookingConfig,
                                            MerchantEmployerPosition employerPosition,
                                            MerchantInvoiceInfo merchantInvoiceInfo,
                                            MerchantExpressInfo merchantExpressInfo,
                                            String productNo,
                                            String productName,
                                            Integer invoiceType,
                                            Integer amountType,
                                            BigDecimal amount,
                                            String tradeCompleteDayBegin,
                                            String tradeCompleteDayEnd,
                                            String trxNo) {

        InvoiceRecord record = new InvoiceRecord();
        record.setUpdateTime(new Date());
        record.setProductNo(productNo);
        record.setProductName(productName);
        record.setEmployerMchNo(bookingConfig.getEmployerNo());
        record.setEmployerMchName(bookingConfig.getEmployerName());
        record.setMainstayMchNo(bookingConfig.getMainstayNo());
        record.setMainstayMchName(bookingConfig.getMainstayName());
        record.setTrxNo(trxNo);
        record.setInvoiceType(invoiceType);
        record.setApplyType(bookingConfig.getApplyType());
        record.setAmountType(amountType);
        record.setInvoiceAmount(amount);
        record.setTradeCompleteDayBegin(tradeCompleteDayBegin);
        record.setTradeCompleteDayEnd(tradeCompleteDayEnd);
        record.setInvoiceCategoryCode(employerPosition.getInvoiceCategoryList().get(0).getInvoiceCategoryCode());
        record.setInvoiceCategoryName(employerPosition.getInvoiceCategoryList().get(0).getInvoiceCategoryName());
        record.setInvoiceStatus(InvoiceStatusEnum.WAIT_ISSUE.getValue());
        record.setAccountHandleStatus(AccountHandleStatusEnum.UN_HANDLE.getValue());
        record.setExpressConsignee(merchantExpressInfo.getConsignee());
        record.setExpressTelephone(merchantExpressInfo.getTelephone());
        record.setProvince(merchantExpressInfo.getProvince());
        record.setCity(merchantExpressInfo.getCity());
        record.setCounty(merchantExpressInfo.getCounty());
        record.setAddress(merchantExpressInfo.getAddress());
        record.setTaxPayerType(merchantInvoiceInfo.getTaxPayerType());
        record.setTaxNo(merchantInvoiceInfo.getTaxNo());
        record.setRegisterAddrInfo(merchantInvoiceInfo.getRegisterAddrInfo());
        record.setAccountNo(merchantInvoiceInfo.getAccountNo());
        record.setBankName(merchantInvoiceInfo.getBankName());
        record.setRemark("系统自动申请开票");
        record.setVersion(0);
        record.setCreateTime(new Date());
        record.setSource(InvoiceSourceEnum.ON_LINE.getCode());
        record.setWorkCategoryCode(employerPosition.getWorkCategoryCode());
        record.setWorkCategoryName(employerPosition.getWorkCategoryName());
        return record;
    }
}
