package com.zhixianghui.service.common.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代征关系表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Repository
public class EmployerMainstayRelationDao extends MyBatisDao<EmployerMainstayRelation, Long> {

    public void batchUpdate(Integer status, List<Long> ids) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("status",status);
        paramMap.put("ids",ids);
        this.getSqlSession().update("batchUpdate",paramMap);
    }
}
