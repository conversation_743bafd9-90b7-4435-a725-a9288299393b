package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.service.TenantWebsiteFacade;
import com.zhixianghui.service.common.core.biz.tenant.TenantWebsiteBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-03-28
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TenantWebsiteImpl implements TenantWebsiteFacade {

    private final TenantWebsiteBiz biz;
}
