package com.zhixianghui.service.common.core.biz.report.external;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.common.dto.ReportEditDto;
import com.zhixianghui.facade.common.entity.report.*;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.service.common.core.biz.report.*;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName WxReportBiz
 * @Description TODO
 * @Date 2021/12/15 18:47
 */
@Service
public class WxReportBiz extends AbstractReportBiz {

    @Autowired
    private ReportChannelRecordBiz reportChannelRecordBiz;

    @Reference
    private NotifyFacade notifyFacade;
    @Autowired
    private EmployerAccountInfoBiz employerAccountInfoBiz;
    @Autowired
    private MainstayChannelRelationBiz mainstayChannelRelationBiz;
    @Autowired
    private PayChannelBiz payChannelBiz;
    @Autowired
    private ReportAccountHistoryBiz reportAccountHistoryBiz;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportDetail(ReportEntity reportEntity) {
        //记录账户报备历史
        ReportAccountHistory reportAccountHistory = new ReportAccountHistory();
        reportAccountHistory.setMainstayNo(reportEntity.getMainstayNo());
        reportAccountHistory.setEmployerNo(reportEntity.getEmployerNo());
        reportAccountHistory.setMerchantType(reportEntity.getMerchantType());
        reportAccountHistory.setPayChannelNo(ChannelNoEnum.WXPAY.name());
        reportAccountHistory.setPayChannelName(ChannelNoEnum.WXPAY.getDesc());
        reportAccountHistory.setPayChannelType(reportEntity.getChannelType());
        reportAccountHistory.setIsShow(YesNoCodeEnum.YES.getValue());
        reportAccountHistory.setTitle(ChannelNoEnum.WXPAY.getDesc());
        reportAccountHistory.setCreateTime(new Date());
        reportAccountHistory.setAlipayUserId("");
        reportAccountHistory.setAgreementNo("");
        //查询通道状态
        PayChannel payChannel = payChannelBiz.getByChannelNo(ChannelNoEnum.WXPAY.name());
        //查询特约商户号
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(),ChannelNoEnum.WXPAY.name());
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        if (reportEntity.getMerchantType().intValue() == MerchantTypeEnum.MAINSTAY.getValue()){
            //代征主体需要联系产品
            reportChannelRecord.setSerialNo(ChannelNoEnum.WXPAY.name() + reportEntity.getMainstayNo());
            reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
            reportChannelRecord.setUpdateTime(new Date());
            reportChannelRecord.setErrMsg("系统已开通代征主体本地支付账户，请联系产品经理向微信官方申请特约商户");
            reportChannelRecordBiz.update(reportChannelRecord);

            if(payChannel.getStatus().equals(OpenOffEnum.OPEN.getValue())){
                mainstayChannelRelation.setStatus(OpenOffEnum.OPEN.getValue());
            }else{
                mainstayChannelRelation.setStatus(OpenOffEnum.OFF.getValue());
            }
            mainstayChannelRelationBiz.update(mainstayChannelRelation);
            //设置reportEntity的employerNo为空字符串，避免联合索引失效
            reportEntity.setEmployerNo("");
            reportEntity.setEmployerName("");
            //设置历史表账户数据
            reportAccountHistory.setChannelMerchantNo(mainstayChannelRelation.getChannelMchNo());
            //创建本地账户表
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_REPORT,reportEntity.getMainstayNo(),
                    UUIDUitl.generateString(10),NotifyTypeEnum.WX_CHANNEL_REPORT.getValue(),MessageMsgDest.TAG_WX_REPORT, JSON.toJSONString(reportEntity));
            reportAccountHistoryBiz.create(reportAccountHistory);
        }else{
            //更新报备记录表
            reportChannelRecord.setSerialNo(ChannelNoEnum.WXPAY.name() + reportEntity.getEmployerNo() + reportEntity.getMainstayNo());
            reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
            reportChannelRecord.setUpdateTime(new Date());
            reportChannelRecord.setErrMsg("系统已开通用工企业本地支付账户，请联系产品经理绑定该用工企业与对应供应商关系");
            reportChannelRecordBiz.update(reportChannelRecord);
            //更新用工企业账户表
            EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(
                    reportEntity.getEmployerNo(),reportEntity.getMainstayNo(), reportEntity.getChannelType());
            employerAccountInfo.setParentMerchantNo(mainstayChannelRelation.getChannelMchNo());
            employerAccountInfo.setSubMerchantNo(reportEntity.getEmployerNo());
            employerAccountInfo.setPayChannelNo(payChannel.getPayChannelNo());
            employerAccountInfo.setPayChannelName(payChannel.getPayChannelName());
            //回调时上游关系都为开启才能开启
            if(payChannel.getStatus().equals(OpenOffEnum.OPEN.getValue())
                    && mainstayChannelRelation.getStatus().equals(OpenOffEnum.OPEN.getValue())){
                employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
            }else{
                employerAccountInfo.setStatus(OpenOffEnum.OFF.getValue());
            }
            employerAccountInfoBiz.update(employerAccountInfo);
            //设置历史表账户数据
            reportAccountHistory.setChannelMerchantNo(employerAccountInfo.getSubMerchantNo());
            reportAccountHistory.setEmployerName(employerAccountInfo.getMchName());
            //创建本地账号表
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_REPORT,reportEntity.getEmployerNo(),
                    UUIDUitl.generateString(10),NotifyTypeEnum.WX_CHANNEL_REPORT.getValue(),MessageMsgDest.TAG_WX_REPORT, JSON.toJSONString(reportEntity));
        }
    }

    @Override
    protected boolean modify(ReportEditDto reportDto, EmployerAccountInfo employerAccountInfo) {
        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持修改报备信息");
    }
}
