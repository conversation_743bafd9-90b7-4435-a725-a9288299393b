package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.service.common.service.SequenceService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
public class SequenceFacadeImpl implements SequenceFacade {
    @Autowired
    SequenceService sequenceService;

    @Override
    public Long nextSnowId() {
        return sequenceService.nextSnowId();
    }

    @Override
    public List<Long> nextSnowId(int count) {
        return sequenceService.nextSnowId(count);
    }

    @Override
    public String nextSnowId(String prefix, boolean isWithDate) {
        return sequenceService.nextSnowId(prefix, isWithDate);
    }

    @Override
    public List<String> nextSnowId(int count, String prefix, boolean isWithDate){
        return sequenceService.nextSnowId(count, prefix, isWithDate);
    }

    @Override
    public Long nextSegmentId(String bizKey){
        return sequenceService.nextSegmentId(bizKey);
    }

    /**
     * 使用数据库批量生成Id序列号
     * @see #nextSegmentId(String)
     * @param bizKey
     * @param count
     * @return
     */
    @Override
    public List<Long> nextSegmentId(String bizKey, int count){
        return sequenceService.nextSegmentId(bizKey, count);
    }

    @Override
    public String nextSegmentId(String prefix, String bizTag, int width) {
        return sequenceService.nextSegmentId(prefix, bizTag, width);
    }

    @Override
    public List<String> nextSegmentId(String prefix, String bizTag, int width, int count) {
        return sequenceService.nextSegmentId(prefix, bizTag, width, count);
    }

    @Override
    public Long nextRedisId(String bizTag) {
        return sequenceService.nextRedisId(bizTag);
    }

    @Override
    public Long nextRedisId(String bizTag, int width) {
        return sequenceService.nextRedisId(bizTag, width);
    }

    @Override
    public String nextRedisId(String prefix, String bizTag, int width) {
        return sequenceService.nextRedisId(prefix, bizTag, width);
    }

    @Override
    public List<String> nextRedisIdList(String bizTag, int width, int count) {
        return sequenceService.nextRedisIdList(bizTag,width,count);
    }

    @Override
    public String nextRedisIdWithDate(String prefix, String bizTag, int width) {
        return sequenceService.nextRedisIdWithDate(prefix, bizTag, width);
    }
}
