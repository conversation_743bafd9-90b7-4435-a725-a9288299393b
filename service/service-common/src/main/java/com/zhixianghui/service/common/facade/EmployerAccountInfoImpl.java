package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.EmployerAccountInfoDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.service.common.core.biz.report.EmployerAccountInfoBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 用工企业账户表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmployerAccountInfoImpl implements EmployerAccountInfoFacade {

    private final EmployerAccountInfoBiz biz;

    @Override
    public PageResult<List<EmployerAccountInfo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public PageResult<List<Map<String,String>>> groupByMch(Map<String, Object> paramMap,PageParam pageParam) {
        return biz.groupByMch(paramMap, pageParam);
    }

    @Override
    public List<EmployerAccountInfo> listByEmployerNoAndMainstayNo(String employerNo, String mainstayNo) {
        return biz.listByEmployerNoAndMainstayNo(employerNo,mainstayNo);
    }

    @Override
    public void batchUpdate(List<EmployerAccountInfo> list) {
        biz.batchUpdate(list);
    }

    @Override
    public PageResult<List<EmployerAccountInfoDto>> listCustomPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listCustomPage(paramMap, pageParam);
    }

    @Override
    public EmployerAccountInfo getByEmployerNoAndMainstayNoAndChannelType(String employerNo, String mainstayNo, Integer channelType) {
        return biz.getByEmployerNoAndMainstayNoAndChannelType(employerNo, mainstayNo, channelType);
    }

    @Override
    public List<EmployerAccountInfo> listBy(Map<String, Object> paramMap) {
        return biz.listBy(paramMap);
    }

    @Override
    public EmployerAccountInfo getByEmployerNoAndMainstayNoAndChannelNo(String mchNo, String mainstayNo, String channelNo) {
        return biz.getByEmployerNoAndMainstayNoAndChannelNo(mchNo,mainstayNo,channelNo);
    }

    @Override
    public EmployerAccountInfo getByEmployerNoAndParentMchNoAndChannelNo(String mchNo, String parentMerchantNo, String channelNo) {
        return biz.getByEmployerNoAndParentMchNoAndChannelNo(mchNo, parentMerchantNo, channelNo);
    }

    @Override
    public List<EmployerAccountInfo> getByEmployerNameAndParentMchNoAndChannelNo(String mchName, String parentMerchantNo, String channelNo) {
        return biz.getByEmployerNameAndParentMchNoAndChannelNo(mchName, parentMerchantNo, channelNo);
    }

    @Override
    public void forceDelete(String mchNo) {
        biz.forceDelete(mchNo);
    }

    @Override
    public EmployerAccountInfo getOneBySubMerchantNoAndPayChannelNo(String payeeIdentity, String channelNo) {
        return biz.getOneBySubMerchantNoAndPayChannelNo(payeeIdentity,channelNo);
    }

    @Override
    public String getAlipayCardNo() {
        try {
            biz.getAlipayCardNo();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public void changeAccount(Long id, String employerNo,String updator) {
         biz.changeAccount(id,employerNo,updator);
    }

    @Override
    public EmployerAccountInfo getById(Long id) {
        return biz.getById(id);
    }

}
