package com.zhixianghui.service.common.core.biz.approvalflow;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowHandleType;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.HandleStatus;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.base.BaseFlow;
import com.zhixianghui.service.common.core.biz.approvalflow.factory.FlowFactory;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDao;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDetailDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description
 * <AUTHOR>
 * @date 2020-08-07 10:05
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApprovalFlowDetailBiz {

    private final CommonBiz commonBiz;
    private final ApprovalFlowDetailDao approvalFlowDetailDao;
    private final ApprovalFlowDao approvalFlowDao;
    private final FlowFactory flowFactory;
    private final ApprovalDisAgreeOtherHandlerBiz approvalDisAgreeOtherHandlerBiz;

    public List<ApprovalFlowDetail> listByApprovalFlowIdAndHandlerId(Long approvalFlowId, Long handlerId, boolean isAdmin) {
        commonBiz.authApproval(approvalFlowId,handlerId,isAdmin);
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("approvalFlowId", approvalFlowId);
        return approvalFlowDetailDao.listBy(paramMap,"CREATE_TIME DESC, UPDATE_TIME DESC,ID DESC");
    }

    @Transactional(rollbackFor = Exception.class)
    public ApprovalInfoVo agreeApprovalDetail(Long approvalDetailId, Long handlerId,
                                              Integer platformSource, String flowName,
                                              String approvalOpinion, boolean isAdmin) {
        //流程已完成，不允许再次操作
        ApprovalFlow approvalFlow = commonBiz.getByDetailId(approvalDetailId);
        if(approvalFlow.getStatus().equals(FlowStatus.FINISHED.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("审批已处理完毕，请不要重复操作 审批流id:" + approvalFlow.getId());
        }
        log.info("审批流程：platformSource:{},handlerId:{},approvalDetailId:{} 同意操作",platformSource,handlerId,approvalDetailId);
        //更新状态
        ApprovalFlowDetail approvalDetail = updateStatus(approvalDetailId,handlerId,platformSource, HandleStatus.AGREE.getValue(),approvalOpinion,isAdmin);
        Integer handleType = approvalDetail.getHandlerType();
        //判断主流程是否到下一步
        if(handleType.equals(FlowHandleType.ORSIGN.getValue()) || handleType.equals(FlowHandleType.SUBMIT.getValue())){
            //或签
            //同级其他处理节点 设为历史记录 已经不需要处理了
            List<ApprovalFlowDetail> details = commonBiz.listByFlowIdAndStepNumAndIsHistory(approvalDetail.getApprovalFlowId(), approvalDetail.getStepNum(),false);
            details.forEach(detail ->detail.setIsHistory(true));
            approvalFlowDetailDao.update(details);
            return nextStep(approvalDetail,flowName);
        }else if(handleType.equals(FlowHandleType.COUNTERSIGN.getValue())){
            //会签
            List<ApprovalFlowDetail> approvalFlowDetails = commonBiz.listByFlowIdAndStepNumAndIsHistory(approvalDetail.getApprovalFlowId(),approvalDetail.getStepNum(),false);
            //还有未审核的
            if(approvalFlowDetails != null && approvalFlowDetails.size() > 0){
                return ApprovalInfoVo.builder().id(approvalFlow.getId()).isLast(false).build();
            }else{
                return nextStep(approvalDetail,flowName);
            }
        }
        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("节点类型有误,节点id" + approvalDetail.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void disAgreeApprovalDetail(Long approvalDetailId, Long handlerId,
                                       Integer platformSource, String flowName,
                                       String approvalOpinion, boolean isAdmin) {
        //流程已完成，不允许再次操作
        ApprovalFlow approvalFlow = commonBiz.getByDetailId(approvalDetailId);
        if(ObjectUtils.isEmpty(approvalFlow)){
            log.error("节点找不到主流程 approvalDetailId：{}",approvalDetailId);
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此节点的审批流程");
        }

        if(approvalFlow.getStatus().equals(FlowStatus.FINISHED.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("审批已处理完毕，请不要重复操作 审批流id:" + approvalFlow.getId());
        }

        log.info("审批流程：platformSource:{},handlerId:{},approvalDetailId:{} 驳回操作",platformSource,handlerId,approvalDetailId);

        //更新状态为不同意
        ApprovalFlowDetail approvalDetail = updateStatus(approvalDetailId,handlerId,platformSource,HandleStatus.DISAGREE.getValue(),approvalOpinion,isAdmin);

        //同级其他处理节点 设为历史记录 已经不需要处理了
        List<ApprovalFlowDetail> details = commonBiz.listByFlowIdAndStepNumAndIsHistory(approvalDetail.getApprovalFlowId(), approvalDetail.getStepNum(),false);
        details.forEach(detail ->detail.setIsHistory(true));
        approvalFlowDetailDao.update(details);

        //额外业务需求：
        approvalDisAgreeOtherHandlerBiz.disAgreeOtherHandle(approvalFlow,approvalDetail);

        //驳回到上一步 如果是在第0步或非法步骤进行驳回 走撤回流程
        if(approvalFlow.getStepNum() - 1 < 0){
            commonBiz.cancelApproval(approvalFlow.getId(),handlerId,approvalDetail.getHandlerName(),platformSource,false);//todo
            return;
        }
        approvalFlow.setStepNum(approvalFlow.getStepNum() - 1);
        approvalFlowDao.update(approvalFlow);

        ApprovalFlow newApprovalFlow = approvalFlowDao.getById(approvalFlow.getId());

        //输进定义好的审批流继续处理
        BaseFlow baseFlow = flowFactory.getFlow(flowName);
        baseFlow.startFlow(newApprovalFlow);
    }

    /**
     * 进行流程下一步
     * @param approvalDetail 节点
     */
    private ApprovalInfoVo nextStep(ApprovalFlowDetail approvalDetail, String flowName){
        ApprovalFlow approvalFlow = approvalFlowDao.getById(approvalDetail.getApprovalFlowId());
        if(ObjectUtils.isEmpty(approvalFlow)){
            log.error("节点找不到主流程{}",approvalDetail);
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此节点的审批流程");
        }
        approvalFlow.setStepNum(approvalFlow.getStepNum() + 1);
        approvalFlowDao.update(approvalFlow);

        //由于事务的存在，version并没有更新到approvalFlow对象，从事务中读取最新的对象
        ApprovalFlow newApprovalFlow = approvalFlowDao.getById(approvalFlow.getId());

        //输进定义好的审批流继续处理
        BaseFlow baseFlow = flowFactory.getFlow(flowName);
        return baseFlow.startFlow(newApprovalFlow);
    }

    private ApprovalFlowDetail updateStatus(Long approvalDetailId, Long handlerId, Integer platformSource,Integer status,String approvalOpinion, boolean isAdmin){
        ApprovalFlowDetail approvalDetail = approvalFlowDetailDao.getById(approvalDetailId);
        if(ObjectUtils.isEmpty(approvalDetail)){
            log.error("无此处理节点{}",approvalDetailId);
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此处理节点");
        }

        //admin操作且不是自己的节点插入新节点记录
        if (isAdmin && !approvalDetail.getHandlerId().equals(handlerId)){
            log.info("admin执行操作,执行审批节点号:{},流水号:{}",approvalDetailId,approvalDetail.getApprovalFlowId());
            ApprovalFlowDetail adminApprovalFlowDetail = new ApprovalFlowDetail();
            adminApprovalFlowDetail.setCreateTime(new Date());
            adminApprovalFlowDetail.setUpdateTime(new Date());
            adminApprovalFlowDetail.setApprovalFlowId(approvalDetail.getApprovalFlowId());
            adminApprovalFlowDetail.setHandlerId(handlerId);
            adminApprovalFlowDetail.setHandlerName("admin");
            adminApprovalFlowDetail.setHandlerType(approvalDetail.getHandlerType());
            adminApprovalFlowDetail.setStepNum(approvalDetail.getStepNum());
            adminApprovalFlowDetail.setStatus(status);
            adminApprovalFlowDetail.setOperatorName("admin");
            adminApprovalFlowDetail.setPlatform(platformSource);
            adminApprovalFlowDetail.setIsHistory(true);
            adminApprovalFlowDetail.setApprovalOpinion(approvalOpinion);
            approvalFlowDetailDao.insert(adminApprovalFlowDetail);
            return adminApprovalFlowDetail;
        }

        if(!approvalDetail.getStatus().equals(HandleStatus.PENDING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("节点非待审批节点，不允许操作" + approvalDetail.getId());
        }

        if(!approvalDetail.getHandlerId().equals(handlerId)
                || !approvalDetail.getPlatform().equals(platformSource)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(Joiner.on("").join("节点id:",approvalDetailId," 无权审批,节点审批人为：",approvalDetail.getHandlerName()));
        }
        approvalDetail.setApprovalOpinion(approvalOpinion);
        approvalDetail.setStatus(status);
        approvalDetail.setIsHistory(true);
        approvalDetail.setUpdateTime(new Date());
        approvalFlowDetailDao.update(approvalDetail);

        return approvalDetail;
    }

    public List<ApprovalFlowDetail> listBy(Map<String, Object> paramMap, String sortColumns) {
        return approvalFlowDetailDao.listBy(paramMap,sortColumns);
    }
}
