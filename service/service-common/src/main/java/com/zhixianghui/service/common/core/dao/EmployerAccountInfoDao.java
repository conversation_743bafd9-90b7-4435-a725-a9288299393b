package com.zhixianghui.service.common.core.dao;

import com.google.common.collect.Maps;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.EmployerAccountInfoDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用工企业账户表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Repository
public class EmployerAccountInfoDao extends MyBatisDao<EmployerAccountInfo, Long> {

    public void updateStatusByMainstayNoAndEmployerNo(String mainstayNo, String employerNo, Integer status, String operator) {
        if (StringUtils.isBlank(mainstayNo) || StringUtils.isBlank(employerNo) || status == null){
            throw new IllegalArgumentException("条件缺失,mainstayNo:" + mainstayNo + ",employerNo:" + employerNo + ",status:" + status);
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("status",status);
        paramMap.put("updateOperator",operator);
        this.getSqlSession().update(fillSqlId("updateStatusByMainstayNoAndEmployerNo"), paramMap);
    }

    public void deleteByMainstayNoAndEmployerNo(String mainstayNo, String employerNo) {
        if (StringUtils.isBlank(mainstayNo) || StringUtils.isBlank(employerNo)){
            throw new IllegalArgumentException("条件缺失,mainstayNo:" + mainstayNo + ",employerNo:" + employerNo);
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("employerNo",employerNo);
        this.getSqlSession().delete(fillSqlId("deleteByMainstayNoAndEmployerNo"), paramMap);
    }

    public void updateStatusByPayChannelNo(String payChannelNo, Integer status, String operator) {
        if (StringUtils.isBlank(payChannelNo) || status == null){
            throw new IllegalArgumentException("条件缺失, payChannelNo: " + payChannelNo + ",status:" + status);
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("payChannelNo",payChannelNo);
        paramMap.put("status",status);
        paramMap.put("updateOperator",operator);
        this.getSqlSession().update(fillSqlId("updateStatusByPayChannelNo"), paramMap);
    }

    public void updateStatusByMainstayNoAndPayChannelNo(String mainstayNo, String payChannelNo, Integer status, String operator) {
        if (StringUtils.isBlank(mainstayNo) || StringUtils.isBlank(payChannelNo) || status == null){
            throw new IllegalArgumentException("条件缺失,mainstayNo:" + mainstayNo + ",payChannelNo:" + payChannelNo + ",status:" + status);
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("payChannelNo",payChannelNo);
        paramMap.put("status",status);
        paramMap.put("updateOperator",operator);
        this.getSqlSession().update(fillSqlId("updateStatusByMainstayNoAndPayChannelNo"), paramMap);
    }

    public void clearInfoByPayChannelNo(String payChannelNo, Integer status){
        if (StringUtils.isBlank(payChannelNo) || status == null){
            throw new IllegalArgumentException("条件缺失, payChannelNo:" + payChannelNo + ",status:" + status);
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("payChannelNo",payChannelNo);
        paramMap.put("status",status);
        this.getSqlSession().update(fillSqlId("clearInfoByPayChannelNo"), paramMap);

    }

    public PageResult<List<EmployerAccountInfoDto>>
    listCustomPage(Map<String, Object> paramMap, PageParam pageParam) {
        long totalRecord = 0L;
        List<EmployerAccountInfoDto> dataList;

        if (pageParam.isNeedTotalRecord()) {
            totalRecord = this.countBy(paramMap)/3;
            if (totalRecord <= 0) {
                //如果总记录数为0，就直接返回了
                dataList = new ArrayList<>();
                return PageResult.newInstance(dataList, pageParam, totalRecord);
            }
        }

        if (isNotEmpty(pageParam.getSortColumns())) {
            if (paramMap == null) {
                paramMap = new HashMap<>(1);
            }
            paramMap.put(SORT_COLUMNS, this.filterSortColumns(pageParam.getSortColumns()));
        }
        dataList = this.getSqlSession().selectList(fillSqlId("listCustomPage"),paramMap,
                new RowBounds(getOffset(pageParam) * 3, pageParam.getPageSize() * 3));
        if (!pageParam.isNeedTotalRecord()) {
            totalRecord = dataList.size();
        }
        return PageResult.newInstance(dataList, pageParam, totalRecord);
    }

    public void clearInfoByMainstayNo(String mainstayNo, Integer status) {
        if (StringUtils.isBlank(mainstayNo) || status == null){
            throw new IllegalArgumentException("条件缺失, mainstayNo:" + mainstayNo + ",status:" + status);
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("status",status);
        this.getSqlSession().update(fillSqlId("clearInfoByMainstayNo"), paramMap);
    }
}
