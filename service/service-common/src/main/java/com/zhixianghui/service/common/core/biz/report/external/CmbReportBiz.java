package com.zhixianghui.service.common.core.biz.report.external;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.common.dto.ReportEditDto;
import com.zhixianghui.facade.common.entity.report.*;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.CmbAccountQueryDTO;
import com.zhixianghui.facade.trade.dto.CmbCreateAccountDTO;
import com.zhixianghui.facade.trade.entity.CmbMerchantBalance;
import com.zhixianghui.facade.trade.service.CmbMerchantBalanceFacade;
import com.zhixianghui.service.common.core.biz.report.ReportAccountHistoryBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CmbReportBiz extends AbstractReportBiz {

    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private CmbFacade cmbFacade;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private CmbMerchantBalanceFacade cmbMerchantBalanceFacade;

    private final RedisClient redisClient;

    private final ReportAccountHistoryBiz reportAccountHistoryBiz;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reportDetail(ReportEntity reportEntity) throws Exception {
        //验证商户参数是否正确，并返回签约号等数据
        Map<String, Object> map = validMerchantNo(reportEntity);
        JSONObject mailBody = null;
        String extAgreementNo = (String) map.get("extAgreementNo");

        //判断邮件信息是否为空
        Merchant merchant = (Merchant) map.get("merchant");
        if (StringUtil.isEmpty(merchant.getContactEmail())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("商户联系人邮件信息为空，无法发送签约邮件");
        }


        if (StringUtil.isEmpty(extAgreementNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数有误");
        }

        try {
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(), reportEntity.getPayChannelNo());
            if (mainstayChannelRelation == null || StringUtils.isBlank(mainstayChannelRelation.getChannelMchNo())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商【" + reportEntity.getMainstayNo() + "】账户配置错误");
            }
            //反查报备信息，判断是否已经申请报备过
            /*JSONObject resBody = null;
            try {
                resBody = cmbFacade.queryAccountBook(mainstayChannelRelation.getChannelMchNo(), reportEntity.getEmployerNo().replace("M", ""));
            } catch (BizException bizException) {
                log.error(bizException.getErrMsg());
                if (!StringUtils.contains(bizException.getErrMsg(),"该记账子单元记录不存在或非生效状态")) {
                    throw bizException;
                }
            }*/
            CmbAccountQueryDTO cmbAccountQueryDTO = new CmbAccountQueryDTO();
            cmbAccountQueryDTO.setMchNo(reportEntity.getEmployerNo());
            cmbAccountQueryDTO.setMainstayNo(reportEntity.getMainstayNo());
            cmbAccountQueryDTO.setMerchantType(reportEntity.getMerchantType());
            CmbMerchantBalance cmbMerchantBalance = cmbMerchantBalanceFacade.getCmbMerchantBalance(cmbAccountQueryDTO);
            if (ObjectUtil.isNotEmpty(cmbMerchantBalance)) {
                //说明已经报备过，执行报备签约后续的操作：回填数据，创建（查询）记账本等
                reportRepeat(cmbMerchantBalance, mainstayChannelRelation, reportEntity);
            } else {
                //4. 获取供应商服账户
                //String accountBookNo = reportEntity.getEmployerNo().replace("M", "");
                //cmbFacade.createAccountBook(mainstayChannelRelation.getChannelMchNo(), accountBookNo , reportEntity.getEmployerName());
                CmbCreateAccountDTO cmbCreateAccount = new CmbCreateAccountDTO();
                cmbCreateAccount.setMchNo(reportEntity.getEmployerNo());
                cmbCreateAccount.setMchName(reportEntity.getEmployerName());
                cmbCreateAccount.setMainstayNo(reportEntity.getMainstayNo());
                cmbCreateAccount.setMainstayName(reportEntity.getMainstayName());
                cmbCreateAccount.setMerchantType(reportEntity.getMerchantType());
                cmbMerchantBalanceFacade.createMerchantBalance(cmbCreateAccount);
                afterReport(reportEntity, mainstayChannelRelation);
            }
        } catch (BizException ex) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(ex.getErrMsg());
        }
    }

    @Override
    protected boolean modify(ReportEditDto reportDto, EmployerAccountInfo employerAccountInfo) {
        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持修改报备信息");
    }

    /**
     * 重复报备处理
     *
     * @param reportEntity
     */
    private void reportRepeat(CmbMerchantBalance cmbMerchantBalance, MainstayChannelRelation mainstayChannelRelation, ReportEntity reportEntity) {
        String channelType = reportEntity.getChannelType() == null ? "" : reportEntity.getChannelType().toString();
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        reportChannelRecord.setSerialNo(ChannelNoEnum.CMB.name() + reportEntity.getEmployerNo() + reportEntity.getMainstayNo() + channelType);
        reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecordBiz.update(reportChannelRecord);

        EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(reportEntity.getEmployerNo(), reportEntity.getMainstayNo(), reportEntity.getChannelType());
        employerAccountInfo.setParentMerchantNo(mainstayChannelRelation.getChannelMchNo());
        employerAccountInfo.setSubMerchantNo(cmbMerchantBalance.getMchNo().replace("M", ""));
        employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
        employerAccountInfo.setPayChannelNo(reportEntity.getPayChannelNo());
        employerAccountInfo.setPayChannelName(reportEntity.getPayChannelName());
        employerAccountInfoBiz.update(employerAccountInfo);

    }

    /**
     * 报备后操作
     *
     * @param reportEntity
     */
    private void afterReport(ReportEntity reportEntity, MainstayChannelRelation mainstayChannelRelation) {
        String channelType = reportEntity.getChannelType() == null ? "" : reportEntity.getChannelType().toString();
        //签约邮件发送成功，填写报备记录
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        //加上通道类型唯一
        reportChannelRecord.setSerialNo(ChannelNoEnum.CMB.name() + reportEntity.getEmployerNo() + reportEntity.getMainstayNo() + channelType);
        reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecordBiz.update(reportChannelRecord);

        EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(reportEntity.getEmployerNo(), reportEntity.getMainstayNo(), reportEntity.getChannelType());
        employerAccountInfo.setParentMerchantNo(mainstayChannelRelation.getChannelMchNo());
        employerAccountInfo.setSubMerchantNo(reportEntity.getEmployerNo().replace("M", ""));
        employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
        employerAccountInfo.setPayChannelNo(reportEntity.getPayChannelNo());
        employerAccountInfo.setPayChannelName(reportEntity.getPayChannelName());
        employerAccountInfoBiz.update(employerAccountInfo);

        ReportAccountHistory reportAccountHistory = new ReportAccountHistory();
        reportAccountHistory.setEmployerNo(employerAccountInfo.getEmployerNo());
        reportAccountHistory.setMainstayNo(employerAccountInfo.getMainstayNo());
        reportAccountHistory.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        reportAccountHistory.setEmployerName(employerAccountInfo.getMchName());
        reportAccountHistory.setPayChannelNo(ChannelNoEnum.CMB.name());
        reportAccountHistory.setPayChannelName(ChannelNoEnum.CMB.getDesc());
        reportAccountHistory.setPayChannelType(ChannelTypeEnum.BANK.getValue());
        reportAccountHistory.setChannelMerchantNo(employerAccountInfo.getSubMerchantNo());
        reportAccountHistory.setAgreementNo("");
        reportAccountHistory.setAlipayUserId("");
        reportAccountHistory.setCreateTime(new Date());
        reportAccountHistory.setIsShow(YesNoCodeEnum.YES.getValue());
        reportAccountHistory.setTitle(ChannelNoEnum.CMB.getDesc());
        reportAccountHistoryBiz.create(reportAccountHistory);
    }

    /**
     * 数据校验并返回商户签约号
     * @param reportEntity
     * @return
     */
    private Map<String,Object> validMerchantNo(ReportEntity reportEntity) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer extAgreementNo = null;
        //报备类型为供应商类型
        if (reportEntity.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()){
            Merchant merchant = merchantQueryFacade.getByMchNo(reportEntity.getMainstayNo());
            if (merchant == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到供应商编号为[" + reportEntity.getMainstayNo() + "]的供应商");
            }
            if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商未激活,供应商编号为[" + reportEntity.getMainstayNo() + "]的供应商");
            }
            extAgreementNo = new StringBuffer(merchant.getMchNo());
            map.put("merchant",merchant);
        }

        //报备类型为商户类型报备
        if (reportEntity.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            //由于前面跳过此校验，因此需要重新校验
            //校验供应商-通道
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(), reportEntity.getPayChannelNo());
            if(mainstayChannelRelation == null || mainstayChannelRelation.getStatus().equals(OpenOffEnum.OFF.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商-通道(供应商账户)不存在或者状态未开启 请检查！MainstayNo:" + reportEntity.getMainstayNo() + ", PayChannelNo:"+ reportEntity.getPayChannelNo());
            }
            //判断商户是否存在
            Merchant merchant = merchantQueryFacade.getByMchNo(reportEntity.getEmployerNo());
            if (merchant == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到商户编号为[" + reportEntity.getEmployerNo() + "]的商户");
            }
            extAgreementNo = new StringBuffer(merchant.getMchNo());
            extAgreementNo.append(reportEntity.getMainstayNo());
            map.put("merchant",merchant);
            map.put("outUid", mainstayChannelRelation.getAlipayUserId());
        }
        map.put("extAgreementNo",extAgreementNo.toString());

        return map;
    }
}
