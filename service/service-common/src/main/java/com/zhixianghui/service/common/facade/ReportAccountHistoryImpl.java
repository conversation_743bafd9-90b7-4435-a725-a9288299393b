package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.report.ReportAccountHistory;
import com.zhixianghui.facade.common.service.ReportAccountHistoryFacade;
import com.zhixianghui.facade.common.vo.ReportAccountHistoryVo;
import com.zhixianghui.service.common.core.biz.report.ReportAccountHistoryBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-07-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReportAccountHistoryImpl implements ReportAccountHistoryFacade {

    private final ReportAccountHistoryBiz reportAccountHistoryBiz;

    @Override
    public List<ReportAccountHistoryVo> getMerchantAccount(String employerNo, String mainstayNo) {
        return reportAccountHistoryBiz.getMerchantAccount(employerNo,mainstayNo);
    }

    @Override
    public List<ReportAccountHistory> getByChannelType(String employerNo, String mainstayNo, Long channelType) {
        return reportAccountHistoryBiz.getByChannelType(employerNo,mainstayNo,channelType);
    }

    @Override
    public void changeStatus(Long id, Integer isShow) {
        reportAccountHistoryBiz.changeStatus(id,isShow);
    }

    @Override
    public void changeTitle(Long id, String title) {
        reportAccountHistoryBiz.changeTitle(id,title);
    }

    @Override
    public ReportAccountHistory getByChannelMerchantNoAndPayChannelNo(String channelMerchantNo, String payChannelNo) {
        return reportAccountHistoryBiz.getByChannelMerchantNoAndPayChannelNo(channelMerchantNo,payChannelNo);
    }
}
