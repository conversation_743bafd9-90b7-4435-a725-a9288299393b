package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.PushManagerDto;
import com.zhixianghui.facade.common.entity.push.PushManager;
import com.zhixianghui.facade.common.service.PushManagerFacade;
import com.zhixianghui.facade.common.vo.PushManagerVo;
import com.zhixianghui.service.common.core.biz.push.PushManagerBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-02-21
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PushManagerImpl implements PushManagerFacade {

    private final PushManagerBiz biz;

    @Override
    public void add(PushManagerDto pushManagerDto,String createBy) {
        biz.add(pushManagerDto,createBy);
    }

    @Override
    public void deleteById(Long id) {
        biz.deleteById(id);
    }

    @Override
    public void update(PushManager pushManager, String realName) {
        biz.update(pushManager,realName);
    }

    @Override
    public PageResult<List<PushManagerVo>> selectPage(PushManager pushManager, int pageSize, int pageCurrent) {
        return biz.selectPage(pushManager,pageSize,pageCurrent);
    }

    @Override
    public List<PushManager> listBy(Map<String, Object> paramMap) {
        return biz.listBy(paramMap);
    }
}
