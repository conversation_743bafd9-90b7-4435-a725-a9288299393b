package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.facade.common.entity.config.WorkCategory;
import com.zhixianghui.service.common.core.dao.WorkCategoryDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工作类目
 *
 * <AUTHOR>
 * @date 2020-08-10
 */
@Service
public class WorkCategoryBiz {
    @Autowired
    private WorkCategoryDao workCategoryDao;

    public void insert(WorkCategory workCategory) {
        workCategoryDao.insert(workCategory);
    }

    public void update(WorkCategory workCategory) {
        workCategoryDao.update(workCategory);
    }

    public void delete(long id) {
        workCategoryDao.deleteById(id);
    }

    public WorkCategory getByCategoryCode(String categoryCode) {
        return workCategoryDao.getByCategoryCode(categoryCode);
    }

    public List<WorkCategory> listSubCategory(Long parentId) {
        if(Objects.isNull(parentId)){
           return null;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", parentId);
        return workCategoryDao.listBy(paramMap);
    }

    public List<WorkCategory> listAll() {
        return workCategoryDao.listAll();
    }
}
