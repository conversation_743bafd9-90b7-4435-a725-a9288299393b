package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.config.BankOrganization;
import com.zhixianghui.facade.common.service.BankOrganizationFacade;
import com.zhixianghui.service.common.core.biz.config.BankOrganizationBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-07-09
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BankOrganizationImpl implements BankOrganizationFacade {

    private final BankOrganizationBiz bankOrganizationbiz;

    @Override
    public BankOrganization getByBankCode(String bankCode) {
        return bankOrganizationbiz.getByBankCode(bankCode);
    }
}
