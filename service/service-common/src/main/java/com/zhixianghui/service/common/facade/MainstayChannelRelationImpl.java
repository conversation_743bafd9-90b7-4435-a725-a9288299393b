package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.KeyPairRecordDto;
import com.zhixianghui.facade.common.dto.MainstayChannelRelationDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.service.common.core.biz.report.MainstayChannelRelationBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 供应商账户表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MainstayChannelRelationImpl implements MainstayChannelRelationFacade {

    private final MainstayChannelRelationBiz biz;

    @Override
    public void create(MainstayChannelRelation mainstayChannelRelation) {
        biz.create(mainstayChannelRelation);
    }

    @Override
    public void batchCreate(List<MainstayChannelRelation> mainstayChannelRelationList,List<KeyPairRecordDto> keyPairRecordList) {
        biz.batchCreate(mainstayChannelRelationList,keyPairRecordList);
    }

    @Override
    public boolean isExist(String mainstayNo) {
        return biz.isExist(mainstayNo);
    }

    @Override
    public PageResult<List<MainstayChannelRelationDto>> listCustomPage(Map<String, Object> paramMap, PageParam pageParam){
        return biz.listCustomPage(paramMap, pageParam);
    }

    @Override
    public void batchUpdate(List<MainstayChannelRelation> list, List<KeyPairRecordDto> keyPairRecordList) {
        biz.batchUpdate(list,keyPairRecordList);
    }

    @Override
    public List<MainstayChannelRelation> listBy(Map<String, Object> paramMap) {
        return biz.listBy(paramMap);
    }

    @Override
    public List<MainstayChannelRelation> listByMainstayNo(String mainstayNo) {
        return biz.listByMainstayNo(mainstayNo);
    }

    @Override
    public MainstayChannelRelation getByChannelMchNoAndPayChannelNo(String channelMchNo, String payChannelNo) {
        return biz.getByChannelMchNoAndPayChannelNo(channelMchNo,payChannelNo);
    }

    @Override
    public void deleteByMainstayNo(String mainstayNo) {
        biz.deleteByMainstayNo(mainstayNo);
    }

    @Override
    public void update(MainstayChannelRelation mainstayChannelRelation) {
        biz.update(mainstayChannelRelation);
    }

    @Override
    public MainstayChannelRelation getByMainstayNoAndPayChannelNo(String mainstayNo, String name) {
        return biz.getByMainstayNoAndPayChannelNo(mainstayNo,name);
    }

    @Override
    public List<Map<String, Object>> getAlipayMainstays() {
        return biz.getAlipayMainstays();
    }

    @Override
    public List<Map<String, Object>> getCmbainstays() {
        return biz.getCmbainstays();
    }
}
