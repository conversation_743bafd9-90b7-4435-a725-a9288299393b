package com.zhixianghui.service.common.core.biz.report;

import cn.hutool.core.date.DateUtil;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.common.PublicStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.service.KeyPairRecordFacade;
import com.zhixianghui.facade.common.dto.KeyPairRecordDto;
import com.zhixianghui.facade.common.dto.MainstayChannelRelationDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.trade.dto.AcMerchantBalanceAddDto;
import com.zhixianghui.facade.trade.dto.CmbCreateAccountDTO;
import com.zhixianghui.facade.trade.service.AcMerchantBalanceFacade;
import com.zhixianghui.facade.trade.service.CmbMerchantBalanceFacade;
import com.zhixianghui.service.common.core.dao.EmployerAccountInfoDao;
import com.zhixianghui.service.common.core.dao.MainstayChannelRelationDao;
import com.zhixianghui.service.common.core.dao.PayChannelDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* 供应商账户表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-09-27
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MainstayChannelRelationBiz {
    @Reference
    private AcMerchantBalanceFacade acMerchantBalanceFacade;
    @Reference
    private CmbMerchantBalanceFacade cmbMerchantBalanceFacade;
    private final MainstayChannelRelationDao mainstayChannelRelationDao;
    private final EmployerAccountInfoDao employerAccountInfoDao;
    private final PayChannelDao payChannelDao;

    @Reference
    private KeyPairRecordFacade keyPairRecordFacade;

    public void create(MainstayChannelRelation mainstayChannelRelation) {
        mainstayChannelRelationDao.insert(mainstayChannelRelation);
    }

    public boolean isExist(String mainstayNo) {
        //todo
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(List<MainstayChannelRelation> mainstayChannelRelationList,List<KeyPairRecordDto> keyPairRecordList) {
        try {
            mainstayChannelRelationDao.insert(mainstayChannelRelationList);
            List <KeyPairRecord> recordList = keyPairRecordList.stream().map(
                    info ->{
                        KeyPairRecord keyPairRecord = new KeyPairRecord();
                        BeanUtils.copyProperties(info,keyPairRecord);
                        keyPairRecord.setChannelPublicKeyEncrypt(info.getChannelPublicKey());
                        keyPairRecord.setMchPublicKeyEncrypt(info.getMchPublicKey());
                        keyPairRecord.setMchPrivateKeyEncrypt(info.getMchPrivateKey());
                        keyPairRecord.setChannelLoginUserEncrypt(info.getChannelLoginUser());
                        return keyPairRecord;
                    }
            ).collect(Collectors.toList());

            // 创建本地账本
            for(MainstayChannelRelation relation : mainstayChannelRelationList){
                // 如果是君享汇和汇聚支付的分账，则需要创建本地账本
                if((relation.getPayChannelNo().equals(ChannelNoEnum.JOINPAY.name()) || relation.getPayChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name()))
                        && relation.getStatus().equals(PublicStatusEnum.ACTIVE.getValue())){
                    AcMerchantBalanceAddDto dto = new AcMerchantBalanceAddDto();
                    dto.setMainstayNo(relation.getMainstayNo());
                    dto.setMainstayName(relation.getMainstayName());
                    dto.setPayChannelNo(relation.getPayChannelNo());
                    dto.setPayChannelName(ChannelNoEnum.getEnum(relation.getPayChannelNo()).getDesc());
                    dto.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
                    acMerchantBalanceFacade.createMerchantBalance(dto);
                }else if (relation.getPayChannelNo().equals(ChannelNoEnum.CMB.name()) &&
                        relation.getStatus().equals(PublicStatusEnum.ACTIVE.getValue())) {
                    log.info("供应商[{}]开通招商银行下发银行卡通道，执行创建本地账户流程。", relation.getMainstayNo());
                    CmbCreateAccountDTO cmbCreateAccount = new CmbCreateAccountDTO();
                    cmbCreateAccount.setMainstayNo(relation.getMainstayNo());
                    cmbCreateAccount.setMainstayName(relation.getMainstayName());
                    cmbCreateAccount.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
                    cmbMerchantBalanceFacade.createMerchantBalance(cmbCreateAccount);
                }
            }
            keyPairRecordFacade.updateOrInsertIfNotExist(recordList);
        }catch (DuplicateKeyException e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商支付账户已存在，不要重复创建");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(List<MainstayChannelRelation> list, List<KeyPairRecordDto> keyPairRecordList) {
        //开启时候要检查支付通道是否启用 web端已校验
        mainstayChannelRelationDao.update(list);
        //关闭供应商和支付通道关系，其下用工企业也要禁用
        for(MainstayChannelRelation relation:list){
            if(relation.getStatus().equals(OpenOffEnum.OFF.getValue())){
                employerAccountInfoDao.updateStatusByMainstayNoAndPayChannelNo(relation.getMainstayNo(), relation.getPayChannelNo(), relation.getStatus(),relation.getUpdateOperator());
            }
            if (relation.getPayChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name()) &&
                    relation.getStatus().equals(PublicStatusEnum.ACTIVE.getValue())) {
                log.info("供应商[{}]开通君享汇下发银行卡通道，执行创建本地账户流程。", relation.getMainstayNo());
                // 如果开通君享汇产品，则需创建本地账户
                AcMerchantBalanceAddDto dto = new AcMerchantBalanceAddDto();
                dto.setMainstayNo(relation.getMainstayNo());
                dto.setMainstayName(relation.getMainstayName());
                dto.setPayChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
                dto.setPayChannelName(ChannelNoEnum.JOINPAY_JXH.getDesc());
                dto.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
                acMerchantBalanceFacade.createMerchantBalance(dto);
                log.info("供应商[{}]创建君享汇本地账户成功。", relation.getMainstayNo());
            } else if (relation.getPayChannelNo().equals(ChannelNoEnum.JOINPAY.name()) &&
                    relation.getStatus().equals(PublicStatusEnum.ACTIVE.getValue())) {
                log.info("供应商[{}]开通汇聚支付下发银行卡通道，执行创建本地账户流程。", relation.getMainstayNo());
                // 如果开通君享汇产品，则需创建本地账户
                AcMerchantBalanceAddDto dto = new AcMerchantBalanceAddDto();
                dto.setMainstayNo(relation.getMainstayNo());
                dto.setMainstayName(relation.getMainstayName());
                dto.setPayChannelNo(ChannelNoEnum.JOINPAY.name());
                dto.setPayChannelName(ChannelNoEnum.JOINPAY.getDesc());
                dto.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
                acMerchantBalanceFacade.createMerchantBalance(dto);
                log.info("供应商[{}]创建汇聚支付本地账户成功。", relation.getMainstayNo());
            }else if (relation.getPayChannelNo().equals(ChannelNoEnum.CMB.name()) &&
                    relation.getStatus().equals(PublicStatusEnum.ACTIVE.getValue())) {
                log.info("供应商[{}]开通招商银行下发银行卡通道，执行创建本地账户流程。", relation.getMainstayNo());
                CmbCreateAccountDTO cmbCreateAccount = new CmbCreateAccountDTO();
                cmbCreateAccount.setMainstayNo(relation.getMainstayNo());
                cmbCreateAccount.setMainstayName(relation.getMainstayName());
                cmbCreateAccount.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
                cmbMerchantBalanceFacade.createMerchantBalance(cmbCreateAccount);
            }
        }
        List <KeyPairRecord> recordList = keyPairRecordList.stream().map(
                info ->{
                    KeyPairRecord keyPairRecord = new KeyPairRecord();
                    BeanUtils.copyProperties(info,keyPairRecord);
                    keyPairRecord.setChannelPublicKeyEncrypt(info.getChannelPublicKey());
                    keyPairRecord.setMchPublicKeyEncrypt(info.getMchPublicKey());
                    keyPairRecord.setMchPrivateKeyEncrypt(info.getMchPrivateKey());
                    keyPairRecord.setChannelLoginUserEncrypt(info.getChannelLoginUser());
                    return keyPairRecord;
                }
        ).collect(Collectors.toList());
        keyPairRecordFacade.updateOrInsertIfNotExist(recordList);
    }

    public List<MainstayChannelRelation> listBy(Map<String, Object> paramMap) {
        return mainstayChannelRelationDao.listBy(paramMap);
    }

    public List<MainstayChannelRelation> listByMainstayNo(String mainstayNo) {
        return mainstayChannelRelationDao.listBy(Collections.singletonMap("mainstayNo",mainstayNo));
    }

    public PageResult<List<MainstayChannelRelationDto>> listCustomPage(Map<String, Object> paramMap, PageParam pageParam) {
        paramMap.put("openStatus", OpenOffEnum.OPEN.getValue());
        pageParam.setSortColumns("MAINSTAY_NO DESC");
        //查询支付通道数量
        List<PayChannel> payChannel = payChannelDao.listAll();
        return mainstayChannelRelationDao.listCustomPage(paramMap, pageParam,payChannel.size());
    }

    public MainstayChannelRelation getByMainstayNoAndPayChannelNo(String mainstayNo,String payChannelNo){
        Map<String, Object> param = new HashMap<>();
        param.put("mainstayNo", mainstayNo);
        param.put("payChannelNo", payChannelNo);
        return mainstayChannelRelationDao.getOne(param);
    }

    public MainstayChannelRelation getByChannelMchNoAndPayChannelNo(String channelMchNo, String payChannelNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("channelMchNo", channelMchNo);
        param.put("payChannelNo", payChannelNo);
        return mainstayChannelRelationDao.getOne(param);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByMainstayNo(String mainstayNo) {
        //删除供应商与所有支付通道的信息
        mainstayChannelRelationDao.deleteBy(Collections.singletonMap("mainstayNo",mainstayNo));
        //用工账户表关于此供应商的通道信息全部清空
        employerAccountInfoDao.clearInfoByMainstayNo(mainstayNo,OpenOffEnum.OFF.getValue());
    }

    public void update(MainstayChannelRelation mainstayChannelRelation) {
        mainstayChannelRelationDao.update(mainstayChannelRelation);
    }

    public List<Map<String, Object>> getAlipayMainstays() {
        return mainstayChannelRelationDao.getAlipayMainstays();
    }

    public List<Map<String, Object>> getCmbainstays() {
        return mainstayChannelRelationDao.getCmbainstays();
    }
}
