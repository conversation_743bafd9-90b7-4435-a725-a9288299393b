package com.zhixianghui.service.common.core.biz.report;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.message.TplName;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.service.common.core.dao.mapper.EmployerAccountNotifyConfigMapper;
import com.zhixianghui.facade.common.entity.report.EmployerAccountNotifyConfig;

import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class EmployerAccountNotifyConfigBiz extends ServiceImpl<EmployerAccountNotifyConfigMapper, EmployerAccountNotifyConfig> {

    @Autowired
    private EmployerAccountInfoBiz employerAccountInfoBiz;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private MerchantFacade merchantFacade;

    public EmployerAccountNotifyConfig getConfig(String mainstayNo, String employerNo) {

        final EmployerAccountNotifyConfig config = this.getOne(new QueryWrapper<EmployerAccountNotifyConfig>().eq(EmployerAccountNotifyConfig.COL_EMPLOYER_NO, employerNo).eq(EmployerAccountNotifyConfig.COL_MAINSTAY_NO, mainstayNo));

        return config;
    }

    public List<EmployerAccountNotifyConfig> listNotifyConfig(String employerNo, String mainstayNo) {
        if (StringUtils.isNotBlank(mainstayNo)) {
           return this.list(new QueryWrapper<EmployerAccountNotifyConfig>().eq(EmployerAccountNotifyConfig.COL_EMPLOYER_NO, employerNo).eq(EmployerAccountNotifyConfig.COL_MAINSTAY_NO, mainstayNo));
        }else {
            return this.list(new QueryWrapper<EmployerAccountNotifyConfig>().eq(EmployerAccountNotifyConfig.COL_EMPLOYER_NO, employerNo));
        }
    }

    public void doBalanceNotify() {

        Date now = new Date();

        final List<EmployerAccountNotifyConfig> notifyConfigs = this.list(new QueryWrapper<EmployerAccountNotifyConfig>().eq(EmployerAccountNotifyConfig.COL_STATUS, 1)
                .le(EmployerAccountNotifyConfig.COL_NOTIFY_TIME_START, now).gt(EmployerAccountNotifyConfig.COL_NOTIFY_TIME_END, now));

        if (notifyConfigs == null) {
            log.info("没有商户配置余额提醒");
        } else {
            log.info("有{}个商户配置了余额提醒", notifyConfigs.size());
        }


        notifyConfigs.forEach(it->{

            final String employerNo = it.getEmployerNo();
            final String mainstayNo = it.getMainstayNo();

            final List<EmployerAccountInfo> employerAccountInfos = employerAccountInfoBiz.listByEmployerNoAndMainstayNo(employerNo, mainstayNo);
            if (employerAccountInfos != null && !employerAccountInfos.isEmpty()) {
                log.info("查询账户");
                BigDecimal totalAmount = BigDecimal.ZERO;
                Map<String, BigDecimal> balanceDetail = new HashMap<>();

                for (EmployerAccountInfo employerAccountInfo : employerAccountInfos) {

                    if (employerAccountInfo.getStatus() == CommonStatusEnum.ACTIVE.getValue()) {
                        final String channelNo = employerAccountInfo.getPayChannelNo();
                        if (StringUtils.isNotBlank(channelNo)) {

                            AmountQueryDto queryDto = new AmountQueryDto();
                            queryDto.setEmployerNo(employerNo);
                            queryDto.setAgreementNo(employerAccountInfo.getSubAgreementNo());
                            queryDto.setChannelType(employerAccountInfo.getChannelType());
                            queryDto.setMainstayNo(mainstayNo);
                            queryDto.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
                            queryDto.setChannelNo(employerAccountInfo.getPayChannelNo());
                            queryDto.setChannelMchNo(employerAccountInfo.getParentMerchantNo());

                            try {
                                final String amount = accountQueryFacade.getAmount(queryDto);
                                if (StringUtils.isNotBlank(amount)) {
                                    BigDecimal amountDeci = new BigDecimal(amount);
                                    totalAmount = totalAmount.add(amountDeci);

                                    balanceDetail.put(ChannelNoEnum.valueOf(employerAccountInfo.getPayChannelNo()).getDesc(), amountDeci);
                                }
                            } catch (Exception e) {
                                balanceDetail.put(ChannelNoEnum.valueOf(employerAccountInfo.getPayChannelNo()).getDesc(), BigDecimal.ZERO);
                            }

                        }
                    }
                }
                final BigDecimal notifyAmount = it.getNotifyAmount();
                log.info("商户号[{}]系统配置的告警余额[{}],实际余额[{}]", it.getEmployerNo(), notifyAmount, totalAmount);
                if (totalAmount.compareTo(notifyAmount) < 0) {
                    log.info("商户号[{}]金额达到阈值，发送邮件", it.getEmployerNo());
                    //金额达到阈值，发送邮件
                    Map<String, Object> tplParam = new HashMap<>();
                    tplParam.put("merchantName", it.getEmployerName());
                    tplParam.put("notifyAmount", it.getNotifyAmount());

                    String receiveAccts = it.getReceiveAccount();


                    EmailParamDto emailParamDto = new EmailParamDto();
                    emailParamDto.setSubject("【智享汇】账户余额提醒");
                    emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
                    emailParamDto.setHtmlFormat(true);
                    emailParamDto.setTpl(EmailTemplateEnum.BALANCE_NOTIFY.getName());
                    emailParamDto.setTplParam(tplParam);

                    if (StringUtils.isNotBlank(receiveAccts)) {
                        final JSONArray recieveArray = JSONArray.parseArray(receiveAccts);

                        if (recieveArray.size()  == 1) {
                            emailParamDto.setTo(recieveArray.getString(0));
                        }else {
                            emailParamDto.setTo(recieveArray.getString(0));
                            emailParamDto.setCc(recieveArray.subList(1,recieveArray.size()).toArray(new String[]{}));
                        }

                    }
                    log.info("邮件参数:{}", JSONObject.toJSONString(emailParamDto));
                    emailFacade.sendAsync(emailParamDto);
                }
            }

        });


    }
}
