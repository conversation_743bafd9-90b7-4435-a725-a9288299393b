package com.zhixianghui.service.common.core.biz.approvalflow;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowHandleType;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.HandleStatus;
import com.zhixianghui.facade.common.utils.ContrastObjUtils;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.common.core.biz.approvalflow.base.BaseFlow;
import com.zhixianghui.service.common.core.biz.approvalflow.factory.FlowFactory;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDao;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDetailDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description
 * <AUTHOR>
 * @date 2020-08-07 10:05
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApprovalFlowBiz {

    private final ApprovalFlowDao approvalFlowDao;
    private final FlowFactory flowFactory;
    private final CommonBiz commonBiz;

    @Reference
    private NotifyFacade notifyFacade;

    @Transactional(rollbackFor = Exception.class)
    public ApprovalInfoVo createFlow(ApprovalFlow approvalFlow) {
        BaseFlow flow = flowFactory.getFlow(FlowTopicType.getNameByValue(approvalFlow.getFlowTopicType()));
        return flow.startFlow(approvalFlow);
    }

    public ApprovalFlow getById(Long id) {
        return approvalFlowDao.getById(id);
    }

    public PageResult<List<ApprovalFlow>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return approvalFlowDao.listPage(paramMap, pageParam);
    }

    public PageResult<List<ApprovalFlow>> listByHandlerIdAndPlatformSource(Long operatorId, Integer platformSource, Map<String, Object> flowParamMap, PageParam pageParam) {
        Map<String,Object> detailParamMap = Maps.newHashMap();
        detailParamMap.put("handlerId", operatorId);
        detailParamMap.put("platform", platformSource);
        return commonBiz.listFlowPage(detailParamMap,flowParamMap,pageParam);
    }

    public PageResult<List<ApprovalFlow>> listPendingByOperatorIdAndPlatformSource(Long operatorId, Integer platformSource, Map<String, Object> flowParamMap, PageParam pageParam) {
        Map<String,Object> detailParamMap = Maps.newHashMap();
        detailParamMap.put("handlerId", operatorId);
        detailParamMap.put("platform", platformSource);
        detailParamMap.put("status", HandleStatus.PENDING.getValue());
        detailParamMap.put("isHistory", false);
        return commonBiz.listFlowPage(detailParamMap,flowParamMap,pageParam);
    }

    public PageResult<List<ApprovalFlow>> listByInitiatorIdAndPlatformSource(Long initiatorId, Integer platformSource, Map<String, Object> flowParamMap, PageParam pageParam) {
        flowParamMap.put("initiatorId", initiatorId);
        flowParamMap.put("platform", platformSource);
        return listPage(flowParamMap,pageParam);
    }

    public ApprovalFlow getByIdAndHandlerId(Long id, Long handlerId, boolean isAdmin) {
        return commonBiz.authApproval(id, handlerId,isAdmin);
    }

    public void cancelApproval(Long approvalFlowId, Long handlerId, String handlerName, Integer platform, boolean isAdmin) {
        commonBiz.cancelApproval(approvalFlowId,handlerId,handlerName,platform,isAdmin);
    }

    public ApprovalFlow getByDetailId(Long detailId) {
        return commonBiz.getByDetailId(detailId);
    }

    public void updateExtInfo(UpdateExtInfoVo updateExtInfoVo,boolean isAdmin) {
        //校验
        Long approvalFlowId = updateExtInfoVo.getApprovalFlowId();
        ApprovalFlow approvalFlow = approvalFlowDao.getById(approvalFlowId);
        if(ObjectUtils.isEmpty(approvalFlow)){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此审批流程");
        }
        //只有当前节点的最新处理人才可以修改
        Long handlerId = updateExtInfoVo.getHandlerId();
        Integer platform = updateExtInfoVo.getPlatform();
        List<ApprovalFlowDetail> details = commonBiz.listByFlowIdAndStepNumAndIsHistory(approvalFlowId,approvalFlow.getStepNum(),false);
        List<ApprovalFlowDetail> ownDetails = details.stream().filter(
                detail->detail.getHandlerId().equals(handlerId)&&detail.getPlatform().equals(platform)
        ).collect(Collectors.toList());
        if(ownDetails.size() <= 0 && !isAdmin){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("非当前处理人,无法更新信息");
        }

        //更新主流程对象
        String newInfo = updateExtInfoVo.getExtInfo();
        String oldInfo = approvalFlow.getExtInfo();
        approvalFlow.setExtInfo(newInfo);

        //操作流程记录对象
        String handlerName = updateExtInfoVo.getHandlerName();
        ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
        approvalFlowDetail.setCreateTime(new Date());
        approvalFlowDetail.setUpdateTime(new Date());
        approvalFlowDetail.setApprovalFlowId(approvalFlow.getId());
        approvalFlowDetail.setHandlerId(handlerId);
        approvalFlowDetail.setHandlerName(handlerName);
        approvalFlowDetail.setHandlerType(FlowHandleType.EDIT.getValue());
        approvalFlowDetail.setStepNum(approvalFlow.getStepNum());
        approvalFlowDetail.setStatus(HandleStatus.AGREE.getValue());
        approvalFlowDetail.setOperatorName(handlerName);
        //更改前信息记录
        String diffInfo = ContrastObjUtils.compareJsonObject(oldInfo,newInfo);
        approvalFlowDetail.getJsonEntity().setEditDiffInfo(diffInfo);
        approvalFlowDetail.setPlatform(platform);
        approvalFlowDetail.setIsHistory(true);

        //两表事务
        commonBiz.logAndUpdateExtInfo(approvalFlow,approvalFlowDetail);
        if(ownDetails.size() <= 0){
            //能到这里说明是admin且不是admin本人操作 可以结束
            log.info("admin编辑信息 流程号:{}",approvalFlow.getId());
            return;
        }
        //避免长事务 异步自动同意进入下一步
        ApprovalFlowDetail agreeDetail = ownDetails.get(0);
        //只有发起人在发起的那一步编辑 才自动提交
        if(agreeDetail.getStepNum().equals(0)){
            Map<String,Object> infoMap = Maps.newHashMap();
            infoMap.put("approvalDetailId", agreeDetail.getId());
            infoMap.put("handlerId", agreeDetail.getHandlerId());
            infoMap.put("platformSource", agreeDetail.getPlatform());
            infoMap.put("flowName", FlowTopicType.getNameByValue(approvalFlow.getFlowTopicType()));
            infoMap.put("approvalOpinion", "");
            notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC,NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_APPROVAL_AUTO_AGREE_ASYNC,JSON.toJSONString(infoMap));
        }
    }

    public ApprovalFlow getOne(Map<String, Object> paramMap) {
        return approvalFlowDao.getOne(paramMap);
    }


    public void updateExtInfoForAgent(UpdateExtInfoVo updateExtInfoVo) {
        //校验
        Long approvalFlowId = updateExtInfoVo.getApprovalFlowId();
        ApprovalFlow approvalFlow = approvalFlowDao.getById(approvalFlowId);
        if(ObjectUtils.isEmpty(approvalFlow)){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此审批流程");
        }
        //更新主流程对象
        String newInfo = updateExtInfoVo.getExtInfo();
        String oldInfo = approvalFlow.getExtInfo();
        approvalFlow.setExtInfo(newInfo);
        //操作流程记录对象
        Long handlerId = updateExtInfoVo.getHandlerId();
        Integer platform = updateExtInfoVo.getPlatform();
        String handlerName = updateExtInfoVo.getHandlerName();
        ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
        approvalFlowDetail.setCreateTime(new Date());
        approvalFlowDetail.setUpdateTime(new Date());
        approvalFlowDetail.setApprovalFlowId(approvalFlow.getId());
        approvalFlowDetail.setHandlerId(handlerId);
        approvalFlowDetail.setHandlerName(handlerName);
        approvalFlowDetail.setHandlerType(FlowHandleType.EDIT.getValue());
        approvalFlowDetail.setStepNum(approvalFlow.getStepNum());
        approvalFlowDetail.setStatus(HandleStatus.AGREE.getValue());
        approvalFlowDetail.setOperatorName(handlerName);
        //更改前信息记录
        String diffInfo = ContrastObjUtils.compareJsonObject(oldInfo,newInfo);
        approvalFlowDetail.getJsonEntity().setEditDiffInfo(diffInfo);
        approvalFlowDetail.setPlatform(platform);
        approvalFlowDetail.setIsHistory(true);
        //两表事务
        commonBiz.logAndUpdateExtInfo(approvalFlow,approvalFlowDetail);

        //避免长事务 异步自动同意进入下一步
        List<ApprovalFlowDetail> details = commonBiz.listByFlowIdAndStepNumAndIsHistory(approvalFlowId,approvalFlow.getStepNum(),false);
        List<ApprovalFlowDetail> ownDetails = details.stream().filter(
                detail->detail.getHandlerId().equals(approvalFlow.getInitiatorId())&&detail.getPlatform().equals(platform)
        ).collect(Collectors.toList());
        ApprovalFlowDetail agreeDetail = ownDetails.get(0);

        Map<String,Object> infoMap = Maps.newHashMap();
        infoMap.put("approvalDetailId", agreeDetail.getId());
        infoMap.put("handlerId", agreeDetail.getHandlerId());
        infoMap.put("platformSource", agreeDetail.getPlatform());
        infoMap.put("flowName", FlowTopicType.getNameByValue(approvalFlow.getFlowTopicType()));
        infoMap.put("approvalOpinion", "");
        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC,NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_APPROVAL_AUTO_AGREE_ASYNC,JSON.toJSONString(infoMap));

    }
}
