package com.zhixianghui.service.common.core.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhixianghui.facade.common.entity.tenant.TenantWebsite;
import com.zhixianghui.facade.common.vo.TenantLinkVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Mapper
public interface TenantWebsiteMapper extends BaseMapper<TenantWebsite> {

    List<TenantLinkVo> getVoListByTenantId(@Param("tenantId") Long id);
}
