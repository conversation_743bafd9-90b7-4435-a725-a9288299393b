package com.zhixianghui.service.common.core.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.common.vo.NotificationDetailFullInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface NotificationRecordDetailMapper extends BaseMapper<NotificationRecordDetail> {
    int updateBatch(List<NotificationRecordDetail> list);

    int updateBatchSelective(List<NotificationRecordDetail> list);

    int batchInsert(@Param("list") List<NotificationRecordDetail> list);

    int insertOrUpdate(NotificationRecordDetail record);

    int insertOrUpdateSelective(NotificationRecordDetail record);

    IPage<NotificationDetailFullInfo> listNotificationRecordFullInfo(Page<Map<String, Object>> page,@Param("param") Map<String, Object> params);
}
