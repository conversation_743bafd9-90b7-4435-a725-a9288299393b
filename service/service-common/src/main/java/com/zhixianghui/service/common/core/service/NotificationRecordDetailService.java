package com.zhixianghui.service.common.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.common.vo.NotificationDetailFullInfo;
import com.zhixianghui.service.common.core.dao.mapper.NotificationRecordDetailMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
@Service
public class NotificationRecordDetailService extends ServiceImpl<NotificationRecordDetailMapper, NotificationRecordDetail> {


    public int updateBatch(List<NotificationRecordDetail> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<NotificationRecordDetail> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<NotificationRecordDetail> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(NotificationRecordDetail record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(NotificationRecordDetail record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    public IPage<NotificationDetailFullInfo> listNotificationRecordFullInfo(Page<Map<String, Object>> page, Map<String, Object> params) {
        return baseMapper.listNotificationRecordFullInfo(page,params);
    }
}
