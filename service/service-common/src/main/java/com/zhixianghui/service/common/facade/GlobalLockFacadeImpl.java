package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.globallock.GlobalLock;
import com.zhixianghui.facade.common.service.GlobalLockFacade;
import com.zhixianghui.service.common.core.biz.globallock.GlobalLockBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * Author: Cmf
 * Date: 2020.1.15
 * Time: 11:44
 * Description:
 */
@Service
public class GlobalLockFacadeImpl implements GlobalLockFacade {
    @Autowired
    private GlobalLockBiz globalLockBiz;

    @Override
    public String tryLock(String resourceId, int expireSecond, String clientFlag) {
        return globalLockBiz.tryLock(resourceId, expireSecond, clientFlag);
    }

    @Override
    public boolean unlock(String resourceId, String clientId) {
        return globalLockBiz.unlock(resourceId, clientId);
    }

    @Override
    public boolean unlockForce(String resourceId, boolean isNeedDelete) {
        return globalLockBiz.unlockForce(resourceId, isNeedDelete);
    }

    @Override
    public PageResult<List<GlobalLock>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return globalLockBiz.listPage(paramMap, pageParam);
    }
}
