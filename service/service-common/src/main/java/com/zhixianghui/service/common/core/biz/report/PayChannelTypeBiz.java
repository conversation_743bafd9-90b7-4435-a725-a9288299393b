package com.zhixianghui.service.common.core.biz.report;

import com.zhixianghui.facade.common.entity.report.PayChannelType;
import com.zhixianghui.service.common.core.dao.PayChannelTypeDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2021-06-25
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PayChannelTypeBiz {

    private final PayChannelTypeDao paychanneltypeDao;

    public List<PayChannelType> getListByChannelNo(String payChannelNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("payChannelNo",payChannelNo);
        return paychanneltypeDao.listBy(paramMap);
    }
}