package com.zhixianghui.service.common.service;

import com.zhixianghui.common.statics.exception.SequenceExceptions;
import com.zhixianghui.middleware.leaf.common.Result;
import com.zhixianghui.middleware.leaf.common.Status;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
public class RedisService {

    @Autowired
    private StringRedisTemplate  stringRedisTemplate;

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY = 10;

    /**
     * Lua脚本：当数字超过指定值时重置为0
     */
    private static final String RESET_SCRIPT = "local cur=redis.call('get', KEYS[1]); " +
            "if(tonumber(cur)>=tonumber(ARGV[1])) then redis.call('set', KEYS[1], 0); end; " +
            "return cur; ";

    /**
     * MAX_VALUE[i]: i位数的最大值
     */
    private static final Long[] MAX_VALUE = new Long[] {
            0L,
            10L,
            100L,
            1000L,
            10000L,
            100000L,
            1000000L,
            10000000L,
            100000000L,
            1000000000L,
            10000000000L,
    };

    public Result getId(String key) {
        RedisAtomicLong atomicLong = new RedisAtomicLong(key, redisConnectionFactory);
        return new Result(atomicLong.incrementAndGet(), Status.SUCCESS);
    }

    public Result getId(String key, int width) {
        if (width <= 0 || width >= MAX_VALUE.length) {
            throw SequenceExceptions.SEQUENCE_COMMON_EXCEPTION.newWithErrMsg("ID长度限定1到10");
        }

        RedisAtomicLong atomicLong = new RedisAtomicLong(key, redisConnectionFactory);

        int retry = 0;   // 重试次数
        do {
            if (retry > MAX_RETRY) {
                throw SequenceExceptions.SEQUENCE_COMMON_EXCEPTION.newWithErrMsg("系统繁忙");
            }

            long id = atomicLong.incrementAndGet();
            if (id < MAX_VALUE[width]) {
                return new Result(id, Status.SUCCESS);
            } else {
                DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
                redisScript.setScriptText(RESET_SCRIPT);
                stringRedisTemplate.execute(redisScript, Collections.singletonList(key), String.valueOf(MAX_VALUE[width]));
            }
            retry++;
        } while (true);
    }

    public Result getId(String key, int width, int count) {
        if (width <= 0 || width >= MAX_VALUE.length) {
            throw SequenceExceptions.SEQUENCE_COMMON_EXCEPTION.newWithErrMsg("ID长度限定1到10");
        }

        RedisAtomicLong atomicLong = new RedisAtomicLong(key, redisConnectionFactory);

        int retry = 0;   // 重试次数
        do {
            if (retry > MAX_RETRY) {
                throw SequenceExceptions.SEQUENCE_COMMON_EXCEPTION.newWithErrMsg("系统繁忙");
            }

            long id = atomicLong.addAndGet(count);
            if (id < MAX_VALUE[width]) {
                return new Result(id, Status.SUCCESS);
            } else {
                DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
                redisScript.setScriptText(RESET_SCRIPT);
                stringRedisTemplate.execute(redisScript, Collections.singletonList(key), String.valueOf(MAX_VALUE[width]));
            }
            retry++;
        } while (true);
    }
}
