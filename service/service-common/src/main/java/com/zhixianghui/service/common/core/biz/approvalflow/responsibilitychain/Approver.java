package com.zhixianghui.service.common.core.biz.approvalflow.responsibilitychain;


import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.ApprovalEndHandlerBiz;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 审批节点 (责任链-审批人)
 * @date 2020-08-12 09:25
 **/
@Slf4j
public abstract class Approver {

    private Approver nextApprover;

    @Autowired
    private ApprovalFlowDao approvalFlowDao;
    @Autowired
    private ApprovalEndHandlerBiz approvalEndHandlerBiz;

    /**
     * 链式调用
     * @param approver 传入的下一个approver
     * @return 下一个approver
     */
    public Approver setApprover(Approver approver) {
        this.nextApprover = approver;
        return approver;
    }

    public final ApprovalInfoVo nextHandle(ApprovalFlow approvalFlow) {
        if(this.nextApprover != null) {
            return this.nextApprover.handleFlow(approvalFlow);
        }else {
            //approvalFlow 更新为已完成
            approvalFlow.setEndTime(new Date());
            approvalFlow.setStatus(FlowStatus.FINISHED.getValue());
            approvalFlowDao.update(approvalFlow);
            log.info("下一步已无处理流程，审批流程:{} ending,执行审批后业务流程",approvalFlow.getId());
            //执行不同的结束流程
            approvalEndHandlerBiz.endHandle(approvalFlow);
            return ApprovalInfoVo.builder().id(approvalFlow.getId()).isLast(true).build();
        }
    }

    /**
     * 处理流程
     * @param approvalFlow 审批流对象
     */
    public abstract ApprovalInfoVo handleFlow(ApprovalFlow approvalFlow);
}

