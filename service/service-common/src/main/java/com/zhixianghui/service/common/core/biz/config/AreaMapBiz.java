package com.zhixianghui.service.common.core.biz.config;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.service.common.core.dao.mapper.AreaMapMapper;
import com.zhixianghui.facade.common.entity.config.AreaMap;
@Service
public class AreaMapBiz extends ServiceImpl<AreaMapMapper, AreaMap> {


    public AreaMap getByZxhCode(String zxhCode) {
        AreaMap areaMap = this.getById(zxhCode);
        return areaMap;
    }

}
