package com.zhixianghui.service.common.core.biz.tenant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.tenant.TenantManage;
import com.zhixianghui.facade.common.entity.tenant.TenantWebsite;
import com.zhixianghui.facade.common.enums.TenantWebsiteTypeEnum;
import com.zhixianghui.facade.common.vo.TenantLinkVo;
import com.zhixianghui.facade.common.vo.TenantVo;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.service.MerchantFileFacade;
import com.zhixianghui.service.common.core.dao.mapper.TenantManageMapper;
import com.zhixianghui.service.common.core.dao.mapper.TenantWebsiteMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-03-24
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TenantManageBiz {

    private final TenantManageMapper tenantManageMapper;

    private final TenantWebsiteBiz tenantWebsiteBiz;

    @Reference
    private MerchantFileFacade merchantFileFacade;

    @Transactional(rollbackFor = Exception.class)
    public void addTenant(TenantVo tenantVo, String realName) {
        TenantManage tenantManage = new TenantManage();
        BeanUtil.copyProperties(tenantVo,tenantManage);
        tenantManage.setCreateTime(new Date());
        tenantManage.setCreator(realName);
        tenantManage.setStatus(OpenOffEnum.OPEN.getValue());

        tenantManageMapper.insert(tenantManage);

        addWebSite(tenantManage.getId(),tenantVo);
    }

    private void addWebSite(Long tenantId,TenantVo tenantVo) {
        List<TenantWebsite> websites = new ArrayList<>();
        for (TenantLinkVo tenantLinkVo : tenantVo.getLink()) {
            TenantWebsite website = new TenantWebsite();
            if (tenantLinkVo.getSiteType().intValue() == TenantWebsiteTypeEnum.PRIVATE_WEBSITE.getValue() && StringUtils.isBlank(tenantLinkVo.getIcpNo())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("租户私有域名必须要填写备案信息");
            }

            if (tenantLinkVo.getSiteType().intValue() == TenantWebsiteTypeEnum.PLATFORM_WEBSITE.getValue()){
                website.setIcpNo(TenantWebsiteTypeEnum.PLATFORM_WEBSITE.getDefaultIcp());
            }else{
                website.setIcpNo(tenantLinkVo.getIcpNo());
            }
            website.setSiteType(tenantLinkVo.getSiteType());
            website.setTenantId(tenantId);
            website.setWebsite(tenantLinkVo.getWebsite());
            websites.add(website);
        }

        tenantWebsiteBiz.saveBatch(websites);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        tenantManageMapper.deleteById(id);
        tenantWebsiteBiz.removeByMap(new HashMap<String,Object>(){{put("tenant_id",id);}});
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(TenantVo tenantVo, String realName) {
        TenantManage tenantManage = tenantManageMapper.selectById(tenantVo.getId());
        if (tenantManage == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("租户不存在");
        }

//        if (tenantManage.getStatus().intValue() == OpenOffEnum.OPEN.getValue()){
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请先禁用后再修改租户信息");
//        }

        tenantManage.setWebSiteName(tenantVo.getWebSiteName());
        tenantManage.setStartTime(tenantVo.getStartTime());
        tenantManage.setEndTime(tenantVo.getEndTime());
        tenantManage.setUpdateTime(new Date());
        tenantManage.setUpdator(realName);
        tenantManageMapper.updateById(tenantManage);

        //删除地址重新添加
        tenantWebsiteBiz.remove(new QueryWrapper<TenantWebsite>().lambda().eq(TenantWebsite::getTenantId,tenantManage.getId()));
        addWebSite(tenantManage.getId(),tenantVo);
    }

    public void updateStatus(Long id, Integer status) {
        TenantManage tenantManage = tenantManageMapper.selectById(id);
        if (tenantManage == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("租户不存在");
        }

        tenantManage.setStatus(status);
        tenantManageMapper.updateById(tenantManage);
    }

    public PageResult<List<TenantVo>> selectPage(TenantManage tenantManage, int pageSize, int pageCurrent) {
        Page page = new Page<>();
        page.setSize(pageSize);
        page.setCurrent(pageCurrent);
        QueryWrapper<TenantManage> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(tenantManage.getTenantName())){
            queryWrapper.lambda().like(TenantManage::getTenantName,tenantManage.getTenantName());
        }
        Page<TenantManage> record = tenantManageMapper.selectPage(page,queryWrapper);
        List<TenantVo> tenantVoList = new ArrayList<>();
        for (TenantManage x : record.getRecords()) {
            TenantVo tenantVo = new TenantVo();
            BeanUtil.copyProperties(x,tenantVo);
            tenantVo.setLink(tenantWebsiteBiz.getListByTenantId(x.getId()));
            tenantVoList.add(tenantVo);
        }
        return PageResult.newInstance(tenantVoList, pageCurrent, pageSize, record.getTotal());
    }

    public Map<String, Object> getTenantLogo(String uri) {
        Map<String,Object> result = tenantManageMapper.selectByWebsite(uri,OpenOffEnum.OPEN.getValue());
        if (result == null){
            return null;
        }
        String tenantNo = (String) result.get("tenantNo");
        String icpNo = (String) result.get("icpNo");
        String webSiteName = (String) result.get("webSiteName");

        Map<String,Object> respMap = new HashMap<>();
        respMap.put("tenantNo",tenantNo);
        respMap.put("platformLogoUrl","");
        respMap.put("loginLogoUrl","");
        respMap.put("icpNo",icpNo);
        respMap.put("webSiteName",webSiteName);
        List<MerchantFile> merchantFileList = merchantFileFacade.listByMchNo(tenantNo);
        for (MerchantFile merchantFile : merchantFileList) {
            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.PLATFORM_LOGO.getValue()){
                respMap.put("platformLogoUrl",merchantFile.getFileUrl());
                continue;
            }

            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.LOGIN_LOGO.getValue()){
                respMap.put("loginLogoUrl",merchantFile.getFileUrl());
            }
        }
        return respMap;
    }

    public TenantManage getTenantByTenantNo(String tenantNo) {
        return tenantManageMapper.selectOne(new QueryWrapper<TenantManage>().lambda().eq(TenantManage::getTenantNo,tenantNo));
    }
}