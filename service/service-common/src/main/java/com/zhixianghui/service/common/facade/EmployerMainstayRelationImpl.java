package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.service.common.core.biz.report.EmployerMainstayRelationBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 代征关系表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmployerMainstayRelationImpl implements EmployerMainstayRelationFacade {

    private final EmployerMainstayRelationBiz biz;

    @Override
    public void create(EmployerMainstayRelation employerMainstayRelation) {
        biz.create(employerMainstayRelation);
    }

    @Override
    public void changeStatus(Long id, Integer status, String operator) {
        biz.changeStatus(id,status,operator);
    }

    @Override
    public void updateIfNotNull(EmployerMainstayRelation employerMainstayRelation) {
        biz.updateIfNotNull(employerMainstayRelation);
    }

    @Override
    public void deleteById(Long id) {
        biz.deleteById(id);
    }

    @Override
    public void deleteByMainstayNo(String mainstayNo) {
        biz.deleteByMainstayNo(mainstayNo);
    }

    @Override
    public PageResult<List<EmployerMainstayRelation>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public EmployerMainstayRelation getById(Long id) {
        return biz.getById(id);
    }

    @Override
    public List<EmployerMainstayRelation> listBy(Map<String, Object> paramMap) {
        return biz.listBy(paramMap);
    }

    @Override
    public void updateEmployerMainstayRelation(EmployerMainstayRelation employerMainstayRelation) {
        biz.updateEmployerMainstayRelation(employerMainstayRelation);

    }

    @Override
    public EmployerMainstayRelation getByEmployerNoAndMchNo(String employerNo, String mainstayNo) {
        return biz.getByEmployerNoAndMainstayNo(employerNo, mainstayNo);
    }

    @Override
    public EmployerMainstayRelation getByExternalEnterpriseSn(String externalEnterpriseSn) {
        return biz.getByExternalEnterpriseSn(externalEnterpriseSn);
    }

    @Override
    public void batchUpdate(Integer status, List<Long> ids) {
        biz.batchUpdate(status,ids);
    }
}
