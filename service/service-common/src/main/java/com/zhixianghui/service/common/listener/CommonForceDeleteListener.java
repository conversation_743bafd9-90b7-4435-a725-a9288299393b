package com.zhixianghui.service.common.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.service.common.core.biz.report.EmployerAccountInfoBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年02月11日 09:23:00
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FORCE_DELETE,
        selectorExpression = MessageMsgDest.TAG_COMMON_FORCE_DELETE,consumeThreadMax = 20,
        consumerGroup = "CommonForceDeleteConsume")
public class CommonForceDeleteListener extends BaseRocketMQListener<String> {


    @Autowired
    private EmployerAccountInfoBiz employerAccountInfoBiz;

    @Override
    public void validateJsonParam(String msg) {
        if(StringUtils.isEmpty(msg)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("计费规则删除异常");
        }
    }

    @Override
    public void consumeMessage(String msg) {
        employerAccountInfoBiz.forceDelete(msg);
    }
}