package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.report.ReportChannelRecord;
import com.zhixianghui.facade.common.service.ReportChannelRecordFacade;
import com.zhixianghui.service.common.core.biz.report.ReportChannelRecordBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 通道报备记录表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-10-15
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReportChannelRecordImpl implements ReportChannelRecordFacade {

    private final ReportChannelRecordBiz biz;

    @Override
    public ReportChannelRecord getBySerialNo(String serialNo) {
        return biz.getBySerialNo(serialNo);
    }

    @Override
    public ReportChannelRecord getById(Long id){
        return biz.getById(id);
    }

    @Override
    public void update(ReportChannelRecord reportChannelRecord) {
        biz.update(reportChannelRecord);
    }

    @Override
    public PageResult<List<ReportChannelRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap,pageParam);
    }

    @Override
    public void delete(ReportChannelRecord reportChannelRecord) {
        biz.deleteByEmployerNoAndMainstayNoAndPayChannelNo(reportChannelRecord.getEmployerNo(),reportChannelRecord.getMainstayNo(),reportChannelRecord.getPayChannelNo());;
    }

}
