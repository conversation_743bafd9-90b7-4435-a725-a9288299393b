package com.zhixianghui.service.common.core.biz.report.external;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.vo.report.UploadPicReqVo;
import com.zhixianghui.facade.common.dto.ReportEditDto;
import com.zhixianghui.facade.common.entity.report.*;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.service.MerchantFileFacade;
import com.zhixianghui.service.common.core.biz.config.DataDictionaryBiz;
import com.zhixianghui.service.common.core.biz.report.EmployerAccountInfoBiz;
import com.zhixianghui.service.common.core.biz.report.MainstayChannelRelationBiz;
import com.zhixianghui.service.common.core.biz.report.ReportAccountHistoryBiz;
import com.zhixianghui.service.common.core.biz.report.ReportChannelRecordBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ReportBiz {

    public static final String MODIFY_PRE = "MODIFY";

    @Reference
    private AlipayFacade alipayFacade;

    @Reference
    private MerchantFileFacade merchantFileFacade;

    @Autowired
    private DataDictionaryBiz dataDictionaryBiz;

    private final JoinPayBankCardReportBiz joinPayBankCardReportBiz;
    private final ReportChannelRecordBiz reportChannelRecordBiz;
    private final AliPayReportBiz aliPayReportBiz;
    private final EmployerAccountInfoBiz employerAccountInfoBiz;
    private final MainstayChannelRelationBiz mainstayChannelRelationBiz;
    private final YishuiReportBiz yishuiReportBiz;
    private final WxReportBiz wxReportBiz;
    private final CmbReportBiz cmbReportBiz;
    private final ReportAccountHistoryBiz reportAccountHistoryBiz;
    private final JxhReportBiz jxhReportBiz;

    public void report(ReportEntity reportEntity) {
        //记录提交报备日志
        Long recordId = doRecord(reportEntity);
        reportEntity.setRecordId(recordId);
        try{
            if (reportEntity.getPayChannelNo().equals(ChannelNoEnum.JOINPAY.name())){
                joinPayBankCardReportBiz.report(reportEntity);
            }else if(reportEntity.getPayChannelNo().equals(ChannelNoEnum.ALIPAY.name())){
                aliPayReportBiz.report(reportEntity);
            } else if (reportEntity.getPayChannelNo().equals(ChannelNoEnum.YISHUI.name())) {
                yishuiReportBiz.reportDetail(reportEntity);
            } else if(reportEntity.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())){
                wxReportBiz.report(reportEntity);
            } else if (reportEntity.getPayChannelNo().equals(ChannelNoEnum.CMB.name())) {
                cmbReportBiz.report(reportEntity);
            } else if (reportEntity.getPayChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name())) {
                jxhReportBiz.report(reportEntity);
            } else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应通道报备方法,请检查编号是否一致,通道是否存在。PayChannelNo:" + reportEntity.getPayChannelNo());
            }

        }catch (RpcException e){
            log.error("报备失败,失败原因:",e);
            ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(recordId);
            reportChannelRecord.setStatus(ReportStatusEnum.FAIL.getValue());
            reportChannelRecord.setUpdateTime(new Date());
            reportChannelRecord.setErrMsg("rpc服务异常");
            reportChannelRecordBiz.update(reportChannelRecord);
        } catch (Exception e){
            log.error("报备失败,失败原因:",e);
            ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(recordId);
            reportChannelRecord.setStatus(ReportStatusEnum.FAIL.getValue());
            reportChannelRecord.setUpdateTime(new Date());
            String errMsg;
            if(e.getMessage() == null){
                errMsg = null;
            }else {
                if(e.getMessage().length() > 200){
                    errMsg = e.getMessage().substring(0,100);
                }else {
                    errMsg = e.getMessage();
                }
            }
            reportChannelRecord.setErrMsg(errMsg);
            reportChannelRecordBiz.update(reportChannelRecord);
        }
    }

    /**
     * 记录提交报备日志
     * @param reportEntity 报备信息
     * @return 日志id
     */
    protected Long doRecord(ReportEntity reportEntity) {
        //清除报备日志-目的只为保留一个记录
        reportChannelRecordBiz.deleteByEmployerNoAndMainstayNoAndPayChannelNoAndType(
                reportEntity.getEmployerNo(),reportEntity.getMainstayNo(),reportEntity.getPayChannelNo(),reportEntity.getChannelType(),reportEntity.getMerchantType()
        );

//        reportChannelRecordBiz.deleteByEmployerNoAndMainstayNoAndPayChannelNo(
//                reportEntity.getEmployerNo(),reportEntity.getMainstayNo(),reportEntity.getPayChannelNo());

        ReportChannelRecord reportChannelRecord = new ReportChannelRecord();
        //初始记录为编号 不唯一
        reportChannelRecord.setSerialNo(reportEntity.getPayChannelNo());
        reportChannelRecord.setStatus(ReportStatusEnum.COMMIT.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        BeanUtils.copyProperties(reportEntity,reportChannelRecord);
        reportChannelRecordBiz.insert(reportChannelRecord);
        return reportChannelRecord.getId();
    }

    @Transactional(rollbackFor = Exception.class,noRollbackFor = {BizException.class})
    public void unsign(ReportEntity reportEntity) {
        String agreementNo = null;
        if (reportEntity.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(reportEntity.getEmployerNo(),reportEntity.getMainstayNo(),reportEntity.getChannelType());
            if (!employerAccountInfo.getEmployerName().equals(employerAccountInfo.getMchName())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("异名支付不允许解除签约");
            }
            if (StringUtils.isBlank(employerAccountInfo.getSubAgreementNo())){
                clearSignRecord(reportEntity);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议号不存在");
            }
            agreementNo = employerAccountInfo.getSubAgreementNo();
        }else if (reportEntity.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()){
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(),reportEntity.getPayChannelNo());
            if (StringUtils.isBlank(mainstayChannelRelation.getAgreementNo())){
                clearSignRecord(reportEntity);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议号不存在");
            }
            agreementNo = mainstayChannelRelation.getAgreementNo();
        }
        String res = alipayFacade.unsign(null, agreementNo);
        JSONObject jsonObject = JSON.parseObject(res).getJSONObject("alipay_user_agreement_unsign_response");
        if (jsonObject.getString("code").equals("10000")){
            clearSignRecordChannel(reportEntity,agreementNo);
        }else{
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(jsonObject.getString("sub_msg"));
        }
    }

    /**
     * 取消协议请求到上游
     * @param reportEntity
     */
    private void clearSignRecordChannel(ReportEntity reportEntity,String agreementNo) {
        if (reportEntity.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            Map<String,Object> paramsMap = new HashMap<>();
            paramsMap.put("employerNo",reportEntity.getEmployerNo());
            paramsMap.put("mainstayNo",reportEntity.getMainstayNo());
            paramsMap.put("payChannelNo",ChannelNoEnum.ALIPAY.name());
            paramsMap.put("channelType",reportEntity.getChannelType());
            paramsMap.remove("channelType");
            //解除签约是按照协议号来的，所以要找出相同协议号的账户
            paramsMap.put("subAgreementNo",agreementNo);
            List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoBiz.listBy(paramsMap);
            employerAccountInfoList.stream().forEach(x-> {
                x.setSubAgreementNo(null);
                x.setSubAlipayUserId(null);
                x.setSubMerchantNo("");
                x.setUpdateTime(new Date());
            });
            employerAccountInfoBiz.updateList(employerAccountInfoList);
            //修改账户记录表数据
            //设置所有相同协议号数据
            paramsMap.put("agreementNo",agreementNo);
            List<ReportAccountHistory> reportAccountHistoryList = reportAccountHistoryBiz.listBy(paramsMap);
            for (ReportAccountHistory reportAccountHistory : reportAccountHistoryList) {
                reportAccountHistoryBiz.delete(reportAccountHistory);
            }
        }else if (reportEntity.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()){
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(),reportEntity.getPayChannelNo());
            mainstayChannelRelation.setAlipayUserId(null);
            mainstayChannelRelation.setAgreementNo(null);
            mainstayChannelRelation.setChannelMchNo(null);
            mainstayChannelRelationBiz.update(mainstayChannelRelation);
            //供应商报备
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("mainstayNo",reportEntity.getMainstayNo());
            paramMap.put("payChannelNo", ChannelNoEnum.ALIPAY.name());
            List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoBiz.listBy(paramMap);
            employerAccountInfoList.stream().
                    forEach(employerAccountInfo -> {
                        employerAccountInfo.setUpdateTime(new Date());
                        employerAccountInfo.setParentAgreementNo(null);
                        employerAccountInfo.setParentAlipayUserId(null);
                        employerAccountInfo.setParentMerchantNo("");
                    });
            employerAccountInfoBiz.batchUpdate(employerAccountInfoList);
        }
    }

    /**
     * 清除签约信息
     * @param reportEntity
     */
    private void clearSignRecord(ReportEntity reportEntity) {
        if (reportEntity.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            Map<String,Object> paramsMap = new HashMap<>();
            paramsMap.put("employerNo",reportEntity.getEmployerNo());
            paramsMap.put("mainstayNo",reportEntity.getMainstayNo());
            paramsMap.put("payChannelNo",ChannelNoEnum.ALIPAY.name());
            paramsMap.put("channelType",reportEntity.getChannelType());
            List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoBiz.listBy(paramsMap);
            employerAccountInfoList.stream().forEach(x-> {
                x.setSubAgreementNo(null);
                x.setSubAlipayUserId(null);
                x.setSubMerchantNo("");
                x.setUpdateTime(new Date());
            });
            employerAccountInfoBiz.updateList(employerAccountInfoList);
        }else if (reportEntity.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()){
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(),reportEntity.getPayChannelNo());
            mainstayChannelRelation.setAlipayUserId(null);
            mainstayChannelRelation.setAgreementNo(null);
            mainstayChannelRelation.setChannelMchNo(null);
            mainstayChannelRelationBiz.update(mainstayChannelRelation);
            //供应商报备
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("mainstayNo",reportEntity.getMainstayNo());
            paramMap.put("payChannelNo", ChannelNoEnum.ALIPAY.name());
            List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoBiz.listBy(paramMap);
            employerAccountInfoList.stream().
                    forEach(employerAccountInfo -> {
                        employerAccountInfo.setUpdateTime(new Date());
                        employerAccountInfo.setParentAgreementNo(null);
                        employerAccountInfo.setParentAlipayUserId(null);
                        employerAccountInfo.setParentMerchantNo("");
                    });
            employerAccountInfoBiz.batchUpdate(employerAccountInfoList);
        }
    }

    public void modify(ReportEntity reportEntity) {
        String employerNo = reportEntity.getEmployerNo();
        String mainstayNo = reportEntity.getMainstayNo();
        String channelNo = reportEntity.getPayChannelNo();

        //查询商户账户信息
        EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelNo(
                employerNo, mainstayNo, channelNo);
        if (employerAccountInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户账户信息不存在");
        }

        ReportEditDto editDto = new ReportEditDto();
        BeanUtil.copyProperties(reportEntity, editDto);
        if (ChannelNoEnum.JOINPAY.name().equals(channelNo)) {
            this.addModifyRecord(reportEntity, employerAccountInfo);//插入修改记录
            joinPayBankCardReportBiz.modify(editDto, employerAccountInfo);
        } else if (ChannelNoEnum.JOINPAY_JXH.name().equals(channelNo)) {
            this.addModifyRecord(reportEntity, employerAccountInfo);//插入修改记录
            jxhReportBiz.modify(editDto, employerAccountInfo);
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前通道暂不支持修改报备信息");
        }
    }

    private void addModifyRecord(ReportEntity reportEntity, EmployerAccountInfo employerAccountInfo) {
        String serialNo = MODIFY_PRE + reportEntity.getEmployerNo() + reportEntity.getMainstayNo();
        ReportChannelRecord reportRecord = reportChannelRecordBiz.getBySerialNo(serialNo);
        if (reportRecord != null) {
            if (reportRecord.getStatus() != ReportStatusEnum.SUCCESS.getValue()
                    && reportRecord.getStatus() != ReportStatusEnum.FAIL.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("已有正在报备的记录，不进行报备");
            }
            reportChannelRecordBiz.deleteBySerialNo(serialNo);//先删除重复记录
        }
        reportRecord = this.buildModifyRecord(reportEntity, employerAccountInfo, serialNo);
        reportChannelRecordBiz.insert(reportRecord);
    }

    private ReportChannelRecord buildModifyRecord(ReportEntity reportEntity, EmployerAccountInfo employerAccountInfo, String serialNo) {
        ReportChannelRecord reportChannelRecord = new ReportChannelRecord();
        reportChannelRecord.setSerialNo(serialNo);
        reportChannelRecord.setCreateTime(new Date());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecord.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        reportChannelRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
        reportChannelRecord.setMainstayName(employerAccountInfo.getMainstayName());
        reportChannelRecord.setEmployerNo(employerAccountInfo.getEmployerNo());
        reportChannelRecord.setEmployerName(employerAccountInfo.getEmployerName());
        reportChannelRecord.setPayChannelNo(employerAccountInfo.getPayChannelNo());
        reportChannelRecord.setPayChannelName(employerAccountInfo.getPayChannelName());
        reportChannelRecord.setReporter(reportEntity.getReporter());
        reportChannelRecord.setChannelType(ChannelTypeEnum.BANK.getValue());
        reportChannelRecord.setStatus(ReportStatusEnum.COMMIT.getValue());
        return reportChannelRecord;
    }
}
