package com.zhixianghui.service.common.core.biz.report;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.report.ReportChannelRecord;
import com.zhixianghui.service.common.core.dao.ReportChannelRecordDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 通道报备记录表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-10-15
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReportChannelRecordBiz {

    private final ReportChannelRecordDao reportChannelRecordDao;

    public void insert(ReportChannelRecord reportChannelRecord){
        reportChannelRecordDao.insert(reportChannelRecord);
    }

    public ReportChannelRecord getById(Long id){
        return reportChannelRecordDao.getById(id);
    }

    public void update(ReportChannelRecord record){
        reportChannelRecordDao.update(record);
    }

    public ReportChannelRecord getBySerialNo(String serialNo) {
        return reportChannelRecordDao.getOne(Collections.singletonMap("serialNo", serialNo));
    }

    public PageResult<List<ReportChannelRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return reportChannelRecordDao.listPage(paramMap,pageParam);
    }

    public List<ReportChannelRecord> listBy(Map<String,Object> paramMap){
        return reportChannelRecordDao.listBy(paramMap);
    }

    public List<ReportChannelRecord> getRecordByEmployerNoAndMainstayNoAndChannelNo(String employerNo, String mainstayNo, String payChannelNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("payChannelNo",payChannelNo);
        return reportChannelRecordDao.listBy(paramMap);
    }

    public void deleteByEmployerNoAndMainstayNoAndPayChannelNo(String employerNo, String mainstayNo, String payChannelNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("payChannelNo",payChannelNo);
        reportChannelRecordDao.deleteBy("deleteByEmployerNoAndMainstayNoAndPayChannelNo",paramMap);
    }

    public void deleteByEmployerNoAndMainstayNoAndPayChannelNoAndType(String employerNo, String mainstayNo, String payChannelNo, Integer channelType,Integer merchantType) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("payChannelNo",payChannelNo);
        paramMap.put("channelType",channelType);
        paramMap.put("merchantType",merchantType);
        reportChannelRecordDao.deleteBy("deleteByEmployerNoAndMainstayNoAndPayChannelNoAndType",paramMap);
    }

    public void deleteBySerialNo(String serialNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("serialNo",serialNo);
        reportChannelRecordDao.deleteBy("deleteBySerialNo",paramMap);
    }

    /***
     * 查询
     * @param employerNo
     * @param mainstayNo
     * @return
     */
    public long selectCountByMchNoAndMainstayNo(String employerNo, String mainstayNo, List<Integer> statusList) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", employerNo);
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("statusList", statusList);
        return reportChannelRecordDao.countBy(paramMap);
    }
}