package com.zhixianghui.service.common.core.biz.push;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.service.utils.MybatisUtil;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.dto.PushManagerDto;
import com.zhixianghui.facade.common.entity.push.PushManager;
import com.zhixianghui.facade.common.vo.PushManagerVo;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.service.common.core.dao.mapper.PushManagerMapper;
import com.zhixianghui.service.common.core.service.PushManagerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-02-21
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PushManagerBiz {

    private final PushManagerMapper pushManagerMapper;

    @Reference
    private MerchantFacade merchantFacade;

    public void add(PushManagerDto pushManagerDto,String createBy) {
        PushManager pushManager = new PushManager();
        BeanUtils.copyProperties(pushManagerDto,pushManager);
        Date now = new Date();
        //确认是否有相同配置
//        PushManager existManager = pushManagerMapper.selectOne(new QueryWrapper<PushManager>().lambda()
//                .eq(PushManager::getMchNo,pushManager.getMchNo())
//                .eq(PushManager::getPushType,pushManager.getPushType()));
//        if (existManager != null){
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该商户已有相同配置，请勿重复添加");
//        }
        pushManager.setCreateBy(createBy);
        pushManager.setCreateTime(now);
        pushManager.setUpdateBy(createBy);
        pushManager.setUpdateTime(now);
        pushManagerMapper.insert(pushManager);
    }

    public void deleteById(Long id) {
        pushManagerMapper.deleteById(id);
    }

    public void update(PushManager pushManager, String realName) {
        pushManager.setUpdateBy(realName);
        pushManager.setUpdateTime(new Date());
//        //确认是否有相同配置
//        PushManager existManager = pushManagerMapper.selectOne(new QueryWrapper<PushManager>().lambda()
//                .eq(PushManager::getMchNo,pushManager.getMchNo())
//                .eq(PushManager::getPushType,pushManager.getPushType())
//                .ne(PushManager::getId,pushManager.getId()));
//        if (existManager != null){
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该商户已有相同配置，请勿重复添加");
//        }
        pushManagerMapper.updateById(pushManager);
    }

    public PageResult<List<PushManagerVo>> selectPage(PushManager pushManager, int pageSize, int pageCurrent) {
        Page page = new Page<>();
        page.setSize(pageSize);
        page.setCurrent(pageCurrent);
        QueryWrapper<PushManager> queryWrapper = new QueryWrapper<>();
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",pushManager.getMchNo());
        paramMap.put("pushType",pushManager.getPushType());
        MybatisUtil.buildWhere(queryWrapper,PushManager.class,paramMap);
        Page<PushManager> pushManagerPage = pushManagerMapper.selectPage(page,queryWrapper);
        List<PushManagerVo> pushManagerVoList = pushManagerPage.getRecords().stream().map(x->{
            PushManagerVo pushManagerVo = new PushManagerVo();
            BeanUtil.copyProperties(x,pushManagerVo);
            pushManagerVo.setMchName(merchantFacade.getByMchNo(x.getMchNo()).getMchName());
            return pushManagerVo;
        }).collect(Collectors.toList());
        return PageResult.newInstance(pushManagerVoList, pageCurrent, pageSize, pushManagerPage.getTotal());
    }

    public List<PushManager> listBy(Map<String, Object> paramMap) {
        QueryWrapper<PushManager> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper,PushManager.class,paramMap);
        return pushManagerMapper.selectList(queryWrapper);
    }
}