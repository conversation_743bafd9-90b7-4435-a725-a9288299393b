package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.service.common.core.dao.BankCardBinDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DataDictionaryBiz
 *
 * <AUTHOR>
 * @date 2019/11/14
 */
@Service
@Slf4j
public class BankCardBinBiz {
    @Autowired
    private BankCardBinDao bankCardBinDao;

    /**
     * 获取卡的卡BIN
     * 不对银行卡号校验是否是规范的卡号
     * 目的是给一些需要获取卡宾但不符合卡号规范使用
     * @param cardNo
     * @return
     */
    public BankCardBin getCardBinByCardNo(String cardNo) {
        String logkey = DesensitizeUtil.maskBankCardNo(cardNo);
        log.debug("cardNo is :" + cardNo);
        if(StringUtil.isEmpty(cardNo) || cardNo.length() < 3) {
            return null;
        }

        int len = Math.min(cardNo.length(), 12);

        for(int i = len; i >= 3; i--) {
            String cardBinStr = cardNo.substring(0, i);
            log.debug("[{}]==>尝试取前{}位:{}", logkey, i, cardBinStr);

            BankCardBin cardBin = getByCardBin(cardBinStr, PublicStatus.ACTIVE, cardNo.length());
            if (cardBin != null) {
                log.info("[{}]==>前{}位取得cardbin", logkey, i);
                return cardBin;
            }
        }
        log.info("[{}]==>没有取得cardbin", logkey);
        return null;
    }

    /**
     * 根据卡Bin值查找卡Bin信息.
     *
     * @param cardBin
     * @return
     */
    public BankCardBin getByCardBin(String cardBin, Integer status, Integer cardLength) {
        if (StringUtil.isEmpty(cardBin)
                || status == null
                || cardLength == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("cardBin|status|cardLength不能为空");
        }
        return bankCardBinDao.getByCardBin(cardBin, status, cardLength);
    }

}
