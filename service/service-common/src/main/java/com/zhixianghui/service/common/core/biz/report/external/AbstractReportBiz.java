package com.zhixianghui.service.common.core.biz.report.external;

import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.dto.ReportEditDto;
import com.zhixianghui.facade.common.entity.report.*;
import com.zhixianghui.service.common.core.biz.report.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractReportBiz {

    @Autowired
    protected PayChannelBiz payChannelBiz;
    @Autowired
    protected EmployerMainstayRelationBiz employerMainstayRelationBiz;
    @Autowired
    protected MainstayChannelRelationBiz mainstayChannelRelationBiz;
    @Autowired
    protected EmployerAccountInfoBiz employerAccountInfoBiz;
    @Autowired
    protected ReportChannelRecordBiz reportChannelRecordBiz;

    /**
     * 报备流程
     * @param reportEntity 报备参数
     */
    void report(ReportEntity reportEntity) throws Exception {
        //校验上游全部关系
        validUpstream(reportEntity);
        //具体报备逻辑
        reportDetail(reportEntity);
    }

    /**
     * 具体报备逻辑
     * @param reportEntity 报备参数
     * @throws Exception e
     */
    protected abstract void reportDetail(ReportEntity reportEntity) throws Exception;

    /**
     * 校验上游全部关系
     * @param reportEntity 报备参数
     */
    void validUpstream(ReportEntity reportEntity){
        log.info("开始报备上游条件检验,报备参数：{}" , reportEntity);
        //检验通道
        PayChannel payChannel = payChannelBiz.getByChannelNo(reportEntity.getPayChannelNo());
        if(payChannel == null || payChannel.getStatus().equals(OpenOffEnum.OFF.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("报备通道不存在或者状态未开启 请检查！PayChannelNo:" + reportEntity.getPayChannelNo());
        }

        //不能进行微信收款报备
        if (reportEntity.getPayChannelNo().equals(ChannelNoEnum.WXGATHERING.name())){
             if (reportEntity.getMerchantType().intValue() == MerchantTypeEnum.EMPLOYER.getValue()){
                 throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业无法进行微信收款报备");
             }else if (reportEntity.getMerchantType().intValue() == MerchantTypeEnum.MAINSTAY.getValue()){
                 throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体无须报备微信收款，直接填写微信商户号即可");
             }
        }

        //如果属于供应商报备则供应商-通道必定为空，需要增加判断
        if (reportEntity.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()){
            return ;
        }

        //校验供应商-通道
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(), reportEntity.getPayChannelNo());
        if(mainstayChannelRelation == null || mainstayChannelRelation.getStatus().equals(OpenOffEnum.OFF.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商-通道(供应商账户)不存在或者状态未开启 请检查！MainstayNo:" + reportEntity.getMainstayNo() + ", PayChannelNo:"+ reportEntity.getPayChannelNo());
        }

        //校验供应商-用工企业 代征关系
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationBiz.getByEmployerNoAndMainstayNo(reportEntity.getEmployerNo(),reportEntity.getMainstayNo());
        if(employerMainstayRelation == null || employerMainstayRelation.getStatus().equals(OpenOffEnum.OFF.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征关系不存在或者状态未开启 请检查！ EmployerNo :" + reportEntity.getEmployerNo() + ", MainstayNo:"+ reportEntity.getMainstayNo());
        }
    }

    /**
     * 修改报备信息
     * @param reportDto
     * @return
     */
    protected abstract boolean modify(ReportEditDto reportDto, EmployerAccountInfo employerAccountInfo);
}
