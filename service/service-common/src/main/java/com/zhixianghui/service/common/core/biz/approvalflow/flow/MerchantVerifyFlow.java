package com.zhixianghui.service.common.core.biz.approvalflow.flow;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.base.BaseFlow;
import com.zhixianghui.service.common.core.biz.approvalflow.base.FlowInitiator;
import com.zhixianghui.service.common.core.biz.approvalflow.responsibilitychain.Approver;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 主体认证审批流程链
 * @date 2020-09-09 10:51
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantVerifyFlow extends BaseFlow {
    private final FlowInitiator flowInitiator;
    private final Approver operationDepartmentAllApprover1;

    @Override
    public ApprovalInfoVo handleFlow(ApprovalFlow approvalFlow){
        flowInitiator
                .setApprover(operationDepartmentAllApprover1);
        return flowInitiator.handleFlow(approvalFlow);
    }
}
