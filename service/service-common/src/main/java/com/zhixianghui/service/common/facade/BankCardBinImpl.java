package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.entity.config.BankInfo;
import com.zhixianghui.facade.common.service.BankCardBinFacade;
import com.zhixianghui.facade.common.service.BankInfoFacade;
import com.zhixianghui.service.common.core.biz.config.BankCardBinBiz;
import com.zhixianghui.service.common.core.biz.config.BankInfoBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 银行卡bin表
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BankCardBinImpl implements BankCardBinFacade {

    private final BankCardBinBiz bankCardBinBiz;

    @Override
    public BankCardBin getCardBinByCardNo(String cardNo) {
        return bankCardBinBiz.getCardBinByCardNo(cardNo);
    }
}
