package com.zhixianghui.service.common.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 审批节点详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-07
 */
@Repository
public class ApprovalFlowDetailDao extends MyBatisDao<ApprovalFlowDetail, Long> {

    /**
     * 撤回后所有节点都改成history
     * @param approvalFlowId 要撤回的审批
     */
    public void cancelApprovalDetail(Long approvalFlowId) {
        this.getSqlSession().update(fillSqlId("cancelApprovalDetail"),approvalFlowId);
    }
}
