package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.facade.common.entity.config.BankOrganization;
import com.zhixianghui.service.common.core.dao.BankOrganizationDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2021-07-09
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BankOrganizationBiz {

    private final BankOrganizationDao bankorganizationDao;

    public BankOrganization getByBankCode(String bankCode) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("bankCode",bankCode);
        return bankorganizationDao.getOne(paramMap);
    }
}