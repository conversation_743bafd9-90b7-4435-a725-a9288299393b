package com.zhixianghui.service.common.core.biz.approvalflow.base;

import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.facade.common.enums.FlowHandleType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.service.common.core.biz.approvalflow.responsibilitychain.Approver;
import com.zhixianghui.service.common.core.biz.approvalflow.responsibilitychain.OperationAllApprover;
import com.zhixianghui.service.common.core.biz.approvalflow.responsibilitychain.OperationLeaderApprover;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 运营平台审批流节点定义(复用)
 * @date 2020-08-12 16:17
 **/
@Component
public class OperationApprover {
    int platformSource = PlatformSource.OPERATION.getValue();

    @Bean
    public Approver createMerchantApprover1(){
        // 风控 & 销售 负责人 会签 1
        return new OperationLeaderApprover(1,platformSource, FlowHandleType.COUNTERSIGN.getValue(),
                Lists.newArrayList(PmsDepartmentNumberEnum.SALE.getNumber(),PmsDepartmentNumberEnum.RCM.getNumber()));
    }

    @Bean
    public Approver operationDepartmentAllApprover1(){
        // 运营部全体 或签 步骤1
        return new OperationAllApprover(1,platformSource, FlowHandleType.ORSIGN.getValue(),
                Lists.newArrayList(PmsDepartmentNumberEnum.OPERATION.getNumber()));
    }
}
