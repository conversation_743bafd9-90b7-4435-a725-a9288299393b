package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.vo.PortalDataDictionaryQueryVO;
import com.zhixianghui.service.common.core.dao.DataDictionaryDao;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DataDictionaryBiz
 *
 * <AUTHOR>
 * @date 2019/11/14
 */
@Service
@Slf4j
public class DataDictionaryBiz {
    @Autowired
    private DataDictionaryDao dataDictionaryDao;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private RedisLock redisLock;


    public DataDictionary getDataDictionaryById(Long id) {
        return dataDictionaryDao.getById(id);
    }

    public void deleteDataDictionaryById(long id) {
        DataDictionary dataDictionary = dataDictionaryDao.getById(id);
        if (StringUtils.equals("SystemConfig", dataDictionary.getDataName())) {
            log.info("删除系统参数缓存");
            this.flushSystemConfig(dataDictionary);
        }
        if (StringUtils.equals("SystemConfigNew", dataDictionary.getDataName())) {
            log.info("删除系统参数缓存");
            this.flushSystemConfigNew(dataDictionary);
        }
        dataDictionaryDao.deleteById(id);
    }

    public void updateDataDictionary(DataDictionary dataDictionary) {
        dataDictionaryDao.update(dataDictionary);
        if (StringUtils.equals("SystemConfig", dataDictionary.getDataName())) {
            log.info("刷新系统参数缓存");
            this.flushSystemConfig(dataDictionary);
        }
        if (StringUtils.equals("SystemConfigNew", dataDictionary.getDataName())) {
            log.info("刷新系统参数缓存");
            this.flushSystemConfigNew(dataDictionary);
        }
    }

    public void createDataDictionary(DataDictionary dataDictionary) {
        dataDictionaryDao.insert(dataDictionary);
        if (StringUtils.equals("SystemConfig", dataDictionary.getDataName())) {
            log.info("刷新系统参数缓存");
            redisClient.del("SystemConfig");
        }
    }

    private void flushSystemConfig(DataDictionary dataDictionary) {
        redisClient.del("SystemConfig");
        Map<String, String> map = new HashMap<>();
        for (DataDictionary.Item item : dataDictionary.getItemList()) {
            String configKey = item.getFlag();
            String configValue = item.getCode();
            map.put(configKey, configValue);
        }
        log.info("加载系统参数到redis");
        redisClient.hset("SystemConfig", map);
    }

    private void flushSystemConfigNew(DataDictionary dataDictionary) {
        redisClient.del("SystemConfigNew");
        Map<String, String> map = new HashMap<>();
        for (DataDictionary.Item item : dataDictionary.getItemList()) {
            String configKey = item.getCode();
            String configValue = item.getFlag();
            map.put(configKey, configValue);
        }
        log.info("加载系统参数到redis");
        redisClient.hset("SystemConfigNew", map);
    }

    public PageResult<List<DataDictionary>> listDataDictionaryPage(Map<String, Object> paramMap, PageParam pageParam) {
        return dataDictionaryDao.listPage(paramMap, pageParam);
    }

    public List<DataDictionary> listAllDataDictionary() {
        return dataDictionaryDao.listAll();
    }

    public DataDictionary getDataDictionaryByName(String dataName) {
        if (StringUtil.isEmpty(dataName)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("dataName不能为空");
        }
        return dataDictionaryDao.getOne(Collections.singletonMap("dataName", dataName));
    }


    public List<DataDictionary> listDataDictionary(List<Long> idList) {
        return dataDictionaryDao.listByIdList(idList);
    }

    public void create(List<DataDictionary> list) {
        dataDictionaryDao.insert(list);
        for (DataDictionary dataDictionary : list) {
            if (StringUtils.equals("SystemConfig", dataDictionary.getDataName())) {
                this.flushSystemConfig(dataDictionary);
            }
            if (StringUtils.equals("SystemConfigNew", dataDictionary.getDataName())) {
                this.flushSystemConfigNew(dataDictionary);
            }
        }
    }

    public String getSystemConfig(String field) {

        String value = redisClient.hget("SystemConfigNew", field);
        if (value != null) {
            log.info("【缓存命中】-系统参数");
            return value;
        }
        RLock rLock = redisLock.tryLock("uploadSystemConfigNew", 5000, 5000);
        if (rLock == null) {
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("获取刷新系统参数锁失败");
        }

        try {
            String value2 = redisClient.hget("SystemConfigNew", field);
            if (value2 != null) {
                return value2;
            }

            DataDictionary systemConfig = this.getDataDictionaryByName("SystemConfigNew");
            List<DataDictionary.Item> itemList = systemConfig.getItemList();

            Map<String, String> map = new HashMap<>();
            for (DataDictionary.Item item : itemList) {
                String configValue = item.getFlag();
                String  configKey= item.getCode();
                map.put(configKey, configValue);
            }
            log.info("加载系统参数到redis");
            redisClient.hset("SystemConfigNew", map);

            return map.get(field);
        }finally {
            redisLock.unlock(rLock);
        }
    }

    public List<DataDictionary> list(PortalDataDictionaryQueryVO portalDataDictionaryQueryVO) {
        Map<String, Object> params = BeanUtil.toMap(portalDataDictionaryQueryVO);
        return dataDictionaryDao.listBy(params);
    }
}
