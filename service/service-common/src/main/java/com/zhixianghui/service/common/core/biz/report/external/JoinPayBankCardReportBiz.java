package com.zhixianghui.service.common.core.biz.report.external;

import com.zhixianghui.common.statics.enums.bankLink.report.ApiReportStatusEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.ValidityDateTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.service.report.ChannelReportFacade;
import com.zhixianghui.facade.banklink.vo.account.SubMchNoQueryDto;
import com.zhixianghui.facade.banklink.vo.report.ReportReqVo;
import com.zhixianghui.facade.banklink.vo.report.ReportResVo;
import com.zhixianghui.facade.common.dto.ReportEditDto;
import com.zhixianghui.facade.common.entity.report.*;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.service.MerchantBankAccountFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.service.common.core.biz.report.ReportAccountHistoryBiz;
import com.zhixianghui.service.common.core.biz.report.ReportChannelRecordBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class JoinPayBankCardReportBiz extends AbstractReportBiz {

    @Reference
    private MerchantEmployerMainFacade employerMainFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantBankAccountFacade merchantBankAccountFacade;
    @Reference
    private ChannelReportFacade channelReportFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;

    @Autowired
    private ReportAccountHistoryBiz reportAccountHistoryBiz;
    @Autowired
    private ReportChannelRecordBiz reportChannelRecordBiz;

    @Override
    public void reportDetail(ReportEntity reportEntity) {
        String employerNo = reportEntity.getEmployerNo();

        //1.获取商户信息
        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到商户编号为[" + employerNo + "]的商户");
        }
        //2. 获取用工企业信息
        MerchantEmployerMain merchantEmployerMain = employerMainFacade.getByMchNo(employerNo);

        //3. 获取商户银行帐户表
        MerchantBankAccount merchantBankAccount = merchantBankAccountFacade.getByMchNo(employerNo);

        //4. 获取供应商服账户
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(reportEntity.getMainstayNo(),reportEntity.getPayChannelNo());

        //5. 调用bankLink接口报备
        ReportReqVo reportReqVo = fillReportReqVo(reportEntity,mainstayChannelRelation,merchantEmployerMain,merchant,merchantBankAccount);
        ReportResVo reportResVo = channelReportFacade.report(reportReqVo);

        //6. 报备后续处理
        afterReport(reportEntity, reportResVo,reportReqVo,mainstayChannelRelation);
    }

    void afterReport(ReportEntity reportEntity, ReportResVo reportResVo,ReportReqVo reportReqVo, MainstayChannelRelation mainstayChannelRelation) {
        Integer reportStatus = reportResVo.getApiReportStatus();
        if(reportStatus.equals(ApiReportStatusEnum.PROCESS.getValue())){
            doSuccess(reportEntity,reportResVo,reportReqVo);
        }else if(reportStatus.equals(ApiReportStatusEnum.FAIL.getValue())){
            log.info("汇聚支付-受理失败，失败原因：{}" , reportResVo.getBizMsg());
            if("B101001".equals(reportResVo.getBizCode())){
                //如果是登录名已存在的错误 反查
                doExist(reportEntity, mainstayChannelRelation);
            }else{
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("受理失败"+reportResVo.getBizMsg());
            }
        }else if(reportStatus.equals(ApiReportStatusEnum.UN_KNOW.getValue())){
            log.info("汇聚支付-受理未知，失败原因：{}", reportResVo.getBizMsg());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("受理未知"+reportResVo.getBizMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    void doExist(ReportEntity reportEntity,MainstayChannelRelation mainstayChannelRelation) {
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        reportChannelRecord.setStatus(ReportStatusEnum.FAIL.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecord.setErrMsg("已报备过，登录名已存在 尝试回填报备信息 请查看是否回填报备信息成功");
        reportChannelRecordBiz.update(reportChannelRecord);
        //反查存库
        log.info("报备登录名已存在，反查通道回填信息======>");
        EmployerAccountInfo employerAccountInfo =
                employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(
                        reportChannelRecord.getEmployerNo(),
                        reportChannelRecord.getMainstayNo(),
                        reportChannelRecord.getChannelType());

        SubMchNoQueryDto subMchNoQueryDto = new SubMchNoQueryDto();
        subMchNoQueryDto.setMainstayNo(reportChannelRecord.getMainstayNo());
        subMchNoQueryDto.setEmployerNo(reportChannelRecord.getEmployerNo());
        subMchNoQueryDto.setChannelType(reportChannelRecord.getChannelType());
        subMchNoQueryDto.setChannelNo(reportChannelRecord.getPayChannelNo());
        subMchNoQueryDto.setChannelMchNo(mainstayChannelRelation.getChannelMchNo());
        String subMchNo = accountQueryFacade.getSubMchNo(subMchNoQueryDto);
        if(subMchNo == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("已报备过,登录名已存在 尝试反查通道回填,但通道返回为null");
        }
        PayChannel payChannel = payChannelBiz.getByChannelNo(reportChannelRecord.getPayChannelNo());
        //回调时上游关系都为开启才能开启
        if(payChannel.getStatus().equals(OpenOffEnum.OPEN.getValue())
                && mainstayChannelRelation.getStatus().equals(OpenOffEnum.OPEN.getValue())){
            employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
        }else{
            employerAccountInfo.setStatus(OpenOffEnum.OFF.getValue());
        }

        employerAccountInfo.setUpdateTime(new Date());
        employerAccountInfo.setUpdateOperator(reportEntity.getReporter());
        employerAccountInfo.setParentMerchantNo(mainstayChannelRelation.getChannelMchNo());
        employerAccountInfo.setSubMerchantNo(subMchNo);
        employerAccountInfo.setPayChannelName(reportChannelRecord.getPayChannelName());
        employerAccountInfo.setPayChannelNo(reportChannelRecord.getPayChannelNo());
        employerAccountInfo.setSubAgreementNo("");
        employerAccountInfo.setSubAlipayUserId("");
        employerAccountInfoBiz.update(employerAccountInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    void doSuccess(ReportEntity reportEntity, ReportResVo reportResVo,ReportReqVo reportReqVo) {
        log.info("汇聚支付-受理成功, 更新报备记录");
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        reportChannelRecord.setSerialNo(ChannelNoEnum.JOINPAY.name() + reportResVo.getMchNo());
        reportChannelRecord.setUpdateTime(new Date());
        reportChannelRecord.setStatus(ReportStatusEnum.PROCESSING.getValue());
        reportChannelRecordBiz.update(reportChannelRecord);
    }

    /**
     * 填充报备请求对象
     */
    private ReportReqVo fillReportReqVo(ReportEntity reportEntity, MainstayChannelRelation mainstayChannelRelation, MerchantEmployerMain merchantEmployerMain, Merchant merchant, MerchantBankAccount merchantBankAccount) {
        ReportReqVo reportReqVo = new ReportReqVo();
        reportReqVo.setChannelNo(reportEntity.getPayChannelNo());
        reportReqVo.setChannelMchNo(mainstayChannelRelation.getChannelMchNo());
        reportReqVo.setChannelType(ChannelTypeEnum.BANK.getValue());
        reportReqVo.setLoginName(merchantEmployerMain.getMchNo()+mainstayChannelRelation.getMainstayNo());
        reportReqVo.setMchName(merchant.getMchName());
        reportReqVo.setShortName(merchantEmployerMain.getShortName());
        reportReqVo.setContactName(merchant.getContactName());
        reportReqVo.setContactPhone(merchant.getContactPhone());
        reportReqVo.setManagementScope(merchantEmployerMain.getManagementScope());
        reportReqVo.setManagementAddrDetail(merchantEmployerMain.getManagementAddrDetail());
        reportReqVo.setLegalPersonName(merchantEmployerMain.getLegalPersonName());
        reportReqVo.setCertificateNumber(merchantEmployerMain.getCertificateNumber());
        reportReqVo.setCertificateTermEnd(merchantEmployerMain.getCertificateTermEnd());
        // 营业执照 校验商户是否存在报备成功或渠道受理成功的数据，如果有，则不修改营业执照，否则可修改
        List<Integer> statusList = new ArrayList<>();
        statusList.add(ReportStatusEnum.PROCESSING.getValue());
        statusList.add(ReportStatusEnum.SUCCESS.getValue());
        long count = reportChannelRecordBiz.selectCountByMchNoAndMainstayNo(reportEntity.getEmployerNo(), reportEntity.getMainstayNo(), statusList);
        if (count <= 0) {
            reportReqVo.setTaxNo(merchantEmployerMain.getTaxNo());
        }

        if (merchantEmployerMain.getManagementValidityDateType().intValue() == ValidityDateTypeEnum.PERIOD.getValue()){
            reportReqVo.setManagementTermEnd(merchantEmployerMain.getManagementTermEnd());
        }else{
            reportReqVo.setManagementTermEnd("2099-01-01");
        }

        reportReqVo.setAccountName(merchantBankAccount.getAccountName());
        reportReqVo.setAccountNo(merchantBankAccount.getAccountNo());
        reportReqVo.setBankChannelNo(merchantBankAccount.getBankChannelNo());
        return reportReqVo;
    }

    @Override
    public boolean modify(ReportEditDto editDto, EmployerAccountInfo employerAccountInfo) {
        String employerNo = editDto.getEmployerNo();
        String mainstayNo = editDto.getMainstayNo();
        String channelNo = ChannelNoEnum.JOINPAY.name();
        //1.获取商户信息
        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到商户编号为[" + employerNo + "]的商户");
        }
        //2. 获取用工企业信息
        MerchantEmployerMain merchantEmployerMain = employerMainFacade.getByMchNo(employerNo);

        //3. 获取商户银行帐户表
        MerchantBankAccount merchantBankAccount = merchantBankAccountFacade.getByMchNo(employerNo);

        //4. 获取供应商服账户
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(mainstayNo, channelNo);

        //5. 调用bankLink接口报备
        ReportEntity reportEntity = new ReportEntity();
        BeanUtil.copyProperties(editDto, reportEntity);
        ReportReqVo reportReqVo = fillReportReqVo(reportEntity, mainstayChannelRelation, merchantEmployerMain, merchant,merchantBankAccount);
        //6.设置商户通道账号
        reportReqVo.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
        ReportResVo reportResVo = channelReportFacade.modify(reportReqVo);
        return handlerModifyResult(reportResVo, employerNo, mainstayNo);
    }

    private boolean handlerModifyResult(ReportResVo reportResVo, String employerNo, String mainstayNo) {
        boolean isOk = false;
        String serialNo = ReportBiz.MODIFY_PRE + employerNo + mainstayNo;
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getBySerialNo(serialNo);
        if (reportResVo.getApiReportStatus() == ApiReportStatusEnum.PROCESS.getValue()){
            reportChannelRecord.setStatus(ReportStatusEnum.PROCESSING.getValue());
            reportChannelRecord.setUpdateTime(new Date());
            isOk = true;
        } else if (reportResVo.getApiReportStatus() == ApiReportStatusEnum.SUCCESS.getValue()) {
            reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
            reportChannelRecord.setUpdateTime(new Date());
            isOk = true;
        } else {
            log.info("汇聚支付-受理失败，失败原因：{}" , reportResVo.getBizMsg());
            reportChannelRecord.setStatus(ReportStatusEnum.FAIL.getValue());
            reportChannelRecord.setErrMsg(reportResVo.getBizMsg());
            reportChannelRecord.setUpdateTime(new Date());
        }
        reportChannelRecordBiz.update(reportChannelRecord);
        return isOk;
    }
}
