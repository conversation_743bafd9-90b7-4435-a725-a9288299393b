package com.zhixianghui.service.common.core.biz.approvalflow.factory;

import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.service.common.core.biz.approvalflow.base.BaseFlow;
import com.zhixianghui.service.common.core.biz.approvalflow.flow.CreateMerchantFlow;
import com.zhixianghui.service.common.core.biz.approvalflow.flow.MerchantVerifyFlow;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 审批流程链工厂(简单工厂模式)
 * @date 2020-08-14 10:47
 **/
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FlowFactory {

    private final CreateMerchantFlow createMerchantFlow;
    private final MerchantVerifyFlow merchantVerifyFlow;

    /**
     * 获取流程链
     * @param flowName 流程名 {@link FlowTopicType}
     * @return 返回相应流程链
     */
    public BaseFlow getFlow(String flowName){
        if(flowName.equals(FlowTopicType.CREATE_MERCHANT.getName())){
            return createMerchantFlow;
        }else if(flowName.equals(FlowTopicType.MERCHANT_VERIFY.getName())){
            return merchantVerifyFlow;
        }else if(flowName.equals(FlowTopicType.CREATE_AGENT.getName())){
            //合伙人与商户的一致  复用
            return createMerchantFlow;
        }
        return null;
    }

}
