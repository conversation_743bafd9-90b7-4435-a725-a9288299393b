package com.zhixianghui.service.common.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import com.zhixianghui.service.common.core.dao.mapper.NotificationRecordMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
@Service
public class NotificationRecordService extends ServiceImpl<NotificationRecordMapper, NotificationRecord> {


    public int updateBatch(List<NotificationRecord> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<NotificationRecord> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<NotificationRecord> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(NotificationRecord record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(NotificationRecord record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    public IPage<NotificationRecord> listNotifications(Page<Map<String, Object>> page, Map<String, Object> params) {
        return baseMapper.listNotifications(page, params);
    }

}
