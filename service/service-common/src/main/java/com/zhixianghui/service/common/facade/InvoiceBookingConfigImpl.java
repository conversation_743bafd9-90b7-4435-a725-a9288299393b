package com.zhixianghui.service.common.facade;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.entity.config.InvoiceBookingConfig;
import com.zhixianghui.facade.common.service.InvoiceBookingConfigFacade;
import com.zhixianghui.service.common.core.service.InvoiceBookingConfigService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
public class InvoiceBookingConfigImpl implements InvoiceBookingConfigFacade {

    @Autowired
    private InvoiceBookingConfigService configService;

    @Override
    public InvoiceBookingConfig applyBookConfig(InvoiceBookingConfig bookingConfig) {

        final InvoiceBookingConfig oldConfig = configService.getOne(new QueryWrapper<InvoiceBookingConfig>()
                .eq(InvoiceBookingConfig.COL_EMPLOYER_NO, bookingConfig.getEmployerNo())
                .eq(InvoiceBookingConfig.COL_MAINSTAY_NO, bookingConfig.getMainstayNo())
                .eq(InvoiceBookingConfig.COL_STATUS, CommonStatusEnum.ACTIVE.getValue())
                .eq(InvoiceBookingConfig.COL_APPLY_DATE,bookingConfig.getApplyDate())
                .eq(InvoiceBookingConfig.COL_APPLY_PERIOD,bookingConfig.getApplyPeriod())
                .eq(InvoiceBookingConfig.COL_APPLY_TYPE,bookingConfig.getApplyType())
        );

        if (bookingConfig.getId() == null) {
            if (oldConfig != null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("已存在相同配置");
            }
            bookingConfig.setStatus(CommonStatusEnum.ACTIVE.getValue());
            configService.save(bookingConfig);
            return bookingConfig;
        }else {
            if (oldConfig != null && oldConfig.getId().longValue() != bookingConfig.getId().longValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("已存在相同配置");
            }
            final InvoiceBookingConfig config = configService.getById(bookingConfig.getId());
            if (config == null) {
                bookingConfig.setStatus(CommonStatusEnum.ACTIVE.getValue());
                configService.save(bookingConfig);
                return bookingConfig;
            }else {
                BeanUtil.copyProperties(bookingConfig,config);
                configService.updateById(config);
                return config;
            }
        }
    }

    @Override
    public List<InvoiceBookingConfig> listBookConfig(String employerNo) {
        final List<InvoiceBookingConfig> list = configService.list(new QueryWrapper<InvoiceBookingConfig>()
                .eq(InvoiceBookingConfig.COL_EMPLOYER_NO, employerNo)
                .eq(InvoiceBookingConfig.COL_STATUS, CommonStatusEnum.ACTIVE.getValue())
        );
        return list;
    }

    @Override
    public void deleteBookConfig(Long id) {
        configService.removeById(id);
    }
}
