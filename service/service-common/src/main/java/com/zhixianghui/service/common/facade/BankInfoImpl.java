package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.config.BankInfo;
import com.zhixianghui.facade.common.service.BankInfoFacade;
import com.zhixianghui.service.common.core.biz.config.BankInfoBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 银行信息表 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-31
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BankInfoImpl implements BankInfoFacade {
    private final BankInfoBiz biz;

    @Override
    public PageResult<List<BankInfo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap,pageParam);
    }

    @Override
    public void insert(BankInfo bankInfo) {
        biz.insert(bankInfo);

    }

    @Override
    public void del(Long id) {
        biz.del(id);
    }

    @Override
    public BankInfo edit(Long id) {
        return biz.edit(id);
    }

    @Override
    public void update(BankInfo bankInfo) {
        biz.update(bankInfo);
    }

    @Override
    public BankInfo getByBankChannelNo(String bankChannelNo) {
        return biz.getByBankChannelNo(bankChannelNo);
    }

    @Override
    public BankInfo getBankInfoById(Long id) {
        return biz.getBankInfoById(id);
    }
}
