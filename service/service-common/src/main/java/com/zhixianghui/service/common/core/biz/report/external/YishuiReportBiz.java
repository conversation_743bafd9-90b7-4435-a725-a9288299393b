package com.zhixianghui.service.common.core.biz.report.external;

import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.dto.ReportEditDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.common.core.biz.config.DataDictionaryBiz;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class YishuiReportBiz extends AbstractReportBiz{
    @Reference
    private NotifyFacade notifyFacade;
    @Autowired
    private DataDictionaryBiz dataDictionaryBiz;
    @Override
    protected void reportDetail(ReportEntity reportEntity) throws Exception {

        String syncToYishui = dataDictionaryBiz.getSystemConfig("syncToYishui");
        if (StringUtils.equals("true", syncToYishui)) {
            //供应商为易税，为商户创建易税账户
            notifyFacade.sendOne(MessageMsgDest.TOPIC_YISHUI,
                    reportEntity.getEmployerNo(),
                    reportEntity.getMainstayNo(),
                    NotifyTypeEnum.YISHUI_NOTIFY.getValue(),
                    MessageMsgDest.TAG_YISHUI_ADD_MERCHANT,
                    JSONUtil.toJsonStr(reportEntity));
        }
    }

    @Override
    protected boolean modify(ReportEditDto reportDto, EmployerAccountInfo employerAccountInfo) {
        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持修改报备信息");
    }
}
