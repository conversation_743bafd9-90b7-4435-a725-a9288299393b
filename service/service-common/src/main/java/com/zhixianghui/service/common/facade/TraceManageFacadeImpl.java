package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.dto.rmq.MsgDto;
import com.zhixianghui.facade.common.entity.rocketmqmanage.TraceEntity;
import com.zhixianghui.facade.common.service.TraceManageFacade;
import com.zhixianghui.service.common.core.biz.rocketmanage.MessageManageBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * Author: Cmf
 * Date: 2019.11.28
 * Time: 18:30
 * Description:
 */
@Service
public class TraceManageFacadeImpl implements TraceManageFacade {

    @Autowired
    private MessageManageBiz messageManageBiz;

    @Override
    public List<TraceEntity> listTraceEntityPage(Date timeBegin, Date timeEnd) {
        return messageManageBiz.listTraceEntity(timeBegin.getTime(), timeEnd.getTime());
    }

    @Override
    public TraceEntity getTraceDetail(String msgId) {
        return messageManageBiz.getTraceDetail(msgId);
    }

    @Override
    public MsgDto<?> getMessageContent(String topic, String msgId) {
        return messageManageBiz.getMessageContent(topic, msgId);

    }

    @Override
    public List<TraceEntity> listTraceEntityByTrxNo(String trxNo) {
        return messageManageBiz.listTraceEntityByTrxNo(trxNo);
    }
}
