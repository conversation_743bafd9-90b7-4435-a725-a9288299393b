package com.zhixianghui.service.common.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.common.dto.NotificationRecordDto;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.common.service.NotificationFacade;
import com.zhixianghui.facade.common.vo.NotificationDetailFullInfo;
import com.zhixianghui.service.common.core.biz.notification.NotificationBiz;
import com.zhixianghui.service.common.core.service.NotificationRecordDetailService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;


@Service
public class NotificationFacadeImpl implements NotificationFacade {

    @Autowired
    private NotificationBiz notificationBiz;

    @Autowired
    private NotificationRecordDetailService detailService;

    @Override
    public void createNotification(NotificationRecordDto notificationRecordDto) {
        this.notificationBiz.createNotification(notificationRecordDto);
    }

    @Override
    public IPage<NotificationDetailFullInfo> listNotificationRecordFullInfo(Page<Map<String, Object>> page, Map<String, Object> params) {
        return detailService.listNotificationRecordFullInfo(page, params);
    }

    @Override
    public IPage<NotificationRecord> listNotifications(Page<Map<String, Object>> page, Map<String, Object> params) {
        return notificationBiz.listNotifications(page, params);
    }

    @Override
    public NotificationRecord getNotificationInfoById(Long notificationId) throws BizException {
        return notificationBiz.getNotificationInfoById(notificationId);
    }

    @Override
    public void updateNotificationDetailReadStatus(NotificationRecordDetail notificationRecordDetail) {
        notificationBiz.updateNotificationDetail(notificationRecordDetail);
    }

    @Override
    public void updateNotification(NotificationRecord notificationRecord) {
        notificationBiz.updateNotification(notificationRecord);
    }
    @Override
    public NotificationRecordDetail getNotificationRecordDetailById(Long id) {
        return detailService.getById(id);
    }

    @Override
    public void deletNotificationByIds(List<Long> ids) throws BizException {
        notificationBiz.deleteNotificationByIds(ids);
    }
}
