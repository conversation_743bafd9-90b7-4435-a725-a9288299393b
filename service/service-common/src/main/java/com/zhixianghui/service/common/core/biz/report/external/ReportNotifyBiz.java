package com.zhixianghui.service.common.core.biz.report.external;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.report.ApiReportStatusEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.vo.report.AlipayAccountBookResVo;
import com.zhixianghui.facade.banklink.vo.report.AlipayReportResVo;
import com.zhixianghui.facade.banklink.vo.report.ReportReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.report.UploadPicReqVo;
import com.zhixianghui.facade.common.constants.ReportConstants;
import com.zhixianghui.facade.common.entity.report.*;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.service.MerchantFileFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.service.common.core.biz.report.*;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @description 报备回调
 * @date 2020-11-12 16:23
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ReportNotifyBiz {

    private final PayChannelBiz payChannelBiz;
    private final MainstayChannelRelationBiz mainstayChannelRelationBiz;
    private final EmployerAccountInfoBiz employerAccountInfoBiz;
    private final ReportChannelRecordBiz reportChannelRecordBiz;
    private final RedisLock redisLock;
    private final RedisClient redisClient;
    private final AliPayReportBiz aliPayReportBiz;
    private final ReportAccountHistoryBiz reportAccountHistoryBiz;
    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MerchantFileFacade merchantFileFacade;


    /**
     * 重复报备查询到报备记录
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void doRepeatReport(AlipayReportResVo alipayReportResVo) {
        String channelType = alipayReportResVo.getOutBizChannelType() == null ? "" : alipayReportResVo.getOutBizChannelType().toString();
        String serialNo = ChannelNoEnum.ALIPAY.name() + alipayReportResVo.getExternalAgreementNo() + channelType;
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getBySerialNo(serialNo);

        if (reportChannelRecord == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("报备记录为空");
        }

        //获取锁
        log.info("[签约环节: {}]==>获取签约锁");
        String lockKey = String.join(":", ReportConstants.REPORT_AUTH_KEY,alipayReportResVo.getExternalAgreementNo());
        String clientId = redisLock.tryLockLong(lockKey,0,1);
        if(clientId == null){
            log.info("[签约环节: {}]==>获取签约锁失败，直接丢弃");
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }

        try {
            //已查询到报备记录，回填数据
            alipayReportResVo.setAlipayUserId(alipayReportResVo.getPrincipalId());
            reportChannelRecord.setStatus(ReportStatusEnum.CASHBOOK.getValue());
            try {
                alipayNotifySuccess(alipayReportResVo);
            }catch (Exception e) {
                log.info("支付宝报备入库失败，需要人工入库");
                reportChannelRecord.setRespData(JSON.toJSONString(alipayReportResVo));
                reportChannelRecord.setErrMsg("已签约, 入库错误，需人工填入编号信息.");
            }
            //申请开通记账本
            accountbookCreate(alipayReportResVo,serialNo);
        }finally {
            //释放锁
            redisLock.unlockLong(clientId);
        }
    }

    /**
     * 延时消息查询报备情况
     */
    @Transactional(rollbackFor = Exception.class)
    public void getReportData(ReportEntity reportEntity) {
        String extAgreement = reportEntity.getExtAgreement();
        String serialNo = ChannelNoEnum.ALIPAY.name() + reportEntity.getExtAgreement() + reportEntity.getChannelType();
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getById(reportEntity.getRecordId());
        if (reportChannelRecord == null){
            log.error("报备记录不存在，可能重复报备，直接丢弃，报备信息：[{}]",JsonUtil.toString(reportEntity));
            return;
        }
        //只有签约中状态才是异步处理
        if (reportChannelRecord.getStatus() != ReportStatusEnum.CONTRACT.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("报备状态不为签约中，已执行后续操作,serialNo:"+ serialNo);
        }

        //获取锁
        log.info("[签约环节: {}]==>获取签约锁");
        String lockKey = String.join(":", ReportConstants.REPORT_AUTH_KEY,extAgreement);
        String clientId = redisLock.tryLockLong(lockKey,0,1);
        if(clientId == null){
            log.info("[签约环节: {}]==>获取签约锁失败，直接丢弃");
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }

        try{
            //查询签约状态
            String res = alipayFacade.agreementQuery(extAgreement,"");
            String resJson = JSONObject.parseObject(res).getJSONObject(ReportConstants.ALIPAY_USER_AGREEMENT_QUERY_RESPONSE).toJSONString();
            AlipayReportResVo alipayReportResVo = JsonUtil.toBean(resJson, AlipayReportResVo.class);
            if (alipayReportResVo.getCode().equals("10000") && alipayReportResVo.getPricipalType().equals("CARD")){
                //执行后续操作，回填报备数据
                alipayReportResVo.setAlipayUserId(alipayReportResVo.getPrincipalId());
                reportChannelRecord.setStatus(ReportStatusEnum.CASHBOOK.getValue());
                try {
                    alipayReportResVo.setOutBizChannelType(reportEntity.getChannelType());
                    alipayNotifySuccess(alipayReportResVo);
                }catch (Exception e) {
                    log.info("支付宝报备入库失败，需要人工入库");
                    reportChannelRecord.setRespData(JSON.toJSONString(alipayReportResVo));
                    reportChannelRecord.setErrMsg("已签约, 入库错误，需人工填入编号信息.");
                }
                //申请开通记账本
                accountbookCreate(alipayReportResVo,serialNo);
            }else{
                Long searchTimes = aliPayReportBiz.getSearchTimes(extAgreement);
                //默认重试次数为5次，超过5次则判断为签约失败
                if (searchTimes > ReportConstants.MAX_DELAY_TIMES){
                    reportChannelRecord.setStatus(ReportStatusEnum.FAIL.getValue());
                    reportChannelRecord.setErrMsg("5次延时查询仍未查到数据，系统默认签约失败，等候用户继续执行扫码签约");
                }else{
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REPORT_DELAY, NotifyTypeEnum.ALI_DELAY_SEARCH.getValue(),MessageMsgDest.TAG_ALIPAY_REPORT_DELAY,JSON.toJSONString(reportEntity), MsgDelayLevelEnum.M_30.getValue());
                }
                reportChannelRecord.setUpdateTime(new Date());
                reportChannelRecordBiz.update(reportChannelRecord);
            }
        }finally {
            //释放锁
            redisLock.unlockLong(clientId);
        }
    }

    /**
     * 支付宝协议签约成功后续处理
     * @param alipayReportResVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void notifySuccess(AlipayReportResVo alipayReportResVo) {
        //String channelType = alipayReportResVo.getOutBizChannelType() == null ? "" : alipayReportResVo.getOutBizChannelType().toString();
        log.info("成功收到回调结果[{}],根据结果回填", JSON.toJSONString(alipayReportResVo));
        //获取锁
        log.info("[签约环节: {}]==>获取签约锁");
        String lockKey = String.join(":", ReportConstants.REPORT_AUTH_KEY,alipayReportResVo.getExternalAgreementNo());
        String clientId = redisLock.tryLockLong(lockKey,0,1);
        if(clientId == null){
            log.info("[签约环节: {}]==>获取签约锁失败，直接丢弃");
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try{
            alipayNotifySuccess(alipayReportResVo);
            //正常签约完成要找出签约中的报备记录，生成记账本
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("merchantType",alipayReportResVo.getMerchantType());
            paramMap.put("mainstayNo",alipayReportResVo.getMainStayNo());
            paramMap.put("employerNo",alipayReportResVo.getMerchantNo());
            paramMap.put("payChannelNo",ChannelNoEnum.ALIPAY.name());
            List<ReportChannelRecord> reportChannelRecordList = reportChannelRecordBiz.listBy(paramMap);
            for (ReportChannelRecord reportChannelRecord : reportChannelRecordList){
                //判断签约状态是否为成功，成功则不需要继续处理
                if (!reportChannelRecord.getStatus().equals(ReportStatusEnum.CONTRACT.getValue())){
                    continue;
                    //throw CommonExceptions.PARAM_INVALID.newWithErrMsg("报备状态不为签约中");
                }
                //签约成功才会返回
                reportChannelRecord.setStatus(ReportStatusEnum.CASHBOOK.getValue());
                reportChannelRecord.setUpdateTime(new Date());
                reportChannelRecordBiz.update(reportChannelRecord);
                try {
                    alipayReportResVo.setOutBizChannelType(reportChannelRecord.getChannelType());
                    //申请开通记账本
                    accountbookCreate(alipayReportResVo,reportChannelRecord.getSerialNo());
                }catch (Exception e) {
                    log.info("支付宝报备入库失败，需要人工入库，返回信息：{}",JSON.toJSONString(alipayReportResVo));
                }
            }
        }finally {
            //释放锁
            redisLock.unlockLong(clientId);
        }
    }

    /**
     * 记账本开通
     * @param alipayReportResVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void accountbookCreate(AlipayReportResVo alipayReportResVo,String serialNo) {
        AlipayAccountBookResVo alipayAccountBookResVo = getAccountBook(alipayReportResVo);
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getBySerialNo(serialNo);
        //记账本开通成功
        //返回10000为开通成功
        if (alipayAccountBookResVo.getCode().equals("10000")){
            try {
                reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
                //发送延时消息订阅支付宝入金
                subcribe(alipayReportResVo.getAgreementNo(),alipayAccountBookResVo.getAccountBookId());
                insertAccountInfoData(alipayReportResVo,alipayAccountBookResVo);
            }catch (Exception e){
                log.info("支付宝记账本信息入库失败，已开通记账本，需要人工入库");
                reportChannelRecord.setRespData(JSON.toJSONString(alipayReportResVo));
                reportChannelRecord.setErrMsg("已开通记账本, 入库错误，需人工填入编号信息");
                reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
            }
        }else{
            String msg = JSON.toJSONString(alipayAccountBookResVo);
            log.info("支付宝记账本创建失败，返回信息：{}",msg);
            reportChannelRecord.setRespData(msg);
            reportChannelRecord.setErrMsg("支付宝记账本创建失败，返回信息：" + msg);
            reportChannelRecord.setStatus(ReportStatusEnum.FAIL.getValue());
        }
        reportChannelRecordBiz.update(reportChannelRecord);
    }

    private void subcribe(String agreementNo, String accountBookId) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("agreementNo",agreementNo);
        paramMap.put("accountBookId",accountBookId);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_SUBCRIBE,
                NotifyTypeEnum.ALI_DELAY_SEARCH.getValue(),
                MessageMsgDest.TAG_ALIPAY_SUBCRIBE,
                JSON.toJSONString(paramMap),
                MsgDelayLevelEnum.S_10.getValue());
    }

    public void subcribe(Map<String, Object> paramMap) throws Exception {
        String agreementNo = (String) paramMap.get("agreementNo");
        String accountBookId = (String) paramMap.get("accountBookId");
        alipayFacade.subscribe(agreementNo,accountBookId);
    }

    /**
     * 处理记账本数据
     * @param alipayReportResVo
     */
    @Transactional(rollbackFor = Exception.class)
    void insertAccountInfoData(AlipayReportResVo alipayReportResVo, AlipayAccountBookResVo account) {
        if (alipayReportResVo.getMerchantType() == 100){
            //查询父记账本id
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(alipayReportResVo.getMainStayNo(),ChannelNoEnum.ALIPAY.name());

            //查询通道
            PayChannel payChannel = payChannelBiz.getByChannelNo(ChannelNoEnum.ALIPAY.name());

            //用工企业申请开通记账本
            EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(
                    alipayReportResVo.getMerchantNo(),alipayReportResVo.getMainStayNo(),alipayReportResVo.getOutBizChannelType());
            employerAccountInfo.setUpdateTime(new Date());
            //记账本id
            employerAccountInfo.setSubMerchantNo(account.getAccountBookId());
            //外标卡号
            employerAccountInfo.setAlipayCardNo(account.getExtCardInfo().getCardNo());
            employerAccountInfo.setPayChannelName(payChannel.getPayChannelName());
            employerAccountInfo.setPayChannelNo(ChannelNoEnum.ALIPAY.name());
            //回填
            employerAccountInfo.setParentMerchantNo(mainstayChannelRelation.getChannelMchNo());


            //回调时上游关系都为开启才能开启
            if(payChannel.getStatus().equals(OpenOffEnum.OPEN.getValue())
                    && mainstayChannelRelation.getStatus().equals(OpenOffEnum.OPEN.getValue())){
                employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
            }else{
                employerAccountInfo.setStatus(OpenOffEnum.OFF.getValue());
            }


            employerAccountInfoBiz.update(employerAccountInfo);
        }else{

            //查询通道
            PayChannel payChannel = payChannelBiz.getByChannelNo(ChannelNoEnum.ALIPAY.name());
            //供应商申请开通记账本
            Map<String,Object> paramMap = Maps.newHashMap();
            paramMap.put("mainstayNo",alipayReportResVo.getMainStayNo());
            paramMap.put("channelType",ChannelTypeEnum.ALIPAY.getValue());
            List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoBiz.listBy(paramMap);
            employerAccountInfoList.stream().forEach(
                    employerAccountInfo -> {
                        employerAccountInfo.setUpdateTime(new Date());
                        employerAccountInfo.setParentMerchantNo(account.getAccountBookId());
                        employerAccountInfo.setPayChannelName(payChannel.getPayChannelName());
                        employerAccountInfo.setPayChannelNo(ChannelNoEnum.ALIPAY.name());
                    });
            employerAccountInfoBiz.batchUpdate(employerAccountInfoList);
            //更新供应商通道编号表
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(alipayReportResVo.getExternalAgreementNo(),ChannelNoEnum.ALIPAY.name());
            mainstayChannelRelation.setChannelMchNo(account.getAccountBookId());
            mainstayChannelRelation.setAlipayCardNo(account.getExtCardInfo().getCardNo());

            //回调时上游关系都为开启才能开启
            if(payChannel.getStatus().equals(OpenOffEnum.OPEN.getValue())){
                mainstayChannelRelation.setStatus(OpenOffEnum.OPEN.getValue());
            }else{
                mainstayChannelRelation.setStatus(OpenOffEnum.OFF.getValue());
            }

            mainstayChannelRelationBiz.update(mainstayChannelRelation);
        }
    }

    /**
     * 成功后操作
     * @param
     * @param alipayReportResVo
     */
    @Transactional(rollbackFor = Exception.class)
    void alipayNotifySuccess(AlipayReportResVo alipayReportResVo) {
        String extAgreementNo =alipayReportResVo.getExternalAgreementNo();
        char mchType = alipayReportResVo.getExternalAgreementNo().charAt(0);
        if (mchType == 'M'){
            //用工企业-供应商报备
            alipayReportResVo.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());

            int sIndex = extAgreementNo.indexOf("S");
            String mchNo = extAgreementNo.substring(0,sIndex);
            String mainStayNo = extAgreementNo.substring(sIndex);

            alipayReportResVo.setMainStayNo(mainStayNo);
            alipayReportResVo.setMerchantNo(mchNo);
            //根据供应商编号查询是否有父商户签约号
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(mainStayNo,ChannelNoEnum.ALIPAY.name());

            //查询通道
            PayChannel payChannel = payChannelBiz.getByChannelNo(ChannelNoEnum.ALIPAY.name());

            //查询出对应类型，先更新通道名称
            EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(mchNo,mainStayNo,alipayReportResVo.getOutBizChannelType());
            employerAccountInfo.setPayChannelNo(ChannelNoEnum.ALIPAY.name());
            employerAccountInfo.setPayChannelName(payChannel.getPayChannelName());
            employerAccountInfoBiz.update(employerAccountInfo);

            Map<String,Object> paramsMap = new HashMap<>();
            paramsMap.put("employerNo",mchNo);
            paramsMap.put("mainstayNo",mainStayNo);
            paramsMap.put("payChannelNo",ChannelNoEnum.ALIPAY.name());
            paramsMap.put("channelType",employerAccountInfo.getChannelType());
            List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoBiz.listBy(paramsMap);
            employerAccountInfoList.stream().forEach(x->{
                //回填数据
                x.setParentAlipayUserId(mainstayChannelRelation.getAlipayUserId());
                x.setParentAgreementNo(mainstayChannelRelation.getAgreementNo());

                x.setSubAgreementNo(alipayReportResVo.getAgreementNo());
                x.setSubAlipayUserId(alipayReportResVo.getAlipayUserId());
                x.setUpdateTime(new Date());
            });

            employerAccountInfoBiz.updateList(employerAccountInfoList);
        }else{
            alipayReportResVo.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            alipayReportResVo.setMainStayNo(alipayReportResVo.getExternalAgreementNo());
            //供应商报备
            Map<String,Object> paramMap = Maps.newHashMap();
            paramMap.put("mainstayNo",extAgreementNo);
            paramMap.put("payChannelNo", ChannelNoEnum.ALIPAY.name());
            List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoBiz.listBy(paramMap);
            employerAccountInfoList.stream().
                    forEach(employerAccountInfo -> {
                        employerAccountInfo.setUpdateTime(new Date());
                        employerAccountInfo.setParentAgreementNo(alipayReportResVo.getAgreementNo());
                        employerAccountInfo.setParentAlipayUserId(alipayReportResVo.getAlipayUserId());
                    });
            employerAccountInfoBiz.batchUpdate(employerAccountInfoList);

            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(alipayReportResVo.getExternalAgreementNo(),ChannelNoEnum.ALIPAY.name());
            mainstayChannelRelation.setAgreementNo(alipayReportResVo.getAgreementNo());
            mainstayChannelRelation.setAlipayUserId(alipayReportResVo.getAlipayUserId());
            mainstayChannelRelationBiz.update(mainstayChannelRelation);
        }
    }

    public void notifySuccess(ReportReceiveRespVo receiveRespVo) {
        log.info("成功收到回调结果[{}],根据结果回填", JSON.toJSONString(receiveRespVo));
        Integer reportStatus = receiveRespVo.getApiReportStatus();
        if(StringUtils.isEmpty(receiveRespVo.getMchNo())){
            log.info("回调结果子商户号为空, 报备回调终止");
            return;
        }
        //分账方编号作为记录标识
        String serialNo = ChannelNoEnum.JOINPAY.name() + receiveRespVo.getMchNo();
        ReportChannelRecord reportChannelRecord = reportChannelRecordBiz.getBySerialNo(serialNo);
        if(reportStatus.equals(ApiReportStatusEnum.SUCCESS.getValue())){
            //更新用工企业信息和报备记录
            try{
                doNotifySuccess(reportChannelRecord,receiveRespVo);
                //报备完成后上传图片
                uploadPic(reportChannelRecord.getEmployerNo(),reportChannelRecord.getMainstayNo());
            }catch (Exception e){
                log.error("汇聚报备回调激活成功，存库异常 记录回调信息与问题，需人工存入",e);
                reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
                reportChannelRecord.setRespData(JSON.toJSONString(receiveRespVo));
                reportChannelRecord.setErrMsg("已激活, 入库错误，需人工填入编号信息.错误原因：" + e.getMessage());
                reportChannelRecord.setUpdateTime(new Date());
                reportChannelRecordBiz.update(reportChannelRecord);
            }
        }else {
            failHandle(reportChannelRecord,receiveRespVo,reportStatus);
            reportChannelRecordBiz.update(reportChannelRecord);
        }
    }

    public void uploadPic(String employerNo, String mainstayNo) {
        List<MerchantFile> merchantFiles = merchantFileFacade.listByMchNo(employerNo);
        EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelNo(
                employerNo,mainstayNo,ChannelNoEnum.JOINPAY.name());
        if (merchantFiles.size() > 0){
            UploadPicReqVo uploadPicReqVo = buildUploadPic(merchantFiles,employerAccountInfo);
            notifyFacade.sendOne(MessageMsgDest.TOPIC_REPORT_UPLOAD,uploadPicReqVo.getEmployerNo(), DateUtil.formatCompactDate(new Date())+uploadPicReqVo.getEmployerNo(),
                    NotifyTypeEnum.REPORT_RECEIVE.getValue(), MessageMsgDest.TAG_REPORT_UPLOAD, JsonUtil.toString(uploadPicReqVo));
        }
    }

    /**
     * 构建图片上传接口参数
     * @param merchantFiles
     * @return
     */
    private UploadPicReqVo buildUploadPic(List<MerchantFile> merchantFiles,EmployerAccountInfo employerAccountInfo) {
        UploadPicReqVo uploadPicReqVo = new UploadPicReqVo();
        uploadPicReqVo.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
        uploadPicReqVo.setMainstayNo(employerAccountInfo.getMainstayNo());
        uploadPicReqVo.setEmployerNo(employerAccountInfo.getEmployerNo());
        uploadPicReqVo.setChannelNo(ChannelNoEnum.JOINPAY.name());
        uploadPicReqVo.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
        for (MerchantFile merchantFile : merchantFiles) {

            //营业执照
            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.BUSINESS_LICENSE.getValue()){
                uploadPicReqVo.setLicense(merchantFile.getFileUrl());
                continue;
            }

            //身份证
            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.ID_CARD_HEAD.getValue()){
                uploadPicReqVo.setIdCardBack(merchantFile.getFileUrl());
                continue;
            }

            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue()){
                uploadPicReqVo.setIdCardFront(merchantFile.getFileUrl());
                continue;
            }

            //身份证
            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.ID_CARD_COPY.getValue()){
                uploadPicReqVo.setIdCardFront(merchantFile.getFileUrl());
                uploadPicReqVo.setIdCardBack(merchantFile.getFileUrl());
                continue;
            }

            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.DOOR_PHOTO.getValue()){
                uploadPicReqVo.setOther1(merchantFile.getFileUrl());
                continue;
            }

            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.WORK_INDOOR.getValue()){
                uploadPicReqVo.setOther2(merchantFile.getFileUrl());
            }
        }
        return uploadPicReqVo;
    }

    @Transactional(rollbackFor = Exception.class)
    void doNotifySuccess(ReportChannelRecord reportChannelRecord, ReportReceiveRespVo receiveRespVo) {
        EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(reportChannelRecord.getEmployerNo(), reportChannelRecord.getMainstayNo(), reportChannelRecord.getChannelType());
        if(employerAccountInfo == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("报备回调,找不到用工企业账户对应通道信息");
        }

        PayChannel payChannel = payChannelBiz.getByChannelNo(reportChannelRecord.getPayChannelNo());
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(employerAccountInfo.getMainstayNo(),reportChannelRecord.getPayChannelNo());
        //回调时上游关系都为开启才能开启
        if(payChannel.getStatus().equals(OpenOffEnum.OPEN.getValue())
                && mainstayChannelRelation.getStatus().equals(OpenOffEnum.OPEN.getValue())){
            employerAccountInfo.setStatus(OpenOffEnum.OPEN.getValue());
        }else{
            employerAccountInfo.setStatus(OpenOffEnum.OFF.getValue());
        }

        employerAccountInfo.setPayChannelName(reportChannelRecord.getPayChannelName());
        employerAccountInfo.setPayChannelNo(reportChannelRecord.getPayChannelNo());
        employerAccountInfo.setSubMerchantNo(receiveRespVo.getMchNo());
        employerAccountInfo.setParentMerchantNo(receiveRespVo.getParentMchNo());
        employerAccountInfo.setUpdateTime(new Date());
        employerAccountInfo.setUpdateOperator(reportChannelRecord.getReporter());
        employerAccountInfo.setSubAlipayUserId("");
        employerAccountInfo.setSubAgreementNo("");

        //插入到历史账号记录表
        ReportAccountHistory reportAccountHistory = new ReportAccountHistory();
        reportAccountHistory.setEmployerNo(employerAccountInfo.getEmployerNo());
        reportAccountHistory.setMainstayNo(employerAccountInfo.getMainstayNo());
        reportAccountHistory.setPayChannelType(employerAccountInfo.getChannelType());
        reportAccountHistory.setPayChannelName(employerAccountInfo.getPayChannelName());
        reportAccountHistory.setPayChannelNo(employerAccountInfo.getPayChannelNo());
        //固定只有用工企业报备
        reportAccountHistory.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        reportAccountHistory.setChannelMerchantNo(employerAccountInfo.getSubMerchantNo());
        reportAccountHistory.setAgreementNo("");
        reportAccountHistory.setAlipayUserId("");
        reportAccountHistory.setIsShow(YesNoCodeEnum.YES.getValue());
        reportAccountHistory.setTitle(ChannelNoEnum.JOINPAY.getDesc());
        reportAccountHistory.setEmployerName(employerAccountInfo.getMchName());
        reportAccountHistoryBiz.create(reportAccountHistory);

        //记录
        reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
        reportChannelRecord.setUpdateTime(new Date());
        employerAccountInfoBiz.update(employerAccountInfo);
        reportChannelRecordBiz.update(reportChannelRecord);
    }

    /**
     * 查询记账本
     */
    private AlipayAccountBookResVo getAccountBook(AlipayReportResVo alipayReportResVo){
        //为了避免已有的记账本被覆盖
        //查询本地是否存在记账本数据
        String accountBookId = "";
        String mchName = "";
        if (alipayReportResVo.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getByEmployerNoAndMainstayNoAndChannelType(alipayReportResVo.getMerchantNo(),alipayReportResVo.getMainStayNo(),alipayReportResVo.getOutBizChannelType());
            accountBookId = employerAccountInfo.getSubMerchantNo();
            mchName = employerAccountInfo.getMchName();
        }else{
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(alipayReportResVo.getMainStayNo(),ChannelNoEnum.ALIPAY.name());
            accountBookId = mainstayChannelRelation.getChannelMchNo();
        }

        //查询记账本是否存在
        if (StringUtils.isNotBlank(accountBookId)){
            String msg = alipayFacade.accountBookQuery(alipayReportResVo.getAgreementNo(),accountBookId);
            String resJson = JSONObject.parseObject(msg).getJSONObject(ReportConstants.ALIPAY_FUND_ACCOUNTBOOK_QUERY_RESPONSE).toJSONString();
            AlipayAccountBookResVo alipayAccountBookResVo = JsonUtil.toBean(resJson, AlipayAccountBookResVo.class);
            if (alipayAccountBookResVo.getCode().equals("10000")){
                return alipayAccountBookResVo;
            }
        }

        //存在不同通道类型，需要加上类型
        //为了避免覆盖生产已存在的记账本，当通道类型为支付宝类型的时候不需要加上类型后缀
        accountBookId = alipayReportResVo.getExternalAgreementNo() +
                ((alipayReportResVo.getOutBizChannelType() == null || alipayReportResVo.getOutBizChannelType() == ChannelTypeEnum.ALIPAY.getValue()) ? "" : alipayReportResVo.getOutBizChannelType().toString());
        //记账本不存在，创建记账本
        String msg = alipayFacade.accountBookCreate(alipayReportResVo.getAgreementNo(),accountBookId);
        String resJson = JSONObject.parseObject(msg).getJSONObject(ReportConstants.ALIPAY_FUND_ACCOUNTBOOK_CREATE_RESPONSE).toJSONString();
        AlipayAccountBookResVo alipayAccountBookResVo = JsonUtil.toBean(resJson, AlipayAccountBookResVo.class);
        if (alipayAccountBookResVo.getCode().equals("10000")){
            //记录记账本记录
            recordAccount(alipayReportResVo,alipayAccountBookResVo,mchName);
        }
        return alipayAccountBookResVo;
    }

    /**
     * 记录记账本
     * @param alipayReportResVo
     * @param alipayAccountBookResVo
     */
    private void recordAccount(AlipayReportResVo alipayReportResVo, AlipayAccountBookResVo alipayAccountBookResVo,String mchName) {
        ReportAccountHistory reportAccountHistory = new ReportAccountHistory();
        reportAccountHistory.setMerchantType(alipayReportResVo.getMerchantType());
        reportAccountHistory.setEmployerNo(alipayReportResVo.getMerchantNo());
        reportAccountHistory.setMainstayNo(alipayReportResVo.getMainStayNo());
        reportAccountHistory.setPayChannelNo(ChannelNoEnum.ALIPAY.name());
        reportAccountHistory.setPayChannelName(ChannelNoEnum.ALIPAY.getDesc());
        reportAccountHistory.setPayChannelType(alipayReportResVo.getOutBizChannelType());
        reportAccountHistory.setChannelMerchantNo(alipayAccountBookResVo.getAccountBookId());
        reportAccountHistory.setAgreementNo(alipayReportResVo.getAgreementNo());
        reportAccountHistory.setAlipayUserId(alipayReportResVo.getAlipayUserId());
        reportAccountHistory.setAlipayCardNo(alipayAccountBookResVo.getExtCardInfo().getCardNo());

        if (reportAccountHistory.getMerchantType().intValue() == MerchantTypeEnum.EMPLOYER.getValue()){
            reportAccountHistory.setEmployerName(mchName);
        }
        reportAccountHistory.setTitle(ChannelNoEnum.ALIPAY.getDesc());
        reportAccountHistory.setIsShow(YesNoCodeEnum.YES.getValue());
        reportAccountHistoryBiz.create(reportAccountHistory);
    }

    /**
     * 信息修改回调
     * @param receiveRespVo
     */
    public void motifySuccess(ReportReceiveRespVo receiveRespVo) {
        Integer reportStatus = receiveRespVo.getApiReportStatus();
        String mchNo = receiveRespVo.getMchNo();
        EmployerAccountInfo employerAccountInfo = employerAccountInfoBiz.getOneBySubMerchantNoAndPayChannelNo(mchNo,ChannelNoEnum.JOINPAY.name());
        if (employerAccountInfo == null){
            log.error("汇聚分账方信息修改，用工企业支付账号不存在，分账方编号：[{}]，返回信息：[{}]",mchNo,JsonUtil.toString(receiveRespVo));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业支付账号不存在");
        }

        ReportChannelRecord reportChannelRecord =
                reportChannelRecordBiz.getBySerialNo(ReportBiz.MODIFY_PRE + employerAccountInfo.getEmployerNo() + employerAccountInfo.getMainstayNo());

        if(reportStatus.intValue() == ApiReportStatusEnum.SUCCESS.getValue()){
            reportChannelRecord.setStatus(ReportStatusEnum.SUCCESS.getValue());
            reportChannelRecord.setUpdateTime(new Date());
        }else{
            failHandle(reportChannelRecord,receiveRespVo,reportStatus);
        }
        reportChannelRecordBiz.update(reportChannelRecord);
    }

    private void failHandle(ReportChannelRecord reportChannelRecord, ReportReceiveRespVo receiveRespVo ,Integer reportStatus) {
        if(reportStatus.equals(ApiReportStatusEnum.FAIL.getValue()) || reportStatus.equals(ApiReportStatusEnum.UN_KNOW.getValue())){
            //认证失败、未知
            reportChannelRecord.setStatus(ReportStatusEnum.FAIL.getValue());
        }else if(reportStatus.equals(ApiReportStatusEnum.PROCESS.getValue())){
            //未认证或认证中
            reportChannelRecord.setStatus(ReportStatusEnum.PROCESSING.getValue());
        }
        reportChannelRecord.setRespData(JSON.toJSONString(receiveRespVo));
        reportChannelRecord.setErrMsg(receiveRespVo.getBizMsg());
        reportChannelRecord.setUpdateTime(new Date());
    }
}
