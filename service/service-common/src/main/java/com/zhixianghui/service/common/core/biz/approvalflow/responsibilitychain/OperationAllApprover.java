package com.zhixianghui.service.common.core.biz.approvalflow.responsibilitychain;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.CommonBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 运营平台通用审批节点（部门所有人）
 * @date 2020-08-12 14:36
 **/
@Slf4j
@RequiredArgsConstructor
public class OperationAllApprover extends Approver{

    private final int stepNum;
    private final int platformSource;
    private final int handlerType;
    private final List<String> departmentNumbers;

    @Autowired
    private CommonBiz commonBiz;

    @Override
    public ApprovalInfoVo handleFlow(ApprovalFlow approvalFlow) {
        Long flowId = approvalFlow.getId();
        if(approvalFlow.getStepNum().equals(stepNum)){
            if(!ObjectUtils.isEmpty(commonBiz.listByFlowIdAndStepNumAndIsHistory(flowId, stepNum,false))){
                //返回等审批
                return ApprovalInfoVo.builder().id(approvalFlow.getId()).isLast(false).build();
            }
            log.info("审批流程id:{},生成步骤stepNum:{}",flowId,stepNum);
            commonBiz.createOperationFlowAllDetails(flowId,stepNum,
                    departmentNumbers,handlerType, platformSource);
            return ApprovalInfoVo.builder().id(approvalFlow.getId()).isLast(false).build();
        }else{
            return this.nextHandle(approvalFlow);
        }
    }
}
