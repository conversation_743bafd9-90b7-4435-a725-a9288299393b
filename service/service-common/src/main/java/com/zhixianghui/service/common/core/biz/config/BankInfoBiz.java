package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.config.BankInfo;
import com.zhixianghui.service.common.core.dao.BankInfoDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <p>
* 银行信息表Biz
* </p>
*
* <AUTHOR>
* @version 创建时间： 2020-08-31
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BankInfoBiz {

    private final BankInfoDao bankInfoDao;

    public PageResult<List<BankInfo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return bankInfoDao.listPage(paramMap,pageParam);
    }

    public void insert(BankInfo bankInfo) {
        bankInfoDao.insert(bankInfo);
    }

    public void del(Long id) {
        bankInfoDao.deleteById(id);
    }

    public BankInfo edit(Long id) {
        return bankInfoDao.getById(id);
    }

    public void update(BankInfo bankInfo) {
        bankInfoDao.update(bankInfo);
    }

    public BankInfo getByBankChannelNo(String bankChannelNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("bankChannelNo",bankChannelNo);
        return bankInfoDao.getOne(paramMap);
    }

    public BankInfo getBankInfoById(Long id) {
        return bankInfoDao.getById(id);
    }
}
