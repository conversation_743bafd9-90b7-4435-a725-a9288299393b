package com.zhixianghui.service.common.core.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.common.entity.tenant.TenantManage;
import com.zhixianghui.facade.common.vo.TenantVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
@Mapper
public interface TenantManageMapper extends BaseMapper<TenantManage> {

    Page<TenantVo> selectTenantPage(Page page, @Param("param") Map<String, Object> paramMap);

    Map<String,Object> selectByWebsite(@Param("uri") String uri,@Param("status")int status);
}
