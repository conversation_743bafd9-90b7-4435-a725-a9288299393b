/*
 * Powered By [joinPay.com]
 */
package com.zhixianghui.service.common.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.IndustryType;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.Map;

/**
 * 行业类型
 * <AUTHOR>
 * @date 2020-08-10
 */
@Repository
public class IndustryTypeDao extends MyBatisDao<IndustryType, Long> {


    public IndustryType getByCode(String industryTypeCode) {
        if (StringUtil.isEmpty(industryTypeCode)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("categoryCode不能为空");
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("industryTypeCode", industryTypeCode);
        return super.getOne(paramMap);
    }
}
