package com.zhixianghui.service.common.service;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.SequenceExceptions;
import com.zhixianghui.middleware.leaf.IDGen;
import com.zhixianghui.middleware.leaf.common.Result;
import com.zhixianghui.middleware.leaf.snowflake.SnowflakeIDGenImpl;
import com.zhixianghui.service.common.config.LeafProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service("SnowflakeService")
public class SnowflakeService {
    private Logger logger = LoggerFactory.getLogger(SnowflakeService.class);
    IDGen idGen;

    @Autowired
    LeafProperties leafProperties;

    @PostConstruct
    public void init() throws BizException {
        boolean flag = leafProperties.getSnowflake().getEnable();
        if (flag) {
            String zkAddress = leafProperties.getSnowflake().getZkAddress();
            int port = leafProperties.getSnowflake().getZkPort();
            String leafName = leafProperties.getSnowflake().getName();
            idGen = new SnowflakeIDGenImpl(zkAddress, port, leafName);
            if (idGen.init()) {
                logger.info("Snowflake Service Init Successfully");
            } else {
                throw SequenceExceptions.SEQUENCE_COMMON_EXCEPTION.newWithErrMsg("Snowflake Service Init Fail");
            }
        } else {
            idGen = null;
            logger.warn("snowflake模式未启用，将无法通过该模式生成序列号");
        }
    }

    public Result getId() {
        return idGen.get("key");
    }
}
