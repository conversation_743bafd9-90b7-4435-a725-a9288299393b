package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.config.WorkCategory;
import com.zhixianghui.facade.common.service.WorkCategoryFacade;
import com.zhixianghui.service.common.core.biz.config.WorkCategoryBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 工作类目
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
public class WorkCategoryFacadeImpl implements WorkCategoryFacade {
    @Autowired
    private WorkCategoryBiz workCategoryBiz;

    @Override
    public void insert(WorkCategory workCategory) {
        workCategoryBiz.insert(workCategory);
    }

    @Override
    public void update(WorkCategory workCategory) {
        workCategoryBiz.update(workCategory);
    }

    @Override
    public void delete(long id) {
        workCategoryBiz.delete(id);
    }

    @Override
    public WorkCategory getByCategoryCode(String categoryCode) {
        return workCategoryBiz.getByCategoryCode(categoryCode);
    }

    @Override
    public List<WorkCategory> listSubCategory(Long parentId) {
        return workCategoryBiz.listSubCategory(parentId);
    }

    @Override
    public List<WorkCategory> listAll() {
        return workCategoryBiz.listAll();
    }

}
