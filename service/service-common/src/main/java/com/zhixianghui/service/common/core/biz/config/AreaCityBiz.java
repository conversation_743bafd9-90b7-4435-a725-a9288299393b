package com.zhixianghui.service.common.core.biz.config;

import cn.hutool.core.map.MapUtil;
import com.zhixianghui.facade.common.entity.config.AreaCity;
import com.zhixianghui.service.common.core.dao.AreaCityDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <p>
* 地区管理--地市表Biz
* </p>
*
* <AUTHOR>
* @version 创建时间： 2020-08-31
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AreaCityBiz {

    private final AreaCityDao areaCityDao;

    public List<AreaCity> listBy(Map<String, Object> paramMap) {
        return areaCityDao.listBy(paramMap);
    }

    public AreaCity getByCityNo(String cityNo) {
        return areaCityDao.getOne(Collections.singletonMap("cityNo", cityNo));
    }

    public AreaCity getByCityName(String proviceNo,String cityName) {
        return areaCityDao.getOne(MapUtil.builder(new HashMap<String, Object>()).put("provinceNo",proviceNo)
        .put("cityName",cityName).build());
    }

    public List<AreaCity> listAll() {
        return areaCityDao.listAll();
    }
}
