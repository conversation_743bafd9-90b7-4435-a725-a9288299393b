package com.zhixianghui.service.common.core.biz.black;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.service.utils.MybatisUtil;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.black.Blacklist;
import com.zhixianghui.facade.common.vo.BlackListVo;
import com.zhixianghui.service.common.core.dao.mapper.BlacklistMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.*;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-03-01
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BlacklistBiz{

    private final BlacklistMapper blacklistMapper;

    @Cacheable(value = "blackCache", key = "targetClass + ':' + methodName + ':' + #tag + ':' + #subjectNo")
    public Blacklist getBlacklist(String tag, String subjectNo) {
        return blacklistMapper.selectOne(new QueryWrapper<Blacklist>().lambda()
                .eq(Blacklist::getSubjectNo,subjectNo)
                .eq(Blacklist::getTag,tag));
    }

    public void add(BlackListVo blackListVo, String realName) {
        Blacklist blacklist = new Blacklist();
        BeanUtil.copyProperties(blackListVo,blacklist);
        blacklist.setCreateTime(new Date());
        blacklist.setCreateBy(realName);
        blacklistMapper.insert(blacklist);
    }

    public void deleteById(Long id) {
        blacklistMapper.deleteById(id);
    }

    public void update(Blacklist blacklist) {
        blacklistMapper.updateById(blacklist);
    }

    public PageResult<List<Blacklist>> listPage(Blacklist blacklist, int pageSize, int pageCurrent) {
        Page page = new Page<>();
        page.setSize(pageSize);
        page.setCurrent(pageCurrent);
        QueryWrapper<Blacklist> queryWrapper = new QueryWrapper<>();
        Map<String,Object> paramMap = new HashMap<>();
        MybatisUtil.buildWhere(queryWrapper,Blacklist.class,paramMap);
        Page<Blacklist> blacklistPage = blacklistMapper.selectPage(page,queryWrapper);
        return PageResult.newInstance(blacklistPage.getRecords(), pageCurrent, pageSize, blacklistPage.getTotal());
    }
}