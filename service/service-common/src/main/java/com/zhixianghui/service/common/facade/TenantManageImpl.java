package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.tenant.TenantManage;
import com.zhixianghui.facade.common.service.TenantManageFacade;
import com.zhixianghui.facade.common.vo.TenantVo;
import com.zhixianghui.service.common.core.biz.tenant.TenantManageBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-03-24
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TenantManageImpl implements TenantManageFacade {

    private final TenantManageBiz biz;

    @Override
    public void add(TenantVo tenantVo, String realName) {
        biz.addTenant(tenantVo,realName);
    }

    @Override
    public void delete(Long id) {
        biz.delete(id);
    }

    @Override
    public void update(TenantVo tenantVo, String realName) {
        biz.update(tenantVo,realName);
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        biz.updateStatus(id,status);
    }

    @Override
    public PageResult<List<TenantVo>> selectPage(TenantManage tenantManage, int pageSize, int pageCurrent) {
        return biz.selectPage(tenantManage,pageSize,pageCurrent);
    }

    @Override
    public Map<String, Object> getTenant(String uri) {
        return biz.getTenantLogo(uri);
    }

    @Override
    public TenantManage getTenantByTenantNo(String tenantNo) {
        return biz.getTenantByTenantNo(tenantNo);
    }
}
