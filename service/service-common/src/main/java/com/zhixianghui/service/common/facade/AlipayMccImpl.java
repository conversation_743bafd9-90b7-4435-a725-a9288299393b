package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.service.AlipayMccFacade;
import com.zhixianghui.facade.common.vo.AlipayMccVo;
import com.zhixianghui.service.common.core.biz.config.AlipayMccBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-06-07
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AlipayMccImpl implements AlipayMccFacade {

    private final AlipayMccBiz biz;

    @Override
    public List<AlipayMccVo> listAll() {
        return biz.listAll();
    }
}
