package com.zhixianghui.service.common.core.biz.approvalflow.base;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 基础审批流
 * @date 2020-08-14 10:27
 **/
@Slf4j
@Getter
public abstract class BaseFlow {

    /**
     * 检查流程是否结束
     * @param approvalFlow 流程对象
     * @return 是否结束
     */
    public boolean checkFlowEnd(ApprovalFlow approvalFlow){
        Integer status = approvalFlow.getStatus();
        //完成、撤回、异常
        return status == FlowStatus.FINISHED.getValue();
    }

    /**
     * 开始流程
     * @param approvalFlow 流程对象
     * @return 返回是否最后一步
     */
    public ApprovalInfoVo startFlow(ApprovalFlow approvalFlow){
        if(!checkFlowEnd(approvalFlow)){
            return handleFlow(approvalFlow);
        }
        return ApprovalInfoVo.builder().id(approvalFlow.getId()).isLast(false).build();
    }

    /**
     * 定义实际处理的过程
     * @param approvalFlow 流程对象
     */
    public abstract ApprovalInfoVo handleFlow(ApprovalFlow approvalFlow);

}
