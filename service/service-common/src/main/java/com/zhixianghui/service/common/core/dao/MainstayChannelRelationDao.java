package com.zhixianghui.service.common.core.dao;

import com.google.common.collect.Maps;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.MainstayChannelRelationDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供应商账户表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-09-27
 */
@Repository
public class MainstayChannelRelationDao extends MyBatisDao<MainstayChannelRelation, Long> {

    public void updateStatusByPayChannelNo(String payChannelNo, Integer status, String operator) {
        if (StringUtils.isBlank(payChannelNo) || status == null){
            throw new IllegalArgumentException("条件缺失,payChannelNo:" + payChannelNo + ",status:" + status);
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("payChannelNo",payChannelNo);
        paramMap.put("status",status);
        paramMap.put("updateOperator",operator);
        this.getSqlSession().update(fillSqlId("updateStatusByPayChannelNo"), paramMap);
    }

    public List<MainstayChannelRelation> listAllMainstay() {
        return this.getSqlSession().selectList(fillSqlId("listAllMainstay"));
    }

    public PageResult<List<MainstayChannelRelationDto>> listCustomPage(Map<String, Object> paramMap, PageParam pageParam,int size) {
        long totalRecord = 0L;
        List<Integer> count;
        List<MainstayChannelRelationDto> dataList;
        int recordSize = 0;
        pageParam.setPageSize(pageParam.getPageSize() * size);
        if (pageParam.isNeedTotalRecord()) {
            count = this.getSqlSession().selectList(fillSqlId("customCountBy"), paramMap);
            totalRecord = count.size();
            if (totalRecord <= 0) {
                //如果总记录数为0，就直接返回了
                dataList = new ArrayList<>();
                return PageResult.newInstance(dataList, pageParam, totalRecord);
            }
            //计算实际需要记录数
            for(int i = 0; i < Math.min(totalRecord , pageParam.getPageSize());i++){
                recordSize += count.get(i);
            }
        }

        if (isNotEmpty(pageParam.getSortColumns())) {
            if (paramMap == null) {
                paramMap = new HashMap<>(1);
            }
            paramMap.put(SORT_COLUMNS, this.filterSortColumns(pageParam.getSortColumns()));
        }
        dataList = this.getSqlSession().selectList(fillSqlId("listCustomPage"),paramMap,
                new RowBounds(getOffset(pageParam), pageParam.getPageSize()));
        if (!pageParam.isNeedTotalRecord()) {
            totalRecord = dataList.size();
        }
        return PageResult.newInstance(dataList, pageParam, totalRecord);
    }

    public List<Map<String, Object>> getAlipayMainstays(){
        List<Object> datas = this.getSqlSession().selectList(fillSqlId("getAlipayMainstays"), null);
        List<Map<String, Object>> collect = datas.stream().map(it -> (Map<String, Object>) it).collect(Collectors.toList());

        return collect;
    }

    public List<Map<String, Object>> getCmbainstays(){
        List<Object> datas = this.getSqlSession().selectList(fillSqlId("getCmbainstays"), null);
        List<Map<String, Object>> collect = datas.stream().map(it -> (Map<String, Object>) it).collect(Collectors.toList());

        return collect;
    }
}
