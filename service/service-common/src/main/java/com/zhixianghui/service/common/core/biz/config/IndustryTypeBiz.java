package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.facade.common.entity.config.IndustryType;
import com.zhixianghui.service.common.core.dao.IndustryTypeDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 行业类型
 *
 * <AUTHOR>
 * @date 2020-08-10
 */
@Service
public class IndustryTypeBiz {
    @Autowired
    private IndustryTypeDao industryTypeDao;

    public void insert(IndustryType industryType) {
        industryTypeDao.insert(industryType);
    }

    public void update(IndustryType industryType) {
        industryTypeDao.update(industryType);
    }

    public void delete(long id) {
        industryTypeDao.deleteById(id);
    }

    public IndustryType getByCode(String categoryCode) {
        return industryTypeDao.getByCode(categoryCode);
    }

    public List<IndustryType> listSubIndustryType(Long parentId) {
        if(Objects.isNull(parentId)){
           return null;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentId", parentId);
        return industryTypeDao.listBy(paramMap);
    }

    public List<IndustryType> listAll() {
        return industryTypeDao.listAll();
    }
}
