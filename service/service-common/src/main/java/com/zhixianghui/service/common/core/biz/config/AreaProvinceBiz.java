package com.zhixianghui.service.common.core.biz.config;

import com.zhixianghui.facade.common.entity.config.AreaProvince;
import com.zhixianghui.service.common.core.dao.AreaProvinceDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
* <p>
* 地区管理--省份表Biz
* </p>
*
* <AUTHOR>
* @version 创建时间： 2020-08-31
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AreaProvinceBiz{
    private final AreaProvinceDao areaProvinceDao;

    public List<AreaProvince> listBy(Map<String, Object> paramMap) {
        return areaProvinceDao.listBy(paramMap);
    }

    public AreaProvince getByProvinceNo(String provinceNo) {
        return areaProvinceDao.getOne(Collections.singletonMap("provinceNo",provinceNo));
    }
    public AreaProvince getProvinceByName(String provinceName) {
        return areaProvinceDao.getOne(Collections.singletonMap("provinceName",provinceName));
    }

    public List<AreaProvince> listAllProvince() {
        return areaProvinceDao.listAll();
    }
}
