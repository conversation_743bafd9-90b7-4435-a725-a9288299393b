package com.zhixianghui.service.common.core.biz.report;

import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.dto.PayChannelDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.entity.report.PayChannelType;
import com.zhixianghui.service.common.core.dao.EmployerAccountInfoDao;
import com.zhixianghui.service.common.core.dao.MainstayChannelRelationDao;
import com.zhixianghui.service.common.core.dao.PayChannelDao;
import com.zhixianghui.service.common.core.dao.PayChannelTypeDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
* 支付通道表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-09-27
*/
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PayChannelBiz {

    private final PayChannelDao payChannelDao;
    private final PayChannelTypeDao payChannelTypeDao;
    private final MainstayChannelRelationDao mainstayChannelRelationDao;
    private final EmployerAccountInfoDao employerAccountInfoDao;

    @Transactional(rollbackFor = Exception.class)
    public void create(PayChannel payChannel, List<PayChannelType> payChannelTypeList) {
        try {
            payChannelDao.insert(payChannel);
            payChannelTypeDao.insert(payChannelTypeList);
            // 新增通道 给已有的供应商都加上新通道信息为空的记录
            List<MainstayChannelRelation> list = mainstayChannelRelationDao.listAllMainstay();
            if(!ObjectUtils.isEmpty(list)){
                    list.forEach(
                            mainstayChannelRelation ->{
                                mainstayChannelRelation.setPayChannelNo(payChannel.getPayChannelNo());
                                mainstayChannelRelation.setStatus(OpenOffEnum.OFF.getValue());
                                mainstayChannelRelation.setUpdateTime(new Date());
                                mainstayChannelRelation.setCreateOperator(payChannel.getCreateOperator());
                                mainstayChannelRelation.setUpdateOperator(payChannel.getUpdateOperator());
                                mainstayChannelRelation.setChannelMchNo("");
                            }
                    );
                    mainstayChannelRelationDao.insert(list);
            }
        } catch (DuplicateKeyException e){
            log.error("DuplicateKeyException",e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付通道编号已经存在，新增通道编号不能相同");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(Long id, Integer status, String operator) {
        PayChannel payChannel =payChannelDao.getById(id);
        if(ObjectUtils.isEmpty(payChannel)){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此支付通道，id：" + id);
        }
        if(!payChannel.getStatus().equals(status)){
            // 通道禁用，则通道关联的代征主体关系、用工企业的通道禁用
            if(status.equals(OpenOffEnum.OFF.getValue())){
                mainstayChannelRelationDao.updateStatusByPayChannelNo(payChannel.getPayChannelNo(),status,operator);
                employerAccountInfoDao.updateStatusByPayChannelNo(payChannel.getPayChannelNo(),status,operator);
            }
            payChannel.setStatus(status);
            payChannel.setUpdateTime(new Date());
            payChannel.setUpdateOperator(operator);
            payChannelDao.update(payChannel);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateIfNotNull(PayChannel payChannel,List<PayChannelType> payChannelTypeList) {
        payChannelDao.updateIfNotNull(payChannel);
        Integer status = payChannel.getStatus();
        String operator = payChannel.getUpdateOperator();
        //删除通道类型重新插入
        payChannelTypeDao.deleteBy("deleteByChannelNo",new HashMap<String, Object>(){{put("payChannelNo",payChannel.getPayChannelNo());}});
        payChannelTypeDao.insert(payChannelTypeList);
        //通道禁用，则通道关联的供应商支付关系、用工企业的通道禁用
        if(status.equals(OpenOffEnum.OFF.getValue())){
            mainstayChannelRelationDao.updateStatusByPayChannelNo(payChannel.getPayChannelNo(),status,operator);
            employerAccountInfoDao.updateStatusByPayChannelNo(payChannel.getPayChannelNo(),status,operator);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        PayChannel payChannel = payChannelDao.getById(id);
        if(payChannel == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无支付通道信息，id:" + id);
        }
        payChannelDao.deleteById(id);
        //删除类型
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("payChannelNo",payChannel.getPayChannelNo());
        payChannelTypeDao.deleteBy("deleteByChannelNo",paramMap);
        //通道删除，则通道关联的供应商支付关系删除、使用该通道的用工企业的通道信息清空,禁用
        mainstayChannelRelationDao.deleteBy(Collections.singletonMap("payChannelNo",payChannel.getPayChannelNo()));
        employerAccountInfoDao.clearInfoByPayChannelNo(payChannel.getPayChannelNo(),OpenOffEnum.OFF.getValue());
    }

    public PageResult<List<PayChannel>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return payChannelDao.listPage(paramMap, pageParam);
    }

    public PayChannel getById(Long id) {
        return payChannelDao.getById(id);
    }

    public List<PayChannel> listAll() {
        return payChannelDao.listAll();
    }

    public PayChannel getByChannelNo(String payChannelNo){
        Map<String, Object> param = new HashMap<>();
        param.put("payChannelNo", payChannelNo);
        return payChannelDao.getOne(param);
    }

    public PageResult<List<PayChannelDto>> listCustomPage(Map<String, Object> beanToMap, PageParam pageParam) {
        PageResult<List<PayChannelDto>> pageResult = payChannelDao.listPage("listCustomPage","customCountBy",beanToMap,pageParam);
        pageResult.getData().stream().forEach(x->{
            if (StringUtil.isNotEmpty(x.getTypeConcat())){
                x.setChannelType(Arrays.asList(x.getTypeConcat().split(",")).stream().map(Integer::parseInt).collect(Collectors.toList()));
            }
        });
        return pageResult;
    }
}