package com.zhixianghui.service.common.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.zhixianghui.common.statics.enums.notification.PublishStatusEnum;
import com.zhixianghui.common.statics.enums.notification.PublishTypeEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import com.zhixianghui.service.common.core.biz.notification.NotificationBiz;
import com.zhixianghui.service.common.core.service.NotificationRecordService;
import com.zhixianghui.service.common.listener.TaskRocketMQListener;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
@Component
@RocketMQMessageListener(topic = "topic-notification-pub-task",consumeThreadMax = 1,consumerGroup = "NotificationPubGroup")
public class NotificationPubTaskListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private NotificationRecordService notificationRecordService;
    @Autowired
    private NotificationBiz notificationBiz;
    @Reference
    private RobotFacade robotFacade;

    @Override
    public void runTask(JSONObject jsonParam) {

        try {
            final QueryWrapper<NotificationRecord> queryWrapper = new QueryWrapper<NotificationRecord>()
                    .eq(NotificationRecord.COL_PUBLISH_STATUS, PublishStatusEnum.CREATED.getCode())
                    .and(wraper -> wraper.eq(NotificationRecord.COL_PUBLISH_TYPE, PublishTypeEnum.PUSH_NOW.getCode())
                            .or().lt(NotificationRecord.COL_PUSH_TIME, LocalDateTime.now(ZoneId.of("Asia/Shanghai"))));
            final List<NotificationRecord> recordList = this.notificationRecordService.list(queryWrapper);

            for (NotificationRecord notificationRecord : recordList) {
                notificationBiz.createNotificationsDetail(notificationRecord);
            }
        } catch (Exception e) {
            StringBuffer sb = new StringBuffer("#### 消息通知发送到收件人异常");
            sb.append("\\n > 执行时间：").append(DateUtil.formatDateTime(new Date()));

            MarkDownMsg msg = new MarkDownMsg();
            msg.setRobotType(RobotTypeEnum.NOTIFICATION_ROBOT.getType());
            msg.setUnikey(IdWorker.get32UUID());
            msg.setContent(sb.toString());

            robotFacade.pushMarkDownAsync(msg);
        }

    }
}
