package com.zhixianghui.service.common.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import com.zhixianghui.facade.common.service.InvoiceCategoryFacade;
import com.zhixianghui.service.common.core.biz.config.InvoiceCategoryBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 发票类目
 * <AUTHOR>
 * @date 2020/8/10
 **/
@Service
public class InvoiceCategoryFacadeImpl implements InvoiceCategoryFacade {
    @Autowired
    private InvoiceCategoryBiz invoiceCategoryBiz;

    @Override
    public void insert(InvoiceCategory invoiceCategory) {
        invoiceCategoryBiz.insert(invoiceCategory);
    }

    @Override
    public void update(InvoiceCategory invoiceCategory) {
        invoiceCategoryBiz.update(invoiceCategory);
    }

    @Override
    public void delete(long id) {
        invoiceCategoryBiz.delete(id);
    }

    @Override
    public InvoiceCategory getByCategoryCode(String categoryCode) {
        return invoiceCategoryBiz.getByCategoryCode(categoryCode);
    }

    @Override
    public PageResult<List<InvoiceCategory>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return invoiceCategoryBiz.listPage(paramMap, pageParam);
    }

    @Override
    public List<InvoiceCategory> listAll() {
        return invoiceCategoryBiz.listAll();
    }
}
