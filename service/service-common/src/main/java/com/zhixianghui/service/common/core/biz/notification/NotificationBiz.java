package com.zhixianghui.service.common.core.biz.notification;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.notification.NotificationReceiverTypeEnum;
import com.zhixianghui.common.statics.enums.notification.PublishStatusEnum;
import com.zhixianghui.common.statics.enums.notification.PublishTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.dto.NotificationRecordDto;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.service.common.core.service.NotificationRecordDetailService;
import com.zhixianghui.service.common.core.service.NotificationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class NotificationBiz {
    @Autowired
    private NotificationRecordService notificationRecordService;
    @Autowired
    private NotificationRecordDetailService notificationRecordDetailService;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private AgentFacade agentFacade;

    @Transactional(rollbackFor = Exception.class)
    public NotificationRecord createNotification(NotificationRecordDto notificationRecordDto) {

        NotificationRecord notificationRecord = new NotificationRecord();
        BeanUtil.copyProperties(notificationRecordDto, notificationRecord);

        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));

        notificationRecord.setCreationTime(now);
        notificationRecord.setUpdateTime(now);
        notificationRecord.setPublishStatus(PublishStatusEnum.CREATED.getCode());
        if (notificationRecordDto.getPublishType().intValue() == PublishTypeEnum.PUSH_NOW.getCode().intValue()) {
            notificationRecord.setPushTime(Date.from( now.atZone(ZoneId.of("+8")).toInstant()));
        }

        notificationRecordService.save(notificationRecord);
        return notificationRecord;
    }

    @Transactional(rollbackFor = Exception.class)
    public void createNotificationsDetail(NotificationRecord notificationRecord) {
        final Integer notificationReceiverType = notificationRecord.getNotificationReceiverType();
        int currentPage = 1;
        int pageSize = 100;

        Map<String, Object> paramMap = new HashMap<>();

        if (notificationReceiverType.intValue() == NotificationReceiverTypeEnum.ALL_MERCHANT.getCode()) {
            paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
        }else if (notificationReceiverType.intValue() == NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode()) {
            paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
            paramMap.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        }else if (notificationReceiverType.intValue() == NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode()) {
            paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
            paramMap.put("merchantType", MerchantTypeEnum.MAINSTAY.getValue());
        }else if (notificationReceiverType.intValue() == NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode()){
            final List<String> mchNos = JSONArray.parseArray(notificationRecord.getNotificationReceivers(), String.class);
            paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
            paramMap.put("mchNos", mchNos);
        } else if (notificationReceiverType.intValue() == NotificationReceiverTypeEnum.ALL_AGENT.getCode()) {
            paramMap.put("agentStatus", AgentStatusEnum.ACTIVE.getValue());
        } else if (notificationReceiverType.intValue() == NotificationReceiverTypeEnum.SELECTED_AGENT.getCode()) {
            final List<String> agentNos = JSONArray.parseArray(notificationRecord.getNotificationReceivers(), String.class);
            paramMap.put("agentStatus", AgentStatusEnum.ACTIVE.getValue());
            paramMap.put("agentNoList", agentNos);
        } else{
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不支持此类接收人类型");
        }

        if (notificationReceiverType.intValue() == NotificationReceiverTypeEnum.ALL_MERCHANT.getCode()
                || notificationReceiverType.intValue() == NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode()
                || notificationReceiverType.intValue() == NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode()
                || notificationReceiverType.intValue() == NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode()) {
            PageResult<List<Merchant>> listPageResult;
            do {
                PageParam pageParam = PageParam.newInstance(currentPage, pageSize);
                listPageResult = merchantQueryFacade.listPage(paramMap, pageParam);
                final List<Merchant> merchants = listPageResult.getData();
                if (merchants != null) {
                    for (Merchant merchant : merchants) {
                        final LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
                        final String mchName = merchant.getMchName();
                        final String mchNo = merchant.getMchNo();

                        NotificationRecordDetail detail = new NotificationRecordDetail();
                        detail.setNotificationId(notificationRecord.getId());
                        detail.setNotificationTitle(notificationRecord.getNotificationTitle());
                        detail.setCreationTime(now);
                        detail.setMchNo(mchNo);
                        detail.setMchName(mchName);
                        detail.setReadStatus(YesNoCodeEnum.NO.getValue());
                        detail.setNotificationType(notificationRecord.getNotificationType());
                        detail.setUpdateTime(now);

                        notificationRecordDetailService.save(detail);
                    }
                }
                currentPage++;
            } while (listPageResult.getData() != null && !listPageResult.getData().isEmpty());
        }

        if (notificationReceiverType.intValue() == NotificationReceiverTypeEnum.ALL_AGENT.getCode()
                || notificationReceiverType.intValue() == NotificationReceiverTypeEnum.SELECTED_AGENT.getCode()){
            PageResult<List<Agent>> listPageResult;
            do {
                PageParam pageParam = PageParam.newInstance(currentPage, pageSize);
                listPageResult = agentFacade.listPage(paramMap, pageParam);
                final List<Agent> agents = listPageResult.getData();
                if (agents != null) {
                    for (Agent agent : agents) {
                        final LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
                        final String mchName = agent.getAgentName();
                        final String mchNo = agent.getAgentNo();

                        NotificationRecordDetail detail = new NotificationRecordDetail();
                        detail.setNotificationId(notificationRecord.getId());
                        detail.setNotificationTitle(notificationRecord.getNotificationTitle());
                        detail.setCreationTime(now);
                        detail.setMchNo(mchNo);
                        detail.setMchName(mchName);
                        detail.setReadStatus(YesNoCodeEnum.NO.getValue());
                        detail.setNotificationType(notificationRecord.getNotificationType());
                        detail.setUpdateTime(now);

                        notificationRecordDetailService.save(detail);
                    }
                }
                currentPage++;
            } while (listPageResult.getData() != null && !listPageResult.getData().isEmpty());
        }

        final LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        notificationRecord.setPublishStatus(PublishStatusEnum.PUBLISHED.getCode());
        notificationRecord.setPublishTime(now);
        notificationRecord.setUpdateTime(now);
        this.notificationRecordService.updateById(notificationRecord);
    }

    public IPage<NotificationRecord> listNotifications(Page<Map<String, Object>> page, Map<String, Object> params) {
       return this.notificationRecordService.listNotifications(page, params);
    }

    public NotificationRecord getNotificationInfoById(Long notificationId) {
        return this.notificationRecordService.getById(notificationId);
    }

    public void updateNotificationDetail(NotificationRecordDetail notificationRecordDetail) {
        this.notificationRecordDetailService.updateById(notificationRecordDetail);
    }

    public void updateNotification(NotificationRecord notificationRecord) {
        this.notificationRecordService.removeById(notificationRecord);
    }
    @Transactional(rollbackFor = Exception.class)
    public void deleteNotificationByIds(List<Long> ids) {
        for (Long id : ids) {
            this.notificationRecordDetailService.remove(new QueryWrapper<NotificationRecordDetail>().eq(NotificationRecordDetail.COL_NOTIFICATION_ID, id));
        }
        this.notificationRecordService.removeByIds(ids);
    }

}
