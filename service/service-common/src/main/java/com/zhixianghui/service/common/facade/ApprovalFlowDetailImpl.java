package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.service.ApprovalFlowDetailFacade;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.ApprovalFlowDetailBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 审批节点详情表 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-07
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApprovalFlowDetailImpl implements ApprovalFlowDetailFacade {
    private final ApprovalFlowDetailBiz biz;

    @Override
    public List<ApprovalFlowDetail> listByApprovalFlowIdAndHandlerId(Long approvalFlowId, Long handlerId,boolean isAdmin) {
        return biz.listByApprovalFlowIdAndHandlerId(approvalFlowId,handlerId,isAdmin);
    }

    @Override
    public ApprovalInfoVo agreeApprovalDetail(Long approvalDetailId, Long handlerId, Integer platformSource, String flowName,String approvalOpinion,
                                              boolean isAdmin) {
        return biz.agreeApprovalDetail(approvalDetailId,handlerId,platformSource,flowName,approvalOpinion,isAdmin);
    }

    @Override
    public void disAgreeApprovalDetail(Long approvalDetailId, Long handlerId, Integer platformSource, String flowName,String approvalOpinion,
                                       boolean isAdmin) {
        biz.disAgreeApprovalDetail(approvalDetailId, handlerId, platformSource, flowName,approvalOpinion,isAdmin);
    }

    @Override
    public List<ApprovalFlowDetail> listBy(Map<String, Object> paramMap, String sortColumns) {
        return biz.listBy(paramMap,sortColumns);
    }

}
