package com.zhixianghui.service.common.facade;

import com.zhixianghui.facade.common.entity.config.AreaCity;
import com.zhixianghui.facade.common.entity.config.AreaMap;
import com.zhixianghui.facade.common.entity.config.AreaProvince;
import com.zhixianghui.facade.common.entity.config.AreaTown;
import com.zhixianghui.facade.common.service.AreaProvinceFacade;
import com.zhixianghui.service.common.core.biz.config.AreaCityBiz;
import com.zhixianghui.service.common.core.biz.config.AreaMapBiz;
import com.zhixianghui.service.common.core.biz.config.AreaProvinceBiz;
import com.zhixianghui.service.common.core.biz.config.AreaTownBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Map;

/**
 * 省市区实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-08-31
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AreaProvinceImpl implements AreaProvinceFacade {
    private final AreaProvinceBiz areaProvinceBiz;
    private final AreaCityBiz areaCityBiz;
    private final AreaTownBiz areaTownBiz;
    private final AreaMapBiz areaMapBiz;

    @Override
    public List<AreaProvince> listProvinceBy(Map<String, Object> paramMap) {
        return areaProvinceBiz.listBy(paramMap);
    }

    @Override
    public List<AreaProvince> listAllProvince() {
        return areaProvinceBiz.listAllProvince();
    }

    @Override
    public AreaProvince getProvinceByCode(String code){
        return areaProvinceBiz.getByProvinceNo(code);
    }

    @Override
    public AreaProvince getProvinceByName(String name) {
        return areaProvinceBiz.getProvinceByName(name);
    }

    @Override
    public List<AreaCity> listCityBy(Map<String, Object> paramMap){
        return areaCityBiz.listBy(paramMap);
    }

    @Override
    public List<AreaCity> listCityAll() {
        return areaCityBiz.listAll();
    }

    @Override
    public List<AreaTown> listTownAll() {
        return areaTownBiz.listTownAll();
    }

    @Override
    public AreaCity getCityByCode(String code) {
        return areaCityBiz.getByCityNo(code);
    }

    @Override
    public AreaCity getCityByName(String proviceNo, String cityName) {
        return areaCityBiz.getByCityName(proviceNo,cityName);
    }

    @Override
    public List<AreaTown> listTownBy(Map<String, Object> paramMap) {
        return areaTownBiz.listBy(paramMap);
    }

    @Override
    public AreaTown getTownByCode(String code) {
        return areaTownBiz.getByTownNo(code);
    }

    @Override
    public AreaTown getTownByName(String cityNo,String name) {
        return areaTownBiz.getByTownName(cityNo, name);
    }

    @Override
    public void saveAreaMap(List<AreaMap> areaMaps) {
        areaMapBiz.saveBatch(areaMaps);
    }

    @Override
    public AreaMap getByZxhCode(String zxhCode) {
        return areaMapBiz.getByZxhCode(zxhCode);
    }
}
