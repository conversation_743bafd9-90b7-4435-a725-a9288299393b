package com.zhixianghui.service.common.core.biz.approvalflow;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 审批过程中的驳回的额外业务通知Biz
 * @date 2020-11-19 15:42
 **/
@Service
@Slf4j
public class ApprovalDisAgreeOtherHandlerBiz {
    @Reference
    private NotifyFacade notifyFacade;

    public void disAgreeOtherHandle(ApprovalFlow approvalFlow, ApprovalFlowDetail approvalDetail) {
        Integer flowType = approvalFlow.getFlowTopicType();
        if (Objects.equals(flowType,FlowTopicType.MERCHANT_VERIFY.getValue())&&
                approvalDetail.getStepNum().equals(1)){
            log.info("主体认证驳回到第0步(发起人) 通知商户服务为改为认证失败 approvalFlowId:{},approvalDetailId:{}",
                    approvalDetail.getApprovalFlowId(),approvalDetail.getId());
            String msg = approvalFlow.getExtInfo();
            notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_APPROVAL_MERCHANT_VERIFY_DISAGREE_ASYNC,msg);
        }
    }
}
