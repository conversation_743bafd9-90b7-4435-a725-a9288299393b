package com.zhixianghui.service.common.core.biz.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.vo.report.AlipayAccountBookResVo;
import com.zhixianghui.facade.common.constants.ReportConstants;
import com.zhixianghui.facade.common.dto.EmployerAccountInfoDto;
import com.zhixianghui.facade.common.entity.report.*;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.service.common.core.dao.EmployerAccountInfoDao;
import com.zhixianghui.service.common.core.dao.EmployerMainstayRelationDao;
import com.zhixianghui.service.common.core.dao.MainstayChannelRelationDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
* 用工企业账户表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-09-27
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmployerAccountInfoBiz {

    private final EmployerMainstayRelationDao employerMainstayRelationDao;

    private final EmployerAccountInfoDao employerAccountInfoDao;

    private final MainstayChannelRelationDao mainstayChannelRelationDao;

    private final EmployerMainstayRelationBiz employerMainstayRelationBiz;

    private final ReportAccountHistoryBiz reportAccountHistoryBiz;

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private OrderFacade orderFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private AlipayFacade alipayFacade;


    public PageResult<List<EmployerAccountInfo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return employerAccountInfoDao.listPage(paramMap, pageParam);
    }

    public PageResult<List<Map<String,String>>> groupByMch(Map<String, Object> paramMap,PageParam pageParam) {
        return employerAccountInfoDao.listPage("groupByMch","groupByMchCount", paramMap, pageParam);
    }

    public List<EmployerAccountInfo> listByEmployerNoAndMainstayNo(String employerNo, String mainstayNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", employerNo);
        paramMap.put("mainstayNo", mainstayNo);
        return employerAccountInfoDao.listBy(paramMap);
    }

    public void batchUpdate(List<EmployerAccountInfo> list) {
        employerAccountInfoDao.update(list);
    }

    public PageResult<List<EmployerAccountInfoDto>> listCustomPage(Map<String, Object> paramMap, PageParam pageParam) {
        return employerAccountInfoDao.listCustomPage(paramMap, pageParam);
    }

    public EmployerAccountInfo getByEmployerNoAndMainstayNoAndChannelType(String employerNo, String mainstayNo, Integer channelType) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", employerNo);
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("channelType", channelType);
        return employerAccountInfoDao.getOne(paramMap);
    }

    public void update(EmployerAccountInfo employerAccountInfo){
        employerAccountInfoDao.update(employerAccountInfo);
    }

    public List<EmployerAccountInfo> listBy(Map<String, Object> paramMap) {
        return employerAccountInfoDao.listBy(paramMap);
    }

    /**
     * 商户信息变化，修改用工企业账户表和代征关系表
     */
    public void merchantMessageChange(MerchantUpdateVo merchantUpdateVo) {
        if (merchantUpdateVo.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            changeMerchantMessage(merchantUpdateVo);
        }else if (merchantUpdateVo.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()){
            changeMainstayMessage(merchantUpdateVo);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeMainstayMessage(MerchantUpdateVo merchantUpdateVo) {
        //根据供应商查询用工企业账户表
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayNo", merchantUpdateVo.getMchNo());
        List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoDao.listBy(paramMap);
        Date date = new Date();
        employerAccountInfoList.stream().
                forEach(employerAccountInfo -> {
                    employerAccountInfo.setMainstayName(merchantUpdateVo.getMchName());
                    employerAccountInfo.setUpdateTime(date);
                    employerAccountInfo.setUpdateOperator(merchantUpdateVo.getUpdator());
                });
        //批量更新
        employerAccountInfoDao.update(employerAccountInfoList);

        //根据供应商编号查询代征关系表
        List<EmployerMainstayRelation> employerMainstayRelationList = employerMainstayRelationDao.listBy(paramMap);
        employerMainstayRelationList.stream().
                forEach(employerMainstayRelation -> {
                    employerMainstayRelation.setMainstayName(merchantUpdateVo.getMchName());
                    employerMainstayRelation.setUpdateTime(date);
                    employerMainstayRelation.setUpdateOperator(merchantUpdateVo.getUpdator());
                });
        //批量更新
        employerMainstayRelationDao.update(employerMainstayRelationList);

        //根据供应商编号查询供应商账户表
        List<MainstayChannelRelation> mainstayChannelRelationList = mainstayChannelRelationDao.listBy(paramMap);
        mainstayChannelRelationList.stream().
                forEach(mainstayChannelRelation -> {
                    mainstayChannelRelation.setMainstayName(merchantUpdateVo.getMchName());
                    mainstayChannelRelation.setUpdateTime(date);
                    mainstayChannelRelation.setUpdateOperator(merchantUpdateVo.getUpdator());
                });
        //批量更新
        mainstayChannelRelationDao.update(mainstayChannelRelationList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeMerchantMessage(MerchantUpdateVo merchantUpdateVo) {
        //根据商户编号查询用工企业账户表
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",merchantUpdateVo.getMchNo());
        List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoDao.listBy(paramMap);
        Date date = new Date();
        employerAccountInfoList.stream().
                forEach(employerAccountInfo -> {
                    employerAccountInfo.setEmployerName(merchantUpdateVo.getMchName());
                    employerAccountInfo.setMchName(merchantUpdateVo.getMchName());
                    employerAccountInfo.setUpdateTime(date);
                    employerAccountInfo.setUpdateOperator(merchantUpdateVo.getUpdator());
                });


        //批量更新
        employerAccountInfoDao.update(employerAccountInfoList);

        //根据商户编号查询代征关系表
        List<EmployerMainstayRelation> employerMainstayRelationList = employerMainstayRelationDao.listBy(paramMap);
        employerMainstayRelationList.stream().
                forEach(employerMainstayRelation -> {
                    employerMainstayRelation.setEmployerName(merchantUpdateVo.getMchName());
                    employerMainstayRelation.setUpdateTime(date);
                    employerMainstayRelation.setUpdateOperator(merchantUpdateVo.getUpdator());
                });
        //批量更新
        employerMainstayRelationDao.update(employerMainstayRelationList);
    }

    /**
     * 如果修改了账户字段，需要自动报备
     */
    private void autoReport(Map<String,List<Map<String,Object>>> accountList,String mchNo,String reporter,String contactPhone) {
        accountList.forEach((mainstayNo,accounts)->{

            final EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationBiz.getByEmployerNoAndMainstayNo(mchNo, mainstayNo);
            if (employerMainstayRelation == null){
                return;
            }
            for (Map<String, Object> account : accounts) {
                /**
                 * 商户编号+代征主体+通道 唯一
                 * 如果不存在，或者通道编号不一致，则需要重新报备
                 */
                final EmployerAccountInfo employerAccountInfo = this.getByEmployerNoAndMainstayNoAndChannelType(mchNo, mainstayNo, (Integer) account.get("channelType"));

                if (employerAccountInfo == null || !StringUtils.equals(employerAccountInfo.getPayChannelNo(), (String) account.get("payChannelNo")) || employerAccountInfo.getStatus().intValue() != OpenOffEnum.OPEN.getValue()) {
                    // todo 需要重新报备
                    Integer channeltype = (Integer) account.get("channelType");
                    String channelName = (String) account.get("channelName");
                    String payChannelNo = (String) account.get("payChannelNo");
                    String payChannelName = (String) account.get("payChannelName");

                    ReportEntity reportEntity = new ReportEntity();
                    reportEntity.setEmployerNo(employerMainstayRelation.getEmployerNo());
                    reportEntity.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
                    reportEntity.setReporter(reporter);
                    reportEntity.setEmployerName(employerMainstayRelation.getEmployerName());
                    reportEntity.setMainstayNo(employerMainstayRelation.getMainstayNo());
                    reportEntity.setMainstayName(employerMainstayRelation.getMainstayName());
                    reportEntity.setChannelType(channeltype);
                    reportEntity.setPayChannelName(payChannelName);
                    reportEntity.setPayChannelNo(payChannelNo);
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_AUTO_REPORT, employerMainstayRelation.getEmployerNo(), contactPhone, NotifyTypeEnum.AUTO_REPORT_AFTER_AUTH.getValue(), MessageMsgDest.TAG_AUTO_REPORT, JSON.toJSONString(reportEntity), MsgDelayLevelEnum.S_5.getValue());
                }
            }
        });

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("status", CommonStatusEnum.ACTIVE.getValue());
        paramMap.put("employerNo", mchNo);
        final List<EmployerAccountInfo> employerAccountInfos = this.listBy(paramMap);

        Set<String> activeAcctSets = new HashSet<>();
        for (EmployerAccountInfo employerAccountInfo : employerAccountInfos) {
            final String a = employerAccountInfo.getEmployerNo();
            final String b = employerAccountInfo.getMainstayNo();
            final String c = employerAccountInfo.getChannelType().toString();

            activeAcctSets.add(a + "-" + b + "-" + c);
        }

        Set<String> nowSets = new HashSet<>();
        accountList.forEach((mainstayNo, accounts) -> {
            for (Map<String, Object> account : accounts) {
                String channelType = account.get("channelType").toString();
                nowSets.add(mchNo + "-" + mainstayNo + "-" + channelType);
            }
        });

        activeAcctSets.removeAll(nowSets);
        activeAcctSets.forEach(it->{
            final String[] strings = it.split("-");
            String itMchNo = strings[0];
            String mainstayNo = strings[1];
            String channelType = strings[2];
            final EmployerAccountInfo accountInfo = getByEmployerNoAndMainstayNoAndChannelType(itMchNo, mainstayNo, Integer.parseInt(channelType));
            accountInfo.setStatus(CommonStatusEnum.INACTIVE.getValue());
            accountInfo.setUpdateTime(new Date());
            accountInfo.setUpdateOperator(reporter);
            employerAccountInfoDao.update(accountInfo);

        });

    }

    public void updateList(List<EmployerAccountInfo> employerAccountInfoList) {
        employerAccountInfoDao.update(employerAccountInfoList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAccount(MerchantFlowVo merchantFlowVo) {
        Merchant merchant = merchantFacade.getByMchNo(merchantFlowVo.getMchNo());
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        if (merchant.getMerchantType() != MerchantTypeEnum.EMPLOYER.getValue()){
            return;
        }
        //merchantFlowVo.buildAccounts();
        autoReport(merchantFlowVo.getAccounts(),merchantFlowVo.getMchNo(),merchantFlowVo.getUpdator(),merchant.getContactPhone());
    }

    public EmployerAccountInfo getByEmployerNoAndMainstayNoAndChannelNo(String mchNo, String mainstayNo, String channelNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", mchNo);
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("payChannelNo", channelNo);
        return employerAccountInfoDao.getOne(paramMap);
    }

    public EmployerAccountInfo getByEmployerNoAndParentMchNoAndChannelNo(String mchNo, String parentMerchantNo, String channelNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", mchNo);
        paramMap.put("parentMerchantNo", parentMerchantNo);
        paramMap.put("payChannelNo", channelNo);
        return employerAccountInfoDao.getOne(paramMap);
    }

    public List<EmployerAccountInfo> getByEmployerNameAndParentMchNoAndChannelNo(String mchName, String parentMerchantNo, String channelNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerName", mchName);
        paramMap.put("parentMerchantNo", parentMerchantNo);
        paramMap.put("payChannelNo", channelNo);
        return employerAccountInfoDao.listBy(paramMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void forceDelete(String mchNo) {
        Map<String,Object> param=new HashMap<>();
        param.put("employerNo",mchNo);
        employerMainstayRelationDao.deleteBy(param);
        employerAccountInfoDao.deleteBy(param);
    }

    public EmployerAccountInfo getOneBySubMerchantNoAndPayChannelNo(String payeeIdentity, String channelNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("payChannelNo",channelNo);
        paramMap.put("subMerchantNo",payeeIdentity);
        EmployerAccountInfo employerAccountInfo = employerAccountInfoDao.getOne(paramMap);
        return employerAccountInfo;
    }

    public void getAlipayCardNo() throws Exception {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("payChannelNo", ChannelNoEnum.ALIPAY.name());
        //用工企业数据
        List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoDao.listBy(paramMap);
        for (EmployerAccountInfo employerAccountInfo : employerAccountInfoList) {
            if (StringUtils.isBlank(employerAccountInfo.getSubMerchantNo())){
                continue;
            }
            String msg = alipayFacade.accountBookQuery(employerAccountInfo.getSubAgreementNo(),employerAccountInfo.getSubMerchantNo());
            String resJson = JSONObject.parseObject(msg).getJSONObject(ReportConstants.ALIPAY_FUND_ACCOUNTBOOK_QUERY_RESPONSE).toJSONString();
            AlipayAccountBookResVo alipayAccountBookResVo = JsonUtil.toBean(resJson, AlipayAccountBookResVo.class);
            if (alipayAccountBookResVo.getCode().equals("10000")){
                String cardNo = alipayAccountBookResVo.getExtCardInfo().getCardNo();
                employerAccountInfo.setAlipayCardNo(cardNo);
                employerAccountInfoDao.update(employerAccountInfo);
                //订阅入金接口
                alipayFacade.subscribe(employerAccountInfo.getSubAgreementNo(),employerAccountInfo.getSubMerchantNo());
                log.info("商户号：[{}]，供应商编号：[{}]，记账本id：[{}]，外标卡号：[{}]",employerAccountInfo.getEmployerNo(),employerAccountInfo.getMainstayNo(),employerAccountInfo.getSubMerchantNo(),employerAccountInfo.getAlipayCardNo());
            }
        }

        //供应商数据
        List<MainstayChannelRelation> mainstayChannelRelationList = mainstayChannelRelationDao.listBy(paramMap);
        for (MainstayChannelRelation mainstayChannelRelation : mainstayChannelRelationList) {
            if (StringUtils.isBlank(mainstayChannelRelation.getChannelMchNo())){
                continue;
            }
            String msg = alipayFacade.accountBookQuery(mainstayChannelRelation.getAgreementNo(),mainstayChannelRelation.getChannelMchNo());
            String resJson = JSONObject.parseObject(msg).getJSONObject(ReportConstants.ALIPAY_FUND_ACCOUNTBOOK_QUERY_RESPONSE).toJSONString();
            AlipayAccountBookResVo alipayAccountBookResVo = JsonUtil.toBean(resJson, AlipayAccountBookResVo.class);
            if (alipayAccountBookResVo.getCode().equals("10000")){
                String cardNo = alipayAccountBookResVo.getExtCardInfo().getCardNo();
                mainstayChannelRelation.setAlipayCardNo(cardNo);
                mainstayChannelRelationDao.update(mainstayChannelRelation);
                //订阅入金接口
                alipayFacade.subscribe(mainstayChannelRelation.getAgreementNo(),mainstayChannelRelation.getChannelMchNo());
                log.info("供应商编号：[{}]，记账本id：[{}]，外标卡号：[{}]",mainstayChannelRelation.getMainstayNo(),mainstayChannelRelation.getChannelMchNo(),mainstayChannelRelation.getAlipayCardNo());
            }
        }
    }

    public void changeAccount(Long id, String employerNo,String updator) {
        ReportAccountHistory reportAccountHistory = reportAccountHistoryBiz.getById(id);
        if (reportAccountHistory == null || !reportAccountHistory.getEmployerNo().equals(employerNo)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("目标支付账户不存在");
        }

        //判断是否存在未完成的订单
        if (!orderFacade.isExistNotCompleteOrder(employerNo,reportAccountHistory.getMainstayNo())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前仍有未完成订单，请等候订单完成或取消发放再切换支付账户");
        }

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",reportAccountHistory.getMainstayNo());
        paramMap.put("channelType",reportAccountHistory.getPayChannelType());
        EmployerAccountInfo employerAccountInfo = employerAccountInfoDao.getOne(paramMap);
        if (employerAccountInfo == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付账户不存在");
        }
        if (employerAccountInfo.getSubMerchantNo().equals(reportAccountHistory.getChannelMerchantNo())){
            return;
        }

        employerAccountInfo.setPayChannelNo(reportAccountHistory.getPayChannelNo());
        employerAccountInfo.setPayChannelName(reportAccountHistory.getPayChannelName());

        employerAccountInfo.setEmployerName(reportAccountHistory.getEmployerName());
        employerAccountInfo.setSubMerchantNo(reportAccountHistory.getChannelMerchantNo());
        employerAccountInfo.setSubAgreementNo(reportAccountHistory.getAgreementNo());
        employerAccountInfo.setSubAlipayUserId(reportAccountHistory.getAlipayUserId());
        employerAccountInfo.setAlipayCardNo(reportAccountHistory.getAlipayCardNo());

        employerAccountInfo.setUpdateTime(new Date());
        employerAccountInfo.setUpdateOperator(updator);

        //查询供应商账户信息--parent_merchant_no
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(reportAccountHistory.getMainstayNo(),reportAccountHistory.getPayChannelNo());
        if (mainstayChannelRelation == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商账户不存在，请联系管理员");
        }
        employerAccountInfo.setParentMerchantNo(mainstayChannelRelation.getChannelMchNo());
        employerAccountInfoDao.update(employerAccountInfo);

    }

    public EmployerAccountInfo getById(Long id) {
        return employerAccountInfoDao.getById(id);
    }
}
