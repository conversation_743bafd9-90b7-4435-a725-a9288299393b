package com.zhixianghui.service.common.core.biz.approvalflow;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentLeaderEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowHandleType;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.HandleStatus;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerMainAuthVo;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDao;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDetailDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 通用biz
 * @date 2020-08-12 11:05
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonBiz {

    private final ApprovalFlowDetailDao approvalFlowDetailDao;
    private final ApprovalFlowDao approvalFlowDao;
    private final ApprovalEndHandlerBiz approvalEndHandlerBiz;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    /**
     * 创建审批流
     *
     * @param approvalFlow 审批流对象
     * @return 有id的审批流对象
     */
    @Transactional(rollbackFor = Exception.class)
    public ApprovalFlow createApprovalFlow(ApprovalFlow approvalFlow) {
        //创建流程
        approvalFlowDao.insert(approvalFlow);
        Long flowId = approvalFlow.getId();
        if (flowId == null) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("插入数据flowId为空");
        }
        //提交审批也要记录一条审批记录
        ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
        approvalFlowDetail.setPlatform(0);
        approvalFlowDetail.setCreateTime(new Date());
        approvalFlowDetail.setUpdateTime(new Date());
        approvalFlowDetail.setApprovalFlowId(flowId);
        approvalFlowDetail.setHandlerId(approvalFlow.getInitiatorId());
        approvalFlowDetail.setHandlerName(approvalFlow.getInitiatorName());
        approvalFlowDetail.setHandlerType(FlowHandleType.SUBMIT.getValue());
        approvalFlowDetail.setStepNum(0);
        approvalFlowDetail.setStatus(HandleStatus.AGREE.getValue());
        approvalFlowDetail.setOperatorName(approvalFlow.getInitiatorName());
        approvalFlowDetail.setPlatform(approvalFlow.getPlatform());
        approvalFlowDetail.setIsHistory(true);
        approvalFlowDetailDao.insert(approvalFlowDetail);
        log.info("初始化完成,审批流程id:{},发起人:{},审批主题:{}", approvalFlow.getId(), approvalFlow.getInitiatorName(), FlowTopicType.getNameByValue(approvalFlow.getFlowTopicType()));
        return approvalFlow;
    }

    /**
     * （运营平台）给指定(负责人审批)审批流生成ApprovalFlowDetail
     *
     * @param flowId            审批流id
     * @param stepNum           步骤
     * @param departmentNumbers 要找的部门负责人部门编号
     * @param platformSource    平台来源
     */
    public void createOperationFlowLeaderDetails(Long flowId, Integer stepNum, List<String> departmentNumbers, Integer handlerType, Integer platformSource) {
        List<PmsDepartment> departments = pmsDepartmentFacade.getDepartmentByNumbers(departmentNumbers);
        List<ApprovalFlowDetail> approvalFlowDetails = departments.stream().map(
                department -> {
                    department = getPmsDepartment(department);
                    ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
                    approvalFlowDetail.setCreateTime(new Date());
                    approvalFlowDetail.setUpdateTime(new Date());
                    approvalFlowDetail.setApprovalFlowId(flowId);
                    approvalFlowDetail.setHandlerId(department.getLeaderId());
                    approvalFlowDetail.setHandlerName(department.getLeaderName());
                    approvalFlowDetail.setHandlerType(handlerType);
                    approvalFlowDetail.setStepNum(stepNum);
                    approvalFlowDetail.setStatus(HandleStatus.PENDING.getValue());
                    approvalFlowDetail.setOperatorName("");
                    approvalFlowDetail.setPlatform(platformSource);
                    return approvalFlowDetail;
                }
        ).collect(Collectors.toList());
        if (approvalFlowDetails.isEmpty()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无审批人可指定，流程回滚");
        }
        approvalFlowDetailDao.insert(approvalFlowDetails);
    }

    /**
     * （运营平台）给指定(需所有人审批)审批流生成ApprovalFlowDetail
     *
     * @param flowId            审批流id
     * @param stepNum           步骤
     * @param departmentNumbers 要找的部门编号
     * @param platformSource    平台来源
     */
    public void createOperationFlowAllDetails(Long flowId, int stepNum, List<String> departmentNumbers, int handlerType, int platformSource) {
        List<PmsOperator> operators = Lists.newArrayList();
        departmentNumbers.forEach(
                number -> operators.addAll(pmsOperatorFacade.listByDepartmentNumber(number))
        );
        List<ApprovalFlowDetail> approvalFlowDetails = operators.stream().map(
                operator -> {
                    ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
                    approvalFlowDetail.setCreateTime(new Date());
                    approvalFlowDetail.setUpdateTime(new Date());
                    approvalFlowDetail.setApprovalFlowId(flowId);
                    approvalFlowDetail.setHandlerId(operator.getId());
                    approvalFlowDetail.setHandlerName(operator.getRealName());
                    approvalFlowDetail.setHandlerType(handlerType);
                    approvalFlowDetail.setStepNum(stepNum);
                    approvalFlowDetail.setStatus(HandleStatus.PENDING.getValue());
                    approvalFlowDetail.setOperatorName("");
                    approvalFlowDetail.setPlatform(platformSource);
                    return approvalFlowDetail;
                }
        ).collect(Collectors.toList());
        if (approvalFlowDetails.isEmpty()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无审批人可指定，流程回滚");
        }
        approvalFlowDetailDao.insert(approvalFlowDetails);
    }

    public List<ApprovalFlowDetail> listByFlowIdAndStepNumAndIsHistory(Long flowId, Integer stepNum, Boolean isHistory) {
        Map<String, Object> paramMap = ImmutableMap.<String, Object>builder()
                .put("approvalFlowId", flowId)
                .put("stepNum", stepNum)
                .put("isHistory", isHistory)
                .build();
        return approvalFlowDetailDao.listBy(paramMap);
    }

    /**
     * 获取最近一级有负责人的部门(当前只向上找一级)
     *
     * @param pmsDepartment 检查的部门
     * @return 有负责人的部门
     */
    private PmsDepartment getPmsDepartment(PmsDepartment pmsDepartment) {
        if (pmsDepartment == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("传入的pmsDepartment为空");
        }
        //当前部门有负责人
        if (pmsDepartment.getLeaderId() != PmsDepartmentLeaderEnum.UNALLOCATED.getId()) {
            return pmsDepartment;
        }

        //无负责人且不是二级部门 找上一级负责人
        if (pmsDepartment.getParentId() != PmsDepartmentEnum.COMPANY.getId()) {
            PmsDepartment parentDepartment = pmsDepartmentFacade.getDepartmentById(pmsDepartment.getParentId());
            //需要找多级递归 getPmsDepartment
            if (parentDepartment != null && parentDepartment.getLeaderId() != PmsDepartmentLeaderEnum.UNALLOCATED.getId()) {
                return parentDepartment;
            } else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到部门负责人:" + pmsDepartment.getDepartmentName());
            }
        }
        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到部门负责人:" + pmsDepartment.getDepartmentName());
    }

    /**
     * 节点处理人反查主审批流程
     *
     * @param detailParamMap 处理节点筛选条件
     * @param flowParamMap   审批流程筛选
     * @param pageParam      分页参数
     * @return 主审批流程分页参数
     */
    public PageResult<List<ApprovalFlow>> listFlowPage(Map<String, Object> detailParamMap, Map<String, Object> flowParamMap, PageParam pageParam) {
        //detailParamMap.put("groupby", "APPROVAL_FLOW_ID");//数据库去重
        List<ApprovalFlowDetail> approvalFlowDetails = approvalFlowDetailDao.listBy(detailParamMap);
        if (ObjectUtils.isEmpty(approvalFlowDetails)) {
            return PageResult.newInstance(Lists.newArrayList(), pageParam, 0L);
        }
        //获取审批流id [代码去重distinct()]
        List<Long> flowIds = approvalFlowDetails.stream().map(ApprovalFlowDetail::getApprovalFlowId)
                .distinct().collect(Collectors.toList());
        flowParamMap.put("idList", flowIds);
        return approvalFlowDao.listPage(flowParamMap, pageParam);
    }

    /**
     * 校验是否有权限查询该审批流程
     *
     * @param approvalFlowId 审批流程id
     * @param handlerId      查询人
     * @return 审批流程
     */
    public ApprovalFlow authApproval(Long approvalFlowId, Long handlerId, boolean isAdmin) {
        ApprovalFlow approvalFlow = approvalFlowDao.getById(approvalFlowId);
        if (ObjectUtils.isEmpty(approvalFlow)) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此审批流程");
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("approvalFlowId", approvalFlowId);
        paramMap.put("handlerId", handlerId);
        List<ApprovalFlowDetail> list = approvalFlowDetailDao.listBy(paramMap, "STEP_NUM ASC");

        //非发起人和非经手人
        if (ObjectUtils.isEmpty(list) && !approvalFlow.getInitiatorId().equals(handlerId) && !isAdmin) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("此流程您无权限审批");
        }
        return approvalFlow;
    }

    public ApprovalFlow getByDetailId(Long detailId) {
        ApprovalFlowDetail approvalFlowDetail = approvalFlowDetailDao.getById(detailId);
        if (ObjectUtils.isEmpty(approvalFlowDetail)) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此审批节点详情 detailId:" + detailId);
        }
        return approvalFlowDao.getById(approvalFlowDetail.getApprovalFlowId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void logAndUpdateExtInfo(ApprovalFlow approvalFlow, ApprovalFlowDetail approvalFlowDetail) {
        approvalFlowDao.update(approvalFlow);
        approvalFlowDetailDao.insert(approvalFlowDetail);
    }

    /**
     * 合伙人审批撤回
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelApproval(Long approvalFlowId, Long handlerId, String handlerName, Integer platform, boolean isAdmin) {
        ApprovalFlow approvalFlow = approvalFlowDao.getById(approvalFlowId);
        if (ObjectUtils.isEmpty(approvalFlow)) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此审批流程");
        }
        //非发起人且非admin
        if (!approvalFlow.getInitiatorId().equals(handlerId)&&!isAdmin) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("此流程非您创建，无法撤回");
        }
        approvalFlow.setStatus(FlowStatus.FINISHED.getValue());
        approvalFlowDao.update(approvalFlow);

        //撤回记录
        ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
        approvalFlowDetail.setCreateTime(new Date());
        approvalFlowDetail.setUpdateTime(new Date());
        approvalFlowDetail.setApprovalFlowId(approvalFlowId);
        approvalFlowDetail.setHandlerId(handlerId);
        approvalFlowDetail.setHandlerName(handlerName);
        approvalFlowDetail.setHandlerType(FlowHandleType.RECALL.getValue());
        approvalFlowDetail.setStepNum(approvalFlow.getStepNum());
        approvalFlowDetail.setStatus(HandleStatus.AGREE.getValue());
        approvalFlowDetail.setOperatorName(handlerName);
        approvalFlowDetail.setPlatform(platform);
        approvalFlowDetail.setIsHistory(true);
        approvalFlowDetailDao.insert(approvalFlowDetail);
        //其他节点都归为history
        approvalFlowDetailDao.cancelApprovalDetail(approvalFlow.getId());

        //发送mq通知其它服务
        approvalFlow.setHandleId(handlerId);
        approvalFlow.setHandleName(handlerName);
        approvalEndHandlerBiz.cancelHandle(approvalFlow);
    }

}
