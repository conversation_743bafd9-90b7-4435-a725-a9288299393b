package com.zhixianghui.service.common.core.biz.report;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.ReportAccountHistory;
import com.zhixianghui.facade.common.vo.ReportAccountHistoryVo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.service.common.core.dao.ReportAccountHistoryDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2021-07-05
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReportAccountHistoryBiz {

    private final ReportAccountHistoryDao reportaccounthistoryDao;

    public void create(ReportAccountHistory reportAccountHistory) {
        try{
            reportaccounthistoryDao.insert(reportAccountHistory);
        }catch (DuplicateKeyException e){
            log.info("记账本记录已存在，跳过插入操作...");
        }
    }

    public List<ReportAccountHistoryVo> getMerchantAccount(String employerNo, String mainstayNo) {
        Map<String,Object> paramMap= new HashMap<>();
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("merchantType",MerchantTypeEnum.EMPLOYER.getValue());
        paramMap.put("isShow", YesNoCodeEnum.YES.getValue());
        List<ReportAccountHistory> list = reportaccounthistoryDao.listBy(paramMap);
        return list.stream().map(x->{
            ReportAccountHistoryVo reportAccountHistoryVo = new ReportAccountHistoryVo();
            BeanUtils.copyProperties(x,reportAccountHistoryVo);
            return reportAccountHistoryVo;
        }).collect(Collectors.toList());
    }

    public ReportAccountHistory getById(Long id) {
        ReportAccountHistory reportAccountHistory = reportaccounthistoryDao.getById(id);
        return reportAccountHistory;
    }

    public List<ReportAccountHistory> getByChannelType(String employerNo, String mainstayNo, Long channelType) {
        Map<String,Object> paramMap= new HashMap<>();
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("merchantType",MerchantTypeEnum.EMPLOYER.getValue());
        paramMap.put("payChannelType",channelType);
        List<ReportAccountHistory> list = reportaccounthistoryDao.listBy(paramMap);
        return list;
    }

    public void changeStatus(Long id, Integer isShow) {
        ReportAccountHistory reportAccountHistory = reportaccounthistoryDao.getById(id);
        if (reportAccountHistory == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账户不存在");
        }

        reportAccountHistory.setIsShow(isShow);
        reportaccounthistoryDao.update(reportAccountHistory);
    }

    public void changeTitle(Long id, String title) {
        ReportAccountHistory reportAccountHistory = reportaccounthistoryDao.getById(id);
        if (reportAccountHistory == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账户不存在");
        }

        reportAccountHistory.setTitle(title);
        reportaccounthistoryDao.update(reportAccountHistory);
    }

    public List<ReportAccountHistory> listBy(Map<String, Object> paramsMap) {
        return reportaccounthistoryDao.listBy(paramsMap);
    }

    public void delete(ReportAccountHistory reportAccountHistory) {
        reportaccounthistoryDao.deleteById(reportAccountHistory.getId());
    }

    public ReportAccountHistory getByChannelMerchantNoAndPayChannelNo(String channelMerchantNo, String payChannelNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("channelMerchantNo",channelMerchantNo);
        paramMap.put("payChannelNo",payChannelNo);
        List<ReportAccountHistory> reportAccountHistoryList =  reportaccounthistoryDao.listBy(paramMap);
        if (reportAccountHistoryList.size() > 0){
            return reportAccountHistoryList.get(0);
        }else{
            return null;
        }
    }
}