package com.zhixianghui.service.common.core.biz.cache;

import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.service.common.core.biz.report.MainstayChannelRelationBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName CacheBiz
 * @Description TODO
 * @Date 2022/12/27 16:01
 */
@Service
public class CacheBiz {

    @Autowired
    private MainstayChannelRelationBiz mainstayChannelRelationBiz;

    @Cacheable(value = "redisCache", key = "targetClass + ':' + methodName + ':' + #mainstayNo + ':' + #payChannelNo")
    public MainstayChannelRelation getMainstayAccountInfo(String mainstayNo, String payChannelNo) {
        return mainstayChannelRelationBiz.getByMainstayNoAndPayChannelNo(mainstayNo,payChannelNo);
    }
}
