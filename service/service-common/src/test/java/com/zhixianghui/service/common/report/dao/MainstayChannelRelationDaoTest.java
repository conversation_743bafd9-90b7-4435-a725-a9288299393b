package com.zhixianghui.service.common.report.dao;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.MainstayChannelRelationDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.service.common.core.dao.MainstayChannelRelationDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-09 14:56
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class MainstayChannelRelationDaoTest {
    @Autowired
    private MainstayChannelRelationDao mainstayChannelRelationDao;
    @Test
    public void listAllMainstay() {
        List<MainstayChannelRelation> list = mainstayChannelRelationDao.listAllMainstay();
        System.out.println(list);
    }

    @Test
    public void listCustomPage() {

    }
}