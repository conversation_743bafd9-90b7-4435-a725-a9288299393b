package com.zhixianghui.service.common.approval.merchant;

import com.google.common.collect.Maps;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.service.common.core.biz.approvalflow.ApprovalFlowDetailBiz;
import com.zhixianghui.service.common.core.dao.ApprovalFlowDetailDao;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-08-10 17:17
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class ApprovalFlowDetailBizTest extends TestCase {
    @Autowired
    private ApprovalFlowDetailDao approvalFlowDetailDao;

    @Autowired
    private ApprovalFlowDetailBiz approvalFlowDetailBiz;

    @Test
    public void a(){
        ApprovalFlowDetail approvalFlowDetail = new ApprovalFlowDetail();
        approvalFlowDetail.setCreateTime(new Date());
        approvalFlowDetail.setUpdateTime(new Date());
        approvalFlowDetail.setApprovalFlowId(0L);
        approvalFlowDetail.setHandlerId(0L);
        approvalFlowDetail.setHandlerName("aaa");
        approvalFlowDetail.setHandlerType(0);
        approvalFlowDetail.setStepNum(0);
        approvalFlowDetail.setStatus(0);
        approvalFlowDetail.setOperatorName("");
        approvalFlowDetail.setPlatform(100);

        ApprovalFlowDetail approvalFlowDetaila = new ApprovalFlowDetail();
        approvalFlowDetaila.setCreateTime(new Date());
        approvalFlowDetaila.setUpdateTime(new Date());
        approvalFlowDetaila.setApprovalFlowId(0L);
        approvalFlowDetaila.setHandlerId(100L);
        approvalFlowDetaila.setHandlerName("bbb");
        approvalFlowDetaila.setHandlerType(0);
        approvalFlowDetaila.setStepNum(0);
        approvalFlowDetaila.setStatus(0);
        approvalFlowDetaila.setOperatorName("");
        approvalFlowDetaila.setPlatform(100);

        approvalFlowDetailDao.insert(approvalFlowDetaila);
//        approvalFlowDetailMapper.insert(Lists.newArrayList(approvalFlowDetaila,approvalFlowDetail));
    }

    @Test
    public void listBy(){
//        Map<String, Object> paramMap = Maps.newHashMap();
//        paramMap.put("handlerId", 39L);
//        paramMap.put("platform", PlatformSource.OPERATION.getValue());
////        paramMap.put("groupby", "APPROVAL_FLOW_ID");
//        List<ApprovalFlowDetail> list = approvalFlowDetailMapper.listBy(paramMap);
//        System.out.println(list);

        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("approvalFlowId", 28);
        paramMap.put("handlerId", 45);
        List<ApprovalFlowDetail> list = approvalFlowDetailDao.listBy(paramMap,"STEP_NUM ASC");
    }

    @Test
    public void agreeApprovalDetail(){
        approvalFlowDetailBiz.agreeApprovalDetail(37L, 42L, PlatformSource.OPERATION.getValue(), FlowTopicType.CREATE_MERCHANT.getName(),"",false);
    }


    @Test
    public void disAgreeApprovalDetail(){
        approvalFlowDetailBiz.disAgreeApprovalDetail(33L, 42L, PlatformSource.OPERATION.getValue(), FlowTopicType.CREATE_MERCHANT.getName(),"",false);
    }


}