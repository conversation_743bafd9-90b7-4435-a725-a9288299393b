package com.zhixianghui.service.common.approval;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowDetailFacade;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 创建商户审批流测试用例
 * 发起人-风控/销售会签-结束  (结束后商户创建在web端)
 * @date 2020-08-18 10:54
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class TestCreateMerchantFlow {

    @Reference
    private ApprovalFlowDetailFacade approvalFlowDetailFacade;
    @Reference
    private ApprovalFlowFacade approvalFlowFacade;

    long initiatorId = 47L;

    @Test
    public void testCreateMerchantFlow(){
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setStepNum(0);
        approvalFlow.setInitiatorId(initiatorId);
        approvalFlow.setInitiatorName("xxx");
        approvalFlow.setFlowTopicType(FlowTopicType.CREATE_MERCHANT.getValue());
        approvalFlow.setCreateTime(new Date());
        approvalFlow.setUpdateTime(new Date());
        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
        approvalFlow.setExtInfo(null);
        approvalFlow.setPlatform(PlatformSource.OPERATION.getValue());
        approvalFlowFacade.createFlow(approvalFlow);
    }

    @Test
    public void testMerchantVerifyFlow(){
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setStepNum(0);
        approvalFlow.setInitiatorId(initiatorId);
        approvalFlow.setInitiatorName("xxx");
        approvalFlow.setFlowTopicType(FlowTopicType.MERCHANT_VERIFY.getValue());
        approvalFlow.setCreateTime(new Date());
        approvalFlow.setUpdateTime(new Date());
        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
        approvalFlow.setExtInfo(null);
        approvalFlow.setPlatform(PlatformSource.OPERATION.getValue());
        approvalFlowFacade.createFlow(approvalFlow);
    }

    @Test
    public void agreeApprovalDetail(){
        ApprovalInfoVo last = approvalFlowDetailFacade.agreeApprovalDetail(56L,47L,PlatformSource.OPERATION.getValue(),FlowTopicType.CREATE_MERCHANT.getName(),"",false);
        System.out.println(last);
    }

    @Test
    public void disAgreeApprovalDetail(){
        approvalFlowDetailFacade.disAgreeApprovalDetail(59L,47L,PlatformSource.OPERATION.getValue(),FlowTopicType.CREATE_MERCHANT.getName(),"",false);
    }

    @Test
    public void listByApprovalFlowIdAndHandlerId(){
        List<ApprovalFlowDetail> list = approvalFlowDetailFacade.listByApprovalFlowIdAndHandlerId(55L,50L,false);
        System.out.println(list);
    }
}
