package com.zhixianghui.service.common.config;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import com.zhixianghui.service.common.ServiceCommonApp;
import com.zhixianghui.service.common.core.biz.config.BankCardBinBiz;
import com.zhixianghui.service.common.core.biz.config.InvoiceCategoryBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@SpringBootTest(classes = ServiceCommonApp.class)
@RunWith(SpringRunner.class)
public class TsBankCardBin {
    @Autowired
    private BankCardBinBiz bankCardBinBiz;

    @Test
    public void get(){
        System.out.println(JSON.toJSONString(bankCardBinBiz.getCardBinByCardNo("111111111111111111")));
    }
}
