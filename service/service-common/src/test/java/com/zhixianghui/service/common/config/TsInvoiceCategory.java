package com.zhixianghui.service.common.config;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import com.zhixianghui.service.common.ServiceCommonApp;
import com.zhixianghui.service.common.core.biz.config.InvoiceCategoryBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@SpringBootTest(classes = ServiceCommonApp.class)
@RunWith(SpringRunner.class)
public class TsInvoiceCategory {
    @Autowired
    private InvoiceCategoryBiz categoryBiz;

    @Test
    public void insert(){
        InvoiceCategory category = new InvoiceCategory();
        category.setUpdator("");
        category.setInvoiceCategoryCode("xxxa");
        category.setInvoiceCategoryName("智商税");
        category.setVersion(0);
        category.setCreateTime(new Date());
        category.setUpdateTime(new Date());
        categoryBiz.insert(category);
    }

    @Test
    public void get(){
        InvoiceCategory category = categoryBiz.getByCategoryCode("xxxa");
        System.out.println(JSON.toJSONString(category));
    }
}
