package com.zhixianghui.service.common.dic;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.ds.simple.SimpleDataSource;
import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import org.junit.Test;

import javax.sql.DataSource;
import java.util.*;

public class DictionaryFromEnumTest {
    /**
     * 开发环境
     */
    private final static String DATABASE_URL = "******************************************************************************************************************************************************";
    private final static  String DATABASE_USER = "root";
    private final static  String DATABASE_PASSWORD = "hjzx!321";


    /**
     * 测试环境环境
     */
//    private final static String DATABASE_URL = "******************************************************************************************************************************************************";
//    private final static  String DATABASE_USER = "root";
//    private final static  String DATABASE_PASSWORD = "hjzx!321";

    @Test
    public  void createDictionary() {
        final List<Tuple> enums = ListUtil.of(
                // 如果要导入多个，则创建多个Tuple即可
//                new Tuple("com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum", "推送规则"),
                new Tuple("com.zhixianghui.facade.trade.enums.ProxyRefundReasonEnum", "退款原因")
        );
        this.operateCreat(enums);
    }

    /**
     * 此段代码一般不需要修改
     * @param enums
     */
    private void operateCreat(List<Tuple> enums) {
        for (Tuple tuple : enums) {
            try {
                final Class<Enum<?>> aClass = (Class<Enum<?>>) Class.forName(tuple.get(0));
                String remark = tuple.get(1);

                final List<String> fieldNames = EnumUtil.getFieldNames(aClass);

                Map<String, Object> map = new HashMap<>();
                for (String fieldName : fieldNames) {
                    final Map<String, Object> nameFieldMap = EnumUtil.getNameFieldMap(aClass, fieldName);
                    System.out.println(JSONUtil.toJsonPrettyStr(nameFieldMap));
                    nameFieldMap.forEach((k, v)->{
                        String newValue = map.get(k) == null ? String.valueOf(v) : map.get(k) + "--" + v;
                        map.put(k, newValue);
                    });
                }
                DataDictionary dataDictionary = new DataDictionary();
                dataDictionary.setDataName(aClass.getSimpleName());
                dataDictionary.setRemark(remark);
                dataDictionary.setSystemType(SystemTypeEnum.COMMON_MANAGEMENT.getValue());
                System.out.println(JSONUtil.toJsonPrettyStr(map));
                List<DataDictionary.Item> itemList = new ArrayList<>();
                map.forEach((k,v)->{
                    final String[] split = String.valueOf(v).split("--");
                    DataDictionary.Item dataDictionaryItem = new DataDictionary.Item();
                    dataDictionaryItem.setCode(split[0]);
                    dataDictionaryItem.setDesc(split[1]);
                    dataDictionaryItem.setFlag(split[2]);
                    itemList.add(dataDictionaryItem);
                });
                dataDictionary.setItemList(itemList);
                DataSource dataSource = new SimpleDataSource(DATABASE_URL, DATABASE_USER, DATABASE_PASSWORD);

                Db.use(dataSource).insert(Entity.create("tbl_data_dictionary")
                        .set("version",0)
                        .set("creator","admin")
                        .set("data_name",dataDictionary.getDataName())
                        .set("data_info", JSONUtil.toJsonStr(dataDictionary.getItemList()))
                        .set("remark",dataDictionary.getRemark())
                        .set("create_time",new Date())
                        .set("modify_time",new Date())
                        .set("modify_operator","admin")
                        .set("system_type",dataDictionary.getSystemType()));

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
