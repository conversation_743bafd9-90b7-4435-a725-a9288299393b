package com.zhixianghui.service.common.report.external;

import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.service.common.core.biz.report.external.JoinPayBankCardReportBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-13 10:15
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class JoinPayBankCardReportBizTest {

    @Autowired
    private JoinPayBankCardReportBiz joinPayBankCardReportBiz;
    @Test
    public void report() throws Exception {
        ReportEntity reportEntity = new ReportEntity();
        reportEntity.setEmployerNo("M00000028");
        reportEntity.setEmployerName("btest");
        reportEntity.setMainstayNo("S000013");
        reportEntity.setMainstayName("btest");
        reportEntity.setPayChannelNo("JOIN_PAY");
        reportEntity.setPayChannelName("汇聚支付");
        reportEntity.setChannelType(100);

//        joinPayBankCardReportBiz.report(reportEntity);
    }
}