package com.zhixianghui.service.common.core.biz.report;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-29 15:52
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class PayChannelBizTest {

    @Autowired
    private PayChannelBiz payChannelBiz;
    @Test
    public void create() {
//        PayChannel payChannel = new PayChannel();
//        payChannel.setPayChannelNo("JOINPAY");
//        payChannel.setPayChannelName("汇聚");
//        payChannel.setChannelType(ChannelTypeEnum.BANK.getValue());
//        payChannel.setCreateTime(new Date());
//        payChannel.setUpdateTime(new Date());
//        payChannel.setUpdateOperator("");
//        payChannel.setCreateOperator("");
//        payChannel.setStatus(OpenOffEnum.OPEN.getValue());
//        payChannel.setDescription("");
//        payChannelBiz.create(payChannel);
    }

    @Test
    public void listPage(){
        PageResult<List<PayChannel>> pageResult = payChannelBiz.listPage(Maps.newHashMap(), PageParam.newInstance(1,2000));
        System.out.println(pageResult);
    }
}