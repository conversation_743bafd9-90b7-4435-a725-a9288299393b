package com.zhixianghui.service.common.core.biz.report;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.MainstayChannelRelationDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @description
 * @date 2020-10-30 09:22
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class MainstayChannelRelationBizTest {

    @Autowired
    private MainstayChannelRelationBiz mainstayChannelRelationBiz;

    @Test
    public void listCustomPage(){
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayName", "");
        paramMap.put("mainstayNo","");
        List<String> payChannelNoList = Lists.newArrayList();
        payChannelNoList.add("JOINPAY");
        payChannelNoList.add("FUMIN");
        paramMap.put("payChannelNoList", payChannelNoList);
        PageResult<List<MainstayChannelRelationDto>> pageResult = mainstayChannelRelationBiz.listCustomPage(paramMap, PageParam.newInstance(1,10));
        System.out.println(pageResult);
    }
}