package com.zhixianghui.service.common.rocketmqmanage;

import com.zhixianghui.service.common.ServiceCommonApp;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description: test
 * @author: xingguang li
 * @created: 2020/09/09 17:48
 */
@SpringBootTest(classes = ServiceCommonApp.class)
@RunWith(SpringRunner.class)
public class RmqMessageTest {
//
//    @Autowired
//    private MqMessageRecordDao mqMessageRecordDao;
//
//    @Test
//    @Ignore
//    public void createRmqMessageTest() {
//        MqMessageRecord mqMessageRecord = new MqMessageRecord();
//        mqMessageRecord.setCreateDate(new Date());
//        mqMessageRecord.setCreateTime(new Date());
//        mqMessageRecord.setKeys("keys");
//        mqMessageRecord.setTags("tags");
//        mqMessageRecord.setTopic("topic");
//        mqMessageRecord.setMsgid("111111111111");
//        mqMessageRecord.setMessage("message");
//        mqMessageRecordDao.insert(mqMessageRecord);
//    }
//
//    @Test
//    public void queryRmqMessageTest() {
//        Map<String,Object> param = new HashMap<>();
//        param.put("topic", "topic");
//        long count = mqMessageRecordDao.countBy(param);
//        System.out.println(count);
//    }
}
