package com.zhixianghui.service.common.approval.impl;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.service.common.core.biz.approvalflow.ApprovalFlowBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
* @description 
* <AUTHOR>
* @date 2020-08-10 10:31
**/

@RunWith(SpringRunner.class)
@SpringBootTest
public class ApprovalFlowImplTest {
    @Autowired
    private ApprovalFlowBiz approvalFlowBiz;

    /**
     * 新建走审批流
     */
    @Test
    public void createMerchantFlow() {
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setVersion(0);
        approvalFlow.setInitiatorId(47L);
        approvalFlow.setInitiatorName("谢锦世");
        approvalFlow.setFlowTopicType(FlowTopicType.CREATE_MERCHANT.getValue());
        approvalFlow.setCreateTime(new Date());
        approvalFlow.setUpdateTime(new Date());
        approvalFlow.setEndTime(new Date());
        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
        approvalFlow.setPlatform(PlatformSource.OPERATION.getValue());
        approvalFlow.setStepNum(0);
        approvalFlowBiz.createFlow(approvalFlow);
    }

    /**
     * 已存在走审批流
     */
    @Test
    public void createMerchantFlow2() {
        ApprovalFlow approvalFlow = approvalFlowBiz.getById(23L);
        approvalFlowBiz.createFlow(approvalFlow);
    }


    @Test
    public void listByOperatorIdAndPlatformSource() {
//        Map<String,Object> flowParamMap = Maps.newHashMap();
//        PageResult<List<ApprovalFlow>> result = approvalFlowBiz.listByOperatorIdAndPlatformSource(39L, PlatformSource.OPERATION.getValue(),flowParamMap, PageParam.newInstance(1,10));
//        System.out.println(result);
    }

    @Test
    public void updateExtInfo(){
        ApprovalFlow approvalFlow = approvalFlowBiz.getById(147L);
        UpdateExtInfoVo updateExtInfoVo = new UpdateExtInfoVo();
        updateExtInfoVo.setApprovalFlowId(approvalFlow.getId());
        updateExtInfoVo.setExtInfo(approvalFlow.getExtInfo());
        updateExtInfoVo.setHandlerId(approvalFlow.getInitiatorId());
        updateExtInfoVo.setHandlerName(approvalFlow.getInitiatorName());
        updateExtInfoVo.setPlatform(approvalFlow.getPlatform());
        approvalFlowBiz.updateExtInfo(updateExtInfoVo,false);
    }

}