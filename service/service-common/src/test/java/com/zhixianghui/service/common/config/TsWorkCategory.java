package com.zhixianghui.service.common.config;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.facade.common.entity.config.WorkCategory;
import com.zhixianghui.service.common.ServiceCommonApp;
import com.zhixianghui.service.common.core.biz.config.WorkCategoryBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/10
 **/
@SpringBootTest(classes = ServiceCommonApp.class)
@RunWith(SpringRunner.class)
public class TsWorkCategory {
    @Autowired
    private WorkCategoryBiz workCategoryBiz;

    @Test
    public void tsInsert(){
        WorkCategory category = new WorkCategory();
        category.setUpdator("");
        category.setUpdateTime(new Date());
        category.setWorkCategoryCode("123");
        category.setWorkCategoryName("长方体移动工程师");
        category.setParentId(2L);
        category.setVersion(0);
        category.setCreateTime(new Date());

        workCategoryBiz.insert(category);
    }

    @Test
    public void tsGet(){
        WorkCategory s = workCategoryBiz.getByCategoryCode("123");
        System.out.println(JSON.toJSONString(s));
    }
}
