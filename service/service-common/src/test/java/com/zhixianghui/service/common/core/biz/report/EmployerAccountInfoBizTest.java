package com.zhixianghui.service.common.core.biz.report;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.EmployerAccountInfoDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-12 09:38
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class EmployerAccountInfoBizTest {
    @Autowired
    private EmployerAccountInfoBiz employerAccountInfoBiz;

    @Test
    public void listPage() {
        Map<String,Object> paramMap = Maps.newHashMap();
        PageResult<List<EmployerAccountInfoDto>> list = employerAccountInfoBiz.listCustomPage(paramMap, PageParam.newInstance(1,10));
        System.out.println(list);
        System.out.println("===========================>下一页");
        list = employerAccountInfoBiz.listCustomPage(paramMap, PageParam.newInstance(2,10));
        System.out.println(list);
    }
}