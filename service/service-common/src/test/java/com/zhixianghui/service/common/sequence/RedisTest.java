package com.zhixianghui.service.common.sequence;

import com.zhixianghui.middleware.leaf.common.Result;
import com.zhixianghui.service.common.service.RedisService;
import com.zhixianghui.service.common.service.SequenceService;
import lombok.extern.log4j.Log4j2;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;

@Log4j2
public class RedisTest extends BaseTestCase {

    @Autowired
    private RedisService redisService;
    @Autowired
    private SequenceService sequenceService;

    private CompletionService<Long> completionService;
    private CompletionService<List<String>> completionService2;

    @PostConstruct
    public void init() {
        ExecutorService executor = new ThreadPoolExecutor(200, 1000,
                60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(10000));

        completionService = new ExecutorCompletionService<>(executor, new ArrayBlockingQueue<>(10000));
        completionService2 = new ExecutorCompletionService<>(executor, new ArrayBlockingQueue<>(10000));
    }

    @Test
    public void testGenId() throws InterruptedException, ExecutionException {
        int n = 10000;

        Set<Long> set = new HashSet<>();

        for (int i = 0; i < n; ++i) {
            completionService.submit(() -> {
                Result id = redisService.getId("redis-keygen-test");
                log.info("gen id: " + id.getId());
                return id.getId();
            });
        }

        for (int i = 0; i < n; ++i) {
            Long id = completionService.take().get();
            if (set.contains(id)) {
                log.error("生成了重复id：" + id);
            } else {
                set.add(id);
            }
        }

        System.out.print("\n生成数据范围：");
        long[][] range = getRange(set.toArray(new Long[0]));
        for (long[] r : range) {
            System.out.print("[" + r[0] + ", " + r[1] +"]");
        }
    }

    @Test
    public void testGenIdList() throws InterruptedException, ExecutionException {
        int n = 100;

        Set<String> set = new HashSet<>();

        for (int i = 0; i < n; ++i) {
            completionService2.submit(() -> {
                List<String> idList = sequenceService.nextRedisIdList("test-tag",10,100);
                log.info("gen idList: " + idList);
                return idList;
            });
        }

        for (int i = 0; i < n; ++i) {
            List<String> idList = completionService2.take().get();
            idList.forEach(
                    id->{
                        if (set.contains(id)) {
                            log.error("生成了重复id：" + id);
                        } else {
                            set.add(id);
                        }
                    }
            );

        }

//        System.out.print("\n生成数据范围：");
//        long[][] range = getRange(set.toArray(new Long[0]));
//        for (long[] r : range) {
//            System.out.print("[" + r[0] + ", " + r[1] +"]");
//        }
    }

    @Test
    public void testGenIdDigits() throws InterruptedException, ExecutionException {
        int n = 100;        // 生成id的个数
        int digits = 10;     // id的长度

        Set<Long> set = new HashSet<>();

        for (int i = 0; i < n; ++i) {
            completionService.submit(() -> {
                Result id = redisService.getId("redis-keygen-test2", digits);
                log.info("gen id: " + id.getId());
                return id.getId();
            });
        }

        for (int i = 0; i < n; ++i) {
            Long id = completionService.take().get();
            set.add(id);
        }

        System.out.print("\n生成数据范围：");
        long[][] range = getRange(set.toArray(new Long[0]));
        for (long[] r : range) {
            System.out.print("[" + r[0] + ", " + r[1] +"]");
        }
    }

    /**
     * 统计数据范围
     */
    public long[][] getRange(Long[] ids) {
        List<long[]> res = new ArrayList<>();

        Arrays.sort(ids);
        Long start = ids[0], end = ids[0];
        for (int i = 1 ; i < ids.length; ++i) {
            if (ids[i] <= end + 1) {
                end = ids[i];
            } else {
                res.add(new long[] {start, end});
                start = end = ids[i];
            }
        }
        res.add(new long[] {start, end});
        return res.toArray(new long[][] {});
    }
}
