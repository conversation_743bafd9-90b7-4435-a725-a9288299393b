<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-service</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>service-export</artifactId>

    <description>统计/报表服务</description>

    <properties>
        <itext.version>7.1.2</itext.version>
        <itext.html2pdf.version>2.0.2</itext.html2pdf.version>
    </properties>

    <build>
        <finalName>${appName}</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- redis -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
<!--            <version>3.16.3</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>redisson-spring-data-25</artifactId>-->
<!--                    <groupId>org.redisson</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.redisson</groupId>-->
<!--            <artifactId>redisson-spring-data-21</artifactId>-->
<!--            <version>3.16.3</version>-->
<!--        </dependency>-->
        <!-- redis -->
        <!-- RocketMQ -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>
        <!-- RocketMQ -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency> <!-- 把nacos作为配置中心时需要引入的依赖 -->
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.18</version>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>

        <!--fastdfs-->
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>component-starter</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>fastdfs-client</artifactId>
            <version>${fastdfs-client.version}</version>
        </dependency>
        <!--fsatdfs-->
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-util</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-service</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-export</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-common</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-merchant</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-trade</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-employee</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-fee</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-risk-control</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-banklink</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-notify</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-flow</artifactId>

        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-account-invoice</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-data</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.2</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>kernel</artifactId>
            <version>${itext.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>io</artifactId>
            <version>${itext.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>layout</artifactId>
            <version>${itext.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>forms</artifactId>
            <version>${itext.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>pdfa</artifactId>
            <version>${itext.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>font-asian</artifactId>
            <version>${itext.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>${itext.html2pdf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.gw.joinpay</groupId>
            <artifactId>keyou-unionHsmApi</artifactId>
            <version>1.7</version>
        </dependency>

        <dependency>
            <groupId>com.gw.joinpay</groupId>
            <artifactId>keyou-commons</artifactId>
            <version>0.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.4</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>api-base</artifactId>
        </dependency>
    </dependencies>
</project>
