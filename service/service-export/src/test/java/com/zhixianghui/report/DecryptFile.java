package com.zhixianghui.report;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.zhixianghui.common.util.utils.AESUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DecryptFile {

    public static void main(String[] args) {
        final ExcelReader excelReader = ExcelUtil.getReader("D:\\freeData.xls");
        final List<Map<String, Object>> mapList = excelReader.readAll();

        final List<Map<String, Object>> collect = mapList.stream().map(objectMap -> {
            final String encryptKeyId = (String) objectMap.get("ENCRYPT_KEY_ID");
            final String receiveName = (String) objectMap.get("RECEIVE_NAME");
            final String decryptName = AESUtil.decryptECB(receiveName, encryptKeyId);

            final String idCardNo = (String) objectMap.get("RECEIVE_ID_CARD_NO");
            final String decryptIdCardNo = AESUtil.decryptECB(idCardNo, encryptKeyId);

            final String cardFrontUrl = (String) objectMap.get("ID_CARD_FRONT_URL");
            if (StringUtils.equals("https://static.hjzxh.com/", cardFrontUrl)) {
                objectMap.put("ID_CARD_FRONT_URL", null);
            }
            final String cardBackUrl = (String) objectMap.get("ID_CARD_BACK_URL");
            if (StringUtils.equals("https://static.hjzxh.com/", cardBackUrl)) {
                objectMap.put("ID_CARD_BACK_URL", null);
            }

            objectMap.put("RECEIVE_NAME", decryptName);
            objectMap.put("RECEIVE_ID_CARD_NO", decryptIdCardNo);
            return objectMap;
        }).collect(Collectors.toList());

        ExcelUtil.getWriter("D:\\个人交付数据.xls").write(collect).flush();

    }

}
