package com.zhixianghui.report;

import com.google.common.collect.Lists;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;

public class ThreadPoolTest {
    public static void main(String[] args){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("executeTaskExecutor-");
        //线程池维护线程的最少数量
        executor.setCorePoolSize(1);
        //线程池所使用的缓冲队列
        executor.setQueueCapacity(2);
        //线程池维护线程的最大数量
        executor.setMaxPoolSize(2);
        //线程池维护线程所允许的空闲时间(即额外的线程等待多久之后会被自动销毁)
        executor.setKeepAliveSeconds(10);
//        executor.setAwaitTerminationSeconds(1);
        executor.initialize();

        List<String> strList = Lists.newArrayList("China","America","Japan","Canada","Cuba","Thailand","Russia");

        strList.stream().forEach(item->{
            try {
                executor.execute(() -> {
                    System.out.print(Thread.currentThread().getId()+"--");System.out.println(item);
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            } catch (RejectedExecutionException e) {
                System.out.println("拒绝执行");
            }
        });
        System.out.println("执行完成");
    }
}
