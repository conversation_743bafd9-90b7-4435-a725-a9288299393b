package com.zhixianghui.report;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;

public class TestLombok {

    public static void main(String[] args) {
        Course c = new Course();
        System.out.println(JSON.toJSONString(c));
        System.out.println(JSONUtil.toJsonStr(c));
    }
}

@Data
class Course{

    public String getPrice() {
        return "100";
    }

    public String getName() {
        return "java编程";
    }


}
