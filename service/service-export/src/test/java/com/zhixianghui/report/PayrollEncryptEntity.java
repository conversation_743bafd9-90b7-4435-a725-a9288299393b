package com.zhixianghui.report;

import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * 对敏感信息加解密entity
 * 提供对敏感信息的加解密、数据的脱敏、敏感信息明文的Md5
 *
 * <AUTHOR>
 * @date 2019/9/16
 */

public class PayrollEncryptEntity extends BaseEntity {
    private static final long serialVersionUID = 1976656173191000527L;
    /**
     * 敏感信息加密秘钥对应的ID 需要先对这个进行设置 ,默认随机获取一个秘钥
     */
    private Long encryptKeyId;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人证件号
     */
    private String receiveIdCardNo;

    /**
     * 持卡人手机号
     */
    private String receivePhoneNo;

    /**
     * 银行卡号
     */
    private String receiveBankCardNo;


    /**
     * 持卡人姓名明文的Md5
     */
    private String receiveNameMd5;

    /**
     * 持卡人证件号明文的Md5
     */
    private String receiveIdCardNoMd5;

    /**
     * 持卡人手机号明文的Md5
     */
    private String receivePhoneNoMd5;

    /**
     * 银行卡号明文的Md5
     */
    private String receiveBankCardNoMd5;






    /**
     * 获取银行卡号明文
     *
     * @return
     */
    public String getReceiveBankCardNoDecrypt() {
        return decryptData(this.receiveBankCardNo);
    }

    /**
     * 获取手机号明文
     *
     * @return
     */
    public String getReceivePhoneNoDecrypt() {
        return decryptData(this.receivePhoneNo);
    }

    /**
     * 获取证件号明文
     *
     * @return
     */
    public String getReceiveIdCardNoDecrypt() {
        return decryptData(this.receiveIdCardNo);
    }

    /**
     * 获取持卡人姓名明文
     *
     * @return
     */
    public String getReceiveNameDecrypt() {
        return decryptData(this.receiveName);
    }

    /**
     * 获取脱敏的持卡人姓名
     *
     * @return
     */
    public String getReceiveNameDesensitize() {
        return DesensitizeUtil.handleNameDesenCenter(getReceiveNameDecrypt());
    }

    /**
     * 获取脱敏的证件号
     *
     * @return
     */
    public String getReceiveIdCardNoDesensitize() {
        return DesensitizeUtil.handleIdNumOrBankCardNo(getReceiveIdCardNoDecrypt());
    }

    /**
     * 获取脱敏的手机号
     *
     * @return
     */
    public String getReceivePhoneNoDesensitize() {
        return DesensitizeUtil.handleMobile(getReceivePhoneNoDecrypt());
    }

    /**
     * 获取脱敏的银行卡号(可能是银行卡、手机号码、邮箱)
     *
     * @return
     */
    public String getReceiveBankCardNoDesensitize() {
        String bankCardNoDecrypt = getReceiveBankCardNoDecrypt();
        if(StringUtil.isNotEmpty(bankCardNoDecrypt)){
            if(ValidateUtil.isMobile(bankCardNoDecrypt)){
                return DesensitizeUtil.handleMobile(bankCardNoDecrypt);
            } else if(ValidateUtil.isEmail(bankCardNoDecrypt)){
                return DesensitizeUtil.handleEmailDesenCenter(bankCardNoDecrypt);
            } else{
                return DesensitizeUtil.handleIdNumOrBankCardNo(bankCardNoDecrypt);
            }
        }
        return null;
    }

    public void setReceiveBankCardNoEncrypt(String receiveBankCardNo) {
        this.receiveBankCardNo = encryptData(receiveBankCardNo);
        this.receiveBankCardNoMd5 = StringUtil.toString(MD5Util.getMixMd5Str(receiveBankCardNo), "");
    }

    public void setReceiveNameEncrypt(String receiveName) {
        this.receiveName = encryptData(receiveName);
        this.receiveNameMd5 = StringUtil.toString(MD5Util.getMixMd5Str(receiveName), "");
    }

    /**
     * 加密明文数据
     *
     * @param plainData
     * @return
     */
    private String encryptData(String plainData) {
        if (StringUtils.isBlank(plainData)) {
            return "";
        }
        EncryptKeys.EncryptKey encryptKey = EncryptKeys.getEncryptKeyById(this.encryptKeyId);
        if (encryptKey == null) {
            throw new IllegalArgumentException("加密秘钥不存在");
        }

        if (1 == encryptKey.getEncryptKeyType()) {
            return SmUtils.encryptSM4EcbByEncryptor(plainData, encryptKey.getEncryptKeyStr());
        } else if(2 == encryptKey.getEncryptKeyType()){
            return SmUtils.encryptSM4Ecb(plainData, encryptKey.getEncryptKeyStr());
        }else {
            throw new IllegalArgumentException("暂不支持的加密方式：" + encryptKey.getEncryptKeyType());
        }
    }

    /**
     * 解密数据
     *
     * @param encryptData
     * @return
     */
    private String decryptData(String encryptData) {
        if (StringUtils.isBlank(encryptData)) {
            return null;
        }
        EncryptKeys.EncryptKey encryptKey = EncryptKeys.getEncryptKeyById(this.encryptKeyId);
        if (encryptKey == null) {
            throw new IllegalArgumentException("加密秘钥不存在");
        }

        if (encryptKey.getEncryptKeyType() == 1) {
            return SmUtils.decryptSM4EcbByEncryptor(encryptData, encryptKey.getEncryptKeyStr());
        } else if(encryptKey.getEncryptKeyType() == 2){
            return SmUtils.decryptSM4Ecb(encryptData, encryptKey.getEncryptKeyStr());
        }else {
            throw new IllegalArgumentException("暂不支持的解密方式：" + encryptKey.getEncryptKeyType());
        }

    }

    public Long getEncryptKeyId() {
        return encryptKeyId;
    }

    public void setEncryptKeyId(Long encryptKeyId) {
        this.encryptKeyId = encryptKeyId;
    }

    public void genRandomEncryptKeyId() {
        this.encryptKeyId = EncryptKeys.getRandomEncryptKeyId();
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public String getReceiveIdCardNo() {
        return receiveIdCardNo;
    }

    public void setReceiveIdCardNo(String receiveIdCardNo) {
        this.receiveIdCardNo = receiveIdCardNo;
    }

    public String getReceivePhoneNo() {
        return receivePhoneNo;
    }

    public void setReceivePhoneNo(String receivePhoneNo) {
        this.receivePhoneNo = receivePhoneNo;
    }

    public String getReceiveBankCardNo() {
        return receiveBankCardNo;
    }

    public void setReceiveBankCardNo(String receiveBankCardNo) {
        this.receiveBankCardNo = receiveBankCardNo;
    }

    public String getReceiveNameMd5() {
        return receiveNameMd5;
    }

    public void setReceiveNameMd5(String receiveNameMd5) {
        this.receiveNameMd5 = receiveNameMd5;
    }

    public String getReceiveIdCardNoMd5() {
        return receiveIdCardNoMd5;
    }

    public void setReceiveIdCardNoMd5(String receiveIdCardNoMd5) {
        this.receiveIdCardNoMd5 = receiveIdCardNoMd5;
    }

    public String getReceivePhoneNoMd5() {
        return receivePhoneNoMd5;
    }

    public void setReceivePhoneNoMd5(String receivePhoneNoMd5) {
        this.receivePhoneNoMd5 = receivePhoneNoMd5;
    }

    public String getReceiveBankCardNoMd5() {
        return receiveBankCardNoMd5;
    }

    public void setReceiveBankCardNoMd5(String receiveBankCardNoMd5) {
        this.receiveBankCardNoMd5 = receiveBankCardNoMd5;
    }
}
