package com.zhixianghui.report;

import cn.keyou.commons.coding.Hex;
import cn.keyou.hsm.config.Configer;
import cn.keyou.hsm.config.Loader;
import cn.keyou.hsm.enumeration.SecretKeyScheme;
import cn.keyou.hsm.enumeration.SecretKeyType;
import cn.keyou.hsm.server.HSMServer;
import cn.keyou.hsm.server.HSMServerGroup;
import cn.keyou.hsm.service.SM4Service;
import cn.keyou.hsm.service.SecretKey;
import cn.keyou.hsm.util.DataPadding;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECKeyGenerationParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECPoint;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Security;
import java.util.HashMap;
import java.util.Map;

/**
 * SM4国密加密算法
 *
 * <AUTHOR>
 * @date 2019/7/8
 */

public class SmUtils {

    private final static String ALGORITHM = "SM4";
    private final static String SM4_ECB_PADDING = "SM4/ECB/PKCS5Padding";
    private static final int SM4_KEY_SIZE = SM4Service.SM4_KEY_SIZE;

    public static final String PUBLIC_KEY = "publicKey";
    public static final String PRIVATE_KEY = "privateKey";

    static {
        Security.addProvider(new BouncyCastleProvider());
        //是否使用长连接
        Loader.setProperty(Configer.LONG_CONNECT, "YES");
        HSMServerGroup serverGroup = new HSMServerGroup();
        //加密机配置
        HSMServer server = new HSMServer("************", 1818, 60000);
        serverGroup.addServer(server);
        Loader.loadHSMServers(serverGroup);
    }


    /**
     * 国密SM4软加密
     *
     * @param data 需要加密的数据
     * @param key  16位的秘钥
     * @return 加密后的Base64字符串
     * @throws Exception
     */
    public static String encryptSM4Ecb(String data, String key)  {
        try{

            Cipher cipher = Cipher.getInstance(SM4_ECB_PADDING, BouncyCastleProvider.PROVIDER_NAME);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);

            byte[] bytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(bytes);
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 国密SM4软解密
     *
     * @param encryptData
     * @param key
     * @return
     * @throws Exception
     */
    public static String decryptSM4Ecb(String encryptData, String key)  {
        try{
            Cipher cipher = Cipher.getInstance(SM4_ECB_PADDING, BouncyCastleProvider.PROVIDER_NAME);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            byte[] bytes = cipher.doFinal(Base64.decodeBase64(encryptData));
            return new String(bytes, StandardCharsets.UTF_8);
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 通过加密机加密
     *
     * @param data 需要加密的数据
     * @param key  32位纯数字key
     * @return 加密后的Base64字符串
     */
    public static String encryptSM4EcbByEncryptor(String data, String key) {
        SecretKey secKey = new SecretKey(SecretKeyScheme.SM4, SecretKeyType.ZEK, key);
        byte[] bytes = SM4Service.encryptWithECB(secKey, DataPadding.encodeWithPKCS5(data.getBytes(StandardCharsets.UTF_8), SM4_KEY_SIZE));
        return Hex.Base64.encode(bytes);
    }

    /**
     * 通过加密机解密
     *
     * @param encryptData 加密机加密出来的base64串
     * @param key         32位纯数字key
     * @return
     */
    public static String decryptSM4EcbByEncryptor(String encryptData, String key) {
        SecretKey secKey = new SecretKey(SecretKeyScheme.SM4, SecretKeyType.ZEK, key);
        return new String(DataPadding.decodeWithPKCS5(SM4Service.decryptWithECB(secKey, Hex.Base64.decode(encryptData))));
    }

    /**
     * 生成SM2密钥对
     * @return
     */
    public static Map<String, String> genSM2KeyPair() {
    	X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
    	ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
    	ECKeyPairGenerator keyPairGenerator = new ECKeyPairGenerator();
    	try {
			keyPairGenerator.init(new ECKeyGenerationParameters(domainParameters, SecureRandom.getInstance("SHA1PRNG")));
		} catch (NoSuchAlgorithmException e) {
		}

    	AsymmetricCipherKeyPair asymmetricCipherKeyPair = keyPairGenerator.generateKeyPair();

    	BigInteger privatekey = ((ECPrivateKeyParameters) asymmetricCipherKeyPair.getPrivate()).getD();
    	String privateKeyHex = privatekey.toString(16);

    	ECPoint ecPoint = ((ECPublicKeyParameters) asymmetricCipherKeyPair.getPublic()).getQ();
    	String publicKeyHex = org.bouncycastle.util.encoders.Hex.toHexString(ecPoint.getEncoded(false));

    	Map<String, String> keyMap = new HashMap<>(2);
        keyMap.put(PUBLIC_KEY, publicKeyHex);
        keyMap.put(PRIVATE_KEY, privateKeyHex);

        return keyMap;
    }

    /**
     * 解密（对应JS的加密）
     * @param cipherData
     * @param privateKey
     * @return
     */
    public static String decrypt4JS(String cipherData, String privateKey) {
    	byte[] cipherDataByte = org.bouncycastle.util.encoders.Hex.decode(cipherData);

    	X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
    	ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());

    	//刚才的私钥Hex，先还原私钥
    	BigInteger privateKeyD = new BigInteger(privateKey, 16);
    	ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(privateKeyD, domainParameters);

    	SM2Engine sm2Engine = new SM2Engine();
        sm2Engine.init(false, privateKeyParameters);

        String result = null;
        try {
            byte[] arrayOfBytes = java.util.Base64.getDecoder().decode(sm2Engine.processBlock(cipherDataByte, 0, cipherDataByte.length));
            result = new String(arrayOfBytes);
        } catch (Exception e) {
        }
        return result;
    }

    public static void main(String[] s) {
    	//publicKey:048dd180b82ff399798f1fd79a654625229c95832f6c1ee186eee5ec70c3a99fc036f1892dd98d47e9f303b4bf42050b9f9b6bd56a899c59fb08f737cc5e349f5d
    	System.out.println(decrypt4JS("04501c7191b8651360ed4c45b73985f44faf5690a8bc5f84e9247f2413cc4023857e3df2b2f69f7f0f1fa49e6d58f173a6009055e68d9da0a59ec3a309eaac2670171d32229f93587813bcf02fecb21b86773b6c2cb01a3593523458c6035f19736aae65316e1334ba4ce24cc957a4cb41931a1c65fe4663d016c1da476c3de72f8a7d951e",
				"de83e5061e15a4bd97977f761b1ae350071de6486a5d8a8175590b1db5cd1c72"));
    }
}
