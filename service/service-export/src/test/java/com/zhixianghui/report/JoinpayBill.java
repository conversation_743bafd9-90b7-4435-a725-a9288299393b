package com.zhixianghui.report;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import com.zhixianghui.common.util.utils.JsonUtil;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class JoinpayBill {
    private final static String PATH = "E:/huidan/";
    private final static String FILE_NAME = "虎牙1月.csv";


    public static void main(String[] args) {
        final CsvReader csvReader = CsvUtil.getReader();
        final CsvData csvData = csvReader.read(FileUtil.file(PATH + FILE_NAME));
        final List<CsvRow> rows = csvData.getRows();
        List<String> failRows = new ArrayList<>();
        int rowNum = 0, total = rows.size();
        for (CsvRow csvRow : rows) {
            String employerNo = null;
            String idCardNo = null;
            String receiveName = null;
            try {
                System.out.println("-------执行------->> " + (rowNum+1) + "/" + total);
                final String completeTime = csvRow.get(0);
                final String merchantOrderNo = csvRow.get(1);
                employerNo = csvRow.get(2);
                final String employerName = csvRow.get(3);
                final String serviceMchNo = csvRow.get(4);
                final String serviceMchName = csvRow.get(5);
                final String bankName = csvRow.get(6);
                final String bankCard = csvRow.get(7);
                final String reName = csvRow.get(8);
                final String amount = csvRow.get(9);
                final String key = csvRow.get(10);

                PayrollRecordItem recordItem = new PayrollRecordItem();
                recordItem.setBankName(bankName);
                recordItem.setEncryptKeyId(Long.parseLong(key));
                recordItem.setReceiveBankCardNo(bankCard);
                recordItem.setReceiveName(reName);
                recordItem.setServiceMchNo(serviceMchNo);
                recordItem.setServiceMchName(serviceMchName);
                recordItem.setMchOrderNo(merchantOrderNo);
                recordItem.setCompleteTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(completeTime));
                recordItem.setReceiveAmount(new BigDecimal(amount));

                idCardNo = recordItem.getReceiveIdCardNo();
                receiveName = recordItem.getReceiveName();

                String dir = PATH + "export/" + employerName+"/"+completeTime.substring(0,7)+"/" + recordItem.getReceiveNameDecrypt();;
                if (!FileUtil.isDirectory(dir)) {
                    FileUtil.mkdir(dir);
                }
                String path = dir+"/" + merchantOrderNo + ".pdf";
                new PayrollPdfBiz().createReceiptPdf(recordItem, new File(path));
            } catch (Exception e) {
                failRows.add(employerNo + "-" + idCardNo + "-" + receiveName);
            }
            rowNum++;
        }
        if (failRows.isEmpty()) {
            System.out.println("订单全部生成成功，总共" + total + "笔订单");
        } else {
            System.out.println("生成失败的订单为：" + JsonUtil.toString(failRows));
        }
    }
}
