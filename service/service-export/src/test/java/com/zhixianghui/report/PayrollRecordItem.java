package com.zhixianghui.report;

import com.zhixianghui.common.util.utils.JsonUtil;

import java.io.Serializable;

/**
 * 薪税汇记录明细
 *
 * <AUTHOR>
 * @date 2019-10-12
 */
public class PayrollRecordItem extends PayrollEncryptEntity {

    private static final long serialVersionUID = 1L;

    //columns START
    /**
     * 更新时间
     */
    private java.util.Date updateTime;

    /**
     * 完成时间
     */
    private java.util.Date completeTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 打款请求流水号
     */
    private String remitPlatTrxNo;

    /**
     * 通道流水号
     */
    private String bankTrxNo;

    /**
     * 发起方式，参考LaunchWayEnum
     */
    private Integer launchWay;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 服务方商户编号
     */
    private String serviceMchNo;

    /**
     * 服务方商户名称
     */
    private String serviceMchName;

    /**
     * 通道编号
     */
    private String channelCode;
    /**
     * 通道编号
     */
    private String channelName;

    /**
     * 通道商户编号
     */
    private String channelMchNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行简码
     */
    private String bankCode;

    /**
     * 实收金额
     */
    private java.math.BigDecimal receiveAmount;

    /**
     * 订单金额
     */
    private java.math.BigDecimal orderAmount;

    /**
     * 订单手续费
     */
    private java.math.BigDecimal orderFee;

    private Integer processStatus;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorDesc;

    private Integer grantType;

    /**
     * 汇聚主体id
     */
    private String joinpayMainstayId;
    /**
     * 服务平台id
     */
    private String servicePlatId;

    private String jsonStr;

    /**
     * 与jsonStr对应，库中存储字段为jsonStr
     */
    private  JsonEntity jsonEntity = new JsonEntity();

    public static class JsonEntity implements Serializable {
        private static final long serialVersionUID = -5884118134401514004L;

        private Integer serviceMchAccountCreditStatus;

        /**
         * 商户网商银行账户
         */
        private String mchBankAccountNo;

        /**
         * 商户银行开户名
         */
        private String mchBankAccountName;
        /**
         * 打款备注
         */
        private String remark;

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getMchBankAccountName() {
            return mchBankAccountName;
        }

        public void setMchBankAccountName(String mchBankAccountName) {
            this.mchBankAccountName = mchBankAccountName;
        }

        //columns END
        public String getMchBankAccountNo() {
            return mchBankAccountNo;
        }

        public void setMchBankAccountNo(String mchBankAccountNo) {
            this.mchBankAccountNo = mchBankAccountNo;
        }

        public Integer getServiceMchAccountCreditStatus() {
            return serviceMchAccountCreditStatus;
        }

        public void setServiceMchAccountCreditStatus(Integer serviceMchAccountCreditStatus) {
            this.serviceMchAccountCreditStatus = serviceMchAccountCreditStatus;
        }
    }

    @Deprecated
    public String getJsonStr() {
        return JsonUtil.toString(this.jsonEntity);
    }

    @Deprecated
    public void setJsonStr(String jsonStr) {
        this.jsonStr = jsonStr;
        this.jsonEntity = JsonUtil.toBean(jsonStr, JsonEntity.class);
    }

    public String getJoinpayMainstayId() {
        return joinpayMainstayId;
    }

    public void setJoinpayMainstayId(String joinpayMainstayId) {
        this.joinpayMainstayId = joinpayMainstayId;
    }

    public String getServicePlatId() {
        return servicePlatId;
    }

    public void setServicePlatId(String servicePlatId) {
        this.servicePlatId = servicePlatId;
    }

    public JsonEntity getJsonEntity() {
        return jsonEntity;
    }

    public void setJsonEntity(JsonEntity jsonEntity) {
        this.jsonEntity = jsonEntity;
    }

    public Integer getGrantType() {
        return grantType;
    }

    public void setGrantType(Integer grantType) {
        this.grantType = grantType;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getChannelMchNo() {
        return channelMchNo;
    }

    public void setChannelMchNo(String channelMchNo) {
        this.channelMchNo = channelMchNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    /**
     * 更新时间
     */
    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 完成时间
     */
    public java.util.Date getCompleteTime() {
        return this.completeTime;
    }

    /**
     * 完成时间
     */
    public void setCompleteTime(java.util.Date completeTime) {
        this.completeTime = completeTime;
    }

    /**
     * 商户批次号
     */
    public String getMchBatchNo() {
        return this.mchBatchNo;
    }

    /**
     * 商户批次号
     */
    public void setMchBatchNo(String mchBatchNo) {
        this.mchBatchNo = mchBatchNo;
    }

    /**
     * 平台批次号
     */
    public String getPlatBatchNo() {
        return this.platBatchNo;
    }

    /**
     * 平台批次号
     */
    public void setPlatBatchNo(String platBatchNo) {
        this.platBatchNo = platBatchNo;
    }

    /**
     * 商户订单号
     */
    public String getMchOrderNo() {
        return this.mchOrderNo;
    }

    /**
     * 商户订单号
     */
    public void setMchOrderNo(String mchOrderNo) {
        this.mchOrderNo = mchOrderNo;
    }

    /**
     * 平台流水号
     */
    public String getPlatTrxNo() {
        return this.platTrxNo;
    }

    /**
     * 平台流水号
     */
    public void setPlatTrxNo(String platTrxNo) {
        this.platTrxNo = platTrxNo;
    }

    /**
     * 打款请求流水号
     */
    public String getRemitPlatTrxNo() {
        return this.remitPlatTrxNo;
    }

    /**
     * 打款请求流水号
     */
    public void setRemitPlatTrxNo(String remitPlatTrxNo) {
        this.remitPlatTrxNo = remitPlatTrxNo;
    }

    /**
     * 通道流水号
     */
    public String getBankTrxNo() {
        return this.bankTrxNo;
    }

    /**
     * 通道流水号
     */
    public void setBankTrxNo(String bankTrxNo) {
        this.bankTrxNo = bankTrxNo;
    }

    /**
     * 发起方式，参考LaunchWayEnum
     */
    public Integer getLaunchWay() {
        return this.launchWay;
    }

    /**
     * 发起方式，参考LaunchWayEnum
     */
    public void setLaunchWay(Integer launchWay) {
        this.launchWay = launchWay;
    }

    /**
     * 商户编号
     */
    public String getMchNo() {
        return this.mchNo;
    }

    /**
     * 商户编号
     */
    public void setMchNo(String mchNo) {
        this.mchNo = mchNo;
    }

    /**
     * 商户名称
     */
    public String getMchName() {
        return this.mchName;
    }

    /**
     * 商户名称
     */
    public void setMchName(String mchName) {
        this.mchName = mchName;
    }

    /**
     * 服务方商户编号
     */
    public String getServiceMchNo() {
        return this.serviceMchNo;
    }

    /**
     * 服务方商户编号
     */
    public void setServiceMchNo(String serviceMchNo) {
        this.serviceMchNo = serviceMchNo;
    }

    /**
     * 服务方商户名称
     */
    public String getServiceMchName() {
        return this.serviceMchName;
    }

    /**
     * 服务方商户名称
     */
    public void setServiceMchName(String serviceMchName) {
        this.serviceMchName = serviceMchName;
    }

    /**
     * 通道编号
     */
    public String getChannelCode() {
        return this.channelCode;
    }

    /**
     * 通道编号
     */
    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    /**
     * 实收金额
     */
    public java.math.BigDecimal getReceiveAmount() {
        return this.receiveAmount;
    }

    /**
     * 实收金额
     */
    public void setReceiveAmount(java.math.BigDecimal receiveAmount) {
        this.receiveAmount = receiveAmount;
    }

    /**
     * 订单金额
     */
    public java.math.BigDecimal getOrderAmount() {
        return this.orderAmount;
    }

    /**
     * 订单金额
     */
    public void setOrderAmount(java.math.BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * 订单手续费
     */
    public java.math.BigDecimal getOrderFee() {
        return this.orderFee;
    }

    /**
     * 订单手续费
     */
    public void setOrderFee(java.math.BigDecimal orderFee) {
        this.orderFee = orderFee;
    }

    /**
     * 订单处理状态，参考ProcessStatusEnum
     */
    public Integer getProcessStatus() {
        return this.processStatus;
    }

    /**
     * 订单处理状态，参考ProcessStatusEnum
     */
    public void setProcessStatus(Integer processStatus) {
        this.processStatus = processStatus;
    }

    /**
     * 错误码
     */
    public String getErrorCode() {
        return this.errorCode;
    }

    /**
     * 错误码
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 错误描述
     */
    public String getErrorDesc() {
        return this.errorDesc;
    }

    /**
     * 错误描述
     */
    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }
}
