package com.zhixianghui.report;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.db.Db;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import cn.hutool.db.ds.simple.SimpleDataSource;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;

import javax.sql.DataSource;
import java.io.File;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.List;

public class JoinpayBillNew {
    private final static String PATH = "E:/huidan/";
    private final static String DIR = "export0808/";
    private final static String FILE_NAME = "20250709to汇聚.xlsx";

    public static void main(String[] args) {
        File file = new File(PATH + FILE_NAME);
        List<Object> objects = EasyExcel.read(file).sheet(0).doReadSync();

        DataSource ds = new SimpleDataSource(
                "***********************************************************************************************************************************************************",
                "weishengbao", "gY!ibIQQYbz9");
        Db db = DbUtil.use(ds);

        for (Object object : objects) {
//            JSONObject item = JSONUtil.parseObj(object);
//            String beginOfMonth = DateUtil.format(
//                    DateUtil.offsetMonth(DateUtil.beginOfMonth(DateUtil.parse(item.getStr("0"),"yyyy年MM月")).toJdkDate(),-1),"yyyy-MM-dd HH:mm:ss"
//            );
//            String endOfMonth = DateUtil.format(
//                    DateUtil.offsetMonth(DateUtil.endOfMonth(DateUtil.parse(item.getStr("0"),"yyyy年MM月")).toJdkDate(),1),
//                    "yyyy-MM-dd HH:mm:ss");
//
////            String mainstayName = item.getStr("1");
//            String recvName = item.getStr("1");
//            String mchName = item.getStr("2");
            String beginOfMonth="2022-01-01 00:00:00";
            String endOfMonth="2022-01-31 23:59:59";
            String mchName = "海南虎牙娱乐信息技术有限公司";
            String recvName = "余培源";


            String sql = "select\n " +
                    "        t.COMPLETE_TIME ,\n" +
                    "        t.MCH_ORDER_NO ,\n" +
                    "        t.MCH_NO ,\n" +
                    "        t.MCH_NAME ,\n" +
                    "        t.SERVICE_MCH_NO ,\n" +
                    "        t.SERVICE_MCH_NAME ,\n" +
                    "        t.BANK_NAME ,\n" +
                    "        t.RECEIVE_BANK_CARD_NO ,\n" +
                    "        t.RECEIVE_NAME ,\n" +
                    "        t.RECEIVE_AMOUNT ,\n" +
                    "        t.ENCRYPT_KEY_ID \n" +
                    "from\n" +
                    "        tbl_payroll_record_item t\n" +
                    "where\n" +
                    "        t.RECEIVE_NAME_MD5 =lower(md5(concat(md5('"+recvName+"'),'Joinpay')))\n" +
                    "        and t.PROCESS_STATUS = 100\n" +
                    "        and t.COMPLETE_TIME >= '"+beginOfMonth+"'\n" +
                    "        and t.COMPLETE_TIME <= '"+endOfMonth+"'\n" +
                    "        and t.MCH_NAME = '"+mchName+"'";

            try {
                List<Entity> entityList = db.query(sql);
                if (entityList.size() == 0) {
                    System.out.println(recvName+" 没有数据");
                }
                for (Entity entity : entityList) {
                    print(entity);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void print(Entity entity) throws Exception {

            final String completeTime = entity.getStr("complete_time");
            final String merchantOrderNo = entity.getStr("mch_order_no");
            final String employerNo =  entity.getStr("mch_no");
            final String employerName = entity.getStr("mch_name");
            final String serviceMchNo = entity.getStr("service_mch_no");
            final String serviceMchName = entity.getStr("service_mch_name");
            final String bankName = entity.getStr("bank_name");
            final String bankCard = entity.getStr("receive_bank_card_no");
            final String reName = entity.getStr("receive_name");
            final String amount = entity.getStr("receive_amount");
            final String key = entity.getStr("encrypt_key_id");

            PayrollRecordItem recordItem = new PayrollRecordItem();
            recordItem.setBankName(bankName);
            recordItem.setEncryptKeyId(Long.parseLong(key));
            recordItem.setReceiveBankCardNo(bankCard);
            recordItem.setReceiveName(reName);
            recordItem.setServiceMchNo(serviceMchNo);
            recordItem.setServiceMchName(serviceMchName);
            recordItem.setMchOrderNo(merchantOrderNo);
            recordItem.setCompleteTime(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(completeTime));
            recordItem.setReceiveAmount(new BigDecimal(amount));
            String dir = PATH + DIR + employerName+"\\"+completeTime.substring(0,7)+"\\" + recordItem.getReceiveNameDecrypt();
            if (!FileUtil.isDirectory(dir)) {
                FileUtil.mkdir(dir);
            }
            String path = dir + "\\" + merchantOrderNo + ".pdf";
            try {
                new PayrollPdfBiz().createReceiptPdf(recordItem, new File(path));
            } catch (Exception e) {
//                System.out.println(path);
            }

    }

}
