package com.zhixianghui.report;

import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;
import com.zhixianghui.common.util.utils.DateUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

@Component("payrollPdfBiz")
public class PayrollPdfBiz {

    private final static float LOGO_LEFT = 45F;
    private final static float TABLE_MARGIN_LEFT = 40F;
    private final static float TABLE_MARGIN_TOP = 5F;
    private final static float TABLE_MARGIN_BOTTOM_40 = 40F;
    private final static float TABLE_MARGIN_BOTTOM = 0F;
    private final static float FONT_SIZE_14 = 14F;
    private final static float FONT_SIZE_18 = 18F;
    private final static float FONT_SIZE_20 = 20F;
    private final static float PAGE_TOP_MARGIN = 50F;
    private final static float PAGE_BOTTOM_MARGIN = 10F;
    private final static String FONT_PROGRAM = "STSongStd-Light";
    private final static String FONT_ENCODING = "UniGB-UCS2-H";
    private final static Color COLOR_1 = new DeviceRgb(246, 245, 236);
    private final static Color COLOR_2 = new DeviceRgb(242, 234, 218);
    private final static Color FONT_COLOR_1 = new DeviceRgb(51, 51, 51);
    private static PageSize PAGE_SIZE_A4 = PageSize.A4;
    private static byte[] logoNoBackColorBytes;
    private static byte[] certificateBytes;

    private static float TABLE_WIDTH = PAGE_SIZE_A4.getWidth() - 70F;

    static {
        try {
            certificateBytes = IOUtils.toByteArray(PayrollPdfBiz.class.getResourceAsStream("/image/certificate.png"));
            logoNoBackColorBytes = IOUtils.toByteArray(PayrollPdfBiz.class.getResourceAsStream("/image/joinpay_logo_no_background.png"));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 电子回单下载
     * @throws Exception
     */
    public void createReceiptPdf(PayrollRecordItem recordItem, File file) throws Exception {
        PdfWriter pdfWriter = new PdfWriter(file);

        Document document = createDocument(pdfWriter, new PageSize(PAGE_SIZE_A4));
        document.getPdfDocument().addEventHandler(PdfDocumentEvent.START_PAGE, new PageBackgroundsEvent());
        Table contentTable = createTable(3);

        this.addBoldCell(contentTable, null, TextAlignment.RIGHT, FONT_SIZE_18,1, 3, "电子回单");
        this.addContentCell(contentTable, null, TextAlignment.RIGHT, FONT_SIZE_14,1, 3, DateUtil.formatDateTime(new Date()));
        this.addLine(contentTable, null,1, 3);
        this.addContentCell(contentTable, null, TextAlignment.LEFT, FONT_SIZE_20,1, 3, "汇聚支付");
        this.addContentCell(contentTable, null, TextAlignment.LEFT, FONT_SIZE_20,1, 1, recordItem.getServiceMchNo());
        this.addContentCell(contentTable, null, TextAlignment.RIGHT, FONT_SIZE_20,1, 2, recordItem.getServiceMchName());

        this.addBoldCell(contentTable, COLOR_2, TextAlignment.LEFT, FONT_SIZE_20,1, 3, "对方账户");
        this.addContentCell(contentTable, COLOR_2, TextAlignment.LEFT, FONT_SIZE_20,1, 3, StringUtils.defaultString(recordItem.getBankName()));

        this.addContentCell(contentTable, COLOR_2, TextAlignment.LEFT, FONT_SIZE_20,1, 1, recordItem.getReceiveBankCardNoDecrypt());
        String receiveName = recordItem.getReceiveNameDecrypt();
        if (receiveName.contains("·")) { //处理少数名族姓名中的点：“·”
            PdfFont sysFont = PdfFontFactory.createFont("c://windows//fonts//simsun.ttc,1", PdfEncodings.IDENTITY_H, false);//仅适用于windows环境
            this.addContentCell(contentTable, COLOR_2, TextAlignment.RIGHT, sysFont, FONT_SIZE_20,1, 2, receiveName);
        } else {
            this.addContentCell(contentTable, COLOR_2, TextAlignment.RIGHT, FONT_SIZE_20,1, 2, receiveName);
        }

        this.addBoldCell(contentTable, null, TextAlignment.LEFT, FONT_SIZE_20,1, 3, "交易信息");
        this.addContentCell(contentTable, null, TextAlignment.LEFT, FONT_SIZE_20,1, 1, "商户订单号");
        this.addContentCell(contentTable, null, TextAlignment.RIGHT, FONT_SIZE_20,1, 2, recordItem.getMchOrderNo());

        this.addContentCell(contentTable, null, TextAlignment.LEFT, FONT_SIZE_20,1, 1, "交易完成时间");
        this.addContentCell(contentTable, null, TextAlignment.RIGHT, FONT_SIZE_20, 1, 2, DateUtil.formatDateTime(recordItem.getCompleteTime()));

        this.addContentCell(contentTable, null, TextAlignment.LEFT, FONT_SIZE_20,1, 1, "交易类型");
        this.addContentCell(contentTable, null, TextAlignment.RIGHT, FONT_SIZE_20,1, 2, "（转出）代付");

        this.addContentCell(contentTable, null, TextAlignment.LEFT, FONT_SIZE_20,1, 1, "交易状态");
        this.addContentCell(contentTable, null, TextAlignment.RIGHT, FONT_SIZE_20,1, 2, "成功");

        this.addContentCell(contentTable, null, TextAlignment.LEFT, FONT_SIZE_20,1, 1, "交易金额");
        this.addContentCell(contentTable, null, TextAlignment.RIGHT, FONT_SIZE_20,1, 2, "人民币 " + recordItem.getReceiveAmount());

        this.addContentCellLeft_top(contentTable, null, FONT_SIZE_20,1, 1, null, "备注信息");
        this.addContentCellRight_top(contentTable, null, FONT_SIZE_20,1, 2, null, StringUtils.defaultString(recordItem.getJsonEntity().getRemark()));

        document.add(contentTable);

        Table footTable = new Table(1);
        footTable.setFixedPosition(TABLE_MARGIN_LEFT, TABLE_MARGIN_BOTTOM_40, TABLE_WIDTH);
        footTable.setKeepTogether(true);

        this.addLine(footTable, null,1, 1);
        this.addContentCell(footTable, null, TextAlignment.LEFT, FONT_SIZE_14,1, 1, "温馨提示：每笔电子回单pdf文件只能做一笔记账，不可重复记账。");

        document.add(footTable);

        // 生成汇聚图标/印章
        this.createImages(logoNoBackColorBytes, LOGO_LEFT, 720, 180, document, 1);
        this.createImages(certificateBytes, 400, 250, 145, document);
        try {
            document.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        try {
            pdfWriter.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    public static void main(String[] args) throws Exception{

        String bankName = "中国工商银行";
        String bankCard = "6212262201032429419";
        String reName = "张婷婷";
        String serviceMchNo = "***************";
        String serviceMchName = "河南宝岭信息科技有限公司";

        String merchantOrderNo = "2021091316391827576978908700000";
        String completeTime = "2021-09-13 16:40:27";
        String amount = "2190.20";

        String path = "C:\\Users\\<USER>\\Desktop\\pinzheng\\" + reName + "\\" + merchantOrderNo + ".pdf";


        PayrollRecordItem recordItem = new PayrollRecordItem();
        recordItem.setBankName(bankName);
        recordItem.genRandomEncryptKeyId();
        recordItem.setReceiveBankCardNoEncrypt(bankCard);
        recordItem.setReceiveNameEncrypt(reName);
        recordItem.setServiceMchNo(serviceMchNo);
        recordItem.setServiceMchName(serviceMchName);

        recordItem.setMchOrderNo(merchantOrderNo);
        recordItem.setCompleteTime(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(completeTime));
        recordItem.setReceiveAmount(new BigDecimal(amount));
        new PayrollPdfBiz().createReceiptPdf(recordItem, new File(path));

//        PayrollOrder payrollOrder = new PayrollOrder();
//        payrollOrder.setMchNo("8888888888888888888");
//        payrollOrder.setMchName("哈哈哈哈哈哈哈哈哈哈或或");
//        payrollOrder.setServiceMchName("呀呀呀呀呀呀晕晕");
//        payrollOrder.setServiceMchNo("**************");
//        payrollOrder.setSuccessOrderAmount(new BigDecimal(50000));
//        payrollOrder.setCreateTime(new Date());
//        payrollOrder.setCompleteTime(new Date());
//        payrollOrder.setMchBatchNo("**********");
//        payrollOrder.setPlatBatchNo("***************");
//        payrollOrder.setMchBatchName("接近接近军军");
//        new PayrollPdfBiz().createAccountVoucherPdf(payrollOrder, new File("C:\\Users\\<USER>\\Desktop\\************.pdf"));
    }

    private static class PageBackgroundsEvent implements IEventHandler {
        @Override
        public void handleEvent(Event event) {
            PdfDocumentEvent docEvent = (PdfDocumentEvent) event;
            PdfPage page = docEvent.getPage();

            PdfCanvas canvas = new PdfCanvas(page);
            Rectangle rect = page.getPageSize();
            //I used custom rgb for Color
            canvas  .saveState()
                    .setFillColor(COLOR_1)
                    .rectangle(rect.getLeft(), rect.getBottom(), rect.getWidth(), rect.getHeight())
                    .fillStroke()
                    .restoreState();
        }
    }

    private Document createDocument(PdfWriter pdfWriter, PageSize pageSize) {
        Document document = new Document(new PdfDocument(pdfWriter), pageSize);
        document.setLeftMargin(0);
        document.setRightMargin(0);
        document.setTopMargin(PAGE_TOP_MARGIN);
        document.setBottomMargin(PAGE_BOTTOM_MARGIN);
        PdfFont pdfFont = null;
        try {
            pdfFont = PdfFontFactory.createFont(FONT_PROGRAM, FONT_ENCODING, true);
        } catch (IOException e) {
            e.printStackTrace();
        }
        document.setFont(pdfFont);
        return document;
    }

    private Table createTable(int numColumns) {
        Table table = new Table(numColumns);
        table.setWidth(TABLE_WIDTH);
        table.setMarginLeft(TABLE_MARGIN_LEFT);
        table.setMarginTop(TABLE_MARGIN_TOP);
        table.setMarginBottom(TABLE_MARGIN_BOTTOM);
        table.setKeepTogether(true);
        return table;
    }

    /**
     * 获取表格中段落
     *
     * @return
     */
    private Paragraph getContentParagraph(String content) {
        return new Paragraph(content)
                .setFontSize(FONT_SIZE_14)
                .setTextAlignment(TextAlignment.CENTER)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setKeepTogether(true);
    }

    /**
     * 获取表格中段落
     *
     * @return
     */
    private Paragraph getContentParagraph(String content, float fontSize, TextAlignment textAlignment, PdfFont font) {
        Paragraph paragraph = new Paragraph(content)
                .setFontSize(fontSize)
                .setTextAlignment(textAlignment)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setKeepTogether(true);
        if (font != null) {
            paragraph.setFont(font);
        }
        return paragraph;
    }

    /**
     * 获取表格中段落
     *
     * @return
     */
    private Paragraph getBoldParagraph(String content, float fontSize, TextAlignment textAlignment) {
        return new Paragraph(content)
                .setFontSize(fontSize)
                .setBold()
                .setTextAlignment(textAlignment)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setKeepTogether(true)
                .setMarginTop(8);
    }

    /**
     * 单元格添加
     * @return
     */
    private Table addContentCell(Table table, Color color, TextAlignment textAlignment, float fontSize, int rowSpan, int colSpan, String contents) {
        if(StringUtils.isEmpty(contents)){
            contents = " ";
        }
        Cell cell = new Cell(rowSpan, colSpan);
        Paragraph paragraph = getContentParagraph(contents, fontSize, textAlignment, null);
        cell.add(paragraph)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setKeepTogether(true)
                .setBorder(Border.NO_BORDER)
                .setBackgroundColor(color)
                .setMargin(0)
                .setFontColor(FONT_COLOR_1);
        if(textAlignment == TextAlignment.RIGHT){
            cell.setPaddingRight(10);
        } else if(textAlignment == TextAlignment.LEFT){
            cell.setPaddingLeft(10);
        }
        table.addCell(cell);

        return table;
    }

    /**
     * 单元格添加
     * @return
     */
    private Table addContentCell(Table table, Color color, TextAlignment textAlignment, PdfFont front, float fontSize, int rowSpan, int colSpan, String contents) {
        if(StringUtils.isEmpty(contents)){
            contents = " ";
        }
        Cell cell = new Cell(rowSpan, colSpan);
        Paragraph paragraph = getContentParagraph(contents, fontSize, textAlignment, front);
        cell.add(paragraph)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setKeepTogether(true)
                .setBorder(Border.NO_BORDER)
                .setBackgroundColor(color)
                .setMargin(0)
                .setFontColor(FONT_COLOR_1);
        if(textAlignment == TextAlignment.RIGHT){
            cell.setPaddingRight(10);
        } else if(textAlignment == TextAlignment.LEFT){
            cell.setPaddingLeft(10);
        }
        table.addCell(cell);

        return table;
    }

    /**
     * 单元格添加
     */
    private Table addContentCellLeft_top(Table table, Color color, float fontSize, int rowSpan, int colSpan, PdfFont font, String... contents) {
        Cell cell = new Cell(rowSpan, colSpan);
        StringBuffer conetntBuffer = new StringBuffer();
        for (String content : contents) {
            conetntBuffer.append(content).append("\n");
        }
        String paragraphContent = conetntBuffer.substring(0, conetntBuffer.length() - 1);
        Paragraph paragraph = getContentParagraph(paragraphContent, fontSize, TextAlignment.LEFT, font);
        cell.add(paragraph)
                .setVerticalAlignment(VerticalAlignment.TOP)
                .setKeepTogether(true)
                .setBorder(Border.NO_BORDER)
                .setBackgroundColor(color)
                .setMargin(0)
                .setPaddingLeft(10);
        table.addCell(cell);

        return table;
    }

    /**
     * 单元格添加
     */
    private Table addBoldCell(Table table, Color color, TextAlignment textAlignment, float fontSize, int rowSpan, int colSpan, String... contents) {
        Cell cell = new Cell(rowSpan, colSpan);
        StringBuffer conetntBuffer = new StringBuffer();
        for (String content : contents) {
            conetntBuffer.append(content).append("\n");
        }
        String paragraphContent = conetntBuffer.substring(0, conetntBuffer.length() - 1);
        Paragraph paragraph = getBoldParagraph(paragraphContent, fontSize, textAlignment);
        cell.add(paragraph)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setKeepTogether(true)
                .setBorder(Border.NO_BORDER)
                .setBackgroundColor(color);
        if(textAlignment == TextAlignment.RIGHT){
            cell.setPaddingRight(10);
        } else if(textAlignment == TextAlignment.LEFT){
            cell.setPaddingLeft(10);
        }
        table.addCell(cell);

        return table;
    }

    /**
     * 单元格添加
     * @return
     */
    private Table addContentCellRight_top(Table table, Color color, float fontSize,int rowSpan, int colSpan, PdfFont font, String... contents) {
        Cell cell = new Cell(rowSpan, colSpan);
        StringBuffer conetntBuffer = new StringBuffer();
        for (String content : contents) {
            conetntBuffer.append(content).append("\n");
        }
        String paragraphContent = conetntBuffer.substring(0, conetntBuffer.length() - 1);
        Paragraph paragraph = getContentParagraph(paragraphContent, fontSize, TextAlignment.RIGHT, font);
        cell.add(paragraph)
                .setVerticalAlignment(VerticalAlignment.TOP)
                .setKeepTogether(true)
                .setBorder(Border.NO_BORDER)
                .setBackgroundColor(color)
                .setPaddingRight(10);
        table.addCell(cell);

        return table;
    }

    /**
     * 单元格添加
     *
     * @param table           表格
     * @param rowSpan         行横跨
     * @param colSpan         列横跨
     * @return
     */
    private Table addLine(Table table, Color color, int rowSpan, int colSpan) {
        Cell cell = new Cell(rowSpan, colSpan);
        cell.add(getContentParagraph(" "))
            .setVerticalAlignment(VerticalAlignment.MIDDLE)
            .setKeepTogether(true)
            .setBorderTop(Border.NO_BORDER)
            .setBorderLeft(Border.NO_BORDER)
            .setBorderRight(Border.NO_BORDER)
            .setBackgroundColor(color);
        table.addCell(cell);
        return table;
    }

    /**
     * 创建固定图片
     *
     */
    private void createImages(byte[] bytes, float left, float bottom, float imageWidth, Document document, int pageNum) throws Exception {
        ImageData imageData = ImageDataFactory.create(bytes, false);
        Image image = new Image(imageData, left, bottom, imageWidth);
        image.setPageNumber(pageNum);
        document.add(image);
    }

    /**
     * 创建固定图片
     */
    private void createImages(byte[] bytes, float left, float bottom, float imageWidth, Document document) throws Exception {
        ImageData imageData = ImageDataFactory.create(bytes, false);
        Image image = new Image(imageData, left, bottom, imageWidth);
        document.add(image);
    }
}
