package com.zhixianghui.report;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;
import com.google.common.collect.Maps;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.service.export.ServiceExportApp;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月21日 18:33:00
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ServiceExportApp.class)
public class TestFee {


    @Test
    public void test() throws IOException {
        File file = new File("E:\\桌面\\平台交付物明细表.docx");
        Map<String, Object> params = Maps.newHashMap();
        params.put("images", "{{@images1}}{{@images2}}{{@images3}}");
        XWPFTemplate.compile(file).render(params).writeToFile("E:\\桌面\\平台交付物明细表-test.docx");
        Map<String, Object> params2 = Maps.newHashMap();
        params2.put("images1", "http://deepoove.com/images/icecream.png");
        params2.put("images2", "http://deepoove.com/images/icecream.png");
        params2.put("images3", "http://deepoove.com/images/icecream.png");
        File file2 = new File("E:\\桌面\\平台交付物明细表-test.docx");
        XWPFTemplate.compile(file2).render(params2).writeToFile("E:\\桌面\\平台交付物明细表-test.docx");
    }
}