package com.zhixianghui.report;

import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.export.utils.GrantDateUtil;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.service.export.ServiceExportApp;
import com.zhixianghui.service.export.core.biz.StatisticsGrantBiz;
import com.zhixianghui.service.export.task.GrantUserTask;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * @description: 统计测试
 * @author: xingguang li
 * @created: 2020/11/17 10:36
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceExportApp.class)
public class StatisticsTest {

    @Autowired
    private StatisticsGrantBiz grantBiz;

    @Autowired
    private GrantUserTask grantUserTask;

    @Reference
    private RecordItemFacade recordItemFacade;

    @Test
    public void testGrant(){
        //grantBiz.getByIdCardNoSupplier("M00000157","whiterock","530427198707100534","S000037",new BigDecimal(2),false);
    }

//    @Test
//    public void testTrade(){
//        GrantDateUtil.GrantDateKeys grantDateKeys = GrantDateUtil.getGrantDate(new Date());
//        System.out.println(grantDateKeys);
//        Map<String,Object> map = recordItemFacade.getSumAmountGroup(grantDateKeys.getQueryStartDate(), grantDateKeys.getQueryEndDate(),
//                grantDateKeys.getQueryStartTime(), grantDateKeys.getQueryEndTime(),"571a849df4af6ac9a76daea56d644416");
//        System.out.println(map);
//    }

    @Test
    public void testUserTask(){
        grantUserTask.doAddTask(200);
    }


//    @Test
//    public void testGrant(){
//        grantBiz.getByIdCardNoSupplier("******************", "S000002", new BigDecimal("3000"));
//    }
}
