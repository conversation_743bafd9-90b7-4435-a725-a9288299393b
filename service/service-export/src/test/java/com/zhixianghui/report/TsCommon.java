package com.zhixianghui.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.opencsv.CSVWriter;
import com.opencsv.ICSVWriter;
import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import com.opencsv.exceptions.CsvRequiredFieldEmptyException;
import com.zhixianghui.common.statics.enums.export.ExportStatusEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.service.export.ServiceExportApp;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.service.export.core.biz.ApiBillBiz;
import com.zhixianghui.service.export.core.dao.ReportRecordDao;
import com.zhixianghui.service.export.core.vo.DailyOrderVo;
import com.zhixianghui.service.export.export.ExcelExportBiz;
import com.zhixianghui.service.export.helper.CacheBiz;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/6
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceExportApp.class)
public class TsCommon {
    @Autowired
    ExcelExportBiz excelExportBiz;
    @Autowired
    ReportRecordDao reportRecordDao;
    @Autowired
    CacheBiz cacheBiz;
    @Autowired
    private FastdfsClient fastdfsClient;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Autowired
    private ApiBillBiz apiBillBiz;

//    @Test
//    public void testAspect(){
//        RecordItem recordItem = new RecordItem();
//        recordItem.setEmployerNo("M00000153");
//        apiBillBiz.test(recordItem);
//    }

    @Test
    public void test() throws IOException, CsvDataTypeMismatchException, CsvRequiredFieldEmptyException {
        String filePath = System.getProperty("user.dir") + File.separator + RandomUtil.get16LenStr() + ".csv";
        FileWriter writer = new FileWriter(filePath);
        OrderItem orderItem = orderItemFacade.getByPlatTrxNo("E20220808001842252");
        DailyOrderVo dailyOrderVo = new DailyOrderVo();
        BeanUtil.copyProperties(orderItem,dailyOrderVo);
        CSVWriter csvWriter = new CSVWriter(writer,CSVWriter.DEFAULT_SEPARATOR,
                ICSVWriter.DEFAULT_QUOTE_CHARACTER,'\\',"\n");
        List<String> header = Arrays.stream(dailyOrderVo.getClass().getDeclaredFields()).map(
                x-> x.getAnnotation(CsvBindByName.class).column()).collect(Collectors.toList());
        csvWriter.writeNext(header.toArray(new String[header.size()]));
        StatefulBeanToCsv beanToCsv = new StatefulBeanToCsvBuilder<DailyOrderVo>(writer).build();
        beanToCsv.write(dailyOrderVo);
        csvWriter.close();
        writer.close();
    }

    @Test
    public void tsSave(){
        ExportRecord record = new ExportRecord();
        record.setUpdateTime(new Date());
        record.setFileNo(""+System.currentTimeMillis());
        record.setOperatorLoginName("<EMAIL>");
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
//        record.setReportType(ReportTypeEnum.MERCHANT_INFO_PMS.getValue());
        record.setExportStatus(ExportStatusEnum.CREATE.getValue());
        record.setParamJson("{}");
        record.setVersion(0);
        record.setCreateTime(new Date());

        ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
        info.setFieldCode("mchStatus");
        info.setFieldDesc("商户状态");
        info.setDataName("MchStatusEnum");
        record.getFieldInfoList().add(info);

        reportRecordDao.insert(record);
    }

    @Test
    public void tsExport(){
        ExportRecord record = reportRecordDao.getById(3L);
        excelExportBiz.export(record);
    }

    @Test
    public void tsDelete(){
        reportRecordDao.deleteById(13L);
    }
}
