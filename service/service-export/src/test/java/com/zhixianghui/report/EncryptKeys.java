package com.zhixianghui.report;

import java.util.*;

/**
 * AES/SM4加密秘钥
 * 只允许新增，不允许修改，否则会造成旧数据无法解密
 *
 * <AUTHOR>
 * @date 2019/7/11
 */

public class EncryptKeys {

    private static final Map<Long, EncryptKey> ENCRYPT_KEY_MAP = new HashMap<>();

    //初始化加密秘钥
    static {
        //除非你明确知道后果，否则建议不要修改已有的秘钥部分，只允许新增
        ENCRYPT_KEY_MAP.put(100L, new EncryptKey(100L, 1, "55555555555522222333331223333333"));
        ENCRYPT_KEY_MAP.put(101L, new EncryptKey(101L, 1, "11111155555522222333331223333333"));
        ENCRYPT_KEY_MAP.put(102L, new EncryptKey(102L, 2, "1111115555522222"));
        ENCRYPT_KEY_MAP.put(103L, new EncryptKey(103L, 2, "1111155555522222"));
        ENCRYPT_KEY_MAP.put(104L, new EncryptKey(104L, 2, "1111155555522222"));
        ENCRYPT_KEY_MAP.put(105L, new EncryptKey(105L, 2, "1111115555522222"));


    }

    public static EncryptKey getEncryptKeyById(long encryptKeyId) {
        return ENCRYPT_KEY_MAP.get(encryptKeyId);
    }

    public static long getRandomEncryptKeyId() {
//        List<Long> idList = new ArrayList<>(ENCRYPT_KEY_MAP.keySet());
        //暂时只用SM软加密
        Map<Long,EncryptKey> encryptKeyMap=new HashMap<>(ENCRYPT_KEY_MAP);
        encryptKeyMap.remove(100L);
        encryptKeyMap.remove(101L);
        List<Long> idList = new ArrayList<>(encryptKeyMap.keySet());
        return idList.get(new Random().nextInt(idList.size()));
    }

    public static List<Long> getAllEncryptKeyId() {
        return new ArrayList<>(ENCRYPT_KEY_MAP.keySet());
    }


    /**
     * 加密秘钥对象
     */
    public static class EncryptKey {
        /**
         * 加密秘钥ID
         */
        private Long encryptKeyId;
        /**
         * 加密方式 ：
         * 1=SM4硬加密
         * 2=SM4软加密
         * 3=AES软加密
         */
        private int encryptKeyType;
        /**
         * 加密的秘钥
         * SM4硬加密为32位16进制数据
         * SM4软加密为16位
         */
        private String encryptKeyStr;

        EncryptKey(Long encryptKeyId, int encryptKeyType, String encryptKeyStr) {
            this.encryptKeyId = encryptKeyId;
            this.encryptKeyStr = encryptKeyStr;
            this.encryptKeyType = encryptKeyType;
        }

        public long getEncryptKeyId() {
            return encryptKeyId;
        }

        public void setEncryptKeyId(Long encryptKeyId) {
            this.encryptKeyId = encryptKeyId;
        }

        public int getEncryptKeyType() {
            return encryptKeyType;
        }

        public void setEncryptKeyType(int encryptKeyType) {
            this.encryptKeyType = encryptKeyType;
        }

        public String getEncryptKeyStr() {
            return encryptKeyStr;
        }

        public void setEncryptKeyStr(String encryptKeyStr) {
            this.encryptKeyStr = encryptKeyStr;
        }
    }
}

