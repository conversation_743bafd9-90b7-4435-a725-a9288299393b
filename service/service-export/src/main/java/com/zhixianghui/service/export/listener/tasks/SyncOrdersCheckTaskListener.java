//package com.zhixianghui.service.export.listener.tasks;
//
//import cn.hutool.core.util.IdUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.zhixianghui.common.util.utils.DateUtil;
//import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
//import com.zhixianghui.facade.banklink.service.message.RobotFacade;
//import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
//import com.zhixianghui.facade.export.entity.CkOrders;
//import com.zhixianghui.facade.notify.service.NotifyFacade;
//import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
//import com.zhixianghui.facade.trade.service.OrderItemFacade;
//import com.zhixianghui.service.export.core.biz.services.CkOrdersService;
//import com.zhixianghui.service.export.listener.TaskRocketMQListener;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.Reference;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//
//@Slf4j
//@Component
//@RocketMQMessageListener(topic = "topic_sync_orders_check_task",consumeThreadMax = 1,consumerGroup = "SynOrdersCheckTaskConsumer")
//public class SyncOrdersCheckTaskListener extends TaskRocketMQListener<JSONObject>{
//
//    @Reference
//    private OrderItemFacade orderItemFacade;
//    @Reference
//    private NotifyFacade notifyFacade;
//    @Autowired
//    private CkOrdersService ckOrdersService;
//    @Reference
//    private RobotFacade robotFacade;
//
//    @Override
//    public void runTask(JSONObject jsonParam) {
//        final Date today = new Date();
//        final Date yesterday = DateUtil.addDay(today, -1);
//        final Date startTime = DateUtil.getDayStart(yesterday);
//        final Date endTime = DateUtil.getDayEnd(yesterday);
//
//        final int countCkOrder = ckOrdersService.count(new QueryWrapper<CkOrders>().ge("complete_time", startTime).lt("complete_time", endTime));
//
//        Map<String, Object> param = new HashMap<>();
//        param.put("completeBeginDate", startTime);
//        param.put("completeEndDate", endTime);
//        param.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
//        final Long countOrderItem = orderItemFacade.countOrderItem(param);
//
//        int countItem = countOrderItem == null ? 0 : countOrderItem.intValue();
//
//        StringBuilder stringBuilder = new StringBuilder("#### 每日数据同步校验["+DateUtil.formatDateTime(startTime)+"至"+DateUtil.formatDateTime(endTime)+"]-结果通知");
//        if (countCkOrder == countOrderItem) {
//            stringBuilder.append("\\n > 每日数据同步校验:无缺失");
//        }else {
//            stringBuilder.append("\\n > 每日数据同步校验，相差 "+ (countCkOrder-countItem) +"笔。\\n > 统计表"+countCkOrder+"笔，订单表"+countItem+"笔");
//        }
//        MarkDownMsg markDownMsg = new MarkDownMsg();
//        markDownMsg.setContent(stringBuilder.toString());
//        markDownMsg.setRobotType(RobotTypeEnum.TASK_NOTIFY_ROBOT.getType());
//        markDownMsg.setUnikey(IdUtil.fastSimpleUUID());
//        robotFacade.pushMarkDownAsync(markDownMsg);
//    }
//}
