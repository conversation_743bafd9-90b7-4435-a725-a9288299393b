package com.zhixianghui.service.export.core.biz;

import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.export.dto.GrantUserDto;
import com.zhixianghui.facade.export.entity.StatisticsGrant2user;
import com.zhixianghui.facade.export.entity.StatisticsRecordUnique;
import com.zhixianghui.facade.export.utils.GrantDateUtil;
import com.zhixianghui.facade.trade.bo.RecordItemGroupBo;
import com.zhixianghui.facade.trade.dto.RiskControlAmountDto;
import com.zhixianghui.facade.trade.dto.RiskControlNumDto;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.vo.statistics.StaticsGrantAmountVo;
import com.zhixianghui.facade.trade.vo.statistics.StaticsGrantCountVo;
import com.zhixianghui.service.export.core.dao.StatisticsGrant2userDao;
import com.zhixianghui.service.export.core.dao.StatisticsRecordUniqueDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * @description: 发放统计业务处理
 * @author: xingguang li
 * @created: 2020/11/02 17:35
 */
@Service
@Slf4j
public class StatisticsGrantBiz {

    @Autowired
    private StatisticsGrant2userDao statisticsGrant2userDao;
    @Reference
    private RecordItemFacade recordItemFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Autowired
    private StatisticsRecordUniqueDao statisticsRecordUniqueDao;
    @Reference
    private SignRecordFacade signRecordFacade;
    @Reference
    private UserInfoFacade userInfoFacade;

    public void insert(StatisticsGrant2user grant2user) {
        statisticsGrant2userDao.insert(grant2user);
    }

    /**
     * 获取用户的发放统计信息
     * @param idCardNo
     * @param supplierNo
     * @param orderAmount
     * @return
     */
    public GrantUserDto getByIdCardNoSupplier(Date startTradeTime,
                                              String platTrxNo,
                                              String employerNo,
                                              String userName,
                                              String idCardNo,
                                              String supplierNos,
                                              BigDecimal orderAmount,
                                              String supplierNo,
                                              String receiveAccount,
                                              String phone) {
        //首先拿到tmp查询以及动态查询的开始截止时间
        GrantDateUtil.GrantDateKeys grantDateKeys = GrantDateUtil.getGrantDate(new Date());
        //此时查出的归集好的tmp数据，此时的前提是tmpMonthEnd不为空
        GrantUserDto grantUserDto = getAmountData(grantDateKeys,idCardNo,supplierNos,orderAmount,employerNo,platTrxNo,startTradeTime,userName,receiveAccount);
//        if (containsPlatform){
//            GrantUserDto noSupplierGrantDto = getAmountData(grantDateKeys,idCardNo,supplierNo,orderAmount);
//            grantUserDto.setTotalMonthAmount(noSupplierGrantDto.getMonthAmount());
//            grantUserDto.setTotalPostponeYearAmount(noSupplierGrantDto.getPostponeYearAmount());
//            grantUserDto.setTotalYearAmount(noSupplierGrantDto.getYearAmount());
//        }

        //获取签约成功记录
        String idCardNoMd5 = MD5Util.getMixMd5Str(idCardNo);
        Map<String,Object> signMap = new HashMap<>();
        signMap.put("receiveNameMd5",MD5Util.getMixMd5Str(userName));
        signMap.put("receiveIdCardNoMd5",idCardNoMd5);
        signMap.put("mainstayNo",supplierNo);
        signMap.put("employerNo",employerNo);
        signMap.put("signStatus", SignStatusEnum.SIGN_SUCCESS.getValue());
        SignRecord signRecord = signRecordFacade.getOne(signMap);
        if(signRecord == null){
            grantUserDto.setSign(false);
        }

        //获取身份认证记录
        UserInfo userInfo = userInfoFacade.getByIdCardNoMd5(idCardNoMd5);
        if (userInfo == null || (StringUtils.isBlank(userInfo.getIdCardFrontUrl()) && StringUtils.isBlank(userInfo.getIdCardCopyUrl()))){
            grantUserDto.setAuth(false);
        }

        //判断要素
        grantUserDto.setThreeElement(!StringUtils.isAnyBlank(userName,idCardNo,receiveAccount));
        grantUserDto.setFourElement(!StringUtils.isAnyBlank(userName,idCardNo,receiveAccount,phone));
        return grantUserDto;
    }

    private GrantUserDto getAmountData(GrantDateUtil.GrantDateKeys grantDateKeys,String idCardNo, String supplierNo,
                                       BigDecimal orderAmount,String employerNo,String platTrxNo,Date startTradeTime,
                                       String userName,String receiveAccount) {

        List<String> supplierNoList = new ArrayList<>();
        if (StringUtils.isNotBlank(supplierNo)){
            supplierNoList = Arrays.asList(supplierNo.split(","));
        }

        StaticsGrantAmountVo tmpAmount = new StaticsGrantAmountVo();
        Map<String,Object> params = new HashMap<>();
        if (grantDateKeys.getTmpMonthEnd() != null) {
            //库里存的是md5数据，所以需要md5加密一次
            params.put("idCardNo", MD5Util.getMixMd5Str(idCardNo));
            params.put("year", grantDateKeys.getYear());
            params.put("month", grantDateKeys.getTmpMonthEnd());
            tmpAmount = calculateAmount(supplierNoList,statisticsGrant2userDao.getAmountGroup(params));
        }

        //实时去查询最近特定时间段的动态数据
        StaticsGrantAmountVo nearBy = calculateAmount(supplierNoList,
                recordItemFacade.getSumAmountGroup(grantDateKeys.getQueryStartDate(), grantDateKeys.getQueryEndDate(), grantDateKeys.getQueryStartTime(), DateUtil.getDayEnd(new Date()),MD5Util.getMixMd5Str(idCardNo)));

        //查询本月的，需替换param条件里面的completeBeginTime和completeEndTime，保留beginDate和endDate用于分库匹配
        StaticsGrantAmountVo monthAmount = calculateAmount(supplierNoList,
                recordItemFacade.getSumAmountGroup(grantDateKeys.getQueryStartDate(),grantDateKeys.getQueryEndDate(),GrantDateUtil.getTimeStrOfZero(DateUtil.getFirstOfMonth(new Date())), DateUtil.getDayEnd(new Date()),MD5Util.getMixMd5Str(idCardNo)));

        //查询本日的，需替换param条件里面的completeBeginTime和completeEndTime，保留beginDate和endDate用于分库匹配
        StaticsGrantAmountVo dailyAmount = calculateAmount(supplierNoList,
                recordItemFacade.getMchAmountGroup(grantDateKeys.getQueryStartDate(),grantDateKeys.getQueryEndDate(),GrantDateUtil.getTimeStrOfZero(new Date()), DateUtil.getDayEnd(new Date()),employerNo));

        //顺延年金额 =
        //假设目前10月5号，顺延年即去年11月初到今年10月底
        //得总顺延年金额等于=(去年10+1月) 到年底的金额 + 自然年金额
        Date nowDate = new Date();
        int currentMonth = DateUtil.getMonth(nowDate);
        StaticsGrantAmountVo yearEndAmount = new StaticsGrantAmountVo();
        //当前月份-12<0，说明统计需要加上去年部分月份统计
        if (currentMonth - 12 < 0){
            Map<String,Object> param = new HashMap<>();
            param.put("year",DateUtil.getYearToInt(nowDate) - 1);
            param.put("month",currentMonth);
            //加上endMonth主要是为了排除下面1月、2月重复统计
            //currentMonth = 1 => 查询范围为 1<x<11
            //currentMonth = 2 => 查询范围为 2<x<12
            //currentMonth = 3 => 查询范围为 3<x<13  不影响实际统计
            param.put("endMonth",currentMonth + 10);
            param.put("idCardNo",MD5Util.getMixMd5Str(idCardNo));
            //yearEndAmount = statisticsGrant2userDao.getYearEndAmount(param);
            yearEndAmount = calculateAmount(supplierNoList,statisticsGrant2userDao.getYearEndAmountGroup(param));
        }

        //顺延年金额注意
        //statisticsGrant2user表统计时间为x月2号3点统计（x-2）月的数据
        //因此需要对当前时间1月，2月作额外处理
        //1月-》实时查询去年11月，12月
        //2月-》实时查询去年12月
        StaticsGrantAmountVo lastYearEndAmount = new StaticsGrantAmountVo();
        if (currentMonth - 2 <= 0){
            Date startDate = DateUtil.getDayStart(DateUtil.getFirstOfMonth(DateUtil.addMonth(nowDate,-2)));
            Date endDate = DateUtil.getDayEnd(DateUtil.getLastOfMonth(DateUtil.addMonth(nowDate,-currentMonth)));
            lastYearEndAmount = calculateAmount(supplierNoList,
                    recordItemFacade.getSumAmountGroup(startDate,endDate,startDate,endDate,MD5Util.getMixMd5Str(idCardNo)));
        }

        //计算24H内相同订单交易次数
        StaticsGrantCountVo tradeTimes = calculateNums(supplierNoList,recordItemFacade.getCountTradeTimes(
                platTrxNo,employerNo,DateUtil.addSecond(startTradeTime,1),DateUtil.addDay(startTradeTime,-1),MD5Util.getMixMd5Str(idCardNo),
                MD5Util.getMixMd5Str(userName),MD5Util.getMixMd5Str(receiveAccount),orderAmount));

        GrantUserDto grantUserDto =  generateGrantUserDto(tmpAmount,nearBy,monthAmount,yearEndAmount,lastYearEndAmount,orderAmount,dailyAmount,tradeTimes);
        grantUserDto.setIdCardNo(idCardNo);
        grantUserDto.setSupplierNo(supplierNo);
        grantUserDto.setYear(grantDateKeys.getYear());
        grantUserDto.setMonth(String.valueOf(DateUtil.getMonth(new Date())));
        grantUserDto.setAge(DateUtil.getAgeByIdCard(idCardNo));
        log.info("风控统计参数：[{}]", JsonUtil.toString(grantUserDto));
        return grantUserDto;
    }

    private GrantUserDto generateGrantUserDto(StaticsGrantAmountVo tmpAmount, StaticsGrantAmountVo nearBy, StaticsGrantAmountVo monthAmount, StaticsGrantAmountVo yearEndAmount, StaticsGrantAmountVo lastYearEndAmount, BigDecimal orderAmount,StaticsGrantAmountVo dailyAmount,StaticsGrantCountVo tradeTimes) {
        GrantUserDto grantUserDto = new GrantUserDto();
        //总的月金额就等于：查询的月份金额 + 本次请求金额
        //计算月金额
        grantUserDto.setMonthAmount(monthAmount.getSpecialAmount().add(orderAmount).toString());
        //计算总月金额
        grantUserDto.setTotalMonthAmount(monthAmount.getTotalAmount().add(orderAmount).toString());

        //计算自然年金额
        //总的自然年金额就等于：tmp金额+最近月份金额 + 本次请求金额
        BigDecimal yearAmount = tmpAmount.getSpecialAmount().add(nearBy.getSpecialAmount()).add(orderAmount);
        grantUserDto.setYearAmount(yearAmount.toString());
        //计算总自然年金额
        BigDecimal totalYearAmount = tmpAmount.getTotalAmount().add(nearBy.getTotalAmount()).add(orderAmount);
        grantUserDto.setTotalYearAmount(totalYearAmount.toString());
        //计算顺延年金额
        grantUserDto.setPostponeYearAmount(yearAmount.add(yearEndAmount.getSpecialAmount()).add(lastYearEndAmount.getSpecialAmount()).toString());
        //计算总顺延年金额
        grantUserDto.setTotalPostponeYearAmount(totalYearAmount.add(yearEndAmount.getTotalAmount()).add(lastYearEndAmount.getTotalAmount()).toString());
        //订单金额
        grantUserDto.setOrderAmount(orderAmount.toString());
        //商户累计金额
        grantUserDto.setMchDailyAmount(dailyAmount.getSpecialAmount().add(orderAmount).toString());
        //商户平台累计金额
        grantUserDto.setMchPlatformDailyAmount(dailyAmount.getTotalAmount().add(orderAmount).toString());
        //24H相同订单次数
        grantUserDto.setMchSingleUserCount(Integer.toString(tradeTimes.getSpecialCount() + 1));
        return grantUserDto;
    }

    private StaticsGrantCountVo calculateNums(List<String> supplierNoList, Map<String, RiskControlNumDto> countMap) {
        StaticsGrantCountVo staticsGrantCountVo = new StaticsGrantCountVo();
        countMap.values().stream().forEach(x->{
            Integer count = x.getCountNum();
            staticsGrantCountVo.setTotalCount(staticsGrantCountVo.getTotalCount() + count);
            if (supplierNoList.contains(x.getMainstayNo())){
                staticsGrantCountVo.setSpecialCount(staticsGrantCountVo.getSpecialCount() + count);
            }
        });
        return staticsGrantCountVo;
    }

    private StaticsGrantAmountVo calculateAmount(List<String> supplierNoList, Map<String, RiskControlAmountDto> amountMap) {
        StaticsGrantAmountVo staticsGrantAmountVo = new StaticsGrantAmountVo();
        amountMap.values().stream().forEach(x->{
            BigDecimal amount = x.getAmount();
            staticsGrantAmountVo.setTotalAmount(staticsGrantAmountVo.getTotalAmount().add(amount));
            if (supplierNoList.contains(x.getMainstayNo())){
                staticsGrantAmountVo.setSpecialAmount(staticsGrantAmountVo.getSpecialAmount().add(amount));
            }
        });
        return staticsGrantAmountVo;
//        //如果没有配置供应商，则计算全部
//        if (supplierNoList.size() == 0){
//            amountMap.values().stream().map(x->amount.add(x.getAmount()));
//            return amount;
//        }
//
//        RiskControlAmountDto recordAmountDto;
//        //有配置供应商，按配置计算
//        for (String s : supplierNoList) {
//            recordAmountDto = amountMap.get(s);
//            if (recordAmountDto != null){
//                amount.add(recordAmountDto.getAmount());
//            }
//        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void grantProcess(RecordItemGroupBo recordItem) {
        //recordItem里面只包含了idcardNo + mainstay的组合，第一步要先查回来该idcardNo + mainstay的在统计月份的总金额，为了兼容分库分表，createdate往前提一个月。
        BigDecimal monthAmount = recordItemFacade.getSumAmount(
                DateUtil.addMonth(recordItem.getCreateDate(), -1),
                DateUtil.getDayEnd(DateUtil.getLastOfMonth(recordItem.getCreateDate())),
                DateUtil.getFirstOfMonth(recordItem.getCreateDate()),
                DateUtil.getDayEnd(DateUtil.getLastOfMonth(recordItem.getCreateDate())),
                recordItem.getMainstayNo(),
                recordItem.getReceiveIdCardNoMd5());
        //第一步：根据创建时间拿到10月mainstayNo - idCardNo
        //第二步：创建时间 9 <= x <= 10，更新时间 x = 10
        //假设一笔订单处于：创建时间 10-31 23:59:59 更新时间 11-01 00:00:01
        //则统计10月数据的时候,会缺少这笔订单
        //统计11月数据的时候由于第一步拿到的mainstayNo和idcardNo是11月的，因此有可能也会缺少这笔订单
        //所以第一步需要拿到本月和上个月的数据
        if (monthAmount == null || monthAmount.compareTo(BigDecimal.ZERO) == 0){
            return;
        }
        StatisticsGrant2user grant2user = new StatisticsGrant2user();
        grant2user.setAmount(monthAmount);
        grant2user.setCreateTime(new Date());
        grant2user.setIdCardNo(recordItem.getReceiveIdCardNoMd5());
        grant2user.setMonth(DateUtil.getMonth(recordItem.getCreateDate()));
        grant2user.setSupplierNo(recordItem.getMainstayNo());
        grant2user.setYear(DateUtil.getYear(recordItem.getCreateDate()));
        statisticsGrant2userDao.insert(grant2user);
        StatisticsRecordUnique statisticsUnique = new StatisticsRecordUnique();
        statisticsUnique.setUniqueKey(String.format("%s-%s-%s-%d", recordItem.getReceiveIdCardNoMd5(),
                recordItem.getMainstayNo(),
                grant2user.getYear(),
                grant2user.getMonth()));
        statisticsRecordUniqueDao.insert(statisticsUnique);
    }

    public static void main(String[] args) {
        GrantDateUtil.GrantDateKeys grantDateKeys = GrantDateUtil.getGrantDate(new Date());
        System.out.println(grantDateKeys);
    }

    public BigDecimal getTotalAmountByIdCard(String receiveIdCardNoMd5) {
        return statisticsGrant2userDao.sumAmountByIdCard(receiveIdCardNoMd5);
    }
}
