package com.zhixianghui.service.export.core.biz;

import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.constants.redis.RedisKeysConstant;
import com.zhixianghui.common.statics.enums.bill.BillStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.vo.ApiBillVo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.service.export.annotation.CheckBlack;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ApiBillBiz
 * @Description TODO
 * @Date 2022/9/30 11:16
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApiBillBiz {

    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;
    @Value("${file.baseurl}")
    private String fileBaseUrl;

    @Autowired
    private FastdfsClient fastdfsClient;

    @Reference
    private OrderItemFacade orderItemFacade;

    @Autowired
    private RedisClient redisClient;

    public void getBill(Map<String, String> map) {
        String mchOrderNo = map.get("mchOrderNo");
        String platTrxNo = map.get("platTrxNo");
        String fileId = map.get("fileId");
        String employerNo = map.get("employerNo");
        String key = RedisKeysConstant.API_BILL_PREFIX + employerNo + ":" + fileId;
        OrderItem orderItem = null;
        try {
            Map<String,String> redisMap = redisClient.hgetAll(key);
            if (redisMap == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数校验失败，file_id 不存在");
            }
            Integer status = Integer.valueOf(redisMap.get("status"));
            //file状态不为处理中则直接返回，不作任何处理
            if (status.intValue() != BillStatusEnum.PROCESS.getValue()){
                return;
            }

            if (StringUtils.isNotBlank(mchOrderNo)){
                orderItem = orderItemFacade.getByMchOrderNo(mchOrderNo);
            }else{
                orderItem = orderItemFacade.getByPlatTrxNo(platTrxNo);
            }
            if (orderItem == null || orderItem.getOrderItemStatus() != OrderItemStatusEnum.GRANT_SUCCESS.getValue()){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单不存在或交易状态不为成功");
            }

            if (!orderItem.getEmployerNo().equals(employerNo)){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单不存在或交易状态不为成功");
            }

            //下载电子回单
            billDownLoad(key,map,orderItem);

        }catch (BizException e){
            updateCacahe(key,map,BillStatusEnum.FAIL.getValue(),e.getErrMsg());
        }catch (Exception e){
            updateCacahe(key,map,BillStatusEnum.FAIL.getValue(),"回单下载失败");
        }

    }

    private void billDownLoad(String key,Map<String,String> map,OrderItem orderItem) {
        // 初始化sftp连接
        ChannelSftp channelSftp = SftpUtil.connect(host, port, userName, pwd);
        String sftpFilePath = CertificateUtil.getFilePath(dir, orderItem.getMainstayNo(), orderItem.getPlatTrxNo());
        String tempPath = System.getProperty("user.dir") + File.separator + "download" + File.separator + RandomUtil.get16LenStr();
        File payFile = null;
        File transferFile = null;
        List<ApiBillVo> apiBillVoList = new ArrayList<>();
        try{
            if (StringUtils.isBlank(orderItem.getProductNo()) || orderItem.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
                //B2B回单
                String payFileName = CertificateUtil.getPayFileName(orderItem.getReceiveNameDecrypt(), orderItem.getMainstayNo(), orderItem.getPlatTrxNo());
                //B2C回单
                String transferFileName = CertificateUtil.getTransferFileName(orderItem.getMainstayName(), orderItem.getEmployerNo(), orderItem.getPlatTrxNo());

                payFile = FileUtils.createFile(tempPath  + File.separator + payFileName);
                SftpUtil.downloadNoClose(sftpFilePath + payFileName, payFile, channelSftp);
                if (!orderItem.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name()) && !orderItem.getPayChannelNo().equals(ChannelNoEnum.CMB.name())){
                    transferFile = FileUtils.createFile(tempPath + File.separator + transferFileName);
                    SftpUtil.downloadNoClose(sftpFilePath + transferFileName, transferFile, channelSftp);
                }
            }else if (orderItem.getProductNo().equals(ProductNoEnum.CKH.getValue())){
                String payFileName = CertificateUtil.getCKHPayFileName(orderItem.getReceiveNameDecrypt(), orderItem.getEmployerNo(), orderItem.getPlatTrxNo());
                payFile = FileUtils.createFile(tempPath  + File.separator + payFileName);
                SftpUtil.downloadNoClose(sftpFilePath + payFileName, payFile, channelSftp);
            }

            if (payFile != null){
                String url = fastdfsClient.uploadFile(payFile.getAbsolutePath(), payFile.getName());
                log.info("上传电子回单至文件系统，订单号：[{}]，文件名：[{}]：fastdfsUrl：[{}]",orderItem.getPlatTrxNo(),payFile.getName(),fileBaseUrl + url);
                ApiBillVo apiBillVo = new ApiBillVo(payFile.getName(), FilenameUtils.getExtension(payFile.getName()),fileBaseUrl + url);
                apiBillVoList.add(apiBillVo);
            }

            if (transferFile != null){
                String url = fastdfsClient.uploadFile(transferFile.getAbsolutePath(),transferFile.getName());
                log.info("上传电子回单至文件系统，订单号：[{}]，文件名：[{}]：fastdfsUrl：[{}]",orderItem.getPlatTrxNo(),transferFile.getName(),fileBaseUrl + url);
                ApiBillVo apiBillVo = new ApiBillVo(transferFile.getName(), FilenameUtils.getExtension(transferFile.getName()),fileBaseUrl + url);
                apiBillVoList.add(apiBillVo);
            }
        } finally{
            if (payFile != null) {
                try {
                    FileUtils.deleteDir(payFile);
                } catch (Exception e) {
                    log.error("删除payFile目录时出现异常", e);
                }
            }
            if (transferFile != null) {
                try {
                    FileUtils.deleteDir(transferFile);
                } catch (Exception e) {
                    log.error("删除transferFile目录时出现异常", e);
                }
            }
            if (channelSftp != null) {
                SftpUtil.sftpClose(channelSftp);
            }
        }

        map.put("file", JsonUtil.toString(apiBillVoList));
        updateCacahe(key,map,BillStatusEnum.SUCCESS.getValue(),"成功");
    }

    private void updateCacahe(String key,Map<String,String> map , int status, String errMsg) {
        map.put("status",String.valueOf(status));
        map.put("msg",errMsg);
        redisClient.hset(key,map);
    }


}
