package com.zhixianghui.service.export.listener;

import com.alibaba.fastjson.JSONArray;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.common.util.utils.ZipUtil;
import com.zhixianghui.facade.trade.dto.FeeOrderBatchVo;
import com.zhixianghui.facade.trade.entity.FeeOrderItem;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.poi.ss.formula.functions.T;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月18日 17:38:00
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_ORDER_CETIFICATE_UPLOAD, selectorExpression = MessageMsgDest.TAG_FEE_ORDER_CETIFICATE_UPLOAD,
        consumeThreadMax = 5, consumerGroup = "feeOrderCetificateConsumer")
public class FeeOrderCetificateListener extends BaseRocketMQListener<String> {

    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    private static final String UNDERLINE = "_";


    @Autowired
    private FastdfsClient fastdfsClient;

    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String feeOrderBatchNo) {
        FeeOrderBatchVo feeOrderBatchVo = feeOrderBatchFacade.selectOrderItem(feeOrderBatchNo);
        FeeOrderItem serviceItem = feeOrderBatchVo.getServiceItem();
        FeeOrderItem taxItem = feeOrderBatchVo.getTaxItem();
        String path=System.getProperty("user.dir") + File.separator + feeOrderBatchNo;
        String tempPath = path + File.separator;
        log.info("账单上传，本地路径地址:{}",tempPath);
        ChannelSftp channelSftp = null;
        File zipFile=null;
        try {
            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            List<String> serviceUrls = formatUrl(serviceItem.getCetificateUrl());
            List<String> taxUrls = formatUrl(taxItem.getCetificateUrl());
            String mainSftpDir = CertificateUtil.getFeeFilePath(dir, serviceItem.getEmployerNo(), feeOrderBatchNo);
            downLoadFiles(serviceUrls, tempPath, getFileNamePrefix(serviceItem.getFeeItemNo(), "服务费凭证"));
            downLoadFiles(taxUrls, tempPath, getFileNamePrefix(taxItem.getFeeItemNo(), "个税凭证"));
            zipFile = ZipUtil.zipFile(path);
            log.info("账单文件上传，路径：{}，压缩文件名：{}",mainSftpDir,zipFile.getName());
            SftpUtil.uploadNoClose(mainSftpDir, zipFile, zipFile.getName(), channelSftp);
        } catch (Exception e) {
            log.error("[{}]账单上传凭证异常", feeOrderBatchNo,e);
        } finally {
            try {
                FileUtils.deleteDir(new File(tempPath));
                if (zipFile != null) {
                    boolean delete = zipFile.delete();
                    log.info("删除zip文件：{}",delete);
                }
            } catch (Exception e) {
                log.error("删除文件时出现异常 tempPath:{}", tempPath, e);
            }
            if (channelSftp != null) {
                SftpUtil.sftpClose(channelSftp);
            }
        }
    }

    private String getFileNamePrefix(String orderItemNo, String prefix) {
        return orderItemNo + UNDERLINE + prefix + UNDERLINE;
    }

    private void downLoadFiles(List<String> urls, String tempPath, String fileNamePrefix) throws IOException {
        for (int i = 0; i < urls.size(); i++) {
            InputStream inputStream = null;
            try {
                String url = urls.get(i);
                String suffix = FileUtils.getSuffix(url);
                String fileName = fileNamePrefix + i + suffix;
                log.info("账单凭证上传,文件名：{}", fileName);
                File file = FileUtils.createFile(tempPath + fileName);
                inputStream = fastdfsClient.downloadFile(url);
                org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
            } finally {
                if (inputStream != null) {
                    inputStream.close();
                }
            }
        }
    }


    private List<String> formatUrl(String json) {
        json = Optional.ofNullable(json).
                orElse(JSONArray.toJSONString(Lists.newArrayList()));
        return JSONArray.parseArray(json, String.class);
    }
}