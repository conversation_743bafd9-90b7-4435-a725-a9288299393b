package com.zhixianghui.service.export.core.biz;


import com.zhixianghui.common.statics.enums.export.ExportStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.service.export.core.dao.ReportRecordDao;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;


/**
 * 商户信息
 * <AUTHOR>
 * @date 2020/8/4
 **/
@Service
@Slf4j
public class ExportRecordBiz {
    @Autowired
    private ReportRecordDao reportRecordDao;
    @Autowired
    private FastdfsClient fastdfsClient;

    /**
     * 保存待导出文件记录
     * 超出数量限制的文件将被删除
     * @param record
     */
    public void insert(ExportRecord record){
        if((Objects.equals(record.getSystemType(), SystemTypeEnum.MERCHANT_MANAGEMENT.getValue()) ||
                Objects.equals(record.getSystemType(), SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue()))
            && StringUtil.isEmpty(record.getMchNo())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("商户号不能为空");
        }
        // 每个账号只能保存 ReportConstant.MAX_FILE_NUM 份文件，超出的删除
        List<ExportRecord> recordList = listBy(record.getSystemType(), record.getOperatorLoginName(), record.getMchNo(), null);
        if(CollectionUtils.size(recordList) > ReportConstant.MAX_FILE_NUM){
            for(int i = ReportConstant.MAX_FILE_NUM; i < recordList.size(); i++){
                ExportRecord historyRecord = recordList.get(i);
                // 终态或者中间态超过24小时则删除
                if(Objects.equals(historyRecord.getExportStatus(), ExportStatusEnum.FAIL.getValue())
                    || Objects.equals(historyRecord.getExportStatus(), ExportStatusEnum.SUCCESS.getValue())
                    || DateUtil.subtractDays(new Date(), record.getCreateTime()) > 1){
                    // 删除文件服务器文件
                    try{
                        if(StringUtil.isNotEmpty(historyRecord.getFileUrl())){
                            fastdfsClient.deleteFile(historyRecord.getFileUrl());
                        }
                        reportRecordDao.deleteById(historyRecord.getId());
                    } catch (Exception e){
                        log.error("[{}-{}]删除导出记录异常:", record.getOperatorLoginName(), record.getFileNo(), e);
                    }
                    log.info("[{}-{}]删除导出记录", record.getOperatorLoginName(), record.getFileNo());
                }
            }
        }
        reportRecordDao.insert(record);
    }

    public PageResult<List<ExportRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam){
        return reportRecordDao.listPage(paramMap, pageParam);
    }

    public List<ExportRecord> listBy(Integer systemType, String loginName, String mchNo, Integer reportType){
        return this.listBy(systemType, loginName, mchNo, reportType, 0);
    }

    public List<ExportRecord> listBy(Integer systemType, String loginName, String mchNo, Integer reportType,Integer direction){
        Assert.notNull(systemType, "系统类型不能为空");
        Assert.hasText(loginName, "登录名不能为空");

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("systemType", systemType);
        paramMap.put("reportType", reportType);
        paramMap.put("operatorLoginName", loginName);
        paramMap.put("mchNo", mchNo);
        paramMap.put("direction", direction);
        return reportRecordDao.listBy(paramMap);
    }
}
