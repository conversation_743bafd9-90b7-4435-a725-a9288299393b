//package com.zhixianghui.service.export.facade;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.zhixianghui.facade.export.service.CkOrderFacade;
//import com.zhixianghui.service.export.core.biz.services.CkOrdersService;
//import org.apache.dubbo.config.annotation.Service;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.List;
//import java.util.Map;
//
//@Service
//public class CkOrderFacadeImpl implements CkOrderFacade {
//    @Autowired
//    public CkOrdersService ckOrdersService;
//
//    @Override
//    public List<Map<String, Object>> listSalerOrdersGroupedByMch(Map<String, Object> params) {
//        return ckOrdersService.listSalerOrdersGroupedByMch(params);
//    }
//
//    @Override
//    public IPage<Map<String, Object>> salerstatistics(Map<String, Object> params, Page<Map<String,Object>> pageParam) {
//        final IPage<Map<String, Object>> salerstatistics = ckOrdersService.salerstatistics(pageParam, params);
//        return salerstatistics;
//    }
//
//    @Override
//    public Map<String, Object> coreIndexStatistics(Map<String, Object> params) {
//        return ckOrdersService.coreIndexStatistics(params);
//    }
//
//    @Override
//    public List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> params) {
//        return ckOrdersService.coreIndexDailyDetail(params);
//    }
//
//    @Override
//    public List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> params) {
//        return ckOrdersService.coreIndexDetailMonthly(params);
//    }
//
//    @Override
//    public List<Map<String, Object>> listSalerOrdersGroupedByMchDaily(Map<String, Object> params){
//        return ckOrdersService.listSalerOrdersGroupedByMchDaily(params);
//    }
//
//    @Override
//    public List<Map<String, Object>> listSalerOrdersGroupedByMchMonthly(Map<String, Object> params) {
//        return ckOrdersService.listSalerOrdersGroupedByMchMonthly(params);
//    }
//
//    @Override
//    public Map<String, Object> salerOrdersStatistics(Map<String, Object> params) {
//        return ckOrdersService.salerOrdersStatistics(params);
//    }
//
//    @Override
//    public Map<String, Object> countOrderAmont(Map<String, Object> paramMap) {
//        return ckOrdersService.countOrderAmount(paramMap);
//    }
//
//}
