package com.zhixianghui.service.export.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.export.entity.StatisticsGrant2user;
import com.zhixianghui.facade.trade.dto.RiskControlAmountDto;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @description: 用户发放dao
 * @author: xingguang li
 * @created: 2020/11/02 11:50
 */
@Repository
public class StatisticsGrant2userDao extends MyBatisDao<StatisticsGrant2user, Long> {

    public BigDecimal getAmount(Map<String,Object> param) {
        return this.getSqlSession().selectOne(fillSqlId("getAmount"), param);
    }

    public BigDecimal getYearEndAmount(Map<String, Object> param) {
        return this.getSqlSession().selectOne(fillSqlId("getYearEndAmount"),param);
    }

    public Map<String, RiskControlAmountDto> getAmountGroup(Map<String, Object> params) {
        return this.getSqlSession().selectMap(fillSqlId("getAmountGroup"),params,"mainstayNo");
    }

    public Map<String, RiskControlAmountDto> getYearEndAmountGroup(Map<String, Object> param) {
        return this.getSqlSession().selectMap(fillSqlId("getYearEndAmountGroup"),param,"mainstayNo");
    }

    public BigDecimal sumAmountByIdCard(String receiveIdCardNoMd5) {
        return this.getSqlSession().selectOne(fillSqlId("sumAmountByIdCard"),receiveIdCardNoMd5);
    }
}
