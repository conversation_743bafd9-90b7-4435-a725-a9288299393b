//package com.zhixianghui.service.export.listener;
//
//import cn.hutool.core.date.DateUtil;
//import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
//import com.zhixianghui.common.statics.enums.fee.RewardTypeEnum;
//import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
//import com.zhixianghui.common.statics.exception.CommonExceptions;
//import com.zhixianghui.facade.export.entity.CkOrders;
//import com.zhixianghui.facade.fee.entity.AgentFeeOrder;
//import com.zhixianghui.facade.fee.entity.MerchantFeeOrder;
//import com.zhixianghui.facade.fee.entity.SalesFeeOrder;
//import com.zhixianghui.facade.fee.entity.VendorFeeOrder;
//import com.zhixianghui.facade.fee.service.AgentFeeOrderQueryFacade;
//import com.zhixianghui.facade.fee.service.MerchantFeeOrderQueryFacade;
//import com.zhixianghui.facade.fee.service.SalesFeeOrderQueryFacade;
//import com.zhixianghui.facade.fee.service.VendorFeeOrderQueryFacade;
//import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
//import com.zhixianghui.facade.notify.service.NotifyFacade;
//import com.zhixianghui.facade.trade.entity.OrderItem;
//import com.zhixianghui.facade.trade.entity.RecordItem;
//import com.zhixianghui.facade.trade.service.OrderItemFacade;
//import com.zhixianghui.facade.trade.service.RecordItemFacade;
//import com.zhixianghui.service.export.core.biz.services.CkOrdersService;
//import com.zhixianghui.starter.comp.component.RedisClient;
//import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//@Component
//@Slf4j
//@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ORDER_DATA_SYNC_CK, selectorExpression =MessageMsgDest.TAG_ORDER_DATA_SYNC_CK, consumeThreadMax = 10, consumerGroup = "syncOrder2CkConsumer")
//public class OrderSyncListener extends BaseRocketMQListener<String> {
//    @Reference
//    private OrderItemFacade orderItemFacade;
//    @Reference
//    private RecordItemFacade recordItemFacade;
//    @Reference
//    private MerchantFeeOrderQueryFacade merchantFeeOrderQueryFacade;
//    @Reference
//    private VendorFeeOrderQueryFacade vendorFeeOrderQueryFacade;
//    @Reference
//    private SalesFeeOrderQueryFacade salesFeeOrderQueryFacade;
//    @Reference
//    private AgentFeeOrderQueryFacade agentFeeOrderQueryFacade;
//    @Reference
//    private NotifyFacade notifyFacade;
//    @Autowired
//    private CkOrdersService ckOrdersService;
//    @Autowired
//    private RedisClient redisClient;
//
//    @Override
//    public void validateJsonParam(String platTrxNo) {
//        if (StringUtils.isBlank(platTrxNo)) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[同步订单失败]-获取的平台流水号为空");
//        }
//    }
//
//    @Override
//    public void consumeMessage(String platTrxNo) {
//        try {
//            final OrderItem orderItem = orderItemFacade.getByPlatTrxNo(platTrxNo);
//            final RecordItem recordItem = recordItemFacade.getByPlatTrxNo(platTrxNo);
//            final MerchantFeeOrder merchantFeeOrder = merchantFeeOrderQueryFacade.getByPlatTrxNo(platTrxNo);
//            final VendorFeeOrder vendorFeeOrder = vendorFeeOrderQueryFacade.getByPlatTrxNo(platTrxNo);
//            final SalesFeeOrder salesFeeOrder = salesFeeOrderQueryFacade.getByPlatTrxNo(platTrxNo);
//            final List<AgentFeeOrder> agentFeeOrders = agentFeeOrderQueryFacade.getByPlatTrxNo(platTrxNo);
//            if (merchantFeeOrder == null || vendorFeeOrder == null || salesFeeOrder == null) {
//                log.info("计费订单缺失-{}", platTrxNo);
//                String incrKey = "sync:ck:times:" + platTrxNo;
//                final Long times = redisClient.incr(incrKey);
//                if (times < 3) {
//                    if (times == 1) {
//                        redisClient.expire(incrKey, 60 * 30);
//                    }
//                    notifyFacade.sendOne(MessageMsgDest.TOPIC_ORDER_DATA_SYNC_CK, orderItem.getEmployerNo(), platTrxNo, NotifyTypeEnum.ORDER_DATA_SYNC_CK.getValue(), MessageMsgDest.TAG_ORDER_DATA_SYNC_CK, orderItem.getPlatTrxNo(), MsgDelayLevelEnum.M_10.getValue());
//                }
//                return;
//            }
//            if (merchantFeeOrder.getExistAgent()!=null && merchantFeeOrder.getExistAgent() && agentFeeOrders == null) {
//                String incrKey = "sync:ck:times:" + platTrxNo;
//                final Long times = redisClient.incr(incrKey);
//                if (times < 3) {
//                    if (times == 1) {
//                        redisClient.expire(incrKey, 60 * 30);
//                    }
//                    notifyFacade.sendOne(MessageMsgDest.TOPIC_ORDER_DATA_SYNC_CK, orderItem.getEmployerNo(), platTrxNo, NotifyTypeEnum.ORDER_DATA_SYNC_CK.getValue(), MessageMsgDest.TAG_ORDER_DATA_SYNC_CK, orderItem.getPlatTrxNo(), MsgDelayLevelEnum.M_10.getValue());
//                }
//                return;
//            }
//
//            CkOrders ckOrders = new CkOrders();
//            ckOrders.setPlatTrxNo(orderItem.getPlatTrxNo());
//            ckOrders.setCreateDate(DateUtil.toLocalDateTime(orderItem.getCreateDate()));
//            ckOrders.setCreateTime(DateUtil.toLocalDateTime(orderItem.getCreateTime()));
//            ckOrders.setCompleteTime(DateUtil.toLocalDateTime(orderItem.getCompleteTime()));
//            ckOrders.setMchBatchNo(orderItem.getMchBatchNo());
//            ckOrders.setPlatBatchNo(orderItem.getPlatBatchNo());
//            ckOrders.setMchOrderNo(orderItem.getMchOrderNo());
//            ckOrders.setLaunchWay(orderItem.getLaunchWay());
//            ckOrders.setEmployerNo(orderItem.getEmployerNo());
//            ckOrders.setEmployerName(orderItem.getEmployerName());
//            ckOrders.setMainstayNo(orderItem.getMainstayNo());
//            ckOrders.setMainstayName(orderItem.getMainstayName());
//            ckOrders.setChannelType(orderItem.getChannelType());
//            ckOrders.setPayChannelNo(orderItem.getPayChannelNo());
//            ckOrders.setChannelName(orderItem.getChannelName());
//            ckOrders.setBankName(orderItem.getBankName());
//            ckOrders.setBankCode(orderItem.getBankCode());
//            ckOrders.setOrderItemNetAmount(orderItem.getOrderItemNetAmount());
//            ckOrders.setOrderItemFee(orderItem.getOrderItemFee());
//            ckOrders.setOrderItemAmount(orderItem.getOrderItemAmount());
//            ckOrders.setOrderItemStatus(orderItem.getOrderItemStatus());
//            ckOrders.setErrorCode(orderItem.getErrorCode());
//            ckOrders.setErrorDesc(orderItem.getErrorDesc());
//            ckOrders.setAccessTimes(orderItem.getAccessTimes());
//            ckOrders.setIsPassHangup(orderItem.getIsPassHangup() ? 1 : 0);
//            ckOrders.setHangupApprovalLoginName(orderItem.getHangupApprovalLoginName());
//            ckOrders.setRemark(orderItem.getRemark());
//            ckOrders.setJsonStr(orderItem.getJsonStr());
//            ckOrders.setAppid(orderItem.getAppid());
//            ckOrders.setIsDelete(orderItem.getIsDelete());
//            ckOrders.setReceiveName(orderItem.getReceiveName());
//            ckOrders.setReceiveNameMd5(orderItem.getReceiveNameMd5());
//            ckOrders.setReceiveIdCardNo(orderItem.getReceiveIdCardNo());
//            ckOrders.setReceiveIdCardNoMd5(orderItem.getReceiveIdCardNoMd5());
//            ckOrders.setReceiveAccountNo(orderItem.getReceiveAccountNo());
//            ckOrders.setReceiveAccountNoMd5(orderItem.getReceiveAccountNoMd5());
//            ckOrders.setReceivePhoneNo(orderItem.getReceivePhoneNo());
//            ckOrders.setReceivePhoneNoMd5(orderItem.getReceivePhoneNoMd5());
//            ckOrders.setEncryptKeyId(orderItem.getEncryptKeyId());
//
//            ckOrders.setWorkCategoryCode(recordItem.getWorkCategoryCode());
//            ckOrders.setWorkCategoryName(recordItem.getWorkCategoryName());
//            ckOrders.setPayErrorCode(recordItem.getErrorCode());
//            ckOrders.setPayErrorDesc(recordItem.getErrorDesc());
//            ckOrders.setProductNo(merchantFeeOrder.getProductNo());
//            ckOrders.setProductName(merchantFeeOrder.getProductName());
//            ckOrders.setCalculateFormula(merchantFeeOrder.getCalculateFormula());
//            ckOrders.setSalerId(merchantFeeOrder.getSalerId());
//            ckOrders.setSalerName(merchantFeeOrder.getSalerName());
//            ckOrders.setDepartmentId(merchantFeeOrder.getDepartmentId());
//            ckOrders.setDepartmentName(merchantFeeOrder.getDepartmentName());
//            ckOrders.setOrderFee(salesFeeOrder.getOrderFee());
//            ckOrders.setChannelOrderNo(vendorFeeOrder.getChannelOrderNo());
//            ckOrders.setChannelTrxNo(vendorFeeOrder.getChannelTrxNo());
//            ckOrders.setVendorFee(vendorFeeOrder.getVendorFee());
//            ckOrders.setVendorFeeCalculateFormula(vendorFeeOrder.getCalculateFormula());
//            ckOrders.setSalesFee(salesFeeOrder.getSalesFee());
//            ckOrders.setSalesProfit(salesFeeOrder.getSalesProfit());
//            ckOrders.setSalesFeeCalculateFormula(salesFeeOrder.getCalculateFormula());
//            ckOrders.setExistAgent(merchantFeeOrder.getExistAgent() != null && merchantFeeOrder.getExistAgent() ? 1 : 0);
//            ckOrders.setRemitPlatTrxNo(recordItem.getRemitPlatTrxNo());
//            ckOrders.setMerchantOrderFee(merchantFeeOrder.getOrderFee());
//
//            for (AgentFeeOrder agentFeeOrder : agentFeeOrders) {
//                if (agentFeeOrder.getRewardType() == RewardTypeEnum.TRADE_PROFIT.getValue()) {
//                    ckOrders.setAgentNo(agentFeeOrder.getAgentNo());
//                    ckOrders.setAgentName(agentFeeOrder.getAgentName());
//                    ckOrders.setAgentCost(agentFeeOrder.getAgentCost());
//                    ckOrders.setAgentFeeCalculateFormula(agentFeeOrder.getCalculateFormula());
//                    ckOrders.setAgentFeeRate(agentFeeOrder.getAgentFeeRate());
//                    ckOrders.setSelfDeclare(agentFeeOrder.getSelfDeclare());
//                    ckOrders.setAgentProfit(agentFeeOrder.getAgentProfit());
//                    ckOrders.setAgentType(agentFeeOrder.getAgentType());
//                }else {
//                    ckOrders.setInviterNo(agentFeeOrder.getInviterNo());
//                    ckOrders.setInviterName(agentFeeOrder.getInviterName());
//                    ckOrders.setSecondAgentCost(agentFeeOrder.getAgentCost());
//                    ckOrders.setSecondAgentProfit(agentFeeOrder.getAgentProfit());
//                    ckOrders.setSecondAgentFeeCalculateFormula(agentFeeOrder.getCalculateFormula());
//                    ckOrders.setSecondAgentFeeRate(agentFeeOrder.getAgentFeeRate());
//                    ckOrders.setSecondSelfDeclare(agentFeeOrder.getSelfDeclare());
//                }
//            }
//
//            final CkOrders ckOrders1 = ckOrdersService.getById(platTrxNo);
//            if (ckOrders1 == null) {
//                ckOrdersService.save(ckOrders);
//            }else {
//                ckOrdersService.updateById(ckOrders);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("[{}]-更新失败，进行重试", platTrxNo, e);
//            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("[" + platTrxNo + "]-更新失败，进行重试");
//        }
//
//    }
//}
