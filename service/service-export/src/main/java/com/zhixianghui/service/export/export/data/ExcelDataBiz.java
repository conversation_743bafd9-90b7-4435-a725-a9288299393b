package com.zhixianghui.service.export.export.data;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.account.invoice.entity.AccountInvoice;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceManageFacade;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.facade.data.service.CkAccountDetailFacade;
import com.zhixianghui.facade.data.service.CkAgentFeeOrderFacade;
import com.zhixianghui.facade.data.service.CkOrderFacade;
import com.zhixianghui.facade.data.service.CkVendorFeeOrderFacade;
import com.zhixianghui.facade.export.vo.EmployerAccountInfoVo;
import com.zhixianghui.facade.export.vo.SubChannelInfoVo;
import com.zhixianghui.facade.fee.entity.AgentFeeOrder;
import com.zhixianghui.facade.fee.entity.MerchantFeeOrder;
import com.zhixianghui.facade.fee.entity.SalesFeeOrder;
import com.zhixianghui.facade.fee.entity.VendorFeeOrder;
import com.zhixianghui.facade.fee.service.AgentFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.AgentMonthBillFacade;
import com.zhixianghui.facade.fee.service.MerchantFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.SalesFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.VendorFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.vo.AgentFeeStatisticVo;
import com.zhixianghui.facade.fee.vo.AgentMonthBillVo;
import com.zhixianghui.facade.fee.vo.MerchantFeeStatisticVo;
import com.zhixianghui.facade.fee.vo.SalesFeeStatisticVo;
import com.zhixianghui.facade.fee.vo.VendorFeeStatisticVo;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.AgentBankAccount;
import com.zhixianghui.facade.merchant.entity.AgentCredential;
import com.zhixianghui.facade.merchant.entity.AgentEmployerMain;
import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import com.zhixianghui.facade.merchant.entity.AgentSaler;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.service.AgentBankAccountFacade;
import com.zhixianghui.facade.merchant.service.AgentCredentialFacade;
import com.zhixianghui.facade.merchant.service.AgentEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.AgentProductQuoteFacade;
import com.zhixianghui.facade.merchant.service.AgentSalerFacade;
import com.zhixianghui.facade.merchant.service.AgreementFacade;
import com.zhixianghui.facade.merchant.service.AgreementFileFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantFileFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentFunctionFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerFunctionFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierFunctionFacade;
import com.zhixianghui.facade.merchant.vo.AgreementExportVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerDetailVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentDetailVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentExportDetailVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.trade.dto.CmbChangesFoundsQueryDTO;
import com.zhixianghui.facade.trade.dto.CmbIncomeRecordParamDTO;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OfflineOrderItemFail;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.OrderItemFail;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.entity.WxIncomeRecord;
import com.zhixianghui.facade.trade.service.CmbChangesFundsFacade;
import com.zhixianghui.facade.trade.service.CmbIncomeRecordFacade;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.service.FreelanceStatFacade;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.InvoicePreFacade;
import com.zhixianghui.facade.trade.service.MerchantStatFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.RechargeQueryFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.service.WithdrawRecordFacade;
import com.zhixianghui.facade.trade.service.WxIncomeRecordFacade;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.vo.CmbChangesFundsVO;
import com.zhixianghui.facade.trade.vo.CmbIncomeRecordVO;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;
import com.zhixianghui.facade.trade.vo.MerchantStatVo;
import com.zhixianghui.service.export.util.ExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * excel报表数据
 *
 * <AUTHOR>
 * @date 2020/8/31
 **/
@Component
@Slf4j
public class ExcelDataBiz {
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private CkAgentFeeOrderFacade ckAgentFeeOrderFacade;
    @Reference
    private CkOrderFacade ckOrderFacade;
    @Reference
    private MerchantEmployerFacade employerFacade;
    @Reference
    private AgreementFileFacade agreementFileFacade;
    @Reference(timeout = 20000)
    private OrderItemFacade orderItemFacade;
    @Reference
    private RecordItemFacade recordItemFacade;
    @Reference
    private MerchantFeeOrderQueryFacade merchantFeeOrderQueryFacade;
    @Reference
    private VendorFeeOrderQueryFacade vendorFeeOrderQueryFacade;
    @Reference
    private SalesFeeOrderQueryFacade salesFeeOrderQueryFacade;
    @Reference
    private RiskControlFacade riskControlFacade;
    @Reference
    private SignRecordFacade signRecordFacade;
    @Reference
    private AgentFacade agentFacade;
    @Reference
    private AgentFeeOrderQueryFacade agentFeeOrderQueryFacade;
    @Reference
    private AgentMonthBillFacade agentMonthBillFacade;
    @Reference
    private EmployerFunctionFacade employerFunctionFacade;
    @Reference
    private AgentFunctionFacade agentFunctionFacade;
    @Reference
    private SupplierFunctionFacade supplierFunctionFacade;
    @Reference
    private PmsPermissionFacade permissionFacade;
    @Reference
    private RechargeQueryFacade rechargeQueryFacade;
    @Reference
    private WithdrawRecordFacade withdrawRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference(timeout = 80000)
    private FreelanceStatFacade freelanceStat;
    @Reference(timeout = 80000)
    private MerchantStatFacade merchantStat;
    @Reference
    private InvoiceFacade invoiceFacade;
    @Reference
    private WxIncomeRecordFacade wxIncomeRecordFacade;
    @Reference
    private AccountInvoiceManageFacade accountInvoiceManageFacade;
    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private AgreementFacade agreementFacade;
    @Reference
    private OfflineOrderItemFacade offlineOrderItemFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private CkAccountDetailFacade ckAccountDetailFacade;
    @Reference
    private CkVendorFeeOrderFacade ckVendorFeeOrderFacade;
    @Reference
    private CmbIncomeRecordFacade cmbIncomeRecordFacade;
    @Reference
    private CmbChangesFundsFacade cmbChangesFundsFacade;
    @Reference
    private InvoicePreFacade invoicePreFacade;

    /**
     * 查询记录
     * TODO 待优化
     */
    public List<Map<String, Object>> listRecord(int reportType, Map<String, Object> param, PageParam pageParam) {
        if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_INFO_EXPORT.getValue())) {
            return getMerchantInfoPms(param);
        } else if (Objects.equals(reportType, ReportTypeEnum.TRADE_ORDER_ITEM_MER.getValue())) {
            return getOrderItem(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.TRADE_ORDER_ITEM_PMS.getValue())) {
            return getOrderItem(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.TRADE_RECORD_ITEM_PMS.getValue())) {
            return getRecordItem(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_FEE_ORDER.getValue())) {
            return listMerchantFeeOrder(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.VENDOR_FEE_ORDER.getValue())) {
            return listVendorFeeOrder(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.SALES_FEE_ORDER.getValue())) {
            return listSalesFeeOrder(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.TRADE_HANG_ORDER_MER.getValue())) {
            return listHangUpOrder(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.TRADE_ORDER_ITEM_SUP.getValue())) {
            return getOrderItem(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.SIGN_RECORD_MER.getValue())
                || Objects.equals(reportType, ReportTypeEnum.SIGN_RECORD_PMS.getValue())
                || Objects.equals(reportType, ReportTypeEnum.SIGN_RECORD_SUP.getValue())) {
            return listSignRecord(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.AGENT_DETAIL.getValue())) {
            return getAgentDetail(param);
        } else if (Objects.equals(reportType, ReportTypeEnum.AGENT_FEE_ORDER.getValue())) {
            return listAgentFeeOrder(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.AGENT_MONTH_BILL_PMS.getValue())) {
            return listAgentMonthBill(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.AGENT_MONTH_BILL_AGENT.getValue())) {
            return listAgentMonthBill(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.PMS_FUNCTION.getValue())) {
            return listPmsFunction(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_FUNCTION.getValue())) {
            return listMerchantFunction(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.SUPPLIER_FUNCTION.getValue())) {
            return listSupplyFunction(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.AGENT_FUNCTION.getValue())) {
            return listAgentFunction(param, pageParam);
        } else if ((Objects.equals(reportType, ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getValue()))) {
            return listRechargeRecord(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.WITHDRAW_RECORD_PMS.getValue())
                || Objects.equals(reportType, ReportTypeEnum.WITHDRAW_RECORD_EMP.getValue())
                || Objects.equals(reportType, ReportTypeEnum.WITHDRAW_RECORD_SUPL.getValue())) {
            return listWithdrawRecord(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.ORDER_ITEM_FAIL.getValue())) {
            return listFailItemByBatchNo(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.DATA_DICTIONARY.getValue())) {
            return listDataDictionary(param);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_LIST_INFO_EXPORT.getValue())) {
            return listMerchangInfo(param);
        } else if (Objects.equals(reportType, ReportTypeEnum.MAINSTAY_MERCHANT_EXPORT.getValue())) {
            return getMerchantInfoPms(param);
        } else if (Objects.equals(reportType, ReportTypeEnum.MAINSTAY_MERCHANT_LIST.getValue())) {
            return getMerchantList(param);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_STAT_INFO_EXPORT.getValue())) {
            return getMerchantStatInfoList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_STAT_INFO_EXPORT_SUPLIER.getValue())) {
            return getMerchantStatInfoList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.FREELANCE_STAT_Export.getValue())) {
            return getFreelanceStatInfoList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_INVOICE_RECORD_EXPORT.getValue())) {
            return getInvoiceRecordList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.WX_INCOME_EXPORT_ENUM.getValue())) {
            return getWxIncomeRecord(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.INVOICE_ACCOUNT_EXPORT.getValue())) {
            return getInvoiceAccountRecord(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.EMPLOYER_MAINSTAY_RELATION_EXPORT.getValue())) {
            return getEmployerMainstayRelation(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.AGENT_EXPORT.getValue())) {
            return listVoPage(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.FEE_ORDER_EXPORT.getValue())) {
            return feeOrderBatchFacade.export(param);
        } else if (Objects.equals(reportType, ReportTypeEnum.WORK_FLOW_TODO_EXPORT.getValue())) {
            return getWorkFlowTodoList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.WORK_FLOW_CARBON_EXPORT.getValue())) {
            return getWorkFlowCarbonList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.WORK_FLOW_HANDLE_EXPORT.getValue())) {
            return getWorkFlowHandleList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.WORK_SUP_FLOW_HANDLE_EXPORT.getValue())) {
            return getSupplierWorkFlowHandleList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.WORK_SUP_FLOW_TODO_EXPORT.getValue())) {
            return getWorkFlowTodoList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_INVOICE_RECORD_INFO_EXPORT.getValue())) {
            return getInvoiceRecordList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.AGREEMENT_PMS_EXPORT.getValue())) {
            return getAgreementList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.RISK_HANDLED_ORDER_EXPORT.getValue())) {
            return getRiskHandledOrderList(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_PERSONNAL_RECEIPT_ORDER_EXPORT.getValue())) {
            return getOrderItem(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_BUSINESS_RECEIPT_ORDER_EXPORT.getValue())) {
            return getOrderItem(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.PMS_OFFLINE_EXPORT.getValue())
                || Objects.equals(reportType, ReportTypeEnum.MERCHANT_OFFLINE_EXPORT.getValue())
                || Objects.equals(reportType, ReportTypeEnum.MAINSTAY_OFFLINE_EXPORT.getValue())) {
            return getOfflineOrderItem(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.MERCHANT_OFFLINE_FAIL_ORDER_EXPORT.getValue())) {
            return getOfflineFailOrderItem(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.EMPLOYER_BALANCE_EXPORT.getValue())) {
            return getAmountWithMerchantInfo(param);
        } else if (Objects.equals(reportType, ReportTypeEnum.CK_ACCTDETAIL_EXPORT.getValue())) {
            return listCkAccountDetail(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.PMS_SALER_MERGE_FEE.getValue())) {
            return listSalerMergeFee(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.PMS_AGENT_MERGE_FEE.getValue())) {
            return listAgentMergeFee(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.PMS_SUPPLIER_MERGE_FEE.getValue())) {
            return listVendorMergeFee(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.CMB_INCOME_RECORD_EXPORT.getValue())) {
            return listCmbIncomeRecord(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.CMB_CHANGES_FUNDS_EXPORT.getValue())) {
            return listCmbIncomeRecord(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.PMS_MERCHANT_MERGE_FEE.getValue())) {
            return listMerchantMergeFee(param, pageParam);
        } else if (Objects.equals(reportType, ReportTypeEnum.INVOICE_PRE_EXPORT.getValue())) {
            return getInvoicePreRecordList(param, pageParam);
        }
        return null;
    }

    private List<Map<String, Object>> listCmbChangesFundsExport(Map<String, Object> param, PageParam pageParam) {
        CmbChangesFoundsQueryDTO dto = BeanUtil.mapToObject(new CmbChangesFoundsQueryDTO(), param);
        dto.setPageCurrent(pageParam.getPageCurrent());
        dto.setPageSize(pageParam.getPageSize());
        IPage<CmbChangesFundsVO> page = cmbChangesFundsFacade.listPage(dto);
        List<CmbChangesFundsVO> records = page.getRecords();
        return records.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listCmbIncomeRecord(Map<String, Object> param, PageParam pageParam) {
        CmbIncomeRecordParamDTO dto = BeanUtil.mapToObject(new CmbIncomeRecordParamDTO(), param);
        dto.setPageCurrent(pageParam.getPageCurrent());
        dto.setPageSize(pageParam.getPageSize());
        IPage<CmbIncomeRecordVO> page = cmbIncomeRecordFacade.listPage(dto);
        List<CmbIncomeRecordVO> records = page.getRecords();
        return records.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listMerchantMergeFee(Map<String, Object> param, PageParam pageParam) {
        List<Map<String, Object>> recordList = new ArrayList<>();
        PageResult<List<MerchantFeeStatisticVo>> pageResult = merchantFeeOrderQueryFacade.merchantFeeStatistics(param, pageParam);
        for (MerchantFeeStatisticVo statisticVo : pageResult.getData()) {
            Map<String, Object> rowMap = new HashMap<>();
            rowMap.put("mchNo", statisticVo.getMchNo());
            rowMap.put("mchName", statisticVo.getMchName());
            rowMap.put("vendorNo", statisticVo.getVendorNo());
            rowMap.put("vendorName", statisticVo.getVendorName());
            rowMap.put("productNo", statisticVo.getProductNo());
            rowMap.put("productName", statisticVo.getProductName());
            rowMap.put("totalNetAmount", statisticVo.getTotalNetAmount());
            rowMap.put("totalOrderFee", statisticVo.getTotalOrderFee());
            recordList.add(rowMap);
        }
        return recordList;
    }

    private List<Map<String, Object>> listVendorMergeFee(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<VendorFeeStatisticVo>> pageResult = vendorFeeOrderQueryFacade.vendorFeeStatistics(param, pageParam);
        List<Map<String, Object>> recordList = new ArrayList<>();
        for (VendorFeeStatisticVo statisticVo : pageResult.getData()) {
            Map<String, Object> rowMap = new HashMap<>();
            rowMap.put("mchNo", statisticVo.getMchNo());
            rowMap.put("mchName", statisticVo.getMchName());
            rowMap.put("vendorNo", statisticVo.getVendorNo());
            rowMap.put("vendorName", statisticVo.getVendorName());
            rowMap.put("productNo", statisticVo.getProductNo());
            rowMap.put("productName", statisticVo.getProductName());
            rowMap.put("totalNetAmount", statisticVo.getTotalNetAmount());
            rowMap.put("totalVendorFee", statisticVo.getTotalVendorFee());
            rowMap.put("orderFee", statisticVo.getOrderFee());
            recordList.add(rowMap);
        }
        return recordList;
    }

    private List<Map<String, Object>> listAgentMergeFee(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<AgentFeeStatisticVo>> pageResult = agentFeeOrderQueryFacade.agentFeeStatistics(param, pageParam);
        List<Map<String, Object>> recordList = new ArrayList<>();
        for (AgentFeeStatisticVo statisticVo : pageResult.getData()) {
            Map<String, Object> rowMap = new HashMap<>();
            rowMap.put("mchNo", statisticVo.getMchNo());
            rowMap.put("mchName", statisticVo.getMchName());
            rowMap.put("vendorNo", statisticVo.getVendorNo());
            rowMap.put("vendorName", statisticVo.getVendorName());
            rowMap.put("productNo", statisticVo.getProductNo());
            rowMap.put("productName", statisticVo.getProductName());
            rowMap.put("agentNo", statisticVo.getAgentNo());
            rowMap.put("agentName", statisticVo.getAgentName());
            rowMap.put("rewardType", statisticVo.getRewardType());
            rowMap.put("realProfitRatio", statisticVo.getRealProfitRatio());
            rowMap.put("agentFeeRate", statisticVo.getAgentFeeRate());
            rowMap.put("totalNetAmount", statisticVo.getTotalNetAmount());
            rowMap.put("totalOrderFee", statisticVo.getTotalOrderFee());
            rowMap.put("agentCost", statisticVo.getAgentCost());
            rowMap.put("agentProfit", statisticVo.getAgentProfit());
            recordList.add(rowMap);
        }
        return recordList;
    }

    private List<Map<String, Object>> listSalerMergeFee(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<SalesFeeStatisticVo>> pageResult = salesFeeOrderQueryFacade.salerFeeStatistics(param, pageParam);
        List<Map<String, Object>> recordList = new ArrayList<>();
        for (SalesFeeStatisticVo statisticVo : pageResult.getData()) {
            Map<String, Object> rowMap = new HashMap<>();
            rowMap.put("employerNo", statisticVo.getEmployerNo());
            rowMap.put("employerName", statisticVo.getEmployerName());
            rowMap.put("mainstayNo", statisticVo.getMainstayNo());
            rowMap.put("mainstayName", statisticVo.getMainstayName());
            rowMap.put("productNo", statisticVo.getProductNo());
            rowMap.put("productName", statisticVo.getProductName());
            rowMap.put("departmentId", statisticVo.getDepartmentId());
            rowMap.put("departmentName", statisticVo.getDepartmentName());
            rowMap.put("salerId", statisticVo.getSalerId());
            rowMap.put("salerName", statisticVo.getSalerName());
            rowMap.put("totalNetAmount", statisticVo.getTotalNetAmount());
            rowMap.put("totalMerchantFee", statisticVo.getTotalMerchantFee());
            rowMap.put("totalSalesFee", statisticVo.getTotalSalesFee());
            rowMap.put("salesProfit", statisticVo.getSalesProfit());
            rowMap.put("orderCount", statisticVo.getOrderCount());
            recordList.add(rowMap);
        }
        return recordList;
    }

    private List<Map<String, Object>> listCkAccountDetail(Map<String, Object> param, PageParam pageParam) {
        Page page = new Page<>();
        page.setCurrent(pageParam.getPageCurrent());
        page.setSize(pageParam.getPageSize());
        Page<CkAccountDetail> pageResult = ckAccountDetailFacade.listBy(param, page);
        List<CkAccountDetail> accountDetailList = pageResult.getRecords();
        return accountDetailList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getAmountWithMerchantInfo(Map<String, Object> param) {
        final PageResult<List<Map<String, String>>> pageResult = employerAccountInfoFacade.groupByMch(param, PageParam.newInstance(1, 999999));
        final List<Map<String, String>> list = pageResult.getData();
        if (list == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂无可导出数据");
        }
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, String> map : list) {
            final String employerNo = map.get("employerNo");
            final String employerName = map.get("employerName");

            Map<String, Object> resultItem = new HashMap<>();
            Map<String, Object> accountMap = this.getAmountByEmployerNoAnyMainstayNo(null, employerNo);
            final List<Map<String, Object>> amountList = (List<Map<String, Object>>) accountMap.get("amountList");
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (Map<String, Object> item : amountList) {
                String bankAmount = (String) item.get("bankAmount");
                String aliPayAmount = (String) item.get("aliPayAmount");
                String weixinAmount = (String) item.get("weixinAmount");
                bankAmount = StringUtil.isDecimal(bankAmount) ? bankAmount : "0";
                aliPayAmount = StringUtil.isDecimal(aliPayAmount) ? aliPayAmount : "0";
                weixinAmount = StringUtil.isDecimal(weixinAmount) ? weixinAmount : "0";
//                log.info("bankAmount={},alipayAmount={},weixinAmount={}",bankAmount,aliPayAmount,weixinAmount);
                BigDecimal mainstayAmount = new BigDecimal(bankAmount).add(new BigDecimal(aliPayAmount)).add(new BigDecimal(weixinAmount));
                totalAmount = totalAmount.add(mainstayAmount);
            }
            resultItem.put("totalAmount", totalAmount);
            resultItem.put("employerName", employerName);
            resultItem.put("EmployerNo", employerNo);
            result.add(resultItem);
        }
        return result;
    }

    private List<Map<String, Object>> getOfflineFailOrderItem(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<OfflineOrderItemFail>> pageResult = offlineOrderItemFacade.pageByPlatBatchNo((String) paramMap.get("platBatchNo"), pageParam);
        List<OfflineOrderItemFail> orderItemList = pageResult.getData();
        return orderItemList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getOfflineOrderItem(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<OfflineOrderItem>> pageResult = offlineOrderItemFacade.listPage(param, pageParam);
        return pageResult.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getRiskHandledOrderList(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<RiskcontrolProcessDetail>> page = riskControlFacade.listPage(param, pageParam);
        return page.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getAgreementList(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<AgreementExportVo>> page = agreementFacade.getExportList(param, pageParam);
        return page.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getSupplierWorkFlowHandleList(Map<String, Object> param, PageParam pageParam) {
        return flowFacade.workOrderPage(PlatformSource.SUPPLIER.getValue(), param, pageParam).getData();
    }

    private List<Map<String, Object>> getWorkFlowHandleList(Map<String, Object> paramMap, PageParam pageParam) {
        Boolean isAdmin = (Boolean) paramMap.get("isAdmin");
        Long currentUserId = ((Integer) paramMap.get("userId")).longValue();
        Integer platform = (Integer) paramMap.get("platformSource");
        return flowFacade.handleList(currentUserId, platform, isAdmin, paramMap, pageParam).getData();
    }

    private List<Map<String, Object>> getWorkFlowCarbonList(Map<String, Object> paramMap, PageParam pageParam) {
        Long currentUserId = ((Integer) paramMap.get("userId")).longValue();
        Integer platform = (Integer) paramMap.get("platformSource");
        return flowFacade.carbonList(currentUserId, platform, paramMap, pageParam).getData();
    }

    private List<Map<String, Object>> getWorkFlowTodoList(Map<String, Object> paramMap, PageParam pageParam) {
        String json = (String) paramMap.get("flowUserVo");
        FlowUserVo flowUserVo = JsonUtil.toBean(json, FlowUserVo.class);
        Boolean isAdmin = (Boolean) paramMap.get("isAdmin");
        return flowFacade.todoList(flowUserVo, isAdmin, paramMap, pageParam).getData();
    }

    private List<Map<String, Object>> getWxIncomeRecord(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<WxIncomeRecord>> pageList = wxIncomeRecordFacade.listPage(param, pageParam);
        pageList.getData().forEach(x -> x.setAmountYuan(AmountUtil.changeToYuan(x.getAmount())));
        return pageList.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getInvoiceAccountRecord(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<AccountInvoice>> pageList = accountInvoiceManageFacade.listAccountPage(param, pageParam);
        return pageList.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getEmployerMainstayRelation(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<EmployerMainstayRelation>> pageList = employerMainstayRelationFacade.listPage(param, pageParam);
        return pageList.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    @Reference
    private AgentSalerFacade agentSalerFacade;
    @Reference
    private AgentCredentialFacade agentCredentialFacade;
    @Reference
    private AgentBankAccountFacade agentBankAccountFacade;
    @Reference
    private AgentEmployerMainFacade agentEmployerMainFacade;
    @Reference
    private AgentProductQuoteFacade agentProductQuoteFacade;
    @Reference
    private MerchantFileFacade merchantFileFacade;

    private List<Map<String, Object>> listVoPage(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<AgentResVo>> pageList = agentFacade.listVoPage(param, pageParam);
        final String fastDfsBaseUrl = dataDictionaryFacade.getSystemConfig("fastDfsBaseUrl");

        List<Map<String, Object>> datas = new ArrayList<>();
        if (pageList != null && pageList.getData() != null) {
            final List<AgentResVo> agents = pageList.getData();
            for (AgentResVo agent : agents) {
                final String agentNo = agent.getAgentNo();
                //0. 查询合伙人
                final Agent byAgentNo = agentFacade.getByAgentNo(agentNo);
                //1. 查询合伙人销售关系表
                final AgentSaler agentSaler = agentSalerFacade.getByAgentNo(agentNo);
                //2. 查询合伙人银行账户表
                final AgentBankAccount agentBankAccount = agentBankAccountFacade.getByAgentNo(agentNo);
                //3. 查询合伙人证件表
                final AgentCredential agentCredential = agentCredentialFacade.getAgentCredential(agentNo);
                //4. 查询合伙人主体信息表
                final AgentEmployerMain agentEmployerMain = agentEmployerMainFacade.getByAgentNo(agentNo);
                //5. 查询合伙人产品报价单
                final List<AgentProductQuote> agentProductQuotes = agentProductQuoteFacade.getByAgentNo(agentNo);
                //6. 查询合伙人文件表
                final List<MerchantFile> merchantFiles = merchantFileFacade.listByMchNo(agentNo);

                AgentExportDetailVo exportDetailVo = new AgentExportDetailVo();
                exportDetailVo.setAgent(byAgentNo);
                exportDetailVo.setAgentBankAccount(agentBankAccount);
                exportDetailVo.setAgentCredential(agentCredential);
                exportDetailVo.setAgentEmployerMain(agentEmployerMain);
                exportDetailVo.setMerchantFiles(merchantFiles);
                exportDetailVo.setAgentProductQuotes(agentProductQuotes);
                exportDetailVo.setAgentSaler(agentSaler);
                final Map<String, Object> dataMap = exportDetailVo.toMap(fastDfsBaseUrl);
                datas.add(dataMap);
            }
        }
        return datas;
    }

    private List<Map<String, Object>> getInvoiceRecordList(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<InvoiceRecord>> list = invoiceFacade.listPage(param, pageParam);
        if (list.getData() != null) {
            for (InvoiceRecord record : list.getData()) {
                record.setPostAddress(record.getProvince() + record.getCity() + record.getCounty() + record.getAddress());
            }
        }
        return list.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getFreelanceStatInfoList(Map<String, Object> param, PageParam pageParam) {
        List<FreelanceStatVo> list = freelanceStat.list(param, pageParam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getMerchantStatInfoList(Map<String, Object> param, PageParam pageParam) {
        List<MerchantStatVo> list = merchantStat.list(param, pageParam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getMerchantList(Map<String, Object> param) {
        String supplierNo = (String) param.get("supplierNo");
        //从代征关系表找出商户列表
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        paramMap.put("mainstayNo", supplierNo);
        List<String> mchNoList = employerMainstayRelationFacade.listBy(paramMap).
                stream().map(EmployerMainstayRelation::getEmployerNo).collect(Collectors.toList());

        //报价单参数
        param.put("mchNos", mchNoList);
        List<MerchantEmployerDetailVo> detailVoList = employerFacade.getInfoVoList(param);
        return detailVoList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listMerchangInfo(Map<String, Object> param) {
        List<MerchantEmployerDetailVo> detailVoList = employerFacade.getInfoVoList(param);
        return detailVoList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    @SuppressWarnings({"unchecked"})
    private List<Map<String, Object>> listDataDictionary(Map<String, Object> param) {
        if (!param.containsKey("idList")) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("没有id参数");
        }
        List<DataDictionary> list = dataDictionaryFacade.listDataDictionary((List<Long>) param.get("idList"));
        return list.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listAgentFunction(Map<String, Object> param, PageParam pageParam) {
        List<AgentFunction> result = agentFunctionFacade.listAll(param, pageParam);
        if (result.isEmpty()) {
            return null;
        }
        result.forEach(item -> {
            item.setParentPermissionFlag(agentFunctionFacade.getPermissionFlag(item.getParentId()));
        });
        return result.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listSupplyFunction(Map<String, Object> param, PageParam pageParam) {
        List<SupplierFunction> result = supplierFunctionFacade.listAll(param, pageParam);
        if (result.isEmpty()) {
            return null;
        }
        result.forEach(item -> {
            item.setParentPermissionFlag(supplierFunctionFacade.getPermissionFlag(item.getParentId()));
        });
        return result.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listMerchantFunction(Map<String, Object> param, PageParam pageParam) {
        List<EmployerFunction> result = employerFunctionFacade.listAll(param, pageParam);
        if (result.isEmpty()) {
            return null;
        }
        result.forEach(item -> {
            item.setParentPermissionFlag(employerFunctionFacade.getPermissionFlag(item.getParentId()));
        });
        return result.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listPmsFunction(Map<String, Object> param, PageParam pageParam) {
        List<PmsFunction> result = permissionFacade.listAllFunction(param, pageParam);
        if (result.isEmpty()) {
            return null;
        }
        result.forEach(item -> {
            item.setParentPermissionFlag(permissionFacade.getPermissionFlag(item.getParentId()));
        });
        return result.stream().map(BeanUtil::toMap).collect(Collectors.toList());

    }

    private List<Map<String, Object>> listAgentMonthBill(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<AgentMonthBillVo>> pageResult = agentMonthBillFacade.listPage(param, pageParam);
        return pageResult.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    public void handlerParamMap(Integer reportType, Map<String, Object> paramMap) {
        //paramMap需要的额外处理
        if (Objects.equals(reportType, ReportTypeEnum.TRADE_ORDER_ITEM_MER.getValue())
                || Objects.equals(reportType, ReportTypeEnum.TRADE_ORDER_ITEM_PMS.getValue())
                || Objects.equals(reportType, ReportTypeEnum.TRADE_RECORD_ITEM_PMS.getValue())
                || Objects.equals(reportType, ReportTypeEnum.TRADE_ORDER_ITEM_SUP.getValue())) {
            ExportUtil.dealStr2Date(paramMap);
        }
    }

    private List<Map<String, Object>> getRecordItem(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<RecordItem>> pageResult = recordItemFacade.listPage(paramMap, pageParam);
        List<RecordItem> recordItemList = pageResult.getData();
        return recordItemList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getOrderItem(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<OrderItem>> pageResult = orderItemFacade.listPage(paramMap, pageParam);
        List<OrderItem> orderItemList = pageResult.getData();
        return orderItemList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listFailItemByBatchNo(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<OrderItemFail>> pageResult = orderItemFacade.pageByPlatBatchNo((String) paramMap.get("platBatchNo"), pageParam);
        List<OrderItemFail> orderItemList = pageResult.getData();
        return orderItemList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listRechargeRecord(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<RechargeRecord>> pageResult = rechargeQueryFacade.list(paramMap, pageParam);
        List<RechargeRecord> orderItemList = pageResult.getData();
        return orderItemList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> getMerchantInfoPms(Map<String, Object> param) {
        //String mchNo = (String) param.get("mchNo");
        MerchantEmployerDetailVo detailVo = employerFacade.getInfoVO(param, null);

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(BeanUtil.toMap(detailVo));
        return list;
    }

    private List<Map<String, Object>> getAgentDetail(Map<String, Object> param) {
        String agentNo = (String) param.get("agentNo");
        AgentDetailVo detailVo = agentFacade.getDetailByAgentNo(agentNo);

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(BeanUtil.toMap(detailVo));
        return list;
    }

    private List<Map<String, Object>> listMerchantFeeOrder(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<MerchantFeeOrder>> pageResult = merchantFeeOrderQueryFacade.listPage(param, pageParam);
        return pageResult.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listAgentFeeOrder(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<AgentFeeOrder>> pageResult = agentFeeOrderQueryFacade.listPage(param, pageParam);
        return pageResult.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listVendorFeeOrder(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<VendorFeeOrder>> pageResult = vendorFeeOrderQueryFacade.listPage(param, pageParam);
        return pageResult.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listSalesFeeOrder(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<SalesFeeOrder>> pageResult = salesFeeOrderQueryFacade.listPage(param, pageParam);
        return pageResult.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listHangUpOrder(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<RiskcontrolOrderItem>> pageResult = riskControlFacade.listPagePendingOrder(param, pageParam);
        return pageResult.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listSignRecord(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<SignRecord>> pageResult = signRecordFacade.listPage(param, pageParam);
        return pageResult.getData().stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }

    private List<Map<String, Object>> listWithdrawRecord(Map<String, Object> param, PageParam pageParam) {
//        final WithdrawRecordQueryDto withdrawRecordQueryDto = new JSONObject(param).toJavaObject(WithdrawRecordQueryDto.class);
        PageResult<List<WithdrawRecord>> pageResult = withdrawRecordFacade.listWithRecordPage(param, pageParam);
        List<WithdrawRecord> orderItemList = pageResult.getData();
        return orderItemList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
    }


    public Map<String, Object> getAmountByEmployerNoAnyMainstayNo(String mainstayNo, String employerNo) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("employerNo", employerNo);
//        paramMap.put("status",OpenOffEnum.OPEN.getValue());
        List<EmployerMainstayRelation> employerMainstayRelationList = employerMainstayRelationFacade.listBy(paramMap);
        List<Map<String, Object>> amountList = new ArrayList<>();
        employerMainstayRelationList.forEach(employerMainstayRelation -> {
            EmployerAccountInfoVo employerAccountInfoVo = getByMainstayNoAndEmployerNo(employerMainstayRelation.getMainstayNo(), employerNo);
            Map<String, Object> infoMap = new HashMap<>();
            infoMap.put("mainstayName", employerAccountInfoVo.getMainstayName());
            infoMap.put("mainstayNo", employerAccountInfoVo.getMainstayNo());
            employerAccountInfoVo.getChannelInfos().forEach(subChannelInfoVo -> {
                Integer channelType = subChannelInfoVo.getChannelType();
                String amount;
                try {
                    if (subChannelInfoVo.getStatus().equals(OpenOffEnum.OFF.getValue())) {
                        amount = "未开通";
                    } else {
                        amount = getAmount(subChannelInfoVo, employerAccountInfoVo, channelType);
                        if (StringUtils.isEmpty(amount)) {
                            log.error("[{}]余额查询异常,通道：{}", employerNo, subChannelInfoVo.getChannelType());
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询异常");
                        }
                    }
                } catch (Exception e) {
                    amount = "异常";
                }
                if (channelType.equals(ChannelTypeEnum.BANK.getValue())) {
                    infoMap.put("bankAmount", amount);
                } else if (channelType.equals(ChannelTypeEnum.ALIPAY.getValue())) {
                    infoMap.put("aliPayAmount", amount);
                } else if (channelType.equals(ChannelTypeEnum.WENXIN.getValue())) {
                    infoMap.put("weixinAmount", amount);
                }
            });
            amountList.add(infoMap);
        });
        resultMap.put("amountList", amountList);
        return resultMap;
    }

    public EmployerAccountInfoVo getByMainstayNoAndEmployerNo(String mainstayNo, String employerNo) {
        List<EmployerAccountInfo> list = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(employerNo, mainstayNo);
        if (ObjectUtils.isEmpty(list) || list.get(0) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(
                    "该用工企业在相关供应商下无信息，请先建立代征关系，供应商编号：" + mainstayNo + ",用工企业编号：" + employerNo);
        }
        EmployerAccountInfoVo employerAccountInfoVo = new EmployerAccountInfoVo();
        BeanUtils.copyProperties(list.get(0), employerAccountInfoVo);

        List<SubChannelInfoVo> channelList = list.stream().map(
                record -> {
                    SubChannelInfoVo subChannelInfoVo = new SubChannelInfoVo();
                    BeanUtils.copyProperties(record, subChannelInfoVo);
                    return subChannelInfoVo;
                }
        ).collect(Collectors.toList());
        employerAccountInfoVo.setChannelInfos(channelList);
        return employerAccountInfoVo;
    }

    /**
     * 获取各通道余额
     *
     * @param channelInfo
     * @param employerAccountInfoVo
     * @param channelType
     * @return
     */
    private String getAmount(SubChannelInfoVo channelInfo, EmployerAccountInfoVo employerAccountInfoVo, Integer channelType) throws Exception {
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setMainstayNo(employerAccountInfoVo.getMainstayNo());
        amountQueryDto.setEmployerNo(employerAccountInfoVo.getEmployerNo());
        amountQueryDto.setChannelType(channelType);
        amountQueryDto.setChannelNo(channelInfo.getPayChannelNo());
        amountQueryDto.setChannelMchNo(channelInfo.getParentMerchantNo());
        amountQueryDto.setSubMerchantNo(channelInfo.getSubMerchantNo());
        amountQueryDto.setAgreementNo(channelInfo.getSubAgreementNo());
        return accountQueryFacade.getAmount(amountQueryDto);
    }

    /**
     * 获取预开发票记录列表
     */
    private List<Map<String, Object>> getInvoicePreRecordList(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<InvoicePreRecord>> pageResult = invoicePreFacade.listPage(param, pageParam);
        if (pageResult != null && pageResult.getData() != null) {
            List<InvoicePreRecord> data = pageResult.getData();
            return data.stream().map(BeanUtil::toMap).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
