package com.zhixianghui.service.export.task;

import com.zhixianghui.common.statics.enums.export.ExportStatusEnum;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.service.export.core.biz.ExportRecordBiz;
import com.zhixianghui.service.export.export.ExcelExportBiz;
import com.zhixianghui.service.export.export.PdfExportBiz;
import com.zhixianghui.service.export.export.ZipExportBiz;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 导出任务
 *
 * <AUTHOR>
 * @date 2020/8/28
 **/
@Slf4j
@Component
public class ExportTask {

    @Autowired
    private ThreadPoolTaskExecutor exportTaskExecutor;
    @Autowired
    private ExcelExportBiz excelExportBiz;
    @Autowired
    private PdfExportBiz pdfExportBiz;
    @Autowired
    private ZipExportBiz zipExportBiz;
    @Autowired
    private ExportRecordBiz exportRecordBiz;
    @Autowired
    private RedisLock redisLock;

    /**
     * 开启队列消费线程
     */
    @PostConstruct
    public void start() {
        Worker worker = new Worker();
        exportTaskExecutor.execute(worker);
    }

    /**
     * 从数据库中查出待导出的记录，放入队列中
     */
    public void doAddTask(Integer pageSize) {
        String clientId = null;
        try{
            // 获取添加任务锁
            clientId = redisLock.tryLockLong(ReportConstant.EXPORT_TASK_LOCK_NAME, 0, ReportConstant.EXPORT_TASK_LOCK_TIME_1);
            if(clientId == null ){
                log.info("{} 没有获取到导出任务锁，可能有其他线程正在处理",Thread.currentThread().getName());
                return;
            }
            // 获取待导出列表
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("exportStatus", ExportStatusEnum.CREATE.getValue());
            paramMap.put("direction", 0);
            PageResult<List<ExportRecord>> pageResult = exportRecordBiz.listPage(paramMap, PageParam.newInstance(1, pageSize));
            if (CollectionUtils.isNotEmpty(pageResult.getData())) {
                log.info("添加任务{}条", pageResult.getData().size());
                pageResult.getData().stream()
                        .forEach(x -> {
                            Worker worker = new Worker();
                            worker.setExportRecord(x);
                            exportTaskExecutor.execute(worker);
                        });
            }
        } finally {
            if(clientId != null){
                redisLock.unlockLong(clientId);
            }
        }

    }
    public static void main(String[] args) {
        System.out.println(ReportTypeEnum.getEnum(33).getFileType());
    }

    /**
     * 运行导出任务的线程
     */
    @Data
    private class Worker implements Runnable {

        private ExportRecord exportRecord;


        @Override
        public void run() {
            // 如果当前活动线程等于最大线程，那么不执行
            if (exportTaskExecutor.getActiveCount() < exportTaskExecutor.getMaxPoolSize()) {
                final ExportRecord record = this.exportRecord;
                log.debug("线程：{}，获取到导出任务：{}", Thread.currentThread().getName(), JsonUtil.toString(record));
                if (record != null) {
                    try {
                        int fileType = ReportTypeEnum.getEnum(record.getReportType()).getFileType();
                        if(Objects.equals(fileType, FileTypeEnum.EXECL.getValue())){
                            excelExportBiz.export(record);
                        } else if (Objects.equals(fileType, FileTypeEnum.PDF.getValue())){
                            pdfExportBiz.export(record);
                        }
                        else if (Objects.equals(fileType, FileTypeEnum.ZIP.getValue())){
                            zipExportBiz.export(record);
                        }else if (Objects.equals(fileType,FileTypeEnum.PARAM.getValue())){
                            Map<String, Object> paramMap = JsonUtil.toBean(record.getParamJson(), HashMap.class);
                            Integer exportFileType = (Integer) paramMap.get("exportFileType");
                            if (exportFileType == null){
                                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("没有对应的类型");
                            }
                            //根据参数判断
                            if(Objects.equals(exportFileType, FileTypeEnum.EXECL.getValue())){
                                excelExportBiz.export(record);
                            } else if (Objects.equals(exportFileType, FileTypeEnum.PDF.getValue())){
                                pdfExportBiz.export(record);
                            }
                            else if (Objects.equals(exportFileType, FileTypeEnum.ZIP.getValue())){
                                zipExportBiz.export(record);
                            }
                        }
                    } catch (Exception e) {
                        log.error("执行导出任务{}, 出现异常：{}", JsonUtil.toString(record), e);
                    }
                }
            } else {
                log.info("threadPool is reach max size{}", exportTaskExecutor.getActiveCount());
            }
        }
    }
}
