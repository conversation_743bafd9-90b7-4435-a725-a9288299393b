package com.zhixianghui.service.export.task;

import cn.hutool.core.io.FileUtil;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.api.base.utils.SignUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.ApiRespCodeEnum;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.notify.NotifyContent;
import com.zhixianghui.facade.common.entity.push.PushManager;
import com.zhixianghui.facade.common.enums.PushTypeEnum;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.enums.ServerTypeEnum;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.export.core.vo.PushCallbackVo;
import com.zhixianghui.service.export.helper.CacheBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName AbstractPushJob
 * @Description TODO
 * @Date 2023/2/22 16:45
 */
@Slf4j
@Component
public abstract class AbstractPushJob {

    @Autowired
    protected CacheBiz cacheBiz;

    @Reference
    protected NotifyFacade notifyFacade;

    //推送时间
    protected ThreadLocal<String> pushDate = new ThreadLocal<>();

    public void start(PushManager pushManager,String date) {
        pushDate.set(date);
        //获取导出文件
        File file = null;
        try {
            file = getFile(pushManager);
            if (file != null){
                if (pushManager.getServerType().intValue() == ServerTypeEnum.SFTP.getValue()){
                    transferToSftp(pushManager,file);
                }else {
                    transfetToFtp(pushManager,file);
                }
                //完成后回调通知给商户
                if (StringUtils.isNotBlank(pushManager.getCallbackUrl())){
                    notifyMerchant(pushManager,file);
                }
            }
        }finally {
            //最后删除本地文件
            if (file != null){
                FileUtil.del(file);
            }
            pushDate.remove();
        }

    }

    /**
     * 发送回调消息
     * @param pushManager
     * @param file
     */
    private void notifyMerchant(PushManager pushManager, File file) {
        String key = UUIDUitl.generateString(16);
        log.info("文件推送完成，开始发送回调信息，商户号：[{}]，推送类型：[{}],日期：[{}]",
                pushManager.getMchNo(), PushTypeEnum.getEnum(pushManager.getPushType()).getDesc(),pushDate.get());
        MerchantNotifyParam merchantNotifyParam = buildNotifyBody(file,pushManager);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_NOTIFY_MERCHANT,
                merchantNotifyParam.getNotifyContent().getMchNo(),
                key, NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_NOTIFY_MERCHANT,
                JsonUtil.toString(merchantNotifyParam));
    }

    /**
     * 构建通知消息
     * @param file
     * @param pushManager
     * @return
     */
    private MerchantNotifyParam buildNotifyBody(File file,PushManager pushManager) {
        PushCallbackVo pushCallbackVo = new PushCallbackVo();
        pushCallbackVo.setFileSize(file.length());
        pushCallbackVo.setCreateTime(new Date());
        pushCallbackVo.setFileName(file.getName());
        pushCallbackVo.setFileExt(FilenameUtils.getExtension(file.getName()));

        MerchantNotifyParam merchantNotifyParam = new MerchantNotifyParam();
        merchantNotifyParam.setNotifyContent(new NotifyContent());
        merchantNotifyParam.getNotifyContent().setMchNo(pushManager.getMchNo());
        merchantNotifyParam.getNotifyContent().setData(JsonUtil.toString(pushCallbackVo));
        merchantNotifyParam.getNotifyContent().setRespCode(ApiRespCodeEnum.SUCCESS.getCode());
        merchantNotifyParam.getNotifyContent().setRandStr(UUIDUitl.generateString(32));
        merchantNotifyParam.getNotifyContent().setSignType(String.valueOf(SignTypeEnum.RSA.getValue()));
        MerchantSecret merchantSecret = cacheBiz.getMerchantSecretByMchNo(pushManager.getMchNo());
        String priKey = merchantSecret.getPlatformPrivateKey();
        String sign = SignUtil.sign(merchantNotifyParam.getNotifyContent(), Integer.parseInt(merchantNotifyParam.getNotifyContent().getSignType()),priKey);
        merchantNotifyParam.getNotifyContent().setSign(sign);
        merchantNotifyParam.getNotifyContent().setSecKey(RSAUtil.encryptByPublicKey(UUIDUitl.generateString(16),merchantSecret.getMerchantPublicKey()));
        merchantNotifyParam.setNotifyUrl(pushManager.getCallbackUrl());
        return merchantNotifyParam;
    }

    /**
     *
     * @param pushManager
     * @param file
     */
    private void transfetToFtp(PushManager pushManager,File file){
        FTPClient ftpClient = null;
        try {
            ftpClient = FtpUtil.connection(pushManager.getSftpIp(), Integer.parseInt(pushManager.getSftpPort()), pushManager.getUsername(),pushManager.getPassword());
            //上传ftp文件
            FtpUtil.uploadFile(ftpClient, pushManager.getSftpPath(), "/",file.getName(), org.apache.commons.io.FileUtils.openInputStream(file));
        }catch (Exception e){
            log.error("文件推送失败，商户号：[{}]，推送类型：[{}]",pushManager.getMchNo(), PushTypeEnum.getEnum(pushManager.getPushType()).getDesc());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件上传失败");
        }finally {
            //退出ftp
            if (ftpClient != null){
                FtpUtil.ftpLogout(ftpClient);
            }
        }
    }

    /**
     *
     * @param pushManager
     * @param file
     */
    private void transferToSftp(PushManager pushManager, File file) {
        ChannelSftp sftp;
        try {
            sftp = SftpUtil.connectNotSafe(
                    pushManager.getSftpIp(),
                    Integer.parseInt(pushManager.getSftpPort()),
                    pushManager.getUsername(),
                    pushManager.getPassword());

            if (sftp != null){
                SftpUtil.uploadNotSafe(pushManager.getSftpPath(),file,sftp);
            }
        } catch (Exception e){
            log.error("文件推送失败，商户号：[{}]，推送类型：[{}]",pushManager.getMchNo(), PushTypeEnum.getEnum(pushManager.getPushType()).getDesc());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件上传失败");
        }
    }

    /**
     * 获取推送文件
     * @param pushManager
     * @return
     */
    public abstract File getFile(PushManager pushManager);
}
