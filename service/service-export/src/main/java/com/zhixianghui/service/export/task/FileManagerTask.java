package com.zhixianghui.service.export.task;

import cn.hutool.extra.spring.SpringUtil;
import com.sun.corba.se.spi.orbutil.threadpool.Work;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.common.entity.push.PushManager;
import com.zhixianghui.facade.common.enums.PushTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FileManagerTask
 * @Description TODO
 * @Date 2023/2/22 15:10
 */
@Slf4j
@Component
public class FileManagerTask {

    private static final Map<Integer,Class<? extends AbstractPushJob>> beanMap = new HashMap<>();

    //推送种类
    static {
        beanMap.put(PushTypeEnum.DAILY_ORDER.getValue(),DailyOrderJob.class);
    }

    @Autowired
    private ThreadPoolTaskExecutor pushTaskExecutor;

    public void addTask(List<PushManager> managerList,String date) {
        log.info("推送定时任务开始执行，共计任务：[{}]条，日期参数：[{}]",managerList.size(),date);
        int i = 0;
        for (PushManager pushManager : managerList) {
            i++;
            Worker worker = new Worker();
            worker.setPushManager(pushManager);
            worker.setNum(i);
            worker.setPushDate(date);
            pushTaskExecutor.execute(worker);
        }
    }

    @Data
    private class Worker implements Runnable{

        private PushManager pushManager;
        private int num;
        private String pushDate;

        @Override
        public void run() {
            PushTypeEnum pushTypeEnum = PushTypeEnum.getEnum(pushManager.getPushType());
            log.info("推送任务开始执行，当前第[{}]条，商户号：[{}]，推送类型：[{}]",num,pushManager.getMchNo(),pushTypeEnum.getDesc());
            AbstractPushJob abstractPushJob = SpringUtil.getBean(beanMap.get(pushTypeEnum.getValue()));
            abstractPushJob.start(pushManager,pushDate);
        }
    }

}
