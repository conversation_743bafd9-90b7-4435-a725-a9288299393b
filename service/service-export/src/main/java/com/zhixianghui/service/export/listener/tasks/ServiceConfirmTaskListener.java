package com.zhixianghui.service.export.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.enums.FlowStatusEnum;
import com.zhixianghui.facade.flow.enums.WorkTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.service.WorkOrderExtFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.service.export.listener.TaskRocketMQListener;
import com.zhixianghui.service.export.task.MonthServiceConfirmTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @ClassName ServiceConfirmTaskListener
 * @Description TODO
 * @Date 2022/4/2 10:04
 */
@Slf4j
@Component
public class ServiceConfirmTaskListener {

    @Autowired
    private MonthServiceConfirmTask monthServiceConfirmTask;

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private WorkOrderExtFacade workOrderExtFacade;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_SERVICE_CONFIRM_TASK, consumeThreadMax = 1, consumerGroup = "serviceConfirmTask")
    public class ServiceConfirmStartListener extends TaskRocketMQListener<JSONObject> {

        @Override
        public void runTask(JSONObject jsonParam) {
            String path = jsonParam.getString("path");
            if (StringUtils.isBlank(path)){
                log.error("模板文件不存在，导出失败");
                return;
            }
            Integer pageSize = jsonParam.getInteger("pageSize");
            String date = jsonParam.getString("date");
            String mchNo = jsonParam.getString("mchNo");
            String mainstayNo = jsonParam.getString("mainstayNo");
            CompletableFuture.runAsync(()-> monthServiceConfirmTask.doAddTask(path,pageSize,date,mchNo,mainstayNo));
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_SERVICE_CONFIRM_CLOSE,consumeThreadMax = 1,consumerGroup = "serviceConfirmCloseGroup")
    public class ServiceConfirmCloseListener extends TaskRocketMQListener<JSONObject>{

        @Override
        public void runTask(JSONObject jsonParam) {
            //获取所有过期工单
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("workType", WorkTypeEnum.WORK_FLOW.getValue());
            paramMap.put("status", FlowStatusEnum.PENDING.getValue());
            paramMap.put("nowDate",new Date());
            List<Long> ids = workOrderExtFacade.getCommonFlowId(paramMap);
            if (ids.size() > 0){
                FlowUserVo flowUserVo = new FlowUserVo();
                flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
                flowUserVo.setUserName("超时关闭");
                flowUserVo.setUserId(1L);
                CompletableFuture.runAsync(()->{
                    for (Long id : ids) {
                        log.info("工单超时关闭，流程id：[{}]",id);
                        flowFacade.deleteProcessInstance(id,flowUserVo,true,"超时关闭");
                    }
                });
            }
        }
    }
}
