package com.zhixianghui.service.export.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/1
 **/
@Component
@Slf4j
public class HelperBiz {
    @Autowired
    private CacheBiz cacheBiz;
    @Reference
    private SequenceFacade sequenceFacade;

    public void checkExportField(ExportRecord record){

        Assert.notEmpty(record.getFieldInfoList(), "导出字段列表为空");

        DataDictionary fieldDictionary = cacheBiz.getByDateName(ReportTypeEnum.getEnum(record.getReportType()).getDataName());
        if(!Objects.equals(fieldDictionary.getSystemType(), SystemTypeEnum.COMMON_MANAGEMENT.getValue())
            && !Objects.equals(fieldDictionary.getSystemType(), record.getSystemType())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("导出字段集字典归属其他系统");
        }

        Set<String> fieldDictionarySet = fieldDictionary.getItemList().stream().map(x -> x.getCode()).collect(Collectors.toSet());
        Set<String> exportFieldSet = record.getFieldInfoList().stream().map(ExportRecord.FieldInfo::getFieldCode).collect(Collectors.toSet());
        if (!fieldDictionarySet.containsAll(exportFieldSet)) {
            log.warn("fieldDictionarySet:{}", JSON.toJSONString(fieldDictionarySet));
            log.warn("exportFieldSet:{}",JSON.toJSONString(exportFieldSet));
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("含有非法导出字段，请检查");
        }


    }

    public String genFileNo() {
        return sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.EXPORT_FILE_SEQ.getPrefix(),
                SequenceBizKeyEnum.EXPORT_FILE_SEQ.getKey(), SequenceBizKeyEnum.EXPORT_FILE_SEQ.getWidth());
    }
}
