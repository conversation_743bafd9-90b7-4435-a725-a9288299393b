package com.zhixianghui.service.export.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.common.entity.push.PushManager;
import com.zhixianghui.facade.common.service.PushManagerFacade;
import com.zhixianghui.service.export.listener.TaskRocketMQListener;
import com.zhixianghui.service.export.task.FileManagerTask;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName PushManagerListener
 * @Description TODO
 * @Date 2023/2/22 14:35
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_PUSH_TASK,consumeThreadMax = 3,consumerGroup = "PushFileManagerConsumer")
public class PushManagerListener extends TaskRocketMQListener<JSONObject> {

    @Reference
    private PushManagerFacade pushManagerFacade;

    @Autowired
    private FileManagerTask fileManagerTask;

    @Override
    public void runTask(JSONObject jsonObject) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",jsonObject.getString("mchNo"));
        paramMap.put("pushType",jsonObject.get("pushType"));
        String date = jsonObject.getString("date");
        String beginRangeDate = jsonObject.getString("beginRangeDate");
        String endRangeDate = jsonObject.getString("endRangeDate");
        List<PushManager> managerList = pushManagerFacade.listBy(paramMap);
        if (StringUtils.isNotBlank(beginRangeDate) && StringUtils.isNotBlank(endRangeDate)){
            List<String> strList = DateUtil.changeStringList(DateUtil.getBetweenDates(beginRangeDate,endRangeDate));
            for (String s : strList) {
                fileManagerTask.addTask(managerList,s);
            }
        }else{
            fileManagerTask.addTask(managerList,date);
        }
    }
}
