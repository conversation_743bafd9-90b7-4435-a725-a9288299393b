package com.zhixianghui.service.export.helper;

import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/9/1
 **/
@Component
public class CacheBiz {

    @Reference
    private MerchantSecretFacade merchantSecretFacade;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Cacheable(value = "redisCache", key = "targetClass + ':' + methodName + ':' + #dataName")
    public DataDictionary getByDateName(String dataName){
        return dataDictionaryFacade.getDataDictionaryByName(dataName);
    }

    @Cacheable(value = "dayCache", key = "methodName + ':' + #mchNo")
    public MerchantSecret getMerchantSecretByMchNo(String mchNo){
        return merchantSecretFacade.getByMchNo(mchNo);
    }
}
