package com.zhixianghui.service.export.listener;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONArray;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.employee.dto.WorkerRecordQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.JobWorkerStatusEnum;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.employee.service.JobWorkerRecordFacade;
import com.zhixianghui.facade.trade.service.ESignFacade;
import com.zhixianghui.facade.trade.vo.esign.ESign;
import com.zhixianghui.facade.trade.vo.esign.ESignItem;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月27日 11:24:00
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_UPLOAD_JOB_FILE, selectorExpression = MessageMsgDest.TAG_UPLOAD_JOB_FILE,
        consumeThreadMax = 5, consumerGroup = "jobWorkerFileConsumer")
public class JobWorkerFileListener extends BaseRocketMQListener<String> {

    @Reference
    private JobWorkerRecordFacade jobWorkerRecordFacade;
    @Reference
    private JobFacade jobFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Autowired
    private FastdfsClient fastdfsClient;
    @Reference(timeout = 50000)
    private ESignFacade eSignFacade;
    @Value("${file.baseurl}")
    private String fileUrl;

    private static final String RES_FILE = "RES_FILE";//成果验收
    private static final String DELIVER_FILE = "DELIVER_FILE";//交付物明细


    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String ids) {
        WorkerRecordQueryDto workerRecordQueryDto = new WorkerRecordQueryDto();
        workerRecordQueryDto.setIds(JSONArray.parseArray(ids, Long.class));
        List<JobWorkerRecord> jobWorkerRecords = jobWorkerRecordFacade.listBy(workerRecordQueryDto.toMap());
        ExecutorService executorService = Executors.newFixedThreadPool(3);
        jobWorkerRecords.forEach(worker->{
            log.info("[{}]准备上传文件，状态：{}",worker.getId(),worker.getJobStatus());
            if(worker.getJobStatus().equals(JobWorkerStatusEnum.COMPLETED.getCode())){
                log.info("[{}]开始上传文件",worker.getId());
                executorService.execute(()->{
                    this.doUpload(worker);
                });
            }
        });
    }

    private void doUpload(JobWorkerRecord worker) {
        //获取字典值(fastdf模板路径)
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName("JobWorkerFileEnum");
        List<DataDictionary.Item> dictionaryItems = JsonUtil.toList(dataDictionary.getDataInfo(), DataDictionary.Item.class);

        String resFileDesc = getTemplateFileUrl(dictionaryItems,RES_FILE);
        String deliverFileDesc = getTemplateFileUrl(dictionaryItems,DELIVER_FILE);

        Job job = jobFacade.getJobByJobId(worker.getJobId());
        {//上传成果验收单
            Map<String, Object> params = buildResMap(job, worker);
            String fileName = String.join("_", worker.getJobId(), worker.getWorkerName())+"_成果验收单";
            String fullPath = System.getProperty("user.dir") + File.separator + fileName + ReportConstant.DOCX_FILE_SUFFIX;
            try {
                File file = FileUtils.createFile(fullPath);
                InputStream inputStream = fastdfsClient.downloadFile(resFileDesc);
                org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
                XWPFTemplate.compile(file).render(params).writeToFile(fullPath);
                ESignItem eSignItem = new ESignItem();
                eSignItem.setKeywords("公章").setXOffset(120).setYOffset(0).setMchNo(job.getEmployerNo());
                byte[] bytes = FileUtils.file2byte(file);
                String sign = eSignFacade.sign(ESign.builder()
                        .logNo(job.getJobId())
                        .fileByte(bytes)
                        .flowName(fileName)
                        .fileName(fileName)
                        .eSignItems(Collections.singletonList(eSignItem))
                        .build()).getReturnFileUrl();
                log.info("[{}-{}]成果验收单：{}",worker.getId(),worker.getWorkerName(),sign);
                worker.setResultSignatureUrl(sign);
            } catch (Exception e) {
                log.error("上传雇员文件异常", e);
            } finally {
                //最后删除文件
                FileUtil.del(fullPath);
            }
        }
        //上传交付物
        {
            Map<String, Object> params = buildDevMap(job, worker);
            String fileName = String.join("_", worker.getJobId(), worker.getWorkerName())+"_交付物明细表";
            String fullPath = System.getProperty("user.dir") + File.separator + fileName + ReportConstant.DOCX_FILE_SUFFIX;
            try {
                File file = FileUtils.createFile(fullPath);
                InputStream inputStream = fastdfsClient.downloadFile(deliverFileDesc);
                org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
                Map<String, Object> map = buildPreDevMap(worker);
                XWPFTemplate.compile(file).render(params).writeToFile(fullPath);
                XWPFTemplate.compile(file).render(map).writeToFile(fullPath);
                ESignItem eSignItem = new ESignItem();
                eSignItem.setKeywords("公章").setXOffset(120).setYOffset(0).setMchNo(job.getEmployerNo());
                byte[] bytes = FileUtils.file2byte(file);
                String fastdfsUrl  = fastdfsClient.uploadFile(bytes, fileName + ReportConstant.DOCX_FILE_SUFFIX);
                String sign = eSignFacade.sign(ESign.builder()
                        .logNo(job.getJobId())
                        .fileUrl(fastdfsUrl)
                        .flowName(fileName)
                        .fileName(fileName)
                        .eSignItems(Collections.singletonList(eSignItem))
                        .build()).getReturnFileUrl();
                log.info("[{}-{}]个人交付物：{}",worker.getId(),worker.getWorkerName(),sign);
                worker.setDeliverSignatureUrl(sign);
            } catch (Exception e) {
                log.error("上传雇员文件异常", e);
            } finally {
                //最后删除文件
                FileUtil.del(fullPath);
            }
        }

        jobWorkerRecordFacade.update(worker);
    }

    private Map<String, Object> buildPreDevMap(JobWorkerRecord worker) {
        Map<String, Object> map = new HashMap<>();
        String attachment = worker.getAttachment();
        if(StringUtil.isNotEmpty(attachment)){
            List<String> list = JSONArray.parseArray(attachment, String.class);
            for (int i = 0; i < list.size(); i++) {
                map.put("images"+i, Pictures.ofUrl(fileUrl+list.get(i))
                        .size(100, 100).create());
            }
        }
        return map;
    }

    private Map<String, Object> buildDevMap(Job job, JobWorkerRecord worker) {
        Map<String, Object> map = new HashMap<>();
        LocalDateTime jobFinishTime = worker.getJobFinishTime();
        int year = jobFinishTime.getYear();
        int month = jobFinishTime.getMonth().getValue();
        int day = jobFinishTime.getDayOfMonth();
        map.put("year", year);
        map.put("month", month);
        map.put("day", day);
        map.put("employerName", job.getEmployerName());
        map.put("workerName", worker.getWorkerName());
        map.put("workerIdcard", worker.getWorkerIdcard());
        map.put("workerPhone", worker.getWorkerPhone());
        map.put("jobDescribe", job.getJobDescribe());
        String attachment = worker.getAttachment();
        if(StringUtil.isNotEmpty(attachment)){
            List<String> list = JSONArray.parseArray(attachment, String.class);
            StringBuilder sb=new StringBuilder();
            for (int i = 0; i < list.size(); i++) {
                sb.append(String.format("{{@images%d}}",i));
            }
            map.put("images",sb.toString());
        }
        return map;
    }


    private String getTemplateFileUrl(List<DataDictionary.Item> dictionaryItems, String flag) {
        DataDictionary.Item item = dictionaryItems.stream().filter(e -> e.getFlag().equals(flag)).findFirst().orElse(null);
        return Optional.ofNullable(item).orElse(new DataDictionary.Item()).getDesc();
    }

    private Map<String, Object> buildResMap(Job job, JobWorkerRecord worker) {
        Map<String, Object> map = new HashMap<>();
        LocalDateTime jobFinishTime = worker.getJobFinishTime();
        int year = jobFinishTime.getYear();
        int month = jobFinishTime.getMonth().getValue();
        int day = jobFinishTime.getDayOfMonth();
        map.put("year", year);
        map.put("month", month);
        map.put("day", day);
        map.put("employerName", job.getEmployerName());
        map.put("workerName", worker.getWorkerName());
        map.put("workerIdcard", worker.getWorkerIdcard());
        map.put("workerPhone", worker.getWorkerPhone());
        map.put("jobName", job.getJobName());
        map.put("jobDescribe", job.getJobDescribe());
        map.put("jobFinishTime", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(worker.getJobFinishTime()));
        return map;
    }
}
