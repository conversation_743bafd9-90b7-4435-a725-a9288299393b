package com.zhixianghui.service.export.core.vo;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName PushCallbackVo
 * @Description TODO
 * @Date 2023/2/23 17:46
 */
@Data
@JSONType(naming= PropertyNamingStrategy.SnakeCase)
public class PushCallbackVo{

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileExt;

    /**
     * 创建时间
     */
    private Date createTime;
}
