package com.zhixianghui.service.export.task;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.service.export.annotation.CheckBlack;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Vector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/26
 **/
@Slf4j
@Component
public class CertificateSftpTask {

    private final static String JOINPAY_TRANSFER_PREFIX = "1";
    private final static String JOINPAY_PAY_PREFIX = "2";

    /**
     * 汇聚商编
     */
    private List<String> joinpayMchNoList = new ArrayList<>();

    /**
     * 灵活用工平台商编
     */

    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    @Reference
    private RecordItemFacade recordItemFacade;

    @Autowired
    private RedisLock redisLock;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Autowired
    private JoinpayBillHandler joinpayBillHandler;


    public static void main(String[] args) {
//        ChannelSftp joinpayChannel = SftpUtil.connect("************", 22, "joinpay", "Eqipay@8@201811");
//        String fileName = "C:\\Users\\<USER>\\Desktop\\111111111.pdf";
//        File file = FileUtils.createFile(fileName);
//        SftpUtil.download("/home/<USER>/file/888100000004180/upload/trade_file/allocatePay/2021/01/25/2_郑婷1_888100000004180_AP102521012500188765.pdf", file, joinpayChannel);
//        CertificateSftpTask task = new CertificateSftpTask();
//        task.host = "************";
//        task.port = 22;
//        task.userName = "joinpay";
//        task.pwd = "Eqipay@8@201811";
//        task.dir = "/home/<USER>/file/";
//        List<String> list = new ArrayList<>();
//        list.add("888100000003636");
//        task.joinpayMchNoList = list;
//        task.uploadJoinpayCertificate(new HashMap<>());
        System.out.println("2020-01-01".replaceAll("-", "/"));
    }

    /**
     * 下载汇聚分账方代付电子凭证并上传
     * 将汇聚生成的凭证文件整理后上传回sftp服务器，目前灵活用工/汇聚智享汇共用一台服务器
     *
     * @param param 任务参数
     */
    public void uploadJoinpayCertificate(HashMap<String, Object> param) {
        log.info("凭证文件下载，上传任务参数：{}", JsonUtil.toString(param));
        if (ObjectUtil.isNull(param.get("payChannelNo"))) {
            log.error("凭证文件下载，所需参数为空，不执行");
            return;
        }
        String payChannelNo = String.valueOf(param.get("payChannelNo"));
        // 获取日期参数
        String tradeDayParam = (String) param.get("tradeDay");
        final String tradeDay = StringUtil.isEmpty(tradeDayParam)
                ? DateUtil.formatSlashDate(DateUtil.addDay(new Date(), -1))
                : tradeDayParam.replaceAll("-", "/");
        log.info("凭证文件下载，开始下载日期[{}]渠道[{}]的回单", tradeDay, payChannelNo);
        String clientId = null;
        // 初始化sftp连接
        ChannelSftp channelSftp = null;
        try {
            String lockName = ReportConstant.CERTIFICATE_TASK_LOCK_NAME + "_" + payChannelNo;
            // 获取添加任务锁
            clientId = redisLock.tryLockLong(lockName, 0, ReportConstant.TASK_LOCK_TIME_60);
            if (clientId == null) {
                log.info("凭证文件下载，{} 没有获取到凭证任务锁，可能有其他线程正在处理", Thread.currentThread().getName());
                return;
            }

            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            // 循环获取每个汇聚平台商户的凭证文件
            final Object channelMchNos = param.get("channelMchNos");
            if (channelMchNos != null && StringUtils.isNotBlank((String) channelMchNos)) {
                joinpayMchNoList = Lists.newArrayList(((String) channelMchNos).split(","));
            } else {
                Map<String, Object> queryParam = new HashMap<>();
                queryParam.put("status", 100);
                queryParam.put("payChannelNo", payChannelNo);
                List<MainstayChannelRelation> channelRelations = mainstayChannelRelationFacade.listBy(queryParam);
                log.info("凭证文件下载，渠道[{}]汇聚回单下单，查询供应商关系表的数据有[{}]条", payChannelNo, Optional.of(channelRelations.size()).orElse(0));
                joinpayMchNoList = channelRelations.stream().filter(it -> {
                    Date tradeDayDate = cn.hutool.core.date.DateUtil.parse(tradeDay, "yyyy/MM/dd");
                    Date dayStart = DateUtil.getDayStart(tradeDayDate);
                    Date dayEnd = DateUtil.getDayEnd(tradeDayDate);
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("processStatus", RecordItemStatusEnum.PAY_SUCCESS.getValue());
                    paramMap.put("payChannelNo", payChannelNo);
                    paramMap.put("mainstayNo", it.getMainstayNo());
                    paramMap.put("completeBeginDate", dayStart);
                    paramMap.put("completeEndDate", dayEnd);
                    Long count = recordItemFacade.countRecordItem(paramMap);
                    log.info("凭证文件下载，根据条件[{}]查询交易记录有[{}]条", paramMap, count);
                    return count != null && count > 0;
                }).map(MainstayChannelRelation::getChannelMchNo).collect(Collectors.toList());
            }
            log.info("凭证文件下载，渠道[{}]待解析回单商户：[{}]", payChannelNo, StringUtils.join(joinpayMchNoList, ","));
            for (String joinpayMchNo : joinpayMchNoList) {
                log.info("凭证文件下载，渠道[{}]开始解析供应商 {} 的回单", payChannelNo, joinpayMchNo);
                // 本地临时文件
                String tempPath = System.getProperty("user.dir") + File.separator + joinpayMchNo + File.separator;
                try {
                    FileUtils.creatDir(tempPath);
                    // 获取汇聚文件
                    String joinpaySftpFilePath = null;
                    if (payChannelNo.equals(ChannelNoEnum.JOINPAY.name())) {
                        // 分账下发回单在sftp的地址
                        joinpaySftpFilePath = dir + joinpayMchNo + "/upload/trade_file/allocatePay/" + tradeDay + "/";
                        Vector<ChannelSftp.LsEntry> vector = SftpUtil.listFiles(joinpaySftpFilePath, channelSftp);
                        if (CollectionUtils.isEmpty(vector)) {
                            log.info("凭证文件下载，文件夹不存在：{}", joinpaySftpFilePath);
                            continue;
                        }
                        processJoinpayVoucher(payChannelNo, channelSftp, tempPath, joinpaySftpFilePath, vector);
                    } else if (payChannelNo.equals(ChannelNoEnum.JOINPAY_JXH.name())) {
                        // 代付回单在sftp的地址
                        joinpaySftpFilePath = dir + joinpayMchNo + "/upload/trade_file/Pay/" + tradeDay + "/";
                        Vector<ChannelSftp.LsEntry> vector = SftpUtil.listFiles(joinpaySftpFilePath, channelSftp);
                        if (CollectionUtils.isEmpty(vector)) {
                            log.info("凭证文件下载，文件夹不存在：{}", joinpaySftpFilePath);
                            continue;
                        }
                        processJoinpayVoucher(payChannelNo, channelSftp, tempPath, joinpaySftpFilePath, vector);
                    }
                } catch (Exception e) {
                    log.error("凭证文件下载，{} 上传凭证文件出现异常：", joinpayMchNo, e);
                } finally {
                    FileUtils.deleteDir(new File(tempPath));
                }
                log.info("凭证文件下载，渠道[{}]完成解析供应商 {} 的回单", payChannelNo, joinpayMchNo);
            }
        } catch (Exception e) {
            log.error("凭证文件下载，日期[{}]渠道[{}]的回单任务出现异常：", tradeDay, payChannelNo, e);
        } finally {
            log.info("凭证文件下载，日期[{}]渠道[{}]的回单任务结束", tradeDay, payChannelNo);
            if (channelSftp != null) {
                SftpUtil.sftpClose(channelSftp);
            }
            if (clientId != null) {
                redisLock.unlockLong(clientId);
            }
        }
    }

    /***
     * 下载汇聚支付回单
     * @param channelSftp
     * @param tempPath
     * @param joinpaySftpFilePath
     * @param vector
     */
    private void processJoinpayVoucher(String channelNo, ChannelSftp channelSftp, String tempPath, String joinpaySftpFilePath,
                                       Vector<ChannelSftp.LsEntry> vector) {
        for (ChannelSftp.LsEntry lsEntry : vector) {
            String fileName = lsEntry.getFilename();
            if (StringUtil.isEmpty(fileName) || !fileName.endsWith(".pdf")) {
                continue;
            }
            // 解析汇聚文件名信息
            String[] names = fileName.split("_|\\.");
            String trxNo = names[names.length - 2];
            // 获取交易信息，组成新文件名
            RecordItem item = recordItemFacade.getByChannelTrxNo(trxNo);
            if (item == null) {
                if (names.length < 3) {
                    log.info("凭证文件下载，根据通道流水号查不到打款记录明细，该文件暂时忽略，渠道：{} 通道流水号：{} 文件名：{}", channelNo, trxNo, fileName);
                    continue;
                }
                //由于君享汇通道在2024-9-12以后更改了文件名，为了兼容两套文件名格式，只能多查一次处理
                String trxNo2 = names[names.length - 3];
                item = recordItemFacade.getByChannelTrxNo(trxNo2);
                if (item == null) {
                    log.info("凭证文件下载，根据通道流水号查不到打款记录明细，该文件暂时忽略，渠道：{} 通道流水号1：{} 通道流水号2：{} 文件名：{}", channelNo, trxNo, trxNo2, fileName);
                    continue;
                }
            }
            joinpayBillHandler.handleData(channelNo, item, names, tempPath, fileName, joinpaySftpFilePath, channelSftp, dir);
        }
    }

    @Component
    public class JoinpayBillHandler {
        @CheckBlack(tag = "bill:upload", target = "#item.employerNo")
        public void handleData(String channelNo, RecordItem item, String[] names, String tempPath, String fileName,
                               String joinpaySftpFilePath, ChannelSftp channelSftp, String dir) {
//            log.info("渠道[{}]JoinpayBillHandler类具体handleData接口接收到的参数，names[{}], dir[{}]", channelNo, Arrays.toString(names), dir);
            String newFileName = null;
            if (names[0].equals(JOINPAY_TRANSFER_PREFIX)) {
                newFileName = CertificateUtil.getTransferFileName(item.getMainstayName(), item.getEmployerNo(), item.getPlatTrxNo());
            } else if (names[0].equals(JOINPAY_PAY_PREFIX)) {
                newFileName = CertificateUtil.getPayFileName(item.getReceiveNameDecrypt(), item.getMainstayNo(), item.getPlatTrxNo());
            } else {
                // 代付的文件
                newFileName = CertificateUtil.getPayFileName(item.getReceiveNameDecrypt(), item.getMainstayNo(), item.getPlatTrxNo());
            }

            File saveFile = FileUtils.createFile(tempPath + fileName);
            String downloadFilePath = joinpaySftpFilePath + fileName;
            // 下载文件
            log.info("凭证文件下载，渠道[{}]平台订单号{}从原来地址[{}]下载。", channelNo, item.getPlatTrxNo(), downloadFilePath);
            SftpUtil.downloadNoClose(downloadFilePath, saveFile, channelSftp);
            // 上传文件
            String mainSftpDir = CertificateUtil.getFilePath(dir, item.getMainstayNo(), item.getPlatTrxNo());
            SftpUtil.uploadNoClose(mainSftpDir, saveFile, newFileName, channelSftp);
            log.info("凭证文件下载，渠道[{}]平台订单号{}凭证已上传至代征主体文件路径：{}", channelNo, item.getPlatTrxNo(), mainSftpDir + newFileName);

//            String employerSftpDir = CertificateUtil.getFilePath(dir, item.getEmployerNo(), item.getPlatTrxNo());
//            SftpUtil.uploadNoClose(employerSftpDir, saveFile, newFileName, channelSftp);
//            log.info("{} 凭证已上传至用工企业文件路径：{}", item.getPlatTrxNo(), employerSftpDir+newFileName);

        }
    }
}

