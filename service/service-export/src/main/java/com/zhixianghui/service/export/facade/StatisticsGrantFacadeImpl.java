package com.zhixianghui.service.export.facade;

import com.zhixianghui.facade.export.dto.GrantUserDto;
import com.zhixianghui.facade.export.entity.StatisticsGrant2user;
import com.zhixianghui.facade.export.service.StatisticsGrantFacade;
import com.zhixianghui.service.export.core.biz.StatisticsGrantBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 发放统计接口实现
 * @author: xingguang li
 * @created: 2020/11/02 11:39
 */
@Service
public class StatisticsGrantFacadeImpl implements StatisticsGrantFacade {

    @Autowired
    private StatisticsGrantBiz statisticsGrantBiz;

    @Override
    public void insert(StatisticsGrant2user grant2user) {
        statisticsGrantBiz.insert(grant2user);
    }

    @Override
    public GrantUserDto get(Date startTradeTime,
                            String platTrxNo,
                            String employerNo,
                            String userName,
                            String idCardNo,
                            String supplierNos,
                            BigDecimal orderAmount,
                            String supplierNo,
                            String receiveAccount,
                            String phone) {
        return statisticsGrantBiz.getByIdCardNoSupplier(startTradeTime,platTrxNo,employerNo,userName,idCardNo, supplierNos, orderAmount,supplierNo,receiveAccount,phone);
    }

    @Override
    public BigDecimal getTotalAmountByIdCard(String receiveIdCardNoMd5) {
        return statisticsGrantBiz.getTotalAmountByIdCard(receiveIdCardNoMd5);
    }
}
