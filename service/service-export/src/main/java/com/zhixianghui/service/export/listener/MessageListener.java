package com.zhixianghui.service.export.listener;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.service.export.core.biz.ExportRecordBiz;
import com.zhixianghui.service.export.helper.HelperBiz;
import com.zhixianghui.service.export.task.CertificateSftpTask;
import com.zhixianghui.service.export.task.ExportTask;
import com.zhixianghui.service.export.task.GrantUserTask;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
@Log4j2
public class MessageListener {

    @Autowired
    private ExportTask exportTask;
    @Autowired
    private GrantUserTask grantUserTask;
    @Autowired
    private CertificateSftpTask certificateSftpTask;

    @Component
    @RocketMQMessageListener(topic = "topic-export-task", consumeThreadMax = 1, consumerGroup = "exportTaskConsume")
    public class ExportMessageListener extends TaskRocketMQListener<HashMap<String, Object>> {


        @Override
        public void runTask(HashMap<String, Object> param) {

            Integer pageSize = (Integer) param.get("pageSize");
            exportTask.doAddTask(pageSize);
        }
    }

    @Component
    @RocketMQMessageListener(topic = "topic-grant2user-task", consumeThreadMax = 1, consumerGroup = "grant2userTaskConsume")
    public class GrantUserMessageListener extends TaskRocketMQListener<HashMap<String, Object>> {

        @Override
        public void runTask(HashMap<String, Object> param) {
            log.info("GrantUserMessageListener：{}", param);
            Integer pageSize = (Integer) param.get("pageSize");
            grantUserTask.doAddTask(pageSize);
        }
    }

    @Component
    @RocketMQMessageListener(topic = "topic-sftp-task", consumeThreadMax = 1, consumerGroup = "certificateSftpTaskConsume")
    public class CertificateSftpTaskMessageListener extends TaskRocketMQListener<HashMap<String, Object>> {

        @Override
        public void runTask(HashMap<String, Object> param) {
            log.info("CertificateSftpTaskMessageListener：{}", JsonUtil.toString(param));
            CompletableFuture.runAsync(()->{
                param.put("payChannelNo", ChannelNoEnum.JOINPAY.name());
                certificateSftpTask.uploadJoinpayCertificate(param);
            });
        }
    }

    @Component
    @RocketMQMessageListener(topic = "topic-sftp-jxh-task", consumeThreadMax = 1, consumerGroup = "certificateSftpTaskConsumeForJxh")
    public class CertificateSftpTaskJxhMessageListener extends TaskRocketMQListener<HashMap<String, Object>> {
        @Override
        public void runTask(HashMap<String, Object> param) {
            log.info("CertificateSftpTaskJxhMessageListener：{}", JsonUtil.toString(param));
            CompletableFuture.runAsync(()->{
                param.put("payChannelNo", ChannelNoEnum.JOINPAY_JXH.name());
                certificateSftpTask.uploadJoinpayCertificate(param);
            });
        }
    }


    @Component
    @RocketMQMessageListener(topic = "topic-month-sales-fee-task", consumeThreadMax = 1, consumerGroup = "monthSalesFeeConsumer")
    public class MothSalesFeeTaskListener extends TaskRocketMQListener<JSONObject> {

        @Autowired
        private HelperBiz helperBiz;
        @Autowired
        private ExportRecordBiz exportRecordBiz;
        @Reference
        private DataDictionaryFacade dataDictionaryFacade;
        @Value("${email.export.sales}")
        private String email;

        @Override
        public void runTask(JSONObject jsonParam) {
            String fileNo = helperBiz.genFileNo();

            Map<String, Object> paramMap = new HashMap<>();
            if (StringUtils.isBlank(jsonParam.getString("tradeMonth"))) {
                paramMap.put("tradeTimeBegin", DateUtil.beginOfMonth(DateUtil.lastMonth().toJdkDate()));
                paramMap.put("tradeTimeEnd", DateUtil.endOfMonth(DateUtil.lastMonth().toJdkDate()));
            }else {
                final DateTime tradeMonth = DateUtil.parse(jsonParam.getString("tradeMonth"), "yyyy-MM");

                paramMap.put("tradeTimeBegin", DateUtil.beginOfMonth(tradeMonth));
                paramMap.put("tradeTimeEnd", DateUtil.endOfMonth(tradeMonth));
            }

            ExportRecord record = ExportRecord.newDefaultInstance();
            record.setFileNo(fileNo);
            record.setOperatorLoginName("admin");
            record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
            record.setFileName(ReportTypeEnum.SALES_FEE_ORDER.getFileName());
            record.setReportType(ReportTypeEnum.SALES_FEE_ORDER.getValue());
            record.setParamJson(JsonUtil.toString(paramMap));
            record.setEmailNotify(1);
            record.setEmail(email);
            record.setEmailSubject("汇聚智享 "+DateUtil.format((Date) paramMap.get("tradeTimeBegin"),"yyyy年MM月")+" 销售计费订单数据");
            record.setEmailContent(DateUtil.format((Date) paramMap.get("tradeTimeBegin"),"yyyy年MM月") +"的销售计费订单已为您导出，请到以下链接下载：\n{}");

            DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.SALES_FEE_ORDER.getDataName());
            dataDictionary.getItemList().forEach(x -> {
                ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
                info.setDataName(x.getFlag());
                info.setFieldCode(x.getCode());
                info.setFieldDesc(x.getDesc());
                record.getFieldInfoList().add(info);
            });
            exportRecordBiz.insert(record);
        }
    }

}
