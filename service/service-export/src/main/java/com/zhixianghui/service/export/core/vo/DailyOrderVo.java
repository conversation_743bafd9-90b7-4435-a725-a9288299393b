package com.zhixianghui.service.export.core.vo;

import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvBindByPosition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName DailOrderVo
 * @Description TODO
 * @Date 2023/2/23 14:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyOrderVo implements Serializable {

    @CsvBindByPosition(position = 0)
    @CsvBindByName(column = "平台批次号")
    private String platBatchNo;

    @CsvBindByPosition(position = 1)
    @CsvBindByName(column = "创建时间")
    private String createTime;

    @CsvBindByPosition(position = 2)
    @CsvBindByName(column = "完成时间")
    private String completeTime;

    @CsvBindByPosition(position = 3)
    @CsvBindByName(column = "商户订单号")
    private String mchOrderNo;

    @CsvBindByPosition(position = 4)
    @CsvBindByName(column = "平台流水号")
    private String platTrxNo;

    @CsvBindByPosition(position = 5)
    @CsvBindByName(column = "用工企业")
    private String employerName;

    @CsvBindByPosition(position = 6)
    @CsvBindByName(column = "代征主体")
    private String mainstayName;

    @CsvBindByPosition(position = 7)
    @CsvBindByName(column = "发放方式")
    private String channelTypeName;

    @CsvBindByPosition(position = 8)
    @CsvBindByName(column = "支付通道")
    private String channelName;

    @CsvBindByPosition(position = 9)
    @CsvBindByName(column = "收款账户")
    private String receiveAccountNoDecrypt;

    @CsvBindByPosition(position = 10)
    @CsvBindByName(column = "收款银行")
    private String bankName;

    @CsvBindByPosition(position = 11)
    @CsvBindByName(column = "姓名")
    private String receiveNameDecrypt;

    @CsvBindByPosition(position = 12)
    @CsvBindByName(column = "身份证")
    private String receiveIdCardNoDecrypt;

    @CsvBindByPosition(position = 13)
    @CsvBindByName(column = "手机号")
    private String receivePhoneNoDecrypt;

    @CsvBindByPosition(position = 14)
    @CsvBindByName(column = "订单金额")
    private BigDecimal orderItemAmount;

    @CsvBindByPosition(position = 15)
    @CsvBindByName(column = "任务金额")
    private BigDecimal orderItemTaskAmount;

    @CsvBindByPosition(position = 16)
    @CsvBindByName(column = "实发金额")
    private BigDecimal orderItemNetAmount;

    @CsvBindByPosition(position = 17)
    @CsvBindByName(column = "服务费")
    private BigDecimal orderItemFee;

    @CsvBindByPosition(position = 18)
    @CsvBindByName(column = "个税")
    private BigDecimal orderItemTaxAmount;

    @CsvBindByPosition(position = 19)
    @CsvBindByName(column = "备注")
    private String remark;

    @CsvBindByPosition(position = 20)
    @CsvBindByName(column = "失败原因")
    private String errorDesc;

    @CsvBindByPosition(position = 21)
    @CsvBindByName(column = "订单状态")
    private String orderItemStatus;

    @CsvBindByPosition(position = 22)
    @CsvBindByName(column = "商户备忘录")
    private String memo;
}
