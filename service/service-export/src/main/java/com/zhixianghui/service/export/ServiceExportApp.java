package com.zhixianghui.service.export;

import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * 报表等文件导出服务
 * <AUTHOR>
 * @date 2020/8/4
 **/
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableAsync
@EnableCaching
public class ServiceExportApp {
    public static void main(String[] args) {
        System.setProperty("druid.mysql.usePingMethod","false");
        new SpringApplicationBuilder(ServiceExportApp.class).web(WebApplicationType.NONE).run(args);
    }
}
