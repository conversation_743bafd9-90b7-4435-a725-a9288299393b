package com.zhixianghui.service.export.core.biz;

import cn.hutool.core.io.FileUtil;
import com.deepoove.poi.XWPFTemplate;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.flow.entity.WorkOrderExt;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.enums.WorkTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.flow.vo.work.SignComponent;
import com.zhixianghui.facade.flow.vo.work.TextComponent;
import com.zhixianghui.facade.flow.vo.work.UploadComponent;
import com.zhixianghui.facade.flow.vo.work.WorkForm;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.service.MerchantSalerFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.vo.ServiceConfirmVo;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @ClassName ServiceConfirmBiz
 * @Description TODO
 * @Date 2022/3/22 11:38
 */
@Slf4j
@Service
public class ServiceConfirmExportBiz {

    @Autowired
    private FastdfsClient fastdfsClient;

    @Reference
    private SequenceFacade sequenceFacade;

    @Reference
    private RecordItemFacade recordItemFacade;

    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private MerchantSalerFacade merchantSalerFacade;

    private static final String[] TABLE_HEADER = {"收款人姓名","证件号","收款账号","交易金额","手机号"};


    public String templateHandle(ServiceConfirmVo serviceConfirmVo,Map<String,Object> paramMap,File file) throws IOException {
        Map<String,Object> datas = BeanUtil.toMap(serviceConfirmVo);
        //设置表头
        //注释掉，暂时不需要做这个

//        RowRenderData header = Rows.of(TABLE_HEADER).center().rowHeight(2.5f).create();
//        Tables.TableBuilder tableBuilder = Tables.ofAutoWidth();
//        TableRenderData tableRenderData = tableBuilder.create();
//        tableRenderData.addRow(header);
//        //查询打款流水
//        List<RecordItem> tableVoList;
//        int offset = 0;
//        do{
//            tableVoList = recordItemFacade.listByOffset(paramMap,offset, ReportConstant.PAGE_SIZE);
//            tableVoList.forEach(x->{
//                RowRenderData rowRenderData = Rows.of(x.getReceiveNameDecrypt(),x.getReceiveIdCardNoDecrypt(),x.getReceiveAccountNoDecrypt(),
//                        x.getOrderNetAmount().toString(),x.getReceivePhoneNoDecrypt()).center().create();
//                tableRenderData.addRow(rowRenderData);
//            });
//            offset += tableVoList.size();
//        }while (tableVoList.size() >= ReportConstant.PAGE_SIZE);
//        datas.put("table",tableRenderData);

        String fileName = String.join("-",serviceConfirmVo.getEmployerNo(),serviceConfirmVo.getMainstayNo(),serviceConfirmVo.getWorkCategoryCode());
        String fullPath = System.getProperty("user.dir") + File.separator + fileName + ReportConstant.DOCX_FILE_SUFFIX;
        XWPFTemplate.compile(file).render(datas).writeToFile(fullPath);
        //上传到fastdfs上
        String fastdfsPath;
        try {
            //生成工单编号
            fastdfsPath = fastdfsClient.uploadFile(fullPath,fileName + ReportConstant.DOCX_FILE_SUFFIX);
            log.info("上到服务履约单到文件系统，商户编号：[{}]，供应商编号：[{}]，自由职业者类别：[{}]，服务履约单路径：[{}]",serviceConfirmVo.getEmployerNo(),serviceConfirmVo.getMainstayNo(),serviceConfirmVo.getWorkCategoryCode(),fastdfsPath);
            return fastdfsPath;
        }finally {
            //最后删除文件
            FileUtil.del(fullPath);
        }
    }

    private void generateForm(String fastdfsPath, ServiceConfirmVo serviceConfirmVo) {
        Date date = new Date();
        //构建表单参数
        WorkForm workForm = buildForm(fastdfsPath);
        //生成工单编号
        String formNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.FORM_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.FORM_NO_SEQ.getKey(),SequenceBizKeyEnum.FORM_NO_SEQ.getWidth());
        //构建工单
        ProcessVo processVo = new ProcessVo();
        processVo.setWorkType(WorkTypeEnum.WORK_FLOW.getValue());
        processVo.setBusinessKey(formNo);
        processVo.setExtInfo(JsonUtil.toString(workForm));
        processVo.setFlowTopicType(FlowTypeEnum.COMMON_WORK_ORDER.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-",FlowTypeEnum.COMMON_WORK_ORDER.getDesc(),
                serviceConfirmVo.getDate(),serviceConfirmVo.getEmployerName(),serviceConfirmVo.getMainstayName(),serviceConfirmVo.getWorkCategoryName()));
        processVo.setProcessDefinitionKey(FlowTypeEnum.COMMON_WORK_ORDER.name());

        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(1L);
        flowUserVo.setUserName("系统发起");
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());

        Map<String,Object> condition = new HashMap<>();
        //设置流程参数
        condition.put("mchNo",serviceConfirmVo.getEmployerNo());
        condition.put("mainstayNo",serviceConfirmVo.getMainstayNo());
        //扩展字段
        WorkOrderExt workOrderExt = new WorkOrderExt();
        BeanUtils.copyProperties(serviceConfirmVo,workOrderExt);
        workOrderExt.setCreateDate(date);
        workOrderExt.setWorkOrderNo(formNo);
        workOrderExt.setEndDate(DateUtil.getDayEnd(DateUtil.addMonth(date,6)));
        processVo.setExtObj(workOrderExt);
        //添加抄送人
        FlowUserVo carbonCopyUser = new FlowUserVo();
        carbonCopyUser.setPlatform(PlatformSource.OPERATION.getValue());
        carbonCopyUser.setUserName(workOrderExt.getSalerName());
        carbonCopyUser.setUserId(workOrderExt.getSalerId());
        processVo.getCarbonCopyList().add(carbonCopyUser);

        flowFacade.startProcessByProcessDefinitionKey(processVo,flowUserVo,null,condition);
    }

    /**
     * 构建表单
     * @param fastdfsPath
     * @return
     */
    private WorkForm buildForm(String fastdfsPath) {
        TextComponent tc1 = new TextComponent("步骤1","请点击 “线下签约” 下载查看 《服务履约单》，并在截止时间内完成确认");
        TextComponent tc2 = new TextComponent("步骤2","选择签署方式。线下签约需要自行打印并盖章上传；线上签约可自动完成签约");
        TextComponent tc3 = new TextComponent("步骤3","点击提交，即可完成签约");
        SignComponent signComponent = new SignComponent();
        signComponent.setTitle("签约方式");
        signComponent.setTemplateFileUrl(fastdfsPath);
        signComponent.setValue("1");
        signComponent.setTips("E签宝正在调用贵司数字证书，点击提交后即可自动完成签约");
        signComponent.setUploadText("提交确认单");
        signComponent.setField(new ArrayList<String>(){{add("field1");add("field2");}});
        signComponent.setTemplateFileName("服务履约单");

//        UploadComponent uploadComponent = new UploadComponent("请下载模板并盖章后上传","",
//                fastdfsPath,"field1",null);
        WorkForm workForm = new WorkForm("服务履约确认单","",tc1,tc2,tc3,signComponent);
        return workForm;
    }

    public void serviceConfirmProcess(ServiceConfirmVo serviceConfirmVo, File file, CountDownLatch countDownLatch) throws IOException {
        //查出的recordItem只是对商户编号、供应商编号、自由职业者服务类型分组，需要具体查出对应的数据
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",serviceConfirmVo.getEmployerNo());
        paramMap.put("mainstayNo",serviceConfirmVo.getMainstayNo());
        paramMap.put("workCategoryCode",serviceConfirmVo.getWorkCategoryCode());
        paramMap.put("completeBeginDate",serviceConfirmVo.getCompleteBeginDate());
        paramMap.put("completeEndDate", serviceConfirmVo.getCompleteEndDate());
        try {
            buildVo(serviceConfirmVo);
            String fastdfsPath = templateHandle(serviceConfirmVo,paramMap,file);
            generateForm(fastdfsPath,serviceConfirmVo);
        }catch (Exception e){
            log.error("定时生成服务履约单异常，用工企业编号：[{}]，代征主体编号：[{}]，自由职业者类型：[{}]",serviceConfirmVo.getEmployerNo(),serviceConfirmVo.getMainstayNo(),serviceConfirmVo.getWorkCategoryCode());
            throw e;
        } finally{
            countDownLatch.countDown();
        }
    }

    private void buildVo(ServiceConfirmVo serviceConfirmVo) {
        //查询商户商户岗位信息
        MerchantEmployerPosition merchantEmployerPosition = merchantEmployerPositionFacade.getByMchNoAndWorkCategoryCode(
                serviceConfirmVo.getEmployerNo(),serviceConfirmVo.getWorkCategoryCode());

        //获取一下商户名称和供应商名称，避免名称修改过
        Merchant merchant = merchantQueryFacade.getByMchNo(serviceConfirmVo.getEmployerNo());
        Merchant mainstay = merchantQueryFacade.getByMchNo(serviceConfirmVo.getMainstayNo());

        //获取销售信息
        MerchantSaler merchantSaler = merchantSalerFacade.getByMchNo(merchant.getMchNo());
        serviceConfirmVo.setMainstayName(mainstay.getMchName());
        serviceConfirmVo.setEmployerName(merchant.getMchName());
        serviceConfirmVo.setAgentNo(merchant.getAgentNo());
        serviceConfirmVo.setAgentName(merchant.getAgentName());
        serviceConfirmVo.setSalerId(merchantSaler.getSalerId());
        serviceConfirmVo.setSalerName(merchantSaler.getSalerName());
        if (merchantEmployerPosition != null && StringUtils.isNotBlank(merchantEmployerPosition.getChargeRuleDesc())){
            serviceConfirmVo.setChargeRuleDesc(merchantEmployerPosition.getChargeRuleDesc());
        }else{
            serviceConfirmVo.setChargeRuleDesc("");
        }

        if (merchantEmployerPosition != null && StringUtils.isNotBlank(merchantEmployerPosition.getServiceDesc())){
            serviceConfirmVo.setServiceDesc(merchantEmployerPosition.getServiceDesc());
        }else{
            serviceConfirmVo.setServiceDesc("");
        }

    }
}
