package com.zhixianghui.service.export.export;

import cn.hutool.core.io.FileUtil;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.enums.export.ExportStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.service.export.core.dao.ReportRecordDao;
import com.zhixianghui.service.export.export.data.ZipDataBiz;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.File;
import java.util.*;

/**
 * <AUTHOR>
 * @description zip导出
 * @date 2020-10-22 10:10
 **/
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ZipExportBiz {

    private final ZipDataBiz zipDataBiz;
    private final ReportRecordDao recordDao;
    private final FastdfsClient fastdfsClient;

    @Value("${export.zip.size}")
    private int exportSize = 1000;

    public void export(ExportRecord record) {
        log.info("[{}-{}]开始导出文件", record.getFileNo(), record.getOperatorLoginName());
        if (!Objects.equals(record.getExportStatus(), ExportStatusEnum.CREATE.getValue())) {
            return;
        }
        // 将导出状态更新为导出中，避免其他线程同时操作,更新条数不为1时，会抛出异常
        record.setExportStatus(ExportStatusEnum.EXPORTING.getValue());
        record.setUpdateTime(new Date());
        recordDao.update(record);

        // 版本号加1
        record.setVersion(record.getVersion() + 1);
        String tempPath = genRandomPath();

        try {
            Map<String, Object> paramMap = JsonUtil.toBean(record.getParamJson(), HashMap.class);

            zipDataBiz.createFiles(record.getReportType(), record.getFileNo(), paramMap, tempPath);
            final List<File> listFiles = (List<File>) org.apache.commons.io.FileUtils.listFiles(new File(tempPath), null, true);
            final List<List<File>> partitions = Lists.partition(listFiles, exportSize);
//            zipFile = ZipUtil.zipFileKeepConstruct(tempPath);
//            fileList = ZipUtil.createSplitZipFileFromFolder(tempPath);
            List<String> filePaths = new ArrayList<>();
            int i = 0;
            for (List<File> partition : partitions) {
                File zipFile = ZipUtil.zipFileKeepConstructPartition(tempPath,i,partition);
                String fileUrl = fastdfsClient.uploadFile(zipFile.getAbsolutePath(), zipFile.getName());
                log.info("[{}-{}]导出文件路径 fileUrl：{}", record.getFileNo(), record.getOperatorLoginName(), fileUrl);
                filePaths.add(fileUrl);
                i++;
                FileUtil.del(zipFile);
                zipFile = null;
            }

            String fileUrl = StringUtils.join(filePaths, ",");
            log.info("文件列表：{}",fileUrl);
            Assert.hasText(fileUrl, "文件上传失败");
            // 更新报表记录导出状态
            record.setExportStatus(ExportStatusEnum.SUCCESS.getValue());
            record.setFileUrl(fileUrl);
        } catch (BizException e) {
            log.info("[{}-{}]导出文件失败：", record.getFileNo(), record.getOperatorLoginName(), e);
            record.setExportStatus(ExportStatusEnum.FAIL.getValue());
            record.setErrDesc(e.getErrMsg());
        } catch (Exception e) {
            log.info("[{}-{}]导出文件失败：", record.getFileNo(), record.getOperatorLoginName(), e);
            record.setExportStatus(ExportStatusEnum.FAIL.getValue());
            record.setErrDesc(StringUtil.subLeft(e.getMessage(), 200));
        } finally {
            // 删除文件
            FileUtils.deleteDir(new File(tempPath));
        }
        record.setUpdateTime(new Date());
        recordDao.update(record);
    }

    private String genRandomPath() {
        try {
            String filePath = System.getProperty("user.dir")
                    + File.separator
                    + ReportConstant.EXPORT_FILE_PATH
                    + File.separator
                    + RandomUtil.get16LenStr();
            FileUtils.creatDir(filePath);
            return filePath;
        } catch (Exception e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("创建文件失败", e);
        }

    }
}
