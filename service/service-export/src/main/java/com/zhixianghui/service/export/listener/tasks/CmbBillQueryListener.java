package com.zhixianghui.service.export.listener.tasks;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.export.dto.TimeRangeDto;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.export.listener.TaskRocketMQListener;
import com.zhixianghui.service.export.util.TaskTimeHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_BILL_TASK,consumeThreadMax = 1,consumerGroup = "cmbBillConsumer")
@Slf4j
public class CmbBillQueryListener extends TaskRocketMQListener<JSONObject> {
    @Reference
    private CmbFacade cmbFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Override
    public void runTask(JSONObject jsonParam) {
        log.info("[招行电子回单]-回单申请开始，请求参数:\n{}",jsonParam==null?null:jsonParam.toStringPretty());
        final TimeRangeDto timeRange = TaskTimeHelper.getTimeRange(jsonParam);

        final List<Map<String, Object>> cmbainstays = mainstayChannelRelationFacade.getCmbainstays();
        for (Map<String, Object> cmbainstay : cmbainstays) {
            String mainstayNo = (String) cmbainstay.get("mainstayNo");
            String acctNo = (String) cmbainstay.get("channelMchNo");
            try {
                String begidx = "0";
                log.info("[招行电子回单]-供应商[{}-{}]申请回单-开始", mainstayNo, acctNo);
                do {
                    final com.alibaba.fastjson.JSONObject applyResult = cmbFacade.reconfileApply(acctNo, DateUtil.formatDate(timeRange.getStartTime()), DateUtil.formatDate(timeRange.getEndTime()), begidx);
                    if (applyResult != null) {
                        final String printid = applyResult.getString("printid");
                        if (printid != null) {
                            begidx = applyResult.getString("begidx");

                            com.alibaba.fastjson.JSONObject notifyContent = new com.alibaba.fastjson.JSONObject();
                            notifyContent.put("acctountNo", acctNo);
                            notifyContent.put("printid", printid);
                            notifyContent.put("mainstayNo", mainstayNo);

                            final String mqDelayLevel = dataDictionaryFacade.getSystemConfig("MQ_DELAY_LEVEL");
                            notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_BILL_SAVE, NotifyTypeEnum.CMB_BILL.getValue(), MessageMsgDest.TAG_CMB_BILL_SAVE, notifyContent.toJSONString(), MsgDelayLevelEnum.valueOf(mqDelayLevel).getValue());
                        }else {
                            begidx = null;
                        }
                    } else {
                        begidx = null;
                    }
                } while (begidx != null);
                log.info("[招行电子回单]-供应商[{}-{}]申请回单-完成", mainstayNo, acctNo);
            } catch (Exception e) {
                log.error("[招行电子回单]-供应商[{}-{}]申请回单-出现异常", mainstayNo, acctNo, e);
            }
        }

        log.info("[招行电子回单]-回单申请完成");
    }

    @Override
    public void validateJsonParam(JSONObject jsonParam) {

    }
}
