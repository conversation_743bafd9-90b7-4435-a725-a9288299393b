package com.zhixianghui.service.export.export;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.export.ExportStatusEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.service.export.helper.CacheBiz;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.service.export.core.dao.ReportRecordDao;
import com.zhixianghui.service.export.export.data.ExcelDataBiz;
import com.zhixianghui.service.export.helper.HelperBiz;
import com.zhixianghui.starter.comp.component.EmailSender;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/8/28
 **/
@Component
@Slf4j
public class ExcelExportBiz {

    private static final ThreadLocal<DateFormat> df = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    @Autowired
    private ReportRecordDao recordDao;
    @Autowired
    private ExcelDataBiz excelDataBiz;
    @Autowired
    private FastdfsClient fastdfsClient;
    @Autowired
    private CacheBiz cacheBiz;
    @Autowired
    private HelperBiz helperBiz;
    @Reference
    private EmailFacade emailFacade;

    @Value("${file.baseurl}")
    private String fileBaseUrl;

    public void export(ExportRecord record) {
        log.info("[{}-{}-{}]开始导出文件", record.getFileNo(), record.getOperatorLoginName(), Thread.currentThread().getName());
        if(!Objects.equals(record.getExportStatus(), ExportStatusEnum.CREATE.getValue())){
            return;
        }

        // 将导出状态更新为导出中，避免其他线程同时操作,更新条数不为1时，会抛出异常
        record.setExportStatus(ExportStatusEnum.EXPORTING.getValue());
        record.setUpdateTime(new Date());
        recordDao.update(record);

        // 版本号加1
        record.setVersion(record.getVersion() + 1);

        File file = null;
        try {
            // 校验所导出字段集是否为数据字典中配置的子集，避免导出其他敏感字段
            helperBiz.checkExportField(record);

            // 生成本地文件
            file = genRandomFile();

            //自动换行
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            contentWriteCellStyle.setWrapped(true);
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(null, contentWriteCellStyle);
            // 创建writerBuilder对象，自动调整列宽
            ExcelWriter excelWriter = EasyExcel.write(new FileOutputStream(file))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .autoCloseStream(Boolean.TRUE)
                    .build();

            // 获取表头内容
            List<String> titleList = record.getFieldInfoList().stream()
                    .map(x -> x.getFieldDesc())
                    .collect(Collectors.toList());
            List<List<String>> titleRowList = new ArrayList<>();
            titleRowList.add(titleList);

            // 生成导出内容
            writeRowList(excelWriter, titleRowList, record);

            // 先关闭文件流，再上传至文件系统，否则会出现文件损坏
            excelWriter.finish();
            String fileUrl = fastdfsClient.uploadFile(file.getAbsolutePath(), file.getName());
            log.info("[{}-{}-{}]导出文件路径 fileUrl：{}", record.getFileNo(), record.getOperatorLoginName(), Thread.currentThread().getName(), fileUrl);
            Assert.hasText(fileUrl, "文件上传失败");

            // 更新报表记录导出状态
            record.setExportStatus(ExportStatusEnum.SUCCESS.getValue());
            record.setFileUrl(fileUrl);
        } catch (BizException e) {
            log.info("[{}-{}-{}]导出文件失败：", record.getFileNo(), record.getOperatorLoginName(), Thread.currentThread().getName(), e);
            record.setExportStatus(ExportStatusEnum.FAIL.getValue());
            record.setErrDesc(e.getErrMsg());
        } catch (Exception e) {
            log.info("[{}-{}-{}]导出文件失败：", record.getFileNo(), record.getOperatorLoginName(), Thread.currentThread().getName(), e);
            record.setExportStatus(ExportStatusEnum.FAIL.getValue());
            record.setErrDesc(StringUtil.subLeft(e.getMessage(), 200));
        } finally {
            // 删除文件
            if (!Objects.isNull(file)) {
                FileUtils.deleteDir(file);
            }
        }
        record.setUpdateTime(new Date());
        recordDao.update(record);

        if (record.getEmailNotify() != null && record.getEmailNotify() == 1) {
            if (record.getEmail() != null && !record.getEmail().isEmpty()) {
                String[] emails = record.getEmail().split(",");
                EmailParamDto emailParamDto = new EmailParamDto();
                emailParamDto.setSubject(record.getEmailSubject());
                emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
                emailParamDto.setTo(emails[0]);
                //配置多个则除第一个之外的作为抄送发送
                if (emails.length > 1) {
                    emailParamDto.setCc(ArrayUtil.sub(emails,1, emails.length));
                }
                emailParamDto.setHtmlFormat(false);
                emailParamDto.setContent(record.getEmailContent().replace("{}", this.fileBaseUrl + record.getFileUrl()));
                emailFacade.sendAsync(emailParamDto);
            }
        }
    }

    private List<List<Object>> writeRowList(ExcelWriter excelWriter, List<List<String>> titleRowList, ExportRecord record) {
        Map<String, Object> paramMap = JsonUtil.toBean(record.getParamJson(), HashMap.class);
        // 查询参数处理
        excelDataBiz.handlerParamMap(record.getReportType(),paramMap);

        PageParam pageParam = PageParam.newInstance(1, ReportConstant.PAGE_SIZE, "ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        List<Map<String, Object>> dataList = excelDataBiz.listRecord(record.getReportType(), paramMap, pageParam);

        List<List<Object>> rowList = new ArrayList<>();

        long recordNum  = 0L;
        int sheetNum = 0;

        WriteSheet writeSheet = EasyExcel.writerSheet(sheetNum,"导出数据").build();
        excelWriter.write(titleRowList, writeSheet);

        while (CollectionUtils.isNotEmpty(dataList)) {
            // sheet有最大数量限制，超过则使用新的sheet
            recordNum = recordNum + dataList.size();
            if(recordNum >= ReportConstant.MAX_SHEET_RECORD_NUM){
                sheetNum ++;
                writeSheet = EasyExcel.writerSheet(sheetNum,"导出数据"+sheetNum).build();
                excelWriter.write(titleRowList, writeSheet);
            }

            // 报表内容写入
            List<List<Object>> tempList = new ArrayList<>();
            for (Map<String, Object> dataMap : dataList) {
                List<Object> row = new ArrayList<>();
//                int point = 0;
//                StringBuffer sb = new StringBuffer();
//                for (int i = 0; i < record.getFieldInfoList().size(); i++) {
//                    point = i;
//                    point = getCellValue(dataMap,record.getFieldInfoList().get(i),record.getFieldInfoList(),point,sb);
//                    if (point == i){
//                        row.add(sb.toString());
//                    }
//                }
                for (int i = 0; i < record.getFieldInfoList().size(); i++) {
                    String cellValue;
                    ExportRecord.FieldInfo fieldInfo = record.getFieldInfoList().get(i);
                    // 获取单元格内容值
                    // 属性值
                    Object dataValue = dataMap.get(fieldInfo.getFieldCode());
                    if (dataValue instanceof ArrayList) {
                        DataDictionary dataDictionary = cacheBiz.getByDateName(fieldInfo.getDataName());
                        List<HashMap> dictionaryItems = JsonUtil.toList(dataDictionary.getDataInfo(), HashMap.class);
                        StringBuffer sb = new StringBuffer();
                        sb = getListValue((List) dataValue,sb,dictionaryItems);
                        cellValue = sb.toString();

                    }else{
                        cellValue = getCellValue(dataMap, fieldInfo);
                    }
                    row.add(cellValue);
                }
                tempList.add(row);
            }
            // 写入文件
            excelWriter.write(tempList, writeSheet);

            // 当前页所获取到的数据少于指定数量时，退出循环
            if (CollectionUtils.size(dataList) < ReportConstant.PAGE_SIZE) {
                break;
            }

            if (!record.getDeepPage()){
                // 获取当前的最小id作为下一次查询的最大id，每次只查第一页，避免深度分页的情况出现
                Object maxId = dataList.get(dataList.size()-1).get("id");
                if(ValidateUtil.isEmpty(maxId)){
                    break;
                }
                paramMap.put("maxId", maxId);
            }else{
                int currentPage = pageParam.getPageCurrent() + 1;
                pageParam.setPageCurrent(currentPage);
            }
            dataList = excelDataBiz.listRecord(record.getReportType(), paramMap, pageParam);
        }
        return rowList;
    }

    private StringBuffer getListValue(List<Object> dataValue,StringBuffer sb,List<HashMap> hashMapList) {
        List<Map<String, Object>> list = dataValue.stream().map(BeanUtil::toMap).collect(Collectors.toList());
        for (Map<String, Object> dataMap : list) {
            for (Map<String,Object> map : hashMapList) {
                ExportRecord.FieldInfo fieldInfo = new ExportRecord.FieldInfo();
                fieldInfo.setDataName(map.get("flag").toString());
                fieldInfo.setFieldCode(map.get("code").toString());
                fieldInfo.setFieldDesc(map.get("desc").toString());
                sb.append(map.get("desc").toString() + ":" + getCellValue(dataMap,fieldInfo));
                sb.append("  ");
            }
            sb.append("\n");
        }
//        return max;
        return sb;
    }

    private String getCellValue(Map<String, Object> dataMap, ExportRecord.FieldInfo fieldInfo) {
        // 属性值
        Object dataValue = dataMap.get(fieldInfo.getFieldCode());
        // 日期格式化
        if(dataValue instanceof Date){
            dataValue = df.get().format(dataValue);
        }

        String cellValue = dataValue == null ? "" : dataValue.toString();
        // 枚举值需做转换
        if (StringUtil.isNotEmpty(fieldInfo.getDataName()) && dataValue != null){
            DataDictionary dataDictionary = cacheBiz.getByDateName(fieldInfo.getDataName());
            List<HashMap> dictionaryItems = JsonUtil.toList(dataDictionary.getDataInfo(), HashMap.class);
            for (Map itemMap : dictionaryItems) {
                if (Objects.equals(itemMap.get("code"), cellValue)) {
                    cellValue = itemMap.get("desc").toString();
                    break;
                }
            }
        }
        return cellValue;
    }

    /**
     * 生成随机文件
     *
     * @return
     * @throws IOException
     */
    private static File genRandomFile() {
        try {
            String filePath = System.getProperty("user.dir")
                    + File.separator
                    + ReportConstant.EXPORT_FILE_PATH
                    + File.separator
                    + RandomUtil.get16LenStr()
                    + ReportConstant.EXCEL_FILE_SUFFIX;
            log.info("路径：{}", filePath);
            return FileUtils.createFile(filePath);
        } catch (Exception e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("创建文件失败", e);
        }

    }

    public static void main(String[] args) throws Exception{
        // 方法1 如果写到同一个sheet
        String fileName = System.getProperty("user.dir")
                + File.separator
                + ReportConstant.EXPORT_FILE_PATH
                + File.separator
                + RandomUtil.get16LenStr()
                + ReportConstant.EXCEL_FILE_SUFFIX;
        ExcelWriter excelWriter = null;
        try {
            // 这里 需要指定写用哪个class去写
            excelWriter = EasyExcel.write(new FileOutputStream(new File(fileName))).build();
            // 这里注意 如果同一个sheet只要创建一次
            WriteSheet writeSheet = EasyExcel.writerSheet("模板").build();
            // 去调用写入,这里我调用了五次，实际使用时根据数据库分页的总的页数来
            for (int i = 0; i < 5; i++) {
                // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
                List<List> data = new ArrayList<>();
                List x1 = new ArrayList();
                x1.add("ada");
                List x2 = new ArrayList();
                x2.add("opop");
                data.add(x1);
                data.add(x2);
                excelWriter.write(data, writeSheet);
            }
        } finally {
            // 千万别忘记finish 会帮忙关闭流
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }

    }
}
