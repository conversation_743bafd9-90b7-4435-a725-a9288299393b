package com.zhixianghui.service.export.aspect;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.entity.black.Blacklist;
import com.zhixianghui.facade.common.enums.BlacklistTagEnum;
import com.zhixianghui.facade.common.service.BlacklistFacade;
import com.zhixianghui.service.export.annotation.CheckBlack;
import org.apache.dubbo.config.annotation.Reference;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @ClassName CheckBlackAspect
 * @Description TODO
 * @Date 2023/3/1 16:50
 */
@Component
@Aspect
public class CheckBlackAspect {

    private DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
    private ExpressionParser parser = new SpelExpressionParser();

    @Reference
    private BlacklistFacade blacklistFacade;

    @Pointcut("@annotation(com.zhixianghui.service.export.annotation.CheckBlack)")
    public void setCheckBlackPointCut(){

    }

    @Around("setCheckBlackPointCut()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = signature.getMethod();
        CheckBlack checkBlack = method.getAnnotation(CheckBlack.class);

        String tag = checkBlack.tag();
        String targetEl = checkBlack.target();

        Expression expression = parser.parseExpression(targetEl);
        EvaluationContext context = new StandardEvaluationContext();
        //获取方法的形参名称
        String[] params = discoverer.getParameterNames(method);
        //获取方法的实际参数值
        Object[] arguments = proceedingJoinPoint.getArgs();
        for (int len = 0; len < discoverer.getParameterNames(method).length; len++) {
            context.setVariable(params[len], arguments[len]);
        }
        String subjectNo = expression.getValue(context,String.class);
        //判断是否在功能黑名单内，如果存在，执行handle，如果不在，则执行目标方法
        Blacklist blacklist = blacklistFacade.getBlacklist(tag,subjectNo);
        if (blacklist == null){
            return proceedingJoinPoint.proceed();
        }
        return handle(tag);
    }

    private Object handle(String tag) {
        if (BlacklistTagEnum.BILL_UPLOAD.getTag().equals(tag)){
            //电子回单
            return null;
        }else{
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不存在此黑名单功能tag");
        }
    }
}
