package com.zhixianghui.service.export.util;

import com.zhixianghui.common.util.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/1
 **/
public class ExportUtil {
    /**
     * 库里json转过来的都是str 处理时间类型
     *
     * @param paramMap 处理完的map
     */
    public static void dealStr2Date(Map<String, Object> paramMap) {
        //是否有查询时间
        String createBeginDateStr = (String) paramMap.get("createBeginDate");
        String createEndDateStr = (String) paramMap.get("createEndDate");
        String completeBeginDateStr = (String) paramMap.get("completeBeginDate");
        String completeEndDateStr = (String) paramMap.get("completeEndDate");
        if (StringUtils.isNotBlank(createBeginDateStr)) {
            Date createBeginDate = DateUtil.DATE_TIME_FORMATTER.parseDateTime(createBeginDateStr).toDate();
            paramMap.put("createBeginDate", createBeginDate);
        }
        if (StringUtils.isNotBlank(createEndDateStr)) {
            Date createEndDate = DateUtil.DATE_TIME_FORMATTER.parseDateTime(createEndDateStr).toDate();
            paramMap.put("createEndDate", createEndDate);
        }
        if (StringUtils.isNotBlank(completeBeginDateStr)) {
            Date completeBeginDate = DateUtil.DATE_TIME_FORMATTER.parseDateTime(completeBeginDateStr).toDate();
            paramMap.put("completeBeginDate", completeBeginDate);
        }
        if (StringUtils.isNotBlank(completeEndDateStr)) {
            Date completeEndDate = DateUtil.DATE_TIME_FORMATTER.parseDateTime(completeEndDateStr).toDate();
            paramMap.put("completeEndDate", completeEndDate);
        }
    }
}
