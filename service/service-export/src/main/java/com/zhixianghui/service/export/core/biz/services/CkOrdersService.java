//package com.zhixianghui.service.export.core.biz.services;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.zhixianghui.facade.export.entity.CkOrders;
//import com.zhixianghui.service.export.core.dao.mappers.CkOrdersMapper;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Map;
//@Service
//public class CkOrdersService extends ServiceImpl<CkOrdersMapper, CkOrders> {
//
//
//    public int updateBatch(List<CkOrders> list) {
//        return baseMapper.updateBatch(list);
//    }
//
//    public int updateBatchSelective(List<CkOrders> list) {
//        return baseMapper.updateBatchSelective(list);
//    }
//
//    public int batchInsert(List<CkOrders> list) {
//        return baseMapper.batchInsert(list);
//    }
//
//    public int insertOrUpdate(CkOrders record) {
//        return baseMapper.insertOrUpdate(record);
//    }
//
//    public int insertOrUpdateSelective(CkOrders record) {
//        return baseMapper.insertOrUpdateSelective(record);
//    }
//    public List<Map<String, Object>> listSalerOrdersGroupedByMch(Map<String, Object> params) {
//        return baseMapper.listSalerOrdersGroupedByMch(params);
//    }
//
//    public IPage<Map<String, Object>> salerstatistics(Page<Map<String, Object>> page, Map<String, Object> params) {
//        return baseMapper.salerstatistics(page, params);
//    }
//
//    public Map<String, Object> coreIndexStatistics(Map<String, Object> params) {
//        return baseMapper.coreIndexStatistics(params);
//    }
//
//    public List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> params) {
//        return baseMapper.coreIndexDailyDetail(params);
//    }
//
//    public List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> params) {
//        return baseMapper.coreIndexDetailMonthly(params);
//    }
//
//    public List<Map<String, Object>> listSalerOrdersGroupedByMchDaily(Map<String, Object> params){
//        return baseMapper.listSalerOrdersGroupedByMchDaily(params);
//    }
//
//    public List<Map<String, Object>> listSalerOrdersGroupedByMchMonthly(Map<String, Object> params){
//        return baseMapper.listSalerOrdersGroupedByMchMonthly(params);
//    }
//
//    public Map<String, Object> salerOrdersStatistics(Map<String, Object> params) {
//        return baseMapper.salerOrdersStatistics(params);
//    }
//
//    public Map<String, Object> countOrderAmount(Map<String, Object> paramMap) {
//        return baseMapper.countOrderAmount(paramMap);
//    }
//}
