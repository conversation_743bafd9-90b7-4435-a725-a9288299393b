package com.zhixianghui.service.export.listener.tasks;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.service.export.annotation.CheckBlack;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_BILL_SAVE,selectorExpression =MessageMsgDest.TAG_CMB_BILL_SAVE, consumeThreadMax = 1,consumerGroup = "cmbBillSaveConsumer")
@Slf4j
public class CmbBillSaveListener extends BaseRocketMQListener<String> {
    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;
    @Reference
    private CmbFacade cmbFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Reference
    private RecordItemFacade recordItemFacade;

    @Autowired
    private CmbBillHandler cmbBillHandler;

    @Override
    public void validateJsonParam(String param) {

    }

    @Override
    public void consumeMessage(String paramStr) {
        log.info("[招行电子回单]-保存招行电子回单到本地-开始,参数:{}",paramStr);
        JSONObject param = JSON.parseObject(paramStr);
        final String printid = param.getString("printid");
        if (printid == null) {
            log.info("printid为空");
            return;
        }

        final String acctountNo = param.getString("acctountNo");
        final String mainstayNo = param.getString("mainstayNo");


        final JSONObject queryResult = cmbFacade.getReconFileUrl(acctountNo, printid);
        if (queryResult == null) {
            log.info("[招行电子回单]-代发明细对账单未能查询到");
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("代发明细对账单未能查询到");
        }

        final String fileurl = queryResult.getString("fileurl");
        String tempPath = System.getProperty("user.dir") + File.separator + mainstayNo + File.separator + IdUtil.randomUUID()+".zip";
        String unzipPath = System.getProperty("user.dir") + File.separator + mainstayNo + File.separator + "unzip_files";

        ChannelSftp channelSftp = null;
        try {
            log.info("[招行电子回单]-下载电子回单{}到{}", printid, tempPath);
            HttpUtil.downloadFile(fileurl, tempPath);
            log.info("[招行电子回单]-解压电子回单{}到:{}", printid, unzipPath);
            ZipUtil.unzip(tempPath, unzipPath);
            final List<String> strings = FileUtil.listFileNames(unzipPath);
            log.info("[招行电子回单]-电子回单{}文件夹共有{}份文件",printid, strings == null ? 0 : strings.size());

            log.info("[招行电子回单]-连接sftp服务器-{}",printid);
            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            for (String fileName : strings) {
                final String platBatchNo = fileName.split("_")[5].replace(".pdf", "").replace("ZHIXIANG", "");
                if (!platBatchNo.startsWith("G")) {
                    return;
                }
                final String detailTrxSeq = fileName.split("_")[4];
                Map<String, Object> queryParam = new HashMap<>();
                queryParam.put("platBatchNo", platBatchNo);
                queryParam.put("detailTrxSeq", detailTrxSeq);
                RecordItem recordItem = recordItemFacade.getOne(queryParam);

                if (recordItem == null) {
                    return;
                }
                String remitNo = recordItem.getRemitPlatTrxNo();

                cmbBillHandler.handleData(recordItem,printid,remitNo,mainstayNo,fileName,unzipPath,channelSftp);
            }
        } catch (Exception e) {
            log.error("[招行电子回单]-上传回单失败",e);
        }finally {
            try {
                FileUtils.deleteDir(new File(tempPath));
            } catch (Exception e){
                log.error("删除目录时出现异常 tempPath:{}", tempPath, e);
            }
            try {
                FileUtils.deleteDir(new File(unzipPath));
            } catch (Exception e){
                log.error("删除目录时出现异常 unzipPath:{}", unzipPath, e);
            }
            if(channelSftp != null){
                SftpUtil.sftpClose(channelSftp);
            }
        }
    }

    @Service
    public class CmbBillHandler{

        @CheckBlack(tag = "bill:upload",target = "#recordItem.employerNo")
        public void handleData(RecordItem recordItem, String printid, String remitNo, String mainstayNo, String fileName, String unzipPath, ChannelSftp channelSftp) {
            if (recordItem != null) {
                log.info("[{}]上传订单-{}", printid, remitNo);
                String platTrxNo = recordItem.getPlatTrxNo();
                String newFileName = CertificateUtil.getPayFileName(recordItem.getReceiveNameDecrypt(), mainstayNo, platTrxNo);
                log.info("[{}]-开始上传文件",platTrxNo);
                File saveFile = FileUtils.createFile(unzipPath + File.separator + fileName);
                log.info("上传文件:{}", saveFile.getAbsolutePath());
                String mainSftpDir = CertificateUtil.getFilePath(dir, mainstayNo, platTrxNo);
                SftpUtil.uploadNoClose(mainSftpDir, saveFile, newFileName, channelSftp);
            }else {
                log.info("[{}]订单 {} 不存在", printid, remitNo);
            }
        }
    }


    public static void main(String[] args) {
        String dest = "D:/" + IdUtil.simpleUUID() + ".zip";
        String unzipFile = "D:/unzip";
        HttpUtil.downloadFile("http://s3gw.cmbchina.com/s/L2xzMTItMTMtemlwLWNkYy4wMC8xNTM4NzIwMTQzNjcyNjcyMjU3LnppcD9BV1NBY2Nlc3NLZXlJZD1sczEyLjEzX3VzZXIwMSZFeHBpcmVzPTE2NTU5NTM3MzEmU2lnbmF0dXJlPUVpJTJCNGh0b0I2WkVmJTJCS1hGajJkaWdrejUwVE0lM0QmcmVzcG9uc2UtY29udGVudC1kaXNwb3NpdGlvbj1hdHRhY2htZW50JTNCZmlsZW5hbWUlM0QxNTM4NzIwMTQzNjcyNjcyMjU3LnppcA==/xciuNfArWNOv97iL4LvzbUobpLk=/ls12.13_user01/0", dest);
        ZipUtil.unzip(dest, unzipFile);

        final List<String> strings = FileUtil.listFileNames(unzipFile);
        for (String fileName : strings) {
            final String simplefileName = fileName.split("_")[5].replace(".pdf", "").replace("ZHIXIANG", "");
        }
    }
}
