package com.zhixianghui.service.export.listener;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.constants.redis.RedisKeysManage;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.dto.FeeOrderBatchVo;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.entity.FeeOrderItem;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.service.FeeOrderItemFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.math.BigDecimal;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月21日 09:37:00
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_ORDER_CETIFICATE_UPLOAD, selectorExpression = MessageMsgDest.TAG_FEE_ORDER_ONLINE_CETIFICATE_APPLY,
        consumeThreadMax = 5, consumerGroup = "feeOnlineApplyConsumer")
public class FeeOrderOnlineUploadListener extends BaseRocketMQListener<String> {

    private static final String BIZ_SCENE = "ENTRUST_ALLOCATION";

    private static final String PRODUCT_CODE = "SINGLE_TRANSFER_NO_PWD";

    private static final String UNDERLINE = "_";

    private final static String FILE_SUFFIX = ".pdf";

    private final static Integer RETRY_COUNT = 2;

    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    @Autowired
    private RedisClient redisClient;
    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;
    @Reference
    private FeeOrderItemFacade feeOrderItemFacade;
    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private NotifyFacade notifyFacade;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String feeBatchNo) {
        log.info("[{}]开始账单下载",feeBatchNo);
        FeeOrderBatch feeOrderBatch = feeOrderBatchFacade.getByBatchNo(feeBatchNo);
        log.info("查询账单批次：{}",feeOrderBatch);
        AssertUtil.notNull(feeOrderBatch, "找不到对应的账单");
        FeeOrderBatchVo feeOrderBatchVo = feeOrderBatchFacade.selectOrderItem(feeBatchNo);
        log.info("查询账单子订单：{}",feeOrderBatchVo);
        //查询商户账户
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade
                .getByEmployerNoAndMainstayNoAndChannelType(feeOrderBatch.getEmployerNo(),
                        feeOrderBatch.getMainstayNo(), feeOrderBatch.getChannelType());
        AssertUtil.notNull(employerAccountInfo, "找不到对应的商户账户");

        //子协议号
        String subAgreementNo = employerAccountInfo.getSubAgreementNo();

        FeeOrderItem serviceItem = feeOrderBatchVo.getServiceItem();
        FeeOrderItem taxItem = feeOrderBatchVo.getTaxItem();
        boolean isZero = taxItem.getPayAmount().compareTo(BigDecimal.ZERO) == 0;
        try {
            log.info("账单电子回单下载,服务费路径:{},个税路径:{}",serviceItem.getCetificateUrl(),taxItem.getCetificateUrl());
            if(!StringUtil.isNoEmptyList(serviceItem.getCetificateUrl(),taxItem.getCetificateUrl())){
                log.info("服务费：{},个税：{}", serviceItem.getPayAmount(), taxItem.getPayAmount());
                handle(serviceItem, subAgreementNo);
                if (!isZero) {
                    log.info("[{}]申请个税电子回单", taxItem.getFeeItemNo());
                    handle(taxItem, subAgreementNo);
                }
            }
        } catch (Exception e) {
            log.error("账单申请回单异常:", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(e.getMessage());
        }


        //延迟三分钟获取下载链接
        ScheduledExecutorService scheduledThreadPool = Executors.newScheduledThreadPool(1);
        scheduledThreadPool.schedule(() -> {
            File zipFile = null;
            ChannelSftp channelSftp = null;
            String path = System.getProperty("user.dir") + File.separator + feeBatchNo;
            String tempPath = path + File.separator;
            try {
                String serviceFileUrl = alipayFacade.billQuery(serviceItem.getCetificateUrl(), subAgreementNo);
                log.info("账单电子回单获取下载地址：服务费：{}", serviceFileUrl);
                if (StringUtil.isEmpty(serviceFileUrl)) {
                    log.info("服务费电子回单暂未生成");
                    retry(feeOrderBatch);
                    return;
                }
                String taxFileUrl = null;
                if(!isZero){
                    taxFileUrl = alipayFacade.billQuery(taxItem.getCetificateUrl(), subAgreementNo);
                    log.info("账单电子回单获取下载地址：个税：{}", taxFileUrl);
                    if (StringUtil.isEmpty(taxFileUrl)) {
                        log.info("个税电子回单暂未生成");
                        retry(feeOrderBatch);
                        return;
                    }
                }


                channelSftp = SftpUtil.connect(host, port, userName, pwd);
                log.info("账单上传，本地路径地址:{}", tempPath);
                download(serviceFileUrl, serviceItem.getFeeItemNo(), tempPath, "服务费");
                if (!isZero) {
                    download(taxFileUrl, taxItem.getFeeItemNo(), tempPath, "个税");
                }
                String mainSftpDir = CertificateUtil.getFeeFilePath(dir, serviceItem.getEmployerNo(), feeBatchNo);
                zipFile = ZipUtil.zipFile(path);
                log.info("账单文件上传，路径：{}，压缩文件名：{}", mainSftpDir, zipFile.getName());
                SftpUtil.uploadNoClose(mainSftpDir, zipFile, zipFile.getName(), channelSftp);
                feeOrderBatch.setReceiptUrl(zipFile.getName());
                feeOrderBatchFacade.update(feeOrderBatch);
            } catch (Exception e) {
                log.error("账单回单上传sftp,处理异常", e);
            } finally {
                try {
                    FileUtils.deleteDir(new File(tempPath));
                    if (zipFile != null) {
                        boolean delete = zipFile.delete();
                        log.info("删除zip文件：{}", delete);
                    }
                } catch (Exception e) {
                    log.info("删除文件时出现异常 tempPath:{}", tempPath, e);
                }
                if (channelSftp != null) {
                    SftpUtil.sftpClose(channelSftp);
                }
            }
        }, 3, TimeUnit.MINUTES);
    }

    private void retry(FeeOrderBatch feeOrderBatch) {
        Long count = redisClient.incr(RedisKeysManage.getFeeOrderFileRetry(feeOrderBatch.getFeeBatchNo()));
        redisClient.expire(WxUtil.getRedisRetryKey(feeOrderBatch.getFeeBatchNo()), 60 * 60 * 2);
        if (count > RETRY_COUNT) {
            log.info("[{}]账单电子回单下载，超过重试次数", feeOrderBatch.getFeeBatchNo());
            return;
        }
        notifyFacade.sendOne(MessageMsgDest.TOPIC_FEE_ORDER_ITEM_PAY, feeOrderBatch.getEmployerNo(), feeOrderBatch.getFeeBatchNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_FEE_ORDER_ONLINE_CETIFICATE_APPLY, feeOrderBatch.getFeeBatchNo(), MsgDelayLevelEnum.M_10.getValue());
    }

    private void download(String fileUrl, String feeOrderItemNo, String tempPath, String name) {
        String fileName = feeOrderItemNo + UNDERLINE + name + FILE_SUFFIX;
        File saveFile = FileUtils.createFile(tempPath + fileName);
        HttpUtil.downloadFile(fileUrl, saveFile);
        log.info("账单凭证上传,文件名：{}", fileName);
    }

    private void handle(FeeOrderItem item, String agreementNo) throws Exception {
        final String RESPONSE_KEY = "alipay_fund_trans_common_query_response";
        final String ID_KEY = "pay_fund_order_id";

        String body = alipayFacade.transCommonQuery(item.getFeeItemNo(), BIZ_SCENE, PRODUCT_CODE);
        log.info("账单电子回单下载，支付宝订单id查询，请求返回结果:{}", body);
        JSONObject data = JSONUtil.parseObj(body).getJSONObject(RESPONSE_KEY);
        String payFundOrderId = data.getStr(ID_KEY);
        AssertUtil.notEmpty(payFundOrderId, "id查询异常");
        //开始发送回单申请
        String fileId = alipayFacade.billApply(payFundOrderId, agreementNo);
        item.setCetificateUrl(fileId);
        feeOrderItemFacade.update(item);
    }


}