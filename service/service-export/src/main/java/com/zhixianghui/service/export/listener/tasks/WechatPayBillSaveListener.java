package com.zhixianghui.service.export.listener.tasks;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.WxApiException;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.facade.banklink.service.wx.WxPayFacade;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.service.export.listener.TaskRocketMQListener;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 * <AUTHOR>
 * @ClassName WechatPayBillSaveListener
 * @Description TODO
 * @Date 2021/12/23 15:11
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_BILL_SAVE,consumeThreadMax = 1,consumerGroup = "wxBillSaveConsumer")
public class WechatPayBillSaveListener extends TaskRocketMQListener<JSONObject> {
    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    @Autowired
    private RedisClient redisClient;

    @Reference
    private WxPayFacade wxPayFacade;

    private final static String WX_BILL_KEY = "WX:BILL:LIST";

    @Override
    public void runTask(JSONObject jsonParam) {
        ChannelSftp channelSftp = null;
        FileOutputStream fileOutputStream = null;
        BufferedOutputStream bufferedOutputStream = null;
        try{
            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            log.info("微信回单下载-开始取出数据进行处理");
            String data = redisClient.lpop(WX_BILL_KEY);
            while (data !=null){
                JSONObject jsonData = JSONObject.parseObject(data);
                String remitPlatTrxNo = jsonData.getString("remitPlatTrxNo");
                String platTrxNo = jsonData.getString("platTrxNo");
                String mainstayNo = jsonData.getString("mainstayNo");
                String receiveName = jsonData.getString("receiveName");
                //获取微信电子回单
                WxResVo wxResVo = wxPayFacade.queryReceiptBill(remitPlatTrxNo);
                if (!wxResVo.isSuccess()){
                    log.error("[{}]电子回单查询异常:{}",platTrxNo,JSONObject.toJSON(wxResVo));
                    continue;
                }

                JSONObject jsonObject = JSONObject.parseObject(wxResVo.getResponBody());
                String downloadUrl = jsonObject.getString("download_url");
                byte[] bytes;
                try {
                     bytes = wxPayFacade.downloadReceiptBill(downloadUrl,remitPlatTrxNo);
                }catch (WxApiException e){
                    log.error("下载微信电子回单异常");
                    continue;
                }
                // 下载文件
                String tempPath = System.getProperty("user.dir") + File.separator + mainstayNo + File.separator;
                log.info("[{}]-开始下载文件",platTrxNo);
                try {
                    File file = FileUtils.createFile(tempPath + platTrxNo);
                    fileOutputStream = new FileOutputStream(file);
                    bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
                    bufferedOutputStream.write(bytes);
                    String newFileName = CertificateUtil.getPayFileName(receiveName, mainstayNo, platTrxNo);
                    String mainSftpDir = CertificateUtil.getFilePath(dir, mainstayNo, platTrxNo);
                    log.info("[{}]-开始上传文件",platTrxNo);
                    SftpUtil.uploadNoClose(mainSftpDir, file, newFileName, channelSftp);
                    bufferedOutputStream.flush();
                    fileOutputStream.flush();
                } catch (Exception e){
                    log.error("订单号：[{}]，下载电子回单异常",platTrxNo);
                    continue;
                } finally {
                    FileUtils.deleteDir(new File(tempPath));
                }
                log.info("弹出下一笔订单");
                data = redisClient.lpop(WX_BILL_KEY);
            }
        }finally {
            if(channelSftp != null){
                SftpUtil.sftpClose(channelSftp);
            }
            try {
                if (bufferedOutputStream != null){
                    bufferedOutputStream.close();
                }
                if (fileOutputStream != null){
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
