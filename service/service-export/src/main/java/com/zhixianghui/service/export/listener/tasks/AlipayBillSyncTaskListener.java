package com.zhixianghui.service.export.listener.tasks;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.service.export.annotation.CheckBlack;
import com.zhixianghui.service.export.listener.TaskRocketMQListener;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_BILL_TASK,consumeThreadMax = 1,consumerGroup = "alipayBillConsumer")
@Slf4j
public class AlipayBillSyncTaskListener extends TaskRocketMQListener<JSONObject> {
    public static final String ALIBILL_DATA_KEY = "AlibillData";
    public static final String ALIBILL_APPLY_FLAG = "AlibillApllyFlag";
    @Autowired
    private TaskClass taskClass;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Autowired
    private RedisClient redisClient;

    @Override
    public void validateJsonParam(JSONObject jsonParam) {
        if (Objects.isNull(jsonParam)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("入参不能为空");
        }
    }

    @Override
    public void runTask(JSONObject jsonParam) {
        String startTimeStr = String.valueOf(jsonParam.getStr("startTime")); //格式 yyyy-MM-dd HH:mm:ss
        String endTimeStr = String.valueOf(jsonParam.getStr("endTime")); //格式 yyyy-MM-dd HH:mm:ss

        Date startTime;
        Date endTime;
        if (StringUtils.isAnyBlank(startTimeStr, endTimeStr)) {
            Date yestoday = DateUtil.addDay(new Date(), -1);
            startTime = DateUtil.getDayStart(yestoday);
            endTime = DateUtil.getDayEnd(yestoday);
        }else{
            startTime = DateUtil.parseTime(startTimeStr);
            endTime = DateUtil.parseTime(endTimeStr);
        }
        redisClient.del(AlipayBillSyncTaskListener.ALIBILL_DATA_KEY);
        redisClient.del(ALIBILL_APPLY_FLAG);
        // 时间装配完成，开始进行业务
        log.info("支付宝回单定时任务参数：{}",JSONUtil.toJsonPrettyStr(jsonParam));
        log.info("支付宝回单定时任务起止时间：{}--{}", DateUtil.formatDateTime(startTime), DateUtil.formatDateTime(endTime));

        String mainstayNo = jsonParam.getStr("mainstayNo");
        if (StrUtil.isNotBlank(mainstayNo)) {
            MainstayChannelRelation mainstayChannelRelation=mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(mainstayNo, ChannelNoEnum.ALIPAY.name());
            taskClass.doTask(startTime,endTime,mainstayNo,mainstayChannelRelation.getAgreementNo(),redisClient);
            redisClient.set(ALIBILL_APPLY_FLAG, "ok");
        }else {

            //1. 查询出开通支付宝的供应商
            List<Map<String, Object>> mainstays = mainstayChannelRelationFacade.getAlipayMainstays();
            //2. 每个供应商启动一个线程
            mainstays.forEach(mainstay->{
                log.info("供应商:{} 开始执行", mainstay.get("mainstayNo"));
                taskClass.doTask(startTime,endTime,String.valueOf(mainstay.get("mainstayNo")),String.valueOf(mainstay.get("agreementNo")),redisClient);
            });
            redisClient.set(ALIBILL_APPLY_FLAG, "ok");
        }
    }
}

@Component
@Slf4j
class TaskClass{

    @Reference
    private RecordItemFacade recordItemFacade;
    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private EmployerAccountInfoFacade accountInfoFacade;
    @Autowired
    private AlipayBillHandler alipayBillHandler;

    @Value("${alipay.bill.delayLevel}")
    private String billDelayLevel;

//    @Async
    public void doTask(Date startTime, Date endTime, String mainstayNo, String agreementNo,RedisClient redisClient) {
        //分页查询该供应商对应的支付记录
        int page=1;
        int pageSize=100;
        Map<String, Object> params = new HashMap<>();
        params.put("createBeginDate",startTime);
        params.put("createEndDate",endTime);
        params.put("mainstayNo", mainstayNo);
        params.put("payChannelNo", ChannelNoEnum.ALIPAY.name());
        params.put("processStatus", RecordItemStatusEnum.PAY_SUCCESS.getValue());
        List<RecordItem> recordItems;
        do {
            PageParam pageParam = PageParam.newInstance(page, pageSize);
            log.info("供应商:{},查询第{}页", mainstayNo, page);
            PageResult<List<RecordItem>> recordItemPageResult = recordItemFacade.listPage(params, pageParam);
            recordItems = recordItemPageResult.getData();
            log.info("供应商:{},查询第{}页,数据 {} 条", mainstayNo, page, recordItems==null?0:recordItems.size());
            if (recordItems != null) {
                for (RecordItem recordItem : recordItems) {
                    // 查询支付宝资金流水号
                    try {
                        alipayBillHandler.alipayBillHandleData(recordItem, agreementNo,redisClient);
                    } catch (Exception e) {
                        try {
                            TimeUnit.SECONDS.sleep(5L);
                            log.error("[{}]查询支付宝记录出错：{}--等待5s进行重试", recordItem.getPlatTrxNo(), e.getMessage());
                            alipayBillHandler.alipayBillHandleData(recordItem, agreementNo,redisClient);
                        } catch (Exception exception) {
                            log.error("[{}]查询支付宝记录出错：{}--但是跳过该笔记录", recordItem.getPlatTrxNo(), e.getMessage());
                        }
                    }
                };
            }
            page++;
        } while (recordItems != null && !recordItems.isEmpty());

        log.info("供应商：{}-申请回单完成", mainstayNo);
    }

    @Service
    public class AlipayBillHandler{
        /**
         * 支付宝回单处理
         * @param recordItem
         * @param agreementNo
         * @param redisClient
         * @throws Exception
         */
        @CheckBlack(tag = "bill:upload",target = "#recordItem.employerNo")
        public void alipayBillHandleData(RecordItem recordItem, String agreementNo, RedisClient redisClient) throws Exception {
            Map<String,Object> map = new HashMap<>();
            if (recordItem.getProductNo().equals(ProductNoEnum.CKH.getValue())){
                map = ckhBillHandle(recordItem,agreementNo);
            }else if (recordItem.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
                map = zxhBillHandle(recordItem,agreementNo);
            }
            if (map.size() > 0){
                redisClient.lpush(AlipayBillSyncTaskListener.ALIBILL_DATA_KEY, JSON.toJSONString(map));
            }
        }

        private Map<String,Object> zxhBillHandle(RecordItem recordItem, String agreementNo)  throws Exception {
            String channelTrxNo = recordItem.getChannelTrxNo();
            //查询商户协议号
            EmployerAccountInfo accountInfo = accountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(recordItem.getEmployerNo(), recordItem.getMainstayNo(), recordItem.getChannelType());
            String body0 = alipayFacade.transCommonQuery(recordItem.getRemitPlatTrxNo(), "ENTRUST_ALLOCATION", "SINGLE_TRANSFER_NO_PWD");
            JSONObject data0 = JSONUtil.parseObj(body0).getJSONObject("alipay_fund_trans_common_query_response");
            String payFundOrderId0 = data0.getStr("pay_fund_order_id");
            //开始发送回单申请
            String fileId0 = alipayFacade.billApply(payFundOrderId0, accountInfo.getSubAgreementNo());
            List<Map<String, String>> fileIds = new ArrayList<>();
            Map<String, String> file0 = new HashMap<>();
            file0.put("fileId", fileId0);
            file0.put("type", "0");
            fileIds.add(file0);

            String body1 = alipayFacade.transCommonQueryByOrderId(channelTrxNo);
            JSONObject data1 = JSONUtil.parseObj(body1).getJSONObject("alipay_fund_trans_common_query_response");
            String payFundOrderId1 = data1.getStr("pay_fund_order_id");
            String fileId1 = alipayFacade.billApply(payFundOrderId1, agreementNo);
            Map<String, String> file1 = new HashMap<>();
            file1.put("fileId", fileId1);
            file1.put("type", "1");
            fileIds.add(file1);

            //发送到延时消息，进行获取账单路径
            Map<String, Object> msgBody = new HashMap<>();
            msgBody.put("productNo",ProductNoEnum.ZXH.getValue());
            msgBody.put("fileIds", fileIds);
            msgBody.put("agreementNo", agreementNo);
            msgBody.put("platTrxNo", recordItem.getPlatTrxNo());
            msgBody.put("mainstayNo", recordItem.getMainstayNo());
            msgBody.put("receiveName", recordItem.getReceiveNameDecrypt());
            msgBody.put("employerNo", recordItem.getEmployerNo());
            msgBody.put("remitPlatTrxNo", recordItem.getRemitPlatTrxNo());
            msgBody.put("mainstayName", recordItem.getMainstayName());
            msgBody.put("subAgreementNo", accountInfo.getSubAgreementNo());
            return msgBody;
        }

        private Map<String,Object> ckhBillHandle(RecordItem recordItem, String agreementNo) throws Exception {
            String channelTrxNo = recordItem.getChannelTrxNo();
            //查询商户协议号
            EmployerAccountInfo accountInfo = accountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(recordItem.getEmployerNo(), recordItem.getMainstayNo(), recordItem.getChannelType());
            //开始发送回单申请
            List<Map<String, String>> fileIds = new ArrayList<>();

            String body1 = alipayFacade.transCommonQueryByOrderId(channelTrxNo);
            JSONObject data1 = JSONUtil.parseObj(body1).getJSONObject("alipay_fund_trans_common_query_response");
            String payFundOrderId1 = data1.getStr("pay_fund_order_id");
            String fileId1 = alipayFacade.billApply(payFundOrderId1,accountInfo.getSubAgreementNo());
            Map<String, String> file1 = new HashMap<>();
            file1.put("fileId", fileId1);
            file1.put("type", "1");
            fileIds.add(file1);

            //发送到延时消息，进行获取账单路径
            Map<String, Object> msgBody = new HashMap<>();
            msgBody.put("productNo",ProductNoEnum.CKH.getValue());
            msgBody.put("fileIds", fileIds);
            msgBody.put("agreementNo", agreementNo);
            msgBody.put("platTrxNo", recordItem.getPlatTrxNo());
            msgBody.put("mainstayNo", recordItem.getMainstayNo());
            msgBody.put("receiveName", recordItem.getReceiveNameDecrypt());
            msgBody.put("employerNo", recordItem.getEmployerNo());
            msgBody.put("remitPlatTrxNo", recordItem.getRemitPlatTrxNo());
            msgBody.put("mainstayName", recordItem.getMainstayName());
            msgBody.put("subAgreementNo", accountInfo.getSubAgreementNo());
            return msgBody;
        }
    }
}
