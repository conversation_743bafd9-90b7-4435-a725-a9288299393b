package com.zhixianghui.service.export.export.data;

import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.ComponentTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.work.BaseComponent;
import com.zhixianghui.facade.flow.vo.work.SignComponent;
import com.zhixianghui.facade.flow.vo.work.UploadComponent;
import com.zhixianghui.facade.flow.vo.work.WorkForm;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.facade.merchant.service.AgreementFacade;
import com.zhixianghui.facade.merchant.service.AgreementFileFacade;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.entity.TaxCertificateRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.enums.ReceiptOrderEnum;
import com.zhixianghui.facade.trade.service.FreelanceStatFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.RechargeQueryFacade;
import com.zhixianghui.facade.trade.service.TaxCertificateRecordFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.service.export.util.ExportUtil;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * excel报表数据
 *
 * <AUTHOR>
 * @date 2020/8/31
 **/
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ZipDataBiz {
    private final FastdfsClient fastdfsClient;

    public static final String ID_CARD_COPY = "-身份证复印件";
    public static final String ID_CARD_BACK = "-身份证背面照";
    public static final String ID_CARD_FRONT = "-身份证正面照";
    public static final String CER_FACE = "-身份证半身照";

    @Reference
    private AgreementFacade agreementFacade;
    @Reference
    private AgreementFileFacade agreementFileFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private FreelanceStatFacade freelanceStatFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private RechargeQueryFacade rechargeQueryFacade;
    @Reference
    private TaxCertificateRecordFacade taxCertificateRecordFacade;


    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    /**
     * 生成文件
     *
     * @param reportType 报表类型
     * @param paramMap   参数
     * @param tempPath   文件夹路径
     */
    public void createFiles(Integer reportType, String fileNo, Map<String, Object> paramMap, String tempPath) throws Exception {
        if (Objects.equals(reportType, ReportTypeEnum.AGREEMENT_ARCHIVE_FILE.getValue())) {
            createAgreementArchiveFilePms(paramMap, tempPath);
        } else if (Objects.equals(reportType, ReportTypeEnum.PAY_CERTIFICATE.getValue())) {
            createPayCertificate(paramMap, tempPath, fileNo);
        } else if (Objects.equals(reportType, ReportTypeEnum.DOWNLOAD_ID_CARD.getValue())) {
            createIDCardFile(paramMap, tempPath, fileNo);
        } else if (Objects.equals(reportType,ReportTypeEnum.WORK_FLOW_TODO_EXPORT.getValue())){
            createTodoFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.WORK_FLOW_HANDLE_EXPORT.getValue())){
            createHandleFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.WORK_FLOW_CARBON_EXPORT.getValue())){
            createCarbonFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.WORK_SUP_FLOW_TODO_EXPORT.getValue())){
            createTodoFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.WORK_SUP_FLOW_HANDLE_EXPORT.getValue())){
            createSupplierHandleFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.AGREEMENT_PMS_EXPORT.getValue())){
            createAgreementFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.AGREEMENT_SUPPLIER_EXPORT.getValue())){
            createAgreementFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.AGREEMENT_PORTAL_EXPORT.getValue())){
            createAgreementFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.MERCHANT_PERSONNAL_RECEIPT_ORDER_EXPORT.getValue())){
            createCertificate(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.MERCHANT_BUSINESS_RECEIPT_ORDER_EXPORT.getValue())){
            createCertificate(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getValue())){
            createRechargeFile(paramMap,tempPath,fileNo);
        }else if (Objects.equals(reportType,ReportTypeEnum.PMS_TAX_CERTIFICATE.getValue())){
            createTaxCertificate(paramMap,tempPath,fileNo);
        }
    }

    private void createTaxCertificate(Map<String, Object> paramMap, String tempPath, String fileNo) throws IOException {
        List<TaxCertificateRecord> recordList = taxCertificateRecordFacade.list(paramMap);
        if (CollectionUtils.isNotEmpty(recordList)){
            for (TaxCertificateRecord x : recordList) {
                String dirName = x.getDateBegin().toString() + "～" + x.getDateEnd() + "-" + x.getEmployerName() + "-" + x.getMainstayName();
                FileUtils.creatDir(tempPath + File.separator + dirName);
                downloadTaxCertificate(x.getCertificateFilePath(),tempPath,dirName);
            }
        }
    }

    private void downloadTaxCertificate(List<String> certificateFilePath,String tempPath,String dirName) throws IOException {
        for (String s : certificateFilePath) {
            File file = FileUtils.createFile(tempPath + File.separator + dirName + File.separator + FilenameUtils.getName(s));
            InputStream inputStream = fastdfsClient.downloadFile(s);
            org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
        }
    }


    private void createRechargeFile(Map<String, Object> paramMap, String tempPath, String fileNo) throws IOException{
        List<RechargeRecord> list = rechargeQueryFacade.listBy(paramMap);
        if (CollectionUtils.isNotEmpty(list)){
            for (RechargeRecord rechargeRecord : list) {
                if (StringUtils.isBlank(rechargeRecord.getReceiptUrl())){
                    continue;
                }
                String fileName = String.join("-",rechargeRecord.getRechargeOrderId(),rechargeRecord.getChannelName(),rechargeRecord.getRechargeAmount().toString());
                File file = FileUtils.createFile(tempPath + File.separator + fileName + "." + FilenameUtils.getExtension(rechargeRecord.getReceiptUrl()));
                InputStream inputStream = fastdfsClient.downloadFile(rechargeRecord.getReceiptUrl());
                org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
            }
        }
    }

    private void createAgreementFile(Map<String, Object> paramMap, String tempPath, String fileNo) throws IOException {
        List<Agreement> agreementList = agreementFacade.listBy(paramMap);
        Map<Long,String> map = agreementList.stream().collect(Collectors.toMap(Agreement::getId,Agreement::getAgreementNo));
        List<AgreementFile> agreementFileList = agreementFileFacade.listBy(paramMap);
        if (CollectionUtils.isNotEmpty(agreementFileList)) {
            for (AgreementFile agreementFile : agreementFileList) {
                File file = FileUtils.createFile(tempPath + File.separator + map.get(agreementFile.getAgreementId()) + "-" + agreementFile.getFileName());
                InputStream inputStream = fastdfsClient.downloadFile(agreementFile.getFileUrl());
                org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
            }
        }
    }

    private void createCarbonFile(Map<String, Object> paramMap, String tempPath, String fileNo) {
        Long currentUserId = ((Integer) paramMap.get("userId")).longValue();
        Integer platform = (Integer) paramMap.get("platformSource");
        List<Map<String,Object>> list = flowFacade.carbonList(currentUserId,platform,paramMap,PageParam.newInstance(1,Integer.MAX_VALUE)).getData();
        getWorkFlowFile(tempPath,list);
    }

    private void createHandleFile(Map<String, Object> paramMap, String tempPath, String fileNo) {
        Boolean isAdmin = (Boolean) paramMap.get("isAdmin");
        Long currentUserId = ((Integer) paramMap.get("userId")).longValue();
        Integer platform = (Integer) paramMap.get("platformSource");
        List<Map<String,Object>> list = flowFacade.handleList(currentUserId,platform,isAdmin,paramMap,PageParam.newInstance(1,Integer.MAX_VALUE)).getData();
        getWorkFlowFile(tempPath,list);
    }

    private void createSupplierHandleFile(Map<String, Object> paramMap, String tempPath, String fileNo) {
        List<Map<String,Object>> list = flowFacade.workOrderPage(PlatformSource.SUPPLIER.getValue(),paramMap,PageParam.newInstance(1,Integer.MAX_VALUE)).getData();
        getWorkFlowFile(tempPath,list);
    }

    private void createTodoFile(Map<String, Object> paramMap, String tempPath, String fileNo) {
        //查询数据
        String json = (String) paramMap.get("flowUserVo");
        FlowUserVo flowUserVo = JsonUtil.toBean(json,FlowUserVo.class);
        Boolean isAdmin = (Boolean) paramMap.get("isAdmin");
        List<Map<String,Object>> list = flowFacade.todoList(flowUserVo,isAdmin,paramMap,PageParam.newInstance(1,Integer.MAX_VALUE)).getData();
        getWorkFlowFile(tempPath,list);
    }

    private void getWorkFlowFile(String tempPath,List<Map<String, Object>> list) {
        if (list.size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查不到对应的数据");
        }
        list.forEach(x->{
            CommonFlow commonFlow = flowFacade.getId((Long) x.get("commonFlowId"));
            WorkForm workForm = new WorkForm();
            workForm.transferBean(commonFlow.getExtInfo());
            String compactDate = DateUtil.formatCompactDate(commonFlow.getCreateTime());
            //创建文件夹
            String dirName = String.join("-",(String)x.get("workOrderNo"),compactDate,
                    (String)x.get("employerName"));
            if (StringUtils.isNotBlank((String)x.get("mainstayName"))){
                dirName = dirName + "-" + (String)x.get("mainstayName");
            }
            FileUtils.creatDir(tempPath + File.separator + dirName);
            downloadWorkFormFile(commonFlow.getId(),workForm,tempPath,dirName);
        });
    }

    private void downloadWorkFormFile(Long id,WorkForm workForm,String tempPath,String dir) {
        try {
            for (BaseComponent x : workForm.getComponent()) {
                if (x.getType().equals(ComponentTypeEnum.SIGN_COMPONENT.getType())){
                    SignComponent s = (SignComponent) x;
                    if (s.getValue().equals("2") && StringUtils.isNotBlank(s.getSignFileUrl())){
                        //线上签约文件
                        String suffix = s.getSignFileUrl().substring(s.getSignFileUrl().lastIndexOf("."));
                        File signFile = FileUtils.createFile(tempPath + File.separator + dir + File.separator + "线上签约文件" + suffix);
                        InputStream signFileStream = fastdfsClient.downloadFile(s.getSignFileUrl());
                        org.apache.commons.io.FileUtils.copyInputStreamToFile(signFileStream,signFile);
                    }else if (s.getValue().equals("1") && s.getUrls() != null && s.getUrls().size() > 0){
                        //线下签约文件
                        for (int i = 0; i < s.getUrls().size(); i++){
                            String suffix = s.getUrls().get(i).substring(s.getUrls().get(i).lastIndexOf("."));
                            File signFile = FileUtils.createFile(tempPath + File.separator + dir + File.separator + "线下签约文件" + (i+1) + suffix);
                            InputStream signFileStream = fastdfsClient.downloadFile(s.getUrls().get(i));
                            org.apache.commons.io.FileUtils.copyInputStreamToFile(signFileStream,signFile);
                        }
                    }
                }else if (x.getType().equals(ComponentTypeEnum.UPLOAD_COMPONENT.getType())){
                    UploadComponent u = (UploadComponent) x;
                    if (u.getValue() != null && u.getValue().size() > 0){
                        for (int i = 0; i < u.getValue().size(); i++){
                            String suffix = u.getValue().get(i).substring(u.getValue().get(i).lastIndexOf("."));
                            File file = FileUtils.createFile(tempPath + File.separator + dir + File.separator + "附件" + (i+1) + suffix);
                            InputStream fileStream = fastdfsClient.downloadFile(u.getValue().get(i));
                            org.apache.commons.io.FileUtils.copyInputStreamToFile(fileStream,file);
                        }
                    }
                }
            }
        }catch (IOException e){
            log.error("附件导出失败，流程id：[{}]", id, e);
        }
    }

    private void createIDCardFile(Map<String, Object> paramMap, String tempPath, String fileNo) {
        PageParam pageParam = PageParam.newInstance(1, ReportConstant.PAGE_SIZE, "ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<UserInfo>> pageResult = freelanceStatFacade.idCardList(paramMap, pageParam);
        if (pageResult ==null || CollectionUtils.isEmpty(pageResult.getData())) {
            return;
        }
        while (!CollectionUtils.isEmpty(pageResult.getData())) {
            for (UserInfo userInfo : pageResult.getData()) {
               try {
                   String path = userInfo.getReceiveNameDecrypt() + "-" + userInfo.getEmployerName()+ "-"+ userInfo.getEmployerNo();

                   if (StringUtils.isNotEmpty(userInfo.getIdCardCopyUrl())) {
                       String suffix = userInfo.getIdCardCopyUrl().substring(userInfo.getIdCardCopyUrl().lastIndexOf("."));
                       File idCardCopy = FileUtils.createFile(tempPath + File.separator + path + File.separator + userInfo.getReceiveNameDecrypt() + ID_CARD_COPY + suffix);
                       InputStream idCardCopyStream = fastdfsClient.downloadFile(userInfo.getIdCardCopyUrl());
                       org.apache.commons.io.FileUtils.copyInputStreamToFile(idCardCopyStream, idCardCopy);
                   }

                   if (StringUtils.isNotEmpty(userInfo.getIdCardBackUrl())) {
                       String suffix = userInfo.getIdCardBackUrl().substring(userInfo.getIdCardBackUrl().lastIndexOf("."));
                       File idCardBack = FileUtils.createFile(tempPath + File.separator + path + File.separator + userInfo.getReceiveNameDecrypt() + ID_CARD_BACK + suffix);
                       InputStream idCardBackStream = fastdfsClient.downloadFile(userInfo.getIdCardBackUrl());
                       org.apache.commons.io.FileUtils.copyInputStreamToFile(idCardBackStream, idCardBack);

                   }

                   if (StringUtils.isNotEmpty(userInfo.getIdCardFrontUrl())) {
                       String suffix = userInfo.getIdCardFrontUrl().substring(userInfo.getIdCardFrontUrl().lastIndexOf("."));
                       File idCardFront = FileUtils.createFile(tempPath + File.separator + path + File.separator + userInfo.getReceiveNameDecrypt() + ID_CARD_FRONT + suffix);
                       InputStream idCardFrontStream = fastdfsClient.downloadFile(userInfo.getIdCardFrontUrl());
                       org.apache.commons.io.FileUtils.copyInputStreamToFile(idCardFrontStream, idCardFront);
                   }

                   if (StringUtils.isNotEmpty(userInfo.getCerFaceUrl())) {
                       String suffix = userInfo.getCerFaceUrl().substring(userInfo.getCerFaceUrl().lastIndexOf("."));
                       File cerFace = FileUtils.createFile(tempPath + File.separator + path + File.separator + userInfo.getReceiveNameDecrypt() + CER_FACE + suffix);
                       InputStream cerFaceStream = fastdfsClient.downloadFile(userInfo.getCerFaceUrl());
                       org.apache.commons.io.FileUtils.copyInputStreamToFile(cerFaceStream, cerFace);
                   }
                   log.info("压缩: {}", tempPath + File.separator + path);
                   ZipUtil.zipFile(tempPath + File.separator + path);
                   FileUtils.deleteDir(new File(tempPath + File.separator + path));
               } catch (IOException e) {
                   log.error("用户身份证导出失败: {}", JSONObject.toJSON(userInfo), e);
               }
            }
            if(pageResult.getData().size() < ReportConstant.PAGE_SIZE){
                break;
            }
            Object maxId = pageResult.getData().get(pageResult.getData().size() - 1).getId();
            paramMap.put("idCardMaxId", maxId);
            pageResult = freelanceStatFacade.idCardList(paramMap, pageParam);
        }
    }

    /**
     * 生成协议文件
     */
    private void createAgreementArchiveFilePms(Map<String, Object> param, String tempPath) throws Exception {
        List<AgreementFile> agreementFiles = agreementFileFacade.listBy(param);
        if (CollectionUtils.isNotEmpty(agreementFiles)) {
            for (AgreementFile agreementFile : agreementFiles) {
                File file = FileUtils.createFile(tempPath + File.separator + agreementFile.getFileName());
                InputStream inputStream = fastdfsClient.downloadFile(agreementFile.getFileUrl());
                org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
            }
        }
    }

    private void createCertificate(Map<String, Object> paramMap, String tempPath, String fileNo) {
        ExportUtil.dealStr2Date(paramMap);
        PageParam pageParam = PageParam.newInstance(1, ReportConstant.PAGE_SIZE, "ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OrderItem>> pageResult = orderItemFacade.listPage(paramMap, pageParam);
        // 初始化sftp连接
        ChannelSftp channelSftp = SftpUtil.connect(host, port, userName, pwd);
        try {
            List<OrderItem> orderItemList = pageResult.getData();
            Integer type = (Integer) paramMap.get("type");
            while (CollectionUtils.isNotEmpty(orderItemList)) {
                for (OrderItem item : pageResult.getData()) {
                    //创客汇产品、微信通道、招行通道没有企业电子回单
                    if (type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue() && (item.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name()) || item.getPayChannelNo().equals(ChannelNoEnum.CMB.name()))){
                        continue;
                    }

                    if(type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue() && StringUtils.isNotBlank(item.getProductNo()) && item.getProductNo().equals(ProductNoEnum.CKH.getValue())){
                        continue;
                    }
                    File payFile = null;
                    // 文件所在的sftp文件夹
                    String sftpFilePath = CertificateUtil.getFilePath(dir, item.getMainstayNo(), item.getPlatTrxNo());
                    try{
                        String fileName = getFileName(type,item);
                        payFile = FileUtils.createFile(tempPath  + File.separator + fileName);
                        SftpUtil.downloadNoClose(sftpFilePath + fileName, payFile, channelSftp);
                    } catch (Exception e){
                        log.error("{} 文件生成下载失败：", fileNo, e);
                        if(payFile != null){
                            FileUtils.deleteDir(payFile);
                        }
                    }
                }
                if(orderItemList.size() < ReportConstant.PAGE_SIZE){
                    break;
                }
                Object maxId = orderItemList.get(orderItemList.size()-1).getId();
                paramMap.put("maxId", maxId);
                pageResult = orderItemFacade.listPage(paramMap, pageParam);
                orderItemList = pageResult.getData();
            }
        } finally {
            SftpUtil.sftpClose(channelSftp);
        }
    }

    private String getFileName(Integer type, OrderItem item) {
        if (type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue()){
            return CertificateUtil.getTransferFileName(item.getMainstayName(), item.getEmployerNo(), item.getPlatTrxNo());
        }else{
            if (StringUtils.isBlank(item.getProductNo()) || item.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
                return CertificateUtil.getPayFileName(item.getReceiveNameDecrypt(), item.getMainstayNo(), item.getPlatTrxNo());
            }else{
                return CertificateUtil.getCKHPayFileName(item.getReceiveNameDecrypt(), item.getEmployerNo(), item.getPlatTrxNo());
            }
        }
    }

    /**
     * 生成
     * @param paramMap
     * @param tempPath
     * @param fileNo
     */
    private void createPayCertificate(Map<String, Object> paramMap, String tempPath, String fileNo) {
        ExportUtil.dealStr2Date(paramMap);
        PageParam pageParam = PageParam.newInstance(1, ReportConstant.PAGE_SIZE, "ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OrderItem>> pageResult = orderItemFacade.listPage(paramMap, pageParam);
        // 初始化sftp连接
        ChannelSftp channelSftp = SftpUtil.connect(host, port, userName, pwd);
        try {
            List<OrderItem> orderItemList = pageResult.getData();
            while (CollectionUtils.isNotEmpty(orderItemList)) {
                for (OrderItem item : pageResult.getData()) {
                    File payFile = null;
                    File transferFile = null;
                    // 文件所在的sftp文件夹
                    String sftpFilePath = CertificateUtil.getFilePath(dir, item.getMainstayNo(), item.getPlatTrxNo());
                    try{
                        if (StringUtils.isBlank(item.getProductNo()) || item.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
                            String payFileName = CertificateUtil.getPayFileName(item.getReceiveNameDecrypt(), item.getMainstayNo(), item.getPlatTrxNo());
                            String transferFileName = CertificateUtil.getTransferFileName(item.getMainstayName(), item.getEmployerNo(), item.getPlatTrxNo());

                            payFile = FileUtils.createFile(tempPath  + File.separator + payFileName);
                            //微信支付只有payFile
                            if (item.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())||item.getPayChannelNo().equals(ChannelNoEnum.CMB.name())){
                                SftpUtil.downloadNoClose(sftpFilePath + payFileName, payFile, channelSftp);
                            }else{
                                transferFile = FileUtils.createFile(tempPath + File.separator + transferFileName);
                                SftpUtil.downloadNoClose(sftpFilePath + payFileName, payFile, channelSftp);
                                SftpUtil.downloadNoClose(sftpFilePath + transferFileName, transferFile, channelSftp);
                            }
                        }else if (item.getProductNo().equals(ProductNoEnum.CKH.getValue())){
                            String payFileName = CertificateUtil.getCKHPayFileName(item.getReceiveNameDecrypt(), item.getEmployerNo(), item.getPlatTrxNo());
                            payFile = FileUtils.createFile(tempPath  + File.separator + payFileName);
                            SftpUtil.downloadNoClose(sftpFilePath + payFileName, payFile, channelSftp);
                        }
                    } catch (Exception e){
                        log.error("{} 文件生成下载失败：", fileNo, e);
                        if(payFile != null){
                            FileUtils.deleteDir(payFile);
                        }
                        if(transferFile != null){
                            FileUtils.deleteDir(transferFile);
                        }
                    }
                }
                if(orderItemList.size() < ReportConstant.PAGE_SIZE){
                    break;
                }
                Object maxId = orderItemList.get(orderItemList.size()-1).getId();
                paramMap.put("maxId", maxId);
                pageResult = orderItemFacade.listPage(paramMap, pageParam);
                orderItemList = pageResult.getData();
            }
        } finally {
            SftpUtil.sftpClose(channelSftp);
        }
    }
}
