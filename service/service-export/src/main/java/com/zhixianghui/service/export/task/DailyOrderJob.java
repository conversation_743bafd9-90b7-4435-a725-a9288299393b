package com.zhixianghui.service.export.task;

import cn.hutool.core.util.IdUtil;
import com.opencsv.CSVWriter;
import com.opencsv.ICSVWriter;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import com.opencsv.exceptions.CsvRequiredFieldEmptyException;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.common.entity.push.PushManager;
import com.zhixianghui.facade.common.enums.PushTypeEnum;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.service.export.core.vo.DailyOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName DailyOrderJob
 * @Description TODO
 * @Date 2023/2/22 16:56
 */
@Slf4j
@Component
public class DailyOrderJob extends AbstractPushJob {

    private final static String BASE_PATH = System.getProperty("user.dir") + File.separator + "dailyOrder" + File.separator;
    private final static String FILE_EXT = ".csv";
    private final static int PAGE_SIZE = 200;

    @Reference
    private OrderItemFacade orderItemFacade;

    @Override
    public File getFile(PushManager pushManager) {
        Date date;
        //查询参数
        if (StringUtils.isNotBlank(pushDate.get())){
            date = DateUtil.parse(pushDate.get());
        }else{
            date = DateUtil.addDay(new Date(),-1);
        }

        Date beginTime = DateUtil.getDayStart(date);
        Date endTime = DateUtil.getDayEnd(date);
        //先查询一次确认是否有订单
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("createBeginDate",beginTime);
        paramMap.put("createEndDate",endTime);
        paramMap.put("employerNo",pushManager.getMchNo());
        Long total = orderItemFacade.countOrderItem(paramMap);
        if (total == 0L){
            //没有订单，直接跳过
            return null;
        }

        FileWriter writer = null;
        CSVWriter csvWriter = null;
        String fileName = pushManager.getMchNo() + "-" + DateUtil.formatDate(date) + PushTypeEnum.DAILY_ORDER.getDesc() + "-" + pushManager.getId() + ".csv";
        String filePath = System.getProperty("user.dir") + File.separator +  fileName;
        try {
            writer = new FileWriter(filePath);
            //生成CSV格式表头
            csvWriter = new CSVWriter(writer,CSVWriter.DEFAULT_SEPARATOR,
                    ICSVWriter.DEFAULT_QUOTE_CHARACTER,'\\',"\n");
            List<String> header = Arrays.stream(DailyOrderVo.class.getDeclaredFields()).map(
                    x-> x.getAnnotation(CsvBindByName.class).column()).collect(Collectors.toList());
            csvWriter.writeNext(header.toArray(new String[header.size()]));
            //写入数据
            writeData(paramMap,beginTime,endTime,pushManager,writer);
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                csvWriter.close();
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return new File(filePath);
    }

    private void writeData(Map<String,Object> paramMap,Date beginTime, Date endTime, PushManager pushManager,FileWriter writer) throws CsvDataTypeMismatchException, CsvRequiredFieldEmptyException {
        int currentPage = 1;
        PageParam pageParam = PageParam.newInstance(currentPage,PAGE_SIZE);
        PageResult<List<OrderItem>> orderItemList = null;
        List<DailyOrderVo> voList = null;
        do{
            pageParam.setPageCurrent(currentPage);
            orderItemList = orderItemFacade.listPage(paramMap,pageParam);
            voList = orderItemList.getData().stream().map(x->{
                DailyOrderVo dailyOrderVo = new DailyOrderVo();
                BeanUtil.copyProperties(x,dailyOrderVo);

                //处理数据
                dailyOrderVo.setCreateTime(DateUtil.formatDateTime(x.getCreateTime()));
                if (x.getCompleteTime() != null){
                    dailyOrderVo.setCompleteTime(DateUtil.formatDateTime(x.getCompleteTime()));
                }
                dailyOrderVo.setChannelTypeName(ChannelTypeEnum.getEnum(x.getChannelType()).getDesc());
                dailyOrderVo.setOrderItemStatus(OrderItemStatusEnum.getEnum(x.getOrderItemStatus()).getDesc());
                return dailyOrderVo;
            }).collect(Collectors.toList());

            StatefulBeanToCsv beanToCsv = new StatefulBeanToCsvBuilder<DailyOrderVo>(writer).build();
            beanToCsv.write(voList);
        }while (orderItemList.getTotalRecord() > (currentPage++ * PAGE_SIZE));
    }

}
