//package com.zhixianghui.service.export.listener.tasks;
//
//import com.alibaba.fastjson.JSONObject;
//import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
//import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
//import com.zhixianghui.common.statics.result.PageParam;
//import com.zhixianghui.common.statics.result.PageResult;
//import com.zhixianghui.common.util.utils.DateUtil;
//import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
//import com.zhixianghui.facade.notify.service.NotifyFacade;
//import com.zhixianghui.facade.trade.entity.OrderItem;
//import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
//import com.zhixianghui.facade.trade.service.OrderItemFacade;
//import com.zhixianghui.service.export.listener.TaskRocketMQListener;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.Reference;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//@Component
//@RocketMQMessageListener(topic = "topic_syncHisDataTask",consumeThreadMax = 1,consumerGroup = "SyncHisDataTaskConsumer")
//public class SyncHisDataTaskListener extends TaskRocketMQListener<JSONObject>{
//
//    @Reference
//    private OrderItemFacade orderItemFacade;
//    @Reference
//    private NotifyFacade notifyFacade;
//
//    @Override
//    public void runTask(JSONObject jsonParam) {
//        log.info("定时任务开始，入参：{}", jsonParam==null?"":jsonParam.toJSONString());
//        int pageSize = jsonParam.getInteger("pageSize");
//        final String startTimeStr = jsonParam.getString("beginTime");
//        final String endTimeStr = jsonParam.getString("endTime");
//
//        Date startTime;
//        Date endTime;
//        if (StringUtils.isAnyBlank(startTimeStr, endTimeStr)) {
//            Date yestoday = DateUtil.addDay(new Date(), -1);
//            startTime = DateUtil.getDayStart(yestoday);
//            endTime = DateUtil.getDayEnd(yestoday);
//        }else{
//            startTime = DateUtil.parseTime(startTimeStr);
//            endTime = DateUtil.parseTime(endTimeStr);
//        }
//        Map<String, Object> param = new HashMap<>();
//        param.put("createBeginDate", startTime);
//        param.put("createEndDate", endTime);
//        param.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
//
//        int pageIndex = 0;
//        List<OrderItem> recordItems = null;
//        do {
//            PageParam pageParam = PageParam.newInstance(pageIndex, pageSize);
//            final PageResult<List<OrderItem>> listPageResult = orderItemFacade.listPage(param, pageParam);
//            recordItems = listPageResult.getData();
//            if (recordItems != null && recordItems.size() > 0) {
//                log.info("数据同步-第{}页，共{}条数据", pageIndex, recordItems.size());
//                for (OrderItem orderItem : recordItems) {
//                    final String platTrxNo = orderItem.getPlatTrxNo();
//                    final String employerNo = orderItem.getEmployerNo();
//                    notifyFacade.sendOne(MessageMsgDest.TOPIC_ORDER_DATA_SYNC_CK, employerNo, platTrxNo, NotifyTypeEnum.ORDER_DATA_SYNC_CK.getValue(), MessageMsgDest.TAG_ORDER_DATA_SYNC_CK, platTrxNo, MsgDelayLevelEnum.M_1.getValue());
//                }
//            }else {
//                log.info("数据同步-第{}页，没有数据");
//            }
//            pageIndex++;
//        } while (recordItems != null && !recordItems.isEmpty());
//
//        log.info("定时任务 [SyncHisDataTaskListener] 执行完毕");
//    }
//}
