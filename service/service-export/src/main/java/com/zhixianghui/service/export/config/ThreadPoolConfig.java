package com.zhixianghui.service.export.config;

import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@SpringBootConfiguration
public class ThreadPoolConfig {

    @Bean
    public ThreadPoolTaskExecutor pushTaskExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("pushTaskExecutor-");
        //每次5条
        executor.setCorePoolSize(5);
        //上限任务500
        executor.setQueueCapacity(500);
        executor.setMaxPoolSize(10);
        executor.setKeepAliveSeconds(10);
        executor.initialize();
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor exportTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("executeTaskExecutor-");
        //线程池维护线程的最少数量
        executor.setCorePoolSize(3);
        //线程池所使用的缓冲队列
        executor.setQueueCapacity(10000);
        //线程池维护线程的最大数量
        executor.setMaxPoolSize(5);
        //线程池维护线程所允许的空闲时间(即额外的线程等待多久之后会被自动销毁)
        executor.setKeepAliveSeconds(10);
        executor.initialize();
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor statisticsTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("executeTaskExecutor-");
        //线程池维护线程的最少数量
        executor.setCorePoolSize(10);
        //线程池所使用的缓冲队列
        executor.setQueueCapacity(20000);
        //线程池维护线程的最大数量
        executor.setMaxPoolSize(15);
        //线程池维护线程所允许的空闲时间(即额外的线程等待多久之后会被自动销毁)
        executor.setKeepAliveSeconds(10);
        executor.initialize();
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor monthServiceConfirmExecutor(){
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("executeTaskExecutor-");
        //线程池维护线程的最少数量
        executor.setCorePoolSize(3);
        //线程池所使用的缓冲队列
        executor.setQueueCapacity(10000);
        //线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        //线程池维护线程所允许的空闲时间(即额外的线程等待多久之后会被自动销毁)
        executor.setKeepAliveSeconds(10);
        executor.initialize();
        return executor;
    }
}
