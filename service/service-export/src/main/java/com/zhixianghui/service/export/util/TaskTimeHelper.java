package com.zhixianghui.service.export.util;

import cn.hutool.json.JSONObject;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.export.dto.TimeRangeDto;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

public class TaskTimeHelper {

    public static TimeRangeDto getTimeRange(JSONObject jsonParam) {
        String startTimeStr = String.valueOf(jsonParam.getStr("startTime")); //格式 yyyy-MM-dd HH:mm:ss
        String endTimeStr = String.valueOf(jsonParam.getStr("endTime")); //格式 yyyy-MM-dd HH:mm:ss

        Date startTime;
        Date endTime;
        if (StringUtils.isAnyBlank(startTimeStr, endTimeStr)) {
            Date yestoday = DateUtil.addDay(new Date(), -1);
            startTime = DateUtil.getDayStart(yestoday);
            endTime = DateUtil.getDayEnd(yestoday);
        }else{
            startTime = DateUtil.parseTime(startTimeStr);
            endTime = DateUtil.parseTime(endTimeStr);
        }
        final TimeRangeDto timeRangeDto = new TimeRangeDto(startTime, endTime);
        return timeRangeDto;
    }

}
