package com.zhixianghui.service.export.task;

import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.export.utils.GrantDateUtil;
import com.zhixianghui.facade.trade.bo.RecordItemGroupBo;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.service.export.core.biz.StatisticsGrantBiz;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;

/**
 * @description: 发放tmp的定时统计
 * @author: xingguang li
 * @created: 2020/11/19 10:36
 */
@Slf4j
@Component
public class GrantUserTask {

    @Autowired
    private ThreadPoolTaskExecutor statisticsTaskExecutor;
    @Reference(timeout = 120000)
    private RecordItemFacade recordItemFacade;
    @Autowired
    private StatisticsGrantBiz statisticsGrantBiz;

    /**
     * 开启队列消费线程
     */
    @PostConstruct
    public void start() {
        Worker worker = new Worker();
        statisticsTaskExecutor.execute(worker);
    }

    public static void main(String[] args) {
        GrantDateUtil.GrantTmpDateDiffer grantTmpDateDiffer = GrantDateUtil.getGrantTmpMonth(new Date());
        System.out.println(JsonUtil.toString(grantTmpDateDiffer));
        System.out.println(DateUtil.addMonth(grantTmpDateDiffer.getStartDate(),-1));
        System.out.println(DateUtil.getDayStart(DateUtil.addMonth(grantTmpDateDiffer.getStartDate(),-1)));
    }

    /**
     * 从数据库中查出待导出的记录，放入队列中
     */
    public void doAddTask(Integer pageSize) {
        GrantDateUtil.GrantTmpDateDiffer grantTmpDateDiffer = GrantDateUtil.getGrantTmpMonth(new Date());
        log.info("add task：{}", JsonUtil.toString(grantTmpDateDiffer));
        // 获取待导出列表
        int offset = 0;
        List<RecordItemGroupBo> recordItems;

        //这里去除的是当前月份- （2个月）的供应商-发放人的键值对
        //假设现在是12月，则按取出创建时间为10月的数据
        Date startDate = DateUtil.addMonth(grantTmpDateDiffer.getStartDate(),-1);
        Date startDateTime = DateUtil.getDayStart(DateUtil.addMonth(grantTmpDateDiffer.getStartDate(),-1));
        Date endDateTime = DateUtil.getDayEnd(grantTmpDateDiffer.getEndDate());
        do {
            recordItems = recordItemFacade.getIdcardNoMainstayGroup(
                    startDate,
                    grantTmpDateDiffer.getEndDate(),
                    startDateTime,
                    endDateTime,
                    offset,
                    pageSize);

            log.info("add task，recordItems：{}", JsonUtil.toString(CollectionUtils.isNotEmpty(recordItems) ? recordItems.get(0).getReceiveIdCardNoMd5() : "no data"));
            recordItems.stream().forEach(item -> {
                //此时的item只是group by 查回来的 idcardNo + mainstay 组合，需要赋值createdate，但只赋值了月份
                item.setCreateDate(grantTmpDateDiffer.getStartDate());
                Worker worker = new Worker();
                worker.setRecordItemGroupBo(item);
                statisticsTaskExecutor.execute(worker);
            });
            offset = pageSize + offset;

        } while (CollectionUtils.isNotEmpty(recordItems));
    }

    /**
     * 运行导出任务的线程
     */
    @Data
    private class Worker implements Runnable {

        private RecordItemGroupBo recordItemGroupBo;
        @Override
        public void run() {
            try {
                final RecordItemGroupBo recordItem = this.recordItemGroupBo;
                if (recordItem != null) {
                    log.info("线程：{}，获取到任务：{}", Thread.currentThread().getName(), JsonUtil.toString(recordItem));
                    try {
                        statisticsGrantBiz.grantProcess(recordItem);
                    } catch (Exception e) {
                        if (e instanceof DuplicateKeyException) {
                            log.error("insertErrorWhenGrant：{}, 出现异常：", JsonUtil.toString(recordItem), e);
                        } else {
                            log.error("executeTimerGrantUserTaskError：{}, 出现异常：", JsonUtil.toString(recordItem), e);
                        }
                    }
                }
            } catch (Throwable e) {
                log.error("线程执行时出现异常", e);
            }
        }
    }
}
