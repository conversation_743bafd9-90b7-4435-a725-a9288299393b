package com.zhixianghui.service.export.listener.tasks;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.service.wx.WxPayFacade;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.enums.BlacklistTagEnum;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.service.export.annotation.CheckBlack;
import com.zhixianghui.service.export.listener.TaskRocketMQListener;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @ClassName WechatPayBillQueryListener
 * @Description TODO
 * @Date 2021/12/23 10:16
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_BILL_QUERY, consumeThreadMax = 1, consumerGroup = "wxBillConsumer")
public class WechatPayBillQueryListener extends TaskRocketMQListener<JSONObject> {

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Reference
    private WxPayFacade wxPayFacade;

    @Reference
    private RecordItemFacade recordItemFacade;

    @Autowired
    private RedisClient redisClient;

    @Autowired
    private WxBillHandler wxBillHandler;

    private final static String WX_BILL_KEY = "WX:BILL:LIST";

    @Override
    public void runTask(JSONObject jsonParam) {
        String startTimeStr = String.valueOf(jsonParam.getString("startTime")); //格式 yyyy-MM-dd HH:mm:ss
        String endTimeStr = String.valueOf(jsonParam.getString("endTime")); //格式 yyyy-MM-dd HH:mm:ss

        Date startTime;
        Date endTime;
        if (StringUtils.isAnyBlank(startTimeStr, endTimeStr)) {
            Date yestoday = DateUtil.addDay(new Date(), -1);
            startTime = DateUtil.getDayStart(yestoday);
            endTime = DateUtil.getDayEnd(yestoday);
        } else {
            startTime = DateUtil.parseTime(startTimeStr);
            endTime = DateUtil.parseTime(endTimeStr);
        }
        redisClient.del(WX_BILL_KEY);
        //1. 查询出开通支付宝的供应商
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("payChannelNo", ChannelNoEnum.WXPAY.name());
        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        List<MainstayChannelRelation> mainstayChannelRelationList = mainstayChannelRelationFacade.listBy(paramMap);
        //2. 每个供应商启动一个线程
        mainstayChannelRelationList.forEach(mainstay -> {
            log.info("供应商:{} 开始执行", mainstay.getMainstayNo());
            if (StringUtils.isNotBlank(mainstay.getChannelMchNo())) {
                CompletableFuture.runAsync(() -> {
                    //异步执行
                    getWxPayBill(mainstay, startTime, endTime);
                });
            }
        });
    }

    private void getWxPayBill(MainstayChannelRelation mainstay, Date startTime, Date endTime) {
        int page = 1;
        int pageSize = 100;
        Map<String, Object> params = new HashMap<>();
        params.put("createBeginDate", startTime);
        params.put("createEndDate", endTime);
        params.put("mainstayNo", mainstay.getMainstayNo());
        params.put("payChannelNo", ChannelNoEnum.WXPAY.name());
        params.put("processStatus", RecordItemStatusEnum.PAY_SUCCESS.getValue());
        List<RecordItem> recordItems;
        do {
            PageParam pageParam = PageParam.newInstance(page, pageSize);
            log.info("供应商:{},查询第{}页", mainstay.getMainstayNo(), page);
            PageResult<List<RecordItem>> recordItemPageResult = recordItemFacade.listPage(params, pageParam);
            recordItems = recordItemPageResult.getData();
            log.info("供应商:{},查询第{}页,数据 {} 条", mainstay.getMainstayNo(),pageParam.getPageCurrent(), recordItems == null ? 0 : recordItems.size());
            if (recordItems != null) {
                for (RecordItem recordItem : recordItems) {
                    try {
                        wxBillHandler.handleData(recordItem);
                    } catch (Exception e) {
                        try {
                            TimeUnit.SECONDS.sleep(5L);
                            log.error("[{}]查询微信记录出错：{}--等待5s进行重试", recordItem.getPlatTrxNo(), e.getMessage());
                            wxBillHandler.handleData(recordItem);
                        } catch (Exception exception) {
                            log.error("[{}]查询微信记录出错：{}--但是跳过该笔记录", recordItem.getPlatTrxNo(), e.getMessage());
                        }
                    }
                }
                ;
            }
            page++;
        } while (recordItems != null && !recordItems.isEmpty());

    }

    @Service
    public class WxBillHandler {

        @CheckBlack(tag = "bill:upload", target = "#recordItem.employerNo")
        public void handleData(RecordItem recordItem) {
            WxResVo wxResVo = wxPayFacade.receiptAccept(recordItem.getRemitPlatTrxNo());
            String responseBody = wxResVo.getResponBody();
            JSONObject jsonObject = JSONObject.parseObject(responseBody);
            String code = String.valueOf(jsonObject.get("code"));
            if (!wxResVo.isSuccess() && !code.equals("ALREADY_EXISTS")) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求微信接口错误");
            }
            Map<String, Object> map = new HashMap<>();
            map.put("remitPlatTrxNo", recordItem.getRemitPlatTrxNo());
            map.put("platTrxNo", recordItem.getPlatTrxNo());
            map.put("receiveName", recordItem.getReceiveNameDecrypt());
            map.put("mainstayNo", recordItem.getMainstayNo());
            //成功的订单号写入到缓存中，等待下一个定时器执行下载回单
            redisClient.lpush(WX_BILL_KEY, JSON.toJSONString(map));
        }
    }
}

