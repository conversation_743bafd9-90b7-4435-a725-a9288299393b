package com.zhixianghui.service.export.facade;

import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.service.export.core.biz.ExportRecordBiz;
import com.zhixianghui.service.export.helper.HelperBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 导出文件记录
 * <AUTHOR>
 * @date 2020/9/3
 **/
@Service
public class ExportRecordFacadeImpl implements ExportRecordFacade {

    @Autowired
    private ExportRecordBiz exportRecordBiz;
    @Autowired
    private HelperBiz helperBiz;

    @Override
    public String genFileNo() {
        return helperBiz.genFileNo();
    }

    @Override
    public void insert(ExportRecord exportRecord) {
        exportRecordBiz.insert(exportRecord);
    }

    @Override
    public List<ExportRecord> listBy(Integer systemType, String loginName, String mchNo, Integer reportType) {
        return exportRecordBiz.listBy(systemType, loginName, mchNo, reportType);
    }

    @Override
    public List<ExportRecord> listBy(Integer systemType, String loginName, String mchNo, Integer reportType,Integer direction) {
        return exportRecordBiz.listBy(systemType, loginName, mchNo, reportType, direction);
    }
}
