package com.zhixianghui.service.export.task;

import cn.hutool.core.io.FileUtil;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.vo.ServiceConfirmVo;
import com.zhixianghui.service.export.constant.ReportConstant;
import com.zhixianghui.service.export.core.biz.ServiceConfirmExportBiz;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CountDownLatch;

import static com.zhixianghui.facade.export.utils.GrantDateUtil.getTimeStrOfMax;
import static com.zhixianghui.facade.export.utils.GrantDateUtil.getTimeStrOfZero;

/**
 * <AUTHOR>
 * @ClassName MonthServiceConfirmTask
 * @Description TODO
 * @Date 2022/3/22 11:03
 */
@Slf4j
@Component
public class MonthServiceConfirmTask {

    @Autowired
    private ThreadPoolTaskExecutor monthServiceConfirmExecutor;

    @Autowired
    private ServiceConfirmExportBiz serviceConfirmBiz;

    @Reference
    private RecordItemFacade recordItemFacade;

    @Autowired
    private FastdfsClient fastdfsClient;

    public void doAddTask(String path,Integer pageSize,String date,String mchNo,String mainstayNo){
        //查询发放过的商户，根据用户企业-供应商-自由职业服务类型过滤
        if (StringUtils.isBlank(date)){
            date = DateUtil.formatDate(new Date());
        }
        //构建查询条件
        Map<String,Object> paramMap = new HashMap<>();
        Date lastMonthBegin = getTimeStrOfZero(DateUtil.getFirstDayOfLastMonth(date));
        Date lastMonthEnd = getTimeStrOfMax(DateUtil.getLastDayOfLastMonth(date));
        paramMap.put("completeBeginDate",lastMonthBegin);
        paramMap.put("completeEndDate", lastMonthEnd);
        paramMap.put("mchNo",mchNo);
        paramMap.put("mainstayNo",mainstayNo);
        File file = getFile(path);
        //序列化参数
        String month = DateUtil.dateYearMonthFormatter(lastMonthBegin);
        //查询总任务数
        Integer totalTask = recordItemFacade.getWorkCategoryCodeCount(paramMap);
        final CountDownLatch countDownLatch = new CountDownLatch(totalTask);
        int offset = 0;
        List<ServiceConfirmVo> serviceConfirmVoList;
        do{
            serviceConfirmVoList = recordItemFacade.getWorkCategoryCode(paramMap,offset, pageSize);
            serviceConfirmVoList.stream().forEach(item -> {
                Worker worker = new Worker();
                item.setDate(month);
                item.setCompleteBeginDate(lastMonthBegin);
                item.setCompleteEndDate(lastMonthEnd);
                worker.setServiceConfirmVo(item);
                worker.setFile(file);
                worker.setCountDownLatch(countDownLatch);
                monthServiceConfirmExecutor.execute(worker);
            });
            offset = pageSize + offset;
        }while (CollectionUtils.isNotEmpty(serviceConfirmVoList));

        try {
            countDownLatch.await();
        } catch (Exception e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("线程异常");
        }finally {
            FileUtil.del(file);
        }
    }

    /**
     * 解决inputstream read后指针改变问题
     * @return
     */
    private File getFile(String path) {
        //获取模板文件
        InputStream inputStream = null;
        try {
            inputStream = fastdfsClient.downloadFile(path);
            File file = FileUtils.createFile(System.getProperty("user.dir") +
                    File.separator + RandomUtil.get16LenStr() + ReportConstant.DOCX_FILE_SUFFIX);
            org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
            return file;
        } catch (Exception e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件下载失败");
        }finally {
            if (inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Data
    private class Worker implements Runnable {

        private ServiceConfirmVo serviceConfirmVo;

        private File file;

        private CountDownLatch countDownLatch;

        @Override
        public void run() {
            try {
                // 如果当前活动线程等于最大线程，那么不执行
                if (monthServiceConfirmExecutor.getActiveCount() < monthServiceConfirmExecutor.getMaxPoolSize()) {
                    if (serviceConfirmVo != null) {
                        serviceConfirmBiz.serviceConfirmProcess(serviceConfirmVo,file,countDownLatch);
                    }
                } else {
                    log.info("threadPool is reach max size{}", monthServiceConfirmExecutor.getActiveCount());
                }
            } catch (Throwable e) {
                log.error("线程执行时出现异常", e);
            }
        }
    }
}
