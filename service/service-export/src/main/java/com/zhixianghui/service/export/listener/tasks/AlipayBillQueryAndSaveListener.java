package com.zhixianghui.service.export.listener.tasks;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.service.export.listener.TaskRocketMQListener;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_BILL_QUERY,consumeThreadMax = 1,consumerGroup = "alipayBillQueryConsumer")
public class AlipayBillQueryAndSaveListener extends TaskRocketMQListener<JSONObject> {
    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    @Autowired
    private RedisClient redisClient;

    @Reference
    private AlipayFacade alipayFacade;

    @Override
    public void validateJsonParam(JSONObject jsonParam) {

    }

    @Override
    public void runTask(JSONObject jsonParamStr) {
        if (!StringUtils.equals("ok", redisClient.get(AlipayBillSyncTaskListener.ALIBILL_APPLY_FLAG))) {
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("申请单数据还没有准备好");
        }

        ChannelSftp channelSftp = null;
        try{
            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            log.info("支付宝回单下载-开始取出数据进行处理");
            String data = redisClient.lpop(AlipayBillSyncTaskListener.ALIBILL_DATA_KEY);
            while (data !=null){
                JSONObject jsonParam = JSONUtil.parseObj(data);
                String agreementNo = jsonParam.getStr("agreementNo");
                String platTrxNo = jsonParam.getStr("platTrxNo");
                List<Map<String, String>> fileIds = jsonParam.get("fileIds", List.class);
                String mainstayNo = jsonParam.getStr("mainstayNo");
                String receiveName = jsonParam.getStr("receiveName");
                String employerNo = jsonParam.getStr("employerNo");
                String mainstayName = jsonParam.getStr("mainstayName");
                String remitPlatTrxNo = jsonParam.getStr("remitPlatTrxNo");
                String subAgreementNo = jsonParam.getStr("subAgreementNo");
                String productNo = jsonParam.getStr("productNo");
                List<Map<String, String>> files = new ArrayList<>();
                for (Map<String, String> fileItem : fileIds) {
                    //去支付宝查询
                    try {
                        log.info("[{}]查询支付宝账单下载地址",platTrxNo);
                        String fileUrl;
                        if (productNo.equals(ProductNoEnum.ZXH.getValue())){
                            fileUrl = getZXHFileUrl(fileItem,subAgreementNo,agreementNo,platTrxNo);
                        }else if (productNo.equals(ProductNoEnum.CKH.getValue())){
                            fileUrl = getCKHFileUrl(fileItem,subAgreementNo,platTrxNo);
                        }else{
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不支持的产品");
                        }

                        Map<String, String> fileMap = new HashMap<>();
                        fileMap.put("fileUrl", fileUrl);
                        fileMap.put("type", fileItem.get("type"));
                        fileMap.put("fileId", fileItem.get("fileId"));
                        files.add(fileMap);
                    } catch (BizException e) {
                        log.error("[{} - 查询账单错误]-{}", platTrxNo,e.getErrMsg());
                        continue;
                    } catch (Exception e) {
                        log.error("[{} - 查询账单错误]", platTrxNo,e);
                        throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("查询账单错误");
                    }
                }

                // 下载文件
                String tempPath = System.getProperty("user.dir") + File.separator + mainstayNo + File.separator;
                log.info("[{}]-开始下载文件",platTrxNo);
                try {
                    for (Map<String, String> fileItem : files){
                        String type = fileItem.get("type");
                        String fileUrl = fileItem.get("fileUrl");
                        File saveFile = FileUtils.createFile(tempPath + platTrxNo+type);
                        HttpUtil.downloadFile(fileUrl, saveFile);
                        String mainSftpDir = CertificateUtil.getFilePath(dir, mainstayNo, platTrxNo);

                        String newFileName = "";
                        if (StringUtils.equals(type,"0")){
                            newFileName = CertificateUtil.getTransferFileName(mainstayName, employerNo, platTrxNo);
                        } else if (StringUtils.equals(type, "1")) {
                            if (productNo.equals(ProductNoEnum.ZXH.getValue())){
                                newFileName = CertificateUtil.getPayFileName(receiveName, mainstayNo, platTrxNo);
                            }else if (productNo.equals(ProductNoEnum.CKH.getValue())){
                                newFileName = CertificateUtil.getCKHPayFileName(receiveName, employerNo, platTrxNo);
                            }
                        }else {
                            continue;
                        }
                        log.info("[{}]-开始上传文件",platTrxNo);
                        SftpUtil.uploadNoClose(mainSftpDir, saveFile, newFileName, channelSftp);
                    }
                }finally {
                    FileUtils.deleteDir(new File(tempPath));
                }
                log.info("弹出下一笔订单");
                data = redisClient.lpop(AlipayBillSyncTaskListener.ALIBILL_DATA_KEY);
            }
        }finally {
            if(channelSftp != null){
                SftpUtil.sftpClose(channelSftp);
            }
        }
    }

    private String getCKHFileUrl(Map<String, String> fileItem, String subAgreementNo, String platTrxNo) throws Exception {
        String fileUrl = alipayFacade.billQuery(fileItem.get("fileId"), subAgreementNo);
        if (StringUtils.isBlank(fileUrl)) {
            log.info("[{}]查询不到账单",platTrxNo);
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("查询账单错误");
        }
        return fileUrl;
    }

    private String getZXHFileUrl(Map<String,String> fileItem,String subAgreementNo,String agreementNo,String platTrxNo) throws Exception {
        String realAgreementNo = StringUtils.equals("0", fileItem.get("type")) ? subAgreementNo : agreementNo;
        String fileUrl = alipayFacade.billQuery(fileItem.get("fileId"), realAgreementNo);
        if (StringUtils.isBlank(fileUrl)) {
            log.info("[{}]查询不到账单",platTrxNo);
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("查询账单错误");
        }
        return fileUrl;
    }
}
