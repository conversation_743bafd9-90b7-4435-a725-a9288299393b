package com.zhixianghui.service.export.listener;

import cn.hutool.core.lang.TypeReference;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.service.export.core.biz.ApiBillBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ApiBillListener
 * @Description TODO
 * @Date 2022/9/30 10:59
 */
@Slf4j
@Component
public class ApiBillListener {

    @Autowired
    private ApiBillBiz apiBillBiz;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_API_BILL_APPLY,selectorExpression = MessageMsgDest.TAG_API_BILL_APPLY,consumeThreadMax = 10,consumerGroup = "apiBillConsumer")
    public class ApiBillApplyListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String json) {
            Map<String,String> map = JsonUtil.toBean(json, new TypeReference<Map<String,String>>(){});
            apiBillBiz.getBill(map);
        }
    }
}
