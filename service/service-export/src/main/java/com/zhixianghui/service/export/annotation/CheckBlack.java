package com.zhixianghui.service.export.annotation;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @ClassName CheckBlack
 * @Description TODO
 * @Date 2023/3/1 16:43
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CheckBlack {

    String target();

    String tag();

}
