<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.export.entity.StatisticsGrant2user">
    <sql id="table"> tbl_statistics_grant2user </sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.export.entity.StatisticsGrant2user">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="ID_CARD_NO" property="idCardNo" jdbcType="VARCHAR"/>
        <result column="SUPPLIER_NO" property="supplierNo" jdbcType="VARCHAR"/>
        <result column="YEAR" property="year" jdbcType="VARCHAR"/>
        <result column="MONTH" property="month" jdbcType="SMALLINT"/>
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
		ID,
		VERSION,
		CREATE_TIME,
		UPDATE_TIME,
		ID_CARD_NO,
		SUPPLIER_NO,
		`YEAR`,
		`MONTH`,
		AMOUNT
	</sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.export.entity.StatisticsGrant2user">
        INSERT INTO <include refid="table" /> (
        VERSION,
        CREATE_TIME,
        UPDATE_TIME,
        ID_CARD_NO,
        SUPPLIER_NO,
        `YEAR`,
        `MONTH`,
        AMOUNT
        ) VALUES (
        0,
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{idCardNo,jdbcType=VARCHAR},
        #{supplierNo,jdbcType=VARCHAR},
        #{year,jdbcType=VARCHAR},
        #{month,jdbcType=SMALLINT},
        #{amount,jdbcType=DECIMAL}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.export.entity.StatisticsGrant2user">
        UPDATE <include refid="table" /> SET
        VERSION = #{version,jdbcType=SMALLINT} + 1,
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        ID_CARD_NO = #{idCardNo,jdbcType=VARCHAR},
        SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR},
        `YEAR` = #{year,jdbcType=VARCHAR},
        `MONTH` = #{month,jdbcType=SMALLINT},
        AMOUNT = #{amount,jdbcType=DECIMAL}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        order by id desc
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        order by id desc
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        select count(1) from
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <select id="getAmountGroup" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.dto.RiskControlAmountDto">
        SELECT supplier_no as mainstayNo,sum(AMOUNT) AS amount
        FROM <include refid="table" />
        WHERE ID_CARD_NO = #{idCardNo,jdbcType=VARCHAR}
        AND `YEAR` = #{year,jdbcType=VARCHAR}
        AND month BETWEEN 1 AND #{month,jdbcType=SMALLINT}
        group by supplier_no
    </select>


    <select id="getAmount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        SELECT sum(AMOUNT) AS "totalAmount"
        FROM <include refid="table" />
        WHERE ID_CARD_NO = #{idCardNo,jdbcType=VARCHAR}
        <if test="supplierNo != null and supplierNo != ''">
            AND SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
        </if>
        AND `YEAR` = #{year,jdbcType=VARCHAR}
        AND month BETWEEN 1 AND #{month,jdbcType=SMALLINT}
    </select>

    <select id="getYearEndAmountGroup" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.dto.RiskControlAmountDto">
        SELECT supplier_no as mainstayNo,sum(AMOUNT) AS amount
        FROM <include refid="table" />
        WHERE ID_CARD_NO = #{idCardNo,jdbcType=VARCHAR}
        AND `YEAR` = #{year,jdbcType=VARCHAR}
        AND month <![CDATA[ > ]]> #{month,jdbcType=SMALLINT}
        and month <![CDATA[ < ]]> #{endMonth,jdbcType=SMALLINT}
        group by supplier_no
    </select>

    <select id="getYearEndAmount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        SELECT sum(AMOUNT) AS "totalAmount"
        FROM <include refid="table" />
        WHERE ID_CARD_NO = #{idCardNo,jdbcType=VARCHAR}
        <if test="supplierNo != null and supplierNo != ''">
            AND SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
        </if>
        AND `YEAR` = #{year,jdbcType=VARCHAR}
        AND month <![CDATA[ > ]]> #{month,jdbcType=SMALLINT}
        and month <![CDATA[ < ]]> #{endMonth,jdbcType=SMALLINT}
    </select>

    <select id="sumAmountByIdCard" parameterType="java.lang.String" resultType="java.math.BigDecimal">
        select IFNULL(SUM(AMOUNT),0) from <include refid="table"/> where ID_CARD_NO = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
    </select>

    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="idCardNo != null and idCardNo !=''">
            and ID_CARD_NO = #{idCardNo,jdbcType=VARCHAR}
        </if>
        <if test="supplierNo != null and supplierNo !=''">
            and SUPPLIER_NO = #{supplierNo,jdbcType=VARCHAR}
        </if>
        <if test="year != null">
            and `YEAR` = #{year,jdbcType=VARCHAR}
        </if>
        <if test="month != null">
            and `MONTH` = #{month,jdbcType=SMALLINT}
        </if>
    </sql>
</mapper>

