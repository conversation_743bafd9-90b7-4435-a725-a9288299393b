<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.export.entity.StatisticsRecordUnique">
    <sql id="table"> tbl_statistics_record_unique </sql>
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.export.entity.StatisticsRecordUnique">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="UNIQUE_KEY" property="uniqueKey" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, UNIQUE_KEY, CREATE_TIME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.export.entity.StatisticsRecordUnique">
        INSERT INTO <include refid="table" /> (
        UNIQUE_KEY,
        CREATE_TIME
        ) VALUES (
        #{uniqueKey,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        UNIQUE_KEY,
        CREATE_TIME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.uniqueKey,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.export.entity.StatisticsRecordUnique">
        UPDATE <include refid="table" /> SET
        UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.export.entity.StatisticsRecordUnique">
        UPDATE <include refid="table" />
        <set>
            <if test="uniqueKey != null">
                UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="uniqueKey != null and uniqueKey !=''">
            and UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
    </sql>
</mapper>

