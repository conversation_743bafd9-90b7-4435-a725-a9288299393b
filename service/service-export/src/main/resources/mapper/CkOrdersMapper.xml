<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.export.core.dao.mappers.CkOrdersMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.export.entity.CkOrders">
    <!--@mbg.generated-->
    <!--@Table ck_orders-->
    <id column="plat_trx_no" jdbcType="VARCHAR" property="platTrxNo" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="mch_batch_no" jdbcType="VARCHAR" property="mchBatchNo" />
    <result column="plat_batch_no" jdbcType="VARCHAR" property="platBatchNo" />
    <result column="mch_order_no" jdbcType="VARCHAR" property="mchOrderNo" />
    <result column="launch_way" jdbcType="INTEGER" property="launchWay" />
    <result column="employer_no" jdbcType="VARCHAR" property="employerNo" />
    <result column="employer_name" jdbcType="VARCHAR" property="employerName" />
    <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName" />
    <result column="channel_type" jdbcType="INTEGER" property="channelType" />
    <result column="pay_channel_no" jdbcType="VARCHAR" property="payChannelNo" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_code" jdbcType="VARCHAR" property="bankCode" />
    <result column="order_item_net_amount" jdbcType="DECIMAL" property="orderItemNetAmount" />
    <result column="order_item_fee" jdbcType="DECIMAL" property="orderItemFee" />
    <result column="order_item_amount" jdbcType="DECIMAL" property="orderItemAmount" />
    <result column="order_item_status" jdbcType="INTEGER" property="orderItemStatus" />
    <result column="error_code" jdbcType="VARCHAR" property="errorCode" />
    <result column="error_desc" jdbcType="VARCHAR" property="errorDesc" />
    <result column="access_times" jdbcType="INTEGER" property="accessTimes" />
    <result column="is_pass_hangup" jdbcType="TINYINT" property="isPassHangup" />
    <result column="hangup_approval_login_name" jdbcType="VARCHAR" property="hangupApprovalLoginName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="json_str" jdbcType="VARCHAR" property="jsonStr" />
    <result column="appid" jdbcType="VARCHAR" property="appid" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="work_category_code" jdbcType="VARCHAR" property="workCategoryCode" />
    <result column="work_category_name" jdbcType="VARCHAR" property="workCategoryName" />
    <result column="pay_error_code" jdbcType="VARCHAR" property="payErrorCode" />
    <result column="pay_error_desc" jdbcType="VARCHAR" property="payErrorDesc" />
    <result column="product_no" jdbcType="VARCHAR" property="productNo" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="calculate_formula" jdbcType="VARCHAR" property="calculateFormula" />
    <result column="saler_id" jdbcType="BIGINT" property="salerId" />
    <result column="saler_name" jdbcType="VARCHAR" property="salerName" />
    <result column="department_id" jdbcType="BIGINT" property="departmentId" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="order_fee" jdbcType="DECIMAL" property="orderFee" />
    <result column="channel_order_no" jdbcType="VARCHAR" property="channelOrderNo" />
    <result column="channel_trx_no" jdbcType="VARCHAR" property="channelTrxNo" />
    <result column="vendor_fee" jdbcType="DECIMAL" property="vendorFee" />
    <result column="vendor_fee_calculate_formula" jdbcType="VARCHAR" property="vendorFeeCalculateFormula" />
    <result column="sales_fee" jdbcType="DECIMAL" property="salesFee" />
    <result column="sales_profit" jdbcType="DECIMAL" property="salesProfit" />
    <result column="sales_fee_calculate_formula" jdbcType="VARCHAR" property="salesFeeCalculateFormula" />
    <result column="agent_no" jdbcType="VARCHAR" property="agentNo" />
    <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
    <result column="inviter_no" jdbcType="VARCHAR" property="inviterNo" />
    <result column="inviter_name" jdbcType="VARCHAR" property="inviterName" />
    <result column="agent_type" jdbcType="INTEGER" property="agentType" />
    <result column="reward_type" jdbcType="INTEGER" property="rewardType" />
    <result column="agent_cost" jdbcType="DECIMAL" property="agentCost" />
    <result column="agent_profit" jdbcType="DECIMAL" property="agentProfit" />
    <result column="second_agent_cost" jdbcType="DECIMAL" property="secondAgentCost" />
    <result column="second_agent_profit" jdbcType="DECIMAL" property="secondAgentProfit" />
    <result column="agent_fee_calculate_formula" jdbcType="VARCHAR" property="agentFeeCalculateFormula" />
    <result column="agent_fee_rate" jdbcType="DECIMAL" property="agentFeeRate" />
    <result column="self_declare" jdbcType="INTEGER" property="selfDeclare" />
    <result column="second_agent_fee_calculate_formula" jdbcType="VARCHAR" property="secondAgentFeeCalculateFormula" />
    <result column="second_agent_fee_rate" jdbcType="DECIMAL" property="secondAgentFeeRate" />
    <result column="second_self_declare" jdbcType="INTEGER" property="secondSelfDeclare" />
    <result column="exist_agent" jdbcType="INTEGER" property="existAgent" />
    <result column="remit_plat_trx_no" jdbcType="VARCHAR" property="remitPlatTrxNo" />
    <result column="encrypt_key_id" jdbcType="INTEGER" property="encryptKeyId" />
    <result column="receive_name" jdbcType="VARCHAR" property="receiveName" />
    <result column="receive_name_md5" jdbcType="VARCHAR" property="receiveNameMd5" />
    <result column="receive_id_card_no" jdbcType="VARCHAR" property="receiveIdCardNo" />
    <result column="receive_id_card_no_md5" jdbcType="VARCHAR" property="receiveIdCardNoMd5" />
    <result column="receive_account_no" jdbcType="VARCHAR" property="receiveAccountNo" />
    <result column="receive_account_no_md5" jdbcType="VARCHAR" property="receiveAccountNoMd5" />
    <result column="receive_phone_no" jdbcType="VARCHAR" property="receivePhoneNo" />
    <result column="receive_phone_no_md5" jdbcType="VARCHAR" property="receivePhoneNoMd5" />
    <result column="merchant_order_fee" jdbcType="DECIMAL" property="merchantOrderFee" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    plat_trx_no, create_date, create_time, complete_time, mch_batch_no, plat_batch_no,
    mch_order_no, launch_way, employer_no, employer_name, mainstay_no, mainstay_name,
    channel_type, pay_channel_no, channel_name, bank_name, bank_code, order_item_net_amount,
    order_item_fee, order_item_amount, order_item_status, error_code, error_desc, access_times,
    is_pass_hangup, hangup_approval_login_name, remark, json_str, appid, is_delete, work_category_code,
    work_category_name, pay_error_code, pay_error_desc, product_no, product_name, calculate_formula,
    saler_id, saler_name, department_id, department_name, order_fee, channel_order_no,
    channel_trx_no, vendor_fee, vendor_fee_calculate_formula, sales_fee, sales_profit,
    sales_fee_calculate_formula, agent_no, agent_name, inviter_no, inviter_name, agent_type,
    reward_type, agent_cost, agent_profit, second_agent_cost, second_agent_profit, agent_fee_calculate_formula,
    agent_fee_rate, self_declare, second_agent_fee_calculate_formula, second_agent_fee_rate,
    second_self_declare, exist_agent, remit_plat_trx_no, encrypt_key_id, receive_name,
    receive_name_md5, receive_id_card_no, receive_id_card_no_md5, receive_account_no,
    receive_account_no_md5, receive_phone_no, receive_phone_no_md5,merchant_order_fee
  </sql>

  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update ck_orders
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.completeTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="mch_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.mchBatchNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="plat_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.platBatchNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mch_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.mchOrderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="launch_way = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.launchWay,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="employer_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.employerNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="employer_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.employerName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mainstay_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.mainstayNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mainstay_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.mainstayName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.channelType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="pay_channel_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.payChannelNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.channelName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bank_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.bankName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bank_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.bankCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_item_net_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderItemNetAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="order_item_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderItemFee,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="order_item_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderItemAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="order_item_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderItemStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="error_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.errorCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="error_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.errorDesc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="access_times = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.accessTimes,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="is_pass_hangup = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.isPassHangup,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="hangup_approval_login_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.hangupApprovalLoginName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="json_str = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.jsonStr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="appid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.appid,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="is_delete = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="work_category_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.workCategoryCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="work_category_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.workCategoryName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="pay_error_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.payErrorCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="pay_error_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.payErrorDesc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="product_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.productNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="product_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.productName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.calculateFormula,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="saler_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salerId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="saler_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salerName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.departmentId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="department_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.departmentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="order_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderFee,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="channel_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.channelOrderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_trx_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.channelTrxNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="vendor_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.vendorFee,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="vendor_fee_calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.vendorFeeCalculateFormula,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sales_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salesFee,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="sales_profit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salesProfit,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="sales_fee_calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salesFeeCalculateFormula,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="agent_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="agent_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="inviter_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.inviterNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="inviter_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.inviterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="agent_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="reward_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.rewardType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="agent_cost = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentCost,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="agent_profit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentProfit,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="second_agent_cost = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondAgentCost,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="second_agent_profit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondAgentProfit,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="agent_fee_calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentFeeCalculateFormula,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="agent_fee_rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentFeeRate,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="self_declare = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.selfDeclare,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="second_agent_fee_calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondAgentFeeCalculateFormula,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="second_agent_fee_rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondAgentFeeRate,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="second_self_declare = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondSelfDeclare,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="exist_agent = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.existAgent,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="remit_plat_trx_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.remitPlatTrxNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="encrypt_key_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.encryptKeyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="receive_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="receive_name_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveNameMd5,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="receive_id_card_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveIdCardNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="receive_id_card_no_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveIdCardNoMd5,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="receive_account_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveAccountNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="receive_account_no_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveAccountNoMd5,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="receive_phone_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receivePhoneNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="receive_phone_no_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receivePhoneNoMd5,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="merchant_order_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.merchantOrderFee,jdbcType=DECIMAL}
        </foreach>
      </trim>
    </trim>
    where create_date in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.createDate,jdbcType=TIMESTAMP}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update ck_orders
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="complete_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.completeTime != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.completeTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="mch_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mchBatchNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.mchBatchNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="plat_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.platBatchNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.platBatchNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="mch_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mchOrderNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.mchOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="launch_way = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.launchWay != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.launchWay,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="employer_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.employerNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.employerNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="employer_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.employerName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.employerName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="mainstay_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mainstayNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.mainstayNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="mainstay_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mainstayName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.mainstayName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelType != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.channelType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="pay_channel_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payChannelNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.payChannelNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.channelName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="bank_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.bankName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="bank_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankCode != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.bankCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_item_net_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderItemNetAmount != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderItemNetAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_item_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderItemFee != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderItemFee,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_item_amount = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderItemAmount != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderItemAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_item_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderItemStatus != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderItemStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="error_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.errorCode != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.errorCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="error_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.errorDesc != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.errorDesc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="access_times = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accessTimes != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.accessTimes,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_pass_hangup = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isPassHangup != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.isPassHangup,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="hangup_approval_login_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hangupApprovalLoginName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.hangupApprovalLoginName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="json_str = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jsonStr != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.jsonStr,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="appid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appid != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.appid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_delete = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="work_category_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.workCategoryCode != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.workCategoryCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="work_category_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.workCategoryName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.workCategoryName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="pay_error_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payErrorCode != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.payErrorCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="pay_error_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payErrorDesc != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.payErrorDesc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="product_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.productNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="product_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.productName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.calculateFormula != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.calculateFormula,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="saler_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salerId != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salerId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="saler_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salerName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salerName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.departmentId != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.departmentId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="department_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.departmentName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.departmentName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderFee != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.orderFee,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelOrderNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.channelOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_trx_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelTrxNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.channelTrxNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="vendor_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vendorFee != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.vendorFee,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="vendor_fee_calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vendorFeeCalculateFormula != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.vendorFeeCalculateFormula,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sales_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salesFee != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salesFee,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="sales_profit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salesProfit != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salesProfit,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="sales_fee_calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salesFeeCalculateFormula != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.salesFeeCalculateFormula,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="agent_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agentNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="agent_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agentName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="inviter_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inviterNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.inviterNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="inviter_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inviterName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.inviterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="agent_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agentType != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="reward_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.rewardType != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.rewardType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="agent_cost = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agentCost != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentCost,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="agent_profit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agentProfit != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentProfit,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="second_agent_cost = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.secondAgentCost != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondAgentCost,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="second_agent_profit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.secondAgentProfit != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondAgentProfit,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="agent_fee_calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agentFeeCalculateFormula != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentFeeCalculateFormula,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="agent_fee_rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agentFeeRate != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.agentFeeRate,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="self_declare = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.selfDeclare != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.selfDeclare,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="second_agent_fee_calculate_formula = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.secondAgentFeeCalculateFormula != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondAgentFeeCalculateFormula,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="second_agent_fee_rate = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.secondAgentFeeRate != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondAgentFeeRate,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="second_self_declare = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.secondSelfDeclare != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.secondSelfDeclare,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="exist_agent = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.existAgent != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.existAgent,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="remit_plat_trx_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remitPlatTrxNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.remitPlatTrxNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="encrypt_key_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.encryptKeyId != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.encryptKeyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveName != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_name_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveNameMd5 != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveNameMd5,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_id_card_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveIdCardNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveIdCardNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_id_card_no_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveIdCardNoMd5 != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveIdCardNoMd5,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_account_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveAccountNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveAccountNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_account_no_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveAccountNoMd5 != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receiveAccountNoMd5,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_phone_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receivePhoneNo != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receivePhoneNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_phone_no_md5 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receivePhoneNoMd5 != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.receivePhoneNoMd5,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="merchant_order_fee = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.merchantOrderFee != null">
            when create_date = #{item.createDate,jdbcType=TIMESTAMP} then #{item.merchantOrderFee,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
    </trim>
    where create_date in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.createDate,jdbcType=TIMESTAMP}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into ck_orders
    (create_date, plat_trx_no, create_time, complete_time, mch_batch_no, plat_batch_no,
    mch_order_no, launch_way, employer_no, employer_name, mainstay_no, mainstay_name,
    channel_type, pay_channel_no, channel_name, bank_name, bank_code, order_item_net_amount,
    order_item_fee, order_item_amount, order_item_status, error_code, error_desc, access_times,
    is_pass_hangup, hangup_approval_login_name, remark, json_str, appid, is_delete,
    work_category_code, work_category_name, pay_error_code, pay_error_desc, product_no,
    product_name, calculate_formula, saler_id, saler_name, department_id, department_name,
    order_fee, channel_order_no, channel_trx_no, vendor_fee, vendor_fee_calculate_formula,
    sales_fee, sales_profit, sales_fee_calculate_formula, agent_no, agent_name, inviter_no,
    inviter_name, agent_type, reward_type, agent_cost, agent_profit, second_agent_cost,
    second_agent_profit, agent_fee_calculate_formula, agent_fee_rate, self_declare,
    second_agent_fee_calculate_formula, second_agent_fee_rate, second_self_declare,
    exist_agent, remit_plat_trx_no, encrypt_key_id, receive_name, receive_name_md5,
    receive_id_card_no, receive_id_card_no_md5, receive_account_no, receive_account_no_md5,
    receive_phone_no, receive_phone_no_md5, merchant_order_fee)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.createDate,jdbcType=TIMESTAMP}, #{item.platTrxNo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.completeTime,jdbcType=TIMESTAMP}, #{item.mchBatchNo,jdbcType=VARCHAR}, #{item.platBatchNo,jdbcType=VARCHAR},
      #{item.mchOrderNo,jdbcType=VARCHAR}, #{item.launchWay,jdbcType=INTEGER}, #{item.employerNo,jdbcType=VARCHAR},
      #{item.employerName,jdbcType=VARCHAR}, #{item.mainstayNo,jdbcType=VARCHAR}, #{item.mainstayName,jdbcType=VARCHAR},
      #{item.channelType,jdbcType=INTEGER}, #{item.payChannelNo,jdbcType=VARCHAR}, #{item.channelName,jdbcType=VARCHAR},
      #{item.bankName,jdbcType=VARCHAR}, #{item.bankCode,jdbcType=VARCHAR}, #{item.orderItemNetAmount,jdbcType=DECIMAL},
      #{item.orderItemFee,jdbcType=DECIMAL}, #{item.orderItemAmount,jdbcType=DECIMAL},
      #{item.orderItemStatus,jdbcType=INTEGER}, #{item.errorCode,jdbcType=VARCHAR}, #{item.errorDesc,jdbcType=VARCHAR},
      #{item.accessTimes,jdbcType=INTEGER}, #{item.isPassHangup,jdbcType=TINYINT}, #{item.hangupApprovalLoginName,jdbcType=VARCHAR},
      #{item.remark,jdbcType=VARCHAR}, #{item.jsonStr,jdbcType=VARCHAR}, #{item.appid,jdbcType=VARCHAR},
      #{item.isDelete,jdbcType=INTEGER}, #{item.workCategoryCode,jdbcType=VARCHAR}, #{item.workCategoryName,jdbcType=VARCHAR},
      #{item.payErrorCode,jdbcType=VARCHAR}, #{item.payErrorDesc,jdbcType=VARCHAR}, #{item.productNo,jdbcType=VARCHAR},
      #{item.productName,jdbcType=VARCHAR}, #{item.calculateFormula,jdbcType=VARCHAR},
      #{item.salerId,jdbcType=BIGINT}, #{item.salerName,jdbcType=VARCHAR}, #{item.departmentId,jdbcType=BIGINT},
      #{item.departmentName,jdbcType=VARCHAR}, #{item.orderFee,jdbcType=DECIMAL}, #{item.channelOrderNo,jdbcType=VARCHAR},
      #{item.channelTrxNo,jdbcType=VARCHAR}, #{item.vendorFee,jdbcType=DECIMAL}, #{item.vendorFeeCalculateFormula,jdbcType=VARCHAR},
      #{item.salesFee,jdbcType=DECIMAL}, #{item.salesProfit,jdbcType=DECIMAL}, #{item.salesFeeCalculateFormula,jdbcType=VARCHAR},
      #{item.agentNo,jdbcType=VARCHAR}, #{item.agentName,jdbcType=VARCHAR}, #{item.inviterNo,jdbcType=VARCHAR},
      #{item.inviterName,jdbcType=VARCHAR}, #{item.agentType,jdbcType=INTEGER}, #{item.rewardType,jdbcType=INTEGER},
      #{item.agentCost,jdbcType=DECIMAL}, #{item.agentProfit,jdbcType=DECIMAL}, #{item.secondAgentCost,jdbcType=DECIMAL},
      #{item.secondAgentProfit,jdbcType=DECIMAL}, #{item.agentFeeCalculateFormula,jdbcType=VARCHAR},
      #{item.agentFeeRate,jdbcType=DECIMAL}, #{item.selfDeclare,jdbcType=INTEGER}, #{item.secondAgentFeeCalculateFormula,jdbcType=VARCHAR},
      #{item.secondAgentFeeRate,jdbcType=DECIMAL}, #{item.secondSelfDeclare,jdbcType=INTEGER},
      #{item.existAgent,jdbcType=INTEGER}, #{item.remitPlatTrxNo,jdbcType=VARCHAR}, #{item.encryptKeyId,jdbcType=INTEGER},
      #{item.receiveName,jdbcType=VARCHAR}, #{item.receiveNameMd5,jdbcType=VARCHAR},
      #{item.receiveIdCardNo,jdbcType=VARCHAR}, #{item.receiveIdCardNoMd5,jdbcType=VARCHAR},
      #{item.receiveAccountNo,jdbcType=VARCHAR}, #{item.receiveAccountNoMd5,jdbcType=VARCHAR},
      #{item.receivePhoneNo,jdbcType=VARCHAR}, #{item.receivePhoneNoMd5,jdbcType=VARCHAR},
      #{item.merchantOrderFee,jdbcType=DECIMAL})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.zhixianghui.facade.export.entity.CkOrders">
    <!--@mbg.generated-->
    insert into ck_orders
    (create_date, plat_trx_no, create_time, complete_time, mch_batch_no, plat_batch_no,
    mch_order_no, launch_way, employer_no, employer_name, mainstay_no, mainstay_name,
    channel_type, pay_channel_no, channel_name, bank_name, bank_code, order_item_net_amount,
    order_item_fee, order_item_amount, order_item_status, error_code, error_desc, access_times,
    is_pass_hangup, hangup_approval_login_name, remark, json_str, appid, is_delete,
    work_category_code, work_category_name, pay_error_code, pay_error_desc, product_no,
    product_name, calculate_formula, saler_id, saler_name, department_id, department_name,
    order_fee, channel_order_no, channel_trx_no, vendor_fee, vendor_fee_calculate_formula,
    sales_fee, sales_profit, sales_fee_calculate_formula, agent_no, agent_name, inviter_no,
    inviter_name, agent_type, reward_type, agent_cost, agent_profit, second_agent_cost,
    second_agent_profit, agent_fee_calculate_formula, agent_fee_rate, self_declare,
    second_agent_fee_calculate_formula, second_agent_fee_rate, second_self_declare,
    exist_agent, remit_plat_trx_no, encrypt_key_id, receive_name, receive_name_md5,
    receive_id_card_no, receive_id_card_no_md5, receive_account_no, receive_account_no_md5,
    receive_phone_no, receive_phone_no_md5, merchant_order_fee)
    values
    (#{createDate,jdbcType=TIMESTAMP}, #{platTrxNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
    #{completeTime,jdbcType=TIMESTAMP}, #{mchBatchNo,jdbcType=VARCHAR}, #{platBatchNo,jdbcType=VARCHAR},
    #{mchOrderNo,jdbcType=VARCHAR}, #{launchWay,jdbcType=INTEGER}, #{employerNo,jdbcType=VARCHAR},
    #{employerName,jdbcType=VARCHAR}, #{mainstayNo,jdbcType=VARCHAR}, #{mainstayName,jdbcType=VARCHAR},
    #{channelType,jdbcType=INTEGER}, #{payChannelNo,jdbcType=VARCHAR}, #{channelName,jdbcType=VARCHAR},
    #{bankName,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, #{orderItemNetAmount,jdbcType=DECIMAL},
    #{orderItemFee,jdbcType=DECIMAL}, #{orderItemAmount,jdbcType=DECIMAL}, #{orderItemStatus,jdbcType=INTEGER},
    #{errorCode,jdbcType=VARCHAR}, #{errorDesc,jdbcType=VARCHAR}, #{accessTimes,jdbcType=INTEGER},
    #{isPassHangup,jdbcType=TINYINT}, #{hangupApprovalLoginName,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR}, #{jsonStr,jdbcType=VARCHAR}, #{appid,jdbcType=VARCHAR},
    #{isDelete,jdbcType=INTEGER}, #{workCategoryCode,jdbcType=VARCHAR}, #{workCategoryName,jdbcType=VARCHAR},
    #{payErrorCode,jdbcType=VARCHAR}, #{payErrorDesc,jdbcType=VARCHAR}, #{productNo,jdbcType=VARCHAR},
    #{productName,jdbcType=VARCHAR}, #{calculateFormula,jdbcType=VARCHAR}, #{salerId,jdbcType=BIGINT},
    #{salerName,jdbcType=VARCHAR}, #{departmentId,jdbcType=BIGINT}, #{departmentName,jdbcType=VARCHAR},
    #{orderFee,jdbcType=DECIMAL}, #{channelOrderNo,jdbcType=VARCHAR}, #{channelTrxNo,jdbcType=VARCHAR},
    #{vendorFee,jdbcType=DECIMAL}, #{vendorFeeCalculateFormula,jdbcType=VARCHAR}, #{salesFee,jdbcType=DECIMAL},
    #{salesProfit,jdbcType=DECIMAL}, #{salesFeeCalculateFormula,jdbcType=VARCHAR},
    #{agentNo,jdbcType=VARCHAR}, #{agentName,jdbcType=VARCHAR}, #{inviterNo,jdbcType=VARCHAR},
    #{inviterName,jdbcType=VARCHAR}, #{agentType,jdbcType=INTEGER}, #{rewardType,jdbcType=INTEGER},
    #{agentCost,jdbcType=DECIMAL}, #{agentProfit,jdbcType=DECIMAL}, #{secondAgentCost,jdbcType=DECIMAL},
    #{secondAgentProfit,jdbcType=DECIMAL}, #{agentFeeCalculateFormula,jdbcType=VARCHAR},
    #{agentFeeRate,jdbcType=DECIMAL}, #{selfDeclare,jdbcType=INTEGER}, #{secondAgentFeeCalculateFormula,jdbcType=VARCHAR},
    #{secondAgentFeeRate,jdbcType=DECIMAL}, #{secondSelfDeclare,jdbcType=INTEGER},
    #{existAgent,jdbcType=INTEGER}, #{remitPlatTrxNo,jdbcType=VARCHAR}, #{encryptKeyId,jdbcType=INTEGER},
    #{receiveName,jdbcType=VARCHAR}, #{receiveNameMd5,jdbcType=VARCHAR}, #{receiveIdCardNo,jdbcType=VARCHAR},
    #{receiveIdCardNoMd5,jdbcType=VARCHAR}, #{receiveAccountNo,jdbcType=VARCHAR}, #{receiveAccountNoMd5,jdbcType=VARCHAR},
    #{receivePhoneNo,jdbcType=VARCHAR}, #{receivePhoneNoMd5,jdbcType=VARCHAR}, #{merchantOrderFee,jdbcType=DECIMAL}
    )
    on duplicate key update
    create_date = #{createDate,jdbcType=TIMESTAMP},
    plat_trx_no = #{platTrxNo,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    complete_time = #{completeTime,jdbcType=TIMESTAMP},
    mch_batch_no = #{mchBatchNo,jdbcType=VARCHAR},
    plat_batch_no = #{platBatchNo,jdbcType=VARCHAR},
    mch_order_no = #{mchOrderNo,jdbcType=VARCHAR},
    launch_way = #{launchWay,jdbcType=INTEGER},
    employer_no = #{employerNo,jdbcType=VARCHAR},
    employer_name = #{employerName,jdbcType=VARCHAR},
    mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
    mainstay_name = #{mainstayName,jdbcType=VARCHAR},
    channel_type = #{channelType,jdbcType=INTEGER},
    pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},
    channel_name = #{channelName,jdbcType=VARCHAR},
    bank_name = #{bankName,jdbcType=VARCHAR},
    bank_code = #{bankCode,jdbcType=VARCHAR},
    order_item_net_amount = #{orderItemNetAmount,jdbcType=DECIMAL},
    order_item_fee = #{orderItemFee,jdbcType=DECIMAL},
    order_item_amount = #{orderItemAmount,jdbcType=DECIMAL},
    order_item_status = #{orderItemStatus,jdbcType=INTEGER},
    error_code = #{errorCode,jdbcType=VARCHAR},
    error_desc = #{errorDesc,jdbcType=VARCHAR},
    access_times = #{accessTimes,jdbcType=INTEGER},
    is_pass_hangup = #{isPassHangup,jdbcType=TINYINT},
    hangup_approval_login_name = #{hangupApprovalLoginName,jdbcType=VARCHAR},
    remark = #{remark,jdbcType=VARCHAR},
    json_str = #{jsonStr,jdbcType=VARCHAR},
    appid = #{appid,jdbcType=VARCHAR},
    is_delete = #{isDelete,jdbcType=INTEGER},
    work_category_code = #{workCategoryCode,jdbcType=VARCHAR},
    work_category_name = #{workCategoryName,jdbcType=VARCHAR},
    pay_error_code = #{payErrorCode,jdbcType=VARCHAR},
    pay_error_desc = #{payErrorDesc,jdbcType=VARCHAR},
    product_no = #{productNo,jdbcType=VARCHAR},
    product_name = #{productName,jdbcType=VARCHAR},
    calculate_formula = #{calculateFormula,jdbcType=VARCHAR},
    saler_id = #{salerId,jdbcType=BIGINT},
    saler_name = #{salerName,jdbcType=VARCHAR},
    department_id = #{departmentId,jdbcType=BIGINT},
    department_name = #{departmentName,jdbcType=VARCHAR},
    order_fee = #{orderFee,jdbcType=DECIMAL},
    channel_order_no = #{channelOrderNo,jdbcType=VARCHAR},
    channel_trx_no = #{channelTrxNo,jdbcType=VARCHAR},
    vendor_fee = #{vendorFee,jdbcType=DECIMAL},
    vendor_fee_calculate_formula = #{vendorFeeCalculateFormula,jdbcType=VARCHAR},
    sales_fee = #{salesFee,jdbcType=DECIMAL},
    sales_profit = #{salesProfit,jdbcType=DECIMAL},
    sales_fee_calculate_formula = #{salesFeeCalculateFormula,jdbcType=VARCHAR},
    agent_no = #{agentNo,jdbcType=VARCHAR},
    agent_name = #{agentName,jdbcType=VARCHAR},
    inviter_no = #{inviterNo,jdbcType=VARCHAR},
    inviter_name = #{inviterName,jdbcType=VARCHAR},
    agent_type = #{agentType,jdbcType=INTEGER},
    reward_type = #{rewardType,jdbcType=INTEGER},
    agent_cost = #{agentCost,jdbcType=DECIMAL},
    agent_profit = #{agentProfit,jdbcType=DECIMAL},
    second_agent_cost = #{secondAgentCost,jdbcType=DECIMAL},
    second_agent_profit = #{secondAgentProfit,jdbcType=DECIMAL},
    agent_fee_calculate_formula = #{agentFeeCalculateFormula,jdbcType=VARCHAR},
    agent_fee_rate = #{agentFeeRate,jdbcType=DECIMAL},
    self_declare = #{selfDeclare,jdbcType=INTEGER},
    second_agent_fee_calculate_formula = #{secondAgentFeeCalculateFormula,jdbcType=VARCHAR},
    second_agent_fee_rate = #{secondAgentFeeRate,jdbcType=DECIMAL},
    second_self_declare = #{secondSelfDeclare,jdbcType=INTEGER},
    exist_agent = #{existAgent,jdbcType=INTEGER},
    remit_plat_trx_no = #{remitPlatTrxNo,jdbcType=VARCHAR},
    encrypt_key_id = #{encryptKeyId,jdbcType=INTEGER},
    receive_name = #{receiveName,jdbcType=VARCHAR},
    receive_name_md5 = #{receiveNameMd5,jdbcType=VARCHAR},
    receive_id_card_no = #{receiveIdCardNo,jdbcType=VARCHAR},
    receive_id_card_no_md5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
    receive_account_no = #{receiveAccountNo,jdbcType=VARCHAR},
    receive_account_no_md5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
    receive_phone_no = #{receivePhoneNo,jdbcType=VARCHAR},
    receive_phone_no_md5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
    merchant_order_fee = #{merchantOrderFee,jdbcType=DECIMAL}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.zhixianghui.facade.export.entity.CkOrders">
    <!--@mbg.generated-->
    insert into ck_orders
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createDate != null">
        create_date,
      </if>
      <if test="platTrxNo != null and platTrxNo != ''">
        plat_trx_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="mchBatchNo != null and mchBatchNo != ''">
        mch_batch_no,
      </if>
      <if test="platBatchNo != null and platBatchNo != ''">
        plat_batch_no,
      </if>
      <if test="mchOrderNo != null and mchOrderNo != ''">
        mch_order_no,
      </if>
      <if test="launchWay != null">
        launch_way,
      </if>
      <if test="employerNo != null and employerNo != ''">
        employer_no,
      </if>
      <if test="employerName != null and employerName != ''">
        employer_name,
      </if>
      <if test="mainstayNo != null and mainstayNo != ''">
        mainstay_no,
      </if>
      <if test="mainstayName != null and mainstayName != ''">
        mainstay_name,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="payChannelNo != null and payChannelNo != ''">
        pay_channel_no,
      </if>
      <if test="channelName != null and channelName != ''">
        channel_name,
      </if>
      <if test="bankName != null and bankName != ''">
        bank_name,
      </if>
      <if test="bankCode != null and bankCode != ''">
        bank_code,
      </if>
      <if test="orderItemNetAmount != null">
        order_item_net_amount,
      </if>
      <if test="orderItemFee != null">
        order_item_fee,
      </if>
      <if test="orderItemAmount != null">
        order_item_amount,
      </if>
      <if test="orderItemStatus != null">
        order_item_status,
      </if>
      <if test="errorCode != null and errorCode != ''">
        error_code,
      </if>
      <if test="errorDesc != null and errorDesc != ''">
        error_desc,
      </if>
      <if test="accessTimes != null">
        access_times,
      </if>
      <if test="isPassHangup != null">
        is_pass_hangup,
      </if>
      <if test="hangupApprovalLoginName != null and hangupApprovalLoginName != ''">
        hangup_approval_login_name,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
      <if test="jsonStr != null and jsonStr != ''">
        json_str,
      </if>
      <if test="appid != null and appid != ''">
        appid,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="workCategoryCode != null and workCategoryCode != ''">
        work_category_code,
      </if>
      <if test="workCategoryName != null and workCategoryName != ''">
        work_category_name,
      </if>
      <if test="payErrorCode != null and payErrorCode != ''">
        pay_error_code,
      </if>
      <if test="payErrorDesc != null and payErrorDesc != ''">
        pay_error_desc,
      </if>
      <if test="productNo != null and productNo != ''">
        product_no,
      </if>
      <if test="productName != null and productName != ''">
        product_name,
      </if>
      <if test="calculateFormula != null and calculateFormula != ''">
        calculate_formula,
      </if>
      <if test="salerId != null">
        saler_id,
      </if>
      <if test="salerName != null and salerName != ''">
        saler_name,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="departmentName != null and departmentName != ''">
        department_name,
      </if>
      <if test="orderFee != null">
        order_fee,
      </if>
      <if test="channelOrderNo != null and channelOrderNo != ''">
        channel_order_no,
      </if>
      <if test="channelTrxNo != null and channelTrxNo != ''">
        channel_trx_no,
      </if>
      <if test="vendorFee != null">
        vendor_fee,
      </if>
      <if test="vendorFeeCalculateFormula != null and vendorFeeCalculateFormula != ''">
        vendor_fee_calculate_formula,
      </if>
      <if test="salesFee != null">
        sales_fee,
      </if>
      <if test="salesProfit != null">
        sales_profit,
      </if>
      <if test="salesFeeCalculateFormula != null and salesFeeCalculateFormula != ''">
        sales_fee_calculate_formula,
      </if>
      <if test="agentNo != null and agentNo != ''">
        agent_no,
      </if>
      <if test="agentName != null and agentName != ''">
        agent_name,
      </if>
      <if test="inviterNo != null and inviterNo != ''">
        inviter_no,
      </if>
      <if test="inviterName != null and inviterName != ''">
        inviter_name,
      </if>
      <if test="agentType != null">
        agent_type,
      </if>
      <if test="rewardType != null">
        reward_type,
      </if>
      <if test="agentCost != null">
        agent_cost,
      </if>
      <if test="agentProfit != null">
        agent_profit,
      </if>
      <if test="secondAgentCost != null">
        second_agent_cost,
      </if>
      <if test="secondAgentProfit != null">
        second_agent_profit,
      </if>
      <if test="agentFeeCalculateFormula != null and agentFeeCalculateFormula != ''">
        agent_fee_calculate_formula,
      </if>
      <if test="agentFeeRate != null">
        agent_fee_rate,
      </if>
      <if test="selfDeclare != null">
        self_declare,
      </if>
      <if test="secondAgentFeeCalculateFormula != null and secondAgentFeeCalculateFormula != ''">
        second_agent_fee_calculate_formula,
      </if>
      <if test="secondAgentFeeRate != null">
        second_agent_fee_rate,
      </if>
      <if test="secondSelfDeclare != null">
        second_self_declare,
      </if>
      <if test="existAgent != null">
        exist_agent,
      </if>
      <if test="remitPlatTrxNo != null and remitPlatTrxNo != ''">
        remit_plat_trx_no,
      </if>
      <if test="encryptKeyId != null">
        encrypt_key_id,
      </if>
      <if test="receiveName != null and receiveName != ''">
        receive_name,
      </if>
      <if test="receiveNameMd5 != null and receiveNameMd5 != ''">
        receive_name_md5,
      </if>
      <if test="receiveIdCardNo != null and receiveIdCardNo != ''">
        receive_id_card_no,
      </if>
      <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 != ''">
        receive_id_card_no_md5,
      </if>
      <if test="receiveAccountNo != null and receiveAccountNo != ''">
        receive_account_no,
      </if>
      <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 != ''">
        receive_account_no_md5,
      </if>
      <if test="receivePhoneNo != null and receivePhoneNo != ''">
        receive_phone_no,
      </if>
      <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 != ''">
        receive_phone_no_md5,
      </if>
      <if test="merchantOrderFee != null">
        merchant_order_fee,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="platTrxNo != null and platTrxNo != ''">
        #{platTrxNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mchBatchNo != null and mchBatchNo != ''">
        #{mchBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="platBatchNo != null and platBatchNo != ''">
        #{platBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="mchOrderNo != null and mchOrderNo != ''">
        #{mchOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="launchWay != null">
        #{launchWay,jdbcType=INTEGER},
      </if>
      <if test="employerNo != null and employerNo != ''">
        #{employerNo,jdbcType=VARCHAR},
      </if>
      <if test="employerName != null and employerName != ''">
        #{employerName,jdbcType=VARCHAR},
      </if>
      <if test="mainstayNo != null and mainstayNo != ''">
        #{mainstayNo,jdbcType=VARCHAR},
      </if>
      <if test="mainstayName != null and mainstayName != ''">
        #{mainstayName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=INTEGER},
      </if>
      <if test="payChannelNo != null and payChannelNo != ''">
        #{payChannelNo,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null and bankName != ''">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null and bankCode != ''">
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="orderItemNetAmount != null">
        #{orderItemNetAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderItemFee != null">
        #{orderItemFee,jdbcType=DECIMAL},
      </if>
      <if test="orderItemAmount != null">
        #{orderItemAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderItemStatus != null">
        #{orderItemStatus,jdbcType=INTEGER},
      </if>
      <if test="errorCode != null and errorCode != ''">
        #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorDesc != null and errorDesc != ''">
        #{errorDesc,jdbcType=VARCHAR},
      </if>
      <if test="accessTimes != null">
        #{accessTimes,jdbcType=INTEGER},
      </if>
      <if test="isPassHangup != null">
        #{isPassHangup,jdbcType=TINYINT},
      </if>
      <if test="hangupApprovalLoginName != null and hangupApprovalLoginName != ''">
        #{hangupApprovalLoginName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="jsonStr != null and jsonStr != ''">
        #{jsonStr,jdbcType=VARCHAR},
      </if>
      <if test="appid != null and appid != ''">
        #{appid,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="workCategoryCode != null and workCategoryCode != ''">
        #{workCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="workCategoryName != null and workCategoryName != ''">
        #{workCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="payErrorCode != null and payErrorCode != ''">
        #{payErrorCode,jdbcType=VARCHAR},
      </if>
      <if test="payErrorDesc != null and payErrorDesc != ''">
        #{payErrorDesc,jdbcType=VARCHAR},
      </if>
      <if test="productNo != null and productNo != ''">
        #{productNo,jdbcType=VARCHAR},
      </if>
      <if test="productName != null and productName != ''">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="calculateFormula != null and calculateFormula != ''">
        #{calculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="salerId != null">
        #{salerId,jdbcType=BIGINT},
      </if>
      <if test="salerName != null and salerName != ''">
        #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null and departmentName != ''">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="orderFee != null">
        #{orderFee,jdbcType=DECIMAL},
      </if>
      <if test="channelOrderNo != null and channelOrderNo != ''">
        #{channelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTrxNo != null and channelTrxNo != ''">
        #{channelTrxNo,jdbcType=VARCHAR},
      </if>
      <if test="vendorFee != null">
        #{vendorFee,jdbcType=DECIMAL},
      </if>
      <if test="vendorFeeCalculateFormula != null and vendorFeeCalculateFormula != ''">
        #{vendorFeeCalculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="salesFee != null">
        #{salesFee,jdbcType=DECIMAL},
      </if>
      <if test="salesProfit != null">
        #{salesProfit,jdbcType=DECIMAL},
      </if>
      <if test="salesFeeCalculateFormula != null and salesFeeCalculateFormula != ''">
        #{salesFeeCalculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="agentNo != null and agentNo != ''">
        #{agentNo,jdbcType=VARCHAR},
      </if>
      <if test="agentName != null and agentName != ''">
        #{agentName,jdbcType=VARCHAR},
      </if>
      <if test="inviterNo != null and inviterNo != ''">
        #{inviterNo,jdbcType=VARCHAR},
      </if>
      <if test="inviterName != null and inviterName != ''">
        #{inviterName,jdbcType=VARCHAR},
      </if>
      <if test="agentType != null">
        #{agentType,jdbcType=INTEGER},
      </if>
      <if test="rewardType != null">
        #{rewardType,jdbcType=INTEGER},
      </if>
      <if test="agentCost != null">
        #{agentCost,jdbcType=DECIMAL},
      </if>
      <if test="agentProfit != null">
        #{agentProfit,jdbcType=DECIMAL},
      </if>
      <if test="secondAgentCost != null">
        #{secondAgentCost,jdbcType=DECIMAL},
      </if>
      <if test="secondAgentProfit != null">
        #{secondAgentProfit,jdbcType=DECIMAL},
      </if>
      <if test="agentFeeCalculateFormula != null and agentFeeCalculateFormula != ''">
        #{agentFeeCalculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="agentFeeRate != null">
        #{agentFeeRate,jdbcType=DECIMAL},
      </if>
      <if test="selfDeclare != null">
        #{selfDeclare,jdbcType=INTEGER},
      </if>
      <if test="secondAgentFeeCalculateFormula != null and secondAgentFeeCalculateFormula != ''">
        #{secondAgentFeeCalculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="secondAgentFeeRate != null">
        #{secondAgentFeeRate,jdbcType=DECIMAL},
      </if>
      <if test="secondSelfDeclare != null">
        #{secondSelfDeclare,jdbcType=INTEGER},
      </if>
      <if test="existAgent != null">
        #{existAgent,jdbcType=INTEGER},
      </if>
      <if test="remitPlatTrxNo != null and remitPlatTrxNo != ''">
        #{remitPlatTrxNo,jdbcType=VARCHAR},
      </if>
      <if test="encryptKeyId != null">
        #{encryptKeyId,jdbcType=INTEGER},
      </if>
      <if test="receiveName != null and receiveName != ''">
        #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receiveNameMd5 != null and receiveNameMd5 != ''">
        #{receiveNameMd5,jdbcType=VARCHAR},
      </if>
      <if test="receiveIdCardNo != null and receiveIdCardNo != ''">
        #{receiveIdCardNo,jdbcType=VARCHAR},
      </if>
      <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 != ''">
        #{receiveIdCardNoMd5,jdbcType=VARCHAR},
      </if>
      <if test="receiveAccountNo != null and receiveAccountNo != ''">
        #{receiveAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 != ''">
        #{receiveAccountNoMd5,jdbcType=VARCHAR},
      </if>
      <if test="receivePhoneNo != null and receivePhoneNo != ''">
        #{receivePhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 != ''">
        #{receivePhoneNoMd5,jdbcType=VARCHAR},
      </if>
      <if test="merchantOrderFee != null">
        #{merchantOrderFee,jdbcType=DECIMAL},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="platTrxNo != null and platTrxNo != ''">
        plat_trx_no = #{platTrxNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mchBatchNo != null and mchBatchNo != ''">
        mch_batch_no = #{mchBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="platBatchNo != null and platBatchNo != ''">
        plat_batch_no = #{platBatchNo,jdbcType=VARCHAR},
      </if>
      <if test="mchOrderNo != null and mchOrderNo != ''">
        mch_order_no = #{mchOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="launchWay != null">
        launch_way = #{launchWay,jdbcType=INTEGER},
      </if>
      <if test="employerNo != null and employerNo != ''">
        employer_no = #{employerNo,jdbcType=VARCHAR},
      </if>
      <if test="employerName != null and employerName != ''">
        employer_name = #{employerName,jdbcType=VARCHAR},
      </if>
      <if test="mainstayNo != null and mainstayNo != ''">
        mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
      </if>
      <if test="mainstayName != null and mainstayName != ''">
        mainstay_name = #{mainstayName,jdbcType=VARCHAR},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=INTEGER},
      </if>
      <if test="payChannelNo != null and payChannelNo != ''">
        pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null and bankName != ''">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null and bankCode != ''">
        bank_code = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="orderItemNetAmount != null">
        order_item_net_amount = #{orderItemNetAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderItemFee != null">
        order_item_fee = #{orderItemFee,jdbcType=DECIMAL},
      </if>
      <if test="orderItemAmount != null">
        order_item_amount = #{orderItemAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderItemStatus != null">
        order_item_status = #{orderItemStatus,jdbcType=INTEGER},
      </if>
      <if test="errorCode != null and errorCode != ''">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
      <if test="errorDesc != null and errorDesc != ''">
        error_desc = #{errorDesc,jdbcType=VARCHAR},
      </if>
      <if test="accessTimes != null">
        access_times = #{accessTimes,jdbcType=INTEGER},
      </if>
      <if test="isPassHangup != null">
        is_pass_hangup = #{isPassHangup,jdbcType=TINYINT},
      </if>
      <if test="hangupApprovalLoginName != null and hangupApprovalLoginName != ''">
        hangup_approval_login_name = #{hangupApprovalLoginName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="jsonStr != null and jsonStr != ''">
        json_str = #{jsonStr,jdbcType=VARCHAR},
      </if>
      <if test="appid != null and appid != ''">
        appid = #{appid,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="workCategoryCode != null and workCategoryCode != ''">
        work_category_code = #{workCategoryCode,jdbcType=VARCHAR},
      </if>
      <if test="workCategoryName != null and workCategoryName != ''">
        work_category_name = #{workCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="payErrorCode != null and payErrorCode != ''">
        pay_error_code = #{payErrorCode,jdbcType=VARCHAR},
      </if>
      <if test="payErrorDesc != null and payErrorDesc != ''">
        pay_error_desc = #{payErrorDesc,jdbcType=VARCHAR},
      </if>
      <if test="productNo != null and productNo != ''">
        product_no = #{productNo,jdbcType=VARCHAR},
      </if>
      <if test="productName != null and productName != ''">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="calculateFormula != null and calculateFormula != ''">
        calculate_formula = #{calculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="salerId != null">
        saler_id = #{salerId,jdbcType=BIGINT},
      </if>
      <if test="salerName != null and salerName != ''">
        saler_name = #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=BIGINT},
      </if>
      <if test="departmentName != null and departmentName != ''">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="orderFee != null">
        order_fee = #{orderFee,jdbcType=DECIMAL},
      </if>
      <if test="channelOrderNo != null and channelOrderNo != ''">
        channel_order_no = #{channelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTrxNo != null and channelTrxNo != ''">
        channel_trx_no = #{channelTrxNo,jdbcType=VARCHAR},
      </if>
      <if test="vendorFee != null">
        vendor_fee = #{vendorFee,jdbcType=DECIMAL},
      </if>
      <if test="vendorFeeCalculateFormula != null and vendorFeeCalculateFormula != ''">
        vendor_fee_calculate_formula = #{vendorFeeCalculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="salesFee != null">
        sales_fee = #{salesFee,jdbcType=DECIMAL},
      </if>
      <if test="salesProfit != null">
        sales_profit = #{salesProfit,jdbcType=DECIMAL},
      </if>
      <if test="salesFeeCalculateFormula != null and salesFeeCalculateFormula != ''">
        sales_fee_calculate_formula = #{salesFeeCalculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="agentNo != null and agentNo != ''">
        agent_no = #{agentNo,jdbcType=VARCHAR},
      </if>
      <if test="agentName != null and agentName != ''">
        agent_name = #{agentName,jdbcType=VARCHAR},
      </if>
      <if test="inviterNo != null and inviterNo != ''">
        inviter_no = #{inviterNo,jdbcType=VARCHAR},
      </if>
      <if test="inviterName != null and inviterName != ''">
        inviter_name = #{inviterName,jdbcType=VARCHAR},
      </if>
      <if test="agentType != null">
        agent_type = #{agentType,jdbcType=INTEGER},
      </if>
      <if test="rewardType != null">
        reward_type = #{rewardType,jdbcType=INTEGER},
      </if>
      <if test="agentCost != null">
        agent_cost = #{agentCost,jdbcType=DECIMAL},
      </if>
      <if test="agentProfit != null">
        agent_profit = #{agentProfit,jdbcType=DECIMAL},
      </if>
      <if test="secondAgentCost != null">
        second_agent_cost = #{secondAgentCost,jdbcType=DECIMAL},
      </if>
      <if test="secondAgentProfit != null">
        second_agent_profit = #{secondAgentProfit,jdbcType=DECIMAL},
      </if>
      <if test="agentFeeCalculateFormula != null and agentFeeCalculateFormula != ''">
        agent_fee_calculate_formula = #{agentFeeCalculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="agentFeeRate != null">
        agent_fee_rate = #{agentFeeRate,jdbcType=DECIMAL},
      </if>
      <if test="selfDeclare != null">
        self_declare = #{selfDeclare,jdbcType=INTEGER},
      </if>
      <if test="secondAgentFeeCalculateFormula != null and secondAgentFeeCalculateFormula != ''">
        second_agent_fee_calculate_formula = #{secondAgentFeeCalculateFormula,jdbcType=VARCHAR},
      </if>
      <if test="secondAgentFeeRate != null">
        second_agent_fee_rate = #{secondAgentFeeRate,jdbcType=DECIMAL},
      </if>
      <if test="secondSelfDeclare != null">
        second_self_declare = #{secondSelfDeclare,jdbcType=INTEGER},
      </if>
      <if test="existAgent != null">
        exist_agent = #{existAgent,jdbcType=INTEGER},
      </if>
      <if test="remitPlatTrxNo != null and remitPlatTrxNo != ''">
        remit_plat_trx_no = #{remitPlatTrxNo,jdbcType=VARCHAR},
      </if>
      <if test="encryptKeyId != null">
        encrypt_key_id = #{encryptKeyId,jdbcType=INTEGER},
      </if>
      <if test="receiveName != null and receiveName != ''">
        receive_name = #{receiveName,jdbcType=VARCHAR},
      </if>
      <if test="receiveNameMd5 != null and receiveNameMd5 != ''">
        receive_name_md5 = #{receiveNameMd5,jdbcType=VARCHAR},
      </if>
      <if test="receiveIdCardNo != null and receiveIdCardNo != ''">
        receive_id_card_no = #{receiveIdCardNo,jdbcType=VARCHAR},
      </if>
      <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 != ''">
        receive_id_card_no_md5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
      </if>
      <if test="receiveAccountNo != null and receiveAccountNo != ''">
        receive_account_no = #{receiveAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 != ''">
        receive_account_no_md5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
      </if>
      <if test="receivePhoneNo != null and receivePhoneNo != ''">
        receive_phone_no = #{receivePhoneNo,jdbcType=VARCHAR},
      </if>
      <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 != ''">
        receive_phone_no_md5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
      </if>
      <if test="merchantOrderFee != null">
        merchant_order_fee = #{merchantOrderFee,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>

  <select id="salerOrdersStatistics" parameterType="java.util.Map" resultType="java.util.Map">
    select
      sum(co.order_item_net_amount) totalNetAmount,
      avg(co.order_item_net_amount) avgNetAmount,
      sum(order_fee) totalMerchantFee,
      count(distinct co.receive_id_card_no_md5) userCount,
      count(plat_trx_no) totalCount
    from
    ck_orders co
    <where>
      <if test="salerIds != null and salerIds.size() > 0">
        and saler_id in
        <foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
      </if>
      <if test="tradeTimeBegin != null">
        and complete_time <![CDATA[>=]]>  #{tradeTimeBegin,jdbcType=TIMESTAMP}
      </if>
      <if test="tradeTimeEnd != null">
        and complete_time <![CDATA[ < ]]> #{tradeTimeEnd,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>

  <select id="listSalerOrdersGroupedByMch" parameterType="java.util.Map" resultType="java.util.Map">
    select
      employer_no employerNo,
      employer_name employerName,
      sum(co.order_item_net_amount) totalNetAmount,
      sum(order_fee) totalMerchantFee,
      count(distinct co.receive_id_card_no_md5) userCount,
      count(plat_trx_no) totalCount
    from
      ck_orders co
    <where>
      <if test="salerIds != null and salerIds.size() > 0">
        and saler_id in
        <foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
      </if>
      <if test="tradeTimeBegin != null">
        and complete_time <![CDATA[>=]]>  #{tradeTimeBegin,jdbcType=TIMESTAMP}
      </if>
      <if test="tradeTimeEnd != null">
        and complete_time <![CDATA[ < ]]> #{tradeTimeEnd,jdbcType=TIMESTAMP}
      </if>
    </where>
    group by
      co.employer_no
    <choose>
      <when test="sortColumns != null and sortColumns !='' ">
        <![CDATA[ ORDER BY ${sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY totalNetAmount DESC ]]>
      </otherwise>
    </choose>
  </select>

  <select id="listSalerOrdersGroupedByMchDaily" parameterType="java.util.Map" resultType="java.util.Map">
    select
      date_format(complete_time ,'%Y-%m-%d') as date,
      employer_no employerNo,
      employer_name employerName,
      sum(co.order_item_net_amount) totalNetAmount,
      avg(co.order_item_net_amount) avgNetAmount,
      sum(order_fee) totalMerchantFee,
      count(distinct co.receive_id_card_no_md5) userCount,
      count(plat_trx_no) totalCount
    from
    ck_orders co
    <where>
      <if test="salerIds != null and salerIds.size() > 0">
        and saler_id in
        <foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
      </if>
      <if test="mchNos !=null">
        and employer_no in
        <foreach collection="mchNos" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="tradeTimeBegin != null">
        and complete_time <![CDATA[>=]]>  #{tradeTimeBegin,jdbcType=TIMESTAMP}
      </if>
      <if test="tradeTimeEnd != null">
        and complete_time <![CDATA[ < ]]> #{tradeTimeEnd,jdbcType=TIMESTAMP}
      </if>
    </where>
    group by
    date,employerNo,employerName
    <choose>
      <when test="sortColumns != null and sortColumns !='' ">
        <![CDATA[ ORDER BY ${sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY  date DESC]]>
      </otherwise>
    </choose>
  </select>

  <select id="listSalerOrdersGroupedByMchMonthly" parameterType="java.util.Map" resultType="java.util.Map">
    select
      date_format(complete_time ,'%Y-%m') as date,
      employer_no employerNo,
      employer_name employerName,
      sum(co.order_item_net_amount) totalNetAmount,
      avg(co.order_item_net_amount) avgNetAmount,
      sum(order_fee) totalMerchantFee,
      count(distinct co.receive_id_card_no_md5) userCount,
      count(plat_trx_no) totalCount
    from
    ck_orders co
    <where>
      <if test="salerIds != null and salerIds.size() > 0">
        and saler_id in
        <foreach collection="salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
      </if>
      <if test="mchNos !=null">
        and employer_no in
        <foreach collection="mchNos" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="tradeTimeBegin != null">
        and complete_time <![CDATA[>=]]>  #{tradeTimeBegin,jdbcType=TIMESTAMP}
      </if>
      <if test="tradeTimeEnd != null">
        and complete_time <![CDATA[ < ]]> #{tradeTimeEnd,jdbcType=TIMESTAMP}
      </if>
    </where>
    group by
    date,employerNo,employerName
    <choose>
      <when test="sortColumns != null and sortColumns !='' ">
        <![CDATA[ ORDER BY ${sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY date DESC ]]>
      </otherwise>
    </choose>
  </select>

  <select id="salerstatistics" parameterType="java.util.Map" resultType="java.util.Map">
    select
      employer_no employerNo,
      employer_name employerName,
      mainstay_no mainstayNo,
      mainstay_name mainstayName,
      product_no productNo,
      product_name productName,
      saler_id salerId,
      department_id departmentId,
      department_name departmentName,
      saler_name salerName,
      sum(co.order_item_net_amount) totalNetAmount,
      sum(order_fee) totalMerchantFee,
      sum(sales_fee) totalSalesFee,
      sum(sales_profit) salesProfit,
      count(distinct co.receive_id_card_no_md5) userCount,
      count(plat_trx_no)
    from
      ck_orders co
    <where>
      <if test="param.salerIds != null and param.salerIds.size() > 0">
        and saler_id in
        <foreach collection="param.salerIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
      </if>
      <if test="param.mchNos !=null">
        and employer_no in
        <foreach collection="param.mchNos" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="param.employerName !=null  and param.employerName !=''">
        and employer_name like CONCAT(CONCAT('%', #{param.employerName,jdbcType=VARCHAR}), '%')
      </if>
      <if test="param.mainstayNo !=null  and param.mainstayNo !=''">
        and mainstay_no  = #{param.mainstayNo,jdbcType=VARCHAR}
      </if>
      <if test="param.mainstayName !=null  and param.mainstayName !=''">
        and mainstay_name like CONCAT(CONCAT('%', #{param.mainstayName,jdbcType=VARCHAR}), '%')
      </if>
      <if test="param.productNo !=null  and param.productNo !=''">
        and product_no  = #{param.productNo,jdbcType=VARCHAR}
      </if>
      <if test="param.agentExists !=null">
        and exist_agent  = #{param.agentExists,jdbcType=INTEGER}
      </if>
      <if test="param.tradeTimeBegin != null">
        and complete_time <![CDATA[>=]]>  #{param.tradeTimeBegin,jdbcType=TIMESTAMP}
      </if>
      <if test="param.tradeTimeEnd != null">
        and complete_time <![CDATA[ < ]]> #{param.tradeTimeEnd,jdbcType=TIMESTAMP}
      </if>
    </where>
    group by
      co.employer_no ,co.saler_id ,mainstay_no ,product_no,department_id
    <choose>
      <when test="param.sortColumns != null and param.sortColumns !='' ">
        <![CDATA[ ORDER BY ${param.sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY totalNetAmount DESC ]]>
      </otherwise>
    </choose>
  </select>
  <select id="coreIndexStatistics" parameterType="java.util.Map" resultType="java.util.Map">
    select
      sum(t.order_item_net_amount) as totalPayAmount,
      avg(t.order_item_net_amount) as avgPayAmount,
      count(plat_trx_no) as orderCount,
      count(distinct receive_id_card_no_md5) as receivedUserCount
    from ck_orders t
    <where>
      <if test="completeBeginDate != null and completeEndDate != null" >
        and complete_time <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
        and complete_time <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
      </if>
      <!--  分表字段区间  -->
      <if test="mchNos !=null">
        and employer_no in
        <foreach collection="mchNos" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="coreIndexDailyDetail" parameterType="java.util.Map" resultType="java.util.Map">
    select
    date_format(complete_time,'%Y-%m-%d') as date,
    sum(t.order_item_net_amount) as totalPayAmount,
    avg(t.order_item_net_amount) as avgPayAmount,
    count(plat_trx_no) as orderCount,
    count(distinct receive_id_card_no_md5) as receivedUserCount
    from ck_orders t
    <where>
      <if test="completeBeginDate != null and completeEndDate != null" >
        and complete_time <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
        and complete_time <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
      </if>
      <if test="mchNos !=null">
        and employer_no in
        <foreach collection="mchNos" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    group by date
  </select>

  <select id="coreIndexDetailMonthly" parameterType="java.util.Map" resultType="java.util.Map">
    select
    date_format(complete_time ,'%Y-%m') as date,
    sum(t.order_item_net_amount) as totalPayAmount,
    avg(t.order_item_net_amount) as avgPayAmount,
    count(plat_trx_no) as orderCount,
    count(distinct receive_id_card_no_md5) as receivedUserCount
    from ck_orders t
    <where>
      <if test="completeBeginDate != null and completeEndDate != null" >
        and complete_time <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
        and complete_time <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
      </if>
      <!--  分表字段区间  -->
      <if test="mchNos !=null">
        and employer_no in
        <foreach collection="mchNos" index="index" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    group by date
  </select>

  <select id="countOrderAmount" parameterType="java.util.Map" resultType="java.util.Map">
    select IFNULL(sum(order_item_net_amount),0) as orderAmount,COUNT(1) as orderCount
    from ck_orders t
    <where>
      <if test="createDateStart != null and createDateEnd != null">
        and create_date <![CDATA[ >= ]]> #{createDateStart,jdbcType=TIMESTAMP}
        and create_date <![CDATA[ <= ]]> #{createDateEnd,jdbcType=TIMESTAMP}
      </if>
      <if test="completeTimeStart != null and completeTimeEnd != null" >
        and complete_time <![CDATA[ >= ]]> #{completeTimeStart,jdbcType=TIMESTAMP}
        and complete_time <![CDATA[ <= ]]> #{completeTimeEnd,jdbcType=TIMESTAMP}
      </if>
      <if test="employerNo != null and employerNo != ''">
        and employer_no = #{employerNo}
      </if>
    </where>
  </select>
</mapper>
