<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.export.entity.ExportRecord">
	<sql id="table"> tbl_export_record </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.export.entity.ExportRecord">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="FILE_NO" property="fileNo" jdbcType="VARCHAR"/>
		<result column="OPERATOR_LOGIN_NAME" property="operatorLoginName" jdbcType="VARCHAR"/>
		<result column="SYSTEM_TYPE" property="systemType" jdbcType="SMALLINT"/>
		<result column="REPORT_TYPE" property="reportType" jdbcType="SMALLINT"/>
		<result column="EXPORT_STATUS" property="exportStatus" jdbcType="SMALLINT"/>
		<result column="MCH_NO" property="mchNo" jdbcType="VARCHAR"/>
		<result column="ERR_DESC" property="errDesc" jdbcType="VARCHAR"/>
		<result column="PARAM_JSON" property="paramJson" jdbcType="OTHER"/>
		<result column="FIELD_JSON" property="fieldJson" jdbcType="OTHER"/>
		<result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR"/>
		<result column="FILE_NAME" property="fileName" jdbcType="VARCHAR"/>
		<result column="EMAIL_NOTIFY" property="emailNotify" jdbcType="INTEGER"/>
		<result column="EMAIL" property="email" jdbcType="VARCHAR"/>
		<result column="EMAIL_SUBJECT" property="emailSubject" jdbcType="VARCHAR"/>
		<result column="EMAIL_CONTENT" property="emailContent" jdbcType="VARCHAR"/>
		<result column="DIRECTION" property="direction" jdbcType="TINYINT"/>
		<result column="DEEP_PAGE" property="deepPage" jdbcType="BOOLEAN"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		VERSION,
		CREATE_TIME,
		UPDATE_TIME,
		FILE_NO,
		OPERATOR_LOGIN_NAME,
		SYSTEM_TYPE,
		REPORT_TYPE,
		EXPORT_STATUS,
		MCH_NO,
		ERR_DESC,
		PARAM_JSON,
		FIELD_JSON,
		FILE_URL,
		FILE_NAME,
		EMAIL_NOTIFY,
		EMAIL,
		EMAIL_SUBJECT,
		EMAIL_CONTENT,
		DIRECTION,
		DEEP_PAGE
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.export.entity.ExportRecord">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	FILE_NO ,
        	OPERATOR_LOGIN_NAME ,
        	SYSTEM_TYPE ,
        	REPORT_TYPE ,
        	EXPORT_STATUS ,
        	MCH_NO ,
			ERR_DESC ,
        	PARAM_JSON ,
        	FIELD_JSON ,
        	FILE_URL ,
        	FILE_NAME,
			EMAIL_NOTIFY,
			EMAIL,
			EMAIL_SUBJECT,
			EMAIL_CONTENT,
			DIRECTION,
			DEEP_PAGE
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{fileNo,jdbcType=VARCHAR},
			#{operatorLoginName,jdbcType=VARCHAR},
			#{systemType,jdbcType=SMALLINT},
			#{reportType,jdbcType=SMALLINT},
			#{exportStatus,jdbcType=SMALLINT},
			#{mchNo,jdbcType=VARCHAR},
			#{errDesc,jdbcType=VARCHAR},
			#{paramJson,jdbcType=OTHER},
			#{fieldJson,jdbcType=OTHER},
			#{fileUrl,jdbcType=VARCHAR},
			#{fileName,jdbcType=VARCHAR},
			#{emailNotify,jdbcType=INTEGER},
			#{email,jdbcType=VARCHAR},
			#{emailSubject,jdbcType=VARCHAR},
			#{emailContent,jdbcType=VARCHAR},
			#{direction,jdbcType=TINYINT},
			#{deepPage,jdbcType=BOOLEAN}
        )
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.export.entity.ExportRecord">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			FILE_NO = #{fileNo,jdbcType=VARCHAR},
			OPERATOR_LOGIN_NAME = #{operatorLoginName,jdbcType=VARCHAR},
			SYSTEM_TYPE = #{systemType,jdbcType=SMALLINT},
			REPORT_TYPE = #{reportType,jdbcType=SMALLINT},
			EXPORT_STATUS = #{exportStatus,jdbcType=SMALLINT},
			MCH_NO = #{mchNo,jdbcType=VARCHAR},
			ERR_DESC = #{errDesc,jdbcType=VARCHAR},
			PARAM_JSON = #{paramJson,jdbcType=OTHER},
			FIELD_JSON = #{fieldJson,jdbcType=OTHER},
			FILE_URL = #{fileUrl,jdbcType=VARCHAR},
			FILE_NAME = #{fileName,jdbcType=VARCHAR},
			EMAIL_NOTIFY=#{emailNotify,jdbcType=INTEGER},
			EMAIL=#{email,jdbcType=VARCHAR},
			EMAIL_SUBJECT=#{emailSubject,jdbcType=VARCHAR},
			EMAIL_CONTENT=#{emailContent,jdbcType=VARCHAR},
			DIRECTION=#{direction,jdbcType=TINYINT},
			DEEP_PAGE=#{deepPage,jdbcType=BOOLEAN}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		order by id desc
	</select>

	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		order by id desc
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE ID = #{id,jdbcType=BIGINT}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="fileNo != null and fileNo !=''">
			and FILE_NO = #{fileNo,jdbcType=VARCHAR}
		</if>
		<if test="operatorLoginName != null and operatorLoginName !=''">
			and OPERATOR_LOGIN_NAME = #{operatorLoginName,jdbcType=VARCHAR}
		</if>
		<if test="systemType != null">
			and SYSTEM_TYPE = #{systemType,jdbcType=SMALLINT}
		</if>
		<if test="reportType != null">
			and REPORT_TYPE = #{reportType,jdbcType=SMALLINT}
		</if>
		<if test="exportStatus != null">
			and EXPORT_STATUS = #{exportStatus,jdbcType=SMALLINT}
		</if>
		<if test="mchNo != null and mchNo !=''">
			and MCH_NO = #{mchNo,jdbcType=VARCHAR}
		</if>
		<if test="paramJson != null">
			and PARAM_JSON = #{paramJson,jdbcType=OTHER}
		</if>
		<if test="fieldJson != null">
			and FIELD_JSON = #{fieldJson,jdbcType=OTHER}
		</if>
		<if test="fileUrl != null and fileUrl !=''">
			and FILE_URL = #{fileUrl,jdbcType=VARCHAR}
		</if>
		<if test="fileName != null and fileName !=''">
			and FILE_NAME = #{fileName,jdbcType=VARCHAR}
		</if>
		<if test="direction !=null ">
			and DIRECTION = #{direction,jdbcType=TINYINT}
		</if>
	</sql>
</mapper>

