package com.zhixianghui.service.flow.core.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.flow.service.FlowImageFacade;
import com.zhixianghui.service.flow.core.biz.FlowImageBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;

/**
 * <AUTHOR>
 * @ClassName FlowImageImpl
 * @Description TODO
 * @Date 2021/4/23 14:18
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FlowImageImpl implements FlowImageFacade {

    private final FlowImageBiz flowImageBiz;

    @Override
    public byte[] getDefinitionImage(String processDefinitionId) throws BizException, IOException {
        return flowImageBiz.getDefinitionImage(processDefinitionId);
    }

    @Override
    public String getInstanceImage(String processInstanceId) throws BizException,IOException{
        return flowImageBiz.getInstanceImage(processInstanceId);
    }
}
