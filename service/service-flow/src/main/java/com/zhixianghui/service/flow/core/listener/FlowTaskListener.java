package com.zhixianghui.service.flow.core.listener;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.flow.dto.FlowSignDto;
import com.zhixianghui.facade.flow.dto.WorkOrderExtDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.ComponentTypeEnum;
import com.zhixianghui.facade.flow.vo.work.BaseComponent;
import com.zhixianghui.facade.flow.vo.work.SignComponent;
import com.zhixianghui.facade.flow.vo.work.WorkForm;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.flow.core.biz.CommonFlowBiz;
import com.zhixianghui.service.flow.core.dao.CommonFlowDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName BaseTaskListener
 * @Description TODO
 * @Date 2021/5/27 10:39
 */
@Slf4j
@Component
public class FlowTaskListener{

    @Reference
    private NotifyFacade notifyFacade;

    @Autowired
    private CommonFlowDao commonFlowDao;

    @Component("mainAuthContinue")
    public class mainAuthContinue implements TaskListener{
        @Override
        public void notify(DelegateTask delegateTask) {
            //获取流程变量
            String mchNo = (String) delegateTask.getVariable("mchNo");
            log.info("继续提交主体认证流程，修改商户认证状态");
            notifyFacade.sendOne(MessageMsgDest.TOPIC_MAIN_AUTH_CHANGE, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_CONTINUE_MAIN_AUTH,mchNo);
        }
    }

    @Component("flowSign")
    public class flowSign implements TaskListener{
        @Override
        public void notify(DelegateTask delegateTask) {
            String processInstanceId = delegateTask.getProcessInstanceId();
            if (StringUtils.isBlank(processInstanceId)){
                delegateTask.setVariable("sign",101);
                return;
            }
            WorkOrderExtDto workOrderExtDto = commonFlowDao.getJoinEntityByProcessInstanceId(processInstanceId);
            WorkForm workForm =new WorkForm();
            workForm.transferBean(workOrderExtDto.getExtInfo());
            BaseComponent baseComponent = workForm.getComponent().stream().filter(x-> x.getType().equals(ComponentTypeEnum.SIGN_COMPONENT.getType())).findFirst().orElse(null);
            if (baseComponent != null){
                    SignComponent signComponent = (SignComponent) baseComponent;
                    if (!signComponent.getValue().equals("2")) {
                        //线下签约，直接退出
                        delegateTask.setVariable("sign",101);
                        return;
                    }
                    FlowSignDto flowSignDto = new FlowSignDto();
                    flowSignDto.setCommitFlowId(workOrderExtDto.getCommonFlowId());
                    flowSignDto.setFileUrl(signComponent.getTemplateFileUrl());
                    flowSignDto.setMerchantNo(workOrderExtDto.getEmployerNo());
                    flowSignDto.setMainstayNo(workOrderExtDto.getMainstayNo());
                    //发送消息进行签约
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_FLOW_SIGN, NotifyTypeEnum.SIGN_NOTIFY.getValue(), MessageMsgDest.TAG_FLOW_SIGN, JSONObject.toJSONString(flowSignDto));
                    delegateTask.setVariable("sign",100);
            }
        }
    }

    @Component("channelAudit")
    public class ChannelAudit implements TaskListener{

        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateTask delegateTask) {
            log.info("通报价单审批，通道审批环节,任务id:[{}]，流程实例id:[{}]",delegateTask.getId(),delegateTask.getProcessInstanceId());
            String taskId = delegateTask.getId();
            String mchNo = (String) delegateTask.getVariable("mchNo");
            Map<String,Object> map = new HashMap<>();
            map.put("taskId",taskId);
            map.put("mchNo",mchNo);
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(delegateTask.getProcessInstanceId());
            map.put("commonFlowId",commonFlow.getId());
            notifyFacade.sendOne(MessageMsgDest.TOPIC_ZFT_CHANNEL_AUDIT,NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),
                    MessageMsgDest.TAG_ZFT_CHANNEL_AUDIT,JsonUtil.toString(map));
        }
    }
}
