package com.zhixianghui.service.flow.aspect;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/7/22 16:42
 */

@Component
public class FlowHandleContext {
    private final Map<String, Builder> handler = new HashMap<>();

    public Builder getBuilder(String type) {
        return handler.get(type);
    }

    public void putBuilder(String type, Builder builder) {
        handler.put(type, builder);
    }
}
