package com.zhixianghui.service.flow.core.listener;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.dto.FlowSignDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.ComponentTypeEnum;
import com.zhixianghui.facade.flow.enums.FlowStatusEnum;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.enums.TaskHandleStatusEnum;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.flow.vo.req.TaskHandleVo;
import com.zhixianghui.facade.flow.vo.work.*;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.enums.QuoteAuditEnum;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerInsertVo;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.service.flow.core.biz.CommonFlowBiz;
import com.zhixianghui.service.flow.core.biz.ProcessInstanceBiz;
import com.zhixianghui.service.flow.core.biz.TaskBiz;
import com.zhixianghui.service.flow.core.dao.CommonFlowDao;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MessageListener
 * @Description TODO
 * @Date 2021/5/13 15:29
 */
@Slf4j
@Component
public class MessageListener {

    @Autowired
    private ProcessInstanceBiz processInstanceBiz;

    @Autowired
    private CommonFlowDao commonFlowDao;

    /**
     * 消费创建代征主体消息
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CREATE_RELATION_FLOW, selectorExpression = MessageMsgDest.TAG_CREATE_RELATION_FLOW,consumeThreadMax = 3, consumerGroup = "createRelationFlowGroup")
    public class CreateEmployerMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String jsonParam) {
            if (StringUtil.isEmpty(jsonParam)){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("商户数据不能为空");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            List<MerchantEmployerInsertVo> voList = JsonUtil.toList(jsonParam,MerchantEmployerInsertVo.class);
            voList.stream().forEach(merchantEmployerInsertVo -> processInstanceBiz.startMerchantRelationFlow(merchantEmployerInsertVo));
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FETCH_SIGN_URL,selectorExpression = MessageMsgDest.TAG_FETCH_SIGN_URL,consumeThreadMax = 3,consumerGroup = "flowSignComsumer")
    public class FlowSignConsumerListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {
            if (StringUtil.isEmpty(jsonParam)){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("签约返回信息为空");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            FlowSignDto flowSignDto = JsonUtil.toBean(jsonParam,FlowSignDto.class);
            log.info("工单签约回调，回调信息：[{}]",jsonParam);
            CommonFlow commonFlow = commonFlowDao.getById(flowSignDto.getCommitFlowId());
            if (commonFlow != null){
//                workForm.transferBean(commonFlow.getExtInfo());
//                BaseComponent baseComponent = workForm.getComponent().stream().filter(x->x.getType().equals(ComponentTypeEnum.SIGN_COMPONENT.getType())).findFirst().orElse(null);
//                if (baseComponent != null){
//                    SignComponent signComponent = (SignComponent) baseComponent;
//                    signComponent.setSignFileUrl(flowSignDto.getFileUrl());
//                }

                JSONObject extInfo = JSONObject.parseObject(commonFlow.getExtInfo());
                extInfo.getJSONArray("component").stream().forEach(x->{
                    JSONObject component = (JSONObject) x;
                    if (component.getString("type").equals(ComponentTypeEnum.SIGN_COMPONENT.getType())){
                        component.put("signFileUrl",flowSignDto.getFileUrl());
                    }
                });
                commonFlow.setExtInfo(extInfo.toJSONString());
                commonFlowDao.update(commonFlow);
            }
        }
    }

    public static void main(String[] args) {
        TextComponent tc1 = new TextComponent("步骤1","请点击下载 《服务履约单》，并在截止时间内完成确认");
        TextComponent tc2 = new TextComponent("步骤2","选择签署方式，线下签署需要自行打印并上传");
        TextComponent tc3 = new TextComponent("步骤3","点击提交，即可完成签约");
        SignComponent signComponent = new SignComponent();
        signComponent.setTitle("签约方式");
        signComponent.setTemplateFileUrl("123123123123123");
        signComponent.setValue("1");
        signComponent.setTips("E签宝正在调用贵司数字证书，点击确认后即可自动完成签约");
        signComponent.setUploadText("提交确认单");
        signComponent.setField(new ArrayList<String>(){{add("field1");add("field2");}});
        WorkForm w = new WorkForm("服务履约确认单","",tc1,tc2,tc3,signComponent);
        String str = JsonUtil.toString(w);

        WorkForm workForm = new WorkForm();
        workForm.transferBean(str);
        BaseComponent baseComponent = workForm.getComponent().stream().filter(x->x.getType().equals(ComponentTypeEnum.SIGN_COMPONENT.getType())).findFirst().orElse(null);
        if (baseComponent != null){
            SignComponent s = (SignComponent) baseComponent;
            s.setUrls(new ArrayList<String>(){{add("123123123");}});
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_CALLBACK,selectorExpression = MessageMsgDest.TAG_ZFT_AUDIT_PASS_NOTIFY,consumeThreadMax = 5,consumerGroup = "ZftPassConsumerGroup")
    public class ZFTPassListener extends BaseRocketMQListener<String>{

        @Autowired
        private CommonFlowBiz commonFlowBiz;
        @Autowired
        private TaskBiz taskBiz;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            Map<String,Object> map = JsonUtil.toBean(jsonParam,new TypeReference<HashMap<String,Object>>(){});
            String memo = (String) map.get("memo");
            String smid = (String) map.get("smid");
            String businessKey = String.join("-", (String)map.get("external_id"), ProductNoEnum.ZFT.getValue());
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("businessKey",businessKey);
            paramMap.put("status", FlowStatusEnum.PENDING.getValue());
            List<CommonFlow> flowList = commonFlowBiz.listBy(paramMap);
            if (flowList != null && flowList.size() > 0){
                CommonFlow commonFlow = flowList.get(0);
                //根据实例id获取任务id
                Task task = taskBiz.taskQuery().processInstanceId(commonFlow.getProcessInstanceId()).active().singleResult();
                //修改extObj信息，添加smid
                MerchantFlowVo merchantFlowVo = JsonUtil.toBean(commonFlow.getExtInfo(),MerchantFlowVo.class);
                merchantFlowVo.setSmid(smid);
                commonFlow.setExtInfo(JsonUtil.toString(merchantFlowVo));
                commonFlowBiz.update(commonFlow);

                TaskHandleVo taskHandleVo = new TaskHandleVo();
                taskHandleVo.setCommonFlowId(commonFlow.getId());
                taskHandleVo.setTaskId(task.getId());
                taskHandleVo.setOpinion(memo);
                taskHandleVo.setStatus(TaskHandleStatusEnum.AGREE.getValue());

                FlowUserVo flowUserVo = new FlowUserVo();
                flowUserVo.setUserName(FlowConstant.EXTERNAL_NAME);
                flowUserVo.setExternalId(FlowConstant.EXTERNAL_ID);
                flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
                taskBiz.executeTask(taskHandleVo,flowUserVo,false);
            }
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_CALLBACK,selectorExpression = MessageMsgDest.TAG_ZFT_AUDIT_REFUSE_NOTIFY,consumeThreadMax = 5,consumerGroup = "ZftRefuseConsumerGroup")
    public class ZFTRefuseListener extends BaseRocketMQListener<String>{

        @Autowired
        private CommonFlowBiz commonFlowBiz;
        @Autowired
        private TaskBiz taskBiz;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            Map<String,Object> map = JsonUtil.toBean(jsonParam,new TypeReference<HashMap<String,Object>>(){});
            String reason = (String) map.get("reason");
            String businessKey = String.join("-", (String)map.get("external_id"), ProductNoEnum.ZFT.getValue());
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("businessKey",businessKey);
            paramMap.put("status", FlowStatusEnum.PENDING.getValue());
            List<CommonFlow> flowList = commonFlowBiz.listBy(paramMap);
            if (flowList != null && flowList.size() > 0){
                CommonFlow commonFlow = flowList.get(0);
                //根据实例id获取任务id
                Task task = taskBiz.taskQuery().processInstanceId(commonFlow.getProcessInstanceId()).active().singleResult();
                TaskHandleVo taskHandleVo = new TaskHandleVo();
                taskHandleVo.setCommonFlowId(commonFlow.getId());
                taskHandleVo.setTaskId(task.getId());
                taskHandleVo.setOpinion(reason);
                taskHandleVo.setStatus(TaskHandleStatusEnum.DISAGREE.getValue());

                FlowUserVo flowUserVo = new FlowUserVo();
                flowUserVo.setUserName(FlowConstant.EXTERNAL_NAME);
                flowUserVo.setExternalId(FlowConstant.EXTERNAL_ID);
                flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
                taskBiz.executeTask(taskHandleVo,flowUserVo,false);
            }
        }
    }
}
