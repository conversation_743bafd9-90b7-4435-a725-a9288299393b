package com.zhixianghui.service.flow.core.util;

import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowUserUtil
 * @Description TODO
 * @Date 2021/5/11 11:48
 */
public class FlowUserUtil {

    /**
     * 缓存转换流程用户类
     * @return
     */
    public static FlowUserVo cacheTransferFlowUserVo(Map<String,String> userMap,String flowUserId){
        FlowUserVo flowUserVo = new FlowUserVo();
        if (flowUserId.equals(FlowConstant.EXTERNAL_ID)){
            flowUserVo.setExternalId(flowUserId);
            flowUserVo.setUserName(FlowConstant.EXTERNAL_NAME);
            flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
            return flowUserVo;
        }

        if (userMap != null && userMap.size() > 0){
            flowUserVo.setUserId(Long.parseLong(userMap.get("id")));
            flowUserVo.setUserName(userMap.get("name"));
            flowUserVo.setPlatform(Integer.parseInt(userMap.get("platform")));
            flowUserVo.setNo(userMap.get("no"));
        }else{
            String[] userArr = flowUserId.split(":");
            flowUserVo.setPlatform(Integer.parseInt(userArr[0]));
            flowUserVo.setUserId(Long.parseLong(userArr[1]));
            flowUserVo.setUserName("");
            if (userArr.length > 2){
                flowUserVo.setNo(userArr[2]);
            }
        }
        return flowUserVo;
    }

    //转换用户引擎内id
    public static String flowUserVoTransferFlowId(FlowUserVo flowUserVo){
        if (flowUserVo.getUserId() == null){
            return flowUserVo.getExternalId();
        }
        if (flowUserVo.getPlatform() == PlatformSource.OPERATION.getValue()){
            return String.join(":",flowUserVo.getPlatform().toString(),flowUserVo.getUserId().toString());
        }else{
            return String.join(":",flowUserVo.getPlatform().toString(),flowUserVo.getUserId().toString(),flowUserVo.getNo());
        }
    }
}
