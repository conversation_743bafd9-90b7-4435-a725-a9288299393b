package com.zhixianghui.service.flow.core.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.utils.ContrastObjUtils;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowStatusEnum;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.enums.TaskHandleTypeEnum;
import com.zhixianghui.facade.flow.vo.req.CommonFlowEditVo;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantInfoChangeRecordFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerInsertVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerQuoteVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.flow.annotation.ModifyRecord;

import com.zhixianghui.service.flow.core.util.FlowUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.flowable.engine.FormService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.form.FormProperty;
import org.flowable.engine.form.TaskFormData;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.task.api.Task;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ProcessInstanceBiz
 * @Description TODO
 * @Date 2021/4/25 9:10
 */
@Slf4j
@Service
public class ProcessInstanceBiz {

    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    @Reference
    private MerchantInfoChangeRecordFacade changeRecordFacade;

    @Reference
    private MerchantFacade merchantFacade;

    @Reference
    private AgentFacade agentFacade;

    @Reference
    private MerchantEmployerQuoteFacade merchantEmployerQuoteFacade;

    @Autowired
    private CommonFlowBiz commonFlowBiz;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private FormService formService;

    @Reference
    private NotifyFacade notifyFacade;

    private ProcessInstanceQuery instanceQuery() {
        return runtimeService.createProcessInstanceQuery();
    }

    @Transactional(rollbackFor = Exception.class)
    public CommonFlow startProcessByProcessDefinitionKey(ProcessVo processVo, FlowUserVo createFlowUser, Map<String, List<FlowUserVo>> userMap, Map<String, Object> flowParam) throws BizException {
        Date date = new Date();
        // 遍历参数列表，取出用户id放入流程参数中
        if (userMap != null) {
            for (Map.Entry<String, List<FlowUserVo>> entry : userMap.entrySet()) {
                List<String> userIdList = entry.getValue().stream().map(flowUserVo -> {
                    String userId = FlowUserUtil.flowUserVoTransferFlowId(flowUserVo);
                    return userId;
                }).collect(Collectors.toList());
                //单实例节点不能使用list
                if (userIdList.size() == 1) {
                    flowParam.put(entry.getKey(), userIdList.get(0));
                } else {
                    flowParam.put(entry.getKey(), userIdList);
                }
            }
        }


        //设置参与者
        flowParam.put(FlowConstant.APPLY_USER_ID, FlowUserUtil.flowUserVoTransferFlowId(createFlowUser));
        //设置启用自动跳过
        flowParam.put(FlowConstant.SKIP_EXPRESSION_ID, true);
        //启动默认同意
        flowParam.put(FlowConstant.FLOW_STATUS, 100);

        //判断是否属于admin
        if(isAdmin(createFlowUser)){
            flowParam.put(FlowConstant.IS_ADMIN,100);
        }else{
            flowParam.put(FlowConstant.IS_ADMIN,101);
        }


        //启动流程，按照最新版本的流程定义启动
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processVo.getProcessDefinitionKey(), flowParam);
        //流程通用表数据
        CommonFlow commonFlow = new CommonFlow();
        BeanUtils.copyProperties(processVo, commonFlow);
        commonFlow.setProcessInstanceId(processInstance.getRootProcessInstanceId());
        commonFlow.setStatus(FlowStatusEnum.PENDING.getValue());
        commonFlow.setInitiatorId(createFlowUser.getUserId());
        commonFlow.setInitiatorName(createFlowUser.getUserName());
        commonFlow.setPlatform(createFlowUser.getPlatform());
        commonFlow.setCreateTime(date);
        commonFlow.setUpdateTime(date);
        commonFlow.setStep(1);

        commonFlow = commonFlowBiz.createRecord(processInstance.getRootProcessInstanceId(), createFlowUser, commonFlow,processVo.getWorkType(),processVo.getExtObj(),processVo.getCarbonCopyList());
        log.info("流程启动，流程根实例id：[{}]，流程业务key：[{}]", processInstance.getRootProcessInstanceId(), commonFlow.getId());
        //更新流程实例业务key
        ProcessInstance proInc = instanceQuery().processInstanceId(processInstance.getRootProcessInstanceId()).singleResult();
        if (proInc != null){
            runtimeService.updateBusinessKey(processInstance.getRootProcessInstanceId(), commonFlow.getId().toString());
        }else{
            commonFlow.setVersion(commonFlow.getVersion() + 1);
            commonFlow.setEndTime(date);
            commonFlow.setStatus(FlowStatusEnum.FINISHED.getValue());
            commonFlowBiz.update(commonFlow);
        }
        return commonFlow;
    }

    /**
     * 判断是否为超管
     * @param createFlowUser
     * @return
     */
    private boolean isAdmin(FlowUserVo createFlowUser) {
        if (createFlowUser.getPlatform() == PlatformSource.OPERATION.getValue()){
            PmsOperator pmsOperator = pmsOperatorFacade.getOperatorById(createFlowUser.getUserId());
            if (pmsOperator != null && pmsOperator.getType() == PmsOperatorTypeEnum.ADMIN.getValue()){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;

    @ModifyRecord
    public void editBusinessVariable(CommonFlowEditVo commonFlowEditVo, FlowUserVo flowUserVo, Boolean isAdmin) throws BizException {
        //判断是否可以编辑
        TaskFormData taskFormData = formService.getTaskFormData(commonFlowEditVo.getTaskId());

        boolean isEdit;
        FormProperty formProperty = taskFormData.getFormProperties().stream().filter(x ->
                x.getId().equals(FlowConstant.EDIT_FORM_ID)).findFirst().orElse(null);
        if (formProperty != null) {
            isEdit = Boolean.parseBoolean(formProperty.getValue());
        } else {
            isEdit = false;
        }

        if (!isEdit) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该环节无法编辑");
        }

        CommonFlow commonFlow = commonFlowBiz.getById(commonFlowEditVo.getCommonFlowId());
        String diffInfo = "";
        //对比变更数据
        //diffInfo = ContrastObjUtils.compareJsonObject(commonFlow.getExtInfo(), commonFlowEditVo.getExtInfo());
        commonFlow.setExtInfo(commonFlowEditVo.getExtInfo());
        commonFlow.setUpdateTime(new Date());
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            commonFlowBiz.editBusinessVariable(commonFlow, null, flowUserVo, commonFlowEditVo.getRemark(),commonFlowEditVo.getTaskId());
            extraHandle(commonFlow.getExtInfo(), commonFlow, flowUserVo);
        } catch (Exception e) {
            log.error("审批流编辑异常 : ", e);
            platformTransactionManager.rollback(transaction);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("审批流编辑异常");
        }
        platformTransactionManager.commit(transaction);
    }

    public void extraHandle(String extInfo, CommonFlow commonFlow, FlowUserVo flowUserVo) {
        if (FlowTypeEnum.PMS_MCH_CREATE_PRODUCT.getFlowTopicType().equals(commonFlow.getFlowTopicType())){
            JSONObject jsonObject = JSON.parseObject(commonFlow.getExtInfo());
            jsonObject.put("commonFlowId",commonFlow.getId());
            notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC,NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_EDIT_QUOTE_ASYNC,jsonObject.toJSONString());
            return;
        }

        if (FlowTypeEnum.PMS_AGENT_QUOTE_EDIT.getFlowTopicType().equals(commonFlow.getFlowTopicType())){
            log.info("修改合伙人产品报价单审批流程内容");
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_QUOTE_EDIT_MODIFY,NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_AGENT_QUOTE_EDIT_MODIFY, JSONObject.toJSONString(commonFlow));
        }

        if (!FlowTypeEnum.PMS_AGENT_CREATE.getFlowTopicType().equals(commonFlow.getFlowTopicType())) {
            return;
        }
        agentFacade.extraHandle(extInfo, commonFlow.getBusinessKey(), flowUserVo.getUserName());
    }

    /**
     * 根据任务id获取根实例
     *
     * @param task
     * @return
     */
    public String getRootInstanceByTask(Task task) {
        ProcessInstance processInstance = instanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        return processInstance.getRootProcessInstanceId();
    }

    /**
     * 撤回流程实例
     *
     * @param commonFlowId
     * @param currentUser
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessInstance(Long commonFlowId, FlowUserVo currentUser, Boolean isAdmin, String reason) throws BizException {
        CommonFlow commonFlow = commonFlowBiz.getById(commonFlowId);
        if ((commonFlow.getPlatform().intValue() != currentUser.getPlatform().intValue() ||
                !commonFlow.getInitiatorId().equals(currentUser.getUserId())) && !isAdmin) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户无撤回该流程权限");
        }
        if (commonFlow.getStatus() == FlowStatusEnum.FINISHED.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("流程已完成，无法撤回");
        }
        runtimeService.deleteProcessInstance(commonFlow.getProcessInstanceId(), TaskHandleTypeEnum.EDIT.getDesc());
        commonFlowBiz.deleteProcessInstance(commonFlow, currentUser, reason);
        log.info("撤回流程，流程id：[{}]，操作人：[{}]", commonFlowId, FlowUserUtil.flowUserVoTransferFlowId(currentUser));
        commonFlow.setCurrentUserName(currentUser.getUserName());
        afterDeleteHandle(commonFlow);
        changeRecordFacade.update(commonFlowId);
    }

    /**
     * 撤回后操作
     *
     * @param commonFlow
     */
    private void afterDeleteHandle(CommonFlow commonFlow) {
        if (FlowTypeEnum.MAINSTAY_MCH_MAIN_AUTH.getFlowTopicType().equals(commonFlow.getFlowTopicType()) ||
                FlowTypeEnum.MERCHANT_MCH_MAIN_AUTH.getFlowTopicType().equals(commonFlow.getFlowTopicType()) ||
                FlowTypeEnum.PMS_MCH_MAIN_AUTH.getFlowTopicType().equals(commonFlow.getFlowTopicType()) ||
                FlowTypeEnum.PMS_MAINSTAY_MAIN_AUTH.getFlowTopicType().equals(commonFlow.getFlowTopicType())) {
            notifyFacade.sendOne(MessageMsgDest.TOPIC_MAIN_AUTH_CHANGE, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_DELETE_MAIN_AUTH_FLOW, commonFlow.getBusinessKey());
            return;
        }
        if (FlowTypeEnum.PMS_AGENT_CREATE.getFlowTopicType().equals(commonFlow.getFlowTopicType())) {
            String msg = commonFlow.getBusinessKey() + "-" + commonFlow.getCurrentUserName();
            log.info("取消合伙人认证审批流程,发送消息");
            notifyFacade.sendOne(
                    MessageMsgDest.TOPIC_APPROVAL_ASYNC,
                    NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),
                    MessageMsgDest.TAG_APPROVAL_AGENT_CANCEL_ASYNC,
                    msg
            );
        }
        if (FlowTypeEnum.PMS_MCH_CREATE_PRODUCT.getFlowTopicType().equals(commonFlow.getFlowTopicType())){
            log.info("取消产品报价单审批流程");
            notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC,NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_APPROVAL_QUOTE_CANDEL,Long.toString(commonFlow.getId()));
        }

        if (FlowTypeEnum.PMS_AGENT_QUOTE_EDIT.getFlowTopicType().equals(commonFlow.getFlowTopicType())){
            log.info("取消合伙人产品报价单审批流程");
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_QUOTE_EDIT_ROLLBACK,NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_AGENT_QUOTE_EDIT_ROLLBACK,Long.toString(commonFlow.getId()));
        }
    }

    /**
     * 内部发起代征关系审批流
     *
     * @param merchantEmployerInsertVo
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void startMerchantRelationFlow(MerchantEmployerInsertVo merchantEmployerInsertVo) throws BizException {
        MerchantEmployerQuoteVo vo = merchantEmployerInsertVo.getQuoteVoList().get(0);
        if (vo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("产品报价单不能为空");
        }

        //判断是否已有重复流程
        String flowBusinessKey = vo.getFlowBusinessKey();
        List<CommonFlow> commonFlowList = commonFlowBiz.getByBusinessKey(flowBusinessKey);
        //批次号存在则说明已创建流程
        if (commonFlowList != null && commonFlowList.size() > 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次号为[" + flowBusinessKey + "]的流程已创建，请勿重复创建");
        }

        //发起流程
        ProcessVo processVo = new ProcessVo();
        processVo.setProcessDefinitionKey(FlowTypeEnum.PMS_EMPLOY_MAINSTAY_RELATION_APPLY.name());
        processVo.setFlowTopicName(String.join("-", FlowTypeEnum.PMS_EMPLOY_MAINSTAY_RELATION_APPLY.getDesc(), merchantEmployerInsertVo.getMchName(), vo.getMainstayMchName()));
        processVo.setFlowTopicType(FlowTypeEnum.PMS_EMPLOY_MAINSTAY_RELATION_APPLY.getFlowTopicType());
        processVo.setBusinessKey(flowBusinessKey);
        processVo.setExtInfo(JsonUtil.toString(merchantEmployerInsertVo));

        //构建参与者参数
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        flowUserVo.setUserId(merchantEmployerInsertVo.getSalerId());
        flowUserVo.setUserName(merchantEmployerInsertVo.getSalerName());

        //构建流程所需参数
        Map<String, Object> map = new HashMap<>();
        map.put("mainstayNo", vo.getMainstayMchNo());
        //启动流程
        startProcessByProcessDefinitionKey(processVo, flowUserVo, null, map);
    }
}
