package com.zhixianghui.service.flow.core.biz;

import com.google.common.collect.Maps;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.FlowHandleType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.constant.NormalConstant;
import com.zhixianghui.facade.flow.dto.CommonFlowLogVo;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.entity.CommonFlowLog;
import com.zhixianghui.facade.flow.enums.FlowStatusEnum;
import com.zhixianghui.facade.flow.enums.TaskHandleStatusEnum;
import com.zhixianghui.service.flow.core.dao.CommonFlowDao;
import com.zhixianghui.service.flow.core.dao.CommonFlowLogDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* 审批节点详情表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-04-26
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonFlowLogBiz {

    private final CommonFlowLogDao commonflowlogDao;

    private final CommonFlowDao commonFlowDao;

    public List<CommonFlowLogVo> getDetailByCommonFlowId(Long commonFlowId,Integer platform) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("commonFlowId",commonFlowId);
        paramMap.put("canSeeEdit",platform.intValue() == PlatformSource.OPERATION.getValue());
        paramMap.put("taskName", FlowHandleType.EDIT.getDesc());
        List<CommonFlowLogVo> list =  commonflowlogDao.listBy("listAllLog",paramMap);

        CommonFlow commonFlow = commonFlowDao.getById(commonFlowId);

        list.stream().forEach(x->{
            List<CommonFlowLog> vo = new ArrayList<>();
            x.getCommonFlowLogList().stream().forEach(commonFlowLog->{
                //隐藏已完成的多人任务中未审批的数据
                if (commonFlow.getStep() != null && x.getStep() != null && commonFlowLog.getStatus() != null
                         && commonFlowLog.getStatus().intValue() == TaskHandleStatusEnum.PENDING.getValue()
                        && (commonFlow.getStep().intValue() > x.getStep().intValue() || commonFlow.getStatus().intValue() == FlowStatusEnum.FINISHED.getValue())){
                    return;
                }

                //不是运营平台查看
                if (platform.intValue() != PlatformSource.OPERATION.getValue()){
                    //显示本平台数据
                    if (platform.intValue() == commonFlowLog.getPlatform().intValue()){
                        vo.add(commonFlowLog);
                        return;
                    }
                    //隐藏变更审批人、编辑数据
                    if (commonFlowLog.getTaskName().equals(FlowConstant.CHANGE_USER) || commonFlowLog.getTaskName().equals(FlowConstant.EDIT_INFO)){
                        return;
                    }
                    //隐藏待审批数据
                    if (commonFlowLog.getStatus() != null && commonFlowLog.getStatus().intValue() == TaskHandleStatusEnum.PENDING.getValue()){
                        return;
                    }

                    //隐藏运营后台人员名称
                    if (commonFlowLog.getPlatform().intValue() == PlatformSource.OPERATION.getValue()){
                        commonFlowLog.setHandlerName(NormalConstant.OPERATION_NAME);
                        commonFlowLog.setApprovalOpinion(null);
                        vo.add(commonFlowLog);
                        return;
                    }

                    //隐藏不同平台人员名称
                    if (platform.intValue() != commonFlowLog.getPlatform().intValue()){
                        commonFlowLog.setHandlerName("操作员");
                        vo.add(commonFlowLog);
                        return;
                    }
                }else{
                    vo.add(commonFlowLog);
                }
            });
            x.setCommonFlowLogList(vo);
        });

        list.stream().map(CommonFlowLogVo::getCommonFlowLogList).forEach(x->{
            x.stream().forEach(commonFlowLog -> {

            });
        });

        return list;
    }
}