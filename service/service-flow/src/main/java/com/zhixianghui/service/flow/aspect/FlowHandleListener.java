package com.zhixianghui.service.flow.aspect;

import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/7/22 16:42
 */

@Component
public class FlowHandleListener implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        Map<String, Object> map = event.getApplicationContext().getBeansWithAnnotation(FlowType.class);
        FlowHandleContext context = event.getApplicationContext().getBean(FlowHandleContext.class);
        map.forEach((beanName, object) -> {
            FlowType handler = object.getClass().getAnnotation(FlowType.class);
            context.putBuilder(handler.value().getFlowTopicType(), (Builder) object);
        });
    }
}
