package com.zhixianghui.service.flow.core.biz;

import com.zhixianghui.service.flow.core.dao.CommonFlowCarbonDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2022-06-09
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonFlowCarbonBiz {

    private final CommonFlowCarbonDao commonflowcarbonDao;
}