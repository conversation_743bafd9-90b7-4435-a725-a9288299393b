package com.zhixianghui.service.flow.core.helper;

import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.tenant.TenantManage;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.TenantManageFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName FlowOperator
 * @Description 方法表达式调用，用于解析获取用户，返回数组/集合，流程图配置方式 ${op.asList("abc","def")}
 * @Date 2021/5/7 11:36
 */
@Component("op")
public class FlowOperator {

    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;

    @Reference
    private SupplierStaffFacade supplierStaffFacade;

    @Reference
    private EmployerStaffFacade employerStaffFacade;

    @Reference
    private TenantManageFacade tenantManageFacade;

    public <T> T[] asArray(T... objects)
    {
        return objects;
    }

    public <T> List<T> asList(T... objects)
    {
        return Arrays.asList(objects);
    }

    public boolean isTenant(String mainstayNo){
        TenantManage tenantManage = tenantManageFacade.getTenantByTenantNo(mainstayNo);
        if (tenantManage == null){
            return false;
        }
        return true;
    }

    /**
     * 根据部门id获取部门负责人
     * @param numbers
     * @return
     */
    public List<String> getLeaderByNumbers(String ... numbers){
        List<String> leaderIdList = pmsDepartmentFacade.getDepartmentByNumbers(Arrays.asList(numbers)).stream().filter(x->!x.getLeaderId().equals(-1L)).map(pmsDepartment ->
                String.join(":", PlatformSource.OPERATION.getValue()+"",pmsDepartment.getLeaderId().toString())
        ).collect(Collectors.toList());
        return leaderIdList;
    }

    /**
     * 根据商户编号，角色获取职工
     * @param mchNo
     * @param roleId
     * @return
     */
    public List<String> getMchStaffByRole(String mchNo,String ... roleId){
        List<Long> roleIds = new ArrayList<>();
        for (String s : roleId){
            if (StringUtil.isNotEmpty(s)){
                roleIds.add(Long.parseLong(s));
            }
        }
        return employerStaffFacade.getDistinctStaffByRoleIdAndMchNo(mchNo,roleIds).stream().map(s ->
                String.join(":",PlatformSource.MERCHANT.getValue()+"",s,mchNo)).collect(Collectors.toList());
    }

    /**
     * 根据供应商编号，角色获取职工
     * @param mainstayNo
     * @param roleId
     * @return
     */
    public List<String> getMainstayStaffByRole(String mainstayNo,String ... roleId){
        List<Long> roleIds = new ArrayList<>();
        for (String s : roleId) {
            if (StringUtil.isNotEmpty(s)){
                roleIds.add(Long.parseLong(s));
            }
        }
        return supplierStaffFacade.getDistinctStaffByRoleIdAndMainstayNo(mainstayNo,roleIds).stream().map(s ->
                String.join(":",PlatformSource.SUPPLIER.getValue()+"",s,mainstayNo)).collect(Collectors.toList());
    }

    /**
     * 根据部门编号获取部门下的人员
     * @param numbers
     * @return
     */
    public List<String> getOperatorByDepartmentNumbers(String ... numbers){
        List<String> list = new ArrayList<>();
        for(String num : numbers){
            list.addAll(pmsOperatorFacade.listActiveByDepartmentNumber(num).stream().map(s ->
                    String.join(":",PlatformSource.OPERATION.getValue()+"",s.getId()+"")).collect(Collectors.toList()));
        }
        return list;
    }

}
