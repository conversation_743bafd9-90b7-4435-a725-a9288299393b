package com.zhixianghui.service.flow.core.facade;

import com.zhixianghui.facade.flow.enums.WorkTypeEnum;
import com.zhixianghui.facade.flow.service.QueryFlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.service.flow.core.dao.CommonFlowDao;
import com.zhixianghui.service.flow.core.util.FlowUserUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName QueryFlowFacadeImpl
 * @Description TODO
 * @Date 2022/5/5 15:06
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class QueryFlowFacadeImpl implements QueryFlowFacade {

    private final CommonFlowDao commonFlowDao;

    @Override
    public Map<String, Object> countTaskData(FlowUserVo flowUserVo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("assignee", FlowUserUtil.flowUserVoTransferFlowId(flowUserVo));
        paramMap.put("workType", WorkTypeEnum.AUDIT_FLOW.getValue());
        long flowCount = commonFlowDao.countBy("todoListCount",paramMap);
        paramMap.put("workType",WorkTypeEnum.WORK_FLOW.getValue());
        long workCount = commonFlowDao.countBy("todoOrderListCount",paramMap);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("flowCount",Long.toString(flowCount));
        resultMap.put("workCount",Long.toString(workCount));
        return resultMap;
    }
}
