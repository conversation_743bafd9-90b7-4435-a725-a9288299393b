package com.zhixianghui.service.flow.core.biz;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.flow.entity.ActReProcdef;
import com.zhixianghui.facade.flow.vo.req.ProcessDefinitionReqVo;
import com.zhixianghui.facade.flow.vo.res.ProcessDefinitionResVo;
import com.zhixianghui.service.flow.core.dao.ActReProcdefDao;
import com.zhixianghui.service.flow.core.dao.CommonFlowDao;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.DeploymentBuilder;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ProcessDefinitionBiz
 * @Description 流程定义biz
 * @Date 2021/4/23 11:07
 */
@Slf4j
@Service
public class ProcessDefinitionBiz {

    @Autowired
    private ActReProcdefDao actReProcdefDao;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    public DeploymentBuilder getDeployBuilder() {
        return repositoryService.createDeployment();
    }

    /**
     * 部署流程图
     *
     * @param name     流程名称
     * @param tenantId 租户id  用作数据权限，没有多租户时务必为空，填入此参数后后续所有操作都需要带上
     * @param bytes
     */
    @Transactional(rollbackFor = Exception.class)
    public void deploy(String name, String platform, String dataObj, String triggerAct, byte[] bytes, String originalFilename, String tenantId, String currentUserName, String description) throws BizException {
        //类型 = 关联对象:触发动作
        String category = String.join(":", platform, dataObj, triggerAct);
        InputStream is = null;
        try {
            is = new ByteArrayInputStream(bytes);
            DeploymentBuilder deploymentBuilder = getDeployBuilder().addInputStream(originalFilename, is);
            deploymentBuilder.name(name);
            deploymentBuilder.category(category);
            if (!StringUtil.isEmpty(tenantId)) {
                deploymentBuilder.tenantId(tenantId);
            }
            Deployment deployment = deploymentBuilder.deploy();
            log.info("部署流程，部署id：{}", deployment.getId());
            //修改表数据-需要严格控制一次只部署一个流程图，否则出现关联问题
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().deploymentId(deployment.getId()).singleResult();
            Map<String, Object> paramMap = Maps.newHashMap();
            Date date = new Date();
            paramMap.put("id", processDefinition.getId());
            paramMap.put("createUser", currentUserName);
            paramMap.put("createTime", date);
            paramMap.put("category", category);
            paramMap.put("tenantId", tenantId);
            paramMap.put("name", name);
            paramMap.put("desc", description);
            paramMap.put("updateUser", currentUserName);
            paramMap.put("updateTime", date);
            actReProcdefDao.update("updateProcessDefinition", paramMap);
        } catch (Exception e) {
            log.info("流程部署失败,异常信息：{}", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("流程部署失败");
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("流程部署失败");
                }
            }
        }
    }

    /**
     * 查看所有流程定义，只查出最新的版本，旧版本不作查询
     *
     * @param paramMap
     * @param pageParam
     * @return
     */
    public PageResult<List<ProcessDefinitionResVo>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<ActReProcdef>> pageResult = actReProcdefDao.listPage("listProDefPage", "proDefPageCount", paramMap, pageParam);
        List<ProcessDefinitionResVo> reqVoList = pageResult.getData().stream().map(actReProcdef -> {
            ProcessDefinitionResVo processDefinitionResVo = new ProcessDefinitionResVo();
            BeanUtils.copyProperties(actReProcdef, processDefinitionResVo);
            if (StringUtil.isNotEmpty(actReProcdef.getCategory()) && actReProcdef.getCategory().indexOf(":") > -1) {
                //从类型出拆除关联对象和触发类型
                String[] types = actReProcdef.getCategory().split(":");
                processDefinitionResVo.setPlatform(types[0]);
                processDefinitionResVo.setDataObj(types[1]);
                processDefinitionResVo.setTriggerAct(types[2]);
            }
            return processDefinitionResVo;
        }).collect(Collectors.toList());
        return PageResult.newInstance(reqVoList, pageParam, pageResult.getTotalRecord());
    }

    /**
     * 查看所有流程定义，只查出最新的版本，旧版本不作查询
     *
     * @return
     */
    @Deprecated
    public PageResult<List<ProcessDefinitionResVo>> listProcessDefinition(ProcessDefinitionReqVo processDefinitionReqVo, PageParam pageParam) {
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        //过滤条件
        if (!StringUtil.isEmpty(processDefinitionReqVo.getName())) {
            processDefinitionQuery.processDefinitionNameLike("%" + processDefinitionReqVo.getName() + "%");
        }
        if (!StringUtil.isEmpty(processDefinitionReqVo.getKey())) {
            processDefinitionQuery.processDefinitionKeyLike("%" + processDefinitionReqVo.getKey() + "%");
        }
        if (!StringUtil.isEmpty(processDefinitionReqVo.getCategory())) {
            processDefinitionQuery.processDefinitionCategory(processDefinitionReqVo.getCategory());
        }
        if (!StringUtil.isEmpty(processDefinitionReqVo.getTenantId())) {
            processDefinitionQuery.processDefinitionTenantId(processDefinitionReqVo.getTenantId());
        }
        //分页查询
        List<ProcessDefinitionResVo> list = processDefinitionQuery.latestVersion().orderByProcessDefinitionId().asc().listPage((pageParam.getPageCurrent() - 1) * pageParam.getPageSize(), pageParam.getPageSize())
                .stream().map(processDefinition -> {
                    ProcessDefinitionResVo processDefinitionResVo = new ProcessDefinitionResVo();
                    BeanUtils.copyProperties(processDefinition, processDefinitionResVo);
                    return processDefinitionResVo;
                }).collect(Collectors.toList());
        //查询总数
        long count = processDefinitionQuery.latestVersion().orderByProcessDefinitionId().asc().count();
        return PageResult.newInstance(list, pageParam, count);
    }

    /**
     * 挂起/激活流程，级联操作，会同时挂起当前正在运行的实例
     *
     * @param id
     * @param state
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessDefinitionState(String id, Integer state) throws BizException {
        //激活
        if (state == 1) {
            repositoryService.activateProcessDefinitionById(id, true, null);
        }

        if (state == 2) {
            //判断能否修改流程定义
            if (validateActiveProcess(id)){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前还有正在运行的实例，无法变更流程定义");
            }
            repositoryService.suspendProcessDefinitionById(id, true, null);
        }
    }

    /**
     * 判断是否可以修改流程定义
     * @param id
     * @return
     */
    private boolean validateActiveProcess(String id) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(id).singleResult();
        List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().processDefinitionKey(processDefinition.getKey()).active().list();
        if (processInstanceList != null && processInstanceList.size() > 0){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 删除流程定义，级联操作，会删除所有相关实例
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) throws BizException{
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(id).singleResult();
        //判断能否修改流程定义
        if (validateActiveProcess(id)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前还有正在运行的实例，无法变更流程定义");
        }
        repositoryService.deleteDeployment(processDefinition.getDeploymentId(), true);
    }

    public ProcessDefinitionResVo getById(String id) {
        ActReProcdef actReProcdef = actReProcdefDao.getById(id);
        ProcessDefinitionResVo processDefinitionResVo = new ProcessDefinitionResVo();
        BeanUtils.copyProperties(actReProcdef, processDefinitionResVo);
        if (StringUtil.isNotEmpty(actReProcdef.getCategory()) && actReProcdef.getCategory().indexOf(":") > -1) {
            //从类型出拆除关联对象和触发类型
            String[] types = actReProcdef.getCategory().split(":");
            processDefinitionResVo.setPlatform(types[0]);
            processDefinitionResVo.setDataObj(types[1]);
            processDefinitionResVo.setTriggerAct(types[2]);
        }
        return processDefinitionResVo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(String id, String dataObj, String triggerAct, String name, String platform, String desc, String tenantId, String currentUserName) {
        String category = String.join(":", platform, dataObj, triggerAct);
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("id", id);
        paramMap.put("category", category);
        paramMap.put("name", name);
        paramMap.put("desc", desc);
        paramMap.put("updateUser", currentUserName);
        paramMap.put("updateTime", new Date());
        paramMap.put("tenantId", tenantId);
        actReProcdefDao.update("updateProcessDefinition", paramMap);
    }
}
