package com.zhixianghui.service.flow.core.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowStatusEnum;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.flow.core.biz.CommonFlowBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowListener
 * @Description TODO
 * @Date 2021/5/13 16:44
 */
@Slf4j
@Component
public class FlowListener {

    @Autowired
    private RuntimeService runtimeService;

    @Reference
    private NotifyFacade notifyFacade;

    /**
     * 商户入驻审核通过操作
     */
    @Component("newMchApply")
    public class NewMchApplyListener extends BaseFlowListener {
        @Override
        public void afterAudit(String json) {
            log.info("商户入驻审核通过，发送审批通过消息");
            //执行完毕，发送消息插入数据
            notifyFacade.sendOne(MessageMsgDest.TOPIC_APPLY_NEW_MERCHANT, NotifyTypeEnum.APPLY_NEW_MERCHANT.getValue(), MessageMsgDest.TAG_APPLY_NEW_MERCHANT, json);
        }
    }

    /**
     * 创建代征关系操作
     */
    @Component("createMchRelation")
    public class CreateMchRelationListener extends BaseFlowListener {
        @Override
        public void afterAudit(String json) {
            log.info("创建代征关系审核通过，发送审批通过消息，topic：{}，tag：{}", MessageMsgDest.TOPIC_AFTER_CREATE_RELATION_FLOW, MessageMsgDest.TAG_AFTER_CREATE_RELATION_FLOW);
            //执行完毕，发送消息插入数据
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AFTER_CREATE_RELATION_FLOW, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_AFTER_CREATE_RELATION_FLOW, json);
        }
    }

    /**
     * 商户主体认证操作
     */
    @Component("newMainAuth")
    public class NewMainAuthListener extends BaseFlowListener {
        @Override
        public void afterAudit(String json) {
            log.info("主体认证审核通过，发送审批通过消息");
            //执行完毕，发送消息插入数据
            notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_MERCHANT_VERIFY_ASYNC, json);
        }
    }

    /**
     * 修改用工企业信息操作
     */
    @Deprecated
    @Component("editMerchantData")
    public class EditMerchantDataListener extends BaseFlowListener {
        @Override
        public void afterAudit(String json) {
            log.info("修改用工企业信息审核通过，发送审核通过消息");
            //执行完毕，发送消息插入数据
            notifyFacade.sendOne(MessageMsgDest.TOPIC_MERCHANT_FULL_EDIT, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_MERCHANT_FULL_EDIT, json);
        }
    }

    /**
     * 主体认证失败
     */
    @Component("authFailed")
    public class AuthFailed extends BaseFlowListener {
        @Override
        public void afterAudit(String json) {
            log.info("主体认证失败，更新商户认证状态");
            notifyFacade.sendOne(MessageMsgDest.TOPIC_MAIN_AUTH_CHANGE, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_MERCHANT_VERIFY_DISAGREE_ASYNC, json);
        }
    }

    @Component("createAgentApply")
    public class CreateAgentApplyListener extends BaseFlowListener {
        @Override
        public void afterAudit(String msg) {
            log.info("创建合伙人审批流程结束，发送消息 激活合伙人");
            notifyFacade.sendOne(
                    MessageMsgDest.TOPIC_APPROVAL_ASYNC,
                    NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),
                    MessageMsgDest.TAG_APPROVAL_CREATE_AGENT_ASYNC,
                    msg
            );
        }
    }

    @Component("modifyAgentApply")
    public class ModifyAgentApplyListener extends BaseFlowListener {
        @Override
        public void afterAudit(String msg) {
            log.info("变合伙人审批流程结束，发送消息 激活合伙人");
            notifyFacade.sendOne(
                    MessageMsgDest.TOPIC_APPROVAL_ASYNC,
                    NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),
                    MessageMsgDest.TAG_APPROVAL_MODIFY_AGENT_ASYNC,
                    msg
            );
        }
    }

    @Component("addMerchantNotify")
    public class AddMerchantNotify implements ExecutionListener {
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {


            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }

            notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_CREATE_MERCHANT_NOTIFY, JSON.toJSONString(commonFlow));
        }
    }

    /**
     * 从流程变量里取出topic，tag，多个topic，tag时不能使用
     * 方便简单流程不需要增加更多类，不用修改重启serviceflow服务，只需要部署流程图
     */
    @Component("singleDefaultListener")
    public class SingleDefaultListener implements ExecutionListener {
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {
            String rootProcessInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(rootProcessInstanceId);
            if (commonFlow == null) {
                return;
            }
            Map<String, Object> flowParam = delegateExecution.getTransientVariables();
            String topic = (String) flowParam.get(FlowConstant.DEFAULT_TOPIC);
            String tag = (String) flowParam.get(FlowConstant.DEFAULT_TAG);
            if (StringUtils.isBlank(topic) || StringUtils.isBlank(tag)) {
                return;
            }
            JSONObject jsonObject = JSONObject.parseObject(commonFlow.getExtInfo());
            jsonObject.put("commonFlowId",commonFlow.getId());
            notifyFacade.sendOne(topic, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), tag, jsonObject.toJSONString());
        }
    }

    @Component("applyAgentQuoteEdit")
    public class applyAgentQuoteEdit implements ExecutionListener {
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {

            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }

            log.info("编辑合伙人报价单审批通过：{}",JSON.toJSONString(commonFlow));

            //发送消息处理
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_QUOTE_EDIT, NotifyTypeEnum.AGENT_QUOTE_EDIT.getValue(), MessageMsgDest.TAG_AGENT_QUOTE_EDIT, JSONObject.toJSONString(commonFlow));
        }
    }

    @Component("agentMaininfoAuth")
    public class agentMaininfoAuth implements ExecutionListener {
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {
            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);
            log.info("commonFlow:{}",JSON.toJSONString(commonFlow));
            if (commonFlow == null) {
                return;
            }
            log.info("编辑合伙人主体认证审批通过：{}",JSON.toJSONString(commonFlow));
            //发送消息处理
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_MAININFO_EDIT, NotifyTypeEnum.AGENT_MAININFO_EDIT.getValue(), MessageMsgDest.TAG_AGENT_MAININFO_EDIT, JSONObject.toJSONString(commonFlow));

        }
    }

    @Component("agentBankacctEdit")
    public class agentBankacctEdit implements ExecutionListener {
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {
            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }
            log.info("编辑合伙人银行账户审批通过：{}",JSON.toJSONString(commonFlow));
            //发送消息处理
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_BANKACCT_EDIT, NotifyTypeEnum.AGENT_BANKACCT_EDIT.getValue(), MessageMsgDest.TAG_AGENT_BANKACCT_EDIT, JSONObject.toJSONString(commonFlow));

        }
    }


    @Component("agentSetSeller")
    public class agentSetSeller implements ExecutionListener {
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {
            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }
            log.info("编辑合伙人销售负责人审批通过：{}",JSON.toJSONString(commonFlow));
            //发送消息处理
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_SET_SELLER, NotifyTypeEnum.AGENT_SET_SELLER.getValue(), MessageMsgDest.TAG_AGENT_SET_SELLER, JSONObject.toJSONString(commonFlow));

        }
    }

    @Component("agentSetInviter")
    public class agentSetInviter implements ExecutionListener {
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {
            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }
            //发送消息处理
            log.info("编辑合伙人邀请人审批通过：{}",JSON.toJSONString(commonFlow));
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_SET_INVITER, NotifyTypeEnum.AGENT_SET_INVITER.getValue(), MessageMsgDest.TAG_AGENT_SET_INVITER, JSONObject.toJSONString(commonFlow));

        }
    }

    @Component("agentSetPrincipal")
    public class AgentSetPrincipal implements ExecutionListener {

        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {

            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }
            log.info("编辑合伙人负责人审批通过：{}",JSON.toJSONString(commonFlow));
            //发送消息处理
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_SET_PRINCIPAL, NotifyTypeEnum.AGENT_SET_PRINCIPAL.getValue(), MessageMsgDest.TAG_AGENT_SET_PRINCIPAL, JSONObject.toJSONString(commonFlow));

        }
    }

    @Component("applyAgentQuoteDel")
    public class AgentQuoteDelListener implements ExecutionListener {

        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {

            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }
            log.info("删除合伙人报价单审批通过：{}",JSON.toJSONString(commonFlow));
            //发送消息处理
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_QUOTE_DEL, NotifyTypeEnum.AGENT_QUOTE_DEL.getValue(), MessageMsgDest.TAG_AGENT_QUOTE_DEL, JSONObject.toJSONString(commonFlow));

        }
    }

    @Component("agentBatchSellerInviterSet")
    public class AgentBatchSellerInviterSet implements ExecutionListener {
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {
            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }
            log.info("批量编辑合伙人销售与邀请人审批通过：{}",JSON.toJSONString(commonFlow));
            //发送消息处理
            notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_BATCH_SET_INVITER_SELLER, NotifyTypeEnum.AGENT_BATCH_SET_INVITER_SELLER.getValue(), MessageMsgDest.TAG_AGENT_BATCH_SET_INVITER_SELLER, JSONObject.toJSONString(commonFlow));

        }
    }

    /**
     * 超管发起的任务，超时自动完成
     */
    @Component("adminTaskExpire")
    public class AdminTaskExpire implements ExecutionListener{
        @Autowired
        private CommonFlowBiz commonFlowBiz;

        @Override
        public void notify(DelegateExecution delegateExecution) {
            String processInstanceId = delegateExecution.getRootProcessInstanceId();
            CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);

            if (commonFlow == null) {
                return;
            }
            commonFlowBiz.adminTaskExpire(commonFlow);
        }
    }

}
