package com.zhixianghui.service.flow.core.biz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.constant.NormalConstant;
import com.zhixianghui.facade.flow.dto.CommonFlowVo;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.entity.CommonFlowCarbon;
import com.zhixianghui.facade.flow.entity.CommonFlowLog;
import com.zhixianghui.facade.flow.entity.WorkOrderExt;
import com.zhixianghui.facade.flow.enums.*;
import com.zhixianghui.facade.flow.vo.req.CommonFlowEditVo;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.TaskHandleVo;
import com.zhixianghui.facade.flow.vo.res.TaskResVo;
import com.zhixianghui.facade.flow.vo.work.BaseExtObj;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.flow.core.dao.CommonFlowCarbonDao;
import com.zhixianghui.service.flow.core.dao.CommonFlowDao;
import com.zhixianghui.service.flow.core.dao.CommonFlowLogDao;
import com.zhixianghui.service.flow.core.dao.WorkOrderExtDao;
import com.zhixianghui.service.flow.core.util.FlowUserUtil;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.flowable.engine.*;
import org.flowable.engine.form.FormProperty;
import org.flowable.engine.form.TaskFormData;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.task.api.Task;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2021-04-25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonFlowBiz {

    private final CommonFlowCarbonDao commonFlowCarbonDao;

    private final CommonFlowLogDao commonFlowLogDao;

    private final CommonFlowDao commonflowDao;

    private final WorkOrderExtDao workOrderExtDao;

    private final TaskService taskService;

    private final FormService formService;

    private final HistoryService historyService;

    private final RuntimeService runtimeService;


    @Autowired
    private RedisClient redisClient;

    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private EmployerStaffFacade employerStaffFacade;
    @Reference
    private AgentStaffFacade agentStaffFacade;
    @Reference
    private SupplierStaffFacade supplierStaffFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Value("${flow.sendMail}")
    private String sendMail;

    /**
     * 流程发起记录
     *
     * @param rootProcessInstanceId
     * @param createFlowUser
     * @param commonFlow
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonFlow createRecord(String rootProcessInstanceId, FlowUserVo createFlowUser, CommonFlow commonFlow,
                                   Integer workType, BaseExtObj baseExtObj,List<FlowUserVo> carbonCopyList) {
        commonflowDao.insert(commonFlow);
        String extInfo = commonFlow.getExtInfo();
        //生成记录表
        List<CommonFlowLog> commonFlowLogList = createFlowLog(rootProcessInstanceId, createFlowUser, commonFlow);
        log.info("工作审批流日志为 commonFlowLogList：{}", JsonUtil.toString(commonFlowLogList));

        //插入记录表数据
        commonFlowLogDao.insert(commonFlowLogList);

        //工单类型插入额外字段
        if (workType.intValue() == WorkTypeEnum.WORK_FLOW.getValue()){
            WorkOrderExt workOrderExt = (WorkOrderExt) baseExtObj;
            workOrderExt.setCommonFlowId(commonFlow.getId());
            workOrderExt.setCreateDate(commonFlow.getCreateTime());
            workOrderExtDao.insert((WorkOrderExt) baseExtObj);
        }

        if (carbonCopyList.size() > 0){
            List<CommonFlowCarbon> commonFlowCarbonList = buildCarbon(carbonCopyList,commonFlow.getId());
            commonFlowCarbonDao.insert(commonFlowCarbonList);
        }

        commonFlow.setExtInfo(extInfo);
        //直接更新
        commonflowDao.update(commonFlow);
        return commonFlow;
    }

    private List<CommonFlowCarbon> buildCarbon(List<FlowUserVo> carbonCopyList, Long commonFlowId) {
        List<CommonFlowCarbon> commonFlowCarbonList = new ArrayList<>();
        Date date = new Date();
        carbonCopyList.forEach(x->{
            CommonFlowCarbon commonFlowCarbon = new CommonFlowCarbon();
            commonFlowCarbon.setCommonFlowId(commonFlowId);
            commonFlowCarbon.setCreateTime(date);
            commonFlowCarbon.setPlatform(x.getPlatform());
            commonFlowCarbon.setUserId(x.getUserId());
            commonFlowCarbonList.add(commonFlowCarbon);
        });
        return commonFlowCarbonList;
    }

    /**
     * 流程发起操作记录
     *
     * @param rootProcessInstanceId
     * @param createFlowUser
     * @param commonFlow
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<CommonFlowLog> createFlowLog(String rootProcessInstanceId, FlowUserVo createFlowUser, CommonFlow commonFlow) {
        List<CommonFlowLog> commonFlowLogList = new ArrayList<>();
        CommonFlowLog flowLog = new CommonFlowLog();
        flowLog.setHandlerId(createFlowUser.getUserId());
        flowLog.setHandlerName(StringUtil.isEmpty(createFlowUser.getUserName()) ? createFlowUser.getNo() : createFlowUser.getUserName());
        flowLog.setPlatform(createFlowUser.getPlatform());
        flowLog.setTaskName(TaskHandleTypeEnum.SUBMIT.getDesc());
        flowLog.setStatus(TaskHandleStatusEnum.AGREE.getValue());
        flowLog.setExtInfo("{}");
        flowLog.setApprovalOpinion(commonFlow.getRemark());
        flowLog.setCommonFlowId(commonFlow.getId());
        flowLog.setCreateTime(commonFlow.getCreateTime());
        flowLog.setUpdateTime(commonFlow.getUpdateTime());
        flowLog.setStep(0);
        flowLog.setBelong(createFlowUser.getNo());
        flowLog.setStepName(TaskHandleTypeEnum.SUBMIT.getDesc());
        commonFlowLogList.add(flowLog);
        List<Task> taskList = taskService.createTaskQuery().processInstanceIdWithChildren(rootProcessInstanceId).active().list();
        //流程参与者
        List<FlowUserVo> flowUserVoList = new ArrayList<>();
        //流程节点名称
        Set<String> taskName = new HashSet<>();
        for (Task t : taskList) {
            commonFlowLogList.addAll(taskService.getIdentityLinksForTask(t.getId()).stream().map(identityLink -> {
                CommonFlowLog commonFlowLog = new CommonFlowLog();
                commonFlowLog.setCommonFlowId(commonFlow.getId());
                FlowUserVo fuv = FlowUserUtil.cacheTransferFlowUserVo(getUserCache(identityLink.getUserId()), identityLink.getUserId());
                flowUserVoList.add(fuv);
                commonFlowLog.setHandlerId(fuv.getUserId());
                commonFlowLog.setHandlerName(StringUtil.isEmpty(fuv.getUserName()) ? fuv.getNo() : fuv.getUserName());
                commonFlowLog.setPlatform(fuv.getPlatform());
                commonFlowLog.setBelong(fuv.getNo());
                commonFlowLog.setTaskName(t.getName());
                commonFlowLog.setExtInfo("{}");
                commonFlowLog.setStatus(TaskHandleStatusEnum.PENDING.getValue());
                commonFlowLog.setProcessInstanceId(t.getProcessInstanceId());
                commonFlowLog.setTaskId(t.getId());
                commonFlowLog.setCreateTime(t.getCreateTime());
                commonFlowLog.setUpdateTime(t.getCreateTime());
                commonFlowLog.setStep(commonFlow.getStep());
                commonFlowLog.setStepName(t.getName());
                taskName.add(t.getName());
                return commonFlowLog;
            }).collect(Collectors.toList()));
        }
        buildMailUser(flowUserVoList,commonFlow);
        if (taskName.size() > 0){
            commonFlow.setTaskName(String.join("、",taskName.toArray(new String[taskName.size()])));
        }else{
            commonFlow.setTaskName("");
        }
        return commonFlowLogList;
    }

    private void buildMailUser(List<FlowUserVo> flowUserVoList, CommonFlow commonFlow) {
        if (sendMail.equals(OpenOffEnum.OFF.getValue()+"")){
            return;
        }
        //去重，避免重复发送给同一参与者
        Map<String,Set<String>> map = flowUserVoList.stream().filter(x->!x.getExternalId().equals(FlowConstant.EXTERNAL_ID)).map(x->{
            if (x.getPlatform().intValue() == PlatformSource.OPERATION.getValue()){
                x.setNo(Long.toString(x.getUserId()));
            }
            return x;
        }).collect(Collectors.groupingBy(x->x.getPlatform().toString(),Collectors.mapping(x->x.getNo(),Collectors.toSet())));
        if (map.size() == 0){
            return;
        }
        Map<String,Object> jsonMap = new HashMap<>();
        //避免消息过长
        commonFlow.setExtInfo("");
        jsonMap.put("commonFlow",BeanUtil.toMap(commonFlow));
        jsonMap.put("user",map);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),MessageMsgDest.TAG_FLOW_ARRIVE_MAIL,JSON.toJSONString(jsonMap));
    }

    /**
     * 修改业务操作记录
     *
     * @param commonFlow
     * @param diffInfo
     * @param flowUserVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void editBusinessVariable(CommonFlow commonFlow, String diffInfo, FlowUserVo flowUserVo, String remark, String taskId) {
        commonflowDao.update(commonFlow);
        Task currentTask = taskService.createTaskQuery().taskId(taskId).singleResult();
        //插入操作日志表
        CommonFlowLog commonFlowLog = buildCommonFlowLog(commonFlow.getId(), null, flowUserVo, null,
                TaskHandleTypeEnum.EDIT.getDesc(), diffInfo, remark, commonFlow.getStep(), currentTask.getName());
        commonFlowLogDao.insert(commonFlowLog);
    }

    public PageResult<List<Map<String, Object>>> carbonList(Long currentUserId, Integer platform, Map<String, Object> paramMap, PageParam pageParam) {
        handleWorkType(paramMap);
        PageResult<List<Map<String,Object>>> pageResult;
        Integer workType = (Integer) paramMap.get("workType");
        paramMap.put("userId",currentUserId);
        paramMap.put("carbonPlatform",platform);

        if (workType.intValue() == WorkTypeEnum.AUDIT_FLOW.getValue()){
            pageResult = getList(platform,"carbonList","carbonListCount",paramMap,pageParam);
        }else{
            pageResult = getList(platform,"carbonOrderList","carbonOrderListCount",paramMap,pageParam);
        }
        return pageResult;
    }

    public PageResult<List<Map<String,Object>>> todoList(FlowUserVo flowUserVo, Boolean isAdmin, Map<String, Object> paramMap, PageParam pageParam) {
        handleWorkType(paramMap);
        if (isAdmin  && flowUserVo.getPlatform() != PlatformSource.OPERATION.getValue()) {
            paramMap.put("flowIdList", buildFlowIdList(flowUserVo));
        }
        if (isAdmin  && flowUserVo.getPlatform() != PlatformSource.OPERATION.getValue() && CollectionUtils.isEmpty((Collection<?>) paramMap.get("flowIdList"))) {
            paramMap.put("assignee", FlowUserUtil.flowUserVoTransferFlowId(flowUserVo));
        }
        if (!isAdmin) {
            //注意系统内任务参与者id为platform:currentUserId:userName（平台:用户id:用户名称）
            paramMap.put("assignee", FlowUserUtil.flowUserVoTransferFlowId(flowUserVo));
        }
        PageResult<List<Map<String,Object>>> pageResult;
        Integer workType = (Integer) paramMap.get("workType");
        if (workType.intValue() == WorkTypeEnum.AUDIT_FLOW.getValue()){
            pageResult = getList(flowUserVo.getPlatform(),"todoList","todoListCount",paramMap,pageParam);
        }else{
            pageResult = getList(flowUserVo.getPlatform(),"todoOrderList","todoOrderListCount",paramMap,pageParam);
        }
        return pageResult;
    }

    private List<Long> buildFlowIdList(FlowUserVo flowUserVo) {
        List<CommonFlowLog> flowList = commonFlowLogDao.listBy(new HashMap<String, Object>() {{
            put("belong", flowUserVo.getNo());
        }});
        return flowList.stream().map(CommonFlowLog :: getCommonFlowId).collect(Collectors.toList());
    }

    public CommonFlowVo getCommonFlowById(Long commonFlowId, String taskId, FlowUserVo flowUserVo, boolean isAdmin, Integer platform) {
        CommonFlow commonFlow = commonflowDao.getById(commonFlowId);
        if (commonFlow == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应的流程");
        }
        CommonFlowVo commonFlowVo = new CommonFlowVo();
        BeanUtil.copyProperties(commonFlow, commonFlowVo);
        if (platform != PlatformSource.OPERATION.getValue() && commonFlowVo.getPlatform() == PlatformSource.OPERATION.getValue()) {
            commonFlowVo.setInitiatorName(NormalConstant.OPERATION_NAME);
            commonFlowVo.setRemark(null);
        }
        //获取流程变量
         List<HistoricVariableInstance> variableInstanceList =
                 historyService.createHistoricVariableInstanceQuery().processInstanceId(commonFlow.getProcessInstanceId()).list();
         variableInstanceList.forEach(x->{
             if (x.getVariableName().equals(FlowConstant.REFERENCE_NO)){
                 commonFlowVo.setReferenceNo((String) x.getValue());
             }
             if (x.getVariableName().equals(FlowConstant.REFERENCE_NAME)){
                 commonFlowVo.setReferenceName((String) x.getValue());
             }
         });

        buildCommonFlow(commonFlowVo, taskId, isAdmin, flowUserVo);
        return commonFlowVo;
    }

    private void buildCommonFlow(CommonFlowVo commonFlowVo, String taskId, boolean isAdmin, FlowUserVo flowUserVo) {

        //处理撤回审批操作操作
        if (commonFlowVo.getInitiatorId().equals(flowUserVo.getUserId()) && commonFlowVo.getPlatform().intValue() == flowUserVo.getPlatform().intValue()) {
            commonFlowVo.setIsInitUser(true);
        } else {
            commonFlowVo.setIsInitUser(false);
        }

        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        //没有任务Id说明是从已办、发起进来的
        if (StringUtil.isEmpty(taskId) || task == null) {
            commonFlowVo.setIsHandleUser(false);
            commonFlowVo.setIsEdit(false);
            commonFlowVo.setIsChangHandler(false);
            commonFlowVo.setIsBack(false);
            return;
        }

        //判断是否可以驳回
        TaskFormData taskFormData = formService.getTaskFormData(taskId);
        if (taskFormData != null && taskFormData.getFormProperties() != null && taskFormData.getFormProperties().size() > 0) {
            FormProperty formProperty = taskFormData.getFormProperties().stream().filter(x ->
                    x.getId().equals(FlowConstant.BACK_FORM_ID)).findFirst().orElse(null);
            if (formProperty != null) {
                commonFlowVo.setIsBack(Boolean.parseBoolean(formProperty.getValue()));
            } else {
                commonFlowVo.setIsBack(false);
            }
        } else {
            commonFlowVo.setIsBack(false);
        }

        //判断是否可以编辑
        if (taskFormData != null && taskFormData.getFormProperties() != null && taskFormData.getFormProperties().size() > 0) {
            //判断是否可以编辑
            FormProperty formProperty = taskFormData.getFormProperties().stream().filter(x ->
                    x.getId().equals(FlowConstant.EDIT_FORM_ID)).findFirst().orElse(null);
            if (formProperty != null) {
                commonFlowVo.setIsEdit(Boolean.parseBoolean(formProperty.getValue()));
            } else {
                commonFlowVo.setIsEdit(false);
            }
        } else {
            commonFlowVo.setIsEdit(false);
        }

        //管理员可以执行所有操作，驳回操作在上一步已判断
        if (isAdmin) {
            commonFlowVo.setIsHandleUser(true);
            commonFlowVo.setIsChangHandler(false);
            commonFlowVo.setIsInitUser(true);
            return;
        }

        //判断此用户是否为任务处理人
        task = taskService.createTaskQuery().taskId(taskId).taskCandidateOrAssigned(FlowUserUtil.flowUserVoTransferFlowId(flowUserVo)).singleResult();
        if (task == null) {
            commonFlowVo.setIsHandleUser(false);
            commonFlowVo.setIsEdit(false);
            commonFlowVo.setIsChangHandler(false);
            commonFlowVo.setIsBack(false);
            return;
        }

        //属于任务处理人，可以处理任务，变更审批人
        commonFlowVo.setIsChangHandler(true);
        commonFlowVo.setIsHandleUser(true);
    }

    public CommonFlow getById(Long id) {
        return commonflowDao.getById(id);
    }


    public PageResult<List<Map<String,Object>>> sendList(Long currentUserId, Integer platform, Boolean isAdmin, Map<String, Object> paramMap, PageParam pageParam) {
        handleWorkType(paramMap);
        //管理员可以查看所有审批
        if (isAdmin  && platform != PlatformSource.OPERATION.getValue()) {
            FlowUserVo flowUserVo = new FlowUserVo();
            flowUserVo.setNo((String) paramMap.get("mchNo"));
            flowUserVo.setPlatform(platform);
            paramMap.put("flowIdList", buildFlowIdList(flowUserVo));
        }
        if (isAdmin  && platform != PlatformSource.OPERATION.getValue() && CollectionUtils.isEmpty((Collection<?>) paramMap.get("flowIdList"))) {
            paramMap.put("initiatorId", currentUserId);
            paramMap.put("platform", platform);
        }
        if (!isAdmin) {
            //注意系统内任务参与者id为platform:currentUserId:userName（平台:用户id:用户名称）
            paramMap.put("initiatorId", currentUserId);
            paramMap.put("platform", platform);
        }
        PageResult<List<Map<String,Object>>> pageResult = getList(platform,"sendList","countBy",paramMap,pageParam);
        return pageResult;
    }

    public PageResult<List<Map<String,Object>>> handList(Long currentUserId, Integer platform, Boolean isAdmin, Map<String, Object> paramMap, PageParam pageParam) {
        handleWorkType(paramMap);
        if (isAdmin  && platform != PlatformSource.OPERATION.getValue()) {
            FlowUserVo flowUserVo = new FlowUserVo();
            flowUserVo.setNo((String) paramMap.get("mchNo"));
            flowUserVo.setPlatform(platform);
            paramMap.put("flowIdList", buildFlowIdList(flowUserVo));
        }
        if (isAdmin  && platform != PlatformSource.OPERATION.getValue() && CollectionUtils.isEmpty((Collection<?>) paramMap.get("flowIdList"))) {
            paramMap.put("handlerId", currentUserId);
            paramMap.put("platform", platform);
        }
        if (!isAdmin) {
            //注意系统内任务参与者id为platform:currentUserId:userName（平台:用户id:用户名称）
            paramMap.put("handlerId", currentUserId);
            paramMap.put("platform", platform);
        }
        PageResult<List<Map<String,Object>>> pageResult;
        Integer workType = (Integer) paramMap.get("workType");
        if (workType.intValue() == WorkTypeEnum.AUDIT_FLOW.getValue()){
            pageResult = getList(platform,"handList","handListCount",paramMap,pageParam);
        }else{
            pageResult = getList(platform,"handOrderList","handOrderCount",paramMap,pageParam);
        }
        return pageResult;
    }

    /**
     * 执行任务记录
     *
     * @param taskHandleVo
     * @param currentUser
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeTaskRecord(TaskHandleVo taskHandleVo, FlowUserVo currentUser, Boolean isAdmin, Task currentTask) {
        log.info("执行审批 taskHandleVo：{} currentUser：{} isAdmin：{} taskId：{} processInstanceId:{} taskName:{}", JsonUtil.toString(taskHandleVo),
                JsonUtil.toString(currentUser), isAdmin, currentTask.getId(), currentTask.getProcessInstanceId(), currentTask.getName());
        //更新记录表
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("taskId", taskHandleVo.getTaskId());
        paramMap.put("handlerId", currentUser.getUserId());
        paramMap.put("platform", currentUser.getPlatform());
        //要考虑候选人taskId相同的情况
        CommonFlowLog commonFlowLog = commonFlowLogDao.getOne("getSingle", paramMap);

        Long spentTime = historyService.createHistoricTaskInstanceQuery().taskId(currentTask.getId()).singleResult().getDurationInMillis() / 1000;

        Date date = new Date();
        if (commonFlowLog != null) {
            log.info("当前条件查询出的日志记录存在，将更新 taskId：{} processInstanceId:{} taskName:{}", currentTask.getId(), currentTask.getProcessInstanceId(), currentTask.getName());
            commonFlowLog.setUpdateTime(date);
            commonFlowLog.setStatus(taskHandleVo.getStatus());
            commonFlowLog.setApprovalOpinion(taskHandleVo.getOpinion());
            commonFlowLog.setPlatform(currentUser.getPlatform());
            commonFlowLog.setHandlerName(StringUtil.isEmpty(currentUser.getUserName()) ? currentUser.getNo() : currentUser.getUserName());
            commonFlowLog.setHandlerId(currentUser.getUserId());
            commonFlowLog.setExtInfo("{}");
            commonFlowLog.setSpentTime(spentTime);
            commonFlowLogDao.update(commonFlowLog);
        } else {
            log.info("当前条件查询出的日志记录不存在，属于处理人变更操作 taskId：{} processInstanceId:{} taskName:{}", currentTask.getId(), currentTask.getProcessInstanceId(), currentTask.getName());
            paramMap.clear();
            paramMap.put("taskId", taskHandleVo.getTaskId());
            commonFlowLog = commonFlowLogDao.getOne("getSingle", paramMap);
            //插入新数据
            List<CommonFlowLog> list = new ArrayList<>();
            String comment;
            if (StringUtil.isNotEmpty(currentTask.getAssignee())) {
                FlowUserVo oldUserVo = FlowUserUtil.cacheTransferFlowUserVo(getUserCache(currentTask.getAssignee()), currentTask.getAssignee());
                comment = "任务处理人由：" +
                        (StringUtil.isEmpty(oldUserVo.getUserName()) ? oldUserVo.getNo() : oldUserVo.getUserName()) +
                        "  变更为：" + currentUser.getUserName();
            } else {
                comment = "变更任务处理人为" + currentUser.getUserName();
            }

            list.add(buildCommonFlowLog(taskHandleVo.getCommonFlowId(), null, currentUser, null
                    , TaskHandleTypeEnum.TRANSFER.getDesc(), "{}", comment, commonFlowLog.getStep(), commonFlowLog.getStepName()));

            CommonFlowLog log = buildCommonFlowLog(taskHandleVo.getCommonFlowId(), currentTask.getProcessInstanceId(), currentUser, taskHandleVo.getStatus(),
                    currentTask.getName(), "{}", taskHandleVo.getOpinion(), commonFlowLog.getStep(), commonFlowLog.getStepName());

            log.setTaskId(currentTask.getId());
            log.setSpentTime(spentTime);
            list.add(log);

            commonFlowLogDao.insert(list);
        }

        CommonFlow commonFlow = commonflowDao.getById(taskHandleVo.getCommonFlowId());
        commonFlow.setUpdateTime(date);
        //查询所有已插入日志的任务id
        Map<String, Object> map = Maps.newHashMap();
        map.put("commonFlowId", taskHandleVo.getCommonFlowId());
        List<String> alreadyTaskList = commonFlowLogDao.listBy(map).stream().filter(l -> StringUtil.isNotEmpty(l.getTaskId())).map(CommonFlowLog::getTaskId).collect(Collectors.toList());
        List<FlowUserVo> flowUserVoList = new ArrayList<>();
        //记录节点名称
        Set<String> taskName = new HashSet();
        //提交任务后查询所有当前活动任务，并插入到操作日志记录表，注意过滤已插入的任务日志
        List<Task> taskList = taskService.createTaskQuery().processInstanceIdWithChildren(commonFlow.getProcessInstanceId()).active().list();
        if (taskList != null && !taskList.isEmpty()) {
            List<CommonFlowLog> commonFlowLogList = new ArrayList<>();
            for (Task task : taskList) {
                if (alreadyTaskList.contains(task.getId())) {
                    log.info("当前任务流程已存在于操作日志记录表，将忽略 taskId：{} processInstanceId:{} taskName:{}", task.getId(), task.getProcessInstanceId(), task.getName());
                    continue;
                }
                List<IdentityLink> identityLinkList = taskService.getIdentityLinksForTask(task.getId());
                if (identityLinkList == null || identityLinkList.isEmpty()) {
                    log.info("当前任务流程获取到的IdentityLink为空，将忽略 taskId：{} processInstanceId:{} taskName:{}", task.getId(), task.getProcessInstanceId(), task.getName());
                    continue;
                }

                log.info("当前任务流程将新增到操作日志记录表 taskId：{} processInstanceId:{} taskName:{}", task.getId(), task.getProcessInstanceId(), task.getName());
                commonFlowLogList.addAll(identityLinkList.stream().map(identityLink -> {
                    CommonFlowLog flowLog = new CommonFlowLog();
                    flowLog.setCreateTime(task.getCreateTime());
                    flowLog.setUpdateTime(task.getCreateTime());
                    flowLog.setCommonFlowId(commonFlow.getId());
                    //参与者
                    FlowUserVo user = FlowUserUtil.cacheTransferFlowUserVo(getUserCache(identityLink.getUserId()), identityLink.getUserId());
                    flowUserVoList.add(user);
                    flowLog.setHandlerId(user.getUserId());
                    flowLog.setHandlerName(user.getUserName());
                    flowLog.setPlatform(user.getPlatform());
                    flowLog.setBelong(user.getNo());
                    flowLog.setStatus(TaskHandleStatusEnum.PENDING.getValue());
                    flowLog.setTaskName(task.getName());
                    flowLog.setProcessInstanceId(task.getProcessInstanceId());
                    flowLog.setTaskId(task.getId());
                    flowLog.setStep(commonFlow.getStep() + 1);
                    flowLog.setStepName(task.getName());
                    taskName.add(task.getName());
                    return flowLog;
                }).collect(Collectors.toList()));
            }

            if (commonFlowLogList.isEmpty()) {
                log.warn("本次审批预期会有新的操作日志记录，但实际上没有，将可能导致审批流程一直卡在'待审批'状态 taskId：{} processInstanceId:{} taskName:{}", currentTask.getId(), currentTask.getProcessInstanceId(), currentTask.getName());
            } else {
                log.info("本次审批导致新增{}条操作日志记录 taskId：{} processInstanceId:{} taskName:{} commonFlowLogList:{}",
                        commonFlowLogList.size(), currentTask.getId(), currentTask.getProcessInstanceId(), currentTask.getName(),
                        JsonUtil.toString(commonFlowLogList));
                commonFlowLogDao.insert(commonFlowLogList);
                commonFlow.setStep(commonFlow.getStep() + 1);
            }
        } else {
            log.info("当前审批流程已到达结束 taskId：{} processInstanceId:{} taskName:{}", currentTask.getId(), currentTask.getProcessInstanceId(), currentTask.getName());
            commonFlow.setEndTime(date);
            commonFlow.setStatus(FlowStatusEnum.FINISHED.getValue());
            String userId = commonFlow.getPlatform()+":"+commonFlow.getInitiatorId();
            FlowUserVo user = FlowUserUtil.cacheTransferFlowUserVo(getUserCache(userId), userId);
            flowUserVoList.add(user);
        }

        if (taskName.size() > 0){
            commonFlow.setTaskName(String.join("、",taskName.toArray(new String[taskName.size()])));
        }else{
            commonFlow.setTaskName("");
        }

        commonflowDao.update(commonFlow);
        buildMailUser(flowUserVoList,commonFlow);
    }

    /**
     * 撤回审批记录
     *
     * @param commonFlow
     * @param currentUser
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessInstance(CommonFlow commonFlow, FlowUserVo currentUser, String reason) {
        Date date = new Date();
        commonFlow.setUpdateTime(date);
        commonFlow.setEndTime(date);
        commonFlow.setStatus(FlowStatusEnum.CANCELED.getValue());
        commonFlow.setStep(commonFlow.getStep() + 1);
        commonFlow.setTaskName(TaskHandleTypeEnum.RECALL.getDesc());
        commonflowDao.update(commonFlow);
        CommonFlowLog commonFlowLog = buildCommonFlowLog(commonFlow.getId(), null, currentUser, null,
                TaskHandleTypeEnum.RECALL.getDesc(), "{}", reason, commonFlow.getStep(), TaskHandleTypeEnum.RECALL.getDesc());
        commonFlowLogDao.insert(commonFlowLog);
    }

    /**
     * 根据根实例id获取流程
     *
     * @param processInstanceId
     * @return
     */
    public CommonFlow getByRootProcessInstanceId(String processInstanceId) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("processInstanceId", processInstanceId);
        return commonflowDao.getOne(paramMap);
    }

    public List<CommonFlow> getByBusinessKey(String businessKey) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("businessKey", businessKey);
        return commonflowDao.listBy(paramMap);
    }


    public boolean isExistNotFinishedFlow(FlowTypeEnum flowTypeEnum,String ... businessKey ) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("flowTopicType", flowTypeEnum.getFlowTopicType());
        paramMap.put("status", FlowStatusEnum.PENDING.getValue());
        paramMap.put("businessKeyList", businessKey);
        CommonFlow commonFlow = commonflowDao.getOne("getExistNotFinishedFlow", paramMap);
        if (commonFlow != null) {
            return true;
        } else {
            return false;
        }
    }

    public boolean isExistNotFinishedUpdateQuoteFlow(FlowTypeEnum flowTypeEnum, String businessKey) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("flowTopicType", flowTypeEnum.getFlowTopicType());
        paramMap.put("status", FlowStatusEnum.FINISHED.getValue());
        paramMap.put("businessKey", businessKey);
        CommonFlow commonFlow = commonflowDao.getOne("getExistNotFinishedUpdateQuoteFlow", paramMap);
        if (commonFlow != null) {
            return true;
        } else {
            return false;
        }
    }

    //获取用户信息
    public Map<String, String> getUserCache(String key) {
        if (key.equals(FlowConstant.EXTERNAL_ID)){
            return null;
        }
        Map<String, String> map = redisClient.hgetAll(key);
        if (map == null || map.size() <= 0) {
            String[] userKey = key.split(":");
            PlatformSource platformSource = PlatformSource.getEnum(Integer.parseInt(userKey[0]));
            if (platformSource == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无法适配平台枚举类，未预期的值：" + userKey[0]);
            }
            Long id = Long.parseLong(userKey[1]);
            switch (platformSource) {
                case OPERATION:
                    pmsOperatorFacade.getAndPutOperatorCache(id);
                    break;
                case MERCHANT:
                    employerStaffFacade.getAndPutStaffCache(id);
                    break;
                case SUPPLIER:
                    supplierStaffFacade.getAndPutStaffCache(id);
                    break;
                case AGENT:
                    agentStaffFacade.getAndPutStaffCache(id);
            }
            map = redisClient.hgetAll(key);
        }
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public void transferTaskLog(CommonFlowEditVo commonFlowEditVo, FlowUserVo currentUser, FlowUserVo nextUser, Task task) {
        //同时修改原记录taskId和processInstanceId为空避免冲突
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("taskId", commonFlowEditVo.getTaskId());
        paramMap.put("handlerId", currentUser.getUserId());
        paramMap.put("platform", currentUser.getPlatform());
        CommonFlowLog commonFlowLog = commonFlowLogDao.getOne("getSingle", paramMap);
        commonFlowLog.setProcessInstanceId(null);
        commonFlowLog.setTaskId(null);
        commonFlowLogDao.update(commonFlowLog);
        //插入新数据
        List<CommonFlowLog> list = new ArrayList<>();
        list.add(buildCommonFlowLog(commonFlowEditVo.getCommonFlowId(), null, currentUser, null
                , TaskHandleTypeEnum.TRANSFER.getDesc(), "{}", "变更任务处理人为：" + nextUser.getUserName()
                , commonFlowLog.getStep(), commonFlowLog.getStepName()));

        CommonFlowLog log = buildCommonFlowLog(commonFlowEditVo.getCommonFlowId(), null, nextUser, TaskHandleStatusEnum.PENDING.getValue()
                , task.getName(), "{}", "", commonFlowLog.getStep(), commonFlowLog.getStepName());

        log.setTaskId(commonFlowEditVo.getTaskId());
        list.add(log);
        commonFlowLogDao.insert(list);
        buildMailUser(new ArrayList<FlowUserVo>(){{add(nextUser);}},commonflowDao.getById(commonFlowEditVo.getCommonFlowId()));
    }

    private CommonFlowLog buildCommonFlowLog(Long commonFlowId, String processInstance, FlowUserVo flowUserVo, Integer status,
                                             String taskName, String extInfo, String approvalOpinion, Integer step, String stepName) {
        Date date = new Date();
        CommonFlowLog commonFlowLog = new CommonFlowLog();
        commonFlowLog.setProcessInstanceId(processInstance);
        commonFlowLog.setCreateTime(date);
        commonFlowLog.setUpdateTime(date);
        commonFlowLog.setCommonFlowId(commonFlowId);
        commonFlowLog.setPlatform(flowUserVo.getPlatform());
        commonFlowLog.setHandlerId(flowUserVo.getUserId());
        commonFlowLog.setHandlerName(StringUtil.isEmpty(flowUserVo.getUserName()) ? flowUserVo.getNo() : flowUserVo.getUserName());
        commonFlowLog.setStatus(status);
        commonFlowLog.setTaskName(taskName);
        commonFlowLog.setApprovalOpinion(approvalOpinion);
        commonFlowLog.setExtInfo(extInfo);
        commonFlowLog.setStep(step);
        commonFlowLog.setStepName(stepName);
        commonFlowLog.setBelong(flowUserVo.getNo());
        return commonFlowLog;
    }

    public void reply(CommonFlowEditVo commonFlowEditVo, FlowUserVo flowUserVo) {
        CommonFlow commonFlow = commonflowDao.getById(commonFlowEditVo.getCommonFlowId());
        commonFlow.setStep(commonFlow.getStep() + 1);
        commonFlow.setUpdateTime(new Date());
        CommonFlowLog commonFlowLog = buildCommonFlowLog(commonFlow.getId(), null, flowUserVo, null, TaskHandleTypeEnum.RELPY.getDesc(), "{}",
                commonFlowEditVo.getContent(), commonFlow.getStep(), TaskHandleTypeEnum.RELPY.getDesc());
        commonflowDao.update(commonFlow);
        commonFlowLogDao.insert(commonFlowLog);
    }

    public boolean isExistTask(FlowUserVo flowUserVo) {
       String userId = FlowUserUtil.flowUserVoTransferFlowId(flowUserVo);
       long count = commonflowDao.countBy("identitylinkTask",new HashMap<String,Object>(){{put("userId",userId);}});
       if (count == 0){
           return false;
       }
       return true;
    }

    private void handleWorkType(Map<String, Object> paramMap) {
        Integer workType = (Integer) paramMap.get("workType");
        if (workType == null){
            //默认查询审批流
            paramMap.put("workType", WorkTypeEnum.AUDIT_FLOW.getValue());
        }else{
            paramMap.put("workType", workType);
        }
    }

    /**
     * 查询并处理敏感数据
     * @param listSql
     * @param countSql
     * @param paramMap
     * @param pageParam
     * @return
     */
    private PageResult<List<Map<String, Object>>> getList(int platform,String listSql,String countSql,Map<String,Object> paramMap,PageParam pageParam){
        PageResult<List<Map<String,Object>>> pageResult;
        pageResult = commonflowDao.listPage(listSql, countSql, paramMap, pageParam,true);
        pageResult.getData().forEach(x -> {
            if (platform != PlatformSource.OPERATION.getValue() && ((Integer)x.get("platform")).intValue() == PlatformSource.OPERATION.getValue()) {
                x.put("initiatorName",NormalConstant.OPERATION_NAME);
                x.put("salerName","");
                x.put("agentName","");
            }
        });
        return pageResult;
    }

    public PageResult<List<Map<String,Object>>> workOrderPage(Integer platform,Map<String, Object> paramMap, PageParam toPageParam) {
        paramMap.put("workType",WorkTypeEnum.WORK_FLOW.getValue());
        return getList(platform,"workOrderList","workOrderCount",paramMap,toPageParam);
    }

    public List<CommonFlow> listBy(Map<String, Object> paramMap) {
        return commonflowDao.listBy(paramMap);
    }

    public void update(CommonFlow commonFlow) {
        commonflowDao.update(commonFlow);
    }

    public List<CommonFlow> getCommonFlowByParamMap(Map<String, Object> paramMap) {
        return commonflowDao.listBy(paramMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByCommonFlowId(Long commonFlowId) {
        CommonFlow commonFlow = getById(commonFlowId);
        if (commonFlow.getStatus().intValue() == FlowStatusEnum.PENDING.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该流程尚未完成，无法删除");
        }
        //删除流程主体
        commonflowDao.deleteById(commonFlowId);
        //删除审批意见
        commonFlowLogDao.deleteBy("deleteByCommonFlowId",commonFlowId);
        //删除抄送数据
        commonFlowCarbonDao.deleteBy("deleteByCommonFlowId",commonFlowId);
        //删除工单数据
        workOrderExtDao.deleteBy("deleteByCommonFlowId",commonFlowId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void adminTaskExpire(CommonFlow commonFlow) {
        commonFlow.setEndTime(new Date());
        commonFlow.setStatus(FlowStatusEnum.FINISHED.getValue());
        commonFlow.setTaskName("");
        commonflowDao.update(commonFlow);

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("handlerId",commonFlow.getInitiatorId());
        paramMap.put("platform",PlatformSource.OPERATION.getValue());
        paramMap.put("status",TaskHandleStatusEnum.PENDING.getValue());
        paramMap.put("commonFlowId",commonFlow.getId());
        CommonFlowLog commonFlowLog = commonFlowLogDao.getOne(paramMap);
        commonFlowLog.setUpdateTime(new Date());
        commonFlowLog.setStatus(TaskHandleStatusEnum.AGREE.getValue());
        commonFlowLog.setApprovalOpinion("超时自动完成");
        commonFlowLog.setExtInfo("{}");
        commonFlowLogDao.update(commonFlowLog);

    }
}