package com.zhixianghui.service.flow.core.listener;

import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.service.flow.core.biz.CommonFlowBiz;
import org.flowable.engine.HistoryService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName BaseFlowListener
 * @Description TODO
 * @Date 2021/5/11 17:38
 */
@Component
public abstract class BaseFlowListener implements ExecutionListener {

    @Autowired
    private HistoryService historyService;
    @Autowired
    private CommonFlowBiz commonFlowBiz;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        String processInstanceId = delegateExecution.getRootProcessInstanceId();
        CommonFlow commonFlow = commonFlowBiz.getByRootProcessInstanceId(processInstanceId);
//        if (commonFlow == null){
//            //commonFlow为null，说明是一步完成
//            String extInfo = (String) delegateExecution.getVariable(FlowConstant.EXT_OBJ);
//            afterAudit(extInfo);
//        }else{
            afterAudit(commonFlow.getExtInfo());
        //}
    }

    public abstract void afterAudit(String json);
}
