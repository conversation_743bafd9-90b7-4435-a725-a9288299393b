package com.zhixianghui.service.flow.config;

import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName FlowableEnableEntityLinkConfiguration
 * @Description 引擎配置类
 * @Date 2021/4/27 18:05
 */
@Configuration
public class FlowableEnableEntityLinkConfiguration implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    @Override
    public void configure(SpringProcessEngineConfiguration springProcessEngineConfiguration) {
        //启用实体连接
        springProcessEngineConfiguration.setEnableEntityLinks(true);
    }

    @Bean
    public FlowableEnableEntityLinkConfiguration getConfig(){
        return this;
    }
}
