package com.zhixianghui.service.flow.core.biz;

import com.zhixianghui.service.flow.core.dao.WorkOrderExtDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2022-04-01
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WorkOrderExtBiz {

    private final WorkOrderExtDao workorderextDao;

    public List<Long> getCommonFlowId(Map<String, Object> paramMap) {
        return workorderextDao.listBy("getCommonFlowId",paramMap);
    }
}