package com.zhixianghui.service.flow.core.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.flow.dto.CommonFlowLogVo;
import com.zhixianghui.facade.flow.dto.CommonFlowVo;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.entity.CommonFlowLog;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.CommonFlowEditVo;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.res.TaskResVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.flow.vo.req.TaskHandleVo;
import com.zhixianghui.service.flow.core.biz.CommonFlowBiz;
import com.zhixianghui.service.flow.core.biz.CommonFlowLogBiz;
import com.zhixianghui.service.flow.core.biz.ProcessInstanceBiz;
import com.zhixianghui.service.flow.core.biz.TaskBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowFacadeImpl
 * @Description TODO
 * @Date 2021/4/25 9:07
 */
@Service(retries = -1,timeout = 15000)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FlowFacadeImpl implements FlowFacade {

    @Autowired
    private ProcessInstanceBiz processInstanceBiz;

    @Autowired
    private CommonFlowBiz commonFlowBiz;

    @Autowired
    private TaskBiz taskBiz;

    @Autowired
    private CommonFlowLogBiz commonFlowLogBiz;

    @Override
    public PageResult<List<Map<String,Object>>> handleList(Long currentUserId, Integer platform, Boolean isAdmin, Map<String, Object> paramMap, PageParam pageParam) {
        return commonFlowBiz.handList(currentUserId,platform,isAdmin,paramMap,pageParam);
    }

    @Override
    public PageResult<List<Map<String,Object>>> sendList(Long currentUserId, Integer platform, Boolean isAdmin, Map<String, Object> paramMap, PageParam pageParam) {
        return commonFlowBiz.sendList(currentUserId,platform,isAdmin,paramMap,pageParam);
    }

    @Override
    public PageResult<List<Map<String,Object>>> todoList(FlowUserVo flowUserVo,Boolean isAdmin, Map<String,Object> paramMap, PageParam pageParam) {
        return commonFlowBiz.todoList(flowUserVo,isAdmin,paramMap,pageParam);
    }

    @Override
    public PageResult<List<Map<String, Object>>> carbonList(Long currentUserId, Integer platform, Map<String, Object> paramMap, PageParam pageParam) {
        return commonFlowBiz.carbonList(currentUserId,platform,paramMap,pageParam);
    }

    @Override
    public CommonFlow startProcessByProcessDefinitionKey(ProcessVo processVo,FlowUserVo createFlowUser, Map<String,List<FlowUserVo>> userMap,Map<String,Object> flowParam) throws BizException{
         return processInstanceBiz.startProcessByProcessDefinitionKey(processVo,createFlowUser,userMap,flowParam);
    }

    @Override
    public void editBusinessVariable(CommonFlowEditVo commonFlowEditVo,FlowUserVo flowUserVo,Boolean isAdmin) throws BizException{
        processInstanceBiz.editBusinessVariable(commonFlowEditVo,flowUserVo,isAdmin);
    }

    @Override
    public void executeTask(TaskHandleVo taskHandleVo,FlowUserVo currentUser,Boolean isAdmin) throws BizException {
        taskBiz.executeTask(taskHandleVo,currentUser,isAdmin);
    }

    @Override
    public void transferTask(CommonFlowEditVo commonFlowEditVo, FlowUserVo currentUser, FlowUserVo nextUser) throws BizException{
        taskBiz.transferTask(commonFlowEditVo,currentUser,nextUser);
    }

    @Override
    public void deleteProcessInstance(Long commonFlowId, FlowUserVo currentUser,Boolean isAdmin,String reason) {
        processInstanceBiz.deleteProcessInstance(commonFlowId,currentUser,isAdmin,reason);
    }

    @Override
    public List<CommonFlowLogVo> getDetailByCommonFlowId(Long commonFlowId, Integer platform) {
        return commonFlowLogBiz.getDetailByCommonFlowId(commonFlowId,platform);
    }

    @Override
    public CommonFlowVo getCommonFlowById(Long commonFlowId, String taskId, FlowUserVo flowUserVo,boolean isAdmin,Integer platform) {
        return commonFlowBiz.getCommonFlowById(commonFlowId,taskId,flowUserVo,isAdmin,platform);
    }

    @Override
    public CommonFlow getByRootProcessInstanceId(String processInstanceId) {
        return commonFlowBiz.getByRootProcessInstanceId(processInstanceId);
    }

    @Override
    public List<CommonFlow> getCommonFlowByParam(Map<String, Object> paramMap) {
        return commonFlowBiz.getCommonFlowByParamMap(paramMap);
    }

    @Override
    public boolean isExistNotFinishedFlow( FlowTypeEnum flowTypeEnum,String ... businessKey) {
        return commonFlowBiz.isExistNotFinishedFlow(flowTypeEnum,businessKey);
    }

    @Override
    public boolean isExistNotFinishedUpdateQuoteFlow(FlowTypeEnum flowTypeEnum, String businessKey) {
        return commonFlowBiz.isExistNotFinishedUpdateQuoteFlow(flowTypeEnum,businessKey);
    }

    @Override
    public void reply(CommonFlowEditVo commonFlowEditVo,FlowUserVo flowUserVo) {
        commonFlowBiz.reply(commonFlowEditVo,flowUserVo);
    }

    @Override
    public CommonFlow getId(Long commonFlowId) {
        return commonFlowBiz.getById(commonFlowId);
    }

    @Override
    public boolean isExistTask(FlowUserVo flowUserVo) {
        return commonFlowBiz.isExistTask(flowUserVo);
    }

    @Override
    public PageResult<List<Map<String,Object>>> workOrderPage(Integer platform,Map<String, Object> paramMap, PageParam toPageParam) {
        return commonFlowBiz.workOrderPage(platform,paramMap,toPageParam);
    }

    @Override
    public List<CommonFlow> listBy(Map<String, Object> paramMap) {
        return commonFlowBiz.listBy(paramMap);
    }

    @Override
    public void update(CommonFlow commonFlow) {
        commonFlowBiz.update(commonFlow);
    }

    @Override
    public void deleteByCommonFlowId(Long commonFlowId) {
        commonFlowBiz.deleteByCommonFlowId(commonFlowId);
    }

}
