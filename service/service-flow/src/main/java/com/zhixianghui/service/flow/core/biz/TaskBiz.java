package com.zhixianghui.service.flow.core.biz;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.vo.req.CommonFlowEditVo;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.TaskHandleVo;
import com.zhixianghui.service.flow.core.util.FlowUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.IdentityService;
import org.flowable.engine.TaskService;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName TaskBiz
 * @Description TODO
 * @Date 2021/4/25 17:18
 */
@Slf4j
@Service
public class TaskBiz {

    @Autowired
    private IdentityService identityService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private CommonFlowBiz commonFlowBiz;

    public TaskQuery taskQuery(){
        return taskService.createTaskQuery();
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeTask(TaskHandleVo taskHandleVo, FlowUserVo currentUser,Boolean isAdmin) throws BizException {
        Task task = taskQuery().taskId(taskHandleVo.getTaskId()).singleResult();
        if (task == null){
            log.info("找不到id：[{}]的任务，请确认是否正确",taskHandleVo.getTaskId());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到任务，任务为空");
        }

        String handldUserId = FlowUserUtil.flowUserVoTransferFlowId(currentUser);
        List<String> partipantList = taskService.getIdentityLinksForTask(task.getId()).stream().map(IdentityLinkInfo::getUserId).collect(Collectors.toList());

        if (!isAdmin && !partipantList.contains(handldUserId)){
            log.info("用户:[{}]不属于任务办理人:[{}]",handldUserId,taskHandleVo.getTaskId());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前用户不属于此任务办理人");
        }

        Map<String,List<FlowUserVo>> userList = new HashMap<>();
        if (taskHandleVo.getParticipant() != null){
            userList = taskHandleVo.getParticipant();
        }

        Map<String,Object> paramMap = new HashMap<>();
        if (taskHandleVo.getCondition() != null){
            paramMap = taskHandleVo.getCondition();
        }

        if (StringUtil.isEmpty(task.getAssignee())){
            taskService.claim(task.getId(),handldUserId);
        }

        //审批通过、拒绝
        paramMap.put(FlowConstant.FLOW_STATUS,taskHandleVo.getStatus());
        // 遍历参数列表，取出用户id放入流程参数中
        for (Map.Entry<String,List<FlowUserVo>> entry : userList.entrySet()){
            List<String> userIdList = entry.getValue().stream().map(flowUserVo -> FlowUserUtil.flowUserVoTransferFlowId(flowUserVo)).collect(Collectors.toList());
            paramMap.put(entry.getKey(),userIdList);
        }

        if (DelegationState.PENDING.equals(task.getDelegationState())){
            //委派类型
            taskService.addComment(task.getId(),task.getProcessInstanceId(), taskHandleVo.getOpinion());
            taskService.resolveTask(task.getId(),paramMap);
        }else{
            //普通类型
            taskService.complete(task.getId(),paramMap);
        }
        log.info("提交任务，处理人：[{}]，处理任务id：[{}]",currentUser.getUserName(),task.getId());
        commonFlowBiz.executeTaskRecord(taskHandleVo,currentUser,isAdmin,task);
    }

    @Transactional(rollbackFor = Exception.class)
    public void transferTask(CommonFlowEditVo commonFlowEditVo, FlowUserVo currentUser, FlowUserVo nextUser) throws BizException{
        Task task = taskQuery().taskId(commonFlowEditVo.getTaskId()).singleResult();
        if (task == null){
            log.info("找不到id：[{}]的任务，请确认是否正确",commonFlowEditVo.getTaskId());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到任务，任务为空");
        }

        String currentUserId = FlowUserUtil.flowUserVoTransferFlowId(currentUser);
        String nextUserId = FlowUserUtil.flowUserVoTransferFlowId(nextUser);

        //判断属于候选任务还是签收任务
        if (StringUtil.isEmpty(task.getAssignee())){
            List<String> idList = taskService.getIdentityLinksForTask(task.getId()).stream().map(IdentityLinkInfo::getUserId).collect(Collectors.toList());
            if (idList.contains(currentUserId)){
                //删除此候选人
                taskService.deleteCandidateUser(task.getId(),currentUserId);
            }else{
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前用户不属于此任务候选人，无法变更审批人");
            }

            //任务候选人里不存在此用户才需要添加，避免重复
            if (!idList.contains(nextUserId)){
                taskService.addCandidateUser(task.getId(),nextUserId);
            }

        }else{
            //签收任务，直接分配
            taskService.setAssignee(commonFlowEditVo.getTaskId(),nextUserId);
        }

        commonFlowBiz.transferTaskLog(commonFlowEditVo,currentUser,nextUser,task);
        log.info("任务：[{}],审批人从用户：[{}] 变更为用户：[{}]",commonFlowEditVo.getTaskId(),currentUserId,
                FlowUserUtil.flowUserVoTransferFlowId(nextUser));
    }
}
