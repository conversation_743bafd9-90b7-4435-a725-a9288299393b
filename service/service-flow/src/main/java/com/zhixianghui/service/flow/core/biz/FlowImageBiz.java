package com.zhixianghui.service.flow.core.biz;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.service.flow.core.helper.CustomProcessDiagramGenerator;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.*;
import org.flowable.bpmn.model.Process;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.image.ProcessDiagramGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ImageBiz
 * @Description TODO
 * @Date 2021/4/23 14:12
 */
@Slf4j
@Service
public class FlowImageBiz {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RedisClient redisClient;



    /**
     * 根据流程定义id获取完整流程图
     * @param processDefinitionId
     * @return
     */
    public byte[] getDefinitionImage(String processDefinitionId) throws BizException, IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        InputStream imageStream = null;
        try{
            imageStream = repositoryService.getProcessDiagram(processDefinitionId);
            byte[] buffer = new byte[1024];
            int n = 0;
            while (-1 != (n = imageStream.read(buffer))){
                output.write(buffer,0,n);
            }
            return output.toByteArray();
        }catch (Exception e){
            log.error("流程图生成失败，失败定义id：{}，检查流程图是否有误",processDefinitionId);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("流程图生成异常，异常信息："+e.getMessage());
        }finally {
            if (imageStream != null){
                imageStream.close();
            }
            output.close();
        }
    }

    /**
     * 根据流程实例id获取流程追踪图
     * @param processInstanceId
     * @return
     */
    public String getInstanceImage(String processInstanceId) throws BizException,IOException{
        //从缓存中获取，降低资源消耗
        String image = redisClient.get(FlowConstant.IMAGE_KEY + processInstanceId);
        if (StringUtil.isNotEmpty(image)){
            return image;
        }
        InputStream imageStream = null;
        try {
            // 通过流程实例ID获取历史流程实例
            HistoricProcessInstance historicProcessInstance = getHistoricProcInst(processInstanceId);
            // 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
            List<HistoricActivityInstance> historicActivityInstanceList = getHistoricActivityInstAsc(processInstanceId);
            //排除线的集合
            historicActivityInstanceList = historicActivityInstanceList.stream()
                    .filter(historicActivityInstance -> !historicActivityInstance.getActivityType().equals("sequenceFlow")).collect(Collectors.toList());
            // 将已经执行的节点ID放入高亮显示节点集合
            List<String> highLightedActivitiIdList = new ArrayList<>();
            for (HistoricActivityInstance historicActivityInstance : historicActivityInstanceList) {
                highLightedActivitiIdList.add(historicActivityInstance.getActivityId());
                log.info("已执行的节点[{}-{}-{}-{}]", historicActivityInstance.getId(), historicActivityInstance
                        .getActivityId(), historicActivityInstance.getActivityName(), historicActivityInstance
                        .getAssignee());
            }
            // 通过流程实例ID获取流程中正在执行的节点
            List<Execution> runningActivityInstanceList = getRunningActivityInst(processInstanceId);
            List<String> runningActivitiIdList = new ArrayList<String>();
            for (Execution execution : runningActivityInstanceList) {
                if (StringUtils.isNotEmpty(execution.getActivityId())) {
                    runningActivitiIdList.add(execution.getActivityId());
                    log.info("执行中的节点[{}-{}-{}]", execution.getId(), execution.getActivityId(), execution.getName());
                }
            }
            // 定义流程画布生成器
            ProcessDiagramGenerator processDiagramGenerator = null;
            processDiagramGenerator = new CustomProcessDiagramGenerator();
            // 获取流程定义Model对象
            BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());
            // 获取已流经的流程线，需要高亮显示高亮流程已发生流转的线id集合
            List<String> highLightedFlowIds = getHighLightedFlows(bpmnModel, historicActivityInstanceList);
            // 使用默认配置获得流程图表生成器，并生成追踪图片字符流
            imageStream = ((CustomProcessDiagramGenerator) processDiagramGenerator)
                    .generateDiagramCustom(bpmnModel, "png",
                            highLightedActivitiIdList, runningActivitiIdList, highLightedFlowIds,
                            "宋体", "微软雅黑", "黑体",
                            null, 2.0);
            // 将InputStream数据流转换为byte[]
            byte[] buffer = new byte[imageStream.available()];
            imageStream.read(buffer);

            String base64 = Base64.getEncoder().encodeToString(buffer);
            //设置三天过期
            redisClient.set(FlowConstant.IMAGE_KEY+processInstanceId,base64,FlowConstant.IMAGE_EXPIRE);
            return base64;
        } catch (Exception e) {
            log.error("通过流程实例ID[{}]获取流程图时出现异常！", processInstanceId);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通过流程实例ID" + processInstanceId + "获取流程图时出现异常！"+e.getMessage());
        } finally {
            if (imageStream != null) {
                imageStream.close();
            }
        }
    }

    /**
     * 通过流程实例ID获取流程中正在执行的节点
     * @param processInstanceId
     * @return
     */
    private List<Execution> getRunningActivityInst(String processInstanceId) {
       return runtimeService.createExecutionQuery().processInstanceId(processInstanceId).list();
    }

    /**
     * 通过流程实例ID获取流程中已经执行的节点，按照执行先后顺序排序
     * @param processInstanceId
     * @return
     */
    private List<HistoricActivityInstance> getHistoricActivityInstAsc(String processInstanceId) {
        return historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceId().asc().list();
    }

    /**
     * 查询历史流程实例
     * @param processInstanceId
     * @return
     */
    private HistoricProcessInstance getHistoricProcInst(String processInstanceId) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
    }

    /**
     * 获取流程图需要高亮的线
     * @param bpmnModel
     * @param historicActivityInstanceList
     * @return
     */
    public List<String> getHighLightedFlows(BpmnModel bpmnModel,
                                            List<HistoricActivityInstance> historicActivityInstanceList) {
        // 已流经的流程线，需要高亮显示
        List<String> highLightedFlowIdList = new ArrayList<>();
        // 全部活动节点
        List<FlowNode> allHistoricActivityNodeList = new ArrayList<>();
        // 已完成的历史活动节点
        List<HistoricActivityInstance> finishedActivityInstanceList = new ArrayList<>();

        for (HistoricActivityInstance historicActivityInstance : historicActivityInstanceList) {
            Process process = bpmnModel.getMainProcess();
            FlowElement fl = bpmnModel.getMainProcess().getFlowElement(historicActivityInstance
                    .getActivityId(), true);
            // 获取流程节点
            FlowNode flowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstance
                    .getActivityId(), true);
            allHistoricActivityNodeList.add(flowNode);
            // 结束时间不为空，当前节点则已经完成
            if (historicActivityInstance.getEndTime() != null) {
                finishedActivityInstanceList.add(historicActivityInstance);
            }
        }

        FlowNode currentFlowNode = null;
        FlowNode targetFlowNode = null;
        HistoricActivityInstance currentActivityInstance;
        // 遍历已完成的活动实例，从每个实例的outgoingFlows中找到已执行的
        for (int k = 0; k < finishedActivityInstanceList.size(); k++) {
            currentActivityInstance = finishedActivityInstanceList.get(k);
            // 获得当前活动对应的节点信息及outgoingFlows信息
            currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(currentActivityInstance
                    .getActivityId(), true);
            // 当前节点的所有流出线
            List<SequenceFlow> outgoingFlowList = currentFlowNode.getOutgoingFlows();

            /**
             * 遍历outgoingFlows并找到已流转的 满足如下条件认为已流转：
             * 1.当前节点是并行网关或兼容网关，则通过outgoingFlows能够在历史活动中找到的全部节点均为已流转
             * 2.当前节点是以上两种类型之外的，通过outgoingFlows查找到的时间最早的流转节点视为有效流转
             * (第2点有问题，有过驳回的，会只绘制驳回的流程线，通过走向下一级的流程线没有高亮显示)
             */
            if ("parallelGateway".equals(currentActivityInstance.getActivityType()) || "inclusiveGateway".equals(
                    currentActivityInstance.getActivityType())) {
                // 遍历历史活动节点，找到匹配流程目标节点的
                for (SequenceFlow outgoingFlow : outgoingFlowList) {
                    // 获取当前节点流程线对应的下级节点
                    targetFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(outgoingFlow.getTargetRef(),
                            true);
                    // 如果下级节点包含在所有历史节点中，则将当前节点的流出线高亮显示
                    if (allHistoricActivityNodeList.contains(targetFlowNode)) {
                        highLightedFlowIdList.add(outgoingFlow.getId());
                    }
                }
            } else {
                /**
                 * 2、当前节点不是并行网关或兼容网关
                 * 【已解决-问题】如果当前节点有驳回功能，驳回到申请节点，
                 * 则因为申请节点在历史节点中，导致当前节点驳回到申请节点的流程线被高亮显示，但实际并没有进行驳回操作
                 */
                List<Map<String, Object>> tempMapList = new ArrayList<>();
                // 当前节点ID
                String currentActivityId = currentActivityInstance.getActivityId();
                int size = historicActivityInstanceList.size();
                boolean ifStartFind = false;
                boolean ifFinded = false;
                HistoricActivityInstance historicActivityInstance;
                // 循环当前节点的所有流出线
                // 循环所有历史节点
                log.info("【开始】-匹配当前节点-ActivityId=【{}】需要高亮显示的流出线", currentActivityId);
                log.info("循环历史节点");
                for (int i = 0; i < historicActivityInstanceList.size(); i++) {
                    // 历史节点
                    historicActivityInstance = historicActivityInstanceList.get(i);
                    log.info("第【{}/{}】个历史节点-ActivityId=[{}]", i + 1, size, historicActivityInstance.getActivityId());
                    // 如果循环历史节点中的id等于当前节点id，从当前历史节点继续先后查找是否有当前节点流程线等于的节点
                    // 历史节点的序号需要大于等于已完成历史节点的序号，防止驳回重审一个节点经过两次是只取第一次的流出线高亮显示，第二次的不显示
                    if (i >= k && historicActivityInstance.getActivityId().equals(currentActivityId)) {
                        log.info("第[{}]个历史节点和当前节点一致-ActivityId=[{}]", i + 1, historicActivityInstance
                                .getActivityId());
                        ifStartFind = true;
                        // 跳过当前节点继续查找下一个节点
                        continue;
                    }
                    if (ifStartFind) {
                        log.info("[开始]-循环当前节点-ActivityId=【{}】的所有流出线", currentActivityId);

                        ifFinded = false;
                        for (SequenceFlow sequenceFlow : outgoingFlowList) {
                            // 如果当前节点流程线对应的下级节点在其后面的历史节点中，则该条流程线进行高亮显示
                            // 【问题】
                            log.info("当前流出线的下级节点=[{}]", sequenceFlow.getTargetRef());
                            if (historicActivityInstance.getActivityId().equals(sequenceFlow.getTargetRef())) {
                                log.info("当前节点[{}]需高亮显示的流出线=[{}]", currentActivityId, sequenceFlow.getId());
                                highLightedFlowIdList.add(sequenceFlow.getId());
                                // 暂时默认找到离当前节点最近的下一级节点即退出循环，否则有多条流出线时将全部被高亮显示
                                ifFinded = true;
                                break;
                            }
                        }
                        log.info("[完成]-循环当前节点-ActivityId=【{}】的所有流出线", currentActivityId);
                    }
                    if (ifFinded) {
                        // 暂时默认找到离当前节点最近的下一级节点即退出历史节点循环，否则有多条流出线时将全部被高亮显示
                        break;
                    }
                }
                log.info("【完成】-匹配当前节点-ActivityId=【{}】需要高亮显示的流出线", currentActivityId);
            }

        }
        return highLightedFlowIdList;
    }
}
