package com.zhixianghui.service.flow.aspect;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.CommonFlowEditVo;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Date 2021/7/16 9:43
 */
@Aspect
@Component
@Slf4j
public class RecordAspect {

    @Reference
    private FlowFacade flowFacade;
    @Autowired
    protected FlowHandleContext context;

    @Around("@annotation(com.zhixianghui.service.flow.annotation.ModifyRecord)")
    public void doAround(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        CommonFlowEditVo commonFlowEditVo = null;
        FlowUserVo flowUserVo = null;
        for (Object obj : args) {
            if (obj instanceof CommonFlowEditVo) {
                commonFlowEditVo = (CommonFlowEditVo) obj;
            }
            if (obj instanceof FlowUserVo) {
                flowUserVo = (FlowUserVo) obj;
            }
        }
        if (commonFlowEditVo == null) {
            log.info("待编辑信息为空...");
            point.proceed();
            return;
        }
        // 查出旧流程
        CommonFlow oldFlow = flowFacade.getId(commonFlowEditVo.getCommonFlowId());
        try {
            point.proceed();
        } catch (Exception e) {
            log.error("编辑出现异常 {}: ", JSONObject.toJSON(commonFlowEditVo), e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("审批流编辑出现异常:" + e);
        }
        Builder builder = context.getBuilder(oldFlow.getFlowTopicType());
        if (builder == null) {
            builder = context.getBuilder(FlowTypeEnum.OTHER.getFlowTopicType());
            builder.build(oldFlow, commonFlowEditVo, flowUserVo);
            return;
        }
        builder.build(oldFlow, commonFlowEditVo, flowUserVo);

    }
}
