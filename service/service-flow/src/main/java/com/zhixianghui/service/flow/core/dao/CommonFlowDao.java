package com.zhixianghui.service.flow.core.dao;

import com.zhixianghui.facade.flow.dto.WorkOrderExtDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.common.service.dao.MyBatisDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 *  Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2021-04-25
 */
@Repository
public class CommonFlowDao extends MyBatisDao<CommonFlow,Long> {

    public WorkOrderExtDto getJoinEntityByProcessInstanceId(String processInstanceId) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("processInstanceId",processInstanceId);
        return this.getSqlSession().selectOne(fillSqlId("getJoinEntityByProcessInstanceId"), processInstanceId);
    }
}
