package com.zhixianghui.service.flow.core.facade;

import com.zhixianghui.facade.flow.service.CommonFlowCarbonFacade;
import com.zhixianghui.service.flow.core.biz.CommonFlowCarbonBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-06-09
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonFlowCarbonImpl implements CommonFlowCarbonFacade {

    private final CommonFlowCarbonBiz biz;
}
