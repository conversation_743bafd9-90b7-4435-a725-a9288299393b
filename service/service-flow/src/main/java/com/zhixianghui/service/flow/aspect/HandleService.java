package com.zhixianghui.service.flow.aspect;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.merchant.EditTypeEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.DataObjectEnum;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.vo.req.CommonFlowEditVo;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.ChangeSourceEnum;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.service.MerchantInfoChangeRecordFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentProductQuoteVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentVo;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.facade.merchant.vo.record.AgentBaseInfoVo;
import com.zhixianghui.facade.merchant.vo.record.AgentProductFeeVo;
import com.zhixianghui.facade.merchant.vo.record.MerchantBaseInfoVo;

import com.zhixianghui.facade.merchant.vo.record.MerchantProductFeeVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/22 16:37
 */
@Component
public class HandleService {

    @Reference
    private MerchantInfoChangeRecordFacade changeRecordFacade;

    @FlowType(value = FlowTypeEnum.PMS_CHANGE_SALER)
    @Service
    public class ChangeSaleHandle implements Builder {
        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
          changeSale(oldFlow, editVo, userVo);
        }
    }

    private void changeSale(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
        MerchantBaseInfoVo oldBaseInfo = MerchantFlowVo.buildSale(MerchantFlowVo.parse(oldFlow.getExtInfo()));
        MerchantBaseInfoVo newBaseInfo =  MerchantFlowVo.buildSale(MerchantFlowVo.parse(editVo.getExtInfo()));
        newBaseInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
        changeRecordFacade.record(newBaseInfo, oldBaseInfo, ChangeSourceEnum.FLOW.getSource());
    }

    @FlowType(value = FlowTypeEnum.PMS_MAINSTAY_CHANGE_SALER)
    @Service
    public class MainstayChangeSaleHandle implements Builder {

        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
            changeSale(oldFlow, editVo, userVo);
        }
    }

    @FlowType(value = FlowTypeEnum.PMS_CHANGE_ACCOUNT)
    @Service
    public class ChangeAccountHandle implements Builder {

        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
            changeSale(oldFlow, editVo, userVo);
        }
    }

    @FlowType(value =  FlowTypeEnum.COMMON_CHANGE_PRINCIPAL)
    @Service
    public class ChangePrincipalHandle implements Builder {

        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
            MerchantBaseInfoVo oldBaseInfo = MerchantFlowVo.buildContactPerson(MerchantFlowVo.parse(oldFlow.getExtInfo()));
            MerchantBaseInfoVo newBaseInfo =  MerchantFlowVo.buildContactPerson(MerchantFlowVo.parse(editVo.getExtInfo()));
            newBaseInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
            changeRecordFacade.record(newBaseInfo, oldBaseInfo, ChangeSourceEnum.FLOW.getSource());
        }
    }

    @FlowType(value =  FlowTypeEnum.PMS_MCH_CREATE_PRODUCT)
    @Service
    public class CreateProductHandle implements Builder {

        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
            MerchantFlowVo merchantFlowVo = MerchantFlowVo.parse(editVo.getExtInfo());
            MerchantProductFeeVo newInfo = MerchantFlowVo.buildQuote(merchantFlowVo, OperationEnum.MODIFY.getOperation());
            newInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
            changeRecordFacade.record(newInfo, MerchantProductFeeVo.newInstance(), ChangeSourceEnum.FLOW.getSource());
        }
    }

    @FlowType(value = FlowTypeEnum.PMS_MERCHANT_FREEZE)
    @Service
    public class ChangeStatusHandle implements Builder {

        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
            MerchantBaseInfoVo oldBaseInfo = MerchantFlowVo.buildMchStatus(MerchantFlowVo.parse(oldFlow.getExtInfo()));
            MerchantBaseInfoVo newBaseInfo =  MerchantFlowVo.buildMchStatus(MerchantFlowVo.parse(editVo.getExtInfo()));
            newBaseInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
            changeRecordFacade.record(newBaseInfo, oldBaseInfo, ChangeSourceEnum.FLOW.getSource());
        }
    }

    @FlowType(value = FlowTypeEnum.PMS_AGENT_MODIFY)
    @Service
    public class AgentInfoHandle implements Builder {

        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
            AgentBaseInfoVo oldBaseInfo = AgentVo.buildInfo(oldFlow.getExtInfo());
            AgentBaseInfoVo newBaseInfo = AgentVo.buildInfo(editVo.getExtInfo());
            newBaseInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
            changeRecordFacade.record(oldBaseInfo, newBaseInfo, ChangeSourceEnum.FLOW.getSource());
        }
    }

    @Service
    @FlowType(value = FlowTypeEnum.PMS_AGENT_QUOTE_EDIT)
    public class AgentEditQuoteInfo implements Builder {
        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
            List<AgentProductFeeVo> oldFeeVo = JSONArray.parseArray(oldFlow.getExtInfo(), AgentProductFeeVo.class);
            LinkedList<AgentProductFeeVo> oldFeeVoList = new LinkedList<>(oldFeeVo);
            List<AgentProductFeeVo>  newFeeVo = JSONArray.parseArray(editVo.getExtInfo(), AgentProductFeeVo.class);
            LinkedList<AgentProductFeeVo> newFeeVoList = new LinkedList<>(newFeeVo);

            int index = 0;
            while (!CollectionUtils.isEmpty(oldFeeVoList) && !CollectionUtils.isEmpty(newFeeVoList)) {
                AgentProductFeeVo oldInfo = oldFeeVoList.get(index);
                AgentProductFeeVo newInfo = newFeeVoList.get(index);
                newInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
                changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
                oldFeeVoList.remove();
                newFeeVoList.remove();
            }

            if (CollectionUtils.isEmpty(oldFeeVoList) && CollectionUtils.isEmpty(newFeeVoList)) {
                return;
            }
            if (CollectionUtils.isEmpty(oldFeeVoList)) {
                for (AgentProductFeeVo feeVo : newFeeVoList) {
                    AgentProductFeeVo oldInfo = AgentProductFeeVo.getInstance();
                    feeVo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
                    changeRecordFacade.record(feeVo, oldInfo, ChangeSourceEnum.FLOW.getSource());
                }
            }

            if (CollectionUtils.isEmpty(newFeeVoList)) {
                for (AgentProductFeeVo feeVo : oldFeeVoList) {
                    AgentProductFeeVo newInfo = AgentProductFeeVo.getInstance();
                    newInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
                    newInfo.setAgentNo(feeVo.getAgentNo());
                    newInfo.setAgentName(feeVo.getAgentName());
                    changeRecordFacade.record(newInfo, feeVo, ChangeSourceEnum.FLOW.getSource());
                }
            }


        }
    }

    @FlowType(value = FlowTypeEnum.OTHER)
    @Service
    public class OtherInfoHandle implements Builder {

        @Override
        public void build(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {

            String[] result = oldFlow.getFlowTopicType().split(":");
            // 100 用工企业 101 合伙人  102 供应商
            if (DataObjectEnum.CREATE_AGENT.getValue() == Integer.parseInt(result[1])) {
                handleAgentInfo(oldFlow, editVo, userVo);
                return;
            }
            handleMerchantInfo(oldFlow, editVo, userVo);
        }
    }

    private void handleMerchantInfo(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
        MerchantBaseInfoVo oldBaseInfo = JSONObject.parseObject(oldFlow.getExtInfo(), MerchantBaseInfoVo.class);
        MerchantBaseInfoVo newBaseInfo = JSONObject.parseObject(editVo.getExtInfo(), MerchantBaseInfoVo.class);
        newBaseInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
        changeRecordFacade.record(newBaseInfo, oldBaseInfo, ChangeSourceEnum.FLOW.getSource());
    }

    private void handleAgentInfo(CommonFlow oldFlow, CommonFlowEditVo editVo, FlowUserVo userVo) {
        AgentBaseInfoVo oldBaseInfo = JSONObject.parseObject(oldFlow.getExtInfo(), AgentBaseInfoVo.class);
        AgentBaseInfoVo newBaseInfo = JSONObject.parseObject(editVo.getExtInfo(), AgentBaseInfoVo.class);
        newBaseInfo.setOperatorInfo(userVo.getUserId(), userVo.getUserName(), oldFlow.getId());
        changeRecordFacade.record(newBaseInfo, oldBaseInfo, ChangeSourceEnum.FLOW.getSource());
    }
}
