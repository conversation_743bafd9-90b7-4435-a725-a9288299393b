package com.zhixianghui.service.flow.core.facade;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.flow.service.ProcessDefinitionFacade;
import com.zhixianghui.facade.flow.vo.req.ProcessDefinitionReqVo;
import com.zhixianghui.facade.flow.vo.res.ProcessDefinitionResVo;
import com.zhixianghui.service.flow.core.biz.ProcessDefinitionBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ProcessDefinitionImpl
 * @Description 流程定义实现类
 * @Date 2021/4/23 11:54
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProcessDefinitionImpl implements ProcessDefinitionFacade {

    private final ProcessDefinitionBiz processDefinitionBiz;

    @Override
    public void deploy(String name, String platform, String dataObj, String triggerAct, byte[] bytes, String originalFilename, String tenantId, String currentUserName, String description) throws BizException {
        processDefinitionBiz.deploy(name, platform, dataObj, triggerAct, bytes, originalFilename, tenantId, currentUserName, description);
    }

    @Override
    public PageResult<List<ProcessDefinitionResVo>> listProcessDefinition(Map<String, Object> paramMap, PageParam pageParam) {
        return processDefinitionBiz.listPage(paramMap, pageParam);
    }

    @Override
    public void updateProcessDefinitionState(String id, Integer state) throws BizException {
        processDefinitionBiz.updateProcessDefinitionState(id, state);
    }

    @Override
    public void delete(String id) throws BizException{
        processDefinitionBiz.delete(id);
    }

    @Override
    public ProcessDefinitionResVo getById(String id) {
        return processDefinitionBiz.getById(id);
    }

    @Override
    public void update(String id, String dataObj, String triggerAct, String name, String platform, String desc, String tenantId, String currentUserName) {
        processDefinitionBiz.update(id, dataObj, triggerAct, name, platform, desc, tenantId, currentUserName);
    }
}
