package com.zhixianghui.service.flow.core.facade;

import com.zhixianghui.facade.flow.service.WorkOrderExtFacade;
import com.zhixianghui.service.flow.core.biz.WorkOrderExtBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-04-01
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WorkOrderExtImpl implements WorkOrderExtFacade {

    private final WorkOrderExtBiz biz;

    @Override
    public List<Long> getCommonFlowId(Map<String, Object> paramMap) {
        return biz.getCommonFlowId(paramMap);
    }
}
