<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.flow.entity.CommonFlowCarbon">
    <sql id="table">tbl_common_flow_carbon</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.flow.entity.CommonFlowCarbon">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="common_flow_id" property="commonFlowId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="platform" property="platform" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, common_flow_id, user_id, platform, create_time
    </sql>


    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            version,
            common_flow_id,
            user_id,
            platform,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            0,
            #{item.commonFlowId,jdbcType=BIGINT},
            #{item.userId,jdbcType=BIGINT},
            #{item.platform,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <select id="deleteByCommonFlowId" parameterType="java.lang.Long" resultType="long">
        delete FROM
        <include refid="table" />
        where COMMON_FLOW_ID = #{commonFlowId}
    </select>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.flow.entity.CommonFlowCarbon">
        UPDATE <include refid="table" /> SET
            version = #{version,jdbcType=SMALLINT},
            common_flow_id = #{commonFlowId,jdbcType=BIGINT},
            user_id = #{userId,jdbcType=BIGINT},
            platform = #{platform,jdbcType=SMALLINT},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.flow.entity.CommonFlowCarbon">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="commonFlowId != null">
                common_flow_id = #{commonFlowId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="commonFlowId != null">
            and common_flow_id = #{commonFlowId,jdbcType=BIGINT}
        </if>
        <if test="userId != null">
            and user_id = #{userId,jdbcType=BIGINT}
        </if>
        <if test="platform != null">
            and platform = #{platform,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

</mapper>
