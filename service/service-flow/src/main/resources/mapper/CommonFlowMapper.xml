<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.flow.entity.CommonFlow">
    <sql id="table">tbl_common_flow</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.flow.entity.CommonFlow">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="INITIATOR_ID" property="initiatorId" jdbcType="BIGINT"/>
        <result column="INITIATOR_NAME" property="initiatorName" jdbcType="VARCHAR"/>
        <result column="WORK_TYPE" property="workType" jdbcType="SMALLINT"/>
        <result column="FLOW_TOPIC_TYPE" property="flowTopicType" jdbcType="VARCHAR"/>
        <result column="FLOW_TOPIC_NAME" property="flowTopicName" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="EXT_INFO" property="extInfo" jdbcType="OTHER"/>
        <result column="TASK_NAME" property="taskName" jdbcType="VARCHAR"/>
        <result column="CURRENT_USER_ID" property="currentUserId" jdbcType="VARCHAR"/>
        <result column="CURRENT_USER_NAME" property="currentUserName" jdbcType="VARCHAR"/>
        <result column="PROCESS_INSTANCE_ID" property="processInstanceId" jdbcType="VARCHAR"/>
        <result column="PLATFORM" property="platform" jdbcType="SMALLINT"/>
        <result column="BUSINESS_KEY" property="businessKey" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="STEP" property="step" jdbcType="SMALLINT"/>

    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, INITIATOR_ID, WORK_TYPE, INITIATOR_NAME, FLOW_TOPIC_TYPE, FLOW_TOPIC_NAME, CREATE_TIME, UPDATE_TIME, END_TIME, STATUS, EXT_INFO,TASK_NAME, CURRENT_USER_ID, CURRENT_USER_NAME, PROCESS_INSTANCE_ID, PLATFORM,BUSINESS_KEY,REMARK,STEP
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.flow.entity.CommonFlow">
        INSERT INTO <include refid="table" /> (
            VERSION,
            INITIATOR_ID,
            INITIATOR_NAME,
            WORK_TYPE,
            FLOW_TOPIC_TYPE,
            FLOW_TOPIC_NAME,
            CREATE_TIME,
            UPDATE_TIME,
            END_TIME,
            STATUS,
            EXT_INFO,
            TASK_NAME,
            CURRENT_USER_ID,
            CURRENT_USER_NAME,
            PROCESS_INSTANCE_ID,
            PLATFORM,
            BUSINESS_KEY,
            REMARK,
            STEP
        ) VALUES (
            #{version,jdbcType=INTEGER},
            #{initiatorId,jdbcType=BIGINT},
            #{initiatorName,jdbcType=VARCHAR},
            #{workType,jdbcType=SMALLINT},
            #{flowTopicType,jdbcType=VARCHAR},
            #{flowTopicName,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{endTime,jdbcType=TIMESTAMP},
            #{status,jdbcType=SMALLINT},
            #{extInfo,jdbcType=OTHER},
            #{taskName,jdbcType=VARCHAR},
            #{currentUserId,jdbcType=VARCHAR},
            #{currentUserName,jdbcType=VARCHAR},
            #{processInstanceId,jdbcType=VARCHAR},
            #{platform,jdbcType=SMALLINT},
            #{businessKey,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR},
            #{step,jdbcType=SMALLINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            INITIATOR_ID,
            INITIATOR_NAME,
            WORK_TYPE,
            FLOW_TOPIC_TYPE,
            FLOW_TOPIC_NAME,
            CREATE_TIME,
            UPDATE_TIME,
            END_TIME,
            STATUS,
            EXT_INFO,
            TASK_NAME,
            CURRENT_USER_ID,
            CURRENT_USER_NAME,
            PROCESS_INSTANCE_ID,
            PLATFORM,
            BUSINESS_KEY,
            REMARK,
            STEP
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=INTEGER},
            #{item.initiatorId,jdbcType=BIGINT},
            #{item.initiatorName,jdbcType=VARCHAR},
            #{item.workType,jdbcType=SMALLINT},
            #{item.flowTopicType,jdbcType=VARCHAR},
            #{item.flowTopicName,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.endTime,jdbcType=TIMESTAMP},
            #{item.status,jdbcType=SMALLINT},
            #{item.extInfo,jdbcType=OTHER},
            #{item.taskName,jdbcType=VARCHAR},
            #{item.currentUserId,jdbcType=VARCHAR},
            #{item.currentUserName,jdbcType=VARCHAR},
            #{item.processInstanceId,jdbcType=VARCHAR},
            #{item.platform.jdbcType=SMALLINT},
            #{item.businessKey,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.step,jdbcType=SMALLINT}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.flow.entity.CommonFlow">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=INTEGER} +1,
            INITIATOR_ID = #{initiatorId,jdbcType=BIGINT},
            INITIATOR_NAME = #{initiatorName,jdbcType=VARCHAR},
            WORK_TYPE = #{workType,jdbcType=SMALLINT},
            FLOW_TOPIC_TYPE = #{flowTopicType,jdbcType=VARCHAR},
            FLOW_TOPIC_NAME = #{flowTopicName,jdbcType=VARCHAR},
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            END_TIME = #{endTime,jdbcType=TIMESTAMP},
            STATUS = #{status,jdbcType=SMALLINT},
            EXT_INFO = #{extInfo,jdbcType=OTHER},
            TASK_NAME = #{taskName,jdbcType=VARCHAR},
            CURRENT_USER_ID = #{currentUserId,jdbcType=VARCHAR},
            CURRENT_USER_NAME = #{currentUserName,jdbcType=VARCHAR},
            PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR},
            PLATFORM = #{platform,jdbcType=SMALLINT},
            BUSINESS_KEY = #{businessKey,jdbcType=VARCHAR},
            REMARK = #{remark,jdbcType=VARCHAR},
            STEP = #{step,jdbcType=SMALLINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.flow.entity.CommonFlow">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="initiatorId != null">
                INITIATOR_ID = #{initiatorId,jdbcType=BIGINT},
            </if>
            <if test="initiatorName != null">
                INITIATOR_NAME = #{initiatorName,jdbcType=VARCHAR},
            </if>
            <if test="workType != null">
                WORK_TYPE = #{workType,jdbcType=SMALLINT},
            </if>
            <if test="flowTopicType != null">
                FLOW_TOPIC_TYPE = #{flowTopicType,jdbcType=VARCHAR},
            </if>
            <if test="flowTopicName != null">
                FLOW_TOPIC_NAME = #{flowTopicName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                END_TIME = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="extInfo != null">
                EXT_INFO = #{extInfo,jdbcType=OTHER},
            </if>
            <if test="taskName != null">
                TASK_NAME = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="currentUserId != null">
                CURRENT_USER_ID = #{currentUserId,jdbcType=VARCHAR},
            </if>
            <if test="currentUserName != null">
                CURRENT_USER_NAME = #{currentUserName,jdbcType=VARCHAR},
            </if>
            <if test="processInstanceId != null">
                PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=SMALLINT},
            </if>
            <if test="businessKey != null">
                BUSINESS_KEY = #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="step != null">
                STEP = #{step,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <select id="sendList" parameterType="java.util.Map" resultType="java.util.Map">
        select f.id as commonFlowId,
        f.INITIATOR_ID as initiatorId,
        f.INITIATOR_NAME as initiatorName,
        f.FLOW_TOPIC_TYPE as flowTopicType,
        f.FLOW_TOPIC_NAME as flowTopicName,
        f.CREATE_TIME as createTime,
        f.STATUS as status,
        f.PLATFORM as platform
        from <include refid="table" /> f
        <where>
            <include refid="condition_sql" />
        </where>
        order by f.create_time desc
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="initiatorId != null">
            and INITIATOR_ID = #{initiatorId,jdbcType=BIGINT}
        </if>
        <if test="initiatorName != null and initiatorName !=''">
            and INITIATOR_NAME like concat('%',#{initiatorName,jdbcType=VARCHAR},'%')
        </if>
        <if test="workType != null and workType != ''">
            and WORK_TYPE = #{workType,jdbcType=SMALLINT}
        </if>
        <if test="flowTopicType != null">
            and FLOW_TOPIC_TYPE = #{flowTopicType,jdbcType=VARCHAR}
        </if>
        <if test="flowTopicName != null and flowTopicName !=''">
            and FLOW_TOPIC_NAME like concat('%',#{flowTopicName,jdbcType=VARCHAR},'%')
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and END_TIME = #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="status != null and status != ''">
            and STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="extInfo != null and extInfo !=''">
            and EXT_INFO = #{extInfo,jdbcType=OTHER}
        </if>
        <if test="currentUserId != null and currentUserId !=''">
            and CURRENT_USER_ID = #{currentUserId,jdbcType=VARCHAR}
        </if>
        <if test="currentUserName != null and currentUserName !=''">
            and CURRENT_USER_NAME = #{currentUserName,jdbcType=VARCHAR}
        </if>
        <if test="processInstanceId != null and processInstanceId !=''">
            and PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="platform != null and platform != ''">
            and PLATFORM = #{platform,jdbcType=SMALLINT}
        </if>
        <if test="businessKey != null and businessKey !=''">
            and BUSINESS_KEY = #{businessKey,jdbcType=VARCHAR}
        </if>
        <if test="step != null">
            and STEP = #{step,jdbcType=SMALLINT}
        </if>
        <if test="flowTopicTypeList != null and flowTopicTypeList.size() > 0">
            and FLOW_TOPIC_TYPE in
            <foreach collection="flowTopicTypeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="businessKeyLike != null and businessKeyLike != ''">
            and BUSINESS_KEY like concat(#{businessKeyLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="flowIdList != null and flowIdList.size() > 0">
            and ID in
            <foreach collection="flowIdList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </sql>

    <sql id="list_condition_sql">
        <if test="handlerId != null">
            and l.HANDLER_ID = #{handlerId,jdbcType=BIGINT}
        </if>
        <if test="platform != null">
            and l.PLATFORM = #{platform,jdbcType=SMALLINT}
        </if>
        <if test="flowTopicName != null and flowTopicName != ''">
            and f.FLOW_TOPIC_NAME like concat('%',#{flowTopicName,jdbcType=VARCHAR},'%')
        </if>
        <if test="initiatorName != null and initiatorName != ''">
            and f.INITIATOR_NAME like concat('%',#{initiatorName,jdbcType=VARCHAR},'%')
        </if>
        <if test="status != null and status != ''">
            and f.STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="workType != null">
            and f.work_type = #{workType}
        </if>
        <if test="beginDate != null and endDate != null and beginDate != '' and endDate != ''">
            and f.CREATE_TIME BETWEEN  #{beginDate} AND #{endDate}
        </if>
        <if test="flowIdList != null and flowIdList.size() > 0">
            and f.id in
            <foreach collection="flowIdList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </sql>

    <!-- 分页获取待办列表 -->
    <select id="todoList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT f.id as commonFlowId,
        t.taskId as taskId,
        f.INITIATOR_ID as initiatorId,
        f.INITIATOR_NAME as initiatorName,
        f.FLOW_TOPIC_TYPE as flowTopicType,
        f.FLOW_TOPIC_NAME as flowTopicName,
        f.CREATE_TIME as createTime,
        f.STATUS as status,
        f.PLATFORM as platform
        FROM (
            SELECT DISTINCT(exc.id_),exc.root_proc_inst_id_,RES.ID_ as taskId
            FROM
                ACT_RU_TASK RES,
                act_ru_execution exc
            WHERE
                RES.SUSPENSION_STATE_ = 1 and res.execution_id_ = exc.id_
                <if test="assignee != null and assignee != ''">
                AND (
                        RES.ASSIGNEE_ = #{assignee,jdbcType=VARCHAR} OR
                    (
                        RES.ASSIGNEE_ IS NULL
                        AND EXISTS (
                            SELECT
                                LINK.ID_
                            FROM
                                ACT_RU_IDENTITYLINK LINK
                            WHERE
                                LINK.TASK_ID_ = RES.ID_
                            AND LINK.TYPE_ = 'candidate'
                            AND (LINK.USER_ID_ = #{assignee,jdbcType=VARCHAR})
                        )
                    )
                )
                </if>
            ) t,<include refid="table"/> f where f.PROCESS_INSTANCE_ID = t.ROOT_PROC_INST_ID_ <include refid="list_condition_sql"></include>
            order by f.CREATE_TIME desc
    </select>

    <select id="todoListCount" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(f.ID) FROM(
            SELECT DISTINCT
            (exc.id_),exc.root_proc_inst_id_
            FROM
                ACT_RU_TASK RES,
                act_ru_execution exc
            WHERE
                RES.SUSPENSION_STATE_ = 1 and res.execution_id_ = exc.id_
                <if test="assignee != null and assignee != ''">
                    AND (
                        RES.ASSIGNEE_ = #{assignee,jdbcType=VARCHAR} OR
                    (
                        RES.ASSIGNEE_ IS NULL
                        AND EXISTS (
                            SELECT
                                LINK.ID_
                            FROM
                                ACT_RU_IDENTITYLINK LINK
                            WHERE
                                LINK.TASK_ID_ = RES.ID_
                            AND LINK.TYPE_ = 'candidate'
                            AND (LINK.USER_ID_ = #{assignee,jdbcType=VARCHAR})
                        )
                    )
                )
                </if>
            ) t,<include refid="table"/> f where f.PROCESS_INSTANCE_ID = t.ROOT_PROC_INST_ID_ <include refid="list_condition_sql"></include>
    </select>

    <select id="carbonList" parameterType="java.util.Map" resultType="java.util.Map">
        select distinct(f.id) as commonFlowId,
        f.INITIATOR_ID as initiatorId,
        f.INITIATOR_NAME as initiatorName,
        f.FLOW_TOPIC_TYPE as flowTopicType,
        f.FLOW_TOPIC_NAME as flowTopicName,
        f.CREATE_TIME as createTime,
        f.STATUS as status,
        f.PLATFORM as platform
        from tbl_common_flow f,tbl_common_flow_carbon carbon where f.id = carbon.common_flow_id
        <include refid="list_condition_sql"></include>
        <if test="userId != null">
            and carbon.user_id = #{userId}
        </if>
        <if test="carbonPlatform != null">
            and carbon.platform = #{carbonPlatform}
        </if>
        order by f.CREATE_TIME desc
    </select>

    <select id="carbonListCount" parameterType="java.util.Map" resultType="long">
        select count(distinct(f.id)) from tbl_common_flow f,tbl_common_flow_log l where f.id = l.COMMON_FLOW_ID
        <include refid="list_condition_sql"></include>
        <if test="userId != null">
            and carbon.user_id = #{userId}
        </if>
        <if test="carbonPlatform != null">
            and carbon.platform = #{carbonPlatform}
        </if>
    </select>

    <!-- 分页查询用户收到审批列表 -->
    <select id="handList" parameterType="java.util.Map" resultType="java.util.Map">
        select distinct(f.id) as commonFlowId,
        f.INITIATOR_ID as initiatorId,
        f.INITIATOR_NAME as initiatorName,
        f.FLOW_TOPIC_TYPE as flowTopicType,
        f.FLOW_TOPIC_NAME as flowTopicName,
        f.CREATE_TIME as createTime,
        f.STATUS as status,
        f.PLATFORM as platform
        from tbl_common_flow f,tbl_common_flow_log l where f.id = l.COMMON_FLOW_ID
            <include refid="list_condition_sql"></include>
        order by f.CREATE_TIME desc
    </select>

    <select id="handListCount"  parameterType="java.util.Map" resultType="long">
        select COUNT(distinct(f.id)) from tbl_common_flow f,tbl_common_flow_log l where f.id = l.COMMON_FLOW_ID
            <include refid="list_condition_sql"></include>
    </select>

    <select id="getExistNotFinishedFlow" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        where BUSINESS_KEY  in
        <foreach collection="businessKeyList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
        and FLOW_TOPIC_TYPE = #{flowTopicType,jdbcType=VARCHAR} and status = #{status,jdbcType=SMALLINT}
        limit 1
    </select>

    <select id="getExistNotFinishedUpdateQuoteFlow" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        where BUSINESS_KEY  like concat(#{businessKey}, '%')
        and FLOW_TOPIC_TYPE = #{flowTopicType,jdbcType=VARCHAR} and status != #{status,jdbcType=SMALLINT}
        limit 1
    </select>

    <select id="identitylinkTask" parameterType="java.util.Map" resultType="long">
        select COUNT(ID_) from act_ru_identitylink where USER_ID_=#{userId} and TYPE_ = "participant"
    </select>

    <!-- 分页获取待处理工单列表 -->
    <select id="todoOrderList" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT f.id as commonFlowId,
        t.taskId as taskId,
        t.nodeKey,
        f.INITIATOR_ID as initiatorId,
        f.INITIATOR_NAME as initiatorName,
        f.FLOW_TOPIC_TYPE as flowTopicType,
        f.FLOW_TOPIC_NAME as flowTopicName,
        f.CREATE_TIME as createTime,
        f.STATUS as status,
        f.PLATFORM as platform,
        f.TASK_NAME as taskName,
         <include refid="orderExtColumn"></include>
         FROM (
        SELECT DISTINCT(exc.id_),exc.root_proc_inst_id_,RES.ID_ as taskId,RES.task_def_key_ as nodeKey
        FROM
        ACT_RU_TASK RES,
        act_ru_execution exc
        WHERE
        RES.SUSPENSION_STATE_ = 1 and res.execution_id_ = exc.id_
        <if test="assignee != null and assignee != ''">
            AND (
            RES.ASSIGNEE_ = #{assignee,jdbcType=VARCHAR} OR
            (
            RES.ASSIGNEE_ IS NULL
            AND EXISTS (
            SELECT
            LINK.ID_
            FROM
            ACT_RU_IDENTITYLINK LINK
            WHERE
            LINK.TASK_ID_ = RES.ID_
            AND LINK.TYPE_ = 'candidate'
            AND (LINK.USER_ID_ = #{assignee,jdbcType=VARCHAR})
            )
            )
            )
        </if>
        ) t,<include refid="table"/> f,tbl_work_order_ext ext where f.PROCESS_INSTANCE_ID = t.ROOT_PROC_INST_ID_ and f.id = ext.common_flow_id <include refid="orderCondition"></include>
        order by ext.work_order_no desc
    </select>

    <select id="todoOrderListCount" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(f.ID) FROM(
        SELECT DISTINCT
        (exc.id_),exc.root_proc_inst_id_
        FROM
        ACT_RU_TASK RES,
        act_ru_execution exc
        WHERE
        RES.SUSPENSION_STATE_ = 1 and res.execution_id_ = exc.id_
        <if test="assignee != null and assignee != ''">
            AND (
            RES.ASSIGNEE_ = #{assignee,jdbcType=VARCHAR} OR
            (
            RES.ASSIGNEE_ IS NULL
            AND EXISTS (
            SELECT
            LINK.ID_
            FROM
            ACT_RU_IDENTITYLINK LINK
            WHERE
            LINK.TASK_ID_ = RES.ID_
            AND LINK.TYPE_ = 'candidate'
            AND (LINK.USER_ID_ = #{assignee,jdbcType=VARCHAR})
            )
            )
            )
        </if>
        ) t,<include refid="table"/> f,tbl_work_order_ext ext where f.PROCESS_INSTANCE_ID = t.ROOT_PROC_INST_ID_ and f.id = ext.common_flow_id <include refid="orderCondition"></include>
    </select>

    <select id="carbonOrderList" parameterType="java.util.Map" resultType="java.util.Map">
        select distinct(f.id) as commonFlowId,
        f.INITIATOR_ID as initiatorId,
        f.INITIATOR_NAME as initiatorName,
        f.FLOW_TOPIC_TYPE as flowTopicType,
        f.FLOW_TOPIC_NAME as flowTopicName,
        f.CREATE_TIME as createTime,
        f.STATUS as status,
        f.PLATFORM as platform,
        f.TASK_NAME as taskName,
        <include refid="orderExtColumn"></include>
        from tbl_common_flow f,tbl_work_order_ext ext,tbl_common_flow_carbon carbon where f.id = ext.common_flow_id and f.id = carbon.common_flow_id
        <include refid="orderCondition"></include>
        <if test="userId != null">
            and carbon.user_id = #{userId}
        </if>
        <if test="carbonPlatform != null">
            and carbon.platform = #{carbonPlatform}
        </if>
        order by ext.work_order_no desc
    </select>

    <select id="carbonOrderListCount" parameterType="java.util.Map" resultType="long">
        select count(distinct(f.id)) from tbl_common_flow f,tbl_work_order_ext ext,tbl_common_flow_carbon carbon where f.id = ext.common_flow_id and f.id = carbon.common_flow_id
        <include refid="orderCondition"></include>
        <if test="userId != null">
            and carbon.user_id = #{userId}
        </if>
        <if test="carbonPlatform != null">
            and carbon.platform = #{carbonPlatform}
        </if>
    </select>

    <!-- 分页查询用户收到工单列表 -->
    <select id="handOrderList" parameterType="java.util.Map" resultType="java.util.Map">
        select distinct(f.id) as commonFlowId,
        f.INITIATOR_ID as initiatorId,
        f.INITIATOR_NAME as initiatorName,
        f.FLOW_TOPIC_TYPE as flowTopicType,
        f.FLOW_TOPIC_NAME as flowTopicName,
        f.CREATE_TIME as createTime,
        f.STATUS as status,
        f.PLATFORM as platform,
        f.TASK_NAME as taskName,
        <include refid="orderExtColumn"></include>
        from tbl_common_flow f,tbl_common_flow_log l,tbl_work_order_ext ext where f.id = l.COMMON_FLOW_ID and f.id = ext.common_flow_id
        <include refid="orderCondition"></include>
        order by ext.work_order_no desc
    </select>

    <select id="handOrderCount"  parameterType="java.util.Map" resultType="long">
        select COUNT(distinct(f.id)) from tbl_common_flow f,tbl_common_flow_log l,tbl_work_order_ext ext where f.id = l.COMMON_FLOW_ID and f.id = ext.common_flow_id
        <include refid="orderCondition"></include>
    </select>

    <sql id="orderCondition">
        <if test="workType != null">
            and f.work_type = #{workType}
        </if>
        <if test="handlerId != null">
            and l.HANDLER_ID = #{handlerId,jdbcType=BIGINT}
        </if>
        <if test="platform != null">
            and l.PLATFORM = #{platform,jdbcType=SMALLINT}
        </if>
        <if test="flowTopicName != null and flowTopicName != ''">
            and f.FLOW_TOPIC_NAME like concat('%',#{flowTopicName,jdbcType=VARCHAR},'%')
        </if>
        <if test="initiatorName != null and initiatorName != ''">
            and f.INITIATOR_NAME like concat('%',#{initiatorName,jdbcType=VARCHAR},'%')
        </if>
        <if test="status != null and status != ''">
            and f.STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="beginCreateDate != null and endCreateDate != null and beginCreateDate != '' and endCreateDate != ''">
            and ext.create_date BETWEEN  #{beginCreateDate} AND #{endCreateDate}
        </if>
        <if test="beginEndDate != null and endEndDate != null and beginEndDate != '' and endEndDate != ''">
            and ext.end_date BETWEEN  #{beginEndDate} AND #{endEndDate}
        </if>
        <if test="employerName != null and employerName != ''">
            and ext.employer_name like concat('%',#{employerName},'%')
        </if>
        <if test="mainstayName != null and mainstayName != ''">
            and ext.mainstay_name like concat('%',#{mainstayName},'%')
        </if>
        <if test="workOrderNo != null and workOrderNo != ''">
            and ext.work_order_no = #{workOrderNo}
        </if>
        <if test="mainstayNo != null and mainstayNo != ''">
            and ext.mainstay_no = #{mainstayNo}
        </if>
        <if test="employerNo != null and employerNo != ''">
            and ext.employer_no = #{employerNo}
        </if>
        <if test="salerId != null">
            and ext.saler_id = #{salerId}
        </if>
        <if test="taskNameLike != null and taskNameLike != ''">
            and f.task_name like concat('%',#{taskNameLike},'%')
        </if>
        <if test="flowIdList != null and flowIdList.size() > 0">
            and f.id in
            <foreach collection="flowIdList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </sql>

    <sql id="orderExtColumn">
        ext.work_order_no as workOrderNo,
        ext.employer_name as employerName,
        ext.mainstay_name as mainstayName,
        ext.mainstay_no as mainstayNo,
        ext.saler_name as salerName,
        ext.agent_name as agentName,
        ext.start_work_time as startWorkTime,
        ext.end_work_time as endWorkTime,
        ext.work_range_url as workRangeUrl,
        ext.end_date as endDate
    </sql>

    <select id="workOrderList" parameterType="java.util.Map" resultType="java.util.Map">
         SELECT f.id as commonFlowId,
        f.INITIATOR_ID as initiatorId,
        f.INITIATOR_NAME as initiatorName,
        f.FLOW_TOPIC_TYPE as flowTopicType,
        f.FLOW_TOPIC_NAME as flowTopicName,
        f.CREATE_TIME as createTime,
        f.STATUS as status,
        f.PLATFORM as platform,
        f.TASK_NAME as taskName,
        <include refid="orderExtColumn"/>
        from tbl_common_flow f,tbl_work_order_ext ext where f.id = ext.common_flow_id
        <include refid="orderCondition"/>
        order by ext.work_order_no desc
    </select>

    <select id="workOrderCount" parameterType="java.util.Map" resultType="long">
        select count(f.id) from tbl_common_flow f,tbl_work_order_ext ext where f.id = ext.common_flow_id
        <include refid="orderCondition"/>
    </select>

    <select id="getJoinEntityByProcessInstanceId" parameterType="java.util.Map" resultType="com.zhixianghui.facade.flow.dto.WorkOrderExtDto">
        select f.id as commonFlowId,
          f.ext_info as extInfo,
          ext.mainstay_no as mainstayNo,
          ext.employer_no as employerNo
          from tbl_common_flow f,tbl_work_order_ext ext where f.id = ext.common_flow_id
          <if test="processInstanceId != null and processInstanceId != ''">
              and f.process_instance_id = #{processInstanceId}
          </if>
    </select>
</mapper>
