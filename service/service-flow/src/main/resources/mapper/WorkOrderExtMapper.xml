<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.flow.entity.WorkOrderExt">
    <sql id="table">tbl_work_order_ext</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.flow.entity.WorkOrderExt">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="work_order_no" property="workOrderNo" jdbcType="VARCHAR"/>
        <result column="common_flow_id" property="commonFlowId" jdbcType="BIGINT"/>
        <result column="employer_no" property="employerNo" jdbcType="VARCHAR"/>
        <result column="employer_name" property="employerName" jdbcType="VARCHAR"/>
        <result column="saler_id" property="salerId" jdbcType="BIGINT"/>
        <result column="saler_name" property="salerName" jdbcType="VARCHAR"/>
        <result column="mainstay_no" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="mainstay_name" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="agent_no" property="agentNo" jdbcType="VARCHAR"/>
        <result column="agent_name" property="agentName" jdbcType="VARCHAR"/>
        <result column="start_work_time" property="startWorkTime" jdbcType="VARCHAR"/>
        <result column="end_work_time" property="endWorkTime" jdbcType="VARCHAR"/>
        <result column="work_range_url" property="workRangeUrl" jdbcType="VARCHAR"/>
        <result column="create_date" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="end_date" property="endDate" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version,work_order_no,common_flow_id, employer_no, employer_name, saler_id, saler_name, mainstay_no, mainstay_name, agent_no, agent_name, start_work_time, end_work_time, work_range_url, create_date, end_date
    </sql>

    <!-- 批量插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.flow.entity.WorkOrderExt">
    INSERT INTO
    <include refid="table"/>
    (
    version,
    work_order_no,
    common_flow_id,
    employer_no,
    employer_name,
    saler_id,
    saler_name,
    mainstay_no,
    mainstay_name,
    agent_no,
    agent_name,
    start_work_time,
    end_work_time,
    work_range_url,
    create_date,
    end_date
    ) VALUES
        (
        0,
        #{workOrderNo,jdbcType=VARCHAR},
        #{commonFlowId,jdbcType=BIGINT},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{salerId,jdbcType=BIGINT},
        #{salerName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{agentNo,jdbcType=VARCHAR},
        #{agentName,jdbcType=VARCHAR},
        #{startWorkTime,jdbcType=VARCHAR},
        #{endWorkTime,jdbcType=VARCHAR},
        #{workRangeUrl,jdbcType=VARCHAR},
        #{createDate,jdbcType=TIMESTAMP},
        #{endDate,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            version,
            work_order_no,
            common_flow_id,
            employer_no,
            employer_name,
            saler_id,
            saler_name,
            mainstay_no,
            mainstay_name,
            agent_no,
            agent_name,
            start_work_time,
            end_work_time,
            work_range_url,
            create_date,
            end_date
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.workOrderNo,jdbcType=VARCHAR},
            #{item.commonFlowId,jdbcType=BIGINT},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.salerId,jdbcType=BIGINT},
            #{item.salerName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.agentNo,jdbcType=VARCHAR},
            #{item.agentName,jdbcType=VARCHAR},
            #{item.startWorkTime,jdbcType=VARCHAR},
            #{item.endWorkTime,jdbcType=VARCHAR},
            #{item.workRangeUrl,jdbcType=VARCHAR},
            #{item.createDate,jdbcType=TIMESTAMP},
            #{item.endDate,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.flow.entity.WorkOrderExt">
        UPDATE <include refid="table" /> SET
            version = #{version,jdbcType=SMALLINT},
            work_order_no = #{workOrderNo,jdbcType=VARCHAR},
            common_flow_id = #{commonFlowId,jdbcType=BIGINT},
            employer_no = #{employerNo,jdbcType=VARCHAR},
            employer_name = #{employerName,jdbcType=VARCHAR},
            saler_id = #{salerId,jdbcType=BIGINT},
            saler_name = #{salerName,jdbcType=VARCHAR},
            mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            agent_no = #{agentNo,jdbcType=VARCHAR},
            agent_name = #{agentName,jdbcType=VARCHAR},
            start_work_time = #{startWorkTime,jdbcType=VARCHAR},
            end_work_time = #{endWorkTime,jdbcType=VARCHAR},
            work_range_url = #{workRangeUrl,jdbcType=VARCHAR},
            create_date = #{createDate,jdbcType=TIMESTAMP},
            end_date = #{endDate,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.flow.entity.WorkOrderExt">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="workOrderNo != null">
                work_order_no = #{workOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="commonFlowId != null">
                common_flow_id = #{commonFlowId,jdbcType=BIGINT},
            </if>
            <if test="employerNo != null">
                employer_no = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="employerName != null">
                employer_name = #{employerName,jdbcType=VARCHAR},
            </if>
            <if test="salerId != null">
                saler_id = #{salerId,jdbcType=BIGINT},
            </if>
            <if test="salerName != null">
                saler_name = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="agentNo != null">
                agent_no = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="agentName != null">
                agent_name = #{agentName,jdbcType=VARCHAR},
            </if>
            <if test="startWorkTime != null">
                start_work_time = #{startWorkTime,jdbcType=VARCHAR},
            </if>
            <if test="endWorkTime != null">
                end_work_time = #{endWorkTime,jdbcType=VARCHAR},
            </if>
            <if test="workRangeUrl != null">
                work_range_url = #{workRangeUrl,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endDate != null">
                end_date = #{endDate,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <select id="getCommonFlowId" parameterType="java.util.Map" resultType="long">
        select common_flow_id from <include refid="table"/> ext,tbl_common_flow f
        where ext.common_flow_id = f.id
        and f.work_type = #{workType,jdbcType=INTEGER}
        and f.status = #{status,jdbcType=INTEGER}
        and ext.end_date <![CDATA[ <= ]]> #{nowDate,jdbcType=TIMESTAMP}
    </select>

    <select id="deleteByCommonFlowId" parameterType="java.lang.Long" resultType="long">
        delete FROM
        <include refid="table" />
        where COMMON_FLOW_ID = #{commonFlowId}
    </select>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="workOrderNo != null">
            and work_order_no = #{workOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="commonFlowId != null">
            and common_flow_id = #{commonFlowId,jdbcType=BIGINT}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and employer_no = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="employerName != null and employerName !=''">
            and employer_name = #{employerName,jdbcType=VARCHAR}
        </if>
        <if test="salerId != null">
            and saler_id = #{salerId,jdbcType=BIGINT}
        </if>
        <if test="salerName != null and salerName !=''">
            and saler_name = #{salerName,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and mainstay_name = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="agentNo != null and agentNo !=''">
            and agent_no = #{agentNo,jdbcType=VARCHAR}
        </if>
        <if test="agentName != null and agentName !=''">
            and agent_name = #{agentName,jdbcType=VARCHAR}
        </if>
        <if test="startWorkTime != null and startWorkTime !=''">
            and start_work_time = #{startWorkTime,jdbcType=VARCHAR}
        </if>
        <if test="endWorkTime != null and endWorkTime !=''">
            and end_work_time = #{endWorkTime,jdbcType=VARCHAR}
        </if>
        <if test="workRangeUrl != null and workRangeUrl !=''">
            and work_range_url = #{workRangeUrl,jdbcType=VARCHAR}
        </if>
        <if test="createDate != null">
            and create_date = #{createDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            and end_date = #{endDate,jdbcType=TIMESTAMP}
        </if>
    </sql>

</mapper>
