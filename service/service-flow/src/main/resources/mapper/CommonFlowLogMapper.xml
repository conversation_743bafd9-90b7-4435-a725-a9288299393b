<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.flow.entity.CommonFlowLog">
    <sql id="table">tbl_common_flow_log</sql>
    
    <resultMap id="logResultMap" type="com.zhixianghui.facade.flow.dto.CommonFlowLogVo">
        <id column="key" jdbcType="VARCHAR"/>
        <result column="STEP" property="step" jdbcType="SMALLINT"/>
        <result column="STEP_NAME" property="stepName" jdbcType="VARCHAR"/>
        <collection property="commonFlowLogList" ofType="com.zhixianghui.facade.flow.entity.CommonFlowLog">
            <id column="ID" property="id" jdbcType="BIGINT"/>
            <result column="COMMON_FLOW_ID" property="commonFlowId" jdbcType="BIGINT"/>
            <result column="VERSION" property="version" jdbcType="INTEGER"/>
            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
            <result column="HANDLER_ID" property="handlerId" jdbcType="BIGINT"/>
            <result column="HANDLER_NAME" property="handlerName" jdbcType="VARCHAR"/>
            <result column="TASK_NAME" property="taskName" jdbcType="VARCHAR"/>
            <result column="STATUS" property="status" jdbcType="SMALLINT"/>
            <result column="EXT_INFO" property="extInfo" jdbcType="OTHER"/>
            <result column="APPROVAL_OPINION" property="approvalOpinion" jdbcType="VARCHAR"/>
            <result column="PROCESS_INSTANCE_ID" property="processInstanceId" jdbcType="VARCHAR"/>
            <result column="TASK_ID" property="taskId" jdbcType="VARCHAR"/>
            <result column="PLATFORM" property="platform" jdbcType="SMALLINT"/>
            <result column="SPENT_TIME" property="spentTime" jdbcType="BIGINT"/>
            <result column="BELONG" property="belong" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.flow.entity.CommonFlowLog">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="COMMON_FLOW_ID" property="commonFlowId" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="HANDLER_ID" property="handlerId" jdbcType="BIGINT"/>
        <result column="HANDLER_NAME" property="handlerName" jdbcType="VARCHAR"/>
        <result column="TASK_NAME" property="taskName" jdbcType="VARCHAR"/>
        <result column="STATUS" property="status" jdbcType="SMALLINT"/>
        <result column="EXT_INFO" property="extInfo" jdbcType="OTHER"/>
        <result column="APPROVAL_OPINION" property="approvalOpinion" jdbcType="VARCHAR"/>
        <result column="PROCESS_INSTANCE_ID" property="processInstanceId" jdbcType="VARCHAR"/>
        <result column="TASK_ID" property="taskId" jdbcType="VARCHAR"/>
        <result column="PLATFORM" property="platform" jdbcType="SMALLINT"/>
        <result column="SPENT_TIME" property="spentTime" jdbcType="BIGINT"/>
        <result column="STEP" property="step" jdbcType="SMALLINT"/>
        <result column="STEP_NAME" property="stepName" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID,
        COMMON_FLOW_ID,
        VERSION,
        CREATE_TIME,
        UPDATE_TIME,
        HANDLER_ID,
        HANDLER_NAME,
        TASK_NAME,
        STATUS,
        EXT_INFO,
        APPROVAL_OPINION,
        PROCESS_INSTANCE_ID,
        TASK_ID,
        PLATFORM,
        SPENT_TIME,
        STEP,
        STEP_NAME,
        BELONG
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.flow.entity.CommonFlowLog">
        INSERT INTO <include refid="table" /> (
            COMMON_FLOW_ID,
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            HANDLER_ID,
            HANDLER_NAME,
            TASK_NAME,
            STATUS,
            EXT_INFO,
            APPROVAL_OPINION,
            PROCESS_INSTANCE_ID,
            TASK_ID,
            PLATFORM,
            SPENT_TIME,
            STEP,
            STEP_NAME,
            BELONG
        ) VALUES (
            #{commonFlowId,jdbcType=BIGINT},
            #{version,jdbcType=INTEGER},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{handlerId,jdbcType=BIGINT},
            #{handlerName,jdbcType=VARCHAR},
            #{taskName,jdbcType=VARCHAR},
            #{status,jdbcType=SMALLINT},
            #{extInfo,jdbcType=OTHER},
            #{approvalOpinion,jdbcType=VARCHAR},
            #{processInstanceId,jdbcType=VARCHAR},
            #{taskId,jdbcType=VARCHAR},
            #{platform,jdbcType=SMALLINT},
            #{spentTime,jdbcType=BIGINT},
            #{step,jdbcType=SMALLINT},
            #{stepName,jdbcType=VARCHAR},
            #{belong,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            COMMON_FLOW_ID,
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            HANDLER_ID,
            HANDLER_NAME,
            TASK_NAME,
            STATUS,
            EXT_INFO,
            APPROVAL_OPINION,
            PROCESS_INSTANCE_ID,
            TASK_ID,
            PLATFORM,
            SPENT_TIME,
            STEP,
            STEP_NAME,
            BELONG
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.commonFlowId,jdbcType=BIGINT},
            #{item.version,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.handlerId,jdbcType=BIGINT},
            #{item.handlerName,jdbcType=VARCHAR},
            #{item.taskName,jdbcType=VARCHAR},
            #{item.status,jdbcType=SMALLINT},
            #{item.extInfo,jdbcType=OTHER},
            #{item.approvalOpinion,jdbcType=VARCHAR},
            #{item.processInstanceId,jdbcType=VARCHAR},
            #{item.taskId,jdbcType=VARCHAR},
            #{item.platform,jdbcType=SMALLINT},
            #{item.spentTime,jdbcType=BIGINT},
            #{item.step,jdbcType=SMALLINT},
            #{item.stepName,jdbcType=VARCHAR},
            #{item.belong,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.flow.entity.CommonFlowLog">
        UPDATE <include refid="table" /> SET
            COMMON_FLOW_ID = #{commonFlowId,jdbcType=BIGINT},
            VERSION = #{version,jdbcType=INTEGER} +1,
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            HANDLER_ID = #{handlerId,jdbcType=BIGINT},
            HANDLER_NAME = #{handlerName,jdbcType=VARCHAR},
            TASK_NAME = #{taskName,jdbcType=VARCHAR},
            STATUS = #{status,jdbcType=SMALLINT},
            EXT_INFO = #{extInfo,jdbcType=OTHER},
            APPROVAL_OPINION = #{approvalOpinion,jdbcType=VARCHAR},
            PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR},
            TASK_ID = #{taskId,jdbcType=VARCHAR},
            PLATFORM = #{platform,jdbcType=SMALLINT},
            SPENT_TIME = #{spentTime,jdbcType=BIGINT},
            STEP = #{step,jdbcType=SMALLINT},
            STEP_NAME = #{stepName,jdbcType=VARCHAR},
            BELONG = #{belong,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <select id="getSingle" parameterType="java.util.Map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        FROM <include refid="table" />
        WHERE task_id = #{taskId,jdbcType=VARCHAR}
        <if test="handlerId != null">
            and handler_id = #{handlerId,jdbcType=BIGINT}
        </if>
        <if test="platform != null">
            and platform = #{platform,jdbcType=SMALLINT}
        </if>
        limit 1
    </select>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.flow.entity.CommonFlowLog">
        UPDATE <include refid="table" />
        <set>
            <if test="commonFlowId != null">
                COMMON_FLOW_ID = #{commonFlowId,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=INTEGER} +1,
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="handlerId != null">
                HANDLER_ID = #{handlerId,jdbcType=BIGINT},
            </if>
            <if test="handlerName != null">
                HANDLER_NAME = #{handlerName,jdbcType=VARCHAR},
            </if>
            <if test="taskName != null">
                TASK_NAME = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=SMALLINT},
            </if>
            <if test="extInfo != null">
                EXT_INFO = #{extInfo,jdbcType=OTHER},
            </if>
            <if test="approvalOpinion != null">
                APPROVAL_OPINION = #{approvalOpinion,jdbcType=VARCHAR},
            </if>
            <if test="processInstanceId != null">
                PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                TASK_ID = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=SMALLINT}
            </if>
            <if test="spentTime != null">
                SPENT_TIME = #{spentTime,jdbcType=BIGINT}
            </if>
            <if test="step != null">
                STEP = #{step,jdbcType=SMALLINT}
            </if>
            <if test="stepName != null">
                STEP_NAME = #{stepName,jdbcType=VARCHAR}
            </if>
            <if test="belong != null">
                BELONG = #{belong,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 不分页查询 -->
    <select id="listAllLog" parameterType="java.util.Map" resultMap="logResultMap">
        SELECT
        concat(IFNULL(STEP_NAME, "null"),STEP) AS "key",
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            common_flow_id = #{commonFlowId,jdbcType=VARCHAR}
            <if test="canSeeEdit == false">
                and task_name != #{taskName,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY step desc,UPDATE_TIME desc,id desc
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <select id="deleteByCommonFlowId" parameterType="java.lang.Long" resultType="long">
        delete FROM
        <include refid="table" />
        where COMMON_FLOW_ID = #{commonFlowId}
    </select>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="commonFlowId != null">
            and COMMON_FLOW_ID = #{commonFlowId,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="handlerId != null">
            and HANDLER_ID = #{handlerId,jdbcType=BIGINT}
        </if>
        <if test="handlerName != null and handlerName !=''">
            and HANDLER_NAME = #{handlerName,jdbcType=VARCHAR}
        </if>
        <if test="taskName != null and taskName !=''">
            and TASK_NAME = #{taskName,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and STATUS = #{status,jdbcType=SMALLINT}
        </if>
        <if test="extInfo != null and extInfo !=''">
            and EXT_INFO = #{extInfo,jdbcType=OTHER}
        </if>
        <if test="approvalOpinion != null and approvalOpinion !=''">
            and APPROVAL_OPINION = #{approvalOpinion,jdbcType=VARCHAR}
        </if>
        <if test="processInstanceId != null and processInstanceId !=''">
            and PROCESS_INSTANCE_ID = #{processInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="taskId != null and taskId !=''">
            and TASK_ID = #{taskId,jdbcType=VARCHAR}
        </if>
        <if test="platform != null">
            and PLATFORM = #{platform,jdbcType=SMALLINT}
        </if>
        <if test="spentTime != null">
            and SPENT_TIME = #{spentTime,jdbcType=BIGINT}
        </if>
        <if test="step != null">
            and STEP = #{step,jdbcType=SMALLINT}
        </if>
        <if test="stepName != null">
            and STEP_NAME = #{stepName,jdbcType=VARCHAR}
        </if>
        <if test="belong != null and belong !=''">
            and BELONG = #{belong,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
