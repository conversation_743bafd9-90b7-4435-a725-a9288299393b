<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.flow.entity.ActReProcdef">
    <sql id="table">act_re_procdef</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.flow.entity.ActReProcdef">
        <id column="ID_" property="id" jdbcType="VARCHAR"/>

        <result column="REV_" property="rev" jdbcType="INTEGER"/>
        <result column="CATEGORY_" property="category" jdbcType="VARCHAR"/>
        <result column="NAME_" property="name" jdbcType="VARCHAR"/>
        <result column="KEY_" property="key" jdbcType="VARCHAR"/>
        <result column="VERSION_" property="version" jdbcType="INTEGER"/>
        <result column="DEPLOYMENT_ID_" property="deploymentId" jdbcType="VARCHAR"/>
        <result column="RESOURCE_NAME_" property="resourceName" jdbcType="VARCHAR"/>
        <result column="DGRM_RESOURCE_NAME_" property="dgrmResourceName" jdbcType="VARCHAR"/>
        <result column="DESCRIPTION_" property="description" jdbcType="VARCHAR"/>
        <result column="HAS_START_FORM_KEY_" property="hasStartFormKey" jdbcType="TINYINT"/>
        <result column="HAS_GRAPHICAL_NOTATION_" property="hasGraphicalNotation" jdbcType="TINYINT"/>
        <result column="SUSPENSION_STATE_" property="suspensionState" jdbcType="INTEGER"/>
        <result column="TENANT_ID_" property="tenantId" jdbcType="VARCHAR"/>
        <result column="ENGINE_VERSION_" property="engineVersion" jdbcType="VARCHAR"/>
        <result column="DERIVED_FROM_" property="derivedFrom" jdbcType="VARCHAR"/>
        <result column="DERIVED_FROM_ROOT_" property="derivedFromRoot" jdbcType="VARCHAR"/>
        <result column="DERIVED_VERSION_" property="derivedVersion" jdbcType="INTEGER"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID_, REV_, CATEGORY_, NAME_, KEY_, VERSION_, DEPLOYMENT_ID_, RESOURCE_NAME_, DGRM_RESOURCE_NAME_, DESCRIPTION_, HAS_START_FORM_KEY_, HAS_GRAPHICAL_NOTATION_, SUSPENSION_STATE_, TENANT_ID_, ENGINE_VERSION_, DERIVED_FROM_, DERIVED_FROM_ROOT_, DERIVED_VERSION_, CREATE_USER, CREATE_TIME,UPDATE_USER,UPDATE_TIME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.flow.entity.ActReProcdef">
        INSERT INTO <include refid="table" /> (
            REV_,
            CATEGORY_,
            NAME_,
            KEY_,
            VERSION_,
            DEPLOYMENT_ID_,
            RESOURCE_NAME_,
            DGRM_RESOURCE_NAME_,
            DESCRIPTION_,
            HAS_START_FORM_KEY_,
            HAS_GRAPHICAL_NOTATION_,
            SUSPENSION_STATE_,
            TENANT_ID_,
            ENGINE_VERSION_,
            DERIVED_FROM_,
            DERIVED_FROM_ROOT_,
            DERIVED_VERSION_,
            CREATE_USER,
            CREATE_TIME,
            UPDATE_USER,
            UPDATE_TIME
        ) VALUES (
            #{rev,jdbcType=INTEGER},
            #{category,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{key,jdbcType=VARCHAR},
            #{version,jdbcType=INTEGER},
            #{deploymentId,jdbcType=VARCHAR},
            #{resourceName,jdbcType=VARCHAR},
            #{dgrmResourceName,jdbcType=VARCHAR},
            #{description,jdbcType=VARCHAR},
            #{hasStartFormKey,jdbcType=TINYINT},
            #{hasGraphicalNotation,jdbcType=TINYINT},
            #{suspensionState,jdbcType=INTEGER},
            #{tenantId,jdbcType=VARCHAR},
            #{engineVersion,jdbcType=VARCHAR},
            #{derivedFrom,jdbcType=VARCHAR},
            #{derivedFromRoot,jdbcType=VARCHAR},
            #{derivedVersion,jdbcType=INTEGER},
            #{createUser,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateUser,jdbcType=VARCHAR},
            #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            REV_,
            CATEGORY_,
            NAME_,
            KEY_,
            VERSION_,
            DEPLOYMENT_ID_,
            RESOURCE_NAME_,
            DGRM_RESOURCE_NAME_,
            DESCRIPTION_,
            HAS_START_FORM_KEY_,
            HAS_GRAPHICAL_NOTATION_,
            SUSPENSION_STATE_,
            TENANT_ID_,
            ENGINE_VERSION_,
            DERIVED_FROM_,
            DERIVED_FROM_ROOT_,
            DERIVED_VERSION_,
            CREATE_USER,
            CREATE_TIME,
            UPDATE_USER,
            UPDATE_TIME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.rev,jdbcType=INTEGER},
            #{item.category,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR},
            #{item.key,jdbcType=VARCHAR},
            #{item.version,jdbcType=INTEGER},
            #{item.deploymentId,jdbcType=VARCHAR},
            #{item.resourceName,jdbcType=VARCHAR},
            #{item.dgrmResourceName,jdbcType=VARCHAR},
            #{item.description,jdbcType=VARCHAR},
            #{item.hasStartFormKey,jdbcType=TINYINT},
            #{item.hasGraphicalNotation,jdbcType=TINYINT},
            #{item.suspensionState,jdbcType=INTEGER},
            #{item.tenantId,jdbcType=VARCHAR},
            #{item.engineVersion,jdbcType=VARCHAR},
            #{item.derivedFrom,jdbcType=VARCHAR},
            #{item.derivedFromRoot,jdbcType=VARCHAR},
            #{item.derivedVersion,jdbcType=INTEGER},
            #{item.createUser,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUser,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.flow.entity.ActReProcdef">
        UPDATE <include refid="table" /> SET
            REV_ = #{rev,jdbcType=INTEGER} + 1,
            CATEGORY_ = #{category,jdbcType=VARCHAR},
            NAME_ = #{name,jdbcType=VARCHAR},
            KEY_ = #{key,jdbcType=VARCHAR},
            VERSION_ = #{version,jdbcType=INTEGER},
            DEPLOYMENT_ID_ = #{deploymentId,jdbcType=VARCHAR},
            RESOURCE_NAME_ = #{resourceName,jdbcType=VARCHAR},
            DGRM_RESOURCE_NAME_ = #{dgrmResourceName,jdbcType=VARCHAR},
            DESCRIPTION_ = #{description,jdbcType=VARCHAR},
            HAS_START_FORM_KEY_ = #{hasStartFormKey,jdbcType=TINYINT},
            HAS_GRAPHICAL_NOTATION_ = #{hasGraphicalNotation,jdbcType=TINYINT},
            SUSPENSION_STATE_ = #{suspensionState,jdbcType=INTEGER},
            TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            ENGINE_VERSION_ = #{engineVersion,jdbcType=VARCHAR},
            DERIVED_FROM_ = #{derivedFrom,jdbcType=VARCHAR},
            DERIVED_FROM_ROOT_ = #{derivedFromRoot,jdbcType=VARCHAR},
            DERIVED_VERSION_ = #{derivedVersion,jdbcType=INTEGER},
            CREATE_USER = #{createUser,jdbcType=VARCHAR},
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_USER = #{updateUser,jdbcType=VARCHAR},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        WHERE ID_ = #{id,jdbcType=VARCHAR} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.flow.entity.ActReProcdef">
        UPDATE <include refid="table" />
        <set>
            <if test="rev != null">
                REV_ = #{rev,jdbcType=INTEGER} + 1,
            </if>
            <if test="category != null">
                CATEGORY_ = #{category,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                NAME_ = #{name,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                KEY_ = #{key,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                VERSION_ = #{version,jdbcType=INTEGER},
            </if>
            <if test="deploymentId != null">
                DEPLOYMENT_ID_ = #{deploymentId,jdbcType=VARCHAR},
            </if>
            <if test="resourceName != null">
                RESOURCE_NAME_ = #{resourceName,jdbcType=VARCHAR},
            </if>
            <if test="dgrmResourceName != null">
                DGRM_RESOURCE_NAME_ = #{dgrmResourceName,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                DESCRIPTION_ = #{description,jdbcType=VARCHAR},
            </if>
            <if test="hasStartFormKey != null">
                HAS_START_FORM_KEY_ = #{hasStartFormKey,jdbcType=TINYINT},
            </if>
            <if test="hasGraphicalNotation != null">
                HAS_GRAPHICAL_NOTATION_ = #{hasGraphicalNotation,jdbcType=TINYINT},
            </if>
            <if test="suspensionState != null">
                SUSPENSION_STATE_ = #{suspensionState,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="engineVersion != null">
                ENGINE_VERSION_ = #{engineVersion,jdbcType=VARCHAR},
            </if>
            <if test="derivedFrom != null">
                DERIVED_FROM_ = #{derivedFrom,jdbcType=VARCHAR},
            </if>
            <if test="derivedFromRoot != null">
                DERIVED_FROM_ROOT_ = #{derivedFromRoot,jdbcType=VARCHAR},
            </if>
            <if test="derivedVersion != null">
                DERIVED_VERSION_ = #{derivedVersion,jdbcType=INTEGER},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                UPDATE_USER= #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE ID_ = #{id,jdbcType=VARCHAR} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID_ = #{id,jdbcType=VARCHAR}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID_ = #{id,jdbcType=VARCHAR}
    </delete>

    <sql id="condition_sql">
        <if test="id != null and id !=''">
            and ID_ = #{id,jdbcType=VARCHAR}
        </if>
        <if test="rev != null">
            and REV_ = #{rev,jdbcType=INTEGER}
        </if>
        <if test="category != null and category !=''">
            and CATEGORY_ = #{category,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name !=''">
            and NAME_ = #{name,jdbcType=VARCHAR}
        </if>
        <if test="key != null and key !=''">
            and KEY_ = #{key,jdbcType=VARCHAR}
        </if>
        <if test="version != null">
            and VERSION_ = #{version,jdbcType=INTEGER}
        </if>
        <if test="deploymentId != null and deploymentId !=''">
            and DEPLOYMENT_ID_ = #{deploymentId,jdbcType=VARCHAR}
        </if>
        <if test="resourceName != null and resourceName !=''">
            and RESOURCE_NAME_ = #{resourceName,jdbcType=VARCHAR}
        </if>
        <if test="dgrmResourceName != null and dgrmResourceName !=''">
            and DGRM_RESOURCE_NAME_ = #{dgrmResourceName,jdbcType=VARCHAR}
        </if>
        <if test="description != null and description !=''">
            and DESCRIPTION_ = #{description,jdbcType=VARCHAR}
        </if>
        <if test="hasStartFormKey != null">
            and HAS_START_FORM_KEY_ = #{hasStartFormKey,jdbcType=TINYINT}
        </if>
        <if test="hasGraphicalNotation != null">
            and HAS_GRAPHICAL_NOTATION_ = #{hasGraphicalNotation,jdbcType=TINYINT}
        </if>
        <if test="suspensionState != null">
            and SUSPENSION_STATE_ = #{suspensionState,jdbcType=INTEGER}
        </if>
        <if test="tenantId != null and tenantId !=''">
            and TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="engineVersion != null and engineVersion !=''">
            and ENGINE_VERSION_ = #{engineVersion,jdbcType=VARCHAR}
        </if>
        <if test="derivedFrom != null and derivedFrom !=''">
            and DERIVED_FROM_ = #{derivedFrom,jdbcType=VARCHAR}
        </if>
        <if test="derivedFromRoot != null and derivedFromRoot !=''">
            and DERIVED_FROM_ROOT_ = #{derivedFromRoot,jdbcType=VARCHAR}
        </if>
        <if test="derivedVersion != null">
            and DERIVED_VERSION_ = #{derivedVersion,jdbcType=INTEGER}
        </if>
        <if test="createUser != null and createUser !=''">
            and CREATE_USER = #{createUser,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateUser != null and updateUser !=''">
            and UPDATE_USER = #{updateUser,jdbcType=VARCHAR}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <update id="updateProcessDefinition" parameterType="java.util.Map">
        update act_re_procdef set rev_ = rev_ + 1
        <if test="createUser != null and createUser != ''">
            ,create_user = #{createUser,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            ,create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="category != null and category != ''">
            ,CATEGORY_ = #{category,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != ''">
            ,NAME_ = #{name,jdbcType=VARCHAR}
        </if>
        <if test="tenantId != null and tenantId != ''">
            ,TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="desc != null and desc != ''">
            ,DESCRIPTION_ = #{desc,jdbcType=VARCHAR}
        </if>
        <if test="updateUser != null and updateUser != ''">
            ,update_user = #{updateUser,jdbcType=VARCHAR}
        </if>
        <if test="updateTime != null">
            ,update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        where id_ = #{id,jdbcType=VARCHAR}
    </update>

    <sql id="pageConditionSql">
        <if test="tenantId != null and tenantId != ''">
            AND RES.TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="key != null and key != ''">
            AND RES.KEY_ like concat('%',#{key,jdbcType=VARCHAR},'%')
        </if>
        <if test="name != null and name != ''">
            AND RES.NAME_ like concat('%',#{name,jdbcType=VARCHAR},'%')
        </if>
    </sql>

    <select id="proDefPageCount" parameterType="java.util.Map" resultType="long">
        SELECT count(RES.ID_) FROM ACT_RE_PROCDEF RES WHERE RES.VERSION_ = (SELECT max(VERSION_) FROM ACT_RE_PROCDEF
        WHERE KEY_ = RES.KEY_
                AND ((TENANT_ID_ IS NOT NULL AND TENANT_ID_ = RES.TENANT_ID_) OR (TENANT_ID_ IS NULL AND RES.TENANT_ID_
                IS NULL))
            <if test="tenantId != null and tenantId != ''">
                AND TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
            </if>
        )
        <include refid="pageConditionSql"></include>
        ORDER BY
        RES.ID_ ASC
    </select>

    <select id="listProDefPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
            RES.*
        FROM
            ACT_RE_PROCDEF RES
        WHERE
        RES.VERSION_ = (
            SELECT
                max(VERSION_)
            FROM
                ACT_RE_PROCDEF
            WHERE
                KEY_ = RES.KEY_
                    AND (
                            (
                                TENANT_ID_ IS NOT NULL
                                AND TENANT_ID_ = RES.TENANT_ID_
                            )
                            OR (
                                TENANT_ID_ IS NULL
                                AND RES.TENANT_ID_ IS NULL
                            )
                    )
            <if test="tenantId != null and tenantId != ''">
                AND TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
            </if>
        )
        <include refid="pageConditionSql"></include>
        ORDER BY
            RES.ID_ ASC
    </select>

</mapper>
