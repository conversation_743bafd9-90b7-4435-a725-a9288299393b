<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="PMS_EMPLOY_MAINSTAY_RELATION_APPLY" name="后台流程-建立代征关系" isExecutable="true">
    <startEvent id="start" name="流程开始" flowable:initiator="applyUserId" flowable:formFieldValidation="true"></startEvent>
    <userTask id="mainstayAudit" name="供应商审批" flowable:candidateUsers="${op.getMainstayStaffByRole(mainstayNo,&quot;&quot;)}" flowable:formFieldValidation="true">
      <documentation>供应商审批</documentation>
      <extensionElements>
        <flowable:formProperty id="canBackVariable" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-2B5081DE-312B-4684-B1AC-4BA159F02C5C"></exclusiveGateway>
    <sequenceFlow id="sid-9CD30261-F678-4440-B7EC-C59C7C574EE9" sourceRef="mainstayAudit" targetRef="sid-2B5081DE-312B-4684-B1AC-4BA159F02C5C"></sequenceFlow>
    <endEvent id="successEnd" name="审批通过">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${createMchRelation}"></flowable:executionListener>
      </extensionElements>
    </endEvent>
    <sequenceFlow id="sid-1E056196-6682-4E38-B94D-35A682F0BBA4" name="审批通过结束事件" sourceRef="sid-2B5081DE-312B-4684-B1AC-4BA159F02C5C" targetRef="successEnd">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 100}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="createSubmit" name="提交审批" flowable:assignee="${applyUserId}" flowable:formFieldValidation="true" flowable:skipExpression="${flowStatus != 101}">
      <documentation>提交审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-79B31496-3D9A-436D-B332-E2F7327C6010" sourceRef="start" targetRef="createSubmit"></sequenceFlow>
    <sequenceFlow id="sid-76856A66-B770-4746-83A1-D053A2654349" sourceRef="createSubmit" targetRef="mainstayAudit"></sequenceFlow>
    <sequenceFlow id="sid-E9C41B02-EFD4-417C-BE51-485183A6FB08" name="审批驳回" sourceRef="sid-2B5081DE-312B-4684-B1AC-4BA159F02C5C" targetRef="createSubmit">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus==101}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_PMS_EMPLOY_MAINSTAY_RELATION_APPLY">
    <bpmndi:BPMNPlane bpmnElement="PMS_EMPLOY_MAINSTAY_RELATION_APPLY" id="BPMNPlane_PMS_EMPLOY_MAINSTAY_RELATION_APPLY">
      <bpmndi:BPMNShape bpmnElement="start" id="BPMNShape_start">
        <omgdc:Bounds height="30.0" width="30.0" x="240.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="mainstayAudit" id="BPMNShape_mainstayAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-2B5081DE-312B-4684-B1AC-4BA159F02C5C" id="BPMNShape_sid-2B5081DE-312B-4684-B1AC-4BA159F02C5C">
        <omgdc:Bounds height="40.0" width="40.0" x="720.0" y="158.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="successEnd" id="BPMNShape_successEnd">
        <omgdc:Bounds height="28.0" width="28.0" x="870.0" y="164.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="createSubmit" id="BPMNShape_createSubmit">
        <omgdc:Bounds height="80.0" width="100.0" x="345.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-1E056196-6682-4E38-B94D-35A682F0BBA4" id="BPMNEdge_sid-1E056196-6682-4E38-B94D-35A682F0BBA4">
        <omgdi:waypoint x="759.5095911949685" y="178.43356643356645"></omgdi:waypoint>
        <omgdi:waypoint x="870.0000829380081" y="178.04860604497966"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9CD30261-F678-4440-B7EC-C59C7C574EE9" id="BPMNEdge_sid-9CD30261-F678-4440-B7EC-C59C7C574EE9">
        <omgdi:waypoint x="639.9499999999989" y="178.16594684385382"></omgdi:waypoint>
        <omgdi:waypoint x="720.4333333333334" y="178.43333333333334"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-76856A66-B770-4746-83A1-D053A2654349" id="BPMNEdge_sid-76856A66-B770-4746-83A1-D053A2654349">
        <omgdi:waypoint x="444.95000000000005" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-79B31496-3D9A-436D-B332-E2F7327C6010" id="BPMNEdge_sid-79B31496-3D9A-436D-B332-E2F7327C6010">
        <omgdi:waypoint x="269.94999779398904" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="344.9999999999497" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E9C41B02-EFD4-417C-BE51-485183A6FB08" id="BPMNEdge_sid-E9C41B02-EFD4-417C-BE51-485183A6FB08">
        <omgdi:waypoint x="740.5" y="197.439058891455"></omgdi:waypoint>
        <omgdi:waypoint x="740.5" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="395.0" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="395.0" y="217.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>