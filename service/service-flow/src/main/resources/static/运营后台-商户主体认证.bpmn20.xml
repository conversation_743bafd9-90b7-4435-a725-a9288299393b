<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="PMS_MCH_MAIN_AUTH" name="运营后台-商户主体认证" isExecutable="true">
    <startEvent id="start" name="流程开始" flowable:initiator="applyUserId" flowable:formFieldValidation="true"></startEvent>
    <userTask id="operatorAudit" name="运营部或签" flowable:candidateUsers="${op.getOperatorByDepartmentNumbers(&quot;02&quot;)}" flowable:formFieldValidation="true">
      <documentation>运营部或签</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" name="是否可以编辑" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <flowable:formProperty id="canBackVariable" name="是否可以驳回" type="boolean" variable="true" default="true"></flowable:formProperty>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-7C5FE51E-96A2-4B22-99ED-6C3B77DBCE7E"></exclusiveGateway>
    <sequenceFlow id="sid-ECD9B3F6-4548-408A-98C6-B5555E3522D5" sourceRef="operatorAudit" targetRef="sid-7C5FE51E-96A2-4B22-99ED-6C3B77DBCE7E"></sequenceFlow>
    <endEvent id="sid-0E9998C7-0EBB-49CF-9CE3-7C5B53378F8B" name="审批通过">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${newMainAuth}"></flowable:executionListener>
      </extensionElements>
    </endEvent>
    <userTask id="createrSubmit" name="提交审批" flowable:assignee="${applyUserId}" flowable:formFieldValidation="true" flowable:skipExpression="${flowStatus != 101}">
      <documentation>提交审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-EF9F72FF-1378-48A4-8911-9AE3C7BE5BFB" sourceRef="start" targetRef="createrSubmit"></sequenceFlow>
    <sequenceFlow id="sid-323429CA-160F-4F50-9B8B-DF9974EFBD90" sourceRef="createrSubmit" targetRef="operatorAudit"></sequenceFlow>
    <sequenceFlow id="sid-4A9DC78B-6AB6-4375-AE22-813E843ACF58" name="审批通过结束事件" sourceRef="sid-7C5FE51E-96A2-4B22-99ED-6C3B77DBCE7E" targetRef="sid-0E9998C7-0EBB-49CF-9CE3-7C5B53378F8B">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 100}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-B2D92622-B706-4A5E-BB13-44F1D8878DDA" name="审批流驳回" sourceRef="sid-7C5FE51E-96A2-4B22-99ED-6C3B77DBCE7E" targetRef="createrSubmit">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${authFailed}"></flowable:executionListener>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus==101}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_PMS_MCH_MAIN_AUTH">
    <bpmndi:BPMNPlane bpmnElement="PMS_MCH_MAIN_AUTH" id="BPMNPlane_PMS_MCH_MAIN_AUTH">
      <bpmndi:BPMNShape bpmnElement="start" id="BPMNShape_start">
        <omgdc:Bounds height="30.0" width="30.0" x="210.0" y="160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="operatorAudit" id="BPMNShape_operatorAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-7C5FE51E-96A2-4B22-99ED-6C3B77DBCE7E" id="BPMNShape_sid-7C5FE51E-96A2-4B22-99ED-6C3B77DBCE7E">
        <omgdc:Bounds height="40.0" width="40.0" x="720.0" y="155.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-0E9998C7-0EBB-49CF-9CE3-7C5B53378F8B" id="BPMNShape_sid-0E9998C7-0EBB-49CF-9CE3-7C5B53378F8B">
        <omgdc:Bounds height="28.0" width="28.0" x="885.0" y="161.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="createrSubmit" id="BPMNShape_createrSubmit">
        <omgdc:Bounds height="80.0" width="100.0" x="345.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-323429CA-160F-4F50-9B8B-DF9974EFBD90" id="BPMNEdge_sid-323429CA-160F-4F50-9B8B-DF9974EFBD90">
        <omgdi:waypoint x="444.9499999999802" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="540.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-B2D92622-B706-4A5E-BB13-44F1D8878DDA" id="BPMNEdge_sid-B2D92622-B706-4A5E-BB13-44F1D8878DDA">
        <omgdi:waypoint x="740.5" y="194.44239566613163"></omgdi:waypoint>
        <omgdi:waypoint x="740.5" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="395.0" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="395.0" y="214.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EF9F72FF-1378-48A4-8911-9AE3C7BE5BFB" id="BPMNEdge_sid-EF9F72FF-1378-48A4-8911-9AE3C7BE5BFB">
        <omgdi:waypoint x="239.9499987519259" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="345.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4A9DC78B-6AB6-4375-AE22-813E843ACF58" id="BPMNEdge_sid-4A9DC78B-6AB6-4375-AE22-813E843ACF58">
        <omgdi:waypoint x="759.5039373813973" y="175.4398734177215"></omgdi:waypoint>
        <omgdi:waypoint x="885.0000670136328" y="175.0440062513792"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-ECD9B3F6-4548-408A-98C6-B5555E3522D5" id="BPMNEdge_sid-ECD9B3F6-4548-408A-98C6-B5555E3522D5">
        <omgdi:waypoint x="639.9499999999989" y="175.16594684385382"></omgdi:waypoint>
        <omgdi:waypoint x="720.4333333333334" y="175.43333333333337"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>