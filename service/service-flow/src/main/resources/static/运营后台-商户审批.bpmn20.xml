<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="PMS_MCH_APPLY" name="运营后台-商户审批" isExecutable="true">
    <startEvent id="start" name="流程开始" flowable:initiator="applyUserId" flowable:formFieldValidation="true"></startEvent>
    <exclusiveGateway id="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94"></exclusiveGateway>
    <sequenceFlow id="sid-5AC1EF59-552C-48DB-B691-A13D9425DD8C" sourceRef="directorAudit" targetRef="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94"></sequenceFlow>
    <endEvent id="successEnd" name="审批通过">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${newMchApply}"></flowable:executionListener>
      </extensionElements>
    </endEvent>
    <userTask id="directorAudit" name="会签审批" flowable:assignee="${leader}" flowable:formFieldValidation="true">
      <documentation>会签审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" name="是否可编辑" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <flowable:formProperty id="canBackVariable" name="是否可以驳回" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${op.getLeaderByNumbers(&quot;00&quot;,&quot;01&quot;)}" flowable:elementVariable="leader">
        <completionCondition>${flowStatus == 101}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <sequenceFlow id="sid-ED5B23D0-1AAB-4715-8D05-C805BE98FA37" name="审批通过结束事件" sourceRef="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94" targetRef="successEnd">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 100}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="createrSubmit" name="提交审批" flowable:assignee="${applyUserId}" flowable:formFieldValidation="true" flowable:skipExpression="${flowStatus != 101}">
      <documentation>提交审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" name="是否可编辑" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-6E6EA050-1EAC-4E1B-A89C-C0EC64D8D58F" sourceRef="start" targetRef="createrSubmit"></sequenceFlow>
    <sequenceFlow id="sid-06E19BE4-FAC5-4990-B088-0BB597ED0B18" name="审批驳回" sourceRef="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94" targetRef="createrSubmit">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 101}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-AE36F9CD-5C5C-419F-92DE-089F0534D06F" sourceRef="createrSubmit" targetRef="directorAudit"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_PMS_MCH_APPLY">
    <bpmndi:BPMNPlane bpmnElement="PMS_MCH_APPLY" id="BPMNPlane_PMS_MCH_APPLY">
      <bpmndi:BPMNShape bpmnElement="start" id="BPMNShape_start">
        <omgdc:Bounds height="30.0" width="30.0" x="75.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94" id="BPMNShape_sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94">
        <omgdc:Bounds height="40.0" width="40.0" x="465.0" y="158.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="successEnd" id="BPMNShape_successEnd">
        <omgdc:Bounds height="28.0" width="28.0" x="615.0" y="164.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="directorAudit" id="BPMNShape_directorAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="315.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="createrSubmit" id="BPMNShape_createrSubmit">
        <omgdc:Bounds height="80.0" width="100.0" x="165.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-ED5B23D0-1AAB-4715-8D05-C805BE98FA37" id="BPMNEdge_sid-ED5B23D0-1AAB-4715-8D05-C805BE98FA37">
        <omgdi:waypoint x="504.50959119496855" y="178.43356643356645"></omgdi:waypoint>
        <omgdi:waypoint x="615.0000829380081" y="178.04860604497966"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-06E19BE4-FAC5-4990-B088-0BB597ED0B18" id="BPMNEdge_sid-06E19BE4-FAC5-4990-B088-0BB597ED0B18">
        <omgdi:waypoint x="485.49999999999994" y="197.4383884803922"></omgdi:waypoint>
        <omgdi:waypoint x="485.5" y="260.0"></omgdi:waypoint>
        <omgdi:waypoint x="215.0" y="260.0"></omgdi:waypoint>
        <omgdi:waypoint x="215.0" y="217.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-AE36F9CD-5C5C-419F-92DE-089F0534D06F" id="BPMNEdge_sid-AE36F9CD-5C5C-419F-92DE-089F0534D06F">
        <omgdi:waypoint x="264.9499999999581" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="314.9999999999364" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5AC1EF59-552C-48DB-B691-A13D9425DD8C" id="BPMNEdge_sid-5AC1EF59-552C-48DB-B691-A13D9425DD8C">
        <omgdi:waypoint x="414.9499999999953" y="178.20726141078836"></omgdi:waypoint>
        <omgdi:waypoint x="465.41666666666436" y="178.41666666666666"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6E6EA050-1EAC-4E1B-A89C-C0EC64D8D58F" id="BPMNEdge_sid-6E6EA050-1EAC-4E1B-A89C-C0EC64D8D58F">
        <omgdi:waypoint x="104.94999683796107" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="164.9999999999357" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>