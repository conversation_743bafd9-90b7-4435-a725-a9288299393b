<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="PMS_EDIT_MERCHANT" name="运营后台-编辑商户信息" isExecutable="true">
    <startEvent id="start" name="流程开始" flowable:initiator="applyUserId" flowable:formFieldValidation="true"></startEvent>
    <userTask id="directorAudit" name="会签审批" flowable:assignee="${leader}" flowable:formFieldValidation="true">
      <documentation>会签审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" name="是否可以修改表单" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <flowable:formProperty id="canBackVariable" name="是否可以驳回" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${op.getLeaderByNumbers(&quot;00&quot;,&quot;01&quot;)}" flowable:elementVariable="leader">
        <completionCondition>${flowStatus==101}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <exclusiveGateway id="sid-B4D45E13-3C77-4E84-AFC7-156F338AFB71"></exclusiveGateway>
    <sequenceFlow id="sid-436D6458-B91E-4B5F-B1EE-1B56BFD25AD8" sourceRef="directorAudit" targetRef="sid-B4D45E13-3C77-4E84-AFC7-156F338AFB71"></sequenceFlow>
    <endEvent id="successEnd" name="审批通过">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${editMerchantData}"></flowable:executionListener>
      </extensionElements>
    </endEvent>
    <userTask id="createSubmit" name="提交审批" flowable:assignee="${applyUserId}" flowable:formFieldValidation="true" flowable:skipExpression="${flowStatus != 101}">
      <documentation>提交审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-C483AD62-0BB5-462B-AB12-C55FC96CA976" sourceRef="start" targetRef="createSubmit"></sequenceFlow>
    <sequenceFlow id="sid-C5907C40-224E-4828-A195-F300CE060AE1" sourceRef="createSubmit" targetRef="directorAudit"></sequenceFlow>
    <sequenceFlow id="sid-DC284BD6-AB2F-427A-B6FF-934F39A49186" name="审批驳回" sourceRef="sid-B4D45E13-3C77-4E84-AFC7-156F338AFB71" targetRef="createSubmit">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 101}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-27E3012C-D8B5-43EC-A7F4-77454C874596" name="审批通过结束事件" sourceRef="sid-B4D45E13-3C77-4E84-AFC7-156F338AFB71" targetRef="successEnd">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 100}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_PMS_EDIT_MERCHANT">
    <bpmndi:BPMNPlane bpmnElement="PMS_EDIT_MERCHANT" id="BPMNPlane_PMS_EDIT_MERCHANT">
      <bpmndi:BPMNShape bpmnElement="start" id="BPMNShape_start">
        <omgdc:Bounds height="30.0" width="30.0" x="120.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="directorAudit" id="BPMNShape_directorAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="465.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B4D45E13-3C77-4E84-AFC7-156F338AFB71" id="BPMNShape_sid-B4D45E13-3C77-4E84-AFC7-156F338AFB71">
        <omgdc:Bounds height="40.0" width="40.0" x="645.0" y="158.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="successEnd" id="BPMNShape_successEnd">
        <omgdc:Bounds height="28.0" width="28.0" x="825.0" y="164.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="createSubmit" id="BPMNShape_createSubmit">
        <omgdc:Bounds height="80.0" width="100.0" x="285.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-436D6458-B91E-4B5F-B1EE-1B56BFD25AD8" id="BPMNEdge_sid-436D6458-B91E-4B5F-B1EE-1B56BFD25AD8">
        <omgdi:waypoint x="564.9499999999989" y="178.16594684385382"></omgdi:waypoint>
        <omgdi:waypoint x="645.4333333333334" y="178.43333333333334"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C483AD62-0BB5-462B-AB12-C55FC96CA976" id="BPMNEdge_sid-C483AD62-0BB5-462B-AB12-C55FC96CA976">
        <omgdi:waypoint x="149.94999919845122" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="284.9999999999945" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-27E3012C-D8B5-43EC-A7F4-77454C874596" id="BPMNEdge_sid-27E3012C-D8B5-43EC-A7F4-77454C874596">
        <omgdi:waypoint x="684.4992634315424" y="178.44508670520233"></omgdi:waypoint>
        <omgdi:waypoint x="825.0000549651019" y="178.04020167212173"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C5907C40-224E-4828-A195-F300CE060AE1" id="BPMNEdge_sid-C5907C40-224E-4828-A195-F300CE060AE1">
        <omgdi:waypoint x="384.9499999999431" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="464.99999999986994" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-DC284BD6-AB2F-427A-B6FF-934F39A49186" id="BPMNEdge_sid-DC284BD6-AB2F-427A-B6FF-934F39A49186">
        <omgdi:waypoint x="665.5" y="197.43982277121376"></omgdi:waypoint>
        <omgdi:waypoint x="665.5" y="271.5"></omgdi:waypoint>
        <omgdi:waypoint x="335.0" y="271.5"></omgdi:waypoint>
        <omgdi:waypoint x="335.0" y="217.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>