<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="AGENT_MCH_APPLY" name="合伙人后台-商户审批" isExecutable="true">
    <documentation>合伙人后台-商户审批</documentation>
    <startEvent id="start" name="流程开始" flowable:initiator="applyUserId" flowable:formFieldValidation="true"></startEvent>
    <exclusiveGateway id="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94"></exclusiveGateway>
    <sequenceFlow id="sid-5AC1EF59-552C-48DB-B691-A13D9425DD8C" sourceRef="directorAudit" targetRef="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94"></sequenceFlow>
    <endEvent id="successEnd" name="审批通过">
      <extensionElements>
        <flowable:executionListener event="end" delegateExpression="${newMchApply}"></flowable:executionListener>
      </extensionElements>
    </endEvent>
    <userTask id="directorAudit" name="会签审批" flowable:assignee="${leader}" flowable:formFieldValidation="true">
      <documentation>会签审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" name="是否可以编辑" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <flowable:formProperty id="canBackVariable" name="是否可以驳回" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${op.getLeaderByNumbers(&quot;00&quot;,&quot;01&quot;)}" flowable:elementVariable="leader">
        <completionCondition>${flowStatus == 101}</completionCondition>
      </multiInstanceLoopCharacteristics>
    </userTask>
    <sequenceFlow id="sid-ED5B23D0-1AAB-4715-8D05-C805BE98FA37" name="审批通过结束事件" sourceRef="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94" targetRef="successEnd">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 100}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="salerAudit" name="销售审批" flowable:assignee="${salesId}" flowable:formFieldValidation="true">
      <documentation>销售审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" name="是否可以编辑" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <flowable:formProperty id="canBackVariable" name="是否可以驳回" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-470D716D-3D31-4D40-88BE-0D513C5F6535"></exclusiveGateway>
    <sequenceFlow id="sid-D311AA16-26A1-46E5-969A-9AD075AA9629" sourceRef="salerAudit" targetRef="sid-470D716D-3D31-4D40-88BE-0D513C5F6535"></sequenceFlow>
    <sequenceFlow id="sid-6E6EA050-1EAC-4E1B-A89C-C0EC64D8D58F" name="审批通过" sourceRef="sid-470D716D-3D31-4D40-88BE-0D513C5F6535" targetRef="directorAudit">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 100}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="sid-38005999-869D-48EB-AE72-2E3B0A837D36" name="提交审批" flowable:assignee="${applyUserId}" flowable:formFieldValidation="true" flowable:skipExpression="${flowStatus != 101}">
      <documentation>提交审批</documentation>
      <extensionElements>
        <flowable:formProperty id="editBusinessVariable" type="boolean" variable="true" default="true" writable="false"></flowable:formProperty>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="sid-A42CF24D-2453-44BC-9455-2A4183B5F6C3" sourceRef="start" targetRef="sid-38005999-869D-48EB-AE72-2E3B0A837D36"></sequenceFlow>
    <sequenceFlow id="sid-E17B45FC-81B6-4690-81ED-A0EAEFBE7B7F" sourceRef="sid-38005999-869D-48EB-AE72-2E3B0A837D36" targetRef="salerAudit"></sequenceFlow>
    <sequenceFlow id="sid-EF739506-4944-4FD0-A1C5-3C6DABB05D26" name="审批驳回" sourceRef="sid-470D716D-3D31-4D40-88BE-0D513C5F6535" targetRef="sid-38005999-869D-48EB-AE72-2E3B0A837D36">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 101}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-87B27678-4AF1-46CD-BCD5-1E5282F6DF45" name="审批驳回" sourceRef="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94" targetRef="sid-38005999-869D-48EB-AE72-2E3B0A837D36">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${flowStatus == 101}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_AGENT_MCH_APPLY">
    <bpmndi:BPMNPlane bpmnElement="AGENT_MCH_APPLY" id="BPMNPlane_AGENT_MCH_APPLY">
      <bpmndi:BPMNShape bpmnElement="start" id="BPMNShape_start">
        <omgdc:Bounds height="30.0" width="30.0" x="30.0" y="157.75"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94" id="BPMNShape_sid-293C2AC6-3235-4625-9A5D-9EB6F4AE7B94">
        <omgdc:Bounds height="40.0" width="40.0" x="825.0" y="152.75"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="successEnd" id="BPMNShape_successEnd">
        <omgdc:Bounds height="28.0" width="28.0" x="990.0" y="158.75"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="directorAudit" id="BPMNShape_directorAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="630.0" y="132.75"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="salerAudit" id="BPMNShape_salerAudit">
        <omgdc:Bounds height="80.0" width="100.0" x="330.0" y="132.75"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-470D716D-3D31-4D40-88BE-0D513C5F6535" id="BPMNShape_sid-470D716D-3D31-4D40-88BE-0D513C5F6535">
        <omgdc:Bounds height="40.0" width="40.0" x="510.0" y="152.75"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-38005999-869D-48EB-AE72-2E3B0A837D36" id="BPMNShape_sid-38005999-869D-48EB-AE72-2E3B0A837D36">
        <omgdc:Bounds height="80.0" width="100.0" x="150.0" y="132.75"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-E17B45FC-81B6-4690-81ED-A0EAEFBE7B7F" id="BPMNEdge_sid-E17B45FC-81B6-4690-81ED-A0EAEFBE7B7F">
        <omgdi:waypoint x="249.94999999994312" y="172.75"></omgdi:waypoint>
        <omgdi:waypoint x="329.99999999997226" y="172.75"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-ED5B23D0-1AAB-4715-8D05-C805BE98FA37" id="BPMNEdge_sid-ED5B23D0-1AAB-4715-8D05-C805BE98FA37">
        <omgdi:waypoint x="864.5039373813873" y="173.18987341772151"></omgdi:waypoint>
        <omgdi:waypoint x="990.0000670136328" y="172.7940062513792"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A42CF24D-2453-44BC-9455-2A4183B5F6C3" id="BPMNEdge_sid-A42CF24D-2453-44BC-9455-2A4183B5F6C3">
        <omgdi:waypoint x="59.94999837389874" y="172.75"></omgdi:waypoint>
        <omgdi:waypoint x="150.0" y="172.75"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EF739506-4944-4FD0-A1C5-3C6DABB05D26" id="BPMNEdge_sid-EF739506-4944-4FD0-A1C5-3C6DABB05D26">
        <omgdi:waypoint x="530.5" y="153.25"></omgdi:waypoint>
        <omgdi:waypoint x="530.5" y="38.0"></omgdi:waypoint>
        <omgdi:waypoint x="200.0" y="38.0"></omgdi:waypoint>
        <omgdi:waypoint x="200.0" y="132.75"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5AC1EF59-552C-48DB-B691-A13D9425DD8C" id="BPMNEdge_sid-5AC1EF59-552C-48DB-B691-A13D9425DD8C">
        <omgdi:waypoint x="729.9499999999999" y="172.90090634441088"></omgdi:waypoint>
        <omgdi:waypoint x="825.4393939393933" y="173.18939393939394"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-D311AA16-26A1-46E5-969A-9AD075AA9629" id="BPMNEdge_sid-D311AA16-26A1-46E5-969A-9AD075AA9629">
        <omgdi:waypoint x="429.949999999997" y="172.91594684385382"></omgdi:waypoint>
        <omgdi:waypoint x="510.43333333333334" y="173.18333333333334"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-87B27678-4AF1-46CD-BCD5-1E5282F6DF45" id="BPMNEdge_sid-87B27678-4AF1-46CD-BCD5-1E5282F6DF45">
        <omgdi:waypoint x="845.5" y="192.19010185427007"></omgdi:waypoint>
        <omgdi:waypoint x="845.5" y="268.875"></omgdi:waypoint>
        <omgdi:waypoint x="200.0" y="268.875"></omgdi:waypoint>
        <omgdi:waypoint x="200.0" y="212.70000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6E6EA050-1EAC-4E1B-A89C-C0EC64D8D58F" id="BPMNEdge_sid-6E6EA050-1EAC-4E1B-A89C-C0EC64D8D58F">
        <omgdi:waypoint x="549.9485346889375" y="172.75"></omgdi:waypoint>
        <omgdi:waypoint x="630.0" y="172.75"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>