spring.application.name=service-flow
spring.cloud.nacos.config.server-addr=@nacosAddr@
spring.cloud.nacos.config.namespace=@nacosNamespace@
spring.cloud.nacos.config.file-extension=properties


spring.cloud.nacos.config.shared-dataids=dubbo.properties,db.properties,rocketmq.properties,redis.properties
spring.cloud.nacos.config.refreshable-dataids=dubbo.properties,db.properties,rocketmq.properties,redis.properties


logging.level.com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder=warn
