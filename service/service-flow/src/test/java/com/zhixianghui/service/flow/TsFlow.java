package com.zhixianghui.service.flow;

import com.google.common.collect.Maps;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.flow.vo.req.TaskHandleVo;
import com.zhixianghui.service.flow.core.biz.CommonFlowBiz;
import com.zhixianghui.service.flow.core.biz.ProcessInstanceBiz;
import com.zhixianghui.service.flow.core.biz.TaskBiz;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName TsFlow
 * @Description TODO
 * @Date 2021/4/25 15:23
 */
@SpringBootTest(classes = ServiceFlowApp.class)
@RunWith(SpringRunner.class)
public class TsFlow {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private ProcessInstanceBiz processInstanceBiz;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskBiz taskBiz;

    @Autowired
    private CommonFlowBiz commonFlowBiz;

    @Test
    public void tsStartFlow(){
            ProcessVo processVo = new ProcessVo();
            Map<String,Object> map = Maps.newHashMap();
            map.put("amount",150000);
            processVo.setFlowTopicName("生成21112条任务");
            processVo.setProcessDefinitionKey("apply");
          //  processVo.setFlowTopicType(2);
            FlowUserVo starter = new FlowUserVo();
            starter.setUserId(1L);
            starter.setUserName("lgl");
            starter.setPlatform(1000);
            processInstanceBiz.startProcessByProcessDefinitionKey(processVo,starter,null,map);
    }

    @Test
    public void startFlow(){
        ProcessVo processVo = new ProcessVo();
        processVo.setFlowTopicName("请假流程");
        processVo.setProcessDefinitionKey("Leave");
       // processVo.setFlowTopicType(2);
        FlowUserVo starter = new FlowUserVo();
        starter.setUserId(1L);
        starter.setUserName("lgl");
        starter.setPlatform(1000);
        Long startTime = System.currentTimeMillis();
        processInstanceBiz.startProcessByProcessDefinitionKey(processVo,starter,null,null);
        Long endTime = System.currentTimeMillis();
        System.out.println((endTime - startTime) + "ms");
    }

    @Test
    public void getTask(){
        Task task = taskService.createTaskQuery().taskId("d22d0513-a642-11eb-b65a-00ff0746f97e").singleResult();

        System.out.println(task);
    }

    @Test
    public void executeTask(){
        TaskHandleVo taskHandleVo = new TaskHandleVo();
        //taskBiz.executeTask();
    }

    @Test
    public void deploy(){
        repositoryService.createDeployment().addClasspathResource("static/项目经费申请.bpmn20.xml")
                .deploy();
    }

    @Test
    public void test(){
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(1L);
        flowUserVo.setPlatform(1000);
//        commonFlowBiz.getCommonFlowById(115L,"e95c6631-bc7e-11eb-800f-0050569466d3",flowUserVo,false);
    }
}
