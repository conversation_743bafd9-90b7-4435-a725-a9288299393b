<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-service</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zhixianghui</groupId>
    <artifactId>service-flow</artifactId>

    <build>
        <finalName>${appName}</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-service</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>component-starter</artifactId>

        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency> <!-- 把nacos作为配置中心时需要引入的依赖 -->
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- RocketMQ -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <!-- flowable流程引擎-->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter</artifactId>
            <version>6.6.0</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-flow</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-notify</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-merchant</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-common</artifactId>

        </dependency>
    </dependencies>
</project>
