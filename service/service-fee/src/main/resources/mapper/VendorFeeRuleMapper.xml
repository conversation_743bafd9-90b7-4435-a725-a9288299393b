<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.fee.entity.VendorFeeRule">
  <sql id="table">tbl_vendor_fee_rule</sql>
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.fee.entity.VendorFeeRule">
    <!--@mbg.generated-->
    <!--@Table fee.tbl_vendor_fee_relation-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="VENDOR_NO" jdbcType="VARCHAR" property="vendorNo" />
    <result column="VENDOR_NAME" jdbcType="VARCHAR" property="vendorName" />
    <result column="RULE_TYPE" jdbcType="SMALLINT" property="ruleType" />
    <result column="RULE_PARAM" jdbcType="VARCHAR" property="ruleParam"/>
    <result column="FORMULA_TYPE" jdbcType="SMALLINT" property="formulaType" />
    <result column="FIXED_FEE" jdbcType="DECIMAL" property="fixedFee" />
    <result column="FEE_RATE" jdbcType="DECIMAL" property="feeRate" />
    <result column="MIN_FEE" jdbcType="DECIMAL" property="minFee" />
    <result column="MAX_FEE" jdbcType="DECIMAL" property="maxFee" />
    <result column="CHARGE_TYPE" jdbcType="SMALLINT" property="chargeType" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="PRIORITY" jdbcType="INTEGER" property="priority" />
    <result column="DESCRIPTION" jdbcType="LONGVARCHAR" property="description" />
    <result column="VERSION" jdbcType="SMALLINT" property="version"/>
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_BY" jdbcType="VARCHAR" property="updateBy" />
    <result column="REMOVED" jdbcType="BOOLEAN" property="removed"/>
  </resultMap>

  <sql id="Base_Column_List">
    ID,
    VENDOR_NO,
    VENDOR_NAME,
    RULE_TYPE,
    RULE_PARAM,
    FORMULA_TYPE,
    FIXED_FEE,
    FEE_RATE,
    MIN_FEE,
    MAX_FEE,
    CHARGE_TYPE,
    `STATUS`,
    PRIORITY,
    DESCRIPTION,
    VERSION,
    CREATE_TIME,
    CREATE_BY,
    UPDATE_TIME,
    UPDATE_BY,
    REMOVED
  </sql>

  <sql id="condition_sql">
      <if test="id != null">
        and ID = #{id,jdbcType=BIGINT}
      </if>
      <if test="vendorNo != null and vendorNo != '' ">
          and VENDOR_NO =#{vendorNo,jdbcType=VARCHAR}
      </if>
      <if test="vendorName != null and vendorName != '' ">
        and VENDOR_NAME = #{vendorName,jdbcType=VARCHAR}
      </if>
      <if test="vendorNameLike != null and vendorNameLike != '' ">
          and VENDOR_NAME like concat('%',#{vendorNameLike,jdbcType=VARCHAR},'%')
      </if>
      <if test="ruleType != null">
        and RULE_TYPE = #{ruleType,jdbcType=SMALLINT}
      </if>
      <if test="ruleParam != null and ruleParam != '' ">
        and RULE_PARAM = #{ruleParam,jdbcType=VARCHAR}
      </if>
      <if test="formulaType != null">
        and FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT}
      </if>
      <if test="fixedFee != null">
        and FIXED_FEE = #{fixedFee,jdbcType=DECIMAL}
      </if>
      <if test="feeRate != null">
        and FEE_RATE = #{feeRate,jdbcType=DECIMAL}
      </if>
      <if test="minFee != null">
        and MIN_FEE = #{minFee,jdbcType=DECIMAL}
      </if>
      <if test="maxFee != null">
        and MAX_FEE = #{maxFee,jdbcType=DECIMAL}
      </if>
      <if test="chargeType != null">
        and CHARGE_TYPE = #{chargeType,jdbcType=SMALLINT}
      </if>
      <if test="status != null">
        and `STATUS` = #{status,jdbcType=INTEGER}
      </if>
      <if test="priority != null">
        and PRIORITY = #{priority,jdbcType=INTEGER}
      </if>
      <if test="description != null and description != ''">
        AND DESCRIPTION = #{description,jdbcType=LONGVARCHAR}
      </if>
      <if test="createTime != null and createTime != ''">
        AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="createBy != null and createBy != ''">
        AND CREATE_BY = #{createBy,jdbcType=VARCHAR}
      </if>
      <if test="updateTime != null and updateTime != ''">
        AND UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateBy != null and updateBy != ''">
        AND UPDATE_BY = #{updateBy,jdbcType=VARCHAR}
      </if>
      <if test="removed != null ">
        AND REMOVED = #{removed,jdbcType=BOOLEAN}
      </if>
   </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
        <include refid="Base_Column_List" />
    from
        <include refid="table"/>
    where ID = #{id,jdbcType=BIGINT}
  </select>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            <include refid="table"/>
             as m
        <where>
                <include refid="condition_sql"/>
        </where>
            order by m.id desc
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
            <include refid="Base_Column_List"/>
		from
            <include refid="table"/> as m
        <where>
            <include refid="condition_sql"/>
        </where>
		order by m.id desc
    </select>

    <select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

  <delete id="deleteById" parameterType="java.lang.Long">
    delete from <include refid="table"/> where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.zhixianghui.facade.fee.entity.VendorFeeRule" useGeneratedKeys="true">
    insert into <include refid="table"/>
     (VENDOR_NO,
      VENDOR_NAME,
      RULE_TYPE,
      RULE_PARAM,
      FORMULA_TYPE,
      FIXED_FEE,
      FEE_RATE,
      MIN_FEE,
      MAX_FEE,
      CHARGE_TYPE,
      `STATUS`,
      PRIORITY,
      DESCRIPTION,
      VERSION,
      CREATE_TIME,
      CREATE_BY,
      UPDATE_TIME,
      UPDATE_BY
      )
    values (#{vendorNo,jdbcType=VARCHAR},
        #{vendorName,jdbcType=VARCHAR},
        #{ruleType,jdbcType=SMALLINT},
        #{ruleParam,jdbcType=VARCHAR},
        #{formulaType,jdbcType=SMALLINT},
        #{fixedFee,jdbcType=DECIMAL},
        #{feeRate,jdbcType=DECIMAL},
        #{minFee,jdbcType=DECIMAL},
        #{maxFee,jdbcType=DECIMAL},
        #{chargeType,jdbcType=SMALLINT},
        #{status,jdbcType=INTEGER},
        #{priority,jdbcType=INTEGER},
        #{description,jdbcType=LONGVARCHAR},
        0, #{createTime,jdbcType=TIMESTAMP},
        #{createBy,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{updateBy,jdbcType=VARCHAR}
      )
  </insert>

  <update id="update" parameterType="com.zhixianghui.facade.fee.entity.VendorFeeRule">
    <!--@mbg.generated-->
    update  <include refid="table"/>
    set   VENDOR_NO = #{vendorNo,jdbcType=VARCHAR},
          VENDOR_NAME = #{vendorName,jdbcType=VARCHAR},
          RULE_TYPE = #{ruleType,jdbcType=SMALLINT},
          RULE_PARAM = #{ruleParam,jdbcType=VARCHAR},
          FORMULA_TYPE = #{formulaType,jdbcType=SMALLINT},
          FIXED_FEE = #{fixedFee,jdbcType=DECIMAL},
          FEE_RATE = #{feeRate,jdbcType=DECIMAL},
          MIN_FEE = #{minFee,jdbcType=DECIMAL},
          MAX_FEE = #{maxFee,jdbcType=DECIMAL},
          CHARGE_TYPE = #{chargeType,jdbcType=SMALLINT},
          `STATUS` = #{status,jdbcType=INTEGER},
          PRIORITY = #{priority,jdbcType=INTEGER},
          DESCRIPTION = #{description,jdbcType=LONGVARCHAR},
          VERSION = #{version,jdbcType=SMALLINT} + 1,
          CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
          CREATE_BY = #{createBy,jdbcType=VARCHAR},
          UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
          UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
          REMOVED = #{removed,jdbcType=BOOLEAN}
    where ID = #{id,jdbcType=BIGINT}  and VERSION = #{version,jdbcType=SMALLINT}
  </update>
</mapper>