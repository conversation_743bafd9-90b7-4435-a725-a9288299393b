<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.fee.entity.AgentMonthBill">
    <sql id="table">tbl_agent_month_bill</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.fee.entity.AgentMonthBill">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="AGENT_NO" property="agentNo" jdbcType="VARCHAR"/>
        <result column="AGENT_NAME" property="agentName" jdbcType="VARCHAR"/>
        <result column="BILL_DATE" property="billDate" jdbcType="VARCHAR"/>
        <result column="AGENT_TYPE" property="agentType" jdbcType="SMALLINT"/>
        <result column="TAX" property="tax" jdbcType="DECIMAL"/>
        <result column="WITHHOLDING_TAX_RATIO" property="withholdingTaxRatio" jdbcType="DECIMAL"/>
        <result column="SETTLEMENT_AMOUNT" property="settlementAmount" jdbcType="DECIMAL"/>
        <result column="SALER_ID" property="salerId" jdbcType="BIGINT"/>
        <result column="SALER_NAME" property="salerName" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="SETTLEMENT_STATUS" property="settlementStatus" jdbcType="INTEGER"/>
        <result column="ENTERPRISE_ID" property="enterpriseId" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="SMALLINT"/>
        <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"/>
        <result column="AGENT_MONTH_PROFIT" property="agentMonthProfit" jdbcType="DECIMAL"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, AGENT_NO, AGENT_NAME, BILL_DATE, AGENT_TYPE, TAX, WITHHOLDING_TAX_RATIO, SETTLEMENT_AMOUNT, SALER_ID, SALER_NAME, NOTE, SETTLEMENT_STATUS, ENTERPRISE_ID, CREATE_TIME, UPDATE_TIME, DELETE_FLAG, BILL_NO, AGENT_MONTH_PROFIT
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.fee.entity.AgentMonthBill">
        INSERT INTO <include refid="table" /> (
            VERSION,
            AGENT_NO,
            AGENT_NAME,
            BILL_DATE,
            AGENT_TYPE,
            TAX,
            WITHHOLDING_TAX_RATIO,
            SETTLEMENT_AMOUNT,
            SALER_ID,
            SALER_NAME,
            NOTE,
            SETTLEMENT_STATUS,
            ENTERPRISE_ID,
            CREATE_TIME,
            UPDATE_TIME,
            DELETE_FLAG,
            BILL_NO,
            AGENT_MONTH_PROFIT
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{agentNo,jdbcType=VARCHAR},
            #{agentName,jdbcType=VARCHAR},
            #{billDate,jdbcType=VARCHAR},
            #{agentType,jdbcType=SMALLINT},
            #{tax,jdbcType=DECIMAL},
            #{withholdingTaxRatio,jdbcType=DECIMAL},
            #{settlementAmount,jdbcType=DECIMAL},
            #{salerId,jdbcType=BIGINT},
            #{salerName,jdbcType=VARCHAR},
            #{note,jdbcType=VARCHAR},
            #{settlementStatus,jdbcType=INTEGER},
            #{enterpriseId,jdbcType=BIGINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{deleteFlag,jdbcType=SMALLINT},
            #{billNo,jdbcType=VARCHAR},
            #{agentMonthProfit,jdbcType=DECIMAL}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            AGENT_NO,
            AGENT_NAME,
            BILL_DATE,
            AGENT_TYPE,
            TAX,
            WITHHOLDING_TAX_RATIO,
            SETTLEMENT_AMOUNT,
            SALER_ID,
            SALER_NAME,
            NOTE,
            SETTLEMENT_STATUS,
            ENTERPRISE_ID,
            CREATE_TIME,
            UPDATE_TIME,
            DELETE_FLAG,
            BILL_NO,
            AGENT_MONTH_PROFIT
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.agentNo,jdbcType=VARCHAR},
            #{item.agentName,jdbcType=VARCHAR},
            #{item.billDate,jdbcType=VARCHAR},
            #{item.agentType,jdbcType=SMALLINT},
            #{item.tax,jdbcType=DECIMAL},
            #{item.withholdingTaxRatio,jdbcType=DECIMAL},
            #{item.settlementAmount,jdbcType=DECIMAL},
            #{item.salerId,jdbcType=BIGINT},
            #{item.salerName,jdbcType=VARCHAR},
            #{item.note,jdbcType=VARCHAR},
            #{item.settlementStatus,jdbcType=INTEGER},
            #{item.enterpriseId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=SMALLINT},
            #{item.billNo,jdbcType=VARCHAR},
            #{item.agentMonthProfit,jdbcType=DECIMAL}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.fee.entity.AgentMonthBill">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="agentName != null">
                AGENT_NAME = #{agentName,jdbcType=VARCHAR},
            </if>
            <if test="billDate != null">
                BILL_DATE = #{billDate,jdbcType=VARCHAR},
            </if>
            <if test="agentType != null">
                AGENT_TYPE = #{agentType,jdbcType=SMALLINT},
            </if>
            <if test="tax != null">
                TAX = #{tax,jdbcType=DECIMAL},
            </if>
            <if test="withholdingTaxRatio != null">
                WITHHOLDING_TAX_RATIO = #{withholdingTaxRatio,jdbcType=DECIMAL},
            </if>
            <if test="settlementAmount != null">
                SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
            </if>
            <if test="salerId != null">
                SALER_ID = #{salerId,jdbcType=BIGINT},
            </if>
            <if test="salerName != null">
                SALER_NAME = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="note != null">
                NOTE = #{note,jdbcType=VARCHAR},
            </if>
            <if test="settlementStatus != null">
                SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
            </if>
            <if test="enterpriseId != null">
                ENTERPRISE_ID = #{enterpriseId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                DELETE_FLAG = #{deleteFlag,jdbcType=SMALLINT},
            </if>
            <if test="billNo != null">
                BILL_NO = #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="agentMonthProfit != null">
                AGENT_MONTH_PROFIT = #{agentMonthProfit,jdbcType=DECIMAL}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.fee.entity.AgentMonthBill">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="agentNo != null">
                AGENT_NO = #{agentNo,jdbcType=VARCHAR},
            </if>
            <if test="agentName != null">
                AGENT_NAME = #{agentName,jdbcType=VARCHAR},
            </if>
            <if test="billDate != null">
                BILL_DATE = #{billDate,jdbcType=VARCHAR},
            </if>
            <if test="agentType != null">
                AGENT_TYPE = #{agentType,jdbcType=SMALLINT},
            </if>
            <if test="tax != null">
                TAX = #{tax,jdbcType=DECIMAL},
            </if>
            <if test="withholdingTaxRatio != null">
                WITHHOLDING_TAX_RATIO = #{withholdingTaxRatio,jdbcType=DECIMAL},
            </if>
            <if test="settlementAmount != null">
                SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL},
            </if>
            <if test="salerId != null">
                SALER_ID = #{salerId,jdbcType=BIGINT},
            </if>
            <if test="salerName != null">
                SALER_NAME = #{salerName,jdbcType=VARCHAR},
            </if>
            <if test="note != null">
                NOTE = #{note,jdbcType=VARCHAR},
            </if>
            <if test="settlementStatus != null">
                SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
            </if>
            <if test="enterpriseId != null">
                ENTERPRISE_ID = #{enterpriseId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                DELETE_FLAG = #{deleteFlag,jdbcType=SMALLINT},
            </if>
            <if test="billNo != null">
                BILL_NO = #{billNo,jdbcType=VARCHAR},
            </if>
            <if test="agentMonthProfit != null">
                AGENT_MONTH_PROFIT = #{agentMonthProfit,jdbcType=DECIMAL}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <select id="listByIdList" resultType="com.zhixianghui.facade.fee.entity.AgentMonthBill">
        SELECT
            ID as id,
            VERSION as version,
            AGENT_NO as agentNo,
            AGENT_NAME as agentName,
            BILL_DATE as billDate,
            AGENT_TYPE as agentType,
            TAX as tax,
            WITHHOLDING_TAX_RATIO as taxPercent,
            SETTLEMENT_AMOUNT as settlementAmount,
            SALER_ID as salerId,
            SALER_NAME as salerName,
            NOTE as note,
            SETTLEMENT_STATUS as settlementStatus,
            ENTERPRISE_ID as enterpriseId,
            CREATE_TIME as createTime,
            UPDATE_TIME as updateTime,
            BILL_NO as billNo,
            AGENT_MONTH_PROFIT as agentMonthProfit
        FROM
        <include refid="table" />
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listSumMap" parameterType="java.util.Map" resultType="java.util.Map">
        select
        BILL_DATE as billDate,
        AGENT_MONTH_PROFIT as agentMonthProfit,
        SETTLEMENT_AMOUNT as settlementAmount
        from
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        order by BILL_DATE desc
    </select>

    <select id="getTotalProfitByAgent" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        IFNULL(SUM(AGENT_MONTH_PROFIT),0) as agentMonthProfit,
        IFNULL(SUM(SETTLEMENT_AMOUNT),0) as settlementAmount
        from
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <select id="listSumPage" parameterType="java.util.Map" resultType="com.zhixianghui.facade.fee.vo.AgentMonthBillVo">
        SELECT
            ID as id,
            VERSION as version,
            AGENT_NO as agentNo,
            AGENT_NAME as agentName,
            BILL_DATE as billDate,
            AGENT_TYPE as agentType,
            TAX as tax,
            WITHHOLDING_TAX_RATIO as taxPercent,
            SETTLEMENT_AMOUNT as settlementAmount,
            SALER_ID as salerId,
            SALER_NAME as salerName,
            NOTE as note,
            SETTLEMENT_STATUS as settlementStatus,
            ENTERPRISE_ID as enterpriseId,
            CREATE_TIME as createTime,
            UPDATE_TIME as updateTime,
            BILL_NO as billNo,
            AGENT_MONTH_PROFIT as agentMonthProfit
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
<!--                <![CDATA[ ORDER BY ID DESC ]]>-->
            order by BILL_DATE desc
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null and id != ''">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="maxId != null">
            AND ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
        <if test="version != null and version != ''">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="agentNo != null and agentNo !=''">
            and AGENT_NO = #{agentNo,jdbcType=VARCHAR}
        </if>
        <if test="agentName != null and agentName !=''">
            and AGENT_NAME like concat('%',#{agentName,jdbcType=VARCHAR},'%')
        </if>
        <if test="billDate != null and billDate !=''">
            and BILL_DATE = #{billDate,jdbcType=VARCHAR}
        </if>
        <if test="agentType != null and agentType != ''">
            and AGENT_TYPE = #{agentType,jdbcType=SMALLINT}
        </if>
        <if test="tax != null and tax != ''">
            and TAX = #{tax,jdbcType=DECIMAL}
        </if>
        <if test="withholdingTaxRatio != null">
            and WITHHOLDING_TAX_RATIO = #{withholdingTaxRatio,jdbcType=DECIMAL}
        </if>
        <if test="settlementAmount != null">
            and SETTLEMENT_AMOUNT = #{settlementAmount,jdbcType=DECIMAL}
        </if>
        <if test="salerId != null and salerId != ''">
            and SALER_ID = #{salerId,jdbcType=BIGINT}
        </if>
        <if test="salerName != null and salerName !=''">
            and SALER_NAME = #{salerName,jdbcType=VARCHAR}
        </if>
        <if test="note != null and note !=''">
            and NOTE = #{note,jdbcType=VARCHAR}
        </if>
        <if test="settlementStatus != null and settlementStatus != ''">
            and SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER}
        </if>
        <if test="enterpriseId != null">
            and ENTERPRISE_ID = #{enterpriseId,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
            and DELETE_FLAG = #{deleteFlag,jdbcType=SMALLINT}
        </if>
        <if test="billNo != null and billNo !=''">
            and BILL_NO = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test="agentMonthProfit != null">
            and AGENT_MONTH_PROFIT = #{agentMonthProfit,jdbcType=DECIMAL}
        </if>
        <if test="beginBillDate != null and endBillDate != null">
            and BILL_DATE <![CDATA[ >= ]]> #{beginBillDate,jdbcType=VARCHAR}
            and BILL_DATE <![CDATA[ < ]]> #{endBillDate,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
