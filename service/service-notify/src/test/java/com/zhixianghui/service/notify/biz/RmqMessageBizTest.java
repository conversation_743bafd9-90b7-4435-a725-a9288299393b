package com.zhixianghui.service.notify.biz;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.notify.entity.RmqMessageRecord;
import com.zhixianghui.service.notify.ServiceNotifyApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-24 18:27
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceNotifyApp.class)
public class RmqMessageBizTest {

    @Autowired
    private RmqMessageBiz rmqMessageBiz;
    @Test
    public void listRmqMessagePage() {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("merchantNo","merchantno1121");
        PageResult<List<RmqMessageRecord>> list = rmqMessageBiz.listRmqMessagePage(paramMap, PageParam.newInstance(0,10));

        System.out.println(list);
    }

    @Test
    public void listByIdList(){
        List<Long> idList = Lists.newArrayList();
        idList.add(1L);
        idList.add(3L);
        idList.add(6L);
        List<RmqMessageRecord> rmqMessageRecords = rmqMessageBiz.listBy(Collections.singletonMap("idList",idList));
        System.out.println(rmqMessageRecords);
    }
}