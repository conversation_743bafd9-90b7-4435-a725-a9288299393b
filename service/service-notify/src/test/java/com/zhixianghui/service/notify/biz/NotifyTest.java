package com.zhixianghui.service.notify.biz;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.service.notify.ServiceNotifyApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @description: test
 * @author: xingguang li
 * @created: 2020/11/09 11:50
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceNotifyApp.class)
public class NotifyTest {

    @Autowired
    private NotifyBiz notifyBiz;

    @Test
    public void testSendDelay(){
        String content = "<h1 style=\"color:red;\">一封测试邮件</h1>";
        EmailParamDto dto = new EmailParamDto();
        dto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        dto.setTo("");
        dto.setHtmlFormat(false);
        dto.setContent(content);
        dto.setSubject("测试-subject2");

        notifyBiz.sendOne(MessageMsgDest.TOPIC_SEND_EMAIL_ASYNC, "9022222222",
                "13233333", 1, MessageMsgDest.TAG_SEND_EMAIL_ASYNC,
                JSONObject.toJSONString(dto), MsgDelayLevelEnum.M_2.getValue());
    }
}
