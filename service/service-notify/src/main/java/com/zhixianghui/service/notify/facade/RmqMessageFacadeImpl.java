package com.zhixianghui.service.notify.facade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.notify.entity.RmqMessageRecord;
import com.zhixianghui.facade.notify.service.RmqMessageFacade;
import com.zhixianghui.service.notify.biz.NotifyBiz;
import com.zhixianghui.service.notify.biz.RmqMessageBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * @description: rmq消息管理实现
 * @author: xingguang li
 * @created: 2020/09/18 15:58
 */
@Service(timeout = 60000,retries = -1)
public class RmqMessageFacadeImpl implements RmqMessageFacade {

    @Autowired
    private RmqMessageBiz rmqMessageBiz;
    @Autowired
    private NotifyBiz notifyBiz;

    @Override
    public void addMessage(RmqMessageRecord mqMessageRecord) {
        rmqMessageBiz.addMessage(mqMessageRecord, true);
    }

    @Override
    public PageResult<List<RmqMessageRecord>> listRmqMessagePage(Map<String, Object> paramMap, PageParam pageParam) {
        return rmqMessageBiz.listRmqMessagePage(paramMap, pageParam);
    }

    @Override
    public boolean reissueMessage(Long id) {
        return notifyBiz.reissueMessage(id);
    }

    @Override
    public void batchReissueMessage(List<Long> idList) {
        notifyBiz.batchReissueMessage(idList);
    }

    @Override
    public void batchReissueMessage(Map<String, Object> param) {
        notifyBiz.batchReissueMessage(param);
    }
}
