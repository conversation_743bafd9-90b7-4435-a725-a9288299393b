package com.zhixianghui.service.notify.biz;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.notify.entity.RmqMessageRecord;
import com.zhixianghui.facade.notify.entity.Unique;
import com.zhixianghui.service.notify.dao.NotifyUniqueDao;
import com.zhixianghui.service.notify.dao.RmqMessageRecordDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: mq消息biz
 * @author: xingguang li
 * @created: 2020/09/09 18:10
 */
@Service
public class RmqMessageBiz {

    @Autowired
    private RmqMessageRecordDao rmqMessageRecordDao;
    @Autowired
    private NotifyUniqueDao notifyUniqueDao;

    @Transactional(rollbackFor = Exception.class)
    public void addMessage(RmqMessageRecord rmqMessageRecord, boolean isOverride) {
        //在商户号和流水号不为空的情况下做唯一性约束才有意义，否则直接保存即可
        if (StringUtil.isNotEmpty(rmqMessageRecord.getMerchantNo())
                && StringUtil.isNotEmpty(rmqMessageRecord.getTrxNo())) {
            Unique unique = new Unique();
            //目前唯一性是根据merchantno+trxno+type确定
            unique.setUniqueKey(String.format("%s-%s-%s-%s",rmqMessageRecord.getTopic(),rmqMessageRecord.getTags(), rmqMessageRecord.getMerchantNo(), rmqMessageRecord.getTrxNo()));
            //捕获异常，如果isOverride，则重写消息体然后更新
            try {
                notifyUniqueDao.insert(unique);
                rmqMessageRecordDao.insert(rmqMessageRecord);
            } catch (Exception e){
                //如果是鉴定为已存在该消息，则更新即可.
                if(e.getMessage().contains("Duplicate") ||
                        e.getMessage().contains("UNIQUE_KEY")) {
                    Map<String,Object> params = new HashMap<>();
                    params.put("merchantNo", rmqMessageRecord.getMerchantNo());
                    params.put("trxNo", rmqMessageRecord.getTrxNo());
                    params.put("topic", rmqMessageRecord.getTopic());
                    params.put("tags", rmqMessageRecord.getTags());
                    RmqMessageRecord recordEntityOrg = rmqMessageRecordDao.getOne("getByMerchantNoTrxNoType", params);
                    rmqMessageRecord.setId(recordEntityOrg.getId());
                    rmqMessageRecord.setVersion(recordEntityOrg.getVersion());
                    //如果选择不更新消息体（isOverride==false），则保留原来的消息体
                    if (!isOverride) {
                        rmqMessageRecord.setMessage(recordEntityOrg.getMessage());
                    }
                    updateMessage(rmqMessageRecord);
                } else {
                    throw e;
                }
            }
        } else {
            //没有商户号和流水号，则直接保存消息即可，不做唯一性限制
            rmqMessageRecordDao.insert(rmqMessageRecord);
        }
    }

    public void updateMessage(RmqMessageRecord rmqMessageRecord) {
        rmqMessageRecordDao.updateIfNotNull(rmqMessageRecord);
    }

    public PageResult<List<RmqMessageRecord>> listRmqMessagePage(Map<String, Object> paramMap, PageParam pageParam) {
        return rmqMessageRecordDao.listPage(paramMap, pageParam);
    }

    public RmqMessageRecord getById(Long id) {
        return rmqMessageRecordDao.getById(id);
    }

    public List<RmqMessageRecord> listBy(Map<String, Object> paramMap) {
        return rmqMessageRecordDao.listBy(paramMap);
    }
}
