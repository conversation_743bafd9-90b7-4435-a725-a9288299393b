package com.zhixianghui.service.notify.facade;

import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.notify.biz.NotifyBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description: 消息通知实现
 * @author: xingguang li
 * @created: 2020/09/18 16:05
 */
@Service(timeout = 60000,retries = -1)
public class NotifyFacadeImpl implements NotifyFacade {

    @Autowired
    private NotifyBiz notifyBiz;

    @Override
    public boolean sendOne(String topic, String merchantNo, String trxNo, int notifyType, String tags, String msg) {
        return notifyBiz.sendOne(topic, merchantNo, trxNo, notifyType, tags, msg, 0);
    }

    @Override
    public boolean sendOne(String topic, int notifyType, String tags, String msg) {
        return notifyBiz.sendOne(topic,null,null, notifyType, tags, msg, 0);
    }

    @Override
    public boolean sendOne(String topic, int notifyType, String tags, String msg, int delayLevel) {
        return notifyBiz.sendOne(topic,null,null,notifyType,tags,msg,delayLevel);
    }

    @Override
    public boolean sendOne(String topic, String merchantNo, String trxNo, int notifyType, String tags, String msg, int delayLevel) {
        return notifyBiz.sendOne(topic, merchantNo, trxNo, notifyType, tags, msg, delayLevel);
    }

}
