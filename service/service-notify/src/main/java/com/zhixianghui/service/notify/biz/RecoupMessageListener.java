package com.zhixianghui.service.notify.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.notify.entity.RmqMessageRecord;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.List;

/**
 * 合伙人计费订单补偿
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MESSAGE_RECOUP, consumeThreadMax = 3, consumerGroup = "messageRecoup")
public class RecoupMessageListener extends BaseRocketMQListener<String> {

    @Autowired
    private RmqMessageBiz rmqMessageBiz;
    @Autowired
    private NotifyBiz notifyBiz;

    @Override
    public void validateJsonParam(String msg) {
        if (StringUtil.isEmpty(msg)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("时间参数不能为空");
        }
    }

    @Override
    public void consumeMessage(String msg) {
        log.info("查询参数: {}", msg);
        HashMap<String, Object> params = JSON.parseObject(msg, HashMap.class);
        int page = 1, pageSize = 500;
        if (params.containsKey("pageSize")) {
            pageSize = (Integer) params.getOrDefault("pageSize", 500);
        }
        log.info("查询条数: {}", pageSize);
        PageParam pageParam = PageParam.newInstance(page, pageSize);
        PageResult<List<RmqMessageRecord>> result = rmqMessageBiz.listRmqMessagePage(params, pageParam);
        int success = 0, fail = 0;
        while (!CollectionUtils.isEmpty(result.getData())) {
            for (RmqMessageRecord record : result.getData()) {
                if (notifyBiz.reissueMessage(record.getId())) {
                    success++;
                } else {
                    log.error("该消息: {} 补发失败", JSONObject.toJSONString(record));
                    fail++;
                }
            }
            page++;
            pageParam = PageParam.newInstance(page, pageSize);
            result = rmqMessageBiz.listRmqMessagePage(params, pageParam);
        }
        log.info("成功发送消息{}条, 失败: {}", success, fail);
    }
}
