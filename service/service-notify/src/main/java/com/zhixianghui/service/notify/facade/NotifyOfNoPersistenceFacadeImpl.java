package com.zhixianghui.service.notify.facade;

import com.zhixianghui.common.statics.dto.rmq.MsgDto;
import com.zhixianghui.facade.notify.service.NotifyOfNoPersistenceFacade;
import com.zhixianghui.starter.comp.component.RMQSender;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.function.Consumer;

/**
 * @description: NotifyOfNoPersistenceFacade实现类
 * @author: xingguang li
 * @created: 2020/09/22 15:12
 */
@Service(timeout = 60000,retries = -1)
public class NotifyOfNoPersistenceFacadeImpl implements NotifyOfNoPersistenceFacade {

    @Autowired
    private RMQSender rmqSender;

    @Override
    public boolean sendOne(MsgDto msg) {
        return rmqSender.sendOne(msg).getSendOk();
    }

    @Override
    public void sendOne(MsgDto msg, Consumer<MsgDto> onFail) {
        rmqSender.sendOne(msg, onFail);
    }

    @Override
    public void sendOneWay(MsgDto msg) {
        rmqSender.sendOneWay(msg);
    }

    @Override
    public void sendOneAsync(MsgDto msg, Consumer<MsgDto> callback) {
        rmqSender.sendOneAsync(msg, callback);
    }

    @Override
    public boolean sendBatch(String destination, List<? extends MsgDto> msgList) {
        return rmqSender.sendBatch(destination, msgList);
    }

    @Override
    public boolean sendBatch(List<? extends MsgDto> msgList) {
        return rmqSender.sendBatch(msgList);
    }

    @Override
    public boolean sendTrans(MsgDto msg) {
        return rmqSender.sendTrans(msg);
    }
}
