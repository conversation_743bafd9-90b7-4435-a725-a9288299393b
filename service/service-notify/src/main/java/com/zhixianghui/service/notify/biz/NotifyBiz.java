package com.zhixianghui.service.notify.biz;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.rmq.MqResult;
import com.zhixianghui.common.statics.dto.rmq.MsgDto;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.notify.entity.RmqMessageRecord;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.starter.comp.component.RMQSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @description: 消息通知业务类
 * @author: xingguang li
 * @created: 2020/09/18 16:07
 */
@Component
public class NotifyBiz {

    private Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private RMQSender rmqSender;
    @Autowired
    private RmqMessageBiz rmqMessageBiz;

    /**
     * 发送消息
     * @param topic {@link MessageMsgDest}
     * @param merchantNo 商编
     * @param trxNo 流水号
     * @param notifyType 通知类型 {@link NotifyTypeEnum#getValue()}
     * @param tags mq的tags
     * @param msg 消息体
     * @param delayLevel 延迟的级别，{@link MsgDelayLevelEnum#getValue()}
     * @return
     * @return
     */
    public boolean sendOne(String topic, String merchantNo, String trxNo, int notifyType, String tags, String msg, int delayLevel) {
        try {
            //先持久化消息message
            RmqMessageRecord rmqMessageRecord = addMessage(topic, merchantNo, trxNo, notifyType, tags, msg);
            //组装消息发送mq
            MsgDto<String> msgDto = new MsgDto<>(topic, tags, trxNo, msg);
            msgDto.setDelayLevel(delayLevel);
            MqResult mqResult = rmqSender.sendOne(msgDto);
            //更新mq的msgid，此msgid非必须，暂时不更新来增加系统开销
//            rmqMessageRecord.setMsgId(mqResult.getMsgId());
//            rmqMessageBiz.updateMessage(rmqMessageRecord);
            return mqResult.getSendOk();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("trxNo : {} send topic : {} has error:",trxNo, topic, e);
            return false;
        }
    }

    private RmqMessageRecord addMessage(String topic, String merchantNo, String trxNo, int notifyType, String tags, String msg) {
        RmqMessageRecord rmqMessageRecord = new RmqMessageRecord(topic,tags,msg,merchantNo,trxNo,notifyType);
        //目前将trxno设置为keys
        rmqMessageRecord.setKeys(trxNo);
        rmqMessageBiz.addMessage(rmqMessageRecord, true);
        return rmqMessageRecord;
    }


    public boolean reissueMessage(Long id) {
        RmqMessageRecord rmqMessageRecord = rmqMessageBiz.getById(id);
        if(rmqMessageRecord == null){
            log.error("reissueMessage id : {}, not exist",id);
            return false;
        }
        try {
            //组装消息发送mq
            MsgDto<String> msgDto = new MsgDto<>(rmqMessageRecord.getTopic(), rmqMessageRecord.getTags(),
                    rmqMessageRecord.getTrxNo(),rmqMessageRecord.getMessage());
            MqResult mqResult = rmqSender.sendOne(msgDto);
            return mqResult.getSendOk();
        } catch (Exception e) {
            log.error("trxNo : {} send topic : {} has error:",rmqMessageRecord.getTrxNo(), rmqMessageRecord.getTopic(), e);
            return false;
        }
    }

    public void batchReissueMessage(List<Long> idList) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("idList",idList);
        List<RmqMessageRecord> rmqMessageRecordList = rmqMessageBiz.listBy(paramMap);
        StringBuilder sb = new StringBuilder();
        rmqMessageRecordList.forEach(
                record->{
                    try {
                        //组装消息发送mq
                        MsgDto<String> msgDto = new MsgDto<>(record.getTopic(), record.getTags(),
                                record.getTrxNo(),record.getMessage());
                        MqResult mqResult = rmqSender.sendOne(msgDto);
                        if(!mqResult.getSendOk()){
                            sb.append(record.getId()).append(",");
                        }
                    } catch (Exception e) {
                        log.error("trxNo : {} send topic : {} has error:",record.getTrxNo(), record.getTopic(), e);
                        sb.append(record.getId()).append(",");
                    }
                }
        );
        if(sb.toString().length() > 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("以下id消息补发失败:"+sb.toString());
        }
    }

    @Async
    public void batchReissueMessage(Map<String,Object> param) {

        int pageSize=500;
        int currentPage = 1;
        PageResult<List<RmqMessageRecord>> listPageResult = null;
        do {
            final PageParam pageParam = PageParam.newInstance(currentPage, pageSize);
            listPageResult = rmqMessageBiz.listRmqMessagePage(param, pageParam);
            List<RmqMessageRecord> rmqMessageRecordList = listPageResult.getData();
            rmqMessageRecordList.forEach(
                    record->{
                        try {
                            //组装消息发送mq
                            MsgDto<String> msgDto = new MsgDto<>(record.getTopic(), record.getTags(),
                                    record.getTrxNo(),record.getMessage());
                            MqResult mqResult = rmqSender.sendOne(msgDto);
                        } catch (Exception e) {
                            log.error("trxNo : {} send topic : {} has error:",record.getTrxNo(), record.getTopic(), e);
                        }
                    }
            );
            currentPage++;
        }while (listPageResult!=null&&listPageResult.getData()!=null||!listPageResult.getData().isEmpty());

    }
}
