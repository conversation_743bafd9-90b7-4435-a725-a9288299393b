<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.notify.entity.RmqMessageRecord">
    <sql id="table"> tbl_mq_message_record </sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.notify.entity.RmqMessageRecord">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="DATE" />
        <result column="NOTIFY_TYPE" property="notifyType" jdbcType="SMALLINT" />
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR" />
        <result column="TRX_NO" property="trxNo" jdbcType="VARCHAR" />
        <result column="TOPIC" property="topic" jdbcType="VARCHAR" />
        <result column="MSG_ID" property="msgId" jdbcType="VARCHAR" />
        <result column="TAGS" property="tags" jdbcType="VARCHAR" />
        <result column="KEYS" property="keys" jdbcType="VARCHAR" />
        <result column="MESSAGE" property="message" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
        ID,
        VERSION,
		TOPIC,
		MSG_ID,
		TAGS,
		`KEYS`,
		MESSAGE,
		MERCHANT_NO,
		NOTIFY_TYPE,
		TRX_NO,
		CREATE_TIME,
		CREATE_DATE
	</sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.notify.entity.RmqMessageRecord">
        INSERT INTO <include refid="table" /> (
        VERSION,
        CREATE_TIME,
        TOPIC,
        MSG_ID,
        TAGS,
        `KEYS`,
        MESSAGE,
        MERCHANT_NO,
        NOTIFY_TYPE,
        TRX_NO,
        CREATE_DATE
        ) VALUES (
        1,
        now(),
        #{topic,jdbcType=VARCHAR},
        #{msgId,jdbcType=VARCHAR},
        #{tags,jdbcType=VARCHAR},
        #{keys,jdbcType=VARCHAR},
        #{message,jdbcType=VARCHAR},
        #{merchantNo,jdbcType=VARCHAR},
        #{notifyType,jdbcType=SMALLINT},
        #{trxNo,jdbcType=VARCHAR},
        #{createDate,jdbcType=DATE}
        )
    </insert>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.notify.entity.RmqMessageRecord">
        UPDATE <include refid="table" />
        <set>
            <if test="topic != null and topic !='' ">
                TOPIC = #{topic,jdbcType=VARCHAR},
            </if>
            <if test="msgId != null and msgId !='' ">
                MSG_ID = #{msgId,jdbcType=VARCHAR},
            </if>
            <if test="tags != null and tags !='' ">
                TAGS = #{tags,jdbcType=VARCHAR},
            </if>
            <if test="keys != null and keys !='' ">
                `KEYS` = #{keys,jdbcType=VARCHAR},
            </if>
            <if test="message != null and message !='' ">
                MESSAGE = #{message,jdbcType=VARCHAR},
            </if>
            VERSION = #{version,jdbcType=INTEGER} + 1
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO <include refid="table" /> (
        VERSION,
        CREATE_TIME,
        TOPIC,
        MSG_ID,
        TAGS,
        `KEYS`,
        MESSAGE,
        MERCHANT_NO,
        NOTIFY_TYPE,
        TRX_NO,
        CREATE_DATE
        ) VALUES
        <foreach collection="list" item="item" open="(" separator="," close=")">
            1,
            #{now(),jdbcType=TIMESTAMP},
            #{topic,jdbcType=VARCHAR},
            #{msgId,jdbcType=VARCHAR},
            #{tags,jdbcType=VARCHAR},
            #{keys,jdbcType=VARCHAR},
            #{message,jdbcType=VARCHAR},
            #{merchantNo,jdbcType=VARCHAR},
            #{notifyType,jdbcType=SMALLINT},
            #{trxNo,jdbcType=VARCHAR},
            #{createDate,jdbcType=DATE}
        </foreach>
    </insert>

    <!-- 多条件组合查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT count(id) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>
    
    <select id="getByMerchantNoTrxNoType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        limit 1
    </select>
    
    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->
    <sql id="condition_sql">
        <if test="merchantNo != null and merchantNo !=''">
            AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="trxNo != null and trxNo !=''">
            AND TRX_NO = #{trxNo,jdbcType=VARCHAR}
        </if>
        <if test="notifyType != null">
            AND NOTIFY_TYPE = #{notifyType,jdbcType=SMALLINT}
        </if>
        <if test="topic != null and topic !=''">
            AND TOPIC = #{topic,jdbcType=VARCHAR}
        </if>
        <if test="tags != null and tags !='' ">
            AND TAGS = #{tags,jdbcType=VARCHAR}
        </if>
        <if test="keys != null and keys != '' ">
            AND `KEYS` = #{keys,jdbcType=VARCHAR}
        </if>
        <if test="idList != null and idList.size() > 0">
            AND ID IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="beginDate != null and endDate != null" >
            and CREATE_TIME between #{beginDate,jdbcType=TIMESTAMP} and #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="searchDate != null" >
            and CREATE_TIME = #{searchDate}
        </if>
    </sql>

</mapper>

