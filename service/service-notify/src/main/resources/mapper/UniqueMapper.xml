<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.notify.entity.Unique">

	<sql id="table"> tbl_notify_unique </sql>

	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.notify.entity.Unique">
		<result property="uniqueKey" column="UNIQUE_KEY" />
	</resultMap>

	<insert id="insert" parameterType="com.zhixianghui.facade.notify.entity.Unique" keyProperty="id" useGeneratedKeys="true">
		insert into
		<include refid="table" />
		(UNIQUE_KEY)
		values (md5(#{uniqueKey}))
	</insert>

	<insert id="batchInsert" parameterType="com.zhixianghui.facade.notify.entity.Unique">
		insert into
		<include refid="table" />
		(UNIQUE_KEY)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(md5(#{item.uniqueKey}))
		</foreach>
	</insert>

</mapper>