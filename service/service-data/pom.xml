<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-service</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>service-data</artifactId>
    <description>统计/报表服务</description>

    <build>
        <finalName>${appName}</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- redis -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.16.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>redisson-spring-data-25</artifactId>
                    <groupId>org.redisson</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-21</artifactId>
            <version>3.16.3</version>
        </dependency>
        <!-- redis -->
        <!-- RocketMQ -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>
        <!-- RocketMQ -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency> <!-- 把nacos作为配置中心时需要引入的依赖 -->
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.housepower</groupId>
            <artifactId>clickhouse-native-jdbc</artifactId>
            <version>2.6.5</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.5.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.18</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-util</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-service</artifactId>

            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>component-starter</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-data</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-common</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-merchant</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-trade</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-fee</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-risk-control</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-banklink</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-notify</artifactId>

        </dependency>
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-flow</artifactId>

        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-account-invoice</artifactId>

        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.2</version>
        </dependency>
    </dependencies>
</project>
