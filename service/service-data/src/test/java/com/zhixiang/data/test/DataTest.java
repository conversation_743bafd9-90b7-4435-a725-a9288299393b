package com.zhixiang.data.test;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.service.data.ServiceDataApp;
import com.zhixianghui.service.data.services.CkOrdersService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServiceDataApp.class)
public class DataTest {

    @Autowired
    private CkOrdersService ckOrdersService;

    @Test
    public void testQueryFromCk() {

        final List<Map<String, Object>> mapList = ckOrdersService.listSalerOrdersGroupedByMchDaily(new HashMap<>());
        System.out.println(JSON.toJSONString(mapList));

    }

}
