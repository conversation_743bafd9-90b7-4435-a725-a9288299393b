package com.zhixianghui.service.data.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.data.entity.CkOrders;
import com.zhixianghui.facade.data.vo.MainIndexExportDataVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface CkOrdersMapper extends BaseMapper<CkOrders> {
    int updateBatch(List<CkOrders> list);

    int updateBatchSelective(List<CkOrders> list);

    int batchInsert(@Param("list") List<CkOrders> list);

    int insertOrUpdate(CkOrders record);

    int insertOrUpdateSelective(CkOrders record);

    List<Map<String, Object>> listSalerOrdersGroupedByMch(Map<String, Object> params);

    IPage<Map<String, Object>> salerstatistics(Page<Map<String, Object>> page,@Param("param") Map<String, Object> params);

    Map<String, Object> coreIndexStatistics(Map<String, Object> params);

    List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> params);

    List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> params);

    List<Map<String, Object>> listSalerOrdersGroupedByMchDaily(Map<String, Object> params);

    List<Map<String, Object>> listSalerOrdersGroupedByMchMonthly(Map<String, Object> params);

    Map<String, Object> salerOrdersStatistics(Map<String, Object> params);

    Map<String, Object> countOrderAmount(Map<String, Object> paramMap);

    Map<String, Object> overview(Map<String, Object> paramMap);

    List<Map<String, Object>> overviewDetailDaily(Map<String, Object> paramMap);

    IPage<Map<String, Object>> vendorStatistics(Page<Map<String, Object>> page, @Param("param") Map<String, Object> params);

    List<MainIndexExportDataVo> mapAmountWithMch(Map<String, Object> paramMap);
}
