package com.zhixianghui.service.data.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.data.services.CkVendorFeeOrderService;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName VendorFeeSyncListener
 * @Description TODO
 * @Date 2023/3/16 10:26
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_SYNC,selectorExpression = MessageMsgDest.TAG_VENDOR_FEE_SYNC,consumeThreadMax = 10,consumerGroup = "vendorSyncConsumer")
public class VendorFeeSyncListener extends BaseRocketMQListener<String> {

    @Autowired
    private CkVendorFeeOrderService ckVendorFeeOrderService;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        ckVendorFeeOrderService.syncOrder(jsonParam);
    }
}
