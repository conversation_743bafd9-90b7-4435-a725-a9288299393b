package com.zhixianghui.service.data.mappers;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.data.entity.CkVendorFeeOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Mapper
public interface CkVendorFeeOrderMapper extends BaseMapper<CkVendorFeeOrder> {

    IPage<Map<String, Object>> vendorStatistics(Page<Map<String, Object>> page, @Param("param") Map<String, Object> paramMap);
}
