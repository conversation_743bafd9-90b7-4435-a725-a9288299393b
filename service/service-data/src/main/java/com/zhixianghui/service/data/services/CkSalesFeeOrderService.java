package com.zhixianghui.service.data.services;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.data.entity.CkSalesFeeOrder;
import com.zhixianghui.service.data.mappers.CkSalesFeeOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-03-15
*/
@Slf4j
@Service
@DS("clickhouse")
public class CkSalesFeeOrderService extends ServiceImpl<CkSalesFeeOrderMapper, CkSalesFeeOrder> {

    @DS("mysql")
    public void syncOrder(String jsonParam) {
        CkSalesFeeOrder ckSalesFeeOrder = JsonUtil.toBean(jsonParam,CkSalesFeeOrder.class);
        try {
            this.save(ckSalesFeeOrder);
        }catch (DuplicateKeyException e){
            //重复插入数据，跳过
        }
    }
}