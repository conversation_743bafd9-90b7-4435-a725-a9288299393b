package com.zhixianghui.service.data.mappers;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.data.entity.CkAgentFeeOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 合伙人计费订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Mapper
public interface CkAgentFeeOrderMapper extends BaseMapper<CkAgentFeeOrder> {

    IPage<Map<String, Object>> agentStatistics(Page<Map<String, Object>> page, @Param("param") Map<String, Object> paramMap);
}
