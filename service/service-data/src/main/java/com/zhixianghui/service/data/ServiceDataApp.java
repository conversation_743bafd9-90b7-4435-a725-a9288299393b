package com.zhixianghui.service.data;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * 报表等文件导出服务
 * <AUTHOR>
 * @date 2020/8/4
 **/
@SpringBootApplication
@EnableAsync
@EnableCaching
@MapperScan("com.zhixianghui.service.data.mappers")
public class ServiceDataApp {
    public static void main(String[] args) {
        new SpringApplicationBuilder(ServiceDataApp.class).web(WebApplicationType.NONE).run(args);
    }
}
