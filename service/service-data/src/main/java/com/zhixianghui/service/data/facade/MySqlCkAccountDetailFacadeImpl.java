package com.zhixianghui.service.data.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.facade.data.service.MysqlCkAccountDetailFacade;
import com.zhixianghui.service.data.services.MysqlCkAccountDetailService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Service(timeout = 60000)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MySqlCkAccountDetailFacadeImpl implements MysqlCkAccountDetailFacade {

    private final MysqlCkAccountDetailService service;

    @Override
    public Page<CkAccountDetail> listBy(Map<String, Object> param,Page page) throws BizException {
        return null;
    }

    @Override
    public void saveOne(CkAccountDetail detail) throws BizException{
        service.save(detail);
    }

    @Override
    public CkAccountDetail getOne(String trxNo, Integer altSource) throws BizException{
        return service.getOne(trxNo, altSource);
    }
}
