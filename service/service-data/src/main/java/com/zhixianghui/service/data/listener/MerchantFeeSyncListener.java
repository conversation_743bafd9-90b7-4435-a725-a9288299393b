package com.zhixianghui.service.data.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.data.entity.CkMerchantFeeOrder;
import com.zhixianghui.service.data.services.CkMerchantFeeOrderService;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName MerchantFeeSyncListener
 * @Description TODO
 * @Date 2023/3/15 17:32
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_SYNC,selectorExpression = MessageMsgDest.TAG_MERCHANT_FEE_SYNC,consumeThreadMax = 10,consumerGroup = "merchantFeeSyncConsumer")
public class MerchantFeeSyncListener extends BaseRocketMQListener<String> {


    @Autowired
    private CkMerchantFeeOrderService ckMerchantFeeOrderService;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        ckMerchantFeeOrderService.syncOrder(jsonParam);
    }
}
