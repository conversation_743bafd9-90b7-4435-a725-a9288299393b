package com.zhixianghui.service.data.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.data.service.CkVendorFeeOrderFacade;
import com.zhixianghui.service.data.services.CkVendorFeeOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-03-16
 */
@Service(timeout = 60000)
public class CkVendorFeeOrderImpl implements CkVendorFeeOrderFacade {

    @Autowired
    private CkVendorFeeOrderService ckVendorFeeOrderService;

    @Override
    public IPage<Map<String, Object>> vendorStatistics(Map<String, Object> paramMap, Page<Map<String, Object>> page) {
        final IPage<Map<String, Object>> vendorStatisticsList = ckVendorFeeOrderService.vendorStatistics(page, paramMap);
        return vendorStatisticsList;
    }
}
