package com.zhixianghui.service.data.listener;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.data.entity.CkOrders;
import com.zhixianghui.service.data.services.CkOrdersService;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_REEXCHANGE,
        selectorExpression = MessageMsgDest.TAG_REEXCHANGE,consumeThreadMax = 1,
        consumerGroup = "reexchangeDataDelConsume")
public class ReexDelListener extends BaseRocketMQListener<String> {

    @Autowired
    private CkOrdersService ckOrdersService;

    @Override
    public void validateJsonParam(String msg) {
        if(StringUtils.isEmpty(msg)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("计费订单删除异常");
        }
    }

    @Override
    public void consumeMessage(String platTrxNo) {
        ckOrdersService.remove(new QueryWrapper<CkOrders>().eq(CkOrders.COL_PLAT_TRX_NO, platTrxNo));
    }
}
