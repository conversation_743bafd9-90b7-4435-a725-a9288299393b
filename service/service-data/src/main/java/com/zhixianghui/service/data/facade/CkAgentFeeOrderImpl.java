package com.zhixianghui.service.data.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.data.service.CkAgentFeeOrderFacade;
import com.zhixianghui.service.data.services.CkAgentFeeOrderService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * 合伙人计费订单表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-03-16
 */
@Service(timeout = 60000)
public class CkAgentFeeOrderImpl implements CkAgentFeeOrderFacade {

    @Autowired
    private CkAgentFeeOrderService agentFeeOrderService;

    @Override
    public IPage<Map<String, Object>> agentStatistics(Map<String, Object> paramMap, Page<Map<String, Object>> page) {
        final IPage<Map<String, Object>> agentStatistics = agentFeeOrderService.agentStatistics(page, paramMap);
        return agentStatistics;
    }
}
