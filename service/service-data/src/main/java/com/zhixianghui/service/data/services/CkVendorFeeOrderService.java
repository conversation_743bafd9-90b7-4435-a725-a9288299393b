package com.zhixianghui.service.data.services;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.data.entity.CkVendorFeeOrder;
import com.zhixianghui.service.data.mappers.CkVendorFeeOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2023-03-16
*/
@Slf4j
@Service
@DS("clickhouse")
public class CkVendorFeeOrderService extends ServiceImpl<CkVendorFeeOrderMapper, CkVendorFeeOrder> {

    @DS("mysql")
    public void syncOrder(String jsonParam) {
        CkVendorFeeOrder ckVendorFeeOrder = JsonUtil.toBean(jsonParam,CkVendorFeeOrder.class);
        try {
            this.save(ckVendorFeeOrder);
        }catch (DuplicateKeyException e){
            //重复插入数据，跳过
        }
    }

    public IPage<Map<String, Object>> vendorStatistics(Page<Map<String, Object>> page, Map<String, Object> paramMap) {
        return baseMapper.vendorStatistics(page,paramMap);
    }
}