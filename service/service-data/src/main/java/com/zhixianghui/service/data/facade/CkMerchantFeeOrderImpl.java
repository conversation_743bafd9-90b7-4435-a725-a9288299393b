package com.zhixianghui.service.data.facade;

import com.zhixianghui.facade.data.service.CkMerchantFeeOrderFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2023-03-15
 */
@Service(timeout = 60000)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CkMerchantFeeOrderImpl implements CkMerchantFeeOrderFacade {

}
