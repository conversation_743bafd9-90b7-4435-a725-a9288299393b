package com.zhixianghui.service.data.facade;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.data.service.CkOrderFacade;
import com.zhixianghui.facade.data.vo.MainIndexExportDataVo;
import com.zhixianghui.facade.data.vo.MonthlyOverviewVo;
import com.zhixianghui.service.data.services.CkOrdersService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service(timeout = 60000)
@DS("mysql")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CkOrderFacadeImpl implements CkOrderFacade {

    public final CkOrdersService ckOrdersService;

    @Override
    public List<Map<String, Object>> listSalerOrdersGroupedByMch(Map<String, Object> params) {
        return ckOrdersService.listSalerOrdersGroupedByMch(params);
    }

    @Override
    public IPage<Map<String, Object>> salerstatistics(Map<String, Object> params, Page<Map<String,Object>> pageParam) {
        final IPage<Map<String, Object>> salerstatistics = ckOrdersService.salerstatistics(pageParam, params);
        return salerstatistics;
    }

    @Override
    public Map<String, Object> coreIndexStatistics(Map<String, Object> params) {
        return ckOrdersService.coreIndexStatistics(params);
    }

    @Override
    public List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> params) {
        return ckOrdersService.coreIndexDailyDetail(params);
    }

    @Override
    public List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> params) {
        return ckOrdersService.coreIndexDetailMonthly(params);
    }

    @Override
    public List<Map<String, Object>> listSalerOrdersGroupedByMchDaily(Map<String, Object> params){
        return ckOrdersService.listSalerOrdersGroupedByMchDaily(params);
    }

    @Override
    public List<Map<String, Object>> listSalerOrdersGroupedByMchMonthly(Map<String, Object> params) {
        return ckOrdersService.listSalerOrdersGroupedByMchMonthly(params);
    }

    @Override
    public Map<String, Object> salerOrdersStatistics(Map<String, Object> params) {
        return ckOrdersService.salerOrdersStatistics(params);
    }

    @Override
    public Map<String, Object> countOrderAmont(Map<String, Object> paramMap) {
        return ckOrdersService.countOrderAmount(paramMap);
    }

    @Override
    public MonthlyOverviewVo getMonthlyOverview(Map<String, Object> param) {
        final Date currentDate = new Date();
        final Date lastDate = DateUtil.addDay(currentDate, -1);
        final Date lastDayEnd = DateUtil.getDayEnd(lastDate);
        final Date beginDayStart = DateUtil.getDayStart(DateUtil.getFirstOfMonth(lastDate));

        final Date beginDayLastMonthStart = DateUtil.getDayStart(DateUtil.getFirstDayOfLastMonth(lastDate));
        final Date lastMonthEnd = DateUtil.addMonth(lastDayEnd, -1);

        param.put("completeBeginDate", beginDayStart);
        param.put("completeEndDate", lastDayEnd);
        final Map<String, Object> overviewThisMonth = ckOrdersService.overview(param);
        final List<Map<String, Object>> detailThisMonth = ckOrdersService.overviewDetailDaily(param);

        param.put("completeBeginDate", beginDayLastMonthStart);
        param.put("completeEndDate", lastMonthEnd);
        final Map<String, Object> overviewLastMonth = ckOrdersService.overview(param);
        final List<Map<String, Object>> detailLastMonth = ckOrdersService.overviewDetailDaily(param);

        MonthlyOverviewVo vo = new MonthlyOverviewVo();


        vo.setOrder(
                new MonthlyOverviewVo.Item()
                        .setLastNum(overviewLastMonth.get("cnt")==null?0:Integer.parseInt(String.valueOf(overviewLastMonth.get("cnt"))))
                        .setNum(overviewThisMonth.get("cnt")==null?0:Integer.parseInt(String.valueOf(overviewThisMonth.get("cnt"))))
                        .setSubtractNum(
                                (overviewThisMonth.get("cnt")==null?0:Integer.parseInt(String.valueOf(overviewThisMonth.get("cnt")))) - (overviewLastMonth.get("cnt")==null?0:Integer.parseInt(String.valueOf(overviewLastMonth.get("cnt"))))
                        )
        );
        vo.setMoney(
                new MonthlyOverviewVo.Money()
                        .setMoney(overviewThisMonth.get("sumAmt")==null? BigDecimal.ZERO:new BigDecimal(String.valueOf(overviewThisMonth.get("sumAmt"))))
                        .setLastMoney(overviewLastMonth.get("sumAmt")==null? BigDecimal.ZERO:new BigDecimal(String.valueOf(overviewLastMonth.get("sumAmt"))))
                        .setSubtractMoney(
                                (overviewThisMonth.get("sumAmt")==null? BigDecimal.ZERO:new BigDecimal(String.valueOf(overviewThisMonth.get("sumAmt")))).subtract(overviewLastMonth.get("sumAmt")==null? BigDecimal.ZERO:new BigDecimal(String.valueOf(overviewLastMonth.get("sumAmt"))))
                        )
        );
        vo.setMerchant(
                new MonthlyOverviewVo.Item()
                        .setLastNum(overviewLastMonth.get("emCnt")==null?0:Integer.parseInt(String.valueOf(overviewLastMonth.get("emCnt"))))
                        .setNum(overviewThisMonth.get("emCnt")==null?0:Integer.parseInt(String.valueOf(overviewThisMonth.get("emCnt"))))
                        .setSubtractNum(
                                (overviewThisMonth.get("emCnt")==null?0:Integer.parseInt(String.valueOf(overviewThisMonth.get("emCnt")))) - (overviewLastMonth.get("emCnt")==null?0:Integer.parseInt(String.valueOf(overviewLastMonth.get("emCnt"))))
                        )
        );
        vo.setUser(
                new MonthlyOverviewVo.Item()
                        .setLastNum(overviewLastMonth.get("idCnt")==null?0:Integer.parseInt(String.valueOf(overviewLastMonth.get("idCnt"))))
                        .setNum(overviewThisMonth.get("idCnt")==null?0:Integer.parseInt(String.valueOf(overviewThisMonth.get("idCnt"))))
                        .setSubtractNum(
                                (overviewThisMonth.get("idCnt")==null?0:Integer.parseInt(String.valueOf(overviewThisMonth.get("idCnt")))) - (overviewLastMonth.get("idCnt")==null?0:Integer.parseInt(String.valueOf(overviewLastMonth.get("idCnt"))))
                        )
        );

        vo.setDateRange(getDateRange(beginDayStart,lastDayEnd));

        Map<String, BigDecimal> thisMonthMap = new LinkedHashMap<>();
        Map<String, BigDecimal> lastMonthMap = new LinkedHashMap<>();
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        List<Date> betweenDates = DateUtil.getBetweenDates(beginDayStart, lastDayEnd);
        betweenDates.remove(betweenDates.size()-1);
        List<String> dates = DateUtil.changeStringList(betweenDates);
        List<Date> lastBetweenDates = DateUtil.getBetweenDates(beginDayLastMonthStart, lastMonthEnd);
        lastBetweenDates.remove(lastBetweenDates.size()-1);
        List<String> lastDates = DateUtil.changeStringList(lastBetweenDates);
        dates.forEach(day->{
            thisMonthMap.put(day, BigDecimal.ZERO);
        });

        lastDates.forEach(day->{
            lastMonthMap.put(day, BigDecimal.ZERO);
        });

        for (Map<String, Object> map : detailThisMonth) {
            String key =simpleDateFormat.format(new Date(((java.sql.Date) map.get("date")).getTime()));
            BigDecimal value = (BigDecimal) map.get("sumAmt");
            thisMonthMap.put(key, value);
        }

        for (Map<String, Object> map : detailLastMonth) {
            String key =simpleDateFormat.format(new Date(((java.sql.Date) map.get("date")).getTime()));
            BigDecimal value = (BigDecimal) map.get("sumAmt");
            lastMonthMap.put(key, value);
        }

        vo.setMoneys(new ArrayList<>(thisMonthMap.values()));
        vo.setLastMoneys(new ArrayList<>(lastMonthMap.values()));

        List<Integer> xAxis = betweenDates.stream().map(e -> {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(e);
            return calendar.get(Calendar.DAY_OF_MONTH);
        }).collect(Collectors.toList());
        vo.setXAxis(xAxis);
        return vo;
    }

    @Override
    public Map<String, MainIndexExportDataVo> mapAmountWithMch(Map<String, Object> paramMap) {
        return ckOrdersService.mapAmountWithMch(paramMap);
    }

    public String getDateRange(Date start,Date end){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy年MM月dd日");
        String startTime=sdf.format(start);
        String endTime=sdf.format(end);
        return startTime + "-" + endTime;
    };

    public static void main(String[] args) {
        try {
            final Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-03-27 23:59:59");
            final Date lastMonth = DateUtil.addMonth(date,-1);
            System.out.println(lastMonth);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
