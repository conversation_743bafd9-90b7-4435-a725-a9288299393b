package com.zhixianghui.service.data.services;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.data.entity.CkMerchantFeeOrder;
import com.zhixianghui.service.data.mappers.CkMerchantFeeOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
* serivice
*
* <AUTHOR>
* @version 创建时间： 2023-03-15
*/
@Slf4j
@Service
@DS("clickhouse")
public class CkMerchantFeeOrderService extends ServiceImpl<CkMerchantFeeOrderMapper, CkMerchantFeeOrder> {

    @DS("mysql")
    public void syncOrder(String jsonParam) {
        CkMerchantFeeOrder ckMerchantFeeOrder = JsonUtil.toBean(json<PERSON>ara<PERSON>,CkMerchantFeeOrder.class);
        try {
            this.save(ckMerchantFeeOrder);
        }catch (DuplicateKeyException e){
            //重复插入数据，跳过
        }
    }
}