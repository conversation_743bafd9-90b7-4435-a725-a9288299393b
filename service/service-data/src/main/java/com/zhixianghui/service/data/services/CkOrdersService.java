package com.zhixianghui.service.data.services;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.data.entity.CkOrders;
import com.zhixianghui.facade.data.vo.MainIndexExportDataVo;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.service.data.mappers.CkOrdersMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@DS("mysql")
public class CkOrdersService extends ServiceImpl<CkOrdersMapper, CkOrders> {


    public int updateBatch(List<CkOrders> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<CkOrders> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<CkOrders> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(CkOrders record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(CkOrders record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
    public List<Map<String, Object>> listSalerOrdersGroupedByMch(Map<String, Object> params) {
        return baseMapper.listSalerOrdersGroupedByMch(params);
    }

    public IPage<Map<String, Object>> salerstatistics(Page<Map<String, Object>> page, Map<String, Object> params) {
        return baseMapper.salerstatistics(page, params);
    }

    public Map<String, Object> coreIndexStatistics(Map<String, Object> params) {
        return baseMapper.coreIndexStatistics(params);
    }

    public List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> params) {
        return baseMapper.coreIndexDailyDetail(params);
    }

    public List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> params) {
        return baseMapper.coreIndexDetailMonthly(params);
    }

    public List<Map<String, Object>> listSalerOrdersGroupedByMchDaily(Map<String, Object> params){
        return baseMapper.listSalerOrdersGroupedByMchDaily(params);
    }

    public List<Map<String, Object>> listSalerOrdersGroupedByMchMonthly(Map<String, Object> params){
        return baseMapper.listSalerOrdersGroupedByMchMonthly(params);
    }

    public Map<String, Object> salerOrdersStatistics(Map<String, Object> params) {
        return baseMapper.salerOrdersStatistics(params);
    }

    public Map<String, Object> countOrderAmount(Map<String, Object> paramMap) {
        return baseMapper.countOrderAmount(paramMap);
    }

    public Map<String, Object> overview(Map<String, Object> paramMap){
        return baseMapper.overview(paramMap);
    }

    public List<Map<String, Object>> overviewDetailDaily(Map<String, Object> paramMap) {
        return baseMapper.overviewDetailDaily(paramMap);
    }

    public Map<String, MainIndexExportDataVo> mapAmountWithMch(Map<String, Object> paramMap) {
        Date completeBeginDate = (Date) paramMap.get("completeBeginDate");
        Date completeEndDate = (Date) paramMap.get("completeEndDate");
        if(completeBeginDate!= null && completeEndDate!=null){
            paramMap.put("beginDate", TrxNoDateUtil.compareMinWithEndDate(DateUtil.addMonth(completeBeginDate, -3)));
            paramMap.put("endDate",completeEndDate);
        }
        List<MainIndexExportDataVo> list = baseMapper.mapAmountWithMch(paramMap);
        return list.stream().collect(Collectors.toMap(obj->obj.getEmployerNo() + "-" + obj.getMainstayNo(),obj->obj));
    }
}
