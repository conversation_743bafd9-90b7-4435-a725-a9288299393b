package com.zhixianghui.service.data.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.data.services.CkMerchantFeeOrderService;
import com.zhixianghui.service.data.services.CkSalesFeeOrderService;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName
 * @Description TODO
 * @Date 2023/3/15 17:32
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_SYNC,selectorExpression = MessageMsgDest.TAG_SALER_FEE_SYNC,consumeThreadMax = 10,consumerGroup = "salerSyncConsumer")
public class SalerFeeSyncListener extends BaseRocketMQListener<String> {


    @Autowired
    private CkSalesFeeOrderService ckSalesFeeOrderService;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        ckSalesFeeOrderService.syncOrder(jsonParam);
    }
}
