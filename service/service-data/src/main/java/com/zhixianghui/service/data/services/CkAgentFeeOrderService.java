package com.zhixianghui.service.data.services;

import cn.hutool.core.lang.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.service.data.mappers.CkAgentFeeOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.data.entity.CkAgentFeeOrder;

import java.util.List;
import java.util.Map;

/**
* 合伙人计费订单表Biz
*
* <AUTHOR>
* @version 创建时间： 2023-03-16
*/
@Slf4j
@Service
@DS("clickhouse")
public class CkAgentFeeOrderService extends ServiceImpl<CkAgentFeeOrderMapper,CkAgentFeeOrder> {

    @DS("mysql")
    public void syncSave(String jsonParam) {
        List<CkAgentFeeOrder> list = JsonUtil.toBean(jsonParam,new TypeReference<List<CkAgentFeeOrder>>(){});
        try {
            saveBatch(list);
        }catch (DuplicateKeyException e){
            //重复插入，跳过
        }
    }

    public IPage<Map<String, Object>> agentStatistics(Page<Map<String, Object>> page, Map<String, Object> paramMap) {
        return baseMapper.agentStatistics(page,paramMap);
    }
}