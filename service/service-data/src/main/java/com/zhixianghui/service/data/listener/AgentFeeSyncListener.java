package com.zhixianghui.service.data.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.data.services.CkAgentFeeOrderService;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName AgentFeeSyncListener
 * @Description TODO
 * @Date 2023/3/16 16:03
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_SYNC,selectorExpression = MessageMsgDest.TAG_AGENT_FEE_SYNC,consumeThreadMax = 10,consumerGroup = "agentSyncConsumer")
public class AgentFeeSyncListener extends BaseRocketMQListener<String> {

    @Autowired
    private CkAgentFeeOrderService ckAgentFeeOrderService;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        ckAgentFeeOrderService.syncSave(jsonParam);
    }
}
