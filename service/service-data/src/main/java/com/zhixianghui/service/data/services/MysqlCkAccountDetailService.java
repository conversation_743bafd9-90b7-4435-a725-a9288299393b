package com.zhixianghui.service.data.services;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.service.data.mappers.CkAccountDetailMapper;
import org.springframework.stereotype.Service;

@Service
@DS("mysql")
public class MysqlCkAccountDetailService extends ServiceImpl<CkAccountDetailMapper, CkAccountDetail> {
    public CkAccountDetail getOne(String trxNo, Integer altSource) {
        QueryWrapper<CkAccountDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("trx_no", trxNo).eq("alt_source", altSource);
        return this.getOne(queryWrapper);
    }
}
