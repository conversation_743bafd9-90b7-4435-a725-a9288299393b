package com.zhixianghui.service.data.services;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.service.utils.MybatisUtil;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.service.data.mappers.CkAccountDetailMapper;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class CkAccountDetailService extends ServiceImpl<CkAccountDetailMapper, CkAccountDetail> {

    public Page<CkAccountDetail> listBy(Page page,Map<String, Object> param) {
        QueryWrapper<CkAccountDetail> queryWrapper = new QueryWrapper();
        MybatisUtil.buildWhereLowercase(queryWrapper, CkAccountDetail.class, param);
        CkAccountDetail.extendWhere(queryWrapper, param);
        Page selectPage = this.getBaseMapper().selectPage(page, queryWrapper);
        return selectPage;
    }

    public CkAccountDetail getOne(String trxNo, Integer altSource) {
        QueryWrapper<CkAccountDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("trx_no", trxNo).eq("alt_source", altSource);
        return this.getOne(queryWrapper);
    }
}

