<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.data.mappers.CkVendorFeeOrderMapper">
    <sql id="table">ck_vendor_fee_order</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.data.entity.CkVendorFeeOrder">
        <id column="plat_trx_no" property="platTrxNo" jdbcType="VARCHAR"/>
        <id column="trade_time" property="tradeTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
        <result column="vendor_no" property="vendorNo" jdbcType="VARCHAR"/>
        <result column="vendor_name" property="vendorName" jdbcType="VARCHAR"/>
        <result column="product_no" property="productNo" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="mch_order_no" property="mchOrderNo" jdbcType="VARCHAR"/>
        <result column="channel_order_no" property="channelOrderNo" jdbcType="VARCHAR"/>
        <result column="channel_trx_no" property="channelTrxNo" jdbcType="VARCHAR"/>
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="vendor_fee" property="vendorFee" jdbcType="DECIMAL"/>
        <result column="calculate_formula" property="calculateFormula" jdbcType="VARCHAR"/>
        <result column="merchant_type" property="merchantType" jdbcType="SMALLINT"/>
        <result column="order_type" property="orderType" jdbcType="SMALLINT"/>
        <result column="saler_id" property="salerId" jdbcType="BIGINT"/>
        <result column="saler_name" property="salerName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="ext_json" property="extJson" jdbcType="VARCHAR"/>
        <result column="exist_agent" property="existAgent" jdbcType="SMALLINT"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, trade_time, mch_no, mch_name, vendor_no, vendor_name, product_no, product_name, mch_order_no, plat_trx_no, channel_order_no, channel_trx_no, order_amount, vendor_fee, calculate_formula, merchant_type, order_type, saler_id, saler_name, remark, ext_json, exist_agent
    </sql>

    <select id="vendorStatistics" parameterType="java.util.Map" resultType="java.util.Map">
        select
        co.mch_no mchNo,
        co.mch_name mchName,
        co.vendor_no vendorNo ,
        co.vendor_name vendorName,
        co.product_no productNo,
        co.product_name productName,
        sum(co.order_amount) totalNetAmount,
        sum(co.vendor_fee) totalVendorFee,
        sum(mo.order_fee) orderFee
        from
        ck_vendor_fee_order co LEFT JOIN ck_merchant_fee_order mo
        on co.plat_trx_no = mo.plat_trx_no
        <where>
            <if test="param.mchNo != null and param.mchNo != ''">
                and co.mch_no = #{param.mchNo}
            </if>
            <if test="param.mchNos !=null">
                and co.mch_no in
                <foreach collection="param.mchNos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.mchNameLike !=null  and param.mchNameLike !=''">
                and co.mch_name like CONCAT(CONCAT('%', #{param.mchNameLike,jdbcType=VARCHAR}), '%')
            </if>
            <if test="param.vendorNameLike !=null  and param.vendorNameLike !=''">
                and co.vendor_name like CONCAT(CONCAT('%', #{param.vendorNameLike,jdbcType=VARCHAR}), '%')
            </if>
            <if test="param.vendorNo !=null  and param.vendorNo !=''">
                and co.vendor_no = #{param.vendorNo,jdbcType=VARCHAR}
            </if>
            <if test="param.productNo !=null  and param.productNo !=''">
                and co.product_no  = #{param.productNo,jdbcType=VARCHAR}
            </if>
            <if test="param.tradeTimeBegin != null">
                and co.trade_time <![CDATA[>=]]>  #{param.tradeTimeBegin,jdbcType=TIMESTAMP}
                and mo.trade_time <![CDATA[>=]]>  #{param.tradeTimeBegin,jdbcType=TIMESTAMP}
            </if>
            <if test="param.tradeTimeEnd != null">
                and co.trade_time <![CDATA[ <= ]]> #{param.tradeTimeEnd,jdbcType=TIMESTAMP}
                and mo.trade_time <![CDATA[ <= ]]> #{param.tradeTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="param.platTrxNo != null and param.platTrxNo != ''">
                and co.plat_trx_no = #{param.platTrxNo}
                and mo.plat_trx_no = #{param.platTrxNo}
            </if>
        </where>
        group by
        co.mch_no,
        co.mch_name,
        co.vendor_no,
        co.vendor_name,
        co.product_no,
        co.product_name
        <choose>
            <when test="param.sortColumns != null and param.sortColumns !='' ">
                <![CDATA[ ORDER BY ${param.sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY totalNetAmount DESC ]]>
            </otherwise>
        </choose>
    </select>

</mapper>
