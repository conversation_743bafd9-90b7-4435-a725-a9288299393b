<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.data.mappers.CkAgentFeeOrderMapper">
    <sql id="table">ck_agent_fee_order</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.data.entity.CkAgentFeeOrder">
        <id column="plat_trx_no" property="platTrxNo" jdbcType="VARCHAR"/>
        <id column="reward_type" property="rewardType" jdbcType="SMALLINT"/>
        <id column="trade_time" property="tradeTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
        <result column="vendor_no" property="vendorNo" jdbcType="VARCHAR"/>
        <result column="vendor_name" property="vendorName" jdbcType="VARCHAR"/>
        <result column="agent_no" property="agentNo" jdbcType="VARCHAR"/>
        <result column="agent_name" property="agentName" jdbcType="VARCHAR"/>
        <result column="product_no" property="productNo" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="mch_order_no" property="mchOrderNo" jdbcType="VARCHAR"/>
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="order_fee" property="orderFee" jdbcType="DECIMAL"/>
        <result column="agent_cost" property="agentCost" jdbcType="DECIMAL"/>
        <result column="agent_profit" property="agentProfit" jdbcType="DECIMAL"/>
        <result column="calculate_formula" property="calculateFormula" jdbcType="VARCHAR"/>
        <result column="merchant_type" property="merchantType" jdbcType="SMALLINT"/>
        <result column="order_type" property="orderType" jdbcType="SMALLINT"/>
        <result column="saler_id" property="salerId" jdbcType="BIGINT"/>
        <result column="saler_name" property="salerName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="ext_json" property="extJson" jdbcType="VARCHAR"/>
        <result column="agent_type" property="agentType" jdbcType="SMALLINT"/>
        <result column="inviter_no" property="inviterNo" jdbcType="VARCHAR"/>
        <result column="inviter_name" property="inviterName" jdbcType="VARCHAR"/>
        <result column="agent_fee_rate" property="agentFeeRate" jdbcType="DECIMAL"/>
        <result column="real_profit_ratio" property="realProfitRatio" jdbcType="INTEGER"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, trade_time, mch_no, mch_name, vendor_no, vendor_name, agent_no, agent_name, reward_type, product_no, product_name, mch_order_no, plat_trx_no, order_amount, order_fee, agent_cost, agent_profit, calculate_formula, merchant_type, order_type, saler_id, saler_name, remark, ext_json, agent_type, inviter_no, inviter_name, agent_fee_rate, real_profit_ratio
    </sql>

    <select id="agentStatistics" parameterType="java.util.Map" resultType="java.util.Map">
        select
        mch_no mchNo,
        mch_name mchName,
        vendor_no vendorNo ,
        vendor_name vendorName,
        product_no productNo,
        product_name productName,
        agent_no agentNo,
        agent_name agentName,
        reward_type rewardType,
        real_profit_ratio realProfitRatio,
        agent_fee_rate agentFeeRate,
        sum(order_amount) totalNetAmount,
        sum(order_fee) totalOrderFee,
        sum(agent_cost) agentCost,
        sum(agent_profit) agentProfit
        from
        ck_agent_fee_order co
        <where>
            <if test="param.mchNo != null and param.mchNo != ''">
                and mch_no = #{param.mchNo}
            </if>
            <if test="param.mchNameLike !=null  and param.mchNameLike !=''">
                and mch_name like CONCAT(CONCAT('%', #{param.mchNameLike,jdbcType=VARCHAR}), '%')
            </if>
            <if test="param.agentNo !=null  and param.agentNo !=''">
                and agent_no = #{param.agentNo,jdbcType=VARCHAR}
            </if>
            <if test="param.agentNameLike != null and param.agentNameLike != ''">
                and agent_name like CONCAT(CONCAT('%', #{param.agentNameLike,jdbcType=VARCHAR}), '%')
            </if>
            <if test="param.productNo !=null  and param.productNo !=''">
                and product_no  = #{param.productNo,jdbcType=VARCHAR}
            </if>
            <if test="param.rewardType != null and param.rewardType != ''">
                and reward_type = #{param.rewardType,jdbcType=VARCHAR}
            </if>
            <if test="param.tradeTimeBegin != null">
                and trade_time <![CDATA[>=]]>  #{param.tradeTimeBegin,jdbcType=TIMESTAMP}
            </if>
            <if test="param.tradeTimeEnd != null">
                and trade_time <![CDATA[ <= ]]> #{param.tradeTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="param.platTrxNo != null and param.platTrxNo != ''">
                and plat_trx_no = #{param.platTrxNo}
            </if>
        </where>
        group by
        mch_no,
        mch_name,
        vendor_no,
        vendor_name,
        product_no,
        product_name,
        reward_type,
        agent_no,
        agent_name,
        real_profit_ratio,
        agent_fee_rate
        <choose>
            <when test="param.sortColumns != null and param.sortColumns !='' ">
                <![CDATA[ ORDER BY ${param.sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY totalNetAmount DESC ]]>
            </otherwise>
        </choose>
    </select>

</mapper>
