<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.data.mappers.CkAccountDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.data.entity.CkAccountDetail">
    <!--@mbg.generated-->
    <!--@Table ck_account_detail-->
    <id column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="trx_no" jdbcType="VARCHAR" property="trxNo" />
    <result column="channel_trx_no" jdbcType="VARCHAR" property="channelTrxNo" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="channel_type" jdbcType="INTEGER" property="channelType" />
    <result column="pay_channel_no" jdbcType="VARCHAR" property="payChannelNo" />
    <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName" />
    <result column="alt_amount" jdbcType="DECIMAL" property="altAmount" />
    <result column="alt_source" jdbcType="INTEGER" property="altSource" />
    <result column="alt_desc" jdbcType="VARCHAR" property="altDesc" />
    <result column="alt_type" jdbcType="INTEGER" property="altType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    create_time, trx_no, channel_trx_no, account_no, account_name, channel_type,
    pay_channel_no, pay_channel_name, alt_amount, alt_source, alt_desc, alt_type
  </sql>
</mapper>
