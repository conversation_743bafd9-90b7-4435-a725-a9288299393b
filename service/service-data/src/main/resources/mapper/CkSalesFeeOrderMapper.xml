<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.data.mappers.CkSalesFeeOrderMapper">
    <sql id="table">ck_sales_fee_order</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.data.entity.CkSalesFeeOrder">
        <id column="plat_trx_no" property="platTrxNo" jdbcType="VARCHAR"/>
        <id column="trade_time" property="tradeTime" jdbcType="TIMESTAMP"/>

        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
        <result column="vendor_no" property="vendorNo" jdbcType="VARCHAR"/>
        <result column="vendor_name" property="vendorName" jdbcType="VARCHAR"/>
        <result column="product_no" property="productNo" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="mch_order_no" property="mchOrderNo" jdbcType="VARCHAR"/>
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="sales_fee" property="salesFee" jdbcType="DECIMAL"/>
        <result column="calculate_formula" property="calculateFormula" jdbcType="VARCHAR"/>
        <result column="merchant_type" property="merchantType" jdbcType="SMALLINT"/>
        <result column="order_type" property="orderType" jdbcType="SMALLINT"/>
        <result column="department_id" property="departmentId" jdbcType="BIGINT"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="saler_id" property="salerId" jdbcType="BIGINT"/>
        <result column="saler_name" property="salerName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="ext_json" property="extJson" jdbcType="VARCHAR"/>
        <result column="sales_profit" property="salesProfit" jdbcType="DECIMAL"/>
        <result column="order_fee" property="orderFee" jdbcType="DECIMAL"/>
        <result column="exist_agent" property="existAgent" jdbcType="SMALLINT"/>
        <result column="agent_profit" property="agentProfit" jdbcType="DECIMAL"/>
        <result column="agent_second_profit" property="agentSecondProfit" jdbcType="DECIMAL"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time, trade_time, mch_no, mch_name, vendor_no, vendor_name, product_no, product_name, mch_order_no, plat_trx_no, order_amount, sales_fee, calculate_formula, merchant_type, order_type, department_id, department_name, saler_id, saler_name, remark, ext_json, sales_profit, order_fee, exist_agent, agent_profit, agent_second_profit
    </sql>
</mapper>
