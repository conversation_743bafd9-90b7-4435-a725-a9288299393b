package com.zhixianghui.service.trade;

import com.zhixianghui.common.util.utils.AESUtil;
import org.junit.Test;
import org.springframework.util.Base64Utils;

import java.nio.charset.StandardCharsets;

public class CommonTestor {

    @Test
    public void aesDecrpt() {


        String grug17051018liam = AESUtil.encryptECB(Base64Utils.encodeToString("赵文平".getBytes(StandardCharsets.UTF_8)), "grug17051018liam");

        System.out.println(grug17051018liam);
    }

}
