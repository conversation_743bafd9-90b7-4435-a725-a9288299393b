package com.zhixianghui.service.trade.dao;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.trade.bo.OrderItemTrxIdBo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-13 14:43
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class OrderItemDaoTest {
    @Autowired
    private OrderItemDao orderItemDao;
    @Autowired
    private OrderItemBiz orderBiz;
    @Autowired
    private RecordItemDao recordItemDao;

    @Test
    public void order() {
        Map<String,Object> param = Maps.newHashMap();
        Date endTime = DateUtil.getLastOfMonth(DateUtil.parse("2021-08-31"));
        Date beginTime = DateUtil.getFirstOfMonth(DateUtil.parse("2021-08-01"));
        param.put("beginDate", beginTime);
        param.put("endDate", endTime);
        System.out.println(beginTime);
        Date lastBeginDate = DateUtil.getLastMonth(beginTime);
        Date lastEndDate = DateUtil.getLastMonth(endTime);
        System.out.println(lastBeginDate);
        param.put("lastBeginDate", lastBeginDate);
        param.put("lastEndDate", lastEndDate);

        param.put("completeDate", beginTime);

        System.out.println(JSONObject.toJSON(orderBiz.freelanceStat(param)));
    }

    @Test
    public void listTrxNoPage() {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo","O20201116000000144");
        paramMap.put("beginDate", TrxNoDateUtil.getDateFromTrxNo("O20201116000000144"));
        paramMap.put("endDate", TrxNoDateUtil.getDateFromTrxNo("O20201116000000144",1));
        paramMap.put("orderItemStatus", OrderItemStatusEnum.CREATE.getValue());
        System.out.println(paramMap);
        PageResult<List<OrderItemTrxIdBo>> result = orderItemDao.listTrxNoIdPage(paramMap, PageParam.newInstance(1,10,"ID ASC"));
        System.out.println(result.getData());
    }

    @Test
    public void listBy() {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo","O20201113000000069");
        paramMap.put("beginDate", TrxNoDateUtil.getDateFromTrxNo("O20201113000000069"));
        paramMap.put("endDate", TrxNoDateUtil.getDateFromTrxNo("O20201113000000069",1));
        System.out.println(paramMap);
        List<OrderItem> result = orderItemDao.listBy(paramMap);
        System.out.println(result);
    }

    @Test
    public void listPage() {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo","O20201113000000069");
        paramMap.put("beginDate", TrxNoDateUtil.getDateFromTrxNo("O20201113000000069"));
        paramMap.put("endDate", TrxNoDateUtil.getDateFromTrxNo("O20201113000000069",1));
        System.out.println(paramMap);

        PageResult<List<OrderItem>> result = orderItemDao.listPage(paramMap,PageParam.newInstance(1,10));
        System.out.println(result.getData());
    }

    @Test
    public void testGroup() {
//        Map<String,Object> param = new HashMap<>();
//        param.put("beginDate", DateUtil.addDay(new Date(), -10));
//        param.put("endDate", new Date());
//        List<RecordItem> recordItems = recordItemDao.getIdcardNoMainstayGroup(param, 1, 10);
//        System.out.println(1);
    }
}