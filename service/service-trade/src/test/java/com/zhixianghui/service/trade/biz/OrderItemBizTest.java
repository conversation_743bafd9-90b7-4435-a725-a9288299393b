package com.zhixianghui.service.trade.biz;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.bo.OrderItemTrxIdBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-16 20:38
 **/
@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class OrderItemBizTest {
    @Autowired
    private OrderItemBiz orderItemBiz;
    @Autowired
    private TradeHelperBiz tradeHelperBiz;

    @Test
    public void tsSum(){
        System.out.println(orderItemBiz.sumOrderItem(new HashMap<>()));
    }
    @Test
    public void listTrxNoIdPage() {
        PageParam pageParam = PageParam.newInstance(1, TradeConstant.SELECT_NUM_PER_GROUP,"PLAT_TRX_NO ASC");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", "O20201116000000009");
        paramMap.put("orderStatus", OrderItemStatusEnum.ACCEPTED.getValue());
        paramMap.put("beginDate", TrxNoDateUtil.getDateFromTrxNo("O20201116000000009"));
        paramMap.put("endDate", TrxNoDateUtil.getDateFromTrxNo("O20201116000000009",1));
        orderItemBiz.listTrxNoIdPage(paramMap,pageParam);
    }

    @Test
    public void listPage(){
        log.info("info-start");
        // 分批发放
        PageParam pageParam = PageParam.newInstance(1, TradeConstant.SELECT_NUM_PER_GROUP,"ID ASC");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", "O20201123000000070");
        paramMap.put("orderItemStatus", OrderItemStatusEnum.ACCEPTED.getValue());
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        PageResult<List<OrderItemTrxIdBo>> pageResult ;
        List<OrderItemTrxIdBo> recordList ;
        do {
            pageResult = orderItemBiz.listTrxNoIdPage(paramMap, pageParam);
            recordList = pageResult.getData();
            if(!ObjectUtils.isEmpty(recordList)){
                List<OrderItemTrxIdBo> notifyList = Lists.newArrayList();
                recordList.forEach(
                        trxIdBo->{
                            notifyList.add(trxIdBo);
                            if(notifyList.size() >= TradeConstant.NOTIFY_NUM_PER_GROUP){
                                //满NOTIFY_NUM_PER_GROUP 发一次
//                                tradeNotifyBiz.notifyGrantStart(order.getEmployerNo(), order.getPlatBatchNo(),
//                                        notifyList.stream().map(OrderItemTrxIdBo::getPlatTrxNo).collect(Collectors.toList()));
                                List<String> list = notifyList.stream().map(OrderItemTrxIdBo::getPlatTrxNo).collect(Collectors.toList());
                                System.out.println(list);
                                notifyList.clear();
                            }
                        }
                );
                //发送剩余的不满NOTIFY_COUNT的
                if(!ObjectUtils.isEmpty(notifyList)){
                    List<String> list = notifyList.stream().map(OrderItemTrxIdBo::getPlatTrxNo).collect(Collectors.toList());
                    System.out.println(list);
//                    tradeNotifyBiz.notifyGrantStart(order.getEmployerNo(), order.getPlatBatchNo(),
//                            notifyList.stream().map(OrderItemTrxIdBo::getPlatTrxNo).collect(Collectors.toList()));
                    notifyList.clear();
                }
                paramMap.put("overId", recordList.get(recordList.size()-1).getId());
            }
        } while (!ObjectUtils.isEmpty(recordList));
        log.info("info-end");
    }

    @Test
    public void listPageSharing(){
        PageParam pageParam = PageParam.newInstance(100, 10,"ID ASC");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", "G20201130000000270");
        orderItemBiz.listPage(paramMap,pageParam);
    }

}
