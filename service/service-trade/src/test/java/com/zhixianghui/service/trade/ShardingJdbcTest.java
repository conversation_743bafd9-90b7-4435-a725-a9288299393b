package com.zhixianghui.service.trade;

import cn.hutool.core.date.DateTime;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.trade.dto.DateQueryDTO;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.biz.SignRecordBiz;
import com.zhixianghui.service.trade.biz.StatisticsBiz;
import com.zhixianghui.service.trade.dao.OrderDao;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 分表测试类
 * @author: xingguang li
 * @created: 2020/11/05 10:05
 */
@SpringBootTest(classes = ServiceTradeApp.class)
@RunWith(SpringRunner.class)
@Slf4j
public class ShardingJdbcTest {

    @Autowired
    private OrderDao orderDao;
    @Autowired
    private OrderItemBiz orderItemBiz;
    @Autowired
    private SignRecordBiz biz;
    @Autowired
    private StatisticsBiz statisticsBiz;
    @Autowired
    private RecordItemBiz recordItemBiz;

    @Test
    public void test() {
        DateQueryDTO dateQueryDTO = new DateQueryDTO();
        dateQueryDTO.setCompleteBeginDate(DateUtil.stringToDateTime("2021-11-01"));
        dateQueryDTO.setCompleteEndDate(DateUtil.stringToDateTime("2021-11-25"));
        statisticsBiz.getMch(dateQueryDTO, dateQueryDTO);
    }

    @Test
    public void testCount() {
        String startDate="2021-08-01";
        String endDate="2021-11-25";
        DateTime startDateTime = cn.hutool.core.date.DateUtil.parseDate(startDate);
        DateTime endDateTime = cn.hutool.core.date.DateUtil.parseDate(endDate);
        Map<String, Object> param = new HashMap<>();
        param.put("completeBeginDate", cn.hutool.core.date.DateUtil.beginOfDay(startDateTime.toJdkDate()).toJdkDate());
        param.put("completeEndDate", cn.hutool.core.date.DateUtil.endOfDay(endDateTime.toJdkDate()).toJdkDate());
        Map<String, Object> map = recordItemBiz.coreIndexStatistics(param);
        System.out.println(map);
    }


    @Test
    public void testSignRecord() {
        SignRecord signRecord = new SignRecord();
        signRecord.setId(46L);
        signRecord.setSignStatus(SignStatusEnum.PENDING.getValue());
        signRecord.setVersion(1);
        biz.updateIfNotNull(signRecord);
    }

    @Test
    public void testOrderInsert() {
        Order order = new Order();
        order.setVersion(1);
        order.setCreateTime(new Date());
        order.setCreateDate(new Date());
        order.setMchBatchNo("3211111");
        order.setBatchName("3测试分表批次2");
        order.setPlatBatchNo("322222");
        order.setEmployerNo("333333333");
        order.setEmployerName("2测试分表用工企业");
        order.setMainstayNo("5555555");
        order.setMainstayName("dddddd");
        order.setChannelType(122);
        order.setPayChannelNo("dddddddd");
        order.setChannelName("channelname");
        order.setBatchStatus(200);
        orderDao.insert(order);
    }

    @Test
    public void testOrderQuery() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("createDate", DateUtil.addDay(new Date(), -30));
        params.put("mchBatchNo", "211111");
//        Order order = orderItemBiz.listPage(params);
//        log.info(JSONObject.toJSONString(order));
    }

}
