package com.zhixianghui.service.trade;

import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.service.trade.helper.CacheBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-22 11:26
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class CacheBizTest {
    @Autowired
    private CacheBiz cacheBiz;

    @Test
    public void testCache(){
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setMainstayNo("S000015");
        amountQueryDto.setEmployerNo("M00000031");
        amountQueryDto.setChannelType(ChannelTypeEnum.BANK.getValue());
        amountQueryDto.setSubMerchantNo("***************");
        amountQueryDto.setChannelNo(ChannelNoEnum.JOINPAY.name());
        amountQueryDto.setChannelMchNo("***************");
        System.out.println(cacheBiz.getAmount(amountQueryDto));
        amountQueryDto.setEmployerNo("M00000032");
        System.out.println(cacheBiz.getAmount(amountQueryDto));
        System.exit(1);
    }
}
