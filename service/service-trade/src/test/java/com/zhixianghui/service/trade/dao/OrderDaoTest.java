package com.zhixianghui.service.trade.dao;


import com.google.common.collect.Maps;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.starter.comp.component.RedisLock;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-06 10:55
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class OrderDaoTest {
    @Autowired
    private OrderDao orderDao;
    @Autowired
    private RedisLock redisLock;

    @Test
    public void insertOrder(){
        Order order = new Order();
        order.setCreateDate(new Date());
        order.setCreateTime(new Date());
        order.setUpdateTime(new Date());
        order.setMchBatchNo("1");
        order.setBatchName("1");
        order.setPlatBatchNo("1");
        order.setEmployerNo("1");
        order.setEmployerName("1");
        order.setMainstayNo("1");
        order.setMainstayName("1");
        order.setChannelType(1);
        order.setPayChannelNo("1");
        order.setChannelName("1");
        order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
        orderDao.insert(order);
        System.out.println(order);
    }

//    @Test
//    public void countBy(){
//        Map<String,Object> paramMap = Maps.newHashMap();
//        paramMap.put("platBatchNo","O20201113000000049");
//        paramMap.put("beginDate", TrxNoDateUtil.getDateFromTrxNo("O20201113000000049"));
//        paramMap.put("endDate", TrxNoDateUtil.getDateFromTrxNo("O20201113000000049",1));
//        System.out.println(paramMap);
//        OrderItemDao.countBy(paramMap);
//    }

//    @Test
//    public void getLockLong() throws InterruptedException {
//        ExecutorService executor = new ThreadPoolExecutor(200, 1000,
//                60, TimeUnit.SECONDS,
//                new ArrayBlockingQueue<>(10000));
//
//        executor.execute(
//                ()->{
//                    RLock a = redisLock.tryLock("222",10000,50000L);
//                    System.out.println(Thread.currentThread().getName() + ":拿到锁" + a);
//                    try {
//
//                        Thread.sleep(20000);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    System.out.println(Thread.currentThread().getName() + ":准备释放锁");
//                    redisLock.unlock(a);
//                }
//        );
//        Thread.sleep(2000);
//        executor.execute(
//                ()->{
//                    RLock a = redisLock.tryLock("222",10000,5000L);
//                    System.out.println(Thread.currentThread().getName() + ":拿到锁" + a);
//                    System.out.println(Thread.currentThread().getName() + ":准备释放锁");
//                    redisLock.unlock(a);
//                }
//        );
//        Thread.sleep(90000);
//    }
//
//    @Test
//    public void getLock() throws InterruptedException {
//        ExecutorService executor = new ThreadPoolExecutor(200, 1000,
//                60, TimeUnit.SECONDS,
//                new ArrayBlockingQueue<>(10000));
//
//        executor.execute(
//                ()->{
//                    String clientId = redisLock.tryLockLong("aaa", 5000, 1);
//                    System.out.println("拿到锁" + clientId);
//                    try {
//                        Thread.sleep(20000);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                    redisLock.unlockLong(clientId);
//                }
//        );
//        Thread.sleep(2000);
//        executor.execute(
//                ()->{
//                    String clientId = redisLock.tryLockLong("aaa", 1000, 1);
//                    System.out.println("拿到锁" + clientId);
//                    redisLock.unlockLong(clientId);
//                }
//        );
//        Thread.sleep(90000);
//    }
}