package com.zhixianghui.service.trade.biz;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.service.trade.dao.OrderDao;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-16 18:12
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
public class OrderBizTest {

    @Autowired
    private OrderBiz orderBiz;

    @Autowired
    private OrderDao orderDao;
    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Test
    public void getOne() {
//        Map<String,Object> paramMap = Maps.newHashMap();
        orderBiz.getOne(Collections.singletonMap("platBatchNo","O20201116000000166"));
    }

    @Test
    public void listPageInCreateDate(){
        PageParam pageParam = PageParam.newInstance(1, 10,"ID ASC");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("mchBatchNo","G20201130000000270");

        List<Date> createDateList = Lists.newArrayList();
        createDateList.add(TrxNoDateUtil.getDateFromTrxNo("G20201130000000270"));
        createDateList.add(TrxNoDateUtil.getDateFromTrxNo("G20201201000000271"));
        paramMap.put("createDateList",createDateList);

        PageResult<List<Order>> pageResult = orderDao.listPage(paramMap,pageParam);
        System.out.println(pageResult.getData());
    }


    @Test
    public void testDownload() throws Exception {
        String body0 = alipayFacade.transCommonQuery("Z20220721000000033", "ENTRUST_ALLOCATION", "SINGLE_TRANSFER_NO_PWD");
        JSONObject data0 = JSONUtil.parseObj(body0).getJSONObject("alipay_fund_trans_common_query_response");
        String payFundOrderId0 = data0.getStr("pay_fund_order_id");
        //开始发送回单申请
        String fileId0 = alipayFacade.billApply(payFundOrderId0, "20225514851912135771");
        System.out.println(fileId0);
//        TimeUnit.MINUTES.sleep(1);
        String fileUrl = alipayFacade.billQuery(fileId0, "20225514851912135771");
        System.out.println(fileUrl);
//        String body1 = alipayFacade.transCommonQuery("Z20220719000000022", "ENTRUST_ALLOCATION", "SINGLE_TRANSFER_NO_PWD");
//        JSONObject data1 = JSONUtil.parseObj(body1).getJSONObject("alipay_fund_trans_common_query_response");
//        String payFundOrderId1 = data1.getStr("pay_fund_order_id");
//        String fileId = alipayFacade.billApply(payFundOrderId1, "20225514851912135771");
//        System.out.println(fileId);

        //280237504
    }

    @Test
    public void testPay(){
        String json="{\"appid\":\"wxbff0f5456c7dacd3\",\"doStepOne\":false,\"doStepTwo\":false,\"employerNo\":\"M00000369\",\"mainstayNo\":\"S000001\",\"mchName\":\"广西帕拉互娱科技有限公司\",\"platTrxNo\":\"E202202281842099\",\"realPayerName\":\"河南宝岭信息科技有限公司\",\"retry\":false,\"retryStep\":0,\"serviceFee\":\"0.56\",\"update\":false}";

        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_PAY_QUERY, UUIDUitl.generateString(10), "E202202281842099",
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_WX_PAY_RETRY, json, MsgDelayLevelEnum.M_1.getValue());
    }
}