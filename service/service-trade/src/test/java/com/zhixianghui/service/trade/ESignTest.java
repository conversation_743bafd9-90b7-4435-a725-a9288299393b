package com.zhixianghui.service.trade;

import com.github.rholder.retry.RetryException;
import com.zhixianghui.facade.trade.vo.esign.ESign;
import com.zhixianghui.facade.trade.vo.esign.ESignItem;
import com.zhixianghui.service.trade.biz.SimpleESignHandleTemPlateBiz;
import com.zhixianghui.service.trade.utils.ESignUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月29日 11:57:00
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class ESignTest {
    @Autowired
    SimpleESignHandleTemPlateBiz simpleESignHandleBiz;

    @Test
    public void test() {

        List<ESignItem> eSignItems = new ArrayList<>();
        ESignItem eSignItem1 = new ESignItem("*********", "mchSignArea");
        ESignItem eSignItem2 = new ESignItem("*********", "supplierSignArea");
        eSignItems.add(eSignItem1);
        eSignItems.add(eSignItem2);

        //后台添加模板
//        String sign = simpleESignHandleBiz.sign(ESign.builder()
//                .logNo("xxxx")
////                .fileUrl("hjzx/M00/00/63/CgoKa2Lh7oOAaV3oAAA5y4Hk5-g93.docx")
//                .flowName("测试")
//                .fileName("测试")
//                .simpleFormFields(getData())
//                .templateId("5d19f2de003e46ad88f60708e894495b")
//                .eSignItems(eSignItems)
//                .build());

        //接口添加
//        String sign = simpleESignHandleBiz.sign(ESign.builder()
//                .logNo("xxxx")
//                .fileUrl("hjzx/M00/00/63/CgoKa2Lh7oOAaV3oAAA5y4Hk5-g93.docx")
//                .flowName("测试")
//                .fileName("测试")
////                .simpleFormFields(getData())
//                .eSignItems(ESignUtil.simpleKeywordESignItem("公章", "*********", 120, 0))
//                .build());

//        System.out.println(sign);
    }


    @Test
    public void test2() throws ExecutionException, RetryException {
//        eSignHandleBiz.existTemplate(new TemplateStatus("e38a8f454a3944da802168ab7678f782"), "test");

    }
    //e38a8f454a3944da802168ab7678f782


    private HashMap<String, String> getData() {
        String str = "mchName1\n" +
                "supplierName1\n" +
                "fileNO\n" +
//                "signDate1\n" +
                "signLoc\n" +
                "mchName2\n" +
                "mchLegalPersonName1\n" +
                "mchFileContactName1\n" +
                "mchManagementAddr\n" +
                "mchFileContactPhone1\n" +
                "mchContactEmail\n" +
                "supplierName2\n" +
                "supplierLegalPersonName\n" +
                "supplierFileContactName1\n" +
                "supplierManagementAddr\n" +
                "supplierFileContactPhone1\n" +
                "supplierContactEmail\n" +
                "supplierName3\n" +
                "workCategoryName\n" +
                "mchFeeLimit1\n" +
                "mchFeeLimit2\n" +
                "quoteRate\n" +
                "supplierAccountName\n" +
                "supplierBankName\n" +
                "supplierAccountNo\n" +
                "mchName3\n" +
                "mchTaxNo\n" +
                "mchInvoiceAddress\n" +
                "mchInvoicePhone\n" +
                "mchInvoiceBankName\n" +
                "mchInvoiceAccountNo\n" +
                "mchInvoiceCategoryName\n" +
                "mchName4\n" +
                "mchFileContactName2\n" +
                "mchFileContactPhone2\n" +
                "supplierName4\n" +
                "supplierFileContactName2\n" +
                "supplierFileContactPhone2\n" +
//                "signDate2\n" +
                "mchName5\n" +
                "mchLegalPersonName2\n" +
                "mchRegisterAddr\n" +
                "mchIndustryTypeName\n" +
                "mchManagementScope\n" +
//                "mchBusinessLicenseImg\n" +
                "mchContactor\n" +
                "mchPersonnelsName\n" +
                "mchServiceDesc";
        HashMap<String, String> map = new HashMap<>();
        String[] split = str.split("\n");
        for (String s : split) {
            map.put(s, "111.1");
        }
        map.put("signDate1", "2022年02月01日");
        map.put("signDate2", "2022年02月01日");
        map.put("mchBusinessLicenseImg", "https://static.hjzxh.com/hjzx/M00/00/7F/Ch62CmIXNQeAMEEIAAOUs1L8MNU902.jpg");
        return map;

    }
}