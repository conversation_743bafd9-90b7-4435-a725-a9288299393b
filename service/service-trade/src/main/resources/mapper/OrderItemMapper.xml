<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.OrderItem">
    <sql id="table">tbl_order_item</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.OrderItem">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="DATE"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="COMPLETE_TIME" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="MCH_BATCH_NO" property="mchBatchNo" jdbcType="VARCHAR"/>
        <result column="PLAT_BATCH_NO" property="platBatchNo" jdbcType="VARCHAR"/>
        <result column="MCH_ORDER_NO" property="mchOrderNo" jdbcType="VARCHAR"/>
        <result column="PLAT_TRX_NO" property="platTrxNo" jdbcType="VARCHAR"/>
        <result column="LAUNCH_WAY" property="launchWay" jdbcType="SMALLINT"/>
        <result column="PRODUCT_NO" property="productNo" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_NAME" property="channelName" jdbcType="VARCHAR"/>
        <result column="TAX_PAYER" property="taxPayer" jdbcType="SMALLINT"/>
        <result column="RECEIVE_NAME" property="receiveName" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME_MD5" property="receiveNameMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO" property="receiveIdCardNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO_MD5" property="receiveIdCardNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO" property="receiveAccountNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO_MD5" property="receiveAccountNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO" property="receivePhoneNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO_MD5" property="receivePhoneNoMd5" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
        <result column="ORDER_ITEM_TASK_AMOUNT" property="orderItemTaskAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_NET_AMOUNT" property="orderItemNetAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_FEE" property="orderItemFee" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_AMOUNT" property="orderItemAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_TAX_AMOUNT" property="orderItemTaxAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_STATUS" property="orderItemStatus" jdbcType="SMALLINT"/>
        <result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR"/>
        <result column="ERROR_DESC" property="errorDesc" jdbcType="VARCHAR"/>
        <result column="ENCRYPT_KEY_ID" property="encryptKeyId" jdbcType="BIGINT"/>
        <result column="ACCESS_TIMES" property="accessTimes" jdbcType="SMALLINT"/>
        <result column="IS_PASS_HANGUP" property="isPassHangup" jdbcType="BIT"/>
        <result column="HANGUP_APPROVAL_LOGIN_NAME" property="hangupApprovalLoginName" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="JSON_STR" property="jsonStr" jdbcType="OTHER"/>
        <result column="APPID" property="appid" jdbcType="VARCHAR"/>
        <result column="IS_DELETE" property="isDelete" jdbcType="SMALLINT"/>
        <result column="MEMO" property="memo" jdbcType="VARCHAR"/>
        <result column="JOB_ID" property="jobId" jdbcType="VARCHAR"/>
        <result column="JOB_NAME" property="jobName" jdbcType="VARCHAR"/>
        <result column="WORK_CATEGORY_CODE" property="workCategoryCode" jdbcType="VARCHAR"/>
        <result column="WORK_CATEGORY_NAME" property="workCategoryName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TRX_NO" property="channelTrxNo" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_DATE, CREATE_TIME, UPDATE_TIME, COMPLETE_TIME, MCH_BATCH_NO, PLAT_BATCH_NO, MCH_ORDER_NO,
          PLAT_TRX_NO, LAUNCH_WAY, PRODUCT_NO,PRODUCT_NAME,EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NO, MAINSTAY_NAME, CHANNEL_TYPE,
          PAY_CHANNEL_NO, CHANNEL_NAME,TAX_PAYER, RECEIVE_NAME, RECEIVE_NAME_MD5, RECEIVE_ID_CARD_NO, RECEIVE_ID_CARD_NO_MD5,
          RECEIVE_ACCOUNT_NO, RECEIVE_ACCOUNT_NO_MD5, RECEIVE_PHONE_NO, RECEIVE_PHONE_NO_MD5, BANK_NAME, BANK_CODE,ORDER_ITEM_TASK_AMOUNT,
          ORDER_ITEM_NET_AMOUNT, ORDER_ITEM_FEE, ORDER_ITEM_AMOUNT,ORDER_ITEM_TAX_AMOUNT, ORDER_ITEM_STATUS, ERROR_CODE, ERROR_DESC,
          ENCRYPT_KEY_ID, ACCESS_TIMES, IS_PASS_HANGUP, HANGUP_APPROVAL_LOGIN_NAME, REMARK, JSON_STR,APPID,IS_DELETE,MEMO,JOB_ID,JOB_NAME,
          WORK_CATEGORY_CODE,WORK_CATEGORY_NAME, CHANNEL_TRX_NO
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.OrderItem">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_DATE,
            CREATE_TIME,
            UPDATE_TIME,
            COMPLETE_TIME,
            MCH_BATCH_NO,
            PLAT_BATCH_NO,
            MCH_ORDER_NO,
            PLAT_TRX_NO,
            LAUNCH_WAY,
            PRODUCT_NO,
            PRODUCT_NAME,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            CHANNEL_TYPE,
            PAY_CHANNEL_NO,
            CHANNEL_NAME,
            TAX_PAYER,
            RECEIVE_NAME,
            RECEIVE_NAME_MD5,
            RECEIVE_ID_CARD_NO,
            RECEIVE_ID_CARD_NO_MD5,
            RECEIVE_ACCOUNT_NO,
            RECEIVE_ACCOUNT_NO_MD5,
            RECEIVE_PHONE_NO,
            RECEIVE_PHONE_NO_MD5,
            BANK_NAME,
            BANK_CODE,
            ORDER_ITEM_TASK_AMOUNT,
            ORDER_ITEM_NET_AMOUNT,
            ORDER_ITEM_FEE,
            ORDER_ITEM_AMOUNT,
            ORDER_ITEM_TAX_AMOUNT,
            ORDER_ITEM_STATUS,
            ERROR_CODE,
            ERROR_DESC,
            ENCRYPT_KEY_ID,
            ACCESS_TIMES,
            IS_PASS_HANGUP,
            HANGUP_APPROVAL_LOGIN_NAME,
            REMARK,
            APPID,
            IS_DELETE,
            JSON_STR,
            MEMO,
            JOB_ID,
            JOB_NAME,
            WORK_CATEGORY_CODE,
            WORK_CATEGORY_NAME,
            CHANNEL_TRX_NO
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{createDate,jdbcType=DATE},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{completeTime,jdbcType=TIMESTAMP},
        #{mchBatchNo,jdbcType=VARCHAR},
        #{platBatchNo,jdbcType=VARCHAR},
        #{mchOrderNo,jdbcType=VARCHAR},
        #{platTrxNo,jdbcType=VARCHAR},
        #{launchWay,jdbcType=SMALLINT},
        #{productNo,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{channelType,jdbcType=SMALLINT},
        #{payChannelNo,jdbcType=VARCHAR},
        #{channelName,jdbcType=VARCHAR},
        #{taxPayer,jdbcType=SMALLINT},
        #{receiveName,jdbcType=VARCHAR},
        #{receiveNameMd5,jdbcType=VARCHAR},
        #{receiveIdCardNo,jdbcType=VARCHAR},
        #{receiveIdCardNoMd5,jdbcType=VARCHAR},
        #{receiveAccountNo,jdbcType=VARCHAR},
        #{receiveAccountNoMd5,jdbcType=VARCHAR},
        #{receivePhoneNo,jdbcType=VARCHAR},
        #{receivePhoneNoMd5,jdbcType=VARCHAR},
        #{bankName,jdbcType=VARCHAR},
        #{bankCode,jdbcType=VARCHAR},
        #{orderItemTaskAmount,jdbcType=DECIMAL},
        #{orderItemNetAmount,jdbcType=DECIMAL},
        #{orderItemFee,jdbcType=DECIMAL},
        #{orderItemAmount,jdbcType=DECIMAL},
        #{orderItemTaxAmount,jdbcType=DECIMAL},
        #{orderItemStatus,jdbcType=SMALLINT},
        #{errorCode,jdbcType=VARCHAR},
        #{errorDesc,jdbcType=VARCHAR},
        #{encryptKeyId,jdbcType=BIGINT},
        #{accessTimes,jdbcType=SMALLINT},
        #{isPassHangup,jdbcType=BIT},
        #{hangupApprovalLoginName,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{appid,jdbcType=VARCHAR},
        #{isDelete,jdbcType=SMALLINT},
        #{jsonStr,jdbcType=OTHER},
        #{memo,jdbcType=VARCHAR},
        #{jobId,jdbcType=VARCHAR},
        #{jobName,jdbcType=VARCHAR},
        #{workCategoryCode,jdbcType=VARCHAR},
        #{workCategoryName,jdbcType=VARCHAR},
        #{channelTrxNo,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            CREATE_DATE,
            CREATE_TIME,
            UPDATE_TIME,
            COMPLETE_TIME,
            MCH_BATCH_NO,
            PLAT_BATCH_NO,
            MCH_ORDER_NO,
            PLAT_TRX_NO,
            LAUNCH_WAY,
            PRODUCT_NO,
            PRODUCT_NAME,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            CHANNEL_TYPE,
            PAY_CHANNEL_NO,
            CHANNEL_NAME,
            TAX_PAYER,
            RECEIVE_NAME,
            RECEIVE_NAME_MD5,
            RECEIVE_ID_CARD_NO,
            RECEIVE_ID_CARD_NO_MD5,
            RECEIVE_ACCOUNT_NO,
            RECEIVE_ACCOUNT_NO_MD5,
            RECEIVE_PHONE_NO,
            RECEIVE_PHONE_NO_MD5,
            BANK_NAME,
            BANK_CODE,
            ORDER_ITEM_TASK_AMOUNT,
            ORDER_ITEM_NET_AMOUNT,
            ORDER_ITEM_FEE,
            ORDER_ITEM_AMOUNT,
            ORDER_ITEM_TAX_AMOUNT,
            ORDER_ITEM_STATUS,
            ERROR_CODE,
            ERROR_DESC,
            ENCRYPT_KEY_ID,
            ACCESS_TIMES,
            IS_PASS_HANGUP,
            HANGUP_APPROVAL_LOGIN_NAME,
            REMARK,
            APPID,
            IS_DELETE,
            JSON_STR,
            MEMO,
            JOB_ID,
            JOB_NAME,
            WORK_CATEGORY_CODE,
            WORK_CATEGORY_NAME,
            CHANNEL_TRX_NO
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.createDate,jdbcType=DATE},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.completeTime,jdbcType=TIMESTAMP},
            #{item.mchBatchNo,jdbcType=VARCHAR},
            #{item.platBatchNo,jdbcType=VARCHAR},
            #{item.mchOrderNo,jdbcType=VARCHAR},
            #{item.platTrxNo,jdbcType=VARCHAR},
            #{item.launchWay,jdbcType=SMALLINT},
            #{item.productNo,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.channelType,jdbcType=SMALLINT},
            #{item.payChannelNo,jdbcType=VARCHAR},
            #{item.channelName,jdbcType=VARCHAR},
            #{item.taxPayer,jdbcType=SMALLINT},
            #{item.receiveName,jdbcType=VARCHAR},
            #{item.receiveNameMd5,jdbcType=VARCHAR},
            #{item.receiveIdCardNo,jdbcType=VARCHAR},
            #{item.receiveIdCardNoMd5,jdbcType=VARCHAR},
            #{item.receiveAccountNo,jdbcType=VARCHAR},
            #{item.receiveAccountNoMd5,jdbcType=VARCHAR},
            #{item.receivePhoneNo,jdbcType=VARCHAR},
            #{item.receivePhoneNoMd5,jdbcType=VARCHAR},
            #{item.bankName,jdbcType=VARCHAR},
            #{item.bankCode,jdbcType=VARCHAR},
            #{item.orderItemTaskAmount,jdbcType=DECIMAL},
            #{item.orderItemNetAmount,jdbcType=DECIMAL},
            #{item.orderItemFee,jdbcType=DECIMAL},
            #{item.orderItemAmount,jdbcType=DECIMAL},
            #{item.orderItemTaxAmount,jdbcType=DECIMAL},
            #{item.orderItemStatus,jdbcType=SMALLINT},
            #{item.errorCode,jdbcType=VARCHAR},
            #{item.errorDesc,jdbcType=VARCHAR},
            #{item.encryptKeyId,jdbcType=BIGINT},
            #{item.accessTimes,jdbcType=SMALLINT},
            #{item.isPassHangup,jdbcType=BIT},
            #{item.hangupApprovalLoginName,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.appid,jdbcType=VARCHAR},
            #{item.isDelete,jdbcType=SMALLINT},
            #{item.jsonStr,jdbcType=OTHER},
            #{item.memo,jdbcType=VARCHAR},
            #{item.jobId,jdbcType=VARCHAR},
            #{item.jobName,jdbcType=VARCHAR},
            #{item.workCategoryCode,jdbcType=VARCHAR},
            #{item.workCategoryName,jdbcType=VARCHAR},
            #{item.channelTrxNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.OrderItem">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=SMALLINT} +1,
                CREATE_DATE = #{createDate,jdbcType=DATE},
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
                MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR},
                PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR},
                MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR},
                PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR},
                LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT},
                PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
                CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
                CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
                TAX_PAYER = #{taxPayer,jdbcType=SMALLINT},
                RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
                RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR},
                RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR},
                RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
                RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR},
                RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
                RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR},
                RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
                BANK_NAME = #{bankName,jdbcType=VARCHAR},
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
                ORDER_ITEM_TASK_AMOUNT = #{orderItemTaskAmount,jdbcType=DECIMAL},
                ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL},
                ORDER_ITEM_FEE = #{orderItemFee,jdbcType=DECIMAL},
                ORDER_ITEM_AMOUNT = #{orderItemAmount,jdbcType=DECIMAL},
                ORDER_ITEM_TAX_AMOUNT = #{orderItemTaxAmount,jdbcType=DECIMAL},
                ORDER_ITEM_STATUS = #{orderItemStatus,jdbcType=SMALLINT},
                ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
                ERROR_DESC = #{errorDesc,jdbcType=VARCHAR},
                ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT},
                ACCESS_TIMES = #{accessTimes,jdbcType=SMALLINT},
                IS_PASS_HANGUP = #{isPassHangup,jdbcType=BIT},
                HANGUP_APPROVAL_LOGIN_NAME = #{hangupApprovalLoginName,jdbcType=VARCHAR},
                REMARK = #{remark,jdbcType=VARCHAR},
                APPID=#{appid,jdbcType=VARCHAR},
                IS_DELETE=#{isDelete,jdbcType=SMALLINT},
                JSON_STR = #{jsonStr,jdbcType=OTHER},
                MEMO = #{memo,jdbcType=VARCHAR},
                JOB_ID = #{jobId,jdbcType=VARCHAR},
                JOB_NAME = #{jobName,jdbcType=VARCHAR},
                WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR},
                WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR},
                CHANNEL_TRX_NO = #{channelTrxNo,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR} and VERSION = #{version,jdbcType=BIGINT}
            and CREATE_DATE = #{createDate,jdbcType=DATE}
    </update>

    <select id="merchantStat" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.entity.MerchantStat">
        select
            EMPLOYER_NO as employerNo,
            EMPLOYER_NAME as employerName,
            MAINSTAY_NAME as mainstayName,
            MAINSTAY_NO as mainstayNo,
            sum(ORDER_ITEM_NET_AMOUNT) as orderItemNetAmount,
            sum(ORDER_ITEM_AMOUNT) as orderItemAmount,
                DATE_FORMAT(COMPLETE_TIME,'%Y-%m') as `createDate`,
            count(distinct RECEIVE_ID_CARD_NO_MD5) as receiverNumber,
            count(RECEIVE_ID_CARD_NO_MD5) as orderAmount,
            ENCRYPT_KEY_ID as encryptKeyId,
            COMPLETE_TIME
        from(
            select
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NAME,
            MAINSTAY_NO,
            ORDER_ITEM_NET_AMOUNT,
            ORDER_ITEM_AMOUNT,
            COMPLETE_TIME,
            RECEIVE_ID_CARD_NO_MD5,
            ENCRYPT_KEY_ID
            from tbl_order_item where COMPLETE_TIME is not null  and ORDER_ITEM_STATUS = 100
            and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            union all
            select
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NAME,
            MAINSTAY_NO,
            ORDER_ITEM_NET_AMOUNT,
            ORDER_ITEM_AMOUNT,
            RECEIVE_ID_CARD_NO_MD5,
            ENCRYPT_KEY_ID,
            COMPLETE_TIME
            from tbl_order_item where COMPLETE_TIME is not null  and ORDER_ITEM_STATUS = 100
            and CREATE_DATE between #{lastBeginDate,jdbcType=DATE} and #{lastEndDate,jdbcType=DATE}
        )a
        where DATE_FORMAT(COMPLETE_TIME,'%Y-%m') =  DATE_FORMAT(#{completeDate,jdbcType=DATE},'%Y-%m')
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo, jdbcType=VARCHAR}
        </if>
        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo, jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and mainstayName LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        group by EMPLOYER_NO, MAINSTAY_NO, DATE_FORMAT(COMPLETE_TIME,'%Y-%m')
    </select>

    <select id="freelanceStat" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.entity.FreelanceStat">
        select
        RECEIVE_PHONE_NO as receivePhoneNo,
        RECEIVE_ID_CARD_NO_MD5 as receiveIdCardNoMd5,
        RECEIVE_NAME as receiveName,
        RECEIVE_NAME_MD5 as receiveNameMd5,
        RECEIVE_ID_CARD_NO as receiveIdCardNo,
        EMPLOYER_NO as employerNo,
        EMPLOYER_NAME as employerName,
        MAINSTAY_NAME as mainstayName,
        MAINSTAY_NO as mainstayNo,
        ENCRYPT_KEY_ID as encryptKeyId,
        count(RECEIVE_ID_CARD_NO_MD5) as conditionOrder,
        sum(ORDER_ITEM_NET_AMOUNT) as orderItemNetAmount,
        COMPLETE_TIME,
        DATE_FORMAT(COMPLETE_TIME,'%Y-%m') as `createDate`
        from (
            select
                RECEIVE_PHONE_NO,
                RECEIVE_ID_CARD_NO_MD5,
                RECEIVE_NAME,
                RECEIVE_NAME_MD5,
                RECEIVE_ID_CARD_NO,
                EMPLOYER_NO,
                EMPLOYER_NAME,
                MAINSTAY_NAME,
                MAINSTAY_NO,
                ENCRYPT_KEY_ID,
                COMPLETE_TIME,
                ORDER_ITEM_NET_AMOUNT
            from tbl_order_item where COMPLETE_TIME is not null  and ORDER_ITEM_STATUS = 100
            and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            union all
            select
                RECEIVE_PHONE_NO,
                RECEIVE_ID_CARD_NO_MD5,
                RECEIVE_NAME,
                RECEIVE_NAME_MD5,
                RECEIVE_ID_CARD_NO,
                EMPLOYER_NO,
                EMPLOYER_NAME,
                MAINSTAY_NAME,
                MAINSTAY_NO,
                ENCRYPT_KEY_ID,
                COMPLETE_TIME,
                ORDER_ITEM_NET_AMOUNT
            from tbl_order_item where COMPLETE_TIME is not null  and ORDER_ITEM_STATUS = 100
            and CREATE_DATE between #{lastBeginDate,jdbcType=DATE} and #{lastEndDate,jdbcType=DATE}
        )a
        where DATE_FORMAT(COMPLETE_TIME,'%Y-%m') =  DATE_FORMAT(#{completeDate,jdbcType=DATE},'%Y-%m')
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
            and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5, jdbcType=VARCHAR}
        </if>
        <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
            and RECEIVE_NAME_MD5 = #{receiveNameMd5, jdbcType=VARCHAR}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo, jdbcType=VARCHAR}
        </if>
        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo, jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        group by RECEIVE_ID_CARD_NO_MD5, EMPLOYER_NO, MAINSTAY_NO, DATE_FORMAT(COMPLETE_TIME,'%Y-%m')
    </select>

    <select id="countOrder" parameterType="java.util.Map" resultType="long">
        select count(*) from (
            select id FROM tbl_order_item
             where RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5, jdbcType=VARCHAR}
             and ORDER_ITEM_STATUS = 100 and  COMPLETE_TIME is not null
             and  DATE_FORMAT(COMPLETE_TIME,'%Y-%m') = DATE_FORMAT(#{completeDate,jdbcType=DATE},'%Y-%m')
             and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}

			union all

		    select id FROM tbl_order_item
            where RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5, jdbcType=VARCHAR}
            and ORDER_ITEM_STATUS = 100 and  COMPLETE_TIME is not null
            and DATE_FORMAT(COMPLETE_TIME,'%Y-%m') = DATE_FORMAT(#{completeDate,jdbcType=DATE},'%Y-%m')
            and CREATE_DATE between #{lastBeginDate,jdbcType=DATE} and #{lastEndDate,jdbcType=DATE}
		) a

    </select>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <select id="getEarlestOrderItem" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        order by ID ASC LIMIT 1
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=SMALLINT}
            </if>
            <if test="createDate != null">
                and CREATE_DATE = #{createDate,jdbcType=DATE}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="completeTime != null">
                and COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP}
            </if>
            <if test="mchBatchNo != null and mchBatchNo !=''">
                and MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="platBatchNo != null and platBatchNo !=''">
                and PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="mchOrderNo != null and mchOrderNo !=''">
                and MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="platTrxNo != null and platTrxNo !=''">
                and PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR}
            </if>
            <if test="launchWay != null">
                and LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT}
            </if>
            <if test="productNo != null and productNo != ''">
                and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
            </if>
            <if test="productName != null and productName != ''">
                and PRODUCT_NAME = #{productName,jdbcType=VARCHAR}
            </if>
            <if test="employerNo != null and employerNo !=''">
                and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="employerName != null and employerName !=''">
                and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
            </if>
            <if test="channelType != null">
                and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
            </if>
            <if test="payChannelNo != null and payChannelNo !=''">
                and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="channelName != null and channelName !=''">
                and CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
            </if>
            <if test="receiveName != null and receiveName !=''">
                and RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR}
            </if>
            <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
                and RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNo != null and receiveIdCardNo !=''">
                and RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
                and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="receiveAccountNo != null and receiveAccountNo !=''">
                and RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 !=''">
                and RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="receivePhoneNo != null and receivePhoneNo !=''">
                and RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR}
            </if>
            <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 !=''">
                and RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="bankName != null and bankName !=''">
                and BANK_NAME = #{bankName,jdbcType=VARCHAR}
            </if>
            <if test="bankCode != null and bankCode !=''">
                and BANK_CODE = #{bankCode,jdbcType=VARCHAR}
            </if>
            <if test="orderItemNetAmount != null">
                and ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL}
            </if>
            <if test="orderItemFee != null">
                and ORDER_ITEM_FEE = #{orderItemFee,jdbcType=DECIMAL}
            </if>
            <if test="orderItemAmount != null">
                and ORDER_ITEM_AMOUNT = #{orderItemAmount,jdbcType=DECIMAL}
            </if>
            <if test="orderItemStatus != null">
                and ORDER_ITEM_STATUS = #{orderItemStatus,jdbcType=SMALLINT}
            </if>
            <if test="employerList != null and employerList.size() > 0">
                and employer_no in
                <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
            </if>
            <if test="exportPlatTrxNos != null and exportPlatTrxNos.size() > 0">
                and PLAT_TRX_NO in
                <foreach collection="exportPlatTrxNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
            </if>
            <if test="orderItemStatusList != null and orderItemStatusList.size() > 0">
                and ORDER_ITEM_STATUS in
                <foreach collection="orderItemStatusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
            </if>
            <if test="errorCode != null and errorCode !=''">
                and ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
            </if>
            <if test="errorDesc != null and errorDesc !=''">
                and ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
            </if>
            <if test="encryptKeyId != null">
                and ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT}
            </if>
            <if test="accessTimes != null">
                and ACCESS_TIMES = #{accessTimes,jdbcType=SMALLINT}
            </if>
            <if test="isPassHangup != null">
                and IS_PASS_HANGUP = #{isPassHangup,jdbcType=BIT}
            </if>
            <if test="hangupApprovalLoginName != null and hangupApprovalLoginName !=''">
                and HANGUP_APPROVAL_LOGIN_NAME = #{hangupApprovalLoginName,jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark !=''">
                and REMARK = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="memo != null and memo !=''">
                and MEMO = #{memo,jdbcType=VARCHAR}
            </if>
            <if test="jobId != null and jobId !=''">
                and JOB_ID = #{jobId,jdbcType=VARCHAR}
            </if>
            <if test="jobName != null and jobName !=''">
                and JOB_NAME = #{jobName,jdbcType=VARCHAR}
            </if>
            <if test="workCategoryCode != null and workCategoryCode !=''">
                and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test="workCategoryName != null and workCategoryName !=''">
                and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
            </if>
            <if test="jsonStr != null and jsonStr !=''">
                and JSON_STR = #{jsonStr,jdbcType=OTHER}
            </if>

            <!--  自定义  -->
            <if test="overId != null and overId !=''">
                and ID > #{overId,jdbcType=BIGINT}
            </if>
            <if test="maxId != null">
                and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
            </if>
            <if test="employerNameLike != null and employerNameLike !=''">
                and EMPLOYER_NAME LIKE concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="createBeginDate != null and createEndDate != null" >
                and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME between #{completeBeginDate,jdbcType=TIMESTAMP} and #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="orderItemNetAmountMin != null and orderItemNetAmountMax != null" >
                and ORDER_ITEM_NET_AMOUNT between #{orderItemNetAmountMin,jdbcType=DECIMAL} and #{orderItemNetAmountMax,jdbcType=DECIMAL}
            </if>
            <if test="createDateList != null and createDateList.size() > 0">
                and CREATE_DATE IN
                <foreach collection="createDateList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=DATE}</foreach>
            </if>

            <if test="platBatchNos != null and platBatchNos.size() > 0">
                and PLAT_BATCH_NO IN
                <foreach collection="platBatchNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
            <if test="isDelete != null " >
                and (is_delete = #{isDelete} or is_delete is null)
            </if>
    </sql>

    <!--  自定义  -->

    <resultMap id="TrxIdMap" type="com.zhixianghui.facade.trade.bo.OrderItemTrxIdBo">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="PLAT_TRX_NO" property="platTrxNo" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listTrxNoAndIdPage" parameterType="java.util.Map" resultMap="TrxIdMap">
        SELECT
            ID,PLAT_TRX_NO
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <if test="sortColumns != null and sortColumns !='' ">
            <![CDATA[ ORDER BY ${sortColumns} ]]>
        </if>
    </select>

    <select id="getCountBo" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.bo.OrderItemCountBo">
        SELECT
            SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN 1 ELSE 0 END ) AS "acceptedCount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_TASK_AMOUNT ELSE 0 END ) AS "acceptedTaskAmount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_NET_AMOUNT ELSE 0 END ) AS "acceptedNetAmount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_FEE ELSE 0 END ) AS "acceptedFee",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_TAX_AMOUNT ELSE 0 END ) AS "acceptedTaxAmount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_AMOUNT ELSE 0 END ) AS "acceptedOrderAmount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN 1 ELSE 0 END ) AS "successCount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN ORDER_ITEM_TASK_AMOUNT ELSE 0 END ) AS "successTaskAmount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN ORDER_ITEM_TAX_AMOUNT ELSE 0 END ) AS "successTaxAmount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN ORDER_ITEM_NET_AMOUNT ELSE 0 END ) AS "successNetAmount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN ORDER_ITEM_FEE ELSE 0 END ) AS "successFee",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 200 OR ORDER_ITEM_STATUS = 700 THEN 1 ELSE 0 END ) AS "failCount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 200 OR ORDER_ITEM_STATUS = 700 THEN ORDER_ITEM_NET_AMOUNT ELSE 0 END ) AS "failNetAmount",
            SUM(CASE WHEN ORDER_ITEM_STATUS = 200 OR ORDER_ITEM_STATUS = 700 THEN ORDER_ITEM_TASK_AMOUNT ELSE 0 END ) AS "failTaskAmount"
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="sumOrderItem" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.bo.OrderItemSumBo">
        SELECT
            COUNT(*) AS "totalNum",
            SUM(ORDER_ITEM_NET_AMOUNT) AS "totalNetAmount",
            SUM(ORDER_ITEM_FEE) AS "totalFee",
            SUM(ORDER_ITEM_NET_AMOUNT+IFNULL(ORDER_ITEM_FEE,0)) AS "totalOrderAmount"
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <update id="cancelOrderItem" parameterType="java.util.Map">
        UPDATE <include refid="table" />
        SET ORDER_ITEM_STATUS = 800,VERSION=VERSION+1
        WHERE PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR} AND ORDER_ITEM_STATUS = 400
        AND CREATE_DATE BETWEEN #{beginDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE};
    </update>


    <update id="updateByPlatBatchNo" parameterType="com.zhixianghui.facade.trade.dto.OrderDeleteDTO">
        update <include refid="table" />
        set is_delete=#{isDelete},UPDATE_TIME=#{updateTime}
        <where>
            PLAT_BATCH_NO=#{platBatchNo}
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
    </update>

    <select id="mapAmountWithMch" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.entity.OrderItem">
        select EMPLOYER_NO as employerNo,sum(ORDER_ITEM_NET_AMOUNT) as orderItemNetAmount from
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        group by employer_no
    </select>

    <select id="sumOrderItemWaitInvoiceAmount" resultType="bigDecimal">
        SELECT  SUM(coalesce(ORDER_ITEM_NET_AMOUNT,0)+coalesce(ORDER_ITEM_TAX_AMOUNT,0))as total
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="sumOrderItemWaitGrantInvoiceAmount" resultType="bigDecimal">
        SELECT  SUM(coalesce(ORDER_ITEM_NET_AMOUNT,0)) as total
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="sumOrderItemWaitServiceFeeInvoiceAmount" resultType="bigDecimal">
        SELECT  SUM(coalesce(ORDER_ITEM_FEE,0)) as total
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="sumCmbOrderAmt" parameterType="java.util.Map" resultType="java.util.Map">
        select
            t.EMPLOYER_NO employerNo,
            t.MAINSTAY_NO mainstayNo,
            sum(t.ORDER_ITEM_NET_AMOUNT) sumNetAmt,
            sum(t.ORDER_ITEM_FEE) sumFee,
            sum(t.ORDER_ITEM_AMOUNT) sumAmt
        from
            tbl_order_item t
        where
            t.CREATE_TIME <![CDATA[ >= ]]> #{createBeginDate}
            and t.CREATE_TIME <![CDATA[ <= ]]> #{createEndDate}
            and t.COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate}
            and t.COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate}
            and t.PAY_CHANNEL_NO = 'CMB'
            and t.ORDER_ITEM_STATUS =100
        group by t.EMPLOYER_NO,t.MAINSTAY_NO
    </select>

    <select id="employerTradeCheck" parameterType="java.util.Map" resultType="java.util.Map">
        select
            MCH_NO as mchNo,
            MCH_NAME as mchName,
            ACTIVE_TIME as activeTime
        from
            merchant.tbl_merchant a
        where
            a.MERCHANT_TYPE = 100
          and a.MCH_STATUS = 100
          and a.ACTIVE_TIME <![CDATA[ < ]]> #{activeTimeEnd,jdbcType=TIMESTAMP}
          and a.MCH_NO not in(
            select
                EMPLOYER_NO
            from
                trade.tbl_order_item t
            where
                t.CREATE_TIME <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
              and t.ORDER_ITEM_STATUS = 100
            group by
                EMPLOYER_NO
        )
    </select>

    <select id="listHasTradeEmp" parameterType="java.util.Map" resultType="java.util.Map">
        select
            EMPLOYER_NO mchNo,EMPLOYER_NAME mchName
        from
            trade.tbl_order_item t
        where
            t.CREATE_TIME <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
          and t.ORDER_ITEM_STATUS = 100
        group by
            EMPLOYER_NO
    </select>

    <select id="selectLastOrderTime" parameterType="java.util.Map" resultType="java.util.Map">
        select
            COMPLETE_TIME lastTime
        from
            trade.tbl_order_item t
        where
          t.EMPLOYER_NO = #{mchNo,jdbcType=VARCHAR}
          and t.ORDER_ITEM_STATUS = 100
        group by
            t.CREATE_TIME desc
        limit 1
    </select>

    <select id="queryUserMonthGrantData" parameterType="java.util.Map" resultType="java.util.Map">
        select
            sum(ifnull(t.ORDER_ITEM_NET_AMOUNT,0)) totalAmount
        from tbl_order_item t
        where
            t.COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate}
          and
            t.COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate}
        and
            t.CREATE_TIME <![CDATA[ >= ]]> #{createBeginDate}
        and
            t.EMPLOYER_NO = #{employerNo}
        and
            t.RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5}
        and
            t.ORDER_ITEM_STATUS =100
        and
            t.MAINSTAY_NO = #{mainstayNo}
    </select>

    <select id="getGrantUserByIdCard" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
            t.RECEIVE_NAME,
            t.ENCRYPT_KEY_ID
        from trade.tbl_order_item t
        where
        t.COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate}
        and
        t.COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate}
        and
        t.CREATE_TIME <![CDATA[ >= ]]> #{createBeginDate}
        and
        t.RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5}
        and
        t.ORDER_ITEM_STATUS =100
        and
        t.MAINSTAY_NO = #{mainstayNo}
        order by t.ID desc
        limit 1
    </select>

    <select id="listGrantOrderBatches" parameterType="java.util.Map" resultType="java.util.Map">
        select
            PLAT_BATCH_NO platBatchNo
        from
            tbl_order_item t
        where
            t.COMPLETE_TIME <![CDATA[ >= ]]> #{startTime}
        and
            t.COMPLETE_TIME <![CDATA[ <= ]]> #{endTime}
        and
            t.ORDER_ITEM_STATUS = 100
        and
            t.MAINSTAY_NO =  #{mainstayNo}
        group by
        PLAT_BATCH_NO
    </select>
</mapper>
