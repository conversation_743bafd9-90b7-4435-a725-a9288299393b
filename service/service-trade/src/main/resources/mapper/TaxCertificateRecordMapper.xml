<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.TaxCertificateRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.TaxCertificateRecord">
    <!--@mbg.generated-->
    <!--@Table tbl_tax_certificate_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="employer_no" jdbcType="VARCHAR" property="employerNo" />
    <result column="employer_name" jdbcType="VARCHAR" property="employerName" />
    <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName" />
    <result column="date_begin" jdbcType="DATE" property="dateBegin" />
    <result column="date_end" jdbcType="DATE" property="dateEnd" />
    <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
    <result column="certificate_file_path" jdbcType="VARCHAR" property="certificateFilePath" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, employer_no, employer_name, mainstay_no, mainstay_name, date_begin, date_end,
    remark, certificate_file_path, create_time, create_by, update_time, update_by
  </sql>

  <sql id="condition_sql">
    <if test="param.id != null">
      and id = #{param.id,jdbcType=BIGINT}
    </if>
    <if test="param.employerNo !=null and param.employerNo != '' ">
      and employer_no = #{param.employerNo,jdbcType=VARCHAR}
    </if>
    <if test="param.employerName !=null and param.employerName != '' ">
      and employer_name like CONCAT('%', CONCAT(#{param.employerName}, '%'))
    </if>
    <if test="param.mainstayNo !=null and param.mainstayNo != '' ">
      and mainstay_no = #{param.mainstayNo,jdbcType=VARCHAR}
    </if>
    <if test="param.mainstayName !=null and param.mainstayName != '' ">
      and mainstay_name like CONCAT('%', CONCAT(#{param.mainstayName}, '%'))
    </if>
    <if test="param.createBeginDate != null and param.createEndDate != null" >
      and create_time between #{param.createBeginDate,jdbcType=TIMESTAMP} and #{param.createEndDate,jdbcType=TIMESTAMP}
    </if>
    <if test="param.dateBegin != null and param.dateEnd != null" >
      and date_begin <![CDATA[ >= ]]> #{param.dateBegin,jdbcType=DATE}
      and date_end <![CDATA[ <= ]]> #{param.dateEnd,jdbcType=DATE}
    </if>
  </sql>

  <select id="list" parameterType="java.util.Map" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from tbl_tax_certificate_record
    <where>
      <include refid="condition_sql">
      </include>
    </where>
    <choose>
      <when test="param.sortColumns != null and param.sortColumns !='' ">
        <![CDATA[ ORDER BY ${param.sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY id DESC ]]>
      </otherwise>
    </choose>
  </select>

  <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from tbl_tax_certificate_record
    <where>
      <include refid="condition_sql">
      </include>
    </where>
    <choose>
      <when test="param.sortColumns != null and param.sortColumns !='' ">
        <![CDATA[ ORDER BY ${param.sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY id DESC ]]>
      </otherwise>
    </choose>
  </select>
</mapper>
