<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.WxIncomeRecord">
    <sql id="table">tbl_wx_income_record</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.WxIncomeRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="sub_mch_id" property="subMchId" jdbcType="VARCHAR"/>
        <result column="mainstay_no" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="mainstay_name" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="income_type" property="incomeType" jdbcType="SMALLINT"/>
        <result column="income_record_id" property="incomeRecordId" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="BIGINT"/>
        <result column="success_time" property="successTime" jdbcType="TIMESTAMP"/>
        <result column="bank_name" property="bankName" jdbcType="VARCHAR"/>
        <result column="bank_account_name" property="bankAccountName" jdbcType="VARCHAR"/>
        <result column="bank_account_number" property="bankAccountNumber" jdbcType="VARCHAR"/>
        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="recharge_remark" property="rechargeRemark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="udpate_time" property="udpateTime" jdbcType="TIMESTAMP"/>
        <result column="updator" property="updator" jdbcType="VARCHAR"/>
        <result column="pay_img_url" property="payImgUrl" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, sub_mch_id, mainstay_no, mainstay_name, income_type, income_record_id, amount, success_time, bank_name, bank_account_name, bank_account_number, mch_no, mch_name, status, recharge_remark, create_time, udpate_time, updator,pay_img_url
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.WxIncomeRecord">
        INSERT INTO <include refid="table" /> (
            version,
            sub_mch_id,
            mainstay_no,
            mainstay_name,
            income_type,
            income_record_id,
            amount,
            success_time,
            bank_name,
            bank_account_name,
            bank_account_number,
            mch_no,
            mch_name,
            status,
            recharge_remark,
            create_time,
            udpate_time,
            updator,
            pay_img_url
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{subMchId,jdbcType=VARCHAR},
            #{mainstayNo,jdbcType=VARCHAR},
            #{mainstayName,jdbcType=VARCHAR},
            #{incomeType,jdbcType=SMALLINT},
            #{incomeRecordId,jdbcType=VARCHAR},
            #{amount,jdbcType=BIGINT},
            #{successTime,jdbcType=TIMESTAMP},
            #{bankName,jdbcType=VARCHAR},
            #{bankAccountName,jdbcType=VARCHAR},
            #{bankAccountNumber,jdbcType=VARCHAR},
            #{mchNo,jdbcType=VARCHAR},
            #{mchName,jdbcType=VARCHAR},
            #{status,jdbcType=SMALLINT},
            #{rechargeRemark,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{udpateTime,jdbcType=TIMESTAMP},
            #{updator,jdbcType=VARCHAR},
            #{payImgUrl,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            version,
            sub_mch_id,
            mainstay_no,
            mainstay_name,
            income_type,
            income_record_id,
            amount,
            success_time,
            bank_name,
            bank_account_name,
            bank_account_number,
            mch_no,
            mch_name,
            status,
            recharge_remark,
            create_time,
            udpate_time,
            updator,
            pay_img_url
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.subMchId,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.incomeType,jdbcType=SMALLINT},
            #{item.incomeRecordId,jdbcType=VARCHAR},
            #{item.amount,jdbcType=BIGINT},
            #{item.successTime,jdbcType=TIMESTAMP},
            #{item.bankName,jdbcType=VARCHAR},
            #{item.bankAccountName,jdbcType=VARCHAR},
            #{item.bankAccountNumber,jdbcType=VARCHAR},
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.mchName,jdbcType=VARCHAR},
            #{item.status,jdbcType=SMALLINT},
            #{item.rechargeRemark,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.udpateTime,jdbcType=TIMESTAMP},
            #{item.updator,jdbcType=VARCHAR},
            #{item.payImgUrl,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.WxIncomeRecord">
        UPDATE <include refid="table" /> SET
            version = #{version,jdbcType=SMALLINT},
            sub_mch_id = #{subMchId,jdbcType=VARCHAR},
            mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            income_type = #{incomeType,jdbcType=SMALLINT},
            income_record_id = #{incomeRecordId,jdbcType=VARCHAR},
            amount = #{amount,jdbcType=BIGINT},
            success_time = #{successTime,jdbcType=TIMESTAMP},
            bank_name = #{bankName,jdbcType=VARCHAR},
            bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
            bank_account_number = #{bankAccountNumber,jdbcType=VARCHAR},
            mch_no = #{mchNo,jdbcType=VARCHAR},
            mch_name = #{mchName,jdbcType=VARCHAR},
            status = #{status,jdbcType=SMALLINT},
            recharge_remark = #{rechargeRemark,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            udpate_time = #{udpateTime,jdbcType=TIMESTAMP},
            updator = #{updator,jdbcType=VARCHAR},
            pay_img_url = #{payImgUrl,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.WxIncomeRecord">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="subMchId != null">
                sub_mch_id = #{subMchId,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="incomeType != null">
                income_type = #{incomeType,jdbcType=SMALLINT},
            </if>
            <if test="incomeRecordId != null">
                income_record_id = #{incomeRecordId,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=BIGINT},
            </if>
            <if test="successTime != null">
                success_time = #{successTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bankName != null">
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankAccountName != null">
                bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
            </if>
            <if test="bankAccountNumber != null">
                bank_account_number = #{bankAccountNumber,jdbcType=VARCHAR},
            </if>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                mch_name = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="rechargeRemark != null">
                recharge_remark = #{rechargeRemark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="udpateTime != null">
                udpate_time = #{udpateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null">
                updator = #{updator,jdbcType=VARCHAR}
            </if>
            <if test="payImgUrl != null">
                pay_img_url = #{payImgUrl,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="maxId != null">
            AND ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="subMchId != null and subMchId !=''">
            and sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and mainstay_name = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="incomeType != null">
            and income_type = #{incomeType,jdbcType=SMALLINT}
        </if>
        <if test="incomeRecordId != null and incomeRecordId !=''">
            and income_record_id = #{incomeRecordId,jdbcType=VARCHAR}
        </if>
        <if test="amount != null">
            and amount = #{amount,jdbcType=BIGINT}
        </if>
        <if test="successTime != null">
            and success_time = #{successTime,jdbcType=TIMESTAMP}
        </if>
        <if test="bankName != null and bankName !=''">
            and bank_name = #{bankName,jdbcType=VARCHAR}
        </if>
        <if test="bankAccountName != null and bankAccountName !=''">
            and bank_account_name = #{bankAccountName,jdbcType=VARCHAR}
        </if>
        <if test="bankAccountNumber != null and bankAccountNumber !=''">
            and bank_account_number = #{bankAccountNumber,jdbcType=VARCHAR}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and mch_no = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="mchName != null and mchName !=''">
            and mch_name = #{mchName,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=SMALLINT}
        </if>
        <if test="rechargeRemark != null and rechargeRemark !=''">
            and recharge_remark = #{rechargeRemark,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="udpateTime != null">
            and udpate_time = #{udpateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updator != null and updator !=''">
            and updator = #{updator,jdbcType=VARCHAR}
        </if>
        <if test="payImgUrl != null and payImgUrl != ''">
            and pay_img_url = #{payImgUrl,jdbcType=VARCHAR}
        </if>
        <if test="mchNameLike != null and mchNameLike != ''">
            and mch_name like concat('%',#{mchNameLike},'%')
        </if>
        <if test="beginDate != null and endDate != null and beginDate != '' and endDate != ''">
            and create_time between #{beginDate} and #{endDate}
        </if>
    </sql>

</mapper>
