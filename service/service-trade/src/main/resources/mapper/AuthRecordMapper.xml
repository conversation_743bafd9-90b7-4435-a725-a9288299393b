<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.AuthRecord">
    <sql id="table">tbl_auth_record</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.AuthRecord">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="AUTH_NO" property="authNo" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="RECEIVE_ID_CARD_NO_MD5" property="receiveIdCardNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO_MD5" property="receiveAccountNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME_MD5" property="receiveNameMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO_MD5" property="receivePhoneNoMd5" jdbcType="VARCHAR"/>
        <result column="AUTH_TYPE" property="authType" jdbcType="SMALLINT"/>
        <result column="PROTOCOL_VERSION" property="protocolVersion" jdbcType="VARCHAR"/>
        <result column="PROTOCOL_NO" property="protocolNo" jdbcType="VARCHAR"/>
        <result column="AUTH_CHANNEL" property="authChannel" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, AUTH_NO, CREATE_TIME, VERSION, RECEIVE_ID_CARD_NO_MD5, RECEIVE_ACCOUNT_NO_MD5, RECEIVE_NAME_MD5, RECEIVE_PHONE_NO_MD5, AUTH_TYPE,PROTOCOL_VERSION,PROTOCOL_NO,AUTH_CHANNEL
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.AuthRecord">
        INSERT INTO <include refid="table" /> (
            AUTH_NO,
            CREATE_TIME,
            VERSION,
            RECEIVE_ID_CARD_NO_MD5,
            RECEIVE_ACCOUNT_NO_MD5,
            RECEIVE_NAME_MD5,
            RECEIVE_PHONE_NO_MD5,
            AUTH_TYPE,
            PROTOCOL_VERSION,
            PROTOCOL_NO,
            AUTH_CHANNEL
        ) VALUES (
            #{authNo,jdbcType=VARCHAR},
            #{createTime,jdbcType=TIMESTAMP},
            #{version,jdbcType=SMALLINT},
            #{receiveIdCardNoMd5,jdbcType=VARCHAR},
            #{receiveAccountNoMd5,jdbcType=VARCHAR},
            #{receiveNameMd5,jdbcType=VARCHAR},
            #{receivePhoneNoMd5,jdbcType=VARCHAR},
            #{authType,jdbcType=SMALLINT},
            #{protocolVersion,jdbcType=VARCHAR},
            #{protocolNo,jdbcType=VARCHAR},
            #{authChannel,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        AUTH_NO,
        CREATE_TIME,
        VERSION,
        RECEIVE_ID_CARD_NO_MD5,
        RECEIVE_ACCOUNT_NO_MD5,
        RECEIVE_NAME_MD5,
        RECEIVE_PHONE_NO_MD5,
        AUTH_TYPE,
        PROTOCOL_VERSION,
        PROTOCOL_NO,
        AUTH_CHANNEL
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.authNo,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.version,jdbcType=SMALLINT},
            #{item.receiveIdCardNoMd5,jdbcType=VARCHAR},
            #{item.receiveAccountNoMd5,jdbcType=VARCHAR},
            #{item.receiveNameMd5,jdbcType=VARCHAR},
            #{item.receivePhoneNoMd5,jdbcType=VARCHAR},
            #{item.authType,jdbcType=SMALLINT},
            #{item.protocolVersion,jdbcType=VARCHAR},
            #{item.protocolNo,jdbcType=VARCHAR},
            #{item.authChannel,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.AuthRecord">
        UPDATE <include refid="table" /> SET
        AUTH_NO = #{authNo,jdbcType=VARCHAR},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        VERSION = #{version,jdbcType=SMALLINT} +1,
        RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
        RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
        RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR},
        RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
        AUTH_TYPE = #{authType,jdbcType=SMALLINT},
        PROTOCOL_VERSION = #{protocolVersion,jdbcType=VARCHAR},
        PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR},
        AUTH_CHANNEL = #{authChannel,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.AuthRecord">
        UPDATE <include refid="table" />
        <set>
            <if test="authNo != null">
                AUTH_NO = #{authNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="receiveIdCardNoMd5 != null">
                RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
            </if>
            <if test="receiveAccountNoMd5 != null">
                RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
            </if>
            <if test="receiveNameMd5 != null">
                RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR},
            </if>
            <if test="receivePhoneNoMd5 != null">
                RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
            </if>
            <if test="authType != null">
                AUTH_TYPE = #{authType,jdbcType=SMALLINT},
            </if>
            <if test="protocolVersion != null">
                PROTOCOL_VERSION = #{protocolVersion,jdbcType=VARCHAR},
            </if>
            <if test="protocolNo != null">
                PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR}
            </if>
            <if test="authChannel != null">
                AUTH_CHANNEL = #{authChannel,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="authNo != null and authNo !=''">
            and AUTH_NO = #{authNo,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
            and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 !=''">
            and RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
            and RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR}
        </if>
        <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 !=''">
            and RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="authType != null">
            and AUTH_TYPE = #{authType,jdbcType=SMALLINT}
        </if>
        <if test="protocolVersion != null">
            and PROTOCOL_VERSION = #{protocolVersion,jdbcType=VARCHAR}
        </if>
        <if test="protocolNo != null">
            and PROTOCOL_NO = #{protocolNo,jdbcType=VARCHAR}
        </if>
        <if test="authChannel != null and authChannel != ''">
            and AUTH_CHANNEL = #{authChannel,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
