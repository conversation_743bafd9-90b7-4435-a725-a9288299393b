<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.RecordItem">
    <sql id="table">tbl_record_item</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.RecordItem">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="DATE"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="COMPLETE_TIME" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="MCH_BATCH_NO" property="mchBatchNo" jdbcType="VARCHAR"/>
        <result column="PLAT_BATCH_NO" property="platBatchNo" jdbcType="VARCHAR"/>
        <result column="MCH_ORDER_NO" property="mchOrderNo" jdbcType="VARCHAR"/>
        <result column="PLAT_TRX_NO" property="platTrxNo" jdbcType="VARCHAR"/>
        <result column="REMIT_PLAT_TRX_NO" property="remitPlatTrxNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TRX_NO" property="channelTrxNo" jdbcType="VARCHAR"/>
        <result column="LAUNCH_WAY" property="launchWay" jdbcType="SMALLINT"/>
        <result column="PRODUCT_NO" property="productNo" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
        <result column="CHANNEL_MCH_NO" property="channelMchNo" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_NAME" property="channelName" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME" property="receiveName" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME_MD5" property="receiveNameMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO" property="receiveIdCardNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO_MD5" property="receiveIdCardNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO" property="receiveAccountNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO_MD5" property="receiveAccountNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO" property="receivePhoneNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO_MD5" property="receivePhoneNoMd5" jdbcType="VARCHAR"/>
        <result column="WORK_CATEGORY_CODE" property="workCategoryCode" jdbcType="VARCHAR"/>
        <result column="WORK_CATEGORY_NAME" property="workCategoryName" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
        <result column="ORDER_TASK_AMOUNT" property="orderTaskAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_TAX_AMOUNT" property="orderTaxAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_NET_AMOUNT" property="orderNetAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_FEE" property="orderFee" jdbcType="DECIMAL"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="PROCESS_STATUS" property="processStatus" jdbcType="SMALLINT"/>
        <result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR"/>
        <result column="ERROR_DESC" property="errorDesc" jdbcType="VARCHAR"/>
        <result column="ENCRYPT_KEY_ID" property="encryptKeyId" jdbcType="BIGINT"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="JSON_STR" property="jsonStr" jdbcType="OTHER"/>
    </resultMap>

    <resultMap id="StatisticsResultMap" type="com.zhixianghui.facade.trade.bo.RecordItemGroupBo">
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO_MD5" property="receiveIdCardNoMd5" jdbcType="VARCHAR"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="DATE"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_DATE, CREATE_TIME, UPDATE_TIME, COMPLETE_TIME, MCH_BATCH_NO, PLAT_BATCH_NO, MCH_ORDER_NO, PLAT_TRX_NO, REMIT_PLAT_TRX_NO, CHANNEL_TRX_NO, LAUNCH_WAY, PRODUCT_NO,PRODUCT_NAME,EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NO, MAINSTAY_NAME, CHANNEL_TYPE, CHANNEL_MCH_NO, PAY_CHANNEL_NO, CHANNEL_NAME, RECEIVE_NAME, RECEIVE_NAME_MD5, RECEIVE_ID_CARD_NO, RECEIVE_ID_CARD_NO_MD5, RECEIVE_ACCOUNT_NO, RECEIVE_ACCOUNT_NO_MD5, RECEIVE_PHONE_NO, RECEIVE_PHONE_NO_MD5,WORK_CATEGORY_CODE,WORK_CATEGORY_NAME, BANK_NAME, BANK_CODE, ORDER_TASK_AMOUNT,ORDER_TAX_AMOUNT,ORDER_NET_AMOUNT, ORDER_FEE, ORDER_AMOUNT, PROCESS_STATUS, ERROR_CODE, ERROR_DESC, ENCRYPT_KEY_ID, REMARK, JSON_STR
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.RecordItem">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_DATE,
            CREATE_TIME,
            UPDATE_TIME,
            COMPLETE_TIME,
            MCH_BATCH_NO,
            PLAT_BATCH_NO,
            MCH_ORDER_NO,
            PLAT_TRX_NO,
            REMIT_PLAT_TRX_NO,
            CHANNEL_TRX_NO,
            LAUNCH_WAY,
            PRODUCT_NO,
            PRODUCT_NAME,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            CHANNEL_TYPE,
            CHANNEL_MCH_NO,
            PAY_CHANNEL_NO,
            CHANNEL_NAME,
            RECEIVE_NAME,
            RECEIVE_NAME_MD5,
            RECEIVE_ID_CARD_NO,
            RECEIVE_ID_CARD_NO_MD5,
            RECEIVE_ACCOUNT_NO,
            RECEIVE_ACCOUNT_NO_MD5,
            RECEIVE_PHONE_NO,
            RECEIVE_PHONE_NO_MD5,
            WORK_CATEGORY_CODE,
            WORK_CATEGORY_NAME,
            BANK_NAME,
            BANK_CODE,
            ORDER_TASK_AMOUNT,
            ORDER_TAX_AMOUNT,
            ORDER_NET_AMOUNT,
            ORDER_FEE,
            ORDER_AMOUNT,
            PROCESS_STATUS,
            ERROR_CODE,
            ERROR_DESC,
            ENCRYPT_KEY_ID,
            REMARK,
            JSON_STR
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{createDate,jdbcType=DATE},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{completeTime,jdbcType=TIMESTAMP},
        #{mchBatchNo,jdbcType=VARCHAR},
        #{platBatchNo,jdbcType=VARCHAR},
        #{mchOrderNo,jdbcType=VARCHAR},
        #{platTrxNo,jdbcType=VARCHAR},
        #{remitPlatTrxNo,jdbcType=VARCHAR},
        #{channelTrxNo,jdbcType=VARCHAR},
        #{launchWay,jdbcType=SMALLINT},
        #{productNo,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{channelType,jdbcType=SMALLINT},
        #{channelMchNo,jdbcType=VARCHAR},
        #{payChannelNo,jdbcType=VARCHAR},
        #{channelName,jdbcType=VARCHAR},
        #{receiveName,jdbcType=VARCHAR},
        #{receiveNameMd5,jdbcType=VARCHAR},
        #{receiveIdCardNo,jdbcType=VARCHAR},
        #{receiveIdCardNoMd5,jdbcType=VARCHAR},
        #{receiveAccountNo,jdbcType=VARCHAR},
        #{receiveAccountNoMd5,jdbcType=VARCHAR},
        #{receivePhoneNo,jdbcType=VARCHAR},
        #{receivePhoneNoMd5,jdbcType=VARCHAR},
        #{workCategoryCode,jdbcType=VARCHAR},
        #{workCategoryName,jdbcType=VARCHAR},
        #{bankName,jdbcType=VARCHAR},
        #{bankCode,jdbcType=VARCHAR},
        #{orderTaskAmount,jdbcType=DECIMAL},
        #{orderTaxAmount,jdbcType=DECIMAL},
        #{orderNetAmount,jdbcType=DECIMAL},
        #{orderFee,jdbcType=DECIMAL},
        #{orderAmount,jdbcType=DECIMAL},
        #{processStatus,jdbcType=SMALLINT},
        #{errorCode,jdbcType=VARCHAR},
        #{errorDesc,jdbcType=VARCHAR},
        #{encryptKeyId,jdbcType=BIGINT},
        #{remark,jdbcType=VARCHAR},
        #{jsonStr,jdbcType=OTHER}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.RecordItem">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=SMALLINT} +1,
                CREATE_DATE = #{createDate,jdbcType=DATE},
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
                MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR},
                PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR},
                MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR},
                PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR},
                REMIT_PLAT_TRX_NO = #{remitPlatTrxNo,jdbcType=VARCHAR},
                CHANNEL_TRX_NO = #{channelTrxNo,jdbcType=VARCHAR},
                LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT},
                PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
                CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
                CHANNEL_MCH_NO = #{channelMchNo,jdbcType=VARCHAR},
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
                CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
                RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
                RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR},
                RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR},
                RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
                RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR},
                RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
                RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR},
                RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
                WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR},
                WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR},
                BANK_NAME = #{bankName,jdbcType=VARCHAR},
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
                ORDER_TASK_AMOUNT = #{orderTaskAmount,jdbcType=DECIMAL},
                ORDER_TAX_AMOUNT = #{orderTaxAmount,jdbcType=DECIMAL},
                ORDER_NET_AMOUNT = #{orderNetAmount,jdbcType=DECIMAL},
                ORDER_FEE = #{orderFee,jdbcType=DECIMAL},
                ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL},
                PROCESS_STATUS = #{processStatus,jdbcType=SMALLINT},
                ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
                ERROR_DESC = #{errorDesc,jdbcType=VARCHAR},
                ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT},
                REMARK = #{remark,jdbcType=VARCHAR},
                JSON_STR = #{jsonStr,jdbcType=OTHER}
        WHERE ID = #{id,jdbcType=BIGINT} and REMIT_PLAT_TRX_NO = #{remitPlatTrxNo,jdbcType=VARCHAR} and VERSION = #{version,jdbcType=BIGINT}  and CREATE_DATE = #{createDate,jdbcType=DATE}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>


    <select id="listByTime" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            <include refid="table" />
        where
            <if test="mchNo != null and mchNo !=''">
        MAINSTAY_NO = #{mchNo,jdbcType=VARCHAR}
            </if>
            <if test="createBeginDate != null and createEndDate != null" >
        and CREATE_TIME between #{createBeginDate,jdbcType=VARCHAR} and #{createEndDate,jdbcType=VARCHAR}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
        and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        and (PROCESS_STATUS = 100 or PROCESS_STATUS = 700)
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <select id="getAmountResult" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        SELECT sum(ORDER_NET_AMOUNT) AS "totalReceiveAmount"
        from <include refid="table" />
        where CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        and UPDATE_TIME BETWEEN #{updateBeginTime,jdbcType=TIMESTAMP} and #{updateEndTime,jdbcType=TIMESTAMP}
        and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        and PROCESS_STATUS in (100,300)
    </select>

    <!--此sql为统计每个月有多少idcardno_md5 + mainstay组合的统计，需配合RECEIVE_ID_CARD_NO_MD5,MAINSTAY_NO的索引，且查从库-->
    <select id="getIdcardNoMainstayGroup" parameterType="java.util.Map" resultMap="StatisticsResultMap">
        select RECEIVE_ID_CARD_NO_MD5,MAINSTAY_NO from tbl_record_item
        where CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        and CREATE_TIME between #{beginDateTime,jdbcType=TIMESTAMP} and #{endDateTime,jdbcType=TIMESTAMP}
        group by RECEIVE_ID_CARD_NO_MD5,MAINSTAY_NO
    </select>

    <!--发放统计-->
    <select id="coreIndexStatistics" parameterType="java.util.Map" resultType="java.util.Map">
        select
        count(ID) orderCount,
        sum(ORDER_NET_AMOUNT) as totalAmount,
        avg(ORDER_NET_AMOUNT) as avgAmount
        from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
            <if test="mch_nos !=null">
                and EMPLOYER_NO in
                <foreach collection="mch_nos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="coreIndexReceivedUserCount" parameterType="java.util.Map" resultType="java.util.Map">
        select
        count(distinct RECEIVE_ID_CARD_NO_MD5) as receivedUserCount
        from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
            <if test="mch_nos !=null">
                and EMPLOYER_NO in
                <foreach collection="mch_nos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="coreIndexDailyDetail" parameterType="java.util.Map" resultType="java.util.Map">
        select
        date_format(COMPLETE_TIME,'%Y-%m-%d') as date,
        count(ID) orderCount,
        avg(ORDER_NET_AMOUNT) as avgAmount,
        sum(ORDER_NET_AMOUNT) as totalAmount
        from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
            <if test="mch_nos !=null">
                and EMPLOYER_NO in
                <foreach collection="mch_nos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by date
    </select>

    <select id="coreIndexDailyDetailReceivedUserCount" parameterType="java.util.Map" resultType="java.util.Map">
        select
        date_format(COMPLETE_TIME,'%Y-%m-%d') as date,
        count(distinct RECEIVE_ID_CARD_NO_MD5) as receivedUserCount
        from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
            <if test="mch_nos !=null">
                and EMPLOYER_NO in
                <foreach collection="mch_nos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by date
    </select>

    <select id="coreIndexDetailMonthly" parameterType="java.util.Map" resultType="java.util.Map">
        select
        date_format(COMPLETE_TIME,'%Y-%m') as date,
        count(ID) orderCount,
        avg(ORDER_NET_AMOUNT) as avgAmount,
        sum(ORDER_NET_AMOUNT) as totalAmount
        from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
            <if test="mch_nos !=null">
                and EMPLOYER_NO in
                <foreach collection="mch_nos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by date
    </select>

    <select id="coreIndexDetailMonthlyReceivedUserCount" parameterType="java.util.Map" resultType="java.util.Map">
        select
        date_format(COMPLETE_TIME,'%Y-%m') as date,
        count(distinct RECEIVE_ID_CARD_NO_MD5) as receivedUserCount
        from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
        group by date
    </select>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=SMALLINT}
            </if>
            <if test="createDate != null">
                and CREATE_DATE = #{createDate,jdbcType=DATE}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="completeTime != null">
                and COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP}
            </if>
            <if test="mchBatchNo != null and mchBatchNo !=''">
                and MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="platBatchNo != null and platBatchNo !=''">
                and PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="mchOrderNo != null and mchOrderNo !=''">
                and MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="platTrxNo != null and platTrxNo !=''">
                and PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR}
            </if>
            <if test="remitPlatTrxNo != null and remitPlatTrxNo !=''">
                and REMIT_PLAT_TRX_NO = #{remitPlatTrxNo,jdbcType=VARCHAR}
            </if>
            <if test="channelTrxNo != null and channelTrxNo !=''">
                and CHANNEL_TRX_NO = #{channelTrxNo,jdbcType=VARCHAR}
            </if>
            <if test="launchWay != null">
                and LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT}
            </if>
            <if test="productNo != null and productNo != ''">
                and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
            </if>
            <if test="productName != null and productName != ''">
                and PRODUCT_NAME = #{productName,jdbcType=VARCHAR}
            </if>
            <if test="employerNo != null and employerNo !=''">
                and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="employerName != null and employerName !=''">
                and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
            </if>
            <if test="channelType != null">
                and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
            </if>
            <if test="channelMchNo != null and channelMchNo !=''">
                and CHANNEL_MCH_NO = #{channelMchNo,jdbcType=VARCHAR}
            </if>
            <if test="payChannelNo != null and payChannelNo !=''">
                and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="channelName != null and channelName !=''">
                and CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
            </if>
            <if test="receiveName != null and receiveName !=''">
                and RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR}
            </if>
            <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
                and RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNo != null and receiveIdCardNo !=''">
                and RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
                and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="receiveAccountNo != null and receiveAccountNo !=''">
                and RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 !=''">
                and RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="receivePhoneNo != null and receivePhoneNo !=''">
                and RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR}
            </if>
            <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 !=''">
                and RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="workCategoryCode != null and workCategoryCode != ''">
                and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test="workCategoryName != null and workCategoryName != ''">
                and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
            </if>
            <if test="bankName != null and bankName !=''">
                and BANK_NAME = #{bankName,jdbcType=VARCHAR}
            </if>
            <if test="bankCode != null and bankCode !=''">
                and BANK_CODE = #{bankCode,jdbcType=VARCHAR}
            </if>
            <if test="orderNetAmount != null">
                and ORDER_NET_AMOUNT = #{orderNetAmount,jdbcType=DECIMAL}
            </if>
            <if test="orderFee != null">
                and ORDER_FEE = #{orderFee,jdbcType=DECIMAL}
            </if>
            <if test="orderAmount != null">
                and ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL}
            </if>
            <if test="processStatus != null">
                and PROCESS_STATUS = #{processStatus,jdbcType=SMALLINT}
            </if>
            <if test="processStatusList != null and processStatusList.size() > 0">
                AND PROCESS_STATUS IN
                <foreach collection="processStatusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
            </if>
            <if test="errorCode != null and errorCode !=''">
                and ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
            </if>
            <if test="errorDesc != null and errorDesc !=''">
                and ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
            </if>
            <if test="encryptKeyId != null">
                and ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT}
            </if>
            <if test="remark != null and remark !=''">
                and REMARK = #{remark,jdbcType=VARCHAR}
            </if>
            <if test="jsonStr != null and jsonStr !=''">
                and JSON_STR = #{jsonStr,jdbcType=OTHER}
            </if>

            <!--  自定义  -->
            <if test="maxId != null">
                and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
            </if>
            <if test="employerNameLike != null and employerNameLike !='' ">
                AND EMPLOYER_NAME LIKE CONCAT('%', CONCAT(#{employerNameLike}, '%'))
            </if>
            <if test="detailTrxSeq != null and detailTrxSeq !='' ">
                AND REMIT_PLAT_TRX_NO LIKE CONCAT('%', CONCAT(#{detailTrxSeq}, '%'))
            </if>
            <if test="createBeginDate != null and createEndDate != null" >
                and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME between #{completeBeginDate,jdbcType=TIMESTAMP} and #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="createDateList != null and createDateList.size() > 0">
                and CREATE_DATE IN
                <foreach collection="createDateList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=DATE}</foreach>
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
    </sql>


    <select id="selectOrderCount" resultType="java.lang.Integer">
            select count(0) from
            tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
    </select>

    <select id="selectMchCount" resultType="java.lang.Integer">
        select count(EMPLOYER_NO) from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
        group by EMPLOYER_NO

    </select>

    <select id="selectUserCount" resultType="java.lang.Integer">
        select count(RECEIVE_ID_CARD_NO_MD5) as countNo from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
        group by RECEIVE_ID_CARD_NO_MD5

    </select>

    <select id="selectOrderSum" resultType="java.math.BigDecimal">
        select sum(ORDER_NET_AMOUNT) from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
    </select>

    <select id="selectOrderPrice" resultType="com.zhixianghui.facade.trade.vo.statistics.MonthOrderVo">
        select DATE_FORMAT(COMPLETE_TIME,'%Y-%m-%d') as `date`,sum(ORDER_NET_AMOUNT) price from tbl_record_item
        <where>
            PROCESS_STATUS = 100
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME <![CDATA[ >= ]]> #{completeBeginDate,jdbcType=TIMESTAMP}
                and COMPLETE_TIME <![CDATA[ <= ]]> #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
        group by DATE_FORMAT(COMPLETE_TIME,'%Y-%m-%d') ORDER BY COMPLETE_TIME
    </select>

<!--    <select id="getListWorkCategoryGroup" resultType="java.util.Map" resultMap="serviceConfirmMap">-->
<!--        select * from tbl_record_item-->
<!--        GROUP BY EMPLOYER_NO,MAINSTAY_NO,WORK_CATEGORY_CODE-->
<!--    </select>-->

    <resultMap id="serviceConfirmMap" type="com.zhixianghui.facade.trade.vo.ServiceConfirmVo">
        <result column="employer_no" property="employerNo" jdbcType="VARCHAR"/>
        <result column="mainstay_no" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="work_category_code" property="workCategoryCode" jdbcType="VARCHAR"/>
        <result column="work_category_name" property="workCategoryName" jdbcType="VARCHAR"/>
        <result column="countNum" property="countNum" jdbcType="INTEGER"/>
        <result column="totalAmount" property="totalAmount" jdbcType="DECIMAL"/>
    </resultMap>

    <select id="getWorkCategoryCode" resultMap="serviceConfirmMap">
        select employer_no,mainstay_no,work_category_code,work_category_name,count(0) as countNum,
        sum(order_net_amount) as totalAmount from tbl_record_item
        <where>
            PROCESS_STATUS IN (100,300) and work_category_code is not null
            <if test="mchNo != null and mchNo != ''">
                and EMPLOYER_NO = #{mchNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNo != null and mainstayNo != ''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME between #{completeBeginDate,jdbcType=TIMESTAMP} and #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
         GROUP BY EMPLOYER_NO,MAINSTAY_NO,WORK_CATEGORY_CODE
    </select>

    <select id="getWorkCategoryCodeCount" resultType="String">
        select EMPLOYER_NO from tbl_record_item
        <where>
            PROCESS_STATUS IN (100,300) and work_category_code is not null
            <if test="mchNo != null and mchNo != ''">
                and EMPLOYER_NO = #{mchNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNo != null and mainstayNo != ''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME between #{completeBeginDate,jdbcType=TIMESTAMP} and #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
        </where>
        GROUP BY EMPLOYER_NO,MAINSTAY_NO,WORK_CATEGORY_CODE
    </select>

    <select id="getAmountResultGroup" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.dto.RiskControlAmountDto">
        SELECT mainstay_no as mainstayNo,sum(ORDER_NET_AMOUNT) as amount
        from <include refid="table" />
        where CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        and CREATE_TIME between #{beginDateTime,jdbcType=TIMESTAMP} and #{endDateTime,jdbcType=TIMESTAMP}
        and UPDATE_TIME BETWEEN #{updateBeginTime,jdbcType=TIMESTAMP} and #{updateEndTime,jdbcType=TIMESTAMP}
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 != ''">
            and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="employerNo != null and employerNo != ''">
            and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        and PROCESS_STATUS in (100,300)
        group by mainstay_no
    </select>

    <select id="getCountTradeTimes" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.dto.RiskControlNumDto">
        SELECT mainstay_no as mainstayNo,count(1) as countNum
        from <include refid="table" />
        where CREATE_TIME between #{beginDateTime,jdbcType=TIMESTAMP} and #{endDateTime,jdbcType=TIMESTAMP}
        <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 != ''">
            and RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="receiveNameMd5 != null and receiveNameMd5 != ''">
            and RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR}
        </if>
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 != ''">
            and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="employerNo != null and employerNo != ''">
            and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="orderNetAmount != null">
            and ORDER_NET_AMOUNT = #{orderNetAmount,jdbcType=DECIMAL}
        </if>
        <if test="platTrxNo != null and platTrxNo != ''">
            and PLAT_TRX_NO != #{platTrxNo,jdbcType=VARCHAR}
        </if>
        and PROCESS_STATUS in (100,300)
        group by mainstay_no
    </select>

    <select id="sumGrantSuccessGroupMonth" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.vo.UserGrantVo">
        SELECT
            CONCAT(YEAR(COMPLETE_TIME),MONTH(COMPLETE_TIME)) as mapKey,
            sum(ORDER_NET_AMOUNT) as totalAmount
        FROM
            tbl_record_item
        WHERE
            RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        AND PROCESS_STATUS = #{processStatus,jdbcType=SMALLINT}
        AND COMPLETE_TIME between #{rangeDayStart,jdbcType=TIMESTAMP} and #{rangeDayEnd,jdbcType=TIMESTAMP}
        AND CREATE_TIME between #{beginCreateTime,jdbcType=TIMESTAMP} and #{endCreateTime,jdbcType=TIMESTAMP}
        GROUP BY
            YEAR(COMPLETE_TIME),MONTH (COMPLETE_TIME)
        ORDER BY
        COMPLETE_TIME desc
    </select>

    <select id="getWaiteGrantSumAmount" parameterType="java.util.Map" resultType="java.util.Map">
        select
            sum(IFNULL(ORDER_NET_AMOUNT,0)) sumNetAmount,
            sum(IFNULL(ORDER_FEE,0)) sumFee,
            sum(IFNULL(ORDER_AMOUNT,0)) sumAmount
        from
            tbl_record_item t
        where
            t.PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
          and t.PROCESS_STATUS = #{processStatus,jdbcType=VARCHAR}
    </select>

    <resultMap id="grantSuccessMap" type="com.zhixianghui.facade.trade.vo.UserGrantVo">
        <id column="mapKey" property="mapKey"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="createTime" property="createTime"/>
        <collection property="detail" ofType="com.zhixianghui.facade.trade.vo.UserGrantDetailVo">
            <result column="employerName" property="employerName"/>
            <result column="channelType" property="channelType"/>
            <result column="remitPlatTrxNo" property="remitPlatTrxNo"/>
            <result column="completeTime" property="completeTime"/>
            <result column="orderNetAmount" property="orderNetAmount"/>
        </collection>
    </resultMap>

    <select id="listGrantSuccessByIdCard" parameterType="java.util.Map" resultMap="grantSuccessMap">
        SELECT
            CONCAT(YEAR(COMPLETE_TIME),MONTH(COMPLETE_TIME)) as mapKey,
            YEAR(COMPLETE_TIME) as year,MONTH(COMPLETE_TIME) as month,
            EMPLOYER_NAME as employerName,
            CHANNEL_TYPE as channelType,
            REMIT_PLAT_TRX_NO as remitPlatTrxNo,
            COMPLETE_TIME as completeTime,
            ORDER_NET_AMOUNT as orderNetAmount,
            CREATE_TIME as createTime
        FROM
            tbl_record_item
        WHERE
            RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        AND PROCESS_STATUS = #{processStatus,jdbcType=SMALLINT}
        AND COMPLETE_TIME between #{rangeDayStart,jdbcType=TIMESTAMP} and #{rangeDayEnd,jdbcType=TIMESTAMP}
        AND CREATE_TIME between #{beginCreateTime,jdbcType=TIMESTAMP} and #{endCreateTime,jdbcType=TIMESTAMP}
        ORDER BY
        create_time desc
    </select>

    <select id="getUserGrantSumAmount" parameterType="java.util.Map" resultType="java.math.BigDecimal">
        select IFNULL(SUM(ORDER_NET_AMOUNT),0)
        FROM
            tbl_record_item
        WHERE
            RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        AND PROCESS_STATUS = #{processStatus,jdbcType=SMALLINT}
        AND COMPLETE_TIME between #{rangeDayStart,jdbcType=TIMESTAMP} and #{rangeDayEnd,jdbcType=TIMESTAMP}
        AND CREATE_TIME between #{beginCreateTime,jdbcType=TIMESTAMP} and #{endCreateTime,jdbcType=TIMESTAMP}
    </select>
</mapper>
