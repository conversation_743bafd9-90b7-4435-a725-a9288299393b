<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.AcRechargeAccount">

    <sql id="table"> tbl_ac_recharge_account </sql>

    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.AcRechargeAccount">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="mch_no" jdbcType="VARCHAR" property="mchNo"/>
        <result column="mch_name" jdbcType="VARCHAR" property="mchName"/>
        <result column="pay_channel_no" jdbcType="VARCHAR" property="payChannelNo"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo"/>
        <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName"/>
        <result column="account_name" jdbcType="VARCHAR" property="accountName"/>
        <result column="account_no" jdbcType="VARCHAR" property="accountNo"/>
        <result column="account_bank" jdbcType="VARCHAR" property="accountBank"/>
        <result column="account_loc" jdbcType="VARCHAR" property="accountLoc"/>
        <result column="account_banch" jdbcType="VARCHAR" property="accountBanch"/>
        <result column="account_bkno" jdbcType="VARCHAR" property="accountBkno"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, create_time, mch_no, mch_name, pay_channel_no, pay_channel_name, mainstay_no,
        mainstay_name, account_name, account_no, account_bank, account_loc, account_banch,
        account_bkno, state
    </sql>
    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="table"/>
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from <include refid="table" />
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert"
            useGeneratedKeys="true" keyProperty="id"
            parameterType="com.zhixianghui.facade.trade.entity.AcRechargeAccount">
        insert into <include refid="table" />
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="mchNo != null">
                mch_no,
            </if>
            <if test="mchName != null">
                mch_name,
            </if>
            <if test="payChannelNo != null">
                pay_channel_no,
            </if>
            <if test="payChannelName != null">
                pay_channel_name,
            </if>
            <if test="mainstayNo != null">
                mainstay_no,
            </if>
            <if test="mainstayName != null">
                mainstay_name,
            </if>
            <if test="accountName != null">
                account_name,
            </if>
            <if test="accountNo != null">
                account_no,
            </if>
            <if test="accountBank != null">
                account_bank,
            </if>
            <if test="accountLoc != null">
                account_loc,
            </if>
            <if test="accountBanch != null">
                account_banch,
            </if>
            <if test="accountBkno != null">
                account_bkno,
            </if>
            <if test="state != null">
                state,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mchNo != null">
                #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelNo != null">
                #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null">
                #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null">
                #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="accountBank != null">
                #{accountBank,jdbcType=VARCHAR},
            </if>
            <if test="accountLoc != null">
                #{accountLoc,jdbcType=VARCHAR},
            </if>
            <if test="accountBanch != null">
                #{accountBanch,jdbcType=VARCHAR},
            </if>
            <if test="accountBkno != null">
                #{accountBkno,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.AcRechargeAccount">
        update <include refid="table" />
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                mch_name = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelNo != null">
                pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="accountName != null">
                account_name = #{accountName,jdbcType=VARCHAR},
            </if>
            <if test="accountNo != null">
                account_no = #{accountNo,jdbcType=VARCHAR},
            </if>
            <if test="accountBank != null">
                account_bank = #{accountBank,jdbcType=VARCHAR},
            </if>
            <if test="accountLoc != null">
                account_loc = #{accountLoc,jdbcType=VARCHAR},
            </if>
            <if test="accountBanch != null">
                account_banch = #{accountBanch,jdbcType=VARCHAR},
            </if>
            <if test="accountBkno != null">
                account_bkno = #{accountBkno,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.AcRechargeAccount">
        update <include refid="table" />
        set create_time      = #{createTime,jdbcType=TIMESTAMP},
            mch_no           = #{mchNo,jdbcType=VARCHAR},
            mch_name         = #{mchName,jdbcType=VARCHAR},
            pay_channel_no   = #{payChannelNo,jdbcType=VARCHAR},
            pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
            mainstay_no      = #{mainstayNo,jdbcType=VARCHAR},
            mainstay_name    = #{mainstayName,jdbcType=VARCHAR},
            account_name     = #{accountName,jdbcType=VARCHAR},
            account_no       = #{accountNo,jdbcType=VARCHAR},
            account_bank     = #{accountBank,jdbcType=VARCHAR},
            account_loc      = #{accountLoc,jdbcType=VARCHAR},
            account_banch    = #{accountBanch,jdbcType=VARCHAR},
            account_bkno     = #{accountBkno,jdbcType=VARCHAR},
            state            = #{state,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>



    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
         <include refid="table"/>
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
         <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM  <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <sql id="condition_sql">
        <if test="mchNo != null and mchNo != ''"> and mch_no = #{mchNo} </if>
        <if test="mainstayNo != null and mainstayNo != ''"> and mainstay_no = #{mainstayNo} </if>
        <if test="status != null"> and status = #{status} </if>
        <if test="payChannelNo != null and payChannelNo != ''"> and pay_channel_no = #{payChannelNo} </if>
    </sql>

</mapper>