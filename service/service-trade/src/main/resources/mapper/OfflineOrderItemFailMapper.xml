<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.OfflineOrderItemFailMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.OfflineOrderItemFail">
    <!--@mbg.generated-->
    <!--@Table tbl_offline_order_item_fail-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="PLAT_BATCH_NO" jdbcType="VARCHAR" property="platBatchNo" />
    <result column="LINE" jdbcType="INTEGER" property="line" />
    <result column="RECEIVE_ACCOUNT_NO" jdbcType="VARCHAR" property="receiveAccountNo" />
    <result column="RECEIVE_NAME" jdbcType="VARCHAR" property="receiveName" />
    <result column="RECEIVE_ID_CARD_NO" jdbcType="VARCHAR" property="receiveIdCardNo" />
    <result column="RECEIVE_PHONE_NO" jdbcType="VARCHAR" property="receivePhoneNo" />
    <result column="ERROR_DESC" jdbcType="LONGVARCHAR" property="errorDesc" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="VERSION" jdbcType="SMALLINT" property="version" />
    <result column="RECEIVE_NAME_MD5" jdbcType="VARCHAR" property="receiveNameMd5" />
    <result column="RECEIVE_ID_CARD_NO_MD5" jdbcType="VARCHAR" property="receiveIdCardNoMd5" />
    <result column="RECEIVE_ACCOUNT_NO_MD5" jdbcType="VARCHAR" property="receiveAccountNoMd5" />
    <result column="RECEIVE_PHONE_NO_MD5" jdbcType="VARCHAR" property="receivePhoneNoMd5" />
    <result column="ENCRYPT_KEY_ID" jdbcType="BIGINT" property="encryptKeyId" />
    <result column="MCH_ORDER_NO" jdbcType="LONGVARCHAR" property="mchOrderNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PLAT_BATCH_NO, LINE, RECEIVE_ACCOUNT_NO, RECEIVE_NAME, RECEIVE_ID_CARD_NO, RECEIVE_PHONE_NO,
    ERROR_DESC, CREATE_TIME, UPDATE_TIME, VERSION, RECEIVE_NAME_MD5, RECEIVE_ID_CARD_NO_MD5,
    RECEIVE_ACCOUNT_NO_MD5, RECEIVE_PHONE_NO_MD5, ENCRYPT_KEY_ID, MCH_ORDER_NO
  </sql>
</mapper>
