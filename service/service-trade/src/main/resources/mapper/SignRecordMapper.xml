<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.SignRecord">
    <sql id="table">tbl_sign_record</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.SignRecord">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="USER_ID" property="userId" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME" property="receiveName" jdbcType="VARCHAR"/>
        <result column="RECEIVE_NAME_MD5" property="receiveNameMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO" property="receiveIdCardNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO_MD5" property="receiveIdCardNoMd5" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO" property="receivePhoneNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO_MD5" property="receivePhoneNoMd5" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="INFO_STATUS" property="infoStatus" jdbcType="SMALLINT"/>
        <result column="SIGN_STATUS" property="signStatus" jdbcType="SMALLINT"/>
        <result column="ERR_CODE" property="errCode" jdbcType="VARCHAR"/>
        <result column="ERR_MSG" property="errMsg" jdbcType="VARCHAR"/>
        <result column="ACCOUNT_ID" property="accountId" jdbcType="VARCHAR"/>
        <result column="FILE_ID" property="fileId" jdbcType="VARCHAR"/>
        <result column="FLOW_ID" property="flowId" jdbcType="VARCHAR"/>
        <result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR"/>
        <result column="SIGN_TYPE" property="signType" jdbcType="SMALLINT"/>
        <result column="REDIRECT_URL" property="redirectUrl" jdbcType="VARCHAR"/>
        <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR"/>
        <result column="ENCRYPT_KEY_ID" property="encryptKeyId" jdbcType="BIGINT"/>
        <result column="JSON_STR" property="jsonStr" jdbcType="OTHER"/>
        <result column="OPERATE_STATUS" property="operateStatus" jdbcType="SMALLINT"/>
        <result column="OPERATE_MSG" property="operateMsg" jdbcType="VARCHAR"/>
        <result column="SMS_SEND_FREQUENCY" property="smsSendFrequency" jdbcType="INTEGER"/>
        <result column="SMS_SEND_TIME" property="smsSendTime" jdbcType="TIMESTAMP"/>
        <result column="ID_CARD_BACK_URL" property="idCardBackUrl" jdbcType="VARCHAR"/>
        <result column="ID_CARD_FRONT_URL" property="idCardFrontUrl" jdbcType="VARCHAR"/>
        <result column="PERSONAL_SIGNATURE" property="personalSignature" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO" property="receiveAccountNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ACCOUNT_NO_MD5" property="receiveAccountNoMd5" jdbcType="VARCHAR"/>
        <result column="CER_FACE_URL" property="cerFaceUrl" jdbcType="VARCHAR"/>
        <result column="ID_CARD_TYPE" property="idCardType" jdbcType="INTEGER"/>
        <result column="ID_CARD_COPY_URL" property="idCardCopyUrl" jdbcType="VARCHAR"/>
        <result column="SIGN_URL" property="signUrl" jdbcType="VARCHAR"/>
        <result column="SIGNER_TYPE" property="signerType" jdbcType="SMALLINT"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, USER_ID, RECEIVE_NAME, RECEIVE_NAME_MD5,
        RECEIVE_ID_CARD_NO, RECEIVE_ID_CARD_NO_MD5, RECEIVE_PHONE_NO,
        RECEIVE_PHONE_NO_MD5, EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NO, MAINSTAY_NAME,
        INFO_STATUS, SIGN_STATUS, ERR_CODE, ERR_MSG, ACCOUNT_ID, FILE_ID, FLOW_ID,
        FILE_URL, SIGN_TYPE, REDIRECT_URL, NOTIFY_URL, ENCRYPT_KEY_ID, JSON_STR,
        OPERATE_STATUS, OPERATE_MSG, SMS_SEND_FREQUENCY, SMS_SEND_TIME, ID_CARD_BACK_URL,
        ID_CARD_FRONT_URL,PERSONAL_SIGNATURE,RECEIVE_ACCOUNT_NO,RECEIVE_ACCOUNT_NO_MD5,
        CER_FACE_URL,ID_CARD_TYPE,ID_CARD_COPY_URL,SIGN_URL,SIGNER_TYPE
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.SignRecord">
        INSERT INTO <include refid="table" /> (
        VERSION,
        CREATE_TIME,
        UPDATE_TIME,
        USER_ID,
        RECEIVE_NAME,
        RECEIVE_NAME_MD5,
        RECEIVE_ID_CARD_NO,
        RECEIVE_ID_CARD_NO_MD5,
        RECEIVE_PHONE_NO,
        RECEIVE_PHONE_NO_MD5,
        EMPLOYER_NO,
        EMPLOYER_NAME,
        MAINSTAY_NO,
        MAINSTAY_NAME,
        INFO_STATUS,
        SIGN_STATUS,
        ERR_CODE,
        ERR_MSG,
        ACCOUNT_ID,
        FILE_ID,
        FLOW_ID,
        FILE_URL,
        SIGN_TYPE,
        REDIRECT_URL,
        NOTIFY_URL,
        ENCRYPT_KEY_ID,
        JSON_STR,
        OPERATE_STATUS,
        OPERATE_MSG,
        SMS_SEND_FREQUENCY,
        SMS_SEND_TIME,
        ID_CARD_BACK_URL,
        ID_CARD_FRONT_URL,
        PERSONAL_SIGNATURE,
        RECEIVE_ACCOUNT_NO,
        RECEIVE_ACCOUNT_NO_MD5,
        CER_FACE_URL,
        ID_CARD_TYPE,
        ID_CARD_COPY_URL,
        SIGN_URL,
        SIGNER_TYPE
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{userId,jdbcType=VARCHAR},
        #{receiveName,jdbcType=VARCHAR},
        #{receiveNameMd5,jdbcType=VARCHAR},
        #{receiveIdCardNo,jdbcType=VARCHAR},
        #{receiveIdCardNoMd5,jdbcType=VARCHAR},
        #{receivePhoneNo,jdbcType=VARCHAR},
        #{receivePhoneNoMd5,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{infoStatus,jdbcType=SMALLINT},
        #{signStatus,jdbcType=SMALLINT},
        #{errCode,jdbcType=VARCHAR},
        #{errMsg,jdbcType=VARCHAR},
        #{accountId,jdbcType=VARCHAR},
        #{fileId,jdbcType=VARCHAR},
        #{flowId,jdbcType=VARCHAR},
        #{fileUrl,jdbcType=VARCHAR},
        #{signType,jdbcType=SMALLINT},
        #{redirectUrl,jdbcType=VARCHAR},
        #{notifyUrl,jdbcType=VARCHAR},
        #{encryptKeyId,jdbcType=BIGINT},
        #{jsonStr,jdbcType=OTHER},
        #{operateStatus,jdbcType=SMALLINT},
        #{operateMsg,jdbcType=VARCHAR},
        #{smsSendFrequency,jdbcType=INTEGER},
        #{smsSendTime,jdbcType=TIMESTAMP},
        #{idCardBackUrl,jdbcType=VARCHAR},
        #{idCardFrontUrl,jdbcType=VARCHAR},
        #{personalSignature,jdbcType=VARCHAR},
        #{receiveAccountNo,jdbcType=VARCHAR},
        #{receiveAccountNoMd5,jdbcType=VARCHAR},
        #{cerFaceUrl,jdbcType=VARCHAR},
        #{idCardType,jdbcType=VARCHAR},
        #{idCardCopyUrl,jdbcType=VARCHAR},
        #{signUrl,jdbcType=VARCHAR},
        #{signerType,jdbcType=SMALLINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        CREATE_TIME,
        UPDATE_TIME,
        USER_ID,
        RECEIVE_NAME,
        RECEIVE_NAME_MD5,
        RECEIVE_ID_CARD_NO,
        RECEIVE_ID_CARD_NO_MD5,
        RECEIVE_PHONE_NO,
        RECEIVE_PHONE_NO_MD5,
        EMPLOYER_NO,
        EMPLOYER_NAME,
        MAINSTAY_NO,
        MAINSTAY_NAME,
        INFO_STATUS,
        SIGN_STATUS,
        ERR_CODE,
        ERR_MSG,
        ACCOUNT_ID,
        FILE_ID,
        FLOW_ID,
        FILE_URL,
        SIGN_TYPE,
        REDIRECT_URL,
        NOTIFY_URL,
        ENCRYPT_KEY_ID,
        JSON_STR,
        OPERATE_STATUS,
        OPERATE_MSG,
        SMS_SEND_FREQUENCY,
        SMS_SEND_TIME,
        ID_CARD_BACK_URL,
        ID_CARD_FRONT_URL,
        PERSONAL_SIGNATURE,
        RECEIVE_ACCOUNT_NO,
        RECEIVE_ACCOUNT_NO_MD5,
        CER_FACE_URL,
        ID_CARD_TYPE,
        ID_CARD_COPY_URL,
        SIGN_URL,
        SIGNER_TYPE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.version,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.userId,jdbcType=VARCHAR},
            #{item.receiveName,jdbcType=VARCHAR},
            #{item.receiveNameMd5,jdbcType=VARCHAR},
            #{item.receiveIdCardNo,jdbcType=VARCHAR},
            #{item.receiveIdCardNoMd5,jdbcType=VARCHAR},
            #{item.receivePhoneNo,jdbcType=VARCHAR},
            #{item.receivePhoneNoMd5,jdbcType=VARCHAR},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.infoStatus,jdbcType=SMALLINT},
            #{item.signStatus,jdbcType=SMALLINT},
            #{item.errCode,jdbcType=VARCHAR},
            #{item.errMsg,jdbcType=VARCHAR},
            #{item.accountId,jdbcType=VARCHAR},
            #{item.fileId,jdbcType=VARCHAR},
            #{item.flowId,jdbcType=VARCHAR},
            #{item.fileUrl,jdbcType=VARCHAR},
            #{item.signType,jdbcType=SMALLINT},
            #{item.redirectUrl,jdbcType=VARCHAR},
            #{item.notifyUrl,jdbcType=VARCHAR},
            #{item.encryptKeyId,jdbcType=BIGINT},
            #{item.jsonStr,jdbcType=OTHER},
            #{item.operateStatus,jdbcType=SMALLINT},
            #{item.operateMsg,jdbcType=VARCHAR},
            #{item.smsSendFrequency,jdbcType=INTEGER},
            #{item.smsSendTime,jdbcType=TIMESTAMP},
            #{item.idCardBackUrl,jdbcType=VARCHAR},
            #{item.idCardFrontUrl,jdbcType=VARCHAR},
            #{item.personalSignature,jdbcType=VARCHAR},
            #{item.receiveAccountNo,jdbcType=VARCHAR},
            #{item.receiveAccountNoMd5,jdbcType=VARCHAR},
            #{item.cerFaceUrl,jdbcType=VARCHAR},
            #{item.idCardType,jdbcType=VARCHAR},
            #{item.idCardCopyUrl,jdbcType=VARCHAR},
            #{item.signUrl,jdbcType=VARCHAR},
            #{item.signerType,jdbcType=SMALLINT}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.SignRecord">
        UPDATE <include refid="table" /> SET
        VERSION = #{version,jdbcType=SMALLINT} +1,
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        USER_ID = #{userId,jdbcType=VARCHAR},
        RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
        RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR},
        RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR},
        RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
        RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR},
        RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
        EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
        EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
        MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
        MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
        INFO_STATUS = #{infoStatus,jdbcType=SMALLINT},
        SIGN_STATUS = #{signStatus,jdbcType=SMALLINT},
        ERR_CODE = #{errCode,jdbcType=VARCHAR},
        ERR_MSG = #{errMsg,jdbcType=VARCHAR},
        ACCOUNT_ID = #{accountId,jdbcType=VARCHAR},
        FILE_ID = #{fileId,jdbcType=VARCHAR},
        FLOW_ID = #{flowId,jdbcType=VARCHAR},
        FILE_URL = #{fileUrl,jdbcType=VARCHAR},
        SIGN_TYPE = #{signType,jdbcType=SMALLINT},
        REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
        NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
        ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT},
        JSON_STR = #{jsonStr,jdbcType=OTHER},
        OPERATE_STATUS = #{operateStatus,jdbcType=SMALLINT},
        OPERATE_MSG = #{operateMsg,jdbcType=VARCHAR},
        SMS_SEND_FREQUENCY = #{smsSendFrequency,jdbcType=INTEGER},
        SMS_SEND_TIME = #{smsSendTime,jdbcType=TIMESTAMP},
        ID_CARD_BACK_URL = #{idCardBackUrl,jdbcType=VARCHAR},
        ID_CARD_FRONT_URL = #{idCardFrontUrl,jdbcType=VARCHAR},
        PERSONAL_SIGNATURE = #{personalSignature,jdbcType=VARCHAR},
        RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR},
        RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
        CER_FACE_URL =  #{cerFaceUrl,jdbcType=VARCHAR},
        ID_CARD_TYPE =  #{idCardType,jdbcType=VARCHAR},
        ID_CARD_COPY_URL = #{idCardCopyUrl,jdbcType=VARCHAR},
        SIGN_URL = #{signUrl,jdbcType=VARCHAR},
        SIGNER_TYPE = #{signerType,jdbcType=SMALLINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.SignRecord">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="receiveName != null">
                RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
            </if>
            <if test="receiveNameMd5 != null">
                RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR},
            </if>
            <if test="receiveIdCardNo != null">
                RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR},
            </if>
            <if test="receiveIdCardNoMd5 != null">
                RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
            </if>
            <if test="receivePhoneNo != null">
                RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR},
            </if>
            <if test="receivePhoneNoMd5 != null">
                RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR},
            </if>
            <if test="employerNo != null">
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="employerName != null">
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="infoStatus != null">
                INFO_STATUS = #{infoStatus,jdbcType=SMALLINT},
            </if>
            <if test="signStatus != null">
                SIGN_STATUS = #{signStatus,jdbcType=SMALLINT},
            </if>
            <if test="errCode != null">
                ERR_CODE = #{errCode,jdbcType=VARCHAR},
            </if>
            <if test="errMsg != null">
                ERR_MSG = #{errMsg,jdbcType=VARCHAR},
            </if>
            <if test="accountId != null">
                ACCOUNT_ID = #{accountId,jdbcType=VARCHAR},
            </if>
            <if test="fileId != null">
                FILE_ID = #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="flowId != null">
                FLOW_ID = #{flowId,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                FILE_URL = #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="signType != null">
                SIGN_TYPE = #{signType,jdbcType=SMALLINT},
            </if>
            <if test="redirectUrl != null">
                REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR},
            </if>
            <if test="notifyUrl != null">
                NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR},
            </if>
            <if test="encryptKeyId != null">
                ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT},
            </if>
            <if test="jsonStr != null">
                JSON_STR = #{jsonStr,jdbcType=OTHER},
            </if>
            <if test="operateStatus != null">
                OPERATE_STATUS = #{operateStatus,jdbcType=SMALLINT},
            </if>
            <if test="operateMsg != null">
                OPERATE_MSG = #{operateMsg,jdbcType=VARCHAR},
            </if>
            <if test="smsSendFrequency != null">
                SMS_SEND_FREQUENCY = #{smsSendFrequency,jdbcType=INTEGER},
            </if>
            <if test="smsSendTime != null">
                SMS_SEND_TIME = #{smsSendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="idCardBackUrl != null">
                ID_CARD_BACK_URL = #{idCardBackUrl,jdbcType=VARCHAR},
            </if>
            <if test="idCardFrontUrl != null">
                ID_CARD_FRONT_URL = #{idCardFrontUrl,jdbcType=VARCHAR},
            </if>
            <if test="personalSignature != null and personalSignature != ''">
                PERSONAL_SIGNATURE = #{personalSignature,jdbcType=VARCHAR},
            </if>

            <if test="receiveAccountNo != null and receiveAccountNo != ''">
                RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 != ''">
                RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR},
            </if>
            <if test="cerFaceUrl != null and cerFaceUrl != ''">
                CER_FACE_URL = #{cerFaceUrl,jdbcType=VARCHAR},
            </if>
            <if test="idCardType != null and idCardType != ''">
                ID_CARD_TYPE = #{idCardType,jdbcType=VARCHAR},
            </if>
            <if test="idCardCopyUrl != null and idCardCopyUrl != ''">
                ID_CARD_COPY_URL = #{idCardCopyUrl,jdbcType=VARCHAR}
            </if>
            <if test="signUrl != null">
                SIGN_URL = #{signUrl,jdbcType=VARCHAR}
            </if>
            <if test="signerType != null">
                SIGNER_TYPE = #{signerType,jdbcType=SMALLINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <update id="updateSmsFrequency" parameterType="com.zhixianghui.facade.trade.entity.SignRecord">
        UPDATE
          <include refid="table" />
        SET
          SMS_SEND_FREQUENCY = SMS_SEND_FREQUENCY + 1,
          SMS_SEND_TIME = #{smsSendTime,jdbcType=TIMESTAMP},
          UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <update id="resetSmsSendFrequency" parameterType="com.zhixianghui.facade.trade.entity.SignRecord">
        UPDATE
        <include refid="table" />
        SET SMS_SEND_FREQUENCY = 0,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <update id="changeStatus" parameterType="com.zhixianghui.facade.trade.entity.SignRecord">
        UPDATE
        <include refid="table" />
        SET
            INFO_STATUS = #{infoStatus,jdbcType=SMALLINT},
            ERR_CODE = #{errCode},
            ERR_MSG = #{errMsg},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>


    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <select id="listBy2" parameterType="java.util.Map" resultType="java.util.Map">
        select
                a.ID as id,
                a.USER_ID as userId,
                a.RECEIVE_NAME as receiveName,
                a.RECEIVE_PHONE_NO as receivePhoneNo,
                a.CREATE_TIME as createTime,
                a.ERR_CODE as errCode,
                a.ERR_MSG as errMsg,
                a.FILE_URL as fileUrl,
                a.INFO_STATUS as infoStatus,
                a.MAINSTAY_NO as mainstayNo,
                a.MAINSTAY_NAME as mainstayName,
                a.SIGN_STATUS as signStatus,
                a.SIGN_TYPE as signType,
                a.FLOW_ID as flowId,
                a.ACCOUNT_ID as accountId,
                a.UPDATE_TIME as updateTime,
                a.EMPLOYER_NO as employerNo,
                a.EMPLOYER_NAME as employerName,
                a.RECEIVE_ACCOUNT_NO as receiveAccountNo,
                a.RECEIVE_ACCOUNT_NO_MD5 as receiveAccountNoMd5,
                a.RECEIVE_PHONE_NO as receivePhoneNo,
                a.RECEIVE_PHONE_NO_MD5 as receivePhoneNoMd5,
                a.ENCRYPT_KEY_ID as encryptKeyId,
                a.RECEIVE_ID_CARD_NO as receiveIdCardNo,
                a.RECEIVE_ID_CARD_NO_MD5 as receiveIdCardNoMd5,
                a.RECEIVE_NAME as receiveName,
                a.RECEIVE_NAME_MD5 as receiveNameMd5,
                b.CER_FACE_URL as cerFaceUrl,
                b.ID_CARD_FRONT_URL as idCardFrontUrl,
                b.ID_CARD_BACK_URL as idCardBackUrl,
                b.ID_CARD_COPY_URL as idCardCopyUrl,
                b.PERSONAL_SIGNATURE as personalSignature,
                b.ENCRYPT_KEY_ID as bKeyId,
                b.OPEN_USER_ID as openUserId,
                a.SIGN_URL as signUrl,
                a.SIGNER_TYPE as signerType
        from tbl_sign_record as a left join tbl_user_info as b on a.RECEIVE_ID_CARD_NO_MD5 =b.RECEIVE_ID_CARD_NO_MD5
        <where>
            <if test="id != null">
                and a.ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="maxId != null">
                AND a.ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
            </if>
            <if test="createTime != null">
                and a.CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and a.UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="userId != null and userId !=''">
                and a.USER_ID = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="receiveName != null and receiveName !=''">
                and a.RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR}
            </if>
            <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
                and a.RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNo != null and receiveIdCardNo !=''">
                and a.RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
                and a.RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="receivePhoneNo != null and receivePhoneNo !=''">
                and a.RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR}
            </if>
            <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 !=''">
                and a.RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="employerNo != null and employerNo !=''">
                and a.EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="employerName != null and employerName !=''">
                and a.EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
            </if>
            <if test="employerNameLike != null and employerNameLike !=''">
                and a.EMPLOYER_NAME LIKE concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and a.MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and a.MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
            </if>
            <if test="infoStatus != null">
                and a.INFO_STATUS = #{infoStatus,jdbcType=SMALLINT}
            </if>
            <if test="signStatus != null">
                and a.SIGN_STATUS = #{signStatus,jdbcType=SMALLINT}
            </if>
            <if test="errCode != null and errCode !=''">
                and a.ERR_CODE = #{errCode,jdbcType=VARCHAR}
            </if>
            <if test="errMsg != null and errMsg !=''">
                and a.ERR_MSG = #{errMsg,jdbcType=VARCHAR}
            </if>
            <if test="accountId != null and accountId !=''">
                and a.ACCOUNT_ID = #{accountId,jdbcType=VARCHAR}
            </if>
            <if test="fileId != null and fileId !=''">
                and a.FILE_ID = #{fileId,jdbcType=VARCHAR}
            </if>
            <if test="flowId != null and flowId !=''">
                and a.FLOW_ID = #{flowId,jdbcType=VARCHAR}
            </if>
            <if test="signType != null">
                and a.SIGN_TYPE = #{signType,jdbcType=SMALLINT}
            </if>
            <if test="createBeginDate != null and createEndDate != null" >
                and a.CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="updateBeginDate != null and updateEndDate != null" >
                and a.UPDATE_TIME between #{updateBeginDate,jdbcType=TIMESTAMP} and #{updateEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="receiveAccountNo != null and receiveAccountNo !=''">
                and a.RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 !=''">
                and a.RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="openUserId !=null and openUserId !=''">
                and b.OPEN_USER_ID = #{openUserId,jdbcType=VARCHAR}
            </if>
            <if test="signStatusList !=null ">
                and a.SIGN_STATUS in
                <foreach collection="signStatusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=SMALLINT}</foreach>
            </if>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="userId != null and userId !=''">
            and USER_ID = #{userId,jdbcType=VARCHAR}
        </if>
        <if test="receiveName != null and receiveName !=''">
            and RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR}
        </if>
        <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
            and RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR}
        </if>
        <if test="receiveIdCardNo != null and receiveIdCardNo !=''">
            and RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR}
        </if>
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
            and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="receivePhoneNo != null and receivePhoneNo !=''">
            and RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR}
        </if>
        <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 !=''">
            and RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
        </if>
        <if test="employerNameLike != null and employerNameLike !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="infoStatus != null">
            and INFO_STATUS = #{infoStatus,jdbcType=SMALLINT}
        </if>
        <if test="signStatus != null">
            and SIGN_STATUS = #{signStatus,jdbcType=SMALLINT}
        </if>
        <if test="errCode != null and errCode !=''">
            and ERR_CODE = #{errCode,jdbcType=VARCHAR}
        </if>
        <if test="errMsg != null and errMsg !=''">
            and ERR_MSG = #{errMsg,jdbcType=VARCHAR}
        </if>
        <if test="accountId != null and accountId !=''">
            and ACCOUNT_ID = #{accountId,jdbcType=VARCHAR}
        </if>
        <if test="fileId != null and fileId !=''">
            and FILE_ID = #{fileId,jdbcType=VARCHAR}
        </if>
        <if test="flowId != null and flowId !=''">
            and FLOW_ID = #{flowId,jdbcType=VARCHAR}
        </if>
        <if test="fileUrl != null and fileUrl !=''">
            and FILE_URL = #{fileUrl,jdbcType=VARCHAR}
        </if>
        <if test="signType != null">
            and SIGN_TYPE = #{signType,jdbcType=SMALLINT}
        </if>
        <if test="redirectUrl != null and redirectUrl !=''">
            and REDIRECT_URL = #{redirectUrl,jdbcType=VARCHAR}
        </if>
        <if test="notifyUrl != null and notifyUrl !=''">
            and NOTIFY_URL = #{notifyUrl,jdbcType=VARCHAR}
        </if>
        <if test="encryptKeyId != null">
            and ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT}
        </if>
        <if test="jsonStr != null and jsonStr !=''">
            and JSON_STR = #{jsonStr,jdbcType=OTHER}
        </if>
        <if test="createBeginDate != null and createEndDate != null" >
            and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
        </if>
        <if test="updateBeginDate != null and updateEndDate != null" >
            and UPDATE_TIME between #{updateBeginDate,jdbcType=TIMESTAMP} and #{updateEndDate,jdbcType=TIMESTAMP}
        </if>
        <if test="operateStatus != null">
            and OPERATE_STATUS = #{operateStatus,jdbcType=SMALLINT}
        </if>
        <if test="operateMsg != null and operateMsg !=''">
            and OPERATE_MSG = #{operateMsg,jdbcType=VARCHAR}
        </if>
        <if test="smsSendFrequency != null">
            and SMS_SEND_FREQUENCY = #{smsSendFrequency,jdbcType=INTEGER}
        </if>
        <if test="smsSendTime != null">
            and SMS_SEND_TIME = #{smsSendTime,jdbcType=TIMESTAMP}
        </if>
        <if test="idCardBackUrl != null and idCardBackUrl !=''">
            and ID_CARD_BACK_URL = #{idCardBackUrl,jdbcType=VARCHAR}
        </if>
        <if test="idCardFrontUrl != null and idCardFrontUrl !=''">
            and ID_CARD_FRONT_URL = #{idCardFrontUrl,jdbcType=VARCHAR}
        </if>
        <if test="personalSignature != null and personalSignature !=''">
            and PERSONAL_SIGNATURE = #{personalSignature,jdbcType=VARCHAR}
        </if>

        <if test="receiveAccountNo != null and receiveAccountNo !=''">
            and RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR}
        </if>
        <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 !=''">
            and RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="cerFaceUrl != null and cerFaceUrl !=''">
            and CER_FACE_URL = #{cerFaceUrl,jdbcType=VARCHAR}
        </if>
        <if test="idCardType != null and idCardType !=''">
            and ID_CARD_TYPE = #{idCardType,jdbcType=VARCHAR}
        </if>
        <if test="idCardCopyUrl != null and idCardCopyUrl !=''">
            and ID_CARD_COPY_URL = #{idCardCopyUrl,jdbcType=VARCHAR}
        </if>
        <if test="signerType != null">
            and SIGNER_TYPE = #{signerType,jdbcType=SMALLINT}
        </if>

        <if test="signStatusList !=null ">
            and SIGN_STATUS in
            <foreach collection="signStatusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=SMALLINT}</foreach>
        </if>
    </sql>

    <!-- 根据手机号查询 -->
    <select id="getByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE RECEIVE_PHONE_NO_MD5 = #{phone,jdbcType=VARCHAR}
        limit 1
    </select>


</mapper>
