<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.UserInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.UserInfo">
    <!--@mbg.generated-->
    <!--@Table tbl_user_info-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="RECEIVE_NAME" jdbcType="VARCHAR" property="receiveName" />
    <result column="RECEIVE_NAME_MD5" jdbcType="VARCHAR" property="receiveNameMd5" />
    <result column="RECEIVE_ID_CARD_NO" jdbcType="VARCHAR" property="receiveIdCardNo" />
    <result column="RECEIVE_ID_CARD_NO_MD5" jdbcType="VARCHAR" property="receiveIdCardNoMd5" />
    <result column="ID_CARD_BACK_URL" jdbcType="VARCHAR" property="idCardBackUrl" />
    <result column="ID_CARD_FRONT_URL" jdbcType="VARCHAR" property="idCardFrontUrl" />
    <result column="PERSONAL_SIGNATURE" jdbcType="VARCHAR" property="personalSignature" />
    <result column="CER_FACE_URL" jdbcType="VARCHAR" property="cerFaceUrl" />
    <result column="ID_CARD_COPY_URL" jdbcType="VARCHAR" property="idCardCopyUrl" />
    <result column="ENCRYPT_KEY_ID" jdbcType="BIGINT" property="encryptKeyId" />
    <result column="OPEN_USER_ID" jdbcType="VARCHAR" property="openUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, VERSION, CREATE_TIME, UPDATE_TIME, RECEIVE_NAME, RECEIVE_NAME_MD5, RECEIVE_ID_CARD_NO,
    RECEIVE_ID_CARD_NO_MD5, ID_CARD_BACK_URL, ID_CARD_FRONT_URL, PERSONAL_SIGNATURE, CER_FACE_URL, ID_CARD_COPY_URL, ENCRYPT_KEY_ID,OPEN_USER_ID
  </sql>
</mapper>
