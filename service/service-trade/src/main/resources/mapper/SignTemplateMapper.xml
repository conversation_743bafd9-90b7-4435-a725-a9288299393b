<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.SignTemplate">
    <sql id="table">tbl_sign_template</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.SignTemplate">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="TEMPLATE_ID" property="templateId" jdbcType="VARCHAR"/>
        <result column="UPLOAD_URL" property="uploadUrl" jdbcType="VARCHAR"/>
        <result column="COMPONENT_ID" property="componentId" jdbcType="OTHER"/>
        <result column="DELETE_FLAG" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="STRUCT_COMPONENT" property="structComponent" jdbcType="OTHER"/>
        <result column="SIGNING_DEADLINE" property="signingDeadline" jdbcType="INTEGER"/>
        <result column="AGREEMENT_EXPIRES_TIME" property="agreementExpiresTime" jdbcType="INTEGER"/>
        <result column="SIGN_TEMPLATE_TYPE" property="signTemplateType" jdbcType="SMALLINT"/>
        <result column="SOURCE_FILE_URL" property="sourceFileUrl" jdbcType="VARCHAR"/>
        <result column="TARGET_FILE_URL" property="targetFileUrl" jdbcType="VARCHAR"/>
        <result column="TEMPLATE_NAME" property="templateName" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, TEMPLATE_ID, UPLOAD_URL, COMPONENT_ID, DELETE_FLAG, STRUCT_COMPONENT, SIGNING_DEADLINE, AGREEMENT_EXPIRES_TIME, SIGN_TEMPLATE_TYPE, SOURCE_FILE_URL, TARGET_FILE_URL, TEMPLATE_NAME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.SignTemplate">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            TEMPLATE_ID,
            UPLOAD_URL,
            COMPONENT_ID,
            DELETE_FLAG,
            STRUCT_COMPONENT,
            SIGNING_DEADLINE,
            AGREEMENT_EXPIRES_TIME,
            SIGN_TEMPLATE_TYPE,
            SOURCE_FILE_URL,
            TARGET_FILE_URL,
            TEMPLATE_NAME
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{templateId,jdbcType=VARCHAR},
            #{uploadUrl,jdbcType=VARCHAR},
            #{componentId,jdbcType=OTHER},
            #{deleteFlag,jdbcType=TINYINT},
            #{structComponent,jdbcType=OTHER},
            #{signingDeadline,jdbcType=INTEGER},
            #{agreementExpiresTime,jdbcType=INTEGER},
            #{signTemplateType,jdbcType=SMALLINT},
            #{sourceFileUrl,jdbcType=VARCHAR},
            #{targetFileUrl,jdbcType=VARCHAR},
            #{templateName,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            TEMPLATE_ID,
            UPLOAD_URL,
            COMPONENT_ID,
            DELETE_FLAG,
            STRUCT_COMPONENT,
            SIGNING_DEADLINE,
            AGREEMENT_EXPIRES_TIME,
            SIGN_TEMPLATE_TYPE,
            SOURCE_FILE_URL,
            TARGET_FILE_URL,
            TEMPLATE_NAME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.templateId,jdbcType=VARCHAR},
            #{item.uploadUrl,jdbcType=VARCHAR},
            #{item.componentId,jdbcType=OTHER},
            #{item.deleteFlag,jdbcType=TINYINT},
            #{item.structComponent,jdbcType=OTHER},
            #{item.signingDeadline,jdbcType=INTEGER},
            #{item.agreementExpiresTime,jdbcType=INTEGER},
            #{item.signTemplateType,jdbcType=SMALLINT},
            #{item.sourceFileUrl,jdbcType=VARCHAR},
            #{item.targetFileUrl,jdbcType=VARCHAR},
            #{item.templateName,jdbcType=VARCHAR}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.SignTemplate">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} +1,
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            TEMPLATE_ID = #{templateId,jdbcType=VARCHAR},
            UPLOAD_URL = #{uploadUrl,jdbcType=VARCHAR},
            COMPONENT_ID = #{componentId,jdbcType=OTHER},
            DELETE_FLAG = #{deleteFlag,jdbcType=TINYINT},
            STRUCT_COMPONENT = #{structComponent,jdbcType=OTHER},
            SIGNING_DEADLINE = #{signingDeadline,jdbcType=INTEGER},
            AGREEMENT_EXPIRES_TIME = #{agreementExpiresTime,jdbcType=INTEGER},
            SIGN_TEMPLATE_TYPE = #{signTemplateType,jdbcType=SMALLINT},
            SOURCE_FILE_URL = #{sourceFileUrl,jdbcType=VARCHAR},
            TARGET_FILE_URL = #{targetFileUrl,jdbcType=VARCHAR},
            TEMPLATE_NAME = #{templateName,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.SignTemplate">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="templateId != null">
                TEMPLATE_ID = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="uploadUrl != null">
                UPLOAD_URL = #{uploadUrl,jdbcType=VARCHAR},
            </if>
            <if test="componentId != null">
                COMPONENT_ID = #{componentId,jdbcType=OTHER},
            </if>
            <if test="deleteFlag != null">
                DELETE_FLAG = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="structComponent != null">
                STRUCT_COMPONENT = #{structComponent,jdbcType=OTHER},
            </if>
            <if test="signingDeadline != null">
                SIGNING_DEADLINE = #{signingDeadline,jdbcType=INTEGER},
            </if>
            <if test="agreementExpiresTime != null">
                AGREEMENT_EXPIRES_TIME = #{agreementExpiresTime,jdbcType=INTEGER},
            </if>
            <if test="signTemplateType != null">
                SIGN_TEMPLATE_TYPE = #{signTemplateType,jdbcType=SMALLINT},
            </if>
            <if test="sourceFileUrl != null">
                SOURCE_FILE_URL = #{sourceFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="targetFileUrl != null">
                TARGET_FILE_URL = #{targetFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="templateName != null">
                TEMPLATE_NAME = #{templateName,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            and ID IN
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="templateId != null and templateId !=''">
            and TEMPLATE_ID = #{templateId,jdbcType=VARCHAR}
        </if>
        <if test="uploadUrl != null and uploadUrl !=''">
            and UPLOAD_URL = #{uploadUrl,jdbcType=VARCHAR}
        </if>
        <if test="componentId != null and componentId !=''">
            and COMPONENT_ID = #{componentId,jdbcType=OTHER}
        </if>
        <if test="deleteFlag != null">
            and DELETE_FLAG = #{deleteFlag,jdbcType=TINYINT}
        </if>
        <if test="structComponent != null and structComponent !=''">
            and STRUCT_COMPONENT = #{structComponent,jdbcType=OTHER}
        </if>
        <if test="signingDeadline != null">
            and SIGNING_DEADLINE = #{signingDeadline,jdbcType=INTEGER}
        </if>
        <if test="agreementExpiresTime != null">
            and AGREEMENT_EXPIRES_TIME = #{agreementExpiresTime,jdbcType=INTEGER}
        </if>
        <if test="signTemplateType != null">
            and SIGN_TEMPLATE_TYPE = #{signTemplateType,jdbcType=SMALLINT}
        </if>
        <if test="sourceFileUrl != null and sourceFileUrl !=''">
            and SOURCE_FILE_URL = #{sourceFileUrl,jdbcType=VARCHAR}
        </if>
        <if test="targetFileUrl != null and targetFileUrl !=''">
            and TARGET_FILE_URL = #{targetFileUrl,jdbcType=VARCHAR}
        </if>
        <if test="templateName != null and templateName !=''">
            and TEMPLATE_NAME like "%"#{templateName,jdbcType=VARCHAR}"%"
        </if>
    </sql>

</mapper>
