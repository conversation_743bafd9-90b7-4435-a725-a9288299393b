<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.WithdrawRecord">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.WithdrawRecord">
    <!--@mbg.generated-->
    <!--@Table tbl_withdraw_record-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="WITHDRAW_NO" jdbcType="VARCHAR" property="withdrawNo" />
    <result column="EMPLOYER_NO" jdbcType="VARCHAR" property="employerNo" />
    <result column="MAINSTAY_NO" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="MERCHANT_TYPE" jdbcType="INTEGER" property="merchantType" />
    <result column="CHANNEL_TYPE" jdbcType="INTEGER" property="channelType" />
    <result column="CHANNEL_NO" jdbcType="VARCHAR" property="channelNo" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
    <result column="EMPLOYER_NAME" jdbcType="VARCHAR" property="employerName" />
    <result column="MAINSTAY_NAME" jdbcType="VARCHAR" property="mainstayName" />
    <result column="RECEIVE_ACCT_NO" jdbcType="VARCHAR" property="receiveAcctNo" />
    <result column="RECEIVE_ID_TYPE" jdbcType="VARCHAR" property="receiveIdType" />
    <result column="RECWIVE_ID_NO" jdbcType="VARCHAR" property="recwiveIdNo" />
    <result column="RECEIVE_ACCT_TYPE" jdbcType="INTEGER" property="receiveAcctType" />
    <result column="RECEIVE_NAME" jdbcType="VARCHAR" property="receiveName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="WITHDRAW_STATUS" jdbcType="INTEGER" property="withdrawStatus" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="ERROR_MSG" jdbcType="VARCHAR" property="errorMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, WITHDRAW_NO, EMPLOYER_NO, MAINSTAY_NO, MERCHANT_TYPE, CHANNEL_TYPE, CHANNEL_NO,
    AMOUNT, REMARK, VERSION, EMPLOYER_NAME, MAINSTAY_NAME, RECEIVE_ACCT_NO, RECEIVE_ID_TYPE,
    RECWIVE_ID_NO, RECEIVE_ACCT_TYPE, RECEIVE_NAME, CREATE_TIME, UPDATE_TIME, WITHDRAW_STATUS,ERROR_CODE,ERROR_MSG
  </sql>

  <sql id="condition_sql">
    <if test="id != null">
      and ID = #{id,jdbcType=BIGINT}
    </if>
    <if test="maxId != null">
      and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="withdrawNo != null and withdrawNo != ''">
      and WITHDRAW_NO = #{withdrawNo,jdbcType=VARCHAR}
    </if>
    <if test="employerNo != null and employerNo !=''">
      and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
    </if>
    <if test="employerName != null and employerName !=''">
      and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
    </if>
    <if test="mainstayNo != null and mainstayNo !=''">
      and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
    </if>
    <if test="mainstayName != null and mainstayName !=''">
      and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
    </if>
    <if test="receiveAcctNo != null and receiveAcctNo !='' ">
      and RECEIVE_ACCT_NO = #{receiveAcctNo,jdbcType=SMALLINT}
    </if>
    <if test="withdrawStatus != null and withdrawStatus !=''">
      and WITHDRAW_STATUS = #{withdrawStatus,jdbcType=VARCHAR}
    </if>
    <if test="merchantType != null and merchantType != ''">
      and MERCHANT_TYPE = #{merchantType,jdbcType=VARCHAR}
    </if>
    <if test="channelNo != null and channelNo != ''">
      and CHANNEL_NO = #{channelNo,jdbcType=VARCHAR}
    </if>
    <if test="employerNameLike != null and employerNameLike !='' ">
      AND EMPLOYER_NAME LIKE CONCAT('%', CONCAT(#{employerNameLike}, '%'))
    </if>
    <if test="createBeginDate != null and createEndDate != null " >
      and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
    </if>
  </sql>



  <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM tbl_withdraw_record
    <where>
      <include refid="condition_sql" />
    </where>
    <choose>
      <when test="sortColumns != null and sortColumns !='' ">
        <![CDATA[ ORDER BY ${sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY ID DESC ]]>
      </otherwise>
    </choose>
  </select>

  <select id="countBy" parameterType="java.util.Map" resultType="long">
    SELECT COUNT(1) FROM tbl_withdraw_record
    <where>
      <include refid="condition_sql" />
    </where>
  </select>

  <select id="sumCmbWithdrawAmt" parameterType="java.util.Map" resultType="java.util.Map">
    select t.EMPLOYER_NO employerNo,t.MAINSTAY_NO mainstayNo,sum(t.AMOUNT) sumWithdrawAmt
    from tbl_withdraw_record t
    where t.CHANNEL_NO  ='CMB' and WITHDRAW_STATUS  = 1
      and t.EMPLOYER_NO = #{employerNo}
      and t.MAINSTAY_NO = #{mainstayNo}
      and t.UPDATE_TIME  <![CDATA[ >= ]]> #{completeBeginDate}
      and t.UPDATE_TIME  <![CDATA[ <= ]]> #{completeEndDate}
    group by t.EMPLOYER_NO,t.MAINSTAY_NO
  </select>

  <select id="sumWithdrawRecord" parameterType="java.util.Map" resultType="java.util.Map">
    select
      sum(ifnull(t.AMOUNT,0)) sumAmt,
      count(id) totalNum
    from tbl_withdraw_record t
    <where>
      <include refid="condition_sql" />
    </where>
  </select>
</mapper>
