<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.OfflineOrderItemMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.OfflineOrderItem">
    <!--@mbg.generated-->
    <!--@Table tbl_offline_order_item-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <id column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="VERSION" jdbcType="SMALLINT" property="version" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="COMPLETE_TIME" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="MCH_BATCH_NO" jdbcType="VARCHAR" property="mchBatchNo" />
    <result column="PLAT_BATCH_NO" jdbcType="VARCHAR" property="platBatchNo" />
    <result column="MCH_ORDER_NO" jdbcType="VARCHAR" property="mchOrderNo" />
    <result column="PLAT_TRX_NO" jdbcType="VARCHAR" property="platTrxNo" />
    <result column="LAUNCH_WAY" jdbcType="SMALLINT" property="launchWay" />
    <result column="PRODUCT_NO" jdbcType="VARCHAR" property="productNo" />
    <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
    <result column="EMPLOYER_NO" jdbcType="VARCHAR" property="employerNo" />
    <result column="EMPLOYER_NAME" jdbcType="VARCHAR" property="employerName" />
    <result column="MAINSTAY_NO" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="MAINSTAY_NAME" jdbcType="VARCHAR" property="mainstayName" />
    <result column="TAX_PAYER" jdbcType="SMALLINT" property="taxPayer" />
    <result column="RECEIVE_NAME" jdbcType="VARCHAR" property="receiveName" />
    <result column="RECEIVE_NAME_MD5" jdbcType="VARCHAR" property="receiveNameMd5" />
    <result column="RECEIVE_ID_CARD_NO" jdbcType="VARCHAR" property="receiveIdCardNo" />
    <result column="RECEIVE_ID_CARD_NO_MD5" jdbcType="VARCHAR" property="receiveIdCardNoMd5" />
    <result column="RECEIVE_ACCOUNT_NO" jdbcType="VARCHAR" property="receiveAccountNo" />
    <result column="RECEIVE_ACCOUNT_NO_MD5" jdbcType="VARCHAR" property="receiveAccountNoMd5" />
    <result column="RECEIVE_PHONE_NO" jdbcType="VARCHAR" property="receivePhoneNo" />
    <result column="RECEIVE_PHONE_NO_MD5" jdbcType="VARCHAR" property="receivePhoneNoMd5" />
    <result column="BANK_NAME" jdbcType="VARCHAR" property="bankName" />
    <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode" />
    <result column="ORDER_ITEM_NET_AMOUNT" jdbcType="DECIMAL" property="orderItemNetAmount" />
    <result column="ORDER_ITEM_TASK_AMOUNT" jdbcType="DECIMAL" property="orderItemTaskAmount" />
    <result column="ORDER_ITEM_FEE" jdbcType="DECIMAL" property="orderItemFee" />
    <result column="ORDER_ITEM_AMOUNT" jdbcType="DECIMAL" property="orderItemAmount" />
    <result column="ORDER_ITEM_TAX_AMOUNT" jdbcType="DECIMAL" property="orderItemTaxAmount" />
    <result column="ORDER_ITEM_STATUS" jdbcType="SMALLINT" property="orderItemStatus" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="ERROR_DESC" jdbcType="VARCHAR" property="errorDesc" />
    <result column="ENCRYPT_KEY_ID" jdbcType="BIGINT" property="encryptKeyId" />
    <result column="ACCESS_TIMES" jdbcType="SMALLINT" property="accessTimes" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="JSON_STR" jdbcType="VARCHAR" property="jsonStr" />
    <result column="APPID" jdbcType="VARCHAR" property="appid" />
    <result column="IS_DELETE" jdbcType="SMALLINT" property="isDelete" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="JOB_ID" jdbcType="VARCHAR" property="jobId" />
    <result column="JOB_NAME" jdbcType="VARCHAR" property="jobName" />
    <result column="WORK_CATEGORY_CODE" jdbcType="VARCHAR" property="workCategoryCode" />
    <result column="WORK_CATEGORY_NAME" jdbcType="VARCHAR" property="workCategoryName" />
    <result column="WORKER_BILL_FILE_PATH" jdbcType="VARCHAR" property="workerBillFilePath"  typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="IS_PASS_HANGUP" jdbcType="BIT" property="isPassHangup" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CREATE_TIME, VERSION, UPDATE_TIME, COMPLETE_TIME, MCH_BATCH_NO, PLAT_BATCH_NO,
    MCH_ORDER_NO, PLAT_TRX_NO, LAUNCH_WAY, PRODUCT_NO, PRODUCT_NAME, EMPLOYER_NO, EMPLOYER_NAME,
    MAINSTAY_NO, MAINSTAY_NAME, TAX_PAYER, RECEIVE_NAME, RECEIVE_NAME_MD5, RECEIVE_ID_CARD_NO,
    RECEIVE_ID_CARD_NO_MD5, RECEIVE_ACCOUNT_NO, RECEIVE_ACCOUNT_NO_MD5, RECEIVE_PHONE_NO,
    RECEIVE_PHONE_NO_MD5, BANK_NAME, BANK_CODE, ORDER_ITEM_NET_AMOUNT, ORDER_ITEM_TASK_AMOUNT,
    ORDER_ITEM_FEE, ORDER_ITEM_AMOUNT, ORDER_ITEM_TAX_AMOUNT, ORDER_ITEM_STATUS, ERROR_CODE,
    ERROR_DESC, ENCRYPT_KEY_ID, ACCESS_TIMES, REMARK, JSON_STR, APPID, IS_DELETE, MEMO,
    JOB_ID, JOB_NAME, WORK_CATEGORY_CODE, WORK_CATEGORY_NAME, WORKER_BILL_FILE_PATH,IS_PASS_HANGUP
  </sql>

  <select id="getCountBoByPlatBatchNo" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.bo.OrderItemCountBo">
  SELECT
  SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN 1 ELSE 0 END ) AS "acceptedCount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_TASK_AMOUNT ELSE 0 END ) AS "acceptedTaskAmount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_NET_AMOUNT ELSE 0 END ) AS "acceptedNetAmount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_FEE ELSE 0 END ) AS "acceptedFee",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_TAX_AMOUNT ELSE 0 END ) AS "acceptedTaxAmount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 400 THEN ORDER_ITEM_AMOUNT ELSE 0 END ) AS "acceptedOrderAmount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN 1 ELSE 0 END ) AS "successCount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN ORDER_ITEM_TASK_AMOUNT ELSE 0 END ) AS "successTaskAmount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN ORDER_ITEM_TAX_AMOUNT ELSE 0 END ) AS "successTaxAmount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN ORDER_ITEM_NET_AMOUNT ELSE 0 END ) AS "successNetAmount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 100 THEN ORDER_ITEM_FEE ELSE 0 END ) AS "successFee",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 200 OR ORDER_ITEM_STATUS = 700 THEN 1 ELSE 0 END ) AS "failCount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 200 OR ORDER_ITEM_STATUS = 700 THEN ORDER_ITEM_NET_AMOUNT ELSE 0 END ) AS "failNetAmount",
  SUM(CASE WHEN ORDER_ITEM_STATUS = 200 OR ORDER_ITEM_STATUS = 700 THEN ORDER_ITEM_TASK_AMOUNT ELSE 0 END ) AS "failTaskAmount"
  FROM
    tbl_offline_order_item
  <where>
    <include refid="condition_sql" />
  </where>
  </select>

  <!-- 不分页查询 -->
  <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    tbl_offline_order_item
    <where>
      <include refid="condition_sql" />
    </where>
    <choose>
      <when test="sortColumns != null and sortColumns !='' ">
        <![CDATA[ ORDER BY ${sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY ID DESC ]]>
      </otherwise>
    </choose>
  </select>

  <sql id="condition_sql">
    <if test="id != null">
      and ID = #{id,jdbcType=BIGINT}
    </if>
    <if test="version != null">
      and VERSION = #{version,jdbcType=SMALLINT}
    </if>
    <if test="createDate != null">
      and CREATE_DATE = #{createDate,jdbcType=DATE}
    </if>
    <if test="createTime != null">
      and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    </if>
    <if test="updateTime != null">
      and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    </if>
    <if test="completeTime != null">
      and COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP}
    </if>
    <if test="mchBatchNo != null and mchBatchNo !=''">
      and MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR}
    </if>
    <if test="platBatchNo != null and platBatchNo !=''">
      and PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
    </if>
    <if test="mchOrderNo != null and mchOrderNo !=''">
      and MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR}
    </if>
    <if test="platTrxNo != null and platTrxNo !=''">
      and PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR}
    </if>
    <if test="launchWay != null">
      and LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT}
    </if>
    <if test="productNo != null and productNo != ''">
      and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
    </if>
    <if test="productName != null and productName != ''">
      and PRODUCT_NAME = #{productName,jdbcType=VARCHAR}
    </if>
    <if test="employerNo != null and employerNo !=''">
      and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
    </if>
    <if test="employerName != null and employerName !=''">
      and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
    </if>
    <if test="mainstayNo != null and mainstayNo !=''">
      and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
    </if>
    <if test="mainstayName != null and mainstayName !=''">
      and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
    </if>
    <if test="channelType != null">
      and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
    </if>
    <if test="payChannelNo != null and payChannelNo !=''">
      and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
    </if>
    <if test="channelName != null and channelName !=''">
      and CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
    </if>
    <if test="receiveName != null and receiveName !=''">
      and RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR}
    </if>
    <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
      and RECEIVE_NAME_MD5 = #{receiveNameMd5,jdbcType=VARCHAR}
    </if>
    <if test="receiveIdCardNo != null and receiveIdCardNo !=''">
      and RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR}
    </if>
    <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
      and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
    </if>
    <if test="receiveAccountNo != null and receiveAccountNo !=''">
      and RECEIVE_ACCOUNT_NO = #{receiveAccountNo,jdbcType=VARCHAR}
    </if>
    <if test="receiveAccountNoMd5 != null and receiveAccountNoMd5 !=''">
      and RECEIVE_ACCOUNT_NO_MD5 = #{receiveAccountNoMd5,jdbcType=VARCHAR}
    </if>
    <if test="receivePhoneNo != null and receivePhoneNo !=''">
      and RECEIVE_PHONE_NO = #{receivePhoneNo,jdbcType=VARCHAR}
    </if>
    <if test="receivePhoneNoMd5 != null and receivePhoneNoMd5 !=''">
      and RECEIVE_PHONE_NO_MD5 = #{receivePhoneNoMd5,jdbcType=VARCHAR}
    </if>
    <if test="bankName != null and bankName !=''">
      and BANK_NAME = #{bankName,jdbcType=VARCHAR}
    </if>
    <if test="bankCode != null and bankCode !=''">
      and BANK_CODE = #{bankCode,jdbcType=VARCHAR}
    </if>
    <if test="orderItemNetAmount != null">
      and ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL}
    </if>
    <if test="orderItemFee != null">
      and ORDER_ITEM_FEE = #{orderItemFee,jdbcType=DECIMAL}
    </if>
    <if test="orderItemAmount != null">
      and ORDER_ITEM_AMOUNT = #{orderItemAmount,jdbcType=DECIMAL}
    </if>
    <if test="orderItemStatus != null">
      and ORDER_ITEM_STATUS = #{orderItemStatus,jdbcType=SMALLINT}
    </if>
    <if test="employerList != null and employerList.size() > 0">
      and employer_no in
      <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
    </if>
    <if test="orderItemStatusList != null and orderItemStatusList.size() > 0">
      and ORDER_ITEM_STATUS in
      <foreach collection="orderItemStatusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </if>
    <if test="errorCode != null and errorCode !=''">
      and ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
    </if>
    <if test="errorDesc != null and errorDesc !=''">
      and ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
    </if>
    <if test="encryptKeyId != null">
      and ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT}
    </if>
    <if test="accessTimes != null">
      and ACCESS_TIMES = #{accessTimes,jdbcType=SMALLINT}
    </if>
    <if test="isPassHangup != null">
      and IS_PASS_HANGUP = #{isPassHangup,jdbcType=BIT}
    </if>
    <if test="hangupApprovalLoginName != null and hangupApprovalLoginName !=''">
      and HANGUP_APPROVAL_LOGIN_NAME = #{hangupApprovalLoginName,jdbcType=VARCHAR}
    </if>
    <if test="remark != null and remark !=''">
      and REMARK = #{remark,jdbcType=VARCHAR}
    </if>
    <if test="memo != null and memo !=''">
      and MEMO = #{memo,jdbcType=VARCHAR}
    </if>
    <if test="jobId != null and jobId !=''">
      and JOB_ID = #{jobId,jdbcType=VARCHAR}
    </if>
    <if test="jobName != null and jobName !=''">
      and JOB_NAME = #{jobName,jdbcType=VARCHAR}
    </if>
    <if test="workCategoryCode != null and workCategoryCode !=''">
      and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
    </if>
    <if test="workCategoryName != null and workCategoryName !=''">
      and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
    </if>
    <if test="jsonStr != null and jsonStr !=''">
      and JSON_STR = #{jsonStr,jdbcType=OTHER}
    </if>

    <!--  自定义  -->
    <if test="overId != null and overId !=''">
      and ID > #{overId,jdbcType=BIGINT}
    </if>
    <if test="maxId != null">
      and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="employerNameLike != null and employerNameLike !=''">
      and EMPLOYER_NAME LIKE concat('%',#{employerNameLike,jdbcType=VARCHAR},'%')
    </if>
    <if test="createBeginDate != null and createEndDate != null" >
      and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
    </if>
    <if test="completeBeginDate != null and completeEndDate != null" >
      and COMPLETE_TIME between #{completeBeginDate,jdbcType=TIMESTAMP} and #{completeEndDate,jdbcType=TIMESTAMP}
    </if>
    <if test="orderItemNetAmountMin != null and orderItemNetAmountMax != null" >
      and ORDER_ITEM_NET_AMOUNT between #{orderItemNetAmountMin,jdbcType=DECIMAL} and #{orderItemNetAmountMax,jdbcType=DECIMAL}
    </if>
    <if test="createDateList != null and createDateList.size() > 0">
      and CREATE_DATE IN
      <foreach collection="createDateList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=DATE}</foreach>
    </if>

    <if test="platBatchNos != null and platBatchNos.size() > 0">
      and PLAT_BATCH_NO IN
      <foreach collection="platBatchNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
    </if>
    <!--  分表字段区间  -->
    <if test="beginDate != null and endDate != null" >
      and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
    </if>
    <if test="isDelete != null " >
      and (is_delete = #{isDelete} or is_delete is null)
    </if>
  </sql>

  <select id="countOrder" parameterType="java.util.Map" resultType="long">
    select count(*) from (
    select id FROM tbl_offline_order_item
    where RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5, jdbcType=VARCHAR}
    and ORDER_ITEM_STATUS = 100 and  COMPLETE_TIME is not null
    and  DATE_FORMAT(COMPLETE_TIME,'%Y-%m') = DATE_FORMAT(#{completeDate,jdbcType=DATE},'%Y-%m')

    union all

    select id FROM tbl_order_item
    where RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5, jdbcType=VARCHAR}
    and ORDER_ITEM_STATUS = 100 and  COMPLETE_TIME is not null
    and DATE_FORMAT(COMPLETE_TIME,'%Y-%m') = DATE_FORMAT(#{completeDate,jdbcType=DATE},'%Y-%m')
    and CREATE_DATE between #{lastBeginDate,jdbcType=DATE} and #{lastEndDate,jdbcType=DATE}
    ) a

  </select>

  <select id="sumOrderItem" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.bo.OrderItemSumBo">
    SELECT
    COUNT(*) AS "totalNum",
    SUM(ORDER_ITEM_NET_AMOUNT) AS "totalNetAmount",
    SUM(ORDER_ITEM_FEE) AS "totalFee",
    SUM(ORDER_ITEM_NET_AMOUNT+IFNULL(ORDER_ITEM_FEE,0)) AS "totalOrderAmount"
    FROM
      tbl_offline_order_item
    <where>
      <include refid="condition_sql" />
    </where>
  </select>

  <select id="sumOrderItemWaitInvoiceAmount" resultType="java.math.BigDecimal">
    SELECT  SUM(coalesce(ORDER_ITEM_NET_AMOUNT,0)+coalesce(ORDER_ITEM_TAX_AMOUNT,0))as total
    FROM tbl_offline_order_item
    <where>
      <include refid="condition_sql" />
    </where>
  </select>

  <select id="listPlatTrxNoByBatchNo" parameterType="java.util.Map" resultType="java.lang.String">
    select
    PLAT_TRX_NO
    from tbl_offline_order_item
    where PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR} and ORDER_ITEM_STATUS=#{orderItemStatus,jdbcType=INTEGER}
  </select>

  <update id="cancelOrderItem" parameterType="java.util.Map">
    UPDATE tbl_offline_order_item
    SET ORDER_ITEM_STATUS = 800,VERSION=VERSION+1
    WHERE PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR} AND ORDER_ITEM_STATUS = 400
  </update>

  <update id="updateByPlatBatchNo" parameterType="com.zhixianghui.facade.trade.dto.OrderDeleteDTO">
    update tbl_offline_order_item
    set is_delete=#{isDelete},UPDATE_TIME=#{updateTime}
    <where>
      PLAT_BATCH_NO=#{platBatchNo}
    </where>
  </update>
</mapper>
