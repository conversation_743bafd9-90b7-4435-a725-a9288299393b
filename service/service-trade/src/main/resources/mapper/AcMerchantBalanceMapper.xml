<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.AcMerchantBalance">

    <sql id="table"> tbl_ac_merchant_balance </sql>

    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.AcMerchantBalance">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="version" jdbcType="SMALLINT" property="version"/>
        <result column="mch_no" jdbcType="VARCHAR" property="mchNo"/>
        <result column="mch_name" jdbcType="VARCHAR" property="mchName"/>
        <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo"/>
        <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName"/>
        <result column="pay_channel_no" jdbcType="VARCHAR" property="payChannelNo"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="total_amount" jdbcType="BIGINT" property="totalAmount"/>
        <result column="freeze_amount" jdbcType="BIGINT" property="freezeAmount"/>
        <result column="settle_amount" jdbcType="BIGINT" property="settleAmount"/>
        <result column="merchant_type" jdbcType="SMALLINT" property="merchantType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, version, mch_no, mch_name, mainstay_no, mainstay_name, pay_channel_no, pay_channel_name,
    total_amount, freeze_amount,settle_amount, merchant_type, create_time, update_time
    </sql>
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from <include refid="table"/>
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from <include refid="table"/>
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert"
            useGeneratedKeys="true" keyProperty="id"
            parameterType="com.zhixianghui.facade.trade.entity.AcMerchantBalance">
        insert into <include refid="table"/> (id, version, mch_no,
                                             mch_name, mainstay_no, mainstay_name,
                                             pay_channel_no, pay_channel_name, total_amount,
                                             freeze_amount,settle_amount, merchant_type, create_time,
                                             update_time)
        values (#{id,jdbcType=BIGINT}, 0, #{mchNo,jdbcType=VARCHAR},
                #{mchName,jdbcType=VARCHAR}, #{mainstayNo,jdbcType=VARCHAR}, #{mainstayName,jdbcType=VARCHAR},
                #{payChannelNo,jdbcType=VARCHAR}, #{payChannelName,jdbcType=VARCHAR}, 0,
                0, 0, #{merchantType,jdbcType=SMALLINT}, now(),
                #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.zhixianghui.facade.trade.entity.AcMerchantBalance">
        insert into <include refid="table"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="mchNo != null">
                mch_no,
            </if>
            <if test="mchName != null">
                mch_name,
            </if>
            <if test="mainstayNo != null">
                mainstay_no,
            </if>
            <if test="mainstayName != null">
                mainstay_name,
            </if>
            <if test="payChannelNo != null">
                pay_channel_no,
            </if>
            <if test="payChannelName != null">
                pay_channel_name,
            </if>
            <if test="totalAmount != null">
                total_amount,
            </if>
            <if test="freezeAmount != null">
                freeze_amount,
            </if>
            <if test="settleAmount != null">
                settle_amount,
            </if>
            <if test="merchantType != null">
                merchant_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                #{version,jdbcType=SMALLINT},
            </if>
            <if test="mchNo != null">
                #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelNo != null">
                #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=BIGINT},
            </if>
            <if test="freezeAmount != null">
                #{freezeAmount,jdbcType=BIGINT},
            </if>
            <if test="settleAmount != null">
                #{settleAmount,jdbcType=BIGINT},
            </if>
            <if test="merchantType != null">
                #{merchantType,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.AcMerchantBalance">
        update <include refid="table"/>
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                mch_name = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="payChannelNo != null">
                pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=BIGINT},
            </if>
            <if test="freezeAmount != null">
                freeze_amount = #{freezeAmount,jdbcType=BIGINT},
            </if>
            <if test="settleAmount != null">
                settle_amount = #{settleAmount,jdbcType=BIGINT},
            </if>
            <if test="merchantType != null">
                merchant_type = #{merchantType,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.AcMerchantBalance">
        update <include refid="table"/>
        set version          = #{version,jdbcType=SMALLINT},
            mch_no           = #{mchNo,jdbcType=VARCHAR},
            mch_name         = #{mchName,jdbcType=VARCHAR},
            mainstay_no      = #{mainstayNo,jdbcType=VARCHAR},
            mainstay_name    = #{mainstayName,jdbcType=VARCHAR},
            pay_channel_no   = #{payChannelNo,jdbcType=VARCHAR},
            pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
            total_amount     = #{totalAmount,jdbcType=BIGINT},
            freeze_amount    = #{freezeAmount,jdbcType=BIGINT},
            settle_amount    = #{settleAmount,jdbcType=BIGINT},
            merchant_type    = #{merchantType,jdbcType=SMALLINT},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            update_time      = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>



    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and mch_no = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="mchName != null and mchName !=''">
            and mch_name = #{mchName,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and mainstay_name = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="totalAmount != null">
            and total_amount = #{totalAmount,jdbcType=BIGINT}
        </if>
        <if test="freezeAmount != null">
            and freeze_amount = #{freezeAmount,jdbcType=BIGINT}
        </if>
        <if test="settleAmount != null">
            and settle_amount = #{settleAmount,jdbcType=BIGINT}
        </if>
        <if test="merchantType != null">
            and merchant_type = #{merchantType,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="payChannelNo != null and payChannelNo !=''">
            and pay_channel_no = #{payChannelNo,jdbcType=VARCHAR}
        </if>
        <if test="payChannelName != null and payChannelName !=''">
            and pay_channel_name = #{payChannelName,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>