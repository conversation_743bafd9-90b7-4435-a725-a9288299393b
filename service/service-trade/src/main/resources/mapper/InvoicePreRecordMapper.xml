<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.trade.entity.InvoicePreRecord">
    <sql id="table"> tbl_invoice_pre_record </sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.InvoicePreRecord">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="COMPLETE_TIME" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="PRODUCT_NO" property="productNo" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_MCH_NO" property="employerMchNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_MCH_NAME" property="employerMchName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_MCH_NO" property="mainstayMchNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_MCH_NAME" property="mainstayMchName" jdbcType="VARCHAR"/>
        <result column="WORK_CATEGORY_CODE" property="workCategoryCode" jdbcType="VARCHAR"/>
        <result column="WORK_CATEGORY_NAME" property="workCategoryName" jdbcType="VARCHAR"/>
        <result column="INVOICE_AMOUNT" property="invoiceAmount" jdbcType="DECIMAL"/>
        <result column="INVOICE_CATEGORY_CODE" property="invoiceCategoryCode" jdbcType="VARCHAR"/>
        <result column="INVOICE_CATEGORY_NAME" property="invoiceCategoryName" jdbcType="VARCHAR"/>
        <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="SMALLINT"/>
        <result column="INVOICE_FILE_URL" property="invoiceFileUrl" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="JSON_INFO" property="jsonInfo" jdbcType="OTHER"/>
        <result column="ERROR_DESC" property="errorDesc" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
        ID,
        VERSION,
        CREATE_TIME,
        UPDATE_TIME,
        COMPLETE_TIME,
        PRODUCT_NO,
        PRODUCT_NAME,
        EMPLOYER_MCH_NO,
        EMPLOYER_MCH_NAME,
        MAINSTAY_MCH_NO,
        MAINSTAY_MCH_NAME,
        WORK_CATEGORY_CODE,
        WORK_CATEGORY_NAME,
        INVOICE_AMOUNT,
        INVOICE_CATEGORY_CODE,
        INVOICE_CATEGORY_NAME,
        INVOICE_STATUS,
        INVOICE_FILE_URL,
        REMARK,
        JSON_INFO,
        ERROR_DESC
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.InvoicePreRecord">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            COMPLETE_TIME,
            PRODUCT_NO,
            PRODUCT_NAME,
            EMPLOYER_MCH_NO,
            EMPLOYER_MCH_NAME,
            MAINSTAY_MCH_NO,
            MAINSTAY_MCH_NAME,
            WORK_CATEGORY_CODE,
            WORK_CATEGORY_NAME,
            INVOICE_AMOUNT,
            INVOICE_CATEGORY_CODE,
            INVOICE_CATEGORY_NAME,
            INVOICE_STATUS,
            INVOICE_FILE_URL,
            REMARK,
            JSON_INFO,
            ERROR_DESC
        ) VALUES (
            0,
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{completeTime,jdbcType=TIMESTAMP},
            #{productNo,jdbcType=VARCHAR},
            #{productName,jdbcType=VARCHAR},
            #{employerMchNo,jdbcType=VARCHAR},
            #{employerMchName,jdbcType=VARCHAR},
            #{mainstayMchNo,jdbcType=VARCHAR},
            #{mainstayMchName,jdbcType=VARCHAR},
            #{workCategoryCode,jdbcType=VARCHAR},
            #{workCategoryName,jdbcType=VARCHAR},
            #{invoiceAmount,jdbcType=DECIMAL},
            #{invoiceCategoryCode,jdbcType=VARCHAR},
            #{invoiceCategoryName,jdbcType=VARCHAR},
            #{invoiceStatus,jdbcType=SMALLINT},
            #{invoiceFileUrl,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR},
            #{jsonInfo,jdbcType=OTHER},
            #{errorDesc,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_TIME,
            UPDATE_TIME,
            COMPLETE_TIME,
            PRODUCT_NO,
            PRODUCT_NAME,
            EMPLOYER_MCH_NO,
            EMPLOYER_MCH_NAME,
            MAINSTAY_MCH_NO,
            MAINSTAY_MCH_NAME,
            WORK_CATEGORY_CODE,
            WORK_CATEGORY_NAME,
            INVOICE_AMOUNT,
            INVOICE_CATEGORY_CODE,
            INVOICE_CATEGORY_NAME,
            INVOICE_STATUS,
            INVOICE_FILE_URL,
            REMARK,
            JSON_INFO,
            ERROR_DESC
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            0,
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.completeTime,jdbcType=TIMESTAMP},
            #{item.productNo,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR},
            #{item.employerMchNo,jdbcType=VARCHAR},
            #{item.employerMchName,jdbcType=VARCHAR},
            #{item.mainstayMchNo,jdbcType=VARCHAR},
            #{item.mainstayMchName,jdbcType=VARCHAR},
            #{item.workCategoryCode,jdbcType=VARCHAR},
            #{item.workCategoryName,jdbcType=VARCHAR},
            #{item.invoiceAmount,jdbcType=DECIMAL},
            #{item.invoiceCategoryCode,jdbcType=VARCHAR},
            #{item.invoiceCategoryName,jdbcType=VARCHAR},
            #{item.invoiceStatus,jdbcType=SMALLINT},
            #{item.invoiceFileUrl,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.jsonInfo,jdbcType=OTHER},
            #{item.errorDesc,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.InvoicePreRecord">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} + 1,
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
            PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
            PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            EMPLOYER_MCH_NO = #{employerMchNo,jdbcType=VARCHAR},
            EMPLOYER_MCH_NAME = #{employerMchName,jdbcType=VARCHAR},
            MAINSTAY_MCH_NO = #{mainstayMchNo,jdbcType=VARCHAR},
            MAINSTAY_MCH_NAME = #{mainstayMchName,jdbcType=VARCHAR},
            WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR},
            WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR},
            INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL},
            INVOICE_CATEGORY_CODE = #{invoiceCategoryCode,jdbcType=VARCHAR},
            INVOICE_CATEGORY_NAME = #{invoiceCategoryName,jdbcType=VARCHAR},
            INVOICE_STATUS = #{invoiceStatus,jdbcType=SMALLINT},
            INVOICE_FILE_URL = #{invoiceFileUrl,jdbcType=VARCHAR},
            REMARK = #{remark,jdbcType=VARCHAR},
            JSON_INFO = #{jsonInfo,jdbcType=OTHER},
            ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.InvoicePreRecord">
        UPDATE <include refid="table" />
        <set>
            VERSION = #{version,jdbcType=SMALLINT} +1,
            <if test="createTime != null">
                CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME =#{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="completeTime != null">
                COMPLETE_TIME =#{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="productNo != null and productNo !='' ">
                PRODUCT_NO =#{productNo,jdbcType=VARCHAR},
            </if>
            <if test="productName != null and productName !='' ">
                PRODUCT_NAME =#{productName,jdbcType=VARCHAR},
            </if>
            <if test="employerMchNo != null">
                EMPLOYER_MCH_NO =#{employerMchNo,jdbcType=VARCHAR},
            </if>
            <if test="employerMchName != null">
                EMPLOYER_MCH_NAME =#{employerMchName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayMchNo != null">
                MAINSTAY_MCH_NO =#{mainstayMchNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayMchName != null">
                MAINSTAY_MCH_NAME =#{mainstayMchName,jdbcType=VARCHAR},
            </if>
            <if test="workCategoryCode != null and workCategoryCode !='' ">
                WORK_CATEGORY_CODE =#{workCategoryCode,jdbcType=VARCHAR},
            </if>
            <if test="workCategoryName != null and workCategoryName !='' ">
                WORK_CATEGORY_NAME =#{workCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceAmount != null">
                INVOICE_AMOUNT =#{invoiceAmount,jdbcType=DECIMAL},
            </if>
            <if test="invoiceCategoryCode != null">
                INVOICE_CATEGORY_CODE =#{invoiceCategoryCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceCategoryName != null">
                INVOICE_CATEGORY_NAME =#{invoiceCategoryName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS =#{invoiceStatus,jdbcType=SMALLINT},
            </if>
            <if test="invoiceFileUrl != null">
                INVOICE_FILE_URL =#{invoiceFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK =#{remark,jdbcType=VARCHAR},
            </if>
            <if test="jsonInfo != null">
                JSON_INFO =#{jsonInfo,jdbcType=OTHER},
            </if>
            <if test="errorDesc != null">
                ERROR_DESC =#{errorDesc,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        select count(1) from
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 查询条件 -->
    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="ids != null and ids.size() > 0">
            and ID IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="maxId != null">
            and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTimeBegin != null and createTimeEnd != null" >
            and CREATE_TIME between #{createTimeBegin,jdbcType=TIMESTAMP} and #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="completeTimeBegin != null and completeTimeEnd != null" >
            and COMPLETE_TIME between #{completeTimeBegin,jdbcType=TIMESTAMP} and #{completeTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="productNo != null and productNo !=''">
            and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
        </if>
        <if test="productName != null and productName !=''">
            and PRODUCT_NAME = #{productName,jdbcType=VARCHAR}
        </if>
        <if test="employerMchNo != null and employerMchNo !=''">
            and EMPLOYER_MCH_NO = #{employerMchNo,jdbcType=VARCHAR}
        </if>
        <if test="employerMchName != null and employerMchName !=''">
            and EMPLOYER_MCH_NAME like concat('%',#{employerMchName,jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayMchNo != null and mainstayMchNo !=''">
            and MAINSTAY_MCH_NO = #{mainstayMchNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayMchName != null and mainstayMchName !=''">
            and MAINSTAY_MCH_NAME = #{mainstayMchName,jdbcType=VARCHAR}
        </if>
        <if test="workCategoryCode != null and workCategoryCode !=''">
            and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test="workCategoryName != null and workCategoryName !=''">
            and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
        </if>
        <if test="invoiceAmount != null">
            and INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL}
        </if>
        <if test="invoiceCategoryCode != null and invoiceCategoryCode !=''">
            and INVOICE_CATEGORY_CODE = #{invoiceCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test="invoiceCategoryName != null and invoiceCategoryName !=''">
            and INVOICE_CATEGORY_NAME = #{invoiceCategoryName,jdbcType=VARCHAR}
        </if>
        <if test="invoiceStatus != null">
            and INVOICE_STATUS = #{invoiceStatus,jdbcType=SMALLINT}
        </if>
        <if test="ignoreInvoiceStatus != null">
            and INVOICE_STATUS != #{ignoreInvoiceStatus,jdbcType=SMALLINT}
        </if>
        <if test="invoiceFileUrl != null and invoiceFileUrl !=''">
            and INVOICE_FILE_URL = #{invoiceFileUrl,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark !=''">
            and REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="jsonInfo != null">
            and JSON_INFO = #{jsonInfo,jdbcType=OTHER}
        </if>
        <if test="errorDesc != null and errorDesc !=''">
            and ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
        </if>
    </sql>

    <!-- 计算预开票总金额和数量 -->
    <select id="countInvoiceAmount" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT IFNULL(SUM(INVOICE_AMOUNT),0) as invoiceAmount,count(1) as invoiceCount  FROM `tbl_invoice_pre_record`
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 根据用工企业编号和代征主体编号查询 -->
    <select id="getByEmployerAndMainstay" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_INVOICE_PRE_RECORD
        WHERE EMPLOYER_MCH_NO = #{employerMchNo} AND MAINSTAY_MCH_NO = #{mainstayMchNo}
    </select>

    <!-- 批量更新预开票状态 -->
    <update id="batchUpdateStatus">
        UPDATE TBL_INVOICE_PRE_RECORD
        SET INVOICE_STATUS = #{updateStatus},
            UPDATE_TIME = #{updateTime},
            COMPLETE_TIME = #{completeTime},
            VERSION = VERSION + 1
        <where>
            <if test="employerMchNo != null and employerMchNo != ''">AND EMPLOYER_MCH_NO = #{employerMchNo}</if>
            <if test="mainstayMchNo != null and mainstayMchNo != ''">AND MAINSTAY_MCH_NO = #{mainstayMchNo}</if>
            <if test="productNo != null and productNo != ''">AND PRODUCT_NO = #{productNo}</if>
            <if test="workCategoryCode != null and workCategoryCode != ''">AND WORK_CATEGORY_CODE = #{workCategoryCode}</if>
        </where>
    </update>

    <!-- 检查是否存在指定条件的预开票记录 -->
    <select id="existRecord" parameterType="java.util.Map" resultType="boolean">
        SELECT EXISTS (
            SELECT 1 FROM TBL_INVOICE_PRE_RECORD
            <where>
                <if test="employerMchNo != null and employerMchNo != ''">AND EMPLOYER_MCH_NO = #{employerMchNo}</if>
                <if test="mainstayMchNo != null and mainstayMchNo != ''">AND MAINSTAY_MCH_NO = #{mainstayMchNo}</if>
                <if test="productNo != null and productNo != ''">AND PRODUCT_NO = #{productNo}</if>
                <if test="workCategoryCode != null and workCategoryCode != ''">AND WORK_CATEGORY_CODE = #{workCategoryCode}</if>
                <if test="invoiceStatus != null">AND INVOICE_STATUS = #{invoiceStatus}</if>
            </where>
        )
    </select>
</mapper>