<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.CmbChangesFundsMapper">

    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.CmbChangesFunds">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="log_key" jdbcType="VARCHAR" property="logKey" />
        <result column="merchant_type" jdbcType="INTEGER" property="merchantType" />
        <result column="mch_no" jdbcType="VARCHAR" property="mchNo" />
        <result column="mch_name" jdbcType="VARCHAR" property="mchName" />
        <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo" />
        <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName" />
        <result column="plat_trx_no" jdbcType="VARCHAR" property="platTrxNo" />
        <result column="plat_batch_no" jdbcType="VARCHAR" property="platBatchNo" />
        <result column="amount_change_type" jdbcType="INTEGER" property="amountChangeType" />
        <result column="amount" jdbcType="DECIMAL" property="amount" />
        <result column="before_amount" jdbcType="DECIMAL" property="beforeAmount" />
        <result column="after_amount" jdbcType="DECIMAL" property="afterAmount" />
        <result column="frozen_amount" jdbcType="DECIMAL" property="frozenAmount" />
        <result column="before_frozen_amount" jdbcType="DECIMAL" property="beforeFrozenAmount" />
        <result column="after_frozen_amount" jdbcType="DECIMAL" property="afterFrozenAmount" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
    </resultMap>

    <sql id="Base_Column_List">
        id, version, log_key, merchant_type, mch_no, mch_name, mainstay_no,
        mainstay_name, plat_trx_no, plat_batch_no, amount_change_type, amount, before_amount, after_amount,
        frozen_amount, before_frozen_amount, after_frozen_amount, create_time, operator
    </sql>


    <insert id="insertBatch" parameterType="com.zhixianghui.facade.trade.entity.CmbChangesFunds">
        INSERT INTO tbl_cmb_changes_funds (version, log_key, merchant_type, mch_no, mch_name, mainstay_no,
        mainstay_name, plat_trx_no, plat_batch_no, amount_change_type, amount, before_amount, after_amount,
        frozen_amount, before_frozen_amount, after_frozen_amount, create_time, operator)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.version}, #{item.logKey}, #{item.merchantType}, #{item.mchNo}, #{item.mchName}, #{item.mainstayNo},
             #{item.mainstayName}, #{item.platTrxNo}, #{item.platBatchNo},
             #{item.amountChangeType}, #{item.amount}, #{item.beforeAmount},
             #{item.afterAmount}, #{item.frozenAmount}, #{item.beforeFrozenAmount},
             #{item.afterFrozenAmount}, #{item.createTime}, #{item.operator}
            )
        </foreach>
    </insert>
</mapper>