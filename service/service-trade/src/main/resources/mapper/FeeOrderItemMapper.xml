<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.FeeOrderItem">
    <sql id="table">tbl_fee_order_item</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.FeeOrderItem">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="create_date" property="createDate" jdbcType="DATE"/>
        <result column="fee_batch_no" property="feeBatchNo" jdbcType="VARCHAR"/>
        <result column="fee_item_no" property="feeItemNo" jdbcType="VARCHAR"/>
        <result column="fee_type" property="feeType" jdbcType="SMALLINT"/>
        <result column="employer_no" property="employerNo" jdbcType="VARCHAR"/>
        <result column="employer_name" property="employerName" jdbcType="VARCHAR"/>
        <result column="mainstay_no" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="mainstay_name" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="pay_amount" property="payAmount" jdbcType="DECIMAL"/>
        <result column="cetificate_url" property="cetificateUrl" jdbcType="VARCHAR"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="complete_time" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="fail_reason" property="failReason" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="fee_source" property="feeSource" jdbcType="SMALLINT"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, create_date,fee_batch_no, fee_item_no, fee_type, employer_no, employer_name, mainstay_no, mainstay_name, pay_amount, cetificate_url, pay_time, complete_time, fail_reason, status,fee_source
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.FeeOrderItem">
        INSERT INTO <include refid="table" /> (
            version,
            create_date,
            fee_batch_no,
            fee_item_no,
            fee_type,
            employer_no,
            employer_name,
            mainstay_no,
            mainstay_name,
            pay_amount,
            cetificate_url,
            pay_time,
            complete_time,
            fail_reason,
            status,
            fee_source
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{createDate,jdbcType=DATE},
            #{feeBatchNo,jdbcType=VARCHAR},
            #{feeItemNo,jdbcType=VARCHAR},
            #{feeType,jdbcType=SMALLINT},
            #{employerNo,jdbcType=VARCHAR},
            #{employerName,jdbcType=VARCHAR},
            #{mainstayNo,jdbcType=VARCHAR},
            #{mainstayName,jdbcType=VARCHAR},
            #{payAmount,jdbcType=DECIMAL},
            #{cetificateUrl,jdbcType=VARCHAR},
            #{payTime,jdbcType=TIMESTAMP},
            #{completeTime,jdbcType=TIMESTAMP},
            #{failReason,jdbcType=VARCHAR},
            #{status,jdbcType=SMALLINT},
            #{feeSource,jdbcType=SMALLINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            version,
            create_date,
            fee_batch_no,
            fee_item_no,
            fee_type,
            employer_no,
            employer_name,
            mainstay_no,
            mainstay_name,
            pay_amount,
            cetificate_url,
            pay_time,
            complete_time,
            fail_reason,
            status,
            fee_source
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.createDate,jdbcType=DATE},
            #{item.feeBatchNo,jdbcType=VARCHAR},
            #{item.feeItemNo,jdbcType=VARCHAR},
            #{item.feeType,jdbcType=SMALLINT},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.payAmount,jdbcType=DECIMAL},
            #{item.cetificateUrl,jdbcType=VARCHAR},
            #{item.payTime,jdbcType=TIMESTAMP},
            #{item.completeTime,jdbcType=TIMESTAMP},
            #{item.failReason,jdbcType=VARCHAR},
            #{item.status,jdbcType=SMALLINT},
            #{item.feeSource,jdbcType=SMALLINT}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.FeeOrderItem">
        UPDATE <include refid="table" /> SET
            version = #{version,jdbcType=SMALLINT},
            create_date = #{createDate,jdbcType=DATE},
            fee_batch_no = #{feeBatchNo,jdbcType=VARCHAR},
            fee_item_no = #{feeItemNo,jdbcType=VARCHAR},
            fee_type = #{feeType,jdbcType=SMALLINT},
            employer_no = #{employerNo,jdbcType=VARCHAR},
            employer_name = #{employerName,jdbcType=VARCHAR},
            mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            pay_amount = #{payAmount,jdbcType=DECIMAL},
            cetificate_url = #{cetificateUrl,jdbcType=VARCHAR},
            pay_time = #{payTime,jdbcType=TIMESTAMP},
            complete_time = #{completeTime,jdbcType=TIMESTAMP},
            fail_reason = #{failReason,jdbcType=VARCHAR},
            status = #{status,jdbcType=SMALLINT},
            fee_source = #{feeSource,jdbcType=SMALLINT}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.FeeOrderItem">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=DATE},
            </if>
            <if test="feeBatchNo != null">
                fee_batch_no = #{feeBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="feeItemNo != null">
                fee_item_no = #{feeItemNo,jdbcType=VARCHAR},
            </if>
            <if test="feeType != null">
                fee_type = #{feeType,jdbcType=SMALLINT},
            </if>
            <if test="employerNo != null">
                employer_no = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="employerName != null">
                employer_name = #{employerName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="cetificateUrl != null">
                cetificate_url = #{cetificateUrl,jdbcType=VARCHAR},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="completeTime != null">
                complete_time = #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="feeSource != null">
                fee_source = #{feeSource,jdbcType=SMALLINT},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createDate != null">
            and createDate = #{createDate,jdbcType=DATE}
        </if>
        <if test="feeBatchNo != null and feeBatchNo !=''">
            and fee_batch_no = #{feeBatchNo,jdbcType=VARCHAR}
        </if>
        <if test="feeItemNo != null and feeItemNo !=''">
            and fee_item_no = #{feeItemNo,jdbcType=VARCHAR}
        </if>
        <if test="feeType != null">
            and fee_type = #{feeType,jdbcType=SMALLINT}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and employer_no = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="employerName != null and employerName !=''">
            and employer_name = #{employerName,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and mainstay_name = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="payAmount != null">
            and pay_amount = #{payAmount,jdbcType=DECIMAL}
        </if>
        <if test="cetificateUrl != null and cetificateUrl !=''">
            and cetificate_url = #{cetificateUrl,jdbcType=VARCHAR}
        </if>
        <if test="payTime != null">
            and pay_time = #{payTime,jdbcType=TIMESTAMP}
        </if>
        <if test="completeTime != null">
            and complete_time = #{completeTime,jdbcType=TIMESTAMP}
        </if>
        <if test="failReason != null and failReason !=''">
            and fail_reason = #{failReason,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=SMALLINT}
        </if>
        <if test="feeSource != null">
            and fee_source = #{feeSource,jdbcType=SMALLINT}
        </if>
        <if test="beginDate != null and endDate != null" >
            and create_date between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach collection="statusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
    </sql>

</mapper>
