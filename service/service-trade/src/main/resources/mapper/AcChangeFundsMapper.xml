<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.AcChangeFunds">

  <sql id="table"> tbl_ac_changes_funds </sql>

  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.AcChangeFunds">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="version" jdbcType="SMALLINT" property="version" />
    <result column="log_key" jdbcType="VARCHAR" property="logKey" />
    <result column="merchant_type" jdbcType="SMALLINT" property="merchantType" />
    <result column="mch_no" jdbcType="VARCHAR" property="mchNo" />
    <result column="mch_name" jdbcType="VARCHAR" property="mchName" />
    <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName" />
    <result column="plat_trx_no" jdbcType="VARCHAR" property="platTrxNo" />
    <result column="amount_change_type" jdbcType="SMALLINT" property="amountChangeType" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="frozen_amount" jdbcType="BIGINT" property="frozenAmount" />
    <result column="settle_amount" jdbcType="BIGINT" property="settleAmount" />
    <result column="before_amount" jdbcType="BIGINT" property="beforeAmount" />
    <result column="before_frozen_amount" jdbcType="BIGINT" property="beforeFrozenAmount" />
    <result column="after_amount" jdbcType="BIGINT" property="afterAmount" />
    <result column="after_frozen_amount" jdbcType="BIGINT" property="afterFrozenAmount" />
    <result column="before_settle_amount" jdbcType="BIGINT" property="beforeSettleAmount" />
    <result column="after_settle_amount" jdbcType="BIGINT" property="afterSettleAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="pay_channel_no" jdbcType="VARCHAR" property="payChannelNo" />
    <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, version, log_key, merchant_type, mch_no, mch_name, mainstay_no, mainstay_name, 
    plat_trx_no, amount_change_type, amount, frozen_amount,settle_amount, before_amount, before_frozen_amount,
    after_amount, after_frozen_amount,before_settle_amount,after_settle_amount, create_time, operator, pay_channel_no, pay_channel_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from <include refid="table"/>
      where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from <include refid="table"/>
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <delete id="deleteBy" parameterType="java.util.Map">
    delete from <include refid="table"/>
    <where>
      <include refid="condition_sql"/>
    </where>
  </delete>

  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.AcChangeFunds">
    insert into <include refid="table"/> (version, log_key,
      merchant_type, mch_no, mch_name, 
      mainstay_no, mainstay_name, plat_trx_no, 
      amount_change_type, amount, frozen_amount,settle_amount,
      before_amount, before_frozen_amount, after_amount, 
      after_frozen_amount,before_settle_amount,after_settle_amount, create_time, operator,
      pay_channel_no, pay_channel_name)
    values (#{version,jdbcType=SMALLINT}, #{logKey,jdbcType=VARCHAR},
      #{merchantType,jdbcType=SMALLINT}, #{mchNo,jdbcType=VARCHAR}, #{mchName,jdbcType=VARCHAR},
      #{mainstayNo,jdbcType=VARCHAR}, #{mainstayName,jdbcType=VARCHAR}, #{platTrxNo,jdbcType=VARCHAR}, 
      #{amountChangeType,jdbcType=SMALLINT}, #{amount,jdbcType=BIGINT}, #{frozenAmount,jdbcType=BIGINT}, #{settleAmount,jdbcType=BIGINT},
      #{beforeAmount,jdbcType=BIGINT}, #{beforeFrozenAmount,jdbcType=BIGINT}, #{afterAmount,jdbcType=BIGINT},
      #{afterFrozenAmount,jdbcType=BIGINT},#{beforeSettleAmount,jdbcType=BIGINT},#{afterSettleAmount,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR},
      #{payChannelNo,jdbcType=VARCHAR}, #{payChannelName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.zhixianghui.facade.trade.entity.AcChangeFunds">
    insert into <include refid="table"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="logKey != null">
        log_key,
      </if>
      <if test="merchantType != null">
        merchant_type,
      </if>
      <if test="mchNo != null">
        mch_no,
      </if>
      <if test="mchName != null">
        mch_name,
      </if>
      <if test="mainstayNo != null">
        mainstay_no,
      </if>
      <if test="mainstayName != null">
        mainstay_name,
      </if>
      <if test="platTrxNo != null">
        plat_trx_no,
      </if>
      <if test="amountChangeType != null">
        amount_change_type,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="frozenAmount != null">
        frozen_amount,
      </if>
      <if test="settleAmount != null">
        settle_amount,
      </if>
      <if test="beforeAmount != null">
        before_amount,
      </if>
      <if test="beforeFrozenAmount != null">
        before_frozen_amount,
      </if>
      <if test="afterAmount != null">
        after_amount,
      </if>
      <if test="afterFrozenAmount != null">
        after_frozen_amount,
      </if>
      <if test="beforeSettleAmount != null">
        before_settle_amount,
      </if>
      <if test="afterSettleAmount != null">
        after_settle_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="payChannelNo != null">
        pay_channel_no,
      </if>
      <if test="payChannelName != null">
        pay_channel_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=SMALLINT},
      </if>
      <if test="logKey != null">
        #{logKey,jdbcType=VARCHAR},
      </if>
      <if test="merchantType != null">
        #{merchantType,jdbcType=SMALLINT},
      </if>
      <if test="mchNo != null">
        #{mchNo,jdbcType=VARCHAR},
      </if>
      <if test="mchName != null">
        #{mchName,jdbcType=VARCHAR},
      </if>
      <if test="mainstayNo != null">
        #{mainstayNo,jdbcType=VARCHAR},
      </if>
      <if test="mainstayName != null">
        #{mainstayName,jdbcType=VARCHAR},
      </if>
      <if test="platTrxNo != null">
        #{platTrxNo,jdbcType=VARCHAR},
      </if>
      <if test="amountChangeType != null">
        #{amountChangeType,jdbcType=SMALLINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="frozenAmount != null">
        #{frozenAmount,jdbcType=BIGINT},
      </if>
      <if test="settleAmount != null">
        #{settleAmount,jdbcType=BIGINT},
      </if>
      <if test="beforeAmount != null">
        #{beforeAmount,jdbcType=BIGINT},
      </if>
      <if test="beforeFrozenAmount != null">
        #{beforeFrozenAmount,jdbcType=BIGINT},
      </if>
      <if test="afterAmount != null">
        #{afterAmount,jdbcType=BIGINT},
      </if>
      <if test="afterFrozenAmount != null">
        #{afterFrozenAmount,jdbcType=BIGINT},
      </if>
      <if test="beforeSettleAmount != null">
        #{beforeSettleAmount,jdbcType=BIGINT},
      </if>
      <if test="afterSettleAmount != null">
        #{afterSettleAmount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="payChannelNo != null">
        #{payChannelNo,jdbcType=VARCHAR},
      </if>
      <if test="payChannelName != null">
        #{payChannelName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.AcChangeFunds">
    update <include refid="table"/>
    <set>
      <if test="version != null">
        version = #{version,jdbcType=SMALLINT},
      </if>
      <if test="logKey != null">
        log_key = #{logKey,jdbcType=VARCHAR},
      </if>
      <if test="merchantType != null">
        merchant_type = #{merchantType,jdbcType=SMALLINT},
      </if>
      <if test="mchNo != null">
        mch_no = #{mchNo,jdbcType=VARCHAR},
      </if>
      <if test="mchName != null">
        mch_name = #{mchName,jdbcType=VARCHAR},
      </if>
      <if test="mainstayNo != null">
        mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
      </if>
      <if test="mainstayName != null">
        mainstay_name = #{mainstayName,jdbcType=VARCHAR},
      </if>
      <if test="platTrxNo != null">
        plat_trx_no = #{platTrxNo,jdbcType=VARCHAR},
      </if>
      <if test="amountChangeType != null">
        amount_change_type = #{amountChangeType,jdbcType=SMALLINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="frozenAmount != null">
        frozen_amount = #{frozenAmount,jdbcType=BIGINT},
      </if>
      <if test="settleAmount != null">
        settle_amount = #{settleAmount,jdbcType=BIGINT},
      </if>
      <if test="beforeAmount != null">
        before_amount = #{beforeAmount,jdbcType=BIGINT},
      </if>
      <if test="beforeFrozenAmount != null">
        before_frozen_amount = #{beforeFrozenAmount,jdbcType=BIGINT},
      </if>
      <if test="afterAmount != null">
        after_amount = #{afterAmount,jdbcType=BIGINT},
      </if>
      <if test="afterFrozenAmount != null">
        after_frozen_amount = #{afterFrozenAmount,jdbcType=BIGINT},
      </if>

      <if test="beforeSettleAmount != null">
        before_settle_amount = #{beforeSettleAmount,jdbcType=BIGINT},
      </if>
      <if test="afterSettleAmount != null">
        after_settle_amount = #{afterSettleAmount,jdbcType=BIGINT},
      </if>

      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="payChannelNo != null">
        pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},
      </if>
      <if test="payChannelName != null">
        pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="update" parameterType="com.zhixianghui.facade.trade.entity.AcChangeFunds">
    update <include refid="table"/>
    set version = #{version,jdbcType=SMALLINT},
      log_key = #{logKey,jdbcType=VARCHAR},
      merchant_type = #{merchantType,jdbcType=SMALLINT},
      mch_no = #{mchNo,jdbcType=VARCHAR},
      mch_name = #{mchName,jdbcType=VARCHAR},
      mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
      mainstay_name = #{mainstayName,jdbcType=VARCHAR},
      plat_trx_no = #{platTrxNo,jdbcType=VARCHAR},
      amount_change_type = #{amountChangeType,jdbcType=SMALLINT},
      amount = #{amount,jdbcType=BIGINT},
      frozen_amount = #{frozenAmount,jdbcType=BIGINT},
      settle_amount = #{settleAmount,jdbcType=BIGINT},
      before_amount = #{beforeAmount,jdbcType=BIGINT},
      before_frozen_amount = #{beforeFrozenAmount,jdbcType=BIGINT},
      after_amount = #{afterAmount,jdbcType=BIGINT},
      after_frozen_amount = #{afterFrozenAmount,jdbcType=BIGINT},
      before_settle_amount = #{beforeSettleAmount},
      after_settle_amount = #{afterSettleAmount},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      operator = #{operator,jdbcType=VARCHAR},
      pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},
      pay_channel_name = #{payChannelName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <!-- 不分页查询 -->
  <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    <include refid="table"/>
    <where>
      <include refid="condition_sql" />
    </where>
    <choose>
      <when test="sortColumns != null and sortColumns !='' ">
        <![CDATA[ ORDER BY ${sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY ID DESC ]]>
      </otherwise>
    </choose>
  </select>

  <!-- 分页查询 -->

  <!-- 分页查询时计算总记录数 -->
  <select id="countBy" parameterType="java.util.Map" resultType="long">
    SELECT COUNT(1) FROM
    <include refid="table"/>
    <where>
      <include refid="condition_sql"/>
    </where>
  </select>


  <select id="groupByMainstayNo" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT mainstay_no,sum(settle_amount) settle_amount FROM
    <include refid="table"/>
    <where>
      <include refid="condition_sql"/>
    </where>
    group by mainstay_no
  </select>

  <!-- 根据id查询 -->
  <select id="getById" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List"/>
      FROM <include refid="table"/>
      WHERE ID = #{id,jdbcType=BIGINT}
  </select>

  <sql id="condition_sql">
      <if test="id != null">
        and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="version != null">
        and version = #{version,jdbcType=SMALLINT}
      </if>
      <if test="logKey != null and logKey !=''">
        and log_key = #{logKey,jdbcType=VARCHAR}
      </if>
      <if test="merchantType != null and merchantType != ''">
        and merchant_type = #{merchantType,jdbcType=VARCHAR}
      </if>
      <if test="mchNo != null and mchNo !=''">
        and mch_no = #{mchNo,jdbcType=VARCHAR}
      </if>
      <if test="mchName != null and mchName !=''">
        and mch_name like concat('%', #{mchName}, '%')
      </if>
      <if test="mainstayNo != null and mainstayNo !=''">
        and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
      </if>
      <if test="mainstayName != null and mainstayName !=''">
        and mainstay_name like concat('%', #{mainstayName}, '%')
      </if>
      <if test="platTrxNo != null and platTrxNo !=''">
        and plat_trx_no = #{platTrxNo,jdbcType=VARCHAR}
      </if>
      <if test="amountChangeType != null">
        and amount_change_type = #{amountChangeType,jdbcType=SMALLINT}
      </if>
      <if test="amount != null">
        and amount = #{amount,jdbcType=BIGINT}
      </if>
      <if test="frozenAmount != null">
        and frozen_amount = #{frozenAmount,jdbcType=BIGINT}
      </if>
      <if test="settleAmount != null">
        and settle_amount = #{settleAmount,jdbcType=BIGINT}
      </if>
      <if test="beforeAmount != null">
        and before_amount = #{beforeAmount,jdbcType=BIGINT}
      </if>
      <if test="beforeFrozenAmount != null">
        and before_frozen_amount = #{beforeFrozenAmount,jdbcType=BIGINT}
      </if>
      <if test="afterAmount != null">
        and after_amount = #{afterAmount,jdbcType=BIGINT}
      </if>
      <if test="afterFrozenAmount != null">
        and after_frozen_amount = #{afterFrozenAmount,jdbcType=BIGINT}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="createTimeBegin != null ">
        and create_time >= #{createTimeBegin}
      </if>
      <if test="createTimeEnd != null ">
        and create_time &lt;= #{createTimeEnd}
      </if>
      <if test="operator != null and operator !=''">
        and operator = #{operator,jdbcType=VARCHAR}
      </if>
      <if test="payChannelNo != null and payChannelNo !=''">
        and pay_channel_no = #{payChannelNo,jdbcType=VARCHAR}
      </if>
      <if test="payChannelName != null and payChannelName !=''">
        and pay_channel_name = #{payChannelName,jdbcType=VARCHAR}
      </if>

      <if test="amountChangeTypeList != null">
        and amount_change_type IN
        <foreach collection="amountChangeTypeList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
  </sql>
  
  <select id="repairRecord" parameterType="java.util.Map" resultMap="BaseResultMap">
    select * from tbl_ac_changes_funds
    where plat_trx_no not in (
    select plat_trx_no FROM `tbl_order_item` where create_time >= #{createTimeBegin} and
    create_time &lt;= #{createTimeEnd} and ORDER_ITEM_STATUS = 100  and pay_channel_no = 'JOINPAY'
    <if test="mainstayNo != null and mainstayNo !=''">
      and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
    </if>
    )
    <if test="mainstayNo != null and mainstayNo !=''">
      and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
    </if>
    and pay_channel_no = 'JOINPAY' and create_time >= #{createTimeBegin} and create_time &lt;= #{createTimeEnd}
    and amount_change_type = 700
  </select>


  <select id="refundSettle" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT mainstay_no,mainstay_name,sum(settle_amount) settle_amount FROM `tbl_ac_changes_funds`
    where
    create_time >= #{createTimeBegin} and create_time &lt;= #{createTimeEnd}
    AND amount_change_type = 800
    <if test="mainstayNo != null and mainstayNo !=''">
        and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
    </if>
    group by mainstay_no,mainstay_name
  </select>

</mapper>