<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.OfflineOrderMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.OfflineOrder">
    <!--@mbg.generated-->
    <!--@Table tbl_offline_order-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <id column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="VERSION" jdbcType="SMALLINT" property="version" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CONFIRM_TIME" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="COMPLETE_TIME" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="MCH_BATCH_NO" jdbcType="VARCHAR" property="mchBatchNo" />
    <result column="BATCH_NAME" jdbcType="VARCHAR" property="batchName" />
    <result column="PLAT_BATCH_NO" jdbcType="VARCHAR" property="platBatchNo" />
    <result column="PRODUCT_NO" jdbcType="VARCHAR" property="productNo" />
    <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
    <result column="JOB_ID" jdbcType="VARCHAR" property="jobId" />
    <result column="JOB_NAME" jdbcType="VARCHAR" property="jobName" />
    <result column="EMPLOYER_NO" jdbcType="VARCHAR" property="employerNo" />
    <result column="EMPLOYER_NAME" jdbcType="VARCHAR" property="employerName" />
    <result column="MAINSTAY_NO" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="MAINSTAY_NAME" jdbcType="VARCHAR" property="mainstayName" />
    <result column="BATCH_STATUS" jdbcType="SMALLINT" property="batchStatus" />
    <result column="WORK_CATEGORY_CODE" jdbcType="VARCHAR" property="workCategoryCode" />
    <result column="WORK_CATEGORY_NAME" jdbcType="VARCHAR" property="workCategoryName" />
    <result column="SERVICE_DESC" jdbcType="VARCHAR" property="serviceDesc" />
    <result column="REQUEST_COUNT" jdbcType="INTEGER" property="requestCount" />
    <result column="REQUEST_TASK_AMOUNT" jdbcType="DECIMAL" property="requestTaskAmount" />
    <result column="REQUEST_NET_AMOUNT" jdbcType="DECIMAL" property="requestNetAmount" />
    <result column="ACCEPTED_COUNT" jdbcType="INTEGER" property="acceptedCount" />
    <result column="ACCEPTED_TASK_AMOUNT" jdbcType="DECIMAL" property="acceptedTaskAmount" />
    <result column="ACCEPTED_NET_AMOUNT" jdbcType="DECIMAL" property="acceptedNetAmount" />
    <result column="ACCEPTED_FEE" jdbcType="DECIMAL" property="acceptedFee" />
    <result column="ACCEPTED_ORDER_AMOUNT" jdbcType="DECIMAL" property="acceptedOrderAmount" />
    <result column="ACCEPTED_TAX_AMOUNT" jdbcType="DECIMAL" property="acceptedTaxAmount" />
    <result column="SUCCESS_COUNT" jdbcType="INTEGER" property="successCount" />
    <result column="SUCCESS_TASK_AMOUNT" jdbcType="DECIMAL" property="successTaskAmount" />
    <result column="SUCCESS_NET_AMOUNT" jdbcType="DECIMAL" property="successNetAmount" />
    <result column="SUCCESS_FEE" jdbcType="DECIMAL" property="successFee" />
    <result column="SUCCESS_TAX_AMOUNT" jdbcType="DECIMAL" property="successTaxAmount" />
    <result column="FAIL_COUNT" jdbcType="INTEGER" property="failCount" />
    <result column="FAIL_TASK_AMOUNT" jdbcType="DECIMAL" property="failTaskAmount" />
    <result column="FAIL_NET_AMOUNT" jdbcType="DECIMAL" property="failNetAmount" />
    <result column="ERROR_CODE" jdbcType="VARCHAR" property="errorCode" />
    <result column="ERROR_DESC" jdbcType="VARCHAR" property="errorDesc" />
    <result column="LAUNCH_WAY" jdbcType="SMALLINT" property="launchWay" />
    <result column="CALLBACK_URL" jdbcType="VARCHAR" property="callbackUrl" />
    <result column="JSON_STR" jdbcType="VARCHAR" property="jsonStr" />
    <result column="IS_DELETE" jdbcType="SMALLINT" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CREATE_TIME, VERSION, UPDATE_TIME, CONFIRM_TIME, COMPLETE_TIME, MCH_BATCH_NO,
    BATCH_NAME, PLAT_BATCH_NO, PRODUCT_NO, PRODUCT_NAME, JOB_ID, JOB_NAME, EMPLOYER_NO,
    EMPLOYER_NAME, MAINSTAY_NO, MAINSTAY_NAME, BATCH_STATUS, WORK_CATEGORY_CODE, WORK_CATEGORY_NAME,
    SERVICE_DESC, REQUEST_COUNT, REQUEST_TASK_AMOUNT, REQUEST_NET_AMOUNT, ACCEPTED_COUNT,
    ACCEPTED_TASK_AMOUNT, ACCEPTED_NET_AMOUNT, ACCEPTED_FEE, ACCEPTED_ORDER_AMOUNT, ACCEPTED_TAX_AMOUNT,
    SUCCESS_COUNT, SUCCESS_TASK_AMOUNT, SUCCESS_NET_AMOUNT, SUCCESS_FEE, SUCCESS_TAX_AMOUNT,
    FAIL_COUNT, FAIL_TASK_AMOUNT, FAIL_NET_AMOUNT, ERROR_CODE, ERROR_DESC, LAUNCH_WAY,
    CALLBACK_URL, JSON_STR, IS_DELETE
  </sql>

  <!-- 账单查询 -->
  <select id="selectFeeOrder" resultType="com.zhixianghui.facade.trade.entity.FeeOrderBatch">
    SELECT
    EMPLOYER_NO,
    EMPLOYER_NAME,
    MAINSTAY_NO,
    MAINSTAY_NAME,
    PRODUCT_NO,
    PRODUCT_NAME,
    COUNT(*) order_batch_count,
    sum(REQUEST_COUNT) order_item_count,
    sum( SUCCESS_COUNT ) success_count,
    sum(FAIL_COUNT) fail_count,
    sum(SUCCESS_TASK_AMOUNT) task_amount,
    sum(SUCCESS_NET_AMOUNT) order_net_amount,
    sum(SUCCESS_FEE) fee_amount,
    sum(SUCCESS_TAX_AMOUNT) tax_amount
    FROM
    `tbl_offline_order`
    <where>
      <include refid="condition_sql" />
    </where>
    GROUP BY
    EMPLOYER_NO,
    EMPLOYER_NAME,
    MAINSTAY_NO,
    MAINSTAY_NAME,
    PRODUCT_NO,
    PRODUCT_NAME
  </select>


  <sql id="condition_sql">
    <if test="id != null">
      and ID = #{id,jdbcType=BIGINT}
    </if>
    <if test="version != null">
      and VERSION = #{version,jdbcType=SMALLINT}
    </if>
    <if test="createDate != null">
      and CREATE_DATE = #{createDate,jdbcType=DATE}
    </if>
    <if test="createTime != null">
      and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
    </if>
    <if test="updateTime != null">
      and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    </if>
    <if test="confirmTime != null">
      and CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP}
    </if>
    <if test="completeTime != null">
      and COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP}
    </if>
    <if test="mchBatchNo != null and mchBatchNo !=''">
      and MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR}
    </if>
    <if test="batchName != null and batchName !=''">
      and BATCH_NAME = #{batchName,jdbcType=VARCHAR}
    </if>
    <if test="platBatchNo != null and platBatchNo !=''">
      and PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
    </if>
    <if test="productNo != null and productNo != ''">
      and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
    </if>
    <if test="productName != null and productName != ''">
      and PRODUCT_NAME = #{productName,jdbcType=VARCHAR}
    </if>
    <if test="jobId != null and jobId != ''">
      and JOB_ID = #{jobId,jdbcType=VARCHAR}
    </if>
    <if test="jobName != null and jobName != ''">
      and JOB_NAME = #{jobName,jdbcType=VARCHAR}
    </if>
    <if test="employerNo != null and employerNo !=''">
      and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
    </if>
    <if test="employerName != null and employerName !=''">
      and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
    </if>
    <if test="mainstayNo != null and mainstayNo !=''">
      and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
    </if>
    <if test="mainstayName != null and mainstayName !=''">
      and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
    </if>
    <if test="channelType != null">
      and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
    </if>
    <if test="payChannelNo != null and payChannelNo !=''">
      and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
    </if>
    <if test="channelName != null and channelName !=''">
      and CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
    </if>
    <if test="batchStatus != null">
      and BATCH_STATUS = #{batchStatus,jdbcType=SMALLINT}
    </if>
    <if test="workCategoryCode != null and workCategoryCode != ''">
      and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
    </if>
    <if test="workCategoryName != null and workCategoryName !=''">
      and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
    </if>
    <if test="serviceDesc != null and serviceDesc !=''">
      and SERVICE_DESC = #{serviceDesc,jdbcType=VARCHAR}
    </if>
    <if test="requestCount != null">
      and REQUEST_COUNT = #{requestCount,jdbcType=INTEGER}
    </if>
    <if test="requestNetAmount != null">
      and REQUEST_NET_AMOUNT = #{requestNetAmount,jdbcType=DECIMAL}
    </if>
    <if test="acceptedCount != null">
      and ACCEPTED_COUNT = #{acceptedCount,jdbcType=INTEGER}
    </if>
    <if test="acceptedNetAmount != null">
      and ACCEPTED_NET_AMOUNT = #{acceptedNetAmount,jdbcType=DECIMAL}
    </if>
    <if test="acceptedFee != null">
      and ACCEPTED_FEE = #{acceptedFee,jdbcType=DECIMAL}
    </if>
    <if test="acceptedOrderAmount != null">
      and ACCEPTED_ORDER_AMOUNT = #{acceptedOrderAmount,jdbcType=DECIMAL}
    </if>
    <if test="successCount != null">
      and SUCCESS_COUNT = #{successCount,jdbcType=INTEGER}
    </if>
    <if test="successNetAmount != null">
      and SUCCESS_NET_AMOUNT = #{successNetAmount,jdbcType=DECIMAL}
    </if>
    <if test="successFee != null">
      and SUCCESS_FEE = #{successFee,jdbcType=DECIMAL}
    </if>
    <if test="failCount != null">
      and FAIL_COUNT = #{failCount,jdbcType=INTEGER}
    </if>
    <if test="failNetAmount != null">
      and FAIL_NET_AMOUNT = #{failNetAmount,jdbcType=DECIMAL}
    </if>
    <if test="errorCode != null and errorCode !=''">
      and ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
    </if>
    <if test="errorDesc != null and errorDesc !=''">
      and ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
    </if>
    <if test="launchWay != null">
      and LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT}
    </if>
    <if test="callbackUrl != null and callbackUrl !=''">
      and CALLBACK_URL = #{callbackUrl,jdbcType=VARCHAR}
    </if>
    <if test="jsonStr != null and jsonStr !=''">
      and JSON_STR = #{jsonStr,jdbcType=OTHER}
    </if>


    <!--  自定义  -->
    <if test="maxId != null">
      and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="batchNameLike != null and batchNameLike !='' ">
      AND BATCH_NAME LIKE CONCAT('%', CONCAT(#{batchNameLike}, '%'))
    </if>
    <if test="employerNameLike != null and employerNameLike !='' ">
      AND EMPLOYER_NAME LIKE CONCAT('%', CONCAT(#{employerNameLike}, '%'))
    </if>
    <if test="createBeginDate != null and createEndDate != null" >
      and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
    </if>
    <if test="completeBeginDate != null and completeEndDate != null" >
      and COMPLETE_TIME between #{completeBeginDate,jdbcType=TIMESTAMP} and #{completeEndDate,jdbcType=TIMESTAMP}
    </if>
    <if test="createDateList != null and createDateList.size() > 0">
      and CREATE_DATE IN
      <foreach collection="createDateList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=DATE}</foreach>
    </if>

    <if test="productNos != null and productNos.size() > 0">
      and PRODUCT_NO IN
      <foreach collection="productNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
    </if>
    <!--  分表字段区间  -->
    <if test="beginDate != null and endDate != null" >
      and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
    </if>
    <if test="isDelete != null " >
      and (is_delete = #{isDelete} or is_delete is null)
    </if>
    <if test="ignoreZeroAmt !=null and ignoreZeroAmt !='' ">
      and SUCCESS_NET_AMOUNT <![CDATA[ > ]]> 0
    </if>
  </sql>

  <select id="sumWaitInvoiceAmount" resultType="java.math.BigDecimal">
    SELECT SUM(COALESCE(SUCCESS_FEE,0)) as total
    FROM tbl_offline_order
    <where>
      <include refid="condition_sql" />
    </where>
  </select>

  <select id="selectOrderNo" parameterType="java.util.Map" resultType="java.lang.String">
    SELECT PLAT_BATCH_NO
    FROM
    tbl_offline_order
    <where>
      <include refid="condition_sql" />
    </where>
    group by PLAT_BATCH_NO
  </select>
</mapper>
