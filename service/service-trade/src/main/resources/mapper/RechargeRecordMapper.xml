<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.RechargeRecord">

  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.RechargeRecord">
    <!--@mbg.generated-->
    <!--@Table tbl_recharge_record-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="VERSION" jdbcType="INTEGER" property="version" />
    <result column="RECHARGE_ORDER_ID" jdbcType="VARCHAR" property="rechargeOrderId" />
    <result column="RECHARGE_AMOUNT" jdbcType="DECIMAL" property="rechargeAmount" />
    <result column="EXPIRE_TIME" jdbcType="TIMESTAMP" property="expireTime"/>
    <result column="PAYEE_IDENTITY" jdbcType="VARCHAR" property="payeeIdentity" />
    <result column="PAYEE_IDENTITY_TYPE" jdbcType="VARCHAR" property="payeeIdentityType" />
    <result column="PAYEE_AGREEMENT_NO" jdbcType="VARCHAR" property="payeeAgreementNo" />
    <result column="CHANNEL_ORDER_ID" jdbcType="VARCHAR" property="channelOrderId" />
    <result column="TRANS_PAY_TIME" jdbcType="TIMESTAMP" property="transPayTime" />
    <result column="EMPLOYER_NO" jdbcType="VARCHAR" property="employerNo" />
    <result column="EMPLOYER_NAME" jdbcType="VARCHAR" property="employerName" />
    <result column="MAINSTAY_NO" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="MAINSTAY_NAME" jdbcType="VARCHAR" property="mainstayName" />
    <result column="ACCOUNT_BOOK_ID" jdbcType="VARCHAR" property="accountBookId" />
    <result column="RECHARGE_STATUS" jdbcType="SMALLINT" property="rechargeStatus" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_BY" jdbcType="VARCHAR" property="createBy" />
    <result column="CHANNEL_CODE" jdbcType="VARCHAR" property="channelCode" />
    <result column="CHANNEL_NAME" jdbcType="VARCHAR" property="channelName" />
    <result column="CHANNEL_TYPE" jdbcType="SMALLINT" property="channelType" />
    <result column="CHANNEL_TRX_NO" jdbcType="VARCHAR" property="channelTrxNo" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="RECHARGE_TYPE" jdbcType="INTEGER" property="rechargeType" />
    <result column="RECEIPT_URL" jdbcType="VARCHAR" property="receiptUrl" />
    <result column="PAYER_NAME" jdbcType="VARCHAR" property="payerName" />
    <result column="PAYER_IDENTITY" jdbcType="VARCHAR" property="payerIdentity" />
    <result column="PAYER_IDENTITY_TYPE" jdbcType="VARCHAR" property="payerIdentityType" />
    <result column="PAYER_BANK_NAME" jdbcType="VARCHAR" property="payerBankName" />
    <result column="PAYEE_NAME" jdbcType="VARCHAR" property="payeeName" />
    <result column="CURRENT_BALANCE" jdbcType="VARCHAR" property="currentBalance" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, VERSION, RECHARGE_ORDER_ID, RECHARGE_AMOUNT, EXPIRE_TIME, PAYEE_IDENTITY, PAYEE_IDENTITY_TYPE,
    PAYEE_AGREEMENT_NO, CHANNEL_ORDER_ID, TRANS_PAY_TIME, EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NO,MAINSTAY_NAME,ACCOUNT_BOOK_ID,
    RECHARGE_STATUS,CREATE_TIME, UPDATE_TIME, CREATE_BY, CHANNEL_CODE, CHANNEL_NAME, CHANNEL_TYPE,CHANNEL_TRX_NO,REMARK,RECHARGE_TYPE,RECEIPT_URL, PAYER_NAME, PAYER_IDENTITY, PAYER_IDENTITY_TYPE, PAYER_BANK_NAME,
    PAYEE_NAME,CURRENT_BALANCE
  </sql>

  <!--更新-->
  <update id="update" parameterType="com.zhixianghui.facade.trade.entity.RechargeRecord">
    UPDATE tbl_recharge_record
    <set>
      VERSION = #{version,jdbcType=INTEGER} + 1,
      RECHARGE_ORDER_ID = #{rechargeOrderId,jdbcType=VARCHAR},
      RECHARGE_AMOUNT = #{rechargeAmount,jdbcType=DECIMAL},
      EXPIRE_TIME = #{expireTime,jdbcType=TIMESTAMP},
      PAYEE_IDENTITY = #{payeeIdentity,jdbcType=VARCHAR},
      PAYEE_IDENTITY_TYPE = #{payeeIdentityType,jdbcType=VARCHAR},
      PAYEE_AGREEMENT_NO = #{payeeAgreementNo,jdbcType=VARCHAR},
      CHANNEL_ORDER_ID = #{channelOrderId,jdbcType=VARCHAR},
      TRANS_PAY_TIME = #{transPayTime,jdbcType=TIMESTAMP},
      EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
      EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CREATE_BY = #{createBy,jdbcType=VARCHAR},
      CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR},
      CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
      CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
      MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
      MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
      ACCOUNT_BOOK_ID = #{accountBookId,jdbcType=VARCHAR},
      RECHARGE_STATUS = #{rechargeStatus,jdbcType=SMALLINT},
      CHANNEL_TRX_NO = #{channelTrxNo,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      RECHARGE_TYPE = #{rechargeType,jdbcType=INTEGER},
      RECEIPT_URL = #{receiptUrl,jdbcType=VARCHAR},
      PAYER_NAME = #{payerName,jdbcType=VARCHAR},
      PAYER_IDENTITY = #{payerIdentity,jdbcType=VARCHAR},
      PAYER_IDENTITY_TYPE = #{payerIdentityType,jdbcType=VARCHAR},
      PAYER_BANK_NAME = #{payerBankName,jdbcType=VARCHAR},
      PAYEE_NAME = #{payeeName,jdbcType=VARCHAR},
      CURRENT_BALANCE = #{currentBalance,jdbcType=DECIMAL}
    </set>
    WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
  </update>

  <sql id="condition_sql">
    <if test="id != null">
      and ID = #{id,jdbcType=BIGINT}
    </if>
    <if test="maxId != null">
      AND ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="version != null">
      and VERSION = #{version,jdbcType=SMALLINT}
    </if>
    <if test="rechargeOrderId != null and rechargeOrderId != ''">
      and RECHARGE_ORDER_ID = #{rechargeOrderId,jdbcType=VARCHAR}
    </if>
    <if test="employerNo != null and employerNo !=''">
      and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
    </if>
    <if test="employerName != null and employerName !=''">
      and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
    </if>
    <if test="mainstayNo != null and mainstayNo !=''">
      and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
    </if>
    <if test="mainstayName != null and mainstayName !=''">
      and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
    </if>
    <if test="channelType != null">
      and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
    </if>
    <if test="channelCode != null and channelCode !=''">
      and CHANNEL_CODE = #{channelCode,jdbcType=VARCHAR}
    </if>
    <if test="rechargeType != null">
      and RECHARGE_TYPE = #{rechargeType,jdbcType=INTEGER}
    </if>
    <if test="rechargeStatus != null">
      and RECHARGE_STATUS = #{rechargeStatus,jdbcType=SMALLINT}
    </if>
    <if test="channelTrxNo !=null and channelTrxNo !=''">
      and CHANNEL_TRX_NO = #{channelTrxNo,jdbcType=VARCHAR}
    </if>
    <if test="employerNameLike != null and employerNameLike !='' ">
      AND EMPLOYER_NAME LIKE CONCAT('%', CONCAT(#{employerNameLike}, '%'))
    </if>
    <if test="createBeginDate != null and createEndDate != null " >
      and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
    </if>
  </sql>



  <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM tbl_recharge_record
    <where>
      <include refid="condition_sql" />
    </where>
    <choose>
      <when test="sortColumns != null and sortColumns !='' ">
        <![CDATA[ ORDER BY ${sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY ID DESC ]]>
      </otherwise>
    </choose>
  </select>

  <select id="countBy" parameterType="java.util.Map" resultType="long">
    SELECT COUNT(1) FROM tbl_recharge_record
    <where>
      <include refid="condition_sql" />
    </where>
  </select>

  <select id="countRechargeAmount" parameterType="java.util.Map" resultType="java.util.Map">
    select IFNULL(SUM(RECHARGE_AMOUNT),0) as chargeAmount,COUNT(1) as chargeCount from tbl_recharge_record
    <where>
      <include refid="condition_sql"/>
    </where>
  </select>

  <select id="sumCmbRechargeAmt" parameterType="java.util.Map" resultType="java.util.Map">
    select t.EMPLOYER_NO employerNo,t.MAINSTAY_NO mainstayNo,sum(t.RECHARGE_AMOUNT) sumRechargeAmt
    from tbl_recharge_record t
    where t.CHANNEL_CODE ='CMB' and RECHARGE_STATUS =1 and PAYER_IDENTITY is not null
      and t.EMPLOYER_NO = #{employerNo}
      and t.MAINSTAY_NO = #{mainstayNo}
      and t.UPDATE_TIME <![CDATA[ >= ]]> #{completeBeginDate}
      and t.UPDATE_TIME <![CDATA[ <= ]]> #{completeEndDate}
    group by t.EMPLOYER_NO,t.MAINSTAY_NO
  </select>

  <select id="sumRechargeRecord" parameterType="java.util.Map" resultType="java.util.Map">
    select
    sum(ifnull(t.RECHARGE_AMOUNT,0)) sumAmt,
    count(id) totalNum
    from tbl_recharge_record t
    <where>
      <include refid="condition_sql" />
    </where>
  </select>

  <select id="isRechargeRecordExist" parameterType="java.util.Map" resultType="java.lang.Long">
    select ID from tbl_recharge_record t
    <where>
      <include refid="condition_sql" />
    </where>
    limit 1
  </select>
</mapper>
