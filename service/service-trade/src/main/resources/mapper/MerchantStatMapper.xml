<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.MerchantStat">
    <sql id="table">tbl_merchant_stat</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.MerchantStat">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="ORDER_ITEM_NET_AMOUNT" property="orderItemNetAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_ITEM_AMOUNT" property="orderItemAmount" jdbcType="DECIMAL"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="BIGINT"/>
        <result column="RECEIVER_NUMBER" property="receiverNumber" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR"/>
        <result column="SALE_ID" property="saleId" jdbcType="BIGINT"/>
        <result column="SALE_NAME" property="saleName" jdbcType="VARCHAR"/>
        <result column="FIRST_ORDER_TIME" property="firstOrderTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NO, MAINSTAY_NAME, ORDER_ITEM_NET_AMOUNT, ORDER_ITEM_AMOUNT, ORDER_AMOUNT, RECEIVER_NUMBER, CREATE_TIME, CREATE_DATE, SALE_ID, SALE_NAME,FIRST_ORDER_TIME
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.MerchantStat">
        INSERT INTO <include refid="table" /> (
            VERSION,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            ORDER_ITEM_NET_AMOUNT,
            ORDER_ITEM_AMOUNT,
            ORDER_AMOUNT,
            RECEIVER_NUMBER,
            CREATE_TIME,
            CREATE_DATE,
            ENCRYPT_KEY_ID,
            UPDATE_TIME,
            SALE_ID,
            SALE_NAME,
            FIRST_ORDER_TIME
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{employerNo,jdbcType=VARCHAR},
            #{employerName,jdbcType=VARCHAR},
            #{mainstayNo,jdbcType=VARCHAR},
            #{mainstayName,jdbcType=VARCHAR},
            #{orderItemNetAmount,jdbcType=DECIMAL},
            #{orderItemAmount,jdbcType=DECIMAL},
            #{orderAmount,jdbcType=BIGINT},
            #{receiverNumber,jdbcType=BIGINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{createDate,jdbcType=VARCHAR},
            #{encryptKeyId,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{saleId,jdbcType=BIGINT},
            #{saleName,jdbcType=VARCHAR},
            #{firstOrderTime,jdbcType=TIMESTAMP}
        )
        ON DUPLICATE KEY UPDATE
            UPDATE_TIME = values(UPDATE_TIME),
            ORDER_ITEM_NET_AMOUNT = values(ORDER_ITEM_NET_AMOUNT),
            ORDER_ITEM_AMOUNT = values(ORDER_ITEM_AMOUNT),
            ORDER_AMOUNT = values(ORDER_AMOUNT),
            RECEIVER_NUMBER = values(RECEIVER_NUMBER),
            SALE_ID = values(SALE_ID),
            SALE_NAME = values(SALE_NAME),
            FIRST_ORDER_TIME = #{firstOrderTime,jdbcType=TIMESTAMP}
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            VERSION,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            ORDER_ITEM_NET_AMOUNT,
            ORDER_ITEM_AMOUNT,
            ORDER_AMOUNT,
            RECEIVER_NUMBER,
            CREATE_TIME,
            CREATE_DATE,
            SALE_ID,
            SALE_NAME,
            FIRST_ORDER_TIME
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.orderItemNetAmount,jdbcType=DECIMAL},
            #{item.orderItemAmount,jdbcType=DECIMAL},
            #{item.orderAmount,jdbcType=BIGINT},
            #{item.receiverNumber,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createDate,jdbcType=VARCHAR},
            #{saleId,jdbcType=BIGINT},
            #{saleName,jdbcType=VARCHAR},
            #{firstOrderTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.MerchantStat">
        UPDATE <include refid="table" /> SET
            VERSION = #{version,jdbcType=SMALLINT} +1,
            EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
            MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL},
            ORDER_ITEM_AMOUNT = #{orderItemAmount,jdbcType=DECIMAL},
            ORDER_AMOUNT = #{orderAmount,jdbcType=BIGINT},
            RECEIVER_NUMBER = #{receiverNumber,jdbcType=BIGINT},
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            CREATE_DATE = #{createDate,jdbcType=VARCHAR},
            SALE_ID =  #{saleId,jdbcType=BIGINT},
            SALE_NAME = #{saleName,jdbcType=VARCHAR},
            FIRST_ORDER_TIME = #{firstOrderTime,jdbcType=TIMESTAMP}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.MerchantStat">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="employerNo != null">
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="employerName != null">
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="orderItemNetAmount != null">
                ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL},
            </if>
            <if test="orderItemAmount != null">
                ORDER_ITEM_AMOUNT = #{orderItemAmount,jdbcType=DECIMAL},
            </if>
            <if test="orderAmount != null">
                ORDER_AMOUNT = #{orderAmount,jdbcType=BIGINT},
            </if>
            <if test="receiverNumber != null">
                RECEIVER_NUMBER = #{receiverNumber,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=VARCHAR}
            </if>
            <if test="saleId != null">
                SALE_ID = #{saleId,jdbcType=BIGINT},
            </if>
            <if test="saleName != null">
                SALE_NAME = #{saleName,jdbcType=VARCHAR}
            </if>
            <if test="firstOrderTime != null">
                FIRST_ORDER_TIME = #{firstOrderTime,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <select id="merchantStat" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->



    <select id="groupBySaleId" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.vo.MerchantStatVo">
        select EMPLOYER_NO,MAINSTAY_NO from tbl_merchant_stat where 1=1
        <if test="saleId != null">
            and SALE_ID = #{saleId,jdbcType=BIGINT},
        </if>
        <if test="saleIds != null and saleIds.size() > 0">
            and SALE_ID in
            <foreach collection="saleIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>

        group by EMPLOYER_NO,MAINSTAY_NO
    </select>

    <select id="merchantStatCount" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.vo.MerchantStatVo">
        select
            count(distinct EMPLOYER_NO) as employerCount,
            sum(ORDER_AMOUNT) as orderCount,
            sum(ORDER_ITEM_NET_AMOUNT) as orderItemNetAmountSum,
            sum(ORDER_ITEM_AMOUNT) as orderAmountSum
        from tbl_merchant_stat where 1=1
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo, jdbcType=VARCHAR}
        </if>
        <if test="employerList != null and employerList.size() > 0">
            and EMPLOYER_NO in
            <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo, jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        <if test="beginDate != null and endDate != null" >
            and CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and CREATE_DATE  &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
        <if test="saleId != null and saleId !=''">
            and SALE_ID = #{saleId,jdbcType=BIGINT}
        </if>
        <if test="saleName != null and saleName !=''">
            and SALE_NAME = #{saleName,jdbcType=VARCHAR}
        </if>
        <if test="firstOrderTime != null">
            and FIRST_ORDER_TIME = #{firstOrderTime,jdbcType=TIMESTAMP}
        </if>
        <if test="saleIds != null and saleIds.size() > 0">
            and SALE_ID in
            <foreach collection="saleIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="employerList != null and employerList.size() > 0">
            and EMPLOYER_NO in
            <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        <if test="orderItemNetAmount != null">
            and ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL}
        </if>
        <if test="orderItemAmount != null">
            and ORDER_ITEM_AMOUNT = #{orderItemAmount,jdbcType=DECIMAL}
        </if>
        <if test="orderAmount != null">
            and ORDER_AMOUNT = #{orderAmount,jdbcType=BIGINT}
        </if>
        <if test="receiverNumber != null">
            and RECEIVER_NUMBER = #{receiverNumber,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createDate != null and createDate !=''">
            and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
        </if>
        <!-- 自定义 -->
        <if test="beginDate != null and endDate != null" >
            and CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and CREATE_DATE  &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
        <if test="saleId != null and saleId !=''">
            and SALE_ID = #{saleId,jdbcType=BIGINT}
        </if>
        <if test="saleName != null and saleName !=''">
            and SALE_NAME = #{saleName,jdbcType=VARCHAR}
        </if>
        <if test="firstOrderTime != null">
            and FIRST_ORDER_TIME = #{firstOrderTime,jdbcType=TIMESTAMP}
        </if>
        <if test="saleIds != null and saleIds.size() > 0">
            and SALE_ID in
            <foreach collection="saleIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="maxId != null">
            and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
    </sql>

</mapper>
