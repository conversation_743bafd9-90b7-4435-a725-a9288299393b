<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.YishuiUsersMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.YishuiUsers">
    <!--@mbg.generated-->
    <!--@Table tbl_yishui_users-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cer_code" jdbcType="VARCHAR" property="cerCode" />
    <result column="cer_code_md5" jdbcType="VARCHAR" property="cerCodeMd5" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="account_no_md5" jdbcType="VARCHAR" property="accountNoMd5" />
    <result column="contract_img" jdbcType="VARCHAR" property="contractImg" />
    <result column="enterprise_professional_facilitator_id" jdbcType="VARCHAR" property="enterpriseProfessionalFacilitatorId" />
    <result column="professional_id" jdbcType="VARCHAR" property="professionalId" />
    <result column="professional_sn" jdbcType="VARCHAR" property="professionalSn" />
    <result column="channel_type" jdbcType="INTEGER" property="channelType" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="sign_img" jdbcType="VARCHAR" property="signImg" />
    <result column="protocol_img" jdbcType="VARCHAR" property="protocolImg" />
    <result column="encrypt_key_id" jdbcType="INTEGER" property="encryptKeyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, cer_code, cer_code_md5, mobile, account_no, account_no_md5, contract_img,
    enterprise_professional_facilitator_id, professional_id, professional_sn, channel_type,
    update_time, create_time, sign_img, protocol_img, encrypt_key_id
  </sql>
</mapper>
