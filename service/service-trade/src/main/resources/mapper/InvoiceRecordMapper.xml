<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.trade.entity.InvoiceRecord">
	<sql id="table"> tbl_invoice_record </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.InvoiceRecord">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="COMPLETE_TIME" property="completeTime" jdbcType="TIMESTAMP"/>
		<result column="EMPLOYER_MCH_NO" property="employerMchNo" jdbcType="VARCHAR"/>
		<result column="EMPLOYER_MCH_NAME" property="employerMchName" jdbcType="VARCHAR"/>
		<result column="MAINSTAY_MCH_NO" property="mainstayMchNo" jdbcType="VARCHAR"/>
		<result column="MAINSTAY_MCH_NAME" property="mainstayMchName" jdbcType="VARCHAR"/>
		<result column="TRX_NO" property="trxNo" jdbcType="VARCHAR"/>
		<result column="INVOICE_TYPE" property="invoiceType" jdbcType="SMALLINT"/>
		<result column="APPLY_TYPE" property="applyType" jdbcType="SMALLINT"/>
		<result column="AMOUNT_TYPE" property="amountType" jdbcType="SMALLINT"/>
		<result column="INVOICE_AMOUNT" property="invoiceAmount" jdbcType="DECIMAL"/>
		<result column="TRADE_COMPLETE_DAY_BEGIN" property="tradeCompleteDayBegin" jdbcType="VARCHAR"/>
		<result column="TRADE_COMPLETE_DAY_END" property="tradeCompleteDayEnd" jdbcType="VARCHAR"/>
		<result column="INVOICE_CATEGORY_CODE" property="invoiceCategoryCode" jdbcType="VARCHAR"/>
		<result column="INVOICE_CATEGORY_NAME" property="invoiceCategoryName" jdbcType="VARCHAR"/>
		<result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="SMALLINT"/>
		<result column="ACCOUNT_HANDLE_STATUS" property="accountHandleStatus" jdbcType="SMALLINT"/>
		<result column="EXPRESS_CONSIGNEE" property="expressConsignee" jdbcType="VARCHAR"/>
		<result column="EXPRESS_TELEPHONE" property="expressTelephone" jdbcType="VARCHAR"/>
		<result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
		<result column="CITY" property="city" jdbcType="VARCHAR"/>
		<result column="COUNTY" property="county" jdbcType="VARCHAR"/>
		<result column="ADDRESS" property="address" jdbcType="VARCHAR"/>
		<result column="EXPRESS_COMPANY" property="expressCompany" jdbcType="VARCHAR"/>
		<result column="EXPRESS_NO" property="expressNo" jdbcType="VARCHAR"/>
		<result column="TAX_PAYER_TYPE" property="taxPayerType" jdbcType="SMALLINT"/>
		<result column="TAX_NO" property="taxNo" jdbcType="VARCHAR"/>
		<result column="REGISTER_ADDR_INFO" property="registerAddrInfo" jdbcType="VARCHAR"/>
		<result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR"/>
		<result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
		<result column="INVOICE_FILE_URL" property="invoiceFileUrl" jdbcType="VARCHAR"/>
		<result column="REMARK" property="remark" jdbcType="VARCHAR"/>
		<result column="JSON_INFO" property="jsonInfo" jdbcType="OTHER"/>
		<result column="ERROR_DESC" property="errorDesc" jdbcType="VARCHAR"/>
		<result column="PRODUCT_NO" property="productNo" jdbcType="VARCHAR"/>
		<result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
		<result column="JOB_ID" property="jobId" jdbcType="VARCHAR"/>
		<result column="JOB_NAME" property="jobName" jdbcType="VARCHAR"/>
		<result column="SOURCE" property="source" jdbcType="INTEGER"/>
		<result column="WORKER_BILL_FILE_PATH" property="workerBillFilePath" jdbcType="VARCHAR"/>
		<result column="SERVICE_FEE_BILL_PATH" property="serviceFeeBillPath" jdbcType="VARCHAR"/>
		<result column="SERVICE_FEE_AMOUNT" property="serviceFeeAmount" jdbcType="VARCHAR"/>
		<result column="CATEGORY" property="category" jdbcType="INTEGER"/>
		<result column="WORK_CATEGORY_CODE" property="workCategoryCode" jdbcType="VARCHAR"/>
		<result column="WORK_CATEGORY_NAME" property="workCategoryName" jdbcType="VARCHAR"/>
		<result column="INVOICE_PRE_IDS" property="invoicePreIds" jdbcType="VARCHAR"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		VERSION,
		CREATE_TIME,
		UPDATE_TIME,
		COMPLETE_TIME,
		PRODUCT_NO,
		PRODUCT_NAME,
		EMPLOYER_MCH_NO,
		EMPLOYER_MCH_NAME,
		MAINSTAY_MCH_NO,
		MAINSTAY_MCH_NAME,
		TRX_NO,
		INVOICE_TYPE,
		APPLY_TYPE,
		AMOUNT_TYPE,
		INVOICE_AMOUNT,
		TRADE_COMPLETE_DAY_BEGIN,
		TRADE_COMPLETE_DAY_END,
		INVOICE_CATEGORY_CODE,
		INVOICE_CATEGORY_NAME,
		INVOICE_STATUS,
		ACCOUNT_HANDLE_STATUS,
		EXPRESS_CONSIGNEE,
		EXPRESS_TELEPHONE,
		PROVINCE,
		CITY,
		COUNTY,
		ADDRESS,
		EXPRESS_COMPANY,
		EXPRESS_NO,
		TAX_PAYER_TYPE,
		TAX_NO,
		REGISTER_ADDR_INFO,
		ACCOUNT_NO,
		BANK_NAME,
		INVOICE_FILE_URL,
		REMARK,
		JSON_INFO,
		ERROR_DESC,
		JOB_ID,
		JOB_NAME,
		SOURCE,
		WORKER_BILL_FILE_PATH,
		SERVICE_FEE_BILL_PATH,
		SERVICE_FEE_AMOUNT,
		CATEGORY,
		WORK_CATEGORY_CODE,
		WORK_CATEGORY_NAME,
		INVOICE_PRE_IDS
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.InvoiceRecord">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	COMPLETE_TIME ,
			PRODUCT_NO,
			PRODUCT_NAME,
        	EMPLOYER_MCH_NO ,
        	EMPLOYER_MCH_NAME ,
        	MAINSTAY_MCH_NO ,
        	MAINSTAY_MCH_NAME ,
        	TRX_NO ,
        	INVOICE_TYPE ,
        	APPLY_TYPE ,
		    AMOUNT_TYPE,
        	INVOICE_AMOUNT ,
        	TRADE_COMPLETE_DAY_BEGIN ,
        	TRADE_COMPLETE_DAY_END ,
        	INVOICE_CATEGORY_CODE ,
        	INVOICE_CATEGORY_NAME ,
        	INVOICE_STATUS ,
        	ACCOUNT_HANDLE_STATUS ,
        	EXPRESS_CONSIGNEE ,
        	EXPRESS_TELEPHONE ,
        	PROVINCE ,
        	CITY ,
        	COUNTY ,
        	ADDRESS ,
        	EXPRESS_COMPANY ,
        	EXPRESS_NO ,
        	TAX_PAYER_TYPE ,
        	TAX_NO ,
        	REGISTER_ADDR_INFO ,
        	ACCOUNT_NO ,
        	BANK_NAME ,
        	INVOICE_FILE_URL ,
        	REMARK ,
        	JSON_INFO ,
        	ERROR_DESC,
			JOB_ID,
			JOB_NAME,
			SOURCE,
			WORKER_BILL_FILE_PATH,
			SERVICE_FEE_BILL_PATH,
			SERVICE_FEE_AMOUNT,
			CATEGORY,
			WORK_CATEGORY_CODE,
			WORK_CATEGORY_NAME,
			INVOICE_PRE_IDS
        ) VALUES (
			0,
			#{createTime,jdbcType=TIMESTAMP},
			#{updateTime,jdbcType=TIMESTAMP},
			#{completeTime,jdbcType=TIMESTAMP},
			#{productNo,jdbcType=VARCHAR},
			#{productName,jdbcType=VARCHAR},
			#{employerMchNo,jdbcType=VARCHAR},
			#{employerMchName,jdbcType=VARCHAR},
			#{mainstayMchNo,jdbcType=VARCHAR},
			#{mainstayMchName,jdbcType=VARCHAR},
			#{trxNo,jdbcType=VARCHAR},
			#{invoiceType,jdbcType=SMALLINT},
			#{applyType,jdbcType=SMALLINT},
		    #{amountType,jdbcType=SMALLINT},
			#{invoiceAmount,jdbcType=DECIMAL},
			#{tradeCompleteDayBegin,jdbcType=VARCHAR},
			#{tradeCompleteDayEnd,jdbcType=VARCHAR},
			#{invoiceCategoryCode,jdbcType=VARCHAR},
			#{invoiceCategoryName,jdbcType=VARCHAR},
			#{invoiceStatus,jdbcType=SMALLINT},
			#{accountHandleStatus,jdbcType=SMALLINT},
			#{expressConsignee,jdbcType=VARCHAR},
			#{expressTelephone,jdbcType=VARCHAR},
			#{province,jdbcType=VARCHAR},
			#{city,jdbcType=VARCHAR},
			#{county,jdbcType=VARCHAR},
			#{address,jdbcType=VARCHAR},
			#{expressCompany,jdbcType=VARCHAR},
			#{expressNo,jdbcType=VARCHAR},
			#{taxPayerType,jdbcType=SMALLINT},
			#{taxNo,jdbcType=VARCHAR},
			#{registerAddrInfo,jdbcType=VARCHAR},
			#{accountNo,jdbcType=VARCHAR},
			#{bankName,jdbcType=VARCHAR},
			#{invoiceFileUrl,jdbcType=VARCHAR},
			#{remark,jdbcType=VARCHAR},
			#{jsonInfo,jdbcType=OTHER},
			#{errorDesc,jdbcType=VARCHAR},
			#{jobId,jdbcType=VARCHAR},
			#{jobName,jdbcType=VARCHAR},
			#{source,jdbcType=INTEGER},
			#{workerBillFilePath,jdbcType=VARCHAR},
			#{serviceFeeBillPath,jdbcType=VARCHAR},
			#{serviceFeeAmount,jdbcType=VARCHAR},
			#{category,jdbcType=INTEGER},
			#{workCategoryCode,jdbcType=VARCHAR},
			#{workCategoryName,jdbcType=VARCHAR},
			#{invoicePreIds,jdbcType=VARCHAR}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	CREATE_TIME ,
        	UPDATE_TIME ,
        	COMPLETE_TIME ,
			PRODUCT_NO,
			PRODUCT_NAME,
        	EMPLOYER_MCH_NO ,
        	EMPLOYER_MCH_NAME ,
        	MAINSTAY_MCH_NO ,
        	MAINSTAY_MCH_NAME ,
        	TRX_NO ,
        	INVOICE_TYPE ,
        	APPLY_TYPE ,
		    AMOUNT_TYPE,
        	INVOICE_AMOUNT ,
        	TRADE_COMPLETE_DAY_BEGIN ,
        	TRADE_COMPLETE_DAY_END ,
        	INVOICE_CATEGORY_CODE ,
        	INVOICE_CATEGORY_NAME ,
        	INVOICE_STATUS ,
        	ACCOUNT_HANDLE_STATUS ,
        	EXPRESS_CONSIGNEE ,
        	EXPRESS_TELEPHONE ,
        	PROVINCE ,
        	CITY ,
        	COUNTY ,
        	ADDRESS ,
        	EXPRESS_COMPANY ,
        	EXPRESS_NO ,
        	TAX_PAYER_TYPE ,
        	TAX_NO ,
        	REGISTER_ADDR_INFO ,
        	ACCOUNT_NO ,
        	BANK_NAME ,
        	INVOICE_FILE_URL ,
        	REMARK ,
        	JSON_INFO ,
        	ERROR_DESC,
			JOB_ID,
			JOB_NAME,
			SOURCE,
			WORKER_BILL_FILE_PATH,
			SERVICE_FEE_BILL_PATH,
			SERVICE_FEE_AMOUNT,
			CATEGORY,
			WORK_CATEGORY_CODE,
			WORK_CATEGORY_NAME,
			INVOICE_PRE_IDS
        ) VALUES
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.updateTime,jdbcType=TIMESTAMP},
			#{item.completeTime,jdbcType=TIMESTAMP},
			#{item.productNo,jdbcType=VARCHAR},
			#{item.productName,jdbcType=VARCHAR},
			#{item.employerMchNo,jdbcType=VARCHAR},
			#{item.employerMchName,jdbcType=VARCHAR},
			#{item.mainstayMchNo,jdbcType=VARCHAR},
			#{item.mainstayMchName,jdbcType=VARCHAR},
			#{item.trxNo,jdbcType=VARCHAR},
			#{item.invoiceType,jdbcType=SMALLINT},
			#{item.applyType,jdbcType=SMALLINT},
			#{item.amountType,jdbcType=SMALLINT},
			#{item.invoiceAmount,jdbcType=DECIMAL},
			#{item.tradeCompleteDayBegin,jdbcType=VARCHAR},
			#{item.tradeCompleteDayEnd,jdbcType=VARCHAR},
			#{item.invoiceCategoryCode,jdbcType=VARCHAR},
			#{item.invoiceCategoryName,jdbcType=VARCHAR},
			#{item.invoiceStatus,jdbcType=SMALLINT},
			#{item.accountHandleStatus,jdbcType=SMALLINT},
			#{item.expressConsignee,jdbcType=VARCHAR},
			#{item.expressTelephone,jdbcType=VARCHAR},
			#{item.province,jdbcType=VARCHAR},
			#{item.city,jdbcType=VARCHAR},
			#{item.county,jdbcType=VARCHAR},
			#{item.address,jdbcType=VARCHAR},
			#{item.expressCompany,jdbcType=VARCHAR},
			#{item.expressNo,jdbcType=VARCHAR},
			#{item.taxPayerType,jdbcType=SMALLINT},
			#{item.taxNo,jdbcType=VARCHAR},
			#{item.registerAddrInfo,jdbcType=VARCHAR},
			#{item.accountNo,jdbcType=VARCHAR},
			#{item.bankName,jdbcType=VARCHAR},
			#{item.invoiceFileUrl,jdbcType=VARCHAR},
			#{item.remark,jdbcType=VARCHAR},
			#{item.jsonInfo,jdbcType=OTHER},
			#{item.errorDesc,jdbcType=VARCHAR},
			#{item.jobId,jdbcType=VARCHAR},
			#{item.jobName,jdbcType=VARCHAR},
			#{item.source,jdbcType=INTEGER},
			#{item.workerBillFilePath,jdbcType=VARCHAR},
			#{item.serviceFeeBillPath,jdbcType=VARCHAR},
			#{item.serviceFeeAmount,jdbcType=VARCHAR},
			#{item.category,jdbcType=INTEGER},
			#{item.workCategoryCode,jdbcType=VARCHAR},
			#{item.workCategoryName,jdbcType=VARCHAR},
			#{item.invoicePreIds,jdbcType=VARCHAR}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.trade.entity.InvoiceRecord">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
			COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
			PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
			PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
			COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
			EMPLOYER_MCH_NO = #{employerMchNo,jdbcType=VARCHAR},
			EMPLOYER_MCH_NAME = #{employerMchName,jdbcType=VARCHAR},
			MAINSTAY_MCH_NO = #{mainstayMchNo,jdbcType=VARCHAR},
			MAINSTAY_MCH_NAME = #{mainstayMchName,jdbcType=VARCHAR},
			TRX_NO = #{trxNo,jdbcType=VARCHAR},
			INVOICE_TYPE = #{invoiceType,jdbcType=SMALLINT},
			APPLY_TYPE = #{applyType,jdbcType=SMALLINT},
            AMOUNT_TYPE = #{amountType,jdbcType=SMALLINT},
			INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL},
			TRADE_COMPLETE_DAY_BEGIN = #{tradeCompleteDayBegin,jdbcType=VARCHAR},
			TRADE_COMPLETE_DAY_END = #{tradeCompleteDayEnd,jdbcType=VARCHAR},
			INVOICE_CATEGORY_CODE = #{invoiceCategoryCode,jdbcType=VARCHAR},
			INVOICE_CATEGORY_NAME = #{invoiceCategoryName,jdbcType=VARCHAR},
			INVOICE_STATUS = #{invoiceStatus,jdbcType=SMALLINT},
			ACCOUNT_HANDLE_STATUS = #{accountHandleStatus,jdbcType=SMALLINT},
			EXPRESS_CONSIGNEE = #{expressConsignee,jdbcType=VARCHAR},
			EXPRESS_TELEPHONE = #{expressTelephone,jdbcType=VARCHAR},
			PROVINCE = #{province,jdbcType=VARCHAR},
			CITY = #{city,jdbcType=VARCHAR},
			COUNTY = #{county,jdbcType=VARCHAR},
			ADDRESS = #{address,jdbcType=VARCHAR},
			EXPRESS_COMPANY = #{expressCompany,jdbcType=VARCHAR},
			EXPRESS_NO = #{expressNo,jdbcType=VARCHAR},
			TAX_PAYER_TYPE = #{taxPayerType,jdbcType=SMALLINT},
			TAX_NO = #{taxNo,jdbcType=VARCHAR},
			REGISTER_ADDR_INFO = #{registerAddrInfo,jdbcType=VARCHAR},
			ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR},
			BANK_NAME = #{bankName,jdbcType=VARCHAR},
			INVOICE_FILE_URL = #{invoiceFileUrl,jdbcType=VARCHAR},
			REMARK = #{remark,jdbcType=VARCHAR},
			JSON_INFO = #{jsonInfo,jdbcType=OTHER},
			ERROR_DESC = #{errorDesc,jdbcType=VARCHAR},
			JOB_ID = #{jobId,jdbcType=VARCHAR},
			JOB_NAME = #{jobName,jdbcType=VARCHAR},
			SOURCE = #{source,jdbcType=INTEGER},
			WORKER_BILL_FILE_PATH = #{workerBillFilePath,jdbcType=VARCHAR},
			SERVICE_FEE_BILL_PATH = #{serviceFeeBillPath,jdbcType=VARCHAR},
			SERVICE_FEE_AMOUNT = #{serviceFeeAmount,jdbcType=VARCHAR},
			CATEGORY = #{category,jdbcType=INTEGER},
			WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR},
			WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR},
			INVOICE_PRE_IDS = #{invoicePreIds,jdbcType=VARCHAR}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.InvoiceRecord">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateTime != null">
				UPDATE_TIME =#{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="completeTime != null">
				COMPLETE_TIME =#{completeTime,jdbcType=TIMESTAMP},
			</if>
			<if test="productNo != null and productNo !='' ">
				PRODUCT_NO =#{productNo,jdbcType=VARCHAR},
			</if>
			<if test="productName != null and productName !='' ">
				PRODUCT_NAME =#{productName,jdbcType=VARCHAR},
			</if>
			<if test="employerMchNo != null">
				EMPLOYER_MCH_NO =#{employerMchNo,jdbcType=VARCHAR},
			</if>
			<if test="employerMchName != null">
				EMPLOYER_MCH_NAME =#{employerMchName,jdbcType=VARCHAR},
			</if>
			<if test="mainstayMchNo != null">
				MAINSTAY_MCH_NO =#{mainstayMchNo,jdbcType=VARCHAR},
			</if>
			<if test="mainstayMchName != null">
				MAINSTAY_MCH_NAME =#{mainstayMchName,jdbcType=VARCHAR},
			</if>
			<if test="trxNo != null">
				TRX_NO =#{trxNo,jdbcType=VARCHAR},
			</if>
			<if test="invoiceType != null">
				INVOICE_TYPE =#{invoiceType,jdbcType=SMALLINT},
			</if>
			<if test="applyType != null">
				APPLY_TYPE =#{applyType,jdbcType=SMALLINT},
			</if>
			<if test="amountType != null">
				AMOUNT_TYPE = #{amountType,jdbcType=SMALLINT},
			</if>
			<if test="invoiceAmount != null">
				INVOICE_AMOUNT =#{invoiceAmount,jdbcType=DECIMAL},
			</if>
			<if test="tradeCompleteDayBegin != null">
				TRADE_COMPLETE_DAY_BEGIN =#{tradeCompleteDayBegin,jdbcType=VARCHAR},
			</if>
			<if test="tradeCompleteDayEnd != null">
				TRADE_COMPLETE_DAY_END =#{tradeCompleteDayEnd,jdbcType=VARCHAR},
			</if>
			<if test="invoiceCategoryCode != null">
				INVOICE_CATEGORY_CODE =#{invoiceCategoryCode,jdbcType=VARCHAR},
			</if>
			<if test="invoiceCategoryName != null">
				INVOICE_CATEGORY_NAME =#{invoiceCategoryName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceStatus != null">
				INVOICE_STATUS =#{invoiceStatus,jdbcType=SMALLINT},
			</if>
			<if test="accountHandleStatus != null">
				ACCOUNT_HANDLE_STATUS =#{accountHandleStatus,jdbcType=SMALLINT},
			</if>
			<if test="expressConsignee != null">
				EXPRESS_CONSIGNEE =#{expressConsignee,jdbcType=VARCHAR},
			</if>
			<if test="expressTelephone != null">
				EXPRESS_TELEPHONE =#{expressTelephone,jdbcType=VARCHAR},
			</if>
			<if test="province != null">
				PROVINCE =#{province,jdbcType=VARCHAR},
			</if>
			<if test="city != null">
				CITY =#{city,jdbcType=VARCHAR},
			</if>
			<if test="county != null">
				COUNTY =#{county,jdbcType=VARCHAR},
			</if>
			<if test="address != null">
				ADDRESS =#{address,jdbcType=VARCHAR},
			</if>
			<if test="expressCompany != null">
				EXPRESS_COMPANY =#{expressCompany,jdbcType=VARCHAR},
			</if>
			<if test="expressNo != null">
				EXPRESS_NO =#{expressNo,jdbcType=VARCHAR},
			</if>
			<if test="taxPayerType != null">
				TAX_PAYER_TYPE =#{taxPayerType,jdbcType=SMALLINT},
			</if>
			<if test="taxNo != null">
				TAX_NO =#{taxNo,jdbcType=VARCHAR},
			</if>
			<if test="registerAddrInfo != null">
				REGISTER_ADDR_INFO =#{registerAddrInfo,jdbcType=VARCHAR},
			</if>
			<if test="accountNo != null">
				ACCOUNT_NO =#{accountNo,jdbcType=VARCHAR},
			</if>
			<if test="bankName != null">
				BANK_NAME =#{bankName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceFileUrl != null">
				INVOICE_FILE_URL =#{invoiceFileUrl,jdbcType=VARCHAR},
			</if>
			<if test="remark != null">
				REMARK =#{remark,jdbcType=VARCHAR},
			</if>
			<if test="jsonInfo != null">
				JSON_INFO =#{jsonInfo,jdbcType=OTHER},
			</if>
			<if test="errorDesc != null">
				ERROR_DESC =#{errorDesc,jdbcType=VARCHAR},
			</if>
			<if test="jobId != null and job_id != '' ">
				JOB_ID =#{jobId,jdbcType=VARCHAR},
			</if>
			<if test="jobName != null and jobName!= ''">
				JOB_NAME =#{jobName,jdbcType=VARCHAR},
			</if>
			<if test="source != null">
				SOURCE = #{source,jdbcType=INTEGER},
			</if>
			<if test="workerBillFilePath != null and workerBillFilePath!= ''">
				WORKER_BILL_FILE_PATH =#{workerBillFilePath,jdbcType=VARCHAR},
			</if>
			<if test="serviceFeeBillPath != null and serviceFeeBillPath!= ''">
				SERVICE_FEE_BILL_PATH =#{serviceFeeBillPath,jdbcType=VARCHAR},
			</if>
			<if test="serviceFeeAmount != null and serviceFeeAmount != ''">
				SERVICE_FEE_AMOUNT =#{serviceFeeAmount,jdbcType=VARCHAR},
			</if>
			<if test="category != null">
				CATEGORY =#{category,jdbcType=INTEGER},
			</if>
			<if test="workCategoryCode != null and workCategoryCode != ''">
				WORK_CATEGORY_CODE =#{workCategoryCode,jdbcType=VARCHAR},
			</if>
			<if test="workCategoryName != null and workCategoryName != ''">
				WORK_CATEGORY_NAME =#{workCategoryName,jdbcType=VARCHAR},
			</if>
			<if test="invoicePreIds != null">
				INVOICE_PRE_IDS =#{invoicePreIds,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
		<choose>
			<when test="sortColumns != null and sortColumns !='' ">
				<![CDATA[ ORDER BY ${sortColumns} ]]>
			</when>
			<otherwise>
				<![CDATA[ ORDER BY ID DESC ]]>
			</otherwise>
		</choose>
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE ID = #{id,jdbcType=BIGINT}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="maxId != null">
			and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
        <if test="createTimeBegin != null and createTimeEnd != null" >
            and CREATE_TIME between #{createTimeBegin,jdbcType=TIMESTAMP} and #{createTimeEnd,jdbcType=TIMESTAMP}
        </if>
		<if test="updateTime != null">
			and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
		</if>
		<if test="completeTimeBegin != null and completeTimeEnd != null" >
			and COMPLETE_TIME between #{completeTimeBegin,jdbcType=TIMESTAMP} and #{completeTimeEnd,jdbcType=TIMESTAMP}
		</if>
		<if test="productNo != null and productNo !=''">
			and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
		</if>
		<if test="productName != null and productName !=''">
			and PRODUCT_NAME = #{productName,jdbcType=VARCHAR}
		</if>
		<if test="employerMchNo != null and employerMchNo !=''">
			and EMPLOYER_MCH_NO = #{employerMchNo,jdbcType=VARCHAR}
		</if>
		<if test="employerMchName != null and employerMchName !=''">
			and EMPLOYER_MCH_NAME = #{employerMchName,jdbcType=VARCHAR}
		</if>
		<if test="employerMchNameLike != null and employerMchNameLike !=''">
			and EMPLOYER_MCH_NAME like concat('%',#{employerMchNameLike,jdbcType=VARCHAR},'%')
		</if>
		<if test="mainstayMchNo != null and mainstayMchNo !=''">
			and MAINSTAY_MCH_NO = #{mainstayMchNo,jdbcType=VARCHAR}
		</if>
		<if test="mainstayMchName != null and mainstayMchName !=''">
			and MAINSTAY_MCH_NAME = #{mainstayMchName,jdbcType=VARCHAR}
		</if>
		<if test="trxNo != null and trxNo !=''">
			and TRX_NO = #{trxNo,jdbcType=VARCHAR}
		</if>
		<if test="invoiceType != null">
			and INVOICE_TYPE = #{invoiceType,jdbcType=SMALLINT}
		</if>
		<if test="applyType != null">
			and APPLY_TYPE = #{applyType,jdbcType=SMALLINT}
		</if>
		<if test="amountType != null">
			and AMOUNT_TYPE = #{amountType,jdbcType=SMALLINT}
		</if>
		<if test="invoiceAmount != null">
			and INVOICE_AMOUNT = #{invoiceAmount,jdbcType=DECIMAL}
		</if>
		<if test="tradeCompleteDayBegin != null and tradeCompleteDayBegin !=''">
			and TRADE_COMPLETE_DAY_BEGIN = #{tradeCompleteDayBegin,jdbcType=VARCHAR}
		</if>
		<if test="tradeCompleteDayEnd != null and tradeCompleteDayEnd !=''">
			and TRADE_COMPLETE_DAY_END = #{tradeCompleteDayEnd,jdbcType=VARCHAR}
		</if>
		<if test="invoiceCategoryCode != null and invoiceCategoryCode !=''">
			and INVOICE_CATEGORY_CODE = #{invoiceCategoryCode,jdbcType=VARCHAR}
		</if>
		<if test="invoiceCategoryName != null and invoiceCategoryName !=''">
			and INVOICE_CATEGORY_NAME = #{invoiceCategoryName,jdbcType=VARCHAR}
		</if>
		<if test="invoiceStatus != null">
			and INVOICE_STATUS = #{invoiceStatus,jdbcType=SMALLINT}
		</if>
		<if test="ignoreInvoiceStatus != null">
			and INVOICE_STATUS != #{ignoreInvoiceStatus,jdbcType=SMALLINT}
		</if>
		<if test="accountHandleStatus != null">
			and ACCOUNT_HANDLE_STATUS = #{accountHandleStatus,jdbcType=SMALLINT}
		</if>
		<if test="expressConsignee != null and expressConsignee !=''">
			and EXPRESS_CONSIGNEE = #{expressConsignee,jdbcType=VARCHAR}
		</if>
		<if test="expressTelephone != null and expressTelephone !=''">
			and EXPRESS_TELEPHONE = #{expressTelephone,jdbcType=VARCHAR}
		</if>
		<if test="province != null and province !=''">
			and PROVINCE = #{province,jdbcType=VARCHAR}
		</if>
		<if test="city != null and city !=''">
			and CITY = #{city,jdbcType=VARCHAR}
		</if>
		<if test="county != null and county !=''">
			and COUNTY = #{county,jdbcType=VARCHAR}
		</if>
		<if test="address != null and address !=''">
			and ADDRESS = #{address,jdbcType=VARCHAR}
		</if>
		<if test="expressCompany != null and expressCompany !=''">
			and EXPRESS_COMPANY = #{expressCompany,jdbcType=VARCHAR}
		</if>
		<if test="expressNo != null and expressNo !=''">
			and EXPRESS_NO = #{expressNo,jdbcType=VARCHAR}
		</if>
		<if test="taxPayerType != null">
			and TAX_PAYER_TYPE = #{taxPayerType,jdbcType=SMALLINT}
		</if>
		<if test="taxNo != null and taxNo !=''">
			and TAX_NO = #{taxNo,jdbcType=VARCHAR}
		</if>
		<if test="registerAddrInfo != null and registerAddrInfo !=''">
			and REGISTER_ADDR_INFO = #{registerAddrInfo,jdbcType=VARCHAR}
		</if>
		<if test="accountNo != null and accountNo !=''">
			and ACCOUNT_NO = #{accountNo,jdbcType=VARCHAR}
		</if>
		<if test="bankName != null and bankName !=''">
			and BANK_NAME = #{bankName,jdbcType=VARCHAR}
		</if>
		<if test="invoiceFileUrl != null and invoiceFileUrl !=''">
			and INVOICE_FILE_URL = #{invoiceFileUrl,jdbcType=VARCHAR}
		</if>
		<if test="remark != null and remark !=''">
			and REMARK = #{remark,jdbcType=VARCHAR}
		</if>
		<if test="jsonInfo != null">
			and JSON_INFO = #{jsonInfo,jdbcType=OTHER}
		</if>
		<if test="errorDesc != null and errorDesc !=''">
			and ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
		</if>
		<if test="jobId != null and jobId !=''">
			and JOB_ID = #{jobId,jdbcType=VARCHAR}
		</if>
		<if test="jobName != null and jobName !=''">
			and JOB_NAME = #{jobName,jdbcType=VARCHAR}
		</if>
		<if test="source != null">
			and SOURCE = #{source,jdbcType=INTEGER}
		</if>
		<if test="workerBillFilePath != null and workerBillFilePath !=''">
			and WORKER_BILL_FILE_PATH = #{workerBillFilePath,jdbcType=VARCHAR}
		</if>
		<if test="serviceFeeBillPath != null and serviceFeeBillPath !=''">
			and SERVICE_FEE_BILL_PATH = #{serviceFeeBillPath,jdbcType=VARCHAR}
		</if>
		<if test="serviceFeeAmount != null and serviceFeeAmount !=''">
			and SERVICE_FEE_AMOUNT = #{serviceFeeAmount,jdbcType=VARCHAR}
		</if>
		<if test="category != null">
			and CATEGORY = #{category,jdbcType=INTEGER}
		</if>
		<if test="workCategoryCode != null and workCategoryCode !=''">
			and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
		</if>
		<if test="workCategoryName != null and workCategoryName !=''">
			and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
		</if>
		<if test="workCategoryCodeIsNull !=null  and workCategoryCodeIsNull != ''">
			and WORK_CATEGORY_CODE is NULL
		</if>
		<if test="trxNos != null and trxNos.size() > 0">
			and TRX_NO in
			<foreach collection="trxNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
		</if>
	</sql>

	<select id="countInvoiceAmount" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT IFNULL(SUM(INVOICE_AMOUNT),0) as invoiceAmount,count(1) as invoiceCount  FROM `tbl_invoice_record`
		<where>
			<include refid="condition_sql"/>
		</where>
	</select>

	<select id="listByTimeRange" parameterType="java.util.Map" resultMap="BaseResultMap">
		select * from tbl_invoice_record
		<where>
			INVOICE_STATUS != 4
			<if test="tradeCompleteDayBegin != null and tradeCompleteDayBegin !=''">
				and TRADE_COMPLETE_DAY_BEGIN <![CDATA[ >= ]]> #{tradeCompleteDayBegin,jdbcType=VARCHAR}
			</if>
			<if test="tradeCompleteDayEnd != null and tradeCompleteDayEnd !=''">
				and TRADE_COMPLETE_DAY_END <![CDATA[ <= ]]> #{tradeCompleteDayEnd,jdbcType=VARCHAR}
			</if>
			<if test="employerMchNo != null and employerMchNo !=''">
				and EMPLOYER_MCH_NO = #{employerMchNo,jdbcType=VARCHAR}
			</if>
			<if test="mainstayMchNo != null and mainstayMchNo !=''">
				and MAINSTAY_MCH_NO = #{mainstayMchNo,jdbcType=VARCHAR}
			</if>
			<if test="invoiceType != null">
				and INVOICE_TYPE = #{invoiceType,jdbcType=SMALLINT}
			</if>
			<if test="amountType != null">
				and AMOUNT_TYPE = #{amountType,jdbcType=SMALLINT}
			</if>
		</where>
		<![CDATA[ ORDER BY ID DESC LIMIT 1]]>
	</select>
</mapper>

