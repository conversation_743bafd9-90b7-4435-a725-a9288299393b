<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.TemplateComponent">
    <sql id="table">tbl_template_component</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.TemplateComponent">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="LABEL" property="label" jdbcType="VARCHAR"/>
        <result column="REQUIRED" property="required" jdbcType="SMALLINT"/>
        <result column="STYLE" property="style" jdbcType="OTHER"/>
        <result column="PRESET" property="preset" jdbcType="SMALLINT"/>
        <result column="TYPE" property="type" jdbcType="INTEGER"/>
        <result column="HANDLES" property="handles" jdbcType="OTHER"/>
        <result column="RESIZABLE" property="resizable" jdbcType="TINYINT"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_TIME, UPDATE_TIME, LABEL, REQUIRED, STYLE, PRESET, TYPE, HANDLES, RESIZABLE
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.TemplateComponent">
        INSERT INTO <include refid="table" /> (
        VERSION,
        CREATE_TIME,
        UPDATE_TIME,
        LABEL,
        REQUIRED,
        STYLE,
        PRESET,
        TYPE,
        HANDLES,
        RESIZABLE
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{label,jdbcType=VARCHAR},
        #{required,jdbcType=SMALLINT},
        #{style,jdbcType=OTHER},
        #{preset,jdbcType=SMALLINT},
        #{type,jdbcType=INTEGER},
        #{handles,jdbcType=OTHER},
        #{resizable,jdbcType=TINYINT}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        CREATE_TIME,
        UPDATE_TIME,
        LABEL,
        REQUIRED,
        STYLE,
        PRESET,
        TYPE,
        HANDLES,
        RESIZABLE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.version,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.label,jdbcType=VARCHAR},
            #{item.required,jdbcType=SMALLINT},
            #{item.style,jdbcType=OTHER},
            #{item.preset,jdbcType=SMALLINT},
            #{item.type,jdbcType=INTEGER},
            #{item.handles,jdbcType=OTHER},
            #{item.resizable,jdbcType=TINYINT}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.TemplateComponent">
        UPDATE <include refid="table" /> SET
        VERSION = #{version,jdbcType=SMALLINT} +1,
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
        LABEL = #{label,jdbcType=VARCHAR},
        REQUIRED = #{required,jdbcType=SMALLINT},
        STYLE = #{style,jdbcType=OTHER},
        PRESET = #{preset,jdbcType=SMALLINT},
        TYPE = #{type,jdbcType=INTEGER},
        HANDLES = #{handles,jdbcType=OTHER},
        RESIZABLE = #{resizable,jdbcType=TINYINT}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.TemplateComponent">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="label != null">
                LABEL = #{label,jdbcType=VARCHAR},
            </if>
            <if test="required != null">
                REQUIRED = #{required,jdbcType=SMALLINT},
            </if>
            <if test="style != null">
                STYLE = #{style,jdbcType=OTHER},
            </if>
            <if test="preset != null">
                PRESET = #{preset,jdbcType=SMALLINT},
            </if>
            <if test="type != null">
                TYPE = #{type,jdbcType=INTEGER},
            </if>
            <if test="handles != null">
                HANDLES = #{handles,jdbcType=OTHER},
            </if>
            <if test="resizable != null">
                RESIZABLE = #{resizable,jdbcType=TINYINT}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="label != null and label !=''">
            and LABEL = #{label,jdbcType=VARCHAR}
        </if>
        <if test="required != null">
            and REQUIRED = #{required,jdbcType=SMALLINT}
        </if>
        <if test="style != null and style !=''">
            and STYLE = #{style,jdbcType=OTHER}
        </if>
        <if test="preset != null">
            and PRESET = #{preset,jdbcType=SMALLINT}
        </if>
        <if test="type != null">
            and TYPE = #{type,jdbcType=INTEGER}
        </if>
        <if test="handles != null and handles !=''">
            and HANDLES = #{handles,jdbcType=OTHER}
        </if>
        <if test="resizable != null">
            and RESIZABLE = #{resizable,jdbcType=TINYINT}
        </if>
    </sql>

</mapper>
