<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.AcIncomeRecord">

    <sql id="table"> tbl_ac_income_record </sql>

    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.AcIncomeRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="mch_no" jdbcType="VARCHAR" property="mchNo"/>
        <result column="mch_name" jdbcType="VARCHAR" property="mchName"/>
        <result column="pay_channel_no" jdbcType="VARCHAR" property="payChannelNo"/>
        <result column="pay_channel_no" jdbcType="VARCHAR" property="payChannelNo"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="trx_no" jdbcType="VARCHAR" property="trxNo"/>
        <result column="pay_no" jdbcType="VARCHAR" property="payNo"/>
        <result column="channel_state" jdbcType="VARCHAR" property="channelState"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="receive_amount" jdbcType="DECIMAL" property="receiveAmount"/>
        <result column="income_fee" jdbcType="DECIMAL" property="incomeFee"/>
        <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="income_trx_no" jdbcType="VARCHAR" property="incomeTrxNo"/>
        <result column="payer_account_no" jdbcType="VARCHAR" property="payerAccountNo"/>
        <result column="payer_account_name" jdbcType="VARCHAR" property="payerAccountName"/>
        <result column="payer_account_bank" jdbcType="VARCHAR" property="payerAccountBank"/>
        <result column="payee_account_no" jdbcType="VARCHAR" property="payeeAccountNo"/>
        <result column="payee_account_name" jdbcType="VARCHAR" property="payeeAccountName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, create_time, mch_no, mch_name, pay_channel_no, pay_channel_name, trx_no, pay_no, channel_state,
        state, receive_amount, income_fee, income_amount, pay_time, income_trx_no, payer_account_no,
        payer_account_name, payer_account_bank, payee_account_no, payee_account_name, remark
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from <include refid="table"/>
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from <include refid="table"/>
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.zhixianghui.facade.trade.entity.AcIncomeRecord">
        insert into <include refid="table"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="mchNo != null">
                mch_no,
            </if>
            <if test="mchName != null">
                mch_name,
            </if>
            <if test="payChannelNo != null">
                pay_channel_no,
            </if>
            <if test="payChannelName != null">
                pay_channel_name,
            </if>
            <if test="trxNo != null">
                trx_no,
            </if>
            <if test="payNo != null">
                pay_no,
            </if>
            <if test="channelState != null">
                channel_state,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="receiveAmount != null">
                receive_amount,
            </if>
            <if test="incomeFee != null">
                income_fee,
            </if>
            <if test="incomeAmount != null">
                income_amount,
            </if>
            <if test="payTime != null">
                pay_time,
            </if>
            <if test="incomeTrxNo != null">
                income_trx_no,
            </if>
            <if test="payerAccountNo != null">
                payer_account_no,
            </if>
            <if test="payerAccountName != null">
                payer_account_name,
            </if>
            <if test="payerAccountBank != null">
                payer_account_bank,
            </if>
            <if test="payeeAccountNo != null">
                payee_account_no,
            </if>
            <if test="payeeAccountName != null">
                payee_account_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mchNo != null">
                #{mchNo},
            </if>
            <if test="mchName != null">
                #{mchName},
            </if>
            <if test="payChannelNo != null">
                #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="trxNo != null">
                #{trxNo,jdbcType=VARCHAR},
            </if>
            <if test="payNo != null">
                #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="channelState != null">
                #{channelState,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="receiveAmount != null">
                #{receiveAmount,jdbcType=DECIMAL},
            </if>
            <if test="incomeFee != null">
                #{incomeFee,jdbcType=DECIMAL},
            </if>
            <if test="incomeAmount != null">
                #{incomeAmount,jdbcType=DECIMAL},
            </if>
            <if test="payTime != null">
                #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="incomeTrxNo != null">
                #{incomeTrxNo,jdbcType=VARCHAR},
            </if>
            <if test="payerAccountNo != null">
                #{payerAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="payerAccountName != null">
                #{payerAccountName,jdbcType=VARCHAR},
            </if>
            <if test="payerAccountBank != null">
                #{payerAccountBank,jdbcType=VARCHAR},
            </if>
            <if test="payeeAccountNo != null">
                #{payeeAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="payeeAccountName != null">
                #{payeeAccountName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.AcIncomeRecord">
        update <include refid="table"/>
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mchNo != null">
                mch_no = #{mchNo},
            </if>
            <if test="mchName != null">
                mch_name = #{mchName},
            </if>
            <if test="payChannelNo != null">
                pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},
            </if>
            <if test="payChannelName != null">
                pay_channel_name = #{payChannelName,jdbcType=VARCHAR},
            </if>
            <if test="trxNo != null">
                trx_no = #{trxNo,jdbcType=VARCHAR},
            </if>
            <if test="payNo != null">
                pay_no = #{payNo,jdbcType=VARCHAR},
            </if>
            <if test="channelState != null">
                channel_state = #{channelState,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=INTEGER},
            </if>
            <if test="receiveAmount != null">
                receive_amount = #{receiveAmount,jdbcType=DECIMAL},
            </if>
            <if test="incomeFee != null">
                income_fee = #{incomeFee,jdbcType=DECIMAL},
            </if>
            <if test="incomeAmount != null">
                income_amount = #{incomeAmount,jdbcType=DECIMAL},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="incomeTrxNo != null">
                income_trx_no = #{incomeTrxNo,jdbcType=VARCHAR},
            </if>
            <if test="payerAccountNo != null">
                payer_account_no = #{payerAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="payerAccountName != null">
                payer_account_name = #{payerAccountName,jdbcType=VARCHAR},
            </if>
            <if test="payerAccountBank != null">
                payer_account_bank = #{payerAccountBank,jdbcType=VARCHAR},
            </if>
            <if test="payeeAccountNo != null">
                payee_account_no = #{payeeAccountNo,jdbcType=VARCHAR},
            </if>
            <if test="payeeAccountName != null">
                payee_account_name = #{payeeAccountName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.AcIncomeRecord">
        update <include refid="table"/>
        set create_time        = #{createTime,jdbcType=TIMESTAMP},
            mch_no             = #{mchNo,jdbcType=VARCHAR},
            mch_name           = #{mchName,jdbcType=VARCHAR},
            pay_channel_no     = #{payChannelNo,jdbcType=VARCHAR},
            pay_channel_name   = #{payChannelName,jdbcType=VARCHAR},
            trx_no             = #{trxNo,jdbcType=VARCHAR},
            pay_no             = #{payNo,jdbcType=VARCHAR},
            channel_state      = #{channelState,jdbcType=VARCHAR},
            state              = #{state,jdbcType=INTEGER},
            receive_amount     = #{receiveAmount,jdbcType=DECIMAL},
            income_fee         = #{incomeFee,jdbcType=DECIMAL},
            income_amount      = #{incomeAmount,jdbcType=DECIMAL},
            pay_time           = #{payTime,jdbcType=TIMESTAMP},
            income_trx_no      = #{incomeTrxNo,jdbcType=VARCHAR},
            payer_account_no   = #{payerAccountNo,jdbcType=VARCHAR},
            payer_account_name = #{payerAccountName,jdbcType=VARCHAR},
            payer_account_bank = #{payerAccountBank,jdbcType=VARCHAR},
            payee_account_no   = #{payeeAccountNo,jdbcType=VARCHAR},
            payee_account_name = #{payeeAccountName,jdbcType=VARCHAR},
            remark             = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM <include refid="table"/>
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <sql id="condition_sql">
        <if test="beginDate != null and endDate != null">
            <![CDATA[ and CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and CREATE_DATE <= #{endDate,jdbcType=VARCHAR} ]]>
        </if>
        <if test="payChannelNo != null">
            and pay_channel_no = #{payChannelNo}
        </if>
        <if test="payerAccountNo != null">
            and payer_account_no = #{payerAccountNo}
        </if>
        <if test="payerAccountName != null">
            and payer_account_name = #{payerAccountName}
        </if>
        <if test="payeeAccountNo != null">
            and payee_account_no = #{payeeAccountNo}
        </if>
        <if test="payeeAccountName != null">
            and payee_account_name = #{payeeAccountName}
        </if>
        <if test="state != null">
            and state = #{state}
        </if>
        <if test="mchNo != null">
            mch_no = #{mchNo}
        </if>
        <if test="mchName != null">
            mch_name = #{mchName}
        </if>
    </sql>

</mapper>