<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.WechatInfoMapper">
    <sql id="table">tbl_wechat_info</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.WechatInfo">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="NICKNAME" property="nickname" jdbcType="VARCHAR"/>
        <result column="WX_OPEN_ID" property="wxOpenId" jdbcType="VARCHAR"/>
        <result column="MINI_OPEN_ID" property="miniOpenId" jdbcType="VARCHAR"/>
        <result column="WX_UNIONID" property="wxUnionid" jdbcType="VARCHAR"/>
        <result column="AVATAR" property="avatar" jdbcType="VARCHAR"/>
        <result column="SEX" property="sex" jdbcType="INTEGER"/>
        <result column="UPDATE_AT" property="updateAt" jdbcType="TIMESTAMP"/>
        <result column="LAST_LOGIN_AT" property="lastLoginAt" jdbcType="TIMESTAMP"/>
        <result column="CREATE_AT" property="createAt" jdbcType="TIMESTAMP"/>
        <result column="WX_SUBSCRIBE_AT" property="wxSubscribeAt" jdbcType="TIMESTAMP"/>
        <result column="SOURCE" property="source" jdbcType="INTEGER"/>
        <result column="USER_NO" property="userNo" jdbcType="VARCHAR"/>
        <result column="APP_ID" property="appId" jdbcType="VARCHAR"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NICKNAME, WX_OPEN_ID, MINI_OPEN_ID, WX_UNIONID, AVATAR, SEX, UPDATE_AT, LAST_LOGIN_AT, CREATE_AT, WX_SUBSCRIBE_AT, SOURCE, USER_NO,APP_ID
    </sql>


</mapper>
