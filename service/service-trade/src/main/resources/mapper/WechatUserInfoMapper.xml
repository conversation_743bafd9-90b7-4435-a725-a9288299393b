<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.WechatUserInfoMapper">
    <sql id="table">tbl_wechat_user_info</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.WechatUserInfo">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="AUTH_STATUS" property="authStatus" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <result column="UPDATE_AT" property="updateAt" jdbcType="TIMESTAMP"/>
        <result column="LAST_LOGIN_AT" property="lastLoginAt" jdbcType="TIMESTAMP"/>
        <result column="CREATE_AT" property="createAt" jdbcType="TIMESTAMP"/>
        <result column="USER_NO" property="userNo" jdbcType="VARCHAR"/>
        <result column="WX_USER_NO" property="wxUserNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO" property="receiveIdCardNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO_MD5" property="receiveIdCardNoMd5" jdbcType="VARCHAR"/>
        <result column="ENCRYPT_KEY_ID" property="encryptKeyId" jdbcType="BIGINT"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, MOBILE, AUTH_STATUS, STATUS, UPDATE_AT, LAST_LOGIN_AT, CREATE_AT, USER_NO, RECEIVE_ID_CARD_NO, RECEIVE_ID_CARD_NO_MD5, ENCRYPT_KEY_ID
    </sql>




</mapper>
