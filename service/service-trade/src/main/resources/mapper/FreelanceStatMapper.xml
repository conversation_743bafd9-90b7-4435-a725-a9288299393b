<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.FreelanceStat">
    <sql id="table">tbl_freelance_stat</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.FreelanceStat">
        <id column="ID" property="id" jdbcType="BIGINT"/>

        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="RECEIVE_NAME" property="receiveName" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO" property="receiveIdCardNo" jdbcType="VARCHAR"/>
        <result column="RECEIVE_ID_CARD_NO_MD5" property="receiveIdCardNoMd5" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="ORDER_ITEM_NET_AMOUNT" property="orderItemNetAmount" jdbcType="DECIMAL"/>
        <result column="RECEIVER_ORDER" property="receiverOrder" jdbcType="BIGINT"/>
        <result column="CONDITION_ORDER" property="conditionOrder" jdbcType="BIGINT"/>
        <result column="SIGN_RECORD" property="signRecord" jdbcType="SMALLINT"/>
        <result column="ID_CARD" property="idCard" jdbcType="SMALLINT"/>
        <result column="ENCRYPT_KEY_ID" property="encryptKeyId" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR"/>
        <result column="RECEIVE_PHONE_NO" property="receivePhoneNo" jdbcType="VARCHAR"/>
        <result column="SIGN_ID" property="signId" jdbcType="BIGINT"/>
        <result column="RECEIVE_NAME_MD5" property="receiveNameMd5" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, RECEIVE_NAME, RECEIVE_ID_CARD_NO, RECEIVE_ID_CARD_NO_MD5, EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NAME, MAINSTAY_NO, ORDER_ITEM_NET_AMOUNT, RECEIVER_ORDER, CONDITION_ORDER, SIGN_RECORD, ID_CARD, ENCRYPT_KEY_ID, CREATE_TIME, CREATE_DATE, RECEIVE_PHONE_NO, SIGN_ID
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.zhixianghui.facade.trade.entity.FreelanceStat">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        RECEIVE_NAME,
        RECEIVE_ID_CARD_NO,
        RECEIVE_ID_CARD_NO_MD5,
        EMPLOYER_NO,
        EMPLOYER_NAME,
        MAINSTAY_NAME,
        MAINSTAY_NO,
        ORDER_ITEM_NET_AMOUNT,
        RECEIVER_ORDER,
        CONDITION_ORDER,
        SIGN_RECORD,
        ID_CARD,
        ENCRYPT_KEY_ID,
        CREATE_TIME,
        CREATE_DATE,
        RECEIVE_PHONE_NO,
        SIGN_ID,
        RECEIVE_NAME_MD5
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{receiveName,jdbcType=VARCHAR},
        #{receiveIdCardNo,jdbcType=VARCHAR},
        #{receiveIdCardNoMd5,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{orderItemNetAmount,jdbcType=DECIMAL},
        #{receiverOrder,jdbcType=BIGINT},
        #{conditionOrder,jdbcType=BIGINT},
        #{signRecord,jdbcType=SMALLINT},
        #{idCard,jdbcType=SMALLINT},
        #{encryptKeyId,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{createDate,jdbcType=VARCHAR},
        #{receivePhoneNo,jdbcType=VARCHAR},
        #{signId,jdbcType=BIGINT},
        #{receiveNameMd5,jdbcType=VARCHAR}
        )
        ON DUPLICATE KEY UPDATE
        UPDATE_TIME = values(UPDATE_TIME),
        ORDER_ITEM_NET_AMOUNT = values(ORDER_ITEM_NET_AMOUNT),
        RECEIVER_ORDER = values(RECEIVER_ORDER),
        CONDITION_ORDER = values(CONDITION_ORDER),
        SIGN_RECORD = values (SIGN_RECORD),
        ID_CARD = values (ID_CARD)
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        VERSION,
        RECEIVE_NAME,
        RECEIVE_ID_CARD_NO,
        RECEIVE_ID_CARD_NO_MD5,
        EMPLOYER_NO,
        EMPLOYER_NAME,
        MAINSTAY_NAME,
        MAINSTAY_NO,
        ORDER_ITEM_NET_AMOUNT,
        RECEIVER_ORDER,
        CONDITION_ORDER,
        SIGN_RECORD,
        ID_CARD,
        ENCRYPT_KEY_ID,
        CREATE_TIME,
        CREATE_DATE
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.version,jdbcType=SMALLINT},
            #{item.receiveName,jdbcType=VARCHAR},
            #{item.receiveIdCardNo,jdbcType=VARCHAR},
            #{item.receiveIdCardNoMd5,jdbcType=VARCHAR},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.orderItemNetAmount,jdbcType=DECIMAL},
            #{item.receiverOrder,jdbcType=BIGINT},
            #{item.conditionOrder,jdbcType=BIGINT},
            #{item.signRecord,jdbcType=SMALLINT},
            #{item.idCard,jdbcType=SMALLINT},
            #{item.encryptKeyId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createDate,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.FreelanceStat">
        UPDATE <include refid="table" /> SET
        VERSION = #{version,jdbcType=SMALLINT} +1,
        RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
        RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR},
        RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
        EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
        EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
        MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
        MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
        ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL},
        RECEIVER_ORDER = #{receiverOrder,jdbcType=BIGINT},
        CONDITION_ORDER = #{conditionOrder,jdbcType=BIGINT},
        SIGN_RECORD = #{signRecord,jdbcType=SMALLINT},
        ID_CARD = #{idCard,jdbcType=SMALLINT},
        ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT},
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
        CREATE_DATE = #{createDate,jdbcType=VARCHAR},
        SIGN_ID = #{signId,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.FreelanceStat">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                VERSION = #{version,jdbcType=SMALLINT} +1,
            </if>
            <if test="receiveName != null">
                RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR},
            </if>
            <if test="receiveIdCardNo != null">
                RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR},
            </if>
            <if test="receiveIdCardNoMd5 != null">
                RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR},
            </if>
            <if test="employerNo != null">
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="employerName != null">
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="orderItemNetAmount != null">
                ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL},
            </if>
            <if test="receiverOrder != null">
                RECEIVER_ORDER = #{receiverOrder,jdbcType=BIGINT},
            </if>
            <if test="conditionOrder != null">
                CONDITION_ORDER = #{conditionOrder,jdbcType=BIGINT},
            </if>
            <if test="signRecord != null">
                SIGN_RECORD = #{signRecord,jdbcType=SMALLINT},
            </if>
            <if test="idCard != null">
                ID_CARD = #{idCard,jdbcType=SMALLINT},
            </if>
            <if test="encryptKeyId != null">
                ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <select id="idCardList" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.entity.UserInfo">
        select b.*, a.EMPLOYER_NO, a.EMPLOYER_NAME  from tbl_user_info b
        left join tbl_freelance_stat a
        on b.RECEIVE_ID_CARD_NO_MD5 = a.RECEIVE_ID_CARD_NO_MD5
        where 1=1 <include refid="condition_id_card" />
        and	((b.ID_CARD_BACK_URL is not null and b.ID_CARD_BACK_URL != '')
        or (b.ID_CARD_FRONT_URL is not null and b.ID_CARD_BACK_URL != '')
        or (b.ID_CARD_COPY_URL is not null and b.ID_CARD_COPY_URL != ''))
        <if test="idCardMaxId != null">
            and b.ID <![CDATA[ < ]]> #{idCardMaxId,jdbcType=BIGINT}
        </if>
        group by b.RECEIVE_ID_CARD_NO_MD5, a.EMPLOYER_NO
        order by b.ID desc
    </select>


    <select id="listBy2" parameterType="java.util.Map" resultType="java.util.Map">
        select
        a.CREATE_DATE as createDate,
        a.CREATE_TIME as createTime,
        a.EMPLOYER_NO as employerNo,
        a.CONDITION_ORDER as conditionOrder,
        a.EMPLOYER_NAME as employerName,
        a.ENCRYPT_KEY_ID as encryptKeyId,
        a.ID as id,
        a.ID_CARD as idCard,
        a.ORDER_ITEM_NET_AMOUNT as orderItemNetAmount,
        a.MAINSTAY_NO as mainstayNo,
        a.MAINSTAY_NAME as mainstayName,
        a.RECEIVE_ID_CARD_NO as receiveIdCardNo,
        a.RECEIVE_NAME as receiveName,
        a.RECEIVE_PHONE_NO as receivePhoneNo,
        a.RECEIVER_ORDER as receiverOrder,
        a.RECEIVE_ID_CARD_NO_MD5 as receiveIdCardNoMd5,
        c.ID as signId,
        c.SIGN_STATUS as signStatus,
        c.RECEIVE_ACCOUNT_NO as receiveAccountNo,
        b.CER_FACE_URL as cerFaceUrl,
        b.ID_CARD_BACK_URL as idCardBackUrl,
        b.ID_CARD_FRONT_URL as idCardFrontUrl,
        b.ID_CARD_COPY_URL as idCardCopyUrl
        from tbl_freelance_stat a use index (uk_stat_no, idx_receiver_name, unk_01)
        left join tbl_user_info b on a.RECEIVE_ID_CARD_NO_MD5 = b.RECEIVE_ID_CARD_NO_MD5
        left join tbl_sign_record c on c.RECEIVE_NAME_MD5 = a.RECEIVE_NAME_MD5
        and c.RECEIVE_ID_CARD_NO_MD5 = a.RECEIVE_ID_CARD_NO_MD5
        and c.MAINSTAY_NO = a.MAINSTAY_NO
        and c.EMPLOYER_NO = a.EMPLOYER_NO
        <where>
            <if test="amountLimit == 1">
                and a.ORDER_ITEM_NET_AMOUNT > #{amountMin}
            </if>
            <if test="hasUploadIdCard == 1">
                and ((b.ID_CARD_BACK_URL is not null and b.ID_CARD_BACK_URL != '')
                or (b.ID_CARD_FRONT_URL is not null and b.ID_CARD_BACK_URL != '')
                or (b.ID_CARD_COPY_URL is not null and b.ID_CARD_COPY_URL != ''))

            </if>
            <if test="id != null">
                and a.ID = #{id,jdbcType=BIGINT}
            </if>

            <if test="hasUploadIdCard == 0">
                and (b.ID_CARD_BACK_URL is null or  b.ID_CARD_BACK_URL = '')
                and (b.ID_CARD_FRONT_URL is null or  b.ID_CARD_FRONT_URL = '')
                and (b.ID_CARD_COPY_URL is null or  b.ID_CARD_COPY_URL = '')
            </if>
            <if test="hasSign == 1">
                and c.SIGN_STATUS = 100
            </if>
            <if test="hasSign == 0">
                and (c.SIGN_STATUS != 100 or c.SIGN_STATUS is null)
            </if>
            <if test="version != null">
                and a.VERSION = #{version,jdbcType=SMALLINT}
            </if>
            <if test="receiveName != null and receiveName !=''">
                and a.RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNo != null and receiveIdCardNo !=''">
                and a.RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR}
            </if>
            <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
                and a.RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
            </if>
            <if test="employerNo != null and employerNo !=''">
                and a.EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>

            <if test="employerList != null and employerList.size() > 0">
                and a.EMPLOYER_NO in
                <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="employerName != null and employerName !=''">
                and a.EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and a.MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and a.MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="orderItemNetAmount != null">
                and a.ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL}
            </if>
            <if test="receiverOrder != null">
                and a.RECEIVER_ORDER = #{receiverOrder,jdbcType=BIGINT}
            </if>
            <if test="conditionOrder != null">
                and a.CONDITION_ORDER = #{conditionOrder,jdbcType=BIGINT}
            </if>
            <if test="signRecord != null">
                and a.SIGN_RECORD = #{signRecord,jdbcType=SMALLINT}
            </if>
            <if test="idCard != null">
                and a.ID_CARD = #{idCard,jdbcType=SMALLINT}
            </if>
            <if test="encryptKeyId != null">
                and a.ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT}
            </if>
            <if test="createTime != null">
                and a.CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createDate != null and createDate !=''">
                and a.CREATE_DATE = #{createDate,jdbcType=VARCHAR}
            </if>
            <!-- 自定义 -->
            <if test="beginDate != null and endDate != null" >
                and a.CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and a.CREATE_DATE &lt;= #{endDate,jdbcType=VARCHAR}
            </if>
            <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
                and a.RECEIVE_NAME_MD5 = #{receiveNameMd5, jdbcType=VARCHAR}
            </if>
            <if test="maxId != null">
                and a.ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
            </if>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY a.ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询 -->

    <select id="freelanceStatCount" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.vo.FreelanceStatVo">
        select
        count(distinct RECEIVE_ID_CARD_NO_MD5) as freelanceCount,
        sum(RECEIVER_ORDER) as orderCount,
        sum(ORDER_ITEM_NET_AMOUNT) as orderItemNetAmountSum
        from tbl_freelance_stat where 1=1
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
            and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5, jdbcType=VARCHAR}
        </if>
        <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
            and RECEIVE_NAME_MD5 = #{receiveNameMd5, jdbcType=VARCHAR}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo, jdbcType=VARCHAR}
        </if>

        <if test="employerList != null and employerList.size() > 0">
            and EMPLOYER_NO in
            <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo, jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        <if test="beginDate != null and endDate != null" >
            and CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and CREATE_DATE  &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="merchantStatCount" parameterType="java.util.Map" resultType="com.zhixianghui.facade.trade.vo.FreelanceStatVo">
        select
        count(distinct RECEIVE_ID_CARD_NO_MD5) as freelanceCount,
        sum(CONDITION_ORDER) as orderCount,
        sum(ORDER_ITEM_NET_AMOUNT) as orderItemNetAmountSum
        from tbl_freelance_stat where 1=1

        <if test="amountLimit == 1">
            and ORDER_ITEM_NET_AMOUNT > #{amountMin}
        </if>
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
            and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5, jdbcType=VARCHAR}
        </if>
        <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
            and RECEIVE_NAME_MD5 = #{receiveNameMd5, jdbcType=VARCHAR}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo, jdbcType=VARCHAR}
        </if>

        <if test="employerList != null and employerList.size() > 0">
            and EMPLOYER_NO in
            <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>


        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo, jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        <if test="beginDate != null and endDate != null" >
            and CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and CREATE_DATE  &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="freelanceCount" parameterType="java.util.Map" resultType="int">
        select
        count(distinct RECEIVE_ID_CARD_NO_MD5) as freelanceCount
        from tbl_freelance_stat where 1=1
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo, jdbcType=VARCHAR}
        </if>

        <if test="employerList != null and employerList.size() > 0">
            and EMPLOYER_NO in
            <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>

        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo, jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        <if test="beginDate != null and endDate != null" >
            and CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and CREATE_DATE  &lt;= #{endDate,jdbcType=VARCHAR}
        </if>

    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            and ID in
            <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="version != null">
            and VERSION = #{version,jdbcType=SMALLINT}
        </if>
        <if test="receiveName != null and receiveName !=''">
            and RECEIVE_NAME = #{receiveName,jdbcType=VARCHAR}
        </if>
        <if test="receiveIdCardNo != null and receiveIdCardNo !=''">
            and RECEIVE_ID_CARD_NO = #{receiveIdCardNo,jdbcType=VARCHAR}
        </if>
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
            and RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="employerList != null and employerList.size() > 0">
            and EMPLOYER_NO in
            <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="employerName != null and employerName !=''">
            and EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="orderItemNetAmount != null">
            and ORDER_ITEM_NET_AMOUNT = #{orderItemNetAmount,jdbcType=DECIMAL}
        </if>
        <if test="receiverOrder != null">
            and RECEIVER_ORDER = #{receiverOrder,jdbcType=BIGINT}
        </if>
        <if test="conditionOrder != null">
            and CONDITION_ORDER = #{conditionOrder,jdbcType=BIGINT}
        </if>
        <if test="signRecord != null">
            and SIGN_RECORD = #{signRecord,jdbcType=SMALLINT}
        </if>
        <if test="idCard != null">
            and ID_CARD = #{idCard,jdbcType=SMALLINT}
        </if>
        <if test="encryptKeyId != null">
            and ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createDate != null and createDate !=''">
            and CREATE_DATE = #{createDate,jdbcType=VARCHAR}
        </if>
        <!-- 自定义 -->
        <if test="beginDate != null and endDate != null" >
            and CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and CREATE_DATE  &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
        <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
            and RECEIVE_NAME_MD5 = #{receiveNameMd5, jdbcType=VARCHAR}
        </if>
        <if test="maxId != null">
            and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
    </sql>

    <sql id="condition_id_card">
        <if test="receiveIdCardNoMd5 != null and receiveIdCardNoMd5 !=''">
            and b.RECEIVE_ID_CARD_NO_MD5 = #{receiveIdCardNoMd5,jdbcType=VARCHAR}
        </if>
        <if test="employerNo != null and employerNo !=''">
            and a.EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="employerList != null and employerList.size() > 0">
            and a.EMPLOYER_NO in
            <foreach collection="employerList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="employerName != null and employerName !=''">
            and a.EMPLOYER_NAME LIKE concat('%',#{employerName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and a.MAINSTAY_NAME LIKE concat('%',#{mainstayName, jdbcType=VARCHAR},'%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and a.MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <!-- 自定义 -->
        <if test="beginDate != null and endDate != null">
            and a.CREATE_DATE >= #{beginDate,jdbcType=VARCHAR} and a.CREATE_DATE  &lt;= #{endDate,jdbcType=VARCHAR}
        </if>
        <if test="receiveNameMd5 != null and receiveNameMd5 !=''">
            and b.RECEIVE_NAME_MD5 = #{receiveNameMd5, jdbcType=VARCHAR}
        </if>
        <if test="maxId != null">
            and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
        </if>
    </sql>

</mapper>
