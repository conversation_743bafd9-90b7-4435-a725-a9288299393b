<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.InvoiceRecordDetailMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.InvoiceRecordDetail">
    <!--@mbg.generated-->
    <!--@Table tbl_invoice_record_detail-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="INVOICE_TRX_NO" jdbcType="VARCHAR" property="invoiceTrxNo" />
    <result column="PLAT_TRX_NO" jdbcType="VARCHAR" property="platTrxNo" />
    <result column="PRODUCT_NO" jdbcType="VARCHAR" property="productNo" />
    <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
    <result column="INVOICE_AMOUNT" jdbcType="DECIMAL" property="invoiceAmount" />
    <result column="INVOICE_FILE_URL" jdbcType="OTHER" property="invoiceFileUrl" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="EMPLOYER_MCH_NO" jdbcType="VARCHAR" property="employerMchNo" />
    <result column="EMPLOYER_MCH_NAME" jdbcType="VARCHAR" property="employerMchName" />
    <result column="MAINSTAY_MCH_NO" jdbcType="VARCHAR" property="mainstayMchNo" />
    <result column="MAINSTAY_MCH_NAME" jdbcType="VARCHAR" property="mainstayMchName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="COMPLETE_TIME" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="INVOICE_STATUS" jdbcType="SMALLINT" property="invoiceStatus" />
    <result column="RECEIVE_NAME" jdbcType="VARCHAR" property="receiveName" />
    <result column="RECEIVE_NAME_MD5" jdbcType="VARCHAR" property="receiveNameMd5" />
    <result column="RECEIVE_ID_CARD_NO" jdbcType="VARCHAR" property="receiveIdCardNo" />
    <result column="RECEIVE_ID_CARD_NO_MD5" jdbcType="VARCHAR" property="receiveIdCardNoMd5" />
    <result column="RECEIVE_ACCOUNT_NO" jdbcType="VARCHAR" property="receiveAccountNo" />
    <result column="RECEIVE_ACCOUNT_NO_MD5" jdbcType="VARCHAR" property="receiveAccountNoMd5" />
    <result column="RECEIVE_PHONE_NO" jdbcType="VARCHAR" property="receivePhoneNo" />
    <result column="RECEIVE_PHONE_NO_MD5" jdbcType="VARCHAR" property="receivePhoneNoMd5" />
    <result column="ENCRYPT_KEY_ID" jdbcType="BIGINT" property="encryptKeyId" />
    <result column="ACCOUNT_HANDLE_STATUS" jdbcType="INTEGER" property="accountHandleStatus" />
    <result column="ERROR_DESC" jdbcType="VARCHAR" property="errorDesc" />
    <result column="EXPRESS_COMPANY" jdbcType="VARCHAR" property="expressCompany" />
    <result column="EXPRESS_NO" jdbcType="VARCHAR" property="expressNo" />
    <result column="WORKER_BILL_FILE_PATH" jdbcType="VARCHAR" property="workerBillFilePath" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, INVOICE_TYPE, INVOICE_TRX_NO, PLAT_TRX_NO, PRODUCT_NO, PRODUCT_NAME, INVOICE_AMOUNT,
    INVOICE_FILE_URL, REMARK, EMPLOYER_MCH_NO, EMPLOYER_MCH_NAME, MAINSTAY_MCH_NO, MAINSTAY_MCH_NAME,
    CREATE_TIME, UPDATE_TIME, COMPLETE_TIME, INVOICE_STATUS, RECEIVE_NAME, RECEIVE_NAME_MD5,
    RECEIVE_ID_CARD_NO, RECEIVE_ID_CARD_NO_MD5, RECEIVE_ACCOUNT_NO, RECEIVE_ACCOUNT_NO_MD5,
    RECEIVE_PHONE_NO, RECEIVE_PHONE_NO_MD5, ENCRYPT_KEY_ID,ACCOUNT_HANDLE_STATUS,ERROR_DESC,
    EXPRESS_COMPANY,EXPRESS_NO,WORKER_BILL_FILE_PATH
  </sql>

  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_invoice_record_detail
    (INVOICE_TYPE, INVOICE_TRX_NO, PLAT_TRX_NO, PRODUCT_NO, PRODUCT_NAME, INVOICE_AMOUNT,
    INVOICE_FILE_URL, REMARK, EMPLOYER_MCH_NO, EMPLOYER_MCH_NAME, MAINSTAY_MCH_NO,
    MAINSTAY_MCH_NAME, CREATE_TIME, UPDATE_TIME, COMPLETE_TIME, INVOICE_STATUS, RECEIVE_NAME,
    RECEIVE_NAME_MD5, RECEIVE_ID_CARD_NO, RECEIVE_ID_CARD_NO_MD5, RECEIVE_ACCOUNT_NO,
    RECEIVE_ACCOUNT_NO_MD5, RECEIVE_PHONE_NO, RECEIVE_PHONE_NO_MD5, ENCRYPT_KEY_ID,ACCOUNT_HANDLE_STATUS,
    ERROR_DESC,EXPRESS_COMPANY,EXPRESS_NO,WORKER_BILL_FILE_PATH)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.invoiceType,jdbcType=INTEGER}, #{item.invoiceTrxNo,jdbcType=VARCHAR}, #{item.platTrxNo,jdbcType=VARCHAR},
      #{item.productNo,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, #{item.invoiceAmount,jdbcType=DECIMAL},
      #{item.invoiceFileUrl,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.employerMchNo,jdbcType=VARCHAR},
      #{item.employerMchName,jdbcType=VARCHAR}, #{item.mainstayMchNo,jdbcType=VARCHAR},
      #{item.mainstayMchName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.completeTime,jdbcType=TIMESTAMP},
      #{item.invoiceStatus,jdbcType=SMALLINT}, #{item.receiveName,jdbcType=VARCHAR},
      #{item.receiveNameMd5,jdbcType=VARCHAR}, #{item.receiveIdCardNo,jdbcType=VARCHAR},
      #{item.receiveIdCardNoMd5,jdbcType=VARCHAR}, #{item.receiveAccountNo,jdbcType=VARCHAR},
      #{item.receiveAccountNoMd5,jdbcType=VARCHAR}, #{item.receivePhoneNo,jdbcType=VARCHAR},
      #{item.receivePhoneNoMd5,jdbcType=VARCHAR}, #{item.encryptKeyId,jdbcType=BIGINT},
      #{item.accountHandleStatus,jdbcType=INTEGER},
      #{item.errorDesc,jdbcType=VARCHAR},
      #{item.expressCompany,jdbcType=VARCHAR},
      #{item.expressNo,jdbcType=VARCHAR},
      #{item.workerBillFilePath,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <update id="updateStatusByInvoceTrxNo" parameterType="java.lang.String">
      update tbl_invoice_record_detail
      set INVOICE_STATUS = #{status,jdbcType=INTEGER}
      where INVOICE_TRX_NO = #{invoceTrxNo,jdbcType=VARCHAR}
  </update>

  <select id="listInvoiceDetailGroupByIdCard" resultType="java.util.Map">
          select t.RECEIVE_ID_CARD_NO_MD5 idCardMd5,INVOICE_TYPE invoiceType,INVOICE_STATUS invoiceStatus, sum(t.INVOICE_AMOUNT) sumAmount from trade.tbl_invoice_record_detail t
          where t.INVOICE_TRX_NO=#{inviceTrxNo,jdbcType=VARCHAR}
          group by t.RECEIVE_ID_CARD_NO_MD5,t.INVOICE_STATUS,t.INVOICE_TYPE
  </select>
</mapper>
