<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.MchPlatBatch">
    <sql id="table">tbl_mch_plat_batch</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.MchPlatBatch">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MCH_BATCH_NO" property="mchBatchNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="PLAT_BATCH_NO" property="platBatchNo" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_TIME, MCH_BATCH_NO, EMPLOYER_NO, PLAT_BATCH_NO
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.MchPlatBatch">
        INSERT INTO <include refid="table" /> (
            CREATE_TIME,
            MCH_BATCH_NO,
            EMPLOYER_NO,
            PLAT_BATCH_NO
        ) VALUES (
        #{createTime,jdbcType=TIMESTAMP},
        #{mchBatchNo,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{platBatchNo,jdbcType=VARCHAR}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.MchPlatBatch">
        UPDATE <include refid="table" /> SET
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR},
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
                PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.MchPlatBatch">
        UPDATE <include refid="table" />
        <set>
            <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mchBatchNo != null">
                    MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="employerNo != null">
                    EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="platBatchNo != null">
                    PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!--按条件删除-->
    <delete id="deleteBy" parameterType="java.util.Map">
        DELETE FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="mchBatchNo != null and mchBatchNo !=''">
                and MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="employerNo != null and employerNo !=''">
                and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="platBatchNo != null and platBatchNo !=''">
                and PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
            </if>
    </sql>

</mapper>
