<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.WxMerchantBalance">
    <sql id="table">tbl_wx_merchant_balance</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.WxMerchantBalance">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
        <result column="mainstay_no" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="mainstay_name" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="total_amount" property="totalAmount" jdbcType="BIGINT"/>
        <result column="freeze_amount" property="freezeAmount" jdbcType="BIGINT"/>
        <result column="merchant_type" property="merchantType" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, mch_no, mch_name, mainstay_no, mainstay_name, total_amount, freeze_amount, merchant_type,create_time, update_time
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.WxMerchantBalance">
        INSERT INTO <include refid="table" /> (
            version,
            mch_no,
            mch_name,
            mainstay_no,
            mainstay_name,
            total_amount,
            freeze_amount,
            merchant_type,
            create_time,
            update_time
        ) VALUES (
            #{version,jdbcType=SMALLINT},
            #{mchNo,jdbcType=VARCHAR},
            #{mchName,jdbcType=VARCHAR},
            #{mainstayNo,jdbcType=VARCHAR},
            #{mainstayName,jdbcType=VARCHAR},
            #{totalAmount,jdbcType=BIGINT},
            #{freezeAmount,jdbcType=BIGINT},
            #{merchantType,jdbcType=SMALLINT},
            #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            version,
            mch_no,
            mch_name,
            mainstay_no,
            mainstay_name,
            total_amount,
            freeze_amount,
            merchant_type,
            create_time,
            update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.version,jdbcType=SMALLINT},
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.mchName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.totalAmount,jdbcType=BIGINT},
            #{item.freezeAmount,jdbcType=BIGINT},
            #{item.merchantType,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}
        )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.WxMerchantBalance">
        UPDATE <include refid="table" /> SET
            version = #{version,jdbcType=SMALLINT} +1 ,
            mch_no = #{mchNo,jdbcType=VARCHAR},
            mch_name = #{mchName,jdbcType=VARCHAR},
            mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            total_amount = #{totalAmount,jdbcType=BIGINT},
            freeze_amount = #{freezeAmount,jdbcType=BIGINT},
            merchant_type = #{merchantType,jdbcType=SMALLINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.WxMerchantBalance">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT} +1 ,
            </if>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                mch_name = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=BIGINT},
            </if>
            <if test="freezeAmount != null">
                freeze_amount = #{freezeAmount,jdbcType=BIGINT},
            </if>
            <if test="merchantType != null">
                merchant_type = #{merchantType,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and mch_no = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="mchName != null and mchName !=''">
            and mch_name = #{mchName,jdbcType=VARCHAR}
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and mainstay_name = #{mainstayName,jdbcType=VARCHAR}
        </if>
        <if test="totalAmount != null">
            and total_amount = #{totalAmount,jdbcType=BIGINT}
        </if>
        <if test="freezeAmount != null">
            and freeze_amount = #{freezeAmount,jdbcType=BIGINT}
        </if>
        <if test="merchantType != null">
            and merchant_type = #{merchantType,jdbcType=SMALLINT}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
    </sql>

</mapper>
