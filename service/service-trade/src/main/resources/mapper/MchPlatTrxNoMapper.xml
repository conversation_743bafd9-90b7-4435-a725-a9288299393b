<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.MchPlatTrxNo">
    <sql id="table">tbl_mch_plat_trx_no</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.MchPlatTrxNo">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MCH_ORDER_NO" property="mchOrderNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TRX_NO" property="channelTrxNo" jdbcType="VARCHAR"/>
        <result column="PLAT_TRX_NO" property="platTrxNo" jdbcType="VARCHAR"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CREATE_TIME, MCH_ORDER_NO, EMPLOYER_NO, CHANNEL_TRX_NO, PLAT_TRX_NO
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.MchPlatTrxNo">
        INSERT INTO <include refid="table" /> (
            CREATE_TIME,
            MCH_ORDER_NO,
            EMPLOYER_NO,
            CHANNEL_TRX_NO,
            PLAT_TRX_NO
        ) VALUES (
        #{createTime,jdbcType=TIMESTAMP},
        #{mchOrderNo,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{channelTrxNo,jdbcType=VARCHAR},
        #{platTrxNo,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
            CREATE_TIME,
            MCH_ORDER_NO,
            EMPLOYER_NO,
            CHANNEL_TRX_NO,
            PLAT_TRX_NO
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.mchOrderNo,jdbcType=VARCHAR},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.channelTrxNo,jdbcType=VARCHAR},
            #{item.platTrxNo,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.MchPlatTrxNo">
        UPDATE <include refid="table" /> SET
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR},
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
                CHANNEL_TRX_NO = #{channelTrxNo,jdbcType=VARCHAR},
                PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR}
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.MchPlatTrxNo">
        UPDATE <include refid="table" />
        <set>
            <if test="createTime != null">
                    CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mchOrderNo != null">
                    MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="employerNo != null">
                    EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
            </if>
            <if test="channelTrxNo != null">
                    CHANNEL_TRX_NO = #{channelTrxNo,jdbcType=VARCHAR},
            </if>
            <if test="platTrxNo != null">
                    PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询 -->
    <select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!--按条件删除-->
    <delete id="deleteBy" parameterType="java.util.Map">
        DELETE FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </delete>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="mchOrderNo != null and mchOrderNo !=''">
                and MCH_ORDER_NO = #{mchOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="employerNo != null and employerNo !=''">
                and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="channelTrxNo != null and channelTrxNo !=''">
                and CHANNEL_TRX_NO = #{channelTrxNo,jdbcType=VARCHAR}
            </if>
            <if test="platTrxNo != null and platTrxNo !=''">
                and PLAT_TRX_NO = #{platTrxNo,jdbcType=VARCHAR}
            </if>
    </sql>

</mapper>
