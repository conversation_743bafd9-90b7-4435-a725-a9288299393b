<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.trade.dao.mapper.IndividualProxyOrderMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.IndividualProxyOrder">
    <!--@mbg.generated-->
    <!--@Table tbl_individual_proxy_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mainstay_no" jdbcType="VARCHAR" property="mainstayNo" />
    <result column="mainstay_name" jdbcType="VARCHAR" property="mainstayName" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="pay_trx_no" jdbcType="VARCHAR" property="payTrxNo" />
    <result column="pay_begin_time" jdbcType="TIMESTAMP" property="payBeginTime" />
    <result column="pay_complete_time" jdbcType="TIMESTAMP" property="payCompleteTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="is_refund" jdbcType="BOOLEAN" property="isRefund" />
    <result column="refund_begin_time" jdbcType="TIMESTAMP" property="refundBeginTime" />
    <result column="refund_complete_time" jdbcType="TIMESTAMP" property="refundCompleteTime" />
    <result column="refund_no" jdbcType="VARCHAR" property="refundNo" />
    <result column="refund_reason" jdbcType="SMALLINT" property="refundReason"/>
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="invoice_status" jdbcType="INTEGER" property="invoiceStatus" />
    <result column="invoice_detail" jdbcType="VARCHAR" property="invoiceDetail" typeHandler="com.zhixianghui.facade.trade.handler.ProxyOrderInvoiceHandler"/>
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="fund_flow_file" jdbcType="VARCHAR" property="fundFlowFile"  typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="business_contract_file" jdbcType="VARCHAR" property="businessContractFile"  typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="entrust_agreement_file" jdbcType="VARCHAR" property="entrustAgreementFile"  typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo" />
    <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
    <result column="invoice_title_id" jdbcType="BIGINT" property="invoiceTitleId" />
    <result column="invoice_title_tax_no" jdbcType="VARCHAR" property="invoiceTitleTaxNo" />
    <result column="invoice_title_company_name" jdbcType="VARCHAR" property="invoiceTitleCompanyName" />
    <result column="invoice_applicant" jdbcType="VARCHAR" property="invoiceApplicant" />
    <result column="address_id" jdbcType="BIGINT" property="addressId" />
    <result column="address_detail" jdbcType="VARCHAR" property="addressDetail" />
    <result column="address_name" jdbcType="VARCHAR" property="addressName" />
    <result column="address_mobile" jdbcType="VARCHAR" property="addressMobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="fee_detail" jdbcType="VARCHAR" property="feeDetail" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="invoice_url" jdbcType="VARCHAR" property="invoiceUrl" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    <result column="express_no" jdbcType="VARCHAR" property="expressNo"/>
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
    <result column="error_remark" jdbcType="VARCHAR" property="errorRemark"/>
    <result column="user_mobile" jdbcType="VARCHAR" property="userMobile"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, mainstay_no, mainstay_name, version, order_no, pay_trx_no, pay_begin_time, pay_complete_time,
    complete_time, is_refund, refund_begin_time, refund_complete_time, refund_no,refund_reason, order_status,
    pay_status, invoice_status, invoice_detail, remark, fund_flow_file, business_contract_file,
    entrust_agreement_file, order_amount, pay_amount, id_card_no, invoice_type, invoice_title_id,
    invoice_title_tax_no, invoice_title_company_name, invoice_applicant, address_id, address_detail,
    address_name, address_mobile, create_time, update_time,fee_detail,invoice_url,express_no,expire_time,errorRemark,user_mobile
  </sql>
</mapper>
