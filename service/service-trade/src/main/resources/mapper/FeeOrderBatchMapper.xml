<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.FeeOrderBatch">
    <sql id="table">
        tbl_fee_order_batch
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.FeeOrderBatch">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="fee_batch_no" property="feeBatchNo" jdbcType="VARCHAR"/>
        <result column="create_date" property="createDate" jdbcType="DATE"/>
        <result column="employer_no" property="employerNo" jdbcType="VARCHAR"/>
        <result column="employer_name" property="employerName" jdbcType="VARCHAR"/>
        <result column="mainstay_no" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="mainstay_name" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="pay_channel_no" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="channel_name" property="channelName" jdbcType="VARCHAR"/>
        <result column="channel_type" property="channelType" jdbcType="SMALLINT"/>
        <result column="product_no" property="productNo" jdbcType="VARCHAR"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="order_batch_count" property="orderBatchCount" jdbcType="INTEGER"/>
        <result column="order_item_count" property="orderItemCount" jdbcType="INTEGER"/>
        <result column="success_count" property="successCount" jdbcType="INTEGER"/>
        <result column="fail_count" property="failCount" jdbcType="INTEGER"/>
        <result column="task_amount" property="taskAmount" jdbcType="DECIMAL"/>
        <result column="order_net_amount" property="orderNetAmount" jdbcType="DECIMAL"/>
        <result column="tax_amount" property="taxAmount" jdbcType="DECIMAL"/>
        <result column="fee_amount" property="feeAmount" jdbcType="DECIMAL"/>
        <result column="receipt_url" property="receiptUrl" jdbcType="VARCHAR"/>
        <result column="balanced_mode" property="balancedMode" jdbcType="SMALLINT"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="complete_time" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="fee_source" property="feeSource" jdbcType="SMALLINT"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">

        id, version, fee_batch_no, create_date, employer_no, employer_name, mainstay_no, mainstay_name, pay_channel_no, channel_name, channel_type, product_no, product_name, order_batch_count, order_item_count, success_count, fail_count, task_amount, order_net_amount, tax_amount, fee_amount, receipt_url, balanced_mode, status, complete_time, create_time, update_time,fee_source

    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.zhixianghui.facade.trade.entity.FeeOrderBatch">

        INSERT INTO
        <include refid="table"/>
         (
        version,
        fee_batch_no,
        create_date,
        employer_no,
        employer_name,
        mainstay_no,
        mainstay_name,
        pay_channel_no,
        channel_name,
        channel_type,
        product_no,
        product_name,
        order_batch_count,
        order_item_count,
        success_count,
        fail_count,
        task_amount,
        order_net_amount,
        tax_amount,
        fee_amount,
        receipt_url,
        balanced_mode,
        status,
        complete_time,
        create_time,
        update_time,
        fee_source
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{feeBatchNo,jdbcType=VARCHAR},
        #{createDate,jdbcType=DATE},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{payChannelNo,jdbcType=VARCHAR},
        #{channelName,jdbcType=VARCHAR},
        #{channelType,jdbcType=SMALLINT},
        #{productNo,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{orderBatchCount,jdbcType=INTEGER},
        #{orderItemCount,jdbcType=INTEGER},
        #{successCount,jdbcType=INTEGER},
        #{failCount,jdbcType=INTEGER},
        #{taskAmount,jdbcType=DECIMAL},
        #{orderNetAmount,jdbcType=DECIMAL},
        #{taxAmount,jdbcType=DECIMAL},
        #{feeAmount,jdbcType=DECIMAL},
        #{receiptUrl,jdbcType=VARCHAR},
        #{balancedMode,jdbcType=SMALLINT},
        #{status,jdbcType=SMALLINT},
        #{completeTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{feeSource,jdbcType=SMALLINT}
        )

    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">

        INSERT INTO

        <include refid="table"/>

        (
        version,
        fee_batch_no,
        create_date,
        employer_no,
        employer_name,
        mainstay_no,
        mainstay_name,
        pay_channel_no,
        channel_name,
        channel_type,
        product_no,
        product_name,
        order_batch_count,
        order_item_count,
        success_count,
        fail_count,
        task_amount,
        order_net_amount,
        tax_amount,
        fee_amount,
        receipt_url,
        balanced_mode,
        status,
        complete_time,
        create_time,
        update_time,
        fee_source
        ) VALUES

        <foreach collection="list" item="item" separator=",">
            (
            #{item.version,jdbcType=SMALLINT},
            #{item.feeBatchNo,jdbcType=VARCHAR},
            #{item.createDate,jdbcType=DATE},
            #{item.employerNo,jdbcType=VARCHAR},
            #{item.employerName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.payChannelNo,jdbcType=VARCHAR},
            #{item.channelName,jdbcType=VARCHAR},
            #{item.channelType,jdbcType=SMALLINT},
            #{item.productNo,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR},
            #{item.orderBatchCount,jdbcType=INTEGER},
            #{item.orderItemCount,jdbcType=INTEGER},
            #{item.successCount,jdbcType=INTEGER},
            #{item.failCount,jdbcType=INTEGER},
            #{item.taskAmount,jdbcType=DECIMAL},
            #{item.orderNetAmount,jdbcType=DECIMAL},
            #{item.taxAmount,jdbcType=DECIMAL},
            #{item.feeAmount,jdbcType=DECIMAL},
            #{item.receiptUrl,jdbcType=VARCHAR},
            #{item.balancedMode,jdbcType=SMALLINT},
            #{item.status,jdbcType=SMALLINT},
            #{item.completeTime,jdbcType=TIMESTAMP},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.feeSource,jdbcType=SMALLINT}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.FeeOrderBatch">

        UPDATE
        <include refid="table"/>
         SET
        version = #{version,jdbcType=SMALLINT},
        fee_batch_no = #{feeBatchNo,jdbcType=VARCHAR},
        create_date = #{createDate,jdbcType=DATE},
        employer_no = #{employerNo,jdbcType=VARCHAR},
        employer_name = #{employerName,jdbcType=VARCHAR},
        mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
        mainstay_name = #{mainstayName,jdbcType=VARCHAR},
        pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},
        channel_name = #{channelName,jdbcType=VARCHAR},
        channel_type = #{channelType,jdbcType=SMALLINT},
        product_no = #{productNo,jdbcType=VARCHAR},
        product_name = #{productName,jdbcType=VARCHAR},
        order_batch_count = #{orderBatchCount,jdbcType=INTEGER},
        order_item_count = #{orderItemCount,jdbcType=INTEGER},
        success_count = #{successCount,jdbcType=INTEGER},
        fail_count = #{failCount,jdbcType=INTEGER},
        task_amount = #{taskAmount,jdbcType=DECIMAL},
        order_net_amount = #{orderNetAmount,jdbcType=DECIMAL},
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
        fee_amount = #{feeAmount,jdbcType=DECIMAL},
        receipt_url = #{receiptUrl,jdbcType=VARCHAR},
        balanced_mode = #{balancedMode,jdbcType=SMALLINT},
        status = #{status,jdbcType=SMALLINT},
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        fee_source = #{feeSource,jdbcType=SMALLINT}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}

    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.FeeOrderBatch">

        UPDATE
        <include refid="table"/>
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT},

            </if>
            <if test="feeBatchNo != null">

                fee_batch_no = #{feeBatchNo,jdbcType=VARCHAR},

            </if>
            <if test="createDate != null">

                create_date = #{createDate,jdbcType=DATE},

            </if>
            <if test="employerNo != null">

                employer_no = #{employerNo,jdbcType=VARCHAR},

            </if>
            <if test="employerName != null">

                employer_name = #{employerName,jdbcType=VARCHAR},

            </if>
            <if test="mainstayNo != null">

                mainstay_no = #{mainstayNo,jdbcType=VARCHAR},

            </if>
            <if test="mainstayName != null">

                mainstay_name = #{mainstayName,jdbcType=VARCHAR},

            </if>
            <if test="payChannelNo != null">

                pay_channel_no = #{payChannelNo,jdbcType=VARCHAR},

            </if>
            <if test="channelName != null">

                channel_name = #{channelName,jdbcType=VARCHAR},

            </if>
            <if test="channelType != null">

                channel_type = #{channelType,jdbcType=SMALLINT},

            </if>
            <if test="productNo != null">

                product_no = #{productNo,jdbcType=VARCHAR},

            </if>
            <if test="productName != null">

                product_name = #{productName,jdbcType=VARCHAR},

            </if>
            <if test="orderBatchCount != null">

                order_batch_count = #{orderBatchCount,jdbcType=INTEGER},

            </if>
            <if test="orderItemCount != null">

                order_item_count = #{orderItemCount,jdbcType=INTEGER},

            </if>
            <if test="successCount != null">

                success_count = #{successCount,jdbcType=INTEGER},

            </if>
            <if test="failCount != null">

                fail_count = #{failCount,jdbcType=INTEGER},

            </if>
            <if test="taskAmount != null">

                task_amount = #{taskAmount,jdbcType=DECIMAL},

            </if>
            <if test="orderNetAmount != null">

                order_net_amount = #{orderNetAmount,jdbcType=DECIMAL},

            </if>
            <if test="taxAmount != null">

                tax_amount = #{taxAmount,jdbcType=DECIMAL},

            </if>
            <if test="feeAmount != null">

                fee_amount = #{feeAmount,jdbcType=DECIMAL},

            </if>
            <if test="receiptUrl != null">

                receipt_url = #{receiptUrl,jdbcType=VARCHAR},

            </if>
            <if test="balancedMode != null">

                balanced_mode = #{balancedMode,jdbcType=SMALLINT},

            </if>
            <if test="status != null">

                status = #{status,jdbcType=SMALLINT},

            </if>
            <if test="completeTime != null">

                complete_time = #{completeTime,jdbcType=TIMESTAMP},

            </if>
            <if test="createTime != null">

                create_time = #{createTime,jdbcType=TIMESTAMP},

            </if>
            <if test="updateTime != null">

                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="feeSource != null">
                fee_source = #{feeSource,jdbcType=SMALLINT}
            </if>
        </set>

        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}

    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">

        SELECT

        <include refid="Base_Column_List"/>

        FROM

        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns != ''">
                <![CDATA[
                 ORDER BY ${sortColumns}
                ]]>
            </when>
            <otherwise>
                <![CDATA[
                 ORDER BY ID DESC
                ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">

        SELECT COUNT(1) FROM

        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">

        SELECT
        <include refid="Base_Column_List"/>

        FROM
        <include refid="table"/>

        WHERE id = #{id,jdbcType=BIGINT}

    </select>

    <!--按id主键删除-->
    <delete id="deleteById">

        DELETE FROM
        <include refid="table"/>
         WHERE id = #{id,jdbcType=BIGINT}

    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="feeBatchNo != null and feeBatchNo != ''">
            and fee_batch_no = #{feeBatchNo,jdbcType=VARCHAR}
        </if>
        <if test="createDate != null">
            and create_date = #{createDate,jdbcType=DATE}
        </if>
        <if test="employerNo != null and employerNo != ''">
            and employer_no = #{employerNo,jdbcType=VARCHAR}
        </if>
        <if test="employerName != null and employerName != ''">
            and employer_name like concat('%', #{employerName}, '%')
        </if>
        <if test="mainstayNo != null and mainstayNo != ''">
            and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName != ''">
            and mainstay_name like concat('%', #{mainstayName}, '%')
        </if>
        <if test="payChannelNo != null and payChannelNo != ''">
            and pay_channel_no = #{payChannelNo,jdbcType=VARCHAR}
        </if>
        <if test="channelName != null and channelName != ''">
            and channel_name = #{channelName,jdbcType=VARCHAR}
        </if>
        <if test="channelType != null">
            and channel_type = #{channelType,jdbcType=SMALLINT}
        </if>
        <if test="productNo != null and productNo != ''">
            and product_no = #{productNo,jdbcType=VARCHAR}
        </if>
        <if test="productName != null and productName != ''">
            and product_name like concat('%', #{productName}, '%')
        </if>
        <if test="orderBatchCount != null">
            and order_batch_count = #{orderBatchCount,jdbcType=INTEGER}
        </if>
        <if test="orderItemCount != null">
            and order_item_count = #{orderItemCount,jdbcType=INTEGER}
        </if>
        <if test="successCount != null">
            and success_count = #{successCount,jdbcType=INTEGER}
        </if>
        <if test="failCount != null">
            and fail_count = #{failCount,jdbcType=INTEGER}
        </if>
        <if test="taskAmount != null">
            and task_amount = #{taskAmount,jdbcType=DECIMAL}
        </if>
        <if test="orderNetAmount != null">
            and order_net_amount = #{orderNetAmount,jdbcType=DECIMAL}
        </if>
        <if test="taxAmount != null">
            and tax_amount = #{taxAmount,jdbcType=DECIMAL}
        </if>
        <if test="feeAmount != null">
            and fee_amount = #{feeAmount,jdbcType=DECIMAL}
        </if>
        <if test="receiptUrl != null and receiptUrl != ''">
            and receipt_url = #{receiptUrl,jdbcType=VARCHAR}
        </if>
        <if test="balancedMode != null">
            and balanced_mode = #{balancedMode,jdbcType=SMALLINT}
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=SMALLINT}
        </if>
        <if test="completeTime != null">
            and complete_time = #{completeTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>


        <!-- 账单日期范围查询 -->
        <if test="startDate != null">
            and create_date >= #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            and create_date &lt;= #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="notStatus != null">
            and status != #{notStatus,jdbcType=SMALLINT}
        </if>
        <if test="feeSource != null">
            and fee_source = #{feeSource,jdbcType=SMALLINT}
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and status in
            <foreach collection="statusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
    </sql>


    <select id="selectCounts" resultType="com.zhixianghui.facade.trade.vo.FeeOrderCountVo">

        select `status`, count(*) count
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>

        group by `status`

    </select>

    <select id="selectSums" resultType="com.zhixianghui.facade.trade.vo.FeeOrderSumVo">

        select sum(order_net_amount) order_net_amount,sum(tax_amount) tax_amount,sum(fee_amount) fee_amount
        FROM
        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>
    </select>

    <select id="selectFailSum" resultType="java.math.BigDecimal">

        select  ifnull(sum(foi.pay_amount),0) pay_amount
        from tbl_fee_order_batch fob, tbl_fee_order_item foi

        <where>
            fob.fee_batch_no=foi.fee_batch_no and
            foi.`status`!=400

            <if test="feeSource != null">
                and foi.fee_source = #{feeSource,jdbcType=SMALLINT}
            </if>
            <if test="employerNo != null and employerNo != ''">

                and fob.employer_no = #{employerNo,jdbcType=VARCHAR}

            </if>
            <if test="employerName != null and employerName != ''">

                and fob.employer_name  like concat('%', #{employerName}, '%')

            </if>
            <if test="mainstayNo != null and mainstayNo != ''">

                and fob.mainstay_no = #{mainstayNo,jdbcType=VARCHAR}

            </if>
            <if test="mainstayName != null and mainstayName != ''">

                and fob.mainstay_name  like concat('%', #{mainstayName}, '%')

            </if>
            <if test="payChannelNo != null and payChannelNo != ''">

                and fob.pay_channel_no = #{payChannelNo,jdbcType=VARCHAR}

            </if>
            <if test="channelName != null and channelName != ''">

                and fob.channel_name = #{channelName,jdbcType=VARCHAR}

            </if>
            <if test="channelType != null">

                and fob.channel_type = #{channelType,jdbcType=SMALLINT}

            </if>
            <if test="productNo != null and productNo != ''">

                and fob.product_no = #{productNo,jdbcType=VARCHAR}

            </if>
            <if test="productName != null and productName != ''">

                and fob.product_name  like concat('%', #{productName}, '%')

            </if>
            <if test="status != null">

                and fob.status = #{status,jdbcType=SMALLINT}

            </if>
            <if test="balancedMode != null">

                and fob.balanced_mode = #{balancedMode,jdbcType=SMALLINT}

            </if>
            <!-- 账单日期范围查询 -->
            <if test="startDate != null">

                and fob.create_date >= #{startDate,jdbcType=TIMESTAMP}

            </if>
            <if test="endDate != null">

                and fob.create_date &lt;= #{startDate,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <select id="selectSingleBatch" parameterType="java.util.Map" resultMap="BaseResultMap">


        select * from

        <include refid="table"/>
        <where>
            <include refid="condition_sql"/>
        </where>

        limit 1

    </select>
</mapper>
