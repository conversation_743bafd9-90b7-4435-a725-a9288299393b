<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.ChangesFunds">
    <sql id="table">tbl_wx_changes_funds</sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.ChangesFunds">
        <id column="id" property="id" jdbcType="BIGINT"/>

        <result column="version" property="version" jdbcType="SMALLINT"/>
        <result column="log_key" property="logKey" jdbcType="VARCHAR"/>
        <result column="merchant_type" property="merchantType" jdbcType="SMALLINT"/>
        <result column="mch_no" property="mchNo" jdbcType="VARCHAR"/>
        <result column="mch_name" property="mchName" jdbcType="VARCHAR"/>
        <result column="mainstay_no" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="mainstay_name" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="plat_trx_no" property="platTrxNo" jdbcType="VARCHAR"/>
        <result column="amount_change_type" property="amountChangeType" jdbcType="SMALLINT"/>
        <result column="amount" property="amount" jdbcType="BIGINT"/>
        <result column="frozen_amount" property="frozenAmount" jdbcType="BIGINT"/>
        <result column="before_amount" property="beforeAmount" jdbcType="BIGINT"/>
        <result column="before_frozen_amount" property="beforeFrozenAmount" jdbcType="BIGINT"/>
        <result column="after_amount" property="afterAmount" jdbcType="BIGINT"/>
        <result column="after_frozen_amount" property="afterFrozenAmount" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, version, log_key, merchant_type,mch_no, mch_name, mainstay_no, mainstay_name, plat_trx_no, amount_change_type, amount, frozen_amount, before_amount, before_frozen_amount, after_amount, after_frozen_amount, create_time, operator
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.ChangesFunds">
        INSERT INTO <include refid="table" /> (
        version,
        log_key,
        merchant_type,
        mch_no,
        mch_name,
        mainstay_no,
        mainstay_name,
        plat_trx_no,
        amount_change_type,
        amount,
        frozen_amount,
        before_amount,
        before_frozen_amount,
        after_amount,
        after_frozen_amount,
        create_time,
        operator
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{logKey,jdbcType=VARCHAR},
        #{merchantType,jdbcType=SMALLINT},
        #{mchNo,jdbcType=VARCHAR},
        #{mchName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{platTrxNo,jdbcType=VARCHAR},
        #{amountChangeType,jdbcType=SMALLINT},
        #{amount,jdbcType=BIGINT},
        #{frozenAmount,jdbcType=BIGINT},
        #{beforeAmount,jdbcType=BIGINT},
        #{beforeFrozenAmount,jdbcType=BIGINT},
        #{afterAmount,jdbcType=BIGINT},
        #{afterFrozenAmount,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{operator,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO
        <include refid="table"/>
        (
        version,
        log_key,
        merchant_type,
        mch_no,
        mch_name,
        mainstay_no,
        mainstay_name,
        plat_trx_no,
        amount_change_type,
        amount,
        frozen_amount,
        before_amount,
        before_frozen_amount,
        after_amount,
        after_frozen_amount,
        create_time,
        operator
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.version,jdbcType=SMALLINT},
            #{item.logKey,jdbcType=VARCHAR},
            #{item.merchantType,jdbcType=SMALLINT},
            #{item.mchNo,jdbcType=VARCHAR},
            #{item.mchName,jdbcType=VARCHAR},
            #{item.mainstayNo,jdbcType=VARCHAR},
            #{item.mainstayName,jdbcType=VARCHAR},
            #{item.platTrxNo,jdbcType=VARCHAR},
            #{item.amountChangeType,jdbcType=SMALLINT},
            #{item.amount,jdbcType=BIGINT},
            #{item.frozenAmount,jdbcType=BIGINT},
            #{item.beforeAmount,jdbcType=BIGINT},
            #{item.beforeFrozenAmount,jdbcType=BIGINT},
            #{item.afterAmount,jdbcType=BIGINT},
            #{item.afterFrozenAmount,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.operator,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.ChangesFunds">
        UPDATE <include refid="table" /> SET
        version = #{version,jdbcType=SMALLINT} +1 ,
        log_key = #{logKey,jdbcType=VARCHAR},
        merchant_type = #{merchantType,jdbcType=SMALLINT},
        mch_no = #{mchNo,jdbcType=VARCHAR},
        mch_name = #{mchName},
        mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
        mainstay_name = #{mainstayName},
        plat_trx_no = #{platTrxNo,jdbcType=VARCHAR},
        amount_change_type = #{amountChangeType,jdbcType=SMALLINT},
        amount = #{amount,jdbcType=BIGINT},
        frozen_amount = #{frozenAmount,jdbcType=BIGINT},
        before_amount = #{beforeAmount,jdbcType=BIGINT},
        before_frozen_amount = #{beforeFrozenAmount,jdbcType=BIGINT},
        after_amount = #{afterAmount,jdbcType=BIGINT},
        after_frozen_amount = #{afterFrozenAmount,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        operator = #{operator,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.trade.entity.ChangesFunds">
        UPDATE <include refid="table" />
        <set>
            <if test="version != null">
                version = #{version,jdbcType=SMALLINT} +1 ,
            </if>
            <if test="logKey != null">
                log_key = #{logKey,jdbcType=VARCHAR},
            </if>
            <if test="merchantType != null">
                merchant_type = #{merchantType,jdbcType=SMALLINT},
            </if>
            <if test="mchNo != null">
                mch_no = #{mchNo,jdbcType=VARCHAR},
            </if>
            <if test="mchName != null">
                mch_name = #{mchName,jdbcType=VARCHAR},
            </if>
            <if test="mainstayNo != null">
                mainstay_no = #{mainstayNo,jdbcType=VARCHAR},
            </if>
            <if test="mainstayName != null">
                mainstay_name = #{mainstayName,jdbcType=VARCHAR},
            </if>
            <if test="platTrxNo != null">
                plat_trx_no = #{platTrxNo,jdbcType=VARCHAR},
            </if>
            <if test="amountChangeType != null">
                amount_change_type = #{amountChangeType,jdbcType=SMALLINT},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=BIGINT},
            </if>
            <if test="frozenAmount != null">
                frozen_amount = #{frozenAmount,jdbcType=BIGINT},
            </if>
            <if test="beforeAmount != null">
                before_amount = #{beforeAmount,jdbcType=BIGINT},
            </if>
            <if test="beforeFrozenAmount != null">
                before_frozen_amount = #{beforeFrozenAmount,jdbcType=BIGINT},
            </if>
            <if test="afterAmount != null">
                after_amount = #{afterAmount,jdbcType=BIGINT},
            </if>
            <if test="afterFrozenAmount != null">
                after_frozen_amount = #{afterFrozenAmount,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=BIGINT}
    </update>

    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询 -->

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!--按id主键删除-->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <sql id="condition_sql">
        <if test="id != null">
            and id = #{id,jdbcType=BIGINT}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=SMALLINT}
        </if>
        <if test="logKey != null and logKey !=''">
            and log_key = #{logKey,jdbcType=VARCHAR}
        </if>
        <if test="merchantType != null and merchantType != ''">
            and merchant_type = #{merchantType,jdbcType=VARCHAR}
        </if>
        <if test="mchNo != null and mchNo !=''">
            and mch_no = #{mchNo,jdbcType=VARCHAR}
        </if>
        <if test="mchName != null and mchName !=''">
            and mch_name like concat('%', #{mchName}, '%')
        </if>
        <if test="mainstayNo != null and mainstayNo !=''">
            and mainstay_no = #{mainstayNo,jdbcType=VARCHAR}
        </if>
        <if test="mainstayName != null and mainstayName !=''">
            and mainstay_name like concat('%', #{mainstayName}, '%')
        </if>
        <if test="platTrxNo != null and platTrxNo !=''">
            and plat_trx_no = #{platTrxNo,jdbcType=VARCHAR}
        </if>
        <if test="amountChangeType != null">
            and amount_change_type = #{amountChangeType,jdbcType=SMALLINT}
        </if>
        <if test="amount != null">
            and amount = #{amount,jdbcType=BIGINT}
        </if>
        <if test="frozenAmount != null">
            and frozen_amount = #{frozenAmount,jdbcType=BIGINT}
        </if>
        <if test="beforeAmount != null">
            and before_amount = #{beforeAmount,jdbcType=BIGINT}
        </if>
        <if test="beforeFrozenAmount != null">
            and before_frozen_amount = #{beforeFrozenAmount,jdbcType=BIGINT}
        </if>
        <if test="afterAmount != null">
            and after_amount = #{afterAmount,jdbcType=BIGINT}
        </if>
        <if test="afterFrozenAmount != null">
            and after_frozen_amount = #{afterFrozenAmount,jdbcType=BIGINT}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createTimeBegin != null and createTimeBegin !=''">
            and create_time >= #{createTimeBegin}
        </if>
        <if test="createTimeEnd != null and createTimeEnd !=''">
            and create_time &lt;= #{createTimeEnd}
        </if>
        <if test="operator != null and operator !=''">
            and operator = #{operator,jdbcType=VARCHAR}
        </if>
    </sql>

</mapper>
