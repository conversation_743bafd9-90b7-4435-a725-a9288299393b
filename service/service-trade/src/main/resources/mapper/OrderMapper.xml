<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.facade.trade.entity.Order">
    <sql id="table">tbl_order</sql>
        <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.trade.entity.Order">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="VERSION" property="version" jdbcType="SMALLINT"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="DATE"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="COMPLETE_TIME" property="completeTime" jdbcType="TIMESTAMP"/>
        <result column="MCH_BATCH_NO" property="mchBatchNo" jdbcType="VARCHAR"/>
        <result column="BATCH_NAME" property="batchName" jdbcType="VARCHAR"/>
        <result column="PLAT_BATCH_NO" property="platBatchNo" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NO" property="productNo" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="JOB_ID" property="jobId" jdbcType="VARCHAR"/>
        <result column="JOB_NAME" property="jobName" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NO" property="employerNo" jdbcType="VARCHAR"/>
        <result column="EMPLOYER_NAME" property="employerName" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NO" property="mainstayNo" jdbcType="VARCHAR"/>
        <result column="MAINSTAY_NAME" property="mainstayName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_TYPE" property="channelType" jdbcType="SMALLINT"/>
        <result column="PAY_CHANNEL_NO" property="payChannelNo" jdbcType="VARCHAR"/>
        <result column="CHANNEL_NAME" property="channelName" jdbcType="VARCHAR"/>
        <result column="BATCH_STATUS" property="batchStatus" jdbcType="SMALLINT"/>
        <result column="WORK_CATEGORY_CODE" property="workCategoryCode" jdbcType="VARCHAR"/>
        <result column="WORK_CATEGORY_NAME" property="workCategoryName" jdbcType="VARCHAR"/>
        <result column="SERVICE_DESC" property="serviceDesc" jdbcType="VARCHAR"/>
        <result column="REQUEST_COUNT" property="requestCount" jdbcType="INTEGER"/>
        <result column="REQUEST_TASK_AMOUNT" property="requestTaskAmount" jdbcType="DECIMAL"/>
        <result column="REQUEST_NET_AMOUNT" property="requestNetAmount" jdbcType="DECIMAL"/>
        <result column="ACCEPTED_COUNT" property="acceptedCount" jdbcType="INTEGER"/>
        <result column="ACCEPTED_TASK_AMOUNT" property="acceptedTaskAmount" jdbcType="DECIMAL"/>
        <result column="ACCEPTED_NET_AMOUNT" property="acceptedNetAmount" jdbcType="DECIMAL"/>
        <result column="ACCEPTED_FEE" property="acceptedFee" jdbcType="DECIMAL"/>
        <result column="ACCEPTED_ORDER_AMOUNT" property="acceptedOrderAmount" jdbcType="DECIMAL"/>
        <result column="ACCEPTED_TAX_AMOUNT" property="acceptedTaxAmount" jdbcType="DECIMAL"/>
        <result column="SUCCESS_COUNT" property="successCount" jdbcType="INTEGER"/>
        <result column="SUCCESS_TASK_AMOUNT" property="successTaskAmount" jdbcType="DECIMAL"/>
        <result column="SUCCESS_NET_AMOUNT" property="successNetAmount" jdbcType="DECIMAL"/>
        <result column="SUCCESS_FEE" property="successFee" jdbcType="DECIMAL"/>
        <result column="SUCCESS_TAX_AMOUNT" property="successTaxAmount" jdbcType="DECIMAL"/>
        <result column="FAIL_COUNT" property="failCount" jdbcType="INTEGER"/>
        <result column="FAIL_TASK_AMOUNT" property="failTaskAmount" jdbcType="DECIMAL"/>
        <result column="FAIL_NET_AMOUNT" property="failNetAmount" jdbcType="DECIMAL"/>
        <result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR"/>
        <result column="ERROR_DESC" property="errorDesc" jdbcType="VARCHAR"/>
        <result column="LAUNCH_WAY" property="launchWay" jdbcType="SMALLINT"/>
        <result column="CALLBACK_URL" property="callbackUrl" jdbcType="VARCHAR"/>
        <result column="JSON_STR" property="jsonStr" jdbcType="OTHER"/>
    </resultMap>

        <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, VERSION, CREATE_DATE, CREATE_TIME, UPDATE_TIME, CONFIRM_TIME, COMPLETE_TIME, MCH_BATCH_NO, BATCH_NAME, PLAT_BATCH_NO, PRODUCT_NO,PRODUCT_NAME,JOB_ID,JOB_NAME,EMPLOYER_NO, EMPLOYER_NAME, MAINSTAY_NO, MAINSTAY_NAME, CHANNEL_TYPE, PAY_CHANNEL_NO, CHANNEL_NAME, BATCH_STATUS, WORK_CATEGORY_CODE, WORK_CATEGORY_NAME, SERVICE_DESC, REQUEST_COUNT,REQUEST_TASK_AMOUNT, REQUEST_NET_AMOUNT, ACCEPTED_COUNT,ACCEPTED_TASK_AMOUNT, ACCEPTED_NET_AMOUNT, ACCEPTED_FEE, ACCEPTED_ORDER_AMOUNT,ACCEPTED_TAX_AMOUNT, SUCCESS_COUNT, SUCCESS_TASK_AMOUNT,SUCCESS_NET_AMOUNT, SUCCESS_FEE,SUCCESS_TAX_AMOUNT, FAIL_COUNT,FAIL_TASK_AMOUNT, FAIL_NET_AMOUNT, ERROR_CODE, ERROR_DESC, LAUNCH_WAY, CALLBACK_URL, JSON_STR
    </sql>


    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.trade.entity.Order">
        INSERT INTO <include refid="table" /> (
            VERSION,
            CREATE_DATE,
            CREATE_TIME,
            UPDATE_TIME,
            CONFIRM_TIME,
            COMPLETE_TIME,
            MCH_BATCH_NO,
            BATCH_NAME,
            PLAT_BATCH_NO,
            PRODUCT_NO,
            PRODUCT_NAME,
            JOB_ID,
            JOB_NAME,
            EMPLOYER_NO,
            EMPLOYER_NAME,
            MAINSTAY_NO,
            MAINSTAY_NAME,
            CHANNEL_TYPE,
            PAY_CHANNEL_NO,
            CHANNEL_NAME,
            BATCH_STATUS,
            WORK_CATEGORY_CODE,
            WORK_CATEGORY_NAME,
            SERVICE_DESC,
            REQUEST_COUNT,
            REQUEST_TASK_AMOUNT,
            REQUEST_NET_AMOUNT,
            ACCEPTED_COUNT,
            ACCEPTED_TASK_AMOUNT,
            ACCEPTED_NET_AMOUNT,
            ACCEPTED_FEE,
            ACCEPTED_ORDER_AMOUNT,
            ACCEPTED_TAX_AMOUNT,
            SUCCESS_COUNT,
            SUCCESS_TASK_AMOUNT,
            SUCCESS_NET_AMOUNT,
            SUCCESS_FEE,
            SUCCESS_TAX_AMOUNT,
            FAIL_COUNT,
            FAIL_TASK_AMOUNT,
            FAIL_NET_AMOUNT,
            ERROR_CODE,
            ERROR_DESC,
            LAUNCH_WAY,
            CALLBACK_URL,
            JSON_STR
        ) VALUES (
        #{version,jdbcType=SMALLINT},
        #{createDate,jdbcType=DATE},
        #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP},
        #{confirmTime,jdbcType=TIMESTAMP},
        #{completeTime,jdbcType=TIMESTAMP},
        #{mchBatchNo,jdbcType=VARCHAR},
        #{batchName,jdbcType=VARCHAR},
        #{platBatchNo,jdbcType=VARCHAR},
        #{productNo,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{jobId,jdbcType=VARCHAR},
        #{jobName,jdbcType=VARCHAR},
        #{employerNo,jdbcType=VARCHAR},
        #{employerName,jdbcType=VARCHAR},
        #{mainstayNo,jdbcType=VARCHAR},
        #{mainstayName,jdbcType=VARCHAR},
        #{channelType,jdbcType=SMALLINT},
        #{payChannelNo,jdbcType=VARCHAR},
        #{channelName,jdbcType=VARCHAR},
        #{batchStatus,jdbcType=SMALLINT},
        #{workCategoryCode,jdbcType=VARCHAR},
        #{workCategoryName,jdbcType=VARCHAR},
        #{serviceDesc,jdbcType=VARCHAR},
        #{requestCount,jdbcType=INTEGER},
        #{requestTaskAmount,jdbcType=DECIMAL},
        #{requestNetAmount,jdbcType=DECIMAL},
        #{acceptedCount,jdbcType=INTEGER},
        #{acceptedTaskAmount,jdbcType=DECIMAL},
        #{acceptedNetAmount,jdbcType=DECIMAL},
        #{acceptedFee,jdbcType=DECIMAL},
        #{acceptedOrderAmount,jdbcType=DECIMAL},
        #{acceptedTaxAmount,jdbcType=DECIMAL},
        #{successCount,jdbcType=INTEGER},
        #{successTaskAmount,jdbcType=DECIMAL},
        #{successNetAmount,jdbcType=DECIMAL},
        #{successFee,jdbcType=DECIMAL},
        #{successTaxAmount,jdbcType=DECIMAL},
        #{failCount,jdbcType=INTEGER},
        #{failTaskAmount,jdbcType=DECIMAL},
        #{failNetAmount,jdbcType=DECIMAL},
        #{errorCode,jdbcType=VARCHAR},
        #{errorDesc,jdbcType=VARCHAR},
        #{launchWay,jdbcType=SMALLINT},
        #{callbackUrl,jdbcType=VARCHAR},
        #{jsonStr,jdbcType=OTHER}
        )
    </insert>

    <!--更新-->
    <update id="update" parameterType="com.zhixianghui.facade.trade.entity.Order">
        UPDATE <include refid="table" /> SET
                VERSION = #{version,jdbcType=SMALLINT} +1,
                CREATE_DATE = #{createDate,jdbcType=DATE},
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
                UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
                CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
                COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP},
                MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR},
                BATCH_NAME = #{batchName,jdbcType=VARCHAR},
                PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR},
                PRODUCT_NO = #{productNo,jdbcType=VARCHAR},
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
                JOB_ID = #{jobId,jdbcType=VARCHAR},
                JOB_NAME = #{jobName,jdbcType=VARCHAR},
                EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR},
                EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR},
                MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR},
                MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR},
                CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT},
                PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR},
                CHANNEL_NAME = #{channelName,jdbcType=VARCHAR},
                BATCH_STATUS = #{batchStatus,jdbcType=SMALLINT},
                WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR},
                WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR},
                SERVICE_DESC = #{serviceDesc,jdbcType=VARCHAR},
                REQUEST_COUNT = #{requestCount,jdbcType=INTEGER},
                REQUEST_TASK_AMOUNT = #{requestTaskAmount,jdbcType=DECIMAL},
                REQUEST_NET_AMOUNT = #{requestNetAmount,jdbcType=DECIMAL},
                ACCEPTED_COUNT = #{acceptedCount,jdbcType=INTEGER},
                ACCEPTED_TASK_AMOUNT = #{acceptedTaskAmount,jdbcType=DECIMAL},
                ACCEPTED_NET_AMOUNT = #{acceptedNetAmount,jdbcType=DECIMAL},
                ACCEPTED_FEE = #{acceptedFee,jdbcType=DECIMAL},
                ACCEPTED_ORDER_AMOUNT = #{acceptedOrderAmount,jdbcType=DECIMAL},
                ACCEPTED_TAX_AMOUNT = #{acceptedTaxAmount,jdbcType=DECIMAL},
                SUCCESS_COUNT = #{successCount,jdbcType=INTEGER},
                SUCCESS_TASK_AMOUNT = #{successTaskAmount,jdbcType=DECIMAL},
                SUCCESS_NET_AMOUNT = #{successNetAmount,jdbcType=DECIMAL},
                SUCCESS_FEE = #{successFee,jdbcType=DECIMAL},
                SUCCESS_TAX_AMOUNT = #{successTaxAmount,jdbcType=DECIMAL},
                FAIL_COUNT = #{failCount,jdbcType=INTEGER},
                FAIL_TASK_AMOUNT = #{failTaskAmount,jdbcType=DECIMAL},
                FAIL_NET_AMOUNT = #{failNetAmount,jdbcType=DECIMAL},
                ERROR_CODE = #{errorCode,jdbcType=VARCHAR},
                ERROR_DESC = #{errorDesc,jdbcType=VARCHAR},
                LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT},
                CALLBACK_URL = #{callbackUrl,jdbcType=VARCHAR},
                JSON_STR = #{jsonStr,jdbcType=OTHER}
        WHERE ID = #{id,jdbcType=BIGINT} and PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR} and VERSION = #{version,jdbcType=BIGINT} and CREATE_DATE = #{createDate,jdbcType=DATE}
    </update>


    <!-- 不分页查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 分页查询时计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(1) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据id查询 -->
    <select id="sumWaitInvoiceAmount" resultType="bigDecimal">
        SELECT SUM(COALESCE(SUCCESS_NET_AMOUNT,0)+COALESCE(SUCCESS_FEE,0)) as total
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="sumWaitCkhInvoiceAmount" resultType="bigDecimal">
        SELECT SUM(COALESCE(SUCCESS_FEE,0)) as total
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="sumWaitCepGrantInvoiceAmount" resultType="bigDecimal">
        SELECT SUM(COALESCE(SUCCESS_NET_AMOUNT,0)) as total
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <select id="sumWaitCepServiceFeeInvoiceAmount" resultType="bigDecimal">
        SELECT SUM(COALESCE(SUCCESS_FEE,0)) as total
        FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <update id="updateById" parameterType="com.zhixianghui.facade.trade.dto.OrderDeleteDTO">
        update <include refid="table" />
        set is_delete=#{isDelete},UPDATE_TIME=#{updateTime}
        <where>
        PLAT_BATCH_NO=#{platBatchNo}
        <if test="beginDate != null and endDate != null" >
            and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        </if>
        </where>
    </update>


    <!-- 账单查询 -->
    <select id="selectFeeOrder" resultType="com.zhixianghui.facade.trade.entity.FeeOrderBatch">
        SELECT
        EMPLOYER_NO,
        EMPLOYER_NAME,
        MAINSTAY_NO,
        MAINSTAY_NAME,
        PRODUCT_NO,
        PRODUCT_NAME,
        PAY_CHANNEL_NO,
        CHANNEL_NAME,
        CHANNEL_TYPE,
        COUNT(*) order_batch_count,
        sum(REQUEST_COUNT) order_item_count,
        sum( SUCCESS_COUNT ) success_count,
        sum(FAIL_COUNT) fail_count,
        sum(SUCCESS_TASK_AMOUNT) task_amount,
        sum(SUCCESS_NET_AMOUNT) order_net_amount,
        sum(SUCCESS_FEE) fee_amount,
        sum(SUCCESS_TAX_AMOUNT) tax_amount
        FROM
        `tbl_order`
        <where>
            <include refid="condition_sql" />
        </where>
        GROUP BY
        EMPLOYER_NO,
        EMPLOYER_NAME,
        MAINSTAY_NO,
        MAINSTAY_NAME,
        PRODUCT_NO,
        PRODUCT_NAME,
        PAY_CHANNEL_NO,
        CHANNEL_NAME,
        CHANNEL_TYPE
    </select>

    <!-- 查询订单批次号 -->
    <select id="selectOrderNo" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT PLAT_BATCH_NO
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        group by PLAT_BATCH_NO
    </select>

    <sql id="condition_sql">
            <if test="id != null">
                and ID = #{id,jdbcType=BIGINT}
            </if>
            <if test="version != null">
                and VERSION = #{version,jdbcType=SMALLINT}
            </if>
            <if test="createDate != null">
                and CREATE_DATE = #{createDate,jdbcType=DATE}
            </if>
            <if test="createTime != null">
                and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="confirmTime != null">
                and CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP}
            </if>
            <if test="completeTime != null">
                and COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP}
            </if>
            <if test="mchBatchNo != null and mchBatchNo !=''">
                and MCH_BATCH_NO = #{mchBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="batchName != null and batchName !=''">
                and BATCH_NAME = #{batchName,jdbcType=VARCHAR}
            </if>
            <if test="platBatchNo != null and platBatchNo !=''">
                and PLAT_BATCH_NO = #{platBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="productNo != null and productNo != ''">
                and PRODUCT_NO = #{productNo,jdbcType=VARCHAR}
            </if>
            <if test="productName != null and productName != ''">
                and PRODUCT_NAME = #{productName,jdbcType=VARCHAR}
            </if>
            <if test="jobId != null and jobId != ''">
                and JOB_ID = #{jobId,jdbcType=VARCHAR}
            </if>
            <if test="jobName != null and jobName != ''">
                and JOB_NAME = #{jobName,jdbcType=VARCHAR}
            </if>
            <if test="employerNo != null and employerNo !=''">
                and EMPLOYER_NO = #{employerNo,jdbcType=VARCHAR}
            </if>
            <if test="employerName != null and employerName !=''">
                and EMPLOYER_NAME = #{employerName,jdbcType=VARCHAR}
            </if>
            <if test="mainstayNo != null and mainstayNo !=''">
                and MAINSTAY_NO = #{mainstayNo,jdbcType=VARCHAR}
            </if>
            <if test="mainstayName != null and mainstayName !=''">
                and MAINSTAY_NAME = #{mainstayName,jdbcType=VARCHAR}
            </if>
            <if test="channelType != null">
                and CHANNEL_TYPE = #{channelType,jdbcType=SMALLINT}
            </if>
            <if test="payChannelNo != null and payChannelNo !=''">
                and PAY_CHANNEL_NO = #{payChannelNo,jdbcType=VARCHAR}
            </if>
            <if test="channelName != null and channelName !=''">
                and CHANNEL_NAME = #{channelName,jdbcType=VARCHAR}
            </if>
            <if test="batchStatus != null">
                and BATCH_STATUS = #{batchStatus,jdbcType=SMALLINT}
            </if>
            <if test="workCategoryCode != null and workCategoryCode != ''">
                and WORK_CATEGORY_CODE = #{workCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test="workCategoryName != null and workCategoryName !=''">
                and WORK_CATEGORY_NAME = #{workCategoryName,jdbcType=VARCHAR}
            </if>
            <if test="serviceDesc != null and serviceDesc !=''">
                and SERVICE_DESC = #{serviceDesc,jdbcType=VARCHAR}
            </if>
            <if test="requestCount != null">
                and REQUEST_COUNT = #{requestCount,jdbcType=INTEGER}
            </if>
            <if test="requestNetAmount != null">
                and REQUEST_NET_AMOUNT = #{requestNetAmount,jdbcType=DECIMAL}
            </if>
            <if test="acceptedCount != null">
                and ACCEPTED_COUNT = #{acceptedCount,jdbcType=INTEGER}
            </if>
            <if test="acceptedNetAmount != null">
                and ACCEPTED_NET_AMOUNT = #{acceptedNetAmount,jdbcType=DECIMAL}
            </if>
            <if test="acceptedFee != null">
                and ACCEPTED_FEE = #{acceptedFee,jdbcType=DECIMAL}
            </if>
            <if test="acceptedOrderAmount != null">
                and ACCEPTED_ORDER_AMOUNT = #{acceptedOrderAmount,jdbcType=DECIMAL}
            </if>
            <if test="successCount != null">
                and SUCCESS_COUNT = #{successCount,jdbcType=INTEGER}
            </if>
            <if test="successNetAmount != null">
                and SUCCESS_NET_AMOUNT = #{successNetAmount,jdbcType=DECIMAL}
            </if>
            <if test="successFee != null">
                and SUCCESS_FEE = #{successFee,jdbcType=DECIMAL}
            </if>
            <if test="failCount != null">
                and FAIL_COUNT = #{failCount,jdbcType=INTEGER}
            </if>
            <if test="failNetAmount != null">
                and FAIL_NET_AMOUNT = #{failNetAmount,jdbcType=DECIMAL}
            </if>
            <if test="errorCode != null and errorCode !=''">
                and ERROR_CODE = #{errorCode,jdbcType=VARCHAR}
            </if>
            <if test="errorDesc != null and errorDesc !=''">
                and ERROR_DESC = #{errorDesc,jdbcType=VARCHAR}
            </if>
            <if test="launchWay != null">
                and LAUNCH_WAY = #{launchWay,jdbcType=SMALLINT}
            </if>
            <if test="callbackUrl != null and callbackUrl !=''">
                and CALLBACK_URL = #{callbackUrl,jdbcType=VARCHAR}
            </if>
            <if test="jsonStr != null and jsonStr !=''">
                and JSON_STR = #{jsonStr,jdbcType=OTHER}
            </if>


            <!--  自定义  -->
            <if test="maxId != null">
                and ID <![CDATA[ < ]]> #{maxId,jdbcType=BIGINT}
            </if>
            <if test="batchNameLike != null and batchNameLike !='' ">
                AND BATCH_NAME LIKE CONCAT('%', CONCAT(#{batchNameLike}, '%'))
            </if>
            <if test="employerNameLike != null and employerNameLike !='' ">
                AND EMPLOYER_NAME LIKE CONCAT('%', CONCAT(#{employerNameLike}, '%'))
            </if>
            <if test="createBeginDate != null and createEndDate != null" >
                and CREATE_TIME between #{createBeginDate,jdbcType=TIMESTAMP} and #{createEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="completeBeginDate != null and completeEndDate != null" >
                and COMPLETE_TIME between #{completeBeginDate,jdbcType=TIMESTAMP} and #{completeEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="createDateList != null and createDateList.size() > 0">
                and CREATE_DATE IN
                <foreach collection="createDateList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=DATE}</foreach>
            </if>

            <if test="productNos != null and productNos.size() > 0">
                and PRODUCT_NO IN
                <foreach collection="productNos" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=VARCHAR}</foreach>
            </if>
            <!--  分表字段区间  -->
            <if test="beginDate != null and endDate != null" >
                and CREATE_DATE between #{beginDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
            <if test="isDelete != null " >
                and (is_delete = #{isDelete} or is_delete is null)
            </if>
            <if test="ignoreZeroAmt !=null and ignoreZeroAmt !='' ">
                and SUCCESS_NET_AMOUNT <![CDATA[ > ]]> 0
            </if>
    </sql>

    <select id="getNotCompleteOrder" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
            <include refid="Base_Column_List" />
        from
            <include refid="table" />
        where EMPLOYER_NO = #{employerNo} and MAINSTAY_NO = #{mainstayNo} and BATCH_STATUS in
        <foreach collection="batchStatusIn" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 1
    </select>

</mapper>
