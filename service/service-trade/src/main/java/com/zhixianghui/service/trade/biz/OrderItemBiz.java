package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.trade.bo.OrderItemCountBo;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.bo.OrderItemTrxIdBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.MchPlatTrxNo;
import com.zhixianghui.facade.trade.entity.MerchantStat;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.facade.trade.vo.AuthInfoVo;
import com.zhixianghui.service.trade.dao.MchPlatTrxNoDao;
import com.zhixianghui.service.trade.dao.OrderItemDao;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单明细表Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OrderItemBiz {

    private final OrderItemDao orderItemDao;
    private final TradeHelperBiz tradeHelperBiz;
    private final MchPlatTrxNoDao mchPlatTrxNoDao;
    private final RedisClient redisClient;
    private final UserInfoBiz userInfoBiz;
    private final SignRecordBiz signRecordBiz;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private RobotFacade robotFacade;

    @Value("${hangup.NotifyEmail.to}")
    private String hangupNotifyEmail;
    @Value("${hangup.NotifyEmail.subject}")
    private String subject;
    @Value("${hangup.NotifyEmail.cc}")
    private String cc;

    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<OrderItem> itemList) {
        if (ObjectUtils.isEmpty(itemList)) {
            return;
        }
        List<MchPlatTrxNo> mchPlatTrxNoList = Lists.newArrayList();
        Date now = new Date();
        itemList.forEach(
                item -> {
                    //映射表
                    MchPlatTrxNo mchPlatTrxNo = new MchPlatTrxNo();
                    mchPlatTrxNo.setCreateTime(now);
                    mchPlatTrxNo.setMchOrderNo(item.getMchOrderNo());
                    mchPlatTrxNo.setEmployerNo(item.getEmployerNo());
                    mchPlatTrxNo.setPlatTrxNo(item.getPlatTrxNo());
                    mchPlatTrxNoList.add(mchPlatTrxNo);
                }
        );
        mchPlatTrxNoDao.insert(mchPlatTrxNoList);
        orderItemDao.insert(itemList);
    }

    public PageResult<List<OrderItemTrxIdBo>> listTrxNoIdPage(Map<String, Object> paramMap, PageParam pageParam) {
        return orderItemDao.listTrxNoIdPage(paramMap, pageParam);
    }

    public OrderItem getByPlatTrxNo(String platTrxNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platTrxNo", platTrxNo);
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        return orderItemDao.getOne(paramMap);
    }

    public void update(OrderItem item) {
        orderItemDao.update(item);
    }

    public OrderItemCountBo getCountBoByPlatBatchNo(String platBatchNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", platBatchNo);
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        return orderItemDao.getCountBoByPlatBatchNo(paramMap);
    }

    public PageResult<List<OrderItem>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return PageResult.newInstance(pageParam, new ArrayList<>());
        }
        tradeHelperBiz.replacePrivacy(paramMap);
        return orderItemDao.listPage(paramMap, pageParam);
    }

    public void update2Hangup(OrderItem orderItem, RiskControlResult riskControlResult) {
        orderItem.setUpdateTime(new Date());
        orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_HANG.getValue());

        if (StringUtils.isNotEmpty(riskControlResult.getErrMsg())) {
            orderItem.setErrorCode(String.valueOf(ApiExceptions.API_TRADE_RISK_HANG.getApiErrorCode()));
            orderItem.setErrorDesc(riskControlResult.getErrMsg());
        }
        JSONObject json = new JSONObject();
        json.put("risk_type",riskControlResult.getControlType());
        orderItem.setJsonStr(json.toJSONString());
        orderItemDao.update(orderItem);
    }

    public void update2Reject(OrderItem orderItem, RiskControlResult riskControlResult) {
        Date now = new Date();
        orderItem.setUpdateTime(now);
        orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
        orderItem.setCompleteTime(now);
        if (StringUtils.isNotEmpty(riskControlResult.getErrMsg())) {
            orderItem.setErrorCode(String.valueOf(ApiExceptions.API_TRADE_RISK_REJECT.getApiErrorCode()));
            orderItem.setErrorDesc(riskControlResult.getErrMsg());
        }
        JSONObject json = new JSONObject();
        json.put("risk_type",riskControlResult.getControlType());
        orderItem.setJsonStr(json.toJSONString());
        orderItemDao.update(orderItem);
    }

    public OrderItem getByEmployerNoAndMchOrderNoApi(String employerNo, String mchOrderNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("mchOrderNo", mchOrderNo);
        paramMap.put("employerNo", employerNo);
        //api 添加时间跨度 最小日期到当前，以免出现表不存在错误
        paramMap.put("beginDate", TrxNoDateUtil.MIN_DATE);
        paramMap.put("endDate", new Date());
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return null;
        }
        return orderItemDao.getOne(paramMap);
    }

    public List<OrderItem> listBy(String platBatchNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", platBatchNo);
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return new ArrayList<>();
        }
        return orderItemDao.listBy(paramMap);
    }

    public List<OrderItem> listByParam(Map<String,Object> paramMap) {
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return new ArrayList<>();
        }
        return orderItemDao.listBy(paramMap);
    }

    public OrderItem getEarlestOrderItem(String employerNo) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("employerNo", employerNo);
        queryParam.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        return orderItemDao.getOne("getEarlestOrderItem", queryParam);
    }

    public Long countOrderItem(Map<String, Object> paramMap) {
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return 0L;
        }
        tradeHelperBiz.replacePrivacy(paramMap);


        log.info("查询订单详情记录数入参:{}", JSON.toJSONString(paramMap));
        return orderItemDao.countBy(paramMap);
    }

    public OrderItemSumBo sumOrderItem(Map<String, Object> paramMap) {
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return new OrderItemSumBo();
        }
        tradeHelperBiz.replacePrivacy(paramMap);
        return orderItemDao.sumOrderItem(paramMap);
    }

    public List<MerchantStat> merchantStat(Map<String, Object> param) {
        return orderItemDao.listBy("merchantStat", param);

    }

    public List<FreelanceStat> freelanceStat(Map<String, Object> param) {
        return orderItemDao.listBy("freelanceStat", param);
    }

    public Long countOrder(Map<String, Object> param) {
        return orderItemDao.countBy("countOrder", param);
    }

    public Map<String, BigDecimal> mapAmountWithMch(Map<String, Object> paramMap) {
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        TrxNoDateUtil.putCreateDateByMapDate(paramMap);
        List<OrderItem> orderItemList = orderItemDao.listBy("mapAmountWithMch", paramMap);
        return orderItemList.stream().collect(Collectors.toMap(OrderItem::getEmployerNo, OrderItem::getOrderItemNetAmount));
        //return orderItemDao.listBy("mapAmountWithMch",paramMap).stream().collect(Collectors.toMap(OrderItem::getEmployerNo,OrderItem::getOrderItemNetAmount));
    }

    public BigDecimal sumOrderItemWaitInvoiceAmount(Map<String, Object> paramMap) {
//        tradeHelperBiz.putOrderCreateDateToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return BigDecimal.ZERO;
        }
        return orderItemDao.sumOrderItemWaitInvoiceAmount(paramMap);
    }

    public BigDecimal sumOrderItemWaitGrantInvoiceAmount(Map<String, Object> paramMap) {
//        tradeHelperBiz.putOrderCreateDateToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return BigDecimal.ZERO;
        }
        return orderItemDao.sumOrderItemWaitGrantInvoiceAmount(paramMap);
    }

    public BigDecimal sumOrderItemWaitServiceFeeInvoiceAmount(Map<String, Object> paramMap) {
//        tradeHelperBiz.putOrderCreateDateToMap(paramMap);
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return BigDecimal.ZERO;
        }
        return orderItemDao.sumOrderItemWaitServiceFeeInvoiceAmount(paramMap);
    }

    public void authInit(String platBatchNo) {
        log.info("[{}]加载认证信息",platBatchNo);
        Map<String, Object> map = new HashMap<>();
        map.put("platBatchNo", platBatchNo);
//        tradeHelperBiz.putOrderItemCreateDateToMap(map);
        List<OrderItem> orderItems = orderItemDao.listBy(map);
        orderItems.forEach(this::getAuthInfo);
    }

    private String getHKeyString(String employerNo,String mainstayNo){
        return String.join("-",employerNo,mainstayNo);
    }

    public AuthInfoVo getAuthInfo(OrderItem orderItem) {
        AuthInfoVo authInfoVo = new AuthInfoVo();
        if (userInfoBiz.isVerified(orderItem.getReceiveIdCardNoMd5())) {
            authInfoVo.setAuth(AuthStatusEnum.SUCCESS.getValue());
        }else {
            authInfoVo.setAuth(AuthStatusEnum.UN_AUTH.getValue());
        }

        if (signRecordBiz.isSigned(orderItem.getMainstayNo(), orderItem.getEmployerNo(), orderItem.getReceiveIdCardNoMd5())) {
            authInfoVo.setSign(SignStatusEnum.SIGN_SUCCESS.getValue());
        }else {
            authInfoVo.setSign(SignStatusEnum.UN_SIGN.getValue());
        }
        return authInfoVo;
    }

    public List<OrderItem> queryByPlatBatchNo(String platBatchNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", platBatchNo);
        return orderItemDao.listBy(paramMap);
    }

    public OrderItem getByMchOrderNo(String mchOrderNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("mchOrderNo", mchOrderNo);
        return orderItemDao.getOne(paramMap);
    }
    public OrderItem getByMchOrderNo(String employerNo, String mchOrderNo,int orderItemStatus) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", employerNo);
        paramMap.put("mchOrderNo", mchOrderNo);
        paramMap.put("orderItemStatus", orderItemStatus);
        return orderItemDao.getOne(paramMap);
    }

    public List<Map<String, Object>> sumCmbOrderAmt(Map<String, Object> paramMap) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        return orderItemDao.sumCmbOrderAmt(paramMap);
    }

    public Map<String, BigDecimal> queryUserMonthGrantData(Map<String, Object> paramMap) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        return orderItemDao.queryUserMonthGrantData(paramMap);
    }
    public OrderItem getGrantUserByIdCard(Map<String, Object> paramMap) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        return orderItemDao.getGrantUserByIdCard(paramMap);
    }

    public List<Map<String,String>> listGrantOrderBatches(Map<String, Object> param) {
       return orderItemDao.listGrantOrderBatches(param);
    }
}
