package com.zhixianghui.service.trade.listener;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.trade.dto.RefundBalanceDTO;
import com.zhixianghui.facade.trade.entity.CmbIncomeRecord;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.CmbAccountChangeEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.facade.trade.vo.CmbAccountChangeVo;
import com.zhixianghui.service.trade.biz.*;
import com.zhixianghui.service.trade.dao.mapper.CmbIncomeRecordMapper;
import com.zhixianghui.service.trade.factory.TradeFactory;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName CmbNotifyListener
 * @Description TODO
 * @Date 2023/2/10 11:35
 */
@Slf4j
@Component
public class CmbNotifyListener {

    @Reference
    private RobotFacade robotFacade;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_NOTIFY,
            selectorExpression = MessageMsgDest.TAG_CMB_MAINTENANCE,consumeThreadMax = 3,consumerGroup = "CmbMaintenanceConsumer")
    public class CmbMaintenaceNotifyListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            String title = "#### 招行维护通知 \\n";
            MarkDownMsg markDownMsg = new MarkDownMsg();
            markDownMsg.setUnikey(UUIDUitl.generateString(8));
            markDownMsg.setRobotType(RobotTypeEnum.WX_INCOME_ROBOT.getType());
            markDownMsg.setContent(title + jsonParam);
            robotFacade.pushMarkDownAsync(markDownMsg);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_NOTIFY,
            selectorExpression = MessageMsgDest.TAG_CMB_REMIT_RESULT,consumeThreadMax = 3,consumerGroup = "CmbRemitNotifyConsumer")
    public class CmbRemitNotifyListener extends BaseRocketMQListener<String>{

        @Autowired
        private RecordItemBiz recordItemBiz;
        @Autowired
        private TradeFactory tradeFactory;
        @Reference
        private CmbFacade cmbFacade;
        @Autowired
        private OrderBiz orderBiz;
        @Autowired
        private CmbTradeNotifyBiz cmbTradeNotifyBiz;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            final cn.hutool.json.JSONObject msgdat = JSONUtil.parseObj(jsonParam).getJSONObject("msgdat");
            final cn.hutool.json.JSONObject agcInfo = msgdat.getJSONObject("agcInfo");

            String platBatchNo = agcInfo.getStr("yurref").replace("ZHIXIANG", "");
            String accountNo = agcInfo.getStr("accnbr");
            String reqnbr = agcInfo.getStr("reqnbr");//流程实例号

            Order order = orderBiz.getByPlatBatchNo(platBatchNo);
            if (order == null || order.getBatchStatus() == OrderStatusEnum.FINISH_GRANT.getValue() || order.getBatchStatus() == OrderStatusEnum.CLOSED_GRANT.getValue()) {
                log.info("[{}]订单状态已经转为终态，不予处理", platBatchNo);
                return;
            }

            cmbTradeNotifyBiz.handleCmbRemitNotify(accountNo, reqnbr, platBatchNo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_NOTIFY,selectorExpression = MessageMsgDest.TAG_CMB_ACCOUNT_CHANGE,
            consumeThreadMax = 20,consumerGroup = "CmbAccountChangeConsumer")
    public class CmbAccountChangeListener extends BaseRocketMQListener<String>{

        @Autowired
        private CmbIncomeRecordBiz cmbIncomeRecordBiz;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            JSONObject jsonObject = JSON.parseObject(jsonParam);
            String type = jsonObject.getString("msgtyp");
            if (!StringUtils.equals(type, CmbAccountChangeEnum.INCOME.getValue())
                    || StringUtils.startsWith(jsonObject.getJSONObject("msgdat").getString("yurref"),"ZHIXIANGWC")
            ||StringUtils.startsWith(jsonObject.getJSONObject("msgdat").getString("yurref"),"ZHIXIANGG")){
                return;
            }

            cmbIncomeRecordBiz.cmbIncomeRecord( jsonObject.getString("msgdat"));
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_RECHARGE_BILL,selectorExpression = MessageMsgDest.TAG_CMB_RECHARGE_BILL,
            consumeThreadMax = 20,consumerGroup = "CmbRechargeBillConsumer")
    public class CmbRechargeBillListener extends BaseRocketMQListener<String>{

        @Autowired
        private RechargeRecordBiz rechargeRecordBiz;

        @Autowired
        private CmbIncomeRecordMapper cmbIncomeRecordMapper;

        @Reference
        private CmbFacade cmbFacade;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String refnbr) {
            CmbIncomeRecord cmbIncomeRecord = cmbIncomeRecordMapper.selectOne(Wrappers.<CmbIncomeRecord>lambdaQuery()
                    .eq(CmbIncomeRecord::getChannelTrxNo, refnbr));
            if(ObjectUtil.isEmpty(cmbIncomeRecord)) {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg(StrUtil.format("招行来账记录不存在，银行单号:{}", refnbr));
            }
            //来账交易流水
            RechargeRecord rechargeRecord = rechargeRecordBiz.getOne(Wrappers.<RechargeRecord>lambdaQuery().eq(RechargeRecord::getChannelOrderId, refnbr));
            if (ObjectUtil.isEmpty(rechargeRecord)) {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg(StrUtil.format("充值记录不存在，银行单号:{}", refnbr));
            }
            final String tradeDate = cn.hutool.core.date.DateUtil.format(cmbIncomeRecord.getSuccessTime(), "yyyy-MM-dd");
            final String filePath = cmbFacade.getAccountReconFileUrlSync(cmbIncomeRecord.getPayeeAccountNo(), tradeDate, cmbIncomeRecord.getChannelTrxNo());
            rechargeRecord.setReceiptUrl(filePath);
            rechargeRecord.setUpdateTime(new Date());
            rechargeRecordBiz.updateById(rechargeRecord);
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_NOTIFY,
            selectorExpression = MessageMsgDest.TAG_CMB_PAY2B_RESULT,consumeThreadMax = 1,consumerGroup = "CmbPay2bNotifyConsumer")
    public class CmbPay2bNotifyListener extends BaseRocketMQListener<String>{

        @Autowired
        private WithdrawRecordBiz withdrawRecordBiz;
        @Reference
        private CmbFacade cmbFacade;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            final cn.hutool.json.JSONObject msgdat = JSONUtil.parseObj(jsonParam).getJSONObject("msgdat");
            final cn.hutool.json.JSONObject trsInfo = msgdat.getJSONObject("trsInfo");
            String bankOrderNo = trsInfo.getStr("yurRef");
            String bankTrxNo = trsInfo.getStr("reqNbr");
            String remitNo = trsInfo.getStr("yurRef").replace("ZHIXIANG", "");
            String accountNo = trsInfo.getStr("dbtAcc");

            if (StringUtils.equals(trsInfo.getStr("rtnFlg"), "S")) {
                withdrawRecordBiz.handleNotify(remitNo, bankTrxNo, bankOrderNo, SuccessFailEnum.SUCCESS, null);
            } else {
                JSONObject queryJsonResult;
                String failReason;
                try {
                    queryJsonResult = cmbFacade.pay2bQuery(accountNo, remitNo);
                    failReason = queryJsonResult.getJSONArray("bb1payqrz1").getJSONObject(0).getString("rtnNar");
                } catch (BizException e) {
                    log.info("招行通道反查失败：{}", remitNo, e);
                    if (e.getErrMsg().contains("没有符合查询条件的数据")) {
                        log.error("支付转账查询-招行通道查询不到符合查询条件的数据,remitNo:{}", remitNo);
                        throw e;
                    }
                    failReason = StrUtil.format("招行通道处理失败");
                }
                withdrawRecordBiz.handleNotify(remitNo, bankTrxNo, bankOrderNo, SuccessFailEnum.FAIL, failReason);
            }
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_CMB_ASYNC,
            selectorExpression = MessageMsgDest.TAG_TRADE_CMB_CHECK_GRANT,consumeThreadMax = 1,consumerGroup = "CmbRemitCheckConsumer")
    public class CmbRemitCheckListener extends BaseRocketMQListener<String>{

        @Autowired
        private OrderBiz orderBiz;
        @Autowired
        private CmbTradeNotifyBiz cmbTradeNotifyBiz;
        @Reference
        private EmployerAccountInfoFacade accountInfoFacade;

        @Override
        public void validateJsonParam(String msgParam) {

        }

        @Override
        public void consumeMessage(String msgParam) {
            cn.hutool.json.JSONObject checkParam = JSONUtil.parseObj(msgParam);
            String platBatchNo = checkParam.getStr("platBatchNo");
            String reqnbr = checkParam.getStr("reqnbr");
            String accountNo = checkParam.getStr("accountNo");

            if (StringUtils.isAnyBlank(reqnbr, accountNo, platBatchNo)) {
                log.info("招行批量发放结果查询-参数错误,reqnbr, accountNo, platBatchNo不能为空");
                return;
            }

            Order order = orderBiz.getByPlatBatchNo(platBatchNo);
            if (order.getBatchStatus().intValue() == OrderStatusEnum.GRANTING.getValue()) {
                cmbTradeNotifyBiz.handleCmbRemitNotify(accountNo, reqnbr, platBatchNo);
            }

        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_REFUND, selectorExpression = MessageMsgDest.TAG_CMB_ACCOUNT_REFUND, consumeThreadMax = 1, consumerGroup = "cmbAccountRefund")
    public class CmbAccountRefundListener extends BaseRocketMQListener<String> {

        @Autowired
        private CmbTradeNotifyBiz cmbTradeNotifyBiz;

        @Override
        public void validateJsonParam(String jsonParam) {
            if (jsonParam == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("消息内容不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            RecordItem recordItem = JSONObject.parseObject(jsonParam, RecordItem.class);
            cmbTradeNotifyBiz.cmbAccountRefund(recordItem);
        }
    }
}
