package com.zhixianghui.service.trade.biz;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.vo.OfflineExcelRow;
import com.zhixianghui.service.trade.dao.SignRecordDao;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* 签约信息表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-01-14
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SignRecordBiz {
    public static final String SIGN_RECORD_CACHE_KEY = "signRecord:cache:";

    private final SignRecordDao signrecordDao;
    private final TradeHelperBiz tradeHelperBiz;

    private final RedisClient redisClient;

    public SignRecord getOne(Map<String, Object> paramMap) {
        final SignRecord record = signrecordDao.getOne(paramMap);
        if (record != null) {
            redisClient.set(buildCacheKey(record.getMainstayNo(), record.getEmployerNo(), record.getReceiveIdCardNoMd5()), record, -1);
        }
        return record;
    }

    public SignRecord getOne2(Map<String, Object> paramMap) {
        final SignRecord record = signrecordDao.getOne2(paramMap);
        if (record != null) {
            redisClient.set(buildCacheKey(record.getMainstayNo(), record.getEmployerNo(), record.getReceiveIdCardNoMd5()), record, -1);
        }
        return record;
    }

    public void insert(SignRecord record) {
        signrecordDao.insert(record);
        redisClient.set(buildCacheKey(record.getMainstayNo(), record.getEmployerNo(), record.getReceiveIdCardNoMd5()), record, -1);
    }

    public void update(SignRecord record) {
        record.setUpdateTime(new Date());
        signrecordDao.update(record);
        redisClient.set(buildCacheKey(record.getMainstayNo(), record.getEmployerNo(), record.getReceiveIdCardNoMd5()), record, -1);
    }

    public void updateIfNotNull(SignRecord record) {
        record.setUpdateTime(new Date());
        signrecordDao.updateIfNotNull(record);
        redisClient.set(buildCacheKey(record.getMainstayNo(), record.getEmployerNo(), record.getReceiveIdCardNoMd5()), record, -1);
    }

    public PageResult<List<SignRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        tradeHelperBiz.replacePrivacy(paramMap);
        return signrecordDao.listPage2(paramMap,pageParam);
    }

    public long count(Map<String, Object> paramMap) {
        tradeHelperBiz.replacePrivacy(paramMap);
        return signrecordDao.countBy(paramMap);
    }

    public List<SignRecord> list(HashMap<String, Object> paramMap) {
        tradeHelperBiz.replacePrivacy(paramMap);
        return  signrecordDao.listBy(paramMap);
    }

    public List<SignRecord> listNew(Map<String, Object> paramMap) {
        tradeHelperBiz.replacePrivacy(paramMap);
        return  signrecordDao.listBy(paramMap);
    }

    public SignRecord getById(Long signId) {
        return signrecordDao.getById(signId);
    }

    public void updateSmsFrequency(SignRecord record) {
        signrecordDao.update("updateSmsFrequency", record);
        redisClient.set(buildCacheKey(record.getMainstayNo(), record.getEmployerNo(), record.getReceiveIdCardNoMd5()), record, -1);
    }

    public void changeStatus(SignRecord signRecord) {
        signrecordDao.update("changeStatus", signRecord);
        redisClient.set(buildCacheKey(signRecord.getMainstayNo(), signRecord.getEmployerNo(), signRecord.getReceiveIdCardNoMd5()), signRecord, -1);
    }

    public void resetSmsSendFrequency(SignRecord record) {
        signrecordDao.update("resetSmsSendFrequency", record);
        redisClient.set(buildCacheKey(record.getMainstayNo(), record.getEmployerNo(), record.getReceiveIdCardNoMd5()), record, -1);
    }

    public void deleteSignRecordById(Long id) {
        final SignRecord record = this.getById(id);
        redisClient.del(buildCacheKey(record.getMainstayNo(), record.getEmployerNo(), record.getReceiveIdCardNoMd5()));
        signrecordDao.deleteById(id);
    }

    public SignRecord getByOnePhone(String phone) {
        return signrecordDao.getByPhone(phone);
    }

    public boolean isSigned(String mainstayNo, String employerNo, String idCardMd5) {
        final SignRecord signRecord = redisClient.get(buildCacheKey(mainstayNo, employerNo, idCardMd5), SignRecord.class);
        if (!Objects.isNull(signRecord)&&signRecord.getSignStatus().intValue() == SignStatusEnum.SIGN_SUCCESS.getValue()) {
            return true;
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", employerNo);
        paramMap.put("receiveIdCardNoMd5", idCardMd5);
        paramMap.put("mainstayNo", mainstayNo);
        final SignRecord recordInDb = this.getOne(paramMap);
        if (!Objects.isNull(recordInDb)&&recordInDb.getSignStatus().intValue() == SignStatusEnum.SIGN_SUCCESS.getValue()) {
            redisClient.set(buildCacheKey(recordInDb.getMainstayNo(), recordInDb.getEmployerNo(), recordInDb.getReceiveIdCardNoMd5()), recordInDb, -1);
            return true;
        }
        return false;
    }

    private String buildCacheKey(String manstayNo, String employerNo, String idcardNoMd5) {
        return SIGN_RECORD_CACHE_KEY + manstayNo + ":" + employerNo + ":" + idcardNoMd5;
    }


    public List<SignRecord> listby2(Map<String, Object> paramMap) {
        List<Map<String,Object>> mapList = signrecordDao.listBy("listBy2",paramMap);
        if (mapList.size() == 0){
            return null;
        }
        List<SignRecord> signRecords = mapList.stream().map(item -> {
            SignRecord signRecord = new SignRecord();
            BeanUtil.mapToObject(signRecord, item);
            return signRecord;
        }).collect(Collectors.toList());
        return signRecords;
    }
}
