package com.zhixianghui.service.trade.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.service.OfflineOrderFacade;
import com.zhixianghui.service.trade.biz.OfflineOrderBiz;
import com.zhixianghui.service.trade.process.OfflineOrderGrantHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OfflineOrderFacadeImpl implements OfflineOrderFacade {
    @Autowired
    private OfflineOrderBiz offlineOrderBiz;
    @Autowired
    private OfflineOrderGrantHandler offlineOrderGrantHandler;

    @Override
    public void saveOrder(OfflineOrder offlineOrder) {
        offlineOrderBiz.saveOrder(offlineOrder);
    }

    @Override
    public OfflineOrder getById(Long id) {
        return offlineOrderBiz.getById(id);
    }

    @Override
    public Page<OfflineOrder> pageOrder(Page<OfflineOrder> page, Map<String, Object> param) {
        return offlineOrderBiz.pageOrder(page, param);
    }

    @Override
    public List<OfflineOrder> listBy(Map<String, Object> param) {
        return offlineOrderBiz.listBy(param);
    }

    @Override
    public OfflineOrder getByPlatBatchNo(String platBatchNo) {
        return offlineOrderBiz.getByPlatBatchNo(platBatchNo);
    }

    @Override
    public OfflineOrder getOne(Map<String, Object> param) {
        return offlineOrderBiz.getOne(param);
    }

    @Override
    public void update(OfflineOrder offlineOrder) {
        offlineOrderBiz.update(offlineOrder);
    }

    @Override
    public void startAccept(OfflineOrder offlineOrder) {
        offlineOrderGrantHandler.startAccept(offlineOrder);
    }

    @Override
    public void startGrant(String platBatchNo) throws BizException {
        offlineOrderGrantHandler.startGrant(platBatchNo);
    }

    @Override
    public BigDecimal sumWaitInvoiceAmount(Map<String, Object> paramMap) {
        return offlineOrderBiz.sumWaitInvoiceAmount(paramMap);
    }

    @Override
    public void cancelBatchOrder(OfflineOrder order) {
        offlineOrderBiz.cancelBatchOrder(order);
    }

    @Override
    public void delete(OrderDeleteDTO orderDeleteDTO) {
        offlineOrderBiz.delete(orderDeleteDTO);
    }
}
