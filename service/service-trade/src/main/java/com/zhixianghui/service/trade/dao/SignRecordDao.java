package com.zhixianghui.service.trade.dao;

import com.beust.jcommander.internal.Lists;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.trade.entity.SignRecord;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 签约信息表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2021-01-14
 */
@Repository
public class SignRecordDao extends MyBatisDao<SignRecord,Long> {

    public PageResult<List<SignRecord>> listPage2(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<Map<String, Object>>> pageResult = this.listPage(fillSqlId("listBy2"), paramMap, pageParam);
        PageResult<List<SignRecord>> listPageResult = PageResult.newInstance(pageParam, Lists.newArrayList());
        if (pageResult == null) {
            return listPageResult;
        }
        if (pageResult.getData() != null) {
            List<Map<String, Object>> datas = pageResult.getData();
            List<SignRecord> signRecords = datas.stream().map(item -> {
                SignRecord signRecord = new SignRecord();
                BeanUtil.mapToObject(signRecord, item);
                return signRecord;
            }).collect(Collectors.toList());
            listPageResult.setData(signRecords);
            listPageResult.setTotalRecord(pageResult.getTotalRecord());
        }

        return listPageResult;
    }

    public SignRecord getOne2(Map<String, Object> paramMap){
        Map<String, Object> data = this.getOne(fillSqlId("listBy2"), paramMap);
        if (data == null) {
            return null;
        }
        SignRecord signRecord = new SignRecord();
        BeanUtil.mapToObject(signRecord, data);
        signRecord.setEncryptKeyId(Long.valueOf(String.valueOf(data.get("aKeyId"))));
        signRecord.setReceiveIdCardNoEncrypt(AESUtil.decryptECB(String.valueOf(data.get("receiveIdCardNo")), EncryptKeys.getEncryptKeyById(Long.valueOf(String.valueOf(data.get("bKeyId")))).getEncryptKeyStr()));
        signRecord.setReceiveNameEncrypt(AESUtil.decryptECB(String.valueOf(data.get("receiveIdCardNo")), EncryptKeys.getEncryptKeyById(Long.valueOf(String.valueOf(data.get("bKeyId")))).getEncryptKeyStr()));
        return signRecord;
    }

    public SignRecord getByPhone(String phone){
        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("phone",phone);
        return this.getOne(fillSqlId("getByPhone"), paramMap);
    }
}
