package com.zhixianghui.service.trade.vo.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/2/14 10:12
 */
@Data
@Accessors(chain = true)
public class LoginReqVo implements Serializable {
    @NotBlank(message = "手机号码不能为空")
    private String phone;
    @NotBlank(message = "验证码不能为空")
    private String code;
    private String userNo;
    private String loginFlag;

    private String encryptedData;
    private String iv;

}
