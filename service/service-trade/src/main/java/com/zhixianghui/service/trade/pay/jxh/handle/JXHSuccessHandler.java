package com.zhixianghui.service.trade.pay.jxh.handle;

import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.facade.banklink.vo.jxh.JxhParam;
import com.zhixianghui.facade.banklink.vo.jxh.ResStatus;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.service.trade.biz.JXHLocalPayBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.pay.jxh.JXHHandler;
import com.zhixianghui.service.trade.pay.jxh.annotations.JXHService;

import javax.annotation.Resource;
import java.util.Date;

@JXHService(type = ResStatus.SUCCESS)
public class JXHSuccessHandler extends JXHHandler {

    private static final Integer LOG_TYPE = WxAmountChangeLogTypeEnum.PAYMENT.getValue();
    @Resource
    private RecordItemBiz recordItemBiz;
    @Resource
    private JXHLocalPayBiz jxhLocalPayBiz;

    @Override
    public void handle(JxhParam jxhParam) {
        AcChangeFunds changeFunds = buildAcChangeFunds(jxhParam);
        AcChangeFunds serviceFeeFunds = buildFee(jxhParam);
        PayReceiveRespVo payReceiveRespVo = getPayReceiveRespVo(jxhParam);

        jxhLocalPayBiz.updateOrder(changeFunds, payReceiveRespVo);
        updateChangeFounds(serviceFeeFunds);

    }


    public void updateFounds(JxhParam jxhParam) {
        AcChangeFunds acChangeFunds = buildAcChangeFunds(jxhParam);
        AcChangeFunds serviceFeeFunds = buildFee(jxhParam);
        jxhLocalPayBiz.updateChangesFunds(acChangeFunds);
        updateChangeFounds(serviceFeeFunds);
    }

    public PayRespVo getResVo(JxhParam jxhParam) {
        updateFounds(jxhParam);
        PayRespVo respVo = new PayRespVo();
        respVo.setBankTrxNo(jxhParam.getPlatformSerialNo());
        respVo.setBankOrderNo(jxhParam.getMerchantOrderNo());
        respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
        respVo.setBizCode("");
        respVo.setBizMsg("");
        return respVo;
    }

    private PayReceiveRespVo getPayReceiveRespVo(JxhParam jxhParam) {
        PayReceiveRespVo payReceiveRespVo = new PayReceiveRespVo();
        payReceiveRespVo.setBankTrxNo(jxhParam.getPlatformSerialNo());
        payReceiveRespVo.setBankOrderNo(jxhParam.getMerchantOrderNo());
        payReceiveRespVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
        payReceiveRespVo.setBizCode("");
        return payReceiveRespVo;
    }

    public AcChangeFunds buildAcChangeFunds(JxhParam jxhParam) {
//        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(jxhParam.getPlatTrxNo());
        RecordItem recordItem = recordItemBiz.getByPlatTrxNo(jxhParam.getMerchantOrderNo());
        AcChangeFunds acChangeFunds = new AcChangeFunds();
        acChangeFunds.setMchNo(jxhParam.getEmployerNo());
        acChangeFunds.setMchName(jxhParam.getMchName());
        acChangeFunds.setMainstayNo(jxhParam.getMainstayNo());
        acChangeFunds.setMainstayName(jxhParam.getRealPayerName());
        acChangeFunds.setPlatTrxNo(jxhParam.getPlatTrxNo());
        acChangeFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        acChangeFunds.setAmountChangeType(LOG_TYPE);
        acChangeFunds.setCreateTime(new Date());
        acChangeFunds.setAmount(-AmountUtil.changeToFen(recordItem.getOrderAmount()));
        acChangeFunds.setFrozenAmount(-AmountUtil.changeToFen(recordItem.getOrderAmount()));
        acChangeFunds.setPayChannelNo(jxhParam.getChannelNo());
        acChangeFunds.setPayChannelName(jxhParam.getChannelName());
        String logKey = AcUtil.getLogKey(acChangeFunds);
        acChangeFunds.setLogKey(logKey);
        return acChangeFunds;
    }


    public AcChangeFunds buildFee(JxhParam jxhParam) {
        AcChangeFunds acChangeFunds = new AcChangeFunds();
        acChangeFunds.setPayChannelNo(jxhParam.getChannelNo());
        acChangeFunds.setPayChannelName(jxhParam.getChannelName());
//        AcChangeFunds.setMchNo(jxhParam.getEmployerNo());
//        AcChangeFunds.setMchName(jxhParam.getMchName());
        acChangeFunds.setMainstayNo(jxhParam.getMainstayNo());
        acChangeFunds.setMainstayName(jxhParam.getRealPayerName());
        acChangeFunds.setPlatTrxNo(jxhParam.getPlatTrxNo());
        acChangeFunds.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        acChangeFunds.setAmountChangeType(LOG_TYPE);
        acChangeFunds.setCreateTime(new Date());
        acChangeFunds.setAmount(AmountUtil.changeToFen(jxhParam.getServiceFee()));
        acChangeFunds.setFrozenAmount(0L);
        String logKey = AcUtil.getLogKey(acChangeFunds);
        acChangeFunds.setLogKey(logKey);
        return acChangeFunds;
    }
}
