package com.zhixianghui.service.trade.vo.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName QueryChargeReqVo
 * @Description TODO
 * @Date 2023/2/20 16:39
 */
@Data
public class QueryChargeReqVo implements Serializable {

    @NotBlank(message = "begin_date 开始时间不能为空")
    @Size(min = 10,max = 10,message = "begin_date 必须为yyyy-MM-dd格式")
    private String beginDate;

    @NotBlank(message = "end_date 结束时间不能为空")
    @Size(min = 10,max = 10,message = "end_date 必须为yyyy-MM-dd格式")
    private String endDate;
}
