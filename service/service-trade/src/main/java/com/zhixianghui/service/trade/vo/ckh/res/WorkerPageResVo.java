package com.zhixianghui.service.trade.vo.ckh.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName WorkerPageResVo
 * @Description TODO
 * @Date 2022/11/2 14:19
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class WorkerPageResVo {

    /**
     * 雇员姓名
     */
    private String name;

    /**
     * 雇员身份证
     */
    private String idCardNo;

    /**
     * 雇员手机号
     */
    private String phoneNo;

    /**
     * 个人状态
     */
    private String status;

    /**
     * 实名状态
     */
    private String auth;

    /**
     * 交付状态
     */
    private String deliveryStatus;

    /**
     * 结算状态
     */
    private String settleStatus;

    /**
     * 任务结果描述
     */
    private String textResult;


    /**
     * 交付物图片
     */
    private List<String> fileUrlList;
}
