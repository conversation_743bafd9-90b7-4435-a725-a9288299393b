package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName QueryChargeVo
 * @Description TODO
 * @Date 2023/2/21 9:41
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class QueryChargeVo {

    /**
     * 充值记录编号
     */
    private String rechargeOrderNo;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 账户类型
     */
    private Integer channelType;

    /**
     * 支付通道
     */
    private String channelName;

    /**
     * 收款方姓名
     */
    private String payeeName;

    /**
     * 收款账号
     */
    private String payeeAccountNo;

    /**
     * 付款方姓名
     */
    private String payerName;

    /**
     * 付款方账号
     */
    private String payerAccountNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 充值金额
     */
    private String amount;

    /**
     * 充值类型
     */
    private Integer rechargeType;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 充值回单下载链接
     */
    private String downloadUrl;
}
