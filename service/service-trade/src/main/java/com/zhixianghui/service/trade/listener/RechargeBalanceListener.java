package com.zhixianghui.service.trade.listener;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.service.trade.biz.AcIncomeRecordBiz;
import com.zhixianghui.service.trade.biz.AccountQueryBiz;
import com.zhixianghui.service.trade.biz.RechargeRecordBiz;
import com.zhixianghui.service.trade.vo.req.RechargeVo;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月06日 15:40:00
 */

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_RECHARGE_BALANCE,
        selectorExpression = MessageMsgDest.TAG_RECHARGE_BALANCE, consumeThreadMax = 1,
        consumerGroup = "RechargeBalanceConsume")
public class RechargeBalanceListener extends BaseRocketMQListener<String> {

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Autowired
    private RechargeRecordBiz rechargeRecordBiz;
    @Autowired
    private AccountQueryBiz accountQueryBiz;
    @Autowired
    private AcIncomeRecordBiz acIncomeRecordBiz;

    @Override
    public void validateJsonParam(String msg) {
        if (StringUtils.isEmpty(msg)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("消息不能为空");
        }
    }

    @Override
    public void consumeMessage(String msg) {
        final RechargeVo rechargeVo = JSONObject.parseObject(msg, RechargeVo.class);
        final RechargeRecord rechargeRecord = rechargeRecordBiz.getByRechargeId(rechargeVo.getRechargeOrderId());
        if (rechargeRecord == null) {
            return;
        }

        final EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(rechargeRecord.getEmployerNo(), rechargeRecord.getMainstayNo(), rechargeRecord.getChannelType().intValue());
        BigDecimal balance = BigDecimal.ZERO;
        // 如果payee_account_no是333开头，则是 君享汇-充值版 模式的充值通知
        if (rechargeVo.getPayeeAccountNo().startsWith("333")) {
            balance = accountQueryBiz.getBalance(employerAccountInfo);
        } else {
            // 维护本地账户余额
            AcMerchantBalance acMerchantBalance = acIncomeRecordBiz.handlerAcIncome(rechargeVo);
            if (acMerchantBalance != null) {
                balance = AmountUtil.changeToYuan(acMerchantBalance.getTotalAmount());
            }
        }
        rechargeRecord.setCurrentBalance(balance);
        rechargeRecord.setUpdateTime(new Date());
        rechargeRecordBiz.updateById(rechargeRecord);
    }
}
