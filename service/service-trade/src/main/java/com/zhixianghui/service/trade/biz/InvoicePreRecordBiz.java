package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.enums.invoice.InvoicePreStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.service.trade.dao.InvoicePreRecordDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预开发票记录业务逻辑类
 */
@Component
public class InvoicePreRecordBiz {

    @Autowired
    private InvoicePreRecordDao invoicePreRecordDao;

    /**
     * 根据用工企业编号和代征主体编号查询
     */
    public InvoicePreRecord getByEmployerAndMainstay(String employerMchNo, String mainstayMchNo) {
        return invoicePreRecordDao.getByEmployerAndMainstay(employerMchNo, mainstayMchNo);
    }

    /**
     * 分页查询
     */
    public PageResult<List<InvoicePreRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return invoicePreRecordDao.listPage(paramMap, pageParam);
    }

    /**
     * 创建预开发票记录
     */
    public void createPreInvoice(InvoicePreRecord record) throws BizException {
        Date now = new Date();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        invoicePreRecordDao.insert(record);
    }

    /**
     * 更新预开发票记录
     */
    public void updatePreInvoice(InvoicePreRecord record) throws BizException {
        Date now = new Date();
        record.setUpdateTime(now);
        invoicePreRecordDao.updateIfNotNull(record);
    }

    /**
     * 删除预开发票记录
     */
    public void deletePreInvoice(Long id) throws BizException {
        invoicePreRecordDao.deleteById(id);
    }

    /**
     * 统计预开票金额
     */
    public Map<String, Object> countInvoiceAmount(Map<String, Object> paramMap) {
        return invoicePreRecordDao.countInvoiceAmount(paramMap);
    }

    public InvoicePreRecord getByEmployerByMap(Map<String, Object> map) {
        return invoicePreRecordDao.getOne(map);
    }

    /**
     * 根据条件批量更新预开票状态
     *
     * @param paramMap 条件参数，包含employerMchNo(用工企业编号)、mainstayMchNo(代征主体编号)、productNo(产品编号)、workCategoryCode(岗位类目)
     * @param status   要更新的状态
     * @return 更新记录数
     */
    public int batchUpdateStatus(Map<String, Object> paramMap, Integer status) {
        paramMap.put("updateStatus", status);
        paramMap.put("updateTime", new Date());
        paramMap.put("completeTime", new Date());
        return invoicePreRecordDao.batchUpdateStatus(paramMap);
    }

    /**
     * 检查是否存在指定条件的预开票记录
     *
     * @param paramMap 查询条件
     * @return true-存在，false-不存在
     */
    public boolean existRecord(Map<String, Object> paramMap) {
        return invoicePreRecordDao.existRecord(paramMap);
    }

    /**
     * 查询未完成的预开票记录列表
     *
     * @param record 发票记录
     * @return 预开票记录列表
     */
    public List<InvoicePreRecord> listUnfinishedRecords(InvoiceRecord record) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerMchNo", record.getEmployerMchNo());
        paramMap.put("mainstayMchNo", record.getMainstayMchNo());
        paramMap.put("productNo", record.getProductNo());
        paramMap.put("workCategoryCode", record.getWorkCategoryCode());
        paramMap.put("invoiceStatus", InvoicePreStatusEnum.UN_FINNISH.getValue());
        return invoicePreRecordDao.listBy(paramMap);
    }

    /**
     * 根据ID查询预开票记录
     *
     * @param id 预开票ID
     * @return 预开票记录
     */
    public InvoicePreRecord getById(Long id) {
        return invoicePreRecordDao.getById(id);
    }

    /**
     * 根据ID列表批量查询预开票记录
     *
     * @param ids ID列表
     * @return 预开票记录列表
     */
    public List<InvoicePreRecord> getByIds(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ids", ids);
        return invoicePreRecordDao.listBy(paramMap);
    }
}