package com.zhixianghui.service.trade.pay.jxh.handle;

import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.facade.banklink.vo.jxh.JxhParam;
import com.zhixianghui.facade.banklink.vo.jxh.ResStatus;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.service.trade.biz.JXHLocalPayBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.pay.jxh.JXHHandler;
import com.zhixianghui.service.trade.pay.jxh.annotations.JXHService;

import javax.annotation.Resource;
import java.util.Date;

@JXHService(type = ResStatus.FAIL)
public class JXHFailHandler extends JXHHandler {

    private static final Integer LOG_TYPE = WxAmountChangeLogTypeEnum.PAYMENT.getValue();
    @Resource
    private RecordItemBiz recordItemBiz;
    @Resource
    private JXHLocalPayBiz jxhLocalPayBiz;

    @Override
    public void handle(JxhParam jxhParam) {
        AcChangeFunds changeFunds = buildAcChangeFunds(jxhParam);
        PayReceiveRespVo payReceiveRespVo = getPayReceiveRespVo(jxhParam);
        jxhLocalPayBiz.updateOrder(changeFunds, payReceiveRespVo);

    }


    public void updateFounds(JxhParam jxhParam) {
        AcChangeFunds acChangeFunds = buildAcChangeFunds(jxhParam);
        jxhLocalPayBiz.updateChangesFunds(acChangeFunds);
    }

    public PayRespVo getResVo(JxhParam jxhParam) {
        updateFounds(jxhParam);
        PayRespVo respVo = new PayRespVo();
        respVo.setBankTrxNo(jxhParam.getPlatformSerialNo());
        respVo.setBankOrderNo(jxhParam.getMerchantOrderNo());
        respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
        respVo.setBizCode(jxhParam.getErrorCode());
        respVo.setBizMsg(WxUtil.getFailReason(jxhParam.getErrorDesc()));
        return respVo;
    }

    private PayReceiveRespVo getPayReceiveRespVo(JxhParam jxhParam) {
        PayReceiveRespVo payReceiveRespVo = new PayReceiveRespVo();
        payReceiveRespVo.setBankTrxNo(jxhParam.getPlatformSerialNo());
        payReceiveRespVo.setBankOrderNo(jxhParam.getMerchantOrderNo());
        payReceiveRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
        payReceiveRespVo.setBizCode(jxhParam.getErrorCode());
        payReceiveRespVo.setBizMsg(jxhParam.getErrorDesc());
        return payReceiveRespVo;
    }

    public AcChangeFunds buildAcChangeFunds(JxhParam jxhParam) {
//        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(jxhParam.getPlatTrxNo());
        RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(jxhParam.getMerchantOrderNo());
        AcChangeFunds changesFunds = new AcChangeFunds();
        changesFunds.setPayChannelNo(jxhParam.getChannelNo());
        changesFunds.setPayChannelName(jxhParam.getChannelName());
        changesFunds.setMchNo(jxhParam.getEmployerNo());
        changesFunds.setMchName(jxhParam.getMchName());
        changesFunds.setMainstayNo(jxhParam.getMainstayNo());
        changesFunds.setMainstayName(jxhParam.getRealPayerName());
        changesFunds.setPlatTrxNo(jxhParam.getPlatTrxNo());
        changesFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        changesFunds.setCreateTime(new Date());
        changesFunds.setAmountChangeType(LOG_TYPE);
        changesFunds.setAmount(0L);
        changesFunds.setFrozenAmount(-AmountUtil.changeToFen(recordItem.getOrderAmount()));
        String logKey = AcUtil.getLogKey(changesFunds);
        changesFunds.setLogKey(logKey);
        return changesFunds;
    }


}
