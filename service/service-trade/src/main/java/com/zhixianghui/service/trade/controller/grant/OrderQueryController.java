package com.zhixianghui.service.trade.controller.grant;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.common.statics.constants.redis.RedisKeysConstant;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bill.BillStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.vo.ApiBillVo;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.RechargeRecordBiz;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import com.zhixianghui.service.trade.vo.req.*;
import com.zhixianghui.service.trade.vo.res.*;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 订单相关查询Controller
 * @date 2020-12-21 10:06
 **/
@Slf4j
@RestController
@RequestMapping("zxh")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderQueryController {


    private final ZxhLimitBiz limitBiz;
    private final OrderItemBiz orderItemBiz;
    private final RedisClient redisClient;
    private final RechargeRecordBiz rechargeRecordBiz;

    @Reference
    private NotifyFacade notifyFacade;

    @PostMapping("billQuery")
    public ResponseDto<BillQueryResVo> billQuery(@Valid @RequestBody RequestDto<BillQueryReqVo> paramVo){
        BillQueryReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        String key =  RedisKeysConstant.API_BILL_PREFIX + employerNo + ":" + reqVo.getFileId();
        try {
            Map<String,String> redisMap = redisClient.hgetAll(key);
            if (redisMap == null || redisMap.size() == 0){
                throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("file_id 不存在");
            }

            Integer status = Integer.valueOf(redisMap.get("status"));
            String msg = redisMap.get("msg");
            List<ApiBillVo> apiBillVoList = new ArrayList<>();
            BillQueryResVo billQueryResVo = new BillQueryResVo();
            if (status.intValue() == BillStatusEnum.SUCCESS.getValue()){
                 apiBillVoList = JsonUtil.toList(redisMap.get("file"),ApiBillVo.class);
                 billQueryResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
                 billQueryResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            }else if (status.intValue() == BillStatusEnum.PROCESS.getValue()){
                billQueryResVo.setBizErrCode(BizCodeEnum.IN_PROCESS.getCode());
                billQueryResVo.setBizErrMsg("回单仍在处理中，请稍后重试");
            }else if (status.intValue() == BillStatusEnum.FAIL.getValue()){
                return ResponseDto.fail(ApiExceptions.API_BILL_ACCEPT_ERROR.getApiErrorCode(),msg);
            }
            billQueryResVo.setFileList(apiBillVoList);
            ResponseDto<BillQueryResVo> resVo = ResponseDto.success(billQueryResVo,"");
            return resVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }

    @PostMapping("/billApply")
    public ResponseDto<BillApplyResVo> billApply(@RequestBody RequestDto<BillApplyReqVo> paramVo){
        BillApplyReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        try {
            if (StringUtils.isBlank(reqVo.getMchOrderNo()) && StringUtils.isBlank(reqVo.getPlatTrxNo())){
                throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("mch_order_no和plat_trx_no 不能都为空");
            }
            String fileId = RandomUtil.get32LenStr();
            String key =  RedisKeysConstant.API_BILL_PREFIX + employerNo + ":" + fileId;
            Map<String,String> map = new HashMap<>();
            map.put("fileId",fileId);
            map.put("mchOrderNo",StringUtils.isNotBlank(reqVo.getMchOrderNo()) ? reqVo.getMchOrderNo() : "");
            map.put("platTrxNo",StringUtils.isNotBlank(reqVo.getPlatTrxNo()) ? reqVo.getPlatTrxNo() : "");
            map.put("status", String.valueOf(BillStatusEnum.PROCESS.getValue()));
            map.put("employerNo",employerNo);
            map.put("msg",BillStatusEnum.PROCESS.getDesc());
            redisClient.hset(key,map);
            redisClient.expire(key,2*60*60);

            log.info("申请电子回单下载链接，商户号：[{}],商户订单号：[{}]，平台流水号：[{}]，fileId：[{}]",
                    employerNo,reqVo.getMchOrderNo(),reqVo.getPlatTrxNo(),fileId);

            BillApplyResVo billApplyResVo = new BillApplyResVo();
            billApplyResVo.setFileId(fileId);
            billApplyResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            billApplyResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<BillApplyResVo> resVo = ResponseDto.success(billApplyResVo,"");

            //发送消息申请下载电子回单
            notifyFacade.sendOne(MessageMsgDest.TOPIC_API_BILL_APPLY,
                    employerNo,
                    fileId,
                    NotifyTypeEnum.TRADE_CERTIFICATE.getValue(),
                    MessageMsgDest.TAG_API_BILL_APPLY,
                    JSON.toJSONString(map));
            return resVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }

    @PostMapping("/queryDetail")
    public ResponseDto<QueryDetailResVo> queryDetail(@RequestBody @Valid RequestDto<QueryDetailReqVo> paramVo) {
        QueryDetailReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();

        String logFlag = employerNo + "-" + reqVo.getMchOrderNo();
        log.info("[{}]查询订单详情信息：{}", logFlag, JsonUtil.toString(paramVo));
        try {
            // 检验产品开通
            limitBiz.validateProductOpen(employerNo, ProductNoEnum.ZXH.getValue());

            // 查询订单明细
            OrderItem item = orderItemBiz.getByEmployerNoAndMchOrderNoApi(employerNo, reqVo.getMchOrderNo());
            if (item == null) {
                throw ApiExceptions.API_TRADE_ORDER_NOT_EXIST;
            }
            String key = RandomUtil.get16LenStr();
            QueryDetailResVo result = fillQueryDetailRespVo(item, key);
            ResponseDto<QueryDetailResVo> returnVo = ResponseDto.success(result,key);
            log.info("[{}]查询订单详情成功：{}", logFlag, JsonUtil.toString(returnVo));
            return returnVo;
        }catch (BizException e){
            log.error("[{}]==>查询订单详情 业务异常：", logFlag, e);
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            log.error("[{}]==>查询订单详情 系统异常：", logFlag, e);
            return ResponseDto.unknown();
        }
    }

    @PostMapping("queryRecharge")
    public ResponseDto<QueryChargeResVo> queryCharge(@RequestBody @Valid RequestDto<QueryChargeReqVo> paramVo){
        QueryChargeReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        log.info("接口查询充值记录，商户号：[{}]，请求参数：[{}]",employerNo,JsonUtil.toString(paramVo));
        try {
            Date beginDate = DateUtil.stringToDateTime(reqVo.getBeginDate());
            Date endDate = DateUtil.addDay(DateUtil.stringToDateTime(reqVo.getEndDate()),1);
            int dateMinus = DateUtil.subtractDays(endDate,beginDate);
            if (dateMinus > 31){
                throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("查询日期间隔最大不能超过31天");
            } else if (dateMinus < 0) {
                throw ApiExceptions.API_PARAM_FAIL;
            }
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("employerNo",employerNo);
            paramMap.put("createBeginDate",beginDate);
            paramMap.put("createEndDate",endDate);
            List<RechargeRecord> rechargeRecords = rechargeRecordBiz.getRechargeRecordList(paramMap);
            String key = RandomUtil.get16LenStr();
            QueryChargeResVo result = fillQueryRechargeResVo(rechargeRecords, key);
            //错误描述
            result.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            result.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<QueryChargeResVo> returnVo = ResponseDto.success(result,key);
            return returnVo;
        }catch (BizException e){
            log.error("商户号：[{}]，查询充值记录异常：",employerNo,e);
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            log.error("商户号：[{}]，查询充值记录异常：",employerNo,e);
            return ResponseDto.unknown();
        }
    }

    @PostMapping("userMonthGrantData")
    public ResponseDto<UserMonthGrantDataResVo> userMonthGrantData(@RequestBody @Valid RequestDto<UserMonthGrantDataReqVo> paramVo) {
        UserMonthGrantDataReqVo userMonthGrantDataReqVo = paramVo.getData();

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("completeBeginDate", cn.hutool.core.date.DateUtil.beginOfMonth(new Date()).toJdkDate());
        paramMap.put("completeEndDate", cn.hutool.core.date.DateUtil.endOfMonth(new Date()).toJdkDate());
        paramMap.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(userMonthGrantDataReqVo.getReceiveIdCardNo()));
        paramMap.put("mainstayNo", userMonthGrantDataReqVo.getMainstayNo());
        paramMap.put("employerNo", paramVo.getMchNo());
        log.info("[userMonthGrantData]查询参数 {}", JSONUtil.toJsonPrettyStr(paramMap));

        Map<String, BigDecimal> monthGrantData = orderItemBiz.queryUserMonthGrantData(paramMap);

        UserMonthGrantDataResVo userMonthGrantDataResVo = new UserMonthGrantDataResVo();
        if (monthGrantData == null) {
            userMonthGrantDataResVo.setAmount(BigDecimal.ZERO);
        }else {
            userMonthGrantDataResVo.setAmount(monthGrantData.get("totalAmount") == null ? BigDecimal.ZERO : monthGrantData.get("totalAmount"));
        }
        userMonthGrantDataResVo.setReceiveIdCardNo(userMonthGrantDataReqVo.getReceiveIdCardNo());
        userMonthGrantDataResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
        userMonthGrantDataResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
        return ResponseDto.success(userMonthGrantDataResVo,"");
    }

    private QueryChargeResVo fillQueryRechargeResVo(List<RechargeRecord> rechargeRecords, String key) {
        QueryChargeResVo queryChargeResVo = new QueryChargeResVo();
        List<QueryChargeVo> list = rechargeRecords.stream().map(x->{
            QueryChargeVo vo = new QueryChargeVo();
            vo.setAmount(x.getRechargeAmount().toPlainString());
            vo.setChannelName(x.getChannelName());
            vo.setChannelType(x.getChannelType().intValue());
            vo.setCreateTime(DateUtil.formatDateTime(x.getCreateTime()));
            vo.setDownloadUrl(x.getReceiptUrl());
            vo.setMainstayName(x.getMainstayName());
            vo.setPayeeAccountNo(AESUtil.encryptECB(x.getPayeeIdentity(), key));
            vo.setPayeeName(AESUtil.encryptECB(x.getPayeeName(), key));
            vo.setPayerAccountNo(AESUtil.encryptECB(x.getPayerIdentity(), key));
            vo.setPayerName(AESUtil.encryptECB(x.getPayerName(), key));
            vo.setPayTime(x.getTransPayTime() == null ? "" : DateUtil.formatDateTime(x.getTransPayTime()));
            vo.setRechargeOrderNo(x.getRechargeOrderId());
            vo.setRechargeType(x.getRechargeType());
            vo.setStatus(x.getRechargeStatus().intValue());
            return vo;
        }).collect(Collectors.toList());
        queryChargeResVo.setRechargeRecordList(list);
        return queryChargeResVo;
    }


    private QueryDetailResVo fillQueryDetailRespVo(OrderItem item, String key) {
        QueryDetailResVo queryDetailResVo = new QueryDetailResVo();
        BeanUtils.copyProperties(item, queryDetailResVo);
        //敏感信息处理
        queryDetailResVo.setReceiveName(AESUtil.encryptECB(item.getReceiveNameDecrypt(), key));
        queryDetailResVo.setReceiveIdCardNo(AESUtil.encryptECB(item.getReceiveIdCardNoDecrypt(), key));
        queryDetailResVo.setReceiveAccountNo(AESUtil.encryptECB(item.getReceiveAccountNoDecrypt(), key));
        queryDetailResVo.setReceivePhoneNo(AESUtil.encryptECB(item.getReceivePhoneNoDecrypt(), key));
        //金额字符化
        queryDetailResVo.setOrderItemNetAmount(String.valueOf(item.getOrderItemNetAmount()));
        queryDetailResVo.setOrderItemFee(String.valueOf(item.getOrderItemFee()));
        queryDetailResVo.setOrderItemAmount(String.valueOf(item.getOrderItemAmount()));
        queryDetailResVo.setJobName(null);
        queryDetailResVo.setJobNo(null);
        //错误描述
        queryDetailResVo.setBizErrCode(StringUtils.isBlank(item.getErrorCode())? BizCodeEnum.SUCCESS.getCode() : item.getErrorCode());
        queryDetailResVo.setBizErrMsg(StringUtils.isBlank(item.getErrorDesc())? BizCodeEnum.SUCCESS.getMsg() : item.getErrorDesc());
        return queryDetailResVo;
    }

    public static void main(String[] args) {

        System.out.println(cn.hutool.core.date.DateUtil.beginOfMonth(new Date()).toJdkDate());
        System.out.println(cn.hutool.core.date.DateUtil.endOfMonth(new Date()).toJdkDate());

    }
}
