package com.zhixianghui.service.trade.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/24 16:42
 */
@Data
public class ApplyAccessTokenReqVo implements Serializable {
    public String grant_type = "client_credential";
    public String appid;
    public String secret;

    public ApplyAccessTokenReqVo(String appId, String secret) {
        this.appid = appId;
        this.secret = secret;
    }

    public Map<String, String> getParam(ApplyAccessTokenReqVo applyAccessToken) {
        Map<String, String> map = new HashMap<>();
        map.put("grant_type", this.grant_type);
        map.put("appid", this.appid);
        map.put("secret", this.secret);
        return map;
    }
}
