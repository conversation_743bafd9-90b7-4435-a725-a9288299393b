package com.zhixianghui.service.trade.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.zhixianghui.common.statics.enums.common.SuccessFailCodeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.OperateStatusEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.ExecuteUrlResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.CreateFileByTemplateResDataVoV3;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.vo.CreateSignReqVo;
import com.zhixianghui.facade.trade.vo.SignRecordVo;
import com.zhixianghui.service.trade.biz.SignRecordBiz;
import com.zhixianghui.service.trade.biz.UserInfoBiz;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import com.zhixianghui.service.trade.process.AbstractSign;
import com.zhixianghui.service.trade.process.SignBiz;
import com.zhixianghui.service.trade.utils.ESignUtil;
import com.zhixianghui.service.trade.utils.seal.SealUtil;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;

import java.awt.*;
import java.io.*;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 签约信息表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-01-14
 */
@Slf4j
@Service(timeout = 30000)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SignRecordImpl extends AbstractSign implements SignRecordFacade {

    private final SignRecordBiz biz;
    private final SignBiz signBiz;
    private final RedisLock redisLock;
    private final ZxhLimitBiz limitBiz;
    private final UserInfoBiz userInfoBiz;

    @Autowired
    private FastdfsClient fastdfsClient;

    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private SequenceFacade sequenceFacade;

    @Value("${sign.fontFamilyByPerson:KaiTi}")
    private String fontFamilyPerson;
    @Value("${sign.fontFamilyByCompany:STFangsong}")
    private String fontFamilyCompany;


    @Override
    public SignRecord isExist(SignRecordVo item) {
        return getOne(item);
    }

    @Override
    public SignRecord getOne(Map<String, Object> paramMap) {
        return biz.getOne(paramMap);
    }

    @Override
    public void resetSmsSendFrequency(SignRecord record) {
        biz.resetSmsSendFrequency(record);

    }

    @Override
    public SignRecord getById(Long signId) {
        return biz.getById(signId);
    }

    private SignRecord getOne(SignRecordVo recordVo) {
        Map<String, Object> map = new HashMap<>();
        map.put("employerNo", recordVo.getEmployerNo());
        map.put("mainstayNo", recordVo.getMainstayNo());
        map.put("receiveNameMd5", MD5Util.getMixMd5Str(recordVo.getName()));
        map.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(recordVo.getIdCard()));

        return getOne(map);
    }

    @Override
    public void update(SignRecord record) {
        biz.update(record);

        SignRecord signRecord = biz.getOne(Collections.singletonMap("userId", record.getUserId()));
        userInfoBiz.insertOrUpdate(new UserInfo().buildUserInfo(signRecord));
//        try {
//            signBiz.syncToExteranlSystem(record);
//        } catch (Exception e) {
//            log.error("同步信息到供应商失败:{}", e.getMessage());
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("同步签约信息到供应商失败");
//        }
    }

    @Override
    public PageResult<List<SignRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap, pageParam);
    }

    @Override
    public List<SignRecord> listRecords(Map<String, Object> paramMap) {
        return biz.listby2(paramMap);
    }

    @Override
    public void insert(SignRecord record) {
        biz.insert(record);
    }

    @Override
    public boolean auth(SignRecord record) {
        return signBiz.auth(record);
    }


    @Override
    public boolean modify(SignRecordVo record) {
        // 需要处理修改三药素之后，数据库里是否存在相同要素的数据的问题
        SignRecord signRecord = biz.getById(record.getId());
        biz.deleteSignRecordById(record.getId());
        if (!StringUtils.equals(record.getName(), signRecord.getReceiveNameDecrypt())
                || !StringUtils.equals(record.getIdCard(), signRecord.getReceiveIdCardNoDecrypt())
        ) {
            final Map<String, Object> param = ESignUtil.buildParam(record.getName(), record.getIdCard(), signRecord.getMainstayNo(), signRecord.getEmployerNo());
            final SignRecord one = biz.getOne(param);
            if (one != null && one.getId().longValue() != signRecord.getId().longValue()) {

                if (one.getInfoStatus() == SuccessFailCodeEnum.SUCCESS.getValue()) {
                    signRecord = one;
                    signRecord.setReceivePhoneNoEncrypt(record.getPhone());
                } else {
                    biz.deleteSignRecordById(one.getId());
                    signRecord.setReceiveNameEncrypt(record.getName());
                    signRecord.setReceiveIdCardNoEncrypt(record.getIdCard());
                    signRecord.setReceivePhoneNoEncrypt(record.getPhone());
                }

            } else {
                signRecord.setReceiveNameEncrypt(record.getName());
                signRecord.setReceiveIdCardNoEncrypt(record.getIdCard());
                signRecord.setReceivePhoneNoEncrypt(record.getPhone());
            }

        } else {
            signRecord.setReceiveNameEncrypt(record.getName());
            signRecord.setReceiveIdCardNoEncrypt(record.getIdCard());
            signRecord.setReceivePhoneNoEncrypt(record.getPhone());
        }

        signRecord.setSignType(ChannelSignTypeEnum.MSG.getValue());
        this.startSign(signRecord, true);
        return true;
    }


    @Override
    public boolean resend(SignRecord record) {
        String logFlag = record.getEmployerNo() + "-" + record.getMainstayNo();
        log.info("[预签约==> {}] 有签约记录", logFlag);
        try {
            limitBiz.signAuth(record.getReceiveNameDecrypt(), record.getReceiveIdCardNoDecrypt(), record.getReceivePhoneNoDecrypt(), logFlag);
        } catch (BizException ex) {
            log.error("[{}]==>预签约 业务异常：", logFlag, ex);
            SignRecord temp = biz.getById(record.getId());
            if (ObjectUtil.isNotEmpty(temp)) {
                temp.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                temp.setSignStatus(SignStatusEnum.SIGN_FAIL.getValue());
                temp.setErrCode(ex.getApiErrorCode());
                temp.setErrMsg(ex.getErrMsg());
                temp.setOperateStatus(null);
                biz.update(temp);
            }
            return false;
        }

        try {
            log.info("[预签约==> {}] 有签约记录, 发起异步预签约", logFlag);
            reallySign(record, true);
            return true;
        } catch (BizException ex) {
            log.error("[{}]==>预签约 系统异常3：", logFlag, ex);
            if (ObjectUtil.isNotEmpty(record)) {
                record.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                record.setSignStatus(SignStatusEnum.SIGN_FAIL.getValue());
                record.setErrCode(ex.getApiErrorCode());
                record.setErrMsg(ex.getErrMsg());
                record.setOperateStatus(null);
                biz.update(record);
            }
            throw ex;
        } catch (Exception e) {
            log.error("[{}]==>预签约 系统异常4：", logFlag, e);
            SignRecord temp = biz.getById(record.getId());
            // 更新失败记录
            temp.setErrCode(ApiExceptions.API_COMMON_ERROR.getApiErrorCode());
            temp.setErrMsg(e.getMessage());
            temp.setOperateStatus(OperateStatusEnum.RESEND_SMS.getStatus());
            biz.update(temp);
            throw e;
        }
    }

    @Override
    public void preSign(SignRecord record, boolean isNeedSendSms) {
        log.info("签约人:{}进入预签约流程", record.getReceiveNameDecrypt());
        SignRecord signRecord = getOne(new HashMap<String, Object>() {
            private static final long serialVersionUID = 8746510767359886217L;

            {
                put("userId", record.getUserId());
            }
        });
        if (signRecord == null) {
            log.error("签约信息没有找到 : {}", JSONObject.toJSON(record));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约信息没有找到");
        }
        if (signRecord.getInfoStatus() != null && SuccessFailCodeEnum.FAIL.getValue() == signRecord.getInfoStatus()) {
            log.warn("该签约三要素异常: {}", JSONObject.toJSON(signRecord));
            return;
        }

        Assert.notNull(signRecord.getUserId(), "调用e签宝操作的用户id不能为空");
        String logFlag = String.join("-", record.getEmployerNo(), record.getMainstayNo());
        try {
            // 调用e签宝操作
            signBiz.preSign(signRecord.getUserId());
            log.info("签约人:{}e签宝预签约流程完成,signType值为:{}", record.getReceiveNameDecrypt(), signRecord.getSignType());
            if (signRecord.getSignType().intValue() == ChannelSignTypeEnum.STAMP_SILENCE.getValue()) {
                // 静默签约
                silenceSign(signRecord);
            } else {
                // 走真实签约
                // 发送短信
                if (isNeedSendSms && signRecord.getSignStatus().intValue() != SignStatusEnum.SIGN_SUCCESS.getValue()) {
                    reallySign(signRecord, false);
//                updateFrequency(signRecord);
                }
            }
        } catch (BizException ex) {
            log.error("[{}]==>预签约 系统异常1：", logFlag, ex);
            signRecord = biz.getOne(MapUtil.builder(new HashMap<String, Object>()).put("userId", signRecord.getUserId()).build());
            if (ObjectUtil.isNotEmpty(signRecord)) {
                signRecord.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                signRecord.setSignStatus(SignStatusEnum.SIGN_FAIL.getValue());
                signRecord.setErrCode(ex.getApiErrorCode());
                signRecord.setErrMsg(ex.getErrMsg());
                signRecord.setOperateStatus(null);
                biz.update(signRecord);
            }
            throw ex;
        } catch (Exception e) {
            log.error("[{}]==>预签约 系统异常2：", logFlag, e);
            SignRecord temp = biz.getById(signRecord.getId());
            // 更新失败记录
            temp.setErrCode(ApiExceptions.API_COMMON_ERROR.getApiErrorCode());
            temp.setErrMsg(e.getMessage());
            temp.setOperateStatus(OperateStatusEnum.RESEND_SMS.getStatus());
            biz.update(temp);
            throw e;
        }
        // 如果是静默签约-自动盖章, 获取pdf链接添加盖章
        log.info("[{}]-当前签约记录为-{}", logFlag, JSON.toJSON(signRecord));
//        if (signRecord.getSignType() == ChannelSignTypeEnum.STAMP_SILENCE.getValue() && signRecord.getSignStatus().intValue() != SignStatusEnum.SIGN_SUCCESS.getValue()) {
//            log.info("[{}]-静默签约自动盖章",logFlag);
//            signBiz.autoAddSeal(signRecord);
//        }
    }

    /***
     * 静默签约
     * 查询用户所在供应商的签约模版
     * 通过e签宝 填写模板生成文件 接口生成待签约文件(填写供应商信息)
     * 拿到待签约文件
     * 在pdf上填写C端用户的信息
     * 文件传到fastdfs
     * 修改签约记录为完成
     * @param record
     */
    private void silenceSign(SignRecord record) {
        String logFlag = String.join("-", record.getEmployerNo(), record.getUserId());
        log.info("[{}]==>签约人:{}发起用户静默签约", logFlag, record.getReceiveNameDecrypt());
        // 获取锁
        log.info("[签约人:{}发起用户静默签约: {}]==>获取发起签约锁", record.getReceiveNameDecrypt(), logFlag);
        String lockKey = String.join(":", TradeConstant.CREATE_SIGN_LOCK_KEY, logFlag);
        String clientId = redisLock.tryLockLong(lockKey, 0, 1);
        try {
            if (clientId == null) {
                log.info("[签约人:{}发起用户静默签约: {}]==>获取发起签约锁失败，直接丢弃", record.getReceiveNameDecrypt(), logFlag);
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
            }
            CreateFileByTemplateResDataVoV3 resVo = signBiz.createSilenceSign(record);
            if (resVo == null) {
                throw ApiExceptions.API_SIGN_CHANNEL_FAIL;
            }
            if (StringUtil.isEmpty(resVo.getFileDownloadUrl())) {
                throw ApiExceptions.API_SIGN_CHANNEL_FAIL;
            }
            String receiveName = record.getReceiveNameDecrypt();
            String mainstayName = record.getMainstayName();
            if (StringUtil.isEmpty(receiveName) || StringUtil.isEmpty(mainstayName)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约双方名称存在空值，无法进行签约");
            }

            getSystemFontFamily();

            log.info("签约人:{}[{}]==>发起静默签约完成，resVo:{}", record.getReceiveNameDecrypt(), logFlag, JsonUtil.toString(resVo));
            log.info("签约人:{}公司印章采用的字体格式：{}", record.getReceiveNameDecrypt(), fontFamilyCompany);
            log.info("签约人:{}个人印章采用的字体格式：{}", record.getReceiveNameDecrypt(), fontFamilyPerson);
            byte[] companyByte = SealUtil.buildCompanySeal(mainstayName, fontFamilyCompany);
            byte[] personByte = SealUtil.buildPersonSeal(receiveName, fontFamilyPerson);
            if (companyByte != null && personByte != null) {
                byte[] pdfBytes = downloadPdf(resVo.getFileDownloadUrl());
                byte[] stampedPdf = addSealsToPdf(
                        pdfBytes,
                        companyByte,
                        190,  // 公司印章新宽度（单位：像素）
                        140,  // 公司印章新高度（单位：像素）
                        200,  // 公司印章X坐标（距离页面右侧100像素）
                        140,   // 公司印章Y坐标（距离页面底部50像素）
                        personByte,
                        100,  // 个人印章新宽度
                        30,   // 个人印章新高度
                        200,  // 个人印章X坐标
                        80,   // 个人印章Y坐标
                        4      // 页数
                );

                record = getById(record.getId());
                String fileName = UUID.randomUUID().toString().replace("-", "") + ".pdf";
                String fastdfsUrl = fastdfsClient.uploadFile(stampedPdf, fileName);
                record.setFileUrl(fastdfsUrl);
                record.setSignStatus(SignStatusEnum.SIGN_SUCCESS.getValue());
                record.setOperateStatus(null);
                record.setInfoStatus(SuccessFailCodeEnum.SUCCESS.getValue());
                update(record);
                log.info("签约人:{}发起静默签约完成", record.getReceiveNameDecrypt());
            }
        } catch (BizException e) {
            log.error("[{}]==>发起签约 业务异常：", logFlag, e);
            throw e;
        } catch (Exception e) {
            log.error("[{}]==>发起签约 系统异常：", logFlag, e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未知异常，请联系客服");
        } finally {
            if (clientId != null) {
                redisLock.unlockLong(clientId);
            }
        }
    }

    private void getSystemFontFamily() {
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        // 获取所有可用字体
        Font[] fonts = ge.getAllFonts();
        log.info("系统支持的字体列表:{}", Arrays.stream(fonts).map(Font::getFamily).collect(Collectors.toList()));
    }

    /**
     * 给PDF添加电子印章
     *
     * @param pdfBytes    原始PDF字节数组
     * @param companySeal 公司印章图片
     * @param personSeal  个人印章图片
     */
    private static byte[] addSealsToPdf(byte[] pdfBytes,
                                        byte[] companySeal, int companyWidth, int companyHeight, int companyX, int companyY,
                                        byte[] personSeal, int personWidth, int personHeight, int personX, int personY, int pageSize) throws Exception {
        ByteArrayOutputStream outputStream = null;
        PdfReader reader = null;
        PdfStamper stamper = null;
        try {
            reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
            outputStream = new ByteArrayOutputStream();
            stamper = new PdfStamper(reader, outputStream);

            // 添加公司印章
            Image companyImage = Image.getInstance(companySeal);
            companyImage.scaleToFit(companyWidth, companyHeight); // 控制尺寸
            companyImage.setAbsolutePosition(
                    reader.getPageSize(pageSize).getWidth() - companyX, // X坐标（从右侧计算）
                    companyY // Y坐标（从底部计算）
            );
            stamper.getOverContent(pageSize).addImage(companyImage);

            // 添加个人印章（同理）
            Image personImage = Image.getInstance(personSeal);
            personImage.scaleToFit(personWidth, personHeight);
            personImage.setAbsolutePosition(
                    reader.getPageSize(pageSize).getWidth() - personX,
                    personY
            );
            stamper.getOverContent(pageSize).addImage(personImage);

            stamper.close();
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("PDF处理失败", e);
        } finally {
            if (stamper != null) {
                stamper.close();
            }
            if (reader != null) {
                reader.close();
            }
            if (outputStream != null) {
                outputStream.close();
            }
        }
    }

    private static byte[] downloadPdf(String pdfUrl) {
        // 下载pdf文件
        try (HttpResponse resp = HttpRequest.get(pdfUrl).execute()) {
            return resp.bodyBytes();
        } catch (Exception e) {
            log.error("下载pdf文件失败:{}", e.getMessage());
            throw e;
        }
    }

    private void reallySign(SignRecord record, boolean isResendMsg) {
        String logFlag = String.join("-", record.getEmployerNo(), record.getUserId());
        log.info("[{}]==>发起签约", logFlag);
        // 获取锁
        log.info("[发起签约环节: {}]==>获取发起签约锁", logFlag);
        String lockKey = String.join(":", TradeConstant.CREATE_SIGN_LOCK_KEY, logFlag);
        String clientId = redisLock.tryLockLong(lockKey, 0, 1);
        try {
            if (clientId == null) {
                log.info("[发起签约环节: {}]==>获取发起签约锁失败，直接丢弃", logFlag);
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
            }
            log.info("[发起签约环节: {}]==>判断签约类型: {}", logFlag, JSONObject.toJSONString(record));
            CreateSignReqVo reqVo = new SignRecordVo().toCreateSignReqVo(record);
            if (record.getSignType() != null) {
                reqVo.setSignType(record.getSignType());
            }
            ExecuteUrlResDataVo resVo = signBiz.createSign(reqVo, logFlag, isResendMsg);
            if (resVo == null) {
                throw ApiExceptions.API_SIGN_CHANNEL_FAIL;
            }
            log.info("[{}]==>发起签约成功", logFlag);
        } catch (BizException e) {
            log.error("[{}]==>发起签约 业务异常：", logFlag, e);
            throw e;
        } catch (Exception e) {
            log.error("[{}]==>发起签约 系统异常：", logFlag, e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未知异常，请联系客服");
        } finally {
            if (clientId != null) {
                redisLock.unlockLong(clientId);
            }
        }
    }


    @Override
    public boolean isExistTemplate(String mainstayNo, String mchNo) {
        SignRecord signRecord = new SignRecord();
        signRecord.setMainstayNo(mainstayNo);
        signRecord.setEmployerNo(mchNo);
        return StringUtils.isNotBlank(existTemplateId(signRecord, merchantFacade));
    }

    @Override
    public void startSign(SignRecord signRecord, boolean isNeedSendSms) {
        if (!isExistTemplate(signRecord.getMainstayNo(), signRecord.getEmployerNo())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未配置模板");
        }
        String userId = MD5Util.getMixMd5Str(signRecord.getMainstayNo() + signRecord.getEmployerNo() + signRecord.getReceiveIdCardNoDecrypt() + signRecord.getReceiveNameDecrypt());
        signRecord.setUserId(userId);
        if (!auth(signRecord)) {
            log.error("信息鉴权不成功 : {}", JSONObject.toJSON(signRecord));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("信息鉴权不成功");
        }
        preSign(signRecord, isNeedSendSms);
    }

    @Override
    public void addSignImages(SignRecord signRecord) {
        this.update(signRecord);
        userInfoBiz.insertOrUpdate(new UserInfo().buildUserInfo(signRecord));
//        try {
//            signBiz.syncToExteranlSystem(signRecord);
//        } catch (Exception e) {
//            log.error("同步信息到供应商失败",e);
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("同步信息到供应商失败");
//        }
    }

    @Override
    public void addUserImage(UserInfo userInfo) {
        userInfoBiz.insertOrUpdate(userInfo);
    }

    @Override
    public void deleteById(Long id) {
        signBiz.deleteRecordById(id);
    }

    @Override
    public boolean isSigned(String mainstayNo, String employerNo, String idCardMd5) {
        return biz.isSigned(mainstayNo, employerNo, idCardMd5);
    }

    public static void main(String[] args) {
        String url = "https://esignoss.esign.cn/1111564182/b1b815b0-d21a-4249-874e-35dc2a36c981/%E3%80%90%E5%B9%BF%E5%B7%9E%E5%B8%82%E6%B1%87%E8%81%9A%E6%99%BA%E4%BA%AB%E7%94%B5%E5%AD%90%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%E3%80%91%26%E3%80%90%E5%8D%A0%E9%93%96%E3%80%91%E5%85%B1%E4%BA%AB%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf?Expires=1749786043&OSSAccessKeyId=LTAI4G23YViiKnxTC28ygQzF&Signature=SVrOqlHOkivKX2cFT8xXQO2ccAo%3D";
        String mainstayName = "广州汇聚智享科技有限公司";
        String receiveName = "占铖";
        String filePath = "/Users/<USER>/Downloads/pdf001.pdf";
        try {
            byte[] companyByte = SealUtil.buildCompanySeal(mainstayName, "STFangsong");
            byte[] personByte = SealUtil.buildPersonSeal(receiveName, "KaiTi");
            if (companyByte != null && personByte != null) {
                File pdfFile = new File("/Users/<USER>/Downloads/00001.pdf").getCanonicalFile();
                byte[] pdfBytes = FileUtil.readBytes(pdfFile);
                byte[] stampedPdf = addSealsToPdf(
                        pdfBytes,
                        companyByte,
                        190,  // 公司印章新宽度（单位：像素）
                        140,  // 公司印章新高度（单位：像素）
                        200,  // 公司印章X坐标（距离页面右侧100像素）
                        140,   // 公司印章Y坐标（距离页面底部50像素）
                        personByte,
                        100,  // 个人印章新宽度
                        30,   // 个人印章新高度
                        200,  // 个人印章X坐标
                        80,   // 个人印章Y坐标
                        4
                );
                File file = new File(filePath);
                try (FileOutputStream fos = new FileOutputStream(file);
                     BufferedOutputStream bos = new BufferedOutputStream(fos)) {

                    //1.如果父目录不存在，则创建
                    File dir = file.getParentFile();
                    if (!dir.exists()) {
                        dir.mkdirs();
                    }
                    //2.写byte数组到文件
                    bos.write(stampedPdf);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
