package com.zhixianghui.service.trade.biz;

import com.aspose.words.Run;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.exception.WxRepBizException;
import com.zhixianghui.facade.banklink.service.pay.PayBankFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.service.trade.pay.wx.biz.WxNotifyBiz;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.omg.SendingContext.RunTime;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月10日 17:35:00
 */
@Slf4j
@Service
public class WxLocalPayBiz {

    @Autowired
    private ChangesFundsBiz changesFundsBiz;
    @Autowired
    private WxMerchantBalanceBiz wxMerchantBalanceBiz;
    @Reference(retries = -1)
    private PayBankFacade payBankFacade;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private WxNotifyBiz wxNotifyBiz;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;

    public PayRespVo pay(PayReqVo reqVo) {
        String key = WxUtil.getRedisLockKey(reqVo.getEmployerNo(), reqVo.getMainstayNo(), MerchantTypeEnum.EMPLOYER.getValue());
        log.info("[开始发放: {}]订单号:{},获取订单预扣锁：{}", reqVo.getEmployerNo(), reqVo.getPlatTrxNo(), key);
        RLock rLock = redisLock.tryLock(key, WxUtil.LOCK_WAIT_TIME, WxUtil.LOCK_LEASE_TIME);
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        String logKey = WxUtil.getLogKey(reqVo.getPlatTrxNo(), WxAmountChangeLogTypeEnum.FROZEN.getValue(), MerchantTypeEnum.EMPLOYER);
        PayRespVo payRespVo = new PayRespVo();
        try {
            if (rLock == null) {
                log.error("[开始发放: {}]==>获取订单预扣锁失败", reqVo.getPlatTrxNo());
                throw new RuntimeException("发放超时，请稍后再试");
            }
            WxMerchantBalance param = new WxMerchantBalance();
            param.setMchNo(reqVo.getEmployerNo());
            param.setMainstayNo(reqVo.getMainstayNo());
            param.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            WxMerchantBalance wxMerchantBalance = wxMerchantBalanceBiz.getOne(param);

            //获取金额
            Long balance = getAmount(reqVo);
            log.info("[{}]订单预扣，查询商户号[{}]余额：{}", reqVo.getPlatTrxNo(), reqVo.getEmployerNo(), balance);
            Long amount = AmountUtil.changeToFen(reqVo.getTotalAmount());
            log.info("[{}]订单预扣，查询商户号[{}]订单总额：{}", reqVo.getPlatTrxNo(), reqVo.getEmployerNo(), amount);
            if ((balance - amount < 0)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账户余额不足");
            }

            ChangesFunds changesFunds = buildChangesFunds(reqVo, logKey);
            changesFunds.setBeforeAmount(wxMerchantBalance.getTotalAmount());
            changesFunds.setBeforeFrozenAmount(wxMerchantBalance.getFreezeAmount());
            changesFunds.setAfterAmount(wxMerchantBalance.getTotalAmount());
            changesFunds.setAfterFrozenAmount(wxMerchantBalance.getFreezeAmount() + amount);
            log.info("[{}]订单预扣,[{}]开始修改资金变动", reqVo.getPlatTrxNo(), logKey);
            changesFundsBiz.insert(changesFunds);
            log.info("[{}]订单预扣,[{}]资金变动结束", reqVo.getPlatTrxNo(), logKey);

            wxMerchantBalance.setFreezeAmount(changesFunds.getAfterFrozenAmount());
            wxMerchantBalanceBiz.update(wxMerchantBalance);
            log.info("[{}]订单预扣,[商户号：{}，冻结金额:]==>冻结金额增加:{}", reqVo.getPlatTrxNo(), reqVo.getEmployerNo(), wxMerchantBalance.getFreezeAmount());


            platformTransactionManager.commit(transaction);

            //exception - > rollback
            //1、请求没过去 -> 回滚 ->对的
            //2、请求过去了 -> 回滚 ->错的
        } catch (DuplicateKeyException e) {
            log.info("[{}]订单预扣，{}===>唯一键重复，跳过", reqVo.getPlatTrxNo(), logKey);
            platformTransactionManager.rollback(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }

        try {
            payRespVo = payBankFacade.pay(reqVo);
        } catch (BizException e) {
            refundAmount(reqVo);
            throw e;
        }


        //todo
//        catch (WxRepBizException e){
//            log.info("[开始发放: {}]==>发放异常，判断是否重试，isRetry:{}",reqVo.getPlatTrxNo(),e.isRetry());
//            //微信需要重试的异常状态
//            if (e.isRetry()){
//                log.info("[发放异常: {}]==>重试",reqVo.getPlatTrxNo());
//                throw new RuntimeException(e.getErrMsg());
//            }else{
//                log.info("[发放异常: {}]==>退款",reqVo.getPlatTrxNo());
//                refundAmount(reqVo);
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(e.getErrMsg());
//            }
//        }
        wxNotifyBiz.send(reqVo);
        return payRespVo;
    }

    private void refundAmount(PayReqVo reqVo) {
        log.info("[微信请求异常: {}]==>开始退款", reqVo.getPlatTrxNo());
        String logKey = WxUtil.getLogKey(reqVo.getPlatTrxNo(), WxAmountChangeLogTypeEnum.REFUND.getValue(), MerchantTypeEnum.EMPLOYER);
        ChangesFunds changesFunds = new ChangesFunds();
        changesFunds.setLogKey(logKey);
        changesFunds.setMchNo(reqVo.getEmployerNo());
        changesFunds.setMchName(reqVo.getMchName());
        changesFunds.setMainstayNo(reqVo.getMainstayNo());
        changesFunds.setMainstayName(reqVo.getRealPayerName());
        changesFunds.setPlatTrxNo(reqVo.getPlatTrxNo());
        changesFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        changesFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.REFUND.getValue());
        changesFunds.setAmount(0L);
        changesFunds.setFrozenAmount(-AmountUtil.changeToFen(reqVo.getTotalAmount()));
        changesFunds.setCreateTime(new Date());
        wxMerchantBalanceBiz.changeAmount(changesFunds);
    }


    private Long getAmount(PayReqVo reqVo) {
        WxMerchantBalance wxMerchantBalance = new WxMerchantBalance();
        wxMerchantBalance.setMchNo(reqVo.getEmployerNo());
        wxMerchantBalance.setMainstayNo(reqVo.getMainstayNo());
        return wxMerchantBalanceBiz.getAmount(wxMerchantBalance);
    }


    private ChangesFunds buildChangesFunds(PayReqVo reqVo, String logKey) {

        ChangesFunds changesFunds = new ChangesFunds();
        changesFunds.setLogKey(logKey);
        changesFunds.setMchNo(reqVo.getEmployerNo());
        changesFunds.setMchName(reqVo.getMchName());
        changesFunds.setMainstayNo(reqVo.getMainstayNo());
        changesFunds.setMainstayName(reqVo.getRealPayerName());
        changesFunds.setPlatTrxNo(reqVo.getPlatTrxNo());
        changesFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        changesFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.FROZEN.getValue());
        changesFunds.setFrozenAmount(AmountUtil.changeToFen(reqVo.getTotalAmount()));
        changesFunds.setCreateTime(new Date());
        return changesFunds;
    }
}