package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.OrderItemFail;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.vo.AuthInfoVo;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.OrderItemFailBiz;
import lombok.RequiredArgsConstructor;
import org.apache.catalina.util.ParameterMap;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单明细表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderItemImpl implements OrderItemFacade {

    private final OrderItemBiz biz;

    private final OrderItemFailBiz orderItemFailBiz;

    @Override
    public void batchInsert(List<OrderItem> itemList){
        biz.batchInsert(itemList);
    }

    @Override
    public PageResult<List<OrderItem>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap,pageParam);
    }

    @Override
    public OrderItem getByPlatTrxNo(String platTrxNo) {
        return biz.getByPlatTrxNo(platTrxNo);
    }

    @Override
    public Long countOrderItem(Map<String, Object> paramMap) {
        return biz.countOrderItem(paramMap);
    }

    @Override
    public OrderItemSumBo sumOrderItem(Map<String, Object> paramMap) {
        return biz.sumOrderItem(paramMap);
    }

    @Override
    public Long countOrderItemByMchOrderNo(String employerNo,String mchOrderNo) {
        Map<String, Object> param = new ParameterMap<>();
        param.put("employerNo", employerNo);
        param.put("mchOrderNo", mchOrderNo);
        return biz.countOrderItem(param);
    }


    final private OrderItemFailBiz failBiz;

    @Override
    public void save(OrderItemFail itemFail) {
        failBiz.save(itemFail);
    }

    @Override
    public List<OrderItemFail> getByPlatBatchNo(String platBatchNo) {
        return failBiz.getByPlatBatchNo(platBatchNo);
    }

    @Override
    public PageResult<List<OrderItemFail>> pageByPlatBatchNo(String platBatchNo, PageParam pageParam) {
        return failBiz.pageByPlatBatchNo(platBatchNo, pageParam);
    }

    @Override
    public Long countOrder(Map<String, Object> param) {
        return biz.countOrder(param);
    }

    @Override
    public Map<String, BigDecimal> mapAmountWithMch(Map<String, Object> paramMap) {
        return biz.mapAmountWithMch(paramMap);
    }

    @Override
    public BigDecimal sumOrderItemWaitInvoiceAmount(Map<String, Object> paramMap) {
        return biz.sumOrderItemWaitInvoiceAmount(paramMap);
    }

    @Override
    public AuthInfoVo getAuthInfo(OrderItem orderItem) {
        return biz.getAuthInfo(orderItem);
    }

    @Override
    public void authInit(String platBatchNo) {
        biz.authInit(platBatchNo);
    }

    @Override
    public void update(OrderItem item) {
        biz.update(item);
    }

    @Override
    public void rejectOrderItem(String platTrxNo, String loginName) {
        orderItemFailBiz.rejectOrderItem(platTrxNo,loginName);
    }

    @Override
    public OrderItem getByMchOrderNo(String mchOrderNo) {
        return biz.getByMchOrderNo(mchOrderNo);
    }

    @Override
    public List<OrderItem> listBy(Map<String, Object> paramMap) {
        return biz.listByParam(paramMap);
    }
}
