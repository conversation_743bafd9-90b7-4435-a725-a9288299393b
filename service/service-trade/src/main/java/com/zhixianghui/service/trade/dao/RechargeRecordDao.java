package com.zhixianghui.service.trade.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import org.springframework.stereotype.Repository;

import java.util.Map;
@Repository

public class RechargeRecordDao extends MyBatisDao<RechargeRecord,Long> {

    public Map<String, Object> countRechargeAmount(Map<String, Object> paramMap){
        return this.getSqlSession().selectOne(this.fillSqlId("countRechargeAmount"),paramMap);
    }

    public Map<String, Object> sumCmbRechargeAmt(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(this.fillSqlId("sumCmbRechargeAmt"), paramMap);
    }

    public Map<String, Object> sumRechargeRecord(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(this.fillSqlId("sumRechargeRecord"), paramMap);
    }

    public boolean isRechargeRecordExist(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(this.fillSqlId("isRechargeRecordExist"), paramMap) != null;
    }
}
