package com.zhixianghui.service.trade.process;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.SignAccountStatusEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.facade.banklink.service.sign.ChannelSignFacade;
import com.zhixianghui.facade.banklink.utils.esign.ESignHelpUtil;
import com.zhixianghui.facade.banklink.utils.esign.HeaderConstant;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDateVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.SignCreatePersonReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.SignCreatePersonResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.facade.banklink.vo.sign.signAuth.SignAuthReqVo;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.dto.FlowSignDto;
import com.zhixianghui.service.trade.config.RetryConfig;
import com.zhixianghui.service.trade.enums.TemplateFileStatusEnum;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.SIGN_ACCOUNT_SEQ;

/**
 * <AUTHOR>
 * @date 2022/4/18 16:06
 */
@Service
public class FlowSignBiz {
    private static final Logger LOGGER = LoggerFactory.getLogger(FlowSignBiz.class);
    public static final String LOG_PREFIX = "线上签约流程-";
    public static final String CONTRACT_NAME = "服务履约单";
    public static final String SIGN_A = "甲方签署区";
    public static final String SIGN_B = "乙方签署区";
    public static final String[] FILE_SUFFIX = new String[]{".pdf", ".doc", ".docx"};
    private static final String TEMPLATE_FILE_PATH = "/tmp/";
    public static final int RETRY = 5;
    public static final long INITIAL_SLEEP_TIME = 3;
    public static final long INCREMENT = 5;

    @Reference
    private ChannelSignFacade channelSignFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private MerchantEmployerMainFacade employerMainFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Autowired
    private FastdfsClient fastdfsClient;
    @Value("${sign.flow.signNotifyUrl}")
    private String signNotifyUrl;
    @Value("${sign.a}")
    private Boolean signA;
    @Value("${sign.b}")
    private Boolean signB;

    public void sign(FlowSignDto flowSignDto) {
        String flag = LOG_PREFIX + flowSignDto.getCommitFlowId() + "-" + flowSignDto.getMerchantNo() + "-" + flowSignDto.getMainstayNo();
        // 获取e签宝账号
        Merchant employer = merchantFacade.getByMchNo(flowSignDto.getMerchantNo());
        if (employer == null) {
            LOGGER.error("[{}] 用工企业不存在", flag);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业不存在");
        }
        // 法人证件号码
        MerchantEmployerMain employerMain = employerMainFacade.getByMchNo(employer.getMchNo());
        if (employerMain == null || StringUtils.isBlank(employerMain.getCertificateNumber())) {
            LOGGER.error("[{}] 用工企业法人证件号码不存在", flag);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业法人证件号码不存在");
        }
//        if (employer.getJsonEntity() == null || StringUtils.isAnyBlank(
//                employer.getJsonEntity().getSignAccountId(), employer.getJsonEntity().getSignOrgId())) {
//            createSignAccount(employerMain, employer, flag);
//            merchantFacade.update(employer);
//        }

        Merchant mainstay = merchantFacade.getByMchNo(flowSignDto.getMainstayNo());
        if (mainstay == null) {
            LOGGER.error("[{}] 代征主体不存在", flag);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体不存在");
        }
        MerchantEmployerMain mainstayMain = employerMainFacade.getByMchNo(mainstay.getMchNo());
        if (mainstayMain == null || StringUtils.isBlank(mainstayMain.getCertificateNumber())) {
            LOGGER.error("[{}] 代征主体法人证件号码不存在", flag);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体法人证件号码不存在");
        }
//        if (mainstay.getJsonEntity() == null || StringUtils.isAnyBlank(
//                mainstay.getJsonEntity().getSignAccountId(), mainstay.getJsonEntity().getSignOrgId())) {
//            createSignAccount(mainstayMain, mainstay, flag);
//            merchantFacade.update(mainstay);
//        }

        // 创建模板
        InputStream inputStream = fastdfsClient.downloadFile(flowSignDto.getFileUrl());
        String fileName = flowSignDto.getFileUrl().substring(flowSignDto.getFileUrl().lastIndexOf("/") + 1);
        File file = FileUtils.createFile(TEMPLATE_FILE_PATH + fileName);
        try {
            org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
        } catch (IOException e) {
            LOGGER.error("[{}] 下载合同出错", flag, e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("下载合同出错");
        }
        String templateId = uploadFile(file, flag);
//        try {
//            existTemplate(new TemplateStatus(templateId), flag);
//        } catch (ExecutionException | RetryException e) {
//            LOGGER.error("[{}] 模板不存在", flag, e);
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询不到合同模板");
//        }
        List<StructComponent> list = new ArrayList<>();
        if (signA) {
            list.add(addSignComponent(templateId, "甲方签署区", flowSignDto, flowSignDto.getXPosA(), flowSignDto.getYPosA()));
        }
        if (signB) {
            list.add(addSignComponent(templateId, "乙方签署区", flowSignDto, flowSignDto.getXPosB(), flowSignDto.getYPosB()));
        }
//        EsignResVo<List<String>> result = channelSignFacade.addTemplate(new TemplateComponentsReqVo(templateId, list));
//        if(!Objects.equals(result.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
//            LOGGER.error("[{}] 请求参数: {}-{} 上传组件失败: {}", flag, templateId, JSONObject.toJSONString(list), JSONObject.toJSONString(result));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝上传组件失败");
//        }
        EsignResVo<CreateByFileResDateVo> signResult = handle(list, fileName, templateId, employer, mainstay);
        if(!Objects.equals(signResult.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
            LOGGER.error("[{}] 请求参数: {} 签约失败: {}", flag, templateId, JSONObject.toJSONString(signResult));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝上传组件失败");
        }
        // 查询合同链接
        String url = null;
        try {
            url = fetchUrl(signResult.getData().getSignFlowId(),templateId, flag);
        } catch (Exception e) {
            LOGGER.error("[{}] 请求参数: {} 查询合同失败: {}", flag, signResult.getData().getSignFlowId(), e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "-查询合同失败");
        }
        if (StringUtils.isBlank(url)) {
            LOGGER.error("[{}] 请求参数: {}-{} 合同链接为空", flag, templateId, signResult.getData().getSignFlowId());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "合同链接为空");
        }
        byte[] fileArray = new byte[0];
        try {
            fileArray = FileUtils.download(url);
        } catch (Exception e) {
            LOGGER.error("[{}] 请求参数: {}-{} 合同下载失败", flag, templateId, url, e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "合同下载失败:" + url);
        }
        String fileUrl = fastdfsClient.uploadFile(fileArray, CONTRACT_NAME + FILE_SUFFIX[0]);
        FlowSignDto msg = new FlowSignDto().setFileUrl(fileUrl).setCommitFlowId(flowSignDto.getCommitFlowId());
        LOGGER.info("[{}] 签约处理成功, 发送消息: {}", flag, JSONObject.toJSONString(msg));
        notifyFacade.sendOne(MessageMsgDest.TOPIC_FETCH_SIGN_URL, NotifyTypeEnum.SIGN_NOTIFY.getValue(), MessageMsgDest.TAG_FETCH_SIGN_URL, JSONObject.toJSONString(msg));
    }

    private String fetchUrl(String flowId,String fileId, String flag) throws ExecutionException, RetryException {
        Retryer<String> retry = new RetryConfig<String>().retryConfig(RETRY, INITIAL_SLEEP_TIME, INCREMENT, TimeUnit.SECONDS);
        //定义请求实现
        Callable<String> callable = () -> {
            DocumentDownloadReqVo documentDownloadReqVo = new DocumentDownloadReqVo(flowId);
            EsignResVo<DocumentDownloadResDataVo> res = channelSignFacade.getDocumentDownloadUrl(documentDownloadReqVo);
            if (res.getData() == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "-e签宝查询合同失败");
            }
            if(Objects.equals(res.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
               return res.getData().getFiles().get(0).getDownloadUrl();
            } else {
                PreviewFileDownloadReqVo previewFileDownloadReqVo = new PreviewFileDownloadReqVo(flowId,fileId);
                EsignResVo<PreviewFileDownloadResDataVo> previewDownloadUrl = channelSignFacade.getPreviewDownloadUrl(previewFileDownloadReqVo);
                if (previewDownloadUrl.getData() == null) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "-e签宝查询合同失败");
                }
                if(Objects.equals(previewDownloadUrl.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
                    return previewDownloadUrl.getData().getFileDownloadUrl();
                }
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝查询合同失败");
            }
        };
        return retry.call(callable);
    }

    private EsignResVo<CreateByFileResDateVo> handle(List<StructComponent> componentList, String fileName, String templateId, Merchant employer, Merchant mainstay) {
//        HashMap<String, String> simpleFormFields = Maps.newHashMap();
        List<CreateFileByTemplateReqVoV3.Components> simpleFormFields = new ArrayList<>();
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put("代征主体名称", employer.getMchName());
        dataMap.put("用工企业名称", mainstay.getMchName());
        dataMap.put("日期", DateUtil.parseJodaDateTime(new Date()).toString("yyyy/MM/dd"));
        //填充
        componentList.forEach(
                component -> {
                    String data = dataMap.get(component.getContext().getLabel());
                    if (data != null) {
                        simpleFormFields.add(new CreateFileByTemplateReqVoV3.Components().setComponentId(component.getId()).setComponentValue(data));
                    }
                }
        );
        CreateFileByTemplateReqVoV3 createFileByTemplateReqVo = new CreateFileByTemplateReqVoV3(fileName, templateId, simpleFormFields);
        EsignResVo<CreateFileByTemplateResDataVoV3> createFileByTemplateResDataVoEsignResVo = channelSignFacade.createFileByTemplate(createFileByTemplateReqVo);
        String fileId = createFileByTemplateResDataVoEsignResVo.getData().getFileId();

        //1、配置文档
        ArrayList<Doc> docs = new ArrayList<>();
        docs.add(new Doc().setFileId(fileId));
        //2、配置流程
        FlowInfo flowInfo =  new FlowInfo()
                .setBusinessScene(CONTRACT_NAME)
                //启用自动归档
                .setAutoArchive(true)
                .setAutoInitiate(true)
                .setFlowConfigInfo(new FlowConfigInfo()
                                .setNoticeDeveloperUrl(signNotifyUrl)
//                            .setRedirectUrl("http://www.baidu.com")
                );
        EsignResVo<SignTemplateResDataVoV3> templateResDataVoEsignResVo = channelSignFacade.getSignTemplate(new SignTemplateReqVo(templateId));
        //配置签署人并指定相应区域
        ArrayList<CreateByFileReqVo.Signer> signers = new ArrayList<>();
        if (signA) {
            signers.add(createSigner(templateResDataVoEsignResVo, employer, SIGN_A, fileId));
        }
        if (signB) {
            signers.add(createSigner(templateResDataVoEsignResVo, mainstay, SIGN_B, fileId));
        }



        //1、设置待签署文件信息
        CreateByFileReqVo createFlowOneStepReqVo = new CreateByFileReqVo();
        createFlowOneStepReqVo.setDocs(Collections.singletonList(new CreateByFileReqVo.CreateByFileDoc()
                .setFileId(fileId)));
        //2、签署流程配置项
        createFlowOneStepReqVo.setSignFlowConfig(new CreateByFileReqVo.SignFlowConfig()
                .setSignFlowTitle(flowInfo.getBusinessScene())
                .setAuthConfig(new CreateByFileReqVo.AuthConfig().setPsnAvailableAuthModes(Arrays.asList("PSN_MOBILE3")))
                .setAutoFinish(true)
                .setNotifyUrl(signNotifyUrl));
        //3、配置签署方的信息
        createFlowOneStepReqVo.setSigners(signers);

        EsignResVo<CreateByFileResDateVo> signResult = channelSignFacade.createByFile(createFlowOneStepReqVo);
//        CreateFlowOneStepReqVo createFlowOneStepReqVo = new CreateFlowOneStepReqVo(docs, flowInfo, signers);
        return signResult;

    }

    private CreateByFileReqVo.Signer createSigner(EsignResVo<SignTemplateResDataVoV3> templateResDataVoEsignResVo, Merchant merchant, String label, String fileId) {
        // 配置签署区域


        List<StructComponentV3> signFile = templateResDataVoEsignResVo.getData().getComponents().stream().filter(
                component-> label.equals(component.getComponentName())
        ).collect(Collectors.toList());
        ArrayList<CreateByFileReqVo.SignFields> orgSignfields = Lists.newArrayList();

        DataDictionary dictionary = dataDictionaryFacade.getDataDictionaryByName("EsignSealsEnum");
        List<DataDictionary.Item> itemList = dictionary.getItemList();

        DataDictionary.Item item = itemList.stream().filter(x -> ObjectUtil.equal(x.getCode(), merchant.getMchNo())).findFirst().orElse(null);

        for (StructComponentV3 structComponent : signFile) {
            ComponentPosition componentPosition = structComponent.getComponentPosition();
            orgSignfields.add(new CreateByFileReqVo.SignFields()
                    .setFileId(fileId)
                    .setNormalSignFieldConfig(new CreateByFileReqVo.NormalSignFieldConfig().setFreeMode(false)
                            .setAutoSign(true)
                            .setAssignedSealId(ObjectUtil.isNotEmpty(item)?item.getFlag():null)
                            .setSignFieldStyle(structComponent.getNormalSignField().getSignFieldStyle())
                            .setSignFieldPosition(new CreateByFileReqVo.SignFieldPosition().setPositionPage(String.valueOf(componentPosition.getComponentPageNum()))
                                    .setPositionX(componentPosition.getComponentPositionX())
                                    .setPositionY(componentPosition.getComponentPositionY()))));//构造企业signfields参数对象,用于后续入参使用,支持链式入参
        }

//        EsignResVo<OrgIdentityInfoResDataVo> resVo = channelSignFacade.getOrgIdentityInfoV3(new OrgIdentityInfoResDateVo().setOrgName(merchant.getMchName()));
//        if (!Objects.equals(resVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(resVo.getMessage());
//        }

        return new CreateByFileReqVo.Signer()
                .setSignerType(1)
//                .setOrgSignerInfo(new CreateByFileReqVo.OrgSignerInfo()
//                        .setOrgId(resVo.getData().getOrgId()))
                .setSignFields(orgSignfields);
    }

//    private void existTemplate(TemplateStatus reqVo, String flag) throws ExecutionException, RetryException {
//        Retryer<Boolean> retry = new RetryConfig<Boolean>().retryConfig(RETRY, INITIAL_SLEEP_TIME, INCREMENT, TimeUnit.SECONDS);
//        //定义请求实现
//        Callable<Boolean> callable = () -> {
//            EsignResVo<TemplateStatusResDataVo> result = channelSignFacade.existTemplate(reqVo);
//            if (result.getData() == null) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "-模板状态查询异常");
//            }
//            if (StringUtils.isNotBlank(result.getData().getTemplateFileStatus()) &&
//                    TemplateFileStatusEnum.PDF.getStatus() == Integer.parseInt(result.getData().getTemplateFileStatus())) {
//                return true;
//            }
//            LOGGER.error("[{}]模板创建未完成 : {}", flag, JSONObject.toJSON(result.getData()));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板创建未完成");
//        };
//        retry.call(callable);
//    }

    public StructComponent addSignComponent(String templateId, String label, FlowSignDto flowSignDto, float x, float y) {
        StructComponent structComponent = new StructComponent();
        structComponent.setType(1);
        StructContext structContext = new StructContext();
        structContext.setLabel(label);
        StructStyle structStyle = new StructStyle();
        structStyle.setWidth(flowSignDto.getWidth());
        structStyle.setHeight(flowSignDto.getHeight());
        structStyle.setFont(1);
        StructPos structPos = new StructPos();
        structPos.setPage(String.valueOf(1));
        structPos.setX(x);
        structPos.setY(y);
        structContext.setStyle(structStyle);
        structContext.setPos(structPos);
        structComponent.setContext(structContext);
        return structComponent;
    }

    private String uploadFile(File file, String flag) {
        String localPath = file.getPath();
        byte[] buffer = FileUtils.fileToByteArray(localPath);
        // 获取请求头
        String content = ESignHelpUtil.getStringContentMD5(localPath);
        CreateSignTemplateReqVo reqVo =  new CreateSignTemplateReqVo(
                content, HeaderConstant.CONTENTTYPE_STREAM.getValue(), file.getName(), true, file.length()
        );
        // 获取文件上传地址
        EsignResVo<CreateSignTemplateResV3DataVo> result = channelSignFacade.createSignTemplate(reqVo);
        if(!Objects.equals(result.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
            LOGGER.error("[{}] 请求参数: {}, e签宝获取文件上传地址失败: {}", flag, JSONObject.toJSONString(reqVo), JSONObject.toJSONString(result));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝获取文件上传地址失败");
        }
        CreateSignTemplateResV3DataVo createSignTemplateResDataVo = result.getData();
        if (StringUtils.isBlank(createSignTemplateResDataVo.getFileUploadUrl())) {
            LOGGER.error("[{}] 请求参数: {}, e签宝获取文件上传地址为空: {}", flag, JSONObject.toJSONString(reqVo), JSONObject.toJSONString(result));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板文件预上传出错, 上传地址为空");
        }
        // 上传
        UploadFileReqVo uploadFileReqVo = new UploadFileReqVo().build(reqVo, localPath);
        boolean success = channelSignFacade.uploadFile(uploadFileReqVo, createSignTemplateResDataVo.getFileUploadUrl(), buffer);
        if (!success) {
            LOGGER.error("[{}] 请求参数: {}, e签宝上传文件出错", flag, JSONObject.toJSONString(uploadFileReqVo));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板文件上传出错");
        }
        return createSignTemplateResDataVo.getFileId();
    }

//    private void createSignAccount(MerchantEmployerMain employerMain, Merchant merchant, String flag) {
//        String legalNo = sequenceFacade.nextRedisId(SIGN_ACCOUNT_SEQ.getPrefix(), SIGN_ACCOUNT_SEQ.getKey(), SIGN_ACCOUNT_SEQ.getWidth());
//        SignCreatePersonReqVo signCreatePersonReqVo = new SignCreatePersonReqVo(legalNo, employerMain.getLegalPersonName(),"CRED_PSN_CH_IDCARD", employerMain.getCertificateNumber());
//        EsignResVo<SignCreatePersonResDataVo> account = channelSignFacade.createPersonByThirdPartyUserId(signCreatePersonReqVo);
//        if(!Objects.equals(account.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
//            LOGGER.error("[{}] 请求参数: {}, 创建法人个人账号失败: {}", JSONObject.toJSONString(signCreatePersonReqVo), JSONObject.toJSONString(account), flag);
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创建法人个人账号失败");
//        }
//        String accountId = account.getData().getAccountId();
//        SignCreateOrganizationReqVo signCreateOrganizationReqVo = new SignCreateOrganizationReqVo(
//                employerMain.getMchNo(), accountId, merchant.getMchName(),"CRED_ORG_USCC", employerMain.getCertificateNumber()
//        );
//        EsignResVo<SignCreateOrganizationResDataVo> res3 = channelSignFacade.createOrganization(signCreateOrganizationReqVo);
//        if(!Objects.equals(res3.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
//            LOGGER.error("[{}] 请求参数: {}, 创建企业账号失败: {}", JSONObject.toJSONString(signCreateOrganizationReqVo), JSONObject.toJSONString(res3), flag);
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创建企业账号失败");
//        }
//        String orgId = res3.getData().getOrgId();
//        Merchant.JsonEntity jsonEntity = new Merchant.JsonEntity().setLegalNo(legalNo).setSignAccountId(accountId).setSignOrgId(orgId).setSignAccountStatus(SignAccountStatusEnum.ACTIVATE.getValue());;
//        merchant.setJsonEntity(jsonEntity);
//        // 允许静默签约
//        SignAuthReqVo signAuthReqVo = new SignAuthReqVo(accountId);
//        EsignResVo<Boolean> signAuthRes = channelSignFacade.signAuth(signAuthReqVo);
//        if(!Objects.equals(signAuthRes.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
//            LOGGER.error("[{}] 请求参数: {}, 设置静默签约失败: {}", JSONObject.toJSONString(signAuthReqVo), JSONObject.toJSONString(signAuthRes), flag);
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("设置静默签约失败");
//        }
//    }
}
