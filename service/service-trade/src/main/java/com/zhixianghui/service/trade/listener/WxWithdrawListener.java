package com.zhixianghui.service.trade.listener;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.service.trade.pay.wx.biz.WxWithdrawBiz;
import com.zhixianghui.service.trade.process.WithdrawBiz;
import com.zhixianghui.starter.comp.component.RedisLock;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName WxWithdrawListener
 * @Description TODO
 * @Date 2022/1/7 10:29
 */
@Component
@Slf4j
public class WxWithdrawListener {
    @Autowired
    private WxWithdrawBiz wxWithdrawBiz;

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Autowired
    private RedisLock redisLock;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_WITHDRAW, selectorExpression = MessageMsgDest.TAG_WX_WITHDRAW, consumeThreadMax = 3, consumerGroup = "wxWithDrawConsume")
    public class WxWithdrawReqListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            WithdrawRecord withdrawRecord = JsonUtil.toBean(jsonParam,WithdrawRecord.class);
            //查询供应商支付账号
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(
                    withdrawRecord.getMainstayNo(), ChannelNoEnum.WXPAY.name());
            String key = WxUtil.getRedisLockKey(null,withdrawRecord.getMainstayNo(), MerchantTypeEnum.MAINSTAY.getValue());
            log.info("供应商提现，供应商编号：[{}]，提现单号：[{}]", withdrawRecord.getMainstayNo(), withdrawRecord.getWithdrawNo());
            RLock rLock = redisLock.tryLock(key, WxUtil.LOCK_WAIT_TIME, WxUtil.LOCK_LEASE_TIME);
            if (rLock == null){
                log.error("供应商编号：[{}]，提现单号：[{}]，供应商提现获取锁失败，抛出异常，等待消息重试",withdrawRecord.getMainstayNo(),withdrawRecord.getWithdrawNo());
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("获取失败异常");
            }

            try {
                wxWithdrawBiz.wxWithdraw(withdrawRecord,mainstayChannelRelation,rLock);
            }finally {
                redisLock.unlock(rLock);
            }
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_WITHDRAW_STATUS,selectorExpression = MessageMsgDest.TAG_WX_WITHDRAW_STATUS,consumeThreadMax = 3,consumerGroup = "wxWithDrawStatusConsumer")
    public class WxWithDrawStatusListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            WithdrawRecord withdrawRecord = JsonUtil.toBean(jsonParam,WithdrawRecord.class);
            wxWithdrawBiz.getWxWithdrawStatus(withdrawRecord);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_WITHDRAW_TIMER,consumeThreadMax = 1,consumerGroup = "wxWithdrawTimerGroup")
    public class WxWithDrawTimerListener extends TaskRocketMQListener<JSONObject> {

        @Override
        public void runTask(JSONObject jsonParam) {
            wxWithdrawBiz.withdrawTimer();
        }
    }
}
