package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.entity.WechatInfo;
import com.zhixianghui.facade.trade.entity.WechatUserInfo;
import com.zhixianghui.facade.trade.service.WeChatUserFacade;
import com.zhixianghui.facade.trade.vo.WxBindMobileReqVo;
import com.zhixianghui.service.trade.biz.WeChatUserBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/2/14 10:55
 */
@Service
public class WeChatUserImpl implements WeChatUserFacade {
    @Autowired
    private WeChatUserBiz weChatUserBiz;

    @Override
    public WechatUserInfo exist(String userNo) {
        return weChatUserBiz.selectByUserNo(userNo);
    }

    @Override
    public UserInfo getUserInfoByPhone(String phone) {
       return weChatUserBiz.getIdCardByPhone(phone);
    }

    @Override
    public String getAppCode(Long jobId) {
        return weChatUserBiz.getAppQRCode(jobId);
    }

    @Override
    public WechatUserInfo getOrRegister(String phone, String code) {
        return weChatUserBiz.getOrRegister(phone,code);
    }

    @Override
    public String getPhoneByOpenIdAndAppId(String openId, String appId) {
        return weChatUserBiz.getPhoneByOpenIdAndAppId(openId, appId);
    }

    @Override
    public WechatUserInfo register(WxBindMobileReqVo wxBindMobileReqVo) {
        return weChatUserBiz.register(wxBindMobileReqVo);
    }

    @Override
    public WechatInfo getMiniUserByOpenIdAndAppId(String openId, String appId) {
        return weChatUserBiz.getMiniUserByOpenIdAndAppId(openId, appId);
    }
}
