package com.zhixianghui.service.trade.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/14 14:41
 */
@Data
public class Code2SessionReqVo implements Serializable {

    private String appid;
    private String secret;
    private String js_code;
    private String grant_type = "authorization_code";

    public Code2SessionReqVo(String appId, String secret, String code) {
        this.appid = appId;
        this.secret = secret;
        this.js_code = code;
    }


    public Map<String, String> toMap() {
        Map<String, String> map = new HashMap<>();
        map.put("appid", this.appid);
        map.put("secret", this.secret);
        map.put("js_code", this.js_code);
        map.put("grant_type", this.grant_type);
        return map;
    }
}
