package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.joinpay.JoinPayFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.trade.constant.JoinPayConstant;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.vo.ResponseVo;
import com.zhixianghui.service.trade.process.WithdrawBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月06日 15:43:00
 */
@Service
@Slf4j
public class WithdrawListenerBiz {

    @Autowired
    private WithdrawBiz withdrawBiz;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private JoinPayFacade joinPayFacade;

    public String bankCallback(Map<String, Object> map) {
        log.info("提现请求回调内容 : {}", JSONObject.toJSONString(map));
        updateOrder(map);
        return JSONObject.toJSONString(new ResponseVo());
    }

    private void updateOrder(Map<String, Object> map){
        String dataStr = JsonUtil.toString(map.get("data"));
        Map<String,Object> data=JsonUtil.toBean(dataStr,Map.class);
        if(data.get("order_status").equals(JoinPayConstant.SUCCESS_ORDER_CODE)){
            withdrawBiz.updateWithdrawRecord(String.valueOf(data.get("mch_order_no")), SuccessFailEnum.SUCCESS,"");
        }else{
            withdrawBiz.updateWithdrawRecord(
                    String.valueOf(data.get("mch_order_no")), SuccessFailEnum.FAIL,
                    String.valueOf(data.get("biz_msg")));
        }
    }
}