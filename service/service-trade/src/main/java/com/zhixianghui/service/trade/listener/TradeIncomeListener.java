package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.trade.biz.AcIncomeRecordBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName 交易入账接收MQ消息
 * @Description TODO
 * @Date 2022/11/3 11:56
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_INCOME_ASYNC,
        selectorExpression = MessageMsgDest.TAG_INCOME_NOTIFY, consumeThreadMax = 3,
        consumerGroup = "tradeIncomeConsumer")
public class TradeIncomeListener extends BaseRocketMQListener<String> {

    @Autowired
    private AcIncomeRecordBiz acIncomeRecordBiz;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    /***
     * 该流程暂不使用，走公共的充值流程
     * @param jsonParam
     */
    @Override
    public void consumeMessage(String jsonParam) {
//        log.info("处理充值成功后的入金流程，接收到的参数：" + jsonParam);
//        IncomeResult result = JSONUtil.toBean(jsonParam, IncomeResult.class);
//        if (!"成功".equals(result.getIncome_status())) {
//            log.error("君享汇入账通知单号[{}]状态[{}]不做入账操作。", result.getTrx_no(), result.getIncome_status());
//            return;
//        }

//        acIncomeRecordBiz.handlerAcIncome(result);
    }
}
