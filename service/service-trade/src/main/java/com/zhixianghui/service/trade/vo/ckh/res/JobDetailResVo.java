package com.zhixianghui.service.trade.vo.ckh.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName JobDetailResVo
 * @Description TODO
 * @Date 2022/11/2 13:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class JobDetailResVo extends ApiBizBaseDto {

    /**
     * 任务状态
     */
    private String jobStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime createTime;

    /**
     * 接单模式
     */
    private String acceptMode;

    /**
     * 任务可见性
     */
    private String scope;

    /**
     * 岗位编号
     */
    private String positionNo;

    /**
     * 岗位名称
     */
    private String positionName;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务有效期类型
     */
    private String avalDateType;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDate jobBeginDate;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    private LocalDate jobEndDate;

    /**
     * 工作开始时间
     */
    @JsonFormat(pattern="HH:mm:ss",timezone="GMT+8")
    private LocalTime workBeginTime;

    /**
     * 工作结束时间
     */
    @JsonFormat(pattern="HH:mm:ss",timezone="GMT+8")
    private LocalTime workEndTime;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 任务详细地址
     */
    private String address;

    /**
     * 任务人数
     */
    private Integer workNum;

    /**
     * 性别要求
     */
    private String sex;

    /**
     * 最小年龄
     */
    private Integer minAge;

    /**
     * 最大年龄
     */
    private Integer maxAge;

    /**
     * 学历背景
     */
    private String eduBackground;

    /**
     * 描述
     */
    private String description;

    /**
     * 交付标准
     */
    private String standard;

    /**
     * 任务标签
     */
    private List<String> tag;
}
