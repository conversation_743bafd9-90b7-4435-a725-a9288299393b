package com.zhixianghui.service.trade.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.api.base.utils.SignUtil;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.enums.common.ApiRespCodeEnum;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.service.auth.PayAuthFacade;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.notify.NotifyContent;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.*;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.service.trade.biz.AuthRecordBiz;
import com.zhixianghui.service.trade.biz.MchPlatBatchBiz;
import com.zhixianghui.service.trade.biz.MchPlatTrxNoBiz;
import com.zhixianghui.service.trade.vo.res.NotifyMchResVo;
import com.zhixianghui.service.trade.vo.res.NotifyMchSignResVo;
import com.zhixianghui.service.trade.vo.res.NotifyRechargeResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.AUTH_AGREEMENT_SEQ;
import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.AUTH_RECORD_SEQ;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-06 15:20
 **/
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TradeHelperBiz {
    //http请求10s dubbo设为12s
    @Reference(timeout = 12000)
    private PayAuthFacade payAuthFacade;
    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Value("${auth.simulation}")
    private String simulation;
    @Value("${auth.authcode}")
    private String authcode;

    private final AuthRecordBiz authRecordBiz;
    private final MchPlatBatchBiz mchPlatBatchBiz;
    private final MchPlatTrxNoBiz mchPlatTrxNoBiz;
    private final CacheBiz cacheBiz;

    public void processBankCardAuth(OrderItem item,AuthTypeEnum authTypeEnum) {
        String receiveName = item.getReceiveNameDecrypt();
        String receiveAccountNo = item.getReceiveAccountNoDecrypt();
        String receiveIdCardNo = item.getReceiveIdCardNoDecrypt();

        String receivePhoneNo = StringUtils.isBlank(item.getReceivePhoneNoMd5()) ? null : item.getReceivePhoneNoDecrypt();

        //先判断本地的鉴权表是否有记录
        List<AuthRecord> authRecords = authRecordBiz.listByElementActiveRecordWithType(receiveName,receiveIdCardNo,receiveAccountNo,receivePhoneNo, authTypeEnum);
        if(!ObjectUtils.isEmpty(authRecords)){
            log.info("[受理环节: 明细-{} 本地数据库鉴权成功] ", item.getPlatTrxNo());
            return ;
        }
        //通道鉴权
        AuthRespVo authRespVo;
        String authNo;
        String serialNo;
        try{
            authNo = sequenceFacade.nextRedisId(AUTH_RECORD_SEQ.getPrefix(),
                    AUTH_RECORD_SEQ.getKey(), AUTH_RECORD_SEQ.getWidth());
            log.info("[受理环节: {} 请求汇聚鉴权号:{}] ", item.getPlatTrxNo(),authNo);
            //用户授权协议流水号
            serialNo = sequenceFacade.nextRedisId(AUTH_AGREEMENT_SEQ.getPrefix(),
                    AUTH_AGREEMENT_SEQ.getKey(),AUTH_AGREEMENT_SEQ.getWidth());
            AuthReqVo authReqVo = new AuthReqVo();
            authReqVo.setBankOrderNo(authNo);
            authReqVo.setAuthType(authTypeEnum.getValue());
            authReqVo.setName(receiveName);
            authReqVo.setBankAccountNo(receiveAccountNo);
            authReqVo.setIdCardNo(receiveIdCardNo);
            authReqVo.setSerialNo(serialNo);
            authReqVo.setPhoneNo(receivePhoneNo);
            authRespVo = payAuthFacade.auth(authReqVo);
        } catch (Exception e){
            log.info("[{}-{}]鉴权异常，忽略:", item.getPlatTrxNo(), receiveIdCardNo, e);
            throw ApiExceptions.API_TRADE_AUTH_EXCEPTION.newWithErrMsg("鉴权未知异常");
        }
        Integer authStatus = authRespVo.getAuthStatus();
        if (StringUtils.equals("true", simulation)) {
            authStatus = Integer.valueOf(this.authcode);
            authRespVo.setBizDesc("模拟["+this.authcode+"]异常");
        }

        // 成功、未知通过此次鉴权 | 成功的才记录,失败未知不记录
        if(Objects.equals(authStatus, BankAuthStatusEnum.SUCCESS.getValue())){
            log.info("[受理环节: 明细-{} 鉴权成功] ", item.getPlatTrxNo());
            // 异步保存，失败也不影响业务
            AuthRecord record =  new AuthRecord();
            record.setAuthNo(authNo);
            record.setCreateTime(new Date());
            record.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(receiveIdCardNo));
            record.setReceiveAccountNoMd5(MD5Util.getMixMd5Str(receiveAccountNo));
            record.setReceiveNameMd5(MD5Util.getMixMd5Str(receiveName));
            record.setAuthType(authTypeEnum.getValue());
            record.setProtocolVersion(authRespVo.getProtocolVersion());
            record.setProtocolNo(serialNo);
            record.setReceivePhoneNoMd5(MD5Util.getMixMd5Str(receivePhoneNo));
            record.setAuthChannel(authRespVo.getChannelNo());
            authRecordBiz.insertAsync(record);
        }else if(Objects.equals(authStatus,BankAuthStatusEnum.SYSTEM_ERROR.getValue())||Objects.equals(authStatus,BankAuthStatusEnum.UN_KNOW.getValue())){
            throw ApiExceptions.API_TRADE_AUTH_EXCEPTION.newWithErrMsg(authRespVo.getBizDesc());
        }
        else if(Objects.equals(authStatus, BankAuthStatusEnum.FAIL.getValue())){
            log.info("[受理环节: 明细-{}] 鉴权失败，biz_code: {}, biz_desc:{}", item.getPlatTrxNo(), authRespVo.getBizCode(),authRespVo.getBizDesc());
            throw ApiExceptions.API_TRADE_AUTH_FAIL.newWithErrMsg(authRespVo.getBizDesc());
        }
    }

    @Deprecated
    public void alipayAuth(OrderItem item) {
        String receiveName = item.getReceiveNameDecrypt();
        String receiveAccountNo = item.getReceiveAccountNoDecrypt();
        String receiveIdCardNo = item.getReceiveIdCardNoDecrypt();
        final String receivePhoneNo = item.getReceivePhoneNo();

        //先判断本地的鉴权表是否有记录
        List<AuthRecord> authRecords = authRecordBiz.listByElementActiveRecordWithType(receiveName,receiveIdCardNo,receiveAccountNo,null, AuthTypeEnum.IDCARD_NAME_BANKCARD);
        if(!ObjectUtils.isEmpty(authRecords)){
            log.info("[受理环节: 明细-{} 本地数据库鉴权成功] ", item.getPlatTrxNo());
            return ;
        }
        //通道鉴权
        AuthRespVo authRespVo;
        String authNo;
        try{
            authNo = sequenceFacade.nextRedisId(AUTH_RECORD_SEQ.getPrefix(),
                    AUTH_RECORD_SEQ.getKey(), AUTH_RECORD_SEQ.getWidth());
            log.info("[受理环节: {} 请求支付宝鉴权号:{}] ", item.getPlatTrxNo(),authNo);
            AuthReqVo authReqVo = new AuthReqVo();
            authReqVo.setBankOrderNo(authNo);
            authReqVo.setAuthType(AuthTypeEnum.IDCARD_NAME_BANKCARD.getValue());
            authReqVo.setName(receiveName);
            authReqVo.setBankAccountNo(receiveAccountNo);
            authReqVo.setIdCardNo(receiveIdCardNo);
            authReqVo.setPhoneNo(receivePhoneNo);
            authRespVo = alipayFacade.auth(authReqVo);
        } catch (Exception e){
            log.info("[{}-{}]鉴权异常，忽略:", item.getPlatTrxNo(), receiveIdCardNo, e);
            return;
        }
        Integer authStatus = authRespVo.getAuthStatus();

        // 成功、未知通过此次鉴权 | 成功的才记录,失败未知不记录
        if(Objects.equals(authStatus, BankAuthStatusEnum.SUCCESS.getValue())){
            log.info("[受理环节: 明细-{} 鉴权成功] ", item.getPlatTrxNo());
            // 异步保存，失败也不影响业务
            AuthRecord record =  new AuthRecord();
            record.setAuthNo(authNo);
            record.setCreateTime(new Date());
            record.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(receiveIdCardNo));
            record.setReceiveAccountNoMd5(MD5Util.getMixMd5Str(receiveAccountNo));
            record.setReceiveNameMd5(MD5Util.getMixMd5Str(receiveName));
            record.setAuthType(AuthTypeEnum.IDCARD_NAME_BANKCARD.getValue());
            record.setAuthChannel(authRespVo.getChannelNo());
            authRecordBiz.insertAsync(record);
        } else if(Objects.equals(authStatus, BankAuthStatusEnum.FAIL.getValue())){
            log.info("[受理环节: 明细-{}] 鉴权失败，biz_code: {}, biz_desc:{}", item.getPlatTrxNo(), authRespVo.getBizCode(),authRespVo.getBizDesc());
            throw ApiExceptions.API_TRADE_AUTH_FAIL.newWithErrMsg("三要素不匹配");
        }
    }

    /**
     * 订单查询---查询条件填充分库分表字段
     * @param paramMap 查询条件paramMap
     */
    public void putOrderCreateDateToMap(Map<String,Object> paramMap){
        //是否有包含流水号
        String platBatchNo = (String)paramMap.get("platBatchNo");
        String mchBatchNo = (String)paramMap.get("mchBatchNo");
        String employerNo = (String)paramMap.get("employerNo");
        if(StringUtils.isNotEmpty(platBatchNo)){
            try {
                Date date = TrxNoDateUtil.getDateFromTrxNo(platBatchNo);
                paramMap.put("createDate", date);
                return;
            } catch (Exception e) {
                log.error("platBatchNo:{} 有误，无法解析日期 ",platBatchNo);
            }
        }
        if(StringUtils.isNotEmpty(mchBatchNo)){
            //查询映射表 如果商户的订单号重复 会有多个 用in查询
            List<MchPlatBatch> mchPlatBatchList = mchPlatBatchBiz.listPlatBatchNoByMchBatchNo(mchBatchNo,employerNo);
            if(!CollectionUtils.isEmpty(mchPlatBatchList)){
                try {
                    if(mchPlatBatchList.size() == 1){
                        platBatchNo = mchPlatBatchList.get(0).getPlatBatchNo();
                        Date date = TrxNoDateUtil.getDateFromTrxNo(platBatchNo);
                        paramMap.put("platBatchNo", platBatchNo);
                        paramMap.put("createDate", date);
                    }else {
                        List<Date> createDateList = Lists.newArrayList();
                        mchPlatBatchList.forEach(
                                mchPlatBatch -> createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatBatch.getPlatBatchNo()))
                        );
                        paramMap.put("createDateList",createDateList);
                    }
                    return;
                } catch (Exception e) {
                    log.error("platBatchNo:{} 有误，无法解析日期 ",platBatchNo);
                }
            }else{
                //有商户批次号且没有找到 说明没有此单
                paramMap.put(TradeConstant.ORDER_NOT_EXIST_FLAG,"true");
                return;
            }
        }
        TrxNoDateUtil.putCreateDateByMapDate(paramMap);
    }


    public  void putCreateTimeRangeToMap(Map<String,Object> paramMap){
        if (paramMap == null) {
            return;
        }

        Object createBeginDateObj = paramMap.get("createBeginDate");
        Object createEndDateObj = paramMap.get("createEndDate");
        Object completeBeginDateObj = paramMap.get("completeBeginDate");
        Object completeEndDateObj = paramMap.get("completeEndDate");

        Date createBeginDate = createBeginDateObj == null ? null : createBeginDateObj instanceof Date ? (Date) createBeginDateObj : cn.hutool.core.date.DateUtil.parse((String) createBeginDateObj).toJdkDate();
        Date createEndDate = createEndDateObj == null ? null : createEndDateObj instanceof Date ? (Date) createEndDateObj : cn.hutool.core.date.DateUtil.parse((String) createEndDateObj).toJdkDate();
        Date completeBeginDate = completeBeginDateObj == null ? null : completeBeginDateObj instanceof Date ? (Date) completeBeginDateObj : cn.hutool.core.date.DateUtil.parse((String) completeBeginDateObj).toJdkDate();
        Date completeEndDate = completeEndDateObj == null ? null : completeEndDateObj instanceof Date ? (Date) completeEndDateObj : cn.hutool.core.date.DateUtil.parse((String) completeEndDateObj).toJdkDate();

        if(completeBeginDate!= null && completeEndDate!=null){
            if (createBeginDate == null && createEndDate == null) {
                // 加入创建时间以优化查询数组，创建时间范围为完成时间往前提3个月
                paramMap.put("createBeginDate", DateUtil.addMonth(completeBeginDate, -3));
                paramMap.put("createEndDate", completeEndDate);
            }
        }
    }

    /**
     * 订单明细查询---查询条件填充分库分表字段
     * @param paramMap 查询条件paramMap
     */
    public void putOrderItemCreateDateToMap(Map<String, Object> paramMap) {
        //是否包含订单明细编号
        String platTrxNo = (String)paramMap.get("platTrxNo");
        String mchOrderNo = (String)paramMap.get("mchOrderNo");
        String employerNo = (String)paramMap.get("employerNo");
        if(StringUtils.isNotEmpty(platTrxNo)){
            try {
                Date date = TrxNoDateUtil.getDateFromTrxNo(platTrxNo);
                paramMap.put("createDate", date);
                return;
            } catch (Exception e) {
                log.error("platTrxNo:{} 有误，无法解析日期 ",platTrxNo);
            }
        }
        if(StringUtils.isNotEmpty(mchOrderNo)){
            //查询映射表
            List<MchPlatTrxNo> mchPlatTrxNoList =mchPlatTrxNoBiz.listPlatTrxNoByMchOrderNo(mchOrderNo,employerNo);
            if(!CollectionUtils.isEmpty(mchPlatTrxNoList)){
                try {
                    if(mchPlatTrxNoList.size() == 1){
                        platTrxNo = mchPlatTrxNoList.get(0).getPlatTrxNo();
                        Date date = TrxNoDateUtil.getDateFromTrxNo(platTrxNo);
                        paramMap.put("platTrxNo", platTrxNo);
                        paramMap.put("createDate", date);
                    }else {
                        List<Date> createDateList = Lists.newArrayList();
                        mchPlatTrxNoList.forEach(
                                mchPlatTrxNo -> createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatTrxNo.getPlatTrxNo()))
                        );
                        paramMap.put("createDateList",createDateList);
                    }
                    return;
                } catch (Exception e) {
                    log.error("platTrxNo:{} 有误，无法解析日期 ",platTrxNo);
                }
            }else{
                //有商户批次号且没有找到 说明没有此单
                paramMap.put(TradeConstant.ORDER_NOT_EXIST_FLAG,"true");
                return;
            }
        }

        //是否有包含批次流水号
        String platBatchNo = (String)paramMap.get("platBatchNo");
        String mchBatchNo = (String)paramMap.get("mchBatchNo");
        if(StringUtils.isNotEmpty(platBatchNo)){
            //统计明细 查跨天
            try {
                Date beginDate = TrxNoDateUtil.getDateFromTrxNo(platBatchNo);
                Date endDate = TrxNoDateUtil.getDateFromTrxNoAddDay(platBatchNo,1);
                paramMap.put("beginDate", beginDate);
                paramMap.put("endDate", endDate);
                return;
            } catch (Exception e) {
                log.error("platBatchNo:{} 有误，无法解析日期 ",platBatchNo);
            }
        }
        if(StringUtils.isNotEmpty(mchBatchNo)){
            //查询映射表
            List<MchPlatBatch> mchPlatBatchList = mchPlatBatchBiz.listPlatBatchNoByMchBatchNo(mchBatchNo,employerNo);
            if(!CollectionUtils.isEmpty(mchPlatBatchList)){
                //批次找明细 查跨天
                try {
                    if(mchPlatBatchList.size() == 1){
                        platBatchNo = mchPlatBatchList.get(0).getPlatBatchNo();
                        Date beginDate = TrxNoDateUtil.getDateFromTrxNo(platBatchNo);
                        Date endDate = TrxNoDateUtil.getDateFromTrxNoAddDay(platBatchNo,1);
                        paramMap.put("platBatchNo", platBatchNo);
                        paramMap.put("beginDate", beginDate);
                        paramMap.put("endDate", endDate);
                    }else {
                        List<Date> createDateList = Lists.newArrayList();
                        mchPlatBatchList.forEach(
                                mchPlatBatch -> {
                                    createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatBatch.getPlatBatchNo()));
                                    createDateList.add(TrxNoDateUtil.getDateFromTrxNoAddDay(mchPlatBatch.getPlatBatchNo(),1));
                                }
                        );
                        paramMap.put("createDateList",createDateList);
                    }
                    return;
                } catch (Exception e) {
                    log.error("platBatchNo:{} 有误，无法解析日期 ",platBatchNo);
                }
            }else{
                //有商户批次号且没有找到 说明没有此单
                paramMap.put(TradeConstant.ORDER_NOT_EXIST_FLAG,"true");
                return;
            }
        }
        TrxNoDateUtil.putCreateDateByMapDate(paramMap);
    }

    /**
     * 打款流水查询---查询条件填充分库分表字段
     * @param paramMap 查询条件paramMap
     */
    public void putRecordItemCreateDateToMap(Map<String, Object> paramMap) {
        //打款流水号
        String remitPlatTrxNo = (String) paramMap.get("remitPlatTrxNo");
        if(StringUtils.isNotEmpty(remitPlatTrxNo)){
            try{
                Date date = TrxNoDateUtil.getDateFromTrxNo(remitPlatTrxNo);
                paramMap.put("createDate",date);
                return;
            }catch (Exception e){
                log.error("remitPlatTrxNo:{} 有误，无法解析日期 ",remitPlatTrxNo);
            }
        }

        String platTrxNo = (String)paramMap.get("platTrxNo");
        String mchOrderNo = (String)paramMap.get("mchOrderNo");
        String employerNo = (String)paramMap.get("employerNo");
        if(StringUtils.isNotEmpty(platTrxNo)){
            //明细找打款 查三个月
            try {
                Date beginDate = TrxNoDateUtil.getDateFromTrxNo(platTrxNo);
                Date endDate = TrxNoDateUtil.getDateFromTrxNo(platTrxNo,2);
                paramMap.put("beginDate", beginDate);
                paramMap.put("endDate", endDate);
                return;
            } catch (Exception e) {
                log.error("platTrxNo:{} 有误，无法解析日期 ",platTrxNo);
            }
        }
        if(StringUtils.isNotEmpty(mchOrderNo)){
            //查询映射表
            List<MchPlatTrxNo> mchPlatTrxNoList = mchPlatTrxNoBiz.listPlatTrxNoByMchOrderNo(mchOrderNo,employerNo);
            if(!CollectionUtils.isEmpty(mchPlatTrxNoList)){
                try {
                    if(mchPlatTrxNoList.size() == 1){
                        platTrxNo = mchPlatTrxNoList.get(0).getPlatTrxNo();
                        Date beginDate = TrxNoDateUtil.getDateFromTrxNo(platTrxNo);
                        Date endDate = TrxNoDateUtil.getDateFromTrxNo(platTrxNo,2);
                        paramMap.put("platTrxNo", platTrxNo);
                        paramMap.put("beginDate", beginDate);
                        paramMap.put("endDate", endDate);
                    }else {
                        List<Date> createDateList = Lists.newArrayList();
                        mchPlatTrxNoList.forEach(
                                mchPlatTrxNo ->{
                                    createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatTrxNo.getPlatTrxNo()));
                                    createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatTrxNo.getPlatTrxNo(),1));
                                    createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatTrxNo.getPlatTrxNo(),2));
                                }
                        );
                        paramMap.put("createDateList",createDateList);
                    }
                    return;
                } catch (Exception e) {
                    log.error("platTrxNo:{} 有误，无法解析日期 ",platTrxNo);
                }
            }else{
                //有商户批次号且没有找到 说明没有此单
                paramMap.put(TradeConstant.ORDER_NOT_EXIST_FLAG,"true");
                return;
            }
        }

        //是否有包含批次流水号
        String platBatchNo = (String)paramMap.get("platBatchNo");
        String mchBatchNo = (String)paramMap.get("mchBatchNo");
        if(StringUtils.isNotEmpty(platBatchNo)){
            //批次找打款 查三个月 批次与明细相差
            try {
                Date beginDate = TrxNoDateUtil.getDateFromTrxNo(platBatchNo);
                Date endDate = TrxNoDateUtil.getDateFromTrxNo(platBatchNo,2);
                paramMap.put("beginDate", beginDate);
                paramMap.put("endDate", endDate);
                return;
            } catch (Exception e) {
                log.error("platBatchNo:{} 有误，无法解析日期 ",platBatchNo);
            }
        }
        if(StringUtils.isNotEmpty(mchBatchNo)){
            //查询映射表
            List<MchPlatBatch> mchPlatBatchList = mchPlatBatchBiz.listPlatBatchNoByMchBatchNo(mchBatchNo,employerNo);
            if(!CollectionUtils.isEmpty(mchPlatBatchList)){
                try {
                    if(mchPlatBatchList.size() == 1){
                        platBatchNo = mchPlatBatchList.get(0).getPlatBatchNo();
                        Date beginDate = TrxNoDateUtil.getDateFromTrxNo(platBatchNo);
                        Date endDate = TrxNoDateUtil.getDateFromTrxNo(platBatchNo,2);
                        paramMap.put("platBatchNo", platBatchNo);
                        paramMap.put("beginDate", beginDate);
                        paramMap.put("endDate", endDate);
                    }else {
                        List<Date> createDateList = Lists.newArrayList();
                        mchPlatBatchList.forEach(
                                mchPlatBatch ->{
                                    createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatBatch.getPlatBatchNo()));
                                    createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatBatch.getPlatBatchNo(),1));
                                    createDateList.add(TrxNoDateUtil.getDateFromTrxNo(mchPlatBatch.getPlatBatchNo(),2));
                                }
                        );
                        paramMap.put("createDateList",createDateList);
                    }
                    return;
                } catch (Exception e) {
                    log.error("platBatchNo:{} 有误，无法解析日期 ",platBatchNo);
                }
            }else{
                //有商户批次号且没有找到 说明没有此单
                paramMap.put(TradeConstant.ORDER_NOT_EXIST_FLAG,"true");
                return;
            }
        }

        String channelTrxNo = (String)paramMap.get("channelTrxNo");
        if(StringUtil.isNotEmpty(channelTrxNo)){
            MchPlatTrxNo mchPlatTrxNo = mchPlatTrxNoBiz.getByChannelTrxNo(channelTrxNo);
            if(mchPlatTrxNo != null){
                paramMap.put("platTrxNo", mchPlatTrxNo.getPlatTrxNo());
                Date beginDate = TrxNoDateUtil.getDateFromTrxNo(mchPlatTrxNo.getPlatTrxNo());
                Date endDate = TrxNoDateUtil.getDateFromTrxNo(mchPlatTrxNo.getPlatTrxNo(),2);
                paramMap.put("beginDate", beginDate);
                paramMap.put("endDate", endDate);
            } else {
                //有商户批次号且没有找到 说明没有此单
                paramMap.put(TradeConstant.ORDER_NOT_EXIST_FLAG,"true");
                return;
            }
        }
        TrxNoDateUtil.putCreateDateByMapDate(paramMap);
    }

    /**
     * 查询中的隐私数据替换
     * @param paramMap 会包含隐私数据查询条件的查询paramMap
     */
    public void replacePrivacy(Map<String, Object> paramMap) {
        String receiveName = (String) paramMap.get("receiveName");
        String receiveIdCardNo = (String) paramMap.get("receiveIdCardNo");
        String receiveAccountNo = (String) paramMap.get("receiveAccountNo");
        String receivePhoneNo = (String) paramMap.get("receivePhoneNo");

        if(StringUtils.isNotBlank(receiveName)){
            paramMap.remove("receiveName");
            paramMap.put("receiveNameMd5", MD5Util.getMixMd5Str(receiveName));
        }
        if(StringUtils.isNotBlank(receiveIdCardNo)){
            paramMap.remove("receiveIdCardNo");
            paramMap.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(receiveIdCardNo));
        }
        if(StringUtils.isNotBlank(receiveAccountNo)){
            paramMap.remove("receiveAccountNo");
            paramMap.put("receiveAccountNoMd5", MD5Util.getMixMd5Str(receiveAccountNo));
        }
        if(StringUtils.isNotBlank(receivePhoneNo)){
            paramMap.remove("receivePhoneNo");
            paramMap.put("receivePhoneNoMd5", MD5Util.getMixMd5Str(receivePhoneNo));
        }
    }

    public MerchantNotifyParam fillGrantResultNotifyParam(Order order, OrderItem item) {
        String secKey = UUIDUitl.generateString(16);
        NotifyMchResVo notifyMchResVo = new NotifyMchResVo();
        notifyMchResVo.setMchOrderNo(item.getMchOrderNo());
        notifyMchResVo.setPlatTrxNo(item.getPlatTrxNo());
        notifyMchResVo.setCreateTime(item.getCreateTime());
        notifyMchResVo.setCompleteTime(item.getCompleteTime());
        notifyMchResVo.setOrderItemStatus(item.getOrderItemStatus());
        notifyMchResVo.setReceiveName(AESUtil.encryptECB(item.getReceiveNameDecrypt(),secKey));
        notifyMchResVo.setReceiveIdCardNo(AESUtil.encryptECB(item.getReceiveIdCardNoDecrypt(),secKey));
        notifyMchResVo.setReceiveAccountNo(AESUtil.encryptECB(item.getReceiveAccountNoDecrypt(),secKey));
        notifyMchResVo.setReceivePhoneNo(AESUtil.encryptECB(item.getReceivePhoneNoDecrypt(),secKey));
        notifyMchResVo.setChannelType(item.getChannelType());
        notifyMchResVo.setAppid(item.getAppid());
        notifyMchResVo.setOrderItemNetAmount(String.valueOf(item.getOrderItemNetAmount()));
        notifyMchResVo.setOrderItemFee(String.valueOf(item.getOrderItemFee()));
        notifyMchResVo.setOrderItemAmount(String.valueOf(item.getOrderItemAmount()));
        notifyMchResVo.setRemark(item.getRemark());
        notifyMchResVo.setBizErrCode(StringUtils.isBlank(item.getErrorCode())? BizCodeEnum.SUCCESS.getCode() :item.getErrorCode());
        notifyMchResVo.setBizErrMsg(StringUtils.isBlank(item.getErrorDesc())?BizCodeEnum.SUCCESS.getMsg():item.getErrorDesc());

        if (notifyMchResVo.getBizErrCode().equals(ApiExceptions.API_TRADE_RISK_REJECT.getApiErrorCode()) && StringUtils.isNotBlank(item.getJsonStr())){
            JSONObject jsonObject = JSON.parseObject(item.getJsonStr());
            notifyMchResVo.setRiskType(jsonObject.getObject("risk_type",new TypeReference<List<String>>() {}));
        }

        return fillNotifyParam(order.getEmployerNo(), order.getCallbackUrl(), order.getJsonEntity().getSignType(),secKey,JsonUtil.toString(notifyMchResVo));
    }

    public MerchantNotifyParam fillSignResultNotifyParam(SignRecord signRecord) {
        String secKey = UUIDUitl.generateString(16);
        NotifyMchSignResVo notifyMchSignResVo = new NotifyMchSignResVo();
        notifyMchSignResVo.setUserId(signRecord.getUserId());
        notifyMchSignResVo.setName(AESUtil.encryptECB(signRecord.getReceiveNameDecrypt(),secKey));
        notifyMchSignResVo.setIdCardNo(AESUtil.encryptECB(signRecord.getReceiveIdCardNoDecrypt(),secKey));
        notifyMchSignResVo.setPhoneNo(AESUtil.encryptECB(signRecord.getReceivePhoneNoDecrypt(),secKey));
        notifyMchSignResVo.setMainstayNo(signRecord.getMainstayNo());
        notifyMchSignResVo.setSignStatus(signRecord.getSignStatus());
        notifyMchSignResVo.setBizErrCode(StringUtils.isBlank(signRecord.getErrCode())? BizCodeEnum.SUCCESS.getCode() : signRecord.getErrCode());
        notifyMchSignResVo.setBizErrMsg(StringUtils.isBlank(signRecord.getErrMsg())? BizCodeEnum.SUCCESS.getMsg() : signRecord.getErrMsg());
        return fillNotifyParam(signRecord.getEmployerNo(),signRecord.getNotifyUrl(),signRecord.getJsonEntity().getSignType(),secKey,JsonUtil.toString(notifyMchSignResVo));
    }


    public MerchantNotifyParam fillNotifyParam(String employerNo, String notifyUrl, String signType,String secKey,String data) {
        MerchantNotifyParam merchantNotifyParam = new MerchantNotifyParam();
        merchantNotifyParam.setNotifyContent(new NotifyContent());
        merchantNotifyParam.getNotifyContent().setRespCode(ApiRespCodeEnum.SUCCESS.getCode());
        merchantNotifyParam.getNotifyContent().setData(data);
        merchantNotifyParam.getNotifyContent().setRandStr(UUIDUitl.generateString(32));
        merchantNotifyParam.getNotifyContent().setSignType(signType);
        merchantNotifyParam.getNotifyContent().setMchNo(employerNo);
        MerchantSecret merchantSecret = cacheBiz.getMerchantSecretByMchNo(employerNo);
        String priKey = merchantSecret.getPlatformPrivateKey();
        String sign = SignUtil.sign(merchantNotifyParam.getNotifyContent(), Integer.parseInt(merchantNotifyParam.getNotifyContent().getSignType()),priKey);
        merchantNotifyParam.getNotifyContent().setSign(sign);
        merchantNotifyParam.getNotifyContent().setSecKey(RSAUtil.encryptByPublicKey(secKey,merchantSecret.getMerchantPublicKey()));
        merchantNotifyParam.setNotifyUrl(notifyUrl);
        return merchantNotifyParam;
    }

    public MerchantNotifyParam fillRechargeRecordParam(RechargeRecord rechargeRecord,String notifyUrl) {
        String secKey = UUIDUitl.generateString(16);
        NotifyRechargeResVo vo = new NotifyRechargeResVo();
        vo.setAmount(rechargeRecord.getRechargeAmount().toPlainString());
        vo.setChannelName(rechargeRecord.getChannelName());
        vo.setChannelType(rechargeRecord.getChannelType().intValue());
        vo.setCreateTime(DateUtil.formatDateTime(rechargeRecord.getCreateTime()));
        vo.setMainstayName(rechargeRecord.getMainstayName());
        vo.setPayeeAccountNo(AESUtil.encryptECB(rechargeRecord.getPayeeIdentity(), secKey));
        vo.setPayeeName(AESUtil.encryptECB(rechargeRecord.getPayeeName(), secKey));
        vo.setPayerAccountNo(AESUtil.encryptECB(rechargeRecord.getPayerIdentity(), secKey));
        vo.setPayerName(AESUtil.encryptECB(rechargeRecord.getPayerName(), secKey));
        vo.setPayTime(rechargeRecord.getTransPayTime() == null ? "" : DateUtil.formatDateTime(rechargeRecord.getTransPayTime()));
        vo.setRechargeOrderNo(rechargeRecord.getRechargeOrderId());
        vo.setRechargeType(rechargeRecord.getRechargeType());
        vo.setRechargeStatus(rechargeRecord.getRechargeStatus().intValue());
        vo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
        vo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
        return fillNotifyParam(rechargeRecord.getEmployerNo(),notifyUrl, String.valueOf(SignTypeEnum.RSA.getValue()),secKey,JsonUtil.toString(vo));
    }
}
