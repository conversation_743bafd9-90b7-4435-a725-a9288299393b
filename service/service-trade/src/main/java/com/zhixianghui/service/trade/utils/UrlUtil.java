package com.zhixianghui.service.trade.utils;


import org.apache.commons.lang3.StringUtils;

import java.net.URISyntaxException;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/9/1 10:49
 */
public class UrlUtil {

    /**
     * 用于url后边拼接参数
     *
     * @param url
     * @param params
     * @return
     * @throws URISyntaxException
     */
    public static String buildUrl(String url, Map<String, String> params) throws URISyntaxException {
        if (StringUtils.isBlank(url) || params == null || params.isEmpty()) {
            return null;
        }
        if (!"?".equals(url.substring(url.length() - 1))) {
            url = url + "?";
        }
        StringBuilder urlParams = new StringBuilder(url);
        for (Map.Entry<String, String> entry : params.entrySet()) {
            urlParams.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        String urlParamsStr = urlParams.toString();
        return urlParamsStr.substring(0, urlParamsStr.length() - 1);

    }
}
