package com.zhixianghui.service.trade.pay.jxh;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.vo.jxh.ResStatus;
import com.zhixianghui.service.trade.pay.jxh.annotations.JXHService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class JXHPayContext {

    @Resource
    private List<JXHHandler> handlers;

    private static final Map<ResStatus,JXHHandler> GRANT_BEANS=new HashMap<>();


    @PostConstruct
    public void init() {
        handlers.forEach(bean->{
            JXHService annotation = AopUtils.getTargetClass(bean).getAnnotation(JXHService.class);
            if(annotation==null){
                return;
            }
            GRANT_BEANS.put(annotation.type(),bean);
        });
    }


    public static JXHHandler getJXHHandler(ResStatus status) {
        JXHHandler jxhHandler = GRANT_BEANS.get(status);
        if(ObjectUtils.isEmpty(jxhHandler)){
            log.info("找不到对应的君享汇处理器：{}",status);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("系统异常");
        }
        return jxhHandler;
    }
}
