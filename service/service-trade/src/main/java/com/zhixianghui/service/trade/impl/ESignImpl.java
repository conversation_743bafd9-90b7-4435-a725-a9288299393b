package com.zhixianghui.service.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.service.ESignFacade;
import com.zhixianghui.facade.trade.vo.esign.ESign;
import com.zhixianghui.service.trade.biz.SimpleESignHandleTemPlateBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年08月01日 17:05:00
 */
@Service(timeout = 15000)
public class ESignImpl implements ESignFacade {

    @Autowired
    private SimpleESignHandleTemPlateBiz eSignHandleTemPlateBiz;

    @Override
    public ESign sign(ESign eSign) throws BizException {
        return eSignHandleTemPlateBiz.sign(eSign);
    }
}