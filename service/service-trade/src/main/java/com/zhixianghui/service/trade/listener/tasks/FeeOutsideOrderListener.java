package com.zhixianghui.service.trade.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.trade.enums.FeeSourceEnum;
import com.zhixianghui.service.trade.biz.FeeOrderBatchBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName FeeOutsideOrderListener
 * @Description TODO
 * @Date 2022/11/18 15:22
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_OUTSIDE_ORDER,consumeThreadMax = 3, consumerGroup = "feeOutSideComsumer")
public class FeeOutsideOrderListener extends TaskRocketMQListener<JSONObject> {

    @Autowired
    private FeeOrderBatchBiz feeOrderBatchBiz;

    @Override
    public void runTask(JSONObject jsonParam) {
        Object obj = jsonParam.get("currentDate");
        Date date = null;
        if(!ObjectUtils.isEmpty(obj)){
            String dateJson = String.valueOf(obj);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try {
                date = sdf.parse(dateJson);
            } catch (ParseException e) {
                log.info("账单同步，日期格式化错误，解析json:{}",dateJson);
            }
        }
        feeOrderBatchBiz.syncFeeOrder(date, FeeSourceEnum.OUTSIDE_ORDER);
    }
}
