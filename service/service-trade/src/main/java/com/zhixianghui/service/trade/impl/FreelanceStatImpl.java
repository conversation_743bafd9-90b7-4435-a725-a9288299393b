package com.zhixianghui.service.trade.impl;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.FreelanceStatFacade;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;
import com.zhixianghui.service.trade.biz.FreelanceStatBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 自由职业者月统计表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-08-02
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FreelanceStatImpl implements FreelanceStatFacade {
    private static Logger LOGGER = LoggerFactory.getLogger(FreelanceStatImpl.class);

    private final FreelanceStatBiz biz;

    @Value("${file.domain}")
    private String DOMAIN_PREFIX;

    @Override
    public List<FreelanceStat> freelanceStat(Map<String, Object> param) {
        return biz.freelanceStat(param);

    }

    @Override
    public PageResult<List<FreelanceStat>> freelanceStat(Map<String, Object> param, PageParam pageParam) {
        return biz.freelanceStat(param, pageParam);
    }

    @Override
    public PageResult<List<Map<String, Object>>> freelanceStat2(Map<String, Object> param, PageParam pageParam){
        return biz.freelanceStat2(param, pageParam);
    }

    @Override
    public FreelanceStatVo count(Map<String, Object> param) {
        return biz.count(param);
    }

    @Override
    public Integer freelanceCount(Map<String, Object> param) {
        return biz.freelanceCount(param);
    }

    @Override
    public FreelanceStatVo merchantStatCount(Map<String, Object> param) {
        return biz.merchantStatCount(param);
    }

    @Override
    public PageResult<List<UserInfo>> idCardList(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.idCardList(paramMap, pageParam);

    }

    @Override
    public void insert(List<FreelanceStat> list) {
        biz.insert(list);
    }

    @Override
    public List<FreelanceStatVo> list(Map<String, Object> param, PageParam pageParam) {
        LOGGER.info("自由职业者数据导出参数: {}", JSONObject.toJSONString(param));
        PageResult<List<Map<String, Object>>> list = freelanceStat2(param, pageParam);
        if (list == null || CollectionUtils.isEmpty(list.getData())) {
            return new ArrayList<>();
        }
        List<FreelanceStatVo> result = new ArrayList<>();
        for (Map<String, Object> stat : list.getData()) {
            FreelanceStatVo statVo = new FreelanceStatVo();
            BeanUtil.copyProperties(stat, statVo);
            BeanUtil.mapToObject(statVo,stat);
            if (stat.get("receiveIdCardNo") != null && !"null".equals(String.valueOf(stat.get("receiveIdCardNo")))) {
                try {
                    String encryptKeyStr = EncryptKeys.getEncryptKeyById(Long.valueOf(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr();
                    statVo.setReceiveIdCardNo(AESUtil.decryptECB(String.valueOf(stat.get("receiveIdCardNo")), encryptKeyStr));
                } catch (Exception e) {
                    LOGGER.error("解密身份证号码失败: {}, 加密数据: {}, id: {}", e.getMessage(), stat.get("receiveIdCardNo"), stat.get("id"), e);
                    throw e;
                }
            }
            if (stat.get("receiveName") != null && !"null".equals(String.valueOf(stat.get("receiveName")))) {
                try {
                    String encryptKeyStr = EncryptKeys.getEncryptKeyById(Long.valueOf(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr();
                    statVo.setReceiveName(AESUtil.decryptECB(String.valueOf(stat.get("receiveName")), encryptKeyStr));
                } catch (Exception e) {
                    LOGGER.error("解密姓名失败: {}, 加密数据: {}, id: {}", e.getMessage(), stat.get("receiveName"), stat.get("id"), e);
                    throw e;
                }
            }
            if (stat.get("receivePhoneNo") != null && !"null".equals(String.valueOf(stat.get("receivePhoneNo")))) {
                try {
                    String encryptKeyStr = EncryptKeys.getEncryptKeyById(Long.valueOf(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr();
                    statVo.setReceivePhoneNo(AESUtil.decryptECB(String.valueOf(stat.get("receivePhoneNo")), encryptKeyStr));
                } catch (Exception e) {
                    LOGGER.error("解密手机号码失败: {}, 加密数据: {}, id: {}", e.getMessage(), stat.get("receivePhoneNo"), stat.get("id"), e);
                    throw e;
                }
            }
            if (stat.get("signStatus")!=null && Integer.valueOf(String.valueOf(stat.get("signStatus")))== SignStatusEnum.SIGN_SUCCESS.getValue()) {
                statVo.setSignDescribe(OperationEnum.SIGN.getDescribe());
            } else {
                statVo.setSignDescribe(OperationEnum.UN_SIGN.getDescribe());
            }
            if (Objects.isNull(stat.get("idCardBackUrl"))&&Objects.isNull(stat.get("idCardFrontUrl"))&&Objects.isNull(stat.get("idCardCopyUrl"))) {
                statVo.setIdCardDescribe(OperationEnum.UN_UPLOAD_ID_CARD.getDescribe());
            } else {
                statVo.setIdCardDescribe(OperationEnum.UPLOAD_ID_CARD.getDescribe());
            }
            if(StringUtil.isNotEmpty(statVo.getIdCardFrontUrl())){
                statVo.setIdCardFrontUrl(DOMAIN_PREFIX+statVo.getIdCardFrontUrl());
            }
            if(StringUtil.isNotEmpty(statVo.getIdCardBackUrl())){
                statVo.setIdCardBackUrl(DOMAIN_PREFIX+statVo.getIdCardBackUrl());
            }
            if(StringUtil.isNotEmpty(statVo.getIdCardCopyUrl())){
                statVo.setIdCardCopyUrl(DOMAIN_PREFIX+statVo.getIdCardCopyUrl());
            }
            if(StringUtil.isNotEmpty(statVo.getCerFaceUrl())){
                statVo.setCerFaceUrl(DOMAIN_PREFIX+statVo.getCerFaceUrl());
            }
            result.add(statVo);
        }
        return result;
    }

    @Override
    public PageResult<List<FreelanceStat>> listPage(Map<String, Object> param, PageParam newInstance) {
        return biz.listPage(param, newInstance);
    }

    @Override
    public FreelanceStat getById(Long id) {
        return biz.getById(id);
    }

    @Override
    public void update(List<FreelanceStat> data) {
        biz.update(data);
    }

    @Override
    public void update(FreelanceStat stat) {
        biz.update(stat);
    }
}
