package com.zhixianghui.service.trade.factory;

import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.service.trade.process.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName TradeFactory
 * @Description TODO
 * @Date 2022/6/28 18:37
 */
@Component
public class TradeFactory {
    @Autowired
    private CKHAcceptBiz ckhAcceptBiz;
    @Autowired
    private ZXHAcceptBiz zxhAcceptBiz;
    @Autowired
    private CKHGrantBiz ckhGrantBiz;
    @Autowired
    private ZXHGrantBiz zxhGrantBiz;

    public AbstractAcceptHandler getAcceptor(String productNo){
        if (productNo.equals(ProductNoEnum.CKH.getValue())){
            return ckhAcceptBiz;
        }else if (productNo.equals(ProductNoEnum.ZXH.getValue()) || productNo.equals(ProductNoEnum.CEP.getValue())){
            return zxhAcceptBiz;
        }else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("产品不存在");
        }
    }

    public AbstractGrantHandler getGrantor(String productNo){
        if (productNo.equals(ProductNoEnum.CKH.getValue())){
            return ckhGrantBiz;
        }else if (productNo.equals(ProductNoEnum.ZXH.getValue()) || productNo.equals(ProductNoEnum.CEP.getValue())){
            return zxhGrantBiz;
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("产品不存在");
        }
    }
}
