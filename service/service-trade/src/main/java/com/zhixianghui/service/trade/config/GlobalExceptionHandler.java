package com.zhixianghui.service.trade.config;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.base.utils.RequestUtil;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.util.utils.JsonUtil;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 统一异常处理类
 * <AUTHOR>
 *
 */
@RestControllerAdvice
@Component
public class GlobalExceptionHandler extends HandlerInterceptorAdapter {

	private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    private final static String[] PARAM_NULL_CODES = new String[]{"NotEmpty", "NotNull"};

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseDto exceptionHandle(MethodArgumentNotValidException e, HttpServletRequest request) {
        RequestParam paramVo = RequestUtil.getRequestParam(request);

        // 记录日志
        logError(paramVo, e);

        // 封装返回参数
        BindingResult bindingResult = e.getBindingResult();
        if (bindingResult.hasErrors()) {
            //hibernate validate 设置默认一遇到错误自动抛出错误,所以不存在多条校验错误存在
            ObjectError objectError = bindingResult.getAllErrors().get(0);
            String[] codes = objectError.getCodes();
            String lastCode = codes[codes.length - 1];
            String bizMsg;
            // 参数缺失异常码转换
            if (ArrayUtils.contains(PARAM_NULL_CODES, lastCode)) {
                bizMsg = ApiExceptions.API_PARAM_NULL.getErrMsg() + "[" + objectError.getDefaultMessage() + "]";
                return ResponseDto.fail(ApiExceptions.API_PARAM_NULL.getApiErrorCode(), bizMsg);
            } else {
                bizMsg = ApiExceptions.API_PARAM_FAIL.getErrMsg() + "[" + objectError.getDefaultMessage() + "]";
                return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), bizMsg);
            }
        }
        return null;
    }

    @ExceptionHandler(JsonMappingException.class)
    public ResponseDto exceptionHandle(JsonMappingException e, HttpServletRequest request) {
        RequestParam paramVo = RequestUtil.getRequestParam(request);

        // 记录日志
        logError(paramVo, e);

        // 封装返回参数
        List<JsonMappingException.Reference> referenceList = e.getPath();
        // 存在多层实体嵌套情况，取最后一个
        String formatErrorFiled = referenceList.get(referenceList.size() - 1).getFieldName();
        String bizMsg = ApiExceptions.API_PARAM_FAIL.getErrMsg() + "[" + formatErrorFiled + " 数据类型错误]";
        return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), bizMsg);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseDto exceptionHandle(HttpMessageNotReadableException e, HttpServletRequest request) {
        RequestParam paramVo = RequestUtil.getRequestParam(request);

        // 记录日志
        logError(paramVo, e);
        String bizMsg = ApiExceptions.API_PARAM_FAIL.getErrMsg() + "[数据类型错误]";
        return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), bizMsg);
    }

    @ExceptionHandler(BizException.class)
    public ResponseDto exceptionHandle(BizException e, HttpServletRequest request) {
        RequestParam paramVo = RequestUtil.getRequestParam(request);

        // 记录日志
        logError(paramVo, e);

        // 封装返回参数
        return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
    }

    @ExceptionHandler(Exception.class)
    public ResponseDto exceptionHandle(Exception e, HttpServletRequest request) {
        RequestParam paramVo = RequestUtil.getRequestParam(request);

        // 记录日志
        logError(paramVo, e);

        // 封装返回参数
        return ResponseDto.unknown();
    }

    private void logError(RequestParam paramVo,Exception e){
        logger.error("汇聚智享系统处理异常，trade服务，请求报文：{}，异常信息：", JsonUtil.toString(paramVo), e);
    }
}
