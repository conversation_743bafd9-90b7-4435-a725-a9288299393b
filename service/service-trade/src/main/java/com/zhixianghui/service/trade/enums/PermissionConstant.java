package com.zhixianghui.service.trade.enums;


/**
 * 与权限相关的常量
 *
 * <AUTHOR> <PERSON>
 */
public class PermissionConstant {

    public static final String REQUEST_TOKEN_HEADER = "authorization";

    /**
     * 图形验证码过期时间
     */
    public static final int CAPTCHA_EXPIRE_TIME = 300;

    /**
     * 短信验证码key
     */
    public static final String SMS_CODE_KEY = "SMS_CODE_KEY";

    /**
     * 校验短信验证码key
     */
    public static final String VERIFY_SMS_CODE_KEY = "VERIFY_SMS_CODE_KEY";

    /**
     * 短信验证码重发时间
     */
    public static final int SMS_CODE_RESEND_TIME = 60;

    /**
     * 短信验证码过期时间
     */
    public static final int SMS_CODE_EXPIRE_TIME = 300;

    /**
     * UUID长度
     */
    public static final int UUID_LENGTH = 300;


}
