package com.zhixianghui.service.trade.pay.wx.biz;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.enums.wx.WxDetailStatusEnum;
import com.zhixianghui.common.statics.enums.wx.WxFailReasonEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.facade.trade.vo.WxPayQueryVo;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.pay.wx.WxPayContext;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月14日 10:06:00
 */
@Service
@Slf4j
public class WxNotifyBiz {

    @Reference
    private NotifyFacade notifyFacade;
    @Autowired
    private RedisClient redisClient;

    public void send(PayReqVo payReqVo) {
        String platTrxNo = payReqVo.getPlatTrxNo();
        log.info("[{}]==>微信回查消息", platTrxNo);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_PAY_QUERY, UUIDUitl.generateString(10), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_WX_PAY_QUERY, JsonUtil.toString(payReqVo), MsgDelayLevelEnum.S_10.getValue());
    }


    public void retry(PayReqVo payReqVo) {
        String platTrxNo = payReqVo.getPlatTrxNo();
        log.info("[{}]进入微信回查重试", platTrxNo);
        Long count = redisClient.incr(WxUtil.getRedisRetryKey(platTrxNo));
        redisClient.expire(WxUtil.getRedisRetryKey(platTrxNo), 60 * 60 * 2);
        if (count > TradeConstant.WX_PAY_QUERY_RETRY_COUNT) {
            log.error("[{}]微信回查重试: 重试超过五次", platTrxNo);
            return;
        }
        log.info("[{}]微信回查重试,开始重试{}次", platTrxNo,count);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_PAY_QUERY, UUIDUitl.generateString(10), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_WX_PAY_RETRY, JsonUtil.toString(payReqVo),MsgDelayLevelEnum.M_1.getValue());

    }


    public void retryNotFond(PayReqVo payReqVo) {
        String platTrxNo = payReqVo.getPlatTrxNo();
        log.info("[{}]进入微信回查订单不存在重试", platTrxNo);
        Long count = redisClient.incr(WxUtil.getRedisRetryKey(platTrxNo));
        redisClient.expire(WxUtil.getRedisRetryKey(platTrxNo), 60 * 60 * 2);
        if (count > TradeConstant.WX_PAY_QUERY_RETRY_COUNT) {
            try {
                log.error("[{}]微信回查订单不存在重试: 重试超过五次", platTrxNo);
                WxPayQueryVo wxPayQueryVo =new WxPayQueryVo();
                wxPayQueryVo.setDetailStatus(WxDetailStatusEnum.FAIL.name());
                wxPayQueryVo.setFailReason(WxFailReasonEnum.NOT_FOUND.name());
                wxPayQueryVo.setDetailId("");
                wxPayQueryVo.setOutDetailNo(payReqVo.getBankOrderNo());
                wxPayQueryVo.setOutBatchNo(payReqVo.getBankOrderNo());
                WxPayParam wxPayParam = WxPayContext.buildParam(wxPayQueryVo, payReqVo);
                log.error("[{}]微信回查订单不存在重试: 入参：{}",platTrxNo, wxPayParam);
                WxPayContext.handle(wxPayParam);
            }catch (Exception e){
                log.error("[{}]微信回查订单不存在重试，更新订单失败", platTrxNo);
            }
            return;
        }
        log.info("[{}]微信回查订单不存在重试,开始重试{}次", platTrxNo,count);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_PAY_QUERY, UUIDUitl.generateString(10), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_WX_PAY_NO_FOND_RETRY, JsonUtil.toString(payReqVo),MsgDelayLevelEnum.M_1.getValue());

    }



    public void adjustment(AdjustmentDTO adjustmentDTO){
        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_PAY_ADJUSTMENT, UUIDUitl.generateString(10), adjustmentDTO.getLogKey(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_WX_PAY_ADJUSTMENT, JsonUtil.toString(adjustmentDTO),MsgDelayLevelEnum.S_1.getValue());
    }

    public void updateChangeFounds(ChangesFunds changesFunds){
        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_PAY_QUERY, UUIDUitl.generateString(10), changesFunds.getPlatTrxNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_WX_PAY_CHANGE_FOUNDS, JsonUtil.toString(changesFunds),MsgDelayLevelEnum.S_1.getValue());
    }
}