package com.zhixianghui.service.trade.pay.wx.biz;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.wx.WxPayFacade;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;
import com.zhixianghui.facade.trade.enums.WithdrawStatusEnum;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.enums.WxWithdrawStatusEnum;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.service.trade.biz.ChangesFundsBiz;
import com.zhixianghui.service.trade.biz.WithdrawRecordBiz;
import com.zhixianghui.service.trade.biz.WxMerchantBalanceBiz;
import com.zhixianghui.service.trade.process.WithdrawAcctDetailBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @ClassName WxWithdrawBiz
 * @Description TODO
 * @Date 2022/1/13 9:55
 */
@Service
@Slf4j
public class WxWithdrawBiz {

    @Autowired
    private WxMerchantBalanceBiz wxMerchantBalanceBiz;

    @Autowired
    private WithdrawRecordBiz withdrawRecordBiz;

    @Autowired
    private ChangesFundsBiz changesFundsBiz;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Reference
    private NotifyFacade notifyFacade;

    @Reference
    private WxPayFacade wxPayFacade;

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Autowired
    private WithdrawAcctDetailBiz withdrawAcctDetailBiz;

    @Autowired
    private RedisClient redisClient;

    private static final String WITHDRAW_TIMES = "withdraw:times:";

    private static final int MAX_RETRY_TIMES = 5;

    /**
     * 查询提现状态
     * @param withdrawRecord
     */
    public void getWxWithdrawStatus(WithdrawRecord withdrawRecord) {
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(withdrawRecord.getMainstayNo(), ChannelNoEnum.WXPAY.name());
        if (mainstayChannelRelation == null){
            log.error("找不到代征主体对应的微信通道数据，提现单号：[{}]，提现记录：[{}]",withdrawRecord.getWithdrawNo(),JsonUtil.toString(withdrawRecord));
            return;
        }
        //查询微信订单状态
        WxResVo wxResVo = wxPayFacade.getWithdrawStatus(mainstayChannelRelation.getChannelMchNo(),withdrawRecord.getWithdrawNo());
        if (wxResVo.isSuccess()){
            statusHandle(wxResVo,withdrawRecord);
        }else if (wxResVo.getCode().intValue() == 404){
            //订单不存在，重试处理
            retry(withdrawRecord);
        }else{
            log.error("查询订单提现状态异常，提现订单号：[{}]",withdrawRecord.getWithdrawNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询订单提现状态异常");
        }
    }

    private void statusHandle(WxResVo wxResVo,WithdrawRecord withdrawRecord) {
        //重新获取一下最新的版本号
        withdrawRecord = withdrawRecordBiz.getByWithdrawRecordNo(withdrawRecord.getWithdrawNo());
        Map<String,Object> map = JsonUtil.toBean(wxResVo.getResponBody(),Map.class);
        WxWithdrawStatusEnum e = WxWithdrawStatusEnum.getEnum((String) map.get("status"));
        log.info("微信代征主体提现状态：[{}]，提现订单号：[{}]，接口返回信息：[{}]",e.getDesc(),withdrawRecord.getWithdrawNo(),wxResVo.getResponBody());
        switch (e){
            case INIT:
                retry(withdrawRecord);
                break;
            case SUCCESS:
                success(map,withdrawRecord);
                break;
            case FAIL:
            case CLOSE:
            case REFUND:
                withdrawFail((String)map.get("reason"),withdrawRecord);
                break;
            case CREATE_SUCCESS:
                //延时一小时再查询结果
                notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_WITHDRAW_STATUS, NotifyTypeEnum.WX_WITHDRAW.getValue(),
                        MessageMsgDest.TAG_WX_WITHDRAW_STATUS,JsonUtil.toString(withdrawRecord),MsgDelayLevelEnum.H_1.getValue());
                break;
        }
    }

    /**
     * 成功处理
     * @param withdrawRecord
     */
    private void success(Map<String,Object> map,WithdrawRecord withdrawRecord) {
        long amount = AmountUtil.changeToFen(withdrawRecord.getAmount());

        ChangesFunds changesFunds = buildChangesFunds(withdrawRecord,WxAmountChangeLogTypeEnum.WITHDRAW,-amount,-amount);
        withdrawRecord.setUpdateTime(new DateTime((String)map.get("update_time")).toJdkDate());
        withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.SUCCESS.getCode());

        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);

        String redisLockKey = WxUtil.getRedisLockKey(withdrawRecord.getEmployerNo(), withdrawRecord.getMainstayNo(),MerchantTypeEnum.MAINSTAY.getValue());
        RLock rLock = redisLock.tryLock(redisLockKey,WxUtil.LOCK_WAIT_TIME,WxUtil.LOCK_LEASE_TIME);
        if (rLock == null){
            log.error("代征主体提现环节，提现订单号：[{}]，获取锁失败，等待消息重试",withdrawRecord.getWithdrawNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("并发异常");
        }

        try {
            withdrawRecordBiz.updateById(withdrawRecord);
            withdrawAcctDetailBiz.save2CkAcctDetail(withdrawRecord);
            wxMerchantBalanceBiz.changeAmount(changesFunds,rLock);
            platformTransactionManager.commit(transaction);
        }catch (DuplicateKeyException e){
            //已重复入账，无须处理
            platformTransactionManager.rollback(transaction);
        }catch (Exception e){
            platformTransactionManager.rollback(transaction);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提现处理异常:" + e);
        }finally {
            redisLock.unlock(rLock);
        }
    }

    /**
     * 失败处理
     * @param
     * @param withdrawRecord
     */
    private void withdrawFail(String reason,WithdrawRecord withdrawRecord) {
        long amount = AmountUtil.changeToFen(withdrawRecord.getAmount());
        ChangesFunds changesFunds = buildChangesFunds(withdrawRecord,WxAmountChangeLogTypeEnum.REFUND,0,-amount);
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        String redisLockKey = WxUtil.getRedisLockKey(withdrawRecord.getEmployerNo(), withdrawRecord.getMainstayNo(),MerchantTypeEnum.MAINSTAY.getValue());
        RLock rLock = redisLock.tryLock(redisLockKey,WxUtil.LOCK_WAIT_TIME,WxUtil.LOCK_LEASE_TIME);
        if (rLock == null){
            log.error("代征主体提现环节，提现订单号：[{}]，获取锁失败，等待消息重试",withdrawRecord.getWithdrawNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("并发异常");
        }

        try {
            //失败记录
            withdrawError(withdrawRecord.getWithdrawNo(),reason,ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
            wxMerchantBalanceBiz.changeAmount(changesFunds,rLock);
            platformTransactionManager.commit(transaction);
        }catch (DuplicateKeyException e){
            //重复变动账户，不作任何处理
            platformTransactionManager.rollback(transaction);
        }catch (Exception e){
            platformTransactionManager.rollback(transaction);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提现处理异常:" + e);
        }finally {
            redisLock.unlock(rLock);
        }
    }

    /**
     * 原单号重试
     * @param withdrawRecord
     */
    private void retry(WithdrawRecord withdrawRecord) {

        Long times = redisClient.incr(WITHDRAW_TIMES + withdrawRecord.getWithdrawNo());
        redisClient.expire(WITHDRAW_TIMES + withdrawRecord.getWithdrawNo(),60*60);
        if(times > MAX_RETRY_TIMES){
            log.error("提现次数超过最大限制，不再进行重试，提现订单号：[{}]",withdrawRecord.getWithdrawNo());
            return;
        }
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(
                withdrawRecord.getMainstayNo(),ChannelNoEnum.WXPAY.name());
        log.error("由于金额已在本地扣减过，进行不换单重试，提现订单号：[{}]",withdrawRecord.getWithdrawNo());
        Long amount = AmountUtil.changeToFen(withdrawRecord.getAmount());
        WxResVo wxResVo = null;
        try {
            wxResVo = wxPayFacade.withdraw(mainstayChannelRelation.getChannelMchNo(),withdrawRecord.getWithdrawNo(),
                    amount.intValue(),withdrawRecord.getRemark());
        }catch (Exception e){
            log.error("提现异常，不确定是否已经请求到渠道，发送消息查询结果，提现订单号：[{}]",withdrawRecord.getWithdrawNo());
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_WITHDRAW_STATUS, NotifyTypeEnum.WX_WITHDRAW.getValue(),
                    MessageMsgDest.TAG_WX_WITHDRAW_STATUS,JsonUtil.toString(withdrawRecord),MsgDelayLevelEnum.M_5.getValue());
            return;
        }

        if (wxResVo.isSuccess() || wxResVo.getCode().intValue() == 500){
            //暂不更新记录，等查询的时候再确定是否成功
            //发送延时消息查询状态
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_WITHDRAW_STATUS,NotifyTypeEnum.WX_WITHDRAW.getValue(),MessageMsgDest.TAG_WX_WITHDRAW_STATUS,JsonUtil.toString(withdrawRecord), MsgDelayLevelEnum.M_5.getValue());
        }else{
            Map<String, String> res = JSONObject.parseObject(wxResVo.getResponBody(), Map.class);
            withdrawFail((String)res.get("message"),withdrawRecord);
            return;
        }
    }


    /**
     * 供应商提现
     * @param withdrawDto
     * @param withdrawNo
     * @param mainstayChannelRelation
     * @return
     */
    public WithdrawRecord mainstayWxPayWithdraw(WithdrawDto withdrawDto, String withdrawNo, MainstayChannelRelation mainstayChannelRelation) {
        //先判断一下供应商账户余额是否足够，真正提现的时候会加锁查询兜底
        WxMerchantBalance wxMerchantBalance = new WxMerchantBalance();
        wxMerchantBalance.setMainstayNo(mainstayChannelRelation.getMainstayNo());
        wxMerchantBalance.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        wxMerchantBalance = wxMerchantBalanceBiz.getOne(wxMerchantBalance);
        if (wxMerchantBalance == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体本地账户不存在，请先进行报备");
        }
        Long amount = AmountUtil.changeToFen(withdrawDto.getAmount());
        if ((wxMerchantBalance.getTotalAmount() - wxMerchantBalance.getFreezeAmount()) < amount){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体账户余额不足");
        }
        WithdrawRecord withdrawRecord = fillWxDrawRecord(withdrawDto,withdrawNo,mainstayChannelRelation);
        WithdrawRecord withdrawRecordNew = withdrawRecordBiz.addWithdrawRecord(withdrawRecord);
        //供应商账号可能会有较多资源占用情况，异步处理
        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_WITHDRAW,mainstayChannelRelation.getMainstayNo(),withdrawNo,
                NotifyTypeEnum.WX_WITHDRAW.getValue(),MessageMsgDest.TAG_WX_WITHDRAW, JsonUtil.toString(withdrawRecord));
        return withdrawRecordNew;
    }

    private WithdrawRecord fillWxDrawRecord(WithdrawDto withdrawDto,String withdrawNo,MainstayChannelRelation mainstayChannelRelation) {
        WithdrawRecord withdrawRecord = new WithdrawRecord();
        withdrawRecord.setWithdrawNo(withdrawNo);
        withdrawRecord.setAmount(new BigDecimal(withdrawDto.getAmount()));
        withdrawRecord.setChannelNo(withdrawDto.getChannelNo());
        withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.NEW.getCode());
        withdrawRecord.setCreateTime(new Date());
        withdrawRecord.setRemark(withdrawDto.getRemark());
        withdrawRecord.setMainstayNo(withdrawDto.getMainstayNo());
        withdrawRecord.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        withdrawRecord.setMainstayName(mainstayChannelRelation.getMainstayName());
        withdrawRecord.setReceiveAcctType(ChannelTypeEnum.BANK.getValue());
        withdrawRecord.setReceiveName(mainstayChannelRelation.getMainstayName());
        withdrawRecord.setReceiveAcctType(ChannelTypeEnum.ALIPAY.getValue());
        withdrawRecord.setChannelType(ChannelTypeEnum.WENXIN.getValue());
        return withdrawRecord;
    }

    /**
     * 真正发起提现
     * @param withdrawRecord
     * @param mainstayChannelRelation
     */
    public void wxWithdraw(WithdrawRecord withdrawRecord,MainstayChannelRelation mainstayChannelRelation,RLock rLock) {
        WxMerchantBalance wxMerchantBalance = new WxMerchantBalance();
        wxMerchantBalance.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        wxMerchantBalance.setMainstayNo(withdrawRecord.getMainstayNo());
        wxMerchantBalance = wxMerchantBalanceBiz.getOne(wxMerchantBalance);
        if (wxMerchantBalance == null){
            log.error("供应商编号：[{}]，提现单号：[{}]，供应商本地账户不存在，请先进行报备",withdrawRecord.getMainstayNo(),withdrawRecord.getWithdrawNo());
            withdrawError(withdrawRecord.getWithdrawNo(),"供应商本地账户不存在，请先进行报备", ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
            return;
        }
        Long amount = AmountUtil.changeToFen(withdrawRecord.getAmount());
        if ((wxMerchantBalance.getTotalAmount() - wxMerchantBalance.getFreezeAmount()) < amount){
            log.error("供应商编号：[{}]，提现单号：[{}]，账户余额不足",withdrawRecord.getMainstayNo(),withdrawRecord.getWithdrawNo());
            withdrawError(withdrawRecord.getWithdrawNo(),"账户余额不足",ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
            return;
        }
        //余额扣减-冻结金额
        //只有这部分需要事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        log.info("提现金额扣减，提现订单号：[{}]，供应商编号：[{}]，冻结金额增加:[{}]",withdrawRecord.getWithdrawNo(),withdrawRecord.getMainstayNo(),amount);
        ChangesFunds changesFunds = buildChangesFunds(withdrawRecord,wxMerchantBalance,
                WxAmountChangeLogTypeEnum.FROZEN,0,amount);
        try {
            wxMerchantBalanceBiz.changeAmount(changesFunds,rLock);
            platformTransactionManager.commit(transaction);
        }catch (DuplicateKeyException e){
            platformTransactionManager.rollback(transaction);
        }catch (Exception e){
            log.error("提现异常，提现订单号：[{}]",withdrawRecord.getWithdrawNo());
            platformTransactionManager.rollback(transaction);
            return;
        }


        WxResVo wxResVo = null;
        try {
            wxResVo = wxPayFacade.withdraw(mainstayChannelRelation.getChannelMchNo(),
                    withdrawRecord.getWithdrawNo(),amount.intValue(),withdrawRecord.getRemark());
            //wxResVo = wxPayFacade.withdraw("1651651",withdrawRecord.getWithdrawNo(),amount.intValue(),withdrawRecord.getRemark());
        }catch (Exception e){
            log.error("提现异常，不确定是否已经请求到渠道，发送消息查询结果，提现订单号：[{}]",withdrawRecord.getWithdrawNo());
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_WITHDRAW_STATUS,NotifyTypeEnum.WX_WITHDRAW.getValue(),
                    MessageMsgDest.TAG_WX_WITHDRAW_STATUS,JsonUtil.toString(withdrawRecord), MsgDelayLevelEnum.M_5.getValue());
            return;
        }
        if (wxResVo.isSuccess() || wxResVo.getCode().intValue() == 500){
            //暂不更新记录，等查询的时候再确定是否成功
            //发送延时消息查询状态
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_WITHDRAW_STATUS,NotifyTypeEnum.WX_WITHDRAW.getValue(),MessageMsgDest.TAG_WX_WITHDRAW_STATUS,JsonUtil.toString(withdrawRecord), MsgDelayLevelEnum.M_5.getValue());
        }else{
            Map<String, String> res = JSONObject.parseObject(wxResVo.getResponBody(), Map.class);
            withdrawFail((String)res.get("message"),withdrawRecord);
            return;
        }
    }

    private void withdrawError(String withDrawNo, String msg,String code) {
        WithdrawRecord withdrawRecord = withdrawRecordBiz.getByWithdrawRecordNo(withDrawNo);
        if (withdrawRecord != null) {
            withdrawRecord.setErrorMsg(msg);
            withdrawRecord.setErrorCode(code);
            withdrawRecord.setUpdateTime(new Date());
            withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.FAIL.getCode());
            withdrawRecordBiz.updateById(withdrawRecord);
        }
    }

    /**
     * 构建变动记录，未获取锁
     * @param withdrawRecord
     * @param wxAmountChangeLogTypeEnum
     * @param totalAmountChange
     * @param frozenAmountChange
     * @return
     */
    private ChangesFunds buildChangesFunds(WithdrawRecord withdrawRecord,WxAmountChangeLogTypeEnum wxAmountChangeLogTypeEnum
            ,long totalAmountChange,long frozenAmountChange){
        ChangesFunds changesFunds = new ChangesFunds();
        changesFunds.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue())
                .setLogKey(WxUtil.getLogKey(withdrawRecord.getWithdrawNo(),wxAmountChangeLogTypeEnum.getValue(),MerchantTypeEnum.MAINSTAY))
                .setMainstayNo(withdrawRecord.getMainstayNo())
                .setMainstayName(withdrawRecord.getMainstayName())
                .setPlatTrxNo(withdrawRecord.getWithdrawNo())
                .setAmountChangeType(WxAmountChangeLogTypeEnum.WITHDRAW.getValue())
                .setAmount(totalAmountChange)
                .setFrozenAmount(frozenAmountChange)
                .setCreateTime(new Date());
        return changesFunds;
    }

    /**
     * 构建变动记录，锁已获取
     * @param withdrawRecord
     * @param wxMerchantBalance
     * @param wxAmountChangeLogTypeEnum
     * @param totalAmountChange
     * @param frozenAmountChange
     * @return
     */
    private ChangesFunds buildChangesFunds(WithdrawRecord withdrawRecord,WxMerchantBalance wxMerchantBalance,
                                           WxAmountChangeLogTypeEnum wxAmountChangeLogTypeEnum,long totalAmountChange,long frozenAmountChange) {
        ChangesFunds changesFunds = new ChangesFunds();
        changesFunds.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue())
                .setLogKey(WxUtil.getLogKey(withdrawRecord.getWithdrawNo(),wxAmountChangeLogTypeEnum.getValue(),MerchantTypeEnum.MAINSTAY))
                .setMainstayNo(withdrawRecord.getMainstayNo())
                .setMainstayName(withdrawRecord.getMainstayName())
                .setPlatTrxNo(withdrawRecord.getWithdrawNo())
                .setAmountChangeType(WxAmountChangeLogTypeEnum.WITHDRAW.getValue())
                .setAmount(totalAmountChange)
                .setFrozenAmount(frozenAmountChange)
                .setCreateTime(new Date());
        return changesFunds;
    }

    /**
     * 微信提现兜底任务
     */
    public void withdrawTimer() {
        Date nowDate = new Date();
        //只获取大于7天小于15天内的记录
        Date startDate = DateUtil.addDay(nowDate,-10);
        Date endDate = DateUtil.addDay(nowDate,-7);
        //查询范围内的提现记录
        List<WithdrawRecord> list = withdrawRecordBiz.list(new QueryWrapper<WithdrawRecord>()
                .eq(WithdrawRecord.COL_MERCHANT_TYPE,MerchantTypeEnum.MAINSTAY.getValue())
                .eq(WithdrawRecord.COL_WITHDRAW_STATUS,WithdrawStatusEnum.SUCCESS.getCode())
                .eq(WithdrawRecord.COL_CHANNEL_NO,ChannelNoEnum.WXPAY.name())
                .between(WithdrawRecord.COL_CREATE_TIME,startDate,endDate));
        if (list.size() == 0){
            log.info("找不到对应的提现记录。。。");
            return;
        }
        //异步执行
        CompletableFuture.runAsync(()-> timerHandle(list));
    }

    private void timerHandle(List<WithdrawRecord> list) {
        Map<String,Object> map = new HashMap<>();
        list.forEach(x->{
            //获取特约商户号
            String subMchId = (String) map.get(x.getMainstayNo());
            if (StringUtils.isBlank(subMchId)){
                MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(x.getMainstayNo(),ChannelNoEnum.WXPAY.name());
                if (mainstayChannelRelation != null){
                    subMchId = mainstayChannelRelation.getChannelMchNo();
                    map.put(x.getMainstayNo(),subMchId);
                }
            }
            //调用微信接口查询提现记录状态
            WxResVo wxResVo = wxPayFacade.getWithdrawStatus(subMchId,x.getWithdrawNo());
            if (wxResVo.isSuccess()){
                Map<String,Object> resMap = JsonUtil.toBean(wxResVo.getResponBody(),Map.class);
                String status = (String) resMap.get("status");
                if (status.equals(WxWithdrawStatusEnum.CLOSE.getValue()) || status.equals(WxWithdrawStatusEnum.FAIL.getValue())
                        || status.equals(WxWithdrawStatusEnum.REFUND.getValue())){
                    log.error("提现订单号：[{}]，提现失败，进行退款操作，接口返回信息：[{}]",x.getWithdrawNo(),JsonUtil.toString(wxResVo.getResponBody()));
                    //操作退款
                    long amount = AmountUtil.changeToFen(x.getAmount());

                    TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
                    String redisLockKey = WxUtil.getRedisLockKey(x.getEmployerNo(), x.getMainstayNo(),MerchantTypeEnum.MAINSTAY.getValue());
                    RLock rLock = redisLock.tryLock(redisLockKey,WxUtil.LOCK_WAIT_TIME,WxUtil.LOCK_LEASE_TIME);
                    if (rLock == null){
                        log.error("代征主体提现环节，提现订单号：[{}]，获取锁失败，等待消息重试",x.getWithdrawNo());
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("并发异常");
                    }

                    try {
                        withdrawError(x.getWithdrawNo(), (String) resMap.get("reason"),ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
                        //由于这里是兜底服务，已经结算过，所以需要修改总金额增加
                        ChangesFunds changesFunds = buildChangesFunds(x,WxAmountChangeLogTypeEnum.REFUND,amount,0);
                        wxMerchantBalanceBiz.changeAmount(changesFunds,rLock);
                        platformTransactionManager.commit(transaction);
                    }catch (DuplicateKeyException e){
                        //重复变动账户，不作任何处理
                        platformTransactionManager.rollback(transaction);
                    }catch (Exception e){
                        platformTransactionManager.rollback(transaction);
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提现处理异常:" + e);
                    }finally {
                        redisLock.unlock(rLock);
                    }

                }else{
                    log.info("微信提现兜底查询结束，不执行任何操作，提现订单号：[{}]，接口返回信息：[{}]",x.getWithdrawNo(),wxResVo.getResponBody());
                }
            }else{
                log.error("兜底查询失败，提现订单号：[{}]，接口返回信息：[{}]",x.getWithdrawNo(),wxResVo.getResponBody());
            }
        });
    }
}
