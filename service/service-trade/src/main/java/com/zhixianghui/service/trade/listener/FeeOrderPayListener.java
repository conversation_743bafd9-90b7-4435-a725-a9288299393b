package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.trade.biz.FeePayBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月11日 16:05:00
 */
@Slf4j
@Component
public class FeeOrderPayListener {
    @Autowired
    private FeePayBiz feePayBiz;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_ORDER_ITEM_PAY, selectorExpression = MessageMsgDest.TAG_FEE_ORDER_ITEM_SERVICE_PAY,
            consumeThreadMax = 5, consumerGroup = "feeServicePayConsumer")
    public class FeePayListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String feeOrderItemNo) {
            log.info("[{}]账单服务费支付",feeOrderItemNo);
            feePayBiz.payFee(feeOrderItemNo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_ORDER_ITEM_PAY, selectorExpression = MessageMsgDest.TAG_FEE_ORDER_ITEM_TAX_PAY,
            consumeThreadMax = 5, consumerGroup = "feeTaxPayConsumer")
    public class ServicePayListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String feeOrderItemNo) {
            log.info("[{}]账单个税支付",feeOrderItemNo);
            feePayBiz.payFee(feeOrderItemNo);
        }
    }
}