package com.zhixianghui.service.trade.pay.wx;

import cn.hutool.extra.spring.SpringUtil;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.facade.trade.vo.WxPayQueryVo;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.service.trade.pay.wx.biz.WxNotifyBiz;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月13日 18:24:00
 */
public class WxProcessingPay extends IWxPay {

    private final WxNotifyBiz wxNotifyBiz = SpringUtil.getBean(WxNotifyBiz.class);

    public WxProcessingPay(WxPayParam wxPayParam) {
        super(wxPayParam);
    }


    @Override
    public void handle() {
        PayReqVo reqVo=new PayReqVo();
        BeanUtil.copyProperties(wxPayParam,reqVo);
        reqVo.setBankOrderNo(wxPayParam.getOutDetailNo());
        wxNotifyBiz.retry(reqVo);
    }



    @Override
    public PayRespVo getResVo(boolean isUpdateFound){
        PayRespVo respVo=new PayRespVo();
        respVo.setBankTrxNo(wxPayParam.getDetailId());
        respVo.setBankOrderNo(wxPayParam.getPlatTrxNo());
        respVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
        respVo.setBizCode("");
        respVo.setBizMsg("");
        return respVo;
    }
}