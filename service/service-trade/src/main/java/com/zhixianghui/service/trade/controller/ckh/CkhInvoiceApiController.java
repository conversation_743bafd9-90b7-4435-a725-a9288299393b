package com.zhixianghui.service.trade.controller.ckh;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.trade.dto.InvoiceRecordInfoQueryDto;
import com.zhixianghui.facade.trade.dto.InvoiceRecordListQueryDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.enums.InvoiceCategoryEnum;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.service.trade.biz.InvoiceRecordBiz;
import com.zhixianghui.service.trade.biz.InvoiceRecordDetailBiz;
import com.zhixianghui.service.trade.process.InvoiceHandleBiz;
import com.zhixianghui.service.trade.vo.res.CommonPageResVo;
import com.zhixianghui.service.trade.vo.res.InvoiceRecordApiResVo;
import com.zhixianghui.service.trade.vo.res.InvoiceRecordInfoApiResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 开票对外api接口
 */
@RestController
@RequestMapping("ckh")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CkhInvoiceApiController {

    public static final int DEFAULT_PAGE_SIZE = 50;

    @Autowired
    private InvoiceRecordBiz invoiceRecordBiz;
    @Autowired
    private InvoiceRecordDetailBiz invoiceRecordDetailBiz;
    @Autowired
    private InvoiceHandleBiz invoiceHandleBiz;

    @PostMapping("invoiceRecordListQuery")
    public ResponseDto<CommonPageResVo<InvoiceRecordApiResVo>> invoiceRecordListQuery(@RequestBody RequestDto<InvoiceRecordListQueryDto> paramVo){
        final String mchNo = paramVo.getMchNo();
        final InvoiceRecordListQueryDto data = paramVo.getData();

        final JSONObject paramDataJson =(JSONObject) JSON.toJSON(data);
        paramDataJson.put("employerMchNo", mchNo);
        log.info(paramDataJson.toJSONString());

        try {
            final PageResult<List<InvoiceRecord>> pageResult = invoiceRecordBiz.listPage(paramDataJson, PageParam.newInstance(data.getCurrentPage(), DEFAULT_PAGE_SIZE));
            CommonPageResVo<InvoiceRecordApiResVo> resVo = new CommonPageResVo<>(pageResult.getPageCurrent(),pageResult.getTotalRecord(),pageResult.getPageSize());
            final List<InvoiceRecord> pageResultData = pageResult.getData();

            List<InvoiceRecordApiResVo> resList = new ArrayList<>();
            if (pageResultData != null && !pageResultData.isEmpty()) {
                for (InvoiceRecord invoiceRecord : pageResultData) {
                    InvoiceRecordApiResVo apiResVo = new InvoiceRecordApiResVo();
                    BeanUtil.copyProperties(invoiceRecord, apiResVo);
                    apiResVo.setMainstayName(invoiceRecord.getMainstayMchName());
                    resList.add(apiResVo);
                }
            }

            resVo.setList(resList);
            return ResponseDto.success(resVo, "");
        } catch (BizException bizException) {
            log.error("查询开票记录列表 业务异常：", bizException);
            return ResponseDto.fail(bizException.getApiErrorCode(), bizException.getErrMsg());
        } catch (Exception exception) {
            log.error("查询开票记录列表 系统异常：", exception);
            return ResponseDto.unknown();
        }

    }

    @PostMapping("invoiceRecordInfoQuery")
    public ResponseDto<InvoiceRecordInfoApiResVo> invoiceRecordInfoQuery(@RequestBody RequestDto<InvoiceRecordInfoQueryDto> paramVo){
        final String mchNo = paramVo.getMchNo();
        final String trxNo = paramVo.getData().getTrxNo();

        if (StringUtils.isBlank(trxNo)) {
            return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), "开票流水号不能为空");
        }

        try{
            final InvoiceRecord invoiceRecord = invoiceRecordBiz.getByTrxNo(trxNo);
            if (invoiceRecord != null) {
                if (!StringUtils.equals(mchNo, invoiceRecord.getEmployerMchNo())) {
                    return ResponseDto.fail(ApiExceptions.API_BIZ_FAIL.getApiErrorCode(), "开票流水号不存在");
                }
                InvoiceRecordInfoApiResVo apiResVo = new InvoiceRecordInfoApiResVo();
                BeanUtil.copyProperties(invoiceRecord, apiResVo);
                apiResVo.setMainstayName(invoiceRecord.getMainstayMchName());

                String key = RandomUtil.get16LenStr();
                if (invoiceRecord.getCategory() == InvoiceCategoryEnum.NATURE_PERSON_INVOICE.getCode()) {
                    final IPage<InvoiceDetailGroupByIdCardVo> groupByIdCardVoIPage = invoiceHandleBiz.listInvoiceDetailGroupByIdCard(new Page<>(1, 100000), trxNo);
                    final List<InvoiceDetailGroupByIdCardVo> records = groupByIdCardVoIPage.getRecords();
                    if (records != null && !records.isEmpty()) {
                        for (InvoiceDetailGroupByIdCardVo record : records) {
                            final InvoiceRecordInfoApiResVo.InvoiceItem invoiceItem = apiResVo.new InvoiceItem();
                            BeanUtil.copyProperties(record, invoiceItem);
                            if (apiResVo.getInvoiceItems() == null) {
                                apiResVo.setInvoiceItems(new ArrayList<>());
                            }
                            invoiceItem.setReceiveName(AESUtil.encryptECB(record.getReceiveName(), key));
                            invoiceItem.setReceiveIdCardNo(AESUtil.encryptECB(record.getReceiveIdCardNo(), key));
                            invoiceItem.setReceiveAccountNo(AESUtil.encryptECB(record.getReceiveAccountNo(), key));
                            invoiceItem.setReceivePhoneNo(AESUtil.encryptECB(record.getReceivePhoneNo(), key));
                            invoiceItem.setInvoiceFileUrl(record.getInvoiceFileUrlList());
                            apiResVo.getInvoiceItems().add(invoiceItem);
                        }
                    }
                }

                return ResponseDto.success(apiResVo,key);
            }else {
                return ResponseDto.fail(ApiExceptions.API_BIZ_FAIL.getApiErrorCode(), "开票流水号不存在");
            }
        }catch (BizException bizException) {
            log.error("查询开票详情 业务异常：", bizException);
            return ResponseDto.fail(bizException.getApiErrorCode(), bizException.getErrMsg());
        } catch (Exception exception) {
            log.error("查询开票详情 系统异常：", exception);
            return ResponseDto.unknown();
        }
    }
}
