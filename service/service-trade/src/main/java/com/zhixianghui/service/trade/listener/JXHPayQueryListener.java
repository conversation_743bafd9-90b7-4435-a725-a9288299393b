package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.service.trade.biz.JXHLocalPayBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_JXH_PAY_QUERY,
        selectorExpression = MessageMsgDest.TAG_JXH_PAY_QUERY,consumeThreadMax = 20,
        consumerGroup = "JXHPayQueryConsume")
@Slf4j
public class JXHPayQueryListener extends BaseRocketMQListener<String> {

    @Autowired
    private JXHLocalPayBiz jxhLocalPayBiz;
    @Autowired
    private OrderItemBiz orderItemBiz;

    @Override
    public void validateJsonParam(String msg) {
        if(StringUtils.isEmpty(msg)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("君享汇订单号不能为空");
        }
    }

    @Override
    public void consumeMessage(String msg) {
        PayReqVo payReqVo = JsonUtil.toBean(msg, PayReqVo.class);
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(payReqVo.getPlatTrxNo());
        log.info("进入君享汇支付回查:{}", msg);
        if (orderItem.getOrderItemStatus().equals(OrderItemStatusEnum.GRANTING.getValue())) {
            jxhLocalPayBiz.payQuery(payReqVo);
        }
    }
}