package com.zhixianghui.service.trade.biz;

import com.google.common.collect.Maps;
import com.zhixianghui.facade.trade.entity.MchPlatBatch;
import com.zhixianghui.service.trade.dao.MchPlatBatchDao;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* 商户平台批次映射Biz
*
* <AUTHOR>
* @version 创建时间： 2020-11-06
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MchPlatBatchBiz {

    private final MchPlatBatchDao mchPlatBatchDao;

    public void insert(MchPlatBatch mchPlatBatch) {
        mchPlatBatchDao.insert(mchPlatBatch);
    }

    /**
     * 根据商户批次号获取平台批次号
     * @param mchBatchNo 商户批次号
     * @param employerNo 商户编号(null 忽略)
     * @return 平台批次号
     */
    public List<MchPlatBatch> listPlatBatchNoByMchBatchNo(String mchBatchNo,String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mchBatchNo",mchBatchNo);
        if(StringUtils.isNotBlank(employerNo)){
            paramMap.put("employerNo",employerNo);
        }
        return mchPlatBatchDao.listBy(paramMap);
    }

    /**
     * 根据商户批次号和用工企业编号删除记录
     * @param mchBatchNo 商户批次号
     * @param employerNo 用工企业编号
     */
    public void deleteByMchBatchNoAndEmployerNo(String mchBatchNo, String employerNo) {
        if (StringUtils.isBlank(mchBatchNo) || StringUtils.isBlank(employerNo)) {
            throw new IllegalArgumentException("删除条件不能为空，mchBatchNo: " + mchBatchNo + ", employerNo: " + employerNo);
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("mchBatchNo", mchBatchNo);
        paramMap.put("employerNo", employerNo);
        mchPlatBatchDao.deleteBy(paramMap);
    }
}