package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.enums.AdjustmentEnum;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.service.trade.dao.ChangesFundsDao;
import com.zhixianghui.service.trade.dao.WxMerchantBalanceDao;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2021-12-07
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class WxMerchantBalanceBiz {

    private final WxMerchantBalanceDao wxmerchantbalanceDao;

    private final ChangesFundsDao changesFundsDao;

    private final RedisLock redisLock;

    private final PlatformTransactionManager platformTransactionManager;

    private final TransactionDefinition transactionDefinition;

    public Long getAmount(WxMerchantBalance wxMerchantBalance) {
        log.info("查询微信余额参数------------------------>{}",wxMerchantBalance);
        WxMerchantBalance merchantBalance= wxmerchantbalanceDao.getOne(BeanUtil.toMap(wxMerchantBalance));
        if(ObjectUtils.isEmpty(merchantBalance)){
            log.info("微信资金表暂未有来账，返回金额0");
            return 0L;
        }
        long amount=merchantBalance.getTotalAmount()-merchantBalance.getFreezeAmount();
        log.info("余额查询成功，查询余额为:{}",amount);
        return amount;
    }

    public WxMerchantBalance getOne(WxMerchantBalance wxMerchantBalance) {
        return wxmerchantbalanceDao.getOne(BeanUtil.toMap(wxMerchantBalance));
    }

    public void update(WxMerchantBalance wxMerchantBalance) {
        wxmerchantbalanceDao.updateIfNotNull(wxMerchantBalance);
    }

    private void actualChangeAmount(ChangesFunds changesFunds){
        Map<String,Object> map = new HashMap<>();
        map.put("mchNo",changesFunds.getMchNo());
        map.put("mainstayNo",changesFunds.getMainstayNo());
        map.put("merchantType",changesFunds.getMerchantType());
        WxMerchantBalance wxMerchantBalance = wxmerchantbalanceDao.getOne(map);
        if(ObjectUtils.isEmpty(wxMerchantBalance)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该商户没有来账记录");
        }
        //计算变动前变动金额
        changesFunds.setBeforeAmount(wxMerchantBalance.getTotalAmount())
                .setBeforeFrozenAmount(wxMerchantBalance.getFreezeAmount())
                .setAfterAmount(wxMerchantBalance.getTotalAmount() + changesFunds.getAmount())
                .setAfterFrozenAmount(wxMerchantBalance.getFreezeAmount() + changesFunds.getFrozenAmount());
        //插入资金变动表
        changesFundsDao.insert(changesFunds);
        log.info("[订单号:{}，变动类型：{}],资金开始扣减，查询商户号[{}]，供应商编号：[{}]，总金额：{}，冻结金额：{}"
                , changesFunds.getPlatTrxNo(),changesFunds.getAmountChangeType(), changesFunds.getMchNo(),changesFunds.getMainstayNo()
                , wxMerchantBalance.getTotalAmount(), wxMerchantBalance.getFreezeAmount());
        wxMerchantBalance.setTotalAmount(changesFunds.getAfterAmount());
        wxMerchantBalance.setFreezeAmount(changesFunds.getAfterFrozenAmount());
        wxMerchantBalance.setUpdateTime(new Date());
        wxmerchantbalanceDao.update(wxMerchantBalance);
        log.info("[订单号:{}，变动类型：{}],资金扣减完成，查询商户号[{}]，供应商编号：[{}]，总金额：{}，冻结金额：{}"
                , changesFunds.getPlatTrxNo(),changesFunds.getAmountChangeType(), changesFunds.getMchNo(),changesFunds.getMainstayNo()
                , wxMerchantBalance.getTotalAmount(), wxMerchantBalance.getFreezeAmount());
    }

    /**
     * 处理余额，自行在外部加锁和事务
     * @param changesFunds
     * @param rLock
     */
    public void changeAmount(ChangesFunds changesFunds,RLock rLock){
        actualChangeAmount(changesFunds);
    }

    /**
     * 根据入账记录处理余额
     * 注意锁释放和事务的顺序
     * @param changesFunds
     */
    public void changeAmount(ChangesFunds changesFunds) {
        String redisLockKey = WxUtil.getRedisLockKey(changesFunds.getMchNo(), changesFunds.getMainstayNo(),changesFunds.getMerchantType());
        RLock rLock = redisLock.tryLock(redisLockKey,WxUtil.LOCK_WAIT_TIME,WxUtil.LOCK_LEASE_TIME);
        log.info("[商户余额计算,订单号:{}]==>获取锁:{}",changesFunds.getPlatTrxNo(),redisLockKey);
        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        if(rLock == null){
            log.info("[商户余额计算]==>获取锁失败，直接丢弃，logKey：[{}]，订单号：[{}]，资金变动信息：[{}]",changesFunds.getLogKey(),changesFunds.getPlatTrxNo(),JSON.toJSONString(changesFunds));
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try{
            actualChangeAmount(changesFunds);
            //提交事务
            platformTransactionManager.commit(transaction);
        }catch (DuplicateKeyException e){
            log.info("该记录已入账，直接跳过，商户号：[{}],logKey：[{}]，交易记录：[{}]",changesFunds.getMchNo(),changesFunds.getLogKey(),JSON.toJSONString(changesFunds));
            platformTransactionManager.rollback(transaction);
            throw e;
        }catch (Exception e){
            platformTransactionManager.rollback(transaction);
            throw e;
        } finally{
            redisLock.unlock(rLock);
        }
    }

    public void createAccount(ReportEntity reportEntity) {
        WxMerchantBalance wxMerchantBalance = new WxMerchantBalance();
        wxMerchantBalance.setVersion(0)
                .setMerchantType(reportEntity.getMerchantType())
                .setMchNo(reportEntity.getEmployerNo())
                .setMchName(reportEntity.getEmployerName())
                .setMainstayNo(reportEntity.getMainstayNo())
                .setMainstayName(reportEntity.getMainstayName())
                .setTotalAmount(0L)
                .setFreezeAmount(0L)
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
        try {
            wxmerchantbalanceDao.insert(wxMerchantBalance);
        }catch (DuplicateKeyException e){
            log.info("商户：[{}]，供应商[{}],已生成过，直接忽略",reportEntity.getEmployerNo(),reportEntity.getMainstayNo());
        }
    }


//    @Transactional(rollbackFor = Exception.class)
    public void adjustment(AdjustmentDTO adjustmentDTO) {
        log.info("进入微信调账，供应商{}，用工企业：{}，调账金额：{},调账类型:{}"
                ,adjustmentDTO.getMainstayNo(),adjustmentDTO.getMchNo()
                ,adjustmentDTO.getAmount(),adjustmentDTO.getType());
        ChangesFunds changesFunds=new ChangesFunds();
        changesFunds.setLogKey(adjustmentDTO.getLogKey());
        changesFunds.setPlatTrxNo(adjustmentDTO.getLogKey());
        Integer merchantType= StringUtil.isEmpty(adjustmentDTO.getMchNo())?MerchantTypeEnum.MAINSTAY.getValue():
                MerchantTypeEnum.EMPLOYER.getValue();
        changesFunds.setMerchantType(merchantType);
        changesFunds.setMchNo(adjustmentDTO.getMchNo());
        changesFunds.setMchName(adjustmentDTO.getMchName());
        changesFunds.setMainstayNo(adjustmentDTO.getMainstayNo());
        changesFunds.setMainstayName(adjustmentDTO.getMainstayName());
        changesFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.ADJUSTMENT.getValue());
        Long ammount=null;
        if(adjustmentDTO.getType()== AdjustmentEnum.ADD.getValue()){
            ammount=AmountUtil.changeToFen(adjustmentDTO.getAmount());
        }else if(adjustmentDTO.getType()== AdjustmentEnum.DEL.getValue()) {
            ammount=-AmountUtil.changeToFen(adjustmentDTO.getAmount());
        }else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("调账参数异常");
        }
        changesFunds.setAmount(ammount);
        changesFunds.setFrozenAmount(0L);
        changesFunds.setCreateTime(new Date());
        changesFunds.setOperator(adjustmentDTO.getOperator());
        this.changeAmount(changesFunds);
    }
}