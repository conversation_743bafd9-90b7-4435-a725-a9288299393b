package com.zhixianghui.service.trade.biz;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.service.wx.WxPayFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.banklink.vo.wx.WxIncomeRecordVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.entity.WxIncomeRecord;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;
import com.zhixianghui.facade.trade.enums.*;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.facade.trade.vo.WxAmountChangeLogVo;
import com.zhixianghui.service.trade.dao.WxIncomeRecordDao;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2021-12-07
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WxIncomeRecordBiz {

    private final WxIncomeRecordDao dao;

    private final RedisClient redisClient;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    private WxPayFacade wxPayFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @Reference
    private SequenceFacade sequenceFacade;

    @Reference
    private RobotFacade robotFacade;

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    private final WxMerchantBalanceBiz wxMerchantBalanceBiz;

    private final OrderBiz orderBiz;

    private final PlatformTransactionManager platformTransactionManager;

    private final TransactionDefinition transactionDefinition;

    private final RedisLock redisLock;

    private final AccountQueryBiz accountQueryBiz;

    private final RechargeRecordBiz rechargeRecordBiz;

    private static final Long MAX_RETRY_TIME = 2L;

    private Long getAcceptTimes(String mainstayNo) {
        Long acceptTime = 0L;
        try {
            acceptTime = redisClient.incr("WX_INCOME_RECORD_RETRY:"+ mainstayNo);
            redisClient.expire("WX_INCOME_RECORD_RETRY:"+ mainstayNo,60*30);
        }catch (Exception e){
            log.error("[繁忙重试环节: {}] ==> redis获取重试次数异常 忽略", mainstayNo, e);
        }
        return acceptTime;
    }


    public void getIncomeRecord(List<MainstayChannelRelation> mainstayChannelRelationList,String now) {
        for (MainstayChannelRelation mainstayChannelRelation : mainstayChannelRelationList) {
            try {
                getIncomeRecord(mainstayChannelRelation.getMainstayNo(),mainstayChannelRelation.getMainstayName(),
                        mainstayChannelRelation.getChannelMchNo(),now);
            }catch (Exception e){
                log.info("获取来账通知异常，供应商编号：[{}]，日期：[{}]",mainstayChannelRelation.getMainstayNo(),new Date());
                if (getAcceptTimes(mainstayChannelRelation.getMainstayNo()) <= MAX_RETRY_TIME){
                    log.info("获取来账通知异常，2分钟后重试处理，供应商编号：[{}]，日期：[{}]",mainstayChannelRelation.getMainstayNo(),now);
                    mainstayChannelRelation.setIncomeRecordTime(now);
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_INCOME_RECORD_ERROR, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(),
                            MessageMsgDest.TAG_WX_INCOME_RECORD_ERROR, JSON.toJSONString(mainstayChannelRelation) , MsgDelayLevelEnum.M_2.getValue());
                }
                continue;
            }
        }
    }

    /**
     * 获取来账通知，每次请求接口获取20个
     * @param mainstayNo
     * @param mainstayName
     * @param subMchId
     * @param now
     * @throws Exception
     */
    private void getIncomeRecord(String mainstayNo,String mainstayName,String subMchId, String now) throws Exception {
        boolean hasNext;
        int offset = 0;
        do {
            WxResVo resVo = wxPayFacade.getIncomeRecord(subMchId,now,offset);
            if (!resVo.isSuccess()){
                log.error("请求来账通知接口异常，特约商户号：[{}]，返回信息：[{}]",subMchId, JSON.toJSONString(resVo));
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求来账通知接口异常");
            }
            WxIncomeRecordVo wxIncomeRecordResVo = JsonUtil.toBeanCamel(resVo.getResponBody(), WxIncomeRecordVo.class);
            insertIncomeRecord(mainstayNo,mainstayName,wxIncomeRecordResVo.getData());
            hasNext = StringUtils.isNotBlank(wxIncomeRecordResVo.getLinks().getNext());
            offset+=wxIncomeRecordResVo.getLimit();
        }while (hasNext);
    }

    /**
     * 插入数据
     * @param mainstayNo
     * @param mainstayName
     * @param dataList
     */
    private void insertIncomeRecord(String mainstayNo,String mainstayName,List<WxIncomeRecordVo.BodyData> dataList) {
        for (WxIncomeRecordVo.BodyData data : dataList) {
            WxIncomeRecord wxIncomeRecord = new WxIncomeRecord();
            BeanUtils.copyProperties(data,wxIncomeRecord);
            wxIncomeRecord.setMainstayNo(mainstayNo)
                    .setMainstayName(mainstayName)
                    .setSuccessTime(new DateTime(data.getSuccessTime()).toJdkDate())
                    .setCreateTime(new Date())
                    .setUdpateTime(new Date())
                    .setStatus(WxIncomeRecordEnum.PENDING.getValue())
                    .setIncomeType(WxIncomeTypeEnum.getEnum(data.getIncomeRecordType()).getValue())
                    .setVersion(0)
                    .setSubMchId(data.getSubMchid());
            String auto = handler(wxIncomeRecord);
            try {
                dao.insert(wxIncomeRecord);
            }catch (DuplicateKeyException e){
                log.info("来账通知已经存在，银行来账微信单号：[{}]",wxIncomeRecord.getIncomeRecordId());
                continue;
            }

            if (StringUtil.isBlank(auto)){
                auto = autoEnterAccount(auto,wxIncomeRecord);
            }
            //发送机器人提醒
            pushToRobot(wxIncomeRecord,auto);
        }
    }

    private String autoEnterAccount(String auto,WxIncomeRecord wxIncomeRecord){
        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        String redisLockKey = WxUtil.getRedisLockKey(wxIncomeRecord.getMchNo(), wxIncomeRecord.getMainstayNo(),MerchantTypeEnum.EMPLOYER.getValue());
        RLock rLock = redisLock.tryLock(redisLockKey,WxUtil.LOCK_WAIT_TIME,WxUtil.LOCK_LEASE_TIME);
        try{
            //处理自动入账
            ChangesFunds changesFunds = buildChangesFund(wxIncomeRecord,wxIncomeRecord.getIncomeRecordId(),"自动入账");
            wxMerchantBalanceBiz.changeAmount(changesFunds,rLock);

            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("incomeRecordId",wxIncomeRecord.getIncomeRecordId());
            wxIncomeRecord = dao.getOne(paramMap);
            wxIncomeRecord.setStatus(WxIncomeRecordEnum.ACTIVE.getValue());
            wxIncomeRecord.setUpdator("自动入账");
            dao.update(wxIncomeRecord);
            //插入交易记录表
            RechargeRecord rechargeRecord = buildRechargeRecord(changesFunds,wxIncomeRecord);
            orderBiz.addRechargeRecord(rechargeRecord);
            rechargeRecordBiz.notifyRechargeMerchant(rechargeRecord);
            platformTransactionManager.commit(transaction);
        }catch (DuplicateKeyException e){
            //提交事务
            platformTransactionManager.rollback(transaction);
        }catch (Exception e){
            log.error("自动入账失败，来账单号：[{}]",wxIncomeRecord.getIncomeRecordId());
            auto = "自动入账失败";
            //提交事务
            platformTransactionManager.rollback(transaction);
        }finally {
            redisLock.unlock(rLock);
        }
        return auto;
    }

    private ChangesFunds buildChangesFund(WxIncomeRecord wxIncomeRecord,String logKey,String handler) {
        ChangesFunds changesFunds = new ChangesFunds();
        changesFunds.setVersion(0)
                .setMerchantType(MerchantTypeEnum.EMPLOYER.getValue())
                .setLogKey(logKey)
                .setMchNo(wxIncomeRecord.getMchNo())
                .setMchName(wxIncomeRecord.getMchName())
                .setMainstayNo(wxIncomeRecord.getMainstayNo())
                .setMainstayName(wxIncomeRecord.getMainstayName())
                .setAmountChangeType(WxAmountChangeLogTypeEnum.DEPOSIT.getValue())
                .setAmount(wxIncomeRecord.getAmount())
                .setFrozenAmount(0L)
                .setCreateTime(new Date())
                .setOperator(handler);
        return changesFunds;
    }

    //判断是否自动入账
    private String handler(WxIncomeRecord wxIncomeRecord) {
        if (StringUtils.isBlank(wxIncomeRecord.getBankAccountName())){
            return "入账银行户名为空，自动入账失败";
        }
        //查询商户
        List<Merchant> list = merchantQueryFacade.listBy(new HashMap<String,Object>(){{put("mchName",wxIncomeRecord.getBankAccountName());}});
        if (list.size() == 0){
            return "找不到对应的商户，自动入账失败";
        }
        if (list.size() > 1){
            return "存在多个同名商户，自动入账失败";
        }
        //获取虚拟账户表数据
        WxMerchantBalance wxMerchantBalance = new WxMerchantBalance();
        wxMerchantBalance.setMainstayNo(wxIncomeRecord.getMainstayNo());
        wxMerchantBalance.setMchNo(list.get(0).getMchNo());
        wxMerchantBalance.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        wxMerchantBalance = wxMerchantBalanceBiz.getOne(wxMerchantBalance);
        if (wxMerchantBalance == null){
            return "商户未进行微信通道报备，自动入账失败";
        }
        wxIncomeRecord.setMchNo(list.get(0).getMchNo());
        wxIncomeRecord.setMchName(list.get(0).getMchName());
        return null;

    }

    /**
     * 发送机器人提醒
     * @param wxIncomeRecord
     */
    public void pushToRobot(WxIncomeRecord wxIncomeRecord,String msg) {
        MarkDownMsg markDownMsg = new MarkDownMsg();
        markDownMsg.setUnikey(wxIncomeRecord.getIncomeRecordId());
        markDownMsg.setRobotType(RobotTypeEnum.WX_INCOME_ROBOT.getType());
        StringBuffer sb = new StringBuffer("#### 微信来账通知，请相关同事注意\\n ");
        sb.append("> 银行来账微信单号：").append(wxIncomeRecord.getIncomeRecordId())
                .append("\\n > 代征主体名称：").append(wxIncomeRecord.getMainstayName())
                .append("\\n > 付款银行名称：").append(wxIncomeRecord.getBankName())
                .append("\\n > 付款银行账户名：").append(wxIncomeRecord.getBankAccountName())
                .append("\\n > 付款卡号：").append(wxIncomeRecord.getBankAccountNumber())
                .append("\\n > 交易金额：").append(AmountUtil.changeToYuan(wxIncomeRecord.getAmount()))
                .append("\\n > 银行来账完成时间：").append(DateUtil.formatDateTime(wxIncomeRecord.getSuccessTime()));

        if (StringUtil.isBlank(msg)){
            sb.append("\\n > 入账信息：<font color=\\\"warning\\\">已申请自动入账，稍后请查看记录</font>");
        }else{
            sb.append("\\n > 入账信息：<font color=\\\"warning\\\">" + msg + "</font>");
        }
        markDownMsg.setContent(sb.toString());
        robotFacade.pushMarkDownAsync(markDownMsg);
    }

    public WxIncomeRecord getById(Long id) {
        return dao.getById(id);
    }

    public void update(WxIncomeRecord wxIncomeRecord) {
        dao.update(wxIncomeRecord);
    }

    public PageResult<List<WxIncomeRecord>> listPage(Map<String, Object> paramMap, PageParam toPageParam) {
        return dao.listPage(paramMap,toPageParam);
    }

    public void confirmIncomeRecord(WxAmountChangeLogVo wxAmountChangeLogVo) {
        WxIncomeRecord wxIncomeRecord = dao.getById(wxAmountChangeLogVo.getIncomeId());
        if (wxIncomeRecord.getStatus().intValue() != WxIncomeRecordEnum.INCOMING.getValue()){
            log.info("来账记录不处于处理中状态，直接跳过，商户号：[{}],银行来账微信单号：[{}]",wxIncomeRecord.getMchNo(),wxIncomeRecord.getIncomeRecordId());
            return;
        }
        wxIncomeRecord.setStatus(WxIncomeRecordEnum.ACTIVE.getValue());
        wxIncomeRecord.setUpdator(wxAmountChangeLogVo.getHandler());
        dao.update(wxIncomeRecord);
        //处理入账
        ChangesFunds changesFunds = buildChangesFund(wxIncomeRecord,wxAmountChangeLogVo.getLogKey(),wxAmountChangeLogVo.getHandler());

        wxMerchantBalanceBiz.changeAmount(changesFunds);
        //插入交易记录表
        RechargeRecord rechargeRecord = buildRechargeRecord(changesFunds,wxAmountChangeLogVo,wxIncomeRecord);
        orderBiz.addRechargeRecord(rechargeRecord);
        rechargeRecordBiz.notifyRechargeMerchant(rechargeRecord);
    }

    private RechargeRecord buildRechargeRecord(ChangesFunds changesFunds,WxIncomeRecord wxIncomeRecord){
        RechargeRecord rechargeRecord = new RechargeRecord();
        String rechargeOrderId = LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) +
                sequenceFacade.nextRedisId(SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getKey(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getWidth());

        //获取通道信息
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(changesFunds.getMchNo(),changesFunds.getMainstayNo(),ChannelNoEnum.WXPAY.name());

        rechargeRecord.setRechargeOrderId(rechargeOrderId)
                .setRechargeAmount(AmountUtil.changeToYuan(changesFunds.getAmount()))
                .setPayeeIdentity(wxIncomeRecord.getSubMchId())
                .setPayeeIdentityType(IdentityTypeEnum.WX_MERCHANT_NO.getValue())
                .setChannelOrderId(changesFunds.getLogKey())
                .setTransPayTime(wxIncomeRecord.getSuccessTime())
                .setEmployerNo(changesFunds.getMchNo())
                .setEmployerName(changesFunds.getMchName())
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setChannelCode(ChannelNoEnum.WXPAY.name())
                .setChannelName(ChannelNoEnum.WXPAY.getDesc())
                .setChannelType((short) ChannelTypeEnum.WENXIN.getValue())
                .setMainstayName(changesFunds.getMainstayName())
                .setMainstayNo(changesFunds.getMainstayNo())
                .setRechargeStatus(RechargeStatusEnum.SUCCESS.getCode().shortValue())
                .setChannelTrxNo(changesFunds.getLogKey())
                .setPayeeName(changesFunds.getMainstayName())
                .setPayerIdentityType(IdentityTypeEnum.BANK_ACCOUNT_NO.getValue())
                .setPayerName(wxIncomeRecord.getBankAccountName())
                .setPayerBankName(wxIncomeRecord.getBankName())
                .setPayerIdentity(wxIncomeRecord.getBankAccountNumber())
                .setCurrentBalance(accountQueryBiz.getBalance(employerAccountInfo));
        if (wxIncomeRecord.getIncomeType().intValue() == WxIncomeTypeEnum.OFFLINERECHARGE.getValue()){
            rechargeRecord.setRechargeType(RechargeTypeEnum.TRANSFER_RECHARGE.getCode());
        }else{
            rechargeRecord.setRechargeType(RechargeTypeEnum.WX_OFFLINERECHARGE.getCode());
        }
        return rechargeRecord;
    }

    private RechargeRecord buildRechargeRecord(ChangesFunds changesFunds,WxAmountChangeLogVo wxAmountChangeLogVo,WxIncomeRecord wxIncomeRecord) {
        RechargeRecord rechargeRecord = new RechargeRecord();
        String rechargeOrderId = LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) +
                sequenceFacade.nextRedisId(SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getKey(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getWidth());

        //获取通道信息
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(changesFunds.getMchNo(),changesFunds.getMainstayNo(),ChannelNoEnum.WXPAY.name());

        rechargeRecord.setRechargeOrderId(rechargeOrderId)
                .setRechargeAmount(AmountUtil.changeToYuan(changesFunds.getAmount()))
                .setPayeeIdentity(wxAmountChangeLogVo.getSubMchId())
                .setPayeeIdentityType(IdentityTypeEnum.WX_MERCHANT_NO.getValue())
                .setChannelOrderId(changesFunds.getLogKey())
                .setTransPayTime(wxAmountChangeLogVo.getSuccessTime())
                .setEmployerNo(changesFunds.getMchNo())
                .setEmployerName(changesFunds.getMchName())
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setChannelCode(ChannelNoEnum.WXPAY.name())
                .setChannelName(ChannelNoEnum.WXPAY.getDesc())
                .setChannelType((short)ChannelTypeEnum.WENXIN.getValue())
                .setMainstayName(changesFunds.getMainstayName())
                .setMainstayNo(changesFunds.getMainstayNo())
                .setRechargeStatus(RechargeStatusEnum.SUCCESS.getCode().shortValue())
                .setChannelTrxNo(changesFunds.getLogKey())
                .setRemark(wxAmountChangeLogVo.getRemark())
                .setReceiptUrl(wxAmountChangeLogVo.getPayImgUrl())
                .setPayeeName(changesFunds.getMainstayName())
                .setPayerIdentityType(IdentityTypeEnum.BANK_ACCOUNT_NO.getValue())
                .setPayerName(wxIncomeRecord.getBankAccountName())
                .setPayerBankName(wxIncomeRecord.getBankName())
                .setPayerIdentity(wxIncomeRecord.getBankAccountNumber())
                .setCurrentBalance(accountQueryBiz.getBalance(employerAccountInfo));
        if (wxAmountChangeLogVo.getIncomeType().intValue() == WxIncomeTypeEnum.OFFLINERECHARGE.getValue()){
            rechargeRecord.setRechargeType(RechargeTypeEnum.TRANSFER_RECHARGE.getCode());
        }else{
            rechargeRecord.setRechargeType(RechargeTypeEnum.WX_OFFLINERECHARGE.getCode());
        }
        return rechargeRecord;
    }



}
