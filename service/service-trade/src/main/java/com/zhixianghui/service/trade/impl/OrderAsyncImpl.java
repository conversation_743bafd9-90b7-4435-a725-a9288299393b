package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.service.OrderAsyncFacade;
import com.zhixianghui.service.trade.factory.TradeFactory;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-26 11:36
 **/
@Service(async = true)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderAsyncImpl implements OrderAsyncFacade {

    private final TradeFactory tradeFactory;

    @Override
    public void startGrant(Order order) {
        //grantBiz.updateAndInitGrant(order);
        tradeFactory.getGrantor(order.getProductNo()).updateAndInitGrant(order);
    }

    @Override
    public void grantAgain(Order order) {
        //grantBiz.initGrant(order);
        tradeFactory.getGrantor(order.getProductNo()).initGrant(order);
    }

    @Override
    public void acceptAgain(Order order) {
        //acceptBiz.acceptAgain(order);
        tradeFactory.getAcceptor(order.getProductNo()).acceptAgain(order);
    }
}
