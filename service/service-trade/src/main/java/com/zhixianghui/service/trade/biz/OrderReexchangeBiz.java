package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.account.AccountRequestDto;
import com.zhixianghui.common.statics.dto.account.invoice.AccountInvoiceAmountTypeEnum;
import com.zhixianghui.common.statics.dto.account.invoice.AccountInvoiceProcessDto;
import com.zhixianghui.common.statics.enums.account.AccountProcessTypeEnum;
import com.zhixianghui.common.statics.enums.account.AccountSysTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceProcessFacade;
import com.zhixianghui.facade.banklink.service.pay.PayBankFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.pay.QueryPayOrderReqVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.dto.RefundBalanceDTO;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.factory.TradeFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 退汇
 * @createTime 2022年02月10日 14:51:00
 */
@Service
@Slf4j
public class OrderReexchangeBiz {

    @Autowired
    private OrderBiz orderBiz;
    @Autowired
    private OrderItemBiz orderItemBiz;
    @Autowired
    private RecordItemBiz recordItemBiz;
    @Reference
    private NotifyFacade notifyFacade;

    @Reference
    private AccountInvoiceProcessFacade accountInvoiceProcessFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private PayBankFacade payBankFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Autowired
    private TradeFactory tradeFactory;
    @Autowired
    private CmbMerchantBalanceBiz cmbMerchantBalanceBiz;
    @Autowired
    private MchPlatBatchBiz mchPlatBatchBiz;
    @Autowired
    private MchPlatTrxNoBiz mchPlatTrxNoBiz;


    @Transactional(rollbackFor = Exception.class)
    public void reexchange(String platTrxNo) {
        Date date = new Date();

        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
        if (Objects.isNull(orderItem) || orderItem.getOrderItemStatus() != OrderItemStatusEnum.GRANT_SUCCESS.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(String.format("平台流水号：【%s】不可退汇", platTrxNo));
        }
        RecordItem recordItem = recordItemBiz.getByPlatTrxNo(platTrxNo);
        orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_SPECIAL_FAIL.getValue());
        orderItem.setUpdateTime(date);
        recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SPECIAL_FAIL.getValue());
        recordItem.setUpdateTime(date);
        orderItem.setErrorCode(ApiExceptions.API_TRADE_CHANNEL_GRANT_FAIL.getApiErrorCode());
        orderItem.setErrorDesc("通道打款失败-退汇");
        orderItemBiz.update(orderItem);
        recordItemBiz.update(recordItem);
        updateOrder(orderItem);
        adjust(orderItem);
        refundFee(recordItem);
        // 删除索引表数据
        mchPlatBatchBiz.deleteByMchBatchNoAndEmployerNo(orderItem.getMchOrderNo(), orderItem.getEmployerNo());
        mchPlatTrxNoBiz.deleteByMchOrderNoAndEmployerNo(orderItem.getMchOrderNo(), orderItem.getEmployerNo());

        notifyFacade.sendOne(MessageMsgDest.TOPIC_REEXCHANGE, recordItem.getEmployerNo(), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_REEXCHANGE, platTrxNo, MsgDelayLevelEnum.S_1.getValue());
    }

    public void refundFee(RecordItem recordItem) {
        if (StringUtils.equals(recordItem.getPayChannelNo(), ChannelNoEnum.CMB.name())) {
            log.info("【{}】退汇-招行本地账户退款-开始", recordItem.getPlatTrxNo());

            final BigDecimal fee = recordItem.getOrderFee();

            final EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(recordItem.getEmployerNo(), recordItem.getMainstayNo(), recordItem.getPayChannelNo());

            QueryPayOrderReqVo queryPayOrderReqVo = new QueryPayOrderReqVo();
            queryPayOrderReqVo.setBankOrderNo(recordItem.getRemitPlatTrxNo());
            queryPayOrderReqVo.setChannelNo(recordItem.getPayChannelNo());
            queryPayOrderReqVo.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
            queryPayOrderReqVo.setChannelType(employerAccountInfo.getChannelType());

            final PayRespVo payRespVo = payBankFacade.queryPayOrder(queryPayOrderReqVo);
            Integer orderStatus = payRespVo.getBankPayStatus();
            if (orderStatus.intValue() == BankPayStatusEnum.SUCCESS.getValue()) {
                log.info("[反查通道: {} ]==>反查通道返回成功-无需退汇", recordItem.getRemitPlatTrxNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道订单成功，无需退汇");
            }

            try {
                //payBankFacade.refundFee(recordItem.getPlatTrxNo(), recordItem.getPayChannelNo(), employerAccountInfo.getParentMerchantNo(), recordItem.getChannelMchNo(), fee.toPlainString());
                // 招行本地账户退款
                RefundBalanceDTO refundBalance = new RefundBalanceDTO();
                refundBalance.setMchNo(recordItem.getEmployerNo());
                refundBalance.setMchName(recordItem.getEmployerName());
                refundBalance.setMainstayNo(recordItem.getMainstayNo());
                refundBalance.setMainstayName(recordItem.getMainstayName());
                refundBalance.setPlatBatchNo(recordItem.getPlatBatchNo());
                refundBalance.setPlatTrxNo(recordItem.getPlatTrxNo());
                refundBalance.setAmount(recordItem.getOrderNetAmount());
                refundBalance.setFeeAmount(recordItem.getOrderFee());
                cmbMerchantBalanceBiz.refundBalanceWithFee(refundBalance);
            } catch (Exception e) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("招行本地账户退款失败");
            }
            log.info("【{}】退汇-招行本地账户退款-完成", recordItem.getPlatTrxNo());

            log.info("【{}】退汇-通知商户-开始", recordItem.getPlatTrxNo());
            Map<String, Object> orderParam = new HashMap<>();
            orderParam.put("platBatchNo", recordItem.getPlatBatchNo());
            final Order order = orderBiz.getOne(orderParam);

            final OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
            tradeFactory.getGrantor(orderItem.getProductNo()).notifyMchGrantResult(orderItem, order);
            log.info("【{}】退汇-通知商户-完成", recordItem.getPlatTrxNo());
        }
    }

    public void updateOrder(OrderItem orderItem) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("platBatchNo", orderItem.getPlatBatchNo());
        Order order = orderBiz.getOne(paramMap);
        log.info("退汇接口订单批次查询结果：{}", order);
        order.setSuccessCount(order.getSuccessCount() - 1);
        order.setSuccessNetAmount(order.getSuccessNetAmount().subtract(orderItem.getOrderItemNetAmount()));
        order.setSuccessFee(order.getSuccessFee().subtract(orderItem.getOrderItemFee()));
        order.setFailCount(order.getFailCount() + 1);
        order.setFailNetAmount(order.getFailNetAmount().add(order.getFailNetAmount()));
        orderBiz.update(order);
    }


    public void adjust(OrderItem orderItem) {
        String processNo = sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getWidth());

        AccountRequestDto requestDto = new AccountRequestDto();
        requestDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        requestDto.setAccountProcessNo(processNo);
        requestDto.setMustSuccess(true);

        AccountInvoiceProcessDto processDto = new AccountInvoiceProcessDto();
        processDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        processDto.setTrxTime(new Date());
        processDto.setTrxNo(processNo);
        processDto.setMchTrxNo(processNo);
        processDto.setProcessType(AccountProcessTypeEnum.ADJUST_SUB.getValue());
        processDto.setAmountType(AccountInvoiceAmountTypeEnum.INVOICE_AMOUNT.getValue());
        processDto.setEmployerMchNo(orderItem.getEmployerNo());
        processDto.setMainstayMchNo(orderItem.getMainstayNo());
        processDto.setAmount(orderItem.getOrderItemAmount());
        processDto.setProductNo(ProductNoEnum.ACCOUNT_ADJUST.name());
        processDto.setDesc("退汇调减");
        log.info("退汇接口账户调减：{}", processDto);

        accountInvoiceProcessFacade.executeSync(requestDto, processDto);
    }


}
