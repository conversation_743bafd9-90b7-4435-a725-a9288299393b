package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.wx.WxCodeEnum;
import com.zhixianghui.common.statics.enums.wx.WxDetailStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.wx.WxPayFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.facade.trade.vo.WxPayQueryVo;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.service.trade.pay.wx.WxPayContext;
import com.zhixianghui.service.trade.pay.wx.biz.WxNotifyBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月13日 10:39:00
 */
@Service
@Slf4j
public class WxPayQueryBiz {

    @Reference
    private WxPayFacade wxPayFacade;
    @Autowired
    private WxNotifyBiz wxNotifyBiz;
    @Value("${wx.isPay}")
    private boolean isPay;

//    @Transactional(rollbackFor = Exception.class)
    public void payQuery(PayReqVo msg)  {
        String bankOrderNo = msg.getBankOrderNo();
        try {
            WxResVo wxResVo = null;
            //判断是否真实支付
            if(isPay){
                wxResVo=wxPayFacade.getOrder(bankOrderNo);
            }else {
                log.info("[{}]微信支付回调测试，模拟支付冻结",msg.getPlatTrxNo());
                WxPayQueryVo wxPayQueryVo=new WxPayQueryVo();
                wxPayQueryVo.setDetailId("");
                wxPayQueryVo.setOutDetailNo(bankOrderNo);
                wxPayQueryVo.setDetailStatus(WxDetailStatusEnum.SUCCESS.name());
//                wxPayQueryVo.setFailReason(WxFailReasonEnum.ACCOUNT_FROZEN.name());
                wxResVo=new WxResVo(WxCodeEnum.SUCCESS.getCode(), JSONObject.toJSONString(wxPayQueryVo));
//                wxResVo=new WxResVo(WxCodeEnum.NOT_FOUND.getCode(), JSONObject.toJSONString(wxPayQueryVo));
            }
            if (wxResVo.isSuccess()) {
                log.info("[{}]微信支付结果查询，查询成功", msg.getPlatTrxNo());
                String responBody = wxResVo.getResponBody();
                WxPayQueryVo wxPayQueryVo = JsonUtil.toBeanCamel(responBody, WxPayQueryVo.class);
                log.info("[{}]微信支付结果查询，返回结果:{}", msg.getPlatTrxNo(), JSONObject.toJSONString(wxPayQueryVo));
                WxPayParam wxPayParam = WxPayContext.buildParam(wxPayQueryVo, msg);
                WxPayContext.handle(wxPayParam);
            } else if (wxResVo.isFrequently()) {
                wxNotifyBiz.retry(msg);
            } else if (wxResVo.isNotFound()) {
                wxNotifyBiz.retryNotFond(msg);
            } else {
                log.error("[{}]微信查询异常,状态码：{},描述：{}", msg.getPlatTrxNo(), wxResVo.getCode(), WxUtil.getCodeDesc(wxResVo.getCode()));
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("微信查询异常");
            }
        }catch (Exception e){
//            e.printStackTrace();
            log.error("[{}]微信查询系统异常", msg.getPlatTrxNo(), e);
        }
    }



}