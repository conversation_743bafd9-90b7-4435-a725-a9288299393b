package com.zhixianghui.service.trade.vo.res;

import com.alibaba.fastjson.annotation.JSONField;
import com.zhixianghui.facade.trade.entity.WechatInfo;
import com.zhixianghui.facade.trade.entity.WechatUserInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/2/14 14:58
 */
@Data
public class Code2SessionResVo implements Serializable {
    @JSONField(name = "openid")
    private String openId;
    @JSONField(name = "session_key")
    private String sessionKey;
    @JSONField(name = "unionid")
    private String unionId;
    @JSONField(name = "errcode")
    private Integer errCode;
    @JSONField(name = "errmsg")
    private String errMsg;

    public WechatInfo toWechatInfo() {
        WechatInfo info = new WechatInfo();
        info.setWxUnionid(this.unionId);
        info.setMiniOpenId(this.openId);
        info.setCreateAt(new Date());
        info.setLastLoginAt(new Date());
        info.setUpdateAt(new Date());
        return info;
    }
}
