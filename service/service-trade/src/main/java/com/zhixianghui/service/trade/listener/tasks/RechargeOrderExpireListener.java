package com.zhixianghui.service.trade.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.service.trade.biz.RechargeRecordBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_RECHARGE_EXPIRE, consumeThreadMax = 1, consumerGroup = "rechargeOrderExpire")
public class RechargeOrderExpireListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private RechargeRecordBiz rechargeRecordBiz;

    @Override
    public void runTask(JSONObject jsonParam) {
        rechargeRecordBiz.expireRechargeRecord(jsonParam.getInteger("pageSize"));
    }
}