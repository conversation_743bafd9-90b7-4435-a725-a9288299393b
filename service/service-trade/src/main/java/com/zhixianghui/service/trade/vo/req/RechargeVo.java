package com.zhixianghui.service.trade.vo.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.enums.IdentityTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class RechargeVo implements Serializable {
    private static final long serialVersionUID = -230308422594408768L;
    private String rechargeOrderId;
    private String channelMchNo;
    private String platTrxNo;
    // 汇聚系统针对此次请求的处理流水号
    @JSONField(name = "trx_no")
    private String trxNo;
    // 虚拟账户收款
    @JSONField(name = "pay_way")
    private String payWay;
    // 虚拟账户收款
    @JSONField(name = "product_name")
    private String productName;
    // 交易状态(成功，失败)
    @JSONField(name = "income_status")
    private String incomeStatus;
    // 到账金额(是指用户通过银行转到汇聚的金额)
    @JSONField(name = "receive_amount")
    private String receiveAmount;
    // 交易手续费(是指此次交易收取的手续费)
    @JSONField(name = "income_fee")
    private String incomeFee;
    //
    /**
     * 是指账务实际入账的金额
     * 若是实时：receive_amount-income_fee = income_amount
     * 若是后收/预付：receive_amount = income_amount
     */
    @JSONField(name = "income_amount")
    private String incomeAmount;
    // 交易入账完成的时间，格式：YYYY-MM-DD HHMMSS
    @JSONField(name = "pay_time")
    private String payTime;
    // 系统入账流水号(对应汇聚内部的调账流水号)
    @JSONField(name = "income_trx_no")
    private String incomeTrxNo;
    // 付款人名称
    @JSONField(name = "payer_account_name")
    private String payerAccountName;
    // 付款人账号
    @JSONField(name = "payer_account_no")
    private String payerAccountNo;
    // 收款人账号 333账号
    @JSONField(name = "payee_account_no")
    private String payeeAccountNo;
    // 收款账号收款户名
    @JSONField(name = "payee_account_name")
    private String payeeAccountName;

    @JSONField(name = "payer_account_bank")
    private String payerAccountBank;

    public RechargeRecord toRechargeRecord(RechargeVo rechargeVo, String rechargeOrderId, EmployerAccountInfo accountInfo) {

        Date now = new Date();
        Date expireDateTime = DateUtil.addMinute(now, 60);

        RechargeRecord rechargeRecord = new RechargeRecord();
        rechargeRecord.setRechargeOrderId(rechargeOrderId);
        rechargeRecord.setChannelName(accountInfo.getPayChannelName());
        rechargeRecord.setChannelType(accountInfo.getChannelType().shortValue());
        rechargeRecord.setRechargeAmount(new BigDecimal(rechargeVo.getIncomeAmount()));
        rechargeRecord.setCreateTime(now);
        rechargeRecord.setTransPayTime(DateUtil.parseTime(rechargeVo.getPayTime()));
        rechargeRecord.setChannelCode(accountInfo.getPayChannelNo());
        rechargeRecord.setExpireTime(expireDateTime);
        rechargeRecord.setUpdateTime(now);
        rechargeRecord.setEmployerNo(accountInfo.getEmployerNo());
        rechargeRecord.setEmployerName(accountInfo.getEmployerName());
        rechargeRecord.setMainstayNo(accountInfo.getMainstayNo());
        rechargeRecord.setMainstayName(accountInfo.getMainstayName());
        rechargeRecord.setAccountBookId(accountInfo.getSubMerchantNo());
        rechargeRecord.setPayeeAgreementNo(accountInfo.getSubAgreementNo());
        rechargeRecord.setPayeeIdentity(rechargeVo.getPayeeAccountNo());
        rechargeRecord.setPayeeIdentityType(IdentityTypeEnum.JOINPAY_ACCOUNT.getValue());
        rechargeRecord.setChannelTrxNo(rechargeVo.getIncomeTrxNo());

        rechargeRecord.setPayerName(rechargeVo.getPayerAccountName());
        rechargeRecord.setPayerIdentity(rechargeVo.getPayerAccountNo());
        rechargeRecord.setPayerIdentityType(IdentityTypeEnum.BANK_ACCOUNT_NO.getValue());
        rechargeRecord.setPayerBankName(rechargeVo.getPayerAccountBank());

        rechargeRecord.setPayeeName(rechargeVo.getPayeeAccountName());
        return rechargeRecord;
    }
}
