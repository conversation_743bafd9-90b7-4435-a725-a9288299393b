package com.zhixianghui.service.trade.biz;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.yishui.YishuiFacade;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.enums.BalancedEnum;
import com.zhixianghui.facade.merchant.service.MerchantBankAccountFacade;
import com.zhixianghui.facade.merchant.service.MerchantCkhQuoteFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.FeeOrderBatchVo;
import com.zhixianghui.facade.trade.entity.*;
import com.zhixianghui.facade.trade.enums.*;
import com.zhixianghui.facade.trade.vo.FeeOrderCountVo;
import com.zhixianghui.facade.trade.vo.FeeOrderSumVo;
import com.zhixianghui.facade.trade.vo.FeeOrderVo;
import com.zhixianghui.service.trade.dao.FeeOrderBatchDao;
import com.zhixianghui.service.trade.dao.FeeOrderItemDao;
import com.zhixianghui.service.trade.dao.OrderDao;
import com.zhixianghui.service.trade.dao.OrderItemDao;
import com.zhixianghui.service.trade.dao.mapper.OfflineOrderMapper;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2022-07-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class FeeOrderBatchBiz {

    private final OfflineOrderMapper offlineOrderMapper;

    private final OfflineOrderItemBiz offlineOrderItemBiz;

    private final FeeOrderBatchDao feeorderbatchDao;

    private final FeeOrderItemDao feeOrderItemDao;

    private final OrderDao orderDao;

    private final TradeHelperBiz tradeHelperBiz;

    private final PlatformTransactionManager platformTransactionManager;

    private final TransactionDefinition transactionDefinition;

    private final OrderItemDao orderItemDao;
    @Reference
    private MerchantCkhQuoteFacade merchantCkhQuoteFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MerchantBankAccountFacade merchantBankAccountFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private YishuiFacade yishuiFacade;

    public PageResult listPage(FeeOrderVo feeOrderVo, PageParam pageParam) {
        feeOrderVo.setSortColumns("create_date desc");
        return feeorderbatchDao.listPage(BeanUtil.toMap(feeOrderVo), pageParam);
    }

    public void syncFeeOrder(Date date,FeeSourceEnum feeSourceEnum) {
        final Date currentDate = new Date();
        final Date yesterday = Optional.ofNullable(date).orElse(DateUtil.addDay(currentDate, -1));
        final Date startTime = DateUtil.getDayStart(yesterday);
        final Date endTime = DateUtil.getDayEnd(yesterday);
        log.info("开始同步{}账单,开始时间：{}，结束时间：{}", feeSourceEnum.getDesc(),startTime, endTime);
        List<FeeOrderBatch> feeOrderBatches = getOrderData(startTime,endTime,feeSourceEnum);
        log.info("查询同步{}账单：{}", feeSourceEnum.getDesc(),feeOrderBatches);
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        feeOrderBatches.forEach(feeOrderBatch -> {
            executorService.execute(() -> {
                TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
                try {
                    Map<String, Object> paramsMap = new HashMap<>();
                    paramsMap.put("createDate", startTime);
                    paramsMap.put("employerNo", feeOrderBatch.getEmployerNo());
                    paramsMap.put("mainstayNo", feeOrderBatch.getMainstayNo());
                    paramsMap.put("productNo", feeOrderBatch.getProductNo());
                    paramsMap.put("payChannelNo", feeOrderBatch.getPayChannelNo());
                    paramsMap.put("channelType", feeOrderBatch.getChannelType());
                    paramsMap.put("channelName", feeOrderBatch.getChannelName());
                    paramsMap.put("feeSource", feeSourceEnum.getValue());
                    FeeOrderBatch res = feeorderbatchDao.getOne(paramsMap);
                    if (!ObjectUtils.isEmpty(res)) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[" + res.getFeeBatchNo() + "]账单数据重复，跳过执行");
                    }

                    MerchantEmployerQuote merchantEmployerQuote = new MerchantEmployerQuote();
                    merchantEmployerQuote.setMchNo(feeOrderBatch.getEmployerNo());
                    merchantEmployerQuote.setMainstayMchNo(feeOrderBatch.getMainstayNo());
                    merchantEmployerQuote.setProductNo(ProductNoEnum.CKH.getValue());
                    merchantEmployerQuote.setStatus(MerchantQuoteStatusEnum.ACTIVE.getValue());
                    Integer mode = Optional.ofNullable(merchantCkhQuoteFacade.getBalancedMode(merchantEmployerQuote))
                            .orElse(BalancedEnum.OFFLINE_BALANCED.getCode());
//                    Integer mode = BalancedEnum.ONLINE_BALANCED.getCode();
                    feeOrderBatch.setBalancedMode(mode);
                    feeOrderBatch.setCreateDate(startTime);
                    feeOrderBatch.setVersion(0);
                    if (feeOrderBatch.getFeeAmount().compareTo(BigDecimal.ZERO) > 0 || feeOrderBatch.getTaxAmount().compareTo(BigDecimal.ZERO) > 0){
                        feeOrderBatch.setStatus(mode.equals(BalancedEnum.ONLINE_BALANCED.getCode()) ?
                                FeeOrderStatus.PAYING.getCode() : FeeOrderStatus.NO_PAY.getCode());
                    }else{
                        feeOrderBatch.setStatus(FeeOrderStatus.SUCCESS.getCode());
                    }
                    feeOrderBatch.setCreateTime(currentDate);
                    feeOrderBatch.setFeeBatchNo(getNo(SequenceBizKeyEnum.CKH_FEE_ORDER_SEQ));
                    feeOrderBatch.setFeeSource(feeSourceEnum.getValue());
                    FeeOrderItem serviceItem = createServiceItem(feeOrderBatch,feeSourceEnum);
                    FeeOrderItem texItem = createTaxItem(feeOrderBatch,feeSourceEnum);
                    feeorderbatchDao.insert(feeOrderBatch);
                    feeOrderItemDao.insert(serviceItem);
                    feeOrderItemDao.insert(texItem);
                    log.info("{}账单插入,批次号:{},服务费账单:({},{}),个税账单:({},{})",feeSourceEnum.getDesc(), feeOrderBatch.getFeeBatchNo()
                            , serviceItem.getFeeItemNo(), serviceItem.getPayAmount()
                            , texItem.getFeeItemNo(), texItem.getPayAmount());
                    if (feeOrderBatch.getBalancedMode().equals(BalancedEnum.ONLINE_BALANCED.getCode()) &&
                            feeOrderBatch.getStatus().intValue() == FeeOrderStatus.PAYING.getCode()&&
                            feeOrderBatch.getChannelType().intValue()== ChannelTypeEnum.ALIPAY.getValue()) {
                        //支付服务费
                        notifyFacade.sendOne(MessageMsgDest.TOPIC_FEE_ORDER_ITEM_PAY, feeOrderBatch.getEmployerNo(), serviceItem.getFeeItemNo(),
                                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_FEE_ORDER_ITEM_SERVICE_PAY, serviceItem.getFeeItemNo(), MsgDelayLevelEnum.S_1.getValue());

                        if (texItem.getPayAmount().compareTo(BigDecimal.ZERO) > 0) {
                            //支付个税
                            notifyFacade.sendOne(MessageMsgDest.TOPIC_FEE_ORDER_ITEM_PAY, feeOrderBatch.getEmployerNo(), texItem.getFeeItemNo(),
                                    NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_FEE_ORDER_ITEM_TAX_PAY, texItem.getFeeItemNo(), MsgDelayLevelEnum.S_1.getValue());
                        }

                    }

                    platformTransactionManager.commit(transaction);
                } catch (Exception e) {
                    platformTransactionManager.rollback(transaction);
                    throw e;
                }
            });

        });
        log.info("查询同步{}账单结束",feeSourceEnum.getDesc());
    }

    private List<FeeOrderBatch> getOrderData(Date startTime,Date endTime,FeeSourceEnum feeSourceEnum) {
        Map<String, Object> params = new HashMap<>();
        List<FeeOrderBatch> feeOrderBatches;
        params.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
        params.put("completeBeginDate", startTime);
        params.put("completeEndDate", endTime);
        params.put("isDelete", YesNoCodeEnum.NO.getValue());
        if (feeSourceEnum.getValue() == FeeSourceEnum.PLATFORM_ORDER.getValue()){
            params.put("productNos", Arrays.asList(ProductNoEnum.CKH.getValue(), ProductNoEnum.JKH.getValue()));
            feeOrderBatches = orderDao.selectFeeOrder(params);
        }else{
            feeOrderBatches = offlineOrderMapper.selectFeeOrder(params);
            feeOrderBatches.forEach(x->{
                x.setChannelType(ChannelTypeEnum.ALIPAY.getValue());
                x.setChannelName(ChannelTypeEnum.ALIPAY.getDesc());
                x.setPayChannelNo(ChannelNoEnum.ALIPAY.name());
            });
        }
        return feeOrderBatches;
    }

    private String getNo(SequenceBizKeyEnum sequenceBizKeyEnum) {
        String id = sequenceFacade.nextRedisId("", sequenceBizKeyEnum.getKey(), sequenceBizKeyEnum.getWidth());
        return sequenceBizKeyEnum.getPrefix() + DateUtil.formatCompactDate(new Date()) + id;
    }

    private FeeOrderItem createServiceItem(FeeOrderBatch feeOrderBatch,FeeSourceEnum feeSourceEnum) {
        FeeOrderItem feeOrderItem = new FeeOrderItem();
        feeOrderItem.setVersion(0);
        feeOrderItem.setCreateDate(feeOrderBatch.getCreateDate());
        feeOrderItem.setFeeBatchNo(feeOrderBatch.getFeeBatchNo());
        feeOrderItem.setFeeItemNo(getNo(SequenceBizKeyEnum.CKH_FEE_ORDER_ITEM_SEQ));
        feeOrderItem.setFeeType(FeeTypeEnum.SERVICE_FEE_ORDER.getCode());
        feeOrderItem.setEmployerNo(feeOrderBatch.getEmployerNo());
        feeOrderItem.setEmployerName(feeOrderBatch.getEmployerName());
        feeOrderItem.setMainstayNo(feeOrderBatch.getMainstayNo());
        feeOrderItem.setMainstayName(feeOrderBatch.getMainstayName());
        feeOrderItem.setPayAmount(feeOrderBatch.getFeeAmount());
        feeOrderItem.setFeeSource(feeSourceEnum.getValue());
        setStatus(feeOrderItem, feeOrderBatch.getBalancedMode(),feeOrderBatch.getPayChannelNo());
        return feeOrderItem;
    }

    private void setStatus(FeeOrderItem feeOrderItem, Integer mode,String payChannelNo) {
        //费用为0则设置状态为已支付
        if (feeOrderItem.getPayAmount().compareTo(BigDecimal.ZERO) <= 0) {
            feeOrderItem.setPayTime(new Date());
            feeOrderItem.setStatus(FeeOrderItemStatusEnum.SUCCESS.getCode());
            feeOrderItem.setCompleteTime(new Date());
        } else {
            //线上支付
            if (mode.equals(BalancedEnum.ONLINE_BALANCED.getCode())) {
                feeOrderItem.setPayTime(new Date());
                if (StringUtils.equals(payChannelNo, ChannelNoEnum.JOINPAY.name())) {
                    feeOrderItem.setStatus(FeeOrderItemStatusEnum.SUCCESS.getCode());
                }else {
                    feeOrderItem.setStatus(FeeOrderItemStatusEnum.PAYING.getCode());
                }
            } else {//线下
                feeOrderItem.setStatus(FeeOrderItemStatusEnum.NO_PAY.getCode());
            }
        }
    }

    private FeeOrderItem createTaxItem(FeeOrderBatch feeOrderBatch,FeeSourceEnum feeSourceEnum) {
        FeeOrderItem feeOrderItem = new FeeOrderItem();
        feeOrderItem.setVersion(0);
        feeOrderItem.setCreateDate(feeOrderBatch.getCreateDate());
        feeOrderItem.setFeeBatchNo(feeOrderBatch.getFeeBatchNo());
        feeOrderItem.setFeeItemNo(getNo(SequenceBizKeyEnum.CKH_FEE_ORDER_ITEM_SEQ));
        feeOrderItem.setFeeType(FeeTypeEnum.TAX_ORDER.getCode());
        feeOrderItem.setEmployerNo(feeOrderBatch.getEmployerNo());
        feeOrderItem.setEmployerName(feeOrderBatch.getEmployerName());
        feeOrderItem.setMainstayNo(feeOrderBatch.getMainstayNo());
        feeOrderItem.setMainstayName(feeOrderBatch.getMainstayName());
        feeOrderItem.setPayAmount(feeOrderBatch.getTaxAmount());
        feeOrderItem.setFeeSource(feeSourceEnum.getValue());
        setStatus(feeOrderItem, feeOrderBatch.getBalancedMode(),feeOrderBatch.getPayChannelNo());
        return feeOrderItem;
    }

    public FeeOrderBatch getByBatchNo(String feeBatchNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("feeBatchNo", feeBatchNo);
        return feeorderbatchDao.getOne(paramMap);
    }

    public void update(FeeOrderBatch feeOrderBatch) {
        feeorderbatchDao.update(feeOrderBatch);
    }


    public Map<String, Object> getStatistics(FeeOrderVo feeOrderVo) {
        Map<String, Object> map = new LinkedHashMap<>();
        List<FeeOrderCountVo> feeOrderCountVos = feeorderbatchDao.selectCounts(feeOrderVo);
        int total = feeOrderCountVos.stream().mapToInt(FeeOrderCountVo::getCount).sum();
        int successCount = feeOrderCountVos.stream().filter(e -> e.getStatus().equals(FeeOrderStatus.SUCCESS.getCode())).mapToInt(FeeOrderCountVo::getCount).sum();
        int failCount = total - successCount;
        map.put("total", total);
        map.put("successCount", successCount);
        map.put("unPayCount", failCount);
        FeeOrderSumVo feeOrderSumVo = Optional.ofNullable(feeorderbatchDao.selectSums(feeOrderVo)).orElse(new FeeOrderSumVo());
        Map<String, Object> sumMap = BeanUtil.toMap(feeOrderSumVo);
        map.putAll(sumMap);
        BigDecimal failSum = feeorderbatchDao.selectFailSum(feeOrderVo);
        map.put("unPayAmount", failSum);
        return map;
    }

    public FeeOrderBatchVo selectOrderItem(String feeBatchNo) {
        List<FeeOrderItem> feeOrderItems = feeOrderItemDao.listBy(Collections.singletonMap("feeBatchNo", feeBatchNo));
        if (feeOrderItems.size() != 2) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(String.format("[%s]账单子查询查询异常", feeBatchNo));
        }
        FeeOrderItem taxItem = feeOrderItems.stream().filter(e -> e.getFeeType().equals(FeeTypeEnum.TAX_ORDER.getCode())).findFirst().orElse(null);
        FeeOrderItem serviceItem = feeOrderItems.stream().filter(e -> e.getFeeType().equals(FeeTypeEnum.SERVICE_FEE_ORDER.getCode())).findFirst().orElse(null);

        FeeOrderBatchVo feeOrderBatchVo = new FeeOrderBatchVo();
        feeOrderBatchVo.setServiceItem(serviceItem);
        feeOrderBatchVo.setTaxItem(taxItem);
        return feeOrderBatchVo;
    }


    public FeeOrderItem getByItemNo(String feeItemNo) {
        return feeOrderItemDao.getOne(Collections.singletonMap("feeItemNo", feeItemNo));
    }


    public Map<String, Object> getOffLineItem(String feeBatchNo) {
        FeeOrderBatchVo feeOrderBatchVo = this.selectOrderItem(feeBatchNo);
        FeeOrderBatch feeOrderBatch = feeorderbatchDao.getOne(Collections.singletonMap("feeBatchNo", feeBatchNo));

        String yishui = dataDictionaryFacade.getSystemConfig("yishui");

        MerchantBankAccount bankAccount = null;
        if (StrUtil.equals(feeOrderBatch.getMainstayNo(), yishui)) {
            JSONObject fundInfo = yishuiFacade.getFundInfo(feeOrderBatch.getEmployerNo());
            if (fundInfo != null) {
                bankAccount = new MerchantBankAccount();
                bankAccount.setBankName(fundInfo.getString("branchName"));
                bankAccount.setMchNo(feeOrderBatch.getEmployerNo());
                bankAccount.setAccountName(fundInfo.getString("accountName"));
                bankAccount.setAccountNo(fundInfo.getString("accountNo"));
            }
        }else {
            bankAccount = merchantBankAccountFacade.getByMchNo(feeOrderBatch.getMainstayNo());
        }

        Map<String, Object> map = BeanUtil.toMap(feeOrderBatchVo);
        map.put("bankAccount", bankAccount);
        map.put("createData", DateUtil.formatDate(feeOrderBatch.getCreateDate()));
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    public void complete(FeeOrderBatchVo feeOrderBatchVo) {
        FeeOrderItem serviceItem = feeOrderBatchVo.getServiceItem();
        FeeOrderItem taxItem = feeOrderBatchVo.getTaxItem();
        completeUpdateItem(serviceItem);
        completeUpdateItem(taxItem);

        FeeOrderBatch batch = feeorderbatchDao.getOne(Collections.singletonMap("feeBatchNo", serviceItem.getFeeBatchNo()));
        batch.setCompleteTime(new Date());
        batch.setUpdateTime(new Date());
        batch.setStatus(FeeOrderStatus.SUCCESS.getCode());
        feeorderbatchDao.update(batch);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_FEE_ORDER_CETIFICATE_UPLOAD, serviceItem.getEmployerNo(), serviceItem.getFeeBatchNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_FEE_ORDER_CETIFICATE_UPLOAD, serviceItem.getFeeBatchNo(), MsgDelayLevelEnum.S_1.getValue());

        //生成计费订单
        generateFeeOrder(batch);
    }

    public void generateFeeOrder(FeeOrderBatch batch) {
        if (batch.getFeeSource().intValue() == FeeSourceEnum.OUTSIDE_ORDER.getValue()){
            String startDate = DateUtil.formatDate(batch.getCreateDate());
            String endDate = DateUtil.formatDate(DateUtil.addDay(batch.getCreateDate(),1));
            List<OfflineOrder> offlineOrder = offlineOrderMapper.selectList(new QueryWrapper<OfflineOrder>().lambda()
                    .eq(OfflineOrder::getEmployerNo,batch.getEmployerNo())
                    .eq(OfflineOrder::getMainstayNo,batch.getMainstayNo())
                    .eq(OfflineOrder::getIsDelete, YesNoCodeEnum.NO.getValue())
                    .eq(OfflineOrder::getBatchStatus, OrderStatusEnum.FINISH_GRANT.getValue())
                    .between(OfflineOrder::getCompleteTime,startDate,endDate));
            offlineOrder.stream().forEach(x->{
                notifyFacade.sendOne(MessageMsgDest.TOPIC_OFFLINE_ORDER_FEE,batch.getEmployerNo(),batch.getFeeBatchNo(),
                        NotifyTypeEnum.TRADE_NOTIFY.getValue(),MessageMsgDest.TAG_OFFLINE_ORDER_FEE,JsonUtil.toString(x));
            });
        }
    }

    private void completeUpdateItem(FeeOrderItem item) {
        if (!item.getStatus().equals(FeeOrderItemStatusEnum.SUCCESS.getCode())) {
            formatData(item);
            feeOrderItemDao.updateIfNotNull(item);
        }
    }

    private void formatData(FeeOrderItem item) {
        item.setCompleteTime(new Date());
        item.setPayTime(new Date());
        item.setStatus(FeeOrderItemStatusEnum.SUCCESS.getCode());
    }

    public List<Map<String, Object>> export(Map<String, Object> param) {
        FeeOrderBatch feeOrderBatch = BeanUtil.toObject(FeeOrderBatch.class, param);
        final Date data = feeOrderBatch.getCreateDate();
        final Date startTime = DateUtil.getDayStart(data);
        final Date endTime = DateUtil.getDayEnd(data);
        List<Map<String, Object>> result;
        Map<String, Object> params = new HashMap<>();
        params.put("productNo", feeOrderBatch.getProductNo());
        params.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
        params.put("employerNo", feeOrderBatch.getEmployerNo());
        params.put("mainstayNo", feeOrderBatch.getMainstayNo());
        params.put("completeBeginDate", startTime);
        params.put("completeEndDate", endTime);
        params.put("isDelete", YesNoCodeEnum.NO.getValue());
        log.info("账单订单明细导出,订单批次查询条件:{}", params);
//        tradeHelperBiz.putOrderCreateDateToMap(params);
        if (feeOrderBatch.getFeeSource().intValue() == FeeSourceEnum.PLATFORM_ORDER.getValue()){
            params.put("payChannelNo", feeOrderBatch.getPayChannelNo());
            params.put("channelName", feeOrderBatch.getChannelName());
            params.put("channelType", feeOrderBatch.getChannelType());
            List<String> batchNos = orderDao.selectOrderNo(params);
            log.info("账单订单明细导出,批次数:{}，批次号：{}", batchNos.size(),batchNos);
            params.remove("batchStatus");
            params.put("completeBeginDate", DateUtil.addDay(startTime, -2));
            params.put("completeEndDate", endTime);
            log.info("账单订单明细导出,订单明细查询条件:{}", params);
            List<OrderItem> orderItems = orderItemDao.listBy(params);
            List<OrderItem> filters = orderItems.stream().filter(e -> batchNos.contains(e.getPlatBatchNo())).collect(Collectors.toList());
            log.info("账单订单明细导出,明细过滤结果，订单明细数：{}", filters.size());
            result = filters.stream().map(BeanUtil::toMap).collect(Collectors.toList());
        }else{
            List<String> batchNos = offlineOrderMapper.selectOrderNo(params);
            params.remove("batchStatus");
            params.remove("completeBeginDate");
            params.remove("completeEndDate");
            params.put("platBatchNos",batchNos);
            List<OfflineOrderItem> offlineOrderItemList = offlineOrderItemBiz.MapperListBy(params);
            result = offlineOrderItemList.stream().map(BeanUtil::toMap).collect(Collectors.toList());
        }
        result.forEach(e->{
            e.put("createDate",DateUtil.formatDate(feeOrderBatch.getCreateDate()));
            e.put("feeBatchNo",feeOrderBatch.getFeeBatchNo());
            e.put("createTime",DateUtil.formatDateTime((Date) e.get("createTime")));
            e.put("completeTime",DateUtil.formatDateTime((Date) e.get("completeTime")));
        });
        return result;

    }

    public boolean isExistNotPay(Map<String, Object> paramMap) {
        FeeOrderBatch feeOrderBatch = feeorderbatchDao.selectSingleBatch(paramMap);
        if (feeOrderBatch != null){
            return true;
        }else{
            return false;
        }
    }

    public boolean isGenerateFee(String mchNo, String mainstayNo, Date completeDate, Integer feeSource) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",mchNo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("feeSource",feeSource);
        paramMap.put("createDate",completeDate);
        FeeOrderBatch feeOrderBatch = feeorderbatchDao.selectSingleBatch(paramMap);
        if (feeOrderBatch != null){
            return true;
        }else{
            return false;
        }
    }

    public Boolean hasNotpayFee(String startTime, String endTime, String employerNo,String mainstayNo,Integer feeSource) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", employerNo);
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("startDate", startTime);
        paramMap.put("endDate", endTime);
        paramMap.put("feeSource", feeSource == InvoiceSourceEnum.ON_LINE.getCode() ? FeeSourceEnum.PLATFORM_ORDER.getValue() : FeeSourceEnum.OUTSIDE_ORDER.getValue());
        paramMap.put("statusList", Lists.newArrayList(FeeOrderStatus.NO_PAY.getCode(), FeeOrderStatus.PAYING.getCode(),FeeOrderStatus.FAIL.getCode()));
        log.info("查询未付款账单，查询参数{}", JSONUtil.toJsonPrettyStr(paramMap));
        final long count = feeorderbatchDao.countBy(paramMap);
        if (count > 0) {
            return true;
        }else {
            return false;
        }
    }
}
