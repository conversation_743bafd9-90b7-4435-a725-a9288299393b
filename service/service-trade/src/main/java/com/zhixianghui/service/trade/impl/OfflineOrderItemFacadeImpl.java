package com.zhixianghui.service.trade.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.dto.OfflineWorkerBillPathUpdateDto;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OfflineOrderItemFail;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.vo.AuthInfoVo;
import com.zhixianghui.service.trade.biz.OfflineOrderItemBiz;
import com.zhixianghui.service.trade.biz.OfflineOrderItemFailBiz;
import com.zhixianghui.service.trade.process.OfflineOrderGrantHandler;
import org.apache.catalina.util.ParameterMap;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OfflineOrderItemFacadeImpl implements OfflineOrderItemFacade {

    @Autowired
    private OfflineOrderItemBiz orderItemBiz;
    @Autowired
    private OfflineOrderItemFailBiz orderItemFailBiz;
    @Autowired
    private OfflineOrderGrantHandler offlineOrderGrantHandler;

    @Override
    public void batchInsert(List<OfflineOrderItem> itemList) {
        orderItemBiz.batchInsert(itemList);
    }

    @Override
    public OrderItemSumBo sumOrderItem(Map<String, Object> beanToMap) {
        return orderItemBiz.sumOrderItem(beanToMap);
    }

    @Override
    public Long countOrderItemByMchOrderNo(String employerNo, String mchOrderNo) {
        Map<String, Object> param = new ParameterMap<>();
        param.put("employerNo", employerNo);
        param.put("mchOrderNo", mchOrderNo);
        return orderItemBiz.countOrderItem(param).longValue();
    }

    @Override
    public PageResult<List<OfflineOrderItem>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        final Page<OfflineOrderItem> itemPage = orderItemBiz.listPage(paramMap, pageParam);
        return PageResult.newInstance(itemPage.getRecords(),Long.valueOf(itemPage.getCurrent()).intValue(),Long.valueOf(itemPage.getSize()).intValue(),itemPage.getTotal());
    }

    @Override
    public List<OfflineOrderItemFail> getByPlatBatchNo(String platBatchNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("platBatchNo", platBatchNo);
        return orderItemFailBiz.listBy(param);
    }

    @Override
    public PageResult<List<OfflineOrderItemFail>> pageByPlatBatchNo(String platBatchNo, PageParam pageParam) {
        Map<String, Object> param = new HashMap<>();
        param.put("platBatchNo", platBatchNo);

        final Page<OfflineOrderItemFail> itemFailPage = orderItemFailBiz.pageBy(param, new Page(pageParam.getPageCurrent(), pageParam.getPageSize()));
        return PageResult.newInstance(itemFailPage.getRecords(), Long.valueOf(itemFailPage.getCurrent()).intValue(), Long.valueOf(itemFailPage.getSize()).intValue(), itemFailPage.getTotal());
    }

    @Override
    public AuthInfoVo getAuthInfo(OfflineOrderItem orderItem) {
        return orderItemBiz.getAuthInfo(orderItem);
    }

    @Override
    public Long countOrder(Map<String, Object> param) {
        return orderItemBiz.countOrder(param);
    }

    @Override
    public Long countOrderItem(Map<String, Object> paramMap) {
        return orderItemBiz.countOrderItem(paramMap).longValue();
    }

    @Override
    public void saveFail(OfflineOrderItemFail itemFail) {
        orderItemFailBiz.saveFailItem(itemFail);
    }

    @Override
    public List<String> listPlatTrxNoByBatchNo(Map<String,Object> paramMap) {
        return orderItemBiz.listPlatTrxNoByBatchNo(paramMap);
    }

    @Override
    public void cancelOrderItem(String platBatchNo){
        orderItemBiz.cancelOrderItem(platBatchNo);
    }

    @Override
    public void authInit(String platBatchNo) {
        orderItemBiz.authInit(platBatchNo);
    }

    @Override
    public OfflineOrderItem uploadWorkerBillPath(OfflineWorkerBillPathUpdateDto dto) {
        final OfflineOrderItem offlineOrderItem = orderItemBiz.getByPlatTrxNo(dto.getPlatTrxNo());
        if (offlineOrderItem != null) {
            offlineOrderItem.setWorkerBillFilePath(dto.getWorkerBillFilePath());
            offlineOrderItem.setUpdateTime(new Date());
            orderItemBiz.update(offlineOrderItem);
        }
        AuthInfoVo authInfo = orderItemBiz.getAuthInfo(offlineOrderItem);
        offlineOrderItem.setAuthStatus(authInfo.getAuth());
        offlineOrderItem.setSignStatus(authInfo.getSign());
        return offlineOrderItem;
    }

    @Override
    public void deleteOrderItem(String platTrxNo) throws BizException {
        offlineOrderGrantHandler.deleteOrderItem(platTrxNo);
    }

    @Override
    public BigDecimal sumOrderItemWaitInvoiceAmount(Map<String, Object> paramMap) {
        return orderItemBiz.sumOrderItemWaitInvoiceAmount(paramMap);
    }

    @Override
    public OfflineOrderItem getOrderItemByPlatTrxNo(String platTrxNo) {
        return orderItemBiz.getByPlatTrxNo(platTrxNo);
    }
}
