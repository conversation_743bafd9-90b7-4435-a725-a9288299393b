package com.zhixianghui.service.trade.vo.res;

import com.zhixianghui.facade.common.entity.config.DataDictionary;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/14 10:44
 */
@Data
@Accessors(chain = true)
public class LoginResVo implements Serializable {
    private static final long serialVersionUID = 8424492985183899144L;
    private String accessToken;
    private String loginFlag;

    private boolean verify;
    private Integer toSign;
    private String name;
    private String phone;
    private String idcard;
    private List<DataDictionary> dict;
}
