package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.common.OrderCompleteVo;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.vo.report.AlipayJobPayslipSyncReqVo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class GrantCompleteListener {
    @Autowired
    private OrderItemBiz orderItemBiz;
    @Reference
    private AlipayFacade alipayFacade;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ORDER_COMPLETE, consumeThreadMax = 5,
            consumerGroup = "orderCompleteAlipayJobPayslipSync")
    public class OrderCompleteAlipayJobPayslipSyncListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("OrderCompleteVo不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            OrderCompleteVo vo = JsonUtil.toBean(msg, OrderCompleteVo.class);
            String platTrxNo = vo.getPlatTrxNo();
            log.info("[{}] 开始支付宝就业到账啦明细同步处理", platTrxNo);
            OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
            this.notifyAlipayJobPayslipSync(orderItem);
        }

        public void notifyAlipayJobPayslipSync(OrderItem orderItem) {
            // 当订单为成功且通道是支付宝，且发放方式为支付宝时，才调用支付宝 "就业到账啦明细同步" 接口
            if (orderItem.getOrderItemStatus() != OrderItemStatusEnum.GRANT_SUCCESS.getValue()
                    || !ChannelNoEnum.ALIPAY.name().equals(orderItem.getPayChannelNo())
                    || ChannelTypeEnum.ALIPAY.getValue() != orderItem.getChannelType()) {
                return;
            }

            // 发起接口调用
            Date completeTime = orderItem.getCompleteTime() != null ? orderItem.getCompleteTime(): new Date();
            AlipayJobPayslipSyncReqVo reqVo = new AlipayJobPayslipSyncReqVo();
            reqVo.setLoginId(orderItem.getReceiveAccountNoDecrypt());//收款账号为手机号或邮箱
            reqVo.setUserName(orderItem.getReceiveNameDecrypt());
            reqVo.setRemark(StringUtil.isEmpty(orderItem.getRemark()) ? null: orderItem.getRemark());
            reqVo.setMchOrderNo(orderItem.getMchOrderNo());
            reqVo.setPlatTrxNo(orderItem.getPlatTrxNo());
            reqVo.setMainstayNo(orderItem.getMainstayNo());
            reqVo.setMainstayName(orderItem.getMainstayName());
            reqVo.setAmount(orderItem.getOrderItemNetAmount());
            reqVo.setCompleteTime(DateUtil.formatDateTime(completeTime));
            log.info("开始调用支付宝'就业到账啦明细同步'接口 mchOrderNo: {} platTrxNo: {}", orderItem.getMchOrderNo(), orderItem.getPlatTrxNo());
            alipayFacade.jobPayslipSync(reqVo);
        }
    }
}
