package com.zhixianghui.service.trade.biz;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.trade.entity.AuthRecord;
import com.zhixianghui.service.trade.dao.AuthRecordDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* 鉴权成功记录表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-11-06
*/
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthRecordBiz {

    private final AuthRecordDao authRecordDao;

    public List<AuthRecord> listByElementActiveRecordWithType(String receiveName, String receiveIdCardNo,
                                                              String receiveAccountNo, String receivePhoneNo, AuthTypeEnum authType) {
        //鉴权要素没有 直接不本地校验 否则listBy查询条件会忽略这些条件
        if(StringUtils.isBlank(receiveName) || StringUtils.isBlank(receiveIdCardNo) || authType == null){
            return null;
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("receiveNameMd5", MD5Util.getMixMd5Str(receiveName));
        param.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(receiveIdCardNo));
        param.put("authType", authType.getValue());
        switch (authType){
            case IDCARD_NAME:
                break;
            case IDCARD_NAME_PHONE:
                if(StringUtils.isBlank(receivePhoneNo)){
                    return null;
                }
                param.put("receivePhoneNoMd5", MD5Util.getMixMd5Str(receivePhoneNo));
                break;
            case IDCARD_NAME_BANKCARD:
                if(StringUtils.isBlank(receiveAccountNo)){
                    return null;
                }
                param.put("receiveAccountNoMd5", MD5Util.getMixMd5Str(receiveAccountNo));
                break;
            case IDCARD_NAME_BANKCARD_PHONE:
                if(StringUtils.isBlank(receivePhoneNo)||StringUtils.isBlank(receiveAccountNo)){
                    return null;
                }
                param.put("receiveAccountNoMd5", MD5Util.getMixMd5Str(receiveAccountNo));
                param.put("receivePhoneNoMd5", MD5Util.getMixMd5Str(receivePhoneNo));
                break;
        }
        return authRecordDao.listBy(param);
    }

    @Async
    public void insertAsync(AuthRecord record) {

        try{
            authRecordDao.insert(record);
        } catch (DuplicateKeyException e){
            log.info("[{}]鉴权信息表唯一约束冲突，不做处理：", record.getAuthNo(), e);
        }
    }
}
