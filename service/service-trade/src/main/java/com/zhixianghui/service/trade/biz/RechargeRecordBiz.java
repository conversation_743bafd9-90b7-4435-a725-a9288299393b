package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.service.pay.VoucherFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.pay.VoucherVo;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.ReportAccountHistory;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.common.service.ReportAccountHistoryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.common.vo.TraceVo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantNotifySet;
import com.zhixianghui.facade.merchant.enums.MerchantNotifySetTypeEnum;
import com.zhixianghui.facade.merchant.service.MerchantNotifySetFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.CmbIncomeRecord;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.enums.DynamicMsgEnum;
import com.zhixianghui.facade.trade.enums.IdentityTypeEnum;
import com.zhixianghui.facade.trade.enums.RechargeStatusEnum;
import com.zhixianghui.facade.trade.enums.RechargeTypeEnum;
import com.zhixianghui.facade.trade.vo.CmbAccountChangeVo;
import com.zhixianghui.service.trade.dao.RechargeRecordDao;
import com.zhixianghui.service.trade.dao.mapper.RechargeRecordMapper;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.service.trade.helper.TradeNotifyBiz;
import com.zhixianghui.service.trade.vo.req.RechargeVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.apache.commons.codec.CharEncoding.UTF_8;

@Service
public class RechargeRecordBiz extends ServiceImpl<RechargeRecordMapper, RechargeRecord> {
    private static final Logger LOGGER = LoggerFactory.getLogger(RechargeRecordBiz.class);
    public static final String RECHARGE_FILE_NAME = "充值凭证.pdf";
    @Autowired
    private RechargeRecordDao rechargeRecordDao;
    @Autowired
    private RechargeRecordMapper rechargeRecordMapper;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private TradeNotifyBiz tradeNotifyBiz;

    @Reference
    private MerchantNotifySetFacade merchantNotifySetFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private ReportAccountHistoryFacade reportAccountHistoryFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private VoucherFacade voucherFacade;
    @Autowired
    private DynamicMsgBiz dynamicMsgBiz;
    @Autowired
    private TradeHelperBiz tradeHelperBiz;

    @Value("${hjzx.certificateCallbackUrl}")
    private String certificateCallbackUrl;

    @Autowired
    private AccountQueryBiz accountQueryBiz;

    @Autowired
    private RedisClient redisClient;

    private OrderBiz orderBiz;

    @Autowired
    public RechargeRecordBiz(@Lazy OrderBiz orderBiz) {
        this.orderBiz = orderBiz;
    }

    public List<RechargeRecord> getRechargeRecordList(Map<String, Object> paramMap) {
        return rechargeRecordDao.listBy(paramMap);
    }

    public PageResult<List<RechargeRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return rechargeRecordDao.listPage(paramMap, pageParam);
    }

    public RechargeRecord getByRechargeId(String rechargeId) {
        return rechargeRecordMapper.selectOne(new QueryWrapper<RechargeRecord>().eq(RechargeRecord.COL_RECHARGE_ORDER_ID, rechargeId));
    }

    public void updateRechargeRecord(RechargeRecord rechargeRecord) {
        rechargeRecordDao.update(rechargeRecord);
    }

    public RechargeRecord getById(String id) {
        return rechargeRecordMapper.selectById(id);
    }


    public RechargeRecord getLastestRecord(String employerNo, String mainstayNo) {

        List<RechargeRecord> records = this.list(new QueryWrapper<RechargeRecord>().eq(RechargeRecord.COL_EMPLOYER_NO, employerNo).eq(RechargeRecord.COL_MAINSTAY_NO, mainstayNo).eq(RechargeRecord.COL_RECHARGE_STATUS, RechargeStatusEnum.NEW.getCode()).orderByDesc(RechargeRecord.COL_CREATE_TIME));

        if (records == null || records.size() == 0 || records.isEmpty()) {
            return null;
        } else {
            return records.get(0);
        }
    }

    /**
     * 超时执行方法
     *
     * @param pageSize
     */
    public void expireRechargeRecord(Integer pageSize) {

        Date now = new Date();
        Map<String, Object> param = new HashMap<>();
        param.put("rechargeStatus", RechargeStatusEnum.NEW.getCode());
        param.put("createBeginDate", DateUtil.addMinute(now, -120));
        param.put("createEndDate", DateUtil.addMinute(now, -60));
        param.put("channelType", ChannelTypeEnum.ALIPAY.getValue());

        boolean hasNext = true;
        int currentPage = 1;
        do {
            PageResult<List<RechargeRecord>> pageResult = rechargeRecordDao.listPage(param, PageParam.newInstance(currentPage, pageSize));
            List<RechargeRecord> data = pageResult.getData();
            if (data != null && !data.isEmpty()) {
                for (RechargeRecord record : data) {

                    if (record.getChannelType() != ChannelTypeEnum.ALIPAY.getValue()) {
                        continue;
                    }

                    // 查询当前余额
                    final EmployerAccountInfo accountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(record.getEmployerNo(), record.getMainstayNo(), record.getChannelType().intValue());
                    final BigDecimal balance = accountQueryBiz.getBalance(accountInfo);
                    record.setCurrentBalance(balance);

                    String queryStr = alipayFacade.transPayQuery(record.getRechargeOrderId());
                    if (queryStr != null) {
                        JSONObject queryJson = JSONObject.parseObject(queryStr).getJSONObject("alipay_fund_trans_common_query_response");
                        if (StringUtils.equals(queryJson.getString("status"), "WAIT_PAY") || StringUtils.equals(queryJson.getString("sub_code"), "ORDER_NOT_EXIST")) {
                            //1. 更新充值记录
                            record.setRechargeStatus(RechargeStatusEnum.FAIL.getCode().shortValue());
                            record.setUpdateTime(new Date());
                            record.setRemark("定时任务更新超时订单:外部接口状态为【待支付】");
                            rechargeRecordMapper.updateById(record);
                            notifyRechargeMerchant(record);
                        } else {
                            if (StringUtils.equals(queryJson.getString("status"), "SUCCESS")) {
                                record.setUpdateTime(new Date());
                                record.setChannelTrxNo(queryJson.getString("pay_fund_order_id"));
                                record.setChannelOrderId(queryJson.getString("order_id"));
                                record.setRechargeStatus(RechargeStatusEnum.SUCCESS.getCode().shortValue());
                                record.setRemark("定时任务更新超时订单:外部接口状态为【成功】");
                                rechargeRecordMapper.updateById(record);
                                notifyRechargeMerchant(record);
                                //推送动态
                                dynamicMsgBiz.putDynamicMsg(record.getEmployerNo(), String.format(
                                        DynamicMsgEnum.CHARGE.getMsg(), record.getRechargeAmount(), record.getRechargeOrderId()));
                            } else {
                                record.setUpdateTime(new Date());
                                record.setRechargeStatus(RechargeStatusEnum.FAIL.getCode().shortValue());
                                record.setRemark("定时任务更新超时订单:外部接口状态为【超时关闭或者不存在订单】");
                                notifyRechargeMerchant(record);
                                rechargeRecordMapper.updateById(record);
                            }
                        }
                    }
                }
                currentPage++;
            } else {
                hasNext = false;
            }

        } while (hasNext);


    }

    public void rechargeCallback(TraceVo<?> traceVo) {
        Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(traceVo.getData()), Map.class);
        JSONObject jsonObject = JSONObject.parseObject((String) map.get("data"));
        RechargeVo rechargeVo = JSONObject.parseObject(JSONObject.toJSONString(jsonObject), RechargeVo.class);

        // 如果失败了
        if (RechargeStatusEnum.FAIL.getMessage().equals(rechargeVo.getIncomeStatus())) {
            LOGGER.error("[{}] 充值回调响应失败:{}", traceVo.getTraceId(), JSONObject.toJSONString(rechargeVo));
            return;
        }
        List<RechargeRecord> list = getRechargeRecordList(new HashMap<String, Object>() {{
            put("channelTrxNo", rechargeVo.getIncomeTrxNo());
        }});

        if (!CollectionUtils.isEmpty(list)) {
            LOGGER.error("[{}] 已存在数据,可能发生重复回调:{}", traceVo.getTraceId(), JSONObject.toJSONString(rechargeVo));
            return;
        }

        String lockKey = String.join(":", "RECHARGE_CALLBACK", rechargeVo.getIncomeTrxNo());
        String clientId = redisLock.tryLockLong(lockKey, 0, 1);
        if (clientId == null) {
            LOGGER.warn("[{}] 充值获取锁失败, 可能发生重复回调:{}", traceVo.getTraceId(), System.currentTimeMillis());
            return;
        }
        LOGGER.info("[{}] 充值获取锁成功: {}", traceVo.getTraceId(), System.currentTimeMillis());
        try {
            rechargeHandle(traceVo, map, rechargeVo);
        } finally {
            redisLock.unlockLong(clientId);
        }

        TraceVo<?> certificateTrace = new TraceVo<>().setTraceId(traceVo.getTraceId()).setData(rechargeVo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CERTIFICATE,
                NotifyTypeEnum.TRADE_CERTIFICATE.getValue(),
                MessageMsgDest.TAG_TRADE_CERTIFICATE,
                JSON.toJSONString(certificateTrace)
        );

        // 异步延迟查询当前余额，异常不应该影响订单更新
        try {
            notifyFacade.sendOne(MessageMsgDest.TOPIC_RECHARGE_BALANCE,
                    NotifyTypeEnum.RECHARGE_BALANCE.getValue(),
                    MessageMsgDest.TAG_RECHARGE_BALANCE,
                    JSON.toJSONString(rechargeVo),
                    MsgDelayLevelEnum.S_30.getValue()
            );
        } catch (Exception e) {
            LOGGER.error("异步延迟查询当前余额失败；充值订单号:{}", rechargeVo.getRechargeOrderId());
            LOGGER.error("异步延迟查询当前余额失败", e);
        }

    }

    private void rechargeHandle(TraceVo<?> traceVo, Map<String, Object> map, RechargeVo rechargeVo) {
        rechargeVo.setChannelMchNo((String) map.get("mch_no"));
        String rechargeOrderId = LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        rechargeOrderId += sequenceFacade.nextRedisId(SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getKey(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getWidth());

        LOGGER.info("[{}] 充值回调内容 : {}-{}", traceVo.getTraceId(), rechargeOrderId, JSONObject.toJSONString(rechargeVo));

        List<EmployerAccountInfo> accountInfoList = employerAccountInfoFacade.listBy(new HashMap<String, Object>() {{
            put("subMerchantNo", rechargeVo.getPayeeAccountNo());
            put("status", OpenOffEnum.OPEN.getValue());
        }});

        if (CollectionUtils.isEmpty(accountInfoList)) {
            LOGGER.error("[{}] 充值回调找不到对应的商户号:{}", traceVo.getTraceId(), rechargeOrderId);
            return;
        }
        // 正常情况下只有一条, 这里大于一时默认取第一条
        if (accountInfoList.size() > 1) {
            LOGGER.warn("[{}] 充值回调找到多条商户记录: {}-{}", traceVo.getTraceId(), rechargeOrderId, accountInfoList);
            asyncNotify(rechargeOrderId, accountInfoList);
        }
        EmployerAccountInfo accountInfo = accountInfoList.get(0);
        RechargeRecord rechargeRecord = new RechargeVo().toRechargeRecord(rechargeVo, rechargeOrderId, accountInfo);
        rechargeRecord.setCreateBy(rechargeVo.getPayerAccountName());
        rechargeRecord.setRechargeType(RechargeTypeEnum.TRANSFER_RECHARGE.getCode());
        rechargeRecord.setRechargeStatus(RechargeStatusEnum.SUCCESS.getCode().shortValue());

        orderBiz.addRechargeRecord(rechargeRecord);
        //发送充值回调通知
        notifyRechargeMerchant(rechargeRecord);
        rechargeVo.setRechargeOrderId(rechargeOrderId);
    }

    @Reference
    private RobotFacade robotFacade;

    private void asyncNotify(String rechargeOrderId, List<EmployerAccountInfo> accountInfoList) {

        CompletableFuture.runAsync(() -> {
            String content = null;
            try {
                content = URLEncoder.encode(JSONObject.toJSONString(accountInfoList.get(0)), UTF_8);
            } catch (UnsupportedEncodingException e) {
                LOGGER.error("[{}] 充值提醒,账户内容编码异常: {}", rechargeOrderId, JSONObject.toJSON(accountInfoList), e);
            }

            MarkDownMsg markDownMsg = new MarkDownMsg();
            markDownMsg.setUnikey(rechargeOrderId);
            markDownMsg.setRobotType(RobotTypeEnum.RECHARGE_ROBOT.getType());
            StringBuilder sb = new StringBuilder("#### 充值提醒\\n ");
            sb.append("> 充值记录Id：").append(rechargeOrderId)
                    .append("\\n > 充值回调后找到多条商户记录：").append(content)
                    .append("\\n > 创建时间：").append(DateUtil.formatDateTime(new Date()));
            markDownMsg.setContent(sb.toString());
            robotFacade.pushMarkDownAsync(markDownMsg);
        });
    }


    public void fetchCertificate(TraceVo<?> traceVo) {
        RechargeVo rechargeVo = JSONObject.parseObject(JSONObject.toJSONString(traceVo.getData()), RechargeVo.class);
        LOGGER.info("[{}]开始生成充值凭证: {}", traceVo.getTraceId(), JSONObject.toJSONString(rechargeVo));
        String rechargeNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.RECHARGE_CERTIFICATE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.RECHARGE_CERTIFICATE_NO_SEQ.getKey(), SequenceBizKeyEnum.RECHARGE_CERTIFICATE_NO_SEQ.getWidth());

        VoucherVo voucherVo = new VoucherVo().setBizType("JUNXH_TRANSFER").setRawOrderNo(rechargeVo.getIncomeTrxNo()).
                setCertificateNo(rechargeNo).
                setRechargeTime(DateUtil.formatDate(DateUtil.parseTime(rechargeVo.getPayTime()))).
                setChannelAccountNo(rechargeVo.getChannelMchNo()).
                setCallbackUrl(certificateCallbackUrl);

        RechargeRecord rechargeRecord = getByRechargeId(rechargeVo.getRechargeOrderId());
        if (rechargeRecord == null) {
            LOGGER.error("[{}]充值记录不存在: {}", traceVo.getTraceId(), JSONObject.toJSONString(traceVo));
            return;
        }
        voucherVo.setChannelMchNo(rechargeVo.getChannelMchNo());
        voucherVo.setChannelNo(rechargeRecord.getChannelCode());

        JSONObject respObject = voucherFacade.generateCertificate(voucherVo, traceVo.getTraceId());
        if (respObject == null || !respObject.containsKey("data") || !respObject.getJSONObject("data").containsKey("platTrxNo")) {
            LOGGER.error("[{}]凭证生成失败: {}", traceVo.getTraceId(), JSONObject.toJSONString(respObject));
            return;
        }
        String platTrxNo = respObject.getJSONObject("data").getString("platTrxNo");
        if (StringUtils.isBlank(platTrxNo)) {
            LOGGER.error("[{}]获取付款凭证的批次号为空: {}", traceVo.getTraceId(), JSONObject.toJSONString(respObject));
            return;
        }
        rechargeVo.setPlatTrxNo(platTrxNo);

        TraceVo<?> downloadTrace = new TraceVo<>().setTraceId(traceVo.getTraceId()).setData(rechargeVo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CERTIFICATE, NotifyTypeEnum.TRADE_CERTIFICATE.getValue(), MessageMsgDest.TAG_DOWNLOAD_PAY_CERTIFICATE, JsonUtil.toString(downloadTrace), MsgDelayLevelEnum.M_1.getValue());
    }

    @Autowired
    private FastdfsClient fastdfsClient;

    public void downloadCertificate(TraceVo<?> traceVo) {
        RechargeVo rechargeVo = JSONObject.parseObject(JSONObject.toJSONString(traceVo.getData()), RechargeVo.class);
        String downloadNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.DOWNLOAD_CERTIFICATE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.DOWNLOAD_CERTIFICATE_NO_SEQ.getKey(), SequenceBizKeyEnum.DOWNLOAD_CERTIFICATE_NO_SEQ.getWidth());

        VoucherVo voucherVo = new VoucherVo().setRawOrderNo(rechargeVo.getIncomeTrxNo()).
                setCertificateNo(downloadNo).
                setRechargeTime(DateUtil.formatDate(DateUtil.parseTime(rechargeVo.getPayTime()))).
                setChannelAccountNo(rechargeVo.getChannelMchNo()).
                setPlatTrxNo(rechargeVo.getPlatTrxNo());

        RechargeRecord rechargeRecord = getByRechargeId(rechargeVo.getRechargeOrderId());
        if (rechargeRecord == null) {
            LOGGER.error("[{}]下载凭证但充值记录不存在: {}", traceVo.getTraceId(), JSONObject.toJSONString(traceVo));
            return;
        }
        voucherVo.setChannelMchNo(rechargeVo.getChannelMchNo());
        voucherVo.setChannelNo(rechargeRecord.getChannelCode());
//        voucherVo.setChannelNo(ChannelNoEnum.JOINPAY.name());

        JSONObject respObject = voucherFacade.downloadCertificate(voucherVo, traceVo.getTraceId());
        if (respObject == null || !respObject.containsKey("data") || !respObject.getJSONObject("data").containsKey("fileUrl")) {
            LOGGER.error("[{}]凭证未生成: {}", traceVo.getTraceId(), JSONObject.toJSONString(respObject));
            return;
        }

        byte[] fileArray = new byte[0];
        try {
            fileArray = FileUtils.download(respObject.getJSONObject("data").getString("fileUrl"));
        } catch (IOException e) {
            LOGGER.error("[{}] 下载付款凭证失败: {}", traceVo.getTraceId(), rechargeVo.getPlatTrxNo(), e);
        }
        String fileUrl = fastdfsClient.uploadFile(fileArray, RECHARGE_FILE_NAME);
        if (StringUtils.isBlank(fileUrl)) {
            LOGGER.error("[{}] 生成付款凭证失败:{}", traceVo.getTraceId(), rechargeVo.getPlatTrxNo());
        }
        RechargeRecord record = getByRechargeId(rechargeVo.getRechargeOrderId());
        if (record == null) {
            LOGGER.error("[{}] 生成付款凭证成功, 但充值记录不存在:{}", traceVo.getTraceId(), rechargeVo.getPlatTrxNo());
            return;
        }
        record.setReceiptUrl(fileUrl);
        updateById(record);
    }

    public Map<String, Object> countRechargeAmount(Map<String, Object> paramMap) {
        return rechargeRecordDao.countRechargeAmount(paramMap);
    }

    /**
     * 支付宝大额入金
     *
     * @param data
     */
    public void alipayIncome(JSONObject data) {
        RechargeRecord rechargeRecord = new RechargeRecord();
        String channelTrxNo = (String) data.get("order_no");
        String amount = (String) data.get("amount");
        String payeeIdentity = (String) data.get("payee_identity");
        String payerName = (String) data.get("payer_name");
        String payerIdentity = (String) data.get("payer_identity");

        if (this.isRecordExist(channelTrxNo)) { //根据通道订单号查询记录是否已经存在，如果已存在，则忽略本次通知
            LOGGER.error("支付宝入金，根据通道订单号查询到充值记录已存在，本次通知将会被丢弃，记账本id：[{}]，回调信息：[{}]", payeeIdentity, data.toJSONString());
            return;
        }

        //根据收款方账号id找到商户信息
        //EmployerAccountInfo employerAccountInfo =
        //        employerAccountInfoFacade.getOneBySubMerchantNoAndPayChannelNo(payeeIdentity,ChannelNoEnum.ALIPAY.name());

        //修改为从账号记录里查询
        ReportAccountHistory reportAccountHistory = reportAccountHistoryFacade.getByChannelMerchantNoAndPayChannelNo(payeeIdentity, ChannelNoEnum.ALIPAY.name());
        if (reportAccountHistory == null) {
            LOGGER.error("支付宝入金，插入充值记录失败，账号记录不存在，记账本id：[{}]，回调信息：[{}]", payeeIdentity, data.toJSONString());
            return;
        }

        Merchant mainstay = merchantQueryFacade.getByMchNo(reportAccountHistory.getMainstayNo());
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(reportAccountHistory.getMainstayNo(), ChannelNoEnum.ALIPAY.name());

        String rechargeOrderId = LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) +
                sequenceFacade.nextRedisId(SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getKey(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getWidth());
        rechargeRecord.setRechargeOrderId(rechargeOrderId)
                .setRechargeAmount(new BigDecimal(amount))
                .setPayeeIdentity(payeeIdentity)
                .setPayeeIdentityType(IdentityTypeEnum.ACCOUNT_BOOK_ID.getValue())
                .setChannelOrderId(channelTrxNo)
                .setTransPayTime(new Date())
                .setEmployerNo(reportAccountHistory.getEmployerNo())
                //可能存在异名，是有reportAccountHistory的employerName
                .setEmployerName(reportAccountHistory.getEmployerName())
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setChannelCode(ChannelNoEnum.ALIPAY.name())
                .setChannelName(ChannelNoEnum.ALIPAY.getDesc())
                .setChannelType((short) ChannelTypeEnum.ALIPAY.getValue())
                .setMainstayName(mainstay.getMchName())
                .setMainstayNo(mainstay.getMchNo())
                .setRechargeStatus(RechargeStatusEnum.SUCCESS.getCode().shortValue())
                .setChannelTrxNo(channelTrxNo)
                .setPayeeName(reportAccountHistory.getEmployerName())
                .setPayerIdentityType(IdentityTypeEnum.BANK_ACCOUNT_NO.getValue())
                .setPayerName(payerName)
                .setPayerBankName("")
                .setPayerIdentity(payerIdentity)
                .setCurrentBalance(getBalance(reportAccountHistory, mainstayChannelRelation));
        orderBiz.addRechargeRecord(rechargeRecord);
        notifyRechargeMerchant(rechargeRecord);
    }

    private BigDecimal getBalance(ReportAccountHistory reportAccountHistory, MainstayChannelRelation mainstayChannelRelation) {
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setEmployerNo(reportAccountHistory.getEmployerNo());
        amountQueryDto.setChannelType(reportAccountHistory.getPayChannelType());
        amountQueryDto.setAgreementNo(reportAccountHistory.getChannelMerchantNo());
        amountQueryDto.setMainstayNo(reportAccountHistory.getMainstayNo());
        amountQueryDto.setChannelNo(reportAccountHistory.getPayChannelNo());
        amountQueryDto.setChannelMchNo(mainstayChannelRelation.getChannelMchNo());
        amountQueryDto.setSubMerchantNo(reportAccountHistory.getChannelMerchantNo());
        return accountQueryBiz.getBalance(amountQueryDto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void cmbRechargeRecord(CmbIncomeRecord cmbIncomeRecord, EmployerAccountInfo employerAccountInfo) {
        //查询是否已经存在相关记录
        RechargeRecord oldRecord = rechargeRecordMapper.selectOne(
                new QueryWrapper<RechargeRecord>().lambda().eq(RechargeRecord::getChannelOrderId, cmbIncomeRecord.getChannelTrxNo()));
        if (oldRecord != null) {
            LOGGER.error("招行来账入账，充值记录已存在，渠道流水号：[{}]", cmbIncomeRecord.getChannelTrxNo());
            return;
        }
        RechargeRecord rechargeRecord = buildCmbRecharge(employerAccountInfo, cmbIncomeRecord);
        orderBiz.addRechargeRecord(rechargeRecord);
        notifyRechargeMerchant(rechargeRecord);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_RECHARGE_BILL, NotifyTypeEnum.CMB_RECHARGE_BILL.getValue(), MessageMsgDest.TAG_CMB_RECHARGE_BILL, cmbIncomeRecord.getChannelTrxNo(), MsgDelayLevelEnum.M_1.getValue());
    }

    private RechargeRecord buildCmbRecharge(EmployerAccountInfo employerAccountInfo, CmbIncomeRecord cmbIncomeRecord) {
        String rechargeOrderId = LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getKey(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getWidth());
        RechargeRecord rechargeRecord = new RechargeRecord();
        rechargeRecord.setRechargeType(RechargeTypeEnum.BANK_NET_RECHARGE.getCode());
        rechargeRecord.setRechargeStatus(RechargeStatusEnum.SUCCESS.getCode().shortValue());
        rechargeRecord.setChannelOrderId(cmbIncomeRecord.getChannelTrxNo());
        rechargeRecord.setRechargeOrderId(rechargeOrderId);
        rechargeRecord.setChannelCode(ChannelNoEnum.CMB.name());
        rechargeRecord.setChannelName(ChannelNoEnum.CMB.getDesc());
        rechargeRecord.setRechargeAmount(cmbIncomeRecord.getAmount());
        rechargeRecord.setChannelType(Integer.valueOf(ChannelTypeEnum.BANK.getValue()).shortValue());
        rechargeRecord.setAccountBookId(cmbIncomeRecord.getMchNo().substring(1));
        rechargeRecord.setChannelTrxNo(cmbIncomeRecord.getChannelTrxNo());
        rechargeRecord.setCreateBy(cmbIncomeRecord.getBankAccountName());
        rechargeRecord.setCreateTime(new Date());
        rechargeRecord.setUpdateTime(new Date());

        rechargeRecord.setEmployerNo(employerAccountInfo.getEmployerNo());
        rechargeRecord.setEmployerName(employerAccountInfo.getEmployerName());
        rechargeRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
        rechargeRecord.setMainstayName(employerAccountInfo.getMainstayName());
        rechargeRecord.setPayeeName(employerAccountInfo.getEmployerName());
        rechargeRecord.setPayeeIdentityType(IdentityTypeEnum.CMB_ACCOUNTBOOK_ID.getValue());
        rechargeRecord.setPayeeIdentity(employerAccountInfo.getSubMerchantNo());

        rechargeRecord.setPayerName(cmbIncomeRecord.getBankAccountName());
        rechargeRecord.setPayerIdentity(cmbIncomeRecord.getBankAccountNumber());
        rechargeRecord.setPayerBankName(cmbIncomeRecord.getBankName());
        rechargeRecord.setPayerIdentityType(IdentityTypeEnum.BANK_ACCOUNT_NO.getValue());

        rechargeRecord.setTransPayTime(new Date());
        rechargeRecord.setRemark(cmbIncomeRecord.getRechargeRemark());

        // 查询当前余额
        BigDecimal balance = accountQueryBiz.getBalance(employerAccountInfo);
        rechargeRecord.setCurrentBalance(balance);
        return rechargeRecord;
    }

    public Map<String, Object> sumCmbRechargeAmt(Map<String, Object> paramMap) {
        return rechargeRecordDao.sumCmbRechargeAmt(paramMap);
    }

    public void notifyRechargeMerchant(RechargeRecord rechargeRecord) {
        MerchantNotifySet merchantNotifySet = merchantNotifySetFacade.getByMchNoAndType(rechargeRecord.getEmployerNo(), MerchantNotifySetTypeEnum.RECHARGE_NOTIFY.getValue());
        if (merchantNotifySet == null) {
            return;
        }
        String notifyUrl = merchantNotifySet.getNotifyUrl();
        Integer notifyStatus = merchantNotifySet.getNotifyStatus();
        if (StringUtils.isNotBlank(notifyUrl) && notifyStatus == CommonStatusEnum.ACTIVE.getValue()) {
            String cacheKey = TradeConstant.RECHARGE_NOTIFY_MCH_REDIS_PREFIX + ":" + rechargeRecord.getRechargeOrderId();
            if (redisClient.get(cacheKey) == null) {
                //缓存五分钟，期间不会再通知商户
                redisClient.set(cacheKey, rechargeRecord.getRechargeOrderId(), 300);
                MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillRechargeRecordParam(rechargeRecord, notifyUrl);
                merchantNotifyParam.setNotifyUrl(notifyUrl);
                tradeNotifyBiz.notifyMerchantRecharge(merchantNotifyParam, rechargeRecord);
            }
        }
    }

    public Map<String, Object> sumRechargeRecord(Map<String, Object> paramMap) {
        return rechargeRecordDao.sumRechargeRecord(paramMap);
    }

    public boolean isRecordExist(String channelTrxNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("channelTrxNo", channelTrxNo);
        return rechargeRecordDao.isRechargeRecordExist(paramMap);
    }
}
