package com.zhixianghui.service.trade.process;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.exception.QpsExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.service.MerchantCkhQuoteFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.facade.trade.bo.OrderItemCountBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.*;
import com.zhixianghui.service.trade.biz.DynamicMsgBiz;
import com.zhixianghui.service.trade.biz.FeeOrderBatchBiz;
import com.zhixianghui.service.trade.biz.OfflineOrderBiz;
import com.zhixianghui.service.trade.biz.OfflineOrderItemBiz;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.service.trade.utils.BuildVoUtil;
import com.zhixianghui.service.trade.utils.RedisRateLimiter;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.util.ParameterMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class OfflineOrderGrantHandler {

    @Autowired
    private OfflineOrderBiz orderBiz;
    @Autowired
    private OfflineOrderItemBiz orderItemBiz;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private RedisRateLimiter redisRateLimiter;
    @Autowired
    private TradeHelperBiz tradeHelperBiz;
    @Autowired
    private FeeOrderBatchBiz feeOrderBatchBiz;
    @Autowired
    private DynamicMsgBiz dynamicMsgBiz;
    @Autowired
    protected RedisLock redisLock;

    @Reference
    private MerchantCkhQuoteFacade merchantCkhQuoteFacade;
    @Reference
    private RiskControlFacade riskControlFacade;
    @Reference
    private NotifyFacade notifyFacade;

    public void startAccept(OfflineOrder offlineOrder) {
        final String platBatchNo = offlineOrder.getPlatBatchNo();
        final String employerNo = offlineOrder.getEmployerNo();

        log.info("[受理环节: {}] -- mchNo:[{}] ==>批次开始受理。]", platBatchNo, employerNo);


        //判断相等 不能用Objects.equals 隐藏了错误 null时返回false
        if(offlineOrder.getBatchStatus() == OrderStatusEnum.CLOSED_GRANT.getValue()){
            log.info("[受理环节: {}] 批次已关闭,不再进行受理",platBatchNo);
            return;
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("platBatchNo", platBatchNo);
        paramMap.put("orderItemStatus", OrderItemStatusEnum.CREATE.getValue());
        final List<String> platTrxNoList = orderItemBiz.listPlatTrxNoByBatchNo(paramMap);
        this.processItem(platTrxNoList);

        orderBiz.update(offlineOrder);

        this.updateAcceptBatchCount(platBatchNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void startGrant(String platBatchNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", platBatchNo);

        Date now = new Date();

        final OfflineOrder order = orderBiz.getByPlatBatchNo(platBatchNo);

        Map<String, Object> param = new HashMap<>();
        param.put("platBatchNo", platBatchNo);
        param.put("orderItemStatus", OrderItemStatusEnum.ACCEPTED.getValue());
        final List<String> platTrxNoList = orderItemBiz.listPlatTrxNoByBatchNo(param);
        if (platTrxNoList == null || platTrxNoList.isEmpty()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无可发放订单");
        }

        boolean missBillFlag = false;
        for (String platTrxNo : platTrxNoList) {
            final OfflineOrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
            if (orderItem.getWorkerBillFilePath() == null || orderItem.getWorkerBillFilePath().isEmpty()) {
                log.error("回单不能为空，平台流水号：{}", orderItem.getPlatTrxNo());
                missBillFlag = true;
                break;
            }
        }
        if (missBillFlag) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次存在未提交回单的明细，中止发放！");
        }


        order.setBatchStatus(OrderStatusEnum.GRANTING.getValue());
        order.setUpdateTime(now);
        order.setCompleteTime(now);
        orderBiz.update(order);

        for (String platTrxNo : platTrxNoList) {
            final OfflineOrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);

            orderItem.setUpdateTime(now);
            orderItem.setCompleteTime(now);
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANTING.getValue());
            orderItemBiz.update(orderItem);

            this.notifyGrantStart(order.getEmployerNo(), order.getMainstayNo(), order.getPlatBatchNo(), platTrxNo);

        }
    }

    public void notifyGrantStart(String employerNo, String mainstayNo, String platBatchNo,String platTrxNo) {
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNo", platTrxNo);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CKH_OFFLINE, employerNo,platTrxNo,
                    NotifyTypeEnum.TRADE_NOTIFY_OFFLINE.getValue(), MessageMsgDest.TAG_TRADE_CKH_OFFLINE,JSON.toJSONString(infoMap),
                    MsgDelayLevelEnum.S_1.getValue());

    }

    public void handleGrant(String platTrxNo) {
        final OfflineOrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
        final OfflineOrder order = orderBiz.getByPlatBatchNo(orderItem.getPlatBatchNo());

        //获取锁
        log.info("[发放环节: {}]==>获取风控锁", orderItem.getPlatTrxNo());
        String lockKey = TradeConstant.RCMS_LOCK_KEY + orderItem.getReceiveIdCardNoDecrypt();
        String clientId = redisLock.tryLockLong(lockKey,30000,1);
        if(clientId == null){
            log.info("[发放环节: {}]==>获取风控锁失败，重新放回发放队列", orderItem.getPlatTrxNo());
            // 加上时间限制，避免因为锁无法释放而一直重试
            if(System.currentTimeMillis() - order.getUpdateTime().getTime() < 10*60*1000){
                notifyGrantStart(orderItem.getEmployerNo(), order.getMainstayNo(), orderItem.getPlatBatchNo(), platTrxNo);
            }
            return;
        }
        try {
            if (Boolean.TRUE.equals(orderItem.getIsPassHangup())) {
                log.info("[发放环节: {}]==>人工通过风控,直接入库", orderItem.getPlatTrxNo());
            } else {
                //校验是否有未完成的挂单,如果有则丢入队列重试,待挂单订单处理完成之后再继续
                if(this.checkPendingOrderByMainstayAndId(orderItem)){
                    notifyGrantStart(orderItem.getEmployerNo(), order.getMainstayNo(), orderItem.getPlatBatchNo(), platTrxNo);
                    redisLock.unlockLong(clientId);
                    return;
                }
                log.info("[发放环节: {}]==>调用风控接口", orderItem.getPlatTrxNo());
                OrderItem zxhOrderItem = new OrderItem();
                BeanUtil.copyProperties(orderItem, zxhOrderItem);
                SettleRiskControlVo settleRiskControlVo = BuildVoUtil.fillSettleRiskControlVo(zxhOrderItem,zxhOrderItem.getCreateTime());
                long startTime = System.currentTimeMillis();
                RiskControlResult riskControlResult = riskControlFacade.processSettle(settleRiskControlVo);
                long endTime = System.currentTimeMillis();
                log.info("[发放环节：风控执行时间==>{}ms]", endTime - startTime);
                boolean isPass = riskOp(riskControlResult, orderItem);
                if (!isPass) {
                    return;
                }
            }
            orderItem.setUpdateTime(new Date());
            orderItem.setCompleteTime(new Date());
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_SUCCESS.getValue());
            orderItemBiz.update(orderItem);
        } catch (Exception e) {
            log.error("处理发放失败：", e);
        }finally {
            redisLock.unlockLong(clientId);
        }

        updateGrantBatchCount(order.getPlatBatchNo());
    }

    /**
     * 更新批次表受理统计信息
     *
     * @param platBatchNo 批次订单编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAcceptBatchCount(String platBatchNo){
        log.info("[受理环节: {}]==>更新批次表受理统计信息", platBatchNo);
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        OfflineOrder order = orderBiz.getOne(paramMap);
        if(order == null ||!Objects.equals(OrderStatusEnum.IMPORTING.getValue(), order.getBatchStatus())){
            return;
        }

        //如果请求数还未入库不进行统计
        if(order.getRequestCount().equals(0)){
            return;
        }
        OrderItemCountBo resultBo = orderItemBiz.getCountBoByPlatBatchNo(platBatchNo);

        log.info("[受理环节: {}]==>受理统计信息resultVo：{}", platBatchNo, JSON.toJSONString(resultBo));

        order.setUpdateTime(new Date());
        BeanUtils.copyProperties(resultBo,order);
        boolean isAccessComplete = order.getRequestCount().equals(resultBo.getAcceptedCount() + resultBo.getFailCount());
        // 受理完成
        if (isAccessComplete) {
            log.info("[受理环节: {}]==>批次受理完成", platBatchNo);
            if (resultBo.getAcceptedCount() == 0) {
                // 全部受理失败，状态为关闭
                order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
                order.setCompleteTime(new Date());
            } else {
                //接口实时发放，受理完直接发起发放流程
                order.setBatchStatus(OrderStatusEnum.PENDING_GRANT.getValue());
            }
            try {
                orderBiz.update(order);
            }catch (Exception e){
                log.error("[受理环节: {}]==>受理完成统计更新数据库异常 mq进行重试 errMsg:{}",platBatchNo,e);
                throw CommonExceptions.COMMON_RETRY_ERROR;
            }
            log.info("[受理环节: {}]==>批次受理完成 成功更新批次状态", platBatchNo);
            return;
        }

        log.debug("[受理环节: {}]==>order: {}", platBatchNo, JsonUtil.toString(order));
        orderBiz.update(order);
    }

    public void updateGrantBatchCount(String platBatchNo){
        log.info("[发放环节: {}]==>更新批次表发放统计信息", platBatchNo);
        OfflineOrder order = orderBiz.getByPlatBatchNo(platBatchNo);
        if(order == null || !Objects.equals(order.getBatchStatus(),OrderStatusEnum.GRANTING.getValue())){
            return;
        }
        OrderItemCountBo resultBo = orderItemBiz.getCountBoByPlatBatchNo(platBatchNo);

        log.info("[发放环节: {}]==>发放统计信息resultVo：{}", platBatchNo, JSON.toJSONString(resultBo));

        order.setSuccessCount(resultBo.getSuccessCount());
        order.setSuccessNetAmount(resultBo.getSuccessNetAmount());
        order.setSuccessFee(resultBo.getSuccessFee());
        order.setSuccessTaskAmount(resultBo.getSuccessTaskAmount());
        order.setSuccessTaxAmount(resultBo.getSuccessTaxAmount());
        order.setFailCount(resultBo.getFailCount());
        order.setFailTaskAmount(resultBo.getFailTaskAmount());
        order.setFailNetAmount(resultBo.getFailNetAmount());
        log.info("[发放环节: {}]==> getAcceptedCount：{}, getRequestCount:{}", platBatchNo, order.getAcceptedCount(),order.getRequestCount());
        boolean isComplete = order.getRequestCount().equals(resultBo.getSuccessCount() + resultBo.getFailCount());
        if (isComplete) {
            log.info("[发放环节: {}]==>批次发放完成", platBatchNo);
            Date completeTime = new Date();
            order.setCompleteTime(completeTime);
            order.setBatchStatus(OrderStatusEnum.FINISH_GRANT.getValue());
            try {
                orderBiz.update(order);
            }catch (Exception e){
                log.error("[发放环节: {}]==>批次发放完成统计更新数据库异常 mq进行重试 errMsg:{}",platBatchNo,e);
                throw CommonExceptions.COMMON_RETRY_ERROR;
            }
            log.info("[发放环节: {}]==>批次发放完成 成功更新批次状态", platBatchNo);

            //推送动态
            if (order.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue()){
                dynamicMsgBiz.putDynamicMsg(order.getEmployerNo(),String.format(
                        DynamicMsgEnum.BATCH_GRANT.getMsg(),order.getPlatBatchNo(),order.getSuccessCount(),order.getFailCount()));
            }
            return;
        }
        orderBiz.update(order);
    }

    private void processItem(List<String> platTrxNoList) {
        //处理每一笔订单明细
        platTrxNoList.forEach(
                platTrxNo->{
                    OfflineOrderItem item = orderItemBiz.getByPlatTrxNo(platTrxNo);
                    if (!Objects.equals(item.getOrderItemStatus(), OrderItemStatusEnum.CREATE.getValue())) {
                        return;
                    }
                    log.info("[受理环节: {}]==>订单明细开始受理", item.getPlatTrxNo());
                    Long acceptTimes = getAcceptTimes(platTrxNo);
                    try {
                        // 鉴权
                        processAuth(item);

                        final BigDecimal serviceFee = calculateServiceFee(item.getEmployerNo(), item.getMainstayNo(), item.getOrderItemNetAmount());

                        //更新状态为已受理状态
                        item.setOrderItemFee(serviceFee);
                        item.setUpdateTime(new Date());
                        item.setOrderItemStatus(OrderItemStatusEnum.ACCEPTED.getValue());
                    } catch (BizException e) {
                        log.info("[受理环节: {}]==>受理业务失败，原因：{}", item.getPlatTrxNo(), e.getErrMsg());
                        if (e.getApiErrorCode()== ApiExceptions.API_TRADE_AUTH_EXCEPTION.getApiErrorCode()
                        || e.getApiErrorCode()== QpsExceptions.QPS_NEED_BLOCK_EX.getApiErrorCode()){
                            if(acceptTimes < TradeConstant.MAX_ACCESS_TIMES + 1){
                                log.info("[受理环节: {}==> 鉴权异常进行重试],异常信息：{}", item.getPlatTrxNo(), e.getErrMsg());
                                processItem(ListUtil.of(platTrxNo));
                            }else{
                                log.info("[受理环节: {}==> 重试到达上限 停止重试]",item.getPlatTrxNo());
                                BuildVoUtil.fillOrderItemAcceptFail(item, e.getApiErrorCode(), StringUtils.isBlank(e.getErrMsg()) ? "要素认证情况未知" : e.getErrMsg());
                                orderItemBiz.update(item);
                            }
                            return;
                        }
                        BuildVoUtil.fillOrderItemAcceptFail(item, e.getApiErrorCode(), e.getErrMsg());
                        orderItemBiz.update(item);
                        return;
                    } catch (Exception e) {
                        log.error("[受理环节: {}]==>受理系统异常，原因：", item.getPlatTrxNo(), e);
                        if(acceptTimes < TradeConstant.MAX_ACCESS_TIMES + 1){
                            log.error("[受理环节: {"+item.getPlatTrxNo()+"}==> 未知系统异常进行重试]",e);
                            processItem(ListUtil.of(platTrxNo));
                        }else{
                            log.warn("[受理环节: {}==> 重试到达上限 停止重试]",item.getPlatTrxNo());
                            BuildVoUtil.fillOrderItemAcceptFail(item, "", "要素认证情况未知");
                            orderItemBiz.update(item);
                        }
                        return;
                    }
                    // 更新
                    orderItemBiz.update(item);
                }
        );
    }

    private Long getAcceptTimes(String platTrxNo) {
        Long acceptTime = 0L;
        try {
            acceptTime = redisClient.incr(TradeConstant.ACCEPT_TIME_REDIS_PREFIX + platTrxNo);
            redisClient.expire(TradeConstant.ACCEPT_TIME_REDIS_PREFIX + platTrxNo,60*60);
        }catch (Exception e){
            log.error("[受理环节: {}] ==> redis获取重试次数异常 忽略", platTrxNo, e);
        }
        return acceptTime;
    }

    /**
     * 鉴权
     * @param item 订单明细
     */
    private void processAuth(OfflineOrderItem item) {
        OrderItem orderItem = new OrderItem();
        BeanUtil.copyProperties(item, orderItem);
        tradeHelperBiz.processBankCardAuth(orderItem, AuthTypeEnum.IDCARD_NAME);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderItem(String platTrxNo) {
        final OfflineOrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
        if (feeOrderBatchBiz.isGenerateFee(orderItem.getEmployerNo(),orderItem.getMainstayNo(),orderItem.getCompleteTime(), FeeSourceEnum.OUTSIDE_ORDER.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("服务费账单已出，不可删除");
        }
        orderItemBiz.deleteOrderItem(platTrxNo);
        final OfflineOrder offlineOrder = orderBiz.getByPlatBatchNo(orderItem.getPlatBatchNo());

        offlineOrder.setRequestCount(offlineOrder.getRequestCount()-1);
        offlineOrder.setRequestTaskAmount(offlineOrder.getRequestTaskAmount().subtract(orderItem.getOrderItemTaskAmount()));
        offlineOrder.setRequestNetAmount(offlineOrder.getRequestNetAmount().subtract(orderItem.getOrderItemAmount()));

        if (orderItem.getOrderItemStatus().intValue() == OrderItemStatusEnum.ACCEPTED.getValue() || orderItem.getOrderItemStatus().intValue() == OrderItemStatusEnum.GRANT_SUCCESS.getValue()) {
            offlineOrder.setAcceptedCount(offlineOrder.getAcceptedCount()-1);
            offlineOrder.setAcceptedTaskAmount(offlineOrder.getAcceptedTaskAmount().subtract(orderItem.getOrderItemTaskAmount()));
            offlineOrder.setAcceptedOrderAmount(offlineOrder.getAcceptedOrderAmount().subtract(orderItem.getOrderItemAmount()));
            offlineOrder.setAcceptedNetAmount(offlineOrder.getAcceptedNetAmount().subtract(orderItem.getOrderItemNetAmount()));
            offlineOrder.setAcceptedFee(offlineOrder.getAcceptedFee().subtract(orderItem.getOrderItemFee() == null ? BigDecimal.ZERO : orderItem.getOrderItemFee()));
        }

        if (orderItem.getOrderItemStatus().intValue() == OrderItemStatusEnum.GRANT_SUCCESS.getValue()) {
            offlineOrder.setSuccessCount(offlineOrder.getSuccessCount()-1);
            offlineOrder.setSuccessNetAmount(offlineOrder.getSuccessNetAmount().subtract(orderItem.getOrderItemNetAmount()));
            offlineOrder.setSuccessFee(offlineOrder.getSuccessFee().subtract(orderItem.getOrderItemFee() == null ? BigDecimal.ZERO : orderItem.getOrderItemFee()));
            offlineOrder.setSuccessTaskAmount(offlineOrder.getSuccessTaskAmount().subtract(orderItem.getOrderItemTaskAmount()));
        }
        offlineOrder.setUpdateTime(new Date());
        orderBiz.update(offlineOrder);
    }

    public BigDecimal calculateServiceFee(String employerNo, String mainstayNo, BigDecimal netAmount) {
        //查看报价规则
        MerchantCkhQuote merchantCkhQuote = merchantCkhQuoteFacade.getFeeRate(employerNo,mainstayNo, ProductNoEnum.CKH.getValue());
        if (merchantCkhQuote == null){
            log.error("找不到对应的报价单：[{}]",employerNo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("计费异常");
        }
        //计算服务费
        BigDecimal serviceFee = AmountUtil.mul(netAmount, merchantCkhQuote.getServiceFeeRate(), 2);
        //最低费用0.01元
        if (serviceFee.compareTo(BigDecimal.ZERO) != 1){
            serviceFee = new BigDecimal("0.01");
        }
        return serviceFee;
    }

    /**
     * 根据风控结果操作
     * @param riskControlResult 风控接口返回结果
     * @param orderItem 要操作的订单明细
     * @return 风控结果 true为通过 false为拦截
     */
    private boolean riskOp(RiskControlResult riskControlResult, OfflineOrderItem orderItem) {
        Integer riskCode = riskControlResult.getCode();
        if(riskCode.equals(ControlAtomEnum.PENDING.getValue())){
            log.info("[发放环节: {}]==>触发风控挂单，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(),orderItem.getPlatBatchNo(),riskControlResult.getErrMsg());
            orderItemBiz.update2Hangup(orderItem,riskControlResult);
            return false;
        }else if (riskCode.equals(ControlAtomEnum.REJECT.getValue())){
            log.info("[发放环节: {}]==>触发风控拒绝，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(),orderItem.getPlatBatchNo(),riskControlResult.getErrMsg());
            orderItemBiz.update2Reject(orderItem,riskControlResult);
            return false;
        }else if (riskCode.equals(ControlAtomEnum.PASS.getValue())){
            log.info("[发放环节: {}]==>通过风控，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(),orderItem.getPlatBatchNo(),riskControlResult.getErrMsg());
            // 通过风控 持久化数据 订单明细-(已受理)->发放中
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANTING.getValue());
            orderItem.setJsonStr(null);
            orderItem.setVersion(orderItem.getVersion() + 1);
            return true;
        }
        return false;
    }

    private boolean checkPendingOrderByMainstayAndId(OfflineOrderItem orderItem){
        String receiveIdCardNoMd5 = orderItem.getReceiveIdCardNoMd5();
        String mainstayNo = orderItem.getMainstayNo();

        Map<String, Object> param = new ParameterMap<>();
        param.put("receiveIdCardNoMd5", receiveIdCardNoMd5);
        //param.put("mainstayNo", mainstayNo);
        param.put("createBeginDate", DateUtil.addDay(new Date(), -7));
        param.put("createEndDate", DateUtil.getDayEnd(new Date()));

        PageResult<List<RiskcontrolOrderItem>> listPageResult = riskControlFacade.listPagePendingOrder(param, PageParam.newInstance(1, 10));
        Long totalRecord = listPageResult.getTotalRecord();

        if (totalRecord==null || totalRecord.longValue()<=0L){
            return false;
        }
        else {
            log.info("风控未处理订单:{}", JSON.toJSONString(listPageResult.getData()));
            return true;
        }
    }
}
