package com.zhixianghui.service.trade.vo.res;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-21 16:08
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JSONType(naming= PropertyNamingStrategy.SnakeCase)
public class NotifyMchResVo extends ApiBizBaseDto {
    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 订单明细状态
     */
    private Integer orderItemStatus;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 收款账户(卡号/支付宝账号)
     */
    private String receiveAccountNo;

    /**
     * 手机号
     */
    private String receivePhoneNo;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 微信支付appid（appid）
     */
    private String appid;

    /**
     * 订单明细实发金额
     */
    private String orderItemNetAmount;

    /**
     * 订单明细代征主体服务费
     */
    private String orderItemFee;

    /**
     * 订单明细(总)金额
     */
    private String orderItemAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 风控类型
     */
    private List<String> riskType;
}
