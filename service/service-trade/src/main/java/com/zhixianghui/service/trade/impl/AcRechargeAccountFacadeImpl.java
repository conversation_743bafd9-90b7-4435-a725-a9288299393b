package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.entity.AcRechargeAccount;
import com.zhixianghui.facade.trade.service.AcRechargeAccountFacade;
import com.zhixianghui.service.trade.biz.AcRechargeAccountBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AcRechargeAccountFacadeImpl implements AcRechargeAccountFacade {

    private final AcRechargeAccountBiz acRechargeAccountBiz;

    @Override
    public void insert(AcRechargeAccount model) {
        acRechargeAccountBiz.insert(model);
    }

    @Override
    public AcRechargeAccount getRechargeAccountByMchNoAndMainstayNo(String mchNo, String mainstayNo, String payChannelNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("payChannelNo", payChannelNo);
        return acRechargeAccountBiz.getOne(paramMap);
    }

    @Override
    public void checkExistOrInsert(AcRechargeAccount model) {
        acRechargeAccountBiz.checkExistOrInsert(model);
    }

    @Override
    public void updateAccountInfo(Long accountId, String accountNo, String accountName) {
        acRechargeAccountBiz.updateAccountInfo(accountId, accountNo, accountName);
    }

    @Override
    public long getCountByModel(AcRechargeAccount model) {
        return acRechargeAccountBiz.getCountByModel(model);
    }
}
