package com.zhixianghui.service.trade.helper;

import com.zhixianghui.common.statics.enums.common.RedisKeyPrefixEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.message.SmsFacade;
import com.zhixianghui.facade.trade.entity.WechatUserInfo;
import com.zhixianghui.service.trade.vo.res.LoginResVo;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.zhixianghui.service.trade.enums.PermissionConstant.*;


/**
 * 验证码校验
 */
@Component
@Log4j2
public class CaptchaHelper {

    /**
     * 验证码短信模板
     */
    private static final String smsTemplate = "【汇聚智享】您的动态验证码为%s，请在页面输入完成验证。如非本人操作请忽略。";

    @Autowired
    private RedisClient redisClient;

    @Reference
    private SmsFacade smsFacade;


    /**
     * 发送短信验证码
     *
     * @param phone
     */
    public void sendSmsCode(String phone) throws BizException {
        String key = RedisKeyPrefixEnum.WEB_EMPLOYER_SESSION_KEY.name() + ":" + SMS_CODE_KEY + ":" + phone;
        // 重发间隔校验
        String value = redisClient.get(key);
        String[] codeAndSendTime;
        if (StringUtil.isNotEmpty(value) && (codeAndSendTime = value.split("-")).length >= 2) {
            long sendTime = Long.parseLong(codeAndSendTime[1]);
            int diff = (int) (System.currentTimeMillis() - sendTime) / 1000;
            if (diff < SMS_CODE_RESEND_TIME) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发送频繁，请于" + (SMS_CODE_RESEND_TIME - diff) + "秒后重试");
            }
        }

        String smsCode = RandomUtil.getDigitStr(6);  // 生成6位随机验证码
        // 短信发送
        smsFacade.send(phone, genSmsMsg(smsCode), "");

        value = smsCode + "-" + System.currentTimeMillis();  // value格式为 "验证码-发送时间"
        log.info("key" + key + "value{}:" + value);
        redisClient.set(key, value, SMS_CODE_EXPIRE_TIME);
    }

    /**
     * 校验短信验证码
     *
     * @param phone
     * @param code
     */
    public void verifySmsCode(String phone, String code) throws BizException {
        // 请求频率拦截，2秒内只能请求一次
        String key = RedisKeyPrefixEnum.WEB_EMPLOYER_SESSION_KEY.name() + ":" + VERIFY_SMS_CODE_KEY + ":" + phone;
        String value = redisClient.get(key);
        if (StringUtil.isNotEmpty(value)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求频率过快");
        }
        redisClient.set(key, Long.toString(System.currentTimeMillis()), 2);

        // 短信验证码校验
        key = RedisKeyPrefixEnum.WEB_EMPLOYER_SESSION_KEY.name() + ":" + SMS_CODE_KEY + ":" + phone;
        value = redisClient.get(key);
        String[] codeAndSendTime;
        if (StringUtil.isEmpty(value) || (codeAndSendTime = value.split("-")).length < 2) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("短信验证码已失效");
        }
        String smsCode = codeAndSendTime[0];
        if (!code.equals(smsCode)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("短信验证码错误");
        }
    }

    /**
     * 将短信验证码置为失效
     *
     * @param phone
     */
    public void invalidSmsCode(String phone) {
        String key = RedisKeyPrefixEnum.WEB_EMPLOYER_SESSION_KEY.name() + ":" + SMS_CODE_KEY + ":" + phone;
        redisClient.del(key);
    }


    /**
     * 短信验证码发送内容
     *
     * @param smsCode
     * @return
     */
    public static String genSmsMsg(String smsCode) {
        return String.format(smsTemplate, smsCode);
    }

    public LoginResVo createToken(WechatUserInfo userInfo) {

        return null;
    }
}
