package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.wx.WxIncomeRecordVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.entity.WxIncomeRecord;
import com.zhixianghui.service.trade.biz.WithdrawRecordBiz;
import com.zhixianghui.service.trade.biz.WxIncomeRecordBiz;
import com.zhixianghui.facade.trade.service.WxIncomeRecordFacade;
import com.zhixianghui.service.trade.pay.wx.biz.WxWithdrawBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-12-07
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WxIncomeRecordImpl implements WxIncomeRecordFacade {

    private final WxIncomeRecordBiz biz;

    private final WxWithdrawBiz wxWithdrawBiz;

    private final WithdrawRecordBiz withdrawRecordBiz;

    @Override
    public WxIncomeRecord getById(Long id) {
        return biz.getById(id);
    }

    @Override
    public void update(WxIncomeRecord wxIncomeRecord) {
        biz.update(wxIncomeRecord);
    }

    @Override
    public PageResult<List<WxIncomeRecord>> listPage(Map<String, Object> paramMap, PageParam toPageParam) {
        return biz.listPage(paramMap,toPageParam);
    }

}
