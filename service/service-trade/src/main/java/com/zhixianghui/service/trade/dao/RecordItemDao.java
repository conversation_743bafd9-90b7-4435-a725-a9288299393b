package com.zhixianghui.service.trade.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.trade.bo.RecordItemGroupBo;
import com.zhixianghui.facade.trade.dto.DateQueryDTO;
import com.zhixianghui.facade.trade.dto.RiskControlAmountDto;
import com.zhixianghui.facade.trade.dto.RiskControlNumDto;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.vo.ServiceConfirmVo;
import com.zhixianghui.facade.trade.vo.UserGrantVo;
import com.zhixianghui.facade.trade.vo.statistics.MonthOrderVo;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 打款交易流水表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Repository
public class RecordItemDao extends MyBatisDao<RecordItem,Long> {

    public BigDecimal getAmountResult(Map<String,Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("getAmountResult"),paramMap);
    }

    public List<RecordItemGroupBo> getIdcardNoMainstayGroup(Map<String,Object> paramMap, int offSet, int pageSize) {
        return this.getSqlSession().selectList(fillSqlId("getIdcardNoMainstayGroup"), paramMap, new RowBounds(offSet, pageSize));
    }

    public Integer selectOrderCount(DateQueryDTO queryDTO){
        return this.getSqlSession().selectOne(fillSqlId("selectOrderCount"),queryDTO);
    }

    public List<Integer> selectMchCount(DateQueryDTO queryDTO){
        return this.getSqlSession().selectList(fillSqlId("selectMchCount"),queryDTO);
    }

    public List<Integer> selectUserCount(DateQueryDTO queryDTO){
        return this.getSqlSession().selectList(fillSqlId("selectUserCount"),queryDTO);
    }

    public List<MonthOrderVo> selectOrderPrice(DateQueryDTO queryDTO){
        return this.getSqlSession().selectList(fillSqlId("selectOrderPrice"),queryDTO);
    }

    public BigDecimal selectOrderSum(DateQueryDTO queryDTO){
        return this.getSqlSession().selectOne(fillSqlId("selectOrderSum"),queryDTO);
    }

    public Map<String, Object> coreIndexStatistics(Map<String, Object> paramMap) {
        Map<String, Object> coreIndexStatistics = this.getSqlSession().selectOne(fillSqlId("coreIndexStatistics"), paramMap);
        return coreIndexStatistics;
    }

    public Map<String, Object> coreIndexReceivedUserCount(Map<String, Object> paramMap) {
        Map<String, Object> coreIndexStatistics = this.getSqlSession().selectOne(fillSqlId("coreIndexReceivedUserCount"), paramMap);
        return coreIndexStatistics;
    }

    public List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("coreIndexDailyDetail"), paramMap);
    }

    public List<Map<String, Object>> coreIndexDailyDetailReceivedUserCount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("coreIndexDailyDetailReceivedUserCount"), paramMap);
    }

    public List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("coreIndexDetailMonthly"), paramMap);
    }

    public List<Map<String, Object>> coreIndexDetailMonthlyReceivedUserCount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("coreIndexDetailMonthlyReceivedUserCount"), paramMap);
    }

    public List<ServiceConfirmVo> getWorkCategoryCode(Map<String, Object> paramMap, int offset, Integer pageSize) {
        return this.getSqlSession().selectList(fillSqlId("getWorkCategoryCode"), paramMap, new RowBounds(offset, pageSize));
    }

    public List<RecordItem> listByOffset(Map<String, Object> paramMap, int offset, int maxSize) {
        return this.getSqlSession().selectList(fillSqlId("listBy"), paramMap, new RowBounds(offset, maxSize));
    }

    public List<String> getWorkCategoryCodeCount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("getWorkCategoryCodeCount"),paramMap);
    }

    public Map<String, RiskControlAmountDto> getAmountResultGroup(Map<String,Object> paramMap) {
        return this.getSqlSession().selectMap(fillSqlId("getAmountResultGroup"),paramMap,"mainstayNo");
    }

    public Map<String,BigDecimal> getWaiteGrantSumAmount(Map<String,Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("getWaiteGrantSumAmount"), paramMap);
    }

    public Map<String, RiskControlNumDto> getCountTradeTimes(Map<String, Object> paramMap) {
        return this.getSqlSession().selectMap(fillSqlId("getCountTradeTimes"),paramMap,"mainstayNo");
    }

    public Map<String, UserGrantVo> sumGrantSuccessGroupMonth(Map<String, Object> paramMap) {
        return this.getSqlSession().selectMap(fillSqlId("sumGrantSuccessGroupMonth"),paramMap,"mapKey");
    }

    public List<UserGrantVo> listGrantSuccessByIdCard(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("listGrantSuccessByIdCard"),paramMap);
    }

    public BigDecimal getUserGrantSumAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("getUserGrantSumAmount"),paramMap);
    }
}
