package com.zhixianghui.service.trade.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface OfflineOrderMapper extends BaseMapper<OfflineOrder> {

    List<FeeOrderBatch> selectFeeOrder(Map<String,Object> paramMap);

    public BigDecimal sumWaitInvoiceAmount(Map<String, Object> paramMap);

    List<String> selectOrderNo(Map<String, Object> params);
}
