package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;
import com.zhixianghui.service.trade.dao.FreelanceStatDao;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 自由职业者月统计表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-08-02
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FreelanceStatBiz {

    private final FreelanceStatDao freelancestatDao;

    public List<FreelanceStat> freelanceStat(Map<String, Object> param) {
        return freelancestatDao.listBy(param);
    }

    public PageResult<List<FreelanceStat>> freelanceStat(Map<String, Object> param, PageParam pageParam) {
        return freelancestatDao.listPage(param, pageParam);
    }

    public PageResult<List<Map<String, Object>>> freelanceStat2(Map<String, Object> param, PageParam pageParam) {
        return freelancestatDao.listPage2(param, pageParam);
    }

    public PageResult<List<UserInfo>> idCardList(Map<String, Object> param, PageParam pageParam) {
        return freelancestatDao.idCardList(param, pageParam);
    }

    public FreelanceStatVo count(Map<String, Object> param) {
        return freelancestatDao.getOne("freelanceStatCount", param);
    }

    public FreelanceStatVo merchantStatCount(Map<String, Object> param) {
        return freelancestatDao.getOne("merchantStatCount", param);

    }

    public Integer freelanceCount(Map<String, Object> param) {
        return freelancestatDao.getOne("freelanceCount", param);
    }

    public void insert(List<FreelanceStat> list) {
        freelancestatDao.insert(list);

    }

    public void insert(FreelanceStat freelanceStat) {
        freelancestatDao.insert(freelanceStat);

    }


    public PageResult<List<FreelanceStat>> listPage(Map<String, Object> param, PageParam newInstance) {
        return  freelancestatDao.listPage(param, newInstance);
    }

    public FreelanceStat getById(Long id) {
        return  freelancestatDao.getOne(new HashMap<String, Object>() {{
           put( "id", id);
        }});
    }

    public void update(List<FreelanceStat> data) {
        freelancestatDao.update(data);

    }

    public void update(FreelanceStat stat) {
        freelancestatDao.update(stat);
    }


}
