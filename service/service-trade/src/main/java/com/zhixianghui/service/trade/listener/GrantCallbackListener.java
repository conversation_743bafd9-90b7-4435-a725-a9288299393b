package com.zhixianghui.service.trade.listener;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.biz.AcLocalPayBiz;
import com.zhixianghui.service.trade.biz.AcMerchantBalanceBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.factory.TradeFactory;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName CallbackListener
 * @Description TODO
 * @Date 2022/7/4 14:23
 */
@Slf4j
@Component
public class GrantCallbackListener {

    @Autowired
    private RecordItemBiz recordItemBiz;

    @Autowired
    private TradeFactory tradeFactory;

    @Autowired
    private AcLocalPayBiz acLocalPayBiz;
    @Autowired
    private OrderItemBiz orderItemBiz;

    /**
     * 支付宝回调
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_CALLBACK, selectorExpression = MessageMsgDest.TAG_ALIPAY_CALLBACK_ENTRUST_TRANSFER ,consumeThreadMax = 20, consumerGroup = "trans_to_alipay")
    public class TransToAlipayMessageListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("转账到支付宝的消息内容不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            JSONObject contentJson = JSON.parseObject(jsonParam);
            String status = contentJson.getString("status");
            String payFundOrderId = contentJson.getString("pay_fund_order_id");
            String orderId = contentJson.getString("order_id");
            String bankOrderNo = StringUtils.substringBefore(contentJson.getString("out_biz_no"),"P");
            RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(bankOrderNo);
            if(recordItem == null){
                log.error("回调打款流水号:{},在本地找不到对应数据",bankOrderNo);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("回调打款流水号:{},在本地找不到对应数据,"+bankOrderNo);
            }
            if (StringUtils.equals(status, "SUCCESS") && payFundOrderId != null) {
                PayReceiveRespVo respVo = new PayReceiveRespVo();
                respVo.setBizMsg(null);
                respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
                respVo.setBankTrxNo(orderId);
                respVo.setBankOrderNo(bankOrderNo);
                respVo.setBizCode("");
                tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
            } else if (StringUtils.equals(status, "CLOSED") || StringUtils.equals(status,"FAIL") || StringUtils.equals(status,"REFUND")) {
                String failReason = StringUtils.equals(status,"CLOSED") ? "支付宝超时关闭" : contentJson.getString("fail_reason");
                PayReceiveRespVo respVo = new PayReceiveRespVo();
                respVo.setBizMsg(null);
                respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                respVo.setBankTrxNo(orderId);
                respVo.setBankOrderNo(bankOrderNo);
                respVo.setBizCode("");
                respVo.setBizMsg(failReason);
                tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
            }else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("");
            }
        }
    }

    /**
     * 汇聚
     * 接受发放回调
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC, selectorExpression = MessageMsgDest.TAG_GRANT_NOTIFY,consumeThreadMax = 20, consumerGroup = "grantNotifyConsume")
    public class GrantNotifyMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发放回调接受 msg不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            PayReceiveRespVo payReceiveRespVo = JsonUtil.toBean(msg, PayReceiveRespVo.class);
            String remitPlatTrxNo = payReceiveRespVo.getBankOrderNo();
            RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(remitPlatTrxNo);
            if(recordItem == null){
                log.error("回调打款流水号:{},在本地找不到对应数据",remitPlatTrxNo);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("回调打款流水号:{},在本地找不到对应数据,"+remitPlatTrxNo);
            }

            Integer bankPayStatus = payReceiveRespVo.getBankPayStatus();
            OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
            if(Objects.equals(recordItem.getProcessStatus(), RecordItemStatusEnum.PAY_CREATED.getValue())
                    && Objects.equals(orderItem.getOrderItemStatus(), OrderItemStatusEnum.GRANTING.getValue())) {
                if (bankPayStatus.equals(BankPayStatusEnum.SUCCESS.getValue())) {
                    acLocalPayBiz.handleMainstayNoLocalAccount(recordItem);
                }
            }

            tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(payReceiveRespVo,recordItem);
        }
    }

    /**
     * 易税回调
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_YISHUI_CALL_BACK, selectorExpression = MessageMsgDest.TAG_YISHUI_CALL_BACK ,consumeThreadMax = 20, consumerGroup = "yishuiCallBackConsumer")
    public class YishuiCallMessageListener extends BaseRocketMQListener<String>{

        @Autowired
        private RecordItemBiz recordItemBiz;

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("易税回调消息内容不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            JSONObject contentJson = JSON.parseObject(jsonParam);
            String status = contentJson.getString("withdrawal_status");
            String orderId = contentJson.getString("enterprise_order_ext_id");
            RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(contentJson.getString("outer_trade_no"));
            if (StringUtils.equals(status, "TRADE_FINISHED")) {

                PayReceiveRespVo respVo = new PayReceiveRespVo();
                respVo.setBizMsg(null);
                respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
                respVo.setBankTrxNo(orderId);
                respVo.setBankOrderNo(recordItem.getRemitPlatTrxNo());
                respVo.setBizCode("");
                tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
            } else if (StringUtils.equals(status, "TRADE_FAILED ")) {
                String failReason = "详情请到供应商的后台查看";
                PayReceiveRespVo respVo = new PayReceiveRespVo();
                respVo.setBizMsg(null);
                respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                respVo.setBankTrxNo(orderId);
                respVo.setBankOrderNo(contentJson.getString("outer_trade_no"));
                respVo.setBizCode("");
                respVo.setBizMsg(failReason);
                tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
            }else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("");
            }
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_CALLBACK_QUERY, selectorExpression = MessageMsgDest.TAG_CMB_CALLBACK_QUERY ,consumeThreadMax = 10, consumerGroup = "cmbCallbackQueryConsumer")
    public class CmbCallBackQueryMessageListener extends BaseRocketMQListener<String>{
        @Reference
        private CmbFacade cmbFacade;

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("消息内容不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            Dict dict = JSONUtil.toBean(jsonParam, Dict.class);

            String platBatchNo = dict.getStr("platBatchNo");
            String remitNo = dict.getStr("remitNo");
            String accountNo = dict.getStr("accountNo");
            //反查招行接口
            JSONObject queryResult = null;
            try {
                queryResult = cmbFacade.payBatchQuery(accountNo, platBatchNo);
            } catch (BizException e) {
                if (e.getErrMsg().contains("没有符合查询条件的数据")) {
                    queryResult = null;
                    return;
                } else {
                    throw e;
                }
            }
            RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(remitNo);
            if (queryResult!=null
                    && queryResult.getJSONArray("bb6bpdqyz2")!=null
                    &&!queryResult.getJSONArray("bb6bpdqyz2").isEmpty()
                    &&queryResult.getJSONArray("bb6bpdqyz2").getJSONObject(0)!=null){
//                JSONObject detailItem = queryResult.getJSONArray("bb6bpdqyz1").getJSONObject(0);
                JSONObject detailItem = queryResult.getJSONArray("bb6bpdqyz2").getJSONObject(0);
                if (StringUtils.equals("S", detailItem.getString("stscod"))) {
                    PayReceiveRespVo respVo = new PayReceiveRespVo();
                    respVo.setBizMsg(null);
                    respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
                    respVo.setBankTrxNo( detailItem.getString("trxseq"));
                    respVo.setBankOrderNo(detailItem.getString("trssqn"));
                    respVo.setBizCode("");
                    tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
                } else if (StringUtils.equals("E",detailItem.getString("stscod"))){
                    String failReason = detailItem.getString("errdsp");
                    PayReceiveRespVo respVo = new PayReceiveRespVo();
                    respVo.setBizMsg(null);
                    respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                    if (detailItem != null) {
                        respVo.setBankTrxNo(detailItem.getString("trssqn"));
                        respVo.setBankOrderNo( detailItem.getString("trxseq"));
                    }

                    respVo.setBizCode("");
                    respVo.setBizMsg(failReason);
                    tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
                }else {
                    throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("反查失败");
                }
            }else {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("反查失败");
            }
        }
    }

    /**
     * 支付宝，退款回调
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_CALLBACK,selectorExpression = MessageMsgDest.TAG_ALIPAY_CALLBACK_REFUND,consumeThreadMax = 8, consumerGroup = "callBackRefundGroup")
    public class AlipayCallBackRefundListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {
            if (StringUtil.isEmpty(jsonParam)){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("jsonParam不能为空");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            JSONObject contentJson = JSON.parseObject(jsonParam);
            String outBizNo = StringUtils.substringBeforeLast(contentJson.getString("out_biz_no"),"R");
            RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(outBizNo);
            tradeFactory.getGrantor(recordItem.getProductNo()).callbackRefund(recordItem);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MOCK_SUCCESS_CALL_BACK, selectorExpression = MessageMsgDest.TAG_MOCK_SUCCESS_CALL_BACK ,consumeThreadMax = 1, consumerGroup = "mockConsumerCallBackConsumer")
    public class MockSuccessCallMessageListener extends BaseRocketMQListener<String>{

        @Autowired
        private RecordItemBiz recordItemBiz;

        @Override
        public void validateJsonParam(String platTrxNo) {
            if (StringUtil.isEmpty(platTrxNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("回调消息内容不能为null");
            }
        }

        @Override
        public void consumeMessage(String platTrxNo) {

            RecordItem recordItem = recordItemBiz.getByPlatTrxNo(platTrxNo);

            PayReceiveRespVo respVo = new PayReceiveRespVo();
            respVo.setBizMsg(null);
            respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
            respVo.setBankTrxNo(RandomUtil.randomNumbers(8));
            respVo.setBankOrderNo(recordItem.getRemitPlatTrxNo());
            respVo.setBizCode("");
            tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);

        }
    }
}
