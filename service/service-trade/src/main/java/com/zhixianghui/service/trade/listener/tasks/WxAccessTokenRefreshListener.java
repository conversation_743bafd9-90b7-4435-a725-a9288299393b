package com.zhixianghui.service.trade.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.trade.biz.WeChatUserBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName WxAccessTokenRefreshListener
 * @Description TODO
 * @Date 2022/10/19 11:16
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ACCESS_TOKEN_REFRESH, consumeThreadMax = 1, consumerGroup = "accessTokenRefreshConsumer")
public class WxAccessTokenRefreshListener extends TaskRocketMQListener<JSONObject> {

    @Autowired
    private WeChatUserBiz weChatUserBiz;

    @Override
    public void runTask(JSONObject jsonParam) {
        weChatUserBiz.applyAccessToken();
    }
}
