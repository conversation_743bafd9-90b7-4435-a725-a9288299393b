package com.zhixianghui.service.trade.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;

//@Configuration
//@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
//@ConditionalOnClass({Servlet.class, DispatcherServlet.class, WebMvcConfigurer.class})
@Deprecated
public class JacksonAutoConfiguration {

    /**
     * 配置jackson
     * @return
     */
    @Bean
    @ConditionalOnProperty(name = "hjzx.api.jackson-snake-case.enabled", havingValue = "true", matchIfMissing = true)
    public Jackson2ObjectMapperBuilderCustomizer customJackson() {
        return jacksonObjectMapperBuilder -> jacksonObjectMapperBuilder.propertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
    }
}
