package com.zhixianghui.service.trade.pay.wx;

import cn.hutool.extra.spring.SpringUtil;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.service.trade.pay.wx.biz.WxNotifyBiz;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月13日 18:18:00
 */
@Slf4j
public class WxSuccessPay extends IWxPay {

    private final Integer Log_type = WxAmountChangeLogTypeEnum.PAYMENT.getValue();

    private WxNotifyBiz wxNotifyBiz=SpringUtil.getBean(WxNotifyBiz.class);

    private ChangesFunds serviceFeeFunds;

    public WxSuccessPay(WxPayParam wxPayParam) {
        super(wxPayParam);
    }


    @Override
    public void handle() {
        buildChangesFunds();
        PayReceiveRespVo payReceiveRespVo = getPayReceiveRespVo();
        wxOrderUpdateBiz.updateOrder(changesFunds,payReceiveRespVo);
        wxNotifyBiz.updateChangeFounds(serviceFeeFunds);
    }

    public void updateFounds() {
        buildChangesFunds();
        wxOrderUpdateBiz.updateChangesFunds(changesFunds);
        wxNotifyBiz.updateChangeFounds(serviceFeeFunds);
    }

    @Override
    public PayRespVo getResVo(boolean isUpdateFound) {
        if (isUpdateFound) {
            updateFounds();
        }
        PayRespVo respVo = new PayRespVo();
        respVo.setBankTrxNo(wxPayParam.getDetailId());
        respVo.setBankOrderNo(wxPayParam.getOutDetailNo());
        respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
        respVo.setBizCode("");
        respVo.setBizMsg("");
        return respVo;
    }

    private PayReceiveRespVo getPayReceiveRespVo() {
        PayReceiveRespVo payReceiveRespVo = new PayReceiveRespVo();
        payReceiveRespVo.setBankTrxNo(wxPayParam.getDetailId());
        payReceiveRespVo.setBankOrderNo(wxPayParam.getOutDetailNo());
        payReceiveRespVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
        payReceiveRespVo.setBizCode("");
        return payReceiveRespVo;
    }

    public void buildChangesFunds() {
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(wxPayParam.getPlatTrxNo());
        String logKey = WxUtil.getLogKey(wxPayParam.getPlatTrxNo(), Log_type, MerchantTypeEnum.EMPLOYER);
        ChangesFunds changesFunds = new ChangesFunds();
        changesFunds.setLogKey(logKey);
        changesFunds.setMchNo(wxPayParam.getEmployerNo());
        changesFunds.setMchName(wxPayParam.getMchName());
        changesFunds.setMainstayNo(wxPayParam.getMainstayNo());
        changesFunds.setMainstayName(wxPayParam.getRealPayerName());
        changesFunds.setPlatTrxNo(wxPayParam.getPlatTrxNo());
        changesFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        changesFunds.setAmountChangeType(Log_type);
        changesFunds.setCreateTime(new Date());
        changesFunds.setAmount(-AmountUtil.changeToFen(orderItem.getOrderItemAmount()));
        changesFunds.setFrozenAmount(-AmountUtil.changeToFen(orderItem.getOrderItemAmount()));
        this.changesFunds = changesFunds;
        buildFee();
    }


    public void buildFee(){
        String logKey = WxUtil.getLogKey(wxPayParam.getPlatTrxNo(), Log_type, MerchantTypeEnum.MAINSTAY);
        ChangesFunds changesFunds = new ChangesFunds();
        changesFunds.setLogKey(logKey);
//        changesFunds.setMchNo(wxPayParam.getEmployerNo());
//        changesFunds.setMchName(wxPayParam.getMchName());
        changesFunds.setMainstayNo(wxPayParam.getMainstayNo());
        changesFunds.setMainstayName(wxPayParam.getRealPayerName());
        changesFunds.setPlatTrxNo(wxPayParam.getPlatTrxNo());
        changesFunds.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        changesFunds.setAmountChangeType(Log_type);
        changesFunds.setCreateTime(new Date());
        changesFunds.setAmount(AmountUtil.changeToFen(wxPayParam.getServiceFee()));
        changesFunds.setFrozenAmount(0L);
        this.serviceFeeFunds = changesFunds;
    }
}