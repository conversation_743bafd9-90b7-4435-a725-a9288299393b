package com.zhixianghui.service.trade.process;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.constants.redis.RedisLua;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.enums.merchant.CertificateTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.service.KeyPairRecordFacade;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.service.joinpay.JoinPayFacade;
import com.zhixianghui.facade.banklink.service.pay.JXHPayFacade;
import com.zhixianghui.facade.banklink.service.wx.WxPayFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawReqVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawRespVo;
import com.zhixianghui.facade.banklink.vo.withdraw.SecurityVo;
import com.zhixianghui.facade.common.dto.BankInfoDto;
import com.zhixianghui.facade.common.entity.config.BankInfo;
import com.zhixianghui.facade.common.entity.config.BankOrganization;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.*;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.service.MerchantBankAccountFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.dto.FreezeBalanceDTO;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.enums.WithdrawStatusEnum;
import com.zhixianghui.service.trade.biz.ChangesFundsBiz;
import com.zhixianghui.service.trade.biz.CmbMerchantBalanceBiz;
import com.zhixianghui.service.trade.biz.WithdrawRecordBiz;
import com.zhixianghui.service.trade.biz.WxMerchantBalanceBiz;
import com.zhixianghui.service.trade.pay.jxh.biz.JXHWithdrawBiz;
import com.zhixianghui.service.trade.pay.wx.biz.WxWithdrawBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Log4j2
public class WithdrawBiz {

    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private MerchantBankAccountFacade merchantBankAccountFacade;
    @Reference
    private BankInfoFacade bankInfoFacade;
    @Reference
    private BankOrganizationFacade bankOrganizationFacade;
    @Reference
    private KeyPairRecordFacade keyPairRecordFacade;
    @Reference(retries = -1)
    private JoinPayFacade joinPayFacade;
    @Reference(retries = -1)
    private JXHPayFacade jxhPayFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private WxPayFacade wxPayFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    private final RedisClient redisClient;
    private final RedisLock redisLock;
    private final WxMerchantBalanceBiz wxMerchantBalanceBiz;
    private final ChangesFundsBiz changesFundsBiz;
    private final WxWithdrawBiz wxWithdrawBiz;
    private final JXHWithdrawBiz jxhWithdrawBiz;
    @Reference
    private CmbFacade cmbFacade;


    final private WithdrawRecordBiz withdrawRecordBiz;
    final private CmbMerchantBalanceBiz cmbMerchantBalanceBiz;

    public WithdrawRecord withdraw(WithdrawDto withdrawDto) {
        String withdrawNo = null;
        try {
            if (withdrawDto.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()) {
                withdrawNo = getWENo();
                final MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(withdrawDto.getMainstayNo(), withdrawDto.getChannelNo());
                if (Objects.isNull(mainstayChannelRelation)) {
                    log.warn("mainstayNo:[{}],代征主体与通道关系不存在", withdrawDto.getMainstayNo());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体与通道关系不存在");
                }
                if (Objects.isNull(mainstayChannelRelation.getChannelMchNo())) {
                    log.warn("mainstayNo:[{}],代征主体未正确报备", withdrawDto.getMainstayNo());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体未正确报备");
                }

                //判断属于哪个通道
                if (withdrawDto.getChannelNo().equals(ChannelNoEnum.WXPAY.name())){
                    return wxWithdrawBiz.mainstayWxPayWithdraw(withdrawDto,withdrawNo,mainstayChannelRelation);
                }else if (withdrawDto.getChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name())||withdrawDto.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())){
                    return jxhWithdrawBiz.mainstayWxPayWithdraw(withdrawDto,withdrawNo,mainstayChannelRelation);
                }else if (withdrawDto.getChannelNo().equals(ChannelNoEnum.ALIPAY.name())){
                    return mainstayAlipayWithdraw(withdrawDto,withdrawNo,mainstayChannelRelation);
                }else if (withdrawDto.getChannelNo().equals(ChannelNoEnum.CMB.name())) {

                    BankInfoDto bankInfoDto = validCanWithDraw(withdrawDto.getMainstayNo());

                    withdrawNo = "WC" + LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.CMB_WITHDRAW_NO_SEQ.getPrefix(), SequenceBizKeyEnum.CMB_WITHDRAW_NO_SEQ.getKey(), SequenceBizKeyEnum.CMB_WITHDRAW_NO_SEQ.getWidth());
                    WithdrawReqVo withdrawReqVo = new WithdrawReqVo();
                    withdrawReqVo.setBankOrderNo(withdrawNo);
                    withdrawReqVo.setChannelType(ChannelTypeEnum.BANK.getValue());
                    withdrawReqVo.setPayerAccountBookId("********");
                    withdrawReqVo.setPayerAgreementNo(mainstayChannelRelation.getChannelMchNo());
                    withdrawReqVo.setRealPayerName(mainstayChannelRelation.getMainstayName());
                    withdrawReqVo.setReceiveAmount(withdrawDto.getAmount());
                    withdrawReqVo.setRemitRemark(withdrawDto.getRemark());
                    withdrawReqVo.setReceiveAccountNo(bankInfoDto.getBankAccountNo());
                    withdrawReqVo.setReceiveName(bankInfoDto.getBankAccountName());
                    withdrawReqVo.setBankChannelNo(bankInfoDto.getBankChannelNo());
                    if (StringUtils.isBlank(bankInfoDto.getBankName())) {
                        withdrawReqVo.setPayeeBankName(bankInfoDto.getBranchBankName());
                    } else {
                        withdrawReqVo.setPayeeBankName(bankInfoDto.getBankName());
                    }

                    WithdrawRecord withdrawRecord = fillWithdrawRecord(withdrawReqVo, null, mainstayChannelRelation, MerchantTypeEnum.MAINSTAY);
                    final WithdrawRecord withdrawRecordNew = withdrawRecordBiz.addWithdrawRecord(withdrawRecord);
                    final WithdrawRespVo withdraw = cmbFacade.withdraw(withdrawReqVo);
                    //处理招行本地账户，冻结余额
                    if(ObjectUtil.isNotEmpty(withdraw) && BankPayStatusEnum.PROCESS.getValue() == withdraw.getBankPayStatus()) {
                        FreezeBalanceDTO param = new FreezeBalanceDTO();
                        param.setMainstayNo(withdrawRecord.getMainstayNo());
                        param.setMainstayName(withdrawRecord.getMainstayName());
                        param.setPlatTrxNo(withdrawRecord.getWithdrawNo());
                        param.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
                        param.setFrozenAmount(withdrawRecord.getAmount());
                        cmbMerchantBalanceBiz.freezeBalance(param);
                    }
                    return withdrawRecordNew;
                }else{
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持此通道提现");
                }
            } else {
                if (withdrawDto.getChannelNo().equals(ChannelNoEnum.JOINPAY.name()) && withdrawDto.getChannelType() == ChannelTypeEnum.BANK.getValue()) {
                    withdrawNo = getWENo();
                    return bankWithdraw(withdrawDto, withdrawNo);
                } else if(withdrawDto.getChannelNo().equals(ChannelNoEnum.ALIPAY.name())) {
                    final EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(withdrawDto.getEmployerNo(), withdrawDto.getMainstayNo(), withdrawDto.getChannelType());
                    if (withdrawDto.getChannelNo().equals(ChannelNoEnum.ALIPAY.name())){
                        String config = dataDictionaryFacade.getSystemConfig("ALIPAY_ACCOUNT_WITHDRAW_LIMIT");
                        List<String> list = null;
                        if (StringUtils.isNotBlank(config)) {
                            list = ListUtil.toList(config.split(","));
                        }

                        if (list != null && list.contains(employerAccountInfo.getSubMerchantNo())){
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("此交易模式不支持线上提现");
                        }
                    }

                    if (Objects.isNull(employerAccountInfo)) {
                        log.warn("employerNo:[{}],用工企业账号不存在", withdrawDto.getEmployerNo());
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体与通道关系不存在");
                    }
                    if (Objects.isNull(employerAccountInfo.getSubMerchantNo()) || Objects.isNull(employerAccountInfo.getSubAgreementNo()) || Objects.isNull(employerAccountInfo.getSubAlipayUserId())) {
                        log.warn("mainstayNo:[{}],代征主体未正确报备", withdrawDto.getMainstayNo());
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体未正确报备");
                    }

                    withdrawNo = "WE" + LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.WITHDRAW_NO_SEQ.getPrefix(), SequenceBizKeyEnum.WITHDRAW_NO_SEQ.getKey(), SequenceBizKeyEnum.WITHDRAW_NO_SEQ.getWidth());
                    WithdrawReqVo withdrawReqVo = new WithdrawReqVo();
                    withdrawReqVo.setBankOrderNo(withdrawNo);
                    withdrawReqVo.setChannelType(withdrawDto.getChannelType());
                    withdrawReqVo.setPayerAccountBookId(employerAccountInfo.getSubMerchantNo());
                    withdrawReqVo.setPayerAgreementNo(employerAccountInfo.getSubAgreementNo());
                    withdrawReqVo.setRealPayerName(employerAccountInfo.getEmployerName());
                    withdrawReqVo.setReceiveAmount(withdrawDto.getAmount());
                    withdrawReqVo.setRemitRemark(withdrawDto.getRemark());
                    withdrawReqVo.setReceiveAccountNo(employerAccountInfo.getSubAlipayUserId());

                    WithdrawRecord withdrawRecord = fillWithdrawRecord(withdrawReqVo, employerAccountInfo, null, MerchantTypeEnum.EMPLOYER);
                    final WithdrawRecord withdrawRecordNew = withdrawRecordBiz.addWithdrawRecord(withdrawRecord);
                    final WithdrawRespVo withdraw = alipayFacade.withdraw(withdrawReqVo);
                    return withdrawRecordNew;
                } else if (withdrawDto.getChannelNo().equals(ChannelNoEnum.CMB.name()) && withdrawDto.getChannelType() == ChannelTypeEnum.BANK.getValue()) {
                    final EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(withdrawDto.getEmployerNo(), withdrawDto.getMainstayNo(), withdrawDto.getChannelType());
                    final MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(withdrawDto.getMainstayNo(), withdrawDto.getChannelNo());

                    if (Objects.isNull(employerAccountInfo)) {
                        log.warn("employerNo:[{}],用工企业账号不存在", withdrawDto.getEmployerNo());
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体与通道关系不存在");
                    }
                    if (Objects.isNull(employerAccountInfo.getSubMerchantNo()) || Objects.isNull(employerAccountInfo.getSubAgreementNo()) || Objects.isNull(employerAccountInfo.getSubAlipayUserId())) {
                        log.warn("mainstayNo:[{}],代征主体未正确报备", withdrawDto.getMainstayNo());
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体未正确报备");
                    }

                    BankInfoDto bankInfoDto = validCanWithDraw(withdrawDto.getEmployerNo());

                    withdrawNo = "WC" + LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.CMB_WITHDRAW_NO_SEQ.getPrefix(), SequenceBizKeyEnum.CMB_WITHDRAW_NO_SEQ.getKey(), SequenceBizKeyEnum.CMB_WITHDRAW_NO_SEQ.getWidth());
                    WithdrawReqVo withdrawReqVo = new WithdrawReqVo();
                    withdrawReqVo.setBankOrderNo(withdrawNo);
                    withdrawReqVo.setChannelType(withdrawDto.getChannelType());
                    withdrawReqVo.setPayerAccountBookId(employerAccountInfo.getSubMerchantNo());
                    withdrawReqVo.setPayerAgreementNo(employerAccountInfo.getParentMerchantNo());
                    withdrawReqVo.setRealPayerName(mainstayChannelRelation.getMainstayName());
                    withdrawReqVo.setReceiveAmount(withdrawDto.getAmount());
                    withdrawReqVo.setRemitRemark(withdrawDto.getRemark());
                    withdrawReqVo.setReceiveAccountNo(bankInfoDto.getBankAccountNo());
                    withdrawReqVo.setReceiveName(bankInfoDto.getBankAccountName());
                    withdrawReqVo.setBankChannelNo(bankInfoDto.getBankChannelNo());
                    if (StringUtils.isBlank(bankInfoDto.getBankName())) {
                        withdrawReqVo.setPayeeBankName(bankInfoDto.getBranchBankName());
                    } else {
                        withdrawReqVo.setPayeeBankName(bankInfoDto.getBankName());
                    }

                    WithdrawRecord withdrawRecord = fillWithdrawRecord(withdrawReqVo, employerAccountInfo, null, MerchantTypeEnum.EMPLOYER);
                    final WithdrawRecord withdrawRecordNew = withdrawRecordBiz.addWithdrawRecord(withdrawRecord);
                    final WithdrawRespVo withdraw = cmbFacade.withdraw(withdrawReqVo);
                    //处理招行本地账户，冻结余额
                    if(ObjectUtil.isNotEmpty(withdraw) && BankPayStatusEnum.PROCESS.getValue() == withdraw.getBankPayStatus()) {
                        FreezeBalanceDTO param = new FreezeBalanceDTO();
                        param.setMchNo(withdrawRecord.getEmployerNo());
                        param.setMchName(withdrawRecord.getEmployerName());
                        param.setMainstayNo(withdrawRecord.getMainstayNo());
                        param.setMainstayName(withdrawRecord.getMainstayName());
                        param.setPlatTrxNo(withdrawRecord.getWithdrawNo());
                        param.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
                        param.setFrozenAmount(withdrawRecord.getAmount());
                        cmbMerchantBalanceBiz.freezeBalance(param);
                    }
                    return withdrawRecordNew;
                } else if (withdrawDto.getChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name()) && withdrawDto.getChannelType() == ChannelTypeEnum.BANK.getValue()) {
                    withdrawNo = getWENo();
                    return jxhWithdrawBiz.employerPayWithdraw(withdrawDto, withdrawNo);
                } else {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持该通道在线提现");
                }
            }
        } catch (BizException e) {

            if (withdrawNo != null) {
                final WithdrawRecord withdrawRecord = withdrawRecordBiz.getByWithdrawRecordNo(withdrawNo);
                if (withdrawRecord != null) {
                    withdrawRecord.setErrorMsg(e.getErrMsg());
                    withdrawRecord.setErrorCode(e.getApiErrorCode());
                    withdrawRecord.setUpdateTime(new Date());
                    withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.FAIL.getCode());
                    withdrawRecordBiz.updateById(withdrawRecord);
                }
            }

            throw e;
        }

    }

    private WithdrawRecord mainstayAlipayWithdraw(WithdrawDto withdrawDto,String withdrawNo,MainstayChannelRelation mainstayChannelRelation) {
        //BankInfoDto bankInfoDto = validCanWithDraw(withdrawDto.getMainstayNo());
        WithdrawReqVo withdrawReqVo = new WithdrawReqVo();
        withdrawReqVo.setBankOrderNo(withdrawNo);
        withdrawReqVo.setChannelType(withdrawDto.getChannelType());
        withdrawReqVo.setPayerAccountBookId(mainstayChannelRelation.getChannelMchNo());
        withdrawReqVo.setPayerAgreementNo(mainstayChannelRelation.getAgreementNo());
        withdrawReqVo.setRealPayerName(mainstayChannelRelation.getMainstayName());
        withdrawReqVo.setReceiveAmount(withdrawDto.getAmount());
        withdrawReqVo.setRemitRemark(withdrawDto.getRemark());
        withdrawReqVo.setReceiveAccountNo(mainstayChannelRelation.getAlipayUserId());
//        withdrawReqVo.setReceiveAccountNo(bankInfoDto.getBankAccountNo());
//        withdrawReqVo.setBankChannelNo(bankInfoDto.getBankChannelNo());
//        withdrawReqVo.setOrganizationName(bankInfoDto.getBankName());
        withdrawReqVo.setReceiveName(mainstayChannelRelation.getMainstayName());

        WithdrawRecord withdrawRecord = fillWithdrawRecord(withdrawReqVo, null, mainstayChannelRelation, MerchantTypeEnum.MAINSTAY);
        final WithdrawRecord withdrawRecordNew = withdrawRecordBiz.addWithdrawRecord(withdrawRecord);
        final WithdrawRespVo withdraw = alipayFacade.withdraw(withdrawReqVo);
        return withdrawRecordNew;
    }

    private String getWENo() {
        return "WS" + LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.WITHDRAW_NO_SEQ.getPrefix(), SequenceBizKeyEnum.WITHDRAW_NO_SEQ.getKey(), SequenceBizKeyEnum.WITHDRAW_NO_SEQ.getWidth());
    }


    private WithdrawRecord bankWithdraw(WithdrawDto withdrawDto,String no) {
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(
                        withdrawDto.getEmployerNo(), withdrawDto.getMainstayNo(), withdrawDto.getChannelType());

        KeyPairRecord keyPairRecord = keyPairRecordFacade.getByChannelNoAndChannelMchNo(withdrawDto.getChannelNo(),
                employerAccountInfo.getParentMerchantNo());
        log.info("秘钥查询，通道编号：{}，商户编号：{}",withdrawDto.getChannelNo(),employerAccountInfo.getParentMerchantNo());
        if (ObjectUtils.isEmpty(keyPairRecord)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户秘钥不存在");
        }
        SecurityVo securityVo = new SecurityVo();
        securityVo.setMchNo(employerAccountInfo.getParentMerchantNo());
        securityVo.setSettleAmount(new BigDecimal(withdrawDto.getAmount()));
        securityVo.setAltMchNo(employerAccountInfo.getSubMerchantNo());
        securityVo.setMchOrderNo(no);
        securityVo.setKeyPairRecord(keyPairRecord);

        WithdrawRecord withdrawRecord = buildBankWithdrawRecord(withdrawDto, employerAccountInfo, securityVo.getMchOrderNo());
        WithdrawRecord withdrawRecordNew = withdrawRecordBiz.addWithdrawRecord(withdrawRecord);
        joinPayFacade.withdraw(securityVo);
        return withdrawRecordNew;
    }

    private WithdrawRecord buildBankWithdrawRecord(WithdrawDto withdrawDto, EmployerAccountInfo accountInfo, String mchOrderNo) {
        WithdrawRecord withdrawRecord = new WithdrawRecord();
        withdrawRecord.setWithdrawNo(mchOrderNo);
        withdrawRecord.setAmount(new BigDecimal(withdrawDto.getAmount()));
        withdrawRecord.setChannelNo(accountInfo.getPayChannelNo());
        withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.NEW.getCode());
        withdrawRecord.setCreateTime(new Date());
        withdrawRecord.setEmployerName(accountInfo.getEmployerName());
        withdrawRecord.setRemark(withdrawDto.getRemark());
        withdrawRecord.setChannelType(accountInfo.getChannelType());
        withdrawRecord.setMainstayNo(accountInfo.getMainstayNo());
        withdrawRecord.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        withdrawRecord.setMainstayName(accountInfo.getMainstayName());
        withdrawRecord.setReceiveAcctNo(accountInfo.getSubMerchantNo());
        withdrawRecord.setReceiveAcctType(ChannelTypeEnum.BANK.getValue());
        withdrawRecord.setReceiveName(accountInfo.getEmployerName());
        withdrawRecord.setEmployerNo(accountInfo.getEmployerNo());
        return withdrawRecord;
    }

    //
    private BankInfoDto validCanWithDraw(String merchantNo) {
        MerchantBankAccount merchantBankAccount = merchantBankAccountFacade.getByMchNo(merchantNo);
        if (merchantBankAccount == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到商户银行卡信息，提现失败");
        }

        BankInfo bankInfo = bankInfoFacade.getByBankChannelNo(merchantBankAccount.getBankChannelNo());
        if (bankInfo == null || StringUtils.isBlank(bankInfo.getBankCode())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("联行号信息有误，请联系管理员");
        }

        BankOrganization bankOrganization = bankOrganizationFacade.getByBankCode(bankInfo.getBankCode());
        if (bankOrganization == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持提现到此银行");
        }

        BankInfoDto bankInfoDto = new BankInfoDto();
        bankInfoDto.setBankName(bankOrganization.getOrganizationName());
        bankInfoDto.setBankChannelNo(bankInfo.getBankChannelNo());
        bankInfoDto.setBankAccountNo(merchantBankAccount.getAccountNo());
        bankInfoDto.setBranchBankName(merchantBankAccount.getBankName());
        bankInfoDto.setBankAccountName(merchantBankAccount.getAccountName());
        return bankInfoDto;
    }

    private WithdrawRecord fillWithdrawRecord(WithdrawReqVo reqVo, EmployerAccountInfo accountInfo, MainstayChannelRelation relation, MerchantTypeEnum merchantType) {

        WithdrawRecord withdrawRecord = new WithdrawRecord();
        Date time = new Date();
        if (merchantType == MerchantTypeEnum.EMPLOYER) {
            withdrawRecord.setWithdrawNo(reqVo.getBankOrderNo());
            withdrawRecord.setAmount(new BigDecimal(reqVo.getReceiveAmount()));
            withdrawRecord.setChannelNo(accountInfo.getPayChannelNo());
            withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.NEW.getCode());
            withdrawRecord.setCreateTime(time);
            withdrawRecord.setEmployerName(accountInfo.getEmployerName());
            withdrawRecord.setRemark(reqVo.getRemitRemark());
            withdrawRecord.setChannelType(accountInfo.getChannelType());
            withdrawRecord.setMainstayNo(accountInfo.getMainstayNo());
            withdrawRecord.setMerchantType(merchantType.getValue());
            withdrawRecord.setMainstayName(accountInfo.getMainstayName());
            withdrawRecord.setReceiveAcctNo(reqVo.getReceiveAccountNo());
            withdrawRecord.setReceiveAcctType(accountInfo.getChannelType());
            withdrawRecord.setReceiveIdType(CertificateTypeEnum.ID_CARD.getValue());
            withdrawRecord.setReceiveName(reqVo.getReceiveName());
            withdrawRecord.setReceiveAcctType(accountInfo.getChannelType());
            withdrawRecord.setEmployerNo(accountInfo.getEmployerNo());

            return withdrawRecord;
        } else {
            withdrawRecord.setWithdrawNo(reqVo.getBankOrderNo());
            withdrawRecord.setAmount(new BigDecimal(reqVo.getReceiveAmount()));
            withdrawRecord.setChannelNo(relation.getPayChannelNo());
            withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.NEW.getCode());
            withdrawRecord.setCreateTime(time);
            withdrawRecord.setRemark(reqVo.getRemitRemark());
            withdrawRecord.setMainstayNo(relation.getMainstayNo());
            withdrawRecord.setMerchantType(merchantType.getValue());
            withdrawRecord.setMainstayName(relation.getMainstayName());
            withdrawRecord.setReceiveAcctNo(reqVo.getReceiveAccountNo());

            if (StringUtils.equals(relation.getPayChannelNo(), ChannelNoEnum.CMB.name())) {
                withdrawRecord.setReceiveAcctType(ChannelTypeEnum.BANK.getValue());
            } else {
                withdrawRecord.setReceiveAcctType(ChannelTypeEnum.ALIPAY.getValue());
            }


            withdrawRecord.setChannelType(reqVo.getChannelType());
            withdrawRecord.setReceiveIdType(CertificateTypeEnum.ID_CARD.getValue());
            withdrawRecord.setReceiveName(reqVo.getReceiveName());

            return withdrawRecord;
        }

    }


    public void updateWithdrawRecord(String outBizNo,SuccessFailEnum successFailEnum, String failReason){
        withdrawRecordBiz.handleNotify(outBizNo,null,null,successFailEnum,failReason);
    }


    public void withdrawBeforeGetAmount(EmployerAccountInfo employerAccountInfo) {
        String msg = "fail";
        try {
            BigDecimal amount = getAmount(employerAccountInfo);
            if (amount.compareTo(BigDecimal.ZERO) != 1){
                log.error("商户提现失败，商户：[{}]，余额不足，剩余余额：[{}]",employerAccountInfo.getEmployerNo(),amount);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户提现失败");
            }
            //构建商户提现参数
            WithdrawDto withdrawDto = new WithdrawDto();
            withdrawDto.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            withdrawDto.setAmount(amount.toString());
            withdrawDto.setRemark("一键提现");
            withdrawDto.setEmployerNo(employerAccountInfo.getEmployerNo());
            withdrawDto.setMainstayNo(employerAccountInfo.getMainstayNo());
            withdrawDto.setChannelNo(employerAccountInfo.getPayChannelNo());
            withdrawDto.setChannelType(employerAccountInfo.getChannelType());
            withdraw(withdrawDto);
            msg = "success";
        }catch (Exception e){
            log.error("商户提现失败，商户：[{}]",employerAccountInfo.getEmployerNo());
        }
        finalHandle(employerAccountInfo.getMainstayNo(),employerAccountInfo.getEmployerNo(),msg);
    }

    private void finalHandle(String mainstayNo,String employerNo, String msg) {
        List<String> keys = new ArrayList<String>(){{ add(TradeConstant.WITHDRAW_ALL_MERCHNAT + mainstayNo); add(TradeConstant.WITHDRAW_ALL_NUM + mainstayNo);}};
        List<String> argv = new ArrayList<String>(){{ add(employerNo);add(msg);}};
        Long num = (Long) redisClient.evalLua(RedisLua.WITHDRAW_ALL_SCRIPT,TradeConstant.WITHDRAW_ALL_MERCHNAT,keys,argv);
        if (num == 0L){
            List<String> acceptList = new ArrayList<>();
            List<String> failList = new ArrayList<>();
            Map<String,String> map = redisClient.hgetAll(TradeConstant.WITHDRAW_ALL_MERCHNAT + mainstayNo);
            for (Map.Entry<String,String> e : map.entrySet()){
                if (e.getValue().equals("success")){
                    acceptList.add(e.getKey());
                }else if (e.getValue().equals("fail")){
                    failList.add(e.getKey());
                }
            }
            redisClient.del(TradeConstant.WITHDRAW_ALL_NUM + mainstayNo);
            redisClient.del(TradeConstant.WITHDRAW_ALL_MERCHNAT + mainstayNo);
            log.info("一键提现完成，受理成功商户号：[{}]，受理失败商户号：[{}]", JsonUtil.toString(acceptList),JsonUtil.toString(failList));
        }
    }

    private BigDecimal getAmount(EmployerAccountInfo employerAccountInfo) {
        //查询商户余额
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setMainstayNo(employerAccountInfo.getMainstayNo());
        amountQueryDto.setEmployerNo(employerAccountInfo.getEmployerNo());
        amountQueryDto.setChannelType(employerAccountInfo.getChannelType());
        amountQueryDto.setChannelNo(employerAccountInfo.getPayChannelNo());
        amountQueryDto.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
        amountQueryDto.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
        amountQueryDto.setAgreementNo(employerAccountInfo.getSubAgreementNo());
        String amountStr = accountQueryFacade.getAmount(amountQueryDto);
        BigDecimal amount = new BigDecimal(amountStr);
        return amount;
    }
}
