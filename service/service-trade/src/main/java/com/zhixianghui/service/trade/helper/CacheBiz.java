package com.zhixianghui.service.trade.helper;

import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.fee.service.MerchantProductFacade;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 缓存类
 * @date 2020-12-17 11:58
 **/
@Service
public class CacheBiz {
    @Reference
    private MerchantProductFacade merchantProductFacade;
    @Reference
    private MerchantSecretFacade merchantSecretFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private MerchantEmployerQuoteFacade merchantEmployerQuoteFacade;

    @Cacheable(value = "redisCache", key = "methodName + ':' + #mchNo + '_' + #productNo")
    public boolean isOpenProduct(String mchNo, String productNo) {
        return merchantProductFacade.isOpenProduct(mchNo, productNo);
    }

    @Cacheable(value = "dayCache", key = "methodName + ':' + #mchNo")
    public MerchantSecret getMerchantSecretByMchNo(String mchNo){
        return merchantSecretFacade.getByMchNo(mchNo);
    }

//    @Cacheable(value = "amountCache", key = "methodName + ':' + #amountQueryDto.mainstayNo+ ':' + #amountQueryDto.employerNo + ':' + #amountQueryDto.channelType")
    public String getAmount(AmountQueryDto amountQueryDto){
        return accountQueryFacade.getAmount(amountQueryDto);
    }

    @Cacheable(value = "redisCache",key = "methodName + ':' + #mchNo + '_' + #mainstayNo + '_' + #productNo")
    public boolean isExistQuote(String mchNo, String mainstayNo, String productNo) {
        return merchantEmployerQuoteFacade.isExistQuote(mchNo,mainstayNo,productNo);
    }
}
