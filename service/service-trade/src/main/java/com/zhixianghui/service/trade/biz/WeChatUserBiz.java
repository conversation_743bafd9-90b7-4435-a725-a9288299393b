package com.zhixianghui.service.trade.biz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.constants.encry.EncryptKeyConstant;
import com.zhixianghui.common.statics.constants.redis.RedisKeysManage;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SuccessFailCodeEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.pay.MiniAppReceiptOrderFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.export.service.StatisticsGrantFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.*;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.facade.trade.vo.*;
import com.zhixianghui.service.trade.dao.mapper.WechatInfoMapper;
import com.zhixianghui.service.trade.dao.mapper.WechatUserInfoMapper;
import com.zhixianghui.service.trade.enums.VerifyStatusEnum;
import com.zhixianghui.service.trade.enums.WeChatConstant;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import com.zhixianghui.service.trade.utils.UrlUtil;
import com.zhixianghui.service.trade.utils.WeChatUtil;
import com.zhixianghui.service.trade.vo.VerifyInfo;
import com.zhixianghui.service.trade.vo.req.ApplyAccessTokenReqVo;
import com.zhixianghui.service.trade.vo.req.Code2SessionReqVo;
import com.zhixianghui.service.trade.vo.req.LoginReqVo;
import com.zhixianghui.service.trade.vo.res.Code2SessionResVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import okhttp3.Response;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.*;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.*;

/**
 * <AUTHOR>
 * @date 2022/2/14 14:36
 */
@Service
public class WeChatUserBiz {
    private static final Logger LOGGER = LoggerFactory.getLogger(WeChatUserBiz.class);

    @Value("${wx.sign.appId}")
    private String appId;
    @Value("${wx.sign.secret}")
    private String secret;
    @Value("${wx.miniapps.env}")
    private String env;

    @Value("${wx.agentapps.appId}")
    private String agentAppId;

    @Value("${wx.agentapps.secret}")
    private String agentSecret;

    public static final String WE_CHAT_PREFIX = "weChat:";
    public static final String USER_AUTH_LIMIT = "user:auth:limit:";
    public static final String WECHAT_SESSION_KEY_PREFIX = "wechat:session:key:";
    public static final String WECHAT_LOGIN_KEY_PREFIX = "WX:login:key:";
    public static final String WECHAT_ACCESS_TOKEN = "wechat:access:token:";
    public static final int AUTH_LIMIT = 5;
    public static final int AUTH_TIME = 24 * 60 * 60;
    public static final int WECHAT_AUTH_TIME = 30 * 60;

    @Autowired
    private UserInfoBiz userInfoBiz;
    @Autowired
    private SignRecordBiz signRecordBiz;
    @Autowired
    private WechatUserInfoMapper wechatUserInfoMapper;
    @Autowired
    private WechatInfoMapper wechatInfoMapper;
//    @Autowired
//    private UserInfoMapper userInfoMapper;
    @Autowired
    private ZxhLimitBiz limitBiz;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private FastdfsClient fastdfsClient;
    @Reference
    private SequenceFacade sequenceFacade;
    @Autowired
    private RecordItemBiz recordItemBiz;
    @Reference
    private StatisticsGrantFacade statisticsGrantFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MiniAppReceiptOrderFacade miniAppReceiptOrderFacade;


    public WechatUserInfo  register(String phone, String wxUserNo) {
        WechatUserInfo wechatUserInfo = selectByPhone(phone);
        if (wechatUserInfo != null) {
            return wechatUserInfo;
        }
        wechatUserInfo= new WechatUserInfo();
        String userNo = sequenceFacade.nextRedisId(MINI_USER_NO_SEQ.getPrefix(), MINI_USER_NO_SEQ.getKey(), MINI_USER_NO_SEQ.getWidth());

        wechatUserInfo.setUserNo(userNo);
        wechatUserInfo.setWxUserNo(wxUserNo);
        wechatUserInfo.setCreateAt(new Date());
        wechatUserInfo.setLastLoginAt(new Date());
        wechatUserInfo.setUpdateAt(new Date());
        wechatUserInfo.setMobile(phone);
        wechatUserInfoMapper.insert(wechatUserInfo);

        if (exist(wechatUserInfo)) {
            setIdCardNo(wechatUserInfo);
        }
        return wechatUserInfo;
    }

    public boolean exist(WechatUserInfo wechatUserInfo) {
        List<SignRecord> list = signRecordBiz.list(new HashMap<String, Object>() {{
            put("receivePhoneNo", wechatUserInfo.getMobile());
            put("infoStatus", SuccessFailCodeEnum.SUCCESS.getValue());
        }});
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        String idCardMd5 = list.get(0).getReceiveIdCardNoMd5();
        UserInfo userInfo = userInfoBiz.getByIdCardNoMd5(idCardMd5);
        if (userInfo == null) {
            return false;
        }
        wechatUserInfo.setReceiveIdCardNo(list.get(0).getReceiveIdCardNo());
        wechatUserInfo.setReceiveIdCardNoMd5(list.get(0).getReceiveIdCardNoMd5());
        wechatUserInfo.setEncryptKeyId(list.get(0).getEncryptKeyId());
        return true;
    }


    public boolean authRegister(LoginReqVo loginReqVO) {
        Code2SessionResVo response = auth(appId,secret,loginReqVO.getCode());
        if (response == null || response.getErrCode() !=null && response.getErrCode() != WeChatConstant.SUCCESS) {
            return false;
        }
        WechatInfo wechatInfo = wechatInfoMapper.selectOne(new QueryWrapper<WechatInfo>().eq("MINI_OPEN_ID", response.getOpenId()).eq("APP_ID",appId));
        if (wechatInfo != null) {
            redisClient.set(WECHAT_SESSION_KEY_PREFIX + wechatInfo.getUserNo(), response.getSessionKey(), WECHAT_AUTH_TIME);
            loginReqVO.setUserNo(wechatInfo.getUserNo());
            return true;
        }
        String wxUserNo = sequenceFacade.nextRedisId(MINI_NO_SEQ.getPrefix(), MINI_NO_SEQ.getKey(), MINI_NO_SEQ.getWidth());

        wechatInfo = response.toWechatInfo();
        wechatInfo.setUserNo(wxUserNo);
        wechatInfo.setAppId(appId);
        wechatInfoMapper.insert(wechatInfo);
        redisClient.set(WECHAT_SESSION_KEY_PREFIX + wxUserNo, response.getSessionKey(), WECHAT_AUTH_TIME);

        loginReqVO.setUserNo(wechatInfo.getUserNo());
        return true;
    }

    private Code2SessionResVo auth(String appId,String secret,String code) {
        Code2SessionReqVo code2Session = new Code2SessionReqVo(appId, secret, code);
        String result = "";
        try {
            result = OkHttpUtil.getJsonSyncWithHead(UrlUtil.buildUrl(WeChatConstant.AUTH_CODE2SESSION_API, code2Session.toMap()));
            LOGGER.info("[Code2Session]小程序登录授权响应原始信息: {}", JSONObject.toJSON(result));
        } catch (Exception e) {
            LOGGER.error("[Code2Session]小程序登录授权失败:", e);
            return null;
        }
        if (StringUtils.isBlank(result)) {
            return null;
        }
        Code2SessionResVo response = JSONObject.parseObject(result, Code2SessionResVo.class);
        LOGGER.info("[Code2Session]小程序登录授权响应信息: {}", JSONObject.toJSON(response));
        return response;
    }

    public PageResult<List<SignRecordVo>> listPage(String userNo, PageParam pageParam, Integer status) {
        WechatUserInfo userInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().eq("user_no", userNo));
        if (userInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户不存在");
        }
        if (StringUtils.isBlank(userInfo.getReceiveIdCardNoMd5())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户未认证");
        }
        PageResult<List<SignRecord>> data = signRecordBiz.listPage(new HashMap<String, Object>() {{
            put("receiveIdCardNo", userInfo.getReceiveIdCardDecrypt());
//            put("receivePhoneNo", userInfo.getMobile());
            put("signStatus", status);
            put("signStatusList", Lists.newArrayList(SignStatusEnum.SIGN_SUCCESS.getValue(), SignStatusEnum.WAIT_SIGN.getValue(), SignStatusEnum.WAIT_SIGN.getValue()));
        }}, pageParam);

        if (data == null || CollectionUtils.isEmpty(data.getData())) {
            return  PageResult.newInstance(new ArrayList<>(), pageParam.getPageCurrent(), pageParam.getPageSize());
        }
        List<SignRecordVo> recordList = new ArrayList<>();
        for (SignRecord signRecord : data.getData()) {
            SignRecordVo recordVo = new SignRecordVo();
            BeanUtil.copyProperties(signRecord, recordVo);
            recordVo.setCooperateName(recordVo.getEmployerName() + "-" + recordVo.getMainstayName());
            recordList.add(recordVo);
        }
        return PageResult.newInstance(recordList, data.getPageCurrent(), data.getPageSize());
    }

    public void setIdCardNo(WechatUserInfo userInfo) {
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("user_no", userInfo.getUserNo()));
        wechatUserInfo.setEncryptKeyId(userInfo.getEncryptKeyId());
        wechatUserInfo.setAuthStatus(SuccessFailCodeEnum.SUCCESS.getValue());
        wechatUserInfo.setReceiveIdCardNo(userInfo.getReceiveIdCardNo());
        wechatUserInfo.setReceiveIdCardNoMd5(userInfo.getReceiveIdCardNoMd5());
        wechatUserInfoMapper.updateById(wechatUserInfo);
    }

    public VerifyInfo getIdCard(String userNo) {
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("user_no", userNo));
        if (StringUtils.isBlank(wechatUserInfo.getReceiveIdCardNo())) {
            return null;
        }
        UserInfo userInfo = userInfoBiz.getByIdCardNoMd5(MD5Util.getMixMd5Str(wechatUserInfo.getReceiveIdCardDecrypt()));
        if (userInfo == null) {
            return null;
        }
        return new VerifyInfo().setIdCardBackUrl(userInfo.getIdCardBackUrl()).
                setIdCardFrontUrl(userInfo.getIdCardFrontUrl());
    }

    public UserInfo getIdCardByPhone(String phone) {
        WechatUserInfo wechatUserInfo = this.selectByPhone(phone);
        if (StringUtils.isBlank(wechatUserInfo.getReceiveIdCardNo())) {
            return null;
        }
        UserInfo userInfo = userInfoBiz.getByIdCardNoMd5(wechatUserInfo.getReceiveIdCardNoMd5());
        if (userInfo == null) {
            return null;
        }
        return userInfo;
    }

    public UserAuthVo getAuthInfo(String phone) {
        WechatUserInfo wechatUserInfo = this.selectByPhone(phone);
        UserAuthVo userAuthVo = null;
        if (ObjectUtils.isNotEmpty(wechatUserInfo)&&StringUtils.isNoneBlank(wechatUserInfo.getReceiveIdCardNo())) {
            UserInfo userInfo = userInfoBiz.getByIdCardNoMd5(MD5Util.getMixMd5Str(wechatUserInfo.getReceiveIdCardDecrypt()));
            if(ObjectUtils.isEmpty(userInfo))return null;
            userAuthVo = new UserAuthVo().setIdCardBackUrl(userInfo.getIdCardBackUrl()).setName(userInfo.getReceiveNameDecrypt()).
                    setIdCardFrontUrl(userInfo.getIdCardFrontUrl()).setIdCardNo(userInfo.getReceiveIdCardNoDecrypt())
                    .setPhone(phone).setIdCardCopyUrl(userInfo.getIdCardCopyUrl());
        }else {
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("receivePhoneNoMd5",MD5Util.getMixMd5Str(phone));
            paramMap.put("infoStatus",SuccessFailCodeEnum.SUCCESS.getValue());
            List<SignRecord> signRecordList = signRecordBiz.listby2(paramMap);
            if(CollectionUtils.isEmpty(signRecordList)){
                return null;
            }

            SignRecord signRecord = signRecordList.stream().filter(x->
                    StringUtils.isNotBlank(x.getIdCardCopyUrl()) ||
                            (StringUtils.isNotBlank(x.getIdCardBackUrl()) && StringUtils.isNotBlank(x.getIdCardFrontUrl()))).findFirst().orElse(null);

            if (signRecord == null){
                return null;
            }

            userAuthVo = new UserAuthVo().setIdCardBackUrl(signRecord.getIdCardBackUrl()).setName(signRecord.getReceiveNameDecrypt()).
                    setIdCardFrontUrl(signRecord.getIdCardFrontUrl()).setIdCardNo(signRecord.getReceiveIdCardNoDecrypt())
                    .setPhone(phone).setIdCardCopyUrl(signRecord.getIdCardCopyUrl());
        }
        if(ObjectUtils.isNotEmpty(userAuthVo)){
            redisClient.set(RedisKeysManage.getUserInfoAuthKey(phone), AESUtil.encryptECB(JSONObject.toJSONString(userAuthVo), EncryptKeyConstant.AUTH_REDIS_AES_KEY));
        }
        return userAuthVo;
    }

    public boolean verify(VerifyInfo verifyInfo, String userNo) {
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("user_no", userNo));
        if (wechatUserInfo == null || StringUtils.isBlank(wechatUserInfo.getMobile())) {
            LOGGER.error("[{}]认证失败, 当前用户不存在", userNo);
            return false;
        }
        LOGGER.info("[{}] 小程序认证", userNo);
        Integer count = redisClient.get(USER_AUTH_LIMIT + userNo, Integer.class);
        if (count != null && count >= AUTH_LIMIT) {
            LOGGER.warn("[{}] 小程序用户24h内认证次数不能超过5次: {}",  userNo, JSONObject.toJSON(verifyInfo));
            verifyInfo.setVerifyStatus(VerifyStatusEnum.AUTH_FREQUENTLY.getStatus());
            return false;
        }
        // 二要素鉴权
        String logFlag = WE_CHAT_PREFIX + verifyInfo.getName() + "-" + verifyInfo.getIdCardNo() + UUIDUitl.generateMixString(10);
        LOGGER.info("[{}] 小程序用户电子签约二要素鉴权: {}", logFlag, JSONObject.toJSONString(verifyInfo));
        try {
            limitBiz.signAuth(verifyInfo.getName(), verifyInfo.getIdCardNo(), null, logFlag);
        } catch (Exception e) {
            LOGGER.error("[{}] 小程序用户电子签约二要素鉴权失败: {}", logFlag, e);
            verifyInfo.setVerifyStatus(VerifyStatusEnum.UN_VERIFIED.getStatus());
            return false;
        } finally {
            LOGGER.info("[{}] 小程序记录认证次数", userNo);
            String key = USER_AUTH_LIMIT + userNo;
            count = redisClient.get(key, Integer.class);
            if (count == null) {
                redisClient.set(key, 0, AUTH_TIME);
            }
            redisClient.incr(key);
        }

        return insertOrUpdateUserInfo(verifyInfo, userNo);
    }


    private boolean insertOrUpdateUserInfo(VerifyInfo verifyInfo, String userNo) {
        String receiveIdCardNoMd5 =  MD5Util.getMixMd5Str(verifyInfo.getIdCardNo());
        UserInfo userInfo = userInfoBiz.getByIdCardNoMd5(receiveIdCardNoMd5);
        if (userInfo != null) {
            userInfo.setReceiveIdCardNo(verifyInfo.getIdCardNo());
            userInfo.setReceiveIdCardNoEncrypt(verifyInfo.getIdCardNo());
            userInfo.setIdCardFrontUrl(verifyInfo.getIdCardFrontUrl());
            userInfo.setIdCardBackUrl(verifyInfo.getIdCardBackUrl());
            userInfo.setUpdateTime(new Date());
            userInfoBiz.updateById(userInfo);
            updateWeChatUserInfo(userInfo, userNo);
            return true;
        }
        String openUserId = sequenceFacade.nextRedisId(OPEN_USER_ID_SEQ.getPrefix(), OPEN_USER_ID_SEQ.getKey(), OPEN_USER_ID_SEQ.getWidth());
        userInfo = new UserInfo();
        userInfo.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        userInfo.setCreateTime(new Date());
        userInfo.setUpdateTime(userInfo.getCreateTime());
        userInfo.setIdCardBackUrl(verifyInfo.getIdCardBackUrl());
        userInfo.setIdCardFrontUrl(verifyInfo.getIdCardFrontUrl());
        userInfo.setReceiveIdCardNoEncrypt(verifyInfo.getIdCardNo());
        userInfo.setReceiveNameEncrypt(verifyInfo.getName());
        userInfo.setOpenUserId(openUserId);
        userInfoBiz.save(userInfo);
        updateWeChatUserInfo(userInfo, userNo);
        return true;
    }

    public void updateWeChatUserInfo(UserInfo userInfo, String userNo) {
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("user_no", userNo));
        wechatUserInfo.setEncryptKeyId(userInfo.getEncryptKeyId());
        wechatUserInfo.setReceiveIdCardNo(userInfo.getReceiveIdCardNo());
        wechatUserInfo.setReceiveIdCardNoMd5(userInfo.getReceiveIdCardNoMd5());
        wechatUserInfo.setAuthStatus(SuccessFailCodeEnum.SUCCESS.getValue());
        wechatUserInfo.setUpdateAt(new Date());
        wechatUserInfoMapper.updateById(wechatUserInfo);
    }

    public Integer isSign(WechatUserInfo userInfo) {
        if (StringUtils.isBlank(userInfo.getReceiveIdCardNoMd5())) {
            return VerifyStatusEnum.NO_RECORD.getStatus();
        }
        long noSign = signRecordBiz.count(new HashMap<String, Object>() {{
            put("receiveIdCardNo", userInfo.getReceiveIdCardDecrypt());
            put("signStatus", SignStatusEnum.WAIT_SIGN.getValue());
        }});
        long sign = signRecordBiz.count(new HashMap<String, Object>() {{
            put("receiveIdCardNo", userInfo.getReceiveIdCardDecrypt());
            put("signStatus", SignStatusEnum.SIGN_SUCCESS.getValue());
        }});
        if (noSign == 0 && sign == 0) {
            return VerifyStatusEnum.NO_RECORD.getStatus();
        }
        if (noSign != 0) {
            return VerifyStatusEnum.NO_SIGN.getStatus();
        }
        return VerifyStatusEnum.HAS_SIGN.getStatus();
    }

    public SignRecord fetch(WechatUserInfo userInfo) {
        List<SignRecord> data = signRecordBiz.list(new HashMap<String, Object>() {{
            put("receivePhoneNo", userInfo.getMobile());
            put("infoStatus", SuccessFailCodeEnum.SUCCESS.getValue());

        }});
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        return data.get(0);
    }

    public WechatUserInfo selectByPhone(String phone) {
        return  wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("MOBILE", phone)
        );

    }

    public WechatUserInfo selectByUserNo(String userNo) {
        return  wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("USER_NO", userNo)
        );
    }

    public WechatUserInfo registerByWeChatPhone(LoginReqVo loginReqVO, String wxUserNo) {
        String sessionKey = redisClient.get(WECHAT_SESSION_KEY_PREFIX + wxUserNo);
        if (StringUtils.isBlank(sessionKey)) {
            LOGGER.error("[{}]缓存过期, 请重新授权: {}", wxUserNo, JSONObject.toJSONString(loginReqVO));
            return null;
        }
        String phone = requestWeChatPhone(loginReqVO, wxUserNo);
        if (phone == null) {
            return null;
        }
        WechatUserInfo wechatUserInfo = selectByPhone(phone);
        if (wechatUserInfo == null) {
            return register(phone, wxUserNo);
        }
        if (wxUserNo.equals(wechatUserInfo.getWxUserNo())) {
            return wechatUserInfo;
        }
        wechatUserInfo.setWxUserNo(wxUserNo);
        wechatUserInfoMapper.updateById(wechatUserInfo);
        return wechatUserInfo;

    }

    private String requestWeChatPhone(LoginReqVo loginReqVO, String wxUserNo) {
        String sessionKey = redisClient.get(WECHAT_SESSION_KEY_PREFIX + wxUserNo);
        String result = WeChatUtil.getUserInfo(loginReqVO.getEncryptedData(), sessionKey, loginReqVO.getIv());
        LOGGER.info("[{}]手机解密后信息 : {}", wxUserNo, result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        return jsonObject.getString("phoneNumber");
    }


    public String applyAccessToken() {
        ApplyAccessTokenReqVo accessToken = new ApplyAccessTokenReqVo(appId, secret);
        String result = "";
        try {
            result = OkHttpUtil.getJsonSync(UrlUtil.buildUrl(WeChatConstant.APPLY_ACCESS_TOKEN, accessToken.getParam(accessToken)));
        } catch (Exception e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("access_token获取失败: " + e);
        }
        LOGGER.info("access_token请求原始响应数据: {}", result);
        if (StringUtils.isBlank(result)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        String token = jsonObject.getString("access_token");
        redisClient.set(WECHAT_ACCESS_TOKEN,token);
        return token;
    }


    public WechatInfo selectWeChatInfo(String loginFlag) {
        return wechatInfoMapper.selectOne(new QueryWrapper<WechatInfo>().eq("USER_NO", loginFlag).eq("APP_ID",appId));
    }


    public void setRedisToken(WechatUserInfo wechatUserInfo,String token){
        WeChatLoginVo weChatLoginVo = BeanUtil.toObject(WeChatLoginVo.class, wechatUserInfo);
        WechatInfo wechatInfo = wechatInfoMapper.selectOne(new QueryWrapper<WechatInfo>().eq("USER_NO", wechatUserInfo.getWxUserNo()).eq("APP_ID",appId));
        if(StringUtils.isNoneBlank(wechatUserInfo.getReceiveIdCardNoMd5())){
            UserInfo userInfo = userInfoBiz.getByIdCardNoMd5(wechatUserInfo.getReceiveIdCardNoMd5());
            Optional.ofNullable(userInfo).map(item->weChatLoginVo.setRealName(item.getReceiveNameDecrypt()));
        }
        weChatLoginVo.setGender(wechatInfo.getSex());
        weChatLoginVo.setMiniOpenId(wechatInfo.getMiniOpenId());
        weChatLoginVo.setMiniAppId(this.appId);
        //md5是避免rediskey过长
        redisClient.set(WECHAT_LOGIN_KEY_PREFIX+MD5Util.getMD5Hex(token),JSONObject.toJSONString(weChatLoginVo),AUTH_TIME);
    }

    public List<WechatUserInfo> getByIdcardMd5(String idCardMd5) {
        return wechatUserInfoMapper.selectList(new QueryWrapper<WechatUserInfo>().eq("RECEIVE_ID_CARD_NO_MD5", idCardMd5));
    }

    public String getAppQRCode(Long jobId) {
        Map<String,String> head = new HashMap<>();
        head.put("access_token",redisClient.get(WECHAT_ACCESS_TOKEN));
        Map<String,Object>  paramMap = new HashMap<>();
        paramMap.put("scene",jobId);
        paramMap.put("page","pages/task/detail");
        paramMap.put("env_version",env);
        try {
            Response respJson = OkHttpUtil.postJsonSync(UrlUtil.buildUrl(WeChatConstant.WX_APP_CODE_UNLIMIT,head), JsonUtil.toString(paramMap));
            return fastdfsClient.uploadFile(respJson.body().bytes(),"appCode.jpeg");
        }catch (Exception e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("生成二维码失败");
        }
    }


    public static void main(String[] args) {
        Date now = new Date();
        Date rangeDayEnd = DateUtil.getDayEnd(DateUtil.getLastOfMonth(now));
        Date rangeDayStart = DateUtil.getDayStart(DateUtil.getFirstOfMonth(DateUtil.addMonth(now,-5)));
        Date recentDayStart = DateUtil.addDay(now,-30);
        System.out.println(rangeDayStart);
        System.out.println(rangeDayEnd);
        System.out.println(recentDayStart);
//        List<SignRecord> signRecordList = new ArrayList<>();
//        SignRecord signRecord1 = new SignRecord();
//        signRecord1.setIdCardBackUrl("123123");
//        signRecord1.setIdCardFrontUrl("123123");
//
//        SignRecord signRecord2 = new SignRecord();
//        signRecordList.add(signRecord1);
//        signRecordList.add(signRecord2);
//
//
//        SignRecord signRecord = signRecordList.stream().filter(x->
//                StringUtils.isNotBlank(x.getIdCardCopyUrl()) ||
//                        (StringUtils.isNotBlank(x.getIdCardBackUrl()) && StringUtils.isNotBlank(x.getIdCardFrontUrl()))).findFirst().orElse(null);
//
//        System.out.println(signRecord);
    }

    public Map<String,Object> getUserIncome(String userNo) {
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("user_no", userNo));
        if (wechatUserInfo == null || StringUtils.isBlank(wechatUserInfo.getReceiveIdCardNoMd5())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户未完成实名认证");
        }
        Date now = new Date();
        //完成时间
        Date rangeDayEnd = DateUtil.getDayEnd(DateUtil.getLastOfMonth(now));
        Date rangeDayStart = DateUtil.getDayStart(DateUtil.getFirstOfMonth(DateUtil.addMonth(now,-5)));
        Date recentDayStart = DateUtil.addDay(now,-29);
        //创建时间往前2个月
        Date createDateStart = DateUtil.addMonth(rangeDayStart,-2);
        Date recentCreateDateStart = DateUtil.addMonth(recentDayStart,-2);
        Map<String,UserGrantVo> sumMap = recordItemBiz.sumGrantSuccessGroupMonth(createDateStart,rangeDayEnd,rangeDayStart,rangeDayEnd,wechatUserInfo.getReceiveIdCardNoMd5());
        List<UserGrantVo> grantList = recordItemBiz.listGrantSuccessByIdCard(createDateStart,rangeDayEnd,rangeDayStart,rangeDayEnd,wechatUserInfo.getReceiveIdCardNoMd5());
        //查询用户总发放金额
        //从统计表查出来的少两个月，需要加上实时统计表数据
        BigDecimal totalAmount = statisticsGrantFacade.getTotalAmountByIdCard(wechatUserInfo.getReceiveIdCardNoMd5());
        Date twoMonthAgo = DateUtil.getDayStart(DateUtil.getFirstOfMonth(DateUtil.addMonth(now,-2)));
        for (UserGrantVo userGrantVo : grantList) {
            UserGrantVo sum = sumMap.get(userGrantVo.getMapKey());
            if (sum == null){
                continue;
            }
            if (userGrantVo.getCreateTime().compareTo(twoMonthAgo) >= 0){
                totalAmount.add(sum.getTotalAmount());
            }
            userGrantVo.setTotalAmount(sum.getTotalAmount());
        }
        //获取最近金额
        BigDecimal recentAmount = recordItemBiz.getGrantSuccessSumAmount(recentCreateDateStart,now,recentDayStart,now,wechatUserInfo.getReceiveIdCardNoMd5());
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("recently",recentAmount);
        resultMap.put("totalAmount",totalAmount);
        resultMap.put("list",grantList);
        return resultMap;
    }

    public Object getRecordItem(String userNo, String remitPlatTrxNo) {
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("user_no", userNo));
        if (wechatUserInfo == null || StringUtils.isBlank(wechatUserInfo.getReceiveIdCardNoMd5())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户未完成实名认证");
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("remitPlatTrxNo",remitPlatTrxNo);
        paramMap.put("receiveIdCardNoMd5",wechatUserInfo.getReceiveIdCardNoMd5());
        RecordItem recordItem = recordItemBiz.getOne(paramMap);
        if (recordItem == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单不存在");
        }
        RecordItemWechatVo recordItemWechatVo = new RecordItemWechatVo();
        BeanUtil.copyProperties(recordItem,recordItemWechatVo);
        recordItemWechatVo.setReceiveAccountNo(recordItem.getReceiveAccountNoDesensitize());
        return recordItemWechatVo;
    }

    public void getReceipt(String userNo, WeChatReceiptVo weChatReceiptVo) {
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().
                eq("user_no", userNo));
        if (wechatUserInfo == null || StringUtils.isBlank(wechatUserInfo.getReceiveIdCardNoMd5())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户未完成实名认证");
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("remitPlatTrxNo",weChatReceiptVo.getRemitPlatTrxNo());
        paramMap.put("receiveIdCardNoMd5",wechatUserInfo.getReceiveIdCardNoMd5());
        RecordItem recordItem = recordItemBiz.getOne(paramMap);
        if (recordItem == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单不存在");
        }
        if (recordItem.getProcessStatus().intValue() != RecordItemStatusEnum.PAY_SUCCESS.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单状态不为成功，无法下载");
        }

        Map<String,Object> resultMap = miniAppReceiptOrderFacade.applyOrder(recordItem.getPayChannelNo(),recordItem.getRemitPlatTrxNo(),recordItem.getEmployerNo(),recordItem.getMainstayNo(),recordItem.getChannelType());
        buildResultMap(weChatReceiptVo,recordItem,resultMap);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_MINIAPP_RECEIPT_ORDER, NotifyTypeEnum.TRADE_CERTIFICATE.getValue(),
                MessageMsgDest.TAG_MINIAPP_RECEIPT_ORDER,JsonUtil.toString(resultMap));
    }

    private void buildResultMap(WeChatReceiptVo weChatReceiptVo,RecordItem recordItem, Map<String, Object> resultMap) {
        resultMap.put("email",weChatReceiptVo.getEmail());
        resultMap.put("channelNo",weChatReceiptVo.getChannelNo());
        resultMap.put("remitPlatTrxNo",recordItem.getRemitPlatTrxNo());
        resultMap.put("platTrxNo",recordItem.getPlatTrxNo());
        resultMap.put("employerNo",recordItem.getEmployerNo());
        resultMap.put("mainstayName",recordItem.getMainstayName());
        resultMap.put("receiveName",recordItem.getReceiveNameDecrypt());
    }

    @Transactional(rollbackFor = Exception.class)
    public WechatUserInfo getOrRegister(String phone, String code) {
        //先获取openid和unionid
        Code2SessionResVo response = auth(agentAppId,agentSecret,code);
        if (response == null || response.getErrCode() !=null && response.getErrCode() != WeChatConstant.SUCCESS) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取小程序登录状态失败");
        }
        //判断手机号是否存在此用户
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().lambda().eq(WechatUserInfo::getMobile,phone));
        WechatInfo wechatInfo = null;
        if (wechatUserInfo != null){
            //查询openid是否存在此用户
            wechatInfo = wechatInfoMapper.selectOne(new QueryWrapper<WechatInfo>().lambda().eq(WechatInfo::getAppId,agentAppId).eq(WechatInfo::getMiniOpenId,response.getOpenId()));
            if (wechatInfo == null){
                wechatInfo = saveAgentWechatInfo(wechatInfo,wechatUserInfo,response);
            }
        }else{
            wechatUserInfo = new WechatUserInfo();
            String userNo = sequenceFacade.nextRedisId(MINI_USER_NO_SEQ.getPrefix(), MINI_USER_NO_SEQ.getKey(), MINI_USER_NO_SEQ.getWidth());
            String wxUserNo = sequenceFacade.nextRedisId(MINI_NO_SEQ.getPrefix(), MINI_NO_SEQ.getKey(), MINI_NO_SEQ.getWidth());
            wechatUserInfo.setUserNo(userNo);
            wechatUserInfo.setWxUserNo(wxUserNo);
            wechatUserInfo.setCreateAt(new Date());
            wechatUserInfo.setLastLoginAt(new Date());
            wechatUserInfo.setUpdateAt(new Date());
            wechatUserInfo.setMobile(phone);
            wechatUserInfoMapper.insert(wechatUserInfo);
            wechatInfo = saveAgentWechatInfo(wechatInfo,wechatUserInfo,response);
        }
        wechatUserInfo.setOpenId(wechatInfo.getMiniOpenId());
        wechatUserInfo.setAppId(wechatInfo.getAppId());
        return wechatUserInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public WechatUserInfo register(WxBindMobileReqVo wxBindMobileReqVo) {

        WechatInfo wechatInfo = wechatInfoMapper.selectOne(
                new QueryWrapper<WechatInfo>().eq("MINI_OPEN_ID", wxBindMobileReqVo.getOpenId())
                        .eq("APP_ID", agentAppId)
        );
        if (wechatInfo == null) {
            String wxUserNo = sequenceFacade.nextRedisId(MINI_NO_SEQ.getPrefix(), MINI_NO_SEQ.getKey(), MINI_NO_SEQ.getWidth());
            wechatInfo = new WechatInfo();
            wechatInfo.setMiniOpenId(wxBindMobileReqVo.getOpenId());
            wechatInfo.setCreateAt(new Date());
            wechatInfo.setLastLoginAt(new Date());
            wechatInfo.setUpdateAt(new Date());
            wechatInfo.setAppId(agentAppId);
            wechatInfo.setUserNo(wxUserNo);
            wechatInfo.setNickname(wxBindMobileReqVo.getNickName());
            wechatInfo.setSex(wxBindMobileReqVo.getGender());
            wechatInfo.setAvatar(wxBindMobileReqVo.getAvatarUrl());
            wechatInfoMapper.insert(wechatInfo);
        }else {
            wechatInfo.setNickname(wxBindMobileReqVo.getNickName());
            wechatInfo.setSex(wxBindMobileReqVo.getGender());
            wechatInfo.setAvatar(wxBindMobileReqVo.getAvatarUrl());
            wechatInfoMapper.updateById(wechatInfo);
        }

        //判断手机号是否存在此用户
        WechatUserInfo wechatUserInfo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().lambda().eq(WechatUserInfo::getMobile,wxBindMobileReqVo.getMobile()));
        if (wechatUserInfo != null){
            WechatUserInfo wechatUserInfo1 = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().lambda().eq(WechatUserInfo::getWxUserNo,wechatInfo.getUserNo()));
            if (wechatUserInfo1 != null && !StrUtil.equals(wechatUserInfo1.getMobile(),wechatUserInfo.getMobile())) {
                wechatUserInfoMapper.delete(new QueryWrapper<WechatUserInfo>().lambda().eq(WechatUserInfo::getWxUserNo, wechatInfo.getUserNo()));
            }

            wechatUserInfo.setWxUserNo(wechatInfo.getUserNo());
            wechatUserInfo.setUpdateAt(new Date());
            wechatUserInfoMapper.updateById(wechatUserInfo);
        }else{
            WechatUserInfo wechatUserInfo1 = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>().lambda().eq(WechatUserInfo::getWxUserNo,wechatInfo.getUserNo()));
            if (wechatUserInfo1 == null) {
                wechatUserInfo = new WechatUserInfo();
                String userNo = sequenceFacade.nextRedisId(MINI_USER_NO_SEQ.getPrefix(), MINI_USER_NO_SEQ.getKey(), MINI_USER_NO_SEQ.getWidth());
                wechatUserInfo.setUserNo(userNo);
                wechatUserInfo.setWxUserNo(wechatInfo.getUserNo());
                wechatUserInfo.setCreateAt(new Date());
                wechatUserInfo.setLastLoginAt(new Date());
                wechatUserInfo.setUpdateAt(new Date());
                wechatUserInfo.setMobile(wxBindMobileReqVo.getMobile());
                wechatUserInfoMapper.insert(wechatUserInfo);
            }else {
                wechatUserInfo1.setMobile(wxBindMobileReqVo.getMobile());
                wechatUserInfoMapper.updateById(wechatUserInfo1);
            }

        }
        return wechatUserInfo;
    }

    private WechatInfo saveAgentWechatInfo(WechatInfo wechatInfo,WechatUserInfo wechatUserInfo,Code2SessionResVo response) {
        //插入info
        wechatInfo = response.toWechatInfo();
        wechatInfo.setAppId(agentAppId);
        wechatInfo.setUserNo(wechatUserInfo.getWxUserNo());
        wechatInfoMapper.insert(wechatInfo);
        return wechatInfo;
    }

    private WechatInfo saveAgentWechatInfo(WechatInfo wechatInfo,WechatUserInfo wechatUserInfo,WxBindMobileReqVo reqVo) {
        //插入info
        WechatInfo info = new WechatInfo();
        info.setMiniOpenId(wechatInfo.getMiniOpenId());
        info.setCreateAt(new Date());
        info.setLastLoginAt(new Date());
        info.setUpdateAt(new Date());
        wechatInfo.setAppId(agentAppId);
        wechatInfo.setUserNo(wechatUserInfo.getWxUserNo());
        wechatInfoMapper.insert(wechatInfo);
        return wechatInfo;
    }

    public String getPhoneByOpenIdAndAppId(String openId, String appId) {
        WechatInfo wechatInfo = wechatInfoMapper.selectOne(
                new QueryWrapper<WechatInfo>().eq("MINI_OPEN_ID", openId)
                        .eq("APP_ID", appId)
        );
        if (wechatInfo == null) {
            return null;
        }
        WechatUserInfo wxUserNo = wechatUserInfoMapper.selectOne(new QueryWrapper<WechatUserInfo>()
                .eq("WX_USER_NO", wechatInfo.getUserNo())
        );
        if (wxUserNo == null) {
            return null;
        }else {
            return wxUserNo.getMobile();
        }
    }

    public WechatInfo getMiniUserByOpenIdAndAppId(String openId, String appId) {
        WechatInfo wechatInfo = wechatInfoMapper.selectOne(
                new QueryWrapper<WechatInfo>().eq("MINI_OPEN_ID", openId)
                        .eq("APP_ID", appId)
        );
        return wechatInfo;
    }
}
