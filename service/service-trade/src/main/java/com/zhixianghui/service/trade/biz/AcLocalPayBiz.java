package com.zhixianghui.service.trade.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.service.dao.DaoUtil;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.joinpay.JoinPayFacade;
import com.zhixianghui.facade.banklink.service.pay.PayBankFacade;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.trade.dto.AcMerchantBalanceAddDto;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.service.trade.dao.AcChangeFundsDao;
import com.zhixianghui.service.trade.dao.AcMerchantBalanceDao;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class AcLocalPayBiz {

    @Autowired
    private AcChangeFundsBiz acChangeFundsBiz;
    @Autowired
    private AcMerchantBalanceBiz acMerchantBalanceBiz;
    @Reference(retries = -1)
    private PayBankFacade payBankFacade;
    @Autowired
    private RedisLock redisLock;
    //    @Autowired
//    private AcNotifyBiz acNotifyBiz;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;
    @Autowired
    private JXHLocalPayBiz jxhLocalPayBiz;
    @Reference
    private JoinPayFacade joinPayFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Autowired
    private AcMerchantBalanceDao acMerchantBalanceDao;
    @Autowired
    private AcChangeFundsDao acChangeFundsDao;


    public PayRespVo pay(PayReqVo reqVo) {
        String key = AcUtil.getRedisLockKey(reqVo.getEmployerNo(), reqVo.getMainstayNo(), MerchantTypeEnum.EMPLOYER.getValue(), reqVo.getChannelNo());
        log.info("[{}开始发放: {}]订单号:{},获取订单预扣锁：{}", reqVo.getChannelName(), reqVo.getEmployerNo(), reqVo.getPlatTrxNo(), key);
        RLock rLock = redisLock.tryLock(key);
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        String logKey = AcUtil.getLogKey(reqVo.getPlatTrxNo(), WxAmountChangeLogTypeEnum.FROZEN.getValue(), MerchantTypeEnum.EMPLOYER.getValue(), reqVo.getChannelNo());
        PayRespVo payRespVo = new PayRespVo();
        try {
            if (rLock == null) {
                log.error("[{}开始发放: {}]==>获取订单预扣锁失败", reqVo.getChannelName(), reqVo.getPlatTrxNo());
                throw new RuntimeException("发放超时，请稍后再试");
            }
            AcMerchantBalance param = new AcMerchantBalance();
            param.setMchNo(reqVo.getEmployerNo());
            param.setMainstayNo(reqVo.getMainstayNo());
            param.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            param.setPayChannelNo(reqVo.getChannelNo());
            AcMerchantBalance acMerchantBalance = acMerchantBalanceBiz.getOne(param);

            //获取金额
            Long balance = getAmount(reqVo);
            log.info("[{}]{}订单预扣，查询商户号[{}]余额：{}", reqVo.getPlatTrxNo(), reqVo.getChannelName(), reqVo.getEmployerNo(), balance);
            Long amount = AmountUtil.changeToFen(reqVo.getTotalAmount());
            log.info("[{}]{}订单预扣，查询商户号[{}]订单总额：{}", reqVo.getPlatTrxNo(), reqVo.getChannelName(), reqVo.getEmployerNo(), amount);
            if ((balance - amount < 0)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账户余额不足");
            }

            AcChangeFunds acChangeFunds = buildAcChangeFunds(reqVo, logKey);
            acChangeFunds.setBeforeAmount(acMerchantBalance.getTotalAmount());
            acChangeFunds.setBeforeFrozenAmount(acMerchantBalance.getFreezeAmount());
            acChangeFunds.setAfterAmount(acMerchantBalance.getTotalAmount());
            acChangeFunds.setAfterFrozenAmount(acMerchantBalance.getFreezeAmount() + amount);
            log.info("[{}]{}订单预扣,[{}]开始修改资金变动", reqVo.getPlatTrxNo(), reqVo.getChannelName(), logKey);
            acChangeFundsBiz.insert(acChangeFunds);
            log.info("[{}]{}订单预扣,[{}]资金变动结束", reqVo.getPlatTrxNo(), reqVo.getChannelName(), logKey);

            acMerchantBalance.setFreezeAmount(acChangeFunds.getAfterFrozenAmount());
            acMerchantBalanceBiz.update(acMerchantBalance);
            log.info("[{}]{}订单预扣,[商户号：{}，冻结金额:]==>冻结金额增加:{}", reqVo.getPlatTrxNo(), reqVo.getChannelName(), reqVo.getEmployerNo(), acMerchantBalance.getFreezeAmount());

            platformTransactionManager.commit(transaction);

            //exception - > rollback
            //1、请求没过去 -> 回滚 ->对的
            //2、请求过去了 -> 回滚 ->错的
        } catch (Throwable e) {
            platformTransactionManager.rollback(transaction);
            if (DaoUtil.isUniqueKeyRepeat(e, "tbl_ac_changes_funds", "idx_log_key")) {
                log.info("[{}]{}订单预扣，{}===>唯一键重复，跳过", reqVo.getPlatTrxNo(), reqVo.getChannelName(), logKey);
            } else {
                log.error("[{}]{}订单预扣，{}===>主键冲突", reqVo.getPlatTrxNo(), reqVo.getChannelName(), logKey, e);
                throw e;
            }
        } finally {
            redisLock.unlock(rLock);
        }

        try {
            log.info("[{}]-[{}]，{}===>调用发放接口", reqVo.getPlatTrxNo(), reqVo.getBankOrderNo(), reqVo.getChannelName());
            payRespVo = payBankFacade.pay(reqVo);
        } catch (BizException e) {
            log.error("[{} 请求异常: {}]==>将进入本地账户退款环节 errMsg:{}", reqVo.getChannelName(), reqVo.getPlatTrxNo(), e.getErrMsg());
            refundAmount(reqVo);
            throw e;
        }

        jxhLocalPayBiz.send(reqVo);
        return payRespVo;
    }

    public String refundAmount(String platTrxNo, String channelNo) {
        String logKey = AcUtil.getLogKey(platTrxNo, WxAmountChangeLogTypeEnum.FROZEN.getValue(), MerchantTypeEnum.EMPLOYER.getValue(), channelNo);
        AcChangeFunds frozenRecord = acChangeFundsBiz.getByLogKey(logKey);
        if (frozenRecord == null) {
            return "当前订单未有对应的账户冻结记录";
        }
        logKey = AcUtil.getLogKey(platTrxNo, WxAmountChangeLogTypeEnum.PAYMENT.getValue(), MerchantTypeEnum.EMPLOYER.getValue(), channelNo);
        AcChangeFunds payRecord = acChangeFundsBiz.getByLogKey(logKey);
        if (payRecord != null) {
            return "当前订单已执行账户余额扣款，不能进行退回操作";
        }
        logKey = AcUtil.getLogKey(platTrxNo, WxAmountChangeLogTypeEnum.REFUND.getValue(), MerchantTypeEnum.EMPLOYER.getValue(), channelNo);
        AcChangeFunds refundRecord = acChangeFundsBiz.getByLogKey(logKey);
        if (refundRecord != null) {
            return "当前订单已执行账户余额退回，不能重复操作";
        }

        refundRecord = new AcChangeFunds();
        refundRecord.setMchNo(frozenRecord.getMchNo());
        refundRecord.setMchName(frozenRecord.getMchName());
        refundRecord.setMainstayNo(frozenRecord.getMainstayNo());
        refundRecord.setMainstayName(frozenRecord.getMainstayName());
        refundRecord.setPlatTrxNo(frozenRecord.getPlatTrxNo());
        refundRecord.setMerchantType(frozenRecord.getMerchantType());
        refundRecord.setAmountChangeType(WxAmountChangeLogTypeEnum.REFUND.getValue());
        refundRecord.setAmount(0L);
        refundRecord.setFrozenAmount(-frozenRecord.getFrozenAmount());
        refundRecord.setCreateTime(new Date());
        refundRecord.setPayChannelNo(frozenRecord.getPayChannelNo());
        refundRecord.setPayChannelName(frozenRecord.getPayChannelName());
        refundRecord.setLogKey(AcUtil.getLogKey(refundRecord));
        acMerchantBalanceBiz.changeAmount(refundRecord);
        return "";
    }

    private void refundAmount(PayReqVo reqVo) {
        log.info("[{}请求异常: {}]==>开始退款", reqVo.getChannelName(), reqVo.getPlatTrxNo());
        AcChangeFunds acChangeFunds = new AcChangeFunds();
        acChangeFunds.setMchNo(reqVo.getEmployerNo());
        acChangeFunds.setMchName(reqVo.getMchName());
        acChangeFunds.setMainstayNo(reqVo.getMainstayNo());
        acChangeFunds.setMainstayName(reqVo.getRealPayerName());
        acChangeFunds.setPlatTrxNo(reqVo.getPlatTrxNo());
        acChangeFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        acChangeFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.REFUND.getValue());
        acChangeFunds.setAmount(0L);
        acChangeFunds.setFrozenAmount(-AmountUtil.changeToFen(reqVo.getTotalAmount()));
        acChangeFunds.setCreateTime(new Date());
        acChangeFunds.setPayChannelNo(reqVo.getChannelNo());
        acChangeFunds.setPayChannelName(reqVo.getChannelName());
        String logKey = AcUtil.getLogKey(acChangeFunds);
        acChangeFunds.setLogKey(logKey);
        acMerchantBalanceBiz.changeAmount(acChangeFunds);
    }


    private Long getAmount(PayReqVo reqVo) {
        AcMerchantBalance acMerchantBalance = new AcMerchantBalance();
        acMerchantBalance.setMchNo(reqVo.getEmployerNo());
        acMerchantBalance.setMainstayNo(reqVo.getMainstayNo());
        acMerchantBalance.setPayChannelNo(reqVo.getChannelNo());
        return acMerchantBalanceBiz.getAmount(acMerchantBalance);
    }


    private AcChangeFunds buildAcChangeFunds(PayReqVo reqVo, String logKey) {
        AcChangeFunds acChangeFunds = new AcChangeFunds();
        acChangeFunds.setVersion(0);
        acChangeFunds.setLogKey(logKey);
        acChangeFunds.setMchNo(reqVo.getEmployerNo());
        acChangeFunds.setMchName(reqVo.getMchName());
        acChangeFunds.setMainstayNo(reqVo.getMainstayNo());
        acChangeFunds.setMainstayName(reqVo.getRealPayerName());
        acChangeFunds.setPlatTrxNo(reqVo.getPlatTrxNo());
        acChangeFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        acChangeFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.FROZEN.getValue());
        acChangeFunds.setFrozenAmount(AmountUtil.changeToFen(reqVo.getTotalAmount()));
        acChangeFunds.setCreateTime(new Date());
        acChangeFunds.setPayChannelNo(reqVo.getChannelNo());
        acChangeFunds.setPayChannelName(reqVo.getChannelName());
        return acChangeFunds;
    }


    public void handleMainstayNoLocalAccount(RecordItem recordItem) {
        if (recordItem.getPayChannelNo().equals(ChannelNoEnum.JOINPAY.name())) {
            Map<String, Object> map = new HashMap<>();
            map.put("mainstayNo", recordItem.getMainstayNo());
            map.put("merchantType", MerchantTypeEnum.MAINSTAY.getValue());
            map.put("payChannelNo", recordItem.getPayChannelNo());
            AcMerchantBalance balance = acMerchantBalanceDao.getOne(map);
            if (ObjectUtil.isEmpty(balance)) {
                log.info("[{}]汇聚支付回调，没供应商本地账户进入创建", recordItem.getMainstayNo());
                AcMerchantBalanceAddDto dto = new AcMerchantBalanceAddDto();
                dto.setMainstayNo(recordItem.getMainstayNo());
                dto.setMainstayName(recordItem.getMainstayName());
                dto.setPayChannelNo(ChannelNoEnum.JOINPAY.name());
                dto.setPayChannelName(ChannelNoEnum.JOINPAY.getDesc());
                dto.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
                acMerchantBalanceBiz.createMerchantBalance(dto);
            }
            AcChangeFunds acChangeFunds = buildMainstayAcChangeFunds(recordItem);
            try {
                acMerchantBalanceBiz.changeAmount(acChangeFunds);
            } catch (DuplicateKeyException e) {
                log.info("[{}]订单金额已进行结算冻结，直接更新订单状态并通知商户", acChangeFunds.getPlatTrxNo());
            }

        }
    }

    private AcChangeFunds buildMainstayAcChangeFunds(RecordItem recordItem) {
        AcChangeFunds acChangeFunds = new AcChangeFunds();
        acChangeFunds.setVersion(0);
//        acChangeFunds.setMchNo(recordItem.getEmployerNo());
//        acChangeFunds.setMchName(recordItem.getEmployerName());
        acChangeFunds.setMainstayNo(recordItem.getMainstayNo());
        acChangeFunds.setMainstayName(recordItem.getMainstayName());
        acChangeFunds.setPlatTrxNo(recordItem.getPlatTrxNo());
        acChangeFunds.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        acChangeFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.SETTLE_FROZEN.getValue());
        acChangeFunds.setAmount(0L);
        acChangeFunds.setFrozenAmount(0L);
        acChangeFunds.setSettleAmount(AmountUtil.changeToFen(recordItem.getOrderFee()));
        acChangeFunds.setCreateTime(new Date());
        acChangeFunds.setPayChannelNo(recordItem.getPayChannelNo());
        acChangeFunds.setPayChannelName(recordItem.getChannelName());
        acChangeFunds.setLogKey(AcUtil.getLogKey(acChangeFunds));
        return acChangeFunds;
    }


    public void handleMainstayLocalSettle(Date date) {
        Date start = DateUtil.beginOfDay(date);
        Date end = DateUtil.endOfDay(date);

        Date now = DateUtil.offsetDay(date,1);
        Date nowStart = DateUtil.beginOfDay(now);
        Date nowEnd = DateUtil.endOfDay(now);

        Map<String, Object> param = new HashMap<>();
        param.put("createTimeBegin", start);
        param.put("createTimeEnd", end);
        param.put("amountChangeType", WxAmountChangeLogTypeEnum.SETTLE_FROZEN.getValue());
        param.put("payChannelNo", ChannelNoEnum.JOINPAY.name());
        log.info("开始结算供应商服务费:param:{}", JSONObject.toJSON(param));
        List<AcChangeFunds> acChangeFundList = acChangeFundsBiz.listBy(param);

        Map<String, Object> param2 = new HashMap<>();
        param2.put("createTimeBegin", nowStart);
        param2.put("createTimeEnd", nowEnd);
        param2.put("amountChangeType", WxAmountChangeLogTypeEnum.SETTLE_SUCCESS.getValue());
        param2.put("payChannelNo", ChannelNoEnum.JOINPAY.name());
        List<AcChangeFunds> acChangeFundList2 = acChangeFundsBiz.listBy(param2);
        acChangeFundList.addAll(acChangeFundList2);

        log.info("开始结算供应商服务费:查询结果:{}", JSONObject.toJSON(acChangeFundList));

        Map<String, Integer> countMap = new HashMap<>();

        // 统计platTrxNo出现的次数
        for (AcChangeFunds trx : acChangeFundList) {
            String platTrxNo = trx.getPlatTrxNo();
            countMap.put(platTrxNo, countMap.getOrDefault(platTrxNo, 0) + 1);
        }

        log.info("供应商服务费，统计订单结算总数量:countMap:{}", JSONObject.toJSON(countMap));

        List<AcChangeFunds> result = new ArrayList<>();
        for (AcChangeFunds trx : acChangeFundList) {
            String platTrxNo = trx.getPlatTrxNo();
            if (countMap.get(platTrxNo) < 2) {
                result.add(trx);
            }
        }

        log.info("供应商服务费，统计订单结算需要结算数量:result:{}", JSONObject.toJSON(result));

        if (CollectionUtil.isNotEmpty(result)) {
            for (AcChangeFunds item : result) {
                if (item.getAmountChangeType() == WxAmountChangeLogTypeEnum.SETTLE_FROZEN.getValue()) {
                    AcChangeFunds acChangeFunds = buildSettleAcChangeFunds(item);
                    acMerchantBalanceBiz.changeAmount(acChangeFunds);
                }
            }
        }

    }


    private AcChangeFunds buildSettleAcChangeFunds(AcChangeFunds param) {
        AcChangeFunds acChangeFunds = new AcChangeFunds();
        acChangeFunds.setVersion(0);
        acChangeFunds.setMchNo(param.getMchNo());
        acChangeFunds.setMchName(param.getMchName());
        acChangeFunds.setMainstayNo(param.getMainstayNo());
        acChangeFunds.setMainstayName(param.getMainstayName());
        acChangeFunds.setPlatTrxNo(param.getPlatTrxNo());
        acChangeFunds.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        acChangeFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.SETTLE_SUCCESS.getValue());
        acChangeFunds.setAmount(param.getSettleAmount());
        acChangeFunds.setFrozenAmount(0L);
        acChangeFunds.setSettleAmount(-param.getSettleAmount());
        acChangeFunds.setCreateTime(DateUtil.offsetDay(param.getCreateTime(),1));
        acChangeFunds.setPayChannelNo(param.getPayChannelNo());
        acChangeFunds.setPayChannelName(param.getPayChannelName());
        acChangeFunds.setLogKey(AcUtil.getLogKey(acChangeFunds));
        return acChangeFunds;
    }


    public void mainstayBalanceSync(Boolean isDeleteRecord) {
        Map<String, Object> param = new HashMap<>();
        param.put("payChannelNo", ChannelNoEnum.JOINPAY.name());
        param.put("merchantType", MerchantTypeEnum.MAINSTAY.getValue());
        List<AcMerchantBalance> acMerchantBalances = acMerchantBalanceBiz.listBy(param);
        log.info("需要同步[汇聚支付]的供应商账户：{}", JSONArray.toJSONString(acMerchantBalances));
        for (AcMerchantBalance acMerchantBalance : acMerchantBalances) {
            mainstayBalanceSyncItem(acMerchantBalance,isDeleteRecord);
        }
    }


    public void mainstayBalanceSyncOne(String mainstayNo){
        AcMerchantBalance param=new AcMerchantBalance();
        param.setPayChannelNo(ChannelNoEnum.JOINPAY.name());
        param.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        param.setMainstayNo(mainstayNo);
        AcMerchantBalance acMerchantBalances = acMerchantBalanceBiz.getOne(param);
        mainstayBalanceSyncItem(acMerchantBalances,false);
    }


    public void mainstayBalanceSyncItem(AcMerchantBalance acMerchantBalance,Boolean isDeleteRecord){
        String redisLockKey = AcUtil.getRedisLockKey(acMerchantBalance.getMchNo(), acMerchantBalance.getMainstayNo(), acMerchantBalance.getMerchantType(), acMerchantBalance.getPayChannelNo());
        RLock rLock = redisLock.tryLock(redisLockKey);
        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        if (rLock == null) {
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try {
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(
                    acMerchantBalance.getMainstayNo(), ChannelNoEnum.JOINPAY.name());
            if(mainstayChannelRelation==null||StringUtil.isEmpty(mainstayChannelRelation.getChannelMchNo())){
                log.info("[{}]供应商未设置渠道商户号，不同步本地账本",acMerchantBalance.getMainstayNo());
                return;
            }
            MainstayAmountQueryDto amountQueryDto = new MainstayAmountQueryDto();
            amountQueryDto.setChannelNo(ChannelNoEnum.JOINPAY.name());
            amountQueryDto.setChannelName(ChannelNoEnum.JOINPAY.getDesc());
            amountQueryDto.setMainstayNo(acMerchantBalance.getMainstayNo());
            amountQueryDto.setChannelMchNo(mainstayChannelRelation.getChannelMchNo());
            Map<String, String> mainstayAmount = joinPayFacade.getMainstayAmount(amountQueryDto);
            log.info("[{}]供应商同步[汇聚支付]余额：{}", acMerchantBalance.getMainstayNo(), JSONObject.toJSONString(mainstayAmount));
            AcMerchantBalance update = new AcMerchantBalance();
//            if (isDeleteRecord!=null&&isDeleteRecord) {
//                Map<String, Object> paramMap = new HashMap<>();
//                paramMap.put("payChannelNo", ChannelNoEnum.JOINPAY.name());
//                paramMap.put("merchantType", MerchantTypeEnum.MAINSTAY.getValue());
//                update.setSettleAmount(0L);
//                acChangeFundsDao.deleteBy(paramMap);
//            }
            Long settleAmount = getSettleAmount(acMerchantBalance);
            update.setId(acMerchantBalance.getId());
            update.setVersion(acMerchantBalance.getVersion() + 1);
            update.setMainstayNo(acMerchantBalance.getMainstayNo());
            update.setTotalAmount(AmountUtil.changeToFen(mainstayAmount.get("useAbleSettAmount")));
            update.setUpdateTime(new Date());
            update.setSettleAmount(settleAmount);
            acMerchantBalanceDao.updateIfNotNull(update);
            log.info("[{}]供应商同步[汇聚支付]的本地余额成功", acMerchantBalance.getMainstayNo());
            //提交事务
            platformTransactionManager.commit(transaction);
        } catch (Exception e) {
            log.error("同步汇聚支付余额异常", e);
            platformTransactionManager.rollback(transaction);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
    }

    public Long  getSettleAmount(AcMerchantBalance acMerchantBalance) {
        Date date=new Date();
        Map<String,Object> param = new HashMap<>();
        param.put("payChannelNo", acMerchantBalance.getPayChannelNo());
        param.put("merchantType", acMerchantBalance.getMerchantType());
        param.put("mainstayNo", acMerchantBalance.getMainstayNo());
        param.put("createTimeBegin", DateUtil.beginOfDay(date));
        param.put("createTimeEnd", DateUtil.endOfDay(date));
        AcChangeFunds acChangeFunds = acChangeFundsDao.groupByMainstayNo(param);
        if(ObjectUtil.isEmpty(acChangeFunds)){
            log.info("[{}]当日供应商结算金额查询，未存在交易记录，返回0",acMerchantBalance.getMainstayNo());
            return 0L;
        }
        return acChangeFunds.getSettleAmount();
    }

    /**
     * 修复错误的流水
     */
    public void repairRecord(String createTimeBegin,String createTimeEnd,String mainstayNo){
        Map<String,Object> params = new HashMap<>();
        params.put("createTimeBegin",createTimeBegin);
        params.put("createTimeEnd",createTimeEnd);
        params.put("mainstayNo",mainstayNo);
        List<AcChangeFunds> acChangeFundsList = acChangeFundsDao.repairRecord(params);
        for (AcChangeFunds item : acChangeFundsList) {
            Map<String,Object> params2 = new HashMap<>();
            params2.put("platTrxNo",item.getPlatTrxNo());
            params2.put("merchantType",item.getMerchantType());
            params2.put("mainstayNo",item.getMainstayNo());
            params2.put("amountChangeType",WxAmountChangeLogTypeEnum.FAIL_REPAIR.getValue());
            AcChangeFunds record = acChangeFundsDao.getOne(params2);
            if(record!=null){
                log.info("[{}]该记录已修复，无需重复",item.getPlatTrxNo());
                continue;
            }

            AcChangeFunds acChangeFunds = new AcChangeFunds();
            acChangeFunds.setVersion(0);
            acChangeFunds.setMchNo(item.getMchNo());
            acChangeFunds.setMchName(item.getMchName());
            acChangeFunds.setMainstayNo(item.getMainstayNo());
            acChangeFunds.setMainstayName(item.getMainstayName());
            acChangeFunds.setPlatTrxNo(item.getPlatTrxNo());
            acChangeFunds.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            acChangeFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.FAIL_REPAIR.getValue());
            acChangeFunds.setAmount(-item.getSettleAmount());
            acChangeFunds.setFrozenAmount(0L);
            acChangeFunds.setSettleAmount(0L);
            acChangeFunds.setCreateTime(new Date());
            acChangeFunds.setPayChannelNo(item.getPayChannelNo());
            acChangeFunds.setPayChannelName(item.getPayChannelName());
            acChangeFunds.setLogKey(AcUtil.getLogKey(acChangeFunds));

            try {
                acMerchantBalanceBiz.changeAmount(acChangeFunds);
            }catch (DuplicateKeyException e) {
                log.info("该记录已修复，直接跳过，商户号：[{}],logKey：[{}],通道：[{}]", acChangeFunds.getMchNo(), acChangeFunds.getLogKey(), acChangeFunds.getPayChannelNo());
            }
        }
    }


    public void refundSettle(String createTimeBegin,String createTimeEnd,String mainstayNo){
        Map<String,Object> params = new HashMap<>();
        params.put("createTimeBegin",createTimeBegin);
        params.put("createTimeEnd",createTimeEnd);
        params.put("mainstayNo",mainstayNo);
        List<AcChangeFunds> acChangeFundsList = acChangeFundsDao.refundSettle(params);
        log.info("供应商结算金额回退：{}",JSONArray.toJSONString(acChangeFundsList));
        for (AcChangeFunds item : acChangeFundsList) {
            AcChangeFunds acChangeFunds = new AcChangeFunds();
            acChangeFunds.setVersion(0);
            acChangeFunds.setMainstayNo(item.getMainstayNo());
            acChangeFunds.setMainstayName(item.getMainstayName());
            acChangeFunds.setPlatTrxNo("refund");
            acChangeFunds.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            acChangeFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.REFUND_SETTLE.getValue());
            acChangeFunds.setAmount(item.getSettleAmount());
            acChangeFunds.setFrozenAmount(0L);
            acChangeFunds.setSettleAmount(-item.getSettleAmount());
            acChangeFunds.setCreateTime(new Date());
            acChangeFunds.setPayChannelNo(ChannelNoEnum.JOINPAY.name());
            acChangeFunds.setPayChannelName(ChannelNoEnum.JOINPAY.getDesc());
            acChangeFunds.setLogKey(AcUtil.getLogKey(acChangeFunds)+"::"+item.getMainstayNo());

            try {
                acMerchantBalanceBiz.changeAmount(acChangeFunds);
            }catch (DuplicateKeyException e) {
                log.info("该记录已入账，直接跳过，商户号：[{}],logKey：[{}],通道：[{}]", acChangeFunds.getMchNo(), acChangeFunds.getLogKey(), acChangeFunds.getPayChannelNo());
            }
        }
    }
}
