package com.zhixianghui.service.trade.biz;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.SignAccountStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.sign.ChannelSignFacade;
import com.zhixianghui.facade.banklink.utils.esign.ESignHelpUtil;
import com.zhixianghui.facade.banklink.utils.esign.HeaderConstant;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.CreateTemplateResV3DataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.SignCreatePersonReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.SignCreatePersonResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.facade.banklink.vo.sign.signAuth.SignAuthReqVo;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.vo.esign.ESign;
import com.zhixianghui.facade.trade.vo.esign.ESignItem;
import com.zhixianghui.service.trade.config.RetryConfig;
import com.zhixianghui.service.trade.enums.TemplateFileStatusEnum;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.SIGN_ACCOUNT_SEQ;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月29日 16:43:00
 */
@Slf4j
public abstract class AbstractESignTemPlateBiz {

    protected static final String TEMPLATE_FILE_PATH = System.getProperty("user.dir") + File.separator + "tmp" + File.separator;
    protected static final String[] FILE_SUFFIX = new String[]{".pdf", ".doc", ".docx"};
    protected static final int RETRY = 8;
    protected static final long INITIAL_SLEEP_TIME = 3;
    protected static final long INCREMENT = 5;

    @Reference
    protected ChannelSignFacade channelSignFacade;
    @Reference
    protected MerchantFacade merchantFacade;
    @Reference
    protected MerchantEmployerMainFacade employerMainFacade;
    @Reference
    protected SequenceFacade sequenceFacade;
    @Reference
    protected NotifyFacade notifyFacade;
    @Autowired
    protected FastdfsClient fastdfsClient;
    @Value("${sign.flow.signNotifyUrl}")
    protected String signNotifyUrl;//统一入口，无需修改


    //模板方法入口
    public ESign sign(ESign eSign) {
        //校验账户
        checkAccount(eSign);
        if (StringUtil.isEmpty(eSign.getTemplateId())) {
            //获取模板文件
            getTemplateFile(eSign);
            //上传模板文件
            uploadTemplateFile(eSign);
        }
//        if (eSign.isAddComponents()) {
//            //创建组件
//            createComponent(eSign);
//            //添加组件到模板
//            addTemplate(eSign);
//        }
        //根据模板创建文件
        createFileByTemplate(eSign);
        //创建签署流程
        createFlow(eSign);
        //下载文件
        if(eSign.getESignItems().stream().allMatch(ESignItem::isAutoExecute)){
            downloadFile(eSign);
        }
        return eSign;
    }

    protected abstract void checkAccount(ESign eSign);

    protected abstract void getTemplateFile(ESign eSign);

    protected abstract void uploadTemplateFile(ESign eSign);

    protected abstract void createComponent(ESign eSign);

//    protected abstract void addTemplate(ESign eSign);

    protected abstract void createFileByTemplate(ESign eSign);

    protected abstract void createFlow(ESign eSign);

    protected abstract void downloadFile(ESign eSign);


    /**
     * 创建账户
     *
     * @param employerMain
     * @param merchant
     */
//    protected void createSignAccount(MerchantEmployerMain employerMain, Merchant merchant) {
//        String legalNo = sequenceFacade.nextRedisId(SIGN_ACCOUNT_SEQ.getPrefix(), SIGN_ACCOUNT_SEQ.getKey(), SIGN_ACCOUNT_SEQ.getWidth());
//        SignCreatePersonReqVo signCreatePersonReqVo = new SignCreatePersonReqVo(legalNo, employerMain.getLegalPersonName(), "CRED_PSN_CH_IDCARD", employerMain.getCertificateNumber());
//        EsignResVo<SignCreatePersonResDataVo> account = channelSignFacade.createPersonByThirdPartyUserId(signCreatePersonReqVo);
//        if (!Objects.equals(account.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//            log.error("[{}] 请求参数: {}, 创建法人个人账号失败", JSONObject.toJSONString(signCreatePersonReqVo), JSONObject.toJSONString(account));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创建法人个人账号失败");
//        }
//        String accountId = account.getData().getAccountId();
//        SignCreateOrganizationReqVo signCreateOrganizationReqVo = new SignCreateOrganizationReqVo(
//                employerMain.getMchNo(), accountId, merchant.getMchName(), "CRED_ORG_USCC", employerMain.getCertificateNumber()
//        );
//        EsignResVo<SignCreateOrganizationResDataVo> res3 = channelSignFacade.createOrganization(signCreateOrganizationReqVo);
//        if (!Objects.equals(res3.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//            log.error("[{}] 请求参数: {}, 创建企业账号失败", JSONObject.toJSONString(signCreateOrganizationReqVo), JSONObject.toJSONString(res3));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创建企业账号失败");
//        }
//        String orgId = res3.getData().getOrgId();
//        Merchant.JsonEntity jsonEntity = new Merchant.JsonEntity().setLegalNo(legalNo).setSignAccountId(accountId).setSignOrgId(orgId).setSignAccountStatus(SignAccountStatusEnum.ACTIVATE.getValue());
//        ;
//        merchant.setJsonEntity(jsonEntity);
//        // 允许静默签约
//        SignAuthReqVo signAuthReqVo = new SignAuthReqVo(accountId);
//        EsignResVo<Boolean> signAuthRes = channelSignFacade.signAuth(signAuthReqVo);
//        if (!Objects.equals(signAuthRes.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//            log.error("[{}] 请求参数: {}, 设置静默签约失败", JSONObject.toJSONString(signAuthReqVo), JSONObject.toJSONString(signAuthRes));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("设置静默签约失败");
//        }
//    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    protected String uploadFile(File file) {
        String localPath = file.getPath();
        byte[] buffer = FileUtils.fileToByteArray(localPath);
        // 获取请求头
        String content = ESignHelpUtil.getStringContentMD5(localPath);
        CreateSignTemplateReqVo reqVo = new CreateSignTemplateReqVo(
                content, HeaderConstant.CONTENTTYPE_STREAM.getValue(), file.getName(), !StringUtils.endsWith(file.getName(), "pdf"), file.length()
        );
        // 获取文件上传地址
        EsignResVo<CreateSignTemplateResV3DataVo> result = channelSignFacade.createSignTemplate(reqVo);
        if (!Objects.equals(result.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            log.error("请求参数: {}, e签宝获取文件上传地址失败: {}", JSONObject.toJSONString(reqVo), JSONObject.toJSONString(result));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝获取文件上传地址失败");
        }
        CreateSignTemplateResV3DataVo createSignTemplateResDataVo = result.getData();
        if (StringUtils.isBlank(createSignTemplateResDataVo.getFileUploadUrl())) {
            log.error("请求参数: {}, e签宝获取文件上传地址为空: {}", JSONObject.toJSONString(reqVo), JSONObject.toJSONString(result));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板文件预上传出错, 上传地址为空");
        }
        // 上传
        UploadFileReqVo uploadFileReqVo = new UploadFileReqVo().build(reqVo, localPath);
        boolean success = channelSignFacade.uploadFile(uploadFileReqVo, createSignTemplateResDataVo.getFileUploadUrl(), buffer);
        if (!success) {
            log.error("请求参数: {}, e签宝上传文件出错", JSONObject.toJSONString(uploadFileReqVo));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板文件上传出错");
        }
        return createSignTemplateResDataVo.getFileId();
    }

    /**
     * 判断模板是否已生成
     *
     * @param reqVo
     * @throws RetryException
     * @throws ExecutionException
     */
//    protected void existTemplate(TemplateStatus reqVo) throws RetryException, ExecutionException {
//        Retryer<Boolean> retry = new RetryConfig<Boolean>().retryConfig(RETRY, INITIAL_SLEEP_TIME, INCREMENT, TimeUnit.SECONDS);
//        //定义请求实现
//        Callable<Boolean> callable = () -> {
//            EsignResVo<TemplateStatusResDataVo> result = channelSignFacade.existTemplate(reqVo);
//            if (result.getData() == null) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板状态查询异常");
//            }
//            if (StringUtils.isNotBlank(result.getData().getTemplateFileStatus()) &&
//                    TemplateFileStatusEnum.PDF.getStatus() == Integer.parseInt(result.getData().getTemplateFileStatus())) {
//                return true;
//            }
//            log.error("模板创建未完成 : {}", JSONObject.toJSON(result.getData()));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板创建未完成");
//        };
//        retry.call(callable);
//    }

    protected EsignResVo<CreateTemplateResV3DataVo> createTemplate(List<ESignItem> eSignItems,CreateTemplateReqVo createTemplateReqVo)  {
        List<String> signerRoles = eSignItems.stream().map(ESignItem::getLabel).distinct().collect(Collectors.toList());
        createTemplateReqVo.setSignerRoles(signerRoles)
                .setDocTemplateType(0);
        EsignResVo<CreateTemplateResV3DataVo> template = channelSignFacade.createTemplate(createTemplateReqVo);
        return template;
    }

    /**
     * 获取文件连接
     *
     * @param flowId
     * @return
     * @throws ExecutionException
     * @throws RetryException
     */
    protected String fetchUrl(String flowId,String fileId) throws ExecutionException, RetryException {
        Retryer<String> retry = new RetryConfig<String>().retryConfig(RETRY, INITIAL_SLEEP_TIME, INCREMENT, TimeUnit.SECONDS);
        //定义请求实现
        Callable<String> callable = () -> {
            DocumentDownloadReqVo documentDownloadReqVo = new DocumentDownloadReqVo(flowId);
            EsignResVo<DocumentDownloadResDataVo> res = channelSignFacade.getDocumentDownloadUrl(documentDownloadReqVo);
            if (res.getData() == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝查询合同失败");
            }
            if (Objects.equals(res.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
                return res.getData().getFiles().get(0).getDownloadUrl();
            }  else {
                PreviewFileDownloadReqVo previewFileDownloadReqVo = new PreviewFileDownloadReqVo(flowId,fileId);
                EsignResVo<PreviewFileDownloadResDataVo> previewDownloadUrl = channelSignFacade.getPreviewDownloadUrl(previewFileDownloadReqVo);
                if (previewDownloadUrl.getData() == null) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(previewDownloadUrl.getMessage() + "-e签宝查询合同失败");
                }
                if(Objects.equals(previewDownloadUrl.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
                    return previewDownloadUrl.getData().getFileDownloadUrl();
                }
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝查询合同失败");
            }
        };
        return retry.call(callable);
    }


//    protected void setPos(ESignItem eSignItem, String fileId) {
//        CheckFilePosVo checkFilePosVo = new CheckFilePosVo(fileId, eSignItem.getKeywords());
//        List<KeywordPosVo> pos = channelSignFacade.getPos(checkFilePosVo);
//        if (ObjectUtils.isEmpty(pos) || !pos.get(0).isSearchResult()) {
//            log.info("[{}]未找到坐标:{}", fileId, eSignItem.getKeywords());
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未找到坐标");
//        }
//
//        KeywordPosVo keywordPosVo = pos.stream().filter(e -> e.getKeyword().equals(eSignItem.getKeywords())).findFirst().orElse(null);
//        KeywordPosVo.Positions positions = keywordPosVo.getPositions().get(0);
//        eSignItem.setPage(String.valueOf(positions.getPageNum()));
//        KeywordPosVo.Coordinates coordinates = positions.getCoordinates().get(0);
//        eSignItem.setXPos(coordinates.getPositonX() + eSignItem.getXOffset());
//        eSignItem.setYPos(coordinates.getPositonY() + eSignItem.getYPos());
//    }

}