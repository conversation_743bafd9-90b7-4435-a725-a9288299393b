package com.zhixianghui.service.trade.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.service.trade.biz.AcLocalPayBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_REFUND_SETTLE_SYNC, consumeThreadMax = 1, consumerGroup = "AcRefundSettleComsumer")
public class AcRefundSettleTaskListener extends TaskRocketMQListener<JSONObject>{
    @Reference
    private MerchantQueryFacade merchantFacade;
    @Autowired
    private AcLocalPayBiz acLocalPayBiz;


    public void runTask(JSONObject map) {
        log.info("[供应商结算回退]开始处理异常数据，{}",map.toJSONString());
        String createTimeBegin = (String)map.get("createTimeBegin");
        String createTimeEnd = (String)map.get("createTimeEnd");
        if(StringUtils.isBlank(createTimeBegin)||StringUtils.isBlank(createTimeEnd)){
            log.info("[AcRefundSettleTaskListener]未输入时间参数");
            return;
        }
        String mainstayNo = (String)map.get("mainstayNo");
        acLocalPayBiz.refundSettle(createTimeBegin,createTimeEnd,mainstayNo);
    }
}
