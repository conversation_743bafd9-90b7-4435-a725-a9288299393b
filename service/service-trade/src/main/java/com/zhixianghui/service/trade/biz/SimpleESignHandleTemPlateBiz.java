package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.constants.redis.RedisKeysManage;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.CreateByFileReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.CreateByFileResDateVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.CreateTemplateResV3DataVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.trade.vo.esign.ESign;
import com.zhixianghui.facade.trade.vo.esign.ESignItem;
import com.zhixianghui.service.trade.utils.ESignUtil;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年08月01日 14:50:00
 */
@Service
@Slf4j
public class SimpleESignHandleTemPlateBiz extends AbstractESignTemPlateBiz {

    @Autowired
    private RedisLock redisLock;

    @Override
    protected void checkAccount(ESign eSign) {
        eSign.getESignItems().forEach(e -> {
            // 获取e签宝账号
            String lockKey = redisLock.tryLockLong(RedisKeysManage.getEsignLockKey(e.getMchNo()), 3000, 2);
            try{
                if(lockKey == null){
                    log.info("[{}][{}] 创建E签宝账号，获取锁失败 ", eSign.getLogNo(),e.getMchNo());
                    throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
                }
                Merchant merchant = merchantFacade.getByMchNo(e.getMchNo());
                if (merchant == null) {
                    log.error("[{}][{}] 用工企业不存在", eSign.getLogNo(),e.getMchNo());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业不存在");
                }
                // 法人证件号码
                MerchantEmployerMain employerMain = employerMainFacade.getByMchNo(merchant.getMchNo());
                if (employerMain == null || StringUtils.isBlank(employerMain.getCertificateNumber())) {
                    log.error("[{}][{}] 用工企业法人证件号码不存在", eSign.getLogNo(), e.getMchNo());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业法人证件号码不存在");
                }
//                if (merchant.getJsonEntity() == null || StringUtils.isAnyBlank(
//                        merchant.getJsonEntity().getSignAccountId(), merchant.getJsonEntity().getSignOrgId())) {
//                    createSignAccount(employerMain, merchant);
//                    merchantFacade.update(merchant);
//                }
//                e.setSignOrgId(merchant.getJsonEntity().getSignOrgId());
//                e.setSignAccountId(merchant.getJsonEntity().getSignAccountId());
            }finally {
                redisLock.unlockLong(lockKey);
            }
        });
    }

    @Override
    protected void getTemplateFile(ESign eSign) {
        // 创建模板
        String fileExtension = FILE_SUFFIX[2];
        if (StringUtils.isNotBlank(eSign.getFileUrl())){
            fileExtension = "." + FilenameUtils.getExtension(eSign.getFileUrl());
        }
        String fileName = eSign.getFileName() + fileExtension;
        File file = null;
        try {
            if (ObjectUtils.isNotEmpty(eSign.getFileByte())) {
                file = FileUtils.byte2file(eSign.getFileByte(), TEMPLATE_FILE_PATH + fileName);
            } else {
                file = FileUtils.createFile(TEMPLATE_FILE_PATH + fileName);
                InputStream inputStream = fastdfsClient.downloadFile(eSign.getFileUrl());
                org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
            }
            eSign.setTemplateFile(file);
        } catch (IOException e) {
            log.error("[{}]下载模板出错",eSign.getLogNo(), e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("下载模板出错");
        }
    }


    @Override
    protected void uploadTemplateFile(ESign eSign) {
        log.info("[{}]开始上传模板，{}",eSign.getLogNo(),eSign);
        String fileId = uploadFile(eSign.getTemplateFile());
        log.info("[{}]文件id：{}",eSign.getLogNo(),fileId);

        //        e签宝要求不能同时调用下面的接口   设置个3秒延时
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
//            通过上传文件生成模版id
            EsignResVo<CreateTemplateResV3DataVo> template = createTemplate(   eSign.getESignItems(),new CreateTemplateReqVo()
                    .setFileId(fileId)
                    .setDocTemplateName(eSign.getTemplateFile().getName()));
            if (!Objects.equals(template.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(template.getMessage());
            }
            String templateId = template.getData().getDocTemplateId();
//            existTemplate(new TemplateStatus(templateId));
            eSign.setTemplateId(templateId);
        } finally {
            if(eSign.getTemplateFile()!=null){
                eSign.getTemplateFile().delete();
            }
        }
    }


    @Override
    protected void createComponent(ESign eSign) {
        List<com.zhixianghui.facade.trade.vo.sign.StructComponent> list = new ArrayList<>();
        eSign.getESignItems().forEach(sign -> list.add(ESignUtil.createSimpleComponent(sign)));
        eSign.setComponents(list);
    }

//    @Override
//    protected void addTemplate(ESign eSign) {
//        EsignResVo<List<String>> result = channelSignFacade.addTemplate(new TemplateComponentsReqVo(eSign.getTemplateId(), eSign.getComponents()));
//        if (!Objects.equals(result.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//            log.error("[{}]请求参数: {}-{} 上传组件失败: {}",eSign.getLogNo(), eSign.getTemplateId(), JSONObject.toJSONString(eSign.getComponents()), JSONObject.toJSONString(result));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝上传组件失败");
//        }
//    }

    @Override
    protected void createFileByTemplate(ESign eSign) {
        //组件属性
        List<CreateFileByTemplateReqVoV3.Components> simpleFormFields = new ArrayList<>();
        eSign.getSimpleFormFields().forEach((x,y)-> simpleFormFields.add(new CreateFileByTemplateReqVoV3.Components().setComponentKey(x).setComponentValue(y)));
//        HashMap<String, String> simpleFormFields = Optional.ofNullable(eSign.getSimpleFormFields()).orElse(Maps.newHashMap());
        CreateFileByTemplateReqVoV3 createFileByTemplateReqVo = new CreateFileByTemplateReqVoV3(eSign.getFileName() + FILE_SUFFIX[2], eSign.getTemplateId(), simpleFormFields);
        EsignResVo<CreateFileByTemplateResDataVoV3> createFileByTemplateResDataVoEsignResVo = channelSignFacade.createFileByTemplate(createFileByTemplateReqVo);
        if (!Objects.equals(createFileByTemplateResDataVoEsignResVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(createFileByTemplateResDataVoEsignResVo.getMessage());
        }

        String fileId = createFileByTemplateResDataVoEsignResVo.getData().getFileId();
        String fileUrl = createFileByTemplateResDataVoEsignResVo.getData().getFileDownloadUrl();
        log.info("[{}]创建模板文件,fileId：{}",eSign.getLogNo(),fileId);
        eSign.setFileId(fileId);
        eSign.setTemplateFileUrl(fileUrl);

        //定位坐标
        List<ESignItem> items = eSign.getESignItems().stream().filter(e -> StringUtils.isNoneBlank(e.getKey())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(items)) {
            EsignResVo<SignTemplateResDataVoV3> signTemplate = channelSignFacade.getSignTemplate(new SignTemplateReqVo(eSign.getTemplateId()));
            if (!Objects.equals(signTemplate.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(signTemplate.getMessage());
            }
            List<StructComponentV3> templateInfo =signTemplate.getData().getComponents();
            eSign.getESignItems().forEach(item->{
                if(StringUtils.isNoneBlank(item.getKey())&&ObjectUtils.isNotEmpty(templateInfo)){
                    StructComponentV3 structComponent = templateInfo.stream().filter(e -> e.getComponentName().equals(item.getLabel())).findFirst().orElse(null);
                    if(ObjectUtils.isNotEmpty(structComponent)){
                        item.setXPos(structComponent.getComponentPosition().getComponentPositionX());
                        item.setYPos(structComponent.getComponentPosition().getComponentPositionY());
                        item.setPage(String.valueOf(structComponent.getComponentPosition().getComponentPageNum()));
                    }
                }
            });
        }
    }

    @Override
    protected void createFlow(ESign eSign) {
        //1、设置待签署文件信息
        CreateByFileReqVo createFlowOneStepReqVo = new CreateByFileReqVo();
        createFlowOneStepReqVo.setDocs(Collections.singletonList(new CreateByFileReqVo.CreateByFileDoc()
                .setFileId(eSign.getFileId())
                .setFileName(eSign.getFileName())));
        //2、签署流程配置项
        createFlowOneStepReqVo.setSignFlowConfig(new CreateByFileReqVo.SignFlowConfig()
                .setSignFlowTitle(eSign.getFlowName())
                .setSignFlowExpireTime(eSign.getSignValidity())
                .setAuthConfig(new CreateByFileReqVo.AuthConfig().setPsnAvailableAuthModes(Arrays.asList("PSN_MOBILE3")))
                .setAutoFinish(true)
                .setNotifyUrl(signNotifyUrl));
        //3、配置签署方的信息
        ArrayList<CreateByFileReqVo.Signer> signers = new ArrayList<>();
        eSign.getESignItems().forEach(eSignItem -> {
//            if(StringUtil.isNotEmpty(eSignItem.getKeywords())){
//                setPos(eSignItem,eSign.getFileId());
//            }
            signers.add(ESignUtil.createSigner(eSignItem, eSign.getFileId()));
        });
        createFlowOneStepReqVo.setSigners(signers);

//        ArrayList<Doc> docs = new ArrayList<>();
//        docs.add(new Doc().setFileId(eSign.getFileId()));
//        //2、配置流程
//        FlowInfo flowInfo = new FlowInfo()
//                .setSignValidity(eSign.getSignValidity())
//                .setBusinessScene(eSign.getFlowName())
//                //启用自动归档
//                .setAutoArchive(true)
//                .setAutoInitiate(true)
//                .setFlowConfigInfo(new FlowConfigInfo()
//                        .setNoticeDeveloperUrl(signNotifyUrl)
//                );
//        //配置签署人并指定相应区域
//        ArrayList<Signer> signers = new ArrayList<>();
//        eSign.getESignItems().forEach(eSignItem -> {
//            if(StringUtil.isNotEmpty(eSignItem.getKeywords())){
//                setPos(eSignItem,eSign.getFileId());
//            }
//            signers.add(ESignUtil.createSigner(eSignItem, eSign.getFileId()));
//        });
//        CreateFlowOneStepReqVo createFlowOneStepReqVo = new CreateFlowOneStepReqVo(docs, flowInfo, signers);

        EsignResVo<CreateByFileResDateVo> signResult = channelSignFacade.createByFile(createFlowOneStepReqVo);
        if (!Objects.equals(signResult.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(signResult.getMessage());
        }

        eSign.setFlowId(signResult.getData().getSignFlowId());
    }

    @Override
    protected void downloadFile(ESign eSign) {
        // 查询合同链接
        String url = null;
        try {
            url = fetchUrl(eSign.getFlowId(),eSign.getFileId());
        } catch (Exception e) {
            log.error("[{}]请求参数: {} 查询签署文件失败: {}",eSign.getLogNo(), eSign.getFlowId(), e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询签署文件失败");
        }
        if (StringUtils.isBlank(url)) {
            log.error("[{}]请求参数: {}-{} 签署文件链接为空",eSign.getLogNo(), eSign.getTemplateId(), eSign.getFlowId());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签署文件链接为空");
        }
        byte[] fileArray;
        try {
            fileArray = FileUtils.download(url);
        } catch (Exception e) {
            log.error("[{}]请求参数: {}-{} 签署文件下载失败",eSign.getLogNo(), eSign.getTemplateId(), url, e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签署文件下载失败:" + url);
        }

        String returnUrl = fastdfsClient.uploadFile(fileArray, eSign.getFileName() + FILE_SUFFIX[0]);
        eSign.setReturnFileUrl(returnUrl);
    }
}