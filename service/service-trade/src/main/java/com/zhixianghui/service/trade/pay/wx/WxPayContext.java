package com.zhixianghui.service.trade.pay.wx;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.vo.WxPayQueryVo;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.service.trade.pay.wx.constant.WxBeans;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月13日 18:30:00
 */
@Slf4j
public class WxPayContext {

    private static final HashMap<String,String> map=new HashMap<>();

    private static final String WX_PREFIX="WX_";

    private static final String PACKAGE_URL ="com.zhixianghui.service.trade.pay.wx";

    static {
        map.put(WxBeans.WX_SUCCESS,PACKAGE_URL+".WxSuccessPay");
        map.put(WxBeans.WX_FAIL,PACKAGE_URL+".WxFailPay");
        map.put(WxBeans.WX_PROCESSING,PACKAGE_URL+".WxProcessingPay");
        map.put(WxBeans.WX_INIT,PACKAGE_URL+".WxProcessingPay");
    }

    private WxPayContext() {};


    public static IWxPay createBean(WxPayParam wxPayParam){
        String name =wxPayParam.getDetailStatus();
        IWxPay iWxPay=null;
        try {
            Class<?> clazz=Class.forName(map.get(WX_PREFIX + name));
            Constructor<?> constructor = clazz.getDeclaredConstructor(WxPayParam.class);
            iWxPay = (IWxPay) constructor.newInstance(wxPayParam);
        }catch (Exception e){
            log.info("【{}】微信回查异常：状态码：{},异常信息：{}", wxPayParam.getOutBatchNo(), name,e.getMessage());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("微信回查异常");
        }
        return iWxPay;
    }

    public static WxPayParam buildParam(WxPayQueryVo wxPayQueryVo, PayReqVo reqVo) throws InvocationTargetException, IllegalAccessException {
        WxPayParam wxPayParam=new WxPayParam();
        BeanUtils.copyProperties(wxPayParam,wxPayQueryVo);
        BeanUtils.copyProperties(wxPayParam,reqVo);
        return wxPayParam;
    }

    public static void handle(WxPayParam wxPayParam){
        IWxPay iWxPay = createBean(wxPayParam);
        iWxPay.handle();
    }


    public static PayRespVo getResVo(WxPayParam wxPayParam,boolean isUpdateFound) {
        IWxPay iWxPay = createBean(wxPayParam);
        return iWxPay.getResVo(isUpdateFound);
    }
}