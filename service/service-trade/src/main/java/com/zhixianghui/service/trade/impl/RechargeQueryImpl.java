package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.service.RechargeQueryFacade;
import com.zhixianghui.service.trade.biz.RechargeRecordBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RechargeQueryImpl implements RechargeQueryFacade {

    final private RechargeRecordBiz rechargeRecordBiz;

    @Override
    public PageResult<List<RechargeRecord>> list(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<RechargeRecord>> pageResult = rechargeRecordBiz.listPage(paramMap, pageParam);
        return pageResult;
    }

    @Override
    public RechargeRecord getByRechargeId(String rechargeId) {
        return rechargeRecordBiz.getByRechargeId(rechargeId);
    }

    @Override
    public RechargeRecord getById(String id) {
        return rechargeRecordBiz.getById(id);
    }

    @Override
    public void updateById(RechargeRecord rechargeRecord) {
        rechargeRecordBiz.updateById(rechargeRecord);
    }

    @Override
    public RechargeRecord getLastestRecord(String employerNo, String mainstayNo) {
        return rechargeRecordBiz.getLastestRecord(employerNo, mainstayNo);
    }

    @Override
    public Map<String, Object> countRechargeAmount(Map<String, Object> paramMap) {
        return rechargeRecordBiz.countRechargeAmount(paramMap);
    }

    @Override
    public List<RechargeRecord> listBy(Map<String, Object> paramMap) {
        return rechargeRecordBiz.getRechargeRecordList(paramMap);
    }

    @Override
    public Map<String, Object> sumRechargeRecord(Map<String, Object> paramMap) {
        return rechargeRecordBiz.sumRechargeRecord(paramMap);
    }
}
