package com.zhixianghui.service.trade.dao;

import com.google.common.collect.ImmutableMap;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 预开发票记录表DAO
 */
@Repository
public class InvoicePreRecordDao extends MyBatisDao<InvoicePreRecord, Long> {

    /**
     * 根据用工企业编号和代征主体编号查询
     */
    public InvoicePreRecord getByEmployerAndMainstay(String employerMchNo, String mainstayMchNo) {
        LimitUtil.notEmpty(employerMchNo, "用工企业编号不能为空");
        LimitUtil.notEmpty(mainstayMchNo, "代征主体编号不能为空");
        return getOne(ImmutableMap.of("employerMchNo", employerMchNo, "mainstayMchNo", mainstayMchNo));
    }

    /**
     * 统计预开票金额
     */
    public Map<String, Object> countInvoiceAmount(Map<String, Object> paramMap) {
        return getSqlSession().selectOne(fillSqlId("countInvoiceAmount"), paramMap);
    }

    /**
     * 根据条件批量更新预开票状态
     *
     * @param paramMap 条件参数，包含employerMchNo(用工企业编号)、mainstayMchNo(代征主体编号)、productNo(产品编号)、workCategoryCode(岗位类目)、updateStatus(更新状态)、updateTime(更新时间)
     * @return 更新记录数
     */
    public int batchUpdateStatus(Map<String, Object> paramMap) {
        return getSqlSession().update(fillSqlId("batchUpdateStatus"), paramMap);
    }

    /**
     * 检查是否存在指定条件的预开票记录
     *
     * @param paramMap 查询条件，包含employerMchNo(用工企业编号)、mainstayMchNo(代征主体编号)、productNo(产品编号)、workCategoryCode(岗位类目)、invoiceStatus(发票状态)
     * @return true-存在，false-不存在
     */
    public boolean existRecord(Map<String, Object> paramMap) {
        Boolean result = getSqlSession().selectOne(fillSqlId("existRecord"), paramMap);
        return result != null && result;
    }
}