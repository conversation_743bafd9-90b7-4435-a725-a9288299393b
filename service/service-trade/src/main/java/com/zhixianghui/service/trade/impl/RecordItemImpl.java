package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.bo.RecordItemGroupBo;
import com.zhixianghui.facade.trade.dto.RiskControlAmountDto;
import com.zhixianghui.facade.trade.dto.RiskControlNumDto;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.vo.ServiceConfirmVo;
import com.zhixianghui.service.trade.biz.CommonBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.factory.TradeFactory;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 打款交易流水表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RecordItemImpl implements RecordItemFacade {

    private final RecordItemBiz biz;
    private final CommonBiz commonBiz;
    private final TradeFactory tradeFactory;

    public void refundLocalFrozenAmount(String platTrxNo) {
        RecordItem recordItem = biz.getByPlatTrxNo(platTrxNo);
        if (recordItem == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("打款记录不存在");
        }
        tradeFactory.getGrantor(recordItem.getProductNo()).refundLocalFrozenAmount(platTrxNo);
    }

    @Override
    public PageResult<List<RecordItem>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return biz.listPage(paramMap,pageParam);
    }

    @Override
    public BigDecimal getSumAmount(Date beginDate, Date endDate, Date updateBeginTime, Date updateEndTime, String mainstayNo, String receiveIdCardNo) {
        return biz.getSumAmount(beginDate, endDate, updateBeginTime, updateEndTime, mainstayNo, receiveIdCardNo);
    }

    @Override
    public List<RecordItemGroupBo> getIdcardNoMainstayGroup(Date beginDate, Date endDate, Date startDateTime,Date endDateTime,int offset, int pageSize) {
        return biz.getIdcardNoMainstayGroup(beginDate, endDate,startDateTime,endDateTime, offset, pageSize);
    }

    @Override
    public String reverseQuery(String remitPlatTrxNo) {
        RecordItem recordItem = biz.getByRemitPlatTrxNo(remitPlatTrxNo);
        return tradeFactory.getGrantor(recordItem.getProductNo()).reverseQuery(recordItem);
    }

    @Override
    public Long countRecordItem(Map<String, Object> paramMap) {
        return biz.countRecordItem(paramMap);
    }

    @Override
    public RecordItem getByChannelTrxNo(String channelTrxNo) {
        return biz.getByChannelTrxNo(channelTrxNo);
    }

    @Override
    public RecordItem getByRemitPlatTrxNo(String remitPlatTrxNo) {
        return biz.getByRemitPlatTrxNo(remitPlatTrxNo);
    }

    @Override
    public void update(RecordItem recordItem) {
        biz.update(recordItem);
    }

    @Override
    public List<RecordItem> listByTime(String beginTime, String endTime, Date subTableBeginDate, Date subTableEndDate, String mchNo, PageParam pageParam) {
        return biz.listByTime(new HashMap<String, Object>() {{
            put("createBeginDate", beginTime);
            put("createEndDate", endTime);
            put("beginDate", subTableBeginDate);
            put("endDate", subTableEndDate);
            put("mchNo", mchNo);
        }}, pageParam);
    }

    @Override
    public List<RecordItem> listByTime(Date beginTime, Date endTime, String mchNo, PageParam pageParam) {
        return biz.listByTime(new HashMap<String, Object>() {
            private static final long serialVersionUID = -8954579066709384096L;

            {
            put("createBeginDate", beginTime);
            put("createEndDate", endTime);
            put("mchNo", mchNo);
        }}, pageParam);
    }

    @Override
    public Map<String, Object> coreIndexStatistics(Map<String, Object> paramMap) {
        return biz.coreIndexStatistics(paramMap);
    }

    @Override
    public List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> paramMap) {
        return biz.coreIndexDailyDetail(paramMap);
    }

    @Override
    public List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> paramMap) {
        return biz.coreIndexDetailMonthly(paramMap);
    }

    @Override
    public Map<String, Object> coreIndexReceivedUserCount(Map<String, Object> paramMap) {
        return biz.coreIndexReceivedUserCount(paramMap);
    }

    @Override
    public List<Map<String, Object>> coreIndexDailyDetailReceivedUserCount(Map<String, Object> paramMap) {
        return biz.coreIndexDailyDetailReceivedUserCount(paramMap);
    }

    @Override
    public List<Map<String, Object>> coreIndexDetailMonthlyReceivedUserCount(Map<String, Object> paramMap) {
        return biz.coreIndexDetailMonthlyReceivedUserCount(paramMap);
    }

    @Override
    public List<ServiceConfirmVo> getWorkCategoryCode(Map<String, Object> paramMap, int offset, Integer pageSize) {
        return biz.getWorkCategoryCode(paramMap,offset,pageSize);
    }

    @Override
    public List<RecordItem> listByOffset(Map<String, Object> paramMap, int offset, int maxSize) {
        return biz.listByOffset(paramMap,offset,maxSize);
    }

    @Override
    public Integer getWorkCategoryCodeCount(Map<String, Object> paramMap) {
        return biz.getWorkCategoryCodeCount(paramMap);
    }

    @Override
    public Map<String, RiskControlAmountDto> getSumAmountGroup(Date beginDate, Date endDate, Date updateBeginTime, Date updateEndTime, String receiveIdCardNo) {
        return biz.getSumAmountGroup(beginDate,endDate,updateBeginTime,updateEndTime,receiveIdCardNo);
    }

    @Override
    public RecordItem getByPlatTrxNo(String platTrxNo) {
        return biz.getByPlatTrxNo(platTrxNo);
    }

    @Override
    public Map<String, RiskControlAmountDto> getMchAmountGroup(Date beginDate, Date endDate, Date updateBeginTime, Date updateEndTime, String employerNo) {
        return biz.getMchAmountGroup(beginDate,endDate,updateBeginTime,updateEndTime,employerNo);
    }

    @Override
    public RecordItem getOne(Map<String,Object> paramMap) {
        return biz.getOne(paramMap);
    }

    @Override
    public Map<String, RiskControlNumDto> getCountTradeTimes(String platTrxNo,String employerNo, Date endTime, Date startTime, String idCardNo, String userName, String receiveAccount, BigDecimal orderAmount) {
         return biz.getCountTradeTimes(platTrxNo,employerNo,endTime,startTime,idCardNo,userName,receiveAccount,orderAmount);
    }


}
