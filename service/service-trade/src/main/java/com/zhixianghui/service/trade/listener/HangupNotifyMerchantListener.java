package com.zhixianghui.service.trade.listener;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.service.trade.helper.TradeNotifyBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_HANGUP_MERCHANT_NOTIFY, selectorExpression = MessageMsgDest.TAG_HANGUP_MERCHANT_NOTIFY ,consumeThreadMax = 1, consumerGroup = "hangupMerchantNotifyConsumer")
@Slf4j
public class HangupNotifyMerchantListener extends BaseRocketMQListener<String> {

    @Autowired
    private OrderItemBiz orderItemBiz;
    @Autowired
    private OrderBiz orderBiz;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private TradeHelperBiz tradeHelperBiz;
    @Autowired
    private TradeNotifyBiz tradeNotifyBiz;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {

        final JSONObject param = JSONObject.parseObject(jsonParam);
        final String platTrxNo = param.getString("platTrxNo");
        final String employerNo = param.getString("employerNo");
        final String msg = param.getString("msg");
        final OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
        if (orderItem.getOrderItemStatus().intValue() != OrderItemStatusEnum.GRANT_HANG.getValue()) {
            log.info("[{}-{}]订单已不是挂单状态，不再通知商户", employerNo, platTrxNo);
            return;
        }
        orderItem.setErrorDesc(msg);

        final Order order = orderBiz.getOne(MapUtil.builder(new HashMap<String, Object>()).put("platBatchNo", orderItem.getPlatBatchNo()).build());
        log.info("[{}-{}]发放挂单，开始通知商户", employerNo, platTrxNo);
        String cacheKey = TradeConstant.GRANT_HANGUP_NOTIFY_MCH_REDIS_PREFIX + orderItem.getPlatTrxNo();
        if(redisClient.get(cacheKey) == null) {
            //缓存五分钟，期间不会再通知商户
            redisClient.set(cacheKey, order.getPlatBatchNo(), 300);

            Order.JsonEntity jsonEntity = new Order.JsonEntity();
            jsonEntity.setSignType("1");
            order.setJsonEntity(jsonEntity);

            MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, orderItem);

            tradeNotifyBiz.notifyMerchant(merchantNotifyParam,order);
            log.info("[{}-{}]发放挂单，通知商户发放挂单", order.getEmployerNo(), order.getMchBatchNo());
        }
    }
}
