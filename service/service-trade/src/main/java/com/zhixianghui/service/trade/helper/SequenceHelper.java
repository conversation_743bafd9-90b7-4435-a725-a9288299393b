package com.zhixianghui.service.trade.helper;

import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.facade.common.service.SequenceFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

@Service
public class SequenceHelper {
    @Reference
    private SequenceFacade sequenceFacade;

    public String genInvoiceTrxNo() {
        return sequenceFacade.nextRedisId(
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getWidth()
        );
    }

    public String genOfflineInvoiceItemDetailPlatTrxNo() {
        return sequenceFacade.nextRedisId("I", SequenceBizKeyEnum.ORDER_ITEM_SEQ.getKey(), SequenceBizKeyEnum.ORDER_ITEM_SEQ.getWidth());
    }
}
