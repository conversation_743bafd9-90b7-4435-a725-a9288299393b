package com.zhixianghui.service.trade.listener;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年02月25日 16:58:00
 */

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.service.trade.biz.WxPayQueryBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 微信支付结果查询
 * @createTime 2021年12月13日 10:11:00
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_PAY_QUERY,
        selectorExpression = MessageMsgDest.TAG_WX_PAY_RETRY,consumeThreadMax = 20,
        consumerGroup = "wxPayQueryRetryConsume")
public class WxPayQueryRetryListener extends BaseRocketMQListener<String> {

    @Autowired
    private WxPayQueryBiz wxPayQueryBiz;

    @Override
    public void validateJsonParam(String msg) {
        if(StringUtils.isEmpty(msg)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单号不能为空");
        }
    }

    @Override
    public void consumeMessage(String msg) {
        PayReqVo payReqVo = JsonUtil.toBean(msg, PayReqVo.class);
        wxPayQueryBiz.payQuery(payReqVo);
    }
}