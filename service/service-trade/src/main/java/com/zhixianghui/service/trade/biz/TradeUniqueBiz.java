package com.zhixianghui.service.trade.biz;

import com.zhixianghui.service.trade.dao.TradeUniqueDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.trade.entity.TradeUnique;
import lombok.RequiredArgsConstructor;

import java.util.Date;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2020-11-03
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TradeUniqueBiz {

    private final TradeUniqueDao tradeUniqueDao;

    public void insertByKey(String key) {
        TradeUnique tradeUnique = new TradeUnique();
        tradeUnique.setUniqueKey(key);
        tradeUnique.setCreateTime(new Date());
        tradeUniqueDao.insert(tradeUnique);
    }
}