package com.zhixianghui.service.trade.process;

import com.zhixianghui.common.statics.enums.data.AltSourceEnum;
import com.zhixianghui.common.statics.enums.data.AltTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.facade.data.service.CkAccountDetailFacade;
import com.zhixianghui.facade.data.service.MysqlCkAccountDetailFacade;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

@Service
public class WithdrawAcctDetailBiz {
    @Reference
    private MysqlCkAccountDetailFacade ckAccountDetailFacade;
    public void save2CkAcctDetail(WithdrawRecord withdrawRecord) {
        final CkAccountDetail accountDetail = ckAccountDetailFacade.getOne(withdrawRecord.getWithdrawNo(), AltSourceEnum.WITHDRAWN.getValue());
        if (accountDetail == null && withdrawRecord.getMerchantType().intValue() == MerchantTypeEnum.EMPLOYER.getValue()) {
            CkAccountDetail detail = new CkAccountDetail();
            detail.setAccountNo(withdrawRecord.getMainstayNo());
            detail.setAccountName(withdrawRecord.getMainstayName());
            detail.setAltAmount(withdrawRecord.getAmount());
            detail.setAltDesc("商户提现");
            detail.setAltSource(AltSourceEnum.WITHDRAWN.getValue());
            detail.setAltType(AltTypeEnum.REMIT.getValue());
            detail.setCreateTime(withdrawRecord.getCreateTime());
            detail.setChannelType(withdrawRecord.getChannelType().intValue());
            detail.setPayChannelNo(withdrawRecord.getChannelNo());
            detail.setPayChannelName(ChannelNoEnum.getDesc(withdrawRecord.getChannelNo()));
            detail.setChannelTrxNo(withdrawRecord.getWithdrawNo());
            detail.setEmployerName(withdrawRecord.getEmployerName());
            detail.setEmployerNo(withdrawRecord.getEmployerNo());
            detail.setTrxNo(withdrawRecord.getWithdrawNo());
            ckAccountDetailFacade.saveOne(detail);
        }
    }
}
