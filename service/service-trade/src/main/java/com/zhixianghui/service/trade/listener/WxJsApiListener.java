package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.trade.process.WxJsapiPayBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName WxJsApiListener
 * @Description TODO
 * @Date 2022/12/28 17:50
 */
@Slf4j
@Component
public class WxJsApiListener {

    @Autowired
    private WxJsapiPayBiz wxJsapiPayBiz;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_JSAPI_REFUND,selectorExpression = MessageMsgDest.TAG_WX_JSAPI_REFUND,consumeThreadMax = 5,consumerGroup = "jsapiRefundConsumer")
    public class WxJsApiRefundListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            wxJsapiPayBiz.refundNotify(jsonParam);
        }
    }

    /**
     * 微信支付回调
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_JSAPI_CALLBACK,selectorExpression = MessageMsgDest.TAG_WX_JSAPI_CALLBACK,consumeThreadMax = 20,consumerGroup = "jsapiNotifyConsumer")
    public class WxJsApiNotifyListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            wxJsapiPayBiz.payNotify(jsonParam);
        }
    }

    /**
     * 延时关闭订单
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_JSAPI_CLOSE,selectorExpression = MessageMsgDest.TAG_WX_JSAPI_CALLBACK,consumeThreadMax = 10,consumerGroup = "jsapiCloseConsumer")
    public class WxJsapiCloseListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String orderNo) {

        }

        @Override
        public void consumeMessage(String orderNo) {
            wxJsapiPayBiz.closeHandle(orderNo);
        }
    }
}
