package com.zhixianghui.service.trade.listener;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.service.KeyPairRecordFacade;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.BankCardBinFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.biz.CommonBiz;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.utils.BuildVoUtil;
import com.zhixianghui.starter.comp.component.RedisLock;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.util.ParameterMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName CmbNotifyListener
 * @Description TODO
 * @Date 2023/2/10 11:35
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_CMB_ASYNC, selectorExpression = MessageMsgDest.TAG_TRADE_CMB_BATCH_GRANT,consumeThreadMax = 1,consumerGroup = "CmbBatchTradeConsumer")
public class CmbBatchTradeListener extends BaseRocketMQListener<String>{

    @Autowired
    private OrderBiz orderBiz;
    @Autowired
    private OrderItemBiz orderItemBiz;
    @Autowired
    private RecordItemBiz recordItemBiz;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private RedisLock redisLock;
    @Reference
    private RiskControlFacade riskControlFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private KeyPairRecordFacade keyPairRecordFacade;
    @Reference
    private BankCardBinFacade bankCardBinFacade;
    @Reference
    private CmbFacade cmbFacade;


    @Override
    public void validateJsonParam(String plateBatchNo) {
    }
    public void notifyGrantBatchCount(String platBatchNo) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_GRANT_BATCH_COUNT,platBatchNo);
    }
    @Override
    public void consumeMessage(String msgContent) {

        JSONObject msg = JSONObject.parseObject(msgContent);
        String platBatchNo = msg.getString("platBatchNo");
        int currentPage = msg.getIntValue("currentPage");
        String reqnbr = msg.getString("reqnbr");

        Order order = orderBiz.getByPlatBatchNo(platBatchNo);
        if (order == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("批次 {} 不存在", platBatchNo));
        }

        String lockKey = TradeConstant.CMB_BATCH_GRANT_LOCK + order.getEmployerNo() + ":" + platBatchNo;
        String clientId = redisLock.tryLockLong(lockKey, 30000, 30);
        if (clientId == null) {
            log.info("[招行批次发放环节: {}]==>获取批次发放锁失败", platBatchNo);
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("获取批次发放锁失败，触发重试");
        }

        try {
            notifyGrantBatchCount(platBatchNo);


            EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(order.getEmployerNo(), order.getMainstayNo(), order.getPayChannelNo());

            JSONObject queryResult = cmbFacade.queryPayBatchOrder(platBatchNo, employerAccountInfo.getParentMerchantNo());
            if (queryResult != null && queryResult.getJSONArray("bb6bthqyz1") !=null
                    && queryResult.getJSONArray("bb6bthqyz1").size() > 0) {
                JSONObject queryData = queryResult.getJSONArray("bb6bthqyz1").getJSONObject(0);
                if (!StringUtils.equals(queryData.getString("reqsta"), "OPR")) {
                    log.info("招行发放通道批次查询-招行已存在相同批次，停止发放");
                    return;
                }
            }

            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("platBatchNo", platBatchNo);
            paramMap.put("orderItemStatusList", ListUtil.of(OrderItemStatusEnum.ACCEPTED.getValue(),OrderItemStatusEnum.GRANT_HANG.getValue(),OrderItemStatusEnum.GRANTING.getValue()));
            List<OrderItem> orderItems = orderItemBiz.listByParam(paramMap);

            boolean riskControlFlag = true;
            for (OrderItem orderItem : orderItems) {

                if (orderItem.getOrderItemStatus().intValue() == OrderItemStatusEnum.GRANT_HANG.getValue()) {
                    riskControlFlag = false;
                    break;
                }else if (orderItem.getOrderItemStatus().intValue() == OrderItemStatusEnum.ACCEPTED.getValue()){
                    try {

                        String lockRiskKey = TradeConstant.RCMS_LOCK_KEY + orderItem.getReceiveIdCardNoDecrypt();
                        String riskClientId = redisLock.tryLockLong(lockRiskKey, 30000, 30);
                        if (riskClientId == null) {
                            log.info("[发放环节: {}]==>获取风控锁失败，重新放回发放队列", orderItem.getPlatTrxNo());
                            this.reNotify(order);
                            return;
                        }
                        try {
                            // 检查是否有同一个人的其它订单未创建支付记录的
                            Map<String, Object> orderItemMap = new HashMap<>();
                            orderItemMap.put("receiveIdCardNoMd5", orderItem.getReceiveIdCardNoMd5());
                            orderItemMap.put("platBatchNo", orderItem.getPlatBatchNo());
                            orderItemMap.put("orderItemStatusList", ListUtil.of(OrderItemStatusEnum.ACCEPTED.getValue(),OrderItemStatusEnum.GRANTING.getValue()));
                            List<OrderItem> items = orderItemBiz.listByParam(orderItemMap);
                            if (items != null && items.size() > 1) {
                                for (OrderItem item : items) {
                                    if (StringUtils.equals(item.getPlatTrxNo(), orderItem.getPlatTrxNo())) {
                                        continue;
                                    }
                                    if (item.getOrderItemStatus().intValue() == OrderItemStatusEnum.GRANTING.getValue()
                                    || (item.getOrderItemStatus().intValue() == OrderItemStatusEnum.ACCEPTED.getValue()
                                        &&item.getIsPassHangup() == true)
                                    ) {
                                        RecordItem recordItem = recordItemBiz.getByPlatTrxNo(item.getPlatTrxNo());
                                        if (recordItem == null) {
                                            this.reNotify(order);
                                            return;
                                        }
                                    }
                                }
                            }

                            RecordItem recordItem = recordItemBiz.getByPlatTrxNo(orderItem.getPlatTrxNo());
                            if (recordItem == null) {
                                recordItem = this.fillRecordItem(order, orderItem, employerAccountInfo);
                            }
                            log.info("[{}]-风控处理开始",orderItem.getPlatTrxNo());
                            boolean riskControl = this.processRiskControl(order, orderItem, recordItem);
                            log.info("[{}]-风控处理结束",orderItem.getPlatTrxNo());
                            if (!riskControl) {
                                riskControlFlag = false;
                                break;
                            }
                        }finally {
                            redisLock.unlockLong(riskClientId);
                        }
                    } catch (Exception e) {
                        this.reNotify(order);
                        return;
                    }
                }
            }
            if (!riskControlFlag) {
                // 招行后台发放走批量发放模式
                log.info("[{}]招行后台发放风控不通过或者挂起订单未被处理完", platBatchNo);
                return;
            }

            Map<String, Object> recordParam = new HashMap<>();
            recordParam.put("platBatchNo", platBatchNo);
            recordParam.put("employerNo", order.getEmployerNo());
            recordParam.put("processStatus", RecordItemStatusEnum.PAY_CREATED.getValue());

            final int pageSize = 1000;
            Long countRecordItem = recordItemBiz.countRecordItem(recordParam);
            long lastPage = countRecordItem % pageSize == 0 ? countRecordItem / pageSize : countRecordItem / pageSize + 1;

            Map<String, BigDecimal> grantSumAmount = recordItemBiz.getWaiteGrantSumAmount(platBatchNo,RecordItemStatusEnum.PAY_CREATED);
            if (grantSumAmount == null || grantSumAmount.get("sumNetAmount") == null) {
                return;
            }
            String orderNetAmount = grantSumAmount.get("sumNetAmount").toPlainString();

            final KeyPairRecord keyPairRecord = keyPairRecordFacade.getByChannelNoAndChannelMchNo("CMB", employerAccountInfo.getParentMerchantNo());
            Dict body = Dict.create();
            body.set("bb6busmod",
                    ListUtil.of(
                            MapUtil.sort(
                                    Dict.create()
                                            .set("buscod", "N03020") //业务类型
                                            .set("busmod", keyPairRecord.getChannelPlatNo().split(":")[1].split("-")[1]) //业务模式
                            )
                    )
            );
            PageResult<List<RecordItem>> pageResult;
            do {
                pageResult = recordItemBiz.listPage(recordParam, PageParam.newInstance(currentPage, pageSize));
                List<RecordItem> recordItems = pageResult.getData();

                List<TreeMap<String, Object>> detailList = new LinkedList();
                if (recordItems != null && !recordItems.isEmpty()) {
                    BigDecimal curRequestAmount = BigDecimal.ZERO;

                    for (RecordItem item : recordItems) {
                        curRequestAmount = curRequestAmount.add(item.getOrderNetAmount());
                        BankCardBin cardBinByCardNo = bankCardBinFacade.getCardBinByCardNo(item.getReceiveAccountNoDecrypt());
                        TreeMap<String, Object> sort = MapUtil.sort(
                                Dict.create() //收款方信息
                                        .set("trxseq", item.getRemitPlatTrxNo().substring(item.getRemitPlatTrxNo().length()-8)) //交易序号
                                        .set("accnbr", item.getReceiveAccountNoDecrypt()) //账号
                                        .set("accnam", item.getReceiveNameDecrypt()) //户名
                                        .set("trsamt", item.getOrderNetAmount().toPlainString()) //交易金额
                                        .set("trsdsp", item.getRemark()) //注释
                                        .set("cprref", item.getRemitPlatTrxNo()+"P")
                                        .set("eacbnk", cardBinByCardNo.getBankName())
                        );
                        detailList.add(sort);
                    }

                    this.buildPayerInfo(platBatchNo, employerAccountInfo, body, currentPage, lastPage, orderNetAmount, countRecordItem+"",
                            recordItems.size()+"", curRequestAmount.toPlainString(),reqnbr);
                    body.set("bb6cdcdlx1", detailList);

                    try {
                        JSONObject payApplyBatch = cmbFacade.payApplyBatch(employerAccountInfo.getParentMerchantNo(), body);
                        reqnbr = payApplyBatch.getJSONArray("bb6cdcbhz1").getJSONObject(0).getString("reqnbr");
                    } catch (BizException e) {
                        if (e.getErrMsg().contains("交易序号重复")) {
                            currentPage++;
                            continue;
                        }else {
                            throw e;
                        }
                    }catch (Exception e) {
                        log.info("[{}]招行后台发放异常，重新进入队列", platBatchNo);
                        msg.put("currentPage", currentPage);
                        msg.put("platBatchNo", platBatchNo);
                        msg.put("reqnbr", reqnbr);
                        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CMB_ASYNC, order.getEmployerNo(), order.getPlatBatchNo(),
                                NotifyTypeEnum.TRADE_CMB_BATCH_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_CMB_BATCH_GRANT, msg.toJSONString(), MsgDelayLevelEnum.S_10.getValue());

                        return;
                    }
                }
                currentPage++;
            } while (pageResult != null && pageResult.getData() != null && pageResult.getData().size() > 0);

            JSONObject checkParam = new JSONObject();
            checkParam.put("platBatchNo", platBatchNo);
            checkParam.put("reqnbr", reqnbr);
            checkParam.put("accountNo", employerAccountInfo.getParentMerchantNo());
            notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CMB_ASYNC, order.getEmployerNo(), order.getPlatBatchNo(),
                    NotifyTypeEnum.TRADE_CMB_BATCH_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_CMB_CHECK_GRANT, checkParam.toJSONString(), MsgDelayLevelEnum.M_1.getValue());
        }finally {
            redisLock.unlockLong(clientId);
        }
    }

    public void buildPayerInfo(String platBatchNo, EmployerAccountInfo employerAccountInfo, Dict body,
                               long currentPage, long lastPage, String totalAmount,String totalCount,
                               String curRequestSize,String curRequestAmount,String reqnbr) {
        //调用招行接口提交
        String begtag = "N";
        String endtag = "N";
        if (currentPage == 1) {
            begtag = "Y";
        }
        if (currentPage == lastPage) {
            endtag = "Y";
        }

        body.set("bb6cdcbhx1",
                ListUtil.of(
                        MapUtil.sort(
                                Dict.create()  //付款方信息
                                        .set("begtag", begtag)//批次开始标志
                                        .set("endtag", endtag)//批次结束标志
                                        .set("reqnbr", reqnbr)//批次结束标志
                                        .set("accnbr", employerAccountInfo.getParentMerchantNo())//账号
                                        .set("accnam", employerAccountInfo.getMainstayName())//户名
                                        .set("ttlamt", totalAmount)//总金额
                                        .set("ttlcnt", totalCount)//总笔数
                                        .set("ttlnum", lastPage)//总次数
                                        .set("curamt", curRequestAmount)//本次金额
                                        .set("curcnt", curRequestSize)//本次笔数
                                        .set("ccynbr", "10")//币种
                                        .set("trstyp", "BYBK")//交易类型
                                        .set("nusage", "下发")//用途
                                        .set("eptdat", cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN))//期望日期
                                        .set("yurref", "ZHIXIANG" + platBatchNo)//业务参考号
                                        .set("dmanbr", employerAccountInfo.getSubMerchantNo())//记账子单元
                                        .set("chlflg", "Y")//结算通道
                        )
                )
        );
    }

    private RecordItem fillRecordItem(Order order, OrderItem orderItem, EmployerAccountInfo employerAccountInfo) {
        RecordItem recordItem = new RecordItem();
        BeanUtils.copyProperties(orderItem,recordItem);
        Date now = new Date();
        recordItem.setCreateDate(now);
        recordItem.setCreateTime(now);
        recordItem.setUpdateTime(now);
        recordItem.setChannelMchNo(employerAccountInfo.getSubMerchantNo());
        recordItem.setOrderTaskAmount(orderItem.getOrderItemTaskAmount());
        recordItem.setOrderTaxAmount(orderItem.getOrderItemTaxAmount());
        recordItem.setOrderNetAmount(orderItem.getOrderItemNetAmount());
        recordItem.setOrderFee(orderItem.getOrderItemFee());
        recordItem.setOrderAmount(orderItem.getOrderItemAmount());
        recordItem.setProcessStatus(RecordItemStatusEnum.PAY_CREATED.getValue());
        recordItem.setWorkCategoryCode(order.getWorkCategoryCode());
        recordItem.setWorkCategoryName(order.getWorkCategoryName());
        String remitPlatTrxNo = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.RECORD_ITEM_SEQ.getKey(), SequenceBizKeyEnum.RECORD_ITEM_SEQ.getWidth());
        String compRemitPlatTrxNo = SequenceBizKeyEnum.RECORD_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now) + remitPlatTrxNo;
        recordItem.setRemitPlatTrxNo(compRemitPlatTrxNo);
        return recordItem;
    }

    private boolean processRiskControl(Order order, OrderItem orderItem, RecordItem recordItem) {
        if (Boolean.TRUE.equals(orderItem.getIsPassHangup())) {
            log.info("[发放环节: {}]==>人工通过风控,直接入库", orderItem.getPlatTrxNo());
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANTING.getValue());
            commonBiz.saveRecordItemAndUpdateOrderItemNew(orderItem, recordItem);
            return true;
        } else {
            //校验是否有未完成的挂单,如果有则丢入队列重试,待挂单订单处理完成之后再继续
            if (this.checkPendingOrderByMainstayAndId(orderItem)) {
                log.info("存在未处理挂单，丢入队列重试");
                this.reNotify(order);
                return false;
            }
            log.info("[发放环节: {}]==>调用风控接口", orderItem.getPlatTrxNo());
//            BigDecimal amount = userAmountMap.get(orderItem.getPlatBatchNo()).get(orderItem.getReceiveIdCardNoMd5());
            SettleRiskControlVo settleRiskControlVo = BuildVoUtil.fillSettleRiskControlVo(orderItem, recordItem.getCreateTime());
            long startTime = System.currentTimeMillis();
            log.info("[发放环节:{}-{}]==>风控处理参数：{}", orderItem.getPlatBatchNo(), orderItem.getPlatTrxNo(), JSONUtil.toJsonStr(settleRiskControlVo));
            RiskControlResult riskControlResult = riskControlFacade.processSettle(settleRiskControlVo);
            long endTime = System.currentTimeMillis();
            log.info("[发放环节：风控执行时间==>{}ms]", endTime - startTime);
            boolean isPass = riskOp(riskControlResult, orderItem, recordItem,order);
            return isPass;
        }

    }

    private void reNotify(Order order) {
        JSONObject msg = new JSONObject();
        msg.put("currentPage", 1);
        msg.put("platBatchNo", order.getPlatBatchNo());
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CMB_ASYNC, order.getEmployerNo(), order.getPlatBatchNo(),
                NotifyTypeEnum.TRADE_CMB_BATCH_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_CMB_BATCH_GRANT, msg.toJSONString(), MsgDelayLevelEnum.S_30.getValue());
    }

    private boolean riskOp(RiskControlResult riskControlResult, OrderItem orderItem, RecordItem recordItem,Order order) {

        Integer riskCode = riskControlResult.getCode();
        if (riskCode.equals(ControlAtomEnum.PENDING.getValue())) {
            log.info("[发放环节: {}]==>触发风控挂单，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(), orderItem.getPlatBatchNo(), riskControlResult.getErrMsg());
            orderItemBiz.update2Hangup(orderItem, riskControlResult);
            return false;
        } else if (riskCode.equals(ControlAtomEnum.REJECT.getValue())) {
            log.info("[发放环节: {}]==>触发风控拒绝，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(), orderItem.getPlatBatchNo(), riskControlResult.getErrMsg());
            orderItemBiz.update2Reject(orderItem, riskControlResult);
            log.info("触发风控拒绝，丢入队列重试");
            this.reNotify(order);
            return false;
        } else if (riskCode.equals(ControlAtomEnum.PASS.getValue())) {
            log.info("[发放环节: {}]==>通过风控，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(), orderItem.getPlatBatchNo(), riskControlResult.getErrMsg());
            // 通过风控 持久化数据 订单明细-(已受理)->发放中
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANTING.getValue());
            orderItem.setJsonStr(null);
            commonBiz.saveRecordItemAndUpdateOrderItemNew(orderItem, recordItem);
            return true;
        }
        return false;
    }

    private boolean checkPendingOrderByMainstayAndId(OrderItem orderItem){
        String receiveIdCardNoMd5 = orderItem.getReceiveIdCardNoMd5();
        String mainstayNo = orderItem.getMainstayNo();

        Map<String, Object> param = new ParameterMap<>();
        param.put("receiveIdCardNoMd5", receiveIdCardNoMd5);
        //param.put("mainstayNo", mainstayNo);
        param.put("createBeginDate", DateUtil.addDay(new Date(), -7));
        param.put("createEndDate", DateUtil.getDayEnd(new Date()));

        PageResult<List<RiskcontrolOrderItem>> listPageResult = riskControlFacade.listPagePendingOrder(param, PageParam.newInstance(1, 10));
        Long totalRecord = listPageResult.getTotalRecord();

        if (totalRecord==null || totalRecord.longValue()<=0L){
            return false;
        }
        else {
            log.info("风控未处理订单:{}", JSON.toJSONString(listPageResult.getData()));
            return true;
        }
    }
}
