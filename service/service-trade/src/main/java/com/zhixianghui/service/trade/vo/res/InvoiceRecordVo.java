package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName InvoiceRecordVo
 * @Description TODO
 * @Date 2022/9/29 9:58
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class InvoiceRecordVo {

    /**
     * 发票流水号
     */
    private String trxNo;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 开票方
     */
    private String drawer;

    /**
     * 发票状态
     */
    private String invoiceStatus;

    /**
     * 开票类目
     */
    private String invoiceCategory;

    /**
     * 开票金额
     */
    private String invoicedAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发票时间始
     */
    private String tradeBeginDay;

    /**
     * 发票时间止
     */
    private String tradeEndDay;

    /**
     * 下载地址
     */
    private List<String> downloadUrl;
}
