package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.service.trade.biz.FeePayBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName FeePayListener
 * @Description TODO
 * @Date 2022/7/8 9:44
 */
@Slf4j
@Component
public class FeePayListener {

    @Autowired
    private FeePayBiz feePayBiz;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CKH_FEE_ASYNC,selectorExpression = MessageMsgDest.TAG_CKH_FEE_EXCEPTION_RETRY,consumeThreadMax = 5,consumerGroup = "ckhFeeErrorConsumer")
    public class CKHFeeExceptionRetryListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String feeOrderItemNo) {
            feePayBiz.handleException(feeOrderItemNo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CKH_FEE_ASYNC,selectorExpression = MessageMsgDest.TAG_CKH_FEE_BATCH_UPDATE,consumeThreadMax = 10,consumerGroup = "ckhBatchUpdateConsumer")
    public class CKHFeeBatchUpdateListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            PayRespVo payRespVo = JsonUtil.toBean(jsonParam,PayRespVo.class);
            feePayBiz.batchUpdate(payRespVo);
        }
    }
}
