package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;
import com.zhixianghui.service.trade.biz.WxMerchantBalanceBiz;
import com.zhixianghui.facade.trade.service.WxMerchantBalanceFacade;
import com.zhixianghui.service.trade.pay.wx.biz.WxNotifyBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-12-07
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WxMerchantBalanceImpl implements WxMerchantBalanceFacade {

    private final WxMerchantBalanceBiz biz;

    private final WxNotifyBiz wxNotifyBiz;

    @Override
    public Long getAmount(WxMerchantBalance wxMerchantBalance) {
        return biz.getAmount(wxMerchantBalance);
    }

    @Override
    public WxMerchantBalance getOne(WxMerchantBalance wxMerchantBalance) {
        return biz.getOne(wxMerchantBalance);
    }

    @Override
    public void update(WxMerchantBalance wxMerchantBalance) {
        biz.update(wxMerchantBalance);
    }

    @Override
    public void adjustment(AdjustmentDTO adjustmentDTO) {
        wxNotifyBiz.adjustment(adjustmentDTO);
    }
}
