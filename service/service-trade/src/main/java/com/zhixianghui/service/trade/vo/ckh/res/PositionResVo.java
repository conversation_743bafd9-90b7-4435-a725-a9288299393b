package com.zhixianghui.service.trade.vo.ckh.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PositionResVo
 * @Description TODO
 * @Date 2022/10/24 10:15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PositionResVo extends ApiBizBaseDto {

    private List<PositionVo> positionList;
}
