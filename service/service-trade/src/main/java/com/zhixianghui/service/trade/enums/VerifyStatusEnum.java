package com.zhixianghui.service.trade.enums;

/**
 * <AUTHOR>
 * @date 2022/2/17 14:17
 */
public enum VerifyStatusEnum {
    AUTH_FREQUENTLY(300, "认证过于频繁"),
    VERIFIED(200, "已认证"),
    UN_VERIFIED(100, "未认证"),

    HAS_SIGN(1, "已签约"),
    NO_SIGN(2, "未签约"),
    NO_RECORD(0, "无记录");

    int status;
    String message;
    VerifyStatusEnum(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public int getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }
}
