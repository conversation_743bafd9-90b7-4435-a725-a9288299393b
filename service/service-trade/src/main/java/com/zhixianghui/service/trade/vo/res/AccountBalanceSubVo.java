package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-16 17:36
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
public class AccountBalanceSubVo  extends ApiBizBaseDto {
    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 是否开通银⾏卡通道
     */
    private boolean isBankCardOpen;

    /**
     * 银⾏卡余额
     */
    private String bankCardBalance;

    /**
     * 是否开通支付宝通道
     */
    private boolean isAlipayOpen;

    /**
     * 支付宝余额
     */
    private String alipayBalance;

    /**
     * 是否开通微信通道
     */
    private boolean isWxPayOpen;

    /**
     * 微信余额
     */
    private String wxPayBalance;

    public AccountBalanceSubVo(String mainstayNo, String bizErrCode, String bizErrMsg){
        this.mainstayNo = mainstayNo;
        this.bizErrCode = bizErrCode;
        this.bizErrMsg = bizErrMsg;
    }

}
