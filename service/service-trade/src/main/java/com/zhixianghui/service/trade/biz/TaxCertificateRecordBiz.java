package com.zhixianghui.service.trade.biz;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.trade.entity.TaxCertificateRecord;
import com.zhixianghui.service.trade.dao.mapper.TaxCertificateRecordMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class TaxCertificateRecordBiz extends ServiceImpl<TaxCertificateRecordMapper, TaxCertificateRecord> {

    public Page<TaxCertificateRecord> listPage(Page<TaxCertificateRecord> page, Map<String,Object> paramMap) {
        return this.baseMapper.listPage(page, paramMap);
    }
    public List<TaxCertificateRecord> list(Map<String, Object> paramMap) {
        return this.baseMapper.list(paramMap);
    }
}
