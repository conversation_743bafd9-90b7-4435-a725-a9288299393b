package com.zhixianghui.service.trade.pay.jxh.biz;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.service.pay.JXHPayFacade;
import com.zhixianghui.facade.banklink.vo.jxh.JxhResVo;
import com.zhixianghui.facade.banklink.vo.jxh.ResData;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.common.entity.config.BankInfo;
import com.zhixianghui.facade.common.entity.config.BankOrganization;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.BankInfoFacade;
import com.zhixianghui.facade.common.service.BankOrganizationFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.service.MerchantBankAccountFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.enums.WithdrawStatusEnum;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.service.trade.biz.AcMerchantBalanceBiz;
import com.zhixianghui.service.trade.biz.WithdrawRecordBiz;
import com.zhixianghui.service.trade.process.WithdrawAcctDetailBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Service
@Slf4j
public class JXHWithdrawBiz {
    @Resource
    private AcMerchantBalanceBiz acMerchantBalanceBiz;
    @Autowired
    private WithdrawRecordBiz withdrawRecordBiz;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private WithdrawAcctDetailBiz withdrawAcctDetailBiz;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;
    @Autowired
    private RedisLock redisLock;

    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private JXHPayFacade jxhPayFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private BankInfoFacade bankInfoFacade;
    @Reference
    private BankOrganizationFacade bankOrganizationFacade;
    @Reference
    private MerchantBankAccountFacade merchantBankAccountFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    public WithdrawRecord mainstayWxPayWithdraw(WithdrawDto withdrawDto, String withdrawNo, MainstayChannelRelation mainstayChannelRelation) {
        //先判断一下供应商账户余额是否足够，真正提现的时候会加锁查询兜底
        AcMerchantBalance acMerchantBalance = new AcMerchantBalance();
        acMerchantBalance.setMainstayNo(mainstayChannelRelation.getMainstayNo());
        acMerchantBalance.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        acMerchantBalance.setPayChannelNo(withdrawDto.getChannelNo());
        acMerchantBalance = acMerchantBalanceBiz.getOne(acMerchantBalance);
        if (acMerchantBalance == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体本地账户不存在，请先进行报备");
        }
        Long amount = AmountUtil.changeToFen(withdrawDto.getAmount());
        if ((acMerchantBalance.getTotalAmount() - acMerchantBalance.getFreezeAmount()) < amount) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体账户余额不足");
        }
        WithdrawRecord withdrawRecord = fillWxDrawRecord(withdrawDto, withdrawNo, mainstayChannelRelation);
        WithdrawRecord withdrawRecordNew = withdrawRecordBiz.addWithdrawRecord(withdrawRecord);
        //供应商账号可能会有较多资源占用情况，异步处理
        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_WITHDRAW, mainstayChannelRelation.getMainstayNo(), withdrawNo,
                NotifyTypeEnum.JXH_WITHDRAW.getValue(), MessageMsgDest.TAG_JXH_WITHDRAW, JsonUtil.toString(withdrawRecord));
        return withdrawRecordNew;
    }

    public WithdrawRecord employerPayWithdraw(WithdrawDto withdrawDto, String withdrawNo) {
        String employerNo = withdrawDto.getEmployerNo();
        String mainstayNo = withdrawDto.getMainstayNo();
        String channelNo = withdrawDto.getChannelNo();
        Integer merchantType = MerchantTypeEnum.EMPLOYER.getValue();
        //1.参数校验
        if (StringUtil.isEmpty(employerNo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业编号缺失，提现失败");
        } else if (StringUtil.isEmpty(mainstayNo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体编号缺失，提现失败");
        }
        Merchant merchant = merchantFacade.getByMchNo(employerNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前商户信息不存在，提现失败");
        }
        MerchantBankAccount merchantBankAccount = merchantBankAccountFacade.getByMchNo(employerNo);
        if (merchantBankAccount == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到商户银行卡信息，提现失败");
        }
        BankInfo bankInfo = bankInfoFacade.getByBankChannelNo(merchantBankAccount.getBankChannelNo());
        if (bankInfo == null || StringUtils.isBlank(bankInfo.getBankCode())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前商户所关联的银行卡联行号信息有误，请联系管理员");
        }
        BankOrganization bankOrganization = bankOrganizationFacade.getByBankCode(bankInfo.getBankCode());
        if (bankOrganization == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持提现到此银行");
        }
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(
                mainstayNo, channelNo);
        if (Objects.isNull(mainstayChannelRelation)) {
            log.warn("employerNo:{} mainstayNo:{} 代征主体与通道关系不存在", employerNo, mainstayNo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体与通道关系不存在");
        }

        //2.判断用工企业的账户余额是否足够，真正提现的时候会加锁查询兜底
        AcMerchantBalance acMerchantBalance = new AcMerchantBalance();
        acMerchantBalance.setMchNo(employerNo);
        acMerchantBalance.setMainstayNo(mainstayNo);
        acMerchantBalance.setPayChannelNo(channelNo);
        acMerchantBalance.setMerchantType(merchantType);
        acMerchantBalance = acMerchantBalanceBiz.getOne(acMerchantBalance);
        if (acMerchantBalance == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业本地账户不存在，请先进行报备");
        }
        Long amount = AmountUtil.changeToFen(withdrawDto.getAmount());
        if ((acMerchantBalance.getTotalAmount() - acMerchantBalance.getFreezeAmount()) < amount) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业账户余额不足");
        }

        //3、创建提现记录并保存入库
        WithdrawRecord withdrawRecord = new WithdrawRecord();
        withdrawRecord.setId(null);
        withdrawRecord.setVersion(0);
        withdrawRecord.setCreateTime(new Date());
        withdrawRecord.setWithdrawNo(withdrawNo);
        withdrawRecord.setEmployerNo(employerNo);
        withdrawRecord.setMainstayNo(mainstayNo);
        withdrawRecord.setMerchantType(merchantType);
        withdrawRecord.setChannelType(ChannelTypeEnum.BANK.getValue());
        withdrawRecord.setChannelNo(withdrawDto.getChannelNo());
        withdrawRecord.setAmount(new BigDecimal(withdrawDto.getAmount()));
        withdrawRecord.setRemark(withdrawDto.getRemark());
        withdrawRecord.setEmployerName(merchant.getMchName());//用工企业名称
        withdrawRecord.setMainstayName(mainstayChannelRelation.getMainstayName());//代征主体名称
        withdrawRecord.setReceiveAcctType(ChannelTypeEnum.BANK.getValue());
        withdrawRecord.setReceiveAcctNo(merchantBankAccount.getAccountNo());
        withdrawRecord.setReceiveName(merchantBankAccount.getAccountName());
        withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.NEW.getCode());
        WithdrawRecord withdrawRecordNew = withdrawRecordBiz.addWithdrawRecord(withdrawRecord);

        //用工企业的账号可能会有较多资源占用情况，异步处理
        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_WITHDRAW, mainstayNo, withdrawNo,
                NotifyTypeEnum.JXH_WITHDRAW.getValue(), MessageMsgDest.TAG_JXH_WITHDRAW, JsonUtil.toString(withdrawRecordNew));
        return withdrawRecordNew;
    }

    public void payQuery(PayReqVo msg) {
        try {
            JxhResVo jxhResVo = jxhPayFacade.querySinglePay(msg);
            log.info("提现记录通道查询结果为: {}", JsonUtil.toString(jxhResVo));
            stateHandler(jxhResVo.getData());
        } catch (Exception e) {
            log.error("[{}]{}提现查询系统异常,error:{}", msg.getPlatTrxNo(),msg.getChannelName(), e);
        }
    }

    public void withdraw(WithdrawRecord withdrawRecord, MainstayChannelRelation mainstayChannelRelation) {
        String mchNo;
        AcMerchantBalance acMerchantBalance = new AcMerchantBalance();
        if (MerchantTypeEnum.EMPLOYER.getValue() == withdrawRecord.getMerchantType()) {
            mchNo = withdrawRecord.getEmployerNo();
            acMerchantBalance.setMchNo(withdrawRecord.getEmployerNo());
        } else if (MerchantTypeEnum.MAINSTAY.getValue() == withdrawRecord.getMerchantType()) {
            mchNo = withdrawRecord.getMainstayNo();
        } else {
            withdrawError(withdrawRecord.getWithdrawNo(), "当前商户类型不支持提现操作", ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
            return;
        }

        acMerchantBalance.setMainstayNo(withdrawRecord.getMainstayNo());
        acMerchantBalance.setPayChannelNo(withdrawRecord.getChannelNo());
        acMerchantBalance.setMerchantType(withdrawRecord.getMerchantType());
        acMerchantBalance.setPayChannelName(withdrawRecord.getChannelName());
        acMerchantBalance = acMerchantBalanceBiz.getOne(acMerchantBalance);
        if (acMerchantBalance == null) {
            log.error("[{}提现]供应商编号：[{}]，提现单号：[{}]，供应商本地账户不存在，请先进行报备", withdrawRecord.getChannelNo(), withdrawRecord.getMainstayNo(),
                    withdrawRecord.getWithdrawNo());
            withdrawError(withdrawRecord.getWithdrawNo(), "供应商本地账户不存在，请先进行报备", ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
            return;
        }
        Long amount = AmountUtil.changeToFen(withdrawRecord.getAmount());
        if ((acMerchantBalance.getTotalAmount() - acMerchantBalance.getFreezeAmount()) < amount) {
            log.error("[{}提现]供应商编号：[{}]，提现单号：[{}]，账户余额不足",withdrawRecord.getChannelNo(),
                    withdrawRecord.getMainstayNo(), withdrawRecord.getWithdrawNo());
            withdrawError(withdrawRecord.getWithdrawNo(), "账户余额不足", ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
            return;
        }
        //余额扣减-冻结金额
        //只有这部分需要事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        log.info("[{}提现]提现金额扣减，提现订单号：[{}]，供应商编号：[{}]，冻结金额增加:[{}]",withdrawRecord.getChannelNo(),
                withdrawRecord.getWithdrawNo(), withdrawRecord.getMainstayNo(), amount);
        AcChangeFunds changesFunds = buildChangesFunds(withdrawRecord, WxAmountChangeLogTypeEnum.FROZEN, 0, amount);
        try {
            acMerchantBalanceBiz.changeAmount(changesFunds);
            platformTransactionManager.commit(transaction);
        } catch (DuplicateKeyException e) {
            platformTransactionManager.rollback(transaction);
        } catch (Exception e) {
            log.error("[{}提现]提现异常，提现订单号：[{}]",withdrawRecord.getChannelNo(), withdrawRecord.getWithdrawNo());
            platformTransactionManager.rollback(transaction);
        }

        String receiveAcctNo = withdrawRecord.getReceiveAcctNo();
        String receiveAcctName = withdrawRecord.getReceiveName();
        MerchantBankAccount merchantBankAccount = merchantBankAccountFacade.getByMchNo(mchNo);
        String bankChannelNo = merchantBankAccount.getBankChannelNo();
        if (MerchantTypeEnum.MAINSTAY.getValue() == withdrawRecord.getMerchantType()) {
            receiveAcctNo = merchantBankAccount.getAccountNo();
            receiveAcctName = merchantBankAccount.getAccountName();
        }

        try {
            PayReqVo payReqVo = new PayReqVo();
            payReqVo.setBankOrderNo(withdrawRecord.getWithdrawNo());
            payReqVo.setReceiveAccountNo(receiveAcctNo);
            payReqVo.setReceiveName(receiveAcctName);
            payReqVo.setBankChannelNo(bankChannelNo);
            payReqVo.setChannelMchNo(mainstayChannelRelation.getChannelMchNo());
            payReqVo.setChannelNo(acMerchantBalance.getPayChannelNo());
            payReqVo.setChannelName(acMerchantBalance.getPayChannelName());
            payReqVo.setReceiveAmount(withdrawRecord.getAmount().toString());
            jxhPayFacade.withdraw(payReqVo);
        } catch (Exception e) {
            log.error("[{}]提现异常，不确定是否已经请求到渠道，发送消息查询结果，提现订单号：[{}]", withdrawRecord.getWithdrawNo(), e);
            notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_WITHDRAW_STATUS, NotifyTypeEnum.JXH_WITHDRAW.getValue(),
                    MessageMsgDest.TAG_JXH_WITHDRAW_STATUS, JsonUtil.toString(withdrawRecord), MsgDelayLevelEnum.M_5.getValue());
            return;
        }
        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_WITHDRAW_STATUS, NotifyTypeEnum.JXH_WITHDRAW.getValue(),
                MessageMsgDest.TAG_JXH_WITHDRAW_STATUS, JsonUtil.toString(withdrawRecord), MsgDelayLevelEnum.M_5.getValue());
    }

    public void stateHandler(ResData data) {
        WithdrawRecord withdrawRecord = withdrawRecordBiz.getByWithdrawRecordNo(data.getMerchantOrderNo());
        if (WithdrawStatusEnum.SUCCESS.getCode().equals(withdrawRecord.getWithdrawStatus())
                || WithdrawStatusEnum.FAIL.getCode().equals(withdrawRecord.getWithdrawStatus())) {
            log.info("当前提现记录已经是终态，无须重复处理此订单 withdrawNo:{}", withdrawRecord.getWithdrawNo());
            return;
        }

        switch (data.getStatus()) {
            case SUCCESS:
                success(withdrawRecord);
                break;
            case FAIL:
                withdrawFail(data.getErrorDesc(), withdrawRecord);
                break;
            case PROCESSING:
            case NOT_FOND:
                retryQry(withdrawRecord);
                break;
        }
    }

    private void retryQry(WithdrawRecord withdrawRecord) {
        String withdrawNo = withdrawRecord.getWithdrawNo();
        log.info("进入提现重试的处理流程 withdrawNo：{} channelName:{}", withdrawNo, withdrawRecord.getChannelName());
        Long count = redisClient.incr(WxUtil.getRedisRetryKey(withdrawNo));
        redisClient.expire(WxUtil.getRedisRetryKey(withdrawNo), 60 * 60 * 2);
        if (count > TradeConstant.JXH_PAY_QUERY_RETRY_COUNT) {
            log.error("[{}]{}提现回查重试: 重试超过五次", withdrawNo,withdrawRecord.getChannelName());
            return;
        }

        log.info("[{}]{}提现回查重试,开始重试{}次", withdrawNo,withdrawRecord.getChannelName(), count);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_WITHDRAW_STATUS, UUIDUitl.generateString(10), withdrawNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_JXH_WITHDRAW_STATUS, JsonUtil.toString(withdrawRecord), MsgDelayLevelEnum.M_1.getValue());
    }

    private void success(WithdrawRecord withdrawRecord) {
        String withdrawNo = withdrawRecord.getWithdrawNo();
        log.info("进入提现成功的处理流程 withdrawNo：{}", withdrawNo);
        long amount = AmountUtil.changeToFen(withdrawRecord.getAmount());
        AcChangeFunds changesFunds = buildChangesFunds(withdrawRecord, WxAmountChangeLogTypeEnum.WITHDRAW, -amount, -amount);
        withdrawRecord.setUpdateTime(new Date());
        withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.SUCCESS.getCode());

        String employerNo = withdrawRecord.getEmployerNo();
        String mainstayNo = withdrawRecord.getMainstayNo();
        Integer merchantType = withdrawRecord.getMerchantType();
        String redisLockKey = AcUtil.getRedisLockKey(employerNo, mainstayNo, merchantType, withdrawRecord.getChannelNo());
        RLock rLock = redisLock.tryLock(redisLockKey);
        if (rLock == null) {
            log.error("提现成功，获取锁失败，等待消息重试，提现订单号：[{}] 提现通道：[{}]", withdrawRecord.getWithdrawNo(), withdrawRecord.getChannelName());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("并发异常");
        }

        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            withdrawRecordBiz.updateById(withdrawRecord);
            withdrawAcctDetailBiz.save2CkAcctDetail(withdrawRecord);
            acMerchantBalanceBiz.changeAmount(changesFunds);
            platformTransactionManager.commit(transaction);
        } catch (DuplicateKeyException e) {
            //已重复入账，无须处理
            platformTransactionManager.rollback(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提现处理异常:" + e);
        } finally {
            redisLock.unlock(rLock);
        }
    }

    private void withdrawFail(String reason, WithdrawRecord withdrawRecord) {
        log.info("进入提现失败的处理流程 withdrawNo：{}", withdrawRecord.getWithdrawNo());
        long amount = AmountUtil.changeToFen(withdrawRecord.getAmount());
        AcChangeFunds changesFunds = buildChangesFunds(withdrawRecord, WxAmountChangeLogTypeEnum.REFUND, 0, -amount);
        String redisLockKey = AcUtil.getRedisLockKey(changesFunds);
        RLock rLock = redisLock.tryLock(redisLockKey);
        if (rLock == null) {
            log.error("提现失败后，获取锁失败，等待消息重试，提现订单号：[{}] 提现通道：[{}]", withdrawRecord.getWithdrawNo(), withdrawRecord.getChannelName());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("并发异常");
        }

        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            //失败记录
            withdrawError(withdrawRecord.getWithdrawNo(), reason, ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
            acMerchantBalanceBiz.changeAmount(changesFunds);
            platformTransactionManager.commit(transaction);
        } catch (DuplicateKeyException e) {
            //重复变动账户，不作任何处理
            platformTransactionManager.rollback(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[{}]提现处理异常:" + e);
        } finally {
            redisLock.unlock(rLock);
        }
    }

    private void withdrawError(String withDrawNo, String msg, String code) {
        WithdrawRecord withdrawRecord = withdrawRecordBiz.getByWithdrawRecordNo(withDrawNo);
        if (withdrawRecord != null) {
            withdrawRecord.setErrorMsg(msg);
            withdrawRecord.setErrorCode(code);
            withdrawRecord.setUpdateTime(new Date());
            withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.FAIL.getCode());
            withdrawRecordBiz.updateById(withdrawRecord);
        }
    }

    private WithdrawRecord fillWxDrawRecord(WithdrawDto withdrawDto, String withdrawNo, MainstayChannelRelation mainstayChannelRelation) {
        WithdrawRecord withdrawRecord = new WithdrawRecord();
        withdrawRecord.setWithdrawNo(withdrawNo);
        withdrawRecord.setAmount(new BigDecimal(withdrawDto.getAmount()));
        withdrawRecord.setChannelNo(withdrawDto.getChannelNo());
        withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.NEW.getCode());
        withdrawRecord.setCreateTime(new Date());
        withdrawRecord.setRemark(withdrawDto.getRemark());
        withdrawRecord.setMainstayNo(withdrawDto.getMainstayNo());
        withdrawRecord.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        withdrawRecord.setMainstayName(mainstayChannelRelation.getMainstayName());
        withdrawRecord.setReceiveAcctType(ChannelTypeEnum.BANK.getValue());
        withdrawRecord.setReceiveName(mainstayChannelRelation.getMainstayName());
        withdrawRecord.setChannelType(ChannelTypeEnum.BANK.getValue());
        return withdrawRecord;
    }

    private AcChangeFunds buildChangesFunds(WithdrawRecord withdrawRecord,
                                            WxAmountChangeLogTypeEnum changeLogTypeEnum,
                                            long totalAmountChange,
                                            long frozenAmountChange) {
        AcChangeFunds changesFunds = new AcChangeFunds();
        Integer merchantType = withdrawRecord.getMerchantType();
        changesFunds.setMerchantType(merchantType)
                .setMchNo(withdrawRecord.getEmployerNo())
                .setMchName(withdrawRecord.getEmployerName())
                .setPayChannelNo(withdrawRecord.getChannelNo())
                .setPayChannelName(withdrawRecord.getChannelName())
                .setMainstayNo(withdrawRecord.getMainstayNo())
                .setMainstayName(withdrawRecord.getMainstayName())
                .setAmountChangeType(changeLogTypeEnum.getValue())
                .setPlatTrxNo(withdrawRecord.getWithdrawNo())
                .setAmount(totalAmountChange)
                .setFrozenAmount(frozenAmountChange)
                .setCreateTime(new Date());
        String logKey = AcUtil.getLogKey(changesFunds);
        changesFunds.setLogKey(logKey);
        return changesFunds;
    }
}
