package com.zhixianghui.service.trade.helper;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.facade.trade.entity.WechatUserInfo;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;


@Component
@Log4j2
public class JwtHelper {

    @Value(value = "${wx.sign.jwtPublicKey}")
    private String jwtPublicKey;
    @Value(value = "${wx.sign.jwtPrivateKey}")
    private String jwtPrivateKey;
    private RSAPublicKey publicKey = null;
    private RSAPrivateKey privateKey = null;

    private final static String USER_TOKEN_INFO_CLAIM_KEY = "USER_TOKEN_INFO_CLAIM_KEY";

    @PostConstruct
    public void init() {
        try {
            publicKey = RSAUtil.parsePublicKey(jwtPublicKey);
            privateKey = RSAUtil.parsePrivateKey(jwtPrivateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成token
     */
    public String genToken(String userNo) {
        Algorithm algorithm = Algorithm.RSA256(null, privateKey);
        JWTCreator.Builder builder = JWT.create();
        builder.withClaim(USER_TOKEN_INFO_CLAIM_KEY, userNo);
        return builder.sign(algorithm);
    }

    public String parseToken(String token) {
        DecodedJWT jwt = JWT.decode(token);
        return jwt.getClaim(USER_TOKEN_INFO_CLAIM_KEY).asString();

    }

}
