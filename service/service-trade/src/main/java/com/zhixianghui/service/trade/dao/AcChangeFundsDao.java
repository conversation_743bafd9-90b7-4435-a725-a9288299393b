package com.zhixianghui.service.trade.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.vo.FeeOrderCountVo;
import com.zhixianghui.facade.trade.vo.FeeOrderVo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;


/**
 * 账户资金变动Dao接口
 * <AUTHOR> @date 2024-04-01
 */
@Repository
public class AcChangeFundsDao extends MyBatisDao<AcChangeFunds, Long> {


    public AcChangeFunds groupByMainstayNo(Map<String,Object> param){
        return this.getSqlSession().selectOne(fillSqlId("groupByMainstayNo"), param);
    }

    public List<AcChangeFunds> repairRecord(Map<String,Object> param){
        return this.getSqlSession().selectList(fillSqlId("repairRecord"), param);
    }

    public List<AcChangeFunds> refundSettle(Map<String,Object> param){
        return this.getSqlSession().selectList(fillSqlId("refundSettle"), param);
    }
}
