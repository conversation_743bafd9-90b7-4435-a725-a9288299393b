package com.zhixianghui.service.trade.process;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.biz.OfflineOrderItemBiz;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.factory.TradeFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName RiskControlHandleBiz
 * @Description TODO
 * @Date 2022/7/4 14:54
 */
@Slf4j
@Service
public class RiskControlHandleBiz {

    @Autowired
    private OrderItemBiz orderItemBiz;

    @Autowired
    private TradeFactory tradeFactory;

    @Autowired
    private OrderBiz orderBiz;

    @Autowired
    private OfflineOrderItemBiz offlineOrderItemBiz;

    @Autowired
    private OfflineOrderGrantHandler offlineOrderGrantHandler;
    @Autowired
    private RecordItemBiz recordItemBiz;
    @Reference
    private NotifyFacade notifyFacade;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    /**
     * 挂单通过
     * @param platTrxNo 挂单的订单明细编号
     * @param loginName 操作人
     */
    public void passHangup(String platTrxNo, String loginName) {
        if (platTrxNo.startsWith("OT")) {
            OfflineOrderItem orderItem = offlineOrderItemBiz.getByPlatTrxNo(platTrxNo);

            if(orderItem == null){
                throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单明细不存在");
            }
            if(!Objects.equals(orderItem.getOrderItemStatus(), OrderItemStatusEnum.GRANT_HANG.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单状态已变更，不能再进行操作");
            }
            //更新为受理完成状态 重新进入受理
            orderItem.setOrderItemStatus(OrderItemStatusEnum.ACCEPTED.getValue());
            orderItem.setIsPassHangup(true);
            orderItem.setUpdateTime(new Date());
            orderItem.setErrorCode("");
            orderItem.setErrorDesc("");
            orderItem.setJsonStr(null);
            offlineOrderItemBiz.update(orderItem);

            // 通知发放
            offlineOrderGrantHandler.handleGrant(platTrxNo);
        }else {
            OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
            if(orderItem == null){
                throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单明细不存在");
            }
            if(!Objects.equals(orderItem.getOrderItemStatus(), OrderItemStatusEnum.GRANT_HANG.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单状态已变更，不能再进行操作");
            }
            //更新为受理完成状态 重新进入受理
            orderItem.setOrderItemStatus(OrderItemStatusEnum.ACCEPTED.getValue());
            orderItem.setIsPassHangup(true);
            orderItem.setHangupApprovalLoginName(loginName);
            orderItem.setUpdateTime(new Date());
            orderItem.setErrorCode("");
            orderItem.setErrorDesc("");
            orderItem.setJsonStr(null);
            orderItemBiz.update(orderItem);

//            if (orderItem.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue()) {
//                if (org.apache.commons.lang3.StringUtils.equals(orderItem.getPayChannelNo(), ChannelNoEnum.CMB.name())) {
////                    Order order = orderBiz.getByPlatBatchNo(orderItem.getPlatBatchNo());
////                    EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(order.getEmployerNo(), order.getMainstayNo(), order.getPayChannelNo());
////                    RecordItem recordItem = this.fillRecordItem(order, orderItem, employerAccountInfo);
////                    recordItemBiz.insert(recordItem);
//                    JSONObject msg = new JSONObject();
//                    msg.put("currentPage", 1);
//                    msg.put("platBatchNo", orderItem.getPlatBatchNo());
//                    notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CMB_ASYNC, orderItem.getEmployerNo(), orderItem.getPlatBatchNo(),
//                            NotifyTypeEnum.TRADE_CMB_BATCH_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_CMB_BATCH_GRANT, msg.toJSONString());
//                    return;
//                }
//            }
            // 通知发放
            tradeFactory.getGrantor(orderItem.getProductNo()).notifyGrantStart(orderItem.getEmployerNo(),orderItem.getMainstayNo(), orderItem.getPlatBatchNo(),
                    Lists.newArrayList(orderItem.getPlatTrxNo()));
        }

    }

    @Reference
    private SequenceFacade sequenceFacade;
    private RecordItem fillRecordItem(Order order, OrderItem orderItem, EmployerAccountInfo employerAccountInfo) {
        RecordItem recordItem = new RecordItem();
        BeanUtils.copyProperties(orderItem,recordItem);
        Date now = new Date();
        recordItem.setCreateDate(DateUtil.getDayStart(now));
        recordItem.setCreateTime(now);
        recordItem.setUpdateTime(now);
        recordItem.setChannelMchNo(employerAccountInfo.getSubMerchantNo());
        recordItem.setOrderTaskAmount(orderItem.getOrderItemTaskAmount());
        recordItem.setOrderTaxAmount(orderItem.getOrderItemTaxAmount());
        recordItem.setOrderNetAmount(orderItem.getOrderItemNetAmount());
        recordItem.setOrderFee(orderItem.getOrderItemFee());
        recordItem.setOrderAmount(orderItem.getOrderItemAmount());
        recordItem.setProcessStatus(RecordItemStatusEnum.PAY_CREATED.getValue());
        recordItem.setWorkCategoryCode(order.getWorkCategoryCode());
        recordItem.setWorkCategoryName(order.getWorkCategoryName());
        String remitPlatTrxNo = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.RECORD_ITEM_SEQ.getKey(), SequenceBizKeyEnum.RECORD_ITEM_SEQ.getWidth());
        String compRemitPlatTrxNo = SequenceBizKeyEnum.RECORD_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now) + remitPlatTrxNo;
        recordItem.setRemitPlatTrxNo(compRemitPlatTrxNo);
        return recordItem;
    }

    /**
     * 挂单拒绝
     * @param platTrxNo 挂单的订单明细编号
     * @param loginName 操作人
     */
    public void rejectHangup(String platTrxNo, String loginName) {
        if (platTrxNo.startsWith("OT")) {
            OfflineOrderItem orderItem = offlineOrderItemBiz.getByPlatTrxNo(platTrxNo);
            if(orderItem == null){
                throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单明细不存在");
            }
            if(!Objects.equals(orderItem.getOrderItemStatus(),OrderItemStatusEnum.GRANT_HANG.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单状态已变更，不能再进行操作");
            }
            //更新为受理失败状态
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
            orderItem.setIsPassHangup(false);
            orderItem.setUpdateTime(new Date());
            orderItem.setCompleteTime(new Date());
            orderItem.setErrorCode(ApiExceptions.API_TRADE_RISK_REJECT.getApiErrorCode());
            offlineOrderItemBiz.update(orderItem);

            // 更新统计
            offlineOrderGrantHandler.updateGrantBatchCount(orderItem.getPlatBatchNo());
        }else {
            OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
            if(orderItem == null){
                throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单明细不存在");
            }
            if(!Objects.equals(orderItem.getOrderItemStatus(),OrderItemStatusEnum.GRANT_HANG.getValue())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单状态已变更，不能再进行操作");
            }
            //更新为受理失败状态
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
            orderItem.setIsPassHangup(false);
            orderItem.setHangupApprovalLoginName(loginName);
            orderItem.setUpdateTime(new Date());
            orderItem.setCompleteTime(new Date());
            orderItem.setErrorCode(ApiExceptions.API_TRADE_RISK_REJECT.getApiErrorCode());
            orderItemBiz.update(orderItem);
            if (StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.CMB.name())&& orderItem.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue()) {
                // 通知发放
                tradeFactory.getGrantor(orderItem.getProductNo()).notifyGrantStart(orderItem.getEmployerNo(),orderItem.getMainstayNo(), orderItem.getPlatBatchNo(),
                        Lists.newArrayList(orderItem.getPlatTrxNo()));
            }
            // 更新统计
            tradeFactory.getGrantor(orderItem.getProductNo()).notifyGrantBatchCount(orderItem.getPlatBatchNo());
        }


    }

    @Transactional(rollbackFor = Exception.class)
    public void riskControlReasonUpdate(String platTrxNo, String reason,Integer code,List<String> typeList) {
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
        if(orderItem == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单明细不存在");
        }
        if(!Objects.equals(orderItem.getOrderItemStatus(),OrderItemStatusEnum.GRANT_HANG.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单状态已变更，不能再进行操作");
        }
        orderItem.setUpdateTime(new Date());
        orderItem.setErrorCode(String.valueOf(ApiExceptions.API_TRADE_RISK_HANG.getApiErrorCode()));
        orderItem.setErrorDesc(reason);
        if (!CollectionUtils.isEmpty(typeList)){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("risk_type",typeList);
            orderItem.setJsonStr(jsonObject.toJSONString());
        }

        if (code != null && code == ControlAtomEnum.REJECT.getValue()){
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
            orderItem.setErrorCode(String.valueOf(ApiExceptions.API_TRADE_RISK_REJECT.getApiErrorCode()));
            orderItem.setCompleteTime(new Date());
            orderItem.setIsPassHangup(false);
        }
        orderItemBiz.update(orderItem);
        // 更新统计
        if (code != null && code == ControlAtomEnum.REJECT.getValue()){
            tradeFactory.getGrantor(orderItem.getProductNo()).notifyGrantBatchCount(orderItem.getPlatBatchNo());
        }
    }
}
