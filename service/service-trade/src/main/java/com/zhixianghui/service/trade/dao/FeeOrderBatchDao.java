package com.zhixianghui.service.trade.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhixianghui.facade.trade.vo.FeeOrderCountVo;
import com.zhixianghui.facade.trade.vo.FeeOrderSumVo;
import com.zhixianghui.facade.trade.vo.FeeOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-05
 */
@Repository
public class FeeOrderBatchDao extends MyBatisDao<FeeOrderBatch,Long> {


    public List<FeeOrderCountVo> selectCounts(FeeOrderVo feeOrderVo){
        return this.getSqlSession().selectList(fillSqlId("selectCounts"), BeanUtil.toMap(feeOrderVo));
    }

    public FeeOrderSumVo selectSums(FeeOrderVo feeOrderVo){
        return this.getSqlSession().selectOne(fillSqlId("selectSums"), BeanUtil.toMap(feeOrderVo));
    }

    public BigDecimal selectFailSum(FeeOrderVo feeOrderVo){
        return this.getSqlSession().selectOne(fillSqlId("selectFailSum"), BeanUtil.toMap(feeOrderVo));
    }

    public FeeOrderBatch selectSingleBatch(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("selectSingleBatch"), paramMap);
    }
}
