package com.zhixianghui.service.trade.process;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.common.OrderCompleteVo;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.service.pay.CKHPayFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName CKHGrantBiz
 * @Description TODO
 * @Date 2022/6/30 10:06
 */
@Slf4j
@Service
public class CKHGrantBiz extends AbstractGrantHandler{

    @Reference
    private CKHPayFacade ckhPayFacade;

    @Override
    public void notifyGrantStart(String employerNo, String mainstayNo, String platBatchNo, List<String> notifyList) {
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(notifyList));
        String yishuiNo = dataDictionaryFacade.getSystemConfig("yishui");
        if (StringUtils.equals(yishuiNo,mainstayNo)) {
            /**
             * 易税供应商由于供应商处在下单之后不能立即根据外部单号查询到订单明细，所以适当拉大延迟时间
             */
            notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, UUIDUitl.generateString(10),platBatchNo,
                    NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT_CKH,JSON.toJSONString(infoMap),
                    MsgDelayLevelEnum.S_10.getValue());
        }else {
            notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, UUIDUitl.generateString(10),platBatchNo,
                    NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT_CKH,JSON.toJSONString(infoMap),
                    MsgDelayLevelEnum.S_1.getValue());
        }
    }

    @Override
    public void notifyGrantStartRisckRetry(String employerNo, String mainstayNo, String platBatchNo, List<String> notifyList) {
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(notifyList));
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT_CKH,JSON.toJSONString(infoMap),
                MsgDelayLevelEnum.M_1.getValue());
    }

    @Override
    public void notifyHandleGrantException(String platTrxNo) {
        //非业务异常延迟队列(避开类似服务暂时异常)
        log.info("[{}]==>发送发放异常处理mq", platTrxNo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC_CKH,UUIDUitl.generateString(10),platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),MessageMsgDest.TAG_GRANT_NOT_BIZ_EXCEPTION_CKH,platTrxNo, MsgDelayLevelEnum.M_1.getValue());
    }

    @Override
    public void notifyGrantBatchCount(String platBatchNo) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_GRANT_BATCH_COUNT_CKH,platBatchNo);
    }

    @Override
    public PayRespVo pay(PayReqVo payReqVo){
        return ckhPayFacade.pay(payReqVo);
    }

    @Override
    public void notifyFailRefund(RecordItem recordItem) {
        //无须退款
    }

    @Override
    public OrderCompleteVo buildNotifyFeeVo(OrderItem orderItem, RecordItem recordItem) {
        return buildNotifyFeeVo(orderItem,recordItem,orderItem.getOrderItemTaskAmount());
    }

    /**
     * '打款流水反查
     * @param recordItem
     */
    @Override
    public PayRespVo queryChannelOrder(RecordItem recordItem, EmployerAccountInfo employerAccountInfo) {
        PayRespVo queryRespVo;
        try {
            queryRespVo = ckhPayFacade.queryPayOrder(recordItem.getRemitPlatTrxNo());
        } catch (Exception e) {
            log.error("[人工反查通道: {}]==>通道反查接口异常", recordItem.getRemitPlatTrxNo(), e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道反查接口异常,请稍后重试");
        }
        log.info("[人工反查通道: {}]==> 通道返回结果:{}", recordItem.getRemitPlatTrxNo(), JsonUtil.toString(queryRespVo));
        return queryRespVo;
    }

    @Override
    public void reverseQueryFailHandle(RecordItem recordItem) {
        return ;
    }

    @Override
    public boolean refundLocalFrozenAmount(RecordItem recordItem) {
        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前产品暂不支持此功能");
    }
}
