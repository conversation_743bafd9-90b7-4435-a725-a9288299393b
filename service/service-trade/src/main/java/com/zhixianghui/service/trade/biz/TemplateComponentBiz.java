package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.service.trade.dao.TemplateComponentDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.trade.entity.TemplateComponent;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;

/**
* 模板组件表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-06-15
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TemplateComponentBiz {

    private final TemplateComponentDao templatecomponentDao;

    public List<TemplateComponent> componentList() {
        return templatecomponentDao.listAll();
    }
}