package com.zhixianghui.service.trade.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.entity.TaxCertificateRecord;
import com.zhixianghui.facade.trade.service.TaxCertificateRecordFacade;
import com.zhixianghui.service.trade.biz.TaxCertificateRecordBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class TaxCertificateRecordImpl implements TaxCertificateRecordFacade {

    @Autowired
    private TaxCertificateRecordBiz taxCertificateRecordBiz;

    @Override
    public TaxCertificateRecord save(TaxCertificateRecord taxCertificateRecord) throws BizException{
        taxCertificateRecordBiz.save(taxCertificateRecord);
        return taxCertificateRecord;
    }

    @Override
    public TaxCertificateRecord update(TaxCertificateRecord taxCertificateRecord) throws BizException {
        taxCertificateRecordBiz.updateById(taxCertificateRecord);
        return taxCertificateRecord;
    }

    @Override
    public TaxCertificateRecord getById(Integer id) throws BizException {
        return taxCertificateRecordBiz.getById(id);
    }

    @Override
    public Page<TaxCertificateRecord> listPage(Page<TaxCertificateRecord> page, Map<String,Object> paramMap) throws  BizException{
        return taxCertificateRecordBiz.listPage(page, paramMap);
    }

    @Override
    public List<TaxCertificateRecord> list(Map<String, Object> paramMap) throws BizException {
        return taxCertificateRecordBiz.list(paramMap);
    }

    @Override
    public boolean delete(Integer id) throws BizException {
        return taxCertificateRecordBiz.removeById(id);
    }
}
