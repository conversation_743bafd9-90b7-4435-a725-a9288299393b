package com.zhixianghui.service.trade.biz;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.trade.dto.*;
import com.zhixianghui.facade.trade.entity.CmbChangesFunds;
import com.zhixianghui.facade.trade.entity.CmbMerchantBalance;
import com.zhixianghui.facade.trade.enums.AdjustAccountTypeEnum;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.CmbUtil;
import com.zhixianghui.service.trade.dao.mapper.CmbChangesFundsMapper;
import com.zhixianghui.service.trade.dao.mapper.CmbMerchantBalanceMapper;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2024/8/16 15:35
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CmbMerchantBalanceBiz {

    private final CmbMerchantBalanceMapper cmbMerchantBalanceMapper;

    private final CmbChangesFundsMapper cmbChangesFundsMapper;

    private final RedisLock redisLock;

    private final PlatformTransactionManager platformTransactionManager;

    private final TransactionDefinition transactionDefinition;

    @Reference
    private SequenceFacade sequenceFacade;

    /**
     * 查询账户余额
     *
     * @param param
     * @return
     */
    public BigDecimal getAmount(CmbAccountQueryDTO param) {
        log.info("查询招行本地账户余额参数: {}", JSONUtil.toJsonStr(param));
        List<CmbMerchantBalance> cmbMerchantBalances = cmbMerchantBalanceMapper.selectList(Wrappers.<CmbMerchantBalance>lambdaQuery()
                .eq(CmbMerchantBalance::getMainstayNo, param.getMainstayNo())
                .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbMerchantBalance::getMchNo, param.getMchNo())
                .eq(CmbMerchantBalance::getMerchantType, param.getMerchantType()));
        if (ObjectUtils.isEmpty(cmbMerchantBalances)) {
            log.info("查询招行本地账户余额，暂未有设置账户，返回金额0");
            return BigDecimal.ZERO;
        }
        if (cmbMerchantBalances.size() > 1) {
            log.error("查询招行本地账户余额，数据异常，当前商户存在多个招行本地账户");
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("招行本地账户数据异常");
        }
        CmbMerchantBalance cmbMerchantBalance = cmbMerchantBalances.get(0);
        BigDecimal amount = NumberUtil.sub(cmbMerchantBalance.getTotalAmount(), cmbMerchantBalance.getFreezeAmount());
        log.info("查询招行本地账户余额，查询成功，查询余额为:{}", amount);
        return amount;
    }

    /**
     * 获取招行本地账户
     * @param param
     * @return
     */
    public CmbMerchantBalance getCmbMerchantBalance(CmbAccountQueryDTO param) {
        List<CmbMerchantBalance> cmbMerchantBalances = cmbMerchantBalanceMapper.selectList(Wrappers.<CmbMerchantBalance>lambdaQuery()
                .eq(CmbMerchantBalance::getMainstayNo, param.getMainstayNo())
                .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbMerchantBalance::getMchNo, param.getMchNo())
                .eq(CmbMerchantBalance::getMerchantType, param.getMerchantType()));
        if (ObjectUtil.isEmpty(cmbMerchantBalances)) {
            return null;
        }
        if (cmbMerchantBalances.size() > 1) {
            log.error("数据异常，当前商户存在多个招行本地账户");
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("招行本地账户数据异常");
        }
        return cmbMerchantBalances.get(0);
    }

    /**
     * 创建本地账户
     *
     * @param param
     */
    public void createMerchantBalance(CmbCreateAccountDTO param) {
        log.info("进入创建招行本地账户的流程，接收到的参数为：{}", JsonUtil.toString(param));
        String key = CmbUtil.getRedisLockKey(param.getMchNo(), param.getMainstayNo(), param.getMerchantType());
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        RLock rLock = redisLock.tryLock(key);
        try {
            if (rLock == null) {
                log.info("[创建招行本地账户]==>获取锁失败，商户类型：{}，代征主体编号：{}，商户编号：{}", param.getMainstayNo(), param.getMainstayNo(), param.getMchNo());
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
            }
            List<CmbMerchantBalance> cmbMerchantBalances = cmbMerchantBalanceMapper.selectList(Wrappers.<CmbMerchantBalance>lambdaQuery()
                    .eq(CmbMerchantBalance::getMainstayNo, param.getMainstayNo())
                    .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbMerchantBalance::getMchNo, param.getMchNo())
                    .eq(CmbMerchantBalance::getMerchantType, param.getMerchantType()));
            if (ObjectUtil.isNotEmpty(cmbMerchantBalances)) {
                return;
            }
            CmbMerchantBalance cmbMerchantBalance = new CmbMerchantBalance();
            cmbMerchantBalance.setVersion(0);
            cmbMerchantBalance.setMchNo(param.getMchNo());
            cmbMerchantBalance.setMchName(param.getMchName());
            cmbMerchantBalance.setMainstayNo(param.getMainstayNo());
            cmbMerchantBalance.setMainstayName(param.getMainstayName());
            cmbMerchantBalance.setTotalAmount(BigDecimal.ZERO);
            cmbMerchantBalance.setFreezeAmount(BigDecimal.ZERO);
            cmbMerchantBalance.setMerchantType(param.getMerchantType());
            cmbMerchantBalance.setCreateTime(new Date());
            cmbMerchantBalance.setUpdateTime(new Date());
            cmbMerchantBalanceMapper.insert(cmbMerchantBalance);
            platformTransactionManager.commit(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            log.error("[创建招行本地账户]==>创建异常，商户类型：{}，代征主体编号：{}，商户编号：{}",
                    param.getMainstayNo(), param.getMainstayNo(), param.getMchNo(), e);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
    }

    /**
     * 批量打款 (主要是给商户批量发放使用)
     *
     * @param param
     * @return
     */
    public void batchPaymentBalance(BatchPaymentBalanceDTO param) {
        log.info("招行本地账户批量打款，批次号：{}，商户类型：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                param.getMerchantType(), param.getMchNo(), param.getMainstayNo());
        String redisLockKey = CmbUtil.getRedisLockKey(param.getMchNo(), param.getMainstayNo(), param.getMerchantType());
        RLock rLock = redisLock.tryLock(redisLockKey, CmbUtil.LOCK_WAIT_TIME, CmbUtil.LOCK_LEASE_TIME);
        log.info("招行本地账户批量打款，获取锁:{}，流水号:{}", redisLockKey, param.getPlatBatchNo());
        if (rLock == null) {
            log.info("招行本地账户批量打款，获取锁失败，直接丢弃，logKey：{}，批次号：{}", redisLockKey, param.getPlatBatchNo());
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }

        //手动启动事务
        List<CmbChangesFunds> insertList = new ArrayList<>();
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            CmbMerchantBalance cmbMerchantBalance = cmbMerchantBalanceMapper.selectOne(Wrappers.<CmbMerchantBalance>lambdaQuery()
                    .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbMerchantBalance::getMchNo, param.getMchNo())
                    .eq(ObjectUtil.isNotEmpty(param.getMainstayNo()), CmbMerchantBalance::getMainstayNo, param.getMainstayNo())
                    .eq(ObjectUtil.isNotEmpty(param.getMerchantType()), CmbMerchantBalance::getMerchantType, param.getMerchantType()));
            if (ObjectUtils.isEmpty(cmbMerchantBalance)) {
                log.info("招行本地账户批量打款，获取不到招行本地账户, 批次号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(), param.getMchNo(), param.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取不到招行本地账户");
            }
            List<BatchPaymentBalanceDTO.BatchOrderInfo> orderInfoList = param.getOrderInfoList();
            BigDecimal payAmount = orderInfoList.stream().map(BatchPaymentBalanceDTO.BatchOrderInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal availableAmount = NumberUtil.sub(cmbMerchantBalance.getTotalAmount(), cmbMerchantBalance.getFreezeAmount());
            if (payAmount.compareTo(availableAmount) > 0) {
                log.info("招行本地账户批量打款, 打款总金额超过账户可用余额，批次号:{}，查询商户号：{}，供应商编号：{}，打款总金额：{}，账户可用余额：{}",
                        param.getPlatBatchNo(), param.getMchNo(), param.getMainstayNo(), payAmount, availableAmount);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("校验不通过, 打款总金额超过账户可用余额");
            }

            for (BatchPaymentBalanceDTO.BatchOrderInfo batchOrderInfo : orderInfoList) {
                CmbChangesFundsParamDTO fundsParamDTO = new CmbChangesFundsParamDTO();
                fundsParamDTO.setMchNo(param.getMchNo());
                fundsParamDTO.setMchName(param.getMchName());
                fundsParamDTO.setMainstayNo(param.getMainstayNo());
                fundsParamDTO.setMainstayName(param.getMainstayName());
                fundsParamDTO.setPlatTrxNo(batchOrderInfo.getPlatTrxNo());
                fundsParamDTO.setPlatBatchNo(param.getPlatBatchNo());
                fundsParamDTO.setLogKeyNo(batchOrderInfo.getPlatTrxNo());
                fundsParamDTO.setAmount(batchOrderInfo.getAmount().negate());
                fundsParamDTO.setFrozenAmount(BigDecimal.ZERO);
                fundsParamDTO.setMerchantType(param.getMerchantType());
                fundsParamDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.PAYMENT.getValue());
                CmbChangesFunds funds = bulidCmbChangesFunds(fundsParamDTO);
                //计算变动前变动金额
                funds.setBeforeAmount(cmbMerchantBalance.getTotalAmount())
                        .setAfterAmount(NumberUtil.add(cmbMerchantBalance.getTotalAmount(), funds.getAmount()))
                        .setBeforeFrozenAmount(cmbMerchantBalance.getFreezeAmount())
                        .setAfterFrozenAmount(NumberUtil.add(cmbMerchantBalance.getFreezeAmount(), funds.getFrozenAmount()));
                insertList.add(funds);
                cmbMerchantBalance.setTotalAmount(funds.getAfterAmount());
                cmbMerchantBalance.setUpdateTime(new Date());
            }

            cmbChangesFundsMapper.insertBatch(insertList);
            cmbMerchantBalanceMapper.updateById(cmbMerchantBalance);
            //提交事务
            platformTransactionManager.commit(transaction);
        } catch (DuplicateKeyException e) {
            //校验是否已经全部处理过
            List<String> logKeys = insertList.stream().map(CmbChangesFunds::getLogKey).collect(Collectors.toList());
            Boolean exist = checkCmbChangesFundsExist(logKeys, param.getMerchantType(), WxAmountChangeLogTypeEnum.PAYMENT.getValue());
            if (exist) { //如果本批次全部都已处理过，就直接返回且不抛出异常，让后续流程可以继续执行
                log.info("招行本地账户批量打款, 该批次已经处理过，无需重复处理，批次号:[{}]，查询商户号[{}]，供应商编号：[{}]",
                        param.getPlatBatchNo(), param.getMchNo(), param.getMainstayNo());
                platformTransactionManager.rollback(transaction);
            } else {
                platformTransactionManager.rollback(transaction);
                log.error("招行本地账户批量打款，批次号：{}，打款异常。", param.getPlatBatchNo(), e);
                throw e;
            }
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            log.error("招行本地账户批量打款，批次号：{}，打款异常。", param.getPlatBatchNo(), e);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
    }

    /**
     * 打款
     *
     * @param param
     */
    public void paymentBalance(PaymentBalanceDTO param) {
        log.info("招行本地账户打款，批次号：{}，流水号：{}，商户类型：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                param.getPlatTrxNo(), param.getMerchantType(), param.getMchNo(), param.getMainstayNo());
        String redisLockKey = CmbUtil.getRedisLockKey(param.getMchNo(), param.getMainstayNo(), param.getMerchantType());
        RLock rLock = redisLock.tryLock(redisLockKey, CmbUtil.LOCK_WAIT_TIME, CmbUtil.LOCK_LEASE_TIME);
        log.info("招行本地账户打款，获取锁:{}，批次号：{}, 流水号:{}", redisLockKey, param.getPlatBatchNo(), param.getPlatTrxNo());
        if (rLock == null) {
            log.info("招行本地账户打款，获取锁失败，直接丢弃，logKey：{}，批次号：{},流水号：{}", redisLockKey, param.getPlatBatchNo(), param.getPlatTrxNo());
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }

        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            CmbMerchantBalance cmbMerchantBalance = cmbMerchantBalanceMapper.selectOne(Wrappers.<CmbMerchantBalance>lambdaQuery()
                    .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbMerchantBalance::getMchNo, param.getMchNo())
                    .eq(ObjectUtil.isNotEmpty(param.getMainstayNo()), CmbMerchantBalance::getMainstayNo, param.getMainstayNo())
                    .eq(ObjectUtil.isNotEmpty(param.getMerchantType()), CmbMerchantBalance::getMerchantType, param.getMerchantType()));
            if (ObjectUtils.isEmpty(cmbMerchantBalance)) {
                log.info("招行本地账户打款，获取不到招行本地账户, 批次号：{}，流水号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                        param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取不到招行本地账户");
            }
            BigDecimal availableAmount = NumberUtil.sub(cmbMerchantBalance.getTotalAmount(), cmbMerchantBalance.getFreezeAmount());
            if (param.getAmount().compareTo(availableAmount) > 0) {
                log.info("招行本地账户打款, 打款金额超过账户可用余额，批次号:{}，流水号：{}，查询商户号：{}，供应商编号：{}，打款总金额：{}，账户可用余额：{}",
                        param.getPlatBatchNo(), param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo(), param.getAmount(), availableAmount);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("校验不通过, 打款金额超过账户可用余额");
            }

            CmbChangesFundsParamDTO fundsParamDTO = new CmbChangesFundsParamDTO();
            fundsParamDTO.setMchNo(param.getMchNo());
            fundsParamDTO.setMchName(param.getMchName());
            fundsParamDTO.setMainstayNo(param.getMainstayNo());
            fundsParamDTO.setMainstayName(param.getMainstayName());
            fundsParamDTO.setPlatTrxNo(param.getPlatTrxNo());
            fundsParamDTO.setPlatBatchNo(param.getPlatBatchNo());
            fundsParamDTO.setLogKeyNo(param.getPlatTrxNo());
            fundsParamDTO.setAmount(param.getAmount().negate());
            fundsParamDTO.setFrozenAmount(BigDecimal.ZERO);
            fundsParamDTO.setMerchantType(param.getMerchantType());
            fundsParamDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.PAYMENT.getValue());
            CmbChangesFunds funds = bulidCmbChangesFunds(fundsParamDTO);

            //计算变动前变动金额
            funds.setBeforeAmount(cmbMerchantBalance.getTotalAmount())
                    .setAfterAmount(NumberUtil.add(cmbMerchantBalance.getTotalAmount(), funds.getAmount()));

            cmbMerchantBalance.setTotalAmount(funds.getAfterAmount());
            cmbMerchantBalance.setUpdateTime(new Date());

            LambdaQueryWrapper<CmbChangesFunds> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(CmbChangesFunds::getId);
            lambdaQueryWrapper.eq(CmbChangesFunds::getMerchantType, param.getMerchantType());
            lambdaQueryWrapper.eq(CmbChangesFunds::getAmountChangeType, WxAmountChangeLogTypeEnum.PAYMENT.getValue());
            lambdaQueryWrapper.eq(CmbChangesFunds::getLogKey, funds.getLogKey());
            List<Object> exists = cmbChangesFundsMapper.selectObjs(lambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(exists)) {
                log.info("招行本地账户打款, 该打款已经处理，无需重复处理，批次号：{}，流水号：{}，查询商户号：{}，供应商编号：{}",
                        param.getPlatBatchNo(), param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                return;
            }
            cmbChangesFundsMapper.insert(funds);
            cmbMerchantBalanceMapper.updateById(cmbMerchantBalance);
            //提交事务
            platformTransactionManager.commit(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            log.error("招行本地账户打款，批次号：{}，流水号：{}，打款异常：", param.getPlatBatchNo(), param.getPlatTrxNo(), e);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
    }

    /**
     * 批量退款
     */
    public void batchRefundBalance(BatchRefundBalanceDTO param) {

    }

    /**
     * 退款
     * 无需处理服务费
     */
    public void refundBalance(RefundBalanceDTO param) {
        log.info("招行本地账户退款，批次号：{}，流水号：{}，，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
        String redisLockKey = CmbUtil.getRedisLockKey(param.getMchNo(), param.getMainstayNo(), MerchantTypeEnum.EMPLOYER.getValue());
        RLock rLock = redisLock.tryLock(redisLockKey, CmbUtil.LOCK_WAIT_TIME, CmbUtil.LOCK_LEASE_TIME);
        log.info("招行本地账户退款，获取锁:{}，批次号：{}, 流水号:{}", redisLockKey, param.getPlatBatchNo(), param.getPlatTrxNo());
        if (rLock == null) {
            log.info("招行本地账户退款，获取锁失败，直接丢弃，logKey：{}，批次号：{},流水号：{}", redisLockKey, param.getPlatBatchNo(), param.getPlatTrxNo());
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            //获取打款变动记录
            CmbChangesFunds payFunds = cmbChangesFundsMapper.selectOne(Wrappers.<CmbChangesFunds>lambdaQuery()
                    .eq(CmbChangesFunds::getMerchantType, MerchantTypeEnum.EMPLOYER.getValue())
                    .eq(CmbChangesFunds::getPlatBatchNo, param.getPlatBatchNo())
                    .eq(CmbChangesFunds::getPlatTrxNo, param.getPlatTrxNo()));
            if (ObjectUtil.isEmpty(payFunds)) {
                log.info("招行本地账户退款，获取不到打款记录, 批次号：{}，流水号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                        param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取不到打款记录");
            }
            // 校验商户退款金额
            BigDecimal add = NumberUtil.add(param.getAmount(), param.getFeeAmount());
            if(add.compareTo(payFunds.getAmount().abs()) > 0) {
                log.info("招行本地账户退款，商户退款金额超出支付金额, 批次号：{}，流水号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                        param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户退款金额超出支付金额");
            }
            CmbMerchantBalance cmbMerchantBalance = cmbMerchantBalanceMapper.selectOne(Wrappers.<CmbMerchantBalance>lambdaQuery()
                    .eq(CmbMerchantBalance::getMchNo, param.getMchNo())
                    .eq(CmbMerchantBalance::getMainstayNo, param.getMainstayNo())
                    .eq(CmbMerchantBalance::getMerchantType, MerchantTypeEnum.EMPLOYER.getValue()));
            if (ObjectUtils.isEmpty(cmbMerchantBalance)) {
                log.info("招行本地账户退款，获取不到招行本地账户, 批次号：{}，流水号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                        param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取不到招行本地账户");
            }
            CmbChangesFundsParamDTO fundsParamDTO = new CmbChangesFundsParamDTO();
            fundsParamDTO.setMchNo(param.getMchNo());
            fundsParamDTO.setMchName(param.getMchName());
            fundsParamDTO.setMainstayNo(param.getMainstayNo());
            fundsParamDTO.setMainstayName(param.getMainstayName());
            fundsParamDTO.setPlatTrxNo(param.getPlatTrxNo());
            fundsParamDTO.setPlatBatchNo(param.getPlatBatchNo());
            fundsParamDTO.setLogKeyNo(param.getPlatTrxNo());
            fundsParamDTO.setAmount(add);
            fundsParamDTO.setFrozenAmount(BigDecimal.ZERO);
            fundsParamDTO.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            fundsParamDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.REFUND.getValue());
            CmbChangesFunds funds = bulidCmbChangesFunds(fundsParamDTO);
            //计算变动前变动金额
            funds.setBeforeAmount(cmbMerchantBalance.getTotalAmount())
                    .setAfterAmount(NumberUtil.add(cmbMerchantBalance.getTotalAmount(), funds.getAmount()));

            cmbMerchantBalance.setTotalAmount(funds.getAfterAmount());
            cmbMerchantBalance.setUpdateTime(new Date());

            LambdaQueryWrapper<CmbChangesFunds> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(CmbChangesFunds::getId);
            lambdaQueryWrapper.eq(CmbChangesFunds::getLogKey, funds.getLogKey());
            List<Object> exists = cmbChangesFundsMapper.selectObjs(lambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(exists)) {
                log.info("招行本地账户退款, 该退款已经存在，无需重复处理，批次号：{}，流水号：{}，查询商户号：{}，供应商编号：{}",
                        param.getPlatBatchNo(), param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                return;
            }
            cmbChangesFundsMapper.insert(funds);
            cmbMerchantBalanceMapper.updateById(cmbMerchantBalance);
            //提交事务
            platformTransactionManager.commit(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            log.error("招行本地账户退款，批次号：{}，流水号：{}，打款异常：", param.getPlatBatchNo(), param.getPlatTrxNo(), e);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
    }

    /**
     * 退款
     * 需要处理服务费
     */
    public void refundBalanceWithFee(RefundBalanceDTO param) {
        log.info("招行本地账户退款，批次号：{}，流水号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            String logKey = CmbUtil.getLogKey(param.getPlatTrxNo(), WxAmountChangeLogTypeEnum.REFUND.getValue(), MerchantTypeEnum.EMPLOYER.getValue());
            LambdaQueryWrapper<CmbChangesFunds> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(CmbChangesFunds::getId);
            lambdaQueryWrapper.eq(CmbChangesFunds::getLogKey, logKey);
            List<Object> exists = cmbChangesFundsMapper.selectObjs(lambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(exists)) {
                log.info("招行本地账户退款, 该退款已经存在，无需重复处理，批次号：{}，流水号：{}，查询商户号：{}，供应商编号：{}",
                        param.getPlatBatchNo(), param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                return;
            }
            //获取打款变动记录
            CmbChangesFunds payFunds = cmbChangesFundsMapper.selectOne(Wrappers.<CmbChangesFunds>lambdaQuery()
                    .eq(CmbChangesFunds::getMerchantType, MerchantTypeEnum.EMPLOYER.getValue())
                    .eq(CmbChangesFunds::getPlatBatchNo, param.getPlatBatchNo())
                    .eq(CmbChangesFunds::getPlatTrxNo, param.getPlatTrxNo()));
            if (ObjectUtil.isEmpty(payFunds)) {
                log.info("招行本地账户退款，获取不到打款记录, 批次号：{}，流水号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                        param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取不到打款记录");
            }

            // 校验商户退款金额
            BigDecimal add = NumberUtil.add(param.getAmount(), param.getFeeAmount());
            if(add.compareTo(payFunds.getAmount().abs()) > 0) {
                log.info("招行本地账户退款，商户退款金额超出支付金额, 批次号：{}，流水号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                        param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户退款金额超出支付金额");
            }
            CmbChangesFundsParamDTO fundsParamDTO = new CmbChangesFundsParamDTO();
            fundsParamDTO.setMchNo(param.getMchNo());
            fundsParamDTO.setMchName(param.getMchName());
            fundsParamDTO.setMainstayNo(param.getMainstayNo());
            fundsParamDTO.setMainstayName(param.getMainstayName());
            fundsParamDTO.setPlatTrxNo(param.getPlatTrxNo());
            fundsParamDTO.setPlatBatchNo(param.getPlatBatchNo());
            fundsParamDTO.setLogKeyNo(param.getPlatTrxNo());
            fundsParamDTO.setAmount(add);
            fundsParamDTO.setFrozenAmount(BigDecimal.ZERO);
            fundsParamDTO.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            fundsParamDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.REFUND.getValue());
            CmbChangesFunds funds = bulidCmbChangesFunds(fundsParamDTO);
            changeAmountWithLock(funds);

            //处理服务费
            CmbChangesFunds payFeeFunds = cmbChangesFundsMapper.selectOne(Wrappers.<CmbChangesFunds>lambdaQuery()
                    .eq(CmbChangesFunds::getMerchantType, MerchantTypeEnum.MAINSTAY.getValue())
                    .eq(CmbChangesFunds::getPlatBatchNo, param.getPlatBatchNo())
                    .eq(CmbChangesFunds::getAmountChangeType, WxAmountChangeLogTypeEnum.PAYMENT.getValue()));
            if (ObjectUtil.isNotEmpty(payFeeFunds)) {
                // 校验服务费不能超出本批次的总服务费
                if(param.getFeeAmount().compareTo(payFeeFunds.getAmount().abs()) > 0) {
                    log.info("招行本地账户退款，退款服务费超出当前批次总服务费, 批次号：{}，流水号：{}，商户号：{}，供应商编号：{}", param.getPlatBatchNo(),
                            param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("退款服务费超出当前批次总服务费");
                }
                CmbChangesFundsParamDTO feeFundsParamDTO = new CmbChangesFundsParamDTO();
                feeFundsParamDTO.setMainstayNo(param.getMainstayNo());
                feeFundsParamDTO.setMainstayName(param.getMainstayName());
                feeFundsParamDTO.setPlatTrxNo(param.getPlatTrxNo());
                feeFundsParamDTO.setPlatBatchNo(param.getPlatBatchNo());
                feeFundsParamDTO.setLogKeyNo(param.getPlatTrxNo());
                feeFundsParamDTO.setAmount(param.getFeeAmount().negate());
                feeFundsParamDTO.setFrozenAmount(BigDecimal.ZERO);
                feeFundsParamDTO.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
                feeFundsParamDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.REFUND.getValue());
                CmbChangesFunds feeFunds = bulidCmbChangesFunds(feeFundsParamDTO);
                changeAmountWithLock(feeFunds);
            }
            //提交事务
            platformTransactionManager.commit(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            log.error("招行本地账户退款，批次号：{}，流水号：{}，打款异常：", param.getPlatBatchNo(), param.getPlatTrxNo(), e);
            throw e;
        }
    }


    /**
     * 批量并行检查资金变动记录是否存在
     *
     * @param logKeys
     * @param merchantType
     * @param amountChangeType
     * @return
     */
    public Boolean checkCmbChangesFundsExist(List<String> logKeys, Integer merchantType, Integer amountChangeType) {
        // 定义每次批量查询的数量
        int batchSize = 1000;
        // 分批处理订单号，并行执行查询
        List<String> existingLogKeys = IntStream.range(0, (logKeys.size() + batchSize - 1) / batchSize)
                .parallel()  // 并行处理
                .mapToObj(i -> {
                    int start = i * batchSize;
                    int end = Math.min(start + batchSize, logKeys.size());
                    List<String> batchList = logKeys.subList(start, end);

                    LambdaQueryWrapper<CmbChangesFunds> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.select(CmbChangesFunds::getId);
                    lambdaQueryWrapper.eq(CmbChangesFunds::getMerchantType, merchantType);
                    lambdaQueryWrapper.eq(CmbChangesFunds::getAmountChangeType, amountChangeType);
                    lambdaQueryWrapper.in(CmbChangesFunds::getLogKey, batchList);
                    return cmbChangesFundsMapper.selectObjs(lambdaQueryWrapper).stream().map(Object::toString).collect(Collectors.toList());
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());
        boolean exist = false;
        if (ObjectUtil.isEmpty(existingLogKeys)) {
            return exist;
        }
        if (logKeys.size() == existingLogKeys.size()) {
            exist = true;
        }
        return exist;
    }


    /**
     * 冻结余额
     *
     * @param param
     */
    public void freezeBalance(FreezeBalanceDTO param) {
        log.info("招行本地账户冻结余额，订单号：{}，商户类型：{}，商户号：{}，供应商编号：{}", param.getPlatTrxNo(),
                param.getMerchantType(), param.getMchNo(), param.getMainstayNo());
        CmbChangesFundsParamDTO fundsParamDTO = new CmbChangesFundsParamDTO();
        fundsParamDTO.setMchNo(param.getMchNo());
        fundsParamDTO.setMchName(param.getMchName());
        fundsParamDTO.setMainstayNo(param.getMainstayNo());
        fundsParamDTO.setMainstayName(param.getMainstayName());
        fundsParamDTO.setPlatTrxNo(param.getPlatTrxNo());
        fundsParamDTO.setLogKeyNo(param.getPlatTrxNo());
        fundsParamDTO.setAmount(BigDecimal.ZERO);
        fundsParamDTO.setFrozenAmount(param.getFrozenAmount());
        fundsParamDTO.setMerchantType(param.getMerchantType());
        fundsParamDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.FROZEN.getValue());
        CmbChangesFunds funds = bulidCmbChangesFunds(fundsParamDTO);
        changeAmount(funds);
    }


    /**
     * 解冻余额
     *
     * @param param
     */
    public void unfreezeBalance(FreezeBalanceDTO param) {
        log.info("招行本地账户解冻余额，订单号：{}，商户类型：{}，商户号：{}，供应商编号：{}", param.getPlatTrxNo(),
                param.getMerchantType(), param.getMchNo(), param.getMainstayNo());
        //查询冻结冻结余额记录
        List<CmbChangesFunds> cmbChangesFunds = cmbChangesFundsMapper.selectList(Wrappers.<CmbChangesFunds>lambdaQuery()
                .eq(CmbChangesFunds::getAmountChangeType, WxAmountChangeLogTypeEnum.FROZEN.getValue())
                .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbChangesFunds::getMchNo, param.getMchNo())
                .eq(CmbChangesFunds::getMainstayNo, param.getMainstayNo())
                .eq(CmbChangesFunds::getPlatTrxNo, param.getPlatTrxNo())
                .eq(CmbChangesFunds::getMerchantType, param.getMerchantType()));
        if (ObjectUtil.isEmpty(cmbChangesFunds)) {
            log.error("解冻余额，查找不到账户冻结余额，提现流水号：[{}]", param.getPlatTrxNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查找不到账户冻结余额");
        }
        CmbChangesFunds freezeFunds = cmbChangesFunds.get(0);
        CmbChangesFundsParamDTO unfreezeParamDTO = new CmbChangesFundsParamDTO();
        unfreezeParamDTO.setMchNo(param.getMchNo());
        unfreezeParamDTO.setMchName(param.getMchName());
        unfreezeParamDTO.setMainstayNo(param.getMainstayNo());
        unfreezeParamDTO.setMainstayName(param.getMainstayName());
        unfreezeParamDTO.setPlatTrxNo(param.getPlatTrxNo());
        unfreezeParamDTO.setLogKeyNo(param.getPlatTrxNo());
        unfreezeParamDTO.setAmount(BigDecimal.ZERO);
        unfreezeParamDTO.setFrozenAmount(freezeFunds.getFrozenAmount().negate());
        unfreezeParamDTO.setMerchantType(param.getMerchantType());
        unfreezeParamDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.UN_FROZEN.getValue());
        CmbChangesFunds unfreezeFunds = bulidCmbChangesFunds(unfreezeParamDTO);
        changeAmount(unfreezeFunds);
    }

    /**
     * 商户支付平台手续费
     *
     * @param param
     */
    public void deductionCommission(DeductionCommissionDTO param) {
        log.info("商户支付服务费，入参参数：{}", JSONUtil.toJsonStr(param));
        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            //增加代征主体服务费
            CmbChangesFundsParamDTO feeFundsDTO = new CmbChangesFundsParamDTO();
            feeFundsDTO.setMainstayNo(param.getMainstayNo());
            feeFundsDTO.setMainstayName(param.getMainstayName());
            feeFundsDTO.setPlatTrxNo(param.getPlatTrxNo());
            feeFundsDTO.setPlatBatchNo(param.getPlatBatchNo());
            feeFundsDTO.setLogKeyNo(param.getPlatBatchNo());
            feeFundsDTO.setAmount(param.getFeeAmount());
            feeFundsDTO.setFrozenAmount(BigDecimal.ZERO);
            feeFundsDTO.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            feeFundsDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.PAYMENT.getValue());
            CmbChangesFunds feeFunds = bulidCmbChangesFunds(feeFundsDTO);

            //校验是否已经处理
            LambdaQueryWrapper<CmbChangesFunds> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(CmbChangesFunds::getId);
            lambdaQueryWrapper.eq(CmbChangesFunds::getMerchantType, MerchantTypeEnum.MAINSTAY.getValue());
            lambdaQueryWrapper.eq(CmbChangesFunds::getAmountChangeType, WxAmountChangeLogTypeEnum.PAYMENT.getValue());
            lambdaQueryWrapper.eq(CmbChangesFunds::getLogKey, feeFunds.getLogKey());
            List<Object> exists = cmbChangesFundsMapper.selectObjs(lambdaQueryWrapper);
            if (ObjectUtil.isNotEmpty(exists)) {
                log.info("商户支付服务费, 服务费已支付，无需重复处理，批次号：{}，流水号：{}，查询商户号：{}，供应商编号：{}",
                        param.getPlatBatchNo(), param.getPlatTrxNo(), param.getMchNo(), param.getMainstayNo());
                return;
            }
            changeAmountWithLock(feeFunds);
            //提交事务
            platformTransactionManager.commit(transaction);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            log.error("[发放环节: {}]==>批次发放完成，处理招行本地账户异常。", param.getPlatTrxNo(), e);
            throw e;
        }
    }

    /**
     * 提现
     *
     * @param param
     */
    public void withdrawAccount(WithdrawAccountParamDTO param) {
        log.info("招行本地账户提现,订单号：{},商户类型：{},商户号：{}，供应商编号：{}", param.getPlatTrxNo(),
                param.getMerchantType(), param.getMchNo(), param.getMainstayNo());
        //查询冻结冻结余额记录
        List<CmbChangesFunds> cmbChangesFunds = cmbChangesFundsMapper.selectList(Wrappers.<CmbChangesFunds>lambdaQuery()
                .eq(CmbChangesFunds::getAmountChangeType, WxAmountChangeLogTypeEnum.FROZEN.getValue())
                .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbChangesFunds::getMchNo, param.getMchNo())
                .eq(CmbChangesFunds::getMainstayNo, param.getMainstayNo())
                .eq(CmbChangesFunds::getPlatTrxNo, param.getPlatTrxNo())
                .eq(CmbChangesFunds::getMerchantType, param.getMerchantType()));
        if (ObjectUtil.isEmpty(cmbChangesFunds)) {
            log.error("招行本地账户提现，查找不到账户冻结余额，提现流水号：{}", param.getPlatTrxNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查找不到账户冻结余额");
        }
        CmbChangesFunds funds = cmbChangesFunds.get(0);
        //扣减本地账户余额
        if (funds.getFrozenAmount().compareTo(param.getAmount()) < 0) {
            log.error("招行本地账户提现，提现金额超过冻结余额，提现失败，提现流水号：{}", param.getPlatTrxNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提现金额超过冻结余额");
        }
        CmbChangesFundsParamDTO withdrawFundsDTO = new CmbChangesFundsParamDTO();
        withdrawFundsDTO.setMainstayNo(param.getMainstayNo());
        withdrawFundsDTO.setMainstayName(param.getMainstayName());
        withdrawFundsDTO.setPlatTrxNo(param.getPlatTrxNo());
        withdrawFundsDTO.setLogKeyNo(param.getPlatTrxNo());
        withdrawFundsDTO.setAmount(param.getAmount().negate());
        withdrawFundsDTO.setFrozenAmount(param.getAmount().negate());
        withdrawFundsDTO.setMerchantType(param.getMerchantType());
        withdrawFundsDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.WITHDRAW.getValue());
        CmbChangesFunds withdrawFunds = bulidCmbChangesFunds(withdrawFundsDTO);
        changeAmount(withdrawFunds);
    }


    /**
     * 充值账户
     *
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void rechargeAccount(RechargeAccountParamDTO param) {
        log.info("招行本地账户充值余额,订单号:{},商户类型:{},商户号{},供应商编号：{}", param.getPlatTrxNo(),
                param.getMerchantType(), param.getMchNo(), param.getMainstayNo());
        CmbChangesFundsParamDTO fundsParamDTO = new CmbChangesFundsParamDTO();
        fundsParamDTO.setMchNo(param.getMchNo());
        fundsParamDTO.setMchName(param.getMchName());
        fundsParamDTO.setMainstayNo(param.getMainstayNo());
        fundsParamDTO.setMainstayName(param.getMainstayName());
        fundsParamDTO.setPlatTrxNo(param.getPlatTrxNo());
        fundsParamDTO.setLogKeyNo(param.getPlatTrxNo());
        fundsParamDTO.setAmount(param.getAmount());
        fundsParamDTO.setFrozenAmount(BigDecimal.ZERO);
        fundsParamDTO.setMerchantType(param.getMerchantType());
        fundsParamDTO.setAmountChangeType(WxAmountChangeLogTypeEnum.DEPOSIT.getValue());
        CmbChangesFunds funds = bulidCmbChangesFunds(fundsParamDTO);
        changeAmountWithLock(funds);
    }


    /**
     * 调账
     *
     * @param param
     */
    public void adjustAccount(AdjustAccountParamDTO param) {
        log.info("招行调账，供应商{}，用工企业：{}，调账金额：{},调账类型:{}", param.getMainstayNo(),
                param.getMchNo(), param.getAmount(), AdjustAccountTypeEnum.getEnum(param.getType()).getDesc());
        // 生成业务编号
        String bizId = null;
        if (MerchantTypeEnum.EMPLOYER.getValue() == param.getMerchantType()) {
            bizId = sequenceFacade.nextRedisId(SequenceBizKeyEnum.CMB_ADJUSTMENT.getPrefix(),
                    SequenceBizKeyEnum.CMB_ADJUSTMENT.getKey(),
                    SequenceBizKeyEnum.CMB_ADJUSTMENT.getWidth());
        } else if (MerchantTypeEnum.MAINSTAY.getValue() == param.getMerchantType()) {
            bizId = sequenceFacade.nextRedisId(SequenceBizKeyEnum.CMB_MAINSTAY_ADJUSTMENT.getPrefix(),
                    SequenceBizKeyEnum.CMB_MAINSTAY_ADJUSTMENT.getKey(),
                    SequenceBizKeyEnum.CMB_MAINSTAY_ADJUSTMENT.getWidth());
        }
        CmbChangesFundsParamDTO fundsParam = new CmbChangesFundsParamDTO();
        fundsParam.setMchNo(param.getMchNo());
        fundsParam.setMchName(param.getMchName());
        fundsParam.setMainstayNo(param.getMainstayNo());
        fundsParam.setMainstayName(param.getMainstayName());
        fundsParam.setPlatTrxNo(bizId);
        fundsParam.setLogKeyNo(bizId);
        if (AdjustAccountTypeEnum.CMB_SUB.getValue() == param.getType()) {
            fundsParam.setAmount(param.getAmount().negate());
        } else if (AdjustAccountTypeEnum.CMB_ADD.getValue() == param.getType()) {
            fundsParam.setAmount(param.getAmount());
        }
        fundsParam.setFrozenAmount(BigDecimal.ZERO);
        fundsParam.setMerchantType(param.getMerchantType());
        fundsParam.setAmountChangeType(WxAmountChangeLogTypeEnum.ADJUSTMENT.getValue());
        CmbChangesFunds funds = bulidCmbChangesFunds(fundsParam);
        funds.setOperator(param.getOperator());
        changeAmount(funds);
    }

    /**
     * 处理余额，带锁
     *
     * @param changesFunds
     */
    private void changeAmountWithLock(CmbChangesFunds changesFunds) {
        String redisLockKey = CmbUtil.getRedisLockKey(changesFunds.getMchNo(), changesFunds.getMainstayNo(), changesFunds.getMerchantType());
        RLock rLock = redisLock.tryLock(redisLockKey, CmbUtil.LOCK_WAIT_TIME, CmbUtil.LOCK_LEASE_TIME);
        log.info("[招行本地账户余额变动]==>获取锁:{},流水号：{}", redisLockKey, changesFunds.getPlatTrxNo());
        if (rLock == null) {
            log.info("[招行本地账户余额变动]==>获取锁失败，直接丢弃，logKey：[{}]，订单号：[{}]，资金变动信息：[{}]", changesFunds.getLogKey(), changesFunds.getPlatTrxNo(), JSON.toJSONString(changesFunds));
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try {
            actualChangeAmount(changesFunds);
        } catch (Exception e) {
            log.error("[招行本地账户余额变动]==>异常：流水号：[{}]", changesFunds.getPlatTrxNo(), e);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
    }

    /**
     * 处理余额，自行在外部加锁和事务
     *
     * @param changesFunds
     */
    public void changeAmountNoLock(CmbChangesFunds changesFunds) {
        actualChangeAmount(changesFunds);
    }

    /**
     * 根据入账记录处理余额
     * 注意锁释放和事务的顺序
     *
     * @param changesFunds
     */
    public void changeAmount(CmbChangesFunds changesFunds) {
        String redisLockKey = CmbUtil.getRedisLockKey(changesFunds.getMchNo(), changesFunds.getMainstayNo(), changesFunds.getMerchantType());
        RLock rLock = redisLock.tryLock(redisLockKey, CmbUtil.LOCK_WAIT_TIME, CmbUtil.LOCK_LEASE_TIME);
        log.info("[招行本地账户余额变动]==>获取锁:{}，流水号:{}", redisLockKey, changesFunds.getPlatTrxNo());
        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        if (rLock == null) {
            log.info("[招行本地账户余额变动]==>获取锁失败，直接丢弃，logKey：[{}]，订单号：[{}]，资金变动信息：[{}]", changesFunds.getLogKey(), changesFunds.getPlatTrxNo(), JSON.toJSONString(changesFunds));
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try {
            actualChangeAmount(changesFunds);
            //提交事务
            platformTransactionManager.commit(transaction);
        } catch (DuplicateKeyException e) {
            log.info("[招行本地账户余额变动]==>该记录已入账，直接跳过，商户号：[{}],logKey：[{}]，交易记录：[{}]", changesFunds.getMchNo(), changesFunds.getLogKey(), JSON.toJSONString(changesFunds));
            platformTransactionManager.rollback(transaction);
            throw e;
        } catch (Exception e) {
            log.error("[招行本地账户余额变动]==>异常：流水号：[{}]", changesFunds.getPlatTrxNo(), e);
            platformTransactionManager.rollback(transaction);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
    }

    /**
     * 实际修改账户余额
     *
     * @param changesFunds
     */
    private void actualChangeAmount(CmbChangesFunds changesFunds) {
        LambdaQueryWrapper<CmbMerchantBalance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmbMerchantBalance::getMainstayNo, changesFunds.getMainstayNo());
        queryWrapper.eq(CmbMerchantBalance::getMerchantType, changesFunds.getMerchantType());
        if(MerchantTypeEnum.EMPLOYER.getValue() == changesFunds.getMerchantType()) {
            queryWrapper.eq(CmbMerchantBalance::getMchNo, changesFunds.getMchNo());
        }
        CmbMerchantBalance cmbMerchantBalance = cmbMerchantBalanceMapper.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(cmbMerchantBalance)) {
            log.info("[招行本地账户余额变动]==>获取不到招行本地账户, 订单号:[{}]，变动类型：[{}], 查询商户号[{}]，供应商编号：[{}]",
                    changesFunds.getPlatTrxNo(), changesFunds.getAmountChangeType(), changesFunds.getMchNo(), changesFunds.getMainstayNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取不到招行本地账户");
        }
        //账户金额校验, 如果是资金扣减，需要校验账户金额不为0
        if (changesFunds.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            BigDecimal add = NumberUtil.add(cmbMerchantBalance.getTotalAmount(), changesFunds.getAmount());
            if (add.compareTo(BigDecimal.ZERO) < 0) {
                log.info("[招行本地账户余额变动]==>招行本地账户校验不通过, 扣减的金额已超过余额,订单号:[{}]，变动类型：[{}], 查询商户号[{}]，供应商编号：[{}]",
                        changesFunds.getPlatTrxNo(), changesFunds.getAmountChangeType(), changesFunds.getMchNo(), changesFunds.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("校验不通过, 扣减的金额已超过余额");
            }
        }
        if (changesFunds.getFrozenAmount().compareTo(BigDecimal.ZERO) < 0) {
            BigDecimal add = NumberUtil.add(cmbMerchantBalance.getFreezeAmount(), changesFunds.getFrozenAmount());
            if (add.compareTo(BigDecimal.ZERO) < 0) {
                log.info("[招行本地账户余额变动]==>招行本地账户校验不通过, 扣减的冻结金额已超过冻结余额,订单号:[{}]，变动类型：[{}], 查询商户号[{}]，供应商编号：[{}]",
                        changesFunds.getPlatTrxNo(), changesFunds.getAmountChangeType(), changesFunds.getMchNo(), changesFunds.getMainstayNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("校验不通过, 扣减的金额已超过余额");
            }
        }
        //计算变动前变动金额
        changesFunds.setBeforeAmount(cmbMerchantBalance.getTotalAmount())
                .setAfterAmount(NumberUtil.add(cmbMerchantBalance.getTotalAmount(), changesFunds.getAmount()))
                .setBeforeFrozenAmount(cmbMerchantBalance.getFreezeAmount())
                .setAfterFrozenAmount(NumberUtil.add(cmbMerchantBalance.getFreezeAmount(), changesFunds.getFrozenAmount()));
        //插入资金变动表
        cmbChangesFundsMapper.insert(changesFunds);
        log.info("[招行本地账户余额变动]==>订单号:{}，变动类型：{},资金开始操作，查询商户号{}，供应商编号：{}，总金额：{}，冻结金额：{}",
                changesFunds.getPlatTrxNo(), changesFunds.getAmountChangeType(), changesFunds.getMchNo(), changesFunds.getMainstayNo(),
                cmbMerchantBalance.getTotalAmount(), cmbMerchantBalance.getFreezeAmount());
        cmbMerchantBalance.setTotalAmount(changesFunds.getAfterAmount());
        cmbMerchantBalance.setFreezeAmount(changesFunds.getAfterFrozenAmount());
        cmbMerchantBalance.setUpdateTime(new Date());
        cmbMerchantBalanceMapper.updateById(cmbMerchantBalance);
        log.info("[招行本地账户余额变动]==>订单号:{}，变动类型：{},资金操作完成，查询商户号{}，供应商编号：{}，总金额：{}，冻结金额：{}",
                changesFunds.getPlatTrxNo(), changesFunds.getAmountChangeType(), changesFunds.getMchNo(), changesFunds.getMainstayNo(),
                cmbMerchantBalance.getTotalAmount(), cmbMerchantBalance.getFreezeAmount());
    }


    /**
     * 构建 CmbChangesFunds
     *
     * @param parma
     * @return
     */
    private CmbChangesFunds bulidCmbChangesFunds(CmbChangesFundsParamDTO parma) {
        CmbChangesFunds funds = new CmbChangesFunds();
        funds.setVersion(0);
        String logKey = CmbUtil.getLogKey(parma.getLogKeyNo(), parma.getAmountChangeType(), parma.getMerchantType());
        funds.setLogKey(logKey);
        funds.setMerchantType(parma.getMerchantType());
        if(MerchantTypeEnum.EMPLOYER.getValue() == parma.getMerchantType()) {
            funds.setMchNo(parma.getMchNo());
            funds.setMchName(parma.getMchName());
        }
        funds.setMainstayNo(parma.getMainstayNo());
        funds.setMainstayName(parma.getMainstayName());
        funds.setPlatTrxNo(parma.getPlatTrxNo());
        funds.setPlatBatchNo(parma.getPlatBatchNo());
        funds.setAmountChangeType(parma.getAmountChangeType());
        funds.setCreateTime(new Date());
        funds.setAmount(parma.getAmount());
        funds.setFrozenAmount(parma.getFrozenAmount());
        return funds;
    }
}
