package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-21 11:19
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class QueryDetailResVo extends ApiBizBaseDto {
    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 收款账户(卡号/支付宝账号)
     */
    private String receiveAccountNo;

    /**
     * 手机号
     */
    private String receivePhoneNo;

    /**
     * 订单明细实发金额
     */
    private String orderItemNetAmount;

    /**
     * 订单明细代征主体服务费
     */
    private String orderItemFee;

    /**
     * 订单明细(总)金额
     */
    private String orderItemAmount;

    /**
     * 订单明细状态
     */
    private Integer orderItemStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date completeTime;

    /**
     * 任务编号
     */
    private String jobNo;

    /**
     * 任务名称
     */
    private String jobName;

    private String memo;
}
