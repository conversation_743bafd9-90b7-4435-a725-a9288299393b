package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.constants.redis.RedisLua;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName DynamicMsgBiz
 * @Description TODO
 * @Date 2022/5/6 15:26
 */
@Service
@Slf4j
public class DynamicMsgBiz {

    @Autowired
    private RedisClient redisClient;

    public void putDynamicMsg(String prefix,String msg){
        String date = DateUtil.formatDate(new Date());
        String key = TradeConstant.DYNAMIC_MSG_KEY + prefix + ":" + date;
        List<String> argv = new ArrayList<>();
        argv.add(msg);
        argv.add(TradeConstant.DYNAMIC_EXPIRE.toString());
        redisClient.evalLua(RedisLua.DYNAMIC_MESSAGE_QUEUE,key,new ArrayList<String>(){{add(key);}},argv);
    }
}
