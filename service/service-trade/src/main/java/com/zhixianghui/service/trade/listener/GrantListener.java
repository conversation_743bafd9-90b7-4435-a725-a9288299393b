package com.zhixianghui.service.trade.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.process.CKHGrantBiz;
import com.zhixianghui.service.trade.process.OfflineOrderGrantHandler;
import com.zhixianghui.service.trade.process.ZXHAcceptBiz;
import com.zhixianghui.service.trade.process.ZXHGrantBiz;
import com.zhixianghui.service.trade.vo.req.SingleGrantReqVo;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName GrantListener
 * @Description TODO
 * @Date 2022/6/30 10:33
 */
@Slf4j
@Component
public class GrantListener {

    @Autowired
    private ZXHGrantBiz zxhGrantBiz;

    @Autowired
    private CKHGrantBiz ckhGrantBiz;

    /**
     * 创客汇
     * 批次发放信息统计
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, selectorExpression = MessageMsgDest.TAG_GRANT_BATCH_COUNT_CKH,consumeThreadMax = 3, consumerGroup = "ckhGrantBatchCount")
    public class CKHGrantBatchCountMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String platBatchNo) {
            if (StringUtil.isEmpty(platBatchNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("受理订单明细遇到业务外异常重发 platBatchNo不能为null");
            }
        }

        @Override
        public void consumeMessage(String platBatchNo) {
            ckhGrantBiz.updateGrantBatchCount(platBatchNo);
        }
    }

    /**
     * 创客汇
     * 发放订单明细遇到业务外异常
     * 重发补偿
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, selectorExpression = MessageMsgDest.TAG_GRANT_NOT_BIZ_EXCEPTION_CKH,consumeThreadMax = 2, consumerGroup = "ckhHandleGrantException")
    public class CKHHandleGrantExceptionMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String platTrxNo) {
            if (StringUtil.isEmpty(platTrxNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发放订单明细遇到业务外异常重发 platTrxNo不能为null");
            }
        }

        @Override
        public void consumeMessage(String platTrxNo) {
            ckhGrantBiz.handleGrantException(platTrxNo);
        }
    }

    /**
     * 创客汇
     * 接收一批订单明细 开始发放
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, selectorExpression = MessageMsgDest.TAG_TRADE_GRANT_CKH,consumeThreadMax = 20, consumerGroup = "ckhGrantStartConsume")
    public class CKHGrantStartMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            log.info("当前线程[{}]", Thread.currentThread().getName());
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单明细列表 List<PlatTrxNo>不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            log.info("接收一批订单开始发放，{}",msg);
            Map<String,String> infoMap = JsonUtil.toBean(msg, new TypeReference<Map<String,String>>(){});
            String platBatchNo = infoMap.get("platBatchNo");
            String employerNo = infoMap.get("employerNo");
            List<String> platTrxNos = JsonUtil.toBean(infoMap.get("platTrxNos"), new TypeReference<List<String>>(){});
            try {
                ckhGrantBiz.startGrant(employerNo,platBatchNo,platTrxNos);
            } catch (Exception e) {
                log.error("[发放环节: {}] -- mchNo:[{}] ==>批次发放异常。platTrxNoList size:{},明细范围[{}]至[{}]"
                        , platBatchNo, employerNo, platTrxNos.size(), platTrxNos.get(0),platTrxNos.get(platTrxNos.size() - 1),e);
                throw e;
            }
        }
    }

    /**
     * 智享汇
     * 接收一批订单明细 开始发放
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC, selectorExpression = MessageMsgDest.TAG_TRADE_GRANT,consumeThreadMax = 20, consumerGroup = "grantStartConsume")
    public class GrantStartMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            log.info("当前线程[{}]", Thread.currentThread().getName());
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单明细列表 List<PlatTrxNo>不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            log.info("接收一批订单开始发放，{}",msg);
            Map<String,String> infoMap = JsonUtil.toBean(msg, new TypeReference<Map<String,String>>(){});
            String platBatchNo = infoMap.get("platBatchNo");
            String employerNo = infoMap.get("employerNo");
            List<String> platTrxNos = JsonUtil.toBean(infoMap.get("platTrxNos"), new TypeReference<List<String>>(){});
            try {
                zxhGrantBiz.startGrant(employerNo,platBatchNo,platTrxNos);
            } catch (Exception e) {
                log.error("[发放环节: {}] -- mchNo:[{}] ==>批次发放异常。platTrxNoList size:{},明细范围[{}]至[{}]"
                        , platBatchNo, employerNo, platTrxNos.size(), platTrxNos.get(0),platTrxNos.get(platTrxNos.size() - 1),e);
                throw e;
            }
        }
    }

    /**
     * 智享汇
     * 发放订单明细遇到业务外异常
     * 重发补偿
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC, selectorExpression = MessageMsgDest.TAG_GRANT_NOT_BIZ_EXCEPTION,consumeThreadMax = 2, consumerGroup = "handleGrantExceptionConsume")
    public class HandleGrantExceptionMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String platTrxNo) {
            if (StringUtil.isEmpty(platTrxNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发放订单明细遇到业务外异常重发 platTrxNo不能为null");
            }
        }

        @Override
        public void consumeMessage(String platTrxNo) {
            zxhGrantBiz.handleGrantException(platTrxNo);
        }
    }

    /**
     * 智享汇
     * 批次发放信息统计
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC, selectorExpression = MessageMsgDest.TAG_GRANT_BATCH_COUNT,consumeThreadMax = 3, consumerGroup = "grantBatchCountConsume")
    public class GrantBatchCountMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String platBatchNo) {
            if (StringUtil.isEmpty(platBatchNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("受理订单明细遇到业务外异常重发 platBatchNo不能为null");
            }
        }

        @Override
        public void consumeMessage(String platBatchNo) {
            zxhGrantBiz.updateGrantBatchCount(platBatchNo);
        }
    }

    /**
     * 创客汇
     * 接收一批订单明细 开始发放
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_CKH_OFFLINE, selectorExpression = MessageMsgDest.TAG_TRADE_CKH_OFFLINE,consumeThreadMax = 20, consumerGroup = "offlineGrantStartConsume")
    public class CKHOfflineGrantStartMessageListener extends BaseRocketMQListener<String> {

        @Autowired
        private OfflineOrderGrantHandler offlineOrderGrantHandler;

        @Override
        public void validateJsonParam(String msg) {
            log.info("当前线程[{}]", Thread.currentThread().getName());
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单明细列表 List<PlatTrxNo>不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            log.info("接收一批订单开始发放，{}",msg);
            Map<String,String> infoMap = JsonUtil.toBean(msg, new TypeReference<Map<String,String>>(){});
            String platBatchNo = infoMap.get("platBatchNo");
            String employerNo = infoMap.get("employerNo");
            String platTrxNo = infoMap.get("platTrxNo");
            try {
                offlineOrderGrantHandler.handleGrant(platTrxNo);
            } catch (Exception e) {
                log.error("[发放环节: {}] -- mchNo:[{}] ==>批次发放异常。platTrxNo:{}]"
                        , platBatchNo, employerNo, platTrxNo,e);
                throw e;
            }
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_SYNC_OUT_ORDER, selectorExpression = MessageMsgDest.TAG_SYNC_OUT_ORDER,consumeThreadMax = 10, consumerGroup = "SyncOutOrderConsume")
    public class SyncOutOrderListenser extends BaseRocketMQListener<String> {

        @Autowired
        private OrderBiz orderBiz;
        @Autowired
        private OrderItemBiz orderItemBiz;
        @Autowired
        private ZXHAcceptBiz zxhAcceptBiz;
        @Reference
        private SequenceFacade sequenceFacade;

        @Override
        public void validateJsonParam(String msg) {
            log.info("当前线程[{}]", Thread.currentThread().getName());
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单明细列表 List<PlatTrxNo>不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            JSONObject jsonObject = JSON.parseObject(msg);
            SingleGrantReqVo item = jsonObject.getObject("item",SingleGrantReqVo.class);
            Merchant merchant = jsonObject.getObject("merchant",Merchant.class);
            Merchant mainstay = jsonObject.getObject("mainstay",Merchant.class);
            MerchantEmployerPosition merchantEmployerPosition = jsonObject.getObject("merchantEmployerPosition",MerchantEmployerPosition.class);
            String signType = jsonObject.getString("signType");
            try {
                Order order = orderBiz.getByMchBatchNo(item.getMchOrderNo(),merchant.getMchNo());
                if (order == null) {
                    order = fillOrder(merchant, mainstay, item.getChannelType(), merchantEmployerPosition, signType);
                    order.setRequestNetAmount(new BigDecimal(item.getOrderItemNetAmount()));
                    order.setRequestTaskAmount(BigDecimal.ZERO);
                    order.setRequestCount(1);
                    order.setBatchStatus(OrderStatusEnum.PENDING_GRANT.getValue());
                    order.setMchBatchNo(item.getMchOrderNo());
                    orderBiz.insert(order);
                }

                OrderItem orderItemDb = orderItemBiz.getByMchOrderNo(order.getEmployerNo(), item.getMchOrderNo(), OrderItemStatusEnum.GRANT_SUCCESS.getValue());
                if (orderItemDb != null) {
                    return;
                }

                OrderItem orderItem = fillOrderItem(order, item, OrderItemStatusEnum.GRANT_SUCCESS.getValue());
                zxhAcceptBiz.calculateFee(orderItem);
                orderItem.setCompleteTime(DateUtil.parseTime(item.getCompleteTime()));
                RecordItem recordItem = this.fillRecordItem(order, orderItem);
                orderBiz.insertItem(orderItem, recordItem);

                Order order1 = orderBiz.getByPlatBatchNo(orderItem.getPlatBatchNo());
                order1.setAcceptedOrderAmount(orderItem.getOrderItemAmount());
                order1.setAcceptedFee(orderItem.getOrderItemFee());
                order1.setAcceptedCount(1);
                order1.setAcceptedNetAmount(orderItem.getOrderItemNetAmount());
                order1.setConfirmTime(new Date());
                order1.setCompleteTime(new Date());
                order1.setBatchStatus(OrderStatusEnum.GRANTING.getValue());
                orderBiz.update(order1);

                zxhGrantBiz.notifyOrderComplete(orderItem, recordItem);

                zxhGrantBiz.updateGrantBatchCount(order1.getPlatBatchNo());
            } catch (Exception e) {
                log.error("同步数据保存出错", e);
            }
        }

        private Order fillOrder(Merchant employer, Merchant mainstay, Integer channelType,MerchantEmployerPosition merchantEmployerPosition,String signType) {
            String batchId = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.ORDER_SEQ.getKey(), SequenceBizKeyEnum.ORDER_SEQ.getWidth());
            Date batchTime = new Date();
            String batchNo = SequenceBizKeyEnum.ORDER_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
            //时间
            Order order = new Order();
            order.setCreateDate(batchTime);
            order.setCreateTime(batchTime);
            order.setUpdateTime(batchTime);
            //账户
            order.setMchBatchNo(batchNo);
            order.setBatchName(String.join("-", DateUtil.formatDate(new Date()),employer.getMchName()));
            order.setPlatBatchNo(batchNo);
            order.setEmployerNo(employer.getMchNo());
            order.setEmployerName(employer.getMchName());
            order.setMainstayNo(mainstay.getMchNo());
            order.setMainstayName(mainstay.getMchName());
            order.setChannelType(channelType);
            order.setPayChannelNo(ChannelNoEnum.OUT_SYNC.name());
            order.setChannelName(ChannelNoEnum.OUT_SYNC.getDesc());
            order.setBatchStatus(OrderStatusEnum.IMPORTING.getValue());
            order.setRequestCount(1);
            order.setRequestNetAmount(BigDecimal.ZERO);
            //任务金额=实发金额
            order.setRequestTaskAmount(BigDecimal.ZERO);
            order.setAcceptedOrderAmount(BigDecimal.ZERO);
            order.setAcceptedCount(1);
            //岗位
            order.setWorkCategoryCode(merchantEmployerPosition.getWorkCategoryCode());
            order.setWorkCategoryName(merchantEmployerPosition.getWorkCategoryName());
            order.setServiceDesc(merchantEmployerPosition.getServiceDesc());
            //api
            order.setLaunchWay(LaunchWayEnum.API.getValue());
            order.setCallbackUrl(null);
            order.getJsonEntity().setSignType(signType);
            //产品
            order.setProductNo(ProductNoEnum.ZXH.getValue());
            order.setProductName(ProductNoEnum.ZXH.getDesc());
            return order;
        }

        private OrderItem fillOrderItem(Order order, SingleGrantReqVo reqVo, Integer status) {
            Date now = new Date();
            OrderItem orderItem = new OrderItem();
            orderItem.setCreateDate(now);
            orderItem.setCreateTime(now);
            orderItem.setUpdateTime(now);
            orderItem.setMchBatchNo(order.getMchBatchNo());
            orderItem.setPlatBatchNo(order.getPlatBatchNo());
            String platTrxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.ORDER_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now),SequenceBizKeyEnum.ORDER_ITEM_SEQ.getKey(),SequenceBizKeyEnum.ORDER_ITEM_SEQ.getWidth());
            orderItem.setMchOrderNo(reqVo.getMchOrderNo());
            orderItem.setPlatTrxNo(platTrxNo);
            orderItem.setLaunchWay(LaunchWayEnum.API.getValue());
            orderItem.setEmployerNo(order.getEmployerNo());
            orderItem.setEmployerName(order.getEmployerName());
            orderItem.setMainstayNo(order.getMainstayNo());
            orderItem.setMainstayName(order.getMainstayName());
            orderItem.setChannelType(order.getChannelType());
            orderItem.setPayChannelNo(order.getPayChannelNo());
            orderItem.setChannelName(order.getChannelName());
            orderItem.setOrderItemNetAmount(new BigDecimal(reqVo.getOrderItemNetAmount()));
            orderItem.setOrderItemTaskAmount(orderItem.getOrderItemNetAmount());
            orderItem.setOrderItemTaxAmount(BigDecimal.ZERO);
            orderItem.setOrderItemStatus(status);
            orderItem.setAccessTimes(0);
            orderItem.setRemark(reqVo.getRemark());
            orderItem.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
            orderItem.setReceiveNameEncrypt(reqVo.getReceiveName());
            orderItem.setReceiveIdCardNoEncrypt(reqVo.getReceiveIdCardNo());
            orderItem.setReceiveAccountNoEncrypt(reqVo.getReceiveAccountNo());
            orderItem.setReceivePhoneNoEncrypt(stringValueOf(reqVo.getReceivePhoneNo()));
            orderItem.setAppid(reqVo.getAppid());
            orderItem.setMemo(reqVo.getMemo());
            orderItem.setProductNo(ProductNoEnum.ZXH.getValue());
            orderItem.setProductName(ProductNoEnum.ZXH.getDesc());
            orderItem.setWorkCategoryCode(order.getWorkCategoryCode());
            orderItem.setWorkCategoryName(order.getWorkCategoryName());
            orderItem.setJobId(order.getJobId());
            orderItem.setJobName(order.getJobName());
            return orderItem;
        }

        public RecordItem fillRecordItem(Order order, OrderItem orderItem) {
            RecordItem recordItem = new RecordItem();
            BeanUtils.copyProperties(orderItem,recordItem);
            Date now = new Date();
            recordItem.setCreateDate(now);
            recordItem.setCreateTime(now);
            recordItem.setUpdateTime(now);
            recordItem.setChannelMchNo(order.getEmployerNo());
            recordItem.setOrderTaskAmount(orderItem.getOrderItemTaskAmount());
            recordItem.setOrderTaxAmount(orderItem.getOrderItemTaxAmount());
            recordItem.setOrderNetAmount(orderItem.getOrderItemNetAmount());
            recordItem.setOrderFee(orderItem.getOrderItemFee());
            recordItem.setOrderAmount(orderItem.getOrderItemAmount());
            recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SUCCESS.getValue());
            recordItem.setWorkCategoryCode(order.getWorkCategoryCode());
            recordItem.setWorkCategoryName(order.getWorkCategoryName());
            recordItem.setCompleteTime(orderItem.getCompleteTime());
            String remitPlatTrxNo = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.RECORD_ITEM_SEQ.getKey(), SequenceBizKeyEnum.RECORD_ITEM_SEQ.getWidth());
            String compRemitPlatTrxNo = SequenceBizKeyEnum.RECORD_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now) + remitPlatTrxNo;
            recordItem.setRemitPlatTrxNo(compRemitPlatTrxNo);
            return recordItem;
        }

        private String stringValueOf(Object obj) {
            return (obj == null) ? " " : obj.toString();
        }
    }
}
