package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.trade.vo.OfflineExcelRow;
import com.zhixianghui.service.trade.biz.OfflineSignBiz;
import com.zhixianghui.service.trade.biz.SignRecordBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName OfflineSignListener
 * @Description TODO
 * @Date 2022/12/14 14:28
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_OFFLINE_SIGNER,
        selectorExpression = MessageMsgDest.TAG_OFFLINE_SIGNER,consumeThreadMax = 10,
        consumerGroup = "offlineSignerConsumer")
public class OfflineSignListener extends BaseRocketMQListener<String> {

    @Autowired
    private OfflineSignBiz offlineSignBiz;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        OfflineExcelRow offlineExcelRow = JsonUtil.toBean(jsonParam,OfflineExcelRow.class);
        offlineSignBiz.offlineRecordHandle(offlineExcelRow);
    }
}
