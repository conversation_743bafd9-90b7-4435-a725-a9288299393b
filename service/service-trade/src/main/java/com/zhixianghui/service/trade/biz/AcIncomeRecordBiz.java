package com.zhixianghui.service.trade.biz;

import cn.hutool.core.date.DateUtil;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.AcIncomeRecord;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.service.trade.dao.AcIncomeRecordDao;
import com.zhixianghui.service.trade.vo.req.RechargeVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * 来账记录信息biz业务实现类
 *
 * <AUTHOR> @date 2024-04-01
 */
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Service
public class AcIncomeRecordBiz {
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    private final AcIncomeRecordDao acIncomeRecordDao;
    private final AcMerchantBalanceBiz acMerchantBalanceBiz;


    //新增来账记录信息
    public void insert(AcIncomeRecord AcIncomeRecord) {
        acIncomeRecordDao.insert(AcIncomeRecord);
    }

    //修改来账记录信息
    public void update(AcIncomeRecord AcIncomeRecord) {
        acIncomeRecordDao.update(AcIncomeRecord);
    }

    //通过id查看来账记录信息
    public AcIncomeRecord getById(Long id) {
        return acIncomeRecordDao.getById(id);
    }

    public List<AcIncomeRecord> listBy(Map<String, Object> paramMap) {
        return acIncomeRecordDao.listBy(paramMap);
    }


    /***
     * 组装redis锁的key值
     * @param payeeAccountNo
     * @param trxNo
     * @return
     */
    private String getRedisLockKey(String payeeAccountNo, String trxNo) {
        return TradeConstant.INCOME_RECORD_REDIS_KEY + "::" + payeeAccountNo + "::" + trxNo;
    }

    /***
     * 处理来账通知的入账流程
     * @param rechargeVo
     */
    public AcMerchantBalance handlerAcIncome(RechargeVo rechargeVo) {
        // 根据来账账号查询商户账号信息
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getOneBySubMerchantNoAndPayChannelNo(rechargeVo.getPayeeAccountNo(),
                ChannelNoEnum.JOINPAY_JXH.name());
        if (employerAccountInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("报价单不存在，不做处理");
        }
        // 封装本地账本的数据
        AcChangeFunds acChangeFunds = buildAcChangeFunds(rechargeVo, employerAccountInfo);
        // 执行入账操作，并返回当前余额
        return acMerchantBalanceBiz.changeAmount(acChangeFunds);
    }

    private AcChangeFunds buildAcChangeFunds(RechargeVo rechargeVo, EmployerAccountInfo employerAccountInfo) {
        AcChangeFunds acChangeFunds = new AcChangeFunds();
        acChangeFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        acChangeFunds.setPlatTrxNo(rechargeVo.getTrxNo());
        acChangeFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.DEPOSIT.getValue());
        Long amount = AmountUtil.changeToFen(rechargeVo.getIncomeAmount());
        acChangeFunds.setAmount(amount);
        acChangeFunds.setMchNo(employerAccountInfo.getEmployerNo());
        acChangeFunds.setMchName(employerAccountInfo.getMchName());
        acChangeFunds.setMainstayNo(employerAccountInfo.getMainstayNo());
        acChangeFunds.setMainstayName(employerAccountInfo.getMainstayName());
        acChangeFunds.setFrozenAmount(0L);
        acChangeFunds.setCreateTime(DateUtil.date());
        acChangeFunds.setOperator("自动入账");
        acChangeFunds.setPayChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
        acChangeFunds.setPayChannelName(ChannelNoEnum.JOINPAY_JXH.getDesc());
        acChangeFunds.setLogKey(AcUtil.getLogKey(acChangeFunds));
        return acChangeFunds;
    }

//    private AcIncomeRecord buildAcIncomeRecord(IncomeResult result, EmployerAccountInfo employerAccountInfo) {
//        AcIncomeRecord acIncome = new AcIncomeRecord();
//        // 实际入账金额
//        acIncome.setIncomeAmount(Convert.toBigDecimal(result.getIncome_amount()));
//        // 交易手续费
//        acIncome.setIncomeFee(Convert.toBigDecimal(result.getIncome_fee()));
//        // 到账金额
//        acIncome.setReceiveAmount(Convert.toBigDecimal(result.getReceive_amount()));
//        acIncome.setIncomeTrxNo(result.getIncome_trx_no());
//        acIncome.setMchName(employerAccountInfo.getMchName());
//        acIncome.setMchNo(employerAccountInfo.getEmployerNo());
//        acIncome.setChannelState(result.getIncome_status());
//        acIncome.setCreateTime(DateUtil.date());
//        acIncome.setPayChannelNo(employerAccountInfo.getPayChannelNo());
//        acIncome.setPayChannelName(employerAccountInfo.getPayChannelName());
//        acIncome.setTrxNo(result.getTrx_no());
//        acIncome.setPayeeAccountName(result.getPayee_account_name());
//        acIncome.setPayeeAccountNo(result.getPayee_account_no());
//        acIncome.setPayerAccountNo(result.getPayer_account_no());
//        acIncome.setPayerAccountName(result.getPayer_account_name());
//        acIncome.setPayerAccountBank(result.getPayer_account_bank());
//        acIncome.setPayTime(Convert.toDate(result.getPay_time()));
//        acIncome.setRemark(result.getRemark());
//        acIncome.setState(IncomeStateEnum.SUCCESS.getValue());
//        return acIncome;
//    }

    private AcIncomeRecord getByTrxNo(String incomeTrxNo) {
        return acIncomeRecordDao.getByTrxNo(incomeTrxNo);
    }
}
