package com.zhixianghui.service.trade.controller.account;

import com.google.common.collect.Maps;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.service.trade.helper.CacheBiz;
import com.zhixianghui.service.trade.vo.req.AccountBalanceReqVo;
import com.zhixianghui.service.trade.vo.req.RechargeInfoReqVo;
import com.zhixianghui.service.trade.vo.res.AccountBalanceResVo;
import com.zhixianghui.service.trade.vo.res.AccountBalanceSubVo;
import com.zhixianghui.service.trade.vo.res.RechargeInfoResVo;
import com.zhixianghui.service.trade.vo.res.RechargeInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 账户对外接口
 * @date 2020-12-16 11:43
 **/
@Slf4j
@RestController
@RequestMapping("zxh")
public class AccountQueryController {
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private PayChannelFacade payChannelFacade;

    @Autowired
    private CacheBiz cacheBiz;

    @PostMapping("/rechargeInfo")
    public ResponseDto rechargeInfo(@RequestBody @Valid RequestDto<RechargeInfoReqVo> paramVo){
        log.info("==>查询入金账户信息：{}",JsonUtil.toString(paramVo));
        String employerNo = paramVo.getMchNo();
        String mainstayNo = paramVo.getData().getMainstayNo();

        //查询代征关系
        EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo,mainstayNo);
        if (relation == null){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("与该代征主体不存在对应代征关系");
        }

        List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(employerNo,mainstayNo);
        List<RechargeInfoVo> rechargeInfoVoList = new ArrayList<>();
        for (EmployerAccountInfo employerAccountInfo : employerAccountInfoList) {
            if (employerAccountInfo.getStatus().intValue() == OpenOffEnum.OFF.getValue()){
                continue;
            }
            RechargeInfoVo rechargeInfoVo = new RechargeInfoVo();
            rechargeInfoVo.setChannelType(employerAccountInfo.getChannelType());
            rechargeInfoVo.setChannelName(ChannelNoEnum.getEnum(employerAccountInfo.getPayChannelNo()).getDesc());
            if (employerAccountInfo.getPayChannelNo().equals(ChannelNoEnum.JOINPAY.name())){
                PayChannel payChannel = payChannelFacade.getByChannelNo(ChannelNoEnum.JOINPAY.name());
                rechargeInfoVo.setBankName(payChannel.getBankName());
                rechargeInfoVo.setSubBankName(payChannel.getSubBankName());
                rechargeInfoVo.setJoinBankNo(payChannel.getJoinBankNo());
                rechargeInfoVo.setBankAddress(payChannel.getBankAddress());
                rechargeInfoVo.setAccountName(employerAccountInfo.getEmployerName());
                rechargeInfoVo.setAccountNo(employerAccountInfo.getSubMerchantNo());
            }else{
                MainstayChannelRelation channelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(mainstayNo,employerAccountInfo.getPayChannelNo());
                rechargeInfoVo.setBankName(channelRelation.getBankName());
                rechargeInfoVo.setSubBankName(channelRelation.getSubBankName());
                rechargeInfoVo.setJoinBankNo(channelRelation.getJoinBankNo());
                rechargeInfoVo.setBankAddress(channelRelation.getBankAddress());
                if (employerAccountInfo.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())){
                    rechargeInfoVo.setAccountNo(channelRelation.getAccountNo());
                    rechargeInfoVo.setAccountName(channelRelation.getAccountName());
                }else if (employerAccountInfo.getPayChannelNo().equals(ChannelNoEnum.CMB.name())){
                    rechargeInfoVo.setAccountNo(channelRelation.getAccountNo() + employerAccountInfo.getSubMerchantNo());
                    rechargeInfoVo.setAccountName(channelRelation.getAccountName());
                }else if (employerAccountInfo.getPayChannelNo().equals(ChannelNoEnum.ALIPAY.name())){
                    rechargeInfoVo.setAccountNo(employerAccountInfo.getAlipayCardNo());
                    rechargeInfoVo.setAccountName(employerAccountInfo.getEmployerName());
                }
            }
            rechargeInfoVoList.add(rechargeInfoVo);
        }

        RechargeInfoResVo rechargeInfoResVo = new RechargeInfoResVo();
        rechargeInfoResVo.setRechargeInfoList(rechargeInfoVoList);
        rechargeInfoResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
        rechargeInfoResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
        ResponseDto<RechargeInfoResVo> responseDto = ResponseDto.success(rechargeInfoResVo, null);
        log.info("==>查询入金账户信息返回：{}", JsonUtil.toString(responseDto));
        return responseDto;
    }

    @PostMapping("/accountBalance")
    public ResponseDto<AccountBalanceResVo> accountBalance(@RequestBody @Valid RequestDto<AccountBalanceReqVo> paramVo){
        log.info("==>查询账户余额信息：{}", JsonUtil.toString(paramVo));
        String employerNo = paramVo.getMchNo();
        String mainstayNoStr = paramVo.getData().getMainstayNo();

        //查询商户的代征主体列表
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
//        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        paramMap.put("mainstayNo",mainstayNoStr);
        List<EmployerMainstayRelation> mainstayNoList = employerMainstayRelationFacade.listBy(paramMap);
        AccountBalanceResVo accountBalanceResVo = new AccountBalanceResVo();
        if(CollectionUtils.isEmpty(mainstayNoList)){
            log.info("{} ==>接口查询余额没有可用账户",employerNo);
        }else {
            log.info("==>查询账户余额信息,{},查询代征主体列表:{}",employerNo,mainstayNoList);
            mainstayNoList.stream().map(EmployerMainstayRelation::getMainstayNo).forEach(
                mainstayNo->{
                    //查询该代征主体下的账户
                    List<EmployerAccountInfo> accountList = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(employerNo,mainstayNo);
                    AccountBalanceSubVo accountBalanceSubVo = new AccountBalanceSubVo();
                    accountBalanceSubVo.setMainstayNo(mainstayNo);
                    accountBalanceSubVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
                    accountBalanceSubVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
                    try {
                        //请求对应开启的通道
                        accountList.stream().filter(x->x.getStatus().equals(OpenOffEnum.OPEN.getValue())).forEach(
                                accountInfo -> {
                                    log.info("==>查询账户余额信息,{},查询代征主体:{},通道编号:{}",employerNo,mainstayNo,accountInfo.getPayChannelNo());
                                    Integer channelType = accountInfo.getChannelType();
                                    AmountQueryDto amountQueryDto = new AmountQueryDto();
                                    amountQueryDto.setMainstayNo(accountInfo.getMainstayNo());
                                    amountQueryDto.setEmployerNo(accountInfo.getEmployerNo());
                                    amountQueryDto.setChannelType(channelType);
                                    amountQueryDto.setChannelNo(accountInfo.getPayChannelNo());
                                    amountQueryDto.setChannelMchNo(accountInfo.getParentMerchantNo());
                                    amountQueryDto.setSubMerchantNo(accountInfo.getSubMerchantNo());
                                    amountQueryDto.setAgreementNo(accountInfo.getSubAgreementNo());
                                    String amount;
                                    try{
                                        amount = accountQueryFacade.getAmount(amountQueryDto);
                                    }catch (Exception e){
                                        log.error("通道:{} 查询异常",accountInfo.getPayChannelNo(),e);
//                                        throw ApiExceptions.API_TRADE_QUERY_BALANCE_ERROR;
                                        amount = "-";
                                    }
                                    if (channelType.equals(ChannelTypeEnum.BANK.getValue())) {
                                        accountBalanceSubVo.setBankCardOpen(true);
                                        accountBalanceSubVo.setBankCardBalance(amount);
                                    }else if (channelType.equals(ChannelTypeEnum.ALIPAY.getValue())){
                                        accountBalanceSubVo.setAlipayOpen(true);
                                        accountBalanceSubVo.setAlipayBalance(amount);
                                    }else if (channelType.equals(ChannelTypeEnum.WENXIN.getValue())){
                                        accountBalanceSubVo.setWxPayOpen(true);
                                        accountBalanceSubVo.setWxPayBalance(amount);
                                    }
                                }
                        );
                    }catch (BizException e){
                        //一个渠道异常 整个代征主体不查询，返回错误码
                        accountBalanceResVo.getAccountList().add(new AccountBalanceSubVo(mainstayNo,e.getApiErrorCode(),e.getErrMsg()));
                        return;
                    }
                    accountBalanceResVo.getAccountList().add(accountBalanceSubVo);
                }
            );
        }
        ResponseDto<AccountBalanceResVo> responseDto = ResponseDto.success(accountBalanceResVo, null);
        log.info("==>查询账户余额信息返回：{}", JsonUtil.toString(responseDto));
        return responseDto;
    }

}
