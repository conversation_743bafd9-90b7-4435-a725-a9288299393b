package com.zhixianghui.service.trade.helper;

import com.zhixianghui.common.statics.dto.account.AccountRequestDto;
import com.zhixianghui.common.statics.dto.account.invoice.AccountInvoiceAmountTypeEnum;
import com.zhixianghui.common.statics.dto.account.invoice.AccountInvoiceProcessDto;
import com.zhixianghui.common.statics.enums.account.AccountProcessTypeEnum;
import com.zhixianghui.common.statics.enums.account.AccountSysTypeEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoicePreStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.AccountInvoiceExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceProcessFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.entity.InvoiceRecordDetail;
import com.zhixianghui.service.trade.biz.InvoicePreRecordBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 账务处理协助类
 *
 * <AUTHOR>
 * @date 2020/12/29
 **/
@Component
@Slf4j
public class AccountHelper {
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private AccountInvoiceProcessFacade accountInvoiceProcessFacade;
    @Autowired
    private InvoicePreRecordBiz invoicePreRecordBiz;

    public void accountInvoiceDebit(InvoiceRecord record) {
        log.info("{} 发票账务进行扣款处理", record.getTrxNo());
        String processNo = sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getWidth());

        AccountRequestDto requestDto = new AccountRequestDto();
        requestDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        requestDto.setAccountProcessNo(processNo);
        requestDto.setUrgent(true);
        requestDto.setMustSuccess(true);

        AccountInvoiceProcessDto processDto = new AccountInvoiceProcessDto();
        processDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        processDto.setTrxTime(record.getCreateTime());
        processDto.setTrxNo(record.getTrxNo());
        processDto.setMchTrxNo(record.getTrxNo());
        processDto.setProcessType(AccountProcessTypeEnum.DEBIT.getValue());
        processDto.setAmountType(AccountInvoiceAmountTypeEnum.INVOICE_AMOUNT.getValue());
        processDto.setEmployerMchNo(record.getEmployerMchNo());
        processDto.setMainstayMchNo(record.getMainstayMchNo());
        processDto.setAmount(record.getInvoiceAmount());
        processDto.setInvoicePreAmount(record.getInvoicePreAmount());
        processDto.setProductNo(ProductNoEnum.INVOICE.name());
        processDto.setDesc("发票开具扣款");
        try {
            accountInvoiceProcessFacade.executeAsync(requestDto, processDto);
        } catch (BizException e) {
            log.error("{} 发票账务调用业务异常：", record.getTrxNo(), e);
            if (e.getSysErrorCode() != AccountInvoiceExceptions.ACCOUNT_PROCESS_PENDING_UNIQUE_KEY_REPEAT.getSysErrorCode()
                    && e.getSysErrorCode() != AccountInvoiceExceptions.ACCOUNT_PROCESS_PENDING_PROCESS_NO_REPEAT.getSysErrorCode()) {
                throw e;
            }
        } catch (Exception e) {
            log.error("{} 发票账务调用系统异常：", record.getTrxNo(), e);
            throw e;
        }
    }

    public void accountInvoiceDetailRefund(InvoiceRecordDetail record) {
        log.info("{} 发票账务进行退回处理", record.getPlatTrxNo());
        String processNo = sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getWidth());

        AccountRequestDto requestDto = new AccountRequestDto();
        requestDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        requestDto.setAccountProcessNo(processNo);
        requestDto.setUrgent(true);
        requestDto.setMustSuccess(true);

        AccountInvoiceProcessDto processDto = new AccountInvoiceProcessDto();
        processDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        processDto.setTrxTime(record.getCreateTime());
        processDto.setTrxNo(record.getInvoiceTrxNo()+record.getPlatTrxNo());
        processDto.setMchTrxNo(record.getInvoiceTrxNo()+record.getPlatTrxNo());
        processDto.setProcessType(AccountProcessTypeEnum.RETURN.getValue());
        processDto.setAmountType(AccountInvoiceAmountTypeEnum.SOURCE_DEBIT_AMOUNT.getValue());
        processDto.setEmployerMchNo(record.getEmployerMchNo());
        processDto.setMainstayMchNo(record.getMainstayMchNo());
        processDto.setAmount(record.getInvoiceAmount());
        processDto.setProductNo(ProductNoEnum.INVOICE.name());
        processDto.setDesc("发票开具失败，金额退回");
        try {
            accountInvoiceProcessFacade.executeAsync(requestDto, processDto);
        } catch (BizException e) {
            log.error("{} 发票账务调用业务异常：", record.getPlatTrxNo(), e);
            if (e.getSysErrorCode() != AccountInvoiceExceptions.ACCOUNT_PROCESS_PENDING_UNIQUE_KEY_REPEAT.getSysErrorCode()
                    && e.getSysErrorCode() != AccountInvoiceExceptions.ACCOUNT_PROCESS_PENDING_PROCESS_NO_REPEAT.getSysErrorCode()) {
                throw e;
            }
        } catch (Exception e) {
            log.error("{} 发票账务调用系统异常：", record.getPlatTrxNo(), e);
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void accountInvoiceRefund(InvoiceRecord record) {
        log.info("{} 发票账务进行退回处理", record.getTrxNo());
        String processNo = sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getWidth());

        AccountRequestDto requestDto = new AccountRequestDto();
        requestDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        requestDto.setAccountProcessNo(processNo);
        requestDto.setUrgent(true);
        requestDto.setMustSuccess(true);

        AccountInvoiceProcessDto processDto = new AccountInvoiceProcessDto();
        processDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        processDto.setTrxTime(record.getCreateTime());
        processDto.setTrxNo(record.getTrxNo());
        processDto.setMchTrxNo(record.getTrxNo());
        processDto.setProcessType(AccountProcessTypeEnum.RETURN.getValue());
        processDto.setAmountType(AccountInvoiceAmountTypeEnum.SOURCE_DEBIT_AMOUNT.getValue());
        processDto.setEmployerMchNo(record.getEmployerMchNo());
        processDto.setMainstayMchNo(record.getMainstayMchNo());
        processDto.setAmount(record.getInvoiceAmount());
        processDto.setProductNo(ProductNoEnum.INVOICE.name());
        // 获取预开票金额 - 待处理状态
        String invoicePreIds = record.getInvoicePreIds();
        if (StringUtils.isNotBlank(invoicePreIds)) {
            List<String> preInvoiceIdList = JsonUtil.toList(invoicePreIds, String.class);
            // 1、如果存在预开票id则查询预开票Ids列表
            List<InvoicePreRecord> preInvoiceRecords = invoicePreRecordBiz.getByIds(preInvoiceIdList);

            // 2、将预开票金额相加
            BigDecimal totalPreInvoiceAmount = preInvoiceRecords.stream()
                    .map(InvoicePreRecord::getInvoiceAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            processDto.setInvoicePreAmount(totalPreInvoiceAmount);

            // 3、更新预开票记录状态到UN_FINNISH
            updateInvoicePreStatus(preInvoiceRecords);

            log.info("处理预开票退回，预开票IDs：{}, 总金额：{}", preInvoiceIdList, totalPreInvoiceAmount);
        } else {
            processDto.setInvoicePreAmount(BigDecimal.ZERO);
        }

        processDto.setDesc("发票开具失败，金额退回");
        try {
            accountInvoiceProcessFacade.executeAsync(requestDto, processDto);
        } catch (BizException e) {
            log.error("{} 发票账务调用业务异常：", record.getTrxNo(), e);
            if (e.getSysErrorCode() != AccountInvoiceExceptions.ACCOUNT_PROCESS_PENDING_UNIQUE_KEY_REPEAT.getSysErrorCode()
                    && e.getSysErrorCode() != AccountInvoiceExceptions.ACCOUNT_PROCESS_PENDING_PROCESS_NO_REPEAT.getSysErrorCode()) {
                throw e;
            }
        } catch (Exception e) {
            log.error("{} 发票账务调用系统异常：", record.getTrxNo(), e);
            throw e;
        }
    }

    /**
     * 更新预开票状态（重载方法，处理预开票记录列表）
     *
     * @param processRecords 处理中的预开票记录列表
     */
    private void updateInvoicePreStatus(List<InvoicePreRecord> processRecords) {
        if (processRecords == null || processRecords.isEmpty()) {
            log.debug("无预开票记录需要更新状态");
            return;
        }

        log.info("开始更新预开票状态为处理中，预开票数量：{}", processRecords.size());

        // 批量更新预开票记录状态为处理中
        processRecords.forEach(preInvoiceRecord -> {
            try {
                // 检查状态是否需要更新
                if (Objects.equals(preInvoiceRecord.getInvoiceStatus(), InvoicePreStatusEnum.PROCESSING.getValue())) {
                    InvoicePreRecord updateRecord = new InvoicePreRecord();
                    updateRecord.setId(preInvoiceRecord.getId());
                    updateRecord.setVersion(preInvoiceRecord.getVersion());
                    updateRecord.setInvoiceStatus(InvoicePreStatusEnum.UN_FINNISH.getValue());

                    invoicePreRecordBiz.updatePreInvoice(updateRecord);
                    log.debug("更新预开票记录状态成功，预开票ID：{}, 状态：{}", preInvoiceRecord.getId(), InvoicePreStatusEnum.UN_FINNISH.getDesc());
                } else {
                    log.debug("预开票记录状态无需更新，ID：{}, 当前状态：{}}", preInvoiceRecord.getId(), InvoicePreStatusEnum.getEnum(preInvoiceRecord.getInvoiceStatus()).getDesc());
                }
            } catch (Exception e) {
                log.error("更新预开票记录状态失败，预开票ID：{}", preInvoiceRecord.getId(), e);
            }
        });

        log.info("完成预开票状态更新为处理中，处理预开票数量：{}", processRecords.size());
    }
}
