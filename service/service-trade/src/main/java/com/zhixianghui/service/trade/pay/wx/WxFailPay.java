package com.zhixianghui.service.trade.pay.wx;

import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月13日 18:20:00
 */
public class WxFailPay extends IWxPay {

    private final Integer Log_type = WxAmountChangeLogTypeEnum.REFUND.getValue();

    public WxFailPay(WxPayParam wxPayParam) {
        super(wxPayParam);
    }


    @Override
    public void handle() {
        buildChangesFunds();
        PayReceiveRespVo payReceiveRespVo = getPayReceiveRespVo();
        wxOrderUpdateBiz.updateOrder(changesFunds,payReceiveRespVo);
    }


    public void updateFounds(){
        buildChangesFunds();
        wxOrderUpdateBiz.updateChangesFunds(changesFunds);
    }

    @Override
    public PayRespVo getResVo(boolean isUpdateFound){
        if (isUpdateFound) {
            updateFounds();
        }
        PayRespVo respVo=new PayRespVo();
        respVo.setBankTrxNo(wxPayParam.getDetailId());
        respVo.setBankOrderNo(wxPayParam.getOutDetailNo());
        respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
        respVo.setBizCode("");
        respVo.setBizMsg(WxUtil.getFailReason(wxPayParam.getFailReason()));
        return respVo;
    }

    private PayReceiveRespVo getPayReceiveRespVo() {
        PayReceiveRespVo payReceiveRespVo = new PayReceiveRespVo();
        payReceiveRespVo.setBankTrxNo(wxPayParam.getDetailId());
        payReceiveRespVo.setBankOrderNo(wxPayParam.getOutDetailNo());
        payReceiveRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
        payReceiveRespVo.setBizCode("");
        payReceiveRespVo.setBizMsg(WxUtil.getFailReason(wxPayParam.getFailReason()));
        return payReceiveRespVo;
    }



    public void buildChangesFunds() {
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(wxPayParam.getPlatTrxNo());
        String logKey = WxUtil.getLogKey(wxPayParam.getPlatTrxNo(), Log_type, MerchantTypeEnum.EMPLOYER);
        ChangesFunds changesFunds = new ChangesFunds();
        changesFunds.setMchNo(wxPayParam.getEmployerNo());
        changesFunds.setMchName(wxPayParam.getMchName());
        changesFunds.setMainstayNo(wxPayParam.getMainstayNo());
        changesFunds.setMainstayName(wxPayParam.getRealPayerName());
        changesFunds.setPlatTrxNo(wxPayParam.getPlatTrxNo());
        changesFunds.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        changesFunds.setCreateTime(new Date());
        changesFunds.setLogKey(logKey);
        changesFunds.setAmountChangeType(Log_type);
        changesFunds.setAmount(0L);
        changesFunds.setFrozenAmount(-AmountUtil.changeToFen(orderItem.getOrderItemAmount()));
        this.changesFunds = changesFunds;
    }

}