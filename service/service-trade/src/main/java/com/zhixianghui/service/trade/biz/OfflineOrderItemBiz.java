package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.zhixianghui.common.service.utils.MybatisUtil;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.trade.bo.OrderItemCountBo;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.vo.AuthInfoVo;
import com.zhixianghui.service.trade.dao.mapper.OfflineOrderItemMapper;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 订单明细表Biz
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OfflineOrderItemBiz {

    private final OfflineOrderItemMapper orderItemMapper;
    private final TradeHelperBiz tradeHelperBiz;
    private final UserInfoBiz userInfoBiz;
    private final SignRecordBiz signRecordBiz;

    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<OfflineOrderItem> itemList) {
        if (ObjectUtils.isEmpty(itemList)) {
            return;
        }
        itemList.forEach(orderItem -> {
            orderItemMapper.insert(orderItem);
        });
    }


    public OfflineOrderItem getByPlatTrxNo(String platTrxNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platTrxNo", platTrxNo);

        QueryWrapper<OfflineOrderItem> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper,OfflineOrderItem.class,paramMap);
        return orderItemMapper.selectOne(queryWrapper);
    }

    public void update(OfflineOrderItem item) {
        orderItemMapper.updateById(item);
    }

    public OrderItemCountBo getCountBoByPlatBatchNo(String platBatchNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", platBatchNo);
        return orderItemMapper.getCountBoByPlatBatchNo(paramMap);
    }

    public Page<OfflineOrderItem> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            final Page page = new Page(pageParam.getPageCurrent(), pageParam.getPageSize());
            page.setRecords(new ArrayList());
            return page;
        }
        tradeHelperBiz.replacePrivacy(paramMap);

        QueryWrapper<OfflineOrderItem> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, OfflineOrderItem.class, paramMap);
        OfflineOrderItem.extendWhere(queryWrapper, paramMap);

        final Page<OfflineOrderItem> orderItemPage = orderItemMapper.selectPage(new Page<>(pageParam.getPageCurrent(), pageParam.getPageSize()), queryWrapper);
        return orderItemPage;
    }

    public List<OfflineOrderItem> listBy(Map<String,Object> paramMap) {
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return new ArrayList<>();
        }
        tradeHelperBiz.replacePrivacy(paramMap);

        QueryWrapper<OfflineOrderItem> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, OfflineOrderItem.class, paramMap);
        OfflineOrderItem.extendWhere(queryWrapper, paramMap);
        return orderItemMapper.selectList(queryWrapper);
    }

    public Integer countOrderItem(Map<String, Object> paramMap) {
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return 0;
        }
        tradeHelperBiz.replacePrivacy(paramMap);


        log.info("查询订单详情记录数入参:{}", JSON.toJSONString(paramMap));
        QueryWrapper<OfflineOrderItem> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, OfflineOrderItem.class, paramMap);
        OfflineOrderItem.extendWhere(queryWrapper, paramMap);
        return orderItemMapper.selectCount(queryWrapper);
    }

    public OrderItemSumBo sumOrderItem(Map<String, Object> paramMap) {
        if (paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null) {
            return new OrderItemSumBo();
        }
        tradeHelperBiz.replacePrivacy(paramMap);
        return orderItemMapper.sumOrderItem(paramMap);
    }


    public Long countOrder(Map<String, Object> param) {
        return orderItemMapper.countOrder(param);
    }


    public BigDecimal sumOrderItemWaitInvoiceAmount(Map<String, Object> paramMap) {
        return orderItemMapper.sumOrderItemWaitInvoiceAmount(paramMap);
    }


    public void authInit(String platBatchNo) {
        log.info("[{}]加载认证信息",platBatchNo);
        Map<String, Object> map = new HashMap<>();
        map.put("platBatchNo", platBatchNo);
        List<OfflineOrderItem> orderItems = this.listBy(map);
        orderItems.forEach(this::getAuthInfo);
    }

    public AuthInfoVo getAuthInfo(OfflineOrderItem orderItem) {
        AuthInfoVo authInfoVo = new AuthInfoVo();
        if (userInfoBiz.isVerified(orderItem.getReceiveIdCardNoMd5())) {
            authInfoVo.setAuth(AuthStatusEnum.SUCCESS.getValue());
        }else {
            authInfoVo.setAuth(AuthStatusEnum.UN_AUTH.getValue());
        }

        if (signRecordBiz.isSigned(orderItem.getMainstayNo(), orderItem.getEmployerNo(), orderItem.getReceiveIdCardNoMd5())) {
            authInfoVo.setSign(SignStatusEnum.SIGN_SUCCESS.getValue());
        }else {
            authInfoVo.setSign(SignStatusEnum.UN_SIGN.getValue());
        }
        return authInfoVo;
    }

    public List<String> listPlatTrxNoByBatchNo(Map<String,Object> paramMap) {
        return orderItemMapper.listPlatTrxNoByBatchNo(paramMap);
    }

    public void cancelOrderItem(String platBatchNo){
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", platBatchNo);
        orderItemMapper.cancelOrderItem(paramMap);
    }

    public AuthInfoVo getAuthInfo(OrderItem orderItem) {
        AuthInfoVo authInfoVo = new AuthInfoVo();
        if (userInfoBiz.isVerified(orderItem.getReceiveIdCardNoMd5())) {
            authInfoVo.setAuth(AuthStatusEnum.SUCCESS.getValue());
        }else {
            authInfoVo.setAuth(AuthStatusEnum.UN_AUTH.getValue());
        }

        if (signRecordBiz.isSigned(orderItem.getMainstayNo(), orderItem.getEmployerNo(), orderItem.getReceiveIdCardNoMd5())) {
            authInfoVo.setSign(SignStatusEnum.SIGN_SUCCESS.getValue());
        }else {
            authInfoVo.setSign(SignStatusEnum.UN_SIGN.getValue());
        }
        return authInfoVo;
    }

    public void deleteOrderItem(String platTrxNo) {
        orderItemMapper.delete(new QueryWrapper<OfflineOrderItem>().eq(OfflineOrderItem.COL_PLAT_TRX_NO, platTrxNo));
    }

    public void update2Hangup(OfflineOrderItem orderItem, RiskControlResult riskControlResult) {
        orderItem.setUpdateTime(new Date());
        orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_HANG.getValue());

        if (StringUtils.isNotEmpty(riskControlResult.getErrMsg())) {
            orderItem.setErrorCode(String.valueOf(ApiExceptions.API_TRADE_RISK_HANG.getApiErrorCode()));
            orderItem.setErrorDesc(riskControlResult.getErrMsg());
        }
        JSONObject json = new JSONObject();
        json.put("risk_type",riskControlResult.getControlType());
        orderItem.setJsonStr(json.toJSONString());
        this.update(orderItem);
    }

    public void update2Reject(OfflineOrderItem orderItem, RiskControlResult riskControlResult) {
        Date now = new Date();
        orderItem.setUpdateTime(now);
        orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
        orderItem.setCompleteTime(now);
        if (StringUtils.isNotEmpty(riskControlResult.getErrMsg())) {
            orderItem.setErrorCode(String.valueOf(ApiExceptions.API_TRADE_RISK_REJECT.getApiErrorCode()));
            orderItem.setErrorDesc(riskControlResult.getErrMsg());
        }
        JSONObject json = new JSONObject();
        json.put("risk_type",riskControlResult.getControlType());
        orderItem.setJsonStr(json.toJSONString());
        this.update(orderItem);
    }

    public List<OfflineOrderItem> MapperListBy(Map<String, Object> params) {
        return orderItemMapper.listBy(params);
    }
}
