package com.zhixianghui.service.trade.pay.jxh.annotations;

import com.zhixianghui.facade.banklink.vo.jxh.ResStatus;
import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Service;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Service
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface JXHService {

    @AliasFor(annotation = Service.class)
    String value() default "";

    ResStatus type();
}
