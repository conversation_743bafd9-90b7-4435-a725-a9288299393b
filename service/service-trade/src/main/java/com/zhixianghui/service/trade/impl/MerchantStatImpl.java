package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.trade.entity.MerchantStat;
import com.zhixianghui.facade.trade.service.MerchantStatFacade;
import com.zhixianghui.facade.trade.vo.MerchantStatVo;
import com.zhixianghui.service.trade.biz.MerchantStatBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 商户月统计表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-08-02
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantStatImpl implements MerchantStatFacade {

    private final MerchantStatBiz biz;

    @Reference
    private MerchantFacade merchantFacade;

    @Override
    public List<MerchantStat> merchantStat(Map<String, Object> param) {
        return biz.merchantStat(param);
    }


    public PageResult<List<MerchantStat>> merchantStat(Map<String, Object> param, PageParam pageParam) {
        return biz.merchantStat(param, pageParam);
    }

    @Override
    public MerchantStatVo count(Map<String, Object> param) {
        return biz.count(param);
    }

    @Override
    public void insert(List<MerchantStat> result) {
        biz.insert(result);
    }

    @Override
    public List<MerchantStatVo> groupBySaleId(Map<String, Object> saleIds) {
        return biz.groupBySaleId(saleIds);
    }

    @Override
    public List<MerchantStatVo> list(Map<String, Object> param, PageParam pageParam) {
        PageResult<List<MerchantStat>> list = merchantStat(param, pageParam);
        if (list == null || CollectionUtils.isEmpty(list.getData())) {
            return null;
        }
        List<MerchantStatVo> result = new ArrayList<>();

        for (MerchantStat stat : list.getData()) {
            MerchantStatVo statVo = new MerchantStatVo();
            BeanUtil.copyProperties(stat, statVo);

            //查询合伙人
            final Merchant merchant = merchantFacade.getByMchNo(stat.getEmployerNo());
            statVo.setAgentName(merchant.getAgentName());
            result.add(statVo);
        }
        return result;
    }
}
