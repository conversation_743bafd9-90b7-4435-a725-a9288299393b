package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.dto.AcMerchantBalanceAddDto;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.dto.MainstayAdjustmentDTO;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.service.AcMerchantBalanceFacade;
import com.zhixianghui.service.trade.biz.AcLocalPayBiz;
import com.zhixianghui.service.trade.biz.AcMerchantBalanceBiz;
import com.zhixianghui.service.trade.pay.jxh.biz.JxhNotifyBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class AcMerchantBalanceFacadeImpl implements AcMerchantBalanceFacade {
    @Autowired
    private AcMerchantBalanceBiz acMerchantBalanceBiz;
    @Autowired
    private AcLocalPayBiz acLocalPayBiz;
    @Autowired
    private JxhNotifyBiz jxhNotifyBiz;

    @Override
    public boolean createMerchantBalance(AcMerchantBalanceAddDto dto) {
        return acMerchantBalanceBiz.createMerchantBalance(dto);
    }

    @Override
    public Long getAmount(AcMerchantBalance acMerchantBalance) {
        return acMerchantBalanceBiz.getAmount(acMerchantBalance);
    }

    @Override
    public void mainstayBalanceSyncOne(String mainstayNo) {
        acLocalPayBiz.mainstayBalanceSyncOne(mainstayNo);
    }

    //调账
    public void adjustment(AdjustmentDTO adjustmentDTO) {
        jxhNotifyBiz.adjustment(adjustmentDTO);
    }

}
