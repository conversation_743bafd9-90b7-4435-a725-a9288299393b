package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.trade.dto.FeeOrderBatchVo;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.entity.FeeOrderItem;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.vo.FeeOrderVo;
import com.zhixianghui.service.trade.biz.FeeOrderBatchBiz;
import com.zhixianghui.service.trade.biz.FeePayBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-07-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FeeOrderBatchImpl implements FeeOrderBatchFacade {

    private final FeeOrderBatchBiz biz;

    private final FeePayBiz feePayBiz;

    @Override
    public PageResult listPage(FeeOrderVo feeOrderVo, PageParam pageParam) {
        return biz.listPage(feeOrderVo,pageParam);
    }

    @Override
    public Map<String, Object> getStatistics(FeeOrderVo feeOrderVo) {
        return biz.getStatistics(feeOrderVo);
    }

    @Override
    public FeeOrderBatchVo selectOrderItem(String feeBatchNo) {
        return biz.selectOrderItem(feeBatchNo);
    }

    @Override
    public Map<String, Object> payFee(String feeItemNo) {
        return BeanUtil.toMap(feePayBiz.payFee(feeItemNo,Boolean.FALSE));
    }

    @Override
    public FeeOrderItem getByItemNo(String feeItemNo) {
        return biz.getByItemNo(feeItemNo);
    }

    @Override
    public Map<String, Object> getOffLineItem(String feeBatchNo) {
        return biz.getOffLineItem(feeBatchNo);
    }

    @Override
    public void complete(FeeOrderBatchVo feeOrderBatchVo) {
        biz.complete(feeOrderBatchVo);
    }

    @Override
    public FeeOrderBatch getByBatchNo(String feeBatchNo) {
        return biz.getByBatchNo(feeBatchNo);
    }

    @Override
    public List<Map<String, Object>> export(Map<String, Object> param) {
        return biz.export(param);
    }

    @Override
    public boolean isExistNotPay(Map<String, Object> paramMap) {
        return biz.isExistNotPay(paramMap);
    }

    @Override
    public void update(FeeOrderBatch feeOrderBatch) {
        biz.update(feeOrderBatch);
    }

    @Override
    public boolean isGenerateFee(String mchNo, String mainstayNo, Date completeDate, Integer feeSource) {
        return biz.isGenerateFee(mchNo,mainstayNo,completeDate,feeSource);
    }

    @Override
    public boolean hasNotpayFee(String startTime, String endTime, String employerNo, String mainstayNo, Integer feeSource) {
        return biz.hasNotpayFee(startTime, endTime, employerNo, mainstayNo, feeSource);
    }
}
