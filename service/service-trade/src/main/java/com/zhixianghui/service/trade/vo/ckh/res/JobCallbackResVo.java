package com.zhixianghui.service.trade.vo.ckh.res;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName JobCallbackResVo
 * @Description TODO
 * @Date 2022/11/3 11:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JSONType(naming= PropertyNamingStrategy.SnakeCase)
public class JobCallbackResVo extends ApiBizBaseDto {

    private String jobNo;

    private String jobStatus;
}
