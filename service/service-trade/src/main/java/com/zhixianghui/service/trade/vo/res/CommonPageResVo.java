package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class CommonPageResVo<T> extends ApiBizBaseDto {

    public CommonPageResVo(Integer currentPage,Long totalRecord,Integer pageSize) {
        this.setPage(new PageInfo(currentPage, totalRecord, pageSize));
    }

    private List<T> list;
    private PageInfo page;

}
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
class PageInfo{
    private Integer currentPage;
    private Long totalRecord;
    private Integer pageSize;
}
