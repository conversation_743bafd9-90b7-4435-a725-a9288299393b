package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.dto.AuthReqDto;
import com.zhixianghui.facade.trade.service.AuthRecordFacade;
import com.zhixianghui.facade.trade.vo.auth.AuthResponseVo;
import com.zhixianghui.service.trade.biz.AuthBiz;
import com.zhixianghui.service.trade.biz.AuthRecordBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 鉴权成功记录表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-06
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthRecordImpl implements AuthRecordFacade {

    private final AuthRecordBiz biz;
    private final AuthBiz authBiz;

    @Override
    public AuthResponseVo auth(AuthReqDto reqDto) {
        return authBiz.auth(reqDto.getName(),reqDto.getIdCardNo(),reqDto.getPhoneNo(), reqDto.getBankAccountNo(), reqDto.getAuthType(),reqDto.getAuthChannel());
    }
}
