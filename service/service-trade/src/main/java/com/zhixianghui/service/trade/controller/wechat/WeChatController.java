package com.zhixianghui.service.trade.controller.wechat;


import cn.hutool.core.lang.Assert;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.banklink.vo.ocr.IdcardOcrVo;
import com.zhixianghui.common.util.utils.IDCardUtils;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.banklink.service.ocr.OcrFacade;
import com.zhixianghui.facade.banklink.vo.ocr.OcrRequestVo;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.vo.PortalDataDictionaryQueryVO;
import com.zhixianghui.facade.employee.annotation.CurrentLoginVo;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.entity.WechatInfo;
import com.zhixianghui.facade.trade.entity.WechatUserInfo;
import com.zhixianghui.facade.trade.vo.SignRecordVo;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.facade.trade.vo.WeChatReceiptVo;
import com.zhixianghui.service.trade.biz.UserInfoBiz;
import com.zhixianghui.service.trade.biz.WeChatUserBiz;
import com.zhixianghui.service.trade.enums.VerifyStatusEnum;
import com.zhixianghui.service.trade.helper.CaptchaHelper;
import com.zhixianghui.service.trade.helper.JwtHelper;
import com.zhixianghui.service.trade.vo.VerifyInfo;
import com.zhixianghui.service.trade.vo.req.LoginReqVo;
import com.zhixianghui.service.trade.vo.res.LoginResVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

import static com.zhixianghui.service.trade.enums.PermissionConstant.REQUEST_TOKEN_HEADER;

/**
 * <AUTHOR>
 * @date 2022/2/14 10:00
 */
@RestController
@RequestMapping("weChat")
public class WeChatController {
    private static final Logger LOGGER = LoggerFactory.getLogger(WeChatController.class);

    @Autowired
    private CaptchaHelper captchaHelper;
    @Autowired
    private JwtHelper jwtHelper;
    @Autowired
    private UserInfoBiz userInfoBiz;
    @Autowired
    private WeChatUserBiz weChatUserBiz;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private OcrFacade ocrFacade;

    @Value("${wx.sign.env}")
    private String env;


    private String parseToken(HttpServletRequest request) {
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        if (StringUtils.isBlank(token)) {
            return null;
        }
        return jwtHelper.parseToken(token);
    }


    @PostMapping("sendSmsCode")
    public RestResult<String> sendSmsCode(@RequestBody LoginReqVo loginReqVO, HttpServletRequest request) {
        if ("dev".equals(env)) {
            return RestResult.success("短信验证码发送成功");
        }
        Assert.notNull(loginReqVO.getPhone(), "手机号码不能为空");
//        if (weChatUserBiz.isSign(loginReqVO.getPhone())) {
//            return RestResult.error("该手机号码不是签约的手机号码或信息认证不匹配");
//        }
        if (parseToken(request) != null) {
            return RestResult.success("当前用户已经登录,请勿重复发送");
        }
        try {
            captchaHelper.sendSmsCode(loginReqVO.getPhone());
        } catch (BizException e) {
            return RestResult.error(e.getErrMsg());
        }
        return RestResult.success("短信验证码发送成功");
    }

    @PostMapping("getUserInfo")
    public RestResult<LoginResVo> getUserInfo(HttpServletRequest request) {
        String userNo = parseToken(request);
        if (StringUtils.isBlank(userNo)) {
            return RestResult.error("token不合法");
        }
        WechatUserInfo userInfo = weChatUserBiz.selectByUserNo(userNo);
        if (userInfo == null) {
            return RestResult.error("用户未登录");
        }
        return RestResult.success(getUserInfo(userInfo));
    }


    @PostMapping("login")
    public RestResult<LoginResVo> login(HttpServletRequest request, @RequestBody @Valid LoginReqVo loginReqVO) {
        if (!"dev".equals(env)) {
            try {
                captchaHelper.verifySmsCode(loginReqVO.getPhone(), loginReqVO.getCode());
            } catch (BizException e) {
                return RestResult.error(e.getErrMsg());
            }
        }
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        if (StringUtils.isNotBlank(token)) {
            WechatUserInfo userInfo = weChatUserBiz.selectByUserNo(parseToken(request));
            LoginResVo resVo = getUserInfo(userInfo).setAccessToken(token);
            return RestResult.success(resVo);
        }
        if (StringUtils.isBlank(loginReqVO.getLoginFlag())) {
            return RestResult.error("小程序未授权");
        }
        WechatInfo wechatInfo = weChatUserBiz.selectWeChatInfo(loginReqVO.getLoginFlag());
        if (wechatInfo == null) {
            return RestResult.error("小程序未授权");
        }
        WechatUserInfo userInfo = weChatUserBiz.register(loginReqVO.getPhone(), loginReqVO.getLoginFlag());
        LoginResVo resVo = getUserInfo(userInfo).setAccessToken(jwtHelper.genToken(userInfo.getUserNo()));
        weChatUserBiz.setRedisToken(userInfo,resVo.getAccessToken());
        return RestResult.success(resVo);
    }

    @PostMapping("loginOut")
    public RestResult<String> loginOut(HttpServletResponse response, HttpServletRequest request) {
        response.setHeader(REQUEST_TOKEN_HEADER, "");
        return RestResult.success("退出登录");
    }

    @PostMapping("auth")
    public RestResult<?> auth(@RequestBody LoginReqVo loginReqVO, HttpServletRequest request) {
        Assert.notNull(loginReqVO.getCode(), "微信code不能为空");
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        if (StringUtils.isNotBlank(token)) {
            LOGGER.info("该用户token不为空, 可能已授权过, 不做重复授权");
            LoginResVo loginResVo = getUserInfo(weChatUserBiz.selectByUserNo(parseToken(request)));
            loginResVo.setAccessToken(token);
            return RestResult.success(loginResVo);
        }
        if (!weChatUserBiz.authRegister(loginReqVO)) {
            return RestResult.error("微信授权失败");
        }
        LoginResVo loginResVo = new LoginResVo().setLoginFlag(loginReqVO.getUserNo());
        return RestResult.success(loginResVo);

    }

    @PostMapping("phone")
    public RestResult<?> phone(@RequestBody LoginReqVo loginReqVO, HttpServletRequest request) {
        if (StringUtils.isAnyBlank(loginReqVO.getIv(), loginReqVO.getEncryptedData())) {
            return RestResult.error("缺少参数信息");
        }
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        if (StringUtils.isNotBlank(token)) {
            WechatUserInfo userInfo = weChatUserBiz.selectByUserNo(parseToken(request));
            LoginResVo resVo = getUserInfo(userInfo).setAccessToken(token);
            return RestResult.success(resVo);
        }
        if (StringUtils.isBlank(loginReqVO.getLoginFlag())) {
            return RestResult.error("小程序未授权");
        }
        WechatInfo wechatInfo = weChatUserBiz.selectWeChatInfo(loginReqVO.getLoginFlag());
        if (wechatInfo == null) {
            return RestResult.error("小程序未授权");
        }
        WechatUserInfo userInfo = weChatUserBiz.registerByWeChatPhone(loginReqVO, loginReqVO.getLoginFlag());
        if (userInfo == null) {
            return RestResult.error("该功能暂为开放, 敬请期待");
        }
        LoginResVo loginResVo = getUserInfo(userInfo).setAccessToken(jwtHelper.genToken(userInfo.getUserNo()));
        weChatUserBiz.setRedisToken(userInfo,loginResVo.getAccessToken());
        return RestResult.success(loginResVo);



    }

    private LoginResVo getUserInfo(WechatUserInfo userInfo) {
        Integer toSign = weChatUserBiz.isSign(userInfo);
        PortalDataDictionaryQueryVO portalDataDictionaryQueryVO=new PortalDataDictionaryQueryVO();
        portalDataDictionaryQueryVO.setSystemType(SystemTypeEnum.CKH_MANAGEMENT.getValue());
        List<DataDictionary> list = dataDictionaryFacade.list(portalDataDictionaryQueryVO);
        LOGGER.info("查询创客汇字典:{}",list);
        LoginResVo loginResVo = new LoginResVo().
                setToSign(toSign).
                setPhone(userInfo.getMobile());
        loginResVo.setDict(list);
        if (StringUtils.isNotBlank(userInfo.getReceiveIdCardNoMd5())) {
            UserInfo info = userInfoBiz.getByIdCardNoMd5(userInfo.getReceiveIdCardNoMd5());
            if (info != null) {
                if (StringUtils.isNoneBlank(info.getIdCardFrontUrl(), info.getIdCardBackUrl()) || StringUtils.isNotBlank(info.getIdCardCopyUrl())) {
                    loginResVo.setVerify(true);
                }
                loginResVo.setName(info.getReceiveNameDecrypt());
                loginResVo.setIdcard(userInfo.getReceiveIdCardDecrypt());
                return loginResVo;
            }
        }
        loginResVo.setVerify(false);
        SignRecord signRecord = weChatUserBiz.fetch(userInfo);
        if (signRecord == null) {
            loginResVo.setName("*");
        } else {
            loginResVo.setName(signRecord.getReceiveNameDecrypt());
        }
        return loginResVo;
    }

    @PostMapping("getIdCard")
    public RestResult<VerifyInfo> getIdCard(HttpServletRequest request) {
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        return RestResult.success(weChatUserBiz.getIdCard(jwtHelper.parseToken(token)));
    }

    @PostMapping("verify")
    public RestResult<VerifyInfo> verify(@RequestBody VerifyInfo verifyInfo, HttpServletRequest request) {
        LimitUtil.notEmpty(verifyInfo.getIdCardBackUrl(), "身份证背面地址不能为空");
        LimitUtil.notEmpty(verifyInfo.getIdCardFrontUrl(), "身份证正面地址不能为空");
        if (!IDCardUtils.verifi(verifyInfo.getIdCardNo())) {
            return RestResult.success(new VerifyInfo().setVerifyStatus(VerifyStatusEnum.UN_VERIFIED.getStatus()));
        }
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        if (!weChatUserBiz.verify(verifyInfo, jwtHelper.parseToken(token))) {
            return RestResult.success(new VerifyInfo().setVerifyStatus(verifyInfo.getVerifyStatus()));
        }
        return RestResult.success(new VerifyInfo().setVerifyStatus(VerifyStatusEnum.VERIFIED.getStatus()));
    }

    @PostMapping("toSign")
    public RestResult<PageResult<List<SignRecordVo>>> toSign(HttpServletRequest request, @RequestBody PageParam pageParam) {
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        String userNo = jwtHelper.parseToken(token);
        return RestResult.success(weChatUserBiz.listPage(userNo, pageParam, SignStatusEnum.WAIT_SIGN.getValue()));
    }

    @PostMapping("hasSign")
    public RestResult<PageResult<List<SignRecordVo>>> hasSign(HttpServletRequest request, @RequestBody PageParam pageParam) {
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        String userNo = jwtHelper.parseToken(token);
        return RestResult.success(weChatUserBiz.listPage(userNo, pageParam, SignStatusEnum.SIGN_SUCCESS.getValue()));
    }

    @PostMapping("listSign")
    public RestResult<PageResult<List<SignRecordVo>>> listSign(HttpServletRequest request, @RequestBody PageParam pageParam) {
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        String userNo = jwtHelper.parseToken(token);
        return RestResult.success(weChatUserBiz.listPage(userNo, pageParam, null));
    }


    @PostMapping("idCardOcr")
    public RestResult<IdcardOcrVo> idcardOcr(@RequestBody OcrRequestVo requestVo,HttpServletRequest request) {
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        String userNo = jwtHelper.parseToken(token);
        requestVo.setMiniUserNo(userNo);
        IdcardOcrVo idCardFontOcr = ocrFacade.idCardFontOcr(requestVo);
        return RestResult.success(idCardFontOcr);
    }

    @PostMapping("getIncome")
    public RestResult getIncome(HttpServletRequest request){
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        String userNo = jwtHelper.parseToken(token);
        return RestResult.success(weChatUserBiz.getUserIncome(userNo));
    }

    @PostMapping("getRecordItem/{remitPlatTrxNo}")
    public RestResult getRecordItem(@PathVariable String remitPlatTrxNo,HttpServletRequest request){
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        String userNo = jwtHelper.parseToken(token);
        return RestResult.success(weChatUserBiz.getRecordItem(userNo,remitPlatTrxNo));
    }

    @PostMapping("getReceipt")
    public RestResult getReceipt(@Valid @RequestBody WeChatReceiptVo weChatReceiptVo,HttpServletRequest request){
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        String userNo = jwtHelper.parseToken(token);
        weChatUserBiz.getReceipt(userNo,weChatReceiptVo);
        return RestResult.success("已发起电子回单申请，稍后请查看邮箱");
    }

}
