package com.zhixianghui.service.trade.pay.jxh.biz;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024年5月21日 10:06:00
 */
@Service
@Slf4j
public class JxhNotifyBiz {

    @Reference
    private NotifyFacade notifyFacade;

    public void adjustment(AdjustmentDTO adjustmentDTO){
        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_PAY_ADJUSTMENT, UUIDUitl.generateString(10),
                adjustmentDTO.getLogKey(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_JXH_PAY_ADJUSTMENT,
                JsonUtil.toString(adjustmentDTO),MsgDelayLevelEnum.S_1.getValue());
    }

}