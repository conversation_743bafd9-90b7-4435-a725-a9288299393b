package com.zhixianghui.service.trade.process;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.enums.common.SuccessFailCodeEnum;
import com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignFlowStatusEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.OperateStatusEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.sign.ChannelSignFacade;
import com.zhixianghui.facade.banklink.service.yishui.YishuiFacade;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.sign.AccountCreateResponse;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.SignReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDateVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.*;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.facade.banklink.vo.yishui.YiResponse;
import com.zhixianghui.facade.banklink.vo.yishui.YishuiAddEmpVo;
import com.zhixianghui.facade.banklink.vo.yishui.YishuiContractInfoVo;
import com.zhixianghui.facade.banklink.vo.yishui.YishuiContractListVo;
import com.zhixianghui.facade.banklink.vo.yishui.req.*;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.SignTemplate;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.vo.CreateSignReqVo;
import com.zhixianghui.facade.trade.vo.PreSignReqVo;
import com.zhixianghui.facade.trade.vo.SignFileVo;
import com.zhixianghui.facade.trade.vo.SignRecordVo;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import com.zhixianghui.facade.trade.vo.sign.StructContext;
import com.zhixianghui.facade.trade.vo.sign.StructPos;
import com.zhixianghui.service.trade.biz.SignRecordBiz;
import com.zhixianghui.service.trade.biz.SignTemplateBiz;
import com.zhixianghui.service.trade.biz.UserInfoBiz;
import com.zhixianghui.service.trade.biz.YishuiUsersBiz;
import com.zhixianghui.service.trade.config.RetryConfig;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.service.trade.helper.TradeNotifyBiz;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import com.zhixianghui.service.trade.utils.ESignUtil;
import com.zhixianghui.service.trade.vo.res.PreSignResVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 签约
 * @date 2021/1/15 16:19
 **/
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SignBiz extends AbstractSign {

    private final SignRecordBiz signRecordBiz;

    private final SignTemplateBiz signTemplateBiz;
    private final RedisClient redisClient;
    private final TradeHelperBiz tradeHelperBiz;
    private final TradeNotifyBiz tradeNotifyBiz;
    private final RedisLock redisLock;
    private final ZxhLimitBiz limitBiz;
    private final FastdfsClient fastdfsClient;
    private final YishuiUsersBiz yishuiUsersBiz;

    @Reference
    private VendorFacade vendorFacade;

    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    private static final int LIMIT_SEND_VALUE = 3;
    @Reference
    private ChannelSignFacade channelSignFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;
    @Reference
    private YishuiFacade yishuiFacade;
    @Reference
    private EmployerMainstayRelationFacade relationFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Autowired
    private UserInfoBiz userInfoBiz;

    //    @Value("${sign.templateId}")
//    private String templateId;
    @Value("${sign.signNotifyUrl}")
    private String signNotifyUrl;

    /**
     * 没记录的情况
     */
    public ResponseDto<PreSignResVo> apiPresign(String employerNo, PreSignReqVo reqVo, String logFlag, String signType) {
        PreSignResVo preSignResVo = new PreSignResVo();
        SignRecord signRecord = null;
        try {
            //生成userId
            String userId = MD5Util.getMixMd5Str(reqVo.getMainstayNo()+employerNo+reqVo.getIdCardNo()+reqVo.getName());

            logFlag = String.join("-", employerNo, userId);
            //获取信息
            Merchant employer = merchantFacade.getByMchNo(employerNo);
            Merchant mainstay = merchantFacade.getByMchNo(reqVo.getMainstayNo());
            if (mainstay == null) {
                throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("mainstay_no: [" + reqVo.getMainstayNo() + "] 代征主体不存在");
            }
            signRecord = fillSignRecord(userId, reqVo, employer, mainstay, signType);

            if (this.authNew(signRecord)) {
                //调用e签宝操作
                this.preSign(userId);

                preSignResVo.setUserId(userId);
                preSignResVo.setInfoStatus(SuccessFailCodeEnum.SUCCESS.getValue());
                preSignResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
                preSignResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
                log.info("[预签约==> {}] 返回商户信息鉴权状态:{}", logFlag, JsonUtil.toString(preSignResVo));
                return ResponseDto.success(preSignResVo, "");
            }else {
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("鉴权处理失败，请联系客服人员");
            }



        } catch (BizException ex) {
            if (ex.getSysErrorCode() == CommonExceptions.GET_LOCK_ERROR.getSysErrorCode()) {
                return ResponseDto.fail(ex.getApiErrorCode(), ex.getErrMsg());
            }
            log.error("[{}]==>预签约 业务异常：", logFlag, ex);
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("employerNo", employerNo);
            paramMap.put("receiveNameMd5", MD5Util.getMixMd5Str(reqVo.getName()));
            paramMap.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(reqVo.getIdCardNo()));
            paramMap.put("mainstayNo", reqVo.getMainstayNo());
            signRecord = signRecordBiz.getOne(paramMap);
            //更新失败记录
            if (signRecord != null && signRecord.getId() != null) {
                signRecord.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                signRecord.setErrCode(ex.getApiErrorCode());
                signRecord.setErrMsg(ex.getErrMsg());
                signRecordBiz.update(signRecord);
            }
            preSignResVo.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
            preSignResVo.setBizErrCode(ex.getApiErrorCode());
            preSignResVo.setBizErrMsg(ex.getErrMsg());
            return ResponseDto.success(preSignResVo, "");
        } catch (Exception e) {
            log.error("[{}]==>预签约 系统异常5：", logFlag, e);
            //更新失败记录
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("employerNo", employerNo);
            paramMap.put("receiveNameMd5", MD5Util.getMixMd5Str(reqVo.getName()));
            paramMap.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(reqVo.getIdCardNo()));
            paramMap.put("mainstayNo", reqVo.getMainstayNo());
            signRecord = signRecordBiz.getOne(paramMap);
            if (signRecord != null && signRecord.getId() != null) {
                signRecord.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                signRecord.setErrCode(ApiExceptions.API_COMMON_ERROR.getApiErrorCode());
                signRecord.setErrMsg("系统异常");
                signRecordBiz.update(signRecord);
            }
            return ResponseDto.unknown();
        }
    }



    private SignRecord fillSignRecord(String userId, PreSignReqVo reqVo, Merchant employer, Merchant mainstay, String signType) {
        SignRecord signRecord = new SignRecord();
        signRecord.setCreateTime(new Date());
        signRecord.setUpdateTime(new Date());
        signRecord.setUserId(userId);
        signRecord.setEmployerNo(employer.getMchNo());
        signRecord.setEmployerName(employer.getMchName());
        signRecord.setMainstayNo(mainstay.getMchNo());
        signRecord.setMainstayName(mainstay.getMchName());
        signRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
        signRecord.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        signRecord.setReceiveNameEncrypt(reqVo.getName());
        signRecord.setReceiveIdCardNoEncrypt(reqVo.getIdCardNo());
        signRecord.setReceivePhoneNoEncrypt(reqVo.getPhoneNo());
        signRecord.getJsonEntity().setSignType(signType);
        signRecord.setIdCardBackUrl(reqVo.getIdCardBackUrl());
        signRecord.setIdCardFrontUrl(reqVo.getIdCardFrontUrl());
        signRecord.setCerFaceUrl(reqVo.getCerFaceUrl());
        signRecord.setReceiveAccountNoEncrypt(reqVo.getBankCardCode());
        signRecord.setIdCardType(IdCardTypeEnum.ORIGINAL.getCode());
        signRecord.setSmsSendFrequency(0);
        return signRecord;
    }


    /**
     * 预签约
     *
     * @param userId 签约用户的Id
     */
    public void preSign(String userId) {
        SignRecord signRecord = signRecordBiz.getOne(Collections.singletonMap("userId", userId));
        if (signRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[预签约] 不存在签约请求记录");
        }
        if (!Objects.equals(signRecord.getInfoStatus(), SuccessFailCodeEnum.SUCCESS.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[预签约] 信息鉴权状态不为成功,不允许进行预签约");
        }
        if (signRecord.getSignStatus().intValue() == SignStatusEnum.SIGN_SUCCESS.getValue()) {
            return;
        }
        if (!Objects.equals(signRecord.getSignStatus(), SignStatusEnum.SIGN_CREATE.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[预签约] 签约状态不为已创建,不允许进行预签约");
        }
        handle(signRecord, userId);
    }

    public CreateFileByTemplateResDataVoV3 createSilenceSign(SignRecord signRecord) {
        String logFlag = signRecord.getEmployerNo() + "-" + signRecord.getUserId();
        log.info("[{}]请求e签宝预签约的相关接口", logFlag);
        //获取锁
        log.info("[预签约环节: {}]==>获取预签约锁", signRecord.getUserId());
        String lockKey = String.join(":", TradeConstant.PRE_SIGN_LOCK_KEY, signRecord.getUserId());
        String clientId = redisLock.tryLockLong(lockKey, 30000, 1);
        if (clientId == null) {
            log.info("[预签约环节: {}]==>获取预签约锁失败，直接丢弃", signRecord.getUserId());
            return null;
        }
        try {
            return createFileByTemplate(signRecord);
        } catch (Exception e) {
            log.error("[{}]请求e签宝预签约的相关接口失败", logFlag, e);
            throw e;
        } finally {
            redisLock.unlockLong(clientId);
        }
    }

    private void handle(SignRecord signRecord, String userId) {
        String logFlag = signRecord.getEmployerNo() + "-" + userId;
        log.info("[{}]请求e签宝预签约的相关接口", logFlag);

        //获取锁
        log.info("[预签约环节: {}]==>获取预签约锁", userId);
        String lockKey = String.join(":", TradeConstant.PRE_SIGN_LOCK_KEY, userId);
        String clientId = redisLock.tryLockLong(lockKey, 30000, 1);
        if (clientId == null) {
            log.info("[预签约环节: {}]==>获取预签约锁失败，直接丢弃", userId);
            return;
        }
        try {
//            String accountId = signRecord.getAccountId();
//            if (StringUtils.isBlank(accountId)) {
            //创建签名用户
//            SignCreatePersonReqVo signCreatePersonReqVo = new SignCreatePersonReqVo(userId, signRecord.getReceiveNameDecrypt(), "CRED_PSN_CH_IDCARD", signRecord.getReceiveIdCardNoDecrypt());
//            signCreatePersonReqVo.setMobile(signRecord.getReceivePhoneNoDecrypt());
//                EsignResVo<SignCreatePersonResDataVo> accountVo = channelSignFacade.createPersonByThirdPartyUserId(signCreatePersonReqVo);
//                if (!Objects.equals(accountVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//                    throw ApiExceptions.API_SIGN_CHANNEL_FAIL;
//                }

//            final AccountCreateResponse personalAccount = channelSignFacade.createPersonalAccount(signCreatePersonReqVo, true);
//            String accountId = accountVo.getData().getAccountId();
//            signRecord.setAccountId(personalAccount.getAccountId());
//            signRecord.setPersonalSignature(personalAccount.getSealUrl());
//            }

//            String fileId = signRecord.getFileId();
//            if (StringUtils.isBlank(fileId)) {
            CreateFileByTemplateResDataVoV3 fileRes = createFileByTemplate(signRecord);
            String fileId = fileRes.getFileId();
            signRecord.setFileId(fileId);
//            }

            SignRecord record = signRecordBiz.getOne(MapUtil.builder(new HashMap<String, Object>()).put("userId", signRecord.getUserId()).build());
            record.setSignStatus(SignStatusEnum.WAIT_SIGN.getValue());
            record.setErrMsg(null);
            record.setErrCode(null);
            record.setFileId(signRecord.getFileId());
//            record.setAccountId(personalAccount.getAccountId());
//            record.setPersonalSignature(personalAccount.getSealUrl());
            signRecordBiz.update(record);
            log.info("[{}]请求e签宝预签约的相关接口完成,更新记录成功", logFlag);
            SignRecord newRecord = signRecordBiz.getOne(MapUtil.builder(new HashMap<String, Object>()).put("userId", signRecord.getUserId()).build());
//            this.createPersonalSignature(newRecord);
            userInfoBiz.insertOrUpdate(new UserInfo().buildUserInfo(record));
        } catch (Exception e) {
            log.error("[{}]请求e签宝预签约的相关接口失败", logFlag, e);
            throw e;
        } finally {
            redisLock.unlockLong(clientId);
        }
    }

    public void preSignModify(String userId, String phoneNo) {

        SignRecord signRecord = signRecordBiz.getOne(Collections.singletonMap("userId", userId));
        if (signRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[预签约] 不存在签约请求记录,userId:" + userId);
        }
        if (!Objects.equals(signRecord.getInfoStatus(), SuccessFailCodeEnum.SUCCESS.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[预签约] 信息鉴权状态不为成功,不允许进行预签约,userId:" + userId);
        }

        //获取锁
        log.info("[预签约环节: {}]==>获取预签约修改锁", userId);
        String lockKey = String.join(":", TradeConstant.PRE_SIGN_MODIFY_LOCK_KEY, userId);
        String clientId = redisLock.tryLockLong(lockKey, 30000, 1);
        if (clientId == null) {
            log.info("[预签约环节: {}]==>获取预签约修改失败，直接丢弃", userId);
            return;
        }

        try {
            String logFlag = signRecord.getEmployerNo() + "-" + userId;
            log.info("[{}]预签约阶段-修改手机号", logFlag);
            signRecord.setReceivePhoneNoEncrypt(phoneNo);
            UpdatePersonInfoReqVo updatePersonInfoReqVo = new UpdatePersonInfoReqVo(signRecord.getAccountId());
            updatePersonInfoReqVo.setMobile(phoneNo);
//            EsignResVo<UpdatePersonInfoResDataVo> updateResVo = channelSignFacade.updatePersonInfo(updatePersonInfoReqVo);
//            if (!Objects.equals(updateResVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//                throw ApiExceptions.API_SIGN_CHANNEL_FAIL;
//            }
//            log.info("[{}]预签约阶段-修改手机号成功", logFlag);
            signRecordBiz.update(signRecord);

//            SignRecord newRecord = signRecordBiz.getOne(MapUtil.builder(new HashMap<String, Object>()).put("userId", signRecord.getUserId()).build());
//            this.createPersonalSignature(newRecord);
            SignRecord srecord = signRecordBiz.getOne(MapUtil.builder(new HashMap<String, Object>()).put("userId", signRecord.getUserId()).build());
            userInfoBiz.insertOrUpdate(new UserInfo().buildUserInfo(srecord));
        } finally {
            redisLock.unlockLong(clientId);
        }
    }

    /**
     * 根据模板创建文件
     *
     * @param signRecord 签约记录
     * @return 成功则返回的是文件信息
     */
    private CreateFileByTemplateResDataVoV3 createFileByTemplate(SignRecord signRecord) {
        String templateId = existTemplateId(signRecord, merchantFacade);
        //查询模板 获取填充位置
        EsignResVo<SignTemplateResDataVoV3> templateResVo = channelSignFacade.getSignTemplate(new SignTemplateReqVo(templateId));
        if (!Objects.equals(templateResVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw ApiExceptions.API_SIGN_CHANNEL_FAIL;
        }
        SignTemplateResDataVoV3 templateDataVo = templateResVo.getData();
        //解析控件label
        List<StructComponentV3> componentList = templateDataVo.getComponents();
        List<CreateFileByTemplateReqVoV3.Components> simpleFormFields = new ArrayList<>();

        if (!CollectionUtils.isEmpty(componentList)) {
            //有控件
            List<MerchantEmployerPosition> positionList = merchantEmployerPositionFacade.listByMchNo(signRecord.getEmployerNo());
            Map<String, String> dataMap = Maps.newHashMap();
            dataMap.put("代征主体名称", signRecord.getMainstayName());
            dataMap.put("用工企业名称", signRecord.getEmployerName());
            dataMap.put("岗位大类", positionList.get(0).getWorkCategoryName());
            dataMap.put("自由职业者名称", signRecord.getReceiveNameDecrypt());
            dataMap.put("自由职业者身份证号", signRecord.getReceiveIdCardNoDecrypt());
            dataMap.put("甲方签署区", "甲方签署区");
            dataMap.put("乙方签署区", "乙方签署区");
            dataMap.put("单行文本", " ");
            dataMap.put("多行文本", " ");
            dataMap.put("自由职业者手机号码", signRecord.getReceivePhoneNoDecrypt());
            dataMap.put("骑缝章", "骑缝章");

            dataMap.put("日期", DateUtil.parseJodaDateTime(new Date()).toString("yyyy/MM/dd"));
            //填充
            componentList.forEach(
                    component -> {
                        String data = dataMap.get(component.getComponentName());
                        if (data != null) {
                            simpleFormFields.add(new CreateFileByTemplateReqVoV3.Components().setComponentId(component.getComponentId()).setComponentValue(data));
                        }
                    }
            );
        }

        // 创建文件  getEmployerName
        CreateFileByTemplateReqVoV3 createFileByTemplateReqVo = new CreateFileByTemplateReqVoV3("【" + signRecord.getMainstayName() + "】&【" + signRecord.getReceiveNameDecrypt() + "】共享服务协议", templateId, simpleFormFields);
        EsignResVo<CreateFileByTemplateResDataVoV3> createFileVo = channelSignFacade.createFileByTemplate(createFileByTemplateReqVo);
        if (!Objects.equals(createFileVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw ApiExceptions.API_SIGN_CHANNEL_FAIL;
        }
        return createFileVo.getData();
    }

    public ExecuteUrlResDataVo createSign(CreateSignReqVo reqVo, String logFlag,boolean isResendMsg) {
        String userId = reqVo.getUserId();
        SignRecord signRecord = signRecordBiz.getOne(Collections.singletonMap("userId", userId));
        if (signRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不存在签约请求记录");
        }
        if (reqVo.getSignType() != null) {
            signRecord.setSignType(reqVo.getSignType());
        }

        if (StringUtils.isBlank(signRecord.getReceivePhoneNoDecrypt())&&reqVo.getSignType()==ChannelSignTypeEnum.MSG.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约手机号不能为空");
        }
        String templateId = existTemplateId(signRecord, merchantFacade);
        String fileId = signRecord.getFileId();
        if (fileId == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约记录未生成签署文件,请进行预签约生成");
        }
        String flowId = signRecord.getFlowId();
        // 如果为短信签约, 且文件id不为空, 则允许重新发送短信
        EsignResVo<ExecuteUrlResDataVo> urlResVo = null;
        if (!isResendMsg && StringUtils.isNotBlank(flowId)) {
            log.info("[发起签约 {}] ==> 渠道已存在签署流程 flowId:{} 查询签约地址返回", logFlag, flowId);

            urlResVo = channelSignFacade.getExecuteUrlV3(new ExecuteUrlReqVoV3().setSignFlowId(flowId)
                    .setOperator(new ExecuteUrlReqVoV3.Operator()
                            .setPsnAccount(signRecord.getReceivePhoneNoDecrypt())));

            if (!Objects.equals(urlResVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
                throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(urlResVo.getMessage());
            }
            // 设置签约地址
            if (urlResVo.getData() != null && StringUtils.isNotBlank(urlResVo.getData().getUrl())) {
                signRecord.setSignUrl(urlResVo.getData().getUrl());
                signRecordBiz.update(signRecord);
                //重新获取一次新的versionNo
                signRecord = signRecordBiz.getById(signRecord.getId());
            }
            if (SignStatusEnum.SIGN_SUCCESS.getValue() == signRecord.getSignStatus()) {
                urlResVo.getData().setExist(true);
                // 如果已经签约成功了直接返回
                log.info("[发起签约 {}] ==>如果已经签约成功了直接返回",logFlag);
                return urlResVo.getData();
            }
            // 如果不是短信模式, 也直接返回
            if (signRecord.getSignType() != ChannelSignTypeEnum.MSG.getValue()) {
                log.info("[发起签约 {}] ==>如果不是短信模式, 也直接返回",logFlag);
                return urlResVo.getData();
            }
        }
        assert urlResVo != null;
        //1、配置文档
//        ArrayList<Doc> docs = Lists.newArrayList();
//        docs.add(new Doc().setFileId(fileId));
        //2、配置流程
        FlowConfigInfo flowConfigInfo = new FlowConfigInfo();
        flowConfigInfo.setNoticeDeveloperUrl(signNotifyUrl);
        flowConfigInfo.setSignPlatform("1");// 1-开放服务h5 2-支付宝签
        if (Objects.equals(ChannelSignTypeEnum.URL_NO_CODE.getValue(), reqVo.getSignType())) {
            flowConfigInfo.setNoticeType("");
        }
        if (StringUtils.isNotBlank(reqVo.getRedirectUrl())) {
            flowConfigInfo.setRedirectUrl(reqVo.getRedirectUrl());
        }
        FlowInfo flowInfo = new FlowInfo()
                .setBusinessScene("自由职业者共享服务协议")
                //启用自动归档
                .setAutoArchive(true)
                .setAutoInitiate(true)
                .setFlowConfigInfo(flowConfigInfo);

        //查询模版 签署区位置
        EsignResVo<SignTemplateResDataVoV3> templateRes = channelSignFacade.getSignTemplate(new SignTemplateReqVo(templateId));
        List<StructComponentV3> components = templateRes.getData().getComponents();
        if (CollUtil.isEmpty(components)) {
            throw ApiExceptions.API_SIGN_TEMPLATE_FAIL.newWithErrMsg("找不到签署区");
        }
        List<StructComponentV3> signFile = components.stream().filter(x -> ObjectUtil.equal(x.getComponentName(), "签署区")).collect(Collectors.toList());
        List<StructComponentV3> aSignFile = signFile.stream()
                .filter(x -> ObjectUtil.equal(x.getComponentSpecialAttribute().getSignerRole(), "甲方签署区")).collect(Collectors.toList());
        List<StructComponentV3> bSignFile = signFile.stream()
                .filter(x -> ObjectUtil.equal(x.getComponentSpecialAttribute().getSignerRole(), "乙方签署区")).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(aSignFile) || CollectionUtils.isEmpty(bSignFile)) {
            throw ApiExceptions.API_SIGN_TEMPLATE_FAIL.newWithErrMsg("找不到签署区");
        }

        //3、配置签署区域
        ArrayList<CreateByFileReqVo.SignFields> psnSignfields = Lists.newArrayList();
        for (StructComponentV3 structComponent : bSignFile) {
            ComponentPosition componentPosition = structComponent.getComponentPosition();
            psnSignfields.add(new CreateByFileReqVo.SignFields()
                    .setFileId(fileId)
                    .setNormalSignFieldConfig(new CreateByFileReqVo.NormalSignFieldConfig().setFreeMode(false)
                            .setAutoSign(false)
                            .setSignFieldStyle(structComponent.getNormalSignField().getSignFieldStyle())
                            .setSignFieldPosition(new CreateByFileReqVo.SignFieldPosition().setPositionPage(String.valueOf(componentPosition.getComponentPageNum()))
                                    .setPositionX(componentPosition.getComponentPositionX())
                                    .setPositionY(componentPosition.getComponentPositionY()))));//构造企业signfields参数对象,用于后续入参使用,支持链式入参
        }
        ArrayList<CreateByFileReqVo.SignFields> orgSignfields = Lists.newArrayList();
        DataDictionary dictionary = dataDictionaryFacade.getDataDictionaryByName("EsignSealsEnum");
        List<DataDictionary.Item> itemList = dictionary.getItemList();
        String mainstayNo = signRecord.getMainstayNo();
        DataDictionary.Item item = itemList.stream().filter(x -> ObjectUtil.equal(x.getCode(), mainstayNo)).findFirst().orElse(null);


        for (StructComponentV3 structComponent : aSignFile) {
            ComponentPosition componentPosition = structComponent.getComponentPosition();
            orgSignfields.add(new CreateByFileReqVo.SignFields()
                    .setFileId(fileId)
                    .setNormalSignFieldConfig(new CreateByFileReqVo.NormalSignFieldConfig().setFreeMode(false)
                            .setAutoSign(true)
                            .setAssignedSealId(ObjectUtil.isNotEmpty(item)?item.getFlag():null)
                            .setSignFieldStyle(structComponent.getNormalSignField().getSignFieldStyle())
                            .setSignFieldPosition(new CreateByFileReqVo.SignFieldPosition().setPositionPage(String.valueOf(componentPosition.getComponentPageNum()))
                                    .setPositionX(componentPosition.getComponentPositionX())
                                    .setPositionY(componentPosition.getComponentPositionY()))));//构造企业signfields参数对象,用于后续入参使用,支持链式入参
        }

        //配置签署人并指定相应区域
        ArrayList<CreateByFileReqVo.Signer> signers = new ArrayList<>();
        if(ObjectUtil.isEmpty(signRecord.getReceivePhoneNoDecrypt())||ObjectUtil.isEmpty(signRecord.getReceivePhoneNoDecrypt().trim())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[发起签约失败] 手机号不能为空");
        }
        signers.add(new CreateByFileReqVo.Signer()
                .setSignerType(0)
                .setNoticeConfig(new CreateByFileReqVo.NoticeConfig().setNoticeTypes(ObjectUtil.equal(reqVo.getSignType(),ChannelSignTypeEnum.MSG.getValue())?"1":""))
                .setPsnSignerInfo(new CreateByFileReqVo.PsnSignerInfo()
                        .setPsnAccount(signRecord.getReceivePhoneNoDecrypt())
                        .setPsnInfo(new CreateByFileReqVo
                                .PsnInfo().setPsnName(signRecord.getReceiveNameDecrypt())))
                .setSignFields(psnSignfields)
        );//传入个人signer信息
        final Vendor vendor = vendorFacade.getVendorByNo(signRecord.getMainstayNo());
        if (StringUtils.equals(vendor.getProductNo(),"预留-需要用过企业盖章的产品")){
            final Merchant merchant = merchantFacade.getByMchNo(signRecord.getEmployerNo());
            if (merchant == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[发起签约] 不存在相应商户,userId:[" + userId + "],merchantNo:[" + signRecord.getEmployerNo() + "]");
            }

            EsignResVo<OrgIdentityInfoResDataVo> resVo = channelSignFacade.getOrgIdentityInfoV3(new OrgIdentityInfoResDateVo().setOrgName(merchant.getMchName()));
            if (!Objects.equals(resVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
                throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(resVo.getMessage());
            }

            signers.add(new CreateByFileReqVo.Signer()
                    .setSignerType(1)
                    .setSignFields(orgSignfields)
            );
        }else {
            //查询代征主体的信息
            Merchant mainstay = merchantFacade.getByMchNo(signRecord.getMainstayNo());
            if (mainstay == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[发起签约] 不存在相应代征主体,userId:[" + userId + "],mainstayNo:[" + signRecord.getMainstayNo() + "]");
            }

            EsignResVo<OrgIdentityInfoResDataVo> resVo = channelSignFacade.getOrgIdentityInfoV3(new OrgIdentityInfoResDateVo().setOrgName(mainstay.getMchName()));
            if (!Objects.equals(resVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
                throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(resVo.getMessage());
            }
            signers.add(new CreateByFileReqVo.Signer()
                    .setSignerType(1)
                    .setSignFields(orgSignfields)
            );
        }
        log.info("[发起签约 {}] ==>调用e签宝发起签约",logFlag);
        //1、设置待签署文件信息
        CreateByFileReqVo createFlowOneStepReqVo = new CreateByFileReqVo();
        createFlowOneStepReqVo.setDocs(Collections.singletonList(new CreateByFileReqVo.CreateByFileDoc()
                .setFileId(fileId)));
        //2、签署流程配置项
        createFlowOneStepReqVo.setSignFlowConfig(new CreateByFileReqVo.SignFlowConfig()
                .setSignFlowTitle(flowInfo.getBusinessScene())
                .setAuthConfig(new CreateByFileReqVo.AuthConfig().setPsnAvailableAuthModes(Arrays.asList("PSN_MOBILE3")))
                .setAutoFinish(true)
                .setNotifyUrl(signNotifyUrl));
        //3、配置签署方的信息
        createFlowOneStepReqVo.setSigners(signers);

        EsignResVo<CreateByFileResDateVo> resVo = channelSignFacade.createByFile(createFlowOneStepReqVo);
        if (!Objects.equals(resVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(resVo.getMessage());
        }
        flowId = resVo.getData().getSignFlowId();

        log.info("[电子签约：user_id:{},flowId:{}]", userId, flowId);

        signRecord.setFlowId(flowId);
        // 获取签署地址
        urlResVo = channelSignFacade.getExecuteUrlV3(new ExecuteUrlReqVoV3().setSignFlowId(flowId)
                .setOperator(new ExecuteUrlReqVoV3.Operator()
                        .setPsnAccount(signRecord.getReceivePhoneNoDecrypt())));

        if (!Objects.equals(urlResVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(urlResVo.getMessage());
        }

        signRecord.setSignType(reqVo.getSignType());
        signRecord.setRedirectUrl(reqVo.getRedirectUrl());
        signRecord.setNotifyUrl(reqVo.getCallbackUrl());
        if (urlResVo.getData() != null && StringUtils.isNotBlank((urlResVo.getData().getUrl()))) {
            // 设置签约地址
            signRecord.setSignUrl(urlResVo.getData().getUrl());
        }
        signRecordBiz.update(signRecord);
        if (signRecord.getSignType() == ChannelSignTypeEnum.MSG.getValue()) {
            incrSendCount(signRecord);
        }
        return urlResVo.getData();
    }

    private void incrSendCount(SignRecord signRecord) {
        signRecord.setSmsSendTime(new Date());
        signRecord.setUpdateTime(new Date());
        signRecordBiz.updateSmsFrequency(signRecord);
    }

    private boolean verifySmsCount(SignRecord signRecord) {
        if (signRecord.getSmsSendTime() != null && DateUtil.subtractDays(new Date(), signRecord.getSmsSendTime()) > 1) {
            signRecord.setSmsSendFrequency(0);
            signRecord.setUpdateTime(new Date());
            signRecordBiz.resetSmsSendFrequency(signRecord);
            return false;
        }
        if (signRecord.getSmsSendTime() != null &&
                DateUtil.subtractDays(new Date(), signRecord.getSmsSendTime()) <= 1) {
            return signRecord.getSmsSendFrequency() != null && signRecord.getSmsSendFrequency() >= LIMIT_SEND_VALUE;
        }
        return false;
    }

    /**
     * 签约回调处理
     *
     * @param signReceiveRespVo 结果
     */
    public void signReceive(SignReceiveRespVo signReceiveRespVo) {
        String flowId = signReceiveRespVo.getFlowId();
        Integer flowStatus = signReceiveRespVo.getFlowStatus();
        SignRecord signRecord = signRecordBiz.getOne(Collections.singletonMap("flowId", flowId));
        if (signRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[签约回调处理] 流程对应的签约记录不存在,flowId:[" + flowId + "]");
        }
        String logFlag = String.join("-", signRecord.getEmployerNo(), signRecord.getUserId(), flowId);
        log.info("[{}] 签约回调处理", logFlag);

        if (Objects.equals(signRecord.getSignStatus(), SignStatusEnum.SIGN_FAIL.getValue())
                || Objects.equals(signRecord.getSignStatus(), SignStatusEnum.SIGN_SUCCESS.getValue())
        ) {
            log.info("[{}] 签约回调处理,状态:{}, 状态为终态", logFlag, signRecord.getSignStatus());
            //检查是否需要重新上传文件
            if (StringUtils.isBlank(signRecord.getFileUrl())) {
                log.info("[{}] 签约回调处理,文件地址不正确,重新获取更新文件地址", logFlag);
                String fileUrl = getFileUrl(flowId, logFlag);
                //还是空的就不入库了
                if (StringUtils.isNotBlank(fileUrl)) {
                    signRecord.setFileUrl(fileUrl);
                    signRecordBiz.update(signRecord);
                } else {
                    log.info("[{}] 签约回调处理,重新获取文件地址为空", logFlag);
                }
            }
            return;
        }

        if (Objects.equals(flowStatus, ChannelSignFlowStatusEnum.FINISH.getValue())) {
            signRecord.setSignStatus(SignStatusEnum.SIGN_SUCCESS.getValue());
            signRecord.setOperateStatus(OperateStatusEnum.CHECK_FILE.getStatus());
            signRecord.setErrCode(null);
            signRecord.setErrMsg(null);

            //推送签约信息给供应商系统
//            syncToExteranlSystem(signRecord);

        } else {
            signRecord.setErrCode(String.valueOf(signReceiveRespVo.getFlowStatus()));
            signRecord.setErrMsg(String.valueOf(signReceiveRespVo.getFlowStatus()));
            signRecord.setSignStatus(SignStatusEnum.SIGN_FAIL.getValue());
        }
        String fileUrl = getFileUrl(flowId, logFlag);
        signRecord.setFileUrl(fileUrl);
        signRecordBiz.update(signRecord);
        //最后通知商户
        notifyMchSignResult(signRecord);
        log.info("[{}] 签约回调处理完成", logFlag);
    }

    /**
     * e签宝地址转成本地存储地址
     *
     * @param flowId  流程id
     * @param logFlag 日志标识
     * @return 本地存储url
     */
    public String getFileUrl(String flowId, String logFlag) {
        EsignResVo<DocumentDownloadResDataVo> resVo = channelSignFacade.getDocumentDownloadUrl(new DocumentDownloadReqVo(flowId));
        if (Objects.equals(resVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            log.info("[{}] 签约回调处理 获取到文件结果", logFlag);
            List<DownloadDoc> docList = resVo.getData().getFiles();
            if (!CollectionUtils.isEmpty(docList)) {
                DownloadDoc doc = docList.get(0);
                try {
                    log.info("[{}] 签约回调处理 获取到文件地址 进行fastDfs储存", logFlag);
                    return channelSignFacade.downLoadFile(logFlag, doc.getFileName()+".pdf", doc.getDownloadUrl());
                } catch (IOException e) {
                    log.error("[{}] 签约完成,文件保存fastDfs地址异常", logFlag, e);
                }
            }
        } else {
            log.info("[{}] 签约回调处理 获取不到e签宝文件地址", logFlag);
        }
        return "";
    }

    private void notifyMchSignResult(SignRecord signRecord) {
        try {
            if (StringUtils.isNotEmpty(signRecord.getNotifyUrl())) {
                String userId = signRecord.getUserId();
                String cacheKey = TradeConstant.GRANT_NOTIFY_MCH_REDIS_PREFIX + userId;
                if (redisClient.get(cacheKey) == null) {
                    //缓存五分钟，期间不会再通知商户
                    redisClient.set(cacheKey, userId, 300);
                    MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillSignResultNotifyParam(signRecord);
                    tradeNotifyBiz.notifyMerchantSign(merchantNotifyParam, signRecord);
                    log.info("[{}-{}]签约完成，通知商户签约结果", signRecord.getEmployerNo(), signRecord.getUserId());
                }
            }
        } catch (Exception e) {
            log.info("[{}-{}]通知商户签约异常", signRecord.getEmployerNo(), signRecord.getUserId(), e);
            throw CommonExceptions.COMMON_RETRY_ERROR;
        }
    }

    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;

    public void preSignWithPending(String userId) {
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        preSign(userId);
        // 更新为发送中状态
        SignRecord signRecord = signRecordBiz.getOne(Collections.singletonMap("userId", userId));
        signRecord.setSignStatus(SignStatusEnum.PENDING.getValue());
        signRecordBiz.update(signRecord);
        platformTransactionManager.commit(transaction);
    }

    public boolean checkTemplateId(String employerNo, String mainstayNo) {
        SignRecord signRecord = new SignRecord();
        signRecord.setEmployerNo(employerNo);
        signRecord.setMainstayNo(mainstayNo);
        return StringUtils.isBlank(existTemplateId(signRecord, merchantFacade));
    }


//    public void createPersonalSignature(SignRecord signRecord) {
//        EsignResVo<PsnIdentityInfoResDataVo> psnIdentityInfoResDataVoEsignResVo  = channelSignFacade.getPsnIdentityInfoV3(new PsnIdentityInfoReqVo().setPsnAccount(signRecord.getReceivePhoneNoDecrypt()));
//        signRecord.setAccountId(psnIdentityInfoResDataVoEsignResVo.getData().getPsnId());
//        savePersonalSignature(personalSignature(signRecord), signRecord.getId());
//    }

//    private void savePersonalSignature(String personalSignature, Long id) {
//        if (ObjectUtil.isEmpty(personalSignature)) {
//            return;
//        }
//        byte[] imageArray = new byte[0];
//        try {
//            imageArray = FileUtils.download(personalSignature);
//        } catch (IOException e) {
//            log.error("[{}] 印章下载出错, 保存签名失败: {}", id, JSONObject.toJSON(personalSignature), e);
//            return;
//        }
//        String fileUrl = fastdfsClient.uploadFile(imageArray, RandomUtil.get16LenStr() + ".jpg");
//        // 保存地址
//        SignRecord signRecord = signRecordBiz.getById(id);
//        if (signRecord == null) {
//            log.error("[{}]签约记录为空, 保存签名失败: {}", id, JSONObject.toJSON(personalSignature));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约记录为空, 保存签名失败");
//        }
//        signRecord.setPersonalSignature(fileUrl);
//        signRecordBiz.update(signRecord);
//    }

//    private String personalSignature(SignRecord record) {
//        String logFlag = String.join("-", record.getEmployerNo(), record.getUserId());
//        log.info("[开始获取个人印章: {}]==>获取锁", logFlag);
//        String lockKey = String.join(":", TradeConstant.CREATE_PERSONAL_SIGNATURE_LOCK_KEY, logFlag);
//        String clientId = redisLock.tryLockLong(lockKey, 0, 1);
//        if (clientId == null) {
//            log.info("[获取个人印章环节: {}]==>获取发起签约锁失败，直接丢弃", logFlag);
//            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
//        }
//        try {
//            //尝试创建个人印章
//            personalSignature(record.getAccountId(), logFlag);
//            //获取列表
//            GetPsnSealListResDataVo psnSealList = getPsnSealList(record.getAccountId());
//            if(ObjectUtil.isNotEmpty(psnSealList)){
//                List<GetPsnSealListResDataVo.Seals> seals = psnSealList.getSeals();
//                GetPsnSealListResDataVo.Seals seal = seals.stream().filter(x -> ObjectUtil.equal(x.getSealName(), "个人签章")).findFirst().orElse(null);
//                if(seal!=null){
//                    log.info("[{}]==>获取个人印章成功", logFlag);
//                    return seal.getSealImageDownloadUrl();
//                }
//            }
//            log.info("[{}]==>获取个人印章失败", logFlag);
//            return null;
//        } catch (BizException e) {
//            log.error("[{}]==>获取个人印章 业务异常：", logFlag, e);
//        } catch (Exception e) {
//            log.error("[{}]==>获取个人印章 系统异常：", logFlag, e);
//        } finally {
//            redisLock.unlockLong(clientId);
//        }
//        return null;
//    }

//    public GetPersonalSignatureResDataVo personalSignature(String accountId, String logFlag) {
//        PersonalSignatureReqVo reqVo = new PersonalSignatureReqVo();
//        reqVo.setPsnId(accountId);
//        EsignResVo<GetPersonalSignatureResDataVo> resVo = channelSignFacade.personalSignature(reqVo);
//
//        if (!Objects.equals(resVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//            log.info("[{}] 请求创建个人签章异常", logFlag);
//            return null;
//        }
//        return resVo.getData();
//    }


//    public GetPsnSealListResDataVo getPsnSealList(String accountId) {
//        PsnSealListReqVo reqVo = new PsnSealListReqVo();
//        reqVo.setPsnId(accountId);
//        EsignResVo<GetPsnSealListResDataVo> resVo = channelSignFacade.getPsnSealList(reqVo);
//        return resVo.getData();
//    }
//    public void syncToExteranlSystem(SignRecord signRecord) {
//        String mainstayNo = signRecord.getMainstayNo();
//        String employerNo = signRecord.getEmployerNo();
//        String yishuiNo = dataDictionaryFacade.getSystemConfig("yishui");
//        if (StringUtils.equals(mainstayNo, yishuiNo)) {
//            EmployerMainstayRelation emRelation = relationFacade.getByEmployerNoAndMchNo(employerNo, mainstayNo);
//
//            SignRecordVo signRecordVo = new SignRecordVo();
//            signRecordVo.setEmployerNo(signRecord.getEmployerNo());
//            signRecordVo.setId(signRecord.getId());
//            signRecordVo.setEmployerName(signRecord.getEmployerName());
//            signRecordVo.setIdCard(signRecord.getReceiveIdCardNoDecrypt());
//            signRecordVo.setName(signRecord.getReceiveNameDecrypt());
//            signRecordVo.setPhone(signRecord.getReceivePhoneNoDecrypt());
//            signRecordVo.setMainstayNo(signRecord.getMainstayNo());
//            signRecordVo.setMainstayName(signRecord.getMainstayName());
//            SignFileVo signFile = getSignFile(signRecordVo);
//
//            String fastDfsBaseUrl = dataDictionaryFacade.getSystemConfig("fastDfsBaseUrl");
//            try {
//                if (StringUtils.isNotBlank(signFile.getCerFrontImg())) {
////                    String fileUrl = uploadFileToYishui(signFile.getCerFrontImg());
//                    String fileUrl = fastDfsBaseUrl+signFile.getCerFrontImg();
//                    signFile.setCerFrontImg(fileUrl);
//                }
//                if (StringUtils.isNotBlank(signFile.getSignImg())) {
////                    String fileUrl = uploadFileToYishui(signFile.getSignImg());
//                    String fileUrl = fastDfsBaseUrl+signFile.getSignImg();
//                    signFile.setSignImg(fileUrl);
//                }
//                if (StringUtils.isNotBlank(signFile.getContractImg())) {
////                    String fileUrl = uploadFileToYishui(signFile.getContractImg());
//                    String fileUrl = fastDfsBaseUrl+signFile.getContractImg();
//                    signFile.setContractImg(fileUrl);
//                }
//                if (StringUtils.isNotBlank(signFile.getProtocolImg())) {
////                    String fileUrl = uploadFileToYishui(signFile.getProtocolImg());
//                    String fileUrl = fastDfsBaseUrl+signFile.getProtocolImg();
//                    signFile.setProtocolImg(fileUrl);
//                }
//                if (StringUtils.isNotBlank(signFile.getCerReverseImg())) {
////                    String fileUrl = uploadFileToYishui(signFile.getCerReverseImg());
//                    String fileUrl = fastDfsBaseUrl+signFile.getCerReverseImg();
//                    signFile.setCerReverseImg(fileUrl);
//                }
//            } catch (Exception e) {
//                log.error("上传签约文件到易税失败:{}",e.getMessage());
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("上传签约文件到供应商失败");
//            }
//
//            EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(signRecord.getEmployerNo(), signRecord.getMainstayNo());
//
//            RequestVo<AddEmpVo> requestVo = new RequestVo<>();
//            requestVo.setEnterprise_sn(employerMainstayRelation.getExternalEnterpriseSn());
//            requestVo.setUser_name(employerMainstayRelation.getEmployerNo());
//            AddEmpVo addEmpVo = new AddEmpVo();
//            addEmpVo.setProtocol_img("")
//                    .setCer_code(signRecord.getReceiveIdCardNoDecrypt())
//                    .setHas_auth("1")
//                    .setName(signRecord.getReceiveNameDecrypt())
//                    .setMobile(signRecord.getReceivePhoneNoDecrypt())
//                    .setBank_code(signRecord.getReceiveAccountNoDecrypt())
//                    .setContract_img(signFile.getContractImg())
//                    .setProtocol_img(signFile.getProtocolImg())
//                    .setSign_img(signFile.getSignImg());
//            requestVo.setParam(addEmpVo);
//
//            YiResponse<YishuiAddEmpVo> yishuiAddEmpVoYiResponse = yishuiFacade.addEmployee(requestVo, ChannelTypeEnum.BANK.getValue());
//            if (yishuiAddEmpVoYiResponse.getCode() != 200) {
//                if (!StringUtils.equals("该身份证已经新增过", yishuiAddEmpVoYiResponse.getMsg())) {
//                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(yishuiAddEmpVoYiResponse.getMsg());
//                }
//            }
//
//            RequestVo<ContractListQueryVo> contractListQueryRequestVo = new RequestVo<>();
//            contractListQueryRequestVo.setUser_name(signRecord.getEmployerNo())
//                    .setEnterprise_sn(employerMainstayRelation.getExternalEnterpriseSn());
//            ContractListQueryVo contractListQueryVo = new ContractListQueryVo();
//            contractListQueryVo.setPagination(new Pagination().setPage_size("2").setPage_start("1"))
//                    .setKeyword(signRecord.getReceiveIdCardNoDecrypt());
//            contractListQueryRequestVo.setParam(contractListQueryVo);
//            YiResponse<YishuiContractListVo> yishuiContractListVoYiResponse = yishuiFacade.contractList(contractListQueryRequestVo);
//
//            if (yishuiContractListVoYiResponse.getCode() != 200) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(yishuiContractListVoYiResponse.getMsg());
//            }
//            YishuiContractListVo data = yishuiContractListVoYiResponse.getData();
//            if (data == null) {
//                CommonExceptions.BIZ_INVALID.newWithErrMsg("未在供应商处找到签约记录");
//            }
//            List<Map<String, Object>> list = data.getList();
//            Map<String, Object> contractInfo = list.get(0);
//
//            String enterpriseProfessionalFacilitatorId = String.valueOf(contractInfo.get("enterprise_professional_facilitator_id"));
//            YiResponse<YishuiContractInfoVo> contractInfoVoYiResponse = yishuiFacade.contractInfo(new RequestVo<String>()
//                    .setUser_name(employerMainstayRelation.getExternalUserName())
//                    .setEnterprise_sn(employerMainstayRelation.getExternalEnterpriseSn())
//                    .setParam(enterpriseProfessionalFacilitatorId));
//            YishuiContractInfoVo contractInfoVo = contractInfoVoYiResponse.getData();
//
//            RequestVo<ContractSaveVo> contractSaveVoRequestVo = new RequestVo<>();
//            contractSaveVoRequestVo.setEnterprise_sn(emRelation.getExternalEnterpriseSn()).setUser_name(employerNo).setPassword(emRelation.getExternalPasswordDecrypt());
//            ContractSaveVo contractSaveVo = new ContractSaveVo();
//            contractSaveVo.setContract_img(signFile.getContractImg())
//                    .setSign_img(signFile.getSignImg())
//                    .setProtocol_img(signFile.getProtocolImg())
//                    .setName(signRecord.getReceiveNameDecrypt())
//                    .setCer_code(signRecord.getReceiveIdCardNoDecrypt())
//                    .setCer_front_img(signFile.getCerFrontImg())
//                    .setCer_reverse_img(signFile.getCerReverseImg())
//                    .setBank_code(contractInfoVo.getBank_code())
//                    .setMobile(contractInfoVo.getMobile())
//                    .setCer_face(fastDfsBaseUrl+signRecord.getCerFaceUrl())
//                    .setEnterprise_professional_facilitator_id(enterpriseProfessionalFacilitatorId);
//
//            contractSaveVoRequestVo.setParam(contractSaveVo);
//            YiResponse yiResponse = yishuiFacade.contractSave(contractSaveVoRequestVo);
//            if (yiResponse.getCode() != 200) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(yiResponse.getMsg());
//            }
//            return;
//        }
//    }

    public SignFileVo getSignFile(SignRecordVo recordVo) {
        LimitUtil.notEmpty(recordVo.getIdCard(), "身份证不能为空"); // 解密了的身份证
        LimitUtil.notEmpty(recordVo.getMainstayNo(), "代征主体编号不能为空");
        LimitUtil.notEmpty(recordVo.getMainstayNo(), "代征主体名称不能为空");
        LimitUtil.notEmpty(recordVo.getEmployerNo(), "用工企业编号不能为空");
        LimitUtil.notEmpty(recordVo.getEmployerName(), "用工企业名称不能为空");
        LimitUtil.notEmpty(recordVo.getName(), "姓名不能为空");

        Map<String, Object> map = new HashMap<>();
        map.put("employerNo", recordVo.getEmployerNo());
        map.put("mainstayNo", recordVo.getMainstayNo());
        map.put("receiveNameMd5", MD5Util.getMixMd5Str(recordVo.getName()));
        map.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(recordVo.getIdCard()));

        SignRecord signRecord = signRecordBiz.getOne(map);
        if (signRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户未签约: " + JSONObject.toJSONString(recordVo));
        }
//        if (StringUtils.isBlank(signRecord.getPersonalSignature())) {
//            createPersonalSignature(signRecord);
//        }
//        signRecord = signRecordBiz.getOne(map);
//        if (signRecord == null || StringUtils.isBlank(signRecord.getPersonalSignature())) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取印章失败");
//        }

        return new SignFileVo.Builder(signRecord).build();
    }

//    public String uploadFileToYishui(String fileId) throws IOException {
//        InputStream inputStream = fastdfsClient.downloadFile(fileId);
//        String originalFileName = fastdfsClient.getOriginalFileName(fileId);
//        String uploadUrl = yishuiFacade.getUploadUrl();
//        OkHttpClient httpClient = new OkHttpClient.Builder().build();
//
//        okhttp3.RequestBody requestBody = new MultipartBody.Builder()
//                .setType(MultipartBody.FORM)
//                .addFormDataPart("token", yishuiFacade.getAgentToken())
//                .addFormDataPart("file", originalFileName,
//                        okhttp3.RequestBody.create(MediaType.parse("multipart/form-data"), IoUtil.readBytes(inputStream)))
//                .build();
//
//        Request request = new Request.Builder()
//                .url(uploadUrl)
//                .post(requestBody)
//                .build();
//        Response response = httpClient.newCall(request).execute();
//        if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
//        String string = response.body().string();
//        JSONObject jsonObject = JSON.parseObject(string);
//        if (jsonObject.getIntValue("code") != 200) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("文件上传失败:",jsonObject.getString("msg")));
//        }
//        String fileUrl = jsonObject.getJSONObject("data").getString("resource_href");
//        return fileUrl;
//    }

    public void deleteRecordById(Long id) {
        signRecordBiz.deleteSignRecordById(id);
    }

    private static final String TEMPLATE_FILE_PATH = "/tmp/";
    private static final String FILE_NAME = "自由职业者共享服务协议.pdf";
    private static final String SEAL_FILE_NAME = "盖章版-自由职业者共享服务协议.pdf";
    private static final String IMAGE_NAME = "个人签章.jpg";

//    public String autoAddSeal(CreateSignReqVo reqVo) {
//        SignRecord signRecord = signRecordBiz.getOne(new HashMap<String, Object>() {
//            private static final long serialVersionUID = -1497549868882616801L;
//
//            { put("userId", reqVo.getUserId()); }});
//        if (signRecord == null) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[自动签约] 不存在签约请求记录,userId:[" + reqVo.getUserId() + "]");
//        }
//        String fileId = signRecord.getFileId();
//        if (fileId == null) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[自动签约] 签约记录未生成签署文件,请进行预签约生成,userId:[" + reqVo.getUserId() + "],signRecordId:[" + signRecord.getId() + "]");
//        }
//        return autoAddSeal(signRecord);
//    }

//    public String autoAddSeal(SignRecord signRecord) {
//        String flag = "自动盖章-" + signRecord.getMainstayNo() + "-" + signRecord.getEmployerNo();
//        String fileUrl = createSignWithoutPartyB(signRecord, flag);
//        InputStream fileInputStream = fastdfsClient.downloadFile(fileUrl);
//        String filePath = TEMPLATE_FILE_PATH + signRecord.getUserId() + FILE_NAME;
//        File file = FileUtils.createFile(filePath);
//        try {
//            org.apache.commons.io.FileUtils.copyInputStreamToFile(fileInputStream, file);
//        } catch (IOException e) {
//            log.error("[{}] 自动盖章-下载合同出错", flag, e);
//            return null;
//        }
//
//        UserInfo info = userInfoBiz.getByIdCardNoMd5(signRecord.getReceiveIdCardNoMd5());
//        InputStream imageInputStream = fastdfsClient.downloadFile(info.getPersonalSignature());
//        String imageInputFilePath = TEMPLATE_FILE_PATH + signRecord.getUserId() + IMAGE_NAME;
//        File imageFile = FileUtils.createFile(imageInputFilePath);
//        try {
//            org.apache.commons.io.FileUtils.copyInputStreamToFile(imageInputStream, imageFile);
//        } catch (IOException e) {
//            log.error("[{}] 自动盖章-下载签名出错", flag, e);
//            return null;
//        }
//
//        final Vendor vendor = vendorFacade.getVendorByNo(signRecord.getMainstayNo());
//
//        final String signPosition = dataDictionaryFacade.getSystemConfig("SIGN_POSITION_"+vendor.getProductNo());
//        final JSONArray signPositionArray = JSON.parseArray(signPosition);
//
//        //查找位置
//        Object[] position = signPositionArray.getJSONArray(0).toArray();
//        Object[] position2 = null;
//        if (signPositionArray.size() >= 2) {
//            position2 = signPositionArray.getJSONArray(1).toArray();
//        }
//
//        String outFilePath = TEMPLATE_FILE_PATH + signRecord.getUserId() + SEAL_FILE_NAME;
//        try {
//            PdfReader pdfReader = new PdfReader(filePath);
//            PdfStamper pdfStamper = new PdfStamper(pdfReader, new FileOutputStream(outFilePath));
//            Image image = Image.getInstance(imageInputFilePath);
//            image.scaleAbsolute(120, 120);
//            image.setAbsolutePosition(Float.parseFloat(position[1]+""), Float.parseFloat(position[2]+""));
//
//            PdfContentByte content = pdfStamper.getUnderContent(Integer.parseInt(position[0]+""));
//            if (content == null) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模版文件错误，页码无法匹配");
//            }
//            content.addImage(image);
//
//            if (position2 != null) {
//                content = pdfStamper.getUnderContent(Integer.parseInt(position2[0]+""));
//                if (content == null) {
//                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模版文件错误，页码无法匹配");
//                }
//                image.scaleAbsolute(120, 120);
//                image.setAbsolutePosition(Float.parseFloat(position2[1]+""), Float.parseFloat(position2[2]+""));
//                content.addImage(image);
//            }
//
//            pdfStamper.close();
//        } catch (IOException | DocumentException e) {
//            log.error("[{}] 自动盖章出错", flag, e);
//            return null;
//        }
//        String url = fastdfsClient.uploadFile(outFilePath, file.getName());
//        log.info("[{}] 自动盖章-地址: {}", flag, url);
//        String userId = signRecord.getUserId();
//        signRecord = signRecordBiz.getOne(new HashMap<String, Object>() {
//            private static final long serialVersionUID = 3289009225056958266L;
//
//            { put("userId", userId); }});
//        signRecord.setFileUrl(url);
//        signRecord.setUpdateTime(new Date());
//        signRecord.setSignStatus(SignStatusEnum.SIGN_SUCCESS.getValue());
//        signRecord.setOperateStatus(OperateStatusEnum.CHECK_FILE.getStatus());
//        signRecord.setSignType(ChannelSignTypeEnum.STAMP_SILENCE.getValue());
//        signRecord.setErrCode(null);
//        signRecord.setErrMsg(null);
//        signRecordBiz.update(signRecord);
//        return url;
//    }

    private String createSignWithoutPartyB(SignRecord signRecord, String flag) {
        String userId = signRecord.getUserId();
        signRecord = signRecordBiz.getOne(new HashMap<String, Object>() {
            private static final long serialVersionUID = -3690281076506464659L;

            {
                put("userId", userId);
            }});
        String templateId = existTemplateId(signRecord, merchantFacade);
        String fileId = signRecord.getFileId();
        if (fileId == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约记录未生成签署文件,请进行预签约生成");
        }

        //1、配置文档
        ArrayList<Doc> docs = Lists.newArrayList();
        docs.add(new Doc().setFileId(fileId));
        //2、配置流程
        FlowConfigInfo flowConfigInfo = new FlowConfigInfo();
        flowConfigInfo.setSignPlatform("1");// 1-开放服务h5 2-支付宝签
        FlowInfo flowInfo = new FlowInfo()
                .setBusinessScene("自由职业者共享服务协议")
                //启用自动归档
                .setAutoArchive(true)
                .setAutoInitiate(true)
                .setFlowConfigInfo(flowConfigInfo);

        //查询模版 签署区位置
        EsignResVo<SignTemplateResDataVoV3> templateRes = channelSignFacade.getSignTemplate(new SignTemplateReqVo(templateId));

//        SignTemplate signTemplate = signTemplateBiz.get(templateId);
//        List<StructComponent> style = signTemplate.getStyle();

//        if (!Objects.equals(templateRes.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
//            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(templateRes.getMessage());
//        }

        //        }
        List<StructComponentV3> components = templateRes.getData().getComponents();
        List<StructComponentV3> aSignFile = components.stream()
                .filter(x -> ObjectUtil.equal(x.getComponentSpecialAttribute().getSignerRole(), "甲方签署区")).collect(Collectors.toList());


//        List<StructComponent> aSignFile = style.stream().filter(
//                component -> "甲方签署区".equals(component.getContext().getLabel())
//        ).collect(Collectors.toList());


        if (CollectionUtils.isEmpty(aSignFile)) {
            throw ApiExceptions.API_SIGN_TEMPLATE_FAIL.newWithErrMsg("找不到签署区");
        }

        // 3、配置签署区域
        ArrayList<CreateByFileReqVo.SignFields> orgSignfields = Lists.newArrayList();

        DataDictionary dictionary = dataDictionaryFacade.getDataDictionaryByName("EsignSealsEnum");
        List<DataDictionary.Item> itemList = dictionary.getItemList();
        String mainstayNo = signRecord.getMainstayNo();
        DataDictionary.Item item = itemList.stream().filter(x -> ObjectUtil.equal(x.getCode(), mainstayNo)).findFirst().orElse(null);


        for (StructComponentV3 structComponent : aSignFile) {
            ComponentPosition componentPosition = structComponent.getComponentPosition();

            orgSignfields.add(new CreateByFileReqVo.SignFields()
                    .setFileId(fileId)
                    .setNormalSignFieldConfig(new CreateByFileReqVo.NormalSignFieldConfig().setFreeMode(false)
                            .setAutoSign(true)
                            .setAssignedSealId(ObjectUtil.isNotEmpty(item)?item.getFlag():null)
                            .setSignFieldStyle(structComponent.getNormalSignField().getSignFieldStyle())
                            .setSignFieldPosition(new CreateByFileReqVo.SignFieldPosition().setPositionPage(String.valueOf(componentPosition.getComponentPageNum()))
                                    .setPositionX(componentPosition.getComponentPositionX())
                                    .setPositionY(componentPosition.getComponentPositionY()))));//构造企业signfields参数对象,用于后续入参使用,支持链式入参
        }
        // 配置签署人并指定相应区域
        ArrayList<CreateByFileReqVo.Signer> signers = new ArrayList<>();
        //查询代征主体的信息
        Merchant mainstay = merchantFacade.getByMchNo(signRecord.getMainstayNo());
        if (mainstay == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[发起签约] 不存在相应代征主体,userId:[" + userId + "],mainstayNo:[" + signRecord.getMainstayNo() + "]");
        }
//        String accountId2 = mainstay.getJsonEntity().getSignAccountId();
//        String orgId = mainstay.getJsonEntity().getSignOrgId();
//        if (accountId2 == null || orgId == null) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[发起签约] userId:" + userId +
//                    "代征主体信息不完整 MainstayNo[" + signRecord.getMainstayNo() + "],accountId:[" + accountId2 + "],orgId:[" + orgId + "]");
//        }
        EsignResVo<OrgIdentityInfoResDataVo> orgIdentityInfoV3 = channelSignFacade.getOrgIdentityInfoV3(new OrgIdentityInfoResDateVo().setOrgName(mainstay.getMchName()));
        if (!Objects.equals(orgIdentityInfoV3.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(orgIdentityInfoV3.getMessage());
        }


        signers.add(new CreateByFileReqVo.Signer()
                .setSignerType(1)
//                .setOrgSignerInfo(new CreateByFileReqVo.OrgSignerInfo()
//                        .setOrgId(orgIdentityInfoV3.getData().getOrgId()))
                .setSignFields(orgSignfields)
        );
//        CreateFlowOneStepReqVo createFlowOneStepReqVo = new CreateFlowOneStepReqVo(docs, flowInfo, signers);
//        EsignResVo<CreateFlowOneStepResDataVo> resVo = channelSignFacade.createFlowOneStep(createFlowOneStepReqVo);



        //1、设置待签署文件信息
        CreateByFileReqVo createFlowOneStepReqVo = new CreateByFileReqVo();
        createFlowOneStepReqVo.setDocs(Collections.singletonList(new CreateByFileReqVo.CreateByFileDoc()
                .setFileId(fileId)));
        //2、签署流程配置项
        createFlowOneStepReqVo.setSignFlowConfig(new CreateByFileReqVo.SignFlowConfig()
                .setSignFlowTitle(flowInfo.getBusinessScene())
                .setAuthConfig(new CreateByFileReqVo.AuthConfig().setPsnAvailableAuthModes(Arrays.asList("PSN_MOBILE3")))
                .setAutoFinish(true)
                .setNotifyUrl(signNotifyUrl));
        //3、配置签署方的信息
        createFlowOneStepReqVo.setSigners(signers);



        EsignResVo<CreateByFileResDateVo> resVo = channelSignFacade.createByFile(createFlowOneStepReqVo);

        if (!Objects.equals(resVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg(resVo.getMessage());
        }
        String flowId = resVo.getData().getSignFlowId();
        signRecord.setFlowId(flowId);
        String fileUrl = null;
        try {
            fileUrl = fetchUrl(flowId, fileId,flag);
            byte[] fileArray =  FileUtils.download(fileUrl);
            fileUrl = fastdfsClient.uploadFile(fileArray, flowId + FILE_NAME);
        } catch (ExecutionException | RetryException | IOException e) {
            log.error("[{}]获取链接失败", flag, e);
            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg("获取链接失败");
        }
        signRecord.setFileUrl(fileUrl);
        signRecordBiz.update(signRecord);
        return fileUrl;
    }

    public static final int RETRY = 5;
    public static final long INITIAL_SLEEP_TIME = 3;
    public static final long INCREMENT = 5;
    private String fetchUrl(String flowId, String fileId,String flag) throws ExecutionException, RetryException {
        Retryer<String> retry = new RetryConfig<String>().retryConfig(RETRY, INITIAL_SLEEP_TIME, INCREMENT, TimeUnit.SECONDS);
        //定义请求实现
        Callable<String> callable = () -> {
            DocumentDownloadReqVo documentDownloadReqVo = new DocumentDownloadReqVo(flowId);
            EsignResVo<DocumentDownloadResDataVo> res = channelSignFacade.getDocumentDownloadUrl(documentDownloadReqVo);
            if (res.getData() == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "-e签宝查询合同失败");
            }
            if(Objects.equals(res.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
                return res.getData().getFiles().get(0).getDownloadUrl();
            } else {

                PreviewFileDownloadReqVo previewFileDownloadReqVo = new PreviewFileDownloadReqVo(flowId,fileId);
                EsignResVo<PreviewFileDownloadResDataVo> previewDownloadUrl = channelSignFacade.getPreviewDownloadUrl(previewFileDownloadReqVo);
                if (previewDownloadUrl.getData() == null) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(flag + "-e签宝查询合同失败");
                }
                if(Objects.equals(previewDownloadUrl.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
                    return previewDownloadUrl.getData().getFileDownloadUrl();
                }

                log.error("[{}] 请求参数: {} 查询合同失败: {}", flag, flowId, JSONObject.toJSONString(res));
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("e签宝查询合同失败");
            }
        };
        return retry.call(callable);
    }

    /**
     * 查找插入签名图片的最终位置, TODO 根据第一次关键字查找位置，并返回该关键字之后的坐标位置
     * 目前暂时写死了
     * @return: float[0]:页码，float[1]:最后一个字的x坐标，float[2]:最后一个字的y坐标
     */
    public static float[] getAddImagePositionXY() {
        float[] temp = new float[3];
        temp[0] = 4;
        temp[1] = 224;
        temp[2] = 160;
        return temp;
    }

    public static float[] getAddImagePositionXY2() {
        float[] temp = new float[3];
        temp[0] = 5;
        temp[1] = 210;
        temp[2] = 191;
        return temp;
    }

    // 检测合并是否顺利
    public void check() {
        return;
    }

    public boolean auth(SignRecord record) {

        Merchant employer = merchantFacade.getByMchNo(record.getEmployerNo());
        Merchant mainstay = merchantFacade.getByMchNo(record.getMainstayNo());
        if (mainstay == null) {
            log.error("代征主体: [" + record.getMainstayNo() + "] 不存在");
            return false;
        }
        if (employer == null) {
            log.error("员工: [" + record.getEmployerNo() + "] 不存在");
            return false;
        }
        record.setMainstayName(mainstay.getMchName());
        String logFlag = String.join("-", record.getEmployerNo(), record.getUserId());

        Map<String, Object> param = ESignUtil.buildParam(record);
        SignRecord dbRecord = signRecordBiz.getOne(param);

        // 鉴权
        try {
            limitBiz.signAuth(record.getReceiveNameDecrypt(), record.getReceiveIdCardNoDecrypt(), record.getReceivePhoneNoDecrypt(), logFlag);
        } catch (BizException e) {
            if (CommonExceptions.GET_LOCK_ERROR.getApiErrorCode().equals(e.getApiErrorCode())) {
                log.error("并发请求拦截,请重试:", e);
                return false;
            }
            // 鉴权失败也要保存
            if (dbRecord == null) {
                log.error("鉴权失败: ", e);
                record.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                record.setErrCode(e.getApiErrorCode());
                record.setErrMsg(e.getErrMsg());
                if (ApiExceptions.API_SIGN_AUTH_FAIL.getApiErrorCode().equals(e.getApiErrorCode())) {
                    record.setOperateStatus(OperateStatusEnum.MODIFY_CONTENT.getStatus());  // 修改三要素
                }
                signRecordBiz.insert(record);
            } else {
                if (record.getSignType() != null) {
                    dbRecord.setSignType(record.getSignType());
                }
                if (record.getReceivePhoneNoDecrypt() == null) {
                    dbRecord.setReceivePhoneNo(null);
                    dbRecord.setReceivePhoneNoMd5(null);
                } else {
                    dbRecord.setReceivePhoneNoEncrypt(record.getReceivePhoneNoDecrypt());
                }
                dbRecord.setUserId(record.getUserId());
                dbRecord.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                dbRecord.setErrCode(e.getApiErrorCode());
                dbRecord.setErrMsg(e.getErrMsg());
                dbRecord.setUserId(record.getUserId());
                dbRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
                dbRecord.setUpdateTime(new Date());
                if (ApiExceptions.API_SIGN_AUTH_FAIL.getApiErrorCode().equals(e.getApiErrorCode())) {
                    dbRecord.setOperateStatus(OperateStatusEnum.MODIFY_CONTENT.getStatus());  // 修改三要素
                }
                update(dbRecord);
            }

            return false;
        }
        if (dbRecord == null) {
            record.setInfoStatus(SuccessFailCodeEnum.SUCCESS.getValue());
            signRecordBiz.insert(record);
        } else {
            if (record.getReceivePhoneNoDecrypt() == null) {
                dbRecord.setReceivePhoneNo(null);
                dbRecord.setReceivePhoneNoMd5(null);
            } else {
                dbRecord.setReceivePhoneNoEncrypt(record.getReceivePhoneNoDecrypt());
            }
            if (record.getSignType() != null) {
                dbRecord.setSignType(record.getSignType());
            }
            if (StringUtils.isNotBlank(record.getIdCardFrontUrl())) {
                dbRecord.setIdCardFrontUrl(record.getIdCardFrontUrl());
            }
            if (StringUtils.isNotBlank(record.getIdCardBackUrl())) {
                dbRecord.setIdCardBackUrl(record.getIdCardBackUrl());
            }
            if (StringUtils.isNotBlank(record.getIdCardCopyUrl())) {
                dbRecord.setIdCardCopyUrl(record.getIdCardCopyUrl());
            }
            if (StringUtils.isNotBlank(record.getCerFaceUrl())) {
                dbRecord.setCerFaceUrl(record.getCerFaceUrl());
            }
            dbRecord.setInfoStatus(SuccessFailCodeEnum.SUCCESS.getValue());
            dbRecord.setUserId(record.getUserId());
            if (dbRecord.getSignStatus().intValue() != SignStatusEnum.SIGN_SUCCESS.getValue()) {
                dbRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
            }
            dbRecord.setUpdateTime(new Date());
            update(dbRecord);
        }

        return true;
    }

    public boolean authNew(SignRecord record) {

        Merchant employer = merchantFacade.getByMchNo(record.getEmployerNo());
        Merchant mainstay = merchantFacade.getByMchNo(record.getMainstayNo());
        if (mainstay == null) {
            log.error("代征主体: [" + record.getMainstayNo() + "] 不存在");
            throw  ApiExceptions.API_BIZ_FAIL.newWithErrMsg("代征主体: [" + record.getMainstayNo() + "] 不存在");
        }
        if (employer == null) {
            log.error("员工: [" + record.getEmployerNo() + "] 不存在");
            throw  ApiExceptions.API_BIZ_FAIL.newWithErrMsg("用工企业: [" + record.getEmployerNo() + "] 不存在");
        }
        record.setMainstayName(mainstay.getMchName());
        String logFlag = String.join("-", record.getEmployerNo(), record.getUserId());

        Map<String, Object> param = ESignUtil.buildParam(record);
        SignRecord dbRecord = signRecordBiz.getOne(param);

        // 鉴权
        try {
            limitBiz.signAuth(record.getReceiveNameDecrypt(), record.getReceiveIdCardNoDecrypt(), record.getReceivePhoneNoDecrypt(), logFlag);
        } catch (BizException e) {
            if (CommonExceptions.GET_LOCK_ERROR.getSysErrorCode()==e.getSysErrorCode()) {
                throw e;
            }
            // 鉴权失败也要保存
            if (dbRecord == null) {
                log.error("鉴权失败: ", e);
                record.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                record.setErrCode(e.getApiErrorCode());
                record.setErrMsg(e.getErrMsg());
                if (ApiExceptions.API_SIGN_AUTH_FAIL.getApiErrorCode().equals(e.getApiErrorCode())) {
                    record.setOperateStatus(OperateStatusEnum.MODIFY_CONTENT.getStatus());  // 修改三要素
                }
                signRecordBiz.insert(record);
            } else {
                if (record.getSignType() != null) {
                    dbRecord.setSignType(record.getSignType());
                }
                if (record.getReceivePhoneNoDecrypt() == null) {
                    dbRecord.setReceivePhoneNo(null);
                    dbRecord.setReceivePhoneNoMd5(null);
                } else {
                    dbRecord.setReceivePhoneNoEncrypt(record.getReceivePhoneNoDecrypt());
                }
                dbRecord.setUserId(record.getUserId());
                dbRecord.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
                dbRecord.setErrCode(e.getApiErrorCode());
                dbRecord.setErrMsg(e.getErrMsg());
                dbRecord.setUserId(record.getUserId());
                dbRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
                dbRecord.setUpdateTime(new Date());
                if (ApiExceptions.API_SIGN_AUTH_FAIL.getApiErrorCode().equals(e.getApiErrorCode())) {
                    dbRecord.setOperateStatus(OperateStatusEnum.MODIFY_CONTENT.getStatus());  // 修改三要素
                }
                update(dbRecord);
            }

            return false;
        }
        if (dbRecord == null) {
            record.setInfoStatus(SuccessFailCodeEnum.SUCCESS.getValue());
            signRecordBiz.insert(record);
        } else {
            if (record.getReceivePhoneNoDecrypt() == null) {
                dbRecord.setReceivePhoneNo(null);
                dbRecord.setReceivePhoneNoMd5(null);
            } else {
                dbRecord.setReceivePhoneNoEncrypt(record.getReceivePhoneNoDecrypt());
            }
            if (record.getSignType() != null) {
                dbRecord.setSignType(record.getSignType());
            }
            if (StringUtils.isNotBlank(record.getIdCardFrontUrl())) {
                dbRecord.setIdCardFrontUrl(record.getIdCardFrontUrl());
            }
            if (StringUtils.isNotBlank(record.getIdCardBackUrl())) {
                dbRecord.setIdCardBackUrl(record.getIdCardBackUrl());
            }
            if (StringUtils.isNotBlank(record.getIdCardCopyUrl())) {
                dbRecord.setIdCardCopyUrl(record.getIdCardCopyUrl());
            }
            if (StringUtils.isNotBlank(record.getCerFaceUrl())) {
                dbRecord.setCerFaceUrl(record.getCerFaceUrl());
            }
            dbRecord.setInfoStatus(SuccessFailCodeEnum.SUCCESS.getValue());
            dbRecord.setUserId(record.getUserId());
            if (dbRecord.getSignStatus().intValue() != SignStatusEnum.SIGN_SUCCESS.getValue()) {
                dbRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
            }
            dbRecord.setUpdateTime(new Date());
            update(dbRecord);
        }

        return true;
    }

    public void update(SignRecord record) {
        signRecordBiz.update(record);

        SignRecord signRecord = signRecordBiz.getOne(Collections.singletonMap("userId", record.getUserId()));
        userInfoBiz.insertOrUpdate(new UserInfo().buildUserInfo(signRecord));

//        try {
//            this.syncToExteranlSystem(record);
//        } catch (Exception e) {
//            log.error("同步信息到供应商失败:{}", e.getMessage());
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("同步签约信息到供应商失败");
//        }
    }

    public static void main(String[] args) {
        String userId = MD5Util.getMixMd5Str("S000019" + "M00000175" + "440785199204182534" + "冯在进");
        System.out.println(userId);
    }

}
