package com.zhixianghui.service.trade.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class WithdrawRecordDao extends MyBatisDao<WithdrawRecord,Long> {
    public Map<String, Object> sumCmbWithdrawAmt(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(this.fillSqlId("sumCmbWithdrawAmt"), paramMap);
    }

    public Map<String, Object> sumWithdrawRecord(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(this.fillSqlId("sumWithdrawRecord"), paramMap);
    }
}
