package com.zhixianghui.service.trade.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.zhixianghui.common.util.utils.AssertUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.export.dto.TimeRangeDto;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public abstract class TaskRocketMQListener<T extends Map<String,Object>> extends BaseRocketMQListener<T> {

    @Reference
    private RobotFacade robotFacade;

    @Override
    public void  validateJsonParam(T jsonParam) {
        AssertUtil.notNull(jsonParam, "定时任务入参不能为空");
        log.info("定时任务入参：{}",JSONObject.toJSONString(jsonParam));
    }

    /**
     * 定时任务业务处理方法
     * @param jsonParam
     */
    public abstract void runTask(T jsonParam);

    /**
     * 失败操作
     */
    public void onFail(T param, Throwable throwable) {
        log.error("定时任务执行异常",throwable);

        StringBuffer sb = new StringBuffer("#### 定时任务执行异常 ");
        sb.append("\\n > 任务名称：").append(param.get("taskName"))
                .append("\\n > 执行时间：").append(DateUtil.formatDateTime(new Date()))
                .append("\\n > 异常信息：").append(throwable.getMessage());

        MarkDownMsg msg = new MarkDownMsg();
        msg.setRobotType(RobotTypeEnum.TASK_NOTIFY_ROBOT.getType());
        msg.setUnikey(IdWorker.get32UUID());
        msg.setContent(sb.toString());

        robotFacade.pushMarkDownAsync(msg);
    }

    /**
     * 成功操作
     */
    public void onSuccess(T param) {
        log.info("定时任务执行成功");
    }

    @Override
    public void consumeMessage(T jsonParam){
        try {
            runTask(jsonParam);
            onSuccess(jsonParam);
        } catch (Exception e) {
            try {
                onFail(jsonParam,e);
            } catch (Exception exception) {
                log.error("发送定时任务执行失败消息异常",e);
            }
            throw e;
        }
    }

    protected TimeRangeDto getTimeRange(T paramMap) {
        String tradeTimeBegin = (String)paramMap.get("tradeTimeBegin");
        String tradeTimeEnd = (String)paramMap.get("tradeTimeEnd");
        String processType = paramMap.get("processType") == null ? "1" : (String) paramMap.get("processType"); //0 每日执行 1 每月执行

        // 没有指定时间，则取上月
        if(StringUtil.isEmpty(tradeTimeBegin) || StringUtil.isEmpty(tradeTimeEnd)){
            if ("0".equals(processType)) {
                Date yesterday = DateUtil.addDay(new Date(), -1);
                tradeTimeBegin = DateUtil.formatDateTime(DateUtil.getDayStart(yesterday));
                tradeTimeEnd = DateUtil.formatDateTime(DateUtil.getDayEnd(yesterday));
            }else if ("-1".equals(processType)) {
                Date now = new Date();
                tradeTimeBegin = DateUtil.formatDateTime(DateUtil.getDayStart(now));
                tradeTimeEnd = DateUtil.formatDateTime(DateUtil.getDayEnd(now));
            }else {
                Date now = new Date();
                tradeTimeBegin = DateUtil.formatDateTime(DateUtil.getDayStart(DateUtil.getFirstDayOfLastMonth(now)));
                tradeTimeEnd = DateUtil.formatDateTime(DateUtil.getDayEnd(DateUtil.getLastDayOfLastMonth(now)));
            }
        } else {
            tradeTimeBegin = DateUtil.formatDateTime(DateUtil.getDayStart(DateUtil.parse(tradeTimeBegin)));
            tradeTimeEnd = DateUtil.formatDateTime(DateUtil.getDayEnd(DateUtil.parse(tradeTimeEnd)));
        }

        return  new TimeRangeDto(DateUtil.parseTime(tradeTimeBegin), DateUtil.parseTime(tradeTimeEnd));
    }
}
