package com.zhixianghui.service.trade.process;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.enums.TaxPayerEnum;
import com.zhixianghui.facade.merchant.service.MerchantCkhQuoteFacade;
import com.zhixianghui.facade.merchant.service.PersonalIncomeTaxFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.trade.entity.OrderItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName CKHAcceptBiz
 * @Description TODO
 * @Date 2022/6/28 18:38
 */
@Slf4j
@Service
public class CKHAcceptBiz extends AbstractAcceptHandler{

    @Reference
    private MerchantCkhQuoteFacade merchantCkhQuoteFacade;

    @Reference
    private PersonalIncomeTaxFacade personalIncomeTaxFacade;

    @Override
    public void calculateFee(OrderItem orderItem) {
        //查看报价规则
        MerchantCkhQuote merchantCkhQuote = merchantCkhQuoteFacade.getFeeRate(orderItem.getEmployerNo(),orderItem.getMainstayNo(),orderItem.getProductNo());
        if (merchantCkhQuote == null){
            log.error("找不到对应的报价单，订单批次号：[{}]",orderItem.getPlatBatchNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("计费异常");
        }
        calculate(merchantCkhQuote,orderItem);
    }

    private void calculate(MerchantCkhQuote merchantCkhQuote, OrderItem orderItem) {
        //TODO 需求变更，暂时个税和增值税都为0
        //计算服务费
        BigDecimal serviceFee = AmountUtil.mul(orderItem.getOrderItemTaskAmount(), merchantCkhQuote.getServiceFeeRate(), 2);
        //最低费用0.01元
//        if (serviceFee.compareTo(BigDecimal.ZERO) != 1){
//            serviceFee = new BigDecimal("0.01");
//        }
        //获取匹配的个税公式
//        PersonalIncomeTax personalIncomeTax = personalIncomeTaxFacade.getSuitTax(orderItem.getOrderItemTaskAmount());
//        log.info("订单号：[{}]，匹配到的个税费率：[{}]，速算扣除数：[{}]",
//                orderItem.getPlatTrxNo(),personalIncomeTax.getTaxRate(),personalIncomeTax.getDeduction());

        //基础费用
        BigDecimal addedFee = AmountUtil.div(orderItem.getOrderItemTaskAmount(),AmountUtil.add(BigDecimal.ONE,merchantCkhQuote.getAddedTaxRate()));
        //乘以费率
        BigDecimal taxFee = AmountUtil.mul(addedFee,BigDecimal.ZERO);
        //劳务报酬需要再乘0.8
//        if (merchantCkhQuote.getTaxFormula().intValue() == TaxFormulaEnum.REMUNERATION_INCOME.getValue()){
//            taxFee = AmountUtil.mul(taxFee,new BigDecimal(0.8));
//        }

        //最终减去速算扣除数
        taxFee = AmountUtil.sub(taxFee,BigDecimal.ZERO);
        //根据个税承担方再进行赋值
        orderItem.setOrderItemFee(serviceFee);
        orderItem.setOrderItemTaxAmount(taxFee);
        if (merchantCkhQuote.getTaxPayer().intValue() == TaxPayerEnum.EMPLOYEE.getValue()){
            //个税由自由者承担
            //实发 = 任务 - 个税，订单总额 = 任务 + 服务费
            orderItem.setOrderItemAmount(AmountUtil.add(serviceFee,orderItem.getOrderItemTaskAmount()));
            orderItem.setOrderItemNetAmount(AmountUtil.sub(orderItem.getOrderItemTaskAmount(),taxFee));
        }else{
            //个税由用工企业承担
            //实发 = 任务，订单总额 = 任务 + 服务费 + 个税
            orderItem.setOrderItemAmount(AmountUtil.add(serviceFee,taxFee,orderItem.getOrderItemTaskAmount()));
            orderItem.setOrderItemNetAmount(orderItem.getOrderItemTaskAmount());
        }
    }

    @Override
    public void notifyHandlerException(String platTrxNo) {
        log.info("[{}]==>发送受理异常处理mq", platTrxNo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, UUIDUitl.generateString(10),platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),MessageMsgDest.TAG_ACCEPT_NOT_BIZ_EXCEPTION_CKH,platTrxNo, MsgDelayLevelEnum.S_10.getValue());
    }

    @Override
    public void notifyAcceptStart(String employerNo, String platBatchNo, List<String> platTrxNoList) {
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(platTrxNoList));
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_ACCEPT_CKH, JSON.toJSONString(infoMap));
    }

    @Override
    public void notifyAcceptBatchCount(String platBatchNo) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_ACCEPT_BATCH_COUNT_CKH,platBatchNo);
    }
}
