package com.zhixianghui.service.trade.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.trade.constant.UniqueKeyConst;
import com.zhixianghui.facade.trade.dto.FreezeBalanceDTO;
import com.zhixianghui.facade.trade.dto.WithdrawAccountParamDTO;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.enums.DynamicMsgEnum;
import com.zhixianghui.facade.trade.enums.WithdrawStatusEnum;
import com.zhixianghui.service.trade.dao.WithdrawRecordDao;
import com.zhixianghui.service.trade.dao.mapper.WithdrawRecordMapper;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.service.trade.process.WithdrawAcctDetailBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class WithdrawRecordBiz extends ServiceImpl<WithdrawRecordMapper, WithdrawRecord> {

    final private WithdrawRecordMapper mapper;
    final private WithdrawRecordDao dao;
    private final TradeHelperBiz tradeHelperBiz;
    private final TradeUniqueBiz tradeUniqueBiz;
    private final DynamicMsgBiz dynamicMsgBiz;
    private final WithdrawAcctDetailBiz withdrawAcctDetailBiz;
    private final CmbMerchantBalanceBiz cmbMerchantBalanceBiz;

    public WithdrawRecord getByWithdrawRecordNo(String withdrawNo) {
        return this.getOne(new QueryWrapper<WithdrawRecord>().eq(WithdrawRecord.COL_WITHDRAW_NO,withdrawNo));
    }

    public WithdrawRecord addWithdrawRecord(WithdrawRecord withdrawRecord) {
        String withdrawNo = withdrawRecord.getWithdrawNo();
        tradeUniqueBiz.insertByKey(UniqueKeyConst.RECHARGE_NO + withdrawNo);
        boolean saved = this.save(withdrawRecord);
        if (!saved) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("保存充值记录失败");
        }

        WithdrawRecord withdrawRecordNew = this.getOne(new QueryWrapper<WithdrawRecord>().eq(WithdrawRecord.COL_WITHDRAW_NO, withdrawNo));
        if (withdrawRecordNew.getWithdrawStatus().equals(WithdrawStatusEnum.SUCCESS.getCode())) {
            withdrawAcctDetailBiz.save2CkAcctDetail(withdrawRecordNew);
        }
        return withdrawRecordNew;
    }

    public void handleNotify(String outBizNo,String bankTrxNo, String bankOrderId, SuccessFailEnum successFailEnum,String failReason) {

        final WithdrawRecord withdrawRecord = this.getOne(new QueryWrapper<WithdrawRecord>().eq(WithdrawRecord.COL_WITHDRAW_NO, outBizNo));
        if (Objects.isNull(withdrawRecord)) {
            log.warn("[{}]原订单不存在", outBizNo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[{"+outBizNo+"}]原订单不存在");
        }
        if (successFailEnum == SuccessFailEnum.SUCCESS) {
            withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.SUCCESS.getCode());
            withdrawRecord.setUpdateTime(new Date());
            this.updateById(withdrawRecord);
            if (withdrawRecord.getMerchantType().intValue() == MerchantTypeEnum.EMPLOYER.getValue()){
                this.withdrawAcctDetailBiz.save2CkAcctDetail(withdrawRecord);
                dynamicMsgBiz.putDynamicMsg(withdrawRecord.getEmployerNo(),String.format(
                        DynamicMsgEnum.WITHDRAW.getMsg(),withdrawRecord.getAmount(),withdrawRecord.getWithdrawNo()));
            }
            // 处理招行本地账户，提现
            if(StringUtils.equals(withdrawRecord.getChannelNo(), ChannelNoEnum.CMB.name())) {
                WithdrawAccountParamDTO withdrawParam = new WithdrawAccountParamDTO();
                withdrawParam.setMchNo(withdrawRecord.getEmployerNo());
                withdrawParam.setMchName(withdrawRecord.getEmployerName());
                withdrawParam.setMainstayNo(withdrawRecord.getMainstayNo());
                withdrawParam.setMainstayName(withdrawRecord.getMainstayName());
                withdrawParam.setMerchantType(withdrawRecord.getMerchantType());
                withdrawParam.setPlatTrxNo(withdrawRecord.getWithdrawNo());
                withdrawParam.setAmount(withdrawRecord.getAmount());
                cmbMerchantBalanceBiz.withdrawAccount(withdrawParam);
            }
        }else {
            withdrawRecord.setErrorMsg(failReason);
            withdrawRecord.setWithdrawStatus(WithdrawStatusEnum.FAIL.getCode());
            withdrawRecord.setUpdateTime(new Date());
            this.updateById(withdrawRecord);
            //处理招行本地账户，解冻金额
            if(StringUtils.equals(withdrawRecord.getChannelNo(), ChannelNoEnum.CMB.name())) {
                FreezeBalanceDTO freezeBalanceDTO = new FreezeBalanceDTO();
                freezeBalanceDTO.setMchNo(withdrawRecord.getEmployerNo());
                freezeBalanceDTO.setMchName(withdrawRecord.getEmployerName());
                freezeBalanceDTO.setMainstayNo(withdrawRecord.getMainstayNo());
                freezeBalanceDTO.setMainstayName(withdrawRecord.getMainstayName());
                freezeBalanceDTO.setMerchantType(withdrawRecord.getMerchantType());
                freezeBalanceDTO.setPlatTrxNo(withdrawRecord.getWithdrawNo());
                freezeBalanceDTO.setFrozenAmount(withdrawRecord.getAmount());
                cmbMerchantBalanceBiz.unfreezeBalance(freezeBalanceDTO);
            }
        }

    }

    public Map<String, Object> sumCmbWithdrawAmt(Map<String, Object> paramMap) {
        return dao.sumCmbWithdrawAmt(paramMap);
    }

    public Map<String, Object> sumWithdrawRecord(Map<String, Object> paramMap) {
        return dao.sumWithdrawRecord(paramMap);
    }
}
