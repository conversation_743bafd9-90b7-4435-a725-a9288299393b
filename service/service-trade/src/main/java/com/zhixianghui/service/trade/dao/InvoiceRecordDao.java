package com.zhixianghui.service.trade.dao;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.entity.TradeUnique;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * 开票记录
 * <AUTHOR>
 * @date 2020-12-28
 */
@Repository
public class InvoiceRecordDao extends MyBatisDao<InvoiceRecord,Long> {

    public InvoiceRecord getByTrxNo(String trxNo) {
        LimitUtil.notEmpty(trxNo, "流水号不能为空");
        return getOne(ImmutableMap.of("trxNo", trxNo));
    }

    public Map<String,Object> countInvoiceAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(this.fillSqlId("countInvoiceAmount"),paramMap);
    }
}
