package com.zhixianghui.service.trade.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.data.AltSourceEnum;
import com.zhixianghui.common.statics.enums.data.AltTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.pay.CKHPayFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.facade.data.service.MysqlCkAccountDetailFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.entity.FeeOrderItem;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.enums.*;
import com.zhixianghui.service.trade.dao.mapper.OfflineOrderMapper;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FeePayBiz
 * @Description TODO
 * @Date 2022/7/7 16:46
 */
@Slf4j
@Service
public class FeePayBiz {

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Autowired
    private FeeOrderItemBiz feeOrderItemBiz;

    @Autowired
    private FeeOrderBatchBiz feeOrderBatchBiz;

    @Autowired
    private OfflineOrderMapper offlineOrderMapper;

    @Autowired
    private CommonBiz commonBiz;

    @Reference
    private CKHPayFacade ckhPayFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @Autowired
    private RedisClient redisClient;

    @Reference
    private MysqlCkAccountDetailFacade ckAccountDetailFacade;

    public PayRespVo payFee(String feeOrderItemNo) {
        return payFee(feeOrderItemNo, true);
    }

    public PayRespVo payFee(String feeOrderItemNo, boolean errorRetry) {
        FeeOrderItem feeOrderItem = feeOrderItemBiz.getByItemNo(feeOrderItemNo);
        if (feeOrderItem == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单明细不存在");
        }
        if (feeOrderItem.getPayAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付金额不能为0");
        }

        PayRespVo payRespVo = new PayRespVo();
        if (feeOrderItem.getStatus().intValue() == FeeOrderItemStatusEnum.SUCCESS.getCode()) {
            log.info("账单：[{}] 已完成支付，无须重复支付");
            payRespVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
            return payRespVo;
        }
        EmployerAccountInfo employerAccountInfo = check(feeOrderItem);
        PayReqVo payReqVo = buildReqVo(employerAccountInfo, feeOrderItem);
        try {
            payRespVo = ckhPayFacade.payServiceFee(payReqVo);
            paySuccess(payRespVo, feeOrderItem);
        } catch (BizException e) {
            payFail(payRespVo, feeOrderItem, e.getErrMsg());
        } catch (Exception e) {
            log.error("交易失败，异常信息", e);
            if (errorRetry) {
                errorRetry(feeOrderItem);
            }
            payFail(payRespVo, feeOrderItem, "交易失败，请稍后重试");
        }
        return payRespVo;
    }

    private void paySuccess(PayRespVo payRespVo, FeeOrderItem feeOrderItem) {
        payRespVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
        payRespVo.setBankOrderNo(feeOrderItem.getFeeItemNo());
        payRespVo.setBankBatchNo(feeOrderItem.getFeeBatchNo());

        feeOrderItem.setStatus(FeeOrderItemStatusEnum.SUCCESS.getCode());
        feeOrderItem.setCompleteTime(new Date());
        feeOrderItemBiz.update(feeOrderItem);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_CKH_FEE_ASYNC, feeOrderItem.getEmployerNo(), feeOrderItem.getFeeItemNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_CKH_FEE_BATCH_UPDATE, JsonUtil.toString(payRespVo));

        ckAccountDetailFacade.saveOne(buildCkAccountDetail(feeOrderItem));
    }

    private void errorRetry(FeeOrderItem feeOrderItem) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_CKH_FEE_ASYNC, feeOrderItem.getEmployerNo(), feeOrderItem.getFeeItemNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_CKH_FEE_EXCEPTION_RETRY, feeOrderItem.getFeeItemNo());
    }

    private void payFail(PayRespVo payRespVo, FeeOrderItem feeOrderItem, String errMsg) {
        payRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
        payRespVo.setBankOrderNo(feeOrderItem.getFeeItemNo());
        payRespVo.setBankBatchNo(feeOrderItem.getFeeBatchNo());
        payRespVo.setBizMsg(errMsg);

        feeOrderItem.setStatus(FeeOrderItemStatusEnum.FAIL.getCode());
        feeOrderItem.setFailReason(errMsg);
        feeOrderItemBiz.update(feeOrderItem);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_CKH_FEE_ASYNC, feeOrderItem.getEmployerNo(), feeOrderItem.getFeeItemNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_CKH_FEE_BATCH_UPDATE, JsonUtil.toString(payRespVo));
    }

    private PayReqVo buildReqVo(EmployerAccountInfo employerAccountInfo, FeeOrderItem feeOrderItem) {
        PayReqVo payReqVo = new PayReqVo();
        //固定只有支付宝记账本
        payReqVo.setChannelNo(ChannelNoEnum.ALIPAY.name());
        payReqVo.setChannelType(ChannelTypeEnum.ALIPAY.getValue());

        payReqVo.setParentMerchantNo(employerAccountInfo.getParentMerchantNo());
        payReqVo.setPayeeAgreementNo(employerAccountInfo.getParentAgreementNo());
        payReqVo.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
        payReqVo.setPayerAgreementNo(employerAccountInfo.getSubAgreementNo());
        payReqVo.setReceiveAmount(feeOrderItem.getPayAmount().toString());
        payReqVo.setBankOrderNo(feeOrderItem.getFeeItemNo());
        payReqVo.setRemitRemark(FeeTypeEnum.getEnum(feeOrderItem.getFeeType()).getDesc());
        return payReqVo;
    }

    private EmployerAccountInfo check(FeeOrderItem feeOrderItem) {
        //查询账单批次
        FeeOrderBatch feeOrderBatch = feeOrderBatchBiz.getByBatchNo(feeOrderItem.getFeeBatchNo());

        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(
                feeOrderItem.getEmployerNo(), feeOrderItem.getMainstayNo(), feeOrderBatch.getChannelType());
        if (employerAccountInfo == null) {
            log.info("订单支付失败，FeeItemNo:[{}]  EmployerNo:[{}] MainstayNo:[{}]", feeOrderItem.getFeeItemNo(), feeOrderItem.getEmployerNo(), feeOrderItem.getMainstayNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业帐号信息对应通道没有配置");
        }
        if (employerAccountInfo.getStatus().equals(OpenOffEnum.OFF.getValue())) {
            log.info("订单支付失败，FeeItemNo:[{}]  用工企业帐号对应通道配置为关闭", feeOrderItem.getFeeItemNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业帐号对应通道配置为关闭");
        }

        if (StringUtils.isBlank(employerAccountInfo.getParentMerchantNo()) || StringUtils.isBlank(employerAccountInfo.getSubMerchantNo())) {
            log.info("订单支付失败，FeeItemNo:[{}]  用工企业父子编号为空", feeOrderItem.getFeeItemNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业帐号对应通道配置为空");
        }
        return employerAccountInfo;
    }

    /**
     * 异常处理
     *
     * @param feeOrderItemNo
     */
    public void handleException(String feeOrderItemNo) {
        Long grantTimes = redisClient.incr(TradeConstant.FEE_RETRY_PREFIX + feeOrderItemNo);
        redisClient.expire(TradeConstant.FEE_RETRY_PREFIX + feeOrderItemNo, 60 * 60);
        if (grantTimes > TradeConstant.MAX_GRANT_TIMES) {
            log.error("账单：[{}]，支付异常，重试次数超过5次，停止重试", feeOrderItemNo);
            return;
        }
        log.info("账单：[{}]，支付异常，重新发起支付", feeOrderItemNo);
        payFee(feeOrderItemNo);

    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(PayRespVo payRespVo) {
        FeeOrderBatch feeOrderBatch = feeOrderBatchBiz.getByBatchNo(payRespVo.getBankBatchNo());
        if (feeOrderBatch == null) {
            log.error("账单：[{}]，批次不存在，更新信息：[{}]", payRespVo.getBankBatchNo(), JsonUtil.toString(payRespVo));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账单批次不存在");
        }
        //支付失败，更新批次
        if (payRespVo.getBankPayStatus().intValue() == BankPayStatusEnum.FAIL.getValue()) {
            feeOrderBatch.setUpdateTime(new Date());
            feeOrderBatch.setStatus(FeeOrderStatus.FAIL.getCode());
            feeOrderBatchBiz.update(feeOrderBatch);
        } else {
            //根据订单批次查询所有明细
            List<FeeOrderItem> feeOrderItemList = feeOrderItemBiz.getListByBatchNo(payRespVo.getBankBatchNo());
            if (feeOrderItemList.stream().allMatch(x -> x.getStatus().intValue() == FeeOrderItemStatusEnum.SUCCESS.getCode())) {
                //当所有订单均处于完成状态才更新批次
                feeOrderBatch.setStatus(FeeOrderStatus.SUCCESS.getCode());
                feeOrderBatch.setUpdateTime(new Date());
                feeOrderBatch.setCompleteTime(new Date());
                feeOrderBatchBiz.update(feeOrderBatch);

                log.info("[{}]完成订单并更新账单批次状态，准备下载回单...",feeOrderBatch.getFeeBatchNo());

                //下载电子回单,延迟30分钟执行
                notifyFacade.sendOne(MessageMsgDest.TOPIC_FEE_ORDER_CETIFICATE_UPLOAD, feeOrderBatch.getEmployerNo(), feeOrderBatch.getFeeBatchNo(),
                        NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_FEE_ORDER_ONLINE_CETIFICATE_APPLY, feeOrderBatch.getFeeBatchNo(), MsgDelayLevelEnum.M_10.getValue());
                //生成计费订单
                feeOrderBatchBiz.generateFeeOrder(feeOrderBatch);
            }
        }


    }

    private CkAccountDetail buildCkAccountDetail(FeeOrderItem feeOrderItem) {
        CkAccountDetail detail = new CkAccountDetail();
        detail.setAccountNo(feeOrderItem.getMainstayNo());
        detail.setTrxNo(feeOrderItem.getFeeItemNo());
        detail.setAccountName(feeOrderItem.getMainstayName());
        detail.setAltAmount(feeOrderItem.getPayAmount());
        detail.setAltDesc("账单支付");
        detail.setAltSource(AltSourceEnum.REMIT.getValue());
        detail.setAltType(AltTypeEnum.REMIT.getValue());
        detail.setCreateTime(feeOrderItem.getPayTime());
        detail.setChannelType(ChannelTypeEnum.ALIPAY.getValue());
        detail.setPayChannelNo(ChannelNoEnum.ALIPAY.name());
        detail.setPayChannelName(ChannelNoEnum.ALIPAY.getDesc());
        detail.setChannelTrxNo(feeOrderItem.getFeeItemNo());
        detail.setEmployerName(feeOrderItem.getEmployerName());
        detail.setEmployerNo(feeOrderItem.getEmployerNo());
        return detail;
    }
}
