package com.zhixianghui.service.trade.vo.ckh.req;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName UploadResultsReqVo
 * @Description TODO
 * @Date 2022/10/27 9:26
 */
@Data
public class UploadResultsReqVo implements Serializable {

    @NotBlank(message = "job_no 任务编号不能为空")
    private String jobNo;

    @NotBlank(message = "phone_no 雇员手机号不能为空")
    private String phoneNo;

    @NotBlank(message = "text_result 任务结果描述不能为空")
    private String textResult;

}
