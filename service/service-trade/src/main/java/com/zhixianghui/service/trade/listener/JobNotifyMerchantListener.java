package com.zhixianghui.service.trade.listener;

import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.api.base.utils.SignUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.ApiRespCodeEnum;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.notify.NotifyContent;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.enums.JobStatusEnum;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.trade.helper.CacheBiz;
import com.zhixianghui.service.trade.vo.ckh.res.JobCallbackResVo;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName JobNotifyMerchantListener
 * @Description TODO
 * @Date 2022/11/3 11:56
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_JOB_CALLBACK,
        selectorExpression = MessageMsgDest.TAG_JOB_CALLBACK,consumeThreadMax = 3,
        consumerGroup = "jobCallbackConsumer")
public class JobNotifyMerchantListener extends BaseRocketMQListener<String> {

    @Autowired
    private RedisClient redisClient;

    @Autowired
    private CacheBiz cacheBiz;

    @Reference
    private NotifyFacade notifyFacade;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        Job job = JsonUtil.toBean(jsonParam,Job.class);
        String cacheKey = "job:callback:" + job.getJobId();
        if (redisClient.get(cacheKey) == null) {
            //缓存五分钟，期间不会再通知商户
            redisClient.set(cacheKey, job.getJobId(), 300);
            String secKey = UUIDUitl.generateString(16);
            JobCallbackResVo jobCallbackResVo = new JobCallbackResVo();
            jobCallbackResVo.setJobNo(job.getJobId());
            jobCallbackResVo.setJobStatus(String.valueOf(job.getJobStatus()));
            if (job.getJobStatus().intValue() == JobStatusEnum.PROCESSING.getCode().intValue()){
                jobCallbackResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
                jobCallbackResVo.setBizErrMsg("审核通过");
            }else{
                jobCallbackResVo.setBizErrMsg(ApiExceptions.API_AUDIT_FAIL_ERROR.getApiErrorCode());
                jobCallbackResVo.setBizErrMsg("审核失败");
            }

            //封装回调数据
            MerchantNotifyParam merchantNotifyParam = new MerchantNotifyParam();
            merchantNotifyParam.setNotifyContent(new NotifyContent());
            merchantNotifyParam.getNotifyContent().setRespCode(ApiRespCodeEnum.SUCCESS.getCode());
            merchantNotifyParam.getNotifyContent().setData(JsonUtil.toString(jobCallbackResVo));
            merchantNotifyParam.getNotifyContent().setRandStr(UUIDUitl.generateString(32));
            merchantNotifyParam.getNotifyContent().setSignType(String.valueOf(SignTypeEnum.RSA.getValue()));
            merchantNotifyParam.getNotifyContent().setMchNo(job.getEmployerNo());
            MerchantSecret merchantSecret = cacheBiz.getMerchantSecretByMchNo(job.getEmployerNo());
            String priKey = merchantSecret.getPlatformPrivateKey();
            String sign = SignUtil.sign(merchantNotifyParam.getNotifyContent(), Integer.parseInt(merchantNotifyParam.getNotifyContent().getSignType()),priKey);
            merchantNotifyParam.getNotifyContent().setSign(sign);
            merchantNotifyParam.getNotifyContent().setSecKey(RSAUtil.encryptByPublicKey(secKey,merchantSecret.getMerchantPublicKey()));
            merchantNotifyParam.setNotifyUrl(job.getCallbackUrl());
            notifyFacade.sendOne(MessageMsgDest.TOPIC_NOTIFY_MERCHANT,
                    job.getEmployerNo(),
                    job.getJobId(), NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_NOTIFY_MERCHANT,
                    JsonUtil.toString(merchantNotifyParam));
        }
    }
}
