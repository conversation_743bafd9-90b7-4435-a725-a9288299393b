package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.service.OrderReexchangeFacade;
import com.zhixianghui.service.trade.biz.OrderReexchangeBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年02月10日 16:12:00
 */
@Service
public class OrderReexchangeImpl implements OrderReexchangeFacade {

    @Autowired
    private OrderReexchangeBiz orderReexchangeBiz;

    @Override
    public void orderReexchange(String platTrxNo) {
        orderReexchangeBiz.reexchange(platTrxNo);
    }
}