package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.service.trade.dao.InvoiceRecordDao;
import com.zhixianghui.service.trade.vo.res.InvoiceRecordResVo;
import com.zhixianghui.service.trade.vo.res.InvoiceRecordVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* 开票记录表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-11-06
*/
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InvoiceRecordBiz {

    @Value("${file.domain}")
    private String DOMAIN_PREFIX;

    private final InvoiceRecordDao invoiceRecordDao;

    public InvoiceRecord getByTrxNo(String trxNo){
        return invoiceRecordDao.getByTrxNo(trxNo);
    }

    public PageResult<List<InvoiceRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam){
        return invoiceRecordDao.listPage(paramMap, pageParam);
    }

    public long recordsCount(Map<String, Object> paramMap) {
        return invoiceRecordDao.countBy(paramMap);
    }

    public void update(InvoiceRecord record){
        invoiceRecordDao.update(record);
    }

    public void insert(InvoiceRecord record){
        invoiceRecordDao.insert(record);
    }

    public Map<String, Object> countInvoiceAmount(Map<String, Object> paramMap) {
       return invoiceRecordDao.countInvoiceAmount(paramMap);
    }

    public InvoiceRecord listByTimeRange(String startTime,String employerNo,String mainstayNo,Integer invoiceType) {
        Map<String, Object> param = new HashMap<>();
        param.put("tradeCompleteDayBegin", startTime);
        param.put("employerMchNo", employerNo);
        param.put("mainstayMchNo", mainstayNo);
        param.put("invoiceType", invoiceType);

        return invoiceRecordDao.getOne("listByTimeRange", param);
    }

    public InvoiceRecordResVo listRecordApi(Map<String, Object> paramMap) {
        List<InvoiceRecord> recordList = invoiceRecordDao.listBy(paramMap);
        List<InvoiceRecordVo> resVoList = recordList.stream().map(x->{
            InvoiceRecordVo recordVo = new InvoiceRecordVo();
            recordVo.setTrxNo(x.getTrxNo());
            recordVo.setInvoiceType(Integer.toString(x.getInvoiceType()));
            recordVo.setDrawer(x.getMainstayMchName());
            recordVo.setInvoiceCategory(x.getInvoiceCategoryName());
            recordVo.setInvoicedAmount(x.getInvoiceAmount().toString());
            recordVo.setRemark(x.getRemark() == null ? "" : x.getRemark());
            recordVo.setTradeBeginDay(x.getTradeCompleteDayBegin());
            recordVo.setTradeEndDay(x.getTradeCompleteDayEnd());
            recordVo.setInvoiceStatus(String.valueOf(x.getInvoiceStatus()));
            recordVo.setDownloadUrl(x.getInvoiceFileUrlList() == null ? new ArrayList<>() : x.getInvoiceFileUrlList().stream().map(file-> DOMAIN_PREFIX + file).collect(Collectors.toList()));
            return recordVo;
        }).collect(Collectors.toList());
        InvoiceRecordResVo invoiceRecordResVo = new InvoiceRecordResVo();
        invoiceRecordResVo.setInvoiceRecordList(resVoList);
        return invoiceRecordResVo;
    }
}
