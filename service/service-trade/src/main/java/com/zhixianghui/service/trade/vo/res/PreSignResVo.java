package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-14 10:16
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PreSignResVo extends ApiBizBaseDto {
    /**
     * 签约用户ID
     */
    private String userId;

    /**
     *  信息鉴权校验状态
     */
    private Integer infoStatus;

    public PreSignResVo() { }

    public PreSignResVo(int infoStatus, String code, String msg) {
        this.infoStatus = infoStatus;
        this.bizErrCode = code;
        this.bizErrMsg = msg;
    }
}
