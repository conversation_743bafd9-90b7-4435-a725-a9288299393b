package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName InvoiceAccountResVo
 * @Description TODO
 * @Date 2022/9/27 18:08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class InvoiceAccountResVo extends ApiBizBaseDto {

    /**
     * 开票账户余额
     */
    private String invoiceBalance;

    /**
     * 已开票金额
     */
    private String invoicedAmount;
}
