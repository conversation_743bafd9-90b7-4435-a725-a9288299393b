package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.service.WxQueryFacade;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.facade.trade.vo.pay.WxPayResVo;
import com.zhixianghui.service.trade.pay.wx.WxPayContext;
import org.apache.dubbo.config.annotation.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月15日 14:38:00
 */
@Service
public class WxQueryImpl implements WxQueryFacade {

    public WxPayResVo getResVo(WxPayParam wxPayParam,boolean isUpdateFond) {
        WxPayResVo wxPayResVo=new WxPayResVo();
        PayRespVo resVo = WxPayContext.getResVo(wxPayParam,isUpdateFond);
        BeanUtil.copyProperties(resVo,wxPayResVo);
        return wxPayResVo;
    }
}