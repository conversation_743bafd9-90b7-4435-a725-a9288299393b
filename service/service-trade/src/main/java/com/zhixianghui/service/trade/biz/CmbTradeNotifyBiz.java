package com.zhixianghui.service.trade.biz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.dto.RefundBalanceDTO;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.factory.TradeFactory;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class CmbTradeNotifyBiz {
    @Autowired
    private RecordItemBiz recordItemBiz;
    @Autowired
    private TradeFactory tradeFactory;
    @Reference
    private CmbFacade cmbFacade;
    @Reference
    private RobotFacade robotFacade;
    @Autowired
    private OrderBiz orderBiz;
    @Autowired
    private CmbMerchantBalanceBiz cmbMerchantBalanceBiz;
    @Autowired
    private RedisClient redisClient;
    @Reference
    private NotifyFacade notifyFacade;

    public void handleCmbRemitNotify(String accountNo,String reqnbr,String platBatchNo) {

        JSONObject queryResult = null ;
        try {
            queryResult = cmbFacade.queryPayBatchOrder(platBatchNo,accountNo);
        } catch (BizException e) {
            if (e.getErrMsg().contains("没有符合查询条件的数据")) {
                queryResult = null;
            } else {
                throw e;
            }
        }

        JSONObject bb6bthqyz1 = queryResult.getJSONArray("bb6bthqyz1").getJSONObject(0);

        JSONObject detailQuery;
        String trxseq = null;
        String bthnbr = null;
        boolean passed = true;
        do {
            detailQuery = cmbFacade.payRecordDetailQuery(accountNo, reqnbr, trxseq, bthnbr);
            if (detailQuery.getJSONArray("bb6dtlqyy1") != null && !detailQuery.getJSONArray("bb6dtlqyy1").isEmpty()) {
                JSONObject bb6dtlqyy1 = detailQuery.getJSONArray("bb6dtlqyy1").getJSONObject(0);
                trxseq = bb6dtlqyy1.getString("trxseq");
                bthnbr = bb6dtlqyy1.getString("bthnbr");
                reqnbr = bb6dtlqyy1.getString("reqnbr");
            }

            if (detailQuery.getJSONArray("bb6dtlqyz1") != null) {
                JSONArray queryJSONArray = detailQuery.getJSONArray("bb6dtlqyz1");
                for (int i = 0; i < queryJSONArray.size(); i++) {
                    JSONObject detail = queryJSONArray.getJSONObject(i);
                    String stscod = detail.getString("stscod");
                    String trssqn = detail.getString("trssqn");
                    String detailTrxSeq = detail.getString("trxseq");

                    Map<String, Object> param = new HashMap<>();
                    param.put("platBatchNo", platBatchNo);
                    param.put("detailTrxSeq", detailTrxSeq);
                    RecordItem recordItem = recordItemBiz.getOne(param);
                    if (recordItem.getProcessStatus() == RecordItemStatusEnum.PAY_SUCCESS.getValue()
                            || recordItem.getProcessStatus() == RecordItemStatusEnum.PAY_FAIL.getValue()
                            || recordItem.getProcessStatus() == RecordItemStatusEnum.PAY_SPECIAL_FAIL.getValue()){
                        continue;
                    }


                    if (StringUtils.equals(stscod, "S")) {
                        PayReceiveRespVo respVo = new PayReceiveRespVo();
                        respVo.setBizMsg(null);
                        respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
                        respVo.setBankTrxNo(trssqn);
                        respVo.setBankOrderNo(trssqn);
                        respVo.setBizCode("");
                        tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
                    } else if (StringUtils.equals(stscod, "E")) {
                        String failReason = detail.getString("errtxt");
                        PayReceiveRespVo respVo = new PayReceiveRespVo();
                        respVo.setBizMsg(null);
                        respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                        respVo.setBankTrxNo(trssqn);
                        respVo.setBankOrderNo(trssqn);
                        respVo.setBizCode("");
                        respVo.setBizMsg(failReason);
                        tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
                    }else if (StringUtils.equals(stscod, "A")) {
                        if (StringUtils.equals(bb6bthqyz1.getString("stscod"),"C")){
                            PayReceiveRespVo respVo = new PayReceiveRespVo();
                            respVo.setBizMsg(null);
                            respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                            respVo.setBankTrxNo(trssqn);
                            respVo.setBankOrderNo(trssqn);
                            respVo.setBizCode("");
                            respVo.setBizMsg("供应商审核拒绝");
                            tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(respVo,recordItem);
                        }else {
                            passed = false;
                        }

                    }else {
                        passed = false;
                    }
                }
            }

        } while (detailQuery.getJSONArray("bb6dtlqyy1") != null && !detailQuery.getJSONArray("bb6dtlqyy1").isEmpty());

        if (!passed) {
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("存在非终态的订单明细，触发重试");
        }
    }


    public void cmbAccountRefund(RecordItem recordItem) {
        try {
            RefundBalanceDTO refundBalance = new RefundBalanceDTO();
            refundBalance.setMchNo(recordItem.getEmployerNo());
            refundBalance.setMchName(recordItem.getEmployerName());
            refundBalance.setMainstayNo(recordItem.getMainstayNo());
            refundBalance.setMainstayName(recordItem.getMainstayName());
            refundBalance.setPlatBatchNo(recordItem.getPlatBatchNo());
            refundBalance.setPlatTrxNo(recordItem.getPlatTrxNo());
            refundBalance.setAmount(recordItem.getOrderNetAmount());
            refundBalance.setFeeAmount(recordItem.getOrderFee());
            cmbMerchantBalanceBiz.refundBalance(refundBalance);
        }catch (Exception e){
            //限制退款重试次数3次
            if (getAcceptTimes(recordItem.getPlatTrxNo()) > TradeConstant.MAX_ACCOUNT_REFUND_TIMES){
                sendToRobot(recordItem);
                log.info("招行本地账户退款重试次数大于五次，默认退款失败，流水号 :{}", recordItem.getPlatTrxNo());
            }else{
                log.error("招行本地账户执行退款重试处理，流水号：{}",  recordItem.getPlatTrxNo());
                notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_REFUND, recordItem.getEmployerNo(), recordItem.getPlatTrxNo(),
                        NotifyTypeEnum.CMB_REFUND_MSG.getValue(), MessageMsgDest.TAG_CMB_ACCOUNT_REFUND, JSON.toJSONString(recordItem),
                        MsgDelayLevelEnum.S_30.getValue());
            }
        }
    }

    private void sendToRobot(RecordItem recordItem) {
        MarkDownMsg markDownMsg = new MarkDownMsg();
        markDownMsg.setRobotType(RobotTypeEnum.GRANTING_ROBOT.getType());
        markDownMsg.setUnikey("REFUND:" + recordItem.getPlatTrxNo());
        StringBuffer sb = new StringBuffer("#### 招行退款失败\\n")
                .append("> 平台流水号：").append(recordItem.getPlatTrxNo())
                .append("\\n > 商户名称：").append(recordItem.getEmployerNo())
                .append("\\n > 实发金额：").append(recordItem.getOrderNetAmount())
                .append("\\n > 服务费：").append(recordItem.getOrderFee())
                .append("\\n > 发放时间：").append(DateUtil.formatDateTime(recordItem.getCreateTime()));
        markDownMsg.setContent(sb.toString());
        robotFacade.pushMarkDownAsync(markDownMsg);
    }

    private Long getAcceptTimes(String platTrxNo) {
        Long acceptTime = 0L;
        try {
            acceptTime = redisClient.incr(TradeConstant.ACCOUNT_REFUND_TIMES_PRE_KEY+ platTrxNo);
            redisClient.expire(TradeConstant.ACCOUNT_REFUND_TIMES_PRE_KEY + platTrxNo,60*60);
        }catch (Exception e){
            log.error("招行本地账户退款：{} ==> redis获取重试次数异常 忽略", platTrxNo, e);
        }
        return acceptTime;
    }
}
