package com.zhixianghui.service.trade.utils;

import cn.hutool.core.util.ObjectUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.vo.esign.ESignItem;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import com.zhixianghui.facade.trade.vo.sign.StructContext;
import com.zhixianghui.facade.trade.vo.sign.StructPos;
import com.zhixianghui.facade.trade.vo.sign.StructStyle;

import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月29日 15:24:00
 */
public class ESignUtil {

    public static List<ESignItem> simpleESignItem(String mchNo, float x, float y) {
        return Collections.singletonList(new ESignItem(mchNo, x, y));
    }

    public static List<ESignItem> simpleKeywordESignItem(String keywords,String mchNo, float xOffset, float yOffset) {
        ESignItem eSignItem = new ESignItem();
        eSignItem.setKeywords(keywords).setXOffset(xOffset).setYOffset(yOffset).setMchNo(mchNo);
        return Collections.singletonList(eSignItem);
    }

    /**
     * 创建简单盖章组件
     *
     * @param eSignItem
     * @return
     */
    public static StructComponent createSimpleComponent(ESignItem eSignItem) {
        StructComponent structComponent = new StructComponent();
        structComponent.setType(1);
        StructContext structContext = new StructContext();
        structContext.setLabel(eSignItem.getLabel());
        StructStyle structStyle = new StructStyle();
        structStyle.setWidth(eSignItem.getWidth());
        structStyle.setHeight(eSignItem.getHeight());
        structStyle.setFont(1);
        StructPos structPos = new StructPos();
        structPos.setPage(String.valueOf(1));
        structPos.setX(eSignItem.getXPos());
        structPos.setY(eSignItem.getYPos());
        structContext.setStyle(structStyle);
        structContext.setPos(structPos);
        structComponent.setContext(structContext);
        return structComponent;
    }

    /**
     * 创建签署流程
     *
     * @param eSignItem
     * @param fileId
     * @return
     */
    public static CreateByFileReqVo.Signer createSigner(ESignItem eSignItem, String fileId) {
        // 配置签署区域
//        ArrayList<Signfield> orgSignFields = new ArrayList<>();
//        orgSignFields.add(new Signfield()
//                .setFileId(fileId)
//                .setAutoExecute(eSignItem.isAutoExecute())
//                .setActorIndentityType("2")
//                .setSignType(eSignItem.getSignType())
//                .setPosBean(new PosBean()
//                        .setPosPage(eSignItem.getPage())
//                        .setPosX(eSignItem.getXPos())
//                        .setPosY(eSignItem.getYPos())));


        ArrayList<CreateByFileReqVo.SignFields> orgSignFields = new ArrayList<>();
        orgSignFields.add(new CreateByFileReqVo.SignFields()
                .setFileId(fileId)
                .setNormalSignFieldConfig(new CreateByFileReqVo.NormalSignFieldConfig().setFreeMode(false)
                        .setAutoSign(eSignItem.isAutoExecute())
                        .setSignFieldStyle(eSignItem.getSignType())
                        .setSignFieldPosition(new CreateByFileReqVo.SignFieldPosition().setPositionPage(eSignItem.getPage())
                                        .setPositionX(eSignItem.getXPos())
                                        .setPositionY(eSignItem.getYPos()))));


        return new CreateByFileReqVo.Signer()
                .setSignerType(1)
                .setOrgSignerInfo(new CreateByFileReqVo.OrgSignerInfo()
                        .setOrgName(eSignItem.getSignerMchName())
                        .setTransactorInfo(new CreateByFileReqVo
                                .TransactorInfo().setPsnAccount(eSignItem.getSignerPhone())
                                        .setPsnInfo(new CreateByFileReqVo.PsnInfo()
                                                .setPsnName(eSignItem.getSignerName()))))
                .setSignFields(orgSignFields);
//                .setSignfields(orgSignFields);

    }

    public static Map<String, Object> buildParam(SignRecord signRecord) {
        return new HashMap<String, Object>() {
            private static final long serialVersionUID = -7568235688545048261L;
            {
                put("receiveNameMd5", signRecord.getReceiveNameMd5());
                put("receiveIdCardNoMd5", signRecord.getReceiveIdCardNoMd5());
                put("mainstayNo", signRecord.getMainstayNo());
                put("employerNo", signRecord.getEmployerNo());
            }};
    }

    public static Map<String, Object> buildParam(String name, String idcardNo, String mainstayNo, String employerNo) {
        return new HashMap<String, Object>() {
            private static final long serialVersionUID = -8510118264614425795L;
            {
                put("receiveNameMd5", MD5Util.getMixMd5Str(name));
                put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(idcardNo));
                put("mainstayNo", mainstayNo);
                put("employerNo", employerNo);
            }};
    }
}
