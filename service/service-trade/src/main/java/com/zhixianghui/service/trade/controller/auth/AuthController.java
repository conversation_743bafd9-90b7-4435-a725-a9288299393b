package com.zhixianghui.service.trade.controller.auth;

import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.facade.trade.vo.auth.AuthRequestDto;
import com.zhixianghui.facade.trade.vo.auth.AuthResponseVo;
import com.zhixianghui.service.trade.biz.AuthBiz;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 *  * <AUTHOR>
 *  * @description 鉴权对外接口
 *  * @date 2022-02-23 16:09
 */
@Slf4j
@RestController
@RequestMapping("zxh")
public class AuthController {
    @Autowired
    private ZxhLimitBiz limitBiz;
    @Autowired
    private AuthBiz authBiz;

    @PostMapping("auth")
    public ResponseDto<AuthResponseVo> auth(@RequestBody @Valid RequestDto<AuthRequestDto> paramVo){
        AuthRequestDto paramVoData = paramVo.getData();
        log.info("[{}]-请求鉴权,类型-[{}]", paramVoData.getIdCardNo(), paramVoData.getAuthType());

        /**
         * 参数校验
         */
        if (paramVoData.getAuthType() == 1 && StringUtils.isBlank(paramVoData.getBankAccountNo())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("[身份证+姓名+银行卡]鉴权，银行卡号不能为空");
        }
        if (paramVoData.getAuthType() == 2 && StringUtils.isBlank(paramVoData.getPhoneNo())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("[身份证+姓名+手机号]鉴权，手机号不能为空");
        }

        this.decryptParam(paramVoData, paramVo.getSecKey());

        return ResponseDto.success(authBiz.auth(paramVoData.getName(), paramVoData.getIdCardNo(), paramVoData.getPhoneNo(), paramVoData.getBankAccountNo(), paramVoData.getAuthType(),null), "");
    }


    private void decryptParam(AuthRequestDto authRequestDto, String secKey) {
        authRequestDto.setBankAccountNo(limitBiz.decryptNotNull(authRequestDto.getBankAccountNo(), "bank_account_no", secKey));
        authRequestDto.setIdCardNo(limitBiz.decryptNotNull(authRequestDto.getIdCardNo(), "id_card_no", secKey));
        authRequestDto.setPhoneNo(limitBiz.decryptNotNull(authRequestDto.getPhoneNo(), "phone_no", secKey));
        authRequestDto.setName(limitBiz.decryptNotNull(authRequestDto.getName(), "name", secKey));
    }
}
