package com.zhixianghui.service.trade.config;

import com.zhixianghui.service.trade.interceptor.ValidateInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 拦截器 过滤器 配置
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    @Autowired
    ValidateInterceptor validateInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //指定拦截器，指定拦截路径
        registry.addInterceptor(validateInterceptor).
                addPathPatterns("/**").
                excludePathPatterns("/weChat/**");
    }
}
