package com.zhixianghui.service.trade;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 交易中心服务
 */
@EnableCaching
@EnableDiscoveryClient
@SpringBootApplication
@MapperScan("com.zhixianghui.service.trade.dao.mapper")
public class ServiceTradeApp {
    public static void main(String[] args) {
        new SpringApplicationBuilder(ServiceTradeApp.class).run(args);
    }
}
