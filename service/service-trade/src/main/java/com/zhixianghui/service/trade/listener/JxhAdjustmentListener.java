package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.service.trade.biz.AcMerchantBalanceBiz;
import com.zhixianghui.service.trade.biz.WxMerchantBalanceBiz;
import com.zhixianghui.service.trade.biz.WxPayQueryBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月05日 17:40:00
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_JXH_PAY_ADJUSTMENT,
        selectorExpression = MessageMsgDest.TAG_JXH_PAY_ADJUSTMENT,consumeThreadMax = 20,
        consumerGroup = "jxhAdjustmentConsume")
public class JxhAdjustmentListener extends BaseRocketMQListener<String> {

    @Autowired
    private AcMerchantBalanceBiz biz;

    @Override
    public void validateJsonParam(String msg) {
        if(StringUtils.isEmpty(msg)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数不能为空");
        }
    }

    @Override
    public void consumeMessage(String msg) {
        AdjustmentDTO adjustmentDTO = JsonUtil.toBean(msg, AdjustmentDTO.class);
        biz.adjustment(adjustmentDTO);
    }
}