package com.zhixianghui.service.trade.listener;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.vo.TraceVo;
import com.zhixianghui.service.trade.process.WithdrawBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName WithdrawAllListener
 * @Description TODO
 * @Date 2022/7/21 15:03
 */
@Component
@Slf4j
public class WithdrawAllListener {

    @Autowired
    private WithdrawBiz withdrawBiz;


    /**
     * 商户提现
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALL_MERCHANT_WITHDRAW, consumeThreadMax = 5,consumerGroup = "withdrawAllConsumer")
    public class AllMerchantWithdrawListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提现参数为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            EmployerAccountInfo employerAccountInfo = JsonUtil.toBean(msg,EmployerAccountInfo.class);
            log.info("开始执行商户提现，商户号：[{}]，代征主体编号：[{}]",
                    employerAccountInfo.getEmployerNo(),employerAccountInfo.getMainstayNo());
            withdrawBiz.withdrawBeforeGetAmount(employerAccountInfo);
        }

    }
}
