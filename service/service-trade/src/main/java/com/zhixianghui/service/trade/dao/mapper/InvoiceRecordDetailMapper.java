package com.zhixianghui.service.trade.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.trade.entity.InvoiceRecordDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface InvoiceRecordDetailMapper extends BaseMapper<InvoiceRecordDetail> {
    int batchInsert(@Param("list") List<InvoiceRecordDetail> list);

    int updateStatusByInvoceTrxNo(@Param("status") Integer status,@Param("invoceTrxNo") String invoceTrxNo);

    IPage<Map<String, Object>> listInvoiceDetailGroupByIdCard(Page<Map<String, Object>> page,@Param("inviceTrxNo") String inviceTrxNo);
}
