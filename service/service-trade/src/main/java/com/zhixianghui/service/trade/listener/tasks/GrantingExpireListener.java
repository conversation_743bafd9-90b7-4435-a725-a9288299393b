package com.zhixianghui.service.trade.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ORDER_GRANTING_EXPIRE,consumeThreadMax = 3,consumerGroup = "grantingExpireHandler")
public class GrantingExpireListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private OrderBiz orderBiz;

    @Override
    public void runTask(JSONObject jsonParam) {
        orderBiz.grantingExpireQueue();
    }
}
