package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.trade.dto.DateQueryDTO;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.facade.trade.vo.statistics.MonthOrderVo;
import com.zhixianghui.facade.trade.vo.statistics.MonthlyOverviewVo;
import com.zhixianghui.service.trade.dao.RecordItemDao;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年11月23日 10:41:00
 */
@Service
@Slf4j
public class StatisticsBiz {

    @Autowired
    private RecordItemDao recordItemDao;

    public MonthlyOverviewVo getMonthlyOverview(String nowDate) {
        MonthlyOverviewVo monthlyOverviewVo=new MonthlyOverviewVo();
        //当前月
        Date dateTime=new Date();
        if(!StringUtils.isEmpty(nowDate)){
            dateTime=DateUtil.stringToDateTime(nowDate);
        }
        Date date = DateUtil.addDay(dateTime, -1);
        Date thisDate = DateUtil.getDayEnd(date);
        //当前月第一天
        Date thisFistDate = DateUtil.getDayStart(DateUtil.getFirstOfMonth(thisDate));
        //上个月
        Date lastMonth = DateUtil.getLastMonth(thisDate);
        //上个月第一天
        Date lastMonthFistDate = DateUtil.getDayStart(DateUtil.getFirstDayOfLastMonth(thisDate));
        //设置当前月时间跨度
        monthlyOverviewVo.setDateRange(getDateRange(thisFistDate,thisDate));
        //当前月时间范围之类的所有时间
        List<Date> betweenDates = DateUtil.getBetweenDates(thisFistDate, thisDate);
        betweenDates.remove(betweenDates.size()-1);
        //当前月时间范围之类的所有时间字符串类型
        List<String> dates = DateUtil.changeStringList(betweenDates);

        //上个月时间范围之类的所有时间
        List<Date> lastBetweenDates = DateUtil.getBetweenDates(lastMonthFistDate, lastMonth);
        lastBetweenDates.remove(lastBetweenDates.size()-1);
        //上个月时间范围之类的所有时间字符串类型
        List<String> lastDates = DateUtil.changeStringList(lastBetweenDates);

        //当前月查询参数
        DateQueryDTO now=new DateQueryDTO(null,null,thisFistDate,thisDate);
        Map<String, Object> nowMap = BeanUtil.toMap(now);
        TrxNoDateUtil.putCreateDateByMapDate(nowMap);
        now=BeanUtil.toObject(DateQueryDTO.class,nowMap);
        //上个月查询参数
        DateQueryDTO last=new DateQueryDTO(null,null,lastMonthFistDate,lastMonth);
        Map<String, Object> lastMap = BeanUtil.toMap(last);
        TrxNoDateUtil.putCreateDateByMapDate(lastMap);
        last=BeanUtil.toObject(DateQueryDTO.class,lastMap);

        monthlyOverviewVo.setOrder(getOrder(now,last));
        monthlyOverviewVo.setMerchant(getMch(now,last));
        monthlyOverviewVo.setUser(getUser(now,last));
        monthlyOverviewVo.setMoney(getMoney(now,last));

        //获取横坐标
        List<Integer> xAxis = betweenDates.stream().map(e -> {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(e);
            return calendar.get(Calendar.DAY_OF_MONTH);
        }).collect(Collectors.toList());
        log.info("横坐标-------------->{}",xAxis);
        monthlyOverviewVo.setXAxis(xAxis);

        monthlyOverviewVo.setMoneys(getMoneys(now,dates));
        monthlyOverviewVo.setLastMoneys(getMoneys(last,lastDates));

        log.info("最后结果-------------->{}",monthlyOverviewVo);
        return monthlyOverviewVo;
    }

    /**
     *获取金额
     */
    public MonthlyOverviewVo.Money getMoney(DateQueryDTO now,DateQueryDTO last){
        MonthlyOverviewVo.Money money = new MonthlyOverviewVo.Money();
        BigDecimal nowMoney = ifNull(recordItemDao.selectOrderSum(now));
        log.info("当前月金额-------------->{}",nowMoney);
        BigDecimal lastMoney = ifNull(recordItemDao.selectOrderSum(last));
        log.info("上个月金额-------------->{}",lastMoney);
        BigDecimal subMoney = nowMoney.subtract(lastMoney);
        return money.setMoney(nowMoney).setLastMoney(lastMoney).setSubtractMoney(subMoney);
    }

    private BigDecimal ifNull(BigDecimal num){
        if (ObjectUtils.isEmpty(num))
            return new BigDecimal(0);
        return num;
    };

    public List<BigDecimal> getMoneys(DateQueryDTO dateQueryDTO,List<String> dates){
        List<MonthOrderVo> monthOrderVos = recordItemDao.selectOrderPrice(dateQueryDTO);
        LinkedHashMap<String,BigDecimal> map=new LinkedHashMap<>();
        dates.forEach(e->map.put(e,new BigDecimal(0)));
        monthOrderVos.forEach(e->{
            if(map.containsKey(e.getDate())){
                map.put(e.getDate(),e.getPrice());
            }
        });
        log.info("每日金额-------------->{}",map);
        return new ArrayList<>(map.values());
    }

    /**
     *获取订单数量
     */
    public MonthlyOverviewVo.Item getOrder(DateQueryDTO now,DateQueryDTO last){
        MonthlyOverviewVo.Item item=new MonthlyOverviewVo.Item();
        int count = recordItemDao.selectOrderCount(now);
        log.info("当月订单数量-------------->{}",count);
        int lastCount = recordItemDao.selectOrderCount(last);
        log.info("上个月订单数量-------------->{}",lastCount);
        return item.setNum(count).setLastNum(lastCount).setSubtractNum(count-lastCount);
    }

    /**
     *获取商户个数
     */
    public MonthlyOverviewVo.Item getMch(DateQueryDTO now,DateQueryDTO last){
        MonthlyOverviewVo.Item item=new MonthlyOverviewVo.Item();
        List<Integer> num = recordItemDao.selectMchCount(now);
        int count = num.size();
        log.info("当月商户个数-------------->{}",count);
        List<Integer> lastNum = recordItemDao.selectMchCount(last);
        int lastCount = lastNum.size();
        log.info("上个月商户个数-------------->{}",lastCount);
        return item.setNum(count).setLastNum(lastCount).setSubtractNum(count-lastCount);
    }

    /**
     *获取下单人数
     */
    public MonthlyOverviewVo.Item getUser(DateQueryDTO now,DateQueryDTO last){
        MonthlyOverviewVo.Item item=new MonthlyOverviewVo.Item();
        List<Integer> num= recordItemDao.selectUserCount(now);
        int count = num.size();
        log.info("当月下单人数-------------->{}",count);
        List<Integer> lastNum= recordItemDao.selectUserCount(last);
        int lastCount = lastNum.size();
        log.info("上个月下单人数-------------->{}",lastCount);
        return item.setNum(count).setLastNum(lastCount).setSubtractNum(count-lastCount);
    }

    /**
     *获取时间跨度
     */
    public String getDateRange(Date start,Date end){
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy年MM月dd日");
        String startTime=sdf.format(start);
        String endTime=sdf.format(end);
        return startTime + "-" + endTime;
    };


}