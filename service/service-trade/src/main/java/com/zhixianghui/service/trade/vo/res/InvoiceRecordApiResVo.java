package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class InvoiceRecordApiResVo implements Serializable {
    private static final long serialVersionUID = -5654559936918165838L;

    /**
     * 发票流水号
     */
    private String trxNo;

    /**
     * 发票开具状态
     * @see com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum
     */
    private Integer invoiceStatus;

    private String jobId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 开票方
     */
    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;

}
