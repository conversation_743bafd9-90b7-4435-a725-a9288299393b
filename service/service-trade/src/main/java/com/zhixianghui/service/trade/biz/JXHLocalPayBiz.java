package com.zhixianghui.service.trade.biz;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.enums.wx.WxCodeEnum;
import com.zhixianghui.common.statics.enums.wx.WxDetailStatusEnum;
import com.zhixianghui.common.statics.enums.wx.WxFailReasonEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.service.pay.JXHPayFacade;
import com.zhixianghui.facade.banklink.vo.jxh.JxhParam;
import com.zhixianghui.facade.banklink.vo.jxh.JxhResVo;
import com.zhixianghui.facade.banklink.vo.jxh.ResData;
import com.zhixianghui.facade.banklink.vo.jxh.ResStatus;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.facade.trade.vo.WxPayQueryVo;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.service.trade.factory.TradeFactory;
import com.zhixianghui.service.trade.pay.jxh.JXHPayContext;
import com.zhixianghui.service.trade.pay.wx.WxPayContext;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Random;

@Service
@Slf4j
public class JXHLocalPayBiz {

    @Reference
    private JXHPayFacade jxhPayFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private TradeFactory tradeFactory;

    @Autowired
    private RecordItemBiz recordItemBiz;

    @Autowired
    private AcMerchantBalanceBiz acMerchantBalanceBiz;
    @Value("${jxh.isPay:true}")
    private boolean isPay;
    @Reference
    private SequenceFacade sequenceFacade;


    public JxhParam buildParam(PayReqVo reqVo, ResData data) {
        JxhParam jxhParam = new JxhParam();
        BeanUtil.copyProperties(data, jxhParam);
        BeanUtil.copyProperties(reqVo, jxhParam);
        jxhParam.setPlatformSerialNo(data.getPlatformSerialNo());
        log.info("[{}]君享汇支付结果参数构建:{}", reqVo.getPlatTrxNo(), JSONObject.toJSON(jxhParam));
        return jxhParam;
    }

    public void payQuery(PayReqVo msg) {
        try {
            JxhResVo jxhResVo = jxhPayFacade.querySinglePay(msg);
            payQuery(jxhResVo.getData(), msg);
        } catch (Exception e) {
            log.error("[{}]君享汇查询系统异常", msg.getPlatTrxNo(), e);
        }
    }


    public void payQuery(ResData data, PayReqVo msg) {
        if (!isPay) {
            log.info("[{}]君享汇支付回调测试，模拟支付成功", msg.getPlatTrxNo());
            String orderNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.TEXT_JXH_SEQ.getPrefix(),
                    SequenceBizKeyEnum.TEXT_JXH_SEQ.getKey(),
                    SequenceBizKeyEnum.TEXT_JXH_SEQ.getWidth());
            data = new ResData();
            data.setStatus(AcUtil.getRandomNumber(299, 205, 201));
            if (data.getStatus().equals(ResStatus.FAIL)) {
                data.setErrorDesc("随机测试失败");
            }
            data.setPlatformSerialNo(orderNo);
            data.setMerchantOrderNo(msg.getBankOrderNo());
        }
        log.info("[{}]君享汇支付结果查询，查询成功：data:{},mgs:{}", msg.getPlatTrxNo(), JSONObject.toJSONString(data)
                , JSONObject.toJSONString(msg));
        JXHPayContext.getJXHHandler(data.getStatus()).handle(buildParam(msg, data));
    }


    public void send(PayReqVo payReqVo) {
        String platTrxNo = payReqVo.getPlatTrxNo();
        log.info("[{}]==>君享汇回查消息", platTrxNo);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_PAY_QUERY, UUIDUitl.generateString(10), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_JXH_PAY_QUERY, JsonUtil.toString(payReqVo), MsgDelayLevelEnum.S_10.getValue());
    }


    public void retry(PayReqVo payReqVo) {
        String platTrxNo = payReqVo.getPlatTrxNo();
        log.info("[{}]进入君享汇回查重试", platTrxNo);
        Long count = redisClient.incr(WxUtil.getRedisRetryKey(platTrxNo));
        redisClient.expire(WxUtil.getRedisRetryKey(platTrxNo), 60 * 60 * 2);
        if (count > TradeConstant.JXH_PAY_QUERY_RETRY_COUNT) {
            log.error("[{}]君享汇回查重试: 重试超过五次", platTrxNo);
            return;
        }
        log.info("[{}]君享汇回查重试,开始重试{}次", platTrxNo, count);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_PAY_QUERY, UUIDUitl.generateString(10), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_JXH_PAY_RETRY, JsonUtil.toString(payReqVo), MsgDelayLevelEnum.M_1.getValue());

    }


    public void retryNotFond(PayReqVo payReqVo) {
        String platTrxNo = payReqVo.getPlatTrxNo();
        log.info("[{}]进入君享汇回查订单不存在重试", platTrxNo);
        Long count = redisClient.incr(WxUtil.getRedisRetryKey(platTrxNo));
        redisClient.expire(WxUtil.getRedisRetryKey(platTrxNo), 60 * 60 * 2);
        if (count > TradeConstant.JXH_PAY_QUERY_RETRY_COUNT) {
            try {
                log.error("[{}]君享汇回查订单不存在重试: 重试超过五次", platTrxNo);
                ResData data = new ResData();
                data.setStatus(214);
                data.setErrorCode("*********");
                data.setErrorDesc("订单不存在");
                data.setMerchantOrderNo("");
                data.setPlatformSerialNo(payReqVo.getBankOrderNo());
                JXHPayContext.getJXHHandler(data.getStatus()).handle(buildParam(payReqVo, data));
//                log.error("[{}]君享汇回查订单不存在重试: 入参：{}",platTrxNo, data);
            } catch (Exception e) {
                log.error("[{}]君享汇回查订单不存在重试，更新订单失败", platTrxNo);
            }
            return;
        }
        log.info("[{}]君享汇回查订单不存在重试,开始重试{}次", platTrxNo, count);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_PAY_QUERY, UUIDUitl.generateString(10), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_JXH_PAY_NO_FOND_RETRY, JsonUtil.toString(payReqVo), MsgDelayLevelEnum.M_1.getValue());

    }


    public void updateOrder(AcChangeFunds changesFunds, PayReceiveRespVo payReceiveRespVo) {
        updateChangesFunds(changesFunds);
        RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(payReceiveRespVo.getBankOrderNo());
        log.info("[{}]君享汇更新订单状态:{}",payReceiveRespVo.getBankOrderNo(),JSONObject.toJSONString(recordItem));
        tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(payReceiveRespVo, recordItem);
    }

    public void updateChangesFunds(AcChangeFunds acChangesFunds) {
        try {
            acMerchantBalanceBiz.changeAmount(acChangesFunds);
        } catch (DuplicateKeyException e) {
            log.info("[{}]订单金额已扣减，直接更新订单状态并通知商户", acChangesFunds.getPlatTrxNo());
        }
    }
}
