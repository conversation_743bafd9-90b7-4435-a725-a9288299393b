package com.zhixianghui.service.trade.helper;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.common.OrderCompleteVo;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import com.zhixianghui.common.statics.enums.fee.AgentSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CostFeeSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.fee.ProductFeeSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.fee.SalesSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.fee.service.AgentProductRelationFacade;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.vo.RelevantAgent;
import com.zhixianghui.facade.merchant.vo.agent.AgentDetailVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.*;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-06 11:37
 **/
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TradeNotifyBiz {
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private AgentProductRelationFacade agentProductRelationFacade;
    @Reference
    private AgentFacade agentFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    //-----------------受理------------------------------
    /**
     * 发起订单某批次受理
     * @param employerNo 商户号
     * @param platBatchNo 批次号
     * @param platTrxNos 批次明细列表
     */
    public void notifyAcceptStart(String employerNo, String platBatchNo, List<String> platTrxNos) {
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(platTrxNos));
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_ACCEPT,JSON.toJSONString(infoMap));
    }

    /**
     * 受理订单明细遇到业务外异常
     * 重发补偿
     * @param platTrxNo 订单明细流水号
     */
    public void notifyHandleAcceptException(String platTrxNo) {
        //非业务异常延迟队列(避开类似服务暂时异常)
        log.info("[{}]==>发送受理异常处理mq", platTrxNo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC,UUIDUitl.generateString(10),platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),MessageMsgDest.TAG_ACCEPT_NOT_BIZ_EXCEPTION,platTrxNo, MsgDelayLevelEnum.S_10.getValue());
    }

    /**
     * 批次受理信息统计
     * @param platBatchNo 批次订单编号
     */
    public void notifyAcceptBatchCount(String platBatchNo) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_ACCEPT_BATCH_COUNT,platBatchNo);
    }

    //------------------发放-------------

    /**
     * 订单某批发放
     * @param employerNo 商户号
     * @param platBatchNo 批次号
     * @param platTrxNos 批次明细列表
     */
    public void notifyGrantStart(String employerNo, String mainstayNo, String platBatchNo, List<String> platTrxNos) {
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(platTrxNos));
        String yishuiNo = dataDictionaryFacade.getSystemConfig("yishui");
        if (StringUtils.equals(yishuiNo,mainstayNo)) {
            /**
             * 易税供应商由于供应商处在下单之后不能立即根据外部单号查询到订单明细，所以适当拉大延迟时间
             */
            notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                    NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT,JSON.toJSONString(infoMap),
                    MsgDelayLevelEnum.S_10.getValue());
        }else {
            notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                    NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT,JSON.toJSONString(infoMap),
                    MsgDelayLevelEnum.S_1.getValue());
        }
    }

    public void notifyGrantStartRisckRetry(String employerNo, String mainstayNo, String platBatchNo, List<String> platTrxNos) {
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(platTrxNos));
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT,JSON.toJSONString(infoMap),
                MsgDelayLevelEnum.M_1.getValue());

    }

    /**
     * 批次发放信息统计
     * @param platBatchNo 批次订单编号
     */
    public void notifyGrantBatchCount(String platBatchNo) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_GRANT_BATCH_COUNT,platBatchNo);
    }

    /**
     * 发放订单明细遇到业务外异常
     * 重发补偿
     * @param platTrxNo 订单明细流水号
     */
    public void notifyHandleGrantException(String platTrxNo) {
        //非业务异常延迟队列(避开类似服务暂时异常)
        log.info("[{}]==>发送发放异常处理mq", platTrxNo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC,UUIDUitl.generateString(10),platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),MessageMsgDest.TAG_GRANT_NOT_BIZ_EXCEPTION,platTrxNo, MsgDelayLevelEnum.M_1.getValue());
    }

    public void notifyHandleCmbGrantException(String platTrxNo) {
        //非业务异常延迟队列(避开类似服务暂时异常)
        log.info("[{}]==>发送发放异常处理mq", platTrxNo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC,UUIDUitl.generateString(10),platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),MessageMsgDest.TAG_GRANT_NOT_BIZ_EXCEPTION,platTrxNo, MsgDelayLevelEnum.M_10.getValue());
    }

    //------------------完成-------------

    /**
     * 通知各个子系统
     */
    public void notifyOrderComplete(OrderItem orderItem,RecordItem recordItem) {
        String orderItemNetAmountStr = String.valueOf(orderItem.getOrderItemNetAmount());
        String employerNo = orderItem.getEmployerNo();
        String mainstayNo = orderItem.getMainstayNo();

        OrderCompleteVo vo = new OrderCompleteVo();
        vo.setTradeTime(orderItem.getCompleteTime());
        vo.setMchNo(employerNo);
        vo.setMchName(orderItem.getEmployerName());
        vo.setVendorNo(mainstayNo);
        vo.setVendorName(orderItem.getMainstayName());
        vo.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        vo.setProductNo(ProductNoEnum.ZXH.getValue());
        vo.setProductName(ProductNoEnum.ZXH.getDesc());
        vo.setMchOrderNo(orderItem.getMchOrderNo());
        vo.setPlatTrxNo(orderItem.getPlatTrxNo());
        vo.setBaseOrderAmount(orderItem.getOrderItemNetAmount());
        vo.setOrderFee(orderItem.getOrderItemFee());
        vo.setRemark(orderItem.getRemark());
        vo.setChannelTrxNo(recordItem.getChannelTrxNo());
        vo.setChannelOrderNo(recordItem.getRemitPlatTrxNo());
        if(orderItem.getOrderItemStatus().equals(OrderItemStatusEnum.GRANT_SUCCESS.getValue())){
            vo.setStatus(PublicStatus.ACTIVE);
        }else if(orderItem.getOrderItemStatus().equals(OrderItemStatusEnum.GRANT_FAIL.getValue())){
            vo.setStatus(PublicStatus.INACTIVE);
        }

        SpecialRuleParamDto param1 = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.VERDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto param2 = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> merchantFeeDtoList = Lists.newArrayList(param1,param2);
        vo.setMerchantFeeDtoList(merchantFeeDtoList);

        SpecialRuleParamDto param3 = new SpecialRuleParamDto(CostFeeSpecialRuleTypeEnum.MERCANT_NO.getValue(),employerNo);
        SpecialRuleParamDto param4 = new SpecialRuleParamDto(CostFeeSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> vendorFeeDtoList = Lists.newArrayList(param3,param4);
        vo.setVendorFeeDtoList(vendorFeeDtoList);

        SpecialRuleParamDto param5 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.MERCHANT_NO.getValue(),employerNo);
        SpecialRuleParamDto param6 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.VENDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto param7 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> salesFeeDtoList = Lists.newArrayList(param5,param6,param7);
        vo.setSalesFeeDtoList(salesFeeDtoList);

        SpecialRuleParamDto agentParam1 = new SpecialRuleParamDto(AgentSpecialRuleTypeEnum.VENDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto agentParam2 = new SpecialRuleParamDto(AgentSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> agentFeeDtoList = Lists.newArrayList(agentParam1, agentParam2);
        vo.setAgentFeeDtoList(agentFeeDtoList);

        // 查出一级合伙人
        RelevantAgent relevantAgent = merchantFacade.getRelevantAgentByMchNo(vo.getMchNo());
        if (relevantAgent != null && StringUtils.isNotEmpty(relevantAgent.getOneLevelAgentNo())) {
            vo.setOneLevelAgentNo(relevantAgent.getOneLevelAgentNo());
            vo.setOneLevelAgentName(relevantAgent.getOneLevelAgentName());
            vo.setOneLeveOpen(agentProductRelationFacade.isOpenProduct(relevantAgent.getOneLevelAgentNo(), vo.getProductNo()));
            vo.setOneLevelAgentStatus(relevantAgent.getOneLevelAgentStatus());
            // 查出二级合伙人
            if (StringUtils.isNotBlank(relevantAgent.getTwoLevelAgentNo())) {
                AgentDetailVo secondAgen = agentFacade.getDetailByAgentNo(relevantAgent.getTwoLevelAgentNo());
                if (secondAgen != null) {
                    vo.setSecondLevelAgentNo(relevantAgent.getTwoLevelAgentNo());
                    vo.setSecondLevelAgentName(relevantAgent.getTwoLevelAgentName());
                    vo.setSecondLevelAgentStatus(relevantAgent.getTwoLevelAgentStatus());
                    vo.setSecondLeveOpen(agentProductRelationFacade.isOpenProduct(relevantAgent.getTwoLevelAgentNo(), vo.getProductNo()));
                }
            }
        }


        String msg = JsonUtil.toString(vo);
        boolean isSendSuccess = notifyFacade.sendOne(MessageMsgDest.TOPIC_ORDER_COMPLETE,
                orderItem.getEmployerNo(),
                orderItem.getPlatTrxNo(),
                NotifyTypeEnum.ORDER_COMPLETE.getValue(),
                null,
                msg);

        if(isSendSuccess){
            log.info("[发放环节: {}]==>订单完成，通知各个系统成功",orderItem.getPlatTrxNo());
        }else {
            log.error("[发放环节: {}]==>订单完成，通知各个系统失败",orderItem.getPlatTrxNo());
        }

        notifyFacade.sendOne(MessageMsgDest.TOPIC_ORDER_DATA_SYNC_CK, vo.getMchNo(), vo.getPlatTrxNo(), NotifyTypeEnum.ORDER_DATA_SYNC_CK.getValue(), MessageMsgDest.TAG_ORDER_DATA_SYNC_CK, orderItem.getPlatTrxNo(),MsgDelayLevelEnum.M_1.getValue());
    }

    /**
     * 通知商户
     * @param merchantNotifyParam 通知商户的信息参数实体
     * @param order 批次号做唯一性
     */
    public void notifyMerchant(MerchantNotifyParam merchantNotifyParam, Order order){
        //一个批次只发一次 用上商户号和批次号唯一 手动补发不持久化
        notifyFacade.sendOne(MessageMsgDest.TOPIC_NOTIFY_MERCHANT,
                merchantNotifyParam.getNotifyContent().getMchNo(),
                order.getPlatBatchNo(),NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_NOTIFY_MERCHANT,
                JsonUtil.toString(merchantNotifyParam));
    }

    /**
     * 通知商户
     * @param merchantNotifyParam 通知商户的信息参数实体
     * @param orderItem 批次号做唯一性
     */
    public void notifyMerchantBatch(MerchantNotifyParam merchantNotifyParam, OrderItem orderItem){
        //一个批次只发一次 用上商户号和批次号唯一 手动补发不持久化
        notifyFacade.sendOne(MessageMsgDest.TOPIC_NOTIFY_MERCHANT,
                merchantNotifyParam.getNotifyContent().getMchNo(),
                orderItem.getPlatBatchNo(),NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_NOTIFY_MERCHANT,
                JsonUtil.toString(merchantNotifyParam));
    }

    public void notifyMerchantSign(MerchantNotifyParam merchantNotifyParam, SignRecord signRecord) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_NOTIFY_MERCHANT,
                merchantNotifyParam.getNotifyContent().getMchNo(),
                signRecord.getUserId(),NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_NOTIFY_MERCHANT,
                JsonUtil.toString(merchantNotifyParam));
    }


    public void notifyPreSign(String userId) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_SIGN_ASYNC, UUIDUitl.generateString(10),userId,
                NotifyTypeEnum.SIGN_NOTIFY.getValue(), MessageMsgDest.TAG_PRE_SIGN, userId, MsgDelayLevelEnum.S_1.getValue());
    }

    public void notifyMerchantRecharge(MerchantNotifyParam merchantNotifyParam, RechargeRecord rechargeRecord) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_NOTIFY_MERCHANT,
                merchantNotifyParam.getNotifyContent().getMchNo(),
                rechargeRecord.getRechargeOrderId(),NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_NOTIFY_MERCHANT,
                JsonUtil.toString(merchantNotifyParam));
    }
}
