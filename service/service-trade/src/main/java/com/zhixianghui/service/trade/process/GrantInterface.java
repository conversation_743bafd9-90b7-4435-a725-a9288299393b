package com.zhixianghui.service.trade.process;

import com.zhixianghui.common.statics.dto.common.OrderCompleteVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName GrantInterface
 * @Description TODO
 * @Date 2022/6/30 10:04
 */
public interface GrantInterface {

    /**
     * 通知开始发放
     * @param employerNo  用工企业编号
     * @param mainstayNo  供应商编号
     * @param platBatchNo  订单批次号
     * @param notifyList   订单明细列表
     */
    void notifyGrantStart(String employerNo, String mainstayNo, String platBatchNo, List<String> notifyList);

    /**
     * 挂单重试
     * @param employerNo  用工企业编号
     * @param mainstayNo  供应商编号
     * @param platBatchNo  订单批次号
     * @param notifyList   订单明细列表
     */
    void notifyGrantStartRisckRetry(String employerNo, String mainstayNo, String platBatchNo, List<String> notifyList);

    /**
     * 发放订单明细遇到业务外异常
     * 重发补偿
     * @param platTrxNo 订单明细流水号
     */
    void notifyHandleGrantException(String platTrxNo);

    /**
     * 发放订单明细遇到业务外异常（20分钟后重试）
     * 重发补偿
     * @param platTrxNo 订单明细流水号
     */
    void notifyHandleGrantExceptionAgain(String platTrxNo);

    /**
     * 更新批次统计信息
     * @param platBatchNo
     */
    void notifyGrantBatchCount(String platBatchNo);

    /**
     * 真正请求交易
     * @param payReqVo  交易参数
     * @return
     */
    PayRespVo pay(PayReqVo payReqVo);

    /**
     * 回调失败退款处理
     */
    void notifyFailRefund(RecordItem recordItem);

    /**
     * 生成计费订单
     * @param orderItem 订单明细
     * @param recordItem 打款流水
     */
    OrderCompleteVo buildNotifyFeeVo(OrderItem orderItem, RecordItem recordItem);

    /**
     * 打款流水反查
     * @param recordItem
     * @param orderItem
     */
    PayRespVo queryChannelOrder(RecordItem recordItem, EmployerAccountInfo employerAccountInfo);

    /**
     * 流水反查失败处理
     * @param recordItem
     */
    void reverseQueryFailHandle(RecordItem recordItem);

    /**
     * 退回本地冻结账户金额
     * @param platTrxNo
     */
    void refundLocalFrozenAmount(String platTrxNo);
}
