package com.zhixianghui.service.trade.impl;

import com.zhixianghui.service.trade.biz.TradeUniqueBiz;
import com.zhixianghui.facade.trade.service.TradeUniqueFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TradeUniqueImpl implements TradeUniqueFacade {

    private final TradeUniqueBiz biz;
}
