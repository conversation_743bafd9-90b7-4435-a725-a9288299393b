package com.zhixianghui.service.trade.process;

import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.constants.redis.RedisKeysConstant;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.invoice.AccountHandleStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.ApplyTypeEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceAmountTypeEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoicePreStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.account.invoice.entity.AccountInvoice;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceManageFacade;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.service.MerchantCacheFacade;
import com.zhixianghui.facade.merchant.service.MerchantExpressInfoFacade;
import com.zhixianghui.facade.merchant.service.MerchantInvoiceInfoFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.dto.InvoiceWorkerItem;
import com.zhixianghui.facade.trade.dto.OfflineInvoiceApplyDto;
import com.zhixianghui.facade.trade.dto.UpdateInvoiceDetailDto;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.entity.InvoiceRecordDetail;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.DynamicMsgEnum;
import com.zhixianghui.facade.trade.enums.InvoiceCategoryEnum;
import com.zhixianghui.facade.trade.enums.InvoiceSourceEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.facade.trade.vo.InvoiceEditVo;
import com.zhixianghui.facade.trade.vo.InvoiceUpdateVo;
import com.zhixianghui.service.trade.biz.DynamicMsgBiz;
import com.zhixianghui.service.trade.biz.InvoicePreRecordBiz;
import com.zhixianghui.service.trade.biz.InvoiceRecordBiz;
import com.zhixianghui.service.trade.biz.InvoiceRecordDetailBiz;
import com.zhixianghui.service.trade.biz.OfflineOrderBiz;
import com.zhixianghui.service.trade.biz.OfflineOrderItemBiz;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.helper.AccountHelper;
import com.zhixianghui.service.trade.helper.SequenceHelper;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileFilter;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 开票处理
 *
 * <AUTHOR>
 * @date 2020-12-28
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InvoiceHandleBiz {
    private final static String DEFAULT_TRADE_TIME_BEGIN = "2020-11-01";

    private final InvoiceRecordBiz invoiceRecordBiz;
    private final OrderBiz orderBiz;
    private final AccountHelper accountHelper;
    private final RedisLock redisLock;
    private final RedisClient redisClient;
    private final DynamicMsgBiz dynamicMsgBiz;
    private final OrderItemBiz orderItemBiz;
    private final InvoiceRecordDetailBiz recordDetailBiz;
    private final FastdfsClient fastdfsClient;
    private final SequenceHelper sequenceHelper;
    private final OfflineOrderBiz offlineOrderBiz;
    private final OfflineOrderItemBiz offlineOrderItemBiz;
    private final InvoicePreRecordBiz invoicePreRecordBiz;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private MerchantCacheFacade merchantCacheFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private JobFacade jobFacade;
    @Reference
    private MerchantInvoiceInfoFacade merchantInvoiceInfoFacade;
    @Reference
    private MerchantExpressInfoFacade merchantExpressInfoFacade;
    @Reference
    private AccountInvoiceManageFacade accountInvoiceManageFacade;


    /**
     * 获取开票对应的交易时间段的起始时间
     *
     * @param employerMchNo    用工企业编号
     * @param mainstayMchNo    代征主体编号
     * @param workCategoryCode 岗位类目
     * @return
     */
    public String getApplyTradeCompleteDayBegin(String employerMchNo,
                                                String mainstayMchNo,
                                                Integer category,
                                                String jobId,
                                                Integer source,
                                                String workCategoryCode,
                                                String productNo,
                                                Integer amountType) {
        if (amountType == null) { //默认是订单金额
            amountType = InvoiceAmountTypeEnum.ORDER_AMOUNT.getValue();
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerMchNo", employerMchNo);
        paramMap.put("mainstayMchNo", mainstayMchNo);
        paramMap.put("applyType", ApplyTypeEnum.GRANT_AMOUNT.getValue());
        paramMap.put("ignoreInvoiceStatus", InvoiceStatusEnum.EXCEPTION.getValue());
        paramMap.put("category", category);
        paramMap.put("jobId", jobId);
        paramMap.put("workCategoryCodeIsNull", "true");
        paramMap.put("productNo", productNo);
        paramMap.put("amountType", amountType);

        source = source == null ? InvoiceSourceEnum.ON_LINE.getCode() : source;
        paramMap.put("source", source);
        log.info("开票申请时第一次查询已开票记录的参数：{}", paramMap);
        PageResult<List<InvoiceRecord>> pageResult = invoiceRecordBiz.listPage(paramMap, PageParam.newInstance(1, 10));
        String lastEndTime = null;
        if (CollectionUtils.isEmpty(pageResult.getData())) {
            log.info("开票申请时第一次查询数据为空，lastEndTime设置为默认值");
            lastEndTime = DEFAULT_TRADE_TIME_BEGIN;
        } else {
            /**
             * 对最近10条数据进行日期排序-倒序，然后取第一条
             */
            lastEndTime = pageResult.getData().get(0).getTradeCompleteDayEnd();
            if (pageResult.getData().size() > 1) {
                final List<String> sortedDate = pageResult.getData().stream()
                        .map(it -> it.getTradeCompleteDayEnd())
                        .sorted((o1, o2) -> {
                            if (o1 == null && o2 == null) {
                                return 0;
                            } else if (o1 == null && o2 != null) {
                                return 1;
                            } else if (o1 != null && o2 == null) {
                                return -1;
                            } else {
                                final LocalDate localDate1 = LocalDate.parse(o1, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                                final LocalDate localDate2 = LocalDate.parse(o2, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                                if (localDate1.isAfter(localDate2)) {
                                    return -1;
                                } else if (localDate1.isEqual(localDate2)) {
                                    return 0;
                                } else {
                                    return 1;
                                }
                            }
                        }).collect(Collectors.toList());
                lastEndTime = sortedDate.get(0);
            }

            if (StringUtil.isEmpty(lastEndTime)) {
                lastEndTime = DEFAULT_TRADE_TIME_BEGIN;
            }
        }

        paramMap.remove("workCategoryCodeIsNull");
        paramMap.put("workCategoryCode", workCategoryCode);
        log.info("开票申请时第二次查询已开票记录的参数：{}", paramMap);
        PageResult<List<InvoiceRecord>> pageResult2 = invoiceRecordBiz.listPage(paramMap, PageParam.newInstance(1, 10));
        String lastEndTime2 = null;
        if (CollectionUtils.isEmpty(pageResult2.getData())) {
            log.info("开票申请时第二次查询数据为空，lastEndlastEndTime2Time设置为默认值");
            lastEndTime2 = DEFAULT_TRADE_TIME_BEGIN;
        } else {
            /**
             * 对最近10条数据进行日期排序-倒序，然后取第一条
             */
            lastEndTime2 = pageResult2.getData().get(0).getTradeCompleteDayEnd();
            if (pageResult2.getData().size() > 1) {
                final List<String> sortedDate = pageResult2.getData().stream()
                        .map(it -> it.getTradeCompleteDayEnd())
                        .sorted((o1, o2) -> {
                            if (o1 == null && o2 == null) {
                                return 0;
                            } else if (o1 == null && o2 != null) {
                                return 1;
                            } else if (o1 != null && o2 == null) {
                                return -1;
                            } else {
                                final LocalDate localDate1 = LocalDate.parse(o1, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                                final LocalDate localDate2 = LocalDate.parse(o2, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                                if (localDate1.isAfter(localDate2)) {
                                    return -1;
                                } else if (localDate1.isEqual(localDate2)) {
                                    return 0;
                                } else {
                                    return 1;
                                }
                            }
                        }).collect(Collectors.toList());
                lastEndTime2 = sortedDate.get(0);
            }

            if (StringUtil.isEmpty(lastEndTime2)) {
                lastEndTime2 = DEFAULT_TRADE_TIME_BEGIN;
            }
        }

        String realLastEndTime;
        log.info("开票申请时，lastEndTime：{}，lastEndTime2：{}", lastEndTime, lastEndTime2);
        if (cn.hutool.core.date.DateUtil.parseDate(lastEndTime).isAfter(cn.hutool.core.date.DateUtil.parseDate(lastEndTime2))) {
            realLastEndTime = lastEndTime;
        } else {
            realLastEndTime = lastEndTime2;
        }

        Date result = DateUtil.addDay(DateUtil.parse(realLastEndTime), 1);
        return DateUtil.formatDate(result);
    }

    /**
     * 申请开票
     *
     * @param record 开票信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void applyInvoice(InvoiceRecord record, String jobId) {
        log.info("开票请求参数：{}", JsonUtil.toString(record));

        // 校验开票金额、开票时间段等
        String tradeCompleteDayBegin;
        if (StringUtils.equals(record.getProductNo(), ProductNoEnum.ZXH.getValue())) {
            tradeCompleteDayBegin = getApplyTradeCompleteDayBegin(record.getEmployerMchNo(), record.getMainstayMchNo(),
                    null, null, record.getSource(), record.getWorkCategoryCode(), record.getProductNo(),
                    record.getAmountType());
        } else if (StringUtils.equals(record.getProductNo(), ProductNoEnum.CEP.getValue())) {
            tradeCompleteDayBegin = getApplyTradeCompleteDayBegin(record.getEmployerMchNo(), record.getMainstayMchNo(),
                    null, null, record.getSource(), record.getWorkCategoryCode(), record.getProductNo(),
                    record.getAmountType());
        } else if (StringUtils.equals(record.getProductNo(), ProductNoEnum.CKH.getValue())) {
            tradeCompleteDayBegin = getApplyTradeCompleteDayBegin(record.getEmployerMchNo(), record.getMainstayMchNo(),
                    record.getCategory(), jobId, record.getSource(), record.getWorkCategoryCode(), record.getProductNo(),
                    record.getAmountType());
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持该产品开票");
        }
        if (!record.getTradeCompleteDayBegin().equals(tradeCompleteDayBegin)) {
            log.error("开票时间段起始时间有误，正确时间应该为：{}，请求参数：{}", tradeCompleteDayBegin, JsonUtil.toString(record));
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("开票时间段起始时间有误");
        }
        if (DateUtil.parse(record.getTradeCompleteDayBegin()).after(DateUtil.parse(record.getTradeCompleteDayEnd()))) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("开票时间段有误, 起始时间不能大于终止时间");
        }

        // 目前只有按批次统计开票
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", record.getEmployerMchNo());
        paramMap.put("mainstayNo", record.getMainstayMchNo());
        paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
        paramMap.put("productNo", record.getProductNo());
        paramMap.put("jobId", jobId);
        paramMap.put("workCategoryCode", record.getWorkCategoryCode());
        paramMap.put("ignoreZeroAmt", "true");
        paramMap.put("completeBeginDate", DateUtil.parseTime(record.getTradeCompleteDayBegin() + " 00:00:00"));
        paramMap.put("completeEndDate", DateUtil.parseTime(record.getTradeCompleteDayEnd() + " 23:59:59"));
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (StringUtils.equals(record.getProductNo(), ProductNoEnum.ZXH.getValue())) {
            totalAmount = orderBiz.sumWaitInvoiceAmount(paramMap);
        } else if (StringUtils.equals(record.getProductNo(), ProductNoEnum.CKH.getValue())) {
            if (Objects.equals(record.getSource(), InvoiceSourceEnum.ON_LINE.getCode())) {
                totalAmount = orderBiz.sumWaitCkhInvoiceAmount(paramMap);
            } else {
                totalAmount = offlineOrderBiz.sumWaitInvoiceAmount(paramMap);
            }
        } else if (StringUtils.equals(record.getProductNo(), ProductNoEnum.CEP.getValue())) {
            if (record.getAmountType() == InvoiceAmountTypeEnum.GRANT_AMOUNT.getValue()) {
                totalAmount = orderBiz.sumWaitCepGrantInvoiceAmount(paramMap);//统计实际下发总额
            } else {
                totalAmount = orderBiz.sumWaitCepServiceFeeInvoiceAmount(paramMap);//统计服务费总额
            }
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持该产品开票");
        }
        //  预开票金额
        BigDecimal invoicePreAmount = getInvoicePreAmountUnfinished(record);
        // 1、判断预开票金额是否大于0，2、查询预开票金额- 未完成的设置预开票ids
        List<InvoicePreRecord> unfinishedRecords = new ArrayList<>();
        if (invoicePreAmount.compareTo(BigDecimal.ZERO) > 0) {
            unfinishedRecords = invoicePreRecordBiz.listUnfinishedRecords(record);
            List<String> preInvoiceIds = unfinishedRecords.stream()
                    .map(preRecord -> String.valueOf(preRecord.getId()))
                    .collect(Collectors.toList());
            record.setInvoicePreIds(JsonUtil.toStringFriendlyNotNull(preInvoiceIds));
            log.info("设置预开票IDs：{}, 预开票金额：{}", preInvoiceIds, invoicePreAmount);
        }

        if (totalAmount.compareTo(record.getInvoiceAmount().add(invoicePreAmount)) != 0) {
            log.error("开票金额有误，正确应该为：{}，请求参数：{}", totalAmount, JsonUtil.toString(record));
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("开票金额有误");
        }

        String lockKey = TradeConstant.INVOICE_LOCK_KEY + record.getEmployerMchNo();
        String clientId = redisLock.tryLockLong(lockKey, 3000, 1);
        try {
            // 入库
            if (org.apache.commons.lang3.StringUtils.isNotBlank(jobId)) {
                final Job job = jobFacade.getJobByJobId(jobId);
                record.setJobId(jobId);
                record.setJobName(job.getJobName());
            }
            // 前端传入开票金额
            BigDecimal invoiceAmountOld = record.getInvoiceAmount();
            invoiceRecordBiz.insert(record);

            // 发票账户扣款
            if (!Objects.equals(record.getSource(), InvoiceSourceEnum.OFF_LINE.getCode())) {
                // 账务处理要扣减
                record.setInvoiceAmount(totalAmount);

                // 判断是否需要处理预开票金额
                boolean needUpdatePreAmount = !StringUtils.equals(record.getProductNo(), ProductNoEnum.CEP.getValue()) ||
                        (StringUtils.equals(record.getProductNo(), ProductNoEnum.CEP.getValue()) &&
                                record.getAmountType() == InvoiceAmountTypeEnum.GRANT_AMOUNT.getValue());

                // 设置预开票金额
                Map<String, Object> paramMap2 = new HashMap<>();
                paramMap2.put("employerMchNo", record.getEmployerMchNo());
                paramMap2.put("mainstayMchNo", record.getMainstayMchNo());

                // 获取账户信息
                AccountInvoice accountInvoice = accountInvoiceManageFacade.getAccountByMap(paramMap2);
                BigDecimal preAmount = (accountInvoice != null && accountInvoice.getInvoicePreAmount() != null) ?
                        accountInvoice.getInvoicePreAmount() : BigDecimal.ZERO;

                // 根据处理条件设置预开票金额
                if (needUpdatePreAmount && invoicePreAmount.compareTo(BigDecimal.ZERO) > 0) {
                    record.setInvoicePreAmount(preAmount.subtract(invoicePreAmount));
                } else {
                    record.setInvoicePreAmount(preAmount);
                }

                accountHelper.accountInvoiceDebit(record);
            }

            // 更新状态
            record.setAccountHandleStatus(AccountHandleStatusEnum.DEBIT.getValue());
            record.setInvoiceAmount(invoiceAmountOld);
            invoiceRecordBiz.update(record);
            // 更新预开票状态
            updateInvoicePreStatus(unfinishedRecords);

            // 发送邮件给供应商
            Merchant merchant = merchantCacheFacade.getByMchNo(record.getMainstayMchNo());
            sendApplyEmail2Mainstay(record, merchant.getContactEmail());
        } catch (Exception e) {
            log.error("{} 申请开票出现异常：", JsonUtil.toString(record), e);
            throw e;
        } finally {
            redisLock.unlockLong(clientId);
        }

    }

    /**
     * 更新预开票状态（重载方法，处理预开票记录列表）
     *
     * @param unfinishedRecords 未完成的预开票记录列表
     */
    private void updateInvoicePreStatus(List<InvoicePreRecord> unfinishedRecords) {
        if (unfinishedRecords == null || unfinishedRecords.isEmpty()) {
            log.debug("无预开票记录需要更新状态");
            return;
        }

        log.info("开始更新预开票状态为处理中，预开票数量：{}", unfinishedRecords.size());

        // 批量更新预开票记录状态为处理中
        unfinishedRecords.forEach(preInvoiceRecord -> {
            try {
                // 检查状态是否需要更新
                if (Objects.equals(preInvoiceRecord.getInvoiceStatus(), InvoicePreStatusEnum.UN_FINNISH.getValue())) {
                    InvoicePreRecord updateRecord = new InvoicePreRecord();
                    updateRecord.setId(preInvoiceRecord.getId());
                    updateRecord.setVersion(preInvoiceRecord.getVersion());
                    updateRecord.setInvoiceStatus(InvoicePreStatusEnum.PROCESSING.getValue());

                    invoicePreRecordBiz.updatePreInvoice(updateRecord);
                    log.debug("更新预开票记录状态成功，预开票ID：{}, 状态：处理中", preInvoiceRecord.getId());
                } else {
                    log.debug("预开票记录状态无需更新，ID：{}, 当前状态：处理中", preInvoiceRecord.getId());
                }
            } catch (Exception e) {
                log.error("更新预开票记录状态失败，预开票ID：{}", preInvoiceRecord.getId(), e);
            }
        });

        log.info("完成预开票状态更新为处理中，处理预开票数量：{}", unfinishedRecords.size());
    }

    /**
     * 获取预开票金额未完成
     *
     * @param record 开票记录
     * @return 可开票金额
     */
    public BigDecimal getInvoicePreAmountUnfinished(InvoiceRecord record) {
        if (StringUtils.equals(record.getProductNo(), ProductNoEnum.CEP.getValue())) {
            if (record.getAmountType() != InvoiceAmountTypeEnum.GRANT_AMOUNT.getValue()) {
                return BigDecimal.ZERO;
            }
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerMchNo", record.getEmployerMchNo());
        paramMap.put("mainstayMchNo", record.getMainstayMchNo());
        paramMap.put("productNo", record.getProductNo());
        paramMap.put("workCategoryCode", record.getWorkCategoryCode());
        paramMap.put("invoiceStatus", InvoicePreStatusEnum.UN_FINNISH.getValue());

        Map<String, Object> result = invoicePreRecordBiz.countInvoiceAmount(paramMap);

        if (result != null && result.get("invoiceAmount") != null) {
            return new BigDecimal(result.get("invoiceAmount").toString());
        }

        return BigDecimal.ZERO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void applyInvoiceOrderItem(InvoiceRecord record, String jobId) {
        log.info("开票请求参数：{}", JsonUtil.toString(record));

        // 校验开票金额、开票时间段等
        String tradeCompleteDayBegin = getApplyTradeCompleteDayBegin(record.getEmployerMchNo(), record.getMainstayMchNo(), record.getCategory(),
                jobId, record.getSource(), record.getWorkCategoryCode(), record.getProductNo(), record.getAmountType());
        if (!record.getTradeCompleteDayBegin().equals(tradeCompleteDayBegin)) {
            log.error("开票时间段起始时间有误，正确时间应该为：{}，请求参数：{}", tradeCompleteDayBegin, JsonUtil.toString(record));
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("开票时间段起始时间有误");
        }
        if (DateUtil.parse(record.getTradeCompleteDayBegin()).after(DateUtil.parse(record.getTradeCompleteDayEnd()))) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("开票时间段有误, 起始时间不能大于终止时间");
        }

        // 目前只有按批次统计开票
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", record.getEmployerMchNo());
        paramMap.put("mainstayNo", record.getMainstayMchNo());
        paramMap.put("productNo", ProductNoEnum.CKH.getValue());
        paramMap.put("jobId", jobId);
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        paramMap.put("completeBeginDate", DateUtil.parseTime(record.getTradeCompleteDayBegin() + " 00:00:00"));
        paramMap.put("completeEndDate", DateUtil.parseTime(record.getTradeCompleteDayEnd() + " 23:59:59"));
        paramMap.put("workCategoryCode", record.getWorkCategoryCode());
        BigDecimal totalAmount;
        if (record.getSource().intValue() == InvoiceSourceEnum.OFF_LINE.getCode().intValue()) {
            totalAmount = offlineOrderItemBiz.sumOrderItemWaitInvoiceAmount(paramMap);
        } else {
            totalAmount = orderItemBiz.sumOrderItemWaitInvoiceAmount(paramMap);
//            if (record.getAmountType() == InvoiceAmountTypeEnum.ORDER_AMOUNT.getValue()) {
//                totalAmount = orderItemBiz.sumOrderItemWaitInvoiceAmount(paramMap);
//            } else if (record.getAmountType() == InvoiceAmountTypeEnum.GRANT_AMOUNT.getValue()) {
//                totalAmount = orderItemBiz.sumOrderItemWaitGrantInvoiceAmount(paramMap);
//            } else {
//                totalAmount = orderItemBiz.sumOrderItemWaitServiceFeeInvoiceAmount(paramMap);
//            }
        }

        if (totalAmount == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("可开票金额不足");
        }
        if (totalAmount.compareTo(record.getInvoiceAmount()) != 0) {
            log.error("开票金额有误，正确应该为：{}，请求参数：{}", totalAmount, JsonUtil.toString(record));
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("开票金额有误");
        }
        String lockKey = TradeConstant.INVOICE_LOCK_KEY + record.getEmployerMchNo();
        String clientId = redisLock.tryLockLong(lockKey, 3000, 5);
        String trxNo = sequenceFacade.nextRedisId(
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getWidth()
        );

        try {
            int pageSize = 100;
            int currentPage = 1;
            BigDecimal applyAmount = BigDecimal.ZERO;
            PageResult<List<OrderItem>> pageResult;
            do {
                PageParam pageParam = PageParam.newInstance(currentPage, pageSize);
                if (record.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
                    pageResult = orderItemBiz.listPage(paramMap, pageParam);
                } else {
                    Page<OfflineOrderItem> offlineOrderItemPage = offlineOrderItemBiz.listPage(paramMap, pageParam);
                    pageResult = new PageResult<>();
                    pageResult.setPageSize(pageSize);
                    pageResult.setPageCurrent(currentPage);

                    List<OrderItem> items = new ArrayList<>();
                    if (offlineOrderItemPage.getRecords() != null) {
                        for (OfflineOrderItem item : offlineOrderItemPage.getRecords()) {
                            OrderItem orderItem = new OrderItem();
                            BeanUtil.copyProperties(item, orderItem);
                            items.add(orderItem);
                        }
                    }
                    pageResult.setData(items);
                }


                if (pageResult != null && pageResult.getData() != null) {
                    final List<OrderItem> orderItems = pageResult.getData();
                    for (OrderItem orderItem : orderItems) {

                        applyAmount = applyAmount.add(orderItem.getOrderItemNetAmount().add(orderItem.getOrderItemTaxAmount() == null ? BigDecimal.ZERO : orderItem.getOrderItemTaxAmount()));
                        InvoiceRecordDetail invoiceRecordDetail = new InvoiceRecordDetail();
                        BeanUtil.copyProperties(record, invoiceRecordDetail);
                        BeanUtil.copyProperties(orderItem, invoiceRecordDetail);

                        final InvoiceRecordDetail recordDetail = recordDetailBiz.getInvoiceRecordDetailByPlatTrxNo(record.getTrxNo(), orderItem.getPlatTrxNo());

                        invoiceRecordDetail.setId(recordDetail == null ? null : recordDetail.getId());
                        invoiceRecordDetail.setPlatTrxNo(orderItem.getPlatTrxNo());
                        invoiceRecordDetail.setInvoiceAmount(orderItem.getOrderItemNetAmount().add(orderItem.getOrderItemTaxAmount() == null ? BigDecimal.ZERO : orderItem.getOrderItemTaxAmount()));
                        invoiceRecordDetail.setInvoiceStatus(InvoiceStatusEnum.WAIT_ISSUE.getValue());
                        invoiceRecordDetail.setCreateTime(new Date());
                        invoiceRecordDetail.setUpdateTime(new Date());
                        invoiceRecordDetail.setInvoiceTrxNo(trxNo);
                        recordDetailBiz.saveOrUpdate(invoiceRecordDetail);

                        currentPage++;
                    }
                }

            } while (pageResult != null && pageResult.getData() != null && !pageResult.getData().isEmpty());

            // 入库
            record.setTrxNo(trxNo);
            record.setInvoiceAmount(applyAmount);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(jobId)) {
                final Job job = jobFacade.getJobByJobId(jobId);
                record.setJobId(jobId);
                record.setJobName(job.getJobName());
            }
            invoiceRecordBiz.insert(record);

            // 发票账户扣款
            if (record.getSource().intValue() != InvoiceSourceEnum.OFF_LINE.getCode().intValue()) {
                accountHelper.accountInvoiceDebit(record);
            }
            // 更新状态
            record.setAccountHandleStatus(AccountHandleStatusEnum.DEBIT.getValue());
            invoiceRecordBiz.update(record);

            // 发送邮件给供应商
            Merchant merchant = merchantCacheFacade.getByMchNo(record.getMainstayMchNo());
            sendApplyEmail2Mainstay(record, merchant.getContactEmail());
        } catch (Exception e) {
            log.error("{} 申请开票出现异常：", JsonUtil.toString(record), e);
            throw e;
        } finally {
            redisLock.unlockLong(clientId);
        }
    }

    /**
     * 更新发票申请记录状态
     */
    public void updateStatus(InvoiceUpdateVo vo) {
        InvoiceRecord record = invoiceRecordBiz.getByTrxNo(vo.getTrxNo());

        // 校验状态
        validStatus(vo.getInvoiceStatus(), record.getInvoiceStatus());

        // 其他处理
        if (vo.getInvoiceStatus() == InvoiceStatusEnum.EXCEPTION.getValue()
                && !Objects.equals(vo.getInvoiceStatus(), record.getInvoiceStatus())) {
            // 账务退回
            // 处理预开票退回逻辑
            processPreInvoiceRefund(record);
            accountHelper.accountInvoiceRefund(record);
            record.setAccountHandleStatus(AccountHandleStatusEnum.REFUND.getValue());

        } else if (record.getInvoiceStatus() == InvoiceStatusEnum.WAIT_SEND.getValue()
                && !Objects.equals(vo.getInvoiceStatus(), record.getInvoiceStatus())) {
            // 发送邮件给用工企业
            Merchant merchant = merchantCacheFacade.getByMchNo(record.getEmployerMchNo());
            sendSuccessEmail2Employer(record, merchant.getContactEmail());
            log.info("{} 发票开具成功，发送通知邮件", record.getTrxNo());
        }

        // 如果是待寄出/已寄出状态，更新预开票状态到已完成
        if ((vo.getInvoiceStatus() == InvoiceStatusEnum.WAIT_SEND.getValue()
                || vo.getInvoiceStatus() == InvoiceStatusEnum.SENDED.getValue())
                && !Objects.equals(vo.getInvoiceStatus(), record.getInvoiceStatus())) {
            updatePreInvoiceStatusToCompleted(record);
        }

        record.setInvoiceStatus(vo.getInvoiceStatus());
        record.setUpdateTime(new Date());
        if (vo.getInvoiceStatus() == InvoiceStatusEnum.WAIT_SEND.getValue()) {
            record.setInvoiceFileUrl(vo.getInvoiceFileUrl());
        } else if (vo.getInvoiceStatus() == InvoiceStatusEnum.SENDED.getValue()) {
            record.setInvoiceFileUrl(vo.getInvoiceFileUrl());
            record.setExpressNo(vo.getExpressNo());
            record.setCompleteTime(new Date());
        } else if (vo.getInvoiceStatus() == InvoiceStatusEnum.EXCEPTION.getValue()) {
            record.setErrorDesc(vo.getErrorDesc());
            record.setCompleteTime(new Date());
        }
        invoiceRecordBiz.update(record);

        //推送动态
        if (vo.getInvoiceStatus() == InvoiceStatusEnum.SENDED.getValue()) {
            dynamicMsgBiz.putDynamicMsg(record.getEmployerMchNo(), String.format(
                    DynamicMsgEnum.INVOICE.getMsg(), record.getTrxNo()));
        }
    }

    public void updateInvoiceRecord(InvoiceEditVo vo) throws BizException {
        InvoiceRecord record = invoiceRecordBiz.getByTrxNo(vo.getTrxNo());
        if (record == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("发票记录不存在");
        } else if (record.getInvoiceStatus() != InvoiceStatusEnum.WAIT_ISSUE.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("发票记录不是'待开具'状态，不能更新");
        }
        record.setInvoiceType(vo.getInvoiceType());
        record.setInvoiceCategoryCode(vo.getInvoiceCategoryCode());
        record.setInvoiceCategoryName(vo.getInvoiceCategoryName());
        record.setRemark(vo.getRemark());
        invoiceRecordBiz.update(record);
    }

    /**
     * 检查发票状态变更
     *
     * @param newInvoiceStatus 新状态
     * @param oldInvoiceStatus 原有状态
     */
    private void validStatus(Integer newInvoiceStatus, Integer oldInvoiceStatus) {
        if (Objects.equals(newInvoiceStatus, oldInvoiceStatus)) {
            return;
        }
        if (oldInvoiceStatus == InvoiceStatusEnum.WAIT_ISSUE.getValue()
                && newInvoiceStatus != InvoiceStatusEnum.WAIT_SEND.getValue()
                && newInvoiceStatus != InvoiceStatusEnum.SENDED.getValue()
                && newInvoiceStatus != InvoiceStatusEnum.EXCEPTION.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[待开具]状态不允许变更为：" + InvoiceStatusEnum.getEnum(newInvoiceStatus).getDesc());
        } else if (oldInvoiceStatus == InvoiceStatusEnum.WAIT_SEND.getValue()
                && newInvoiceStatus != InvoiceStatusEnum.SENDED.getValue() && newInvoiceStatus != InvoiceStatusEnum.EXCEPTION.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[待寄出]状态不允许变更为：" + InvoiceStatusEnum.getEnum(newInvoiceStatus).getDesc());
        } else if (oldInvoiceStatus == InvoiceStatusEnum.SENDED.getValue()
                && newInvoiceStatus != InvoiceStatusEnum.EXCEPTION.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[已寄出]状态不允许变更为：" + InvoiceStatusEnum.getEnum(newInvoiceStatus).getDesc());
        } else if (oldInvoiceStatus == InvoiceStatusEnum.EXCEPTION.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[其他异常]状态不允许变更为：" + InvoiceStatusEnum.getEnum(newInvoiceStatus).getDesc());
        }
    }

    /**
     * 发送开具成功邮件
     */
    private void sendSuccessEmail2Employer(InvoiceRecord record, String toEmail) {
        if (StringUtil.isEmpty(toEmail)) {
            log.info("{} 邮箱为空，不发送开具成功邮件", record.getTrxNo());
            return;
        }
        EmailParamDto paramDto = new EmailParamDto();
        paramDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        paramDto.setTo(toEmail);
        paramDto.setSubject("【智享汇】你的开票申请成功");
        paramDto.setTpl(EmailTemplateEnum.SUCCESS_INVOICE_EMPLOYER.getName());
        Map<String, Object> emailParamMap = new HashMap<>();
        emailParamMap.put("trxNo", record.getTrxNo());
        emailParamMap.put("createTime", DateUtil.formatDateTime(record.getCreateTime()));
        emailParamMap.put("mainstayMchName", record.getMainstayMchName());
        emailParamMap.put("invoiceAmount", record.getInvoiceAmount() + "元");
        emailParamMap.put("invoiceTypeName", InvoiceTypeEnum.getEnum(record.getInvoiceType()).getDesc());
        emailParamMap.put("invoiceCategoryName", record.getInvoiceCategoryName());
        String addrInfo = record.getProvince() + StringUtils.defaultString(record.getCity())
                + StringUtils.defaultString(record.getCounty())
                + record.getAddress()
                + " "
                + DesensitizeUtil.handleNameDesenCenter(record.getExpressConsignee())
                + " "
                + DesensitizeUtil.handleMobile(record.getExpressTelephone());
        emailParamMap.put("addrInfo", addrInfo);
        paramDto.setTplParam(emailParamMap);
        paramDto.setHtmlFormat(true);
        emailFacade.sendAsync(paramDto);
    }

    /**
     * 发送开具成功邮件
     */
    private void sendApplyEmail2Mainstay(InvoiceRecord record, String toEmail) {
        if (StringUtil.isEmpty(toEmail)) {
            log.info("{} 邮箱为空，不发送申请开票邮件", record.getTrxNo());
            return;
        }
        EmailParamDto paramDto = new EmailParamDto();
        paramDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        paramDto.setTo(toEmail);
        paramDto.setSubject("【智享汇】你有新的开票申请");
        paramDto.setTpl(EmailTemplateEnum.APPLY_INVOICE_MAINSTAY.getName());
        Map<String, Object> emailParamMap = new HashMap<>();
        emailParamMap.put("trxNo", record.getTrxNo());
        emailParamMap.put("createTime", DateUtil.formatDateTime(record.getCreateTime()));
        emailParamMap.put("mainstayName", record.getMainstayMchName());
        emailParamMap.put("employerName", record.getEmployerMchName());
        emailParamMap.put("invoiceAmount", record.getInvoiceAmount() + "元");
        emailParamMap.put("invoiceTypeName", InvoiceTypeEnum.getEnum(record.getInvoiceType()).getDesc());
        emailParamMap.put("invoiceCategoryName", record.getInvoiceCategoryName());
        String addrInfo = record.getProvince() + StringUtils.defaultString(record.getCity())
                + StringUtils.defaultString(record.getCounty())
                + record.getAddress()
                + " "
                + DesensitizeUtil.handleNameDesenCenter(record.getExpressConsignee())
                + " "
                + DesensitizeUtil.handleMobile(record.getExpressTelephone());
        emailParamMap.put("addrInfo", addrInfo);
        paramDto.setTplParam(emailParamMap);
        paramDto.setHtmlFormat(true);
        emailFacade.sendAsync(paramDto);
    }


    public void updateInvoiceRecordDetail(UpdateInvoiceDetailDto dto) {
        final InvoiceRecord invoiceRecord = invoiceRecordBiz.getByTrxNo(dto.getInvoiceTrxNo());
        if (invoiceRecord != null && invoiceRecord.getInvoiceStatus().intValue() == InvoiceStatusEnum.EXCEPTION.getValue()
                && dto.getInvoiceStatus().intValue() != InvoiceStatusEnum.EXCEPTION.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次已为\"其他异常\",不允许修改清单为该状态");
        }
        final List<InvoiceRecordDetail> recordDetails = recordDetailBiz.listInvoiceRecordDetailByInvoiceTrxNoAndIdCardNo(dto.getInvoiceTrxNo(), dto.getIdCardNo());
        for (InvoiceRecordDetail record : recordDetails) {
            validInvoiceDetailStatus(dto.getInvoiceStatus(), record.getInvoiceStatus());
            if (dto.getInvoiceStatus() == InvoiceStatusEnum.EXCEPTION.getValue()
                    && !Objects.equals(dto.getInvoiceStatus(), record.getInvoiceStatus())) {
                log.info("{}:开票异常，发票账务退回", record.getPlatTrxNo());
//                accountHelper.accountInvoiceDetailRefund(record);
//                record.setAccountHandleStatus(AccountHandleStatusEnum.REFUND.getValue());
            } else if (record.getInvoiceStatus() == InvoiceStatusEnum.WAIT_SEND.getValue()
                    && !Objects.equals(dto.getInvoiceStatus(), record.getInvoiceStatus())) {
                // 发送邮件给用工企业
                Merchant merchant = merchantCacheFacade.getByMchNo(record.getEmployerMchNo());

                sendSuccessEmail2Employer(invoiceRecord, merchant.getContactEmail());
                log.info("{} 发票开具成功，发送通知邮件", record.getPlatTrxNo());
            }

            //推送动态
            if (dto.getInvoiceStatus() == InvoiceStatusEnum.SENDED.getValue()) {
                dynamicMsgBiz.putDynamicMsg(invoiceRecord.getEmployerMchNo(), String.format(
                        DynamicMsgEnum.INVOICE.getMsg(), record.getPlatTrxNo()));
            }
        }


        recordDetailBiz.update(new UpdateWrapper<InvoiceRecordDetail>()
                .eq(org.apache.commons.lang3.StringUtils.isNotBlank(dto.getMainstayNo()), InvoiceRecordDetail.COL_MAINSTAY_MCH_NO, dto.getMainstayNo())
                .eq(org.apache.commons.lang3.StringUtils.isNotBlank(dto.getIdCardNo()), InvoiceRecordDetail.COL_RECEIVE_ID_CARD_NO_MD5, MD5Util.getMixMd5Str(dto.getIdCardNo()))
                .eq(org.apache.commons.lang3.StringUtils.isNotBlank(dto.getInvoiceTrxNo()), InvoiceRecordDetail.COL_INVOICE_TRX_NO, dto.getInvoiceTrxNo())
                .set(dto.getInvoiceStatus() != null, InvoiceRecordDetail.COL_INVOICE_STATUS, dto.getInvoiceStatus())
                .set(dto.getInvoiceFileUrls() != null, InvoiceRecordDetail.COL_INVOICE_FILE_URL, JSONUtil.toJsonStr(dto.getInvoiceFileUrls()))
                .set(StringUtils.isNotBlank(dto.getErrorDesc()), "ERROR_DESC", dto.getErrorDesc())
                .set(StringUtils.isNotBlank(dto.getExpressNo()), "EXPRESS_NO", dto.getExpressNo())
                .set("UPDATE_TIME", new Date())
        );

        //处理父订单状态
        final List<InvoiceRecordDetail> invoiceRecordDetails = recordDetailBiz.listInvoiceRecordDetailByInvoiceTrxNo(dto.getInvoiceTrxNo());
        Set statusSet = new HashSet<>();
        Set<String> expressNos = new HashSet<>();
        for (InvoiceRecordDetail detail : invoiceRecordDetails) {
            final int status = detail.getInvoiceStatus().intValue();
            statusSet.add(status);
            if (status == InvoiceStatusEnum.SENDED.getValue() && StringUtils.isNotBlank(detail.getExpressNo())) {
                expressNos.add(detail.getExpressNo());
            }
        }

        if (statusSet.size() == 1) {
            if (statusSet.contains(InvoiceStatusEnum.WAIT_SEND.getValue())) {

                invoiceRecord.setInvoiceStatus(InvoiceStatusEnum.WAIT_SEND.getValue());
                invoiceRecord.setUpdateTime(new Date());
                invoiceRecordBiz.update(invoiceRecord);
            }
            if (statusSet.contains(InvoiceStatusEnum.SENDED.getValue())) {

                invoiceRecord.setInvoiceStatus(InvoiceStatusEnum.SENDED.getValue());
                if (!expressNos.isEmpty()) {
                    invoiceRecord.setExpressNo(StringUtils.join(expressNos, ","));
                }
                invoiceRecord.setUpdateTime(new Date());
                invoiceRecordBiz.update(invoiceRecord);
            }
            if (statusSet.contains(InvoiceStatusEnum.EXCEPTION.getValue())) {
                invoiceRecord.setUpdateTime(new Date());
                if (invoiceRecord != null && invoiceRecord.getInvoiceStatus().intValue() != InvoiceStatusEnum.EXCEPTION.getValue()) {
                    /**
                     * 避免重复扣款
                     */
                    invoiceRecord.setInvoiceStatus(InvoiceStatusEnum.EXCEPTION.getValue());
                    accountHelper.accountInvoiceRefund(invoiceRecord);
                    invoiceRecord.setAccountHandleStatus(AccountHandleStatusEnum.REFUND.getValue());
                }
                invoiceRecordBiz.update(invoiceRecord);
            }
        }
        statusSet = null;
    }

    /**
     * 检查发票状态变更
     *
     * @param newInvoiceStatus 新状态
     * @param oldInvoiceStatus 原有状态
     */
    private void validInvoiceDetailStatus(Integer newInvoiceStatus, Integer oldInvoiceStatus) {
        if (Objects.equals(newInvoiceStatus, oldInvoiceStatus)) {
            return;
        }
        if (oldInvoiceStatus == InvoiceStatusEnum.WAIT_ISSUE.getValue()
                && newInvoiceStatus != InvoiceStatusEnum.WAIT_SEND.getValue() && newInvoiceStatus != InvoiceStatusEnum.EXCEPTION.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[待开具]状态不允许变更为：" + InvoiceStatusEnum.getEnum(newInvoiceStatus).getDesc());
        } else if (oldInvoiceStatus == InvoiceStatusEnum.WAIT_SEND.getValue()
                && newInvoiceStatus != InvoiceStatusEnum.SENDED.getValue() && newInvoiceStatus != InvoiceStatusEnum.EXCEPTION.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[待寄出]状态不允许变更为：" + InvoiceStatusEnum.getEnum(newInvoiceStatus).getDesc());
        } else if (oldInvoiceStatus == InvoiceStatusEnum.SENDED.getValue()
                && newInvoiceStatus != InvoiceStatusEnum.EXCEPTION.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("[已寄出]状态不允许变更为：" + InvoiceStatusEnum.getEnum(newInvoiceStatus).getDesc());
        }
    }

    public IPage<InvoiceDetailGroupByIdCardVo> listInvoiceDetailGroupByIdCard
            (Page<InvoiceDetailGroupByIdCardVo> page, String invoiceTrxNo) {
        final IPage<Map<String, Object>> recordDetails = recordDetailBiz.listInvoiceDetailGroupByIdCard(new Page<Map<String, Object>>(page.getCurrent(), page.getSize()), invoiceTrxNo);
        if (recordDetails.getRecords() == null) {
            page.setRecords(null);
            return page;
        }

        List<InvoiceDetailGroupByIdCardVo> resultList = new ArrayList<>();
        for (Map<String, Object> record : recordDetails.getRecords()) {
            String idCardMd5 = record.get("idCardMd5") == null ? null : String.valueOf(record.get("idCardMd5"));
            Integer invoiceType = record.get("invoiceType") == null ? null : Integer.valueOf(String.valueOf(record.get("invoiceType")));
            Integer invoiceStatus = record.get("invoiceStatus") == null ? null : Integer.valueOf(String.valueOf(record.get("invoiceStatus")));
            BigDecimal sumAmount = record.get("sumAmount") == null ? null : new BigDecimal(String.valueOf(record.get("sumAmount")));

            final InvoiceRecordDetail recordDetail = recordDetailBiz.getOneByIdCardMd5(invoiceTrxNo, idCardMd5);
            if (recordDetail == null) {
                continue;
            }
            InvoiceDetailGroupByIdCardVo data = new InvoiceDetailGroupByIdCardVo();

            data.setReceiveIdCardNo(recordDetail.getReceiveIdCardNoDecrypt());
            data.setReceiveName(recordDetail.getReceiveNameDecrypt());
            data.setReceiveAccountNo(recordDetail.getReceiveAccountNoDecrypt());
            data.setReceivePhoneNo(recordDetail.getReceivePhoneNoDecrypt());
            data.setInvoiceStatus(invoiceStatus);
            data.setInvoiceTrxNo(invoiceTrxNo);
            data.setInvoiceAmount(sumAmount);
            data.setInvoiceType(invoiceType);
            data.setInvoiceFileUrlList(recordDetail.getInvoiceFileUrlList());
            data.setExpressNo(recordDetail.getExpressNo());
            data.setWorkerBillFilePath(recordDetail.getWorkerBillFilePath());
            resultList.add(data);
        }

        page.setRecords(resultList);
        page.setTotal(recordDetails.getTotal());
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    public String applyOffline(OfflineInvoiceApplyDto applyDto) {
        /**
         Map<String, Object> params = new HashMap<>();
         params.put("employerMchNo", applyDto.getEmployerNo());
         params.put("mainstayMchNo", applyDto.getMainstayMchNo());
         params.put("jobId", applyDto.getJobId());
         final long recordsCount = invoiceRecordBiz.recordsCount(params);
         if (recordsCount > 0) {
         throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该任务已在该供应商申请过开票");
         }**/
        if (!applyDto.isPayed()) {
            redisClient.set(RedisKeysConstant.INVOICE_OFFLINE_APPLY_CACHE_PREFIX + applyDto.getEmployerNo(), applyDto, -1);
            return null;
        }

        final String jobId = applyDto.getJobId();
        final List<InvoiceWorkerItem> workerItems = applyDto.getInvoiceWorkerItems();
        final InputStream inputStream = fastdfsClient.downloadFile(applyDto.getWorkerBillFilePath());

        if (workerItems.isEmpty()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("开票人员名单不能为空");
        }

        final Job job = jobFacade.getJobByJobId(jobId);
        if (job == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("任务不存在");
        }
        Map<String, String> map = new HashMap<>();
        workerItems.forEach(it -> {
            final String idCardNo = it.getWorkerIdcard();
            map.put(idCardNo, it.getWorkerName());
        });

        final String path = genRandomPath();


        File unZipedFiles;
        try {
            unZipedFiles = ZipUtil.unzip(inputStream, new File(path), Charset.forName("GBK"));
        } catch (Exception e) {
            unZipedFiles = ZipUtil.unzip(inputStream, new File(path), Charset.forName("UTF-8"));
        }
        final File[] listFiles = unZipedFiles.listFiles();

        if (listFiles == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件列表为空");
        }

        Set<String> idcardSet = new HashSet<>();
        for (File itemFile : listFiles) {
            final String fileName = itemFile.getName();
            if (!org.apache.commons.lang3.StringUtils.contains(fileName, "_") || !org.apache.commons.lang3.StringUtils.contains(fileName, ".")) {
                log.warn("[{}]文件名格式错误,只能为 姓名_身份证号码.文件类型 或者 姓名_身份证号码_xxx.文件类型", fileName);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件名格式错误,只能为 姓名_身份证号码.文件类型 或者 姓名_身份证号码_xxx.文件类型");
            }
            final String[] fileNameSplited = fileName.split("_");
            if (fileNameSplited.length < 2 || StringUtils.isBlank(fileNameSplited[0]) || StringUtils.isBlank(fileNameSplited[1])) {
                log.warn("[{}]文件名格式错误,只能为 姓名_身份证号码.文件类型 或者 姓名_身份证号码_xxx.文件类型", fileName);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件名格式错误,只能为 姓名_身份证号码.文件类型 或者 姓名_身份证号码_xxx.文件类型");
            }
            String name = fileNameSplited[0];
            String idCardNo;
            if (fileNameSplited.length == 2) {
                idCardNo = fileNameSplited[1].substring(0, fileNameSplited[1].lastIndexOf("."));
            } else {
                idCardNo = fileNameSplited[1];
            }


            if (!map.containsKey(idCardNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件列表与开票人员名单不符");
            }
            final String workerName = map.get(idCardNo);
            if (!org.apache.commons.lang3.StringUtils.equals(workerName, name)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件列表与开票人员名单不符");
            }
            idcardSet.add(idCardNo);
        }

        if (!SetUtils.isEqualSet(idcardSet, map.keySet())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件列表与开票人员名单不符");
        }

        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoFacade.getByMchNo(job.getEmployerNo());
        LimitUtil.notEmpty(merchantInvoiceInfo, "开票信息不存在");
        MerchantExpressInfo merchantExpressInfo = merchantExpressInfoFacade.getByMchNo(job.getEmployerNo());
        LimitUtil.notEmpty(merchantExpressInfo, "邮寄信息不存在");

        final String invoiceTrxNo = sequenceHelper.genInvoiceTrxNo();
        BigDecimal invoiceAmount = BigDecimal.ZERO;
        for (InvoiceWorkerItem workerItem : applyDto.getInvoiceWorkerItems()) {
            final File[] files = unZipedFiles.listFiles(new FileFilter() {
                @Override
                public boolean accept(File pathname) {
                    return pathname.getName().contains(workerItem.getWorkerIdcard());
                }
            });
            List<String> fastDfsPaths = new ArrayList<>();
            for (File fileItem : files) {
                final String fastDfsPath = fastdfsClient.uploadFile(fileItem.getAbsolutePath(), fileItem.getName());
                fastDfsPaths.add(fastDfsPath);
            }

            final InvoiceRecordDetail recordDetail = this.fillOffLineInvoiceDetail(invoiceTrxNo, workerItem, applyDto, fastDfsPaths);
            recordDetailBiz.save(recordDetail);
            invoiceAmount = invoiceAmount.add(workerItem.getInvoiceAmount());
        }

        final InvoiceRecord invoiceRecord = this.fillInvoiceRecord(applyDto, merchantExpressInfo, merchantInvoiceInfo, job, invoiceAmount, invoiceTrxNo, InvoiceTypeEnum.NORMAL.getValue());
        invoiceRecord.setCategory(InvoiceCategoryEnum.NATURE_PERSON_INVOICE.getCode());
        invoiceRecordBiz.insert(invoiceRecord);

        final String invoiceTrxNoMajor = sequenceHelper.genInvoiceTrxNo();
        final InvoiceRecord invoiceRecordMarjor = this.fillInvoiceRecord(applyDto, merchantExpressInfo, merchantInvoiceInfo, job, applyDto.getServiceFeeAmount(), invoiceTrxNoMajor, InvoiceTypeEnum.MAJOR.getValue());
        invoiceRecordMarjor.setCategory(InvoiceCategoryEnum.SERVICE_FEE_INVOICE.getCode());
        invoiceRecordBiz.insert(invoiceRecordMarjor);

        redisClient.del(RedisKeysConstant.INVOICE_OFFLINE_APPLY_CACHE_PREFIX + applyDto.getEmployerNo());
        return invoiceTrxNo;

    }

    private InvoiceRecordDetail fillOffLineInvoiceDetail(String invoiceTrxNo, InvoiceWorkerItem
            invoiceWorkerItem, OfflineInvoiceApplyDto applyDto, List<String> fastDfsPaths) {
        InvoiceRecordDetail recordDetail = new InvoiceRecordDetail();
        recordDetail.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        recordDetail.setInvoiceTrxNo(invoiceTrxNo);
        recordDetail.setAccountHandleStatus(AccountHandleStatusEnum.DEBIT.getValue());
        recordDetail.setInvoiceAmount(invoiceWorkerItem.getInvoiceAmount());

        recordDetail.setInvoiceStatus(InvoiceStatusEnum.WAIT_ISSUE.getValue());
        recordDetail.setReceiveAccountNoEncrypt("-");
        recordDetail.setReceiveIdCardNoEncrypt(invoiceWorkerItem.getWorkerIdcard());
        recordDetail.setReceiveNameEncrypt(invoiceWorkerItem.getWorkerName());
        recordDetail.setReceivePhoneNoEncrypt(invoiceWorkerItem.getWorkerPhone());
        recordDetail.setPlatTrxNo(sequenceHelper.genOfflineInvoiceItemDetailPlatTrxNo());
        recordDetail.setCreateTime(new Date());
        recordDetail.setUpdateTime(new Date());
        recordDetail.setEmployerMchName(applyDto.getEmployerName());
        recordDetail.setEmployerMchNo(applyDto.getEmployerNo());
        recordDetail.setWorkerBillFilePath(StringUtils.join(fastDfsPaths, ","));
        recordDetail.setMainstayMchNo(applyDto.getMainstayMchNo());
        recordDetail.setMainstayMchName(applyDto.getMainstayMchName());
        recordDetail.setInvoiceType(applyDto.getInvoiceType());
        recordDetail.setProductNo(ProductNoEnum.CKH.getValue());
        recordDetail.setProductName(ProductNoEnum.CKH.getDesc());

        return recordDetail;
    }

    private InvoiceRecord fillInvoiceRecord(OfflineInvoiceApplyDto applyDto,
                                            MerchantExpressInfo merchantExpressInfo,
                                            MerchantInvoiceInfo merchantInvoiceInfo,
                                            Job job,
                                            BigDecimal invoiceAmount,
                                            String invoiceTrxNo,
                                            Integer invoiceType) {
        InvoiceRecord invoiceRecord = new InvoiceRecord();
        invoiceRecord.setInvoiceType(invoiceType);
        invoiceRecord.setInvoiceAmount(invoiceAmount);
        invoiceRecord.setInvoiceStatus(InvoiceStatusEnum.WAIT_ISSUE.getValue());
        invoiceRecord.setJobId(applyDto.getJobId());
        invoiceRecord.setProductNo(ProductNoEnum.CKH.getValue());
        invoiceRecord.setProductName(ProductNoEnum.CKH.getDesc());
        invoiceRecord.setCreateTime(new Date());
        invoiceRecord.setUpdateTime(new Date());
        invoiceRecord.setAccountHandleStatus(AccountHandleStatusEnum.DEBIT.getValue());
        invoiceRecord.setTrxNo(invoiceTrxNo);
        invoiceRecord.setSource(InvoiceSourceEnum.OFF_LINE.getCode());
        invoiceRecord.setJobName(job.getJobName());
        invoiceRecord.setAddress(merchantExpressInfo.getAddress());
        invoiceRecord.setInvoiceCategoryCode(applyDto.getInvoiceCategoryCode());
        invoiceRecord.setInvoiceCategoryName(applyDto.getInvoiceCategoryName());
        invoiceRecord.setExpressConsignee(merchantExpressInfo.getConsignee());
        invoiceRecord.setExpressTelephone(merchantExpressInfo.getTelephone());
        invoiceRecord.setProvince(merchantExpressInfo.getProvince());
        invoiceRecord.setCity(merchantExpressInfo.getCity());
        invoiceRecord.setCounty(merchantExpressInfo.getCounty());
        invoiceRecord.setTaxPayerType(merchantInvoiceInfo.getTaxPayerType());
        invoiceRecord.setTaxNo(merchantInvoiceInfo.getTaxNo());
        invoiceRecord.setRegisterAddrInfo(merchantInvoiceInfo.getRegisterAddrInfo());
        invoiceRecord.setAccountNo(merchantInvoiceInfo.getAccountNo());
        invoiceRecord.setBankName(merchantInvoiceInfo.getBankName());
        invoiceRecord.setRemark(applyDto.getRemark());
        invoiceRecord.setMainstayMchNo(applyDto.getMainstayMchNo());
        invoiceRecord.setMainstayMchName(applyDto.getMainstayMchName());
        invoiceRecord.setWorkerBillFilePath(applyDto.getWorkerBillFilePath());
        invoiceRecord.setServiceFeeBillPath(applyDto.getServiceFeeBillPath());
        invoiceRecord.setServiceFeeAmount(applyDto.getServiceFeeAmount());
        invoiceRecord.setEmployerMchNo(applyDto.getEmployerNo());
        invoiceRecord.setEmployerMchName(applyDto.getEmployerName());
        invoiceRecord.setApplyType(ApplyTypeEnum.GRANT_AMOUNT.getValue());

        return invoiceRecord;
    }

    private String genRandomPath() {
        try {
            String filePath = System.getProperty("user.dir")
                    + File.separator
                    + "import"
                    + File.separator
                    + RandomUtil.get16LenStr();
            FileUtils.creatDir(filePath);
            return filePath;
        } catch (Exception e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("创建文件失败", e);
        }
    }

    /**
     * 更新预开票记录状态为已完成
     *
     * @param record 发票记录
     */
    private void updatePreInvoiceStatusToCompleted(InvoiceRecord record) {
        String invoicePreIds = record.getInvoicePreIds();
        if (StringUtils.isNotBlank(invoicePreIds)) {
            try {
                List<String> preInvoiceIdList = JsonUtil.toList(invoicePreIds, String.class);
                log.info("开始更新预开票状态为已完成，发票交易号：{}, 预开票IDs：{}", record.getTrxNo(), preInvoiceIdList);

                // 先查询预开票记录列表
                List<InvoicePreRecord> preInvoiceRecords = invoicePreRecordBiz.getByIds(preInvoiceIdList);

                // 批量更新预开票记录状态为已完成
                preInvoiceRecords.forEach(preInvoiceRecord -> {
                    try {
                        // 检查状态是否需要更新
                        if (!Objects.equals(preInvoiceRecord.getInvoiceStatus(), InvoicePreStatusEnum.FINNISH.getValue())) {
                            InvoicePreRecord updateRecord = new InvoicePreRecord();
                            updateRecord.setId(preInvoiceRecord.getId());
                            updateRecord.setVersion(preInvoiceRecord.getVersion());
                            updateRecord.setInvoiceStatus(InvoicePreStatusEnum.FINNISH.getValue());
                            updateRecord.setUpdateTime(new Date());
                            updateRecord.setCompleteTime(new Date());
                            invoicePreRecordBiz.updatePreInvoice(updateRecord);
                            log.debug("更新预开票记录状态成功，预开票ID：{}", preInvoiceRecord.getId());
                        } else {
                            log.debug("预开票记录状态无需更新，ID：{}", preInvoiceRecord.getId());
                        }
                    } catch (Exception e) {
                        log.error("更新预开票记录状态失败，预开票ID：{}", preInvoiceRecord.getId(), e);
                    }
                });

                log.info("完成预开票状态更新，发票交易号：{}, 处理预开票数量：{}", record.getTrxNo(), preInvoiceIdList.size());
            } catch (Exception e) {
                log.error("解析预开票IDs失败，发票交易号：{}, 预开票IDs：{}", record.getTrxNo(), invoicePreIds, e);
            }
        } else {
            log.debug("发票记录无关联预开票，发票交易号：{}", record.getTrxNo());
        }
    }

    /**
     * 处理预开票退回逻辑
     * 1. 查询预开票记录列表
     * 2. 计算预开票总金额
     * 3. 更新预开票记录状态为未完成
     *
     * @param record 发票记录
     */
    private void processPreInvoiceRefund(InvoiceRecord record) {
        String invoicePreIds = record.getInvoicePreIds();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(invoicePreIds)) {
            try {
                List<String> preInvoiceIdList = JsonUtil.toList(invoicePreIds, String.class);
                log.info("开始处理预开票退回，发票交易号：{}, 预开票IDs：{}", record.getTrxNo(), preInvoiceIdList);

                // 1、如果存在预开票id则查询预开票Ids列表
                List<InvoicePreRecord> preInvoiceRecords = invoicePreRecordBiz.getByIds(preInvoiceIdList);

                // 2、将预开票金额相加
                BigDecimal totalPreInvoiceAmount = preInvoiceRecords.stream()
                        .map(InvoicePreRecord::getInvoiceAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                record.setInvoicePreAmount(totalPreInvoiceAmount);

                // 3、更新预开票记录状态到UN_FINNISH
                preInvoiceRecords.forEach(preInvoiceRecord -> {
                    try {
                        // 检查状态是否需要更新
                        if (!Objects.equals(preInvoiceRecord.getInvoiceStatus(), InvoicePreStatusEnum.UN_FINNISH.getValue())) {
                            InvoicePreRecord updateRecord = new InvoicePreRecord();
                            updateRecord.setId(preInvoiceRecord.getId());
                            updateRecord.setVersion(preInvoiceRecord.getVersion());
                            updateRecord.setInvoiceStatus(InvoicePreStatusEnum.UN_FINNISH.getValue());
                            updateRecord.setUpdateTime(new Date());
                            invoicePreRecordBiz.updatePreInvoice(updateRecord);
                            log.debug("更新预开票记录状态成功，预开票ID：{}", preInvoiceRecord.getId());
                        } else {
                            log.debug("预开票记录状态无需更新，ID：{}", preInvoiceRecord.getId());
                        }
                    } catch (Exception e) {
                        log.error("更新预开票记录状态失败，预开票ID：{}", preInvoiceRecord.getId(), e);
                    }
                });

                log.info("完成预开票退回处理，发票交易号：{}, 预开票IDs：{}, 总金额：{}",
                        record.getTrxNo(), preInvoiceIdList, totalPreInvoiceAmount);
            } catch (Exception e) {
                log.error("处理预开票退回失败，发票交易号：{}, 预开票IDs：{}", record.getTrxNo(), invoicePreIds, e);
            }
        } else {
            log.debug("发票记录无关联预开票，无需处理退回，发票交易号：{}", record.getTrxNo());
            record.setInvoicePreAmount(BigDecimal.ZERO);
        }
    }


}
