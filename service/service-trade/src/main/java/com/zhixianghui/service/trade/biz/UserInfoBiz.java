package com.zhixianghui.service.trade.biz;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.statics.constants.redis.RedisKeysManage;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.trade.dto.UserInfoQueryDto;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.entity.WechatUserInfo;
import com.zhixianghui.service.trade.dao.mapper.UserInfoMapper;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.OPEN_USER_ID_SEQ;

@Service
public class UserInfoBiz extends ServiceImpl<UserInfoMapper, UserInfo> {
    private static final String CACHE_KEY_PREFIX = "userInfo:cache:byIDCard:";
    @Reference
    private SequenceFacade sequenceFacade;
    @Autowired
    private SignRecordBiz signRecordBiz;
    @Autowired
    private WeChatUserBiz weChatUserBiz;
    @Autowired
    private RedisClient redisClient;

    public void insertOrUpdate(UserInfo record) {
        String receiveIdCardNoMd5 = record.getReceiveIdCardNoMd5();
        UserInfo userInfo = this.getOne(new QueryWrapper<UserInfo>().eq(UserInfo.COL_RECEIVE_ID_CARD_NO_MD5, receiveIdCardNoMd5));
        if (userInfo == null) {
            String openUserId = sequenceFacade.nextRedisId(OPEN_USER_ID_SEQ.getPrefix(), OPEN_USER_ID_SEQ.getKey(), OPEN_USER_ID_SEQ.getWidth());
            record.setOpenUserId(openUserId);
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            this.save(record);
        } else {
            Long id = userInfo.getId();
            BeanUtils.copyProperties(record, userInfo);
            userInfo.setId(id);
            userInfo.setUpdateTime(new Date());

            this.updateById(userInfo);
        }
    }

    public UserInfo getByIdCardNoMd5(String idCardNoMd5) {
        final UserInfo userInfoCached = redisClient.get(buildCacheKey(idCardNoMd5), UserInfo.class);
        if (userInfoCached != null) {
            return userInfoCached;
        }

        final UserInfo userInfo = this.getOne(new QueryWrapper<UserInfo>().eq(UserInfo.COL_RECEIVE_ID_CARD_NO_MD5, idCardNoMd5));
        if (userInfo != null) {
            redisClient.set(buildCacheKey(idCardNoMd5), userInfo, -1);
        }
        return userInfo;
    }

    public IPage<UserInfo> userInfoIPage(IPage<UserInfo> page, UserInfoQueryDto userInfoQueryDto) {
        final IPage<UserInfo> data = this.baseMapper.selectPage(page, new QueryWrapper<UserInfo>()
                .eq(
                        StringUtils.isNotBlank(userInfoQueryDto.getReceiveNameMd5()),
                        UserInfo.COL_RECEIVE_NAME_MD5,
                        userInfoQueryDto.getReceiveNameMd5()
                )
                .eq(
                        StringUtils.isNotBlank(userInfoQueryDto.getReceiveIdCardNoMd5()),
                        UserInfo.COL_RECEIVE_ID_CARD_NO_MD5,
                        userInfoQueryDto.getReceiveIdCardNoMd5())
                .orderByDesc(UserInfo.COL_ID)
        );
        return data;
    }

    public void deleteId(Long id) {
        if (id == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数错误：必须提供ID编号");
        }
        final UserInfo userInfo = this.getById(id);
        if (userInfo != null) {
            //1. 删除缓存
            final List<WechatUserInfo> wechatUserInfos = weChatUserBiz.getByIdcardMd5(userInfo.getReceiveIdCardNoMd5());
            for (WechatUserInfo wechatUserInfo : wechatUserInfos) {
                final String key = RedisKeysManage.getUserInfoAuthKey(wechatUserInfo.getMobile());
                redisClient.del(key);
            }

            Map<String, Object> param = MapUtil.builder(new HashMap<String,Object>()).put("receiveIdCardNoMd5", userInfo.getReceiveIdCardNoMd5()).build();
            final List<SignRecord> signRecords = signRecordBiz.listNew(param);
            for (SignRecord signRecord : signRecords) {
                final String key = RedisKeysManage.getUserInfoAuthKey(signRecord.getReceivePhoneNoDecrypt());
                redisClient.del(key);
            }

            //2. 删除商户
            this.removeById(id);
        }
    }

    public boolean isVerified(String idcardNoMd5) {
        final UserInfo userInfo = this.getByIdCardNoMd5(idcardNoMd5);
        if (userInfo == null
                || (StringUtils.isAnyBlank(userInfo.getIdCardBackUrl(), userInfo.getIdCardFrontUrl()) && StringUtils.isBlank(userInfo.getIdCardCopyUrl()))
        ) {
            return false;
        }else {
            return true;
        }
    }

    @Override
    public boolean save(UserInfo entity) {
        super.save(entity);
        final UserInfo userInfo = this.getOne(new QueryWrapper<UserInfo>().eq(UserInfo.COL_RECEIVE_ID_CARD_NO_MD5, entity.getReceiveIdCardNoMd5()));
        redisClient.set(buildCacheKey(entity.getReceiveIdCardNoMd5()), userInfo, -1);
        return true;
    }

    @Override
    public boolean removeById(Serializable id) {

        final UserInfo userInfo = this.getById(id);
        redisClient.del(buildCacheKey(userInfo.getReceiveIdCardNoMd5()));
        return super.removeById(id);
    }

    @Override
    public boolean updateById(UserInfo entity) {
        super.updateById(entity);
        final UserInfo userInfo = this.getOne(new QueryWrapper<UserInfo>().eq(UserInfo.COL_RECEIVE_ID_CARD_NO_MD5, entity.getReceiveIdCardNoMd5()));
        redisClient.set(buildCacheKey(entity.getReceiveIdCardNoMd5()), userInfo, -1);
        return true;
    }

    private String buildCacheKey(String idCardMd5) {
        return StrUtil.format(CACHE_KEY_PREFIX + idCardMd5);
    }

    private UserInfo getById(Long id) {
        final UserInfo userInfo = super.getById(id);
        if (userInfo != null) {
            redisClient.set(buildCacheKey(userInfo.getReceiveIdCardNoMd5()), userInfo, -1);
        }
        return userInfo;
    }
}
