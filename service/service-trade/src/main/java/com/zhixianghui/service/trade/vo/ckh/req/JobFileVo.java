package com.zhixianghui.service.trade.vo.ckh.req;

import com.zhixianghui.common.statics.annotations.NotSign;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName JobFileVo
 * @Description TODO
 * @Date 2022/10/27 9:45
 */
@Data
public class JobFileVo implements Serializable {

    @NotBlank(message = "file_name 文件名不能为空")
    private String fileName;

    @NotSign
    @NotBlank(message = "file_body 文件内容不能为空")
    private String fileBody;

    /**
     * 临时存储，非上传参数
     */
    private byte[] fileBytes;
}
