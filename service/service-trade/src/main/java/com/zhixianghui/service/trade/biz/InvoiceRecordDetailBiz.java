package com.zhixianghui.service.trade.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.trade.dto.InvoiceRecordDetailDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecordDetail;
import com.zhixianghui.service.trade.dao.mapper.InvoiceRecordDetailMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class InvoiceRecordDetailBiz extends ServiceImpl<InvoiceRecordDetailMapper, InvoiceRecordDetail> {


    public int batchInsert(List<InvoiceRecordDetail> list) {
        return baseMapper.batchInsert(list);
    }

    public int updateStatusByInvoceTrxNo(Integer status,String invoiceTrxNo) {
        return baseMapper.updateStatusByInvoceTrxNo(status, invoiceTrxNo);
    }

    public InvoiceRecordDetail getInvoiceRecordDetailByPlatTrxNo(String invoiceTrxNo,String platTrxNo) {
        final InvoiceRecordDetail invoiceRecordDetail = this.getOne(new QueryWrapper<InvoiceRecordDetail>()
                .eq(InvoiceRecordDetail.COL_PLAT_TRX_NO, platTrxNo)
                .eq(InvoiceRecordDetail.COL_INVOICE_TRX_NO, invoiceTrxNo)
        );
        return invoiceRecordDetail;
    }

    public List<InvoiceRecordDetail> listInvoiceRecordDetailByInvoiceTrxNo(String invoiceTrxNo) {
        final List<InvoiceRecordDetail> invoiceRecordDetails = this.list(new QueryWrapper<InvoiceRecordDetail>()
                .eq(InvoiceRecordDetail.COL_INVOICE_TRX_NO, invoiceTrxNo).orderByDesc(InvoiceRecordDetail.COL_PLAT_TRX_NO)
        );
        return invoiceRecordDetails;
    }


    public List<InvoiceRecordDetail> listInvoiceRecordDetailByInvoiceTrxNoAndIdCardNo(String invoiceTrxNo,String idCardNo) {
        final List<InvoiceRecordDetail> invoiceRecordDetails = this.list(new QueryWrapper<InvoiceRecordDetail>()
                .eq(InvoiceRecordDetail.COL_INVOICE_TRX_NO, invoiceTrxNo)
                .eq(InvoiceRecordDetail.COL_RECEIVE_ID_CARD_NO_MD5, MD5Util.getMixMd5Str(idCardNo))
        );
        return invoiceRecordDetails;
    }

    public List<InvoiceRecordDetail> listInvoiceDetailIdCards(InvoiceRecordDetailDto dto) {
        final List<InvoiceRecordDetail> list = this.list(
                new QueryWrapper<InvoiceRecordDetail>()
                        .eq(dto.getStatus() != null, InvoiceRecordDetail.COL_INVOICE_STATUS, dto.getStatus())
                        .eq(StringUtils.isNotBlank(dto.getInvoiceTrxNo()), InvoiceRecordDetail.COL_INVOICE_TRX_NO, dto.getInvoiceTrxNo())
                        .eq(StringUtils.isNotBlank(dto.getReceiveIdCardNoMd5()), InvoiceRecordDetail.COL_RECEIVE_ID_CARD_NO_MD5, dto.getReceiveIdCardNoMd5())
                        .eq(StringUtils.isNotBlank(dto.getMchNo()), InvoiceRecordDetail.COL_EMPLOYER_MCH_NO, dto.getMchNo())
                        .eq(StringUtils.isNotBlank(dto.getMainstayNo()), InvoiceRecordDetail.COL_MAINSTAY_MCH_NO, dto.getMainstayNo())
                        .groupBy(InvoiceRecordDetail.COL_RECEIVE_ID_CARD_NO_MD5)
        );
        return list;
    }

    public IPage<InvoiceRecordDetail> invoiceRecordDetailPage(IPage<InvoiceRecordDetail> page, InvoiceRecordDetailDto dto) {
        final IPage<InvoiceRecordDetail> recordDetailPage = this.page(page, new QueryWrapper<InvoiceRecordDetail>()
                .eq(dto.getStatus() != null, InvoiceRecordDetail.COL_INVOICE_STATUS, dto.getStatus())
                .eq(StringUtils.isNotBlank(dto.getInvoiceTrxNo()), InvoiceRecordDetail.COL_INVOICE_TRX_NO, dto.getInvoiceTrxNo())
                .eq(StringUtils.isNotBlank(dto.getMchNo()), InvoiceRecordDetail.COL_EMPLOYER_MCH_NO, dto.getMchNo())
                .eq(StringUtils.isNotBlank(dto.getMainstayNo()), InvoiceRecordDetail.COL_MAINSTAY_MCH_NO, dto.getMainstayNo())

        );

        return recordDetailPage;
    }

    public IPage<Map<String,Object>> listInvoiceDetailGroupByIdCard(Page<Map<String, Object>> page, String trxNo) {
        return this.baseMapper.listInvoiceDetailGroupByIdCard(page, trxNo);
    }

    public InvoiceRecordDetail getOneByIdCardMd5(String invoiceTrxNo,String idCardMd5) {
        final InvoiceRecordDetail invoiceRecordDetail = this.baseMapper.selectOne(new QueryWrapper<InvoiceRecordDetail>()
                .eq(StringUtils.isNotBlank(idCardMd5),InvoiceRecordDetail.COL_RECEIVE_ID_CARD_NO_MD5, idCardMd5)
                .eq(StringUtils.isNotBlank(invoiceTrxNo),InvoiceRecordDetail.COL_INVOICE_TRX_NO, invoiceTrxNo)
                .orderByDesc(InvoiceRecordDetail.COL_CREATE_TIME)
                .last("limit 1")
        );
        return invoiceRecordDetail;
    }
}
