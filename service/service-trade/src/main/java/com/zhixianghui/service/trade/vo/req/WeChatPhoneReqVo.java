package com.zhixianghui.service.trade.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/2/24 16:51
 */
@Data
public class WeChatPhoneReqVo implements Serializable {
    private String access_token;
    private String code;

    public WeChatPhoneReqVo(String accessToken, String code) {
        this.access_token = accessToken;
        this.code = code;
    }

    public Map<String, String> getParam() {
        Map<String, String> map = new HashMap<>();
        map.put("access_token", this.access_token);
        map.put("code", this.code);
        return map;
    }

    public String buildUrl(String getPhoneNumberApi) {
        return getPhoneNumberApi + this.access_token;
    }
}
