package com.zhixianghui.service.trade.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.trade.entity.YishuiUsers;
import com.zhixianghui.service.trade.dao.mapper.YishuiUsersMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class YishuiUsersBiz extends ServiceImpl<YishuiUsersMapper, YishuiUsers> {

    public YishuiUsers getByIdCardAndAccountNoAndChannelType(String idCard, String acctNo, int channelType) {

        idCard = MD5Util.getMixMd5Str(idCard);
        acctNo = MD5Util.getMixMd5Str(acctNo);
        YishuiUsers yishuiUser = this.getOne(new QueryWrapper<YishuiUsers>().eq(YishuiUsers.COL_CER_CODE_MD5, idCard).eq(YishuiUsers.COL_ACCOUNT_NO_MD5,acctNo).eq(YishuiUsers.COL_CHANNEL_TYPE,channelType));
        return yishuiUser;
    }

    public List<YishuiUsers> getByIdCard(String idCard) {

        idCard = MD5Util.getMixMd5Str(idCard);
        List<YishuiUsers> yishuiUser = this.list(new QueryWrapper<YishuiUsers>().eq(YishuiUsers.COL_CER_CODE_MD5, idCard));
        return yishuiUser;
    }

}


