package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.service.trade.biz.WithdrawListenerBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月06日 15:40:00
 */

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_JOINPAY_WITHDRAW,
        selectorExpression = MessageMsgDest.TAG_JOINPAY_WITHDRAW,consumeThreadMax = 20,
        consumerGroup = "joinpayWithdrawConsume")
public class JoinPayWithdrawListener extends BaseRocketMQListener<String> {

    @Autowired
    private WithdrawListenerBiz withdrawListenerBiz;

    @Override
    public void validateJsonParam(String msg) {
        if(StringUtils.isEmpty(msg)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("消息不能为空");
        }
    }

    @Override
    public void consumeMessage(String msg) {
        Map<String,Object> map = JsonUtil.toBean(msg, Map.class);
        withdrawListenerBiz.bankCallback(map);
    }
}