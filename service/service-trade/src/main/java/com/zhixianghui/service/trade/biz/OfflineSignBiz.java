package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.enums.common.SuccessFailCodeEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantCacheFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.enums.SignerTypeEnum;
import com.zhixianghui.facade.trade.vo.OfflineExcelRow;
import com.zhixianghui.facade.trade.vo.auth.AuthResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName OfflineSignBiz
 * @Description TODO
 * @Date 2022/12/14 14:56
 */
@Slf4j
@Service
public class OfflineSignBiz {

    @Autowired
    private SignRecordBiz signRecordBiz;

    @Autowired
    private AuthBiz authBiz;

    @Reference
    private MerchantCacheFacade merchantCacheFacade;

    public void offlineRecordHandle(OfflineExcelRow offlineExcelRow) {
        //查询是否存在已签约的数据
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",offlineExcelRow.getMerchantNo());
        paramMap.put("mainstayNo",offlineExcelRow.getMainstayNo());
        paramMap.put("receiveNameMd5", MD5Util.getMixMd5Str(offlineExcelRow.getUserName()));
        paramMap.put("receiveIdCardNoMd5",MD5Util.getMixMd5Str(offlineExcelRow.getIdcardNo()));
        SignRecord signRecord = signRecordBiz.getOne(paramMap);
        if (signRecord != null && signRecord.getSignStatus().intValue() == SignStatusEnum.SIGN_SUCCESS.getValue()){
            //已签约跳过
            return;
        }

        Merchant merchant = merchantCacheFacade.getByMchNo(offlineExcelRow.getMerchantNo());
        if (merchant == null || merchant.getMerchantType().intValue() != MerchantTypeEnum.EMPLOYER.getValue()){
            log.error("导入线下签约人员失败，商户不存在，导入信息：[{}]", JsonUtil.toString(offlineExcelRow));
            return;
        }
        Merchant mainstay = merchantCacheFacade.getByMchNo(offlineExcelRow.getMainstayNo());
        if (mainstay == null || mainstay.getMerchantType().intValue() != MerchantTypeEnum.MAINSTAY.getValue()){
            log.error("导入线下签约人员失败，供应商不存在，导入信息：[{}]", JsonUtil.toString(offlineExcelRow));
            return;
        }


        //鉴权
        boolean isAuth = auth(offlineExcelRow);
        //没有签约记录，新增
        if (signRecord == null){
            signRecord = buildSignRecord(offlineExcelRow,isAuth,merchant,mainstay);
            signRecordBiz.insert(signRecord);
        }else{
            signRecord.setReceiveNameEncrypt(offlineExcelRow.getUserName());
            signRecord.setReceiveIdCardNoEncrypt(offlineExcelRow.getIdcardNo());
            signRecord.setReceivePhoneNoEncrypt(offlineExcelRow.getPhone());
            signRecord.setSignerType(SignerTypeEnum.OFFLINE_SIGN.getValue());
            setStatus(signRecord,isAuth);
            signRecordBiz.update(signRecord);
        }

    }

    private SignRecord buildSignRecord(OfflineExcelRow offlineExcelRow,boolean isAuth,Merchant merchant,Merchant mainstay) {
        SignRecord signRecord = new SignRecord();
        signRecord.setCreateTime(new Date());
        signRecord.setUpdateTime(new Date());
        signRecord.setUserId(MD5Util.getMixMd5Str(offlineExcelRow.getMainstayNo()+offlineExcelRow.getMerchantNo()+offlineExcelRow.getIdcardNo()+offlineExcelRow.getUserName()));
        signRecord.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        signRecord.setReceiveNameEncrypt(offlineExcelRow.getUserName());
        signRecord.setReceiveIdCardNoEncrypt(offlineExcelRow.getIdcardNo());
        signRecord.setReceivePhoneNoEncrypt(offlineExcelRow.getPhone());
        signRecord.setEmployerNo(offlineExcelRow.getMerchantNo());
        signRecord.setEmployerName(merchant.getMchName());
        signRecord.setMainstayNo(offlineExcelRow.getMainstayNo());
        signRecord.setMainstayName(mainstay.getMchName());
        signRecord.setSignerType(SignerTypeEnum.OFFLINE_SIGN.getValue());
        setStatus(signRecord,isAuth);
        return signRecord;
    }

    //根据鉴权设置状态
    private void setStatus(SignRecord signRecord, boolean isAuth) {
        if (isAuth){
            signRecord.setInfoStatus(SuccessFailCodeEnum.SUCCESS.getValue());
            signRecord.setSignStatus(SignStatusEnum.SIGN_SUCCESS.getValue());
        }else{
            signRecord.setInfoStatus(SuccessFailCodeEnum.FAIL.getValue());
            signRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
            signRecord.setErrMsg("线下签约-信息鉴权状态不为成功");
            signRecord.setErrCode(CommonExceptions.BIZ_INVALID.getApiErrorCode());
        }

    }

    private boolean auth(OfflineExcelRow offlineExcelRow) {
        AuthResponseVo authResponseVo;
        //有手机号：三要素
        //没有手机号：二要素
        if (StringUtils.isBlank(offlineExcelRow.getPhone())){
            authResponseVo = authBiz.auth(offlineExcelRow.getUserName(),
                    offlineExcelRow.getIdcardNo(),
                    null,
                    null,
                    AuthTypeEnum.IDCARD_NAME.getValue(),
                    null);
        }else{
            authResponseVo = authBiz.auth(offlineExcelRow.getUserName(),
                    offlineExcelRow.getIdcardNo(),
                    offlineExcelRow.getPhone(),
                    null,
                    AuthTypeEnum.IDCARD_NAME_PHONE.getValue(),
                    null);
        }
        return authResponseVo.getAuthStatus().intValue() == BankAuthStatusEnum.SUCCESS.getValue();
    }
}
