package com.zhixianghui.service.trade.controller.grant;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.parser.ParserConfig;
import com.beust.jcommander.internal.Lists;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.service.MerchantCacheFacade;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import com.zhixianghui.service.trade.process.ZXHAcceptBiz;
import com.zhixianghui.service.trade.process.ZXHGrantBiz;
import com.zhixianghui.service.trade.vo.req.SingleGrantReqVo;
import com.zhixianghui.service.trade.vo.req.SyncTradeDataReqVo;
import com.zhixianghui.service.trade.vo.res.SingleGrantResVo;
import com.zhixianghui.service.trade.vo.res.SyncTradeDataResVo;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.Validation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @description 受理发放对外接口
 * @date 2020-12-16 11:46
 **/
@Slf4j
@RestController
@RequestMapping("zxh")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SingleGrantController {

    private final ZxhLimitBiz limitBiz;
    private final OrderBiz orderBiz;
    //private final TradeNotifyBiz tradeNotifyBiz;
    private final ZXHAcceptBiz zxhAcceptBiz;
    private final ZXHGrantBiz zxhGrantBiz;
    private final OrderItemBiz orderItemBiz;
    @Reference
    private DataDictionaryFacade dictionaryFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private MerchantSecretFacade merchantSecretFacade;
    @Reference
    private MerchantCacheFacade merchantCacheFacade;
    @Autowired
    private RedisClient redisClient;
    @Reference
    private NotifyFacade notifyFacade;

    @PostMapping("/singleGrant")
    public ResponseDto<SingleGrantResVo> singleGrant(@RequestBody @Valid RequestDto<SingleGrantReqVo> paramVo){
        SingleGrantReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        String mainstayNo = reqVo.getMainstayNo();
        String mchOrderNo = reqVo.getMchOrderNo();
        Integer channelType = reqVo.getChannelType();
        String signType = paramVo.getSignType();
        if (StringUtil.isEmpty(reqVo.getProductNo())) {
            reqVo.setProductNo(ProductNoEnum.ZXH.getValue());
        }
        String logFlag = String.join("-",mchOrderNo,employerNo,mainstayNo);
        log.info("[{}]==>单笔发放受理开始：{}", logFlag, JsonUtil.toString(paramVo));
        try{
            // 敏感传输参数解密
            decryptParam(reqVo,paramVo.getSecKey());
            // 校验收到参数合法性
            checkParam(reqVo);
            // 检验产品开通
            limitBiz.validateProductAndQuoteOpen(employerNo, mainstayNo, reqVo.getProductNo());
            // 校验商户信息状态
            limitBiz.validateMchInfo(logFlag, employerNo,mainstayNo);
            // 校验报备账户状态
            EmployerAccountInfo employerAccountInfo = limitBiz.validateAccountInfo(logFlag,employerNo,mainstayNo,channelType);
            //校验风控规则
            limitBiz.validateRiskControlRule(employerNo,mainstayNo);
            //服务岗位的处理
            MerchantEmployerPosition merchantEmployerPosition = limitBiz.validateEmployerPosition(employerNo,mainstayNo,reqVo.getProductNo());
            //批次记录/订单明细 入库
            Order order = fillOrder(employerAccountInfo,reqVo,merchantEmployerPosition,signType);
            OrderItem orderItem = fillOrderItem(order,reqVo);
            orderBiz.insertOrderAndItem(order,orderItem);
            //返回给商户 已创建状态
            SingleGrantResVo singleGrantResVo = fillSingleGrantResVo(orderItem);
            //发通知 进行受理和发放
            //tradeNotifyBiz.notifyAcceptStart(employerNo,order.getPlatBatchNo(), Lists.newArrayList(orderItem.getPlatTrxNo()));
            zxhAcceptBiz.notifyAcceptStart(employerNo,order.getPlatBatchNo(), Lists.newArrayList(orderItem.getPlatTrxNo()));
            log.info("[{}]==>单笔发放受理完成", logFlag);
            return ResponseDto.success(singleGrantResVo,"");
        }catch (BizException e){
            log.error("[{}]==>单笔发放受理结束 业务异常：", logFlag, e);
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            log.error("[{}]==>单笔发放受理结束 系统异常：", logFlag, e);
            return ResponseDto.unknown();
        }
    }

    @PostMapping("/syncTradeData")
    public ResponseDto<SyncTradeDataResVo> syncTradeData(@RequestBody @Valid RequestDto<SyncTradeDataReqVo> paramVo){
        SyncTradeDataReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        String mainstayNo = reqVo.getMainstayNo();

        String logFlag = String.join("-",reqVo.getBatchNo(),employerNo,mainstayNo);
        log.info("[{}]==>同步外部订单数据开始：{}", logFlag, JsonUtil.toString(paramVo));

        // 校验商户信息状态
        //商户状态
        Merchant merchant = merchantCacheFacade.getByMchNo(employerNo);
        if (merchant == null){
            throw ApiExceptions.API_TRADE_MERCHANT_NOT_EXIST;
        }
        if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw ApiExceptions.API_TRADE_MERCHANT_STATUS_FAIL;
        }
        //代征主体
        Merchant mainstay = merchantCacheFacade.getByMchNo(mainstayNo);
        if (mainstay == null){
            throw ApiExceptions.API_TRADE_MAINSTAY_NOT_EXIST;
        }
        if (!mainstay.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw ApiExceptions.API_TRADE_MAINSTAY_STATUS_FAIL;
        }

        List<Map<String,Object>> itemObj = reqVo.getItems();
        if (itemObj == null || itemObj.isEmpty() || itemObj.size() > 500) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("每次提交的明细条数不能超过500");
        }

        ParserConfig parserConfig = new ParserConfig();
        parserConfig.propertyNamingStrategy = PropertyNamingStrategy.CamelCase;
        List<SingleGrantReqVo> items = JSONUtil.toList(JSON.toJSONString(itemObj), SingleGrantReqVo.class);
        for (SingleGrantReqVo item : items) {
            item.setMainstayNo(reqVo.getMainstayNo());
            item.setChannelType(reqVo.getChannelType());

            this.checkSyncParam(item);

            // 敏感传输参数解密
            decryptParam(item, paramVo.getSecKey());
            // 校验收到参数合法性
            checkParamSync(item);

            Date completeTime = null;
            if (StringUtils.isBlank(item.getCompleteTime())) {
                throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("complete_time 完成时间格式有误,格式必须为 yyyy-MM-dd HH:mm:ss");
            } else {
                try {
                    completeTime = DateUtil.parseTime(item.getCompleteTime());
                } catch (Exception e) {
                    throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("complete_time 完成时间格式有误,格式必须为 yyyy-MM-dd HH:mm:ss");
                }
            }

            try {
                redisClient.lpush("SyncTradeData:" + paramVo.getMchNo(), JSON.toJSONString(item));
            } catch (BizException exception) {
                log.error("数据保存失败", exception);
                throw exception;
            } catch (Throwable throwable) {
                log.error("数据保存失败", throwable);
                throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("数据保存失败，请联系智享技术人员");
            }
        }

        SyncTradeDataResVo resVo = new SyncTradeDataResVo();
        resVo.setBatchNo(reqVo.getBatchNo());
        resVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
        resVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
        return ResponseDto.success(resVo, null);
    }

    private OrderItem fillOrderItem(Order order, SingleGrantReqVo reqVo,Integer status) {
        Date now = new Date();
        OrderItem orderItem = new OrderItem();
        orderItem.setCreateDate(now);
        orderItem.setCreateTime(now);
        orderItem.setUpdateTime(now);
        orderItem.setMchBatchNo(order.getMchBatchNo());
        orderItem.setPlatBatchNo(order.getPlatBatchNo());
        String platTrxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.ORDER_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now),SequenceBizKeyEnum.ORDER_ITEM_SEQ.getKey(),SequenceBizKeyEnum.ORDER_ITEM_SEQ.getWidth());
        orderItem.setMchOrderNo(reqVo.getMchOrderNo());
        orderItem.setPlatTrxNo(platTrxNo);
        orderItem.setLaunchWay(LaunchWayEnum.API.getValue());
        orderItem.setEmployerNo(order.getEmployerNo());
        orderItem.setEmployerName(order.getEmployerName());
        orderItem.setMainstayNo(order.getMainstayNo());
        orderItem.setMainstayName(order.getMainstayName());
        orderItem.setChannelType(order.getChannelType());
        orderItem.setPayChannelNo(order.getPayChannelNo());
        orderItem.setChannelName(order.getChannelName());
        orderItem.setOrderItemNetAmount(new BigDecimal(reqVo.getOrderItemNetAmount()));
        orderItem.setOrderItemTaskAmount(orderItem.getOrderItemNetAmount());
        orderItem.setOrderItemTaxAmount(BigDecimal.ZERO);
        orderItem.setOrderItemStatus(status);
        orderItem.setAccessTimes(0);
        orderItem.setRemark(reqVo.getRemark());
        orderItem.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        orderItem.setReceiveNameEncrypt(reqVo.getReceiveName());
        orderItem.setReceiveIdCardNoEncrypt(reqVo.getReceiveIdCardNo());
        orderItem.setReceiveAccountNoEncrypt(reqVo.getReceiveAccountNo());
        orderItem.setReceivePhoneNoEncrypt(stringValueOf(reqVo.getReceivePhoneNo()));
        orderItem.setAppid(reqVo.getAppid());
        orderItem.setMemo(reqVo.getMemo());
        orderItem.setProductNo(order.getProductNo());
        orderItem.setProductName(order.getProductName());
        orderItem.setWorkCategoryCode(order.getWorkCategoryCode());
        orderItem.setWorkCategoryName(order.getWorkCategoryName());
        orderItem.setJobId(order.getJobId());
        orderItem.setJobName(order.getJobName());
        return orderItem;
    }

    private OrderItem fillOrderItem(Order order, SingleGrantReqVo reqVo){
       return this.fillOrderItem(order, reqVo, OrderItemStatusEnum.CREATE.getValue());
    }

    private SingleGrantResVo fillSingleGrantResVo(OrderItem orderItem) {
        SingleGrantResVo singleGrantResVo = new SingleGrantResVo();
        singleGrantResVo.setMchOrderNo(orderItem.getMchOrderNo());
        singleGrantResVo.setPlatTrxNo(orderItem.getPlatTrxNo());
        singleGrantResVo.setOrderItemStatus(orderItem.getOrderItemStatus());
        singleGrantResVo.setBizErrCode(StringUtils.isBlank(orderItem.getErrorCode())? BizCodeEnum.IN_PROCESS.getCode() :orderItem.getErrorCode());
        singleGrantResVo.setBizErrMsg(StringUtils.isBlank(orderItem.getErrorDesc())?BizCodeEnum.IN_PROCESS.getMsg():orderItem.getErrorDesc());
        return singleGrantResVo;
    }

    private void decryptParam(SingleGrantReqVo reqVo, String secKey) {
        reqVo.setReceiveName(limitBiz.decryptNotNull(reqVo.getReceiveName(), "receive_name", secKey));
        reqVo.setReceiveIdCardNo(limitBiz.decryptNotNull(reqVo.getReceiveIdCardNo(), "receive_id_card_no", secKey));
        reqVo.setReceivePhoneNo(limitBiz.decrypt(reqVo.getReceivePhoneNo(), "receive_phone_no", secKey));
        reqVo.setReceiveAccountNo(limitBiz.decryptNotNull(reqVo.getReceiveAccountNo(), "receive_account_no", secKey));
    }

    private void checkParam(SingleGrantReqVo reqVo) {
        if (!ValidateUtil.isChineseName(reqVo.getReceiveName())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receiveName  姓名(" + reqVo.getReceiveName() + ")格式错误");
        }

        if (StringUtils.isNotEmpty(reqVo.getCallbackUrl()) && !ValidateUtil.isURL(reqVo.getCallbackUrl())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("callback_url 回调地址格式有误");
        }
        if(StringUtils.isNotEmpty(reqVo.getReceivePhoneNo())
                && !ValidateUtil.isMobile(reqVo.getReceivePhoneNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_phone_no  手机号码(" + reqVo.getReceivePhoneNo() + ")格式错误");
        }
        if(!IDCardUtils.verifi(reqVo.getReceiveIdCardNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_id_card_no 身份证号(" + reqVo.getReceiveIdCardNo() + ")格式错误");
        }

        String phoneMustMainstayConfig = dictionaryFacade.getSystemConfig("PHONE_MUST_MAINSTAY");
        if (StringUtils.isNotBlank(phoneMustMainstayConfig)) {
            ArrayList<String> phoneMustMainstays = ListUtil.toList(phoneMustMainstayConfig.split(","));
            if (phoneMustMainstays != null && phoneMustMainstays.contains(reqVo.getMainstayNo()) && StringUtils.isBlank(reqVo.getReceivePhoneNo())) {
                throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg(StrUtil.format("该代征主体[]收款人手机号必填",reqVo.getMainstayNo()));
            }
        }


        /**
        //由于银行卡校验算法存在误杀情况，去掉该校验
        if(Objects.equals(ChannelTypeEnum.BANK.getValue(), reqVo.getChannelType())
                && !ValidateUtil.isBankCard(reqVo.getReceiveAccountNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_account_no 银行卡号(" + reqVo.getReceiveAccountNo() + ")格式错误");
        }**/
        if(!ValidateUtil.isDoubleAnd2decimals(reqVo.getOrderItemNetAmount())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_amount 实发金额[" + reqVo.getOrderItemNetAmount() + "]格式错误,必须是数字,且最多两位小数");
        }
        if(new BigDecimal(reqVo.getOrderItemNetAmount()).compareTo(new BigDecimal("0.1")) < 0){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_amount 实发金额(" + reqVo.getOrderItemNetAmount() + ")有误，必须大于等于0.1元");
        }
        if (reqVo.getChannelType().intValue() == ChannelTypeEnum.WENXIN.getValue() && StringUtils.isBlank(reqVo.getAppid())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("appid 微信发放时appid不能为空");
        }
    }

    private void checkParamSync(SingleGrantReqVo reqVo) {
        if (!ValidateUtil.isChineseName(reqVo.getReceiveName())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receiveName  姓名(" + reqVo.getReceiveName() + ")格式错误");
        }

        if (StringUtils.isNotEmpty(reqVo.getCallbackUrl()) && !ValidateUtil.isURL(reqVo.getCallbackUrl())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("callback_url 回调地址格式有误");
        }
        if(!IDCardUtils.verifi(reqVo.getReceiveIdCardNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_id_card_no 身份证号(" + reqVo.getReceiveIdCardNo() + ")格式错误");
        }

        String phoneMustMainstayConfig = dictionaryFacade.getSystemConfig("PHONE_MUST_MAINSTAY");
        if (StringUtils.isNotBlank(phoneMustMainstayConfig)) {
            ArrayList<String> phoneMustMainstays = ListUtil.toList(phoneMustMainstayConfig.split(","));
            if (phoneMustMainstays != null && phoneMustMainstays.contains(reqVo.getMainstayNo()) && StringUtils.isBlank(reqVo.getReceivePhoneNo())) {
                throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg(StrUtil.format("该代征主体[]收款人手机号必填",reqVo.getMainstayNo()));
            }
        }

        if(!ValidateUtil.isDoubleAnd2decimals(reqVo.getOrderItemNetAmount())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_amount 实发金额[" + reqVo.getOrderItemNetAmount() + "]格式错误,必须是数字,且最多两位小数");
        }
        if(new BigDecimal(reqVo.getOrderItemNetAmount()).compareTo(new BigDecimal("0.1")) < 0){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_amount 实发金额(" + reqVo.getOrderItemNetAmount() + ")有误，必须大于等于0.1元");
        }
        if (reqVo.getChannelType().intValue() == ChannelTypeEnum.WENXIN.getValue() && StringUtils.isBlank(reqVo.getAppid())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("appid 微信发放时appid不能为空");
        }
    }

    private Order fillOrder(EmployerAccountInfo employerAccountInfo, SingleGrantReqVo reqVo, MerchantEmployerPosition merchantEmployerPosition, String signType) {
        String batchId = sequenceFacade.nextRedisId("",SequenceBizKeyEnum.ORDER_SEQ.getKey(), SequenceBizKeyEnum.ORDER_SEQ.getWidth());
        Date batchTime = new Date();
        String batchNo = SequenceBizKeyEnum.ORDER_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
        //时间
        Order order = new Order();
        order.setCreateDate(batchTime);
        order.setCreateTime(batchTime);
        order.setUpdateTime(batchTime);
        //账户
        order.setMchBatchNo(batchNo);
        order.setBatchName(String.join("-", DateUtil.formatDate(new Date()),employerAccountInfo.getEmployerName()));
        order.setPlatBatchNo(batchNo);
        order.setEmployerNo(employerAccountInfo.getEmployerNo());
        order.setEmployerName(employerAccountInfo.getMchName());
        order.setMainstayNo(employerAccountInfo.getMainstayNo());
        order.setMainstayName(employerAccountInfo.getMainstayName());
        order.setChannelType(employerAccountInfo.getChannelType());
        order.setPayChannelNo(employerAccountInfo.getPayChannelNo());
        order.setChannelName(employerAccountInfo.getPayChannelName());
        order.setBatchStatus(OrderStatusEnum.IMPORTING.getValue());
        order.setRequestCount(1);
        order.setRequestNetAmount(new BigDecimal(reqVo.getOrderItemNetAmount()));
        //任务金额=实发金额
        order.setRequestTaskAmount(order.getRequestNetAmount());
        //岗位
        order.setWorkCategoryCode(merchantEmployerPosition.getWorkCategoryCode());
        order.setWorkCategoryName(merchantEmployerPosition.getWorkCategoryName());
        order.setServiceDesc(merchantEmployerPosition.getServiceDesc());
        //api
        order.setLaunchWay(LaunchWayEnum.API.getValue());
        order.setCallbackUrl(reqVo.getCallbackUrl());
        order.getJsonEntity().setSignType(signType);
        //产品
        order.setProductNo(reqVo.getProductNo());
        order.setProductName(ProductNoEnum.getEnum(reqVo.getProductNo()).getDesc());
        return order;
    }

    private Order fillOrder(Merchant employer, Merchant mainstay, Integer channelType,MerchantEmployerPosition merchantEmployerPosition,String signType) {
        String batchId = sequenceFacade.nextRedisId("",SequenceBizKeyEnum.ORDER_SEQ.getKey(), SequenceBizKeyEnum.ORDER_SEQ.getWidth());
        Date batchTime = new Date();
        String batchNo = SequenceBizKeyEnum.ORDER_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
        //时间
        Order order = new Order();
        order.setCreateDate(batchTime);
        order.setCreateTime(batchTime);
        order.setUpdateTime(batchTime);
        //账户
        order.setMchBatchNo(batchNo);
        order.setBatchName(String.join("-", DateUtil.formatDate(new Date()),employer.getMchName()));
        order.setPlatBatchNo(batchNo);
        order.setEmployerNo(employer.getMchNo());
        order.setEmployerName(employer.getMchName());
        order.setMainstayNo(mainstay.getMchNo());
        order.setMainstayName(mainstay.getMchName());
        order.setChannelType(channelType);
        order.setPayChannelNo(ChannelNoEnum.OUT_SYNC.name());
        order.setChannelName(ChannelNoEnum.OUT_SYNC.getDesc());
        order.setBatchStatus(OrderStatusEnum.IMPORTING.getValue());
        order.setRequestCount(1);
        order.setRequestNetAmount(BigDecimal.ZERO);
        //任务金额=实发金额
        order.setRequestTaskAmount(BigDecimal.ZERO);
        order.setAcceptedOrderAmount(BigDecimal.ZERO);
        order.setAcceptedCount(1);
        //岗位
        order.setWorkCategoryCode(merchantEmployerPosition.getWorkCategoryCode());
        order.setWorkCategoryName(merchantEmployerPosition.getWorkCategoryName());
        order.setServiceDesc(merchantEmployerPosition.getServiceDesc());
        //api
        order.setLaunchWay(LaunchWayEnum.API.getValue());
        order.setCallbackUrl(null);
        order.getJsonEntity().setSignType(signType);
        //产品
        order.setProductNo(ProductNoEnum.ZXH.getValue());
        order.setProductName(ProductNoEnum.ZXH.getDesc());
        return order;
    }
    public RecordItem fillRecordItem(Order order, OrderItem orderItem) {
        RecordItem recordItem = new RecordItem();
        BeanUtils.copyProperties(orderItem,recordItem);
        Date now = new Date();
        recordItem.setCreateDate(now);
        recordItem.setCreateTime(now);
        recordItem.setUpdateTime(now);
        recordItem.setChannelMchNo(order.getEmployerNo());
        recordItem.setOrderTaskAmount(orderItem.getOrderItemTaskAmount());
        recordItem.setOrderTaxAmount(orderItem.getOrderItemTaxAmount());
        recordItem.setOrderNetAmount(orderItem.getOrderItemNetAmount());
        recordItem.setOrderFee(orderItem.getOrderItemFee());
        recordItem.setOrderAmount(orderItem.getOrderItemAmount());
        recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SUCCESS.getValue());
        recordItem.setWorkCategoryCode(order.getWorkCategoryCode());
        recordItem.setWorkCategoryName(order.getWorkCategoryName());
        recordItem.setCompleteTime(orderItem.getCompleteTime());
        String remitPlatTrxNo = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.RECORD_ITEM_SEQ.getKey(), SequenceBizKeyEnum.RECORD_ITEM_SEQ.getWidth());
        String compRemitPlatTrxNo = SequenceBizKeyEnum.RECORD_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now) + remitPlatTrxNo;
        recordItem.setRemitPlatTrxNo(compRemitPlatTrxNo);
        return recordItem;
    }
    private static String stringValueOf(Object obj) {
        return (obj == null) ? " " : obj.toString();
    }

    @PostMapping("/confirmSyncTradeData")
    public ResponseDto<String> confirmSyncTradeData(@RequestBody @Valid RequestDto<Map<String,String>> paramVo){

        Map<String, String> reqvo = paramVo.getData();
        String employerNo = reqvo.get("employerNo");
        String mainstayNo = reqvo.get("mainstayNo");
        String productNo = reqvo.get("productNo");
        if (StringUtil.isEmpty(productNo)) {
            productNo = ProductNoEnum.ZXH.getValue();
        }

        //服务岗位的处理
        MerchantEmployerPosition merchantEmployerPosition = limitBiz.validateEmployerPosition(employerNo,mainstayNo,productNo);
        // 校验商户信息状态
        //商户状态
        Merchant merchant = merchantCacheFacade.getByMchNo(employerNo);
        //代征主体，去掉这部分，改成从用户的请求参数中获取代征主体信息
//        Merchant mainstay = merchantCacheFacade.getByMchNo(mainstayNo);

        String signType = paramVo.getSignType();


        ThreadUtil.execAsync(() -> {
            handleSyncData(merchant, null, merchantEmployerPosition, signType);
        });

        return ResponseDto.success("操作成功","");
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleSyncData(Merchant merchant,
                               Merchant mainstay,
                               MerchantEmployerPosition merchantEmployerPosition,
                               String signType) {
        //   批次记录/订单明细 入库
        String data = null;
        do {
            data = redisClient.rpoplpush("SyncTradeData:" + merchant.getMchNo(),"SyncTradeDataBackUp:"+DateUtil.formatCompactDate(new Date())+":" + merchant.getMchNo());
            if (data != null) {

                SingleGrantReqVo item = JSONUtil.toBean(data, SingleGrantReqVo.class);
                // 从客户上送的交易数据中去获取代征主体编号，修复一商户号对应多代征主体问题
                Merchant mainstayForRedis = merchantCacheFacade.getByMchNo(item.getMainstayNo());
                Map<String, Object> msgBody = new HashMap<>();
                msgBody.put("item", item);
                msgBody.put("merchant", merchant);
                msgBody.put("mainstay", mainstayForRedis);
                msgBody.put("merchantEmployerPosition", merchantEmployerPosition);
                msgBody.put("signType", signType);
                notifyFacade.sendOne(MessageMsgDest.TOPIC_SYNC_OUT_ORDER,
                        merchant.getMchNo(),
                        item.getMchOrderNo(),
                        NotifyTypeEnum.TOPIC_SYNC_OUT_ORDER.getValue(),
                        MessageMsgDest.TAG_SYNC_OUT_ORDER, JSON.toJSONString(msgBody));
            }
        } while (data != null);


    }

    public void checkSyncParam(SingleGrantReqVo item) {
        Validator validatorFactory = Validation.buildDefaultValidatorFactory().getValidator();
        Set<ConstraintViolation<SingleGrantReqVo>> errors = validatorFactory.validate(item);
        if (errors.iterator().hasNext()) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg(errors.iterator().next().getMessage());
        }
    }


    @Autowired
    private RecordItemBiz recordItemBiz;

    @PostMapping("/syncDataFeeFix")
    public ResponseDto<String> syncDataFeeFix(@RequestBody @Valid RequestDto<Map<String,Object>> paramVo){

        Map<String, Object> param = paramVo.getData();
        log.info("syncDataFeeFix接收参数:{}", JSONUtil.toJsonPrettyStr(param));
        ThreadUtil.execAsync(() -> {

            int pageCurrent = 1;
            int pageSize = 500;

            PageResult<List<OrderItem>> listPageResult = null;
            do {
                PageParam pageParam = PageParam.newInstance(pageCurrent, pageSize);
                log.info("syncDataFeeFix处理第{}页数据-开始", pageCurrent);
                listPageResult = orderItemBiz.listPage(param, pageParam);
                List<OrderItem> data = listPageResult.getData();
                if (data != null) {
                    for (OrderItem orderItem : data) {
                        RecordItem recordItem = recordItemBiz.getByPlatTrxNo(orderItem.getPlatTrxNo());
                        try {
                            zxhGrantBiz.notifyOrderComplete(orderItem, recordItem);
                        } catch (Exception e) {
                            log.info("订单完成通知失败:{}", orderItem.getPlatTrxNo());
                        }
                    }
                }
                log.info("syncDataFeeFix处理第{}页数据-完成", pageCurrent);
                pageCurrent++;
            } while (listPageResult != null && listPageResult.getData() != null && !listPageResult.getData().isEmpty());
        });

        return ResponseDto.success("操作成功");
    }
}
