package com.zhixianghui.service.trade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.dto.UserInfoQueryDto;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.vo.UserAuthVo;
import com.zhixianghui.service.trade.biz.UserInfoBiz;
import com.zhixianghui.service.trade.biz.WeChatUserBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @ClassName UserInfoFacadeImpl
 * @Description TODO
 * @Date 2022/2/21 11:59
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserInfoFacadeImpl implements UserInfoFacade {

    private final UserInfoBiz userInfoBiz;

    private final WeChatUserBiz weChatUserBiz;

    @Override
    public UserInfo getByIdCardNoMd5(String idCardNoMd5) {
        return userInfoBiz.getByIdCardNoMd5(idCardNoMd5);
    }

    @Override
    public UserAuthVo getAuthInfo(String phone) {
        return weChatUserBiz.getAuthInfo(phone);
    }

    @Override
    public IPage<UserInfo> userInfoIPage(IPage<UserInfo> page, UserInfoQueryDto userInfoQueryDto) {
        return userInfoBiz.userInfoIPage(page, userInfoQueryDto);
    }

    @Override
    public void deleteId(Long id) {
        userInfoBiz.deleteId(id);
    }

    @Override
    public boolean isVerified(String idcardNoMd5) throws BizException {
        return userInfoBiz.isVerified(idcardNoMd5);
    }

    @Override
    public void addUserInfo(UserInfo userInfo) throws BizException{
        userInfoBiz.insertOrUpdate(userInfo);
    }
}
