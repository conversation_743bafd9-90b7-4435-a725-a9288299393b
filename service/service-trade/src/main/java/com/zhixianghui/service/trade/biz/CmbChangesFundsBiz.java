package com.zhixianghui.service.trade.biz;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.trade.dto.CmbChangesFoundsQueryDTO;
import com.zhixianghui.facade.trade.entity.CmbChangesFunds;
import com.zhixianghui.facade.trade.entity.CmbIncomeRecord;
import com.zhixianghui.facade.trade.vo.CmbChangesFundsVO;
import com.zhixianghui.facade.trade.vo.CmbIncomeRecordVO;
import com.zhixianghui.service.trade.dao.mapper.CmbChangesFundsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/16 16:35
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CmbChangesFundsBiz {

    private final CmbChangesFundsMapper cmbChangesFundsMapper;

    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;


    public IPage<CmbChangesFundsVO> listPage(CmbChangesFoundsQueryDTO param) {
        Page<CmbChangesFunds> page = new Page<>(param.getPageCurrent(), param.getPageSize());
        return cmbChangesFundsMapper.selectPage(page, Wrappers.<CmbChangesFunds>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(param.getLogKey()), CmbChangesFunds::getLogKey, param.getLogKey())
                .eq(ObjectUtil.isNotEmpty(param.getPlatTrxNo()), CmbChangesFunds::getPlatTrxNo, param.getPlatTrxNo())
                .eq(ObjectUtil.isNotEmpty(param.getPlatBatchNo()), CmbChangesFunds::getPlatBatchNo, param.getPlatBatchNo())
                .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbChangesFunds::getMchNo, param.getMchNo())
                .like(ObjectUtil.isNotEmpty(param.getMchName()), CmbChangesFunds::getMchName, param.getMchName())
                .eq(ObjectUtil.isNotEmpty(param.getMainstayNo()), CmbChangesFunds::getMainstayNo, param.getMainstayNo())
                .like(ObjectUtil.isNotEmpty(param.getMainstayName()), CmbChangesFunds::getMainstayName, param.getMainstayName())
                .eq(ObjectUtil.isNotEmpty(param.getAmountChangeType()), CmbChangesFunds::getAmountChangeType, param.getAmountChangeType())
                .between(ObjectUtil.isNotEmpty(param.getCreateTimeBegin()) && ObjectUtil.isNotEmpty(param.getCreateTimeEnd()),
                        CmbChangesFunds::getCreateTime, param.getCreateTimeBegin(), param.getCreateTimeEnd())
                .orderByDesc(CmbChangesFunds::getCreateTime)
        ).convert(e -> {
            CmbChangesFundsVO record = new CmbChangesFundsVO();
            BeanUtil.copyProperties(e, record);
            return record;
        });
    }

    public void exportCmbChangesFunds(CmbChangesFoundsQueryDTO param, String loginName) {
        Map<String, Object> paramMap = BeanUtil.toMap(param);
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(loginName);
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.CMB_CHANGES_FUNDS_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.CMB_CHANGES_FUNDS_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.CMB_CHANGES_FUNDS_EXPORT.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }
}
