package com.zhixianghui.service.trade.process;

import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName AcceptInterface
 * @Description TODO
 * @Date 2022/6/28 18:56
 */
public interface AcceptInterface {

    /**
     * 计费
     * @param orderItem  订单明细
     */
    void calculateFee(OrderItem orderItem);

    /**
     * 异常处理通知
     * @param platTrxNo  订单明细号
     */
    void notifyHandlerException(String platTrxNo);

    /**
     * 通知开始受理
     * @param employerNo  用工企业编号
     * @param platBatchNo  订单批次号
     * @param platTrxNoList  订单明细列表
     */
    void notifyAcceptStart(String employerNo, String platBatchNo, List<String> platTrxNoList);

    /**
     * 发送批次统计MQ
     * @param platBatchNo 批次编号
     */
    void notifyAcceptBatchCount(String platBatchNo);
}
