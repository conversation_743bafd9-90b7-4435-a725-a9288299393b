package com.zhixianghui.service.trade.listener.tasks;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.service.trade.biz.AcLocalPayBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AC_SETTLE_SYNC, consumeThreadMax = 1, consumerGroup = "AcSettleTaskComsumer")
public class AcSettleTaskListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private AcLocalPayBiz acLocalPayBiz;
    @Autowired
    private RedisLock redisLock;

    @Override
    public void runTask(JSONObject map) {
        RLock rLock = redisLock.tryLock("AcSettleTaskComsumer");
        log.info("进入供应商结算定时任务");
        if (rLock == null) {
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try {
            String obj = (String)map.get("currentDate");
            Date date = DateUtil.yesterday();
            if(StringUtil.isNotEmpty(obj)){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                try {
                    date = sdf.parse(obj);
                } catch (ParseException e) {
                    log.info("供应商结算，日期格式化错误，解析json:{}",obj);
                }
            }

            acLocalPayBiz.handleMainstayLocalSettle(date);
        }finally {
            redisLock.unlock(rLock);
        }
    }
}
