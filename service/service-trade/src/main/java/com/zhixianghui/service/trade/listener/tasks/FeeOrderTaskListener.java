package com.zhixianghui.service.trade.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.trade.enums.FeeSourceEnum;
import com.zhixianghui.service.trade.biz.FeeOrderBatchBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月11日 09:51:00
 */
// test 0,30 * * * * ?
//0 0 2 * * ?
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FEE_ORDER_SYNC, consumeThreadMax = 3, consumerGroup = "feeOrderSyncHandler")
public class FeeOrderTaskListener extends TaskRocketMQListener<JSONObject> {

    @Autowired
    private FeeOrderBatchBiz feeOrderBatchBiz;

    @Override
    public void runTask(JSONObject jsonParam) {
        Object obj = jsonParam.get("currentDate");
        Date date = null;
        if(!ObjectUtils.isEmpty(obj)){
            String dateJson = String.valueOf(obj);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try {
                date = sdf.parse(dateJson);
            } catch (ParseException e) {
                log.info("账单同步，日期格式化错误，解析json:{}",dateJson);
            }
        }
        feeOrderBatchBiz.syncFeeOrder(date, FeeSourceEnum.PLATFORM_ORDER);
    }
}