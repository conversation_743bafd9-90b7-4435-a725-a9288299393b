package com.zhixianghui.service.trade.utils;

import com.zhixianghui.common.statics.dto.fee.CalculateResultDto;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-06 16:44
 **/
public class BuildVoUtil {
    /**
     * 填充失败信息
     * @param item 订单明细
     * @param apiErrorCode api错误码
     * @param errMsg 错误信息
     */
    public static void fillOrderItemAcceptFail(OrderItem item, String apiErrorCode, String errMsg) {
        item.setUpdateTime(new Date());
        item.setCompleteTime(new Date());
        item.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
        item.setErrorCode(apiErrorCode);
        item.setErrorDesc(errMsg);
    }

    public static void fillOrderItemAcceptFail(OfflineOrderItem item, String apiErrorCode, String errMsg) {
        item.setUpdateTime(new Date());
        item.setCompleteTime(new Date());
        item.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
        item.setErrorCode(apiErrorCode);
        item.setErrorDesc(errMsg);
    }

    public static void fillOrderItemAcceptSuccess(OrderItem item, CalculateResultDto calculateResultDto) {
        BigDecimal orderFee = calculateResultDto.getOrderFee();
        item.setOrderItemFee(orderFee);
        item.setOrderItemAmount(item.getOrderItemNetAmount().add(orderFee));
        item.setUpdateTime(new Date());
        item.setOrderItemStatus(OrderItemStatusEnum.ACCEPTED.getValue());
    }

    public static PayReqVo fillPayReqVo(RecordItem recordItem, EmployerAccountInfo employerAccountInfo, OrderItem orderItem) {
        PayReqVo payReqVo = new PayReqVo();
        payReqVo.setPayerChannelAccountNo(employerAccountInfo.getSubMerchantNo());
        payReqVo.setBankOrderNo(recordItem.getRemitPlatTrxNo());
        payReqVo.setReceiveName(recordItem.getReceiveNameDecrypt());
        payReqVo.setReceiveAccountNo(recordItem.getReceiveAccountNoDecrypt());
        payReqVo.setReceiveAmount(String.valueOf(recordItem.getOrderNetAmount()));
        payReqVo.setServiceFee(String.valueOf(recordItem.getOrderFee()));
        payReqVo.setTotalAmount(String.valueOf(recordItem.getOrderAmount()));
        payReqVo.setRemitRemark(recordItem.getRemark());
        payReqVo.setChannelNo(recordItem.getPayChannelNo());
        payReqVo.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
        payReqVo.setChannelType(employerAccountInfo.getChannelType());
        payReqVo.setPayeeAgreementNo(employerAccountInfo.getParentAgreementNo());
        payReqVo.setPayerAgreementNo(employerAccountInfo.getSubAgreementNo());
        payReqVo.setParentMerchantNo(employerAccountInfo.getParentMerchantNo());
        payReqVo.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
        payReqVo.setRealPayerName(employerAccountInfo.getMainstayName());
        payReqVo.setPlatTrxNo(recordItem.getPlatTrxNo());
        payReqVo.setMchName(recordItem.getEmployerName());
        payReqVo.setCreateTime(recordItem.getCreateTime());
        payReqVo.setEmployerNo(recordItem.getEmployerNo());
        payReqVo.setIdCardNumber(recordItem.getReceiveIdCardNoDecrypt());
        payReqVo.setMainstayNo(recordItem.getMainstayNo());
        if (orderItem != null) {
            payReqVo.setAppid(orderItem.getAppid());
            payReqVo.setPayBatchNo(orderItem.getPlatBatchNo());
        }
        return payReqVo;
    }

    public static SettleRiskControlVo fillSettleRiskControlVo(OrderItem orderItem,Date startTradeTime) {
        SettleRiskControlVo settleRiskControlVo = new SettleRiskControlVo();
        settleRiskControlVo.setOrderAmount(orderItem.getOrderItemNetAmount());
        settleRiskControlVo.setOrderNo(orderItem.getPlatTrxNo());
        settleRiskControlVo.setSupplierNo(orderItem.getMainstayNo());
        settleRiskControlVo.setEmployerNo(orderItem.getEmployerNo());
        settleRiskControlVo.setUserIdCard(orderItem.getReceiveIdCardNoDecrypt());
        settleRiskControlVo.setUserName(orderItem.getReceiveNameDecrypt());
        settleRiskControlVo.setPlatTrxNo(orderItem.getPlatTrxNo());
        settleRiskControlVo.setPayRemark(orderItem.getRemark());
        settleRiskControlVo.setPhone(orderItem.getReceivePhoneNoDecrypt());
        settleRiskControlVo.setReceiveAccount(orderItem.getReceiveAccountNoDecrypt());
        settleRiskControlVo.setStartTradeTime(startTradeTime);
        return settleRiskControlVo;
    }

    public static SettleRiskControlVo fillCmbSettleRiskControlVo(OrderItem orderItem,BigDecimal itemNetAmount,Date startTradeTime) {
        SettleRiskControlVo settleRiskControlVo = new SettleRiskControlVo();
        settleRiskControlVo.setOrderAmount(itemNetAmount);
        settleRiskControlVo.setOrderNo(orderItem.getPlatTrxNo());
        settleRiskControlVo.setSupplierNo(orderItem.getMainstayNo());
        settleRiskControlVo.setEmployerNo(orderItem.getEmployerNo());
        settleRiskControlVo.setUserIdCard(orderItem.getReceiveIdCardNoDecrypt());
        settleRiskControlVo.setUserName(orderItem.getReceiveNameDecrypt());
        settleRiskControlVo.setPlatTrxNo(orderItem.getPlatTrxNo());
        settleRiskControlVo.setPayRemark(orderItem.getRemark());
        settleRiskControlVo.setPhone(orderItem.getReceivePhoneNoDecrypt());
        settleRiskControlVo.setReceiveAccount(orderItem.getReceiveAccountNoDecrypt());
        settleRiskControlVo.setStartTradeTime(startTradeTime);
        return settleRiskControlVo;
    }
}
