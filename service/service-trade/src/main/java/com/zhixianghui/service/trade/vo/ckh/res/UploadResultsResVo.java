package com.zhixianghui.service.trade.vo.ckh.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName UploadResultsResVo
 * @Description TODO
 * @Date 2022/10/27 15:04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UploadResultsResVo extends ApiBizBaseDto {

    private String name;

    private String idCardNo;

    private String phoneNo;

    private List<String> fileUrlList;

    private String status;
}
