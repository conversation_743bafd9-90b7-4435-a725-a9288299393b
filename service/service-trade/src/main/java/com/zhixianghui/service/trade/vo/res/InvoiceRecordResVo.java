package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName InvoiceRecordResVo
 * @Description TODO
 * @Date 2022/9/28 18:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class InvoiceRecordResVo extends ApiBizBaseDto {

    List<InvoiceRecordVo> invoiceRecordList;

}
