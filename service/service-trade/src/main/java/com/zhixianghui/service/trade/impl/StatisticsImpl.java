package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.service.StatisticsFacade;
import com.zhixianghui.facade.trade.vo.statistics.MonthlyOverviewVo;
import com.zhixianghui.service.trade.biz.StatisticsBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年11月23日 10:41:00
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StatisticsImpl implements StatisticsFacade {

    private final StatisticsBiz biz;

    @Override
        public MonthlyOverviewVo getMonthlyOverview(String nowDate) {
        return biz.getMonthlyOverview(nowDate);
    }
}