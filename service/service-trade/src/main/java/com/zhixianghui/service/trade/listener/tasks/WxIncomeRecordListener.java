package com.zhixianghui.service.trade.listener.tasks;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.trade.vo.WxAmountChangeLogVo;
import com.zhixianghui.service.trade.biz.WxIncomeRecordBiz;
import com.zhixianghui.service.trade.biz.WxMerchantBalanceBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import com.zhixianghui.starter.comp.component.RedisLock;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WxIncomeRecordListener
 * @Description TODO
 * @Date 2021/12/8 11:51
 */
@Slf4j
@Component
public class WxIncomeRecordListener{

    @Autowired
    private WxIncomeRecordBiz wxIncomeRecordBiz;

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Autowired
    private WxMerchantBalanceBiz wxMerchantBalanceBiz;

    /**
     * 定时任务获取来账通知接口
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_INCOME_RECORD,consumeThreadMax = 3,consumerGroup = "wxIncomeRecordConsumerTest")
    public class WxIncomeRecordTaskListener extends TaskRocketMQListener<JSONObject> {
        @Override
        public void runTask(JSONObject jsonParam) {
            List<MainstayChannelRelation> list =  mainstayChannelRelationFacade
                    .listBy(new HashMap<String,Object>(){{put("payChannelNo", ChannelNoEnum.WXPAY.name());}})
                    .stream().filter(x-> StringUtils.isNotBlank(x.getChannelMchNo())).collect(Collectors.toList());
            if (list != null && list.size() > 0){
                String now = DateUtil.formatDate(new Date());
                CompletableFuture.runAsync(()-> wxIncomeRecordBiz.getIncomeRecord(list,now));
            }
        }
    }

    /**
     * 来账通知异常处理
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_INCOME_RECORD_ERROR,selectorExpression = MessageMsgDest.TAG_WX_INCOME_RECORD_ERROR,consumeThreadMax = 3,consumerGroup = "wxIncomeRecordErrorConsumerTest")
    public class WxIncomeRecordErrorListener extends BaseRocketMQListener<String>{
        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            MainstayChannelRelation mainstayChannelRelation = JsonUtil.toBean(jsonParam,MainstayChannelRelation.class);
            wxIncomeRecordBiz.getIncomeRecord(new ArrayList<MainstayChannelRelation>(){{add(mainstayChannelRelation);}},mainstayChannelRelation.getIncomeRecordTime());
        }
    }

    /**
     * 确认入账
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_AMOUNT_CHANGE,selectorExpression = MessageMsgDest.TAG_WX_AMOUNT_CHANGE,consumeThreadMax = 1,consumerGroup = "confirmIncomeRecordConsumer")
    public class ConfirmWxIncomeRecordListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            WxAmountChangeLogVo wxAmountChangeLogVo = JsonUtil.toBean(jsonParam,WxAmountChangeLogVo.class);
            wxIncomeRecordBiz.confirmIncomeRecord(wxAmountChangeLogVo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_REPORT,selectorExpression = MessageMsgDest.TAG_WX_REPORT,consumeThreadMax = 3,consumerGroup = "wxReportConsumer")
    public class WxReportListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            ReportEntity reportEntity = JsonUtil.toBean(jsonParam,ReportEntity.class);
            wxMerchantBalanceBiz.createAccount(reportEntity);
        }
    }

}

