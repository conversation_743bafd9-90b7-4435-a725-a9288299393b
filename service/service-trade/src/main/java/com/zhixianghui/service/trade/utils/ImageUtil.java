package com.zhixianghui.service.trade.utils;

import cn.hutool.core.io.FileUtil;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import net.coobird.thumbnailator.Thumbnails;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * <AUTHOR>
 * @Date 2021/6/29 14:29
 */
public class ImageUtil {

    /**
     *
     * @param path 图片完整路径
     *             eg. /data/to/a.jpg
     * @return
     */
    public static byte[] generateByte(String path) {
        // 将图片文件转化为字节数组字符串
        byte[] data = null;
        // 读取图片字节数组
        try (InputStream in = new FileInputStream(path);) {
            data = new byte[in.available()];
            in.read(data);
        } catch (IOException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("图片生成字节数组异常:" + e);
        }
        return data;
    }

    /**
     *
     * @param image 图片数组
     * @return 压缩后的图片数组
     */
    public static byte[] decompressImageByte(byte[] image, String format) {
        ByteArrayInputStream intPutStream = new ByteArrayInputStream(image);
        // 缩小0.5倍
        Thumbnails.Builder<? extends InputStream> builder = Thumbnails.of(intPutStream).scale(0.5);
        try {
            BufferedImage bufferedImage = builder.asBufferedImage();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, format, baos);
            return baos.toByteArray();
        } catch (IOException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("压缩图片异常:" + e);
        }
    }

    /**
     *
     * @param image 图片字节数组
     * @param path 路径
     *             eg, /data/a.jpg
     * @throws Exception
     */
    public static void byteToImage(byte[] image, String path) throws Exception {
        FileOutputStream fileOutputStream = new FileOutputStream(new File(path));
        fileOutputStream.write(image);
        fileOutputStream.close();
    }

    /**
     *
     * @param data 字节数组
     * @return base64 字符串
     */
    public static String encoderBase64(byte[] data) {
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(data);
    }

    /**
     *
     * @param base64 base64字符串
     * @return 字节数组
     * @throws IOException
     */
    public static byte[] translateBase64(String base64) throws IOException {
        BASE64Decoder decoder = new BASE64Decoder();
        return decoder.decodeBuffer(base64);
    }

    public static void main(String[] args) {
        final String s = FileUtil.readString("D:/base64.txt", "gbk");
        System.out.println(s);
        try {
            final byte[] bytes = ImageUtil.translateBase64(s);

            FileUtils.byte2file(bytes, "D:/pic.png");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
