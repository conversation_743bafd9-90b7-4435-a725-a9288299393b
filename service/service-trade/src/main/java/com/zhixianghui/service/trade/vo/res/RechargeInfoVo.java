package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName RechargeInfoVo
 * @Description TODO
 * @Date 2023/5/29 15:16
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RechargeInfoVo {

    private String channelName;

    private Integer channelType;

    private String accountName = "";

    private String accountNo = "";

    private String bankName = "";

    private String subBankName = "";

    private String bankAddress = "";

    private String joinBankNo = "";

    public void setAccountName(String accountName) {
        if (accountName != null){
            this.accountName = accountName;
        }
    }

    public void setAccountNo(String accountNo) {
        if (accountNo != null){
            this.accountNo = accountNo;
        }
    }

    public void setBankName(String bankName) {
        if (bankName != null){
            this.bankName = bankName;
        }
    }

    public void setSubBankName(String subBankName) {
        if (subBankName != null){
            this.subBankName = subBankName;
        }
    }

    public void setBankAddress(String bankAddress) {
        if (bankAddress != null){
            this.bankAddress = bankAddress;
        }
    }

    public void setJoinBankNo(String joinBankNo) {
        if (joinBankNo != null){
            this.joinBankNo = joinBankNo;
        }
    }
}
