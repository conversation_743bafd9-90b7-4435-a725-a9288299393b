package com.zhixianghui.service.trade.process;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote;
import com.zhixianghui.facade.common.entity.individualproxy.MainstayIndividualProxy;
import com.zhixianghui.facade.common.service.MainstayIndividualProxyFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.dto.ConfirmInvoiceDto;
import com.zhixianghui.facade.trade.dto.IndividualProxyOrderDto;
import com.zhixianghui.facade.trade.dto.ProxyOrderQueryDto;
import com.zhixianghui.facade.trade.entity.IndividualProxyOrder;
import com.zhixianghui.facade.trade.entity.ProxyOrderItem;
import com.zhixianghui.facade.trade.enums.IndividualProxyOrderPayStatus;
import com.zhixianghui.facade.trade.enums.IndividualProxyOrderStatus;
import com.zhixianghui.facade.trade.vo.ProxyPayDetailVo;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.trade.biz.IndividualProxyOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class IndividualProxyBiz {

    @Autowired
    private IndividualProxyOrderService proxyOrderService;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private MainstayIndividualProxyFacade mainstayIndividualProxyFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Autowired
    private WxJsapiPayBiz wxJsapiPayBiz;

    public IndividualProxyOrder applyInvoice(IndividualProxyOrderDto dto, WeChatLoginVo weChatLoginVo) {
        /**
         * 1. 订单初始化
         */
        final IndividualProxyOrder order = this.initOrder(dto, weChatLoginVo);

        /**
         * 2. 处理明细，计算费用,生成费用详情
         */
        final MainstayIndividualProxy mainstay = mainstayIndividualProxyFacade.getByMainstayNo(dto.getMainstayNo());
        if (!mainstay.getStatus()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("服务商不可用");
        }
        final List<IndividualProxyQuote> taxQuote = mainstay.getTaxQuote();
        if (taxQuote == null || taxQuote.isEmpty()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该服务商缺少报价");
        }


        final List<ProxyOrderItem> invoiceDetails = dto.getInvoiceDetail();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalPayAmount = BigDecimal.ZERO;
        ProxyPayDetailVo proxyPayDetailVo = new ProxyPayDetailVo();
        for (ProxyOrderItem invoiceDetail : invoiceDetails) {
            totalAmount = totalAmount.add(invoiceDetail.getInvoiceAmount());

            final IndividualProxyQuote proxyQuote = this.findQuote(taxQuote, invoiceDetail);
            final BigDecimal taxAmount = proxyQuote.getTaxRatio().multiply(invoiceDetail.getInvoiceAmount()).setScale(2,RoundingMode.HALF_UP);
            final BigDecimal vatAmount = proxyQuote.getVatRatio().multiply(invoiceDetail.getInvoiceAmount()).setScale(2,RoundingMode.HALF_UP);
            final BigDecimal localEduAddTaxAmount = proxyQuote.getLocalEduAddTaxRatio().multiply(invoiceDetail.getInvoiceAmount()).setScale(2,RoundingMode.HALF_UP);
            final BigDecimal eduAddTaxAmount = proxyQuote.getEduAddTaxRatio().multiply(invoiceDetail.getInvoiceAmount()).setScale(2,RoundingMode.HALF_UP);
            final BigDecimal buildingTaxAmount = proxyQuote.getBuildingTaxRatio().multiply(invoiceDetail.getInvoiceAmount()).setScale(2,RoundingMode.HALF_UP);
            final BigDecimal stampDutyAmount = proxyQuote.getStampDutyRatio().multiply(invoiceDetail.getInvoiceAmount()).setScale(2,RoundingMode.HALF_UP);
            final BigDecimal serviceFeeAmount = mainstay.getServiceFeeRatio().multiply(invoiceDetail.getInvoiceAmount()).setScale(2,RoundingMode.HALF_UP);

            totalPayAmount = totalPayAmount.add(taxAmount).add(vatAmount).add(localEduAddTaxAmount)
                    .add(eduAddTaxAmount).add(buildingTaxAmount).add(stampDutyAmount).add(serviceFeeAmount);

            proxyPayDetailVo.setTaxAmount(taxAmount);
            proxyPayDetailVo.setLocalEduAddTaxAmount(localEduAddTaxAmount);
            proxyPayDetailVo.setEduAddTaxAmount(eduAddTaxAmount);
            proxyPayDetailVo.setVatAmount(vatAmount);
            proxyPayDetailVo.setBuildingTaxAmount(buildingTaxAmount);
            proxyPayDetailVo.setServiceFeeAmount(serviceFeeAmount);
            proxyPayDetailVo.setStampDutyAmount(stampDutyAmount);
        }
        order.setOrderAmount(totalAmount);
        order.setPayAmount(totalPayAmount);
        order.setFeeDetail(proxyPayDetailVo);
        //设置支付过期时间10min
        order.setExpireTime(DateUtil.addMinute(new Date(),10).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        //发送消息检查是否需要关单
        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_JSAPI_CLOSE,
                order.getOrderNo(),
                order.getOrderNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),
                MessageMsgDest.TAG_WX_JSAPI_CLOSE,
                order.getOrderNo(),
                MsgDelayLevelEnum.M_10.getValue());
        proxyOrderService.save(order);

        return order;
    }

    private IndividualProxyOrder initOrder(IndividualProxyOrderDto dto, WeChatLoginVo weChatLoginVo) {
        final IndividualProxyOrder order = BeanUtil.toBean(dto, IndividualProxyOrder.class);
        final String oderNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.PROXY_ORDER_NO_SEQ.getPrefix(), SequenceBizKeyEnum.PROXY_ORDER_NO_SEQ.getKey(), SequenceBizKeyEnum.PROXY_ORDER_NO_SEQ.getWidth());
        order.setOrderNo(oderNo);
        order.setOrderStatus(IndividualProxyOrderStatus.WAITE_PAY.getValue());
        order.setPayStatus(IndividualProxyOrderPayStatus.NOT_PAYED.getValue());
        order.setInvoiceStatus(InvoiceStatusEnum.WAIT_ISSUE.getValue());
        order.setIdCardNo(weChatLoginVo.getIdCard());
        order.setUserMobile(weChatLoginVo.getMobile());

        return order;
    }

    private IndividualProxyQuote findQuote(List<IndividualProxyQuote> taxQuote,ProxyOrderItem invoiceDetail) {
        IndividualProxyQuote proxyQuote = null;
        for (IndividualProxyQuote individualProxyQuote : taxQuote) {
            if (StringUtils.equals(individualProxyQuote.getInvoiceCategoryCode(), invoiceDetail.getInvoiceCategoryCode())) {
                proxyQuote = individualProxyQuote;
                break;
            } else {
                final List<IndividualProxyQuote> subcategorys = individualProxyQuote.getSubcategorys();
                boolean findQuote = false;
                if (subcategorys != null) {
                    for (IndividualProxyQuote subcategory : subcategorys) {
                        if (StringUtils.equals(subcategory.getInvoiceCategoryCode(), invoiceDetail.getInvoiceCategoryCode())) {
                            proxyQuote = subcategory;
                            findQuote = true;
                            break;
                        }
                    }
                    if (findQuote) {
                        break;
                    }
                }
            }
        }
        return proxyQuote;
    }

    public Page<IndividualProxyOrder> listOrderPage(ProxyOrderQueryDto queryDto, Page page) {
        return proxyOrderService.listPage(page, BeanUtil.beanToMap(queryDto));
    }

    public IndividualProxyOrder getProxyOrderById(Long id,String idcardNo) {
        final IndividualProxyOrder proxyOrder = proxyOrderService.getById(id);
        if (!StringUtils.equals(proxyOrder.getIdCardNo(), idcardNo)) {
            return null;
        }
        return proxyOrder;
    }

    public IndividualProxyOrder getProxyOrderById(Long id) {
        final IndividualProxyOrder proxyOrder = proxyOrderService.getById(id);
        return proxyOrder;
    }

    public IndividualProxyOrder cancelProxyOrderById(Long id,String idcardNo) {
        final IndividualProxyOrder proxyOrder = proxyOrderService.getById(id);
        if (!StringUtils.equals(proxyOrder.getIdCardNo(), idcardNo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单不存在");
        }
        proxyOrder.setOrderStatus(IndividualProxyOrderStatus.CANCELLED.getValue());
        proxyOrderService.updateById(proxyOrder);
        return proxyOrder;
    }

    public void confirmInvoice(ConfirmInvoiceDto confirmInvoiceDto) {
        final IndividualProxyOrder proxyOrder = proxyOrderService.getById(confirmInvoiceDto.getId());
        if (proxyOrder == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("原始订单不存在");
        }
        final Integer currentStatus = proxyOrder.getOrderStatus();
        if (confirmInvoiceDto.getAction() == 1) {
            if (IndividualProxyOrderStatus.WAITE_PAY.getValue() == currentStatus.intValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单未付款，不能开票");
            }
            if (IndividualProxyOrderStatus.WAITE_INVOICE.getValue() == currentStatus.intValue()) {
                if (confirmInvoiceDto.getStatus().intValue() == IndividualProxyOrderStatus.COMPLETED.getValue()
                        || confirmInvoiceDto.getStatus().intValue() == IndividualProxyOrderStatus.WAITE_PAY.getValue()) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不可转为该状态");
                }
            }
            if (IndividualProxyOrderStatus.WAITE_POST.getValue() == currentStatus.intValue()) {
                if (confirmInvoiceDto.getStatus().intValue() == IndividualProxyOrderStatus.WAITE_INVOICE.getValue()
                        || confirmInvoiceDto.getStatus().intValue() == IndividualProxyOrderStatus.WAITE_PAY.getValue()) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不可转为该状态");
                }
            }
            if (IndividualProxyOrderStatus.CANCELLED.getValue() == currentStatus.intValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单已取消，不能开票");
            }

            proxyOrder.setErrorRemark(confirmInvoiceDto.getErrorRemark());
            proxyOrder.setOrderStatus(confirmInvoiceDto.getStatus());
            proxyOrder.setExpressNo(confirmInvoiceDto.getExpressNo());
            proxyOrder.setInvoiceUrl(confirmInvoiceDto.getInvoiceUrl());

            proxyOrderService.updateById(proxyOrder);
        } else if (confirmInvoiceDto.getAction() == 2) {
            if (currentStatus.intValue() != IndividualProxyOrderStatus.WAITE_PAY.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("只有待支付状态订单可以取消");
            }
            proxyOrder.setErrorRemark("订单取消");
            proxyOrder.setOrderStatus(IndividualProxyOrderStatus.CANCELLED.getValue());
            proxyOrderService.updateById(proxyOrder);
        }else {//退款
            if (currentStatus.intValue() != IndividualProxyOrderStatus.WAITE_INVOICE.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("只有待开票状态订单可以退款");
            }
            proxyOrder.setErrorRemark(confirmInvoiceDto.getErrorRemark());
            proxyOrder.setRefundReason(confirmInvoiceDto.getRefundReason());
            //异步退款
            wxJsapiPayBiz.refund(proxyOrder);
        }
    }
}
