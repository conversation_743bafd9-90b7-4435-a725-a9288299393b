package com.zhixianghui.service.trade.process;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.fee.CalculateResultDto;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import com.zhixianghui.common.statics.enums.fee.ProductFeeSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.fee.service.MerchantFeeCalculateFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.service.trade.utils.BuildVoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ZXHAccpetBiz
 * @Description TODO
 * @Date 2022/7/4 10:32
 */
@Slf4j
@Service
public class ZXHAcceptBiz extends AbstractAcceptHandler{

    @Reference
    private MerchantFeeCalculateFacade productFeeCalculateFacade;

    @Override
    public void calculateFee(OrderItem orderItem) {
        log.info("[受理环节: {}]==>订单明细开始计费", orderItem.getPlatTrxNo());
        //代征主体编号
        SpecialRuleParamDto param1 = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.VERDOR_NO.getValue(),orderItem.getMainstayNo());
        //实发金额
        SpecialRuleParamDto param2 = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),String.valueOf(orderItem.getOrderItemNetAmount()));
        List<SpecialRuleParamDto> ruleReqList = Lists.newArrayList(param1,param2);
        String productNo = orderItem.getProductNo();
        CalculateResultDto calculateResultDto = productFeeCalculateFacade.calculateFee(orderItem.getEmployerNo(), productNo, orderItem.getOrderItemNetAmount(),ruleReqList,orderItem.getPlatTrxNo());
        log.info("[受理环节: {}]==>订单明细计费结束,计费结果:{}", orderItem.getPlatTrxNo(), JSON.toJSONString(calculateResultDto));
        BigDecimal orderFee = calculateResultDto.getOrderFee();
        orderItem.setOrderItemFee(orderFee);
        orderItem.setOrderItemAmount(orderItem.getOrderItemNetAmount().add(orderFee));
    }

    @Override
    public void notifyHandlerException(String platTrxNo) {
        //非业务异常延迟队列(避开类似服务暂时异常)
        log.info("[{}]==>发送受理异常处理mq", platTrxNo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),MessageMsgDest.TAG_ACCEPT_NOT_BIZ_EXCEPTION,platTrxNo, MsgDelayLevelEnum.S_10.getValue());
    }

    @Override
    public void notifyAcceptStart(String employerNo, String platBatchNo, List<String> platTrxNoList) {
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(platTrxNoList));
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_ACCEPT,JSON.toJSONString(infoMap));
    }

    @Override
    public void notifyAcceptBatchCount(String platBatchNo) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_ACCEPT_BATCH_COUNT,platBatchNo);
    }
}
