package com.zhixianghui.service.trade.process;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.trade.entity.SignRecord;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2021/5/31 9:52
 */
public abstract class AbstractSign {


    /**
     * 判断企业或代征主体是否有自定义签约模板
     * @param signRecord
     * @param merchantFacade
     * @return 模板id
     */
    public String existTemplateId(SignRecord signRecord, MerchantFacade merchantFacade) {
        Merchant merchant = merchantFacade.getByMchNo(signRecord.getEmployerNo());
        if (merchant == null || merchant.getMerchantType() != MerchantTypeEnum.EMPLOYER.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业不存在");
        }
        if (StringUtils.isNotBlank(merchant.getTemplateId())) {
            return merchant.getTemplateId();
        }
        merchant = merchantFacade.getByMchNo(signRecord.getMainstayNo());
        if (merchant == null || merchant.getMerchantType() != MerchantTypeEnum.MAINSTAY.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        if (StringUtils.isNotBlank(merchant.getTemplateId())) {
            return merchant.getTemplateId();
        }
        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户未配置模板id");
    }



}
