package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.api.base.params.FileParam;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.employee.dto.JobDto;
import com.zhixianghui.facade.employee.dto.WorkerRecordQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.*;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.employee.service.JobWorkerRecordFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.service.MerchantCacheFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerCooperateFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.vo.MerchantPositionVo;
import com.zhixianghui.facade.trade.vo.auth.AuthResponseVo;
import com.zhixianghui.service.trade.utils.ImageUtil;
import com.zhixianghui.service.trade.vo.ckh.req.*;
import com.zhixianghui.service.trade.vo.ckh.res.JobDetailResVo;
import com.zhixianghui.service.trade.vo.ckh.res.WorkerPageResVo;
import com.zhixianghui.service.trade.vo.res.CommonPageResVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ApiJobBiz
 * @Description TODO
 * @Date 2022/10/25 16:38
 */
@Slf4j
@Service
public class ApiJobBiz {

    @Value("${file.domain}")
    private String DOMAIN_PREFIX;

    @Autowired
    private FastdfsClient fastdfsClient;

    @Reference
    private MerchantCacheFacade merchantCacheFacade;

    @Reference
    private MerchantEmployerCooperateFacade merchantEmployerCooperateFacade;

    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;

    @Reference
    private JobFacade jobFacade;

    @Reference
    private JobWorkerRecordFacade jobWorkerRecordFacade;

    @Autowired
    private AuthBiz authBiz;

    //图片大小限制
    private static final long LIMIT_SIZE = 2 * 1048576L;

    //文件类型
    private List<String> FILE_EXT = Arrays.asList(new String[]{"jpg","png","jpeg"});

    public Job createJob(String mchNo, CreateJobReqVo createJobReqVo) {
        Merchant merchant = validateMch(mchNo);

        MerchantEmployerPosition position = merchantEmployerPositionFacade.getByMchNoAndWorkCategoryCode(mchNo,createJobReqVo.getPositionNo());
        if (position == null){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("商户不存在此岗位类型编码");
        }

        MerchantEmployerCooperate merchantEmployerCooperate = merchantEmployerCooperateFacade.getByMchNo(mchNo);
        if (merchantEmployerCooperate == null){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("商户信息异常");
        }

        JobDto jobDto = buildJob(createJobReqVo,merchant,position,merchantEmployerCooperate);
        Job job = jobFacade.addJobApi(jobDto);
        return job;
    }

    /**
     * 构建任务
     * @param createJobReqVo
     * @param merchant
     * @param position
     * @return
     */
    private JobDto buildJob(CreateJobReqVo createJobReqVo,
                            Merchant merchant,
                            MerchantEmployerPosition position,
                            MerchantEmployerCooperate merchantEmployerCooperate) {
        JobDto jobDto = new JobDto();
        jobDto.setJobTag(JSONArray.toJSONString(createJobReqVo.getTag() == null ? new ArrayList<>() : createJobReqVo.getTag()));
        jobDto.setScope(createJobReqVo.getScope());
        jobDto.setJobName(createJobReqVo.getJobName());
        jobDto.setAcceptMode(createJobReqVo.getAcceptMode());
        jobDto.setIndustryCode(merchantEmployerCooperate.getIndustryTypeCode());
        jobDto.setIndustryName(merchantEmployerCooperate.getIndustryTypeName());
        jobDto.setWorkCategoryCode(position.getWorkCategoryCode());
        jobDto.setWorkCategoryName(position.getWorkCategoryName());
        jobDto.setEmployerNo(merchant.getMchNo());
        jobDto.setEmployerName(merchant.getMchName());
        jobDto.setJobStatus(JobStatusEnum.NEW.getCode());
        jobDto.setCallbackUrl(createJobReqVo.getCallbackUrl());
        //任务时间
        if (StringUtils.isNotBlank(createJobReqVo.getJobBeginDate()) && StringUtils.isNotBlank(createJobReqVo.getJobEndDate())){
            jobDto.setJobAvalDateType(JobAvalDateTypeEnum.SHORT_TIME.getCode());
            jobDto.setJobStartDate(LocalDate.parse(createJobReqVo.getJobBeginDate()));
            jobDto.setJobFinishDate(LocalDate.parse(createJobReqVo.getJobEndDate()));
        }else{
            jobDto.setJobAvalDateType(JobAvalDateTypeEnum.LONG_TIME.getCode());
        }

        //工作时间
        if (StringUtils.isNotBlank(createJobReqVo.getWorkBeginTime()) && StringUtils.isNotBlank(createJobReqVo.getWorkEndTime())){
            jobDto.setJobTimeStart(LocalTime.parse(createJobReqVo.getWorkBeginTime()));
            jobDto.setJobTimeEnd(LocalTime.parse(createJobReqVo.getWorkEndTime()));
        }else{
            jobDto.setJobTimeStart(LocalTime.MIN);
            jobDto.setJobTimeEnd(LocalTime.MAX);
        }

        jobDto.setJobProvinceName(createJobReqVo.getProvince());
        jobDto.setJobCityName(createJobReqVo.getCity());
        jobDto.setJobAreaName(createJobReqVo.getArea());
        jobDto.setJobAddress(createJobReqVo.getAddress());
        jobDto.setWorkerNum(createJobReqVo.getWorkerNum());
        jobDto.setRewardType(RewardTypeEnum.DISCUSS.getCode());
        jobDto.setWorkerGender(createJobReqVo.getSex());
        jobDto.setEduBackground(createJobReqVo.getEduBackground());
        jobDto.setProfessionalSkill(createJobReqVo.getSkill());
        jobDto.setJobDescribe(createJobReqVo.getDescription());
        jobDto.setDeliveryStandard(createJobReqVo.getStandard());

        //年龄
        if (createJobReqVo.getMinAge() != null && createJobReqVo.getMaxAge() != null){
            jobDto.setWorkerAgeLimitMin(createJobReqVo.getMinAge());
            jobDto.setWorkerAgeLimitMax(createJobReqVo.getMaxAge());
            jobDto.setWorkerAgeLimitType(WorkerAgeLimitTypeEnum.LIMIT.getCode());
        }else{
            jobDto.setWorkerAgeLimitType(WorkerAgeLimitTypeEnum.NO_LIMIE.getCode());
        }
        return jobDto;
    }

    /**
     * 校验商户状态
     * @param mchNo
     */
    private Merchant validateMch(String mchNo) {
        Merchant merchant = merchantCacheFacade.getByMchNo(mchNo);
        if (merchant == null){
            throw ApiExceptions.API_TRADE_MERCHANT_NOT_EXIST;
        }

        if (merchant.getMchStatus().intValue() != MchStatusEnum.ACTIVE.getValue()){
            throw ApiExceptions.API_TRADE_MERCHANT_STATUS_FAIL;
        }
        return merchant;
    }

    /**
     * 关联人员任务 *
     * @param addWorkerReqVo
     * @param mchNo
     */
    public JobWorkerRecord addWorker(AddWorkerReqVo addWorkerReqVo, String mchNo) {
        validateMch(mchNo);

        Job job = jobFacade.getJobByJobId(addWorkerReqVo.getJobNo());
        if (job == null || !job.getEmployerNo().equals(mchNo)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("job_no 任务不存在");
        }

        if(job.getJobStatus().intValue() != JobStatusEnum.PROCESSING.getCode().intValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("任务状态非进行中，无法关联人员");
        }

        AuthResponseVo authResp = authBiz.auth(addWorkerReqVo.getName(),addWorkerReqVo.getIdCardNo(),
                addWorkerReqVo.getPhoneNo(),"", AuthTypeEnum.IDCARD_NAME_PHONE.getValue(),null);

        //鉴权失败
        if (authResp.getAuthStatus().intValue() != BankAuthStatusEnum.SUCCESS.getValue()){
            throw ApiExceptions.API_TRADE_AUTH_FAIL.newWithErrMsg(authResp.getBizDesc());
        }

        JobWorkerRecord workerRecord = jobWorkerRecordFacade.addWorkerApi(job,addWorkerReqVo.getName(),
                addWorkerReqVo.getIdCardNo(),addWorkerReqVo.getPhoneNo(),mchNo);

        return workerRecord;
    }

    public JobWorkerRecord uploadResults(List<FileParam> fileParamList,UploadResultsReqVo uploadResultsReqVo, String mchNo) {
        validateMch(mchNo);

        Job job = jobFacade.getJobByJobId(uploadResultsReqVo.getJobNo());
        if (job == null || !job.getEmployerNo().equals(mchNo)){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("job_no 任务不存在");
        }

        JobWorkerRecord jobWorkerRecord = jobWorkerRecordFacade.getByJobIdAndPhoneNo(job.getJobId(),uploadResultsReqVo.getPhoneNo());
        if (jobWorkerRecord == null) {
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("雇员不存在");
        }

        if (jobWorkerRecord.getJobStatus().intValue() != JobWorkerStatusEnum.PROCESSING.getCode()){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("该雇员任务状态不为进行中，交付失败");
        }


        List<String> file;
        //处理图片文件
        try {
            file = handleImage(fileParamList);
        }catch (IOException e){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("交付图片处理异常");
        }

        //更新雇员状态
        jobWorkerRecord.setAttachment(JSONArray.toJSONString(file));
        jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.TO_BE_CONFIRMED.getCode());
        jobWorkerRecord.setDeliveryContent(uploadResultsReqVo.getTextResult());
        jobWorkerRecordFacade.update(jobWorkerRecord);
        return jobWorkerRecord;
    }


    private List<String> handleImage(List<FileParam> fileList) throws IOException {
        byte[] imgByte;

        if (fileList == null || fileList.size() == 0){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("file_list 不能为空");
        }
        if (fileList.size() > 5){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("file_list 最多上传5张交付物图片");
        }
        //先校验再上传
        for (FileParam jobFileVo : fileList) {
            if(StringUtils.isBlank(jobFileVo.getFileName())){
                throw ApiExceptions.API_LIMIT_PHOTO.newWithErrMsg("file_name 不能为空");
            }
            if(StringUtils.isBlank(jobFileVo.getFileBody())){
                throw ApiExceptions.API_LIMIT_PHOTO.newWithErrMsg("file_body 不能为空");
            }

            //校验文件类型
            if (!FILE_EXT.contains(FilenameUtils.getExtension(jobFileVo.getFileName()))){
                throw ApiExceptions.API_LIMIT_PHOTO.newWithErrMsg("只允许提交png，jpeg，jpg格式文件");
            }

            imgByte = ImageUtil.translateBase64(jobFileVo.getFileBody());
            if (imgByte.length > LIMIT_SIZE) {
                // 不能超过2M
                throw ApiExceptions.API_LIMIT_PHOTO.newWithErrMsg("交付附件不能大于2M");
            }
            jobFileVo.setFileBytes(imgByte);
        }

        List<String> fileBody = new ArrayList<>();
        for (FileParam jobFileVo : fileList) {
            fileBody.add(fastdfsClient.uploadFile(jobFileVo.getFileBytes(),jobFileVo.getFileName()));
        }
        return fileBody;
    }

    public void complateJob(String mchNo, CompleteJobReqVo completeJobReqVo) {
        validateMch(mchNo);
        Job job = jobFacade.getJobByJobId(completeJobReqVo.getJobNo());
        if (job == null || !job.getEmployerNo().equals(mchNo)){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("job_no 任务不存在");
        }

        if (!job.getJobStatus().equals(JobStatusEnum.PROCESSING.getCode())){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("任务尚未发布，无法完成");
        }
        jobFacade.completeJob(job.getId());
    }

    public JobDetailResVo getJobDetail(String mchNo, String jobNo) {
        Job job = jobFacade.getJobByJobId(jobNo);
        if (job == null || !job.getEmployerNo().equals(mchNo)){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("job_no 任务不存在");
        }

        JobDetailResVo resVo = new JobDetailResVo();
        resVo.setAcceptMode(String.valueOf(job.getAcceptMode()));
        resVo.setAddress(job.getJobAddress());
        resVo.setArea(job.getJobAreaName());
        resVo.setCity(job.getJobCityName());
        resVo.setAvalDateType(String.valueOf(job.getJobAvalDateType()));
        resVo.setCreateTime(job.getCreateTime());
        resVo.setDescription(job.getJobDescribe());
        resVo.setEduBackground(String.valueOf(job.getEduBackground()));
        resVo.setJobBeginDate(job.getJobStartDate());
        resVo.setJobEndDate(job.getJobFinishDate());
        resVo.setJobName(job.getJobName());
        resVo.setJobStatus(String.valueOf(job.getJobStatus()));
        resVo.setMaxAge(job.getWorkerAgeLimitMax());
        resVo.setMinAge(job.getWorkerAgeLimitMin());
        resVo.setPositionName(job.getWorkCategoryName());
        resVo.setPositionNo(job.getWorkCategoryCode());
        resVo.setProvince(job.getJobProvinceName());
        resVo.setScope(String.valueOf(job.getScope()));
        resVo.setSex(String.valueOf(job.getWorkerGender()));
        resVo.setStandard(job.getDeliveryStandard());
        resVo.setTag(StringUtils.isBlank(job.getJobTag()) ? new ArrayList<>() : JSONArray.parseArray(job.getJobTag(),String.class));
        resVo.setWorkNum(job.getWorkerNum());
        resVo.setWorkBeginTime(job.getJobTimeStart());
        resVo.setWorkEndTime(job.getJobTimeEnd());
        return resVo;
    }

    public CommonPageResVo<WorkerPageResVo> workerPage(String mchNo, JobWorkerPageReqVo data, String key) {
        Job job = jobFacade.getJobByJobId(data.getJobNo());
        if (job == null || !job.getEmployerNo().equals(mchNo)){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("job_no 任务不存在");
        }

        Page<JobWorkerRecord> page = new Page();
        page.setCurrent(data.getCurrentPage());
        page.setSize(10L);
        WorkerRecordQueryDto workerRecordQueryDto = new WorkerRecordQueryDto();
        workerRecordQueryDto.setJobId(data.getJobNo());
        Page<JobWorkerRecord> workerRecordPage = jobWorkerRecordFacade.workerRecordPage(page,workerRecordQueryDto);
        List<WorkerPageResVo> workerPageResVoList = workerRecordPage.getRecords().stream().map(x->{
            WorkerPageResVo resVo = new WorkerPageResVo();
            resVo.setAuth(String.valueOf(x.getAuthStatus()));
            resVo.setDeliveryStatus(String.valueOf(x.getDeliveryStatus()));
            resVo.setIdCardNo(AESUtil.encryptECB(x.getWorkerIdcard(),key));
            resVo.setName(AESUtil.encryptECB(x.getWorkerName(),key));
            resVo.setPhoneNo(AESUtil.encryptECB(x.getWorkerPhone(),key));
            resVo.setSettleStatus(String.valueOf(x.getSettleStatus()));
            resVo.setStatus(String.valueOf(x.getJobStatus()));
            resVo.setTextResult(StringUtils.isBlank(x.getDeliveryContent()) ? "" : x.getDeliveryContent());
            resVo.setFileUrlList(StringUtils.isBlank(x.getAttachment()) ? new ArrayList<>() : JSONArray.parseArray(x.getAttachment(),String.class));
            return resVo;
        }).collect(Collectors.toList());
        CommonPageResVo<WorkerPageResVo> commonPageResVo = new CommonPageResVo(new Long(workerRecordPage.getCurrent()).intValue(),workerRecordPage.getTotal(),new Long(workerRecordPage.getSize()).intValue());
        commonPageResVo.setList(workerPageResVoList);
        return commonPageResVo;
    }
}
