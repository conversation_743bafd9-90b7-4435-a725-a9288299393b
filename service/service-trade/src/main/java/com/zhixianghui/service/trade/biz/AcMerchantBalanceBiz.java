package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.trade.dto.AcMerchantBalanceAddDto;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;
import com.zhixianghui.facade.trade.enums.AdjustmentEnum;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.service.trade.dao.AcChangeFundsDao;
import com.zhixianghui.service.trade.dao.AcMerchantBalanceDao;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 账户资金变动biz业务实现类
 *
 * <AUTHOR> @date 2024-04-01
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AcMerchantBalanceBiz {

    private final AcMerchantBalanceDao acMerchantBalanceDao;

    private final AcChangeFundsDao acChangeFundsDao;

    private final RedisLock redisLock;

    private final PlatformTransactionManager platformTransactionManager;

    private final TransactionDefinition transactionDefinition;


    //新增账户资金变动
    public void insert(AcMerchantBalance AcMerchantBalance) {
        acMerchantBalanceDao.insert(AcMerchantBalance);
    }

    //修改账户资金变动
    public void update(AcMerchantBalance AcMerchantBalance) {
        acMerchantBalanceDao.update(AcMerchantBalance);
    }

    public Long getAmount(AcMerchantBalance acMerchantBalance) {
        log.info("查询{}余额参数------------------------>{}", acMerchantBalance.getPayChannelName(), JSONObject.toJSON(acMerchantBalance));
        AcMerchantBalance merchantBalance = acMerchantBalanceDao.getOne(BeanUtil.toMap(acMerchantBalance));
        if (ObjectUtils.isEmpty(merchantBalance)) {
            log.info("{}资金表暂未有来账，返回金额0", acMerchantBalance.getPayChannelName());
            return 0L;
        }
        long amount = merchantBalance.getTotalAmount() - merchantBalance.getFreezeAmount();
        log.info("{}余额查询成功，查询余额为:{}", acMerchantBalance.getPayChannelName(), amount);
        return amount;
    }

    //通过id查看账户资金变动
    public AcMerchantBalance getById(Long id) {
        return acMerchantBalanceDao.getById(id);
    }

    public AcMerchantBalance getOne(AcMerchantBalance acMerchantBalance) {
        return acMerchantBalanceDao.getOne(BeanUtil.toMap(acMerchantBalance));
    }

    public List<AcMerchantBalance> listBy(Map<String, Object> paramMap) {
        return acMerchantBalanceDao.listBy(paramMap);
    }

    //条件分页查询账户资金变动
//    public PageResult<List<AcMerchantBalance>> listPage(Map<String, Object> paramMap, PageQuery pageQuery) {
//		return acMerchantBalanceDao.listPage(paramMap, pageQuery);
//	}


    public AcMerchantBalance changeAmount(AcChangeFunds changeFunds) {
        log.info("[余额计算，操作[{}]的账户，订单号:{}]接收到的参数[{}]",
                MerchantTypeEnum.getEnum(changeFunds.getMerchantType()).getDesc(),
                changeFunds.getPlatTrxNo(),
                JsonUtil.toString(changeFunds));
        String redisLockKey = AcUtil.getRedisLockKey(changeFunds);
        RLock rLock = redisLock.tryLock(redisLockKey);
        log.info("[余额计算，操作[{}]的账户，订单号:{}]==>获取锁:{}",
                MerchantTypeEnum.getEnum(changeFunds.getMerchantType()).getDesc(),
                changeFunds.getPlatTrxNo(),
                redisLockKey);
        //手动启动事务
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        if (rLock == null) {
            log.info("[余额计算，操作[{}]的账户]==>获取锁失败，直接丢弃，logKey：[{}]，订单号：[{}],通道：[{}]，资金变动信息：[{}]",
                    MerchantTypeEnum.getEnum(changeFunds.getMerchantType()).getDesc(),
                    changeFunds.getLogKey(), changeFunds.getPlatTrxNo(), changeFunds.getPayChannelNo(),
                    JSON.toJSONString(changeFunds));
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        AcMerchantBalance balance = null;
        try {
            balance = actualChangeAmount(changeFunds);
            //提交事务
            platformTransactionManager.commit(transaction);
        } catch (DuplicateKeyException e) {
            log.info("该记录已入账，直接跳过，商户号：[{}],logKey：[{}],通道：[{}]，交易记录：[{}]", changeFunds.getMchNo(), changeFunds.getLogKey(), changeFunds.getPayChannelNo(), JSON.toJSONString(changeFunds));
            platformTransactionManager.rollback(transaction);
            throw e;
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
        return balance;
    }


    private AcMerchantBalance actualChangeAmount(AcChangeFunds changeFunds) {
        Map<String, Object> map = new HashMap<>();
        map.put("mchNo", changeFunds.getMchNo());
        map.put("mainstayNo", changeFunds.getMainstayNo());
        map.put("merchantType", changeFunds.getMerchantType());
        map.put("payChannelNo", changeFunds.getPayChannelNo());
        AcMerchantBalance acMerchantBalance = acMerchantBalanceDao.getOne(map);
        if (ObjectUtils.isEmpty(acMerchantBalance)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该商户没有来账记录");
        }
        if(ObjectUtils.isEmpty(acMerchantBalance.getSettleAmount())){
            acMerchantBalance.setSettleAmount(0L);
        }
        if(ObjectUtils.isEmpty(changeFunds.getSettleAmount())){
            changeFunds.setSettleAmount(0L);
        }
        //计算变动前变动金额
        changeFunds.setVersion(0).setBeforeAmount(acMerchantBalance.getTotalAmount())
                .setBeforeFrozenAmount(acMerchantBalance.getFreezeAmount())
                .setAfterAmount(acMerchantBalance.getTotalAmount() + changeFunds.getAmount())
                .setAfterFrozenAmount(acMerchantBalance.getFreezeAmount() + changeFunds.getFrozenAmount())
                .setBeforeSettleAmount(acMerchantBalance.getSettleAmount())
                .setAfterSettleAmount(acMerchantBalance.getSettleAmount() + changeFunds.getSettleAmount())
                .setCreateTime(new Date());
        //插入资金变动表
        acChangeFundsDao.insert(changeFunds);
        log.info("[订单号:{}，变动类型：{}],资金开始扣减，查询商户号[{}]，供应商编号：[{}],通道编号:[{}]，总金额：{}，冻结金额：{},结算金额:{}",
                changeFunds.getPlatTrxNo(), changeFunds.getAmountChangeType(), changeFunds.getMchNo(),
                changeFunds.getMainstayNo(), changeFunds.getPayChannelNo(),
                acMerchantBalance.getTotalAmount(), acMerchantBalance.getFreezeAmount(),acMerchantBalance.getSettleAmount());
        acMerchantBalance.setVersion(acMerchantBalance.getVersion() + 1);
        acMerchantBalance.setTotalAmount(changeFunds.getAfterAmount());
        acMerchantBalance.setFreezeAmount(changeFunds.getAfterFrozenAmount());
        acMerchantBalance.setSettleAmount(changeFunds.getAfterSettleAmount());
        acMerchantBalance.setUpdateTime(new Date());
        acMerchantBalanceDao.update(acMerchantBalance);
        log.info("[订单号:{}，变动类型：{}],资金扣减完成，查询商户号[{}]，供应商编号：[{}],通道编号:[{}]，总金额：{}，冻结金额：{},结算金额:{}"
                , changeFunds.getPlatTrxNo(), changeFunds.getAmountChangeType(), changeFunds.getMchNo(), changeFunds.getMainstayNo(), changeFunds.getPayChannelNo()
                , acMerchantBalance.getTotalAmount(), acMerchantBalance.getFreezeAmount(),acMerchantBalance.getSettleAmount());
        return acMerchantBalance;
    }


    /***
     * 创建默认的本地账户
     * @param dto
     * @return
     */
    public boolean createMerchantBalance(AcMerchantBalanceAddDto dto) {
        log.info("进入创建本地账户的流程，接收到的参数为：{}", JsonUtil.toString(dto));
        String key = AcUtil.getRedisLockKey(dto.getMchNo(), dto.getMainstayNo(), MerchantTypeEnum.EMPLOYER.getValue(), dto.getPayChannelNo());
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        RLock rLock = redisLock.tryLock(key);
        try {
            if (rLock == null) {
                throw new RuntimeException("发放超时，请稍后再试");
            }
            Map<String, Object> map = BeanMap.create(dto);
            // 查询是否存在
            long count = acMerchantBalanceDao.countBy(map);
            if (count <= 0) {
                AcMerchantBalance model = new AcMerchantBalance();
                model.setMchNo(dto.getMchNo());
                model.setMchName(dto.getMchName());
                model.setMainstayNo(dto.getMainstayNo());
                model.setMainstayName(dto.getMainstayName());
                model.setPayChannelNo(dto.getPayChannelNo());
                model.setPayChannelName(dto.getPayChannelName());
                model.setMerchantType(dto.getMerchantType());
                model.setVersion(0);
                model.setCreateTime(new Date());
                model.setTotalAmount(0L);
                model.setFreezeAmount(0L);
                acMerchantBalanceDao.insert(model);
                platformTransactionManager.commit(transaction);
                return true;
            }
            return false;
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            throw e;
        } finally {
            redisLock.unlock(rLock);
        }
    }

    public void adjustment(AdjustmentDTO adjustmentDTO) {
        log.info("进入君享汇调账，供应商{}，用工企业：{}，调账金额：{},调账类型:{}"
                ,adjustmentDTO.getMainstayNo(),adjustmentDTO.getMchNo()
                ,adjustmentDTO.getAmount(),adjustmentDTO.getType());
        AcChangeFunds changeFunds=new AcChangeFunds();
        changeFunds.setLogKey(adjustmentDTO.getLogKey());
        changeFunds.setPlatTrxNo(adjustmentDTO.getLogKey());
        Integer merchantType= StringUtil.isEmpty(adjustmentDTO.getMchNo())?MerchantTypeEnum.MAINSTAY.getValue():
                MerchantTypeEnum.EMPLOYER.getValue();
        changeFunds.setMerchantType(merchantType);
        changeFunds.setMchNo(adjustmentDTO.getMchNo());
        changeFunds.setMchName(adjustmentDTO.getMchName());
        changeFunds.setMainstayNo(adjustmentDTO.getMainstayNo());
        changeFunds.setMainstayName(adjustmentDTO.getMainstayName());
        changeFunds.setAmountChangeType(WxAmountChangeLogTypeEnum.ADJUSTMENT.getValue());
        Long ammount=null;
        if(adjustmentDTO.getType()== AdjustmentEnum.ADD.getValue()){
            ammount= AmountUtil.changeToFen(adjustmentDTO.getAmount());
        }else if(adjustmentDTO.getType()== AdjustmentEnum.DEL.getValue()) {
            ammount=-AmountUtil.changeToFen(adjustmentDTO.getAmount());
        }else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("调账参数异常");
        }
        changeFunds.setAmount(ammount);
        changeFunds.setFrozenAmount(0L);
        changeFunds.setCreateTime(new Date());
        changeFunds.setOperator(adjustmentDTO.getOperator());
        changeFunds.setPayChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
        changeFunds.setPayChannelName(ChannelNoEnum.JOINPAY_JXH.getDesc());
        this.changeAmount(changeFunds);
    }


}
