package com.zhixianghui.service.trade.biz;

import com.google.common.collect.Maps;
import com.zhixianghui.facade.trade.entity.MchPlatTrxNo;
import com.zhixianghui.service.trade.dao.MchPlatTrxNoDao;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
* 商户平台订单映射Biz
*
* <AUTHOR>
* @version 创建时间： 2020-11-06
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MchPlatTrxNoBiz {

    private final MchPlatTrxNoDao mchPlatTrxNoDao;

    /**
     * 根据商户订单号获取平台订单号
     * @param mchOrderNo 商户订单号
     * @param employerNo 商户编号(null 忽略)
     * @return 平台批次号
     */
    public List<MchPlatTrxNo> listPlatTrxNoByMchOrderNo(String mchOrderNo, String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mchOrderNo",mchOrderNo);
        if(StringUtils.isNotBlank(employerNo)){
            paramMap.put("employerNo",employerNo);
        }
        return mchPlatTrxNoDao.listBy(paramMap);
    }

    /**
     * 根据流水号获取
     * @param platTrxNo 流水号
     * @return 平台批次号
     */
    public MchPlatTrxNo getByPlatTrxNo(String platTrxNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platTrxNo",platTrxNo);
        return mchPlatTrxNoDao.getOne(paramMap);
    }

    /**
     * 根据渠道流水号获取平台流水号
     * @param channelTrxNo 渠道流水号
     * @return 平台批次号
     */
    public MchPlatTrxNo getByChannelTrxNo(String channelTrxNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("channelTrxNo",channelTrxNo);
        return mchPlatTrxNoDao.getOne(paramMap);
    }

    public void insert(MchPlatTrxNo mchPlatTrxNo) {
        mchPlatTrxNoDao.insert(mchPlatTrxNo);
    }

    public void update(MchPlatTrxNo mchPlatTrxNo) {
        mchPlatTrxNoDao.update(mchPlatTrxNo);
    }

    /**
     * 根据商户订单号和用工企业编号删除记录
     * @param mchOrderNo 商户订单号
     * @param employerNo 用工企业编号
     */
    public void deleteByMchOrderNoAndEmployerNo(String mchOrderNo, String employerNo) {
        if (StringUtils.isBlank(mchOrderNo) || StringUtils.isBlank(employerNo)) {
            throw new IllegalArgumentException("删除条件不能为空，mchOrderNo: " + mchOrderNo + ", employerNo: " + employerNo);
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("mchOrderNo", mchOrderNo);
        paramMap.put("employerNo", employerNo);
        mchPlatTrxNoDao.deleteBy(paramMap);
    }
}