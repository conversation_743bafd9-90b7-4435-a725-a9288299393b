package com.zhixianghui.service.trade.utils;

import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-02 19:44
 **/
@Slf4j
@Component
public class RedisRateLimiter {

    @Autowired
    private RedisClient redisClient;
    /**
     * 请大家把自己定义的“令牌桶”统一放到这里。
     *
     * 解释：为了防止多个业务功能命名的“令牌桶名称”相同，而导致共用一个令牌桶。
     * 或同一个令牌桶用了不同的名称，而导致不能实现真正的限流作用。
     */
    public enum TokenBucketEnum{
        /** 受理错误桶 允许重试错误次数 500/一小时 */
        ACCEPT_ERROR("ACCEPT:ERROR", 500L, 60*60, 1000L),
        /** 发放错误桶 允许重试错误次数 500/一小时 */
        GRANT_ERROR("GRANT:ERROR", 500L, 60*60, 1000L),
        ;

        /** 令牌桶名称 */
        private final String name;
        /** 令牌数 */
        private final Long permits;
        /** 周期（秒）*/
        private final Integer cycle;
        /** 自旋阻塞时间(毫秒)，建议为周期的1/100 */
        private final Long timeOut;

        TokenBucketEnum (String name, Long permits, Integer cycle, Long timeOut){
            this.name = name;
            this.permits = permits;
            this.cycle = cycle;
            this.timeOut = timeOut;
        }
    }

    /**
     * 阻塞式获取令牌
     * @param tokenBucket 令牌桶
     */
    public boolean tryAcquire(TokenBucketEnum tokenBucket) {
        //每次自增操作后的值
        Long incrementalResult;
        if((incrementalResult = redisClient.incr(tokenBucket.name)) > tokenBucket.permits){
            //阻塞一定时间
            try {
                Thread.sleep(tokenBucket.timeOut);
            } catch (InterruptedException e) {
                log.error("获取令牌阻塞失败",e);
                return false;
            }
            if ((incrementalResult = redisClient.incr(tokenBucket.name)) > tokenBucket.permits){
                log.info("[令牌桶 :{}]==>令牌达到上限，无法获取",tokenBucket.name);
                return false;
            }
        }
        if(incrementalResult == 1){
            //设置“tokenBucketCycle”后失效
            redisClient.expire(tokenBucket.name, tokenBucket.cycle);
        }
        return true;
    }
}
