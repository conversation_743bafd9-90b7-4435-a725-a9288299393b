package com.zhixianghui.service.trade.pay.jxh;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.vo.jxh.JxhParam;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.service.trade.biz.AcMerchantBalanceBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.factory.TradeFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;

@Slf4j
public abstract class JXHHandler {



    @Reference
    private NotifyFacade notifyFacade;

    public abstract void handle(JxhParam jxhParam);



    public void updateChangeFounds(AcChangeFunds changesFunds){
        notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_PAY_QUERY, UUIDUitl.generateString(10), changesFunds.getPlatTrxNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_JXH_PAY_CHANGE_FOUNDS, JsonUtil.toString(changesFunds), MsgDelayLevelEnum.S_1.getValue());
    }


}
