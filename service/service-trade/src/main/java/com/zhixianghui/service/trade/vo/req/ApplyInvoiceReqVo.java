package com.zhixianghui.service.trade.vo.req;

import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 申请开票vo
 * <AUTHOR>
 * @date 2020/12/28
 **/
@Data
public class ApplyInvoiceReqVo{

    @NotEmpty(message = "代征主体编号 不能为空")
    private String mainstayNo;

    @NotNull(message = "发票类型 不能为空")
    @EnumValue(intValues = {1,2}, message = "发票类型 有误")
    private Integer invoiceType;

    @NotEmpty(message = "开票交易时间段终止时间 不能为空")
    @Length(min = 10, max = 10, message = "开票交易时间段终止时间 格式有误")
    private String tradeCompleteDayEnd;

    @Length(max = 200, message = "开票说明长度不能超过200")
    private String remark;

    private String productNo;
}
