package com.zhixianghui.service.trade.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.facade.trade.dto.CmbChangesFoundsQueryDTO;
import com.zhixianghui.facade.trade.service.CmbChangesFundsFacade;
import com.zhixianghui.facade.trade.vo.CmbChangesFundsVO;
import com.zhixianghui.service.trade.biz.CmbChangesFundsBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:14
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CmbChangesFundsFacadeImpl implements CmbChangesFundsFacade {


    private final CmbChangesFundsBiz cmbChangesFundsBiz;


    @Override
    public IPage<CmbChangesFundsVO> listPage(CmbChangesFoundsQueryDTO param) {
        return cmbChangesFundsBiz.listPage(param);
    }

    @Override
    public void exportCmbChangesFunds(CmbChangesFoundsQueryDTO param, String loginName) {
        cmbChangesFundsBiz.exportCmbChangesFunds(param, loginName);
    }
}
