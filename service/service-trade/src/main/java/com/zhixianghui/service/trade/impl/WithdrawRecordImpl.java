package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.dto.withdraw.WithdrawRecordQueryDto;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.service.WithdrawRecordFacade;
import com.zhixianghui.service.trade.biz.WithdrawRecordBiz;
import com.zhixianghui.service.trade.dao.WithdrawRecordDao;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WithdrawRecordImpl implements WithdrawRecordFacade {

    private final WithdrawRecordDao withdrawRecordDao;

    private final WithdrawRecordBiz withdrawRecordBiz;

    @Override
    public PageResult<List<WithdrawRecord>> listWithRecordPage(WithdrawRecordQueryDto withdrawRecordQueryDto, PageParam pageParam) {
        PageResult<List<WithdrawRecord>> record = withdrawRecordDao.listPage(BeanUtil.toMap(withdrawRecordQueryDto), pageParam);
        record.getData().forEach(x->{
            if (StringUtils.isNotBlank(x.getChannelNo())){
                x.setChannelName(ChannelNoEnum.getEnum(x.getChannelNo()).getDesc());
            }
        });
        return record;
    }

    @Override
    public PageResult<List<WithdrawRecord>> listWithRecordPage(Map<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<WithdrawRecord>> record = withdrawRecordDao.listPage(paramMap, pageParam);
        record.getData().forEach(x->{
            if (StringUtils.isNotBlank(x.getChannelNo())){
                x.setChannelName(ChannelNoEnum.getEnum(x.getChannelNo()).getDesc());
            }
        });
        return record;
    }

    @Override
    public Map<String, Object> sumWithdrawRecord(Map<String, Object> paramMap) {
        return withdrawRecordBiz.sumWithdrawRecord(paramMap);
    }

    @Override
    public WithdrawRecord getByWithdrawRecordNo(String withdrawNo) {
        return withdrawRecordBiz.getByWithdrawRecordNo(withdrawNo);
    }
}
