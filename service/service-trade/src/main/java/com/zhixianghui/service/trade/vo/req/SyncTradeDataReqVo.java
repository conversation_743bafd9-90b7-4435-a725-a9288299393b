package com.zhixianghui.service.trade.vo.req;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class SyncTradeDataReqVo implements Serializable {

    private static final long serialVersionUID = 8939141946139764225L;
    @NotEmpty(message = "明细items列表不能为空")
    private List<Map<String,Object>> items;

    @NotBlank(message = "批次号batchNo不能为空")
    private String batchNo;

    @NotBlank(message = "代征主体编号mainstayNo不能为空")
    private String mainstayNo;

    @NotNull(message = "支付类型channelType不能为空")
    private Integer channelType;

    private Integer hasNext = 0;

}
