package com.zhixianghui.service.trade.process;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.api.base.enums.CmbBizCodeEnum;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.common.OrderCompleteVo;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.service.KeyPairRecordFacade;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.pay.QueryPayOrderReqVo;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.BankCardBinFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.trade.dto.BatchPaymentBalanceDTO;
import com.zhixianghui.facade.trade.dto.PaymentBalanceDTO;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.biz.AcLocalPayBiz;
import com.zhixianghui.service.trade.biz.CmbMerchantBalanceBiz;
import com.zhixianghui.service.trade.biz.OrderReexchangeBiz;
import com.zhixianghui.service.trade.biz.WxLocalPayBiz;
import com.zhixianghui.service.trade.utils.BuildVoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ZXHGrantBiz
 * @Description TODO
 * @Date 2022/7/4 11:53
 */
@Slf4j
@Service
public class ZXHGrantBiz extends AbstractGrantHandler {
    @Autowired
    private WxLocalPayBiz wxLocalPayBiz;
    @Autowired
    private OrderReexchangeBiz orderReexchangeBiz;
    @Autowired
    private AcLocalPayBiz acLocalPayBiz;
    @Autowired
    private CmbMerchantBalanceBiz cmbMerchantBalanceBiz;

    @Reference
    private KeyPairRecordFacade keyPairRecordFacade;
    @Reference
    private BankCardBinFacade bankCardBinFacade;
    @Reference
    private CmbFacade cmbFacade;


    @Override
    public void notifyGrantStart(String employerNo, String mainstayNo, String platBatchNo, List<String> notifyList) {
        Map<String, String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(notifyList));

        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10), platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT, JSON.toJSONString(infoMap),
                MsgDelayLevelEnum.S_1.getValue());
//        String yishuiNo = dataDictionaryFacade.getSystemConfig("yishui");
//        if (StringUtils.equals(yishuiNo,mainstayNo)) {
//            /**
//             * 易税供应商由于供应商处在下单之后不能立即根据外部单号查询到订单明细，所以适当拉大延迟时间
//             */
//            notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
//                    NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT,JSON.toJSONString(infoMap),
//                    MsgDelayLevelEnum.S_10.getValue());
//        }else {
//            notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10),platBatchNo,
//                    NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT,JSON.toJSONString(infoMap),
//                    MsgDelayLevelEnum.S_1.getValue());
//        }
    }

    @Override
    public void notifyGrantStartRisckRetry(String employerNo, String mainstayNo, String platBatchNo, List<String> notifyList) {
        Map<String, String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(notifyList));
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10), platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_GRANT, JSON.toJSONString(infoMap),
                MsgDelayLevelEnum.M_1.getValue());
    }

    @Override
    public void notifyHandleGrantException(String platTrxNo) {
        //非业务异常延迟队列(避开类似服务暂时异常)
        log.info("[{}]==>发送发放异常处理mq", platTrxNo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_GRANT_NOT_BIZ_EXCEPTION, platTrxNo, MsgDelayLevelEnum.M_1.getValue());
    }

    @Override
    public void notifyHandleGrantExceptionAgain(String platTrxNo) {
        //非业务异常延迟队列(避开类似服务暂时异常)
        log.info("[{}]==>发送发放异常处理mq", platTrxNo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10), platTrxNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_GRANT_NOT_BIZ_EXCEPTION, platTrxNo, MsgDelayLevelEnum.M_20.getValue());
    }

    @Override
    public void notifyGrantBatchCount(String platBatchNo) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC, UUIDUitl.generateString(10), platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_GRANT_BATCH_COUNT, platBatchNo);
    }

    @Override
    public PayRespVo pay(PayReqVo payReqVo) {
        PayRespVo payRespVo;
        Order order = orderBiz.getByPlatBatchNo(payReqVo.getPayBatchNo());
        if (StringUtils.equals(order.getPayChannelNo(), ChannelNoEnum.CMB.name())
                && order.getLaunchWay() == LaunchWayEnum.EMPLOYER.getValue()) {
            payRespVo = new PayRespVo();
            payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());

            // 检查是否都处理完成
            Map<String, Object> param = new HashMap<>();
            param.put("platBatchNo", order.getPlatBatchNo());
            param.put("orderItemStatusList", ListUtil.of(OrderItemStatusEnum.ACCEPTED.getValue(), OrderItemStatusEnum.GRANT_HANG.getValue()));
            Long countOrderItem = orderItemBiz.countOrderItem(param);
            if (countOrderItem != null && countOrderItem.intValue() == 0) {
                EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(order.getEmployerNo(), order.getMainstayNo(), order.getPayChannelNo());
                this.cmbBatchPay(order, employerAccountInfo);
                payRespVo.setBizCode(CmbBizCodeEnum.CMB_BATCH_FINISH.getCode());
            }
            return payRespVo;
        } else {
            if (StringUtils.equals(payReqVo.getChannelNo(), ChannelNoEnum.WXPAY.name())) {
                payRespVo = wxLocalPayBiz.pay(payReqVo);
            } else if (StringUtils.equals(payReqVo.getChannelNo(), ChannelNoEnum.JOINPAY_JXH.name())) {
                payRespVo = acLocalPayBiz.pay(payReqVo);
            } else {
                // 如果是招行API发起的支付，先预扣本地本地招行
                if (StringUtils.equals(order.getPayChannelNo(), ChannelNoEnum.CMB.name())) {
                    payRespVo = cmbPay(order, payReqVo);
                } else {
                    payRespVo = payBankFacade.pay(payReqVo);
                }
            }
            return payRespVo;
        }
    }

    public void cmbBatchPay(Order order, EmployerAccountInfo employerAccountInfo) {
        String platBatchNo = order.getPlatBatchNo();

        JSONObject queryResult = cmbFacade.queryPayBatchOrder(platBatchNo, employerAccountInfo.getParentMerchantNo());
        if (queryResult != null && queryResult.getJSONArray("bb6bthqyz1") != null
                && queryResult.getJSONArray("bb6bthqyz1").size() > 0) {
            JSONObject queryData = queryResult.getJSONArray("bb6bthqyz1").getJSONObject(0);
            if (!StringUtils.equals(queryData.getString("reqsta"), "OPR")) {
                log.info("招行发放通道批次查询-招行已存在相同批次，停止发放，批次号：{}", platBatchNo);
                return;
            }
        }
        Map<String, Object> recordParam = new HashMap<>();
        recordParam.put("platBatchNo", platBatchNo);
        recordParam.put("employerNo", order.getEmployerNo());
        recordParam.put("processStatus", RecordItemStatusEnum.PAY_CREATED.getValue());
        String reqnbr = null;
        final int pageSize = 1000;
        int currentPage = 1;
        Long countRecordItem = recordItemBiz.countRecordItem(recordParam);
        long lastPage = countRecordItem % pageSize == 0 ? countRecordItem / pageSize : countRecordItem / pageSize + 1;

        Map<String, BigDecimal> grantSumAmount = recordItemBiz.getWaiteGrantSumAmount(platBatchNo, RecordItemStatusEnum.PAY_CREATED);
        if (grantSumAmount == null || grantSumAmount.get("sumNetAmount") == null) {
            return;
        }
        List<RecordItem> list = recordItemBiz.list(recordParam);
        if (ObjectUtil.isEmpty(list)) {
            log.info("招行发放，查找不到招行打款流水，停止发放，批次号：{}", platBatchNo);
            return;
        }
        //先预扣本地本地招行账户, 批量扣完才继续走后面的流程
        try {
            BatchPaymentBalanceDTO batchPaymentBalance = new BatchPaymentBalanceDTO();
            batchPaymentBalance.setMchNo(order.getEmployerNo());
            batchPaymentBalance.setMchName(order.getEmployerName());
            batchPaymentBalance.setMainstayNo(order.getMainstayNo());
            batchPaymentBalance.setMainstayName(order.getMainstayName());
            batchPaymentBalance.setPlatBatchNo(platBatchNo);
            batchPaymentBalance.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            List<BatchPaymentBalanceDTO.BatchOrderInfo> collect = list.stream().map(e -> {
                BatchPaymentBalanceDTO.BatchOrderInfo batchOrderInfo = new BatchPaymentBalanceDTO.BatchOrderInfo();
                batchOrderInfo.setPlatTrxNo(e.getPlatTrxNo());
                batchOrderInfo.setAmount(e.getOrderAmount());
                return batchOrderInfo;
            }).collect(Collectors.toList());
            batchPaymentBalance.setOrderInfoList(collect);
            cmbMerchantBalanceBiz.batchPaymentBalance(batchPaymentBalance);
        } catch (Exception e) {
            log.error("招行发放异常，批量扣减招行账户异常。批次号：{}", platBatchNo, e);
            return;
        }
        String orderNetAmount = grantSumAmount.get("sumNetAmount").toPlainString();
        final KeyPairRecord keyPairRecord = keyPairRecordFacade.getByChannelNoAndChannelMchNo("CMB", employerAccountInfo.getParentMerchantNo());
        Dict body = Dict.create();
        body.set("bb6busmod",
                ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("buscod", "N03020") //业务类型
                                        .set("busmod", keyPairRecord.getChannelPlatNo().split(":")[1].split("-")[1]) //业务模式
                        )
                )
        );
        PageResult<List<RecordItem>> pageResult;
        do {
            pageResult = recordItemBiz.listPage(recordParam, PageParam.newInstance(currentPage, pageSize));
            List<RecordItem> recordItems = pageResult.getData();

            List<TreeMap<String, Object>> detailList = new LinkedList();
            if (recordItems != null && !recordItems.isEmpty()) {
                BigDecimal curRequestAmount = BigDecimal.ZERO;

                for (RecordItem item : recordItems) {
                    curRequestAmount = curRequestAmount.add(item.getOrderNetAmount());
                    BankCardBin cardBinByCardNo = bankCardBinFacade.getCardBinByCardNo(item.getReceiveAccountNoDecrypt());
                    TreeMap<String, Object> sort = MapUtil.sort(
                            Dict.create() //收款方信息
                                    .set("trxseq", item.getRemitPlatTrxNo().substring(item.getRemitPlatTrxNo().length() - 8)) //交易序号
                                    .set("accnbr", item.getReceiveAccountNoDecrypt()) //账号
                                    .set("accnam", item.getReceiveNameDecrypt()) //户名
                                    .set("trsamt", item.getOrderNetAmount().toPlainString()) //交易金额
                                    .set("trsdsp", item.getRemark()) //注释
                                    .set("cprref", item.getRemitPlatTrxNo() + "P")
                                    .set("eacbnk", cardBinByCardNo.getBankName())
                    );
                    detailList.add(sort);
                }

                this.buildPayerInfo(platBatchNo, employerAccountInfo, body, currentPage, lastPage, orderNetAmount, countRecordItem + "",
                        recordItems.size() + "", curRequestAmount.toPlainString(), reqnbr);
                body.set("bb6cdcdlx1", detailList);
                try {
                    JSONObject payApplyBatch = cmbFacade.payApplyBatch(employerAccountInfo.getParentMerchantNo(), body);
                    reqnbr = payApplyBatch.getJSONArray("bb6cdcbhz1").getJSONObject(0).getString("reqnbr");
                } catch (BizException e) {
                    if (e.getErrMsg().contains("交易序号重复")) {
                        currentPage++;
                        continue;
                    } else if (e.getErrMsg().contains("业务参考号重复")) {
                        log.info("[{}]业务参考号重复，终止提交，等待前一笔已提交订单回调", platBatchNo);
                        return;
                    } else {
                        throw e;
                    }
                } catch (Exception e) {
                    log.info("[{}]招行后台发放异常，重新进入队列", platBatchNo);
                    return;
                }
            }
            currentPage++;
        } while (pageResult != null && pageResult.getData() != null && pageResult.getData().size() > 0);

        JSONObject checkParam = new JSONObject();
        checkParam.put("platBatchNo", platBatchNo);
        checkParam.put("reqnbr", reqnbr);
        checkParam.put("accountNo", employerAccountInfo.getParentMerchantNo());
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CMB_ASYNC, order.getEmployerNo(), order.getPlatBatchNo(),
                NotifyTypeEnum.TRADE_CMB_BATCH_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_CMB_CHECK_GRANT, checkParam.toJSONString(), MsgDelayLevelEnum.M_1.getValue());
    }

    public PayRespVo cmbPay(Order order, PayReqVo payReqVo) {
        PayRespVo payRespVo;
        Map<String, Object> recordParam = new HashMap<>();
        recordParam.put("platBatchNo", order.getPlatBatchNo());
        recordParam.put("employerNo", order.getEmployerNo());
        recordParam.put("processStatus", RecordItemStatusEnum.PAY_CREATED.getValue());
        List<RecordItem> recordItems = recordItemBiz.list(recordParam);
        if (ObjectUtil.isEmpty(recordItems)) {
            log.info("招行单笔发放，查找不到招行打款流水，停止发放，批次号：{}", order.getPlatBatchNo());
            payRespVo = new PayRespVo();
            payRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            return payRespVo;
        }
        RecordItem recordItem = recordItems.get(0);
        try {
            PaymentBalanceDTO paymentBalanceDTO = new PaymentBalanceDTO();
            paymentBalanceDTO.setMchNo(order.getEmployerNo());
            paymentBalanceDTO.setMchName(order.getEmployerName());
            paymentBalanceDTO.setMainstayNo(order.getMainstayNo());
            paymentBalanceDTO.setMainstayName(order.getMainstayName());
            paymentBalanceDTO.setPlatBatchNo(order.getPlatBatchNo());
            paymentBalanceDTO.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            paymentBalanceDTO.setPlatTrxNo(recordItem.getPlatTrxNo());
            paymentBalanceDTO.setAmount(recordItem.getOrderAmount());
            cmbMerchantBalanceBiz.paymentBalance(paymentBalanceDTO);
        } catch (Exception e) {
            log.error("招行单笔发放，扣减招行账户异常，批次号：{}", order.getPlatBatchNo(), e);
            payRespVo = new PayRespVo();
            payRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            return payRespVo;
        }
        //招行支付
        try {
            payRespVo = payBankFacade.pay(payReqVo);
            if (BankPayStatusEnum.FAIL.getValue() == payRespVo.getBankPayStatus()) {
                cmbRefund(recordItem);
            }
            return payRespVo;
        } catch (Exception e) {
            log.error("招行单笔发放，批次号：{}，异常：", order.getPlatBatchNo(), e);
            cmbRefund(recordItem);
            payRespVo = new PayRespVo();
            payRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            return payRespVo;
        }
    }

    public void cmbRefund(RecordItem recordItem) {
        notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_REFUND, recordItem.getEmployerNo(), recordItem.getPlatTrxNo(),
                NotifyTypeEnum.CMB_REFUND_MSG.getValue(), MessageMsgDest.TAG_CMB_ACCOUNT_REFUND, JSON.toJSONString(recordItem),
                MsgDelayLevelEnum.S_30.getValue());
    }

    public void buildPayerInfo(String platBatchNo, EmployerAccountInfo employerAccountInfo, Dict body,
                               long currentPage, long lastPage, String totalAmount, String totalCount,
                               String curRequestSize, String curRequestAmount, String reqnbr) {
        //调用招行接口提交
        String begtag = "N";
        String endtag = "N";
        if (currentPage == 1) {
            begtag = "Y";
        }
        if (currentPage == lastPage) {
            endtag = "Y";
        }

        body.set("bb6cdcbhx1",
                ListUtil.of(
                        MapUtil.sort(
                                Dict.create()  //付款方信息
                                        .set("begtag", begtag)//批次开始标志
                                        .set("endtag", endtag)//批次结束标志
                                        .set("reqnbr", reqnbr)//批次结束标志
                                        .set("accnbr", employerAccountInfo.getParentMerchantNo())//账号
                                        .set("accnam", employerAccountInfo.getMainstayName())//户名
                                        .set("ttlamt", totalAmount)//总金额
                                        .set("ttlcnt", totalCount)//总笔数
                                        .set("ttlnum", lastPage)//总次数
                                        .set("curamt", curRequestAmount)//本次金额
                                        .set("curcnt", curRequestSize)//本次笔数
                                        .set("ccynbr", "10")//币种
                                        .set("trstyp", "BYBK")//交易类型
                                        .set("nusage", "下发")//用途
                                        .set("eptdat", cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN))//期望日期
                                        .set("yurref", "ZHIXIANG" + platBatchNo)//业务参考号
                                        //.set("dmanbr", employerAccountInfo.getSubMerchantNo())//记账子单元
                                        .set("chlflg", "Y")//结算通道
                        )
                )
        );
    }

    @Override
    public void notifyFailRefund(RecordItem recordItem) {
        //发放失败，退款处理
        if (recordItem.getPayChannelNo().equals(ChannelNoEnum.ALIPAY.name())) {

            EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.
                    getByEmployerNoAndMainstayNoAndChannelType(recordItem.getEmployerNo(), recordItem.getMainstayNo(), recordItem.getChannelType());
            OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
            PayReqVo payReqVo = BuildVoUtil.fillPayReqVo(recordItem, employerAccountInfo, orderItem);
            notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND, payReqVo.getSubMerchantNo(), payReqVo.getBankOrderNo(), NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(),
                    MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(payReqVo), MsgDelayLevelEnum.S_30.getValue());
            log.info("支付宝回调返回发放到个人失败，执行退款处理，订单号：[{}]", payReqVo.getBankOrderNo());

        } else if (recordItem.getPayChannelNo().equals(ChannelNoEnum.CMB.name())) {
            log.info("招行打款失败，执行退款处理，批次号：{}，流水号：{}", recordItem.getPlatBatchNo(), recordItem.getPlatTrxNo());
            cmbRefund(recordItem);
            /*Order order = orderBiz.getByPlatBatchNo(recordItem.getPlatBatchNo());
            if (order.getLaunchWay().intValue() == LaunchWayEnum.API.getValue()) {

                EmployerAccountInfo accountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(order.getEmployerNo(), order.getMainstayNo(), ChannelNoEnum.CMB.name());

                PayReqVo payReqVo = new PayReqVo();
                payReqVo.setPayBatchNo(recordItem.getPlatBatchNo());
                payReqVo.setBankOrderNo(recordItem.getRemitPlatTrxNo());
                payReqVo.setChannelMchNo(accountInfo.getParentMerchantNo());
                payReqVo.setSubMerchantNo(recordItem.getChannelMchNo());
                payReqVo.setServiceFee(recordItem.getOrderFee().toPlainString());
                payReqVo.setChannelNo(ChannelNoEnum.CMB.name());
                payReqVo.setEmployerNo(recordItem.getEmployerNo());
                payReqVo.setMainstayNo(recordItem.getMainstayNo());
                payReqVo.setMchName(recordItem.getEmployerName());
                payReqVo.setParentMerchantNo(accountInfo.getParentMerchantNo());
                payReqVo.setPayerChannelAccountNo(recordItem.getChannelMchNo());
                payReqVo.setPlatTrxNo(recordItem.getPlatTrxNo());
                payReqVo.setRealPayerName(recordItem.getMainstayName());
                notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_REFUND, payReqVo.getSubMerchantNo(), payReqVo.getBankOrderNo(), NotifyTypeEnum.CMB_REFUND_MSG.getValue(), MessageMsgDest.TAG_CMB_REFUND, JSON.toJSONString(payReqVo), MsgDelayLevelEnum.S_10.getValue());

            }*/
        }
    }

    @Override
    public OrderCompleteVo buildNotifyFeeVo(OrderItem orderItem, RecordItem recordItem) {
        return buildNotifyFeeVo(orderItem, recordItem, orderItem.getOrderItemNetAmount());
    }

    @Override
    public PayRespVo queryChannelOrder(RecordItem recordItem, EmployerAccountInfo employerAccountInfo) {
        QueryPayOrderReqVo queryPayOrderReqVo = new QueryPayOrderReqVo();
        queryPayOrderReqVo.setBankOrderNo(recordItem.getRemitPlatTrxNo());
        queryPayOrderReqVo.setChannelNo(recordItem.getPayChannelNo());
        queryPayOrderReqVo.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
        queryPayOrderReqVo.setChannelType(employerAccountInfo.getChannelType());
        queryPayOrderReqVo.setPayBatchNo(recordItem.getPlatBatchNo());
        PayRespVo queryRespVo;
        try {
            queryRespVo = payBankFacade.queryPayOrder(queryPayOrderReqVo);
        } catch (Exception e) {
            log.error("[人工反查通道: {}]==>通道反查接口异常", recordItem.getRemitPlatTrxNo(), e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道反查接口异常,请稍后重试");
        }
        return queryRespVo;
    }

    @Override
    public void reverseQueryFailHandle(RecordItem recordItem) {
        if (StringUtils.equals(recordItem.getPayChannelNo(), ChannelNoEnum.CMB.name())) {
            //如果是招行，则需要退回服务费
            orderReexchangeBiz.refundFee(recordItem);
        }
    }

    @Override
    public boolean refundLocalFrozenAmount(RecordItem recordItem) {
        log.info("智享汇产品操作退回本地冻结金额 mchOrderNo:{} platTrxNo:{}", recordItem.getMchOrderNo(), recordItem.getPlatTrxNo());
        if (StringUtils.equals(recordItem.getPayChannelNo(), ChannelNoEnum.JOINPAY_JXH.name())) {
            String msg = acLocalPayBiz.refundAmount(recordItem.getPlatTrxNo(), recordItem.getPayChannelNo());
            if (StringUtil.isNotEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(msg);
            }
            return true;
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("仅汇聚智享通道允许执行此操作");
        }
    }
}
