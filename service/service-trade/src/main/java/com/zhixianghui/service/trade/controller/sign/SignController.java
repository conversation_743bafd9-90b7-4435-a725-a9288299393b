package com.zhixianghui.service.trade.controller.sign;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.banklink.service.report.ChannelReportFacade;
import com.zhixianghui.facade.banklink.vo.report.AltMchSignVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.ExecuteUrlResDataVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.ReportFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.vo.CreateSignReqVo;
import com.zhixianghui.facade.trade.vo.PreSignReqVo;
import com.zhixianghui.service.trade.biz.SignRecordBiz;
import com.zhixianghui.service.trade.biz.UserInfoBiz;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import com.zhixianghui.service.trade.process.SignBiz;
import com.zhixianghui.service.trade.utils.ImageUtil;
import com.zhixianghui.service.trade.vo.req.SignQueryReqVo;
import com.zhixianghui.service.trade.vo.res.CreateSignResVo;
import com.zhixianghui.service.trade.vo.res.PreSignResVo;
import com.zhixianghui.service.trade.vo.res.SignQueryResVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @description 签约对外接口
 * @date 2021-01-14 09:39
 **/
@Slf4j
@RestController
@RequestMapping("zxhSign")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SignController {

    private final UserInfoBiz userInfoBiz;
    private final SignRecordBiz signRecordBiz;
    private final SignBiz signBiz;
    private final ZxhLimitBiz limitBiz;
    private final RedisLock redisLock;
    private static final long LIMIT_SIZE = 1048576L;
    @Value("${file.domain}")
    private String DOMAIN_PREFIX;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;


    @SuppressWarnings("unchecked")
    @PostMapping("/preSign")
    public ResponseDto<PreSignResVo> preSign(@RequestBody @Valid RequestDto<PreSignReqVo> paramVo) {
        String employerNo = paramVo.getMchNo();
        PreSignReqVo reqVo = paramVo.getData();
        String logFlag = String.join("-", employerNo, reqVo.getMainstayNo());
        log.info("[预签约==> {}] 开始", logFlag);

        // 敏感传输参数解密
        decryptParam(reqVo, paramVo.getSecKey());
        // 校验收到参数合法性
        checkParam(paramVo);

        // 检查模板是否配置
        if (signBiz.checkTemplateId(employerNo, reqVo.getMainstayNo())) {
            return ResponseDto.fail(ApiExceptions.API_EMPTY_TEMPLATE.getApiErrorCode(), ApiExceptions.API_EMPTY_TEMPLATE.getErrMsg());
        }

        //无记录的情况 处理图片信息
        handleImage(paramVo);
        return signBiz.apiPresign(employerNo, reqVo, logFlag, paramVo.getSignType());
    }

    @Autowired
    private FastdfsClient fastdfsClient;
    private void handleImage(RequestDto<PreSignReqVo> paramVo) {
        String yishuiNo = dataDictionaryFacade.getSystemConfig("yishui");
        if (StringUtils.equals(yishuiNo, paramVo.getData().getMainstayNo())&&StringUtils.isBlank(paramVo.getCerFacePhoto())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("该供应商签约必传半身照");
        }

        byte[] backPhoto = new byte[0];
        byte[] frontPhoto = new byte[0];
        byte[] cerfacePhoto = new byte[0];
        try {
            if (StringUtils.isNotBlank(paramVo.getIdCardBackPhoto())) {
                backPhoto = ImageUtil.translateBase64(paramVo.getIdCardBackPhoto());
            }
            if (StringUtils.isNotBlank(paramVo.getIdCardFrontPhoto())) {
                frontPhoto = ImageUtil.translateBase64(paramVo.getIdCardFrontPhoto());
            }
            if (StringUtils.isNotBlank(paramVo.getCerFacePhoto())) {
                cerfacePhoto = ImageUtil.translateBase64(paramVo.getCerFacePhoto());
            }
        } catch (IOException e) {
            log.error("图片转换失败:", e);
            return;
        }
        if (backPhoto.length > LIMIT_SIZE || frontPhoto.length> LIMIT_SIZE||cerfacePhoto.length>LIMIT_SIZE) {
            // 不能超过1M
            throw ApiExceptions.API_LIMIT_PHOTO;
        }

        if (backPhoto.length > 0) {
            String idCardBackUrl = fastdfsClient.uploadFile(backPhoto, RandomUtil.get16LenStr() + ".jpg");
            paramVo.getData().setIdCardBackUrl(idCardBackUrl);
        }
        if (frontPhoto.length > 0) {
            String idCardFrontUrl = fastdfsClient.uploadFile(frontPhoto, RandomUtil.get16LenStr() + ".jpg");
            paramVo.getData().setIdCardFrontUrl(idCardFrontUrl);
        }
        if (cerfacePhoto.length > 0) {
            String cerFaceUrl = fastdfsClient.uploadFile(cerfacePhoto, RandomUtil.get16LenStr() + ".jpg");
            paramVo.getData().setCerFaceUrl(cerFaceUrl);
        }
    }

    @PostMapping("/createSign")
    public ResponseDto<CreateSignResVo> createSign(@RequestBody @Valid RequestDto<CreateSignReqVo> paramVo) {
        String employerNo = paramVo.getMchNo();
        CreateSignReqVo reqVo = paramVo.getData();
        String logFlag = String.join("-", employerNo, reqVo.getUserId());
        log.info("[{}]==>发起签约", logFlag);

        String userId = reqVo.getUserId();
        SignRecord signRecord = signRecordBiz.getOne(Collections.singletonMap("userId", userId));
        if (signRecord == null) {
            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg("签约记录不存在,请先做预签约");
        }
        if (!StringUtils.equals(paramVo.getMchNo(), signRecord.getEmployerNo())) {
            throw ApiExceptions.API_SIGN_FAIL.newWithErrMsg("签约记录不存在");
        }
        if (signRecord.getSignStatus().intValue() == SignStatusEnum.SIGN_SUCCESS.getValue()) {
            CreateSignResVo createSignResVo = new CreateSignResVo();
            createSignResVo.setSignStatus(SignStatusEnum.SIGN_SUCCESS.getValue());
            createSignResVo.setBizErrCode(BizCodeEnum.HAS_BEEN_COMPLETED.getCode());
            createSignResVo.setBizErrMsg(BizCodeEnum.HAS_BEEN_COMPLETED.getMsg());
            return ResponseDto.success(createSignResVo, "");
        }

        //获取锁
        log.info("[发起签约环节: {}]==>获取发起签约锁", logFlag);
        String lockKey = String.join(":", TradeConstant.CREATE_SIGN_LOCK_KEY, logFlag);
        String clientId = redisLock.tryLockLong(lockKey, 0, 1);
        try {
            if (clientId == null) {
                log.info("[发起签约环节: {}]==>获取发起签约锁失败，直接丢弃", logFlag);
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
            }
//            if (reqVo.getSignType() == ChannelSignTypeEnum.STAMP_SILENCE.getValue()) {
//                String url = signBiz.autoAddSeal(reqVo);
//                CreateSignResVo createSignResVo = new CreateSignResVo();
//                if (StringUtils.isNotBlank(url)) {
//                    createSignResVo.setSignUrl(DOMAIN_PREFIX + url);
//                    createSignResVo.setSignStatus(SignStatusEnum.SIGN_SUCCESS.getValue());
//                    createSignResVo.setBizErrCode(BizCodeEnum.HAS_BEEN_COMPLETED.getCode());
//                    createSignResVo.setBizErrMsg(BizCodeEnum.HAS_BEEN_COMPLETED.getMsg());
//                    return ResponseDto.success(createSignResVo, "");
//                }
//                return defaultResponse(createSignResVo);
//            }
            ExecuteUrlResDataVo resVo = signBiz.createSign(reqVo, logFlag, false);
            if (resVo == null) {
                throw ApiExceptions.API_SIGN_CHANNEL_FAIL;
            }
            log.info("[{}]==>发起签约成功", logFlag);
            CreateSignResVo createSignResVo = new CreateSignResVo();
            createSignResVo.setSignUrl(resVo.getShortUrl());
            createSignResVo.setSignUrlH5(resVo.getUrl());
            if (resVo.isExist()) {
                createSignResVo.setSignStatus(SignStatusEnum.SIGN_SUCCESS.getValue());
                createSignResVo.setBizErrCode(BizCodeEnum.HAS_BEEN_COMPLETED.getCode());
                createSignResVo.setBizErrMsg(BizCodeEnum.HAS_BEEN_COMPLETED.getMsg());
                return ResponseDto.success(createSignResVo, "");
            }
            if (resVo.isResendExceedLimit()) {
                // 如果是重发短信超过限制
                createSignResVo.setSignStatus(SignStatusEnum.WAIT_SIGN.getValue());
                createSignResVo.setBizErrCode(BizCodeEnum.RESEND_EXCEED_LIMIT.getCode());
                createSignResVo.setBizErrMsg(BizCodeEnum.RESEND_EXCEED_LIMIT.getMsg());
                return ResponseDto.success(createSignResVo, "");
            }
            return defaultResponse(createSignResVo);
        } catch (BizException e) {
            log.error("[{}]==>发起签约 业务异常：", logFlag, e);
            return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
        } catch (Exception e) {
            log.error("[{}]==>发起签约 系统异常：", logFlag, e);
            return ResponseDto.unknown();
        } finally {
            if (clientId != null) {
                redisLock.unlockLong(clientId);
            }
        }
    }

    private ResponseDto<CreateSignResVo> defaultResponse(CreateSignResVo createSignResVo) {
        createSignResVo.setSignStatus(SignStatusEnum.WAIT_SIGN.getValue());
        createSignResVo.setBizErrCode(BizCodeEnum.IN_PROCESS.getCode());
        createSignResVo.setBizErrMsg(BizCodeEnum.IN_PROCESS.getMsg());
        return ResponseDto.success(createSignResVo, "");
    }


    @PostMapping("/signQuery")
    public ResponseDto<SignQueryResVo> signQuery(@RequestBody @Valid RequestDto<SignQueryReqVo> paramVo) {
        SignQueryReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        String logFlag = employerNo + "-" + reqVo.getUserId()+"-"+reqVo.getOpenUserId();
        log.info("[{}]查询签约记录信息：{}", logFlag, JsonUtil.toString(paramVo));
        if (StringUtils.isAllBlank(reqVo.getUserId(),reqVo.getOpenUserId())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数userId不能空");
        }
        try {
            SignRecord signRecord;
            if (StringUtils.isNotBlank(reqVo.getUserId())){
                signRecord = signRecordBiz.getOne(Collections.singletonMap("userId", reqVo.getUserId()));
            }else {
                if (StringUtils.isBlank(reqVo.getMainstayNo())) {
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("使用openUserId时供应商编号不能为空");
                }

                Map<String, Object> param = MapUtil.builder(new HashMap<String, Object>())
                        .put("openUserId", reqVo.getOpenUserId())
                        .put("employerNo", employerNo)
                        .put("mainstayNo", reqVo.getMainstayNo()).build();
                signRecord = signRecordBiz.getOne2(param);
            }


            if (signRecord == null) {
                throw ApiExceptions.API_SIGN_RECORD_NOT_EXIST;
            }
            String key = RandomUtil.get16LenStr();
            SignQueryResVo signQueryResVo = new SignQueryResVo();
            signQueryResVo.setUserId(signRecord.getUserId());
            signQueryResVo.setOpenUserId(signRecord.getOpenUserId());
            signQueryResVo.setName(AESUtil.encryptECB(signRecord.getReceiveNameDecrypt(), key));
            signQueryResVo.setIdCardNo(AESUtil.encryptECB(signRecord.getReceiveIdCardNoDecrypt(), key));
            signQueryResVo.setPhoneNo(AESUtil.encryptECB(signRecord.getReceivePhoneNoDecrypt(), key));
            signQueryResVo.setMainstayNo(signRecord.getMainstayNo());
            signQueryResVo.setSignStatus(signRecord.getSignStatus());
            signQueryResVo.setBizErrCode(StringUtils.isBlank(signRecord.getErrCode()) ? BizCodeEnum.SUCCESS.getCode() : signRecord.getErrCode());
            signQueryResVo.setBizErrMsg(StringUtils.isBlank(signRecord.getErrMsg()) ? BizCodeEnum.SUCCESS.getMsg() : signRecord.getErrMsg());
            signQueryResVo.setAuthStatus(userInfoBiz.isVerified(signRecord.getReceiveIdCardNoMd5()) ? AuthStatusEnum.SUCCESS.getValue() : AuthStatusEnum.UN_AUTH.getValue());
            ResponseDto<SignQueryResVo> returnVo = ResponseDto.success(signQueryResVo, key);
            log.info("[{}]查询签约记录：{}", logFlag, JsonUtil.toString(returnVo));
            return returnVo;
        } catch (BizException e) {
            log.error("[{}]==>查询签约记录 业务异常：", logFlag, e);
            return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
        } catch (Exception e) {
            log.error("[{}]==>查询签约记录 系统异常：", logFlag, e);
            return ResponseDto.unknown();
        }
    }


    private void checkParam(RequestDto<PreSignReqVo> paramVo) {
        PreSignReqVo reqVo = paramVo.getData();
        if (StringUtils.isNotEmpty(reqVo.getPhoneNo())
                && !ValidateUtil.isMobile(reqVo.getPhoneNo())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_phone_no  手机号码(" + reqVo.getPhoneNo() + ")格式错误");
        }
        if (!IDCardUtils.verifi(reqVo.getIdCardNo())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_id_card_no 身份证号(" + reqVo.getIdCardNo() + ")格式错误");
        }
        String yishuiNo = dataDictionaryFacade.getSystemConfig("yishui");
        if (StringUtils.equals(yishuiNo, reqVo.getMainstayNo())&&(StringUtils.isBlank(reqVo.getBankCardCode())||StringUtils.isBlank(paramVo.getCerFacePhoto()))) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("该供应商签约必传银行卡号");
        }

        if (!ValidateUtil.isChineseName(reqVo.getName())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("name  姓名(" + reqVo.getName() + ")格式错误");
        }

        //查询代征关系
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(paramVo.getMchNo(),reqVo.getMainstayNo());
        if (employerMainstayRelation == null || employerMainstayRelation.getStatus() == OpenOffEnum.OFF.getValue()) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("商户号 (" + paramVo.getMchNo() + ") 与代征主体号(" + reqVo.getMainstayNo() + ")未激活代征关系");
        }
    }

    private void decryptParam(PreSignReqVo reqVo, String secKey) {
        reqVo.setName(limitBiz.decryptNotNull(reqVo.getName(), "name", secKey));
        reqVo.setIdCardNo(limitBiz.decryptNotNull(reqVo.getIdCardNo(), "id_card_no", secKey));
        if (StringUtils.isNotBlank(reqVo.getPhoneNo())) {
            reqVo.setPhoneNo(limitBiz.decrypt(reqVo.getPhoneNo(), "phone_no", secKey));
        }
    }

    /**
     * 此接口只用于处理商户协议签约和文件上传的历史数据，不对外开放
     */
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private ReportFacade reportFacade;
    @PostMapping("uploadPic")
    public ResponseDto<String> uploadPic(@RequestBody @Valid RequestDto<Map<String,String>> paramVo){
        Map<String, String> data = paramVo.getData();

        ThreadUtil.execAsync(() -> {
            EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getOneBySubMerchantNoAndPayChannelNo(data.get("subMerchantNo"), ChannelNoEnum.JOINPAY.name());
            reportFacade.uploadPic(employerAccountInfo.getEmployerNo(), employerAccountInfo.getMainstayNo());
        });

        return ResponseDto.success("提交成功");
    }

    @Reference
    private ChannelReportFacade channelReportFacade;
    @PostMapping("cheackMchSignAndPicUploadApi")
    public ResponseDto<Map<String, Map<String,String>>> cheackMchSignAndPicUploadApi(@RequestBody @Validated RequestDto<Map<String,String>> paramVo){
        Map<String, String> data = paramVo.getData();
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getOneBySubMerchantNoAndPayChannelNo(data.get("subMerchantNo"), ChannelNoEnum.JOINPAY.name());
        if (employerAccountInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道信息不存在或者未开通该通道");
        }
        AltMchSignVo altMchSignVo = new AltMchSignVo();
        altMchSignVo.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
        altMchSignVo.setAltMchNo(employerAccountInfo.getSubMerchantNo());
        altMchSignVo.setChannelNo(ChannelNoEnum.JOINPAY.name());
        Map<String, Map<String, String>> map = channelReportFacade.cheackMchSignAndPicUpload(altMchSignVo);
        Map<String, String> info = new HashMap<>();
        info.put("employerNo", employerAccountInfo.getEmployerNo());
        info.put("employerName", employerAccountInfo.getEmployerName());
        info.put("mainstayNo", employerAccountInfo.getMainstayNo());
        info.put("mainstayName", employerAccountInfo.getMainstayName());
        map.put("employerInfo", info);
        return ResponseDto.success(map);
    }
}
