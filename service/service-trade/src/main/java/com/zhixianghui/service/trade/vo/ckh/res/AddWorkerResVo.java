package com.zhixianghui.service.trade.vo.ckh.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName AddWorkerResVo
 * @Description TODO
 * @Date 2022/10/26 17:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AddWorkerResVo extends ApiBizBaseDto {

    private String name;

    private String idCardNo;

    private String phoneNo;

    private String status;

    private String auth;
}
