package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.facade.trade.dto.AdjustAccountParamDTO;
import com.zhixianghui.facade.trade.dto.CmbAccountQueryDTO;
import com.zhixianghui.facade.trade.dto.CmbCreateAccountDTO;
import com.zhixianghui.facade.trade.entity.CmbMerchantBalance;
import com.zhixianghui.facade.trade.service.CmbMerchantBalanceFacade;
import com.zhixianghui.service.trade.biz.CmbMerchantBalanceBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/22 14:28 
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CmbMerchantBalanceFacadeImpl implements CmbMerchantBalanceFacade {

    private final CmbMerchantBalanceBiz cmbMerchantBalanceBiz;

    @Override
    public void createMerchantBalance(CmbCreateAccountDTO cmbCreateAccount) {
        cmbMerchantBalanceBiz.createMerchantBalance(cmbCreateAccount);
    }

    @Override
    public void adjustAccount(AdjustAccountParamDTO adjustmentDTO) {
        cmbMerchantBalanceBiz.adjustAccount(adjustmentDTO);
    }

    @Override
    public CmbMerchantBalance getCmbMerchantBalance(CmbAccountQueryDTO cmbAccountQueryDTO) {
        return cmbMerchantBalanceBiz.getCmbMerchantBalance(cmbAccountQueryDTO);
    }

    @Override
    public BigDecimal getAmount(CmbAccountQueryDTO param) {
        return cmbMerchantBalanceBiz.getAmount(param);
    }
}
