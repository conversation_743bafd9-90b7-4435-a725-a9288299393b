package com.zhixianghui.service.trade.controller.ckh;

import com.alibaba.fastjson.JSONArray;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.api.base.params.FileParam;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.IDCardUtils;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.service.MerchantCacheFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.service.trade.biz.ApiJobBiz;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import com.zhixianghui.service.trade.vo.ckh.req.*;
import com.zhixianghui.service.trade.vo.ckh.res.*;
import com.zhixianghui.service.trade.vo.res.CommonPageResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName JobController
 * @Description TODO
 * @Date 2022/10/20 10:20
 */
@Slf4j
@RestController
@RequestMapping("ckh")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class JobApiController {

    @Value("${file.domain}")
    private String DOMAIN_PREFIX;

    private final ApiJobBiz apiJobBiz;

    private final ZxhLimitBiz limitBiz;

    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;

    /**
     * 岗位类目查询api
     * @param paramVo
     * @return
     */
    @PostMapping("positionQuery")
    public ResponseDto<PositionResVo> positionQuery(@RequestBody RequestDto<Void> paramVo){
        String mchNo = paramVo.getMchNo();
        try {
            List<MerchantEmployerPosition> positionList = merchantEmployerPositionFacade.listByMchNo(mchNo);
            List<PositionVo> resList = positionList.stream().map(x->{
                PositionVo positionVo = new PositionVo();
                positionVo.setPositionNo(x.getWorkCategoryCode());
                positionVo.setPositionName(x.getWorkCategoryName());
                return positionVo;
            }).collect(Collectors.toList());

            PositionResVo resVo = new PositionResVo();
            resVo.setPositionList(resList);
            //错误描述
            resVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            resVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<PositionResVo> returnVo = ResponseDto.success(resVo,"");
            return returnVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }

    /**
     *  创建任务api
     * @param paramVo
     * @return
     */
    @PostMapping("createJob")
    public ResponseDto<CreateJobResVo> createJob(@RequestBody @Valid RequestDto<CreateJobReqVo> paramVo){
        String mchNo = paramVo.getMchNo();
        CreateJobReqVo createJobReqVo = paramVo.getData();
        try {
            //校验商户状态
            Job job = apiJobBiz.createJob(mchNo,createJobReqVo);
            CreateJobResVo createJobResVo = new CreateJobResVo();
            createJobResVo.setJobNo(job.getJobId());
            //错误描述
            createJobResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            createJobResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<CreateJobResVo> returnVo = ResponseDto.success(createJobResVo,"");
            return returnVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }

    /**
     * 关联人员
     * @param paramVo
     * @return
     */
    @PostMapping("addWorker")
    public ResponseDto<AddWorkerResVo> addWorker(@Valid @RequestBody RequestDto<AddWorkerReqVo> paramVo){
        String mchNo = paramVo.getMchNo();
        String secKey = paramVo.getSecKey();
        AddWorkerReqVo addWorkerReqVo = paramVo.getData();
        try {
            // 敏感传输参数解密
            decryptParam(addWorkerReqVo,secKey);
            // 校验收到参数合法性
            checkParam(addWorkerReqVo);
            JobWorkerRecord jobWorkerRecord = apiJobBiz.addWorker(addWorkerReqVo,mchNo);
            AddWorkerResVo addWorkerResVo = new AddWorkerResVo();
            String key = RandomUtil.get16LenStr();
            addWorkerResVo.setName(AESUtil.encryptECB(jobWorkerRecord.getWorkerName(),key));
            addWorkerResVo.setIdCardNo(AESUtil.encryptECB(jobWorkerRecord.getWorkerIdcard(),key));
            addWorkerResVo.setPhoneNo(AESUtil.encryptECB(jobWorkerRecord.getWorkerPhone(),key));
            addWorkerResVo.setAuth(String.valueOf(jobWorkerRecord.getAuthStatus()));
            addWorkerResVo.setStatus(String.valueOf(jobWorkerRecord.getJobStatus()));
            //错误描述
            addWorkerResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            addWorkerResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<AddWorkerResVo> returnVo = ResponseDto.success(addWorkerResVo,key);
            return returnVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }

    /**
     * 上传交付成果
     * @param paramVo
     * @return
     */
    @PostMapping("uploadResults")
    public ResponseDto<UploadResultsResVo> uploadResults(@Valid @RequestBody RequestDto<UploadResultsReqVo> paramVo){
        String mchNo = paramVo.getMchNo();
        String secKey = paramVo.getSecKey();
        List<FileParam> fileList = paramVo.getFileList();
        UploadResultsReqVo uploadResultsReqVo = paramVo.getData();
        try {
            uploadResultsReqVo.setPhoneNo(AESUtil.decryptECB(uploadResultsReqVo.getPhoneNo(),secKey));
            JobWorkerRecord jobWorkerRecord = apiJobBiz.uploadResults(fileList,uploadResultsReqVo,mchNo);
            String key = RandomUtil.get16LenStr();
            UploadResultsResVo uploadResultsResVo = new UploadResultsResVo();
            uploadResultsResVo.setName(AESUtil.encryptECB(jobWorkerRecord.getWorkerName(),key));
            uploadResultsResVo.setIdCardNo(AESUtil.encryptECB(jobWorkerRecord.getWorkerIdcard(),key));
            uploadResultsResVo.setPhoneNo(AESUtil.encryptECB(jobWorkerRecord.getWorkerPhone(),key));
            uploadResultsResVo.setStatus(String.valueOf(jobWorkerRecord.getJobStatus()));
            List<String> fileUrlList = JSONArray.parseArray(jobWorkerRecord.getAttachment(),String.class);
            if (fileUrlList != null && fileUrlList.size() > 0){
                fileUrlList.stream().forEach(x-> x = DOMAIN_PREFIX + x );
            }
            uploadResultsResVo.setFileUrlList(fileUrlList);
            //错误描述
            uploadResultsResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            uploadResultsResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<UploadResultsResVo> returnVo = ResponseDto.success(uploadResultsResVo,key);
            return returnVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }

    /**
     * 完成任务
     * @param paramVo
     * @return
     */
    @PostMapping("completeJob")
    public ResponseDto<CompleteJobResVo> completeJob(@Valid @RequestBody RequestDto<CompleteJobReqVo> paramVo){
        String mchNo = paramVo.getMchNo();
        CompleteJobReqVo completeJobReqVo = paramVo.getData();
        try {
            apiJobBiz.complateJob(mchNo,completeJobReqVo);
            CompleteJobResVo completeJobResVo = new CompleteJobResVo();
            //错误描述
            completeJobResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            completeJobResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<CompleteJobResVo> returnVo = ResponseDto.success(completeJobResVo,"");
            return returnVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }

    /**
     * 查询任务详情
     * @param paramVo
     * @return
     */
    @PostMapping("jobDetail")
    public ResponseDto<JobDetailResVo> jobDetail(@Valid @RequestBody RequestDto<JobDetailReqVo> paramVo){
        String mchNo = paramVo.getMchNo();
        JobDetailReqVo data = paramVo.getData();
        try {
            JobDetailResVo jobDetailResVo = apiJobBiz.getJobDetail(mchNo,data.getJobNo());
            //错误描述
            jobDetailResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            jobDetailResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<JobDetailResVo> returnVo = ResponseDto.success(jobDetailResVo,"");
            return returnVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }

    @PostMapping("workerPage")
    public ResponseDto<CommonPageResVo<WorkerPageResVo>> workerPage(@Valid @RequestBody RequestDto<JobWorkerPageReqVo> paramVo){
        String mchNo = paramVo.getMchNo();
        JobWorkerPageReqVo data = paramVo.getData();
        try {
            String key = RandomUtil.get16LenStr();
            CommonPageResVo<WorkerPageResVo> resVo = apiJobBiz.workerPage(mchNo,data,key);
            //错误描述
            resVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            resVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<CommonPageResVo<WorkerPageResVo>> returnVo = ResponseDto.success(resVo,key);
            return returnVo;
        }catch (BizException e){
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            return ResponseDto.unknown();
        }
    }


    private void checkParam(AddWorkerReqVo addWorkerReqVo) {
        if (!ValidateUtil.isChineseName(addWorkerReqVo.getName())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("name 姓名(" + addWorkerReqVo.getName() + ")格式错误");
        }
        if(!ValidateUtil.isMobile(addWorkerReqVo.getPhoneNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("phone_no  手机号码(" + addWorkerReqVo.getPhoneNo() + ")格式错误");
        }
        if(!IDCardUtils.verifi(addWorkerReqVo.getIdCardNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_id_card_no 身份证号(" + addWorkerReqVo.getIdCardNo() + ")格式错误");
        }

    }

    private void decryptParam(AddWorkerReqVo reqVo, String secKey) {
        reqVo.setName(limitBiz.decryptNotNull(reqVo.getName(), "name", secKey));
        reqVo.setIdCardNo(limitBiz.decryptNotNull(reqVo.getIdCardNo(), "id_card_no", secKey));
        reqVo.setPhoneNo(limitBiz.decryptNotNull(reqVo.getPhoneNo(), "phone_no", secKey));
    }

}
