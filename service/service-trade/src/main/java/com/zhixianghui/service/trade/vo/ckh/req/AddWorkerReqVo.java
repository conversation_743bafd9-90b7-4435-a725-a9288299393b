package com.zhixianghui.service.trade.vo.ckh.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName AddWorkerReqVo
 * @Description TODO
 * @Date 2022/10/26 10:19
 */
@Data
public class AddWorkerReqVo implements Serializable {

    @NotBlank(message = "job_no 任务编号不能为空")
    private String jobNo;

    @NotBlank(message = "name 雇员姓名不能为空")
    private String name;

    @NotBlank(message = "id_card_no 雇员身份证号不能为空")
    private String idCardNo;

    @NotBlank(message = "phone_no 雇员手机号码不能为空")
    private String phoneNo;
}
