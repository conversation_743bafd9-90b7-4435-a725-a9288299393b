package com.zhixianghui.service.trade.listener.tasks;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.trade.dto.AcMerchantBalanceAddDto;
import com.zhixianghui.service.trade.biz.AcLocalPayBiz;
import com.zhixianghui.service.trade.biz.AcMerchantBalanceBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MAINSTAY_SETTLE_SYNC, consumeThreadMax = 1, consumerGroup = "AcSettleMainstaySyncComsumer")
public class AcSettleMainstaySyncTaskListener extends TaskRocketMQListener<JSONObject> {
    @Reference
    private MerchantQueryFacade merchantFacade;
    @Autowired
    private AcMerchantBalanceBiz acMerchantBalanceBiz;


    public void runTask(JSONObject map) {
        Map<String, Object> paramMap=new HashMap<>();
        paramMap.put("merchantType", MerchantTypeEnum.MAINSTAY.getValue());
        List<Merchant> merchants = merchantFacade.listBy(paramMap);
        log.info("[同步账户]供应商同步商户账本:{}", JSONArray.toJSONString(merchants));
        for (Merchant merchant : merchants) {
            AcMerchantBalanceAddDto dto = new AcMerchantBalanceAddDto();
            dto.setMainstayNo(merchant.getMchNo());
            dto.setMainstayName(merchant.getMchName());
            dto.setPayChannelNo(ChannelNoEnum.JOINPAY.name());
            dto.setPayChannelName(ChannelNoEnum.JOINPAY.getDesc());
            dto.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            acMerchantBalanceBiz.createMerchantBalance(dto);
            log.info("[同步账户]供应商[{}]创建汇聚支付本地账户成功。", merchant.getMchNo());
        }

        log.info("[同步账户]供应商创建汇聚支付本地账户成功。");
    }
}