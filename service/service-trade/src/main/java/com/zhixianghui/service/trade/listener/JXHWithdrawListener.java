package com.zhixianghui.service.trade.listener;

import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.vo.jxh.ResData;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.enums.WithdrawStatusEnum;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.pay.jxh.biz.JXHWithdrawBiz;
import com.zhixianghui.starter.comp.component.RedisLock;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class JXHWithdrawListener {

    @Autowired
    private JXHWithdrawBiz jxhWithdrawBiz;

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Autowired
    private RedisLock redisLock;
    @Resource
    private OrderItemBiz orderItemBiz;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_JXH_WITHDRAW, selectorExpression = MessageMsgDest.TAG_JXH_WITHDRAW, consumeThreadMax = 3, consumerGroup = "jxhWithDrawConsume")
    public class JXHWithdrawReqListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            log.info("接收到供应商|用工企业发起渠道提现的消息：" + jsonParam);
            WithdrawRecord withdrawRecord = JsonUtil.toBean(jsonParam, WithdrawRecord.class);
            if (withdrawRecord == null || StringUtil.isEmpty(withdrawRecord.getMainstayNo()) || StringUtil.isEmpty(withdrawRecord.getChannelNo())) {
                log.error("提现业务参数缺失，不执行该操作，消息参数为：" + jsonParam);
                return;
            }

            String employerNo = withdrawRecord.getEmployerNo();
            String mainstayNo = withdrawRecord.getMainstayNo();
            String channelNo = withdrawRecord.getChannelNo();
            Integer merchantType = withdrawRecord.getMerchantType();
            //查询供应商支付账号
            MainstayChannelRelation channelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(mainstayNo, channelNo);
            if (channelRelation == null) {
                log.error("根据供应商编号[{}]渠道编号[{}]查询不到映射关系，无法继续执行提现流程。", mainstayNo, channelNo);
                return;
            }

            String key = AcUtil.getRedisLockKey(employerNo, mainstayNo, merchantType, channelNo);
            log.info("[{}]供应商提现，供应商编号：[{}]，提现单号：[{}]", channelNo, mainstayNo, withdrawRecord.getWithdrawNo());
            RLock rLock = redisLock.tryLock(key, WxUtil.LOCK_WAIT_TIME, WxUtil.LOCK_LEASE_TIME);
            if (rLock == null) {
                log.error("[{}]供应商编号：[{}]，提现单号：[{}]，供应商提现获取锁失败，抛出异常，等待消息重试", channelNo, mainstayNo, withdrawRecord.getWithdrawNo());
                return;
            }

            try {
                jxhWithdrawBiz.withdraw(withdrawRecord, channelRelation);
            } finally {
                redisLock.unlock(rLock);
            }
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_JXH_WITHDRAW_STATUS, selectorExpression = MessageMsgDest.TAG_JXH_WITHDRAW_STATUS, consumeThreadMax = 3, consumerGroup = "jxhWithDrawStatusConsumer")
    public class JXHWithDrawStatusListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            WithdrawRecord withdrawRecord = JsonUtil.toBean(jsonParam, WithdrawRecord.class);
            PayReqVo payReqVo = new PayReqVo();
            final MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(withdrawRecord.getMainstayNo(), withdrawRecord.getChannelNo());
            if (withdrawRecord.getWithdrawStatus().equals(WithdrawStatusEnum.NEW.getCode())) {
                payReqVo.setChannelMchNo(mainstayChannelRelation.getChannelMchNo());
                payReqVo.setChannelNo(mainstayChannelRelation.getPayChannelNo());
                payReqVo.setChannelName(withdrawRecord.getChannelName());
//                payReqVo.setPlatTrxNo(withdrawRecord.getWithdrawNo());
                payReqVo.setBankOrderNo(withdrawRecord.getWithdrawNo());
                jxhWithdrawBiz.payQuery(payReqVo);
            }
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_JXH_WITHDRAW_ASYNC, selectorExpression = MessageMsgDest.TAG_JXH_WITHDRAW_ASYNC, consumeThreadMax = 3, consumerGroup = "jxhWithDrawAsyncConsumer")
    public class JXHWithDrawAsyncListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            log.info("君享汇提现，接收到的参数：" + jsonParam);
            ResData data = JSONUtil.toBean(jsonParam, ResData.class);
            jxhWithdrawBiz.stateHandler(data);
        }
    }

}
