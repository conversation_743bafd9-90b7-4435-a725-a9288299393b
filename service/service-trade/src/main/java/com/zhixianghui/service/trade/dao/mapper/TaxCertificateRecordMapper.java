package com.zhixianghui.service.trade.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.trade.entity.TaxCertificateRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface TaxCertificateRecordMapper extends BaseMapper<TaxCertificateRecord> {

    Page<TaxCertificateRecord> listPage(Page<TaxCertificateRecord> page, @Param("param") Map<String, Object> paramMap);

    public List<TaxCertificateRecord> list(@Param("param") Map<String, Object> paramMap);
}
