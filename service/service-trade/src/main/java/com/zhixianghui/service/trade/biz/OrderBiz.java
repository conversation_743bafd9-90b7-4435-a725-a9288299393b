package com.zhixianghui.service.trade.biz;

import cn.hutool.core.map.MapUtil;
import com.alibaba.nacos.api.naming.pojo.AbstractHealthChecker;
import com.google.common.collect.Maps;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.common.statics.constants.redis.RedisLua;
import com.zhixianghui.common.statics.enums.data.AltSourceEnum;
import com.zhixianghui.common.statics.enums.data.AltTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceAmountTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.facade.data.service.MysqlCkAccountDetailFacade;
import com.zhixianghui.facade.merchant.entity.MerchantNotifySet;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.enums.MerchantNotifySetTypeEnum;
import com.zhixianghui.facade.merchant.service.MerchantNotifySetFacade;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.constant.UniqueKeyConst;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.entity.*;
import com.zhixianghui.facade.trade.enums.DynamicMsgEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.enums.RechargeStatusEnum;
import com.zhixianghui.service.trade.dao.OrderDao;
import com.zhixianghui.service.trade.dao.OrderItemDao;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.service.trade.helper.TradeNotifyBiz;
import com.zhixianghui.service.trade.vo.req.SingleGrantReqVo;
import com.zhixianghui.service.trade.vo.res.SingleGrantResVo;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
* 订单批次表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-11-03
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderBiz {

    private final OrderDao orderDao;
    private final OrderItemDao orderItemDao;
    private final TradeHelperBiz tradeHelperBiz;
    private final MchPlatBatchBiz mchPlatBatchBiz;
    private final MchPlatTrxNoBiz mchPlatTrxNoBiz;
    private final TradeUniqueBiz tradeUniqueBiz;
    private final RechargeRecordBiz rechargeRecordBiz;
    private final RedisClient redisClient;
    private final OrderItemBiz orderItemBiz;
    private final DynamicMsgBiz dynamicMsgBiz;
    private final TradeNotifyBiz tradeNotifyBiz;
    private final MerchantSecretFacade merchantSecretFacade;
    private final RecordItemBiz recordItemBiz;
    @Reference
    private RobotFacade robotFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private MysqlCkAccountDetailFacade ckAccountDetailFacade;
    @Reference
    private MerchantNotifySetFacade merchantNotifySetFacade;

    @Transactional(rollbackFor = Exception.class)
    public Long insert(Order order) {
        MchPlatBatch mchPlatBatch = new MchPlatBatch();
        mchPlatBatch.setEmployerNo(order.getEmployerNo());
        mchPlatBatch.setMchBatchNo(order.getMchBatchNo());
        mchPlatBatch.setPlatBatchNo(order.getPlatBatchNo());
        mchPlatBatchBiz.insert(mchPlatBatch);
        orderDao.insert(order);
        return order.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertOrderAndItem(Order order, OrderItem orderItem){
        try{
            MchPlatBatch mchPlatBatch = new MchPlatBatch();
            mchPlatBatch.setEmployerNo(order.getEmployerNo());
            mchPlatBatch.setMchBatchNo(order.getMchBatchNo());
            mchPlatBatch.setPlatBatchNo(order.getPlatBatchNo());
            mchPlatBatchBiz.insert(mchPlatBatch);
            orderDao.insert(order);

            MchPlatTrxNo mchPlatTrxNo = new MchPlatTrxNo();
            mchPlatTrxNo.setMchOrderNo(orderItem.getMchOrderNo());
            mchPlatTrxNo.setEmployerNo(orderItem.getEmployerNo());
            mchPlatTrxNo.setPlatTrxNo(orderItem.getPlatTrxNo());
            mchPlatTrxNoBiz.insert(mchPlatTrxNo);
            orderItemDao.insert(orderItem);
        }catch (DuplicateKeyException de){
            log.error("入库失败出现重复订单号",de);
            throw ApiExceptions.API_TRADE_ORDER_REPEAT;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertItem(OrderItem orderItem,RecordItem recordItem){
        try{

            MchPlatTrxNo mchPlatTrxNo = new MchPlatTrxNo();
            mchPlatTrxNo.setMchOrderNo(orderItem.getMchOrderNo());
            mchPlatTrxNo.setEmployerNo(orderItem.getEmployerNo());
            mchPlatTrxNo.setPlatTrxNo(orderItem.getPlatTrxNo());
            mchPlatTrxNoBiz.insert(mchPlatTrxNo);
            orderItemDao.insert(orderItem);
            recordItemBiz.insert(recordItem);
        }catch (DuplicateKeyException de){
            log.error("入库失败出现重复订单号",de);
            throw ApiExceptions.API_TRADE_ORDER_REPEAT;
        }
    }

    public void update(Order order) {
        orderDao.update(order);
    }

    public PageResult<List<Order>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return PageResult.newInstance(pageParam,new ArrayList<>());
        }
        return orderDao.listPage(paramMap,pageParam);
    }

    public Order getOne(Map<String, Object> paramMap) {
//        tradeHelperBiz.putOrderCreateDateToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return null;
        }
        return orderDao.getOne(paramMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelBatchOrder(Order order) {
        log.info("[{}]==>批次取消发放",order.getPlatBatchNo());
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.PENDING_GRANT.getValue())
        && !Objects.equals(order.getBatchStatus(),OrderStatusEnum.IMPORTING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次状态已经变更，不能取消发放");
        }
        order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
        orderDao.update(order);

        // 更新订单明细状态
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",order.getPlatBatchNo());
        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        orderItemDao.cancelOrderItem(paramMap);

        notifyMchAcceptFailResult(order);
    }

    public void notifyMchAcceptFailResult(Order order) {
        try{
            if (StringUtils.isNotEmpty(order.getCallbackUrl())){
                String cacheKey = TradeConstant.ACCEPT_FAIL_NOTIFY_MCH_REDIS_PREFIX + order.getPlatBatchNo();
                if(redisClient.get(cacheKey) == null) {
                    //缓存五分钟，期间不会再通知商户
                    redisClient.set(cacheKey, order.getPlatBatchNo(), 300);
                    List<OrderItem> itemList = orderItemBiz.listBy(order.getPlatBatchNo());
                    MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, itemList.get(0));
                    tradeNotifyBiz.notifyMerchant(merchantNotifyParam,order);
                    log.info("[{}-{}]异步受理失败，通知商户发放结果", order.getEmployerNo(), order.getMchBatchNo());
                }
            }else{
                String mchNo = order.getEmployerNo();
                final MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(mchNo);

                if (merchantSecret == null) {
                    return;
                }

                //查询是否配置notifyUrl
                MerchantNotifySet merchantNotifySet = merchantNotifySetFacade.getByMchNoAndType(mchNo, MerchantNotifySetTypeEnum.GRANT_COMPLETE_NOTIFY.getValue());
                if (merchantNotifySet == null){
                    return;
                }

                String notifyUrl = merchantNotifySet.getNotifyUrl();
                Integer notifyStatus = merchantNotifySet.getNotifyStatus();
                if (StringUtils.isNotBlank(notifyUrl) && notifyStatus == CommonStatusEnum.ACTIVE.getValue()) {
                    List<OrderItem> itemList = orderItemBiz.listBy(order.getPlatBatchNo());
                    itemList.forEach(item->{
                        String cacheKey = TradeConstant.GRANT_NOTIFY_MCH_BATCH_REDIS_PREFIX + order.getPlatBatchNo()+":"+item.getPlatTrxNo();
                        if(redisClient.get(cacheKey) == null) {
                            //缓存五分钟，期间不会再通知商户
                            redisClient.set(cacheKey, item.getPlatTrxNo(), 300);

                            Order.JsonEntity jsonEntity = new Order.JsonEntity();
                            jsonEntity.setSignType("1");
                            order.setJsonEntity(jsonEntity);

                            MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, item);
                            merchantNotifyParam.setNotifyUrl(notifyUrl);
                            tradeNotifyBiz.notifyMerchantBatch(merchantNotifyParam,item);
                            log.info("[{}-{}]异步受理失败，通知商户发放结果", order.getEmployerNo(), item.getPlatTrxNo());
                        }
                    });
                }
            }
        }catch (Exception e){
            log.info("[{}-{}]异步受理失败通知商户异常", order.getEmployerNo(), order.getMchBatchNo(), e);
            throw CommonExceptions.COMMON_RETRY_ERROR;
        }
    }

    public void cancelOrderItem(String platBatchNo){
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", platBatchNo);
        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        orderItemDao.cancelOrderItem(paramMap);
    }

    public Long countOrder(Map<String, Object> paramMap) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return 0L;
        }
        return orderDao.countBy(paramMap);
    }

    public BigDecimal sumWaitInvoiceAmount(Map<String, Object> paramMap) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return BigDecimal.ZERO;
        }
        return orderDao.sumWaitInvoiceAmount(paramMap);
    }

    public BigDecimal sumWaitCkhInvoiceAmount(Map<String, Object> paramMap) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return BigDecimal.ZERO;
        }
        return orderDao.sumWaitCkhInvoiceAmount(paramMap);
    }

    public BigDecimal sumWaitCepInvoiceAmount(Integer amountType, Map<String, Object> paramMap) {
        if (amountType == InvoiceAmountTypeEnum.GRANT_AMOUNT.getValue()) {
            return this.sumWaitCepGrantInvoiceAmount(paramMap);
        } else if (amountType == InvoiceAmountTypeEnum.SERVICE_FEE.getValue()) {
            return this.sumWaitCepServiceFeeInvoiceAmount(paramMap);
        } else {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("未支持的发票类型");
        }
    }

    public BigDecimal sumWaitCepGrantInvoiceAmount(Map<String, Object> paramMap) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return BigDecimal.ZERO;
        }
        return orderDao.sumWaitCepGrantInvoiceAmount(paramMap);
    }

    public BigDecimal sumWaitCepServiceFeeInvoiceAmount(Map<String, Object> paramMap) {
        tradeHelperBiz.putCreateTimeRangeToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return BigDecimal.ZERO;
        }
        return orderDao.sumWaitCepServiceFeeInvoiceAmount(paramMap);
    }

    public RechargeRecord getRechargeRecord(String rechargeOrderId) {
        if (rechargeOrderId == null || rechargeOrderId.trim().isEmpty()) {
            return null;
        }
        return rechargeRecordBiz.getByRechargeId(rechargeOrderId);
    }

    @Transactional(rollbackFor = Exception.class)
    public RechargeRecord addRechargeRecord(RechargeRecord rechargeRecord) {
        tradeUniqueBiz.insertByKey(UniqueKeyConst.RECHARGE_NO + rechargeRecord.getRechargeOrderId());
        boolean saved = rechargeRecordBiz.save(rechargeRecord);
        if (saved){
            RechargeRecord rechargeRecordNew = rechargeRecordBiz.getByRechargeId(rechargeRecord.getRechargeOrderId());
            if (rechargeRecord.getRechargeStatus() != null && rechargeRecord.getRechargeStatus() == RechargeStatusEnum.SUCCESS.getCode().shortValue()){
                //保存收支明细
                this.save2CkAcctDetail(rechargeRecordNew);
                dynamicMsgBiz.putDynamicMsg(rechargeRecord.getEmployerNo(),
                        String.format(DynamicMsgEnum.CHARGE.getMsg(),rechargeRecordNew.getRechargeAmount(),rechargeRecord.getRechargeOrderId()));
            }
            return rechargeRecordNew;
        }else {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("保存充值记录失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRechargeRecord(RechargeRecord rechargeRecord) {
        rechargeRecordBiz.updateRechargeRecord(rechargeRecord);
    }

    public void save2CkAcctDetail(RechargeRecord rechargeRecord) {
        final CkAccountDetail accountDetail = ckAccountDetailFacade.getOne(rechargeRecord.getRechargeOrderId(), AltSourceEnum.RECHARGE.getValue());
        if (accountDetail == null) {
            CkAccountDetail detail = new CkAccountDetail();
            detail.setAccountNo(rechargeRecord.getMainstayNo());
            detail.setTrxNo(rechargeRecord.getRechargeOrderId());
            detail.setAccountName(rechargeRecord.getMainstayName());
            detail.setAltAmount(rechargeRecord.getRechargeAmount());
            detail.setAltDesc("商户充值");
            detail.setAltSource(AltSourceEnum.RECHARGE.getValue());
            detail.setAltType(AltTypeEnum.RECIEVE.getValue());
            detail.setCreateTime(rechargeRecord.getCreateTime());
            detail.setChannelType(rechargeRecord.getChannelType().intValue());
            detail.setPayChannelNo(rechargeRecord.getChannelCode());
            detail.setPayChannelName(rechargeRecord.getChannelName());
            detail.setChannelTrxNo(rechargeRecord.getChannelOrderId());
            detail.setEmployerName(rechargeRecord.getEmployerName());
            detail.setEmployerNo(rechargeRecord.getEmployerNo());
            ckAccountDetailFacade.saveOne(detail);
        }
    }

    public RechargeRecord upRechargeRecord(RechargeRecord rechargeRecord) {
        boolean updated = rechargeRecordBiz.updateById(rechargeRecord);
        if (updated){
            RechargeRecord rechargeRecordNew = rechargeRecordBiz.getByRechargeId(rechargeRecord.getRechargeOrderId());
            return rechargeRecordNew;
        }else {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("更新充值记录失败");
        }
    }

    public void orderExpireQueue() {
        List<String> orderSet = getRedisSet(TradeConstant.ORDER_BATCH_ZSET_KEY);
        if (orderSet == null){
            return;
        }
        Map<String,Object> paramMap = new HashMap<>();

        //最大50次
        int maxTimes = 0;
        for (String s : orderSet) {
            paramMap.put("platBatchNo",s);
            Order order = this.getOne(paramMap);
            if (order != null){
                if (order.getBatchStatus() == OrderStatusEnum.PENDING_GRANT.getValue() ||
                        order.getBatchStatus() == OrderStatusEnum.IMPORTING.getValue() ||
                        order.getBatchStatus() == OrderStatusEnum.IMPORT_FAIL.getValue()){
                    try{
                        this.cancelBatchOrder(order);
                        //每处理完一次出队
                        redisClient.zrem(TradeConstant.ORDER_BATCH_ZSET_KEY,s);
                        log.info("批次号：[{}]超时，自动关闭订单",s);
                    }catch (BizException e){
                        if (getExceptionTimes(s) > 3){
                            log.info("无法修改批次状态，批次号：[{}]，直接出队",s);
                            redisClient.zrem(TradeConstant.ORDER_BATCH_ZSET_KEY,s);
                        }
                    }
                }else{
                    //发放中和发放完成直接出队，不作任何处理
                    redisClient.zrem(TradeConstant.ORDER_BATCH_ZSET_KEY,s);
                }
            }else{
                if (getExceptionTimes(s) > 3){
                    log.info("无法修改批次状态，批次号：[{}]，直接出队",s);
                    redisClient.zrem(TradeConstant.ORDER_BATCH_ZSET_KEY,s);
                }
            }
            if (++maxTimes > 50){
                break;
            }
        }
    }

    public Long getExceptionTimes(String no) {
        Long exceptionTimes = 0L;
        try {
            exceptionTimes = redisClient.incr(TradeConstant.ORDER_BATCH_ZSET_EXCEPTION + no);
            redisClient.expire(TradeConstant.ORDER_BATCH_ZSET_EXCEPTION + no,5*60);
        }catch (Exception e){
            log.error("查询批次号:[{}]重试次数异常",no);
        }
        return exceptionTimes;
    }


    private void pushWxRobot(Order order,long granting,int size) {
        //推送企业微信机器人
        MarkDownMsg markDownMsg = new MarkDownMsg();
        markDownMsg.setUnikey(order.getPlatBatchNo());
        markDownMsg.setRobotType(RobotTypeEnum.GRANTING_ROBOT.getType());

        StringBuffer sb = new StringBuffer("#### 卡单提醒 ");
        sb.append("\\n > 平台批次号：").append(order.getPlatBatchNo())
                .append("\\n > 商户名称：").append(order.getEmployerName())
                .append("\\n > 发放通道：").append(order.getChannelName())
                .append("\\n > 发放方式：").append(ChannelTypeEnum.getEnum(order.getChannelType()).getDesc())
                .append("\\n > 当前状态：").append(OrderStatusEnum.GRANTING.getDesc())
                .append("\\n > 已创建笔数：").append(size)
                .append("\\n > 处于发放中笔数：").append(granting)
                .append("\\n > 确认发放时间：").append(DateUtil.formatDateTime(order.getConfirmTime()));
        markDownMsg.setContent(sb.toString());
        robotFacade.pushMarkDownAsync(markDownMsg);
    }

    /**
     * 发放中队列
     */
    public void grantingExpireQueue() {
        List<String> orderSet = getRedisSet(TradeConstant.ORDER_GRANTING_ZSET_KEY);
        if (orderSet == null){
            return;
        }
        int maxTimes = 0;

        HashMap<String,Object> paramMap = new HashMap<>();
        List<OrderItem> orderItemList;
        for (String s : orderSet) {
            paramMap.put("platBatchNo",s);
            Order order = getOne(paramMap);
            if (order.getBatchStatus().intValue() != OrderStatusEnum.GRANTING.getValue()){
                redisClient.zrem(TradeConstant.ORDER_GRANTING_ZSET_KEY,s);
                continue;
            }
            orderItemList = orderItemBiz.listBy(s);
            int granting = 0,handUp = 0;
            for (OrderItem x : orderItemList) {
                if (x.getOrderItemStatus().intValue() == OrderItemStatusEnum.GRANTING.getValue()){
                    granting++;
                }
                if (x.getOrderItemStatus().intValue() == OrderItemStatusEnum.GRANT_HANG.getValue()){
                    handUp++;
                }
            }
            if ((granting == 0 && handUp == 0) || (granting > 0)){
                //推送到机器人
                pushWxRobot(order,granting,orderItemList.size());
            }
            redisClient.zrem(TradeConstant.ORDER_GRANTING_ZSET_KEY,s);
            if (++maxTimes > 20){
                break;
            }
        }
    }

    private List<String> getRedisSet(String prefix){
        long currentTime = System.currentTimeMillis();
        List<String> argv = new ArrayList<>();
        argv.add("0");
        argv.add("0");
        argv.add(Long.toString(currentTime));
        List<String> list = (List<String>) redisClient.evalLua(RedisLua.GET_SORTED_SET,prefix,new ArrayList<String>(){{add(prefix);}},argv);
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(OrderDeleteDTO orderDeleteDTO) {
        log.info("订单数据删除入参----------->{}",orderDeleteDTO);
        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
        Date createTime = orderDeleteDTO.getCreateTime();
        Date begin = DateUtil.getFirstOfMonth(createTime);
        log.info("开始时间----------------->{}",sdf.format(begin));
        Date end = DateUtil.getLastOfMonth(createTime);
        log.info("结束时间----------------->{}",sdf.format(end));
        orderDeleteDTO.setBeginDate(begin);
        orderDeleteDTO.setEndDate(end);
        orderDeleteDTO.setIsDelete(YesNoCodeEnum.YES.getValue());
        orderDeleteDTO.setUpdateTime(new Date());
        int res = orderDao.update("updateById", orderDeleteDTO);
        if(res!=1){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("删除数据失败");
        }
        int itemRes = orderItemDao.update("updateByPlatBatchNo", orderDeleteDTO);
        log.info("订单详情删除数据----------->{}",itemRes);
    }


    public Order getByPlatBatchNo(String platBatchNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
//        tradeHelperBiz.putOrderCreateDateToMap(paramMap);
        return orderDao.getOne(paramMap);
    }

    public boolean isExistNotCompleteOrder(String employerNo, String mainstayNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",mainstayNo);

        paramMap.put("batchStatusIn",new int[]{OrderStatusEnum.PENDING_GRANT.getValue(),
                OrderStatusEnum.GRANTING.getValue(),
                OrderStatusEnum.IMPORTING.getValue()});
        Order order = orderDao.getNotCompleteOrder(paramMap);
        if (order == null){
            return true;
        }else{
            return false;
        }
    }


    public OrderItemSumBo sumOrderItem(String mchBatchNo, String employerNo) {
        Map param = new HashMap();
        param.put("mchBatchNo",mchBatchNo);
        param.put("employerNo",employerNo);
        return orderItemBiz.sumOrderItem(param);

    }

    public Order getByMchBatchNo(String mchBatchNo, String employerNo) {
        Map param = new HashMap();
        param.put("mchBatchNo",mchBatchNo);
        param.put("employerNo",employerNo);
        return this.getOne(param);
    }

}
