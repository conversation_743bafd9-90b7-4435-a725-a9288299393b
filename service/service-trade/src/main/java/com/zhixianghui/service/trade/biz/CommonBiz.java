package com.zhixianghui.service.trade.biz;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BanklinkExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.pay.PayBankFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.pay.QueryPayOrderReqVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.merchant.service.MerchantChannelFacade;
import com.zhixianghui.facade.trade.entity.MchPlatTrxNo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.service.trade.factory.TradeFactory;
import com.zhixianghui.service.trade.helper.TradeNotifyBiz;
import com.zhixianghui.service.trade.process.AbstractGrantHandler;
import com.zhixianghui.service.trade.utils.BuildVoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 多表联合操作Biz
 * @date 2020-11-10 15:40
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CommonBiz {
    private final RecordItemBiz recordItemBiz;
    private final OrderItemBiz orderItemBiz;
    private final TradeNotifyBiz tradeNotifyBiz;
    private final MchPlatTrxNoBiz mchPlatTrxNoBiz;
    private final WxLocalPayBiz wxLocalPayBiz;
    @Reference
    private MerchantChannelFacade merchantChannelFacade;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference(retries = -1)
    private PayBankFacade payBankFacade;

    @Autowired
    private OrderReexchangeBiz orderReexchangeBiz;

    /**
     * 保存打款流水并更新订单明细
     *
     * @param orderItem  订单明细
     * @param recordItem 打款流水
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveRecordItemAndUpdateOrderItem(OrderItem orderItem, RecordItem recordItem) {
        try {
            orderItemBiz.update(orderItem);
            log.info("[{}]保存打款记录-{}", recordItem.getPlatTrxNo(), JSONUtil.toJsonPrettyStr(recordItem));
            recordItemBiz.insert(recordItem);
        } catch (DuplicateKeyException e) {
            log.error("保存记录明细，出现订单号重复：", e);
            throw ApiExceptions.API_TRADE_ORDER_REPEAT;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveRecordItemAndUpdateOrderItemNew(OrderItem orderItem, RecordItem recordItem) {
        try {
            orderItemBiz.update(orderItem);
            RecordItem item = recordItemBiz.getByPlatTrxNo(orderItem.getPlatTrxNo());
            if (item == null) {
                recordItemBiz.insert(recordItem);
            }
        } catch (DuplicateKeyException e) {
            log.error("保存记录明细，出现订单号重复：", e);
            throw ApiExceptions.API_TRADE_ORDER_REPEAT;
        }
    }

    /**
     * 更新打款流水和订单明细
     *
     * @param orderItem  订单明细
     * @param recordItem 打款流水
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRecordItemAndUpdateOrderItem(OrderItem orderItem, RecordItem recordItem, Date now) {
        orderItem.setUpdateTime(now);
        recordItem.setUpdateTime(now);
        orderItemBiz.update(orderItem);
        recordItemBiz.update(recordItem);
        if (StringUtil.isNotEmpty(recordItem.getChannelTrxNo())) {
            MchPlatTrxNo mchPlatTrxNo = mchPlatTrxNoBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
            if (mchPlatTrxNo != null) {
                mchPlatTrxNo.setChannelTrxNo(recordItem.getChannelTrxNo());
                mchPlatTrxNoBiz.update(mchPlatTrxNo);
            }
        }
    }


    /**
     * 更新订单明细和打款记录
     *
     * @param recordItem   订单明细
     * @param outerErrCode 外部错误码
     * @param outerErrMsg  外部错误信息
     * @param innerErrCode 内部错误码
     * @param innerErrMsg  内部信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessFail(RecordItem recordItem, String outerErrCode, String outerErrMsg, String innerErrCode, String innerErrMsg) {
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
        orderItem.setUpdateTime(new Date());
        orderItem.setCompleteTime(new Date());
        if (StringUtils.isNotEmpty(outerErrCode)) {
            orderItem.setErrorCode(outerErrCode);
            orderItem.setErrorDesc(outerErrMsg);
        }
        orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
        orderItemBiz.update(orderItem);
        Date now = new Date();
        recordItem.setUpdateTime(now);
        recordItem.setCompleteTime(now);
        if (StringUtils.isNotEmpty(innerErrCode)) {
            recordItem.setErrorCode(innerErrCode);
            recordItem.setErrorDesc(innerErrMsg);
        }
        recordItem.setProcessStatus(RecordItemStatusEnum.PAY_FAIL.getValue());
        RecordItem result = recordItemBiz.getByRemitPlatTrxNo(recordItem.getRemitPlatTrxNo());
        if(result!=null){
            recordItemBiz.update(recordItem);
        }
    }
}
