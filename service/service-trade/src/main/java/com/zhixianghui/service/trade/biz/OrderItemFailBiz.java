package com.zhixianghui.service.trade.biz;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.common.statics.entity.BaseEntity;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.PendingOrderOpeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.trade.entity.AuthRecord;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.service.trade.dao.OrderItemFailDao;
import com.zhixianghui.service.trade.factory.TradeFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.trade.entity.OrderItemFail;
import com.zhixianghui.service.trade.dao.mapper.OrderItemFailMapper;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderItemFailBiz extends ServiceImpl<OrderItemFailMapper, OrderItemFail> {

    @Autowired
    private OrderItemFailDao orderItemFailDao;
    @Autowired
    private OrderItemBiz orderItemBiz;
    @Autowired
    private TradeFactory tradeFactory;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private RiskControlFacade riskControlFacade;

    public List<OrderItemFail> getByPlatBatchNo(String platBatchNo) {
        final List<OrderItemFail> itemFails = this.list(new QueryWrapper<OrderItemFail>().eq(OrderItemFail.COL_PLAT_BATCH_NO, platBatchNo).orderByAsc(OrderItemFail.COL_ID));
        return itemFails;
    }

    public PageResult<List<OrderItemFail>> pageByPlatBatchNo(String platBatchNo, PageParam pageParam) {

        Map<String, Object> param = new HashMap<>();
        param.put("platBatchNo", platBatchNo);
        final PageResult<List<OrderItemFail>> pageResult = orderItemFailDao.listPage(param, pageParam);
        return pageResult;
    }

    /**
     * 商户取消发放
     * @param platTrxNo 挂单的订单明细编号
     * @param loginName 操作人
     */
    public void rejectOrderItem(String platTrxNo, String loginName) {
        Map<String,Object> map=new HashMap<>();
        map.put("platTrxNo",platTrxNo);
        map.put("status", ControlAtomEnum.PENDING.getValue());
        List<RiskcontrolProcessDetail> riskcontrolProcessDetails = riskControlFacade.listBy(map);
        List<Long> idList = riskcontrolProcessDetails.stream().map(BaseEntity::getId).collect(Collectors.toList());
        //更新明细的状态
        Map<String,Object> param = new HashMap<>();
        param.put("list", idList.size() == 0 ? null : idList);
        param.put("platTrxNoList", Collections.singletonList(platTrxNo));
        param.put("operation", PendingOrderOpeEnum.REJECT.getValue());
        param.put("updateUser", loginName);
        riskControlFacade.updateStatusByIdList(param);


        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
        if(orderItem == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单明细不存在");
        }
        if(!Objects.equals(orderItem.getOrderItemStatus(), OrderItemStatusEnum.GRANT_HANG.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单状态已变更，不能再进行操作");
        }
        //更新为受理失败状态
        orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
        orderItem.setIsPassHangup(false);
        orderItem.setHangupApprovalLoginName(loginName);
        orderItem.setUpdateTime(new Date());
        orderItem.setCompleteTime(new Date());
        orderItem.setRemark("商户主动取消发放");
        orderItem.setErrorCode(ApiExceptions.API_TRADE_RISK_REJECT.getApiErrorCode());
        orderItemBiz.update(orderItem);
        if (StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.CMB.name())&& orderItem.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue()) {
            // 通知发放
            tradeFactory.getGrantor(orderItem.getProductNo()).notifyGrantStart(orderItem.getEmployerNo(),orderItem.getMainstayNo(), orderItem.getPlatBatchNo(),
                    Lists.newArrayList(orderItem.getPlatTrxNo()));
        }
        // 更新统计
        tradeFactory.getGrantor(orderItem.getProductNo()).notifyGrantBatchCount(orderItem.getPlatBatchNo());
    }
}
