package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 发起签约结果
 * @date 2021/1/15 16:49
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreateSignResVo extends ApiBizBaseDto {
    /**
     * 签约状态
     */
    private Integer signStatus;

    /**
     * 签约地址
     */
    private String signUrl;

    /**
     * H5签约地址
     */
    private String signUrlH5;
}
