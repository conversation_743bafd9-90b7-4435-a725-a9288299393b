package com.zhixianghui.service.trade.enums;

/**
 * <AUTHOR>
 * @Date 2021/6/9 15:21
 */
public enum  SignTemplateTypeEnum {

    EMPLOYER(100, "用工企业指定"),
    MAINSTAY(101, "代征主体指定"),;

    int type;
    String desc;

    SignTemplateTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
