package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.trade.biz.FeeOrderBatchBiz;
import com.zhixianghui.service.trade.biz.OfflineOrderBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName OfflineOrderFeeListener
 * @Description TODO
 * @Date 2022/12/1 9:47
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_OFFLINE_ORDER_FEE,
        selectorExpression = MessageMsgDest.TAG_OFFLINE_ORDER_FEE,consumeThreadMax = 5,
        consumerGroup = "offlineOrderFeeConsumer")
public class OfflineOrderFeeListener extends BaseRocketMQListener<String> {
    @Autowired
    private OfflineOrderBiz offlineOrderBiz;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        offlineOrderBiz.generateOrderFee(jsonParam);
    }
}
