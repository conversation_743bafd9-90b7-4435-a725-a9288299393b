package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.trade.entity.AcRechargeAccount;
import com.zhixianghui.service.trade.dao.AcRechargeAccountDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 充值账户信息biz业务实现类
 *
 * <AUTHOR> @date 2024-04-03
 */
@Service
public class AcRechargeAccountBiz {

    @Autowired
    private AcRechargeAccountDao acRechargeAccountDao;

    //新增充值账户信息
    public void insert(AcRechargeAccount acRechargeAccount) {
        acRechargeAccountDao.insert(acRechargeAccount);
    }

    //修改充值账户信息
    public void update(AcRechargeAccount acRechargeAccount) {
        acRechargeAccountDao.update(acRechargeAccount);
    }

    public void updateAccountInfo(Long accountId, String accountNo, String accountName) {
        AcRechargeAccount account = acRechargeAccountDao.getById(accountId);
        if (StringUtil.isNotEmpty(accountNo)) {
            account.setAccountNo(accountNo);
        }
        account.setAccountName(accountName);
        acRechargeAccountDao.updateIfNotNull(account);
    }

    //通过id查看充值账户信息
    public AcRechargeAccount getById(Long id) {
        return acRechargeAccountDao.getById(id);
    }

    public List<AcRechargeAccount> listBy(Map<String, Object> paramMap) {
        return acRechargeAccountDao.listBy(paramMap);
    }

    public AcRechargeAccount getOne(Map<String, Object> paramMap) {
        return acRechargeAccountDao.getOne(paramMap);
    }

    public void checkExistOrInsert(AcRechargeAccount model) {
        BeanMap map = BeanMap.create(model);
        long count = acRechargeAccountDao.countBy(map);
        if (count <= 0) {
            model.setCreateTime(new Date());
            acRechargeAccountDao.insert(model);
        }
    }

    public long getCountByModel(AcRechargeAccount model) {
        BeanMap map = BeanMap.create(model);
        return acRechargeAccountDao.countBy(map);
    }
}
