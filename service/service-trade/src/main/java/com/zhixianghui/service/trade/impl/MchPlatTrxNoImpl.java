package com.zhixianghui.service.trade.impl;

import com.zhixianghui.service.trade.biz.MchPlatTrxNoBiz;
import com.zhixianghui.facade.trade.service.MchPlatTrxNoFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 商户平台订单映射 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-06
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MchPlatTrxNoImpl implements MchPlatTrxNoFacade {

    private final MchPlatTrxNoBiz biz;
}
