package com.zhixianghui.service.trade.listener;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.trade.dto.FlowSignDto;
import com.zhixianghui.service.trade.process.FlowSignBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/18 15:37
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FLOW_SIGN, selectorExpression = MessageMsgDest.TAG_FLOW_SIGN, consumeThreadMax = 1, consumerGroup = MessageMsgDest.SIGN_GROUP)
public class FlowSignListener extends BaseRocketMQListener<String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(FlowSignListener.class);
    @Autowired
    private FlowSignBiz flowSignBiz;

    @Override
    public void validateJsonParam(String param) {

    }

    @Override
    public void consumeMessage(String param) {
        if (StringUtils.isBlank(param)) {
            return;
        }
        try {
            FlowSignDto flowSignDto = JSONObject.parseObject(param, FlowSignDto.class);
            validateParam(flowSignDto);
            flowSignBiz.sign(flowSignDto);
        } catch (Exception e) {
            LOGGER.error("[{}]线上签署流程发生异常: {}",  e.getMessage(), param, e);
        }
    }

    private void validateParam(FlowSignDto flowSignDto) {
        if (flowSignDto == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("序列化对象不能为空");
        }
//        if (StringUtils.isBlank(flowSignDto.getCommitFlowId())) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("流程id不能为空");
//        }
        if (StringUtils.isBlank(flowSignDto.getFileUrl())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件链接不能为空");
        }
        if (StringUtils.isBlank(flowSignDto.getMainstayNo())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体编号不能为空");
        }
        if (StringUtils.isBlank(flowSignDto.getMerchantNo())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户编号不能为空");
        }
    }
}
