package com.zhixianghui.service.trade.pay.wx.biz;

import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.service.trade.biz.WxMerchantBalanceBiz;
import com.zhixianghui.service.trade.factory.TradeFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月16日 10:51:00
 */
@Slf4j
@Service
public class WxOrderUpdateBiz {

    @Autowired
    private WxMerchantBalanceBiz wxMerchantBalanceBiz;

    @Autowired
    private TradeFactory tradeFactory;

    @Autowired
    private RecordItemBiz recordItemBiz;

    public void updateOrder(ChangesFunds changesFunds, PayReceiveRespVo payReceiveRespVo){
        updateChangesFunds(changesFunds);
        RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(payReceiveRespVo.getBankOrderNo());
        tradeFactory.getGrantor(recordItem.getProductNo()).handleNotify(payReceiveRespVo,recordItem);
    }

    public void updateChangesFunds(ChangesFunds changesFunds){
        try{
            wxMerchantBalanceBiz.changeAmount(changesFunds);
        }catch (DuplicateKeyException e){
            log.info("[{}]订单金额已扣减，直接更新订单状态并通知商户",changesFunds.getPlatTrxNo());
        }
    }
}
