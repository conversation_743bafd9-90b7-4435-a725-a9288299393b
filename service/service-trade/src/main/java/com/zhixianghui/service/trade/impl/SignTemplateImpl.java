package com.zhixianghui.service.trade.impl;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.sign.ChannelSignFacade;
import com.zhixianghui.facade.banklink.utils.esign.ESignHelpUtil;
import com.zhixianghui.facade.banklink.utils.esign.HeaderConstant;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.components.ComponentsResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.CreateTemplateResV3DataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.EditTemplateResV3DataVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.facade.trade.entity.TemplateComponent;
import com.zhixianghui.facade.trade.service.SignTemplateFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.trade.entity.SignTemplate;
import com.zhixianghui.facade.trade.vo.DocTemplateAddOrEditVo;
import com.zhixianghui.facade.trade.vo.MerchantInfo;
import com.zhixianghui.facade.trade.vo.SignCustomizeTemplateVo;
import com.zhixianghui.facade.trade.vo.SignTemplateResVo;
import com.zhixianghui.service.trade.biz.SignTemplateBiz;
import com.zhixianghui.service.trade.biz.TemplateComponentBiz;
import com.zhixianghui.service.trade.config.RetryConfig;
import com.zhixianghui.service.trade.enums.TemplateFileStatusEnum;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 模板id表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-06-07
 */
@Slf4j
// 调用e签报接口存在慢的情况, 这里设置了9s
@Service(timeout = 9000)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SignTemplateImpl implements SignTemplateFacade {

    @Reference
    private ChannelSignFacade channelSignFacade;
    @Reference
    private MerchantFacade merchantFacade;

    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;

    @Autowired
    private FastdfsClient fastdfsClient;
    //
    private static final String TEMPLATE_FILE_PATH = "/tmp/";
    private static final String PDF_SUFFIX = ".pdf";

    private final SignTemplateBiz biz;
    private final TemplateComponentBiz componentBiz;
    private static final int SUCCESS_CODE = 0;

    @Value("${sign.templateCreateUrl}")
    private String templateCreateUrl;

    @Override
    public DocTemplateAddOrEditVo addTemplate(SignCustomizeTemplateVo templateVo) {
        DocTemplateAddOrEditVo docTemplateAddOrEditVo = new DocTemplateAddOrEditVo();


        if (CollectionUtils.isEmpty(templateVo.getMchNoList()) && CollectionUtils.isEmpty(templateVo.getEmployerList())
                && CollectionUtils.isEmpty(templateVo.getMerchantList())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户id列表不能为空");
        }
        if (CollectionUtils.isEmpty(templateVo.getMchNoList())) {
            if (CollectionUtils.isEmpty(templateVo.getEmployerList())) {
                templateVo.setMchNoList(
                        templateVo.getMerchantList().stream().map(MerchantInfo::getMchNo).collect(Collectors.toList())
                        );
            } else {
                templateVo.setMchNoList(
                        templateVo.getEmployerList().stream().map(MerchantInfo::getMchNo).collect(Collectors.toList())
                        );
            }
        }
        templateVo.getMchNoList().forEach(item -> {
            if (StringUtils.isNotBlank(merchantFacade.getByMchNo(item).getTemplateId())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户[{" + item + "}]已存在模板id, 不能重复创建");
            }
        });
        String path = TEMPLATE_FILE_PATH  + templateVo.getTemplateName() + PDF_SUFFIX;
        templateVo.setLocalUrl(path);
        File file = FileUtils.createFile(path);
        InputStream inputStream = fastdfsClient.downloadFile(templateVo.getTargetFileUrl());
        try {
            org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
        } catch (IOException e) {
            log.error("文件下载失败:" + templateVo.getTargetFileUrl());
        }
        if (!file.exists()) {
            log.error("文件路径不存在:" + templateVo.getLocalUrl());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件路径不存在");
        }
        byte[] buffer = FileUtils.fileToByteArray(path);
        // 获取请求头
        CreateSignTemplateReqVo reqVo = buildRequestHead(templateVo, file.length(), file.getName());
        // 预创建
        CreateSignTemplateResV3DataVo createSignTemplateResDataVo = preCreate(templateVo, reqVo);
        // 上传
        uploadESign(templateVo, reqVo, createSignTemplateResDataVo, buffer);
        // 查询模板是否可用
//        try {
//            existTemplate(new TemplateStatus(createSignTemplateResDataVo.getFileId()), createSignTemplateResDataVo);
//        } catch (ExecutionException | RetryException e) {
//            log.error("重试查询文件异常:" + e);
//            return docTemplateAddOrEditVo;
//        }

        List<String> signerRoles = Arrays.asList("甲方签署区","乙方签署区");

        CreateTemplateReqVo createTemplateReqVo = new CreateTemplateReqVo();
        createTemplateReqVo.setDocTemplateName(templateVo.getTemplateName())
                .setDocTemplateType(0)
                .setRedirectUrl(templateCreateUrl)
                .setFileId(createSignTemplateResDataVo.getFileId())
                .setSignerRoles(signerRoles)
                .setCustomComponents(initTemplateCustomComponents());

        EsignResVo<CreateTemplateResV3DataVo> template = channelSignFacade.createTemplate(createTemplateReqVo);
        if (!Objects.equals(template.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(template.getMessage());
        }

        String docTemplateId = template.getData().getDocTemplateId();
        docTemplateAddOrEditVo.setDocTemplateId(docTemplateId)
                .setDocTemplateCreateLongUrl(template.getData().getDocTemplateCreateLongUrl())
                .setDocTemplateCreateUrl(template.getData().getDocTemplateCreateUrl());
        // 添加组件
//        TemplateComponentsResDataVo componentsList = addComponent(templateVo, createSignTemplateResDataVo);
        // 添加模板
        SignTemplate signTemplate = build(docTemplateAddOrEditVo, new TemplateComponentsResDataVo(), templateVo);


        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        try {
            biz.add(signTemplate);
            if (CollectionUtils.isEmpty(templateVo.getMchNoList())) {
                log.warn("没有选择用工企业或代征主体");
                return docTemplateAddOrEditVo;
            }
            // 将模板id更新到商户表
            merchantFacade.updateTemplateId(docTemplateId, templateVo.getMchNoList());
        } catch (Exception e) {
            log.error("模板添加失败: {},{}", JSONObject.toJSON(templateVo), JSONObject.toJSON(signTemplate), e);
            platformTransactionManager.rollback(transaction);
            return docTemplateAddOrEditVo;
        }
        platformTransactionManager.commit(transaction);
        CompletableFuture.runAsync(() -> {
            try {
                FileUtils.deleteDir(file);
            } catch (Exception e) {
                log.error("删除文件失败:" + e);
            }
        });
        return docTemplateAddOrEditVo;
    }

    private List<String> initTemplateCustomComponents() {

        List<TemplateComponent> templateComponents = componentBiz.componentList().stream().filter(x->!ObjectUtil.equal(x.getType(),6)).collect(Collectors.toList());

        EsignPageVo esignPageVo  = new EsignPageVo();
        esignPageVo.setPageNum(1);
        esignPageVo.setPageSize(100);
        //查询控件
        EsignResVo<ComponentsResDataVo> componentsReqVoEsignResVo = channelSignFacade.customComponentsList(esignPageVo);
        //为空 或者不相等 初始化控件
        if(ObjectUtil.isEmpty(componentsReqVoEsignResVo.getData())||!ObjectUtil.equal(componentsReqVoEsignResVo.getData().getTotal(),templateComponents.size())){
             if(ObjectUtil.isNotEmpty(componentsReqVoEsignResVo.getData())&&componentsReqVoEsignResVo.getData().getTotal()>0){
            //删除原有控件
                List<ComponentsResDataVo.Components> components = componentsReqVoEsignResVo.getData().getComponents();
                List<String> collect = components.stream().map(ComponentsResDataVo.Components::getComponentId).collect(Collectors.toList());
                DelComponentReqVo delComponentReqVo = new DelComponentReqVo()
                        .setComponentId(collect);
                channelSignFacade.delComponentsList(delComponentReqVo);
            }
            //创建组件
            CreateComponentReqVo reqVo = new CreateComponentReqVo();
            List<ComponentsResDataVo.Components> collect = templateComponents.stream().map(x -> {
                JSONObject jsonObject = JSONObject.parseObject(x.getStyle());
                return new ComponentsResDataVo.Components()
                        .setComponentName(x.getLabel())
                        .setCustomBizNum(String.valueOf(x.getId()))
                        .setComponentType(String.valueOf(x.getType()))
                        .setComponentSize(new ComponentsResDataVo.ComponentSize()
                                .setComponentHeight(jsonObject.get("height").toString())
                                .setComponentWidth(jsonObject.get("width").toString()))
                        .setComponentTextFormat(new ComponentsResDataVo.ComponentTextFormat()
                                .setFont(jsonObject.get("font").toString())
                                .setFontSize(jsonObject.get("fontSize").toString())
                                .setTextColor(jsonObject.get("textColor").toString()))
                        .setRequired(true);
            }).collect(toList());
            reqVo.setComponents(collect);

            channelSignFacade.createComponentsList(reqVo);
            //二次查询
            componentsReqVoEsignResVo = channelSignFacade.customComponentsList(esignPageVo);
        }
        //设置控件
        return componentsReqVoEsignResVo.getData().getComponents().stream().map(ComponentsResDataVo.Components::getComponentId).collect(Collectors.toList());
    }


    private SignTemplate build(DocTemplateAddOrEditVo docTemplateAddOrEditVo , TemplateComponentsResDataVo componentsList, SignCustomizeTemplateVo templateVo) {
        SignTemplate signTemplate = new SignTemplate();
        signTemplate.setComponentId(JSONObject.toJSONString(componentsList.getComponentIdList()));
        signTemplate.setCreateTime(new Date());
        signTemplate.setUpdateTime(new Date());
        signTemplate.setTemplateId(docTemplateAddOrEditVo.getDocTemplateId());
        signTemplate.setVersion(0);
        signTemplate.setUploadUrl(docTemplateAddOrEditVo.getDocTemplateCreateUrl());
//        if (!CollectionUtils.isEmpty(componentsList.getComponentIdList())) {
//            Assert.isTrue(componentsList.getComponentIdList().size() == templateVo.getStructComponent().size(), "返回的组件id数量需要和组件数量相等");
//             int index = 0;
//            for (StructComponent item : templateVo.getStructComponent()) {
//                item.setId(componentsList.getComponentIdList().get(index));
//                index++;
//            }
//        }
//        log.info("添加id：" + JSONObject.toJSONString(templateVo.getStructComponent()));
//        signTemplate.setStructComponent(JSONObject.toJSONString(templateVo.getStructComponent()));
        signTemplate.setTargetFileUrl(templateVo.getTargetFileUrl());
        signTemplate.setSourceFileUrl(templateVo.getSourceFileUrl());
        signTemplate.setTemplateName(templateVo.getTemplateName());
        signTemplate.setSignTemplateType(templateVo.getSignTemplateType());
        signTemplate.setDeleteFlag(RemovedEnum.NORMAL.getValue());
        return signTemplate;
    }

    private CreateSignTemplateReqVo buildRequestHead(SignCustomizeTemplateVo templateVo, long length, String name) {
        String content = ESignHelpUtil.getStringContentMD5(templateVo.getLocalUrl());
        return new CreateSignTemplateReqVo(
                content, HeaderConstant.CONTENTTYPE_STREAM.getValue(), name, false, length
        );
    }

    private CreateSignTemplateResV3DataVo preCreate(SignCustomizeTemplateVo templateVo, CreateSignTemplateReqVo reqVo) {
        // 获取上传地址
        EsignResVo<CreateSignTemplateResV3DataVo> result = createSignTemplate(reqVo);
        if (result.getCode() != SUCCESS_CODE) {
            log.error("预创建出错 : {}", JSONObject.toJSONString(result));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("预创建出错");
        }
        CreateSignTemplateResV3DataVo createSignTemplateResDataVo = result.getData();
        if (StringUtils.isBlank(createSignTemplateResDataVo.getFileUploadUrl())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板文件预上传出错, 上传地址为空");
        }
        return createSignTemplateResDataVo;
    }

//    private TemplateComponentsResDataVo addComponent(SignCustomizeTemplateVo templateVo, CreateSignTemplateResV3DataVo createSignTemplateResDataVo) {
//        // 添加组件
//        EsignResVo<List<String>> ComponentsResult = channelSignFacade.addTemplate(
//                new TemplateComponentsReqVo(createSignTemplateResDataVo.getFileId(), templateVo.getStructComponent())
//        );
//        TemplateComponentsResDataVo resDataVo = new TemplateComponentsResDataVo();
//        resDataVo.setComponentIdList(ComponentsResult.getData());
//        return resDataVo;
//    }

    private void uploadESign(SignCustomizeTemplateVo templateVo, CreateSignTemplateReqVo reqVo, CreateSignTemplateResV3DataVo createSignTemplateResDataVo, byte[] buffer) {
        UploadFileReqVo uploadFileReqVo = new UploadFileReqVo().build(reqVo, templateVo.getLocalUrl());
        // 上传文件
        boolean success = uploadFile(uploadFileReqVo, createSignTemplateResDataVo.getFileUploadUrl(), buffer);
        if (!success) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板文件上传出错");
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public DocTemplateAddOrEditVo modify(SignCustomizeTemplateVo templateVo,Boolean isChangeFile) {
        String localPath = TEMPLATE_FILE_PATH + templateVo.getTemplateName() + PDF_SUFFIX;
        File file = FileUtils.createFile(localPath);
        InputStream inputStream = fastdfsClient.downloadFile(templateVo.getTargetFileUrl());
        try {
            org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
        } catch (IOException e) {
            log.error("文件修改失败:" + e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件修改失败");
        }
        templateVo.setLocalUrl(localPath);

        SignTemplate signTemplate = biz.get(templateVo.getTemplateId());
        if (signTemplate == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该模板不存在");
        }
//        if (CollectionUtils.isEmpty(templateVo.getStructComponent())) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("组件列表不能为空");
//        }

        // 重新上传文件
        return reCreateAndModify(signTemplate, templateVo,isChangeFile);

//        List<String> list = JSONArray.parseArray(signTemplate.getComponentId(), String.class);
//        if (!CollectionUtils.isEmpty(list)) {
//            List<String> componentList = templateVo.getStructComponent().stream().map(StructComponent::getId).
//                    collect(Collectors.toList());
//            List<String> delete = list.stream().filter(item -> !componentList.contains(item)).collect(toList());
//            if (!CollectionUtils.isEmpty(delete)) {
//                String ids = StringUtils.join(delete, ",");
//                // 删除
//                EsignResVo<?> result = channelSignFacade.del(new DeleteComponentsReqVo(signTemplate.getTemplateId(), ids));
//                if (result.getCode() != SUCCESS_CODE) {
//                    log.error("删除组件失败:" + ids);
//                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("删除组件失败");
//                }
//            }
//        }
//
//        // 修改组件
//        EsignResVo<List<String>> ComponentsResult = channelSignFacade.addTemplate(
//                new TemplateComponentsReqVo(signTemplate.getTemplateId(), templateVo.getStructComponent())
//        );
//        if (ComponentsResult.getCode() != SUCCESS_CODE) {
//            log.error("修改组件失败:" + JSONObject.toJSONString(templateVo.getStructComponent()));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("修改组件失败");
//        }
//        signTemplate.setComponentId(JSONObject.toJSONString(ComponentsResult.getData()));
//        signTemplate.setUpdateTime(new Date());
//        signTemplate.setStructComponent(JSONObject.toJSONString(templateVo.getStructComponent()));
//        signTemplate.setTargetFileUrl(templateVo.getTargetFileUrl());
//        signTemplate.setSourceFileUrl(templateVo.getSourceFileUrl());
//        signTemplate.setTemplateName(templateVo.getTemplateName());
//        signTemplate.setSignTemplateType(templateVo.getSignTemplateType());
//        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
//        try {
//            biz.modify(signTemplate);
//            // 修改商户列表
//            modifyMerchant(signTemplate, templateVo);
//        } catch (Exception e) {
//            log.error("模板修改失败: {}", JSONObject.toJSON(signTemplate), e);
//            platformTransactionManager.rollback(transaction);
//            return false;
//        }
//        platformTransactionManager.commit(transaction);
//        return true;
    }

    private DocTemplateAddOrEditVo reCreateAndModify(SignTemplate signTemplate, SignCustomizeTemplateVo templateVo,Boolean isChangeFile) {
        DocTemplateAddOrEditVo docTemplateAddOrEditVo = new DocTemplateAddOrEditVo();

        if(isChangeFile){
            File file = new File(templateVo.getLocalUrl());
            // 获取请求头
            CreateSignTemplateReqVo reqVo = buildRequestHead(templateVo, file.length(), file.getName());
            // 预创建
            CreateSignTemplateResV3DataVo createSignTemplateResDataVo = preCreate(templateVo, reqVo);
            // 上传
            byte[] buffer = FileUtils.fileToByteArray(templateVo.getLocalUrl());
            uploadESign(templateVo, reqVo, createSignTemplateResDataVo, buffer);
            // 查询模板是否可用
//        try {
//            existTemplate(new TemplateStatus(createSignTemplateResDataVo.getFileId()), createSignTemplateResDataVo);
//        } catch (ExecutionException | RetryException e) {
//            log.error("重试查询文件异常:" + e);
//            return docTemplateAddOrEditVo;
//        }

            List<String> signerRoles = Arrays.asList("甲方签署区","乙方签署区");

            CreateTemplateReqVo createTemplateReqVo = new CreateTemplateReqVo();
            createTemplateReqVo.setDocTemplateName(templateVo.getTemplateName())
                    .setDocTemplateType(0)
                    .setRedirectUrl(templateCreateUrl)
                    .setFileId(createSignTemplateResDataVo.getFileId())
                    .setSignerRoles(signerRoles)
                    .setCustomComponents(initTemplateCustomComponents());


            EsignResVo<CreateTemplateResV3DataVo> template = channelSignFacade.createTemplate(createTemplateReqVo);
            if (!Objects.equals(template.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(template.getMessage());
            }

            createSignTemplateResDataVo.setFileId(template.getData().getDocTemplateId());
            docTemplateAddOrEditVo.setDocTemplateId(template.getData().getDocTemplateId())
                    .setDocTemplateCreateUrl(template.getData().getDocTemplateCreateUrl())
                    .setDocTemplateCreateLongUrl(template.getData().getDocTemplateCreateLongUrl());
            // 添加组件
//        TemplateComponentsResDataVo componentsList = addComponent(templateVo, createSignTemplateResDataVo);
            // 删除原来模板,添加新模板
            SignTemplate newTemplate = build(docTemplateAddOrEditVo, new TemplateComponentsResDataVo(), templateVo);
            TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
            try {
                del(signTemplate.getId());
                biz.add(newTemplate);
                modifyMerchant(signTemplate, newTemplate, templateVo);
            } catch (Exception e) {
                platformTransactionManager.rollback(transaction);
                log.error("重新创建文件异常:" + e);
                return docTemplateAddOrEditVo;
            }
            platformTransactionManager.commit(transaction);
            CompletableFuture.runAsync(() -> {
                try {
                    FileUtils.deleteDir(file);
                } catch (Exception e) {
                    log.error("删除文件失败:" + e);
                }
            });
        }else{
            EditTemplateReqVo editTemplateReqVo = new EditTemplateReqVo();
            editTemplateReqVo.setDocTemplateId(signTemplate.getTemplateId())
                    .setRedirectUrl(templateCreateUrl)
                    .setSignerRoles(Arrays.asList("甲方签署区","乙方签署区"))
                    .setCustomComponents(initTemplateCustomComponents());
            EsignResVo<EditTemplateResV3DataVo> template = channelSignFacade.editTemplate(editTemplateReqVo);

            docTemplateAddOrEditVo.setDocTemplateId(signTemplate.getTemplateId())
                    .setDocTemplateCreateUrl(template.getData().getDocTemplateEditUrl())
                    .setDocTemplateCreateLongUrl(template.getData().getDocTemplateEditUrl());
            SignTemplate signTemplate1 = new SignTemplate();
            signTemplate1.setTemplateId(signTemplate.getTemplateId());
            modifyMerchant(signTemplate, signTemplate1, templateVo);
        }


        return docTemplateAddOrEditVo;
    }

//    private void existTemplate(TemplateStatus reqVo, CreateSignTemplateResV3DataVo createSignTemplateResDataVo) throws ExecutionException, RetryException {
//
//        Retryer<Boolean> retryer = new RetryConfig<Boolean>().retryConfig(3, 1, 5, TimeUnit.SECONDS);
//        //定义请求实现
//        Callable<Boolean> callable = () -> {
//            EsignResVo<TemplateStatusResDataVo> result = channelSignFacade.existTemplate(reqVo);
//            if (result.getData() == null) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板状态查询异常");
//            }
//            if (StringUtils.isNotBlank(result.getData().getTemplateFileStatus()) &&
//                    TemplateFileStatusEnum.SUCCESS.getStatus() == Integer.parseInt(result.getData().getTemplateFileStatus())) {
//                return true;
//            }
//            log.error("模板创建未完成 : {}", JSONObject.toJSON(result.getData()));
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板创建未完成");
//        };
//        retryer.call(callable);
//    }


    private void modifyMerchant(SignTemplate oldSignTemplate, SignTemplate newSignTemplate, SignCustomizeTemplateVo templateVo) {
        if (CollectionUtils.isEmpty(templateVo.getMchNoList())) {
            if (CollectionUtils.isEmpty(templateVo.getEmployerList())) {
                templateVo.setMchNoList(
                        templateVo.getMerchantList().stream().map(MerchantInfo::getMchNo).collect(Collectors.toList())
                );
            } else {
                templateVo.setMchNoList(
                        templateVo.getEmployerList().stream().map(MerchantInfo::getMchNo).collect(Collectors.toList())
                );
            }
        }
        List<Merchant> merchantList = merchantFacade.getByTemplateId(oldSignTemplate.getTemplateId(), oldSignTemplate.getSignTemplateType());
        List<String> delete = merchantList.stream().map(Merchant :: getMchNo).collect(toList());
        if (!CollectionUtils.isEmpty(delete)) {
            merchantFacade.updateTemplateId("", delete);
        }

        if (CollectionUtils.isEmpty(templateVo.getMchNoList())) {
            return;
        }

        merchantFacade.updateTemplateId(newSignTemplate.getTemplateId(), templateVo.getMchNoList());
    }

    @Override
    public PageResult<List<SignTemplateResVo>> list(SignCustomizeTemplateVo queryVo) {
        Map<String, Object> map = BeanUtil.toMap(queryVo);
        if (!CollectionUtils.isEmpty(queryVo.getMchNo())) {
            List<String> templateIdList = merchantFacade.listTemplateIdByMchNo(queryVo.getMchNo());
            map.put("idList", templateIdList);
        }
        PageResult<List<SignTemplate>> result = biz.list(map, queryVo.getPageParam());
        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            return null;
        }
        List<SignTemplate> signTemplateList = result.getData();
        List<SignTemplateResVo> resVoList = new ArrayList<>();
        for (SignTemplate signTemplate : signTemplateList) {
            SignTemplateResVo signTemplateResVo = new SignTemplateResVo();
            BeanUtil.copyProperties(signTemplate, signTemplateResVo);
            List<Merchant> merchantList = merchantFacade.getByTemplateId(signTemplate.getTemplateId(), signTemplate.getSignTemplateType());
            signTemplateResVo.setEmployerList(merchantList.stream().
                    filter(item -> item.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()).
                    map(item -> new MerchantInfo(item.getMchNo(), item.getMchName(), item.getMerchantType())).
                    collect(toList()));
            signTemplateResVo.setMerchantList(merchantList.stream().
                    filter(item -> item.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()).
                    map(item -> new MerchantInfo(item.getMchNo(), item.getMchName(), item.getMerchantType())).
                    collect(toList()));
            resVoList.add(signTemplateResVo);
        }
        return PageResult.newInstance(
                PageParam.newInstance(result.getPageCurrent(), result.getPageSize()),
                resVoList
        );
    }


    @Override
    public boolean del(Long id) {
        TransactionStatus transaction = platformTransactionManager.getTransaction(transactionDefinition);
        SignTemplate signTemplate = biz.get(id);
        if (signTemplate == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约模板不存在");
        }
        try {
            merchantFacade.delTemplateId(signTemplate.getTemplateId());
            signTemplate.setId(id);
            signTemplate.setDeleteFlag(RemovedEnum.REMOVED.getValue());
            biz.del(signTemplate);
        } catch (Exception e) {
            platformTransactionManager.rollback(transaction);
            log.error("删除异常" + e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("删除异常");
        }
        platformTransactionManager.commit(transaction);
        return true;
    }

    public EsignResVo<CreateSignTemplateResV3DataVo> createSignTemplate(CreateSignTemplateReqVo vo) {
        return channelSignFacade.createSignTemplate(vo);
    }

    public boolean uploadFile(UploadFileReqVo uploadFileReqVo, String uploadUrl, byte[] buffer) {
        return channelSignFacade.uploadFile(uploadFileReqVo, uploadUrl, buffer);
    }

    @Override
    public List<TemplateComponent> componentList() {
        return componentBiz.componentList();

    }
}
