package com.zhixianghui.service.trade.impl;

import com.zhixianghui.service.trade.biz.MchPlatBatchBiz;
import com.zhixianghui.facade.trade.service.MchPlatBatchFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.dubbo.config.annotation.Service;
import lombok.RequiredArgsConstructor;

/**
 * 商户平台批次映射 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-06
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MchPlatBatchImpl implements MchPlatBatchFacade {

    private final MchPlatBatchBiz biz;
}
