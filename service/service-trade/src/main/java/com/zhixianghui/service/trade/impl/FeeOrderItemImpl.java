package com.zhixianghui.service.trade.impl;

import com.zhixianghui.facade.trade.entity.FeeOrderItem;
import com.zhixianghui.facade.trade.service.FeeOrderItemFacade;
import com.zhixianghui.service.trade.biz.FeeOrderItemBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2022-07-05
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FeeOrderItemImpl implements FeeOrderItemFacade {

    private final FeeOrderItemBiz biz;

    @Override
    public void update(FeeOrderItem feeOrderItem) {
        biz.update(feeOrderItem);
    }

    @Override
    public Boolean feeStatusCheckByTimeRang(String startTime, String endTime, String employerNo, String mainstayNo,Integer feeType){
        return biz.feeStatusCheckByTimeRang(startTime, endTime, employerNo, mainstayNo, feeType);
    }
}
