package com.zhixianghui.service.trade.biz;

import com.google.common.collect.Lists;
import com.zhixianghui.facade.trade.entity.FeeOrderItem;
import com.zhixianghui.facade.trade.enums.FeeOrderItemStatusEnum;
import com.zhixianghui.service.trade.dao.FeeOrderItemDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2022-07-05
*/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FeeOrderItemBiz {

    private final FeeOrderItemDao feeOrderItemDao;


    public FeeOrderItem getByItemNo(String feeOrderItemNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("feeItemNo",feeOrderItemNo);
        return feeOrderItemDao.getOne(paramMap);
    }

    public void update(FeeOrderItem feeOrderItem) {
        feeOrderItemDao.update(feeOrderItem);
    }

    public List<FeeOrderItem> getListByBatchNo(String bankBatchNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("feeBatchNo",bankBatchNo);
        return feeOrderItemDao.listBy(paramMap);
    }

    public Boolean feeStatusCheckByTimeRang(String startTime, String endTime, String employerNo,String mainstayNo,Integer feeType) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", employerNo);
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("beginDate", startTime);
        paramMap.put("endDate", endTime);
        paramMap.put("feeType", feeType);
        paramMap.put("statusList", Lists.newArrayList(FeeOrderItemStatusEnum.NO_PAY, FeeOrderItemStatusEnum.PAYING));
        final long count = feeOrderItemDao.countBy(paramMap);
        if (count > 0) {
            return false;
        }else {
            return true;
        }
    }
}
