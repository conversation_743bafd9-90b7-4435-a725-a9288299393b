package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.banklink.service.auth.PayAuthFacade;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.trade.entity.AuthRecord;
import com.zhixianghui.facade.trade.vo.auth.AuthResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.AUTH_AGREEMENT_SEQ;
import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.AUTH_RECORD_SEQ;

@Service
@Slf4j
public class AuthBiz {

    @Autowired
    private AuthRecordBiz authRecordBiz;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private PayAuthFacade payAuthFacade;

    public AuthResponseVo auth(String name, String idCardNo, String phone, String bankAccountNo, int authType,String authChannel) {
        //先判断本地的鉴权表是否有记录
        List<AuthRecord> authRecords = authRecordBiz.listByElementActiveRecordWithType(name,idCardNo,bankAccountNo,phone, AuthTypeEnum.getEnum(authType));
        if(!ObjectUtils.isEmpty(authRecords)){
            log.info("本地数据库鉴权成功");
            AuthResponseVo authResponseVo = new AuthResponseVo();
            authResponseVo.setAuthStatus(BankAuthStatusEnum.SUCCESS.getValue());
            authResponseVo.setBizCode("AU000000");
            authResponseVo.setBizDesc("鉴权成功");
            return authResponseVo;
        }
        //通道鉴权
        AuthRespVo authRespVo;
        String authNo;
        String serialNo;
        try{
            authNo = sequenceFacade.nextRedisId(AUTH_RECORD_SEQ.getPrefix(),AUTH_RECORD_SEQ.getKey(), AUTH_RECORD_SEQ.getWidth());
            log.info("[请求汇聚鉴权号:{}] ", authNo);
            //用户授权协议流水号
            serialNo = sequenceFacade.nextRedisId(AUTH_AGREEMENT_SEQ.getPrefix(),AUTH_AGREEMENT_SEQ.getKey(),AUTH_AGREEMENT_SEQ.getWidth());
            AuthReqVo authReqVo = new AuthReqVo();
            authReqVo.setBankOrderNo(authNo);
            authReqVo.setAuthType(authType);
            authReqVo.setName(name);
            authReqVo.setBankAccountNo(bankAccountNo);
            authReqVo.setIdCardNo(idCardNo);
            authReqVo.setSerialNo(serialNo);
            authReqVo.setAuthChannel(authChannel);
            authReqVo.setPhoneNo(phone);
            authRespVo = payAuthFacade.auth(authReqVo);
        } catch (Exception e){
            AuthResponseVo authResponseVo = new AuthResponseVo();
            authResponseVo.setAuthStatus(BankAuthStatusEnum.SYSTEM_ERROR.getValue());
            authResponseVo.setBizCode("AU000001");
            authResponseVo.setBizDesc("鉴权未知异常");
            return authResponseVo;
        }
        Integer authStatus = authRespVo.getAuthStatus();

        // 成功、未知通过此次鉴权 | 成功的才记录,失败未知不记录
        if(Objects.equals(authStatus, BankAuthStatusEnum.SUCCESS.getValue())){
            log.info("[鉴权号{} 鉴权成功] ", authNo);
            // 异步保存，失败也不影响业务
            AuthRecord record =  new AuthRecord();
            record.setAuthNo(authNo);
            record.setCreateTime(new Date());
            record.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(idCardNo));
            record.setReceiveAccountNoMd5(MD5Util.getMixMd5Str(bankAccountNo));
            record.setReceiveNameMd5(MD5Util.getMixMd5Str(name));
            record.setAuthType(authType);
            record.setProtocolVersion(authRespVo.getProtocolVersion());
            record.setProtocolNo(serialNo);
            record.setReceivePhoneNoMd5(MD5Util.getMixMd5Str(phone));
            record.setAuthChannel(authRespVo.getChannelNo());
            authRecordBiz.insertAsync(record);
        }else if(Objects.equals(authStatus,BankAuthStatusEnum.SYSTEM_ERROR.getValue())||Objects.equals(authStatus,BankAuthStatusEnum.UN_KNOW.getValue())){
            log.info("[鉴权号-{}] 鉴权失败，biz_code: {}, biz_desc:{}", authNo, authRespVo.getBizCode(),authRespVo.getBizDesc());
            AuthResponseVo authResponseVo = new AuthResponseVo();
            authResponseVo.setAuthStatus(BankAuthStatusEnum.UN_KNOW.getValue());
            authResponseVo.setBizCode(authRespVo.getBizCode());
            authResponseVo.setBizDesc(authRespVo.getBizDesc());
            return authResponseVo;
        }
        else if(Objects.equals(authStatus, BankAuthStatusEnum.FAIL.getValue())){
            log.info("[鉴权号-{}] 鉴权失败，biz_code: {}, biz_desc:{}", authNo, authRespVo.getBizCode(),authRespVo.getBizDesc());
            AuthResponseVo authResponseVo = new AuthResponseVo();
            authResponseVo.setAuthStatus(BankAuthStatusEnum.FAIL.getValue());
            authResponseVo.setBizCode(authRespVo.getBizCode());
            authResponseVo.setBizDesc(authRespVo.getBizDesc());
            return authResponseVo;
        }
        AuthResponseVo authResponseVo = new AuthResponseVo();
        authResponseVo.setAuthStatus(BankAuthStatusEnum.SUCCESS.getValue());
        authResponseVo.setBizCode("AU000000");
        authResponseVo.setBizDesc("鉴权成功");
        return authResponseVo;
    }

}
