package com.zhixianghui.service.trade.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.entity.Order;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单批次表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Repository
public class OrderDao extends MyBatisDao<Order,Long> {

    public BigDecimal sumWaitInvoiceAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("sumWaitInvoiceAmount"),paramMap);
    }

    public BigDecimal sumWaitCkhInvoiceAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("sumWaitCkhInvoiceAmount"),paramMap);
    }

    public BigDecimal sumWaitCepGrantInvoiceAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("sumWaitCepGrantInvoiceAmount"),paramMap);
    }

    public BigDecimal sumWaitCepServiceFeeInvoiceAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("sumWaitCepServiceFeeInvoiceAmount"),paramMap);
    }

    public List<FeeOrderBatch> selectFeeOrder(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("selectFeeOrder"),paramMap);
    }

    public List<String> selectOrderNo(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("selectOrderNo"),paramMap);
    }

    public Order getNotCompleteOrder(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("getNotCompleteOrder"),paramMap);
    }
}
