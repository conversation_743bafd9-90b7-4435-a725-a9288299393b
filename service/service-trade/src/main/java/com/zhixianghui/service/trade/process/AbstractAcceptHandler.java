package com.zhixianghui.service.trade.process;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.exception.QpsExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.enums.HemaPayTypeEnum;
import com.zhixianghui.facade.banklink.service.hema.HemaFacade;
import com.zhixianghui.facade.banklink.service.yishui.YishuiFacade;
import com.zhixianghui.facade.banklink.vo.hema.PaymentReqVo;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.yishui.YiResponse;
import com.zhixianghui.facade.banklink.vo.yishui.YishuiAddEmpVo;
import com.zhixianghui.facade.banklink.vo.yishui.YishuiContractInfoVo;
import com.zhixianghui.facade.banklink.vo.yishui.YishuiContractListVo;
import com.zhixianghui.facade.banklink.vo.yishui.YishuiFastIssuing;
import com.zhixianghui.facade.banklink.vo.yishui.req.AddBankVo;
import com.zhixianghui.facade.banklink.vo.yishui.req.AddEmpVo;
import com.zhixianghui.facade.banklink.vo.yishui.req.ContractListQueryVo;
import com.zhixianghui.facade.banklink.vo.yishui.req.FastIssuingVo;
import com.zhixianghui.facade.banklink.vo.yishui.req.IssuingData;
import com.zhixianghui.facade.banklink.vo.yishui.req.Pagination;
import com.zhixianghui.facade.banklink.vo.yishui.req.RequestVo;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.BankCardBinFacade;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.merchant.entity.MerchantNotifySet;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.enums.MerchantNotifySetTypeEnum;
import com.zhixianghui.facade.merchant.service.MerchantNotifySetFacade;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.bo.OrderItemCountBo;
import com.zhixianghui.facade.trade.bo.OrderItemTrxIdBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.factory.TradeFactory;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.service.trade.helper.TradeNotifyBiz;
import com.zhixianghui.service.trade.utils.BuildVoUtil;
import com.zhixianghui.service.trade.utils.RedisRateLimiter;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AbstractAcceptHandler
 * @Description TODO
 * @Date 2022/6/28 18:35
 */
@Slf4j
public abstract class AbstractAcceptHandler implements AcceptInterface{

    @Autowired
    protected OrderBiz orderBiz;

    @Autowired
    protected OrderItemBiz orderItemBiz;

    @Autowired
    protected RedisClient redisClient;

    @Autowired
    protected RedisRateLimiter redisRateLimiter;

    @Reference
    protected EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    protected EmployerMainstayRelationFacade employerMainstayRelationFacade;

    @Reference
    protected YishuiFacade yishuiFacade;

    @Reference
    protected NotifyFacade notifyFacade;

    @Autowired
    protected TradeHelperBiz tradeHelperBiz;

    @Reference
    protected MerchantSecretFacade merchantSecretFacade;

    @Autowired
    protected TradeNotifyBiz tradeNotifyBiz;

    @Autowired
    protected TradeFactory tradeFactory;

    @Reference
    protected BankCardBinFacade bankCardBinFacade;

    @Reference
    protected DataDictionaryFacade dictionaryFacade;

    @Reference
    protected MerchantNotifySetFacade merchantNotifySetFacade;

    @Reference
    protected HemaFacade hemaFacade;

    /**
     * 初始化受理
     * @param order 批次订单
     */
    public void initAccept(Order order) {
        log.info("[受理环节: {} ]==>初始化受理开始,解析统计数据入库", order.getPlatBatchNo());
        if(!Objects.equals(OrderStatusEnum.IMPORTING.getValue(), order.getBatchStatus())){
            log.info("[受理环节: {}]==>批次状态为{}, 中止受理", order.getPlatBatchNo(), order.getBatchStatus());
            return;
        }
        try {
            //将解析excel时的请求笔数等统计信息更新进数据库
            orderBiz.update(order);
            //加一次统计 避免量少 请求笔数未入库时就受理完 导致请求数与受理数对比失败
            updateAcceptBatchCount(order.getPlatBatchNo());
            log.info("[受理环节: {} ]==>初始化受理结束,解析统计数据已入库", order.getPlatBatchNo());
        } catch (Exception e) {
            log.error("[受理环节: {}]==>更新批次表失败",order.getPlatBatchNo(), e);
        }
    }

    public void startAccept(String employerNo, String platBatchNo, List<String> platTrxNoList) {
        if(ObjectUtils.isEmpty(platTrxNoList)) {
            return;
        }
        log.info("[受理环节: {}] -- mchNo:[{}] ==>批次开始受理。platTrxNoList size:{},明细范围[{}]至[{}]"
                , platBatchNo, employerNo,platTrxNoList.size(), platTrxNoList.get(0),platTrxNoList.get(platTrxNoList.size() - 1));

        // 查询该批次单
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        Order order = orderBiz.getOne(paramMap);
        if(order == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次订单不存在");
        }
        //判断相等 不能用Objects.equals 隐藏了错误 null时返回false
        if(order.getBatchStatus() == OrderStatusEnum.CLOSED_GRANT.getValue()){
            log.info("[受理环节: {}] 批次已关闭,不再进行受理",platBatchNo);
            return;
        }
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(order.getEmployerNo(),order.getMainstayNo(),order.getChannelType());
        checkInfo(employerAccountInfo,order);

        //处理每一笔订单明细
        platTrxNoList.forEach(
                platTrxNo->{
                    OrderItem item = orderItemBiz.getByPlatTrxNo(platTrxNo);
                    if (!Objects.equals(item.getOrderItemStatus(), OrderItemStatusEnum.CREATE.getValue())) {
                        log.warn("受理环节：{}-{}==>订单非已创建状态，不予受理", platBatchNo, platTrxNo);
                        return;
                    }
                    log.info("[受理环节: {}]==>订单明细开始受理", item.getPlatTrxNo());
                    Long acceptTimes = getAcceptTimes(platTrxNo);
                    try {
                        //如果是招行，对卡bin进行校验
                        if (StringUtils.equals(item.getPayChannelNo() , ChannelNoEnum.CMB.name())) {
                            BankCardBin cardBinByCardNo = bankCardBinFacade.getCardBinByCardNo(item.getReceiveAccountNoDecrypt());
                            if (cardBinByCardNo == null) {
                                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("{}::{}", ApiExceptions.API_BIZ_FAIL.getApiErrorCode(), "卡bin校验失败"));
                            }
                        }

                        // 算费
                        calculateFee(item);
                        // 鉴权
                        processAuth(item);
//                        //同步到供应商（易税供应商专用）
//                        sendToMainstay(item);
//                        //同步签约信息（易税供应商专用）
//                        sign(item);
                        //同步到供应商（河马专用）
                        sendToHema(item,acceptTimes);
                        //更新状态为已受理状态
                        item.setUpdateTime(new Date());
                        item.setOrderItemStatus(OrderItemStatusEnum.ACCEPTED.getValue());
                    } catch (BizException e) {
                        log.info("[受理环节: {}]==>受理业务失败，原因：{}", item.getPlatTrxNo(), e.getErrMsg());
                        if (e.getApiErrorCode() == ApiExceptions.API_TRADE_AUTH_EXCEPTION.getApiErrorCode()
                                || e.getApiErrorCode() == QpsExceptions.QPS_NEED_BLOCK_EX.getApiErrorCode()) {
                            if (acceptTimes < TradeConstant.MAX_ACCESS_TIMES + 1) {
                                log.info("[受理环节: {}==> 鉴权异常进行重试],异常信息：{}", item.getPlatTrxNo(), e.getErrMsg());
                                // 受理失败补偿mq
                                notifyHandlerException(item.getPlatTrxNo());
                            } else {
                                log.info("[受理环节: {}==> 重试到达上限 停止重试]", item.getPlatTrxNo());
                                BuildVoUtil.fillOrderItemAcceptFail(item, e.getApiErrorCode(), StringUtils.isBlank(e.getErrMsg()) ? "要素认证情况未知" : e.getErrMsg());
                                orderItemBiz.update(item);
                            }
                            return;
                        }

                        BuildVoUtil.fillOrderItemAcceptFail(item, e.getApiErrorCode(), e.getErrMsg());
                        orderItemBiz.update(item);
                        return;
                    }catch (Exception e) {
                        log.error("[受理环节: {}]==>受理系统异常，原因：", item.getPlatTrxNo(), e);
                        if(acceptTimes < TradeConstant.MAX_ACCESS_TIMES + 1){
                                log.error("[受理环节: {"+item.getPlatTrxNo()+"}==> 未知系统异常进行重试]",e);
                                // 受理失败补偿mq
                                notifyHandlerException(item.getPlatTrxNo());
                        }else{
                            log.warn("[受理环节: {}==> 重试到达上限 停止重试]",item.getPlatTrxNo());
                            BuildVoUtil.fillOrderItemAcceptFail(item, "", "要素认证情况未知");
                            orderItemBiz.update(item);
                        }
                        return;
                    }
                    // 更新
                    orderItemBiz.update(item);
                }
        );
        // 发送统计MQ
        notifyAcceptBatchCount(platBatchNo);
    }

    private void sendToHema(OrderItem item,Long acceptTimes) throws Exception{
        String hemaMaintayNo = dictionaryFacade.getSystemConfig("HEMA_MAINSTAY_NO");
        List<String> hemaMainstayNos = new ArrayList<>();
        if (StringUtils.isNotBlank(hemaMaintayNo)) {
             hemaMainstayNos = ListUtil.toList(hemaMaintayNo.split(","));
        }
        if (hemaMainstayNos.size() > 0 && hemaMainstayNos.contains(item.getMainstayNo())){
            //把订单推送到河马
            PaymentReqVo paymentReqVo = new PaymentReqVo();
            paymentReqVo.setThirdOrderId(item.getPlatTrxNo());
            paymentReqVo.setThirdBizOrderId(item.getPlatTrxNo());
            paymentReqVo.setPayeeName(item.getReceiveNameDecrypt());
            paymentReqVo.setPayeeIdCard(item.getReceiveIdCardNoDecrypt());
            paymentReqVo.setPayeeAccount(item.getReceiveAccountNoDecrypt());
            paymentReqVo.setAmount(item.getOrderItemNetAmount());
            paymentReqVo.setExtNeedCallBack("0");
            paymentReqVo.setPayType(HemaPayTypeEnum.getEnumByChannelType(item.getChannelType()).getValue());
            hemaFacade.payment(paymentReqVo);
        }
    }

    private void checkInfo(EmployerAccountInfo employerAccountInfo, Order order) {
        if (employerAccountInfo == null) {
            log.info("[受理环节: {}] ==>没有用工企业帐号对应通道配置 EmployerNo:[{}] MainstayNo:[{}] ChannelType:[{}]",order.getPlatBatchNo(), order.getEmployerNo(),order.getMainstayNo(),order.getChannelType());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业帐号信息对应通道没有配置");
        }
        if (employerAccountInfo.getStatus().equals(OpenOffEnum.OFF.getValue())) {
            log.info("[受理环节: {}] ==> 用工企业帐号对应通道配置为关闭",order.getPlatBatchNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业帐号对应通道配置为关闭");
        }

        if(employerAccountInfo.getChannelType() != ChannelTypeEnum.WENXIN.getValue() &&
                !employerAccountInfo.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())){
            if (StringUtils.isBlank(employerAccountInfo.getParentMerchantNo()) || StringUtils.isBlank(employerAccountInfo.getSubMerchantNo())) {
                log.info("[受理环节: {}] ==> 用工企业帐号对应通道配置的父子商编号为空",order.getPlatBatchNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用工企业帐号对应通道配置为空");
            }
        }
    }

    private Long getAcceptTimes(String platTrxNo) {
        Long acceptTime = 0L;
        try {
            acceptTime = redisClient.incr(TradeConstant.ACCEPT_TIME_REDIS_PREFIX + platTrxNo);
            redisClient.expire(TradeConstant.ACCEPT_TIME_REDIS_PREFIX + platTrxNo,60*60);
        }catch (Exception e){
            log.error("[受理环节: {}] ==> redis获取重试次数异常 忽略", platTrxNo, e);
        }
        return acceptTime;
    }

    public void sign(OrderItem orderItem) {
        String payChannelNo = orderItem.getPayChannelNo();
        //如果易税接口，需要跟易税进行用户信息同步
        if (StringUtils.equals(ChannelNoEnum.YISHUI.name(), payChannelNo)) {
            String idCard = orderItem.getReceiveIdCardNoDecrypt();
            log.info("未在易税签约用户表查询到，转到易税接口查询:idcard:{}",idCard);
            EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(orderItem.getEmployerNo(), orderItem.getMainstayNo());
            String mainstayNo = orderItem.getMainstayNo();

            RequestVo<AddEmpVo> requestVo = new RequestVo<>();
            requestVo.setEnterprise_sn(employerMainstayRelation.getExternalEnterpriseSn());
            requestVo.setUser_name(employerMainstayRelation.getEmployerNo());

            AddEmpVo addEmpVo = new AddEmpVo();
            addEmpVo.setProtocol_img("")
                    .setCer_code(orderItem.getReceiveIdCardNoDecrypt())
                    .setHas_auth("1")
                    .setName(orderItem.getReceiveNameDecrypt())
                    .setMobile(orderItem.getReceivePhoneNoDecrypt())
                    .setBank_code(orderItem.getReceiveAccountNoDecrypt())
                    .setContract_img("")
                    .setProtocol_img("");
            requestVo.setParam(addEmpVo);

            YiResponse<YishuiAddEmpVo> yishuiAddEmpVoYiResponse = yishuiFacade.addEmployee(requestVo, orderItem.getChannelType());
            if (yishuiAddEmpVoYiResponse.getCode() != 200) {
                if (StringUtils.equals("该身份证已经新增过", yishuiAddEmpVoYiResponse.getMsg())) {
                    RequestVo<ContractListQueryVo> contractListQueryRequestVo = new RequestVo<>();
                    contractListQueryRequestVo.setUser_name(employerMainstayRelation.getExternalUserName())
                            .setEnterprise_sn(employerMainstayRelation.getExternalEnterpriseSn())
                            .setPassword(employerMainstayRelation.getExternalPasswordDecrypt());
                    ContractListQueryVo contractListQueryVo = new ContractListQueryVo();
                    contractListQueryVo.setPagination(new Pagination().setPage_size("2").setPage_start("1"))
                            .setKeyword(orderItem.getReceiveIdCardNoDecrypt());
                    contractListQueryRequestVo.setParam(contractListQueryVo);
                    YiResponse<YishuiContractListVo> yishuiContractListVoYiResponse = yishuiFacade.contractList(contractListQueryRequestVo);
                    if (yishuiContractListVoYiResponse.getCode() != 200 && !StringUtils.equals("暂无数据",yishuiContractListVoYiResponse.getMsg())) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商处查询签约信息失败:"+yishuiContractListVoYiResponse.getMsg());
                    } else {
                        YishuiContractListVo data = yishuiContractListVoYiResponse.getData();
                        if (data != null && data.getList().size() > 0) {
                            for (Map<String, Object> map : data.getList()) {
                                String enterpriseProfessionalFacilitatorId = String.valueOf(map.get("enterprise_professional_facilitator_id"));
                                YiResponse<YishuiContractInfoVo> contractInfoVoYiResponse = yishuiFacade.contractInfo(new RequestVo<String>()
                                        .setUser_name(employerMainstayRelation.getExternalUserName())
                                        .setEnterprise_sn(employerMainstayRelation.getExternalEnterpriseSn())
                                        .setParam(enterpriseProfessionalFacilitatorId));
                                YishuiContractInfoVo contractInfoVo = contractInfoVoYiResponse.getData();

                                String professionalId = contractInfoVo.getProfessional_id();
                                //添加银行卡
                                this.addBank(orderItem, employerMainstayRelation, professionalId);
                            }
                        }
                    }
                    return;
                }
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("{}与供应商{}签约失败-{}", orderItem.getReceiveIdCardNoDecrypt(), mainstayNo, yishuiAddEmpVoYiResponse));
            }
            this.addBank(orderItem, employerMainstayRelation, yishuiAddEmpVoYiResponse.getData().getProfessional_id());
        }
    }

    public void sendToMainstay(OrderItem orderItem) {
        String payChannelNo = orderItem.getPayChannelNo();

        EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(orderItem.getEmployerNo(), orderItem.getMainstayNo());

        //如果易税接口，需要跟易税进行用户信息同步
        if (StringUtils.equals(ChannelNoEnum.YISHUI.name(), payChannelNo)) {
            RequestVo<FastIssuingVo> fastIssuingVoRequestVo = new RequestVo<>();
            FastIssuingVo fastIssuingVo = new FastIssuingVo();
            fastIssuingVo.setTrade_number(orderItem.getPlatTrxNo())
                    .setIssuing_data(Lists.newArrayList(
                            new IssuingData().setRequest_no(orderItem.getPlatTrxNo())
                                    .setName(orderItem.getReceiveNameDecrypt())
                                    .setBank_code(orderItem.getReceiveAccountNoDecrypt())
                                    .setMoney(orderItem.getOrderItemNetAmount().toPlainString())
                                    .setRemark(orderItem.getRemark())
                                    .setCer_code(orderItem.getReceiveIdCardNoDecrypt())
                    ));
            fastIssuingVoRequestVo.setParam(fastIssuingVo)
                    .setEnterprise_sn(relation.getExternalEnterpriseSn());
            YiResponse<YishuiFastIssuing> yishuiFastIssuingYiResponse = yishuiFacade.fastIssuing(fastIssuingVoRequestVo);

            if (yishuiFastIssuingYiResponse.getCode() != 200) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("{}", yishuiFastIssuingYiResponse.getMsg()));
            }
        }
    }

    public void handleAcceptException(String platTrxNo) {
        log.info("[受理环节: {}]==>开始处理受理异常订单", platTrxNo);
        OrderItem item = orderItemBiz.getByPlatTrxNo(platTrxNo);
        if(!Objects.equals(item.getOrderItemStatus(), OrderItemStatusEnum.CREATE.getValue())){
            return;
        }
        // 发送受理mq
        log.info("[受理环节: {}]==>发送重新受理mq", platTrxNo);
        notifyAcceptStart(item.getEmployerNo(), item.getPlatBatchNo(), Lists.newArrayList(item.getPlatTrxNo()));
    }

    /**
     * 更新批次表受理统计信息
     *
     * @param platBatchNo 批次订单编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAcceptBatchCount(String platBatchNo){
        log.info("[受理环节: {}]==>更新批次表受理统计信息", platBatchNo);
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        Order order = orderBiz.getOne(paramMap);
        if(order == null ||!Objects.equals(OrderStatusEnum.IMPORTING.getValue(), order.getBatchStatus())){
            return;
        }

        //如果请求数还未入库不进行统计
        if(order.getRequestCount().equals(0)){
            return;
        }
        OrderItemCountBo resultBo = orderItemBiz.getCountBoByPlatBatchNo(platBatchNo);

        log.info("[受理环节: {}]==>受理统计信息resultVo：{}", platBatchNo, JSON.toJSONString(resultBo));

        order.setUpdateTime(new Date());
        BeanUtils.copyProperties(resultBo,order);
        boolean isAccessComplete = order.getRequestCount().equals(resultBo.getAcceptedCount() + resultBo.getFailCount());
        // 受理完成
        if (isAccessComplete) {
            log.info("[受理环节: {}]==>批次受理完成", platBatchNo);
            if (resultBo.getAcceptedCount() == 0) {
                // 全部受理失败，状态为关闭
                order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
                order.setCompleteTime(new Date());
                //通知商户
                notifyMchAcceptFailResult(order);
            } else {
                //接口实时发放，受理完直接发起发放流程
                if(Objects.equals(LaunchWayEnum.API.getValue(), order.getLaunchWay())){
                    order.setConfirmTime(order.getConfirmTime() == null ? order.getUpdateTime(): order.getConfirmTime());
                    order.setBatchStatus(OrderStatusEnum.GRANTING.getValue());
                    tradeFactory.getGrantor(order.getProductNo()).initGrant(order);
                } else {
                    order.setBatchStatus(OrderStatusEnum.PENDING_GRANT.getValue());
                }
            }
            try {
                orderBiz.update(order);
            }catch (Exception e){
                log.error("[受理环节: {}]==>受理完成统计更新数据库异常 mq进行重试 errMsg:{}",platBatchNo,e);
                throw CommonExceptions.COMMON_RETRY_ERROR;
            }
            log.info("[受理环节: {}]==>批次受理完成 成功更新批次状态", platBatchNo);
            return;
        }

        log.info("[受理环节: {}]==>order: {}", platBatchNo, JsonUtil.toString(order));
        orderBiz.update(order);
    }

    public void notifyMchAcceptFailResult(Order order) {
        try{
            if (StringUtils.isNotEmpty(order.getCallbackUrl())){
                String cacheKey = TradeConstant.ACCEPT_FAIL_NOTIFY_MCH_REDIS_PREFIX + order.getPlatBatchNo();
                if(redisClient.get(cacheKey) == null) {
                    //缓存五分钟，期间不会再通知商户
                    redisClient.set(cacheKey, order.getPlatBatchNo(), 300);
                    List<OrderItem> itemList = orderItemBiz.listBy(order.getPlatBatchNo());
                    MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, itemList.get(0));
                    tradeNotifyBiz.notifyMerchant(merchantNotifyParam,order);
                    log.info("[{}-{}]异步受理失败，通知商户发放结果", order.getEmployerNo(), order.getMchBatchNo());
                }
            }else{
                String mchNo = order.getEmployerNo();
                final MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(mchNo);

                if (merchantSecret == null) {
                    return;
                }

                MerchantNotifySet merchantNotifySet = merchantNotifySetFacade.getByMchNoAndType(mchNo, MerchantNotifySetTypeEnum.GRANT_COMPLETE_NOTIFY.getValue());
                if (merchantNotifySet == null){
                    return;
                }


                String notifyUrl = merchantNotifySet.getNotifyUrl();
                Integer notifyStatus = merchantNotifySet.getNotifyStatus();
                if (StringUtils.isNotBlank(notifyUrl) && notifyStatus == CommonStatusEnum.ACTIVE.getValue()) {
                    List<OrderItem> itemList = orderItemBiz.listBy(order.getPlatBatchNo());
                    itemList.forEach(item->{
                        String cacheKey = TradeConstant.GRANT_NOTIFY_MCH_BATCH_REDIS_PREFIX + order.getPlatBatchNo()+":"+item.getPlatTrxNo();
                        if(redisClient.get(cacheKey) == null) {
                            //缓存五分钟，期间不会再通知商户
                            redisClient.set(cacheKey, item.getPlatTrxNo(), 300);

                            Order.JsonEntity jsonEntity = new Order.JsonEntity();
                            jsonEntity.setSignType("1");
                            order.setJsonEntity(jsonEntity);

                            MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, item);
                            merchantNotifyParam.setNotifyUrl(notifyUrl);
                            tradeNotifyBiz.notifyMerchantBatch(merchantNotifyParam,item);
                            log.info("[{}-{}]异步受理失败，通知商户发放结果", order.getEmployerNo(), item.getPlatTrxNo());
                        }
                    });
                }
            }
        }catch (Exception e){
            log.info("[{}-{}]异步受理失败通知商户异常", order.getEmployerNo(), order.getMchBatchNo(), e);
            throw CommonExceptions.COMMON_RETRY_ERROR;
        }
    }

    private void addBank(OrderItem orderItem,EmployerMainstayRelation employerMainstayRelation, String professionalId){
        RequestVo<AddBankVo> addBankRequestVo = new RequestVo<>();
        addBankRequestVo.setUser_name(employerMainstayRelation.getExternalUserName())
                .setEnterprise_sn(employerMainstayRelation.getExternalEnterpriseSn())
                .setPassword(employerMainstayRelation.getExternalPasswordDecrypt());

        addBankRequestVo.setParam(new AddBankVo()
                .setBank_type("1")
                .setBank_code(orderItem.getReceiveAccountNoDecrypt())
                .setName(orderItem.getReceiveNameDecrypt())
                .setMobile(orderItem.getReceivePhoneNoDecrypt())
                .setProfessional_id(professionalId));
        YiResponse<String> addBankResp = yishuiFacade.addBank(addBankRequestVo);
        if (addBankResp.getCode() != 200&&!StringUtils.equals(addBankResp.getMsg(), "该收款账户已经存在")) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[供应商返回]"+addBankResp.getMsg());
        }
    }

    /**
     * 重新受理(数据库读)
     * @param order 批次订单
     */
    public void acceptAgain(Order order) {
        log.info("[受理环节: {} ]==>数据库分批通知受理开始", order.getPlatBatchNo());
        if(!Objects.equals(OrderStatusEnum.IMPORTING.getValue(), order.getBatchStatus())){
            log.info("[受理环节: {}]==>批次状态为{}, 中止受理", order.getPlatBatchNo(), order.getBatchStatus());
            return;
        }

        // 分批发放
        PageParam pageParam = PageParam.newInstance(1, TradeConstant.SELECT_NUM_PER_GROUP,"ID ASC");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", order.getPlatBatchNo());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.CREATE.getValue());
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);
        PageResult<List<OrderItemTrxIdBo>> pageResult;
        List<OrderItemTrxIdBo> recordList;

        do {
            pageResult = orderItemBiz.listTrxNoIdPage(paramMap, pageParam);
            recordList = pageResult.getData();
            if(!ObjectUtils.isEmpty(recordList)){

                List<OrderItemTrxIdBo> notifyList = Lists.newArrayList();
                recordList.forEach(
                        trxIdBo->{
                            notifyList.add(trxIdBo);
                            if(notifyList.size() >= TradeConstant.NOTIFY_NUM_PER_GROUP){
                                //满NOTIFY_NUM_PER_GROUP 发一次
                                notifyAcceptStart(order.getEmployerNo(), order.getPlatBatchNo(),
                                        notifyList.stream().map(OrderItemTrxIdBo::getPlatTrxNo).collect(Collectors.toList()));
                                notifyList.clear();
                            }
                        }
                );
                //发送剩余的不满NOTIFY_COUNT的
                if(!ObjectUtils.isEmpty(notifyList)){
                    notifyAcceptStart(order.getEmployerNo(), order.getPlatBatchNo(),
                            notifyList.stream().map(OrderItemTrxIdBo::getPlatTrxNo).collect(Collectors.toList()));
                    notifyList.clear();
                }

                paramMap.put("overId", recordList.get(recordList.size()-1).getId());
            }
        } while (!ObjectUtils.isEmpty(recordList));
        log.info("[受理环节: {} ]==>分批通知受理结束", order.getPlatBatchNo());
    }

    /**
     * 鉴权
     * @param item 订单明细
     */
    private void processAuth(OrderItem item) {
        // 获取银行卡卡bin信息（对所有类型都先获取，包括银行卡）
        if(Objects.equals(item.getChannelType(), ChannelTypeEnum.BANK.getValue())) {
            log.info("[受理环节: {}]==>获取银行卡卡bin信息", item.getPlatTrxNo());
            BankCardBin bankCardBin = bankCardBinFacade.getCardBinByCardNo(item.getReceiveAccountNoDecrypt());
            if(bankCardBin == null){
                log.info("[受理环节: {}]==>获取银行卡卡bin信息为空,忽略", item.getPlatTrxNo());
                item.setBankName("");
                item.setBankCode("");
            }else{
                item.setBankName(bankCardBin.getBankName());
                item.setBankCode(bankCardBin.getBankCode());
            }
        }
        
        //二要素商户不需要鉴权
        String twoElementMerchant = dictionaryFacade.getSystemConfig("TWO_ELEMENT_GRANT");
        if (StringUtils.isNotBlank(twoElementMerchant)) {
            ArrayList<String> merchantConfig = ListUtil.toList(twoElementMerchant.split(","));
            if (merchantConfig.contains(item.getEmployerNo())){
                return;
            }
        }

        // 银行卡鉴权
        if(Objects.equals(item.getChannelType(), ChannelTypeEnum.BANK.getValue())){
            log.info("[受理环节: {}]==>订单明细开始鉴权", item.getPlatTrxNo());
            String phoneMustMainstayConfig = dictionaryFacade.getSystemConfig("PHONE_MUST_MAINSTAY");
            List<String> phoneMustMainstay = null;
            if (StringUtils.isNotBlank(phoneMustMainstayConfig)) {
                phoneMustMainstay = ListUtil.toList(phoneMustMainstayConfig.split(","));
            }

            if (StringUtils.isNotBlank(item.getReceivePhoneNoMd5())&&phoneMustMainstay!=null&&phoneMustMainstay.contains(item.getMainstayNo())) {
                log.info("[受理环节：{}]==>四要素鉴权", item.getPlatTrxNo());
                tradeHelperBiz.processBankCardAuth(item, AuthTypeEnum.IDCARD_NAME_BANKCARD_PHONE);
            }else {
                tradeHelperBiz.processBankCardAuth(item, AuthTypeEnum.IDCARD_NAME_BANKCARD);
            }
        } else if (Objects.equals(item.getChannelType(), ChannelTypeEnum.ALIPAY.getValue())) {
            log.info("[受理环节：{}]==> 支付宝鉴权",item.getPlatTrxNo());
            tradeHelperBiz.processBankCardAuth(item, AuthTypeEnum.IDCARD_NAME);
        }else if (Objects.equals(item.getChannelType(), ChannelTypeEnum.WENXIN.getValue())) {
            log.info("[受理环节：{}]==> 微信鉴权",item.getPlatTrxNo());
            tradeHelperBiz.processBankCardAuth(item, AuthTypeEnum.IDCARD_NAME);
        }
    }
}
