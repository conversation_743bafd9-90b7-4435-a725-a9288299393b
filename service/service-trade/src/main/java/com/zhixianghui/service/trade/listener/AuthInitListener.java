package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月25日 10:24:00
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AUTH_INIT, selectorExpression = MessageMsgDest.TAG_AUTH_INIT,
        consumeThreadMax = 5, consumerGroup = "ahthInitConsumer")
public class AuthInitListener extends BaseRocketMQListener<String> {

    @Autowired
    private OrderItemBiz orderItemBiz;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String platBatchNo) {
        orderItemBiz.authInit(platBatchNo);
    }
}