package com.zhixianghui.service.trade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.dto.OfflineInvoiceApplyDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.facade.trade.vo.InvoiceEditVo;
import com.zhixianghui.facade.trade.vo.InvoiceUpdateVo;
import com.zhixianghui.service.trade.biz.InvoiceRecordBiz;
import com.zhixianghui.service.trade.process.InvoiceHandleBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * 订单批次表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InvoiceFacadeImpl implements InvoiceFacade {

    private final InvoiceRecordBiz invoiceRecordBiz;
    private final InvoiceHandleBiz invoiceHandleBiz;


    @Override
    public InvoiceRecord getByTrxNo(String trxNo) {
        return invoiceRecordBiz.getByTrxNo(trxNo);
    }

    @Override
    public PageResult<List<InvoiceRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return invoiceRecordBiz.listPage(paramMap, pageParam);
    }

    @Override
    public void updateStatus(InvoiceUpdateVo vo) {
        invoiceHandleBiz.updateStatus(vo);
    }

    @Override
    public void updateInvoiceRecord(InvoiceEditVo vo) {
        invoiceHandleBiz.updateInvoiceRecord(vo);
    }

    @Override
    public String getApplyTradeCompleteDayBegin(String employerMchNo, String mainstayMchNo,Integer category,String jobId,
                                                Integer source,String workCategoryCode, String productNo, Integer amountType) {
        return invoiceHandleBiz.getApplyTradeCompleteDayBegin(employerMchNo, mainstayMchNo, category, jobId, source,
                workCategoryCode,productNo,amountType);
    }

    @Override
    public void applyInvoice(InvoiceRecord record,String jobId) {
        invoiceHandleBiz.applyInvoice(record, jobId);
    }

    @Override
    public void applyInvoiceOrderItem(InvoiceRecord record,String jobId) {
        invoiceHandleBiz.applyInvoiceOrderItem(record,jobId);
    }

    @Override
    public Map<String, Object> countInvoiceAmount(Map<String, Object> paramMap) {
        return invoiceRecordBiz.countInvoiceAmount(paramMap);
    }

    @Override
    public IPage<InvoiceDetailGroupByIdCardVo> listInvoiceDetailGroupByIdCard(Page<InvoiceDetailGroupByIdCardVo> page, String invoiceTrxNo) throws BizException {
        return invoiceHandleBiz.listInvoiceDetailGroupByIdCard(page,invoiceTrxNo);
    }

    @Override
    public String applyOffline(OfflineInvoiceApplyDto applyDto) throws BizException{
        return invoiceHandleBiz.applyOffline(applyDto);
    }
}
