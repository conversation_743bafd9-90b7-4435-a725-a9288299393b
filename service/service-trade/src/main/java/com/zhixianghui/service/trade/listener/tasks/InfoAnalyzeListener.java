package com.zhixianghui.service.trade.listener.tasks;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.service.MerchantSalerFacade;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.MerchantStat;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.service.trade.biz.FreelanceStatBiz;
import com.zhixianghui.service.trade.biz.MerchantStatBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.SignRecordBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/8/11 16:36
 */
@Slf4j
@Component
public class InfoAnalyzeListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(InfoAnalyzeListener.class);

    @Autowired
    private OrderItemBiz orderBiz;
    @Autowired
    private MerchantStatBiz merchantBiz;
    @Autowired
    private FreelanceStatBiz freelanceBiz;
    @Autowired
    private SignRecordBiz signRecordBiz;
    @Reference
    private MerchantSalerFacade merchantSaleFacade;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_LAST_MONTH_MERCHANT_INFO_ANALYZE, consumeThreadMax = 1, consumerGroup = "lastMonthMerchantInfoAnalyzeHandler")
    public class LastMonthMerchantAnalyze extends TaskRocketMQListener<JSONObject> {

        @Override
        public void runTask(JSONObject jsonParam) {
            // 以当前月为基准, 统计上个月 yyyy-MM-dd
            String startDate = DateUtil.formatDate(DateUtil.getFirstDayOfLastMonth(new Date()));
            String endDate = DateUtil.formatDate(DateUtil.getLastDayOfLastMonth(new Date()));
            LOGGER.info("统计上个月企业数据: {}-{}", startDate, endDate);
            initMerchant(startDate, endDate);
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MERCHANT_INFO_ANALYZE, consumeThreadMax = 1, consumerGroup = "merchantInfoAnalyzeHandler")
    public class MerchantAnalyze extends TaskRocketMQListener<JSONObject> {

        @Override
        public void runTask(JSONObject jsonParam) {
            LOGGER.info("请求参数: {}", JSONObject.toJSONString(jsonParam));
            if (!jsonParam.containsKey("startDate") || !jsonParam.containsKey("endDate") ) {
                initMerchant(null, null);
                return;
            }
            String startDate = jsonParam.getString("startDate");
            String endDate = jsonParam.getString("endDate");
            initMerchant(startDate, endDate);
        }
    }

    public void initMerchant(String startDate, String endDate) {
        Map<String, Object> param = new HashMap<>();
        initParam(param, startDate, endDate);
        log.info("initMerchant查询参数:{}", JSONUtil.toJsonPrettyStr(param));
        List<MerchantStat> result = orderBiz.merchantStat(param);
        if (result != null) {
            log.info(JSONUtil.toJsonPrettyStr(result));
        }
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        for (MerchantStat merchantStat : result) {
            merchantStat.setVersion(0);
            merchantStat.setCreateTime(new Date());
            merchantStat.setUpdateTime(new Date());

            MerchantSaler merchantSaler = merchantSaleFacade.getByMchNo(merchantStat.getEmployerNo());
            merchantStat.setSaleId(merchantSaler.getSalerId());
            merchantStat.setSaleName(merchantSaler.getSalerName());

            //查询首次发放时间
            OrderItem orderItem = orderBiz.getEarlestOrderItem(merchantStat.getEmployerNo());
            merchantStat.setFirstOrderTime(orderItem.getCompleteTime());
            try {
                merchantBiz.insert(merchantStat);
            } catch (Exception e) {
                log.error("自由职业者数据插入失败: {}", JSONObject.toJSON(merchantStat), e);
            }

        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_LAST_MONTH_FREELANCE_INFO_ANALYZE, consumeThreadMax = 1, consumerGroup = "lastMonthFreelanceInfoAnalyzeHandler")
    public class LastMonthFreelanceAnalyze extends TaskRocketMQListener<JSONObject> {

        @Override
        public void runTask(JSONObject jsonParam) {
            String startDate = DateUtil.formatDate(DateUtil.getFirstDayOfLastMonth(new Date()));
            String endDate = DateUtil.formatDate(DateUtil.getLastDayOfLastMonth(new Date()));
            LOGGER.info("统计上个月企业数据: {}-{}", startDate, endDate);
            initFreelanceStat(startDate, endDate);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_FREELANCE_INFO_ANALYZE, consumeThreadMax = 1, consumerGroup = "freelanceInfoAnalyzeHandler")
    public class FreelanceAnalyze extends TaskRocketMQListener<JSONObject> {

        @Override
        public void runTask(JSONObject jsonParam) {
            LOGGER.info("请求参数: {}", JSONObject.toJSONString(jsonParam));
            if (!jsonParam.containsKey("startDate") || !jsonParam.containsKey("endDate") ) {
                initFreelanceStat(null, null);
                return;
            }
            String startDate = jsonParam.getString("startDate");
            String endDate = jsonParam.getString("endDate");
            initFreelanceStat(startDate, endDate);
        }
    }

    public void initFreelanceStat(String startDate, String endDate) {
        Map<String, Object> param = new HashMap<>();
        initParam(param, startDate, endDate);
        LOGGER.info("自由职业者数据入参: {}",JSONObject.toJSONString(param));
        List<FreelanceStat> list = orderBiz.freelanceStat(param);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        LOGGER.info("自由职业者数据: {},{}", list.size(), JSONArray.toJSONString(list));
        for (FreelanceStat stat : list) {
            param.put("receiveIdCardNoMd5", stat.getReceiveIdCardNoMd5());
            Long orderCount = orderBiz.countOrder(param);
            stat.setReceiverOrder(orderCount);
            SignRecord signRecord = signRecordBiz.getOne(new HashMap<String, Object>() {{
                put("receiveNameMd5", stat.getReceiveNameMd5());
                put("receiveIdCardNoMd5", stat.getReceiveIdCardNoMd5());
                put("mainstayNo", stat.getMainstayNo());
                put("employerNo", stat.getEmployerNo());
            }});
            LOGGER.info("签约数据: {}", JSONObject.toJSONString(signRecord));
            if (signRecord == null) {
                stat.setSignRecord(OperationEnum.UN_SIGN.getOperation());
                stat.setIdCard(OperationEnum.UN_UPLOAD_ID_CARD.getOperation());
                continue;
            }
            if (signRecord.getSignStatus() != SignStatusEnum.SIGN_SUCCESS.getValue()) {
                stat.setSignRecord(OperationEnum.UN_SIGN.getOperation());
            }
            if (StringUtils.isAnyBlank(signRecord.getIdCardBackUrl(), signRecord.getIdCardFrontUrl())) {
                stat.setIdCard(OperationEnum.UN_UPLOAD_ID_CARD.getOperation());
                stat.setSignId(signRecord.getId());
                if (signRecord.getSignStatus() == SignStatusEnum.SIGN_SUCCESS.getValue()) {
                    stat.setSignRecord(OperationEnum.SIGN.getOperation());
                }
                continue;
            }
            stat.setSignRecord(OperationEnum.SIGN.getOperation());
            stat.setIdCard(OperationEnum.UPLOAD_ID_CARD.getOperation());
            stat.setSignId(signRecord.getId());
        }
        for (FreelanceStat freelanceStat : list) {
            freelanceStat.setCreateTime(new Date());
            freelanceStat.setVersion(0);
            freelanceStat.setUpdateTime(new Date());
            try {
                freelanceBiz.insert(freelanceStat);
            } catch (Exception e) {
                log.error("自由职业者数据插入失败: {}", JSONObject.toJSON(freelanceStat), e);
            }
        }
    }

    public static void main(String[] args) {
        Date endTime = DateUtil.addDay(DateUtil.getDayEnd(new Date()), -1);
        Date beginTime = DateUtil.getDayStart(DateUtil.getFirstOfMonth(endTime));
        System.out.println(beginTime);
        System.out.println(endTime);
        Date lastEndDate = DateUtil.getLastOfMonth(DateUtil.getLastMonth(endTime));
        Date lastBeginDate = DateUtil.getFirstOfMonth(DateUtil.getLastMonth(beginTime));
        System.out.println(lastBeginDate);
        System.out.println(lastEndDate);
    }
    private void initParam(Map<String, Object> param, String startDate, String endDate) {
        if (StringUtils.isAnyBlank(startDate, endDate)) {
            Date endTime = DateUtil.addDay(DateUtil.getDayEnd(new Date()), -1);
            Date beginTime = DateUtil.getDayStart(DateUtil.getFirstOfMonth(endTime));
            param.put("beginDate", beginTime);
            param.put("endDate", endTime);
            param.put("currentDate", DateUtil.formatShorterDate(endTime));
            param.put("completeDate", endTime);

            Date lastEndDate = DateUtil.getLastOfMonth(DateUtil.getLastMonth(endTime));
            Date lastBeginDate = DateUtil.getFirstOfMonth(DateUtil.getLastMonth(beginTime));
            param.put("lastBeginDate", lastBeginDate);
            param.put("lastEndDate", lastEndDate);
            param.put("lastDate", DateUtil.formatShorterDate(lastEndDate));
            return;
        }

        Date endTime = DateUtil.getLastOfMonth(DateUtil.parse(startDate));
        Date beginTime = DateUtil.getFirstOfMonth(DateUtil.parse(endDate));
        param.put("beginDate", beginTime);
        param.put("endDate", endTime);
        param.put("completeDate", endTime);
        param.put("currentDate", DateUtil.formatShorterDate(endTime));

        Date lastEndDate = DateUtil.getLastOfMonth(DateUtil.getLastMonth(endTime));
        Date lastBeginDate = DateUtil.getFirstOfMonth(DateUtil.getLastMonth(beginTime));
        param.put("lastBeginDate", lastBeginDate);
        param.put("lastEndDate", lastEndDate);
        param.put("lastDate", DateUtil.formatShorterDate(lastEndDate));
    }
}
