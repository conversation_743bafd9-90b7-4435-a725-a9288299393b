package com.zhixianghui.service.trade.biz;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.trade.dto.ChangesFoundsDTO;
import com.zhixianghui.facade.trade.entity.AcChangeFunds;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.vo.ChangesFundsVo;
import com.zhixianghui.service.trade.dao.AcChangeFundsDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 * 账户资金变动biz业务实现类
 * <AUTHOR> @date 2024-04-01
 */
@Service
public class AcChangeFundsBiz {
	
	@Autowired
	private AcChangeFundsDao acChangeFundsDao;
	
	//新增账户资金变动
    public void insert(AcChangeFunds AcChangeFunds) {
        acChangeFundsDao.insert(AcChangeFunds);
	}

    //修改账户资金变动
    public void update(AcChangeFunds AcChangeFunds) {
        acChangeFundsDao.update(AcChangeFunds);
    }

    //通过id查看账户资金变动
    public AcChangeFunds getById(Long id) {
        return acChangeFundsDao.getById(id);
    }
    
	public List<AcChangeFunds> listBy(Map<String, Object> paramMap) {
		return acChangeFundsDao.listBy(paramMap);
	}

    public List<AcChangeFunds> listBy(AcChangeFunds acChangeFunds) {
        return acChangeFundsDao.listBy(BeanUtil.toMap(acChangeFunds));
    }

    public AcChangeFunds getOne(AcChangeFunds acChangeFunds) {
        return acChangeFundsDao.getOne(BeanUtil.toMap(acChangeFunds));
    }

    public AcChangeFunds getByLogKey(String logKey) {
        if (StringUtil.isEmpty(logKey)) {
            return null;
        }
        return acChangeFundsDao.getOne(Collections.singletonMap("logKey", logKey));
    }

    //条件分页查询账户资金变动
//    public PageResult<List<AcChangeFunds>> listPage(Map<String, Object> paramMap, PageQuery pageQuery) {
//		return acChangeFundsDao.listPage(paramMap, pageQuery);
//	}

    //条件分页查询账户资金变动
    public PageResult<List<ChangesFundsVo>> listPage(ChangesFoundsDTO changesFoundsDTO) {
        Map<String, Object> map = BeanUtil.toMap(changesFoundsDTO);
        map.put("payChannelNo", ChannelNoEnum.JOINPAY_JXH.name());

        PageParam pageParam=new PageParam();
        pageParam.setPageSize(changesFoundsDTO.getPageSize());
        pageParam.setPageCurrent(changesFoundsDTO.getPageCurrent());
        PageResult<List<AcChangeFunds>> listPageResult = acChangeFundsDao.listPage(map, pageParam);
        List<AcChangeFunds> data = listPageResult.getData();
        List<ChangesFundsVo> list=data.stream().map(e->{
            ChangesFundsVo changesFundsVo = new ChangesFundsVo();
            changesFundsVo.setLogKey(e.getLogKey());
            changesFundsVo.setMchNo(e.getMchNo());
            changesFundsVo.setMchName(e.getMchName());
            changesFundsVo.setMainstayNo(e.getMainstayNo());
            changesFundsVo.setMainstayName(e.getMainstayName());
            changesFundsVo.setAmount(AmountUtil.changeToYuan(formatNull(e.getAmount())));
            changesFundsVo.setFrozenAmount(AmountUtil.changeToYuan(formatNull(e.getFrozenAmount())));
            changesFundsVo.setPlatTrxNo(e.getPlatTrxNo());
            changesFundsVo.setCreateTime(e.getCreateTime());
            changesFundsVo.setAmountChangeType(e.getAmountChangeType());
            return changesFundsVo;
        }).collect(Collectors.toList());
        PageResult<List<ChangesFundsVo>> page=new PageResult<>();
        page.setTotalRecord(listPageResult.getTotalRecord());
        page.setPageCurrent(listPageResult.getPageCurrent());
        page.setPageSize(listPageResult.getPageSize());
        page.setData(list);
        return page;
    }

    private Long formatNull(Long money){
        return money==null?0L:money;
    }
	
}
