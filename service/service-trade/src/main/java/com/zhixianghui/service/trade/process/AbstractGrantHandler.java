package com.zhixianghui.service.trade.process;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.common.OrderCompleteVo;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.fee.*;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BanklinkExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.service.pay.PayBankFacade;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.pay.QueryPayOrderReqVo;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.fee.service.AgentProductRelationFacade;
import com.zhixianghui.facade.merchant.entity.MerchantNotifySet;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.enums.MerchantNotifySetTypeEnum;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantNotifySetFacade;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import com.zhixianghui.facade.merchant.vo.RelevantAgent;
import com.zhixianghui.facade.merchant.vo.agent.AgentDetailVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.facade.trade.bo.OrderItemCountBo;
import com.zhixianghui.facade.trade.bo.OrderItemTrxIdBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.dto.DeductionCommissionDTO;

import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.*;
import com.zhixianghui.service.trade.biz.*;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import com.zhixianghui.service.trade.helper.TradeNotifyBiz;
import com.zhixianghui.service.trade.utils.BuildVoUtil;
import com.zhixianghui.service.trade.utils.RedisRateLimiter;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.util.ParameterMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AbstractGrantHandler
 * @Description TODO
 * @Date 2022/6/30 10:05
 */
@Slf4j
public abstract class AbstractGrantHandler implements GrantInterface {

    @Reference
    protected PayBankFacade payBankFacade;

    @Autowired
    protected RecordItemBiz recordItemBiz;

    @Autowired
    protected TradeHelperBiz tradeHelperBiz;

    @Autowired
    protected OrderItemBiz orderItemBiz;

    @Reference
    protected DataDictionaryFacade dataDictionaryFacade;

    @Reference
    protected NotifyFacade notifyFacade;

    @Autowired
    protected OrderBiz orderBiz;

    @Reference
    protected EmployerAccountInfoFacade employerAccountInfoFacade;

    @Autowired
    protected RedisClient redisClient;

    @Autowired
    protected RedisLock redisLock;

    @Autowired
    protected CommonBiz commonBiz;

    @Reference
    protected RiskControlFacade riskControlFacade;

    @Autowired
    protected RedisRateLimiter redisRateLimiter;

    @Autowired
    protected DynamicMsgBiz dynamicMsgBiz;

    @Reference
    protected SequenceFacade sequenceFacade;

    @Reference
    protected MerchantFacade merchantFacade;

    @Reference
    protected AgentProductRelationFacade agentProductRelationFacade;

    @Reference
    protected AgentFacade agentFacade;

    @Reference
    protected MerchantSecretFacade merchantSecretFacade;

    @Autowired
    protected TradeNotifyBiz tradeNotifyBiz;

    @Reference
    private CmbFacade cmbFacade;

    @Reference
    private RobotFacade robotFacade;

    @Reference
    private MerchantNotifySetFacade merchantNotifySetFacade;

    @Autowired
    private CmbMerchantBalanceBiz cmbMerchantBalanceBiz;

    public void updateAndInitGrant(Order order) {
        log.info("[发放环节: {}]==>更新并分批通知发放", order.getPlatBatchNo());
        // 更新批次表状态
        order.setUpdateTime(new Date());
        order.setConfirmTime(order.getConfirmTime() == null? order.getUpdateTime(): order.getConfirmTime());
        order.setBatchStatus(OrderStatusEnum.GRANTING.getValue());
        orderBiz.update(order);
        initGrant(order);
//        if (StringUtils.equals(order.getProductNo(), ProductNoEnum.ZXH.getValue()) && StringUtils.equals(order.getPayChannelNo(), ChannelNoEnum.CMB.name())) {
//            // 招行后台发放走批量发放模式
//            JSONObject msg = new JSONObject();
//            msg.put("currentPage", 1);
//            msg.put("platBatchNo", order.getPlatBatchNo());
//            notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_CMB_ASYNC, order.getEmployerNo(), order.getPlatBatchNo(),
//                    NotifyTypeEnum.TRADE_CMB_BATCH_NOTIFY.getValue(), MessageMsgDest.TAG_TRADE_CMB_BATCH_GRANT, msg.toJSONString());
//        } else {
//            initGrant(order);
//        }
    }


    public  void initGrant(Order order){
        log.info("[发放环节: {}]==>分批通知发放", order.getPlatBatchNo());
        if(!Objects.equals(OrderStatusEnum.GRANTING.getValue(), order.getBatchStatus())){
            log.info("[发放环节: {}]==>批次状态为{}, 中止发放", order.getPlatBatchNo(), order.getBatchStatus());
            return;
        }
        // 分批发放
        PageParam pageParam = PageParam.newInstance(1, TradeConstant.SELECT_NUM_PER_GROUP,"ID ASC");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo", order.getPlatBatchNo());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.ACCEPTED.getValue());
//        tradeHelperBiz.putOrderItemCreateDateToMap(paramMap);

        if (StringUtils.equals(order.getPayChannelNo(), ChannelNoEnum.CMB.name())) {
            StringBuilder stringBuilder = new StringBuilder("#### 招行通道发放通知");
            stringBuilder.append("\\n > [招行发放]招行通道正在发放，请登录后台进行确认\n");
            stringBuilder.append("\\n > 商户编号:"+order.getEmployerName()+"\n");
            stringBuilder.append("\\n > 供应商编号:"+order.getMainstayName()+"\n");
            stringBuilder.append("\\n > 平台批次号:"+order.getPlatBatchNo()+"\n");

            MarkDownMsg markDownMsg = new MarkDownMsg();
            markDownMsg.setContent(stringBuilder.toString());
            markDownMsg.setRobotType(RobotTypeEnum.GRANTING_ROBOT.getType());
            markDownMsg.setUnikey(IdUtil.fastSimpleUUID());
            robotFacade.pushMarkDownAsync(markDownMsg);
        }

        PageResult<List<OrderItemTrxIdBo>> pageResult ;
        List<OrderItemTrxIdBo> recordList ;
        do {
            pageResult = orderItemBiz.listTrxNoIdPage(paramMap, pageParam);
            recordList = pageResult.getData();
            if(!ObjectUtils.isEmpty(recordList)){
                List<OrderItemTrxIdBo> notifyList = Lists.newArrayList();
                recordList.forEach(
                        trxIdBo->{
                            notifyList.add(trxIdBo);
                            if(notifyList.size() >= TradeConstant.NOTIFY_NUM_PER_GROUP){
                                // 满NOTIFY_NUM_PER_GROUP 发一次
                                notifyGrantStart(order.getEmployerNo(), order.getMainstayNo(), order.getPlatBatchNo(),
                                        notifyList.stream().map(OrderItemTrxIdBo::getPlatTrxNo).collect(Collectors.toList()));
                                notifyList.clear();
                            }
                        }
                );
                //发送剩余的不满NOTIFY_COUNT的
                if(!ObjectUtils.isEmpty(notifyList)){
                    notifyGrantStart(order.getEmployerNo(), order.getMainstayNo(), order.getPlatBatchNo(),
                            notifyList.stream().map(OrderItemTrxIdBo::getPlatTrxNo).collect(Collectors.toList()));
                    notifyList.clear();
                }
                paramMap.put("overId", recordList.get(recordList.size()-1).getId());
            }
        } while (!ObjectUtils.isEmpty(recordList));
        log.info("[发放环节: {} ]==>分批通知发放结束", order.getPlatBatchNo());
    }

    /**
     * 正式发放每一批订单明细
     * @param platBatchNo 批次订单号
     * @param platTrxNoList 其中一批订单明细
     */
    public void startGrant(String employerNo, String platBatchNo, List<String> platTrxNoList) {
        if(ObjectUtils.isEmpty(platTrxNoList)) {
            return;
        }
        log.info("[发放环节: {}] -- mchNo:[{}] ==>批次开始发放。platTrxNoList size:{},明细范围[{}]至[{}]"
                , platBatchNo, employerNo,platTrxNoList.size(), platTrxNoList.get(0),platTrxNoList.get(platTrxNoList.size() - 1));
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        Order order = orderBiz.getOne(paramMap);
        if(order == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次订单不存在");
        }
        if(!Objects.equals(order.getBatchStatus(),OrderStatusEnum.GRANTING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次状态异常，不能进行订单处理操作");
        }
        processStart(order, platTrxNoList);
    }

    public void processStart(Order order, List<String> platTrxNoList) {
        log.info("[发放环节: {}]===>批次发放开始 platTrxNoList size:{}", order.getPlatBatchNo(), platTrxNoList.size());
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(order.getEmployerNo(),order.getMainstayNo(),order.getChannelType());
        //放入队列
        Date expireTime = DateUtil.parseJodaDateTime(new Date()).plusMinutes(TradeConstant.GRANTING_WATCH_MAX_TIME).toDate();
        redisClient.zadd(TradeConstant.ORDER_GRANTING_ZSET_KEY,expireTime.getTime(),order.getPlatBatchNo());
        //发放每一笔订单明细
        platTrxNoList.forEach(
                platTrxNo->{
                    OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
                    this.cmbSpeacialHandle(orderItem);
                    if(!Objects.equals(orderItem.getOrderItemStatus(), OrderItemStatusEnum.ACCEPTED.getValue())){
                        return;
                    }
                    RecordItem recordItem = null;
                    //获取锁
                    log.info("[发放环节: {}]==>获取风控锁", orderItem.getPlatTrxNo());
                    String lockKey = TradeConstant.RCMS_LOCK_KEY + orderItem.getReceiveIdCardNoDecrypt();
                    String clientId = redisLock.tryLockLong(lockKey,30000,1);
                    if(clientId == null){
                        log.info("[发放环节: {}]==>获取风控锁失败，重新放回发放队列", orderItem.getPlatTrxNo());
                        // 加上时间限制，避免因为锁无法释放而一直重试
                        if(System.currentTimeMillis() - order.getUpdateTime().getTime() < 10*60*1000){
                            notifyGrantStart(orderItem.getEmployerNo(), order.getMainstayNo(), orderItem.getPlatBatchNo(), Lists.newArrayList(orderItem.getPlatTrxNo()));
                        }
                        return;
                    }
                    try {
                        recordItem = fillRecordItem(order,orderItem,employerAccountInfo);
                        if(Boolean.TRUE.equals(orderItem.getIsPassHangup())){
                            log.info("[发放环节: {}]==>人工通过风控,直接入库", orderItem.getPlatTrxNo());
                            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANTING.getValue());
                            commonBiz.saveRecordItemAndUpdateOrderItem(orderItem, recordItem);
                            orderItem.setVersion(orderItem.getVersion() + 1);
                        }else{
                            //校验是否有未完成的挂单,如果有则丢入队列重试,待挂单订单处理完成之后再继续
                            if(this.checkPendingOrderByMainstayAndId(orderItem)){
                                notifyGrantStartRisckRetry(orderItem.getEmployerNo(), order.getMainstayNo(), orderItem.getPlatBatchNo(), Lists.newArrayList(orderItem.getPlatTrxNo()));
                                redisLock.unlockLong(clientId);
                                return;
                            }

                            log.info("[发放环节: {}]==>调用风控接口", orderItem.getPlatTrxNo());
                            SettleRiskControlVo settleRiskControlVo = BuildVoUtil.fillSettleRiskControlVo(orderItem,recordItem.getCreateTime());
                            long startTime=System.currentTimeMillis();
                            RiskControlResult riskControlResult = riskControlFacade.processSettle(settleRiskControlVo);
                            long endTime=System.currentTimeMillis();
                            log.info("[发放环节：风控执行时间==>{}ms]",endTime - startTime);
                            boolean isPass = riskOp(riskControlResult,orderItem,recordItem);
                            if(!isPass) {return;}
                        }
                        //调用资金发放接口
                        log.info("[发放环节: {}]==>调用发放接口,使用打款流水号:{}", orderItem.getPlatTrxNo(),recordItem.getRemitPlatTrxNo());
                        PayReqVo payReqVo = BuildVoUtil.fillPayReqVo(recordItem,employerAccountInfo,orderItem);
                        ChannelNoEnum channelNoEnum = ChannelNoEnum.getEnum(payReqVo.getChannelNo());
                        payReqVo.setChannelName(channelNoEnum.getDesc());
                        payReqVo.setChannelNo(channelNoEnum.name());
                        payReqVo.setAppid(orderItem.getAppid());
                        payReqVo.setMainstayNo(order.getMainstayNo());
                        PayRespVo payRespVo = pay(payReqVo);
                        payOp(payRespVo,orderItem,recordItem,payReqVo);
                    }catch (BizException bz){
                        if (CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.getSysErrorCode() == bz.getSysErrorCode()) {
                            throw new RuntimeException(bz.getMessage());
                        }
                        log.error("[发放环节: {}]==>发放, 处理业务异常:", orderItem.getPlatTrxNo(), bz);
                        if (!(StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.CMB.name()) && orderItem.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue())) {
                            processFail(recordItem, bz.getApiErrorCode(), bz.getErrMsg(),
                                    String.valueOf(bz.getSysErrorCode()), bz.getErrMsg());
                        }
                    }catch (Exception e){
                        log.error("[发放环节: {}]==>发放, 处理系统异常 自动重试:", orderItem.getPlatTrxNo(), e);
//                        if(redisRateLimiter.tryAcquire(RedisRateLimiter.TokenBucketEnum.GRANT_ERROR)){
                        log.info("[发放环节: {}==> 获取到令牌进行重试]", orderItem.getPlatTrxNo());
                        if (!(StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.CMB.name()) && orderItem.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue())) {
                            notifyHandleGrantException(orderItem.getPlatTrxNo());
                        }
//                        }else {
//                            log.info("[发放环节: {}==> 令牌超限，无法获取到令牌进行重试]",orderItem.getPlatTrxNo());
//                        }
                    }finally {
                        redisLock.unlockLong(clientId);
                    }
                }
        );
        //更新统计
        notifyGrantBatchCount(order.getPlatBatchNo());
    }

    /**
     * 填充打款流水基本信息
     * @param orderItem 订单明细
     * @param employerAccountInfo 用工企业账户
     * @return 打款流水
     */
    public RecordItem fillRecordItem(Order order,OrderItem orderItem, EmployerAccountInfo employerAccountInfo) {
        RecordItem recordItem = new RecordItem();
        BeanUtils.copyProperties(orderItem,recordItem);
        Date now = new Date();
        recordItem.setCreateDate(now);
        recordItem.setCreateTime(now);
        recordItem.setUpdateTime(now);
        recordItem.setChannelMchNo(employerAccountInfo.getSubMerchantNo());
        recordItem.setOrderTaskAmount(orderItem.getOrderItemTaskAmount());
        recordItem.setOrderTaxAmount(orderItem.getOrderItemTaxAmount());
        recordItem.setOrderNetAmount(orderItem.getOrderItemNetAmount());
        recordItem.setOrderFee(orderItem.getOrderItemFee());
        recordItem.setOrderAmount(orderItem.getOrderItemAmount());
        recordItem.setProcessStatus(RecordItemStatusEnum.PAY_CREATED.getValue());
        recordItem.setWorkCategoryCode(order.getWorkCategoryCode());
        recordItem.setWorkCategoryName(order.getWorkCategoryName());
        String remitPlatTrxNo = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.RECORD_ITEM_SEQ.getKey(), SequenceBizKeyEnum.RECORD_ITEM_SEQ.getWidth());
        String compRemitPlatTrxNo = SequenceBizKeyEnum.RECORD_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now) + remitPlatTrxNo;
        recordItem.setRemitPlatTrxNo(compRemitPlatTrxNo);
        return recordItem;
    }

    private void cmbSpeacialHandle(OrderItem orderItem) {
        if(orderItem.getOrderItemStatus().intValue()==OrderItemStatusEnum.GRANT_FAIL.getValue()
            && StringUtils.equals(ApiExceptions.API_TRADE_RISK_REJECT.getApiErrorCode(),orderItem.getErrorCode())
            && StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.CMB.name())
            && orderItem.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue()
        ){
            log.info(" [发放环节: {}]==>招行后台发放直接请求发放接口开始", orderItem.getPlatTrxNo());
            PayReqVo payReqVo = new PayReqVo();
            payReqVo.setPayBatchNo(orderItem.getPlatBatchNo());
            payReqVo.setMainstayNo(orderItem.getMainstayNo());
            payReqVo.setEmployerNo(orderItem.getEmployerNo());
            PayRespVo payRespVo = pay(payReqVo);
            payOp(payRespVo,orderItem,null,payReqVo);
            log.info(" [发放环节: {}]==>招行后台发放直接请求发放接口完成", orderItem.getPlatTrxNo());

        }
    }
    /**
     * 查询是否存在挂单订单
     * @param orderItem
     * @return
     */
    private boolean checkPendingOrderByMainstayAndId(OrderItem orderItem){
        String receiveIdCardNoMd5 = orderItem.getReceiveIdCardNoMd5();
        String mainstayNo = orderItem.getMainstayNo();

        Map<String, Object> param = new ParameterMap<>();
        param.put("receiveIdCardNoMd5", receiveIdCardNoMd5);
        //param.put("mainstayNo", mainstayNo);
        param.put("createBeginDate", DateUtil.addDay(new Date(), -7));
        param.put("createEndDate", DateUtil.getDayEnd(new Date()));

        PageResult<List<RiskcontrolOrderItem>> listPageResult = riskControlFacade.listPagePendingOrder(param, PageParam.newInstance(1, 10));
        Long totalRecord = listPageResult.getTotalRecord();

        if (totalRecord==null || totalRecord.longValue()<=0L){
            return false;
        }
        else {
            log.info("风控未处理订单:{}", JSON.toJSONString(listPageResult.getData()));
            return true;
        }
    }

    /**
     * 根据风控结果操作
     * @param riskControlResult 风控接口返回结果
     * @param orderItem 要操作的订单明细
     * @param recordItem 要操作的打款流水
     * @return 风控结果 true为通过 false为拦截
     */
    private boolean riskOp(RiskControlResult riskControlResult, OrderItem orderItem,RecordItem recordItem) {
        Integer riskCode = riskControlResult.getCode();
        if(riskCode.equals(ControlAtomEnum.PENDING.getValue())){
            log.info("[发放环节: {}]==>触发风控挂单，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(),orderItem.getPlatBatchNo(),riskControlResult.getErrMsg());
            orderItemBiz.update2Hangup(orderItem,riskControlResult);
            return false;
        }else if (riskCode.equals(ControlAtomEnum.REJECT.getValue())){
            log.info("[发放环节: {}]==>触发风控拒绝，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(),orderItem.getPlatBatchNo(),riskControlResult.getErrMsg());
            orderItemBiz.update2Reject(orderItem,riskControlResult);
            this.cmbSpeacialHandle(orderItem);
            return false;
        }else if (riskCode.equals(ControlAtomEnum.PASS.getValue())){
            log.info("[发放环节: {}]==>通过风控，batchNo:[{}], desc{}", orderItem.getPlatTrxNo(),orderItem.getPlatBatchNo(),riskControlResult.getErrMsg());
            // 通过风控 持久化数据 订单明细-(已受理)->发放中
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANTING.getValue());
            orderItem.setJsonStr(null);
            commonBiz.saveRecordItemAndUpdateOrderItem(orderItem, recordItem);
            orderItem.setVersion(orderItem.getVersion() + 1);
            return true;
        }
        return false;
    }

    /**
     * 根据发放接口结果操作
     * @param payRespVo 发放接口返回结果
     * @param orderItem 要操作的订单明细
     * @param recordItem 要操作的打款流水
     */
    private void payOp(PayRespVo payRespVo, OrderItem orderItem, RecordItem recordItem,PayReqVo payReqVo) {
        Integer bankPayStatus = payRespVo.getBankPayStatus();
        if(bankPayStatus.equals(BankPayStatusEnum.SUCCESS.getValue())
                ||bankPayStatus.equals(BankPayStatusEnum.PROCESS.getValue())){
            if (StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.CMB.name()) && orderItem.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue()) {
                log.info("[发放环节: {}]==>调用发放接口返回成功/处理中 等待回调", orderItem.getPlatTrxNo());
            }else {
                log.info("[发放环节: {}]==>调用发放接口返回成功/处理中 等待回调, 使用打款流水号:[{}]", orderItem.getPlatTrxNo(),recordItem.getRemitPlatTrxNo());
            }

            if (StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.CMB.name()) && orderItem.getLaunchWay().intValue() == LaunchWayEnum.API.getValue()) {
                    // 异步反查订单处理结果
                    Dict dict = Dict.create()
                            .set("platBatchNo", recordItem.getPlatBatchNo())
                            .set("remitNo", recordItem.getRemitPlatTrxNo())
                            .set("accountNo",payReqVo.getChannelMchNo());
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_CALLBACK_QUERY, recordItem.getEmployerNo(), recordItem.getPlatTrxNo(), NotifyTypeEnum.CMB_CALL_BACK_QUERY.getValue(), MessageMsgDest.TAG_CMB_CALLBACK_QUERY, JSONUtil.toJsonStr(dict), MsgDelayLevelEnum.M_8.getValue());
            }
        }else if(bankPayStatus.equals(BankPayStatusEnum.FAIL.getValue())){
            if(payRespVo.getBizCode().equals(String.valueOf(BanklinkExceptions.ORDER_NO_REPEAT.getSysErrorCode()))){
                log.error("[发放环节: {}]==>调用发放接口返回失败，失败业务码为打款流水号重复 不作处理 , 使用打款流水号:[{}]", orderItem.getPlatTrxNo(),recordItem.getRemitPlatTrxNo());
                return;
            }
            log.error("[发放环节: {}]==>调用发放接口返回失败，更新订单明细及记录明细 , 使用打款流水号:[{}]", orderItem.getPlatTrxNo(),recordItem.getRemitPlatTrxNo());
            String msg = payRespVo.getBizMsg();
            processFail(recordItem, ApiExceptions.API_TRADE_CHANNEL_GRANT_FAIL.getApiErrorCode(), msg,
                    String.valueOf(ApiExceptions.API_TRADE_CHANNEL_GRANT_FAIL.getSysErrorCode()), msg);
        }else if(bankPayStatus.equals(BankPayStatusEnum.UN_KNOW.getValue())){
            log.error("[发放环节: {}]==>调用发放接口返回未知,进入异常重试 使用打款流水号:[{}]", orderItem.getPlatTrxNo(),recordItem.getRemitPlatTrxNo());
            notifyHandleGrantException(orderItem.getPlatTrxNo());
        }
    }

    /**
     * 错误/失败 更新订单明细和打款记录
     * 更新完发起统计
     * @param recordItem 订单明细
     * @param outerErrCode 外部错误码
     * @param outerErrMsg 外部错误信息
     * @param innerErrCode 内部错误码
     * @param innerErrMsg 内部信息
     */
    private void processFail(RecordItem recordItem, String outerErrCode, String outerErrMsg,
                             String innerErrCode, String innerErrMsg) {
        if(recordItem == null) {
            return;
        }
        // 失败处理
        commonBiz.updateProcessFail(recordItem, outerErrCode, outerErrMsg, innerErrCode, innerErrMsg);
        // 通知更新处理数据
        notifyGrantBatchCount(recordItem.getPlatBatchNo());
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
        notifyOrderComplete(orderItem,recordItem);
    }

    /**
     * 处理发放系统异常
     */
    public void handleGrantException(String platTrxNo){
//        Long grantTimes = redisClient.incr(TradeConstant.GRANT_TIME_REDIS_PREFIX + platTrxNo);
//        redisClient.expire(TradeConstant.GRANT_TIME_REDIS_PREFIX + platTrxNo,60*60);
//        log.info("[发放环节: {}]==>处理发放系统异常,进行不换单发放重试",platTrxNo);
//        if(grantTimes > TradeConstant.MAX_GRANT_TIMES){
//            log.error("[发放环节: {}]==>不换单发放重试数超过5次,停止重试", platTrxNo);
//            return;
//        }
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(platTrxNo);
        if(orderItem == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单明细不存在");
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platTrxNo",platTrxNo);
        RecordItem recordItem = recordItemBiz.getOne(paramMap);

        //没有入库 放回队列重试
        if(recordItem == null){
            log.info("[发放环节: {}]==>发放重试,打款流水没有入库 重新放回队列发放",platTrxNo);
            notifyGrantStart(orderItem.getEmployerNo(), orderItem.getMainstayNo(), orderItem.getPlatBatchNo(), Lists.newArrayList(orderItem.getPlatTrxNo()));
            return;
        }

        String remitPlatTrxNo = recordItem.getRemitPlatTrxNo();
        //检验打款流水状态
        if(!Objects.equals(recordItem.getProcessStatus(),RecordItemStatusEnum.PAY_CREATED.getValue())){
            log.info("[发放环节: {}]==>发放重试,打款流水非已创建状态 不做打款重试 打款流水号:[{}]",platTrxNo,remitPlatTrxNo);
            return;
        }

        handleExceptionSearch(recordItem,orderItem,platTrxNo,remitPlatTrxNo);
    }

    private void handleExceptionSearch(RecordItem recordItem, OrderItem orderItem, String platTrxNo, String remitPlatTrxNo) {
        //如果入库了 反查通道
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(orderItem.getEmployerNo(), orderItem.getMainstayNo(), orderItem.getChannelType());
        QueryPayOrderReqVo queryPayOrderReqVo = new QueryPayOrderReqVo();
        queryPayOrderReqVo.setBankOrderNo(recordItem.getRemitPlatTrxNo());
        queryPayOrderReqVo.setChannelNo(recordItem.getPayChannelNo());
        queryPayOrderReqVo.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
        queryPayOrderReqVo.setChannelType(employerAccountInfo.getChannelType());
        //判断支付通道类型
        PayRespVo queryRespVo = payBankFacade.queryPayOrder(queryPayOrderReqVo);
        Integer orderStatus = queryRespVo.getBankPayStatus();
        if (orderStatus.equals(BankPayStatusEnum.PROCESS.getValue())) {
            log.info("[发放环节: {}]==>反查接口 订单存在,返回状态为{},不作处理 打款流水号:[{}]", platTrxNo, orderStatus, remitPlatTrxNo);
            return;
        }

        if (orderStatus.equals(BankPayStatusEnum.UN_KNOW.getValue())) {
            if (StringUtils.equals(recordItem.getPayChannelNo(), ChannelNoEnum.JOINPAY_JXH.name())) {
                log.info("[发放环节: {}]==>反查接口 君享汇订单交易状态未知,返回状态为{}，进入异常重试， 20分钟后再重试 打款流水号:[{}]", platTrxNo, orderStatus, remitPlatTrxNo);
                notifyHandleGrantExceptionAgain(platTrxNo);
                return;
            }
            log.info("[发放环节: {}]==>反查接口 订单存在,返回状态为{},不作处理 打款流水号:[{}]", platTrxNo, orderStatus, remitPlatTrxNo);
            return;
        }

        if (orderStatus == BankPayStatusEnum.CMB_PROCESS.getValue()) {
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg(StrUtil.format("[{}]-通道在处理中，将触发重试", platTrxNo));
        }

        Date now = new Date();
        if (orderStatus.equals(BankPayStatusEnum.SUCCESS.getValue())) {
            log.info("[发放环节: {}]==>反查接口 返回订单处理成功,进行成功操作 打款流水号:[{}]", platTrxNo, remitPlatTrxNo);
            recordItem.setChannelTrxNo(queryRespVo.getBankTrxNo());
            orderItem.setChannelTrxNo(queryRespVo.getBankTrxNo());
            recordItem.setCompleteTime(now);
            orderItem.setCompleteTime(now);
            recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SUCCESS.getValue());
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        } else if (orderStatus.equals(BankPayStatusEnum.FAIL.getValue())) {
            log.info("[发放环节: {}]==>反查接口 返回订单处理失败,进行失败操作 打款流水号:[{}]", platTrxNo, remitPlatTrxNo);
            //失败中订单不存在 特殊处理
            if (StringUtils.equals(queryRespVo.getBizCode(), String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()))) {
                log.info("[发放环节: {}]==>反查接口 返回订单处理失败，业务码为订单不存在,调用发放接口进行打款重试 打款流水号:[{}]", platTrxNo, remitPlatTrxNo);
                //调用资金发放接口
                PayReqVo payReqVo = BuildVoUtil.fillPayReqVo(recordItem, employerAccountInfo, orderItem);
                log.info("[发放环节: {}]==>反查接口,构建参数：{}", platTrxNo, payReqVo);
                PayRespVo payRespVo = pay(payReqVo);
                payOp(payRespVo, orderItem, recordItem, payReqVo);
                return;
            } else {
                recordItem.setChannelTrxNo(queryRespVo.getBankTrxNo());
                orderItem.setChannelTrxNo(queryRespVo.getBankTrxNo());
                recordItem.setCompleteTime(now);
                orderItem.setCompleteTime(now);
                orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
                if (StringUtils.equals(queryRespVo.getBizCode(), String.valueOf(BanklinkExceptions.ORDER_REFUND.getSysErrorCode()))) {
                    recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SPECIAL_FAIL.getValue());
                } else {
                    recordItem.setProcessStatus(RecordItemStatusEnum.PAY_FAIL.getValue());
                }
            }
        }


        commonBiz.updateRecordItemAndUpdateOrderItem(orderItem, recordItem, now);
        //统计
        notifyGrantBatchCount(orderItem.getPlatBatchNo());
        //通知各个系统
        notifyOrderComplete(orderItem, recordItem);

    }

    public void updateGrantBatchCount(String platBatchNo){
        log.info("[发放环节: {}]==>更新批次表发放统计信息", platBatchNo);
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        Order order = orderBiz.getOne(paramMap);
        if(order == null || !Objects.equals(order.getBatchStatus(),OrderStatusEnum.GRANTING.getValue())){
            return;
        }
        OrderItemCountBo resultBo = orderItemBiz.getCountBoByPlatBatchNo(platBatchNo);

        log.info("[发放环节: {}]==>发放统计信息resultVo：{}", platBatchNo, JSON.toJSONString(resultBo));

        order.setSuccessCount(resultBo.getSuccessCount());
        order.setSuccessNetAmount(resultBo.getSuccessNetAmount());
        order.setSuccessFee(resultBo.getSuccessFee());
        order.setSuccessTaskAmount(resultBo.getSuccessTaskAmount());
        order.setSuccessTaxAmount(resultBo.getSuccessTaxAmount());
        order.setFailCount(resultBo.getFailCount());
        order.setFailTaskAmount(resultBo.getFailTaskAmount());
        order.setFailNetAmount(resultBo.getFailNetAmount());
        log.info("[发放环节: {}]==> getAcceptedCount：{}, getRequestCount:{}", platBatchNo, order.getAcceptedCount(),order.getRequestCount());
        boolean isComplete = order.getRequestCount().equals(resultBo.getSuccessCount() + resultBo.getFailCount());
        if (isComplete) {
            if (StringUtils.equals(order.getPayChannelNo(), ChannelNoEnum.CMB.name())) {
                this.handleCmbFee(order);
            }
            log.info("[发放环节: {}]==>批次发放完成 先调用发票账务处理", platBatchNo);
            Date completeTime = new Date();
            order.setCompleteTime(completeTime);
            accountInvoiceProcessNotify(order);
            order.setBatchStatus(OrderStatusEnum.FINISH_GRANT.getValue());
            try {
                orderBiz.update(order);
            }catch (Exception e){
                log.error("[发放环节: {}]==>批次发放完成统计更新数据库异常 mq进行重试 errMsg:{}",platBatchNo,e);
                throw CommonExceptions.COMMON_RETRY_ERROR;
            }
            log.info("[发放环节: {}]==>批次发放完成 成功更新批次状态", platBatchNo);
            //最后通知商户
            try {
                notifyMchGrantResult(order);
            } catch (Exception e) {
                log.error("通知商户失败",e);
            }

            //推送动态
            if (order.getLaunchWay().intValue() == LaunchWayEnum.EMPLOYER.getValue()){
                dynamicMsgBiz.putDynamicMsg(order.getEmployerNo(),String.format(
                        DynamicMsgEnum.BATCH_GRANT.getMsg(),order.getPlatBatchNo(),order.getSuccessCount(),order.getFailCount()));
            }

            //推送到河马
            payCallbackToHema(order);
            return;
        }
        orderBiz.update(order);
    }

    /**
     * 通知各个子系统
     */
    public void notifyOrderComplete(OrderItem orderItem,RecordItem recordItem) {
        OrderCompleteVo vo = buildNotifyFeeVo(orderItem,recordItem);
        String msg = JsonUtil.toString(vo);
        boolean isSendSuccess = notifyFacade.sendOne(MessageMsgDest.TOPIC_ORDER_COMPLETE,
                orderItem.getEmployerNo(),
                orderItem.getPlatTrxNo(),
                NotifyTypeEnum.ORDER_COMPLETE.getValue(),
                null,
                msg);

        if(isSendSuccess){
            log.info("[发放环节: {}]==>订单完成，通知各个系统成功",orderItem.getPlatTrxNo());
        }else {
            log.error("[发放环节: {}]==>订单完成，通知各个系统失败",orderItem.getPlatTrxNo());
        }
        notifyFacade.sendOne(MessageMsgDest.TOPIC_ORDER_DATA_SYNC_CK, vo.getMchNo(), vo.getPlatTrxNo(), NotifyTypeEnum.ORDER_DATA_SYNC_CK.getValue(), MessageMsgDest.TAG_ORDER_DATA_SYNC_CK, orderItem.getPlatTrxNo(),MsgDelayLevelEnum.M_1.getValue());
    }

    /**
     * 订单计费
     * @param orderItem  订单明细
     * @param recordItem  打款流水
     * @param calculateAmount  计费金额（智享汇使用实发金额，创客汇使用任务金额）
     * @return
     */
    protected OrderCompleteVo buildNotifyFeeVo(OrderItem orderItem, RecordItem recordItem,BigDecimal calculateAmount){
        String orderItemNetAmountStr = String.valueOf(orderItem.getOrderItemNetAmount());
        String employerNo = orderItem.getEmployerNo();
        String mainstayNo = orderItem.getMainstayNo();

        OrderCompleteVo vo = new OrderCompleteVo();
        vo.setTradeTime(orderItem.getCompleteTime());
        vo.setMchNo(employerNo);
        vo.setMchName(orderItem.getEmployerName());
        vo.setVendorNo(mainstayNo);
        vo.setVendorName(orderItem.getMainstayName());
        vo.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        vo.setProductNo(orderItem.getProductNo());
        vo.setProductName(orderItem.getProductName());
        vo.setMchOrderNo(orderItem.getMchOrderNo());
        vo.setPlatTrxNo(orderItem.getPlatTrxNo());
        vo.setBaseOrderAmount(calculateAmount);
        vo.setOrderFee(orderItem.getOrderItemFee());
        vo.setRemark(orderItem.getRemark());
        vo.setChannelTrxNo(recordItem.getChannelTrxNo());
        vo.setChannelOrderNo(recordItem.getRemitPlatTrxNo());
        if(orderItem.getOrderItemStatus().equals(OrderItemStatusEnum.GRANT_SUCCESS.getValue())){
            vo.setStatus(PublicStatus.ACTIVE);
        }else if(orderItem.getOrderItemStatus().equals(OrderItemStatusEnum.GRANT_FAIL.getValue())){
            vo.setStatus(PublicStatus.INACTIVE);
        }

        SpecialRuleParamDto param1 = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.VERDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto param2 = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> merchantFeeDtoList = com.beust.jcommander.internal.Lists.newArrayList(param1,param2);
        vo.setMerchantFeeDtoList(merchantFeeDtoList);

        SpecialRuleParamDto param3 = new SpecialRuleParamDto(CostFeeSpecialRuleTypeEnum.MERCANT_NO.getValue(),employerNo);
        SpecialRuleParamDto param4 = new SpecialRuleParamDto(CostFeeSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> vendorFeeDtoList = com.beust.jcommander.internal.Lists.newArrayList(param3,param4);
        vo.setVendorFeeDtoList(vendorFeeDtoList);

        SpecialRuleParamDto param5 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.MERCHANT_NO.getValue(),employerNo);
        SpecialRuleParamDto param6 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.VENDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto param7 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> salesFeeDtoList = com.beust.jcommander.internal.Lists.newArrayList(param5,param6,param7);
        vo.setSalesFeeDtoList(salesFeeDtoList);

        SpecialRuleParamDto agentParam1 = new SpecialRuleParamDto(AgentSpecialRuleTypeEnum.VENDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto agentParam2 = new SpecialRuleParamDto(AgentSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        SpecialRuleParamDto agentParam3 = new SpecialRuleParamDto(AgentSpecialRuleTypeEnum.MERCHANT_NO.getValue(),employerNo);
        List<SpecialRuleParamDto> agentFeeDtoList = com.beust.jcommander.internal.Lists.newArrayList(agentParam1, agentParam2, agentParam3);
        vo.setAgentFeeDtoList(agentFeeDtoList);

        // 查出一级合伙人
        RelevantAgent relevantAgent = merchantFacade.getRelevantAgentByMchNo(vo.getMchNo());
        if (relevantAgent != null && StringUtils.isNotEmpty(relevantAgent.getOneLevelAgentNo())) {
            vo.setOneLevelAgentNo(relevantAgent.getOneLevelAgentNo());
            vo.setOneLevelAgentName(relevantAgent.getOneLevelAgentName());
            vo.setOneLeveOpen(agentProductRelationFacade.isOpenProduct(relevantAgent.getOneLevelAgentNo(), vo.getProductNo()));
            vo.setOneLevelAgentStatus(relevantAgent.getOneLevelAgentStatus());
            // 查出二级合伙人
            if (StringUtils.isNotBlank(relevantAgent.getTwoLevelAgentNo())) {
                AgentDetailVo secondAgen = agentFacade.getDetailByAgentNo(relevantAgent.getTwoLevelAgentNo());
                if (secondAgen != null) {
                    vo.setSecondLevelAgentNo(relevantAgent.getTwoLevelAgentNo());
                    vo.setSecondLevelAgentName(relevantAgent.getTwoLevelAgentName());
                    vo.setSecondLevelAgentStatus(relevantAgent.getTwoLevelAgentStatus());
                    vo.setSecondLeveOpen(agentProductRelationFacade.isOpenProduct(relevantAgent.getTwoLevelAgentNo(), vo.getProductNo()));
                }
            }
        }
        return vo;
    }

    private void notifyMchGrantResult(Order order) {
        try{
            if (StringUtils.isNotEmpty(order.getCallbackUrl())){
                String cacheKey = TradeConstant.GRANT_NOTIFY_MCH_REDIS_PREFIX + order.getPlatBatchNo();
                if(redisClient.get(cacheKey) == null) {
                    //缓存五分钟，期间不会再通知商户
                    redisClient.set(cacheKey, order.getPlatBatchNo(), 300);
                    List<OrderItem> itemList = orderItemBiz.listBy(order.getPlatBatchNo());
                    MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, itemList.get(0));

                    tradeNotifyBiz.notifyMerchant(merchantNotifyParam,order);
                    log.info("[{}-{}]发放完成，通知商户发放结果", order.getEmployerNo(), order.getMchBatchNo());
                }
            }else{
                String mchNo = order.getEmployerNo();
                final MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(mchNo);

                if (merchantSecret == null) {
                    return;
                }

                MerchantNotifySet merchantNotifySet = merchantNotifySetFacade.getByMchNoAndType(mchNo, MerchantNotifySetTypeEnum.GRANT_COMPLETE_NOTIFY.getValue());
                if (merchantNotifySet == null){
                    return;
                }

                String notifyUrl = merchantNotifySet.getNotifyUrl();
                Integer notifyStatus = merchantNotifySet.getNotifyStatus();
                if (StringUtils.isNotBlank(notifyUrl) && notifyStatus == CommonStatusEnum.ACTIVE.getValue()) {
                    List<OrderItem> itemList = orderItemBiz.listBy(order.getPlatBatchNo());

                    itemList.forEach(item->{
                        String cacheKey = TradeConstant.GRANT_NOTIFY_MCH_BATCH_REDIS_PREFIX + order.getPlatBatchNo()+":"+item.getPlatTrxNo();
                        if(redisClient.get(cacheKey) == null) {
                            //缓存五分钟，期间不会再通知商户
                            redisClient.set(cacheKey, item.getPlatTrxNo(), 300);

                            Order.JsonEntity jsonEntity = new Order.JsonEntity();
                            jsonEntity.setSignType("1");
                            order.setJsonEntity(jsonEntity);

                            MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, item);
                            merchantNotifyParam.setNotifyUrl(notifyUrl);
                            tradeNotifyBiz.notifyMerchantBatch(merchantNotifyParam,item);
                            log.info("[{}-{}]发放完成，通知商户发放结果", order.getEmployerNo(), item.getPlatTrxNo());
                        }
                    });
                }
            }
        }catch (Exception e){
            log.info("[{}-{}]通知商户发放异常", order.getEmployerNo(), order.getMchBatchNo(), e);
            throw CommonExceptions.COMMON_RETRY_ERROR;
        }
    }

    private void payCallbackToHema(Order order) {
        String hemaMaintayNo = dataDictionaryFacade.getSystemConfig("HEMA_MAINSTAY_NO");
        List<String> hemaMainstayNos = new ArrayList<>();
        if (StringUtils.isNotBlank(hemaMaintayNo)) {
            hemaMainstayNos = ListUtil.toList(hemaMaintayNo.split(","));
        }
        if (hemaMainstayNos.size() > 0 && hemaMainstayNos.contains(order.getMainstayNo())) {
            List<OrderItem> itemList = orderItemBiz.listBy(order.getPlatBatchNo());
            itemList.forEach(x->{
                notifyFacade.sendOne(MessageMsgDest.TOPIC_PAY_CALLBACK_TO_HEMA,
                        order.getEmployerNo(),
                        order.getPlatBatchNo(),NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_PAY_CALLBACK_TO_HEMA,
                        JsonUtil.toString(x));
            });
        }
    }

    private void accountInvoiceProcessNotify(Order order){
        notifyFacade.sendOne(MessageMsgDest.TOPIC_INVOICE_PROCESS, order.getEmployerNo(), order.getPlatBatchNo(), NotifyTypeEnum.INVOICE_PROCESS.getValue(), MessageMsgDest.TAG_INVOICE_PROCESS, JSON.toJSONString(order));
    }

    /**
     * 渠道回调处理
     * @param payReceiveRespVo 回调结果
     */
    public void handleNotify(PayReceiveRespVo payReceiveRespVo,RecordItem recordItem) {
        log.info("交易中心: 回调处理接收结果{}", JSON.toJSONString(payReceiveRespVo));
        Integer bankPayStatus = payReceiveRespVo.getBankPayStatus();
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
        if(Objects.equals(recordItem.getProcessStatus(),RecordItemStatusEnum.PAY_CREATED.getValue())
                && Objects.equals(orderItem.getOrderItemStatus(),OrderItemStatusEnum.GRANTING.getValue())){
            Date now = new Date();
            if(bankPayStatus.equals(BankPayStatusEnum.SUCCESS.getValue())){
                log.info("[回调环节: 打款流水号 {}]==>订单平台流水号{},返回订单成功 更新订单明细及记录明细,通知系统", recordItem.getRemitPlatTrxNo(), orderItem.getPlatTrxNo());
                recordItem.setChannelTrxNo(payReceiveRespVo.getBankTrxNo());
                orderItem.setChannelTrxNo(payReceiveRespVo.getBankTrxNo());
                recordItem.setCompleteTime(now);
                orderItem.setCompleteTime(now);
                recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SUCCESS.getValue());
                orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_SUCCESS.getValue());

            }else if (bankPayStatus.equals(BankPayStatusEnum.FAIL.getValue())){
                log.info("[回调环节: 打款流水号 {}]==>订单平台流水号{},返回订单失败 更新订单明细及记录明细,通知系统", recordItem.getRemitPlatTrxNo(), orderItem.getPlatTrxNo());
                recordItem.setChannelTrxNo(payReceiveRespVo.getBankTrxNo());
                orderItem.setChannelTrxNo(payReceiveRespVo.getBankTrxNo());
                recordItem.setCompleteTime(now);
                orderItem.setCompleteTime(now);
                recordItem.setProcessStatus(RecordItemStatusEnum.PAY_FAIL.getValue());
                orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
                orderItem.setErrorCode(ApiExceptions.API_TRADE_CHANNEL_GRANT_FAIL.getApiErrorCode());
                orderItem.setErrorDesc(payReceiveRespVo.getBizMsg());
                notifyFailRefund(recordItem);
            }
            commonBiz.updateRecordItemAndUpdateOrderItem(orderItem,recordItem,now);
        } else {
            log.error("[回调环节: 打款流水号 {}]==>订单平台流水号{},订单明细状态:{},记录明细状态：{}，不为发放中,请检查"
                    ,recordItem.getRemitPlatTrxNo(),recordItem.getPlatTrxNo(),orderItem.getOrderItemStatus(), recordItem.getProcessStatus());
        }
        // 订单为成功/失败等终态时，发送通知
        if(Objects.equals(orderItem.getOrderItemStatus(),OrderItemStatusEnum.GRANT_SUCCESS.getValue())
                || Objects.equals(orderItem.getOrderItemStatus(),OrderItemStatusEnum.GRANT_FAIL.getValue())){
            //统计
            notifyGrantBatchCount(orderItem.getPlatBatchNo());
            //通知各个系统
            notifyOrderComplete(orderItem,recordItem);
        }
    }

    /**
     * 回调失败退款
     */
    public void callbackRefund(RecordItem recordItem) {
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(
                recordItem.getEmployerNo(),recordItem.getMainstayNo(),recordItem.getChannelType());
        PayReqVo payReqVo = BuildVoUtil.fillPayReqVo(recordItem,employerAccountInfo,null);
        payBankFacade.payRefund(payReqVo);
    }

    /**
     * 流水反查
     * @param recordItem
     * @return
     */
    public String reverseQuery(RecordItem recordItem){
        log.info("[人工反查通道: {}]==> 开始通道反查", recordItem.getRemitPlatTrxNo());
        OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
        if (orderItem == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该打款流水对应的订单明细不存在 反查流水号:" + recordItem.getRemitPlatTrxNo());
        }

        //检验打款流水状态
        if (!Objects.equals(recordItem.getProcessStatus(), RecordItemStatusEnum.PAY_CREATED.getValue())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该打款流水已经是终态，不进行反查");
        }
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(
                orderItem.getEmployerNo(), orderItem.getMainstayNo(), orderItem.getChannelType());
        //查询订单状态
        PayRespVo queryRespVo = queryChannelOrder(recordItem,employerAccountInfo);
        Integer orderStatus = queryRespVo.getBankPayStatus();

        if (orderStatus.equals(BankPayStatusEnum.PROCESS.getValue())) {
            return "通道返回处理中,等待通道处理";
        }
        if (orderStatus.equals(BankPayStatusEnum.UN_KNOW.getValue())) {
            return "通道返回未知，请稍后重试";
        }

        Date now = new Date();
        if (orderStatus.equals(BankPayStatusEnum.SUCCESS.getValue())) {
            log.info("[人工反查通道: {} ]==>反查通道返回成功", recordItem.getRemitPlatTrxNo());
            recordItem.setChannelTrxNo(queryRespVo.getBankTrxNo());
            orderItem.setChannelTrxNo(queryRespVo.getBankTrxNo());
            recordItem.setCompleteTime(now);
            orderItem.setCompleteTime(now);
            recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SUCCESS.getValue());
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        } else if (orderStatus.equals(BankPayStatusEnum.FAIL.getValue())) {
            log.info("[人工反查通道: {} ]==>反查通道返回失败", recordItem.getRemitPlatTrxNo());
            //失败中订单不存在 特殊处理
            if (queryRespVo.getBizCode().equals(String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()))) {
                return orderNotExistPayAgain(recordItem, orderItem, employerAccountInfo);
            } else {
                recordItem.setChannelTrxNo(queryRespVo.getBankTrxNo());
                orderItem.setChannelTrxNo(queryRespVo.getBankTrxNo());
                recordItem.setCompleteTime(now);
                orderItem.setCompleteTime(now);
                recordItem.setProcessStatus(RecordItemStatusEnum.PAY_FAIL.getValue());
                orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
                reverseQueryFailHandle(recordItem);
            }
        }

        commonBiz.updateRecordItemAndUpdateOrderItem(orderItem, recordItem, now);
        //统计
        notifyGrantBatchCount(orderItem.getPlatBatchNo());
        //通知各个系统
        notifyOrderComplete(orderItem, recordItem);

        return "反查成功,更新为终态 请刷新";
    }

    /**
     * 订单不存在，重新支付
     * @param recordItem
     * @param orderItem
     * @param employerAccountInfo
     * @return
     */
    private String orderNotExistPayAgain(RecordItem recordItem, OrderItem orderItem, EmployerAccountInfo employerAccountInfo) {
        log.info("[人工反查通道: {} ]==> 反查返回打款订单不存在,调用打款接口", recordItem.getRemitPlatTrxNo());
        //调用资金发放接口
        PayReqVo payReqVo = BuildVoUtil.fillPayReqVo(recordItem, employerAccountInfo,orderItem);
        PayRespVo payRespVo = pay(payReqVo);
        log.info("[人工反查通道: {} ]==> 反查返回打款订单不存在,调用打款接口结果:{}", recordItem.getRemitPlatTrxNo(), JsonUtil.toString(payRespVo));
        Integer bankPayStatus = payRespVo.getBankPayStatus();
        if (bankPayStatus.equals(BankPayStatusEnum.SUCCESS.getValue())
                || bankPayStatus.equals(BankPayStatusEnum.PROCESS.getValue())) {
            return "订单在通道不存在,重新推送通道,通道处理中";
        } else if (bankPayStatus.equals(BankPayStatusEnum.FAIL.getValue())) {
            if (payRespVo.getBizCode().equals(String.valueOf(BanklinkExceptions.ORDER_NO_REPEAT.getSysErrorCode()))) {
                return "订单在通道不存在,重新推送通道,通道返回订单号重复,请稍后重试";
            }
            String msg = payRespVo.getBizMsg();
            commonBiz.updateProcessFail(recordItem, ApiExceptions.API_TRADE_CHANNEL_GRANT_FAIL.getApiErrorCode(), msg,
                    String.valueOf(ApiExceptions.API_TRADE_CHANNEL_GRANT_FAIL.getSysErrorCode()), msg);
            // 通知更新处理数据
            notifyGrantBatchCount(recordItem.getPlatBatchNo());
            notifyOrderComplete(orderItem, recordItem);
            return "订单在通道不存在,重新推送通道,通道返回失败 更新状态";
        } else if (bankPayStatus.equals(BankPayStatusEnum.UN_KNOW.getValue())) {
            return "订单在通道不存在,重新推送通道,通道返回未知,请稍后重试";
        }
        return "订单在通道不存在,重新推送通道";
    }

    public void notifyMchGrantResult(OrderItem orderItem,Order order) {
        try{
            if (StringUtils.isNotEmpty(order.getCallbackUrl())){
                String cacheKey = TradeConstant.GRANT_NOTIFY_MCH_REDIS_PREFIX + orderItem.getPlatTrxNo();
                if(redisClient.get(cacheKey) == null) {
                    //缓存五分钟，期间不会再通知商户
                    redisClient.set(cacheKey, order.getPlatBatchNo(), 300);
                    MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, orderItem);

                    tradeNotifyBiz.notifyMerchant(merchantNotifyParam,order);
                    log.info("[{}-{}]发放完成，通知商户发放结果", order.getEmployerNo(), order.getMchBatchNo());
                }
            }else{
                String mchNo = order.getEmployerNo();
                final MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(mchNo);

                if (merchantSecret == null) {
                    return;
                }

                String notifyUrl = merchantSecret.getNotifyUrl();
                Integer notifyStatus = merchantSecret.getNotifyStatus();
                if (StringUtils.isNotBlank(notifyUrl) && notifyStatus == CommonStatusEnum.ACTIVE.getValue()) {
                    List<OrderItem> itemList = orderItemBiz.listBy(order.getPlatBatchNo());

                    String cacheKey = TradeConstant.GRANT_NOTIFY_MCH_BATCH_REDIS_PREFIX + orderItem.getPlatBatchNo()+":"+orderItem.getPlatTrxNo();
                    if(redisClient.get(cacheKey) == null) {
                        //缓存五分钟，期间不会再通知商户
                        redisClient.set(cacheKey, orderItem.getPlatTrxNo(), 300);

                        Order.JsonEntity jsonEntity = new Order.JsonEntity();
                        jsonEntity.setSignType("1");
                        order.setJsonEntity(jsonEntity);

                        MerchantNotifyParam merchantNotifyParam = tradeHelperBiz.fillGrantResultNotifyParam(order, orderItem);
                        merchantNotifyParam.setNotifyUrl(notifyUrl);
                        tradeNotifyBiz.notifyMerchantBatch(merchantNotifyParam,orderItem);
                        log.info("[{}-{}]发放完成，通知商户发放结果", order.getEmployerNo(), orderItem.getPlatTrxNo());
                    }

                }

            }
        }catch (Exception e){
            log.info("[{}-{}]通知商户发放异常", order.getEmployerNo(), order.getMchBatchNo(), e);
            throw CommonExceptions.COMMON_RETRY_ERROR;
        }
    }

    private void handleCmbFee(Order order) {
        String platBatchNo = order.getPlatBatchNo();
        Map<String, BigDecimal> grantSumAmount = recordItemBiz.getWaiteGrantSumAmount(platBatchNo,RecordItemStatusEnum.PAY_SUCCESS);
        if (grantSumAmount == null || grantSumAmount.get("sumFee") == null) {
            return;
        }
        String sumFee = grantSumAmount.get("sumFee").toPlainString();
        if (sumFee == null || new BigDecimal(sumFee).compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        DeductionCommissionDTO deductionCommissionDTO = new DeductionCommissionDTO();
        deductionCommissionDTO.setMchNo(order.getEmployerNo());
        deductionCommissionDTO.setMchName(order.getEmployerName());
        deductionCommissionDTO.setMainstayNo(order.getMainstayNo());
        deductionCommissionDTO.setMainstayName(order.getMainstayName());
        deductionCommissionDTO.setPlatBatchNo(order.getPlatBatchNo());
        deductionCommissionDTO.setFeeAmount(BigDecimal.valueOf(Double.parseDouble(sumFee)));
        //招行本地账户扣手续费
        cmbMerchantBalanceBiz.deductionCommission(deductionCommissionDTO);
    }

    @Override
    public void refundLocalFrozenAmount(String platTrxNo) {
        RecordItem recordItem = recordItemBiz.getByPlatTrxNo(platTrxNo);
        if (recordItem == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("打款记录不存在");
        } else if (recordItem.getProcessStatus() != RecordItemStatusEnum.PAY_FAIL.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("打款记录的状态不是打款失败");
        } else if (System.currentTimeMillis() - recordItem.getCreateTime().getTime() < 20 * 60 * 1000) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("打款记录创建20分钟后才能操作");
        }
        boolean isSuccess = this.refundLocalFrozenAmount(recordItem);
        if (isSuccess) {
            recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SPECIAL_FAIL.getValue());
            recordItemBiz.update(recordItem);
        }
    }

    abstract boolean refundLocalFrozenAmount(RecordItem recordItem);
}
