package com.zhixianghui.service.trade.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.zhixianghui.common.service.utils.MybatisUtil;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.common.OrderCompleteVo;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleParamDto;
import com.zhixianghui.common.statics.enums.fee.AgentSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CostFeeSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.fee.ProductFeeSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.fee.SalesSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.fee.service.AgentProductRelationFacade;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.vo.RelevantAgent;
import com.zhixianghui.facade.merchant.vo.agent.AgentDetailVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.service.trade.dao.mapper.OfflineOrderItemMapper;
import com.zhixianghui.service.trade.dao.mapper.OfflineOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class OfflineOrderBiz {

    @Autowired
    private OfflineOrderMapper offlineOrderMapper;

    @Autowired
    private OfflineOrderItemMapper offlineOrderItemMapper;

    @Reference
    private MerchantFacade merchantFacade;

    @Reference
    private AgentFacade agentFacade;

    @Reference
    private AgentProductRelationFacade agentProductRelationFacade;

    @Reference
    private NotifyFacade notifyFacade;

    public void saveOrder(OfflineOrder offlineOrder) {
        offlineOrderMapper.insert(offlineOrder);
    }

    public OfflineOrder getById(Long id) {
        return offlineOrderMapper.selectById(id);
    }

    public Page<OfflineOrder> pageOrder(Page<OfflineOrder> page, Map<String,Object> param) {
        QueryWrapper<OfflineOrder> queryWrapper = new QueryWrapper<OfflineOrder>();
        MybatisUtil.buildWhere(queryWrapper, OfflineOrder.class, param);
        OfflineOrder.extendWhere(queryWrapper, param);
        final Page<OfflineOrder> selectPage = offlineOrderMapper.selectPage(page, queryWrapper);
        return selectPage;
    }

    public List<OfflineOrder> listBy(Map<String,Object> param) {
        QueryWrapper<OfflineOrder> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, OfflineOrder.class, param);
        OfflineOrder.extendWhere(queryWrapper, param);
        return offlineOrderMapper.selectList(queryWrapper);
    }

    public OfflineOrder getByPlatBatchNo(String platBatchNo) {
        return offlineOrderMapper.selectOne(new QueryWrapper<OfflineOrder>().lambda().eq(OfflineOrder::getPlatBatchNo, platBatchNo));
    }

    public OfflineOrder getOne(Map<String, Object> param) {
        final QueryWrapper<OfflineOrder> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, OfflineOrder.class, param);
        OfflineOrder.extendWhere(queryWrapper, param);
        return offlineOrderMapper.selectOne(queryWrapper);
    }

    public void update(OfflineOrder offlineOrder) {
        offlineOrderMapper.updateById(offlineOrder);
    }

    public BigDecimal sumWaitInvoiceAmount(Map<String, Object> paramMap) {
        return offlineOrderMapper.sumWaitInvoiceAmount(paramMap);
    }

	@Transactional(rollbackFor = Exception.class)
    public void cancelBatchOrder(OfflineOrder order) {
        log.info("[{}]==>批次取消发放",order.getPlatBatchNo());
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.PENDING_GRANT.getValue())
                && !Objects.equals(order.getBatchStatus(),OrderStatusEnum.IMPORTING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次状态已经变更，不能取消发放");
        }
        order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
        offlineOrderMapper.updateById(order);

        // 更新订单明细状态
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",order.getPlatBatchNo());
        offlineOrderItemMapper.cancelOrderItem(paramMap);

    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(OrderDeleteDTO orderDeleteDTO) {
        log.info("订单数据删除入参----------->{}",orderDeleteDTO);
        final OfflineOrder offlineOrder = this.getByPlatBatchNo(orderDeleteDTO.getPlatBatchNo());
        offlineOrder.setIsDelete(YesNoCodeEnum.YES.getValue());
        offlineOrder.setUpdateTime(new Date());
        offlineOrderMapper.updateById(offlineOrder);

        orderDeleteDTO.setIsDelete(YesNoCodeEnum.YES.getValue());
        orderDeleteDTO.setUpdateTime(new Date());
        int itemRes = offlineOrderItemMapper.updateByPlatBatchNo(orderDeleteDTO);
        log.info("订单详情删除数据----------->{}",itemRes);
    }

    public void generateOrderFee(String jsonParam) {
        OfflineOrder offlineOrder = JsonUtil.toBean(jsonParam,OfflineOrder.class);
        List<OfflineOrderItem> offlineOrderItemList = offlineOrderItemMapper.selectList(new QueryWrapper<OfflineOrderItem>().lambda()
                .eq(OfflineOrderItem::getPlatBatchNo,offlineOrder.getPlatBatchNo())
                .eq(OfflineOrderItem::getOrderItemStatus, OrderItemStatusEnum.GRANT_SUCCESS.getValue()));

        //查出合伙人
        // 查出一级合伙人
        RelevantAgent relevantAgent = merchantFacade.getRelevantAgentByMchNo(offlineOrder.getEmployerNo());
        boolean relevantAgentOpen = false;
        AgentDetailVo secondAgent = null;
        boolean secondAgentOpen = false;
        if (relevantAgent != null){
            relevantAgentOpen = agentProductRelationFacade.isOpenProduct(relevantAgent.getOneLevelAgentNo(), offlineOrder.getProductNo());
            //查出二级合伙人
            if (StringUtils.isNotBlank(relevantAgent.getTwoLevelAgentNo())) {
                secondAgent = agentFacade.getDetailByAgentNo(relevantAgent.getTwoLevelAgentNo());
                secondAgentOpen = agentProductRelationFacade.isOpenProduct(relevantAgent.getTwoLevelAgentNo(), offlineOrder.getProductNo());
            }
        }
        for (OfflineOrderItem x : offlineOrderItemList) {
            OrderCompleteVo completeVo = buildNotifyFeeVo(x,relevantAgent,relevantAgentOpen,secondAgent,secondAgentOpen);
            notifyFacade.sendOne(MessageMsgDest.TOPIC_ORDER_COMPLETE,
                    x.getEmployerNo(),
                    x.getPlatTrxNo(),
                    NotifyTypeEnum.ORDER_COMPLETE.getValue(),
                    null,
                    JsonUtil.toString(completeVo));
            log.info("发送外部订单计费消息，订单号：[{}]",x.getPlatTrxNo());
        }
    }

    private OrderCompleteVo buildNotifyFeeVo(OfflineOrderItem x, RelevantAgent relevantAgent, boolean relevantAgentOpen, AgentDetailVo secondAgent, boolean secondAgentOpen) {
        String orderItemNetAmountStr = String.valueOf(x.getOrderItemNetAmount());
        String employerNo = x.getEmployerNo();
        String mainstayNo = x.getMainstayNo();

        OrderCompleteVo vo = new OrderCompleteVo();
        vo.setTradeTime(x.getCompleteTime());
        vo.setMchNo(employerNo);
        vo.setMchName(x.getEmployerName());
        vo.setVendorNo(mainstayNo);
        vo.setVendorName(x.getMainstayName());
        vo.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        vo.setProductNo(x.getProductNo());
        vo.setProductName(x.getProductName());
        vo.setMchOrderNo(x.getMchOrderNo());
        vo.setPlatTrxNo(x.getPlatTrxNo());
        vo.setBaseOrderAmount(x.getOrderItemTaskAmount());
        vo.setOrderFee(x.getOrderItemFee());
        vo.setRemark(x.getRemark());
        vo.setChannelTrxNo(x.getPlatTrxNo());
        vo.setChannelOrderNo(x.getPlatTrxNo());
        vo.setStatus(PublicStatus.ACTIVE);
        SpecialRuleParamDto param1 = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.VERDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto param2 = new SpecialRuleParamDto(ProductFeeSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> merchantFeeDtoList = com.beust.jcommander.internal.Lists.newArrayList(param1,param2);
        vo.setMerchantFeeDtoList(merchantFeeDtoList);

        SpecialRuleParamDto param3 = new SpecialRuleParamDto(CostFeeSpecialRuleTypeEnum.MERCANT_NO.getValue(),employerNo);
        SpecialRuleParamDto param4 = new SpecialRuleParamDto(CostFeeSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> vendorFeeDtoList = com.beust.jcommander.internal.Lists.newArrayList(param3,param4);
        vo.setVendorFeeDtoList(vendorFeeDtoList);

        SpecialRuleParamDto param5 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.MERCHANT_NO.getValue(),employerNo);
        SpecialRuleParamDto param6 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.VENDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto param7 = new SpecialRuleParamDto(SalesSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> salesFeeDtoList = com.beust.jcommander.internal.Lists.newArrayList(param5,param6,param7);
        vo.setSalesFeeDtoList(salesFeeDtoList);

        SpecialRuleParamDto agentParam1 = new SpecialRuleParamDto(AgentSpecialRuleTypeEnum.VENDOR_NO.getValue(),mainstayNo);
        SpecialRuleParamDto agentParam2 = new SpecialRuleParamDto(AgentSpecialRuleTypeEnum.ORDER_AMOUNT.getValue(),orderItemNetAmountStr);
        List<SpecialRuleParamDto> agentFeeDtoList = com.beust.jcommander.internal.Lists.newArrayList(agentParam1, agentParam2);
        vo.setAgentFeeDtoList(agentFeeDtoList);

        if (relevantAgent != null && StringUtils.isNotEmpty(relevantAgent.getOneLevelAgentNo())) {
            vo.setOneLevelAgentNo(relevantAgent.getOneLevelAgentNo());
            vo.setOneLevelAgentName(relevantAgent.getOneLevelAgentName());
            vo.setOneLeveOpen(relevantAgentOpen);
            vo.setOneLevelAgentStatus(relevantAgent.getOneLevelAgentStatus());
            // 查出二级合伙人
            if (StringUtils.isNotBlank(relevantAgent.getTwoLevelAgentNo())) {
                AgentDetailVo secondAgen = agentFacade.getDetailByAgentNo(relevantAgent.getTwoLevelAgentNo());
                if (secondAgen != null) {
                    vo.setSecondLevelAgentNo(relevantAgent.getTwoLevelAgentNo());
                    vo.setSecondLevelAgentName(relevantAgent.getTwoLevelAgentName());
                    vo.setSecondLevelAgentStatus(relevantAgent.getTwoLevelAgentStatus());
                    vo.setSecondLeveOpen(secondAgentOpen);
                }
            }
        }
        return vo;
    }
}
