package com.zhixianghui.service.trade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.trade.dto.CmbIncomeRecordParamDTO;
import com.zhixianghui.facade.trade.dto.IdParamDTO;
import com.zhixianghui.facade.trade.service.CmbIncomeRecordFacade;
import com.zhixianghui.facade.trade.vo.CmbIncomeAuditVO;
import com.zhixianghui.facade.trade.vo.CmbIncomeRecordVO;
import com.zhixianghui.facade.trade.vo.MerchantInfoVO;
import com.zhixianghui.service.trade.biz.CmbIncomeRecordBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CmbIncomeRecordFacadeImpl implements CmbIncomeRecordFacade {

    private final CmbIncomeRecordBiz cmbIncomeRecordBiz;

    @Override
    public RestResult<String> auditCmbIncomeRecord(CmbIncomeAuditVO param) {
        return cmbIncomeRecordBiz.auditCmbIncomeRecord(param);
    }

    @Override
    public IPage<CmbIncomeRecordVO> listPage(CmbIncomeRecordParamDTO param) {
        return cmbIncomeRecordBiz.listPage(param);
    }

    @Override
    public List<MerchantInfoVO> listMerchantInfo(IdParamDTO param) {
        return cmbIncomeRecordBiz.listMerchantInfo(param);
    }

    @Override
    public void exportCmbIncomeRecord(CmbIncomeRecordParamDTO param, String loginName) {
        cmbIncomeRecordBiz.exportCmbIncomeRecord(param, loginName);
    }
}
