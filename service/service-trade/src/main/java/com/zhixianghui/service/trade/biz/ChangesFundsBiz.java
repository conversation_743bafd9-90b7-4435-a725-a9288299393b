package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.trade.dto.ChangesFoundsDTO;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.vo.ChangesFundsVo;
import com.zhixianghui.service.trade.dao.ChangesFundsDao;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* Biz
*
* <AUTHOR>
* @version 创建时间： 2021-12-09
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChangesFundsBiz {

    private final ChangesFundsDao changesfundsDao;

    public void insert(ChangesFunds changesFunds) {
        changesfundsDao.insert(changesFunds);
    }

    public PageResult<List<ChangesFundsVo>> listPage(ChangesFoundsDTO changesFoundsDTO) {
        Map<String, Object> map = BeanUtil.toMap(changesFoundsDTO);
        PageParam pageParam=new PageParam();
        pageParam.setPageSize(changesFoundsDTO.getPageSize());
        pageParam.setPageCurrent(changesFoundsDTO.getPageCurrent());
        PageResult<List<ChangesFunds>> listPageResult = changesfundsDao.listPage(map, pageParam);
        List<ChangesFunds> data = listPageResult.getData();
        List<ChangesFundsVo> list=data.stream().map(e->{
            ChangesFundsVo changesFundsVo = new ChangesFundsVo();
            changesFundsVo.setLogKey(e.getLogKey());
            changesFundsVo.setMchNo(e.getMchNo());
            changesFundsVo.setMchName(e.getMchName());
            changesFundsVo.setMainstayNo(e.getMainstayNo());
            changesFundsVo.setMainstayName(e.getMainstayName());
            changesFundsVo.setAmount(AmountUtil.changeToYuan(formatNull(e.getAmount())));
            changesFundsVo.setFrozenAmount(AmountUtil.changeToYuan(formatNull(e.getFrozenAmount())));
            changesFundsVo.setPlatTrxNo(e.getPlatTrxNo());
            changesFundsVo.setCreateTime(e.getCreateTime());
            changesFundsVo.setAmountChangeType(e.getAmountChangeType());
            return changesFundsVo;
        }).collect(Collectors.toList());
        PageResult<List<ChangesFundsVo>> page=new PageResult<>();
        page.setTotalRecord(listPageResult.getTotalRecord());
        page.setPageCurrent(listPageResult.getPageCurrent());
        page.setPageSize(listPageResult.getPageSize());
        page.setData(list);
        return page;
    }

    private Long formatNull(Long money){
        return money==null?0L:money;
    }
}