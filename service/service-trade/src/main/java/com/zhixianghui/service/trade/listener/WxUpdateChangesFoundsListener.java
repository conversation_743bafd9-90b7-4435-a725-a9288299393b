package com.zhixianghui.service.trade.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.service.trade.biz.WxPayQueryBiz;
import com.zhixianghui.service.trade.pay.wx.biz.WxOrderUpdateBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月07日 09:55:00
 */
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_PAY_QUERY,
        selectorExpression = MessageMsgDest.TAG_WX_PAY_CHANGE_FOUNDS,consumeThreadMax = 20,
        consumerGroup = "wxUpdateChangesFoundsConsume")
public class WxUpdateChangesFoundsListener extends BaseRocketMQListener<String> {

    @Autowired
    private WxOrderUpdateBiz wxOrderUpdateBiz;

    @Override
    public void validateJsonParam(String msg) {
        if(StringUtils.isEmpty(msg)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单号不能为空");
        }
    }

    @Override
    public void consumeMessage(String msg) {
        ChangesFunds changesFunds = JsonUtil.toBean(msg, ChangesFunds.class);
        wxOrderUpdateBiz.updateChangesFunds(changesFunds);
    }
}