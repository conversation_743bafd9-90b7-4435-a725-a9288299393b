package com.zhixianghui.service.trade.utils;

import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.AlgorithmParameters;
import java.security.Security;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2021/8/25 10:02
 */


public class WeChatUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(WeChatUtil.class);


    public static void main(String[] args) {
        String enc = "ISNQLzUrrOuEl2QvRuIsi21LLX8VJvjGto1YIV3fUZRBQ5Xo73uutpWZI/iwsh+LZLlahE5typWKsczCYCNtkv5+MeqjD2y/nILaIBTXZ8gpMBj6fXcgP+d0rJDZqF3ANk2Bl0FipvcEdKkAygQIaofYzHpYdpxdWqYLk4u6D0Td2UFg4hnCDa1emPL3PFJh8ksbf3DQMgrwMRIgjcC1lA==";
        String iv = "R6J8xg3+3B38UlDzibhuOw==";
        String session = "F0pfjzx/88bHR0YTltBD+g==";
        System.out.println(getUserInfo(enc, session, iv));
    }

    public static String getUserInfo(String encryptedData, String sessionKey, String iv){
        // 被加密的数据
        byte[] dataByte = Base64.decode(encryptedData);
        // 加密秘钥
        byte[] keyByte = Base64.decode(sessionKey);
        // 偏移量
        byte[] ivByte = Base64.decode(iv);

        try {
            // 如果密钥不足16位，那么就补足.  这个if中的内容很重要
            int base = 16;
            if (keyByte.length % base != 0) {
                int groups = keyByte.length / base + 1;
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyByte, 0, temp, 0, keyByte.length);
                keyByte = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding","BC");
            SecretKeySpec spec = new SecretKeySpec(keyByte, "AES");
            AlgorithmParameters parameters = AlgorithmParameters.getInstance("AES");
            parameters.init(new IvParameterSpec(ivByte));
            cipher.init(Cipher.DECRYPT_MODE, spec, parameters);// 初始化
            byte[] resultByte = cipher.doFinal(dataByte);
            if (null != resultByte && resultByte.length > 0) {
                return new String(resultByte, StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            LOGGER.error("解密失败: {}-{}-{}",encryptedData, sessionKey, iv, e);
        }
        return null;
    }
}
