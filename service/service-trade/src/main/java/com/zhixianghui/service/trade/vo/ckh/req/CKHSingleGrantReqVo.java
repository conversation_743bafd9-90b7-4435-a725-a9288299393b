package com.zhixianghui.service.trade.vo.ckh.req;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @ClassName CkhSingleGrantReqVo
 * @Description TODO
 * @Date 2022/10/31 14:57
 */
@Data
public class CKHSingleGrantReqVo {

    /**
     * 商户订单号
     */
    @NotBlank(message = "mch_order_no 订单号不能为空")
    @Length(min = 5, max = 32, message = "mch_order_no 订单号长度有误，应在[5-32]之间")
    private String mchOrderNo;

    /**
     * 发放方式
     */
    @NotNull(message = "channel_type 发放方式不能为空")
    private Integer channelType;

    @NotBlank(message = "job_no 任务编号不能为空")
    private String jobNo;

    /**
     * 代征主体编号
     */
    @NotBlank(message = "mainstay_no 代征主体编号不能为空")
    private String mainstayNo;

    /**
     * 持卡人姓名
     */
    @NotBlank(message = "receive_name 姓名不能为空")
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    @NotBlank(message = "receive_id_card_no 身份证号不能为空")
    private String receiveIdCardNo;

    /**
     * 收款账户(卡号/支付宝账号)
     */
    @NotBlank(message = "receive_account_no 收款账户不能为空")
    private String receiveAccountNo;

    /**
     * 收款人手机号
     */
    private String receivePhoneNo;

    /**
     * 任务金额
     */
    @NotBlank(message = "order_item_task_amount 任务金额不能为空")
    private String orderItemTaskAmount;

    /**
     * 打款备注
     */
    @Length(max = 20, message = "remark 打款备注长度最多不能超过20")
    private String remark;

    @Length(max = 30, message = "备忘录 长度不能超过30")
    private String memo;
    /**
     * 回调地址
     */
    private String callbackUrl;
}
