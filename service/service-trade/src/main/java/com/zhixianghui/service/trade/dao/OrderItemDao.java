package com.zhixianghui.service.trade.dao;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.bo.OrderItemCountBo;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.bo.OrderItemTrxIdBo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单明细表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Repository
@Slf4j
public class OrderItemDao extends MyBatisDao<OrderItem,Long> {

    /**
     * 分页查询流水号
     * @param paramMap 查询条件
     * @param pageParam 分页参数
     * @return 流水号
     */
    public PageResult<List<OrderItemTrxIdBo>> listTrxNoIdPage(Map<String, Object> paramMap, PageParam pageParam) {
        long totalRecord = 0L;
        List<OrderItemTrxIdBo> dataList;
        if (pageParam.isNeedTotalRecord()) {
            totalRecord = this.countBy("countBy", paramMap);
            if (totalRecord <= 0) {
                //如果总记录数为0，就直接返回了
                dataList = new ArrayList<>();
                return PageResult.newInstance(dataList, pageParam, totalRecord);
            }
        }

        if (isNotEmpty(pageParam.getSortColumns())) {
            if (paramMap == null) {
                paramMap = Maps.newHashMap();
            }
            paramMap.put(SORT_COLUMNS, this.filterSortColumns(pageParam.getSortColumns()));
        }
        dataList = this.getSqlSession().selectList(fillSqlId("listTrxNoAndIdPage"), paramMap,
                new RowBounds(getOffset(pageParam), pageParam.getPageSize()));
        if (!pageParam.isNeedTotalRecord()) {
            totalRecord = dataList.size();
        }
        return PageResult.newInstance(dataList, pageParam, totalRecord);
    }

    public OrderItemCountBo getCountBoByPlatBatchNo(Map<String,Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("getCountBo"),paramMap);
    }

    public void cancelOrderItem(Map<String,Object> paramMap) {
        this.getSqlSession().update(fillSqlId("cancelOrderItem"),paramMap);
    }

    public OrderItemSumBo sumOrderItem(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("sumOrderItem"),paramMap);
    }

    public BigDecimal sumOrderItemWaitInvoiceAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("sumOrderItemWaitInvoiceAmount"),paramMap);
    }

    public BigDecimal sumOrderItemWaitGrantInvoiceAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("sumOrderItemWaitGrantInvoiceAmount"),paramMap);
    }

    public BigDecimal sumOrderItemWaitServiceFeeInvoiceAmount(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("sumOrderItemWaitServiceFeeInvoiceAmount"),paramMap);
    }

    public List<Map<String, Object>> sumCmbOrderAmt(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("sumCmbOrderAmt"), paramMap);
    }

    public List<Map<String, Object>> employerTradeCheck(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("employerTradeCheck"), paramMap);
    }

    public List<Map<String, Object>> listHasTradeEmp(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("listHasTradeEmp"), paramMap);
    }

    public Map<String, Date> selectLastOrderTime(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("selectLastOrderTime"), paramMap);
    }

    public Map<String, BigDecimal> queryUserMonthGrantData(Map<String, Object> paramMap) {
        log.info("[queryUserMonthGrantData]查询参数 {}", JSONUtil.toJsonPrettyStr(paramMap));
        return this.getSqlSession().selectOne(fillSqlId("queryUserMonthGrantData"), paramMap);
    }

    public OrderItem getGrantUserByIdCard(Map<String, Object> paramMap) {
        return this.getSqlSession().selectOne(fillSqlId("getGrantUserByIdCard"), paramMap);
    }

    public List<Map<String,String>> listGrantOrderBatches(Map<String, Object> paramMap) {
        return this.getSqlSession().selectList(fillSqlId("listGrantOrderBatches"), paramMap);
    }
}
