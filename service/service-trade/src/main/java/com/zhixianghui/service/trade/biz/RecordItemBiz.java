package com.zhixianghui.service.trade.biz;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.trade.bo.RecordItemGroupBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.constant.UniqueKeyConst;
import com.zhixianghui.facade.trade.dto.RiskControlAmountDto;
import com.zhixianghui.facade.trade.dto.RiskControlNumDto;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.facade.trade.vo.ServiceConfirmVo;
import com.zhixianghui.facade.trade.vo.UserGrantVo;
import com.zhixianghui.service.trade.dao.RecordItemDao;
import com.zhixianghui.service.trade.helper.TradeHelperBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
* 打款交易流水表Biz
*
* <AUTHOR>
* @version 创建时间： 2020-11-03
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class RecordItemBiz {

    private final RecordItemDao recordItemDao;
    private final TradeHelperBiz tradeHelperBiz;
    private final TradeUniqueBiz tradeUniqueBiz;

    @Transactional(rollbackFor = Exception.class)
    public void insert(RecordItem recordItem) {
        tradeUniqueBiz.insertByKey(UniqueKeyConst.REMIT_PLAT_TRX_NO + recordItem.getRemitPlatTrxNo());
        recordItemDao.insert(recordItem);
    }

    public void update(RecordItem recordItem) {
        recordItemDao.update(recordItem);
    }

    public RecordItem getByRemitPlatTrxNo(String remitPlatTrxNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("remitPlatTrxNo",remitPlatTrxNo);
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.getOne(paramMap);
    }

    public RecordItem getByPlatTrxNo(String platTrxNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platTrxNo",platTrxNo);
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.getOne(paramMap);
    }

    public PageResult<List<RecordItem>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return PageResult.newInstance(pageParam,new ArrayList<>());
        }
        tradeHelperBiz.replacePrivacy(paramMap);
        return recordItemDao.listPage(paramMap,pageParam);
    }

    public List<RecordItem> list(Map<String, Object> paramMap) {
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return new ArrayList<>();
        }
        tradeHelperBiz.replacePrivacy(paramMap);
        return recordItemDao.listBy(paramMap);
    }

    /**
     * 获取发放金额
     * @param beginDate 开始时间，一般比completeBeginTime前，由于定位分表所在表
     * @param endDate 截止时间，由于定位分表所在表
     * @param updateBeginTime 统计开始时间
     * @param updateEndTime 统计截止时间
     * @param mainstayNo 代征主体no
     * @param receiveIdCardNo 接收者身份证原文
     * @return
     */
    public BigDecimal getSumAmount(Date beginDate, Date endDate, Date updateBeginTime,
                                   Date updateEndTime, String mainstayNo, String receiveIdCardNo) {
        if (StringUtil.isEmpty(mainstayNo) || StringUtil.isEmpty(receiveIdCardNo)) {
            return null;
        }
        Map<String,Object> params = new HashMap<>();
        params.put("beginDate", beginDate);
        params.put("endDate", endDate);
        params.put("updateBeginTime", updateBeginTime);
        params.put("updateEndTime", updateEndTime);
        params.put("mainstayNo", mainstayNo);
        params.put("receiveIdCardNoMd5", receiveIdCardNo);
        return recordItemDao.getAmountResult(params);
    }

    /**
     * 获取idcard + mainstay的组合
     * @param beginDate
     * @param endDate
     * @param offset
     * @param pageSize
     * @return
     */
    public List<RecordItemGroupBo> getIdcardNoMainstayGroup(Date beginDate, Date endDate,Date startDateTime,Date endDateTime, int offset, int pageSize) {
        if (beginDate == null || endDate == null) {
            return null;
        }
        Map<String,Object> params = new HashMap<>();
        params.put("beginDate", beginDate);
        params.put("endDate", endDate);
        params.put("beginDateTime",startDateTime);
        params.put("endDateTime",endDateTime);
        return recordItemDao.getIdcardNoMainstayGroup(params, offset, pageSize);
    }

    public RecordItem getOne(Map<String,Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return null;
        }
        return recordItemDao.getOne(paramMap);
    }

    public Long countRecordItem(Map<String, Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return 0L;
        }
        tradeHelperBiz.replacePrivacy(paramMap);
        return recordItemDao.countBy(paramMap);
    }

    public RecordItem getByChannelTrxNo(String channelTrxNo) {
        if (StringUtil.isEmpty(channelTrxNo)) {
            return null;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("channelTrxNo", channelTrxNo);
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        if(paramMap.get(TradeConstant.ORDER_NOT_EXIST_FLAG) != null){
            return null;
        }
        return recordItemDao.getOne(paramMap);
    }

    public List<RecordItem> listByTime(HashMap<String, Object> paramMap, PageParam pageParam) {
        PageResult<List<RecordItem>> pageResult = recordItemDao.listPage("listByTime", paramMap, pageParam);
        if (pageResult == null) {
            return null;
        }
        return pageResult.getData();

    }

    public Map<String, Object> coreIndexStatistics(Map<String, Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.coreIndexStatistics(paramMap);
    }

    public Map<String, Object> coreIndexReceivedUserCount(Map<String, Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.coreIndexReceivedUserCount(paramMap);
    }

    public List<Map<String, Object>> coreIndexDailyDetail(Map<String, Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.coreIndexDailyDetail(paramMap);
    }

    public List<Map<String, Object>> coreIndexDailyDetailReceivedUserCount(Map<String, Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.coreIndexDailyDetailReceivedUserCount(paramMap);
    }

    public List<Map<String, Object>> coreIndexDetailMonthly(Map<String, Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.coreIndexDetailMonthly(paramMap);
    }

    public List<Map<String, Object>> coreIndexDetailMonthlyReceivedUserCount(Map<String, Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.coreIndexDetailMonthlyReceivedUserCount(paramMap);
    }

    public List<ServiceConfirmVo> getWorkCategoryCode(Map<String, Object> paramMap, int offset, Integer pageSize) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.getWorkCategoryCode(paramMap,offset,pageSize);
    }

    public List<RecordItem> listByOffset(Map<String, Object> paramMap, int offset, int maxSize) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        return recordItemDao.listByOffset(paramMap,offset,maxSize);
    }

    public Integer getWorkCategoryCodeCount(Map<String, Object> paramMap) {
//        tradeHelperBiz.putRecordItemCreateDateToMap(paramMap);
        //sharedingjdbc不支持count子查询...
        List<String> list = recordItemDao.getWorkCategoryCodeCount(paramMap);
        return list.size();
    }

    public Map<String, RiskControlAmountDto> getSumAmountGroup(Date beginDate, Date endDate, Date updateBeginTime,
                                                               Date updateEndTime, String receiveIdCardNo) {
        if (StringUtil.isEmpty(receiveIdCardNo)) {
            return null;
        }
        Map<String,Object> params = new HashMap<>();
        params.put("beginDate", beginDate);
        params.put("endDate", DateUtil.addDay(endDate,1));
        params.put("beginDateTime", DateUtil.getDayStart(beginDate));
        params.put("endDateTime",DateUtil.addDay(DateUtil.getDayEnd(endDate),1));
        params.put("updateBeginTime", updateBeginTime);
        params.put("updateEndTime", updateEndTime);
        params.put("receiveIdCardNoMd5", receiveIdCardNo);
        return recordItemDao.getAmountResultGroup(params);
    }

    public Map<String, RiskControlAmountDto> getMchAmountGroup(Date beginDate, Date endDate, Date updateBeginTime, Date updateEndTime, String employerNo) {
        Map<String,Object> params = new HashMap<>();
        params.put("beginDate", beginDate);
        params.put("endDate", DateUtil.addDay(endDate,1));
        params.put("beginDateTime", DateUtil.getDayStart(beginDate));
        params.put("endDateTime",DateUtil.addDay(DateUtil.getDayEnd(endDate),1));
        params.put("updateBeginTime", updateBeginTime);
        params.put("updateEndTime", updateEndTime);
        params.put("employerNo", employerNo);
        log.info(JSONUtil.toJsonPrettyStr(params));
        return recordItemDao.getAmountResultGroup(params);
    }

    public Map<String,BigDecimal> getWaiteGrantSumAmount(String platBatchNo, RecordItemStatusEnum recordItemStatusEnum) {
        Map<String,Object> params = new HashMap<>();
        params.put("platBatchNo", platBatchNo);
        params.put("processStatus", recordItemStatusEnum.getValue());
        return recordItemDao.getWaiteGrantSumAmount(params);
    }

    public Map<String, RiskControlNumDto> getCountTradeTimes(String platTrxNo,String employerNo, Date endTime, Date startTime, String idCardNo, String userName, String receiveAccount, BigDecimal orderAmount) {
        Map<String,Object> params = new HashMap<>();
        params.put("platTrxNo",platTrxNo);
        params.put("beginDateTime",startTime);
        params.put("endDateTime",endTime);
        params.put("employerNo",employerNo);
        params.put("receiveIdCardNoMd5",idCardNo);
        params.put("receiveNameMd5",userName);
        params.put("receiveAccountNoMd5",receiveAccount);
        params.put("orderNetAmount",orderAmount);
        return recordItemDao.getCountTradeTimes(params);
    }

    public Map<String, UserGrantVo> sumGrantSuccessGroupMonth(Date createDateStart, Date dayEnd, Date rangeDayStart, Date rangeDayEnd, String receiveIdCardNoMd5) {
        Map<String,Object> params = new HashMap<>();
        params.put("beginCreateTime",createDateStart);
        params.put("endCreateTime",dayEnd);
        params.put("rangeDayStart",rangeDayStart);
        params.put("rangeDayEnd",rangeDayEnd);
        params.put("receiveIdCardNoMd5",receiveIdCardNoMd5);
        params.put("processStatus",RecordItemStatusEnum.PAY_SUCCESS.getValue());
        return recordItemDao.sumGrantSuccessGroupMonth(params);
    }

    public List<UserGrantVo> listGrantSuccessByIdCard(Date createDateStart, Date dayEnd, Date rangeDayStart, Date rangeDayEnd, String receiveIdCardNoMd5) {
        Map<String,Object> params = new HashMap<>();
        params.put("beginCreateTime",createDateStart);
        params.put("endCreateTime",dayEnd);
        params.put("rangeDayStart",rangeDayStart);
        params.put("rangeDayEnd",rangeDayEnd);
        params.put("receiveIdCardNoMd5",receiveIdCardNoMd5);
        params.put("processStatus",RecordItemStatusEnum.PAY_SUCCESS.getValue());
        return recordItemDao.listGrantSuccessByIdCard(params);
    }

    public BigDecimal getGrantSuccessSumAmount(Date recentCreateDateStart, Date recentCreateDateEnd, Date recentDayStart, Date now, String receiveIdCardNoMd5) {
        Map<String,Object> params = new HashMap<>();
        params.put("beginCreateTime",recentCreateDateStart);
        params.put("endCreateTime",recentCreateDateEnd);
        params.put("rangeDayStart",recentDayStart);
        params.put("rangeDayEnd",now);
        params.put("receiveIdCardNoMd5",receiveIdCardNoMd5);
        params.put("processStatus",RecordItemStatusEnum.PAY_SUCCESS.getValue());
        return recordItemDao.getUserGrantSumAmount(params);
    }
}
