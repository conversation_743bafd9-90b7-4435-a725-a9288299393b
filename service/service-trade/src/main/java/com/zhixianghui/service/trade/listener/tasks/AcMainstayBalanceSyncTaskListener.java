package com.zhixianghui.service.trade.listener.tasks;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.banklink.service.joinpay.JoinPayFacade;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.service.trade.biz.AcLocalPayBiz;
import com.zhixianghui.service.trade.biz.AcMerchantBalanceBiz;
import com.zhixianghui.service.trade.dao.AcMerchantBalanceDao;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MAINSTAY_BALANCE_SYNC, consumeThreadMax = 1, consumerGroup = "AcMainstayBalanceSyncComsumer")
public class AcMainstayBalanceSyncTaskListener extends TaskRocketMQListener<JSONObject> {
    @Autowired
    private AcLocalPayBiz acLocalPayBiz;

    @Override
    public void runTask(JSONObject jsonParam) {
        acLocalPayBiz.mainstayBalanceSync((Boolean)jsonParam.get("isDeleteRecord"));
    }
}
