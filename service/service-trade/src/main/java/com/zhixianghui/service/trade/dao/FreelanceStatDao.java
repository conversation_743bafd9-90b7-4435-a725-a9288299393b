package com.zhixianghui.service.trade.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.UserInfo;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 自由职业者月统计表 Mapper 接口
 * <AUTHOR>
 * @version 创建时间： 2021-08-02
 */
@Repository
public class FreelanceStatDao extends MyBatisDao<FreelanceStat,Long> {

    public PageResult<List<Map<String, Object>>> listPage2(Map<String, Object> param, PageParam pageParam) {
        return this.listPage(fillSqlId("listBy2"), param, pageParam);
    }

    public PageResult<List<UserInfo>> idCardList(Map<String, Object> param, PageParam pageParam) {
        return this.listPage(fillSqlId("idCardList"), param, pageParam);
    }
}
