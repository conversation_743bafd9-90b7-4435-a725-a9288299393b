package com.zhixianghui.service.trade.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.service.utils.MybatisUtil;
import com.zhixianghui.facade.trade.entity.OfflineOrderItemFail;
import com.zhixianghui.service.trade.dao.mapper.OfflineOrderItemFailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class OfflineOrderItemFailBiz {

    @Autowired
    public OfflineOrderItemFailMapper itemFailMapper;

    public List<OfflineOrderItemFail> listBy(Map<String,Object> param) {
        QueryWrapper<OfflineOrderItemFail> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, OfflineOrderItemFail.class, param);
        return itemFailMapper.selectList(queryWrapper);
    }

    public Page<OfflineOrderItemFail> pageBy(Map<String, Object> param, Page page) {
        QueryWrapper<OfflineOrderItemFail> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper, OfflineOrderItemFail.class, param);
        return itemFailMapper.selectPage(page, queryWrapper);
    }

    public void saveFailItem(OfflineOrderItemFail offlineOrderItemFail) {
        itemFailMapper.insert(offlineOrderItemFail);
    }

}
