package com.zhixianghui.service.trade.dao;

import com.google.common.collect.ImmutableMap;
import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.trade.entity.AcIncomeRecord;
import org.springframework.stereotype.Repository;


/**
 * 来账记录信息Dao接口
 * <AUTHOR> @date 2024-04-01
 */
@Repository
public class AcIncomeRecordDao extends MyBatisDao<AcIncomeRecord, Long> {

    /***
     * 根据渠道流水号查询数据
     * @param incomeTrxNo
     * @return
     */
    public AcIncomeRecord getByTrxNo(String incomeTrxNo) {
        LimitUtil.notEmpty(incomeTrxNo, "流水号不能为空");
        return getOne(ImmutableMap.of("trxNo", incomeTrxNo));
    }
}
