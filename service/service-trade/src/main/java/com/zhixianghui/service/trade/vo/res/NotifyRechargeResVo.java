package com.zhixianghui.service.trade.vo.res;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName NotifyRechargeResVo
 * @Description TODO
 * @Date 2023/7/10 10:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JSONType(naming= PropertyNamingStrategy.SnakeCase)
public class NotifyRechargeResVo extends ApiBizBaseDto {

    /**
     * 充值回调id
     */
    private String rechargeOrderNo;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 账户类型
     */
    private Integer channelType;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 收款方户名
     */
    private String payeeName;

    /**
     * 收款方账号
     */
    private String payeeAccountNo;

    /**
     * 付款方户名
     */
    private String payerName;

    /**
     * 付款方账号
     */
    private String payerAccountNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 充值金额
     */
    private String amount;

    /**
     * 充值类型
     */
    private Integer rechargeType;

    /**
     * 订单状态
     */
    private Integer rechargeStatus;
}
