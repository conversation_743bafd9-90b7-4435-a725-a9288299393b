package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import com.zhixianghui.facade.trade.vo.ApiBillVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName BillQueryResVo
 * @Description TODO
 * @Date 2022/10/8 11:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BillQueryResVo extends ApiBizBaseDto {

    private List<ApiBillVo> fileList;
}
