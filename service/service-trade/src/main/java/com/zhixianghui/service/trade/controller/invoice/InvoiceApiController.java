package com.zhixianghui.service.trade.controller.invoice;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.common.statics.enums.account.AccountStatusEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.invoice.AccountHandleStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.ApplyTypeEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceAmountTypeEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceManageFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.service.MerchantExpressInfoFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantInvoiceInfoFacade;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.enums.InvoiceSourceEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.service.trade.biz.InvoiceRecordBiz;
import com.zhixianghui.service.trade.vo.req.ApplyInvoiceReqVo;
import com.zhixianghui.service.trade.vo.req.InvoiceAccountReqVo;
import com.zhixianghui.service.trade.vo.req.InvoiceRecordReqVo;
import com.zhixianghui.service.trade.vo.res.InvoiceAccountResVo;
import com.zhixianghui.service.trade.vo.res.InvoiceRecordResVo;
import com.zhixianghui.service.trade.vo.res.WaitIssueAmountQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName InvoiceApiController
 * @Description TODO
 * @Date 2022/9/27 16:31
 */
@Slf4j
@RestController
@RequestMapping("zxh")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InvoiceApiController {

    @Reference
    private AccountInvoiceManageFacade invoiceManageFacade;
    @Reference
    private MerchantInvoiceInfoFacade merchantInvoiceInfoFacade;
    @Reference
    private MerchantExpressInfoFacade merchantExpressInfoFacade;
    @Reference
    private InvoiceFacade invoiceFacade;
    @Reference
    private OrderFacade orderFacade;
    @Reference
    private VendorFacade vendorFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;


    private final InvoiceRecordBiz invoiceRecordBiz;

    @PostMapping("/invoiceRecord")
    public ResponseDto<InvoiceRecordResVo> invoiceRecord(@Valid @RequestBody RequestDto<InvoiceRecordReqVo> paramVo) {
        InvoiceRecordReqVo reqVo = paramVo.getData();
        String mainstayNo = reqVo.getMainstayNo();
        String employerNo = paramVo.getMchNo();
        try {
            Date beginDate = DateUtil.stringToDateTime(reqVo.getBeginDate());
            Date endDate = DateUtil.addDay(DateUtil.stringToDateTime(reqVo.getEndDate()), 1);
            paramValid(beginDate, endDate);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("productNo", ProductNoEnum.ZXH.getValue());
            paramMap.put("createTimeBegin", beginDate);
            paramMap.put("createTimeEnd", endDate);
            paramMap.put("ignoreInvoiceStatus", InvoiceStatusEnum.EXCEPTION.getValue());
            paramMap.put("mainstayMchNo", mainstayNo);
            paramMap.put("employerMchNo", employerNo);
            InvoiceRecordResVo resVo = invoiceRecordBiz.listRecordApi(paramMap);
            //错误描述
            resVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            resVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<InvoiceRecordResVo> returnVo = ResponseDto.success(resVo, "");
            return returnVo;
        } catch (BizException e) {
            return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
        } catch (Exception e) {
            return ResponseDto.unknown();
        }
    }

    private void paramValid(Date beginDate, Date endDate) {
        if (endDate.before(beginDate)) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("日期范围校验失败");
        }
        if (beginDate.before(DateUtil.addYear(endDate, -1))) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("日期间隔不能超过一年");
        }
    }

    @PostMapping("/invoiceAccount")
    public ResponseDto<InvoiceAccountResVo> invoiceAccount(@RequestBody RequestDto<InvoiceAccountReqVo> paramVo) {
        InvoiceAccountReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        String mainstayNo = reqVo.getMainstayNo();
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("employerMchNo", employerNo);
            paramMap.put("mainstayMchNo", mainstayNo);
            paramMap.put("status", AccountStatusEnum.ACTIVE.getValue());
            paramMap.put("ignoreInvoiceStatus", InvoiceStatusEnum.EXCEPTION.getValue());
            Map<String, Object> accountMap = invoiceManageFacade.statisticsAccountInvoice(paramMap);
            Long totalCount = (Long) accountMap.get("total_count");
            if (totalCount == 0L) {
                throw ApiExceptions.API_INVOICE_ACCOUNT_NOT_EXIST;
            }
            //查询开票记录
            Map<String, Object> invoiceMap = invoiceRecordBiz.countInvoiceAmount(paramMap);
            InvoiceAccountResVo invoiceAccountResVo = new InvoiceAccountResVo();
            invoiceAccountResVo.setInvoicedAmount(invoiceMap.get("invoiceAmount").toString());
            invoiceAccountResVo.setInvoiceBalance(accountMap.get("total_amount").toString());
            //错误描述
            invoiceAccountResVo.setBizErrCode(BizCodeEnum.SUCCESS.getCode());
            invoiceAccountResVo.setBizErrMsg(BizCodeEnum.SUCCESS.getMsg());
            ResponseDto<InvoiceAccountResVo> returnVo = ResponseDto.success(invoiceAccountResVo, "");
            return returnVo;
        } catch (BizException e) {
            return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
        } catch (Exception e) {
            return ResponseDto.unknown();
        }
    }

    @PostMapping("/invoiceApply")
    public ResponseDto<InvoiceRecordResVo> invoiceApply(@Validated @RequestBody RequestDto<ApplyInvoiceReqVo> paramVo) {
        ApplyInvoiceReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        String mainstayNo = reqVo.getMainstayNo();
        String productNo = reqVo.getProductNo();
        if (StringUtil.isEmpty(productNo)) {
            productNo = ProductNoEnum.ZXH.getValue();
        }

        if (!DateUtil.parse(reqVo.getTradeCompleteDayEnd()).before(DateUtil.getDayStart(new Date()))) {
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("开票终止日期错误，终止日期须为当日以前，不含当日");
        }


        Merchant empMch = merchantFacade.getByMchNo(employerNo);
        Merchant mainstayMch = merchantFacade.getByMchNo(mainstayNo);

        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoFacade.getByMchNo(employerNo);
        if (merchantInvoiceInfo == null) {
            log.info("商户:{},供应商:{}({})-没有发票信息，终止开票", empMch.getMchName(), mainstayNo, mainstayMch.getMchName());
            return  ResponseDto.fail(ApiExceptions.API_INVOICE_INFO_NOT_EXIST.getApiErrorCode(), ApiExceptions.API_INVOICE_INFO_NOT_EXIST.getErrMsg());
        }
        MerchantExpressInfo merchantExpressInfo = merchantExpressInfoFacade.getByMchNo(employerNo);
        if (merchantExpressInfo == null) {
            log.info("商户:{},供应商:{}({})-没有快递信息，终止开票", empMch.getMchName(), mainstayNo, mainstayMch.getMchName());
            return  ResponseDto.fail(ApiExceptions.API_INVOICE_EXPRESSINFO_NOT_EXIST.getApiErrorCode(), ApiExceptions.API_INVOICE_EXPRESSINFO_NOT_EXIST.getErrMsg());
        }

        final Vendor vendor = vendorFacade.getVendorByNo(mainstayNo);
        if (vendor == null) {
            return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), "你的商户未与该代征主体建立代征关系");
        }

        if (!StringUtils.equals(vendor.getProductNo(), ProductNoEnum.ZXH.getValue())) {
            return  ResponseDto.fail(ApiExceptions.API_BIZ_FAIL.getApiErrorCode(), "该产品暂不支持接口申请开票");
        }
        Integer amountType = InvoiceAmountTypeEnum.ORDER_AMOUNT.getValue();
        List<String> trxNos = new ArrayList<>();

        try {
            List<MerchantEmployerPosition> employerPositions = merchantEmployerPositionFacade.listByMchNo(employerNo);

            if (employerPositions != null) {
                log.info(JSONUtil.toJsonPrettyStr(employerPositions));
                for (MerchantEmployerPosition employerPosition : employerPositions) {
                    String applyTradeCompleteDayBegin = invoiceFacade.getApplyTradeCompleteDayBegin(employerNo, mainstayNo,
                            null, null, InvoiceSourceEnum.ON_LINE.getCode(), employerPosition.getWorkCategoryCode(),
                            productNo, amountType);

                    if (DateUtil.parse(applyTradeCompleteDayBegin).after(DateUtil.parse(reqVo.getTradeCompleteDayEnd()))) {
                        log.info("[{}-{}-{}] 开票截止日期小于开票开始日期，终止该岗位类目开票", employerNo, mainstayNo, employerPosition.getWorkCategoryCode());
                    }
                    log.info("{}-{}-{}-{}:开始申请开票", employerNo, mainstayNo, employerPosition.getWorkCategoryName(), applyTradeCompleteDayBegin);

                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("employerNo", employerNo);
                    paramMap.put("mainstayNo", mainstayNo);
                    paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
                    paramMap.put("completeBeginDate", DateUtil.parseTime(applyTradeCompleteDayBegin + " 00:00:00"));
                    paramMap.put("completeEndDate", DateUtil.getDayEnd(DateUtil.parse(reqVo.getTradeCompleteDayEnd())));

                    // 加入创建时间以优化查询数组，创建时间范围为完成时间往前提3个月
                    paramMap.put("createBeginDate", DateUtil.addMonth(DateUtil.parseTime(applyTradeCompleteDayBegin + " 00:00:00"), -3));
                    paramMap.put("createEndDate", DateUtil.getDayEnd(DateUtil.parse(reqVo.getTradeCompleteDayEnd())));
                    paramMap.put("workCategoryCode", employerPosition.getWorkCategoryCode());
                    paramMap.put("ignoreZeroAmt", "true");
                    BigDecimal invoiceAmount = orderFacade.sumWaitInvoiceAmount(paramMap);

                    if (invoiceAmount == null || invoiceAmount.compareTo(BigDecimal.ZERO) == 0) {
                        log.info("{}-{}-{}-{}:待开票金额为0，终止开票", employerNo, mainstayNo, employerPosition.getWorkCategoryName(), applyTradeCompleteDayBegin);
                        continue;
                    }

                    log.info("[{}-{}-{}:待开票金额-{}]", employerNo, mainstayNo, applyTradeCompleteDayBegin, invoiceAmount);

                    String trxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getPrefix(),
                            SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getKey(),
                            SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getWidth());


                    InvoiceRecord invoiceRecord = fillInvoiceRecord(
                            reqVo,
                            merchantInvoiceInfo,
                            merchantExpressInfo,
                            employerPosition,
                            invoiceAmount,
                            amountType,
                            applyTradeCompleteDayBegin,
                            DateUtil.formatDate(DateUtil.getDayEnd(DateUtil.parse(reqVo.getTradeCompleteDayEnd()))),
                            mainstayMch.getMchName(),
                            trxNo
                    );

                    invoiceFacade.applyInvoice(invoiceRecord, null);
                    trxNos.add(trxNo);
                }
            }
        } catch (BizException e) {
            return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
        } catch (Exception e) {
            return ResponseDto.unknown();
        }
        if (!trxNos.isEmpty()) {
            InvoiceRecordResVo resVo = invoiceRecordBiz.listRecordApi(MapUtil.of("trxNos", trxNos));
            return ResponseDto.success(resVo, null);
        }else {
            return ResponseDto.fail(ApiExceptions.API_INVOICE_APPLYRESULT_EMPTY.getApiErrorCode(), ApiExceptions.API_INVOICE_APPLYRESULT_EMPTY.getErrMsg());
        }


    }

    @RequestMapping("waitIssueAmount")
    public ResponseDto<List<WaitIssueAmountQuery>> waitIssueAmount(@RequestBody RequestDto<ApplyInvoiceReqVo> paramVo) {
        ApplyInvoiceReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        String mainstayNo = reqVo.getMainstayNo();
        String productNo = reqVo.getProductNo();
        if (StringUtil.isEmpty(productNo)) {
            productNo = ProductNoEnum.ZXH.getValue();
        }

        if (StringUtils.isBlank(mainstayNo)) {
            return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), "代征主体编号不能为空");
        }
        if (StringUtils.isBlank(reqVo.getTradeCompleteDayEnd())) {
            return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), "截止日期不能为空");
        }

        if (!DateUtil.parse(reqVo.getTradeCompleteDayEnd()).before(DateUtil.getDayStart(new Date()))) {
            return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), "开票终止日期错误，终止日期须为当日以前，不含当日");
        }

        final Vendor vendor = vendorFacade.getVendorByNo(mainstayNo);
        if (vendor == null) {
            return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), "你的商户未与该代征主体建立代征关系");
        }
        if (!StringUtils.equals(vendor.getProductNo(), ProductNoEnum.ZXH.getValue())) {
            return ResponseDto.fail(ApiExceptions.API_PARAM_FAIL.getApiErrorCode(), "该产品暂不支持接口申请开票");
        }
        Integer amountType = InvoiceAmountTypeEnum.ORDER_AMOUNT.getValue();

        List<MerchantEmployerPosition> employerPositions = merchantEmployerPositionFacade.listByMchNo(employerNo);

        try {
            List<WaitIssueAmountQuery> returnList = new ArrayList<>();
            if (employerPositions != null) {
                log.info(JSONUtil.toJsonPrettyStr(employerPositions));

                for (MerchantEmployerPosition employerPosition : employerPositions) {

                    WaitIssueAmountQuery waitIssueAmountQuery = new WaitIssueAmountQuery();
                    waitIssueAmountQuery.setWorkCategoryCode(employerPosition.getWorkCategoryCode());
                    waitIssueAmountQuery.setWorkCategoryName(employerPosition.getWorkCategoryName());

                    String applyTradeCompleteDayBegin = invoiceFacade.getApplyTradeCompleteDayBegin(employerNo, mainstayNo,
                            null, null, InvoiceSourceEnum.ON_LINE.getCode(), employerPosition.getWorkCategoryCode(),
                            productNo, amountType);

                    if (DateUtil.parse(applyTradeCompleteDayBegin).after(DateUtil.parse(reqVo.getTradeCompleteDayEnd()))) {
                        log.info("[{}-{}-{}] 开票截止日期小于开票开始日期，终止该岗位类目开票", employerNo, mainstayNo, employerPosition.getWorkCategoryCode());
                        waitIssueAmountQuery.setAmount(BigDecimal.ZERO.toPlainString());
                        returnList.add(waitIssueAmountQuery);
                        continue;
                    }

                    Map<String, Object> paramMap = new HashMap<>();

                    paramMap.put("employerNo", employerNo);
                    paramMap.put("mainstayNo", mainstayNo);
                    paramMap.put("productNo", ProductNoEnum.ZXH.getValue());
                    paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
                    paramMap.put("completeBeginDate", DateUtil.parseTime(applyTradeCompleteDayBegin + " 00:00:00"));
                    paramMap.put("completeEndDate", DateUtil.parseTime(reqVo.getTradeCompleteDayEnd() + " 23:59:59"));
                    paramMap.put("workCategoryCode", employerPosition.getWorkCategoryCode());
                    paramMap.put("ignoreZeroAmt", "true");

                    log.info("查询可开票金额参数:{}",JSONUtil.toJsonPrettyStr(paramMap));
                    BigDecimal total = orderFacade.sumWaitInvoiceAmount(paramMap);
                    if (total == null) {
                        total = BigDecimal.ZERO;
                    }
                    waitIssueAmountQuery.setAmount(total.toPlainString());

                    returnList.add(waitIssueAmountQuery);
                }
            }
            return ResponseDto.success(returnList, "");
        }catch (BizException e) {
            return ResponseDto.fail(e.getApiErrorCode(), e.getErrMsg());
        } catch (Exception e) {
            return ResponseDto.unknown();
        }
    }

    private InvoiceRecord fillInvoiceRecord(ApplyInvoiceReqVo applyInvoiceReqVo,
                                            MerchantInvoiceInfo merchantInvoiceInfo,
                                            MerchantExpressInfo merchantExpressInfo,
                                            MerchantEmployerPosition employerPosition,
                                            BigDecimal amount,
                                            Integer amountType,
                                            String tradeCompleteDayBegin,
                                            String tradeCompleteDayEnd,
                                            String mainstayName,
                                            String trxNo) {

        InvoiceRecord record = new InvoiceRecord();
        record.setUpdateTime(new Date());
        record.setProductNo(ProductNoEnum.ZXH.getValue());
        record.setProductName(ProductNoEnum.ZXH.getDesc());
        record.setEmployerMchNo(merchantInvoiceInfo.getMchNo());
        record.setEmployerMchName(merchantInvoiceInfo.getMchName());
        record.setMainstayMchNo(applyInvoiceReqVo.getMainstayNo());
        record.setMainstayMchName(mainstayName);
        record.setTrxNo(trxNo);
        record.setInvoiceType(applyInvoiceReqVo.getInvoiceType());
        record.setApplyType(1);
        record.setAmountType(amountType);
        record.setInvoiceAmount(amount);
        record.setTradeCompleteDayBegin(tradeCompleteDayBegin);
        record.setTradeCompleteDayEnd(tradeCompleteDayEnd);
        record.setInvoiceStatus(InvoiceStatusEnum.WAIT_ISSUE.getValue());
        record.setAccountHandleStatus(AccountHandleStatusEnum.UN_HANDLE.getValue());
        record.setExpressConsignee(merchantExpressInfo.getConsignee());
        record.setExpressTelephone(merchantExpressInfo.getTelephone());
        record.setProvince(merchantExpressInfo.getProvince());
        record.setCity(merchantExpressInfo.getCity());
        record.setCounty(merchantExpressInfo.getCounty());
        record.setAddress(merchantExpressInfo.getAddress());
        record.setTaxPayerType(merchantInvoiceInfo.getTaxPayerType());
        record.setTaxNo(merchantInvoiceInfo.getTaxNo());
        record.setRegisterAddrInfo(merchantInvoiceInfo.getRegisterAddrInfo());
        record.setAccountNo(merchantInvoiceInfo.getAccountNo());
        record.setBankName(merchantInvoiceInfo.getBankName());
        record.setRemark(applyInvoiceReqVo.getRemark());
        record.setVersion(0);
        record.setCreateTime(new Date());
        record.setSource(InvoiceSourceEnum.ON_LINE.getCode());
        record.setInvoiceCategoryCode(employerPosition.getInvoiceCategoryList().get(0).getInvoiceCategoryCode());
        record.setInvoiceCategoryName(employerPosition.getInvoiceCategoryList().get(0).getInvoiceCategoryName());
        record.setWorkCategoryCode(employerPosition.getWorkCategoryCode());
        record.setWorkCategoryName(employerPosition.getWorkCategoryName());
        return record;
    }
}
