package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.SignTemplate;
import com.zhixianghui.service.trade.dao.SignTemplateDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 模板id表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-06-07
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SignTemplateBiz {

    private final SignTemplateDao signtemplateDao;

    public void add(List<SignTemplate> signTemplateList) {
        signtemplateDao.insert(signTemplateList);
    }

    public void add(SignTemplate signTemplate) {
        signtemplateDao.insert(signTemplate);
    }

    public SignTemplate get(Long id) {
        return signtemplateDao.getById(id);
    }

    public SignTemplate get(String id) {
        return signtemplateDao.getOne(new HashMap<String, Object>(){{
            put("templateId", id);
        }});
    }

    public void modify(SignTemplate signTemplate) {
        signtemplateDao.update(signTemplate);
    }

    public PageResult<List<SignTemplate>> list(Map<String, Object> paramMap, PageParam pageParam) {
        paramMap.put("deleteFlag", RemovedEnum.NORMAL.getValue());
        return signtemplateDao.listPage(paramMap, pageParam);
    }

    public void del(SignTemplate signTemplate) {
        signtemplateDao.updateIfNotNull(signTemplate);
    }

}