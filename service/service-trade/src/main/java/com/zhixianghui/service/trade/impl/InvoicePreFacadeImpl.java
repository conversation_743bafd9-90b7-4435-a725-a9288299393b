package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;
import com.zhixianghui.facade.trade.service.InvoicePreFacade;
import com.zhixianghui.service.trade.biz.InvoicePreRecordBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;

import java.util.List;
import java.util.Map;

/**
 * 预开发票记录接口实现类
 */
@Service
@RequiredArgsConstructor
public class InvoicePreFacadeImpl implements InvoicePreFacade {

    private final InvoicePreRecordBiz invoicePreRecordBiz;

    @Override
    public InvoicePreRecord getByEmployerAndMainstay(String employerMchNo, String mainstayMchNo) {
        return invoicePreRecordBiz.getByEmployerAndMainstay(employerMchNo, mainstayMchNo);
    }

    @Override
    public PageResult<List<InvoicePreRecord>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return invoicePreRecordBiz.listPage(paramMap, pageParam);
    }

    @Override
    public void createPreInvoice(InvoicePreRecord record) throws BizException {
        invoicePreRecordBiz.createPreInvoice(record);
    }

    @Override
    public void updatePreInvoice(InvoicePreRecord record) throws BizException {
        invoicePreRecordBiz.updatePreInvoice(record);
    }

    @Override
    public void deletePreInvoice(Long id) throws BizException {
        invoicePreRecordBiz.deletePreInvoice(id);
    }

    @Override
    public Map<String, Object> countInvoiceAmount(Map<String, Object> paramMap) {
        return invoicePreRecordBiz.countInvoiceAmount(paramMap);
    }

    @Override
    public InvoicePreRecord getByEmployerByMap(Map<String, Object> map) {
        return invoicePreRecordBiz.getByEmployerByMap(map);
    }

    @Override
    public boolean existRecord(Map<String, Object> paramMap) {
        return invoicePreRecordBiz.existRecord(paramMap);
    }
}