package com.zhixianghui.service.trade.pay.wx;

import cn.hutool.extra.spring.SpringUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.vo.WxPayQueryVo;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.WxMerchantBalanceBiz;
import com.zhixianghui.service.trade.pay.wx.biz.WxOrderUpdateBiz;


/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月13日 18:15:00
 */
public abstract class IWxPay {

    protected final WxMerchantBalanceBiz wxMerchantBalanceBiz = SpringUtil.getBean(WxMerchantBalanceBiz.class);

    protected final OrderItemBiz orderItemBiz = SpringUtil.getBean(OrderItemBiz.class);

    protected final WxOrderUpdateBiz wxOrderUpdateBiz = SpringUtil.getBean(WxOrderUpdateBiz.class);

    protected ChangesFunds changesFunds;

    protected WxPayParam wxPayParam;

    public IWxPay(WxPayParam wxPayParam) {
        this.wxPayParam = wxPayParam;
    }

    public abstract void handle();


    public abstract PayRespVo getResVo(boolean isUpdateFound);

}