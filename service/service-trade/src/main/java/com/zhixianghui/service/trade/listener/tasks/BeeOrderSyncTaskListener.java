package com.zhixianghui.service.trade.listener.tasks;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.service.yishui.YishuiFacade;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.dto.TimeRangeDto;
import com.zhixianghui.facade.trade.enums.FeeSourceEnum;
import com.zhixianghui.service.trade.biz.FeeOrderBatchBiz;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_BEE_ORDER_SYNC, consumeThreadMax = 1, consumerGroup = "BeeOrderSyncTaskComsumer")
public class BeeOrderSyncTaskListener extends TaskRocketMQListener<JSONObject> {

    @Autowired
    private OrderItemBiz orderItemBiz;
    @Reference(timeout = 120000)
    private YishuiFacade yishuiFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Override
    public void runTask(JSONObject jsonParam) {
        TimeRangeDto timeRange = this.getTimeRange(jsonParam);
        String mainstayNos = dataDictionaryFacade.getSystemConfig("yishui");
        if (StrUtil.isBlank(mainstayNos)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("系统参数未配置易税供应商编号");
        }

        String[] mainstayList = mainstayNos.split(",");
        for (String mainstayNo : mainstayList) {
            log.info("易税[{}]订单开始同步",mainstayNo);
            Map<String, Object> param = new HashMap<>();
            param.put("startTime", timeRange.getStartTime());
            param.put("endTime", timeRange.getEndTime());
            param.put("mainstayNo", mainstayNo);

            List<Map<String, String>> list = orderItemBiz.listGrantOrderBatches(param);
            if (list ==null || list.size() == 0) {
                log.info("未查询到该时间段易税[{}]订单",mainstayNo);
                return;
            }
            for (Map<String, String> map : list) {
                if (map.get("platBatchNo") != null) {
                    log.info("易税[{}]订单批次号-开始同步：{}",mainstayNo,map.get("platBatchNo"));
                    yishuiFacade.orderBatchSubmit(map.get("platBatchNo"));
                    log.info("易税[{}]订单批次号-结束同步：{}",mainstayNo,map.get("platBatchNo"));
                }
            }
            log.info("易税[{}]订单完成同步",mainstayNo);
        }

    }
}
