package com.zhixianghui.service.trade.helper;

import com.zhixianghui.api.base.enums.VersionEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.exception.QpsExceptions;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.banklink.service.auth.PayAuthFacade;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.service.MerchantCacheFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.AuthRecord;
import com.zhixianghui.service.trade.biz.AuthRecordBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import com.zhixianghui.facade.employee.entity.Job;

import java.util.*;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.AUTH_AGREEMENT_SEQ;
import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.AUTH_RECORD_SEQ;

/**
 * <AUTHOR>
 * @description 校验类
 * @date 2020-12-17 11:56
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ZxhLimitBiz {

    private final CacheBiz cacheBiz;
    private final AuthRecordBiz authRecordBiz;
    private final RedisLock redisLock;
    private final RedisClient redisClient;

    @Reference
    private RiskControlFacade riskControlFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private MerchantCacheFacade merchantCacheFacade;
    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;
    @Reference(timeout = 12000)
    private PayAuthFacade payAuthFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private JobFacade jobFacade;

    public MerchantEmployerPosition validateCKHEmployerPosition(String employerNo, Job job) {
        MerchantEmployerPosition merchantEmployerPosition = merchantEmployerPositionFacade.getByMchNoAndWorkCategoryCode(employerNo,job.getWorkCategoryCode());
        if (merchantEmployerPosition == null){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("此任务岗位信息已失效");
        }

        return merchantEmployerPosition;
    }

    public Job getJobByJobNo(String employerNo, String jobNo) {
        Job job = jobFacade.getJobByJobId(jobNo);
        if (job == null || !job.getEmployerNo().equals(employerNo)){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("job_no 任务编号不存在");
        }
        return job;
    }

    public void validateProductOpen(String mchNo,String productNo) {
        if(!cacheBiz.isOpenProduct(mchNo,productNo)){
            throw ApiExceptions.API_TRADE_PRODUCT_NOT_OPENED_ERR;
        }
    }

    public void validateProductAndQuoteOpen(String mchNo,String mainstayNo, String productNo) {
        if (!ProductNoEnum.ZXH.getValue().equals(productNo) && !ProductNoEnum.CEP.getValue().equals(productNo)) {
            throw ApiExceptions.API_TRADE_PRODUCT_NOT_OPENED_ERR.newWithErrMsg("未支持的产品类型，请核对参数");
        }
        if(!cacheBiz.isOpenProduct(mchNo,productNo)){
            throw ApiExceptions.API_TRADE_PRODUCT_NOT_OPENED_ERR;
        }

        if (!cacheBiz.isExistQuote(mchNo,mainstayNo,productNo)){
            throw ApiExceptions.API_TRADE_PRODUCT_NOT_OPENED_ERR.newWithErrMsg("未设置产品计费，请联系产品经理");
        }
    }

    public void validateMchInfo(String logFlag, String employerNo, String mainstayNo) {
        log.info("校验商户状态信息[{}]",logFlag);
        //商户状态
        Merchant merchant = merchantCacheFacade.getByMchNo(employerNo);
        if (merchant == null){
            throw ApiExceptions.API_TRADE_MERCHANT_NOT_EXIST;
        }
        if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw ApiExceptions.API_TRADE_MERCHANT_STATUS_FAIL;
        }
        //代征主体
        Merchant mainstay = merchantCacheFacade.getByMchNo(mainstayNo);
        if (mainstay == null){
            throw ApiExceptions.API_TRADE_MAINSTAY_NOT_EXIST;
        }
        if (!mainstay.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw ApiExceptions.API_TRADE_MAINSTAY_STATUS_FAIL;
        }

        //检查代征关系
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo, mainstayNo);
        if (employerMainstayRelation == null || employerMainstayRelation.getStatus() == OpenOffEnum.OFF.getValue()) {
            throw ApiExceptions.API_TRADE_EMPLOYER_MAINSTAY_STATE_ERROR;
        }
    }

    public EmployerAccountInfo validateAccountInfo(String logFlag, String employerNo, String mainstayNo, Integer channelType) {
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo,mainstayNo,channelType);
        if (employerAccountInfo == null) {
            log.info("[接口发放:{}] ==>没有用工企业帐号对应通道配置 通常为未建立代征关系 " +
                    "EmployerNo:[{}] MainstayNo:[{}] ChannelType:[{}]",logFlag,employerNo,mainstayNo,channelType);
            throw ApiExceptions.API_TRADE_CHANNEL_ERROR;
        }
        if (employerAccountInfo.getStatus().equals(OpenOffEnum.OFF.getValue())) {
            log.info("[接口发放:{}] ==> 用工企业帐号对应通道配置为关闭,EmployerNo:[{}] MainstayNo:[{}] ChannelType:[{}]",logFlag,employerNo,mainstayNo,channelType);
            throw ApiExceptions.API_TRADE_CHANNEL_ERROR;
        }
        if (StringUtils.isBlank(employerAccountInfo.getParentMerchantNo()) || StringUtils.isBlank(employerAccountInfo.getSubMerchantNo())) {
            log.info("[接口发放:{}] ==> 用工企业帐号对应通道配置的父子商编号为空,EmployerNo:[{}] MainstayNo:[{}] ChannelType:[{}]",logFlag,employerNo,mainstayNo,channelType);
            throw ApiExceptions.API_TRADE_CHANNEL_ERROR;

        }
        if (StringUtils.isBlank(employerAccountInfo.getPayChannelNo())) {
            log.info("[接口发放:{}] ==> 用工企业帐号对应通道配置的通道编号为空 ,EmployerNo:[{}] MainstayNo:[{}] ChannelType:[{}]",logFlag,employerNo,mainstayNo,channelType);
            throw ApiExceptions.API_TRADE_CHANNEL_ERROR;
        }
        return employerAccountInfo;
    }

    public String decryptNotNull(String fieldValue, String fieldName, String aesKey){
        if(StringUtils.isEmpty(fieldValue)){
            return null;
        }

        String value;
        try{
            value = AESUtil.decryptECB(fieldValue, aesKey);
        } catch (Exception e){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg(fieldName + " 解密失败");
        }
        if(StringUtils.isEmpty(value)){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg(fieldName + " 不能为空");
        }
        return value;
    }

    public String decrypt(String fieldValue, String fieldName, String aesKey){
        if(StringUtils.isEmpty(fieldValue)){
            return null;
        }

        try{
            return AESUtil.decryptECB(fieldValue, aesKey);
        } catch (Exception e){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg(fieldName + " 解密失败");
        }
    }

    public MerchantEmployerPosition validateEmployerPosition(String employerNo,String mainstayNo,String productNo) {
        //自由职业者服务编号 默认取第一个报价单里的第一个
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",employerNo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        paramMap.put("productNo", productNo);
        List<MerchantEmployerPosition> positionList = merchantEmployerPositionFacade.listByMchNoWithQuoteWithoutGroup(paramMap);
        if(CollectionUtils.isEmpty(positionList)){
            throw ApiExceptions.API_TRADE_POSITION_NOT_EXIST;
        }
        return positionList.get(0);
    }

    public void validVersion(String method, String version) {
        if (!VersionEnum.validMethodVersion(method, version)) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("version 版本号有误");
        }
    }

    public static void main(String[] args) {
        System.out.println(MD5Util.getMixMd5Str("******************"));
    }

    public void signAuth(String name, String idCardNo, String phoneNo, String logFlag) {
        //获取锁
        log.info("[预签约环节: {}]==>获取预签约鉴权锁", logFlag);
        String lockKey = String.join(":",TradeConstant.AUTH_LOCK_KEY , logFlag);
        String clientId = redisLock.tryLockLong(lockKey,0,1);
        if(clientId == null){
            log.info("[预签约环节: {}]==>获取预签约锁失败，直接丢弃", logFlag);
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }

        try{
            String key = MD5Util.getMixMd5Str(name + idCardNo + phoneNo);
            //先判断本地的缓存是否存在(失败或者成功都有)
            log.info("[预签约环节: {}]==>获取预签约鉴权缓存内容", logFlag);
            String resKey = String.join(":",TradeConstant.AUTH_RESULT_KEY,key);
            String authRes = redisClient.hget(resKey,"result");
            if("true".equals(authRes)){
                log.info("[签约鉴权==>{}] 本地缓存结果->鉴权成功 ", logFlag);
                return;
            }else if("false".equals(authRes)){
                log.info("[签约鉴权==>{}] 本地缓存结果->鉴权失败 ", logFlag);
                String desc = redisClient.hget(resKey,"desc");
                throw ApiExceptions.API_SIGN_AUTH_FAIL.newWithErrMsg(desc);
            }

            //先判断本地的鉴权表是否有记录
            List<AuthRecord> authRecords;
            if (StringUtils.isBlank(phoneNo)) {
                authRecords = authRecordBiz.listByElementActiveRecordWithType(name,idCardNo,null,null,AuthTypeEnum.IDCARD_NAME);
            }else {
                authRecords = authRecordBiz.listByElementActiveRecordWithType(name,idCardNo,null,phoneNo,AuthTypeEnum.IDCARD_NAME_PHONE);
            }

            if(!ObjectUtils.isEmpty(authRecords)){
                log.info("[签约鉴权==>{}] 本地数据库鉴权成功 ", logFlag);
                redisClient.hset(resKey ,"result","true");
                redisClient.hset(resKey ,"desc","成功");
                return ;
            }

            String authNo = sequenceFacade.nextRedisId(AUTH_RECORD_SEQ.getPrefix(),
                    AUTH_RECORD_SEQ.getKey(), AUTH_RECORD_SEQ.getWidth());
            String serialNo = sequenceFacade.nextRedisId(AUTH_AGREEMENT_SEQ.getPrefix(),
                    AUTH_AGREEMENT_SEQ.getKey(),AUTH_AGREEMENT_SEQ.getWidth());
            log.info("[签约鉴权==>{}] 开始鉴权",logFlag);
            AuthReqVo authReqVo = new AuthReqVo();
            authReqVo.setBankOrderNo(authNo);
            authReqVo.setName(name);
            authReqVo.setIdCardNo(idCardNo);
            authReqVo.setAuthType(AuthTypeEnum.IDCARD_NAME.getValue());
            authReqVo.setSerialNo(serialNo);
//            调用汇聚鉴权不鉴权手机号
//            if (StringUtils.isNotBlank(phoneNo)) {
//                authReqVo.setAuthType(AuthTypeEnum.IDCARD_NAME_PHONE.getValue());
//                authReqVo.setPhoneNo(phoneNo);
//            }
            AuthRespVo authRespVo = null;
            try {
                authRespVo = payAuthFacade.auth(authReqVo);
                if(!Objects.equals(BankAuthStatusEnum.SUCCESS.getValue(),authRespVo.getAuthStatus())){
                    log.error("[签约鉴权==>{}] 鉴权失败 入缓存，防止外部服务一直调用鉴权接口",logFlag);
                    redisClient.hset(resKey,"result","false");
                    redisClient.hset(resKey,"desc",authRespVo.getBizDesc());
                    redisClient.expire(resKey, 300);
                    throw ApiExceptions.API_SIGN_AUTH_FAIL.newWithErrMsg(authRespVo.getBizDesc());
                }else{
                    // 异步保存，失败也不影响业务
                    AuthRecord record =  new AuthRecord();
                    record.setAuthNo(authNo);
                    record.setCreateTime(new Date());
                    record.setReceiveNameMd5(MD5Util.getMixMd5Str(name));
                    record.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(idCardNo));
                    record.setAuthType(AuthTypeEnum.IDCARD_NAME.getValue());
                    if (StringUtils.isNotBlank(phoneNo)) {
                        record.setReceivePhoneNoMd5(MD5Util.getMixMd5Str(phoneNo));
                        record.setAuthType(AuthTypeEnum.IDCARD_NAME_PHONE.getValue());
                    }
                    record.setProtocolNo(serialNo);
                    record.setProtocolVersion(authRespVo.getProtocolVersion());
                    record.setAuthChannel(authRespVo.getChannelNo());
                    authRecordBiz.insertAsync(record);
                    redisClient.hset(resKey,"result","true");
                    redisClient.hset(resKey,"desc","成功");
                    log.info("[签约鉴权==>{}] 鉴权成功 入库,入缓存",logFlag);
                }
            }catch (Exception e) {
                if (e instanceof BizException) {
                    if (((BizException) e).getApiErrorCode() == QpsExceptions.QPS_NEED_BLOCK_EX.getApiErrorCode()) {
                        ApiExceptions.API_SIGN_AUTH_FAIL.newWithErrMsg(authRespVo.getBizDesc());
                    }
                }
                log.error("[{}]鉴权失败:",logFlag,e);
                log.error("[签约鉴权==>{}] 鉴权失败 入缓存，防止外部服务一直调用鉴权接口",logFlag);
                redisClient.hset(resKey,"result","false");
                redisClient.hset(resKey,"desc",authRespVo.getBizDesc());
                redisClient.expire(resKey, 300);
                throw ApiExceptions.API_SIGN_AUTH_FAIL.newWithErrMsg(authRespVo.getBizDesc());
            }

        }finally {
            redisLock.unlockLong(clientId);
        }
    }

    public void validateRiskControlRule(String employerNo, String mainstayNo) {
        if (!riskControlFacade.grantValidateExistRule(employerNo,mainstayNo)){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("找不到对应的规则，请联系平台管理员");
        }
    }

}
