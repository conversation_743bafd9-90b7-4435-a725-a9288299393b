package com.zhixianghui.service.trade.vo.ckh.req;

import com.zhixianghui.common.util.validator.EnumValue;
import com.zhixianghui.facade.employee.enums.EduBackgroundEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName CreateJobReqVo
 * @Description TODO
 * @Date 2022/10/25 9:32
 */
@Data
public class CreateJobReqVo implements Serializable {

    /**
     * 接单模式
     * {@link com.zhixianghui.facade.employee.enums.AcceptModeEnum}
     */
    @NotNull(message = "accept_mode 接单模式不能为空")
    @EnumValue(intValues = {100,200}, message = "accept_mode 取值范围有误")
    private Integer acceptMode;

    /**
     * 任务可见性
     * {@link com.zhixianghui.facade.employee.enums.JobVisibilityEnum}
     */
    @NotNull(message = "scope 任务可见性不能为空")
    @EnumValue(intValues = {100,200},message = "scope 取值范围有误")
    private Integer scope;

    @NotBlank(message = "job_name 任务名称不能为空")
    @Length(max = 50, message = "任务名称不能超过50个字符")
    private String jobName;

    @NotBlank(message = "position_no 岗位类目编号不能为空")
    private String positionNo;

    @NotBlank(message = "province 省级名称不能为空")
    @Length(max = 20,message = "province 省级名称不能超过20个字符")
    private String province;

    @NotBlank(message = "city 市级名称不能为空")
    @Length(max = 20, message = "city 市级名称不能超过20个字符")
    private String city;

    @NotBlank(message = "area 区级名称不能为空")
    @Length(max = 20,message = "area 区级名称不能超过20个字符")
    private String area;

    @NotBlank(message = "address 详细地址不能为空")
    @Length(max = 100,message = "address 详细地址不能超过100个字符")
    private String address;

    @NotNull(message = "worker_num 招收人数不能为空")
    private Integer workerNum;

    /**
     * 性别
     * {@link com.zhixianghui.facade.employee.enums.WorkerGenderEnum}
     */
    @NotNull(message = "sex 性别不能为空")
    @EnumValue(intValues = {0,1,2},message = "sex 取值范围有误")
    private Integer sex;

    /**
     * 学历背景
     * {@link com.zhixianghui.facade.employee.enums.EduBackgroundEnum}
     */
    @EnumValue(intValues = {-1,100,200,300,400,500,600,700},message = "edu_background 学历取值范围有误")
    private Integer eduBackground = EduBackgroundEnum.NO_LIMIT.getCode();

    @NotBlank(message = "description 任务描述不能为空")
    private String description;

    private String standard;

    private List<String> tag;

    @Length(max = 100,message = "skill 专业技能不能超过100个字符")
    private String skill;

    @Size(min = 10,max = 10,message = "job_begin_date 任务开始时间必须为yyyy-MM-dd格式")
    private String jobBeginDate;

    @Size(min = 10,max = 10,message = "job_end_date 任务结束时间必须为yyyy-MM-dd格式")
    private String jobEndDate;

    @Size(min = 8,max = 8,message = "work_begin_time 工作时段必须为HH:mm:ss")
    private String workBeginTime;

    @Size(min = 8,max = 8,message = "work_end_time 工作时段必须为HH:mm:ss")
    private String workEndTime;

    @Min(value = 0,message = "minAge 取值范围有误")
    @Max(value = 100,message = "minAge 取值范围有误")
    private Integer minAge;

    @Min(value = 0,message = "maxAge 取值范围有误")
    @Max(value = 100,message = "maxAge 取值范围有误")
    private Integer maxAge;

    /**
     * 回调参数
     */
    private String callbackUrl;
}
