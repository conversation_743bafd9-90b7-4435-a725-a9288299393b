package com.zhixianghui.service.trade.pay.jxh.handle;

import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.banklink.vo.jxh.JxhParam;
import com.zhixianghui.facade.banklink.vo.jxh.ResStatus;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.service.trade.biz.JXHLocalPayBiz;
import com.zhixianghui.service.trade.pay.jxh.JXHHandler;
import com.zhixianghui.service.trade.pay.jxh.annotations.JXHService;

import javax.annotation.Resource;

@JXHService(type = ResStatus.NOT_FOND)
public class JXHNotFondHandler extends JXHHandler {

    @Resource
    private JXHLocalPayBiz jxhLocalPayBiz;

    @Override
    public void handle(JxhParam jxhParam) {
        PayReqVo reqVo = new PayReqVo();
        BeanUtil.copyProperties(jxhParam, reqVo);
        reqVo.setBankOrderNo(jxhParam.getMerchantOrderNo());
        jxhLocalPayBiz.retryNotFond(reqVo);
    }


    public PayRespVo getResVo(JxhParam jxhParam) {
        PayRespVo respVo = new PayRespVo();
        respVo.setBankTrxNo(jxhParam.getPlatformSerialNo());
        respVo.setBankOrderNo(jxhParam.getMerchantOrderNo());
        respVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
        respVo.setBizCode("");
        respVo.setBizMsg("");
        return respVo;
    }
}
