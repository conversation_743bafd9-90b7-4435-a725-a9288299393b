package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class InvoiceRecordInfoApiResVo extends ApiBizBaseDto implements Serializable {

    private static final long serialVersionUID = 5552111294704943400L;
    /**
     * 发票流水号
     */
    private String trxNo;

    /**
     * 发票开具状态
     * @see com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum
     */
    private Integer invoiceStatus;

    private String jobId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    private Integer invoiceType;

    /**
     * 发票类目名称
     */
    private String invoiceCategoryName;

    /**
     * 开票方
     */
    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 开票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开票交易时间段起始时间
     */
    private String tradeCompleteDayBegin;

    /**
     * 开票交易时间段终止时间
     */
    private String tradeCompleteDayEnd;

    private String serviceFeeBillPath;

    private List<String> invoiceFileUrlList;

    private List<InvoiceItem> invoiceItems;

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public class InvoiceItem{
        private String receiveName;
        private String receiveIdCardNo;
        private String receivePhoneNo;
        private String receiveAccountNo;
        private BigDecimal invoiceAmount;
        private Integer invoiceStatus;
        private List<String> invoiceFileUrl;
        private String workerBillFilePath;
    }
}
