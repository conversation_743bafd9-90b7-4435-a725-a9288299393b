package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.dto.ChangesFoundsDTO;
import com.zhixianghui.facade.trade.entity.ChangesFunds;
import com.zhixianghui.facade.trade.service.AcChangeFundsFacade;
import com.zhixianghui.facade.trade.service.ChangesFundsFacade;
import com.zhixianghui.facade.trade.vo.ChangesFundsVo;
import com.zhixianghui.service.trade.biz.AcChangeFundsBiz;
import com.zhixianghui.service.trade.biz.ChangesFundsBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 *  Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2021-12-09
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AcChangesFundsImpl implements AcChangeFundsFacade {

    private final AcChangeFundsBiz biz;

    @Override
    public PageResult<List<ChangesFundsVo>> listPage(ChangesFoundsDTO changesFoundsDTO) {
        return biz.listPage(changesFoundsDTO);
    }
}
