package com.zhixianghui.service.trade.process;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.pay.MiniPayFacade;
import com.zhixianghui.facade.banklink.vo.pay.MiniAppPayVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.AccountInfoCacheFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.trade.entity.IndividualProxyOrder;
import com.zhixianghui.facade.trade.entity.WechatInfo;
import com.zhixianghui.facade.trade.enums.IndividualProxyOrderPayStatus;
import com.zhixianghui.facade.trade.enums.IndividualProxyOrderStatus;
import com.zhixianghui.facade.trade.enums.WxJsApiPayStatusEnum;
import com.zhixianghui.facade.trade.enums.WxJsApiRefundStatusEnum;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.trade.biz.IndividualProxyOrderService;
import com.zhixianghui.service.trade.biz.WeChatUserBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName WxJsapiPayBiz
 * @Description TODO
 * @Date 2022/12/28 15:02
 */
@Slf4j
@Service
public class WxJsapiPayBiz {

    private static final String ERROR_ORDER_PRE = "ERROR:ORDER:";

    private static final String ORDER_PREPAY_PRE  = "PREPAY:ID:";

    @Autowired
    private WeChatUserBiz weChatUserBiz;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private IndividualProxyOrderService proxyOrderService;

    @Reference
    private AccountInfoCacheFacade accountInfoCacheFacde;
    @Reference
    private MiniPayFacade miniPayFacade;
    @Reference
    private SequenceFacade sequenceFacade;


    /**
     * 小程序支付
     * @param orderNo
     * @param weChatLoginVo
     * @return
     */
    public String prePay(String orderNo, WeChatLoginVo weChatLoginVo) {
        IndividualProxyOrder order = validOrder(orderNo);

        String cachePrePay = redisClient.get(ORDER_PREPAY_PRE + orderNo);
        if (StringUtils.isNotBlank(cachePrePay)){
            return cachePrePay;
        }

        WechatInfo wechatInfo = weChatUserBiz.selectWeChatInfo(weChatLoginVo.getWxUserNo());
        if (wechatInfo == null){
            log.error("jsapi下单失败，微信用户不存在，C端个人代开订单号：[{}]",orderNo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("微信用户不存在");
        }

        //获取供应商支付信息
        MainstayChannelRelation mainstayChannelRelation = accountInfoCacheFacde.getMainstayAccountInfo(
                order.getMainstayNo(), ChannelNoEnum.WXGATHERING.name());
        if (mainstayChannelRelation == null || mainstayChannelRelation.getStatus().intValue() == OpenOffEnum.OFF.getValue()){
            log.error("jsapi下单失败，供应商未启用微信收款通道，C端个人代开订单号：[{}]",orderNo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("服务商未启用产品，支付失败");
        }
        order.setPayBeginTime(LocalDateTime.now());
        proxyOrderService.updateById(order);
        MiniAppPayVo miniAppPayVo = buildPayVo(order,mainstayChannelRelation,wechatInfo);
        //请求微信jsapi下单
        WxResVo wxResVo = miniPayFacade.jsapiPay(miniAppPayVo);
        Map<String,Object> resp = JsonUtil.toBean(wxResVo.getResponBody(),Map.class);
        if (wxResVo.isSuccess()){
            //放入缓存
            cachePrePay = (String) resp.get("prepay_id");
            redisClient.set(ORDER_PREPAY_PRE + orderNo, cachePrePay ,15 * 60);
            return cachePrePay;
        }else{
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg((String) resp.get("message"));
        }
    }

    /**
     * @param order
     * @param mainstayChannelRelation
     * @param wechatInfo
     * @return
     */
    private MiniAppPayVo buildPayVo(IndividualProxyOrder order, MainstayChannelRelation mainstayChannelRelation, WechatInfo wechatInfo) {
        MiniAppPayVo miniAppPayVo = new MiniAppPayVo();
        miniAppPayVo.setOrderNo(order.getOrderNo());
        miniAppPayVo.setPayUserId(wechatInfo.getMiniOpenId());
        miniAppPayVo.setReceiveMchId(mainstayChannelRelation.getChannelMchNo());
        miniAppPayVo.setAmount(AmountUtil.changeToFen(order.getPayAmount()));
        miniAppPayVo.setExpireTime(order.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss+08:00")));
        return miniAppPayVo;
    }

    /**
     * 订单校验
     * @param orderNo
     */
    private IndividualProxyOrder validOrder(String orderNo) {
        String msg = redisClient.get(ERROR_ORDER_PRE + orderNo);
        if (StringUtils.isNotBlank(msg)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(msg);
        }

        IndividualProxyOrder individualProxyOrder = proxyOrderService.getOne(
                new QueryWrapper<IndividualProxyOrder>().lambda().eq(IndividualProxyOrder::getOrderNo,orderNo));
        try {
            if (individualProxyOrder == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单不存在");
            }

            if (individualProxyOrder.getOrderStatus().intValue() == IndividualProxyOrderStatus.WAITE_PAY.getValue()){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单不处于未支付状态，请勿重复支付");
            }
        }catch (BizException e){
            redisClient.set(ERROR_ORDER_PRE + orderNo,e.getErrMsg(),5 * 60);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(e.getErrMsg());
        }
        return individualProxyOrder;
    }

    /**
     * 微信jsapi支付回调处理
     * @param jsonParam
     */
    public void payNotify(String jsonParam) {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        String orderNo = jsonObject.getString("out_trade_no");
        String transactionId = jsonObject.getString("transaction_id");
        String state = jsonObject.getString("trade_state");
        String successTime = jsonObject.getString("success_time");
        //只回调支付成功的订单
        if (!WxJsApiPayStatusEnum.SUCCESS.getStatus().equals(state)){
            log.info("微信回调，jsapi支付不为成功，订单号：[{}]，回调参数：[{}]",orderNo,jsonParam);
            return;
        }
        IndividualProxyOrder individualProxyOrder = proxyOrderService.getOne(
                new QueryWrapper<IndividualProxyOrder>().lambda().eq(IndividualProxyOrder::getOrderNo,orderNo));
        if (individualProxyOrder == null){
            log.error("C端代开订单不存在，订单号：[{}]，回调参数：[{}]",orderNo,jsonParam);
            return;
        }

        paySuccess(individualProxyOrder,transactionId,successTime);
    }

    /**
     * 支付成功，更新订单状态
     * @param individualProxyOrder
     * @param transactionId
     * @param successTime
     */
    private void paySuccess(IndividualProxyOrder individualProxyOrder,String transactionId,String successTime) {
        //不管订单状态是否为未支付，都需要设置为已付款
        individualProxyOrder.setPayTrxNo(transactionId);
        individualProxyOrder.setPayStatus(IndividualProxyOrderPayStatus.PAYED.getValue());
        individualProxyOrder.setPayCompleteTime(LocalDateTime.parse(successTime));
        //只有未支付状态才修改订单状态
        if (individualProxyOrder.getOrderStatus().intValue() == IndividualProxyOrderStatus.WAITE_PAY.getValue()){
            individualProxyOrder.setOrderStatus(IndividualProxyOrderStatus.WAITE_INVOICE.getValue());
        }
        proxyOrderService.updateById(individualProxyOrder);
    }

    /**
     * 延时查询订单
     * @param orderNo
     */
    public void closeHandle(String orderNo) {
        IndividualProxyOrder individualProxyOrder = proxyOrderService.getOne(
                new QueryWrapper<IndividualProxyOrder>().lambda().eq(IndividualProxyOrder::getOrderNo,orderNo));
        if (individualProxyOrder == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单号不存在");
        }

        //已付款不能关闭订单
        if (individualProxyOrder.getPayStatus().intValue() == IndividualProxyOrderPayStatus.PAYED.getValue()){
            log.info("C端代开订单关闭失败，订单已付款，订单号：[{}]",orderNo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单已支付，无法关闭");
        }

        //关闭订单前查询微信进行确认
        try {
            WxResVo wxResVo = miniPayFacade.jsapiQuery(orderNo);
            if (wxResVo.isSuccess()){
                JSONObject jsonObject = JSONObject.parseObject(wxResVo.getResponBody());
                String status = jsonObject.getString("trade_state");
                if (status.equals(WxJsApiPayStatusEnum.NOTPAY.getStatus())){
                    //如果订单未支付，关闭订单
                    closeOrder(individualProxyOrder);
                }else if (status.equals(WxJsApiPayStatusEnum.SUCCESS.getStatus())){
                    //如果订单已支付，没有更新订单，可能回调出现问题
                    String transactionId = jsonObject.getString("transaction_id");
                    String successTime = jsonObject.getString("success_time");
                    paySuccess(individualProxyOrder,transactionId,successTime);
                }else{
                    log.error("微信支付订单状态异常，不进行关闭，订单号：[{}]，返回参数：[{}]",orderNo,wxResVo.getResponBody());
                    return;
                }
            }else{
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("关单前查询订单异常");
            }
        }catch (BizException e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(e.getErrMsg());
        }

    }

    /**
     * 关闭订单
     * @param individualProxyOrder
     */
    private void closeOrder(IndividualProxyOrder individualProxyOrder) {
        //订单状态为未支付，关闭订单
        WxResVo closeVo = miniPayFacade.closeOrder(individualProxyOrder.getOrderNo());
        if (closeVo.isSuccess()){
            individualProxyOrder.setOrderStatus(IndividualProxyOrderStatus.CANCELLED.getValue());
            individualProxyOrder.setPayCompleteTime(LocalDateTime.now());
            proxyOrderService.updateById(individualProxyOrder);
        }else {
            log.error("微信支付订单关闭失败，订单号：[{}]，返回参数：[{}]",individualProxyOrder.getOrderNo(),closeVo.getResponBody());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("关闭订单异常");
        }
    }


    /**
     * 退款回调处理
     * @param jsonParam
     */
    public void refundNotify(String jsonParam) {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        String orderNo = jsonObject.getString("out_trade_no");
        String refundStatus = jsonObject.getString("refund_status");
        String successTime = jsonObject.getString("success_time");
        IndividualProxyOrder individualProxyOrder = proxyOrderService.getOne(
                new QueryWrapper<IndividualProxyOrder>().lambda().eq(IndividualProxyOrder::getOrderNo,orderNo));
        if (individualProxyOrder == null){
            log.error("退款订单不存在，订单号：[{}]，退款信息：[{}]",orderNo,jsonParam);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单不存在");
        }

        if (refundStatus.equals(WxJsApiRefundStatusEnum.SUCCESS.getValue())){
            individualProxyOrder.setRefundCompleteTime(LocalDateTime.parse(successTime));
            individualProxyOrder.setIsRefund(true);
            proxyOrderService.updateById(individualProxyOrder);
        }else{
            log.error("退款失败，订单号：[{}]，退款信息：[{}]",orderNo,jsonParam);
        }
    }


    /**
     * 退款
     * @param order
     */
    public void refund(IndividualProxyOrder order){
        if (order.getIsRefund()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该订单已退款，请勿重复操作");
        }

        if (StringUtils.isBlank(order.getRefundNo())){
            order.setRefundNo(order.getOrderNo() + "R");
        }

        //先查询退款订单是否存在
        WxResVo wxResVo = miniPayFacade.jsapiRefundQuery(order.getRefundNo());
        JSONObject queryObj = JSON.parseObject(wxResVo.getResponBody());
        if (wxResVo.isNotFound()){
            //执行退款操作
            WxResVo refundResp = miniPayFacade.refund(order.getOrderNo(),order.getRefundNo(),AmountUtil.changeToFen(order.getPayAmount()),order.getErrorRemark());
            if (refundResp.isSuccess()){
                order.setRefundBeginTime(LocalDateTime.now());
                proxyOrderService.updateById(order);
            }else{
                JSONObject jsonObj = JSON.parseObject(refundResp.getResponBody());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(jsonObj.getString("message"));
            }
        }else if (wxResVo.isSuccess()){
            //退款订单存在，更新数据
            String refundStatus = queryObj.getString("refund_status");
            String successTime = queryObj.getString("success_time");
            if (refundStatus.equals(WxJsApiRefundStatusEnum.SUCCESS.getValue())) {
                order.setRefundCompleteTime(LocalDateTime.parse(successTime));
                order.setIsRefund(true);
                proxyOrderService.updateById(order);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("退款订单已存在，请勿重复退款");
            }else{
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(queryObj.getString("message"));
            }
        }else{
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(queryObj.getString("message"));
        }

    }
}
