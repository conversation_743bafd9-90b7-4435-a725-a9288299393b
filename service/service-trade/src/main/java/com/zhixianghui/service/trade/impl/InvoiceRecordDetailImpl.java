package com.zhixianghui.service.trade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.trade.dto.InvoiceRecordDetailDto;
import com.zhixianghui.facade.trade.dto.UpdateInvoiceDetailDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecordDetail;
import com.zhixianghui.facade.trade.service.InvoiceRecordDetailFacade;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.service.trade.biz.InvoiceRecordDetailBiz;
import com.zhixianghui.service.trade.process.InvoiceHandleBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
@Slf4j
public class InvoiceRecordDetailImpl implements InvoiceRecordDetailFacade {

    @Autowired
    private InvoiceRecordDetailBiz invoiceRecordDetailBiz;
    @Autowired
    private InvoiceHandleBiz invoiceHandleBiz;


    @Override
    public IPage<InvoiceRecordDetail> invoiceRecordDetailPage(IPage<InvoiceRecordDetail> page, InvoiceRecordDetailDto dto) {
        return invoiceRecordDetailBiz.invoiceRecordDetailPage(page, dto);
    }

    @Override
    public List<InvoiceRecordDetail> listInvoiceDetailIdCards(InvoiceRecordDetailDto dto) {
        return invoiceRecordDetailBiz.listInvoiceDetailIdCards(dto);
    }

    @Override
    public void updateInvoiceRecordDetail(UpdateInvoiceDetailDto dto) {
        invoiceHandleBiz.updateInvoiceRecordDetail(dto);
    }

    @Override
    public InvoiceRecordDetail getInvoiceRecordDetailByPlatTrxNo(String invoiceTrxNo,String platTrxNo) {
        return invoiceRecordDetailBiz.getInvoiceRecordDetailByPlatTrxNo(invoiceTrxNo,platTrxNo);
    }


    @Override
    public List<InvoiceRecordDetail> listInvoiceRecordDetailByInvoiceTrxNo(String invoiceTrxNo) {
        return invoiceRecordDetailBiz.listInvoiceRecordDetailByInvoiceTrxNo(invoiceTrxNo);
    }

    @Override
    public List<InvoiceRecordDetail> listInvoiceRecordDetailByInvoiceTrxNoAndIdCardNo(String invoiceTrxNo,String idCard) {
        return invoiceRecordDetailBiz.listInvoiceRecordDetailByInvoiceTrxNoAndIdCardNo(invoiceTrxNo, idCard);
    }

    @Override
    public IPage<InvoiceDetailGroupByIdCardVo> listInvoiceDetailGroupByIdCard(Page<InvoiceDetailGroupByIdCardVo> page, String trxNo) {
        return invoiceHandleBiz.listInvoiceDetailGroupByIdCard(page, trxNo);
    }
}
