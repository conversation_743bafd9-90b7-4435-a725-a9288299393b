package com.zhixianghui.service.trade.listener;

import cn.hutool.core.lang.Dict;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.PendingOrderOpeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AssertUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.service.pay.PayBankFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.sign.SignReceiveRespVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.vo.TraceVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.enums.RechargeStatusEnum;
import com.zhixianghui.facade.trade.enums.WithdrawStatusEnum;
import com.zhixianghui.service.trade.biz.*;
import com.zhixianghui.service.trade.factory.TradeFactory;
import com.zhixianghui.service.trade.helper.TradeNotifyBiz;
import com.zhixianghui.service.trade.process.AbstractGrantHandler;
import com.zhixianghui.service.trade.process.RiskControlHandleBiz;
import com.zhixianghui.service.trade.process.SignBiz;
import com.zhixianghui.service.trade.process.WithdrawAcctDetailBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;


/**
 * <AUTHOR>
 * @description
 * @date 2020-11-06 14:07
 **/
@Component
@Slf4j
public class MessageListener {

    @Autowired
    private SignBiz signBiz;
    @Autowired
    private RechargeRecordBiz rechargeRecordBiz;
    @Autowired
    private WithdrawRecordBiz withdrawRecordBiz;
    @Autowired
    private CommonBiz commonBiz;
    @Autowired
    private OrderItemBiz orderItemBiz;
    @Autowired
    private RiskControlHandleBiz riskControlHandleBiz;
    @Autowired
    private WithdrawAcctDetailBiz withdrawAcctDetailBiz;


    /**
     * 充值回调
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC, selectorExpression = MessageMsgDest.TAG_TRADE_RECHARGE, consumeThreadMax = 20, consumerGroup = "rechargeConsume")
    public class RechargeMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("充值回调参数为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            TraceVo<?> traceVo = JSONObject.parseObject(msg, TraceVo.class);
            rechargeRecordBiz.rechargeCallback(traceVo);
        }
    }

    /**
     * 获取凭证
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_CERTIFICATE, selectorExpression = MessageMsgDest.TAG_TRADE_CERTIFICATE, consumeThreadMax = 20, consumerGroup = "certificateConsume")
    public class TradeCertificateListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取充值证书参数为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            TraceVo<?> traceVo = JSONObject.parseObject(msg, TraceVo.class);
            try {
                rechargeRecordBiz.fetchCertificate(traceVo);
            } catch (Exception e){
                log.error("[{}] 生成充值凭证失败: {}", traceVo.getTraceId(), JSONObject.toJSONString(traceVo), e);
            }
        }
    }

    /**
     * 获取凭证
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_CERTIFICATE, selectorExpression = MessageMsgDest.TAG_DOWNLOAD_PAY_CERTIFICATE, consumeThreadMax = 3, consumerGroup = "downloadCertificateConsume")
    public class DownloadCertificateListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("下载证书参数为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            TraceVo<?> downloadTrace = JSONObject.parseObject(msg, TraceVo.class);
            rechargeRecordBiz.downloadCertificate(downloadTrace);
        }

    }

    /**
     * 风控监听订单操作
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_RISKCONTROL_NOTIFY_TRADE, selectorExpression = MessageMsgDest.TAG_RISKCONTROL_NOTIFY_TRADE,consumeThreadMax = 2, consumerGroup = "riskControlNotifyConsume")
    public class RiskControlNotifyMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("风控监听订单操作 msg不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            Map<String,Object> jsonParam = JsonUtil.toBean(msg,new TypeReference<HashMap<String,Object>>(){});
            Integer operation = Integer.valueOf(String.valueOf(jsonParam.get("operation")));
            String operatorName = (String) jsonParam.get("operatorName");
            String platTrxNos = (String) jsonParam.get("platTrxNos");
            String[] platTrxNoArr = platTrxNos.split(",");
            if(Objects.equals(PendingOrderOpeEnum.PASS.getValue(),operation)){
                //通过
                Arrays.stream(platTrxNoArr).forEach(
                        platTrxNo->{
                            try {
                                riskControlHandleBiz.passHangup(platTrxNo,operatorName);
                            }catch (Exception e){
                                log.error("手动通过风控失败, 流水号:[{}]",platTrxNo,e);
                            }
                        }
                );
            }else if(Objects.equals(PendingOrderOpeEnum.REJECT.getValue(),operation)){
                //拒绝
                Arrays.stream(platTrxNoArr).forEach(
                        platTrxNo->{
                            try {
                                riskControlHandleBiz.rejectHangup(platTrxNo,operatorName);
                            }catch (Exception e){
                                log.error("手动拒绝风控失败, 流水号:[{}]",platTrxNo,e);
                            }
                        }
                );
            }
        }
    }


    /**
     * 风控原因更新订单操作
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_RISKCONTROL_NOTIFY_TRADE, selectorExpression = MessageMsgDest.TAG_RISKCONTROL_NOTIFY_TRADE_AGAIN,consumeThreadMax = 1, consumerGroup = "riskControlReasonConsume")
    public class RiskControlReasonUpdateMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("风控原因更新订单操作 msg不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            Map<String,Object> jsonParam = JsonUtil.toBean(msg,new TypeReference<HashMap<String,Object>>(){});
            String reason = (String) jsonParam.get("reason");
            String platTrxNo = (String) jsonParam.get("platTrxNo");
            Integer code = (Integer) jsonParam.get("code");
            List<String> list = (List<String>) jsonParam.get("riskType");
            riskControlHandleBiz.riskControlReasonUpdate(platTrxNo,reason,code,list);
        }
    }


    /**
     * 预签约通知
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_SIGN_ASYNC, selectorExpression = MessageMsgDest.TAG_PRE_SIGN ,consumeThreadMax = 6, consumerGroup = "preSignConsume")
    public class PreSignMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("预签约通知 msg不能为null");
            }
        }

        @Override
        public void consumeMessage(String userId) {
            signBiz.preSign(userId);
        }
    }

    /**
     * 签约回调
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_SIGN_ASYNC, selectorExpression = MessageMsgDest.TAG_SIGN_RECEIVE ,consumeThreadMax = 6, consumerGroup = "signReceiveConsume")
    public class SignReceiveMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约回调 msg不能为null");
            }
        }

        @Override
        public void consumeMessage(String resp) {
            SignReceiveRespVo signReceiveRespVo = JsonUtil.toBean(resp,SignReceiveRespVo.class);
            signBiz.signReceive(signReceiveRespVo);
        }
    }

    /**
     * 预签约修改手机号通知
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_SIGN_ASYNC, selectorExpression = MessageMsgDest.TAG_SIGN_MODIFY_INFO ,consumeThreadMax = 6, consumerGroup = "signModifyConsume")
    public class SignModifyMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("预签约修改手机号通知 msg不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            Map<String,String> infoMap = JsonUtil.toBean(msg,new TypeReference<Map<String,String>>(){});
            String userId = infoMap.get("userId");
            String phoneNo = infoMap.get("phoneNo");
            signBiz.preSignModify(userId,phoneNo);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_CALLBACK, selectorExpression = MessageMsgDest.TAG_ALIPAY_WITHDRAW ,consumeThreadMax = 6, consumerGroup = "trans_withdraw")
    public class WithdrawMessageListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提现到支付宝的消息内容不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            JSONObject contentJson = JSON.parseObject(jsonParam);
            String status = contentJson.getString("status");
            String payFundOrderId = contentJson.getString("pay_fund_order_id");
            String orderId = contentJson.getString("order_id");
            String outBizNo = contentJson.getString("out_biz_no");
            String failReason = contentJson.getString("fail_reason");
            if (StringUtils.equals(status, "SUCCESS") && payFundOrderId != null) {
                withdrawRecordBiz.handleNotify(outBizNo,payFundOrderId,orderId, SuccessFailEnum.SUCCESS,null);
            } else if (StringUtils.equals(status, "CLOSED") || StringUtils.equals(status,"FAIL") || StringUtils.equals(status,"REFUND")) {
                withdrawRecordBiz.handleNotify(outBizNo,payFundOrderId,orderId,SuccessFailEnum.FAIL,failReason);
            }else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("");
            }
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_CALLBACK, selectorExpression = MessageMsgDest.TAG_ALIPAY_CALLBACK_TRANSPAY ,consumeThreadMax = 20, consumerGroup = "transCallbackConsumer")
    public class TransPayCallbackMessageListener extends BaseRocketMQListener<String> {

        @Reference
        private EmployerAccountInfoFacade employerAccountInfoFacade;
        @Autowired
        private AccountQueryBiz accountQueryBiz;
        @Autowired
        private OrderBiz orderBiz;

        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("充值回调消息体不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            JSONObject data = JSONObject.parseObject(jsonParam);
            String payDate = data.getString("pay_date");
            String payFundOrderId = data.getString("pay_fund_order_id");
            String outBizNo = data.getString("out_biz_no");
            String orderId = data.getString("order_id");
            String status = data.getString("status");

            RechargeRecord rechargeRecord = rechargeRecordBiz.getByRechargeId(outBizNo);
            if (rechargeRecord.getRechargeStatus() != null && (rechargeRecord.getRechargeStatus() == RechargeStatusEnum.SUCCESS.getCode().shortValue()
                    || rechargeRecord.getRechargeStatus() == RechargeStatusEnum.FAIL.getCode().shortValue())) {
                log.info("充值记录已经为终态，无需继续更新订单信息 订单号: {} 通道单号: {} 记录状态: {}", outBizNo, orderId, rechargeRecord.getRechargeStatus());
                return;
            }

            rechargeRecord.setChannelOrderId(orderId);

            if (StringUtils.equals(status, SuccessFailEnum.SUCCESS.name())) {
                rechargeRecord.setTransPayTime(DateUtil.parseTime(payDate));
                rechargeRecord.setChannelTrxNo(payFundOrderId);
                rechargeRecord.setRechargeStatus(RechargeStatusEnum.SUCCESS.getCode().shortValue());
            }else {
                rechargeRecord.setRechargeStatus(RechargeStatusEnum.FAIL.getCode().shortValue());
            }

            // 查询当前余额
            final EmployerAccountInfo accountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(rechargeRecord.getEmployerNo(), rechargeRecord.getMainstayNo(), rechargeRecord.getChannelType().intValue());
            final BigDecimal balance = accountQueryBiz.getBalance(accountInfo);
            rechargeRecord.setCurrentBalance(balance);

            rechargeRecordBiz.updateById(rechargeRecord);
            rechargeRecordBiz.notifyRechargeMerchant(rechargeRecord);
            if (StringUtils.equals(status, SuccessFailEnum.SUCCESS.name())){
                orderBiz.save2CkAcctDetail(rechargeRecord);
            }
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_BUSY, consumeThreadMax = 1, consumerGroup = "busyRetryConsume",consumeMode = ConsumeMode.ORDERLY)
    public class AlipayUnkownExceptionRetryListener extends BaseRocketMQListener<String> {

        @Autowired
        private RecordItemBiz recordItemBiz;
        @Reference
        private PayBankFacade payBankFacade;
        @Autowired
        private TradeNotifyBiz tradeNotifyBiz;
        @Autowired
        private RedisClient redisClient;
        @Reference
        private NotifyFacade notifyFacade;
        @Autowired
        private TradeFactory tradeFactory;

        @Override
        public void validateJsonParam(String jsonParam) {
            AssertUtil.notNull(jsonParam,"消息体不能为空");
        }

        @Override
        public void consumeMessage(String jsonParam) {
            PayReqVo payReqVo = JSONObject.parseObject(jsonParam, PayReqVo.class);
            try {
                payBankFacade.pay(payReqVo);
                final RecordItem byRemitPlatTrxNo = recordItemBiz.getByRemitPlatTrxNo(payReqVo.getBankOrderNo());
                if (byRemitPlatTrxNo != null) {
                    tradeFactory.getGrantor(byRemitPlatTrxNo.getProductNo()).notifyGrantBatchCount(byRemitPlatTrxNo.getPlatBatchNo());
                }
            } catch (BizException e) {

                log.info("getApiErrorCode:{}",e.getApiErrorCode());
                if (StringUtils.equals(e.getApiErrorCode(), ApiExceptions.API_BIZ_FAIL.getApiErrorCode())) {
                    RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(payReqVo.getBankOrderNo());
                    if (recordItem == null) {
                        log.warn("[{}]找不到打款记录记录", payReqVo.getBankOrderNo());
                        return;
                    }
                    AbstractGrantHandler handler = tradeFactory.getGrantor(recordItem.getProductNo());
                    // 失败处理
                    commonBiz.updateProcessFail(recordItem, e.getApiErrorCode(), e.getErrMsg(),
                            String.valueOf(e.getSysErrorCode()), e.getErrMsg());
                    // 通知更新处理数据
                    handler.notifyGrantBatchCount(recordItem.getPlatBatchNo());
                    OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
                    handler.notifyOrderComplete(orderItem, recordItem);

                }
            }

        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_RETRY,selectorExpression = MessageMsgDest.TAG_CMB_RETRY, consumeThreadMax = 1, consumerGroup = "cmb_pay_retry_consumer",consumeMode = ConsumeMode.ORDERLY)
    public class CmbExceptionRetryListener extends BaseRocketMQListener<String> {

        @Autowired
        private RecordItemBiz recordItemBiz;
        @Reference
        private PayBankFacade payBankFacade;
        @Autowired
        private TradeNotifyBiz tradeNotifyBiz;
        @Autowired
        private RedisClient redisClient;
        @Reference
        private NotifyFacade notifyFacade;
        @Autowired
        private TradeFactory tradeFactory;

        @Override
        public void validateJsonParam(String jsonParam) {
            AssertUtil.notNull(jsonParam,"消息体不能为空");
        }

        @Override
        public void consumeMessage(String jsonParam) {
            PayReqVo payReqVo = JSONObject.parseObject(jsonParam, PayReqVo.class);
            try {
                payBankFacade.pay(payReqVo);
                final RecordItem byRemitPlatTrxNo = recordItemBiz.getByRemitPlatTrxNo(payReqVo.getBankOrderNo());
                if (byRemitPlatTrxNo != null) {
                    tradeFactory.getGrantor(byRemitPlatTrxNo.getProductNo()).notifyGrantBatchCount(byRemitPlatTrxNo.getPlatBatchNo());
                }
            } catch (BizException e) {

                log.info("getApiErrorCode:{}",e.getApiErrorCode());
                if (StringUtils.equals(e.getApiErrorCode(), ApiExceptions.API_BIZ_FAIL.getApiErrorCode())) {
                    RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(payReqVo.getBankOrderNo());
                    if (recordItem == null) {
                        log.warn("[{}]找不到打款记录记录", payReqVo.getBankOrderNo());
                        return;
                    }
                    // 失败处理
                    commonBiz.updateProcessFail(recordItem, e.getApiErrorCode(), e.getErrMsg(),
                            String.valueOf(e.getSysErrorCode()), e.getErrMsg());
                    // 通知更新处理数据
                    AbstractGrantHandler handler = tradeFactory.getGrantor(recordItem.getProductNo());
                    handler.notifyGrantBatchCount(recordItem.getPlatBatchNo());
                    OrderItem orderItem = orderItemBiz.getByPlatTrxNo(recordItem.getPlatTrxNo());
                    handler.notifyOrderComplete(orderItem, recordItem);

                } else {
                    if (getAcceptTimes(payReqVo.getBankOrderNo()) < 3) {

                        notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_RETRY,
                                payReqVo.getSubMerchantNo(),
                                payReqVo.getBankOrderNo(),
                                NotifyTypeEnum.CMB_BUSY_RETRY.getValue(),
                                MessageMsgDest.TAG_CMB_RETRY,
                                JSON.toJSONString(payReqVo),
                                MsgDelayLevelEnum.M_1.getValue());
                    }
                }
            }

        }

        private Long getAcceptTimes(String bankOrderNo) {
            Long acceptTime = 0L;
            try {
                acceptTime = redisClient.incr("BUSY_RETRY:"+ bankOrderNo);
                redisClient.expire("BUSY_RETRY:"+ bankOrderNo,60*60);
            }catch (Exception e){
                log.error("[繁忙重试环节: {}] ==> redis获取重试次数异常 忽略", bankOrderNo, e);
            }
            return acceptTime;
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_CALLBACK, selectorExpression = MessageMsgDest.TAG_ALIPAY_TRANS_INCOME,consumeThreadMax = 10, consumerGroup = "alipayTransIncome")
    public class AlipayTransIncomeListener extends BaseRocketMQListener<String>{

        @Autowired
        private RechargeRecordBiz rechargeRecordBiz;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            JSONObject data = JSONObject.parseObject(jsonParam);
            rechargeRecordBiz.alipayIncome(data);
        }
    }
}
