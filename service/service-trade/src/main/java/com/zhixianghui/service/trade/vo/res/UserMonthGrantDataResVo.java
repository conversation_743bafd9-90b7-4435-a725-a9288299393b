package com.zhixianghui.service.trade.vo.res;


import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UserMonthGrantDataResVo extends ApiBizBaseDto {

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;


    /**
     * 本月已发放金额
     */
    private BigDecimal amount;
}
