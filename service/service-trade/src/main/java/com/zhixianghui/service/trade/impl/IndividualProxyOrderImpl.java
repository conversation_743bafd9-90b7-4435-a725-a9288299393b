package com.zhixianghui.service.trade.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.trade.dto.ConfirmInvoiceDto;
import com.zhixianghui.facade.trade.dto.IndividualProxyOrderDto;
import com.zhixianghui.facade.trade.dto.ProxyOrderQueryDto;
import com.zhixianghui.facade.trade.entity.IndividualProxyOrder;
import com.zhixianghui.facade.trade.service.IndividualProxyOrderFacade;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.trade.process.IndividualProxyBiz;
import com.zhixianghui.service.trade.process.WxJsapiPayBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class IndividualProxyOrderImpl implements IndividualProxyOrderFacade {

    @Autowired
    private IndividualProxyBiz individualProxyBiz;

    @Autowired
    private WxJsapiPayBiz wxJsapiPayBiz;

    @Override
    public IndividualProxyOrder applyInvoice(IndividualProxyOrderDto dto, WeChatLoginVo weChatLoginVo) throws BizException {
        return individualProxyBiz.applyInvoice(dto, weChatLoginVo);
    }

    @Override
    public Page<IndividualProxyOrder> listPage(ProxyOrderQueryDto proxyOrderQueryDto, Page page) {
        return individualProxyBiz.listOrderPage(proxyOrderQueryDto, page);
    }

    @Override
    public IndividualProxyOrder getProxyOrderById(Long id, String idcardNo) {
        return individualProxyBiz.getProxyOrderById(id, idcardNo);
    }

    @Override
    public IndividualProxyOrder getProxyOrderById(Long id) {
        return individualProxyBiz.getProxyOrderById(id);
    }

    @Override
    public void confirmInvoice(ConfirmInvoiceDto confirmInvoiceDto) throws BizException{
        individualProxyBiz.confirmInvoice(confirmInvoiceDto);
    }

    @Override
    public IndividualProxyOrder cancelProxyOrderById(Long id,String idcardNo) {
        return individualProxyBiz.cancelProxyOrderById(id, idcardNo);
    }

    @Override
    public String prePay(String orderNo, WeChatLoginVo weChatLoginVo) throws BizException {
        return wxJsapiPayBiz.prePay(orderNo,weChatLoginVo);
    }
}
