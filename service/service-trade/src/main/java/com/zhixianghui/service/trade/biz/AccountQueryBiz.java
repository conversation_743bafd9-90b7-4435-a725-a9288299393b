package com.zhixianghui.service.trade.biz;

import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class AccountQueryBiz {
    @Reference
    private AccountQueryFacade accountQueryFacade;

    /**
     * 查询余额
     *
     * @return
     */
    public BigDecimal getBalance(EmployerAccountInfo accountInfo) {
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setEmployerNo(accountInfo.getEmployerNo());
        amountQueryDto.setChannelType(accountInfo.getChannelType());
        amountQueryDto.setAgreementNo(accountInfo.getSubAgreementNo());
        amountQueryDto.setMainstayNo(accountInfo.getMainstayNo());
        amountQueryDto.setChannelNo(accountInfo.getPayChannelNo());
        amountQueryDto.setChannelMchNo(accountInfo.getParentMerchantNo());
        amountQueryDto.setSubMerchantNo(accountInfo.getSubMerchantNo());
        final String amount = accountQueryFacade.getAmount(amountQueryDto);

        return amount == null ? null : new BigDecimal(amount);
    }

    public BigDecimal getBalance(AmountQueryDto amountQueryDto){
        final String amount = accountQueryFacade.getAmount(amountQueryDto);
        return amount == null ? null : new BigDecimal(amount);
    }
}
