package com.zhixianghui.service.trade.impl;

import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.factory.TradeFactory;
import com.zhixianghui.service.trade.process.WithdrawBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 订单批次表 Facade实现类
 *
 * <AUTHOR>
 * @version 创建时间： 2020-11-03
 */
@Service(timeout = 120000)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderImpl implements OrderFacade {

    private final OrderBiz orderBiz;
    private final WithdrawBiz withdrawBiz;
    private final TradeFactory tradeFactory;

    @Override
    public Long insert(Order order) {
        return orderBiz.insert(order);
    }

    @Override
    public void startAccept(Order order) {
        //acceptBiz.initAccept(order);
        tradeFactory.getAcceptor(order.getProductNo()).initAccept(order);
    }

    @Override
    public PageResult<List<Order>> listPage(Map<String, Object> paramMap, PageParam pageParam) {
        return orderBiz.listPage(paramMap,pageParam);
    }

    @Override
    public void update(Order order) {
        orderBiz.update(order);
    }

    @Override
    public Order getOne(Map<String, Object> paramMap) {
        return orderBiz.getOne(paramMap);
    }

    @Override
    public void cancelBatchOrder(Order order) {
        orderBiz.cancelBatchOrder(order);
    }

    @Override
    public void cancelOrderItem(String platBatchNo){
        orderBiz.cancelOrderItem(platBatchNo);

    }

    @Override
    public Long countOrder(Map<String, Object> paramMap) {
        return orderBiz.countOrder(paramMap);
    }

    @Override
    public BigDecimal sumWaitInvoiceAmount(Map<String, Object> paramMap) {
        return orderBiz.sumWaitInvoiceAmount(paramMap);
    }

    @Override
    public BigDecimal sumWaitCepInvoiceAmount(Integer amountType, Map<String, Object> paramMap) {
        return orderBiz.sumWaitCepInvoiceAmount(amountType, paramMap);
    }

    @Override
    public BigDecimal sumWaitCkhInvoiceAmount(Map<String, Object> paramMap) {
        return orderBiz.sumWaitCkhInvoiceAmount(paramMap);
    }

    @Override
    public RechargeRecord addRechargeRecord(RechargeRecord rechargeRecord) {
        return orderBiz.addRechargeRecord(rechargeRecord);
    }

    @Override
    public RechargeRecord upDateRechargeRecord(RechargeRecord rechargeRecord) {
        return orderBiz.upRechargeRecord(rechargeRecord);
    }

    @Override
    public WithdrawRecord Withdraw(WithdrawDto withdrawDto) {
        return withdrawBiz.withdraw(withdrawDto);
    }

    @Override
    public void updateWithdrawRecord(String outBizNo, SuccessFailEnum successFailEnum, String failReason) {
        withdrawBiz.updateWithdrawRecord(outBizNo,successFailEnum,failReason);
    }

    @Override
    public void delete(OrderDeleteDTO orderDeleteDTO) {
        orderBiz.delete(orderDeleteDTO);
    }

    @Override
    public Order getByPlatBatchNo(String platBatchNo) {
        return orderBiz.getByPlatBatchNo(platBatchNo);
    }

    @Override
    public boolean isExistNotCompleteOrder(String employerNo, String mainstayNo) {
        return orderBiz.isExistNotCompleteOrder(employerNo,mainstayNo);
    }

}
