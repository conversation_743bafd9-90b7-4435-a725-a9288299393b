package com.zhixianghui.service.trade.vo.res;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 签约回调结果
 * @date 2021/1/20 18:24
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JSONType(naming= PropertyNamingStrategy.SnakeCase)
public class NotifyMchSignResVo extends ApiBizBaseDto {
    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 电话号码
     */
    private String phoneNo;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 签约状态
     */
    private Integer signStatus;
}
