package com.zhixianghui.service.trade.listener;

import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.service.trade.biz.WeChatUserBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AgentMiniAppListener
 * @Description TODO
 * @Date 2023/9/13 17:25
 */
@Component
public class AgentMiniAppListener {

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_AGENT_GET_OR_REGISTER,selectorExpression = MessageMsgDest.TAG_AGENT_GET_OR_REGISTER,consumeThreadMax = 16,consumerGroup = "agentGetOrRegisterGroup")
    public class AgentGetOrRegisterListener extends BaseRocketMQListener<String>{

        @Autowired
        private WeChatUserBiz weChatUserBiz;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            Map<String,Object> infoMap = JsonUtil.toBean(jsonParam, new TypeReference<Map<String,Object>>(){});
            String phone = (String) infoMap.get("phone");
            String code = (String) infoMap.get("code");
            weChatUserBiz.getOrRegister(phone,code);
        }
    }
}
