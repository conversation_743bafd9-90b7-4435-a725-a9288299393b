package com.zhixianghui.service.trade.controller.ckh;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.beust.jcommander.internal.Lists;
import com.zhixianghui.api.base.dto.RequestDto;
import com.zhixianghui.api.base.dto.ResponseDto;
import com.zhixianghui.api.base.enums.BizCodeEnum;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.*;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.service.trade.biz.OrderBiz;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.helper.ZxhLimitBiz;
import com.zhixianghui.service.trade.process.CKHAcceptBiz;
import com.zhixianghui.service.trade.vo.ckh.req.CKHSingleGrantReqVo;
import com.zhixianghui.service.trade.vo.req.QueryDetailReqVo;
import com.zhixianghui.service.trade.vo.res.QueryDetailResVo;
import com.zhixianghui.service.trade.vo.res.SingleGrantResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 受理发放对外接口
 * @date 2020-12-16 11:46
 **/
@Slf4j
@RestController
@RequestMapping("ckh")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CKHSingleGrantController {

    private final ZxhLimitBiz limitBiz;
    private final OrderBiz orderBiz;
    //private final TradeNotifyBiz tradeNotifyBiz;
    private final CKHAcceptBiz ckhAcceptBiz;
    private final OrderItemBiz orderItemBiz;

    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;
    @Reference
    private DataDictionaryFacade dictionaryFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private MerchantSecretFacade merchantSecretFacade;

    @PostMapping("/singleGrant")
    public ResponseDto<SingleGrantResVo> singleGrant(@RequestBody @Valid RequestDto<CKHSingleGrantReqVo> paramVo){
        CKHSingleGrantReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();
        String mainstayNo = reqVo.getMainstayNo();
        String mchOrderNo = reqVo.getMchOrderNo();
        Integer channelType = reqVo.getChannelType();
        String signType = paramVo.getSignType();
        String jobNo = reqVo.getJobNo();

        String logFlag = String.join("-",ProductNoEnum.CKH.getValue(),mchOrderNo,employerNo,mainstayNo);
        log.info("[{}]==>创客汇对外接口，单笔发放受理开始：{}", logFlag, JsonUtil.toString(paramVo));
        try{
            // 敏感传输参数解密
            decryptParam(reqVo,paramVo.getSecKey());
            // 校验收到参数合法性
            checkParam(reqVo);
            // 检验产品开通
            limitBiz.validateProductAndQuoteOpen(employerNo,mainstayNo, ProductNoEnum.CKH.getValue());
            // 校验商户信息状态
            limitBiz.validateMchInfo(logFlag, employerNo,mainstayNo);
            // 校验报备账户状态
            EmployerAccountInfo employerAccountInfo =limitBiz.validateAccountInfo(logFlag,employerNo,mainstayNo,channelType);
            //校验风控规则
            limitBiz.validateRiskControlRule(employerNo,mainstayNo);
            //校验账单是否支付完成
            checkPayOrder(employerNo,mainstayNo);
            //任务校验
            Job job = limitBiz.getJobByJobNo(employerNo,jobNo);
            //服务岗位的处理
            MerchantEmployerPosition merchantEmployerPosition = limitBiz.validateCKHEmployerPosition(employerNo,job);
            //批次记录/订单明细 入库
            Order order = fillOrder(job,employerAccountInfo,reqVo,merchantEmployerPosition,signType);
            OrderItem orderItem = fillOrderItem(order,reqVo);
            orderBiz.insertOrderAndItem(order,orderItem);
            //返回给商户 已创建状态
            SingleGrantResVo singleGrantResVo = fillSingleGrantResVo(orderItem);
            //发通知 进行受理和发放
            ckhAcceptBiz.notifyAcceptStart(employerNo,order.getPlatBatchNo(), Lists.newArrayList(orderItem.getPlatTrxNo()));
            log.info("[{}]==>单笔发放受理完成", logFlag);
            return ResponseDto.success(singleGrantResVo,"");
        }catch (BizException e){
            log.error("[{}]==>单笔发放受理结束 业务异常：", logFlag, e);
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            log.error("[{}]==>单笔发放受理结束 系统异常：", logFlag, e);
            return ResponseDto.unknown();
        }
    }

    private void checkPayOrder(String employerNo,String mainstayNo) {
        //查询用工企业账单是否已全部支付
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("productNo", ProductNoEnum.CKH.getValue());
        paramMap.put("notStatus", FeeOrderStatus.SUCCESS.getCode());
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("feeSource", FeeSourceEnum.PLATFORM_ORDER.getValue());
        if (feeOrderBatchFacade.isExistNotPay(paramMap)){
            throw ApiExceptions.API_BIZ_FAIL.newWithErrMsg("商户仍有未支付账单，请先支付后发");
        }
    }

    private OrderItem fillOrderItem(Order order, CKHSingleGrantReqVo reqVo) {
        Date now = new Date();
        OrderItem orderItem = new OrderItem();
        orderItem.setCreateDate(now);
        orderItem.setCreateTime(now);
        orderItem.setUpdateTime(now);
        orderItem.setMchBatchNo(order.getMchBatchNo());
        orderItem.setPlatBatchNo(order.getPlatBatchNo());
        String platTrxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.CKH_ORDER_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now),SequenceBizKeyEnum.CKH_ORDER_ITEM_SEQ.getKey(),SequenceBizKeyEnum.CKH_ORDER_ITEM_SEQ.getWidth());
        orderItem.setMchOrderNo(reqVo.getMchOrderNo());
        orderItem.setPlatTrxNo(platTrxNo);
        orderItem.setLaunchWay(LaunchWayEnum.API.getValue());
        orderItem.setEmployerNo(order.getEmployerNo());
        orderItem.setEmployerName(order.getEmployerName());
        orderItem.setMainstayNo(order.getMainstayNo());
        orderItem.setMainstayName(order.getMainstayName());
        orderItem.setChannelType(order.getChannelType());
        orderItem.setPayChannelNo(order.getPayChannelNo());
        orderItem.setChannelName(order.getChannelName());
        orderItem.setOrderItemTaskAmount(new BigDecimal(reqVo.getOrderItemTaskAmount()));
        orderItem.setOrderItemStatus(OrderItemStatusEnum.CREATE.getValue());
        orderItem.setAccessTimes(0);
        orderItem.setRemark(reqVo.getRemark());
        orderItem.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        orderItem.setReceiveNameEncrypt(reqVo.getReceiveName());
        orderItem.setReceiveIdCardNoEncrypt(reqVo.getReceiveIdCardNo());
        orderItem.setReceiveAccountNoEncrypt(reqVo.getReceiveAccountNo());
        orderItem.setReceivePhoneNoEncrypt(stringValueOf(reqVo.getReceivePhoneNo()));
        orderItem.setMemo(reqVo.getMemo());
        orderItem.setProductNo(ProductNoEnum.CKH.getValue());
        orderItem.setProductName(ProductNoEnum.CKH.getDesc());
        orderItem.setWorkCategoryCode(order.getWorkCategoryCode());
        orderItem.setWorkCategoryName(order.getWorkCategoryName());
        orderItem.setJobId(order.getJobId());
        orderItem.setJobName(order.getJobName());
        return orderItem;
    }

    private SingleGrantResVo fillSingleGrantResVo(OrderItem orderItem) {
        SingleGrantResVo singleGrantResVo = new SingleGrantResVo();
        singleGrantResVo.setMchOrderNo(orderItem.getMchOrderNo());
        singleGrantResVo.setPlatTrxNo(orderItem.getPlatTrxNo());
        singleGrantResVo.setOrderItemStatus(orderItem.getOrderItemStatus());
        singleGrantResVo.setBizErrCode(StringUtils.isBlank(orderItem.getErrorCode())? BizCodeEnum.IN_PROCESS.getCode() :orderItem.getErrorCode());
        singleGrantResVo.setBizErrMsg(StringUtils.isBlank(orderItem.getErrorDesc())?BizCodeEnum.IN_PROCESS.getMsg():orderItem.getErrorDesc());
        return singleGrantResVo;
    }

    private void decryptParam(CKHSingleGrantReqVo reqVo, String secKey) {
        reqVo.setReceiveName(limitBiz.decryptNotNull(reqVo.getReceiveName(), "receive_name", secKey));
        reqVo.setReceiveIdCardNo(limitBiz.decryptNotNull(reqVo.getReceiveIdCardNo(), "receive_id_card_no", secKey));
        reqVo.setReceivePhoneNo(limitBiz.decrypt(reqVo.getReceivePhoneNo(), "receive_phone_no", secKey));
        reqVo.setReceiveAccountNo(limitBiz.decryptNotNull(reqVo.getReceiveAccountNo(), "receive_account_no", secKey));
    }

    private void checkParam(CKHSingleGrantReqVo reqVo) {
        if (!ValidateUtil.isChineseName(reqVo.getReceiveName())) {
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receiveName  姓名(" + reqVo.getReceiveName() + ")格式错误");
        }

        if (StringUtils.isNotEmpty(reqVo.getCallbackUrl()) && !ValidateUtil.isURL(reqVo.getCallbackUrl())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("callback_url 回调地址格式有误");
        }
        if(StringUtils.isNotEmpty(reqVo.getReceivePhoneNo())
                && !ValidateUtil.isMobile(reqVo.getReceivePhoneNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_phone_no  手机号码(" + reqVo.getReceivePhoneNo() + ")格式错误");
        }
        if(!IDCardUtils.verifi(reqVo.getReceiveIdCardNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_id_card_no 身份证号(" + reqVo.getReceiveIdCardNo() + ")格式错误");
        }

        String phoneMustMainstayConfig = dictionaryFacade.getSystemConfig("PHONE_MUST_MAINSTAY");
        if (StringUtils.isNotBlank(phoneMustMainstayConfig)) {
            ArrayList<String> phoneMustMainstays = ListUtil.toList(phoneMustMainstayConfig.split(","));
            if (phoneMustMainstays != null && phoneMustMainstays.contains(reqVo.getMainstayNo()) && StringUtils.isBlank(reqVo.getReceivePhoneNo())) {
                throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg(StrUtil.format("该代征主体[]收款人手机号必填",reqVo.getMainstayNo()));
            }
        }


        /**
        //由于银行卡校验算法存在误杀情况，去掉该校验
        if(Objects.equals(ChannelTypeEnum.BANK.getValue(), reqVo.getChannelType())
                && !ValidateUtil.isBankCard(reqVo.getReceiveAccountNo())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("receive_account_no 银行卡号(" + reqVo.getReceiveAccountNo() + ")格式错误");
        }**/
        if(!ValidateUtil.isDoubleAnd2decimals(reqVo.getOrderItemTaskAmount())){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("order_item_task_amount 任务金额[" + reqVo.getOrderItemTaskAmount() + "]格式错误,必须是数字,且最多两位小数");
        }
        if(new BigDecimal(reqVo.getOrderItemTaskAmount()).compareTo(new BigDecimal("0.1")) < 0){
            throw ApiExceptions.API_PARAM_FAIL.newWithErrMsg("order_item_task_amount 任务金额(" + reqVo.getOrderItemTaskAmount() + ")有误，必须大于等于0.1元");
        }
    }


    private Order fillOrder(Job job,EmployerAccountInfo employerAccountInfo, CKHSingleGrantReqVo reqVo, MerchantEmployerPosition merchantEmployerPosition, String signType) {
        String batchId = sequenceFacade.nextRedisId("",SequenceBizKeyEnum.CKH_ORDER_SEQ.getKey(), SequenceBizKeyEnum.CKH_ORDER_SEQ.getWidth());
        Date batchTime = new Date();
        String batchNo = SequenceBizKeyEnum.CKH_ORDER_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
        //时间
        Order order = new Order();
        order.setCreateDate(batchTime);
        order.setCreateTime(batchTime);
        order.setUpdateTime(batchTime);
        //账户
        order.setMchBatchNo(batchNo);
        order.setBatchName(String.join("-", DateUtil.formatDate(new Date()),employerAccountInfo.getEmployerName()));
        order.setPlatBatchNo(batchNo);
        order.setEmployerNo(employerAccountInfo.getEmployerNo());
        order.setEmployerName(employerAccountInfo.getMchName());
        order.setMainstayNo(employerAccountInfo.getMainstayNo());
        order.setMainstayName(employerAccountInfo.getMainstayName());
        order.setChannelType(employerAccountInfo.getChannelType());
        order.setPayChannelNo(employerAccountInfo.getPayChannelNo());
        order.setChannelName(employerAccountInfo.getPayChannelName());
        order.setBatchStatus(OrderStatusEnum.IMPORTING.getValue());
        order.setRequestCount(1);
        //任务金额=实发金额
        order.setRequestTaskAmount(new BigDecimal(reqVo.getOrderItemTaskAmount()));
        //岗位
        order.setWorkCategoryCode(merchantEmployerPosition.getWorkCategoryCode());
        order.setWorkCategoryName(merchantEmployerPosition.getWorkCategoryName());
        order.setServiceDesc(merchantEmployerPosition.getServiceDesc());
        //api
        order.setLaunchWay(LaunchWayEnum.API.getValue());
        order.setCallbackUrl(reqVo.getCallbackUrl());
        order.getJsonEntity().setSignType(signType);
        //产品
        order.setProductNo(ProductNoEnum.CKH.getValue());
        order.setProductName(ProductNoEnum.CKH.getDesc());
        //
        order.setJobId(job.getJobId());
        order.setJobName(job.getJobName());
        return order;
    }

    private static String stringValueOf(Object obj) {
        return (obj == null) ? " " : obj.toString();
    }

    @PostMapping("/queryDetail")
    public ResponseDto<QueryDetailResVo> queryDetail(@RequestBody @Valid RequestDto<QueryDetailReqVo> paramVo) {
        QueryDetailReqVo reqVo = paramVo.getData();
        String employerNo = paramVo.getMchNo();

        String logFlag = employerNo + "-" + reqVo.getMchOrderNo();
        log.info("[{}]查询订单详情信息：{}", logFlag, JsonUtil.toString(paramVo));
        try {
            // 检验产品开通
            limitBiz.validateProductOpen(employerNo, ProductNoEnum.CKH.getValue());

            // 查询订单明细
            OrderItem item = orderItemBiz.getByEmployerNoAndMchOrderNoApi(employerNo, reqVo.getMchOrderNo());
            if (item == null) {
                throw ApiExceptions.API_TRADE_ORDER_NOT_EXIST;
            }
            String key = RandomUtil.get16LenStr();
            QueryDetailResVo result = fillQueryDetailRespVo(item, key);
            ResponseDto<QueryDetailResVo> returnVo = ResponseDto.success(result,key);
            log.info("[{}]查询订单详情成功：{}", logFlag, JsonUtil.toString(returnVo));
            return returnVo;
        }catch (BizException e){
            log.error("[{}]==>查询订单详情 业务异常：", logFlag, e);
            return ResponseDto.fail(e.getApiErrorCode(),e.getErrMsg());
        }catch (Exception e){
            log.error("[{}]==>查询订单详情 系统异常：", logFlag, e);
            return ResponseDto.unknown();
        }
    }

    private QueryDetailResVo fillQueryDetailRespVo(OrderItem item, String key) {
        QueryDetailResVo queryDetailResVo = new QueryDetailResVo();
        BeanUtils.copyProperties(item, queryDetailResVo);
        queryDetailResVo.setMemo(item.getMemo());
        //敏感信息处理
        queryDetailResVo.setReceiveName(AESUtil.encryptECB(item.getReceiveNameDecrypt(), key));
        queryDetailResVo.setReceiveIdCardNo(AESUtil.encryptECB(item.getReceiveIdCardNoDecrypt(), key));
        queryDetailResVo.setReceiveAccountNo(AESUtil.encryptECB(item.getReceiveAccountNoDecrypt(), key));
        queryDetailResVo.setReceivePhoneNo(AESUtil.encryptECB(item.getReceivePhoneNoDecrypt(), key));
        //金额字符化
        queryDetailResVo.setOrderItemNetAmount(String.valueOf(item.getOrderItemNetAmount()));
        queryDetailResVo.setOrderItemFee(String.valueOf(item.getOrderItemFee()));
        queryDetailResVo.setOrderItemAmount(String.valueOf(item.getOrderItemAmount()));
        //任务名称
        queryDetailResVo.setJobNo(item.getJobId());
        queryDetailResVo.setJobName(item.getJobName());
        //错误描述
        queryDetailResVo.setBizErrCode(StringUtils.isBlank(item.getErrorCode())? BizCodeEnum.SUCCESS.getCode() : item.getErrorCode());
        queryDetailResVo.setBizErrMsg(StringUtils.isBlank(item.getErrorDesc())? BizCodeEnum.SUCCESS.getMsg() : item.getErrorDesc());
        return queryDetailResVo;
    }
}
