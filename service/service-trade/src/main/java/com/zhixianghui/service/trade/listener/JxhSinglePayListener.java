package com.zhixianghui.service.trade.listener;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.banklink.vo.jxh.ResData;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.service.trade.biz.JXHLocalPayBiz;
import com.zhixianghui.service.trade.biz.RecordItemBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/***
 * 君享汇代付回调的消息处理类
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_JXH_SINGLEPAY_ASYNC,
        selectorExpression = MessageMsgDest.TAG_JXH_SINGLEPAY, consumeThreadMax = 3,
        consumerGroup = "jxhSinglePayConsumer")
public class JxhSinglePayListener extends BaseRocketMQListener<String> {

    @Resource
    private RecordItemBiz recordItemBiz;
    @Resource
    private JXHLocalPayBiz jxhLocalPayBiz;

    @Override
    public void validateJsonParam(String jsonParam) {

    }


    /***
     * 交易状态
     * 201 订单已创建 汇聚受理并创建代付订单的初始状态
     * 202 待商户审核 代付订单处在等待商户审核的阶段
     * 203 交易处理中  代付订单处理中状态
     * 204 交易失败    明确交易失败的状态码
     * 205 交易成功    明确交易成功的状态码
     * 208 订单已取消   代付订单被商户审核为拒绝付款
     * 210 账务冻结中   确认出款后对商户的账户余额进行冻结处理
     * 211 账务解冻中   确认失败后对商户的账户余额进行解冻处理
     * 212 订单取消中   商户审核为拒绝付款的中间状态
     * 213 账务扣款中   确认成功后对商户的账户冻结款的扣款处理
     * 214 订单不存在   汇聚未受理该笔代付请求，找不到该笔订单，明确失败
     * @param jsonParam
     */
    @Override
    public void consumeMessage(String jsonParam) {
        log.info("君享汇单笔下发业务，接收到的参数：" + jsonParam);
        ResData data = JSONUtil.toBean(jsonParam, ResData.class);
        data.setErrorDesc(data.getErrorCodeDesc());
        RecordItem recordItem = recordItemBiz.getByRemitPlatTrxNo(data.getMerchantOrderNo());
        PayReqVo reqVo = new PayReqVo();
        reqVo.setChannelNo(recordItem.getPayChannelNo());
        reqVo.setChannelName(recordItem.getChannelName());
        reqVo.setEmployerNo(recordItem.getEmployerNo());
        reqVo.setMchName(recordItem.getEmployerName());
        reqVo.setMainstayNo(recordItem.getMainstayNo());
        reqVo.setRealPayerName(recordItem.getMainstayName());
        reqVo.setPlatTrxNo(recordItem.getPlatTrxNo());
        reqVo.setBankOrderNo(recordItem.getRemitPlatTrxNo());
        reqVo.setServiceFee(recordItem.getOrderFee().toString());
        // 渠道流水号
        reqVo.setBankTrxNo(data.getPlatformSerialNo());
        log.info("君享汇回调参数构建reqVo:{}", JSONObject.toJSONString(reqVo));
        jxhLocalPayBiz.payQuery(data, reqVo);
    }
}
