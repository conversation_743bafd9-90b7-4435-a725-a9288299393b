package com.zhixianghui.service.trade.listener.tasks;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.service.trade.biz.OrderItemBiz;
import com.zhixianghui.service.trade.biz.RechargeRecordBiz;
import com.zhixianghui.service.trade.biz.WithdrawRecordBiz;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_ACCOUNT_BOOK_BALANCE_CHECK,consumeThreadMax = 1,consumerGroup = "cmbBalanceCheckConsumer")
@Slf4j
public class CmbAccountBookBalanceCheckListener extends TaskRocketMQListener<JSONObject> {

    @Autowired
    private OrderItemBiz orderItemBiz;
    @Autowired
    private RechargeRecordBiz rechargeRecordBiz;
    @Autowired
    private WithdrawRecordBiz withdrawRecordBiz;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private CmbFacade cmbFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private RobotFacade robotFacade;


    @Override
    public void runTask(JSONObject jsonParam) {
        //时间处理
        Date now = new Date();

        String tradeDayStr = jsonParam.getString("tradeDay");

        Date dayStart;
        Date dayEnd;
        Date yestorday = DateUtil.addDay(new Date(), -1);
        if (StringUtils.isBlank(tradeDayStr)) {
            dayStart = DateUtil.getDayStart(yestorday);
            dayEnd   = DateUtil.getDayEnd(yestorday);
        }else {
            dayStart = DateUtil.getDayStart(DateUtil.parse(tradeDayStr));
            dayEnd = DateUtil.getDayEnd(DateUtil.parse(tradeDayStr));
        }

        // 查询出在招行通道产生交易的商户以及交易金额
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("completeBeginDate", dayStart);
        paramMap.put("completeEndDate", dayEnd);
        log.info("查询出在招行通道产生交易的商户以及交易金额，时间范围:{}-{}",dayStart,dayEnd);
        List<Map<String, Object>> mapList = orderItemBiz.sumCmbOrderAmt(paramMap);
        if (mapList == null || mapList.isEmpty()) {
            log.info("该时间段无招行通道的交易");
            return;
        }

        for (Map<String, Object> sumItem : mapList) {
            String employerNo = (String) sumItem.get("employerNo");
            String mainstayNo = (String) sumItem.get("mainstayNo");
            BigDecimal sumNetAmt = (BigDecimal) sumItem.get("sumNetAmt");
            BigDecimal sumFee = (BigDecimal) sumItem.get("sumFee");
            BigDecimal sumAmt = (BigDecimal) sumItem.get("sumAmt");

            //查询出商户的充值金额
            Map<String, Object> rParams = new HashMap<>();
            rParams.put("mainstayNo", mainstayNo);
            rParams.put("employerNo", employerNo);
            rParams.put("completeBeginDate", dayStart);
            rParams.put("completeEndDate", dayEnd);
            Map<String, Object> sumCmbRechargeAmt = rechargeRecordBiz.sumCmbRechargeAmt(rParams);

            BigDecimal sumRechargeAmt = (sumCmbRechargeAmt == null || sumCmbRechargeAmt.get("sumRechargeAmt") == null) ? BigDecimal.ZERO : (BigDecimal) sumCmbRechargeAmt.get("sumRechargeAmt");

            //查询出商户的提现金额
            Map<String, Object> wParams = new HashMap<>();
            wParams.put("mainstayNo", mainstayNo);
            wParams.put("employerNo", employerNo);
            wParams.put("completeBeginDate", dayStart);
            wParams.put("completeEndDate", dayEnd);
            Map<String, Object> sumCmbWithdrawAmt = withdrawRecordBiz.sumCmbWithdrawAmt(wParams);

            BigDecimal sumWithdrawAmt = (sumCmbWithdrawAmt == null || sumCmbWithdrawAmt.get("sumWithdrawAmt") == null) ? BigDecimal.ZERO : (BigDecimal) sumCmbWithdrawAmt.get("sumWithdrawAmt");

            // 招行接口查询余额

//            EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(employerNo, mainstayNo, ChannelNoEnum.CMB.name());
            MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(mainstayNo, ChannelNoEnum.CMB.name());

            String queryDateBegin = cn.hutool.core.date.DateUtil.offsetDay(dayStart, -1).toString("yyyyMMdd");
            String queryDateEnd = cn.hutool.core.date.DateUtil.date(dayStart).toString("yyyyMMdd");
            JSONObject cmbBalance = cmbFacade.queryDailyAccountNoBalance(
                    mainstayChannelRelation.getJoinBankNo(),
                    mainstayChannelRelation.getChannelMchNo(),
                    employerNo.replace("M",""),
                    queryDateBegin,
                    queryDateEnd
            );
            JSONArray array = cmbBalance.getJSONArray("ntdmahadz1");

            Map<String, BigDecimal> balanceMap = new HashMap<>();
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                BigDecimal dmabal = jsonObject.getBigDecimal("dmabal");
                String psedat = jsonObject.getString("psedat");
                if (dmabal == null) {
                    dmabal = BigDecimal.ZERO;
                }
                balanceMap.put(psedat, dmabal);
            }

            BigDecimal amt = balanceMap.get(queryDateEnd).subtract(balanceMap.get(queryDateBegin) == null ? BigDecimal.ZERO : balanceMap.get(queryDateBegin));

            if (amt.compareTo(sumRechargeAmt.subtract(sumWithdrawAmt).subtract(sumAmt)) != 0) {
                log.info("招行账务对账不平，商户：{},供应商：{}",employerNo,mainstayNo);
                /**
                 * 如果账户变动金额不等于 充值金额-提现金额-发放金额 则账不平
                 */
                StringBuilder stringBuilder = new StringBuilder("#### 招行对账["+DateUtil.formatDate(dayStart)+"]-异常通知");
                stringBuilder.append("\\n > [招行对账]招行对账不平，请登录后台进行确认\n");
                stringBuilder.append("\\n > 商户编号:"+employerNo+"\n");
                stringBuilder.append("\\n > 供应商编号:"+mainstayNo+"\n");
                stringBuilder.append("\\n > 发放订单金额:"+sumAmt.toPlainString()+"\n");
                stringBuilder.append("\\n > 充值金额:"+sumRechargeAmt.toPlainString()+"\n");
                stringBuilder.append("\\n > 提现金额:"+sumWithdrawAmt.toPlainString()+"\n");
                stringBuilder.append("\\n > "+queryDateBegin+" 余额:"+balanceMap.get(queryDateBegin).toPlainString()+"\n");
                stringBuilder.append("\\n > "+queryDateEnd+" 余额:"+balanceMap.get(queryDateEnd).toPlainString()+"\n");

                MarkDownMsg markDownMsg = new MarkDownMsg();
                markDownMsg.setContent(stringBuilder.toString());
                markDownMsg.setRobotType(RobotTypeEnum.TASK_NOTIFY_ROBOT.getType());
                markDownMsg.setUnikey(IdUtil.fastSimpleUUID());
                robotFacade.pushMarkDownAsync(markDownMsg);
            }else {
                log.info("招行账务对账正常，商户：{},供应商：{}",employerNo,mainstayNo);
            }

        }


    }
}
