package com.zhixianghui.service.trade.listener;

import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.service.trade.process.CKHAcceptBiz;
import com.zhixianghui.service.trade.process.ZXHAcceptBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AcceptListener
 * @Description TODO
 * @Date 2022/6/28 18:31
 */
@Slf4j
@Component
public class AcceptListener {

    @Autowired
    private CKHAcceptBiz ckhAcceptBiz;

    @Autowired
    private ZXHAcceptBiz zxhAcceptBiz;


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC_CKH,selectorExpression = MessageMsgDest.TAG_ACCEPT_BATCH_COUNT_CKH,consumeThreadMax = 20,consumerGroup = "ckhAcceptBatchCount")
    public class CKHAcceptBatchCountListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String platBatchNo) {
            if (StringUtil.isEmpty(platBatchNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("platBatchNo不能为null");
            }
        }

        @Override
        public void consumeMessage(String platBatchNo) {
            ckhAcceptBiz.updateAcceptBatchCount(platBatchNo);
        }
    }

    /**
     * 创客汇异常处理
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC_CKH,selectorExpression = MessageMsgDest.TAG_ACCEPT_NOT_BIZ_EXCEPTION_CKH,consumeThreadMax = 20,consumerGroup = "ckhAcceptExceptionConsume")
    public class CKHAccpetExpcetionListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String platTrxNo) {
            if (StringUtil.isEmpty(platTrxNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("platTrxNo不能为null");
            }
        }

        @Override
        public void consumeMessage(String platTrxNo) {
            ckhAcceptBiz.handleAcceptException(platTrxNo);
        }
    }

    /**
     * 创客汇订单明细受理
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC_CKH, selectorExpression = MessageMsgDest.TAG_TRADE_ACCEPT_CKH,consumeThreadMax = 20, consumerGroup = "ckhAcceptStartConsume")
    public class CKHAcceptStartMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单明细列表 List<PlatTrxNo>不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            Map<String,String> infoMap = JsonUtil.toBean(msg, new TypeReference<Map<String,String>>(){});
            String employerNo = infoMap.get("employerNo");
            String platBatchNo = infoMap.get("platBatchNo");
            List<String> platTrxNos = JsonUtil.toBean(infoMap.get("platTrxNos"), new TypeReference<List<String>>(){});
            try {
                ckhAcceptBiz.startAccept(employerNo,platBatchNo,platTrxNos);
            } catch (Exception e) {
                log.error("[受理环节: {}] -- mchNo:[{}] ==>批次受理异常。platTrxNoList size:{},明细范围[{}]至[{}]"
                        , platBatchNo, employerNo, platTrxNos.size(), platTrxNos.get(0),platTrxNos.get(platTrxNos.size() - 1),e);
                throw e;
            }
        }
    }

    /**
     * 智享汇受理订单明细遇到业务外异常
     * 重发补偿
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC, selectorExpression = MessageMsgDest.TAG_ACCEPT_NOT_BIZ_EXCEPTION,consumeThreadMax = 2, consumerGroup = "handleAcceptExceptionConsume")
    public class HandleAcceptExceptionMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String platTrxNo) {
            if (StringUtil.isEmpty(platTrxNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("platTrxNo不能为null");
            }
        }

        @Override
        public void consumeMessage(String platTrxNo) {
            zxhAcceptBiz.handleAcceptException(platTrxNo);
        }
    }

    /**
     * 智享汇
     * 接收一批订单明细 开始受理
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC, selectorExpression = MessageMsgDest.TAG_TRADE_ACCEPT,consumeThreadMax = 20, consumerGroup = "acceptStartConsume")
    public class AcceptStartMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String msg) {
            if (StringUtil.isEmpty(msg)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单明细列表 List<PlatTrxNo>不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            log.info("接收一批订单明细 开始受理, {}", msg);
            Map<String,String> infoMap = JsonUtil.toBean(msg, new TypeReference<Map<String,String>>(){});
            String employerNo = infoMap.get("employerNo");
            String platBatchNo = infoMap.get("platBatchNo");
            List<String> platTrxNos = JsonUtil.toBean(infoMap.get("platTrxNos"), new TypeReference<List<String>>(){});
            try {
                zxhAcceptBiz.startAccept(employerNo,platBatchNo,platTrxNos);
            } catch (Exception e) {
                log.error("[受理环节: {}] -- mchNo:[{}] ==>批次受理异常。platTrxNoList size:{},明细范围[{}]至[{}]"
                        , platBatchNo, employerNo, platTrxNos.size(), platTrxNos.get(0),platTrxNos.get(platTrxNos.size() - 1),e);
                throw e;
            }
        }
    }

    /**
     * 智享汇
     * 批次受理信息统计
     */
    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_TRADE_ASYNC, selectorExpression = MessageMsgDest.TAG_ACCEPT_BATCH_COUNT,consumeThreadMax = 3, consumerGroup = "acceptBatchCountConsume")
    public class AcceptBatchCountMessageListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String platBatchNo) {
            if (StringUtil.isEmpty(platBatchNo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("platBatchNo不能为null");
            }
        }

        @Override
        public void consumeMessage(String platBatchNo) {
            zxhAcceptBiz.updateAcceptBatchCount(platBatchNo);
        }
    }
}
