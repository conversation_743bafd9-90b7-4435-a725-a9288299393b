package com.zhixianghui.service.trade.biz;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.CmbIncomeRecordParamDTO;
import com.zhixianghui.facade.trade.dto.IdParamDTO;
import com.zhixianghui.facade.trade.dto.RechargeAccountParamDTO;
import com.zhixianghui.facade.trade.entity.CmbIncomeRecord;
import com.zhixianghui.facade.trade.enums.CmbIncomeStateEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.vo.CmbAccountChangeVo;
import com.zhixianghui.facade.trade.vo.CmbIncomeAuditVO;
import com.zhixianghui.facade.trade.vo.CmbIncomeRecordVO;
import com.zhixianghui.facade.trade.vo.MerchantInfoVO;
import com.zhixianghui.service.trade.dao.mapper.CmbIncomeRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/16 16:34
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CmbIncomeRecordBiz {

    private final CmbIncomeRecordMapper cmbIncomeRecordMapper;
    private final CmbMerchantBalanceBiz cmbMerchantBalanceBiz;
    private final RechargeRecordBiz rechargeRecordBiz;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    public IPage<CmbIncomeRecordVO> listPage(CmbIncomeRecordParamDTO param) {
        Page<CmbIncomeRecord> page = new Page<>(param.getPageCurrent(), param.getPageSize());
        return cmbIncomeRecordMapper.selectPage(page, Wrappers.<CmbIncomeRecord>lambdaQuery()
                .eq(ObjectUtil.isNotEmpty(param.getChannelTrxNo()), CmbIncomeRecord::getChannelTrxNo, param.getChannelTrxNo())
                .eq(ObjectUtil.isNotEmpty(param.getMchNo()), CmbIncomeRecord::getMchNo, param.getMchNo())
                .like(ObjectUtil.isNotEmpty(param.getMchName()), CmbIncomeRecord::getMchName, param.getMchName())
                .eq(ObjectUtil.isNotEmpty(param.getPayeeAccountNo()), CmbIncomeRecord::getPayeeAccountNo, param.getPayeeAccountNo())
                .eq(ObjectUtil.isNotEmpty(param.getMainstayNo()), CmbIncomeRecord::getMainstayNo, param.getMainstayNo())
                .like(ObjectUtil.isNotEmpty(param.getMainstayName()), CmbIncomeRecord::getMainstayName, param.getMainstayName())
                .eq(ObjectUtil.isNotEmpty(param.getState()), CmbIncomeRecord::getState, param.getState())
                .between(ObjectUtil.isNotEmpty(param.getFinishStartTime()) && ObjectUtil.isNotEmpty(param.getFinishEndTime()),
                        CmbIncomeRecord::getSuccessTime, param.getFinishStartTime(), param.getFinishEndTime())
                .orderByDesc(CmbIncomeRecord::getCreateTime)
        ).convert(e -> {
            CmbIncomeRecordVO record = new CmbIncomeRecordVO();
            BeanUtil.copyProperties(e, record);
            return record;
        });
    }

    /**
     * 招行通知，插入来账记录
     *
     * @param json
     */
    @Transactional(rollbackFor = Exception.class)
    public void cmbIncomeRecord(String json) {
        CmbAccountChangeVo cmbAccountChangeVo = JsonUtil.toBean(json, CmbAccountChangeVo.class);
        //查询是是否已经有来账通知
        List<CmbIncomeRecord> cmbIncomeRecords = cmbIncomeRecordMapper.selectList(Wrappers.<CmbIncomeRecord>lambdaQuery()
                .eq(CmbIncomeRecord::getChannelTrxNo, cmbAccountChangeVo.getRefnbr()));
        if (ObjectUtil.isNotEmpty(cmbIncomeRecords)) {
            log.error("招行账户变动来账通知，来账记录已存在，回调信息：[{}]", json);
            return;
        }
        //构建来账记录
        CmbIncomeRecord cmbIncomeRecord = buildBaseCmbIncomeRecord(cmbAccountChangeVo);
        //查询用工企业信息
        List<EmployerAccountInfo> employerAccountInfos = employerAccountInfoFacade.getByEmployerNameAndParentMchNoAndChannelNo(cmbAccountChangeVo.getRpynam(),
                cmbAccountChangeVo.getAccnbr(), ChannelNoEnum.CMB.name());

        if (ObjectUtil.isEmpty(employerAccountInfos)) {
            //如果没有获取到用工企业信息
            log.info("招行账户变动来账通知，查询不到用工企业信息，无法入账，待运营人员处理，渠道交易流水号：[{}]", cmbAccountChangeVo.getRefnbr());
            cmbIncomeRecordMapper.insert(cmbIncomeRecord);
            return;
        }

        if (employerAccountInfos.size() == 1) {
            log.info("招行账户变动来账通知，只有一个用工企业账户，自动入账，渠道交易流水号：[{}]", cmbAccountChangeVo.getRefnbr());
            // 只有一个商户信息，自动入账
            EmployerAccountInfo employerAccountInfo = employerAccountInfos.get(0);
            cmbIncomeRecord.setMchNo(employerAccountInfo.getMchName());
            cmbIncomeRecord.setMchName(employerAccountInfo.getMchName());
            cmbIncomeRecord.setMainstayName(employerAccountInfo.getMainstayName());
            cmbIncomeRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
            cmbIncomeRecord.setState(CmbIncomeStateEnum.SUCCESS.getValue());
            cmbIncomeRecord.setUpdateTime(new Date());
            cmbIncomeRecordMapper.insert(cmbIncomeRecord);

            //招行本地账户账户变动
            RechargeAccountParamDTO rechargeAccount = new RechargeAccountParamDTO();
            rechargeAccount.setMchNo(employerAccountInfo.getEmployerNo());
            rechargeAccount.setMchName(employerAccountInfo.getMchName());
            rechargeAccount.setMainstayNo(employerAccountInfo.getMainstayNo());
            rechargeAccount.setMainstayName(employerAccountInfo.getMainstayName());
            rechargeAccount.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            rechargeAccount.setPlatTrxNo(cmbIncomeRecord.getChannelTrxNo());
            rechargeAccount.setAmount(cmbIncomeRecord.getAmount());
            cmbMerchantBalanceBiz.rechargeAccount(rechargeAccount);

            //插入充值记录
            rechargeRecordBiz.cmbRechargeRecord(cmbIncomeRecord, employerAccountInfo);

        } else {
            // 查询到多个商户，则需要等待运营人员手动操作入账
            String employerNos = employerAccountInfos.stream().map(EmployerAccountInfo::getEmployerNo) // 提取name字段
                    .collect(Collectors.joining(","));
            log.info("招行账户变动来账通知，存在多个用工企业信息信息，无法自动入账，待运营人员处理，渠道交易流水号：[{}]，用工企业编码：[{}]", cmbAccountChangeVo.getRefnbr(), employerNos);
            cmbIncomeRecordMapper.insert(cmbIncomeRecord);
        }
    }

    private CmbIncomeRecord buildBaseCmbIncomeRecord(CmbAccountChangeVo cmbAccountChangeVo) {
        CmbIncomeRecord cmbIncomeRecord = new CmbIncomeRecord();
        cmbIncomeRecord.setVersion(0);
        cmbIncomeRecord.setAmount(new BigDecimal(cmbAccountChangeVo.getC_trsamt()));
        DateTime parse = DateUtil.parse(cmbAccountChangeVo.getTrsdat() + cmbAccountChangeVo.getTrstim(), DatePattern.PURE_DATETIME_PATTERN);
        cmbIncomeRecord.setSuccessTime(parse);
        cmbIncomeRecord.setChannelTrxNo(cmbAccountChangeVo.getRefnbr());
        cmbIncomeRecord.setBankName(cmbAccountChangeVo.getRpybnk());
        cmbIncomeRecord.setBankAccountName(cmbAccountChangeVo.getRpynam());
        cmbIncomeRecord.setBankAccountNumber(cmbAccountChangeVo.getRpyacc());
        cmbIncomeRecord.setPayeeAccountNo(cmbAccountChangeVo.getAccnbr());
        cmbIncomeRecord.setPayeeAccountName(cmbAccountChangeVo.getAccnam());
        cmbIncomeRecord.setState(CmbIncomeStateEnum.WAIT.getValue());
        cmbIncomeRecord.setRechargeRemark(cmbAccountChangeVo.getNaryur());
        cmbIncomeRecord.setCreateTime(new Date());
        cmbIncomeRecord.setUpdateTime(new Date());
        return cmbIncomeRecord;
    }

    /**
     * 审核来账记录
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RestResult<String> auditCmbIncomeRecord(CmbIncomeAuditVO param) {
        CmbIncomeRecord cmbIncomeRecord = cmbIncomeRecordMapper.selectById(param.getId());
        if (ObjectUtil.isEmpty(cmbIncomeRecord)) {
            log.error("审核招行来账，找不到来账记录，ID：[{}]", param.getId());
            return RestResult.error("找不到来账记录");
        }
        if (CmbIncomeStateEnum.SUCCESS.getValue() == cmbIncomeRecord.getState()) {
            log.error("审核招行来账，来账记录已审核，无需重复处理，ID：[{}]", param.getId());
            return RestResult.error("来账记录已审核，无需重复处理");
        }
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getById(param.getMchId());
        if (ObjectUtil.isEmpty(employerAccountInfo)) {
            log.error("审核招行来账，查找不到商户信息，ID：[{}]，商户ID：[{}]", param.getId(), param.getMchId());
            return RestResult.error("查找不到商户信息");
        }
        cmbIncomeRecord.setMchNo(employerAccountInfo.getMchName());
        cmbIncomeRecord.setMchName(employerAccountInfo.getMchName());
        cmbIncomeRecord.setMainstayName(employerAccountInfo.getMainstayName());
        cmbIncomeRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
        cmbIncomeRecord.setUpdator(param.getUpdator());
        cmbIncomeRecord.setUpdateTime(new Date());
        cmbIncomeRecord.setState(CmbIncomeStateEnum.SUCCESS.getValue());
        cmbIncomeRecordMapper.updateById(cmbIncomeRecord);

        //招行本地账户账户变动
        RechargeAccountParamDTO rechargeAccount = new RechargeAccountParamDTO();
        rechargeAccount.setMchNo(employerAccountInfo.getEmployerNo());
        rechargeAccount.setMchName(employerAccountInfo.getMchName());
        rechargeAccount.setMainstayNo(employerAccountInfo.getMainstayNo());
        rechargeAccount.setMainstayName(employerAccountInfo.getMainstayName());
        rechargeAccount.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        rechargeAccount.setPlatTrxNo(cmbIncomeRecord.getChannelTrxNo());
        rechargeAccount.setAmount(cmbIncomeRecord.getAmount());
        cmbMerchantBalanceBiz.rechargeAccount(rechargeAccount);

        //插入充值记录
        rechargeRecordBiz.cmbRechargeRecord(cmbIncomeRecord, employerAccountInfo);
        return RestResult.success("审核成功");
    }

    public List<MerchantInfoVO> listMerchantInfo(IdParamDTO param) {
        CmbIncomeRecord cmbIncomeRecord = cmbIncomeRecordMapper.selectById(param.getId());
        if (ObjectUtil.isEmpty(cmbIncomeRecord)) {
            log.error("审核招行来账，找不到来账记录，ID：[{}]", param.getId());
            return new ArrayList<>();
        }
        //查询用工企业信息
        List<EmployerAccountInfo> employerAccountInfos = employerAccountInfoFacade.getByEmployerNameAndParentMchNoAndChannelNo(cmbIncomeRecord.getBankAccountName(),
                cmbIncomeRecord.getPayeeAccountNo(), ChannelNoEnum.CMB.name());
        if (ObjectUtil.isEmpty(employerAccountInfos)) {
            return new ArrayList<>();
        }
        return employerAccountInfos.stream().map(e -> {
            MerchantInfoVO vo = new MerchantInfoVO();
            vo.setId(e.getId());
            vo.setMchNo(e.getEmployerNo());
            vo.setMchName(e.getMchName());
            return vo;
        }).collect(Collectors.toList());
    }

    public void exportCmbIncomeRecord(CmbIncomeRecordParamDTO param, String loginName) {
        Map<String, Object> paramMap = BeanUtil.toMap(param);
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(loginName);
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.CMB_INCOME_RECORD_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.CMB_INCOME_RECORD_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.CMB_INCOME_RECORD_EXPORT.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }
}
