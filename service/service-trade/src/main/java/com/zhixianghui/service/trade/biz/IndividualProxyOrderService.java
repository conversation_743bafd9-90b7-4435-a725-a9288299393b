package com.zhixianghui.service.trade.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.service.utils.MybatisUtil;
import com.zhixianghui.facade.trade.entity.IndividualProxyOrder;
import com.zhixianghui.service.trade.dao.mapper.IndividualProxyOrderMapper;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class IndividualProxyOrderService extends ServiceImpl<IndividualProxyOrderMapper, IndividualProxyOrder> {

    public Page<IndividualProxyOrder> listPage(Page page,Map<String, Object> paramMap) {
        QueryWrapper<IndividualProxyOrder> queryWrapper = new QueryWrapper<>();
        MybatisUtil.buildWhere(queryWrapper,IndividualProxyOrder.class,paramMap);
        IndividualProxyOrder.extendWhere(queryWrapper,paramMap);
        return this.page(page, queryWrapper);
    }

}
