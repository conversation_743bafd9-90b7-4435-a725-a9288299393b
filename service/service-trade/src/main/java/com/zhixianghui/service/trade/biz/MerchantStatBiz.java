package com.zhixianghui.service.trade.biz;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.trade.vo.MerchantStatVo;
import com.zhixianghui.service.trade.dao.MerchantStatDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zhixianghui.facade.trade.entity.MerchantStat;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 商户月统计表Biz
*
* <AUTHOR>
* @version 创建时间： 2021-08-02
*/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantStatBiz {

    private final MerchantStatDao merchantstatDao;

    public List<MerchantStat> merchantStat(Map<String, Object> param) {
        return merchantstatDao.listBy("merchantStat", param);
    }

    public PageResult< List<MerchantStat>> merchantStat(Map<String, Object> param, PageParam pageParam) {
        return merchantstatDao.listPage("merchantStat", param, pageParam);
    }

    public MerchantStatVo count(Map<String, Object> param) {
        return merchantstatDao.getOne("merchantStatCount", param);
    }

    public void insert(List<MerchantStat> result) {
        merchantstatDao.insert(result);
    }

    public void insert(MerchantStat merchantStat) {
        merchantstatDao.insert(merchantStat);
    }

    public List<MerchantStatVo> groupBySaleId(Map<String, Object> saleIds) {
        return merchantstatDao.listBy("groupBySaleId", saleIds);
    }
}