package com.zhixianghui.service.trade.listener.tasks;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.trade.vo.NoTradeEmployerExcelVo;
import com.zhixianghui.service.trade.dao.OrderItemDao;
import com.zhixianghui.service.trade.listener.TaskRocketMQListener;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 半年没交易商户做出提示
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_EMPLOYER_TRADE_CHECK_TASK, consumeThreadMax = 1, consumerGroup = "EmployerTradeCheckConsumer")
public class EmployerTradeCheckTaskListener extends TaskRocketMQListener<JSONObject> {

    @Autowired
    private OrderItemDao orderItemDao;

    @Autowired
    private RedisClient redisClient;

    @Reference
    private RobotFacade robotFacade;

    @Autowired
    private FastdfsClient fastdfsClient;

    @Override
    public void runTask(JSONObject jsonParam) {

        Date startTime = DateUtil.beginOfDay(DateUtil.offsetMonth(new Date(), -1).toJdkDate());
        Date activeTimeEnd = DateUtil.beginOfDay(DateUtil.offsetMonth(new Date(), -1).toJdkDate());
        log.info("[半年没交易商户做出提示]-开始时间:{}", DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss"));

        // 查询出近6个月产生交易的商户，如果在之前进入过缓存，则删除
        List<Map<String, Object>> mapList = orderItemDao.listHasTradeEmp(MapUtil.of("startTime", startTime));
        mapList.forEach(it->{
            String mchInfo = redisClient.get("EmployerTradeCheck:" + it.get("mchNo"));
            if (mchInfo != null) {
                redisClient.del("EmployerTradeCheck:" + it.get("mchNo"));
            }
        });

        Map<String, Object> param = MapUtil.builder(new HashMap<String,Object>())
                .put("startTime", startTime)
                .put("activeTimeEnd", activeTimeEnd)
                .build();
        List<Map<String, Object>> checkResult = orderItemDao.employerTradeCheck(param);

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            List<NoTradeEmployerExcelVo> toNotifyData = checkResult.stream()
                .filter(it->{

                    String mchInfo = redisClient.get("EmployerTradeCheck:" + it.get("mchNo"));
                    if (mchInfo == null) {
                        redisClient.set("EmployerTradeCheck:" + it.get("mchNo"), DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
                        return true;
                    }else {
                        return false;
                    }
                }).map(it-> {
                    NoTradeEmployerExcelVo employerExcelVo = BeanUtil.toBean(it, NoTradeEmployerExcelVo.class);
                    Map<String, Date> orderTime = orderItemDao.selectLastOrderTime(MapUtil.of("mchNo", employerExcelVo.getMchNo()));
                    if (orderTime == null || orderTime.get("lastTime") == null) {
                        employerExcelVo.setLastOrderTime("1970-01-01 00:00:00");
                    }else {
                        employerExcelVo.setLastOrderTime(DateUtil.format(orderTime.get("lastTime"),"yyyy-MM-dd HH:mm:ss"));
                    }
                    return employerExcelVo;
                })
                .sorted((a,b)->{
                    DateTime dateA = DateUtil.parse(a.getLastOrderTime(), "yyyy-MM-dd HH:mm:ss");
                    DateTime dateB = DateUtil.parse(b.getLastOrderTime(), "yyyy-MM-dd HH:mm:ss");
                    if (dateA.isAfter(dateB)) {
                        return -1;
                    }
                    else if (dateA.equals(dateB)){
                        return 0;
                    }else {
                        return 1;
                    }
                })
                .collect(Collectors.toList());

        if (toNotifyData==null ||toNotifyData.isEmpty()) {
            return;
        }

        for (NoTradeEmployerExcelVo notifyDatum : toNotifyData) {
            String activeTime = notifyDatum.getActiveTime();
            String lastOrderTime = notifyDatum.getLastOrderTime();
            if (StringUtils.equals(lastOrderTime, "1970-01-01 00:00:00")) {
                notifyDatum.setSleepDate(-1L);
                notifyDatum.setLastOrderTime("从未交易");
            }else {
                DateTime parseDate = DateUtil.parseDate(lastOrderTime);
                DateTime nowDate = DateUtil.date(new Date());
                long betweenDay = DateUtil.betweenDay(parseDate, nowDate, true);
                notifyDatum.setSleepDate(betweenDay);
            }

        }

        ExcelWriter excelWriter = null;
        File tmpFile = null;
        try {
            tmpFile = new File(System.getProperty("java.io.tmpdir") + System.getProperty("file.separator") + IdUtil.fastUUID() +".xlsx");
            excelWriter = EasyExcel.write(tmpFile).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(1, "近一月未产生交易商户").head(NoTradeEmployerExcelVo.class).build();
            excelWriter.write(toNotifyData, writeSheet);
        }finally {
            if (excelWriter != null){
                excelWriter.finish();
            }
        }

        String fileId = fastdfsClient.uploadFile(FileUtil.readBytes(tmpFile), "近一月未产生交易商户.xlsx");
        log.info("【近一月未产生交易商户】文件已经上传，fileId={}", fileId);
        robotFacade.sendFileMsg(RobotTypeEnum.WX_SALE_PROD_ROBOT, "近一月未产生交易商户.xlsx", fileId);
    }
}
