package com.zhixianghui.service.trade.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhixianghui.facade.trade.bo.OrderItemCountBo;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface OfflineOrderItemMapper extends BaseMapper<OfflineOrderItem> {

    OrderItemCountBo getCountBoByPlatBatchNo(Map<String, Object> paramMap);

    Long countOrder(Map<String, Object> paramMap);

    OrderItemSumBo sumOrderItem(Map<String, Object> paramMap);

    BigDecimal sumOrderItemWaitInvoiceAmount(Map<String, Object> paramMap);

    List<String> listPlatTrxNoByBatchNo(Map<String,Object> paramMap);

    void cancelOrderItem(Map<String, Object> paramMap);

    int updateByPlatBatchNo(OrderDeleteDTO orderDeleteDTO);

    List<OfflineOrderItem> listBy(Map<String, Object> params);
}
