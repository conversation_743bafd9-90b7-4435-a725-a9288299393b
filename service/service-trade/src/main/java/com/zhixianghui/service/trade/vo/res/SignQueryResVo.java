package com.zhixianghui.service.trade.vo.res;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.zhixianghui.api.base.dto.ApiBizBaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 签约查询结果
 * @date 2021/1/15 16:49
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SignQueryResVo extends ApiBizBaseDto {
    /**
     * 用户唯一标识
     */
    private String userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 电话号码
     */
    private String phoneNo;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 签约状态
     */
    private Integer signStatus;

    /**
     * 全局ID
     */
    private String openUserId;

    private Integer authStatus;
}
