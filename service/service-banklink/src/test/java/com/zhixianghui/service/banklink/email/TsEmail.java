package com.zhixianghui.service.banklink.email;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.service.banklink.ServiceBanklinkApp;
import com.zhixianghui.service.banklink.core.biz.RobotBiz.RobotBiz;
import com.zhixianghui.service.banklink.core.biz.message.email.EmailBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/12/30
 **/
@SpringBootTest(classes = ServiceBanklinkApp.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class TsEmail {
    @Autowired
    private EmailBiz emailBiz;
    /**
     * 发送邮件
     */
    @Test
    public void tsSend(){
        EmailParamDto param = new EmailParamDto();
        param.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        param.setTo("<EMAIL>");
        param.setSubject("【智享汇】你的开票申请成功");
        param.setTpl("merchant.ftl");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("trxNo", "***********");
        paramMap.put("createTime", "2020-12-31 09:55:23");
        paramMap.put("mainstayMchName", "宝岭");
        paramMap.put("invoiceAmount", "125元");
        paramMap.put("invoiceTypeName", InvoiceTypeEnum.MAJOR.getDesc());
        paramMap.put("invoiceCategoryName", "技术服务");
        paramMap.put("addrInfo", "广东省广州市广州塔尖避雷针处 张*虎 188****999");
        param.setTplParam(paramMap);
        param.setHtmlFormat(true);
        emailBiz.sendAsync(param);
    }

//    public static void main(String[] args) {
//        RobotBiz robotBiz = new RobotBiz();
//        MarkDownMsg markDownMsg = new MarkDownMsg();
//        markDownMsg.setRobotType(RobotTypeEnum.TASK_NOTIFY_ROBOT.getType());
//        markDownMsg.setUnikey(IdWorker.get32UUID());
//        markDownMsg.setContent("得加钱");
//        robotBiz.sendRequest(markDownMsg, "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=208897d0-a550-4a65-a003-6990039f3d82");
//        while (true) {
//
//        }
//    }
}
