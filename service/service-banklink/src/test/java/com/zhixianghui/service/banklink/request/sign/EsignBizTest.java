package com.zhixianghui.service.banklink.request.sign;

import com.google.common.collect.Maps;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.SignCreatePersonReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.SignCreatePersonResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.UpdatePersonInfoReqVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.facade.banklink.vo.sign.signAuth.SignAuthReqVo;
import com.zhixianghui.service.banklink.ServiceBanklinkApp;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description
 * @date 2021-01-07 18:06
 **/

@SpringBootTest(classes = ServiceBanklinkApp.class,webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class EsignBizTest {
    @Autowired
    private EsignBiz esignBiz;

    @Test
    public void testCreatePersonByThirdPartyUserId(){
        SignCreatePersonReqVo signCreatePersonReqVo =
                new SignCreatePersonReqVo("123123","谢谢","CRED_PSN_CH_IDCARD","110101199003076692");
        EsignResVo<SignCreatePersonResDataVo> res = esignBiz.createPersonByThirdPartyUserId(signCreatePersonReqVo);
        System.out.println(res);
    }

    @Test
    public void testCreateOrganization(){
        SignCreatePersonReqVo signCreatePersonReqVo =
                new SignCreatePersonReqVo("123123","谢谢","CRED_PSN_CH_IDCARD","110101199003076692");
        EsignResVo<SignCreatePersonResDataVo> res = esignBiz.createPersonByThirdPartyUserId(signCreatePersonReqVo);
        System.out.println(res);
        String accountId = res.getData().getAccountId();
        SignCreateOrganizationReqVo signCreateOrganizationReqVo = new SignCreateOrganizationReqVo(
                "*********",accountId,"xxxx汇聚公司","CRED_ORG_USCC","110101199003076691");
        EsignResVo<SignCreateOrganizationResDataVo> res2 = esignBiz.createOrganization(signCreateOrganizationReqVo);
        System.out.println(res2);
    }

    @Test
    public void testGetSignTemplate(){
        EsignResVo<SignTemplateResDataVo> res = esignBiz.getSignTemplate(new SignTemplateReqVo("47591f7b2e984557b210e3283b313338"));
        System.out.println(JsonUtil.toString(res));
    }

    @Test
    public void testSignAuth(){
        SignCreatePersonReqVo signCreatePersonReqVo =
                new SignCreatePersonReqVo("********","谢谢","CRED_PSN_CH_IDCARD","110101199003076692");
        EsignResVo<SignCreatePersonResDataVo> res = esignBiz.createPersonByThirdPartyUserId(signCreatePersonReqVo);
        System.out.println(res);
        String accountId = res.getData().getAccountId();
        SignCreateOrganizationReqVo signCreateOrganizationReqVo = new SignCreateOrganizationReqVo(
                "***********",accountId,"xxxx汇聚公司","CRED_ORG_USCC","110101199003076691");
        EsignResVo<SignCreateOrganizationResDataVo> res2 = esignBiz.createOrganization(signCreateOrganizationReqVo);
        System.out.println(res2);

        SignAuthReqVo signAuthReqVo = new SignAuthReqVo(res2.getData().getOrgId());
        EsignResVo<Boolean> res3 = esignBiz.signAuth(signAuthReqVo);
        System.out.println(res3);
    }

    @Test
    public void testCreateFileByTemplate(){
        HashMap<String,String> simpleFormFields = Maps.newHashMap();
        simpleFormFields.put("584e5d278bb643d795aecc73aaa282d8","河南宝岭信息科技有限公司");
        simpleFormFields.put("45c018e981e94188af8dbb9d46b7a095", "广州市古格酒店有限公司");
        simpleFormFields.put("af186e7d34e74d4490aa9a2b068bd162", "xxx服务岗位");
        simpleFormFields.put("dc4d1b145fb249ef8103707ba3cc8c0d", "河南宝岭信息科技有限公司");
        simpleFormFields.put("e8dc3071021947f296db797a9299cdca", "河南宝岭信息科技有限公司");
        simpleFormFields.put("3cba43414c6b474e878461bc5aa843e4", "河南宝岭信息科技有限公司");
        simpleFormFields.put("b675d2ddc60f47daa8101ff3163d7988", "张三");
        simpleFormFields.put("f27c1c09e7cb4abab99532d09ff19074", "632323190605260906");
        simpleFormFields.put("d68fcd2a9fcc40009d5d40191819c69d", "2021/01/07");
        CreateFileByTemplateReqVo createFileByTemplateReqVo = new CreateFileByTemplateReqVo("模板111","47591f7b2e984557b210e3283b313338",simpleFormFields);
        EsignResVo<CreateFileByTemplateResDataVo> res = esignBiz.createFileByTemplate(createFileByTemplateReqVo);
        System.out.println(res);
    }

    @Test
    public void testCreateFlowOneStep(){
        SignCreatePersonReqVo signCreatePersonReqVo =
                new SignCreatePersonReqVo("************9g","谢谢","CRED_PSN_CH_IDCARD","110101199003076692");
        signCreatePersonReqVo.setMobile("***********");
        EsignResVo<SignCreatePersonResDataVo> res1 = esignBiz.createPersonByThirdPartyUserId(signCreatePersonReqVo);
        String accountId = res1.getData().getAccountId();


        SignCreatePersonReqVo signCreatePersonReqVo2 =
                new SignCreatePersonReqVo("************","谢谢","CRED_PSN_CH_IDCARD","110101199003076692");
        EsignResVo<SignCreatePersonResDataVo> res2 = esignBiz.createPersonByThirdPartyUserId(signCreatePersonReqVo2);
        System.out.println(res2);
        String accountId2 = res2.getData().getAccountId();
        SignCreateOrganizationReqVo signCreateOrganizationReqVo = new SignCreateOrganizationReqVo(
                "***********1",accountId2,"xxxx汇聚公司","CRED_ORG_USCC","110101199003076691");
        EsignResVo<SignCreateOrganizationResDataVo> res3 = esignBiz.createOrganization(signCreateOrganizationReqVo);
        String orgId = res3.getData().getOrgId();
        System.out.println(res3);
        //允许静默
        SignAuthReqVo signAuthReqVo = new SignAuthReqVo(accountId2);
        EsignResVo<Boolean> signAuthRes = esignBiz.signAuth(signAuthReqVo);
        System.out.println(signAuthRes);
        //创建文件
        HashMap<String,String> simpleFormFields = Maps.newHashMap();
        simpleFormFields.put("584e5d278bb643d795aecc73aaa282d8","河南宝岭信息科技有限公司");
        simpleFormFields.put("45c018e981e94188af8dbb9d46b7a095", "广州市古格酒店有限公司");
        simpleFormFields.put("af186e7d34e74d4490aa9a2b068bd162", "xxx服务岗位");
        simpleFormFields.put("dc4d1b145fb249ef8103707ba3cc8c0d", "河南宝岭信息科技有限公司");
        simpleFormFields.put("e8dc3071021947f296db797a9299cdca", "河南宝岭信息科技有限公司");
        simpleFormFields.put("3cba43414c6b474e878461bc5aa843e4", "河南宝岭信息科技有限公司");
        simpleFormFields.put("b675d2ddc60f47daa8101ff3163d7988", "张三");
        simpleFormFields.put("f27c1c09e7cb4abab99532d09ff19074", "632323190605260906");
        simpleFormFields.put("d68fcd2a9fcc40009d5d40191819c69d", "2021/01/07");
        CreateFileByTemplateReqVo createFileByTemplateReqVo = new CreateFileByTemplateReqVo("合同合同1","47591f7b2e984557b210e3283b313338",simpleFormFields);
        EsignResVo<CreateFileByTemplateResDataVo> res4 = esignBiz.createFileByTemplate(createFileByTemplateReqVo);

        String fileId = res4.getData().getFileId();
        //1、配置文档
        ArrayList<Doc> docs = Lists.newArrayList();
        docs.add(new Doc().setFileId(fileId));
        //2、配置流程
        FlowInfo flowInfo =  new FlowInfo()
                .setBusinessScene("b2c合同签署测试")
                //启用自动归档
                .setAutoArchive(true)
                .setAutoInitiate(true)
                .setFlowConfigInfo(new FlowConfigInfo()
                                .setNoticeDeveloperUrl("https://notify-dev.hjzxh.com/esignReceive/flowEnd")
//                            .setRedirectUrl("http://www.baidu.com")
                );

        //查询模版 签署区位置
        EsignResVo<SignTemplateResDataVo> res5 = esignBiz.getSignTemplate(new SignTemplateReqVo("47591f7b2e984557b210e3283b313338"));

        List<StructComponent> signFile = res5.getData().getStructComponents().stream().filter(
                component-> "甲方签署区".equals(component.getContext().getLabel())
        ).collect(Collectors.toList());
        StructPos jiafangPos = signFile.get(0).getContext().getPos();

        List<StructComponent> signFile2 = res5.getData().getStructComponents().stream().filter(
                component-> "乙方签署区".equals(component.getContext().getLabel())
        ).collect(Collectors.toList());
        StructPos yifangPos = signFile2.get(0).getContext().getPos();

        //3、配置签署区域
        ArrayList<Signfield> psnSignfields = Lists.newArrayList();
        psnSignfields.add(new Signfield()
                .setFileId(fileId)
                .setPosBean(new PosBean()
                        .setPosPage(String.valueOf(yifangPos.getPage()))
                        .setPosX(yifangPos.getX())
                        .setPosY(yifangPos.getY()))
                .setSealType("1"));//构造个人signfields参数对象,用于后续入参使用,支持链式入参

        ArrayList<Signfield> orgSignfields = Lists.newArrayList();
        orgSignfields.add(new Signfield()
                .setFileId(fileId)
                .setAutoExecute(true)
                .setActorIndentityType("2")//机构签署必传
                .setPosBean(new PosBean()
                        .setPosPage(String.valueOf(jiafangPos.getPage()))
                        .setPosX(jiafangPos.getX())
                        .setPosY(jiafangPos.getY())));//构造企业signfields参数对象,用于后续入参使用,支持链式入参

        //配置签署人并指定相应区域
        ArrayList<Signer> signers = Lists.newArrayList();
        signers.add(new Signer()
                .setSignerAccount(
                        new SignerAccount()
                                .setSignerAccountId(accountId)
                ).setSignfields(psnSignfields));//传入个人signer信息

        signers.add(new Signer()
                .setSignerAccount(
                        new SignerAccount()
                                .setSignerAccountId(accountId2)//签署经办人
                                .setAuthorizedAccountId(orgId)//企业签署需要传入该参数
                )
                .setSignfields(orgSignfields));//传入企业signer信息
        CreateFlowOneStepReqVo createFlowOneStepReqVo = new CreateFlowOneStepReqVo(docs,flowInfo,signers);
        EsignResVo<CreateFlowOneStepResDataVo> res6 = esignBiz.createFlowOneStep(createFlowOneStepReqVo);
        System.out.println(res6);
        String flowId = res6.getData().getFlowId();
        //查询流程签署地址
        ExecuteUrlReqVo executeUrlReqVo = new ExecuteUrlReqVo(flowId,accountId);
        EsignResVo<ExecuteUrlResDataVo> res7 = esignBiz.getExecuteUrl(executeUrlReqVo);
        System.out.println(res7);
        //查询流程签署地址
        ExecuteUrlReqVo executeUrlReqVo1 = new ExecuteUrlReqVo(flowId,accountId2);
        EsignResVo<ExecuteUrlResDataVo> res8 = esignBiz.getExecuteUrl(executeUrlReqVo1);
        System.out.println(res8);
        //查询流程详情
        SignFlowReqVo signFlowReqVo = new SignFlowReqVo(flowId);
        EsignResVo<SignFlowResDataVo> res9 = esignBiz.getSignFlow(signFlowReqVo);
        System.out.println(res9);
    }

    @Test
    public void getExecuteUrl(){
        //查询流程签署地址
        ExecuteUrlReqVo executeUrlReqVo1 = new ExecuteUrlReqVo("4533bf21e6f34ebf9b96c344e3ebd971","3180ada0673e4d8fab85c7445392cbd9");
        EsignResVo<ExecuteUrlResDataVo> res8 = esignBiz.getExecuteUrl(executeUrlReqVo1);
        System.out.println(res8);
    }

    @Test
    public void getDocumentDownloadUrl(){
        DocumentDownloadReqVo documentDownloadReqVo = new DocumentDownloadReqVo("73d61f5d291a4b4c8158c466cd9f1b04");
//        EsignResVo<DocumentDownloadResDataVo> res = esignBiz.getDocumentDownloadUrl(documentDownloadReqVo);
//        System.out.println(res);
    }

    @Test
    public void updateInfo(){
        String url = "https://esignoss.esign.cn/1111563786/deb1a3ec-74d8-4a08-acb6-5451931b79d0/%E3%80%90UI%E6%AC%A7%E8%90%A8%E6%9C%89%E8%A6%85OA%E4%B8%8E%E3%80%91%26%E3%80%90%E8%B0%A2%E9%94%A6%E4%B8%96%E3%80%91%E5%85%B1%E4%BA%AB%E6%9C%8D%E5%8A%A1%E5%8D%8F%E8%AE%AE.pdf?Expires=1611143608&OSSAccessKeyId=LTAI4GJDCzRmsaAhkYbZFxUS&Signature=vzWhCVlH0zvE2VgR9UBj7kb2UE4%3D";
//        esignBiz.downLoadFile("aaa.pdf",url);
        WebClient webClient = WebClient.create(url);
        Mono<ClientResponse> mono = webClient
                .get()
//                .header("Host","esignoss.esign.cn")
//                .header("User-Agent","PostmanRuntime/7.26.8")
//                .header("Accept","*/*")
//                .header("Accept-Encoding","gzip, deflate, br")
//                .header("Connection","keep-alive")
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .exchange();

        ClientResponse response = mono.block();
        ResponseEntity<String> res = response.toEntity(String.class).block();
        System.out.println(res.getStatusCode());
        System.out.println("===============");
        System.out.println(res.getBody());
        System.out.println("===============");
        System.out.println(res.getHeaders());
        System.out.println("===============");
        System.out.println(res.getStatusCodeValue());
//        byte[] scenarioBytesFile = res.getBody();
//        Resource resource = response.bodyToMono(Resource.class).block();
//        InputStream fileStream = resource.getInputStream();
//        File out = new File("E:\\" + fileName);
//        FileUtils.copyInputStreamToFile(fileStream,out);
//        byte[] byt = new byte[fileStream.available()];
//        fileStream.read(byt);
//        String url = fastdfsClient.uploadFile(scenarioBytesFile,fileName);
    }

    @Test
    public void testS(){
        UpdatePersonInfoReqVo updatePersonInfoReqVo = new UpdatePersonInfoReqVo("7f80e9c38d7a4569838195b6b55cf7e0");
        updatePersonInfoReqVo.setMobile("15918561132");
        updatePersonInfoReqVo.setName("来了");
//        esignBiz.updatePersonInfo(updatePersonInfoReqVo);
    }
}