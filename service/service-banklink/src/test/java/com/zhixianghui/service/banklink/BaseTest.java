package com.zhixianghui.service.banklink;

import cn.hutool.json.JSONUtil;
import com.alibaba.druid.filter.config.ConfigTools;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.LogEncryUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.RSAUtil;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.junit.Test;

import java.util.Map;

public class BaseTest {

    @Test
    public void test() {
        String decrypt = LogEncryUtil.decrypt("bW2apV0aZiqT4x5xodaTto583t6K+OE765mtaV89xPvao65B2hmPwyXvMO3UHGBMUNRqupCsP9pwIhT9/U6KFnaXZumTHeDd4ugzbp9mrxo7glbrTEMIMnWf/rVE/quPj63aWWrWlu+JigqU5sBxKnSptXS4ehE8k5ovYl9hd7LCjJvsbxl4xEPcFkBctd8mm59sD5mXNm17501mzZAyjHIeQvEZq0DhAIKOR+CvCykKqppfqwM9XG+H5o3ZqYbfik1xIn68GYNBJPnlGu7r5IYi4znz69Oh2ExxaIgCLpdCvaIsbfw9n5zX53wnwHDLmUjJDzufFFngC3LIPA3jTnnK81sSV6pqfHDFQBNgrgsr3XCGn9R3uNkCbnHGpRO/Sdazjq98E0cysiJ1CfhdTVQ6XGQBJxPj5i2iByATXrnEq8U8yJlLpzR9TuWTEtH+Ux/2XfGskVbpQAe4Xw4hmF3KcGJpbIYci1TO8IfqLx/RyFRz5XsQbhZYdMIazQpWy03T3GYCuuxwTcLpOlvm1A==");
        System.out.println(decrypt);

        System.out.println(MD5Util.getMixMd5Str("810000195609220032"));
    }

    @Test
    public void testDecrypt() {
        String data = "RaU6c4dz3JS5aeXLsLZF+g==";
        String s = AESUtil.decryptECB(data, "hjzx12345688888@");
        System.out.println(s);
    }

    @Test
    public void notifyTest(){
        String url = "http://tscebuy-mg.aiadcloud.cn/tsce_mg/signContract_zxhSignContractCallBack.action";
        String content = "{\n" +
                "    \"data\": \"{\\\"biz_err_code\\\":\\\"HS000000\\\",\\\"biz_err_msg\\\":\\\"成功\\\",\\\"id_card_no\\\":\\\"9ciO7WsJPg6dN0M2ag4tAp5kRKhW1RMuQBDjhlk3sHw=\\\",\\\"mainstay_no\\\":\\\"S000004\\\",\\\"name\\\":\\\"h3miSgc+3GWY0j0GXCxOEw==\\\",\\\"phone_no\\\":\\\"vyIon+ZrpSL3h6ATCHX01A==\\\",\\\"sign_status\\\":100,\\\"user_id\\\":\\\"SI00020634\\\"}\",\n" +
                "    \"mch_no\": \"*********\",\n" +
                "    \"rand_str\": \"pvDjQJVPa0IgeI43vyxz6ANAcpQdrP9H\",\n" +
                "    \"resp_code\": \"success\",\n" +
                "    \"sec_key\": \"QmR/S1HaeEdibQkQ2yI9BmZhsZdJCZHUeqqdno8+97MTQh687du+riqj/D/RN1kqBKGnRFbDtW6j09hWYEJYknZKgadJnMVo9CiyKOEbU6DUFmv0oX1I7hFBarfRKBn8uVNH6U479c4nVebKUFROzpPNBH+bg5yU6TW+r07LkNA=\",\n" +
                "    \"sign\": \"BJER4rTzGGHiwbeEIa2UqcDEjKrO2S6kcTr5ZK21T70bzdeBZngIL93nsTa4vUdPdljdOedkgN7zbY5ysRJ3crddQvn8g/rd9v38GZ89dhrf3FxiW0gP4r89/tkXQp6Xk29DA8yU3Hn06tvN7HkIxkPDCYCCRZa5uTndBQhgMlY=\",\n" +
                "    \"sign_type\": \"1\"\n" +
                "}";
//        WebClient webClient = WebClient.create(url);
//        Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
//                .body(BodyInserters.fromObject(content)).retrieve().bodyToMono(JSONObject.class);
//        JSONObject respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
//
//        System.out.println(respObject);

        OkHttpClient okHttpClient = new OkHttpClient.Builder().build();
        okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(okhttp3.MediaType.parse("application/json; charset=utf-8"), content);
        Request request = new Request.Builder().post(requestBody).url(url).build();
        JSONObject respObject;
        try {
            Response execute = okHttpClient.newCall(request).execute();

            if (execute.body() == null) {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("商户返回确认信息为null");
            }

            String respStr = execute.body().string();
            if (JSONUtil.isJsonObj(respStr)) {
                 respObject = JSONObject.parseObject(respStr);
            }else {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("商户返回确认信息为格式不正确");
            }
        } catch (Exception e1) {
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("回调商户接口异常");
        }
        System.out.println(respObject.toJSONString());
    }

    @Test
    public void testString() {

        try {
           String data = ConfigTools.decrypt("MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJX33lSCPFoYWegm9T58XCi0vOFNiNcDwXXTht9PghuMrTIn4yW4GT2BZuQUIfjhlH6kneUfY4Z+DOgz0oa23WUCAwEAAQ==", "Fvn3WaPTcX4ePtRoLJlZlU2sgdCtrcK6qpgldc48E1y49c3tuLHGLF/wla8DhiVipv8eayXP7GovmvYUGJDk0Q==");
            System.out.println(data);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void genRsaKeys() {
        final Map<String, String> stringStringMap = RSAUtil.genKeyPair();
        System.out.println(JSONUtil.toJsonPrettyStr(stringStringMap));
    }

    @Test
    public void testRsaEncrypt() {
//        final String data = RSAUtil.encryptByPublicKey("普海涛", RSAUtil.genKeyPair().get("publicKey"));
        final String s = AESUtil.encryptECB(" ", "hjzx66688866666@");
        if ( s == null) {
            System.out.println("加密空字符串结果为null");
        }else {
            System.out.println(s.length());
        }
    }
}
