package com.zhixianghui.service.banklink.pay;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.pay.QueryPayOrderReqVo;
import com.zhixianghui.facade.banklink.vo.report.ReportReceiveReqVo;
import com.zhixianghui.service.banklink.ServiceBanklinkApp;
import com.zhixianghui.service.banklink.request.pay.JoinpayPayBiz;
import com.zhixianghui.service.banklink.request.report.JoinpayReportBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2020/10/27
 **/
@SpringBootTest(classes = ServiceBanklinkApp.class)
@RunWith(SpringRunner.class)
public class TsJoinpayPay {
    @Autowired
    private JoinpayPayBiz joinpayPayBiz;
    @Autowired
    private JoinpayReportBiz joinpayReportBiz;
    @Test
    public void tsPay(){
        PayReqVo reqVo = new PayReqVo();
        reqVo.setPayerChannelAccountNo("***************");
        reqVo.setBankOrderNo(RandomUtil.get16LenStr());
        reqVo.setReceiveName("黄顺思");
        reqVo.setReceiveAccountNo("6217003320016925227");
        reqVo.setReceiveAmount("1");
        reqVo.setServiceFee("0.1");
        reqVo.setRemitRemark("收钱啦");
        reqVo.setChannelNo("joinpay");
        reqVo.setChannelMchNo("***************");
        PayRespVo respVo = joinpayPayBiz.pay(reqVo);
        System.out.println("返回数据：" + JsonUtil.toString(respVo));
    }

    @Test
    public void tsQueryOrder(){
        QueryPayOrderReqVo reqVo = new QueryPayOrderReqVo();
        reqVo.setBankOrderNo("b269540bc4c24999");
        reqVo.setChannelNo("joinpay");
        reqVo.setChannelMchNo("***************");

        joinpayPayBiz.queryPayOrder(reqVo);
    }

    @Test
    public void ts(){
        String s = "{\"rand_str\":\"4F0F84CE062417BDCBBE61971DCB644A\",\"data\":\"{\\\"biz_code\\\":\\\"B100000\\\",\\\"auth_status\\\":\\\"R4004\\\",\\\"alt_mch_no\\\":\\\"***************\\\",\\\"biz_msg\\\":\\\"受理成功\\\"}\",\"aes_key\":\"\",\"resp_code\":\"A1000\",\"sign\":\"479F751E0BC71E69565675C354CE8E61\",\"mch_no\":\"***************\",\"resp_msg\":\"受理成功\",\"sign_type\":\"1\"}";
        JSONObject jsonObject = JSONObject.parseObject(s);
        ReportReceiveReqVo reqVo = new ReportReceiveReqVo();
        reqVo.setRespContent(jsonObject);
        reqVo.setChannelNo(ChannelNoEnum.JOINPAY.name());
        reqVo.setChannelMchNo("***************");
        joinpayReportBiz.verifyAndHandleResult(reqVo);
    }
}
