package com.zhixianghui.service.banklink.auth;

import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.service.banklink.ServiceBanklinkApp;
import com.zhixianghui.service.banklink.request.auth.JoinpayAuthBiz;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2020/10/27
 **/
@SpringBootTest(classes = ServiceBanklinkApp.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class TsJoinpayAuth {
    @Autowired
    private JoinpayAuthBiz joinpayAuthBiz;
    @Test
    public void tsAuth(){
        AuthReqVo reqVo = new AuthReqVo();
        reqVo.setName("牟方明");
        reqVo.setBankAccountNo("*****************");
        reqVo.setIdCardNo("******************");
//        reqVo.setPhoneNo("***********");
        reqVo.setAuthType(AuthTypeEnum.IDCARD_NAME_BANKCARD.getValue());
        reqVo.setBankOrderNo(RandomUtil.get16LenStr());
        reqVo.setSerialNo("YYYUU88777");
        AuthRespVo respVo = joinpayAuthBiz.auth(reqVo);
        System.out.println("返回数据：" + JsonUtil.toString(respVo));
    }


}
