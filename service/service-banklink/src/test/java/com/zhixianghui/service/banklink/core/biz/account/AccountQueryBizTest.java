package com.zhixianghui.service.banklink.core.biz.account;


import com.zhixianghui.facade.banklink.vo.account.SubMchNoQueryDto;
import com.zhixianghui.service.banklink.ServiceBanklinkApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-12 18:33
 **/
@SpringBootTest(classes = ServiceBanklinkApp.class)
@RunWith(SpringRunner.class)
public class AccountQueryBizTest {
    @Autowired
    private AccountQueryBiz accountQueryBiz;

    @Test
    public void getAccount(){
        SubMchNoQueryDto subMchNoQueryDto = new SubMchNoQueryDto();
        subMchNoQueryDto.setMainstayNo("S000002");
        subMchNoQueryDto.setEmployerNo("M00000099");
        subMchNoQueryDto.setChannelType(1);
        subMchNoQueryDto.setChannelNo("JOINPAY");
        subMchNoQueryDto.setChannelMchNo("***************");
        System.out.println(accountQueryBiz.getSubMchNo(subMchNoQueryDto));
    }
}