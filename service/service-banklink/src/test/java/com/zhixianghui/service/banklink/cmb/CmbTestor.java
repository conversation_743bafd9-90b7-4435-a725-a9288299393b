package com.zhixianghui.service.banklink.cmb;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.banklink.vo.pay.CmbPay2BVo;
import com.zhixianghui.facade.banklink.vo.pay.CmbPayeeVo;
import com.zhixianghui.facade.banklink.vo.pay.CmbPayerVo;
import com.zhixianghui.service.banklink.ServiceBanklinkApp;
import com.zhixianghui.service.banklink.request.cmb.CmbApiService;
import com.zhixianghui.service.banklink.request.pay.CmbPayBiz;
import com.zhixianghui.starter.comp.component.RedisRateLimit;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Date;

@SpringBootTest(classes = ServiceBanklinkApp.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class CmbTestor {

    @Autowired
    private CmbApiService cmbApiService;
//    private String accountNo = "***************";
    /**
     * 测试用户
     */
    private String accountNo = "***************";

    /**
     * 1.1 新增记账子单元 NTDMAADD
     */
    @Test
    public void createBookAccount() {
        cmbApiService.createAccountBook(accountNo, "********", "测试子单元");
    }

    /**
     * 1.2修改记账子单元NTDMAMNT
     */
    @Test
    public void updateBookAccount() {
        cmbApiService.updateAccountBook("20",
                accountNo,
                "********",
                "广州市汇聚智享电子科技有限公司");
    }

    /**
     * 1.3 查询记账子单元信息NTDMALST
     */
    @Test
    public void queryAccountBook() {
        JSONObject data = cmbApiService.queryAccountBook(accountNo, null);
        System.out.println(data.toJSONString());
    }

    /**
     * 1.4关闭记账子单元NTDMADLT
     */
    @Test
    public void closeAccountBook() {
        cmbApiService.closeAccountBook(accountNo, "********");
    }

    /**
     * 1.13查询记账子单元当天交易NTDMTLST
     */
    @Test
    public void testQueryAccountBookRecords() {
        JSONObject record = cmbApiService.queryTodayAccountBookRecord(accountNo, "********", null);
        System.out.println(record.toJSONString());
    }

    /**
     * 5.5.2.14查询记账子单元历史交易NTDMTHLS
     */
    @Test
    public void testQueryAccountBookRecordsHis() {
        JSONObject record = cmbApiService.queryHisAccountBookRecord(accountNo, "********", null, -2);
        System.out.println(record.toJSONString());
    }

    /**
     * 1.15查询记账子单元余额NTDMABAL
     */
    @Test
    public void queryAccountBookBalance() {
        cmbApiService.queryAccountBookBalance(accountNo, "********");
    }

    /**
     * 1.16查询所有记账子单元的某日余额NTDMAHBD
     */
    @Test
    public void queryAllAccountBookBalanceHis() {
        JSONObject queryAllAccountBookBalanceHis = cmbApiService.queryAllAccountBookBalanceHis("20", this.accountNo, "********", null);
        System.out.println(queryAllAccountBookBalanceHis.toJSONString());
    }

    /**
     * 5.5.2.17查询单个记账子单元的历史余额NTDMAHAD
     */
    @Test
    public void testQueryAccountBlanceHis() {
        cmbApiService.queryDailyAccountNoBalance("20", accountNo, "********", "********", "********");
    }

    /**
     * 1.20记账子单元冲账NTHCLACT
     */
    @Test
    public void testStrikeBalance() {
        cmbApiService.strikeBalance(accountNo, "********", "********", "K2777900004710C");
    }

    /**
     * 1.21记账子单元内部转账NTDMATRX
     */
    @Test
    public void testTransToAnotherBook() {
        cmbApiService.transferAccountBooks(accountNo, "********",
                "********", "100", "调账", "TEST0000112");
    }

    /**
     * 1.可经办业务模式查询DCLISMOD
     */
    @Test
    public void getAllowedBizMod() {
        cmbApiService.getAllowedBizMod(accountNo, "N02030");
    }

    /**
     * 2.查询可经办的账户列表DCLISACC
     */
    @Test
    public void testGetAllowedAccount() {
        cmbApiService.getAllowedAccount(accountNo);
    }

    /**
     * 3.账户详细信息查询NTQACINF
     */
    @Test
    public void getAccountInfo() {
        JSONObject data = cmbApiService.getAccountInfo(accountNo, 20);
        System.out.println(JSON.toJSONString(data));
    }

    /**
     * 4.查询账户历史余额NTQABINF
     */
    @Test
    public void getAccountBalanceHis() {
        cmbApiService.getAccountBalanceHis(accountNo, "20", "********", "********");
    }

    /**
     * 5查询分行号信息NTACCBBK
     */
    @Test
    public void testGetBankBranch() {
        cmbApiService.getBankBranch(accountNo);
    }


    @Test
    public void testGetAccountOrders() {
        cmbApiService.getAccountOrders(accountNo, "20", "********", "0");
    }

    /**
     * 7.账户交易信息查询trsQryByBreakPoint
     */
    @Test
    public void testTrsQryByBreakPoint() {
        cmbApiService.trsQryByBreakPoint(accountNo, "********", "********");
    }

    /**
     * 2. 企银支付单笔经办BB1PAYOP
     */
    @Test
    public void pay2bApply() {
        CmbPay2BVo cmbPay2BVo = new CmbPay2BVo();
        cmbPay2BVo.setReceiveBankName("招商银行深圳分行")
                .setTransAmount(NumberUtil.decimalFormat("#0.00", new BigDecimal("10")))
                .setTrxNo("TESTT0000001")
                .setReceiveAccountNo("***************")
                .setReceiveAccountName("银企直连测试用户8")
                .setAccountNo("***************");

        JSONObject pay2bApply = cmbApiService.pay2bApply(cmbPay2BVo);
        System.out.println(pay2bApply.toJSONString());
    }

    /**
     * 3.企银支付业务查询BB1PAYQR
     */
    @Test
    public void pay2bQuery() {
        JSONObject pay2bApply = cmbApiService.pay2bQuery(accountNo, "******************");
        System.out.println(pay2bApply.toJSONString());
    }

    /**
     * 电子回单异步查询ASYCALHD
     */
    @Test
    public void applyAccountReconFile() {
        cmbApiService.applyAccountReconFile(accountNo, "2024-06-26", "2024-06-30");
    }

    /**
     * 11.单笔回单查询DCSIGREC
     */
    @Test
    public void getAccountReconFileUrlSync() {
        final JSONObject data = cmbApiService.getAccountReconFileUrlSync(accountNo, "2024-06-19", "C0546W80000KSKZ");
        final String fildat = data.getString("fildat");
        final byte[] decode = Base64.decode(fildat);
        FileUtil.writeBytes(decode, "D:\\C0546IS000JF57Z.pdf");
    }

    /**
     * 5.4.1代发经办 BB6BTHHL - 单笔
     */
    @Test
    public void payToPerson() {
        CmbPayerVo payerVo = new CmbPayerVo();
        payerVo.setAccountNo(accountNo);
        payerVo.setAccountName("广州市汇聚智享电子科技有限公司");
        payerVo.setAmount("0.3");
        payerVo.setRemitNo(RandomUtil.randomNumbers(18));
        payerVo.setRemark("测试资金下发");
        payerVo.setAccountBookNo("********");
        payerVo.setPayBatchNo("********01");

        CmbPayeeVo cmbPayeeVo = new CmbPayeeVo();
        cmbPayeeVo.setAccountName("冯建杨");
        cmbPayeeVo.setAccountNo("****************");
        cmbPayeeVo.setRemitDetailNo(payerVo.getRemitNo() + "P");
        cmbPayeeVo.setAmount("0.3");
        cmbPayeeVo.setRemark("收款方");
        cmbPayeeVo.setRemitSeqNo("********");
        cmbApiService.payApply(payerVo, cmbPayeeVo);
    }

    /**
     * 5.4.1代发经办 BB6BTHHL - 超网批量代发
     */
    @Test
    public void payApplyBatch() {
        Dict body = Dict.create();
        body.set("bb6busmod",
                ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("buscod", "N03020") //业务类型
                                        .set("busmod", "00003") //业务模式
                        )
                )
        );
        body.set("bb6cdcbhx1", ListUtil.of(
                MapUtil.sort(
                        Dict.create()  //付款方信息
                                .set("begtag", "Y")//批次开始标志
                                .set("endtag", "y")//批次结束标志
                                .set("accnbr", accountNo)//账号
                                .set("accnam", "企业网银新********")//户名
                                .set("ttlamt", "0.5")//总金额
                                .set("ttlcnt", 2)//总笔数
                                .set("ttlnum", 1)//总次数
                                .set("curamt", "0.5")//本次金额
                                .set("curcnt", 2)//本次笔数
                                .set("ccynbr", "10")//币种
                                .set("trstyp", "BYBK")//交易类型
                                .set("nusage", "下发")//用途
                                .set("eptdat", cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN))//期望日期
                                .set("yurref", "ZHITEST000011")//业务参考号
                                .set("dmanbr", "********")//记账子单元
                                .set("chlflg", "Y")//结算通道
                )
        ));
        body.set("bb6cdcdlx1", ListUtil.of(
                MapUtil.sort(
                        Dict.create() //收款方信息
                                .set("trxseq", "00000001") //交易序号
                                .set("accnbr", "6230580000048295914") //账号
                                .set("accnam", "普海涛") //户名
                                .set("trsamt", "0.25") //交易金额
                                .set("trsdsp", "测试") //注释
                                .set("cprref", "*********")
                                .set("eacbnk", "平安银行")
                ),
                MapUtil.sort(
                        Dict.create() //收款方信息
                                .set("trxseq", "00000001") //交易序号
                                .set("accnbr", "6230580000048295914") //账号
                                .set("accnam", "普海涛") //户名
                                .set("trsamt", "0.25") //交易金额
                                .set("trsdsp", "测试") //注释
                                .set("cprref", "*********")
                                .set("eacbnk", "平安银行")
                )
        ));

        JSONObject payApplyBatch = cmbApiService.payApplyBatch(accountNo, body);
        System.out.println(payApplyBatch.toJSONString());
    }


    /**
     * 5.4.7代发类型查询 BB6AGTQY
     */
    @Test
    public void testPayTypeQury() {
        cmbApiService.payTypeQuery(accountNo);
    }

    /**
     * 3.批次与明细查询 BB6BPDQY
     */
    @Test
    public void testPayBatchQuery() {
        cmbApiService.payBatchQuery(accountNo, "G20240628000186887");
    }


    /**
     * 5. 代发批次查询 BB6BTHQY
     */
    @Test
    public void queryPayBatchOrder() {
        JSONObject batchOrder = cmbApiService.queryPayBatchOrder("********01", accountNo);
        System.out.println(batchOrder.toJSONString());
    }

    /**
     * 5.4.5代发明细查询 BB6DTLQY
     */
    @Test
    public void testPayRecordDetailQuery() {
        cmbApiService.payRecordDetailQuery(accountNo, "4D20004676", null, null);
    }

    /**
     * 13.代发明细对账单查询请求DCAGPPDF
     */
    @Test
    public void applyReconFile() {
        cmbApiService.applyReconFile(accountNo, "2024-03-04", "2024-03-04", "0");
    }


    /**
     * 代发明细对账单处理结果查询请求DCTASKID
     */
    @Test
    public void getReconFileUrl() {
        cmbApiService.getReconFileUrl(accountNo, "IM0sQ9GOBXyqdQvnEhDacVq5yXZgNllqYnL5fyYC7Rg=");
    }

    /**
     * 代发明细对账单处理结果查询请求DCTASKID
     */
    @Test
    public void getAccountReconFileUrl() {
        cmbApiService.getAccountReconFileUrl(accountNo, "mwy2m8BVURHlksud7uBV5H8iLiY38N9gttZTNLiKWTo=");
    }

    /**
     * 5.4.6代发退票查询 BB6RFDQY
     */
    @Test
    public void testPayRefundQuery() {
        cmbApiService.payRefundQuery(accountNo, "********", "********");
    }

    @Autowired
    private CmbPayBiz payBiz;

    @Test
    public void testSyncOrder() {
        payBiz.syncOrder2Local(accountNo);
    }

    @Autowired
    private RedisRateLimit redisRateLimit;

    @Test
    public void testRateLimit() throws InterruptedException {

        while (true) {
            Long token = redisRateLimit.getLimitToken(
                    "mainstay",
                    "5",
                    "5"
            );
            System.out.println(token);
        }

    }

}
