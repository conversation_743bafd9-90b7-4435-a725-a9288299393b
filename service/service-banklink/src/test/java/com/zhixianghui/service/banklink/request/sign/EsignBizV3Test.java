package com.zhixianghui.service.banklink.request.sign;

import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgAuthUrlReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgAuthUrlResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDateVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.PsnAuthUrlReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.PsnAuthUrlResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.PsnIdentityInfoReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.PsnIdentityInfoResDataVo;
import com.zhixianghui.service.banklink.ServiceBanklinkApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;


/**
 * <AUTHOR>
 * @description
 * @date 2024年4月9日11:20:59
 **/

@SpringBootTest(classes = ServiceBanklinkApp.class,webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class EsignBizV3Test {

    @Autowired
    private EsignBizV3 esignBizV3;

    /**
     * 查询个人认证信息
     */
    @Test
    public void PsnInfoV3(){
        EsignResVo<PsnIdentityInfoResDataVo> PsnIdentityInfoResDataVoEsignResVo = esignBizV3.getPsnIdentityInfoV3(new PsnIdentityInfoReqVo().setPsnAccount("***********"));
        System.out.println(PsnIdentityInfoResDataVoEsignResVo);
    }

    /**
     * 获取个人认证&授权页面链接
     */
    @Test
    public void PsnAuthUrlV3(){
        PsnAuthUrlReqVo psnAuthUrlReqVo  = new PsnAuthUrlReqVo();


        psnAuthUrlReqVo.setPsnAuthConfig( new PsnAuthUrlReqVo.
                PsnAuthConfig().
                setPsnId("e636d46a03b845e49603a143f37e70ef"));


        psnAuthUrlReqVo.setAuthorizeConfig(new PsnAuthUrlReqVo.AuthorizeConfig()
                .setAuthorizedScopes(Arrays.asList("get_psn_identity_info","psn_initiate_sign","manage_psn_resource","psn_sign_file_storage")));

        EsignResVo<PsnAuthUrlResDataVo> psnAuthUrl = esignBizV3.PsnAuthUrlV3(psnAuthUrlReqVo);
        System.out.println(psnAuthUrl);
    }


    /**
     * 查询机构认证信息
     */
    @Test
    public void OrgIdentityInfoV3(){
        OrgIdentityInfoResDateVo orgIdentityInfoResDateVo = new OrgIdentityInfoResDateVo();
        orgIdentityInfoResDateVo.setOrgName("广州市汇聚智享电子科技有限公司");
        EsignResVo<OrgIdentityInfoResDataVo> orgIdentityInfoResDataVoEsignResVo = esignBizV3.OrgIdentityInfoV3(orgIdentityInfoResDateVo);
        System.out.println(orgIdentityInfoResDataVoEsignResVo);
    }

    /**
     * 获取机构认证&授权页面链接
     */
    @Test
    public void OrgAuthUrlV3(){
        OrgAuthUrlReqVo orgAuthUrlReqVo  = new OrgAuthUrlReqVo();


        orgAuthUrlReqVo.setOrgAuthConfig( new OrgAuthUrlReqVo.
                OrgAuthConfig().
                setOrgId("9d2b91f8a6d44a0c8cfa1691dd1dea32"));


        orgAuthUrlReqVo.setAuthorizeConfig(new OrgAuthUrlReqVo.AuthorizeConfig()
                .setAuthorizedScopes(Arrays.asList("get_org_identity_info"
                        ,"org_initiate_sign")));

        EsignResVo<OrgAuthUrlResDataVo> orgAuthUrl = esignBizV3.OrgAuthUrlV3(orgAuthUrlReqVo);
        System.out.println(orgAuthUrl);
    }

}