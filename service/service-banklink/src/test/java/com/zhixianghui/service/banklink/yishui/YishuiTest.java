package com.zhixianghui.service.banklink.yishui;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.facade.banklink.vo.yishui.*;
import com.zhixianghui.facade.banklink.vo.yishui.req.*;
import com.zhixianghui.facade.common.entity.config.AreaCity;
import com.zhixianghui.facade.common.entity.config.AreaMap;
import com.zhixianghui.facade.common.entity.config.AreaProvince;
import com.zhixianghui.facade.common.entity.config.AreaTown;
import com.zhixianghui.facade.common.service.AreaProvinceFacade;
import com.zhixianghui.service.banklink.ServiceBanklinkApp;
import com.zhixianghui.service.banklink.request.xiaomifeng.BeeRequest;
import com.zhixianghui.service.banklink.request.yishui.YishuiService;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import okhttp3.*;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@SpringBootTest(classes = ServiceBanklinkApp.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class YishuiTest {
    @Autowired
    private BeeRequest beeRequest;

    @Test
    public void getJobInfo() {
        System.out.println(beeRequest.batchQuery("Md3cf5c793040045","C20240109000000187"));
    }

    @Test
    public void upload() {

    }
}
