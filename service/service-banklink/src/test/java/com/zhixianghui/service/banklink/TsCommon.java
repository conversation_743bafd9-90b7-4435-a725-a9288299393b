package com.zhixianghui.service.banklink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.service.banklink.core.biz.KeyPairRecordBiz;
import com.zhixianghui.service.banklink.request.pay.AlipayBiz;
import com.zhixianghui.service.banklink.request.pay.JoinpayPayBiz;
import com.zhixianghui.service.banklink.utils.alipay.AlipayUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/27
 **/
@SpringBootTest(classes = ServiceBanklinkApp.class)
@RunWith(SpringRunner.class)
public class TsCommon {
    @Autowired
    private KeyPairRecordBiz recordBiz;
    @Autowired
    private JoinpayPayBiz joinpayPayBiz;

    @Autowired
    private AlipayUtil alipayUtil;

    @Autowired
    private AlipayBiz alipayBiz;

    @Test
    public void tsInsert(){
        KeyPairRecord record = new KeyPairRecord();
        record.genEncryptKeyId();
        record.setMchPrivateKeyEncrypt("MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJjMpubH1sTeajm65vedq/rvRlr4hgt/WfTFd4EE7dNHxAlLtYQ99I4iXIECHD/kEU8eoHwQuof15u0y6XjXKuzII19J5xZrglwlsWBSVuzYRMAMHojqJS2z4UhNNl1gv3Ib95jQFpzpeC+2cfh3w5ky5qZdPp1jdWotsKm2ttuPAgMBAAECgYAMC/6QrWrKZLq9SHYTSbQYearzWyq+V1ERUkxEuM3DXaIgFX7/KR9R4XWnmjH9+449za1beroqJAIL63qq01QH7VlSz8wdJdRPA/88JTT4PBb2HF2Qz3bPIvBpvPbxibB1QqmbEvnqksEQ4Zq5Yl5/yx7ln1/73eqKr/pKHt9OuQJBAO40GGZTNS4rYLgvJgxV2WFXAhgMjTtt3APjxta42keKeXAgNC+P+YYL9DVzbmjoirMCymBMH6r9VsvxtM3sPKMCQQCkNxwGwihjqkZ5PQ40tQ1EusyzoMN/K+sw4S6OFu2854jkLk7ybLLpjzq6PRDdqcBJWy1HPCce29RvBnINbwglAkAmWzsmlvV0RsXfQrBUwior02/h4XuIe0s3USE+iCuV44VkebsNgZiDbj9XsAuyVuempadi1D8xMHQeiQA9PHITAkA1wxUZ4Vb3yjdoWeTcvKmrZry90lQuvmIxjod/XWJYBjV6/u45z/yDGrFPZ86tw3WAjqk0OS1VnQLflmygmUlBAkAogeBYrOL1jyLHDyYVMLLImPjg1MXzTJ4efgfm20hABj5zRou1TeOcS3deBKUucXZp7nrNMKG0KopT4TUDmQ4K");
        record.setChannelPublicKeyEncrypt("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCd2pvgWSqgdHm/I9AHs4uRef9BUmRqWt2A4SiB5IomWush/zioh3d+rqwfd0M0tJlv0DYV1KTQG/fjPkSXaGQW0nw55bkXjK7qGCuyd8RLeiJAcFJE7NlhTL0fZ4ZgSYvI2T1bGURvZcc4A+Lown4mjr9bzaaBaYldqppG+iu36wIDAQAB");
        record.setUpdator("test");
        record.setChannelNo("joinpay");
        record.setChannelMchNo("888100000003636");
        record.setVersion(0);
        record.setCreateTime(new Date());
        recordBiz.updateOrInsertIfNotExist(Lists.newArrayList(record));
    }


    @Test
    public void testChech() {
        String data = "{\"charset\":\"utf-8\",\"notify_time\":\"2021-03-31 19:59:24\",\"alipay_user_id\":\"2088302056430623\",\"sign\":\"mc5R7hPj3tXyLJkKyQnuT4jzZCOCV+85njg/bA2u5nv96UVXC5u/tDVmOgBhAcZs+0GphNn1q6P8VwzVrET0dGkc1d6OxLhHP1giwBNsww5Z2YwjD4t+uAlLAP9l59izip6BDkzgNoHy3SDemZIj3VF3wqGPztLffjfHToWsV7VYWwbW4zORxKU3rPD6MWL47jDF7/SlC/M+zB5GUBRVXNCZu4y3uEaedzJmXPraxinyf0e1QUybwP3S7sps0frP3eeiUW0+Tf74ZkJlAkRBCzoCOmW11rHF0x72bOhYoq1FEWzrStyg5mtuXzlSLkbsYS5SDoeuhs/mnlNupJBhfA==\",\"external_agreement_no\":\"212993201232\",\"version\":\"1.0\",\"sign_time\":\"2021-03-31 19:59:24\",\"notify_id\":\"2021033100222195924092711459021741\",\"notify_type\":\"dut_user_sign\",\"agreement_no\":\"20215131720176121662\",\"invalid_time\":\"2115-02-01 00:00:00\",\"auth_app_id\":\"2021002135615256\",\"personal_product_code\":\"FUND_SAFT_SIGN_WITHHOLDING_P\",\"valid_time\":\"2021-03-31 19:59:24\",\"login_token\":\"73b764dbf078c0d2ef4e8d6e08889f2e_62\",\"app_id\":\"2021002135615256\",\"sign_type\":\"RSA2\",\"sign_scene\":\"INDUSTRY|SATF_ACC\",\"status\":\"NORMAL\",\"alipay_logon_id\":\"pht***@163.com\"}";
        JSONObject jsonObject = JSON.parseObject(data);

        boolean verify = alipayUtil.verify(jsonObject.toJavaObject(Map.class));
        System.out.println(verify);

    }



}
