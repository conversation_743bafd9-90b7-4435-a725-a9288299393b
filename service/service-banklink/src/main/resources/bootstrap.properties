spring.application.name=service-banklink
spring.cloud.nacos.config.server-addr=@nacosAddr@
spring.cloud.nacos.config.namespace=@nacosNamespace@
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.discovery.namespace=@nacosNamespace@

logging.level.com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder=warn


spring.cloud.nacos.config.shared-dataids=dubbo.properties,db.properties,rocketmq.properties,redis.properties,fastdfs.properties,wx-config.properties
spring.cloud.nacos.config.refreshable-dataids=dubbo.properties,db.properties,rocketmq.properties,redis.properties,fastdfs.properties,wx-config.properties

#channel.cmb.server-host=https://cdc.cmbchina.com/cdcserver/api/v2
channel.cmb.server-host=http://cdctest.cmburl.cn/cdcserver/api/v2
channel.cmb.bus-mod=00003
