<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.banklink.entity.MailReceiver">
    <sql id="table"> tbl_mail_receiver </sql>

    <!-- 用于返回的bean对象 -->
    <resultMap id="BaseResultMap" type="com.zhixianghui.facade.banklink.entity.MailReceiver">
        <result column="ID" property="id" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="INTEGER"/>
        <result column="GROUP_KEY" property="groupKey" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="FROM" property="from" jdbcType="VARCHAR"/>
        <result column="TO" property="to" jdbcType="OTHER"/>
    </resultMap>

    <!-- 用于select查询公用抽取的列 -->
    <sql id="Base_Column_List">
		ID,
		CREATE_TIME,
		VERSION,
		GROUP_KEY,
		REMARK,
		FROM,
		TO
	</sql>

    <!-- 插入记录 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.banklink.entity.MailReceiver">
        INSERT INTO <include refid="table" /> (
        CREATE_TIME,
        VERSION,
        GROUP_KEY,
        REMARK,
        FROM,
        TO
        ) VALUES (
        #{createTime,jdbcType=TIMESTAMP},
        0,
        #{groupKey,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{from,jdbcType=VARCHAR},
        #{to,jdbcType=OTHER}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
        INSERT INTO <include refid="table" /> (
        CREATE_TIME,
        VERSION,
        GROUP_KEY,
        REMARK,
        FROM,
        TO
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.createTime,jdbcType=TIMESTAMP},
            0,
            #{item.groupKey,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.from,jdbcType=VARCHAR},
            #{item.to,jdbcType=OTHER}
            )
        </foreach>
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.zhixianghui.facade.banklink.entity.MailReceiver">
        UPDATE <include refid="table" />
        <set>
            CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            VERSION = #{version,jdbcType=INTEGER} + 1,
            GROUP_KEY = #{groupKey,jdbcType=VARCHAR},
            REMARK = #{remark,jdbcType=VARCHAR},
            FROM = #{from,jdbcType=VARCHAR},
            TO = #{to,jdbcType=OTHER}
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 只更新不为null的属性 -->
    <update id="updateIfNotNull" parameterType="com.zhixianghui.facade.banklink.entity.MailReceiver">
        UPDATE <include refid="table" />
        <set>
            <if test="createTime != null">
                CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
            </if>
            VERSION = #{version,jdbcType=INTEGER} + 1,
            <if test="groupKey != null">
                GROUP_KEY = #{groupKey,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="from != null">
                FROM = #{from,jdbcType=VARCHAR},
            </if>
            <if test="to != null">
                TO = #{to,jdbcType=OTHER}
            </if>
        </set>
        WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=INTEGER}
    </update>

    <!-- 多条件组合查询 -->
    <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
        <choose>
            <when test="sortColumns != null and sortColumns !='' ">
                <![CDATA[ ORDER BY ${sortColumns} ]]>
            </when>
            <otherwise>
                <![CDATA[ ORDER BY ID DESC ]]>
            </otherwise>
        </choose>
    </select>

    <!-- 根据多条件组合查询，计算总记录数 -->
    <select id="countBy" parameterType="java.util.Map" resultType="long">
        SELECT count(ID) FROM
        <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </select>

    <!-- 按查询条件删除 -->
    <delete id="deleteBy">
        DELETE FROM <include refid="table" />
        <where>
            <include refid="condition_sql" />
        </where>
    </delete>

    <!-- 根据多个id查询 -->
    <select id="listByIdList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </select>

    <!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="table" />
        WHERE ID = #{id,jdbcType=BIGINT}
    </select>

    <!-- 按id主键删除 -->
    <delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 按多个id主键删除 -->
    <delete id="deleteByIdList" parameterType="list">
        DELETE FROM <include refid="table" />
        WHERE ID IN
        <foreach collection="list" item="item" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </delete>

    <!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
    <!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

    <sql id="condition_sql">
        <if test="id != null ">
            AND ID = #{id,jdbcType=BIGINT}
        </if>
        <if test="idList != null and idList.size() > 0">
            AND ID IN <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
        </if>
        <if test="createTime != null ">
            AND CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="version != null ">
            AND VERSION = #{version,jdbcType=INTEGER}
        </if>
        <if test="groupKey != null and groupKey !='' ">
            AND GROUP_KEY = #{groupKey,jdbcType=VARCHAR}
        </if>
        <if test="remark != null and remark !='' ">
            AND REMARK = #{remark,jdbcType=VARCHAR}
        </if>
        <if test="from != null and from !='' ">
            AND FROM = #{from,jdbcType=VARCHAR}
        </if>
        <if test="to != null ">
            AND TO = #{to,jdbcType=OTHER}
        </if>
    </sql>
</mapper>

