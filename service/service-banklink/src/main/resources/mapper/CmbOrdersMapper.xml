<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.banklink.core.mapper.CmbOrdersMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.banklink.entity.CmbOrders">
    <!--@mbg.generated-->
    <!--@Table tbl_cmb_orders-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="accnbr" jdbcType="VARCHAR" property="accnbr" />
    <result column="dmanbr" jdbcType="VARCHAR" property="dmanbr" />
    <result column="dmanam" jdbcType="VARCHAR" property="dmanam" />
    <result column="trxnbr" jdbcType="VARCHAR" property="trxnbr" />
    <result column="trxamt" jdbcType="DECIMAL" property="trxamt" />
    <result column="trxdir" jdbcType="CHAR" property="trxdir" />
    <result column="trxtim" jdbcType="VARCHAR" property="trxtim" />
    <result column="rpyacc" jdbcType="VARCHAR" property="rpyacc" />
    <result column="rpynam" jdbcType="VARCHAR" property="rpynam" />
    <result column="trxtxt" jdbcType="VARCHAR" property="trxtxt" />
    <result column="narinn" jdbcType="VARCHAR" property="narinn" />
    <result column="autflg" jdbcType="INTEGER" property="autflg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, accnbr, dmanbr, dmanam, trxnbr, trxamt, trxdir, trxtim, rpyacc, rpynam, trxtxt,
    narinn, create_time,autflg
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tbl_cmb_orders
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="accnbr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.accnbr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dmanbr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dmanbr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dmanam = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dmanam,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="trxnbr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.trxnbr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="trxamt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.trxamt,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="trxdir = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.trxdir,jdbcType=CHAR}
        </foreach>
      </trim>
      <trim prefix="trxtim = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.trxtim,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="rpyacc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.rpyacc,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="rpynam = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.rpynam,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="trxtxt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.trxtxt,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="narinn = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.narinn,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tbl_cmb_orders
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="accnbr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accnbr != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.accnbr,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="dmanbr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dmanbr != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dmanbr,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="dmanam = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dmanam != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dmanam,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="trxnbr = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.trxnbr != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.trxnbr,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="trxamt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.trxamt != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.trxamt,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="trxdir = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.trxdir != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.trxdir,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="trxtim = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.trxtim != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.trxtim,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="rpyacc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.rpyacc != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.rpyacc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="rpynam = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.rpynam != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.rpynam,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="trxtxt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.trxtxt != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.trxtxt,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="narinn = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.narinn != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.narinn,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_cmb_orders
    (accnbr, dmanbr, dmanam, trxnbr, trxamt, trxdir, trxtim, rpyacc, rpynam, trxtxt,
      narinn, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.accnbr,jdbcType=VARCHAR}, #{item.dmanbr,jdbcType=VARCHAR}, #{item.dmanam,jdbcType=VARCHAR},
        #{item.trxnbr,jdbcType=VARCHAR}, #{item.trxamt,jdbcType=DECIMAL}, #{item.trxdir,jdbcType=CHAR},
        #{item.trxtim,jdbcType=VARCHAR}, #{item.rpyacc,jdbcType=VARCHAR}, #{item.rpynam,jdbcType=VARCHAR},
        #{item.trxtxt,jdbcType=VARCHAR}, #{item.narinn,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.zhixianghui.facade.banklink.entity.CmbOrders" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_cmb_orders
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      accnbr,
      dmanbr,
      dmanam,
      trxnbr,
      trxamt,
      trxdir,
      trxtim,
      rpyacc,
      rpynam,
      trxtxt,
      narinn,
      create_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{accnbr,jdbcType=VARCHAR},
      #{dmanbr,jdbcType=VARCHAR},
      #{dmanam,jdbcType=VARCHAR},
      #{trxnbr,jdbcType=VARCHAR},
      #{trxamt,jdbcType=DECIMAL},
      #{trxdir,jdbcType=CHAR},
      #{trxtim,jdbcType=VARCHAR},
      #{rpyacc,jdbcType=VARCHAR},
      #{rpynam,jdbcType=VARCHAR},
      #{trxtxt,jdbcType=VARCHAR},
      #{narinn,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      accnbr = #{accnbr,jdbcType=VARCHAR},
      dmanbr = #{dmanbr,jdbcType=VARCHAR},
      dmanam = #{dmanam,jdbcType=VARCHAR},
      trxnbr = #{trxnbr,jdbcType=VARCHAR},
      trxamt = #{trxamt,jdbcType=DECIMAL},
      trxdir = #{trxdir,jdbcType=CHAR},
      trxtim = #{trxtim,jdbcType=VARCHAR},
      rpyacc = #{rpyacc,jdbcType=VARCHAR},
      rpynam = #{rpynam,jdbcType=VARCHAR},
      trxtxt = #{trxtxt,jdbcType=VARCHAR},
      narinn = #{narinn,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.zhixianghui.facade.banklink.entity.CmbOrders" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tbl_cmb_orders
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accnbr != null and accnbr != ''">
        accnbr,
      </if>
      <if test="dmanbr != null and dmanbr != ''">
        dmanbr,
      </if>
      <if test="dmanam != null and dmanam != ''">
        dmanam,
      </if>
      <if test="trxnbr != null and trxnbr != ''">
        trxnbr,
      </if>
      <if test="trxamt != null">
        trxamt,
      </if>
      <if test="trxdir != null and trxdir != ''">
        trxdir,
      </if>
      <if test="trxtim != null and trxtim != ''">
        trxtim,
      </if>
      <if test="rpyacc != null and rpyacc != ''">
        rpyacc,
      </if>
      <if test="rpynam != null and rpynam != ''">
        rpynam,
      </if>
      <if test="trxtxt != null and trxtxt != ''">
        trxtxt,
      </if>
      <if test="narinn != null and narinn != ''">
        narinn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accnbr != null and accnbr != ''">
        #{accnbr,jdbcType=VARCHAR},
      </if>
      <if test="dmanbr != null and dmanbr != ''">
        #{dmanbr,jdbcType=VARCHAR},
      </if>
      <if test="dmanam != null and dmanam != ''">
        #{dmanam,jdbcType=VARCHAR},
      </if>
      <if test="trxnbr != null and trxnbr != ''">
        #{trxnbr,jdbcType=VARCHAR},
      </if>
      <if test="trxamt != null">
        #{trxamt,jdbcType=DECIMAL},
      </if>
      <if test="trxdir != null and trxdir != ''">
        #{trxdir,jdbcType=CHAR},
      </if>
      <if test="trxtim != null and trxtim != ''">
        #{trxtim,jdbcType=VARCHAR},
      </if>
      <if test="rpyacc != null and rpyacc != ''">
        #{rpyacc,jdbcType=VARCHAR},
      </if>
      <if test="rpynam != null and rpynam != ''">
        #{rpynam,jdbcType=VARCHAR},
      </if>
      <if test="trxtxt != null and trxtxt != ''">
        #{trxtxt,jdbcType=VARCHAR},
      </if>
      <if test="narinn != null and narinn != ''">
        #{narinn,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="accnbr != null and accnbr != ''">
        accnbr = #{accnbr,jdbcType=VARCHAR},
      </if>
      <if test="dmanbr != null and dmanbr != ''">
        dmanbr = #{dmanbr,jdbcType=VARCHAR},
      </if>
      <if test="dmanam != null and dmanam != ''">
        dmanam = #{dmanam,jdbcType=VARCHAR},
      </if>
      <if test="trxnbr != null and trxnbr != ''">
        trxnbr = #{trxnbr,jdbcType=VARCHAR},
      </if>
      <if test="trxamt != null">
        trxamt = #{trxamt,jdbcType=DECIMAL},
      </if>
      <if test="trxdir != null and trxdir != ''">
        trxdir = #{trxdir,jdbcType=CHAR},
      </if>
      <if test="trxtim != null and trxtim != ''">
        trxtim = #{trxtim,jdbcType=VARCHAR},
      </if>
      <if test="rpyacc != null and rpyacc != ''">
        rpyacc = #{rpyacc,jdbcType=VARCHAR},
      </if>
      <if test="rpynam != null and rpynam != ''">
        rpynam = #{rpynam,jdbcType=VARCHAR},
      </if>
      <if test="trxtxt != null and trxtxt != ''">
        trxtxt = #{trxtxt,jdbcType=VARCHAR},
      </if>
      <if test="narinn != null and narinn != ''">
        narinn = #{narinn,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>
