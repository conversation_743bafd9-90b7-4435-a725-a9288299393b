<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zhixianghui.facade.banklink.entity.KeyPairRecord">
	<sql id="table"> tbl_key_pair_record </sql>

	<!-- 用于返回的bean对象 -->
	<resultMap id="BaseResultMap" type="com.zhixianghui.facade.banklink.entity.KeyPairRecord">
		<result column="ID" property="id" jdbcType="BIGINT"/>
		<result column="VERSION" property="version" jdbcType="SMALLINT"/>
		<result column="UPDATOR" property="updator" jdbcType="VARCHAR"/>
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="CHANNEL_NO" property="channelNo" jdbcType="VARCHAR"/>
		<result column="CHANNEL_MCH_NO" property="channelMchNo" jdbcType="VARCHAR"/>
		<result column="MCH_PRIVATE_KEY" property="mchPrivateKey" jdbcType="LONGVARCHAR"/>
		<result column="MCH_PUBLIC_KEY" property="mchPublicKey" jdbcType="LONGVARCHAR"/>
		<result column="CHANNEL_PUBLIC_KEY" property="channelPublicKey" jdbcType="LONGVARCHAR"/>
		<result column="ENCRYPT_KEY_ID" property="encryptKeyId" jdbcType="INTEGER"/>
		<result column="CHANNEL_LOGIN_USER" property="channelLoginUser" jdbcType="VARCHAR"/>
		<result column="CHANNEL_PLAT_NO" property="channelPlatNo" jdbcType="VARCHAR"/>
	</resultMap>

	<!-- 用于select查询公用抽取的列 -->
	<sql id="Base_Column_List">
		ID,
		VERSION,
		UPDATOR,
		CREATE_TIME,
		CHANNEL_NO,
		CHANNEL_MCH_NO,
		MCH_PRIVATE_KEY,
		MCH_PUBLIC_KEY,
		CHANNEL_PUBLIC_KEY,
		ENCRYPT_KEY_ID,
		CHANNEL_LOGIN_USER,
		CHANNEL_PLAT_NO
	</sql>

	<!-- 插入记录 -->
	<insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.zhixianghui.facade.banklink.entity.KeyPairRecord">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	UPDATOR ,
        	CREATE_TIME ,
        	CHANNEL_NO ,
        	CHANNEL_MCH_NO ,
        	MCH_PRIVATE_KEY ,
        	MCH_PUBLIC_KEY ,
        	CHANNEL_PUBLIC_KEY ,
        	ENCRYPT_KEY_ID,
			CHANNEL_LOGIN_USER,
			CHANNEL_PLAT_NO
        ) VALUES (
			0,
			#{updator,jdbcType=VARCHAR},
			#{createTime,jdbcType=TIMESTAMP},
			#{channelNo,jdbcType=VARCHAR},
			#{channelMchNo,jdbcType=VARCHAR},
			#{mchPrivateKey,jdbcType=LONGVARCHAR},
			#{mchPublicKey,jdbcType=LONGVARCHAR},
			#{channelPublicKey,jdbcType=LONGVARCHAR},
			#{encryptKeyId,jdbcType=INTEGER},
			#{channelLoginUser,jdbcType=INTEGER},
			#{channelPlatNo,jdbcType=INTEGER}
        )
	</insert>

	<!-- 批量插入记录 -->
	<insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" parameterType="list">
		INSERT INTO <include refid="table" /> (
        	VERSION ,
        	UPDATOR ,
        	CREATE_TIME ,
        	CHANNEL_NO ,
        	CHANNEL_MCH_NO ,
        	MCH_PRIVATE_KEY ,
        	MCH_PUBLIC_KEY ,
        	CHANNEL_PUBLIC_KEY ,
        	ENCRYPT_KEY_ID,
			CHANNEL_LOGIN_USER,
			CHANNEL_PLAT_NO
        ) VALUES
		<foreach collection="list" item="item" separator=",">
			(
			0,
			#{item.updator,jdbcType=VARCHAR},
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.channelNo,jdbcType=VARCHAR},
			#{item.channelMchNo,jdbcType=VARCHAR},
			#{item.mchPrivateKey,jdbcType=LONGVARCHAR},
			#{item.mchPublicKey,jdbcType=LONGVARCHAR},
			#{item.channelPublicKey,jdbcType=LONGVARCHAR},
			#{item.encryptKeyId,jdbcType=INTEGER},
			#{item.channelLoginUser,jdbcType=INTEGER},
			#{item.channelPlatNo,jdbcType=INTEGER}
			)
		</foreach>
	</insert>

	<!--更新-->
	<update id="update" parameterType="com.zhixianghui.facade.banklink.entity.KeyPairRecord">
        UPDATE <include refid="table" /> SET
			VERSION = #{version,jdbcType=SMALLINT} + 1,
			UPDATOR = #{updator,jdbcType=VARCHAR},
			CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
			CHANNEL_NO = #{channelNo,jdbcType=VARCHAR},
			CHANNEL_MCH_NO = #{channelMchNo,jdbcType=VARCHAR},
			MCH_PRIVATE_KEY = #{mchPrivateKey,jdbcType=LONGVARCHAR},
			MCH_PUBLIC_KEY = #{mchPublicKey,jdbcType=LONGVARCHAR},
			CHANNEL_PUBLIC_KEY = #{channelPublicKey,jdbcType=LONGVARCHAR},
			ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=INTEGER},
			CHANNEL_LOGIN_USER = #{channelLoginUser,jdbcType=INTEGER},
			CHANNEL_PLAT_NO = #{channelPlatNo,jdbcType=INTEGER}
         WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 只更新不为null的属性 -->
	<update id="updateIfNotNull" parameterType="com.zhixianghui.facade.banklink.entity.KeyPairRecord">
		UPDATE <include refid="table" />
		<set>
			VERSION = #{version,jdbcType=SMALLINT} +1,
			<if test="updator != null">
				UPDATOR =#{updator,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				CREATE_TIME =#{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="channelNo != null">
				CHANNEL_NO =#{channelNo,jdbcType=VARCHAR},
			</if>
			<if test="channelMchNo != null">
				CHANNEL_MCH_NO =#{channelMchNo,jdbcType=VARCHAR},
			</if>
			<if test="mchPrivateKey != null">
				MCH_PRIVATE_KEY =#{mchPrivateKey,jdbcType=LONGVARCHAR},
			</if>
			<if test="mchPublicKey != null">
				MCH_PUBLIC_KEY =#{mchPublicKey,jdbcType=LONGVARCHAR},
			</if>
			<if test="channelPublicKey != null">
				CHANNEL_PUBLIC_KEY =#{channelPublicKey,jdbcType=LONGVARCHAR},
			</if>
			<if test="encryptKeyId != null">
				ENCRYPT_KEY_ID =#{encryptKeyId,jdbcType=INTEGER},
			</if>
			<if test="channelLoginUser != null and channelLoginUser != ''">
				CHANNEL_LOGIN_USER =#{channelLoginUser,jdbcType=INTEGER},
			</if>
			<if test="channelPlatNo != null and channelPlatNo != ''">
				CHANNEL_PLAT_NO =#{channelPlatNo,jdbcType=INTEGER},
			</if>
		</set>
		WHERE ID = #{id,jdbcType=BIGINT} and VERSION = #{version,jdbcType=SMALLINT}
	</update>

	<!-- 不分页查询 -->
	<select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询 -->
	<select id="listPage" parameterType="java.util.Map" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 分页查询时计算总记录数 -->
	<select id="countBy" parameterType="java.util.Map" resultType="long">
		select count(1) from
		<include refid="table" />
		<where>
			<include refid="condition_sql" />
		</where>
	</select>

	<!-- 根据id查询 -->
    <select id="getById" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM <include refid="table" />
		WHERE ID = #{id,jdbcType=BIGINT}
	</select>

	<!--按id主键删除-->
	<delete id="deleteById">
        DELETE FROM <include refid="table" /> WHERE ID = #{id,jdbcType=BIGINT}
    </delete>

	<!-- ↑↑↑↑↑↑ 如果有新增字段、删除字段、修改字段 以上内容可用模板生成新内容后直接替换 ↑↑↑↑↑↑ -->
	<!-- ↓↓↓↓↓↓ 以下内容是根据需求手动添加或修改的，请勿使用模板内容直接覆盖 ↓↓↓↓↓↓ -->

	<sql id="condition_sql">
		<if test="id != null">
			and ID = #{id,jdbcType=BIGINT}
		</if>
		<if test="version != null">
			and VERSION = #{version,jdbcType=SMALLINT}
		</if>
		<if test="updator != null and updator !=''">
			and UPDATOR = #{updator,jdbcType=VARCHAR}
		</if>
		<if test="createTime != null">
			and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
		</if>
		<if test="channelNo != null and channelNo !=''">
			and CHANNEL_NO = #{channelNo,jdbcType=VARCHAR}
		</if>
		<if test="channelMchNo != null and channelMchNo !=''">
			and CHANNEL_MCH_NO = #{channelMchNo,jdbcType=VARCHAR}
		</if>
		<if test="mchPrivateKey != null and mchPrivateKey !=''">
			and MCH_PRIVATE_KEY = #{mchPrivateKey,jdbcType=LONGVARCHAR}
		</if>
		<if test="mchPublicKey != null and mchPublicKey !=''">
			and MCH_PUBLIC_KEY = #{mchPublicKey,jdbcType=LONGVARCHAR}
		</if>
		<if test="channelPublicKey != null and channelPublicKey !=''">
			and CHANNEL_PUBLIC_KEY = #{channelPublicKey,jdbcType=LONGVARCHAR}
		</if>
		<if test="encryptKeyId != null">
			and ENCRYPT_KEY_ID = #{encryptKeyId,jdbcType=INTEGER}
		</if>
		<if test="channelLoginUser != null and channelLoginUser !=''">
			and CHANNEL_LOGIN_USER = #{channelLoginUser,jdbcType=INTEGER}
		</if>
		<if test="channelPlatNo != null and channelPlatNo !=''">
			and CHANNEL_PLAT_NO = #{channelPlatNo,jdbcType=INTEGER}
		</if>
	</sql>
</mapper>

