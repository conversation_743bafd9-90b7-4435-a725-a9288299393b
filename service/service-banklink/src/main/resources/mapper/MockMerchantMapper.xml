<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.banklink.core.mapper.MockMerchantMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.banklink.entity.MockMerchant">
    <!--@mbg.generated-->
    <!--@Table tbl_mock_merchant-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employer_no" jdbcType="VARCHAR" property="employerNo" />
    <result column="employer_name" jdbcType="VARCHAR" property="employerName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, employer_no, employer_name, create_time
  </sql>
</mapper>
