package com.zhixianghui.service.banklink.request.cmb;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import com.zhixianghui.facade.banklink.vo.pay.CmbPay2BVo;
import com.zhixianghui.facade.banklink.vo.pay.CmbPayeeVo;
import com.zhixianghui.facade.banklink.vo.pay.CmbPayerVo;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.service.BankCardBinFacade;
import com.zhixianghui.service.banklink.config.CmbConfig;
import com.zhixianghui.service.banklink.core.biz.KeyPairRecordBiz;
import com.zhixianghui.service.banklink.core.biz.account.CmbAccountBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class CmbApiService {

    @Autowired
    private CmbExchangeUtil cmbExchangeUtil;

    @Autowired
    private CmbConfig cmbConfig;
    @Autowired
    private CmbAccountBiz cmbAccountBiz;
    @Autowired
    private KeyPairRecordBiz keyPairRecordBiz;
    @Reference
    private BankCardBinFacade bankCardBinFacade;

    /**
     * 账户详情查询
     *
     * @param accountNo 账号
     * @param bankDivNo 分行号
     */
    public JSONObject getAccountInfo(String accountNo, Integer bankDivNo) {
        Dict body = Dict.create().set("ntqacinfx",
                ListUtil.of(
                        MapUtil.sort(Dict.create().set("bbknbr", bankDivNo).set("accnbr", accountNo)
                        )
                ));

        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        JSONObject result = cmbExchangeUtil.doRequest("NTQACINF", body, keyPairReqVo);
        return result;
    }

    /**
     * 5.1.2查询可经办的账户列表(DCLISACC)
     *
     * @param accountNo
     * @return
     */
    public JSONObject getAllowedAccount(String accountNo) {
        final KeyPairRecord keyPairRecord = keyPairRecordBiz.getByChannelNoAndChannelMchNo("CMB", accountNo);

        Dict body = Dict.create().set("buscod", "N03020").set("busmod", keyPairRecord.getChannelPlatNo().split(":")[1].split("-")[1]);

        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        JSONObject result = cmbExchangeUtil.doRequest("DCLISACC", body, keyPairReqVo);
        return result;
    }

    /**
     * 5.1.4查询账户历史余额(NTQABINF)
     *
     * @param accountNo 分行号
     * @param branchNo  账号
     * @param startDate 起始日期 YYYYMMDD
     * @param endDate   结束日期 YYYYMMDD
     * @return
     */
    public JSONObject getAccountBalanceHis(String accountNo, String branchNo, String startDate, String endDate) {
        Dict body = Dict.create().set("ntqabinfy", ListUtil.of(MapUtil.sort(
                Dict.create().set("bbknbr", branchNo)
                        .set("accnbr", accountNo)
                        .set("bgndat", startDate)
                        .set("enddat", endDate)
        )));

        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        JSONObject result = cmbExchangeUtil.doRequest("NTQABINF", body, keyPairReqVo);
        return result;
    }

    /**
     * 5.1.7账户交易信息查询(DCTRSINF)
     *
     * @param accountNo
     * @param branchNo
     * @param tradeDate YYYYMMDD
     * @param trsseq    起始记账序号  断点续传使用,首次查询填0或者为空，注意交易日切换后，记账序号又须从0起查
     * @return
     */
    public JSONObject getAccountOrders(String accountNo, String branchNo, String tradeDate, String trsseq) {
        Dict body = Dict.create().set("bbknbr", branchNo)
                .set("accnbr", accountNo)
                .set("trsdat", tradeDate)
                .set("trsseq", trsseq);

        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        JSONObject result = cmbExchangeUtil.doRequest("DCTRSINF", body, keyPairReqVo);
        return result;
    }

    /**
     * 7.账户交易信息查询trsQryByBreakPoint
     *
     * @param accountNo
     * @param startDate YYYYMMDD
     * @param endDate    起始记账序号  断点续传使用,首次查询填0或者为空，注意交易日切换后，记账序号又须从0起查
     * @return
     */
    public JSONObject trsQryByBreakPoint(String accountNo, String startDate, String endDate) {
        Dict body = Dict.create()
                .set("TRANSQUERYBYBREAKPOINT_X1", ListUtil.of(MapUtil.sort(
                    Dict.create().set("cardNbr", accountNo)
                        .set("beginDate", startDate)
                        .set("endDate", endDate))));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        JSONObject result = cmbExchangeUtil.doRequest("trsQryByBreakPoint", body, keyPairReqVo);
        return result;
    }



    /**
     * 5.1.5查询分行号信息(NTACCBBK)
     *
     * @param accountNo
     * @return
     */
    public JSONObject getBankBranch(String accountNo) {
        Dict body = Dict.create().set("fctval", accountNo);
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        JSONObject result = cmbExchangeUtil.doRequest("NTACCBBK", body, keyPairReqVo);
        return result;
    }

    /**
     * 新增子记账单元
     */
    public JSONObject createAccountBook(String accountNo, String accountBookNo, String accountBookName) {

        Dict body = Dict.create()
                .set("ntdmaaddx",
                        ListUtil.of(
                                MapUtil.sort(
                                        Dict.create()
                                                .set("accnbr", accountNo)//账号
                                                .set("dmanbr", accountBookNo)//记账子单元编号
                                                .set("dmanam", accountBookName)//记账子单元名称
                                                .set("ovrctl", "N")//记账子单元名称
                                                .set("bcktyp", "Y") //额度控制标志
                                                .set("clstyp", "N") //退票处理方式
                                )
                        )
                );
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        JSONObject result = cmbExchangeUtil.doRequest("NTDMAADD", body, keyPairReqVo);

        return result;
    }

    /**
     * 修改子记账单元
     */
    public JSONObject updateAccountBook(String branchNo, String accountNo, String accountBookNo, String accountBookName) {
        Dict body = Dict.create()
                .set("ntdmamntx1",
                        ListUtil.of(
                                MapUtil.sort(
                                        Dict.create()
                                                .set("bbknbr", branchNo) //分行号
                                                .set("accnbr", accountNo) //账号
                                                .set("dmanbr", accountBookNo) //记账子单元编号
                                                .set("dmanam", accountBookName) //记账子单元名称
                                                .set("ovrctl", "N") //额度控制标志
                                                .set("bcktyp", "Y") //退票处理方式
                                                .set("clstyp", "N") //余额非零时是否可关闭
                                )
                        )
                );

        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMAMNT", body, keyPairReqVo);
    }

    /**
     * 查询子记账单元
     */
    public JSONObject queryAccountBook(String accountNo, String accountBookNo) {
        Dict body = Dict.create()
                .set("ntdmalstx",
                        ListUtil.of(
                                MapUtil.sort(
                                        Dict.create()
                                                .set("accnbr", accountNo) //帐号
                                                .set("dmanbr", accountBookNo) //记账子单元编号
                                )
                        )
                );
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMALST", body, keyPairReqVo);
    }

    /**
     * 关闭记账子单元
     */
    public JSONObject closeAccountBook(String accountNo, String accountBookNo) {
        Dict body = Dict.create()
                .set("ntdmadltx1",
                        Dict.create()
                                .set("accnbr", accountNo) //帐号
                )
                .set("ntdmadltx2",
                        Dict.create().set("dmanbr", accountBookNo) //记账子单元编号
                );
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMADLT", body, keyPairReqVo);
    }

    /**
     * 查询记账子单元余额
     */
    public JSONObject queryAccountBookBalance(String accountNo, String accountBookNo) {
        Dict body = Dict.create()
                .set("ntdmabalx",
                        ListUtil.of(
                                MapUtil.sort(
                                        Dict.create()
                                                .set("accnbr", accountNo) //账号
                                                .set("dmanbr", accountBookNo) //记账子单元编号
                                )
                        )
                );
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMABAL", body, keyPairReqVo);
    }

    /**
     * 记账子单元内部转账
     */
    public JSONObject transferAccountBooks(String accountNo, String payerAccountBookNo, String payeeAccountBookNo, String amt, String remark, String trxNo) {
        /**
         * 1. 更新缓存
         */
//        cmbAccountBiz.transfer(accountNo, payerAccountBookNo, payeeAccountBookNo, amt);
        final KeyPairRecord keyPairRecord = keyPairRecordBiz.getByChannelNoAndChannelMchNo("CMB", accountNo);

        /**
         * 2.请求接口
         */
        Dict body = Dict.create()
                .set("ntbusmody", ListUtil.of(MapUtil.sort(
                        Dict.of("busmod", keyPairRecord.getChannelPlatNo().split(":")[3].split("-")[1])
                )))
                .set("ntdmatrxx1",
                        ListUtil.of(
                                MapUtil.sort(
                                        Dict.create()
                                                .set("accnbr", accountNo) // 账号
                                                .set("dmadbt", payerAccountBookNo) // 付款方记账子单元编号
                                                .set("dmacrt", payeeAccountBookNo) // 收款方记账子单元编号
                                                .set("trxamt", amt) // 转账金额
                                                .set("trxtxt", remark) // 交易摘要
                                                .set("yurref", trxNo) // 业务参考号
                                )
                        )
                );
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMATRX", body, keyPairReqVo);
    }

    /**
     * 5.4.1代发经办 BB6BTHHL
     *
     * @param cmbPayerVo
     * @param cmbPayeeVo
     * @return
     */
    public JSONObject payApply(CmbPayerVo cmbPayerVo, CmbPayeeVo cmbPayeeVo) {
        if (StringUtils.isBlank(cmbPayeeVo.getBankName())) {
            BankCardBin cardBinByCardNo = bankCardBinFacade.getCardBinByCardNo(cmbPayeeVo.getAccountNo());
            if (cardBinByCardNo == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("{}::{}", ApiExceptions.API_BIZ_FAIL.getApiErrorCode(), "卡bin校验失败"));
            }
            cmbPayeeVo.setBankName(cardBinByCardNo.getBankName());
        }

        /**
         * 测试注释掉
         * cmbAccountBiz.pay(cmbPayerVo.getAccountNo(), cmbPayerVo.getAccountBookNo(), cmbPayeeVo.getAmount());
         */
        final KeyPairRecord keyPairRecord = keyPairRecordBiz.getByChannelNoAndChannelMchNo("CMB", cmbPayerVo.getAccountNo());


        Dict body = Dict.create()
                .set("bb6busmod",
                        ListUtil.of(
                                MapUtil.sort(
                                        Dict.create()
                                                .set("buscod", "N03020") //业务类型
                                                .set("busmod", keyPairRecord.getChannelPlatNo().split(":")[1].split("-")[1]) //业务模式
                                )
                        )
                )
                .set("bb6cdcbhx1",
                        ListUtil.of(
                                MapUtil.sort(
                                        Dict.create()  //付款方信息
                                                .set("begtag", "Y")//批次开始标志
                                                .set("endtag", "Y")//批次结束标志
                                                .set("accnbr", cmbPayerVo.getAccountNo())//账号
                                                .set("accnam", cmbPayerVo.getAccountName())//户名
                                                .set("ttlamt", cmbPayerVo.getAmount())//总金额
                                                .set("ttlcnt", "1")//总笔数
                                                .set("ttlnum", "1")//总次数
                                                .set("curamt", cmbPayerVo.getAmount())//本次金额
                                                .set("curcnt", "1")//本次笔数
                                                .set("ccynbr", "10")//币种
                                                .set("trstyp", "BYBK")//交易类型
                                                .set("nusage", StringUtils.isBlank(cmbPayerVo.getRemark()) ? "下发" : cmbPayerVo.getRemark())//用途
                                                .set("eptdat", DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN))//期望日期
                                                .set("yurref", "ZHIXIANG" + cmbPayerVo.getPayBatchNo())//业务参考号
                                                .set("dmanbr", cmbPayerVo.getAccountBookNo())//记账子单元
                                                .set("chlflg", "Y")//结算通道
                                )
                        )
                )
                .set("bb6cdcdlx1",
                        ListUtil.of(
                                MapUtil.sort(
                                        Dict.create() //收款方信息
                                                .set("trxseq", cmbPayeeVo.getRemitSeqNo()) //交易序号
                                                .set("accnbr", cmbPayeeVo.getAccountNo()) //账号
                                                .set("accnam", cmbPayeeVo.getAccountName()) //户名
                                                .set("trsamt", cmbPayeeVo.getAmount()) //交易金额
                                                .set("trsdsp", cmbPayeeVo.getRemark()) //注释
                                                .set("cprref", cmbPayeeVo.getRemitDetailNo())
                                                .set("eacbnk", cmbPayeeVo.getBankName())
                                )
                        )
                );

        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(cmbPayerVo.getAccountNo());
        return cmbExchangeUtil.doRequest("BB6BTHHL", body, keyPairReqVo);
    }

    /**
     * 5.3.6超网批量代发
     *
     * @return
     */
    public JSONObject payApplyBatch(String accountNo, Dict body) {

        /**
         * 测试注释掉
         * cmbAccountBiz.pay(cmbPayerVo.getAccountNo(), cmbPayerVo.getAccountBookNo(), cmbPayeeVo.getAmount());
         */

        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("BB6BTHHL", body, keyPairReqVo);
    }

    /**
     * 5.4.7代发类型查询 BB6AGTQY
     *
     * @param accountNo
     * @return
     */
    public JSONObject payTypeQuery(String accountNo) {
        Dict body = Dict.create()
                .set("bb6typqyx1", ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("buscod", "N03020")  //业务类型
                                        .set("accnbr", accountNo) //账号
                                        .set("ccynbr", "10")
                                        .set("stscod", "A")
                        )
                ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("BB6AGTQY", body, keyPairReqVo);
    }

    /**
     * 5.4.2批次与明细查询 BB6BPDQY
     *
     * @param accountNo
     * @param remitNo
     * @return
     */
    public JSONObject payBatchQuery(String accountNo, String remitNo) {
        Dict body = Dict.create()
                .set("bb6bpdqyy1", ListUtil.of(

                        MapUtil.sort(
                                Dict.create()
                                        .set("buscod", "N03020")
                                        .set("yurref", "ZHIXIANG" + remitNo)
                        )

                ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("BB6BPDQY", body, keyPairReqVo);
    }

    /**
     * 5.4.6代发退票查询 BB6RFDQY
     *
     * @param accountNo
     * @param startDate YYYYMMDD
     * @param endDate   YYYYMMDD
     * @return
     */
    public JSONObject payRefundQuery(String accountNo, String startDate, String endDate) {
        Dict body = Dict.create()
                .set("bb6rfdqyy1", ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("accnbr", accountNo)
                                        .set("trstyp", "BYBK")
                                        .set("bgndat", startDate)
                                        .set("enddat", endDate)
                        )

                ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("BB6RFDQY", body, keyPairReqVo);
    }

    /**
     * 5.4.5代发明细查询 BB6DTLQY
     *
     * @param accountNo
     * @return
     */
    public JSONObject payRecordDetailQuery(String accountNo, String reqnbr, String trxseq, String bthnbr) {
        Dict body = Dict.create()
                .set("bb6dtlqyy1", ListUtil.of(

                        MapUtil.sort(
                                Dict.create()
                                        .set("reqnbr", reqnbr)
                                        .set("trxseq", trxseq)
                                        .set("bthnbr", bthnbr)
                        )

                ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("BB6DTLQY", body, keyPairReqVo);
    }

    /**
     * 5.1.1可经办业务模式查询(DCLISMOD)
     *
     * @param accountNo
     * @param buscode
     * @return
     */
    public JSONObject getAllowedBizMod(String accountNo, String buscode) {
        Dict dict = Dict.create().set("buscod", buscode);
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("DCLISMOD", dict, keyPairReqVo);
    }

    /**
     * 代发明细对账单查询请求DCAGPPDF
     *
     * @param accountNo
     * @return
     */
    public JSONObject applyReconFile(String accountNo, String beginDate, String endDate, String begidx) {
        final KeyPairRecord keyPairRecord = keyPairRecordBiz.getByChannelNoAndChannelMchNo("CMB", accountNo);
        final String s = keyPairRecord.getChannelPlatNo().split(":")[1];
        Dict body = Dict.create()
                .set("payeac", accountNo)
                .set("begdat", beginDate)
                .set("enddat", endDate)
                .set("buscod", s.split("-")[0])
                .set("busmod", s.split("-")[1])
                .set("prtmod", "S")
                .set("begidx", begidx);

        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("DCAGPPDF", body, keyPairReqVo);
    }

    /**
     * 电子回单异步查询ASYCALHD
     *
     * @param accountNo
     * @param beginDate
     * @param endDate
     * @return
     */
    public JSONObject applyAccountReconFile(String accountNo, String beginDate, String endDate) {
        Dict body = Dict.create()
                .set("primod", "PDF")
                .set("eacnbr", accountNo)
                .set("begdat", beginDate)
                .set("enddat", endDate)
                .set("rrcflg", "1");
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("ASYCALHD", body, keyPairReqVo);
    }

    /**
     * 14.代发明细对账单处理结果查询请求DCTASKID
     *
     * @param accountNo
     * @param taskId
     * @return
     */
    public JSONObject getReconFileUrl(String accountNo, String taskId) {
        Dict body = Dict.create().set("taskid", taskId).set("qwenab", "true");
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("DCTASKID", body, keyPairReqVo);
    }

    /**
     * 14.代发明细对账单处理结果查询请求DCTASKID
     *
     * @param accountNo
     * @param taskId
     * @return
     */
    public JSONObject getAccountReconFileUrl(String accountNo, String taskId) {
        Dict body = Dict.create().set("taskid", taskId).set("qwenab", "true");
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("DCTASKID", body, keyPairReqVo);
    }

    /**
     * 5.5.2.13查询记账子单元当天交易NTDMTLST
     *
     * @param accountNo
     * @param accountBookNo
     * @param ctnkey
     * @return
     */
    public JSONObject queryTodayAccountBookRecord(String accountNo, String accountBookNo, String ctnkey) {
        Dict body = Dict.create().set("ntdmtlsty", ListUtil.of(
                MapUtil.sort(
                        Dict.create().set("accnbr", accountNo)
                                .set("dmanbr", accountBookNo)
                                .set("ctnkey", ctnkey)
                )
        ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMTLST", body, keyPairReqVo);
    }

    /**
     * 5.5.2.14查询记账子单元历史交易NTDMTHLS
     *
     * @param accountNo
     * @param accountBookNo
     * @param ctnkey
     * @return
     */
    public JSONObject queryHisAccountBookRecord(String accountNo, String accountBookNo, String ctnkey, int dayOffset) {
        Dict body = Dict.create().set("ntdmthlsy", ListUtil.of(
                MapUtil.sort(
                        Dict.create().set("accnbr", accountNo)
                                .set("dmanbr", accountBookNo)
                                .set("ctnkey", ctnkey)
                                .set("begdat", DateUtil.offsetDay(new Date(), dayOffset).toString("yyyyMMdd"))
                                .set("enddat", DateUtil.offsetDay(new Date(), -1).toString("yyyyMMdd"))
                )
        ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMTHLS", body, keyPairReqVo);
    }

    /**
     * 5.5.2.17查询单个记账子单元的历史余额NTDMAHAD
     *
     * @param bankBranch
     * @param accountNo
     * @param accountBookNo
     * @param begdat
     * @param enddat
     * @return
     */
    public JSONObject queryDailyAccountNoBalance(String bankBranch, String accountNo, String accountBookNo, String begdat, String enddat) {
        Dict body = Dict.create().set("ntdmahadx1", ListUtil.of(
                MapUtil.sort(
                        Dict.create().set("bbknbr", bankBranch)
                                .set("accnbr", accountNo)
                                .set("dmanbr", accountBookNo)
                                .set("begdat", begdat)
                                .set("enddat", enddat)
                )
        ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMAHAD", body, keyPairReqVo);
    }

    /**
     * 5.5.2.20记账子单元冲账NTHCLACT
     *
     * @param accountNo
     * @param tradeDate
     * @param trxNo
     * @return
     */
    public JSONObject strikeBalance(String accountNo, String orgAccountNo, String tradeDate, String trxNo) {
        Dict body = Dict.create().set("nthclactx1", ListUtil.of(
                MapUtil.sort(
                        Dict.create().set("accnbr", accountNo)
                )
        )).set("nthclactx2", ListUtil.of(
                MapUtil.sort(
                        Dict.create().set("dmanbr", orgAccountNo)//原记账子单元编号
                                .set("trxdat", tradeDate)//交易日期
                                .set("trxnbr", trxNo)//记账流水号
                )
        ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTHCLACT", body, keyPairReqVo);
    }

    /**
     * 11.单笔回单查询DCSIGREC
     *
     * @param accountNo
     * @param tradeDate
     * @param trxSeq
     * @return
     */
    public JSONObject getAccountReconFileUrlSync(String accountNo, String tradeDate, String trxSeq) {
        Dict body = Dict.create()
                .set("eacnbr", accountNo)
                .set("quedat", tradeDate)
                .set("trsseq", trxSeq);
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("DCSIGREC", body, keyPairReqVo);
    }

    /**
     * 1.16查询所有记账子单元的某日余额 NTDMAHBD
     *
     * @param bankBranch
     * @param accountNo
     * @param qrydat
     * @param dmanbr
     * @return
     */
    public JSONObject queryAllAccountBookBalanceHis(String bankBranch, String accountNo, String qrydat, String dmanbr) {
        Dict body = Dict.create().set("ntdmahbdx1", ListUtil.of(
                MapUtil.sort(
                        Dict.create().set("bbknbr", bankBranch)
                                .set("accnbr", accountNo)
                                .set("qrydat", qrydat)
                                .set("dmanbr", dmanbr)
                )
        ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMAHBD", body, keyPairReqVo);
    }

    /**
     * 5. 代发批次查询 BB6BTHQY
     *
     * @param batchNo
     * @param accountNo
     * @return
     */
    public JSONObject queryPayBatchOrder(String batchNo, String accountNo) {
        Dict body = Dict.create().set("bb6bthqyy1", ListUtil.of(
                MapUtil.sort(
                        Dict.create()
                                .set("buscod", "N03020")
                                .set("yurref", "ZHIXIANG" + batchNo)
                                .set("accnbr", accountNo)
                )
        ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("BB6BTHQY", body, keyPairReqVo);
    }


    /**
     * 企银支付单笔经办BB1PAYOP
     *
     * @return
     */
    public JSONObject pay2bApply(CmbPay2BVo cmbPay2BVo) {

        if (StringUtils.isBlank(cmbPay2BVo.getReceiveBankName())) {
            BankCardBin cardBinByCardNo = bankCardBinFacade.getCardBinByCardNo(cmbPay2BVo.getReceiveAccountNo());
            if (cardBinByCardNo == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("{}::{}", ApiExceptions.API_BIZ_FAIL.getApiErrorCode(), "卡bin校验失败"));
            }
            cmbPay2BVo.setReceiveBankName(cardBinByCardNo.getBankName());
        }

        final KeyPairRecord keyPairRecord = keyPairRecordBiz.getByChannelNoAndChannelMchNo("CMB", cmbPay2BVo.getAccountNo());

        Dict body = Dict.create().set("bb1paybmx1", ListUtil.of(
                MapUtil.sort(
                        Dict.create()
                                .set("busMod", keyPairRecord.getChannelPlatNo().split(":")[2].split("-")[1])
                                .set("busCod", "N02030")
                )
        )).set("bb1payopx1", ListUtil.of(
                MapUtil.sort(
                        Dict.create()
                                .set("dbtAcc", cmbPay2BVo.getAccountNo())//转出帐号
                                //.set("dmaNbr", cmbPay2BVo.getAccountBookNo())//记账子单元编号
                                .set("crtAcc", cmbPay2BVo.getReceiveAccountNo())//收方帐号
                                .set("crtNam", cmbPay2BVo.getReceiveAccountName())//收方户名
                                .set("crtBnk", cmbPay2BVo.getReceiveBankName())//收方开户行名称
                                .set("ccyNbr", "10")//币种
                                .set("trsAmt", cmbPay2BVo.getTransAmount())//交易金额
                                .set("stlChn", "Q")//结算通道
                                .set("brdNbr", cmbPay2BVo.getBankNo())
                                .set("nusAge", "余额提现")//用途
                                .set("yurRef", "ZHIXIANG" + cmbPay2BVo.getTrxNo())//业务参考号
                                .set("rcvChk", "1")//行内收方账号户名校验
                                .set("drpFlg", "B")//直汇普通标志
                )
        ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(cmbPay2BVo.getAccountNo());
        return cmbExchangeUtil.doRequest("BB1PAYOP", body, keyPairReqVo);
    }

    /**
     * 企银支付业务查询BB1PAYQR
     *
     * @return
     */
    public JSONObject pay2bQuery(String accountNo, String trxNo) {

        Dict body = Dict.create().set("bb1payqrx1", ListUtil.of(
                MapUtil.sort(
                        Dict.create()
                                .set("yurRef", "ZHIXIANG" + trxNo)
                                .set("busCod", "N02030")
                )
        ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("BB1PAYQR", body, keyPairReqVo);
    }

    /**
     * 1.5设置记账子单元关联付款方信息NTDMARLT
     *
     * @param accountNo
     * @param accountId
     * @param targetAccountName
     * @param bankDivNo
     * @return
     */
    public JSONObject incomeAccountSetting(String accountNo, String accountId, String targetAccountName, Integer bankDivNo) {
        final KeyPairRecord keyPairRecord = keyPairRecordBiz.getByChannelNoAndChannelMchNo("CMB", accountNo);

        Dict body = Dict.create()
                .set("ntbusmody", ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("busmod", keyPairRecord.getChannelPlatNo().split(":")[3].split("-")[1])
                        )
                ))
                .set("ntdmarltx2", ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("bbknbr", bankDivNo)
                                        .set("accnbr", accountNo)
                                        .set("dmanbr", accountId)
                                        .set("tlyopr", "R")
                                        .set("dbtnam", targetAccountName)
                                        .set("yurref", System.currentTimeMillis() + RandomUtil.getDigitStr(8))
                        )
                ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMARLT", body, keyPairReqVo);
    }

    public JSONObject incomeAccountModify(String accountNo, String accountId, String tlyopr, Integer bankDivNo) {
        final KeyPairRecord keyPairRecord = keyPairRecordBiz.getByChannelNoAndChannelMchNo("CMB", accountNo);

        Dict body = Dict.create()
                .set("ntbusmody", ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("busmod", keyPairRecord.getChannelPlatNo().split(":")[3].split("-")[1])
                        )
                ))
                .set("ntdmatmnx1", ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("bbknbr", bankDivNo)
                                        .set("accnbr", accountNo)
                                        .set("dmanbr", accountId)
                                        .set("tlyopr", tlyopr)
                                        .set("yurref", System.currentTimeMillis() + RandomUtil.getDigitStr(8))
                        )
                ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMATMN", body, keyPairReqVo);
    }

    /**
     * 1.7查询记账子单元关联付款方信息NTDMARLQ
     *
     * @param accountNo
     * @param accountId
     * @param bankDivNo
     * @return
     */
    public JSONObject incomeAccountQuery(String accountNo, String accountId, Integer bankDivNo) {

        Dict body = Dict.create()
                .set("ntdmarlqy1", ListUtil.of(
                        MapUtil.sort(
                                Dict.create()
                                        .set("bbknbr", bankDivNo)
                                        .set("accnbr", accountNo)
                                        .set("dmanbr", accountId)
                        )
                ));
        BaseKeyPairReqVo keyPairReqVo = new BaseKeyPairReqVo();
        keyPairReqVo.setChannelNo("CMB");
        keyPairReqVo.setChannelMchNo(accountNo);
        return cmbExchangeUtil.doRequest("NTDMARLQ", body, keyPairReqVo);
    }
}
