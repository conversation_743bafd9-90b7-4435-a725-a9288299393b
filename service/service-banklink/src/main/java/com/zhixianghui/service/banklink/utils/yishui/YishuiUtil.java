package com.zhixianghui.service.banklink.utils.yishui;

import cn.hutool.core.map.MapUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import org.apache.zookeeper.Login;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.crypto.KeyGenerator;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

public class YishuiUtil {

    public static String Login(){

        String userName = "汇聚智享";
        String password = "123456";
        String enterprise_sn = "E0000672";
        String random = RandomUtil.get16LenStr();
        String aeskey = RandomUtil.get32LenStr();


        Map<String, Object> param = new HashMap<>();
        param.put("user_name", userName);
        param.put("password", password);
        param.put("enterprise_sn", enterprise_sn);
        param.put("random", random);
        param.put("aeskey", aeskey);

        String s = "user_name="+userName+"&password="+password+"&enterprise_sn="+enterprise_sn+"&random="+random+"&aeskey="+aeskey;
        System.out.println(s);
        String sign = DigestUtil.md5Hex(s);
        System.out.println(sign);
        param.put("sign", sign);

        System.out.println(JSON.toJSONString(param));
        WebClient webClient = WebClient.create("http://testshuichou.zhuoyankeji.com/sdk/login");
        Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromObject(JSON.toJSONString(param))).retrieve().bodyToMono(JSONObject.class);
        JSONObject response = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));

        System.out.println(JSON.toJSONString(response));
        System.out.println(response.getString("code"));
        System.out.println(response.getString("token"));

        return response.getString("token");
    }

    public static void main(String[] args) {
        YishuiUtil.Login();
    }
}
