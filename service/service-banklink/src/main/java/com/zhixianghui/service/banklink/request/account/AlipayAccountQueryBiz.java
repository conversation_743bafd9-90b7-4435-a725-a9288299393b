package com.zhixianghui.service.banklink.request.account;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.service.banklink.request.alipay.AliPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AlipayAccountQueryBiz {

    final private AliPayService aliPayService;

    /**
     * 查询记账本余额
     * @param agreementno
     * @param acctBookId
     * @return
     */
    public String queryBalance(String agreementno,String acctBookId) {
        try {
            String data = aliPayService.accountBookQuery(agreementno, acctBookId);

            JSONObject jsonData = JSON.parseObject(data).getJSONObject("alipay_fund_accountbook_query_response");
            String balanceValue = jsonData.getString("available_amount");
            return balanceValue;
        } catch (AlipayApiException e) {
            log.error("记账本查询出错", e);
            throw CommonExceptions.UNEXPECT_ERROR.newWith("记账本查询出错", e);
        }
    }

}
