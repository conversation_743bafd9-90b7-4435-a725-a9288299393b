/*
 * Powered By [joinPay.com]
 */
package com.zhixianghui.service.banklink.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.banklink.entity.MailReceiver;
import org.springframework.stereotype.Repository;
import java.util.HashMap;
import java.util.Map;

@Repository
public class MailReceiverDao extends MyBatisDao<MailReceiver, Long>{

    public MailReceiver getByGroupKey(String groupKey){
        Map<String, Object> map = new HashMap<>();
        map.put("groupKey", groupKey);
        return getOne(map);
    }
}
