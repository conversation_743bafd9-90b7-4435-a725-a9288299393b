package com.zhixianghui.service.banklink.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName HemaConfig
 * @Description TODO
 * @Date 2023/9/27 11:28
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "hema")
public class HemaConfig {

    private String appKey;

    private String secretKey;

    private String publicKey;

    private String queryInvoiceUrl;

    private String paymentUrl;

    private String payCallBackUrl;
}
