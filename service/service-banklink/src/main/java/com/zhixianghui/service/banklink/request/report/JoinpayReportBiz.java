package com.zhixianghui.service.banklink.request.report;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.bankLink.report.ApiReportStatusEnum;
import com.zhixianghui.common.statics.enums.common.JoinpaySignType;
import com.zhixianghui.common.statics.enums.merchant.AllocateMerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.*;
import com.zhixianghui.common.statics.exception.BanklinkExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.vo.report.*;
import com.zhixianghui.service.banklink.annotation.CurrentKeyPair;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import com.zhixianghui.service.banklink.utils.ImageUtil;
import com.zhixianghui.service.banklink.utils.PdfUConvertUtil;
import com.zhixianghui.service.banklink.utils.joinpay.SignUtil;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.*;

/**
 * <AUTHOR>
 * @description 汇聚报备biz
 * @date 2020-10-30 10:47
 **/
@Service
@Slf4j
public class JoinpayReportBiz {
    @Value("${report.joinpayAllocFundsUrl}")
    private String joinpayAllocFundsUrl;
    @Value("${report.joinpayAllocFundsNotifyUrl}")
    private String joinpayAllocFundsNotifyUrl;
    @Value("${report.joinpayAllocFundsModifyNotify}")
    private String joinpayAllocFundsModifyNotify;
    @Value("${report.uploadAllocFundsUrl}")
    private String uploadAllocFundsUrl;
    @Value("${report.joinpaySimpleNotify}")
    private String simpleNotify;

    /**
     * 汇聚响应码 对应 resp_code, 受理成功、失败、未知
     */
    private static final String RESP_SUCCESS = "A1000";
    private static final String RESP_FAIL = "A2000";
    private static final String RESP_UN_KNOW = "A3000";

    /**
     * 汇聚报备 分账方认证状态 未认证、认证中、认证成功、认证失败
     */
    private static final String NO_AUTH = "R4001";
    private static final String AUTHING = "R4002";
    private static final String AUTH_FAIL = "R4003";
    private static final String AUTH_SUCCESS = "R4004";

    private static long _2MB = 2097152L;

    @Autowired
    private FastdfsClient fastdfsClient;

    @CurrentKeyPair
    public ReportResVo report(ReportReqVo reportReqVo) {
        TreeMap<String, Object> map = fillQueryReportParam(reportReqVo);
        // 请求
        log.info("[{}] 请求汇聚分账方入网接口参数：{}", reportReqVo.getChannelMchNo(), JsonUtil.toString(map));
        JSONObject respObject;
        try{
           WebClient webClient = WebClient.create(joinpayAllocFundsUrl);
           Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                   .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
           respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        }catch (Exception e){
           log.info("[{}] 请求汇聚分账方入网接口异常：",reportReqVo.getChannelMchNo(), e);
           ReportResVo respVo = new ReportResVo();
           respVo.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
           return respVo;
        }
        log.info("[{}] 请求汇聚分账方入网接口返回原始结果：{}", reportReqVo.getChannelMchNo(), JsonUtil.toString(respObject));
        return fillReportRespVo(respObject);
    }

    /**
     * 汇聚响应转换报备统一对象
     * @param reportResult 汇聚响应
     * @return 报备统一对象
     */
    private ReportResVo fillReportRespVo(JSONObject reportResult) {
        ReportResVo respVo = new ReportResVo();
        if (reportResult == null) {
            respVo.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
            return respVo;
        }

        String respCode = reportResult.getString("resp_code");
        if(respCode != null ){
            JSONObject responseData = reportResult.getJSONObject("data");
            switch(respCode){
                case RESP_SUCCESS:
                    respVo.setApiReportStatus(ApiReportStatusEnum.PROCESS.getValue());
                    respVo.setBizCode(responseData.getString("biz_code"));
                    respVo.setBizMsg(responseData.getString("biz_msg"));
                    respVo.setMchNo(responseData.getString("alt_mch_no"));
                    break;
                case RESP_FAIL:
                    respVo.setApiReportStatus(ApiReportStatusEnum.FAIL.getValue());
                    respVo.setBizCode(responseData.getString("biz_code"));
                    respVo.setBizMsg(responseData.getString("biz_msg"));
                    break;
                case RESP_UN_KNOW:
                    respVo.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
                    respVo.setBizCode(responseData.getString("biz_code"));
                    respVo.setBizMsg(responseData.getString("biz_msg"));
                    break;
                default:
                    respVo.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
            }
        }else{
            respVo.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
        }
        return respVo;
    }

    /**
     * 组装报备请求map
     * @param reportReqVo 待组装参数
     * @return 组装好的请求map
     */
    private TreeMap<String, Object> fillQueryReportParam(ReportReqVo reportReqVo) {
        //组装参数实体
        Map<String, Object> data = Maps.newHashMap();
        data.put("login_name", reportReqVo.getLoginName());
        data.put("alt_mch_name", reportReqVo.getMchName());
        data.put("alt_mch_short_name", reportReqVo.getShortName());
        data.put("alt_merchant_type", AllocateMerchantTypeEnum.ENTERPRISE.getValue());
        data.put("busi_contact_name", reportReqVo.getContactName());
        data.put("busi_contact_mobile_no", reportReqVo.getContactPhone());
        data.put("phone_no", reportReqVo.getContactPhone());
        data.put("manage_scope", reportReqVo.getManagementScope());
        data.put("manage_addr", reportReqVo.getManagementAddrDetail());
        data.put("legal_person", reportReqVo.getLegalPersonName());
        data.put("id_card_no", reportReqVo.getCertificateNumber());
        data.put("id_card_expiry", reportReqVo.getCertificateTermEnd());
        data.put("license_no", reportReqVo.getTaxNo());
        data.put("license_expiry", reportReqVo.getManagementTermEnd());
        data.put("sett_mode", SettModeEnum.MANUAL.getValue());
        data.put("sett_date_type", SettDateTypeEnum.NATURAL_DAY.getValue());
        data.put("risk_day", 1);
        data.put("bank_account_type", BankAccountTypeEnum.PUBLIC.getValue());
        data.put("bank_account_name", reportReqVo.getAccountName());
        data.put("bank_account_no", reportReqVo.getAccountNo());
        data.put("bank_channel_no", reportReqVo.getBankChannelNo());
        data.put("notify_url", joinpayAllocFundsNotifyUrl);

        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altmch.create");
        map.put("version", "1.1");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", reportReqVo.getChannelMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map, reportReqVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    /**
     * 报备回调
     * @param reqVo 回调信息
     * @return 组装返回实体
     */
    @CurrentKeyPair
    public ReportReceiveRespVo verifyAndHandleResult(ReportReceiveReqVo reqVo) {
        log.info("报备回调返回报文:{}",JSON.toJSONString(reqVo.getRespContent()));
        TreeMap<String, Object> resultMap = JsonUtil.toBean(JsonUtil.toString(reqVo.getRespContent()), new TypeReference<TreeMap<String, Object>>(){});
        String sign = resultMap.get("sign").toString();
        boolean isVerifyPass = SignUtil.verify_2_0(resultMap, reqVo.getKeyPairRecord().getMchPrivateKeyDecrypt(), sign);
        if(!isVerifyPass){
            log.error("汇聚分账方报备回调验签失败，报文：{}", JsonUtil.toString(reqVo.getRespContent()));
            throw BanklinkExceptions.VERIFY_SIGN_FAIL.newWithErrMsg("验证签名失败");
        }
        return fillReportReceiveRespVo(reqVo.getRespContent());
    }


    private ReportReceiveRespVo fillReportReceiveRespVo(JSONObject respContent) {
        ReportReceiveRespVo respVo = new ReportReceiveRespVo();
        JSONObject dataObj = respContent.getJSONObject("data");
        String parentMchNo = respContent.getString("mch_no");
        if (dataObj != null) {
            String authStatus = dataObj.getString("auth_status");
            if(AUTH_SUCCESS.equals(authStatus)){
                respVo.setApiReportStatus(ApiReportStatusEnum.SUCCESS.getValue());
            }else if(AUTH_FAIL.equals(authStatus)){
                respVo.setApiReportStatus(ApiReportStatusEnum.FAIL.getValue());
            }else if (AUTHING.equals(authStatus) || NO_AUTH.equals(authStatus)){
                respVo.setApiReportStatus(ApiReportStatusEnum.PROCESS.getValue());
            }
            respVo.setBizCode(dataObj.getString("biz_code"));
            respVo.setBizMsg(dataObj.getString("biz_msg"));
            respVo.setMchNo(dataObj.getString("alt_mch_no"));
            respVo.setParentMchNo(parentMchNo);
        } else {
            respVo.setBizCode(respContent.getString("resp_code"));
            respVo.setBizMsg(respContent.getString("resp_msg"));
        }
        // 返回系统异常时未知实际处理结果
        if (respVo.getApiReportStatus() == null) {
            respVo.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
        }
        return respVo;
    }


    @CurrentKeyPair
    public void altMchSign(AltMchSignVo signVo){
        TreeMap<String, Object> map = fillQueryChangeParam(signVo);
        // 请求
        JSONObject respObject;
        try{
            WebClient webClient = WebClient.create(uploadAllocFundsUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
            System.out.println(respObject.toJSONString());
            log.info("汇聚返回：[{}]",respObject.toJSONString());
        }catch (Exception e){
            log.info("协议页面签约接口异常：", e);
            e.printStackTrace();
        }
    }

    @CurrentKeyPair
    public Map<String, String> queryAltMchSignRecord(AltMchSignVo signVo){
        TreeMap<String, Object> map = fillQuerySignRecordParam(signVo);
        // 请求
        Map<String, String> respMap = new HashMap<>();
        JSONObject respObject;
        try{
            WebClient webClient = WebClient.create(uploadAllocFundsUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
            log.info("汇聚返回：[{}]",respObject.toJSONString());

            String respCode = respObject.getString("resp_code");
            if (respCode != null && StringUtils.equals(respCode, RESP_SUCCESS)) {
                JSONObject data = respObject.getJSONObject("data");
                if (StringUtils.equals(data.getString("sign_status"), "P1000")) {
                    respMap.put("altMchSignStatus", "100");
                    respMap.put("altMchSignTrxno", data.getString("sign_trx_no"));
                    respMap.put("errorMsg", "");
                    return respMap;
                }else {
                    respMap.put("altMchSignStatus", "101");
                    respMap.put("altMchSignTrxno", data.getString("sign_trx_no"));
                    respMap.put("errorMsg", data.getString("biz_msg"));
                    return respMap;
                }
            }else {
                respMap.put("altMchSignStatus", "101");
                respMap.put("altMchSignTrxno", "");
                respMap.put("errorMsg", respObject.getString("resp_msg"));
                return respMap;
            }
        }catch (Exception e){
            log.info("协议签约查询接口异常：", e);
            respMap.put("altMchSignStatus", "101");
            respMap.put("altMchSignTrxno", "");
            respMap.put("errorMsg","查询系统异常："+ e.getMessage());
            return respMap;
        }
    }

    private TreeMap<String, Object> fillQueryChangeParam(AltMchSignVo signVo) {
        Map<String,Object> data = new HashMap<>();
        data.put("alt_mch_no",signVo.getAltMchNo());
        data.put("sign_status","P1000");
        data.put("sign_time", DateUtil.formatDateTime(new Date()));
        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altMchSign.sign");
        map.put("version", "1.1");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", signVo.getChannelMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map, signVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    private TreeMap<String, Object> fillQuerySignRecordParam(AltMchSignVo signVo) {
        Map<String,Object> data = new HashMap<>();
        data.put("alt_mch_no",signVo.getAltMchNo());

        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altMchSign.querySignRecord");
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", signVo.getChannelMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map, signVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    private TreeMap<String, Object> fillQuerySignPicParam(AltMchSignVo signVo) {
        Map<String,Object> data = new HashMap<>();
        data.put("alt_mch_no",signVo.getAltMchNo());

        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altMchPics.queryPicsInfo");
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", signVo.getChannelMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map, signVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    /**
     * 修改汇聚方商户信息
     * @param reportReqVo
     * @return
     */
    @CurrentKeyPair
    public ReportResVo modify(ReportReqVo reportReqVo) {
        TreeMap<String, Object> map = fillModifyParam(reportReqVo);
        // 请求
        log.info("[{}-{}] 请求汇聚分账方信息修改接口参数：{}", reportReqVo.getChannelMchNo(), reportReqVo.getSubMerchantNo(),JsonUtil.toString(map));
        JSONObject respObject;
        try{
            WebClient webClient = WebClient.create(joinpayAllocFundsUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        }catch (Exception e){
            log.info("[{}-{}] 请求汇聚分账方信息修改接口异常：",reportReqVo.getChannelMchNo(),reportReqVo.getSubMerchantNo(), e);
            ReportResVo respVo = new ReportResVo();
            respVo.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}-{}] 请求汇聚分账方信息修改接口返回原始结果：{}", reportReqVo.getChannelMchNo(),reportReqVo.getSubMerchantNo(),JsonUtil.toString(respObject));
        return fillReportRespVo(respObject);
    }

    private TreeMap<String, Object> fillModifyParam(ReportReqVo reportReqVo) {
        Map<String,Object> data = new HashMap<>();
        data.put("alt_mch_no",reportReqVo.getSubMerchantNo());
        data.put("alt_mch_name",reportReqVo.getMchName());
        data.put("busi_contact_mobile_no", reportReqVo.getContactPhone());
        data.put("legal_person",reportReqVo.getLegalPersonName());
        data.put("id_card_no",reportReqVo.getCertificateNumber());
        data.put("id_card_expiry", reportReqVo.getCertificateTermEnd());
        data.put("license_no", reportReqVo.getTaxNo());
        data.put("license_expiry", reportReqVo.getManagementTermEnd());
        data.put("bank_account_type", BankAccountTypeEnum.PUBLIC.getValue());
        data.put("bank_account_name", reportReqVo.getAccountName());
        data.put("bank_account_no", reportReqVo.getAccountNo());
        data.put("bank_channel_no", reportReqVo.getBankChannelNo());
        data.put("notify_url", joinpayAllocFundsModifyNotify);
        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altmch.modify");
        map.put("version", "1.1");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", reportReqVo.getChannelMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map, reportReqVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    @Data
    public class PicEntity{
        private String other1;
        private String other2;
        private String idcard;
        private String license;
        private String account;
    }

    @CurrentKeyPair
    public void uploadPic(UploadPicReqVo uploadPicReqVo) throws IOException {
        TreeMap<String, Object> map = fillUploadPicParam(uploadPicReqVo);
        // 请求
        JSONObject respObject;
        try{
            WebClient webClient = WebClient.create(uploadAllocFundsUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(20000));
            log.info("商户号：[{}],供应商编号：[{}]，汇聚分账方图片上传接口返回：[{}]",
                    uploadPicReqVo.getEmployerNo(),uploadPicReqVo.getMainstayNo(),respObject.toJSONString());
        }catch (Exception e){
            log.info("请求汇聚分账方图片上传接口异常：", e);
            e.printStackTrace();
        }
    }


    @CurrentKeyPair
    public void modifyPic(UploadPicReqVo uploadPicReqVo) throws IOException {
        TreeMap<String, Object> map = fillModifyPicParam(uploadPicReqVo);
        // 请求
        JSONObject respObject;
        try{
            WebClient webClient = WebClient.create(uploadAllocFundsUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(20000));
            log.info("商户号：[{}],供应商编号：[{}]，汇聚分账方图片上传接口返回：[{}]",
                    uploadPicReqVo.getEmployerNo(),uploadPicReqVo.getMainstayNo(),respObject.toJSONString());
        }catch (Exception e){
            log.info("请求汇聚分账方图片上传接口异常：", e);
            e.printStackTrace();
        }
    }

    private TreeMap<String, Object> fillUploadPicParam(UploadPicReqVo uploadPicReqVo) throws IOException {
        Map<String,Object> data = new HashMap<>();
        data.put("alt_mch_no",uploadPicReqVo.getSubMerchantNo());
        BASE64Encoder encoder = new BASE64Encoder();

        //获取文件扩展名
        String idcardFrontExt = getFileExt(uploadPicReqVo.getIdCardFront());
        String idcardBackExt = getFileExt(uploadPicReqVo.getIdCardBack());
        String licenseExt = getFileExt(uploadPicReqVo.getLicense());
        String other1Ext = getFileExt(uploadPicReqVo.getOther1());
        String other2Ext = getFileExt(uploadPicReqVo.getOther2());

        //获取base64
        String idcardFrontBase64 = getPicBase64(uploadPicReqVo.getIdCardFront(),encoder);
        String idcardBackBase64 = getPicBase64(uploadPicReqVo.getIdCardBack(),encoder);
        String licenseBase64 = getPicBase64(uploadPicReqVo.getLicense(),encoder);
        String other1Base64 = getPicBase64(uploadPicReqVo.getOther1(),encoder);
        String other2Base64 = getPicBase64(uploadPicReqVo.getOther2(),encoder);


        data.put("card_positive", "data:image/" + idcardFrontExt + ";base64," + idcardFrontBase64);
        data.put("card_negative","data:image/" + idcardBackExt + ";base64," + idcardBackBase64);
        data.put("open_account_licence","data:image/" + licenseExt + ";base64," + licenseBase64);
        data.put("trade_licence","data:image/" + licenseExt + ";base64," + licenseBase64);
        data.put("other_one","data:image/" + other1Ext + ";base64," + other1Base64);
        data.put("other_two","data:image/" + other2Ext + ";base64," + other2Base64);
        data.put("notify_url",simpleNotify);
        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altMchPics.uploadPic");
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", uploadPicReqVo.getChannelMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map, uploadPicReqVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    private TreeMap<String, Object> fillModifyPicParam(UploadPicReqVo uploadPicReqVo) throws IOException {
        Map<String,Object> data = new HashMap<>();
        data.put("alt_mch_no",uploadPicReqVo.getSubMerchantNo());
        BASE64Encoder encoder = new BASE64Encoder();

        //获取文件扩展名
        String idcardFrontExt = getFileExt(uploadPicReqVo.getIdCardFront());
        String idcardBackExt = getFileExt(uploadPicReqVo.getIdCardBack());
        String licenseExt = getFileExt(uploadPicReqVo.getLicense());
        String other1Ext = getFileExt(uploadPicReqVo.getOther1());
        String other2Ext = getFileExt(uploadPicReqVo.getOther2());

        //获取base64
        String idcardFrontBase64 = getPicBase64(uploadPicReqVo.getIdCardFront(),encoder);
        String idcardBackBase64 = getPicBase64(uploadPicReqVo.getIdCardBack(),encoder);
        String licenseBase64 = getPicBase64(uploadPicReqVo.getLicense(),encoder);
        String other1Base64 = getPicBase64(uploadPicReqVo.getOther1(),encoder);
        String other2Base64 = getPicBase64(uploadPicReqVo.getOther2(),encoder);


        data.put("card_positive", "data:image/" + idcardFrontExt + ";base64," + idcardFrontBase64);
        data.put("card_negative","data:image/" + idcardBackExt + ";base64," + idcardBackBase64);
        data.put("open_account_licence","data:image/" + licenseExt + ";base64," + licenseBase64);
        data.put("trade_licence","data:image/" + licenseExt + ";base64," + licenseBase64);
        data.put("other_one","data:image/" + other1Ext + ";base64," + other1Base64);
        data.put("other_two","data:image/" + other2Ext + ";base64," + other2Base64);
        data.put("notify_url",simpleNotify);
        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altMchPics.modifyPic");
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", uploadPicReqVo.getChannelMchNo());
        map.put("data", JSON.toJSON(data));
        map.put("sign", SignUtil.genSign_2_0(map, uploadPicReqVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    @CurrentKeyPair
    public Map<String, String> queryPicsInfo(AltMchSignVo altMchSignVo) {
        TreeMap<String, Object> map = fillQuerySignPicParam(altMchSignVo);
        // 请求
        JSONObject respObject;
        Map<String, String> respMap = new HashMap<>();
        try{
            WebClient webClient = WebClient.create(uploadAllocFundsUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(20000));
            String respCode = respObject.getString("resp_code");
            log.info("汇聚返回：[{}]",respObject.toJSONString());
            if (respCode != null && StringUtils.equals(respCode, RESP_SUCCESS)) {
                JSONObject data = respObject.getJSONObject("data");
                String approveStatus = data.getString("approve_status");
                respMap.put("approveStatus", approveStatus);
                respMap.put("approveNote", data.getString("approve_note"));
                respMap.put("errorMsg", data.getString("biz_msg"));

                if (StringUtils.equals(approveStatus,"P1000")){
                    respMap.put("approveStatusDesc", "审批通过");
                }else if (StringUtils.equals(approveStatus,"P0001")){
                    respMap.put("approveStatusDesc", "未审批");
                }else {
                    respMap.put("approveStatusDesc", "审批不通过");
                }

                return respMap;
            }else {
                JSONObject data = respObject.getJSONObject("data");
                respMap.put("approveStatus", "");
                respMap.put("approveNote", "");
                if (data != null) {
                    respMap.put("errorMsg", data.getString("biz_msg"));
                }else {
                    respMap.put("errorMsg", respObject.getString("resp_msg"));
                }
                return respMap;
            }
        }catch (Exception e){
            log.info("请求汇聚分账方图片查询接口异常：", e);
            log.info("协议签约查询接口异常：", e);
            respMap.put("approveStatus", "");
            respMap.put("approveNote", "");
            respMap.put("errorMsg","查询系统异常："+ e.getMessage());
            return respMap;
        }
    }

    /**
     * 获取文件扩展名
     * @param filePath
     * @return
     */
    private String getFileExt(String filePath) {
        String ext = FilenameUtils.getExtension(filePath);
        if (ext.equals("pdf")){
            ext = "png";
        }
        return ext;
    }

    /**
     * 下载文件并转换
     * @param picUrl
     * @return
     */
    private String getPicBase64(String picUrl,BASE64Encoder encoder) throws IOException {
        InputStream inputStream = fastdfsClient.downloadFile(picUrl);
        String ext = FilenameUtils.getExtension(picUrl);
        byte[] picArray;
        if (ext.equals("pdf")){
            picArray = PdfUConvertUtil.convertToPNG(inputStream);
            ext = "png";
        }else{
            picArray = IoUtil.readBytes(inputStream);
        }

        //压缩图片
        while (picArray.length > _2MB){
            picArray = ImageUtil.decompressImageByte(picArray,ext);
        }

        return encoder.encode(picArray);
    }

}
