package com.zhixianghui.service.banklink.request.sign;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.*;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.facade.banklink.vo.sign.signAuth.SignAuthReqVo;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 * @description e签宝Biz
 * @date 2021-01-05 15:10
 **/
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EsignBiz {
    @Value("${sign.esignGateway}")
    private String host;
    @Value("${sign.esignProjectId}")
    private String projectId;
    @Value("${sign.esignProjectSecret}")
    private String projectSecret;

    private String revokeUrl = "/v1/signflows/{flowId}/revoke";
    private String createPersonUrl = "/v1/accounts/createByThirdPartyUserId";
    private String createOrganizationUrl = "/v1/organizations/createByThirdPartyUserId";
    private String getSignTemplateUrl = "/v1/docTemplates";
    private String signAuthUrl = "/v1/signAuth";
    private String createFileByTemplateUrl="/v1/files/createByTemplate";
    private String createFlowOneStepUrl="/api/v2/signflows/createFlowOneStep";
    private String getExecuteUrl="/v1/signflows/{flowId}/executeUrl";
    private String getSignFlowUrl="/v1/signflows";
    private String getDocumentDownloadUrl="/v1/signflows/{flowId}/documents";
    private String updatePersonInfoUrl="/v1/accounts/{accountId}";
    private String createByUploadUrl="/v1/docTemplates/createByUploadUrl";
    private String componentUploadUrl="/v1/docTemplates/{templateId}/components";
    private String deleteComponentUrl="/v1/docTemplates/createByUploadUrl";
    private String getTemplateStatusUrl="/v1/docTemplates/{templateId}/getBaseInfo";
    private String getKeywordPositionsUrl="/v3/files/{fileId}/keyword-positions";
    private String getPersonalSignatureUrl = "/v1/accounts/{accountId}/seals/personaltemplate";
    private String getPersonalAccountUrl = "/v1/accounts/getByThirdId";
    private String getPersonalSealListUrl = "/v1/accounts/{accountId}/seals";
    private String getTemplateInfo = "/v1/docTemplates/{templateId}";
    private String getAllTemplateInfo = "/v3/flow-templates/basic-info";
    private String delayFlowUrl = "/v3/sign-flow/{signFlowId}/delay";
    private String getUploadUrl = "/v1/files/getUploadUrl";
    private final FastdfsClient fastdfsClient;

    public EsignResVo getUploadUrl(CreateFileUploadUrlVo createFileUploadUrlVo){
        log.error("请求获取文件上传链接地址接口，请求参数：[{}]",JsonUtil.toString(createFileUploadUrlVo));
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(createFileUploadUrlVo, HttpMethod.POST.name(),
                    createFileUploadUrlVo.buildFullUrl(getUploadUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.error("请求获取文件上传链接地址接口异常",e);
            EsignResVo<CreateFileUploadUrlResVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        EsignResVo<CreateFileUploadUrlResVo> resVo = new EsignResVo<CreateFileUploadUrlResVo>().
                fillEsignResVo(respObject, CreateFileUploadUrlResVo.class);
        return resVo;
    }

    /**
     * 流程延期接口
     * @param delayFlowReqVo
     * @return
     */
    public EsignResVo delayFlow(DelayFlowReqVo delayFlowReqVo) {
        log.info("[{}] 请求e签宝流程延期接口", delayFlowReqVo.getSignFlowId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(delayFlowReqVo, HttpMethod.POST.name(),
                    delayFlowReqVo.buildFullUrl(delayFlowUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝延期接口异常：", delayFlowReqVo.getSignFlowId(), e);
            EsignResVo respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            respVo.setMessage("接口异常");
            return respVo;
        }
        log.info("[{}] 请求e签宝流程延期接口原始结果：{}", delayFlowReqVo.getSignFlowId(), JsonUtil.toString(respObject));
        EsignResVo resVo = respObject.toJavaObject(EsignResVo.class);
        return resVo;
    }

    /**
     * 撤回流程接口
     * @param revokeFlowVo
     * @return
     */
    public EsignResVo revokeFlow(RevokeFlowVo revokeFlowVo) {
        log.info("[{}] 请求e签宝撤回流程接口", revokeFlowVo.getFlowId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(revokeFlowVo, HttpMethod.PUT.name(),
                    revokeFlowVo.buildFullUrl(revokeUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝撤回流程接口异常：", revokeFlowVo.getFlowId(), e);
            EsignResVo respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝撤回流程接口返回原始结果：{}", revokeFlowVo.getFlowId(), JsonUtil.toString(respObject));
        EsignResVo resVo = respObject.toJavaObject(EsignResVo.class);
        return resVo;
    }

    /**
     * 创建个人签章
     * @param reqVo 请求参数
     * @return
     */
    public EsignResVo<GetPersonalSignatureResDataVo> personalSignature(PersonalSignatureReqVo reqVo) {
        log.info("[{}] 创建个人签章", JSONObject.toJSON(reqVo));
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(reqVo, HttpMethod.POST.name(),
                    reqVo.buildFullUrl(getPersonalSignatureUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 创建个人签章请求异常：", JSONObject.toJSON(respObject), e);
            EsignResVo<GetPersonalSignatureResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求创建个人签章：{}", reqVo.getPsnId(), JsonUtil.toString(respObject));
        EsignResVo<GetPersonalSignatureResDataVo> resVo = new EsignResVo<GetPersonalSignatureResDataVo>().
                fillEsignResVo(respObject, GetPersonalSignatureResDataVo.class);
        log.info("请求创建个人签章返回封装Vo：{}", JSONObject.toJSON(resVo));
        return resVo;
    }

    public EsignResVo<PersonalSealListResVo> personalSealList(PersonalSealListReqVo reqVo) {
        log.info("[{}] 查询个人签章", JSONObject.toJSON(reqVo));
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(reqVo, HttpMethod.GET.name(),
                    reqVo.buildFullUrl(getPersonalSealListUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 查询个人签章请求异常：", JSONObject.toJSON(respObject), e);
            EsignResVo<PersonalSealListResVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求查询个人签章：{}", reqVo.getAccountId(), JsonUtil.toString(respObject));
        EsignResVo<PersonalSealListResVo> resVo = new EsignResVo<PersonalSealListResVo>().
                fillEsignResVo(respObject, PersonalSealListResVo.class);
        log.info("请求查询个人签章返回封装Vo：{}", JSONObject.toJSON(resVo));
        return resVo;
    }

    /**
     * 查询模板状态
     * @param reqVo 请求参数
     */
    public EsignResVo<TemplateStatusResDataVo> existTemplate(TemplateStatus reqVo) {
        log.info("[{}] 查询模板状态", reqVo.getTemplateId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(reqVo, HttpMethod.GET.name(),
                    reqVo.buildFullUrl(getTemplateStatusUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 查询模板状态·：", JSONObject.toJSON(respObject), e);
            EsignResVo<TemplateStatusResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 查询模板状态：{}", reqVo.getTemplateId(), JsonUtil.toString(respObject));
        EsignResVo<TemplateStatusResDataVo> resVo = new EsignResVo<TemplateStatusResDataVo>().
                fillEsignResVo(respObject, TemplateStatusResDataVo.class);
        log.info("[{}] 查询模板状态返回VO：{}", reqVo.getTemplateId(), respObject.toJSONString());
        return resVo;
    }

    public EsignResVo<?> del(DeleteComponentsReqVo deleteComponentsReqVo) {
        log.info("[{}] 请求e签宝删除组件", JSONObject.toJSON(deleteComponentsReqVo));
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(deleteComponentsReqVo, HttpMethod.DELETE.name(),
                    deleteComponentsReqVo.buildFullUrl(deleteComponentUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝删除组件异常：", JSONObject.toJSON(respObject), e);
            EsignResVo<?> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝删除组件返回封装Vo：{}", deleteComponentsReqVo.getTemplateId(), JsonUtil.toString(respObject));
        EsignResVo<?> respVo = JSONObject.toJavaObject(respObject, EsignResVo.class);
        log.info("[{}] 请求e签宝删除组件返回封装Vo：{}", deleteComponentsReqVo.getTemplateId(), respObject.toJSONString());
        return respVo;
    }

    public EsignResVo<List<String>> addTemplate(TemplateComponentsReqVo componentsReqVo) {
        log.info("[{}] 请求e签宝上传组件", JSONObject.toJSON(componentsReqVo));
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(componentsReqVo, HttpMethod.POST.name(),
                    componentsReqVo.buildFullUrl(componentUploadUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝上传组件：", JSONObject.toJSON(respObject), e);
            EsignResVo<List<String>> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝上传组件：{}", componentsReqVo.getTemplateId(), JsonUtil.toString(respObject));
        EsignResVo<List<String>> respVo = new EsignResVo<>();
        respVo.setCode((Integer) respObject.get("code"));
        respVo.setMessage((String) respObject.get("message"));
        if (respVo.getCode() == 0) {
            respVo.setRespStatus(SignChannelStatusEnum.SUCCESS.getValue());
        }
        log.info("id:" + respObject.getJSONArray("data"));
        respVo.setData(respObject.getJSONArray("data").toJavaList(String.class));
        log.info("[{}] 请求e签宝上传组件返回封装Vo：{}", componentsReqVo.getTemplateId(), respObject.toJSONString());
        return respVo;
    }

    public boolean uploadFile(UploadFileReqVo uploadFileReqVo, String uploadUrl, byte[] buffer) {
        log.info("[{}-{}] 请求e签宝上传文件", JSONObject.toJSON(uploadFileReqVo), uploadUrl);
        JSONObject respObject = null;
        try {
            // 发请求
            return uploadRequest(uploadFileReqVo, uploadUrl, Math.toIntExact(RequestConstant.REQUEST_TIMEOUT * 20), buffer);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝上传文件接口异常：", JSONObject.toJSON(respObject), e);
            return false;
        }
    }

    /**
     *
     * @param createSignTemplateReqVo 上传方式创建模板reqVo
     * @return 返回通过上传方式创建模板的结果
     */
    public EsignResVo<CreateSignTemplateResDataVo> createSignTemplate(CreateSignTemplateReqVo createSignTemplateReqVo) {
        log.info("[{}] 请求e签宝通过上传方式创建模板", createSignTemplateReqVo.getFileName());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(createSignTemplateReqVo, HttpMethod.POST.name(),
                    createSignTemplateReqVo.buildFullUrl(createByUploadUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝通过上传方式创建模板接口异常：", createSignTemplateReqVo.getFileName(), e);
            EsignResVo<CreateSignTemplateResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝通过上传方式创建模板返回原始结果：{}", createSignTemplateReqVo.getFileName(), JsonUtil.toString(respObject));
        EsignResVo<CreateSignTemplateResDataVo> resVo = new EsignResVo<CreateSignTemplateResDataVo>().
                fillEsignResVo(respObject, CreateSignTemplateResDataVo.class);
        log.info("[{}] 请求e签宝通过上传方式创建模板返回封装Vo：{}", createSignTemplateReqVo.getFileName(), JsonUtil.toString(resVo));
        return resVo;
    }


    /**
     * 个人账户创建
     *
     * @param signCreatePersonReqVo 个人账户创建reqVo
     * @return 个人账户创建结果
     */
    public EsignResVo<SignCreatePersonResDataVo> createPersonByThirdPartyUserId(SignCreatePersonReqVo signCreatePersonReqVo) {
        log.info("[{}] 请求e签宝个人帐号创建接口", signCreatePersonReqVo.getThirdPartyUserId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(signCreatePersonReqVo, HttpMethod.POST.name(),
                    signCreatePersonReqVo.buildFullUrl(createPersonUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝个人帐号创建接口异常：", signCreatePersonReqVo.getThirdPartyUserId(), e);
            EsignResVo<SignCreatePersonResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝个人帐号创建接口返回原始结果：{}", signCreatePersonReqVo.getThirdPartyUserId(), JsonUtil.toString(respObject));
        EsignResVo<SignCreatePersonResDataVo> resVo = new EsignResVo<SignCreatePersonResDataVo>()
                .fillEsignResVo(respObject, SignCreatePersonResDataVo.class);
        log.info("[{}] 请求e签宝个人帐号创建接口返回封装Vo：{}", signCreatePersonReqVo.getThirdPartyUserId(), JsonUtil.toString(resVo));
        return resVo;
    }

    public EsignResVo<PersonalAccountQueryResVo> getPersonalAccountByThirdId(String thirdPartyUserId) {
        log.info("[{}] 查询e签宝个人帐号接口", thirdPartyUserId);
        PersonalAccountQueryReqVo reqVo = new PersonalAccountQueryReqVo(thirdPartyUserId);
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(reqVo, HttpMethod.GET.name(),
                    reqVo.buildFullUrl(getPersonalAccountUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 查询e签宝个人帐号接口：", reqVo.getThirdPartyUserId(), e);
            EsignResVo<PersonalAccountQueryResVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 查询e签宝个人帐号接口：{}", reqVo.getThirdPartyUserId(), JsonUtil.toString(respObject));
        EsignResVo<PersonalAccountQueryResVo> resVo = new EsignResVo<PersonalAccountQueryResVo>()
                .fillEsignResVo(respObject, PersonalAccountQueryResVo.class);
        log.info("[{}] 查询e签宝个人帐号接口：{}", reqVo.getThirdPartyUserId(), JsonUtil.toString(resVo));
        return resVo;
    }

    /**
     * 机构账号创建
     *
     * @param signCreateOrganizationReqVo 机构账号创建reqVo
     * @return 机构账号创建结果
     */
    public EsignResVo<SignCreateOrganizationResDataVo> createOrganization(SignCreateOrganizationReqVo signCreateOrganizationReqVo) {
        log.info("[{}] 请求e签宝机构账号创建接口", signCreateOrganizationReqVo.getThirdPartyUserId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(signCreateOrganizationReqVo, HttpMethod.POST.name(),
                    signCreateOrganizationReqVo.buildFullUrl(createOrganizationUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝机构账号创建异常：", signCreateOrganizationReqVo.getThirdPartyUserId(), e);
            EsignResVo<SignCreateOrganizationResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝机构账号创建返回原始结果：{}", signCreateOrganizationReqVo.getThirdPartyUserId(), JsonUtil.toString(respObject));
        EsignResVo<SignCreateOrganizationResDataVo> resVo = new EsignResVo<SignCreateOrganizationResDataVo>()
                .fillEsignResVo(respObject, SignCreateOrganizationResDataVo.class);
        log.info("[{}] 请求e签宝机构账号创建返回封装Vo：{}", signCreateOrganizationReqVo.getThirdPartyUserId(), JsonUtil.toString(resVo));
        return resVo;
    }

    /**
     * 查询模板详情
     *
     * @param signTemplateReqVo 查询模板详情ReqVo
     * @return 查询模板详情结果
     */
    public EsignResVo<SignTemplateResDataVo> getSignTemplate(SignTemplateReqVo signTemplateReqVo) {
        log.info("[{}] 请求e签宝查询模板详情接口", signTemplateReqVo.getTemplateId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(signTemplateReqVo, HttpMethod.GET.name(),
                    signTemplateReqVo.buildFullUrl(getSignTemplateUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝查询模板详情异常：", signTemplateReqVo.getTemplateId(), e);
            EsignResVo<SignTemplateResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝查询模板详情返回原始结果：{}", signTemplateReqVo.getTemplateId(), JsonUtil.toString(respObject));
        EsignResVo<SignTemplateResDataVo> resVo = new EsignResVo<SignTemplateResDataVo>()
                .fillEsignResVo(respObject, SignTemplateResDataVo.class);
        log.info("[{}] 请求e签宝查询模板详情返回封装Vo：{}", signTemplateReqVo.getTemplateId(), JsonUtil.toString(resVo));
        return resVo;
    }

    /**
     * 设置静默签署
     *
     * @param signAuthReqVo 设置静默签署ReqVo
     * @return 设置静默签署结果
     */
    public EsignResVo<Boolean> signAuth(SignAuthReqVo signAuthReqVo) {
        log.info("[{}] 请求e签宝设置静默签署接口", signAuthReqVo.getAccountId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(signAuthReqVo, HttpMethod.POST.name(),
                    signAuthReqVo.buildFullUrl(signAuthUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝设置静默签署异常：", signAuthReqVo.getAccountId(), e);
            EsignResVo<Boolean> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝设置静默签署返回原始结果：{}", signAuthReqVo.getAccountId(), JsonUtil.toString(respObject));
        EsignResVo<Boolean> resVo = new EsignResVo<Boolean>()
                .fillEsignResVo(respObject, Boolean.class);
        log.info("[{}] 请求e签宝设置静默签署返回封装Vo：{}", signAuthReqVo.getAccountId(), JsonUtil.toString(resVo));
        return resVo;
    }

    /**
     * 通过模板创建文件
     *
     * @param createFileByTemplateReqVo 通过模板创建文件ReqVo
     * @return 通过模板创建文件结果
     */
    public EsignResVo<CreateFileByTemplateResDataVo> createFileByTemplate(CreateFileByTemplateReqVo createFileByTemplateReqVo) {
        log.info("[{}] 请求e签宝通过模板创建文件接口", createFileByTemplateReqVo.getTemplateId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(createFileByTemplateReqVo, HttpMethod.POST.name(),
                    createFileByTemplateReqVo.buildFullUrl(createFileByTemplateUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝通过模板创建文件异常：", createFileByTemplateReqVo.getTemplateId(), e);
            EsignResVo<CreateFileByTemplateResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝通过模板创建文件返回原始结果：{}", createFileByTemplateReqVo.getTemplateId(), JsonUtil.toString(respObject));
        EsignResVo<CreateFileByTemplateResDataVo> resVo = new EsignResVo<CreateFileByTemplateResDataVo>()
                .fillEsignResVo(respObject, CreateFileByTemplateResDataVo.class);
        log.info("[{}] 请求e签宝通过模板创建文件返回封装Vo：{}", createFileByTemplateReqVo.getTemplateId(), JsonUtil.toString(resVo));

        return resVo;
    }

    /**
     * 一步发起签署
     *
     * @param createFlowOneStepReqVo 一步发起签署ReqVo
     * @return 一步发起签署结果
     */
    public EsignResVo<CreateFlowOneStepResDataVo> createFlowOneStep(CreateFlowOneStepReqVo createFlowOneStepReqVo) {
        log.info("[{}] 请求e签宝一步发起签署接口", createFlowOneStepReqVo.getSigners());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(createFlowOneStepReqVo, HttpMethod.POST.name(),
                    createFlowOneStepReqVo.buildFullUrl(createFlowOneStepUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝一步发起签署异常：", createFlowOneStepReqVo.getSigners(), e);
            EsignResVo<CreateFlowOneStepResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝一步发起签署返回原始结果：{}", createFlowOneStepReqVo.getSigners(), JsonUtil.toString(respObject));
        EsignResVo<CreateFlowOneStepResDataVo> resVo = new EsignResVo<CreateFlowOneStepResDataVo>()
                .fillEsignResVo(respObject, CreateFlowOneStepResDataVo.class);
        log.info("[{}] 请求e签宝一步发起签署返回封装：{}", createFlowOneStepReqVo.getSigners(), JsonUtil.toString(resVo));
        return resVo;
    }

    /**
     * 获取签署地址
     *
     * @param executeUrlReqVo 获取签署地址ReqVo
     * @return 获取签署地址结果
     */
    public EsignResVo<ExecuteUrlResDataVo> getExecuteUrl(ExecuteUrlReqVo executeUrlReqVo) {
        log.info("[{}] 请求e签宝获取签署地址接口", executeUrlReqVo.getFlowId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(executeUrlReqVo, HttpMethod.GET.name(),
                    executeUrlReqVo.buildFullUrl(getExecuteUrl), 6000);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获取签署地址异常：", executeUrlReqVo.getFlowId(), e);
            EsignResVo<ExecuteUrlResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝获取签署地址返回原始结果：{}", executeUrlReqVo.getFlowId(), JsonUtil.toString(respObject));
        EsignResVo<ExecuteUrlResDataVo> resVo = new EsignResVo<ExecuteUrlResDataVo>()
                .fillEsignResVo(respObject, ExecuteUrlResDataVo.class);
        log.info("[{}] 请求e签宝获取签署地址返回封装Vo：{}", executeUrlReqVo.getFlowId(), JsonUtil.toString(resVo));
        return resVo;
    }

    /**
     * 签署流程查询
     *
     * @param signFlowReqVo 签署流程查询ReqVo
     * @return 签署流程查询结果
     */
    public EsignResVo<SignFlowResDataVo> getSignFlow(SignFlowReqVo signFlowReqVo) {
        log.info("[{}] 请求e签宝签署流程查询接口", signFlowReqVo.getFlowId());
        JSONObject respObject;
        try {
            //发请求
            respObject = sendRequest(signFlowReqVo, HttpMethod.GET.name(),
                    signFlowReqVo.buildFullUrl(getSignFlowUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝签署流程查询异常：", signFlowReqVo.getFlowId(), e);
            EsignResVo<SignFlowResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        log.info("[{}] 请求e签宝签署流程查询返回原始结果：{}", signFlowReqVo.getFlowId(), JsonUtil.toString(respObject));
        EsignResVo<SignFlowResDataVo> resVo = new EsignResVo<SignFlowResDataVo>()
                .fillEsignResVo(respObject, SignFlowResDataVo.class);
        log.info("[{}] 请求e签宝签署流程查询返回封装Vo：{}", signFlowReqVo.getFlowId(), JsonUtil.toString(resVo));
        return resVo;
    }

    /**
     * 流程文档下载
     *
     * @param documentDownloadReqVo 流程文档下载ReqVo
     * @return 流程文档下载结果
     */
//    public EsignResVo<DocumentDownloadResDataVo> getDocumentDownloadUrl(DocumentDownloadReqVo documentDownloadReqVo) {
//        log.info("[{}] 请求e签宝流程文档下载接口", documentDownloadReqVo.getFlowId());
//        JSONObject respObject;
//        try {
//            //发请求
//            respObject = sendRequest(documentDownloadReqVo, HttpMethod.GET.name(),
//                    documentDownloadReqVo.buildFullUrl(getDocumentDownloadUrl), RequestConstant.REQUEST_TIMEOUT);
//        } catch (Exception e) {
//            log.info("[{}] 请求e签宝流程文档下载异常：", documentDownloadReqVo.getFlowId(), e);
//            EsignResVo<DocumentDownloadResDataVo> respVo = new EsignResVo<>();
//            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
//            return respVo;
//        }
//        log.info("[{}] 请求e签宝流程文档下载返回原始结果：{}", documentDownloadReqVo.getFlowId(), JsonUtil.toString(respObject));
//        EsignResVo<DocumentDownloadResDataVo> resVo = new EsignResVo<DocumentDownloadResDataVo>()
//                .fillEsignResVo(respObject, DocumentDownloadResDataVo.class);
//        log.info("[{}] 请求e签宝流程文档下载返回封装Vo：{}", documentDownloadReqVo.getFlowId(), JsonUtil.toString(resVo));
//        return resVo;
//    }


//    public EsignResVo<UpdatePersonInfoResDataVo> updatePersonInfo(UpdatePersonInfoReqVo updatePersonInfoReqVo) {
//        log.info("[{}] 请求e签宝个人账号更新接口", updatePersonInfoReqVo.getAccountId());
//        JSONObject respObject;
//        try {
//            //发请求
//            respObject = sendRequest(updatePersonInfoReqVo, HttpMethod.PUT.name(),
//                    updatePersonInfoReqVo.buildFullUrl(updatePersonInfoUrl), RequestConstant.REQUEST_TIMEOUT);
//        } catch (Exception e) {
//            log.info("[{}] 请求e签宝个人账号更新接口异常：", updatePersonInfoReqVo.getAccountId(), e);
//            EsignResVo<UpdatePersonInfoResDataVo> respVo = new EsignResVo<>();
//            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
//            return respVo;
//        }
//        log.info("[{}] 请求e签宝流程个人账号更新接口返回原始结果：{}", updatePersonInfoReqVo.getAccountId(), JsonUtil.toString(respObject));
//        EsignResVo<UpdatePersonInfoResDataVo> resVo = new EsignResVo<UpdatePersonInfoResDataVo>()
//                .fillEsignResVo(respObject, UpdatePersonInfoResDataVo.class);
//        log.info("[{}] 请求e签宝流程个人账号更新接口返回封装Vo：{}", updatePersonInfoReqVo.getAccountId(), JsonUtil.toString(resVo));
//        return resVo;
//    }

    public List<KeywordPosVo> getPos(CheckFilePosVo checkFilePosVo) {
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(checkFilePosVo, HttpMethod.GET.name(),
                    checkFilePosVo.buildFullUrl(getKeywordPositionsUrl), RequestConstant.REQUEST_TIMEOUT);
            log.info("[{}] 请求e签宝获取坐标接口：返回结果：{}", checkFilePosVo.getFileId(), respObject);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获取坐标接口异常：", checkFilePosVo.getFileId(), e);
        }
        JSONObject data = respObject.getJSONObject("data");
        Object keywordPositions = data.get("keywordPositions");
        if(ObjectUtils.isEmpty(keywordPositions)){
            return null;
        }
        return BeanUtil.toArray(KeywordPosVo.class, keywordPositions);
    }

    public EsignResVo<TemplatePageResDataResVo> getAllTemplateInfo(EsignPageVo esignPageVo){
        JSONObject resObject = null;
        try {
            resObject = sendRequest(esignPageVo,HttpMethod.GET.name(),
                    esignPageVo.buildFullUrl(getAllTemplateInfo),RequestConstant.REQUEST_TIMEOUT);
            log.info("分页请求e签宝模板列表，返回结果：{}",resObject.toJSONString());
        }catch (Exception e){
            log.info("分页获取模板列表异常：",e);
            EsignResVo<TemplatePageResDataResVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        EsignResVo<TemplatePageResDataResVo> resVo = new EsignResVo<TemplatePageResDataResVo>()
                .fillEsignResVo(resObject, TemplatePageResDataResVo.class);
        log.info("分页请求e签宝模板协议列表接口返回结果：{}",JsonUtil.toString(resVo));
        return resVo;
    }

    public List<StructComponent> getTemplateInfo(String templateId) {
        TemplateParamVo templateParamVo=new TemplateParamVo(templateId);
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(templateParamVo, HttpMethod.GET.name(),
                    templateParamVo.buildFullUrl(getTemplateInfo), RequestConstant.REQUEST_TIMEOUT);
            log.info("[{}] 请求e签宝获取模板信息：返回结果：{}", templateId, respObject);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获取模板信息异常：", templateId, e);
        }
        JSONObject data = respObject.getJSONObject("data");
        JSONArray structComponents = data.getJSONArray("structComponents");
        if(ObjectUtils.isEmpty(structComponents)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不对对应模板");
        }
        return structComponents.toJavaList(StructComponent.class);
    }

    /**
     * 流程文档上传到fastdfs
     *
     * @param fileName 文件名
     * @param fileUrl  远程url
     * @return fastdfs地址
     * @throws IOException
     */
    public String downLoadFile(String logFlag, String fileName, String fileUrl) throws IOException {
        log.info("[{}]==>签约文件上传至fastDfs,远程文件名:{},url:{}", logFlag, fileName, fileUrl);
        WebClient webClient = WebClient.builder().build();
        Mono<ClientResponse> mono = webClient
                .get()
                .uri(UriComponentsBuilder
                        .fromHttpUrl(fileUrl)
                        .build(true)
                        .toUri())
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .exchange();

        ClientResponse response = mono.block();
        if (response == null) {
            return "";
        }
        if (!Objects.equals(HttpStatus.OK, response.statusCode())) {
            log.error("uploadSignFileError : [{}] 远程签约文件访问返回不为200 status:{},远程文件名:{},url:{}",
                    logFlag, response.statusCode(), fileName, fileUrl);
            return "";
        }
        Resource resource = response.bodyToMono(Resource.class).block();
        if (resource == null) {
            return "";
        }
        InputStream fileStream = resource.getInputStream();
        byte[] byt = new byte[fileStream.available()];
        fileStream.read(byt);
        return fastdfsClient.uploadFile(byt, fileName);
    }


    /**
     * e签宝通用请求
     *
     * @param reqVo         请求数据
     * @param requestMethod 请求方式
     * @param url           接口url
     * @param timeout       超时时间
     * @return 结果
     * @throws Exception 异常
     */
    private JSONObject sendRequest(EsignBaseReqVo reqVo, String requestMethod, String url, long timeout) throws Exception {

        Map<String, String> headersMap = reqVo
                .buildHeadersMap(requestMethod, url, projectId, projectSecret);
        //构建请求头
        Consumer<HttpHeaders> headersConsumer = httpHeaders -> {
            for (Map.Entry<String, String> entry : headersMap.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue());
            }
        };
        log.info("请求的数据 \n url:{},\n 请求参数:{},\n 请求头:{}", url, JsonUtil.toString(reqVo), JsonUtil.toString(headersMap));
        //发起请求
        WebClient webClient = WebClient.create(host + url);
        Mono<JSONObject> mono = null;
        if (HttpMethod.POST.name().equals(requestMethod)) {
            mono = webClient.post().headers(headersConsumer)
                    .body(BodyInserters.fromObject(JsonUtil.toString(reqVo))).retrieve().bodyToMono(JSONObject.class);
        } else if (HttpMethod.GET.name().equals(requestMethod)) {
            mono = webClient.get().headers(headersConsumer).retrieve().bodyToMono(JSONObject.class);
        } else if (HttpMethod.PUT.name().equals(requestMethod)) {
            mono = webClient.put().headers(headersConsumer)
                    .body(BodyInserters.fromObject(JsonUtil.toString(reqVo))).retrieve().bodyToMono(JSONObject.class);
        }
        if (mono == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求方式有误");
        }
        return mono.block(Duration.ofMillis(timeout));
    }


    private boolean uploadRequest(UploadFileReqVo uploadFileReqVo, String url, int timeout, byte[] buffer) throws IOException {
        HttpPut httpPut = new HttpPut(url);
        // 设置头部
        httpPut.setHeader("Content-Type", uploadFileReqVo.getContentType());
        httpPut.setHeader("Content-MD5", uploadFileReqVo.getContentMd5());
        log.info("请求的数据 \n url:{},\n 请求参数:{},\n 请求头:{}", url, JsonUtil.toString(uploadFileReqVo), JsonUtil.toString(httpPut));
        // 设置实体内容
        httpPut.setEntity(new ByteArrayEntity(buffer));
        // 请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout)
                .setConnectTimeout(timeout).build();
        httpPut.setConfig(requestConfig);
        // 执行请求
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient httpclient = httpClientBuilder.build();
        HttpResponse response = httpclient.execute(httpPut);
        int statusCode = response.getStatusLine().getStatusCode();
        String entity = EntityUtils.toString(response.getEntity(), UTF_8);
        log.info("Response code: {}, body: {}", statusCode, entity);
        return true;
    }



}
