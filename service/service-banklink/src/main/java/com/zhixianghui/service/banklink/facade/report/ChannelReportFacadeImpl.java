package com.zhixianghui.service.banklink.facade.report;

import com.zhixianghui.facade.banklink.service.report.ChannelReportFacade;
import com.zhixianghui.facade.banklink.vo.report.*;
import com.zhixianghui.service.banklink.core.biz.report.ChannelReportBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-30 10:37
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelReportFacadeImpl implements ChannelReportFacade {

    private final ChannelReportBiz channelReportBiz;

    @Override
    public ReportResVo report(ReportReqVo reportReqVo) {
        return channelReportBiz.report(reportReqVo);
    }

    @Override
    public ReportReceiveRespVo verifyAndHandleResult(ReportReceiveReqVo reqVo) {
        return channelReportBiz.verifyAndHandleResult(reqVo);
    }

    @Override
    public ReportResVo modify(ReportReqVo reportReqVo) {
        return channelReportBiz.modify(reportReqVo);
    }

    @Override
    public Map<String, Map<String,String>> cheackMchSignAndPicUpload(AltMchSignVo vo){
        return channelReportBiz.cheackMchSignAndPicUpload(vo);
    }
}
