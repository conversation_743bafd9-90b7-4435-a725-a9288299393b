package com.zhixianghui.service.banklink.request.sign;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.sign.EsignBaseReqVo;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.components.ComponentsResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgAuthUrlReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgAuthUrlResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDateVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.*;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 * @description e签宝Biz
 * @date 2021-01-05 15:10
 **/
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EsignBizV3 {
    @Value("${sign.esignGateway}")
    private String host;
    @Value("${sign.esignProjectId}")
    private String projectId;
    @Value("${sign.esignProjectSecret}")
    private String projectSecret;

    private final FastdfsClient fastdfsClient;


    //实名认证和授权服务api

    //查询个人认证信息
    private String psnIdentityInfo = "/v3/persons/identity-info";
    //获取个人认证&授权页面链接
    private String psnAuthUrl = "/v3/psn-auth-url";
    //查询个人认证信息
    private String orgIdentityInfo = "/v3/organizations/identity-info";
    //获取个人认证&授权页面链接
    private String orgAuthUrl = "/v3/org-auth-url";


    private String fileUploadUrl = "/v3/files/file-upload-url";
    private String revokeUrl = "/v3/sign-flow/{signFlowId}/revoke";

    private String createFileByTemplateUrl = "/v3/files/create-by-doc-template";
    private String createByFile = "/v3/sign-flow/create-by-file";

    private String getExecuteUrl = "/v3/sign-flow/{signFlowId}/sign-url";

    private String docTemplateCreateUrl = "/v3/doc-templates/doc-template-create-url";

    private String docTemplateEditUrl = "/v3/doc-templates/{docTemplateId}/doc-template-edit-url";

    private String orgSealList = "/v3/seals/org-own-seal-list";
    private String getSignTemplateUrl = "/v3/doc-templates";

    private String componentsList = "/v3/custom-components/get-list";

    private String delComponentsList = "/v3/custom-components/delete";

    private String createComponentsList = "/v3/custom-components/create";

    private String getAllTemplateInfo = "/v3/doc-templates";

    private String getFileDownloadUrl = "/v3/sign-flow/{signFlowId}/file-download-url";

    private String getPreviewFileDownloadUrl = "/v3/sign-flow/{signFlowId}/preview-file-download-url";

    private String getPersonalSignatureUrl = "/v3/seals/psn-seals/create-by-template";
    private String getPsnSealListUrl = "/v3/seals/psn-seal-list";

    //步奏1 查询机构认证状态+个人认证状态

    /**
     * 查询个人认证信息
     */
    public EsignResVo<PsnIdentityInfoResDataVo> getPsnIdentityInfoV3(PsnIdentityInfoReqVo  psnIdentityInfoReqVo) {
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(psnIdentityInfoReqVo, HttpMethod.GET.name(),
                    psnIdentityInfoReqVo.buildFullUrl(psnIdentityInfo), RequestConstant.REQUEST_TIMEOUT);
        }catch (Exception e) {
            log.info("[{}] 请求e签宝查询个人认证请求异常：", JSONObject.toJSON(respObject), e);
            EsignResVo<PsnIdentityInfoResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝查询个人认证信息成功", JsonUtil.toString(respObject));
        EsignResVo<PsnIdentityInfoResDataVo> resVo = new EsignResVo<PsnIdentityInfoResDataVo>().
                fillEsignResVo(respObject, PsnIdentityInfoResDataVo.class);
        return resVo;
        //{"code":0,"data":{"authorizeUserInfo":false,"realnameStatus":1,"psnId":"e636d46a03b845e49603a143f37e70ef"},"message":"成功"}
//        {"code":0,"data":{"authorizeUserInfo":true,"realnameStatus":1,"psnId":"e636d46a03b845e49603a143f37e70ef","psnAccount":{"accountMobile":"***********"},"psnInfo":{"psnName":"张宸","psnIDCardNum":"******************","psnIDCardType":"CRED_PSN_CH_IDCARD","psnMobile":"***********","psnIdentityVerify":false}},"message":"成功"}
    }


    /**
     * 查询机构认证信息
     */
    public EsignResVo<OrgIdentityInfoResDataVo> OrgIdentityInfoV3(OrgIdentityInfoResDateVo orgIdentityInfoResDateVo) {
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(orgIdentityInfoResDateVo, HttpMethod.GET.name(),
                    orgIdentityInfoResDateVo.buildFullUrl(orgIdentityInfo), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝查询机构认证信息异常", JsonUtil.toString(orgIdentityInfoResDateVo), e);
            EsignResVo respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝查询机构认证信息成功", JsonUtil.toString(respObject));
        EsignResVo<OrgIdentityInfoResDataVo> resVo = new EsignResVo<OrgIdentityInfoResDataVo>().
                fillEsignResVo(respObject, OrgIdentityInfoResDataVo.class);
        return resVo;
//        {"code":0,"data":{"authorizeUserInfo":true,"realnameStatus":1,"orgId":"9d2b91f8a6d44a0c8cfa1691dd1dea32","orgName":"广州市汇聚智享电子科技有限公司","orgInfo":{"orgIDCardNum":"91440101MA9UQFEL4R","orgIDCardType":"CRED_ORG_USCC","legalRepName":"王军","legalRepIDCardNum":"340603197509020816","adminName":"方*能","adminAccount":"1******3826"}},"message":"成功"}
    }






    //步奏2 获取认证授权页面链接

    /**
     * 获取个人认证&授权页面链接
     */
    public EsignResVo<PsnAuthUrlResDataVo> PsnAuthUrlV3(PsnAuthUrlReqVo psnAuthUrlReqVo) {
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(psnAuthUrlReqVo, HttpMethod.POST.name(),
                    psnAuthUrlReqVo.buildFullUrl(psnAuthUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获取个人认证&授权页面链接异常", JsonUtil.toString(psnAuthUrlReqVo), e);
            EsignResVo respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }

        if(!ObjectUtil.equal(respObject.get("code"), 0)){
            log.info("[{}] 请求e签宝获取个人认证&授权页面链接失败", JsonUtil.toString(respObject));
            EsignResVo respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝获取个人认证&授权页面链接成功", JsonUtil.toString(respObject));
        EsignResVo<PsnAuthUrlResDataVo> resVo = new EsignResVo<PsnAuthUrlResDataVo>().
                fillEsignResVo(respObject, PsnAuthUrlResDataVo.class);
        return resVo;

    }

    /**
     * 获取机构认证&授权页面链接
     */
    public EsignResVo<OrgAuthUrlResDataVo> OrgAuthUrlV3(OrgAuthUrlReqVo orgAuthUrlReqVo) {
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(orgAuthUrlReqVo, HttpMethod.POST.name(),
                    orgAuthUrlReqVo.buildFullUrl(orgAuthUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获机构认证&授权页面链接异常", JsonUtil.toString(orgAuthUrlReqVo), e);
            EsignResVo respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }

        if(!ObjectUtil.equal(respObject.get("code"), 0)){
            log.info("[{}] 请求e签宝获取机构认证&授权页面链接失败", JsonUtil.toString(respObject));
            EsignResVo respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝获取机构认证&授权页面链接成功", JsonUtil.toString(respObject));
        EsignResVo<OrgAuthUrlResDataVo> resVo = new EsignResVo<OrgAuthUrlResDataVo>().
                fillEsignResVo(respObject, OrgAuthUrlResDataVo.class);
        return resVo;
//        {"code":0,"data":{"authFlowId":"OF-2e195f6084080025","authUrl":"https://hjzx.smlh5.esign.cn/auth/h5/index?authFlowId=OF-2e195f6084080025&clientType=ALL&appId=7438838667","authShortUrl":"https://smlt.esign.cn/FqsECFD"},"message":"成功"}
    }

    public EsignResVo revokeFlow(RevokeFlowVoV3 revokeFlowVo) {
        log.info("[{}] 请求e签宝撤回流程接口", revokeFlowVo.getSignFlowId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(revokeFlowVo, HttpMethod.POST.name(),
                    revokeFlowVo.buildFullUrl(revokeUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝撤回流程接口异常：", revokeFlowVo.getSignFlowId(), e);
            EsignResVo respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝撤回流程接口返回原始结果：{}", revokeFlowVo.getSignFlowId(), JsonUtil.toString(respObject));
        EsignResVo resVo = respObject.toJavaObject(EsignResVo.class);
        return resVo;
    }


    /**
     * 获取文件上传链接地址
     * @param createSignTemplateReqVo
     * @return 结果
     */
    public EsignResVo<CreateFileUploadUrlResVo> getUploadUrl(CreateFileUploadUrlVo createFileUploadUrlVo){
        log.error("请求获取文件上传链接地址接口，请求参数：[{}]",JsonUtil.toString(createFileUploadUrlVo));
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(createFileUploadUrlVo, HttpMethod.POST.name(),
                    createFileUploadUrlVo.buildFullUrl(fileUploadUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.error("请求获取文件上传链接地址接口异常",e);
            EsignResVo<CreateFileUploadUrlResVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        EsignResVo<CreateFileUploadUrlResVo> resVo = new EsignResVo<CreateFileUploadUrlResVo>().
                fillEsignResVo(respObject, CreateFileUploadUrlResVo.class);
        return resVo;
    }


    public boolean uploadFile(UploadFileReqVo uploadFileReqVo, String uploadUrl, byte[] buffer) {
        log.info("[{}-{}] 请求e签宝上传文件", JSONObject.toJSON(uploadFileReqVo), uploadUrl);
        JSONObject respObject = null;
        try {
            // 发请求
            return uploadRequest(uploadFileReqVo, uploadUrl, Math.toIntExact(RequestConstant.REQUEST_TIMEOUT * 20), buffer);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝上传文件接口异常：", JSONObject.toJSON(respObject), e);
            return false;
        }
    }



    /**
     * e签宝通用请求
     *
     * @param reqVo         请求数据
     * @param requestMethod 请求方式
     * @param url           接口url
     * @param timeout       超时时间
     * @return 结果
     * @throws Exception 异常
     */
    private JSONObject sendRequest(EsignBaseReqVo reqVo, String requestMethod, String url, long timeout) throws Exception {

        Map<String, String> headersMap = reqVo
                .buildHeadersMap(requestMethod, url, projectId, projectSecret);
        //构建请求头
        Consumer<HttpHeaders> headersConsumer = httpHeaders -> {
            for (Map.Entry<String, String> entry : headersMap.entrySet()) {
                httpHeaders.add(entry.getKey(), entry.getValue());
            }
        };
        log.info("请求的数据 \n url:{},\n 请求参数:{},\n 请求头:{}", url, JsonUtil.toString(reqVo), JsonUtil.toString(headersMap));
        //发起请求
        WebClient webClient = WebClient.create(host + url);
        Mono<JSONObject> mono = null;
        if (HttpMethod.POST.name().equals(requestMethod)) {
            mono = webClient.post().headers(headersConsumer)
                    .body(BodyInserters.fromObject(JsonUtil.toString(reqVo))).retrieve().bodyToMono(JSONObject.class);
        } else if (HttpMethod.GET.name().equals(requestMethod)) {
            mono = webClient.get().headers(headersConsumer).retrieve().bodyToMono(JSONObject.class);
        } else if (HttpMethod.PUT.name().equals(requestMethod)) {
            mono = webClient.put().headers(headersConsumer)
                    .body(BodyInserters.fromObject(JsonUtil.toString(reqVo))).retrieve().bodyToMono(JSONObject.class);
        }
        if (mono == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求方式有误");
        }
        return mono.block(Duration.ofMillis(timeout));
    }


    private boolean uploadRequest(UploadFileReqVo uploadFileReqVo, String url, int timeout, byte[] buffer) throws IOException {
        HttpPut httpPut = new HttpPut(url);
        // 设置头部
        httpPut.setHeader("Content-Type", uploadFileReqVo.getContentType());
        httpPut.setHeader("Content-MD5", uploadFileReqVo.getContentMd5());
        log.info("请求的数据 \n url:{},\n 请求参数:{},\n 请求头:{}", url, JsonUtil.toString(uploadFileReqVo), JsonUtil.toString(httpPut));
        // 设置实体内容
        httpPut.setEntity(new ByteArrayEntity(buffer));
        // 请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout)
                .setConnectTimeout(timeout).build();
        httpPut.setConfig(requestConfig);
        // 执行请求
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient httpclient = httpClientBuilder.build();
        HttpResponse response = httpclient.execute(httpPut);
        int statusCode = response.getStatusLine().getStatusCode();
        String entity = EntityUtils.toString(response.getEntity(), UTF_8);
        log.info("Response code: {}, body: {}", statusCode, entity);
        return true;
    }


    /**
     * 通过模板创建文件
     *
     * @param createFileByTemplateReqVo 通过模板创建文件ReqVo
     * @return 通过模板创建文件结果
     */
    public EsignResVo<CreateFileByTemplateResDataVoV3> createFileByTemplate(CreateFileByTemplateReqVoV3 createFileByTemplateReqVo) {
        log.info("[{}] 请求e签宝通过模板创建文件接口", createFileByTemplateReqVo.getDocTemplateId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(createFileByTemplateReqVo, HttpMethod.POST.name(),
                    createFileByTemplateReqVo.buildFullUrl(createFileByTemplateUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝通过模板创建文件异常：", createFileByTemplateReqVo.getDocTemplateId(), e);
            EsignResVo<CreateFileByTemplateResDataVoV3> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝通过模板创建文件返回原始结果：{}", createFileByTemplateReqVo.getDocTemplateId(), JsonUtil.toString(respObject));
        EsignResVo<CreateFileByTemplateResDataVoV3> resVo = new EsignResVo<CreateFileByTemplateResDataVoV3>()
                .fillEsignResVo(respObject, CreateFileByTemplateResDataVoV3.class);
        log.info("[{}] 请求e签宝通过模板创建文件返回封装Vo：{}", createFileByTemplateReqVo.getDocTemplateId(), JsonUtil.toString(resVo));
        return resVo;
    }


    /**
     * （精简版）基于文件发起签署
     *
     * @param createByFileReqVo （精简版）基于文件发起签署
     * @return （精简版）基于文件发起签署结果
     */
    public EsignResVo<CreateByFileResDateVo> createByFile(CreateByFileReqVo createByFileReqVo) {
        log.info("[{}] 请求e签宝（精简版）基于文件发起签署接口", createByFileReqVo.getSigners());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(createByFileReqVo, HttpMethod.POST.name(),
                    createByFileReqVo.buildFullUrl(createByFile), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝（精简版）基于文件发起签署异常：", createByFileReqVo.getSigners(), e);
            EsignResVo<CreateByFileResDateVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝（精简版）基于文件发起签署返回原始结果：{}", createByFileReqVo.getSigners(), JsonUtil.toString(respObject));
        EsignResVo<CreateByFileResDateVo> resVo = new EsignResVo<CreateByFileResDateVo>()
                .fillEsignResVo(respObject, CreateByFileResDateVo.class);
        log.info("[{}] 请求e签宝（精简版）基于文件发起签署返回封装：{}", createByFileReqVo.getSigners(), JsonUtil.toString(resVo));
        return resVo;
    }


    /**
     * 获取签署地址
     *
     * @param executeUrlReqVo 获取签署地址ReqVo
     * @return 获取签署地址结果
     */
    public EsignResVo<ExecuteUrlResDataVo> getExecuteUrl(ExecuteUrlReqVoV3 executeUrlReqVo) {
        log.info("[{}] 请求e签宝获取签署地址接口", executeUrlReqVo.getSignFlowId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(executeUrlReqVo, HttpMethod.POST.name(),
                    executeUrlReqVo.buildFullUrl(getExecuteUrl), 6000);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获取签署地址异常：", executeUrlReqVo.getSignFlowId(), e);
            EsignResVo<ExecuteUrlResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝获取签署地址返回原始结果：{}", executeUrlReqVo.getSignFlowId(), JsonUtil.toString(respObject));
        EsignResVo<ExecuteUrlResDataVo> resVo = new EsignResVo<ExecuteUrlResDataVo>()
                .fillEsignResVo(respObject, ExecuteUrlResDataVo.class);
        log.info("[{}] 请求e签宝获取签署地址返回封装Vo：{}", executeUrlReqVo.getSignFlowId(), JsonUtil.toString(resVo));
        return resVo;
    }

    public EsignResVo<CreateSignTemplateResV3DataVo> createSignTemplate(CreateSignTemplateReqVo createSignTemplateReqVo) {

        log.info("[{}] 请求e签宝通过上传方式创建模板", createSignTemplateReqVo.getFileName());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(createSignTemplateReqVo, HttpMethod.POST.name(),
                    createSignTemplateReqVo.buildFullUrl(fileUploadUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝通过上传方式创建模板接口异常：", createSignTemplateReqVo.getFileName(), e);
            EsignResVo<CreateSignTemplateResV3DataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝通过上传方式创建模板返回原始结果：{}", createSignTemplateReqVo.getFileName(), JsonUtil.toString(respObject));
        EsignResVo<CreateSignTemplateResV3DataVo> resVo = new EsignResVo<CreateSignTemplateResV3DataVo>().
                fillEsignResVo(respObject, CreateSignTemplateResV3DataVo.class);
        log.info("[{}] 请求e签宝通过上传方式创建模板返回封装Vo：{}", createSignTemplateReqVo.getFileName(), JsonUtil.toString(resVo));
        return resVo;
    }

    public EsignResVo<CreateTemplateResV3DataVo> createTemplate(CreateTemplateReqVo createTemplateReqVo) {

        log.info("[{}] 请求e签宝获取制作合同模板页面", createTemplateReqVo.getDocTemplateName());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(createTemplateReqVo, HttpMethod.POST.name(),
                    createTemplateReqVo.buildFullUrl(docTemplateCreateUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获取制作合同模板页面接口异常：", createTemplateReqVo.getDocTemplateName(), e);
            EsignResVo<CreateTemplateResV3DataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝获取制作合同模板页面返回原始结果：{}", createTemplateReqVo.getDocTemplateName(), JsonUtil.toString(respObject));
        EsignResVo<CreateTemplateResV3DataVo> resVo = new EsignResVo<CreateTemplateResV3DataVo>().
                fillEsignResVo(respObject, CreateTemplateResV3DataVo.class);
        log.info("[{}] 请求e签宝获取制作合同模板页面返回封装Vo：{}", createTemplateReqVo.getDocTemplateName(), JsonUtil.toString(resVo));
        return resVo;
    }

    public EsignResVo<SignTemplateResDataVoV3> getSignTemplate(SignTemplateReqVo signTemplateReqVo) {
        log.info("[{}] 请求e签宝合同模板中控件详情", signTemplateReqVo.getTemplateId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(signTemplateReqVo, HttpMethod.GET.name(),
                    signTemplateReqVo.buildFullUrl(getSignTemplateUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝合同模板中控件详情：", signTemplateReqVo.getTemplateId(), e);
            EsignResVo<SignTemplateResDataVoV3> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝合同模板中控件详情返回原始结果：{}", signTemplateReqVo.getTemplateId(), JsonUtil.toString(respObject));
        EsignResVo<SignTemplateResDataVoV3> resVo = new EsignResVo<SignTemplateResDataVoV3>()
                .fillEsignResVo(respObject, SignTemplateResDataVoV3.class);
        log.info("[{}] 请求e签宝合同模板中控件详情封装Vo：{}", signTemplateReqVo.getTemplateId(), JsonUtil.toString(resVo));
        return resVo;
    }


    public EsignResVo<ComponentsResDataVo> customComponentsList(EsignPageVo esignPageVo) {
        log.info("[{}] 请求e签宝查询自定义业务控件列表");
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(esignPageVo, HttpMethod.POST.name(),
                    esignPageVo.buildFullUrl(componentsList), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("请求e签宝查询自定义业务控件列表：", e);
            EsignResVo<ComponentsResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("请求e签宝查询自定义业务控件列表返回原始结果：{}", JsonUtil.toString(respObject));
        EsignResVo<ComponentsResDataVo> resVo = new EsignResVo<ComponentsResDataVo>()
                .fillEsignResVo(respObject, ComponentsResDataVo.class);
        log.info("请求e签宝查询自定义业务控件列表封装Vo：{}",  JsonUtil.toString(resVo));
        return resVo;

    }

    public EsignResVo<?> delComponentsList(DelComponentReqVo delComponentReqVo) {
        log.info("[{}] 请求e签宝删除自定义业务控件");
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(delComponentReqVo, HttpMethod.POST.name(),
                    delComponentReqVo.buildFullUrl(delComponentsList), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("请求e签宝删除自定义业务控件：", e);
            EsignResVo<?> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("请求e签宝删除自定义业务控件返回原始结果：{}", JsonUtil.toString(respObject));
        EsignResVo<?> respVo = JSONObject.toJavaObject(respObject, EsignResVo.class);
        log.info("请求e签宝删除自定义业务控件封装Vo：{}",  JsonUtil.toString(respVo));
        return respVo;
    }

    public EsignResVo<?> createComponentsList(CreateComponentReqVo reqVo) {
        log.info("[{}] 请求e签宝创建自定义业务控件");
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(reqVo, HttpMethod.POST.name(),
                    reqVo.buildFullUrl(createComponentsList), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("请求e签宝创建自定义业务控件：", e);
            EsignResVo<?> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("请求e签宝创建自定义业务控件返回原始结果：{}", JsonUtil.toString(respObject));
        EsignResVo<?> respVo = JSONObject.toJavaObject(respObject, EsignResVo.class);
        log.info("请求e签宝创建自定义业务控件封装Vo：{}",  JsonUtil.toString(respVo));
        return respVo;
    }

    public EsignResVo<TemplatePageResDataResVo> getAllTemplateInfo(EsignPageVo esignPageVo) {
        JSONObject resObject = null;
        try {
            resObject = sendRequest(esignPageVo,HttpMethod.GET.name(),
                    esignPageVo.buildFullUrl(getAllTemplateInfo),RequestConstant.REQUEST_TIMEOUT);
            log.info("分页请求e签宝模板列表，返回结果：{}",resObject.toJSONString());
        }catch (Exception e){
            log.info("分页获取模板列表异常：",e);
            EsignResVo<TemplatePageResDataResVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(resObject.get("message").toString());
            return respVo;
        }
        EsignResVo<TemplatePageResDataResVo> resVo = new EsignResVo<TemplatePageResDataResVo>()
                .fillEsignResVo(resObject, TemplatePageResDataResVo.class);
        log.info("分页请求e签宝模板协议列表接口返回结果：{}",JsonUtil.toString(resVo));
        return resVo;
    }


    /**
     * 流程文档上传到fastdfs
     *
     * @param fileName 文件名
     * @param fileUrl  远程url
     * @return fastdfs地址
     * @throws IOException
     */
    public String downLoadFile(String logFlag, String fileName, String fileUrl) throws IOException {
        log.info("[{}]==>签约文件上传至fastDfs,远程文件名:{},url:{}", logFlag, fileName, fileUrl);
        WebClient webClient = WebClient.builder().build();
        Mono<ClientResponse> mono = webClient
                .get()
                .uri(UriComponentsBuilder
                        .fromHttpUrl(fileUrl)
                        .build(true)
                        .toUri())
                .accept(MediaType.APPLICATION_OCTET_STREAM)
                .exchange();

        ClientResponse response = mono.block();
        if (response == null) {
            return "";
        }
        if (!Objects.equals(HttpStatus.OK, response.statusCode())) {
            log.error("uploadSignFileError : [{}] 远程签约文件访问返回不为200 status:{},远程文件名:{},url:{}",
                    logFlag, response.statusCode(), fileName, fileUrl);
            return "";
        }
        Resource resource = response.bodyToMono(Resource.class).block();
        if (resource == null) {
            return "";
        }
        InputStream fileStream = resource.getInputStream();
        byte[] byt = new byte[fileStream.available()];
        fileStream.read(byt);
        return fastdfsClient.uploadFile(byt, fileName);
    }

    public EsignResVo<DocumentDownloadResDataVo> getDocumentDownloadUrl(DocumentDownloadReqVo documentDownloadReqVo) {
        log.info("[{}] 请求e签宝流程文档下载接口", documentDownloadReqVo.getSignFlowId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(documentDownloadReqVo, HttpMethod.GET.name(),
                    documentDownloadReqVo.buildFullUrl(getFileDownloadUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝流程文档下载异常：", documentDownloadReqVo.getSignFlowId(), e);
            EsignResVo<DocumentDownloadResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝流程文档下载返回原始结果：{}", documentDownloadReqVo.getSignFlowId(), JsonUtil.toString(respObject));
        EsignResVo<DocumentDownloadResDataVo> resVo = new EsignResVo<DocumentDownloadResDataVo>()
                .fillEsignResVo(respObject, DocumentDownloadResDataVo.class);
        log.info("[{}] 请求e签宝流程文档下载返回封装Vo：{}", documentDownloadReqVo.getSignFlowId(), JsonUtil.toString(resVo));
        return resVo;
    }

    public EsignResVo<PreviewFileDownloadResDataVo> getPreviewDownloadUrl(PreviewFileDownloadReqVo documentDownloadReqVo) {
        log.info("[{}] 请求e签宝签署中文件", documentDownloadReqVo.getSignFlowId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(documentDownloadReqVo, HttpMethod.GET.name(),
                    documentDownloadReqVo.buildFullUrl(getPreviewFileDownloadUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝签署中文件异常：", documentDownloadReqVo.getSignFlowId(), e);
            EsignResVo<PreviewFileDownloadResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝签署中文件返回原始结果：{}", documentDownloadReqVo.getSignFlowId(), JsonUtil.toString(respObject));
        EsignResVo<PreviewFileDownloadResDataVo> resVo = new EsignResVo<PreviewFileDownloadResDataVo>()
                .fillEsignResVo(respObject, PreviewFileDownloadResDataVo.class);
        log.info("[{}] 请求e签宝签署中文件返回封装Vo：{}", documentDownloadReqVo.getSignFlowId(), JsonUtil.toString(resVo));
        return resVo;
    }

    public EsignResVo<GetPersonalSignatureResDataVo> personalSignature(PersonalSignatureReqVo reqVo) {
        log.info("[{}] 创建个人签章", JSONObject.toJSON(reqVo));
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(reqVo, HttpMethod.POST.name(),
                    reqVo.buildFullUrl(getPersonalSignatureUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 创建个人签章请求异常：", JSONObject.toJSON(respObject), e);
            EsignResVo<GetPersonalSignatureResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求创建个人签章：{}", reqVo.getPsnId(), JsonUtil.toString(respObject));
        EsignResVo<GetPersonalSignatureResDataVo> resVo = new EsignResVo<GetPersonalSignatureResDataVo>().
                fillEsignResVo(respObject, GetPersonalSignatureResDataVo.class);
        log.info("请求创建个人签章返回封装Vo：{}", JSONObject.toJSON(resVo));
        return resVo;

    }

    public EsignResVo<GetPsnSealListResDataVo> getPsnSealList(PsnSealListReqVo reqVo) {
        log.info("[{}] 创建个人签章", JSONObject.toJSON(reqVo));
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(reqVo, HttpMethod.GET.name(),
                    reqVo.buildFullUrl(getPsnSealListUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 获取个人章列表：", JSONObject.toJSON(respObject), e);
            EsignResVo<GetPsnSealListResDataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 获取个人章列表：{}", reqVo.getPsnId(), JsonUtil.toString(respObject));
        EsignResVo<GetPsnSealListResDataVo> resVo = new EsignResVo<GetPsnSealListResDataVo>().
                fillEsignResVo(respObject, GetPsnSealListResDataVo.class);
        log.info("获取个人章列表返回封装Vo：{}", JSONObject.toJSON(resVo));
        return resVo;
    }

    public EsignResVo<EditTemplateResV3DataVo> editTemplate(EditTemplateReqVo createTemplateReqVo) {

        log.info("[{}] 请求e签宝获取编辑合同模板页面", createTemplateReqVo.getDocTemplateId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(createTemplateReqVo, HttpMethod.POST.name(),
                    createTemplateReqVo.buildFullUrl(docTemplateEditUrl), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获取编辑合同模板页面接口异常：", createTemplateReqVo.getDocTemplateId(), e);
            EsignResVo<EditTemplateResV3DataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝获取编辑合同模板页面返回原始结果：{}", createTemplateReqVo.getDocTemplateId(), JsonUtil.toString(respObject));
        EsignResVo<EditTemplateResV3DataVo> resVo = new EsignResVo<EditTemplateResV3DataVo>().
                fillEsignResVo(respObject, EditTemplateResV3DataVo.class);
        log.info("[{}] 请求e签宝获取编辑合同模板页面返回封装Vo：{}", createTemplateReqVo.getDocTemplateId(), JsonUtil.toString(resVo));
        return resVo;
    }

    public EsignResVo assignedSignature(AssignedSignatureReqVo assignedSignatureReqVo) {
        log.info("[{}] 请求e签宝获取印章分页接口", assignedSignatureReqVo.getOrgId());
        JSONObject respObject = null;
        try {
            //发请求
            respObject = sendRequest(assignedSignatureReqVo, HttpMethod.GET.name(),
                    assignedSignatureReqVo.buildFullUrl(orgSealList), RequestConstant.REQUEST_TIMEOUT);
        } catch (Exception e) {
            log.info("[{}] 请求e签宝获取印章分页接口异常：", assignedSignatureReqVo.getOrgId(), e);
            EsignResVo<EditTemplateResV3DataVo> respVo = new EsignResVo<>();
            respVo.setRespStatus(SignChannelStatusEnum.FAIL.getValue());
            respVo.setMessage(respObject.get("message").toString());
            return respVo;
        }
        log.info("[{}] 请求e签宝获取印章分页接口返回原始结果：{}", assignedSignatureReqVo.getOrgId(), JsonUtil.toString(respObject));
        EsignResVo<EditTemplateResV3DataVo> resVo = new EsignResVo<EditTemplateResV3DataVo>().
                fillEsignResVo(respObject, EditTemplateResV3DataVo.class);
        log.info("[{}] 请求e签宝获取印章分页接口返回封装Vo：{}", assignedSignatureReqVo.getOrgId(), JsonUtil.toString(resVo));
        return resVo;
    }
}
