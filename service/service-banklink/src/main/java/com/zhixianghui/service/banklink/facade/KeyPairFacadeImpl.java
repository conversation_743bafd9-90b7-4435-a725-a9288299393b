package com.zhixianghui.service.banklink.facade;

import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.service.KeyPairRecordFacade;
import com.zhixianghui.service.banklink.core.biz.KeyPairRecordBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 秘钥对信息记录
 * <AUTHOR>
 * @date 2020/10/27
 **/
@Service
public class KeyPairFacadeImpl implements KeyPairRecordFacade {
    @Autowired
    private KeyPairRecordBiz keyPairRecordBiz;

    @Override
    public void updateOrInsertIfNotExist(List<KeyPairRecord> recordList) {
        keyPairRecordBiz.updateOrInsertIfNotExist(recordList);
    }

    @Override
    public KeyPairRecord getByChannelNoAndChannelMchNo(String channelNo, String channelMchNo) {
        return keyPairRecordBiz.getByChannelNoAndChannelMchNo(channelNo, channelMchNo);
    }
}
