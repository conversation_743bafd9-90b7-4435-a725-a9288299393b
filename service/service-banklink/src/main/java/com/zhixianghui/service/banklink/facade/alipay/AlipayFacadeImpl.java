package com.zhixianghui.service.banklink.facade.alipay;

import com.alipay.api.AlipayApiException;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawReqVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawRespVo;
import com.zhixianghui.facade.banklink.vo.report.AlipayJobPayslipSyncReqVo;
import com.zhixianghui.facade.banklink.vo.report.AlipayJobPayslipSyncRspVo;
import com.zhixianghui.service.banklink.request.alipay.AliPayService;
import com.zhixianghui.service.banklink.request.pay.AlipayBiz;
import com.zhixianghui.service.banklink.utils.alipay.AlipayUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AlipayFacadeImpl implements AlipayFacade {

    final private AlipayUtil alipayUtil;
    final private AliPayService aliPayService;
    final private AlipayBiz alipayBiz;

    public AlipayJobPayslipSyncRspVo jobPayslipSync(AlipayJobPayslipSyncReqVo reqVo) {
        return alipayBiz.jobPayslipSync(reqVo);
    }

    /**
     * 回调验签
     * @param params
     * @return
     */
    @Override
    public boolean verifyNotify(Map<String, String> params) {
        return alipayUtil.verify(params);
    }

    @Override
    public String accountBookCreate(String agreementNo, String merchantNo) {
        try {
            return aliPayService.accountBookCreate(agreementNo,merchantNo);
        } catch (AlipayApiException e) {
            log.error("记账本开通失败",e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("记账本开通失败" + e.getErrMsg());
        }
    }

    /**
     * 商户自身给记账本充值
     * @param agreementNo
     * @param accountBookId
     * @param aliUserId
     * @param amt
     * @param outBizNo
     * @return
     */
    @Override
    public String recharge(String agreementNo, String accountBookId, String aliUserId, String amt, String outBizNo,String expireTime) {
        try {
            String transPay = aliPayService.transPay(agreementNo, accountBookId, aliUserId, amt, outBizNo,expireTime);
            return transPay;
        } catch (AlipayApiException e) {
            throw CommonExceptions.BIZ_INVALID.newWith("调用【商户自身给记账本充值】失败", e);
        }
    }

    @Override
    public String sign(String extAgreementNo,Integer channelType) {
        try {
            return aliPayService.sign(extAgreementNo,channelType);
        } catch (AlipayApiException e) {
            log.error("签约失败", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约失败," + e.getErrMsg());
        }
    }

    @Override
    public String agreementQuery(String extAgreementNo, String agreementNo) {
        try{
            return aliPayService.agreementQuery(extAgreementNo,agreementNo);
        } catch (AlipayApiException e) {
            log.error("查询签约协议失败", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询失败" + e.getErrMsg());
        }
    }

    @Override
    public String accountBookQuery(String agreementNo, String accountBookId) {
        try {
            return aliPayService.accountBookQuery(agreementNo,accountBookId);
        }catch (AlipayApiException e){
            log.error("查询记账本信息失败", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询失败" + e.getErrMsg());
        }
    }

    @Override
    public void uniTransRefund(PayReqVo reqVo) {
        alipayBiz.uniTransRefund(reqVo);
    }

    @Override
    public String transCommonQuery(String outBizNo, String bizScene, String productCode) {
        try {
            return aliPayService.transCommonQuery(outBizNo,bizScene,productCode);
        } catch (AlipayApiException e) {
            e.printStackTrace();
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询失败" + e.getErrMsg());
        }
    }

    @Override
    public String transCommonQueryByOrderId(String orderId) throws Exception, BizException {
        return aliPayService.transCommonQueryByOrderId(orderId);
    }

    @Override
    public AuthRespVo auth(AuthReqVo reqVo) {
        return aliPayService.auth(reqVo);
    }

    @Override
    public String unsign(String extAgreementNo,String agreementNo) {
        try {
            return aliPayService.unsign(extAgreementNo,agreementNo);
        } catch (AlipayApiException e) {
			log.error("解除签约协议失败", e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("解约失败" + e.getErrMsg());
        }
    }

    @Override
    public String transPayQuery(String outBizNo){

        try {
            return aliPayService.transQuery(outBizNo);
        } catch (AlipayApiException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询充值记录失败" + e.getErrMsg());
        }
    }

    @Override
    public WithdrawRespVo withdraw(WithdrawReqVo withdrawReqVo) throws BizException {
        return alipayBiz.withdraw(withdrawReqVo);
    }

    @Override
    public String billApply(String key,String agreementNo) throws Exception, BizException {
        return aliPayService.billApply(key, agreementNo);
    }

    @Override
    public String billQuery(String fileId, String agreementNo) throws Exception, BizException {
        return aliPayService.billQuery(fileId, agreementNo);
    }

    @Override
    public void subscribe(String agreementNo, String accountBookId) throws Exception{
        aliPayService.subscribe(agreementNo,accountBookId);
    }

}
