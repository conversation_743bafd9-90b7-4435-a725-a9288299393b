package com.zhixianghui.service.banklink.listener;

import cn.hutool.core.lang.TypeReference;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.trade.vo.WeChatReceiptVo;
import com.zhixianghui.service.banklink.core.biz.receiptOrder.ReceiptOrderBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MiniAppListener
 * @Description TODO
 * @Date 2023/6/29 9:39
 */
@Slf4j
@Component
public class MiniAppListener {

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_MINIAPP_RECEIPT_ORDER,selectorExpression = MessageMsgDest.TAG_MINIAPP_RECEIPT_ORDER,consumeThreadMax = 5,consumerGroup = "miniAppReceiptOrderConsumer")
    public class MiniAppReceiptOrderListener extends BaseRocketMQListener<String>{

        @Autowired
        private ReceiptOrderBiz receiptOrderBiz;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            Map<String,Object> paramMap = JsonUtil.toBean(jsonParam,new TypeReference<HashMap<String,Object>>(){});
            receiptOrderBiz.downloadAndSendReceipt(paramMap);
        }
    }
}
