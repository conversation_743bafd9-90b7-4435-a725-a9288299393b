package com.zhixianghui.service.banklink.facade.pay;

import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.service.pay.PayBankFacade;
import com.zhixianghui.facade.banklink.vo.pay.*;
import com.zhixianghui.service.banklink.core.biz.PaySandBoxBiz;
import com.zhixianghui.service.banklink.request.pay.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 出款交易
 *
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Service(timeout = 20000)
@Slf4j
public class PayBankFacadeImpl implements PayBankFacade {
    @Autowired
    private JoinpayPayBiz joinpayPayBiz;
    @Autowired
    private JxhPayBiz jxhPayBiz;
    @Autowired
    private AlipayBiz alipayBiz;
    @Autowired
    private YishuiPayBiz yishuiPayBiz;
    @Autowired
    private WxPayBiz wxPayBiz;
    @Autowired
    private CmbPayBiz cmbPayBiz;
    @Autowired
    private PaySandBoxBiz paySandBoxBiz;

    /**
     * 不处理业务异常，往上抛出
     *
     * @param reqVo
     * @return
     * @throws BizException
     */
    @Override
    public PayRespVo pay(PayReqVo reqVo) throws BizException {
        PayRespVo respVo = paySandBoxBiz.handle(reqVo);
        if (respVo != null) {
            return respVo;
        }

        if (reqVo.getChannelType() == ChannelTypeEnum.BANK.getValue() && StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.JOINPAY.name())) {
            return joinpayPayBiz.pay(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.ALIPAY.name()) &&
                (reqVo.getChannelType() == ChannelTypeEnum.ALIPAY.getValue() || reqVo.getChannelType() == ChannelTypeEnum.BANK.getValue())) {
            return alipayBiz.pay(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.YISHUI.name())) {
            return yishuiPayBiz.pay(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.WXPAY.name())) {
            log.info("微信支付");
            return wxPayBiz.pay(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.CMB.name())) {
            return cmbPayBiz.pay(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.JOINPAY_JXH.name())) {
            return jxhPayBiz.singlePay(reqVo);
        } else {
            String errorInfo = "暂时不支持该支付通道:" + reqVo.getChannelType() + " - " + reqVo.getChannelNo();
            log.error(errorInfo);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(errorInfo);
        }

    }

    @Override
    public void refundFee(String platTrxNo, String payChannelNo, String chanelAcctNo, String mchAcctBookNo, String feeAmt) {
        if (StringUtils.equals(payChannelNo, ChannelNoEnum.CMB.name())) {
            cmbPayBiz.refundFee(platTrxNo, chanelAcctNo, mchAcctBookNo, feeAmt);
        }
    }

    @Override
    public PayRespVo queryPayOrder(QueryPayOrderReqVo reqVo) {
        if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.JOINPAY.name()) && reqVo.getChannelType() == ChannelTypeEnum.BANK.getValue()) {
            return joinpayPayBiz.queryPayOrder(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelTypeEnum.ALIPAY.name()) && (
                reqVo.getChannelType() == ChannelTypeEnum.BANK.getValue() || reqVo.getChannelType() == ChannelTypeEnum.ALIPAY.getValue())) {
            return alipayBiz.queryAliPayOrder(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.WXPAY.name()) && reqVo.getChannelType() == ChannelTypeEnum.WENXIN.getValue()) {
            log.info("进入微信查询");
            return wxPayBiz.queryOrder(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.CMB.name())) {
            return cmbPayBiz.queryOrder(reqVo);
        } else if (StringUtils.equals(reqVo.getChannelNo(), ChannelNoEnum.JOINPAY_JXH.name())){
            PayReqVo param = new PayReqVo();
            param.setChannelMchNo(reqVo.getChannelMchNo());
            param.setBankOrderNo(reqVo.getBankOrderNo());
//            param.setPlatTrxNo(reqVo.getBankOrderNo());
            param.setChannelNo(reqVo.getChannelNo());
            param.setChannelName(reqVo.getChannelName());
            param.setChannelMchNo(reqVo.getChannelMchNo());
            param.setKeyPairRecord(reqVo.getKeyPairRecord());
            return jxhPayBiz.queryPay(param);
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应通道");
        }
    }

    @Override
    public PayReceiveRespVo verifyAndHandleResult(PayReceiveReqVo reqVo) {
        return joinpayPayBiz.verifyAndHandleResult(reqVo);
    }

    @Override
    public PayRespVo queryAliPayOrder(QueryPayOrderReqVo queryPayOrderReqVo) throws BizException {
        return alipayBiz.queryAliPayOrder(queryPayOrderReqVo);
    }

    @Override
    public void payRefund(PayReqVo payReqVo) {
        alipayBiz.uniTransRefund(payReqVo);
    }

    /***
     * 处理君享汇下发异步通知
     * @param reqVo
     * @return
     */
    @Override
    public SinglePayReceiveRespVo verifySinglePayAndHandleResult(PayReceiveReqVo reqVo) {
        return jxhPayBiz.verifySinglePayAndHandleResult(reqVo);
    }
}
