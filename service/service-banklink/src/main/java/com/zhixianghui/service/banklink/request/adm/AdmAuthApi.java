package com.zhixianghui.service.banklink.request.adm;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.auth.AuthChannelEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.exception.QpsExceptions;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.service.banklink.request.pay.ConvertMessageBiz;
import com.zhixianghui.service.banklink.utils.aidongman.ThreeDesUtil;
import com.zhixianghui.starter.comp.component.RedisRateLimit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Service
public class AdmAuthApi {
    @Value(value = "${ProtocolVersion}")
    private String protocolVersion;
    @Autowired
    private ConvertMessageBiz convertMessageBiz;

    @Autowired
    private RedisRateLimit redisRateLimit;

    private final static String PRIVATE_KEY = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAN0GDcwJzyHaNuup7/oxTnbcetyjfyaTISJYkSkQ5r03N179IwZvo4CvCxVnu+O0zOHvAHkOdJu24G6vyiwzkSYrU+T/ck6I6Fyc6edxsVL4T4qX2Z6lenlXWL66LLb4NUcCUZDl3/9FSz/A65KQb6slsO61LGdBwWn04Qk+2z0tAgMBAAECgYEA1StJk0jCpXu5DIHSGhQEAU+Uj8iAkcvFYULafij24fenR84NQd/aafppTjPGW0+9awsAAcGXZbdzrk2NSCRUEBhJ5JfbXaFMSa2SLPnxv0peiFOsaErUeEXbPToMKMgQ6r5ZtZEcmvSo45PWNYzEVlDbqTxJCMAq35hunfP/FwECQQD+YyLWm2Ve6/UzZ+Hh3270MqKAiCLFhVyKtgEjkXpVLDN9n2hJ3WT03j6/h8Ehzrjj6x0QsN842aCEeFYvPUQ5AkEA3mzFAPQ+bKjMI6LEzthNE2HxL9OfW2LG9bS165aoKJXY19nmFC0xjoXawM68U7pu8nrFEjclHBrBKdjQNhbIlQJBALdkowYSi5dOBrzjpH3hRa05H9wHyQGbLmc15UZOJ+Jldk2XcWjm+lRqMNSFux9zV+nAY3jX+mWSx10ZIBD4W0kCQHM/3NxfsjDPTedxIZUkEtvmNfjYKIxLpFgQKOtHhx5wdolxyHvP5wu4Y97fwEmKIo7V+mMz27CyVECArXI5X/kCQQDVo83gYxRkgxO/2Pd17DqHsBMLlqo+A6ML/GSppWicIXs80sA4AymuBD/buQDkefGzg1tPKLrNYDbG/NK5jVyp";
    public static final String PLAT_PUB_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCsrfiHyLpfi2Hu6Cldn0ha77qLoclaLRul50QouAllnUqrB5Q4KwddhxhMXuOmGEseUEKVjWXZ1BYUIcteohioaywpIuJ+CcyPRFwwnZJ9mziMSafuFnpMgJRD/b5g1hO7yB3gOHIzJigZ95t5N8XfUNjyns5UyoQQqFPGR/UMGwIDAQAB";
    private final static String DES_KEY = "1234@@abcd1234@@abcd$$$$";
    private final static String BANK_URL = "http://skapi.amicloud.cn:8880/v2.0/realname/***********";
    private final static String CERT_URL = "http://skapi.amicloud.cn:8880/v2.0/idcard/***********";
    private final static String OPERATOR_URL = "http://skapi.amicloud.cn:8880/v1.0/operator/***********";
    private final static String PLAT_MCH_NO = "***********";

    public AuthRespVo auth(AuthReqVo authReqVo) {

        AuthRespVo authRespVo = null;
        if (AuthTypeEnum.IDCARD_NAME_BANKCARD.getValue() == authReqVo.getAuthType()) {
            authRespVo = this.idcardNameBankcard(authReqVo);
        } else if (AuthTypeEnum.IDCARD_NAME_PHONE.getValue() == authReqVo.getAuthType()) {
            authRespVo = this.idcardNamePhone(authReqVo);
        } else if (AuthTypeEnum.IDCARD_NAME.getValue() == authReqVo.getAuthType()) {
            authRespVo = this.idcardName(authReqVo);
        } else if (AuthTypeEnum.IDCARD_NAME_BANKCARD_PHONE.getValue() == authReqVo.getAuthType()) {
            authRespVo = this.idcardNameBankcardPhone(authReqVo);
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持该鉴权类型");
        }

        return authRespVo;
    }

    private AuthRespVo idcardNameBankcard(AuthReqVo authReqVo) {

        final Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>())
                .put("out_trade_no", authReqVo.getBankOrderNo())
                .put("tran_time", LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .put("customer_code", PLAT_MCH_NO)
                .put("verify_type", "0030")
                .put("acct_no", authReqVo.getBankAccountNo())
                .put("name", authReqVo.getName())
                .put("cert_no", authReqVo.getIdCardNo())
                .put("bsn_info", getBsnInfo(authReqVo))
                .put("risk_info", getRiskInfo())
                .build();

        return this.doAuth(BANK_URL, params);
    }

    private AuthRespVo idcardNamePhone(AuthReqVo authReqVo) {

        final Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>())
                .put("out_trade_no", authReqVo.getBankOrderNo())
                .put("tran_time", LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .put("customer_code", PLAT_MCH_NO)
                .put("verify_type", "0130")
                .put("name", authReqVo.getName())
                .put("cert_no", authReqVo.getIdCardNo())
                .put("phone", authReqVo.getPhoneNo())
                .put("cert_type", "01")
                .put("bsn_info", getBsnInfo(authReqVo))
                .put("risk_info", getRiskInfo())
                .build();

        return this.doAuth(OPERATOR_URL, params);
    }

    private AuthRespVo idcardNameBankcardPhone(AuthReqVo authReqVo) {

        final Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>())
                .put("out_trade_no", authReqVo.getBankOrderNo())
                .put("tran_time", LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .put("customer_code", PLAT_MCH_NO)
                .put("verify_type", "0040")
                .put("acct_no", authReqVo.getBankAccountNo())
                .put("name", authReqVo.getName())
                .put("cert_no", authReqVo.getIdCardNo())
                .put("phone", authReqVo.getPhoneNo())
                .put("bsn_info", getBsnInfo(authReqVo))
                .put("risk_info", getRiskInfo())
                .build();

        return this.doAuth(BANK_URL, params);
    }

    private AuthRespVo idcardName(AuthReqVo authReqVo) {

        final Map<String, Object> params = MapUtil.builder(new LinkedHashMap<String, Object>())
                .put("out_trade_no", authReqVo.getBankOrderNo())
                .put("tran_time", LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .put("verify_type", "0220")
                .put("name", authReqVo.getName())
                .put("cert_no", authReqVo.getIdCardNo())
                .build();

        return this.doAuth(CERT_URL, params);
    }

    private AuthRespVo doAuth(String url, Map<String, Object> params) {
        Long token = redisRateLimit.getLimitToken("ADM:REQUEST:LIMIT", "10", "10");
        if (token == null || token == 0L) {
            throw QpsExceptions.QPS_NEED_BLOCK_EX.newWithErrMsg("触发爱动漫限流");
        }

        Map<String, Object> toSignParam = new LinkedHashMap<>();
        params.forEach((key, val) -> {
            toSignParam.put(key.toUpperCase(), val);
        });

        String sortJoin = null;
        if (StringUtils.equals(String.valueOf(params.get("verify_type")), "0220")) {
            sortJoin = MapUtil.join(toSignParam, "&", "=", true);
        }else {
            sortJoin= MapUtil.sortJoin(toSignParam, "&", "=", true);
        }
        final String sign = RSAUtil.sign(sortJoin, PRIVATE_KEY, true);

        params.put("sign", sign);
        params.put("customer_code", PLAT_MCH_NO);
        log.info("爱动漫鉴权-鉴权号:{} 发送报文：\n{}", params.get("out_trade_no"), JSONUtil.toJsonPrettyStr(params));
        final String str = JSONUtil.toJsonStr(params);
//        final DESede des = new DESede(Mode.ECB.name(), "pkcs7padding", DES_KEY.getBytes());
//        final String encryptHex = des.encryptHex(str);
        final byte[] bytes = ThreeDesUtil.encryptMode(DES_KEY.getBytes(), str.getBytes());

        JSONObject respObject = null;
        try {
            WebClient webClient = WebClient.create(url);
            Mono<String> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(ThreeDesUtil.byte2Hex(bytes))).retrieve().bodyToMono(String.class);
            respObject = JSONObject.parseObject(mono.block(Duration.ofMillis(10000)));
        } catch (Exception e) {
            log.info("[{}] 请求爱动漫鉴权接口异常：", params.get("out_trade_no"), e);
            AuthRespVo respVo = new AuthRespVo();
            respVo.setAuthStatus(BankAuthStatusEnum.UN_KNOW.getValue());
            respVo.setBizDesc("[鉴权失败]-"+e.getMessage());
            respVo.setChannelNo(AuthChannelEnum.ADM.name());
            return respVo;
        }
        if (respObject != null) {
            log.info("爱动漫鉴权-鉴权号:{},接口返参：{}", params.get("out_trade_no"), respObject.toJSONString());
            if (StringUtils.equals(respObject.getString("code"), "0000")) {
                AuthRespVo authRespVo = new AuthRespVo();
                authRespVo.setAuthStatus(BankAuthStatusEnum.SUCCESS.getValue());
                authRespVo.setChannelNo(AuthChannelEnum.ADM.name());
                authRespVo.setBizDesc(respObject.getString("message"));

                return authRespVo;
            }else {
                AuthRespVo authRespVo = new AuthRespVo();
                authRespVo.setAuthStatus(BankAuthStatusEnum.FAIL.getValue());
                authRespVo.setChannelNo(AuthChannelEnum.ADM.name());
                String rb_msg = convertMessageBiz.getAdmAuthMsg(respObject.getString("message"), respObject.getString("code"));
                authRespVo.setBizDesc(rb_msg);
                authRespVo.setBizCode(respObject.getString("code"));

                if (StringUtils.equals(respObject.getString("code"), "1000")
                ||StringUtils.equals(respObject.getString("code"), "1200")
                ||StringUtils.equals(respObject.getString("code"), "1201")
                ||StringUtils.equals(respObject.getString("code"), "1299")
                ||StringUtils.equals(respObject.getString("code"), "1099")){
                    authRespVo.setAuthStatus(BankAuthStatusEnum.SYSTEM_ERROR.getValue());
                }

                return authRespVo;
            }
        }else {
            AuthRespVo authRespVo = new AuthRespVo();
            authRespVo.setAuthStatus(BankAuthStatusEnum.UN_KNOW.getValue());
            authRespVo.setChannelNo(AuthChannelEnum.ADM.name());
            authRespVo.setBizDesc("[鉴权失败]-鉴权未知异常");
            authRespVo.setBizCode("99999");

            return authRespVo;
        }
    }

    private String getBsnInfo(AuthReqVo authReqVo) {
        TreeMap map = MapUtil.sort(
                MapUtil.builder(new HashMap<String, Object>())
                        .put("bsn_type", "02")
                        .put("bsn_scene", "99")
                        .put("bsn_scene_desc", "灵工结算")
                        .put("protocol_version",protocolVersion)
                        .put("protocol_no",authReqVo.getSerialNo())
                        .build()
        );
        return JSONUtil.toJsonStr(map);
    }

    public String getRiskInfo() {
        TreeMap map = MapUtil.sort(
                MapUtil.builder(new HashMap<String, Object>())
                        .put("app_name", "智享汇")
                        .put("ip_type", "04")
                        .put("source_ip", "***************")
                        .build()
        );
        return JSONUtil.toJsonStr(map);
    }

    public static void main(String[] args) {
        AuthReqVo authReqVo = new AuthReqVo();
        authReqVo.setAuthType(AuthTypeEnum.IDCARD_NAME_BANKCARD_PHONE.getValue());
        authReqVo.setBankOrderNo(RandomUtil.get16LenStr());
        authReqVo.setName("普海涛");
//        authReqVo.setIdCardNo("530427198609100534");
        authReqVo.setIdCardNo("530427198707100534");
        authReqVo.setPhoneNo("***********");
        authReqVo.setBankAccountNo("6230580000048295914");
        authReqVo.setSerialNo(RandomUtil.get16LenStr());


        AdmAuthApi admAuthApi = new AdmAuthApi();
        admAuthApi.auth(authReqVo);

    }
}
