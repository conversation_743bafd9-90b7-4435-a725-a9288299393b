package com.zhixianghui.service.banklink.aspect;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import com.zhixianghui.service.banklink.helper.CacheBiz;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 设置秘钥对
 * <AUTHOR>
 */
@Component
@Aspect
public class KeyPairAspect {
    @Autowired
    private CacheBiz cacheBiz;

    @Pointcut("@annotation(com.zhixianghui.service.banklink.annotation.CurrentKeyPair)")
    public void setKeyPairPointcut(){

    }

    @Before("setKeyPairPointcut()")
    public void setKeyPair(JoinPoint point) {
        Object[] args = point.getArgs();
        for (Object obj:args){
            if(obj instanceof BaseKeyPairReqVo){
                BaseKeyPairReqVo reqVo = (BaseKeyPairReqVo) obj;
                if(StringUtil.isEmpty(reqVo.getChannelNo())
                        || StringUtil.isEmpty(reqVo.getChannelMchNo())){
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("渠道编号或渠道商户编号不能为空");
                }
                KeyPairRecord keyPairRecord = cacheBiz.getByChannelNoAndChannelMchNo(reqVo.getChannelNo(), reqVo.getChannelMchNo());
                LimitUtil.notEmpty(keyPairRecord, String.format("通道编号：%s,通道商户编号：%s,获取不到秘钥信息", reqVo.getChannelNo(), reqVo.getChannelMchNo()));

                reqVo.setKeyPairRecord(keyPairRecord);
                break;
            }
        }
    }
}