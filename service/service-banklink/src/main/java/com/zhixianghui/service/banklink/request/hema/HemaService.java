package com.zhixianghui.service.banklink.request.hema;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.facade.banklink.enums.HemaCheckStatusEnum;
import com.zhixianghui.facade.banklink.vo.hema.HemaBaseResponse;
import com.zhixianghui.facade.banklink.vo.hema.PayCallbackReqVo;
import com.zhixianghui.facade.banklink.vo.hema.PaymentReqVo;
import com.zhixianghui.service.banklink.config.HemaConfig;
import com.zhixianghui.service.banklink.utils.hema.HemaHttpUtil;
import com.zhixianghui.service.banklink.utils.hema.HemaRSAUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName HemaService
 * @Description TODO
 * @Date 2023/9/27 14:38
 */
@Slf4j
@Service
public class HemaService {

    @Autowired
    private HemaConfig hemaConfig;

    public void payCallback(PayCallbackReqVo payCallbackReqVo) throws BizException,Exception {
        String json = JsonUtil.toString(payCallbackReqVo);
        log.info("请求河马结算回调接口，订单号：[{}]，请求参数：[{}]",payCallbackReqVo.getThirdOrderId(),json);
        String resData = doPost(hemaConfig.getPayCallBackUrl(),json);
        log.info("河马结算回调接口返回，订单号：[{}]，返回参数：[{}]",payCallbackReqVo.getThirdOrderId(),resData);
    }

    /**
     * 结算接口
     * @param paymentReqVo
     */
    public void payment(PaymentReqVo paymentReqVo) throws BizException,Exception {
        String json = JsonUtil.toString(paymentReqVo);
        log.info("请求河马结算接口，订单号：[{}]，请求参数：[{}]",paymentReqVo.getThirdOrderId(),json);
        String resData = doPost(hemaConfig.getPaymentUrl(),json);
        JSONObject jsonObject = JSON.parseObject(resData);
        Integer checkStatus = (Integer) jsonObject.get("checkStatus");
        String reason = (String) jsonObject.get("reason");
        log.info("河马结算接口返回，订单号：[{}]，请求参数：[{}]",paymentReqVo.getThirdBizOrderId(),jsonObject.toJSONString());
        if (checkStatus.intValue() == HemaCheckStatusEnum.FAIL.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(reason);
        }
    }

    /**
     * 查询商户合作信息接口
     */
    public void queryInvoice(){
        JSONObject jsonObject = new JSONObject();
        try {
            String resData = doPost(hemaConfig.getQueryInvoiceUrl(),jsonObject.toJSONString());
            log.info("请求河马查询商户合作信息接口，响应参数：[{}]",resData);
        } catch (BizException e) {
            e.printStackTrace();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public String doPost(String url,String jsonParam) throws Exception {
        //对请求体进行加密
        String encryetParam = HemaRSAUtil.encryptByPublicKey(jsonParam,hemaConfig.getPublicKey());
        String timeStamp = String.valueOf(System.currentTimeMillis());
        //对请求参数进行签名
        //注意SecretKey后没有“=”
        String sign = MD5Util.getMD5Hex(encryetParam
            + "&Timestamp=" + timeStamp
            + "&AppKey=" + hemaConfig.getAppKey()
            + "&Version=1.0"
            + "&SecretKey" + hemaConfig.getSecretKey());
        String resData = HemaHttpUtil.post(url,encryetParam,hemaConfig.getAppKey(),timeStamp,sign);
        HemaBaseResponse hemaBaseResponse = JsonUtil.toBean(resData,HemaBaseResponse.class);
        log.info("河马请求返回参数：[{}]",JsonUtil.toString(hemaBaseResponse));
        //响应成功
        if (hemaBaseResponse.getStatus() == 0) {
            //业务报文（密文）
            String data = hemaBaseResponse.getData();
            if (data == null || "".equals(data)) {
                return null;
            }
            //解密
            String dec = HemaRSAUtil.decryptByPublicKey(data, hemaConfig.getPublicKey());
            return dec;
        }else{
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("河马接口请求失败：" + hemaBaseResponse.getErrorMessage());
        }
    }
}
