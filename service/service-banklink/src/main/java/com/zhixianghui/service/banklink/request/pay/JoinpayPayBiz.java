package com.zhixianghui.service.banklink.request.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.exception.BanklinkExceptions;
import com.zhixianghui.common.util.utils.HEXUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.vo.pay.*;
import com.zhixianghui.service.banklink.annotation.CurrentKeyPair;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import com.zhixianghui.service.banklink.utils.joinpay.AESUtil;
import com.zhixianghui.service.banklink.utils.joinpay.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Objects;
import java.util.TreeMap;

/**
 * 汇聚支付出款交易
 *
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Component
@Slf4j
public class JoinpayPayBiz extends ConvertMessageBiz {

    // 付款成功
    private static final Integer SUCCESS_PAY_STATUS = 100;
    // 付款失败
    private static final Integer FAIL_PAY_STATUS = 101;
    // 付款处理中
    private static final Integer PROCESS_PAY_STATUS = 102;
    // 成功请求响应码
    private static final String SUCCESS_RESP_CODE = "A1000";
    // 系统异常响应码
    private static final String SYSTEM_EXCEPTION_CODE = "B113000";
    // 订单号不存在错误码
    private static final String ORDER_NOT_EXSIT_CODE = "B113007";
    // 订单号重复码
    private static final String ORDER_NO_REPEAT_CODE = "B113006";

    @Value(value = "${joinpayPayUrl}")
    private String joinpayPayUrl;
    @Value(value = "${joinpayPayNotifyUrl}")
    private String joinpayPayNotifyUrl;

    /**
     * 付款请求
     *
     * @param reqVo
     * @return
     */
    @CurrentKeyPair
    public PayRespVo pay(PayReqVo reqVo) {
        // 封装请求参数并算签
        TreeMap<String, Object> map = fillPayParam(reqVo);

        // 请求
        log.info("[{}] 请求汇聚分账方代付接口参数：{}", reqVo.getBankOrderNo(), JsonUtil.toString(map));
        JSONObject respObject = null;
        try {
            WebClient webClient = WebClient.create(joinpayPayUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        } catch (Exception e) {
            log.info("[{}] 请求汇聚分账方代付接口异常：", reqVo.getBankOrderNo(), e);
            PayRespVo respVo = new PayRespVo();
            respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
            return respVo;
        }

        log.info("[{}] 请求汇聚分账方代付接口返回原始结果：{}", reqVo.getBankOrderNo(), JsonUtil.toString(respObject));
        return fillPayRespVo(respObject);
    }
    /**
     * 查询付款请求结果
     *
     * @param reqVo
     * @return
     */
    @CurrentKeyPair
    public PayRespVo queryPayOrder(QueryPayOrderReqVo reqVo) {
        // 封装请求参数并算签
        TreeMap<String, Object> map = fillQueryPayOrderParam(reqVo);

        // 请求
        log.info("[{}] 汇聚分账方代付结果查询接口请求参数：{}", reqVo.getBankOrderNo(), JsonUtil.toString(map));
        WebClient webClient = WebClient.create(joinpayPayUrl);
        Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
        JSONObject respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        log.info("[{}] 请求汇聚分账方代付结果查询接口返回原始结果：{}", reqVo.getBankOrderNo(), JsonUtil.toString(respObject));
        return fillQueryPayRespVo(respObject);
    }

    /**
     * 对回调结果进行验签并封装结果
     * @param reqVo
     * @return
     */
    @CurrentKeyPair
    public PayReceiveRespVo verifyAndHandleResult(PayReceiveReqVo reqVo) {
        TreeMap<String, Object> resultMap = JsonUtil.toBean(JsonUtil.toString(reqVo.getRespContent()), new TypeReference<TreeMap<String, Object>>() {});
        String sign = resultMap.get("sign").toString();
        boolean isVerifyPass = SignUtil.verify_2_0(resultMap, reqVo.getKeyPairRecord().getMchPrivateKeyDecrypt(), sign);
        if (!isVerifyPass) {
            log.error("汇聚分账方代付回调验签失败，报文：{}", JsonUtil.toString(reqVo.getRespContent()));
            throw BanklinkExceptions.VERIFY_SIGN_FAIL.newWithErrMsg("验证签名失败");
        }
        return fillPayReceiveRespVo(reqVo.getRespContent());
    }

    private PayReceiveRespVo fillPayReceiveRespVo(JSONObject respObject) {
        PayReceiveRespVo respVo = new PayReceiveRespVo();
        JSONObject dataObj = respObject.getJSONObject("data");
        if (dataObj != null) {
            Integer payStatus = dataObj.getInteger("pay_status");
            if (Objects.equals(SUCCESS_PAY_STATUS, payStatus)) {
                respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
            } else if (Objects.equals(FAIL_PAY_STATUS, payStatus)) {
                respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            }
            respVo.setBankOrderNo(dataObj.getString("mch_order_no"));
            respVo.setBankTrxNo(dataObj.getString("plat_trx_no"));
            respVo.setBizCode(dataObj.getString("biz_code"));
            respVo.setBizMsg(dataObj.getString("biz_msg"));
        } else {
            respVo.setBizCode(respObject.getString("resp_code"));
            respVo.setBizMsg(respObject.getString("resp_msg"));
        }
        // 返回系统异常时未知实际处理结果
        if (respVo.getBankPayStatus() == null
                && SYSTEM_EXCEPTION_CODE.equals(respVo.getBizCode())) {
            respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
        }
        convert(respVo);
        return respVo;
    }

    private PayRespVo fillQueryPayRespVo(JSONObject respObject) {
        PayRespVo respVo = new PayRespVo();
        if (respObject == null) {
            respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        JSONObject dataObj = respObject.getJSONObject("data");
        if (dataObj != null) {
            Integer payStatus = dataObj.getInteger("pay_status");
            if (Objects.equals(PROCESS_PAY_STATUS, payStatus)) {
                respVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
            } else if (Objects.equals(SUCCESS_PAY_STATUS, payStatus)) {
                respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
            } else if (Objects.equals(FAIL_PAY_STATUS, payStatus)) {
                respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            }
            respVo.setBankOrderNo(dataObj.getString("mch_order_no"));
            respVo.setBankTrxNo(dataObj.getString("plat_trx_no"));
            respVo.setBizCode(dataObj.getString("biz_code"));
            respVo.setBizMsg(dataObj.getString("biz_msg"));
        } else {
            respVo.setBizCode(respObject.getString("resp_code"));
            respVo.setBizMsg(respObject.getString("resp_msg"));
        }
        if (ORDER_NOT_EXSIT_CODE.equals(respVo.getBizCode())) {
            respVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()));
        } else if (ORDER_NO_REPEAT_CODE.equals(respVo.getBizCode())) {
            respVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NO_REPEAT.getSysErrorCode()));
        }
        // 返回系统异常时未知实际处理结果
        if (respVo.getBankPayStatus() == null) {
            if (SYSTEM_EXCEPTION_CODE.equals(respVo.getBizCode())) {
                respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
            } else {
                respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            }
        }
        convert(respVo);
        return respVo;
    }

    private PayRespVo fillPayRespVo(JSONObject respObject) {
        PayRespVo respVo = new PayRespVo();
        if (respObject == null) {
            respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
            return respVo;

        }
        JSONObject dataObj = respObject.getJSONObject("data");
        if (dataObj != null) {
            Integer payStatus = dataObj.getInteger("pay_status");
            if (Objects.equals(PROCESS_PAY_STATUS, payStatus)) {
                respVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
            }
            respVo.setBankOrderNo(dataObj.getString("mch_order_no"));
            respVo.setBankTrxNo(dataObj.getString("plat_trx_no"));
            respVo.setBizCode(dataObj.getString("biz_code"));
            respVo.setBizMsg(dataObj.getString("biz_msg"));
        } else {
            respVo.setBizCode(respObject.getString("resp_code"));
            respVo.setBizMsg(respObject.getString("resp_msg"));
        }
        if (ORDER_NOT_EXSIT_CODE.equals(respVo.getBizCode())) {
            respVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()));
        } else if (ORDER_NO_REPEAT_CODE.equals(respVo.getBizCode())) {
            respVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NO_REPEAT.getSysErrorCode()));
        }
        // 返回系统异常时未知实际处理结果
        if (respVo.getBankPayStatus() == null) {
            if (SYSTEM_EXCEPTION_CODE.equals(respVo.getBizCode())) {
                respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
            } else {
                respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            }

        }
        convert(respVo);
        return respVo;
    }

    private TreeMap<String, Object> fillPayParam(PayReqVo reqVo) {
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("method", "altPay.pay");
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", reqVo.getChannelMchNo());

        String pwd = RandomUtil.get16LenStr();
        String iv = RandomUtil.get16LenStr();
        map.put("aes_key", HEXUtil.encode(RSAUtil.encryptByPublicKey(pwd + ":" + iv, reqVo.getKeyPairRecord().getChannelPublicKeyDecrypt())));

        TreeMap<String, Object> dataMap = new TreeMap<>();
        dataMap.put("mch_order_no", reqVo.getBankOrderNo());
        dataMap.put("alt_mch_no", reqVo.getPayerChannelAccountNo());
        dataMap.put("receive_name", AESUtil.encrypt(reqVo.getReceiveName(), pwd, iv));
        dataMap.put("receive_account_no", AESUtil.encrypt(reqVo.getReceiveAccountNo(), pwd, iv));
        dataMap.put("receive_amount", reqVo.getReceiveAmount());
        dataMap.put("plat_mch_fee", reqVo.getServiceFee());
        dataMap.put("remit_remark", reqVo.getRemitRemark());
        dataMap.put("callback_url", joinpayPayNotifyUrl);
        map.put("data", JSON.toJSON(dataMap));

        // 算签
        map.put("sign", SignUtil.genSign_2_0(map, reqVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    private TreeMap<String, Object> fillQueryPayOrderParam(QueryPayOrderReqVo reqVo) {
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("method", "altPay.queryOrder");
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", reqVo.getChannelMchNo());

        String pwd = RandomUtil.get16LenStr();
        String iv = RandomUtil.get16LenStr();
        map.put("aes_key", HEXUtil.encode(RSAUtil.encryptByPublicKey(pwd + ":" + iv, reqVo.getKeyPairRecord().getChannelPublicKeyDecrypt())));

        TreeMap<String, Object> dataMap = new TreeMap<>();
        dataMap.put("mch_order_no", reqVo.getBankOrderNo());
        map.put("data", JSON.toJSON(dataMap));

        // 算签
        map.put("sign", SignUtil.genSign_2_0(map, reqVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }




}
