package com.zhixianghui.service.banklink.request.cmb;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.vo.BaseKeyPairReqVo;
import com.zhixianghui.service.banklink.annotation.CurrentKeyPair;
import com.zhixianghui.service.banklink.config.CmbConfig;
import okhttp3.FormBody;
import okhttp3.FormBody.Builder;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class CmbExchangeUtil {

    private static Logger logger = LoggerFactory.getLogger(CmbExchangeUtil.class);
    private static java.util.Base64.Encoder encoder = java.util.Base64.getEncoder();
    private static java.util.Base64.Decoder decoder = java.util.Base64.getDecoder();
    @Autowired
    private CmbConfig cmbConfig;

    @CurrentKeyPair
    public JSONObject doRequest(String api, Dict body, BaseKeyPairReqVo baseKeyPairReqVo) {
        return this.doRequest(api, body, baseKeyPairReqVo, true);
    }

    @CurrentKeyPair
    public JSONObject doRequest(String api, Dict body, BaseKeyPairReqVo baseKeyPairReqVo,boolean printLog) {

        KeyPairRecord keyPairRecord = baseKeyPairReqVo.getKeyPairRecord();
        String uid = keyPairRecord.getChannelLoginUserDecrypt();
        //移除参数中的空字符串
        Map<String, Object> bodyParams = MapUtil.removeNullValue(body);

        JSONObject request = new JSONObject();

        Date now = new Date();
        /**
         * 组装head
         */
        JSONObject head = new JSONObject();
        head.put("funcode", api);
        head.put("userid", keyPairRecord.getChannelLoginUserDecrypt());
        head.put("reqid", DateUtil.format(now, DatePattern.PURE_DATETIME_MS_PATTERN) + IdUtil.fastSimpleUUID());

        /**
         * 组装signature
         */
        Dict signature = new Dict();
        signature.put("sigtim", DateUtil.format(now, DatePattern.PURE_DATETIME_PATTERN));
        signature.put("sigdat", "__signature_sigdat__");

        /**
         * 组装request
         */
        request.put("body", MapUtil.sort(bodyParams));
        request.put("head", MapUtil.sort(head));


        TreeMap<String, Object> sortedsignature = MapUtil.sort(signature);
        JSONObject params = new JSONObject();
        params.put("request", MapUtil.sort(request));
        params.put("signature", sortedsignature);
        try {
            /**
             * params排序
             */
            TreeMap<String, Object> sortedParams = MapUtil.sort(params);
            String paramstrToSign = StrUtil.removeAllLineBreaks(StrUtil.cleanBlank(JSON.toJSONString(sortedParams)));

            byte[] signature1 = DCCryptor.CMBSM2SignWithSM3(getID_IV(uid), decoder.decode(keyPairRecord.getMchPrivateKeyDecrypt()), paramstrToSign.getBytes(StandardCharsets.UTF_8));
            String mchSignStr = new String(encoder.encode(signature1));

//            byte[] signature2 = DCCryptor.CMBSM2SignWithSM3(getID_IV(uid), decoder.decode(keyPairRecord.getMchPublicKeyDecrypt()), mchSignStr.getBytes(StandardCharsets.UTF_8));
//            String platSignStr = new String(encoder.encode(signature2));

            sortedsignature.put("sigdat", mchSignStr);
            LinkedHashMap<String, Object> signInfo = new LinkedHashMap<>(sortedsignature);
//            signInfo.put("paltsigdat", platSignStr);
            params.put("signature", signInfo);

            String paramstrToEncrypt = StrUtil.cleanBlank(StrUtil.removeAllLineBreaks(JSON.toJSONString(params)));
            logger.info("招行接口[{}]请求参数：{}",api, JSON.toJSONString(params));
            byte[] enInput = DCCryptor.CMBSM4EncryptWithCBC(keyPairRecord.getChannelPublicKeyDecrypt().getBytes(), getID_IV(uid), paramstrToEncrypt.getBytes(StandardCharsets.UTF_8));
            String encrptedparam = new String(encoder.encode(enInput));

            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .writeTimeout(10, TimeUnit.SECONDS)
                    .readTimeout(10, TimeUnit.SECONDS)
                    .build();

            FormBody.Builder formBodyBuilder = new Builder();

            Map<String, String> bodyContent = new HashMap<>();
            bodyContent.put("UID", keyPairRecord.getChannelLoginUserDecrypt());
            bodyContent.put("FUNCODE", api);
//            bodyContent.put("INSPLAT", keyPairRecord.getChannelPlatNo().split(":")[0]);
            bodyContent.put("ALG", "SM");
            bodyContent.put("DATA", encrptedparam);

            bodyContent.forEach((k,v)->{
                formBodyBuilder.add(k, v);
            });
            final FormBody formBody = formBodyBuilder.build();

            final Request httpRequest = new Request.Builder().url(cmbConfig.getServerHost()).post(formBody).build();

            logger.info("\nhttp url:{}\nhttp method:{}\nhttp headers\nhttp body:{}",httpRequest.url(),httpRequest.method(), JsonUtil.toString(httpRequest.headers().toMultimap()),JsonUtil.toString(bodyContent));

            Response response = httpClient.newCall(httpRequest).execute();
            if (response.isSuccessful()) {
                String decryptStr = new String(DCCryptor.CMBSM4DecryptWithCBC(keyPairRecord.getChannelPublicKeyDecrypt().getBytes(), getID_IV(uid), decoder.decode(response.body().string())), StandardCharsets.UTF_8);
//                String decryptStr = symmetricCrypto.decryptStr(response.body().string());
                if (printLog) {
                    logger.info("招行接口[{}]返回参数：{}",api, decryptStr);
                }
                JSONObject respObj = JSON.parseObject(decryptStr).getJSONObject("response");
                if (!StringUtils.equals(respObj.getJSONObject("head").getString("resultcode"), "SUC0000")) {
                    String errMsg = respObj.getJSONObject("head").getString("resultmsg");
                    String errcode = respObj.getJSONObject("head").getString("resultcode");
                    logger.error("[招行接口[{}]业务失败]-{}",api, errMsg);
                    if (StringUtils.contains(errMsg, "您在固定时间内并发请求次数超限")
                    || StringUtils.contains(errMsg,"操作过于频繁，请稍后重试")) {
                        throw new RuntimeException("系统繁忙，请稍后重试");
                    }
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("{}::{}", errcode, errMsg));
                }

                JSONObject respBody = respObj.getJSONObject("body");
                return respBody;
            }else {
                logger.info("api:[{}],错误码:{},response.body:{}",api,response.code(), response.body().string());
                throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("请求通道招行失败:" + response.message());
            }
        }catch (BizException e){
            logger.error("请求招行通道业务异常",e);
            throw e;
        }catch (Exception e) {
            logger.error("请求招行通道未知异常",e);
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg(e.getMessage());
        }
    }

    private static byte[] getID_IV(String uid) {
        String userid = uid + "0000000000000000";
        return userid.substring(0, 16).getBytes();
    }

}
