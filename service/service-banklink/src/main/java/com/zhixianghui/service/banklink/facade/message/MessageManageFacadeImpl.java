package com.zhixianghui.service.banklink.facade.message;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.banklink.entity.MailReceiver;
import com.zhixianghui.facade.banklink.service.message.MessageManageFacade;
import com.zhixianghui.service.banklink.core.biz.message.email.MailReceiveBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class MessageManageFacadeImpl implements MessageManageFacade {
    @Autowired
    MailReceiveBiz mailReceiveBiz;

    public boolean addMailReceiver(MailReceiver mailReceiver){
        return mailReceiveBiz.addMailReceiver(mailReceiver);
    }

    public boolean editMailReceiver(Long id, String from, String to, String remark){
        return mailReceiveBiz.editMailReceiver(id, from, to, remark);
    }

    public boolean deleteMailReceiver(Long id, String operator){
        return mailReceiveBiz.deleteMailReceiver(id, operator);
    }

    public PageResult<List<MailReceiver>> listPage(Map<String, Object> paramMap, PageParam pageParam){
        return mailReceiveBiz.listPage(paramMap, pageParam);
    }
}
