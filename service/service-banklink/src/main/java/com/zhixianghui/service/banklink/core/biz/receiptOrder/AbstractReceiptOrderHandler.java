package com.zhixianghui.service.banklink.core.biz.receiptOrder;

import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.service.banklink.core.biz.message.email.EmailBiz;
import org.apache.commons.io.FilenameUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AbstractReceiptOrderHandler
 * @Description TODO
 * @Date 2023/6/29 11:26
 */
@Component
public abstract class AbstractReceiptOrderHandler {

    protected final static String TEMP_PATH = System.getProperty("user.dir") + File.separator + "wx" + File.separator;

    protected final static String FILE_SUFFIX = ".pdf";

    @Reference
    protected EmployerAccountInfoFacade employerAccountInfoFacade;

    @Autowired
    private EmailBiz emailBiz;

    /**
     * 申请电子回单
     * @param remitPlatTrxNo
     * @param employerNo
     * @param mainstayNo
     * @param channelType
     * @return
     */
    public abstract Map<String,Object> applyOrder(String remitPlatTrxNo, String employerNo, String mainstayNo, Integer channelType);

    /**
     * 下载电子回单并发送邮件
     * @param paramMap
     */
    public void downloadAndSendReceipt(Map<String, Object> paramMap){
        File file = download(paramMap);
        String email = (String) paramMap.get("email");
        EmailParamDto emailParamDto = buildEmailParam(file,email);
        emailBiz.sendAsync(emailParamDto);
    }

    private EmailParamDto buildEmailParam(File file, String email) {
        EmailParamDto param = new EmailParamDto();
        param.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        param.setTo(email);
        param.setSubject("【智享汇】电子凭证");
        param.setTpl(EmailTemplateEnum.RECEIPT_ORDERD.getName());
        Map<String, Object> map = new HashMap<>();
        param.setTplParam(map);
        param.setHtmlFormat(true);
        param.setHasAttachment(true);
        param.setFileName(file.getName());
        param.setFilePath(file.getAbsolutePath());
        return param;
    }

    protected abstract File download(Map<String, Object> paramMap);

    public EmployerAccountInfo getAccountInfo(String employerNo,String mainstayNo,Integer channelType){
        return employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo,mainstayNo,channelType);
    }
}
