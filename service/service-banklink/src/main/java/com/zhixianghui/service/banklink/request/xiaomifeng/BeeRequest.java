package com.zhixianghui.service.banklink.request.xiaomifeng;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.Sign;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.facade.banklink.vo.yishui.req.SohoDto;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class BeeRequest {

    @Value("${bee.request.privatekey}")
    private String privateKey;
    @Value("${bee.request.host}")
    private String host;

    @Value("${sftp.host}")
    private String sftphost;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private OrderFacade orderFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private UserInfoFacade userInfoFacade;
    @Autowired
    private FastdfsClient fastdfsClient;
    @Reference
    private SignRecordFacade signRecordFacade;

    private JSONObject post(String appid, String uriName, String uri, Map<String,Object> data, File file) {
        try {
            TimeUnit.SECONDS.sleep(1L);
        } catch (InterruptedException e) {

        }
        RequestParamDto paramDto = new RequestParamDto();
        paramDto.setAppid(appid);
        paramDto.setReqid(IdUtil.fastSimpleUUID());
        paramDto.setData(Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(data).getBytes()));
        sign(uri,paramDto);

        String params = JSONUtil.toJsonStr(paramDto);

        if (file != null) {
            JSONObject newparam = JSON.parseObject(params);
            newparam.put("file", file);
            params = newparam.toJSONString();
            log.info("【小蜜蜂订单同步】-{} -小蜜蜂请求路径:{}", uriName, host + uri);
            log.info("【小蜜蜂订单同步】-{} -小蜜蜂接口入参:{}",uriName, JSONUtil.toJsonPrettyStr(params));

            String response = HttpUtil.createPost(host + uri)
                    .form("appid",newparam.get("appid"))
                    .form("data",newparam.get("data"))
                    .form("sign",newparam.get("sign"))
                    .form("reqid",newparam.get("reqid"))
                    .form("file",newparam.get("file"))
                    .form("timestamp",newparam.get("timestamp"))
                    .execute().body();

            log.info("【小蜜蜂订单同步】-{} -易税接口{}返回：{}",uriName, uri, response);
            if (StrUtil.isNotBlank(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                String respData = jsonObject.getString("data");
                if (StrUtil.isNotBlank(respData)) {
                    String dataStr = new String(Base64.getDecoder().decode(respData), StandardCharsets.UTF_8);
                    log.info("【小蜜蜂订单同步】-{} -易税接口{}返回解密:{}",uriName,uri, dataStr);
                    return JSON.parseObject(dataStr);
                }
            }
        }else {
            log.info("【小蜜蜂订单同步】-{} -小蜜蜂接口入参:{}",uriName,JSONUtil.toJsonPrettyStr(paramDto));

            String response = HttpUtil.post(host + uri, params);

            log.info("【小蜜蜂订单同步】-{} -易税接口返回：{}", uriName,response);
            if (StrUtil.isNotBlank(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                String respData = jsonObject.getString("data");
                if (StrUtil.isNotBlank(respData)) {
                    String dataStr = new String(Base64.getDecoder().decode(respData), StandardCharsets.UTF_8);
                    log.info("【小蜜蜂订单同步】-{} -易税接口{}返回解密:{}",uriName,uri, dataStr);
                    return JSON.parseObject(dataStr);
                }
            }
        }

        return null;
    }

    @SneakyThrows
    private void sign(String uri, RequestParamDto paramDto) {
        Map<String, Object> orgMap = BeanUtil.beanToMap(paramDto);
        orgMap.remove("sign");
        String sortJoin = MapUtil.sortJoin(orgMap, "&", "=", true);
        String toSignStr = uri+"&"+sortJoin;

        // 解码私钥内容
        byte[] decodedKey = Base64.getDecoder().decode(this.privateKey);

        // 使用PKCS#8格式的密钥工厂生成私钥对象
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");

        // 使用SHA256withRSA算法进行签名
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(keyFactory.generatePrivate(keySpec));
        signature.update(toSignStr.getBytes(StandardCharsets.UTF_8));
        byte[] signatureBytes = signature.sign();

        // 对签名结果进行Base64编码
        String encodedSignature = Base64.getEncoder().encodeToString(signatureBytes);

        paramDto.setSign(encodedSignature);
    }


    public JSONObject getJobInfo(String appid){
        Map<String, Object> data = new HashMap<>();
        JSONObject post = this.post(appid,"3.3分包任务-分包任务列表", "/v2/project/list/",  data,null);
        if (post != null) {
            JSONArray jobArray = post.getJSONObject("data").getJSONArray("list");
            if (jobArray != null && jobArray.size() > 0) {
                for (Object object : jobArray) {
                    JSONObject job = (JSONObject) object;
                    String status = job.getString("status");
                    System.out.println(job.toJSONString());
                    if (StrUtil.equals(status, "ONGOING")) {
                        System.out.println(job.toJSONString());
                        return job;
                    }
                }
            } else {
                log.error("【小蜜蜂订单同步】-没有查询到小蜜蜂任务数据");
            }
        }
        return null;
    }

    public JSONObject getFundInfo(String appid){
        Map<String, Object> data = new HashMap<>();
        JSONObject post = this.post(appid,"3.5.商户账户及交易-获取账户列表-单个", "/v2/merchant/fund/list/", data,null);
        if (post != null) {
            JSONArray fundArray = post.getJSONObject("data").getJSONArray("list");
            if (fundArray != null && fundArray.size() > 0) {
                JSONObject fund = fundArray.getJSONObject(0);
//                log.info(fund.toJSONString());
                return fund;
            } else {
                log.error("没有查询到小蜜蜂资金账户数据");
            }
        }
        return null;
    }

    public JSONArray listFundInfo(String appid){
        Map<String, Object> data = new HashMap<>();
        JSONObject post = this.post(appid,"3.5.商户账户及交易-获取账户列表-列表","/v2/merchant/fund/list/", data,null);
        if (post != null) {
            JSONArray fundArray = post.getJSONObject("data").getJSONArray("list");
            return fundArray;
        }
        return null;
    }

    public JSONObject getYishuiFundInfo(String employerNo){
        DataDictionary dictionary = dataDictionaryFacade.getDataDictionaryByName("YISHUI_EMP_APPID_MAPPING");
        List<DataDictionary.Item> itemList = dictionary.getItemList();
        String appid = null;
        for (DataDictionary.Item item : itemList) {
            if (StrUtil.equals(item.getCode(),employerNo)) {
                appid = item.getDesc();
                break;
            }
        }
        if (appid == null) {
            log.error(StrUtil.format("【小蜜蜂订单同步】-商户 {} appid不存在",employerNo));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("商户 {} appid不存在",employerNo));
        }
        return this.getFundInfo(appid);
    }

    public void orderBatchSubmit(String outBatchNo){
        Order batchOrder = orderFacade.getByPlatBatchNo(outBatchNo);
        if (batchOrder == null) {
            log.error(StrUtil.format("【小蜜蜂订单同步】-批次 {} 订单不存在",outBatchNo));
            return;
        }

        DataDictionary dictionary = dataDictionaryFacade.getDataDictionaryByName("YISHUI_EMP_APPID_MAPPING");
        List<DataDictionary.Item> itemList = dictionary.getItemList();
        String appid = null;
        for (DataDictionary.Item item : itemList) {
            if (StrUtil.equals(item.getCode(),batchOrder.getEmployerNo())) {
                appid = item.getDesc();
                break;
            }
        }
        if (appid == null) {
            log.error(StrUtil.format("【小蜜蜂订单同步】-商户 {} appid不存在",batchOrder.getEmployerNo()));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("商户 {} appid不存在",batchOrder.getEmployerNo()));
        }

        JSONObject jobInfo = this.getJobInfo(appid);
        if (jobInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【小蜜蜂订单同步】-任务不存在");
        }
        JSONObject fundInfo = this.getFundInfo(appid);
        if (fundInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【小蜜蜂订单同步】-资金账户不存在");
        }

        JSONObject queryResult = this.batchQuery(appid,outBatchNo);
        if (queryResult!=null && StrUtil.equals(queryResult.getString("status"),"SUCC")) {
            log.info("【小蜜蜂订单同步】-小蜜蜂批次订单[{}]已完成",outBatchNo);
            return;
        }

        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("platBatchNo", outBatchNo);
        //只要发放成功的明细
        queryParam.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        List<OrderItem> orderItems = orderItemFacade.listBy(queryParam);
        if (orderItems==null || orderItems.size()==0){
            log.info("【小蜜蜂订单同步】-订单批次[{}]没有查询到订单明细数据",outBatchNo);
            return;
        }

        List<Map<String,Object>> orders = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            SohoDto soho = new SohoDto();

            Map<String, Object> map = new HashMap<>();
            map.put("employerNo", orderItem.getEmployerNo());
            map.put("mainstayNo", orderItem.getMainstayNo());
            map.put("receiveNameMd5", MD5Util.getMixMd5Str(orderItem.getReceiveNameDecrypt()));
            map.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str(orderItem.getReceiveIdCardNoDecrypt()));
            SignRecord signRecord = signRecordFacade.getOne(map);
            if (signRecord != null) {
                soho.setContract(signRecord.getFileUrl());
            }else {
                log.info("【小蜜蜂订单同步】-收款用户认证、签约信息不存在：{}",orderItem.getReceiveIdCardNoDecrypt());
            }

            UserInfo userInfo = userInfoFacade.getByIdCardNoMd5(orderItem.getReceiveIdCardNoMd5());
            if (userInfo != null){
                soho.setFrontImg(userInfo.getIdCardBackUrl());
                soho.setBackImg(userInfo.getIdCardFrontUrl());
            }


            if (StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.JOINPAY.name())){
                soho.setAccountNo(orderItem.getReceiveAccountNoDecrypt());
                soho.setBankName(orderItem.getBankName());
            }
            if (StringUtils.equals(orderItem.getPayChannelNo(), ChannelNoEnum.ALIPAY.name())) {
                soho.setAlipayAccount(orderItem.getReceiveAccountNoDecrypt());
            }
            soho.setName(orderItem.getReceiveNameDecrypt());
            soho.setMobile(orderItem.getReceivePhoneNoDecrypt());
            soho.setIdNo(orderItem.getReceiveIdCardNoDecrypt());

            this.sohoCreate(appid, soho);

            Map<String, Object> order = new HashMap<>();
            order.put("outOrderNo", orderItem.getPlatTrxNo());
            order.put("mobile", orderItem.getReceivePhoneNoDecrypt());
            order.put("idNo", orderItem.getReceiveIdCardNoDecrypt());
            order.put("accountName", orderItem.getReceiveNameDecrypt());
            order.put("accountType", StringUtils.equals(orderItem.getPayChannelNo(),ChannelNoEnum.JOINPAY.name())?"CARD":"ALIPAY");
            order.put("accountNo", orderItem.getReceiveAccountNoDecrypt());
            order.put("amount", orderItem.getOrderItemNetAmount());
            orders.add(order);
        }

        this.sohoAssign(appid, jobInfo.getString("id"), orders);

        Map<String, Object> data = new HashMap<>();
        data.put("projectId", jobInfo.getString("id"));
        data.put("payOffline", true);
        data.put("outBatchNo", outBatchNo);
        data.put("orders", orders);
        log.info("【小蜜蜂订单同步】-[{}]-提交批次订单参数：{}", outBatchNo, JSON.toJSONString(data));
        JSONObject post = this.post(appid,"3.6.批量代发-提交代发批次", "/v2/remit/batch/submit/", data,null);
        if (post != null) {
            String code = post.getString("code");
            if (StrUtil.equals(code, "0") || StrUtil.contains(post.getString("msg"),"外部批次号重复")) {
                JSONObject postData = this.batchQuery(appid,outBatchNo);
                if (postData!=null){
                    JSONArray ordersArray = postData.getJSONArray("orders");
                    if (ordersArray != null && ordersArray.size() > 0) {
                        for (Object object : ordersArray) {
                            JSONObject order = (JSONObject) object;
                            if (order==null || order.size()==0 || !StrUtil.equals(order.getString("status"), "INIT")) {
                                continue;
                            }

                            String id = order.getString("id");
                            String outOrderNo = order.getString("outOrderNo");
                            OrderItem item = orderItemFacade.getByPlatTrxNo(outOrderNo);
                            ChannelSftp channelSftp = null;
                            try{
                                //上传电子回单
                                channelSftp = SftpUtil.connect(sftphost, port, userName, pwd);
                                String fileName = getFileName(item);
                                String sftpFilePath = CertificateUtil.getFilePath(dir, item.getMainstayNo(), item.getPlatTrxNo());
                                String tempPath = System.getProperty("user.dir") + File.separator + "download" + File.separator + RandomUtil.get16LenStr();
                                FileUtils.creatDir(tempPath);
                                File file = FileUtils.createFile(tempPath + File.separator + fileName);
                                log.info("【小蜜蜂订单同步】-从sftp下载电子回单-{}",item.getPlatTrxNo());
                                SftpUtil.downloadNoClose(sftpFilePath + fileName, file, channelSftp);
                                log.info("【小蜜蜂订单同步】-调用小蜜蜂上传文件接口上传文件-{}",item.getPlatTrxNo());
                                String fileUrl = this.upload(appid,file);

                                Map<String, Object> uploadData = new HashMap<>();
                                uploadData.put("orderId", id);
                                uploadData.put("receipt", fileUrl);
                                log.info("【小蜜蜂订单同步】-调用小蜜蜂上传回单接口-{}",item.getPlatTrxNo());
                                JSONObject filePost = this.post(appid,"3.6.批量代发-上传代发订单回单","/v2/remit/order/receipt" +
                                                "/upload/",
                                        uploadData, file);

                                if (filePost ==null || (!StrUtil.equals(filePost.getString("code"), "0"))
                                || (!StrUtil.equals(filePost.getString("status"), "SUCC"))) {

                                }

                            } catch (Exception e) {
                                log.error("【小蜜蜂订单同步】-上传回单出错");
                                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【小蜜蜂订单同步】-上传回单出错");
                            }

                        }
                    }

                    Map<String, Object> payParam = new HashMap<>();
                    payParam.put("batchId", postData.getString("id"));
                    payParam.put("fundId", fundInfo.getString("id"));
                    payParam.put("outBatchNo", postData.getString("outBatchNo"));
                    payParam.put("orders", orders);
                    log.info("【小蜜蜂订单同步】-[{}]-小蜜蜂支付参数：{}", outBatchNo, JSON.toJSONString(payParam));
                    JSONObject payPost = this.post(appid,"3.6.批量代发-支付代发批次","/v2/remit/batch/pay/", payParam, null);
                    String payCode = payPost.getString("code");
                    if (!StrUtil.equals(payCode, "0")){
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg(payPost.getString("msg"));
                    }
                }
            }
        }
    }

    public JSONObject batchQuery(String appid,String outBatchNo) {
        Map<String, Object> data = new HashMap<>();
        data.put("outBatchNo", outBatchNo);
        JSONObject post = this.post(appid,"3.6.批量代发-查询代发批次详情","/v2/remit/batch/query/", data,null);
        if (post != null) {
            String code = post.getString("code");
            if (StrUtil.equals(code, "0")) {
                return post.getJSONObject("data");
            }
        }
        return null;
    }

    public JSONObject sohoQueryList(String appid) {
        Map<String, Object> data = new HashMap<>();
        JSONObject post = this.post(appid,"自由职业者列表","/v2/soho/list/", data,null);
        if (post != null) {
            String code = post.getString("code");
            if (StrUtil.equals(code, "0")) {
                return post.getJSONObject("data");
            }
        }
        return null;
    }

    public JSONObject sohoQueryByIdcard(String appid,String idcard,String name) {
        Map<String, Object> data = new HashMap<>();
        data.put("idNo", idcard);
        data.put("name", name);
        JSONObject post = this.post(appid,"自由职业者列表","/v2/soho/list/", data,null);
        if (post != null) {
            String code = post.getString("code");
            if (StrUtil.equals(code, "0")) {
                return post.getJSONObject("data");
            }
        }
        return null;
    }

    public JSONObject sohoQuery(String appid,String sohoId) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", sohoId);
        JSONObject post = this.post(appid,"自由职业者详情","/v2/soho/query/", data,null);
        if (post != null) {
            String code = post.getString("code");
            if (StrUtil.equals(code, "0")) {
                return post.getJSONObject("data");
            }
        }
        return null;
    }

    public JSONObject sohoCreate(String appid, SohoDto sohoDto) {
        JSONObject querydata = this.sohoQueryByIdcard(appid, sohoDto.getIdNo(), sohoDto.getName());
        if(querydata != null){
            JSONArray list = querydata.getJSONArray("list");
            //上传图片
            boolean hasIdCard=StrUtil.isNotBlank(sohoDto.getFrontImg()) && StrUtil.isNotBlank(sohoDto.getBackImg());
            if (hasIdCard) {
                String fontUrl = this.uploadFileFromFdfs(appid, sohoDto.getFrontImg());
                String backUrl = this.uploadFileFromFdfs(appid, sohoDto.getBackImg());
                sohoDto.setFrontImg(fontUrl);
                sohoDto.setBackImg(backUrl);
            }else{
                log.error("证件照不存在");
            }

            if (StrUtil.isNotBlank(sohoDto.getContract())) {
                String contract = this.uploadFileFromFdfs(appid, sohoDto.getContract());
                sohoDto.setContract(contract);
            }else{
                log.error("签约图片不存在");
            }

            Map<String, Object> data = BeanUtil.beanToMap(sohoDto, false, true);
            if (list == null || list.size() == 0) {

                JSONObject post = this.post(appid,"自由职业者创建","/v2/soho/create/", data,null);
                if (post != null) {
                    String code = post.getString("code");
                    if (StrUtil.equals(code, "0")) {
                        this.sohoCommit(appid, post.getJSONObject("data").getString("id"));
                        return post.getJSONObject("data");
                    }
                }
                return null;
            }else {
                data.put("id", list.getJSONObject(0).getString("id"));
                JSONObject post = this.post(appid,"自由职业者更新","/v2/soho/update/", data,null);
                if (post != null) {
                    String code = post.getString("code");
                    if (StrUtil.equals(code, "0")) {
                        this.sohoCommit(appid, list.getJSONObject(0).getString("id"));
                        return post.getJSONObject("data");
                    }
                }
                return null;
            }
        }
        return null;
    }

    public JSONObject sohoUpdate(String appid, SohoDto sohoDto) {
        Map<String, Object> data = BeanUtil.beanToMap(sohoDto, false, true);
        JSONObject post = this.post(appid,"自由职业者更新","/v2/soho/update/", data,null);
        if (post != null) {
            String code = post.getString("code");
            if (StrUtil.equals(code, "0")) {
                return post.getJSONObject("data");
            }
        }
        return null;
    }

    public JSONObject sohoCommit(String appid,String sohoId) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", sohoId);
        JSONObject post = this.post(appid,"提交自由职业者审核", "/v2/soho/commit/", data,null);
        if (post != null) {
            String code = post.getString("code");
            if (StrUtil.equals(code, "0")) {
                return post.getJSONObject("data");
            }
        }
        return null;
    }

    private String getFileName(OrderItem item) {

        if (StringUtils.isBlank(item.getProductNo()) || item.getProductNo().equals(ProductNoEnum.ZXH.getValue())
                || (item.getProductNo().equals(ProductNoEnum.CKH.getValue())&&StringUtils.equals(item.getPayChannelNo(), ChannelNoEnum.JOINPAY.name()))){
            return CertificateUtil.getPayFileName(item.getReceiveNameDecrypt(), item.getMainstayNo(), item.getPlatTrxNo());
        }else{
            return CertificateUtil.getCKHPayFileName(item.getReceiveNameDecrypt(), item.getEmployerNo(), item.getPlatTrxNo());
        }

    }
    public String upload(String appid,File file){

        String md5 = SecureUtil.md5(file);

        Map<String, Object> data = new HashMap<>();
        data.put("scene", "REMIT_RECEIPT");
        data.put("md5", md5);

        JSONObject post = this.post(appid,"文件上传","/v2/file/upload/", data, file);
        if (post != null) {
            String code = post.getString("code");
            if (StrUtil.equals(code, "0")) {
                JSONObject postData = post.getJSONObject("data");
                if (postData!=null){
                    return postData.getString("file");
                }
            }
        }
        return null;
    }

    public String uploadFileFromFdfs(String appid,String fileId){
        if (StrUtil.isBlank(fileId)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("文件id不能为空");
        }
        String fileType = fileId.split("\\.")[fileId.split("\\.").length - 1];
        InputStream inputStream = fastdfsClient.downloadFile(fileId);
        try{
            String fileName = System.getProperty("user.dir") + File.separator + "download"+File.separator+"fdfs" + File.separator + RandomUtil.get16LenStr()+"."+fileType;

            byte[] bytes = IoUtil.readBytes(inputStream);
            File tempFile = FileUtils.byte2file(bytes, fileName);
           return this.upload(appid,tempFile);
        }catch (Exception e){
            log.error("【小蜜蜂订单同步】-文件上传失败",e);
            return null;
        }finally {
            if (inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("【小蜜蜂订单同步】-文件流关闭失败",e);
                }
            }
        }
    }

    public void sohoAssign(String appid, String projectId,List<Map<String,Object>> orders) {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, String>> items = new ArrayList<>();
        for (Map<String, Object> order : orders) {
            Map<String, String> item = new HashMap<>();
            item.put("mobile", item.get("mobile"));
            item.put("name", item.get("name"));
            item.put("idNo", item.get("accountName"));
            items.add(item);
        }

        data.put("projectId", projectId);
        data.put("orders", ListUtil.of(items));

        JSONObject post = this.post(appid,"3.4.任务关联及签约-批量任务关联","/v2/sign/batch/assign/", data,null);
    }

    public static void main(String[] args) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {
        System.out.println(MD5Util.getMixMd5Str("440105199804100323"));
    }

}
