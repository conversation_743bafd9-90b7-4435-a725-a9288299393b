package com.zhixianghui.service.banklink.request.pay;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BanklinkExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.banklink.entity.CmbOrders;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.vo.pay.*;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.BankCardBinFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.enums.IdentityTypeEnum;
import com.zhixianghui.facade.trade.enums.RechargeStatusEnum;
import com.zhixianghui.facade.trade.enums.RechargeTypeEnum;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.service.banklink.constant.AlipayConstant;
import com.zhixianghui.service.banklink.constant.CmbConstant;
import com.zhixianghui.service.banklink.core.biz.KeyPairRecordBiz;
import com.zhixianghui.service.banklink.core.biz.RobotBiz.RobotBiz;
import com.zhixianghui.service.banklink.core.biz.account.CmbAccountBiz;
import com.zhixianghui.service.banklink.core.services.CmbOrdersService;
import com.zhixianghui.service.banklink.request.cmb.CmbApiService;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import com.zhixianghui.starter.comp.component.RedisRateLimit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CmbPayBiz {

    @Autowired
    private CmbApiService cmbApiService;
    @Reference
    private NotifyFacade notifyFacade;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private CmbOrdersService cmbOrdersService;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private RobotBiz robotBiz;
    @Autowired
    private RedisRateLimit redisRateLimit;
    @Reference
    private OrderFacade orderFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private EmployerAccountInfoFacade accountInfoFacade;
    @Autowired
    private KeyPairRecordBiz keyPairRecordBiz;
    @Reference
    private RecordItemFacade recordItemFacade;
    @Autowired
    private CmbAccountBiz cmbAccountBiz;
    @Reference
    private BankCardBinFacade cardBinFacade;


    public void refundFee(String platTrxNo,String chanelAcctNo,String mchAcctBookNo,String feeAmt) {
        log.info("[退款环节: {}]==>获取退款锁",platTrxNo);
        String lockKey = String.join(":", CmbConstant.CMB_REFUND_FEE_AUTH_KEY,platTrxNo);
        String clientId = redisLock.tryLockLong(lockKey,0,1);
        if(clientId == null){
            log.info("[退款环节: {}]==>获取退款锁失败，直接丢弃",platTrxNo);
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try {
            JSONObject transResult = cmbApiService.transferAccountBooks(chanelAcctNo,"********",mchAcctBookNo, feeAmt, "退汇退款",platTrxNo+"R");
            if (transResult != null && transResult.get("ntdmatrxz1") != null&&transResult.getJSONArray("ntdmatrxz1").getJSONObject(0)!=null) {
                JSONObject respData = transResult.getJSONArray("ntdmatrxz1").getJSONObject(0);
                log.info("[{}]-退汇退回计费成功，详情:\n{}",platTrxNo, respData.toJSONString());
            }else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("[" + platTrxNo + "]-退汇退回服务费失败");
            }
        } finally {
            //释放锁
            redisLock.unlockLong(clientId);
        }

    }

    public PayRespVo pay(PayReqVo payReqVo) {
        Long token = redisRateLimit.getLimitToken("CMB:REQUEST", "5", "5");
        if (token == null || token == 0L) {
            throw new RuntimeException("流量超限，丢回消息重试队列重发");
        }
        PayRespVo respVo = new PayRespVo();
        // 发放到个人
        CmbPayerVo payerVo = new CmbPayerVo();
        payerVo.setAmount(payReqVo.getReceiveAmount());
        payerVo.setRemark(payReqVo.getRemitRemark());
        payerVo.setRemitNo(payReqVo.getBankOrderNo());
        payerVo.setAccountName(payReqVo.getRealPayerName());
        payerVo.setAccountNo(payReqVo.getChannelMchNo());
        payerVo.setAccountBookNo(payReqVo.getSubMerchantNo());
        payerVo.setPayBatchNo(payReqVo.getPayBatchNo());

        CmbPayeeVo payeeVo = new CmbPayeeVo();
        payeeVo.setRemitSeqNo(payReqVo.getBankOrderNo().substring(payReqVo.getBankOrderNo().length()-8));
        payeeVo.setAmount(payReqVo.getReceiveAmount());
        payeeVo.setRemark(payReqVo.getRemitRemark());
        payeeVo.setAccountNo(payReqVo.getReceiveAccountNo());
        payeeVo.setAccountName(payReqVo.getReceiveName());
        payeeVo.setRemitDetailNo(payReqVo.getBankOrderNo()+"P");
        payeeVo.setBankName(payReqVo.getPayeeBankName());

        try {
            JSONObject payResult = cmbApiService.payApply(payerVo, payeeVo);
            if (payResult != null && payResult.get("bb6cdcbhz1") != null && payResult.getJSONArray("bb6cdcbhz1").getJSONObject(0) != null) {
                JSONObject payRstData = payResult.getJSONArray("bb6cdcbhz1").getJSONObject(0);
                String bankTrxNo = payRstData.getString("reqnbr");

                respVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
                respVo.setBankTrxNo(bankTrxNo);
                return respVo;
            }else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发放失败");
            }
        }catch (BizException e){
            log.error("招行通道支付业务异常：", e);
            //调用招行接口异常，需要退款
            //payReqVo.setFeeTrxNo(feeTrxNo);
            //notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_REFUND,payReqVo.getSubMerchantNo(),payReqVo.getBankOrderNo(),NotifyTypeEnum.CMB_REFUND_MSG.getValue(), MessageMsgDest.TAG_CMB_REFUND, JSON.toJSONString(payReqVo), MsgDelayLevelEnum.S_10.getValue());
            if (StringUtils.equals(e.getApiErrorCode(), ApiExceptions.API_BIZ_FAIL.getApiErrorCode())) {
                String code = e.getErrMsg().split("::")[0];
                String msg = e.getErrMsg().split("::")[1];
                respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                respVo.setBizCode(code);
                respVo.setBizMsg(msg);
                respVo.setBankTrxNo(payReqVo.getBankTrxNo());
                return respVo;
            }else {
                throw new RuntimeException(e.getMessage());
            }
        }catch (Exception e) {
            log.error("招行通道支付异常：", e);
            //调用招行接口未知异常，需要退款
            //payReqVo.setFeeTrxNo(feeTrxNo);
            //notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_REFUND,payReqVo.getSubMerchantNo(),payReqVo.getBankOrderNo(),NotifyTypeEnum.CMB_REFUND_MSG.getValue(), MessageMsgDest.TAG_CMB_REFUND, JSON.toJSONString(payReqVo), MsgDelayLevelEnum.S_10.getValue());
            throw new RuntimeException(e.getMessage());
        }
    }

    public void refund(PayReqVo reqVo) {
        String payBatchNo = reqVo.getPayBatchNo();
        String remitNo = reqVo.getBankOrderNo();
        String accountNo = reqVo.getChannelMchNo();
        String accountBookNo = reqVo.getSubMerchantNo();
        String feeAmount = reqVo.getServiceFee();
        log.info("[退款环节: {}]==>获取退款锁",remitNo);
        String lockKey = String.join(":", CmbConstant.CMB_REFUND_AUTH_KEY,remitNo);
        String clientId = redisLock.tryLockLong(lockKey,0,1);
        if(clientId == null){
            log.info("[退款环节: {}]==>获取退款锁失败，直接丢弃",remitNo);
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try {
//            CmbOrders cmbOrders = cmbOrdersService.getOne(new QueryWrapper<CmbOrders>().eq(CmbOrders.COL_TRXNBR, reqVo.getFeeTrxNo()));
            JSONObject queryResult = null;
            try {
                queryResult = cmbApiService.payBatchQuery(accountNo, payBatchNo);
            } catch (BizException e) {
                if (e.getErrMsg().contains("没有符合查询条件的数据")) {
                    queryResult = null;
                } else {
                    throw e;
                }
            }
            if (queryResult!=null
                    && queryResult.getJSONArray("bb6bpdqyz2")!=null
                    &&!queryResult.getJSONArray("bb6bpdqyz2").isEmpty()
                    &&queryResult.getJSONArray("bb6bpdqyz2").getJSONObject(0)!=null) {
                JSONObject detailItem = queryResult.getJSONArray("bb6bpdqyz2").getJSONObject(0);
                if (StringUtils.equals("E", detailItem.getString("stscod"))) {
                    cmbApiService.transferAccountBooks(accountNo, "********", accountBookNo, feeAmount, "交易失败退款",remitNo+"R");
                } else if (StringUtils.equals("S", detailItem.getString("stscod"))) {
                    log.info("[{}]退款查询发现订单已成功，调用反查接口扭转订单状态", remitNo);
                    recordItemFacade.reverseQuery(remitNo);
                } else {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("[{}]招行端订单处理中，丢回队列重试", remitNo));
                }
            }else {
                cmbApiService.transferAccountBooks(accountNo, "********", accountBookNo, feeAmount, "交易失败退款", remitNo + "R");
            }
        }catch (Exception e){
            log.warn("退款失败",e);
            //限制退款重试次数3次
            if (getAcceptTimes(remitNo) > AlipayConstant.MAX_REFUND_TIMES){
                sendToRobot(reqVo);
                log.info("招行退款重试次数大于五次，默认退款失败，bankOrderNo:{}",reqVo.getBankOrderNo());
            }else{
                log.error("招行执行退款重试处理，订单号：" + reqVo.getBankOrderNo());
                notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_REFUND,reqVo.getSubMerchantNo(),reqVo.getBankOrderNo(),NotifyTypeEnum.CMB_REFUND_MSG.getValue(), MessageMsgDest.TAG_CMB_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.M_5.getValue());
            }
        }finally {
            //释放锁
            redisLock.unlockLong(clientId);
        }
    }

    private Long getAcceptTimes(String bankOrderNo) {
        Long acceptTime = 0L;
        try {
            acceptTime = redisClient.incr(AlipayConstant.REFUND_TIMES_PRE_KEY+ bankOrderNo);
            redisClient.expire(AlipayConstant.REFUND_TIMES_PRE_KEY + bankOrderNo,60*60);
        }catch (Exception e){
            log.error("[退款环节: {}] ==> redis获取重试次数异常 忽略", bankOrderNo, e);
        }
        return acceptTime;
    }

    private void sendToRobot(PayReqVo reqVo) {
        MarkDownMsg markDownMsg = new MarkDownMsg();
        markDownMsg.setRobotType(RobotTypeEnum.GRANTING_ROBOT.getType());
        markDownMsg.setUnikey("REFUND:" + reqVo.getBankOrderNo());
        StringBuffer sb = new StringBuffer("#### 招行退款失败\\n")
                .append("> 平台流水号：").append(reqVo.getPlatTrxNo())
                .append("\\n > 商户名称：").append(reqVo.getMchName())
                .append("\\n > 实发金额：").append(reqVo.getReceiveAmount())
                .append("\\n > 服务费：").append(reqVo.getServiceFee())
                .append("\\n > 发放时间：").append(com.zhixianghui.common.util.utils.DateUtil.formatDateTime(reqVo.getCreateTime()));

        markDownMsg.setContent(sb.toString());
        robotBiz.pushMarkDownAsync(markDownMsg);
    }

    private PayRespVo retry(PayRespVo payRespVo, PayReqVo reqVo) {
        log.info("订单号：[{}]，触发不换单打款重试",reqVo.getBankOrderNo());
        reqVo.setRetry(true);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_RETRY,
                reqVo.getSubMerchantNo(),
                reqVo.getBankOrderNo(),
                NotifyTypeEnum.CMB_BUSY_RETRY.getValue(),
                MessageMsgDest.TAG_CMB_RETRY,
                JSON.toJSONString(reqVo),
                MsgDelayLevelEnum.M_10.getValue());

        payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
        payRespVo.setBizCode("");
        payRespVo.setBankOrderNo(reqVo.getBankOrderNo());
        payRespVo.setBizMsg("");
        return payRespVo;
    }

    public void syncOrder2Local(String accountNo) {
        Date today = new Date();
//        String startDate = DateUtil.format(DateUtil.offsetDay(today, -1).toJdkDate(), "yyyyMMdd");
//        String endDate = DateUtil.format(today, "yyyyMMdd");
//        log.info("同步招行订单，时间段[{}-{}]", startDate, endDate);

        try {
            if (DateUtil.toLocalDateTime(today).getHour()<2) {
                log.info("在每日2点之前,查询上一日交易数据");
                String hisCtnKey = null;
                do{
                    JSONObject recordData2 = cmbApiService.queryHisAccountBookRecord(accountNo, null, hisCtnKey,-2);
                    if (recordData2!=null) {
                        JSONArray ntdmthlsz = recordData2.getJSONArray("ntdmthlsz");
                        JSONArray ntdmthlsy = recordData2.getJSONArray("ntdmthlsy");
                        if (ntdmthlsz != null&&!ntdmthlsz.isEmpty()) {
                            if (ntdmthlsy!=null&&!ntdmthlsy.isEmpty()) {
                                hisCtnKey = ntdmthlsy.getJSONObject(0).getString("ctnkey");
                            }
                            for (int i = 0; i < ntdmthlsz.size(); i++) {
                                JSONObject record = ntdmthlsz.getJSONObject(i);
                                String accountBookNo = record.getString("dmanbr");
                                String trxnbr = record.getString("trxnbr");
                                String trxdir = record.getString("trxdir");//交易关联记账子单元的方式：1：自动 2：手工 3：内部转账

                                String platTrxNo = redisClient.get("cmbfee:map:" + trxnbr);
                                if (StringUtils.isNotBlank(platTrxNo)) {
                                    String key = "accountBookRecord:" + accountNo + ":" + accountBookNo + ":" + trxdir + ":" + platTrxNo;
                                    redisClient.set(key, record.toJSONString(), 3600 * 24 * 7);
                                }else {
                                    String key = "accountBookRecord:" + accountNo + ":" + accountBookNo + ":" + trxdir + ":" + trxnbr;
                                    redisClient.set(key, record.toJSONString(), 3600 * 24 * 7);
                                }

                                CmbOrders cmbOrders = JSON.toJavaObject(record, CmbOrders.class);
                                CmbOrders orders = cmbOrdersService.getOne(new QueryWrapper<CmbOrders>().eq(CmbOrders.COL_TRXNBR, trxnbr));
                                if (orders == null){
                                    cmbOrders.setCreateTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                                    cmbOrders.setUpdateTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                                    cmbOrdersService.save(cmbOrders);

                                    this.syncRecharge(cmbOrders);

                                }else {
                                    BeanUtils.copyProperties(cmbOrders,orders);
                                    cmbOrders.setUpdateTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                                    cmbOrdersService.updateById(orders);
                                }
                            }

                        }
                    }
                    try {
                        TimeUnit.SECONDS.sleep(8);
                    } catch (InterruptedException e) {
                        log.error("线程睡眠失败");
                    }
                }while (hisCtnKey != null);
            }

            String ctnkey = null;
            do {
                JSONObject recordData = cmbApiService.queryTodayAccountBookRecord(accountNo, null, ctnkey);


                if (recordData!=null) {
                    JSONArray ntdmthlsz = recordData.getJSONArray("ntdmtlstz");
                    JSONArray ntdmthlsy = recordData.getJSONArray("ntdmtlsty");
                    if (ntdmthlsz != null&&!ntdmthlsz.isEmpty()) {
                        if (ntdmthlsy!=null&&!ntdmthlsy.isEmpty()) {
                            ctnkey = ntdmthlsy.getJSONObject(0).getString("ctnkey");
                        }
                        for (int i = 0; i < ntdmthlsz.size(); i++) {
                            JSONObject record = ntdmthlsz.getJSONObject(i);
                            String accountBookNo = record.getString("dmanbr");
                            String trxnbr = record.getString("trxnbr");
                            String trxdir = record.getString("trxdir");//交易关联记账子单元的方式：1：自动 2：手工 3：内部转账

                            String platTrxNo = redisClient.get("cmbfee:map:" + trxnbr);
                            if (StringUtils.isNotBlank(platTrxNo)) {
                                String key = "accountBookRecord:" + accountNo + ":" + accountBookNo + ":" + trxdir + ":" + platTrxNo;
                                redisClient.set(key, record.toJSONString(), 3600 * 24 * 7);
                            }else {
                                String key = "accountBookRecord:" + accountNo + ":" + accountBookNo + ":" + trxdir + ":" + trxnbr;
                                redisClient.set(key, record.toJSONString(), 3600 * 24 * 7);
                            }

                            CmbOrders cmbOrders = JSON.toJavaObject(record, CmbOrders.class);
                            CmbOrders orders = cmbOrdersService.getOne(new QueryWrapper<CmbOrders>().eq(CmbOrders.COL_TRXNBR, trxnbr));
                            if (orders == null){
                                cmbOrders.setCreateTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                                cmbOrders.setUpdateTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                                try {
                                    cmbOrdersService.save(cmbOrders);
                                } catch (Exception e) {
                                    if (!StringUtils.contains(e.getMessage(), "Duplicate entry")) {
                                        throw new RuntimeException(e);
                                    }
                                }

                                //筛选出充值订单并保存
                                //改为以回调为准
                                //this.syncRecharge(cmbOrders);

                            }else {
                                BeanUtils.copyProperties(cmbOrders,orders);
                                cmbOrders.setUpdateTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
                                cmbOrdersService.updateById(orders);
                            }
                        }
                    }
                }
                try {
                    TimeUnit.SECONDS.sleep(8);
                } catch (InterruptedException e) {
                    log.error("线程睡眠失败");
                }
            } while (ctnkey != null);
        } catch (BizException e) {
            if (!StringUtils.contains(e.getErrMsg(), "系统繁忙")) {
                throw e;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    private void syncRecharge(CmbOrders cmbOrders) {
        String trxdir = cmbOrders.getTrxdir();
        String trxnbr = cmbOrders.getTrxnbr();
        final int autflg = cmbOrders.getAutflg();
        //筛选出充值订单并保存
        if (StringUtils.equals(trxdir, "C")
                && autflg == 1
                && ValidateUtil.isBankCard(cmbOrders.getRpyacc())) {

            final EmployerAccountInfo employerAccountInfo = accountInfoFacade.getByEmployerNoAndParentMchNoAndChannelNo("M" + cmbOrders.getDmanbr(), cmbOrders.getAccnbr(), ChannelNoEnum.CMB.name());
            if (employerAccountInfo == null) {
                log.warn("账户表找不到记账子单元为 {} 的账户", cmbOrders.getDmanbr());
            } else {
                String rechargeOrderId = LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getKey(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getWidth());
                RechargeRecord rechargeRecord = new RechargeRecord();
                rechargeRecord.setRechargeType(RechargeTypeEnum.BANK_NET_RECHARGE.getCode());
                rechargeRecord.setRechargeStatus(RechargeStatusEnum.SUCCESS.getCode().shortValue());
                rechargeRecord.setChannelOrderId(trxnbr);
                rechargeRecord.setRechargeOrderId(rechargeOrderId);
                rechargeRecord.setChannelCode(ChannelNoEnum.CMB.name());
                rechargeRecord.setChannelName(ChannelNoEnum.CMB.getDesc());
                rechargeRecord.setRechargeAmount(cmbOrders.getTrxamt());
                rechargeRecord.setChannelType(Integer.valueOf(ChannelTypeEnum.BANK.getValue()).shortValue());
                rechargeRecord.setAccountBookId(cmbOrders.getDmanbr());
                rechargeRecord.setChannelTrxNo(trxnbr);
                rechargeRecord.setCreateBy(cmbOrders.getRpynam());
                rechargeRecord.setCreateTime(new Date());
                rechargeRecord.setUpdateTime(new Date());

                rechargeRecord.setEmployerNo(employerAccountInfo.getEmployerNo());
                rechargeRecord.setEmployerName(employerAccountInfo.getEmployerName());
                rechargeRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
                rechargeRecord.setMainstayName(employerAccountInfo.getMainstayName());
                rechargeRecord.setPayeeName(cmbOrders.getDmanam());
                rechargeRecord.setPayeeIdentityType(IdentityTypeEnum.CMB_ACCOUNTBOOK_ID.getValue());
                rechargeRecord.setPayeeIdentity(cmbOrders.getDmanbr());

                final BankCardBin cardBin = cardBinFacade.getCardBinByCardNo(cmbOrders.getRpyacc());

                rechargeRecord.setPayerName(cmbOrders.getRpynam());
                rechargeRecord.setPayerIdentity(cmbOrders.getRpyacc());
                rechargeRecord.setPayerBankName(cardBin == null ? "-" : cardBin.getBankName());
                rechargeRecord.setPayerIdentityType(IdentityTypeEnum.BANK_ACCOUNT_NO.getValue());

                rechargeRecord.setTransPayTime(new Date());
                rechargeRecord.setRemark(cmbOrders.getTrxtxt());

                // 查询当前余额
                final String balance = cmbAccountBiz.getBalance(employerAccountInfo.getParentMerchantNo(), employerAccountInfo.getSubMerchantNo());
                if (balance != null) {
                    final BigDecimal newBalance = new BigDecimal(balance).add(rechargeRecord.getRechargeAmount());
                    rechargeRecord.setCurrentBalance(newBalance);
                }
                orderFacade.addRechargeRecord(rechargeRecord);

//                cmbAccountBiz.recharge(employerAccountInfo.getParentMerchantNo(), employerAccountInfo.getSubMerchantNo(),cmbOrders.getTrxamt().toPlainString());
            }
        }
    }

    public PayRespVo queryOrder(QueryPayOrderReqVo payReqVo) {

        PayRespVo respVo = new PayRespVo();
        JSONObject queryResult = null ;
        try {
            queryResult = cmbApiService.payBatchQuery(payReqVo.getChannelMchNo(), payReqVo.getPayBatchNo());
        } catch (BizException e) {
            if (e.getErrMsg().contains("没有符合查询条件的数据")) {
                queryResult = null;
            } else {
                throw e;
            }
        }

        if (queryResult!=null
                && queryResult.getJSONArray("bb6bpdqyz2")!=null
                &&!queryResult.getJSONArray("bb6bpdqyz2").isEmpty()
                &&queryResult.getJSONArray("bb6bpdqyz2").getJSONObject(0)!=null) {
            JSONObject bb6bpdqyz1 = queryResult.getJSONArray("bb6bpdqyz1").getJSONObject(0);
            JSONArray bb6bpdqyz2 = queryResult.getJSONArray("bb6bpdqyz2");
            for (Object item : bb6bpdqyz2) {
                JSONObject detailItem = JSON.parseObject(JSONObject.toJSONString(item));
                if (payReqVo.getBankOrderNo().contains(detailItem.getString("trxseq"))){
                    if (StringUtils.equals("S", detailItem.getString("stscod"))) {
                        respVo.setBankOrderNo(payReqVo.getBankOrderNo());
                        respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
                        respVo.setBankTrxNo(detailItem.getString("trssqn"));
                    } else if (StringUtils.equals("A",detailItem.getString("stscod"))){
                        if (StringUtils.equals(bb6bpdqyz1.getString("stscod"),"C")){
                            respVo.setBankOrderNo(payReqVo.getBankOrderNo());
                            respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                            respVo.setBankTrxNo(detailItem.getString("trssqn"));
                            respVo.setBizMsg("供应商审核拒绝");
                            respVo.setBizCode("");
                            return respVo;
                        }else {
                            respVo.setBankOrderNo(payReqVo.getBankOrderNo());
                            respVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
                            return respVo;
                        }

                    }else {
                        respVo.setBankOrderNo(payReqVo.getBankOrderNo());
                        respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                        respVo.setBankTrxNo(detailItem.getString("trssqn"));
                        respVo.setBizMsg(detailItem.getString("errtxt"));
                        respVo.setBizCode(detailItem.getString("errcod"));
                    }
                    return respVo;
                }
            }
            respVo.setBankOrderNo(payReqVo.getBankOrderNo());
            respVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
            return respVo;
        }else {
            respVo.setBankOrderNo(payReqVo.getBankOrderNo());
            respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            respVo.setBizMsg("订单不存在");
            respVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()));
            return respVo;
        }
    }

}
