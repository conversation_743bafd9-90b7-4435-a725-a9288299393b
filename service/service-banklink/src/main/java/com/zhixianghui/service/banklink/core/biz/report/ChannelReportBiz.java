package com.zhixianghui.service.banklink.core.biz.report;

import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.vo.report.*;
import com.zhixianghui.service.banklink.request.pay.JxhPayBiz;
import com.zhixianghui.service.banklink.request.report.JoinpayReportBiz;
import com.zhixianghui.service.banklink.request.report.JxhReportBiz;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-10-30 10:42
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelReportBiz {
    private final JoinpayReportBiz joinpayReportBiz;
    private final JxhReportBiz jxhReportBiz;

    public ReportResVo report(ReportReqVo reportReqVo) {
        if (reportReqVo.getChannelType() == ChannelTypeEnum.BANK.getValue() && reportReqVo.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())){
            return joinpayReportBiz.report(reportReqVo);
        } else if (reportReqVo.getChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name())){
            return jxhReportBiz.report(reportReqVo);
        }else{
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应通道报备方法,请检查编号是否一致,通道是否存在。PayChannelNo:" + reportReqVo.getChannelNo());
        }
    }

    public ReportReceiveRespVo verifyAndHandleResult(ReportReceiveReqVo reqVo) {
        if (reqVo.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())){
            return joinpayReportBiz.verifyAndHandleResult(reqVo);
        }else{
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应通道报备回调方法,请检查编号是否一致,通道是否存在。PayChannelNo:" + reqVo.getChannelNo());
        }
    }

    public ReportResVo modify(ReportReqVo reqVo) {
        if (ChannelNoEnum.JOINPAY.name().equals(reqVo.getChannelNo())) {
            return joinpayReportBiz.modify(reqVo);
        } else if (ChannelNoEnum.JOINPAY_JXH.name().equals(reqVo.getChannelNo())) {
            return jxhReportBiz.modify(reqVo);
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应通道报备方法,请检查编号是否一致,通道是否存在。PayChannelNo:" + reqVo.getChannelNo());
        }
    }


    public Map<String,Map<String,String>> cheackMchSignAndPicUpload(AltMchSignVo vo){

        Map<String, String> signResult = joinpayReportBiz.queryAltMchSignRecord(vo);
        Map<String, String> uploadPicResult = joinpayReportBiz.queryPicsInfo(vo);

        Map<String, Map<String, String>> result = new HashMap<>();
        result.put("altMchSignInfo", signResult);
        result.put("altMchPicUploadInfo", uploadPicResult);
        return result;
    }
}
