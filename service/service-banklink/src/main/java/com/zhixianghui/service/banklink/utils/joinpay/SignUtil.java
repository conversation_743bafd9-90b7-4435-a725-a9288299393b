package com.zhixianghui.service.banklink.utils.joinpay;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.util.utils.HEXUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/10/27
 **/
@Slf4j
public class SignUtil {

    public static String sortAndSplicing(TreeMap<String, String> params) {
        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);
        StringBuilder str1 = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = params.get(key);
            if (i == keys.size() - 1) {
                str1.append(value.toString());
            } else {
                str1.append(value);
            }
        }
        return str1.toString();
    }

    /**
     * MD5签名
     *
     * @param requestSign 请求签名串
     * @param privateKey  密钥
     */
    public static String SignByMD5(String requestSign, String privateKey) {
        return DigestUtils.md5Hex(requestSign + privateKey).toUpperCase();
    }

    public static String genHmac(TreeMap<String, String> map, String mchPrivateKeyStr) {
        String unSignStr = getUnSignStr_1_0(map);
        return DigestUtils.md5Hex(unSignStr + mchPrivateKeyStr);
    }

    /**
     * 算签
     * 汇聚旧收单等老版本接口使用
     *
     * @param map
     * @param mchPrivateKeyStr
     * @return
     */
    public static String genSign_1_0(TreeMap<String, String> map, String mchPrivateKeyStr) {
        String unSignStr = getUnSignStr_1_0(map);
        try {
            String sign = HEXUtil.encode(MD5Util.getMD5(unSignStr + mchPrivateKeyStr), true);
            return URLEncoder.encode(sign, StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            log.error("算签失败, 待签名字符串：{}，异常信息：", unSignStr, e);
        }
        return null;
    }

    /**
     * 算签
     * 汇聚报备、分账等新版本接口使用
     *
     * @param map
     * @param mchPrivateKeyStr
     * @return
     */
    public static String genSign_2_0(TreeMap<String, Object> map, String mchPrivateKeyStr) {
        String unSignStr = getUnSignStr_2_0(map);
        try {
            String sign = HEXUtil.encode(MD5Util.getMD5(unSignStr + "&key=" + mchPrivateKeyStr), true);
            return sign;
        } catch (Exception e) {
            log.error("算签异常, 待签名字符串：{}，异常信息：", unSignStr, e);
        }
        return null;
    }

    /**
     * 验签
     * 汇聚报备、分账等新版本接口使用
     *
     * @return
     */
    public static boolean verify_2_0(TreeMap<String, Object> map, String mchPrivateKeyStr, String sign) {
        String signStr = getUnSignStr_2_0(map);
        try {
            String verifySign = HEXUtil.encode(MD5Util.getMD5(signStr + "&key=" + mchPrivateKeyStr), true);
            return verifySign.equals(sign.toUpperCase());
        } catch (Exception e) {
            log.error("验签异常, 报文信息：{}，待签名字符串：{}，异常信息：", JsonUtil.toString(map), signStr, e);
            throw e;
        }
    }

    private static String getUnSignStr_2_0(TreeMap<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry entry : map.entrySet()) {
            if ("aes_key".equals(entry.getKey())
                    || "sign".equals(entry.getKey())) {
                continue;
            }
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        log.info("签名原始串:" + sb.toString());
        return sb.toString();
    }

    private static String getUnSignStr_1_0(TreeMap<String, String> map) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry entry : map.entrySet()) {
            if ("hamc".equals(entry.getKey())) {
                continue;
            }
            sb.append(entry.getValue());
        }
        return sb.toString();
    }


    /**
     * 生成 RSA 签名串 *
     *
     * @param data       需要生成签名串的数据 *
     * @param privateKey 私钥 *
     * @return 签名字符串
     * @throws Exception
     */
    public static String sign(String data, String privateKey) throws Exception {
        try {
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);

            byte[] keyBytes = Base64.getDecoder().decode(privateKey);

            String algorithm = "MD5withRSA";
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            PrivateKey priKey = KeyFactory.getInstance("RSA").generatePrivate(pkcs8KeySpec);
            Signature signature = Signature.getInstance(algorithm);

            signature.initSign(priKey);
            signature.update(dataBytes);
            return Base64.getEncoder().encodeToString(signature.sign());
        } catch (Throwable e) {
            throw new Exception("生成 RSA 签名失败", e);
        }

    }

    /**
     * 验证 RSA 签名串 *
     *
     * @param data      需要验签的数据 *
     * @param publicKey 公钥 *
     * @param sign      用户传过来的签名串 *
     * @return *
     * @throws Exception
     */
    public static boolean verify(String data, String publicKey, String sign) throws Exception {
        try {
            byte[] dataBytes = data.getBytes(StandardCharsets.UTF_8);
            byte[] signBytes = Base64.getDecoder().decode(sign);
            String algorithm = "MD5withRSA";
            byte[] keyBytes = Base64.getDecoder().decode(publicKey);
            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicK = keyFactory.generatePublic(x509KeySpec);
            Signature signature = Signature.getInstance(algorithm);
            signature.initVerify(publicK);
            signature.update(dataBytes);
            return signature.verify(signBytes);
        } catch (Throwable e) {
            throw new Exception("RSA 验签失败", e);
        }
    }


    /**
     * 算签
     * 汇聚报备、分账等新版本接口使用
     *
     * @param map
     * @param mchPrivateKeyStr
     * @return
     */
    public static String rsaGenSign_2_0(TreeMap<String, Object> map, String mchPrivateKeyStr) {
        String unSignStr = getUnSignStr_2_0(map);
        try {
            return sign(unSignStr, mchPrivateKeyStr);
        } catch (Exception e) {
            log.error("算签异常, 待签名字符串：{}，异常信息：", unSignStr, e);
        }
        return null;
    }


    // 下发请求时的签名顺序
    public final static String singlePayKeyOrderBy = "userNo,tradeMerchantNo,productCode,requestTime,merchantOrderNo,receiverAccountNoEnc," +
            "receiverNameEnc,receiverAccountType,receiverBankChannelNo,paidAmount,currency,isChecked,paidDesc,paidUse,callbackUrl,firstProductCode";

    // 查询下发请求时的签名顺序
    public static final String querySingleKeyOrderBy = "userNo,merchantOrderNo";

    // 公共返回数据
    private final static String commonVerifyKeyOrderBy = "statusCode,message,data";

    public final static String verifyDataKeyOrderBy = "errorCode,errorDesc,userNo,merchantOrderNo";

    // 查单时校验渠道返回数据的签名顺序
    public static final String querySingleVerifyKeyOrderBy = "status,errorCode,errorDesc,userNo,tradeMerchantNo,merchantOrderNo,platformSerialNo," +
            "receiverAccountNoEnc,receiverNameEnc,paidAmount,fee";

    // 代付异步通知签名顺序
    public static final String verifyNotifyKeyOrderBy = "status,errorCode,errorCodeDesc,userNo,tradeMerchantNo,merchantOrderNo,platformSerialNo," +
            "receiverAccountNoEnc,receiverNameEnc,paidAmount,fee";

    public static String singlePaySign(Map<String, Object> params, String md5Key, String keyOrderBy) {
        StringBuilder hmacData = new StringBuilder();
        if (ObjectUtil.isNotEmpty(keyOrderBy)) {
            for (String str : keyOrderBy.split(",")) {
                if (ObjectUtil.isNotNull(params.get(str))) {
                    hmacData.append(params.get(str));
                }
            }
        }
        hmacData.append(md5Key);
        return SecureUtil.md5(hmacData.toString()).toUpperCase();
    }


    /***
     * 代付申请的同步返回的签名校验
     * @param channelSignKey
     * @return
     */
    public static boolean verifySinglePay(Map<String, Object> params, String channelSignKey, String keyOrderBy) {
        JSONObject object = JSONObject.parseObject(Convert.toStr(params.get("data")));
        String channelHmac = Convert.toStr(object.get("hmac"));
        StringBuilder hmacData = new StringBuilder();
        if (ObjectUtil.isNotEmpty(commonVerifyKeyOrderBy)) {
            for (String str : commonVerifyKeyOrderBy.split(",")) {
                if ("data".equals(str)) {
                    for (String subStr : keyOrderBy.split(",")) {
                        if (ObjectUtil.isNotNull(object.get(subStr))) {
                            if ("paidAmount".equals(subStr) || "fee".equals(subStr)) {
                                hmacData.append(formatAmount(Convert.toStr(object.get(subStr))));
                            } else {
                                hmacData.append(object.get(subStr));
                            }
                        }
                    }
                } else {
                    if ("paidAmount".equals(str) || "fee".equals(str)) {
                        hmacData.append(formatAmount(Convert.toStr(object.get(str))));
                    } else {
                        hmacData.append(Optional.ofNullable(params.get(str)).orElse(""));
                    }
                }
            }
        }
        hmacData.append(channelSignKey);
        String hmac = SecureUtil.md5(hmacData.toString()).toUpperCase();
        return channelHmac.equalsIgnoreCase(hmac);
    }

    /***
     * 代付申请的同步返回的签名校验
     * @param channelSignKey
     * @return
     */
    public static boolean verifyNotifySinglePay(Map<String, Object> params, String channelSignKey, String keyOrderBy) {
        String channelHmac = Convert.toStr(params.get("hmac"));
        StringBuilder hmacData = new StringBuilder();
        if (ObjectUtil.isNotEmpty(commonVerifyKeyOrderBy)) {
            for (String str : commonVerifyKeyOrderBy.split(",")) {
                if ("data".equals(str)) {
                    for (String subStr : keyOrderBy.split(",")) {
                        if (ObjectUtil.isNotNull(params.get(subStr))) {
                            if ("paidAmount".equals(subStr) || "fee".equals(subStr)) {
                                hmacData.append(formatAmount(Convert.toStr(params.get(subStr))));
                            } else {
                                hmacData.append(params.get(subStr));
                            }
                        }
                    }
                } else {
                    if ("paidAmount".equals(str) || "fee".equals(str)) {
                        hmacData.append(formatAmount(Convert.toStr(params.get(str))));
                    } else {
                        hmacData.append(Optional.ofNullable(params.get(str)).orElse(""));
                    }
                }
            }
        }
        hmacData.append(channelSignKey);
        log.info("[下发通知]校验渠道签名的原始串：" + hmacData.toString());
        String hmac = SecureUtil.md5(hmacData.toString()).toUpperCase();
        return channelHmac.equalsIgnoreCase(hmac);
    }

    private static String formatAmount(Object amount) {
        return String.format("%.2f", Convert.toBigDecimal(amount));
    }
}
