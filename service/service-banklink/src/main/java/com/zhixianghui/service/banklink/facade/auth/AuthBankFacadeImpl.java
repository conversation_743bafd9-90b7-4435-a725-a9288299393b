package com.zhixianghui.service.banklink.facade.auth;

import com.zhixianghui.common.statics.enums.auth.AuthChannelEnum;
import com.zhixianghui.facade.banklink.service.auth.PayAuthFacade;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.service.banklink.request.adm.AdmAuthApi;
import com.zhixianghui.service.banklink.request.auth.JoinpayAuthBiz;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 鉴权
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Service
public class AuthBankFacadeImpl implements PayAuthFacade {
    @Autowired
    private JoinpayAuthBiz joinpayAuthBiz;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Autowired
    private AdmAuthApi admAuthApi;
    @Override
    public AuthRespVo auth(AuthReqVo reqVo) {
        if (reqVo.getAuthChannel() == null) {
            final String authChannel = dataDictionaryFacade.getSystemConfig("AUTH_CHANNEL");
            if (StringUtils.equals(authChannel, "ADM")) {
                return admAuthApi.auth(reqVo);
            }else {
                return joinpayAuthBiz.auth(reqVo);
            }
        }else {
            if (StringUtils.equals(reqVo.getAuthChannel(), AuthChannelEnum.ADM.name())) {
                return admAuthApi.auth(reqVo);
            }else {
                return joinpayAuthBiz.auth(reqVo);
            }
        }
    }
}
