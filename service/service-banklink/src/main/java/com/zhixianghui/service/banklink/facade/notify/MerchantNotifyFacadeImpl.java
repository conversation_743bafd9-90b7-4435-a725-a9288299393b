package com.zhixianghui.service.banklink.facade.notify;

import com.zhixianghui.facade.banklink.service.notify.MerchantNotifyFacade;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.service.banklink.core.biz.notify.MerchantNotifyBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-22 09:45
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantNotifyFacadeImpl implements MerchantNotifyFacade {
    private final MerchantNotifyBiz merchantNotifyBiz;
    @Override
    public void sendMerchantNotify(MerchantNotifyParam merchantNotifyParam) {
        merchantNotifyBiz.sendMerchantNotify(merchantNotifyParam);
    }
}
