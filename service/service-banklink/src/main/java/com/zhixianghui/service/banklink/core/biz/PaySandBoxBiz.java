package com.zhixianghui.service.banklink.core.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.facade.banklink.entity.MockMerchant;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.banklink.core.services.MockMerchantService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class PaySandBoxBiz {

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Autowired
    private MockMerchantService mockMerchantService;

    public PayRespVo handle(PayReqVo reqVo) throws BizException {
        String sandBox = dataDictionaryFacade.getSystemConfig("SAND_BOX");
        if (!StringUtils.equals(sandBox, "ACTIVE")) {
            return null;
        }
        MockMerchant mockMerchant = mockMerchantService.getOne(new QueryWrapper<MockMerchant>().eq("employer_no", reqVo.getEmployerNo()));
        if (mockMerchant == null) {
            return null;
        }

        PayRespVo payRespVo = new PayRespVo();
        //金额小于0.5模拟成功,大于等于0.5 失败
        if (AmountUtil.lessThan(new BigDecimal(reqVo.getReceiveAmount()), new BigDecimal("0.5"))) {

            notifyFacade.sendOne(
                    MessageMsgDest.TOPIC_MOCK_SUCCESS_CALL_BACK,
                    reqVo.getEmployerNo(),
                    reqVo.getPlatTrxNo(),
                    NotifyTypeEnum.TRADE_MOCK_SUCCESS_CALL_BACK_NOTIFY.getValue(),
                    MessageMsgDest.TAG_MOCK_SUCCESS_CALL_BACK,
                    reqVo.getPlatTrxNo()
            );

            payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
            payRespVo.setBizCode("");
            payRespVo.setBankOrderNo(reqVo.getBankOrderNo());
            payRespVo.setBizMsg("");
            return payRespVo;
        }else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模拟失败");
        }
    }


}
