package com.zhixianghui.service.banklink.request.pay;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ConvertMessageBiz {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConvertMessageBiz.class);

    private static final String ERROR_MESSAGE_MAPPING_ENUM = "ErrorMessageMappingEnum";
    public static final String TARGET_DICTIONARY_PREFIX = "target:dictionary:";

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Autowired
    private RedisClient redisClient;



    protected void convert(PayReceiveRespVo respVo) {
        if (respVo == null || StringUtils.isBlank(respVo.getBizCode())) {
            return;
        }
        String msg = null;
        try {
            msg = handle(respVo.getBizCode());
        } catch (Exception e) {
            LOGGER.error("汇聚分账方代付回调信息转换失败: {}", JSONObject.toJSON(respVo), e);
        }
        if (StringUtils.isBlank(msg)) {
            return;
        }
        respVo.setBizMsg(msg);
    }

    protected void convert(PayRespVo respVo) {
        if (respVo == null || StringUtils.isBlank(respVo.getBizCode())) {
            return;
        }
        String msg = null;
        try {
            msg = handle(respVo.getBizCode());
        } catch (Exception e) {
            LOGGER.error("汇聚分账方代付响应信息转换失败: {}", JSONObject.toJSON(respVo), e);
        }
        if (StringUtils.isBlank(msg)) {
            return;
        }
        respVo.setBizMsg(msg);
    }

    private String handle(String bizCode) {
        String redisKey = TARGET_DICTIONARY_PREFIX + ERROR_MESSAGE_MAPPING_ENUM;
        String targetValue = redisClient.get(redisKey);
        DataDictionary targetDictionary = null;
        if (StringUtils.isBlank(targetValue)) {
            targetDictionary = dataDictionaryFacade.getDataDictionaryByName(ERROR_MESSAGE_MAPPING_ENUM);
            redisClient.set(redisKey, JSONObject.toJSONString(targetDictionary),60);
        } else {
            targetDictionary = JSONObject.parseObject(targetValue, DataDictionary.class);
        }

        if (targetDictionary == null) {
            LOGGER.error("字典不存在, 转化失败");
            return null;
        }
        Map<String, String> map = targetDictionary.getItemList().stream().collect(Collectors.toMap(DataDictionary.Item :: getCode, DataDictionary.Item :: getDesc));
        return map.getOrDefault(bizCode, "");
    }

    public String getAuthMsg(String errorMsg){

        String code = StringUtils.substringBetween(errorMsg, "[", "]");
        if (StringUtils.isBlank(code)||!NumberUtil.isNumber(code)) {
            return "[鉴权失败]-"+errorMsg;
        }

        String message = this.handle("AU"+code);
        if (StringUtils.isBlank(message)) {
            message = "[鉴权失败]-要素认证情况未知";
        }else {
            message = "[鉴权失败]-" + message;
        }
        return message;
    }

    public String getAdmAuthMsg(String errorMsg,String errorCode){

        String code = StringUtils.substringBetween(errorMsg, "[", "]");
        code = StringUtils.isBlank(code) ? errorCode : errorCode + code;

        String message = this.handle("AU-ADM_"+code);
        if (StringUtils.isBlank(message)) {
            message = "[鉴权失败]-"+errorMsg;
        }else {
            message = "[鉴权失败]-" + message;
        }
        return message;
    }
}
