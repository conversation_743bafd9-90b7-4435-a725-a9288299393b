package com.zhixianghui.service.banklink.core.biz.message.email;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.dto.rmq.MsgDto;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.entity.MailReceiver;
import com.zhixianghui.service.banklink.core.biz.message.resolver.TemplateResolver;
import com.zhixianghui.service.banklink.core.dao.MailReceiverDao;
import com.zhixianghui.starter.comp.component.EmailSender;
import com.zhixianghui.starter.comp.component.RMQSender;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

@Component
@Log4j2
public class EmailBiz {
    private final static String TEMPLATE_FOLDER = "email" + File.separator;//邮件模版的路径：classpath:templates/email/
    @Autowired
    EmailSender emailSender;
    @Autowired
    TemplateResolver templateResolver;
    @Autowired
    RMQSender rmqSender;
    @Autowired
    MailReceiverDao mailReceiverDao;

    public boolean sendToGroupSync(String groupKey, String subject, String content, boolean isHtml) {
        StringBuilder to = new StringBuilder();
        String[] cc = new String[]{};
        EmailFromEnum from = splitFromMailReceiver(groupKey, to, cc);
        if (isHtml) {
            return emailSender.sendHtmlMail(from.getAccount(), to.toString(), cc, subject, content,false,null,null);
        } else {
            return emailSender.sendTextMail(from.getAccount(), to.toString(), cc, subject, content);
        }
    }

    public boolean sendToGroupAsync(String groupKey, String subject, String content, boolean isHtml) {
        StringBuilder to = new StringBuilder();
        String[] cc = new String[]{};
        EmailFromEnum from = splitFromMailReceiver(groupKey, to, cc);

        MsgDto<EmailParamDto> msgDto = new MsgDto<>();
        msgDto.setTopic(MessageMsgDest.TOPIC_SEND_EMAIL_ASYNC);
        msgDto.setTags(MessageMsgDest.TAG_SEND_EMAIL_ASYNC);
        msgDto.setTrxNo(from.getAccount());

        EmailParamDto emailParamDto = new EmailParamDto();
        emailParamDto.setFrom(from);
        emailParamDto.setTo(to.toString());
        emailParamDto.setCc(cc);
        emailParamDto.setSubject(subject);
        emailParamDto.setContent(content);
        emailParamDto.setHtmlFormat(isHtml);

        msgDto.setJsonParam(emailParamDto);

        return rmqSender.sendOne(msgDto).getSendOk();
    }

    private EmailFromEnum splitFromMailReceiver(String groupKey, StringBuilder to, String[] cc) {
        MailReceiver mailReceiver = mailReceiverDao.getByGroupKey(groupKey);
        if (mailReceiver == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("groupKey=" + "groupKey对应的收件人记录不存在");
        }
        EmailFromEnum from = EmailFromEnum.getEnum(mailReceiver.getFrom());
        List<String> toList = JsonUtil.toList(mailReceiver.getTo(), String.class);
        if (toList != null && !toList.isEmpty()) {
            for (int i = 0; i < toList.size(); i++) {
                if (i == 0) {
                    to.append(toList.get(i));
                } else {
                    cc[i - 1] = toList.get(i);
                }
            }
        }
        return from;
    }


    public boolean sendSync(EmailParamDto param) {
        String content;
        if (StringUtil.isNotEmpty(param.getTpl())) {
            log.info("email:{}", TEMPLATE_FOLDER + param.getTpl());
            content = templateResolver.resolve(TEMPLATE_FOLDER + param.getTpl(), param.getTplParam());
        } else {
            content = param.getContent();
        }

        if (param.isHtmlFormat()) {
            return emailSender.sendHtmlMail(param.getFrom().getAccount(), param.getTo(), param.getCc(), param.getSubject(), content,param.isHasAttachment(),param.getFilePath(),param.getFileName());
        } else {
            return emailSender.sendTextMail(param.getFrom().getAccount(), param.getTo(), param.getCc(), param.getSubject(), content);
        }
    }

    public boolean sendAsync(EmailParamDto param) {
        String trxNo = param.getFrom().getAccount() + "_" + param.getTpl();

        MsgDto<EmailParamDto> msgDto = new MsgDto<>();
        msgDto.setTopic(MessageMsgDest.TOPIC_SEND_EMAIL_ASYNC);
        msgDto.setTags(MessageMsgDest.TAG_SEND_EMAIL_ASYNC);
        msgDto.setTrxNo(trxNo);
        msgDto.setJsonParam(param);
        return rmqSender.sendOne(msgDto).getSendOk();
    }
}
