package com.zhixianghui.service.banklink.request.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.banklink.ParticipantsInfo;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.BanklinkExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.enums.AlipayErrorCode;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.pay.QueryPayOrderReqVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawReqVo;
import com.zhixianghui.facade.banklink.vo.pay.WithdrawRespVo;
import com.zhixianghui.facade.banklink.vo.report.AlipayJobPayslipSyncReqVo;
import com.zhixianghui.facade.banklink.vo.report.AlipayJobPayslipSyncRspVo;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.RecordItemStatusEnum;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.service.banklink.constant.AlipayConstant;
import com.zhixianghui.service.banklink.core.biz.RobotBiz.RobotBiz;
import com.zhixianghui.service.banklink.request.alipay.AliPayService;
import com.zhixianghui.service.banklink.request.alipay.AlipayEbppIndustryJobPayslipSyncResponse;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AlipayBiz {

    private final RobotBiz robotBiz;
    private final RedisClient redisClient;
    private final RedisLock redisLock;
    final private AliPayService aliPayService;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private RecordItemFacade recordItemFacade;

    public AlipayJobPayslipSyncRspVo jobPayslipSync(AlipayJobPayslipSyncReqVo reqVo) {
        try {
            AlipayJobPayslipSyncRspVo rspVo = new AlipayJobPayslipSyncRspVo();
            rspVo.setMchOrderNo(reqVo.getMchOrderNo());
            rspVo.setPlatTrxNo(reqVo.getPlatTrxNo());

            AlipayEbppIndustryJobPayslipSyncResponse response = aliPayService.jobPayslipSync(reqVo);
            rspVo.setSuccess(response.isSuccess());
            rspVo.setBizNo(response.getBizNo());
            rspVo.setCode(response.getCode());
            rspVo.setMsg(response.getMsg());
            rspVo.setSubCode(response.getSubCode());
            rspVo.setSubMsg(response.getSubMsg());
            if (rspVo.isSuccess()) {
                log.info("[支付宝就业到账啦明细同步] 接口调用成功 mchOrderNo: {} platTrxNo: {}", reqVo.getMchOrderNo(), reqVo.getPlatTrxNo());
            } else {
                log.error("[支付宝就业到账啦明细同步] 接口调用失败 msg: {} subMsg: {} mchOrderNo: {} platTrxNo: {}",
                        rspVo.getMsg(), rspVo.getSubMsg(), reqVo.getMchOrderNo(), reqVo.getPlatTrxNo());
            }
            return rspVo;
        } catch (AlipayApiException e) {
            log.error("支付宝就业到账啦明细同步失败 AlipayJobPayslipSyncReqVo：{}", JsonUtil.toString(reqVo), e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付宝就业到账啦明细同步失败");
        }
    }

    /**
     * 企业支付宝支付实现
     * @param reqVo
     * @return
     */
    public PayRespVo pay(PayReqVo reqVo) {
        boolean do_step_1 = false;
        boolean do_step_2 = false;
        PayRespVo payRespVo = new PayRespVo();

        if (reqVo.isRetry()) {
            if (reqVo.getRetryStep() == 1) {
                try {
                    JSONObject queryResp = JSONObject.parseObject(aliPayService.transCommonQuery(reqVo.getBankOrderNo(),
                            AlipayConstant.ALIPAY_BIZ_ENTRUST_ALLOCATION,AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD)).getJSONObject("alipay_fund_trans_common_query_response");
                    if (queryResp.getInteger("code").intValue() == 40004) {
                        do_step_1 = true;
                    }else if (queryResp.getInteger("code").intValue() == 10000) {
                        do_step_1 = false;
                    }else {
                        throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("["+reqVo.getBankOrderNo()+"]查询记账本间转账订单状态异常："+JSON.toJSONString(queryResp));
                    }
                } catch (Exception e) {
                    throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("["+reqVo.getBankOrderNo()+"]查询记账本间转账出错:"+e.getMessage());
                }
            }
        }else {
            do_step_1 = true;
        }

        //1. 从商户记账本转账至供应商记账本
        if (do_step_1) {
            reqVo.setBizScene(AlipayConstant.ALIPAY_BIZ_ENTRUST_ALLOCATION);
            reqVo.setProductCode(AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD);
            JSONObject uniTransRsp;
            try{
                 uniTransRsp = this.uniTrans(reqVo).getJSONObject("alipay_fund_trans_uni_transfer_response");
            }catch (SocketTimeoutException e){
                //网络异常时触发重试，只重试一次
                if (!reqVo.isRetry()){
                    return retryStep(payRespVo,reqVo,1);
                }else{
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND, reqVo.getSubMerchantNo(), reqVo.getBankOrderNo(), NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(), MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.S_30.getValue());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("网络异常重试失败，退款处理");
                }
            }

            if (uniTransRsp.getInteger("code").intValue() != AlipayErrorCode.SUCCESS.getCode()) {
                if (uniTransRsp.getInteger("code").intValue() == AlipayErrorCode.UNKOWN_ERROR.getCode()) {
                    if (!reqVo.isRetry()) {
                        return retryStep(payRespVo,reqVo,1);
                    }else {
                        notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND, reqVo.getSubMerchantNo(), reqVo.getBankOrderNo(), NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(), MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.S_30.getValue());
                        throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg(uniTransRsp.getString("sub_msg"));
                    }
                } else{
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND, reqVo.getSubMerchantNo(), reqVo.getBankOrderNo(), NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(), MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.S_30.getValue());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(uniTransRsp.getString("sub_msg"));
                }
            }
        }

        if (reqVo.isRetry()) {
            try {
                JSONObject queryResp = JSONObject.parseObject(aliPayService.transCommonQuery(reqVo.getBankOrderNo() + "P",
                        AlipayConstant.ALIPAY_BIZ_ENTRUST_TRANSFER, AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD)).getJSONObject("alipay_fund_trans_common_query_response");
                if (queryResp.getInteger("code").intValue() == 40004) {
                    do_step_2 = true;
                } else if (queryResp.getInteger("code").intValue() == 10000) {
                    payRespVo.setBankTrxNo(queryResp.getString("order_id"));
                }else {
                    throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("["+reqVo.getBankOrderNo()+"]查询发放到个人订单出错:"+JSON.toJSONString(queryResp));
                }
            } catch (Exception e) {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("["+reqVo.getBankOrderNo()+"]查询发放到个人订单出错:"+e.getMessage());
            }
        }else {
            do_step_2 = true;
        }

        if (do_step_2) {
            //2. 发放到个人支付宝
            //判断发放类型
            JSONObject grantResp = null;
            if (reqVo.isRetry()) {
                // 如果重试订单，则先确认记账本间转账是成功的
                try {
                    JSONObject queryResp = JSONObject.parseObject(aliPayService.transCommonQuery(reqVo.getBankOrderNo(),
                            AlipayConstant.ALIPAY_BIZ_ENTRUST_ALLOCATION,AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD)).getJSONObject("alipay_fund_trans_common_query_response");
                    if (queryResp.getInteger("code").intValue() != AlipayErrorCode.SUCCESS.getCode()) {
                        log.info("[{}]-重试订单步骤2：记账本间转账状态不为成功，进入重试", reqVo.getBankOrderNo());
                        return this.retryStep(payRespVo, reqVo, 2);
                    }
                    log.info("[{}]-重试订单步骤2: 记账本间转账已为成功，继续发放到个人", reqVo.getBankOrderNo());
                } catch (Exception e) {
                    log.error( "["+reqVo.getBankOrderNo()+"]-重试订单步骤2:查询记账本间转账订单失败", e);
                    return this.retryStep(payRespVo, reqVo, 2);
                }
            }

            try{
                if (reqVo.getChannelType() == ChannelTypeEnum.ALIPAY.getValue()){
                    grantResp = this.uniTransToPersonal(reqVo).getJSONObject("alipay_fund_trans_uni_transfer_response");
                }else if (reqVo.getChannelType() == ChannelTypeEnum.BANK.getValue()){
                    grantResp = this.uniTransToBankCard(reqVo).getJSONObject("alipay_fund_trans_uni_transfer_response");
                }

                //如果余额不足，触发重试
                if (grantResp != null
                        && StringUtils.equals(grantResp.getString("sub_code"), "BALANCE_IS_NOT_ENOUGH")) {
                    log.info("订单号：[{}] 余额不足，等待重试",reqVo.getBankOrderNo());
                    grantResp.put("code", AlipayErrorCode.MAINSTAY_BALANCE_NOT_ENOUGH.getCode());
                    grantResp.put("sub_msg", "未知原因，请重试");
                }
            }catch (SocketTimeoutException e){
                //网络异常时触发重试，只重试一次
                if (!reqVo.isRetry()){
                    return retryStep(payRespVo,reqVo,2);
                }else{
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND, reqVo.getSubMerchantNo(), reqVo.getBankOrderNo(), NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(), MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.S_30.getValue());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("重试操作，网络异常，退款处理");
                }
            }
            if (grantResp.getInteger("code").intValue() != AlipayErrorCode.SUCCESS.getCode()) {
                if (grantResp.getInteger("code").intValue() == AlipayErrorCode.UNKOWN_ERROR.getCode()) {
                    if (!reqVo.isRetry()) {
                        retryStep(payRespVo,reqVo,2);
                    }else {
                        notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND, reqVo.getSubMerchantNo(), reqVo.getBankOrderNo(), NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(), MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.S_30.getValue());
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg(grantResp.getString("sub_msg"));
                    }
                } else if (grantResp.getInteger("code").intValue() == AlipayErrorCode.MAINSTAY_BALANCE_NOT_ENOUGH.getCode()) {
                    if (getAcceptTimes(AlipayConstant.MAINSTAY_BALANCE_NOT_ENOUGH_KEY, reqVo.getBankOrderNo()) < AlipayConstant.MAX_MAINSTAY_BALANCE_NOT_ENOUGH_TIMES) {
                        retryStep(payRespVo, reqVo, 2);
                    } else {
                        notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND, reqVo.getSubMerchantNo(), reqVo.getBankOrderNo(), NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(), MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.S_30.getValue());
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg(grantResp.getString("sub_msg"));
                    }
                } else {
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND, reqVo.getSubMerchantNo(), reqVo.getBankOrderNo(), NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(), MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.S_30.getValue());
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(grantResp.getString("sub_msg"));
                }
            } else {
                payRespVo.setBankTrxNo(grantResp.getString("order_id"));
            }
        }

        payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
        payRespVo.setBizCode("");
        payRespVo.setBankOrderNo(reqVo.getBankOrderNo());
        payRespVo.setBizMsg("");
        return payRespVo;
    }

    private PayRespVo retryStep(PayRespVo payRespVo, PayReqVo reqVo, int step) {
        log.info("订单号：[{}]，触发不换单打款重试，重试步骤：[{}]",reqVo.getBankOrderNo(),step);
        reqVo.setRetry(true);
        reqVo.setRetryStep(step);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_BUSY,
                reqVo.getSubMerchantNo(),
                reqVo.getBankOrderNo(),
                NotifyTypeEnum.ALIPAY_BUSY_RETRY.getValue(),
                step == 1 ? MessageMsgDest.TAG_ALIPAY_BUSY_STEP_1 : MessageMsgDest.TAG_ALIPAY_BUSY_STEP_2,
                JSON.toJSONString(reqVo),
                MsgDelayLevelEnum.M_1.getValue());

        payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
        payRespVo.setBizCode("");
        payRespVo.setBankOrderNo(reqVo.getBankOrderNo());
        payRespVo.setBizMsg("");
        return payRespVo;
    }

    /**
     * 从用工企业的记账本转到代征主体的记账本
     * @param reqVo
     * @return
     */
    private JSONObject uniTrans(PayReqVo reqVo) throws SocketTimeoutException {
        String totalAmount = new BigDecimal(reqVo.getReceiveAmount()).add(new BigDecimal(reqVo.getServiceFee())).toPlainString();

        ParticipantsInfo payee = new ParticipantsInfo();
        payee.setAgreementNo(reqVo.getPayeeAgreementNo());
        payee.setIdentity(reqVo.getParentMerchantNo());

        ParticipantsInfo payer = new ParticipantsInfo();
        payer.setAgreementNo(reqVo.getPayerAgreementNo());
        payer.setIdentity(reqVo.getSubMerchantNo());

        String remark = reqVo.getEmployerNo()+ "-智享汇专项转账";
        try {
            String s = aliPayService.uniTransfer(reqVo.getBankOrderNo(), totalAmount, reqVo.getRemitRemark(), payee, payer,remark);
            return JSON.parseObject(s);
        } catch (AlipayApiException e) {
            if (e.getMessage().contains("SocketTimeoutException")){
                log.error("网络异常，订单号：[{}]，等待重试",reqVo.getBankOrderNo());
                throw new SocketTimeoutException(e.getCause().getMessage());
            }else{
                log.error("记账本间状态异常",e);
                throw CommonExceptions.BIZ_INVALID.newWith("记账本间状态异常",e);
            }
        }
    }

    /**
     * 退款回用工企业的记账本
     * @param reqVo
     * @return
     */
    public void uniTransRefund(PayReqVo reqVo) {
        String totalAmount = new BigDecimal(reqVo.getReceiveAmount()).add(new BigDecimal(reqVo.getServiceFee())).toPlainString();

        ParticipantsInfo payer = new ParticipantsInfo();
        payer.setAgreementNo(reqVo.getPayeeAgreementNo());
        payer.setIdentity(reqVo.getParentMerchantNo());

        ParticipantsInfo payee = new ParticipantsInfo();
        payee.setAgreementNo(reqVo.getPayerAgreementNo());
        payee.setIdentity(reqVo.getSubMerchantNo());

        RecordItem recordItem;

        //获取锁
        log.info("[退款环节: {}]==>获取退款锁",reqVo.getBankOrderNo() + "R");
        String lockKey = String.join(":", AlipayConstant.ALIPAY_REFUND_AUTH_KEY,reqVo.getBankOrderNo() + "R");
        String clientId = redisLock.tryLockLong(lockKey,0,1);
        if(clientId == null){
            log.info("[退款环节: {}]==>获取退款锁失败，直接丢弃",reqVo.getBankOrderNo() + "R");
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }
        try {
            //判断是否可以退回
            Integer isUniTransfer = validTransfer(reqVo);
            if (isUniTransfer != null){
                recordItem = recordItemFacade.getByRemitPlatTrxNo(reqVo.getBankOrderNo());
                recordItem.setProcessStatus(isUniTransfer);
                recordItemFacade.update(recordItem);
            }

            if (isUniTransfer != null && isUniTransfer.intValue() == RecordItemStatusEnum.REFUNDING.getValue()){
                String json = aliPayService.uniTransfer(reqVo.getBankOrderNo()+"R", totalAmount, "[退回]"+reqVo.getRemitRemark(), payee, payer,reqVo.getEmployerNo() + "-智享汇专项转账退款");
                JSONObject refundResult = JSON.parseObject(json).getJSONObject("alipay_fund_trans_uni_transfer_response");
                //对支付宝系统判断
                if (StringUtils.equals(refundResult.getString("status"), SuccessFailEnum.SUCCESS.name())){
                    recordItem = recordItemFacade.getByRemitPlatTrxNo(reqVo.getBankOrderNo());
                    recordItem.setProcessStatus(RecordItemStatusEnum.PAY_SPECIAL_FAIL.getValue());
                    recordItemFacade.update(recordItem);
                    log.info("退款完成，订单号：[{}]",reqVo.getBankOrderNo()+"R");
                }else{
                    throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("请求退款接口返回失败，进入重试方法，返回信息：" + refundResult.toJSONString());
                }
            }else{
                log.info("无需执行退款逻辑，bankOrderNo:{}",reqVo.getBankOrderNo());
            }
        } catch (Exception e) {
            uniPayTransRefund(reqVo);
        } finally {
            //释放锁
            redisLock.unlockLong(clientId);
        }
    }

    private void uniPayTransRefund(PayReqVo reqVo){
        //限制退款重试次数3次
        if (getAcceptTimes(reqVo.getBankOrderNo(),AlipayConstant.REFUND_TIMES_PRE_KEY) > AlipayConstant.MAX_REFUND_TIMES){
            sendToRobot(reqVo);
            log.info("退款重试次数大于五次，默认退款失败，bankOrderNo:{}",reqVo.getBankOrderNo());
        }else{
            log.error("执行退款重试处理，订单号：" + reqVo.getBankOrderNo());
            notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_REFUND,reqVo.getSubMerchantNo(),reqVo.getBankOrderNo(),NotifyTypeEnum.ALIPAY_REFUND_MSG.getValue(), MessageMsgDest.TAG_ALIPAY_REFUND, JSON.toJSONString(reqVo), MsgDelayLevelEnum.S_30.getValue());
        }
    }

    private void sendToRobot(PayReqVo reqVo) {
        MarkDownMsg markDownMsg = new MarkDownMsg();
        markDownMsg.setRobotType(RobotTypeEnum.GRANTING_ROBOT.getType());
        markDownMsg.setUnikey("REFUND:" + reqVo.getBankOrderNo());
        StringBuffer sb = new StringBuffer("#### 退款失败\\n")
                .append("> 平台流水号：").append(reqVo.getPlatTrxNo())
                .append("\\n > 商户名称：").append(reqVo.getMchName())
                .append("\\n > 实发金额：").append(reqVo.getReceiveAmount())
                .append("\\n > 服务费：").append(reqVo.getServiceFee())
                .append("\\n > 发放时间：").append(DateUtil.formatDateTime(reqVo.getCreateTime()));

        markDownMsg.setContent(sb.toString());

        robotBiz.pushMarkDownAsync(markDownMsg);
    }

    private Long getAcceptTimes(String bankOrderNo,String preKey) {
        Long acceptTime = 0L;
        try {
            acceptTime = redisClient.incr(preKey + bankOrderNo);
            redisClient.expire(preKey + bankOrderNo,60*60);
        }catch (Exception e){
            log.error("[退款环节: {}] ==> redis获取重试次数异常 忽略", bankOrderNo, e);
        }
        return acceptTime;
    }

    /**
     * 验证订单是否可以退款
     * @param reqVo
     * @return
     * @throws AlipayApiException
     */
    private Integer validTransfer(PayReqVo reqVo) throws AlipayApiException,BizException{
        //查询订单编号是否存在
        JSONObject searchRes = getAlipayOrder(reqVo.getBankOrderNo(),AlipayConstant.ALIPAY_BIZ_ENTRUST_ALLOCATION,AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD);
        int searchResCode = searchRes.getIntValue("code");
        if (searchResCode == AlipayErrorCode.BIZ_ERROR.getCode() && StringUtils.equals(searchRes.getString("sub_code"),"ORDER_NOT_EXIST")){
            //原支付单不存在，不需要退款
            log.info("原支付单不存在，不需要退款，打款流水号：{}",reqVo.getBankOrderNo());
            return null;
        }else if (searchResCode == AlipayErrorCode.SUCCESS.getCode() && StringUtils.equals(searchRes.getString("status"),"SUCCESS")){
            //原支付单存在，判断退款单是否存在
            JSONObject fundSearchRes = getAlipayOrder(reqVo.getBankOrderNo() + "R",AlipayConstant.ALIPAY_BIZ_ENTRUST_ALLOCATION,AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD);
            int fundSearchResCode = fundSearchRes.getIntValue("code");
            if (fundSearchResCode == AlipayErrorCode.BIZ_ERROR.getCode() && StringUtils.equals(fundSearchRes.getString("sub_code"),"ORDER_NOT_EXIST")) {
                //退款单不存在，执行退款操作
                log.info("退款单不存在，执行退款操作,退款订单流水号：{}", reqVo.getBankOrderNo() + "R");
                return RecordItemStatusEnum.REFUNDING.getValue();
            } else if (fundSearchResCode == AlipayErrorCode.SUCCESS.getCode() && StringUtils.equals(searchRes.getString("status"),"SUCCESS")) {
                log.info("退款单已存在，无需执行退款，退款订单流水号：{}", reqVo.getBankOrderNo() + "R");
                //退款单存在，不需要继续退款
                return RecordItemStatusEnum.PAY_SPECIAL_FAIL.getValue();
            }
        }
        throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("查询失败，进入退款重试流程");
    }

    /**
     * 发放到银行卡
     * @param reqVo
     * @return
     */
    private JSONObject uniTransToBankCard(PayReqVo reqVo) throws SocketTimeoutException {
        try {
            ParticipantsInfo payer = new ParticipantsInfo();
            payer.setAgreementNo(reqVo.getPayeeAgreementNo());
            payer.setIdentity(reqVo.getParentMerchantNo());

            ParticipantsInfo payee = new ParticipantsInfo();
            payee.setIdentity(reqVo.getReceiveAccountNo());
            payee.setName(reqVo.getReceiveName());

            String resp = aliPayService.uniBankCard(reqVo.getBankOrderNo() + "P",reqVo.getReceiveAmount(),reqVo.getRemitRemark(),payee,payer);
            JSONObject respnseBody = JSON.parseObject(resp);
            return respnseBody;
        } catch (AlipayApiException e) {
            if (e.getMessage().contains("SocketTimeoutException")){
                log.error("网络异常，订单号：[{}]，等待重试",reqVo.getBankOrderNo());
                throw new SocketTimeoutException(e.getCause().getMessage());
            }else{
                log.error("发放到个人银行卡调用失败",e);
                throw CommonExceptions.BIZ_INVALID.newWith("记账本发放到个人银行卡调用失败",e);
            }
        }
    }

    /**
     * 发放到个人（支付宝账户）
     * @param reqVo
     * @return
     */
    private JSONObject uniTransToPersonal(PayReqVo reqVo) throws SocketTimeoutException {
        ParticipantsInfo payee = new ParticipantsInfo();
        payee.setIdentity(reqVo.getReceiveAccountNo());
        payee.setName(reqVo.getReceiveName());

        ParticipantsInfo payer = new ParticipantsInfo();
        payer.setAgreementNo(reqVo.getPayeeAgreementNo());
        payer.setAccountbookSceneCode("SATF_FUND_BOOK");
        payer.setIdentity(reqVo.getParentMerchantNo());
        payer.setEmployerNo(reqVo.getEmployerNo());

        try {
            String s = aliPayService.transferToAlipay(reqVo.getBankOrderNo()+"P", reqVo.getReceiveAmount(), reqVo.getRemitRemark(),reqVo.getRealPayerName(),payee, payer);
            JSONObject respnseBody = JSON.parseObject(s);
            return respnseBody;
        } catch (AlipayApiException e) {
            if (e.getMessage().contains("SocketTimeoutException")){
                log.error("网络异常，订单号：[{}]，等待重试",reqVo.getBankOrderNo());
                throw new SocketTimeoutException(e.getCause().getMessage());
            }else{
                log.error("记账本间状态异常，报错信息：[{}]",e.getCause().getMessage());
                throw CommonExceptions.BIZ_INVALID.newWith("记账本发放到个人支付宝接口调用失败",e);
            }
        }

    }

    /**
     * 查询支付宝订单
     * @param orderNo
     * @param bizScene
     * @param productCode
     * @return
     * @throws AlipayApiException
     */
    private JSONObject getAlipayOrder(String orderNo,String bizScene,String productCode) throws AlipayApiException{
        JSONObject jsonObject = JSONObject.parseObject(aliPayService.transCommonQuery(orderNo,bizScene,productCode))
                .getJSONObject("alipay_fund_trans_common_query_response");
        return jsonObject;
    }

    /**
     * 查询订单状态
     * @param queryPayOrderReqVo
     * @return
     */
    public PayRespVo queryAliPayOrder(QueryPayOrderReqVo queryPayOrderReqVo) throws BizException {
        PayRespVo payRespVo = new PayRespVo();
        payRespVo.setBankOrderNo(queryPayOrderReqVo.getBankOrderNo());
        try {
            //查询供应商-个人订单是否存在
            //由于属于第二步，如果供应商-个人订单存在，则整个订单完成
            JSONObject searchRes = getAlipayOrder(queryPayOrderReqVo.getBankOrderNo() + "P"
                    ,AlipayConstant.ALIPAY_BIZ_ENTRUST_TRANSFER,AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD);

            int secondCode = searchRes.getIntValue("code");
            if (secondCode == AlipayErrorCode.SUCCESS.getCode()){
                payRespVo.setBankTrxNo(searchRes.getString("order_id"));
                payRespVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
            }else if (secondCode == AlipayErrorCode.BIZ_ERROR.getCode() && StringUtils.equals(searchRes.getString("sub_code"),"ORDER_NOT_EXIST")){
                //查询不到第二步订单，说明有可能在第一步的时候出现异常
                //查询第一步订单
                JSONObject firstSearchRes = getAlipayOrder(queryPayOrderReqVo.getBankOrderNo()
                        ,AlipayConstant.ALIPAY_BIZ_ENTRUST_ALLOCATION,AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD);

                int firstCode = firstSearchRes.getIntValue("code");
                if (firstCode == AlipayErrorCode.SUCCESS.getCode()){
                    //判断是否存在退款订单
                    JSONObject refundSearchRes = getAlipayOrder(queryPayOrderReqVo.getBankOrderNo() + "R"
                            ,AlipayConstant.ALIPAY_BIZ_ENTRUST_ALLOCATION,AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD);

                    int refundCode = refundSearchRes.getIntValue("code");
                    if (refundCode == AlipayErrorCode.SUCCESS.getCode()){
                        payRespVo.setBizMsg("已退款");
                        payRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                        payRespVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_REFUND.getSysErrorCode()));
                    }else if (refundCode == AlipayErrorCode.BIZ_ERROR.getCode() && StringUtils.equals(searchRes.getString("sub_code"),"ORDER_NOT_EXIST")){
                        //查询不到退款订单，有可能正在处理转账
                        payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
                    }else{
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询退款订单状态失败，返回信息："+refundSearchRes.toJSONString());
                    }
                }else if (firstCode == AlipayErrorCode.BIZ_ERROR.getCode() && StringUtils.equals(searchRes.getString("sub_code"),"ORDER_NOT_EXIST")){
                    //第一步订单都不存在，说明整个流程失败
                    payRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                    payRespVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()));
                }else{
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询用工企业-供应商订单状态失败，返回信息："+firstSearchRes.toJSONString());
                }
            }else{
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询供应商-个人订单状态失败，返回信息："+searchRes.toJSONString());
            }
        }catch (AlipayApiException e){
            throw CommonExceptions.BIZ_INVALID.newWith("查询订单状态失败",e);
        }
        return payRespVo;
    }

    //提现到企业支付宝
    public WithdrawRespVo withdraw(WithdrawReqVo withdrawReqVo) {

        ParticipantsInfo payee = new ParticipantsInfo();
        payee.setIdentity(withdrawReqVo.getReceiveAccountNo());
        payee.setName(withdrawReqVo.getReceiveName());

        ParticipantsInfo payer = new ParticipantsInfo();
        payer.setAgreementNo(withdrawReqVo.getPayerAgreementNo());
        payer.setIdentity(withdrawReqVo.getPayerAccountBookId());

        try {
            String s = aliPayService.withdraw(withdrawReqVo.getBankOrderNo(), withdrawReqVo.getReceiveAmount(), withdrawReqVo.getRemitRemark(),withdrawReqVo.getRealPayerName(),payee, payer);
            JSONObject withdrawrResult = JSON.parseObject(s).getJSONObject("alipay_fund_trans_uni_transfer_response");

            WithdrawRespVo withdrawRespVo = new WithdrawRespVo();
            withdrawRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
            withdrawRespVo.setBizCode("");
            withdrawRespVo.setBankOrderNo(withdrawReqVo.getBankOrderNo());
            withdrawRespVo.setBizMsg("");
            withdrawRespVo.setBankTrxNo(withdrawrResult.getString("order_id"));
            return withdrawRespVo;
        } catch (BizException e){
            log.error("记账本提现到支付宝接口调用失败",e);
            throw CommonExceptions.BIZ_INVALID.newWith(e.getErrMsg(),e);
        }catch (AlipayApiException e) {
            log.error("记账本提现到支付宝接口调用失败",e);
            throw CommonExceptions.BIZ_INVALID.newWith(e.getErrMsg(),e);
        }
    }

    /**
     * 提现到对公银行卡
     * @param withdrawReqVo
     * @return
     */
    public WithdrawRespVo withdrawToBank(WithdrawReqVo withdrawReqVo) {

        ParticipantsInfo payee = new ParticipantsInfo();
        payee.setIdentity(withdrawReqVo.getReceiveAccountNo());
        payee.setName(withdrawReqVo.getReceiveName());
        payee.setOrganizationName(withdrawReqVo.getOrganizationName());
        payee.setBankChannelNo(withdrawReqVo.getBankChannelNo());

        ParticipantsInfo payer = new ParticipantsInfo();
        payer.setAgreementNo(withdrawReqVo.getPayerAgreementNo());
        payer.setIdentity(withdrawReqVo.getPayerAccountBookId());

        try {
            String s = aliPayService.withdrawToBank(withdrawReqVo.getBankOrderNo(), withdrawReqVo.getReceiveAmount(), withdrawReqVo.getRemitRemark(),payee, payer);
            JSONObject withdrawrResult = JSON.parseObject(s).getJSONObject("alipay_fund_trans_uni_transfer_response");

            WithdrawRespVo withdrawRespVo = new WithdrawRespVo();
            withdrawRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
            withdrawRespVo.setBizCode("");
            withdrawRespVo.setBankOrderNo(withdrawReqVo.getBankOrderNo());
            withdrawRespVo.setBizMsg("");
            withdrawRespVo.setBankTrxNo(withdrawrResult.getString("order_id"));
            return withdrawRespVo;
        } catch (BizException e){
            log.error("记账本提现到银行卡接口调用失败",e);
            throw CommonExceptions.BIZ_INVALID.newWith(e.getErrMsg(),e);
        }catch (AlipayApiException e) {
            log.error("记账本提现到银行卡接口调用失败",e);
            throw CommonExceptions.BIZ_INVALID.newWith(e.getErrMsg(),e);
        }
    }


    /**
     * 压力测试模拟
     * @param vo
     */
    private void stressSimulation(PayReqVo vo) {

        try {
            //转账到代征主体记账本
            log.info("[{}]转账到代征主体记账本",vo.getBankOrderNo());
//            aliPayService.accountBookQuery(vo.getPayerAgreementNo(), vo.getSubMerchantNo());
//            this.uniTrans(vo);
            TimeUnit.MILLISECONDS.sleep(1700L);
            //转出到个人支付宝
            log.info("[{}]转出到个人支付宝",vo.getBankOrderNo());
//            aliPayService.accountBookQuery(vo.getPayeeAgreementNo(), vo.getParentMerchantNo());
//            this.uniTransRefund(vo);
            TimeUnit.MILLISECONDS.sleep(1800L);
            //回调
            log.info("[{}]回调",vo.getBankOrderNo());
//            aliPayService.accountBookQuery(vo.getPayerAgreementNo(), vo.getSubMerchantNo());
            TimeUnit.MILLISECONDS.sleep(500L);
            //退款
            log.info("[{}]退款",vo.getBankOrderNo());
//            aliPayService.accountBookQuery(vo.getPayeeAgreementNo(), vo.getParentMerchantNo());
            TimeUnit.MILLISECONDS.sleep(1000L);
            //写库
            log.info("[{}]写库",vo.getBankOrderNo());
            TimeUnit.MILLISECONDS.sleep(20L);
        } catch (Exception e) {
            log.error("模拟错误",e);
        }

    }


    /**
     * 创客汇，发放到个人银行卡
     * @param payReqVo
     * @return
     */
    public PayRespVo ckhPayToPersonalBankCard(PayReqVo payReqVo) {
        PayRespVo payRespVo = new PayRespVo();
        try {
            ParticipantsInfo payer = new ParticipantsInfo();
            payer.setAgreementNo(payReqVo.getPayerAgreementNo());
            payer.setIdentity(payReqVo.getSubMerchantNo());

            ParticipantsInfo payee = new ParticipantsInfo();
            payee.setIdentity(payReqVo.getReceiveAccountNo());
            payee.setName(payReqVo.getReceiveName());

            String s = aliPayService.uniBankCard(payReqVo.getBankOrderNo(),payReqVo.getReceiveAmount(),payReqVo.getRemitRemark(),payee,payer);
            JSONObject json = JSON.parseObject(s).getJSONObject("alipay_fund_trans_uni_transfer_response");
            if (json.getInteger("code").intValue() != AlipayErrorCode.SUCCESS.getCode()) {
                if (json.getInteger("code").intValue() == AlipayErrorCode.UNKOWN_ERROR.getCode()) {
                    log.error("支付宝接口调用失败，返回参数：[{}]",s);
                    throw new RuntimeException("支付宝接口调用失败");
                } else {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(json.getString("sub_msg"));
                }
            } else {
                payRespVo.setBankTrxNo(json.getString("order_id"));
                payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
                payRespVo.setBizCode("");
                payRespVo.setBankOrderNo(payReqVo.getBankOrderNo());
                payRespVo.setBizMsg("");
                return payRespVo;
            }
        } catch (AlipayApiException e) {
            if (e.getMessage().contains("SocketTimeoutException")){
                log.error("网络异常，订单号：[{}]，等待重试",payReqVo.getBankOrderNo());
                //为了避免重复支付直接抛runtimeException，让系统自动重试
                throw new RuntimeException(e.getCause().getMessage());
            }else{
                log.error("发放到个人异常，报错信息：[{}]",e.getCause().getMessage());
                throw CommonExceptions.BIZ_INVALID.newWith("记账本发放到个人支付宝接口调用失败",e);
            }
        }
    }

    /**
     * 创客汇，发放到个人
     * @param payReqVo
     */
    public PayRespVo ckhPayToPersonalAliPay(PayReqVo payReqVo){
        PayRespVo payRespVo = new PayRespVo();
        ParticipantsInfo payee = new ParticipantsInfo();
        payee.setIdentity(payReqVo.getReceiveAccountNo());
        payee.setName(payReqVo.getReceiveName());

        ParticipantsInfo payer = new ParticipantsInfo();
        payer.setAgreementNo(payReqVo.getPayerAgreementNo());
        payer.setAccountbookSceneCode("SATF_FUND_BOOK");
        payer.setIdentity(payReqVo.getSubMerchantNo());
        payer.setEmployerNo(payReqVo.getEmployerNo());

        try {
            String s = aliPayService.transferToAlipay(payReqVo.getBankOrderNo(), payReqVo.getReceiveAmount(), payReqVo.getRemitRemark(),payReqVo.getRealPayerName(),payee, payer);
            JSONObject json = JSON.parseObject(s).getJSONObject("alipay_fund_trans_uni_transfer_response");
            if (json.getInteger("code").intValue() != AlipayErrorCode.SUCCESS.getCode()) {
                if (json.getInteger("code").intValue() == AlipayErrorCode.UNKOWN_ERROR.getCode()) {
                    log.error("支付宝接口调用失败，返回参数：[{}]",s);
                    throw new RuntimeException("支付宝接口调用失败");
                } else {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(json.getString("sub_msg"));
                }
            } else {
                payRespVo.setBankTrxNo(json.getString("order_id"));
                payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
                payRespVo.setBizCode("");
                payRespVo.setBankOrderNo(payReqVo.getBankOrderNo());
                payRespVo.setBizMsg("");
                return payRespVo;
            }
        } catch (AlipayApiException e) {
            if (e.getMessage().contains("SocketTimeoutException")){
                log.error("网络异常，订单号：[{}]，等待重试",payReqVo.getBankOrderNo());
                //为了避免重复支付直接抛runtimeException，让系统自动重试
                throw new RuntimeException(e.getCause().getMessage());
            }else{
                log.error("发放到个人异常，报错信息：[{}]",e.getCause().getMessage());
                throw CommonExceptions.BIZ_INVALID.newWith("记账本发放到个人支付宝接口调用失败",e);
            }
        }
    }

    public PayRespVo ckhPayFee(PayReqVo payReqVo) {
        PayRespVo payRespVo = new PayRespVo();
        ParticipantsInfo payee = new ParticipantsInfo();
        payee.setAgreementNo(payReqVo.getPayeeAgreementNo());
        payee.setIdentity(payReqVo.getParentMerchantNo());

        ParticipantsInfo payer = new ParticipantsInfo();
        payer.setAgreementNo(payReqVo.getPayerAgreementNo());
        payer.setIdentity(payReqVo.getSubMerchantNo());

        try {
            String s = aliPayService.uniTransfer(payReqVo.getBankOrderNo(),payReqVo.getReceiveAmount(), payReqVo.getRemitRemark(), payee, payer,"账单支付");
            JSONObject json =  JSON.parseObject(s).getJSONObject("alipay_fund_trans_uni_transfer_response");
            if (json.getInteger("code").intValue() != AlipayErrorCode.SUCCESS.getCode()) {
                if (json.getInteger("code").intValue() == AlipayErrorCode.UNKOWN_ERROR.getCode()) {
                    log.error("支付宝接口调用失败，返回参数：[{}]",s);
                    throw new RuntimeException("支付宝接口调用失败");
                } else {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(json.getString("sub_msg"));
                }
            } else {
                payRespVo.setBankTrxNo(json.getString("order_id"));
                payRespVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
                payRespVo.setBizCode("");
                payRespVo.setBankOrderNo(payReqVo.getBankOrderNo());
                payRespVo.setBizMsg("");
                return payRespVo;
            }
        } catch (AlipayApiException e) {
            if (e.getMessage().contains("SocketTimeoutException")){
                log.error("网络异常，订单号：[{}]，等待重试",payReqVo.getBankOrderNo());
                //为了避免重复支付直接抛runtimeException，让系统自动重试
                throw new RuntimeException(e.getCause().getMessage());
            }else{
                log.error("发放到个人异常，报错信息：[{}]",e.getCause().getMessage());
                throw CommonExceptions.BIZ_INVALID.newWith("记账本发放到个人支付宝接口调用失败",e);
            }
        }
    }

    /**
     * 创客汇，查询订单状态
     * @param remitPlatTrxNo
     * @return
     */
    public PayRespVo queryAlipayCKHOrder(String remitPlatTrxNo) {
        PayRespVo payRespVo = new PayRespVo();
        payRespVo.setBankOrderNo(remitPlatTrxNo);
        /**
         * 创客汇打款只有一步
         */
        try {
            JSONObject json = getAlipayOrder(remitPlatTrxNo
                    ,AlipayConstant.ALIPAY_BIZ_ENTRUST_TRANSFER,AlipayConstant.ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD);
            int code = json.getIntValue("code");
            if (code == AlipayErrorCode.SUCCESS.getCode()) {
                payRespVo.setBankTrxNo(json.getString("order_id"));
                payRespVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
            }else if (code == AlipayErrorCode.BIZ_ERROR.getCode() && StringUtils.equals(json.getString("sub_code"),"ORDER_NOT_EXIST")) {
                payRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                payRespVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()));
            }else{
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单查询失败，支付宝接口查询异常");
            }
            return payRespVo;
        } catch (AlipayApiException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询订单状态失败");
        }
    }

}
