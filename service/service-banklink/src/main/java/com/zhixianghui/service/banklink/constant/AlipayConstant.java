package com.zhixianghui.service.banklink.constant;

/**
 * <AUTHOR>
 * @description 支付宝常量
 * @date 2021-04-12 11:41
 **/
public class AlipayConstant {

    /**
     * 退款重试次数前缀
     */
    public static final String REFUND_TIMES_PRE_KEY = "REFUND_TIMES_PRE_KEY:";

    /**
     * 退款最大重试次数
     */
    public static final Integer MAX_REFUND_TIMES = 5;

    /**
     * 退款锁前缀
     */
    public static final String ALIPAY_REFUND_AUTH_KEY = "ALIPAY_REFUND_AUTH_KEY";

    /**
     * 记账本之间拨款场景码
     */
    public static final String ALIPAY_BIZ_ENTRUST_ALLOCATION = "ENTRUST_ALLOCATION";

    /**
     * 记账本拨款到支付宝个人账号场景码
     */
    public static final String ALIPAY_BIZ_ENTRUST_TRANSFER = "ENTRUST_TRANSFER";

    /**
     * 记账本之间拨款、拨款到个人账号产品码
     */
    public static  final String ALIPAY_PRODUCT_SINGLE_TRANSFER_NO_PWD = "SINGLE_TRANSFER_NO_PWD";

    public static final Integer MAX_MAINSTAY_BALANCE_NOT_ENOUGH_TIMES = 10;

    public static final String MAINSTAY_BALANCE_NOT_ENOUGH_KEY = "MAINSTAY_BALANCE_NOT_ENOUGH:";
}
