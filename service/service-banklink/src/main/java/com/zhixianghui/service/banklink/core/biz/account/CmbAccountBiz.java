package com.zhixianghui.service.banklink.core.biz.account;

import cn.hutool.core.util.StrUtil;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.service.banklink.request.account.CmbAccountQueryBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 招行账户服务
 */
@Service
@Slf4j
public class CmbAccountBiz {

    public static final String MAINSTAY_ACCOUNT_BOOK_DEFAULT = "********";

    @Autowired
    private CmbAccountQueryBiz cmbAccountQueryBiz;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private RedisLock redisLock;


    /**
     * 查询余额
     * @param accountNo
     * @param accountBookNo
     */
    public String getBalance(String accountNo, String accountBookNo) {

        final String balance = cmbAccountQueryBiz.queryBalance(accountNo, accountBookNo);
        if (balance != null && Float.parseFloat(balance) >= 0) {
            return balance;
        } else {
            return "0";
        }
    }


    /**
     * 记账子单元转账
     * @param accountNo
     * @param payerAccountBookNo
     * @param payeeAccountBookNo
     * @param amount
     */
//    public void transfer(String accountNo, String payerAccountBookNo, String payeeAccountBookNo, String amount) {
//
//        //只是为了刷新在没有本地账本时候刷新本地缓存
//        this.getBalance(accountNo, payeeAccountBookNo);
//        final String payerBalance = this.getBalance(accountNo, payerAccountBookNo);
//        if (payerBalance == null || new BigDecimal(payerBalance).compareTo(new BigDecimal(amount)) < 0) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("转账余额不足");
//        }
//        redisClient.eval(transRedisScript(),
//                Lists.newArrayList(accountBookRedisKey(accountNo, payerAccountBookNo),
//                        accountBookRedisKey(accountNo, payeeAccountBookNo)), amount);
//    }

    public void pay(String accountNo, String accountBookNo, String amount) {
        final String balance = this.getBalance(accountNo, accountBookNo);
        if (balance == null || new BigDecimal(balance).compareTo(new BigDecimal(amount)) < 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("{}::{}", ApiExceptions.API_BIZ_FAIL.getApiErrorCode(), "余额不足"));
        }

//        final String redisKey = accountBookRedisKey(accountNo, accountBookNo);
//        redisClient.eval(payRedisScript(), Lists.newArrayList(redisKey), amount);
    }

//    public void recharge(String accountNo,String accountBookNo,String amount) {
//        this.getBalance(accountNo, accountBookNo);

//        final String redisKey = accountBookRedisKey(accountNo, accountBookNo);
//        redisClient.eval(rechargeRedisScript(), Lists.newArrayList(redisKey), amount);
//    }

//    private String accountBookRedisKey(String accountNo, String accountBookNo) {
//        return StrUtil.format("{}{}:{}", RedisKeysConstant.CMB_BALANCE_CACHE_KEY_PREFIX, accountNo, accountBookNo);
//    }

    private String transRedisScript() {
        String script = "local payerBalance = redis.call('get',KEYS[1])\n" +
                "local payeeBalance = redis.call('get',KEYS[2])\n" +
                "local amt = ARGV[1]\n" +
                "if payerBalance then\n" +
                "    payerBalance = payerBalance\n" +
                "else\n" +
                "    payerBalance = 0\n" +
                "end\n" +
                "\n" +
                "if payeeBalance then\n" +
                "    payeeBalance = payeeBalance\n" +
                "else\n" +
                "    payeeBalance = 0\n" +
                "end\n" +
                "if amt then\n" +
                "    amt = amt\n" +
                "else\n" +
                "    error('参数错误')\n" +
                "end\n" +
                "\n" +
                "if tonumber(amt)>tonumber(payerBalance) then\n" +
                "    error('余额不足')\n" +
                "else\n" +
                "    redis.call('set',KEYS[1],tostring(math.floor((payerBalance - amt)*100+0.5)/100))\n" +
                "    redis.call('set',KEYS[2],tostring(math.floor((payeeBalance + amt)*100+0.5)/100))\n" +
                "end";

        return script;
    }

    private String payRedisScript() {
        return "local payerBalance = redis.call('get',KEYS[1])\n" +
                "local amt = ARGV[1]\n" +
                "if payerBalance then\n" +
                "    payerBalance = payerBalance\n" +
                "else\n" +
                "    payerBalance = 0\n" +
                "end\n" +
                "\n" +
                "if amt then\n" +
                "    amt = amt\n" +
                "else\n" +
                "    error('参数错误')\n" +
                "end\n" +
                "\n" +
                "if tonumber(amt)>tonumber(payerBalance) then\n" +
                "    error('余额不足')\n" +
                "else\n" +
                "    redis.call('set',KEYS[1],tostring(math.floor((payerBalance - amt)*100+0.5)/100))\n" +
                "end";
    }

    private String rechargeRedisScript() {
        return "local payeeBalance = redis.call('get',KEYS[1])\n" +
                "local amt = ARGV[1]\n" +
                "\n" +
                "if payeeBalance then\n" +
                "    payeeBalance = payeeBalance\n" +
                "else\n" +
                "    error(\"余额不足\")\n" +
                "end\n" +
                "if amt then\n" +
                "    amt = amt\n" +
                "else\n" +
                "    error('参数错误')\n" +
                "end\n" +
                "redis.call('set',KEYS[1],tostring(math.floor((payeeBalance + amt)*100+0.5)/100))";
    }
}
