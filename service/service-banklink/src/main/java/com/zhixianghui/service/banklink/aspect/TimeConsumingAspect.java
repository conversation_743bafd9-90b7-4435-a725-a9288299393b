package com.zhixianghui.service.banklink.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName TimeConsumingAspect
 * @Description TODO
 * @Date 2021/6/2 9:34
 */
@Component
@Aspect
@Slf4j
public class TimeConsumingAspect {


    @Pointcut("@annotation(com.zhixianghui.service.banklink.annotation.TimeConsuming)")
    public void aroundAdvisePointCur(){

    }

    @Around("aroundAdvisePointCur()")
    public Object startAroundAdvise(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        Long currentTime = System.currentTimeMillis();

        Object result = null;
        Throwable throwable = null;
        try{
            result = proceedingJoinPoint.proceed();
        }catch (Throwable e){
            throwable = e;
        }
        Long completeTime = System.currentTimeMillis();
        String className = proceedingJoinPoint.getTarget().getClass().getName();
        String method = signature.getMethod().getName();
        log.info("方法执行完成，所属位置：[{}]，方法名：[{}]，共计耗时：{}ms",className,method,completeTime - currentTime);
        if (throwable != null){
            throw throwable;
        }
        return result;
    }
}
