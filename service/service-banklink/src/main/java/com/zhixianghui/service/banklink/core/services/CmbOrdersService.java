package com.zhixianghui.service.banklink.core.services;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.facade.banklink.entity.CmbOrders;
import com.zhixianghui.service.banklink.core.mapper.CmbOrdersMapper;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class CmbOrdersService extends ServiceImpl<CmbOrdersMapper, CmbOrders> {


    public int updateBatch(List<CmbOrders> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<CmbOrders> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<CmbOrders> list) {
        return baseMapper.batchInsert(list);
    }

    public int insertOrUpdate(CmbOrders record) {
        return baseMapper.insertOrUpdate(record);
    }

    public int insertOrUpdateSelective(CmbOrders record) {
        return baseMapper.insertOrUpdateSelective(record);
    }
}
