package com.zhixianghui.service.banklink.core.dao;

import com.zhixianghui.common.service.dao.MyBatisDao;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/10/27
 **/
@Repository
public class KeyPairRecordDao extends MyBatisDao<KeyPairRecord, Long> {

    public KeyPairRecord getByChannelNoAndChannelMchNo(String channelNo, String channelMchNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("channelNo", channelNo);
        paramMap.put("channelMchNo", channelMchNo);
        return this.getOne(paramMap);
    }
}
