package com.zhixianghui.service.banklink.core.biz.RobotBiz;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.InputStream;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName RobotBiz
 * @Description TODO
 * @Date 2021/6/4 18:10
 */
@Slf4j
@Service
public class RobotBiz {

    @Reference
    private NotifyFacade notifyFacade;

    @Autowired
    private RedisClient redisClient;

    @Autowired
    private FastdfsClient fastdfsClient;

    private final static String REDIS_PREKEY = "MARKDOWN:PREKEY:";

    private final static String REDIS_RETRY_KEY = "MARKDOWN:RETRY:KEY:";

    private final static int MAX_RETRY_TIMES = 2;

    @Value("${wx.risk.robot}")
    private String riskUrl;

    @Value("${wx.granting.robot}")
    private String grantingUrl;

    @Value("${wx.default.robot}")
    private String defaultUrl;

    @Value("${wx.newMerchant.robot}")
    private String newMerchantUrl;

    @Value("${wx.recharge.robot}")
    private String rechargeUrl;

    @Value("${wx.notice.robot}")
    private String noticeUrl;

    @Value("${wx.income.robot}")
    private String incomeUrl;

    @Value("${wx.saleprod.robot}")
    private String saleprodUrl;

    @Value("${wx.recmmendMerchant.robot}")
    private String merchantRecommendUrl;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    public boolean pushMarkDownAsync(MarkDownMsg markDownMsg) {
        return notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_WORK_ROBOT, NotifyTypeEnum.WX_WORK_ROBOT.getValue(),
                MessageMsgDest.TAG_WX_WORK_ROBOT, JsonUtil.toString(markDownMsg));
    }

    public boolean pushMarkDownSync(MarkDownMsg markDownMsg) {
        final String runtimeEnv = dataDictionaryFacade.getSystemConfig("RUNTIME_ENV");
        if (StringUtils.equals(runtimeEnv, "TEST")) {
            markDownMsg.setContent(markDownMsg.getContent()+"\n > 环境：测试环境");
        } else if (StringUtils.equals(runtimeEnv, "PROD")) {
            markDownMsg.setContent(markDownMsg.getContent()+"\n > 环境：生产环境");
        }else {
            markDownMsg.setContent(markDownMsg.getContent()+"\n > 环境：开发环境");
        }

        if (StringUtil.isEmpty(markDownMsg.getUnikey())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("唯一键不能为空");
        }

        if (markDownMsg.getRobotType() == RobotTypeEnum.RISK_ROBOT.getType()) {
            return sendRequest(markDownMsg, riskUrl);
        } else if (markDownMsg.getRobotType() == RobotTypeEnum.GRANTING_ROBOT.getType()) {
            return sendRequest(markDownMsg, grantingUrl);
        } else if (markDownMsg.getRobotType() == RobotTypeEnum.MERCHANT_ADD_ROBOT.getType()) {
            return sendRequest(markDownMsg, newMerchantUrl);
        } else if (markDownMsg.getRobotType() == RobotTypeEnum.RECHARGE_ROBOT.getType()) {
            return sendRequest(markDownMsg, rechargeUrl);
        } else if (markDownMsg.getRobotType() == RobotTypeEnum.NOTICE_ROBOT.getType()){
            return sendRequest(markDownMsg,noticeUrl);
        }else if (markDownMsg.getRobotType() == RobotTypeEnum.WX_INCOME_ROBOT.getType()){
            return sendRequest(markDownMsg,incomeUrl);
        }
        else if (markDownMsg.getRobotType() == RobotTypeEnum.WX_SALE_PROD_ROBOT.getType()){
            return sendRequest(markDownMsg,saleprodUrl);
        }
        else if (markDownMsg.getRobotType()  == RobotTypeEnum.MERCHANT_RECOMMEND_ROBOT.getType()){
            return sendRequest(markDownMsg,merchantRecommendUrl);
        }
        else {
            return sendRequest(markDownMsg, defaultUrl);
        }
    }

    public boolean pushFileMsgSync(String mediaId,RobotTypeEnum robotType) {

        String robotUrl = null;
        if (robotType.getType() == RobotTypeEnum.RISK_ROBOT.getType()) {
            robotUrl= riskUrl;
        } else if (robotType.getType()  == RobotTypeEnum.GRANTING_ROBOT.getType()) {
            robotUrl=  grantingUrl;
        } else if (robotType.getType()  == RobotTypeEnum.MERCHANT_ADD_ROBOT.getType()) {
            robotUrl= newMerchantUrl;
        } else if (robotType.getType()  == RobotTypeEnum.RECHARGE_ROBOT.getType()) {
            robotUrl= rechargeUrl;
        } else if (robotType.getType()  == RobotTypeEnum.NOTICE_ROBOT.getType()){
            robotUrl = noticeUrl;
        }else if (robotType.getType()  == RobotTypeEnum.WX_INCOME_ROBOT.getType()){
            robotUrl = incomeUrl;
        }
        else if (robotType.getType()  == RobotTypeEnum.WX_SALE_PROD_ROBOT.getType()){
            robotUrl = saleprodUrl;
        }
        else if (robotType.getType()  == RobotTypeEnum.MERCHANT_RECOMMEND_ROBOT.getType()){
            robotUrl = merchantRecommendUrl;
        }
        else {
            robotUrl = defaultUrl;
        }
        String content = String.format(("{\"msgtype\":\"file\",\"file\":{\"media_id\":\"%s\"}}"), mediaId);
        WebClient webClient = WebClient.create(robotUrl);
        Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromObject(content)).retrieve().bodyToMono(JSONObject.class);
        JSONObject jsonObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        int msg = jsonObject.getInteger("errcode");
        if (msg == 0) {
            //设置发送限制
//            redisClient.set(REDIS_PREKEY + markDownMsg.getUnikey(),markDownMsg.getUnikey(),5 * 60);
            return true;
        } else if (msg == 45009) {
            //超出频率限制，两分钟后再试
//            robotRetry(markDownMsg);
            return true;
        } else {
            return false;
        }
    }

    public static boolean sendRequest(MarkDownMsg markDownMsg, String url) {
        log.info("推送到企业微信机器人，推送内容：{}", markDownMsg.getContent());
        //发送时间限制，限制5分钟发送一次
//        String hasbeenSend = redisClient.get(REDIS_PREKEY + markDownMsg.getUnikey());
//        if (!StringUtil.isEmpty(hasbeenSend)){
//            return false;
//        }
        WebClient webClient = WebClient.create(url);
        Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromObject(markDownMsg.getRobotJson())).retrieve().bodyToMono(JSONObject.class);
        JSONObject jsonObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        int msg = jsonObject.getInteger("errcode");
        if (msg == 0) {
            //设置发送限制
//            redisClient.set(REDIS_PREKEY + markDownMsg.getUnikey(),markDownMsg.getUnikey(),5 * 60);
            return true;
        } else if (msg == 45009) {
            //超出频率限制，两分钟后再试
//            robotRetry(markDownMsg);
            return true;
        } else {
            return false;
        }
    }

    private void robotRetry(MarkDownMsg markDownMsg) {
        //重试两次，避免发送太多无用消息
        if (getAcceptTimes(markDownMsg.getUnikey()) > MAX_RETRY_TIMES) {
            log.info("重试两次机器人仍处于繁忙，默认丢弃");
        } else {
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_WORK_ROBOT, NotifyTypeEnum.WX_WORK_ROBOT.getValue(),
                    MessageMsgDest.TAG_WX_WORK_ROBOT, JsonUtil.toString(markDownMsg), MsgDelayLevelEnum.M_2.getValue());
        }
    }

    private Long getAcceptTimes(String unikey) {
        Long acceptTime = 0L;
        try {
            acceptTime = redisClient.incr(REDIS_RETRY_KEY + unikey);
            redisClient.expire(REDIS_RETRY_KEY + unikey, 60 * 60);
        } catch (Exception e) {
            log.error("redis获取重试次数异常 忽略", unikey, e);
        }
        return acceptTime;
    }

    public String uploadFile(RobotTypeEnum robotType,String fileName,String fileId) {
        String msgUrl = null;
        if (robotType.getType() == RobotTypeEnum.WX_SALE_PROD_ROBOT.getType()){
            msgUrl = saleprodUrl;
        }
        else {
            msgUrl = defaultUrl;
        }
        String key = msgUrl.substring(msgUrl.indexOf("key="));

        InputStream inputStream = fastdfsClient.downloadFile(fileId);
        byte[] bytes = IoUtil.readBytes(inputStream);
        File file = FileUtil.writeBytes(bytes, new File(FileUtil.getTmpDir(), fileName));

        String url = StrUtil.format("https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?type=file&{}",key);
        HttpResponse httpResponse = HttpUtil.createPost(url)
                .contentLength(bytes.length)
                .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
                .form("filelength",bytes.length)
                .form("filename",fileName)
                .form("name","media")
                .form("file", file)
                .execute();
        if (httpResponse.isOk()) {
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(httpResponse.body());
            log.info(httpResponse.body());
            int msg = jsonObject.getInt("errcode");
            if (msg == 0) {
                return jsonObject.getStr("media_id");
            } else {
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("上传文件到微信异常");
            }

        }else {
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("上传文件到微信异常");
        }

    }
}
