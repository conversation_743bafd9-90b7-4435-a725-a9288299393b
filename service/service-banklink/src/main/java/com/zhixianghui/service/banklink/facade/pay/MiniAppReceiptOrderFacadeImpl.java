package com.zhixianghui.service.banklink.facade.pay;

import cn.hutool.extra.spring.SpringUtil;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.banklink.service.pay.MiniAppReceiptOrderFacade;
import com.zhixianghui.service.banklink.core.biz.receiptOrder.AbstractReceiptOrderHandler;
import com.zhixianghui.service.banklink.core.biz.receiptOrder.ReceiptOrderBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MiniAppReceiptOrderFacadeImpl
 * @Description TODO
 * @Date 2023/6/29 11:15
 */
@Service
public class MiniAppReceiptOrderFacadeImpl implements MiniAppReceiptOrderFacade {

    @Autowired
    private ReceiptOrderBiz receiptOrderBiz;

    @Override
    public Map<String, Object> applyOrder(String channelNo, String remitPlatTrxNo, String employerNo, String mainstayNo, Integer channelType) {
        return receiptOrderBiz.applyOrder(channelNo,remitPlatTrxNo,employerNo,mainstayNo,channelType);
    }


}
