package com.zhixianghui.service.banklink.facade.pay;

import com.zhixianghui.facade.banklink.service.pay.MiniPayFacade;
import com.zhixianghui.facade.banklink.vo.pay.MiniAppPayVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.service.banklink.request.wxpay.WxPayService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @ClassName MiniPayFacadeImpl
 * @Description TODO
 * @Date 2022/12/28 10:01
 */
@Service(retries = -1,timeout = 20000)
public class MiniPayFacadeImpl implements MiniPayFacade {

    @Autowired
    private WxPayService wxPayService;

    @Override
    public WxResVo jsapiPay(MiniAppPayVo miniAppPayVo) {
        return wxPayService.jsapiPay(miniAppPayVo);
    }

    @Override
    public WxResVo closeOrder(String orderNo) {
        return wxPayService.jsapiClose(orderNo);
    }

    @Override
    public WxResVo jsapiQuery(String orderNo) {
        return wxPayService.jsapiQuery(orderNo);
    }

    @Override
    public WxResVo jsapiRefundQuery(String refundOrderNo) {
        return wxPayService.jsapiRefundQuery(refundOrderNo);
    }

    @Override
    public WxResVo refund(String originOrderNo, String refundOrderNo, Long amount, String refundReason) {
        return wxPayService.jsapiRefund(originOrderNo,refundOrderNo,amount,refundReason);
    }
}
