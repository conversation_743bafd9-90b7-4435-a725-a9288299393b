package com.zhixianghui.service.banklink.core.biz.account;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.banklink.vo.account.SubMchNoQueryDto;
import com.zhixianghui.facade.banklink.vo.yishui.YiResponse;
import com.zhixianghui.facade.banklink.vo.yishui.YishuiMyAccountVo;
import com.zhixianghui.facade.banklink.vo.yishui.req.RequestVo;
import com.zhixianghui.facade.trade.dto.CmbAccountQueryDTO;
import com.zhixianghui.facade.trade.entity.AcMerchantBalance;
import com.zhixianghui.facade.trade.entity.CmbMerchantBalance;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;
import com.zhixianghui.facade.trade.service.AcMerchantBalanceFacade;
import com.zhixianghui.facade.trade.service.CmbMerchantBalanceFacade;
import com.zhixianghui.facade.trade.service.WxMerchantBalanceFacade;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.service.banklink.core.biz.YishuiBiz;
import com.zhixianghui.service.banklink.request.account.AlipayAccountQueryBiz;
import com.zhixianghui.service.banklink.request.account.JoinpayAccountQueryBiz;
import com.zhixianghui.service.banklink.request.wxpay.WxPayService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-02 10:35
 **/
@Service
public class AccountQueryBiz {
    @Autowired
    private JoinpayAccountQueryBiz joinpayAccountQueryBiz;
    @Autowired
    private AlipayAccountQueryBiz alipayAccountQueryBiz;
    @Autowired
    private YishuiBiz yishuiBiz;
    @Reference
    private WxMerchantBalanceFacade wxMerchantBalanceFacade;
    @Reference
    private AcMerchantBalanceFacade acMerchantBalanceFacade;
    @Reference
    private CmbMerchantBalanceFacade cmbMerchantBalanceFacade;
    @Autowired
    private WxPayService payService;
    @Autowired
    private CmbAccountBiz cmbAccountBiz;

    /***
     * 余额查询，返回（元）
     * @param amountQueryDto
     * @return
     */
    public String getAmount(AmountQueryDto amountQueryDto) {
        if (amountQueryDto.getChannelType() == ChannelTypeEnum.BANK.getValue() && amountQueryDto.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())) {
            return joinpayAccountQueryBiz.getAmount(amountQueryDto);
        } else if (amountQueryDto.getChannelNo() != null && StringUtils.equals(amountQueryDto.getChannelNo().trim(), ChannelNoEnum.ALIPAY.name()) &&
                (amountQueryDto.getChannelType() == ChannelTypeEnum.BANK.getValue() || amountQueryDto.getChannelType() == ChannelTypeEnum.ALIPAY.getValue())) {
            if (StringUtils.isBlank(amountQueryDto.getAgreementNo()) || StringUtils.isBlank(amountQueryDto.getChannelMchNo())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付宝账户协议号或者记账本id为空");
            }
            //支付宝有银行卡发放和支付宝发放两种类型
            /**
             * 支付宝余额查询
             */
            String balanceValue = alipayAccountQueryBiz.queryBalance(amountQueryDto.getAgreementNo(), amountQueryDto.getSubMerchantNo());
            return balanceValue;
        } else if (amountQueryDto.getChannelNo() != null && StringUtils.equals(amountQueryDto.getChannelNo().trim(), ChannelNoEnum.YISHUI.name())) {

            YiResponse<YishuiMyAccountVo> yishuiMyAccountVoYiResponse = yishuiBiz.myAccount(new RequestVo().setUser_name(amountQueryDto.getEmployerNo()).setEnterprise_sn(amountQueryDto.getSubMerchantNo()));
            if (yishuiMyAccountVoYiResponse.getCode() != 200) {
                return "0";
            }
            YishuiMyAccountVo data = yishuiMyAccountVoYiResponse.getData();
            String balance = data.getBalance();
            return balance;
        } else if (checkChannel(amountQueryDto, ChannelTypeEnum.WENXIN, ChannelNoEnum.WXPAY)) {
            WxMerchantBalance wxMerchantBalance = new WxMerchantBalance();
            wxMerchantBalance.setMchNo(amountQueryDto.getEmployerNo());
            wxMerchantBalance.setMainstayNo(amountQueryDto.getMainstayNo());
            Long amount = wxMerchantBalanceFacade.getAmount(wxMerchantBalance);
            return AmountUtil.changeToYuan(amount).toString();
        } else if (StringUtils.equals(amountQueryDto.getChannelNo(), ChannelNoEnum.CMB.name())) {
            //cmbAccountBiz.getBalance(amountQueryDto.getChannelMchNo(), amountQueryDto.getSubMerchantNo());
            CmbAccountQueryDTO queryDTO = new CmbAccountQueryDTO();
            queryDTO.setMchNo(amountQueryDto.getEmployerNo());
            queryDTO.setMainstayNo(amountQueryDto.getMainstayNo());
            queryDTO.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
            BigDecimal amount = cmbMerchantBalanceFacade.getAmount(queryDTO);
            return amount.toString();
        } else if (StringUtils.equals(amountQueryDto.getChannelNo(), ChannelNoEnum.JOINPAY_JXH.name())) {
            AcMerchantBalance model = new AcMerchantBalance();
            model.setPayChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
            model.setMainstayNo(amountQueryDto.getMainstayNo());
            model.setMchNo(amountQueryDto.getEmployerNo());
            return Convert.toStr(AmountUtil.changeToYuan(acMerchantBalanceFacade.getAmount(model)));
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("getAmount:找不到对应通道,请检查编号是否一致,通道是否存在。PayChannelNo:" + amountQueryDto.getChannelNo() + ",ChannelType:" + amountQueryDto.getChannelType());
        }
    }

    public boolean checkChannel(AmountQueryDto amountQueryDto, ChannelTypeEnum channelTypeEnum, ChannelNoEnum channelNoEnum) {
        String channelNo = amountQueryDto.getChannelNo();
        Integer channelType = amountQueryDto.getChannelType();
        return channelType == channelTypeEnum.getValue() && !StringUtils.isEmpty(channelNo) && StringUtils.equals(channelNo.trim(), channelNoEnum.name());
    }

    public Map<String, String> getMainstayAmount(MainstayAmountQueryDto mainstayAmountQueryDto) {
        if (mainstayAmountQueryDto.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())) {
            AcMerchantBalance model = new AcMerchantBalance();
            model.setPayChannelNo(ChannelNoEnum.JOINPAY.name());
            model.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            model.setMainstayNo(mainstayAmountQueryDto.getMainstayNo());
            Long amount = acMerchantBalanceFacade.getAmount(model);
            Map<String, String> balanceData = new HashMap<>();
            balanceData.put("useAbleSettAmount", AmountUtil.changeToYuanInString(Long.toString(amount)));
            balanceData.put("frozenAmount", "0");
            return balanceData;
//            return joinpayAccountQueryBiz.getMainstayAmount(mainstayAmountQueryDto);
        } else if (mainstayAmountQueryDto.getChannelNo() != null &&
                StringUtils.equals(mainstayAmountQueryDto.getChannelNo().trim(), ChannelNoEnum.ALIPAY.name())) {
            if (StringUtils.isBlank(mainstayAmountQueryDto.getAgreementNo()) || StringUtils.isBlank(mainstayAmountQueryDto.getChannelMchNo())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付宝账户协议号或者记账本id为空");
            }
            /**
             * 支付宝余额查询
             */
            String balanceValue = alipayAccountQueryBiz.queryBalance(mainstayAmountQueryDto.getAgreementNo(), mainstayAmountQueryDto.getChannelMchNo());

            Map<String, String> balanceData = new HashMap<>();
            balanceData.put("useAbleSettAmount", balanceValue);
            balanceData.put("frozenAmount", "0");

            return balanceData;
        } else if (mainstayAmountQueryDto.getChannelNo().equals(ChannelNoEnum.YISHUI.name())) {
            Map<String, String> balanceData = new HashMap<>();
            balanceData.put("useAbleSettAmount", "0");
            balanceData.put("frozenAmount", "0");
            return balanceData;
        } else if (mainstayAmountQueryDto.getChannelNo().equals(ChannelNoEnum.WXPAY.name())) {
            //查询供应商余额
            WxMerchantBalance wxMerchantBalance = new WxMerchantBalance();
            wxMerchantBalance.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            wxMerchantBalance.setMainstayNo(mainstayAmountQueryDto.getMainstayNo());
            wxMerchantBalance = wxMerchantBalanceFacade.getOne(wxMerchantBalance);
            String amount = "0";
            if (wxMerchantBalance != null) {
                amount = Long.toString(wxMerchantBalance.getTotalAmount() - wxMerchantBalance.getFreezeAmount());
            }
            Map<String, String> balanceData = new HashMap<>();
            balanceData.put("useAbleSettAmount", AmountUtil.changeToYuanInString(amount));
            balanceData.put("frozenAmount", "0");
            return balanceData;
        } else if (StringUtils.equals(mainstayAmountQueryDto.getChannelNo(), ChannelNoEnum.CMB.name())) {
            //String balance = cmbAccountBiz.getBalance(mainstayAmountQueryDto.getChannelMchNo(), "********");
            CmbAccountQueryDTO queryDTO = new CmbAccountQueryDTO();
            queryDTO.setMainstayNo(mainstayAmountQueryDto.getMainstayNo());
            queryDTO.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            CmbMerchantBalance cmbMerchantBalance = cmbMerchantBalanceFacade.getCmbMerchantBalance(queryDTO);
            Map<String, String> balanceData = new HashMap<>();
            if(ObjectUtil.isNotEmpty(cmbMerchantBalance)) {
                balanceData.put("useAbleSettAmount", cmbMerchantBalance.getTotalAmount().toString());
                balanceData.put("frozenAmount", cmbMerchantBalance.getFreezeAmount().toString());
            }else {
                balanceData.put("useAbleSettAmount", "0");
                balanceData.put("frozenAmount", "0");
            }
            return balanceData;
        } else if (StringUtils.equals(mainstayAmountQueryDto.getChannelNo(), ChannelNoEnum.JOINPAY_JXH.name())) {
            AcMerchantBalance model = new AcMerchantBalance();
            model.setPayChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
            model.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
            model.setMainstayNo(mainstayAmountQueryDto.getMainstayNo());
            Long amount = acMerchantBalanceFacade.getAmount(model);
            Map<String, String> balanceData = new HashMap<>();
            balanceData.put("useAbleSettAmount", AmountUtil.changeToYuanInString(Long.toString(amount)));
            balanceData.put("frozenAmount", "0");
            return balanceData;
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("getMainstayAmount:找不到对应通道,请检查编号是否一致,通道是否存在。PayChannelNo:" + mainstayAmountQueryDto.getChannelNo() + ",ChannelType:" + mainstayAmountQueryDto.getChannelType());
        }
    }

    public String getSubMchNo(SubMchNoQueryDto subMchNoQueryDto) {
        if (subMchNoQueryDto.getChannelType() == ChannelTypeEnum.BANK.getValue() && subMchNoQueryDto.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())) {
            return joinpayAccountQueryBiz.getAccount(subMchNoQueryDto);
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应通道,请检查编号是否一致,通道是否存在。PayChannelNo:" + subMchNoQueryDto.getChannelNo() + ",ChannelType:" + subMchNoQueryDto.getChannelType());
        }
    }
}
