package com.zhixianghui.service.banklink.core.biz.notify;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.ApiRespCodeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.http.InvalidMediaTypeException;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.UnsupportedMediaTypeException;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-22 09:47
 **/
@Slf4j
@Service
public class MerchantNotifyBiz {

    public void sendMerchantNotify(MerchantNotifyParam merchantNotifyParam) {
        // 请求
        log.info("回调商户接口 ==>通知商户信息:{}",  JsonUtil.toString(merchantNotifyParam));
        JSONObject respObject;
        try {
            WebClient webClient = WebClient.create(merchantNotifyParam.getNotifyUrl());
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(merchantNotifyParam.getNotifyContent()))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        } catch (UnsupportedMediaTypeException e) {
            log.error("接收到不支持的数据格式",e);
            return;
        } catch (InvalidMediaTypeException e){

            log.info("Webflux客户端请求出现InvalidMediaTypeException，切换到okHttp客户端重试:[{}]",merchantNotifyParam.getNotifyContent().getMchNo());
            // 换个Http客户端进行重试
            OkHttpClient okHttpClient = new OkHttpClient.Builder().build();
            okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(okhttp3.MediaType.parse("application/json; charset=utf-8"), JSONUtil.toJsonStr(merchantNotifyParam.getNotifyContent()));
            Request request = new Request.Builder().post(requestBody).url(merchantNotifyParam.getNotifyUrl()).build();
            try {
                Response execute = okHttpClient.newCall(request).execute();

                if (execute.body() == null) {
                    log.error("回调商户接口 ==>商户返回确认信息为null,通知信息:{} ", merchantNotifyParam);
                    throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("商户返回确认信息为null");
                }

                String respStr = execute.body().string();
                if (JSONUtil.isJsonObj(respStr)) {
                    respObject = JSONObject.parseObject(respStr);
                }else {
                    log.error("回调商户接口 ==>商户返回确认信息为格式不正确,通知信息:{} ", respStr);
                    throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("商户返回确认信息为格式不正确");
                }
            } catch (Exception e1) {
                log.error("回调商户接口 ==>通知商户信息异常,通知信息:{} ", merchantNotifyParam, e1);
                throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("回调商户接口异常");
            }
        }
        catch (Exception e) {
            log.error("回调商户接口 ==>通知商户信息异常,通知信息:{} ", merchantNotifyParam, e);
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("回调商户接口异常");
        }
        if(respObject == null){
            log.error("回调商户接口 ==>商户返回确认信息为null,通知信息:{} ", merchantNotifyParam);
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("商户返回确认信息为null");
        }
        String responseCode = respObject.getString("resp_code");
        if(responseCode == null){
            log.error("回调商户接口 ==>商户返回确认信息responseCode为null,通知信息:{} ", merchantNotifyParam);
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("商户返回确认信息,responseCode为空");
        }
        if(!responseCode.equals(ApiRespCodeEnum.SUCCESS.getCode())){
            log.error("回调商户接口 ==>商户返回确认信息responseCode不为success,通知信息:{} ", merchantNotifyParam);
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg("商户返回确认信息,responseCode为空");
        }
        log.info("回调商户接口成功，商户号：{},data:{}", merchantNotifyParam.getNotifyContent().getMchNo(),merchantNotifyParam.getNotifyContent().getData());
    }
}
