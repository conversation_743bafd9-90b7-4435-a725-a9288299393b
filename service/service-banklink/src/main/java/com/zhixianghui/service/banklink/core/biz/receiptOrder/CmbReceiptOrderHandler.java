package com.zhixianghui.service.banklink.core.biz.receiptOrder;

import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.service.banklink.request.cmb.CmbApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName CmbReceiptOrderHandler
 * @Description TODO
 * @Date 2023/6/29 11:48
 */
@Slf4j
@Component
public class CmbReceiptOrderHandler extends AbstractReceiptOrderHandler{

    @Autowired
    private CmbApiService cmbApiService;
    @Reference
    private RecordItemFacade recordItemFacade;

    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    @Override
    public Map<String, Object> applyOrder(String remitPlatTrxNo, String employerNo, String mainstayNo, Integer channelType) {
        RecordItem recordItem = recordItemFacade.getByRemitPlatTrxNo(remitPlatTrxNo);
        //获取当前时间
        Date now = new Date();
        Date date = DateUtil.addDay(DateUtil.getDayStart(recordItem.getCreateTime()),1);
        if ((now.getTime() - date.getTime()) < 12 * 60 * 60){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该电子回单需要订单完成后次日12点才可下载");
        }
        return new HashMap<String,Object>(){{put("remitPlatTrxNo",remitPlatTrxNo);}};
    }

    @Override
    protected File download(Map<String, Object> paramMap) {
        String remitPlatTrxNo = (String) paramMap.get("remitPlatTrxNo");
        String platTrxNo = (String) paramMap.get("platTrxNo");
        String receiveName = (String) paramMap.get("receiveName");
        String mainstayNo = (String) paramMap.get("mainstayNo");
        File file = FileUtils.createFile(TEMP_PATH + "电子凭证" + remitPlatTrxNo + "-" + (new Date()).getTime() + FILE_SUFFIX);
        //连接sftp服务器
        ChannelSftp channelSftp = SftpUtil.connect(host, port, userName, pwd);
        //sftp服务器上对应文件地址
        String unzipPath = System.getProperty("user.dir") + File.separator + mainstayNo + File.separator + "unzip_files";
        String sftpFileName = CertificateUtil.getPayFileName(receiveName,mainstayNo,platTrxNo);
        SftpUtil.download(unzipPath + File.separator + sftpFileName,file,channelSftp);
        return file;
    }
}
