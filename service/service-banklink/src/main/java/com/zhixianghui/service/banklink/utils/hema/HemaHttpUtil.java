package com.zhixianghui.service.banklink.utils.hema;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.RSAUtil;
import okhttp3.*;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @ClassName HemaHttpUtil
 * @Description TODO
 * @Date 2023/9/27 17:05
 */
public class HemaHttpUtil {

    private static OkHttpClient okHttpClient = null;

    private static OkHttpClient getOkHttpClient() {
        if (okHttpClient == null) {
            okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(60, TimeUnit.SECONDS)
                    .writeTimeout(60, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .build();
        }
        return okHttpClient;
    }

    public static String post(String url,String encryetParam,String appKey,String timestamp,String sign) throws IOException {
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), encryetParam);
        Request request = new Request.Builder()
                .addHeader("AppKey",appKey)
                .addHeader("Timestamp",timestamp)
                .addHeader("Version","1.0")
                .addHeader("Sign",sign)
                .url(url)
                .post(body).build();
        Response response = getOkHttpClient().newCall(request).execute();
        if (response.isSuccessful()) {
            ResponseBody responseBody = response.body();
            if (responseBody == null){
                return null;
            }
            return responseBody.string();
        }else{
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("请求代征平台失败:" + response.message());
        }
    }
}
