package com.zhixianghui.service.banklink.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.enums.HemaPayCodeEnum;
import com.zhixianghui.facade.banklink.vo.hema.PayCallbackReqVo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.service.banklink.request.hema.HemaService;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName HemaListener
 * @Description TODO
 * @Date 2023/10/9 11:26
 */
@Slf4j
@Component
public class HemaListener {

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_PAY_CALLBACK_TO_HEMA,selectorExpression = MessageMsgDest.TAG_PAY_CALLBACK_TO_HEMA,consumeThreadMax = 10,consumerGroup = "payCallbackHemaGroup")
    public class PayCallBackListener extends BaseRocketMQListener<String>{

        @Autowired
        private HemaService hemaService;

        @Override
        public void validateJsonParam(String jsonParam) {

        }

        @Override
        public void consumeMessage(String jsonParam) {
            OrderItem orderItem = JsonUtil.toBean(jsonParam,OrderItem.class);
            PayCallbackReqVo payCallbackReqVo = new PayCallbackReqVo();
            payCallbackReqVo.setSynchronizationTime(DateUtil.formatDateTime(orderItem.getCompleteTime()));
            payCallbackReqVo.setReason(orderItem.getErrorDesc());
            payCallbackReqVo.setThirdOrderId(orderItem.getPlatTrxNo());
            if (orderItem.getOrderItemStatus().intValue() == OrderItemStatusEnum.GRANT_SUCCESS.getValue()){
                payCallbackReqVo.setItemStatus(HemaPayCodeEnum.PAY_SUCCESS.getValue());
            }else{
                payCallbackReqVo.setItemStatus(HemaPayCodeEnum.PAY_FAIL.getValue());
            }
            try {
                hemaService.payCallback(payCallbackReqVo);
            } catch (Exception e) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("河马结算回调接口调用失败");
            }
        }
    }
}
