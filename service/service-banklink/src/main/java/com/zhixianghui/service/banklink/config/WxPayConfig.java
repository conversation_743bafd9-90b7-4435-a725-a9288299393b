package com.zhixianghui.service.banklink.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName WxPayConfig
 * @Description TODO
 * @Date 2021/12/1 10:10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wxpay")
public class WxPayConfig {

    //微信商户号
    private String merchantId;

    //api证书序列号
    private String serialNo;

    //微信商户api私钥
    private String privateKey;

    //证书路径
    private String certificatePath;

    //apiv3密钥
    private String apiv3Key;

    //服务商appId
    private String appId;

    //支付url
    private String payUrl;

    //实时查询余额url
    private String fundBalanceUrl;

    //订单详情url
    private String orderDetailUrl;

    //来账通知url
    private String incomeRecordsUrl;

    //电子回单受理
    private String receiptAcceptUrl;

    //提现受理
    private String withdrawUrl;

    //提现订单状态查询
    private String withdrawStatusUrl;

    //jsapi回调地址
    private String jsapiCallbackUrl;

    //sp_appid
    private String spAppId;

}
