package com.zhixianghui.service.banklink.facade.ocr;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.vo.ocr.BusinessLicenseOctVo;
import com.zhixianghui.facade.banklink.vo.ocr.IdcardOcrVo;
import com.zhixianghui.facade.banklink.service.ocr.OcrFacade;
import com.zhixianghui.facade.banklink.vo.ocr.OcrRequestVo;
import com.zhixianghui.service.banklink.core.biz.ocrBiz.OcrBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class OcrFacadeImpl implements OcrFacade {


    @Autowired
    private OcrBiz ocrBiz;

    @Override
    public IdcardOcrVo idCardFontOcr(OcrRequestVo ocrRequestVo) throws BizException {
        return ocrBiz.idCardFontOcr(ocrRequestVo);
    }

    @Override
    public BusinessLicenseOctVo businessLicenseOcr(OcrRequestVo ocrRequestVo) throws BizException{
        return ocrBiz.businessLicenseOcr(ocrRequestVo);
    }

}
