package com.zhixianghui.service.banklink.listener.tasks;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.service.banklink.request.pay.CmbPayBiz;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_ORDER_TO_LOCAL_TASK,consumeThreadMax = 1,consumerGroup = "cmbOrder2LocalConsumer")
public class SyncCmbAccountBookOrderToLocal extends TaskRocketMQListener<JSONObject>{
    @Autowired
    private CmbPayBiz cmbPayBiz;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Override
    public void runTask(JSONObject jsonParam) {
        List<MainstayChannelRelation> mainstayChannelRelations = mainstayChannelRelationFacade.listBy(
                MapUtil.builder(new HashMap<String, Object>()).put("payChannelNo", "CMB")
                        .put("status", 100)
                        .build()
        );
        for (MainstayChannelRelation mainstayChannelRelation : mainstayChannelRelations) {
            if (StringUtils.isNotBlank(mainstayChannelRelation.getChannelMchNo())) {
                cmbPayBiz.syncOrder2Local(mainstayChannelRelation.getChannelMchNo());
            }
        }
    }
}
