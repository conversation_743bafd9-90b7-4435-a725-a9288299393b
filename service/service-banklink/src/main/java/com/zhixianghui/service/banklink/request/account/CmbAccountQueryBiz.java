package com.zhixianghui.service.banklink.request.account;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.service.banklink.request.cmb.CmbApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CmbAccountQueryBiz {

    final private CmbApiService cmbApiService;

    /**
     * 查询记账本余额
     * @return
     */
    public String queryBalance(String accountNo,String acctBookNo) {
        try {
            JSONObject data = cmbApiService.queryAccountBookBalance(accountNo, acctBookNo);

            if(data!=null&&data.get("ntdmabalz")!=null&&data.getJSONArray("ntdmabalz")!=null
            &&!data.getJSONArray("ntdmabalz").isEmpty()){
                String balance = data.getJSONArray("ntdmabalz").getJSONObject(0).getString("actbal");
                return balance;
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("记账子单元余额查询出错", e);
            throw CommonExceptions.UNEXPECT_ERROR.newWith("记账子单元余额查询出错", e);
        }
    }

}
