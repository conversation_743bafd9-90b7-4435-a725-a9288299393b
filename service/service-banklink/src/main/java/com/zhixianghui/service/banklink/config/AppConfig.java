package com.zhixianghui.service.banklink.config;

import com.alipay.api.CertAlipayRequest;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AppConfig {

    @Autowired
    private AlipayConfig alipayConfig;

    @Bean
    public CertAlipayRequest certAlipayRequest() {
        CertAlipayRequest certAlipayRequest = new CertAlipayRequest();
        certAlipayRequest.setPrivateKey(alipayConfig.getMerchantPrivateKey());
        certAlipayRequest.setAppId(alipayConfig.getAppId());
        certAlipayRequest.setSignType(alipayConfig.getSignType());
        certAlipayRequest.setServerUrl(alipayConfig.getServerUrl());
        certAlipayRequest.setAlipayPublicCertPath(alipayConfig.getAlipayCertPath());
        certAlipayRequest.setCertPath(alipayConfig.getAppCertPath());
        certAlipayRequest.setCharset(alipayConfig.getCharSet());
        certAlipayRequest.setRootCertPath(alipayConfig.getAlipayRootCertPath());

        return certAlipayRequest;
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return mybatisPlusInterceptor;
    }
}
