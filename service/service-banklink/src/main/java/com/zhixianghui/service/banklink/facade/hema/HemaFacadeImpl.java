package com.zhixianghui.service.banklink.facade.hema;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.service.hema.HemaFacade;
import com.zhixianghui.facade.banklink.vo.hema.PaymentReqVo;
import com.zhixianghui.service.banklink.request.hema.HemaService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @ClassName HemaFacadeImpl
 * @Description TODO
 * @Date 2023/10/8 16:35
 */
@Service(retries = -1,timeout = 15000)
public class HemaFacadeImpl implements HemaFacade {

    @Autowired
    private HemaService hemaService;

    @Override
    public void payment(PaymentReqVo paymentReqVo) throws BizException,Exception {
        hemaService.payment(paymentReqVo);
    }
}
