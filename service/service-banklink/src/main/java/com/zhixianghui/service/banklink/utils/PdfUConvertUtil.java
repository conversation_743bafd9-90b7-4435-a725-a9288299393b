package com.zhixianghui.service.banklink.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * <AUTHOR>
 * @ClassName PdfUtil
 * @Description TODO
 * @Date 2022/8/24 14:56
 */
public class PdfUConvertUtil {

    public static byte[] convertToPNG(InputStream inputStream) throws IOException {
        PDDocument document = null;
        ByteArrayOutputStream os = null;
        try {
            os = new ByteArrayOutputStream();
            document = PDDocument.load(inputStream);
            PDFRenderer renderer = new PDFRenderer(document);
            //固定只取第一页
            BufferedImage bufferedImage = renderer.renderImageWithDPI(0,160);
            ImageIO.write(bufferedImage,"png",os);
            return os.toByteArray();
        }finally {
            if (document != null){
                document.close();
            }
            if (os != null){
                os.close();
            }
        }


    }

    public static void main(String[] args) throws IOException {

        byte[] bytes = PdfUConvertUtil.convertToPNG(new FileInputStream("C:\\Users\\<USER>\\Desktop\\baixin.pdf"));
        new FileOutputStream("C:\\Users\\<USER>\\Desktop\\baixin.png").write(bytes);
    }
}
