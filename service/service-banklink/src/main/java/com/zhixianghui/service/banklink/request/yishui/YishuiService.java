package com.zhixianghui.service.banklink.request.yishui;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.vo.yishui.*;
import com.zhixianghui.facade.banklink.vo.yishui.req.*;
import com.zhixianghui.service.banklink.annotation.CurrentYiToken;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Service
@Log4j2
public class YishuiService {

    public static final String AES_KEY = "1f277aea889019ae";

    private static final String LOGIN_URL = "sdk/login";
    private static final String CARDBIN_URL = "cardBin";
    public static final String MY_ACCOUNT_URL = "Enterprise/myAccount";
    public static final String MY_ACCT_DETAILS_URL = "Enterprise/accountOrderList";
    public static final String ADD_EMPLOYEE_URL = "Enterprise/addEmployee";
    public static final String ADD_EMPLOYEE_ALIPAY_URL = "Enterprise/addEmployeeAlipay";
    public static final String CONTRACT_INFO_URL = "Enterprise/contractInfo";
    public static final String CONTRACT_LIST_URL = "Enterprise/contractList";
    public static final String CONTRACT_SAVE_URL = "Enterprise/contractSave";
    public static final String CONTRACT_DO_URL = "Enterprise/contractDo";
    public static final String FASTISSUING_URL = "Enterprise/fastIssuing";
    public static final String CHANGE_ORDER_STATUS_URL = "Enterprise/changeOrderStatus";
    public static final String GET_ORDERLIST_URL = "Enterprise/getOrderList";
    public static final String GET_ORDEREXT_URL = "Enterprise/getOrderExt";
    public static final String GET_ORDER_EXTDETAIL_URL = "Enterprise/getOrderExtDetail";
    public static final String FIND_ORDER_FROM_REQUESTNO_URL = "Enterprise/findOrderFromRequestNo";
    public static final String SAVE_ENTERPRISE_DETAIL_URL = "Facilitator/saveEnterpriseDetail";
    public static final String AGENT_LOGIN_URL = "login";
    public static final String SAVE_ENTERPRISE_DRAFT_RATE_URL = "Facilitator/saveEnterpriseDraftRate";
    public static final String UPLOAD_URL = "upload";
    public static final String REGIONS = "regions";
    public static final String GET_ENTERPRISE_DETAIL = "Facilitator/getEnterpriseDetail";
    public static final String ENTERPRISE_LIST = "Facilitator/enterpriseList";
    public static final String ADD_BANK = "Enterprise/addBank";


    @Value("${yishui.api.url}")
    private String apiUrl;
    @Value("${yishui.qudao.username}")
    private String qudaoUsername;
    @Value("${yishui.qudao.password}")
    private String qudaoPassword;
    @Value("${yishui.qudao.vcode}")
    private String vCode;
    @Value("${yishui.notify.url}")
    private String notifyUrl;

    public String getUploadUrl() {
        return apiUrl + UPLOAD_URL;
    }

    /**
     * 渠道API登录
     * @return
     */
    public String agentLogin() {

        Map<String, Object> data = new HashMap<>();
        Map<String, Object> param = new HashMap<>();

        data.put("user_name", qudaoUsername);
        data.put("password", qudaoPassword);
        data.put("v_code", vCode);
        param.put("data", data);
        log.info("易税渠道商登录入参:{}", JSONObject.toJSONString(param));

        JSONObject response = this.request(param, AGENT_LOGIN_URL);
        log.info("易税渠道商登录请求返回:{}",response.toJSONString());

        if (response.getIntValue("code") != 200) {
            log.error("易税渠道商登录失败:{}", JSON.toJSONString(response));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("易税渠道商登录失败");
        }

        return response.getJSONObject("data").getString("token");
    }

    /**
     * 新增与编辑企业基本资料
     */
    public YiResponse<Map<String, String>> saveEnterpriseDetail(String token,SaveEnterpriseDetailVo saveVo) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", token);
        param.put("data", saveVo);
        log.info("新增与编辑企业基本资料-入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, SAVE_ENTERPRISE_DETAIL_URL);
        if (response.getIntValue("code") != 200) {
            log.error("新增与编辑企业基本资料-失败:{}", JSON.toJSONString(response));
        }

//        String data = response.getString("data");
//        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
//        String s = Base64.decodeStr(decryptECB);
//        log.info("新增与编辑企业基本资料解密后数据:{}", s);

        YiResponse<Map<String, String>> yiResponse = response.toJavaObject(YiResponse.class);
        return yiResponse;
    }



    public String regions(String token) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", token);

        JSONObject response = this.request(param, REGIONS);
        if (response.getIntValue("code") != 200) {
            log.error("渠道查询地区ID-失败:{}", JSON.toJSONString(response));
        }
        return response.toJSONString();
    }

    public YiResponse<Map<String, Object>> getEnterpriseDetail(String token, String enterpriseId) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", token);

        Map<String, Object> data = Maps.newHashMap();
        data.put("enterprise_id", enterpriseId);
        param.put("data", data);

        JSONObject response = this.request(param, GET_ENTERPRISE_DETAIL);
        if (response.getIntValue("code") != 200) {
            log.error("查看企业基本信息-失败:{}",JSON.toJSONString(response));
        }

        YiResponse<Map<String, Object>> yiResponse = response.toJavaObject(YiResponse.class);

        return yiResponse;
    }

    public YiResponse<Map<String, Object>> getEnterpriseDetailByName(String token, String enterpriseName) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", token);

        Map<String, Object> data = Maps.newHashMap();
        data.put("enterprise_name", enterpriseName);
        data.put("pagination", MapUtil.builder(new HashMap<String, Object>())
                .put("current_page", 1).put("page_size", 1).build());
        param.put("data", data);

        JSONObject response = this.request(param, ENTERPRISE_LIST);
        if (response.getIntValue("code") != 200) {
            log.error("查看企业基本信息-失败:{}",JSON.toJSONString(response));
        }
        JSONArray dataArray = response.getJSONObject("data").getJSONArray("list");

        YiResponse<Map<String, Object>> yiResponse = response.toJavaObject(YiResponse.class);
        if (dataArray != null && dataArray.size() == 1) {
            yiResponse.setData(dataArray.getJSONObject(0));
        }

        return yiResponse;
    }

    /**
     * 编辑企业草稿费率接口
     * @param token
     * @param draftRate
     * @return
     */
    public YiResponse saveEnterpriseDraftRate(String token, SaveEnterpriseDraftRate draftRate) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", token);
        param.put("data", JSONUtil.toJsonStr(draftRate));
        log.info("编辑企业草稿费率接口-入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, SAVE_ENTERPRISE_DRAFT_RATE_URL);
        if (response.getIntValue("code") != 200) {
            log.error("编辑企业草稿费率接口-失败:{}", JSON.toJSONString(response));
        }

//        String data = response.getString("data");
//        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
//        String s = Base64.decodeStr(decryptECB);
//        log.info("编辑企业草稿费率接口-解密后数据:{}", s);

        YiResponse<Map<String, String>> yiResponse = response.toJavaObject(YiResponse.class);
        return yiResponse;
    }


    /**
     * 企业API登录
     * @param username 账号
     * @param password 密码
     * @param enterprise_sn 企业编码
     * @return
     */
    public String login(String username, String password, String enterprise_sn) {

        String random = RandomUtil.get16LenStr();
        Map<String, Object> param = new HashMap<>();
        param.put("user_name", username);
        param.put("password", password);
        param.put("enterprise_sn", enterprise_sn);
        param.put("random", random);
        param.put("aeskey", AES_KEY);

        String s = "user_name=" + username + "&password=" + password + "&enterprise_sn=" + enterprise_sn + "&random=" + random + "&aeskey=" + AES_KEY;
        String sign = DigestUtil.md5Hex(s);
        param.put("sign", sign);

        log.info("易税登录入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, LOGIN_URL);

        if (response.getIntValue("code") != 200) {
            log.error("易税登录失败:{}", JSON.toJSONString(response));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("易税登录失败:{}",response.getString("msg")));
        }

        return response.getString("token");
    }

    /**
     * 银行卡卡BIN 参数传银行卡号
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiCardBinVo> carbin(RequestVo<String> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", MapUtil.builder(new HashMap<>()).put("card_number", requestVo.getParam()).build());
        log.info("易税卡bin查询入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, CARDBIN_URL);
        if (response.getIntValue("code") != 200) {
            log.error("易税卡bin查询失败:{}", JSON.toJSONString(response));
        }

        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("cardbin解密后数据:{}", s);

        YiResponse<YishuiCardBinVo> yiResponse = response.toJavaObject(YiResponse.class);
        yiResponse.setData(JSONUtil.toBean(s, YishuiCardBinVo.class));
        return yiResponse;
    }

    /**
     * 账户信息  入参不需要传param
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiMyAccountVo> myAccount(RequestVo requestVo) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());

        JSONObject response = this.request(param, MY_ACCOUNT_URL);
        if (response.getIntValue("code") != 200) {
            log.error("账户信息查询失败:{}", JSON.toJSONString(response));
        }

        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("账户信息解密后数据:{}", s);

        YiResponse<YishuiMyAccountVo> yiResponse = response.toJavaObject(YiResponse.class);
        yiResponse.setData(JSONUtil.toBean(s, YishuiMyAccountVo.class));
        return yiResponse;
    }

    /**
     * 账户明细
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiMyAcctDetailVo> myAcctDetail(RequestVo<MyAcctDetailVo> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        Map<String, Object> dataParam = Maps.newHashMap();
        Map<String, Object> pageParam = Maps.newHashMap();

        param.put("token", requestVo.getToken());
        dataParam.put("client_sn", requestVo.getParam().getClient_sn());
        dataParam.put("client_name", requestVo.getParam().getClient_name());
        dataParam.put("enterprise_order_ext_sn", requestVo.getParam().getEnterprise_order_ext_sn());
        dataParam.put("enterprise_order_sn", requestVo.getParam().getEnterprise_order_sn());
        dataParam.put("op_type", requestVo.getParam().getOp_type());

        pageParam.put("page_start", requestVo.getParam().getPagination().getPage_start());
        pageParam.put("page_size", requestVo.getParam().getPagination().getPage_size());

        dataParam.put("pagination", pageParam);
        param.put("data", dataParam);

        JSONObject response = this.request(param, MY_ACCT_DETAILS_URL);
        if (response.getIntValue("code") != 200) {
            log.error("账户详情查询失败:{}", JSON.toJSONString(response));
        }

        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("账户详情解密后数据:{}", s);

        YiResponse<YishuiMyAcctDetailVo> yiResponse = response.toJavaObject(YiResponse.class);
        yiResponse.setData(JSONUtil.toBean(s, YishuiMyAcctDetailVo.class));
        return yiResponse;
    }

    /**
     * 支付宝人员新增
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiAddEmpVo> addEmployeeAlipay(RequestVo<AddEmpVo> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", BeanUtil.toMap(requestVo.getParam()));
        log.info("增加人员(支付宝)入参:{}",JSONUtil.toJsonStr(param));
        JSONObject response = this.request(param, ADD_EMPLOYEE_ALIPAY_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("人员新增(支付宝)解密后数据:{}", s);
        YiResponse<YishuiAddEmpVo> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("人员新增(支付宝)失败:{}", JSON.toJSONString(response));
        }else {
            yiResponse.setData(JSONUtil.toBean(s, YishuiAddEmpVo.class));
        }
        return yiResponse;
    }

    /**
     * 人员新增
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiAddEmpVo> addEmployee(RequestVo<AddEmpVo> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", BeanUtil.toMap(requestVo.getParam()));

        log.info("增加人员入参:{}",JSONUtil.toJsonStr(param));

        JSONObject response = this.request(param, ADD_EMPLOYEE_URL);
        YiResponse<YishuiAddEmpVo> yiResponse = response.toJavaObject(YiResponse.class);

        if (response.getIntValue("code") != 200) {
            log.error("人员新增失败:{}", JSON.toJSONString(response));
        }else {
            String data = response.getString("data");
            String decryptECB = AESUtil.decryptECB(data, AES_KEY);
            String s = Base64.decodeStr(decryptECB);
            log.info("人员新增解密后数据:{}", s);
            yiResponse.setData(JSONUtil.toBean(s, YishuiAddEmpVo.class));
        }

        return yiResponse;
    }

    /**
     * 修改与完善人员信息
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiContractListVo> contractList(RequestVo<ContractListQueryVo> requestVo) {
        ContractListQueryVo vo = requestVo.getParam();
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", MapUtil.builder(new HashMap())
                .put("keyword", vo.getKeyword())
                .put("professional_sn", vo.getProfessional_sn())
                .put("is_auth", vo.getIs_auth())
                .put("is_contract", vo.getIs_contract())
                .put("pagination", MapUtil.builder(new HashMap<>())
                        .put("page_start", vo.getPagination().getPage_start())
                        .put("page_size", vo.getPagination().getPage_size()).build()).build());

        log.info("人员列表入参:{}", JSONUtil.toJsonStr(param));

        JSONObject response = this.request(param, CONTRACT_LIST_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("人员列表解密后数据:{}", s);

        YiResponse<YishuiContractListVo> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("人员列表查询失败:{}", JSON.toJSONString(response));
            if (StringUtils.equals(response.getString("msg"), "暂无数据")) {
                yiResponse.setData(null);
            }
        } else {
            yiResponse.setData(JSONUtil.toBean(s, YishuiContractListVo.class));
        }
        return yiResponse;
    }

    /**
     * 修改与完善人员信息
     * @return
     */
    @CurrentYiToken
    public YiResponse contractSave(RequestVo<ContractSaveVo> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", BeanUtil.toMap(requestVo.getParam()));

        JSONObject response = this.request(param, CONTRACT_SAVE_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("修改与完善人员信息解密后数据:{}", s);

        YiResponse yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("修改与完善人员信息失败:{}", JSON.toJSONString(response));
        }
        return yiResponse;
    }

    /**
     * 入参：enterprise_professional_facilitator_id 人员签约ID
     * 人员签约与解约: 如果之前签约状态为1（已签约），调用后变为2（已解约），再调用变为1（已签约）
     * @param
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiContractDoVo> contractDo(RequestVo<String> requestVo) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", MapUtil.builder(new HashMap<>()).put("enterprise_professional_facilitator_id", requestVo.getParam()).build());
        log.info("人员签约与解约入参:{}", JSONObject.toJSONString(param));

        JSONObject response = this.request(param, CONTRACT_DO_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("人员签约与解约解密后数据:{}", s);

        YiResponse<YishuiContractDoVo> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("人员签约与解约失败:{}", JSON.toJSONString(response));
        }else {
            yiResponse.setData(JSONUtil.toBean(s, YishuiContractDoVo.class));
        }

        return yiResponse;

    }

    /**
     * API批量付款
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiFastIssuing> fastIssuing(RequestVo<FastIssuingVo> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        requestVo.getParam().setNotify_url(notifyUrl);
        param.put("data", BeanUtil.toMap(requestVo.getParam()));
        log.info("API批量付款入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, FASTISSUING_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("API批量付款解密后数据:{}", s);

        YiResponse<YishuiFastIssuing> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("API批量付款失败:{}", JSON.toJSONString(response));
        }else {
            yiResponse.setData(JSONUtil.toBean(s, YishuiFastIssuing.class));
        }
        return yiResponse;
    }

    /**
     *  人员详情 入参传 enterprise_professional_facilitator_id 签约ID
     * @param
     * @return
     */
    @CurrentYiToken
    public YiResponse<YishuiContractInfoVo> contractInfo(RequestVo<String> requestVo) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", MapUtil.builder(new HashMap<>()).put("enterprise_professional_facilitator_id", requestVo.getParam()).build());
        log.info("人员详情查询入参:{}",JSONUtil.toJsonStr(param));
        JSONObject response = this.request(param, CONTRACT_INFO_URL);

        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("人员详情解密后数据:{}", s);
        YiResponse<YishuiContractInfoVo> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("人员详情查询失败:{}", JSON.toJSONString(response));
        }else {
            yiResponse.setData(JSONUtil.toBean(s, YishuiContractInfoVo.class));
        }
        return yiResponse;
    }

    /**
     * 批次审核
     * @return
     */
    @CurrentYiToken
    public YiResponse changeOrderStatus(RequestVo<ChangeOrderReqVo> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", MapUtil.builder(new HashMap<>())
                .put("enterprise_order_id", requestVo.getParam().getEnterprise_order_id())
                .put("status", requestVo.getParam().getStatus())
                .put("remarks", requestVo.getParam().getRemarks()).build());
        log.info("批次审核入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, CHANGE_ORDER_STATUS_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("批次审核解密后数据:{}", s);

        YiResponse yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("批次审核失败:{}", JSON.toJSONString(response));
        }

        return yiResponse;
    }

    /**
     * 批次列表
     * @return
     */
    @CurrentYiToken
    public YiResponse<Map<String, Object>> getOrderList(RequestVo<OrderListVo> requestVo) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", BeanUtil.toMap(requestVo.getParam()));
        log.info("批次列表入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, GET_ORDERLIST_URL);

        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("批次列表解密后数据:{}", s);

        YiResponse<Map<String, Object>> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("批次列表失败:{}", JSON.toJSONString(response));
        }else {
            yiResponse.setData(JSONUtil.toBean(s, Map.class));
        }

        return yiResponse;
    }

    /**
     * 批次订单列表
     * @return
     */
    @CurrentYiToken
    public YiResponse<Map<String, Object>> getOrderExt(RequestVo<OrderExtReqVo> requestVo) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", MapUtil.builder(new HashMap<>())
                .put("enterprise_order_id", requestVo.getParam().getEnterprise_order_id())
                .put("pagination",MapUtil.builder(new HashMap<>())
                        .put("page_size", requestVo.getParam().getPagination().getPage_size())
                        .put("page_start", requestVo.getParam().getPagination().getPage_start())
                        .build())
                .build());

        log.info("批次订单列表入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, GET_ORDEREXT_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("批次订单列表解密后数据:{}", s);

        YiResponse<Map<String, Object>> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("批次订单列表失败:{}", JSON.toJSONString(response));
        } else {
            yiResponse.setData(JSONUtil.toBean(s, Map.class));
        }

        return yiResponse;

    }

    /**
     *订单详情 入参 enterprise_order_ext_id 订单ID
     * @param
     * @return
     */
    @CurrentYiToken
    public YiResponse getOrderExtDetail(RequestVo<String> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", MapUtil.builder(new HashMap<>()).put("enterprise_order_ext_id", requestVo.getParam()).build());
        log.info("订单详情入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, GET_ORDER_EXTDETAIL_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("订单详情解密后数据:{}", s);

        YiResponse<Map<String, Object>> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("订单详情失败:{}", JSON.toJSONString(response));
        } else {
            yiResponse.setData(JSONUtil.toBean(s, Map.class));
        }

        return yiResponse;
    }

    /**
     * 第三方单号查询订单信息 入参 request_no 第三方单号
     * @param
     * @return
     */
    @CurrentYiToken
    public YiResponse<Map<String, Object>> findOrderFromRequestNo(RequestVo<String> requestVo) {

        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", MapUtil.builder(new HashMap<>()).put("request_no", requestVo.getParam()).build());
        log.info("第三方单号查询订单信息入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, FIND_ORDER_FROM_REQUESTNO_URL);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("第三方单号查询订单信息解密后数据:{}", s);

        YiResponse<Map<String, Object>> yiResponse = response.toJavaObject(YiResponse.class);
        if (response.getIntValue("code") != 200) {
            log.error("第三方单号查询订单信息失败:{}", JSON.toJSONString(response));
        } else {
            yiResponse.setData(JSONUtil.toBean(s, Map.class));
        }
        return yiResponse;
    }

    @CurrentYiToken
    public YiResponse<String> addBank(RequestVo<AddBankVo> requestVo){
        Map<String, Object> param = Maps.newHashMap();
        param.put("token", requestVo.getToken());
        param.put("data", BeanUtil.toMap(requestVo.getParam()));

        log.info("签约管理/新增收款账户入参:{}", JSON.toJSONString(param));

        JSONObject response = this.request(param, ADD_BANK);
        YiResponse<String> yiResponse = response.toJavaObject(YiResponse.class);
        String data = response.getString("data");
        String decryptECB = AESUtil.decryptECB(data, AES_KEY);
        String s = Base64.decodeStr(decryptECB);
        log.info("签约管理/新增收款账户-解密后数据:{}", s);
        if (response.getIntValue("code") != 200) {
            log.error("签约管理/新增收款账户异常:{}", JSON.toJSONString(response));
        }else {
            yiResponse.setData(s);
        }

        return yiResponse;
    }

    private JSONObject request(Map<String, Object> param, String url) {
        String params = JSONUtil.toJsonStr(param);
        log.info("易税接口入参:{}",JSONUtil.toJsonPrettyStr(param));
        WebClient webClient = WebClient.create(apiUrl + url);
        Mono<String> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromObject(params)).retrieve().bodyToMono(String.class);
        String response = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        log.info("易税接口返回：{}", response);

        return JSON.parseObject(response);
    }

}
