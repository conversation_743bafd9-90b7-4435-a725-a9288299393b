package com.zhixianghui.service.banklink.facade.wx;

import com.zhixianghui.facade.banklink.service.wx.WxPayFacade;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.service.banklink.request.wxpay.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @ClassName WxPayFacadeImpl
 * @Description TODO
 * @Date 2021/12/8 15:42
 */
@Slf4j
@Service
public class WxPayFacadeImpl implements WxPayFacade {

    @Autowired
    private WxPayService wxPayService;

    @Override
    public WxResVo getIncomeRecord(String subMchId,String date,Integer offset) throws Exception {
        return wxPayService.incomeRecords(subMchId,date,offset);
    }

    @Override
    public WxResVo getOrder(String orderNo) throws Exception {
        return wxPayService.getOrder(orderNo);
    }

    @Override
    public WxResVo receiptAccept(String recordItemNo) {
        return wxPayService.receiptAccept(recordItemNo);
    }

    @Override
    public WxResVo queryReceiptBill(String recordItemNo) {
        return wxPayService.queryReceiptBill(recordItemNo);
    }

    @Override
    public byte[] downloadReceiptBill(String downloadUrl,String recordItemNo) {
        return wxPayService.downloadReceiptBill(downloadUrl,recordItemNo);
    }

    @Override
    public WxResVo withdraw(String subMchId, String withdrawNo, Integer amount, String remark) {
        return wxPayService.withDraw(subMchId,withdrawNo,amount,remark);
    }

    @Override
    public WxResVo getWithdrawStatus(String subMchId, String withdrawNo) {
        return wxPayService.getWithDrawStatus(subMchId,withdrawNo);
    }
}
