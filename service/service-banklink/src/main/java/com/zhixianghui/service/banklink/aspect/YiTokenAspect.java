package com.zhixianghui.service.banklink.aspect;

import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.vo.yishui.req.RequestVo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.service.banklink.core.biz.YishuiBiz;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 设置秘钥对
 * <AUTHOR>
 */
@Component
@Aspect
public class YiTokenAspect {
    @Autowired
    private YishuiBiz yishuiBiz;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private EmployerMainstayRelationFacade relationFacade;

    @Pointcut("@annotation(com.zhixianghui.service.banklink.annotation.CurrentYiToken)")
    public void setYiTokenPointcut(){

    }

    @Before("setYiTokenPointcut()")
    public void setKeyPair(JoinPoint point) {
        Object[] args = point.getArgs();
        for (Object obj:args){
            if(obj instanceof RequestVo){
                RequestVo reqVo = (RequestVo) obj;
                if(StringUtil.isEmpty(reqVo.getEnterprise_sn())){
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("user_name、enterprise_sn不能为空");
                }
                EmployerMainstayRelation byExternalEnterpriseSn = relationFacade.getByExternalEnterpriseSn(reqVo.getEnterprise_sn());
                if (StringUtils.isBlank(reqVo.getPassword())) {

                    if (StringUtils.isNotBlank(byExternalEnterpriseSn.getExternalPassword())) {
                        reqVo.setPassword(byExternalEnterpriseSn.getExternalPasswordDecrypt());
                    }else {
                        String defaultYishuiPassword = dataDictionaryFacade.getSystemConfig("defaultYishuiPassword");
                        if (StringUtils.isBlank(defaultYishuiPassword)) {
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请配置正确的默认登陆密码");
                        }
                        reqVo.setPassword(AESUtil.decryptECB(defaultYishuiPassword, EncryptKeys.getEncryptKeyById(101L).getEncryptKeyStr()));
                    }
                }
                String token = yishuiBiz.getYishuiToken(byExternalEnterpriseSn.getExternalUserName(), reqVo.getPassword(), byExternalEnterpriseSn.getExternalEnterpriseSn());
                LimitUtil.notEmpty(token, String.format("用户名：%s,用户密码：%s,企业编码：%s,登录失败", reqVo.getUser_name(),reqVo.getPassword(),reqVo.getEnterprise_sn()));

                reqVo.setToken(token);
                break;
            }
        }
    }
}
