package com.zhixianghui.service.banklink.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "channel.ali")
public class AlipayConfig {

    /**
     * appId
     */
    private String appId;
    /**
     * 回调url
     */
    private String notifyUrl;
    /**
     * 编码方式
     */
    private String charSet = "utf-8";
    /**
     * 商户私钥
     */
    private String merchantPrivateKey;
    /**
     * 支付宝公钥
     */
    private String alipayPublicKey;
    /**
     * 签名方式
     */
    private String signType = "RSA2";
    /**
     * 支付宝服务地址
     */
    private String serverUrl = "https://openapi.alipay.com/gateway.do";
    /**
     * 应用证书路径
     */
    private String appCertPath;
    /**
     * 支付宝证书路径
     */
    private String alipayCertPath;
    /**
     * 根证书路径
     */
    private String alipayRootCertPath;


}
