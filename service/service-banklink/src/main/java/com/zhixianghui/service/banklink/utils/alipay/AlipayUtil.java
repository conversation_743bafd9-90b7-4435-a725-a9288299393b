package com.zhixianghui.service.banklink.utils.alipay;

import com.alipay.api.AlipayApiException;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.easysdk.factory.Factory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AlipayUtil {
    final private CertAlipayRequest certAlipayRequest;

    private final static List<String> NO_SIGN_PARAM = Arrays.asList(new String[]{"out_biz_channel_type"});//不参与签名/验签的参数

    public boolean verify(Map<String,String> params) {

        try {
            NO_SIGN_PARAM.stream().forEach(x-> params.remove(x));
            boolean checkResult = AlipaySignature.rsaCertCheckV1(params, certAlipayRequest.getAlipayPublicCertPath(), certAlipayRequest.getCharset(),certAlipayRequest.getSignType());
            return checkResult;
        } catch (AlipayApiException e) {
            log.error("回调验签失败",e);
            return false;
        }

    }

}
