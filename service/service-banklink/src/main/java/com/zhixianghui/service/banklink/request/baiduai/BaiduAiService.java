package com.zhixianghui.service.banklink.request.baiduai;

import cn.hutool.core.io.FileUtil;
import com.baidu.aip.ocr.AipOcr;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.vo.ocr.BusinessLicenseOctVo;
import com.zhixianghui.facade.banklink.vo.ocr.IdcardOcrVo;
import com.zhixianghui.facade.banklink.enums.IdCardSiteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BaiduAiService {

    public static final String APP_ID = "********";
    public static final String API_KEY = "mhdp1tvPjdc7GT6BzLWpiHVG";
    public static final String SECRET_KEY = "9RcTmPZctIudrfdwX5RWdZ9lfXtG9zrS";

    public IdcardOcrVo idCardOcr(byte[] idcardBytes, IdCardSiteEnum idCardSite,String miniUserNo) {
        AipOcr client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);
        client.setConnectionTimeoutInMillis(20000);
        client.setSocketTimeoutInMillis(10000);

        log.info("百度智能云身份证OCR识别开始：miniUserNo:{}", miniUserNo);
        JSONObject idcard = client.idcard(idcardBytes, idCardSite.getValue(), Maps.newHashMap());
        log.info(idcard.toString());
        Long logId = idcard.getLong("log_id");
        log.info("百度智能云身份证OCR识别结束-miniUserNo:{},logId:{}", miniUserNo, logId);
        if (!StringUtils.equals("normal", idcard.getString("image_status")) || idcard.getInt("words_result_num") != 6) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("识别失败，请手动填写身份证信息");
        }

        JSONObject wordsResult = idcard.getJSONObject("words_result");
        String name = wordsResult.getJSONObject("姓名").getString("words");
        String national = wordsResult.getJSONObject("民族").getString("words");
        String address = wordsResult.getJSONObject("住址").getString("words");
        String idcardNo = wordsResult.getJSONObject("公民身份号码").getString("words");
        String birthday = wordsResult.getJSONObject("出生").getString("words");
        String gender = wordsResult.getJSONObject("性别").getString("words");
        return new IdcardOcrVo(name, national, address, idcardNo, birthday, gender );
    }

    public BusinessLicenseOctVo businessLicenseOcr(byte[] bussinessLicenseBytes,String miniUserNo) {
        AipOcr client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);
        client.setConnectionTimeoutInMillis(20000);
        client.setSocketTimeoutInMillis(10000);
        log.info("百度智能云营业执照OCR识别开始：miniUserNo:{}", miniUserNo);
        JSONObject bussinessLicense = client.businessLicense(bussinessLicenseBytes, Maps.newHashMap());
        Long logId = bussinessLicense.getLong("log_id");
        log.info("百度智能云营业执照OCR识别结束-miniUserNo:{},logId:{}", miniUserNo, logId);
        if (bussinessLicense.getInt("words_result_num") != 15) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请提供完整清晰的营业执照图片");
        }

        JSONObject wordsResult      = bussinessLicense.getJSONObject("words_result");
        String bussinsesScope       = wordsResult.getJSONObject("经营范围").getString("words");
        String orgType              = wordsResult.getJSONObject("组成形式").getString("words");
        String legalPerson          = wordsResult.getJSONObject("法人").getString("words");
        String licenseNo            = wordsResult.getJSONObject("证件编号").getString("words");
        String registCapital        = wordsResult.getJSONObject("注册资本").getString("words");
        String companyName          = wordsResult.getJSONObject("单位名称").getString("words");
        String validScope           = wordsResult.getJSONObject("有效期").getString("words");
        String socialCreditCode     = wordsResult.getJSONObject("社会信用代码").getString("words");
        String paidCapital          = wordsResult.getJSONObject("实收资本").getString("words");
        String approvalDate         = wordsResult.getJSONObject("核准日期").getString("words");
        String foundDate            = wordsResult.getJSONObject("成立日期").getString("words");
        String taxRegistNo          = wordsResult.getJSONObject("税务登记号").getString("words");
        String address              = wordsResult.getJSONObject("地址").getString("words");
        String registOrganization   = wordsResult.getJSONObject("登记机关").getString("words");
        String category             = wordsResult.getJSONObject("类型").getString("words");
        return new BusinessLicenseOctVo(bussinsesScope, orgType, legalPerson, licenseNo, registCapital,
                companyName, validScope, socialCreditCode, paidCapital, approvalDate, foundDate, taxRegistNo,
                address, registOrganization, category);
    }

    public static void main(String[] args) {

        testBussinessLicenseOcr();

    }

    private static void testIdcardOcr() {
        byte[] readBytes = FileUtil.readBytes("C:\\Users\\<USER>\\Desktop\\Ch62CmQjBXqAM3K2AAGmvc0BbFM517.JPG");
        BaiduAiService baiduAiService = new BaiduAiService();
        IdcardOcrVo idcardOcrVo = baiduAiService.idCardOcr(readBytes, IdCardSiteEnum.FONT,"");
        System.out.printf(idcardOcrVo.toString());
    }

    private static void testBussinessLicenseOcr() {
        byte[] readBytes = FileUtil.readBytes("C:\\Users\\<USER>\\Desktop\\20230329114141.png");
        BaiduAiService baiduAiService = new BaiduAiService();
        BusinessLicenseOctVo businessLicenseOctVo = baiduAiService.businessLicenseOcr(readBytes,"");
        System.out.printf(businessLicenseOctVo.toString());
    }
}
