package com.zhixianghui.service.banklink.request.auth;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.auth.AuthChannelEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.LogEncryUtil;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.service.banklink.request.pay.ConvertMessageBiz;
import com.zhixianghui.service.banklink.utils.joinpay.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

/**
 * 汇聚支付出款交易
 *
 * <AUTHOR>
 * @date 2020/10/26
 **/
@Component
@Slf4j
public class JoinpayAuthBiz {

    // 鉴权成功
    private static final Integer SUCCESS_AUTH_STATUS = 100;
    // 鉴权失败
    private static final Integer FAIL_AUTH_STATUS = 101;
    //系统异常
    private static final Integer EXCEPTION_AUTH_STATUS = 102;
    // 成功请求响应码
    private static final String SUCCESS_RESP_CODE = "A1000";
    // 系统异常响应码
    private static final String SYSTEM_EXCEPTION_CODE = "B113000";

    @Autowired
    private ConvertMessageBiz convertMessageBiz;

    @Value(value = "${joinpayAuthUrl}")
    private String joinpayAuthUrl;
    @Value(value = "${joinpayAuthPrivateKey}")
    private String joinpayAuthPrivateKey;
    @Value(value = "${joinpayAuthMchNo}")
    private String joinpayAuthMchNo;
    @Value(value = "${joinpayAuthMchName}")
    private String joinpayAuthMchName;
    @Value(value = "${ProtocolVersion}")
    private String protocolVersion;
    /**
     * 付款请求
     *
     * @param reqVo
     * @return
     */
    public AuthRespVo auth(AuthReqVo reqVo) {
        // 封装请求参数并算签
        Map<String, String>  map = fillAuthParamV3(reqVo);
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.setAll(map);
        // 请求
        log.info("[{}] 请求汇聚鉴权接口参数：{}", reqVo.getBankOrderNo(), LogEncryUtil.encrypt(JsonUtil.toString(map)));
        JSONObject respObject = null;
        try{
            WebClient webClient = WebClient.create(joinpayAuthUrl);
            Mono<String> mono = webClient.post().contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .body(BodyInserters.fromFormData(formData)).retrieve().bodyToMono(String.class);
            respObject = JSONObject.parseObject(mono.block(Duration.ofMillis(10000)));
        }catch (Exception e){
            log.info("[{}] 请求汇聚鉴权接口异常：",reqVo.getBankOrderNo(), e);
            AuthRespVo respVo = new AuthRespVo();
            respVo.setAuthStatus(BankAuthStatusEnum.UN_KNOW.getValue());
//            respVo.setBizDesc(e.getMessage());
            return respVo;
        }

        log.info("请求汇聚鉴权接口返回原始结果：{}", JsonUtil.toString(respObject));
        return fillAuthRespVo(respObject);
    }

    private AuthRespVo fillAuthRespVo(JSONObject respObject) {
        AuthRespVo respVo = new AuthRespVo();
        if(Objects.equals(SUCCESS_AUTH_STATUS, respObject.getInteger("ra_Status"))){
            respVo.setAuthStatus(BankAuthStatusEnum.SUCCESS.getValue());
        } else if(Objects.equals(FAIL_AUTH_STATUS, respObject.getInteger("ra_Status"))){
            if(StringUtils.contains(respObject.getString("rb_Msg") ,"通道超时")){
                respVo.setAuthStatus(BankAuthStatusEnum.SYSTEM_ERROR.getValue());
            }else {
                respVo.setAuthStatus(BankAuthStatusEnum.FAIL.getValue());
            }
        } else if(Objects.equals(EXCEPTION_AUTH_STATUS, respObject.getInteger("ra_Status"))){
            respVo.setAuthStatus(BankAuthStatusEnum.UN_KNOW.getValue());
        } else {
            respVo.setAuthStatus(BankAuthStatusEnum.SYSTEM_ERROR.getValue());
        }
        String rb_msg = convertMessageBiz.getAuthMsg(respObject.getString("rb_Msg"));
        respVo.setBizDesc(rb_msg);
        respVo.setProtocolVersion(protocolVersion);
        respVo.setChannelNo(AuthChannelEnum.JOINPAY.name());
        return respVo;
    }

    private Map<String,String> fillAuthParamV3(AuthReqVo reqVo){
        TreeMap<String, String> map = new TreeMap<>();
        map.put("p0_Version", "3.0");
        map.put("p1_MerchantNo", joinpayAuthMchNo);
        map.put("p2_MerchantName", joinpayAuthMchName);
        map.put("q1_OrderNo", reqVo.getBankOrderNo());

        // 身份证+姓名+卡号
        if(Objects.equals(reqVo.getAuthType(), AuthTypeEnum.IDCARD_NAME_BANKCARD.getValue())){
            map.put("q2_AuthType", "8");
            map.put("s3_PayerBankCardNo", reqVo.getBankAccountNo());
        }
        // 身份证+姓名+手机号
        else if(Objects.equals(reqVo.getAuthType(), AuthTypeEnum.IDCARD_NAME_PHONE.getValue())){
            map.put("q2_AuthType", "12");
            map.put("s6_BankMobile", reqVo.getPhoneNo());
        } else if(Objects.equals(reqVo.getAuthType(), AuthTypeEnum.IDCARD_NAME.getValue())){
            map.put("q2_AuthType", "5");
        } else if(Objects.equals(reqVo.getAuthType(), AuthTypeEnum.IDCARD_NAME_BANKCARD_PHONE.getValue())){
            map.put("q2_AuthType", "9");
            map.put("s3_PayerBankCardNo", reqVo.getBankAccountNo());
            map.put("s6_BankMobile", reqVo.getPhoneNo());
        }else {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("鉴权方式有误");
        }


        map.put("s1_PayerName", reqVo.getName());
        map.put("s2_PayerCardNo", reqVo.getIdCardNo());
        map.put("s7_ProtocolVersion",protocolVersion);
        map.put("s8_ProtocolNo",reqVo.getSerialNo());
        map.put("hmac", SignUtil.genSign_1_0(map, joinpayAuthPrivateKey));
        return map;
    }

    @Deprecated
    private Map<String, String> fillAuthParam(AuthReqVo reqVo) {
        TreeMap<String, String> map = new TreeMap<>();
        map.put("p0_Version", "2.0");
        map.put("p1_MerchantNo", joinpayAuthMchNo);
        map.put("p2_MerchantName", joinpayAuthMchName);
        map.put("q1_OrderNo", reqVo.getBankOrderNo());

        // 身份证+姓名+卡号
        if(Objects.equals(reqVo.getAuthType(), AuthTypeEnum.IDCARD_NAME_BANKCARD.getValue())){
            map.put("q2_AuthType", "8");
            map.put("s3_PayerBankCardNo", reqVo.getBankAccountNo());
        }
        // 身份证+姓名+手机号
        else if(Objects.equals(reqVo.getAuthType(), AuthTypeEnum.IDCARD_NAME_PHONE.getValue())){
            map.put("q2_AuthType", "12");
            map.put("s6_BankMobile", reqVo.getPhoneNo());
        } else if(Objects.equals(reqVo.getAuthType(), AuthTypeEnum.IDCARD_NAME.getValue())){
            map.put("q2_AuthType", "5");
        } else {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("鉴权方式有误");
        }


        map.put("s1_PayerName", reqVo.getName());
        map.put("s2_PayerCardNo", reqVo.getIdCardNo());
        map.put("hmac", SignUtil.genSign_1_0(map, joinpayAuthPrivateKey));
        return map;
    }


}
