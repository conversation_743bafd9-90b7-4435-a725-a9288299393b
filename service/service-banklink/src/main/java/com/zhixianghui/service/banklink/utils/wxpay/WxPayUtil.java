package com.zhixianghui.service.banklink.utils.wxpay;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import com.zhixianghui.common.statics.exception.WxApiException;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.service.banklink.config.WxPayConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.X509Certificate;
import java.util.Base64;

/**
 * <AUTHOR>
 * @ClassName WxPayUtil
 * @Description TODO
 * @Date 2021/12/3 9:32
 */
@Slf4j
@Component
public class WxPayUtil {

    @Autowired
    private WxPayConfig wxPayConfig;

    private static WechatPayHttpClientBuilder builder;

    private static ScheduledUpdateCertificatesVerifier verifier;

    private static CloseableHttpClient httpClient;

    private static PrivateKey privateKey;

    private final static String ENCRYPT_TYPE = "RSA/ECB/OAEPWithSHA-1AndMGF1Padding";

    private final static String HASH_TYPE = "SHA256withRSA";

    private static String signHeader;

    private static String signHeadSuf;

    @PostConstruct
    public void init() throws UnsupportedEncodingException {
        //采用无限制加密算法
        Security.setProperty("crypto.policy","unlimited");

        privateKey = PemUtil.loadPrivateKey(new ByteArrayInputStream(wxPayConfig.getPrivateKey().getBytes("utf-8")));

        verifier = new ScheduledUpdateCertificatesVerifier(
                new WechatPay2Credentials(wxPayConfig.getMerchantId(), new PrivateKeySigner(wxPayConfig.getSerialNo(),privateKey)),
                wxPayConfig.getApiv3Key().getBytes(StandardCharsets.UTF_8));

        //构建需要签名的httpClient
        builder = WechatPayHttpClientBuilder.create()
                .withMerchant(wxPayConfig.getMerchantId(),wxPayConfig.getSerialNo(), privateKey)
                //定时更新平台证书
                .withValidator(new WechatPay2Validator(verifier));

        httpClient = builder.build();

        signHeader =  new StringBuffer("WECHATPAY2-SHA256-RSA2048 mchid=").append("\"").append(wxPayConfig.getMerchantId()).append("\",").toString();
        signHeadSuf = new StringBuffer("serial_no=").append("\"").append(wxPayConfig.getSerialNo()).append("\"").toString();
    }

    //获取微信平台证书
    public static X509Certificate getWxCertificate(){
        if (verifier != null){
            return verifier.getLatestCertificate();
        }else{
            throw new NullPointerException("验签器不存在");
        }
    }

    //敏感信息加密
    public static String getEncrypt(String message) throws IllegalBlockSizeException {
        try {
            Cipher cipher = Cipher.getInstance(ENCRYPT_TYPE);
            cipher.init(Cipher.ENCRYPT_MODE, getWxCertificate().getPublicKey());

            byte[] data = message.getBytes("utf-8");
            byte[] cipherdata = cipher.doFinal(data);
            return Base64.getEncoder().encodeToString(cipherdata);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | UnsupportedEncodingException e) {
            throw new RuntimeException("当前Java环境不支持RSA v1.5/OAEP", e);
        } catch (InvalidKeyException e) {
            throw new IllegalArgumentException("无效的证书", e);
        } catch (IllegalBlockSizeException | BadPaddingException e) {
            throw new IllegalBlockSizeException("加密原串的长度不能超过214字节");
        }
    }

    /**
     * post请求，关单接口使用，返回204为成功
     * @param uriBuilder
     * @param json
     * @return
     * @throws URISyntaxException
     * @throws IOException
     */
    public static WxResVo sendPostEmptyResp(URIBuilder uriBuilder,String json) throws URISyntaxException, IOException {
        HttpPost httpPost = new HttpPost(uriBuilder.build());
        httpPost.setHeader("User-Agent","Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        httpPost.setHeader("Accept","application/json");
        httpPost.setHeader("Content-Type","application/json");
        httpPost.setHeader("Wechatpay-Serial",getWxCertificate().getSerialNumber().toString(16));
        httpPost.setEntity(new StringEntity(json,"utf-8"));
        CloseableHttpResponse response = httpClient.execute(httpPost);
        StatusLine statusLine = response.getStatusLine();
        if (statusLine.getStatusCode() == 204){
            return new WxResVo(200,"");
        }else{
            String bodyAsString = EntityUtils.toString(response.getEntity());
            return new WxResVo(statusLine.getStatusCode(),bodyAsString);
        }
    }

    public static WxResVo sendPost(URIBuilder uriBuilder,String json) throws URISyntaxException, IOException {
        HttpPost httpPost = new HttpPost(uriBuilder.build());
        httpPost.setHeader("User-Agent","Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        httpPost.setHeader("Accept","application/json");
        httpPost.setHeader("Content-Type","application/json");
        httpPost.setHeader("Wechatpay-Serial",getWxCertificate().getSerialNumber().toString(16));
        httpPost.setEntity(new StringEntity(json,"utf-8"));
        CloseableHttpResponse response = httpClient.execute(httpPost);
        StatusLine statusLine = response.getStatusLine();
        String bodyAsString = EntityUtils.toString(response.getEntity());
        return new WxResVo(statusLine.getStatusCode(),bodyAsString);
    }

    public static WxResVo sendGet(URIBuilder uriBuilder) throws URISyntaxException, IOException {
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        httpGet.setHeader("User-Agent","Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        httpGet.setHeader("Accept","application/json");
        httpGet.setHeader("Content-Type","application/json");
        httpGet.setHeader("Wechatpay-Serial",getWxCertificate().getSerialNumber().toString(16));
        CloseableHttpResponse response = httpClient.execute(httpGet);
        StatusLine statusLine = response.getStatusLine();
        String bodyAsString = EntityUtils.toString(response.getEntity());
        return new WxResVo(statusLine.getStatusCode(),bodyAsString);
    }

    /**
     * 不需要应答验签的get发放
     * @throws IOException
     * @throws URISyntaxException
     */
    public static HttpResponse httpGetWithNoResSign(String url) {
        long timestamp = System.currentTimeMillis();
        String nonceStr = RandomStringUtils.randomAlphanumeric(32);
        StringBuffer sb = new StringBuffer(signHeader).append("nonce_str=").append("\"").append(nonceStr).append("\",")
                .append("timestamp=").append("\"").append(timestamp).append("\",")
                .append("signature=").append("\"").append(getSign(HttpMethod.GET,url,timestamp,nonceStr,null)).append("\",")
                .append(signHeadSuf);
        HttpResponse response = HttpRequest.get(url).auth(sb.toString()).execute();
        System.out.println(response);
        return response;
    }

    private static String getSign(HttpMethod httpMethod, String url, long timestamp, String nonce, String body){
        Signature sign = null;
        try {
            sign = Signature.getInstance(HASH_TYPE);
            sign.initSign(privateKey);
            sign.update(buildMessage(httpMethod,url,timestamp,nonce,body));
            return Base64.getEncoder().encodeToString(sign.sign());
        } catch (Exception e) {
            log.error("微信签名失败");
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    private static byte[] buildMessage(HttpMethod httpMethod, String url, long timestamp, String nonce, String body) throws UnsupportedEncodingException, URISyntaxException {
        URI uri = new URI(url);
        String canonicalUrl = uri.getPath();
        if (uri.getQuery() != null){
            canonicalUrl += "?" + uri.getQuery();
        }
        StringBuffer sb = new StringBuffer();
        sb.append(httpMethod.name()).append("\n")
                .append(canonicalUrl).append("\n")
                .append(timestamp).append("\n")
                .append(nonce).append("\n");
        if (StringUtils.isNotBlank(body)){
            sb.append(body).append("\n");
        }
        return sb.toString().getBytes("utf-8");
    }

    public  static void main(String[] args) {
        String privateKey="MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCbLEfV9WOilkeQeei6pDu58bh4CN23Tti0FjG0XmDlmd2gnvqRwTmtJqprVHOI+neHOcUF6p68QOV4KD/GAGfjWa/QSExcCKtKhlXEjCHSYNuMx1FQm4FxUuHXg8zZRCOn+JyUk9EXojqsm3WwPKK1j8I4omznxXRNqYW8BM5jc4br879Ji7wKJm3u5pDpPR6V1DSUhw0J2CAadxSdAqxtfTRAivLSUtMpFaFrPpqVjurtlJNN7taTn2v6Cy8PNL9OfXJJN0RGJYvFlbUAMCkTmt8o53eB0TngatWpfMga5OcdNRk2VRpqH6zPKeuCdZdurL6hC6n5EgZVtRYUgpxDAgMBAAECggEAOjQDxedDNgr0UObJOqi7lmVqceuxbj5u3i2s+yCja7uo+8Rl7CH0BlSIe7WzmF+KUfiDRFLas3YMHGPMd/JTBV3wqklUCvlddpiotw5w6T5XW5ivkXKivOT/vRy8y4nZbfm8kaZYOWsqYu/01uQHi96eVSvneZAArdi2Ru3ulDJ9k6+VfyQSQF8Ia6cS9BJ/EmNANRGRkiM1YmJl76rLUv1IydgrWYdKrg4ZhRQqIS1flzfJtZ9nkiS+B3/JcBmmeiHsaFZRV4JOVXQFFLUJB+MMGLpZxjpmHY5YLkKqiUH3zzXo34tOXYkbesXRiHz9PJMce3TX3erTHAKSJXSUgQKBgQDJsM9g+kiXnJc5O5zn2bFJO8gu6aG4HES3mALy58XLOPvT7GSFtIMoP9Z5UPR7AyO1kJtxcDQwo5ejqGWSiD5pgmlcpHZfd+OZVNnAvTP0zow4bkC7LdcfE07T7tkpXdjZRQIEHk43lsKUV1GLClb9GdapyQvkE767n1UDREgNIwKBgQDE9Nr/vegLvewXc0msxuyBl5dQtVqb5Jc5i87LL83wKXsb+r7LE2lGFp+TNck/EjSMEKTkqHWsZio7CdVBt5sPS7sJxMz06GFA5yOcW9+sFca7RKJAmiqW2s9B5QQL68J07p0Y6T6qP1JrwvWN1HdwLfeVQ8rVrPpNiU5o1XL2YQKBgCKtZ7qAzeJjZgzNFxk18Klqwexu9wM0uQvn8vnYp8VdMs8pIc9rTfGpqDKLnL1ZuNYK2u8SH6cNOZFF7tDW5AQ4C+2FyWR4tIU0dYwR+9DngmxhaHfGVepd43vE4UmffR0+JC6ZF1Gvdh0TNk31pSc3PhVLH+RDENQpfPBm2QyJAoGADA3cVWifChDuoSTKLoviL7BCb4sZ546e/sOivWbImNlBEp5PDQi8GbEKxSy2i2im1Ke0H9Z7lTiPfxuD9Miy8cW1xcxIqviJ8znyQQCSeOrARffWYlPNJXxw0NyeWchtF1D/RmYIEs1bUwMUmkCVqsT1cSo8AMXcb+Bmbx4ZMiECgYAwBiJbJVhSdAYu8OexPgwPjcScLz3i+W45gYoGbwMmLBJCl9HQt9RqKTvflx0UoIUbHvz5xpfXAWsEdOzR8nDBw4wTsBbOc3vBi+J/0J4KLX5Y41bJX0tBI9jzukKfA+5SFE4A5PttJZ4iGB8IiXxJHuc8RTNSo2ddYfAPrPYnAw==";
        privateKey = privateKey.replace("-----BEGIN PRIVATE KEY-----", "").replace("-----END PRIVATE KEY-----", "").replaceAll("\\s+", "");
        System.out.println(privateKey);
    }
}
