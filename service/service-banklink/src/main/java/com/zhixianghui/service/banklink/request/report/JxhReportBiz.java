package com.zhixianghui.service.banklink.request.report;

import com.zhixianghui.common.statics.enums.bankLink.report.ApiReportStatusEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.vo.joinpay.req.ApplyAccountEditReq;
import com.zhixianghui.facade.banklink.vo.joinpay.req.ApplyAccountReq;
import com.zhixianghui.facade.banklink.vo.joinpay.resp.JoinpayBaseResp;
import com.zhixianghui.facade.banklink.vo.report.ReportReqVo;
import com.zhixianghui.facade.banklink.vo.report.ReportResVo;
import com.zhixianghui.service.banklink.annotation.CurrentKeyPair;
import com.zhixianghui.service.banklink.request.pay.JxhPayBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class JxhReportBiz {
    @Autowired
    private JxhPayBiz jxhPayBiz;

    @CurrentKeyPair
    public ReportResVo report(ReportReqVo reportReqVo) {
        ApplyAccountReq req = new ApplyAccountReq();
        req.setPayeeAccountNickname(replaceNickName(reportReqVo.getAccountName()));
        req.setPayerAccountNo(reportReqVo.getAccountNo());
        req.setPayerAccountName(reportReqVo.getAccountName());
        req.setChannelMchNo(reportReqVo.getChannelMchNo());
        req.setChannelPublicKey(reportReqVo.getKeyPairRecord().getChannelPublicKeyDecrypt());
        req.setPlatPrivateKey(reportReqVo.getKeyPairRecord().getMchPrivateKeyDecrypt());
        JoinpayBaseResp resp = jxhPayBiz.createApplyAcount(req);
        ReportResVo result = new ReportResVo();
        if ("SUCCESS".equals(resp.getResp_code())) {
            result.setApiReportStatus(ApiReportStatusEnum.SUCCESS.getValue());
            result.setBizCode(resp.getBiz_code());
            result.setBizMsg(resp.getBiz_msg());
            result.setData(JsonUtil.toString(resp.getData()));
        } else if ("FAIL".equals(resp.getResp_code())) {
            result.setApiReportStatus(ApiReportStatusEnum.FAIL.getValue());
            result.setBizCode(resp.getBiz_code());
            result.setBizMsg(resp.getBiz_msg());
        } else {
            result.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
        }
        return result;
    }

    @CurrentKeyPair
    public ReportResVo modify(ReportReqVo reportReqVo) {
        ApplyAccountEditReq req = new ApplyAccountEditReq();
        req.setPayeeAccountNo(reportReqVo.getSubMerchantNo());
        if (StringUtil.isNotEmpty(reportReqVo.getAccountName())) {
            req.setPayeeAccountNickname(replaceNickName(reportReqVo.getAccountName()));
        }
        if (StringUtil.isNotEmpty(reportReqVo.getAccountNo())) {
            req.setPayerAccountNo(reportReqVo.getAccountNo());
        }
        if (StringUtil.isNotEmpty(reportReqVo.getAccountName())) {
            req.setPayerAccountName(reportReqVo.getAccountName());
        }
        req.setChannelMchNo(reportReqVo.getChannelMchNo());
        req.setChannelPublicKey(reportReqVo.getKeyPairRecord().getChannelPublicKeyDecrypt());
        req.setPlatPrivateKey(reportReqVo.getKeyPairRecord().getMchPrivateKeyDecrypt());
        JoinpayBaseResp resp = jxhPayBiz.editApplyAccount(req);
        log.info("君享汇账号修改接口响应数据为：{}", JsonUtil.toString(resp));
        ReportResVo result = new ReportResVo();
        if ("SUCCESS".equals(resp.getResp_code())) {
            result.setApiReportStatus(ApiReportStatusEnum.SUCCESS.getValue());
            result.setBizCode(resp.getBiz_code());
            result.setBizMsg(resp.getBiz_msg());
            result.setData(JsonUtil.toString(resp.getData()));
        } else if ("FAIL".equals(resp.getResp_code())) {
            result.setApiReportStatus(ApiReportStatusEnum.FAIL.getValue());
            result.setBizCode(resp.getBiz_code());
            result.setBizMsg(resp.getBiz_msg());
        } else {
            result.setApiReportStatus(ApiReportStatusEnum.UN_KNOW.getValue());
        }
        return result;
    }

    /***
     * 处理公司名称
     * @param nickName
     * @return
     */
    public static String replaceNickName(String nickName) {
        String[] template = {"科技有限公司", "有限公司", "有限责任公司", "股份有限公司", "集团有限公司"};
        for (String s : template) {
            int indexOf = nickName.indexOf(s);
            if (indexOf > 0) {
                nickName = nickName.substring(0, indexOf);
                if (nickName.length() > 15) {
                    nickName = nickName.substring(0, 14);
                }
                break;
            }
        }
        return nickName;
    }

    public static void main(String[] args) {
        System.out.println(replaceNickName("北京拉近众博科技有限公司"));
        System.out.println(replaceNickName("广州安泰保险代理有限公司东莞分公司"));
        System.out.println(replaceNickName("广州安泰保险代理有限公司东莞寮步分公司"));
        System.out.println(replaceNickName("白日天光（上海）生物科技有限责任公司"));
        System.out.println(replaceNickName("东方大地（武汉）保险经纪有限公司湖北分公司"));
    }
}
