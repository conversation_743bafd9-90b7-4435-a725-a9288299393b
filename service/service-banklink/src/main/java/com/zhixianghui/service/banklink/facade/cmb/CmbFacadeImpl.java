package com.zhixianghui.service.banklink.facade.cmb;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.banklink.vo.pay.*;
import com.zhixianghui.service.banklink.request.cmb.CmbApiService;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;


@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CmbFacadeImpl implements CmbFacade {

    final private CmbApiService cmbApiService;
    final private FastdfsClient fastdfsClient;

    @Override
    public JSONObject createAccountBook(String accountNo, String accountBookNo, String accountBookName) {
        return cmbApiService.createAccountBook(accountNo, accountBookNo, accountBookName);
    }

    @Override
    public JSONObject queryAccountBook(String accountNo,String accountBookNo) {
        return cmbApiService.queryAccountBook(accountNo, accountBookNo);
    }

    @Override
    public JSONObject payBatchQuery(String accountNo, String remitNo) {
        return cmbApiService.payBatchQuery(accountNo, remitNo);
    }

    @Override
    public JSONObject queryDailyAccountNoBalance(String bankBranch, String accountNo,String accountBookNo,String begdat,String enddat) {
        return cmbApiService.queryDailyAccountNoBalance(bankBranch, accountNo, accountBookNo, begdat, enddat);
    }

    @Override
    public WithdrawRespVo withdraw(WithdrawReqVo withdrawReqVo) {
        JSONObject payResult = null;

        CmbPay2BVo cmbPay2BVo = new CmbPay2BVo();
        cmbPay2BVo.setReceiveAccountName(withdrawReqVo.getReceiveName());
        cmbPay2BVo.setReceiveAccountNo(withdrawReqVo.getReceiveAccountNo());
        cmbPay2BVo.setTrxNo(withdrawReqVo.getBankOrderNo());
        cmbPay2BVo.setTransAmount(NumberUtil.decimalFormat("#0.00", new BigDecimal(withdrawReqVo.getReceiveAmount())));
        cmbPay2BVo.setAccountNo(withdrawReqVo.getPayerAgreementNo());
        //cmbPay2BVo.setAccountBookNo(withdrawReqVo.getPayerAccountBookId());
        cmbPay2BVo.setReceiveBankName(withdrawReqVo.getPayeeBankName());
        cmbPay2BVo.setBankNo(withdrawReqVo.getBankChannelNo());
        try {
            payResult = cmbApiService.pay2bApply(cmbPay2BVo);
            if (payResult != null && payResult.get("bb1payopz1") != null && payResult.getJSONArray("bb1payopz1").getJSONObject(0) != null) {
                JSONObject payRstData = payResult.getJSONArray("bb1payopz1").getJSONObject(0);
                String bankTrxNo = payRstData.getString("reqNbr");
                String reqSts = payRstData.getString("reqSts");
                String rtnFlg = payRstData.getString("rtnFlg");
                String errTxt = payRstData.getString("errTxt");
                if(StringUtils.equals("FIN", reqSts) && StringUtils.equals("F", rtnFlg)) {
                    //经办接口返回FIN+F，可以判定为经办失败
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg(errTxt);
                }
                WithdrawRespVo withdrawRespVo = new WithdrawRespVo();
                withdrawRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
                withdrawRespVo.setBankTrxNo(bankTrxNo);
                withdrawRespVo.setBankOrderNo(withdrawReqVo.getBankOrderNo());
                return withdrawRespVo;
            }else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提现失败");
            }
        } catch (Exception e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(e.getMessage());
        }
    }

    @Override
    public JSONObject reconfileApply(String accountNo, String startDate, String endDate, String begidx) {
        final JSONObject reconFileJson = cmbApiService.applyReconFile(accountNo, startDate, endDate,begidx);
        return reconFileJson;
    }

    @Override
    public JSONObject getReconFileUrl(String accountNo, String taskId) {
        final JSONObject reconFileJson = cmbApiService.getReconFileUrl(accountNo, taskId);
        return reconFileJson;
    }

    @Override
    public String getAccountReconFileUrlSync(String accountNo, String tradeDate,String trxSeq) {
        final JSONObject respData = cmbApiService.getAccountReconFileUrlSync(accountNo, tradeDate, trxSeq);
        final String fildat = respData.getString("fildat");
        String filepath = null;
        if (StringUtils.isNotBlank(fildat)) {
            final byte[] decode = Base64.decode(fildat);
            filepath = fastdfsClient.uploadFile(decode, RandomUtil.get16LenStr() + ".pdf");
        }
        return filepath;
    }

    @Override
    public JSONObject payApplyBatch(String accountNo, Dict body) throws BizException {
        return cmbApiService.payApplyBatch(accountNo, body);
    }

    @Override
    public JSONObject queryPayBatchOrder(String batchNo, String accountNo) {
        return cmbApiService.queryPayBatchOrder(batchNo, accountNo);
    }


    @Override
    public JSONObject payRecordDetailQuery(String accountNo,String reqnbr,String trxseq,String bthnbr) {
        return cmbApiService.payRecordDetailQuery(accountNo, reqnbr, trxseq, bthnbr);
    }

    @Override
    public JSONObject pay2bQuery(String accountNo,String trxNo) {
        return cmbApiService.pay2bQuery(accountNo, trxNo);
    }

    @Override
    public JSONObject transferAccountBooks(String accountNo, String payerAccountBookNo, String payeeAccountBookNo, String amt, String remark, String trxNo) throws BizException {
        return cmbApiService.transferAccountBooks(accountNo, payerAccountBookNo, payeeAccountBookNo, amt, remark, trxNo);
    }
}
