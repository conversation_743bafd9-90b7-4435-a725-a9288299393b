package com.zhixianghui.service.banklink.request.pay;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.BanklinkExceptions;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.vo.joinpay.req.ApplyAccountEditReq;
import com.zhixianghui.facade.banklink.vo.joinpay.req.ApplyAccountQueryReq;
import com.zhixianghui.facade.banklink.vo.joinpay.req.ApplyAccountReq;
import com.zhixianghui.facade.banklink.vo.joinpay.resp.JoinpayBaseResp;
import com.zhixianghui.facade.banklink.vo.joinpay.result.ApplyAcountResult;
import com.zhixianghui.facade.banklink.vo.jxh.JxhResVo;
import com.zhixianghui.facade.banklink.vo.jxh.ResData;
import com.zhixianghui.facade.banklink.vo.jxh.ResStatus;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.pay.SinglePayReceiveRespVo;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.trade.utils.AcUtil;
import com.zhixianghui.service.banklink.annotation.CurrentKeyPair;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import com.zhixianghui.service.banklink.utils.joinpay.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class JxhPayBiz {
    @Reference
    private SequenceFacade sequenceFacade;

    // 君享汇账户创建的接口地址
    @Value(value = "${jxhIncomePayUrl:https://api.joinpay.com/income}")
    private String jxhIncomePayUrl;
    // 单笔代付请求地址
    @Value(value = "${jxhSinglePayUrl:https://www.joinpay.com/payment/pay/singlePay}")
    private String jxhSinglePayUrl;
    // 下发的异步通知地址
    @Value(value = "${jxhSinglePayNotifyUrl}")
    private String jxhSinglePayNotifyUrl;
    // 提现的异步通知地址
    @Value(value = "${jxhWithdrawNotifyUrl}")
    private String jxhWithdrawNotifyUrl;
    // 下发查询接口地址
    @Value(value = "${jxhQuerySinglePayUrl:https://www.joinpay.com/payment/pay/singlePayQuery}")
    private String jxhQuerySinglePayUrl;

    @Value("${jxh.isPay:true}")
    private boolean isPay;
    @Value("${jxh.timeOut:1}")
    private Integer timeOut;

    /***
     * 创建君享汇虚拟账户
     * @param req
     * @return
     */

    public JoinpayBaseResp createApplyAcount(ApplyAccountReq req) {
        JSONObject respObject = null;
        try {
            // 封装请求参数并算签
            TreeMap<String, Object> map = fillApplyAcountParam(req);
            // 请求
            log.info("账号名[{}] 请求君享汇创建虚拟账户接口参数：{}", req.getPayerAccountName(), JsonUtil.toString(map));
            WebClient webClient = WebClient.create(jxhIncomePayUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        } catch (Exception e) {
            log.info("账号名[{}] 请求君享汇创建虚拟账户接口异常：", req.getPayerAccountName(), e);
            JoinpayBaseResp<ApplyAcountResult> respVo = new JoinpayBaseResp<ApplyAcountResult>();
            respVo.setResp_code("UNKNOWN");
            return respVo;
        }

        log.info("账号名[{}] 请求君享汇创建虚拟账户接口返回原始结果：{}", req.getPayerAccountName(), JsonUtil.toString(respObject));
        return analysisJoinpayResp(respObject);
    }

    /***
     * 修改君享汇虚拟账户
     * @param req
     * @return
     */
    @CurrentKeyPair
    public JoinpayBaseResp editApplyAccount(ApplyAccountEditReq req) {
        JSONObject respObject = null;
        try {
            // 封装请求参数并算签
            TreeMap<String, Object> map = fillEditApplyAcountParam(req);
            // 请求
            log.info("账号名[{}] 请求君享汇修改虚拟账户接口参数：{}", req.getPayerAccountName(), JsonUtil.toString(map));
            WebClient webClient = WebClient.create(jxhIncomePayUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        } catch (Exception e) {
            log.info("账号名[{}] 请求君享汇修改虚拟账户接口异常：", req.getPayerAccountName(), e);
            JoinpayBaseResp<ApplyAcountResult> respVo = new JoinpayBaseResp<ApplyAcountResult>();
            respVo.setResp_code("UNKNOWN");
            return respVo;
        }

        log.info("账号名[{}] 请求君享汇修改虚拟账户接口返回原始结果：{}", req.getPayerAccountName(), JsonUtil.toString(respObject));
        return analysisJoinpayResp(respObject);
    }

    /***
     * 查询充值账户信息
     * @param req
     * @return
     */
    @CurrentKeyPair
    public JoinpayBaseResp queryApplyAcount(ApplyAccountQueryReq req) {
        JSONObject respObject = null;
        try {
            // 封装请求参数并算签
            TreeMap<String, Object> map = fillQueryApplyAcountParam(req);
            // 请求
            log.info("收款账号[{}] 请求君享汇查询虚拟账户接口参数：{}", req.getPayeeAccountNo(), JsonUtil.toString(map));
            WebClient webClient = WebClient.create(jxhIncomePayUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        } catch (Exception e) {
            log.info("收款账号[{}] 请求君享汇查询虚拟账户接口异常：", req.getPayeeAccountNo(), e);
            JoinpayBaseResp<ApplyAcountResult> respVo = new JoinpayBaseResp<ApplyAcountResult>();
            respVo.setResp_code("UNKNOWN");
            return respVo;
        }

        log.info("收款账号[{}] 请求君享汇查询虚拟账户接口返回原始结果：{}", req.getPayeeAccountNo(), JsonUtil.toString(respObject));
        return analysisJoinpayResp(respObject);
    }

    /***
     * 单笔代付
     * 需要传的字段如下：
     * channelMchNo（渠道商户号）、PlatTrxNo（平台单号）、ReceiveAccountNo（卡号）、ReceiveName（户名）、
     * AccountType（账户类型）、BankChannelNo（对公必填）、ReceiveAmount（实付金额（元））、RemitRemark（打款备注（默认为"营业款"））
     * @param reqVo
     * @return
     */
    @CurrentKeyPair
    public PayRespVo singlePay(PayReqVo reqVo) {
        // 下发只能是对私，所以固定该类型
        if (!isPay) {
            try {
                log.info("[{}]君享汇支付测试,测试睡眠时间，时长：{}s", reqVo.getBankOrderNo(), timeOut);
                TimeUnit.SECONDS.sleep(timeOut);
                log.info("[{}]君享汇支付测试,不进行真实打款操作", reqVo.getBankOrderNo());
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return getTestPayRespVo(reqVo);
        } else {
            reqVo.setAccountType(201);
            // 封装请求参数并算签
            TreeMap<String, Object> map = fillSinglePayParam(reqVo, jxhSinglePayNotifyUrl, reqVo.getKeyPairRecord().getChannelPublicKeyDecrypt());

            // 请求
            log.info("[{}] 请求君享汇下发接口参数：{}", reqVo.getBankOrderNo(), JsonUtil.toString(map));
            WebClient webClient = WebClient.create(jxhSinglePayUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            JSONObject respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));

            log.info("[{}] 请求君享汇下发接口返回原始结果：{}", reqVo.getBankOrderNo(), JsonUtil.toString(respObject));
            return fillSinglePayRespVo(respObject, reqVo.getKeyPairRecord().getChannelPublicKeyDecrypt());
        }
    }


    /***
     * 提现接口
     * 需要传的字段如下：
     * channelMchNo（渠道商户号）、PlatTrxNo（平台单号）、ReceiveAccountNo（卡号）、ReceiveName（户名）、
     * AccountType（账户类型）、BankChannelNo（对公必填）、ReceiveAmount（实付金额（元））、RemitRemark（打款备注（默认为"营业款"））
     * @param reqVo
     * @return
     */
    @CurrentKeyPair
    public PayRespVo withdraw(PayReqVo reqVo) {
        log.info("接收到提现请求 PayReqVo:{}", JsonUtil.toString(reqVo));
        // 商户、供应商提现，只能对公
        reqVo.setAccountType(204);

        String signKey = getSignKey(reqVo);

        // 封装请求参数并算签
        TreeMap<String, Object> map = fillSinglePayParam(reqVo, jxhWithdrawNotifyUrl, signKey);

        // 请求
        log.info("[{}] 请求君享汇下发接口参数：{}", reqVo.getBankOrderNo(), JsonUtil.toString(map));
        WebClient webClient = WebClient.create(jxhSinglePayUrl);
        Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
        JSONObject respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));

        log.info("[{}] 请求君享汇下发接口返回原始结果：{}", reqVo.getBankOrderNo(), JsonUtil.toString(respObject));
        return fillSinglePayRespVo(respObject, signKey);
    }


    /***
     * 由于汇聚支付提现和君享汇下发都是调用的汇聚代付接口
     * 两种模式保存的秘钥字段不一致，导致需要根据渠道区分获取秘钥的字段
     * @param reqVo
     * @return
     */
    public String getSignKey(Object reqVo) {
        String signKey = null;
        if (reqVo instanceof PayReqVo) {
            PayReqVo payReqVo = (PayReqVo) reqVo;
            if (payReqVo.getChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name())) {
                signKey = payReqVo.getKeyPairRecord().getChannelPublicKeyDecrypt();
            } else if (payReqVo.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())) {
                signKey = payReqVo.getKeyPairRecord().getMchPrivateKeyDecrypt();
            }
        } else if (reqVo instanceof PayReceiveReqVo) {
            PayReceiveReqVo payReqVo = (PayReceiveReqVo) reqVo;
            if (payReqVo.getChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name())) {
                signKey = payReqVo.getKeyPairRecord().getChannelPublicKeyDecrypt();
            } else if (payReqVo.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())) {
                signKey = payReqVo.getKeyPairRecord().getMchPrivateKeyDecrypt();
            }
        }
        return signKey;
    }


    /***
     * 查询单笔代付状态
     * 需要传的字段如下：
     * platTrxNo 平台订单号
     * @return
     */
    @CurrentKeyPair
    public JxhResVo querySinglePay(PayReqVo reqVo) {
        if (!isPay) {
            try {
                log.info("[{}]{}支付测试,测试睡眠时间，时长：{}s", reqVo.getBankOrderNo(), reqVo.getChannelName(), timeOut);
                TimeUnit.SECONDS.sleep(timeOut);
                log.info("[{}]{}支付测试,不进行真实打款操作", reqVo.getBankOrderNo(), reqVo.getChannelName());
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return getTestJxhResVo(reqVo);
        } else {
            String signKey = getSignKey(reqVo);

            // 封装请求参数并算签
            TreeMap<String, Object> map = fillQuerySinglePayParam(reqVo, signKey);

            // 请求
            log.info("[{}] 请求{}下发查询接口参数：{}", reqVo.getBankOrderNo(), reqVo.getChannelName(), JsonUtil.toString(map));
            JSONObject respObject = null;
            try {
                WebClient webClient = WebClient.create(jxhQuerySinglePayUrl);
                Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                        .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
                respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
            } catch (Exception e) {
                log.error("[{}] 请求{}下发查询接口异常：{}", reqVo.getBankOrderNo(), reqVo.getChannelName(), e);
                JxhResVo respVo = new JxhResVo();
                respVo.setStatusCode(BankPayStatusEnum.UN_KNOW.getValue());
                respVo.setMessage("未知异常");
                return respVo;
            }

            log.info("[{}] 请求{}下发查询接口返回原始结果：{}", reqVo.getBankOrderNo(), reqVo.getChannelName(), JsonUtil.toString(respObject));
            return JsonUtil.toBean(respObject.toString(), JxhResVo.class);
        }
    }


    @CurrentKeyPair
    public PayRespVo queryPay(PayReqVo reqVo) {
        if (!isPay) {
            try {
                log.info("[{}]君享汇支付测试,测试睡眠时间，时长：{}s", reqVo.getPlatTrxNo(), timeOut);
                TimeUnit.SECONDS.sleep(timeOut);
                log.info("[{}]君享汇支付测试,不进行真实打款操作", reqVo.getPlatTrxNo());
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return getTestPayRespVo(reqVo);
        } else {
            String signKey = getSignKey(reqVo);
            // 封装请求参数并算签
            TreeMap<String, Object> map = fillQuerySinglePayParam(reqVo, signKey);

            // 请求
            log.info("[{}] 请求君享汇下发查询接口参数：{}", reqVo.getBankOrderNo(), JsonUtil.toString(map));
            JSONObject respObject = null;
            try {
                WebClient webClient = WebClient.create(jxhQuerySinglePayUrl);
                Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                        .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
                respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
            } catch (Exception e) {
                log.info("[{}] 请求君享汇下发查询接口异常：", reqVo.getBankOrderNo(), e);
                PayRespVo respVo = new PayRespVo();
                respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
            }

            log.info("[{}] 请求君享汇下发查询接口返回原始结果：{}", reqVo.getBankOrderNo(), JsonUtil.toString(respObject));
            return fillQuerySinglePayRespVo(respObject, signKey);
        }
    }

    private JxhResVo getTestJxhResVo(PayReqVo reqVo) {
        JxhResVo payRespVo = new JxhResVo();

        String orderNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.TEXT_JXH_SEQ.getPrefix(),
                SequenceBizKeyEnum.TEXT_JXH_SEQ.getKey(),
                SequenceBizKeyEnum.TEXT_JXH_SEQ.getWidth());
        ResData data = new ResData();
        data.setStatus(AcUtil.getRandomNumber(299, 205, 201));
        if (data.getStatus().equals(ResStatus.FAIL)) {
            data.setErrorDesc("随机测试失败");
        }
        data.setPlatformSerialNo(orderNo);
        data.setMerchantOrderNo(reqVo.getBankOrderNo());

        payRespVo.setData(data);
        payRespVo.setMessage("");
        payRespVo.setStatusCode(2001);
        log.info("[{}]君享汇支付回查测试，模拟回查成功 回查结果为：{}", reqVo.getPlatTrxNo(), JsonUtil.toString(payRespVo));
        return payRespVo;
    }

    private PayRespVo getTestPayRespVo(PayReqVo reqVo) {
        PayRespVo payRespVo = new PayRespVo();
        payRespVo.setBankPayStatus(AcUtil.getRandomNumber(100, 101, 102));
        payRespVo.setBizMsg("");
        if (payRespVo.getBankPayStatus() == 101) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发起请求随机测试失败");
        }
        payRespVo.setBizCode("");
        payRespVo.setBankOrderNo(reqVo.getBankOrderNo());
        payRespVo.setBankTrxNo("");
        return payRespVo;
    }

    public PayRespVo fillQuerySinglePayRespVo(JSONObject respObject, String signKey) {
        PayRespVo respVo = new PayRespVo();
        HashMap<String, Object> map = JsonUtil.toBean(String.valueOf(respObject), HashMap.class);
        String statusCode = Convert.toStr(map.get("statusCode"));
        if (!SignUtil.verifySinglePay(map, signKey, SignUtil.querySingleVerifyKeyOrderBy)) {
            throw BanklinkExceptions.VERIFY_SIGN_FAIL.newWithErrMsg("验证签名失败");
        }

        if ("2002".equals(statusCode)) {
            // 受理失败
            respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            respVo.setBizCode(statusCode);
            respVo.setBizMsg(Convert.toStr(map.get("message")));
            return respVo;
        } else if ("2001".equals(statusCode)) {
            // 默认为订单已创建，等待下次查询
            ResStatus status = ResStatus.PROCESSING;
            JxhResVo resVo = JsonUtil.toBean(respObject.toString(), JxhResVo.class);
            if (ObjectUtil.isNotEmpty(resVo.getData().getStatus())) {
                status = resVo.getData().getStatus();
            }
            switch (status) {
                case SUCCESS:
                    // 明确成功
                    respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
                    // 渠道流水号
                    respVo.setBankTrxNo(resVo.getData().getPlatformSerialNo());
                    // 平台订单号
                    respVo.setBankOrderNo(resVo.getData().getMerchantOrderNo());
                    break;
                case FAIL:
                    // 明确失败
                    String errorCode = resVo.getData().getErrorCode();
                    if("".equals(errorCode)){
                        respVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()));
                    }
                    respVo.setBizCode(errorCode);
                    respVo.setBizMsg(resVo.getData().getErrorDesc());
                    respVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
                    break;
                default:
                    // 处理中
                    respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
                    break;
            }
        } else {
            respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
        }
        return respVo;
    }

    private TreeMap<String, Object> fillQuerySinglePayParam(PayReqVo reqVo, String signKey) {

        TreeMap<String, Object> dataMap = new TreeMap<>();
        // 商户号
        dataMap.put("userNo", reqVo.getChannelMchNo());
        dataMap.put("merchantOrderNo", reqVo.getBankOrderNo());
        dataMap.put("hmac", SignUtil.singlePaySign(dataMap, signKey, SignUtil.querySingleKeyOrderBy));
        return dataMap;
    }

    private TreeMap<String, Object> fillApplyAcountParam(ApplyAccountReq reqVo) throws Exception {
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("method", reqVo.getMethod());
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "2");
        map.put("mch_no", reqVo.getChannelMchNo());

        TreeMap<String, Object> dataMap = new TreeMap<>();
        dataMap.put("payee_account_nickname", reqVo.getPayeeAccountNickname());
        dataMap.put("payer_account_name", reqVo.getPayerAccountName());
//        dataMap.put("payer_account_no", reqVo.getPayerAccountNo());
//        dataMap.put("payee_account_no", reqVo.getPayeeAccountNo());

        map.put("data", JSON.toJSONString(dataMap));

        // 算签
        map.put("sign", SignUtil.rsaGenSign_2_0(map, reqVo.getPlatPrivateKey()));
        return map;
    }


    private TreeMap<String, Object> fillEditApplyAcountParam(ApplyAccountEditReq reqVo) throws Exception {
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("method", reqVo.getMethod());
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "2");
        map.put("mch_no", reqVo.getChannelMchNo());

        TreeMap<String, Object> dataMap = new TreeMap<>();
        dataMap.put("payee_account_nickname", reqVo.getPayeeAccountNickname());
        dataMap.put("payer_account_name", reqVo.getPayerAccountName());
        dataMap.put("payer_account_no", reqVo.getPayerAccountNo());
        dataMap.put("payee_account_no", reqVo.getPayeeAccountNo());

        map.put("data", JSON.toJSONString(dataMap));

        // 算签
        map.put("sign", SignUtil.rsaGenSign_2_0(map, reqVo.getPlatPrivateKey()));
        return map;
    }

    private TreeMap<String, Object> fillQueryApplyAcountParam(ApplyAccountQueryReq reqVo) throws Exception {
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("method", reqVo.getMethod());
        map.put("version", "1.0");
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "2");
        map.put("mch_no", reqVo.getChannelMchNo());

        TreeMap<String, Object> dataMap = new TreeMap<>();
        dataMap.put("payee_account_no", reqVo.getPayeeAccountNo());

        map.put("data", JSON.toJSONString(dataMap));

        // 算签
        map.put("sign", SignUtil.rsaGenSign_2_0(map, reqVo.getPlatPrivateKey()));
        return map;
    }


    /***
     * 解析汇聚返回结果
     * @param respObject
     * @return
     */
    private JoinpayBaseResp analysisJoinpayResp(JSONObject respObject) {
        if (respObject == null) {
            return null;
        }
        String data = respObject.getString("data");
        respObject.remove("data");
        JoinpayBaseResp resp = JSONUtil.toBean(respObject.toString(), JoinpayBaseResp.class);
        if (!"SUCCESS".equals(respObject.getString("resp_code"))) {
            return resp;
        }
        if (StringUtil.isNotEmpty(data)) {
            ApplyAcountResult result = JSONUtil.toBean(data, ApplyAcountResult.class);
            resp.setData(result);
        }
        return resp;
    }

    /***
     * 处理君享汇下发异步通知
     * @param reqVo
     * @return
     */
    @CurrentKeyPair
    public SinglePayReceiveRespVo verifySinglePayAndHandleResult(PayReceiveReqVo reqVo) {
        HashMap<String, Object> map = JsonUtil.toBean(String.valueOf(reqVo.getRespContent()), HashMap.class);
        String signKey = getSignKey(reqVo);
        if (!SignUtil.verifyNotifySinglePay(map, signKey, SignUtil.verifyNotifyKeyOrderBy)) {
            log.error("君享汇代付回调验签失败，报文：{}", JsonUtil.toString(reqVo.getRespContent()));
            throw BanklinkExceptions.VERIFY_SIGN_FAIL.newWithErrMsg("验证签名失败");
        }
        SinglePayReceiveRespVo respVo = new SinglePayReceiveRespVo();
        respVo.setMerchantOrderNo(Convert.toStr(map.get("merchantOrderNo")));
        respVo.setStatus(Convert.toInt(map.get("status")));
        respVo.setErrorCode(Convert.toStr(map.get("errorCode")));
        respVo.setErrorCodeDesc(Convert.toStr(map.get("errorCodeDesc")));
        respVo.setCompleteTime(Convert.toStr(map.get("completeTime")));
        respVo.setPlatformSerialNo(Convert.toStr(map.get("platformSerialNo")));
        return respVo;
    }


    /***
     * 解析单笔代付返回的参数
     * @param respObject
     * @return
     */
    private PayRespVo fillSinglePayRespVo(JSONObject respObject, String signKey) {
        PayRespVo respVo = new PayRespVo();
        if (respObject == null) {
            respVo.setBizCode(Convert.toStr(BanklinkExceptions.REQUEST_EXCEPTION.getSysErrorCode()));
            respVo.setBizMsg(BanklinkExceptions.REQUEST_EXCEPTION.getErrMsg());
            respVo.setBankPayStatus(BankPayStatusEnum.UN_KNOW.getValue());
            return respVo;
        }
        HashMap<String, Object> map = JsonUtil.toBean(String.valueOf(respObject), HashMap.class);
        if (!SignUtil.verifySinglePay(map, signKey, SignUtil.verifyDataKeyOrderBy)) {
            // 签名不通过
            log.error("[{}]单笔下发失败，错误原因：校验渠道签名失败", respObject.getString("merchantOrderNo"));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("校验渠道签名失败");
        }
        // 2001-受理成功；2002-受理失败；2003-未知
        String statusCode = Convert.toStr(map.get("statusCode"));
        if ("2002".equals(statusCode)) {
            // 暂时返回渠道的错误码和错误信息
            log.error("[{}]单笔下发失败，错误码[{}]错误原因[{}]",
                    Convert.toStr(map.get("merchantOrderNo")), Convert.toStr(map.get("errorCode")), Convert.toStr(map.get("errorDesc")));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(respObject.getString("errorDesc"));
        } else if ("2001".equals(statusCode)) {
            respVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
        } else {
            respVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
        }
        // 渠道请求订单号无字段返回，则暂存平台订单号
        respVo.setBankOrderNo(respObject.getString("merchantOrderNo"));
        return respVo;
    }

    /***
     * 组装朝夕付的请求参数
     * @param reqVo
     * @return
     */
    private TreeMap<String, Object> fillSinglePayParam(PayReqVo reqVo, String callbackUrl, String signKey) {
        TreeMap<String, Object> dataMap = new TreeMap<>();
        // 商户号
        dataMap.put("userNo", reqVo.getChannelMchNo());
        // "普通代付：BANK_PAY_ORDINARY_ORDER
        // "朝夕付：BANK_PAY_DAILY_ORDER
        // "任意付：BANK_PAY_MAT_ENDOWMENT_ORDER
        // "组合付：BANK_PAY_COMPOSE_ORDER
        dataMap.put("productCode", "BANK_PAY_DAILY_ORDER");
        // 请求时间
        dataMap.put("requestTime", DateUtil.formatDateTime(DateUtil.date()));
        // 商户订单号
        dataMap.put("merchantOrderNo", reqVo.getBankOrderNo());
        // 收款人银行卡号
        dataMap.put("receiverAccountNoEnc", reqVo.getReceiveAccountNo());
        // 收款人姓名
        dataMap.put("receiverNameEnc", reqVo.getReceiveName());
        // 账户类型;对私账户：201,对公账户：204
        dataMap.put("receiverAccountType", Convert.toStr(reqVo.getAccountType()));
        // 联行号，对公必填
        dataMap.put("receiverBankChannelNo", reqVo.getBankChannelNo());
        // 实发金额
        dataMap.put("paidAmount", reqVo.getReceiveAmount());
        // 人民币币种填写：201
        dataMap.put("currency", "201");
        // 复核：201，不复核：202
        dataMap.put("isChecked", "202");
        // 打款备注 reqVo.getRemitRemark()
        dataMap.put("paidDesc", reqVo.getRemitRemark());
        // 代付用途 201-工资奖金;205-劳务费；208-营业款;207-资金下发
        dataMap.put("paidUse", "207");
        dataMap.put("callbackUrl", callbackUrl);
        // 取tbl_key_pair_record表的channelPublicKey字段
        dataMap.put("hmac", SignUtil.singlePaySign(dataMap, signKey, SignUtil.singlePayKeyOrderBy));
        return dataMap;
    }
}
