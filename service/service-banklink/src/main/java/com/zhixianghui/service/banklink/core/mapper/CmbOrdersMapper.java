package com.zhixianghui.service.banklink.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhixianghui.facade.banklink.entity.CmbOrders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CmbOrdersMapper extends BaseMapper<CmbOrders> {
    int updateBatch(List<CmbOrders> list);

    int updateBatchSelective(List<CmbOrders> list);

    int batchInsert(@Param("list") List<CmbOrders> list);

    int insertOrUpdate(CmbOrders record);

    int insertOrUpdateSelective(CmbOrders record);
}
