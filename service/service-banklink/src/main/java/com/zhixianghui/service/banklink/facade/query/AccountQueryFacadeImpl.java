
package com.zhixianghui.service.banklink.facade.query;

import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.banklink.vo.account.SubMchNoQueryDto;
import com.zhixianghui.service.banklink.core.biz.account.AccountQueryBiz;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-02 10:34
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AccountQueryFacadeImpl implements AccountQueryFacade {

    private final AccountQueryBiz accountQueryBiz;

    @Override
    public String getAmount(AmountQueryDto amountQueryDto) {
        return accountQueryBiz.getAmount(amountQueryDto);
    }

    @Override
    public Map<String, String> getMainstayAmount(MainstayAmountQueryDto mainstayAmountQueryDto) {
        return accountQueryBiz.getMainstayAmount(mainstayAmountQueryDto);
    }

    @Override
    public String getSubMchNo(SubMchNoQueryDto subMchNoQueryDto) {
        return accountQueryBiz.getSubMchNo(subMchNoQueryDto);
    }


}
