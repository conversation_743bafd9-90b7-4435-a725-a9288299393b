package com.zhixianghui.service.banklink.request.pay;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.exception.BanklinkExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.pay.QueryPayOrderReqVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.facade.trade.service.WxQueryFacade;
import com.zhixianghui.facade.trade.utils.WxUtil;
import com.zhixianghui.facade.trade.vo.WxPayQueryVo;
import com.zhixianghui.facade.trade.vo.pay.WxPayParam;
import com.zhixianghui.facade.trade.vo.pay.WxPayResVo;
import com.zhixianghui.service.banklink.request.wxpay.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @ClassName WxPayBiz
 * @Description TODO
 * @Date 2021/12/3 14:19
 */
@Service
@Slf4j
public class WxPayBiz {

    @Autowired
    private WxPayService wxPayService;
    @Reference(retries = -1)
    private WxQueryFacade wxQueryFacade;
    @Reference
    private RecordItemFacade recordItemFacade;
    @Value("${wx.isPay}")
    private boolean isPay;
    @Value("${wx.timeOut}")
    private Integer timeOut;


    public PayRespVo pay(PayReqVo reqVo) throws BizException {
        if (!isPay) {
            try {
                log.info("[{}]微信支付测试,测试睡眠时间，时长：{}s", reqVo.getPlatTrxNo(), timeOut);
                TimeUnit.SECONDS.sleep(timeOut);
                log.info("[{}]微信支付测试,不进行真实打款操作", reqVo.getPlatTrxNo());
                return getTestPayRespVo(reqVo);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        WxResVo wxResVo = wxPayService.transfer(reqVo);
        String responseBody = wxResVo.getResponBody();
        PayRespVo payRespVo = new PayRespVo();
        if (wxResVo.isSuccess()) {
            log.info("[微信支付接口调用成功: {}]==>微信返回调用结果:{}", reqVo.getPlatTrxNo(), responseBody);
            Map<String, String> res = JSONObject.parseObject(responseBody, Map.class);
            payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
            payRespVo.setBizCode("");
            payRespVo.setBizMsg("");
            payRespVo.setBankOrderNo(reqVo.getBankOrderNo());
            payRespVo.setBankTrxNo(res.get("out_batch_no"));
//        } else if(wxResVo.isNeedRetryFail()){
//            Map<String, Object> map = JsonUtil.toBean(wxResVo.getResponBody(), Map.class);
//            log.error("微信支付失败，微信接口返回需要重试，[{}]微信接口调用异常，状态码：{},原因：{}", reqVo.getPlatTrxNo(), wxResVo.getCode(), String.valueOf(map.get("message")));
//            throw new WxRepBizException(String.valueOf(map.get("message")),true);
        } else if (wxResVo.isSystemError()) {
            log.info("[微信支付接口调用系统错误，可能受理了转账单: {}]==>微信返回调用结果:{}", reqVo.getPlatTrxNo(), responseBody);
            Map<String, String> res = JSONObject.parseObject(responseBody, Map.class);
            payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
            payRespVo.setBizCode("");
            payRespVo.setBizMsg("");
            payRespVo.setBankOrderNo(reqVo.getBankOrderNo());
            payRespVo.setBankTrxNo(res.get("out_batch_no"));
        } else {
            Map<String, Object> map = JsonUtil.toBean(wxResVo.getResponBody(), Map.class);
            log.error("微信支付失败，[{}]微信接口调用异常，状态码：{},原因：{}", reqVo.getPlatTrxNo(), wxResVo.getCode(), String.valueOf(map.get("message")));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(String.valueOf(map.get("message")));
        }
        return payRespVo;
    }


    private PayRespVo getTestPayRespVo(PayReqVo reqVo) {
        PayRespVo payRespVo = new PayRespVo();
        payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
        payRespVo.setBizCode("");
        payRespVo.setBizMsg("");
        payRespVo.setBankOrderNo(reqVo.getBankOrderNo());
        payRespVo.setBankTrxNo("");
        return payRespVo;
    }


    public PayRespVo queryOrder(QueryPayOrderReqVo reqVo) {
        log.info("[{}]微信订单反查，参数:{}",reqVo.getBankOrderNo(),reqVo);
        RecordItem recordItem = recordItemFacade.getByRemitPlatTrxNo(reqVo.getBankOrderNo());
        log.info("[{}]微信订单反查，recordItem:{}",reqVo.getBankOrderNo(),recordItem);
        WxResVo wxResVo = wxPayService.getOrder(recordItem.getRemitPlatTrxNo());
        log.info("[{}-{}]微信订单反查结果:{}",reqVo.getBankOrderNo(),recordItem.getPlatTrxNo(),wxResVo);
        PayRespVo payRespVo = new PayRespVo();
        payRespVo.setBankOrderNo(recordItem.getPlatTrxNo());
        if (wxResVo.isSuccess()) {
            String responBody = wxResVo.getResponBody();
            WxPayQueryVo wxPayQueryVo = JsonUtil.toBeanCamel(responBody, WxPayQueryVo.class);
            WxPayParam wxPayParam = buildParam(wxPayQueryVo);
            wxPayParam.setServiceFee(recordItem.getOrderFee().toPlainString());
            WxPayResVo resVo = wxQueryFacade.getResVo(wxPayParam, true);
            BeanUtil.copyProperties(resVo, payRespVo);
        } else if (wxResVo.isNotFound()) {
            payRespVo.setBankPayStatus(BankPayStatusEnum.FAIL.getValue());
            payRespVo.setBankTrxNo("");
            payRespVo.setBizCode(String.valueOf(BanklinkExceptions.ORDER_NOT_EXSIT.getSysErrorCode()));
            payRespVo.setBizMsg(WxUtil.getCodeDesc(wxResVo.getCode()));
        } else {
            log.error("[{}]微信订单反查错误，错误码：{},原因：{}",
                    reqVo.getBankOrderNo(), wxResVo.getCode(), WxUtil.getCodeDesc(wxResVo.getCode()));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("微信系统异常");
        }
        return payRespVo;
    }

    public WxPayParam buildParam(WxPayQueryVo wxPayQueryVo) {
        RecordItem recordItem = recordItemFacade.getByRemitPlatTrxNo(wxPayQueryVo.getOutDetailNo());
        WxPayParam wxPayParam = new WxPayParam();
        try {
            BeanUtils.copyProperties(wxPayParam, wxPayQueryVo);
        } catch (Exception e) {
            log.info("[{}]微信查询，复制属性失败，error：{}", wxPayQueryVo.getOutDetailNo(), e.getMessage());
        }
        wxPayParam.setMchName(recordItem.getEmployerName());
        wxPayParam.setEmployerNo(recordItem.getEmployerNo());
        wxPayParam.setMainstayNo(recordItem.getMainstayNo());
        wxPayParam.setRealPayerName(recordItem.getMainstayName());
        wxPayParam.setPlatTrxNo(recordItem.getPlatTrxNo());
        return wxPayParam;
    }

}
