package com.zhixianghui.service.banklink.core.biz.esign;

import cn.hutool.core.util.StrUtil;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.facade.banklink.vo.sign.AccountCreateResponse;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.*;
import com.zhixianghui.facade.banklink.vo.sign.signAuth.SignAuthReqVo;
import com.zhixianghui.service.banklink.request.sign.EsignBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service
@Slf4j
public class EsignCoreBiz {

    @Autowired
    private EsignBiz esignBiz;

    /**
     * 描述： 创建个人的e签宝账号，设置静默授权以及印章获取三步合一
     * @param signCreatePersonReqVo
     * @param autoSign
     * @return
     */
//    public AccountCreateResponse createPersonalAccount(SignCreatePersonReqVo signCreatePersonReqVo,boolean autoSign) {
//
//        AccountCreateResponse accountCreateResponse = new AccountCreateResponse();
//
//        /**
//         * 1. 检查e签宝是否存在个人账户,如果不存在则创建
//         */
//        final EsignResVo<PersonalAccountQueryResVo> accountByThirdIdRes = esignBiz.getPersonalAccountByThirdId(signCreatePersonReqVo.getThirdPartyUserId());
//
//        if (accountByThirdIdRes.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()) {
//            if (accountByThirdIdRes.getCode() != ********) {
//                throw ApiExceptions.API_SIGN_CHANNEL_FAIL.newWithErrMsg(accountByThirdIdRes.getMessage());
//            }
//        }
//
//        final PersonalAccountQueryResVo accountByThirdIdResData = accountByThirdIdRes.getData();
//        final String accountId;
//        if (accountByThirdIdResData == null || !StringUtils.equals(accountByThirdIdResData.getIdNumber(), signCreatePersonReqVo.getIdNumber())) {
//            final EsignResVo<SignCreatePersonResDataVo> personalAccount = esignBiz.createPersonByThirdPartyUserId(signCreatePersonReqVo);
//            if (personalAccount.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()) {
//                throw ApiExceptions.API_SIGN_CHANNEL_FAIL.newWithErrMsg(accountByThirdIdRes.getMessage());
//            }
//            final SignCreatePersonResDataVo accountData = personalAccount.getData();
//            accountId = accountData.getAccountId();
//        }else {
//            accountId = accountByThirdIdResData.getAccountId();
//            //如果存在，检验手机号是否变化，如果变化，则做更新操作
//            if (!StringUtils.equals(accountByThirdIdResData.getMobile(), signCreatePersonReqVo.getMobile())) {
//                UpdatePersonInfoReqVo updatePersonInfoReqVo = new UpdatePersonInfoReqVo(accountId);
//                updatePersonInfoReqVo.setMobile(signCreatePersonReqVo.getMobile());
//                updatePersonInfoReqVo.setName(signCreatePersonReqVo.getName());
//                esignBiz.updatePersonInfo(updatePersonInfoReqVo);
//            }
//        }
//
//        accountCreateResponse.setAccountId(accountId);
//
//        /**
//         * 2. 设置静默签署授权
//         */
//        if (autoSign) {
//            SignAuthReqVo signAuthReqVo = new SignAuthReqVo(accountId);
//            signAuthReqVo.setAccountId(accountId);
//            final EsignResVo<Boolean> autoSignResp = esignBiz.signAuth(signAuthReqVo);
//            if (autoSignResp.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()) {
//                throw ApiExceptions.API_SIGN_CHANNEL_FAIL.newWithErrMsg(accountByThirdIdRes.getMessage());
//            }
//        }
//
//        /**
//         * 3. 查询印章是否存在，如不存在则则创建，如存在则取回默认印章
//         */
//        final EsignResVo<PersonalSealListResVo> sealListResp = esignBiz.personalSealList(new PersonalSealListReqVo(accountId, 1, 100));
//        if (sealListResp.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()) {
//            throw ApiExceptions.API_SIGN_CHANNEL_FAIL.newWithErrMsg(accountByThirdIdRes.getMessage());
//        }
//        if (sealListResp.getData() != null && sealListResp.getData().getSeals() != null && !sealListResp.getData().getSeals().isEmpty()) {
//            final List<SealItem> seals = sealListResp.getData().getSeals();
//            SealItem targetSeal = seals.get(0);
//            for (SealItem seal : seals) {
//                if (seal.isDefaultFlag()) {
//                    targetSeal = seal;
//                }
//            }
//
//            accountCreateResponse.setSealId(targetSeal.getSealId());
//            accountCreateResponse.setSealFileKey(targetSeal.getFileKey());
//
//            try {
//                String fdfsUrl = esignBiz.downLoadFile("", StrUtil.format("seal_{}_{}.png", accountId, signCreatePersonReqVo.getIdNumber()), targetSeal.getUrl());
//                accountCreateResponse.setSealUrl(fdfsUrl);
//            } catch (IOException e) {
//                log.error("上传个人印章到文件服务器出错",e);
//                throw ApiExceptions.API_SIGN_CHANNEL_FAIL.newWithErrMsg("上传个人印章到文件服务器出错");
//            }
//
//        }else {
//            PersonalSignatureReqVo personalSignatureReqVo = new PersonalSignatureReqVo();
//            personalSignatureReqVo.setAccountId(accountId);
//            personalSignatureReqVo.setColor("RED");
//            personalSignatureReqVo.setType("RECTANGLE");
//            final EsignResVo<GetPersonalSignatureResDataVo> personalSealCreateResp = esignBiz.personalSignature(personalSignatureReqVo);
//            if (personalSealCreateResp.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()) {
//                throw ApiExceptions.API_SIGN_CHANNEL_FAIL.newWithErrMsg(accountByThirdIdRes.getMessage());
//            }
//            final GetPersonalSignatureResDataVo personalSealCreateRespData = personalSealCreateResp.getData();
//            accountCreateResponse.setSealId(personalSealCreateRespData.getSealId());
//            accountCreateResponse.setSealFileKey(personalSealCreateRespData.getFileKey());
//            try {
//                String fdfsUrl = esignBiz.downLoadFile("", StrUtil.format("seal_{}_{}.png", accountId, signCreatePersonReqVo.getIdNumber()), personalSealCreateRespData.getUrl());
//                accountCreateResponse.setSealUrl(fdfsUrl);
//            } catch (IOException e) {
//                log.error("上传个人印章到文件服务器出错",e);
//                throw ApiExceptions.API_SIGN_CHANNEL_FAIL.newWithErrMsg("上传个人印章到文件服务器出错");
//            }
//        }
//
//        return accountCreateResponse;
//    }

}
