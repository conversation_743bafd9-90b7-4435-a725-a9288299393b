package com.zhixianghui.service.banklink.facade.robot;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.service.banklink.core.biz.RobotBiz.RobotBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @ClassName RobotFacadeImpl
 * @Description TODO
 * @Date 2021/6/4 17:53
 */
@Service
public class RobotFacadeImpl implements RobotFacade {
    @Autowired
    private RobotBiz robotBiz;

    @Override
    public boolean pushMarkDownAsync(MarkDownMsg markDownMsg) throws BizException {
        return robotBiz.pushMarkDownAsync(markDownMsg);
    }

    @Override
    public boolean pushMarkDownSync(MarkDownMsg markDownMsg) throws BizException{
        return robotBiz.pushMarkDownSync(markDownMsg);
    }

    @Override
    public boolean sendFileMsg(RobotTypeEnum robotType, String fileName, String fileId) {
        String mediaId = robotBiz.uploadFile(robotType, fileName, fileId);
        robotBiz.pushFileMsgSync(mediaId, robotType);
        return true;
    }
}
