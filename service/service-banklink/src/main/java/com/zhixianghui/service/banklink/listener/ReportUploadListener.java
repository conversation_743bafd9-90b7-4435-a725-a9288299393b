package com.zhixianghui.service.banklink.listener;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.report.AltMchSignVo;
import com.zhixianghui.facade.banklink.vo.report.UploadPicReqVo;
import com.zhixianghui.service.banklink.request.report.JoinpayReportBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ReportUploadListener
 * @Description TODO
 * @Date 2023/3/10 14:58
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MessageMsgDest.TOPIC_REPORT_UPLOAD,selectorExpression = MessageMsgDest.TAG_REPORT_UPLOAD,consumeThreadMax = 5,consumerGroup = "reportUploadGroup")
public class ReportUploadListener extends BaseRocketMQListener<String> {

    @Autowired
    private JoinpayReportBiz joinpayReportBiz;

    @Override
    public void validateJsonParam(String jsonParam) {

    }

    @Override
    public void consumeMessage(String jsonParam) {
        UploadPicReqVo uploadPicReqVo = JsonUtil.toBean(jsonParam,UploadPicReqVo.class);
        try {
            AltMchSignVo altMchSignVo = new AltMchSignVo();
            altMchSignVo.setAltMchNo(uploadPicReqVo.getSubMerchantNo());
            altMchSignVo.setChannelMchNo(uploadPicReqVo.getChannelMchNo());
            altMchSignVo.setChannelNo(ChannelNoEnum.JOINPAY.name());
            Map<String, String> stringMap = joinpayReportBiz.queryPicsInfo(altMchSignVo);
            if (stringMap != null && StringUtils.equals(stringMap.get("approveStatus"), "P2000")) {
                joinpayReportBiz.modifyPic(uploadPicReqVo);
            }else {
                joinpayReportBiz.uploadPic(uploadPicReqVo);
            }


            joinpayReportBiz.altMchSign(altMchSignVo);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
