package com.zhixianghui.service.banklink.core.biz.message.sms;

import com.zhixianghui.common.statics.enums.message.SmsPlatformEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.vo.sms.SmsSendResp;
import com.zhixianghui.service.banklink.core.biz.message.resolver.TemplateResolver;
import com.zhixianghui.service.banklink.core.biz.message.sms.senders.AliyunSmsSender;
import com.zhixianghui.service.banklink.core.biz.message.sms.senders.ChuangLanSmsSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SmsBiz {
    @Autowired
    TemplateResolver templateResolver;
    @Autowired
    ChuangLanSmsSender chuangLanSmsSender;
    @Autowired
    AliyunSmsSender aliyunSmsSender;

    public SmsSendResp send(String phone, String msg, String bizKey){
        SmsSender smsSender = getSmsSender(SmsPlatformEnum.CHUANG_LAN);//默认使用创蓝发送
        return smsSender.send(phone, msg, bizKey);
    }

    private SmsSender getSmsSender(SmsPlatformEnum smsPlatform){
        if(smsPlatform == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请指定短信运营商！");
        }

        switch (smsPlatform){
            case ALI_YUN:
                return aliyunSmsSender;
            case CHUANG_LAN:
                return chuangLanSmsSender;
            default:
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未预期的短信运营商");
        }
    }
}
