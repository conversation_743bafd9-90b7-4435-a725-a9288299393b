package com.zhixianghui.service.banklink.facade.message;

import com.zhixianghui.common.statics.dto.message.SmsParamDto;
import com.zhixianghui.common.statics.enums.message.SmsPlatformEnum;
import com.zhixianghui.facade.banklink.vo.sms.SmsQueryParam;
import com.zhixianghui.facade.banklink.vo.sms.SmsQueryResp;
import com.zhixianghui.facade.banklink.vo.sms.SmsSendResp;
import com.zhixianghui.facade.banklink.service.message.SmsFacade;
import com.zhixianghui.service.banklink.core.biz.message.sms.SmsBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class SmsFacadeImpl implements SmsFacade {

    @Autowired
    SmsBiz smsBiz;

    /**
     * 短信发送接口，系统会选择默认的运营商和通用的短信模版进行发送
     * @param phone        收信人手机号
     * @param msg          短信内容
     * @return
     */
    @Override
    public SmsSendResp send(String phone, String msg, String bizKey){
        return smsBiz.send(phone, msg, bizKey);
    }

    /**
     * 短信发送接口，可指定短信运营商、短信模版类型、短信模版名称、短信模版参数 等等的参数来发送
     * @param smsParam
     * @return
     */
    @Override
    public SmsSendResp send(SmsParamDto smsParam){
        return null;
    }

    @Override
    public SmsQueryResp query(SmsPlatformEnum platform, SmsQueryParam queryParam){
        return null;
    }
}
