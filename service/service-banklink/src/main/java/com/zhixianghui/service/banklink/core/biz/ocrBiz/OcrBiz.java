package com.zhixianghui.service.banklink.core.biz.ocrBiz;

import cn.hutool.core.io.IoUtil;
import com.zhixianghui.facade.banklink.vo.ocr.BusinessLicenseOctVo;
import com.zhixianghui.facade.banklink.vo.ocr.IdcardOcrVo;
import com.zhixianghui.facade.banklink.enums.IdCardSiteEnum;
import com.zhixianghui.facade.banklink.vo.ocr.OcrRequestVo;
import com.zhixianghui.service.banklink.request.baiduai.BaiduAiService;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;

@Service
@Slf4j
public class OcrBiz {

    @Autowired
    private BaiduAiService baiduAiService;
    @Autowired
    private FastdfsClient fastdfsClient;

    public IdcardOcrVo idCardFontOcr(OcrRequestVo ocrRequestVo) {
        InputStream inputStream = fastdfsClient.downloadFile(ocrRequestVo.getFileUrl());
        byte[] readBytes = IoUtil.readBytes(inputStream);
        return baiduAiService.idCardOcr(readBytes, IdCardSiteEnum.FONT,ocrRequestVo.getMiniUserNo());
    }

    public BusinessLicenseOctVo businessLicenseOcr(OcrRequestVo ocrRequestVo) {
        InputStream inputStream = fastdfsClient.downloadFile(ocrRequestVo.getFileUrl());
        byte[] readBytes = IoUtil.readBytes(inputStream);
        return baiduAiService.businessLicenseOcr(readBytes,ocrRequestVo.getMiniUserNo());
    }
}
