package com.zhixianghui.service.banklink.listener;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.notify.MerchantNotifyParam;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.service.banklink.core.biz.RobotBiz.RobotBiz;
import com.zhixianghui.service.banklink.core.biz.message.email.EmailBiz;
import com.zhixianghui.service.banklink.core.biz.notify.MerchantNotifyBiz;
import com.zhixianghui.service.banklink.request.pay.AlipayBiz;
import com.zhixianghui.service.banklink.request.pay.CmbPayBiz;
import com.zhixianghui.starter.comp.rmq.BaseRocketMQListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MessageListener {

    final private AlipayBiz alipayBiz;

    private final EmailBiz emailBiz;
    private final MerchantNotifyBiz merchantNotifyBiz;
    private final RobotBiz robotBiz;

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_WX_WORK_ROBOT,selectorExpression = MessageMsgDest.TAG_WX_WORK_ROBOT,consumeThreadMax = 3,consumerGroup = "robotAsyncConsume")
    public class RobotMessageListener extends BaseRocketMQListener<String>{

        @Override
        public void validateJsonParam(String msg) {
            if (msg == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("markdownMsg不能为空");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            MarkDownMsg markDownMsg = JsonUtil.toBean(msg,MarkDownMsg.class);
            robotBiz.pushMarkDownSync(markDownMsg);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_SEND_EMAIL_ASYNC, consumeThreadMax = 8, consumerGroup = "emailAsyncConsume")
    public class EmailMessageListener extends BaseRocketMQListener<EmailParamDto> {

        @Override
        public void validateJsonParam(EmailParamDto emailParamDto) {
            if (emailParamDto == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("emailParamDto不能为null");
            }
        }

        @Override
        public void consumeMessage(EmailParamDto emailParamDto) {
            emailBiz.sendSync(emailParamDto);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_NOTIFY_MERCHANT, selectorExpression = MessageMsgDest.TAG_NOTIFY_MERCHANT,consumeThreadMax = 8, consumerGroup = "notifyMerchantConsumer")
    public class NotifyMerchantListener extends BaseRocketMQListener<String> {

        @Override
        public void validateJsonParam(String msg) {
            if (msg == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("MerchantNotifyParam不能为null");
            }
        }

        @Override
        public void consumeMessage(String msg) {
            MerchantNotifyParam merchantNotifyParam = JsonUtil.toBean(msg,MerchantNotifyParam.class);
            merchantNotifyBiz.sendMerchantNotify(merchantNotifyParam);
        }
    }

    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_ALIPAY_REFUND, selectorExpression = MessageMsgDest.TAG_ALIPAY_REFUND,consumeThreadMax = 8, consumerGroup = "alipayRefund")
    public class RefundFailRetryMsgListener extends BaseRocketMQListener<String> {
        @Override
        public void validateJsonParam(String jsonParam) {
            if (jsonParam == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("消息内容不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            PayReqVo payReqVo = JSONObject.parseObject(jsonParam, PayReqVo.class);
            alipayBiz.uniTransRefund(payReqVo);
//            JSONObject refundResult = alipayBiz.uniTransRefund(payReqVo);
//            if (refundResult != null && !StringUtils.equals(refundResult.getString("status"), SuccessFailEnum.SUCCESS.name())) {
//                throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("退款失败，退款订单号:["+payReqVo.getBankOrderNo()+"]");
//            }
        }
    }


    @Component
    @RocketMQMessageListener(topic = MessageMsgDest.TOPIC_CMB_REFUND, selectorExpression = MessageMsgDest.TAG_CMB_REFUND,consumeThreadMax = 1, consumerGroup = "cmbRefund")
    public class CmbRefundListener extends BaseRocketMQListener<String> {
        @Autowired
        private CmbPayBiz cmbPayBiz;

        @Override
        public void validateJsonParam(String jsonParam) {
            if (jsonParam == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("消息内容不能为null");
            }
        }

        @Override
        public void consumeMessage(String jsonParam) {
            PayReqVo payReqVo = JSONObject.parseObject(jsonParam, PayReqVo.class);
            cmbPayBiz.refund(payReqVo);
        }
    }

}
