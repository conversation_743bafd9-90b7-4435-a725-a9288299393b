package com.zhixianghui.service.banklink.request.account;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.banklink.vo.account.SubMchNoQueryDto;
import com.zhixianghui.service.banklink.annotation.CurrentKeyPair;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import com.zhixianghui.service.banklink.utils.joinpay.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-02 10:37
 **/
@Service
@Slf4j
public class JoinpayAccountQueryBiz {

    @Value("${report.joinpayAllocFundsUrl}")
    private String joinpayAllocFundsUrl;
    @Value("${report.joinpay.mainstayBalanceUrl}")
    private String mainstayBalanceUrl;

    @CurrentKeyPair
    public String getAmount(AmountQueryDto amountQueryDto){
        TreeMap<String, Object> map = fillQueryAmountParam(amountQueryDto);
        // 请求
        log.info("父商户:[{}] 分账方:[{}] 请求汇聚分账方查询余额接口参数：{}", amountQueryDto.getChannelMchNo(), amountQueryDto.getSubMerchantNo(), JsonUtil.toString(map));
        JSONObject respObject;
        try{
            WebClient webClient = WebClient.create(joinpayAllocFundsUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject =mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        }catch (Exception e){
            log.info("父商户:[{}] 分账方:[{}] 请求汇聚分账方查询余额接口异常", amountQueryDto.getChannelMchNo(), amountQueryDto.getSubMerchantNo(),e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求汇聚分账方查询余额接口异常");
        }
        if(respObject == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("汇聚查询接口异常,返回为空");
        }
        JSONObject responseData = respObject.getJSONObject("data");
        if(responseData == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("汇聚查询接口异常,data为空");
        }
        log.info("请求汇聚分账方查询余额接口返回原始结果：{}", JsonUtil.toString(respObject));
        return responseData.getString("alt_total_balance");
    }

    @CurrentKeyPair
    public Map<String, String> getMainstayAmount(MainstayAmountQueryDto amountQueryDto) {

        TreeMap<String, String> map = new TreeMap<>();
        map.put("userNo", amountQueryDto.getChannelMchNo());
        String hmac = SignUtil.genSign_1_0(map, amountQueryDto.getKeyPairRecord().getMchPrivateKeyDecrypt());
        map.put("hmac", hmac);

        // 请求
        log.info("父商户:[{}] 请求汇聚查询余额接口参数：{}", amountQueryDto.getChannelMchNo(), JsonUtil.toString(map));
        JSONObject respObject;
        try{
            WebClient webClient = WebClient.create(mainstayBalanceUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject =mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        }catch (Exception e){
            log.info("父商户:[{}] 请求汇聚查询余额接口异常", amountQueryDto.getChannelMchNo(),e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求汇聚查询余额接口异常");
        }
        if(respObject == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("汇聚查询接口异常,返回为空");
        }
        JSONObject responseData = respObject.getJSONObject("data");
        if(responseData == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("汇聚查询接口异常,data为空");
        }
        log.info("请求汇聚查询余额接口返回原始结果：{}", JsonUtil.toString(respObject));

        Map<String, String> balanceData = new HashMap<>();
        balanceData.put("useAbleSettAmount",responseData.getString("useAbleSettAmount"));
        balanceData.put("frozenAmount",responseData.getString("availableSettAmountFrozen"));

        return balanceData;
    }

    private TreeMap<String, Object> fillQueryAmountParam(AmountQueryDto amountQueryDto) {
        //组装参数实体
        Map<String, Object> data = Maps.newHashMap();
        data.put("alt_mch_no", amountQueryDto.getSubMerchantNo());
        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altAccount.get");
        map.put("version", "1.1");
        map.put("data", JSON.toJSON(data));
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", amountQueryDto.getChannelMchNo());
        map.put("sign", SignUtil.genSign_2_0(map, amountQueryDto.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }

    @CurrentKeyPair
    public String getAccount(SubMchNoQueryDto subMchNoQueryDto) {
        TreeMap<String, Object> map = fillQueryAccountParam(subMchNoQueryDto);
        // 请求
        log.info("父商户:[{}] 分账方登录名:[{}] 请求汇聚分账方查询商户信息参数：{}",
                subMchNoQueryDto.getChannelMchNo(), subMchNoQueryDto.getEmployerNo() + subMchNoQueryDto.getMainstayNo(), JsonUtil.toString(map));

        JSONObject respObject;
        try{
            WebClient webClient = WebClient.create(joinpayAllocFundsUrl);
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(map))).retrieve().bodyToMono(JSONObject.class);
            respObject =mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        }catch (Exception e){
            log.info("父商户:[{}] 分账方登录名:[{}] 请求汇聚分账方查询商户信息参数接口异常", subMchNoQueryDto.getChannelMchNo(), subMchNoQueryDto.getEmployerNo() + subMchNoQueryDto.getMainstayNo(),e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求汇聚分账方查询商户信息参数接口异常");
        }
        if(respObject == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("汇聚分账方查询接口异常,返回为空");
        }
        JSONObject responseData = respObject.getJSONObject("data");
        if(responseData == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("汇聚分账方查询接口异常,data为空");
        }
        log.info("请求汇聚分账方查询商户信息接口返回原始结果：{}", JsonUtil.toString(respObject));
        return responseData.getString("alt_mch_no");
    }

    private TreeMap<String, Object> fillQueryAccountParam(SubMchNoQueryDto subMchNoQueryDto) {
        //组装参数实体
        Map<String, Object> data = Maps.newHashMap();
        data.put("alt_login_name", subMchNoQueryDto.getEmployerNo() + subMchNoQueryDto.getMainstayNo());
        TreeMap<String,Object> map = Maps.newTreeMap();
        map.put("method", "altmch.query");
        map.put("version", "1.0");
        map.put("data", JSON.toJSON(data));
        map.put("rand_str", RandomUtil.get32LenStr());
        map.put("sign_type", "1");
        map.put("mch_no", subMchNoQueryDto.getChannelMchNo());
        map.put("sign", SignUtil.genSign_2_0(map, subMchNoQueryDto.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        return map;
    }
}
