package com.zhixianghui.service.banklink.utils.hema;

import org.springframework.util.Base64Utils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 * @ClassName HemaRSAUtil
 * @Description TODO
 * @Date 2023/10/10 11:24
 */
public class HemaRSAUtil {

    /**
     * 通过公钥对文明进行加密
     *
     * @param content         明文内容
     * @param publicKeyBase64 公钥base64
     * @return
     */
    public static String encryptByPublicKey(String content, String publicKeyBase64) {
        try {
            // 需要加密的内容字节
            byte[] srcBytes = content.getBytes();
            // Cipher负责完成加密或解密工作，基于RSA
            Cipher cipher = Cipher.getInstance("RSA");
            // 初始化加密模式
            cipher.init(Cipher.ENCRYPT_MODE, getPublicKey(publicKeyBase64));
            //分段加密
            byte[] resultBytes = encryptCipherDoFinal(cipher, srcBytes);
            return Base64Utils.encodeToString(resultBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取公钥对象
     *
     * @param publicKeyBase64 公钥base64
     * @return 公钥对象
     */
    public static PublicKey getPublicKey(String publicKeyBase64) throws InvalidKeySpecException, NoSuchAlgorithmException {
        //公钥base64转字节
        byte[] decode = Base64Utils.decodeFromString(publicKeyBase64);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        //构造公钥对象
        return keyFactory.generatePublic(new X509EncodedKeySpec(decode));
    }

    /**
     * 最大长度53，分段加密
     */
    private static byte[] encryptCipherDoFinal(Cipher cipher, byte[] srcBytes) throws IllegalBlockSizeException, BadPaddingException, IOException {

        int inputLen = srcBytes.length;
        int offLen = 0;//偏移量
        int i = 0;
        ByteArrayOutputStream bops = new ByteArrayOutputStream();
        while (inputLen - offLen > 0) {
            byte[] cache;
            if (inputLen - offLen > 53) {
                cache = cipher.doFinal(srcBytes, offLen, 53);
            } else {
                cache = cipher.doFinal(srcBytes, offLen, inputLen - offLen);
            }
            bops.write(cache);
            i++;
            offLen = 53 * i;
        }
        return bops.toByteArray();
    }

    /**
     * 通过公钥对密文进行解密
     *
     * @param ciphertext   密文
     * @param publicKeyBase64 公钥base64
     * @return
     */
    public static String decryptByPublicKey(String ciphertext, String publicKeyBase64) {
        try {
            // Cipher负责完成加密或解密工作，基于RSA
            Cipher deCipher = Cipher.getInstance("RSA");
            // 初始化解密模式
            deCipher.init(Cipher.DECRYPT_MODE, getPublicKey(publicKeyBase64));
            byte[] decBytes = decryptCipherDoFinal(deCipher, Base64Utils.decodeFromString(ciphertext));
            return new String(decBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 最大长度64，分段解密
     */
    private static byte[] decryptCipherDoFinal(Cipher cipher, byte[] srcBytes) throws IllegalBlockSizeException, BadPaddingException, IOException {
        int inputLen = srcBytes.length;
        int offLen = 0;
        int i = 0;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        while (inputLen - offLen > 0) {
            byte[] cache;
            if (inputLen - offLen > 64) {
                cache = cipher.doFinal(srcBytes, offLen, 64);
            } else {
                cache = cipher.doFinal(srcBytes, offLen, inputLen - offLen);
            }
            byteArrayOutputStream.write(cache);
            i++;
            offLen = 64 * i;
        }
        return byteArrayOutputStream.toByteArray();
    }
}
