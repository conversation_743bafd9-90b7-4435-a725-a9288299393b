package com.zhixianghui.service.banklink.facade.joinpay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.joinpay.JoinPayFacade;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.banklink.vo.withdraw.SecurityVo;
import com.zhixianghui.facade.banklink.vo.withdraw.WithdrawQueryVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.trade.constant.JoinPayConstant;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.service.banklink.config.JoinPayConfig;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import com.zhixianghui.service.banklink.request.account.JoinpayAccountQueryBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月04日 09:44:00
 */
@Service
@Slf4j
public class JoinPayImpl implements JoinPayFacade {

    @Autowired
    private JoinPayConfig joinPayConfig;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private OrderFacade orderFacade;
    @Autowired
    private JoinpayAccountQueryBiz joinpayAccountQueryBiz;

    @Override
    public JSONObject withdraw(SecurityVo securityVo) {
        securityVo.setCallbackUrl(joinPayConfig.getCallBackFundUrl());
        TreeMap<String, Object> param = securityVo.getParam();

        log.info("[{}]开始银行卡提现：{}", securityVo.getMchNo(), JSONObject.toJSONString(param));
        JSONObject respObject;
        try {
            WebClient webClient = WebClient.create(joinPayConfig.getFundUrl());
            Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromObject(JSON.toJSONString(param))).retrieve().bodyToMono(JSONObject.class);
            respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        } catch (Exception e) {
            log.info("[{}]银行卡提现异常：{}", securityVo.getMchNo(), e);
            return null;
        }
        log.info("[{}]银行卡提现结束: 返回结果{}", securityVo.getMchNo(), JSONObject.toJSONString(respObject));
        Map<String, Object> map = respObject.getInnerMap();
        if(!map.get("resp_code").equals(JoinPayConstant.SUCCESS_RESP_CODE)){
            String dataStr = JsonUtil.toString(map.get("data"));
            Map<String,Object> data=JsonUtil.toBean(dataStr,Map.class);
            orderFacade.updateWithdrawRecord(securityVo.getMchOrderNo(), SuccessFailEnum.FAIL,
                    String.valueOf(data.get("biz_msg")));
        }
        return respObject;
    }


    @Override
    public JSONObject query(WithdrawQueryVo withdrawQueryVo) {
        final EmployerAccountInfo employerAccountInfo =
                employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(
                        withdrawQueryVo.getEmployerNo(), withdrawQueryVo.getMainstayNo(), withdrawQueryVo.getChannelType());

        SecurityVo securityVo = new SecurityVo();
        securityVo.setMchNo(employerAccountInfo.getParentMerchantNo());
        securityVo.setMchOrderNo(withdrawQueryVo.getMchOrderNo());
        securityVo.setCallbackUrl(joinPayConfig.getCallBackFundUrl());
        TreeMap<String, Object> param = securityVo.getQuery();
        WebClient webClient = WebClient.create(joinPayConfig.getFundUrl());
        Mono<JSONObject> mono = webClient.post().contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromObject(JSON.toJSONString(param))).retrieve().bodyToMono(JSONObject.class);
        JSONObject block = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        log.info("[{}]汇聚回查接口，返回结果:{}",withdrawQueryVo.getMchOrderNo(),block.toJSONString());
        return block;
    }

    @Override
    public Map<String, String> getMainstayAmount(MainstayAmountQueryDto amountQueryDto) {
        return joinpayAccountQueryBiz.getMainstayAmount(amountQueryDto);
    }
}