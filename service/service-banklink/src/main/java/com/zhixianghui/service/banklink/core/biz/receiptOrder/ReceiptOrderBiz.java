package com.zhixianghui.service.banklink.core.biz.receiptOrder;

import cn.hutool.extra.spring.SpringUtil;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ReceiptOrderBiz
 * @Description TODO
 * @Date 2023/7/4 16:12
 */
@Service
public class ReceiptOrderBiz {

    private static Map<String,Class<? extends AbstractReceiptOrderHandler>> beanMap = new HashMap<>();

    static {
        beanMap.put(ChannelNoEnum.ALIPAY.name(),AlipayReceiptOrderHandler.class);
        beanMap.put(ChannelNoEnum.WXPAY.name(),WxReceiptOrderHandler.class);
        beanMap.put(ChannelNoEnum.CMB.name(),CmbReceiptOrderHandler.class);
    }


    private AbstractReceiptOrderHandler getHandler(String channelNo){
        return SpringUtil.getBean(beanMap.get(channelNo));
    }

    public Map<String, Object> applyOrder(String channelNo, String remitPlatTrxNo, String employerNo, String mainstayNo, Integer channelType) {
        return getHandler(channelNo).applyOrder(remitPlatTrxNo,employerNo,mainstayNo,channelType);
    }

    public void downloadAndSendReceipt(Map<String, Object> paramMap) {
        String channelNo = (String) paramMap.get("channelNo");
        getHandler(channelNo).downloadAndSendReceipt(paramMap);
    }
}
