package com.zhixianghui.service.banklink.request.pay;

import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.banklink.vo.yishui.YiResponse;
import com.zhixianghui.facade.banklink.vo.yishui.req.ChangeOrderReqVo;
import com.zhixianghui.facade.banklink.vo.yishui.req.RequestVo;
import com.zhixianghui.service.banklink.core.biz.YishuiBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class YishuiPayBiz {

    @Autowired
    private YishuiBiz yishuiBiz;

    public PayRespVo pay(PayReqVo payReqVo) {

        //查询订单号
        YiResponse<Map<String, Object>> orderDetailResponse = yishuiBiz.findOrderFromRequestNo(new RequestVo<String>()
                .setParam(payReqVo.getPlatTrxNo())
                .setUser_name(payReqVo.getEmployerNo())
                .setEnterprise_sn(payReqVo.getSubMerchantNo()));

        if (orderDetailResponse.getCode() != 200) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg(orderDetailResponse.getMsg());
        }
        Map<String, Object> detailData = orderDetailResponse.getData();
        String batchId = String.valueOf(detailData.get("enterprise_order_id"));
        String bankTrxNo = String.valueOf(detailData.get("enterprise_order_ext_id"));

        RequestVo<ChangeOrderReqVo> changeOrderReqVoRequestVo = new RequestVo<>();
        ChangeOrderReqVo changeOrderReqVo = new ChangeOrderReqVo();
        changeOrderReqVo.setEnterprise_order_id(batchId);
        changeOrderReqVo.setRemarks(payReqVo.getRemitRemark());
        changeOrderReqVo.setStatus("1");
        changeOrderReqVoRequestVo.setEnterprise_sn(payReqVo.getSubMerchantNo())
                .setParam(changeOrderReqVo);
        YiResponse yiResponse = yishuiBiz.changeOrderStatus(changeOrderReqVoRequestVo);
        if (yiResponse.getCode() != 200) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(yiResponse.getMsg());
        }

        PayRespVo payRespVo = new PayRespVo();
        payRespVo.setBankPayStatus(BankPayStatusEnum.PROCESS.getValue());
        payRespVo.setBizCode("");
        payRespVo.setBizMsg("");
        payRespVo.setBankOrderNo(payReqVo.getBankOrderNo());
        payRespVo.setBankTrxNo(bankTrxNo);
        return payRespVo;
    }

}
