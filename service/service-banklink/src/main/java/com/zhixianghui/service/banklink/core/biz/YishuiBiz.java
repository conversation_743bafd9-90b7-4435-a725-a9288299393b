package com.zhixianghui.service.banklink.core.biz;

import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.vo.yishui.*;
import com.zhixianghui.facade.banklink.vo.yishui.req.*;
import com.zhixianghui.service.banklink.request.yishui.YishuiService;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class YishuiBiz {
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private YishuiService yishuiService;
    @Autowired
    private RedisLock redisLock;


    /**
     * 登录获取token并存入redis
     * @param username
     * @param password
     * @param enterprise_sn
     * @return
     */
    public String getYishuiToken(String username, String password, String enterprise_sn) {

        String redisToken = redisClient.get("yishui:" + "token:" + enterprise_sn + "-" + username);
        if (StringUtils.isNotBlank(redisToken)) {
            log.info("缓存获取到[{}-{}]的token", enterprise_sn, username);
            return redisToken;
        }

        RLock rLock = redisLock.tryLock(enterprise_sn + "-" + username, 5000, 5000);
        if (rLock == null) {
            log.info("[登录环节: {}]==>获取锁失败，直接丢弃",enterprise_sn + "-" + username);
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }

        String loginToken = null;
        try {
            String redisToken1 = redisClient.get("yishui:" + "token:" + enterprise_sn + "-" + username);
            if (StringUtils.isNotBlank(redisToken1)) {
                log.info("缓存获取到[{}-{}]的token", enterprise_sn, username);
                return redisToken1;
            }

            loginToken = yishuiService.login(username, password, enterprise_sn);
            if (StringUtils.isBlank(loginToken)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("易税登录异常，获取到的token为空");
            }

            boolean set = redisClient.set("yishui:" + "token:" + enterprise_sn + "-" + username, loginToken, 540);
            if (!set) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("易税token保存到Redis出错:" + enterprise_sn + "-" + username);
            }
        }finally {
            redisLock.unlock(rLock);
        }
        if (loginToken == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无法登录获取到token");
        }
        return loginToken;
    }

    public String getAgentToken() {
        String redisToken = redisClient.get("yishui:agentToken:hjzx");
        if (StringUtils.isNotBlank(redisToken)) {
            log.info("缓存获取到易税渠道的token");
            return redisToken;
        }

        RLock rLock = redisLock.tryLock("agentToken", 5000, 5000);
        if (rLock == null) {
            log.info("[登录环节: 易税渠道]==>获取锁失败，直接丢弃");
            throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("并发请求拦截,请重试");
        }

        String loginToken = null;
        try {
            String redisToken1 = redisClient.get("yishui:agentToken:hjzx");
            if (StringUtils.isNotBlank(redisToken1)) {
                log.info("缓存获取到易税渠道的token");
                return redisToken1;
            }

            loginToken = yishuiService.agentLogin();
            if (StringUtils.isBlank(loginToken)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("易税渠道登录异常，获取到的token为空");
            }

            boolean set = redisClient.set("yishui:agentToken:hjzx", loginToken, 540);
            if (!set) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("易税渠道token保存到Redis出错");
            }
        }finally {
            redisLock.unlock(rLock);
        }
        if (loginToken == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("渠道无法登录获取到token");
        }
        return loginToken;
    }

    public String getUploadUrl() {
        return yishuiService.getUploadUrl();
    }

    public YiResponse<Map<String, String>> saveEnterpriseDetail(SaveEnterpriseDetailVo saveVo) {
        String agentToken = this.getAgentToken();
        return yishuiService.saveEnterpriseDetail(agentToken, saveVo);
    }

    public YiResponse saveEnterpriseDraftRate(SaveEnterpriseDraftRate draftRate) {
        String agentToken = this.getAgentToken();
        return yishuiService.saveEnterpriseDraftRate(agentToken, draftRate);
    }

    public YiResponse<Map<String, Object>> getEnterpriseDetail(String enterpriseId) {
        String agentToken = this.getAgentToken();
        return yishuiService.getEnterpriseDetail(agentToken, enterpriseId);
    }

    public YiResponse<Map<String, Object>> getEnterpriseDetailByName(String enterpriseName) {
        String agentToken = this.getAgentToken();
        return yishuiService.getEnterpriseDetailByName(agentToken, enterpriseName);
    }

    public YiResponse<YishuiAddEmpVo> addEmployee(RequestVo<AddEmpVo> requestVo,int channelType) {
        if (channelType == ChannelTypeEnum.BANK.getValue()) {
            YiResponse<YishuiAddEmpVo> yishuiAddEmpVoYiResponse = yishuiService.addEmployee(requestVo);
            return yishuiAddEmpVoYiResponse;
        } else if (channelType == ChannelTypeEnum.ALIPAY.getValue()) {
            YiResponse<YishuiAddEmpVo> yishuiAddEmpVoYiResponse = yishuiService.addEmployeeAlipay(requestVo);
            return yishuiAddEmpVoYiResponse;
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该供应商暂不支持该通道类型");
        }
    }

    public YiResponse<YishuiContractInfoVo> contractInfo(RequestVo<String> requestVo) {
        YiResponse<YishuiContractInfoVo> yishuiContractInfoVoYiResponse = yishuiService.contractInfo(requestVo);
        return yishuiContractInfoVoYiResponse;
    }

    public YiResponse<YishuiContractListVo> contractList(RequestVo<ContractListQueryVo> requestVo) {
        YiResponse<YishuiContractListVo> yishuiContractListVoYiResponse = yishuiService.contractList(requestVo);
        return yishuiContractListVoYiResponse;
    }

    public YiResponse<YishuiFastIssuing> fastIssuing(RequestVo<FastIssuingVo> requestVo) {
        YiResponse<YishuiFastIssuing> yishuiFastIssuingYiResponse = yishuiService.fastIssuing(requestVo);
        return yishuiFastIssuingYiResponse;
    }

    public YiResponse changeOrderStatus(RequestVo<ChangeOrderReqVo> requestVo) {
        YiResponse yiResponse = yishuiService.changeOrderStatus(requestVo);
        return yiResponse;
    }

    public YiResponse<Map<String, Object>> findOrderFromRequestNo(RequestVo<String> requestVo) {
        YiResponse<Map<String, Object>> yiResponse = yishuiService.findOrderFromRequestNo(requestVo);
        return yiResponse;
    }

    public YiResponse<YishuiMyAccountVo> myAccount(RequestVo requestVo) {
        YiResponse<YishuiMyAccountVo> yishuiMyAccountVoYiResponse = yishuiService.myAccount(requestVo);
        return yishuiMyAccountVoYiResponse;
    }

    public YiResponse contractSave(RequestVo<ContractSaveVo> requestVo) {
        YiResponse yiResponse = yishuiService.contractSave(requestVo);
        return yiResponse;
    }

    public YiResponse<String> addBank(RequestVo<AddBankVo> requestVo) {
        YiResponse<String> yiResponse = yishuiService.addBank(requestVo);
        return yiResponse;
    }
}
