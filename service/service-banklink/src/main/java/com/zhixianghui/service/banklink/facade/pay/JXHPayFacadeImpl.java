package com.zhixianghui.service.banklink.facade.pay;

import com.zhixianghui.facade.banklink.service.pay.JXHPayFacade;
import com.zhixianghui.facade.banklink.vo.jxh.JxhResVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.service.banklink.request.pay.JxhPayBiz;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;

@Service
public class JXHPayFacadeImpl implements JXHPayFacade {
    @Resource
    private JxhPayBiz jxhPayBiz;

    @Override
    public JxhResVo querySinglePay(PayReqVo reqVo) {
        return jxhPayBiz.querySinglePay(reqVo);
    }


    @Override
    public PayRespVo withdraw(PayReqVo reqVo) {
        return jxhPayBiz.withdraw(reqVo);
    }
}
