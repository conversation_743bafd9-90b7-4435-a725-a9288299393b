package com.zhixianghui.service.banklink.facade.pay;

import cn.hutool.core.util.StrUtil;
import com.zhixianghui.common.statics.enums.bankLink.pay.BankPayStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.banklink.service.pay.CKHPayFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayRespVo;
import com.zhixianghui.facade.merchant.entity.MerchantCkhQuote;
import com.zhixianghui.facade.merchant.enums.BalancedEnum;
import com.zhixianghui.facade.merchant.service.MerchantCkhQuoteFacade;
import com.zhixianghui.service.banklink.request.pay.AlipayBiz;
import com.zhixianghui.service.banklink.request.pay.JoinpayPayBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.net.SocketTimeoutException;

/**
 * <AUTHOR>
 * @ClassName CKHPayFacadeImpl
 * @Description TODO
 * @Date 2022/7/1 14:57
 */
@Slf4j
@Service(timeout = 20000)
public class CKHPayFacadeImpl implements CKHPayFacade {

    @Autowired
    private AlipayBiz alipayBiz;
    @Autowired
    private JoinpayPayBiz joinpayPayBiz;
    @Reference
    private MerchantCkhQuoteFacade merchantCkhQuoteFacade;

    @Override
    public PayRespVo pay(PayReqVo payReqVo) {
        if (payReqVo.getChannelNo().equals(ChannelNoEnum.ALIPAY.name())) {
            if (payReqVo.getChannelType().intValue() == ChannelTypeEnum.ALIPAY.getValue()) {
                return alipayBiz.ckhPayToPersonalAliPay(payReqVo);
            } else {
                return alipayBiz.ckhPayToPersonalBankCard(payReqVo);
            }
        }else if (payReqVo.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())){
            MerchantCkhQuote merchantCkhQuote = merchantCkhQuoteFacade.getFeeRate(payReqVo.getEmployerNo(),payReqVo.getMainstayNo(),ProductNoEnum.CKH.getValue());
            if (merchantCkhQuote == null){
                log.error("找不到对应的报价单，平台流水号：[{}]",payReqVo.getPlatTrxNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("计费异常");
            }
            if (merchantCkhQuote.getBalancedMode().intValue() == BalancedEnum.OFFLINE_BALANCED.getCode()) {
                payReqVo.setServiceFee(BigDecimal.ZERO.toPlainString());
            }

            return joinpayPayBiz.pay(payReqVo);
        }
        else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创客汇不支持此通道交易");
        }
    }

    @Override
    public PayRespVo payServiceFee(PayReqVo payReqVo) {
        if (ChannelNoEnum.ALIPAY.name().equals(payReqVo.getChannelNo())) {
            return alipayBiz.ckhPayFee(payReqVo);
        }else{
            PayRespVo payRespVo = new PayRespVo();
            payRespVo.setBankPayStatus(BankPayStatusEnum.SUCCESS.getValue());
            payRespVo.setBizCode("");
            payRespVo.setBankOrderNo(payReqVo.getBankOrderNo());
            payRespVo.setBizMsg("");
            return payRespVo;
        }
    }

    @Override
    public PayRespVo queryPayOrder(String remitPlatTrxNo) {
        return alipayBiz.queryAlipayCKHOrder(remitPlatTrxNo);
    }
}
