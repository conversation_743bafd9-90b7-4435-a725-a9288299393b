package com.zhixianghui.service.banklink.core.biz.receiptOrder;

import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.service.pay.VoucherFacade;
import com.zhixianghui.facade.banklink.vo.pay.VoucherVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.service.banklink.request.pay.JoinpayPayBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName JoinpayReceiptOrderHandler
 * @Description TODO
 * @Date 2023/6/29 14:10
 */
@Slf4j
@Component
public class JoinpayReceiptOrderHandler extends AbstractReceiptOrderHandler{

    @Reference
    private RecordItemFacade recordItemFacade;

    @Reference
    private VoucherFacade voucherFacade;


    @Override
    public Map<String, Object> applyOrder(String remitPlatTrxNo, String employerNo, String mainstayNo, Integer channelType) {
//        RecordItem recordItem = recordItemFacade.getByRemitPlatTrxNo(remitPlatTrxNo);
//        EmployerAccountInfo employerAccountInfo = getAccountInfo(employerNo,mainstayNo,channelType);
//        VoucherVo voucherVo = new VoucherVo().setBizType("ALLOCATE_BANK_TRANSFER").setRawOrderNo(remitPlatTrxNo).
//                setCertificateNo("WX" + remitPlatTrxNo).
//                setRechargeTime(DateUtil.formatDate(recordItem.getCreateTime())).
//                setChannelAccountNo(employerAccountInfo.getParentMerchantNo()).
//                setCallbackUrl(certificateCallbackUrl).
//                setChannelMchNo(employerAccountInfo.getParentMerchantNo()).
//                setChannelNo(ChannelNoEnum.JOINPAY.name());
//        voucherFacade.generateCertificate(voucherVo,remitPlatTrxNo);
        return null;
    }

    @Override
    protected File download(Map<String, Object> paramMap) {
        return null;
    }
}
