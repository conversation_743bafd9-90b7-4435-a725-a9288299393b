package com.zhixianghui.service.banklink.facade.ac;

import com.zhixianghui.facade.banklink.service.ac.AcAccountManagerFacade;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AcAccountManagerFacadeImpl implements AcAccountManagerFacade {


}
