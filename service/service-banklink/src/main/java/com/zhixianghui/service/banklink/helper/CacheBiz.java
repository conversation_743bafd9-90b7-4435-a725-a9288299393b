package com.zhixianghui.service.banklink.helper;

import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.service.banklink.core.biz.KeyPairRecordBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * 缓存
 * <AUTHOR>
 * @date 2020/9/7
 **/
@Component
public class CacheBiz {

    @Autowired
    private KeyPairRecordBiz keyPairRecordBiz;

    /**
     * 获取秘钥对
     * @param channelNo
     * @param channelMchNo
     * @return
     */
    @Cacheable(value = "keyCache", key = "targetClass + ':' + methodName + ':' + #channelNo + ':' + #channelMchNo")
    public KeyPairRecord getByChannelNoAndChannelMchNo(String channelNo, String channelMchNo){
        return keyPairRecordBiz.getByChannelNoAndChannelMchNo(channelNo, channelMchNo);
    }
}
