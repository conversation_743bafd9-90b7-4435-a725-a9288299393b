package com.zhixianghui.service.banklink.facade.pay;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.banklink.service.pay.VoucherFacade;
import com.zhixianghui.facade.banklink.vo.pay.VoucherVo;
import com.zhixianghui.service.banklink.annotation.CurrentKeyPair;
import com.zhixianghui.service.banklink.request.voucher.HttpRequest;
import com.zhixianghui.service.banklink.request.voucher.HttpResponse;
import com.zhixianghui.service.banklink.utils.joinpay.SignUtil;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.util.TreeMap;

@Service
public class VoucherFacadeImpl implements VoucherFacade {
    private static final Logger LOGGER = LoggerFactory.getLogger(VoucherFacadeImpl.class);

    @Value("https://www.joinpay.com/trade/voucherPush.action")
    public String JOIN_CERTIFICATE_URL;
    @Value("https://www.joinpay.com/trade/voucherQuery.action")
    public String JOIN_QUERY_CERTIFICATE_URL;

    @CurrentKeyPair
    @Override
    public JSONObject generateCertificate(VoucherVo voucherVo, String traceId) {
        TreeMap<String, String> map = voucherVo.fixParam(voucherVo);
        if (voucherVo.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())) {
            map.put("hmac", SignUtil.genHmac(map, voucherVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        } else if (voucherVo.getChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name())) {
            map.put("hmac", SignUtil.genHmac(map, voucherVo.getKeyPairRecord().getChannelPublicKeyDecrypt()));
        }
        LOGGER.info("[{}] 生成凭证原始请求参数: {}", traceId, JSONObject.toJSONString(voucherVo));
        LOGGER.info("[{}] 生成凭证请求参数: {}", traceId, JSONObject.toJSONString(map));
        HttpRequest request = new HttpRequest();
        HttpResponse response = null;
        try {
            response = request.sendPost(JOIN_CERTIFICATE_URL, map);
        } catch (IOException e) {
            LOGGER.error("[{}] 凭证生成失败: ", traceId, e);
            return null;
        }
        LOGGER.info("[{}] 生成凭证返回原始结果: {}", traceId, response.getContent());
        JSONObject respObject = JSONObject.parseObject(response.getContent());
        LOGGER.info("[{}]请求生成凭证结束: {}", traceId, JSONObject.toJSONString(respObject));
        return respObject;
    }

    @CurrentKeyPair
    @Override
    public JSONObject downloadCertificate(VoucherVo voucherVo, String traceId) {
        TreeMap<String, String> map = voucherVo.fixDownloadParam(voucherVo);
        String signStr = SignUtil.sortAndSplicing(map);
        // 签名
        if (voucherVo.getChannelNo().equals(ChannelNoEnum.JOINPAY.name())) {
            map.put("hmac", SignUtil.SignByMD5(signStr, voucherVo.getKeyPairRecord().getMchPrivateKeyDecrypt()));
        } else if (voucherVo.getChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name())) {
            map.put("hmac", SignUtil.SignByMD5(signStr, voucherVo.getKeyPairRecord().getChannelPublicKeyDecrypt()));
        }
        HttpRequest request = new HttpRequest();
        HttpResponse response = null;
        LOGGER.info("[{}] 获取凭证原始请求参数: {}", traceId, JSONObject.toJSONString(voucherVo));
        LOGGER.info("[{}] 获取凭证请求参数: {}", traceId, JSONObject.toJSONString(map));

        try {
            response = request.sendPost(JOIN_QUERY_CERTIFICATE_URL, map);
        } catch (IOException e) {
            LOGGER.error("[{}]获取凭证链接失败: ", traceId, e);
            return null;
        }
        LOGGER.info("[{}]获取凭证链接返回原始结果：{}", traceId, response.getContent());
        JSONObject respObject = JSONObject.parseObject(response.getContent());
        LOGGER.info("[{}]获取凭证链接结束: {}", traceId, JSONObject.toJSONString(respObject));
        return respObject;
    }
}
