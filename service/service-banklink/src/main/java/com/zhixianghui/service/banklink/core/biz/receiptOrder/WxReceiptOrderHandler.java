package com.zhixianghui.service.banklink.core.biz.receiptOrder;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.exception.WxApiException;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.service.banklink.request.wxpay.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName WxReceiptOrderHandler
 * @Description TODO
 * @Date 2023/6/29 11:40
 */
@Slf4j
@Component
public class WxReceiptOrderHandler extends AbstractReceiptOrderHandler{

    @Autowired
    private WxPayService wxPayService;

    @Override
    public Map<String, Object> applyOrder(String remitPlatTrxNo, String employerNo, String mainstayNo, Integer channelType) {
        WxResVo wxResVo = wxPayService.receiptAccept(remitPlatTrxNo);
        if (!wxResVo.isSuccess()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("申请电子回单失败");
        }
        return new HashMap<>();
    }

    @Override
    protected File download(Map<String, Object> paramMap) {
        String remitPlatTrxNo = (String) paramMap.get("remitPlatTrxNo");
        //获取微信电子回单
        WxResVo wxResVo = wxPayService.queryReceiptBill(remitPlatTrxNo);
        if (!wxResVo.isSuccess()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("下载微信电子回单失败");
        }
        JSONObject jsonObject = JSONObject.parseObject(wxResVo.getResponBody());
        String downloadUrl = jsonObject.getString("download_url");
        byte[] bytes;
        try {
            bytes = wxPayService.downloadReceiptBill(downloadUrl,remitPlatTrxNo);
        }catch (WxApiException e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("下载微信电子回单失败");
        }

        FileOutputStream fileOutputStream = null;
        BufferedOutputStream bufferedOutputStream = null;
        // 下载文件
        try {
            File file = FileUtils.createFile(TEMP_PATH + "电子凭证" + remitPlatTrxNo + "-" + new Date().getTime() + FILE_SUFFIX);
            fileOutputStream = new FileOutputStream(file);
            bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
            bufferedOutputStream.write(bytes);
            return file;
        }catch (Exception e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("下载微信电子回单失败");
        }finally {
            try {
                if (bufferedOutputStream != null){
                    bufferedOutputStream.close();
                }
                if (fileOutputStream != null){
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
