package com.zhixianghui.service.banklink.request.wxpay;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.exception.WxApiException;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.vo.pay.MiniAppPayVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReqVo;
import com.zhixianghui.facade.banklink.vo.wx.WxResVo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.trade.utils.AmountUtil;
import com.zhixianghui.service.banklink.config.WxPayConfig;
import com.zhixianghui.service.banklink.utils.wxpay.WxPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName WxPayService
 * @Description TODO
 * @Date 2021/12/3 14:20
 */
@Service
@Slf4j
public class WxPayService {

    @Autowired
    private WxPayUtil wxPayUtil;

    @Autowired
    private WxPayConfig wxPayConfig;

    //特约商户资金授权类型
    private static final String AUTHORIZATION_TYPE = "INFORMATION_AND_FUND_AUTHORIZATION_TYPE";

    //账户类型
    private static final String ACCOUNT_TYPE = "OPERATION";

    //电子回单受理类型
    private static final String ACCEPT_TYPE = "BATCH_TRANSFER";

    //最小鉴权金额
    private static final Integer MIN_AUTH_MONEY = 30;

    //最大条数
    private static final int MAX_LIMIT = 20;

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    public WxResVo getWithDrawStatus(String subMchId,String withdrawNo){
        try {
            String url = wxPayConfig.getWithdrawStatusUrl().replace("{out_request_no}",withdrawNo);
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("sub_mchid",subMchId);
            WxResVo wxResVo = WxPayUtil.sendGet(uriBuilder);
            log.info("特约商户号：[{}]，提现订单号：[{}]，查询微信提现接口返回：[{}]，",subMchId,withdrawNo,objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求特约商户余额查询接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * jsapi查询退款订单（直连商户）
     * @param refundOrderNo 退款订单
     * @return
     */
    public WxResVo jsapiRefundQuery(String refundOrderNo){
        try {
            String url = "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds/{out_refund_no}".replace("{out_refund_no}",refundOrderNo);
            URIBuilder uriBuilder = new URIBuilder(url);
            WxResVo wxResVo = WxPayUtil.sendGet(uriBuilder);
            log.info("退款订单号：[{}]，查询微信查询jsapi退款订单接口返回：[{}]，",refundOrderNo,objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求微信jsapi退款订单查询接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * jsapi退款（直连商户）
     * @param originOrderNo
     * @param refundOrderNo
     * @param amount
     * @param reason
     * @return
     */
    public WxResVo jsapiRefund(String originOrderNo,String refundOrderNo,Long amount,String reason){
        try {
            String url = "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds";
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("out_trade_no",originOrderNo);
            paramMap.put("out_refund_no",refundOrderNo);
            paramMap.put("reason",reason);
            paramMap.put("notify_url",wxPayConfig.getJsapiCallbackUrl());
            Map<String,Object> amountMap = new HashMap<>();
            amountMap.put("refund",amount);
            amountMap.put("total",amount);
            amountMap.put("currency","CNY");
            paramMap.put("amount",amountMap);
            String reqJson = objectMapper.writeValueAsString(paramMap);
            log.info("调用微信退款接口，原订单号：[{}]，退款订单号：[{}]，请求参数：[{}]",originOrderNo,refundOrderNo,reqJson);
            WxResVo wxResVo = wxPayUtil.sendPost(new URIBuilder(url),reqJson);
            log.info("调用微信退款接口，原订单号：[{}]，退款订单号：[{}]，返回信息：[{}]",originOrderNo,refundOrderNo,wxResVo.getResponBody());
            return wxResVo;
        }catch (Exception e){
            log.error("请求微信jsapi退款接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }


    /**
     * jsapi关闭订单（直连商户）
     * @param outTradeNo
     * @return
     */
    public WxResVo jsapiClose(String orderNo){
        try {
            String url = "https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/{out_trade_no}/close".replace("{out_trade_no}",orderNo);
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("mchid",wxPayConfig.getMerchantId());
            String reqJson = objectMapper.writeValueAsString(paramMap);
            log.info("调用微信jsapi关闭订单接口，订单号：[{}]",orderNo);
            WxResVo wxResVo = WxPayUtil.sendPostEmptyResp(new URIBuilder(url),reqJson);
            log.info("调用微信jsapi关闭接口，订单号：[{}]，返回信息：[{}]",orderNo,JsonUtil.toString(wxResVo));
            return wxResVo;
        } catch (Exception e) {
            log.error("请求微信jsapi关闭订单接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * jsapi订单查询（直连商户）
     * @param mchid
     * @param outTradeNo
     * @return
     */
    public WxResVo jsapiQuery(String orderNo){
        try {
            String url = "https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/{out_trade_no}".replace("{out_trade_no}",orderNo);
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("mchid",wxPayConfig.getMerchantId());
            WxResVo wxResVo = WxPayUtil.sendGet(uriBuilder);
            log.info("jsapi订单号：[{}]，查询微信查询jsapi订单接口返回：[{}]，",orderNo,objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        } catch (Exception e) {
            log.error("请求微信jsapi订单查询接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * 小程序支付（直连商户）
     * @param subMchid
     * @param description
     * @param recordItemNo
     * @param amount
     * @param payUser
     * @return
     */
    public WxResVo jsapiPay(MiniAppPayVo miniAppPayVo){
        try {
            //交易过期时间
            String url = "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("appid",wxPayConfig.getAppId());
            paramMap.put("mchid",wxPayConfig.getMerchantId());
            paramMap.put("out_trade_no",miniAppPayVo.getOrderNo());
            if (StringUtils.isNotBlank(miniAppPayVo.getExpireTime())){
                paramMap.put("time_expire",miniAppPayVo.getExpireTime());
            }
            paramMap.put("description",miniAppPayVo.getDescription());
            paramMap.put("notify_url",wxPayConfig.getJsapiCallbackUrl());
            Map<String,Object> amountMap = new HashMap<>();
            amountMap.put("total",miniAppPayVo.getAmount());
            paramMap.put("amount",amountMap);
            Map<String,Object> payerMap = new HashMap<>();
            payerMap.put("openid",miniAppPayVo.getPayUserId());
            paramMap.put("payer",payerMap);
            String reqJson = objectMapper.writeValueAsString(paramMap);
            log.info("调用微信jsapi下单接口，订单号：[{}]，请求参数：[{}]",miniAppPayVo.getOrderNo(),reqJson);
            WxResVo wxResVo = WxPayUtil.sendPost(new URIBuilder(url),reqJson);
            log.info("调用微信jsapi下单接口，订单号：[{}]，返回信息：[{}]",miniAppPayVo.getOrderNo(),JsonUtil.toString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求微信jsapi下单接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

//    public WxResVo jsapiPay(String subMchid,String description,String recordItemNo,int amount,String receiveUser){
//        try {
//            String url = "https://api.mch.weixin.qq.com/v3/pay/partner/transactions/jsapi";
//            Map<String,Object> paramMap = new HashMap<>();
//            paramMap.put("sp_appid",wxPayConfig.getAppId());
//            paramMap.put("sp_mchid",wxPayConfig.getMerchantId());
//            paramMap.put("sub_mchid",subMchid);
//            paramMap.put("description",description);
//            paramMap.put("out_trade_no",recordItemNo);
//            paramMap.put("notify_url","https://www.baidu.com");
//            Map<String,Object> amountMap = new HashMap<>();
//            amountMap.put("total",amount);
//            paramMap.put("amount",amountMap);
//            Map<String,Object> payerMap = new HashMap<>();
//            payerMap.put("sp_openid",receiveUser);
//            paramMap.put("payer",payerMap);
//            String reqJson = objectMapper.writeValueAsString(paramMap);
//            WxResVo wxResVo = WxPayUtil.sendPost(new URIBuilder(url),reqJson);
//            log.info("调用微信jsapi下单接口，订单号：[{}]，返回信息：[{}]",recordItemNo,JsonUtil.toString(wxResVo));
//            return wxResVo;
//        }catch (Exception e){
//            log.error("请求微信jsapi下单接口异常",e);
//            throw new WxApiException(e.getCause().getMessage());
//        }
//    }

    /**
     * 提现
     */
    public WxResVo withDraw(String subMchId,String withdrawNo,int amount,String remark){
        try{
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("sub_mchid",subMchId);
            paramMap.put("out_request_no",withdrawNo);
            paramMap.put("amount",amount);
            paramMap.put("remark",remark);
            paramMap.put("bank_memo",remark);
            paramMap.put("account_type",ACCOUNT_TYPE);
            String reqJson = objectMapper.writeValueAsString(paramMap);
            WxResVo wxResVo = WxPayUtil.sendPost(new URIBuilder(wxPayConfig.getWithdrawUrl()),reqJson);
            log.info("特约商户提现，特约商户号：[{}]，提现单号：[{}]，返回信息：{}",subMchId,withdrawNo,objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求微信电子回单受理接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * 下载电子回单
     */
    public byte[] downloadReceiptBill(String downloadUrl,String recordItemNo){
        try {
            HttpResponse httpResponse = wxPayUtil.httpGetWithNoResSign(downloadUrl);
            if (httpResponse.getStatus() == 200){
                return httpResponse.bodyBytes();
            }else{
                log.error("订单号：[{}],获取电子回单异常，返回信息：{}",recordItemNo,httpResponse.body());
                throw new WxApiException("获取电子回单异常");
            }
        } catch (Exception e) {
            log.error("请求微信下载电子回单接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }

    }

    public WxResVo queryReceiptBill(String recordItemNo) throws WxApiException{
        try {
            String url = wxPayConfig.getReceiptAcceptUrl();
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("accept_type",ACCEPT_TYPE);
            uriBuilder.addParameter("out_batch_no",recordItemNo);
            uriBuilder.addParameter("out_detail_no",recordItemNo);
            WxResVo wxResVo = WxPayUtil.sendGet(uriBuilder);
            log.info("订单：[{}]，微信电子回单查询接口返回：[{}]",recordItemNo,objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求微信电子回单查询接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * 申请受理电子回单
     * @param recordItemNo 打款流水号
     * @return
     * @throws WxApiException
     */
    public WxResVo receiptAccept(String recordItemNo) throws WxApiException{
        try{
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("accept_type",ACCEPT_TYPE);
            paramMap.put("out_batch_no",recordItemNo);
            paramMap.put("out_detail_no",recordItemNo);
            String reqJson = objectMapper.writeValueAsString(paramMap);
            WxResVo wxResVo = WxPayUtil.sendPost(new URIBuilder(wxPayConfig.getReceiptAcceptUrl()),reqJson);
            log.info("订单：[{}]，微信电子回单受理接口返回：[{}]",recordItemNo,objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求微信电子回单受理接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * 供应商打款到个人
     * @param payReqVo
     * @return
     */
    public WxResVo transfer(PayReqVo payReqVo) throws WxApiException {
        if (StringUtil.isEmpty(payReqVo.getReceiveAmount())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("打款金额不能为空");
        }

        Long money = AmountUtil.changeToFen(payReqVo.getReceiveAmount());
        if (money.longValue() > 2000000L){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("微信风控限制单笔金额不能超过2万");
        }

        String twoElementGrant = dataDictionaryFacade.getSystemConfig("TWO_ELEMENT_GRANT");
        try {
            log.info("订单：[{}]，微信支付金额:{}",payReqVo.getBankOrderNo(),money);
            String remarks = StringUtils.isBlank(payReqVo.getRemitRemark()) ? "微信转账" : payReqVo.getRemitRemark();
            Map<String,Object> batchInfo = new HashMap<>();
            batchInfo.put("sub_mchid",payReqVo.getParentMerchantNo());
            batchInfo.put("sub_appid",payReqVo.getAppid());
            batchInfo.put("authorization_type",AUTHORIZATION_TYPE);
            batchInfo.put("out_batch_no",payReqVo.getBankOrderNo());
            batchInfo.put("batch_name","微信支付");
            batchInfo.put("batch_remark",payReqVo.getBankOrderNo());
            batchInfo.put("total_amount",money);
            batchInfo.put("total_num",1);
            batchInfo.put("sp_appid",wxPayConfig.getSpAppId());
            Map<String,Object> detailInfo = new HashMap<>();
            detailInfo.put("out_detail_no",payReqVo.getBankOrderNo());
            detailInfo.put("transfer_amount",money);
            detailInfo.put("transfer_remark",remarks);
            detailInfo.put("openid",payReqVo.getReceiveAccountNo());
            if (StringUtils.isBlank(twoElementGrant) ||
                    !ListUtil.toList(twoElementGrant.split(",")).contains(payReqVo.getEmployerNo())) {
                if (money >= MIN_AUTH_MONEY){
                    detailInfo.put("user_name",WxPayUtil.getEncrypt(payReqVo.getReceiveName()));
                }
            }

            batchInfo.put("transfer_detail_list",new ArrayList<Map<String,Object>>(){{add(detailInfo);}});
            String reqJson = objectMapper.writeValueAsString(batchInfo);
            log.info("订单：[{}]，请求微信转账接口，接口请求参数：[{}]",payReqVo.getBankOrderNo(),reqJson);
            WxResVo wxResVo = WxPayUtil.sendPost(new URIBuilder(wxPayConfig.getPayUrl()),reqJson);
            log.info("订单：[{}]，微信转账接口返回：[{}]",payReqVo.getBankOrderNo(),objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求微信打款接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * 查询特约商户余额
     * @param subMchId 特约商户号
     * @return
     * @throws URISyntaxException
     */
    public WxResVo getAmount(String subMchId) throws WxApiException {
        try {
            String url = wxPayConfig.getFundBalanceUrl().replace("{sub_mchid}",subMchId);
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("account_type",ACCOUNT_TYPE);
            WxResVo wxResVo = WxPayUtil.sendGet(uriBuilder);
            log.info("特约商户号：[{}]，查询特约商户号余额接口返回：[{}]",subMchId,objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求特约商户余额查询接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * 查询订单明细
     * @param recordItemNo 打款流水号
     * @return
     * @throws Exception
     */
    public WxResVo getOrder(String recordItemNo) throws WxApiException {
        try {
            String url = wxPayConfig.getOrderDetailUrl().replace("{out_batch_no}",recordItemNo).replace("{out_detail_no}",recordItemNo);
            URIBuilder uriBuilder = new URIBuilder(url);
            WxResVo wxResVo = WxPayUtil.sendGet(uriBuilder);
            log.info("订单号：[{}]，订单明细查询接口返回：[{}]",recordItemNo,objectMapper.writeValueAsString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求订单明细查询接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }

    /**
     * 账户变动查询
     * @param subMchId 特约商户号
     * @param date 查询日期
     * @param offset 最大偏移量
     * @return
     * @throws Exception
     */
    public WxResVo incomeRecords(String subMchId,String date,int offset) throws WxApiException {
        try{
            URIBuilder uriBuilder =  new URIBuilder(wxPayConfig.getIncomeRecordsUrl());
            uriBuilder.addParameter("sub_mchid",subMchId);
            uriBuilder.addParameter("account_type",ACCOUNT_TYPE);
            uriBuilder.addParameter("date",date);
            uriBuilder.addParameter("offset",String.valueOf(offset));
            uriBuilder.addParameter("limit",String.valueOf(MAX_LIMIT));
            WxResVo wxResVo = WxPayUtil.sendGet(uriBuilder);
            log.info("查询特约商户来账通知，特约商户号：[{}],时间：[{}]，偏移量：[{}]，返回信息：[{}]",subMchId,date,offset, JsonUtil.toString(wxResVo));
            return wxResVo;
        }catch (Exception e){
            log.error("请求微信接口异常",e);
            throw new WxApiException(e.getCause().getMessage());
        }
    }
}
