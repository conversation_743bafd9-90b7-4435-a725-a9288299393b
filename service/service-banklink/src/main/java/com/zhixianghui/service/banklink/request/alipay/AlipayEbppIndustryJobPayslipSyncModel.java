package com.zhixianghui.service.banklink.request.alipay;

import com.alipay.api.AlipayObject;
import com.alipay.api.internal.mapping.ApiField;

import java.util.Date;

public class AlipayEbppIndustryJobPayslipSyncModel extends AlipayObject {
    private static final long serialVersionUID = 2656438638871911123L;
    @ApiField("amount")
    private String amount;
    @ApiField("bank_code")
    private String bankCode;
    @ApiField("bill_detail_url")
    private String billDetailUrl;
    @ApiField("card_no")
    private String cardNo;
    @ApiField("channel")
    private String channel;
    @ApiField("company_cert_no")
    private String companyCertNo;
    @ApiField("company_name")
    private String companyName;
    @ApiField("contact_info")
    private String contactInfo;
    @ApiField("login_id")
    private String loginId;
    @ApiField("open_id")
    private String openId;
    @ApiField("out_biz_no")
    private String outBizNo;
    @ApiField("remark")
    private String remark;
    @ApiField("salary_time")
    private Date salaryTime;
    @ApiField("user_id")
    private String userId;
    @ApiField("user_name")
    private String userName;

    public AlipayEbppIndustryJobPayslipSyncModel() {
    }

    public String getAmount() {
        return this.amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getBankCode() {
        return this.bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBillDetailUrl() {
        return this.billDetailUrl;
    }

    public void setBillDetailUrl(String billDetailUrl) {
        this.billDetailUrl = billDetailUrl;
    }

    public String getCardNo() {
        return this.cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getChannel() {
        return this.channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getCompanyCertNo() {
        return this.companyCertNo;
    }

    public void setCompanyCertNo(String companyCertNo) {
        this.companyCertNo = companyCertNo;
    }

    public String getCompanyName() {
        return this.companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getContactInfo() {
        return this.contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public String getLoginId() {
        return this.loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getOpenId() {
        return this.openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOutBizNo() {
        return this.outBizNo;
    }

    public void setOutBizNo(String outBizNo) {
        this.outBizNo = outBizNo;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getSalaryTime() {
        return this.salaryTime;
    }

    public void setSalaryTime(Date salaryTime) {
        this.salaryTime = salaryTime;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
