package com.zhixianghui.service.banklink.core.biz.message.sms.senders;

import com.alibaba.fastjson.JSON;
import com.chuanglan.constant.ChuangLanSmsConstant;
import com.chuanglan.model.request.SmsSendRequest;
import com.chuanglan.model.response.SmsSendResponse;
import com.chuanglan.util.ChuangLanSmsUtil;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.banklink.vo.sms.SmsQueryParam;
import com.zhixianghui.facade.banklink.vo.sms.SmsQueryResp;
import com.zhixianghui.facade.banklink.vo.sms.SmsSendResp;
import com.zhixianghui.service.banklink.core.biz.message.sms.SmsSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> li
 * @desc  创蓝短信平台发送器
 * @date 2020/09/01
 */
@Service
public class ChuangLanSmsSender implements SmsSender {

    private Logger logger = LoggerFactory.getLogger(ChuangLanSmsSender.class);

    /**
     * 正常响应的code
     */
    public final static String SUCCESS_SEND_STATUS = "0";
    /**
     * 模板错误的异常
     */
    public final static String ERROR_SEND_TEMPLATE_STATUS = "124";

    @Value("${sms.chuanglan.account}")
    private String account;

    @Value("${sms.chuanglan.pwd}")
    private String password;

    @Override
    public String prefixName(String tplCode) {
        return DEFAULT_PREFIX_NAME;
    }

    @Override
    public SmsSendResp send(String phone, String msg, String bizKey) {

        //检查
        this.checkMsg(phone, msg);

        //创蓝发送短信需要自己拼装内容，而不能模板id + 参数的模式
        SmsSendRequest smsSingleRequest = new SmsSendRequest(account, password, msg, phone,"true");
        String requestJson = JSON.toJSONString(smsSingleRequest);
        try {

            String response = ChuangLanSmsUtil.sendSmsByPost(ChuangLanSmsConstant.SMS_REQUEST_URL, requestJson);
            logger.info("【创蓝短信发送】{}，短信信息：{}，短信发送返回：{}",phone,msg,response);
            SmsSendResponse smsSingleResponse = JSON.parseObject(response, SmsSendResponse.class);
            //直接发送短信消息的前提是msg必须满足已申请的创蓝的模板标准。所以此种错误异常需单独出来
            if (smsSingleResponse.getCode().equals(ERROR_SEND_TEMPLATE_STATUS)) {
                logger.error("短信发送异常，模板错误，消息体需按照创蓝的已申请模板发送 phone={}", phone);
                throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("短信发送异常，模板错误，消息体需按照创蓝的已申请模板发送");
            }
            SmsSendResp smsSendResp = new SmsSendResp();
            smsSendResp.setCode(smsSingleResponse.getCode());
            smsSendResp.setIsSuccess(SUCCESS_SEND_STATUS.equalsIgnoreCase(smsSingleResponse.getCode()));
            smsSendResp.setMessage(smsSingleResponse.getErrorMsg());
            smsSendResp.setSerialNo(smsSingleResponse.getMsgId());
            return smsSendResp;
        } catch (Exception e) {
            logger.error("短信发送异常 phone={}", phone, e);
            throw CommonExceptions.UNEXPECT_ERROR.newWith("短信发送异常", e);
        }
    }

    @Override
    @Deprecated
    public SmsSendResp send(String phone, String tplCode, Map<String, Object> tplParam, String bizKey) {
        return null;
    }

    @Override
    public SmsQueryResp getSingleSmsStatus(SmsQueryParam queryParam) {
        return null;
    }


    /**
     * 对消息体做简单检查，比如签名检查，没有签名则加上签名
     * @param phone 电话号码
     * @param msg 消息体
     */
    private void checkMsg(String phone, String msg){
        if (StringUtil.isEmpty(phone)) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("短信发送异常，电话号码为空");
        } else if (!ValidateUtil.isMobile(phone)) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("短信发送异常，电话号码格式错误");
        } else if (StringUtil.isEmpty(msg)) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("短信发送异常，消息体为空");
        }
        //判断是否以签名开头
        if (!msg.contains(this.prefixName(""))) {
            msg = String.format("【%s】%s", this.prefixName(""), msg);
        }
    }
}
