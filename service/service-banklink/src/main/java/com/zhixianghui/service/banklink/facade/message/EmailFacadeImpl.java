package com.zhixianghui.service.banklink.facade.message;

import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.service.banklink.core.biz.message.email.EmailBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class EmailFacadeImpl implements EmailFacade {
    @Autowired
    EmailBiz emailBiz;

    @Override
    public boolean sendToGroupSync(String groupKey, String subject, String content, boolean isHtml) throws BizException {
        return emailBiz.sendToGroupSync(groupKey, subject, content, isHtml);
    }

    @Override
    public boolean sendToGroupAsync(String groupKey, String subject, String content, boolean isHtml) throws BizException {
        return emailBiz.sendToGroupAsync(groupKey, subject, content, isHtml);
    }

    @Override
    public boolean sendSync(EmailParamDto param) throws BizException {
        return emailBiz.sendSync(param);
    }

    @Override
    public boolean sendAsync(EmailParamDto param) throws BizException {
        return emailBiz.sendAsync(param);
    }

}
