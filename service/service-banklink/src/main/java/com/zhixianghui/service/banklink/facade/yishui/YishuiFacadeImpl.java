package com.zhixianghui.service.banklink.facade.yishui;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.facade.banklink.service.yishui.YishuiFacade;
import com.zhixianghui.facade.banklink.vo.yishui.*;
import com.zhixianghui.facade.banklink.vo.yishui.req.*;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.service.banklink.core.biz.YishuiBiz;
import com.zhixianghui.service.banklink.request.xiaomifeng.BeeRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Service
@Slf4j
public class YishuiFacadeImpl implements YishuiFacade {

    @Autowired
    private YishuiBiz yishuiBiz;
    @Autowired
    private BeeRequest beeRequest;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Override
    public String getYishuiToken(String username, String password, String enterprise_sn) throws BizException{
        return yishuiBiz.getYishuiToken(username, password, enterprise_sn);
    }

    @Override
    public String getAgentToken() throws BizException {
        return yishuiBiz.getAgentToken();
    }

    @Override
    public String getUploadUrl() throws BizException{
        return yishuiBiz.getUploadUrl();
    }

    @Override
    public YiResponse<Map<String, String>> saveEnterpriseDetail(SaveEnterpriseDetailVo saveVo) throws BizException{
        return yishuiBiz.saveEnterpriseDetail(saveVo);
    }

    @Override
    public YiResponse saveEnterpriseDraftRate(SaveEnterpriseDraftRate draftRate) throws BizException {
        return yishuiBiz.saveEnterpriseDraftRate(draftRate);
    }

    @Override
    public YiResponse<Map<String, Object>> getEnterpriseDetail(String enterpriseId) {
        return yishuiBiz.getEnterpriseDetail(enterpriseId);
    }

    @Override
    public YiResponse<Map<String, Object>> getEnterpriseDetailByName(String enterpriseName) {
        return yishuiBiz.getEnterpriseDetailByName(enterpriseName);
    }

    @Override
    public YiResponse<YishuiAddEmpVo> addEmployee(RequestVo<AddEmpVo> requestVo, int channelType) {
        return yishuiBiz.addEmployee(requestVo, channelType);
    }

    @Override
    public YiResponse<YishuiContractListVo> contractList(RequestVo<ContractListQueryVo> requestVo) {
        return yishuiBiz.contractList(requestVo);
    }

    @Override
    public YiResponse<YishuiContractInfoVo> contractInfo(RequestVo<String> requestVo) {
        return yishuiBiz.contractInfo(requestVo);
    }

    @Override
    public YiResponse<YishuiFastIssuing> fastIssuing(RequestVo<FastIssuingVo> requestVo) {
        return yishuiBiz.fastIssuing(requestVo);
    }

    @Override
    public YiResponse changeOrderStatus(RequestVo<ChangeOrderReqVo> requestVo) {
        return yishuiBiz.changeOrderStatus(requestVo);
    }

    @Override
    public YiResponse<Map<String, Object>> findOrderFromRequestNo(RequestVo<String> requestVo) {
        return yishuiBiz.findOrderFromRequestNo(requestVo);
    }

    @Override
    public YiResponse contractSave(RequestVo<ContractSaveVo> requestVo) {
        return yishuiBiz.contractSave(requestVo);
    }

    @Override
    public YiResponse<String> addBank(RequestVo<AddBankVo> requestVo) {
        return yishuiBiz.addBank(requestVo);
    }


    @Override
    public void orderBatchSubmit(String outBatchNo){
        ThreadUtil.execAsync(() -> {
            beeRequest.orderBatchSubmit(outBatchNo);
        });
    }

    @Override
    public JSONObject getFundInfo(String employerNo){
        return beeRequest.getYishuiFundInfo(employerNo);
    }
}
