package com.zhixianghui.service.banklink.request.alipay;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.service.banklink.constant.RequestConstant;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;

public class BankcardCheckService {
    public static final String CHECK_URL = "https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?cardNo={{yourcardNo}}&cardBinCheck=true";

    public static boolean checkBankcard(String bankCardno) {
        final String checkUrl = CHECK_URL.replace("{{yourcardNo}}", bankCardno);

        WebClient webClient = WebClient.create(checkUrl);
        Mono<JSONObject> mono = webClient.get().retrieve().bodyToMono(JSONObject.class);
        JSONObject respObject = mono.block(Duration.ofMillis(RequestConstant.REQUEST_TIMEOUT));
        return respObject.getBooleanValue("validated");
    }

    public static void main(String[] args) {
        final long start = System.currentTimeMillis();
        System.out.println(checkBankcard("623058000002919545"));
        System.out.println(System.currentTimeMillis()-start);
    }
}
