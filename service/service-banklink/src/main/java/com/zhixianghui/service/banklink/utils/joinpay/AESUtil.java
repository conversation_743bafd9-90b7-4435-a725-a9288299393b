package com.zhixianghui.service.banklink.utils.joinpay;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.CodeUtil;
import com.zhixianghui.common.util.utils.HEXUtil;
import com.zhixianghui.common.util.utils.RandomUtil;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * AES加解密工具类
 * <AUTHOR>
 * @date 2018-12-15
 */
public class AESUtil {
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";//算法/模式/补码方式
    private final static String AES = "AES";
    
    public static String encrypt(String content, String password, String ivParam){
        try {
            SecretKeySpec secSpec = genSecretKeySpec(password);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            IvParameterSpec ivp = new IvParameterSpec(ivParam.getBytes(StandardCharsets.UTF_8.name()));//使用CBC模式，需要一个向量iv，可增加加密算法的强度
            cipher.init(Cipher.ENCRYPT_MODE, secSpec, ivp);
            byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8.name()));
            return HEXUtil.encode(CodeUtil.base64Encode(encrypted));
        }catch(Throwable e){
            throw CommonExceptions.UNEXPECT_ERROR.newWith("AES加密异常", e);
        }
    }

    public static String decrypt(String content, String password, String ivParam){
        try {
            SecretKeySpec secSpec = genSecretKeySpec(password);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            IvParameterSpec ivp = new IvParameterSpec(ivParam.getBytes(StandardCharsets.UTF_8.name()));
            cipher.init(Cipher.DECRYPT_MODE, secSpec, ivp);
            byte[] encrypted1 = CodeUtil.base64Decode(HEXUtil.decode(content));
            byte[] original = cipher.doFinal(encrypted1);
            return new String(original, StandardCharsets.UTF_8.name());
        }catch(Throwable e){
            throw CommonExceptions.UNEXPECT_ERROR.newWith("AES解密异常", e);
        }
    }

    /**
     * 生成密钥对象 密钥可支持16位或32位，如果是32位，可能会报：java.security.InvalidKeyException: Illegal key size 异常，此时需要更换JDK的local_policy.jar和US_export_policy.jar
     * @param password
     * @return
     * @throws Exception
     */
    public static SecretKeySpec genSecretKeySpec(String password) throws Exception{
        if (password == null || (password.length() != 16 && password.length() != 32)) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("password长度须为16或32位");
        }
        KeyGenerator kGen = KeyGenerator.getInstance(AES);
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(password.getBytes());
        kGen.init(password.length()==16 ? 128 : 256, secureRandom);
        SecretKey secretKey = kGen.generateKey();
        byte[] enCodeFormat = secretKey.getEncoded();
        return new SecretKeySpec(enCodeFormat, AES);
    }

    public static void main(String[] args){
        String data = "的的饭店附近的房间都开了就321ew232PKL13RFG分担分担节快乐大家分开@#,：？。76&~~！@#";
        String pwd = RandomUtil.get16LenStr();
        String iv = RandomUtil.get16LenStr();

        String encryptStr = encrypt(data, pwd, iv);
        String decryptStr = decrypt(encryptStr, pwd, iv);//使用正确的密钥、正确的向量解密

        System.out.println("data="+data);
        System.out.println("encryptStr="+encryptStr);
        System.out.println("decryptStr="+decryptStr);
        System.out.println("data.equals(decryptStr) ? "+(data.equals(decryptStr)));

        String pwd2 = RandomUtil.get16LenStr();
        String iv2 = RandomUtil.get16LenStr();
        String decryptStr2 = decrypt(encryptStr, pwd, iv2);//使用正确的密钥、错误的向量解密
//        String decryptStr2 = decrypt(encryptStr, pwd2, iv);//使用错误的密钥解密、正确的向量解密
//        String decryptStr2 = decrypt(encryptStr, pwd2, iv2);//使用错误的密钥、错误的向量解密
        System.out.println("decryptStr2="+decryptStr2);
    }
}
