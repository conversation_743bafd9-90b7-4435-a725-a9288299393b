package com.zhixianghui.service.banklink.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月04日 09:51:00
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "join.pay.withdraw")
public class JoinPayConfig {

    private String fundUrl;

    private String callBackFundUrl;
}