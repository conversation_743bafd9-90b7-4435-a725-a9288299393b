package com.zhixianghui.service.banklink.request.alipay;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayDataBillAccountbookereceiptQueryRequest;
import com.alipay.api.request.AlipayDataBillEreceiptagentApplyRequest;
import com.alipay.api.request.AlipayFundAccountbookCreateRequest;
import com.alipay.api.request.AlipayFundAccountbookNotifyQueryRequest;
import com.alipay.api.request.AlipayFundAccountbookNotifySubscribeRequest;
import com.alipay.api.request.AlipayFundAccountbookQueryRequest;
import com.alipay.api.request.AlipayFundTransCommonQueryRequest;
import com.alipay.api.request.AlipayFundTransPagePayRequest;
import com.alipay.api.request.AlipayFundTransUniTransferRequest;
import com.alipay.api.request.AlipayUserAgreementPageSignRequest;
import com.alipay.api.request.AlipayUserAgreementQueryRequest;
import com.alipay.api.request.AlipayUserAgreementUnsignRequest;
import com.alipay.api.request.AlipayUserCertdocCertverifyConsultRequest;
import com.alipay.api.request.AlipayUserCertdocCertverifyPreconsultRequest;
import com.alipay.api.response.AlipayDataBillAccountbookereceiptQueryResponse;
import com.alipay.api.response.AlipayDataBillEreceiptagentApplyResponse;
import com.alipay.api.response.AlipayFundAccountbookCreateResponse;
import com.alipay.api.response.AlipayFundAccountbookNotifyQueryResponse;
import com.alipay.api.response.AlipayFundAccountbookNotifySubscribeResponse;
import com.alipay.api.response.AlipayFundAccountbookQueryResponse;
import com.alipay.api.response.AlipayFundTransCommonQueryResponse;
import com.alipay.api.response.AlipayFundTransPagePayResponse;
import com.alipay.api.response.AlipayFundTransUniTransferResponse;
import com.alipay.api.response.AlipayUserAgreementPageSignResponse;
import com.alipay.api.response.AlipayUserAgreementQueryResponse;
import com.alipay.api.response.AlipayUserAgreementUnsignResponse;
import com.alipay.api.response.AlipayUserCertdocCertverifyConsultResponse;
import com.alipay.api.response.AlipayUserCertdocCertverifyPreconsultResponse;
import com.zhixianghui.common.statics.dto.banklink.ParticipantsInfo;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.facade.banklink.vo.report.AlipayJobPayslipSyncReqVo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.trade.enums.IdentityTypeEnum;
import com.zhixianghui.service.banklink.config.AlipayConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AliPayService {

    final private CertAlipayRequest certAlipayRequest;
    final private AlipayConfig alipayConfig;

    @Reference
    protected DataDictionaryFacade dictionaryFacade;

    private static final BigDecimal TO_ALIPAY_LIMIT = new BigDecimal("100000");
    private static final BigDecimal TO_BANKCARD_LIMIT = new BigDecimal("50000");

    /**
     * 支付宝就业到账啦明细同步
     * @param reqVo
     * @return
     * @throws AlipayApiException
     */
    public AlipayEbppIndustryJobPayslipSyncResponse jobPayslipSync(AlipayJobPayslipSyncReqVo reqVo) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        Date salaryTime;
        try {
            salaryTime = sdf.parse(reqVo.getCompleteTime());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        AlipayEbppIndustryJobPayslipSyncModel model = new AlipayEbppIndustryJobPayslipSyncModel();
        // uid参数未来计划废弃，存量商户可继续使用，新商户请使用openid。请根据应用-开发配置-openid配置选择支持的字段。
        model.setUserId(reqVo.getUserId());
        // 设置支付宝openid
        model.setOpenId(reqVo.getOpenId());
        // 设置支付宝登录号
        model.setLoginId(reqVo.getLoginId());
        // 设置外部业务编号
        model.setOutBizNo(reqVo.getPlatTrxNo());
        // 设置付款企业名称
        model.setCompanyName(reqVo.getMainstayName());
        // 设置企业信用代码
        model.setCompanyCertNo(reqVo.getMainstayCertNo());
        // 设置付款金额
        model.setAmount(reqVo.getAmount().toString());
        // 设置付款时间
        model.setSalaryTime(salaryTime);
        // 设置用户收款账号
        model.setCardNo(reqVo.getCardNo());
        // 设置用户收款渠道
        model.setChannel(reqVo.getChannel());
        // 设置客服电话
        model.setContactInfo(reqVo.getContactInfo());
        // 设置账单详情地址
        model.setBillDetailUrl(reqVo.getBillDetailUrl());
        // 设置付款银行简称
        model.setBankCode(reqVo.getBankCode());
        // 设置备注信息
        model.setRemark(reqVo.getRemark());
        // 设置用户姓名
        model.setUserName(reqVo.getUserName());

        AlipayEbppIndustryJobPayslipSyncRequest request = new AlipayEbppIndustryJobPayslipSyncRequest();
        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token
        // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

        AlipayEbppIndustryJobPayslipSyncResponse response = alipayClient.certificateExecute(request);
        log.info("[支付宝就业到账啦明细同步]，返回信息：[{}] mchOrderNo:{} platTrxNo:{}", JsonUtil.toString(response), reqVo.getMchOrderNo(), reqVo.getPlatTrxNo());
        return response;
    }

    /**
     * 查询订阅关系
     * @param agreementNo
     * @param accountBookId
     * @return
     * @throws AlipayApiException
     */
    public String querySubscribe(String agreementNo,String accountBookId) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();
        bizParams.put("agreement_no",agreementNo);
        bizParams.put("account_book_id",accountBookId);
        bizParams.put("product_code","SATF_FUND_BOOK");
        bizParams.put("biz_scene","DEFAULT");

        AlipayFundAccountbookNotifyQueryRequest request = new AlipayFundAccountbookNotifyQueryRequest();
        request.setBizContent(JSONObject.toJSONString(bizParams));
        AlipayFundAccountbookNotifyQueryResponse response = alipayClient.certificateExecute(request);
        return response.getBody();
    }

    /**
     * 订阅入金消息
     * @param agreementNo
     * @param accountBookId
     * @return
     * @throws AlipayApiException
     */
    public String subscribe(String agreementNo,String accountBookId) throws AlipayApiException{
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();
        bizParams.put("agreement_no",agreementNo);
        bizParams.put("account_book_id",accountBookId);
        bizParams.put("product_code","SATF_FUND_BOOK");
        bizParams.put("biz_scene","DEFAULT");

        AlipayFundAccountbookNotifySubscribeRequest request = new AlipayFundAccountbookNotifySubscribeRequest();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        AlipayFundAccountbookNotifySubscribeResponse response = alipayClient.certificateExecute(request);
        log.info("订阅支付宝入金消息，协议号：[{}]，记账本id：[{}]，返回信息：[{}]",agreementNo,accountBookId,response.getBody());
        return response.getBody();
    }

    /**
     * 用户协议签约的页面接口
     * @param extAgreementNo
     * @return
     * @throws AlipayApiException
     */
    public String sign(String extAgreementNo,Integer channelType) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();
        Map<String, Object> accessParam = new HashMap<>();
        accessParam.put("channel","QRCODE");
        bizParams.put("personal_product_code", "FUND_SAFT_SIGN_WITHHOLDING_P");
        bizParams.put("product_code", "FUND_SAFT_SIGN_WITHHOLDING");
        bizParams.put("sign_scene", "INDUSTRY|SATF_ACC");
        bizParams.put("access_params",accessParam);
        bizParams.put("external_agreement_no", extAgreementNo);
        bizParams.put("third_party_type", "PARTNER");
        AlipayUserAgreementPageSignRequest request = new AlipayUserAgreementPageSignRequest();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数

        String notifyUrl = alipayConfig.getNotifyUrl();
        //构建回调参数
        if (channelType != null){
            notifyUrl = notifyUrl + "?out_biz_channel_type=" + channelType.intValue();
        }
        request.setNotifyUrl(notifyUrl);
        AlipayUserAgreementPageSignResponse response = alipayClient.pageExecute(request,"get");//通过alipayClient调用API，获得对应的response类
        return response.getBody();
    }

    /**
     * 协议查询接口
     * @param extAgreementNo 商户签约号，对应签约时候传入的值。
     * @param agreementNo  支付宝协议号（用户签约成功后的协议号 ） ，如果传了该参数，其他参数会被忽略
     * @return
     * @throws AlipayApiException
     */
    public String agreementQuery(String extAgreementNo,String agreementNo) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        if (StringUtils.isBlank(extAgreementNo) && StringUtils.isBlank(agreementNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("签约号与协议号必传其一");
        }

        Map bizParams = new HashMap();
        bizParams.put("personal_product_code", "FUND_SAFT_SIGN_WITHHOLDING_P");
        bizParams.put("product_code", "FUND_SAFT_SIGN_WITHHOLDING");
        bizParams.put("sign_scene", "INDUSTRY|SATF_ACC");
        bizParams.put("external_agreement_no", extAgreementNo);
        bizParams.put("agreement_no", agreementNo);

        AlipayUserAgreementQueryRequest request = new AlipayUserAgreementQueryRequest ();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        AlipayUserAgreementQueryResponse response = alipayClient.certificateExecute(request);//通过alipayClient调用API，获得对应的response类
        return response.getBody();
    }


    /**
     * 协议解约接口
     * @param extAgreementNo 商户签约号，对应签约时候传入的值。
     * @param agreementNo  支付宝协议号（用户签约成功后的协议号 ） ，如果传了该参数，其他参数会被忽略
     * @return
     * @throws AlipayApiException
     */
    public String unsign(String extAgreementNo,String agreementNo) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        if (StringUtils.isBlank(extAgreementNo) && StringUtils.isBlank(agreementNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("签约号与协议号必传其一");
        }

        Map bizParams = new HashMap();
        bizParams.put("personal_product_code", "FUND_SAFT_SIGN_WITHHOLDING_P");
        bizParams.put("sign_scene", "INDUSTRY|SATF_ACC");
        bizParams.put("external_agreement_no", extAgreementNo);
        bizParams.put("agreement_no", agreementNo);

        AlipayUserAgreementUnsignRequest request = new AlipayUserAgreementUnsignRequest ();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        AlipayUserAgreementUnsignResponse response = alipayClient.certificateExecute(request);//通过alipayClient调用API，获得对应的response类
        return response.getBody();
    }


    /**
     * 记账本开通
     * @param agreementNo  协议编号
     * @param merchantNo   商户编号
     * @return
     * @throws AlipayApiException
     */
    public String accountBookCreate(String agreementNo,String merchantNo) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        Map bizParams = new HashMap();
        Map extInfo = new HashMap();
        extInfo.put("agreement_no", agreementNo);

        bizParams.put("merchant_user_id", merchantNo);
        bizParams.put("merchant_user_type", "BUSINESS_ORGANIZATION");
        bizParams.put("scene_code", "SATF_FUND_BOOK");
        bizParams.put("ext_info",extInfo);

        AlipayFundAccountbookCreateRequest request = new AlipayFundAccountbookCreateRequest ();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        AlipayFundAccountbookCreateResponse response = alipayClient.certificateExecute(request);//通过alipayClient调用API，获得对应的response类
        String body = response.getBody();
        log.info("支付宝创建记账本返回参数：[{}]",body);
        return body;
    }


    /**
     *  记账本查询
     * @param agreementNo
     * @param accountBookId
     * @return
     * @throws AlipayApiException
     */
    public String accountBookQuery(String agreementNo,String accountBookId) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        Map bizParams = new HashMap();
        Map extInfo = new HashMap();
        extInfo.put("agreement_no", agreementNo);

        bizParams.put("account_book_id", accountBookId);
        bizParams.put("scene_code", "SATF_FUND_BOOK");
        bizParams.put("ext_info",JSONObject.toJSONString(extInfo));

        AlipayFundAccountbookQueryRequest request = new AlipayFundAccountbookQueryRequest ();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        AlipayFundAccountbookQueryResponse response = alipayClient.certificateExecute(request);//通过alipayClient调用API，获得对应的response类
        return response.getBody();
    }

    /**
     * 资金专款拨入（商户自身给记账本充值）
     * @return
     * @throws AlipayApiException
     */
    public String transPay(String agreementNo,String accountBookId,String aliUserId,String amt,String outBizNo,String expireTime) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map extInfo = new HashMap();
        extInfo.put("agreement_no", agreementNo);
        extInfo.put("account_book_id", accountBookId);
        extInfo.put("accountbook_scene_code", "SATF_FUND_BOOK");

        Map bizParams = new HashMap();
        Map<String, Object> payeeInfo = new HashMap<>();
        payeeInfo.put("identity",aliUserId);
        payeeInfo.put("identity_type","ALIPAY_USER_ID");
        payeeInfo.put("ext_info",JSONObject.toJSONString(extInfo));

        bizParams.put("out_biz_no", outBizNo);
        bizParams.put("trans_amount", amt);
        bizParams.put("product_code", "FUND_ACCOUNT_BOOK");
        bizParams.put("biz_scene","SATF_DEPOSIT");
        bizParams.put("time_expire", expireTime);
        bizParams.put("payee_info", payeeInfo);
        bizParams.put("order_title", "代发专项充值");
        bizParams.put("remark", "代发专项充值");

        AlipayFundTransPagePayRequest request = new AlipayFundTransPagePayRequest();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        request.setNotifyUrl(alipayConfig.getNotifyUrl());
        AlipayFundTransPagePayResponse response = alipayClient.pageExecute(request,"get");//通过alipayClient调用API，获得对应的response类
        return response.getBody();
    }

    /**
     * 支付宝账单通用查询
     * @param outBizNo
     * @param bizScene
     * @param productCode
     * @return
     * @throws AlipayApiException
     */
    public String transCommonQuery(String outBizNo,String bizScene,String productCode) throws AlipayApiException{
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();
        bizParams.put("product_code", productCode);
        bizParams.put("biz_scene",bizScene);
        bizParams.put("out_biz_no", outBizNo);
        AlipayFundTransCommonQueryRequest request = new AlipayFundTransCommonQueryRequest();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        AlipayFundTransCommonQueryResponse  response = alipayClient.certificateExecute(request);//通过alipayClient调用API，获得对应的response类
        log.info("充值查询返回:{}", response.getBody());
        return response.getBody();
    }

    /**
     *
     * @return
     * @throws AlipayApiException
     */
    public String transCommonQueryByOrderId(String orderId) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();
        bizParams.put("order_id", orderId);
        AlipayFundTransCommonQueryRequest request = new AlipayFundTransCommonQueryRequest();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        AlipayFundTransCommonQueryResponse  response = alipayClient.certificateExecute(request);
        if (response.isSuccess()) {
            return response.getBody();
        } else if (StringUtils.equals("INVALID_PARAMETER",response.getSubCode())&&StringUtils.equals("参数有误order_id格式不正确！",response.getSubMsg())) {
            Map bizParams2 = new HashMap();
            bizParams2.put("pay_fund_order_id", orderId);
            request.setBizContent(JSONObject.toJSONString(bizParams2));//设置业务参数
            AlipayFundTransCommonQueryResponse  response2 = alipayClient.certificateExecute(request);
            if (response2.isSuccess()) {
                return response2.getBody();
            }else {
                log.error("外部订单号[pay_fund_order_id]-[{}]查询支付宝转账记录异常:{}-{}",orderId, response2.getSubCode(), response2.getSubMsg());
                throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("外部订单号查询支付宝转账记录异常：" + response2.getSubMsg());
            }
        } else {
            log.error("外部订单号[order_id]-[{}]查询支付宝转账记录异常:{}-{}",orderId, response.getSubCode(), response.getSubMsg());
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("外部订单号查询支付宝转账记录异常：" + response.getSubMsg());
        }
    }


    /**
     * 记账本充值订单查询
     * @param outBizNo
     * @return
     * @throws AlipayApiException
     */
    public String transQuery(String outBizNo) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        Map bizParams = new HashMap();
        bizParams.put("product_code", "FUND_ACCOUNT_BOOK");
        bizParams.put("biz_scene","COMANAGER_SATF_DEPOSIT");
        bizParams.put("out_biz_no", outBizNo);

        AlipayFundTransCommonQueryRequest request = new AlipayFundTransCommonQueryRequest();
        request.setBizContent(JSONObject.toJSONString(bizParams));//设置业务参数
        AlipayFundTransCommonQueryResponse  response = alipayClient.certificateExecute(request);//通过alipayClient调用API，获得对应的response类
        log.info("转账查询接口返回:{}", response.getBody());
        return response.getBody();
    }

    /**
     * 基于记账本单笔代发到支付宝账户
     * @param outBizNo 外部订单号
     * @param amt  调拨金额
     * @param title 转账业务的标题，用于在支付宝用户的账单里显示
     * @param payee
     * @param payer
     * @return
     * @throws AlipayApiException
     */
    public String withdraw(String outBizNo, String amt, String title, String realPayerName, ParticipantsInfo payee,ParticipantsInfo payer) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        Map<String, Object> payeeInfo = new HashMap<>();
        payeeInfo.put("identity", payee.getIdentity());
        payeeInfo.put("identity_type", "ALIPAY_USER_ID");

        Map<String, Object> payerExtInfo = new HashMap<>();
        payerExtInfo.put("agreement_no", payer.getAgreementNo());
        payerExtInfo.put("accountbook_scene_code", payer.getAccountbookSceneCode());

        Map<String, Object> payerInfo = new HashMap<>();
        payerInfo.put("identity", payer.getIdentity());
        payerInfo.put("identity_type", "ACCOUNT_BOOK_ID");
        payerInfo.put("ext_info", JSON.toJSONString(payerExtInfo));

        Map bizParams = new HashMap();
        bizParams.put("out_biz_no", outBizNo);
        bizParams.put("trans_amount", amt);
        bizParams.put("product_code", "SINGLE_TRANSFER_NO_PWD");
        bizParams.put("biz_scene", "ENTRUST_TRANSFER");
        bizParams.put("payee_info", payeeInfo);
        bizParams.put("order_title", title);
        bizParams.put("payer_info", payerInfo);
        bizParams.put("remark", title);
        bizParams.put("business_params", "{\"payer_show_name\":\""+realPayerName+"\"}");

        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        request.setBizContent(JSON.toJSONString(bizParams));
        request.setNotifyUrl(alipayConfig.getNotifyUrl());
        AlipayFundTransUniTransferResponse response = alipayClient.certificateExecute(request);
        log.info("发放到个人支付宝接口返回:{}",response.getBody());
        if (!response.isSuccess()) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg(response.getSubMsg());
        }
        return response.getBody();
    }

    /**
     * 提现到银行卡
     * @param bankOrderNo
     * @param receiveAmount
     * @param remitRemark
     * @param payee
     * @param payer
     * @return
     * @throws AlipayApiException
     */
    public String withdrawToBank(String bankOrderNo, String receiveAmount, String remitRemark, ParticipantsInfo payee, ParticipantsInfo payer) throws AlipayApiException{
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        Map bankCardExt = new HashMap();
        bankCardExt.put("account_type","1");
        bankCardExt.put("inst_name",payee.getOrganizationName());
        bankCardExt.put("bank_code",payee.getBankChannelNo());

        Map payeeInfoMap = new HashMap();
        payeeInfoMap.put("identity",payee.getIdentity());
        payeeInfoMap.put("identity_type","BANKCARD_ACCOUNT");
        payeeInfoMap.put("name",payee.getName());
        payeeInfoMap.put("bankcard_ext_info",bankCardExt);

        Map payerExtInfoMap = new HashMap();
        payerExtInfoMap.put("agreement_no",payer.getAgreementNo());
        payerExtInfoMap.put("accountbook_scene_code","SATF_FUND_BOOK");

        Map payerInfoMap = new HashMap();
        payerInfoMap.put("identity",payer.getIdentity());
        payerInfoMap.put("identity_type","ACCOUNT_BOOK_ID");
        payerInfoMap.put("ext_info",JSON.toJSONString(payerExtInfoMap));

        Map bizParams = new HashMap();
        bizParams.put("out_biz_no",bankOrderNo);
        bizParams.put("trans_amount",receiveAmount);
        bizParams.put("product_code","SINGLE_TRANSFER_NO_PWD");
        bizParams.put("biz_scene","ENTRUST_TRANSFER");
        bizParams.put("order_title",remitRemark);
        bizParams.put("remark",remitRemark);

        Map businessParams = new HashMap();
        businessParams.put("withdraw_timeliness","T0");
        bizParams.put("business_params",JSON.toJSONString(businessParams));
        bizParams.put("payee_info",payeeInfoMap);
        bizParams.put("payer_info",payerInfoMap);

        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        request.setBizContent(JSON.toJSONString(bizParams));
        request.setNotifyUrl(alipayConfig.getNotifyUrl());
        AlipayFundTransUniTransferResponse response = alipayClient.certificateExecute(request);
        log.info("支付宝提现到对公银行卡返回:{}", response.getBody());
        return response.getBody();
    }

    /**
     * 支付宝发放到对私银行卡
     * @param outBizNo
     * @param amount
     * @param title
     * @param payee
     * @param payer
     * @return
     * @throws AlipayApiException
     */
    public String uniBankCard(String outBizNo,String amount,String title,ParticipantsInfo payee,ParticipantsInfo payer) throws AlipayApiException {
        BigDecimal amt = new BigDecimal(amount);
        if (amt.compareTo(TO_BANKCARD_LIMIT) == 1){
            Map<String,Object> map = new HashMap<>();
            map.put("code", 99999);
            map.put("sub_msg","单笔额度超限，单笔限额为5万元");
            Map<String,Object> resp = new HashMap<>();
            resp.put("alipay_fund_trans_uni_transfer_response",map);
            return JsonUtil.toString(resp);
        }

        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        Map bankCardExt = new HashMap();
        bankCardExt.put("account_type","2");
        Map payeeInfoMap = new HashMap();
        payeeInfoMap.put("identity",payee.getIdentity());
        payeeInfoMap.put("identity_type","BANKCARD_ACCOUNT");
        payeeInfoMap.put("name",payee.getName());
        payeeInfoMap.put("bankcard_ext_info",bankCardExt);

        Map payerExtInfoMap = new HashMap();
        payerExtInfoMap.put("agreement_no",payer.getAgreementNo());
        payerExtInfoMap.put("accountbook_scene_code","SATF_FUND_BOOK");

        Map payerInfoMap = new HashMap();
        payerInfoMap.put("identity",payer.getIdentity());
        payerInfoMap.put("identity_type","ACCOUNT_BOOK_ID");
        payerInfoMap.put("ext_info",JSON.toJSONString(payerExtInfoMap));

        Map bizParams = new HashMap();
        bizParams.put("out_biz_no",outBizNo);
        bizParams.put("trans_amount",amount);
        bizParams.put("product_code","SINGLE_TRANSFER_NO_PWD");
        bizParams.put("biz_scene","ENTRUST_TRANSFER");
        bizParams.put("order_title",title);
        bizParams.put("remark",title);

        Map businessParams = new HashMap();
        businessParams.put("withdraw_timeliness","T0");
        bizParams.put("business_params",JSON.toJSONString(businessParams));
        bizParams.put("payee_info",payeeInfoMap);
        bizParams.put("payer_info",payerInfoMap);

        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        request.setBizContent(JSON.toJSONString(bizParams));
        request.setNotifyUrl(alipayConfig.getNotifyUrl());
        AlipayFundTransUniTransferResponse response = alipayClient.certificateExecute(request);
        log.info("支付宝发放到个人银行卡返回:{}", response.getBody());
        return response.getBody();
    }

    /**
     * 记账本之间相互调拨
     * @param outBizNo 外部订单号
     * @param amt  调拨金额
     * @param title 转账业务的标题，用于在支付宝用户的账单里显示
     * @param payee
     * @param payer
     * @return
     * @throws AlipayApiException
     */
    public String uniTransfer(String outBizNo, String amt, String title, ParticipantsInfo payee,ParticipantsInfo payer,String remark) throws AlipayApiException {

        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        Map<String, Object> extInfo = new HashMap<>();
        extInfo.put("agreement_no", payee.getAgreementNo());
        extInfo.put("accountbook_scene_code", "SATF_FUND_BOOK");


        Map<String, Object> payeeInfo = new HashMap<>();
        payeeInfo.put("identity", payee.getIdentity());
        payeeInfo.put("identity_type", "ACCOUNT_BOOK_ID");
        payeeInfo.put("ext_info", JSON.toJSONString(extInfo));

        Map<String, Object> payerExtInfo = new HashMap<>();
        payerExtInfo.put("agreement_no", payer.getAgreementNo());
        payerExtInfo.put("accountbook_scene_code", payer.getAccountbookSceneCode());

        Map<String, Object> payerInfo = new HashMap<>();
        payerInfo.put("identity", payer.getIdentity());
        payerInfo.put("identity_type", "ACCOUNT_BOOK_ID");
        payerInfo.put("ext_info", JSON.toJSONString(payerExtInfo));

        Map bizParams = new HashMap();
        bizParams.put("out_biz_no", outBizNo);
        bizParams.put("trans_amount", amt);
        bizParams.put("product_code", "SINGLE_TRANSFER_NO_PWD");
        bizParams.put("biz_scene", "ENTRUST_ALLOCATION");
        bizParams.put("payee_info", payeeInfo);
        bizParams.put("order_title", title);
        bizParams.put("payer_info", payerInfo);
        bizParams.put("remark", remark);

        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        request.setBizContent(JSON.toJSONString(bizParams));
        AlipayFundTransUniTransferResponse response = alipayClient.certificateExecute(request);
        log.info("支付宝转账接口返回:{}",response.getBody());

        return response.getBody();
    }


    /**
     * 基于记账本单笔代发到支付宝账户
     * @param outBizNo 外部订单号
     * @param amt  调拨金额
     * @param title 转账业务的标题，用于在支付宝用户的账单里显示
     * @param payee
     * @param payer
     * @return
     * @throws AlipayApiException
     */
    public String transferToAlipay(String outBizNo, String amt, String title, String realPayerName, ParticipantsInfo payee,ParticipantsInfo payer) throws AlipayApiException {
        //单笔大于10w超限
        BigDecimal amount = new BigDecimal(amt);
        if (amount.compareTo(TO_ALIPAY_LIMIT) == 1){
            Map<String,Object> map = new HashMap<>();
            map.put("code", 99999);
            map.put("sub_msg","单笔额度超限，单笔限额为10万元");
            Map<String,Object> resp = new HashMap<>();
            resp.put("alipay_fund_trans_uni_transfer_response",map);
            return JsonUtil.toString(resp);
        }
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);

        Map<String, Object> payeeInfo = new HashMap<>();

        String identity = payee.getIdentity();
        if (isValid2088Identity(identity)){
            payeeInfo.put("identity_type", IdentityTypeEnum.ALIPAY_USER_ID.getValue());
        }else {
            payeeInfo.put("identity_type", IdentityTypeEnum.ALIPAY_LOGON_ID.getValue());
        }
        payeeInfo.put("identity", identity);
        payeeInfo.put("name", payee.getName());

        Map<String, Object> payerExtInfo = new HashMap<>();
        payerExtInfo.put("agreement_no", payer.getAgreementNo());
        payerExtInfo.put("accountbook_scene_code", payer.getAccountbookSceneCode());

        Map<String, Object> payerInfo = new HashMap<>();
        payerInfo.put("identity", payer.getIdentity());
        payerInfo.put("identity_type", "ACCOUNT_BOOK_ID");
        payerInfo.put("ext_info", JSON.toJSONString(payerExtInfo));

        Map bizParams = new HashMap();
        bizParams.put("out_biz_no", outBizNo);
        bizParams.put("trans_amount", amt);
        bizParams.put("product_code", "SINGLE_TRANSFER_NO_PWD");
        bizParams.put("biz_scene", "ENTRUST_TRANSFER");
        bizParams.put("payee_info", payeeInfo);
        bizParams.put("order_title", title);
        bizParams.put("payer_info", payerInfo);
        bizParams.put("remark", title);

        String merchantsConfigKey = "payer_show_name_1";
        String merchants = dictionaryFacade.getSystemConfig(merchantsConfigKey);
        String payerShowName = "福利发放";

        if (StringUtils.isNotBlank(merchants) &&
                ListUtil.toList(merchants.split(",")).contains(payer.getEmployerNo())) {
            bizParams.put("business_params", "{\"payer_show_name\":\"" + payerShowName + "\"}");
        } else {
            bizParams.put("business_params", "{\"payer_show_name\":\"" + realPayerName + "\"}");
        }

        AlipayFundTransUniTransferRequest request = new AlipayFundTransUniTransferRequest();
        request.setBizContent(JSON.toJSONString(bizParams));
        request.setNotifyUrl(alipayConfig.getNotifyUrl());
        AlipayFundTransUniTransferResponse response = alipayClient.certificateExecute(request);
        log.info("接口返回:{}",response.getBody());

        return response.getBody();
    }

    /**
     * 判断2088开头的userId支付宝账号,并排除支付宝邮箱账号
     * @param identity
     * @return
     */
    private boolean isValid2088Identity(String identity) {
        return identity != null
                && identity.startsWith("2088")
                && !identity.contains("@");
    }

    /**
     * 实名证件信息比对验证预咨询
     * @param userName
     * @param certNo
     * @param mobile
     * @param logonId
     * @return
     * @throws AlipayApiException
     */
    public AlipayUserCertdocCertverifyPreconsultResponse preCertverify(String userName,String certNo,String mobile,String logonId) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();

        bizParams.put("user_name",userName);
        bizParams.put("cert_type","IDENTITY_CARD");
        bizParams.put("cert_no",certNo);
        bizParams.put("mobile",mobile);
        bizParams.put("logon_id",logonId);
        bizParams.put("ext_info","{}");

        AlipayUserCertdocCertverifyPreconsultRequest request = new AlipayUserCertdocCertverifyPreconsultRequest();
        request.setBizContent(JSON.toJSONString(bizParams));

        final AlipayUserCertdocCertverifyPreconsultResponse response = alipayClient.certificateExecute(request);
        log.info("接口返回:{}", response.getBody());
        return response;
    }

    /**
     *实名证件信息比对验证咨询
     * @param verifyId
     * @return
     * @throws AlipayApiException
     */
    public AlipayUserCertdocCertverifyConsultResponse confirmCertverify(String verifyId) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();

        bizParams.put("verify_id",verifyId);

        AlipayUserCertdocCertverifyConsultRequest request = new AlipayUserCertdocCertverifyConsultRequest();
        request.setBizContent(JSON.toJSONString(bizParams));

        final AlipayUserCertdocCertverifyConsultResponse response = alipayClient.certificateExecute(request);
        log.info("接口返回:{}", response.getBody());
        return response;
    }

    /**
     * 鉴权
     * @return
     */
    public AuthRespVo auth(AuthReqVo authReqVo) {

        try {
            final AlipayUserCertdocCertverifyPreconsultResponse preCertverifyResp = this.preCertverify(authReqVo.getName(), authReqVo.getIdCardNo(), authReqVo.getPhoneNo(), authReqVo.getBankAccountNo());
            String verifyId = preCertverifyResp.getVerifyId();
            if (!preCertverifyResp.isSuccess()) {
                AuthRespVo authRespVo = new AuthRespVo();
                authRespVo.setAuthStatus(BankAuthStatusEnum.FAIL.getValue());
                authRespVo.setBizDesc(preCertverifyResp.getMsg()+"["+preCertverifyResp.getSubMsg()+"]");
                authRespVo.setBizCode(preCertverifyResp.getCode());

                return authRespVo;
            }
            final AlipayUserCertdocCertverifyConsultResponse confirmCertverifyResp = this.confirmCertverify(verifyId);
            String passedStr =confirmCertverifyResp.getPassed();
            if (StringUtils.equals(passedStr, "T")) {
                AuthRespVo authRespVo = new AuthRespVo();
                authRespVo.setAuthStatus(BankAuthStatusEnum.SUCCESS.getValue());
                authRespVo.setBizDesc(confirmCertverifyResp.getMsg());
                authRespVo.setBizCode(confirmCertverifyResp.getCode());

                return authRespVo;
            }else {
                AuthRespVo authRespVo = new AuthRespVo();
                authRespVo.setAuthStatus(BankAuthStatusEnum.FAIL.getValue());
                authRespVo.setBizDesc(confirmCertverifyResp.getMsg()+"["+confirmCertverifyResp.getFailReason()+"]("+confirmCertverifyResp.getFailParams()+")");
                authRespVo.setBizCode(preCertverifyResp.getCode());
                return authRespVo;
            }

        } catch (Exception e) {
            log.error("[{}]请求支付宝鉴权结果异常:{}",authReqVo.getBankOrderNo(), e.getMessage());
            AuthRespVo authRespVo = new AuthRespVo();
            authRespVo.setAuthStatus(BankAuthStatusEnum.UN_KNOW.getValue());
            return authRespVo;
        }

    }

    public String billApply(String key,String agreementNo) throws AlipayApiException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();

        bizParams.put("type", "FUND_DETAIL");
        bizParams.put("key", key);
        bizParams.put("agreement_no", agreementNo);

        AlipayDataBillEreceiptagentApplyRequest req = new AlipayDataBillEreceiptagentApplyRequest();
        req.setBizContent(JSON.toJSONString(bizParams));
        AlipayDataBillEreceiptagentApplyResponse response = alipayClient.certificateExecute(req);
        if (response.isSuccess()) {
            return response.getFileId();
        }else {
            log.error("申请报价单出错:{}",JSONUtil.toJsonPrettyStr(response));
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg(response.getSubMsg());
        }
    }

    public String billQuery(String fileId,String agreementNo) throws AlipayApiException, BizException {
        AlipayClient alipayClient = new DefaultAlipayClient(certAlipayRequest);
        Map bizParams = new HashMap();
        bizParams.put("file_id", fileId);
        bizParams.put("agreement_no", agreementNo);
        AlipayDataBillAccountbookereceiptQueryRequest req = new AlipayDataBillAccountbookereceiptQueryRequest();
        req.setBizContent(JSON.toJSONString(bizParams));

        AlipayDataBillAccountbookereceiptQueryResponse response = alipayClient.certificateExecute(req);
        log.info("对账单查询原始结果:{}",JSONUtil.toJsonStr(response));
        if (response.isSuccess()){
            return response.getDownloadUrl();
        }else{
            log.error("查询电子回单出错:{}",JSONUtil.toJsonPrettyStr(response));
            throw CommonExceptions.COMMON_RETRY_ERROR.newWithErrMsg(response.getSubMsg());
        }
    }
}
