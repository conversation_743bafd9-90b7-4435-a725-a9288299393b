package com.zhixianghui.service.banklink.core.biz;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.service.banklink.core.dao.KeyPairRecordDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/27
 **/
@Service
public class KeyPairRecordBiz {
    @Autowired
    private KeyPairRecordDao keyPairRecordDao;

    /**
     * 数据已经存在则更新，不存在则新增
     * @param recordList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrInsertIfNotExist(List<KeyPairRecord> recordList){
        for(KeyPairRecord record : recordList){
            KeyPairRecord recordInDB = getByChannelNoAndChannelMchNo(record.getChannelNo(), record.getChannelMchNo());
            if(recordInDB == null){
                keyPairRecordDao.insert(record);
            } else {
                recordInDB.setEncryptKeyId(record.getEncryptKeyId());
                recordInDB.setChannelPublicKey(record.getChannelPublicKey());
                recordInDB.setMchPrivateKey(record.getMchPrivateKey());
                recordInDB.setMchPublicKey(record.getMchPublicKey());
                recordInDB.setUpdator(record.getUpdator());
                recordInDB.setChannelLoginUser(record.getChannelLoginUser());
                recordInDB.setChannelPlatNo(record.getChannelPlatNo());
                keyPairRecordDao.update(recordInDB);
            }
        }
    }

    /**
     * 根据渠道编号跟渠道商户号查询
     * @param channelNo     渠道编号
     * @param channelMchNo  渠道商户号
     * @return
     */
    public KeyPairRecord getByChannelNoAndChannelMchNo(String channelNo, String channelMchNo){
        if(StringUtil.isEmpty(channelNo) || StringUtil.isEmpty(channelMchNo)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("渠道编号或渠道商户编号不能为空");
        }
        return keyPairRecordDao.getByChannelNoAndChannelMchNo(channelNo, channelMchNo);
    }
}
