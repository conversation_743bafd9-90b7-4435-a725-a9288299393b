package com.zhixianghui.service.banklink.facade.sign;

import com.zhixianghui.facade.banklink.service.sign.ChannelSignFacade;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.components.ComponentsResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.*;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.OrgIdentityInfoResDateVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.*;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.service.banklink.core.biz.esign.EsignCoreBiz;
import com.zhixianghui.service.banklink.request.sign.EsignBizV3;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-05 15:09
 **/
@Service(timeout = 9000)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ChannelSignFacadeImpl implements ChannelSignFacade {

//    private final EsignBiz biz;

    private final EsignBizV3 bizV3;

    private final EsignCoreBiz esignCoreBiz;



    @Override
    public EsignResVo<CreateSignTemplateResV3DataVo> createSignTemplate(CreateSignTemplateReqVo createSignTemplateReqVo) {
        return bizV3.createSignTemplate(createSignTemplateReqVo);

    }

    @Override
    public EsignResVo<CreateTemplateResV3DataVo> createTemplate(CreateTemplateReqVo createTemplateReqVo) {
        return bizV3.createTemplate(createTemplateReqVo);
    }

    @Override
    public EsignResVo<EditTemplateResV3DataVo> editTemplate(EditTemplateReqVo createTemplateReqVo) {
        return bizV3.editTemplate(createTemplateReqVo);
    }

    @Override
    public boolean uploadFile(UploadFileReqVo uploadFileReqVo, String uploadUrl, byte[] buffer) {
        return bizV3.uploadFile(uploadFileReqVo, uploadUrl, buffer);
    }

//    @Override
//    public EsignResVo<List<String>> addTemplate(TemplateComponentsReqVo templateComponentsReqVo) {
//        return biz.addTemplate(templateComponentsReqVo);
//    }


//    @Override
//    public EsignResVo<TemplateStatusResDataVo> existTemplate(TemplateStatus reqVo) {
//        return biz.existTemplate(reqVo);
//    }

    @Override
    public EsignResVo<GetPersonalSignatureResDataVo> personalSignature(PersonalSignatureReqVo reqVo) {
        return bizV3.personalSignature(reqVo);
    }

//    @Override
//    public List<KeywordPosVo> getPos(CheckFilePosVo checkFilePosVo) {
//        return biz.getPos(checkFilePosVo);
//    }

    @Override
    public EsignResVo<CreateFileUploadUrlResVo> getUploadUrl(CreateFileUploadUrlVo createFileUploadUrlVo) {
        return bizV3.getUploadUrl(createFileUploadUrlVo);
    }

//    @Override
//    public EsignResVo delayFlow(DelayFlowReqVo delayFlowReqVo) {
//        return biz.delayFlow(delayFlowReqVo);
//    }

    @Override
    public EsignResVo revokeFlow(RevokeFlowVoV3 revokeFlowVo) {
        return bizV3.revokeFlow(revokeFlowVo);
    }

    /**
     * 个人账户创建
     * @param signCreatePersonReqVo  个人账户创建reqVo
     * @return 个人账户创建结果
     */
//    @Override
//    public EsignResVo<SignCreatePersonResDataVo> createPersonByThirdPartyUserId(SignCreatePersonReqVo signCreatePersonReqVo){
//        return biz.createPersonByThirdPartyUserId(signCreatePersonReqVo);
//    }

    /**
     * 机构账号创建
     * @param signCreateOrganizationReqVo 机构账号创建reqVo
     * @return 机构账号创建结果
     */
//    @Override
//    public EsignResVo<SignCreateOrganizationResDataVo> createOrganization(SignCreateOrganizationReqVo signCreateOrganizationReqVo){
//        return biz.createOrganization(signCreateOrganizationReqVo);
//    }

    /**
     * 查询模板详情
     * @param signTemplateReqVo 查询模板详情ReqVo
     * @return 查询模板详情结果
     */
    @Override
    public EsignResVo<SignTemplateResDataVoV3> getSignTemplate(SignTemplateReqVo signTemplateReqVo){
        return bizV3.getSignTemplate(signTemplateReqVo);
    }

    /**
     * 设置静默签署
     * @param signAuthReqVo  设置静默签署ReqVo
     * @return 设置静默签署结果
     */
//    @Override
//    public EsignResVo<Boolean> signAuth(SignAuthReqVo signAuthReqVo){
//        return biz.signAuth(signAuthReqVo);
//    }

    /**
     * 通过模板创建文件
     * @param createFileByTemplateReqVo  通过模板创建文件ReqVo
     * @return 通过模板创建文件结果
     */
    @Override
    public EsignResVo<CreateFileByTemplateResDataVoV3> createFileByTemplate(CreateFileByTemplateReqVoV3 createFileByTemplateReqVo){
        return bizV3.createFileByTemplate(createFileByTemplateReqVo);
    }

    @Override
    public EsignResVo<CreateByFileResDateVo> createByFile(CreateByFileReqVo createByFileReqVo) {
        return bizV3.createByFile(createByFileReqVo);
    }

    /**
     * 一步发起签署
     * @param createFlowOneStepReqVo  一步发起签署ReqVo
     * @return 一步发起签署结果
     */
//    @Override
//    public EsignResVo<CreateFlowOneStepResDataVo> createFlowOneStep(CreateFlowOneStepReqVo createFlowOneStepReqVo){
//        return biz.createFlowOneStep(createFlowOneStepReqVo);
//    }

    /**
     * 获取签署地址
     * @param executeUrlReqVo  获取签署地址ReqVo
     * @return 获取签署地址结果
     */
//    @Override
//    public EsignResVo<ExecuteUrlResDataVo> getExecuteUrl(ExecuteUrlReqVo executeUrlReqVo){
//        return biz.getExecuteUrl(executeUrlReqVo);
//    }

    /**
     * 获取签署地址
     * @param executeUrlReqVo  获取签署地址ReqVo
     * @return 获取签署地址结果
     */
    @Override
    public EsignResVo<ExecuteUrlResDataVo> getExecuteUrlV3(ExecuteUrlReqVoV3 executeUrlReqVo){
        return bizV3.getExecuteUrl(executeUrlReqVo);
    }

    /**
     * 签署流程查询
     * @param signFlowReqVo  签署流程查询ReqVo
     * @return 签署流程查询结果
     */
//    @Override
//    public EsignResVo<SignFlowResDataVo> getSignFlow(SignFlowReqVo signFlowReqVo){
//        return biz.getSignFlow(signFlowReqVo);
//    }

    /**
     * 流程文档下载
     * @param documentDownloadReqVo  流程文档下载ReqVo
     * @return 流程文档下载结果
     */
    @Override
    public EsignResVo<DocumentDownloadResDataVo> getDocumentDownloadUrl(DocumentDownloadReqVo documentDownloadReqVo){
        return bizV3.getDocumentDownloadUrl(documentDownloadReqVo);
    }

    @Override
    public EsignResVo<PreviewFileDownloadResDataVo> getPreviewDownloadUrl(PreviewFileDownloadReqVo documentDownloadReqVo) {
        return bizV3.getPreviewDownloadUrl(documentDownloadReqVo);
    }

    @Override
    public String downLoadFile(String logFlag, String fileName, String fileUrl) throws IOException {
        return bizV3.downLoadFile(logFlag,fileName,fileUrl);
    }

//    @Override
//    public EsignResVo<UpdatePersonInfoResDataVo> updatePersonInfo(UpdatePersonInfoReqVo updatePersonInfoReqVo){
//        return biz.updatePersonInfo(updatePersonInfoReqVo);
//    }

//    @Override
//    public AccountCreateResponse createPersonalAccount(SignCreatePersonReqVo signCreatePersonReqVo, boolean autoSign) {
//        return esignCoreBiz.createPersonalAccount(signCreatePersonReqVo, autoSign);
//    }

//    @Override
//    public List<StructComponent> getTemplateInfo(String templateId) {
//        return biz.getTemplateInfo(templateId);
//    }

    @Override
    public EsignResVo<TemplatePageResDataResVo> getTemplatePage(EsignPageVo esignPageVo) {
        return bizV3.getAllTemplateInfo(esignPageVo);
    }

    @Override
    public EsignResVo<OrgIdentityInfoResDataVo> getOrgIdentityInfoV3(OrgIdentityInfoResDateVo orgIdentityInfoResDateVo) {
        return bizV3.OrgIdentityInfoV3(orgIdentityInfoResDateVo);
    }

    @Override
    public EsignResVo<ComponentsResDataVo> customComponentsList(EsignPageVo esignPageVo) {
        return bizV3.customComponentsList(esignPageVo);
    }

    @Override
    public EsignResVo<?> delComponentsList(DelComponentReqVo delComponentReqVo) {
        return bizV3.delComponentsList(delComponentReqVo);
    }

    @Override
    public EsignResVo<?> createComponentsList(CreateComponentReqVo reqVo) {
        return bizV3.createComponentsList(reqVo);
    }

    @Override
    public EsignResVo<PsnIdentityInfoResDataVo> getPsnIdentityInfoV3(PsnIdentityInfoReqVo setPsnAccount) {
        return bizV3.getPsnIdentityInfoV3(setPsnAccount);
    }

    @Override
    public EsignResVo<GetPsnSealListResDataVo> getPsnSealList(PsnSealListReqVo reqVo) {
        return bizV3.getPsnSealList(reqVo);
    }
}
