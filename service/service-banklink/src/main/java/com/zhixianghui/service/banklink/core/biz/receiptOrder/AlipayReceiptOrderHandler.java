package com.zhixianghui.service.banklink.core.biz.receiptOrder;

import cn.hutool.http.HttpUtil;
import com.alipay.api.AlipayApiException;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.service.banklink.request.alipay.AliPayService;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AlipayReceiptOrderHandler
 * @Description TODO
 * @Date 2023/6/29 11:31
 */
@Component
public class AlipayReceiptOrderHandler extends AbstractReceiptOrderHandler{

    @Autowired
    private AliPayService aliPayService;

    @Override
    public Map<String, Object> applyOrder(String remitPlatTrxNo, String employerNo, String mainstayNo, Integer channelType) {
        EmployerAccountInfo employerAccountInfo = getAccountInfo(employerNo,mainstayNo,channelType);
        //只需要获取个人电子回单
        try {
            String fileId = aliPayService.billApply(remitPlatTrxNo + "P",employerAccountInfo.getSubAgreementNo());
            Map<String,Object> resultMap = new HashMap<>();
            resultMap.put("fileId",fileId);
            resultMap.put("agreementNo",employerAccountInfo.getSubAgreementNo());
            return resultMap;
        } catch (AlipayApiException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求电子回单失败");
        }
    }

    @Override
    protected File download(Map<String, Object> paramMap) {
        String fileId = (String) paramMap.get("fileId");
        String agreementNo = (String) paramMap.get("agreementNo");
        String remitPlatTrxNo = (String) paramMap.get("remitPlatTrxNo");
        try {
            String fileUrl = aliPayService.billQuery(fileId,agreementNo);
            File file = FileUtils.createFile(TEMP_PATH + "电子凭证" + remitPlatTrxNo + "-" + (new Date()).getTime() + FILE_SUFFIX);
            HttpUtil.downloadFile(fileUrl, file);
            return file;
        } catch (AlipayApiException e) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取支付宝电子回单异常");
        }
    }

}
