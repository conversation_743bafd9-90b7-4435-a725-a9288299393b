<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.employee.mapper.InvoiceTitleInfoMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.employee.entity.InvoiceTitleInfo">
    <!--@mbg.generated-->
    <!--@Table tbl_invoice_title_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tax_no" jdbcType="VARCHAR" property="taxNo" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_address" jdbcType="VARCHAR" property="companyAddress" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile" />
    <result column="is_default" jdbcType="BOOLEAN" property="isDefault" />
    <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tax_no, company_name, company_address, bank_name, bank_account_no, contact_mobile,
    is_default, id_card_no, create_time, update_time
  </sql>
</mapper>
