<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.employee.mapper.InvoicePostAddressMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.employee.entity.InvoicePostAddress">
    <!--@mbg.generated-->
    <!--@Table tbl_invoice_post_address-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="province_no" jdbcType="VARCHAR" property="provinceNo" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_no" jdbcType="VARCHAR" property="cityNo" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="area_no" jdbcType="VARCHAR" property="areaNo" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="is_default" jdbcType="BOOLEAN" property="isDefault" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, mobile, province_no, province_name, city_no, city_name, area_no, area_name,
    address, is_default, create_time, update_time, id_card_no
  </sql>
</mapper>
