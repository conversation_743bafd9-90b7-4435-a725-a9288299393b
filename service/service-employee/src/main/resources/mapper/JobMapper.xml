<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.employee.mapper.JobMapper">
    <resultMap type="com.zhixianghui.facade.employee.entity.Job" id="JobResult">
        <result property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="industryCode" column="industry_code"/>
        <result property="industryName" column="industry_name"/>
        <result property="jobId" column="job_id"/>
        <result property="jobName" column="job_name"/>
        <result property="employerNo" column="employer_no"/>
        <result property="employerName" column="employer_name"/>
        <result property="jobTag" column="job_tag"/>
        <result property="jobAvalDateType" column="job_aval_date_type"/>
        <result property="jobStartDate" column="job_start_date"/>
        <result property="jobFinishDate" column="job_finish_date"/>
        <result property="jobTimeStart" column="job_time_start"/>
        <result property="jobTimeEnd" column="job_time_end"/>
        <result property="jobProvinceNo" column="job_province_no"/>
        <result property="jobProvinceName" column="job_province_name"/>
        <result property="jobCityNo" column="job_city_no"/>
        <result property="jobCityName" column="job_city_name"/>
        <result property="jobAreaNo" column="job_area_no"/>
        <result property="jobAreaName" column="job_area_name"/>
        <result property="jobAddress" column="job_address"/>
        <result property="workerNum" column="worker_num"/>
        <result property="employedNum" column="employed_num"/>
        <result property="rewardType" column="reward_type"/>
        <result property="payType" column="pay_type"/>
        <result property="rewardAmount" column="reward_amount"/>
        <result property="subsidyAmount" column="subsidy_amount"/>
        <result property="workerGender" column="worker_gender"/>
        <result property="workerAgeLimitType" column="worker_age_limit_type"/>
        <result property="workerAgeLimitMin" column="worker_age_limit_min"/>
        <result property="workerAgeLimitMax" column="worker_age_limit_max"/>
        <result property="eduBackground" column="edu_background"/>
        <result property="professionalSkill" column="professional_skill"/>
        <result property="jobDescribe" column="job_describe"/>
        <result property="deliveryStandard" column="delivery_standard"/>
        <result property="acceptMode" column="accept_mode"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="jobStatus" column="job_status"/>
        <result property="scope" column="scope"/>
        <result property="isDelete" column="is_delete"/>
        <result property="workCategoryName" column="work_category_name"/>
        <result property="workCategoryCode" column="work_category_code"/>
        <result property="appCodeUrl" column="app_code_url"/>
        <result property="callbackUrl" column="callback_url"/>
    </resultMap>

    <sql id="selectJobVo">

        select id, version, industry_code, industry_name, job_id, job_name, employer_no, employer_name, job_tag, job_aval_date_type, job_start_date, job_finish_date, job_time_start, job_time_end, job_province_no, job_province_name, job_city_no, job_city_name,job_area_no,job_area_name, job_address, worker_num,employed_num, reward_type, pay_type, reward_amount, subsidy_amount, worker_gender, worker_age_limit_type, worker_age_limit_min, worker_age_limit_max, edu_background, professional_skill, job_describe, delivery_standard, accept_mode, create_time, update_time, create_by, update_by, job_status, scope,is_delete,work_category_code,work_category_name,app_code_url,callback_url from tbl_job

    </sql>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, version, industry_code, industry_name, job_id, job_name, employer_no, employer_name,
        job_tag, job_aval_date_type, job_start_date, job_finish_date, job_time_start, job_time_end,
        job_province_no, job_province_name, job_city_no, job_city_name,job_area_no,job_area_name, job_address,
        worker_num,employed_num,
        reward_type, pay_type, reward_amount, subsidy_amount, worker_gender, worker_age_limit_type,
        worker_age_limit_min, worker_age_limit_max, edu_background, professional_skill, job_describe,
        delivery_standard, accept_mode,create_time,create_by,update_time,update_by,job_status,scope,is_delete,
        work_category_name,work_category_code,app_code_url,callback_url
    </sql>

    <select id="listBy" parameterType="java.util.Map" resultMap="JobResult">


        select
        <include refid="Base_Column_List"/>
         from tbl_job

        <where>
            <if test="param.jobId != null and param.jobId != ''">
                and job_id LIKE CONCAT('%', CONCAT(#{param.jobId,jdbcType=VARCHAR}, '%'))

            </if>
            <if test="param.jobName != null and param.jobName != ''">
                and job_name LIKE CONCAT('%', CONCAT(#{param.jobName,jdbcType=VARCHAR}, '%'))

            </if>
            <if test="param.industryCode != null  and param.industryCode != ''">

                and industry_code = #{param.industryCode,jdbcType=VARCHAR}

            </if>
            <if test="param.jobStatus != null">

                and job_status = #{param.jobStatus,jdbcType=INTEGER}

            </if>
            <if test="param.acceptMode != null">

                and accept_mode = #{param.acceptMode,jdbcType=INTEGER}

            </if>
            <if test="param.employerNo != null and param.employerNo != ''">

                and employer_no = #{param.employerNo,jdbcType=VARCHAR}

            </if>
            <if test="param.createTimeBegin != null">

                and create_time <![CDATA[>=]]> #{param.createTimeBegin,jdbcType=TIMESTAMP}

            </if>
            <if test="param.createTimeEnd != null">

                and create_time <![CDATA[ < ]]> #{param.createTimeEnd,jdbcType=TIMESTAMP}

            </if>
            <if test="param.employerNameLike != null and param.employerNameLike !=''">
                and employer_Name like concat('%',#{param.employerNameLike,jdbcType=VARCHAR},'%')
            </if>
            and is_delete = 0
        </where>
        <choose>
            <when test="param.sortColumns != null and param.sortColumns != ''">
                <![CDATA[
                 ORDER BY ${param.sortColumns}
                ]]>
            </when>
            <otherwise>
                <![CDATA[
                 ORDER BY ID DESC
                ]]>
            </otherwise>
        </choose>
    </select>

    <select id="selectJobList" parameterType="com.zhixianghui.facade.employee.dto.JobWebQueryDto" resultMap="JobResult">
        <include refid="selectJobVo"/>
        <where>
            <if test="industryCode != null  and industryCode != ''">
                and industry_code = #{industryCode}
            </if>
            <if test="industryName != null  and industryName != ''">
                 and industry_name like concat('%', #{industryName}, '%')
            </if>
            <if test="jobId != null  and jobId != ''">
                 and job_id = #{jobId}
            </if>
            <if test="jobName != null  and jobName != ''">
                 and job_name like concat('%', #{jobName}, '%')
            </if>
            <if test="employerNo != null  and employerNo != ''">
                 and employer_no = #{employerNo}
            </if>
            <if test="employerName != null  and employerName != ''">
                 and employer_name like concat('%', #{employerName}, '%')
            </if>
            <if test="jobTag != null  and jobTag != ''">
                 and job_tag = #{jobTag}
            </if>
            <if test="jobAvalDateType != null">
                 and job_aval_date_type = #{jobAvalDateType}
            </if>
            <if test="jobStartDate != null">
                 and job_start_date = #{jobStartDate}
            </if>
            <if test="jobFinishDate != null">
                 and job_finish_date = #{jobFinishDate}
            </if>
            <if test="jobTimeStart != null">
                 and job_time_start = #{jobTimeStart}
            </if>
            <if test="jobTimeEnd != null">
                 and job_time_end = #{jobTimeEnd}
            </if>
            <if test="jobProvinceNo != null">
                 and job_province_no = #{jobProvinceNo}
            </if>
            <if test="jobProvinceName != null  and jobProvinceName != ''">
                 and job_province_name like concat('%', #{jobProvinceName}, '%')
            </if>
            <if test="jobCityNo != null">
                 and job_city_no = #{jobCityNo}
            </if>
            <if test="jobCityName != null  and jobCityName != ''">
                 and job_city_name like concat('%', #{jobCityName}, '%')
            </if>
            <if test="jobAreaNo != null">
                 and job_area_no = #{jobAreaNo}
            </if>
            <if test="jobAreaName != null  and jobAreaName != ''">
                 and job_area_name like concat('%', #{jobAreaName}, '%')
            </if>
            <if test="jobAddress != null  and jobAddress != ''">
                 and job_address = #{jobAddress}
            </if>
            <if test="workerNum != null">
                 and worker_num = #{workerNum}
            </if>
            <if test="employedNum != null">
                 and employed_num = #{employedNum}
            </if>
            <if test="rewardType != null">
                 and reward_type = #{rewardType}
            </if>
            <if test="payType != null">
                 and pay_type = #{payType}
            </if>
            <if test="rewardAmount != null">
                 and reward_amount = #{rewardAmount}
            </if>
            <if test="subsidyAmount != null">
                 and subsidy_amount = #{subsidyAmount}
            </if>
            <if test="workerGender != null">
                 and worker_gender = #{workerGender}
            </if>
            <if test="workerAgeLimitType != null">
                 and worker_age_limit_type = #{workerAgeLimitType}
            </if>
            <if test="workerAgeLimitMin != null">
                 and worker_age_limit_min = #{workerAgeLimitMin}
            </if>
            <if test="workerAgeLimitMax != null">
                 and worker_age_limit_max = #{workerAgeLimitMax}
            </if>
            <if test="eduBackground != null">
                 and edu_background = #{eduBackground}
            </if>
            <if test="professionalSkill != null  and professionalSkill != ''">
                 and professional_skill = #{professionalSkill}
            </if>
            <if test="jobDescribe != null  and jobDescribe != ''">
                 and job_describe = #{jobDescribe}
            </if>
            <if test="deliveryStandard != null  and deliveryStandard != ''">
                 and delivery_standard = #{deliveryStandard}
            </if>
            <if test="acceptMode != null">
                 and accept_mode = #{acceptMode}
            </if>
            <if test="jobStatus != null">
                 and job_status = #{jobStatus}
            </if>
            <if test="scope != null">
                 and scope = #{scope}
            </if>
            <if test="jobIds != null">

                and job_id in

                <foreach collection="jobIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>

            and is_delete = 0

            <if test="keyword != null">

                and (industry_name like concat('%', #{keyword}, '%')
                or job_name like concat('%', #{keyword}, '%'))
            </if>
        </where>

        order by create_time desc

    </select>

    <select id="selectJobById" parameterType="Long" resultMap="JobResult">
        <include refid="selectJobVo"/>

        where id = #{id}

    </select>

    <select id="listJobListOnGrant" parameterType="java.util.Map" resultMap="JobResult">
        select
        <include refid="Base_Column_List"/>
        from tbl_job
        where
        employer_no=#{employerNo,jdbcType=VARCHAR}
        and (
            (job_aval_date_type =100 and job_status =200 )
            or
            job_status =300
        )
        ORDER BY ID DESC
    </select>

    <update id="updateJob" parameterType="com.zhixianghui.facade.employee.entity.Job">

        update tbl_job

        <trim prefix="SET" suffixOverrides=",">
            version = #{version}+1,

            <if test="version != null">
                version = #{version},
            </if>
            <if test="industryCode != null">
                industry_code = #{industryCode},
            </if>
            <if test="industryName != null">
                industry_name = #{industryName},
            </if>
            <if test="jobId != null">
                job_id = #{jobId},
            </if>
            <if test="jobName != null">
                job_name = #{jobName},
            </if>
            <if test="employerNo != null">
                employer_no = #{employerNo},
            </if>
            <if test="employerName != null">
                employer_name = #{employerName},
            </if>
            <if test="jobTag != null">
                job_tag = #{jobTag},
            </if>
            <if test="jobAvalDateType != null">
                job_aval_date_type = #{jobAvalDateType},
            </if>
            <if test="jobStartDate != null">
                job_start_date = #{jobStartDate},
            </if>
            <if test="jobFinishDate != null">
                job_finish_date = #{jobFinishDate},
            </if>
            <if test="jobTimeStart != null">
                job_time_start = #{jobTimeStart},
            </if>
            <if test="jobTimeEnd != null">
                job_time_end = #{jobTimeEnd},
            </if>
            <if test="jobProvinceNo != null">
                job_province_no = #{jobProvinceNo},
            </if>
            <if test="jobProvinceName != null">
                job_province_name = #{jobProvinceName},
            </if>
            <if test="jobCityNo != null">
                job_city_no = #{jobCityNo},
            </if>
            <if test="jobCityName != null">
                job_city_name = #{jobCityName},
            </if>
            <if test="jobAreaNo != null">
                job_area_no = #{jobAreaNo},
            </if>
            <if test="jobAreaName != null">
                job_area_name = #{jobAreaName},
            </if>
            <if test="jobAddress != null">
                job_address = #{jobAddress},
            </if>
            <if test="workerNum != null">
                worker_num = #{workerNum},
            </if>
            <if test="employedNum != null">
                employed_num = #{employedNum},
            </if>
            <if test="rewardType != null">
                reward_type = #{rewardType},
            </if>
            <if test="payType != null">
                pay_type = #{payType},
            </if>
            <if test="rewardAmount != null">
                reward_amount = #{rewardAmount},
            </if>
            <if test="subsidyAmount != null">
                subsidy_amount = #{subsidyAmount},
            </if>
            <if test="workerGender != null">
                worker_gender = #{workerGender},
            </if>
            <if test="workerAgeLimitType != null">
                worker_age_limit_type = #{workerAgeLimitType},
            </if>
            <if test="workerAgeLimitMin != null">
                worker_age_limit_min = #{workerAgeLimitMin},
            </if>
            <if test="workerAgeLimitMax != null">
                worker_age_limit_max = #{workerAgeLimitMax},
            </if>
            <if test="eduBackground != null">
                edu_background = #{eduBackground},
            </if>
            <if test="professionalSkill != null">
                professional_skill = #{professionalSkill},
            </if>
            <if test="jobDescribe != null">
                job_describe = #{jobDescribe},
            </if>
            <if test="deliveryStandard != null">
                delivery_standard = #{deliveryStandard},
            </if>
            <if test="acceptMode != null">
                accept_mode = #{acceptMode},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="jobStatus != null">
                job_status = #{jobStatus},
            </if>
            <if test="scope != null">
                scope = #{scope},
            </if>
            <if test="workCategoryCode != null and workCategoryCode != ''">

                work_category_code = #{workCategoryCode},

            </if>
            <if test="workCategoryName != null and workCategoryName != ''">

                work_category_name = #{workCategoryName},
            </if>
            <if test="appCodeUrl != null and appCodeUrl != ''">
                app_code_url = #{appCodeUrl},
            </if>
            <if test="callbackUrl != null and callbackUrl != ''">
                callback_url = #{callbackUrl},
            </if>
        </trim>

        where id = #{id} and version = #{version}

    </update>

    <delete id="deleteJobByIds" parameterType="String">

        delete from tbl_job where id in

        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>















