<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhixianghui.service.employee.mapper.JobWorkerRecordMapper">
  <resultMap id="BaseResultMap" type="com.zhixianghui.facade.employee.entity.JobWorkerRecord">
    <!--@mbg.generated-->
    <!--@Table tbl_job_worker_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="version" jdbcType="SMALLINT" property="version" />
    <result column="job_id" jdbcType="VARCHAR" property="jobId" />
    <result column="job_name" jdbcType="VARCHAR" property="jobName" />
    <result column="employer_no" jdbcType="VARCHAR" property="employerNo" />
    <result column="employer_name" jdbcType="VARCHAR" property="employerName" />
    <result column="worker_name" jdbcType="VARCHAR" property="workerName" />
    <result column="worker_phone" jdbcType="VARCHAR" property="workerPhone" />
    <result column="worker_idcard" jdbcType="VARCHAR" property="workerIdcard" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="edu_background" jdbcType="INTEGER" property="eduBackground" />
    <result column="auth_status" jdbcType="INTEGER" property="authStatus" />
    <result column="job_status" jdbcType="INTEGER" property="jobStatus" />
    <result column="delivery_status" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="settle_status" jdbcType="INTEGER" property="settleStatus" />
    <result column="is_assign" jdbcType="INTEGER" property="isAssign" />
    <result column="job_accept_time" jdbcType="TIMESTAMP" property="jobAcceptTime" />
    <result column="job_finish_time" jdbcType="TIMESTAMP" property="jobFinishTime" />
    <result column="job_settle_time" jdbcType="TIMESTAMP" property="jobSettleTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="attachment" jdbcType="VARCHAR" property="attachment" />
    <result column="delivery_content" jdbcType="LONGVARCHAR" property="deliveryContent" />
    <result column="fail_reason" jdbcType="LONGVARCHAR" property="failReason" />
    <result column="result_signature_url" jdbcType="VARCHAR" property="resultSignatureUrl" />
    <result column="deliver_signature_url" jdbcType="VARCHAR" property="deliverSignatureUrl" />
    <result column="mini_open_id" jdbcType="VARCHAR" property="miniOpenId" />
    <result column="mini_app_id" jdbcType="VARCHAR" property="miniAppId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, version, job_id, job_name, employer_no, employer_name, worker_name, worker_phone,
    worker_idcard, gender, edu_background, auth_status, job_status, delivery_status,
    settle_status,is_assign, job_accept_time, job_finish_time,job_settle_time, create_time, attachment,
    delivery_content,fail_reason,result_signature_url,deliver_signature_url,mini_open_id,mini_app_id
  </sql>

  <select id="listBy" parameterType="java.util.Map" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from tbl_job_worker_record
    <where>
      <if test="param.workerName != null and param.workerName !='' ">
        and worker_name LIKE CONCAT('%', CONCAT(#{param.workerName,jdbcType=VARCHAR}, '%'))
      </if>
      <if test="param.workerPhone !=null and param.workerPhone!='' ">
        and worker_phone = #{param.workerPhone,jdbcType=VARCHAR}
      </if>
      <if test="param.jobName !=null and param.jobName != '' ">
        and job_name LIKE CONCAT('%', CONCAT(#{param.jobName,jdbcType=VARCHAR}, '%'))
      </if>
      <if test="param.jobId !=null and param.jobId != '' ">
        and job_id = #{param.jobId,jdbcType=VARCHAR}
      </if>
      <if test="param.employerNo !=null and param.employerNo !=''">
        and employer_no = #{param.employerNo,jdbcType=VARCHAR}
      </if>
      <if test="param.jobStatus !=null ">
        and job_status = #{param.jobStatus,jdbcType=INTEGER}
      </if>
      <if test="param.settleStatus !=null ">
        and settle_status = #{param.settleStatus,jdbcType=INTEGER}
      </if>
      <if test="param.deliveryStatus !=null ">
        and delivery_status = #{param.deliveryStatus,jdbcType=INTEGER}
      </if>
      <if test="param.isAssign !=null ">
        and is_assign = #{param.isAssign,jdbcType=INTEGER}
      </if>
      <if test="param.deliverStatusList != null and param.deliverStatusList.size() > 0">
        and delivery_status IN
        <foreach collection="param.deliverStatusList" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=INTEGER}</foreach>
      </if>
      <if test="param.ids != null and param.ids.size() > 0">
        and id IN
        <foreach collection="param.ids" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
      </if>
    </where>
    <choose>
      <when test="param.sortColumns != null and param.sortColumns !='' ">
        <![CDATA[ ORDER BY ${param.sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY ID DESC ]]>
      </otherwise>
    </choose>
  </select>

  <select id="selectJobAndWorker" resultType="com.zhixianghui.facade.employee.vo.JobWorkerVo">
    SELECT job.id,job.job_id,job.industry_code
    ,job.industry_name,job.job_name,job.job_tag,job.job_province_name
    ,job.job_city_name,job.pay_type,job.reward_amount,job.subsidy_amount,job.reward_type
    ,job.job_status,worker.id worker_id,worker.auth_status,worker.delivery_status
    ,worker.settle_status,worker.job_status worker_job_status,worker.worker_phone,worker.worker_idcard
    FROM `tbl_job` job,`tbl_job_worker_record` worker
    where job.job_id=worker.job_id
      and worker.job_status = #{jobStatus}
    and worker.worker_idcard = #{workerIdcard}
    <choose>
      <when test="sortColumns != null and sortColumns !='' ">
        <![CDATA[ ORDER BY ${sortColumns} ]]>
      </when>
      <otherwise>
        <![CDATA[ ORDER BY worker.create_time DESC ]]>
      </otherwise>
    </choose>
  </select>

  <select id="selectJobAndWorkerDetail" resultType="com.zhixianghui.facade.employee.vo.JobWorkerDetailVo">
        SELECT job.id,job.job_id,job.industry_code
    ,job.industry_name,job.job_name,job.job_tag,job.job_province_name,job.reward_type
    ,job.job_city_name,job.job_aval_date_type,job.pay_type,job.reward_amount,job.subsidy_amount
    ,job.job_status,job.job_describe,job.delivery_standard,worker.id worker_id
    ,worker.auth_status,worker.delivery_status,worker.settle_status
    ,worker.job_accept_time,worker.job_finish_time,worker.create_time
    ,worker.attachment,worker.delivery_content,worker.job_status worker_job_status,worker.worker_phone,worker.worker_idcard
    ,worker.fail_reason
    FROM `tbl_job` job,`tbl_job_worker_record` worker
    where job.job_id=worker.job_id and job.id=#{id} and worker.worker_idcard=#{workerIdcard}
  </select>


  <select id="selectJobAndWorkerDetailById" resultType="com.zhixianghui.facade.employee.vo.JobWorkerDetailVo">
    SELECT job.id,job.job_id,job.industry_code
    ,job.industry_name,job.job_name,job.job_tag,job.job_province_name,job.reward_type
    ,job.job_city_name,job.job_aval_date_type,job.pay_type,job.reward_amount,job.subsidy_amount
    ,job.job_status,job.job_describe,job.delivery_standard,worker.id worker_id
    ,worker.auth_status,worker.delivery_status,worker.settle_status
    ,worker.job_accept_time,worker.job_finish_time,worker.create_time
    ,worker.attachment,worker.delivery_content,worker.job_status worker_job_status,worker.worker_idcard
    ,worker.fail_reason
    FROM `tbl_job` job,`tbl_job_worker_record` worker
    where job.job_id=worker.job_id and  worker.id=#{id}
  </select>

  <update id="batchUpdateJobWorkerStatus">
    update tbl_job_worker_record t set t.job_status=#{jobWorkerStatus}
    <where>
      <if test="ids != null and ids.size() > 0">
        AND id IN
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
      </if>
      <if test="ids==null">
        AND 1=0
      </if>
    </where>
  </update>

  <select id="selectByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from tbl_job_worker_record
    where id in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
  </select>

  <select id="countWorker" resultType="com.zhixianghui.facade.employee.vo.JobWorkerCountVo">
    select job_id,count(job_id) count from tbl_job_worker_record where job_status>=300
    and job_id in
    <foreach collection="jobIds" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
     group by job_id
  </select>

  <update id="batchUpdateStatus" parameterType="com.zhixianghui.facade.employee.vo.JobWorkerUpdateVo">
    update tbl_job_worker_record t
    <trim prefix="SET" suffixOverrides=",">
        <if test="vo.column != null">
            t.${vo.column} = #{vo.value},
        </if>
        <if test="vo.jobFinishTime != null">
            job_finish_time = #{vo.jobFinishTime},
        </if>
    </trim>
    <where>
    <if test="ids != null and ids.size() > 0">
      AND id IN
      <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">#{item,jdbcType=BIGINT}</foreach>
    </if>
    <if test="ids==null">
      AND 1=0
    </if>
    </where>
  </update>
</mapper>
