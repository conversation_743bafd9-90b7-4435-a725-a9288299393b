spring.application.name=service-employee
spring.cloud.nacos.config.server-addr=@nacosAddr@
spring.cloud.nacos.config.namespace=@nacosNamespace@
spring.cloud.nacos.config.file-extension=properties

spring.cloud.nacos.discovery.namespace=@nacosNamespace@


spring.cloud.nacos.config.shared-dataids=rocketmq.properties,dubbo.properties,redis.properties,db.properties

#spring.datasource.username=root
#spring.datasource.password=hjzx!321
#spring.datasource.url=***************************************************************************************************************************************************

logging.level.com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder=info
logging.level.com.zhixianghui.service.employee.mapper=debug