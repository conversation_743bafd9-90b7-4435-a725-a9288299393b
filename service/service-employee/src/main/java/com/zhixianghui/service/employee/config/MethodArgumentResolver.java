package com.zhixianghui.service.employee.config;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.employee.annotation.CurrentLoginVo;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.employee.biz.EmployeeBiz;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 *
 *
 * <AUTHOR> <PERSON>
 */
@Component
public class MethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Autowired
    private RedisClient redisClient;

    @Autowired
    private EmployeeBiz employeeBiz;

    public static final String WECHAT_LOGIN_KEY_PREFIX = "WX:login:key:";

    public static final String REQUEST_TOKEN_HEADER = "authorization";

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return (parameter.getParameterType().isAssignableFrom(WeChatLoginVo.class) && parameter.hasParameterAnnotation(CurrentLoginVo.class));
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        HttpServletRequest request = (HttpServletRequest) (webRequest.getNativeRequest());
        String token = Optional.ofNullable(request.getHeader(REQUEST_TOKEN_HEADER)).orElse("-1");
        String json = redisClient.get(WECHAT_LOGIN_KEY_PREFIX + MD5Util.getMD5Hex(token));
        if(StringUtils.isBlank(json)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("token失效");
        }
        final WeChatLoginVo weChatLoginVo = JSONObject.parseObject(json, WeChatLoginVo.class);
        final String idcardNo = employeeBiz.getIdCardNoByPhone(weChatLoginVo.getMobile());
        weChatLoginVo.setIdCard(idcardNo);
        return weChatLoginVo;
    }

}
