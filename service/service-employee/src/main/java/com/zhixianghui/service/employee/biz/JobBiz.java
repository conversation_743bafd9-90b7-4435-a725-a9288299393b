package com.zhixianghui.service.employee.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.employee.dto.JobDto;
import com.zhixianghui.facade.employee.dto.JobQueryDto;
import com.zhixianghui.facade.employee.dto.JobWebQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.*;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.service.WeChatUserFacade;
import com.zhixianghui.facade.trade.vo.UserAuthVo;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.employee.service.JobService;
import com.zhixianghui.service.employee.service.JobWorkerRecordService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class JobBiz {

    @Autowired
    private JobService jobService;
    @Autowired
    private JobWorkerRecordService jobWorkerRecordService;
    @Reference
    private SequenceFacade sequenceFacade;
    @Autowired
    private JobWorkerRecordBiz workerRecordBiz;
    @Reference
    private UserInfoFacade userInfoFacade;
    @Reference
    private WeChatUserFacade weChatUserFacade;
    @Reference
    private NotifyFacade notifyFacade;

    public Job addJobApi(JobDto jobDto) {
        String jobId = "JB" + LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.CKH_JOB_ID_SEQ.getPrefix(), SequenceBizKeyEnum.CKH_JOB_ID_SEQ.getKey(), SequenceBizKeyEnum.CKH_JOB_ID_SEQ.getWidth());
        Job job = jobDto.toJobEntity();
        job.setJobTag(jobDto.getJobTag());
        job.setJobId(jobId);
        job.setVersion(0);
        jobService.save(job);
        return job;
    }

    @Transactional(rollbackFor = Exception.class)
    public Job addJob(JobDto jobDto) {
        String regex="市辖区|县|省直辖行政单位";
        final Job job = jobDto.toJobEntity();
        String jobId = "JB" + LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.CKH_JOB_ID_SEQ.getPrefix(), SequenceBizKeyEnum.CKH_JOB_ID_SEQ.getKey(), SequenceBizKeyEnum.CKH_JOB_ID_SEQ.getWidth());
        String jobTag =formatTag(jobDto.getJobTag());
        job.setJobTag(jobTag);
        job.setJobId(jobId);
        job.setVersion(0);
        job.setJobCityName(job.getJobCityName().replaceAll(regex,job.getJobProvinceName()));
        job.setJobStatus(JobStatusEnum.NEW.getCode());

        jobService.save(job);
        if (jobDto.isAuto()){
            Long[] ids = new Long[1];
            ids[0] = job.getId();
            approved(ids,ApproveEvent.ADOPT,"平台方");
        }
        return job;
    }

    private String formatTag(String jobTag){
        if(StringUtil.isNotEmpty(jobTag)){
            String[] arr = jobTag.replaceAll("\\s+", " ").split(" ");
            return JSONArray.toJSONString(arr);
        }else {
            return JSONArray.toJSONString(new ArrayList<String>());
        }
    }

    public Page<Job> pageJob(Page page, Map<String, Object> param) {
        final Page<Job> pageData = jobService.pageJobs(page, param);
        // 查询出已录用人数
        if (pageData.getRecords() != null && !pageData.getRecords().isEmpty()) {
            final List<Job> records = pageData.getRecords();
            for (Job record : records) {
                final String jobId = record.getJobId();
                final Integer workerCount = workerRecordBiz.countEmployedWorker(jobId);
                record.setEmployedNum(workerCount);
            }
        }
        return pageData;
    }

    public Job getJobById(Long id) {
        return jobService.getById(id);
    }

    public Job getJobByJobId(String jobId) {
        return jobService.getJobByJobId(jobId);
    }

    public Page<Job> pageJob(Page page, JobQueryDto jobQueryDto) {
        return jobService.page(page, new QueryWrapper<Job>().setEntity(jobQueryDto.toJob()));
    }

    public List<Job> list(JobWebQueryDto jobWebQueryDto) {
        return jobService.list(jobWebQueryDto);
    }

    public List<Job> listJobListOnGrant(Map<String, Object> param) {
        return jobService.listJobListOnGrant(param);
    }

    public void acceptJob(Long id, WeChatLoginVo weChatLoginVo) {
        UserAuthVo authInfo = userInfoFacade.getAuthInfo(weChatLoginVo.getMobile());
        if (ObjectUtils.isEmpty(authInfo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户未认证");
        }
        if (authInfo.getAuthStatus().intValue() != AuthStatusEnum.SUCCESS.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户未认证");
        }
        Job job = this.getJobById(id);
        JobWorkerRecord record = jobWorkerRecordService.getOne(new QueryWrapper<JobWorkerRecord>()
                .eq(JobWorkerRecord.COL_JOB_ID, job.getJobId())
                .eq(JobWorkerRecord.COL_WORKER_IDCARD, weChatLoginVo.getIdCard()));

        if (ObjectUtils.isNotEmpty(record)&&record.getJobStatus().equals(JobWorkerStatusEnum.WAIT_RECEIVED.getCode())) {
            record.setJobStatus(JobWorkerStatusEnum.WAIT_EMPLOY.getCode());
            if (StringUtils.isBlank(record.getWorkerName())){
                record.setWorkerName(authInfo.getName());
            }
            if (StringUtils.isBlank(record.getWorkerIdcard())){
                record.setWorkerIdcard(authInfo.getIdCardNo());
            }
            record.setAuthStatus(authInfo.getAuthStatus());
            record.setMiniOpenId(weChatLoginVo.getMiniOpenId());
            record.setMiniAppId(weChatLoginVo.getMiniAppId());
            jobWorkerRecordService.updateById(record);
        } else {
            if (ObjectUtils.isNotEmpty(record)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("您已经申请过此任务，请勿重复申请");
            }
            JobWorkerRecord jobWorkerRecord = new JobWorkerRecord();
            jobWorkerRecord.setJobId(job.getJobId());
            jobWorkerRecord.setJobName(job.getJobName());
            jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.WAIT_EMPLOY.getCode());
            jobWorkerRecord.setEmployerName(job.getEmployerName());
            jobWorkerRecord.setEmployerNo(job.getEmployerNo());
            jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.NOT_DELIVERED.getCode());
            jobWorkerRecord.setJobAcceptTime(DateUtil.now());
            jobWorkerRecord.setCreateTime(DateUtil.now());
            jobWorkerRecord.setWorkerName(authInfo.getName());
            jobWorkerRecord.setWorkerIdcard(authInfo.getIdCardNo());
            jobWorkerRecord.setWorkerPhone(weChatLoginVo.getMobile());
            jobWorkerRecord.setGender(weChatLoginVo.getGender());
            jobWorkerRecord.setIsAssign(IsAssignEnum.NO.getCode());
            jobWorkerRecord.setAuthStatus(weChatLoginVo.getAuthStatus());
            jobWorkerRecord.setMiniOpenId(weChatLoginVo.getMiniOpenId());
            jobWorkerRecord.setMiniAppId(weChatLoginVo.getMiniAppId());
            jobWorkerRecordService.save(jobWorkerRecord);
        }



    }

    public void deleteById(Long id) {
        jobService.deleteById(id);
    }

    public Job updateById(JobDto jobDto) {
        final Job job = jobDto.toJobEntity();
        String jobTag =formatTag(jobDto.getJobTag());
        job.setJobTag(jobTag);
        job.setUpdateTime(DateUtil.now());

        jobService.update(job);
        return job;
//        JobWorkerStatusEnum jobWorkerStatus = job.getAcceptMode().intValue() == AcceptModeEnum.MANUAL.getCode().intValue() ? JobWorkerStatusEnum.WAIT_EMPLOY : JobWorkerStatusEnum.PROCESSING;
//        jobWorkerRecordService.remove(new QueryWrapper<JobWorkerRecord>().eq("job_id", job.getJobId()));

    }

    public void update(Job job){
        jobService.update(job);
    }

    public void approved(Long[] ids, ApproveEvent event, String updator) {
        for (int i = 0; i < ids.length; i++) {
            Job job = jobService.getById(ids[i]);
            if (ObjectUtils.isNotEmpty(job) && job.getJobStatus().equals(JobStatusEnum.NEW.getCode())) {
                switch (event) {
                    case ADOPT:
                        job.setJobStatus(JobStatusEnum.PROCESSING.getCode());
                        break;
                    case FAIL:
                        job.setJobStatus(JobStatusEnum.REJECTED.getCode());
                        break;
                    default:
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("操作异常");
                }
                job.setUpdateTime(DateUtil.now());
                job.setUpdateBy(updator);
                jobService.updateById(job);
                //通知商户
                if (StringUtils.isNotBlank(job.getCallbackUrl())){
                    notifyFacade.sendOne(MessageMsgDest.TOPIC_JOB_CALLBACK, job.getEmployerNo(),job.getJobId(),
                            NotifyTypeEnum.MERCHANT_NOTIFY.getValue(),MessageMsgDest.TAG_JOB_CALLBACK, JSON.toJSONString(job));
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void completeJob(Long id) {
        Job job = this.getJobById(id);
        job.setJobStatus(JobStatusEnum.COMPLETED.getCode());
        jobService.update(job);
        List<JobWorkerRecord> jobWorkerRecords = workerRecordBiz.listByJobId(job.getJobId());
        if (jobWorkerRecords.size() > 0){
            List<Long> workerIds = jobWorkerRecords.stream().map(JobWorkerRecord::getId).collect(Collectors.toList());
            workerRecordBiz.completeJobWorker(workerIds);
        }
    }
}
