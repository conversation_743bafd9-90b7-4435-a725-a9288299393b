package com.zhixianghui.service.employee;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 交易中心服务
 */
@EnableCaching
@EnableDiscoveryClient
@SpringBootApplication
public class ServiceEmployeeApp {
    public static void main(String[] args) {
        System.setProperty("druid.mysql.usePingMethod","false");
        new SpringApplicationBuilder(ServiceEmployeeApp.class).run(args);
    }
}
