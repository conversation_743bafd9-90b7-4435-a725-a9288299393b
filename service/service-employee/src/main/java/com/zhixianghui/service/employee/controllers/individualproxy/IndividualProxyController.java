package com.zhixianghui.service.employee.controllers.individualproxy;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.individualproxy.MainstayIndividualProxy;
import com.zhixianghui.facade.common.service.MainstayIndividualProxyFacade;
import com.zhixianghui.facade.employee.annotation.CurrentLoginVo;
import com.zhixianghui.facade.employee.dto.InvoicePostAddressDto;
import com.zhixianghui.facade.employee.dto.InvoiceTitleInfoDto;
import com.zhixianghui.facade.employee.entity.InvoicePostAddress;
import com.zhixianghui.facade.employee.entity.InvoiceTitleInfo;
import com.zhixianghui.facade.trade.dto.IndividualProxyOrderDto;
import com.zhixianghui.facade.trade.dto.ProxyOrderQueryDto;
import com.zhixianghui.facade.trade.entity.IndividualProxyOrder;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.employee.biz.IndividualProxyBiz;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("individualProxy")
public class IndividualProxyController {

    @Reference
    private MainstayIndividualProxyFacade mainstayIndividualProxyFacade;

    @Autowired
    private IndividualProxyBiz biz;

    /**
     * 供应商列表
     *
     * @return
     */
    @PostMapping("listMainstay")
    public RestResult<List<MainstayIndividualProxy>> listMainstay() {
        final List<MainstayIndividualProxy> mainstays = mainstayIndividualProxyFacade.listAll(Maps.newHashMap());
        return RestResult.success(mainstays);
    }


    @PostMapping("addInvoiceTitle")
    public RestResult<InvoiceTitleInfo> addInvoiceTitle(@Validated @RequestBody InvoiceTitleInfoDto dto, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        return RestResult.success(biz.addInvoiceTitleInfo(dto, weChatLoginVo));
    }

    @PostMapping("listInvoiceTitle")
    public RestResult<List<InvoiceTitleInfo>> listInvoiceTitle(@CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        return RestResult.success(biz.listInvoiceTitleInfo(weChatLoginVo));
    }

    @PostMapping("editInvoiceTitle")
    public RestResult<InvoiceTitleInfo> editInvoiceTitle(@RequestBody InvoiceTitleInfo invoiceTitleInfo, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        if (StringUtils.isBlank(invoiceTitleInfo.getTaxNo())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("税号不能为空");
        }
        if (StringUtils.isBlank(invoiceTitleInfo.getCompanyName())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("公司名称不能为空");
        }

        return RestResult.success(biz.updateInvoiceTitleInfo(invoiceTitleInfo));
    }

    @PostMapping("deleteInvoiceTitle")
    public RestResult<String> deleteInvoiceTitle(Long id, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        biz.deleteInvoiceTitleInfo(id, weChatLoginVo);
        return RestResult.success("删除成功");
    }

    /**
     * ------------------------------------------------------------------------------
     * 收票地址
     * ------------------------------------------------------------------------------
     */
    @PostMapping("addInvoicePostAddress")
    public RestResult<InvoicePostAddress> addInvoicePostAddress(@Validated @RequestBody InvoicePostAddressDto dto, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        return RestResult.success(biz.addInvoicePostAddress(dto, weChatLoginVo));
    }

    @PostMapping("editInvoicePostAddress")
    public RestResult<InvoicePostAddress> editInvoicePostAddress(@Validated @RequestBody InvoicePostAddress invoicePostAddress) {
        return RestResult.success(biz.editInvoicePostAddress(invoicePostAddress));
    }

    @PostMapping("listInvoicePostAddress")
    public RestResult<List<InvoicePostAddress>> listInvoicePostAddress(@CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        return RestResult.success(biz.listInvoicePostAddress(weChatLoginVo));
    }

    @PostMapping("deleteInvoicePostAddress")
    public RestResult<String> InvoicePostAddress(Long id, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        biz.deleteInvoicePostAddress(id, weChatLoginVo);
        return RestResult.success("删除成功");
    }

    @PostMapping("applyInvoice")
    public RestResult<IndividualProxyOrder> applyInvoice(@Validated @RequestBody IndividualProxyOrderDto dto, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        return RestResult.success(biz.applyInvoice(dto, weChatLoginVo));
    }

    @PostMapping("listProxyOrder")
    public RestResult<Page<IndividualProxyOrder>> listProxyOrder(@RequestBody ProxyOrderQueryDto proxyOrderQueryDto, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        proxyOrderQueryDto.setIdCardNo(weChatLoginVo.getIdCard());
        return RestResult.success(biz.listProxyOrder(proxyOrderQueryDto, new Page<>(proxyOrderQueryDto.getCurrent(), proxyOrderQueryDto.getSize())));
    }

    @GetMapping("getProxyOrderById")
    public RestResult<IndividualProxyOrder> getProxyOrderById(@RequestParam("id") Long id,@CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        return RestResult.success(biz.getProxyOrderById(id, weChatLoginVo.getIdCard()));
    }

    @PostMapping("cancelProxyOrderById")
    public RestResult<IndividualProxyOrder> cancelProxyOrderById(Long id, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        return RestResult.success(biz.cancelProxyOrderById(id, weChatLoginVo.getIdCard()));
    }

    @PostMapping("pay")
    public RestResult pay(@RequestBody ProxyOrderQueryDto proxyOrderQueryDto,@CurrentLoginVo WeChatLoginVo weChatLoginVo){
        if (StringUtils.isNotBlank(proxyOrderQueryDto.getOrderNo())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单号不能为空");
        }
        return RestResult.success(biz.pay(proxyOrderQueryDto.getOrderNo(),weChatLoginVo));
    }

}
