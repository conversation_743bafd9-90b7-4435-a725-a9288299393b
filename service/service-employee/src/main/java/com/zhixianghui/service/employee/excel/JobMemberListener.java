package com.zhixianghui.service.employee.excel;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.zhixianghui.common.statics.enums.auth.AuthChannelEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.employee.dto.JobMemberExcelDto;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.AcceptModeEnum;
import com.zhixianghui.facade.employee.enums.DeliveryStatusEnum;
import com.zhixianghui.facade.employee.enums.IsAssignEnum;
import com.zhixianghui.facade.employee.enums.JobWorkerStatusEnum;
import com.zhixianghui.facade.employee.vo.JobVo;
import com.zhixianghui.facade.trade.dto.AuthReqDto;
import com.zhixianghui.facade.trade.vo.auth.AuthResponseVo;
import com.zhixianghui.service.employee.service.JobWorkerRecordService;
import com.zhixianghui.service.employee.vo.JobReadVo;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description GrantDataListener 发放文件解析类
 * @date 2020-11-04 10:55
 **/
@Slf4j
public class JobMemberListener extends AnalysisEventListener<JobMemberExcelDto> {

    private JobReadVo jobReadVo;

    public JobMemberListener(JobReadVo jobReadVo) {
        this.jobReadVo = jobReadVo;
    }

    /**
     * 这个每一条数据解析都会来调用
     */
    @Override
    public void invoke(@Valid JobMemberExcelDto data, AnalysisContext context) {
        final JobWorkerRecordService jobWorkerRecordService =jobReadVo.getJobWorkerRecordService();
        final JobVo jobVo=jobReadVo.getJobVo();


        Integer rowNum = context.readRowHolder().getRowIndex();
        checkRow(data, rowNum + 1);
        List<String> idCards = jobReadVo.getIdCards();
        if (idCards.contains(data.getWorkerIdcard())) {
            final JobWorkerRecord jobWorkerRecord = jobWorkerRecordService.getWorkerRecordByJobIdAndIdCardNo(jobVo.getJobId(), data.getWorkerIdcard());
            if (jobWorkerRecord != null) {
                JobWorkerStatusEnum jobWorkerStatus = jobVo.getAcceptMode().intValue() == AcceptModeEnum.MANUAL.getCode().intValue() ? JobWorkerStatusEnum.WAIT_RECEIVED : JobWorkerStatusEnum.PROCESSING;

                jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.NOT_DELIVERED.getCode());
                jobWorkerRecord.setJobAcceptTime(DateUtil.now());
                jobWorkerRecord.setWorkerName(data.getWorkerName());
                jobWorkerRecord.setWorkerIdcard(data.getWorkerIdcard());
                jobWorkerRecord.setWorkerPhone(data.getWorkerPhone());
                jobWorkerRecord.setGender(IdcardUtil.getGenderByIdCard(data.getWorkerIdcard()));
                jobWorkerRecord.setJobStatus(jobWorkerStatus.getCode());
                jobWorkerRecord.setIsAssign(IsAssignEnum.YES.getCode());

                /**
                 * 鉴权
                 */
                AuthReqDto authReqDto = new AuthReqDto();
                authReqDto.setName(data.getWorkerName());
                authReqDto.setIdCardNo(data.getWorkerIdcard());
                authReqDto.setAuthType(AuthTypeEnum.IDCARD_NAME.getValue());
//                authReqDto.setAuthChannel(AuthChannelEnum.ADM.name());

                final AuthResponseVo responseVo = jobReadVo.getAuthRecordFacade().auth(authReqDto);
                if (responseVo.getAuthStatus().intValue() != BankAuthStatusEnum.SUCCESS.getValue()) {
                    jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.FAIL_AUTH_FAIL.getCode());
                }

                jobWorkerRecordService.updateById(jobWorkerRecord);

                idCards.add(data.getWorkerIdcard());

                return;
            }

        }

        JobWorkerStatusEnum jobWorkerStatus = jobVo.getAcceptMode().intValue() == AcceptModeEnum.MANUAL.getCode().intValue() ? JobWorkerStatusEnum.WAIT_RECEIVED : JobWorkerStatusEnum.PROCESSING;
        JobWorkerRecord jobWorkerRecord = new JobWorkerRecord();
        jobWorkerRecord.setJobId(jobVo.getJobId());
        jobWorkerRecord.setJobName(jobVo.getJobName());
        jobWorkerRecord.setEmployerName(jobVo.getEmployerName());
        jobWorkerRecord.setEmployerNo(jobVo.getEmployerNo());
        jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.NOT_DELIVERED.getCode());
        jobWorkerRecord.setJobAcceptTime(DateUtil.now());
        jobWorkerRecord.setWorkerName(data.getWorkerName());
        jobWorkerRecord.setWorkerIdcard(data.getWorkerIdcard());
        jobWorkerRecord.setWorkerPhone(data.getWorkerPhone());
        jobWorkerRecord.setGender(IdcardUtil.getGenderByIdCard(data.getWorkerIdcard()));
        jobWorkerRecord.setJobStatus(jobWorkerStatus.getCode());
        jobWorkerRecord.setIsAssign(IsAssignEnum.YES.getCode());
        jobWorkerRecord.setMainstayNo(jobVo.getMainstayNo());
        jobWorkerRecord.setMainstayName(jobVo.getMainstayName());

        /**
         * 鉴权
         */
        AuthReqDto authReqDto = new AuthReqDto();
        authReqDto.setName(data.getWorkerName());
        authReqDto.setIdCardNo(data.getWorkerIdcard());
        authReqDto.setAuthType(AuthTypeEnum.IDCARD_NAME.getValue());
//        authReqDto.setAuthChannel(AuthChannelEnum.ADM.name());

        final AuthResponseVo responseVo = jobReadVo.getAuthRecordFacade().auth(authReqDto);
        if (responseVo.getAuthStatus().intValue() != BankAuthStatusEnum.SUCCESS.getValue()) {
            jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.FAIL_AUTH_FAIL.getCode());
        }

        jobWorkerRecordService.save(jobWorkerRecord);

        idCards.add(data.getWorkerIdcard());


    }

    private void checkRow(JobMemberExcelDto data, Integer rowNum) {
//        if (!IdcardUtil.isValidCard(data.getWorkerIdcard())) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【第" + rowNum + "】行：身份证格式错误");
//        }
//        if (!ValidateUtil.isChineseName(data.getWorkerName())) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【第" + rowNum + "】行：姓名格式错误");
//        }
        if (!ValidateUtil.isMobile(data.getWorkerPhone())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【第" + rowNum + "】手机号码格式错误");
        }

    }

    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {

    }

    /**
     * 在转换异常 获取其他异常下会调用本接口。抛出异常则停止读取。如果这里不抛出异常则 继续读取下一行。
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        // 如果是某一个单元格的转换异常 能获取到具体行号
        // 如果要获取头的信息 配合invokeHeadMap使用
        if (exception instanceof ExcelDataConvertException) {
            log.error("遇到解析失败，但是继续解析下一行:{}", exception.getMessage());
            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            log.error("第{}行，第{}列解析异常", excelDataConvertException.getRowIndex(),
                    excelDataConvertException.getColumnIndex());
        } else if (exception instanceof BizException) {
            log.error("解析遇到业务异常:{}", exception.getMessage());
            throw (BizException) exception;
        } else {
            throw exception;
        }
    }

    private static String stringValueOf(Object obj) {
        return (obj == null) ? " " : obj.toString();
    }

}
