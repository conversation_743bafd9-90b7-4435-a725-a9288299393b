package com.zhixianghui.service.employee.biz;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.employee.dto.InvoicePostAddressDto;
import com.zhixianghui.facade.employee.dto.InvoiceTitleInfoDto;
import com.zhixianghui.facade.employee.entity.InvoicePostAddress;
import com.zhixianghui.facade.employee.entity.InvoiceTitleInfo;
import com.zhixianghui.facade.trade.dto.IndividualProxyOrderDto;
import com.zhixianghui.facade.trade.dto.ProxyOrderQueryDto;
import com.zhixianghui.facade.trade.entity.IndividualProxyOrder;
import com.zhixianghui.facade.trade.service.IndividualProxyOrderFacade;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.employee.service.InvoicePostAddressService;
import com.zhixianghui.service.employee.service.InvoiceTitleInfoService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class IndividualProxyBiz {

    @Autowired
    private InvoicePostAddressService invoicePostAddressService;

    @Autowired
    private InvoiceTitleInfoService invoiceTitleInfoService;

    @Reference
    private IndividualProxyOrderFacade individualProxyOrderFacade;

    @Transactional(rollbackFor = Exception.class)
    public InvoiceTitleInfo addInvoiceTitleInfo(InvoiceTitleInfoDto dto, WeChatLoginVo weChatLoginVo) {

        if (dto.getIsDefault()) {
            QueryWrapper<InvoiceTitleInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(InvoiceTitleInfo.COL_ID_CARD_NO, weChatLoginVo.getIdCard());
            queryWrapper.eq(InvoiceTitleInfo.COL_IS_DEFAULT, Boolean.TRUE);
            final InvoiceTitleInfo infoServiceOne = invoiceTitleInfoService.getOne(queryWrapper);
            if (infoServiceOne != null) {
                infoServiceOne.setIsDefault(Boolean.TRUE);
                invoiceTitleInfoService.updateById(infoServiceOne);
            }
        }


        final InvoiceTitleInfo titleInfo = BeanUtil.toBean(dto, InvoiceTitleInfo.class);
        titleInfo.setIdCardNo(weChatLoginVo.getIdCard());
        invoiceTitleInfoService.save(titleInfo);

        return titleInfo;
    }

    public List<InvoiceTitleInfo> listInvoiceTitleInfo(WeChatLoginVo weChatLoginVo) {
        final List<InvoiceTitleInfo> list = invoiceTitleInfoService.list(new QueryWrapper<InvoiceTitleInfo>().eq(InvoiceTitleInfo.COL_ID_CARD_NO, weChatLoginVo.getIdCard()));
        return list;
    }

    public InvoiceTitleInfo updateInvoiceTitleInfo(InvoiceTitleInfo invoiceTitleInfo) {
        if (invoiceTitleInfo.getIsDefault()) {
            QueryWrapper<InvoiceTitleInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(InvoiceTitleInfo.COL_ID_CARD_NO, invoiceTitleInfo.getIdCardNo());
            queryWrapper.eq(InvoiceTitleInfo.COL_IS_DEFAULT, Boolean.TRUE);
            final InvoiceTitleInfo infoServiceOne = invoiceTitleInfoService.getOne(queryWrapper);
            if (infoServiceOne != null) {
                infoServiceOne.setIsDefault(Boolean.TRUE);
                invoiceTitleInfoService.updateById(infoServiceOne);
            }
        }
        invoiceTitleInfoService.updateById(invoiceTitleInfo);
        return invoiceTitleInfo;
    }

    public void deleteInvoiceTitleInfo(Long id, WeChatLoginVo weChatLoginVo) {
        final InvoiceTitleInfo byId = invoiceTitleInfoService.getById(id);
        if (byId == null || !StringUtils.equals(byId.getIdCardNo(), weChatLoginVo.getIdCard())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("抬头数据不存在");
        }
        invoiceTitleInfoService.removeById(id);
    }

    public InvoicePostAddress addInvoicePostAddress(InvoicePostAddressDto dto, WeChatLoginVo weChatLoginVo) {
        if (dto.getIsDefault()) {
            QueryWrapper<InvoicePostAddress> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(InvoicePostAddress.COL_ID_CARD_NO, weChatLoginVo.getIdCard());
            queryWrapper.eq(InvoicePostAddress.COL_IS_DEFAULT, Boolean.TRUE);
            final InvoicePostAddress postAddressServiceOne = invoicePostAddressService.getOne(queryWrapper);
            if (postAddressServiceOne != null) {
                postAddressServiceOne.setIsDefault(Boolean.TRUE);
                invoicePostAddressService.updateById(postAddressServiceOne);
            }
        }

        final InvoicePostAddress postAddress = BeanUtil.toBean(dto, InvoicePostAddress.class);
        postAddress.setIdCardNo(weChatLoginVo.getIdCard());
        invoicePostAddressService.save(postAddress);
        return postAddress;
    }

    public InvoicePostAddress editInvoicePostAddress(InvoicePostAddress invoicePostAddress) {
        if (invoicePostAddress.getIsDefault()) {
            QueryWrapper<InvoicePostAddress> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(InvoicePostAddress.COL_ID_CARD_NO, invoicePostAddress.getIdCardNo());
            queryWrapper.eq(InvoicePostAddress.COL_IS_DEFAULT, Boolean.TRUE);
            final InvoicePostAddress postAddressServiceOne = invoicePostAddressService.getOne(queryWrapper);
            if (postAddressServiceOne != null) {
                postAddressServiceOne.setIsDefault(Boolean.TRUE);
                invoicePostAddressService.updateById(postAddressServiceOne);
            }
        }
        invoicePostAddressService.updateById(invoicePostAddress);
        return invoicePostAddress;
    }

    public List<InvoicePostAddress> listInvoicePostAddress(WeChatLoginVo weChatLoginVo) {
        return invoicePostAddressService.list(new QueryWrapper<InvoicePostAddress>().eq(InvoicePostAddress.COL_ID_CARD_NO, weChatLoginVo.getIdCard()));
    }

    public void deleteInvoicePostAddress(Long id, WeChatLoginVo weChatLoginVo) {
        final InvoicePostAddress byId = invoicePostAddressService.getById(id);
        if (byId == null || !StringUtils.equals(byId.getIdCardNo(), weChatLoginVo.getIdCard())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("收票地址数据不存在");
        }
        invoicePostAddressService.removeById(id);
    }

    public IndividualProxyOrder applyInvoice(IndividualProxyOrderDto dto, WeChatLoginVo weChatLoginVo) {
        final InvoicePostAddress postAddress = this.invoicePostAddressService.getById(dto.getAddressId());
        if (postAddress == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("收票地址信息不存在");
        }

        final InvoiceTitleInfo titleInfo = this.invoiceTitleInfoService.getById(dto.getInvoiceTitleId());
        if (titleInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("开票抬头信息不存在");
        }

        return individualProxyOrderFacade.applyInvoice(dto, weChatLoginVo);
    }

    public IndividualProxyOrder getProxyOrderById(Long id,String idcardNo) {
        return individualProxyOrderFacade.getProxyOrderById(id, idcardNo);
    }

    public Page<IndividualProxyOrder> listProxyOrder(ProxyOrderQueryDto proxyOrderQueryDto, Page page) {
        return individualProxyOrderFacade.listPage(proxyOrderQueryDto, page);
    }

    public IndividualProxyOrder cancelProxyOrderById(Long id,String idcardNo) {
        return individualProxyOrderFacade.cancelProxyOrderById(id, idcardNo);
    }

    public String pay(String orderNo, WeChatLoginVo weChatLoginVo) {
        return individualProxyOrderFacade.prePay(orderNo,weChatLoginVo);
    }
}

