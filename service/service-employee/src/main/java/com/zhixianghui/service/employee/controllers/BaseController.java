package com.zhixianghui.service.employee.controllers;

import cn.hutool.db.sql.SqlUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.employee.vo.TableDataInfo;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月15日 15:47:00
 */
public class BaseController {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    protected void startPage(PageParam pageParam) {
        int pageNum = pageParam.getPageCurrent();
        int pageSize = pageParam.getPageSize();
        PageHelper.startPage(pageNum, pageSize);
    }

    protected RestResult toTable(List<?> list) {
        PageInfo pageInfo = new PageInfo(list);
        TableDataInfo rspData = new TableDataInfo();
        rspData.setRows(list);
        rspData.setTotalRecord(pageInfo.getTotal());
        return RestResult.success(rspData);
    }

    protected RestResult success(Object data){
        return RestResult.success(data);
    }

}