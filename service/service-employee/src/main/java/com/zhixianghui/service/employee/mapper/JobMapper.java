package com.zhixianghui.service.employee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.employee.dto.JobWebQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface JobMapper extends BaseMapper<Job> {

    Page<Job> listBy(IPage<Job> page,@Param("param") Map<String, Object> param);

    /**
     * 查询任务
     *
     * @param id 任务主键
     * @return 任务
     */
    public Job selectJobById(Long id);

    /**
     * 查询任务列表
     *
     * @param job 任务
     * @return 任务集合
     */
    public List<Job> selectJobList(JobWebQueryDto jobWebQueryDto);

    /**
     * 新增任务
     *
     * @param job 任务
     * @return 结果
     */
    public int insertJob(Job job);

    /**
     * 修改任务
     *
     * @param job 任务
     * @return 结果
     */
    public int updateJob(Job job);


    /**
     * 批量删除任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJobByIds(Long[] ids);

    public List<Job> listJobListOnGrant(Map<String, Object> param);
}
