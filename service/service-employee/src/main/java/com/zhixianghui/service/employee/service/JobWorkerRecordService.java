package com.zhixianghui.service.employee.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.statics.annotations.NeedSetValue;
import com.zhixianghui.facade.employee.dto.JobWorkerWebQueryDto;
import com.zhixianghui.facade.employee.dto.WorkerRecordQueryDto;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.vo.JobWorkerCountVo;
import com.zhixianghui.facade.employee.vo.JobWorkerDetailVo;
import com.zhixianghui.facade.employee.vo.JobWorkerUpdateVo;
import com.zhixianghui.facade.employee.vo.JobWorkerVo;
import com.zhixianghui.service.employee.mapper.JobWorkerRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class JobWorkerRecordService extends ServiceImpl<JobWorkerRecordMapper, JobWorkerRecord> {

    @Autowired
    private JobWorkerRecordMapper jobWorkerRecordMapper;

    @NeedSetValue
    public Page<JobWorkerRecord> listPage(Page<JobWorkerRecord> page,WorkerRecordQueryDto queryDto) {
        return this.baseMapper.listBy(page, queryDto.toMap());
    }

    @NeedSetValue
    public List<JobWorkerVo> selectJobAndWorker(JobWorkerWebQueryDto workerWebQueryDto){
        return jobWorkerRecordMapper.selectJobAndWorker(workerWebQueryDto);
    }

    @NeedSetValue
    public JobWorkerDetailVo selectJobAndWorkerDetail(Long id,String workerIdcard){
        return jobWorkerRecordMapper.selectJobAndWorkerDetail(id,workerIdcard);
    }
    public int batchUpdateJobWorkerStatus(List<Long> ids,Integer jobWorkerStatus) {
       return this.baseMapper.batchUpdateJobWorkerStatus(ids, jobWorkerStatus);
    }

    @NeedSetValue
    public JobWorkerDetailVo selectJobAndWorkerDetailById(Long id) {
        return jobWorkerRecordMapper.selectJobAndWorkerDetailById(id);
    }

    @NeedSetValue
    public List<JobWorkerRecord> selectByIds(List<Long> ids){
        return jobWorkerRecordMapper.selectByIds(ids);
    }

    public List<JobWorkerCountVo> countWorker(List<String> jobIds){
        return jobWorkerRecordMapper.countWorker(jobIds);
    }

    public int batchUpdateStatus(List<Long> ids, JobWorkerUpdateVo jobWorkerUpdateVo) {
        return jobWorkerRecordMapper.batchUpdateStatus(ids,jobWorkerUpdateVo);
    }

    public List<JobWorkerRecord> listBy(Map<String, Object> map) {
        return jobWorkerRecordMapper.listBy(map);
    }

    public JobWorkerRecord getWorkerRecordByJobIdAndIdCardNo(String jobId, String idcardNo) {
        return this.getOne(new QueryWrapper<JobWorkerRecord>()
                .eq(JobWorkerRecord.COL_JOB_ID, jobId)
                .eq(JobWorkerRecord.COL_WORKER_IDCARD, idcardNo));
    }
}
