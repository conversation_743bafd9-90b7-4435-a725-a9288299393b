package com.zhixianghui.service.employee.config;

import com.zhixianghui.service.employee.interceptor.ValidateInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * 拦截器 过滤器 配置
 *
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    @Autowired
    ValidateInterceptor validateInterceptor;
    @Autowired
    private MethodArgumentResolver methodArgumentResolver;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //指定拦截器，指定拦截路径
        registry.addInterceptor(validateInterceptor).
                addPathPatterns("/**").
                excludePathPatterns("/weChat/**");
    }


    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(methodArgumentResolver);
    }
}
