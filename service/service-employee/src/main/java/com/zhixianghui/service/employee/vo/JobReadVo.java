package com.zhixianghui.service.employee.vo;

import com.zhixianghui.facade.employee.vo.JobVo;
import com.zhixianghui.facade.trade.service.AuthRecordFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.service.employee.service.JobWorkerRecordService;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月01日 16:25:00
 */
@Data
@Accessors(chain = true)
public class JobReadVo implements Serializable {

    private static final long serialVersionUID = -7918501142072662652L;
    private JobVo jobVo;

    private List<String> idCards;

    private int headRowNum;

    private UserInfoFacade userInfoFacade;

    private AuthRecordFacade authRecordFacade;

    private JobWorkerRecordService jobWorkerRecordService;
}
