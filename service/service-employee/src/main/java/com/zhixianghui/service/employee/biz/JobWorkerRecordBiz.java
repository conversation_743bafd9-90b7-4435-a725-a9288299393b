package com.zhixianghui.service.employee.biz;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.annotations.NeedSetValue;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.employee.dto.JobWorkerApprovedDto;
import com.zhixianghui.facade.employee.dto.JobWorkerWebQueryDto;
import com.zhixianghui.facade.employee.dto.WorkerRecordQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.*;
import com.zhixianghui.facade.employee.vo.JobWorkerCountVo;
import com.zhixianghui.facade.employee.vo.JobWorkerDetailVo;
import com.zhixianghui.facade.employee.vo.JobWorkerUpdateVo;
import com.zhixianghui.facade.employee.vo.JobWorkerVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.service.employee.service.JobService;
import com.zhixianghui.service.employee.service.JobWorkerRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class JobWorkerRecordBiz {
    @Autowired
    private JobService jobService;
    @Autowired
    private JobWorkerRecordService workerRecordService;
    @Autowired
    private UtilBiz utilBiz;
    @Reference
    private NotifyFacade notifyFacade;

    public Page workerRecordBizPage(Page<JobWorkerRecord> page, WorkerRecordQueryDto workerRecordQueryDto) {
        return workerRecordService.listPage(page, workerRecordQueryDto);
    }

    @NeedSetValue
    public JobWorkerRecord findById(Long id) {
        return workerRecordService.getById(id);
    }

    public List<JobWorkerVo> selectJobAndWorker(JobWorkerWebQueryDto workerWebQueryDto) {
        return workerRecordService.selectJobAndWorker(workerWebQueryDto);
    }

    public JobWorkerDetailVo selectJobAndWorkerDetail(Long id, String workerIdcard) {
        return workerRecordService.selectJobAndWorkerDetail(id, workerIdcard);
    }

    public void update(JobWorkerRecord jobWorkerRecord) {
        workerRecordService.updateById(jobWorkerRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateJobWorkerStatus(List<Long> ids, Integer jobWorkerStatus) {
        int res = workerRecordService.batchUpdateJobWorkerStatus(ids, jobWorkerStatus);
        if (jobWorkerStatus.equals(JobWorkerStatusEnum.PROCESSING.getCode())) {
            updateEmployed(ids);
        }
        return res;
    }

    private void updateEmployed(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids))return;

        List<JobWorkerRecord> jobWorkerRecords = workerRecordService.selectByIds(ids);
        List<String> jobIds = jobWorkerRecords.stream().map(JobWorkerRecord::getJobId).distinct().collect(Collectors.toList());
        List<JobWorkerCountVo> jobWorkerCountVos = workerRecordService.countWorker(jobIds);
        jobWorkerCountVos.forEach(e->{
            Job job = jobService.getJobByJobId(e.getJobId());
            job.setEmployedNum(e.getCount());
            jobService.updateById(job);
        });
    }

    public void submit(JobWorkerWebQueryDto workerWebQueryDto) {
        JobWorkerRecord jobWorkerRecord = workerRecordService.getOne(new QueryWrapper<JobWorkerRecord>()
                .eq(JobWorkerRecord.COL_JOB_ID, workerWebQueryDto.getJobId())
                .eq(JobWorkerRecord.COL_WORKER_IDCARD, workerWebQueryDto.getWorkerIdcard()));
        jobWorkerRecord.setAttachment(JSONArray.toJSONString(workerWebQueryDto.getAttachment()));
        jobWorkerRecord.setDeliveryContent(workerWebQueryDto.getDeliveryContent());
        jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.PROCESSING.getCode());
        jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.TO_BE_CONFIRMED.getCode());
        workerRecordService.updateById(jobWorkerRecord);
    }

    public void submitById(JobWorkerWebQueryDto workerWebQueryDto) {
        JobWorkerRecord jobWorkerRecord = workerRecordService.getById(workerWebQueryDto.getId());
        jobWorkerRecord.setAttachment(JSONArray.toJSONString(workerWebQueryDto.getAttachment()));
        jobWorkerRecord.setDeliveryContent(workerWebQueryDto.getDeliveryContent());
        jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.PROCESSING.getCode());
        jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.TO_BE_CONFIRMED.getCode());
        workerRecordService.updateById(jobWorkerRecord);
    }

    @NeedSetValue
    public List<JobWorkerRecord> listByJobId(String jobId) {
        return workerRecordService.list(new QueryWrapper<JobWorkerRecord>()
                .eq(JobWorkerRecord.COL_JOB_ID, jobId));
    }

    public void approved(JobWorkerApprovedDto jobWorkerApprovedDto) {
        JobWorkerRecord jobWorkerRecord = workerRecordService.getById(jobWorkerApprovedDto.getId());
        if (!(jobWorkerRecord.getDeliveryStatus().equals(DeliveryStatusEnum.TO_BE_CONFIRMED.getCode()) ||
                jobWorkerRecord.getDeliveryStatus().equals(DeliveryStatusEnum.FAIL.getCode()))) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不是审核状态");
        }
        switch (jobWorkerApprovedDto.getEvent()) {
            case ADOPT:
                jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.COMPLETED.getCode());
                jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getCode());
                jobWorkerRecord.setSettleStatus(SettleStatusEnum.PENDING_SETTLEMENT.getCode());
                jobWorkerRecord.setJobFinishTime(DateUtil.now());
                break;
            case FAIL:
                jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.FAIL.getCode());
                jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.PROCESSING.getCode());
                break;
            default:
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("操作异常");
        }
        jobWorkerRecord.setFailReason(jobWorkerApprovedDto.getFailReason());
        workerRecordService.updateById(jobWorkerRecord);
        log.info("更新雇员信息成功");
        if (jobWorkerApprovedDto.getEvent() == ApproveEvent.ADOPT) {
            log.info("审批通过，申请验收单与交付物明细");
            final ArrayList<Long> ids = Lists.newArrayList(jobWorkerApprovedDto.getId());
            notifyFacade.sendOne(MessageMsgDest.TOPIC_UPLOAD_JOB_FILE, NotifyTypeEnum.JOB_WORKER_FILE.getValue(),
                    MessageMsgDest.TAG_UPLOAD_JOB_FILE, JsonUtil.toString(ids));
        }
    }

    public JobWorkerDetailVo selectJobAndWorkerDetailById(Long id) {
        return workerRecordService.selectJobAndWorkerDetailById(id);
    }


    public void completeJobWorker(List<Long> workerIds){
        List<JobWorkerRecord> jobWorkerRecords = workerRecordService.selectByIds(workerIds);
        List<Long> ids = jobWorkerRecords.stream().filter(e -> !e.getJobStatus().equals(JobWorkerStatusEnum.REJECTED.getCode()))
                .map(JobWorkerRecord::getId).collect(Collectors.toList());
        workerRecordService.batchUpdateJobWorkerStatus(ids,JobWorkerStatusEnum.COMPLETED.getCode());
        JobWorkerUpdateVo jobWorkerUpdateVo=new JobWorkerUpdateVo();
        jobWorkerUpdateVo.setColumn("settle_status");
        jobWorkerUpdateVo.setValue(SettleStatusEnum.PENDING_SETTLEMENT.getCode());
        jobWorkerUpdateVo.setJobFinishTime(DateUtil.now());
        this.batchUpdateStatus(ids,jobWorkerUpdateVo);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_UPLOAD_JOB_FILE, NotifyTypeEnum.JOB_WORKER_FILE.getValue(),
                MessageMsgDest.TAG_UPLOAD_JOB_FILE, JsonUtil.toString(ids));
    }

    public int batchUpdateStatus(List<Long> ids, JobWorkerUpdateVo jobWorkerUpdateVo) {
        return workerRecordService.batchUpdateStatus(ids,jobWorkerUpdateVo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchApprovedSuccess(List<Long> ids) {
        ids.forEach(e->{
            JobWorkerApprovedDto jobWorkerApprovedDto=new JobWorkerApprovedDto();
            jobWorkerApprovedDto.setEvent(ApproveEvent.ADOPT);
            jobWorkerApprovedDto.setId(e);
            this.approved(jobWorkerApprovedDto);
        });
    }

    public void delete(Long id) {
        workerRecordService.removeById(id);
    }

    public List<JobWorkerRecord> listBy(Map<String, Object> map) {
        return workerRecordService.listBy(map);
    }

    public JobWorkerRecord getByJobIdAndIdCard(String jobId, String idCard) {
        return workerRecordService.getOne(new QueryWrapper<JobWorkerRecord>()
        .eq(JobWorkerRecord.COL_JOB_ID,jobId).eq(JobWorkerRecord.COL_WORKER_IDCARD, idCard));
    }

    public Integer countEmployedWorker(String jobId) {
        final int count = workerRecordService.count(new QueryWrapper<JobWorkerRecord>().eq(JobWorkerRecord.COL_JOB_ID, jobId)
                .and(qw -> qw.eq(JobWorkerRecord.COL_JOB_STATUS, JobWorkerStatusEnum.PROCESSING.getCode().intValue())
                        .or()
                        .eq(JobWorkerRecord.COL_JOB_STATUS, JobWorkerStatusEnum.COMPLETED.getCode().intValue()))
        );
        return count;
    }
	public JobWorkerRecord addWorkerApi(Job job, String name, String idCardNo, String phoneNo, String mchNo) {


        //api接口已鉴权过，无须再次鉴权
        JobWorkerRecord jobWorkerRecord = getByJobIdAndIdCard(job.getJobId(),idCardNo);
        //已存在，直接返回
        if (jobWorkerRecord != null && jobWorkerRecord.getJobStatus().intValue() != JobWorkerStatusEnum.FAIL_AUTH_FAIL.getCode()){
            return jobWorkerRecord;
        }

        JobWorkerStatusEnum jobWorkerStatus = job.getAcceptMode().intValue() == AcceptModeEnum.MANUAL.getCode().intValue()
                ? JobWorkerStatusEnum.WAIT_RECEIVED : JobWorkerStatusEnum.PROCESSING;

        if (jobWorkerRecord == null){
            jobWorkerRecord = new JobWorkerRecord();
            jobWorkerRecord.setJobId(job.getJobId());
            jobWorkerRecord.setJobName(job.getJobName());
            jobWorkerRecord.setEmployerName(job.getEmployerName());
            jobWorkerRecord.setEmployerNo(job.getEmployerNo());
            jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.NOT_DELIVERED.getCode());
            jobWorkerRecord.setJobAcceptTime(DateUtil.now());
            jobWorkerRecord.setWorkerName(name);
            jobWorkerRecord.setWorkerIdcard(idCardNo);
            jobWorkerRecord.setWorkerPhone(phoneNo);
            jobWorkerRecord.setGender(IdcardUtil.getGenderByIdCard(idCardNo));
            jobWorkerRecord.setJobStatus(jobWorkerStatus.getCode());
            jobWorkerRecord.setIsAssign(IsAssignEnum.YES.getCode());
            jobWorkerRecord.setAuthStatus(utilBiz.getAuthStatus(jobWorkerRecord.getWorkerIdCardMd5()));
            workerRecordService.save(jobWorkerRecord);
        }else{
            jobWorkerRecord.setWorkerIdcard(idCardNo);
            jobWorkerRecord.setWorkerName(name);
            jobWorkerRecord.setWorkerPhone(phoneNo);
            jobWorkerRecord.setJobStatus(jobWorkerStatus.getCode());
            jobWorkerRecord.setAuthStatus(utilBiz.getAuthStatus(jobWorkerRecord.getWorkerIdCardMd5()));
            update(jobWorkerRecord);
        }
        return jobWorkerRecord;
    }

    public JobWorkerRecord getByJobIdAndPhoneNo(String jobId, String phoneNo) {
        return workerRecordService.getOne(new QueryWrapper<JobWorkerRecord>()
                .eq(JobWorkerRecord.COL_JOB_ID,jobId).eq(JobWorkerRecord.COL_WORKER_PHONE, phoneNo));
    }}
