package com.zhixianghui.service.employee.aspect;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.internal.$Gson$Types;
import com.zhixianghui.common.statics.annotations.SetValue;
import com.zhixianghui.common.statics.result.PageResult;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 */
@Aspect
@Component
public class SetFieldAspect {

    @Autowired
    private ApplicationContext applicationContext;

    @AfterReturning(value = "@annotation(com.zhixianghui.common.statics.annotations.NeedSetValue)", returning = "ret")
    public Object doSetValue(JoinPoint jp, Object ret) throws Throwable {
        if(ObjectUtils.isEmpty(ret)){
            return ret;
        }
        if (ret instanceof Collection) {
            setFieldVal((Collection) ret);
        } else if (ret instanceof Page) {
            setFieldVal(((Page) ret).getRecords());
        } else {
            setFieldVal(Arrays.asList(ret));
        }
        return ret;
    }

    private void setFieldVal(Collection col) throws Exception {
        if (CollectionUtils.isEmpty(col)) {
            return;
        }
        Class<?> clazz = col.iterator().next().getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            SetValue annotation = field.getAnnotation(SetValue.class);
            if (annotation == null) {
                continue;
            }
            boolean isCheckNull = annotation.isCheckNull();
            //暴力访问
            field.setAccessible(true);
            String className = annotation.className();
            Class<?> beanClass = Class.forName(className);
            Object bean = this.applicationContext.getBean(beanClass);

            String[] params = annotation.paramField();
            Class<?>[] types = new Class[params.length];
            for (int i = 0; i < params.length; i++) {
                types[i] = clazz.getDeclaredField(params[i]).getType();
            }

            Method method = beanClass
                    .getMethod(annotation.method(), types);

            Field[] paramFields = new Field[params.length];
            for (int i = 0; i < params.length; i++) {
                paramFields[i] = clazz.getDeclaredField(params[i]);
                paramFields[i].setAccessible(true);
            }

            out:
            for (Object obj : col) {
                if(field.get(obj)!=null){
                    continue;
                }
                Object[] paramValues = new Object[params.length];
                for (int i = 0; i < params.length; i++) {
                    Object paramValue = paramFields[i].get(obj);
                    if (isCheckNull && paramValue == null) {
                        continue out;
                    }
                    paramValues[i] = paramValue;
                }
                Object value = method.invoke(bean, paramValues);
                if (value != null && !(value instanceof String) && !(value instanceof Date)) {
                    if (!StringUtils.isEmpty(annotation.targetField())) {
                        Field targetField = value.getClass().getDeclaredField(annotation.targetField());
                        targetField.setAccessible(true);
                        value = targetField.get(value);
                    }
                }
                field.set(obj, value);
            }
        }
    }

}
