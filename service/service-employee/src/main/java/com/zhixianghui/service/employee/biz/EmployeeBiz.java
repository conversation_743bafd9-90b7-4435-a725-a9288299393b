package com.zhixianghui.service.employee.biz;

import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.service.WeChatUserFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

@Service
public class EmployeeBiz {

    @Reference
    private UserInfoFacade userInfoFacade;
    @Reference
    private WeChatUserFacade weChatUserFacade;

    public String getIdCardNoByPhone(String phone) {

        final UserInfo userInfo = weChatUserFacade.getUserInfoByPhone(phone);
        return userInfo == null ? null : userInfo.getReceiveIdCardNoDecrypt();
    }


}
