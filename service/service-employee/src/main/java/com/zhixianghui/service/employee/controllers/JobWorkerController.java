package com.zhixianghui.service.employee.controllers;

import com.alibaba.fastjson.JSONArray;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.employee.annotation.CurrentLoginVo;
import com.zhixianghui.facade.employee.dto.JobWorkerWebQueryDto;
import com.zhixianghui.facade.employee.vo.JobWorkerDetailVo;
import com.zhixianghui.facade.employee.vo.JobWorkerVo;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.employee.biz.JobBiz;
import com.zhixianghui.service.employee.biz.JobWorkerRecordBiz;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月16日 10:40:00
 */
@RestController
@RequestMapping("/jobWorker")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class JobWorkerController extends BaseController{

    final private JobWorkerRecordBiz jobWorkerRecordBiz;

    final private JobBiz jobBiz;

    @PostMapping("/selectJobAndWorker")
    public RestResult selectJobAndWorker(@RequestBody JobWorkerWebQueryDto workerWebQueryDto
            ,@CurrentLoginVo WeChatLoginVo weChatLoginVo){

        workerWebQueryDto.setWorkerIdcard(weChatLoginVo.getIdCard());
        startPage(workerWebQueryDto);
        List<JobWorkerVo> list = jobWorkerRecordBiz.selectJobAndWorker(workerWebQueryDto);
        return toTable(list);
    }

    @PostMapping("/selectJobAndWorkerDetail/{id}")
    public RestResult selectJobAndWorkerDetail(@PathVariable("id")Long id,@CurrentLoginVo WeChatLoginVo weChatLoginVo){
        JobWorkerDetailVo jobWorkerDetailVo = jobWorkerRecordBiz.selectJobAndWorkerDetail(id, weChatLoginVo.getIdCard());
        Map<String, Object> map = BeanUtil.toMap(jobWorkerDetailVo);
        map.put("attachment", JSONArray.parseArray((String)map.get("attachment"),String.class));
        return success(map);
    }

    @PostMapping("/submit")
    public RestResult submit(@RequestBody JobWorkerWebQueryDto workerWebQueryDto
            ,@CurrentLoginVo WeChatLoginVo weChatLoginVo){
        workerWebQueryDto.setWorkerIdcard(weChatLoginVo.getIdCard());
        jobWorkerRecordBiz.submit(workerWebQueryDto);
        return success("提交成功");
    }

    @PostMapping("/acceptJob/{id}")
    public RestResult acceptJob(@PathVariable("id") Long id, @CurrentLoginVo WeChatLoginVo weChatLoginVo){
        jobBiz.acceptJob(id,weChatLoginVo);
        return success("提交成功");
    }



}
