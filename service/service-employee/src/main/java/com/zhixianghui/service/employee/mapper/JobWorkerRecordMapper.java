package com.zhixianghui.service.employee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.employee.dto.JobWorkerWebQueryDto;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.vo.JobWorkerCountVo;
import com.zhixianghui.facade.employee.vo.JobWorkerDetailVo;
import com.zhixianghui.facade.employee.vo.JobWorkerUpdateVo;
import com.zhixianghui.facade.employee.vo.JobWorkerVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface JobWorkerRecordMapper extends BaseMapper<JobWorkerRecord> {

    Page<JobWorkerRecord> listBy(Page<JobWorkerRecord> page,@Param("param") Map<String,Object> param);

    List<JobWorkerRecord> listBy(@Param("param") Map<String,Object> param);

    int batchUpdateJobWorkerStatus(@Param("ids") List<Long> ids,@Param("jobWorkerStatus") Integer jobWorkerStatus);
    List<JobWorkerVo> selectJobAndWorker(JobWorkerWebQueryDto workerWebQueryDto);

    JobWorkerDetailVo selectJobAndWorkerDetail(@Param("id") Long id,@Param("workerIdcard") String workerIdcard);

    JobWorkerDetailVo selectJobAndWorkerDetailById(@Param("id") Long id);

    List<JobWorkerRecord> selectByIds(@Param("ids") List<Long> ids);

    List<JobWorkerCountVo> countWorker(@Param("jobIds") List<String> jobIds);

    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("vo") JobWorkerUpdateVo jobWorkerUpdateVo);
}
