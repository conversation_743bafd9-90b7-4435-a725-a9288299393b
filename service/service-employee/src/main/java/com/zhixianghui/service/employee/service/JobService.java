package com.zhixianghui.service.employee.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.employee.dto.JobWebQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.service.employee.mapper.JobMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class JobService extends ServiceImpl<JobMapper, Job> {
    @Autowired
    private JobMapper jobMapper;

    public Page<Job> pageJobs(IPage<Job> page, Map<String, Object> param) {
        return baseMapper.listBy(page, param);
    }

    public List<Job> list(JobWebQueryDto jobWebQueryDto) {
        return jobMapper.selectJobList(jobWebQueryDto);
    }

    public List<Job> listJobListOnGrant(Map<String, Object> param) {
        return jobMapper.listJobListOnGrant(param);
    }
    public void deleteById(Long id) {
        int row = jobMapper.deleteById(id);
        if(row!=1){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("删除记录数不为1");
        }
    }

    public void update(Job job){
        int row = jobMapper.updateJob(job);
        if(row ==0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("更新记录数为0");
        }
    }

    public Job getJobByJobId(String jobId) {
        return this.getOne(new QueryWrapper<Job>().eq(Job.COL_JOB_ID, jobId));
    }
}
