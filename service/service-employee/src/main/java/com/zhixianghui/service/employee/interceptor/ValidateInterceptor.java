package com.zhixianghui.service.employee.interceptor;

import com.zhixianghui.api.base.params.RequestParam;
import com.zhixianghui.api.base.utils.RequestUtil;
import com.zhixianghui.api.base.webmvc.ContentCachingRequestWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 通用校验拦截器
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ValidateInterceptor implements HandlerInterceptor {
    /**
     * 接口参数、状态校验等前置统一校验
     * 异常直接抛出，由全局异常处理器捕获处理
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
//        ContentCachingRequestWrapper httpRequest = (ContentCachingRequestWrapper)request;
//        RequestParam param = RequestUtil.getRequestParam(httpRequest);
        // 接口版本号校验

        return true;
    }
}
