package com.zhixianghui.service.employee.controllers;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.employee.annotation.CurrentLoginVo;
import com.zhixianghui.facade.employee.dto.JobWebQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.IsAssignEnum;
import com.zhixianghui.facade.employee.enums.JobStatusEnum;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import com.zhixianghui.service.employee.biz.JobBiz;
import com.zhixianghui.service.employee.facade.JobFacadImpl;
import com.zhixianghui.service.employee.service.JobWorkerRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("job")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class JobController extends BaseController {

    final private JobBiz jobBiz;

    final private JobFacadImpl jobFacad;

    final private JobWorkerRecordService jobWorkerRecordService;

    @PostMapping("/list")
    public RestResult list(@RequestBody JobWebQueryDto jobWebQueryDto, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        if (StringUtils.isBlank(weChatLoginVo.getIdCard())) {
            throw ApiExceptions.API_NONE_IDENTITY_INFO.newWithErrMsg("请先进行实名认证");
        }
        if (jobWebQueryDto.getIsAssign()!=null&&jobWebQueryDto.getIsAssign().equals(IsAssignEnum.YES.getCode())) {
            List<JobWorkerRecord> list = jobWorkerRecordService.list(new QueryWrapper<JobWorkerRecord>()
                    .eq(JobWorkerRecord.COL_WORKER_IDCARD, weChatLoginVo.getIdCard())
                    .eq(JobWorkerRecord.COL_IS_ASSIGN, IsAssignEnum.YES.getCode()));
            List<String> jobIds = list.stream().map(JobWorkerRecord::getJobId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(jobIds)) {
                jobIds.add("-1");
            }
            jobWebQueryDto.setJobIds(jobIds);
        }
        jobWebQueryDto.setJobStatus(JobStatusEnum.PROCESSING.getCode());
        startPage(jobWebQueryDto);
        List<Job> list = jobBiz.list(jobWebQueryDto);
        return toTable(list);
    }

    @PostMapping("/getJob/{id}")
    public RestResult getJob(@PathVariable("id") Long id, @CurrentLoginVo WeChatLoginVo weChatLoginVo) {
        if (StringUtils.isBlank(weChatLoginVo.getIdCard())) {
            throw ApiExceptions.API_NONE_IDENTITY_INFO.newWithErrMsg("请先进行实名认证");
        }

        Job job = jobBiz.getJobById(id);
        JobWorkerRecord jobWorkerRecord = Optional.ofNullable(jobWorkerRecordService.getOne(new QueryWrapper<JobWorkerRecord>()
                .eq(JobWorkerRecord.COL_JOB_ID, job.getJobId())
                .eq(JobWorkerRecord.COL_WORKER_IDCARD,weChatLoginVo.getIdCard()))).orElse(new JobWorkerRecord());
        Map<String, Object> map = BeanUtil.toMap(job);
        map.put("workerJobStatus",jobWorkerRecord.getJobStatus());
        return success(map);
    }




}
