package com.zhixianghui.service.employee.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.employee.dto.JobWorkerApprovedDto;
import com.zhixianghui.facade.employee.dto.JobWorkerWebQueryDto;
import com.zhixianghui.facade.employee.dto.WorkerRecordQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.service.JobWorkerRecordFacade;
import com.zhixianghui.facade.employee.vo.JobWorkerDetailVo;
import com.zhixianghui.facade.employee.vo.JobWorkerUpdateVo;
import com.zhixianghui.service.employee.biz.JobWorkerRecordBiz;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Service
public class JobWorkerRecordFacadeImpl implements JobWorkerRecordFacade {
    @Autowired
    private JobWorkerRecordBiz jobWorkerRecordBiz;

    @Override
    public Page<JobWorkerRecord> workerRecordPage(Page<JobWorkerRecord> page, WorkerRecordQueryDto workerRecordQueryDto){
        return jobWorkerRecordBiz.workerRecordBizPage(page, workerRecordQueryDto);
    }

    @Override
    public List<JobWorkerRecord> listBy(Map<String, Object> map) {
        return jobWorkerRecordBiz.listBy(map);
    }

    @Override
    public JobWorkerRecord findById(Long id) {
        return jobWorkerRecordBiz.findById(id);
    }

    @Override
    public void update(JobWorkerRecord jobWorkerRecord) {
        jobWorkerRecordBiz.update(jobWorkerRecord);
    }

    @Override
    public int batchUpdateJobWorkerStatus(List<Long> ids, Integer jobWorkerStatus) {
        return jobWorkerRecordBiz.batchUpdateJobWorkerStatus(ids, jobWorkerStatus);
    }

    @Override
    public void approved(JobWorkerApprovedDto jobWorkerApprovedDto) {
        jobWorkerRecordBiz.approved(jobWorkerApprovedDto);
    }

    @Override
    public JobWorkerDetailVo selectJobAndWorkerDetailById(Long id) {
        return jobWorkerRecordBiz.selectJobAndWorkerDetailById(id);
    }

    @Override
    public void completeJobWorker(List<Long> workerIds) {
        jobWorkerRecordBiz.completeJobWorker(workerIds);
    }

    @Override
    public int batchUpdateStatus(List<Long> ids, JobWorkerUpdateVo jobWorkerUpdateVo) {
        return jobWorkerRecordBiz.batchUpdateStatus(ids,jobWorkerUpdateVo);
    }

    @Override
    public void batchApprovedSuccess(List<Long> ids) {
        jobWorkerRecordBiz.batchApprovedSuccess(ids);
    }

    @Override
    public void submit(JobWorkerWebQueryDto workerWebQueryDto) {
        jobWorkerRecordBiz.submitById(workerWebQueryDto);
    }

    @Override
    public void delete(Long id) {
        jobWorkerRecordBiz.delete(id);
    }

    @Override
    public JobWorkerRecord getByJobIdAndIdCard(String jobId, String idCard) {
        return jobWorkerRecordBiz.getByJobIdAndIdCard(jobId,idCard);
    }

    @Override
    public Integer countEmployedWorker(String jobId) {
        return jobWorkerRecordBiz.countEmployedWorker(jobId);
    }
	
    @Override
    public JobWorkerRecord getByJobIdAndPhoneNo(String jobId, String phoneNo) {
        return jobWorkerRecordBiz.getByJobIdAndPhoneNo(jobId,phoneNo);
    }

    @Override
    public JobWorkerRecord addWorkerApi(Job job, String name, String idCardNo, String phoneNo, String mchNo) {
        return jobWorkerRecordBiz.addWorkerApi(job,name,idCardNo,phoneNo,mchNo);
    }}
