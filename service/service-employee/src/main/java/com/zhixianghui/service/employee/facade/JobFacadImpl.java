package com.zhixianghui.service.employee.facade;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.constants.redis.RedisKeysManage;
import com.zhixianghui.common.statics.enums.auth.AuthChannelEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.facade.employee.dto.*;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.*;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.employee.vo.JobVo;
import com.zhixianghui.facade.trade.dto.AuthReqDto;
import com.zhixianghui.facade.trade.service.AuthRecordFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.service.WeChatUserFacade;
import com.zhixianghui.facade.trade.vo.auth.AuthResponseVo;
import com.zhixianghui.service.employee.biz.JobBiz;
import com.zhixianghui.service.employee.biz.JobWorkerRecordBiz;
import com.zhixianghui.service.employee.excel.JobMemberListener;
import com.zhixianghui.service.employee.service.JobWorkerRecordService;
import com.zhixianghui.service.employee.vo.JobReadVo;
import com.zhixianghui.starter.comp.component.RedisLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class JobFacadImpl implements JobFacade {
    private static final int FILE_SIZE = 5;
    private static final String UNIT = "M";
    private static final String SUFFIX = ".xlsx";
    private static final int HEAD_ROW_NUMBER = 2;

    final private JobBiz jobBiz;

    final private JobWorkerRecordBiz workerRecordBiz;

    final private JobWorkerRecordService jobWorkerRecordService;
    @Reference
    private UserInfoFacade userInfoFacade;
    @Reference
    private AuthRecordFacade authRecordFacade;
    @Reference
    private WeChatUserFacade weChatUserFacade;

    final private RedisLock redisLock;

    @Override
    public Job addJob(JobDto jobDto) {

        final Job job = jobBiz.addJob(jobDto);
        return job;

    }

    @Override
    public Job addJobApi(JobDto jobDto) {
        Job job = jobBiz.addJobApi(jobDto);
        return job;
    }

    @Override
    public Page<Job> pageJob(Page page, Map<String, Object> param) {
        return jobBiz.pageJob(page, param);
    }

    @Override
    public Page<Job> pageJob(Page page, JobQueryDto jobQueryDto) {
        return jobBiz.pageJob(page, jobQueryDto);
    }

    @Override
    public JobVo getJobById(Long id) {
        Job job = jobBiz.getJobById(id);
        JobVo jobVo = BeanUtil.toObject(JobVo.class, job);
        List<JobWorkerRecord> jobWorkerRecords = workerRecordBiz.listByJobId(job.getJobId());
        jobVo.setJobWorkerRecords(jobWorkerRecords);
        return jobVo;
    }

    @Override
    public Job getById(Long id) {
        return jobBiz.getJobById(id);
    }

    @Override
    public Job getJobByJobId(String jobId) {
        return jobBiz.getJobByJobId(jobId);
    }

    @Override
    public void deleteById(Long id) {
        jobBiz.deleteById(id);
    }

    @Override
    public Job updateById(JobDto jobDto) {
        return jobBiz.updateById(jobDto);
    }

    @Override
    public void approved(Long[] ids, ApproveEvent event, String updator) {
        jobBiz.approved(ids, event, updator);
    }

    @Override
    public List<Job> list(JobWebQueryDto jobWebQueryDto) {
        return jobBiz.list(jobWebQueryDto);
    }

    @Override
    public List<Job> listJobListOnGrant(Map<String, Object> param) {
        return jobBiz.listJobListOnGrant(param);
    }

    @Override
    public void completeJob(Long id) {
        jobBiz.completeJob(id);
    }

    @Override
    public void uploadJobMember(Long id, String name, byte[] file2byte) {
        JobVo jobVo = this.getJobById(id);
        if (ObjectUtils.isEmpty(jobVo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("没有找到任务信息");
        }
        List<String> idCards = jobVo.getJobWorkerRecords().stream()
                .map(JobWorkerRecord::getWorkerIdcard).collect(Collectors.toList());
        JobReadVo jobReadVo = new JobReadVo();
        jobReadVo.setJobVo(jobVo);
        jobReadVo.setJobWorkerRecordService(jobWorkerRecordService);
        jobReadVo.setUserInfoFacade(userInfoFacade);
        jobReadVo.setAuthRecordFacade(authRecordFacade);
        jobReadVo.setHeadRowNum(HEAD_ROW_NUMBER);
        jobReadVo.setIdCards(idCards);
        File excelFile = FileUtils.byte2file(file2byte, System.getProperty("java.io.tmpdir") + System.getProperty("file.separator") +
                name);
        Executors.newSingleThreadExecutor().execute(() -> {
            String clientId = redisLock.tryLockLong(RedisKeysManage.getJobImportExcelLockKey(id), 2 * 60 * 1000, 2);
            try {
                if (!StringUtils.isBlank(clientId)) {
                    EasyExcel.read(excelFile, JobMemberExcelDto.class, new JobMemberListener(jobReadVo)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
                    log.info("[{}]雇员导入成功:{}", id, idCards);
//                    List<String> jobIds = new ArrayList<>();
//                    jobIds.add(jobVo.getJobId());
//                    List<JobWorkerCountVo> jobWorkerCountVos = jobWorkerRecordService.countWorker(jobIds);
//                    jobWorkerCountVos.forEach(e->{
//                        Job job = jobBiz.getJobByJobId(e.getJobId());
//                        job.setEmployedNum(e.getCount());
//                        jobBiz.update(job);
//                    });

                } else {
                    log.info("[{}]雇员导入获取锁失败", id);
                }
            } catch (BizException e) {
                log.error("雇员导入业务异常:", e);
            } catch (Exception e) {
                log.error("雇员导入,excel解析异常:", e);
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("解析Excel文件出错");
            } finally {
                redisLock.unlockLong(clientId);
                excelFile.delete();
            }
        });

    }

    @Override
    public void uploadJobMember(SignedJobMemberDto jobMemberDto) {
        JobVo jobVo = this.getJobById(jobMemberDto.getId());
        if (ObjectUtils.isEmpty(jobVo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("没有找到任务信息");
        }
        List<String> idCards = jobVo.getJobWorkerRecords().stream()
                .map(JobWorkerRecord::getWorkerIdcard).collect(Collectors.toList());

        for (JobMemberExcelDto jobMember : jobMemberDto.getJobMembers()) {
            if (idCards.contains(jobMember.getWorkerIdcard())) {
                final JobWorkerRecord jobWorkerRecord = jobWorkerRecordService.getWorkerRecordByJobIdAndIdCardNo(jobVo.getJobId(), jobMember.getWorkerIdcard());
                if (jobWorkerRecord != null) {
                    JobWorkerStatusEnum jobWorkerStatus = jobVo.getAcceptMode().intValue() == AcceptModeEnum.MANUAL.getCode().intValue() ? JobWorkerStatusEnum.WAIT_RECEIVED : JobWorkerStatusEnum.PROCESSING;

                    jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.NOT_DELIVERED.getCode());
                    jobWorkerRecord.setJobAcceptTime(DateUtil.now());
                    jobWorkerRecord.setWorkerName(jobMember.getWorkerName());
                    jobWorkerRecord.setWorkerIdcard(jobMember.getWorkerIdcard());
                    jobWorkerRecord.setWorkerPhone(jobMember.getWorkerPhone());
                    jobWorkerRecord.setGender(IdcardUtil.getGenderByIdCard(jobMember.getWorkerIdcard()));
                    jobWorkerRecord.setJobStatus(jobWorkerStatus.getCode());
                    jobWorkerRecord.setIsAssign(IsAssignEnum.YES.getCode());

                    /**
                     * 鉴权
                     */
                    AuthReqDto authReqDto = new AuthReqDto();
                    authReqDto.setName(jobMember.getWorkerName());
                    authReqDto.setIdCardNo(jobMember.getWorkerIdcard());
                    authReqDto.setAuthType(AuthTypeEnum.IDCARD_NAME.getValue());
//                    authReqDto.setAuthChannel(AuthChannelEnum.ADM.name());

                    final AuthResponseVo responseVo = authRecordFacade.auth(authReqDto);
                    if (responseVo.getAuthStatus().intValue() != BankAuthStatusEnum.SUCCESS.getValue()) {
                        jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.FAIL_AUTH_FAIL.getCode());
                    }

                    jobWorkerRecordService.updateById(jobWorkerRecord);

                    idCards.add(jobMember.getWorkerIdcard());

                    return;
                }

            }

            JobWorkerStatusEnum jobWorkerStatus = jobVo.getAcceptMode().intValue() == AcceptModeEnum.MANUAL.getCode().intValue() ? JobWorkerStatusEnum.WAIT_RECEIVED : JobWorkerStatusEnum.PROCESSING;
            JobWorkerRecord jobWorkerRecord = new JobWorkerRecord();
            jobWorkerRecord.setJobId(jobVo.getJobId());
            jobWorkerRecord.setJobName(jobVo.getJobName());
            jobWorkerRecord.setEmployerName(jobVo.getEmployerName());
            jobWorkerRecord.setEmployerNo(jobVo.getEmployerNo());
            jobWorkerRecord.setDeliveryStatus(DeliveryStatusEnum.NOT_DELIVERED.getCode());
            jobWorkerRecord.setJobAcceptTime(DateUtil.now());
            jobWorkerRecord.setWorkerName(jobMember.getWorkerName());
            jobWorkerRecord.setWorkerIdcard(jobMember.getWorkerIdcard());
            jobWorkerRecord.setWorkerPhone(jobMember.getWorkerPhone());
            jobWorkerRecord.setGender(IdcardUtil.getGenderByIdCard(jobMember.getWorkerIdcard()));
            jobWorkerRecord.setJobStatus(jobWorkerStatus.getCode());
            jobWorkerRecord.setIsAssign(IsAssignEnum.YES.getCode());
            jobWorkerRecord.setMainstayNo(jobVo.getMainstayNo());
            jobWorkerRecord.setMainstayName(jobVo.getMainstayName());

            /**
             * 鉴权
             */
            AuthReqDto authReqDto = new AuthReqDto();
            authReqDto.setName(jobMember.getWorkerName());
            authReqDto.setIdCardNo(jobMember.getWorkerIdcard());
            authReqDto.setAuthType(AuthTypeEnum.IDCARD_NAME.getValue());
//            authReqDto.setAuthChannel(AuthChannelEnum.ADM.name());

            final AuthResponseVo responseVo = authRecordFacade.auth(authReqDto);
            if (responseVo.getAuthStatus().intValue() != BankAuthStatusEnum.SUCCESS.getValue()) {
                jobWorkerRecord.setJobStatus(JobWorkerStatusEnum.FAIL_AUTH_FAIL.getCode());
            }

            jobWorkerRecordService.save(jobWorkerRecord);

            idCards.add(jobMember.getWorkerIdcard());
        }
    }

    @Override
    public String getQrCode(Long id) {
        Job job  = getById(id);
        if (StringUtils.isBlank(job.getAppCodeUrl())){
            job.setAppCodeUrl(weChatUserFacade.getAppCode(id));
            jobBiz.update(job);
        }
        return job.getAppCodeUrl();
    }
}


