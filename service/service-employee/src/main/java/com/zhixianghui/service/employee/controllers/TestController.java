package com.zhixianghui.service.employee.controllers;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.employee.annotation.CurrentLoginVo;
import com.zhixianghui.facade.employee.enums.ApproveEvent;
import com.zhixianghui.facade.trade.vo.WeChatLoginVo;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月14日 18:19:00
 */
@RestController
@RequestMapping("/weChat")
public class TestController {

    @RequestMapping("/test")
    public RestResult test(@CurrentLoginVo WeChatLoginVo weChatLoginVo){
        return RestResult.success(weChatLoginVo);
    }

    @PostMapping("/testId/{ids}")
    public RestResult testId(@PathVariable("ids")List<Long> ids, @RequestParam("event")ApproveEvent event){
        return RestResult.success(ids);
    }
}