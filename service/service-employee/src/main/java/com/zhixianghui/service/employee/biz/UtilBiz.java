package com.zhixianghui.service.employee.biz;

import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月04日 09:56:00
 */
@Service
public class UtilBiz {
    @Reference
    private UserInfoFacade userInfoFacade;
    @Reference
    private SignRecordFacade signRecordFacade;

    public Integer getAuthStatus(String workerIdcardMd5) {
        if (userInfoFacade.isVerified(workerIdcardMd5)) {
            return AuthStatusEnum.SUCCESS.getValue();
        }

        return AuthStatusEnum.FAIL.getValue();
    }

    public Integer getAuthStatusWithNoMd5(String workerIdcard) {
        if (userInfoFacade.isVerified(MD5Util.getMixMd5Str(workerIdcard))) {
            return AuthStatusEnum.SUCCESS.getValue();
        }

        return AuthStatusEnum.FAIL.getValue();
    }

}
