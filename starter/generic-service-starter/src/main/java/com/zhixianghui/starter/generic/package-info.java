/**
 * 说明：
 *  1、当前模块的service包下的 zhixianghuiService 是对各服务提供者的接口封装，大体上分为 基础服务、业务服务 两类，基础服务包括：
 *      service-global-lock、service-message、service-config 等服务，而业务服务当然就是各个业务的服务，如：计费服务、收单服务等。
 *  2、当前模块的service包下 zhixianghuiService 虽然没有直接依赖各个服务提供者的facade包，但对接口名、方法名、方法的入参/回参等
 *      依然是有依赖的，所以，如果服务提供者那边的信息有变动，这边也是需要跟着改动的
 *  3、使用泛化调用的好处是不需要直接依赖各服务提供者的facade包，在项目结构上解耦合了，缺点是虽然结构上解耦，但是逻辑上依然没有解耦，
 *      甚至因为多了一层封装，还带来了一定的性能损耗，这两种特点就决定了本模块的适用场景大概有如下：
 *        3.1、比较适合那些需要调用接口，但又不想直接依赖其facade包的情况，如在某些情况下，A服务只是想调用一下B服务的一个接口，
 *              此时如果直接依赖它的facade包，未免有些过于臃肿
 *        3.2、把某些基础服务的多个接口聚合到一起，提供一个更大粒度的功能，如：GlobalLockHelper，提供加锁、解锁、邮件预警
 *        3.3、在网关层，进行方法调用的路由转发
 *  4、为了避免带来过多的系统开销，当前模块提供的聚合功能，采取按需启用的策略，默认情况下都不开启，需要的时候可自行在配置文件中开启，
 *      具体的配置参数请在DubboServiceAutoConfiguration中查看
 */
package com.zhixianghui.starter.generic;