package com.zhixianghui.starter.generic.service;

import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.starter.generic.invoker.DubboServiceInvoker;
import com.zhixianghui.starter.generic.invoker.Parameters;

import java.util.List;

/**
 * <AUTHOR>
 * 基础服务的方法聚合类
 */
public class BaseService {
    private DubboServiceInvoker dubboServiceInvoker;

    public BaseService(DubboServiceInvoker dubboServiceInvoker) {
        this.dubboServiceInvoker = dubboServiceInvoker;
    }

    /**
     * 异步发送邮件，调用了 com.zhixianghui.facade.banklink.service.message.EmailFacade#sendAsync(String,String,String)
     *
     * @param groupKey
     * @param subject
     * @param content
     * @return
     */
    public boolean sendMailAsync(String groupKey, String subject, String content) {
        String methodName = "sendAsync";

        Parameters parameters = Parameters.newInstance()
                .addParameter(String.class.getName(), groupKey)
                .addParameter(String.class.getName(), subject)
                .addParameter(String.class.getName(), content);

        Object result = dubboServiceInvoker.invoke(InterfaceConst.EMAIL_FACADE, methodName, parameters);
        if (result != null) {
            return (boolean) result;
        } else {
            return false;
        }
    }

    /**
     * 发送邮件，调用了 com.zhixianghui.facade.banklink.service.message.EmailFacade#sendAsync(EmailFromEnum,String,String[],String,String)
     *
     * @param from
     * @param to
     * @param cc
     * @param subject
     * @param content
     * @return
     */
    public boolean sendMail(EmailFromEnum from, String to, String[] cc, String subject, String content) {
        String methodName = "send";

        Parameters parameters = Parameters.newInstance()
                .addParameter(EmailFromEnum.class.getName(), from)
                .addParameter(String.class.getName(), to)
                .addParameter(String[].class.getName(), cc)
                .addParameter(String.class.getName(), subject)
                .addParameter(String.class.getName(), content);

        Object result = dubboServiceInvoker.invoke(InterfaceConst.EMAIL_FACADE, methodName, parameters);
        if (result != null) {
            return (boolean) result;
        } else {
            return false;
        }
    }

    /**
     * 加锁，调用了 com.zhixianghui.facade.common.service.GlobalLockFacade#tryLock(String,int,String)
     *
     * @param resourceId
     * @param expireSecond
     * @param clientFlag
     * @return
     */
    public String tryLock(String resourceId, int expireSecond, String clientFlag) {
        String methodName = "tryLock";

        Parameters parameters = Parameters.newInstance()
                .addParameter(String.class.getName(), resourceId)
                .addParameter(int.class.getName(), expireSecond)
                .addParameter(String.class.getName(), clientFlag);

        Object result = dubboServiceInvoker.invoke(InterfaceConst.GLOBAL_LOCK_FACADE, methodName, parameters);
        if (result != null) {
            return (String) result;
        } else {
            return null;
        }
    }

    /**
     * 释放锁，调用了 com.zhixianghui.facade.common.service.GlobalLockFacade#unlock(String,String)
     *
     * @param resourceId
     * @param clientId
     * @return
     */
    public boolean unlock(String resourceId, String clientId) {
        String methodName = "unlock";

        Parameters parameters = Parameters.newInstance()
                .addParameter(String.class.getName(), resourceId)
                .addParameter(String.class.getName(), clientId);

        Object result = dubboServiceInvoker.invoke(InterfaceConst.GLOBAL_LOCK_FACADE, methodName, parameters);
        if (result != null) {
            return (boolean) result;
        } else {
            return false;
        }
    }

    /**
     * 调用了 com.zhixianghui.facade.common.service.SequenceFacade#nextSnowId(int, java.lang.String, boolean)
     *
     * @param prefix
     * @param isWithDate
     * @return
     */
    public List<String> nextSnowId(String prefix, boolean isWithDate, int count) {
        String methodName = "nextSnowId";
        Parameters parameters = Parameters.newInstance()
                .addParameter(int.class.getName(), count)
                .addParameter(String.class.getName(), prefix)
                .addParameter(boolean.class.getName(), isWithDate);
        try {
            Object result = dubboServiceInvoker.invoke(InterfaceConst.SEQUENCE_FACADE, methodName, parameters);
            if (result instanceof List) {
                return (List<String>) result;
            } else {
                throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("雪花算法序列号生成服务不可用");
            }
        } catch (Exception ex) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("雪花算法序列号生成服务不可用");
        }
    }


    /**
     * 调用了 com.zhixianghui.facade.common.service.SequenceFacade#nextSegmentId(java.lang.String, int)
     *
     * @param bizTag
     * @param count
     * @return
     */
    public List<Long> nextSegmentId(String bizTag, int count) {
        String methodName = "nextSegmentId";
        Parameters parameters = Parameters.newInstance()
                .addParameter(String.class.getName(), bizTag)
                .addParameter(int.class.getName(), count);
        try {
            Object result = dubboServiceInvoker.invoke(InterfaceConst.SEQUENCE_FACADE, methodName, parameters);
            if (result instanceof List) {
                return (List<Long>) result;
            } else {
                throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("segment序列号生成服务不可用");
            }
        } catch (Exception ex) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("segment序列号生成服务不可用");
        }
    }
}
