package com.zhixianghui.starter.generic.hepler;

import com.zhixianghui.common.statics.dto.seq.SeqTypeDto;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.starter.generic.service.BaseService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Author: Cmf
 * Date: 2020.3.13
 * Time: 15:58
 * Description:
 */
public class SequenceHelper {
    private BaseService baseService;

    public SequenceHelper(BaseService baseService) {
        this.baseService = baseService;
    }

    /**
     * 生成单个序列号
     *
     * @return
     */
    public String genSeqNo(SeqTypeDto seqTypeDto) {
        if (seqTypeDto == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("seqTypeDto不能为null");
        }
        if (seqTypeDto.getSeqAlgorithm() == SeqTypeDto.ALGORITHM_SNOW) {
            return baseService.nextSnowId(seqTypeDto.getPrefix(), seqTypeDto.isWithDate(), 1).get(0);
        } else {
            Long value = baseService.nextSegmentId(seqTypeDto.getPrefix(), 1).get(0);
            int subLength = seqTypeDto.getLength() - seqTypeDto.getPrefix().length();
            String formatStr = "%s%" + (subLength > 0 ? "0" + subLength : "") + "d";
            return String.format(formatStr, seqTypeDto.getPrefix(), value);
        }
    }

    /**
     * 生成批量序列号
     *
     * @param seqTypeDto
     * @param count
     * @return
     */
    public List<String> genBatchSeqNo(SeqTypeDto seqTypeDto, int count) {
        if (seqTypeDto == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("seqTypeDto不能为null");
        }
        if (seqTypeDto.getSeqAlgorithm() == SeqTypeDto.ALGORITHM_SNOW) {
            return baseService.nextSnowId(seqTypeDto.getPrefix(), seqTypeDto.isWithDate(), count);
        } else {
            List<Long> longs = baseService.nextSegmentId(seqTypeDto.getPrefix(), count);
            int subLength = seqTypeDto.getLength() - seqTypeDto.getPrefix().length();
            String formatStr = "%s%" + (subLength > 0 ? "0" + subLength : "") + "d";
            return longs.stream().map(l -> String.format(formatStr, seqTypeDto.getPrefix(), l)).collect(Collectors.toList());
        }
    }

}
