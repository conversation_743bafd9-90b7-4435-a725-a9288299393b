package com.zhixianghui.starter.comp.rmq;

import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.dto.rmq.MsgDto;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.util.utils.JsonUtil;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @description: rocketmq监听抽象类，其余需继承此类
 * @author: xingguang li
 * @created: 2020/09/07 14:46
 */
@Component
public abstract class BaseRocketMQListener<T> implements RocketMQListener<MessageExt>, RocketMQPushConsumerLifecycleListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public ThreadLocal<MessageExt> getMessageExtThreadLocal() {
        return messageExtThreadLocal;
    }

    private ThreadLocal<MessageExt> messageExtThreadLocal;

    @Override
    public void onMessage(MessageExt messageExt) {

        this.messageExtThreadLocal = new ThreadLocal<>();
        this.messageExtThreadLocal.set(messageExt);

        RocketMQMessageListener anno = this.getClass().getAnnotation(RocketMQMessageListener.class);
        MsgDto<T> msgDto;
        try {
            msgDto = JsonUtil.toBean(new String(messageExt.getBody(), StandardCharsets.UTF_8),
                    new TypeReference<MsgDto<T>>(((ParameterizedTypeImpl) getClass().getGenericSuperclass()).getActualTypeArguments()) {
                    });
            String tag = anno.selectorExpression();
            //除非通配，否则要校验tag
            if (!tag.equals("*") && !Objects.equals(tag, msgDto.getTags())) {
                logger.info("消息tag校验不通过,message={}", new String(messageExt.getBody(), StandardCharsets.UTF_8));
                return;
            }
        } catch (Exception ex) {
            logger.info("对消息进行tag校验时，出现异常,message={},anno={}", new String(messageExt.getBody(), StandardCharsets.UTF_8), anno, ex);
            return;
        }

        try {
            validateJsonParam(msgDto.getJsonParam());
        } catch (Exception ex) {
            logger.error("JsonParam数据校验失败,msg={}", new String(messageExt.getBody()), ex);
            return;
        }

        try {
            consumeMessage(msgDto.getJsonParam());
        } catch (Exception ex) {
            logger.info("使用获取到数据，进行业务处理时出现异常", ex);
            if (!(ex instanceof BizException) || ((BizException) ex).isMqRetry()) {
                throw ex;
            }
        }
    }

        /**
         * 进消息数据进行校验
         *
         * @param jsonParam
         * @return
         */
            public abstract void validateJsonParam(T jsonParam);

        /**
         * 进行业务处理
         *
         * @param jsonParam
         */
        public abstract void consumeMessage(T jsonParam);

    @Override
    public void prepareStart(DefaultMQPushConsumer consumer) {
        consumer.setConsumeThreadMax(64);
        consumer.setConsumeThreadMin(20);
    }
}
