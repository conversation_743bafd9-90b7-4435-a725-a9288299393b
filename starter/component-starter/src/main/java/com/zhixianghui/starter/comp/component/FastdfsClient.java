package com.zhixianghui.starter.comp.component;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.csource.common.NameValuePair;
import com.zhixianghui.csource.fastdfs.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * Author: Cmf
 * Date: 2019/11/13
 * Time: 19:27
 * Description: fastdfs文件客户端，请使用该类对fastdfs进行操作
 */
public class FastdfsClient {
    private static final String ORIGINAL_FILE_NAME = "ORIGINAL_FILE_NAME";
    private Logger logger = LoggerFactory.getLogger(FastdfsClient.class);

    public String uploadFile(String file, String originalFileName) {
        StorageClient1 storageClient1 = null;
        try {
            storageClient1 = getStorageClient();
            return storageClient1.upload_file1(file, getFileExtension(originalFileName), new NameValuePair[]{new NameValuePair(ORIGINAL_FILE_NAME, originalFileName)});
        } catch (Exception e) {
            logger.info("上传fastdfs失败,file={},originalFileName={}", file, originalFileName, e);
            throw CommonExceptions.BIZ_INVALID.newWith("上传FASTDFS 失败", e);
        } finally {
            try {
                if (storageClient1 != null) {
                    storageClient1.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    public String uploadFile(byte[] fileBytes, String originalFileName) {
        StorageClient1 storageClient1 = null;
        try {
            storageClient1 = getStorageClient();
            return storageClient1.upload_file1(fileBytes, getFileExtension(originalFileName), new NameValuePair[]{new NameValuePair(ORIGINAL_FILE_NAME, originalFileName)});
        } catch (Exception e) {
            logger.error("上传fastdfs失败, originalFileName={}", originalFileName, e);
            throw CommonExceptions.BIZ_INVALID.newWith("上传FASTDFS 失败", e);
        } finally {
            try {
                if (storageClient1 != null) {
                    storageClient1.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    /**
     * 获取accesssString,附加到url后面,此方面的返回结果为token=*****&ts=*****
     *
     * @param fastdfsFile fastdfs文件id
     * @return
     */
    public String genAccessString(String fastdfsFile) {
        try {
            long epochSecond = Instant.now().getEpochSecond();
            String token = ProtoCommon.getToken(fastdfsFile.substring(fastdfsFile.indexOf("/") + 1), (int) epochSecond, ClientGlobal.getG_secret_key());
            return "token=" + token + "&ts=" + epochSecond;
        } catch (Exception ex) {
            logger.error("生成token失败,fastdfsfile={}", fastdfsFile, ex);
            throw CommonExceptions.BIZ_INVALID.newWith("生成token失败", ex);
        }
    }

    /**
     * 获取源文件名
     *
     * @param fastdfsFile fastdfs文件id
     * @return 源文件名
     */
    public String getOriginalFileName(String fastdfsFile) {
        StorageClient1 storageClient1 = null;
        try {
            storageClient1 = getStorageClient();
            NameValuePair[] metadata1 = storageClient1.get_metadata1(fastdfsFile);
            if (metadata1 == null) {
                return null;
            }
            Optional<NameValuePair> first = Stream.of(metadata1).filter(p -> Objects.equals(p.getName(), ORIGINAL_FILE_NAME)).findFirst();
            return first.map(NameValuePair::getValue).orElse(null);
        } catch (Exception e) {
            logger.error("获取源文件名失败,fastdfsFile={}", fastdfsFile, e);
            throw CommonExceptions.BIZ_INVALID.newWith("获取源文件名失败", e);
        } finally {
            try {
                if (storageClient1 != null) {
                    storageClient1.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    /**
     * 根据组名和远程文件名来删除一个文件
     * @param fileName
     *            例如"M00/00/00/wKgxgk5HbLvfP86RAAAAChd9X1Y736.jpg"
     * @return 0为成功，非0为失败，具体为错误代码
     */
    public int deleteFile(String fileName) {
        StorageClient1 storageClient1 = null;
        try {
            storageClient1 = getStorageClient();
            return storageClient1.delete_file1(fileName);
        }  catch (Exception e) {
            logger.error("删除文件失败,fastdfsFile={}", fileName, e);
            throw CommonExceptions.BIZ_INVALID.newWith("删除文件失败", e);
        } finally {
            try {
                if (storageClient1 != null) {
                    storageClient1.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    private StorageClient1 getStorageClient() throws IOException {
        TrackerClient trackerClient = new TrackerClient();
        TrackerServer trackerServer = null;
        try {
            trackerServer = trackerClient.getConnection();
            StorageServer storageServer = trackerClient.getStoreStorage(trackerServer);
            return new StorageClient1(trackerServer, storageServer);
        } finally {
            try {
                if (trackerServer != null) {
                    trackerServer.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.trim().equals("")) {
            return "";
        }
        int i = fileName.lastIndexOf(".");
        if (i > 0) {
            return fileName.substring(i + 1);
        } else {
            return "";
        }
    }

    /**
     * 文件下载
     *
     * @param fileId 文件id
     * @return 返回一个流
     */
    public InputStream downloadFile(String fileId) {
        StorageClient1 storageClient1 = null;
        try {
            storageClient1 = getStorageClient();
            byte[] bytes = storageClient1.download_file1(fileId);
            return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            logger.error("获取文件失败, fastdfs fileId={}", fileId, e);
            return null;
        }finally {
            try {
                if (storageClient1 != null) {
                    storageClient1.close();
                }
            } catch (Exception ignored) {
            }
        }
    }

}
