package com.zhixianghui.starter.comp.component;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.QpsExceptions;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * qps限流器
 * <p>
 * 说明：
 * 1、一个jvm进程需确保只有一个当前类的实例
 * 2、一个jvm进程中请勿和@SentinelResource混合使用
 * 3、一个jvm进程中本类勿和动态数据源共用
 * 4、有前面几点限制的原因是 FlowRuleManager.currentProperty是一个静态变量，且根据FlowRuleManager的api设计来看，作者是希望
 * currentProperty属性的值只允许全部替换，而不允许往里面追加或部分更新，所以，如果同一个jvm里面有多个地方调用了 FlowRuleManager.loadRules()
 * 或者 FlowRuleManager.register2Property()，其currentProperty属性的值就会被替换掉，所以就会导致有些配置被覆盖掉了
 */
public class QpsLimiter {
    private Map<String, Integer> resourceMap;

    public QpsLimiter(Map<String, Integer> resourceMap) {
        this.resourceMap = resourceMap;
        initFlowRule();
    }

    /**
     * @param resource
     * @param param
     * @param function
     * @param <T>
     * @param <R>
     * @return
     * @see #execute(String, Function, Object, Consumer)
     */
    public <T, R> R execute(String resource, Function<T, R> function, T param) throws BizException {
        return execute(resource, function, param, null);
    }

    /**
     * 包装限流操作
     *
     * @param resource    需要进行降级处理的资源名称
     * @param bizExecutor 业务逻辑执行器，没有被降级的话将由此参数执行业务逻辑
     * @param param       执行业务逻辑需要的参数
     * @param errExecutor 异常执行器，发生异常时将调用此接口处理，此处的异常包含降级异常和其他异常 @see QpsException
     * @param <T>
     * @param <R>
     * @return
     */
    public <T, R> R execute(String resource, Function<T, R> bizExecutor, T param, Consumer<BizException> errExecutor) throws BizException {
        if (!resourceMap.containsKey(resource)) {
            throw new RuntimeException("resource:" + resource + "未执行初始化加载！");
        }

        Entry entry = null;
        R result = null;
        BizException qpsEx = null;
        try {
            entry = SphU.entry(resource);
            result = bizExecutor.apply(param);
        } catch (BlockException e) {
            qpsEx = QpsExceptions.QPS_NEED_BLOCK_EX.newWith("", e);
        } catch (Throwable e) {
            qpsEx = QpsExceptions.QPS_NO_NEED_BLOCK_EX.newWith("", e);
        } finally {
            if (entry != null) {
                entry.exit();
            }
        }

        if (qpsEx != null) {
            if (errExecutor != null) {
                errExecutor.accept(qpsEx);
            }
            throw qpsEx;
        }

        return result;
    }

    private void initFlowRule() {
        List<FlowRule> rules = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : resourceMap.entrySet()) {
            FlowRule rule = new FlowRule();
            rule.setResource(entry.getKey());
            rule.setCount(entry.getValue());
            rule.setGrade(RuleConstant.FLOW_GRADE_QPS);
            rules.add(rule);
        }
        FlowRuleManager.loadRules(rules);
    }
}
