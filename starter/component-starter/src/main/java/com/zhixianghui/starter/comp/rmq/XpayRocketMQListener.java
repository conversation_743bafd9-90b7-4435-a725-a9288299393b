//package com.zhixianghui.starter.comp.rmq;
//
//import com.alibaba.fastjson.TypeReference;
//import com.zhixianghui.common.statics.dto.rmq.MsgDto;
//import com.zhixianghui.common.statics.exception.BizException;
//import com.zhixianghui.common.util.utils.JsonUtil;
//import org.apache.rocketmq.common.message.MessageExt;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.apache.rocketmq.spring.core.RocketMQListener;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;
//
//import java.nio.charset.StandardCharsets;
//import java.util.Objects;
//
///**
// * Author: Cmf
// * Date: 2020.4.25
// * Time: 1:07
// * Description:易账管家定义的所有RocketMqListener继承此抽象类
// */
//public abstract class XpayRocketMQListener<T> implements RocketMQListener<MessageExt> {
//    protected Logger logger = LoggerFactory.getLogger(this.getClass());
//
//    @Override
//    public void onMessage(MessageExt messageExt) {
//        RocketMQMessageListener anno = this.getClass().getAnnotation(RocketMQMessageListener.class);
//        MsgDto<T> msgDto;
//        try {
//            msgDto = JsonUtil.toBean(new String(messageExt.getBody(), StandardCharsets.UTF_8),
//                    new TypeReference<MsgDto<T>>(((ParameterizedTypeImpl) getClass().getGenericSuperclass()).getActualTypeArguments()) {
//                    });
//            String tag = anno.selectorExpression();
//            if (!Objects.equals(tag, msgDto.getTags())) {
//                logger.info("消息tag校验不通过,message={}", new String(messageExt.getBody(), StandardCharsets.UTF_8));
//                return;
//            }
//        } catch (Exception ex) {
//            logger.info("对消息进行tag校验时，出现异常,message={},anno={}", new String(messageExt.getBody(), StandardCharsets.UTF_8), anno, ex);
//            return;
//        }
//
//        try {
//            validateJsonParam(msgDto.getJsonParam());
//        } catch (Exception ex) {
//            logger.error("JsonParam数据校验失败,msg={}", new String(messageExt.getBody()), ex);
//            return;
//        }
//
//        try {
//            consumeMessage(msgDto.getJsonParam());
//        } catch (Exception ex) {
//            logger.info("使用获取到数据，进行业务处理时出现异常", ex);
//            if (!(ex instanceof BizException) || ((BizException) ex).isMqRetry()) {
//                throw ex;
//            }
//        }
//    }
//
//    /**
//     * 进消息数据进行校验
//     *
//     * @param jsonParam
//     * @return
//     */
//    public abstract void validateJsonParam(T jsonParam);
//
//    /**
//     * 进行业务处理
//     *
//     * @param jsonParam
//     */
//    public abstract void consumeMessage(T jsonParam);
//
//}
