package com.zhixianghui.web.notify.controller.cmb;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.web.notify.enums.CmbReceiveEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.bouncycastle.asn1.ASN1EncodableVector;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithID;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECPoint;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.Map;

@RestController
@Slf4j
public class CmbCallbackController {

    @Value("${cmb.key}")
    private String key;

    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private RobotFacade robotFacade;

    @PostMapping("cmb/callbck")
    public String payCallback(@RequestBody Map<String,Object> param) {
        log.info("招行通知回调，回调参数：[{}]",JsonUtil.toStringPretty(param));
        if (!verify(param)){
            log.error("招行回调，验签失败,回调参数：[{}]",JsonUtil.toString(param));
            return "fail";
        }

        String type = (String) param.get("nottyp");

        JSONObject notdat = JSONUtil.parseObj(param.get("notdat"));
        CmbReceiveEnum cmbReceiveEnum = CmbReceiveEnum.getEnum(type);
        if (cmbReceiveEnum == null || StringUtils.isBlank(cmbReceiveEnum.getTag())){
            log.error("招行回调，通知类型不匹配，通知key：[{}]",key);
            return "fail";
        }

        if (StringUtils.equals(type, CmbReceiveEnum.AGENCY_PAYMENT_NOTIFY.getType())) {
            log.info("回调信息:notdat={}", notdat.toStringPretty());
            if (StringUtils.equals(notdat.getStr("msgtyp"), "FIN")) {
                final JSONObject agcInfo = notdat.getJSONObject("msgdat").getJSONObject("agcInfo");
                log.info("回调信息:agcInfo={}", agcInfo.toStringPretty());
                if (StringUtils.equals(agcInfo.getStr("rtnflg"), "S") || StringUtils.equals(agcInfo.getStr("rtnflg"), "F")||StringUtils.equals(agcInfo.getStr("rtnflg"), "R")) {
                    log.info("发放状态为成功或者失败,{}", agcInfo.toStringPretty());
                } else {
                    log.info("发放状态不为成功或者失败,{}", agcInfo.toStringPretty());
                    return "fail";
                }
            }else {
                log.warn("{}-代发回调状态为 {},非成功状态，不做后续处理",notdat.getStr("yurref"),notdat.getStr("msgtyp"));
                return "success";
            }
        }

        if (StringUtils.equals(type, CmbReceiveEnum.PAY_NOTIFY.getType())) {

            if (StringUtils.equals(notdat.getStr("msgtyp"), "FINS")) {
                final JSONObject trsInfo = notdat.getJSONObject("msgdat").getJSONObject("trsInfo");
                if (StringUtils.equals(trsInfo.getStr("rtnFlg"), "S")
                        || StringUtils.equals(trsInfo.getStr("rtnFlg"), "F")
                        || StringUtils.equals(trsInfo.getStr("rtnFlg"), "R")
                        || StringUtils.equals(trsInfo.getStr("rtnFlg"), "C")) {

                }else {
                    return "fail";
                }
            }
            else if (StringUtils.equals(notdat.getStr("msgtyp"), "FINB")) {
                final JSONObject backInfo = notdat.getJSONObject("msgdat").getJSONObject("backInfo");
                String yurRef = backInfo.getStr("yurRef");
                String trsAmt = backInfo.getStr("trsAmt");
                String dbtNam = backInfo.getStr("dbtNam");
                String crtNam = backInfo.getStr("crtNam");
                String rtnNar = backInfo.getStr("rtnNar");

                StringBuilder stringBuilder = new StringBuilder("#### 招行转账支付退票通知");
                stringBuilder.append("\\n > 业务参考号:"+yurRef+"\n");
                stringBuilder.append("\\n > 金额:"+trsAmt+"\n");
                stringBuilder.append("\\n > 付方户名:"+dbtNam+"\n");
                stringBuilder.append("\\n > 收方户名:"+crtNam+"\n");
                stringBuilder.append("\\n > 退票原因:"+rtnNar+"\n");

                MarkDownMsg markDownMsg = new MarkDownMsg();
                markDownMsg.setContent(stringBuilder.toString());
                markDownMsg.setRobotType(RobotTypeEnum.TASK_NOTIFY_ROBOT.getType());
                markDownMsg.setUnikey(IdUtil.fastSimpleUUID());
                robotFacade.pushMarkDownAsync(markDownMsg);

                robotFacade.pushMarkDownAsync(markDownMsg);

                return "success";
            }else {
                return "success";
            }
        }

        log.info("招行回调-发送mq消息:{}", notdat.toStringPretty());
        String key = (String) param.get("notnbr");
        notifyFacade.sendOne(MessageMsgDest.TOPIC_CMB_NOTIFY,
                key,
                key,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),
                cmbReceiveEnum.getTag(),
                notdat.toString());
        return "success";
    }

//    public static void main(String[] args) {
//        String respStr = "{\"sigtim\":\"20230209105229\",\"sigdat\":\"TTrHDYGOG+yS1VaftwL8q77mEUT7hoUSzZPBfdHsM9X74K5LHfm/C+ZKH583HeTfUzQ2ouqnhJqtm92TBbH4NA==\",\"notdat\":\"{\\\"msgdat\\\":{\\\"chknbr\\\":\\\" \\\",\\\"infflg\\\":\\\"2\\\",\\\"refsub\\\":\\\" \\\",\\\"refnbr\\\":\\\"C0546IG0006AL3Z\\\",\\\"rpyacc\\\":\\\"6214832071039189\\\",\\\"trscod\\\":\\\"CPUA\\\",\\\"gsbacc\\\":\\\" \\\",\\\"otrnar\\\":\\\" \\\",\\\"rpynam\\\":\\\"普海涛\\\",\\\"amtcdr\\\":\\\"C\\\",\\\"naryur\\\":\\\"测试资金\\\",\\\"vltdat\\\":\\\"20230209\\\",\\\"yurref\\\":\\\" \\\",\\\"accnam\\\":\\\"德兴市联友信息技术有限公司\\\",\\\"gsbnam\\\":\\\" \\\",\\\"narext\\\":\\\" \\\",\\\"trsanl\\\":\\\" \\\",\\\"nusage\\\":\\\" \\\",\\\"trsdat\\\":\\\"20230209\\\",\\\"reqnbr\\\":\\\" \\\",\\\"trstim\\\":\\\"105229\\\",\\\"rpybnk\\\":\\\"招商银行\\\",\\\"gsbbbk\\\":\\\" \\\",\\\"frmcod\\\":\\\"00000136\\\",\\\"athflg\\\":\\\"N\\\",\\\"rpybbn\\\":\\\" \\\",\\\"rsvflg\\\":\\\"N\\\",\\\"accnbr\\\":\\\"121946347610606\\\",\\\"busnam\\\":\\\" \\\",\\\"rpybbk\\\":\\\" \\\",\\\"c_trsamt\\\":\\\"1\\\",\\\"c_ccynbr\\\":\\\"人民币\\\",\\\"busnar\\\":\\\" \\\",\\\"blvamt\\\":\\\"2.5\\\",\\\"rpyadr\\\":\\\" \\\"},\\\"msgtyp\\\":\\\"NCCRTTRS\\\"}\",\"notkey\":\"121946347610606\",\"notnbr\":\"142766321389142016\",\"nottyp\":\"YQN01010\"}";
//        Map<String,Object> jsonParam = JsonUtil.toBean(respStr,new TypeReference<HashMap<String,Object>>(){});
//        verify(jsonParam);
//    }

    private boolean verify(Map<String, Object> param) {
        String resSign = (String) param.get("sigdat");
        param.put("sigdat","__signature_sigdat__");
        String signStr = JsonUtil.toString(param);
        byte[] USERID = "1234567812345678".getBytes();
        try {
            byte[] pubKeys = Base64.decodeBase64(key);
            byte[] byteBuffer = signStr.getBytes("UTF-8");
            byte[] signature = Base64.decodeBase64(resSign);
            ECPublicKeyParameters publicKey = encodePublicKey(pubKeys);
            SM2Signer signer = new SM2Signer();
            ParametersWithID parameters = new ParametersWithID(publicKey, USERID);
            signer.init(false, parameters);
            signer.update(byteBuffer, 0, byteBuffer.length);
            if (!signer.verifySignature(encodeDERSignature(signature))) {
                throw new Exception("请求签名校验不通过");
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    private static byte[] encodeDERSignature(byte[] signature) throws Exception {
        byte[] r = new byte[32];
        byte[] s = new byte[32];
        System.arraycopy(signature, 0, r, 0, 32);
        System.arraycopy(signature, 32, s, 0, 32);
        ASN1EncodableVector vector = new ASN1EncodableVector();
        vector.add(new ASN1Integer(new BigInteger(1, r)));
        vector.add(new ASN1Integer(new BigInteger(1, s)));

        try {
            return (new DERSequence(vector)).getEncoded();
        } catch (Exception e) {
            throw new Exception("签名数据不正常");
        }
    }

    private static ECPublicKeyParameters encodePublicKey(byte[] value) {
        byte[] x = new byte[32];
        byte[] y = new byte[32];
        System.arraycopy(value, 1, x, 0, 32);
        System.arraycopy(value, 33, y, 0, 32);
        BigInteger iX = new BigInteger(1, x);
        BigInteger iY = new BigInteger(1, y);
        ECPoint ecQ = getSM2Curve().createPoint(iX, iY);
        return new ECPublicKeyParameters(ecQ, getECDomainParameters());
    }

    private static ECCurve getSM2Curve() {
        ECParameterSpec spec = ECNamedCurveTable.getParameterSpec("sm2p256v1");
        return spec.getCurve();
    }
    private static ECDomainParameters getECDomainParameters() {
        ECParameterSpec spec = ECNamedCurveTable.getParameterSpec("sm2p256v1");
        return new ECDomainParameters(spec.getCurve(), spec.getG(), spec.getN(), spec.getH(), spec.getSeed());
    }


}
