package com.zhixianghui.web.notify.controller.pay;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.web.notify.vo.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年01月04日 10:20:00
 */
@RestController
@RequestMapping("/withdraw")
@Slf4j
public class WithdrawController {
    @Reference
    private NotifyFacade notifyFacade;

    @RequestMapping("/bank/callback")
    public String bankCallback(@RequestBody Map<String, Object> map) {
        String dataStr = JsonUtil.toString(map.get("data"));
        Map<String,Object> data=JsonUtil.toBean(dataStr,Map.class);
        log.info("[{}]==>银行卡提现回调", data.get("mch_order_no"));

        notifyFacade.sendOne(MessageMsgDest.TOPIC_JOINPAY_WITHDRAW, UUIDUitl.generateString(10), String.valueOf(data.get("mch_order_no")),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_JOINPAY_WITHDRAW, JsonUtil.toString(map), MsgDelayLevelEnum.S_1.getValue());
        return JSONObject.toJSONString(new ResponseVo());
    }


}