package com.zhixianghui.web.notify.vo.sign;

import lombok.Data;

/**
 * <AUTHOR>
 * @description e签宝流程结束回调Vo
 * @date 2021-01-13 15:23
 **/
@Data
public class EsignFlowEndVoV3 {

    /**
     * 标记该通知的业务类型，该通知固定为：SIGN_FLOW_FINISH
     */
    private String action;

    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 流程id
     */
    private String signFlowId;

    /**
     * 签署流程标题
     */
    private String signFlowTitle;
    /**
     * 签署流程最终状态
     * 2 - 已完成（所有签署方完成签署）
     * 3 - 已撤销（发起方撤销签署任务）
     * 5 - 已过期（签署截止日到期后触发）
     * 7 - 已拒签（签署方拒绝签署）
     */
    private String signFlowStatus;

    /**
     * 当流程异常结束时，附加终止原因描述
     */
    private String statusDescription;

    /**
     * 签署流程创建时间（Unix时间戳格式，单位：毫秒）
     */
    private Long signFlowCreateTime;

    /**
     * 签署流程开启时间（Unix时间戳格式，单位：毫秒）
     */
    private Long signFlowStartTime;
    /**
     * 签署流程完结时间（Unix时间戳格式，单位：毫秒）
     */
    private Long signFlowFinishTime;

}
