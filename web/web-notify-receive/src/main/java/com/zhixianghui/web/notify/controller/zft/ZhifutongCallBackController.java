package com.zhixianghui.web.notify.controller.zft;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.pay.outlink.entity.PayApplyRecord;
import com.zhixianghui.facade.pay.outlink.service.PayFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@Slf4j
public class ZhifutongCallBackController {

    @Reference
    private PayFacade payFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @PostMapping("zft/PayCallback")
    public String payCallback(HttpServletRequest request) {
        Map<String, String> param = getParameterMap(request);

        log.info("接收到支付宝直付通回调");
        log.info("回调参数：{}", JSON.toJSONString(param));

        if (param != null) {
            log.info(JSONUtil.toJsonPrettyStr(param));
        }
        boolean success = payFacade.verifyNotify(param);
        if (!success) {
            log.error("验签不通过");
            return SuccessFailEnum.FAIL.getCode();
        }
        JSONObject backParamJson = JSONUtil.parseObj(param);
        PayApplyRecord applyRecord = payFacade.getByPaytrxNo(backParamJson.getStr("out_trade_no"));
        if (applyRecord == null) {
            return SuccessFailEnum.SUCCESS.getCode();
        }
        notifyFacade.sendOne(MessageMsgDest.TOPIC_ZFT_PAY_RESULT_CALLBACK,
                applyRecord.getMerchantNo(), applyRecord.getPayTrxNo(),
                NotifyTypeEnum.TRADE_ZFT_PAY_RESULT_NOTIFY.getValue(),
                MessageMsgDest.TAG_ZFT_PAY_RESULT_CALLBACK, backParamJson.toString()
        );
        return SuccessFailEnum.SUCCESS.getCode();
    }
    private static Map getParameterMap(HttpServletRequest request) {
        // 参数Map
        Map<String,String[]> properties = request.getParameterMap();
        // 返回值Map
        Map<String,String> returnMap = new HashMap();

        for (Map.Entry<String,String[]> entry : properties.entrySet()) {
            String name = entry.getKey();
            String[]  values = entry.getValue();
            String value = "";

            for(int i=0;i<values.length;i++){
                value = values[i] + ",";
            }
            value = value.substring(0, value.length()-1);

            returnMap.put(name, value);
        }
        return returnMap;
    }
}
