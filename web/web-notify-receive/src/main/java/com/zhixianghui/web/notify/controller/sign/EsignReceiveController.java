package com.zhixianghui.web.notify.controller.sign;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.vo.sign.SignReceiveRespVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.web.notify.utils.esign.SafeVerify;
import com.zhixianghui.web.notify.vo.sign.EsignFlowEndVo;
import com.zhixianghui.web.notify.vo.sign.EsignFlowEndVoV3;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;


/**
 * <AUTHOR>
 * @description e签宝回调
 * @date 2021-01-13 15:16
 **/
@RestController
@Slf4j
@RequestMapping("/esignReceive")
public class EsignReceiveController {
    @Value("${sign.esignProjectSecret}")
    private String projectSecret;

    @Reference
    private NotifyFacade notifyFacade;

    @GetMapping("finishTemplate")
    public void finishTemplate(HttpServletRequest request, HttpServletResponse response){
        try {
            response.getWriter().write("<html>\n" +
                    "  <script>\n" +
                    "    if (navigator.userAgent.indexOf(\"MSIE\") > 0) {\n" +
                    "      if (navigator.userAgent.indexOf(\"MSIE 6.0\") > 0) {\n" +
                    "        window.opener = null;\n" +
                    "        window.close();\n" +
                    "      } else {\n" +
                    "        window.open('', '_top');\n" +
                    "        window.top.close();\n" +
                    "      }\n" +
                    "    } else if (navigator.userAgent.indexOf(\"Firefox\") > 0) { //火狐浏览器\n" +
                    "        window.location.href = 'about:blank ';\n" +
                    "    } else {//谷歌等其他浏览器\n" +
                    "      window.opener = null;\n" +
                    "      window.open('', '_self', '');\n" +
                    "      window.close();\n" +
                    "    }\n" +
                    "  </script>\n" +
                    "</html>");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            try {
                response.getWriter().close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @PostMapping("flowEnd")
    public String testFlowEnd(@RequestBody EsignFlowEndVoV3 esignFlowEndVo, HttpServletRequest request){
        log.info("收到[e签宝流程结束]的回调:{}", JsonUtil.toString(esignFlowEndVo));
        if("SIGN_FLOW_COMPLETE".equals(esignFlowEndVo.getAction())){
            boolean isPass = new SafeVerify().checkPass(request,projectSecret);
            if (isPass){
                log.info("收到[e签宝流程结束]的回调==> 校验成功");
                SignReceiveRespVo respVo = new SignReceiveRespVo();
                respVo.setFlowId(esignFlowEndVo.getSignFlowId());
                respVo.setBusinessScence(esignFlowEndVo.getSignFlowTitle());
                respVo.setFlowStatus(Integer.valueOf(esignFlowEndVo.getSignFlowStatus()));
                //通知 更新记录文件下载保存 异步处理 回调商户
                notifyFacade.sendOne(MessageMsgDest.TOPIC_SIGN_ASYNC, UUIDUitl.generateString(10),esignFlowEndVo.getSignFlowId(),
                        NotifyTypeEnum.SIGN_NOTIFY.getValue(),MessageMsgDest.TAG_SIGN_RECEIVE, JSON.toJSONString(respVo));
            }else{
                log.info("收到[e签宝流程结束]的回调==> 校验失败");
            }
        }
        return "success";
    }

    @PostMapping("online/flowEnd")
    public String onlineFlowEnd(@RequestBody Map<String,Object> paramMap, HttpServletRequest request){
        String json = JsonUtil.toString(paramMap);
        log.info("收到[e签宝在线签约流程结束]的回调:{}", json);
        //回调通知
        String flowId = (String) paramMap.get("signFlowId");
        notifyFacade.sendOne(MessageMsgDest.TOPIC_SIGN_NOTIFY, UUIDUitl.generateString(10),flowId,
                NotifyTypeEnum.SIGN_NOTIFY.getValue(),MessageMsgDest.TAG_SIGN_NOTIFY, json);
        return "success";
    }
}
