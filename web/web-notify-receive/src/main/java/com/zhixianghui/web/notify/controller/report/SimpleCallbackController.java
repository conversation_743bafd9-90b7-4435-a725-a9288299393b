package com.zhixianghui.web.notify.controller.report;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @ClassName SimpleCallbackController
 * @Description TODO
 * @Date 2023/9/26 16:00
 */
@Slf4j
@RestController
@RequestMapping("/reportReceive")
public class SimpleCallbackController {

    @PostMapping("/simpleCallback")
    private String simpleCallback(HttpServletRequest request, @RequestBody String str){
        log.info("回调内容 : {}", str);
        return "success";
    }
}
