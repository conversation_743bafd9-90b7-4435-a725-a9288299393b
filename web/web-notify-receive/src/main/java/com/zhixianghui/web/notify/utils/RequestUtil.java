package com.zhixianghui.web.notify.utils;

import com.zhixianghui.web.notify.web.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class RequestUtil {

    public static String getRequestBody(HttpServletRequest request){
        if(request instanceof ContentCachingRequestWrapper){
            //解决Request Body只能读一次的问题
            return ((ContentCachingRequestWrapper) request).getRequestBody();
        }else{
            //如果直接从这里读取，会存在Request Body只能读一次的问题
            return readBody(request);
        }
    }

    public static String readBody(HttpServletRequest request){
        // 请求内容RequestBody
        String reqBody = null;
        int contentLength = request.getContentLength();
        if (contentLength < 0) {
            return null;
        }
        byte buffer[] = new byte[contentLength];
        try {
            for (int i = 0; i < contentLength;) {
                int readlen = request.getInputStream().read(buffer, i, contentLength - i);
                if (readlen == -1) {
                    break;
                }
                i += readlen;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        reqBody = new String(buffer, StandardCharsets.UTF_8);
        return reqBody;
    }
}
