package com.zhixianghui.web.notify.controller.huiju;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/MonitorNotify")
public class MonitorNotify {

    @Reference
    private RobotFacade robotFacade;

    @PostMapping("nofify")
    public RestResult<String> notify(@RequestBody MarkDownMsg markDownMsg) {
        robotFacade.pushMarkDownAsync(markDownMsg);
        return RestResult.success("ok");
    }
}