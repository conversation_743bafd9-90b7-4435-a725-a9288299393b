package com.zhixianghui.web.notify.enums;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName CmbReceiveEnum
 * @Description TODO
 * @Date 2023/2/10 10:56
 */
@Getter
@AllArgsConstructor
public enum CmbReceiveEnum {

    MAINTENANCE_NOTIFY("维护通知","YQSYSTEM",MessageMsgDest.TAG_CMB_MAINTENANCE),

    ACCOUNT_CHANGE_NOTIFY("账务变动通知","YQN01010",MessageMsgDest.TAG_CMB_ACCOUNT_CHANGE),

    RECEIVE_BILL_NOTIFY("回单结果通知","YQF01010",null),

    PAY_NOTIFY("支付结果通知","YQN02030",MessageMsgDest.TAG_CMB_PAY2B_RESULT),

    AGENCY_PAYMENT_NOTIFY("代发结果通知","YQN03010",MessageMsgDest.TAG_CMB_REMIT_RESULT),

    RECONCILIATIONS_BILL_NOTIFY("代发明细对账单结果通知","YQF03010",null),

    WITHHOLD_NOTIFY("代扣结果通知","YQN03030",null),

    REMIT_NOTIFY("汇入汇款通知","YQN07110",null);

    private String desc;

    private String type;

    private String tag;

    public static CmbReceiveEnum getEnum(String type) {
        return Arrays.stream(values()).filter(p -> p.type.equals(type)).findFirst().orElse(null);
    }
}
