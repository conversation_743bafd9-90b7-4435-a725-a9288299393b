package com.zhixianghui.web.notify.utils;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName WxPayConfig
 * @Description TODO
 * @Date 2021/12/1 10:10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wxpay")
public class WxPayConfig {

    //微信商户号
    private String merchantId;

    //api证书序列号
    private String serialNo;

    //微信商户api私钥
    private String privateKey;

    //apiv3密钥
    private String apiv3Key;

}
