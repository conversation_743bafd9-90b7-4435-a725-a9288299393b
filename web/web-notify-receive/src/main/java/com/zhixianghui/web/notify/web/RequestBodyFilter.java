package com.zhixianghui.web.pms.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @description 支持POST请求 获取get类的query参数
 * @date 2020-08-19 14:39
 **/
//@Component
public class RequestBodyFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        if(StringUtils.isNotEmpty(request.getContentType())&&request.getContentType().contains("application/json")){
            RequestBodyWrapper requestWrapper = new RequestBodyWrapper((HttpServletRequest) request);
            chain.doFilter(requestWrapper, response);
        }else {
            chain.doFilter(request, response);
        }
    }

    /**
     * 静态内部类，解析json内容
     */
    private static class RequestBodyWrapper extends HttpServletRequestWrapper {

        private final JSONObject jsonObject;

        /**
         * Constructs a request object wrapping the given request.
         *
         * @param request The request to wrap
         * @throws IllegalArgumentException if the request is null
         */
        public RequestBodyWrapper(HttpServletRequest request) throws IOException {
            super(request);
            byte[] body = IOUtils.toByteArray(new InputStreamReader(request.getInputStream()), "UTF-8");
            if(ArrayUtils.isEmpty(body)){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("请求体不能为空");
            }
            jsonObject = JSON.parseObject(IOUtils.toString(body, "UTF-8"));
        }

        @Override
        public ServletInputStream getInputStream(){
            final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(jsonObject.toJSONString().getBytes());
            return new ServletInputStream() {
                @Override
                public boolean isFinished() {
                    return false;
                }

                @Override
                public boolean isReady() {
                    return false;
                }

                @Override
                public void setReadListener(ReadListener listener) {

                }

                @Override
                public int read() throws IOException {
                    return byteArrayInputStream.read();
                }
            };
        }

        @Override
        public String getParameter(String name) {
            String parameter = super.getParameter(name);
            if (StringUtils.isEmpty(parameter)){
                return jsonObject.toJSONString();
            }
            return parameter;
        }

        @Override
        public String[] getParameterValues(String name) {
            String[] arr = new String[1];
            String parameter = jsonObject.getString(name);
            if(StringUtils.isNotEmpty(parameter)){
                arr[0] = parameter;
                return arr;
            }else{
                return super.getParameterValues(name);
            }
        }
    }
}
