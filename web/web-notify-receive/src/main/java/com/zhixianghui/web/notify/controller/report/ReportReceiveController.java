package com.zhixianghui.web.notify.controller.report;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.banklink.service.report.ChannelReportFacade;
import com.zhixianghui.facade.banklink.vo.report.ReportReceiveReqVo;
import com.zhixianghui.facade.banklink.vo.report.ReportReceiveRespVo;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 报备回调
 * @date 2020-11-02 11:33
 **/
@RestController
@Slf4j
@RequestMapping("/reportReceive")
public class ReportReceiveController {

    @Reference
    private ChannelReportFacade channelReportFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private SequenceFacade sequenceFacade;

    /**
     * 汇聚报备回调接收
     * @param respBody 回调信息
     * @return 指定参数
     */
    @PostMapping("joinpayNotify")
    public String joinpayNotify(@RequestBody JSONObject respBody){
        log.info("回调接口请求参数:{}",JSON.toJSONString(respBody));
        ReportReceiveRespVo respVo = validateResp(respBody);
        //发消息给报备服务，更新信息
        notifyFacade.sendOne(MessageMsgDest.TOPIC_REPORT_ASYNC, NotifyTypeEnum.REPORT_RECEIVE.getValue(),MessageMsgDest.TAG_REPORT_NOTIFY_ASYNC,JSON.toJSONString(respVo));

        Map<String,String> response = Maps.newHashMap();
        response.put("resp_code", "A1000");
        response.put("resp_msg", "success");
        return JSON.toJSONString(response);
    }

    @PostMapping("modifyNotify")
    public String modifyNotify(@RequestBody JSONObject respBody){
        log.info("回调接口请求参数:{}",JSON.toJSONString(respBody));
        //{"aes_key":"","data":"{\"biz_code\":\"B100000\",\"biz_msg\":\"受理成功\",\"alt_mch_no\":\"333117700008901\",\"auth_status\":\"R4004\"}","mch_no":"888116000006821","rand_str":"218F24AEEA00C41210DFED7BBC0A2028","resp_code":"A1000","resp_msg":"受理成功","sign":"CA0ECF817B1409A4FC907E297F460515","sign_type":"1"}
        ReportReceiveRespVo respVo = validateResp(respBody);
        Map<String,String> response = Maps.newHashMap();
        response.put("resp_code", "A1000");
        response.put("resp_msg", "success");
        return JSON.toJSONString(response);
    }

    private ReportReceiveRespVo validateResp(JSONObject respBody) {
        String channelMchNo = respBody.getString("mch_no");
        ReportReceiveReqVo reqVo = new ReportReceiveReqVo();
        reqVo.setRespContent(respBody);
        reqVo.setChannelNo(ChannelNoEnum.JOINPAY.name());
        reqVo.setChannelMchNo(channelMchNo);
        ReportReceiveRespVo respVo = channelReportFacade.verifyAndHandleResult(reqVo);
        //发消息给报备服务，更新信息
        notifyFacade.sendOne(MessageMsgDest.TOPIC_REPORT_ASYNC,
                NotifyTypeEnum.REPORT_RECEIVE.getValue(),
                MessageMsgDest.TAG_REPORT_MODIFY_NOTIFY,
                JSON.toJSONString(respVo));
        return respVo;
    }

//    @RequestMapping("simpleCallback")
//    private String simpleCallback(@RequestBody Map<String,Object> map){
//        log.info("汇聚回调内容 : {}", JSONObject.toJSONString(map));
//        Map<String,String> response = Maps.newHashMap();
//        response.put("resp_code", "A1000");
//        response.put("resp_msg", "success");
//        return JSON.toJSONString(response);
//    }
}
