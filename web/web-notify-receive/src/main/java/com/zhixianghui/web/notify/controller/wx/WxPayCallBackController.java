package com.zhixianghui.web.notify.controller.wx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.web.notify.utils.WxPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.GeneralSecurityException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月20日 16:18:00
 */
@RestController
@RequestMapping
@Slf4j
public class WxPayCallBackController {

    @Autowired
    private WxPayUtil wxPayUtil;

    @Reference
    private NotifyFacade notifyFacade;


    @RequestMapping("/callback/wx")
    public String callBack(@RequestBody Map<String,Object> map) {
        log.info("微信异步回调参数，callBack：{}", JSONObject.toJSONString(map));
        return "微信回调成功";
    }

    @RequestMapping("/callback/wx/jsapi")
    public Map<String,Object> jsapiCallBack(HttpServletRequest request, HttpServletResponse response) throws IOException, GeneralSecurityException {
        ServletInputStream inputStream = request.getInputStream();
        StringBuffer sb = new StringBuffer();
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
        String s;
        while ((s = bufferedReader.readLine()) != null){
            sb.append(s);
        }
        String respBody = sb.toString();

        //微信验签
        if (!wxPayUtil.verify(request,respBody)){
            log.error("微信jsapi支付回调，验签失败，回调参数：[{}]",respBody);
            response.setStatus(500);
            Map<String,Object> responseMap = new HashMap<>();
            responseMap.put("code","FAIL");
            responseMap.put("message","验签失败");
            return responseMap;
        }

        //参数解密
        String decryptBody = wxPayUtil.decrypt(respBody);
        JSONObject jsonObject = JSONObject.parseObject(decryptBody);

        String eventType = jsonObject.getString("event_type");
        String outTradeNo = jsonObject.getString("out_trade_no");
        //支付成功回调
        if (eventType.equals("TRANSACTION.SUCCESS")){
            log.info("微信jsapi支付回调，订单号：[{}]，回调参数：[{}]",outTradeNo,decryptBody);
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_JSAPI_CALLBACK,
                    outTradeNo,
                    outTradeNo,
                    NotifyTypeEnum.TRADE_NOTIFY.getValue(),
                    MessageMsgDest.TAG_WX_JSAPI_CALLBACK,
                    decryptBody);
        }else if (eventType.equals("REFUND.SUCCESS")){
            String outRefundNo = jsonObject.getString("out_refund_no");
            log.info("微信jsapi退款回调，退款订单号：[{}]，原订单号：[{}],回调参数：[{}]",outRefundNo,outTradeNo,decryptBody);
            notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_JSAPI_REFUND,
                    outRefundNo,
                    outRefundNo,
                    NotifyTypeEnum.TRADE_NOTIFY.getValue(),
                    MessageMsgDest.TAG_WX_JSAPI_REFUND,
                    decryptBody);
        }else{
            log.error("微信jsapi支付回调，操作类型不匹配，回调参数：[{}]",respBody);
            response.setStatus(500);
            Map<String,Object> responseMap = new HashMap<>();
            responseMap.put("code","FAIL");
            return responseMap;
        }


        Map<String,Object> responseMap = new HashMap<>();
        responseMap.put("code","SUCCESS");
        responseMap.put("message","成功");
        return responseMap;
    }

}