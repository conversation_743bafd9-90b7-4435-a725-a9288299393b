package com.zhixianghui.web.notify.web;

import org.hibernate.validator.HibernateValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

/**
 * Author: Cmf
 * Date: 2019/10/9
 * Time: 17:20
 * Description:
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Autowired
    private AllowCrossFilter allowCrossFilter;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/statics/**").addResourceLocations("classpath:/statics/");

    }

    @Bean
    public FilterRegistrationBean<AllowCrossFilter> allowCrossFilterRegistrationBean() {
        FilterRegistrationBean<AllowCrossFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(allowCrossFilter);
        return registrationBean;
    }

    @Bean
    public Validator validator(){
        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .addProperty( "hibernate.validator.fail_fast", "true" )  // 快速失败模式
                .buildValidatorFactory();
        return validatorFactory.getValidator();
    }
}
