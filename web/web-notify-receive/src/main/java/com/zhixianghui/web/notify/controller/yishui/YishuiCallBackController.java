package com.zhixianghui.web.notify.controller.yishui;

import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@Slf4j
public class YishuiCallBackController {

    @Reference
    private NotifyFacade notifyFacade;

    @PostMapping("callback/yishui")
    public String paySuccessCallBack(HttpServletRequest request) {
        Map<String, String> params = getParameterMap(request);
        log.info("回调原始入参:{}", JSONUtil.toJsonPrettyStr(params));

        if (StringUtils.equals(String.valueOf(params.get("type")), "payment_order")) {
//            String signStr = String.valueOf(params.get("sign"));
//            params.remove("sign");
//            String s = MapUtil.sortJoin(params, "&", "=", true);
//            String sign = DigestUtil.md5Hex(s).toUpperCase();
//            if (!StringUtils.equals(sign, signStr)) {
//                return "fail";
//            }

            //发送消息
            String clientSn = params.get("client_sn");
            String requestNo =params.get("outer_trade_no");
            notifyFacade.sendOne(MessageMsgDest.TOPIC_YISHUI_CALL_BACK,
                    clientSn,
                    requestNo,
                    NotifyTypeEnum.YISHUI_CALLBACK_NOTIFY.getValue(),
                    MessageMsgDest.TAG_YISHUI_CALL_BACK,
                    JSONUtil.toJsonStr(params)
            );
        }

        return "success";
    }
    private static Map getParameterMap(HttpServletRequest request) {
        // 参数Map
        Map<String,String[]> properties = request.getParameterMap();
        // 返回值Map
        Map<String,String> returnMap = new HashMap();

        for (Map.Entry<String,String[]> entry : properties.entrySet()) {
            String name = entry.getKey();
            String[]  values = entry.getValue();
            String value = "";

            for(int i=0;i<values.length;i++){
                value = values[i] + ",";
            }
            value = value.substring(0, value.length()-1);

            returnMap.put(name, value);
        }
        return returnMap;
    }
}
