package com.zhixianghui.web.notify.controller.pay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.pay.PayBankFacade;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveReqVo;
import com.zhixianghui.facade.banklink.vo.pay.PayReceiveRespVo;
import com.zhixianghui.facade.banklink.vo.pay.SinglePayReceiveRespVo;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.common.vo.TraceVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.service.WithdrawRecordFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

/**
 * 汇聚付款回调
 *
 * <AUTHOR>
 * @date 2020/10/28
 **/
@RestController
@RequestMapping("/joinpayPayReceive")
@Slf4j
public class JoinpayPayReceiveController {
    private static final String SUCCESS_RESPONSE_CODE = "success";

    @Reference
    private PayBankFacade payBankFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private WithdrawRecordFacade withdrawRecordFacade;

    /**
     * 付款结果回调
     *
     * @param respBody
     * @return
     */
    @RequestMapping("/payReceive")
    public JSONObject payReceive(@RequestBody JSONObject respBody) {
        log.info("汇聚分账方代付回调结果原始报文：{}", JsonUtil.toString(respBody));
        String channelMchNo = respBody.getString("mch_no");
        respBody.put("data", JsonUtil.toString(respBody.get("data")));
        PayReceiveReqVo reqVo = new PayReceiveReqVo();
        reqVo.setRespContent(respBody);
        reqVo.setChannelNo(ChannelNoEnum.JOINPAY.name());
        reqVo.setChannelMchNo(channelMchNo);
        PayReceiveRespVo respVo = payBankFacade.verifyAndHandleResult(reqVo);
        log.info("汇聚分账方代付回调结果处理后：{}", JsonUtil.toString(respBody));

        // 发送mq消息，通知交易中心
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC,
                channelMchNo,
                respVo.getBankOrderNo(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),
                MessageMsgDest.TAG_GRANT_NOTIFY,
                JSON.toJSONString(respVo));


        JSONObject returnObj = new JSONObject();
        returnObj.put("resp_code", "A1000");
        returnObj.put("resp_msg", "受理成功");
        return returnObj;
    }

    /**
     * @param map 回调参数
     * @return
     */
    @RequestMapping("rechargeCallback")
    private String rechargeCallback(@RequestBody Map<String, Object> map) {
        String traceId = sequenceFacade.nextRedisId(SequenceBizKeyEnum.TRACE_ID.getPrefix(), SequenceBizKeyEnum.TRACE_ID.getKey(), SequenceBizKeyEnum.TRACE_ID.getWidth());

        log.info("[{}]充值回调内容 : {}", traceId, JSONObject.toJSONString(map));
        TraceVo<Map<String, Object>> traceVo = new TraceVo<Map<String, Object>>().setTraceId(traceId).setData(map);
        // 发送mq消息，通知交易中心
        notifyFacade.sendOne(MessageMsgDest.TOPIC_TRADE_ASYNC,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),
                MessageMsgDest.TAG_TRADE_RECHARGE,
                JSON.toJSONString(traceVo)
        );
        return SUCCESS_RESPONSE_CODE;
    }


    /**
     * 汇聚支付单笔代付异步通知处理接口
     *
     * @param respBody
     * @return
     */
    @RequestMapping("/singlePayNotify/{notifyType}")
    public JSONObject singlePayNotify(@RequestBody JSONObject respBody, @PathVariable(value = "notifyType") String notifyType) {
        log.info("订单号[{}]，通知类型[{}]汇聚支付单笔下发回调结果原始报文：{}", respBody.getString("merchantOrderNo"),
                notifyType, JsonUtil.toString(respBody));
        String channelMchNo = respBody.getString("userNo");
        String merchantOrderNo = respBody.getString("merchantOrderNo");
        PayReceiveReqVo reqVo = new PayReceiveReqVo();
        reqVo.setRespContent(respBody);
        if (StringUtil.isEmpty(notifyType) || "trade".equals(notifyType)) {
            reqVo.setChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
        } else if ("withdraw".equals(notifyType)) {
            WithdrawRecord withdrawRecord = withdrawRecordFacade.getByWithdrawRecordNo(merchantOrderNo);
            if (Objects.nonNull(withdrawRecord) && ChannelNoEnum.JOINPAY_JXH.name().equals(withdrawRecord.getChannelNo())) {
                reqVo.setChannelNo(ChannelNoEnum.JOINPAY_JXH.name());
            } else {
                reqVo.setChannelNo(ChannelNoEnum.JOINPAY.name());
            }
        } else {
            log.error("订单号[{}]，通知类型[{}]未知，不做任何处理。", respBody.getString("merchantOrderNo"), notifyType);
            JSONObject returnObj = new JSONObject();
            returnObj.put("statusCode", "2002");
            returnObj.put("message", "受理失败");
            return returnObj;
        }
        reqVo.setChannelMchNo(channelMchNo);
        SinglePayReceiveRespVo respVo = payBankFacade.verifySinglePayAndHandleResult(reqVo);
        if (StringUtil.isEmpty(notifyType) || "trade".equals(notifyType)) {
            // 发送mq消息，通知交易中心
            notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_SINGLEPAY_ASYNC,
                    channelMchNo,
                    merchantOrderNo,
                    NotifyTypeEnum.JXH_SINGLEPAY_NOTIFY.getValue(),
                    MessageMsgDest.TAG_JXH_SINGLEPAY,
                    JSON.toJSONString(respVo));
        } else if ("withdraw".equals(notifyType)) {
            notifyFacade.sendOne(MessageMsgDest.TOPIC_JXH_WITHDRAW_ASYNC,
                    channelMchNo,
                    merchantOrderNo,
                    NotifyTypeEnum.JXH_WITHDRAW.getValue(),
                    MessageMsgDest.TAG_JXH_WITHDRAW_ASYNC,
                    JSON.toJSONString(respVo));
        }

        JSONObject returnObj = new JSONObject();
        returnObj.put("statusCode", "2001");
        returnObj.put("message", "受理成功");
        log.info("订单号[{}]汇聚支付单笔下发回调返回给渠道的结果：{}", merchantOrderNo, JsonUtil.toString(returnObj));
        return returnObj;
    }

}
