package com.zhixianghui.web.notify.controller;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.ApiRespCodeEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 模拟商户接到回调
 * @date 2020-12-24 14:21
 **/
@RestController
@RequestMapping("/notifyMerchantReceive")
@Slf4j
public class TestMerchantNotifyController {

    @RequestMapping("/testReceive")
    public JSONObject testReceive(@RequestBody JSONObject respBody){
        log.info("模拟商户收到回调原文：{}", JsonUtil.toString(respBody));
        log.info("模拟商户收到回调data：{}", respBody.get("data"));

        JSONObject returnObj = new JSONObject();
        returnObj.put("resp_code", ApiRespCodeEnum.SUCCESS.getCode());
        return returnObj;
    }

    @RequestMapping("/testReceive2")
    public JSONObject testReceive2(@RequestBody JSONObject respBody){
        log.info("模拟商户收到回调原文：{}", JsonUtil.toString(respBody));
        log.info("模拟商户收到回调data：{}", respBody.get("data"));

        JSONObject returnObj = new JSONObject();
        log.info("不返回success");
        returnObj.put("resp_code", "abc");
        return returnObj;
    }

    @RequestMapping("/testSignReceive")
    public JSONObject testSignReceive(@RequestBody JSONObject respBody){
        log.info("模拟商户收到回调原文：{}", JsonUtil.toString(respBody));
        log.info("模拟商户收到回调data：{}", respBody.get("data"));

        JSONObject returnObj = new JSONObject();
        log.info("返回success");
        returnObj.put("resp_code", ApiRespCodeEnum.SUCCESS.getCode());
        return returnObj;
    }
}
