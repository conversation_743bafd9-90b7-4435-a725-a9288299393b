package com.zhixianghui.web.notify.controller.alipay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付宝回调controller
 */
@RestController
@Slf4j
public class AliPayCallBackController {

    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private NotifyFacade notifyFacade;


    @RequestMapping("callback/ali")
    public String callBack(HttpServletRequest request) {
        Map<String, String> params = getParameterMap(request);

        log.info("回调参数：{}",JSON.toJSONString(params));


        /*1. 返参验签*/

        //暂时注释掉验签
//        boolean success = alipayFacade.verifyNotify(params);
//        if (!success) {
//            log.error("验签不通过");
//            return SuccessFailEnum.FAIL.getCode();
//        }

        String tag;
        int delayLevel = 0;
        if (StringUtils.equals(params.get("notify_type"),"dut_user_sign")){
            /**签约回调**/
            tag = MessageMsgDest.TAG_ALIPAY_CALLBACK_USERSIGN;
        }else if (StringUtils.equals(params.get("msg_method"),"alipay.fund.trans.order.changed")){
            String biz_content_str = JSONObject.parseObject(JSONObject.toJSONString(params)).getString("biz_content");
            JSONObject bizContentJson = JSONObject.parseObject(biz_content_str);
            params = bizContentJson.toJavaObject(Map.class);
            String outBizNo = bizContentJson.getString("out_biz_no");

            if(StringUtils.equals(bizContentJson.getString("origin_interface"),"alipay.fund.trans.page.pay")&&StringUtils.equals(bizContentJson.getString("biz_scene"),"SATF_DEPOSIT")){
                if (StringUtils.equals(bizContentJson.getString("action_type"), "FINISH")) {
                    if (StringUtils.equals(bizContentJson.getString("status"), SuccessFailEnum.SUCCESS.name())) {
                        tag = MessageMsgDest.TAG_ALIPAY_CALLBACK_TRANSPAY;
                    } else if (StringUtils.equals(bizContentJson.getString("status"), "CLOSED")) {
                        tag = MessageMsgDest.TAG_ALIPAY_CALLBACK_TRANSPAY;
                    } else {
                        return SuccessFailEnum.FAIL.getCode();
                    }
                } else {
                    return SuccessFailEnum.SUCCESS.getCode();
                }
            }else if (StringUtils.equals(bizContentJson.getString("origin_interface"),"alipay.fund.trans.uni.transfer")&&StringUtils.equals(bizContentJson.getString("biz_scene"),"ENTRUST_TRANSFER")) {
                if (StringUtils.equals(bizContentJson.getString("action_type"), "FINISH")) {

                    /**
                     * 约定：以W为开头的外部订单号属于提现订单
                     */
                  if (outBizNo.startsWith("W")){
                        if (StringUtils.equals(bizContentJson.getString("status"), SuccessFailEnum.SUCCESS.name())) {
                            tag = MessageMsgDest.TAG_ALIPAY_WITHDRAW;
                        } else if (StringUtils.equals(bizContentJson.getString("status"), "CLOSED") ||
                                StringUtils.equals(bizContentJson.getString("status"), "FAIL") ||
                                StringUtils.equals(bizContentJson.getString("status"), "REFUND")) {
                            tag = MessageMsgDest.TAG_ALIPAY_WITHDRAW;
                        } else {
                            return SuccessFailEnum.FAIL.getCode();
                        }
                    }else {
                        if (StringUtils.equals(bizContentJson.getString("status"), SuccessFailEnum.SUCCESS.name())) {
                            tag = MessageMsgDest.TAG_ALIPAY_CALLBACK_ENTRUST_TRANSFER;
                        } else if (StringUtils.equals(bizContentJson.getString("status"), "CLOSED") ||
                                StringUtils.equals(bizContentJson.getString("status"), "FAIL") ||
                                StringUtils.equals(bizContentJson.getString("status"), "REFUND")) {
                            tag = MessageMsgDest.TAG_ALIPAY_CALLBACK_ENTRUST_TRANSFER;
                        } else {
                            return SuccessFailEnum.FAIL.getCode();
                        }
                    }
                } else {
                    return SuccessFailEnum.SUCCESS.getCode();
                }
            }else if(StringUtils.equals(bizContentJson.getString("origin_interface"),"alipay.fund.trans.uni.transfer")&&StringUtils.equals(bizContentJson.getString("biz_scene"),"ENTRUST_ALLOCATION")){
                if (outBizNo.startsWith("R") && outBizNo.endsWith("R")){
                    if (StringUtils.equals(bizContentJson.getString("action_type"), "FINISH")){
                        if(StringUtils.equals(bizContentJson.getString("status"), SuccessFailEnum.SUCCESS.name())){
                            log.info("支付宝回调...退款完成，订单号：[{}]",outBizNo);
                            return SuccessFailEnum.SUCCESS.getCode();
                        }else{
                            log.info("支付宝回调退款失败，重新发起退款，订单号：[{}]",outBizNo);
                            tag = MessageMsgDest.TAG_ALIPAY_CALLBACK_REFUND;
                        }
                    }else{
                        return SuccessFailEnum.FAIL.getCode();
                    }
                }else{
                    return SuccessFailEnum.SUCCESS.getCode();
                }
            }else{
                return SuccessFailEnum.FAIL.getCode();
            }
        } else if(StringUtils.equals(params.get("msg_method"), "alipay.fund.accountbook.trans.notify")) {
            //记账本入金
            log.info("支付宝回调记账本入金，回调参数：[{}]",JSON.toJSONString(params));
            String biz_content_str = JSONObject.parseObject(JSONObject.toJSONString(params)).getString("biz_content");
            JSONObject bizContentJson = JSONObject.parseObject(biz_content_str);
            params = bizContentJson.toJavaObject(Map.class);
            tag = MessageMsgDest.TAG_ALIPAY_TRANS_INCOME;
            delayLevel = MsgDelayLevelEnum.S_30.getValue();//延时30秒，目的是为了让'订单支付成功'的回调先执行，从而避免同一笔订单有两条充值记录
        } else if(StringUtils.equals(params.get("msg_method"),"ant.merchant.expand.indirect.zft.passed")) {
            log.info("支付宝直付通商户进件成功，回调参数：[{}]",JSON.toJSONString(params));
            String biz_content_str = JSONObject.parseObject(JSONObject.toJSONString(params)).getString("biz_content");
            JSONObject bizContentJson = JSONObject.parseObject(biz_content_str);
            params = bizContentJson.toJavaObject(Map.class);
            tag = MessageMsgDest.TAG_ZFT_AUDIT_PASS_NOTIFY;
        } else if (StringUtils.equals(params.get("msg_method"),"ant.merchant.expand.indirect.zft.rejected")) {
            log.info("支付宝直付通商户进件失败，回调参数：[{}]",JSON.toJSONString(params));
            String biz_content_str = JSONObject.parseObject(JSONObject.toJSONString(params)).getString("biz_content");
            JSONObject bizContentJson = JSONObject.parseObject(biz_content_str);
            params = bizContentJson.toJavaObject(Map.class);
            tag = MessageMsgDest.TAG_ZFT_AUDIT_REFUSE_NOTIFY;
        } else {
            return SuccessFailEnum.FAIL.getCode();
        }

        /*2. 发送消息*/
        if (delayLevel > 0) {
            notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_CALLBACK, NotifyTypeEnum.ALI_CALLBACK_MSG.getValue(), tag, JSON.toJSONString(params), delayLevel);
        } else {
            notifyFacade.sendOne(MessageMsgDest.TOPIC_ALIPAY_CALLBACK, NotifyTypeEnum.ALI_CALLBACK_MSG.getValue(), tag, JSON.toJSONString(params));
        }
        return SuccessFailEnum.SUCCESS.getCode();
    }

    private static Map getParameterMap(HttpServletRequest request) {
        // 参数Map
        Map<String,String[]> properties = request.getParameterMap();
        // 返回值Map
        Map<String,String> returnMap = new HashMap();

        for (Map.Entry<String,String[]> entry : properties.entrySet()) {
            String name = entry.getKey();
            String[]  values = entry.getValue();
            String value = "";

            for(int i=0;i<values.length;i++){
                value = values[i] + ",";
            }
            value = value.substring(0, value.length()-1);

            returnMap.put(name, value);
        }
        return returnMap;
    }
}
