package com.zhixianghui.web.notify.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.contrib.apache.httpclient.util.AesUtil;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.*;

/**
 * <AUTHOR>
 * @ClassName WxPayUtil
 * @Description TODO
 * @Date 2021/12/3 9:32
 */
@Slf4j
@Component
public class WxPayUtil {

    @Autowired
    private WxPayConfig wxPayConfig;

    private static ScheduledUpdateCertificatesVerifier verifier;

    private static PrivateKey privateKey;

    @PostConstruct
    public void init() throws UnsupportedEncodingException {
        //采用无限制加密算法
        Security.setProperty("crypto.policy","unlimited");

        privateKey = PemUtil.loadPrivateKey(new ByteArrayInputStream(wxPayConfig.getPrivateKey().getBytes("utf-8")));

        verifier = new ScheduledUpdateCertificatesVerifier(
                new WechatPay2Credentials(wxPayConfig.getMerchantId(), new PrivateKeySigner(wxPayConfig.getSerialNo(),privateKey)),
                wxPayConfig.getApiv3Key().getBytes(StandardCharsets.UTF_8));
    }

    public boolean verify(HttpServletRequest request, String respBody) {
        //获取验签字段
        String signature = request.getHeader("Wechatpay-Signature");
        String serial = request.getHeader("Wechatpay-Serial");

        String verifySignature = request.getHeader("Wechatpay-Timestamp") + "\n"
                + request.getHeader("Wechatpay-Nonce") + "\n" + respBody + "\n";
        boolean verify = verifier.verify(serial, verifySignature.getBytes(), signature);
        return verify;
    }

    public String decrypt(String respBody) throws GeneralSecurityException {
        JSONObject jsonObject = JSON.parseObject(respBody);
        JSONObject resources = jsonObject.getJSONObject("resource");
        String associatedData = resources.getString("associated_data");
        String nonce = resources.getString("nonce");
        String ciphertext = resources.getString("ciphertext");
        AesUtil aesUtil = new AesUtil(wxPayConfig.getApiv3Key().getBytes());
        return aesUtil.decryptToString(associatedData.getBytes(),nonce.getBytes(),ciphertext);
    }
}
