package com.zhixianghui.web.notify.vo.sign;

import lombok.Data;

/**
 * <AUTHOR>
 * @description e签宝流程结束回调Vo
 * @date 2021-01-13 15:23
 **/
@Data
public class EsignFlowEndVo {

    /**
     * 标记该通知的业务类型，该通知固定为：SIGN_FLOW_FINISH
     */
    private String action;

    /**
     * 流程id
     */
    private String flowId;

    /**
     * 签署文件主题描述
     */
    private String businessScence;

    /**
     * 任务状态
     *
     * 2-已完成: 所有签署人完成签署；
     *
     * 3-已撤销: 发起方撤销签署任务；
     *
     *
     * 5-已过期: 签署截止日到期后触发；
     *
     * 7-已拒签
     */
    private Integer flowStatus;

    /**
     * 当流程异常结束时，附加终止原因描述
     */
    private String statusDescription;

    /**
     * 签署任务发起时间 格式yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 签署任务结束时间 格式yyyy-MM-dd HH:mm:ss
     */
    private String endTime;

    /**
     * 时间戳
     */
    private Long timestamp;
}
