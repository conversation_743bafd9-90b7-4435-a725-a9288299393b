package com.zhixianghui.web.agent.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceOkHttpImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedissonConfigImpl;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.constant.WxConfigConstat;
import org.hibernate.validator.HibernateValidator;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.List;

/**
 * Author: Cmf
 * Date: 2019/10/9
 * Time: 17:20
 * Description:
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Autowired
    private PermissionInterceptor permissionInterceptor;

    @Autowired
    private WxTokenValidInterceptor wxTokenValidInterceptor;

    @Autowired
    private AllowCrossFilter allowCrossFilter;

    @Autowired
    private RequestBodyFilter requestBodyFilter;

    @Autowired
    private MethodArgumentResolver methodArgumentResolver;

    @Autowired
    private WxMethodArgumentResolver wxMethodArgumentResolver;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(permissionInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/user/login", "/user/captcha", "/user/sendSmsCode", "/user/resetPwd",
                        "/user/getUserStatus", "/error", "/user/getUserPublickey","/wx/**","/wxmini/biz/**");

        registry.addInterceptor(wxTokenValidInterceptor)
                .addPathPatterns("/wx/**")
                .excludePathPatterns("/wx/merchant/appNewMerchant",
                        "/wx/user/login","/wx/user/getUserStatus",
                        "/wx/user/register","/wx/user/sendSmsCode",
                        "/wx/user/getUserPublickey",
                        "/wx/user/wechatLogin",
                        "/wx/user/getWxMiniAccessToken",
                        "/wx/user/getWxUserInfo",
                        "/wxmini/biz/**"
                );
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/statics/**").addResourceLocations("classpath:/statics/");

    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(methodArgumentResolver);
        resolvers.add(wxMethodArgumentResolver);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("*")
                .allowedHeaders("content-type", PermissionConstant.REQUEST_TOKEN_HEADER)
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Bean
    public FilterRegistrationBean<AllowCrossFilter> allowCrossFilterRegistrationBean() {
        FilterRegistrationBean<AllowCrossFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(allowCrossFilter);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<RequestBodyFilter> requestBodyFilterRegistrationBean(){
        FilterRegistrationBean<RequestBodyFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(requestBodyFilter);
        registration.addUrlPatterns("/*");
        registration.setName("requestBodyFilter");
        registration.setOrder(1);
        return registration;
    }

    @Bean
    public Validator validator(){
        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .addProperty( "hibernate.validator.fail_fast", "true" )  // 快速失败模式
                .buildValidatorFactory();

        return validatorFactory.getValidator();
    }

    @Bean(name = "multipartResolver")
    public CommonsMultipartResolver getCommonsMultipartResolver() {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        multipartResolver.setMaxUploadSize(20971520);
        multipartResolver.setMaxInMemorySize(1048576);
        return multipartResolver;
    }

    @Bean
    @ConditionalOnClass(RedissonClient.class)
    public WxMaService wxMaService(){
        WxMaService wxMaService = new WxMaServiceOkHttpImpl();

        WxMaRedissonConfigImpl wxMaConfig = new WxMaRedissonConfigImpl(redissonClient,"wxmini:access:");
        wxMaConfig.setAppid(WxConfigConstat.APP_ID);
        wxMaConfig.setSecret(WxConfigConstat.SECRET);
        wxMaService.addConfig(WxConfigConstat.APP_ID, wxMaConfig);
        return wxMaService;
    }
}
