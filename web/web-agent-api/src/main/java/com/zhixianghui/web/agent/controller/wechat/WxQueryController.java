package com.zhixianghui.web.agent.controller.wechat;

import com.zhixianghui.common.statics.enums.fee.QueryTimeTypeEnum;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.fee.service.AgentFeeSumFacade;
import com.zhixianghui.facade.fee.service.AgentMonthBillFacade;
import com.zhixianghui.facade.fee.vo.AgentFeeSumVo;
import com.zhixianghui.web.agent.annotation.WxCurrentAgentNo;
import com.zhixianghui.web.agent.biz.AgentBiz;
import com.zhixianghui.web.agent.vo.PageVo;
import com.zhixianghui.web.agent.vo.agent.req.AgentQueryVo;
import com.zhixianghui.web.agent.vo.agent.res.AgentSimpleResVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName WxQueryController
 * @Description TODO
 * @Date 2023/9/6 9:55
 */
@RestController
@RequestMapping("/wx/query")
public class WxQueryController {

    @Reference
    private AgentFeeSumFacade agentFeeSumFacade;

    @Reference
    private AgentMonthBillFacade agentMonthBillFacade;

    @Autowired
    private AgentBiz agentBiz;

    /**
     * 查询合伙人收益
     * @param queryTimeType
     * @param agentNo
     * @return
     */
    @GetMapping("profit")
    public RestResult profit(@RequestParam Integer queryTimeType,@WxCurrentAgentNo String agentNo){
        QueryTimeTypeEnum.TimeInterval timeInterval = QueryTimeTypeEnum.getTimeByValue(queryTimeType);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("tradeDayStar", timeInterval.getStartTime());
        paramMap.put("tradeDayEnd", timeInterval.getEndTime());
        paramMap.put("agentNo",agentNo);
        AgentFeeSumVo agentFeeSumVo = agentFeeSumFacade.getSumByAgentNo(paramMap);
        agentFeeSumVo.setAgentNo(agentNo);
        return RestResult.success(agentFeeSumVo);
    }

    /**
     * 查询商户关系
     * @param relationType
     * @param pageVo
     * @param agentNo
     * @return
     */
    @PostMapping("merchantRelation")
    public RestResult merchantRelation(@RequestParam Integer relationType,@RequestParam String agentNo,@RequestBody PageVo pageVo){
        return RestResult.success(agentBiz.listSelfAgentMerchantRelationPage(relationType,agentNo,pageVo.toPageParam("CREATE_TIME DESC")));
    }

    /**
     * 合伙人列表
     */
    @PostMapping("listAgentPage")
    public RestResult<PageResult<List<AgentSimpleResVo>>> listAgentPage(@RequestBody PageVo pageVo,@WxCurrentAgentNo String agentNo){
        AgentQueryVo agentQueryVo = new AgentQueryVo();
        return RestResult.success(agentBiz.listAgentPage(agentQueryVo,pageVo.toPageParam("CREATE_TIME DESC"),agentNo));
    }

    /**
     * 合伙人月业绩查看
     */
    @GetMapping("monthBillList")
    public RestResult listPage(@WxCurrentAgentNo String agentNo)
    {
        //获取上月时间
        Date now = new Date();
        String endDate = DateUtil.dateYearMonthFormatter(now);
        String beginDate = DateUtil.dateYearMonthFormatter(DateUtil.addMonth(now,-12));
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("agentNo",agentNo);
        paramMap.put("beginBillDate",beginDate);
        paramMap.put("endBillDate",endDate);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("total",agentMonthBillFacade.getTotalProfitByAgent(agentNo));
        resultMap.put("list",agentMonthBillFacade.listMap(paramMap));
        return RestResult.success(resultMap);
    }


}
