package com.zhixianghui.web.agent.vo.wx;

import com.zhixianghui.common.statics.enums.user.agent.AgentLoginTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName WxLoginReqVo
 * @Description TODO
 * @Date 2023/8/31 17:34
 */
@Data
public class WxLoginReqVo implements Serializable {

    @NotEmpty(message = "请输入手机号码")
    private String phone;

    /**
     * 登录方式 {@link AgentLoginTypeEnum#getValue()}
     */
    @NotNull(message = "请选择登录类型")
    private Integer loginType;

    /**
     * 密码
     */
    private String pwd;

    /**
     * 手机验证码
     */
    private String smsCode;

    @NotEmpty(message = "code不能为空")
    private String code;
}
