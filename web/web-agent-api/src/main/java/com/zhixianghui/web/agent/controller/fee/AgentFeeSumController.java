package com.zhixianghui.web.agent.controller.fee;


import com.alibaba.fastjson.JSONArray;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.fee.QueryTimeTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.fee.service.AgentFeeSumFacade;
import com.zhixianghui.facade.fee.service.AgentMonthBillFacade;
import com.zhixianghui.facade.fee.vo.AgentFeeSumVo;
import com.zhixianghui.facade.fee.vo.AgentMonthBillVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentAgentNo;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.vo.fee.AgentFeeSumQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 合伙人分佣统计
 * <AUTHOR>
 * @since 2021-02-25
 */
@RestController
@RequestMapping("agentFeeSum")
@Slf4j
public class AgentFeeSumController {
    @Reference
    private AgentFeeSumFacade agentFeeSumFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private AgentFacade agentFacade;
    @Reference
    private AgentMonthBillFacade agentMonthBillFacade;

    @Permission("fee:agentMonthBill:export")
    @PostMapping("exportMonthBill")
    public RestResult<String> exportMonthBill(
            @RequestBody AgentMonthBillVo agentMonthBillVo,
            @CurrentStaffVo AgentStaffVO staffVO
    ) {
        agentMonthBillVo.setOperateName(staffVO.getName());
        agentMonthBillVo.setOperateTime(staffVO.getCreateTime());
        agentMonthBillVo.setAgentNo(staffVO.getAgentNo());
        agentMonthBillVo.setSystemType(SystemTypeEnum.AGENT_MANAGEMENT.getValue());
        agentMonthBillVo.setOperateName(staffVO.getPhone());
        try {
            agentMonthBillFacade.exportMonthBill(agentMonthBillVo);
            return RestResult.success("成功创建导出任务，请稍后查看");
        } catch (Exception e) {
            log.error("[{}]导出任务失败 : {}", agentMonthBillVo.getBillDate(), e);
            return RestResult.error("任务创建失败...");
        }

    }

    /**
     * 合伙人月业绩查看
     */
    @Permission("fee:agentMonthBill:list")
    @PostMapping("monthBillList")
    public RestResult<PageResult<List<AgentMonthBillVo>>> listPage(
            @RequestBody @Valid AgentMonthBillVo vo,
            @CurrentAgentNo String agentNo
    )
    {
        log.info("入参: {}", agentNo);
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        paramMap.put("agentNo",agentNo);
        PageResult<List<AgentMonthBillVo>> pageResult = agentMonthBillFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        log.info(JSONArray.toJSONString(pageResult.getData()));
        return RestResult.success(pageResult);
    }

    /**
     * 合伙人分佣统计分页查询
     */
    @Permission("fee:agentFeeSum:list")
    @PostMapping("listPage")
    public RestResult<PageResult<List<AgentFeeSumVo>>> listPage(@RequestBody @Valid AgentFeeSumQueryVo vo, @CurrentAgentNo String agentNo) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        paramMap.put("inviterNoOrAgentNo",agentNo);
        QueryTimeTypeEnum.TimeInterval timeInterval = QueryTimeTypeEnum.getTimeByValue(vo.getQueryTimeType());
        paramMap.put("tradeDayStar", timeInterval.getStartTime());
        paramMap.put("tradeDayEnd", timeInterval.getEndTime());
//        String sortColumn = "";
//        if (StringUtils.isNotBlank(vo.getSortColumns())) {
//            if ("tradeProfit".equals(vo.getSortColumns())) {
//                sortColumn = "TRADE_PROFIT desc";
//            }
//            if ("totalNetAmount".equals(vo.getSortColumns())) {
//                sortColumn = "TOTAL_NET_AMOUNT desc";
//            }
//            if ("totalProfit".equals(vo.getSortColumns())) {
//                sortColumn = "TOTAL_PROFIT desc";
//            }
//        }

        PageResult<List<AgentFeeSumVo>> pageResult = agentFeeSumFacade.listSumPage(
                paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize())
        );
        if(!CollectionUtils.isEmpty(pageResult.getData())){
            pageResult.getData().forEach(x -> {
                x.setTotalProfit("*");
                // 只能看自己的邀请奖励，不能看下级合伙人的
                if(!agentNo.equals(x.getAgentNo())){
                    x.setTotalInviteReward("*");
                }
            });
        }
        return RestResult.success(pageResult);
    }
}
