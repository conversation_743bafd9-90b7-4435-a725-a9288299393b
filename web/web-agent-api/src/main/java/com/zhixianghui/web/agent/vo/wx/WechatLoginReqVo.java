package com.zhixianghui.web.agent.vo.wx;

import com.zhixianghui.common.statics.enums.user.agent.AgentLoginTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName WxLoginReqVo
 * @Description TODO
 * @Date 2023/8/31 17:34
 */
@Data
public class WechatLoginReqVo implements Serializable {

    private static final long serialVersionUID = 9060600352815211215L;
    @NotEmpty(message = "code不能为空")
    private String code;
}
