package com.zhixianghui.web.agent.vo.wx;

import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.facade.trade.enums.WxUserTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName WxLoginTokenVo
 * @Description TODO
 * @Date 2023/9/5 10:47
 */
@Data
public class WxLoginTokenVo implements Serializable {

    private String userNo;

    private String wxUserNo;

    private String openId;

    private String appId;

    private AgentOperatorVO agentOperatorVO;

    private AgentStaffVO agentStaffVO;
}
