package com.zhixianghui.web.agent.vo.wx;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

@Data
public class SalesLeadVo implements Serializable {
    private static final long serialVersionUID = 5190591862423225633L;
    @NotBlank(message = "客户名称不能为空")
    private String contactName;
    @NotBlank(message = "联系手机号不能为空")
    private String contactMobile;
    @NotBlank(message = "联系邮箱不能为空")
    private String contactEmail;
    @NotBlank(message = "公司名称不能为空")
    private String companyName;
    @NotBlank(message = "职位信息不能为空")
    private String contactPosition;
    private String createNo;

}
