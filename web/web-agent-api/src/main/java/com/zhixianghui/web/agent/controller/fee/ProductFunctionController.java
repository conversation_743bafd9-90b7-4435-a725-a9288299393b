package com.zhixianghui.web.agent.controller.fee;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.fee.entity.Product;
import com.zhixianghui.facade.fee.service.ProductFunctionFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ProductFunctionController
 * @Description TODO
 * @Date 2021/5/12 17:42
 */
@RestController
@RequestMapping("productFunction")
public class ProductFunctionController {

    @Reference
    private ProductFunctionFacade productFunctionFacade;

    @GetMapping("getAllProduct")
    public RestResult<List<Product>> getAllProduct(){
        List<Product> list = productFunctionFacade.getAllProductFunction();
        return RestResult.success(list);
    }
}
