package com.zhixianghui.web.agent.vo.agent.req;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/8 10:28
 **/
@Data
public class AgentQueryVo {
    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentNameLike;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 合伙人状态
     */
    private Integer agentStatus;

    /**
     * 创建起始时间
     */
    private Date createBeginTime;

    /**
     * 创建截止时间
     */
    private Date createEndTime;
}
