package com.zhixianghui.web.agent.biz;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.enums.agent.RelationTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import com.zhixianghui.web.agent.vo.agent.req.AgentQueryVo;
import com.zhixianghui.web.agent.vo.agent.res.AgentSimpleResVo;
import com.zhixianghui.web.agent.vo.agent.res.MerchantResVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/8 10:28
 **/
@Service
public class AgentBiz {

    @Reference
    private AgentFacade agentFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    public PageResult<List<AgentSimpleResVo>> listAgentPage(AgentQueryVo agentQueryVo, PageParam pageParam, String agentNo) {
        Map<String,Object> paramMap = BeanUtil.toMap(agentQueryVo);
        //只能看自己邀请的
        paramMap.put("inviterNo",agentNo);
        PageResult<List<AgentResVo>> pageResult = agentFacade.listVoPage(paramMap,pageParam);
        List<AgentSimpleResVo> list = pageResult.getData().stream().map(
                res->{
                    AgentSimpleResVo agentSimpleResVo = new AgentSimpleResVo();
                    BeanUtils.copyProperties(res,agentSimpleResVo);
                    return agentSimpleResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public PageResult<List<MerchantResVo>> listAgentMerchantRelationPage(String agentNo, PageParam pageParam, String currentAgentNo) {
        LimitUtil.notEmpty(agentNo,"agentNo 不能为空");
        Agent agent = agentFacade.getByAgentNo(agentNo);
        if(agent == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人不存在,agentNo:" + agentNo);
        }
        if(!Objects.equals(agent.getInviterNo(),currentAgentNo)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("您非该合伙人的邀请人,无权查看");
        }

        PageResult<List<Merchant>> merResult = merchantQueryFacade.listPage(Collections.singletonMap("agentNo",agentNo),pageParam);
        return getMerListPageResult(merResult,agentNo);
    }

    public PageResult<List<MerchantResVo>> listSelfAgentMerchantRelationPage(Integer relationType, String agentNo, PageParam pageParam) {
        RelationTypeEnum relation = RelationTypeEnum.getEnumByValue(relationType);
        if(relation == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("筛选商户关系类型不存在,relationType:" + relationType);
        }

        PageResult<List<Merchant>> merResult;

        //直接 不需要查下属
        if(relation == RelationTypeEnum.DIRECT){
            merResult = merchantQueryFacade.listPage(Collections.singletonMap("agentNo",agentNo),pageParam);
            return getMerListPageResult(merResult,agentNo);
        }

        List<String> agentNos = Lists.newArrayList();
        //查询直接下属合伙人
        List<Agent> agents = agentFacade.listBy(Collections.singletonMap("inviterNo",agentNo));
        if(!CollectionUtils.isEmpty(agents)){
            agents.forEach(x->agentNos.add(x.getAgentNo()));
        }

        //全部加上自身
        if(relation == RelationTypeEnum.ALL){
            agentNos.add(agentNo);
        }

        //此时还是为空说明是间接且没有下属邀请人
        if(CollectionUtils.isEmpty(agentNos)){
            return PageResult.newInstance(pageParam,null);
        }
        //间接
        merResult = merchantQueryFacade.listPage(Collections.singletonMap("agentNos",agentNos),pageParam);
        return getMerListPageResult(merResult,agentNo);
    }

    public Agent getByAgentNo(String agentNo) {
        return agentFacade.getByAgentNo(agentNo);
    }

    private PageResult<List<MerchantResVo>> getMerListPageResult(PageResult<List<Merchant>> merResult,String agentNo) {
        if(!CollectionUtils.isEmpty(merResult.getData())){
            List<MerchantResVo> list = merResult.getData().stream().map(x->{
                MerchantResVo m = MerchantResVo.toVo(x);
                if (agentNo.equals(m.getAgentNo())){
                    m.setRelationType(RelationTypeEnum.DIRECT.getValue());
                }else{
                    m.setRelationType(RelationTypeEnum.INDIRECT.getValue());
                }
                return m;
            }).collect(Collectors.toList());
            return PageResult.newInstance(list,merResult.getPageCurrent(),merResult.getPageSize(),merResult.getTotalRecord());
        }else{
            return PageResult.newInstance(null,merResult.getPageCurrent(),merResult.getPageSize(),merResult.getTotalRecord());
        }
    }

    public List<Agent> listByInviter(String agentNo) {
        return agentFacade.listBy(
                MapUtil.builder(new HashMap<String, Object>())
                        .put("inviterNo", agentNo).build()
        );
    }
}
