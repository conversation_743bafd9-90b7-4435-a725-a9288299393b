package com.zhixianghui.web.agent.vo.user;

import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import lombok.Data;

/**
 * 商户信息vo
 *
 * <AUTHOR>
 */
@Data
public class AgentInfoVO {

    /**
     * 商户编号
     */
    private String agentNo;

    /**
     * 商户名称
     */
    private String agentName;
    /**
     * 类型
     */
    private Integer agentType;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 商户状态 {@link AgentStatusEnum#getValue()}
     */
    private Integer agentStatus;

    /**
     * 资质状态 {@link AuthStatusEnum#getValue()}
     */
    private Integer authStatus;
}
