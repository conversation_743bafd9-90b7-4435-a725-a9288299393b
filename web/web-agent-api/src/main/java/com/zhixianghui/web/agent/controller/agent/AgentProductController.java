package com.zhixianghui.web.agent.controller.agent;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.fee.entity.AgentProductRelation;
import com.zhixianghui.facade.fee.service.AgentProductRelationFacade;
import com.zhixianghui.web.agent.annotation.CurrentAgentNo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("agentProduct")
public class AgentProductController {
    @Reference
    private AgentProductRelationFacade agentProductRelationFacade;

    @RequestMapping("listAgentProductVo")
    public RestResult<List<Map<String, String>>> listAgentProductVo(@CurrentAgentNo String agentNo){
        List<AgentProductRelation> relationList = agentProductRelationFacade.listByAgentNoAndStatus(agentNo, null);
        List<Map<String, String>> result = relationList.stream().map(x->{
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("productNo", x.getProductNo());
            hashMap.put("productName", x.getProductName());
            return hashMap;
        }).collect(Collectors.toList());
        return RestResult.success(result);
    }
}
