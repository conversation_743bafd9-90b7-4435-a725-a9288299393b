package com.zhixianghui.web.agent.controller.agent;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteRateFacade;
import com.zhixianghui.web.agent.annotation.CurrentAgentNo;
import com.zhixianghui.web.agent.biz.AgentBiz;
import com.zhixianghui.web.agent.vo.PageVo;
import com.zhixianghui.web.agent.vo.agent.req.AgentQueryVo;
import com.zhixianghui.web.agent.vo.agent.res.AgentSimpleResVo;
import com.zhixianghui.web.agent.vo.agent.res.MerchantResVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 合伙人
 * @date 2021/2/8 10:26
 **/
@RestController
@RequestMapping("agent")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentController {
    private final AgentBiz agentBiz;
    @Reference
    MerchantEmployerQuoteFacade merchantEmployerQuoteFacade;

    /**
     * 合伙人列表
     */
    @PostMapping("listAgentPage")
    @Permission("agent:view")
    public RestResult<PageResult<List<AgentSimpleResVo>>> listAgentPage(@Validated @RequestBody AgentQueryVo agentQueryVo, @RequestBody PageVo pageVo, @CurrentAgentNo String agentNo){
        return RestResult.success(agentBiz.listAgentPage(agentQueryVo,pageVo.toPageParam("CREATE_TIME DESC"),agentNo));
    }

    /**
     * 合伙人-商户关系
     */
    @PostMapping("listAgentMerchantRelationPage")
    @Permission("agent:view")
    public RestResult<PageResult<List<MerchantResVo>>> listAgentMerchantRelationPage(@RequestParam String agentNo, @RequestBody PageVo pageVo, @CurrentAgentNo String currentAgentNo){
        return RestResult.success(agentBiz.listAgentMerchantRelationPage(agentNo,pageVo.toPageParam("CREATE_TIME DESC"),currentAgentNo));
    }

    /**
     * 当前登录合伙人-商户关系
     */
    @PostMapping("listSelfAgentMerchantRelationPage")
    @Permission("agent:view")
    public RestResult<PageResult<List<MerchantResVo>>> listSelfAgentMerchantRelationPage(@RequestParam Integer relationType, @CurrentAgentNo String currentAgentNo, @RequestBody PageVo pageVo){
        PageResult<List<MerchantResVo>> pageResult = agentBiz.listSelfAgentMerchantRelationPage(relationType,currentAgentNo,pageVo.toPageParam("CREATE_TIME DESC"));
        return RestResult.success(pageResult);
    }

    /**
     * 获取商户的费率
     * @param employerNo
     * @return
     */
    @Permission("agent:view")
    @RequestMapping("getMerchantQuoteInfo")
    public RestResult<List<MerchantEmployerQuote>> getMerchantQuoteInfo(@RequestParam String employerNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", employerNo);
        List<MerchantEmployerQuote> quoteList = merchantEmployerQuoteFacade.getQuoteList(employerNo, paramMap);
        return RestResult.success(quoteList);
    }

    /**
     * 合伙人信息查询
     *
     * @return .
     */
    @RequestMapping("getAgent")
    public RestResult<Agent> getMerchant(@CurrentAgentNo String agentNo) {
        if(StringUtil.isEmpty(agentNo)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前未绑定商户");
        }
        Agent agent = agentBiz.getByAgentNo(agentNo);
        return RestResult.success(agent);
    }
}
