package com.zhixianghui.web.agent.controller.config;


import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import com.zhixianghui.facade.common.service.InvoiceCategoryFacade;
import com.zhixianghui.web.agent.vo.config.InvoiceCategoryQueryVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发票类目配置
 * <AUTHOR>
 * @date 2020/8/10
 **/
@RestController
@RequestMapping("invoiceCategory")
public class InvoiceCategoryController {
    @Reference
    private InvoiceCategoryFacade invoiceCategoryFacade;

    /**
     * 发票类目分页查询
     * @param queryVo
     * @return
     */
    @PostMapping("listPage")
    public RestResult<PageResult<List<InvoiceCategory>>> listPage(@RequestBody InvoiceCategoryQueryVo queryVo) {
        PageParam pageParam = PageParam.newInstance(queryVo.getPageCurrent(), queryVo.getPageSize());
        PageResult<List<InvoiceCategory>> pageResult = invoiceCategoryFacade.listPage(BeanUtil.toMap(queryVo), pageParam);
        return RestResult.success(pageResult);
    }

    @GetMapping("getAll")
    public RestResult getAll(){
        List<InvoiceCategory> list = invoiceCategoryFacade.listAll();
        return RestResult.success(list);
    }
}
