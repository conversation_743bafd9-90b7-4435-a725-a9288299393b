package com.zhixianghui.web.agent.controller.config;


import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.config.WorkCategory;
import com.zhixianghui.facade.common.service.WorkCategoryFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工作岗位类目配置
 * <AUTHOR>
 * @date 2020/8/10
 **/
@RestController
@RequestMapping("workCategory")
public class WorkCategoryController {
    @Reference
    private WorkCategoryFacade workCategoryFacade;

    /**
     * 查询所有子工作岗位类目
     * @return
     */
    @GetMapping("listAll")
    public RestResult<List<WorkCategory>> listAll() {
        List<WorkCategory> categoryList = workCategoryFacade.listAll();
        return RestResult.success(categoryList);
    }

}
