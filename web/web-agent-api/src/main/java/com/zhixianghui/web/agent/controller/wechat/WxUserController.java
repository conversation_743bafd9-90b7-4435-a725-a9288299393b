package com.zhixianghui.web.agent.controller.wechat;

import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentLoginTypeEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentOperatorStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentOperatorFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.WxAgentRegisterVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.WechatUserInfo;
import com.zhixianghui.facade.trade.service.WeChatUserFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.agent.annotation.CurrentOperatorVO;
import com.zhixianghui.web.agent.annotation.Logger;
import com.zhixianghui.web.agent.annotation.WxCurrentOperatorVo;
import com.zhixianghui.web.agent.component.CaptchaHelper;
import com.zhixianghui.web.agent.component.DataDecryptHelper;
import com.zhixianghui.web.agent.component.JwtHelper;
import com.zhixianghui.web.agent.component.OperatorPwdHelper;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.constant.WxConfigConstat;
import com.zhixianghui.web.agent.enums.SmsCodeTypeEnum;
import com.zhixianghui.web.agent.third.wxmini.MiniSessionInfo;
import com.zhixianghui.web.agent.third.wxmini.WxMiniService;
import com.zhixianghui.web.agent.utils.NetUtil;
import com.zhixianghui.web.agent.vo.user.AgentInfoVO;
import com.zhixianghui.web.agent.vo.wx.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WxUserController
 * @Description TODO
 * @Date 2023/8/30 17:40
 */
@RestController
@RequestMapping("/wx/user")
public class WxUserController {

    @Autowired
    private CaptchaHelper captchaHelper;

    @Reference
    private AgentOperatorFacade agentOperatorFacade;

    @Autowired
    private OperatorPwdHelper operatorPwdHelper;

    @Autowired
    private DataDecryptHelper dataDecryptHelper;

    @Reference
    private WeChatUserFacade weChatUserFacade;

    @Reference
    private AgentFacade agentFacade;

    @Reference
    private AgentStaffFacade agentStaffFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private RedisClient redisClient;

    @Autowired
    private WxMiniService wxMiniService;

    private final static String WECHAT_LOGIN_KEY_PREFIX = "WX_AGENT_LOGIN_KEY:";

    public static final int AUTH_TIME = 24 * 60 * 60;

    @PostMapping("register")
    public RestResult register(@Validated @RequestBody WxAgentRegisterVo wxAgentRegisterVo){
        AgentOperator agentOperator = agentOperatorFacade.getByPhone(wxAgentRegisterVo.getPhone());
        if (agentOperator != null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户已存在");
        }
        agentFacade.createWxAgent(wxAgentRegisterVo);
        return RestResult.success("注册成功");
    }

    @GetMapping("getUserStatus")
    public RestResult<Map<String, Object>> getUserStatus(@RequestParam String phone) {
        AgentOperator operator = agentOperatorFacade.getByPhone(phone);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("register", operator != null);
        resultMap.put("initPwd", operator != null && operator.getIsInitPwd() == AgentInitPwdStatusEnum.INITED.getValue());
        if (operator != null) {
            resultMap.put("status", operator.getStatus());
        }
        return RestResult.success(resultMap);
    }

    /**
     * 发送短信验证码
     * @return
     */
    @PostMapping("sendSmsCode")
    public RestResult<String> sendSmsCode(@RequestParam String phone,@RequestParam Integer type) {
        if (!ValidateUtil.isMobile(phone)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("手机号格式错误");
        }
        if (type.intValue() == SmsCodeTypeEnum.LOGIN.getValue()){
            captchaHelper.sendSmsCode(phone,SmsCodeTypeEnum.LOGIN.getKey() + phone);
        }else if (type.intValue() == SmsCodeTypeEnum.REGISTER.getValue()){
            captchaHelper.sendSmsCode(phone,SmsCodeTypeEnum.REGISTER.getKey() + phone);
        }else{
            captchaHelper.sendSmsCode(phone,SmsCodeTypeEnum.NEW_MCH.getKey() + phone);
        }
        return RestResult.success("短信验证码发送成功");
    }

    @GetMapping("getUserPublickey")
    public RestResult<Map<String, Object>> getUserPublickey(@RequestParam String phone,
                                                            HttpServletRequest request) {
        Map<String, String> keyPair = RSAUtil.genKeyPair();
        keyPair.put("type","wechat");
        operatorPwdHelper.cachePublicKey(phone, keyPair);

        Map<String, Object> result = new HashMap<>();
        result.put(RSAUtil.PUBLIC_KEY, keyPair.get(RSAUtil.PUBLIC_KEY));
        return RestResult.success(result);
    }

    @PostMapping("login")
    public RestResult login(HttpServletRequest request, @RequestBody @Valid WxLoginReqVo loginReqVO) {
        AgentOperator agentOperator = verifyLogin(request,loginReqVO);
        //注册用户，如果用户已存在，直接返回用户信息
        WxLoginResVo wxLoginResVo = new WxLoginResVo();
        wxLoginResVo.setAgentOperatorVO(AgentOperatorVO.build(agentOperator));
        wxLoginResVo.setAgentNos(getAgentInfoListByPhone(loginReqVO.getPhone()));
        String token = jwtHelper.genWxToken(loginReqVO.getPhone());
        wxLoginResVo.setToken(token);
        WxLoginTokenVo wxLoginTokenVo = new WxLoginTokenVo();
        wxLoginTokenVo.setAgentOperatorVO(wxLoginResVo.getAgentOperatorVO());
        redisClient.set(WECHAT_LOGIN_KEY_PREFIX+ MD5Util.getMD5Hex(token), JsonUtil.toString(wxLoginTokenVo),AUTH_TIME);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("phone",loginReqVO.getPhone());
        paramMap.put("code",loginReqVO.getCode());
        //查询手机号是否存在用户
        notifyFacade.sendOne(MessageMsgDest.TOPIC_AGENT_GET_OR_REGISTER, loginReqVO.getPhone(), loginReqVO.getPhone(),
                NotifyTypeEnum.TRADE_NOTIFY.getValue(), MessageMsgDest.TAG_AGENT_GET_OR_REGISTER, JsonUtil.toString(paramMap));
        return RestResult.success(wxLoginResVo);
    }

    @PostMapping("selectAgent")
    public RestResult selectAgent(@RequestParam String agentNo, @WxCurrentOperatorVo AgentOperatorVO operatorVO, HttpServletRequest request){
        String token = request.getHeader(PermissionConstant.REQUEST_TOKEN_HEADER);
        AgentStaffVO staffVO = agentStaffFacade.getByPhone(agentNo, operatorVO.getPhone());
        if (staffVO == null) {
            return RestResult.error("不是该商户的员工");
        }
        //更新redis
        String info = redisClient.get(WECHAT_LOGIN_KEY_PREFIX+ MD5Util.getMD5Hex(token));
        if (StringUtils.isBlank(info)){
            return RestResult.error("登录信息不存在");
        }
        WxLoginTokenVo wxLoginTokenVo = JsonUtil.toBean(info,WxLoginTokenVo.class);
        wxLoginTokenVo.setAgentStaffVO(staffVO);
        redisClient.set(WECHAT_LOGIN_KEY_PREFIX+ MD5Util.getMD5Hex(token), JsonUtil.toString(wxLoginTokenVo),AUTH_TIME);
        return RestResult.success(staffVO);
    }

    @PostMapping("loginOut")
    public RestResult loginOut(HttpServletRequest request){
        String token = request.getHeader(PermissionConstant.REQUEST_TOKEN_HEADER);
        redisClient.del(WECHAT_LOGIN_KEY_PREFIX+ MD5Util.getMD5Hex(token));
        return RestResult.success("退出登录成功");
    }

    /**
     * 查询操作员的可选商户列表
     * @return
     */
    public List<AgentInfoVO> getAgentInfoListByPhone(String phone) {
        List<String> agentNoList = agentStaffFacade.listByPhone(phone).stream()
                .map(AgentStaffVO::getAgentNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(agentNoList)){
            return new ArrayList<>();
        }
        // 查询操作员关联的商户
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agentNoList", agentNoList);
        List<Agent> agents = agentFacade.listBy(paramMap);

        List<AgentInfoVO> agentInfoVOS = agents.stream().filter(
                agent -> agent.getAgentStatus() != AgentStatusEnum.RETREAT.getValue()
        ).map(agent -> {
            AgentInfoVO agentInfoVO = new AgentInfoVO();
            agentInfoVO.setAgentNo(agent.getAgentNo());
            agentInfoVO.setAgentName(agent.getAgentName());
            agentInfoVO.setAgentStatus(agent.getAgentStatus());
            agentInfoVO.setAuthStatus(agent.getAuthStatus());
            agentInfoVO.setContactPhone(agent.getContactPhone());
            agentInfoVO.setAgentType(agent.getAgentType());
            return agentInfoVO;
        }).collect(Collectors.toList());
        return agentInfoVOS;
    }

    /**
     * 验证登录
     */
    public AgentOperator verifyLogin(HttpServletRequest request,WxLoginReqVo loginReqVO) throws BizException {
        AgentOperator operator = agentOperatorFacade.getByPhone(loginReqVO.getPhone());
        if (operator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账号不存在");
        } else if (operator.getStatus() == AgentOperatorStatusEnum.INACTIVE.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号密码输入错误次数过多已被冻结，请重置密码后再登录");
        }
        //校验RSA公私钥对，同时返回私钥
        String privateKey = operatorPwdHelper.getPrivateKey(loginReqVO.getPhone(), request);
        if (loginReqVO.getLoginType() == AgentLoginTypeEnum.PWD.getValue()) {  // 密码登录
            // 校验登录密码
            if (operator.getIsInitPwd() == AgentInitPwdStatusEnum.NO_INIT.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号未设置密码");
            }
            // 解密密码并校验
            operatorPwdHelper.loginVerifyPwd(operator, dataDecryptHelper.decryptData(privateKey, loginReqVO.getPwd()));
            //校验通过则临时把私钥放入
            operator.setPrivateKey(privateKey);
        } else if (loginReqVO.getLoginType() == AgentLoginTypeEnum.SMS.getValue()) {  // 手机验证码登录
            // 校验手机验证码
            captchaHelper.verifySmsCode(loginReqVO.getPhone(), dataDecryptHelper.decryptData(privateKey, loginReqVO.getSmsCode()),SmsCodeTypeEnum.LOGIN.getKey() + loginReqVO.getPhone());
            captchaHelper.invalidSmsCode(SmsCodeTypeEnum.LOGIN.getValue() + loginReqVO.getPhone(),loginReqVO.getPhone());
            //校验通过则临时把私钥放入
            operator.setPrivateKey(privateKey);
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("登录方式参数错误");
        }
        return operator;
    }

    @PostMapping("wechatLogin")
    public RestResult<MiniSessionInfo> wechatLogin(@RequestBody @Valid WechatLoginReqVo loginReqVO) {
        String code = loginReqVO.getCode();
        MiniSessionInfo wxMiniSession = wxMiniService.getWxMiniSession(code);
        return RestResult.success(wxMiniSession);
    }

    @PostMapping("getWxMiniAccessToken")
    public RestResult<String> getWxMiniAccessToken(){
        String wxMiniAccessToken = wxMiniService.getWxMiniAccessToken();
        return RestResult.success(wxMiniAccessToken);
    }

    @PostMapping("getWxUserInfo")
    public RestResult<WxMaUserInfo> getWxUserInfo(@RequestBody @Validated WxMiniUserInfoReqVo userInfoReqVo){
        WxMaUserInfo wxUserInfo = wxMiniService.getWxUserInfo(userInfoReqVo.getSessionKey(), userInfoReqVo.getEncryptedData(), userInfoReqVo.getIvStr());
        return RestResult.success(wxUserInfo);
    }

    @GetMapping("agentList")
    public RestResult<List<AgentInfoVO>> agentList(@RequestParam(required = true) String phone) {
        return RestResult.success(getAgentInfoListByPhone(phone));
    }
}
