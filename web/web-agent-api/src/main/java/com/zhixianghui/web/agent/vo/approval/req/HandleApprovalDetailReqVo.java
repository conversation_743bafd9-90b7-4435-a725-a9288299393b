package com.zhixianghui.web.agent.vo.approval.req;


import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2020-08-19 09:18
 **/
@Data
public class HandleApprovalDetailReqVo {

    @NotNull(message = "节点id不能为空")
    private Long approvalDetailId;

    @NotNull(message = "处理节点的类型不能为空")
    private Integer handleStatus;

    @NotNull(message = "流程主题类型不能为空")
    private Integer flowTopicType;

    @Length(max = 100, message = "审批意见不允许超过100字")
    private String approvalOpinion;

}
