package com.zhixianghui.web.agent.biz;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowDetailFacade;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.vo.PageVo;
import com.zhixianghui.web.agent.vo.approval.req.ApprovalFlowQueryVo;
import com.zhixianghui.web.agent.vo.approval.res.ApprovalDetailResVo;
import com.zhixianghui.web.agent.vo.approval.res.ApprovalListResVo;
import com.zhixianghui.web.agent.vo.approval.res.ApprovalResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description web审批服务逻辑biz
 * @date 2020-08-17 12:34
 **/
@Slf4j
@Service
public class ApprovalFlowBiz {
    @Reference
    private ApprovalFlowFacade approvalFlowFacade;
    @Reference
    private ApprovalFlowDetailFacade approvalFlowDetailFacade;

    public PageResult<List<ApprovalListResVo>> listSendApproval(ApprovalFlowQueryVo approvalFlowQueryVo, PageVo pageVo, AgentStaffVO staffVO) {
        Map<String,Object> paramMap = BeanUtil.toMap(approvalFlowQueryVo);
        //维度是合伙人维度
        paramMap.put("dimension",staffVO.getAgentNo());
        paramMap.put("platform", PlatformSource.AGENT.getValue());
        PageResult<List<ApprovalFlow>> pageResult = approvalFlowFacade.listPage(paramMap,pageVo.toPageParam("CREATE_TIME DESC"));
        List<ApprovalListResVo> list = pageResult.getData().stream().map(ApprovalListResVo::buildVo).collect(Collectors.toList());
        return PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord());
    }

    public ApprovalResVo getSingleApproval(Long approvalFlowId, AgentStaffVO staffVO) {
        ApprovalFlow approvalFlow = auth(approvalFlowId, staffVO);
        return ApprovalResVo.buildVo(approvalFlow);
    }

    public List<ApprovalDetailResVo> getApprovalDetail(Long approvalFlowId,AgentStaffVO staffVO) {
        auth(approvalFlowId, staffVO);
        List<ApprovalFlowDetail> result = approvalFlowDetailFacade.listBy(Collections.singletonMap("approvalFlowId", approvalFlowId),"CREATE_TIME DESC, UPDATE_TIME DESC,ID DESC");
        return result.stream().map(ApprovalDetailResVo::buildVo).collect(Collectors.toList());
    }

    public void cancelApproval(Long approvalFlowId,AgentOperatorVO operatorVO, AgentStaffVO staffVO) {
        auth(approvalFlowId, staffVO);
        //上一行已经校验合伙人维度
        //为了兼容此处使用admin权限，跳过里面发起人维度的校验
        approvalFlowFacade.cancelApproval(approvalFlowId, operatorVO.getId(),operatorVO.getPhone(),PlatformSource.OPERATION.getValue(), true);
    }

    /**
     * 检验审批流能否审核 能就返回审批流对象
     * @param approvalFlowId 审批流id
     * @param staffVO 操作员
     * @return 审批流对象
     */
    private ApprovalFlow auth(Long approvalFlowId, AgentStaffVO staffVO) {
        ApprovalFlow approvalFlow = approvalFlowFacade.getOne(Collections.singletonMap("id",approvalFlowId));
        String agentNo = approvalFlow.getJsonEntity().getDimension();
        if(StringUtils.isBlank(agentNo) || !agentNo.equals(staffVO.getAgentNo())){
            log.error("审批所属agentNo:{},当前人agentNo:{}",agentNo,staffVO.getAgentNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无权限查看该审批流程");
        }
        return approvalFlow;
    }


    public void updateExtInfo(UpdateExtInfoVo updateExtInfoVo, AgentOperatorVO currentOperator) {
        updateExtInfoVo.setHandlerId(currentOperator.getId());
        updateExtInfoVo.setHandlerName(currentOperator.getPhone());
        updateExtInfoVo.setPlatform(PlatformSource.AGENT.getValue());
        approvalFlowFacade.updateExtInfoForAgent(updateExtInfoVo);
    }
}
