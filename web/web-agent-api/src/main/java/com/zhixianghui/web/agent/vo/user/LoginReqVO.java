package com.zhixianghui.web.agent.vo.user;

import com.zhixianghui.common.statics.enums.user.agent.AgentLoginTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 登录请求VO
 *
 * <AUTHOR>
 */
@Data
public class LoginReqVO {

    /**
     * 手机号
     */
    @NotEmpty
    private String phone;

    /**
     * 登录方式 {@link AgentLoginTypeEnum#getValue()}
     */
    @NotNull
    private Integer loginType;

    /**
     * 密码
     */
    private String pwd;

    /**
     * 图形验证码id
     */
    private String captchaId;

    /**
     * 图形验证码
     */
    private String captcha;

    /**
     * 手机验证码
     */
    private String smsCode;
}
