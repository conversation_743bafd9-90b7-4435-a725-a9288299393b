package com.zhixianghui.web.agent.controller.wechat;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.banklink.enums.RobotTypeEnum;
import com.zhixianghui.facade.banklink.service.message.RobotFacade;
import com.zhixianghui.facade.banklink.vo.robot.MarkDownMsg;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.flow.dto.FlowStartDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import com.zhixianghui.facade.merchant.entity.SalesLead;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.SalesLeadStatusEnums;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.AgentProductQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantCreateAnonFacade;
import com.zhixianghui.facade.merchant.service.SalesLeadFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.CreateLinkReqVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerAddVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.web.agent.annotation.WxCurrentStaffVo;
import com.zhixianghui.web.agent.component.CaptchaHelper;
import com.zhixianghui.web.agent.vo.wx.SalesLeadVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName WxMerchantController
 * @Description TODO
 * @Date 2023/9/6 17:45
 */
@Slf4j
@RestController
@RequestMapping("/wx/merchant")
public class WxMerchantController {

    @Reference
    private AgentFacade agentFacade;

    @Reference
    private MerchantCreateAnonFacade merchantCreateAnonFacade;

    @Autowired
    private CaptchaHelper captchaHelper;

    @Reference
    private SequenceFacade sequenceFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private AgentProductQuoteFacade agentProductQuoteFacade;

    @Reference
    private AgentStaffFacade agentStaffFacade;

    @Reference
    private SalesLeadFacade salesLeadFacade;
    @Reference
    private RobotFacade robotFacade;

    private static final String SECRET = "m26K2Ygw9qoU2x0z";

    private final static String KEY_PRE = "anonymous:create:";

    @GetMapping("generateCreateURL")
    public RestResult generateCreateURL(@WxCurrentStaffVo AgentStaffVO staffVO){
        String agentNo = staffVO.getAgentNo();
        String phone = staffVO.getPhone();
        Agent agent = agentFacade.getByAgentNo(agentNo);
        if (agent == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人信息不存在");
        }
        //查询是否已经存在链接
        AgentStaffVO agentStaff = agentStaffFacade.getByPhone(staffVO.getAgentNo(),staffVO.getPhone());
        if (StringUtils.isNotBlank(agentStaff.getJsonEntity().getInviteUrl())){
            return RestResult.success(agentStaff.getJsonEntity().getInviteUrl());
        }else{
            //不存在，生成链接
            //判断是否有报价单
            List<AgentProductQuote> agentProductQuoteList = agentProductQuoteFacade.getByAgentNo(agentNo);
            if (agentProductQuoteList == null || agentProductQuoteList.size() == 0){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请联系客户经理添加报价单");
            }
            CreateLinkReqVo createLinkReqVo = new CreateLinkReqVo();
            createLinkReqVo.setAgentNo(agentNo);
            createLinkReqVo.setPlatform(PlatformSource.AGENT.getValue());
            createLinkReqVo.setCreateAgentPhone(phone);
            String url = merchantCreateAnonFacade.getURL(createLinkReqVo);
            //增加平台参数
            url = url + "&t=" + PlatformSource.AGENT.getValue();
            //保存到数据库
            agentStaff.getJsonEntity().setInviteUrl(url);
            agentStaff.setExtraInfo(JsonUtil.toString(agentStaff.getJsonEntity()));
            agentStaffFacade.updateExtraInfoByAgentNo(agentStaff);
            return RestResult.success(url);
        }
    }

    @RequestMapping("appNewMerchant")
    public RestResult<Map<String,Object>> submitAudit(@RequestParam String p, @RequestParam Integer t, @Valid @RequestBody FlowStartDto<MerchantEmployerAddVo> flowStartDto) throws UnsupportedEncodingException {
        //校验链接
        //解密
        p = p.replace(" ","+");
        String param = AESUtil.decryptECB(p,SECRET);
        CreateLinkReqVo createLinkReqVo = JSONUtil.toBean(param,CreateLinkReqVo.class);
        if (!createLinkReqVo.getPlatform().equals(t)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求地址参数有误");
        }
        MerchantEmployerAddVo merchantEmployerAddVo = flowStartDto.getExtObj();
        merchantEmployerAddVo.setWorkerMonthIncomeRate(new BigDecimal(100));
        //校验验证码
        String key = KEY_PRE + merchantEmployerAddVo.getContactPhone();
        captchaHelper.verifySmsCode(merchantEmployerAddVo.getContactPhone(),merchantEmployerAddVo.getSmsCode(),key);
        captchaHelper.invalidSmsCode(key,merchantEmployerAddVo.getContactPhone());
        // 生成商户编号
        String mchNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getPrefix(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getKey(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getWidth());
        merchantEmployerAddVo.setMchNo(mchNo);
        //查询合伙人
        Agent agent = agentFacade.getByAgentNo(createLinkReqVo.getAgentNo());
        //当前的所选合伙人为指定的邀请人
        PmsOperator pmsOperator = new PmsOperator();
        pmsOperator.setLoginName(createLinkReqVo.getCreateAgentPhone());
        pmsOperator.setRealName(agent.getAgentName());

        merchantEmployerAddVo.setAgentNo(agent.getAgentNo());
        merchantEmployerAddVo.setAgentName(agent.getAgentName());
        merchantEmployerAddVo.setPmsOperator(pmsOperator);

        AgentStaffVO staffVO = agentStaffFacade.getByPhone(agent.getAgentNo(),createLinkReqVo.getCreateAgentPhone());

        List<AgentResVo> agentList = agentFacade.listVoPage(Collections.singletonMap("agentNo",agent.getAgentNo()), PageParam.newInstance(1,1)).getData();
        if(CollectionUtils.isEmpty(agentList) || agentList.get(0).getSalerId() == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到销售或当前合伙人信息");
        }
        //销售为合伙人的销售
        merchantEmployerAddVo.setSalerId(agentList.get(0).getSalerId());
        merchantEmployerAddVo.setSalerName(agentList.get(0).getSalerName());
        log.info("商户入网：{}", JsonUtil.toString(merchantEmployerAddVo));
        //当前用户
        FlowUserVo createUser = new FlowUserVo();
        createUser.setPlatform(PlatformSource.AGENT.getValue());
        createUser.setUserName(createLinkReqVo.getCreateAgentPhone());
        createUser.setUserId(staffVO.getId());
        createUser.setNo(staffVO.getAgentNo());
        //构建参数
        ProcessVo processVo = new ProcessVo();
        processVo.setBusinessKey(mchNo);
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(FlowTypeEnum.AGENT_MCH_APPLY.name());
        processVo.setFlowTopicType(FlowTypeEnum.AGENT_MCH_APPLY.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-",FlowTypeEnum.AGENT_MCH_APPLY.getDesc(),merchantEmployerAddVo.getMchName()));
        processVo.setExtInfo(JsonUtil.toString(merchantEmployerAddVo));
        processVo.setRemark(flowStartDto.getRemark());
        //构建参与者，下一个环节参与者为销售
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(merchantEmployerAddVo.getSalerId());
        flowUserVo.setUserName(merchantEmployerAddVo.getSalerName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        //注意这里key与流程图相同
        flowStartDto.getParticipant().put("salesId",new ArrayList<FlowUserVo>(){{add(flowUserVo);}});
        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo,createUser,flowStartDto.getParticipant(),flowStartDto.getCondition());
        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_CREATE_MERCHANT_NOTIFY, JsonUtil.toString(commonFlow));
        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @RequestMapping("recommendCustom")
    public RestResult<Map<String,Object>> recommendCustom(@RequestBody @Validated SalesLeadVo customInfo, @WxCurrentStaffVo AgentStaffVO staffVO){
        if (customInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提交信息不能为空");
        }
        if (!ValidateUtil.isEmail(customInfo.getContactEmail())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("邮箱格式错误");
        }
        if (!ValidateUtil.isMobile(customInfo.getContactMobile())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("手机号码格式错误");
        }
        SalesLead salesLead = BeanUtil.copyProperties(customInfo, SalesLead.class);


        salesLead.setAgentNo(staffVO.getAgentNo());
        salesLead.setAgentName(staffVO.getAgentName());
        salesLead.setStatus(SalesLeadStatusEnums.CREATED.getCode().shortValue());

        if (StringUtils.isNotBlank(customInfo.getCreateNo())) {
            salesLeadFacade.updateByCreateNo(salesLead);
        }else {
            salesLead.setCreateTime(new Date());
            salesLead.setCreateNo(RandomUtil.randomNumbers(7)+System.currentTimeMillis());
            salesLeadFacade.save(salesLead);

            ThreadUtil.execAsync(() -> {

                //机器人通知
                StringBuffer sb = new StringBuffer("#### 商户推荐提醒\\n ");
                sb.append("\\n > 商户名称：").append(customInfo.getCompanyName())
                        .append("\\n > 联系人：").append(customInfo.getContactName())
                        .append("\\n > 推荐人：").append(salesLead.getAgentName());

                MarkDownMsg markDownMsg = new MarkDownMsg();
                markDownMsg.setUnikey(IdUtil.fastUUID());
                markDownMsg.setRobotType(RobotTypeEnum.MERCHANT_RECOMMEND_ROBOT.getType());
                markDownMsg.setContent(sb.toString());
                robotFacade.pushMarkDownAsync(markDownMsg);
            });
        }


        Map<String, Object> resp = new HashMap<>();
        resp.put("createNo", salesLead.getCreateNo());
        return RestResult.success(resp);
    }

}
