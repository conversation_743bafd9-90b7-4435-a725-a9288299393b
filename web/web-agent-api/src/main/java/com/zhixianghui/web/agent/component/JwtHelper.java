package com.zhixianghui.web.agent.component;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.JwtTokenUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.service.agent.AgentOperatorFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.dto.JwtInfo;
import com.zhixianghui.web.agent.dto.Session;
import com.zhixianghui.web.agent.utils.NetUtil;
import com.zhixianghui.web.agent.vo.permission.FunctionVO;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Log4j2
public class JwtHelper {

    private static final String USER_TOKEN_INFO_CLAIM_KEY = "USER_TOKEN_INFO_CLAIM_KEY";

    @Value(value = "${jwtPublicKey}")
    private String jwtPublicKey;

    @Value(value = "${jwtPrivateKey}")
    private String jwtPrivateKey;

    private RSAPublicKey publicKey = null;

    private RSAPrivateKey privateKey = null;

    @Reference
    private AgentOperatorFacade agentOperatorFacade;
    @Reference
    private AgentStaffFacade agentStaffFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Autowired
    private SessionManager sessionManager;

    @PostConstruct
    public void init() {
        try {
            publicKey = RSAUtil.parsePublicKey(jwtPublicKey);
            privateKey = RSAUtil.parsePrivateKey(jwtPrivateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成token
     */
    public String genToken(HttpServletRequest request, JwtInfo jwtInfo) {
        Map<String, String> claims = new HashMap<>();
        claims.put("UserAgent", request.getHeader("User-Agent"));
        claims.put("Host", NetUtil.getIpAddr(request));
        return JwtTokenUtil.genToken(privateKey, jwtInfo, claims);
    }

    /**
     * 生成token
     */
    public String genWxToken(String phone) {
        Algorithm algorithm = Algorithm.RSA256(null, privateKey);
        JWTCreator.Builder builder = JWT.create();
        builder.withClaim(USER_TOKEN_INFO_CLAIM_KEY, phone);
        return builder.sign(algorithm);
    }

    public void verifyWxToken(String token){
        try {
            Algorithm algorithm = Algorithm.RSA256(publicKey, null);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(token);
        } catch (Exception ex) {
            log.info("token无效: ", ex);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("token无效");
        }
    }

    /**
     * 校验token
     */
    public JwtInfo verifyToken(HttpServletRequest request) {

        String token = null;
        if (("GET".equals(request.getMethod()) && StringUtil.isNotEmpty(request.getParameter(PermissionConstant.REQUEST_TOKEN_HEADER)))) {
            token = request.getParameter(PermissionConstant.REQUEST_TOKEN_HEADER);
        } else {
            token = request.getHeader(PermissionConstant.REQUEST_TOKEN_HEADER);
        }
//        if (StringUtils.isBlank(token)) {
//            log.warn("token为空, 该用互可能未登录");
//            return null;
//        }

        /***********************接口测试，不合生产*****************************/
        final String openCaptcha = dataDictionaryFacade.getSystemConfig("OPEN_CAPTCHA");
        if (openCaptcha != null) {
            String loginName = request.getHeader("x-loginName");
            String agentNo = request.getHeader("x-agentNo");
            Session session = null;
            if (StringUtil.isNotEmpty(loginName)) {
                AgentOperator operator = agentOperatorFacade.getByPhone(loginName);
                AgentOperatorVO vo = AgentOperatorVO.build(operator);
                session = sessionManager.createSession(vo.getPhone());
                session.save();
                session.setAttribute(PermissionConstant.OPERATOR_SESSION_KEY, JsonUtil.toString(vo));

                JwtInfo jwtInfo = new JwtInfo();
                jwtInfo.setUuid(session.getUuid());
                jwtInfo.setAgentOperatorVO(vo);
                token = genToken(request, jwtInfo);
            }
            if (StringUtil.isNotEmpty(agentNo)) {
                AgentStaffVO staffVO = agentStaffFacade.getByPhone(agentNo, loginName);
                List<FunctionVO> functions = agentStaffFacade.listFunctionByStaffId(agentNo, staffVO.getId()).stream()
                        .map(FunctionVO::buildVo).collect(Collectors.toList());
                // 商户编号、员工信息、权限信息更新至session
                Map<String, String> map = new HashMap<>();
                map.put(PermissionConstant.AGENT_NO_SESSION_KEY, agentNo);
                map.put(PermissionConstant.STAFF_SESSION_KEY, JsonUtil.toString(staffVO));
                map.put(PermissionConstant.PERMISSION_SESSION_KEY, JsonUtil.toString(
                        functions.stream().map(FunctionVO::getPermissionFlag).collect(Collectors.toList())));
                session.setAttribute(map);
            }
        }
        /***********************接口测试，不合生产*****************************/

        Map<String, String> claims = new HashMap<>();
        claims.put("UserAgent", request.getHeader("User-Agent"));
//        claims.put("Host", NetUtil.getIpAddr(request));

        JwtInfo jwtInfo = null;
        try {
            jwtInfo = JwtTokenUtil.verifyToken(publicKey, token, claims, JwtInfo.class);
        } catch (Exception e) {
            log.error("校验token失败, IP: {}, {},", claims.get("Host"), e);
        }
        return jwtInfo;
    }
}
