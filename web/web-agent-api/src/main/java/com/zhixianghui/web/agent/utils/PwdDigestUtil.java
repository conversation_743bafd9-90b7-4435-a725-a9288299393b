package com.zhixianghui.web.agent.utils;

import com.zhixianghui.common.util.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.codec.digest.DigestUtils;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码摘要
 *
 * <AUTHOR> <PERSON>
 */
public class PwdDigestUtil {

    private final static String SALT = "jE9ezW9hyBbzxlzacYxPv4ey4am5PsZ59dBsUEErkz0=";
    /**
     * 密码摘要
     * @param pwd   密码原文
     */
    public static DigestResult digestPwd(String pwd) {
        // 生成随机盐
        SecureRandom random = new SecureRandom();
        byte[] saltByte = new byte[32];
        random.nextBytes(saltByte);
        String salt = Base64.getEncoder().encodeToString(saltByte);
        // 将 salt + password 哈希
        return new DigestResult(salt, DigestUtils.sha1Hex(salt + pwd));
    }

    /**
     * 密码摘要
     * @param pwd   密码原文
     */
    public static DigestResult digestFixSaltPwd(String pwd) {
        return new DigestResult(SALT, DigestUtils.sha1Hex(SALT + pwd));
    }

    public static void main(String[] args) {
        SecureRandom random = new SecureRandom();
        byte[] saltByte = new byte[32];
        random.nextBytes(saltByte);
        System.out.println(JsonUtil.toString(saltByte));
    }

    /**
     * 验证密码
     * @param salt      随机盐
     * @param digest    摘要
     * @param pwd       密码原文
     */
    public static boolean verifyPwd(String salt, String digest, String pwd) {
        return DigestUtils.sha1Hex(salt + pwd).equals(digest);
    }

    /**
     * 验证密码
     * @param digest    摘要
     * @param pwd       密码原文
     */
    public static boolean verifyFixSaltPwd(String digest, String pwd) {
        return DigestUtils.sha1Hex(SALT + pwd).equals(digest);
    }

    @Data
    @AllArgsConstructor
    public static class DigestResult {

        /**
         * 随机盐
         */
        private String salt;

        /**
         * 密码摘要
         */
        private String pwd;
    }
}
