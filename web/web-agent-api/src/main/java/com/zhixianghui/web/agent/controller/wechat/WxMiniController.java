package com.zhixianghui.web.agent.controller.wechat;

import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.AgentBussinessEcard;
import com.zhixianghui.facade.merchant.entity.WechatUserInfo;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.WechatUserInfoFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentBussinessEcardFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentOperatorFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.WxBindMobileReqVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.agent.component.CaptchaHelper;
import com.zhixianghui.web.agent.component.DataDecryptHelper;
import com.zhixianghui.web.agent.component.JwtHelper;
import com.zhixianghui.web.agent.constant.WxConfigConstat;
import com.zhixianghui.web.agent.enums.SmsCodeTypeEnum;
import com.zhixianghui.web.agent.vo.user.AgentInfoVO;
import com.zhixianghui.web.agent.vo.wx.WxLoginResVo;
import com.zhixianghui.web.agent.vo.wx.WxLoginTokenVo;
import com.zhixianghui.web.agent.vo.wx.WxPhoneQueryReqVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/wxmini/biz")
public class WxMiniController {
    @Autowired
    public FastdfsClient fastdfsClient;
    @Reference
    private AgentBussinessEcardFacade agentBussinessEcardFacade;
    @Reference
    private WechatUserInfoFacade weChatUserFacade;
    @Autowired
    private JwtHelper jwtHelper;
    @Reference
    private AgentFacade agentFacade;
    @Autowired
    private DataDecryptHelper dataDecryptHelper;
    @Reference
    private AgentStaffFacade agentStaffFacade;
    @Reference
    private AgentOperatorFacade agentOperatorFacade;
    @Autowired
    private CaptchaHelper captchaHelper;
    @Autowired
    private RedisClient redisClient;
    private final static String WECHAT_LOGIN_KEY_PREFIX = "WX_AGENT_LOGIN_KEY:";

    public static final int AUTH_TIME = 24 * 60 * 60;

    @GetMapping("getEcardByOpenId")
    public RestResult<AgentBussinessEcard> getEcardByOpenId(@RequestParam String openId) {
        AgentBussinessEcard ecard = agentBussinessEcardFacade.getAgentBussinessEcardByOpenId(openId);
        return RestResult.success(ecard);
    }

    @PostMapping("modifyAgentEcard")
    public RestResult<AgentBussinessEcard> modifyAgentEcard(@RequestBody AgentBussinessEcard ecard) {
        if (!ValidateUtil.isMobile(ecard.getEcardMobile())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("手机号格式错误");
        }
        if (!ValidateUtil.isEmail(ecard.getEmail())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("邮箱格式错误");
        }
        if (ecard.getEcardName() != null && ecard.getEcardName().length() > 30) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("姓名过长");
        }
        if (ecard.getCompany() != null && ecard.getCompany().length() > 90) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("公司名过长");
        }
        if (ecard.getPosition() != null && ecard.getPosition().length() > 30) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("职位名过长");
        }
        if (ecard.getWechatId() != null && ecard.getWechatId().length() > 90) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("微信号过长");
        }
        if (ecard.getAddress() != null && ecard.getAddress().length() > 100) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("地址过长");
        }

        agentBussinessEcardFacade.modifyAgentEcard(ecard);
        return RestResult.success(ecard);
    }

    @PostMapping("loginAgentSystem")
    public RestResult<WxLoginResVo> loginAgentSystem(@RequestBody @Validated WxPhoneQueryReqVo wxPhoneQueryReqVo) {
        String phone = weChatUserFacade.getPhoneByOpenIdAndAppId(wxPhoneQueryReqVo.getOpenId(), WxConfigConstat.APP_ID);
        if (phone == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("您暂未成为我们的合伙人，如想成为合伙人，请联系客服");
        }
        AgentOperator operator = agentOperatorFacade.getByPhone(phone);
        if (operator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("您暂未成为我们的合伙人，如想成为合伙人，请联系客服");
        }

        WxLoginResVo wxLoginResVo = new WxLoginResVo();
        wxLoginResVo.setAgentOperatorVO(AgentOperatorVO.build(operator));
        wxLoginResVo.setAgentNos(getAgentInfoListByPhone(phone));
        String token = jwtHelper.genWxToken(phone);
        wxLoginResVo.setToken(token);
        WxLoginTokenVo wxLoginTokenVo = new WxLoginTokenVo();
        wxLoginTokenVo.setAgentOperatorVO(wxLoginResVo.getAgentOperatorVO());
        redisClient.set(WECHAT_LOGIN_KEY_PREFIX + MD5Util.getMD5Hex(token), JsonUtil.toString(wxLoginTokenVo), AUTH_TIME);

        return RestResult.success(wxLoginResVo);
    }

    public List<AgentInfoVO> getAgentInfoListByPhone(String phone) {
        List<String> agentNoList = agentStaffFacade.listByPhone(phone).stream()
                .map(AgentStaffVO::getAgentNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(agentNoList)) {
            return new ArrayList<>();
        }
        // 查询操作员关联的商户
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agentNoList", agentNoList);
        List<Agent> agents = agentFacade.listBy(paramMap);

        List<AgentInfoVO> agentInfoVOS = agents.stream().filter(
                agent -> agent.getAgentStatus() != AgentStatusEnum.RETREAT.getValue()
        ).map(agent -> {
            AgentInfoVO agentInfoVO = new AgentInfoVO();
            agentInfoVO.setAgentNo(agent.getAgentNo());
            agentInfoVO.setAgentName(agent.getAgentName());
            agentInfoVO.setAgentStatus(agent.getAgentStatus());
            agentInfoVO.setAuthStatus(agent.getAuthStatus());
            agentInfoVO.setContactPhone(agent.getContactPhone());
            agentInfoVO.setAgentType(agent.getAgentType());
            return agentInfoVO;
        }).collect(Collectors.toList());
        return agentInfoVOS;
    }

    @PostMapping("getPhoneByOpenId")
    public RestResult<String> getPhoneByOpenId(@RequestBody @Validated WxPhoneQueryReqVo wxPhoneQueryReqVo) {
        String phone = weChatUserFacade.getPhoneByOpenIdAndAppId(wxPhoneQueryReqVo.getOpenId(), WxConfigConstat.APP_ID);
        return RestResult.success(phone);
    }

    @PostMapping("bindMobile")
    public RestResult<WechatUserInfo> bindMobile(@RequestBody @Validated WxBindMobileReqVo wxBindMobileReqVo) {
        captchaHelper.verifySmsCode(wxBindMobileReqVo.getMobile(), wxBindMobileReqVo.getSmsCode(), SmsCodeTypeEnum.REGISTER.getKey() + wxBindMobileReqVo.getMobile());
        captchaHelper.invalidSmsCode(SmsCodeTypeEnum.REGISTER.getValue() + wxBindMobileReqVo.getMobile(), wxBindMobileReqVo.getMobile());
        wxBindMobileReqVo.setAppId(WxConfigConstat.APP_ID);

        WechatUserInfo wechatUserInfo = weChatUserFacade.register(wxBindMobileReqVo);
        return RestResult.success(wechatUserInfo);
    }

    @PostMapping("getMiniUserByOpenIdAndAppId")
    public RestResult<WechatUserInfo> getMiniUserByOpenIdAndAppId(@RequestBody @Validated WxPhoneQueryReqVo wxPhoneQueryReqVo) {
        WechatUserInfo wechatInfo = weChatUserFacade.getMiniUserByOpenIdAndAppId(wxPhoneQueryReqVo.getOpenId(), WxConfigConstat.APP_ID);
        return RestResult.success(wechatInfo);
    }

    @PostMapping("upload")
    public RestResult<String> upload(@RequestParam("file") MultipartFile multiPartfile) throws IOException {
        if(multiPartfile == null){
            return RestResult.error("文件不能为空");
        }

        // 上传
        String fileUrl = fastdfsClient.uploadFile(multiPartfile.getBytes(), multiPartfile.getOriginalFilename());
        return RestResult.success(fileUrl);
    }
}
