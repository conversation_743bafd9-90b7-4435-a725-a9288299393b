package com.zhixianghui.web.agent.controller.config;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.config.IndustryType;
import com.zhixianghui.facade.common.service.IndustryTypeFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行业类型配置
 * <AUTHOR>
 * @date 2020/8/10
 **/
@RestController
@RequestMapping("industryType")
public class IndustryTypeController {
    @Reference
    private IndustryTypeFacade industryTypeFacade;

    /**
     * 查询所有子行业类型
     * @return
     */
    @GetMapping("listAll")
    public RestResult<List<IndustryType>> listAll() {
        List<IndustryType> categoryList = industryTypeFacade.listAll();
        return RestResult.success(categoryList);
    }

}
