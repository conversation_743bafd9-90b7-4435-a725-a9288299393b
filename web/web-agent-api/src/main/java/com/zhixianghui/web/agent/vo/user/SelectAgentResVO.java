package com.zhixianghui.web.agent.vo.user;

import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.vo.permission.FunctionVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class SelectAgentResVO {

    /**
     * 员工信息
     */
    @NotNull
    private AgentStaffVO staff;

    /**
     * 关联的功能菜单
     */
    @NotNull
    private List<FunctionVO> functions;
}
