package com.zhixianghui.web.agent.controller.flow;

import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.service.agent.AgentRoleFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/5 17:29
 */
public class CommonFlow {

    private static final String CRM_FLAG = "CRM观察员";

    public boolean crmRole(String agentNo, AgentRoleFacade agentRoleFacade) {
        List<AgentRole> roleList = agentRoleFacade.listAll(agentNo);
        if (CollectionUtils.isEmpty(roleList)) {
            return false;
        }
        for (AgentRole agentRole : roleList) {
            if (CRM_FLAG.equals(agentRole.getName())) {
                return true;
            }
        }
        return false;
    }

    public boolean admin(AgentStaffVO staffVO, AgentRoleFacade permissionFacade) {
        if (staffVO.getType() == PortalStaffTypeEnum.ADMIN.getValue()) {
            return true;
        }
        return crmRole(staffVO.getAgentNo(), permissionFacade);
    }
}
