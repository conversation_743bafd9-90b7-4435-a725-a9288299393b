package com.zhixianghui.web.agent.vo.agent.res;

import com.zhixianghui.facade.merchant.entity.Merchant;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 商户关系的商户Vo
 * @date 2021/2/5 10:19
 **/
@Data
public class MerchantResVo {
    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 创建时间
     */
    protected Date createTime;

    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 邀请关系
     */
    private Integer relationType;

    /**
     * 商户状态
     */
    protected Integer mchStatus;


    public static MerchantResVo toVo(Merchant merchant){
        MerchantResVo merchantResVo = new MerchantResVo();
        BeanUtils.copyProperties(merchant,merchantResVo);
        return merchantResVo;
    }
}
