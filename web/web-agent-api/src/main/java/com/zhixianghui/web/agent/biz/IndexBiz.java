package com.zhixianghui.web.agent.biz;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.notification.NotificationReceiverTypeEnum;
import com.zhixianghui.common.statics.enums.notification.PublishStatusEnum;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.common.service.NotificationFacade;
import com.zhixianghui.facade.common.vo.NotificationDetailFullInfo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class IndexBiz {
    @Reference
    private NotificationFacade notificationFacade;

    public Map<String,Object> getNotifyList(String agentNo) {
        Page page = new Page<>();
        page.setCurrent(1L);
        page.setSize(10L);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("sortColumns","b.PUBLISH_TIME desc");
        paramMap.put("mchNo",agentNo);
        paramMap.put("publishStatus", PublishStatusEnum.PUBLISHED.getCode());
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_AGENT.getCode(),NotificationReceiverTypeEnum.SELECTED_AGENT.getCode()});
        IPage pageData = notificationFacade.listNotificationRecordFullInfo(page,paramMap);
        List<NotificationDetailFullInfo> list = pageData.getRecords();
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("array",list.stream().map(x->{
            NotificationRecordDetail detail = x.getNotificationRecordDetail();
            return new HashMap<String,Object>(){
                private static final long serialVersionUID = 516683242197986207L;

                {put("id",detail.getNotificationId());
                    put("notificationTitle",detail.getNotificationTitle());
                    put("pop", x.getNotificationRecord().isPop());
                    put("readStatus", detail.getReadStatus());
                    put("publishTime",x.getNotificationRecord().getPublishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));}};
        }).collect(Collectors.toList()));

        paramMap.put("readStatus", YesNoCodeEnum.NO.getValue());
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_AGENT.getCode(),NotificationReceiverTypeEnum.SELECTED_AGENT.getCode()});
        IPage readData = notificationFacade.listNotificationRecordFullInfo(page,paramMap);
        resultMap.put("count",readData.getTotal());
        return resultMap;
    }
}
