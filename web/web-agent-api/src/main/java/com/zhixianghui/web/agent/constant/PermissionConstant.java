package com.zhixianghui.web.agent.constant;


/**
 * 与权限相关的常量
 *
 * <AUTHOR> <PERSON>
 */
public class PermissionConstant {

    public static final String REQUEST_TOKEN_HEADER = "x-token";

    /**
     * session过期时间/秒
     */
    public static final int SESSION_EXPIRE_TIME = 3600;

    /**
     * 支付密码连续输错次数限制(默认5).
     */
    public static final int TRADE_PWD_ERROR_LIMIT = 5;

    /**
     * 操作员密码连续输错次数限制(默认5).
     */
    public static final int LOGIN_PWD_ERROR_LIMIT = 5;

    /**
     * 图形验证码key
     */
    public static final String CAPTCHA_KEY = "CAPTCHA_KEY";

    /**
     * 图形验证码过期时间
     */
    public static final int CAPTCHA_EXPIRE_TIME = 300;

    /**
     * 短信验证码key
     */
    public static final String SMS_CODE_KEY = "SMS_CODE_KEY";

    /**
     * 校验短信验证码key
     */
    public static final String VERIFY_SMS_CODE_KEY = "VERIFY_SMS_CODE_KEY";

    /**
     * 短信验证码重发时间
     */
    public static final int SMS_CODE_RESEND_TIME = 60;

    /**
     * 短信验证码过期时间
     */
    public static final int SMS_CODE_EXPIRE_TIME = 300;

    /**
     * 邮箱验证码key
     */
    public static final String EMAIL_CODE_KEY = "EMAIL_CODE_KEY";

    /**
     * 校验邮箱验证码key
     */
    public static final String VERIFY_EMAIL_CODE_KEY = "VERIFY_EMAIL_CODE_KEY";

    /**
     * 邮箱验证码重发时间
     */
    public static final int EMAIL_CODE_RESEND_TIME = 60;

    /**
     * 邮箱验证码过期时间
     */
    public static final int EMAIL_CODE_EXPIRE_TIME = 300;

    /**
     * 登录操作员姓名的session键名
     */
    public static final String UUID_SESSION_KEY = "UUID";

    /**
     * 操作员所选择的合伙人编号session键名
     */
    public static final String AGENT_NO_SESSION_KEY = "CURRENT_AGENT_NO";
    public static final String AGENT_TYPE_SESSION_KEY = "CURRENT_AGENT_TYPE";

    /**
     * 登录操作员的session键名.
     */
    public static final String OPERATOR_SESSION_KEY = "CURRENT_OPERATOR";

    /**
     * 登录操作员的session的私钥键名
     */
    public static final String OPERATOR_SESSION_PRIVATEKEY_KEY = "OPERATOR_SESSION_PRIVATEKEY_KEY";

    /**
     * 登录员工的session键名.
     */
    public static final String STAFF_SESSION_KEY = "CURRENT_STAFF";

    /**
     * 登录操作员拥有的权限集合的session键名.
     */
    public static final String PERMISSION_SESSION_KEY = "CURRENT_PERMISSIONS";

    /**
     * 修改支付密码：发送手机验证码key
     */
    public static final String CHANGE_TRADE_PWD_SMS_CODE_KEY = "CHANGE_TRADE_PWD_SMS_CODE_KEY";

    /**
     * 更换负责人：发送手机验证码key
     */
    public static final String CHANGE_LEADER_SMS_CODE_KEY = "CHANGE_LEADER_SMS_CODE_KEY";

    /**
     * 更换负责人：发送邮箱验证码key
     */
    public static final String CHANGE_LEADER_EMAIL_CODE_KEY = "CHANGE_LEADER_EMAIL_CODE_KEY";

    /**
     * 用户动态RSA对的临存key
     */
    public static final String USER_DYNAMIC_RSA_KEY = "USER_DYNAMIC_RSA_KEY";

    /**
     * 用户动态RSA对缓存时间
     */
    public static final Integer USER_DYNAMIC_RSA_EXPIRE_TIME = 120;

    /**
     * 最大历史密码存储数
     */
    public static final Integer MAX_HISTORY_PWD_NUM = 4;
}
