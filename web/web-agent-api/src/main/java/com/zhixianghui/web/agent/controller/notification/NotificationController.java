package com.zhixianghui.web.agent.controller.notification;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.notification.NotificationReceiverTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.common.service.NotificationFacade;
import com.zhixianghui.facade.common.vo.NotificationDetailFullInfo;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.dto.NotificationDetailQueryDto;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("notification")
public class NotificationController {

    @Reference
    private NotificationFacade notificationFacade;

    @PostMapping("listNotificationRecordFullInfo")
    public RestResult<IPage<NotificationDetailFullInfo>> listNotificationRecordFullInfo(@RequestBody Page<Map<String, Object>> page, @RequestBody NotificationDetailQueryDto dto, @CurrentStaffVo AgentStaffVO staffVO) {
        final Map<String, Object> paramMap = BeanUtil.toMap(dto);
        paramMap.put("mchNo", staffVO.getAgentNo());
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_AGENT.getCode(),NotificationReceiverTypeEnum.SELECTED_AGENT.getCode()});
        final IPage pageData = notificationFacade.listNotificationRecordFullInfo(page, paramMap);
        return RestResult.success(pageData);
    }

    @PostMapping("updateNotificationDetailReadStatus")
    public RestResult<String> updateNotificationDetailReadStatus(@RequestParam List<Long> ids, @RequestParam Integer status, @CurrentStaffVo AgentStaffVO staffVO) {
        for (Long id : ids) {
            final NotificationRecordDetail notificationRecordDetailById = this.notificationFacade.getNotificationRecordDetailById(id);
            if (notificationRecordDetailById != null) {
                final LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
                notificationRecordDetailById.setReadStatus(status);
                notificationRecordDetailById.setUpdateTime(now);
                notificationRecordDetailById.setReadOperatorId(staffVO.getId());
                notificationRecordDetailById.setReadOperatorName(staffVO.getOperatorName());
                notificationRecordDetailById.setReadTime(now);

                this.notificationFacade.updateNotificationDetailReadStatus(notificationRecordDetailById);
            }
        }

        return RestResult.success("更新成功");
    }

    @GetMapping("notificationInfoById")
    public RestResult<NotificationRecord> getNotificationInfoById(@RequestParam Long id) {
        return RestResult.success(this.notificationFacade.getNotificationInfoById(id));
    }

}
