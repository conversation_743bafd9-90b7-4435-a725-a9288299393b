package com.zhixianghui.web.agent.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentStaffTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentAgentNo;
import com.zhixianghui.web.agent.annotation.CurrentSession;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.annotation.Logger;
import com.zhixianghui.web.agent.component.DataDecryptHelper;
import com.zhixianghui.web.agent.component.TradePwdHelper;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.dto.Session;
import com.zhixianghui.web.agent.vo.permission.AddStaffVO;
import com.zhixianghui.web.agent.vo.permission.EditStaffVO;
import com.zhixianghui.web.agent.vo.permission.StaffQueryVO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 员工管理
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("staff")
public class StaffController {

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private AgentStaffFacade agentStaffFacade;

    @Autowired
    private TradePwdHelper tradePwdHelper;

    @Autowired
    private DataDecryptHelper dataDecryptHelper;

    /**
     * 员工分页查询
     * @param staffQueryVO
     * @param agentNo
     * @return
     */
    @Permission("pms:staff:view")
    @PostMapping("listPage")
    public RestResult<Object> listPage(@RequestBody StaffQueryVO staffQueryVO,
                                       @CurrentAgentNo String agentNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("phone", staffQueryVO.getPhone());
        paramMap.put("name", staffQueryVO.getName());
        paramMap.put("nameLike", staffQueryVO.getNameLike());
        paramMap.put("roleIds", staffQueryVO.getRoleIds());
        if (staffQueryVO.getRoleIds() != null){
            paramMap.put("idNum", staffQueryVO.getRoleIds().size());
        }
        return RestResult.success(agentStaffFacade.listPage(agentNo, paramMap, PageParam.newInstance(staffQueryVO.getPageCurrent(), staffQueryVO.getPageSize())));
    }

    /**
     * 新增员工
     * @param addStaffVO
     */
    @Permission("pms:staff:add")
    @PostMapping("add")
    @Logger(type = OperateLogTypeEnum.CREATE, action = "新增员工")
    public RestResult<String> add(@RequestBody @Valid AddStaffVO addStaffVO,
                                  @CurrentStaffVo AgentStaffVO currentStaffVO,
                                  @CurrentAgentNo String agentNo,
                                  @CurrentSession Session session) {
        // 密码解密
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        addStaffVO.setTradePwd(dataDecryptHelper.decryptData(privateKey, addStaffVO.getTradePwd()));

        tradePwdHelper.verifyTradePwd(currentStaffVO, agentNo, addStaffVO.getTradePwd());

        AgentStaffVO staffVO = new AgentStaffVO();
        staffVO.setCreator(currentStaffVO.getOperatorName());
        staffVO.setType(AgentStaffTypeEnum.USER.getValue());
        staffVO.setAgentNo(agentNo);
        staffVO.setAgentName(currentStaffVO.getAgentName());
        staffVO.setPhone(addStaffVO.getPhone());
        staffVO.setCreateTime(new Date());
        staffVO.setName(addStaffVO.getName());
        staffVO.setOperatorName(currentStaffVO.getOperatorName());
        agentStaffFacade.createAndAssignRole(staffVO, addStaffVO.getRoleIds());
        return RestResult.success("添加员工成功");
    }

    /**
     * 根据员工id查询员工信息
     * @param id        员工id
     */
    @Permission("pms:staff:add")
    @GetMapping("getById")
    public RestResult<Map<String, Object>> getById(@RequestParam Long id, @CurrentAgentNo String agentNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("staff", agentStaffFacade.getById(agentNo, id));
        map.put("roleIds", agentStaffFacade.getRoleByStaffId(agentNo, id).stream().map(AgentRole::getId).collect(Collectors.toList()));
        return RestResult.success(map);
    }


    /**
     * 修改员工信息
     */
    @Permission("pms:staff:edit")
    @PostMapping("edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "修改员工信息")
    public RestResult<String> edit(@RequestBody @Valid EditStaffVO editStaffVO,
                                   @CurrentStaffVo AgentStaffVO staffVO,
                                   @CurrentAgentNo String agentNo,
                                   @CurrentSession Session session) {
        // 密码解密
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        editStaffVO.setTradePwd(dataDecryptHelper.decryptData(privateKey, editStaffVO.getTradePwd()));

        tradePwdHelper.verifyTradePwd(staffVO, agentNo, editStaffVO.getTradePwd());

        agentStaffFacade.updateRole(agentNo, editStaffVO.getId(), editStaffVO.getRoleIds(),editStaffVO.getName());
        return RestResult.success("操作成功");
    }

    /**
     * 删除员工
     * @param id    员工id
     */
    @Permission("pms:staff:delete")
    @PostMapping("delete")
    @Logger(type = OperateLogTypeEnum.DELETE, action = "删除员工")
    public RestResult<String> delete(@RequestParam Long id,
                                     @RequestParam String tradePwd,
                                     @CurrentStaffVo AgentStaffVO staffVO,
                                     @CurrentAgentNo String agentNo,
                                     @CurrentSession Session session) {
//        FlowUserVo flowUserVo = new FlowUserVo();
//        flowUserVo.setUserId(staffVO.getId());
//        flowUserVo.setPlatform(PlatformSource.AGENT.getValue());
//        flowUserVo.setNo(agentNo);
//        if (flowFacade.isExistTask(flowUserVo)){
//            return RestResult.error("当前员工存在未处理待办，无法删除");
//        }
        // 密码解密
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        tradePwd = dataDecryptHelper.decryptData(privateKey, tradePwd);

        tradePwdHelper.verifyTradePwd(staffVO, agentNo, tradePwd);

        agentStaffFacade.deleteById(agentNo, id);
        return RestResult.success("删除成功");
    }
}
