package com.zhixianghui.web.agent.config;

import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.web.agent.component.JwtHelper;
import com.zhixianghui.web.agent.component.SessionManager;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.dto.JwtInfo;
import com.zhixianghui.web.agent.dto.Session;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 权限拦截器
 *
 * <AUTHOR> Guangsheng
 */
@Component
@Log4j2
public class PermissionInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private SessionManager sessionManager;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (Objects.equals(request.getMethod(), HttpMethod.OPTIONS.name())) {
            return false;
        }

        // 解析token，获取session
        JwtInfo jwtInfo;
        Session session;
        try {
            jwtInfo = jwtHelper.verifyToken(request);
            session = sessionManager.getSession(jwtInfo.getAgentOperatorVO().getPhone(), jwtInfo.getUuid());
            if (session == null) {
                writeDenyResult(response, RestResult.unAuth("token无效"));
                return false;
            } else {
                // 更新有效期时间
                session.refreshExpire();
            }
        } catch (Exception e) {
            log.error("token校验异常：", e);
            writeDenyResult(response, RestResult.unAuth("token无效"));
            return false;
        }
        // 存入request
        request.setAttribute("jwtInfo", jwtInfo);
        request.setAttribute("session", session);

        log.debug("当前操作员: {}", jwtInfo.getAgentOperatorVO().getName());

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        Method invocation = ((HandlerMethod) handler).getMethod();
        // 判断该方法是否加了@Permission注解
        if (!invocation.isAnnotationPresent(Permission.class)) {
            return true;
        }

        // 得到方法上的Permission注解值
        final Permission permission = invocation.getAnnotation(Permission.class);
        log.info("Invoke PermissionInterceptor. Permission: {}", permission.value());

        //用户权限列表
        List<String> mchNoAndPermission = session.getAttributes(PermissionConstant.AGENT_NO_SESSION_KEY, PermissionConstant.PERMISSION_SESSION_KEY);
        if (mchNoAndPermission.size() < 2 || StringUtil.isEmpty(mchNoAndPermission.get(0))) {
            log.info("Permission Denied: [{}], Operator: [{}]", permission.value(), jwtInfo.getAgentOperatorVO().getName());
            writeDenyResult(response, RestResult.noSelectMch("请先选择商户"));
            return false;
        }
        String mchNo = mchNoAndPermission.get(0);
        List<String> permissions = JsonUtil.toBean(mchNoAndPermission.get(1), new TypeReference<List<String>>() {});
        List<String> requiredPermissions = Arrays.asList(permission.value());
        if (permissions != null && CollectionUtils.containsAny(permissions,requiredPermissions)) { // 拥有此功能点权限
            // 执行被拦截的方法，如果此方法不调用，则被拦截的方法不会被执行
            log.info("Permission Accessed: [{}], Operator: [{}]", permission.value(), jwtInfo.getAgentOperatorVO().getName());
            return true;
        } else {
            // 没有此功能权限
            log.info("Permission Denied: [{}], Operator: [{}]", permission.value(), jwtInfo.getAgentOperatorVO().getName());
            writeDenyResult(response, RestResult.deny("无权限"));
            return false;
        }
    }

    private void writeDenyResult(HttpServletResponse response, RestResult restResult) throws Exception {
        response.getWriter().write(JsonUtil.toString(restResult));
        response.setContentType("application/json;charset=utf-8");
        response.flushBuffer();
    }
}
