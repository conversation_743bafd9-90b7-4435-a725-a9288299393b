package com.zhixianghui.web.agent.component;

import com.zhixianghui.common.statics.enums.user.agent.AgentInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentOperatorStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentTradePwd;
import com.zhixianghui.facade.merchant.service.agent.AgentTradePwdFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.utils.PwdDigestUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 商户支付密码
 *
 * <AUTHOR> <PERSON>
 */
@Component
@Log4j2
public class TradePwdHelper {

    @Reference
    private AgentTradePwdFacade agentTradePwdFacade;

    /**
     * 查询商户是否设置了支付密码
     * @param agentNo
     * @return
     */
    public boolean isInitTradePwd(String agentNo) {
        AgentTradePwd agentTradePwd = agentTradePwdFacade.getByAgentNo(agentNo);
        return agentTradePwd != null && agentTradePwd.getIsInitPwd() == AgentInitPwdStatusEnum.INITED.getValue();
    }

    /**
     * 修改支付密码
     */
    public void changeTradePwd(String agentNo, String tradePwd) {
        // 密码复杂度验证
        if(!ValidateUtil.isInteger(tradePwd) ||  tradePwd.length() != 6){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("支付密码必须为6位数字");
        }

        // 密码摘要
        PwdDigestUtil.DigestResult digestResult = PwdDigestUtil.digestFixSaltPwd(tradePwd);

        AgentTradePwd agentTradePwd = agentTradePwdFacade.getByAgentNo(agentNo);
        if (agentTradePwd == null) {  // 没有支付密码记录则直接创建
            agentTradePwd = new AgentTradePwd();
            agentTradePwd.setCreateTime(new Date());
            agentTradePwd.setAgentNo(agentNo);
            agentTradePwd.setStatus(AgentOperatorStatusEnum.ACTIVE.getValue());
            agentTradePwd.setIsInitPwd(AgentInitPwdStatusEnum.INITED.getValue());
            agentTradePwd.setPwd(digestResult.getPwd());
            agentTradePwd.setPwdErrorCount(0);
            agentTradePwd.setPwdErrorTime(null);
            agentTradePwd.getJsonEntity().getHistoryPwdList().add(digestResult.getPwd());
            agentTradePwdFacade.create(agentTradePwd);
        } else {
            agentTradePwd.setUpdateTime(new Date());
            agentTradePwd.setStatus(AgentOperatorStatusEnum.ACTIVE.getValue());
            agentTradePwd.setIsInitPwd(AgentInitPwdStatusEnum.INITED.getValue());
            agentTradePwd.setPwd(digestResult.getPwd());

            // 不能与历史密码重复
            List<String> historyPwdList = agentTradePwd.getJsonEntity().getHistoryPwdList();
            if(historyPwdList.contains(digestResult.getPwd())){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("密码不能与前4次重复");
            }

            // 只存储最近4次密码密文
            if(historyPwdList.size() >= PermissionConstant.MAX_HISTORY_PWD_NUM){
                historyPwdList.add(digestResult.getPwd());
                agentTradePwd.getJsonEntity().setHistoryPwdList(historyPwdList.subList(historyPwdList.size() - PermissionConstant.MAX_HISTORY_PWD_NUM, historyPwdList.size()));
            } else {
                agentTradePwd.getJsonEntity().getHistoryPwdList().add(digestResult.getPwd());
            }

            agentTradePwd.setPwdErrorCount(0);
            agentTradePwdFacade.update(agentTradePwd);
        }
    }

    /**
     * 验证支付密码
     */
    public void verifyTradePwd(AgentStaffVO staffVO, String agentNo, String tradePwd) throws BizException {
        AgentTradePwd agentTradePwd = agentTradePwdFacade.getByAgentNo(agentNo);
        if (agentTradePwd == null || agentTradePwd.getIsInitPwd() == AgentInitPwdStatusEnum.NO_INIT.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请先设置支付密码");
        }
        if (agentTradePwd.getStatus() == AgentOperatorStatusEnum.INACTIVE.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付密码已被冻结，请先修改");
        }

        // 验证支付密码
        if (PwdDigestUtil.verifyFixSaltPwd(agentTradePwd.getPwd(), tradePwd)) {
            // 重置错误次数
            agentTradePwd.setPwdErrorCount(0);
            agentTradePwdFacade.update(agentTradePwd);
        } else {
            // 密码错误
            log.warn("支付密码输入错误，操作员：{}-{}，商户：{}", staffVO.getPhone(), staffVO.getName(), agentNo);
            // 错误次数加1
            agentTradePwd.setPwdErrorCount(agentTradePwd.getPwdErrorCount() + 1);
            agentTradePwd.setPwdErrorTime(new Date());
            // 超过密码输错次数限制，冻结
            String errMsg;
            if (agentTradePwd.getPwdErrorCount() >= PermissionConstant.TRADE_PWD_ERROR_LIMIT) {
                agentTradePwd.setStatus(AgentOperatorStatusEnum.INACTIVE.getValue());
                errMsg = "支付密码已连续输错【" + PermissionConstant.TRADE_PWD_ERROR_LIMIT + "】次，已被冻结";
            } else {
                errMsg = "支付密码错误，剩余【" + (PermissionConstant.TRADE_PWD_ERROR_LIMIT - agentTradePwd.getPwdErrorCount()) + "】次机会";
            }
            agentTradePwdFacade.update(agentTradePwd);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(errMsg);
        }
    }
}
