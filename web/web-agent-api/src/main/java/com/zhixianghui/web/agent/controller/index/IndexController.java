package com.zhixianghui.web.agent.controller.index;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.biz.IndexBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName IndexController
 * @Description TODO
 * @Date 2022/4/29 10:15
 */
@RestController
@RequestMapping("index")
public class IndexController {

    @Autowired
    private IndexBiz indexBiz;


    @GetMapping("notifyList")
    public RestResult notifyList(@CurrentStaffVo AgentStaffVO agentStaffVO){
        final Map<String, Object> notifyList = indexBiz.getNotifyList(agentStaffVO.getAgentNo());
        return RestResult.success(notifyList);
    }

}
