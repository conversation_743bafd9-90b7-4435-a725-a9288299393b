package com.zhixianghui.web.agent.dto;

import com.alibaba.fastjson.TypeReference;
import com.zhixianghui.common.statics.enums.common.RedisKeyPrefixEnum;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.agent.constant.PermissionConstant;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public class Session {

    private final RedisClient redisClient;

    private String phone;

    private String uuid;

    public Session(RedisClient redisClient, String phone) {
        this.redisClient = redisClient;
        this.phone = phone;
        this.uuid = UUID.randomUUID().toString();
    }

    public static Session getByPhone(RedisClient redisClient, String phone) {
        String uuid = redisClient.hget(getSessionKey(phone), PermissionConstant.UUID_SESSION_KEY);
        if (StringUtil.isEmpty(uuid)) {
            return null;
        }
        Session session = new Session(redisClient, phone);
        session.uuid = uuid;
        return session;
    }

    public static void deleteByPhone(RedisClient redisClient, String phone) {
        redisClient.del(Session.getSessionKey(phone));

    }

    public void save() {
        deleteByPhone(redisClient, phone);
        setAttribute(PermissionConstant.UUID_SESSION_KEY, uuid);
        refreshExpire();
    }

    /**
     * 刷新有效时间
     */
    public void refreshExpire(){

        redisClient.expire(getSessionKey(phone), PermissionConstant.SESSION_EXPIRE_TIME);
    }

    public <T> T getAttribute(String attributeName, Class<T> clazz) {
        String value = redisClient.hget(getSessionKey(phone), attributeName);
        if (clazz == String.class) {
            return (T) value;
        } else if (StringUtil.isEmpty(value)) {
            return null;
        }
        return JsonUtil.toBean(value, clazz);
    }

    public <T> T getAttribute(String attributeName, TypeReference<T> typeReference) {
        String value = redisClient.hget(getSessionKey(phone), attributeName);
        if (typeReference.getType() == String.class) {
            return (T) value;
        } else if (StringUtil.isEmpty(value)) {
            return null;
        }
        return JsonUtil.toBean(value, typeReference);
    }

    public <T> T getAttributeOrDefault(String attributeName, Class<T> clazz, T defaultValue) {
        T value = getAttribute(attributeName, clazz);
        if (value == null) {
            return defaultValue;
        }
        return value;
    }

    public List<String> getAttributes(String... attributeNames) {
        return redisClient.hmget(getSessionKey(phone), attributeNames);
    }

    public Map<String, String> getAllAttribute() {
        return redisClient.hgetAll(getSessionKey(phone));
    }

    public Set<String> getAttributeNames() {
        return redisClient.hkeys(getSessionKey(phone));
    }

    public void setAttribute(String attributeName, Object attributeValue) {
        if (attributeValue instanceof String) {
            redisClient.hset(getSessionKey(phone), attributeName, (String) attributeValue);
        } else {
            redisClient.hset(getSessionKey(phone), attributeName, JsonUtil.toString(attributeValue));
        }
    }

    public void setAttribute(Map<String, String> map) {
        redisClient.hset(getSessionKey(phone), map);
    }

    public void removeAttribute(String attributeName) {
        redisClient.hdel(getSessionKey(phone), attributeName);
    }

    public String getPhone() {
        return phone;
    }

    public String getUuid() {
        return uuid;
    }

    public static String getSessionKey(String phone) {
        return RedisKeyPrefixEnum.WEB_AGENT_SESSION_KEY.name() + ":" + phone;
    }
}
