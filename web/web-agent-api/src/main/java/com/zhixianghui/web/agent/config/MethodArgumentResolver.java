package com.zhixianghui.web.agent.config;

import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentAgentNo;
import com.zhixianghui.web.agent.annotation.CurrentOperatorVO;
import com.zhixianghui.web.agent.annotation.CurrentSession;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.component.JwtHelper;
import com.zhixianghui.web.agent.component.SessionManager;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.dto.JwtInfo;
import com.zhixianghui.web.agent.dto.Session;
import com.zhixianghui.web.agent.exception.NoSelectAgentException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

/**
 *
 *
 * <AUTHOR> Guangsheng
 */
@Component
public class MethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private SessionManager sessionManager;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return (parameter.getParameterType().isAssignableFrom(AgentOperatorVO.class) && parameter.hasParameterAnnotation(CurrentOperatorVO.class))
                || (parameter.getParameterType().isAssignableFrom(AgentStaffVO.class) && parameter.hasParameterAnnotation(CurrentStaffVo.class))
                || (parameter.getParameterType().isAssignableFrom(String.class) && parameter.hasParameterAnnotation(CurrentAgentNo.class))
                || (parameter.getParameterType().isAssignableFrom(Session.class) && parameter.hasParameterAnnotation(CurrentSession.class));
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        HttpServletRequest request = (HttpServletRequest) (webRequest.getNativeRequest());
        JwtInfo jwtInfo = (JwtInfo) request.getAttribute("jwtInfo");
        Session session = (Session) request.getAttribute("session");
        if (jwtInfo == null || session == null) {
            return null;
        }

        if (parameter.getParameterType().isAssignableFrom(Session.class) && parameter.hasParameterAnnotation(CurrentSession.class)) {
            return session;
        } else if (parameter.getParameterType().isAssignableFrom(AgentOperatorVO.class) && parameter.hasParameterAnnotation(CurrentOperatorVO.class)) {
            return session.getAttribute(PermissionConstant.OPERATOR_SESSION_KEY, AgentOperatorVO.class);
        } else if (parameter.getParameterType().isAssignableFrom(AgentStaffVO.class) && parameter.hasParameterAnnotation(CurrentStaffVo.class)) {
            AgentStaffVO staffVO = session.getAttribute(PermissionConstant.STAFF_SESSION_KEY, AgentStaffVO.class);
            if (staffVO == null) {
                throw new NoSelectAgentException();
            }
            return staffVO;
        } else if (parameter.getParameterType().isAssignableFrom(String.class) && parameter.hasParameterAnnotation(CurrentAgentNo.class)) {
            String agentNo = session.getAttribute(PermissionConstant.AGENT_NO_SESSION_KEY, String.class);
            if (agentNo == null) {
                throw new NoSelectAgentException();
            }
            return agentNo;
        }
        return null;
    }
}
