package com.zhixianghui.web.agent.vo.fee;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class AgentFeeSumQueryVo extends PageQueryVo {

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentNameLike;

    /**
     * 查询时间类型
     */
    @EnumValue(intValues = {1,2,3}, message = "查询字段类型有误")
    @NotNull(message = "查询时段不能为空")
    private Integer queryTimeType;

    /**
     * 排序列
     */
    @EnumValue(strValues = {"totalNetAmount", "totalProfit", "totalTradeProfit"}, message = "不支持该排序列")
    @NotEmpty(message = "排序维度不能为空")
    private String sortColumns;

}