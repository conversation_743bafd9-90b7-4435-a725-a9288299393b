package com.zhixianghui.web.agent.config;

import com.zhixianghui.facade.merchant.entity.user.agent.AgentStaff;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.WxCurrentAgentNo;
import com.zhixianghui.web.agent.annotation.WxCurrentOperatorVo;
import com.zhixianghui.web.agent.annotation.WxCurrentStaffVo;
import com.zhixianghui.web.agent.dto.Session;
import com.zhixianghui.web.agent.exception.NoSelectAgentException;
import com.zhixianghui.web.agent.vo.wx.WxLoginTokenVo;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @ClassName WxMethodArgumentResolver
 * @Description TODO
 * @Date 2023/9/8 10:06
 */
@Component
public class WxMethodArgumentResolver implements HandlerMethodArgumentResolver {
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return (parameter.getParameterType().isAssignableFrom(AgentOperatorVO.class) && parameter.hasParameterAnnotation(WxCurrentOperatorVo.class))
                || (parameter.getParameterType().isAssignableFrom(AgentStaffVO.class) && parameter.hasParameterAnnotation(WxCurrentStaffVo.class))
                || (parameter.getParameterType().isAssignableFrom(String.class) && parameter.hasParameterAnnotation(WxCurrentAgentNo.class));
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        HttpServletRequest request = (HttpServletRequest) (nativeWebRequest.getNativeRequest());
        WxLoginTokenVo wxLoginTokenVo = (WxLoginTokenVo) request.getAttribute("loginInfo");
        if (wxLoginTokenVo == null){
            return null;
        }
        if (parameter.getParameterType().isAssignableFrom(AgentOperatorVO.class) && parameter.hasParameterAnnotation(WxCurrentOperatorVo.class)) {
            return wxLoginTokenVo.getAgentOperatorVO();
        } else if (parameter.getParameterType().isAssignableFrom(AgentStaffVO.class) && parameter.hasParameterAnnotation(WxCurrentStaffVo.class)) {
            AgentStaffVO staffVO = wxLoginTokenVo.getAgentStaffVO();
            if (staffVO == null) {
                throw new NoSelectAgentException();
            }
            return staffVO;
        } else if (parameter.getParameterType().isAssignableFrom(String.class) && parameter.hasParameterAnnotation(WxCurrentAgentNo.class)) {
            String agentNo = wxLoginTokenVo.getAgentStaffVO().getAgentNo();
            if (agentNo == null) {
                throw new NoSelectAgentException();
            }
            return agentNo;
        }
        return null;
    }
}
