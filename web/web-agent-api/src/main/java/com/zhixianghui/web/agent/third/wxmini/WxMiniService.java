package com.zhixianghui.web.agent.third.wxmini;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WxMiniService {
    @Autowired
    private WxMaService service;

    public MiniSessionInfo getWxMiniSession(String jsCode){
        WxMaUserService userService = service.getUserService();
        try {
            WxMaJscode2SessionResult sessionInfo = userService.getSessionInfo(jsCode);

            String openid = sessionInfo.getOpenid();
            String sessionKey = sessionInfo.getSessionKey();
            String unionid = sessionInfo.getUnionid();
            return new MiniSessionInfo(openid, sessionKey, unionid);
        } catch (WxErrorException e) {
            log.error("登录失败", e);
            return null;
        }
    }

    public String getWxMiniAccessToken(){
        try {
            String accessToken = service.getAccessToken();
            return accessToken;
        } catch (WxErrorException e) {
            log.error("获取access_token出错",e);
            return null;
        }
    }

    public WxMaUserInfo getWxUserInfo(String sessionKey, String encryptedData, String ivStr){
        WxMaUserService userService = service.getUserService();
        WxMaUserInfo userInfo = userService.getUserInfo(sessionKey, encryptedData, ivStr);

        return userInfo;
    }

}
