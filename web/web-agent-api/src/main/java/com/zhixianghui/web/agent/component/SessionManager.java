package com.zhixianghui.web.agent.component;

import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.agent.dto.Session;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * Session管理
 *
 * <AUTHOR>
 */
@Component
@Log4j2
public class SessionManager {

    @Autowired
    private RedisClient redisClient;

    public Session createSession(String phone) {
        return new Session(redisClient, phone);
    }

    public void save(Session session) {
        session.save();
    }

    public Session getSession(String phone, String uuid) {
        if (StringUtil.isEmpty(phone)) return null;

        Session session = Session.getByPhone(redisClient, phone);
        Assert.notNull(session, "会话为空");
        if (session.getUuid().equals(uuid))  {
            return session;
        } else {
            return null;
        }
    }

    public void deleteSession(String phone, String uuid) {
        Session session = getSession(phone, uuid);
        if (session != null) {
            Session.deleteByPhone(redisClient, phone);
        }
    }
}
