package com.zhixianghui.web.agent.controller.approval;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentOperatorVO;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.biz.ApprovalFlowBiz;
import com.zhixianghui.web.agent.vo.PageVo;
import com.zhixianghui.web.agent.vo.approval.req.ApprovalFlowQueryVo;
import com.zhixianghui.web.agent.vo.approval.res.ApprovalDetailResVo;
import com.zhixianghui.web.agent.vo.approval.res.ApprovalListResVo;
import com.zhixianghui.web.agent.vo.approval.res.ApprovalResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/3/1 15:54
 **/
@Slf4j
@RestController
@RequestMapping("approval")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApprovalController {
    private final ApprovalFlowBiz approvalFlowBiz;

    /**
     * 发出的审批
     * @param approvalFlowQueryVo 查询参数
     * @param pageVo 分页参数
     * @return 发出的审批列表
     */
    @GetMapping("/listSend")
    public RestResult<PageResult<List<ApprovalListResVo>>> listSendApproval(@Validated ApprovalFlowQueryVo approvalFlowQueryVo, PageVo pageVo, @CurrentStaffVo AgentStaffVO staffVO){
        PageResult<List<ApprovalListResVo>> pageResult = approvalFlowBiz.listSendApproval(approvalFlowQueryVo,pageVo,staffVO);
        return RestResult.success(pageResult);
    }

    /**
     * 获取单个审批的内容
     * @param approvalFlowId 查询参数
     * @return 发出的审批列表
     */
    @GetMapping("/getApproval")
    public RestResult<ApprovalResVo> getSingleApproval(@RequestParam Long approvalFlowId, @CurrentStaffVo AgentStaffVO staffVO){
        ApprovalResVo approvalResVo = approvalFlowBiz.getSingleApproval(approvalFlowId,staffVO);
        return RestResult.success(approvalResVo);
    }

    /**
     * 审批流程各节点详情
     * @param approvalFlowId 审批流程id
     * @return 审批流程各节点处理详情
     */
    @GetMapping("/getDetail")
    public RestResult<List<ApprovalDetailResVo>> getApprovalDetail(@RequestParam Long approvalFlowId, @CurrentStaffVo AgentStaffVO staffVO){
        List<ApprovalDetailResVo> result = approvalFlowBiz.getApprovalDetail(approvalFlowId,staffVO);
        return RestResult.success(result);
    }

    /**
     * 撤回审批流程
     * @param approvalFlowId 审批流程id
     * @return 结果
     */
    @PostMapping("/cancelApproval")
    public RestResult<String> cancelApproval(@RequestParam Long approvalFlowId,@CurrentOperatorVO AgentOperatorVO operatorVO, @CurrentStaffVo AgentStaffVO staffVO){
        approvalFlowBiz.cancelApproval(approvalFlowId,operatorVO,staffVO);
        return RestResult.success("撤回成功");
    }

    /**
     * 更新附加信息
     * @return 操作结果
     */
    @PostMapping("/updateExtInfo")
    public RestResult<String> updateExtInfo(@Validated @RequestBody UpdateExtInfoVo updateExtInfoVo, @CurrentOperatorVO AgentOperatorVO currentOperator){
        approvalFlowBiz.updateExtInfo(updateExtInfoVo, currentOperator);
        return RestResult.success("更新成功");
    }
}
