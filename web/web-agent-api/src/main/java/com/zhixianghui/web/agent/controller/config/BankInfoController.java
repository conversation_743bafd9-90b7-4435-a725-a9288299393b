package com.zhixianghui.web.agent.controller.config;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.config.BankInfo;
import com.zhixianghui.facade.common.service.BankInfoFacade;
import com.zhixianghui.web.agent.vo.PageVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 银行信息
 * @date 2020-08-31 16:43
 **/
@RestController
@RequestMapping("bankInfo")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BankInfoController {
    @Reference
    private BankInfoFacade bankInfoFacade;


    /**
     * 获取银行信息
     * @param bankChannelNo 联行号 准确查询
     * @param bankNameLike  银行名模糊查询
     * @param pageVo   分页参数
     * @return  银行分页
     */
    @GetMapping("getBankInfo")
    public RestResult<PageResult<List<BankInfo>>> getBankInfo(String bankChannelNo, String bankNameLike, PageVo pageVo){
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("bankChannelNo",bankChannelNo);
        paramMap.put("bankNameLike",bankNameLike);
        return RestResult.success(bankInfoFacade.listPage(paramMap,pageVo.toPageParam()));
    }


}
