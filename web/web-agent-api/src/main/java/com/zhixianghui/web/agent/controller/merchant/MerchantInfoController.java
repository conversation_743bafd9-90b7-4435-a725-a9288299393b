package com.zhixianghui.web.agent.controller.merchant;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.flow.dto.FlowStartDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerAddVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.web.agent.annotation.CurrentOperatorVO;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.annotation.Logger;
import com.zhixianghui.web.agent.vo.merchant.MerchantEmployerInsertVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2021/3/1 11:22
 **/
@Slf4j
@RestController
@RequestMapping("merchantEmployer")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantInfoController {

    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private ApprovalFlowFacade approvalFlowFacade;
    @Reference
    private AgentFacade agentFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private VendorFacade vendorFacade;
    @Reference
    private NotifyFacade notifyFacade;

    /**
     * 提交商户审核信息
     * @param flowStartDto
     * @param staffVO
     * @return
     */
    @RequestMapping("submitAudit")
    @Permission("merchant:employer:submit")
    @Logger(type = OperateLogTypeEnum.CREATE,action = "提交商户审核信息")
    public RestResult<Map<String,Object>> submitAudit(@Valid @RequestBody FlowStartDto<MerchantEmployerAddVo> flowStartDto, @CurrentStaffVo AgentStaffVO staffVO){
        // 生成商户编号
        String mchNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getPrefix(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getKey(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getWidth());
        MerchantEmployerAddVo merchantEmployerAddVo = flowStartDto.getExtObj();
        merchantEmployerAddVo.setMchNo(mchNo);

        //当前的所选合伙人为指定的邀请人
        PmsOperator pmsOperator = new PmsOperator();
        pmsOperator.setLoginName(staffVO.getPhone());
        pmsOperator.setRealName(staffVO.getAgentName());

        merchantEmployerAddVo.setAgentNo(staffVO.getAgentNo());
        merchantEmployerAddVo.setAgentName(staffVO.getAgentName());
        merchantEmployerAddVo.setPmsOperator(pmsOperator);

        List<AgentResVo> agentList = agentFacade.listVoPage(Collections.singletonMap("agentNo",staffVO.getAgentNo()), PageParam.newInstance(1,1)).getData();
        if(CollectionUtils.isEmpty(agentList) || agentList.get(0).getSalerId() == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到销售或当前合伙人信息");
        }
        //销售为合伙人的销售

        merchantEmployerAddVo.setSalerId(agentList.get(0).getSalerId());
        merchantEmployerAddVo.setSalerName(agentList.get(0).getSalerName());
        log.info("商户入网：{}", JsonUtil.toString(merchantEmployerAddVo));

        //当前用户
        FlowUserVo createUser = new FlowUserVo();
        createUser.setPlatform(PlatformSource.AGENT.getValue());
        createUser.setUserName(StringUtil.isEmpty(staffVO.getName()) ? staffVO.getAgentNo() : staffVO.getName());
        createUser.setUserId(staffVO.getId());
        createUser.setNo(staffVO.getAgentNo());

        //构建参数
        ProcessVo processVo = new ProcessVo();
        processVo.setBusinessKey(mchNo);
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(FlowTypeEnum.AGENT_MCH_APPLY.name());
        processVo.setFlowTopicType(FlowTypeEnum.AGENT_MCH_APPLY.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-",FlowTypeEnum.AGENT_MCH_APPLY.getDesc(),merchantEmployerAddVo.getMchName()));
        processVo.setExtInfo(JsonUtil.toString(merchantEmployerAddVo));
        processVo.setRemark(flowStartDto.getRemark());
        //构建参与者，下一个环节参与者为销售
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(merchantEmployerAddVo.getSalerId());
        flowUserVo.setUserName(merchantEmployerAddVo.getSalerName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        //注意这里key与流程图相同
        flowStartDto.getParticipant().put("salesId",new ArrayList<FlowUserVo>(){{add(flowUserVo);}});
        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo,createUser,flowStartDto.getParticipant(),flowStartDto.getCondition());

        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_CREATE_MERCHANT_NOTIFY, JSON.toJSONString(commonFlow));

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

//    /**
//     * 提交商户审核信息
//     *
//     * @param auditVO .
//     * @return .
//     */
//    @RequestMapping("submitAudit")
//    @Permission("merchant:employer:submit")
//    @Logger(type = OperateLogTypeEnum.CREATE, action = "提交商户审核信息")
//    public RestResult<Map<String, Object>> submitAudit(@Valid @RequestBody MerchantEmployerInsertVo auditVO, @CurrentOperatorVO AgentOperatorVO operator, @CurrentStaffVo AgentStaffVO staffVO) {
//        // 生成商户编号
//        String mchNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getPrefix(),
//                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getKey(),
//                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getWidth());
//        auditVO.setMchNo(mchNo);
//
//        //当前的所选合伙人为指定的邀请人
//        auditVO.setAgentNo(staffVO.getAgentNo());
//        auditVO.setAgentName(staffVO.getAgentName());
//
//        List<AgentResVo> agentList = agentFacade.listVoPage(Collections.singletonMap("agentNo",staffVO.getAgentNo()), PageParam.newInstance(1,1)).getData();
//        if(CollectionUtils.isEmpty(agentList) || agentList.get(0).getSalerId() == null){
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到销售或当前合伙人信息");
//        }
//        //销售为合伙人的销售
//        auditVO.setSalerId(agentList.get(0).getSalerId());
//        auditVO.setSalerName(agentList.get(0).getSalerName());
//        log.info("商户入网：{}", JsonUtil.toString(auditVO));
//        ApprovalFlow approvalFlow = new ApprovalFlow();
//        approvalFlow.setVersion(0);
//        approvalFlow.setStepNum(0);
//        approvalFlow.setInitiatorId(operator.getId());
//        approvalFlow.setInitiatorName(operator.getPhone());
//        //由于要同一合伙人纬度看到消息 此处使用合伙人的信息
//        approvalFlow.getJsonEntity().setDimension(staffVO.getAgentNo());
//        approvalFlow.setFlowTopicType(FlowTopicType.CREATE_MERCHANT.getValue());
//        approvalFlow.setFlowTopicName(String.join("-", FlowTopicType.CREATE_MERCHANT.getDesc(), auditVO.getMchName()));
//        approvalFlow.setCreateTime(new Date());
//        approvalFlow.setUpdateTime(new Date());
//        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
//        approvalFlow.setExtInfo(JsonUtil.toString(auditVO));
//        approvalFlow.setPlatform(PlatformSource.AGENT.getValue());
//
//        ApprovalInfoVo infoVo = approvalFlowFacade.createFlow(approvalFlow);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("id", infoVo.getId());
//        result.put("createTime", DateUtil.formatDateTime(approvalFlow.getCreateTime()));
//        result.put("submitName", operator.getPhone());
//        return RestResult.success(result);
//    }

    /**
     * 获取供应商商户列表
     * @return .
     */
    @RequestMapping("listActiveMainstay")
    public RestResult<List<Merchant>> listActiveMainstay() {
        List<Vendor> vendorList = vendorFacade.getVendors();
        List<Merchant> merchantList = new ArrayList<>(vendorList.size());
        if (CollectionUtils.isEmpty(vendorList)) {
            return RestResult.success(merchantList);
        }
        vendorList.forEach(item -> {
            Merchant merchant = new Merchant();
            merchant.setMchNo(item.getVendorNo());
            merchant.setMchName(item.getVendorName());
            merchantList.add(merchant);
        });
        return RestResult.success(merchantList);
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
//        paramMap.put("merchantType", MerchantTypeEnum.MAINSTAY.getValue());
//        List<Merchant> merchantList = merchantQueryFacade.listBy(paramMap);
//        return RestResult.success(merchantList);
    }
}
