package com.zhixianghui.web.agent.component;

import com.zhixianghui.common.statics.enums.common.RedisKeyPrefixEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentOperatorStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.service.agent.AgentOperatorFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.utils.NetUtil;
import com.zhixianghui.web.agent.utils.PwdDigestUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * 操作员密码
 *
 * <AUTHOR> Guangsheng
 */
@Component
@Log4j2
public class OperatorPwdHelper {

    @Reference
    private AgentOperatorFacade agentOperatorFacade;

    @Autowired
    private RedisClient redisClient;

    /**
     * 登录密码校验
     * @param operator
     * @param pwd
     */
    public void loginVerifyPwd(AgentOperator operator, String pwd) throws BizException {
        if (PwdDigestUtil.verifyPwd(operator.getSalt(), operator.getPwd(), pwd)) {
            // 更新操作员登录信息
            operator.setLastLoginTime(Optional.ofNullable(operator.getCurLoginTime()).orElse(new Date()));
            operator.setCurLoginTime(new Date());
            operator.setPwdErrorCount(0);
            agentOperatorFacade.update(operator);
        } else {
            // 密码错误
            log.warn("==>帐号{}:{}，密码错误", operator.getPhone(), operator.getName());
            // 错误次数加1
            operator.setPwdErrorCount(operator.getPwdErrorCount() + 1);
            operator.setPwdErrorTime(new Date());
            // 超过密码输错次数限制，冻结
            String errMsg;
            if (operator.getPwdErrorCount() >= PermissionConstant.LOGIN_PWD_ERROR_LIMIT) {
                operator.setStatus(AgentOperatorStatusEnum.INACTIVE.getValue());
                errMsg = "密码已连续输错【" + PermissionConstant.LOGIN_PWD_ERROR_LIMIT + "】次，帐号已被冻结";
            } else {
                errMsg = "帐号或密码错误，剩余【" + (PermissionConstant.LOGIN_PWD_ERROR_LIMIT - operator.getPwdErrorCount()) + "】次机会";
            }
            agentOperatorFacade.update(operator);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(errMsg);
        }
    }

    /**
     * 更新操作员密码
     * @param operator
     * @param pwd
     * @param verifyOriginPwd
     * @param originPwd
     */
    public void updatePwd(AgentOperator operator, String pwd, boolean verifyOriginPwd, String originPwd) {
        if (verifyOriginPwd) {
            if (StringUtil.isEmpty(originPwd)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("原密码不能为空");
            }
            if (!PwdDigestUtil.verifyPwd(operator.getSalt(), operator.getPwd(), originPwd)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("原密码错误");
            }
        }

        PwdDigestUtil.DigestResult result = PwdDigestUtil.digestPwd(pwd);
        operator.setPwd(result.getPwd());
        operator.setSalt(result.getSalt());
        operator.setIsInitPwd(AgentInitPwdStatusEnum.INITED.getValue());   // 密码已被初始化
        operator.setPwdErrorCount(0);                                       // 错误次数重置 0
        operator.setStatus(AgentOperatorStatusEnum.ACTIVE.getValue());     // 解除冻结
        operator.setModPwdTime(new Date());
        operator.setUpdateTime(new Date());
        agentOperatorFacade.update(operator);
    }

    /**
     * 统一获取publickey的redis存储key
     * @param phone
     * @return
     */
    private String getPublickeyRedisKey(String phone) {
        return String.format("LOGIN-%s-%s-%s", RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name(),
                PermissionConstant.USER_DYNAMIC_RSA_KEY,
                phone);
    }

    /**
     * redis存储rsa的公私钥对
     * @param phone
     * @param keypair
     */
    public void cachePublicKey(String phone, Map<String, String> keypair){
        redisClient.set(getPublickeyRedisKey(phone), JsonUtil.toString(keypair), PermissionConstant.USER_DYNAMIC_RSA_EXPIRE_TIME);
    }

    /**
     * 校验RSA公私钥
     * @param phone
     * @param request
     */
    public String getPrivateKey(String phone, HttpServletRequest request) {
        //校验公私钥对是否过期，过期需要重新登录获取
        String keypair = redisClient.get(getPublickeyRedisKey(phone));
        if (StringUtil.isEmpty(keypair)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("公钥已超时，请返回首页重新登录");
        }
        //校验生成RSA时的ip和UserAgent是否改变
        Map<String,String> keypairMap = JsonUtil.toBean(keypair,Map.class);

        if (keypairMap.get("type") != null && keypairMap.get("type").equals("wechat")){
            return keypairMap.get(RSAUtil.PRIVATE_KEY);
        }
        if (!keypairMap.get("UserAgent").equals(request.getHeader("User-Agent")) ||
                !keypairMap.get("Host").equals(NetUtil.getIpAddr(request))) {
            log.error("RSA的校验失败。原存储的Host：{}，UserAgent：{}。当前请求的Host：{}，UserAgent：{}",
                    keypairMap.get("Host"),
                    keypairMap.get("UserAgent"),
                    request.getHeader("User-Agent"),
                    NetUtil.getIpAddr(request));
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("登录身份验证错误");
        }
        return keypairMap.get(RSAUtil.PRIVATE_KEY);
    }
}
