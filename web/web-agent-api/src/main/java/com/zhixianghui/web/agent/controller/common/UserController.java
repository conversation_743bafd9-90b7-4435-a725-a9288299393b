package com.zhixianghui.web.agent.controller.common;

import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentLoginTypeEnum;
import com.zhixianghui.common.statics.enums.user.agent.AgentOperatorStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentOperatorFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentOperatorVO;
import com.zhixianghui.web.agent.annotation.CurrentSession;
import com.zhixianghui.web.agent.annotation.Logger;
import com.zhixianghui.web.agent.component.*;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.dto.JwtInfo;
import com.zhixianghui.web.agent.dto.Session;
import com.zhixianghui.web.agent.utils.NetUtil;
import com.zhixianghui.web.agent.vo.permission.FunctionVO;
import com.zhixianghui.web.agent.vo.user.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 公共接口
 */
@RestController
@RequestMapping("user")
@Log4j2
public class UserController {

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private CaptchaHelper captchaHelper;

    @Autowired
    private DataDecryptHelper dataDecryptHelper;

    @Autowired
    private OperatorPwdHelper operatorPwdHelper;

    @Reference
    private AgentOperatorFacade agentOperatorFacade;

    @Reference
    private AgentStaffFacade agentStaffFacade;

    @Reference
    private AgentFacade agentFacade;

    /**
     * 获取图形验证码
     * @return
     */
    @PostMapping("captcha")
    public RestResult<CaptchaVO> captcha() {
        return RestResult.success(captchaHelper.genCaptcha());
    }

    /**
     * 发送短信验证码
     * @return
     */
    @PostMapping("sendSmsCode")
    @Logger(type = OperateLogTypeEnum.LOGIN, action = "发送短信验证码")
    public RestResult<String> sendSmsCode(@RequestParam String phone) {
        captchaHelper.sendSmsCode(phone);
        return RestResult.success("短信验证码发送成功");
    }

    /**
     * 查询用户状态
     */
    @GetMapping("getUserStatus")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "查询是否注册")
    public RestResult<Map<String, Object>> getUserStatus(@RequestParam String phone) {
        AgentOperator operator = agentOperatorFacade.getByPhone(phone);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("register", operator != null);
        resultMap.put("initPwd", operator != null && operator.getIsInitPwd() == AgentInitPwdStatusEnum.INITED.getValue());
        if (operator != null) {
            resultMap.put("status", operator.getStatus());
        }
        return RestResult.success(resultMap);
    }

    /**
     * 设置登录密码（使用手机验证码）
     * @return
     * 前端请求此接口前，需要先请求/getUserPublickey接口获取动态的公钥
     */
    @PostMapping("resetPwd")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "设置登录密码")
    public RestResult<String> resetPwd(@RequestParam String phone,
                                       @RequestParam String pwd,
                                       @RequestParam String smsCode,
                                       HttpServletRequest request) {
        AgentOperator operator = agentOperatorFacade.getByPhone(phone);
        if (operator == null) {
            return RestResult.error("帐号未注册，请联系客服");
        }

        captchaHelper.verifySmsCode(phone, smsCode);

        // 密码解密
        //校验RSA公私钥对，同时返回私钥
        String privateKey = operatorPwdHelper.getPrivateKey(phone, request);
        pwd = dataDecryptHelper.decryptData(privateKey, pwd);
        operatorPwdHelper.updatePwd(operator, pwd, false, null);

        captchaHelper.invalidSmsCode(phone);
        return RestResult.success("设置密码成功");
    }

    /**
     * 修改密码（登录状态下）
     * @param originPwd     原密码，为加密后的
     * @param pwd           新密码，为加密后的
     */
    @PostMapping("updatePwd")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "修改密码")
    public RestResult<String> updatePwd(@RequestParam(required = false) String originPwd,
                                        @RequestParam String pwd,
                                        @CurrentOperatorVO AgentOperatorVO operatorVO,
                                        @CurrentSession Session session,
                                        HttpServletRequest request) {
        AgentOperator operator = agentOperatorFacade.getByPhone(operatorVO.getPhone());


        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        pwd = dataDecryptHelper.decryptData(privateKey, pwd);
        originPwd = dataDecryptHelper.decryptData(privateKey, originPwd);
        if (operator.getIsInitPwd() == AgentInitPwdStatusEnum.NO_INIT.getValue()) {  // 未初始化密码，直接设置
            operatorPwdHelper.updatePwd(operator, pwd, false, originPwd);
        } else {
            if (StringUtil.isEmpty(originPwd)) {
                return RestResult.error("原密码不能为空");
            }
            operatorPwdHelper.updatePwd(operator, pwd, true, originPwd);
        }

        // 退出登录
        sessionManager.deleteSession(session.getPhone(), session.getUuid());
        return RestResult.success("修改密码成功，请重新登录");
    }

    /**
     * 获取RSA公钥
     * @param phone
     * @return
     */
    @GetMapping("getUserPublickey")
    @Logger(type = OperateLogTypeEnum.LOGIN, action = "登录前获取RSA公钥")
    public RestResult<Map<String, Object>> getUserPublickey(@RequestParam String phone,
                                                            HttpServletRequest request) {
        Map<String, String> keyPair = RSAUtil.genKeyPair();
        //加多两个浏览器和ip信息由于校验
        keyPair.put("UserAgent", request.getHeader("User-Agent"));
        keyPair.put("Host", NetUtil.getIpAddr(request));
        operatorPwdHelper.cachePublicKey(phone, keyPair);

        Map<String, Object> result = new HashMap<>();
        result.put(RSAUtil.PUBLIC_KEY, keyPair.get(RSAUtil.PUBLIC_KEY));
        return RestResult.success(result);
    }

    /**
     * 登录
     * @return
     */
    @PostMapping("login")
    @Logger(type = OperateLogTypeEnum.LOGIN, action = "登录")
    public RestResult<LoginResVO> login(@RequestBody @Valid LoginReqVO loginReqVO,
                                        HttpServletRequest request) {
        // 校验登录参数
        verifyLoginParam(loginReqVO);

        // 密码校验
        AgentOperator operator = verifyLogin(loginReqVO, request);

        AgentOperatorVO vo = AgentOperatorVO.build(operator);
        // 创建session
        Session session = sessionManager.createSession(vo.getPhone());
        session.save();
        session.setAttribute(PermissionConstant.OPERATOR_SESSION_KEY, JsonUtil.toString(vo));
        //登录成功，则把该用户的私钥交给session来管理
        session.setAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, operator.getPrivateKey());
        // 生成token
        JwtInfo jwtInfo = new JwtInfo();
        jwtInfo.setUuid(session.getUuid());
        jwtInfo.setAgentOperatorVO(vo);
        // 返回信息
        LoginResVO loginResVO = new LoginResVO();
        loginResVO.setToken(jwtHelper.genToken(request, jwtInfo));
        loginResVO.setOperator(vo);
        loginResVO.setAgentNos(getAgentInfoListByPhone(loginReqVO.getPhone()));
        return RestResult.success(loginResVO);
    }

    /**
     * 校验登录参数
     */
    public void verifyLoginParam(LoginReqVO loginReqVO) throws BizException {
        if (loginReqVO.getLoginType() != AgentLoginTypeEnum.PWD.getValue()
                && loginReqVO.getLoginType() != AgentLoginTypeEnum.SMS.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("登录方式参数错误");
        }
        if (StringUtil.isEmpty(loginReqVO.getCaptcha()) || StringUtil.isEmpty(loginReqVO.getCaptchaId())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("图形验证码不能为空");
        }

        // 密码登录
        if (loginReqVO.getLoginType() == AgentLoginTypeEnum.PWD.getValue() && StringUtil.isEmpty(loginReqVO.getPwd())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("密码不能为空");
        }

        // 手机验证码登录
        if (loginReqVO.getLoginType() == AgentLoginTypeEnum.SMS.getValue() && StringUtil.isEmpty(loginReqVO.getSmsCode())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("手机验证码不能为空");
        }
    }

    /**
     * 验证登录
     */
    public AgentOperator verifyLogin(LoginReqVO loginReqVO, HttpServletRequest request) throws BizException {
        AgentOperator operator = agentOperatorFacade.getByPhone(loginReqVO.getPhone());
        if (operator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号或密码错误");
        } else if (operator.getStatus() == AgentOperatorStatusEnum.INACTIVE.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号密码输入错误次数过多已被冻结，请重置密码后再登录");
        }

        // 校验图形验证码
        captchaHelper.verifyCaptcha(loginReqVO.getCaptchaId(), loginReqVO.getCaptcha());

        //校验RSA公私钥对，同时返回私钥
        String privateKey = operatorPwdHelper.getPrivateKey(loginReqVO.getPhone(), request);
        if (loginReqVO.getLoginType() == AgentLoginTypeEnum.PWD.getValue()) {  // 密码登录
            // 校验登录密码
            if (operator.getIsInitPwd() == AgentInitPwdStatusEnum.NO_INIT.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号未设置密码");
            }
            // 解密密码并校验
            operatorPwdHelper.loginVerifyPwd(operator, dataDecryptHelper.decryptData(privateKey, loginReqVO.getPwd()));
            //校验通过则临时把私钥放入
            operator.setPrivateKey(privateKey);
        } else if (loginReqVO.getLoginType() == AgentLoginTypeEnum.SMS.getValue()) {  // 手机验证码登录
            // 校验手机验证码
            captchaHelper.verifySmsCode(loginReqVO.getPhone(), dataDecryptHelper.decryptData(privateKey, loginReqVO.getSmsCode()));
            captchaHelper.invalidSmsCode(loginReqVO.getPhone());
            //校验通过则临时把私钥放入
            operator.setPrivateKey(privateKey);
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("登录方式参数错误");
        }

        captchaHelper.invalidCaptcha(loginReqVO.getCaptchaId());  // 图形验证码失效
        return operator;
    }

    /**
     * 退出
     * @return
     */
    @GetMapping("logout")
    @Logger(type = OperateLogTypeEnum.LOGOUT, action = "登出")
    public RestResult<String> logout(@CurrentSession Session session) {
        sessionManager.deleteSession(session.getPhone(), session.getUuid());
        return RestResult.success("");
    }

    /**
     * 查询可选商户列表
     * @return
     */
    @GetMapping("agentList")
    public RestResult<List<AgentInfoVO>> agentList(@CurrentOperatorVO AgentOperatorVO operatorVO) {
        return RestResult.success(getAgentInfoListByPhone(operatorVO.getPhone()));
    }

    /**
     * 选择合伙人
     * @return  权限标识
     */
    @GetMapping("selectAgent")
    public RestResult<SelectAgentResVO> selectAgent(@RequestParam String agentNo,
                                                @RequestParam String agentType,
                                                @CurrentOperatorVO AgentOperatorVO operatorVO,
                                                @CurrentSession Session session) {
        AgentStaffVO staffVO = agentStaffFacade.getByPhone(agentNo, operatorVO.getPhone());
        if (staffVO == null) {
            return RestResult.error("不是该商户的员工");
        }
        // 商户状态校验
        Agent agent = agentFacade.getByAgentNo(agentNo);
        if (agent == null) {
            return RestResult.error("商户不存在");
        }
        if (agent.getAgentStatus() == AgentStatusEnum.INACTIVE.getValue()) {
            return RestResult.error("商户已被冻结");
        }

        boolean isAdmin = agentStaffFacade.isAdmin(staffVO);
        List<FunctionVO> functions = null;
        if (isAdmin) {
            functions = agentStaffFacade.listAllFunction().stream().map(FunctionVO::buildVo).collect(Collectors.toList());
        } else {
            functions = agentStaffFacade.listFunctionByStaffId(agentNo, staffVO.getId()).stream()
                    .map(FunctionVO::buildVo).collect(Collectors.toList());
        }

        // 商户编号、员工信息、权限信息更新至session
        Map<String, String> map = new HashMap<>();
        map.put(PermissionConstant.AGENT_NO_SESSION_KEY, agentNo);
        map.put(PermissionConstant.AGENT_TYPE_SESSION_KEY, agentType);
        map.put(PermissionConstant.STAFF_SESSION_KEY, JsonUtil.toString(staffVO));
        map.put(PermissionConstant.PERMISSION_SESSION_KEY, JsonUtil.toString(
                functions.stream().map(FunctionVO::getPermissionFlag).collect(Collectors.toList())));
        session.setAttribute(map);
        // 返回
        SelectAgentResVO selectAgentResVO = new SelectAgentResVO();
        selectAgentResVO.setStaff(staffVO);
        selectAgentResVO.setFunctions(functions);
        return RestResult.success(selectAgentResVO);
    }

    /**
     * 修改个人信息
     */
    @PostMapping("updateInfo")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "修改个人信息")
    public RestResult<String> updateInfo(@RequestBody @Valid UpdateInfoVO updateInfoVO,
                                         @CurrentOperatorVO AgentOperatorVO operatorVO,
                                         @CurrentSession Session session) {
        AgentOperator operator = agentOperatorFacade.getByPhone(operatorVO.getPhone());
        operator.setName(updateInfoVO.getName());
        operator.setHeadPortraitFileUrl(updateInfoVO.getHeadPortraitFileUrl());
        agentOperatorFacade.update(operator);
        // 修改session中的信息
        operatorVO.setName(updateInfoVO.getName());
        session.setAttribute(PermissionConstant.OPERATOR_SESSION_KEY, JsonUtil.toString(operatorVO));
        return RestResult.success("操作成功");
    }

    /**
     * 获取登录信息
     */
    @RequestMapping("info")
    public RestResult<UserInfo> info(@CurrentSession Session session) {
        UserInfo userInfo = new UserInfo();
        userInfo.setOperator(session.getAttribute(PermissionConstant.OPERATOR_SESSION_KEY, AgentOperatorVO.class));
        userInfo.setAgentNo(session.getAttribute(PermissionConstant.AGENT_NO_SESSION_KEY, String.class));
        userInfo.setAgentType(session.getAttribute(PermissionConstant.AGENT_TYPE_SESSION_KEY, String.class));
        AgentStaffVO agentStaffVO = session.getAttribute(PermissionConstant.STAFF_SESSION_KEY, AgentStaffVO.class);
        userInfo.setStaff(agentStaffVO);

        if (agentStaffVO == null || StringUtil.isEmpty(userInfo.getAgentNo())) {
            userInfo.setFunctions(null);
            return RestResult.success(userInfo);
        }
        boolean isAdmin = agentStaffFacade.isAdmin(agentStaffVO);
        if (isAdmin) {
            userInfo.setFunctions(agentStaffFacade.listAllFunction().stream().map(FunctionVO::buildVo).collect(Collectors.toList()));
        } else {
            userInfo.setFunctions(agentStaffFacade.listFunctionByStaffId(userInfo.getAgentNo(), userInfo.getStaff().getId())
                    .stream().map(FunctionVO::buildVo).collect(Collectors.toList()));
        }
        return RestResult.success(userInfo);

    }

    /**
     * 查询操作员的可选商户列表
     * @return
     */
    public List<AgentInfoVO> getAgentInfoListByPhone(String phone) {
        List<String> agentNoList = agentStaffFacade.listByPhone(phone).stream()
                .map(AgentStaffVO::getAgentNo).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(agentNoList)){
            return new ArrayList<>();
        }
        // 查询操作员关联的商户
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agentNoList", agentNoList);
        List<Agent> agents = agentFacade.listBy(paramMap);

        List<AgentInfoVO> agentInfoVOS = agents.stream().filter(
                agent -> agent.getAgentStatus() != AgentStatusEnum.RETREAT.getValue()
        ).map(agent -> {
            AgentInfoVO agentInfoVO = new AgentInfoVO();
            agentInfoVO.setAgentNo(agent.getAgentNo());
            agentInfoVO.setAgentName(agent.getAgentName());
            agentInfoVO.setAgentStatus(agent.getAgentStatus());
            agentInfoVO.setAuthStatus(agent.getAuthStatus());
            agentInfoVO.setContactPhone(agent.getContactPhone());
            agentInfoVO.setAgentType(agent.getAgentType());
            return agentInfoVO;
        }).collect(Collectors.toList());
        return agentInfoVOS;
    }
}
