package com.zhixianghui.web.agent.config;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.agent.component.JwtHelper;
import com.zhixianghui.web.agent.constant.PermissionConstant;
import com.zhixianghui.web.agent.vo.wx.WxLoginTokenVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName WxTokenValidInterceptor
 * @Description TODO
 * @Date 2023/9/7 17:15
 */
@Slf4j
@Component
public class WxTokenValidInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private RedisClient redisClient;

    private final static String WECHAT_LOGIN_KEY_PREFIX = "WX_AGENT_LOGIN_KEY:";

    public static final int AUTH_TIME = 24 * 60 * 60;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (Objects.equals(request.getMethod(), HttpMethod.OPTIONS.name())) {
            return false;
        }

        //获取token
        String token = request.getHeader(PermissionConstant.REQUEST_TOKEN_HEADER);
        if (StringUtils.isBlank(token)){
            writeDenyResult(response, RestResult.unAuth("token不存在"));
            return false;
        }
        String tokenKey = WECHAT_LOGIN_KEY_PREFIX+ MD5Util.getMD5Hex(token);
        WxLoginTokenVo wxLoginTokenVo = null;
        try {
            jwtHelper.verifyWxToken(token);
            String info = redisClient.get(tokenKey);
            if (info == null){
                writeDenyResult(response,RestResult.unAuth("token无效"));
                return false;
            }
            wxLoginTokenVo = JsonUtil.toBean(info,WxLoginTokenVo.class);
            //更新令牌时间
            redisClient.expire(tokenKey,AUTH_TIME);
        }catch (Exception e){
            writeDenyResult(response,RestResult.unAuth("token无效"));
            return false;
        }
        request.setAttribute("loginInfo",wxLoginTokenVo);
        return true;
    }


    private void writeDenyResult(HttpServletResponse response, RestResult restResult) throws Exception {
        response.getWriter().write(JsonUtil.toString(restResult));
        response.setContentType("application/json;charset=utf-8");
        response.flushBuffer();
    }
}
