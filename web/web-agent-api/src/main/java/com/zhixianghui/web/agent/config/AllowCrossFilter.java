package com.zhixianghui.web.agent.config;

import com.zhixianghui.web.agent.constant.PermissionConstant;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 跨域过滤器
 *
 * <AUTHOR>
 */
@Component
public class AllowCrossFilter implements Filter {
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Method", "*");
        response.addHeader("Access-Control-Allow-Headers", "content-type," + PermissionConstant.REQUEST_TOKEN_HEADER);
        filterChain.doFilter(servletRequest, servletResponse);
    }
}
