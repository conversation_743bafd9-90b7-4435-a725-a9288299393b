package com.zhixianghui.web.agent.vo.permission;

import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
public class RoleVO {

    /**
     * id
     */
    private Long id;

    /**
     * 角色名称
     */
    @NotEmpty(message = "角色名称不能为空")
    @Size(min = 2, max = 20, message = "角色名称在2到20个字符")
    private String roleName;

    /**
     * 描述
     */
    @Size( max = 50, message = "描述长度不能超过50")
    private String remark;

    /**
     * 角色类型:0自定义,1预置
     */
    private Integer roleType;

    public static AgentRole buildDto(RoleVO vo, String agentNo) {
        AgentRole role = new AgentRole();
        role.setId(vo.getId());
        role.setCreateTime(new Date());
        role.setAgentNo(agentNo);
        role.setName(vo.getRoleName());
        role.setRemark(vo.getRemark());
        role.setRoleType(RoleTypeEnum.CUSTOMIZE.getType());
        return role;
    }

    public static RoleVO buildVo(AgentRole role) {
        RoleVO vo = new RoleVO();
        vo.setId(role.getId());
        vo.setRoleName(role.getName());
        vo.setRemark(role.getRemark());
        vo.setRoleType(role.getRoleType());
        return vo;
    }
}
