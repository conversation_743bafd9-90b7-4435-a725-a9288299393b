package com.zhixianghui.web.agent.controller.flow;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.dto.CommonFlowLogVo;
import com.zhixianghui.facade.flow.dto.CommonFlowVo;
import com.zhixianghui.facade.flow.entity.CommonFlowLog;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.service.FlowImageFacade;
import com.zhixianghui.facade.flow.vo.req.*;
import com.zhixianghui.facade.flow.vo.res.TaskResVo;
import com.zhixianghui.facade.merchant.service.agent.AgentRoleFacade;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentOperator;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentOperatorVO;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.vo.PageVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowController
 * @Description TODO
 * @Date 2021/5/17 9:16
 */
@RestController
@RequestMapping("flow")
public class FlowController extends CommonFlow {

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private FlowImageFacade flowImageFacade;

    @Reference
    private AgentStaffFacade agentStaffFacade;

    @Reference
    private AgentRoleFacade agentRoleFacade;

    /**
     * 我的已办
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("agent:flow:view")
    @PostMapping("handleList")
    public RestResult<PageResult<List<Map<String,Object>>>> handleList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo AgentStaffVO staffVO) {
        boolean admin = admin(staffVO, agentRoleFacade);
        paramMap.put("mchNo", staffVO.getAgentNo());
        PageResult<List<Map<String,Object>>> taskList = flowFacade.handleList(staffVO.getId(), PlatformSource.AGENT.getValue(), admin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 我的发起
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("agent:flow:view")
    @PostMapping("sendList")
    public RestResult<PageResult<List<Map<String,Object>>>> sendList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo AgentStaffVO staffVO) {
        boolean admin = admin(staffVO, agentRoleFacade);
        paramMap.put("mchNo", staffVO.getAgentNo());
        PageResult<List<Map<String,Object>>> taskList = flowFacade.sendList(staffVO.getId(), PlatformSource.AGENT.getValue(), admin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 我的待办
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("agent:flow:view")
    @PostMapping("todoList")
    public RestResult<PageResult<List<Map<String,Object>>>> todoList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo AgentStaffVO staffVO) {
        PageResult<List<Map<String,Object>>> taskList = flowFacade.todoList(getFlowUserVo(staffVO), false, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 获取操作日志
     *
     * @param commonFlowId 流程通用id
     * @return
     */
    @Permission("agent:flow:view")
    @GetMapping("getDetail")
    public RestResult<List<CommonFlowLogVo>> getDetail(@RequestParam(name = "commonFlowId") Long commonFlowId) {
        List<CommonFlowLogVo> commonFlowLogList = flowFacade.getDetailByCommonFlowId(commonFlowId, PlatformSource.AGENT.getValue());
        return RestResult.success(commonFlowLogList);
    }

    /**
     * 根据流程实例id获取流程追踪图
     *
     * @param processInstanceId 流程实例id
     * @return
     * @throws IOException
     */
    @Permission("agent:flow:image")
    @GetMapping("getInstanceImage")
    public RestResult<?> getInstanceImage(@RequestParam(name = "processInstanceId") String processInstanceId) throws IOException {
        String base64 = flowImageFacade.getInstanceImage(processInstanceId);
        return RestResult.success(base64);
    }

    /**
     * 提交任务
     *
     * @param taskHandleVo
     * @return
     */
    @Permission("agent:flow:submit")
    @PostMapping("submitTask")
    public RestResult<?> submitTask(@RequestBody TaskHandleVo taskHandleVo, @CurrentStaffVo AgentStaffVO staffVO) {
        flowFacade.executeTask(taskHandleVo, getFlowUserVo(staffVO), false);
        return RestResult.success("任务提交完毕");
    }

    @Permission("agent:flow:submit")
    @PostMapping("withDraw")
    public RestResult<?> withDraw(@RequestBody Map<String, Object> map, @CurrentStaffVo AgentStaffVO staffVO) {
        Long commonFlowId = Long.valueOf(String.valueOf(map.get("commonFlowId")));
        String reason = (String) map.get("reason");
        flowFacade.deleteProcessInstance(commonFlowId, getFlowUserVo(staffVO), false, reason);
        return RestResult.success("流程撤回成功");
    }

    /**
     * 获取流程数据
     *
     * @param commonFlowId
     * @return
     */
    @Permission("agent:flow:view")
    @GetMapping("getByCommonFlowId")
    public RestResult<CommonFlowVo> getByCommonFlowId(@RequestParam("commonFlowId") Long commonFlowId, @RequestParam(value = "taskId", required = false) String taskId,
                                                      @CurrentStaffVo AgentStaffVO staffVO) {

        CommonFlowVo commonFlowVo = flowFacade.getCommonFlowById(commonFlowId, taskId, getFlowUserVo(staffVO), false, PlatformSource.AGENT.getValue());
        return RestResult.success(commonFlowVo);
    }

    @Permission("agent:flow:submit")
    @PostMapping("editBusinessVariable")
    public RestResult<?> editBusinessVariable(@RequestBody @Validated(IEditBusinessVariable.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo AgentStaffVO staffVO) {
        flowFacade.editBusinessVariable(commonFlowEditVo, getFlowUserVo(staffVO), false);
        return RestResult.success("修改信息成功");
    }

    @Permission("agent:flow:submit")
    @PostMapping("transferTask")
    public RestResult<?> transferTask(@RequestBody @Validated(ITransferUser.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo AgentStaffVO staffVO) {
        //获取变更人
        AgentStaffVO agentStaffVO = agentStaffFacade.getById(staffVO.getAgentNo(), commonFlowEditVo.getNextUserId());
        if (agentStaffVO == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户不存在，请选择其他用户");
        }
        FlowUserVo nextUser = new FlowUserVo();
        nextUser.setUserId(agentStaffVO.getId());
        nextUser.setPlatform(PlatformSource.AGENT.getValue());
        nextUser.setUserName(agentStaffVO.getName());
        nextUser.setNo(agentStaffVO.getAgentNo());
        flowFacade.transferTask(commonFlowEditVo, getFlowUserVo(staffVO), nextUser);
        return RestResult.success("变更审批人成功");
    }

    @Permission("agent:flow:reply")
    @PostMapping("reply")
    public RestResult<?> reply(@RequestBody @Validated(IReply.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo AgentStaffVO agentStaffVO) {
        flowFacade.reply(commonFlowEditVo, getFlowUserVo(agentStaffVO));
        return RestResult.success("回复成功");
    }


    private FlowUserVo getFlowUserVo(AgentStaffVO staffVO) {
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setPlatform(PlatformSource.AGENT.getValue());
        flowUserVo.setUserId(staffVO.getId());
        flowUserVo.setUserName(StringUtil.isEmpty(staffVO.getName()) ? staffVO.getAgentNo() : staffVO.getName());
        flowUserVo.setNo(staffVO.getAgentNo());
        return flowUserVo;
    }
}
