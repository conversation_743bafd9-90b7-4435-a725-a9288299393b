package com.zhixianghui.web.agent.vo.user;

import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.vo.permission.FunctionVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UserInfo {

    /**
     * 当前登录的操作员
     */
    @NotNull
    private AgentOperatorVO operator;

    /**
     * 当前员工
     */
    private AgentStaffVO staff;

    /**
     * 当前选择的合伙人
     */
    private String agentNo;
    /**
     * 合伙人类型
     */
    private String agentType;

    /**
     * 关联的功能菜单
     */
    private List<FunctionVO> functions;
}
