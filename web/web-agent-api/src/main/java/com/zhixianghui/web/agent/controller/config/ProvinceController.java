package com.zhixianghui.web.agent.controller.config;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.config.AreaCity;
import com.zhixianghui.facade.common.entity.config.AreaProvince;
import com.zhixianghui.facade.common.entity.config.AreaTown;
import com.zhixianghui.facade.common.service.AreaProvinceFacade;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 省市区Controller
 * @date 2020-08-31 15:59
 **/
@RestController
@RequestMapping("province")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProvinceController {
    @Reference
    private AreaProvinceFacade areaProvinceFacade;

    /**
     * 获取全部的省份
     * @return 省份列表
     */
    @GetMapping("listAllProvince")
    public RestResult<List<AreaProvince>> listAllProvince(){

        return RestResult.success(areaProvinceFacade.listAllProvince());
    }

    /**
     * 根据ProvinceNo获取市列表
     * @return 市列表
     */
    @GetMapping("listCityByProvinceNo")
    public RestResult<List<AreaCity>> listCityByProvinceNo(@RequestParam String provinceNo){
        return RestResult.success(areaProvinceFacade.listCityBy(Collections.singletonMap("provinceNo",provinceNo)));
    }

    /**
     * 根据CityNo获取区域列表
     * @return 区域列表
     */
    @GetMapping("listTownByCityNo")
    public RestResult<List<AreaTown>> listTownByCityNo(@RequestParam String cityNo){
        return RestResult.success(areaProvinceFacade.listTownBy(Collections.singletonMap("cityNo",cityNo)));
    }

}
