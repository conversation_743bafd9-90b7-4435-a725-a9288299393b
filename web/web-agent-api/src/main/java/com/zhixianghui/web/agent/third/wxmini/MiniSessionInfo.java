package com.zhixianghui.web.agent.third.wxmini;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MiniSessionInfo implements Serializable {
    private static final long serialVersionUID = 5131041833852851849L;
    private String openid;
    private String sessionKey;
    private String unionid;
}
