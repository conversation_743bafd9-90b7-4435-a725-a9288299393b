package com.zhixianghui.web.agent.vo.merchant;

import com.zhixianghui.common.util.validator.EnumValue;
import com.zhixianghui.common.util.validator.Phone;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerQuoteVo;
import com.zhixianghui.facade.merchant.vo.MerchantPositionVo;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 提交商户审核信息
 * <AUTHOR>
 * @date 2020-08-05
 */
@Data
public class MerchantEmployerInsertVo implements Serializable {
    private static final long serialVersionUID = 7919155227046962924L;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 销售id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 商户名称
     */
    @NotEmpty(message = "商户名称不能为空")
    @Length(min = 1, max=100, message = "商户名称长度有误，请限制在1-100")
    private String mchName;

    /**
     * 行业类型编码
     */
    @NotEmpty(message = "行业类型不能为空")
    @Length(min = 1, max=20, message = "行业类型有误")
    private String industryTypeCode;

    /**
     * 行业类型名称
     */
    @NotEmpty(message = "行业类型不能为空")
    @Length(min = 1, max=50, message = "行业类型有误")
    private String industryTypeName;

    /**
     * 备注
     */
    @Length(max=200, message = "备注长度过长，最大支持200字符")
    private String remark;
    
    /**
     * 预计用工人数
     */
    @NotNull(message = "预计用工人数不能为空")
    private Integer workerNum;

    /**
     * 预计C端签约率等级
     */
    @NotNull(message = "请选择预计C端签约率等级")
    @EnumValue(intValues = {100, 101, 102, 103}, message = "请正确选择预计C端签约率等级")
    private Integer signRateLevel;

    /**
     * 自由职业者月经营所得低于9.7万比例
     */
    @NotNull(message = "自由职业者月经营所得低于9.7万比例不能为空")
    @Max(value = 100, message = "自由职业者月经营所得低于9.7万比例不能超过100%")
    private BigDecimal workerMonthIncomeRate;

    /**
     * 预计月资金流水
     */
    @NotNull(message = "预计月资金流水不能为空")
    private BigDecimal monthMoneySlip;

    /**
     * 提供收入明细方式
     */
    @NotNull(message = "请选择提供收入明细方式")
    @EnumValue(intValues = {100, 101, 102}, message = "请选择正确的提供收入明细方式")
    private Integer provideIncomeDetailType;

    /**
     * 公司网站
     */
    @Length(max = 200, message = "公司网站过长，最大限制200字符")
    private String companyWebsite;

    /**
     * 业务平台名称
     */
    @Length(max = 200, message = "业务平台名称过长，最大限制200字符")
    private String bizPlatformName;

    /**
     * 联系电话
     */
    @NotEmpty(message = "联系电话不能为空")
    @Phone(message = "联系电话有误")
    private String contactPhone;

    /**
     * 联系人名称
     */
    @NotEmpty(message = "联系人名称不能为空")
    @Length(max = 10, message = "联系人名称过长，最大限制10字符")
    private String contactName;

    /**
     * 补充文件id
     */
    @Size(max = 8, message = "补充文件不能超过8份")
    private List<String> supplementFileUrls;

    /**
     * 对外宣传资料文件id
     */
    @Size(max = 3, message = "公司宣传文件不能超过3份")
    private List<String> companyLeafletFileUrls;

    @Valid
    @NotEmpty(message = "岗位列表不能为空")
    private List<MerchantPositionVo> positionVoList;

    @Valid
    @NotEmpty(message = "报价单不能为空")
    private List<MerchantEmployerQuoteVo> quoteVoList;
}
