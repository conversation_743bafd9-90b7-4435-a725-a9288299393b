package com.zhixianghui.web.agent.controller.fee;


import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.fee.entity.AgentFeeOrder;
import com.zhixianghui.facade.fee.service.AgentFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.vo.AgentFeeOrderSumVo;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.agent.annotation.CurrentAgentNo;
import com.zhixianghui.web.agent.annotation.CurrentStaffVo;
import com.zhixianghui.web.agent.vo.fee.AgentFeeOrderQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 合伙人计费订单表
 *
 * <AUTHOR>
 * @since 2021-02-22
 */
@RestController
@RequestMapping("agentFeeOrder")
@Slf4j
public class AgentFeeOrderController {

    @Reference
    private AgentFeeOrderQueryFacade queryFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private AgentFacade agentFacade;


    @PostMapping("countOrder")
    public RestResult<AgentFeeOrderSumVo> countOrder(@RequestBody AgentFeeOrderQueryVo vo, @CurrentAgentNo String agentNo) {

        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        if (StringUtils.isNotBlank(vo.getAgentNo())) {
            paramMap.put("agentNo", vo.getAgentNo());
            if (!StringUtils.equals(vo.getAgentNo(),agentNo)) {
                paramMap.put("inviterNo", agentNo);
            }
        } else {
            paramMap.put("inviterNoOrAgentNo", agentNo);
        }

        return RestResult.success(queryFacade.countOrder(paramMap));
    }

    /**
     * 合伙人计费订单分页查询
     */
    @Permission("fee:agentFeeOrder:list")
    @PostMapping("listPage")
    public RestResult<PageResult<List<AgentFeeOrder>>> listPage(@RequestBody @Valid AgentFeeOrderQueryVo vo, @CurrentAgentNo String agentNo) {

        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        if (StringUtils.isNotBlank(vo.getAgentNo())) {
            paramMap.put("agentNo", vo.getAgentNo());
            if (!StringUtils.equals(vo.getAgentNo(),agentNo)) {
                paramMap.put("inviterNo", agentNo);
            }
        }
        else {
            paramMap.put("inviterNoOrAgentNo", agentNo);
        }
        PageResult<List<AgentFeeOrder>> pageResult = queryFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(pageResult);
    }

    /**
     * 合伙人计费订单导出
     */
    @RequestMapping("exportAgentFeeOrder")
    @Permission("fee:agentFeeOrder:export")
    public RestResult<String> exportAgentFeeOrder(@Valid @RequestBody AgentFeeOrderQueryVo vo, @CurrentStaffVo AgentStaffVO staffVO) {
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setMchNo(staffVO.getAgentNo());
        record.setSystemType(SystemTypeEnum.AGENT_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGENT_FEE_ORDER.getFileName());
        record.setReportType(ReportTypeEnum.AGENT_FEE_ORDER.getValue());

        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        String agentNo = staffVO.getAgentNo();
        if (StringUtils.isNotBlank(vo.getAgentNo())) {
            paramMap.put("agentNo", vo.getAgentNo());
            if (!StringUtils.equals(vo.getAgentNo(),agentNo)) {
                paramMap.put("inviterNo", agentNo);
            }
        } else {
            paramMap.put("inviterNoOrAgentNo", agentNo);
        }
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.AGENT_FEE_ORDER.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

}
