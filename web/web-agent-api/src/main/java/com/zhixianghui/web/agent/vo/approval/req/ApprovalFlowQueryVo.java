package com.zhixianghui.web.agent.vo.approval.req;

import com.zhixianghui.facade.common.enums.FlowStatus;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2020-08-17 11:08
 **/
@Data
public class ApprovalFlowQueryVo {

    /**
     * 流程状态 {@link FlowStatus}
     */
    private Integer status;

    /**
     * 流程开始的起始时间
     */
    private String beginDate;

    /**
     * 流程开始的截止时间
     */
    private String endDate;

    /**
     * 流程主题名称
     */
    private String flowTopicNameLike;

    /**
     * 发起人
     */
    private String initiatorNameLike;
}
