package com.zhixianghui.web.agent.vo.agent.res;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/8 14:46
 **/
@Data
public class AgentSimpleResVo implements Serializable {

    private static final long serialVersionUID = -8784308624144415044L;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 合伙人状态
     */
    private Integer agentStatus;

    /**
     * 直属商户数
     */
    private Integer merNum = 0;

    /**
     * 创建时间
     */
    protected Date createTime;
}

