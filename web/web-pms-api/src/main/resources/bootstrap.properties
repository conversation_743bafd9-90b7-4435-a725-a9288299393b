spring.application.name=web-pms-api
spring.cloud.nacos.config.server-addr=@nacosAddr@
spring.cloud.nacos.config.namespace=@nacosNamespace@
spring.cloud.nacos.config.file-extension=properties

#\u8BBE\u7F6E\u5171\u4EAB\u7684\u914D\u7F6E\u6587\u4EF6
spring.cloud.nacos.config.shared-dataids=dubbo.properties,db.properties,rocketmq.properties,redis.properties,fastdfs.properties
spring.cloud.nacos.config.refreshable-dataids=dubbo.properties,db.properties,rocketmq.properties,redis.properties,fastdfs.properties


logging.level.com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder=warn
