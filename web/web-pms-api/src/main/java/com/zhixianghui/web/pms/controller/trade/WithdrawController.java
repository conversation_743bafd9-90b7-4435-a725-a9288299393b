package com.zhixianghui.web.pms.controller.trade;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @ClassName WithdrawController
 * @Description TODO
 * @Date 2022/7/20 18:07
 */
@Slf4j
@RestController
@RequestMapping("withdraw")
public class WithdrawController {

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @Autowired
    private RedisClient redisClient;

    @PostMapping("withdrawAll")
    @Permission("withdraw:withdrawAll")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "操作提现所有商户")
    public RestResult withdrawAll(@RequestBody Map<String,Object> map, @CurrentUser PmsOperator pmsOperator){
        boolean isAdmin = PmsOperatorTypeEnum.ADMIN.getValue() == pmsOperator.getType();
        if (!isAdmin){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无权限");
        }
        String mainstayNo = (String) map.get("mainstayNo");
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(
                mainstayNo, ChannelNoEnum.JOINPAY.name());
        if (mainstayChannelRelation == null || mainstayChannelRelation.getStatus().equals(OpenOffEnum.OFF.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体未开通此通道");
        }

        //查询供应商下每一个商户
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("payChannelNo",ChannelNoEnum.JOINPAY.name());
        paramMap.put("status",OpenOffEnum.OPEN.getValue());
        List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoFacade.listBy(paramMap);
        if (employerAccountInfoList.size() <= 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该代征主体下没有开通此通道的用工企业");
        }

        CompletableFuture.runAsync(()->{
            Map<String,String> withdrawMap  = new HashMap<>();
            employerAccountInfoList.stream().forEach(x->{
                withdrawMap.put(x.getEmployerNo(),"empty");
            });
            /**
             * 设置缓存参数
             */
            redisClient.incrBy(TradeConstant.WITHDRAW_ALL_NUM + mainstayNo,employerAccountInfoList.size());
            redisClient.hset(TradeConstant.WITHDRAW_ALL_MERCHNAT + mainstayNo,withdrawMap);

            /**
             * 分别发送消息执行
             */
            employerAccountInfoList.stream().forEach(x->{
                notifyFacade.sendOne(MessageMsgDest.TOPIC_ALL_MERCHANT_WITHDRAW,
                        x.getEmployerNo(),
                        x.getEmployerNo(),
                        NotifyTypeEnum.TRADE_NOTIFY.getValue(),
                        null,
                        JsonUtil.toString(x));
            });
            log.info("发起一键提现请求，代征主体编号：[{}]，预计商户数量：[{}]",mainstayNo,employerAccountInfoList.size());
        });
        return RestResult.success("已发起提现请求，预计商户：" + employerAccountInfoList.size() + "个");
    }
}
