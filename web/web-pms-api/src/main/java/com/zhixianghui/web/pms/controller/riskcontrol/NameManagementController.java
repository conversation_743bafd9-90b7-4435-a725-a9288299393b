package com.zhixianghui.web.pms.controller.riskcontrol;


import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.supplier.SupplierInitPwdStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.riskcontrol.constant.SupplierSpecialEnum;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementDTO;
import com.zhixianghui.facade.riskcontrol.dto.NameManagementQueryDTO;
import com.zhixianghui.facade.riskcontrol.service.NameManagementFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 名单管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-05
 */
@RestController
@RequestMapping("/nameManagement")
public class NameManagementController {

    @Reference
    private NameManagementFacade managementFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;


    @PostMapping("/create")
    public RestResult create(@Valid @RequestBody NameManagementDTO managementDTO,@CurrentUser PmsOperator pmsOperator) {
        managementDTO.setUpdator(pmsOperator.getRealName());
        managementFacade.create(managementDTO);
        return RestResult.success("创建成功");
    }


    @PostMapping("/listPage")
    public RestResult listPage(@RequestBody NameManagementQueryDTO nameManagementQueryDTO) {
        return RestResult.success(managementFacade.listPage(nameManagementQueryDTO));
    }

    @PostMapping("/update")
    public RestResult update(@Valid @RequestBody NameManagementDTO managementDTO, @CurrentUser PmsOperator pmsOperator) {
        managementDTO.setUpdator(pmsOperator.getRealName());
        managementFacade.update(managementDTO);
        return RestResult.success("更新成功");
    }

    @PostMapping("/delete")
    public RestResult delete(@RequestBody Map<String,Object> param) {
        Object ids = param.get("ids");
        if(ids==null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数不能为空");
        }
        managementFacade.delete(String.valueOf(param.get("ids")));
        return RestResult.success("删除成功");
    }

    /**
     * 查询所有的供应商编号和名称
     * @return
     */
    @GetMapping("getAllSuppliers")
    @Permission("riskcontrol:rule:view")
    public RestResult<List<Map<String, String>>> listAllSuppliers() {
        List<Merchant> suppliers = merchantQueryFacade.listBy(
                Collections.singletonMap("merchantType", MerchantTypeEnum.MAINSTAY.getValue()));

        List<Map<String, String>> res = suppliers.stream().map(supplier -> {
            Map<String, String> supplierVo = new HashMap<>();
            supplierVo.put("mchNo", supplier.getMchNo());
            supplierVo.put("mchName", supplier.getMchName());
            return supplierVo;
        }).collect(Collectors.toList());
        Map<String, String> map=new HashMap<>();
        map.put("mchNo", SupplierSpecialEnum.PLATFORM.getNo());
        map.put("mchName", SupplierSpecialEnum.PLATFORM.getName());
        LinkedList<Map<String, String>> list=new LinkedList(res);
        list.addFirst(map);

        return RestResult.success(list);
    }
}
