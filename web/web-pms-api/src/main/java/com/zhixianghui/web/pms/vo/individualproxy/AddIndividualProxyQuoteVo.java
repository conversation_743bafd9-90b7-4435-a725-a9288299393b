package com.zhixianghui.web.pms.vo.individualproxy;

import com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AddIndividualProxyQuoteVo implements Serializable {
    private static final long serialVersionUID = 5526146094462569644L;
    @NotNull(message = "代征id不能为空")
    private Integer id;
    private String parentCateCode;
    private IndividualProxyQuote individualProxyQuote;
}
