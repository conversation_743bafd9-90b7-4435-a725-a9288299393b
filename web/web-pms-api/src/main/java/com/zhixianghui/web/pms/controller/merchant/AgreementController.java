package com.zhixianghui.web.pms.controller.merchant;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AgreementFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AgreementSignerTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AgreementStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.AgreementComponent;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.AgreementComponentFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.AgreementResVo;
import com.zhixianghui.facade.merchant.vo.MerchantInfoVo;
import com.zhixianghui.web.pms.vo.merchant.agreement.AgreementQueryVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.merchant.AgreementBiz;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.merchant.agreement.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 商户协议Controller
 * @date 2020-09-03 15:04
 **/
@Slf4j
@RestController
@RequestMapping("agreement")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementController {

    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private AgreementComponentFacade agreementComponentFacade;

    private final AgreementBiz agreementBiz;
    private final FastdfsClient fastdfsClient;

    @GetMapping("getComponentList")
    public RestResult getComponentList(@RequestParam(name="singleSignerType",required = false)Integer singleSignerType){
        List<AgreementComponent> agreementComponentList = agreementComponentFacade.getAll();
        if (singleSignerType == null){
            return RestResult.success(agreementComponentList);
        }

        if (singleSignerType.intValue() == AgreementSignerTypeEnum.FIRST_PARTY.getValue()){
            return RestResult.success(agreementComponentList.stream().filter(x->x.getLabel().startsWith("甲方")).collect(Collectors.toList()));
        }else{
            return RestResult.success(agreementComponentList.stream().filter(x->x.getLabel().startsWith("乙方")).collect(Collectors.toList()));
        }
    }
    

    /**
     * 线上直接发起，上传模板文件
     * @param file
     * @return
     */
    @PostMapping("upload")
    public RestResult upload(@RequestParam MultipartFile file) throws IOException {
        String pdfPath = agreementBiz.handleOnlineTemplate(file.getBytes(),file.getOriginalFilename());
        return RestResult.success(pdfPath);
    }

    @PostMapping("exportExcel")
    public RestResult exportExcel(@RequestBody Map<String,Object> paramMap,@CurrentUser PmsOperator pmsOperator){
        paramMap.remove("idList");
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        ExportRecord record = getRecord(paramMap,pmsOperator);
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("replace")
    public RestResult replace(@RequestBody Map<String,Object> paramMap,@CurrentUser PmsOperator pmsOperator){
        agreementBiz.replace(paramMap,pmsOperator);
        return RestResult.success("归档协议更新成功");
    }

    /**
     * 导出归档文件
     * @param agreementHandleReqVo
     * @param pmsOperator
     * @return
     */
    @PostMapping("exportFile")
    public RestResult exportFile(@RequestBody AgreementHandleReqVo agreementHandleReqVo,@CurrentUser PmsOperator pmsOperator){
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("exportFileType", FileTypeEnum.ZIP.getValue());
        if (agreementHandleReqVo.getIdList() == null || agreementHandleReqVo.getIdList().size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请选中所需导出归档文件");
        }
        paramMap.put("agreementIdList",agreementHandleReqVo.getIdList());
        paramMap.put("type",AgreementFileTypeEnum.ARCHIVE.getValue());
        //先校验一下是否包含归档文件
        List<Agreement> agreementList = agreementBiz.listBy(paramMap);
        if (agreementList.size() == 0 || agreementList.stream().noneMatch(x->x.getStatus().intValue() == AgreementStatusEnum.FINISHED.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所选数据没有已完成的协议");
        }
        ExportRecord record = getRecord(paramMap,pmsOperator);
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private ExportRecord getRecord(Map<String, Object> paramMap,PmsOperator pmsOperator) {
        ExportRecord record = ExportRecord.newDefaultInstance();
        String fileNo = exportRecordFacade.genFileNo();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGREEMENT_PMS_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.AGREEMENT_PMS_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setDeepPage(true);

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.AGREEMENT_PMS_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        return record;
    }

    /**
     * 协议延期
     * @param agreementHandleReqVo
     * @return
     */
    @PostMapping("delay")
    public RestResult delay(@RequestBody AgreementHandleReqVo agreementHandleReqVo){
        agreementBiz.delay(agreementHandleReqVo);
        return RestResult.success("延期成功");
    }

    /**
     * 转线下签署
     * @param agreementHandleReqVo
     * @return
     */
    @PostMapping("turnToOffline")
    public RestResult turnToOffline(@RequestBody AgreementHandleReqVo agreementHandleReqVo){
        if (agreementHandleReqVo.getIdList() == null || agreementHandleReqVo.getIdList().size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请选择对应协议");
        }
        agreementBiz.turnToOffline(agreementHandleReqVo.getIdList());
        return RestResult.success("批量转线下签署成功");
    }

    /**
     *
     * @param agreementHandleReqVo
     * @return
     */
    @PostMapping("delete")
    public RestResult deleteAgreement(@RequestBody AgreementHandleReqVo agreementHandleReqVo){
        if (agreementHandleReqVo.getIdList() == null || agreementHandleReqVo.getIdList().size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请选择对应协议");
        }
        agreementBiz.deleteAgreement(agreementHandleReqVo.getIdList());
        return RestResult.success("批量删除成功");
    }

    /**
     * 撤回协议
     * @param agreementHandleReqVo
     * @return
     */
    @PostMapping("cancel")
    public RestResult cancelAgreement(@RequestBody AgreementHandleReqVo agreementHandleReqVo){
        if (agreementHandleReqVo.getIdList() == null || agreementHandleReqVo.getIdList().size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请选择对应协议");
        }
        agreementBiz.cancelAgreement(agreementHandleReqVo.getIdList());
        return RestResult.success("批量撤回成功");
    }

    /**
     * 创建(发起)协议
     * @param agreementReqVo 协议请求参数
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.CREATE, name = "创建(发起)协议")
    @Permission("merchant:agreement:add")
    @PostMapping("createAgreement")
    public RestResult<String> createAgreement(@Validated(IAgreement.class) @RequestBody AgreementReqVo agreementReqVo, @CurrentUser PmsOperator currentOperator){
        agreementBiz.createAgreement(agreementReqVo, currentOperator);
        return RestResult.success("创建成功");
    }

    /**
     * 归档协议
     * @param agreementReqVo 请求参数
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "归档协议")
    @Permission("merchant:agreement:edit")
    @PostMapping("archiveAgreement")
    public RestResult<String> archiveAgreement(@Validated(IAgreementArchive.class) @RequestBody AgreementReqVo agreementReqVo, @CurrentUser PmsOperator currentOperator){
        agreementBiz.archiveAgreement(agreementReqVo.getId(), agreementReqVo.getFileVoList(), currentOperator);
        return RestResult.success("归档成功");
    }

    @PostMapping("getAgreementPage")
    public RestResult getAgreementPage(@RequestBody AgreementQueryVo agreementQueryVo,@RequestBody(required=false) PageVo pageVo,@CurrentUser PmsOperator operator){
        Map<String,Object> paramMap = new HashMap<>();
        List<String> mchNoList = new ArrayList<>();
        // 销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("departmentId", operator.getDepartmentId());
                paramMap.put("salerIds", salerIds);
                paramMap.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
                List<MerchantInfoVo> infoVoList = merchantQueryFacade.listAll(paramMap);
                if (infoVoList.size() > 0){
                    mchNoList = infoVoList.stream().map(MerchantInfoVo::getMchNo).collect(Collectors.toList());
                }
            }
        }
        return RestResult.success(agreementBiz.getAgreementPage(agreementQueryVo,pageVo,mchNoList));
    }

    private List<Long> getSubSalerIds(PmsOperator operator) {
        List<Long> salerIds = new ArrayList<>();
        try{
            List<PmsOperator> salers = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
            salerIds = salers.stream().map(PmsOperator::getId).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            salerIds.add(operator.getId());
        }
        return salerIds;
    }

    /**
     * 根据id获取协议内容
     * @param id 协议id
     * @return 协议内容
     */
    @Permission("merchant:agreement:view")
    @GetMapping("getAgreementById/{id}")
    public RestResult<AgreementResVo> getAgreementById(@PathVariable Long id) {
        AgreementResVo agreementResVo = agreementBiz.getAgreementById(id);
        return RestResult.success(agreementResVo);
    }

    /**
     * 根据id获取协议模板内容
     * @param id 模板id
     * @return 模板内容
     */
    @Permission("merchant:agreementTemp:view")
    @GetMapping("getTemplateById/{id}")
    public RestResult<AgreementTemplateResVo> getTemplateById(@PathVariable Long id) {
        AgreementTemplateResVo agreementTemplateResVo =  agreementBiz.getTemplateById(id);
        return RestResult.success(agreementTemplateResVo);
    }

    /**
     * 分页查询模板
     * @param agreementQueryVo 查询参数
     * @param pageVo 分页参数
     * @return 查询结果
     */
    @Permission("merchant:agreementTemp:view")
    @PostMapping("listTemplatePage")
    public RestResult<PageResult<List<AgreementTemplateResVo>>> listTemplatePage(@RequestBody AgreementQueryVo agreementQueryVo, @RequestBody(required=false) PageVo pageVo) {
        PageResult<List<AgreementTemplateResVo>> pageResult = agreementBiz.listTemplatePage(agreementQueryVo, pageVo);
        return RestResult.success(pageResult);
    }

    /**
     * 分页查询协议
     * @param agreementQueryVo 查询参数
     * @param pageVo 分页参数
     * @return 查询结果
     */
    @Permission("merchant:agreement:view")
    @PostMapping("listAgreementPage")
    public RestResult<PageResult<List<AgreementResVo>>> listAgreementPage(@RequestBody AgreementQueryVo agreementQueryVo,@RequestBody(required=false) PageVo pageVo) {
        PageResult<List<AgreementResVo>> pageResult = agreementBiz.listAgreementPage(agreementQueryVo, pageVo);
        return RestResult.success(pageResult);
    }

    /**
     * 编辑协议
     * @param agreementId 协议id
     * @param deadline 签约截止时间
     * @param expireTime 协议过期时间
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "编辑协议")
    @Permission("merchant:agreement:edit")
    @PostMapping("editAgreement")
    public RestResult<String> editAgreement(@RequestParam Long agreementId, @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date deadline,
                                            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date expireTime, @CurrentUser PmsOperator currentOperator){
        agreementBiz.editAgreement(agreementId,deadline,expireTime,currentOperator);
        return RestResult.success("编辑成功");
    }

    /**
     * 编辑模板
     * @param agreementReqVo 请求参数
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "编辑模板")
    @Permission("merchant:agreementTemp:edit")
    @PostMapping("editTemplate")
    public RestResult<String> editTemplate(@Validated(IAgreementTemplate.class) @RequestBody AgreementReqVo agreementReqVo, @CurrentUser PmsOperator currentOperator){
        agreementBiz.editTemplate(agreementReqVo, currentOperator);
        return RestResult.success("编辑模板成功");
    }

    /**
     * 下载协议归档文件
     * @param id 协议id
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.QUERY, name = "下载协议归档文件")
    @Permission("merchant:agreement:view")
    @PostMapping("downloadArchiveFile")
    public RestResult<String> downloadArchiveFile(@RequestParam Long id,@CurrentUser PmsOperator operator){
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGREEMENT_ARCHIVE_FILE.getFileName());
        record.setReportType(ReportTypeEnum.AGREEMENT_ARCHIVE_FILE.getValue());
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("agreementId",id);
        paramMap.put("type", AgreementFileTypeEnum.ARCHIVE.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @Logger(type =OperateLogTypeEnum.MODIFY,name = "同步协议模板文件")
    @PostMapping("syncTemplate")
    public RestResult syncTemplate(@CurrentUser PmsOperator pmsOperator){
        agreementBiz.syncTemplate(pmsOperator);
        return RestResult.success("模板同步处理中，请稍后查看");
    }

}
