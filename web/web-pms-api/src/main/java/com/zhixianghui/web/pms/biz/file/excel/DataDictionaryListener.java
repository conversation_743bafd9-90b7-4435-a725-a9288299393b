package com.zhixianghui.web.pms.biz.file.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.web.pms.vo.common.DataDictionaryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/20 17:55
 */
@Slf4j
public class DataDictionaryListener extends AnalysisEventListener<DataDictionaryVo> {
    private List<DataDictionary> list = new ArrayList<>();
    private static final int BATCH_COUNT = 100;
    private DataDictionaryFacade dataDictionaryFacade;

    public DataDictionaryListener(DataDictionaryFacade dataDictionaryFacade) {
        this.dataDictionaryFacade = dataDictionaryFacade;
    }

    @Override
    public void invoke(DataDictionaryVo data, AnalysisContext context) {
        DataDictionary dataDictionary = data.toDataDictionary(data);
        if (list.size() > BATCH_COUNT) {
            saveData(list);
            list.clear();
        }
        list.add(dataDictionary);
    }

    private void saveData(List<DataDictionary> list) {
        list.removeIf(dataDictionary -> dataDictionaryFacade.getDataDictionaryByName(dataDictionary.getDataName()) != null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        dataDictionaryFacade.create(list);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (!ObjectUtils.isEmpty(list)) {
            saveData(list);
        }
        log.info("所有数据解析完成！");
    }
}
