package com.zhixianghui.web.pms.controller.merchant;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.service.employer.EmployerOperatorFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.web.pms.vo.merchant.employer.EmployerOperatorQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用工企业操作员
 *
 * <AUTHOR> <PERSON>
 */
@RestController
@RequestMapping("employerOperator")
@Slf4j
public class EmployerOperatorController {

    @Reference
    private EmployerOperatorFacade employerOperatorFacade;

    /**
     * 分页查询操作员
     */
    @Permission("employer:operator:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<EmployerOperatorVO>>> listPage(@RequestBody EmployerOperatorQueryVO vo) {
        Map<String, Object> map = new HashMap<>();
        map.put("phone", StringUtil.isEmpty(vo.getPhone()) ? null : vo.getPhone());
        map.put("nameLike", StringUtil.isEmpty(vo.getNameLike()) ? null : vo.getNameLike());
        map.put("status", vo.getStatus());
        map.put("createTimeBegin", vo.getCreateTimeBegin());
        map.put("createTimeEnd", vo.getCreateTimeEnd());
        PageResult<List<EmployerOperatorVO>> result = employerOperatorFacade.listPage(map,
                PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(result);
    }
}
