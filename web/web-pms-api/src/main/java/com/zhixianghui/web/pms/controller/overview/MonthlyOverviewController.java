package com.zhixianghui.web.pms.controller.overview;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.data.service.CkOrderFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.service.StatisticsFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.common.ParamHelper;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年11月23日 14:39:00
 */
@RestController
@RequestMapping("/overview")
public class MonthlyOverviewController {

    @Reference
    private StatisticsFacade statisticsFacade;
    @Reference
    private CkOrderFacade ckOrderFacade;
    @Autowired
    private ParamHelper paramHelper;

    @GetMapping("/getMonthlyOverview")
    public RestResult getMonthlyOverview(@RequestParam(required = false) String nowDate, @CurrentUser PmsOperator pmsOperator) {

        Map<String, Object> param = new HashMap<>();
        this.paramHelper.hanldSaler(pmsOperator, param);

        return RestResult.success(ckOrderFacade.getMonthlyOverview(param));
    }
}
