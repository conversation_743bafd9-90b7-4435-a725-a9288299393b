package com.zhixianghui.web.pms.controller.flow;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.flow.constant.FlowConstant;
import com.zhixianghui.facade.flow.dto.FlowStartDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowStatusEnum;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.enums.QuoteFlowAuditEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.*;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.vo.flow.*;
import com.zhixianghui.facade.merchant.vo.record.MerchantBaseInfoVo;
import com.zhixianghui.facade.merchant.vo.record.MerchantProductFeeVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @ClassName MerchantFlowController
 * @Description TODO
 * @Date 2021/7/20 10:27
 */
@Slf4j
@RestController
@RequestMapping("merchantFlow")
public class MerchantFlowController {
    private static final Map<String, Function<MerchantFlowVo, MerchantBaseInfoVo>> HANDLE = new HashMap<>();

    static {
        HANDLE.put(FlowTypeEnum.PMS_CHANGE_SALER.getFlowTopicType(), MerchantFlowVo::buildSale);
        HANDLE.put(FlowTypeEnum.PMS_MAINSTAY_CHANGE_SALER.getFlowTopicType(), MerchantFlowVo::buildSale);
        HANDLE.put(FlowTypeEnum.COMMON_CHANGE_PRINCIPAL.getFlowTopicType(), MerchantFlowVo::buildContactPerson);
        HANDLE.put(FlowTypeEnum.COMMON_MAINSTAY_CHANGE_PRINCIPAL.getFlowTopicType(), MerchantFlowVo::buildContactPerson);
        HANDLE.put(FlowTypeEnum.PMS_MERCHANT_FREEZE.getFlowTopicType(), MerchantFlowVo::buildMchStatus);
        HANDLE.put(FlowTypeEnum.PMS_MAINSTAY_FREEZE.getFlowTopicType(), MerchantFlowVo::buildMchStatus);
        HANDLE.put(FlowTypeEnum.PMS_CHANGE_ACCOUNT.getFlowTopicType(), MerchantFlowVo::buildAccount);
        HANDLE.put(FlowTypeEnum.PMS_MAINSTAY_CHANGE_ACCOUNT.getFlowTopicType(), MerchantFlowVo::buildAccount);
    }

    @Reference
    private FlowFacade flowFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private MerchantSalerFacade merchantSalerFacade;
    @Reference
    private MerchantInfoChangeRecordFacade infoChangeRecordFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MerchantBankAccountFacade accountFacade;
    @Reference
    private VendorFacade vendorFacade;
    @Reference
    private MerchantEmployerQuoteFacade merchantEmployerQuoteFacade;

    @Permission("pms:merchantFlow:quoteEdit")
    @Logger(type = OperateLogTypeEnum.DELETE, name = "删除报价单")
    @PostMapping("deleteQuote")
    public RestResult<?> deleteQuote(@RequestBody FlowStartDto<MerchantFlowVo> flowStartDto, @CurrentUser PmsOperator pmsOperator) {
        MerchantFlowVo merchantFlowVo = flowStartDto.getExtObj();
        MerchantEmployerQuote merchantEmployerQuote = merchantEmployerQuoteFacade.getById(merchantFlowVo.getQuoteId());
        if (merchantEmployerQuote.getStatus() == MerchantQuoteStatusEnum.INACTIVE.getValue()) {
            return RestResult.error("此报价单未生效，请撤回对应流程");
        }
        flowStartDto.setBusinessKey(Long.toString(merchantFlowVo.getQuoteId()));
        flowStartDto.setFlowTopicName(String.join("-", FlowTypeEnum.PMS_MCH_DELETE_PRODUCT.getDesc(), merchantFlowVo.getMchName(),merchantFlowVo.getMainstayMchName()));
        CommonFlow commonFlow = startFlow(flowStartDto, FlowTypeEnum.PMS_MCH_DELETE_PRODUCT, merchantFlowVo, pmsOperator,
                MessageMsgDest.TOPIC_APPROVAL_ASYNC, MessageMsgDest.TAG_DELETE_MERCHANT_QUOTE);
        MerchantProductFeeVo feeVo = MerchantFlowVo.buildQuote(merchantFlowVo, OperationEnum.DELETE.getOperation());
        feeVo.setOperatorInfo(pmsOperator.getId(), pmsOperator.getRealName(), commonFlow.getId());
        infoChangeRecordFacade.record(feeVo, MerchantProductFeeVo.newInstance(), ChangeSourceEnum.FLOW.getSource());
        return RestResult.success(commonFlow.buildReturn());
    }

    @Permission("pms:merchantFlow:quoteEdit")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "新增/编辑 报价单")
    @PostMapping("addQuote")
    public RestResult<Map<String, Object>> addQuote(@Validated(IQuoteValid.class) @RequestBody FlowStartDto<MerchantFlowVo> flowStartDto, @CurrentUser PmsOperator pmsOperator) {
        MerchantFlowVo merchantFlowVo = handleParam(flowStartDto);
        Merchant merchant = merchantFacade.getByMchNo(merchantFlowVo.getMchNo());
        if (!merchant.getBusinessType().contains(MerchantBusinessEnum.PAY_SERVICE.getValue()) &&
                merchantFlowVo.getProductNo().equals(ProductNoEnum.ZFT.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("非代付商户，无法创建直付通报价单");
        }
        if (merchant.getAuthStatus().intValue() != AuthStatusEnum.SUCCESS.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户未认证，请先完成主体认证");
        }
        CommonFlow commonFlow = startFlow(flowStartDto,FlowTypeEnum.PMS_MCH_CREATE_PRODUCT, merchantFlowVo, pmsOperator,
                MessageMsgDest.TOPIC_APPROVAL_ASYNC, MessageMsgDest.TAG_ADD_MERCHANT_QUOTE);
        merchantFlowVo.setFlowId(commonFlow.getId());

//        if (commonFlow.getStatus().intValue() == FlowStatusEnum.PENDING.getValue()){
            notifyFacade.sendOne(MessageMsgDest.TOPIC_ADD_MERCHANT_QUOTE, NotifyTypeEnum.MERCHANT_QUPTE_ADD.getValue(), MessageMsgDest.TAG_ADD_MERCHANT_QUOTE, JsonUtil.toString(merchantFlowVo));
//        }
        MerchantProductFeeVo feeVo = MerchantFlowVo.buildQuote(merchantFlowVo, OperationEnum.ADD.getOperation());
        feeVo.setOperatorInfo(pmsOperator.getId(), pmsOperator.getRealName(), commonFlow.getId());
        infoChangeRecordFacade.record(feeVo, MerchantProductFeeVo.newInstance(), ChangeSourceEnum.FLOW.getSource());
        return RestResult.success(commonFlow.buildReturn());
    }

    private MerchantFlowVo handleParam(FlowStartDto<MerchantFlowVo> flowStartDto) {
        MerchantFlowVo merchantFlowVo = flowStartDto.getExtObj();

        if (merchantFlowVo.getProductNo().equals(ProductNoEnum.ZFT.getValue())){
            //通道审批
            flowStartDto.getCondition().put("audit",QuoteFlowAuditEnum.CHANNEL.getValue());
            flowStartDto.setBusinessKey(String.join("-", merchantFlowVo.getMchNo(), merchantFlowVo.getProductNo()));
            flowStartDto.setFlowTopicName(String.join("-", FlowTypeEnum.PMS_MCH_CREATE_PRODUCT.getDesc(), merchantFlowVo.getMchName()));
        }else{
            if (StringUtils.isBlank(merchantFlowVo.getMainstayMchNo()) || StringUtils.isBlank(merchantFlowVo.getMainstayMchName())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体信息不能为空");
            }
            if (CollectionUtils.isEmpty(merchantFlowVo.getAccounts())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("开通的发放方式不能为空");
            }
            if (CollectionUtils.isEmpty(merchantFlowVo.getPositionList())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("岗位信息不能为空");
            }
            //供应商审批
            Vendor vendor = vendorFacade.getVendorByNoAndProduct(merchantFlowVo.getMainstayMchNo(),merchantFlowVo.getProductNo());
            if (vendor == null){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商不存在或未开通对应产品");
            }
            flowStartDto.getCondition().put("mainstayNo", vendor.getSupplierNo());
            flowStartDto.getCondition().put("audit", QuoteFlowAuditEnum.MAINSTAY.getValue());
            flowStartDto.setBusinessKey(String.join("-", merchantFlowVo.getMchNo(), merchantFlowVo.getMainstayMchNo(), merchantFlowVo.getProductNo()));
            flowStartDto.setFlowTopicName(String.join("-", FlowTypeEnum.PMS_MCH_CREATE_PRODUCT.getDesc(), merchantFlowVo.getMchName(),merchantFlowVo.getMainstayMchName()));
        }

        if (merchantFlowVo.getProductNo().equals(ProductNoEnum.CKH.getValue())){
            //TODO 固定写死个税承担方、个税承担比例、增值税、个税类型、个税公式
            merchantFlowVo.getMerchantCkhQuote().setTaxRatePct(new BigDecimal("100"));
            merchantFlowVo.getMerchantCkhQuote().setAddedTaxRatePct(BigDecimal.ZERO);
            merchantFlowVo.getMerchantCkhQuote().setTaxTypeDesc(ProductNoEnum.CKH.getText());
            merchantFlowVo.getMerchantCkhQuote().setTaxPayer(TaxPayerEnum.EMPLOYEE.getValue());
            merchantFlowVo.getMerchantCkhQuote().setTaxFormula(TaxFormulaEnum.OPERATING_INCOME.getValue());
        }
        return merchantFlowVo;
    }

    @Permission("pms:merchantFlow:freeze")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "修改商户状态")
    @PostMapping("freeze")
    public RestResult<Map<String, Object>> freeze(@Validated(IFreezeValid.class) @RequestBody FlowStartDto<MerchantFlowVo> flowStartDto, @CurrentUser PmsOperator pmsOperator) {
        MerchantFlowVo merchantFlowVo = flowStartDto.getExtObj();
        Merchant merchant = merchantFacade.getByMchNo(merchantFlowVo.getMchNo());

        if (merchantFlowVo.getHandleType() == 1 && merchant.getMchStatus() != MchStatusEnum.INACTIVE.getValue()) {
            return RestResult.error("当前商户非冻结状态");
        } else if (merchantFlowVo.getHandleType() == 0 && merchant.getMchStatus() == MchStatusEnum.INACTIVE.getValue()) {
            return RestResult.error("当前商户已处于冻结状态");
        }

        if (merchantFlowVo.getHandleType() == 1) {
            merchantFlowVo.setMchStatus(merchant.getAuthStatus() == AuthStatusEnum.SUCCESS.getValue() ? MchStatusEnum.ACTIVE.getValue() : MchStatusEnum.CREATE.getValue());
        } else {
            merchantFlowVo.setMchStatus(MchStatusEnum.INACTIVE.getValue());
        }
        FlowTypeEnum flowTypeEnum = (merchantFlowVo.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) ?
                FlowTypeEnum.PMS_MERCHANT_FREEZE : FlowTypeEnum.PMS_MAINSTAY_FREEZE;

        flowStartDto.setBusinessKey(merchant.getMchNo());
        flowStartDto.setFlowTopicName(String.join("-", flowTypeEnum.getDesc(), merchantFlowVo.getMchName()));

        CommonFlow commonFlow = startFlow(flowStartDto, flowTypeEnum, merchantFlowVo, pmsOperator,
                MessageMsgDest.TOPIC_APPROVAL_ASYNC, MessageMsgDest.TAG_MERCHANT_FREEZE);
        // 保存前后修改信息
        merchantFlowVo.setFlowId(commonFlow.getId());
        compareAndRecord(merchantFlowVo, flowTypeEnum, pmsOperator, commonFlow,merchant);
        return RestResult.success(commonFlow.buildReturn());
    }

    private void compareAndRecord(MerchantFlowVo merchantFlowVo, FlowTypeEnum flowTypeEnum, PmsOperator pmsOperator, CommonFlow commonFlow,Merchant merchant) {
        MerchantFlowVo flowVo = new MerchantFlowVo();
        BeanUtil.copyProperties(merchant, flowVo);
        if (FlowTypeEnum.PMS_CHANGE_SALER.getFlowTopicType().equals(flowTypeEnum.getFlowTopicType()) ||
                FlowTypeEnum.PMS_MAINSTAY_CHANGE_SALER.getFlowTopicType().equals(flowTypeEnum.getFlowTopicType())) {
            MerchantSaler merchantSaler = merchantSalerFacade.getByMchNo(merchant.getMchNo());
            flowVo.setSalerId(merchantSaler.getSalerId());
            flowVo.setSalerName(merchantSaler.getSalerName());
            flowVo.setAgentNo(merchant.getAgentNo());
            flowVo.setAgentName(merchant.getAgentName());
        }
        MerchantBaseInfoVo oldInfo = HANDLE.get(flowTypeEnum.getFlowTopicType()).apply(flowVo);
        MerchantBaseInfoVo newInfo = HANDLE.get(flowTypeEnum.getFlowTopicType()).apply(merchantFlowVo);

        if (FlowTypeEnum.PMS_CHANGE_ACCOUNT.getFlowTopicType().equals(flowTypeEnum.getFlowTopicType()) ||
                FlowTypeEnum.PMS_MAINSTAY_CHANGE_ACCOUNT.getFlowTopicType().equals(flowTypeEnum.getFlowTopicType())) {
            // 查出旧的
            Map<String, Object> maps = accountFacade.getAccountInfoOfMap(merchant.getMchNo());
            @SuppressWarnings("unchecked")
            Map<String, List<Map<String, Object>>> oldAccountInfo = buildAccounts((List<Map<String, Object>>) maps.get("employerAccountInfo"));
            MerchantBankAccount oldAccount = (MerchantBankAccount) maps.get("bankAccount");
            oldInfo.setAccount(oldAccount);
            oldInfo.setAccountInfo(oldAccountInfo);
            MerchantAccountInfoVo newBackInfo = merchantFlowVo.getBankAccount();
            log.info("账户信息 : {}", JSONObject.toJSON(newBackInfo));
            Map<String, List<Map<String, Object>>> newAccountInfo = merchantFlowVo.getAccounts();
            newInfo.setAccount(newBackInfo.toMerchantBankAccount(newBackInfo));
            newInfo.setAccountInfo(newAccountInfo);
        }
        newInfo.setOperatorInfo(pmsOperator.getId(), pmsOperator.getRealName(), merchantFlowVo.getFlowId());
        log.info("旧数据 : {}, 新数据 : {}", JSONObject.toJSON(oldInfo), JSONObject.toJSON(newInfo));
        infoChangeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
    }

    @Permission("pms:merchantFlow:changeAccount")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "修改账户信息")
    @PostMapping("changeAccount")
    public RestResult<Map<String, Object>> changeAccount(@Validated(IAccountValid.class) @RequestBody FlowStartDto<MerchantFlowVo> flowStartDto, @CurrentUser PmsOperator pmsOperator) {
        MerchantFlowVo merchantFlowVo = flowStartDto.getExtObj();

        Merchant merchant = merchantFacade.getByMchNo(merchantFlowVo.getMchNo());
        if (merchant.getAuthStatus().intValue() != AuthStatusEnum.SUCCESS.getValue()){
            return RestResult.error("商户尚未完成主体信息认证，无法发起审批");
        }

        FlowTypeEnum flowTypeEnum = (merchantFlowVo.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) ?
                FlowTypeEnum.PMS_CHANGE_ACCOUNT : FlowTypeEnum.PMS_MAINSTAY_CHANGE_ACCOUNT;

        flowStartDto.setBusinessKey(merchantFlowVo.getMchNo());
        flowStartDto.setFlowTopicName(String.join("-", flowTypeEnum.getDesc(), merchantFlowVo.getMchName()));

        CommonFlow commonFlow = startFlow(flowStartDto, flowTypeEnum, merchantFlowVo, pmsOperator,
                MessageMsgDest.TOPIC_APPROVAL_ASYNC, MessageMsgDest.TAG_CHANGE_ACCOUNT);
        merchantFlowVo.setFlowId(commonFlow.getId());
        compareAndRecord(merchantFlowVo, flowTypeEnum, pmsOperator, commonFlow,merchant);
        return RestResult.success(commonFlow.buildReturn());
    }

    @Permission("pms:merchantFlow:changePrincipal")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "修改负责人")
    @PostMapping("changePrincipal")
    public RestResult<Map<String, Object>> changeContact(@Validated(IContactValid.class) @RequestBody FlowStartDto<MerchantFlowVo> flowStartDto, @CurrentUser PmsOperator pmsOperator) {
        MerchantFlowVo merchantFlowVo = flowStartDto.getExtObj();
        Merchant merchant = merchantFacade.getByMchNo(merchantFlowVo.getMchNo());
        FlowTypeEnum flowTypeEnum = (merchantFlowVo.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) ?
                FlowTypeEnum.COMMON_CHANGE_PRINCIPAL : FlowTypeEnum.COMMON_MAINSTAY_CHANGE_PRINCIPAL;
        flowStartDto.setBusinessKey(merchantFlowVo.getMchNo());
        flowStartDto.setFlowTopicName(String.join("-", flowTypeEnum.getDesc(), merchantFlowVo.getMchName()));
        CommonFlow commonFlow = startFlow(flowStartDto, flowTypeEnum, merchantFlowVo, pmsOperator,
                MessageMsgDest.TOPIC_APPROVAL_ASYNC, MessageMsgDest.TAG_CHANGE_PRINCIPAL);
        merchantFlowVo.setFlowId(commonFlow.getId());
        compareAndRecord(merchantFlowVo, flowTypeEnum, pmsOperator, commonFlow,merchant);
        return RestResult.success(commonFlow.buildReturn());
    }


    @Permission("pms:merchantFlow:changeSaler")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "修改销售/合伙人")
    @PostMapping("changeSaler")
    public RestResult<Map<String, Object>> changeSaler(@Validated(ISalerValid.class) @RequestBody FlowStartDto<MerchantFlowVo> flowStartDto, @CurrentUser PmsOperator pmsOperator) {
        MerchantFlowVo merchantFlowVo = flowStartDto.getExtObj();

        Merchant merchant = merchantFacade.getByMchNo(merchantFlowVo.getMchNo());

        FlowTypeEnum flowTypeEnum = (merchantFlowVo.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) ?
                FlowTypeEnum.PMS_CHANGE_SALER : FlowTypeEnum.PMS_MAINSTAY_CHANGE_SALER;
        flowStartDto.setBusinessKey(merchantFlowVo.getMchNo());
        flowStartDto.setFlowTopicName(String.join("-", flowTypeEnum.getDesc(), merchantFlowVo.getMchName()));
        CommonFlow commonFlow = startFlow(flowStartDto, flowTypeEnum, merchantFlowVo, pmsOperator,
                MessageMsgDest.TOPIC_APPROVAL_ASYNC, MessageMsgDest.TAG_CHANGE_SALER);
        merchantFlowVo.setFlowId(commonFlow.getId());
        compareAndRecord(merchantFlowVo, flowTypeEnum, pmsOperator, commonFlow,merchant);
        return RestResult.success(commonFlow.buildReturn());
    }

    private CommonFlow startFlow(FlowStartDto<?> flowStartDto, FlowTypeEnum flowTypeEnum, MerchantFlowVo merchantFlowVo, PmsOperator pmsOperator, String defaultTopic, String defaultTag) {
        //判断是由还有没结束的流程
        if (flowFacade.isExistNotFinishedFlow(flowTypeEnum,flowStartDto.getBusinessKey())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前商户变更流程未审核完毕，请审核完毕后再提交");
        }
        //修改人
        merchantFlowVo.setUpdator(pmsOperator.getRealName());

        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(pmsOperator.getId());
        flowUserVo.setUserName(pmsOperator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());

        //流程主体参数
        ProcessVo processVo = new ProcessVo();
        processVo.setBusinessKey(flowStartDto.getBusinessKey());
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(flowTypeEnum.name());
        processVo.setFlowTopicType(flowTypeEnum.getFlowTopicType());
        processVo.setFlowTopicName(flowStartDto.getFlowTopicName());
        processVo.setExtInfo(JsonUtil.toString(merchantFlowVo));
        processVo.setRemark(flowStartDto.getRemark());

        //设置默认监听器topic，tag
        flowStartDto.setCondition(flowStartDto.getCondition() == null ? new HashMap<>() : flowStartDto.getCondition());
        flowStartDto.getCondition().put(FlowConstant.DEFAULT_TOPIC, defaultTopic);
        flowStartDto.getCondition().put(FlowConstant.DEFAULT_TAG, defaultTag);

        //设置流程变量
        flowStartDto.getCondition().put("mchNo", merchantFlowVo.getMchNo());
        return flowFacade.startProcessByProcessDefinitionKey(processVo, flowUserVo, flowStartDto.getParticipant(), flowStartDto.getCondition());
    }

    @PostMapping("record")
    public RestResult<List<MerchantInfoChangeRecord>> record(@RequestBody MerchantFlowVo flowVo, @CurrentUser PmsOperator pmsOperator) {
        return RestResult.success(infoChangeRecordFacade.list(flowVo.getFlowId()));
    }

    @SuppressWarnings("unchecked")
    public static Map<String, List<Map<String, Object>>> buildAccounts(List<Map<String, Object>> employerAccountInfo) {
        if (employerAccountInfo == null || employerAccountInfo.size() == 0) {
            return new HashMap<>();
        }
        Map<String, List<Map<String, Object>>> accounts = new HashMap<>();
        employerAccountInfo.forEach(x -> {
            List<Map<String, Object>> list = new ArrayList<>();
            List<EmployerAccountInfo> employerAccountInfoList = (List<EmployerAccountInfo>) x.get("employerAccountInfo");
            if (employerAccountInfoList != null && employerAccountInfoList.size() > 0) {
                employerAccountInfoList.forEach(map -> {
                    Map<String, Object> channelMap = new HashMap<>();
                    channelMap.put("channelType", map.getChannelType());
                    channelMap.put("channelName", map.getPayChannelName());
                    channelMap.put("payChannelNo", map.getPayChannelNo());
                    list.add(channelMap);
                });
            }
            accounts.put((String) x.get("mainstayNo"), list);
        });
        return accounts;
    }
}


