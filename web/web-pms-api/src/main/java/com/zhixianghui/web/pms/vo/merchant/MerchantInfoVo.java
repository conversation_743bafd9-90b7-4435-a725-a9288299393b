package com.zhixianghui.web.pms.vo.merchant;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商户信息vo，结合多表
 * <AUTHOR>
 * @date 2020/8/14
 **/
@Data
public class MerchantInfoVo implements Serializable {
    private Long id;
    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 商户状态
     * @see com.zhixianghui.common.statics.enums.merchant.MchStatusEnum
     */
    private Integer mchStatus;

    /**
     * 认证状态
     * @see com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum
     */
    private Integer authStatus;

    /**
     * 商户类型
     * @see com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum
     */
    private Integer merchantType;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 销售用户id
     */
    private Long salerId;

    /**
     * 销售名称
     */
    private String salerName;

    /**
     * 销售部门id
     */
    private Long saleDepartmentId;

    /**
     * 销售部门名称
     */
    private String saleDepartmentName;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系手机号
     */
    private String contactPhone;

    /**
     * 电签账户状态
     */
    private Integer signAccountStatus;
}
