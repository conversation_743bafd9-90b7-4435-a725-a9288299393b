package com.zhixianghui.web.pms.controller.internal;

import com.alibaba.fastjson.JSONArray;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.fee.entity.MerchantFeeOrder;
import com.zhixianghui.facade.fee.service.MerchantFeeOrderQueryFacade;

import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.entity.CommonFlowLog;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantSalerFacade;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/3/30
 * 内部请求, 只用于初始化数据或者进行补偿
 */
@Slf4j
@RestController
@RequestMapping("internal")
public class InternalController {
    @Reference
    private MerchantSalerFacade merchantSalerFacade;
    @Reference
    private MerchantFeeOrderQueryFacade queryFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private AgentFacade agentFacade;

    private static final String DEFAULT_IP = "127.0.0.1";
    private static final int CURRENT_PAGE = 1;
    private static final int PAGE_SIZE = 1000;

//    public String initFlowLog(HttpServletRequest request) {
//        List<CommonFlow> list = flowFacade.listFlow();
//        if (CollectionUtils.isEmpty(list)) {
//            return "empty flow list";
//        }
//        List<CommonFlowLog> commonFlowLogList = flowFacade.listFlowLog();
//        Map<Long, String> result = list.stream().collect(Collectors.toMap(CommonFlow :: getId, CommonFlow :: getBusinessKey));
//        for (CommonFlowLog flowLog : commonFlowLogList) {
//            if (flowLog.getPlatform() == PlatformSource.OPERATION.getValue()) {
//                continue;
//            }
//            String mchNo = result.get(flowLog.getId());
//            if (flowLog.getPlatform() == PlatformSource.AGENT.getValue()) {
//                if (agentFacade.getByAgentNo(mchNo) == null) {
//                    continue;
//                }
//            } else {
//                if (merchantFacade.getByMchNo(mchNo) == null) {
//                    continue;
//                }
//            }
//            flowLog.setBelong(mchNo);
//        }
//        flowFacade.update(commonFlowLogList);
//        return "OK";
//    }


    @RequestMapping("initMerchantFeeOrder")
    public String initMerchantFeeOrder(HttpServletRequest request) {
        String ip = request.getRemoteAddr();
        log.info("ip value is = {}", ip);
        if (!DEFAULT_IP.equals(ip)){
            return "denied";
        }
        handle();
        return "OK";
    }

    private void handle() {
        int currentPage = CURRENT_PAGE;
        while (true) {
            // 查询
            List<MerchantFeeOrder> merchantFeeOrderList = select(currentPage, PAGE_SIZE);
            if (CollectionUtils.isEmpty(merchantFeeOrderList)) {
                break;
            }
            // 更新
            update(merchantFeeOrderList);
            currentPage++;
        }
    }

    private void update(List<MerchantFeeOrder> merchantFeeOrderList) {
        List<String> mchNoList = merchantFeeOrderList.stream().map(MerchantFeeOrder::getMchNo).distinct().collect(Collectors.toList());
        List<MerchantSaler> merchantSaleList = merchantSalerFacade.getBatchMerchantMchNO(mchNoList);
        if (CollectionUtils.isEmpty(merchantSaleList)) {
            log.info("当前的商户批次在商户销售表中不存在 : {}", JSONArray.toJSONString(mchNoList));
            return;
        }
        Map<String, MerchantSaler> merchantSaleMap = merchantSaleList.stream().collect(Collectors.toMap(MerchantSaler::getMchNo, item -> item));
        merchantFeeOrderList.forEach(item -> {
            item.setDepartmentName(merchantSaleMap.get(item.getMchNo()).getSaleDepartmentName());
            item.setDepartmentId(merchantSaleMap.get(item.getMchNo()).getSaleDepartmentId());
            item.setSalerId(merchantSaleMap.get(item.getMchNo()).getSalerId());
            item.setSalerName(merchantSaleMap.get(item.getMchNo()).getSalerName());
        });
        queryFacade.updateBatchMerchantFeeOrder(merchantFeeOrderList);
    }

    private List<MerchantFeeOrder> select(int currentPage, int pageSize) {
        PageResult<List<MerchantFeeOrder>> result = queryFacade.listPage(new HashMap<>(), PageParam.newInstance(currentPage, pageSize));
        return result == null ? null : result.getData();
    }
}
