package com.zhixianghui.web.pms.vo.sign;

import com.zhixianghui.facade.trade.vo.SignCustomizeTemplateVo;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Date 2021/6/7 18:23
 */
@Data
public class SignTemplateVo extends SignCustomizeTemplateVo {

    private MultipartFile sourceFile;

    private Boolean isChangeFile = false;

    public SignTemplateVo() {}

    public SignTemplateVo(String originalFilename, byte[] fileBytes) {
        this.protocolName = originalFilename;
        this.bytes = fileBytes;
    }
}
