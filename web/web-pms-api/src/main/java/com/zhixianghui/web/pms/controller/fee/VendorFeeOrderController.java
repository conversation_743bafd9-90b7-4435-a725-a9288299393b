package com.zhixianghui.web.pms.controller.fee;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.data.service.CkOrderFacade;
import com.zhixianghui.facade.data.service.CkVendorFeeOrderFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.export.vo.ExportVo;
import com.zhixianghui.facade.fee.entity.AgentFeeOrder;
import com.zhixianghui.facade.fee.entity.SalesFeeOrder;
import com.zhixianghui.facade.fee.entity.VendorFeeOrder;
import com.zhixianghui.facade.fee.entity.VendorFeeRule;
import com.zhixianghui.facade.fee.service.AgentFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.VendorFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.VendorFeeRuleFacade;
import com.zhixianghui.facade.fee.vo.VendorFeeStatisticVo;
import com.zhixianghui.facade.fee.vo.VendorFeeSumVo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.fee.SalesFeeOrderQueryVo;
import com.zhixianghui.web.pms.vo.fee.VendorFeeOrderQueryVo;
import com.zhixianghui.web.pms.vo.fee.VendorFeeRelationQueryVo;
import com.zhixianghui.web.pms.vo.fee.VendorFeeRelationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应商成本订单
 */
@Slf4j
@RestController
@RequestMapping("vendorFeeOrder")
public class VendorFeeOrderController{
    @Reference
    private AgentFeeOrderQueryFacade agentQueryFacade;
    @Reference
    private VendorFeeOrderQueryFacade queryFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private CkVendorFeeOrderFacade ckVendorFeeOrderFacade;

    @PostMapping("exportVendorStatistics")
    public RestResult exportVendorStatistics (@RequestBody @Valid VendorFeeOrderQueryVo vo,@CurrentUser PmsOperator operator){
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        if (vo.getExistAgent() != null){
            List<String> mchNoList = merchantFacade.listMerchantNo(vo.getExistAgent());
            paramMap.put("mchNos",mchNoList);
        }
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.PMS_SUPPLIER_MERGE_FEE.getFileName());
        record.setReportType(ReportTypeEnum.PMS_SUPPLIER_MERGE_FEE.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setDeepPage(true);

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.PMS_SUPPLIER_MERGE_FEE.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("vendorStatistics")
    public RestResult<PageResult<List<VendorFeeStatisticVo>>> vendorStatistics(@RequestBody @Valid VendorFeeOrderQueryVo vo, @RequestBody Page<Map<String, Object>> page){
        PageParam pageParam = PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize());
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        if (vo.getExistAgent() != null) {
            List<String> mchNoList = merchantFacade.listMerchantNo(vo.getExistAgent());
            paramMap.put("mchNos",mchNoList);
        }
        PageResult<List<VendorFeeStatisticVo>> pageResult = queryFacade.vendorFeeStatistics(paramMap, pageParam);
        for (VendorFeeStatisticVo statisticVo : pageResult.getData()) {
            Merchant merchant = merchantFacade.getByMchNo(statisticVo.getMchNo());
            if (StringUtils.isBlank(merchant.getAgentNo())) {
                statisticVo.setExistAgent(false);
            } else {
                statisticVo.setExistAgent(true);
            }
        }
        return RestResult.success(pageResult);
    }

    /**
     * 供应商计费订单分页查询
     * @param vo
     * @return
     */
    @Permission("fee:vendorFeeOrder:list")
    @PostMapping("listPage")
    public RestResult<PageResult<List<VendorFeeOrder>>> listPage(@RequestBody @Valid VendorFeeOrderQueryVo vo) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);

        if (vo.getExistAgent() != null) {
            if (CollectionUtils.isEmpty(putConditionIfAgent(paramMap, vo))) {
                return RestResult.success(PageResult.newInstance(null, vo.getPageCurrent(), vo.getPageSize()));
            }
        }
        PageResult<List<VendorFeeOrder>> pageResult = queryFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(pageResult);
    }

    private List<String> putConditionIfAgent(Map<String, Object> paramMap, VendorFeeOrderQueryVo vo) {
        PageResult<List<AgentFeeOrder>> pageResult = agentQueryFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getData())) {
            return null;
        }

        List<String> platTrxNoList = pageResult.getData().stream().map(AgentFeeOrder::getPlatTrxNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(platTrxNoList)) {
            return null;
        }
        paramMap.put("platTrxNoList", platTrxNoList);
        return platTrxNoList;
    }

    /**
     * 供应商计费订单分页查询
     * @param vo
     * @return
     */
    @Permission("fee:vendorFeeOrder:sum")
    @PostMapping("sumVendorFeeOrder")
    public RestResult<VendorFeeSumVo> sumVendorFeeOrder(@RequestBody @Valid VendorFeeOrderQueryVo vo) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        VendorFeeSumVo sumVo = queryFacade.sumVendorFeeOrder(paramMap);
        return RestResult.success(sumVo);
    }

    /**
     * 供应商成本订单导出
     *
     * @param vo     .
     * @return .
     */
    @RequestMapping("exportVendorFeeOrder")
    @Permission("fee:vendorFeeOrder:export")
    public RestResult<String> exportVendorFeeOrder(@Valid @RequestBody VendorFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.VENDOR_FEE_ORDER.getFileName());
        record.setReportType(ReportTypeEnum.VENDOR_FEE_ORDER.getValue());
        record.setParamJson(JsonUtil.toString(vo));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.VENDOR_FEE_ORDER.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
