package com.zhixianghui.web.pms.vo.official;

import com.zhixianghui.common.util.validator.Email;
import com.zhixianghui.common.util.validator.Phone;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @ClassName OfficialMailVo
 * @Description TODO
 * @Date 2021/9/3 17:55
 */
@Data
public class OfficialMailVo {

    @NotNull(message = "合作商类型不能为空")
    private Integer type;

    @NotEmpty(message = "姓名不能为空")
    private String username;

    private String businessName;

    private String industry;

    @NotEmpty(message = "联系方式不能为空")
    @Phone(message = "联系电话有误")
    private String phone;

    private String remark;
}
