package com.zhixianghui.web.pms.controller.approval;

import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.approval.ApprovalFlowBiz;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.approval.req.ApprovalFlowQueryVo;
import com.zhixianghui.web.pms.vo.approval.res.ApprovalListResVo;
import com.zhixianghui.web.pms.vo.approval.res.ApprovalResVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 审批流程Controller
 * @date 2020-08-17 09:44
 **/
@RestController
@RequestMapping("/approval")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApprovalFlowController extends BaseController {

    private final ApprovalFlowBiz approvalFlowBiz;

    /**
     * 收到的审批
     * @param approvalFlowQueryVo 查询参数
     * @param pageVo 分页参数
     * @return 收到的审批列表
     */
    @GetMapping("/listReceived")
    public RestResult<PageResult<List<ApprovalListResVo>>> listReceivedApproval(@Validated ApprovalFlowQueryVo approvalFlowQueryVo, PageVo pageVo, @CurrentUser PmsOperator currentOperator){
        PageResult<List<ApprovalListResVo>> pageResult = approvalFlowBiz.listReceivedApproval(approvalFlowQueryVo,pageVo,currentOperator);
        return RestResult.success(pageResult);
    }

    /**
     * 发出的审批
     * @param approvalFlowQueryVo 查询参数
     * @param pageVo 分页参数
     * @return 发出的审批列表
     */
    @GetMapping("/listSend")
    public RestResult<PageResult<List<ApprovalListResVo>>> listSendApproval(@Validated ApprovalFlowQueryVo approvalFlowQueryVo, PageVo pageVo, @CurrentUser PmsOperator currentOperator){
        PageResult<List<ApprovalListResVo>> pageResult = approvalFlowBiz.listSendApproval(approvalFlowQueryVo,pageVo,currentOperator);
        return RestResult.success(pageResult);
    }

    /**
     * 待我审批的审批流程
     * @param approvalFlowQueryVo 查询参数
     * @param pageVo 分页参数
     * @return 发出的审批列表
     */
    @GetMapping("/listPending")
    public RestResult<PageResult<List<ApprovalListResVo>>> listPendingApproval(@Validated ApprovalFlowQueryVo approvalFlowQueryVo, PageVo pageVo, @CurrentUser PmsOperator currentOperator){
        approvalFlowQueryVo.setStatus(FlowStatus.PENDING.getValue());
        PageResult<List<ApprovalListResVo>> pageResult = approvalFlowBiz.listPendingApproval(approvalFlowQueryVo,pageVo,currentOperator);
        return RestResult.success(pageResult);
    }

    /**
     * 获取单个审批的内容
     * @param approvalFlowId 查询参数
     * @return 发出的审批列表
     */
    @GetMapping("/getApproval")
    public RestResult<ApprovalResVo> getSingleApproval(@RequestParam Long approvalFlowId, @CurrentUser PmsOperator currentOperator){
        ApprovalResVo approvalResVo = approvalFlowBiz.getSingleApproval(approvalFlowId,currentOperator);
        return RestResult.success(approvalResVo);
    }

    /**
     * 撤回审批流程
     * @param approvalFlowId 审批流程id
     * @return 结果
     */
    @PostMapping("/cancelApproval")
    public RestResult<String> cancelApproval(@RequestParam Long approvalFlowId, @CurrentUser PmsOperator currentOperator){
        approvalFlowBiz.cancelApproval(approvalFlowId,currentOperator);
        return RestResult.success("撤回成功");
    }

    /**
     * 更新附加信息
     * @return 操作结果
     */
    @PostMapping("/updateExtInfo")
    public RestResult<String> updateExtInfo(@Validated @RequestBody UpdateExtInfoVo updateExtInfoVo, @CurrentUser PmsOperator currentOperator){
        approvalFlowBiz.updateExtInfo(updateExtInfoVo, currentOperator);
        return RestResult.success("更新成功");
    }
}
