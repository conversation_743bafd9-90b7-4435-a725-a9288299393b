package com.zhixianghui.web.pms.vo.approval.res;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowStatus;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-08-17 14:37
 **/
@Data
public class ApprovalResVo {
    private Long id;

    /**
     * 步骤序号
     */
    private Integer stepNum;

    /**
     * 发起人
     */
    private Long initiatorId;

    /**
     * 发起人名称
     */
    private String initiatorName;

    /**
     * 流程主题(创建商户等)
     */
    private Integer flowTopicType;

    /**
     * 流程主题名称
     */
    private String flowTopicName;

    /**
     * 流程开始时间
     */
    private Date createTime;

    /**
     * 流程更新时间
     */
    private Date updateTime;

    /**
     * 流程结束时间
     */
    private Date endTime;

    /**
     * 流程状态 {@link FlowStatus}
     */
    private Integer status;

    /**
     * 额外信息(备注,审批信息等)
     */
    private String extInfo;

    /**
     * 平台来源
     */
    private Integer platform;

    public static ApprovalResVo buildVo(ApprovalFlow approvalFlow) {
        ApprovalResVo approvalResVo = new ApprovalResVo();
        BeanUtils.copyProperties(approvalFlow, approvalResVo);
        return approvalResVo;
    }
}
