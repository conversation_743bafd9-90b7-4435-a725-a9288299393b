package com.zhixianghui.web.pms.controller.employee;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.employee.dto.JobQueryDto;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.enums.ApproveEvent;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/job")
public class JobController {

    @Reference
    private JobFacade jobFacade;


    @PostMapping("/approved/{ids}")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "审核任务")
    public RestResult<String> approved(@PathVariable("ids") Long[] ids
            , @RequestParam("event") ApproveEvent event
            , @CurrentUser PmsOperator pmsOperator) {
        jobFacade.approved(ids,event,pmsOperator.getRealName());
        return RestResult.success("审批成功");
    }

    @PostMapping("page")
    public RestResult<IPage> pageJobs(@RequestBody Page page, @RequestBody JobQueryDto jobQueryDto) {
        final Page<Job> jobPage = jobFacade.pageJob(page, BeanUtil.toMap(jobQueryDto));
        return RestResult.success(jobPage);
    }

    @GetMapping("/getById/{id}")
    public RestResult getJobById(@PathVariable("id") Long id) {
        return RestResult.success(jobFacade.getJobById(id));
    }

    @PostMapping("listJobListOnGrant")
    public RestResult<List<Job>> listJobListOnGrant(@RequestParam("mchNo") String mchNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("employerNo", mchNo);
        final List<Job> jobList = jobFacade.listJobListOnGrant(param);
        return RestResult.success(jobList);
    }

}
