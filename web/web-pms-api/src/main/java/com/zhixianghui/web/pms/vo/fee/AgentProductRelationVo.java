package com.zhixianghui.web.pms.vo.fee;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class AgentProductRelationVo {
    private Long id;

    /**
     * 商户编号
     */
    @NotEmpty(message = "合伙人编号不能为空")
    private String agentNo;

    /**
     * 商户名称
     */
    @NotEmpty(message = "合伙人名称不能为空")
    private String agentName;

    /**
     * 产品编号
     */
    @NotEmpty(message = "产品编号不能为空")
    private String productNo;

    /**
     * 产品名称
     */
    @NotEmpty(message = "产品名称不能为空")
    private String productName;

    /**
     * 描述
     */
    private String description;
}