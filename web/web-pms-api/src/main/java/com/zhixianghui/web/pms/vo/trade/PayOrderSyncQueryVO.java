package com.zhixianghui.web.pms.vo.trade;

import com.zhixianghui.common.statics.enums.tradesync.OrderCheckStatusEnum;
import com.zhixianghui.common.statics.enums.tradesync.OrderSyncStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * Author: Cmf
 * Date: 2019.12.2
 * Time: 16:27
 * Description:
 */
@Getter
@Setter
public class PayOrderSyncQueryVO implements Serializable {
    private Date createTimeBegin;
    private Date createTimeEnd;
    private String merchantNo;
    private String mchPayTrxNo;
    private String platPayTrxNo;
    private String channelPayTrxNo;
    private String payChannel;

    /**
     * {@link OrderSyncStatusEnum}
     */
    private Integer orderSyncStatus;
    /**
     * {@link OrderCheckStatusEnum}
     */
    private Integer orderCheckStatus;

}
