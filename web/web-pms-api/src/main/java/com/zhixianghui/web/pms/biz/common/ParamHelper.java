package com.zhixianghui.web.pms.biz.common;

import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ParamHelper {

    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    public List<Long> hanldSaler(PmsOperator operator, Map<String, Object> paramMap) {
        List<Long> salerIds = new ArrayList<>();
        if (paramMap.get("salerIds") != null && JSONUtil.parseArray(paramMap.get("salerIds")).size()>0) {
            paramMap.put("salerIds", paramMap.get("salerIds"));
            salerIds.addAll(JSONUtil.parseArray(paramMap.get("salerIds")).toList(Long.class));
        }else {
            // 销售部只能查自己及下属的商户
            if (operator.getDepartmentId() != null) {
                PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
                if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {

                    try{
                        List<PmsOperator> salers = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
                        salerIds = salers.stream().map(PmsOperator::getId).collect(Collectors.toList());
                    } catch (Exception e){
                        log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
                        salerIds.add(operator.getId());
                    }

                    paramMap.put("salerIds", salerIds);
                }else {
                    Long roleId = pmsOperatorFacade.getSaleRole();
                    if (roleId == null) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("销售不存在");
                    }
                    List<PmsRoleOperator> pmsRoleOperator = pmsOperatorFacade.listOperatorByRoleId(Math.toIntExact(roleId));
                    salerIds = pmsRoleOperator.stream().map(PmsRoleOperator::getOperatorId).collect(Collectors.toList());
                }
            }
        }
        return salerIds;
    }

}
