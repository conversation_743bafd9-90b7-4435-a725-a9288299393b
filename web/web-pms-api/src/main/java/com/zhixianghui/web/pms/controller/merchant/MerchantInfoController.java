package com.zhixianghui.web.pms.controller.merchant;


import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.EditTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.export.vo.ExportVo;
import com.zhixianghui.facade.flow.dto.FlowStartDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowStatusEnum;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.AgreementFacade;
import com.zhixianghui.facade.merchant.service.MerchantBankAccountFacade;
import com.zhixianghui.facade.merchant.service.MerchantCacheFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerCooperateFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantExpressInfoFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantFileFacade;
import com.zhixianghui.facade.merchant.service.MerchantInfoChangeRecordFacade;
import com.zhixianghui.facade.merchant.service.MerchantInvoiceInfoFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.service.MerchantSalerFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffFacade;
import com.zhixianghui.facade.merchant.vo.EmployerFullInfoUpdateVo;
import com.zhixianghui.facade.merchant.vo.EmployerMainUpdateVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerAddVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerInsertVo;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerMainAuthVo;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.facade.merchant.vo.merchant.EmployerCoopValid;
import com.zhixianghui.facade.merchant.vo.merchant.ListMerchantProductVo;
import com.zhixianghui.facade.merchant.vo.merchant.MainstayCoopValid;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantBaseValid;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantBusinessValid;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantMainValid;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.approval.ApprovalFlowBiz;
import com.zhixianghui.web.pms.biz.merchant.MerchantBiz;
import com.zhixianghui.web.pms.biz.record.EditHandleContext;
import com.zhixianghui.web.pms.vo.merchant.BankAccountVo;
import com.zhixianghui.web.pms.vo.merchant.MerchantInvoiceInfoVo;
import com.zhixianghui.web.pms.vo.merchant.employer.ChangeLeaderVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 商户审批
 *
 * <AUTHOR>
 * @date 2020-08-10
 */
@RestController
@RequestMapping("merchantEmployer")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantInfoController extends com.zhixianghui.web.pms.controller.flow.CommonFlow {
    private final ApprovalFlowBiz approvalFlowBiz;
    private final MerchantBiz merchantBiz;

    @Reference
    private PmsPermissionFacade pmsPermissionFacade;
    @Reference
    private MerchantEmployerMainFacade merchantEmployerMainFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MerchantEmployerFacade merchantEmployerFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private ApprovalFlowFacade approvalFlowFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private MerchantBankAccountFacade accountFacade;
    @Reference
    private MerchantEmployerCooperateFacade cooperateFacade;
    @Reference(retries = -1)
    private MerchantEmployerMainFacade mainFacade;
    @Reference
    private MerchantFileFacade fileFacade;
    @Reference
    private EmployerStaffFacade employerStaffFacade;
    @Reference
    private SupplierStaffFacade supplierStaffFacade;
    @Reference
    private MerchantCacheFacade merchantCacheFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private MerchantInvoiceInfoFacade merchantInvoiceInfoFacade;
    @Reference
    private MerchantFileFacade MerchantFileFacade;
    @Reference
    private MerchantSalerFacade merchantSalerFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private AgentFacade agentFacade;
    @Reference
    private MerchantEmployerQuoteFacade merchantEmployerQuoteFacade;
    @Reference
    private MerchantInfoChangeRecordFacade infoChangeRecordFacade;
    @Reference
    private AgreementFacade agreementFacade;
    @Reference
    private MerchantExpressInfoFacade merchantExpressInfoFacade;

    @PostMapping("submitAudit")
    @Permission("merchant:employer:submit")
    @Logger(type = OperateLogTypeEnum.CREATE,name = "提交商户审核信息")
    public RestResult<Map<String,Object>> submitAudit(@Valid @RequestBody FlowStartDto<MerchantEmployerInsertVo> flowStartDto, @CurrentUser PmsOperator operator){
        // 生成商户编号
        String mchNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getPrefix(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getKey(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getWidth());
        MerchantEmployerInsertVo merchantEmployerInsertVo = flowStartDto.getExtObj();
        merchantEmployerInsertVo.setMchNo(mchNo);
        //构建流程参数
        CommonFlow commonFlow = startFlow(operator,FlowTypeEnum.PMS_MCH_APPLY,merchantEmployerInsertVo.getMchNo(),merchantEmployerInsertVo.getMchName(),flowStartDto,merchantEmployerInsertVo);

        notifyFacade.sendOne(MessageMsgDest.TOPIC_APPROVAL_ASYNC, NotifyTypeEnum.APPROVAL_NOTIFY.getValue(), MessageMsgDest.TAG_APPROVAL_CREATE_MERCHANT_NOTIFY, JSON.toJSONString(commonFlow));

        //返回值
        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @PostMapping("applyNewMerchant")
    @Permission("merchant:employer:submit")
    @Logger(type = OperateLogTypeEnum.CREATE,name = "运营后台-新建商户")
    public  RestResult<String>applyNewMerchant(@Valid @RequestBody MerchantEmployerAddVo merchantEmployerAddVo, @CurrentUser PmsOperator operator){
        // 生成商户编号
        String mchNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getPrefix(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getKey(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getWidth());

        merchantEmployerAddVo.setMchNo(mchNo);
        merchantEmployerAddVo.setPmsOperator(operator);
//        notifzyFacade.sendOne(MessageMsgDest.TOPIC_APPLY_NEW_MERCHANT, NotifyTypeEnum.APPLY_NEW_MERCHANT.getValue(), MessageMsgDest.TAG_APPLY_NEW_MERCHANT, JSON.toJSONString(merchantEmployerAddVo));

        merchantEmployerFacade.applyMerchant(merchantEmployerAddVo);
        //返回值
        return RestResult.success("success");
    }

//    /**
//     * 提交商户审核信息
//     *
//     * @param auditVO .
//     * @return .
//     */
//    @RequestMapping("submitAudit")
//    @Permission("merchant:employer:submit")
//    @Logger(type = OperateLogTypeEnum.CREATE, name = "提交商户审核信息")
//    public RestResult<Map<String, Object>> submitAudit(@Valid @RequestBody MerchantEmployerInsertVo auditVO, @CurrentUser PmsOperator operator) {
//        // 生成商户编号
//        String mchNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getPrefix(),
//                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getKey(),
//                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getWidth());
//        auditVO.setMchNo(mchNo);
//        log.info("商户入网：{}", JsonUtil.toString(auditVO));
//        ApprovalFlow approvalFlow = new ApprovalFlow();
//        approvalFlow.setVersion(0);
//        approvalFlow.setStepNum(0);
//        approvalFlow.setInitiatorId(operator.getId());
//        approvalFlow.setInitiatorName(operator.getRealName());
//        approvalFlow.setFlowTopicType(FlowTopicType.CREATE_MERCHANT.getValue());
//        approvalFlow.setFlowTopicName(String.join("-", FlowTopicType.CREATE_MERCHANT.getDesc(), auditVO.getMchName()));
//        approvalFlow.setCreateTime(new Date());
//        approvalFlow.setUpdateTime(new Date());
//        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
//        approvalFlow.setExtInfo(JsonUtil.toString(auditVO));
//        approvalFlow.setPlatform(PlatformSource.OPERATION.getValue());
//
//        ApprovalInfoVo infoVo = approvalFlowFacade.createFlow(approvalFlow);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("id", infoVo.getId());
//        result.put("createTime", DateUtil.formatDateTime(approvalFlow.getCreateTime()));
//        result.put("submitName", operator.getRealName());
//        return RestResult.success(result);
//    }

    @PostMapping("mainAuth")
    @Permission("merchant:employer:mainAuth")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "主体认证")
    public RestResult<Map<String,Object>> mainAuth(@Valid @RequestBody FlowStartDto<MerchantEmployerMainAuthVo> flowStartDto,@CurrentUser PmsOperator operator){
        MerchantEmployerMainAuthVo authVo = flowStartDto.getExtObj();
        Merchant merchant = merchantQueryFacade.getByMchNo(authVo.getMchNo());
        if (!Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.FAIL.getValue())
                && !Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.UN_AUTH.getValue())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许提交主体认证");
        }

        authVo.setMerchantType(merchant.getMerchantType());
        authVo.setMchName(merchant.getMchName());
        authVo.setContactName(merchant.getContactName());
        authVo.setContactPhone(merchant.getContactPhone());
        authVo.setOperatorLoginName(operator.getRealName());

        /**
         * 检查代征关系是否建立
         */
        final Map<String, List<Map<String,Object>>> accounts = authVo.getAccounts();
        accounts.forEach((mainstayNo, value) -> {
            // 查询代征关系表
            final EmployerMainstayRelation emRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(merchant.getMchNo(), mainstayNo);
            if (emRelation == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征关系未建立:代征主体编号["+mainstayNo+"]");
            }
        });

        merchantEmployerFacade.validMainAuthVo(authVo);
//        authVo.getPersonnels().forEach(personnel->{
//            personnel.setMchNo(authVo.getMchNo());
//            personnel.setMchName(authVo.getMchName());
//            personnel.setUpdator(operator.getRealName());
//        });
        // 校验字段
        log.info("主体认证：{}", JsonUtil.toString(authVo));


        //判断数据对象类型并构建流程参数
        CommonFlow commonFlow;
        if (merchant.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            commonFlow = startFlow(operator,FlowTypeEnum.PMS_MCH_MAIN_AUTH,authVo.getMchNo(),authVo.getMchName(),flowStartDto,authVo);
        }else{
            commonFlow = startFlow(operator,FlowTypeEnum.PMS_MAINSTAY_MAIN_AUTH,authVo.getMchNo(),authVo.getMchName(),flowStartDto,authVo);
        }
//        // 商户状态更新为审核中
        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
        merchant.setUpdator(operator.getRealName());
        merchant.setUpdateTime(new Date());
        merchantFacade.update(merchant);

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

//    /**
//     * 主体认证信息
//     *
//     * @param authVo .
//     * @return .
//     */
//    @RequestMapping("mainAuth")
//    @Permission("merchant:employer:mainAuth")
//    @Logger(type = OperateLogTypeEnum.MODIFY, name = "主体认证")
//    public RestResult<Map<String, Object>> mainAuth(@Valid @RequestBody MerchantEmployerMainAuthVo authVo, @CurrentUser PmsOperator operator) {
//        Merchant merchant = merchantQueryFacade.getByMchNo(authVo.getMchNo());
//        if (!Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.FAIL.getValue())
//                && !Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.UN_AUTH.getValue())) {
//            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许提交主体认证");
//        }
//
//        authVo.setMerchantType(merchant.getMerchantType());
//        authVo.setMchName(merchant.getMchName());
//        authVo.setContactName(merchant.getContactName());
//        authVo.setContactPhone(merchant.getContactPhone());
//        authVo.setOperatorLoginName(operator.getRealName());
//        // 校验字段
//        merchantEmployerFacade.validMainAuthVo(authVo);
//        log.info("主体认证：{}", JsonUtil.toString(authVo));
//        // 保存主体认证审核记录
//        ApprovalFlow approvalFlow = new ApprovalFlow();
//        approvalFlow.setVersion(0);
//        approvalFlow.setStepNum(0);
//        approvalFlow.setInitiatorId(operator.getId());
//        approvalFlow.setInitiatorName(operator.getRealName());
//        approvalFlow.setFlowTopicType(FlowTopicType.MERCHANT_VERIFY.getValue());
//        approvalFlow.setFlowTopicName(String.join("-", FlowTopicType.MERCHANT_VERIFY.getDesc(), authVo.getMchName()));
//        approvalFlow.setCreateTime(new Date());
//        approvalFlow.setUpdateTime(new Date());
//        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
//        approvalFlow.setExtInfo(JsonUtil.toString(authVo));
//        approvalFlow.setPlatform(PlatformSource.OPERATION.getValue());
//
//        ApprovalInfoVo infoVo = approvalFlowFacade.createFlow(approvalFlow);
//
//        // 商户状态更新为审核中
//        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
//        merchant.setUpdator(operator.getRealName());
//        merchant.setUpdateTime(new Date());
//        merchantFacade.update(merchant);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("id", infoVo.getId());
//        result.put("createTime", DateUtil.formatDateTime(approvalFlow.getCreateTime()));
//        result.put("submitName", operator.getRealName());
//        return RestResult.success(result);
//    }


    /**
     * 主体认证审批编辑
     *
     * @param updateExtInfoVo
     * @param operator
     * @return
     */
    @RequestMapping("mainAuthApprovalEdit")
    @Permission("merchant:employer:mainAuth")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "主体认证审批编辑")
    public RestResult<String> mainAuthApprovalEdit(@Validated @RequestBody UpdateExtInfoVo updateExtInfoVo, @CurrentUser PmsOperator operator) {
        String newAuthStr = updateExtInfoVo.getExtInfo();
        MerchantEmployerMainAuthVo newAuth = JsonUtil.toBean(newAuthStr, MerchantEmployerMainAuthVo.class);
        Merchant merchant = merchantQueryFacade.getByMchNo(newAuth.getMchNo());

        newAuth.setMerchantType(merchant.getMerchantType());
        newAuth.setMchName(merchant.getMchName());
        newAuth.setContactName(merchant.getContactName());
        newAuth.setContactPhone(merchant.getContactPhone());
        newAuth.setOperatorLoginName(operator.getRealName());

        Merchant mch = merchantCacheFacade.getByMchNo(newAuth.getMchNo());
        if (Objects.equals(mch.getAuthStatus(), AuthStatusEnum.SUCCESS.getValue())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许编辑提交主体认证");
        }
        // 校验字段
        merchantEmployerFacade.validMainAuthVo(newAuth);
        log.info("主体认证信息审批编辑：{}", JsonUtil.toString(newAuth));

        // 更新主体认证审核记录
        updateExtInfoVo.setExtInfo(JsonUtil.toString(newAuth));
        approvalFlowBiz.updateExtInfo(updateExtInfoVo, operator);

        // 商户状态更新为审核中
        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
        merchant.setUpdator(operator.getRealName());
        merchant.setUpdateTime(new Date());
        merchantFacade.update(merchant);

        return RestResult.success("更新成功，等待审批");
    }

    /**
     * 修改商户所有基本信息
     * @param operator
     * @return
     */
    @PostMapping("updateAllMerchantMessage")
    @Permission("merchant:employer:updateMessage")
    @Logger(type = OperateLogTypeEnum.MODIFY,name = "修改商户所有基本信息")
    public RestResult<Map<String,Object>> updateAllMerchantMessage(@Valid @RequestBody FlowStartDto<EmployerFullInfoUpdateVo> flowStartDto, @CurrentUser PmsOperator operator){
        EmployerFullInfoUpdateVo vo = flowStartDto.getExtObj();
        //判断流程是否存在
        boolean isExist = flowFacade.isExistNotFinishedFlow(FlowTypeEnum.PMS_EDIT_MERCHANT,vo.getMchNo());
        if (isExist){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前商户变更流程未审核完毕，请审核完毕后再提交");
        }
        log.info("更新商户所有信息：{}",JsonUtil.toString(vo));
        Merchant mch = merchantQueryFacade.getByMchNo(vo.getMchNo());
        if (mch == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        if (!Objects.equals(mch.getAuthStatus(), AuthStatusEnum.SUCCESS.getValue())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("商户尚未激活，无法编辑信息");
        }
        String loginName = operator.getRealName();
        //更新人
        vo.setCurrentOperator(loginName);
        vo.setMerchantType(mch.getMerchantType());

        if (StringUtils.isNotBlank(vo.getAgentNo())){
            //获取合伙人
            Agent agent = agentFacade.getByAgentNo(vo.getAgentNo());
            if (agent != null){
                vo.setAgentName(agent.getAgentName());
            }
        }
        //构建流程参数
        CommonFlow commonFlow = startFlow(operator,FlowTypeEnum.PMS_EDIT_MERCHANT,vo.getMchNo(),vo.getMchName(),flowStartDto,vo);
        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());

//        //更新商户信息
//        cooperateFacade.updateAllMerchantMessage(vo);
//        //发送消息通知修改其他数据库
//        notifyFacade.sendOne(MessageMsgDest.TOPIC_MERCHANT_FULL_EDIT, NotifyTypeEnum.MERCHANT_CHANGE.getValue(),null,JsonUtil.toString(vo));
        return RestResult.success(result);
    }

    /**
     * 构建流程参数
     * @param operator
     * @param flowTypeEnum
     * @param businessKey
     * @param businessName
     * @param flowStartDto
     * @param authVo
     * @param <T>
     * @return
     */
    private <T> CommonFlow startFlow(PmsOperator operator,FlowTypeEnum flowTypeEnum,String businessKey,String businessName,FlowStartDto flowStartDto,T authVo){
        //当前用户
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(operator.getId());
        flowUserVo.setUserName(operator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        //流程主体参数
        ProcessVo processVo = new ProcessVo();
        processVo.setBusinessKey(businessKey);
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(flowTypeEnum.name());
        processVo.setFlowTopicType(flowTypeEnum.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-",flowTypeEnum.getDesc(),businessName));
        processVo.setExtInfo(JsonUtil.toString(authVo));
        processVo.setRemark(flowStartDto.getRemark());
        //设置流程变量
        flowStartDto.getCondition().put("mchNo",businessKey);
        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo,flowUserVo,flowStartDto.getParticipant(),flowStartDto.getCondition());
        return commonFlow;
    }

    /**
     * 更新主体信息
     *
     * @param vo .
     * @return .
     */
    @RequestMapping("updateMain")
    @Permission("merchant:employer:updateMain")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新主体信息")
    public RestResult<String> updateMain(@Valid @RequestBody EmployerMainUpdateVo vo, @CurrentUser PmsOperator operator) {
        log.info("更新主体信息：{}", JsonUtil.toString(vo));
        mainFacade.updateOrInsert(vo, operator.getRealName());
        return RestResult.success("更新成功");
    }

    /**
     * 更新银行账号信息
     *
     * @param vo .
     * @return .
     */
    @Deprecated
    @RequestMapping("updateBankAccount")
    @Permission("merchant:employer:updateBankAccount")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新银行账号信息")
    public RestResult<String> updateBankAccount(@Valid @RequestBody BankAccountVo vo, @CurrentUser PmsOperator operator) {
        log.info("更新银行卡信息：{}", JsonUtil.toString(vo));
        MerchantBankAccount account = accountFacade.getByMchNo(vo.getMchNo());
        Merchant mch = merchantQueryFacade.getByMchNo(vo.getMchNo());

        if (account == null) {
            account = new MerchantBankAccount();
            account.setCreateTime(new Date());
            account.setVersion(0);
        }
        account.setUpdateTime(new Date());
        account.setUpdator(operator.getRealName());
        account.setMchNo(vo.getMchNo());
        account.setAccountNo(vo.getAccountNo());
        account.setAccountName(mch.getMchName());
        account.setBankName(vo.getBankName());
        account.setBankChannelNo(vo.getBankChannelNo());
        if (account.getId() == null) {
            accountFacade.insert(account);
        } else {
            accountFacade.update(account);
        }
        return RestResult.success("更新成功");
    }

    /**
     * 商户信息列表导出
     * @param exportVo
     * @param operator
     * @return
     */
    @RequestMapping("exportEmployerList")
    @Permission("merchant:employer:export")
    public RestResult<String> exportEmployerList(@Valid @RequestBody ExportVo exportVo,@CurrentUser PmsOperator operator){

        // 销售部只能查自己及下属的商户
        if(!crmRole(operator.getId(),pmsPermissionFacade) && operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                exportVo.getParamMap().put("salerIds", salerIds);
            }
        }

        ExportRecord record = getExportRecord(operator,exportVo,ReportTypeEnum.MERCHANT_LIST_INFO_EXPORT);
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private List<Long> getSubSalerIds(PmsOperator operator) {
        List<Long> salerIds = new ArrayList<>();
        try{
            List<PmsOperator> salers = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
            salerIds = salers.stream().map(PmsOperator::getId).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            salerIds.add(operator.getId());
        }
        return salerIds;
    }

    /**
     * 商户信息导出
     *
     * @param exportVo .
     * @return .
     */
    @RequestMapping("exportEmployerInfo")
    @Permission("merchant:employer:export")
    public RestResult<String> exportEmployerInfo(@Valid @RequestBody ExportVo exportVo, @CurrentUser PmsOperator operator) {
        String mchNo = (String) exportVo.getParamMap().get("mchNo");
        if (StringUtil.isEmpty(mchNo)) {
            return RestResult.error("商户号不能为空");
        }
        ExportRecord record = getExportRecord(operator,exportVo,ReportTypeEnum.MERCHANT_INFO_EXPORT);
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private ExportRecord getExportRecord(PmsOperator operator,ExportVo exportVo,ReportTypeEnum reportTypeEnum){
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());

        record.setParamJson(JsonUtil.toString(exportVo.getParamMap()));
        exportVo.getFieldInfoList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        return record;
    }

    /**
     * 更换商户负责人
     *
     * @return
     */
    @Permission("merchant:employer:changeLeader")
    @PostMapping("changeLeader")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更换商户负责人")
    public RestResult<String> changeLeader(@CurrentUser PmsOperator operator,
                                           @RequestBody @Valid ChangeLeaderVO vo) {
        Merchant merchant = merchantQueryFacade.getByMchNo(vo.getMchNo());
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        if (merchant.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()) {
            employerStaffFacade.changeAdmin(vo.getMchNo(), vo.getNewLeaderPhone(), vo.getNewLeaderName(), operator.getRealName());
        } else if (merchant.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()) {
            supplierStaffFacade.changeAdmin(vo.getMchNo(), vo.getNewLeaderPhone(), vo.getNewLeaderName(), operator.getRealName());
        }

        merchant.setContactName(vo.getNewLeaderName());
        merchant.setContactPhone(vo.getNewLeaderPhone());
        merchantFacade.update(merchant);

        return RestResult.success("更换负责人成功");
    }

    /**
     * 更新商户开票信息
     *
     * @return
     */
    @Permission("merchant:employer:updateInvoiceInfo")
    @PostMapping("updateInvoiceInfo")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新商户开票信息")
    public RestResult<String> updateInvoiceInfo(@CurrentUser PmsOperator operator,
                                                @RequestBody @Valid MerchantInvoiceInfoVo vo) {
        MerchantInvoiceInfo invoiceInfo = merchantInvoiceInfoFacade.getByMchNo(vo.getMchNo());
        if (invoiceInfo == null) {
            invoiceInfo = new MerchantInvoiceInfo();
            invoiceInfo.setCreateTime(new Date());
            invoiceInfo.setVersion(0);
        }

        invoiceInfo.setUpdateTime(new Date());
        invoiceInfo.setMchName(vo.getMchName());
        invoiceInfo.setTaxPayerType(vo.getTaxPayerType());
        invoiceInfo.setTaxNo(vo.getTaxNo());
        invoiceInfo.setRegisterAddrInfo(vo.getRegisterAddrInfo());
        invoiceInfo.setAccountNo(vo.getAccountNo());
        invoiceInfo.setBankName(vo.getBankName());
        invoiceInfo.setDefaultInvoiceCategoryCode(vo.getDefaultInvoiceCategoryCode());
        invoiceInfo.setDefaultInvoiceCategoryName(vo.getDefaultInvoiceCategoryName());
        if (invoiceInfo.getId() == null) {
            merchantInvoiceInfoFacade.insert(invoiceInfo);
        } else {
            merchantInvoiceInfoFacade.update(invoiceInfo);
        }
        return RestResult.success("更新商户开票信息成功");
    }

    /**
     * 获取商户基本信息
     * @param mchNo
     * @return
     */
    @GetMapping("getBaseInfo")
    @Permission("merchantEmployer:base:view")
    public RestResult<Map<String,Object>> getBaseInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantQueryFacade.getBaseInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取商户合作信息
     * @param mchNo
     * @return
     */
    @GetMapping("getCooperateInfo")
    @Permission("merchantEmployer:coop:view")
    public RestResult<Map<String,Object>> getCooperateInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantEmployerFacade.getCooperateInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取产品报价单
     * @param mchNo
     * @return
     */
    @GetMapping("getQuoteInfo")
    @Permission("merchantEmployer:quote:view")
    public RestResult getQuoteInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",mchNo);
        List<MerchantEmployerQuote> quoteList = merchantEmployerQuoteFacade.getQuoteList(mchNo,paramMap);
        return RestResult.success(quoteList);
    }

    /**
     * 获取商户主体信息
     * @param mchNo
     * @return
     */
    @GetMapping("getMainInfo")
    @Permission("merchantEmployer:main:view")
    public RestResult<Map<String,Object>> getMainInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = mainFacade.getMainInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取经营信息
     * @param mchNo
     * @return
     */
    @GetMapping("getBusinessInfo")
    @Permission("merchantEmployer:business:view")
    public RestResult<Map<String,Object>> getBusinessInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = mainFacade.getBusinessInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取账号信息
     * @param mchNo
     * @return
     */
    @GetMapping("getAccountInfo")
    @Permission("merchantEmployer:account:view")
    public RestResult<Map<String,Object>> getAccountInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = accountFacade.getAccountInfo(mchNo);
        return RestResult.success(maps);
    }

    @GetMapping("getAgreementInfo")
    @Permission("merchantEmployer:agreement:view")
    public RestResult getAgreementInfo(@RequestParam String mchNo){
        Map<String,Object> maps = new HashMap<>();
        maps.put("mchNo",mchNo);
        List<Agreement> pageResult = agreementFacade.getAgreementPageByMchNoAndMainstayNo(maps);
        return RestResult.success(pageResult);
    }


    private final EditHandleContext context;
    @PostMapping("editBaseInfo")
    @Permission("merchantEmployer:base:edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新基本信息")
    public RestResult editBaseInfo(@Validated(MerchantBaseValid.class)@RequestBody MerchantUpdateVo merchantUpdateVo,@CurrentUser PmsOperator pmsOperator){
        merchantUpdateVo.setUpdator(pmsOperator.getRealName());
        context.getBuilder(EditTypeEnum.EDIT_MERCHANT_BASE_INFO.getType()).build(merchantUpdateVo, pmsOperator);
        merchantFacade.updateBaseInfo(merchantUpdateVo);
        notifyFacade.sendOne(MessageMsgDest.TOPIC_MERCHANT_BASE_EDIT,NotifyTypeEnum.MERCHANT_CHANGE.getValue(),MessageMsgDest.TAG_MERCHANT_BASE_EDIT,JsonUtil.toString(merchantUpdateVo));
        return RestResult.success("更新基本信息成功");
    }

    @PostMapping("editMerchantCooperateInfo")
    @Permission("merchantEmployer:coop:edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新商户合作信息")
    public RestResult editCooperateInfo(@Validated(EmployerCoopValid.class)@RequestBody MerchantUpdateVo merchantUpdateVo, @CurrentUser PmsOperator pmsOperator){
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("status", FlowStatusEnum.PENDING.getValue());
        paramMap.put("businessKeyLike",merchantUpdateVo.getMchNo());
        paramMap.put("flowTopicTypeList",Arrays.asList(FlowTypeEnum.PMS_MCH_CKH_PRODUCT.getFlowTopicType(),FlowTypeEnum.PMS_MCH_CREATE_PRODUCT.getFlowTopicType()));
        List<CommonFlow> commonFlowList = flowFacade.getCommonFlowByParam(paramMap);
        if (commonFlowList.size() > 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前有正在进行的审批流程，不允许编辑");
        }
        merchantUpdateVo.setUpdator(pmsOperator.getRealName());
        context.getBuilder(EditTypeEnum.EDIT_MERCHANT_COOPERATE_INFO.getType()).build(merchantUpdateVo, pmsOperator);
        cooperateFacade.updateMerchantCooperate(merchantUpdateVo);
        return RestResult.success("更新合作信息成功");
    }

    @PostMapping("editMainstayCooperateInfo")
    @Permission("merchantEmployer:coop:edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新供应商合作信息")
    public RestResult editMainstayCooperateInfo(@Validated(MainstayCoopValid.class)@RequestBody MerchantUpdateVo merchantUpdateVo,
                                                      @CurrentUser PmsOperator pmsOperator) {
        merchantUpdateVo.setUpdator(pmsOperator.getRealName());
        context.getBuilder(EditTypeEnum.EDIT_MAINSTAY_COOPERATE_INFO.getType()).build(merchantUpdateVo, pmsOperator);
        cooperateFacade.updateMainstayCooperate(merchantUpdateVo);
        return RestResult.success("更新合作信息成功");
    }

    @PostMapping("editMainInfo")
    @Permission("merchantEmployer:main:edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新主体信息")
    public RestResult editMainInfo(@Validated(MerchantMainValid.class)@RequestBody MerchantUpdateVo merchantUpdateVo,@CurrentUser PmsOperator pmsOperator){
        Merchant merchant = merchantFacade.getByMchNo(merchantUpdateVo.getMchNo());
        if (merchant.getAuthStatus().intValue() != AuthStatusEnum.SUCCESS.getValue()){
            return RestResult.error("商户尚未完成主体信息认证，无法编辑");
        }
        merchantUpdateVo.setUpdator(pmsOperator.getRealName());
        context.getBuilder(EditTypeEnum.EDIT_MAIN_INFO.getType()).build(merchantUpdateVo, pmsOperator);
        mainFacade.updateMain(merchantUpdateVo);
        return RestResult.success("更新主体信息成功");
    }

    @PostMapping("editBusinessInfo")
    @Permission("merchantEmployer:business:edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新经营信息")
    public RestResult editBusinessInfo(@Validated(MerchantBusinessValid.class) @RequestBody MerchantUpdateVo merchantUpdateVo,@CurrentUser PmsOperator pmsOperator){
        Merchant merchant = merchantFacade.getByMchNo(merchantUpdateVo.getMchNo());
        if (merchant.getAuthStatus().intValue() != AuthStatusEnum.SUCCESS.getValue()){
            return RestResult.error("商户尚未完成主体信息认证，无法编辑");
        }
        merchantUpdateVo.setUpdator(pmsOperator.getRealName());
        context.getBuilder(EditTypeEnum.EDIT_BUSINESS_INFO.getType()).build(merchantUpdateVo, pmsOperator);
        mainFacade.updateBusiness(merchantUpdateVo);
        return RestResult.success("更新经营信息成功");
    }

    @PostMapping("record")
    public RestResult<List<MerchantInfoChangeRecord>> record(@RequestBody MerchantFlowVo flowVo, @CurrentUser PmsOperator pmsOperator){
        return RestResult.success(infoChangeRecordFacade.list(flowVo.getMchNo()));
    }

    @GetMapping("listMchProduct")
    public RestResult<List<ListMerchantProductVo>> listMchProduct(@RequestParam("mchNo") String mchNo) {
        final HashMap<String, Object> param = MapUtil.of("mchNo", mchNo);
        return RestResult.success(merchantEmployerQuoteFacade.listMchProduct(param));
    }
    @RequestMapping("getExpressInfo")
    public RestResult<MerchantExpressInfo> getExpressInfo(@RequestParam("mchNo") String mchNo) {
        MerchantExpressInfo info = merchantExpressInfoFacade.getByMchNo(mchNo);
        return RestResult.success(info);
    }
}
