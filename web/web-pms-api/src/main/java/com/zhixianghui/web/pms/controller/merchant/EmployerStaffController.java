package com.zhixianghui.web.pms.controller.merchant;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.pms.vo.merchant.employer.EmployerStaffQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用工企业员工
 *
 * <AUTHOR> <PERSON>
 */
@RestController
@RequestMapping("employerStaff")
@Slf4j
public class EmployerStaffController {

    @Reference
    private EmployerStaffFacade employerStaffFacade;

    /**
     * 分页查询员工
     */
    @Permission("employer:staff:view")
    @RequestMapping("listPage")
    public RestResult<PageResult<List<EmployerStaffVO>>> listPage(@RequestBody EmployerStaffQueryVO vo) {
        PageResult<List<EmployerStaffVO>> result = employerStaffFacade.listPage(BeanUtil.toMap(vo),
                PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(result);
    }
}
