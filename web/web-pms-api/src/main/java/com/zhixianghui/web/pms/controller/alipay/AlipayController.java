package com.zhixianghui.web.pms.controller.alipay;

import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AlipayController {

    @Reference
    private AlipayFacade alipayFacade;

    @PostMapping("ali/sign")
    public String sign(String extAgreementNo) {
        //return alipayFacade.sign(extAgreementNo);
        return null;
    }

}
