package com.zhixianghui.web.pms.controller.trade;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.ReceiptOrderEnum;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.trade.req.CertificateQueryVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import sun.misc.BASE64Decoder;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/29
 **/
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CertificateController {
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private OrderItemFacade orderItemFacade;

    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    /**
     * 批量下载凭证文件
     *
     * @return 提示
     */
    @RequestMapping("/certificate/batchDownloadCertificateFile/{type}")
    public RestResult<String> downloadCertificateFile(@PathVariable Integer type,@RequestBody CertificateQueryVo queryVo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(queryVo);
        ReportTypeEnum reportTypeEnum = ReceiptOrderEnum.getEnum(type).getReportTypeEnum();
        paramMap.put("exportFileType", FileTypeEnum.ZIP.getValue());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        paramMap.put("type",type);
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 单笔实时下载电子凭证文件
     *
     * @param platTrxNo
     * @param operator
     * @param response
     * @throws Exception
     */
    @GetMapping("/download/downloadCertificateFile")
    public void downloadCertificateFile(@RequestParam String platTrxNo,@RequestParam(name = "type") Integer type, @CurrentUser PmsOperator operator, HttpServletResponse response) throws Exception{
        OrderItem item = orderItemFacade.getByPlatTrxNo(platTrxNo);
        if (item == null || item.getOrderItemStatus() != OrderItemStatusEnum.GRANT_SUCCESS.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("订单不存在或者状态不为成功");
        }
        if (type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue() && item.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("此通道或产品不支持企业支付回单下载");
        }

        if (type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue() && (item.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())
                ||item.getPayChannelNo().equals(ChannelNoEnum.OUT_SYNC.name()))){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("此通道或产品不支持企业支付回单下载");
        }

        //外部同步订单特殊处理
        if (StringUtils.equals(item.getPayChannelNo(), ChannelNoEnum.OUT_SYNC.name())) {
            OutputStream os = null;
            try {
                os = response.getOutputStream();
                String fileName = getFileName(type,item);

                Map<String, Object> downloadParam = new HashMap<>();
                downloadParam.put("source", "HJZX");
                downloadParam.put("flowNo", item.getMchOrderNo());
                downloadParam.put("tradeDate", DateUtil.formatDate(item.getCompleteTime()));
                String post = HttpRequest.post("https://open-baseweb.leshuazf.com/external-api/invoiceFlow/download")
                        .header(Header.CONTENT_TYPE, "application/json")
                        .body(JSONUtil.toJsonStr(downloadParam)) //表单内容
                        .timeout(20000) //超时，毫秒
                        .execute().body();
                log.info(post);

                JSONObject jsonObject = JSONUtil.parseObj(post);
                String fileData = jsonObject.getJSONObject("data").getStr("fileData");
                BASE64Decoder decoder = new BASE64Decoder();
                byte[] bytes = decoder.decodeBuffer(fileData);

                response.reset();
                response.setContentType("application/pdf;charset=utf-8");
                response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
                os.write(bytes);
                os.flush();
            }catch (Exception e){
                log.error("下载回单失败:", e);
                String errMsg = e.getMessage().contains("文件不存在")?"凭证文件不存在":"凭证文件下载异常";
                if(os != null){
                    response.reset();
                    response.setContentType("application/json;charset=utf-8");
                    os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes("utf-8"));
                } else {
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("下载电子凭证文件异常");
                }
            }
            return;
        }

        // 文件所在的sftp文件夹
        String tempPath = System.getProperty("user.dir") + File.separator + "download" + File.separator + RandomUtil.get16LenStr();
        ChannelSftp channelSftp = null;
        OutputStream os = null;
        String sftpFilePath = CertificateUtil.getFilePath(dir, item.getMainstayNo(), item.getPlatTrxNo());
        File file =  null;
        try {
            os = response.getOutputStream();
            // 连接sftp
            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            String fileName = getFileName(type,item);
            FileUtils.creatDir(tempPath);
            file = FileUtils.createFile(tempPath + File.separator + fileName);
            SftpUtil.downloadNoClose(sftpFilePath + fileName, file, channelSftp);
            response.reset();
            response.setContentType("application/pdf;charset=utf-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
            os.write(org.apache.commons.io.FileUtils.readFileToByteArray(file));
            os.flush();
        }catch (Exception e){
            log.error("{} 下载电子凭证文件异常：", operator.getLoginName(), e);
            String errMsg = e.getMessage().contains("文件不存在")?"凭证文件不存在":"凭证文件下载异常";
            if(os != null){
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes("utf-8"));
            } else {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("下载电子凭证文件异常");
            }
        }finally {
            FileUtils.deleteDir(new File(tempPath));
            if (channelSftp != null) {
                SftpUtil.sftpClose(channelSftp);
            }
        }
    }

    private String getFileName(Integer type, OrderItem item) {
        if (type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue()) {
            return CertificateUtil.getTransferFileName(item.getMainstayName(), item.getEmployerNo(), item.getPlatTrxNo());
        } else {
            if (StringUtils.isBlank(item.getProductNo())
                    || item.getProductNo().equals(ProductNoEnum.ZXH.getValue())
                    || item.getProductNo().equals(ProductNoEnum.CEP.getValue())
                    || (item.getProductNo().equals(ProductNoEnum.CKH.getValue()) && StringUtils.equals(item.getPayChannelNo(), ChannelNoEnum.JOINPAY.name()))) {
                return CertificateUtil.getPayFileName(item.getReceiveNameDecrypt(), item.getMainstayNo(), item.getPlatTrxNo());
            } else {
                return CertificateUtil.getCKHPayFileName(item.getReceiveNameDecrypt(), item.getEmployerNo(), item.getPlatTrxNo());
            }
        }
    }

}
