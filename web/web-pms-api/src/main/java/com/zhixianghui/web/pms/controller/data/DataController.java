package com.zhixianghui.web.pms.controller.data;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.data.dto.SelerStatisticsDto;
import com.zhixianghui.facade.data.service.CkOrderFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.fee.service.SalesFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.vo.SalesFeeStatisticVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.common.ParamHelper;
import com.zhixianghui.web.pms.enums.DimensionTypeEnum;
import com.zhixianghui.web.pms.vo.data.CoreIndexRequest;
import com.zhixianghui.web.pms.vo.data.CoreIndexResponse;
import com.zhixianghui.web.pms.vo.fee.SalesFeeOrderQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("data")
@Slf4j
public class DataController {

    @Reference
    private RecordItemFacade recordItemFacade;
    @Autowired
    private ParamHelper paramHelper;
    @Reference
    private MerchantQueryFacade merchantFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;

    @PostMapping("coreIndex")
    public RestResult<CoreIndexResponse> getCoreIndex(@RequestBody @Valid CoreIndexRequest coreIndexRequest,@CurrentUser PmsOperator pmsOperator) {

        final String startDate = coreIndexRequest.getStartDate();
        final String endDate = coreIndexRequest.getEndDate();

        if (StringUtils.isAnyBlank(startDate, endDate)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("初始和结束日期不能为空");
        }

        /**
         * 日期校验
         */
        DateTime startDateTime = DateUtil.parseDate(startDate);
        DateTime endDateTime = DateUtil.parseDate(endDate);
        if (endDateTime.isBefore(startDateTime)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("终止日期不能小于开始日期");
        }

        Map<String, Object> param = new HashMap<>();
        final CoreIndexRequest.SecondDimension secondDimension = coreIndexRequest.getSecondDimension();
        if (secondDimension != null && secondDimension.getDimensionType() != null) {
            if (secondDimension.getDimensionType() == DimensionTypeEnum.MERCHANT.getCode()) {
                if (secondDimension.getDimensionMessages() != null && !secondDimension.getDimensionMessages().isEmpty()) {
                    param.put("mchNos", secondDimension.getDimensionMessages());
                }
            } else if (secondDimension.getDimensionType() == DimensionTypeEnum.BRAND.getCode()) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
                paramMap.put("branchName", secondDimension.getDimensionMessages()==null ||  secondDimension.getDimensionMessages().isEmpty() ? null:secondDimension.getDimensionMessages().get(0));

                this.paramHelper.hanldSaler(pmsOperator, paramMap);
                final List<Map<String, Object>> branchVos = merchantFacade.listByBranchEqual(paramMap);
                final List<String> mchNos = branchVos.stream().map(it -> it.get("mchNo").toString()).collect(Collectors.toList());
                if (mchNos == null || mchNos.isEmpty()) {
                    return RestResult.success(new CoreIndexResponse());
                }
                log.info("品牌:{}，商户编号:{}",secondDimension.getDimensionMessages(), JSONUtil.toJsonStr(mchNos));
                param.put("mchNos", mchNos);
            }
        }

        /**
         * 查询订单统计
         */
        param.put("completeBeginDate", DateUtil.beginOfDay(startDateTime.toJdkDate()).toJdkDate());
        param.put("completeEndDate", DateUtil.endOfDay(endDateTime.toJdkDate()).toJdkDate());

        this.paramHelper.hanldSaler(pmsOperator, param);
        Map<String, Object> coreIndexStatistics = ckOrderFacade.coreIndexStatistics(param);
        List<Map<String, Object>> dailyDetail = ckOrderFacade.coreIndexDailyDetail(param);
        List<Map<String, Object>> monthlyDetail = ckOrderFacade.coreIndexDetailMonthly(param);

        String orderCount = coreIndexStatistics == null || coreIndexStatistics.get("orderCount") == null ? "0" : String.valueOf(coreIndexStatistics.get("orderCount"));
        String receivedUserCount = coreIndexStatistics ==null || coreIndexStatistics.get("receivedUserCount") == null ? "0" : String.valueOf(coreIndexStatistics.get("receivedUserCount"));
        String totalAmount = coreIndexStatistics == null || coreIndexStatistics.get("totalPayAmount") == null ? "0" : new BigDecimal(String.valueOf(coreIndexStatistics.get("totalPayAmount"))).setScale(2, RoundingMode.HALF_UP).toPlainString();
        String avgAmount = coreIndexStatistics == null || coreIndexStatistics.get("avgPayAmount") == null ? "0" : new BigDecimal(String.valueOf(coreIndexStatistics.get("avgPayAmount"))).setScale(2, RoundingMode.HALF_UP).toPlainString();

        CoreIndexResponse coreIndexResponse = new CoreIndexResponse();
        coreIndexResponse.setOrderCount(Integer.valueOf(orderCount));
        coreIndexResponse.setTotalPayAmount(totalAmount);
        coreIndexResponse.setAvgPayAmount(avgAmount);
        coreIndexResponse.setReceivedUserCount(Integer.valueOf(receivedUserCount));
        coreIndexResponse.setPayAmountDaily(dailyDetail);
        coreIndexResponse.setPayAmountMonthly(monthlyDetail);
        return RestResult.success(coreIndexResponse);
    }

    @Reference
    private SalesFeeOrderQueryFacade salesFeeOrderQueryFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private CkOrderFacade ckOrderFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @PostMapping("listSalerOrdersGroupedByMch")
    public RestResult<List<Map<String, Object>>> listSalerOrdersGroupedByMch(@RequestBody SelerStatisticsDto selerStatisticsDto, @CurrentUser PmsOperator pmsOperator) {
        selerStatisticsDto.setSalerIds((selerStatisticsDto.getSalerIds()==null ||selerStatisticsDto.getSalerIds().isEmpty())?null:selerStatisticsDto.getSalerIds());
        selerStatisticsDto.setMchNos((selerStatisticsDto.getMchNos()==null ||selerStatisticsDto.getMchNos().isEmpty())?null:selerStatisticsDto.getMchNos());
        Map<String, Object> params = BeanUtil.toMap(selerStatisticsDto);
        this.paramHelper.hanldSaler(pmsOperator, params);
        return RestResult.success(ckOrderFacade.listSalerOrdersGroupedByMch(params));
    }

    @Deprecated
    @PostMapping("exportSalerStatistics")
    public RestResult<String> exportSalerStatistics(@RequestBody SelerStatisticsDto vo, @CurrentUser PmsOperator operator) {
        Map<String,Object> paramMap = handleSalersParam(vo,operator);
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.PMS_SALER_MERGE_FEE.getFileName());
        record.setReportType(ReportTypeEnum.PMS_SALER_MERGE_FEE.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setDeepPage(true);

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.PMS_SALER_MERGE_FEE.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @Deprecated
    @PostMapping("salerstatistics")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "销售成本订单汇总-导出")
    public RestResult<PageResult<List<SalesFeeStatisticVo>>> salerstatistics(@RequestBody SelerStatisticsDto selerStatisticsDto, @RequestBody Page<Map<String, Object>> page, @CurrentUser PmsOperator pmsOperator) {
        Map<String,Object> params = handleSalersParam(selerStatisticsDto,pmsOperator);
        PageParam pageParam = PageParam.newInstance((int) page.getCurrent(), (int) page.getSize());
        PageResult<List<SalesFeeStatisticVo>> pageResult = salesFeeOrderQueryFacade.salerFeeStatistics(params, pageParam);
        return RestResult.success(pageResult);
    }

    private Map<String,Object> handleSalersParam(SelerStatisticsDto selerStatisticsDto,PmsOperator pmsOperator) {
        selerStatisticsDto.setSalerIds((selerStatisticsDto.getSalerIds()==null ||selerStatisticsDto.getSalerIds().isEmpty())?null:selerStatisticsDto.getSalerIds());
        selerStatisticsDto.setMchNos((selerStatisticsDto.getMchNos()==null ||selerStatisticsDto.getMchNos().isEmpty())?null:selerStatisticsDto.getMchNos());
        if (selerStatisticsDto.getAgentExists() != null) {
            if (StringUtils.equals(selerStatisticsDto.getAgentExists(), "true")) {
                selerStatisticsDto.setAgentExists("1");
            }else {
                selerStatisticsDto.setAgentExists("0");
            }
        }

        Map<String, Object> params = BeanUtil.toMap(selerStatisticsDto);
        this.paramHelper.hanldSaler(pmsOperator, params);
        return params;
    }

    @PostMapping("listSalerOrdersGroupedByMchDaily")
    public RestResult<Map<String, List<Map<String, Object>>>> listSalerOrdersGroupedByMchDaily(@RequestBody SelerStatisticsDto selerStatisticsDto,@CurrentUser PmsOperator pmsOperator){
        selerStatisticsDto.setSalerIds((selerStatisticsDto.getSalerIds()==null ||selerStatisticsDto.getSalerIds().isEmpty())?null:selerStatisticsDto.getSalerIds());
        selerStatisticsDto.setMchNos((selerStatisticsDto.getMchNos()==null ||selerStatisticsDto.getMchNos().isEmpty())?null:selerStatisticsDto.getMchNos());
        Map<String, Object> params = BeanUtil.toMap(selerStatisticsDto);
        this.paramHelper.hanldSaler(pmsOperator, params);
        final List<Map<String, Object>> mapList = ckOrderFacade.listSalerOrdersGroupedByMchDaily(params);
        final Map<String, List<Map<String, Object>>> resultData = mapList.stream().collect(Collectors.groupingBy(m -> String.valueOf(m.get("employerNo"))));
        return RestResult.success(resultData);
    }

    @PostMapping("listSalerOrdersGroupedByMchMonthly")
    public RestResult< Map<String, List<Map<String, Object>>>> listSalerOrdersGroupedByMchMonthly(@RequestBody SelerStatisticsDto selerStatisticsDto,@CurrentUser PmsOperator pmsOperator){
        selerStatisticsDto.setSalerIds((selerStatisticsDto.getSalerIds()==null ||selerStatisticsDto.getSalerIds().isEmpty())?null:selerStatisticsDto.getSalerIds());
        selerStatisticsDto.setMchNos((selerStatisticsDto.getMchNos()==null ||selerStatisticsDto.getMchNos().isEmpty())?null:selerStatisticsDto.getMchNos());
        Map<String, Object> params = BeanUtil.toMap(selerStatisticsDto);
        this.paramHelper.hanldSaler(pmsOperator, params);
        final List<Map<String, Object>> mapList = ckOrderFacade.listSalerOrdersGroupedByMchMonthly(params);
        final Map<String, List<Map<String, Object>>> resultData = mapList.stream().collect(Collectors.groupingBy(m -> String.valueOf(m.get("employerNo"))));
        return RestResult.success(resultData);
    }

    @PostMapping("salerOrdersStatistics")
    public RestResult<Map<String, Object>> salerOrdersStatistics(@RequestBody SelerStatisticsDto selerStatisticsDto,@CurrentUser PmsOperator pmsOperator) {
        selerStatisticsDto.setSalerIds((selerStatisticsDto.getSalerIds()==null ||selerStatisticsDto.getSalerIds().isEmpty())?null:selerStatisticsDto.getSalerIds());
        selerStatisticsDto.setMchNos((selerStatisticsDto.getMchNos()==null ||selerStatisticsDto.getMchNos().isEmpty())?null:selerStatisticsDto.getMchNos());
        Map<String, Object> params = BeanUtil.toMap(selerStatisticsDto);
        this.paramHelper.hanldSaler(pmsOperator, params);
        return RestResult.success(ckOrderFacade.salerOrdersStatistics(params));
    }
}
