package com.zhixianghui.web.pms.controller.data;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.fee.service.AgentFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.MerchantFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.SalesFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.VendorFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.vo.DataStatisticsVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.service.MerchantSalerFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.agent.SimpleAgentInfoVo;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.common.ParamHelper;
import com.zhixianghui.web.pms.biz.file.ExportBiz;
import com.zhixianghui.web.pms.vo.data.MainIndexRep;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName MainIndexController
 * @Description TODO
 * @Date 2021/11/23 15:29
 */
@RestController
@RequestMapping("data")
public class MainIndexController {

    @Reference
    private OrderItemFacade orderItemFacade;

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    @Reference
    private MerchantFacade merchantFacade;

    @Reference
    private SalesFeeOrderQueryFacade salesFeeOrderQueryFacade;

    @Reference
    private MerchantSalerFacade merchantSalerFacade;

    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    @Reference
    private VendorFeeOrderQueryFacade vendorFeeOrderQueryFacade;

    @Reference
    private AgentFeeOrderQueryFacade agentFeeOrderQueryFacade;

    @Reference
    private MerchantFeeOrderQueryFacade merchantFeeOrderQueryFacade;

    @Reference
    private AgentFacade agentFacade;

    @Autowired
    private ExportBiz exportBiz;

    @Autowired
    private ParamHelper paramHelper;

    private static final String THIS_MONTH_TOTAL_COUNT = "thisMonthTotalCount";
    private static final String LAST_MONTH_TOTAL_COUNT = "lastMonthTotalCount";
    private static final String THIS_MONTH_COUNT = "thisMonthCount";
    private static final String LAST_MONTH_COUNT = "lastMonthCount";

    private static final String THIS_MONTH_TOTAL_MONEY = "thisMonthTotalMoney";
    private static final String LAST_MONTH_TOTAL_MONEY = "lastMonthTotalMoney";
    private static final String THIS_MONTH_MONEY = "thisMonthMoney";
    private static final String LAST_MONTH_MONEY = "lastMonthMoney";

    private static final String THIS_MONTH_TOTAL_PROFIT = "thisMonthProfitTotal";
    private static final String LAST_MONTH_TOTAL_PROFIT = "lastMonthProfitTotal";
    private static final String THIS_MONTH_PROFIT = "thisMonthProfit";
    private static final String LAST_MONTH_PROFIT = "lastMonthProfit";

    private static final String NUM = "num";
    private static final String MONEY = "money";

    @GetMapping("salerData")
    public RestResult salerData(@RequestParam String endDate, @CurrentUser PmsOperator pmsOperator){
        //要查出销售统计数据，所以需要先查出来所有销售人员
        //查询销售角色

        final List<Long> idList = this.paramHelper.hanldSaler(pmsOperator, new HashMap<>());

        Map<String, Object> salerParam = new HashMap<>();
        salerParam.put("idList", idList);
        List<PmsOperator> salers = pmsOperatorFacade.listBy(salerParam);
        //获取时间
        Date now = DateUtil.parse(endDate);
        Date lastDate = DateUtil.addMonth(now,-1);
        Map<String,Object> nowDateMap = getDate(now);
        Map<String,Object> lastDateMap = getDate(lastDate);
        this.paramHelper.hanldSaler(pmsOperator, nowDateMap);
        this.paramHelper.hanldSaler(pmsOperator, lastDateMap);
        //获取签约数据
        Map<String,Object> merchantMap = merchantSalerFacade.getSalerStatistics(nowDateMap,lastDateMap);
        MainIndexRep mainIndexRep = new MainIndexRep();
        mainIndexRep.setThisMonthNum((Long) merchantMap.get(THIS_MONTH_TOTAL_COUNT));
        mainIndexRep.setLastMonthNum((Long) merchantMap.get(LAST_MONTH_TOTAL_COUNT));
        Map<String,Object> lastMonthCount = (Map<String, Object>) merchantMap.get(LAST_MONTH_COUNT);
        Map<String,Object> thisMonthCount = (Map<String, Object>) merchantMap.get(THIS_MONTH_COUNT);
        //获取月订单数据
        Map<String,Object> orderMap = salesFeeOrderQueryFacade.getSalerStatistics(nowDateMap,lastDateMap);
        mainIndexRep.setThisMonthMoney((BigDecimal) orderMap.get(THIS_MONTH_TOTAL_MONEY));
        mainIndexRep.setLastMonthMoney((BigDecimal) orderMap.get(LAST_MONTH_TOTAL_MONEY));
        Map<String,Object> lastMonthMoney = (Map<String, Object>) orderMap.get(LAST_MONTH_MONEY);
        Map<String,Object> thisMonthMoney = (Map<String, Object>)orderMap.get(THIS_MONTH_MONEY);
        salers.stream().forEach(x-> {
            formatCountData(x.getId(),x.getRealName(),mainIndexRep,lastMonthCount,thisMonthCount,false);
            formatAmountData(x.getId(),x.getRealName(),mainIndexRep,lastMonthMoney,thisMonthMoney,false);
        });
        //排序
        sortItem(mainIndexRep);
        return RestResult.success(mainIndexRep);
    }

    @GetMapping("agentData")
    public RestResult agentData(@RequestParam String endDate, @CurrentUser PmsOperator pmsOperator) {
        //获取时间
        Date now = DateUtil.parse(endDate);
        Date lastDate = DateUtil.addMonth(now, -1);
        Map<String, Object> nowDateMap = getDate(now);
        Map<String, Object> lastDateMap = getDate(lastDate);
        this.paramHelper.hanldSaler(pmsOperator, nowDateMap);
        this.paramHelper.hanldSaler(pmsOperator, lastDateMap);
        //获取月签约商户数量
        Map<String, Object> agentMap = merchantFacade.getAgentData(nowDateMap, lastDateMap);
        MainIndexRep mainIndexRep = new MainIndexRep();
        mainIndexRep.setThisMonthNum((Long) agentMap.get(THIS_MONTH_TOTAL_COUNT));
        mainIndexRep.setLastMonthNum((Long) agentMap.get(LAST_MONTH_TOTAL_COUNT));
        Map<String, Object> lastMonthCount = (Map<String, Object>) agentMap.get(LAST_MONTH_COUNT);
        Map<String, Object> thisMonthCount = (Map<String, Object>) agentMap.get(THIS_MONTH_COUNT);
        //获取合伙人订单数据
        Map<String, Object> orderMap = agentFeeOrderQueryFacade.getAgentOrder(nowDateMap, lastDateMap);
        mainIndexRep.setThisMonthMoney((BigDecimal) orderMap.get(THIS_MONTH_TOTAL_MONEY));
        mainIndexRep.setLastMonthMoney((BigDecimal) orderMap.get(LAST_MONTH_TOTAL_MONEY));
        Map<String, Object> lastMonthMoney = (Map<String, Object>) orderMap.get(LAST_MONTH_MONEY);
        Map<String, Object> thisMonthMoney = (Map<String, Object>) orderMap.get(THIS_MONTH_MONEY);
        //获取所有合伙人
        Map<String, Object> param = new HashMap<>();
        this.paramHelper.hanldSaler(pmsOperator, param);
        List<SimpleAgentInfoVo> agentList = agentFacade.listAllSimpleAgentInfoByParam(param);
        agentList.stream().forEach(x -> {
            formatCountData(x.getAgentNo(), x.getAgentName(), mainIndexRep, lastMonthCount, thisMonthCount, true);
            formatAmountData(x.getAgentNo(), x.getAgentName(), mainIndexRep, lastMonthMoney, thisMonthMoney, true);
        });
        //排序
        sortItem(mainIndexRep);
        return RestResult.success(mainIndexRep);
    }

    //处理不同表查询
    //数量统计
    private void formatCountData(Object key,String name,MainIndexRep mainIndexRep, Map<String, Object> lastMonthCount, Map<String, Object> thisMonthCount,boolean ignoreNull) {
        MainIndexRep.NumData numData = new MainIndexRep.NumData();
        numData.setKey(key);
        numData.setName(name);
        //数量统计
        if (lastMonthCount != null && lastMonthCount.get(key) != null &&
                ((Map<String, Object>) lastMonthCount.get(key)).get(NUM) != null){
            numData.setLastMonth((Long) ((Map<String, Object>) lastMonthCount.get(key)).get(NUM));
        }
        if (thisMonthCount != null && thisMonthCount.get(key) != null &&
                ((Map<String, Object>) thisMonthCount.get(key)).get(NUM) != null){
            numData.setThisMonth((Long) ((Map<String, Object>) thisMonthCount.get(key)).get(NUM));
        }
        if (!ignoreNull || numData.getThisMonth() != 0L){
            mainIndexRep.getNumData().add(numData);
        }
    }

    //处理不同表查询
    //数量统计
    private void formatAmountData(Object key, String name, MainIndexRep mainIndexRep, Map<String, Object> lastMonthMoney, Map<String, Object> thisMonthMoney,boolean ignoreNull) {
        //金额统计
        MainIndexRep.MoneyData moneyData = new MainIndexRep.MoneyData();
        moneyData.setKey(key);
        moneyData.setName(name);
        if (lastMonthMoney != null && lastMonthMoney.get(key) != null &&
                ((Map<String, Object>) lastMonthMoney.get(key)).get(MONEY) != null){
            moneyData.setLastMonth((BigDecimal) ((Map<String, Object>) lastMonthMoney.get(key)).get(MONEY));
        }
        if (thisMonthMoney != null && thisMonthMoney.get(key) != null &&
                ((Map<String, Object>) thisMonthMoney.get(key)).get(MONEY) != null){
            moneyData.setThisMonth((BigDecimal) ((Map<String, Object>) thisMonthMoney.get(key)).get(MONEY));
        }
        if (!ignoreNull || moneyData.getThisMonth().compareTo(BigDecimal.ZERO) != 0){
            mainIndexRep.getMoneyData().add(moneyData);
        }
    }

    @GetMapping("supplyData")
    public RestResult supplyData(@RequestParam String endDate, @CurrentUser PmsOperator pmsOperator) {
        //获取时间
        Date now = DateUtil.parse(endDate);
        Date lastDate = DateUtil.addMonth(now, -1);
        Map<String, Object> nowDateMap = getDate(now);
        Map<String, Object> lastDateMap = getDate(lastDate);
        this.paramHelper.hanldSaler(pmsOperator, nowDateMap);
        this.paramHelper.hanldSaler(pmsOperator, lastDateMap);
        //获取供应商数据
        Map<String, Object> amountMap = vendorFeeOrderQueryFacade.getSupplyOrder(nowDateMap, lastDateMap);
        MainIndexRep mainIndexRep = new MainIndexRep();
        formatData(amountMap, mainIndexRep);
        //排序
        sortItem(mainIndexRep);
        return RestResult.success(mainIndexRep);
    }

    @GetMapping("merchantData")
    public RestResult merchantData(@RequestParam String endDate,@CurrentUser PmsOperator pmsOperator){
        //获取时间
        Date now = DateUtil.parse(endDate);
        Date lastDate = DateUtil.addMonth(now,-1);
        Map<String,Object> nowDateMap = getDate(now);
        Map<String,Object> lastDateMap = getDate(lastDate);
        this.paramHelper.hanldSaler(pmsOperator, nowDateMap);
        this.paramHelper.hanldSaler(pmsOperator, lastDateMap);
        //获取供应商数据
        Map<String,Object> amountMap = merchantFeeOrderQueryFacade.getMerchantOrder(nowDateMap,lastDateMap);
        MainIndexRep mainIndexRep = new MainIndexRep();
        formatData(amountMap,mainIndexRep);
        //排序
        sortItem(mainIndexRep);
        return RestResult.success(mainIndexRep);
    }


    //处理同表查询
    private void formatData(Map<String, Object> amountMap, MainIndexRep mainIndexRep) {
        DataStatisticsVo lastMonthTotal = (DataStatisticsVo)amountMap.get(LAST_MONTH_TOTAL_MONEY);
        DataStatisticsVo thisMonthTotal = (DataStatisticsVo)amountMap.get(THIS_MONTH_TOTAL_MONEY);
        DataStatisticsVo thisMonthTotalProfit = (DataStatisticsVo) amountMap.get(THIS_MONTH_TOTAL_PROFIT);
        DataStatisticsVo lastMonthTotalProfit = (DataStatisticsVo) amountMap.get(LAST_MONTH_TOTAL_PROFIT);
        //获取总数
        mainIndexRep.setLastMonthMoney(lastMonthTotal.getMoney());
        mainIndexRep.setThisMonthMoney(thisMonthTotal.getMoney());
        mainIndexRep.setLastMonthNum(lastMonthTotal.getNum());
        mainIndexRep.setThisMonthNum(thisMonthTotal.getNum());
        if (thisMonthTotalProfit != null && lastMonthTotalProfit != null){
            mainIndexRep.setThisMonthProfit(thisMonthTotalProfit.getMoney());
            mainIndexRep.setLastMonthProfit(lastMonthTotalProfit.getMoney());
        }
        //组装前端数据
        Map<String,DataStatisticsVo> thisMonth = (Map<String, DataStatisticsVo>) amountMap.get(THIS_MONTH_MONEY);
        Map<String,DataStatisticsVo> lastMonth = (Map<String, DataStatisticsVo>) amountMap.get(LAST_MONTH_MONEY);
        //取出所有值
        List<DataStatisticsVo> statisticsVoCollection = new ArrayList<>();
        if (thisMonth != null){
            statisticsVoCollection.addAll(thisMonth.values());
        }
        if (lastMonth != null){
            statisticsVoCollection.addAll(lastMonth.values());
        }

        statisticsVoCollection.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DataStatisticsVo::getItemNo))), ArrayList::new)).stream().forEach(x->
                formatData(x.getItemNo(),x.getItemName(),lastMonth,thisMonth,new MainIndexRep.NumData(),mainIndexRep.getNumData(),new MainIndexRep.MoneyData(),mainIndexRep.getMoneyData()));

        //组装前端数据
        Map<String,DataStatisticsVo> thisMonthProfit = (Map<String, DataStatisticsVo>) amountMap.get(THIS_MONTH_PROFIT);
        Map<String,DataStatisticsVo> lastMonthProfit = (Map<String, DataStatisticsVo>) amountMap.get(LAST_MONTH_PROFIT);
        //取出所有值
        if (thisMonthProfit != null || lastMonthProfit != null){
            List<DataStatisticsVo> profitCollection = new ArrayList<>();
            if (thisMonthProfit != null){
                profitCollection.addAll(thisMonthProfit.values());
            }
            if (lastMonthProfit != null){
                profitCollection.addAll(lastMonthProfit.values());
            }
            profitCollection.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DataStatisticsVo::getItemNo))), ArrayList::new)).stream().forEach(x->
                    formatData(x.getItemNo(),x.getItemName(),lastMonthProfit,thisMonthProfit,null,null,new MainIndexRep.MoneyData(),mainIndexRep.getProfitData()));
        }
    }

    //构建数据
    private void formatData(String itemNo, String itemName,
                            Map<String, DataStatisticsVo> lastMonth, Map<String, DataStatisticsVo> thisMonth,
                            MainIndexRep.NumData numData,
                            List<MainIndexRep.NumData> mainIndexRepNumData, MainIndexRep.MoneyData moneyData, List<MainIndexRep.MoneyData> mainIndexRepMoneyData) {
        //数量统计
        if (numData != null){
            numData.setName(itemName);
            if (lastMonth != null && lastMonth.get(itemNo) != null ){
                numData.setLastMonth(lastMonth.get(itemNo).getNum());
            }
            if (thisMonth != null && thisMonth.get(itemNo) != null){
                numData.setThisMonth(thisMonth.get(itemNo).getNum());
            }
            if (numData.getThisMonth() != 0L){
                mainIndexRepNumData.add(numData);
            }
        }

        //金额统计
        if (moneyData != null){
            moneyData.setName(itemName);
            if (lastMonth != null && lastMonth.get(itemNo) != null){
                moneyData.setLastMonth(lastMonth.get(itemNo).getMoney());
            }
            if (thisMonth != null && thisMonth.get(itemNo) != null){
                moneyData.setThisMonth(thisMonth.get(itemNo).getMoney());
            }
            if (moneyData.getThisMonth().compareTo(BigDecimal.ZERO) != 0){
                mainIndexRepMoneyData.add(moneyData);
            }
        }
    }

    private void sortItem(MainIndexRep mainIndexRep) {
        List<MainIndexRep.NumData> numDataList =mainIndexRep.getNumData()
                .stream().filter(x-> x.getLastMonth() != 0 || x.getThisMonth() != 0).sorted(Comparator.comparing(MainIndexRep.NumData::getThisMonth)).collect(Collectors.toList());

        List<MainIndexRep.MoneyData> moneyDataList = mainIndexRep.getMoneyData()
                .stream().filter(x-> x.getThisMonth().compareTo(BigDecimal.ZERO) != 0 ||
                        x.getLastMonth().compareTo(BigDecimal.ZERO) != 0).sorted(Comparator.comparing(MainIndexRep.MoneyData::getThisMonth)).collect(Collectors.toList());

        List<MainIndexRep.MoneyData> profitDataList = mainIndexRep.getProfitData()
                .stream().filter(x-> x.getThisMonth().compareTo(BigDecimal.ZERO) != 0 ||
                        x.getLastMonth().compareTo(BigDecimal.ZERO) != 0).sorted(Comparator.comparing(MainIndexRep.MoneyData::getThisMonth)).collect(Collectors.toList());

        mainIndexRep.setNumData(numDataList.subList(numDataList.size() > 15 ? numDataList.size() - 15 : 0,numDataList.size()));
        mainIndexRep.setMoneyData(moneyDataList.subList(moneyDataList.size() > 15 ? moneyDataList.size() - 15 : 0,moneyDataList.size()));
        mainIndexRep.setProfitData(profitDataList.subList(profitDataList.size() > 15 ? profitDataList.size() - 15 : 0,profitDataList.size()));
    }

    public static Map<String,Object> getDate(Date date){
        Map<String,Object> map = new HashMap<>();
        //获取当前开始时间
        Date startDay = DateUtil.getDayStart(DateUtil.getFirstOfMonth(date));
        //获取结束时间
        Date endDay = DateUtil.getDayEnd(date);
        map.put("completeBeginDate",startDay);
        map.put("completeEndDate",endDay);
        return map;
    }
}
