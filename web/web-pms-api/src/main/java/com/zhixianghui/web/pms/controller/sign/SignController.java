package com.zhixianghui.web.pms.controller.sign;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.vo.OfflineExcelRow;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.sign.SignBiz;
import com.zhixianghui.web.pms.controller.fix.helper.MultipartFileUtil;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.sign.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description 电子签约
 * @date 2021/1/18 15:34
 **/
@RestController
@RequestMapping("sign")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SignController {

    private static final int HEAD_ROW_NUMBER = 1;

    private final SignBiz signBiz;

    @Reference
    private NotifyFacade notifyFacade;

    @Permission("sign:signRecord:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<SignRecordResVo>>> listPage(@RequestBody SignRecordQueryVo queryVo, @RequestBody PageVo pageVo) {
        PageResult<List<SignRecordResVo>> pageResult = signBiz.listPage(queryVo,pageVo.toPageParam());
        return RestResult.success(pageResult);
    }

    /**
     * 线下上传协议文件
     */
    @PostMapping("uploadFile")
    public RestResult uploadFile(@RequestBody SignRecord signRecord){
        signBiz.uploadFile(signRecord);
        return RestResult.success("协议上传成功");
    }

    /**
     * 导出电子签约
     */
    @Permission("sign:signRecord:export")
    @PostMapping("exportSignRecord")
    public RestResult<String> exportSignRecord(@RequestBody SignRecordQueryVo queryVo, @CurrentUser PmsOperator pmsOperator) {
        signBiz.exportSignRecord(queryVo, pmsOperator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @Permission("sign:signRecord:view")
    @PostMapping("addSignImages")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "签约添加图片")
    public RestResult<String> addSignImages(@RequestBody AddSignImagesVo addSignImagesVo) {
        if (addSignImagesVo.getIdCardType().intValue() == IdCardTypeEnum.ORIGINAL.getCode().intValue()) {
            LimitUtil.notEmpty(addSignImagesVo.getIdCardBackUrl(), "身份证背面地址不能为空");
            LimitUtil.notEmpty(addSignImagesVo.getIdCardFrontUrl(), "身份证正面地址不能为空");
        }else {
            LimitUtil.notEmpty(addSignImagesVo.getIdCardCopyFileUrl(), "身份证背面地址不能为空");
        }

        try {
            signBiz.addSignImages(addSignImagesVo);
            return RestResult.success("导入成功");
        } catch (Exception e) {
            log.error("导入失败",e);
            return RestResult.success(StrUtil.format("导入失败:{}",e.getMessage()));
        }
    }

    @Permission("sign:signRecord:del")
    @GetMapping("delete/{id}")
    @Logger(type = OperateLogTypeEnum.DELETE, name = "删除签约数据")
    public RestResult<String> deleteById(@PathVariable("id") Long id) {
        Assert.notNull(id, () -> CommonExceptions.PARAM_INVALID.newWithErrMsg("id不能为空"));
        signBiz.deleteRecordById(id);
        return RestResult.success("操作成功");
    }

    @PostMapping("uploadOfflineSign")
    @Logger(type = OperateLogTypeEnum.CREATE,name = "导入线下签约数据")
    public RestResult uploadOfflineSign(@RequestParam("file")MultipartFile file){
        if(!file.getOriginalFilename().endsWith(".xlsx")) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("FileName:" + file.getOriginalFilename() +"文件格式有误，必须为xlsx格式");
        }
        File excelFile = MultipartFileUtil.transfer2File(file);

        CompletableFuture.runAsync(()->{
            EasyExcel.read(excelFile, OfflineExcelRow.class, new OfflineExcelListener(notifyFacade, MessageMsgDest.TOPIC_OFFLINE_SIGNER,MessageMsgDest.TAG_OFFLINE_SIGNER))
                    .sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        });
        return RestResult.success("数据正在导入中，请稍后查看");
    }
}
