package com.zhixianghui.web.pms.vo.data;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class CoreIndexRequest {
    @NotBlank(message = "开始时间不能为空")
    private String startDate;
    @NotBlank(message = "结束时间不能为空")
    private String endDate;
    private SecondDimension secondDimension;


    @Data
    public class SecondDimension{
        private Integer dimensionType;
        private List<String> dimensionMessages;
    }
}
