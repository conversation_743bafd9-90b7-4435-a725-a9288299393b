package com.zhixianghui.web.pms.controller.individualproxy;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.dto.ConfirmInvoiceDto;
import com.zhixianghui.facade.trade.dto.ProxyOrderQueryDto;
import com.zhixianghui.facade.trade.entity.IndividualProxyOrder;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.IndividualProxyOrderFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.vo.IndividualProxyOrderVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("proxyOrder")
public class IndividualProxyOrderController {

    @Reference
    private IndividualProxyOrderFacade individualProxyOrderFacade;
    @Reference
    private UserInfoFacade userInfoFacade;

    @PostMapping("listPage")
    public RestResult<Page<IndividualProxyOrder>> listPage(@RequestBody ProxyOrderQueryDto proxyOrderQueryDto, @CurrentUser PmsOperator pmsOperator) {
        return RestResult.success(individualProxyOrderFacade.listPage(proxyOrderQueryDto, new Page(proxyOrderQueryDto.getCurrent(), proxyOrderQueryDto.getSize())));
    }

    @PostMapping("confirmInvoice")
    public RestResult<String> confirmInvoice(@Validated @RequestBody ConfirmInvoiceDto confirmInvoiceDto) {
        individualProxyOrderFacade.confirmInvoice(confirmInvoiceDto);
        return RestResult.success("操作成功");
    }

    @GetMapping("getProxyOrderById")
    public RestResult<IndividualProxyOrderVo> getProxyOrderById(@RequestParam("id") Long id) {
        final IndividualProxyOrder proxyOrder = individualProxyOrderFacade.getProxyOrderById(id);
        IndividualProxyOrderVo vo = new IndividualProxyOrderVo();
        BeanUtil.copyProperties(proxyOrder,vo);
        vo.setBusinessContractFile(proxyOrder.getBusinessContractFile());
        vo.setEntrustAgreementFile(proxyOrder.getEntrustAgreementFile());
        vo.setFeeDetail(proxyOrder.getFeeDetail());
        vo.setFundFlowFile(proxyOrder.getFundFlowFile());
        vo.setInvoiceUrl(proxyOrder.getInvoiceUrl());

        final UserInfo userInfo = userInfoFacade.getByIdCardNoMd5(MD5Util.getMixMd5Str(proxyOrder.getIdCardNo()));
        vo.setUserIdcardNo(userInfo.getReceiveIdCardNoDecrypt());
        vo.setUserName(userInfo.getReceiveNameDecrypt());
        vo.setUserIdcardImg(Lists.newArrayList(userInfo.getIdCardFrontUrl(), userInfo.getIdCardBackUrl(), userInfo.getIdCardCopyUrl()));

        return RestResult.success(vo);
    }

}
