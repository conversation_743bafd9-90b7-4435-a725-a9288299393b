package com.zhixianghui.web.pms.controller.agent;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.user.portal.PortalFunctionTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.agent.AgentFunctionFacade;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.file.FileBiz;
import com.zhixianghui.web.pms.biz.agent.AgentBiz;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.utils.MultipartFileUtil;
import com.zhixianghui.web.pms.vo.file.FileVo;
import com.zhixianghui.web.pms.vo.agent.req.AgentFunctionVO;
import com.zhixianghui.web.pms.vo.permission.PmsFunctionVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 合伙人后台菜单
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("agentFunction")
@Slf4j
public class AgentFunctionController extends BaseController {

    @Reference
    private AgentFunctionFacade agentFunctionFacade;
    @Autowired
    private AgentBiz agentBiz;
    @Autowired
    private FileBiz<FileVo.AgentFile, AgentFunction> fileBiz;
    /**
     * 权限的导出
     */
    @Permission("agent:function:export")
    @GetMapping("export")
    public RestResult<String> export(FunctionVO employerFunction, @CurrentUser PmsOperator operator) {
        employerFunction.setOperateName(operator.getLoginName());
        employerFunction.setOperateTime(operator.getCurrLoginTime());
        agentFunctionFacade.export(employerFunction);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 权限的导入
     */
    @Permission("agent:function:import")
    @PostMapping("import")
    public RestResult<String> importFile(PmsFunctionVO vo, @CurrentUser PmsOperator operator) {
        MultipartFileUtil.check(vo);
        fileBiz.importFile(operator, vo, agentBiz, FileVo.AgentFile.class);
        return RestResult.success("导入成功");
    }

    /**
     * 查询所有功能
     */
    @Permission("agent:function:view")
    @GetMapping("listAll")
    public RestResult<List<AgentFunctionVO>> listAll() {
        List<AgentFunction> functions = agentFunctionFacade.listAll();
        return RestResult.success(functions.stream().map(AgentFunctionVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 新增功能
     */
    @Permission("agent:function:add")
    @PostMapping("add")
    public RestResult<String> add(@RequestBody @Valid AgentFunctionVO vo) {
        if (vo.getType() == PortalFunctionTypeEnum.MENU_TYPE.getValue()) {
            // 菜单项需要校验 URL 字段
            if (StringUtil.isEmpty(vo.getUrl())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("URL不能为空");
            }
            if (!ValidateUtil.isStrLengthValid(vo.getUrl(), 2, 100)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("URL长度限定2到100");
            }
        }
        try {
            agentFunctionFacade.create(AgentFunctionVO.buildDto(vo));
            super.logSave("添加用工企业后台功能[" + vo.getName() + "]", true);
            return RestResult.success("添加功能成功");
        } catch (Exception e) {
            super.logSave("添加用工企业后台功能", false);
            throw e;
        }
    }

    /**
     * 修改功能
     */
    @Permission("agent:function:edit")
    @PostMapping("edit")
    public RestResult<String> editPmsFunction(@RequestBody @Valid AgentFunctionVO vo) {
        try {
            AgentFunction function = agentFunctionFacade.getById(vo.getId());
            if (function.getType() == PortalFunctionTypeEnum.MENU_TYPE.getValue()) {
                // 菜单项需要校验 URL 字段
                if (StringUtil.isEmpty(vo.getUrl())) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("URL不能为空");
                }
                if (!ValidateUtil.isStrLengthValid(vo.getUrl(), 2, 100)) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("URL长度限定2到100");
                }
            }

            function.setName(vo.getName());
            function.setNumber(vo.getNumber());
            function.setUrl(vo.getUrl());
            agentFunctionFacade.update(function);

            super.logEdit("修改用工企业后台功能，功能名称[" + vo.getName() + "]", true);
            return RestResult.success("修改成功");
        } catch (Exception e) {
            super.logEdit("修改用工企业后台功能，功能名称[" + vo.getName() + "]", false);
            throw e;
        }
    }

    /**
     * 删除功能
     * @param id    功能id
     */
    @Permission("agent:function:delete")
    @RequestMapping("delete")
    public RestResult<String> deletePmsFunction(@RequestParam long id) {
        AgentFunction function = null;
        try {
            if ((function = agentFunctionFacade.getById(id)) == null) {
                return RestResult.error("无法获取要删除的数据");
            }

            agentFunctionFacade.deleteById(id);

            super.logDelete("删除用工企业后台功能，功能名称[" + function.getName() + "]", true);
            return RestResult.success("删除成功");
        } catch (Exception e) {
            super.logDelete("删除用工企业后台功能，功能名称[" + Optional.ofNullable(function).map(AgentFunction::getName).orElse("") + "]", false);
            throw e;
        }
    }
}
