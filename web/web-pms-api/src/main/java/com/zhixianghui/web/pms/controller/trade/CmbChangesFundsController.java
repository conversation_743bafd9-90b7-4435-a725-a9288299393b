package com.zhixianghui.web.pms.controller.trade;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.dto.ChangesFoundsDTO;
import com.zhixianghui.facade.trade.dto.CmbChangesFoundsQueryDTO;
import com.zhixianghui.facade.trade.dto.CmbIncomeRecordParamDTO;
import com.zhixianghui.facade.trade.service.ChangesFundsFacade;
import com.zhixianghui.facade.trade.service.CmbChangesFundsFacade;
import com.zhixianghui.facade.trade.vo.CmbChangesFundsVO;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2024/8/29 11:12
 */
@RestController
@RequestMapping("/cmbChangesFunds")
public class CmbChangesFundsController {

    @Reference
    private CmbChangesFundsFacade cmbChangesFundsFacade;

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    @PostMapping("/listPage")
    public RestResult<IPage<CmbChangesFundsVO>> listPage(@RequestBody CmbChangesFoundsQueryDTO param) {
        return RestResult.success(cmbChangesFundsFacade.listPage(param));
    }


    /**
     * @param param
     * @param operator
     * @return
     */
    @PostMapping("/export")
    //@Permission("recharge:list:view")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "招行资金变动记录-导出")
    public RestResult<String> exportCmbChangesFunds(@RequestBody CmbChangesFoundsQueryDTO param, @CurrentUser PmsOperator operator) {
        Date createTimeBegin = param.getCreateTimeBegin();
        Date createTimeEnd = param.getCreateTimeEnd();
        if (ObjectUtil.isAllNotEmpty(createTimeBegin, createTimeEnd)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("请提供起止创建时间");
        }
        if (DateUtil.compare(createTimeBegin, createTimeEnd) > 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
        }
        cmbChangesFundsFacade.exportCmbChangesFunds(param, operator.getLoginName());
        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}