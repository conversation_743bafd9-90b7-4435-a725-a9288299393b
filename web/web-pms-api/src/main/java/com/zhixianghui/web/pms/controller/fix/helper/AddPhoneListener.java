package com.zhixianghui.web.pms.controller.fix.helper;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AddPhoneListener extends AnalysisEventListener<AddPhoneExcelData> {

    private OrderItemFacade orderItemFacade;
    private RecordItemFacade recordItemFacade;
    public AddPhoneListener(OrderItemFacade orderItemFacade, RecordItemFacade recordItemFacade) {
        this.orderItemFacade = orderItemFacade;
        this.recordItemFacade = recordItemFacade;
    }

    @Override
    public void invoke(AddPhoneExcelData addPhoneExcelData, AnalysisContext analysisContext) {
        final String platTrxNo = addPhoneExcelData.getPlatTrxNo();

        final OrderItem orderItem = orderItemFacade.getByPlatTrxNo(platTrxNo);
        if (orderItem != null) {
            orderItem.setReceivePhoneNoEncrypt(addPhoneExcelData.getReceivePhoneNo());
            orderItemFacade.update(orderItem);
            log.info("订单明细更新成功，订单号：{}", platTrxNo);
        } else {
            log.info("订单明细不存在，订单号：{}", platTrxNo);
        }

        final RecordItem recordItem = recordItemFacade.getByPlatTrxNo(platTrxNo);
        if (recordItem != null) {
            recordItem.setReceivePhoneNoEncrypt(addPhoneExcelData.getReceivePhoneNo());
            recordItemFacade.update(recordItem);
            log.info("支付记录更新成功，订单号：{}", platTrxNo);
        }else {
            log.info("支付记录不存在，订单号：{}", platTrxNo);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
