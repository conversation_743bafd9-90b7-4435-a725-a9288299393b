package com.zhixianghui.web.pms.controller.report;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.common.dto.MainstayChannelRelationDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.entity.report.PayChannelType;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.facade.common.service.PayChannelTypeFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.report.MainstayChannelRelationBiz;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.report.MainstayChannelRelationVo;
import com.zhixianghui.web.pms.vo.report.req.MainstayChannelRelationQueryVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.*;

/**
 * <AUTHOR>
 * @description 供应商支付账户Controller
 * @date 2020-09-29 10:55
 **/
@RestController
@RequestMapping("mainstayChannelRelation")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class MainstayChannelRelationController {

    private final MainstayChannelRelationBiz mainstayChannelRelationBiz;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private PayChannelFacade payChannelFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private PayChannelTypeFacade payChannelTypeFacade;

    /**
     * 创建供应商支付账户
     *
     * @param mainstayChannelRelationVo 供应商支付账户信息
     * @param currentOperator           创建人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.CREATE, name = "创建供应商支付账户")
    @Permission("report:mainstayChannelRelation:add")
    @PostMapping("create")
    public RestResult<String> create(@Validated @RequestBody MainstayChannelRelationVo mainstayChannelRelationVo, @CurrentUser PmsOperator currentOperator) {
        mainstayChannelRelationBiz.create(mainstayChannelRelationVo, currentOperator);
        return RestResult.success("创建成功");
    }

    /**
     * 更新供应商支付账户
     *
     * @param mainstayChannelRelationVo 供应商支付账户信息
     * @param currentOperator           操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新供应商支付账户")
    @Permission("report:mainstayChannelRelation:edit")
    @PostMapping("update")
    public RestResult<String> update(@Validated @RequestBody MainstayChannelRelationVo mainstayChannelRelationVo, @CurrentUser PmsOperator currentOperator) {
        mainstayChannelRelationBiz.update(mainstayChannelRelationVo, currentOperator);
        return RestResult.success("更新成功");
    }

    /**
     * 分页查询供应商支付账户
     *
     * @param queryVo 查询条件
     * @param pageVo  分页参数
     * @return 供应商支付账户列表
     */
    @Permission("report:mainstayChannelRelation:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<MainstayChannelRelationDto>>> listPage(@RequestBody MainstayChannelRelationQueryVo queryVo, @RequestBody PageVo pageVo) {
        PageResult<List<MainstayChannelRelationDto>> pageResult = mainstayChannelRelationBiz.listPage(queryVo, pageVo.toPageParam());
        return RestResult.success(pageResult);
    }

    /**
     * 获取单个供应商支付账户
     *
     * @param mainstayNo 供应商编号
     * @return 供应商支付账户
     */
    @Permission("report:mainstayChannelRelation:view")
    @GetMapping("getByMainstayNo")
    public RestResult<MainstayChannelRelationVo> getByMainstayNo(@RequestParam String mainstayNo) {
        MainstayChannelRelationVo mainstayChannelRelationVo = mainstayChannelRelationBiz.getByMainstayNo(mainstayNo);
        return RestResult.success(mainstayChannelRelationVo);
    }

    /**
     * 删除供应商支付账户
     *
     * @param mainstayNo 供应商编号
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.DELETE, name = "删除供应商支付账户")
    @Permission("report:mainstayChannelRelation:edit")
    @PostMapping("deleteByMainstayNo")
    public RestResult<String> deleteByMainstayNo(@RequestParam String mainstayNo) {
        mainstayChannelRelationBiz.deleteByMainstayNo(mainstayNo);
        return RestResult.success("删除成功");
    }

    @PostMapping("getBalanceByMainstayNo")
    public RestResult<List<Map<String, Object>>> getBalanceByMainstayNo(@RequestParam @Validated @NotBlank String mainstayNo) {
        final List<MainstayChannelRelation> mainstayChannelRelations = mainstayChannelRelationFacade.listByMainstayNo(mainstayNo);

        List<Map<String, Object>> channelList = new ArrayList<>();
        mainstayChannelRelations.forEach(it -> {
            PayChannel payChannel = payChannelFacade.getByChannelNo(it.getPayChannelNo());
            Map<String, String> amount;
            try {
                if (it.getStatus().equals(OpenOffEnum.OFF.getValue())) {
                    Map<String, String> map = new HashMap<>();
                    map.put("useAbleSettAmount", "未开通");
                    map.put("frozenAmount", "未开通");
                    amount = map;
                } else {
                    amount = getAmount(it);
                }
            } catch (Exception e) {
                log.error("查询余额出错", e);
                amount = null;
            }
            Map<String, Object> detailMap = new HashMap<>();
            if (amount == null) {
                detailMap.put("balance", "0");
                detailMap.put("frozenAmount", "0");
            } else {
                detailMap.put("balance", amount.get("useAbleSettAmount"));
                detailMap.put("frozenAmount", amount.get("frozenAmount"));
            }
            detailMap.put("payChannelNo", it.getPayChannelNo());
            detailMap.put("mainstayNo", it.getMainstayNo());
            detailMap.put("mainstayName", it.getMainstayName());
            detailMap.put("channelMchNo", it.getChannelMchNo());
            detailMap.put("payChannelName", payChannel.getPayChannelName());
            channelList.add(detailMap);
        });

        return RestResult.success(channelList);
    }

    /**
     * 获取各通道余额
     *
     * @return
     */
    private Map<String, String> getAmount(MainstayChannelRelation channelRelation) throws Exception {
        MainstayAmountQueryDto amountQueryDto = new MainstayAmountQueryDto();
        amountQueryDto.setMainstayNo(channelRelation.getMainstayNo());
        amountQueryDto.setChannelNo(channelRelation.getPayChannelNo());
        amountQueryDto.setChannelMchNo(channelRelation.getChannelMchNo());
        amountQueryDto.setAgreementNo(channelRelation.getAgreementNo());
        Map<String, String> amount = accountQueryFacade.getMainstayAmount(amountQueryDto);
        return amount;
    }
}
