package com.zhixianghui.web.pms.vo.permission;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Data
public class PmsFunctionEditVO {

    /**
     * id
     */
    private Long id;

    /**
     * 名称
     */
    @NotEmpty(message = "功能名称不能为空")
    @Size(min = 2, max = 30, message = "功能名称长度限定2到30")
    private String name;

    /**
     * 编号
     */
    @NotEmpty(message = "编号不能为空")
    @Size(min = 2, max = 20, message = "编号长度限定2到20")
    private String number;

    /**
     * 后端API地址
     */
    private String url;

    private String permissionFlag;
}
