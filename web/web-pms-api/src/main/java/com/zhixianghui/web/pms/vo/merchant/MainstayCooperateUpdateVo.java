package com.zhixianghui.web.pms.vo.merchant;

import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerCooperate;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.vo.EmployerCooperateUpdateVo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/8
 **/
@Data
public class MainstayCooperateUpdateVo implements Serializable {
    private String entrustAgreementFileUrl;
    @NotEmpty(message = "B端协议模板不能为空")
    private String agreementTemplate2BFileUrl;
    @NotEmpty(message = "C端协议模板不能为空")
    private String agreementTemplate2CFileUrl;
    @NotEmpty(message = "商户号不能为空")
    private String mchNo;

    private String mchName;
    private String contactName;
    private String contactPhone;
    private String contactEmail;
    private String servicePhone;
    private String salerName;
    private Integer provideIncomeDetailType;
    private Long salerId;

    public Merchant toMerchant(MainstayCooperateUpdateVo vo, Merchant merchant, PmsOperator pmsOperator) {
        if (StringUtils.isNotBlank(vo.getContactName())) {
            merchant.setContactName(vo.getContactName());
        }
        if (StringUtils.isNotBlank(vo.getContactPhone())) {
            merchant.setContactPhone(vo.getContactPhone());
        }
        if (StringUtils.isNotBlank(vo.getMchName())) {
            merchant.setMchName(vo.getMchName());
        }
        if (StringUtils.isNotBlank(pmsOperator.getLoginName())) {
            merchant.setUpdator(pmsOperator.getLoginName());
        }
        merchant.setUpdateTime(new Date());
        return merchant;
    }

    public MerchantSaler toMerchantSaler(MainstayCooperateUpdateVo vo, MerchantSaler merchantSaler, PmsOperator pmsOperator) {
        if (vo.getSalerId() != null) {
            merchantSaler.setSalerId(vo.getSalerId());
            merchantSaler.setSaleDepartmentId(pmsOperator.getDepartmentId());
        }
        if (StringUtils.isNotBlank(vo.getSalerName())) {
            merchantSaler.setSalerName(vo.getSalerName());
            merchantSaler.setSaleDepartmentName(pmsOperator.getDepartmentName());
        }
        merchantSaler.setUpdateTime(new Date());
        return merchantSaler;
    }

    public MerchantEmployerCooperate toMerchantEmployerCooperate(MainstayCooperateUpdateVo vo, MerchantEmployerCooperate cooperate) {
        if (vo.getProvideIncomeDetailType() != null) {
            cooperate.setProvideIncomeDetailType(vo.getProvideIncomeDetailType());
        }
        return cooperate;
    }
}
