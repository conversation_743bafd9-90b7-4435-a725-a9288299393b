package com.zhixianghui.web.pms.controller.fee;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.fee.VendorQueryVo;
import com.zhixianghui.web.pms.vo.fee.VendorVo;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("vendor_manager")
public class VendorController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(VendorController.class);

    @Reference
    private VendorFacade facade;

    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;

    @Permission("fee:vendor:list")
    @PostMapping("list")
    public RestResult<PageResult<List<Vendor>>> listVendor(@RequestBody @Valid VendorQueryVo vo) {
        PageResult<List<Vendor>> pageResult = facade.getVendors(BeanUtil.toMap(vo), PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(pageResult);
    }

    @Permission("fee:vendor:list")
    @GetMapping("listAll")
    public RestResult<List<Vendor>> listAllVendor() {
        List<Vendor> pageResult = facade.getAllVendors();
        return RestResult.success(pageResult);
    }

    @Permission("fee:vendor:list")
    @PostMapping("listSimple")
    public RestResult<List<Map<String,Object>>> listAllVendorSimple() {
        List<Vendor> vendorList = facade.getAllVendors();
        List<Map<String,Object>> vendorMapList = vendorList.stream().map(vendor -> {
            Map<String,Object> vendorMap = new HashMap<>();
            vendorMap.put("id",vendor.getId());
            vendorMap.put("vendorNo",vendor.getVendorNo());
            vendorMap.put("vendorName",vendor.getVendorName());
            return vendorMap;
        }).collect(Collectors.toList());
        return RestResult.success(vendorMapList);
    }

    @Permission("fee:vendor:delete")
    @PostMapping("delete/{id}")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.DELETE, name = "删除数据")
    public RestResult<Map> delete(@CurrentUser PmsOperator currentOperator, @PathVariable long id) {

        logger.info("<<VendorController.delete>> 删除供应商 id:{}", id);

        Vendor vendor = facade.getVendorById(id);
        String vendorNo = vendor.getVendorNo();
        facade.deleteById(id, currentOperator.getLoginName());

        logger.info("<<VendorController.delete>> 删除供应商商户关系 vendorNo:{}", vendorNo);
        employerMainstayRelationFacade.deleteByMainstayNo(vendorNo);

        Map responseMap = new HashMap();
        responseMap.put("msg", "删除操作成功");
        return RestResult.success(responseMap);

    }

    @Permission("fee:vendor:edit")
    @PostMapping("edit")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.MODIFY, name = "修改数据")
    public RestResult<Map> edit(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid VendorVo vendorVo) {
        //0. 参数校验
        if (vendorVo.getId() == null) {
            RestResult restResult = RestResult.error("参数缺失，id字段必传");
            restResult.setCode(CommonExceptions.PARAM_INVALID.getSysErrorCode());
            return restResult;
        }

        //1. 检查是否在数据库存在记录
        Vendor vendorOld = facade.getVendorById(vendorVo.getId());
        if (vendorOld == null) {
            RestResult restResult = RestResult.error("该供应商不存在");
            restResult.setCode(CommonExceptions.BIZ_INVALID.getSysErrorCode());
            return restResult;
        }

        //2. 如果修改了vendorNo，检查是否被别的供应商占用
        if (StringUtil.isNotEmpty(vendorVo.getVendorNo())) {
            Vendor vendorOther = facade.getVendorByNo(vendorVo.getVendorNo());
            if (vendorOther != null && !Objects.equals(vendorOther.getId(), vendorVo.getId())) {
                RestResult restResult = RestResult.error("供应商编号已被占用，如有疑问，请联系管理员。");
                restResult.setCode(CommonExceptions.BIZ_INVALID.getSysErrorCode());
                return restResult;
            }
        }

        //3. 修改， 为了降低版本对前端传参的依赖，使用最新查询出来的版本号

        vendorOld.setVendorNo(vendorVo.getVendorNo());
        vendorOld.setVendorName(vendorVo.getVendorName());
        vendorOld.setSupplierNo(vendorVo.getVendorNo());
        vendorOld.setSupplierName(vendorVo.getVendorName());
        vendorOld.setProductNo(vendorVo.getProductNo());
        vendorOld.setProductName(vendorVo.getProductName());
        vendorOld.setDescription(vendorVo.getDescription());
        vendorOld.setUpdateBy(currentOperator.getLoginName());
        vendorOld.setUpdateTime(new Date());

        facade.update(vendorOld);

        Map responseMap = new HashMap();
        responseMap.put("msg", "修改操作成功");
        return RestResult.success(responseMap);

    }

    @Permission("fee:vendor:add")
    @PostMapping("add")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.CREATE, name = "修改数据")
    public RestResult<Map> add(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid VendorVo vendorVo) {
        Vendor vendorOther = facade.getVendorByNo(vendorVo.getVendorNo());
        if (vendorOther != null && !Objects.equals(vendorOther.getId(), vendorVo.getId())) {
            RestResult restResult = RestResult.error("供应商编号已被占用，如有疑问，请联系管理员。");
            restResult.setCode(CommonExceptions.BIZ_INVALID.getSysErrorCode());
            return restResult;
        }

        Vendor vendor = new Vendor();
        vendor.setId(vendorVo.getId());
        vendor.setVendorNo(vendorVo.getVendorNo());
        vendor.setVendorName(vendorVo.getVendorName());
        vendor.setSupplierName(vendorVo.getVendorName());
        vendor.setSupplierNo(vendorVo.getVendorNo());
        vendor.setProductNo(vendorVo.getProductNo());
        vendor.setProductName(vendorVo.getProductName());
        vendor.setStatus(PublicStatus.ACTIVE);
        vendor.setDescription(vendorVo.getDescription());
        vendor.setUpdateBy(currentOperator.getLoginName());
        vendor.setUpdateTime(new Date());
        vendor.setCreateBy(currentOperator.getLoginName());
        vendor.setCreateTime(new Date());
        vendor.setVersion(0);
        facade.addVendor(vendor);

        Map responseMap = new HashMap();
        responseMap.put("msg", "新增操作成功");
        return RestResult.success(responseMap);
    }
}
