package com.zhixianghui.web.pms.biz.record;

import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/7/22 16:42
 */

@Component
public class EditHandleListener implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        Map<String, Object> map = event.getApplicationContext().getBeansWithAnnotation(EditType.class);
        EditHandleContext context = event.getApplicationContext().getBean(EditHandleContext.class);
        map.forEach((beanName, object) -> {
            EditType handler = object.getClass().getAnnotation(EditType.class);
            context.putBuilder(handler.value().getType(), (Builder) object);
        });
    }
}
