package com.zhixianghui.web.pms.vo.permission;

import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRole;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

@Data
public class PmsOperatorVO {

    private Long id;

    private Date createTime;

    /**
     * 登录名
     */
    @NotEmpty(message = "登录名不能为空")
    private String loginName;

    /**
     * 登录密码
     */
    @NotEmpty(message = "登录密码不能为空")
    private String loginPwd;

    /**
     * 姓名
     */
    @NotEmpty(message = "姓名不能为空")
    private String realName;

    /**
     * 手机号
     */
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号不正确")
    private String mobileNo;

    /**
     * 描述
     */
    private String remark;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 操作员类型
     */
    private Integer type;

    /**
     * 所属部门id
     */
    private Long departmentId;

    /**
     * 所属部门名称
     */
    private String departmentName;

    /**
     * 分配角色id
     */
    private List<Long> roleIds;
    private List<PmsRole> pmsRolesList;

    public static PmsOperator buildDto(PmsOperatorVO vo) {
        PmsOperator pmsOperator = new PmsOperator();
        pmsOperator.setId(vo.getId());
        pmsOperator.setCreateTime(vo.getCreateTime());
        pmsOperator.setLoginName(vo.getLoginName());
        // pmsOperator.setLoginPwd(vo.getLoginPwd());
        pmsOperator.setRealName(vo.getRealName());
        pmsOperator.setMobileNo(vo.getMobileNo());
        pmsOperator.setRemark(vo.getRemark());
        pmsOperator.setStatus(vo.getStatus());
        pmsOperator.setType(vo.getType());
        pmsOperator.setDepartmentId(vo.getDepartmentId());
        pmsOperator.setDepartmentName(vo.getDepartmentName());
        pmsOperator.setRoleIds(vo.getRoleIds());
        return pmsOperator;
    }

    public static PmsOperatorVO buildVo(PmsOperator operator) {
        PmsOperatorVO vo = new PmsOperatorVO();
        vo.setId(operator.getId());
        vo.setCreateTime(operator.getCreateTime());
        vo.setLoginName(operator.getLoginName());
        // vo.setLoginPwd(operator.getLoginPwd());
        vo.setRealName(operator.getRealName());
        vo.setMobileNo(operator.getMobileNo());
        vo.setRemark(operator.getRemark());
        vo.setStatus(operator.getStatus());
        vo.setType(operator.getType());
        vo.setDepartmentId(operator.getDepartmentId());
        vo.setDepartmentName(operator.getDepartmentName());
        vo.setRoleIds(operator.getRoleIds());
        return vo;
    }

}
