package com.zhixianghui.web.pms.biz.report;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.banklink.entity.KeyPairRecord;
import com.zhixianghui.facade.banklink.service.KeyPairRecordFacade;
import com.zhixianghui.facade.common.dto.KeyPairRecordDto;
import com.zhixianghui.facade.common.dto.MainstayChannelRelationDto;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.vo.report.MainstayChannelRelationVo;
import com.zhixianghui.web.pms.vo.report.ParentChannelInfoVo;
import com.zhixianghui.web.pms.vo.report.req.MainstayChannelRelationQueryVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-29 10:58
 **/
@Service
public class MainstayChannelRelationBiz {
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private PayChannelFacade payChannelFacade;
    @Reference
    private KeyPairRecordFacade keyPairRecordFacade;

    public void create(MainstayChannelRelationVo mainstayChannelRelationVo, PmsOperator currentOperator) {
        // 获取通道状态 用于校验
        Map<String, Integer> channelMap = payChannelFacade.listAll().stream()
                .collect(Collectors.toMap(PayChannel::getPayChannelNo, PayChannel::getStatus));
        List<KeyPairRecordDto> keyPairRecordList = Lists.newArrayList();
        List<MainstayChannelRelation> relationList = Lists.newArrayList();
        mainstayChannelRelationVo.getChannelInfos().forEach(
                channelInfo -> {
                    //如果开启支付关系 支付通道处于关闭返回错误提示
                    Integer channelStatus = channelMap.get(channelInfo.getPayChannelNo());
                    if (channelStatus == null) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无法查询此通道状态，通道编号：" + channelInfo.getPayChannelNo());
                    }
                    if (channelInfo.getStatus().equals(OpenOffEnum.OPEN.getValue()) &&
                            channelStatus.equals(OpenOffEnum.OFF.getValue())) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道状态为禁用，请先启用支付通道, 通道编号：" + channelInfo.getPayChannelNo());
                    }
                    MainstayChannelRelation mainstayChannelRelation = new MainstayChannelRelation();
                    mainstayChannelRelation.setMainstayName(mainstayChannelRelationVo.getMainstayName());
                    mainstayChannelRelation.setMainstayNo(mainstayChannelRelationVo.getMainstayNo());
                    mainstayChannelRelation.setCreateOperator(currentOperator.getRealName());
                    mainstayChannelRelation.setUpdateOperator(currentOperator.getRealName());
                    mainstayChannelRelation.setUpdateTime(new Date());
                    BeanUtils.copyProperties(channelInfo, mainstayChannelRelation);
//                    mainstayChannelRelation.setBankName(channelInfo.getBankName());
//                    mainstayChannelRelation.setSubBankName(channelInfo.getSubBankName());
                    if (channelInfo.getPayChannelNo().equals(ChannelNoEnum.ALIPAY.name())) {
                        mainstayChannelRelation.setSubBankName("支付宝-备付金账户");
                        mainstayChannelRelation.setBankName("支付机构备付金集中存管账户");
                        mainstayChannelRelation.setBankAddress("上海市-上海市");
                        mainstayChannelRelation.setJoinBankNo("************");
                    } else if (channelInfo.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())) {
                        mainstayChannelRelation.setSubBankName("财付通-备付金账户");
                        mainstayChannelRelation.setBankName("支付机构备付金存管账户");
                        mainstayChannelRelation.setBankAddress("广东省-深圳市");
                        mainstayChannelRelation.setJoinBankNo("************");
                    }
                    relationList.add(mainstayChannelRelation);

                    if (StringUtils.isNotBlank(channelInfo.getPayChannelNo()) && StringUtils.isNotBlank(channelInfo.getChannelMchNo())) {
                        KeyPairRecordDto keyPairRecordDto = new KeyPairRecordDto();
                        keyPairRecordDto.setCreateTime(new Date());
                        keyPairRecordDto.setUpdator(currentOperator.getRealName());
                        keyPairRecordDto.setChannelNo(channelInfo.getPayChannelNo());
                        keyPairRecordDto.setChannelMchNo(channelInfo.getChannelMchNo());
                        keyPairRecordDto.setMchPrivateKey(channelInfo.getPrivateKey());
                        keyPairRecordDto.setMchPublicKey(channelInfo.getPublicKey());
                        keyPairRecordDto.setChannelPublicKey(channelInfo.getChannelKey());
                        keyPairRecordDto.setChannelLoginUser(channelInfo.getChannelLoginUser());
                        keyPairRecordDto.setChannelPlatNo(channelInfo.getChannelPlatNo());
                        keyPairRecordDto.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
                        keyPairRecordList.add(keyPairRecordDto);
                    }
                }
        );
        mainstayChannelRelationFacade.batchCreate(relationList, keyPairRecordList);
    }

    public void update(MainstayChannelRelationVo mainstayChannelRelationVo, PmsOperator currentOperator) {
        // 获取通道状态 用于校验
        Map<String, Integer> channelMap = payChannelFacade.listAll().stream()
                .collect(Collectors.toMap(PayChannel::getPayChannelNo, PayChannel::getStatus));


        List<KeyPairRecordDto> keyPairRecordList = Lists.newArrayList();

        //组装前端信息
        Map<String, MainstayChannelRelation> relationMap = mainstayChannelRelationVo.getChannelInfos().stream().map(
                channelInfo -> {
                    //如果开启支付关系 支付通道处于关闭返回错误提示
                    Integer channelStatus = channelMap.get(channelInfo.getPayChannelNo());
                    if (channelStatus == null) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无法查询此通道状态，通道编号：" + channelInfo.getPayChannelNo());
                    }
                    if (channelInfo.getStatus().equals(OpenOffEnum.OPEN.getValue()) &&
                            channelStatus.equals(OpenOffEnum.OFF.getValue())) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道状态为禁用，请先启用支付通道, 通道编号：" + channelInfo.getPayChannelNo());
                    }
                    MainstayChannelRelation mainstayChannelRelation = new MainstayChannelRelation();
                    mainstayChannelRelation.setMainstayName(mainstayChannelRelationVo.getMainstayName());
                    mainstayChannelRelation.setMainstayNo(mainstayChannelRelationVo.getMainstayNo());
                    mainstayChannelRelation.setUpdateOperator(currentOperator.getRealName());
                    mainstayChannelRelation.setUpdateTime(new Date());
                    BeanUtils.copyProperties(channelInfo, mainstayChannelRelation);
//                    mainstayChannelRelation.setBankName(channelInfo.getBankName());
//                    mainstayChannelRelation.setSubBankName(channelInfo.getSubBankName());

                    if (StringUtils.isNotBlank(channelInfo.getPayChannelNo()) && StringUtils.isNotBlank(channelInfo.getChannelMchNo())) {
                        KeyPairRecordDto keyPairRecordDto = new KeyPairRecordDto();
                        keyPairRecordDto.setCreateTime(new Date());
                        keyPairRecordDto.setUpdator(currentOperator.getRealName());
                        keyPairRecordDto.setChannelNo(channelInfo.getPayChannelNo());
                        keyPairRecordDto.setChannelMchNo(channelInfo.getChannelMchNo());
                        keyPairRecordDto.setMchPrivateKey(channelInfo.getPrivateKey());
                        keyPairRecordDto.setMchPublicKey(channelInfo.getPublicKey());
                        keyPairRecordDto.setChannelPublicKey(channelInfo.getChannelKey());
                        keyPairRecordDto.setChannelLoginUser(channelInfo.getChannelLoginUser());
                        keyPairRecordDto.setChannelPlatNo(channelInfo.getChannelPlatNo());
                        keyPairRecordDto.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
                        keyPairRecordList.add(keyPairRecordDto);
                    }
                    return mainstayChannelRelation;
                }
        ).collect(Collectors.toMap(MainstayChannelRelation::getPayChannelNo, MainstayChannelRelation::getMainstayChannelRelation));

        // 获取旧的数据 用于更新
        List<MainstayChannelRelation> relationList = mainstayChannelRelationFacade.listBy(Collections.singletonMap("mainstayNo", mainstayChannelRelationVo.getMainstayNo()));

        //替换数据
        relationList.forEach(
                relation -> {
                    MainstayChannelRelation updateRelation = relationMap.get(relation.getPayChannelNo());
                    //这部分数据不能被覆盖
                    updateRelation.setId(relation.getId());
                    updateRelation.setVersion(relation.getVersion());
                    updateRelation.setCreateOperator(relation.getCreateOperator());
                    updateRelation.setCreateTime(relation.getCreateTime());
                    updateRelation.setAgreementNo(relation.getAgreementNo());
                    updateRelation.setAlipayUserId(relation.getAlipayUserId());
                    if (relation.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())) {
                        updateRelation.setSubBankName("财付通-备付金账户");
                        updateRelation.setBankName("支付机构备付金存管账户");
                        updateRelation.setBankAddress("广东省-深圳市");
                        updateRelation.setJoinBankNo("************");
                    } else {
                        updateRelation.setAccountName(relation.getAccountName());
                        updateRelation.setAccountNo(relation.getAccountNo());
                        updateRelation.setBankName(relation.getBankName());
                        updateRelation.setSubBankName(relation.getSubBankName());
                        updateRelation.setJoinBankNo(relation.getJoinBankNo());
                        updateRelation.setBankAddress(relation.getBankAddress());
                        updateRelation.setAlipayCardNo(relation.getAlipayCardNo());
                    }
                    BeanUtils.copyProperties(updateRelation, relation);
                }
        );
        mainstayChannelRelationFacade.batchUpdate(relationList, keyPairRecordList);
    }

    public PageResult<List<MainstayChannelRelationDto>> listPage(MainstayChannelRelationQueryVo queryVo, PageParam pageParam) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayName", queryVo.getMainstayName());
        paramMap.put("mainstayNameLike", queryVo.getMainstayNameLike());
        paramMap.put("mainstayNo", queryVo.getMainstayNo());
        paramMap.put("payChannelNoList", queryVo.getPayChannelNoList());
        return mainstayChannelRelationFacade.listCustomPage(paramMap, pageParam);
    }

    public MainstayChannelRelationVo getByMainstayNo(String mainstayNo) {
        List<MainstayChannelRelation> list = mainstayChannelRelationFacade.listByMainstayNo(mainstayNo);
        if (ObjectUtils.isEmpty(list) || list.get(0) == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该供应商无支付通道信息，请先新建，供应商编号：" + mainstayNo);
        }
        MainstayChannelRelationVo mainstayChannelRelationVo = new MainstayChannelRelationVo();
        mainstayChannelRelationVo.setMainstayName(list.get(0).getMainstayName());
        mainstayChannelRelationVo.setMainstayNo(list.get(0).getMainstayNo());
        List<ParentChannelInfoVo> channelList = list.stream().map(
                record -> {
                    ParentChannelInfoVo parentChannelInfoVo = new ParentChannelInfoVo();
                    BeanUtils.copyProperties(record, parentChannelInfoVo);
                    if (StringUtils.isNotBlank(record.getChannelMchNo())) {
                        //额外获取密钥
                        KeyPairRecord keyPairRecord = keyPairRecordFacade.getByChannelNoAndChannelMchNo(record.getPayChannelNo(), record.getChannelMchNo());
                        if (keyPairRecord != null) {
                            parentChannelInfoVo.setPublicKey(keyPairRecord.getMchPublicKeyDecrypt());
                            parentChannelInfoVo.setPrivateKey(keyPairRecord.getMchPrivateKeyDecrypt());
                            parentChannelInfoVo.setChannelKey(keyPairRecord.getChannelPublicKeyDecrypt());
                            parentChannelInfoVo.setChannelLoginUser(keyPairRecord.getChannelLoginUserDecrypt());
                            parentChannelInfoVo.setChannelPlatNo(keyPairRecord.getChannelPlatNo());
                        }
                    }
                    return parentChannelInfoVo;
                }
        ).collect(Collectors.toList());
        mainstayChannelRelationVo.setChannelInfos(channelList);
        return mainstayChannelRelationVo;
    }


    public void deleteByMainstayNo(String mainstayNo) {
        mainstayChannelRelationFacade.deleteByMainstayNo(mainstayNo);
    }
}
