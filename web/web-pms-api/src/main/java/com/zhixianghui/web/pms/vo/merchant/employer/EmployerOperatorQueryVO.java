package com.zhixianghui.web.pms.vo.merchant.employer;

import com.zhixianghui.common.statics.enums.user.portal.PortalOperatorStatusEnum;
import com.zhixianghui.web.pms.vo.PageVo;
import lombok.Data;

import java.util.Date;

@Data
public class EmployerOperatorQueryVO extends PageVo {

    /**
     * 操作员姓名
     */
    private String nameLike;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 状态 {@link PortalOperatorStatusEnum#getValue()}
     */
    private Integer status;

    /**
     * 创建时间开始
     */
    private Date createTimeBegin;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;
}
