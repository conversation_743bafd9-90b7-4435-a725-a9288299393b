package com.zhixianghui.web.pms.vo.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName ExcelModelVo
 * @Description TODO
 * @Date 2022/2/23 17:53
 */

@Data
public class MerchantExcelModelVo {

    @ExcelProperty(value = "商户编号")
    @ColumnWidth(value = 15)
    private String mchNo;

    @ExcelProperty(value = "商户名称")
    @ColumnWidth(value = 30)
    private String mchName;

    @ExcelProperty(value = "品牌名称")
    @ColumnWidth(value = 30)
    private String branchName;

    @ExcelProperty(value = "供应商编号")
    @ColumnWidth(value = 15)
    private String mainstayNo;

    @ExcelProperty(value = "供应商名称")
    @ColumnWidth(value = 30)
    private String mainstayName;

    @ExcelProperty(value = "销售名称")
    @ColumnWidth(value = 15)
    private String salerName;

    @ExcelProperty(value = "月份")
    @ColumnWidth(value = 8)
    private String month;

    @ExcelProperty(value = "本月实发金额（元）")
    @ColumnWidth(value = 25)
    private BigDecimal thisMonth = new BigDecimal(0.00);

    @ExcelProperty(value = "上月同期实发金额（元）")
    @ColumnWidth(value = 25)
    private BigDecimal lastMonth = new BigDecimal(0.00);

    @ExcelProperty(value = "差值")
    @ColumnWidth(value = 15)
    private BigDecimal balance = new BigDecimal(0.00);

    @ExcelProperty(value = "本月业务收入（元）")
    @ColumnWidth(value = 25)
    private BigDecimal businessIncome = new BigDecimal(0.00);

    @ExcelProperty(value = "本月销售毛利（元）")
    @ColumnWidth(value = 25)
    private BigDecimal thisMonthSalesProfit = new BigDecimal(0.00);

    @ExcelProperty(value = "上月同期销售毛利（元）")
    @ColumnWidth(value = 25)
    private BigDecimal lastMonthSalesProfit = new BigDecimal(0.00);

    @ExcelProperty(value = "是否有合伙人")
    @ColumnWidth(value = 15)
    private String hasAgent;

    @ExcelProperty(value = "本月渠道分润（元）")
    @ColumnWidth(value = 25)
    private BigDecimal agentProfit = new BigDecimal(0.00);

    @ExcelProperty(value = "商户激活时间")
    @ColumnWidth(value = 30)
    private Date activeTime;
}
