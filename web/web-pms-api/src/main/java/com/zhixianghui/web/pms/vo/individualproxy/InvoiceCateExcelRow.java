package com.zhixianghui.web.pms.vo.individualproxy;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class InvoiceCateExcelRow {
    @ExcelProperty(value = "一级类目名称")
    private String firstLevelName;
    @ExcelProperty(value = "明细类目名称")
    private String secondLevelName;
    @ExcelProperty(value = "个税%")
    private String taxRatio;
    @ExcelProperty(value = "增值税%")
    private String vatRatio;
    @ExcelProperty(value = "地方教育附加税%")
    private String localEduAddTaxRatio;
    @ExcelProperty(value = "教育附加税%")
    private String eduAddTaxRatio;
    @ExcelProperty(value = "城建税%")
    private String buildingTaxRatio;
    @ExcelProperty(value = "印花税%")
    private String stampDutyRatio;
}
