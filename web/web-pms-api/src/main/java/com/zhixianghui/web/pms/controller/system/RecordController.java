package com.zhixianghui.web.pms.controller.system;

import com.zhixianghui.common.statics.enums.merchant.DealStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.fee.entity.AgentProductRelation;
import com.zhixianghui.facade.fee.entity.MerchantProductRelation;
import com.zhixianghui.facade.fee.service.AgentProductRelationFacade;
import com.zhixianghui.facade.fee.service.MerchantProductFacade;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantInfoChangeRecord;
import com.zhixianghui.facade.merchant.enums.QuoteAuditEnum;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantInfoChangeRecordFacade;
import com.zhixianghui.facade.merchant.vo.record.RecordVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/28 9:30
 */
@RestController
@RequestMapping("record")
public class RecordController {
    @Reference
    private MerchantInfoChangeRecordFacade changeRecordFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private MerchantProductFacade merchantProductFacade;
    @Reference
    private AgentProductRelationFacade agentProductRelationFacade;
    @Reference
    private AgentFacade agentFacade;

    @RequestMapping("system")
    public RestResult<RecordVo> system(@RequestBody RecordVo recordVo) {
        if (StringUtils.isAnyBlank(recordVo.getMchNo(), recordVo.getType())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("缺少商户号");
        }
        MerchantInfoChangeRecord changeRecord = changeRecordFacade.getLastChange(recordVo.getMchNo());

        if (MerchantTypeEnum.AGENT.getValue() == Integer.parseInt(recordVo.getType())) {
            Agent agent = agentFacade.getByAgentNo(recordVo.getMchNo());
            if (agent == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人不存在");
            }
            List<AgentProductRelation> result = agentProductRelationFacade.listByAgentNoAndStatus(recordVo.getMchNo(), null);
            int status = CollectionUtils.isEmpty(result) ? QuoteAuditEnum.UN_CREATE.getValue() :  QuoteAuditEnum.CREATE.getValue();
            return RestResult.success(RecordVo.build(changeRecord, agent, status));
        }
//        PageResult<List<MerchantProductRelation>> result = merchantProductFacade.listPage(new HashMap<String, Object>() {{
//            put("MCH_NO", recordVo.getMchNo());
//        }}, PageParam.newInstance(1, 10));
//        int status = CollectionUtils.isEmpty(result.getData()) ? QuoteAuditEnum.UN_CREATE.getValue() : QuoteAuditEnum.CREATE.getValue();
        Merchant merchant = merchantFacade.getByMchNo(recordVo.getMchNo());
        int status = merchant.getDealStatus().intValue() == DealStatusEnum.UNDEAL.getValue() ? QuoteAuditEnum.UN_CREATE.getValue() : QuoteAuditEnum.CREATE.getValue();
        return RestResult.success(RecordVo.build(changeRecord, merchant, status));
    }
}
