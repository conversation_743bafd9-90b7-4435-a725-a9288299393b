package com.zhixianghui.web.pms.controller.individualproxy;

import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote;
import com.zhixianghui.facade.common.entity.individualproxy.MainstayIndividualProxy;
import com.zhixianghui.facade.common.service.MainstayIndividualProxyFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.file.ExportBiz;
import com.zhixianghui.web.pms.vo.individualproxy.InvoiceCateExcelRow;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("download")
public class IndividualDownloadController {
    @Reference
    private MainstayIndividualProxyFacade mainstayIndividualProxyFacade;
    @Autowired
    private ExportBiz exportBiz;

    @GetMapping("exportInvoiceCateQuote")
    public void exportInvoiceCateQuote(@RequestParam("mainstayNo") String mainstayNo, HttpServletResponse servletResponse, @CurrentUser PmsOperator pmsOperator) {
        final MainstayIndividualProxy mainstayIndividualProxy = mainstayIndividualProxyFacade.getByMainstayNo(mainstayNo);
        final List<IndividualProxyQuote> taxQuotes = mainstayIndividualProxy.getTaxQuote();
        if (taxQuotes == null || !taxQuotes.isEmpty()) {
            return;
        }
        List<InvoiceCateExcelRow> rows = new ArrayList<>();
        for (IndividualProxyQuote taxQuote : taxQuotes) {
            if (taxQuote.getSubcategorys() == null || taxQuote.getSubcategorys().isEmpty()) {
                for (IndividualProxyQuote subcategory : taxQuote.getSubcategorys()) {
                    InvoiceCateExcelRow row = new InvoiceCateExcelRow();
                    BeanUtil.copyProperties(subcategory, row);
                    row.setFirstLevelName(taxQuote.getInvoiceCategoryName());
                    row.setSecondLevelName(subcategory.getInvoiceCategoryName());
                    rows.add(row);
                }
            }else {
                InvoiceCateExcelRow row = new InvoiceCateExcelRow();
                BeanUtil.copyProperties(row, taxQuote);
                row.setFirstLevelName(taxQuote.getInvoiceCategoryName());
                rows.add(row);
            }
        }
        exportBiz.export(rows, InvoiceCateExcelRow.class, pmsOperator);
        exportBiz.export(servletResponse, ReportTypeEnum.IMPORT_INVOICE_QUOTE.getFileName(),rows,InvoiceCateExcelRow.class);
    }

}
