package com.zhixianghui.web.pms.controller.config;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.config.IndustryType;
import com.zhixianghui.facade.common.service.IndustryTypeFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.config.IndustryTypeInsertVo;
import com.zhixianghui.web.pms.vo.config.IndustryTypeUpdateVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 行业类型配置
 * <AUTHOR>
 * @date 2020/8/10
 **/
@RestController
@RequestMapping("industryType")
public class IndustryTypeController {
    @Reference
    private IndustryTypeFacade industryTypeFacade;

    /**
     * 行业类型保存
     * @param vo
     * @param operator
     * @return
     */
    @PostMapping("save")
    @Permission("config:industryType:save")
    public RestResult<String> save(@Valid @RequestBody IndustryTypeInsertVo vo, @CurrentUser PmsOperator operator) {
        IndustryType industryType = industryTypeFacade.getByCode(vo.getIndustryTypeCode());
        if(!Objects.isNull(industryType)){
            return RestResult.success("行业类型编码已存在");
        }
        IndustryType category = new IndustryType();
        category.setUpdator(operator.getLoginName());
        category.setUpdateTime(new Date());
        category.setIndustryTypeCode(vo.getIndustryTypeCode());
        category.setIndustryTypeName(vo.getIndustryTypeName());
        category.setParentId(vo.getParentId());
        category.setVersion(0);
        category.setCreateTime(new Date());

        industryTypeFacade.insert(category);
        return RestResult.success("成功");
    }

    /**
     * 行业类型更新
     * @param vo
     * @param operator
     * @return
     */
    @PostMapping("update")
    @Permission("config:industryType:update")
    public RestResult<String> update(@Valid @RequestBody IndustryTypeUpdateVo vo, @CurrentUser PmsOperator operator) {
        IndustryType category = industryTypeFacade.getByCode(vo.getIndustryTypeCode());
        if(Objects.isNull(category)){
            return RestResult.error("行业类型不存在");
        }
        category.setIndustryTypeName(vo.getIndustryTypeName());
        category.setUpdateTime(new Date());
        category.setUpdator(operator.getLoginName());
        industryTypeFacade.update(category);
        return RestResult.success("成功");
    }

    /**
     * 行业类型删除
     * @param id
     * @return
     */
    @GetMapping("delete")
    @Permission("config:industryType:delete")
    public RestResult<String> delete(@RequestParam(name = "id") Long id) {
        List<IndustryType> categoryList = industryTypeFacade.listSubIndustryType(id);
        if(CollectionUtils.isNotEmpty(categoryList)){
            return RestResult.error("当前行业类型含有子类目，不允许删除");
        }
        industryTypeFacade.delete(id);
        return RestResult.success("成功");
    }

    /**
     * 根据代码查询行业类型
     * @param industryTypeCode
     * @return
     */
    @PostMapping("getByCode")
    public RestResult<IndustryType> getByCode(@RequestParam(name = "industryTypeCode") String industryTypeCode) {
        IndustryType category = industryTypeFacade.getByCode(industryTypeCode);
        return RestResult.success(category);
    }

    /**
     * 查询所有子行业类型
     * @return
     */
    @GetMapping("listAll")
    public RestResult<List<IndustryType>> listAll() {
        List<IndustryType> categoryList = industryTypeFacade.listAll();
        return RestResult.success(categoryList);
    }

}
