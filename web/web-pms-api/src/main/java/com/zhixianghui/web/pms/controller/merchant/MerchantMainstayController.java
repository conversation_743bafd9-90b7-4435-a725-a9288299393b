package com.zhixianghui.web.pms.controller.merchant;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.enums.bankLink.auth.BankAuthStatusEnum;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.*;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.auth.PayAuthFacade;
import com.zhixianghui.facade.banklink.service.sign.ChannelSignFacade;
import com.zhixianghui.facade.banklink.vo.auth.AuthReqVo;
import com.zhixianghui.facade.banklink.vo.auth.AuthRespVo;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createOrganization.SignCreateOrganizationResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.SignCreatePersonReqVo;
import com.zhixianghui.facade.banklink.vo.sign.createPerson.SignCreatePersonResDataVo;
import com.zhixianghui.facade.banklink.vo.sign.signAuth.SignAuthReqVo;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantMainstayFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.merchant.MerchantMainstayVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.*;

/**
 * 供应商管理
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("merchantMainstay")
public class MerchantMainstayController {

    @Reference
    private MerchantMainstayFacade merchantMainstayFacade;

    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    @Reference
    private SequenceFacade sequenceFacade;

    @Reference
    private MerchantEmployerMainFacade merchantEmployerMainFacade;

    @Reference
    private PayAuthFacade payAuthFacade;
    @Reference
    private ChannelSignFacade channelSignFacade;
    @Reference
    private MerchantFacade merchantFacade;


    /**
     * 创建供应商
     */
    @PostMapping("create")
    @Permission("merchantMainstay:create")
    public RestResult<String> createMerchantMainstay(@RequestBody @Valid MerchantMainstayVO vo, @CurrentUser PmsOperator operator) {
        Date cur = new Date();

        // 生成代征主体编号
        String mchNo = sequenceFacade.nextRedisId(MERCHANT_MAINSTAY_SEQ.getPrefix(),
                MERCHANT_MAINSTAY_SEQ.getKey(), MERCHANT_MAINSTAY_SEQ.getWidth());

        // 商户信息
        Merchant merchant = new Merchant();
        merchant.setMchNo(mchNo);
        merchant.setCreateTime(cur);
        merchant.setUpdateTime(cur);
        merchant.setUpdator(operator.getRealName());
        merchant.setMchName(vo.getMchName());
        merchant.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        merchant.setMchStatus(MchStatusEnum.CREATE.getValue());
        merchant.setAuthStatus(AuthStatusEnum.UN_AUTH.getValue());
        merchant.setActiveTime(cur);
        merchant.setContactName(vo.getContactName());
        merchant.setContactPhone(vo.getContactPhone());
        merchant.setContactEmail(vo.getContactEmail());
        merchant.setFounder(operator.getRealName());

        // 销售信息
        MerchantSaler saler = new MerchantSaler();
        PmsOperator saleOperator = pmsOperatorFacade.getOperatorById(vo.getSaleId());
        if (saleOperator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("销售人员不存在");
        }
        saler.setCreateTime(cur);
        saler.setUpdateTime(cur);
        saler.setUpdator(operator.getRealName());
        saler.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        saler.setSalerId(saleOperator.getId());
        saler.setSalerName(saleOperator.getRealName());
        saler.setSaleDepartmentId(saleOperator.getDepartmentId());
        saler.setSaleDepartmentName(saleOperator.getDepartmentName());
        saler.setMchNo(mchNo);

        List<MerchantFile> fileList = new ArrayList<>();
        // 合作信息文件
        if(StringUtil.isNotEmpty(vo.getEntrustAgreementFileUrl())){
            MerchantFile entrustAgreementFile = new MerchantFile();
            entrustAgreementFile.setUpdator(operator.getLoginName());
            entrustAgreementFile.setMchName(vo.getMchName());
            entrustAgreementFile.setFileUrl(vo.getEntrustAgreementFileUrl());
            entrustAgreementFile.setFileType(MerchantFileTypeEnum.ENTRUST_AGREEMENT.getValue());
            entrustAgreementFile.setVersion(0);
            entrustAgreementFile.setCreateTime(new Date());
            entrustAgreementFile.setMchNo(mchNo);
            fileList.add(entrustAgreementFile);
        }


        MerchantFile agreementTemplate2BFile = new MerchantFile();
        agreementTemplate2BFile.setUpdator(operator.getLoginName());
        agreementTemplate2BFile.setMchName(vo.getMchName());
        agreementTemplate2BFile.setFileUrl(vo.getAgreementTemplate2BFileUrl());
        agreementTemplate2BFile.setFileType(MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_B.getValue());
        agreementTemplate2BFile.setVersion(0);
        agreementTemplate2BFile.setCreateTime(new Date());
        agreementTemplate2BFile.setMchNo(mchNo);
        fileList.add(agreementTemplate2BFile);

        MerchantFile agreementTemplate2CFile = new MerchantFile();
        agreementTemplate2CFile.setUpdator(operator.getLoginName());
        agreementTemplate2CFile.setMchName(vo.getMchName());
        agreementTemplate2CFile.setFileUrl(vo.getAgreementTemplate2CFileUrl());
        agreementTemplate2CFile.setFileType(MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_C.getValue());
        agreementTemplate2CFile.setVersion(0);
        agreementTemplate2CFile.setCreateTime(new Date());
        agreementTemplate2CFile.setMchNo(mchNo);
        fileList.add(agreementTemplate2CFile);

        merchantMainstayFacade.createMainstay(merchant, saler, fileList);

        return RestResult.success("创建供应商成功");
    }

    /**
     * 创建供应商签约信息
     */
    @PostMapping("createSignInfo")
    @Permission("merchantMainstay:create")
    public RestResult<String> createSignInfo(String mainstayNo, @CurrentUser PmsOperator operator) throws Exception {
        //获取信息
        MerchantEmployerMain merchantEmployerMain = merchantEmployerMainFacade.getByMchNo(mainstayNo);
        Merchant mainstay = merchantFacade.getByMchNo(mainstayNo);
        if(merchantEmployerMain == null || mainstay == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("获取代征主体相关详细失败，mainstayNo：["+mainstayNo+"]");
        }
        Merchant.JsonEntity jsonEntity = mainstay.getJsonEntity();
        if(jsonEntity.getSignAccountStatus() != null && jsonEntity.getSignAccountStatus().equals(SignAccountStatusEnum.ACTIVATE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("电签账号已激活,请直接使用 mainstayNo：["+mainstayNo+"]");
        }
        try{
            Integer certificateType = merchantEmployerMain.getCertificateType();
            String legalPersonName = merchantEmployerMain.getLegalPersonName();
            String certificateNumber = merchantEmployerMain.getCertificateNumber();
            String mainstayName = mainstay.getMchName();
            String taxNo = merchantEmployerMain.getTaxNo();

            if(!Objects.equals(CertificateTypeEnum.ID_CARD.getValue(),certificateType)){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("法人证件类型不为身份证,无法鉴权进行签约");
            }
            String legalNo = jsonEntity.getLegalNo();
            if(StringUtils.isBlank(legalNo)){
                //生成法人唯一标识
                legalNo = sequenceFacade.nextRedisId(SIGN_ACCOUNT_SEQ.getPrefix(),
                        SIGN_ACCOUNT_SEQ.getKey(), SIGN_ACCOUNT_SEQ.getWidth());
                String serialNo = sequenceFacade.nextRedisId(AUTH_AGREEMENT_SEQ.getPrefix(),
                        AUTH_AGREEMENT_SEQ.getKey(),AUTH_AGREEMENT_SEQ.getWidth());
                //信息鉴权
                AuthReqVo authReqVo = new AuthReqVo();
                authReqVo.setBankOrderNo(legalNo);
                authReqVo.setAuthType(AuthTypeEnum.IDCARD_NAME.getValue());
                authReqVo.setName(legalPersonName);
                authReqVo.setIdCardNo(certificateNumber);
                authReqVo.setSerialNo(serialNo);
                AuthRespVo authRespVo = payAuthFacade.auth(authReqVo);
                if(!Objects.equals(authRespVo.getAuthStatus(), BankAuthStatusEnum.SUCCESS.getValue())){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("法人信息鉴权失败,请检查信息");
                }
                jsonEntity.setLegalNo(legalNo);
            }
//            String signAccountId = jsonEntity.getSignAccountId();
//            if(StringUtils.isBlank(signAccountId)){
//                //创建法人个人账号
//                SignCreatePersonReqVo signCreatePersonReqVo = new SignCreatePersonReqVo(legalNo,legalPersonName,"CRED_PSN_CH_IDCARD",certificateNumber);
//                EsignResVo<SignCreatePersonResDataVo> account = channelSignFacade.createPersonByThirdPartyUserId(signCreatePersonReqVo);
//                if(!Objects.equals(account.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
//                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创建法人个人账号失败"+account.getMessage());
//                }
//                signAccountId = account.getData().getAccountId();
//                jsonEntity.setSignAccountId(signAccountId);
//            }

//            String orgId = jsonEntity.getSignOrgId();
//            if(StringUtils.isBlank(orgId)){
//                //创建机构账号
//                SignCreateOrganizationReqVo signCreateOrganizationReqVo = new SignCreateOrganizationReqVo(mainstayNo,signAccountId,mainstayName,"CRED_ORG_USCC",taxNo);
//                EsignResVo<SignCreateOrganizationResDataVo> orgVo = channelSignFacade.createOrganization(signCreateOrganizationReqVo);
//                if(!Objects.equals(orgVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
//                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创建机构账号失败,"+orgVo.getMessage());
//                }
//                jsonEntity.setSignOrgId(orgVo.getData().getOrgId());
//            }

            //设置静默签约
//            SignAuthReqVo signAuthReqVo = new SignAuthReqVo(signAccountId);
//            EsignResVo<Boolean> authVo = channelSignFacade.signAuth(signAuthReqVo);
//            if(!Objects.equals(authVo.getRespStatus(), SignChannelStatusEnum.SUCCESS.getValue())){
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("设置静默签约失败," + authVo.getMessage());
//            }

            //保存信息
            jsonEntity.setSignAccountStatus(SignAccountStatusEnum.ACTIVATE.getValue());
            mainstay.setUpdateTime(new Date());
            mainstay.setUpdator(operator.getLoginName());
            merchantFacade.update(mainstay);
            return RestResult.success("创建供应商签约信息成功");
        }catch (Exception e){
            merchantFacade.update(mainstay);
            throw e;
        }
    }
}
