package com.zhixianghui.web.pms.controller.trade;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.withdraw.WithdrawRecordQueryDto;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.TimeRangeUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.trade.OrderBiz;
import com.zhixianghui.web.pms.biz.trade.WithdrawRecordBiz;
import com.zhixianghui.web.pms.biz.trade.offline.OfflineOrderItemBiz;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.trade.req.OrderItemByIdcardQueryVo;
import com.zhixianghui.web.pms.vo.trade.req.OrderItemQueryVo;
import com.zhixianghui.web.pms.vo.trade.req.OrderQueryVo;
import com.zhixianghui.web.pms.vo.trade.req.RecordItemQueryVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderItemPendingExtRespVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderItemResVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderResVo;
import com.zhixianghui.web.pms.vo.trade.res.RecordItemResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @description 批次订单明细
 * @date 2020-11-09 09:33
 **/
@RestController
@RequestMapping("order")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OrderController {

    private final OrderBiz orderBiz;
    private final WithdrawRecordBiz withdrawRecordBiz;
    private final OfflineOrderItemBiz offlineOrderItemBiz;

    @Reference
    private UserInfoFacade userInfoFacade;

    @Permission("order:listOrderItemPage:view")
    @PostMapping("exportExcel/{type}")
    public RestResult exportExcel(@PathVariable Integer type, @Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                  @CurrentUser PmsOperator pmsOperator){
        orderItemQueryTimeHandler(orderItemQueryVo);
        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        orderBiz.exportReceipt(type,orderItemQueryVo,pmsOperator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 发放名单 批次分页查询
     * @param orderQueryVo 查询条件
     * @param pageVo 分页参数
     * @return 分页结果
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderPage")
    public RestResult<PageResult<List<OrderResVo>>> listOrderPage(@Validated @RequestBody OrderQueryVo orderQueryVo, @RequestBody PageVo pageVo){
        orderQueryTimeHandler(orderQueryVo);
        validDate(orderQueryVo.getCreateBeginDate(), orderQueryVo.getCreateEndDate(), orderQueryVo.getCompleteBeginDate(), orderQueryVo.getCompleteEndDate());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OrderResVo>> pageResult = orderBiz.listOrderPage(orderQueryVo,pageParam);
        return RestResult.success(pageResult);
    }

    /**
     * 发放名单 批次总条数
     * @param orderQueryVo 查询条件
     * @return 总条数
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("countOrder")
    public RestResult<Map<String,Object>> countOrder(@Validated @RequestBody OrderQueryVo orderQueryVo){
        orderQueryTimeHandler(orderQueryVo);
        validDate(orderQueryVo.getCreateBeginDate(), orderQueryVo.getCreateEndDate(), orderQueryVo.getCompleteBeginDate(), orderQueryVo.getCompleteEndDate());
        Long totalRecord = orderBiz.countOrder(orderQueryVo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 订单明细 分页查询
     * @param orderItemQueryVo 查询条件
     * @param pageVo 分页参数
     * @return 分页结果
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("listOrderItemPage")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemPage(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,@RequestBody  PageVo pageVo){
        log.info("转换前的参数:{}", JSONUtil.toJsonStr(orderItemQueryVo));
        orderItemQueryTimeHandler(orderItemQueryVo);

        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());

        log.info("转换后的参数:{}", JSONUtil.toJsonStr(orderItemQueryVo));

        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OrderItemResVo>> pageResult = orderBiz.listOrderItemPage(orderItemQueryVo,pageParam);
        return RestResult.success(pageResult);
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @return 总条数
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("countOrderItem")
    public RestResult<Map<String,Object>> countOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo){

        orderItemQueryTimeHandler(orderItemQueryVo);

        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        Long totalRecord = orderBiz.countOrderItem(orderItemQueryVo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @return 总条数
     */
    @Permission("order:listOrderItemPage:sum")
    @PostMapping("sumOrderItem")
    public RestResult<OrderItemSumBo> sumOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo){
        orderItemQueryTimeHandler(orderItemQueryVo);

        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        OrderItemSumBo sumBo = orderBiz.sumOrderItem(orderItemQueryVo);
        return RestResult.success(sumBo);
    }

    /**
     * 打款交易流水 分页查询
     * @param recordItemQueryVo 查询条件
     * @param pageVo 分页参数
     * @return 分页结果
     */
    @Permission("order:listRecordItemPage:view")
    @PostMapping("listRecordItemPage")
    public RestResult<PageResult<List<RecordItemResVo>>> listRecordItemPage(@Validated @RequestBody RecordItemQueryVo recordItemQueryVo,@RequestBody  PageVo pageVo){
        recordItemQueryTimeHandler(recordItemQueryVo);
        validDate(recordItemQueryVo.getCreateBeginDate(), recordItemQueryVo.getCreateEndDate(), recordItemQueryVo.getCompleteBeginDate(), recordItemQueryVo.getCompleteEndDate());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<RecordItemResVo>> pageResult = orderBiz.listRecordItemPage(recordItemQueryVo,pageParam);
        return RestResult.success(pageResult);
    }

    /**
     * 打款交易流水 总条数
     * @param recordItemQueryVo 查询条件
     * @return 总条数
     */
    @Permission("order:listRecordItemPage:view")
    @PostMapping("countRecordItem")
    public RestResult<Map<String,Object>> countRecordItem(@Validated @RequestBody RecordItemQueryVo recordItemQueryVo){
        recordItemQueryTimeHandler(recordItemQueryVo);
        validDate(recordItemQueryVo.getCreateBeginDate(), recordItemQueryVo.getCreateEndDate(), recordItemQueryVo.getCompleteBeginDate(), recordItemQueryVo.getCompleteEndDate());
        Long totalRecord = orderBiz.countRecordItem(recordItemQueryVo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 退回本地冻结金额
     * @param platTrxNo
     * @return
     */
    @Permission("order:listRecordItemPage:refund")
    @GetMapping("refundFrozenAmount")
    public RestResult<String> refundFrozenAmount(@RequestParam String platTrxNo) {
        try {
            orderBiz.refundLocalFrozenAmount(platTrxNo);
            return RestResult.success("退回成功");
        } catch (BizException e) {
            return RestResult.error(e.getErrMsg());
        } catch (Exception e) {
            log.error("执行本地冻结金额退回时出现异常 platTrxNo:{}", platTrxNo, e);
            return RestResult.error("系统异常");
        }
    }


    private void validDate(Date createBeginDate, Date createEndDate, Date completeBeginDate, Date completeEndDate) {

        if(DateUtil.compare(createBeginDate, createEndDate, Calendar.SECOND) > 0){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
        }

        if((completeBeginDate != null && completeEndDate == null) ||
                ( completeBeginDate == null && completeEndDate != null)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间必须成对选择");
        }

        if(completeBeginDate != null && DateUtil.compare(completeBeginDate, completeEndDate, Calendar.SECOND) > 0){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间的截止时间不能大于起始时间");
        }
    }

    /**
     * 订单明细-导出
     * @param orderItemQueryVo 查询条件
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("exportOrderItem")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "订单明细-导出")
    public RestResult<String> exportOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo, @CurrentUser PmsOperator operator){
        orderItemQueryTimeHandler(orderItemQueryVo);
        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        orderBiz.exportOrderItem(orderItemQueryVo,operator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 打款流水-导出
     * @param recordItemQueryVo 查询条件
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("exportRecordItem")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "打款流水-导出")
    public RestResult<String> exportRecordItem(@Validated @RequestBody RecordItemQueryVo recordItemQueryVo, @CurrentUser PmsOperator operator){
        recordItemQueryTimeHandler(recordItemQueryVo);
        validDate(recordItemQueryVo.getCreateBeginDate(), recordItemQueryVo.getCreateEndDate(), recordItemQueryVo.getCompleteBeginDate(), recordItemQueryVo.getCompleteEndDate());
        orderBiz.exportRecordItem(recordItemQueryVo,operator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 重新受理
     */
    @Permission("order:acceptAgain")
    @PostMapping("acceptAgain")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "重新受理")
    public RestResult<String> acceptAgain(@RequestParam String platBatchNo){
        orderBiz.acceptAgain(platBatchNo);
        return RestResult.success("提交成功");
    }

    /**
     * 重新发放
     * 判断批次状态
     * 不能导致代替商户发放
     */
    @Permission("order:grantAgain")
    @PostMapping("grantAgain")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "重新发放")
    public RestResult<String> grantAgain(@RequestParam String platBatchNo){
        orderBiz.grantAgain(platBatchNo);
        return RestResult.success("提交成功");
    }

    /**
     * 打款流水反查
     * @param remitPlatTrxNo 打款流水号
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "打款流水反查")
    @Permission("order:recordItem:reverseQuery")
    @PostMapping("reverseQuery")
    public RestResult<String> reverseQuery(@RequestParam String remitPlatTrxNo) {
        String msg = orderBiz.reverseQuery(remitPlatTrxNo);
        return RestResult.success(msg);
    }

    @Permission("order:withdraw.view")
    @PostMapping("listWithdrawRecordPage")
    public RestResult<PageResult<List<WithdrawRecord>>> listWithdrawRecordPage(@RequestBody @Validated WithdrawRecordQueryDto withdrawRecordQueryDto, @CurrentUser PmsOperator pmsOperator, @RequestBody PageParam pageParam) {
        validDate(withdrawRecordQueryDto.getCreateBeginDate(), withdrawRecordQueryDto.getCreateEndDate());

        log.info("查询参数:{}", JSON.toJSONString(withdrawRecordQueryDto));
        return RestResult.success(withdrawRecordBiz.listWithRecordPage(withdrawRecordQueryDto,pageParam));
    }

    @Permission("order:withdraw.view")
    @PostMapping("sumWithdrawRecord")
    public RestResult<Map<String,Object>> sumWithdrawRecord(@RequestBody @Validated WithdrawRecordQueryDto withdrawRecordQueryDto) {
        validDate(withdrawRecordQueryDto.getCreateBeginDate(), withdrawRecordQueryDto.getCreateEndDate());

        log.info("查询参数:{}", JSON.toJSONString(withdrawRecordQueryDto));
        return RestResult.success(withdrawRecordBiz.sumWithdrawRecord(BeanUtil.toMap(withdrawRecordQueryDto)));
    }

    @Permission("order:withdraw.view")
    @PostMapping("exportWithRecord")
    public RestResult<String> exportWithRecord(@RequestBody @Validated WithdrawRecordQueryDto withdrawRecordQueryDto, @CurrentUser PmsOperator pmsOperator) {
        validDate(withdrawRecordQueryDto.getCreateBeginDate(), withdrawRecordQueryDto.getCreateEndDate());

        withdrawRecordBiz.exportWithRecord(withdrawRecordQueryDto,pmsOperator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("getOrderItemByPlatTrxNo")
    public RestResult<OrderItemPendingExtRespVo> getOrderItemByPlatTrxNo(@RequestParam String platTrxNo) {
        if (platTrxNo.startsWith("OT")) {
            return offlineOrderItemBiz.getOrderItemByPlatTrxNo(platTrxNo);
        }

        final OrderItem orderItem = orderBiz.getItemByPlatTrxNo(platTrxNo);

        if (Objects.isNull(orderItem) || StringUtils.isBlank(orderItem.getReceiveIdCardNoMd5())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("平台流水号为{}的订单详情不存在或者详情不存在身份证号");
        }

        OrderItemPendingExtRespVo respVo = new OrderItemPendingExtRespVo();
        BeanUtil.copyProperties(orderItem, respVo);
        if (StringUtils.isNotBlank(orderItem.getReceiveName())) {
            respVo.setReceiveName(orderItem.getReceiveNameDecrypt());
        }
        if (StringUtils.isNotBlank(orderItem.getReceiveIdCardNo())) {
            respVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDecrypt());
        }
        if (StringUtils.isNotBlank(orderItem.getReceiveAccountNo())) {
            respVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDecrypt());
        }
        if (StringUtils.isNotBlank(orderItem.getReceivePhoneNo())) {
            respVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDecrypt());
        }

        //查询用户信息
        UserInfo userInfo = userInfoFacade.getByIdCardNoMd5(orderItem.getReceiveIdCardNoMd5());
        if (userInfo != null){
            respVo.setIdCardBackUrl(userInfo.getIdCardBackUrl());
            respVo.setIdCardCopyUrl(userInfo.getIdCardCopyUrl());
            respVo.setIdCardFrontUrl(userInfo.getIdCardFrontUrl());
        }
        return RestResult.success(respVo);
    }

    @PostMapping("putOrderExpireCache")
    public RestResult<String> putOrderExpireCache(@Validated @RequestBody OrderQueryVo orderQueryVo){
        orderQueryVo.setCreateBeginDate(TrxNoDateUtil.compareMinWithEndDate(orderQueryVo.getCreateBeginDate()));
        orderQueryVo.setCreateEndDate(TrxNoDateUtil.compareMinWithEndDate(orderQueryVo.getCreateEndDate()));
        orderBiz.putOrderExpireCache(orderQueryVo);
        return RestResult.success("放入队列成功");
    }

    @PostMapping("getOrderItemByIdcard")
    public RestResult<PageResult<List<OrderItemResVo>>> getOrderItemByIdcard(@Validated @RequestBody OrderItemByIdcardQueryVo queryVo, @RequestBody PageParam pageParam) {

        //1. 根据平台流水号查询订单
        final OrderItem orderItem = orderBiz.getItemByPlatTrxNo(queryVo.getPlatTrxNo());
        if (Objects.isNull(orderItem) || StringUtils.isBlank(orderItem.getReceiveIdCardNoMd5())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("平台流水号为{}的订单详情不存在或者详情不存在身份证号");
        }

        //2. 拼装时间
        Date now = new Date();
        final Date firstOfMonth = DateUtil.getFirstOfMonth(now);
        final Date lastOfMonth = DateUtil.getLastOfMonth(now);

        log.info("startTime:{},endTime:{}", firstOfMonth, lastOfMonth);

        OrderItemQueryVo orderItemQueryVo = new OrderItemQueryVo();
        orderItemQueryVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDecrypt());
        orderItemQueryVo.setCreateBeginDate(firstOfMonth);
        orderItemQueryVo.setCreateEndDate(lastOfMonth);
        final PageResult<List<OrderItemResVo>> pageResult = orderBiz.listOrderItemPage(orderItemQueryVo, pageParam);
        return RestResult.success(pageResult);
    }

    private void validDate(Date createBeginDate, Date createEndDate) {

        if (createBeginDate != null && createEndDate != null) {
            if(DateUtil.compare(createBeginDate, createEndDate, Calendar.SECOND) > 0){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
            }
        }
    }

    private void orderQueryTimeHandler(OrderQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchBatchNo(), orderQueryVo.getPlatBatchNo())) {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestThreeMonthStartTime());
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }else {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }
        }
    }

    private void orderItemQueryTimeHandler(OrderItemQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchBatchNo(), orderQueryVo.getPlatTrxNo(),orderQueryVo.getMchOrderNo())) {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestThreeMonthStartTime());
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }else {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }
        }
    }

    private void recordItemQueryTimeHandler(RecordItemQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getPlatBatchNo(), orderQueryVo.getPlatTrxNo(),orderQueryVo.getMchOrderNo(),orderQueryVo.getRemitPlatTrxNo())) {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestThreeMonthStartTime());
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }else {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }
        }
    }
}
