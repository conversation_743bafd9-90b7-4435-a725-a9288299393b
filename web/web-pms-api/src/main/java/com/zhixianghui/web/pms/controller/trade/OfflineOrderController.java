package com.zhixianghui.web.pms.controller.trade;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.trade.offline.OfflineOrderBiz;
import com.zhixianghui.web.pms.biz.trade.offline.OfflineOrderItemBiz;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.trade.req.OrderItemQueryVo;
import com.zhixianghui.web.pms.vo.trade.req.OrderQueryVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderItemResVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

@RestController
@RequestMapping("offlineOrder")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OfflineOrderController {
    private final OfflineOrderBiz offlineOrderBiz;
    private final OfflineOrderItemBiz orderItemBiz;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private UserInfoFacade userInfoFacade;

    /**
     * 发放名单
     * @param orderQueryVo 查询条件
     * @return 发放批次列表
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderPage")
    public RestResult<PageResult<List<OrderResVo>>> listOrderPage(@Validated @RequestBody OrderQueryVo orderQueryVo,
                                                                  @RequestBody PageVo pageVo) {
        orderQueryTimeHandler(orderQueryVo);
        PageResult<List<OrderResVo>> result = offlineOrderBiz.listOrderPage(orderQueryVo,pageVo);
        return RestResult.success(result);
    }

    /**
     * 订单明细-导出
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("exportOrderItem")
    public RestResult<String> exportOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo, @CurrentUser PmsOperator operator){
        checkDate(orderItemQueryVo);
        Map<String, Object> paramMap = BeanUtil.toMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo,paramMap);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.PMS_OFFLINE_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.PMS_OFFLINE_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.PMS_OFFLINE_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private void handlerAmount(OrderItemQueryVo orderItemQueryVo, Map<String, Object> paramMap) {
        String amountMaxStr = orderItemQueryVo.getOrderItemNetAmountMax();
        String amountMinStr = orderItemQueryVo.getOrderItemNetAmountMin();
        if (StringUtil.isNotEmpty(amountMaxStr) && !ValidateUtil.isDoubleAnd2decimals(amountMaxStr)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最大金额必须是数字，且最多两位小数");
        }else {
            paramMap.remove("orderItemNetAmountMax");
        }
        if (StringUtil.isNotEmpty(amountMinStr) && !ValidateUtil.isDoubleAnd2decimals(amountMinStr)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最小金额必须是数字，且最多两位小数");
        }else {
            paramMap.remove("orderItemNetAmountMin");
        }

        if(StringUtil.isNotEmpty(amountMaxStr) && StringUtil.isNotEmpty(amountMinStr)){
            BigDecimal amountMax = new BigDecimal(amountMaxStr);
            BigDecimal amountMin = new BigDecimal(amountMinStr);
            if (amountMax.compareTo(amountMin) < 0) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最大金额不能小于最小金额");
            }
            paramMap.put("orderItemNetAmountMax", amountMax);
            paramMap.put("orderItemNetAmountMin", amountMin);
        }
    }

    /**
     * 订单明细
     * @param orderItemQueryVo 查询条件
     * @return 订单明细列表
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("listOrderItemPage")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemPage(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                                          @RequestBody PageVo pageVo) {
        checkDate(orderItemQueryVo);
        PageResult<List<OrderItemResVo>> result = orderItemBiz.listOrderItemPage(orderItemQueryVo,pageVo);
        return RestResult.success(result);
    }
    private void orderQueryTimeHandler(OrderQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchBatchNo())) {
                orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestOneYearStartTime());
                orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
            }else {
                orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
            }
        }
    }

    private void checkDate(@RequestBody @Validated OrderItemQueryVo orderItemQueryVo) {

        orderItemQueryTimeHandler(orderItemQueryVo);
        Date completeBeginDate = orderItemQueryVo.getCreateBeginDate();
        Date completeEndDate = orderItemQueryVo.getCreateEndDate();
        if ((completeBeginDate != null && completeEndDate == null) ||
                (completeBeginDate == null && completeEndDate != null)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间必须成对选择");
        }

        if (completeBeginDate != null && DateUtil.compare(completeBeginDate, completeEndDate, Calendar.SECOND) > 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间的截止时间不能大于起始时间");
        }
    }

    private void orderItemQueryTimeHandler(OrderItemQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        log.info("原始参数：{}", JSON.toJSONString(orderQueryVo));

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchBatchNo(), orderQueryVo.getMchOrderNo(),orderQueryVo.getPlatTrxNo())) {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestOneYearStartTime());
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }else {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }
        }
        log.info("时间转换后的参数:{}", JSON.toJSONString(orderQueryVo));
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @return 总条数
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("countOrderItem")
    public RestResult<Map<String, Object>> countOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo) {
        checkDate(orderItemQueryVo);
        Long totalRecord = orderItemBiz.countOrderItem(orderItemQueryVo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @return 总条数
     */
    @PostMapping("sumOrderItem")
    public RestResult<OrderItemSumBo> sumOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo){
        checkDate(orderItemQueryVo);
        OrderItemSumBo sumBo = orderItemBiz.sumOrderItem(orderItemQueryVo);
        return RestResult.success(sumBo);
    }

    /**
     * 重新受理
     */
    @Permission("order:acceptAgain")
    @PostMapping("acceptAgain")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "重新受理")
    public RestResult<String> acceptAgain(@RequestParam String platBatchNo){
        offlineOrderBiz.acceptAgain(platBatchNo);
        return RestResult.success("提交成功");
    }

}
