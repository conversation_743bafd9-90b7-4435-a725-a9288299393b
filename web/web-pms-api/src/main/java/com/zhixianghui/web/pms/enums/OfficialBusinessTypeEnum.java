package com.zhixianghui.web.pms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @ClassName OfficialBusinessTypeEnum
 * @Description TODO
 * @Date 2023/5/23 15:09
 */
@Getter
@AllArgsConstructor
public enum  OfficialBusinessTypeEnum {


    SERVICE(100,"财税服务商"),

    AGENT(101,"渠道代理商"),

    MERCHANT(103,"用工企业");

    private int value;

    private String desc;

    public static OfficialBusinessTypeEnum getEnum(int value) {
        return Arrays.stream(values()).filter(p -> p.value == value).findFirst().orElse(null);
    }
}
