package com.zhixianghui.web.pms.controller.merchant;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.facade.banklink.service.cmb.CmbFacade;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("accountBook")
public class EmployerAccountBookController {

    @Reference
    private CmbFacade cmbFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @GetMapping("queryDailyBalance")
    public JSONObject queryDailyBalance(String employerNo,String mainstayNo) {

        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo, mainstayNo, ChannelTypeEnum.BANK.getValue());
        MainstayChannelRelation mainstayChannelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(mainstayNo, ChannelNoEnum.CMB.name());
        return cmbFacade.queryDailyAccountNoBalance(mainstayChannelRelation.getJoinBankNo(), mainstayChannelRelation.getChannelMchNo(), employerAccountInfo.getSubMerchantNo(), DateUtil.offsetDay(new Date(), -11).toString("yyyyMMdd"), DateUtil.offsetDay(new Date(), -1).toString("yyyyMMdd"));
    }

}
