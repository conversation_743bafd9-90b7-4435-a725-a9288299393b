package com.zhixianghui.web.pms.vo.common;

import com.zhixianghui.common.statics.enums.fee.SalesSpecialRuleTypeEnum;
import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class SpecialFeeRuleVo {
    /**
     * 特殊规则类型
     *
     * @see com.zhixianghui.common.statics.enums.fee.ProductFeeSpecialRuleTypeEnum
     * @see com.zhixianghui.common.statics.enums.fee.CostFeeSpecialRuleTypeEnum
     * @see SalesSpecialRuleTypeEnum
     */
    @NotNull(message = "特殊规则类型不能为空")
    private Integer specialRuleType;

    /**
     * @see com.zhixianghui.common.statics.enums.common.CompareTypeEnum
     */
    @NotNull(message = "特殊规则比较类型不能为空")
    @EnumValue(intValues = {1, 2, 3, 4 ,5})
    private Integer compareType;
    /**
     * 符合的值
     */
    @NotEmpty(message = "特殊规则匹配值不能为空")
    private String value;
}