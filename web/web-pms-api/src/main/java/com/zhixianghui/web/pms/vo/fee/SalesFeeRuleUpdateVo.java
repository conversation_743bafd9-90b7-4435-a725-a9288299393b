package com.zhixianghui.web.pms.vo.fee;

import com.zhixianghui.common.util.validator.EnumValue;
import com.zhixianghui.web.pms.vo.common.SpecialFeeRuleVo;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/11
 **/
@Data
public class SalesFeeRuleUpdateVo extends BaseFeeRuleVo{
    @NotNull(message = "id 不能为空")
    private Long id;

    /**
     * 部门id
     */
    @NotNull(message = "部门不能为空")
    private Long departmentId;

    /**
     * 部门名称
     */
    @NotEmpty(message = "部门不能为空")
    private String departmentName;

    /**
     * 产品编码
     */
    @NotEmpty(message = "产品编码不能为空")
    private String productNo;

    /**
     * 产品名称
     */
    @NotEmpty(message = "产品名称不能为空")
    private String productName;

    /**
     * 备注
     */
    private String description;
}
