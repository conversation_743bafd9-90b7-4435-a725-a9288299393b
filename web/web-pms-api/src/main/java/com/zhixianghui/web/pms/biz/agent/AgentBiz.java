package com.zhixianghui.web.pms.biz.agent;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zhixianghui.common.statics.enums.agent.AgentStatusEnum;
import com.zhixianghui.common.statics.enums.agent.RelationTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.export.vo.ExportVo;
import com.zhixianghui.facade.fee.entity.AgentFeeRule;
import com.zhixianghui.facade.fee.service.AgentFeeRuleFacade;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.merchant.dto.AgentBaseInfoDto;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.service.agent.AgentFunctionFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.facade.merchant.vo.agent.*;
import com.zhixianghui.web.pms.biz.CommonBiz;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.agent.req.AgentQueryVo;
import com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo;
import com.zhixianghui.web.pms.vo.agent.res.MerchantResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.MERCHANT_AGENT_SEQ;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/2 17:29
 **/
@Service
@Slf4j
public class AgentBiz extends CommonBiz {
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private AgentManagerFacade agentManagerFacade;
    @Reference
    private AgentFacade agentFacade;
    @Reference
    private AgentFeeRuleFacade feeRuleFacade;
    @Reference
    private AgentBankAccountFacade agentBankAccountFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private AgentFunctionFacade agentFunctionFacade;
    @Reference
    private AgentEmployerMainFacade agentEmployerMainFacade;
    @Reference
    private AgentProductQuoteFacade agentProductQuoteFacade;
    @Reference
    private AgentSalerFacade agentSalerFacade;
    @Reference
    private AgentCredentialFacade credentialFacade;
    @Reference
    private MerchantFileFacade merchantFileFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private PmsPermissionFacade pmsPermissionFacade;

    public Agent generateAgent(AgentAddVo vo,Integer agentType, PmsOperator operator) {
        Date cur = new Date();
        // 生成合伙人编号
        String agentNo = sequenceFacade.nextRedisId(MERCHANT_AGENT_SEQ.getPrefix(),
                MERCHANT_AGENT_SEQ.getKey(), MERCHANT_AGENT_SEQ.getWidth());
        //补充不需要前端传的内容
        vo.setAgentNo(agentNo);
        vo.setAgentType(agentType);
        PmsOperator pmsOperator = pmsOperatorFacade.getOperatorById(vo.getSalerId());
        if (pmsOperator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("销售不存在");
        }
        vo.setSalerId(vo.getSalerId());
        vo.setSalerName(pmsOperator.getRealName());

        //基本信息
        Agent agent = new Agent();
        agent.setUpdateTime(cur);
        agent.setCreateTime(cur);
        agent.setUpdater(operator.getRealName());
        agent.setAgentStatus(AgentStatusEnum.CREATE.getValue());
        agent.setAgentType(agentType);
        agent.setAuthStatus(AuthStatusEnum.UN_AUTH.getValue());
        agent.setFounder(operator.getRealName());

        BigDecimal taxPercent = vo.getTaxPercent() == null ? null : new BigDecimal(vo.getTaxPercent());
        agent.setWithholdingTaxRatio(taxPercent);
        BeanUtils.copyProperties(vo,agent);
        return agent;
    }

    public AgentSaler generateAgentSale(Agent agent, AgentAddVo vo, PmsOperator operator) {
        //销售
        AgentSaler agentSaler = new AgentSaler();
        agentSaler.setUpdateTime(agent.getUpdateTime());
        agentSaler.setCreateTime(agent.getCreateTime());
        agentSaler.setUpdater(operator.getRealName());
        agentSaler.setSaleDepartmentId(operator.getDepartmentId());
        agentSaler.setSaleDepartmentName(operator.getDepartmentName());
        BeanUtils.copyProperties(vo, agentSaler);
        return agentSaler;
    }

    public void createAgent(Agent agent, AgentSaler agentSaler) {
        agentManagerFacade.generateAgent(agent, agentSaler);
    }

    @Deprecated
//    public Map<String,Object> createAgent(AgentVo vo,Integer agentType, PmsOperator operator) {
//        Agent agent = generateAgent(vo, agentType, operator);
//        AgentSaler agentSaler = generateAgentSale(agent, vo, operator);
//
//        log.info("[{}] 创建合伙人,名称:{},销售:{}",agent.getAgentNo(), vo.getAgentName(), operator.getRealName());
//        Long id = agentManagerFacade.createAgent(agent, agentSaler, JsonUtil.toString(vo));
//        Map<String, Object> result = Maps.newHashMap();
//        result.put("id", id);
//        result.put("createTime", DateUtil.formatDateTime(agent.getCreateTime()));
//        result.put("submitName", operator.getRealName());
//        return result;
//    }

    public List<SimpleAgentInfoVo> getAllAgentSimple() {
        return agentFacade.listAllSimpleAgentInfo();
    }

    public PageResult<List<AgentResVo>> listAgentPage(AgentQueryVo agentQueryVo, PageParam pageParam,PmsOperator operator) {
        Map<String,Object> paramMap = BeanToMapUtil.beanToMap(agentQueryVo);
        // 销售部只能查自己及下属的商户
        putIfSaler(operator, paramMap);
        return agentFacade.listVoPage(paramMap,pageParam);
    }

    public void exportAgent(ExportVo exportVo, PmsOperator operator) {
        Map<String,Object> paramMap = exportVo.getParamMap();
        // 销售部只能查自己及下属的商户
        putIfSaler(operator, paramMap);
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGENT_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.AGENT_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.AGENT_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        exportVo.getFieldInfoList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
    }

    public PageResult<List<MerchantResVo>> listAgentMerchantRelationPage(String agentNo, Integer relationType, PageParam pageParam, PmsOperator operator) {
        RelationTypeEnum relation = RelationTypeEnum.getEnumByValue(relationType);
        if(relation == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("筛选商户关系类型不存在,relationType:" + relationType);
        }
        List<AgentResVo> result = agentFacade.listVoPage(Collections.singletonMap("agentNo",agentNo),PageParam.newInstance(1,1)).getData();
        if(CollectionUtils.isEmpty(result)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人不存在,agentNo:" + agentNo);
        }

        // 判断该合伙人是否属于自己或旗下销售的
        if (operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                //合伙人的销售是否旗下销售之一
                if(!salerIds.contains(result.get(0).getSalerId())){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人不在旗下,不允许查看,agentNo:" + agentNo);
                }
            }
        }

        PageResult<List<Merchant>> merResult;

        //直接 不需要查下属
        if(relation == RelationTypeEnum.DIRECT){
            merResult = merchantQueryFacade.listPage(Collections.singletonMap("agentNo",agentNo),pageParam);
            return getMerListPageResult(merResult);
        }

        List<String> agentNos = Lists.newArrayList();
        //查询直接下属合伙人
        List<Agent> agents = agentFacade.listBy(Collections.singletonMap("inviterNo",agentNo));
        if(!CollectionUtils.isEmpty(agents)){
            agents.forEach(x->agentNos.add(x.getAgentNo()));
        }

        //全部加上自身
        if(relation == RelationTypeEnum.ALL){
            agentNos.add(agentNo);
        }

        //此时还是为空说明是间接且没有下属邀请人
        if(CollectionUtils.isEmpty(agentNos)){
            return PageResult.newInstance(pageParam,null);
        }
        //间接
        merResult = merchantQueryFacade.listPage(Collections.singletonMap("agentNos",agentNos),pageParam);
        return getMerListPageResult(merResult);
    }

    private PageResult<List<MerchantResVo>> getMerListPageResult(PageResult<List<Merchant>> merResult) {
        if(!CollectionUtils.isEmpty(merResult.getData())){
            List<MerchantResVo> list = merResult.getData().stream().map(MerchantResVo::toVo).collect(Collectors.toList());
            return PageResult.newInstance(list,merResult.getPageCurrent(),merResult.getPageSize(),merResult.getTotalRecord());
        }else{
            return PageResult.newInstance(null,merResult.getPageCurrent(),merResult.getPageSize(),merResult.getTotalRecord());
        }
    }


    public void batchSetSeller(List<String> agentNoList, Long sellerId, PmsOperator operator) {
        LimitUtil.notEmpty(agentNoList,"设置的合伙人不能为空");
        List<Agent> agentList = agentFacade.listBy(Collections.singletonMap("agentNoList",agentNoList));
        LimitUtil.notEmpty(agentNoList,"要设置的合伙人找不到");
        agentList.forEach(
                agent -> {
                    if(!Objects.equals(agent.getAgentStatus(),AgentStatusEnum.ACTIVE.getValue()) &&
                       !Objects.equals(agent.getAgentStatus(),AgentStatusEnum.INACTIVE.getValue()) ){
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("要设置的agentNo:" + agent.getAgentNo() +",不为激活或冻结状态");
                    }
                    Map<String, Object> paramMap = new HashMap<String, Object>(){
                        private static final long serialVersionUID = 3126600321871705128L;
                        { put("inviterNo", agent.getAgentNo()); }};
                    List<Agent> tmp = agentFacade.listBy(paramMap);
                    if (!CollectionUtils.isEmpty(tmp)) {
                        for (Agent item : tmp) {
                            agentNoList.add(item.getAgentNo());
                        }
                    }
                }
        );
        agentManagerFacade.batchSetSeller(agentNoList,sellerId,operator.getRealName());
    }

    public void batchSetInviter(List<String> agentNoList, String inviterNo, PmsOperator operator) {
        LimitUtil.notEmpty(agentNoList,"设置的合伙人不能为空");
        if(agentNoList.contains(inviterNo)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("设置邀请人不能为自己,合伙人编号："+inviterNo);
        }
        List<Agent> agentList = agentFacade.listBy(Collections.singletonMap("agentNoList",agentNoList));
        LimitUtil.notEmpty(agentNoList,"要设置的合伙人找不到");
        agentList.forEach(
                agent -> {
                    if(!Objects.equals(agent.getAgentStatus(),AgentStatusEnum.ACTIVE.getValue()) &&
                            !Objects.equals(agent.getAgentStatus(),AgentStatusEnum.INACTIVE.getValue()) ){
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("要设置的agentNo:" + agent.getAgentNo() +",不为激活或冻结状态");
                    }
                }
        );
        agentManagerFacade.batchSetInviter(agentNoList,inviterNo,operator.getRealName());
    }

    public AgentProductQuoteVo getAgentProductQuoteVo(Long id) {
        AgentProductQuote agentProductQuote = agentFacade.getAgentProductQuote(id);
        if (agentProductQuote == null) {
            return null;
        }
        AgentProductQuoteVo vo = new AgentProductQuoteVo();
        BeanUtils.copyProperties(agentProductQuote, vo);
        List<SpecialFeeRuleVo> list = JsonUtil.toBean(agentProductQuote.getRuleParam(), List.class);
        vo.setRuleParam(list);
        return vo;
    }

    public AgentDetailVo getAgentDetail(String agentNo, PmsOperator operator) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agentNo", agentNo);
        // 销售部只能查自己及下属的商户
        putIfSaler(operator, paramMap);

        PageResult<List<AgentResVo>> listPageResult = agentFacade.listVoPage(paramMap, PageParam.newInstance(1, 1));
        LimitUtil.notEmpty(listPageResult, "合伙人信息不存在");

        AgentDetailVo agentDetailVo = agentFacade.getDetailByAgentNo(agentNo);

        if (!CollectionUtils.isEmpty(agentDetailVo.getQuoteVoList())) {
            for (AgentProductQuoteVo item : agentDetailVo.getQuoteVoList()) {
                AgentFeeRule agentFeeRule = feeRuleFacade.listPage(item.getAgentNo(), item.getProductNo());
                if (agentFeeRule == null) {
                    continue;
                }
                build(item, agentFeeRule);
            }
        }
        return agentDetailVo;
    }

    private void build(AgentProductQuoteVo quoteVo, AgentFeeRule agentFeeRule) {
        quoteVo.setDescription(agentFeeRule.getDescription());
        if (agentFeeRule.getFirstFeeRate() != null) {
            quoteVo.setFeeRate(AmountUtil.mul(agentFeeRule.getFirstFeeRate(), new BigDecimal(100)));
        }
        quoteVo.setFixedFee(agentFeeRule.getFirstFixedFee());
        quoteVo.setFormulaType(agentFeeRule.getFirstFormulaType());
        quoteVo.setMaxFee(agentFeeRule.getMaxFee());
        quoteVo.setMinFee(agentFeeRule.getMinFee());
        quoteVo.setPriority(agentFeeRule.getPriority());
        quoteVo.setRuleType(agentFeeRule.getRuleType());
        if (agentFeeRule.getSecondFeeRate() != null) {
            quoteVo.setSecondFeeRate(AmountUtil.mul(agentFeeRule.getSecondFeeRate(), new BigDecimal(100)));
        }
        quoteVo.setSecondFixedFee(agentFeeRule.getSecondFixedFee());
        quoteVo.setSecondFormulaType(agentFeeRule.getSecondFormulaType());
        if (!CollectionUtils.isEmpty(agentFeeRule.getSpecialFeeRuleList())) {
            agentFeeRule.getSpecialFeeRuleList().forEach(item -> {
                SpecialFeeRuleVo specialFeeRuleVo = new SpecialFeeRuleVo();
                BeanUtil.copyProperties(specialFeeRuleVo, item);
                quoteVo.getRuleParam().add(specialFeeRuleVo);
            });
        }
    }

    public void legal(AgentVo agentVo, PmsOperator operator) {
        if(agentVo.getAgentNo().equals(agentVo.getInviterNo())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("设置邀请人不能为自己");
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("agentNo", agentVo.getAgentNo());
        putIfSaler(operator, paramMap);

        PageResult<List<AgentResVo>> listPageResult = agentFacade.listVoPage(paramMap, PageParam.newInstance(1, 1));
        LimitUtil.notEmpty(listPageResult, "合伙人信息不存在");
        if (flowFacade.isExistNotFinishedFlow(FlowTypeEnum.PMS_AGENT_MODIFY,agentVo.getAgentNo())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前合伙人变更流程审核未完成, 请审核后再提交");
        }

    }

    @Deprecated
    public void updateAgentDetail(AgentVo vo, Integer agentType, PmsOperator operator) {
        legal(vo, operator);
        agentManagerFacade.updateAgentDetail(vo, agentType, operator.getLoginName());
    }

    /**
     * 销售部员工增加条件判断
     * @param operator
     * @param paramMap
     */
    private void putIfSaler(PmsOperator operator, Map<String, Object> paramMap) {
        // 销售部只能查自己及下属的商户
        if (!crmRole(operator.getId(),pmsPermissionFacade) && operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }
    }

    private List<Long> getSubSalerIds(PmsOperator operator) {
        List<Long> salerIds = new ArrayList<>();
        try{
            List<PmsOperator> salers = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
            salerIds = salers.stream().map(PmsOperator::getId).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            salerIds.add(operator.getId());
        }
        return salerIds;
    }

    public void changeStatus(String agentNo, Integer status, PmsOperator operator) {
        Agent agent = agentFacade.getOne(Collections.singletonMap("agentNo",agentNo));
        if(agent == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无对应的合伙人 agentNo:" + agentNo);
        }

        if(Objects.equals(agent.getAgentStatus(),status)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("操作状态与原状态一致，不进行修改 agentNo:" + agentNo);
        }
        if(Objects.equals(agent.getAgentStatus(),AgentStatusEnum.RETREAT.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人状态为已清退 不允许修改状态 agentNo:" + agentNo);
        }

        if(Objects.equals(agent.getAgentStatus(),AgentStatusEnum.CREATE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人状态为已创建 请通过审批激活 agentNo:" + agentNo);
        }

        agent.setAgentStatus(status);
        agent.setUpdater(operator.getRealName());
        agent.setUpdateTime(new Date());
        agentManagerFacade.changeStatus(agent);
    }


    public List<SimpleAgentInfoVo> listNotRetreatAgentSimple(Long salerId) {
        return agentFacade.listNotRetreatAgentSimple(salerId);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <V> void saveFunction(List<V> list) {
        agentFunctionFacade.saveFunction((List<AgentFunction>) list);
    }

    public void saveAgentExtInfo(AgentAddVo addVo) {
        agentManagerFacade.saveAgentExtInfo(addVo);
    }


    public AgentBaseInfoVo getAgent(String agentNo) {
        Agent agent = agentFacade.getByAgentNo(agentNo);
        AgentSaler agentSaler = agentSalerFacade.getByAgentNo(agentNo);

        AgentBaseInfoVo baseInfoVo = new AgentBaseInfoVo();
        BeanUtil.copyProperties(agent, baseInfoVo);
        baseInfoVo.setSalerId(agentSaler.getSalerId());
        baseInfoVo.setSalerName(agentSaler.getSalerName());

        return baseInfoVo;
    }

    public AgentEmployerMain mainInfo(String agentNo) {
        AgentEmployerMain agentEmployerMain = agentEmployerMainFacade.getByAgentNo(agentNo);
        return agentEmployerMain;
    }

    public AgentCredential agentCredential(String agentNo) {
        AgentCredential agentCredential = credentialFacade.getAgentCredential(agentNo);
        return agentCredential;
    }

    public AgentBankAccount bankInfo(String agentNo) {
        return agentBankAccountFacade.getByAgentNo(agentNo);
    }

    public List<AgentProductQuote> cooperationInfo(String agentNo) {
        return agentProductQuoteFacade.getByAgentNo(agentNo);
    }

    public List<AgentProductQuote> cooperationInfoByParams(Map<String, Object> params) {
        return agentProductQuoteFacade.getByParams(params);
    }

    public void editAgentBaseInfo(AgentBaseInfoVo agentBaseInfoVo,PmsOperator pmsOperator) {
        AgentBaseInfoDto agentBaseInfoDto = new AgentBaseInfoDto();
        BeanUtil.copyProperties(agentBaseInfoVo,agentBaseInfoDto);
        agentManagerFacade.editAgentBaseInfo(agentBaseInfoDto,pmsOperator);
    }

    public AgentMainVo buildMainInfo(String agentNo) {
        AgentEmployerMain agentEmployerMain = mainInfo(agentNo);
        AgentCredential agentCredential = agentCredential(agentNo);

        Map<String, Object> param = new HashMap<>();
        param.put("mchNo", agentNo);
        List<MerchantFile> merchantFileList = merchantFileFacade.listBy(param);

        AgentMainVo agentMainVo = new AgentMainVo();

        if (agentCredential == null && agentEmployerMain == null) {
            return null;
        }

        if (agentCredential != null) {
            BeanUtil.copyProperties(agentCredential, agentMainVo);
        }
        if (agentEmployerMain != null) {
            BeanUtil.copyProperties(agentEmployerMain,agentMainVo);
        }

        if (merchantFileList != null) {
            for (MerchantFile merchantFile : merchantFileList) {

                if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.BUSINESS_LICENSE.getValue()) {
                    agentMainVo.setBusinessLicenseFileUrl(merchantFile.getFileUrl());
                }

                if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.ID_CARD_HEAD.getValue()) {
                    agentMainVo.setIdCardHeadFileUrl(merchantFile.getFileUrl());
                }

                if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue()) {
                    agentMainVo.setIdCardEmblemFileUrl(merchantFile.getFileUrl());
                }

                if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.ID_CARD_COPY.getValue()) {
                    agentMainVo.setIdCardCopyFileUrl(merchantFile.getFileUrl());
                }
            }
        }
        return agentMainVo;
    }

    public void preSaveAgentQuote(AgentProductQuoteVo quoteVo,PmsOperator pmsOperator,Long flowId) {

        if (StringUtils.isNotBlank(quoteVo.getMainstayNo())&&StringUtils.isBlank(quoteVo.getMainstayName())) {
            Merchant mainstay = merchantQueryFacade.getByMchNo(quoteVo.getMainstayNo());
            if (mainstay == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体不存在:" + quoteVo.getMainstayNo());
            }
            log.info("合伙人报价单预存储-获取代征主体:{}", JSONUtil.toJsonStr(mainstay));
            quoteVo.setMainstayName(mainstay.getMchName());
        }

        AgentProductQuote agentProductQuote = new AgentProductQuote();
        agentProductQuote.setAgentNo(quoteVo.getAgentNo());
        agentProductQuote.setAgentName(quoteVo.getAgentName());
        agentProductQuote.setProductNo(quoteVo.getProductNo());
        agentProductQuote.setFeeRate(quoteVo.getFeeRate());
        agentProductQuote.setFixedFee(quoteVo.getFixedFee());
        agentProductQuote.setFormulaType(quoteVo.getFormulaType());
        agentProductQuote.setMainstayName(quoteVo.getMainstayName());
        agentProductQuote.setUpdater(pmsOperator.getLoginName());
        agentProductQuote.setMainstayNo(quoteVo.getMainstayNo());
        agentProductQuote.setStatus(CommonStatusEnum.INACTIVE.getValue());
        agentProductQuote.setFlowId(flowId);
        agentProductQuote.setProductName(quoteVo.getProductName());
        agentProductQuote.setRuleType(quoteVo.getRuleType());
        agentProductQuote.setRuleParam(JSONObject.toJSONString(quoteVo.getRuleParam()));
        agentProductQuote.setMainstayNo(quoteVo.getMainstayNo());
        agentProductQuote.setMainstayName(quoteVo.getMainstayName());
        agentProductQuote.setPriority(quoteVo.getPriority());
        agentProductQuote.setSecondFeeRate(quoteVo.getSecondFeeRate());
        agentProductQuote.setSecondFixedFee(quoteVo.getSecondFixedFee());
        agentProductQuote.setSecondFormulaType(quoteVo.getSecondFormulaType());
        agentProductQuote.setMaxFee(quoteVo.getMaxFee());
        agentProductQuote.setMinFee(quoteVo.getMinFee());
        agentProductQuote.setDescription(quoteVo.getDescription());
        agentProductQuote.setCalculateMode(quoteVo.getCalculateMode());

        Date now = new Date();
        agentProductQuote.setUpdateTime(now);
        agentProductQuote.setCreateTime(now);
        agentProductQuote.setVersion(0);
        agentProductQuote.setRealProfitRatio(quoteVo.getRealProfitRatio());
        agentProductQuoteFacade.preSaveAgentQuote(agentProductQuote);
    }

    public List<Agent> listActiveAgent() {
        final HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("agentStatus", 100);
        return agentFacade.listBy(paramMap);
    }
}
