package com.zhixianghui.web.pms.vo.file;

import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/4/6 14:53
 */
public class FileVo {
    @Data
    public static class PmsFile {
        private String name;
        private String number;
        private String functionType;
        private String permissionFlag;
        private String url;                     //  后端API地址
        private String parentPermissionFlag;

        public <T> PmsFunction toPmsFunction(T data) {
            PmsFile pmsFile = (PmsFile) data;
            PmsFunction pmsFunction = new PmsFunction();
            pmsFunction.setCreateTime(new Date());
            pmsFunction.setNumber(pmsFile.getNumber());
            pmsFunction.setName(pmsFile.getName());
            pmsFunction.setPermissionFlag(pmsFile.getPermissionFlag());
            pmsFunction.setFunctionType(pmsFile.getFunctionType() == null ? null: Integer.parseInt(pmsFile.getFunctionType()));
            pmsFunction.setUrl(pmsFile.getUrl());
            pmsFunction.setCreateTime(new Date());
            pmsFunction.setParentPermissionFlag(pmsFile.getParentPermissionFlag());

            return pmsFunction;
        }

    }

    @Data
    public static class EmployerFile {
        private String name;
        private String number;
        private String type;
        private String permissionFlag;
        private String url;                     //  后端API地址
        private String parentPermissionFlag;


        public <T> EmployerFunction toEmployerFunction(T data) {
            EmployerFile employerFile = (EmployerFile) data;
            EmployerFunction employerFunction = new EmployerFunction();

            employerFunction.setNumber(employerFile.getNumber());
            employerFunction.setName(employerFile.getName());
            employerFunction.setPermissionFlag(employerFile.getPermissionFlag());
            employerFunction.setType(employerFile.getType() == null ? null: Integer.parseInt(employerFile.getType()));
            employerFunction.setUrl(employerFile.getUrl());
            employerFunction.setCreateTime(new Date());
            employerFunction.setUpdateTime(new Date());
            employerFunction.setParentPermissionFlag(employerFile.getParentPermissionFlag());
            return employerFunction;
        }
    }

    @Data
    public static class SupplyFile {
        private String name;
        private String number;
        private String type;
        private String permissionFlag;
        private String url;                     //  后端API地址
        private String parentPermissionFlag;


        public <T> SupplierFunction toSupplyFileFunction(T data) {
            SupplyFile supplyFile = (SupplyFile) data;
            SupplierFunction supplierFunction = new SupplierFunction();
            supplierFunction.setUpdateTime(new Date());
            supplierFunction.setCreateTime(new Date());
            supplierFunction.setNumber(supplyFile.getNumber());
            supplierFunction.setName(supplyFile.getName());
            supplierFunction.setPermissionFlag(supplyFile.getPermissionFlag());
            supplierFunction.setType(supplyFile.getType() == null ? null: Integer.parseInt(supplyFile.getType()));
            supplierFunction.setUrl(supplyFile.getUrl());
            supplierFunction.setParentPermissionFlag(supplyFile.getParentPermissionFlag());

            return supplierFunction;
        }
    }

    @Data
    public static class AgentFile {
        private String name;
        private String number;
        private String type;
        private String permissionFlag;
        private String url;                     //  后端API地址
        private String parentPermissionFlag;


        public <T> AgentFunction toAgentFunction(T data) {
            AgentFile agentFile = (AgentFile) data;
            AgentFunction agentFunction = new AgentFunction();
            agentFunction.setUpdateTime(new Date());
            agentFunction.setCreateTime(new Date());
            agentFunction.setNumber(agentFile.getNumber());
            agentFunction.setName(agentFile.getName());
            agentFunction.setPermissionFlag(agentFile.getPermissionFlag());
            agentFunction.setType(agentFile.getType() == null ? null: Integer.parseInt(agentFile.getType()));
            agentFunction.setUrl(agentFile.getUrl());
            agentFunction.setParentPermissionFlag(agentFile.getParentPermissionFlag());
            return agentFunction;
        }
    }
}
