package com.zhixianghui.web.pms.vo.merchant.agreement;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 协议查询Vo
 * @date 2020-09-07 14:35
 **/
@Data
public class AgreementQueryVo {
    /**
     * 任务主题
     */
    private String topicLike;

    /**
     * 销售ID
     */
    private Long salerId;

    /**
     * 签署人编号列表
     */
    private List<String> signerNoList;

    /**
     * 协议负责人名称
     */
    private String signNameLike;

    /**
     * 协议状态
     */
    private Integer status;

    /**
     * 查询的起始日期
     */
    private String beginDate;

    /**
     * 查询的截止日期
     */
    private String endDate;

    /**
     * 签署类型
     */
    private Integer signType;

    /**
     * 截止时间
     */
    private String beginDeadLine;

    /**
     * 截止时间
     */
    private String endDeadLine;

    /**
     * 完成时间
     */
    private String beginFinishTime;

    /**
     * 完成时间
     */
    private String endFinishTime;

}
