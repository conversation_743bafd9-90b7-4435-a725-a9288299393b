package com.zhixianghui.web.pms.controller.trade;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.enums.RechargeStatusEnum;
import com.zhixianghui.facade.trade.enums.RechargeTypeEnum;
import com.zhixianghui.facade.trade.service.RechargeQueryFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.trade.OrderBiz;
import com.zhixianghui.web.pms.vo.trade.req.RechargeRecordEditVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RestController
@RequestMapping("recharge")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class RechargeQueryController {

    @Reference
    private RechargeQueryFacade rechargeQueryFacade;
    @Reference
    private EmailFacade emailFacade;
    @Reference
    private MerchantFacade merchantFacade;

    final private OrderBiz orderBiz;

    @PostMapping("listRecharge")
    @Permission("recharge:list:view")
    public RestResult<PageResult<List<RechargeRecord>>> listPage(@RequestBody Map<String, Object> paramMap,@RequestBody PageParam pageParam) {
        return RestResult.success(rechargeQueryFacade.list(paramMap, pageParam));
    }

    @PostMapping("getByRechargeId")
    @Permission("recharge:list:view")
    public RestResult<RechargeRecord> getByRechargeId(String rechargeId) {
        return RestResult.success(rechargeQueryFacade.getByRechargeId(rechargeId));
    }

    @PostMapping("getById")
    @Permission("recharge:list:view")
    public RestResult<RechargeRecord> getById(String id) {
        return RestResult.success(rechargeQueryFacade.getById(id));
    }

    @PostMapping("exportRechargeRecord")
    @Permission("recharge:list:view")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "充值记录-导出")
    public RestResult<String> exportRechargeRecord(@RequestBody Map<String, Object> paramMap, @CurrentUser PmsOperator operator) {
        String startTime = (String) paramMap.get("createBeginDate");
        String endTime = (String) paramMap.get("createEndDate");
        if (StringUtils.isAnyBlank(startTime, endTime)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("请提供起止创建时间");
        }

        validDate(LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        orderBiz.exportRechargeRecord(paramMap,operator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("exportRechargeRecordZip")
    @Permission("recharge:list:view")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "充值记录-导出回单")
    public RestResult<String> exportRechargeRecordZip(@RequestBody Map<String, Object> paramMap, @CurrentUser PmsOperator operator) {
        String startTime = (String) paramMap.get("createBeginDate");
        String endTime = (String) paramMap.get("createEndDate");
        if (StringUtils.isAnyBlank(startTime, endTime)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("请提供起止创建时间");
        }

        validDate(LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        paramMap.put("exportFileType",FileTypeEnum.ZIP.getValue());
        orderBiz.exportRechargeRecordZip(paramMap,operator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("editRecord")
    @Logger(type = OperateLogTypeEnum.MODIFY)
    public RestResult<RechargeRecord> edit(@Validated @RequestBody RechargeRecordEditVo rechargeRecordEditVo, @CurrentUser PmsOperator operator) {

        RechargeRecord rechargeRecord = rechargeQueryFacade.getByRechargeId(rechargeRecordEditVo.getRechargeOrderId());
        if (Objects.isNull(rechargeRecord)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("充值订单不存在");
        }

        if (rechargeRecord.getChannelType() == ChannelTypeEnum.ALIPAY.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付宝充值记录不允许修改");
        }

        if (rechargeRecordEditVo.getRechargeStatus() == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("状态不能为空");
        }

        if (StringUtils.isNotBlank(rechargeRecordEditVo.getRemark()) && rechargeRecordEditVo.getRemark().length() > 50) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("备注长度超出限额[50]");
        }

        if (rechargeRecordEditVo.getEmailNotify()&&rechargeRecordEditVo.getRechargeStatus()!=RechargeStatusEnum.SUCCESS.getCode()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("充值状态不是成功，不允许发邮件通知");
        }

        rechargeRecord.setRechargeAmount(new BigDecimal(rechargeRecordEditVo.getRechargeAmount()));
        rechargeRecord.setUpdateTime(new Date());
        rechargeRecord.setCreateBy(operator.getLoginName());
        rechargeRecord.setRechargeStatus(RechargeStatusEnum.getEnum(rechargeRecordEditVo.getRechargeStatus()).getCode().shortValue());
        if (!Objects.isNull(rechargeRecordEditVo.getRemark())) {
            rechargeRecord.setRemark(rechargeRecordEditVo.getRemark());
        }

        if (!Objects.isNull(rechargeRecordEditVo.getReceiptUrl())) {
            rechargeRecord.setReceiptUrl(rechargeRecordEditVo.getReceiptUrl());
        }

        rechargeQueryFacade.updateById(rechargeRecord);

        //发送邮件通知
        this.sendRechargeEmail(rechargeRecord,rechargeRecordEditVo);

        return RestResult.success(rechargeRecord);
    }

    private void validDate(LocalDateTime createBeginDate, LocalDateTime createEndDate) {
        if(createBeginDate.isAfter(createEndDate)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
        }
    }

    private void sendRechargeEmail(RechargeRecord rechargeRecord,RechargeRecordEditVo rechargeRecordEditVo) {
        if (rechargeRecordEditVo.getEmailNotify()) {
            final Merchant merchant = merchantFacade.getByMchNo(rechargeRecord.getEmployerNo());
            final String contactEmail = merchant.getContactEmail();

            log.info("向{}发送充值成功通知邮件",contactEmail);
            if (StringUtils.isNotBlank(contactEmail)) {
                EmailParamDto paramDto = new EmailParamDto();
                paramDto.setTo(contactEmail);
                paramDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
                paramDto.setHtmlFormat(true);
                paramDto.setSubject("智享汇综合服务平台充值到账通知");
                paramDto.setTpl(EmailTemplateEnum.RECHARGE_SUCCESS_NOTIFY.getName());
                Map<String, Object> tplParam = new HashMap<>();
                tplParam.put("employerNo", merchant.getMchName());
                tplParam.put("rechargeOrderId", rechargeRecord.getRechargeOrderId());
                tplParam.put("createTime", DateUtil.formatDateTime(rechargeRecord.getCreateTime()));
                tplParam.put("channelType", ChannelTypeEnum.getEnum(rechargeRecord.getChannelType()).getDesc()+"通道");
                tplParam.put("rechargeType", RechargeTypeEnum.getEnum(rechargeRecord.getRechargeType()).getMessage());
                paramDto.setTplParam(tplParam);

                log.info("邮件信息是:{}", JSONObject.toJSONString(paramDto));
                emailFacade.sendAsync(paramDto);
            }
        }
    }

    @PostMapping("sumRechargeRecord")
    @Permission("recharge:list:view")
    public RestResult<Map<String,Object>> sumRechargeRecord(@RequestBody Map<String, Object> paramMap) {
        return RestResult.success(rechargeQueryFacade.sumRechargeRecord(paramMap));
    }
}
