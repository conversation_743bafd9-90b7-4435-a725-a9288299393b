package com.zhixianghui.web.pms.biz.approval;

import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.HandleStatus;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowDetailFacade;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.vo.approval.req.HandleApprovalDetailReqVo;
import com.zhixianghui.web.pms.vo.approval.res.ApprovalDetailResVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020-08-18 10:19
 **/
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApprovalFlowDetailBiz {
    @Reference
    private ApprovalFlowDetailFacade approvalFlowDetailFacade;
    @Reference
    private ApprovalFlowFacade approvalFlowFacade;

    public RestResult<String> handleApprovalDetail(HandleApprovalDetailReqVo handleApprovalDetailReqVo, PmsOperator currentOperator) {
        Integer handleStatus = handleApprovalDetailReqVo.getHandleStatus();
        Integer flowTopicType = handleApprovalDetailReqVo.getFlowTopicType();
        Long approvalDetailId = handleApprovalDetailReqVo.getApprovalDetailId();
        String approvalOpinion = handleApprovalDetailReqVo.getApprovalOpinion();
        boolean isAdmin = PmsOperatorTypeEnum.ADMIN.getValue() == currentOperator.getType();
        if(handleStatus.equals(HandleStatus.AGREE.getValue())){
            approvalFlowDetailFacade.agreeApprovalDetail(approvalDetailId,
                    currentOperator.getId(),
                    PlatformSource.OPERATION.getValue(),
                    FlowTopicType.getNameByValue(flowTopicType),approvalOpinion,isAdmin);
                return RestResult.success("成功通过");
        }else if(handleStatus.equals(HandleStatus.DISAGREE.getValue())){
            approvalFlowDetailFacade.disAgreeApprovalDetail(approvalDetailId,
                    currentOperator.getId(),
                    PlatformSource.OPERATION.getValue(),
                    FlowTopicType.getNameByValue(flowTopicType),approvalOpinion,isAdmin);
            return RestResult.success("成功驳回");
        }
        return RestResult.error("操作类型有误");
    }

    public List<ApprovalDetailResVo> getApprovalDetail(Long approvalFlowId, PmsOperator currentOperator) {
        List<ApprovalFlowDetail> result = approvalFlowDetailFacade.listByApprovalFlowIdAndHandlerId(approvalFlowId,currentOperator.getId(), PmsOperatorTypeEnum.ADMIN.getValue() == currentOperator.getType());
        return result.stream().map(ApprovalDetailResVo::buildVo).collect(Collectors.toList());
    }
}
