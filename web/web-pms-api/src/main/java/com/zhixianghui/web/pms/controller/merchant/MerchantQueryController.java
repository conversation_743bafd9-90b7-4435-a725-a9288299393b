package com.zhixianghui.web.pms.controller.merchant;


import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import com.zhixianghui.facade.merchant.vo.MerchantInfoVo;
import com.zhixianghui.facade.merchant.vo.MerchantPositionVo;
import com.zhixianghui.facade.merchant.vo.merchant.BranchVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.controller.flow.CommonFlow;
import com.zhixianghui.web.pms.vo.merchant.MerchantSalerQueryVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
/**
 * Author: Cmf
 * Date: 2020.2.14
 * Time: 11:56
 * Description:
 */
@RequestMapping("merchant")
@RestController
public class MerchantQueryController extends CommonFlow {
    private Logger log = LoggerFactory.getLogger(MerchantQueryController.class);

    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantEmployerFacade merchantEmployerFacade;
    @Reference
    private MerchantEmployerCooperateFacade employerCooperateFacade;
    @Reference
    private MerchantEmployerMainFacade employerMainFacade;
    @Reference
    private MerchantBankAccountFacade bankAccountFacade;
    @Reference
    private MerchantEmployerPositionFacade positionFacade;
    @Reference
    private MerchantEmployerQuoteFacade quoteFacade;
    @Reference
    private MerchantFileFacade fileFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private MerchantInvoiceInfoFacade invoiceInfoFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private PmsPermissionFacade pmsPermissionFacade;

    @PostMapping("listAll")
    public RestResult listAll(@RequestBody MerchantSalerQueryVO vo,@CurrentUser PmsOperator operator){
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        // 销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("departmentId", operator.getDepartmentId());
                paramMap.put("salerIds", salerIds);
            }
        }
        return RestResult.success(merchantQueryFacade.listAll(paramMap));
    }

    /**
     * 获取商户列表
     *
     * @param vo     .
     * @return .
     */
    @RequestMapping("listMerchantPage")
    public RestResult<PageResult<List<MerchantInfoVo>>> listMerchantPage(
            @Valid @RequestBody MerchantSalerQueryVO vo, @CurrentUser PmsOperator operator
    ) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        paramMap.put("departmentId", vo.getDepartmentId());
        // 销售部只能查自己及下属的商户
        if(!crmRole(operator.getId(),pmsPermissionFacade) && operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }

        PageResult pageResult = merchantQueryFacade.listExtObjectPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(pageResult);
    }

    /**
     * 根据商户编码获取发票类目
     * @return .
     */
    @GetMapping("getInvoiceList")
    public RestResult<List<Map<String, Object>>> getInvoiceList(@RequestParam(name = "mchNo") String mchNo) {
        List<MerchantEmployerPosition> positionList = positionFacade.listByMchNo(mchNo);
        List<Map<String, Object>> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(positionList)){
            for(MerchantEmployerPosition position : positionList){
                Map<String, Object> map = new HashMap<>();
                map.put("serviceDesc", position.getServiceDesc());
                map.put("workCategoryName", position.getWorkCategoryName());
                map.put("workCategoryCode", position.getWorkCategoryCode());
                map.put("invoiceCategoryList", position.getJsonEntity().getInvoiceCategoryList());
                resultList.add(map);
            }
        }
        return RestResult.success(resultList);
    }

    /**
     * 获取供应商商户列表
     * @return .
     */
    @RequestMapping("listActiveMainstay")
    public RestResult<List<Merchant>> listActiveMainstay() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
        paramMap.put("merchantType", MerchantTypeEnum.MAINSTAY.getValue());
        List<Merchant> merchantList = merchantQueryFacade.listBy(paramMap);
        return RestResult.success(merchantList);
    }

    /**
     * 获取供应商商户列表
     * @return .
     */
    @RequestMapping("templateActiveMainstayList")
    public RestResult<List<MerchantVo>> templateActiveMainstayList() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
        paramMap.put("merchantType", MerchantTypeEnum.MAINSTAY.getValue());
        List<Merchant> merchantList = merchantQueryFacade.listBy(paramMap);
        if (CollectionUtils.isEmpty(merchantList)) {
            return RestResult.success(null);
        }
        List<MerchantVo> merchantVoList = new ArrayList<>();
        merchantList.forEach(item -> {
            MerchantVo merchantVo = new MerchantVo(item.getMchNo(), item.getMchName(), item.getMerchantType(), item.getTemplateId());
            merchantVoList.add(merchantVo);
        });
        return RestResult.success(merchantVoList);
    }
    @Data
    public static class MerchantVo {
        public String mchNo;
        public String mchName;
        private Integer merchantType;
        private String templateId;
        public MerchantVo(String mchNo, String mchName, Integer merchantType, String templateId) {
            this.mchNo = mchNo;
            this.mchName = mchName;
            this.merchantType = merchantType;
            this.templateId = templateId;
        }
    }

    @RequestMapping("listActiveEmployer")
    public RestResult<List<MerchantVo>> listActiveEmployer() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
        paramMap.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        List<Merchant> merchantList = merchantQueryFacade.listBy(paramMap);
        if (CollectionUtils.isEmpty(merchantList)) {
            return RestResult.success(null);
        }
        List<MerchantVo> merchantVoList = new ArrayList<>();
        merchantList.forEach(item -> {
            MerchantVo merchantVo = new MerchantVo(item.getMchNo(), item.getMchName(), item.getMerchantType(), item.getTemplateId());
            merchantVoList.add(merchantVo);
        });
        return RestResult.success(merchantVoList);
    }

    /**
     * 获取所有商户列表
     * @return .
     */
    @RequestMapping("listAllActiveMch")
    public RestResult<List<Map<String, Object>>> listAllActiveMch() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
        List<Map<String, Object>> resultList = this.listAllMerchants(paramMap);
        return RestResult.success(resultList);
    }

    /**
     * 获取所有商户列表
     * @return .
     */
    @RequestMapping("listAllMch")
    public RestResult<List<Map<String, Object>>> listAllMch() {
        Map<String, Object> paramMap = new HashMap<>();
        List<Map<String, Object>> resultList = this.listAllMerchants(paramMap);
        return RestResult.success(resultList);
    }

    /**
     * 获取详细信息
     * 方便前端处理，数据按照请求的分类
     * @param mchNo
     * @param operator
     * @return
     */
    @Deprecated
    @RequestMapping("getEmployerInfoVO")
    @Permission("merchantEmployer:plat:view")
    public RestResult<Map<String, Object>> getEmployerInfoVO(@RequestParam String mchNo, @CurrentUser PmsOperator operator){
        if(StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mchNo", mchNo);

        // 根据部门id, 查出对应商户列表, 如销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }
        PageResult<List<Object>> pageResult = merchantQueryFacade.listExtObjectPage(paramMap, PageParam.newInstance(1, 1));
        if(CollectionUtils.isEmpty(pageResult.getData())){
            return RestResult.error("商户不存在");
        }

        MerchantInfoVo infoVo = (MerchantInfoVo)pageResult.getData().get(0);
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);

        // 合作信息
        Map<String, Object> cooperateMap = new HashMap<>();
        cooperateMap.put("contactName", merchant.getContactName());
        cooperateMap.put("contactPhone", merchant.getContactPhone());
        cooperateMap.put("mchName", merchant.getMchName());
        cooperateMap.put("salerId", infoVo.getSalerId());
        cooperateMap.put("salerName", infoVo.getSalerName());
        //备注：注意生产合并
        cooperateMap.put("remark",merchant.getRemark());

        // 主体信息
        MerchantEmployerMain main = employerMainFacade.getByMchNo(mchNo);
        Map<String, Object> mainMap = BeanUtil.toMap(main);
        mainMap.put("contactEmail", merchant.getContactEmail());
        mainMap.put("servicePhone", merchant.getServicePhone());
        mainMap.put("contactName", merchant.getContactName());
        mainMap.put("contactPhone", merchant.getContactPhone());
        mainMap.put("mchName", merchant.getMchName());

        // 银行账号信息
        MerchantBankAccount bankAccount = bankAccountFacade.getByMchNo(mchNo);

        // 文件信息
        List<MerchantFile> fileList = fileFacade.listByMchNo(mchNo);
        List<String> cooperateFileList = new ArrayList<>();
        List<String> supplementFileUrls = new ArrayList<>();
        for (MerchantFile merchantFile : fileList){
            if(merchantFile.getFileType() == MerchantFileTypeEnum.COMPANY_LEAFLET.getValue()){
                cooperateFileList.add(merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue()){
                supplementFileUrls.add(merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.BUSINESS_LICENSE.getValue()){
                mainMap.put("businessLicenseFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.ID_CARD_HEAD.getValue()){
                mainMap.put("idCardHeadFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue()){
                mainMap.put("idCardEmblemFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.ID_CARD_COPY.getValue()){
                mainMap.put("idCardCopyFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.DOOR_PHOTO.getValue()){
                mainMap.put("doorPhotoFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.WORK_INDOOR.getValue()){
                mainMap.put("workIndoorFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.RECEPTION.getValue()){
                mainMap.put("receptionFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.ENTRUST_AGREEMENT.getValue()){
                cooperateMap.put("entrustAgreementFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_B.getValue()){
                cooperateMap.put("agreementTemplate2BFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_C.getValue()){
                cooperateMap.put("agreementTemplate2CFileUrl", merchantFile.getFileUrl());
            }
        }
        cooperateMap.put("cooperateFileList", cooperateFileList);
        cooperateMap.put("supplementFileUrls", supplementFileUrls);

        // 商户开票信息
        MerchantInvoiceInfo invoiceInfo = invoiceInfoFacade.getByMchNo(mchNo);
        Map<String, Object> invoiceInfoMap = BeanUtil.toMap(invoiceInfo);

        // 用工企业特有信息
        //账户信息
        List<String> employerAccountList = new ArrayList<>();
        if(merchant.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            MerchantEmployerCooperate cooperate = employerCooperateFacade.getByMchNo(mchNo);
            Map<String, Object> cooperateVoMap = BeanUtil.toMap(cooperate);
            cooperateMap.putAll(cooperateVoMap);

            // 报价单信息
            List<MerchantEmployerQuote> quoteList = quoteFacade.listByMchNo(mchNo);
            cooperateMap.put("quoteVoList", quoteList);

            Map<String, String> map = new HashMap<>();
            for (MerchantEmployerQuote merchantEmployerQuote : quoteList) {
                map.put(merchantEmployerQuote.getMainstayMchNo(), merchantEmployerQuote.getMainstayMchName());
            }

            map.forEach((mainstayNo,mainstayName)->{

                final List<EmployerAccountInfo> employerAccountInfos = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(mchNo, mainstayNo);

                final List<EmployerAccountInfo> collect = employerAccountInfos.stream().filter(it -> it.getStatus().intValue() == CommonStatusEnum.ACTIVE.getValue()).collect(Collectors.toList());

                log.info(JSON.toJSONString(employerAccountInfos));
                log.info(JSON.toJSONString(collect));
                if (collect != null && !collect.isEmpty()) {
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("mainstayNo", mainstayNo);
                    dataMap.put("mainstayName", mainstayName);
                    dataMap.put("employerAccountInfo", collect);
                    employerAccountList.add(JSON.toJSONString(dataMap));
                }
            });

            // 岗位信息
            List<MerchantEmployerPosition> positionList = positionFacade.listByMchNo(mchNo);
            // 获取所有绑定的发票类目信息
            List<InvoiceCategoryVo> invoiceCategoryVoList = new ArrayList<>();
            List<MerchantPositionVo> positionVoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(positionList)) {
                positionList.forEach(x -> {
                    invoiceCategoryVoList.addAll(x.getJsonEntity().getInvoiceCategoryList());
                    MerchantPositionVo merchantPositionVo = new MerchantPositionVo();
                    buildMerchantPositionVo(merchantPositionVo,x);
                    positionVoList.add(merchantPositionVo);
                });
            }
            cooperateMap.put("positionVoList", positionVoList);

            // 去重
            List<InvoiceCategoryVo> uniqueInvoiceCategoryVoList = invoiceCategoryVoList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InvoiceCategoryVo::getInvoiceCategoryCode))), ArrayList::new)
            );
            invoiceInfoMap.put("invoiceCategoryVoList", uniqueInvoiceCategoryVoList);
        }



        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cooperate", cooperateMap);
        resultMap.put("main", mainMap);
        resultMap.put("bankAccount", bankAccount);
        resultMap.put("employerAccountInfo", employerAccountList);
        resultMap.put("invoiceInfo", invoiceInfoMap);

        return RestResult.success(resultMap);
    }

    @GetMapping("listQuoteByEmployerNo")
    private RestResult<List<MerchantEmployerQuote>> listQuoteByEmployerNo(@RequestParam String mchNo) {
        Map<String,Object> map = new HashMap<>();
        map.put("mchNo",mchNo);
        map.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        List<MerchantEmployerQuote> quoteList = quoteFacade.listBy(map);
        return RestResult.success(quoteList);
    }

    private void buildMerchantPositionVo(MerchantPositionVo merchantPositionVo, MerchantEmployerPosition x) {
        merchantPositionVo.setId(x.getId());
        merchantPositionVo.setInvoiceCategoryList(x.getJsonEntity().getInvoiceCategoryList());
        merchantPositionVo.setChargeRuleDesc(x.getChargeRuleDesc());
        merchantPositionVo.setServiceDesc(x.getServiceDesc());
        merchantPositionVo.setWorkCategoryCode(x.getWorkCategoryCode());
        merchantPositionVo.setWorkCategoryName(x.getWorkCategoryName());
        merchantPositionVo.setWorkplaceCode(x.getWorkplaceCode());
        merchantPositionVo.setCreateTime(x.getCreateTime());
        merchantPositionVo.setMchNo(x.getMchNo());
        merchantPositionVo.setUpdateTime(x.getUpdateTime());
        merchantPositionVo.setUpdator(x.getUpdator());
        merchantPositionVo.setVersion(x.getVersion());
        merchantPositionVo.setParentPermissionFlag(x.getParentPermissionFlag());
    }

    private List<Long> getSubSalerIds(PmsOperator operator) {
        List<Long> salerIds = new ArrayList<>();
        try{
            List<PmsOperator> salers = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
            salerIds = salers.stream().map(PmsOperator::getId).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            salerIds.add(operator.getId());
        }
        return salerIds;
    }

    @GetMapping("listActiveMerchant")
    public RestResult<List<MerchantInfoVo>> listActiveMerchant(@CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        // 销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("departmentId", operator.getDepartmentId());
                paramMap.put("salerIds", salerIds);
            }
        }

        final List<MerchantInfoVo> merchantInfoVos = merchantQueryFacade.listAll(paramMap);
        return RestResult.success(merchantInfoVos);
    }

    @GetMapping("listBranchMerchant")
    public RestResult listBranchMerchant(@CurrentUser PmsOperator operator){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        // 销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("departmentId", operator.getDepartmentId());
                paramMap.put("salerIds", salerIds);
            }
        }

        List<BranchVo> merchantInfoVos = merchantQueryFacade.listByBranch(paramMap);
        return RestResult.success(merchantInfoVos);
    }

    @RequestMapping("listPosition")
    public RestResult listPosition(@RequestParam String mchNo) {
        // 岗位信息
        List<MerchantEmployerPosition> positionList = positionFacade.listByMchNo(mchNo);
        // 获取所有绑定的发票类目信息
        List<InvoiceCategoryVo> invoiceCategoryVoList = new ArrayList<>();
        List<MerchantPositionVo> positionVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(positionList)) {
            positionList.forEach(x -> {
                invoiceCategoryVoList.addAll(x.getJsonEntity().getInvoiceCategoryList());
                MerchantPositionVo merchantPositionVo = new MerchantPositionVo();
                buildMerchantPositionVo(merchantPositionVo,x);
                positionVoList.add(merchantPositionVo);
            });
        }
        return RestResult.success(positionList);
    }

    private List<Map<String, Object>> listAllMerchants(Map<String, Object> paramMap) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        PageParam pageParam = PageParam.newInstance(1, 200);
        int currPageSize;
        do {
            PageResult<List<Merchant>> pageResult = merchantQueryFacade.listPage(paramMap, pageParam);
            List<Merchant> merchantList = pageResult.getData();
            currPageSize = merchantList != null ? merchantList.size() : 0;
            if (currPageSize == 0) {
                break;
            }
            for (Merchant mch : merchantList) {
                Map<String, Object> map = new HashMap<>();
                map.put("mchNo", mch.getMchNo());
                map.put("mchName", mch.getMchName());
                map.put("merchantType", mch.getMerchantType());
                resultList.add(map);
            }
            pageParam.setPageCurrent(pageParam.getPageCurrent() + 1);
        } while (currPageSize == pageParam.getPageSize());
        return resultList;
    }
}
