package com.zhixianghui.web.pms.controller.trade;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.dto.CmbIncomeRecordParamDTO;
import com.zhixianghui.facade.trade.dto.IdParamDTO;
import com.zhixianghui.facade.trade.entity.WxIncomeRecord;
import com.zhixianghui.facade.trade.enums.WxIncomeRecordEnum;
import com.zhixianghui.facade.trade.service.CmbIncomeRecordFacade;
import com.zhixianghui.facade.trade.service.WxIncomeRecordFacade;
import com.zhixianghui.facade.trade.vo.CmbIncomeAuditVO;
import com.zhixianghui.facade.trade.vo.CmbIncomeRecordVO;
import com.zhixianghui.facade.trade.vo.MerchantInfoVO;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.trade.IncomeRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/cmbIncomeRecord")
public class CmbIncomeRecordController {

    @Reference
    private CmbIncomeRecordFacade cmbIncomeRecordFacade;

    @PostMapping("/listPage")
    public RestResult<IPage<CmbIncomeRecordVO>> listPage(@RequestBody CmbIncomeRecordParamDTO param){
        return RestResult.success(cmbIncomeRecordFacade.listPage(param));
    }

    @PostMapping("/listMerchantInfo")
    public RestResult<List<MerchantInfoVO>> listMerchantInfo(@RequestBody IdParamDTO param){
        return RestResult.success(cmbIncomeRecordFacade.listMerchantInfo(param));
    }

    @Logger(type = OperateLogTypeEnum.MODIFY,name = "人工审核招行来账通知")
    @PostMapping("/audit")
    public RestResult<String> audit(@RequestBody @Valid CmbIncomeAuditVO param, @CurrentUser PmsOperator pmsOperator){
        param.setUpdator(pmsOperator.getRealName());
        return cmbIncomeRecordFacade.auditCmbIncomeRecord(param);
    }

    @PostMapping("exportCmbIncomeRecord")
    //@Permission("recharge:list:view")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "招行来账记录-导出")
    public RestResult<String> exportCmbIncomeRecord(@RequestBody CmbIncomeRecordParamDTO param, @CurrentUser PmsOperator operator) {
        Date finishStartTime = param.getFinishStartTime();
        Date finishEndTime = param.getFinishEndTime();
        if (ObjectUtil.isAllNotEmpty(finishStartTime, finishEndTime)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("请提供起止完成时间");
        }
        if(DateUtil.compare(finishStartTime, finishEndTime) > 0){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间的截止时间不能大于起始时间");
        }
        cmbIncomeRecordFacade.exportCmbIncomeRecord(param, operator.getLoginName());
        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
