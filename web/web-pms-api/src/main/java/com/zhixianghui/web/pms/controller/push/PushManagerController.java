package com.zhixianghui.web.pms.controller.push;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.dto.PushManagerDto;
import com.zhixianghui.facade.common.entity.push.PushManager;
import com.zhixianghui.facade.common.service.PushManagerFacade;
import com.zhixianghui.facade.common.vo.PushManagerVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.PageVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PushManagerController
 * @Description TODO
 * @Date 2023/2/21 16:03
 */
@RestController
@RequestMapping("pushManager")
public class PushManagerController {

    @Reference
    private PushManagerFacade pushManagerFacade;

    @PostMapping("listPage")
    public RestResult listPage(@RequestBody PushManager pushManager, @RequestBody PageVo pageVo){
        PageParam pageParam = pageVo.toPageParam();
        PageResult<List<PushManagerVo>> pageList = pushManagerFacade.selectPage(pushManager,pageParam.getPageSize(),pageParam.getPageCurrent());
        return RestResult.success(pageList);
    }

    @PostMapping("add")
    public RestResult add(@Valid @RequestBody PushManagerDto pushManagerDto, @CurrentUser PmsOperator pmsOperator){
        pushManagerFacade.add(pushManagerDto,pmsOperator.getRealName());
        return RestResult.success("任务添加成功");
    }

    @PostMapping("delete/{id}")
    public RestResult delete(@PathVariable Long id){
        pushManagerFacade.deleteById(id);
        return RestResult.success("任务删除成功");
    }

    @PostMapping("update")
    public RestResult update(@RequestBody PushManager pushManager,@CurrentUser PmsOperator pmsOperator){
        pushManagerFacade.update(pushManager,pmsOperator.getRealName());
        return RestResult.success("任务更新成功");
    }
}
