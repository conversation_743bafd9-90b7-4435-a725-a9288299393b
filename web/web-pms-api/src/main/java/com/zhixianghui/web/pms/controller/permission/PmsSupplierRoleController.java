package com.zhixianghui.web.pms.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.service.supplier.SupplierRoleFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffRoleFacade;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierRoleVo;
import com.zhixianghui.web.pms.vo.permission.PmsRoleAssignFunctionVO;
import com.zhixianghui.web.pms.vo.permission.PmsRoleEditVO;
import com.zhixianghui.web.pms.vo.permission.PmsRoleQueryVO;
import com.zhixianghui.web.pms.vo.permission.PmsRoleVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色管理
 */
@RestController
@RequestMapping("pmsSupplierRole")
@Log4j2
public class PmsSupplierRoleController {

    @Reference
    private SupplierRoleFacade supplierRoleFacade;
    @Reference
    private SupplierStaffRoleFacade supplierStaffRoleFacade;

    /**
     * 分页查询角色
     */
    @Permission("pms:role:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<SupplierRoleVo>>> listPage(@RequestBody PmsRoleQueryVO vo) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", vo.getRoleName());
        PageResult<List<SupplierRoleVo>> result = supplierRoleFacade.listPage(map, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));

        return RestResult.success(result);
    }

    /**
     * 新增角色
     */
    @Permission("pms:role:add")
    @PostMapping("add")
    public RestResult<String> add(@Validated @RequestBody PmsRoleVO roleVO) {
        SupplierRole role = roleVO.buildSupplierRole(roleVO);
        supplierRoleFacade.create(role);
        return RestResult.success("新增角色成功");
    }

    /**
     * 更新角色
     */
    @Permission("pms:role:edit")
    @PostMapping("edit")
    public RestResult<String> edit(@Validated @RequestBody PmsRoleEditVO vo) {
        String mchNo =  String.valueOf(RoleTypeEnum.SUPPLIER_EMPLOYER_NO.getType());
        SupplierRole role = supplierRoleFacade.getById(mchNo, vo.getId());
        if (role.getRoleType() != RoleTypeEnum.PRESET.getType()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前角色不允许编辑");
        }
        role.setRemark(vo.getRemark() == null ? "" : vo.getRemark());
        supplierRoleFacade.update(role);
        return RestResult.success("更新角色成功");
    }

    /**
     * 删除角色
     */
    @Permission("pms:role:delete")
    @PostMapping("delete")
    public RestResult<String> delete(@RequestParam Long id) {
        String mchNo =  String.valueOf(RoleTypeEnum.SUPPLIER_EMPLOYER_NO.getType());
        supplierRoleFacade.deleteById(mchNo, id);
        return RestResult.success("删除角色成功");
    }

    /**
     * 查询角色关联的菜单
     * @param roleId    角色id
     */
    @Permission("pms:role:view")
    @GetMapping("listRoleFunction")
    public RestResult<List<FunctionVO>> listRoleFunction(@RequestParam Long roleId) {
        String mchNo =  String.valueOf(RoleTypeEnum.SUPPLIER_EMPLOYER_NO.getType());

        return RestResult.success(supplierRoleFacade.listFunctionByRoleId(mchNo, roleId)
                .stream().map(FunctionVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 为角色分配功能
     */
    @Permission("pms:role:edit")
    @PostMapping("assignFunction")
    public RestResult<String> assignFunction(@RequestBody @Valid PmsRoleAssignFunctionVO vo) {
        String mchNo =  String.valueOf(RoleTypeEnum.SUPPLIER_EMPLOYER_NO.getType());
        supplierRoleFacade.updateFunction(mchNo, vo.getRoleId(),
                vo.getFunctionIds() == null ? new ArrayList<>() : vo.getFunctionIds());
        return RestResult.success("操作成功");
    }
}
