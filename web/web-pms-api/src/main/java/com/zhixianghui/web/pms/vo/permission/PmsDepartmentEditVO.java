package com.zhixianghui.web.pms.vo.permission;

import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class PmsDepartmentEditVO {

    /**
     * 部门id
     */
    @NotNull(message = "部门id不能为空")
    private Long id;

    /**
     * 部门名称
     */
    @NotEmpty(message = "部门名称不能为空")
    @Size(min = 2, max = 50, message = "部门名称长度必须为2-50")
    private String departmentName;

    /**
     * 序号
     */
    @NotEmpty(message = "部门编号不能为空")
    @Size(min = 1, max = 50, message = "部门编号长度必须为1-50")
    private String number;

    /**
     * 部门负责人id
     */
    private Long leaderId;

    public static PmsDepartment buildDto(PmsDepartmentEditVO vo) {
        PmsDepartment department = new PmsDepartment();
        department.setId(vo.getId());
        department.setDepartmentName(vo.getDepartmentName());
        department.setNumber(vo.getNumber());
        department.setLeaderId(vo.getLeaderId());
        return department;
    }
}
