package com.zhixianghui.web.pms.vo.agent.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AgentBaseInfoVo {

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentName;

    /**
     * 主体类型
     */
    private Integer agentType;

    /**
     * 合伙人状态
     */
    private Integer agentStatus;

    /**
     * 邀请人编号
     */
    private String inviterNo;

    /**
     * 邀请人名称
     */
    private String inviterName;

    /**
     * 负责销售
     */
    private Long salerId;
    private String salerName;

    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 备注
     */
    private String remark;


    /**
     * 代扣税比例
     */
    private BigDecimal withholdingTaxRatio;

    /**
     * 是否自行申报, 1是,0否
     */
    private Integer selfDeclared;

}
