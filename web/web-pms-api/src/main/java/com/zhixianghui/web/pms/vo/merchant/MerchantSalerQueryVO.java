package com.zhixianghui.web.pms.vo.merchant;

import lombok.Data;

import java.io.Serializable;


/**
 * 销售商户查询
 * <AUTHOR>
 * @date 2020-08-18
 */
@Data
public class MerchantSalerQueryVO implements Serializable {
    private static final long serialVersionUID = 7919155227046962924L;

    private Integer pageCurrent;

    private Integer pageSize;

    /**
     * 商户编号
     */
    private String mchNo;
    /**
     * 商户全称
     */
    private String mchNameLike;

    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 商户类型
     */
    private Integer merchantType;

    /**
     * 所属部门id
     */
    private Long departmentId;
    /**
     * 销售id
     */
    private Long salerId;

    /**
     * 商户状态
     */
    private Integer mchStatus;

    /**
     * 认证状态
     */
    private Integer authStatus;

    /**
     * 创建时间-开始
     */
    private String createTimeBegin;
    /**
     * 创建时间-结束
     */
    private String createTimeEnd;

    private String branchName;

    private String remark;

    private String quoteMainstayNo;
}
