package com.zhixianghui.web.pms.vo.agent.req;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/3 18:13
 **/
@Data
public class AgentQueryVo {
    /**
     * 合伙人编号
     */
    private String agentNo;

    /**
     * 合伙人名称
     */
    private String agentNameLike;

    /**
     * 合伙人类型
     */
    private Integer agentType;

    /**
     * 邀请人编号
     */
    private String inviterNo;

    /**
     * 邀请人名称
     */
    private String inviterNameLike;

    /**
     * 销售
     */
    private Long salerId;

    /**
     * 合伙人状态
     */
    private Integer agentStatus;

    /**
     * 不查询的合伙人状态
     */
    private List<Integer> ignoreAgentStatusList;

    /**
     * 创建起始时间
     */
    private Date createBeginTime;

    /**
     * 创建截止时间
     */
    private Date createEndTime;
}
