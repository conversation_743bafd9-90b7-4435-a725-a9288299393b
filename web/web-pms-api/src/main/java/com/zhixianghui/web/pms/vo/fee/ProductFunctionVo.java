package com.zhixianghui.web.pms.vo.fee;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class ProductFunctionVo {

    /**
     *
     */
    private Long id;

    /**
     * 产品功能供应商编号
     */
    @NotEmpty(message = "产品编号不能为空")
    private String productNo;

    /**
     * 产品功能名称
     */
    @NotEmpty(message = "产品功能名称不能为空")
    private String productName;

    /**
     * 类型:0 产品 1 功能
     */
    @NotNull(message = "产品类型不能为空")
    private Integer productType;

    /**
     * 描述
     */
    private String description;

    private Integer status;
}
