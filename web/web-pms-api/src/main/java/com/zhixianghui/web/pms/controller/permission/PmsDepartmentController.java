package com.zhixianghui.web.pms.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.permission.PmsDepartmentEditVO;
import com.zhixianghui.web.pms.vo.permission.PmsDepartmentVO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门管理
 *
 * <AUTHOR> <PERSON>uangsheng
 */
@RestController
@RequestMapping("pmsDepartment")
public class PmsDepartmentController extends BaseController {

    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;

    /**
     * 查询所有部门信息
     */
    @GetMapping("listPmsDepartment")
    public RestResult<List<PmsDepartmentVO>> listPmsDepartment() {
        List<PmsDepartment> departmentList = pmsDepartmentFacade.listDepartment();
        return RestResult.success(departmentList.stream().map(PmsDepartmentVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 查询单个部门信息
     * @param departmentId  部门id
     */
    @Permission("pms:department:view")
    @GetMapping("getPmsDepartmentById")
    public RestResult<PmsDepartmentVO> getPmsDepartmentById(@RequestParam long departmentId) {
        PmsDepartment department = pmsDepartmentFacade.getDepartmentById(departmentId);
        if (department == null) {
            return RestResult.error("部门不存在");
        }
        return RestResult.success(PmsDepartmentVO.buildVo(department));
    }

    /**
     * 查询未设置负责人的部门
     */
    @Permission("pms:department:view")
    @GetMapping("listWithoutLeader")
    public RestResult<List<PmsDepartmentVO>> listWithoutLeader() {
        List<PmsDepartment> departmentList = pmsDepartmentFacade.listWithoutLeader(null);
        return RestResult.success(departmentList.stream().map(PmsDepartmentVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 新建部门
     */
    @Permission("pms:department:add")
    @PostMapping("createDepartment")
    public RestResult<String> createDepartment(@RequestBody @Valid PmsDepartmentVO vo) {
        PmsDepartment department = PmsDepartmentVO.buildDto(vo);
        department.setCreateTime(new Date());
        pmsDepartmentFacade.createDepartment(department);
        super.logSave("新建部门[" + vo.getDepartmentName() + "]", true);
        return RestResult.success("新建部门成功");
    }

    /**
     * 编辑部门
     */
    @Permission("pms:department:edit")
    @RequestMapping("editDepartment")
    public RestResult<String> editDepartment(@RequestBody @Valid PmsDepartmentEditVO vo) {
        pmsDepartmentFacade.updateDepartment(PmsDepartmentEditVO.buildDto(vo));
        super.logEdit("编辑部门[" + vo.getDepartmentName() + "]", true);
        return RestResult.success("编辑部门成功");
    }

    /**
     * 删除部门
     * @param departmentId      部门id
     */
    @Permission("pms:department:delete")
    @RequestMapping("deleteDepartment")
    public RestResult<String> deleteDepartment(@RequestParam long departmentId) {
        pmsDepartmentFacade.deleteDepartmentById(departmentId);
        super.logDelete("删除部门[id：" + departmentId + "]", true);
        return RestResult.success("删除部门成功");
    }

    /**
     * 为部门配置负责人
     * @param departmentId      部门id
     * @param leaderId          负责人id
     */
    @Permission("pms:department:edit")
    @RequestMapping("assignLeader")
    public RestResult<String> assignLeader(@RequestParam long departmentId,
                                           @RequestParam long leaderId) {
        pmsDepartmentFacade.assignLeader(departmentId, leaderId);
        super.logEdit("部门配置负责人[id：" + departmentId + "]", true);
        return RestResult.success("部门配置负责人成功");
    }
}
