package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.fee.entity.MerchantProductRelation;
import com.zhixianghui.facade.fee.service.MerchantProductFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.fee.*;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

@RestController
@RequestMapping("mch_prod_manager")
public class MerchantProductRelationController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(MerchantProductRelationController.class);

    @Reference
    private MerchantProductFacade facade;

    @Permission("fee:mchProdManager:list")
    @PostMapping("list")
    public RestResult<PageResult<List<MerchantProductRelation>>> listMerchantFeeRelation(@Valid @RequestBody MerchantProductRelationQueryVo vo) {
        PageResult<List<MerchantProductRelation>> pageResult = facade.listPage(BeanUtil.toMap(vo), PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize(),"UPDATE_TIME DESC"));
        return RestResult.success(pageResult);
    }

    @Permission("fee:mchProdManager:delete")
    @PostMapping("delete/{id}")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.DELETE, name = "删除数据")
    public RestResult<Map> delete(@CurrentUser PmsOperator currentOperator, @PathVariable long id) {
        facade.delete(id, currentOperator.getLoginName());

        Map responseMap = new HashMap();
        responseMap.put("msg", "删除操作成功");
        return RestResult.success(responseMap);

    }

    @Permission("fee:mchProdManager:edit")
    @PostMapping("edit")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.MODIFY, name = "修改数据")
    public RestResult<Map> edit(@CurrentUser PmsOperator currentOperator, @Valid @RequestBody MerchantProductRelationVo vo) {
        LimitUtil.notEmpty(vo.getId(), "id不能为空");
        MerchantProductRelation relation = facade.getById(vo.getId());

        relation.setMchNo(vo.getMchNo());
        relation.setMchName(vo.getMchName());
        relation.setProductNo(vo.getProductNo());
        relation.setProductName(vo.getProductName());
        relation.setDescription(vo.getDescription());
        relation.setUpdateBy(currentOperator.getLoginName());
        relation.setUpdateTime(new Date());

        facade.update(relation);

        Map responseMap = new HashMap();
        responseMap.put("msg", "修改操作成功");
        return RestResult.success(responseMap);
    }

    @Permission("fee:mchProdManager:add")
    @PostMapping("add")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.CREATE, name = "修改数据")
    public RestResult<Map> add(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid MerchantProductAddVo merchantProductAddVo) {

        if (merchantProductAddVo == null) {
            RestResult restResult = RestResult.error("参数不能为空");
            restResult.setCode(CommonExceptions.BIZ_INVALID.getSysErrorCode());
            return restResult;
        }

        List<SimpleMerchant> simpleMerchants = merchantProductAddVo.getMerchants();
        List<SimpleProduct> simpleProducts = merchantProductAddVo.getProducts();


        List<MerchantProductRelation> merchantProductRelations = new ArrayList<>();
        for (SimpleMerchant simpleMerchant : simpleMerchants) {

            for (SimpleProduct simpleProduct : simpleProducts) {
                MerchantProductRelation relation = new MerchantProductRelation();
                relation.setMchNo(simpleMerchant.getMchNo());
                relation.setMchName(simpleMerchant.getMchName());
                relation.setProductNo(simpleProduct.getProductNo());
                relation.setProductName(simpleProduct.getProductName());
                relation.setStatus(PublicStatus.ACTIVE);
                relation.setRemoved(RemovedEnum.NORMAL.getValue());
                relation.setUpdateBy(currentOperator.getLoginName());
                relation.setUpdateTime(new Date());
                relation.setCreateBy(currentOperator.getLoginName());
                relation.setCreateTime(new Date());
                merchantProductRelations.add(relation);
            }
        }

        facade.insert(merchantProductRelations);

        Map responseMap = new HashMap();
        responseMap.put("msg", "新增操作成功");
        return RestResult.success(responseMap);
    }

    /**
     * 更新状态
     * @param id
     * @param status
     * @param currentOperator
     * @return
     */
    @Permission("fee:mchProdManager:updateStatus")
    @GetMapping("updateStatus")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.MODIFY, name = "修改状态")
    public RestResult<String> updateStatus(@RequestParam Long id,
                                           @RequestParam Integer status,
                                           @CurrentUser PmsOperator currentOperator) {
        LimitUtil.notEmpty(id, "id不能为空");
        LimitUtil.notEmpty(status, "状态码不能为空");

        MerchantProductRelation relation = facade.getById(id);
        LimitUtil.notEmpty(relation, "产品开通不存在");

        relation.setStatus(status);
        relation.setUpdateBy(currentOperator.getLoginName());
        relation.setUpdateTime(new Date());
        facade.update(relation);
        return RestResult.success("添加成功");
    }


}
