package com.zhixianghui.web.pms.vo.report.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 查询Vo
 * @date 2020-10-21 09:27
 **/
@Data
public class ReportChannelRecordQueryVo {
    /**
     * 供应商编号
     */
    private String mainstayNo;

    /**
     * 供应商名称
     */
    private String mainstayName;

    /**
     * 供应商名称 like
     */
    private String mainstayNameLike;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 用工企业名称 like
     */
    private String employerNameLike;

    /**
     * 日志流水号(搭配各通道唯一标识)
     */
    private String serialNo;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 报备状态(详细看通道信息)
     */
    private Integer status;

    /**
     * 查询的起始日期
     */
    private String beginDate;

    /**
     * 查询的截止日期
     */
    private String endDate;
}
