package com.zhixianghui.web.pms.biz;

import com.zhixianghui.common.statics.enums.invoice.InvoicePreStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.account.invoice.entity.AccountInvoice;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceManageFacade;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;
import com.zhixianghui.facade.trade.service.InvoicePreFacade;
import com.zhixianghui.web.pms.vo.invoice.InvoicePreRecordQueryVo;
import com.zhixianghui.web.pms.vo.invoice.InvoicePreRecordVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceBiz {

    @Reference
    private InvoicePreFacade invoicePreFacade;
    @Reference
    private AccountInvoiceManageFacade accountInvoiceManageFacade;

    /**
     * 分页查询预开发票记录
     *
     * @param vo 查询参数
     * @return 分页结果
     */
    public PageResult<List<InvoicePreRecord>> listPreInvoice(InvoicePreRecordQueryVo vo) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        return invoicePreFacade.listPage(paramMap, vo.getPageParam());
    }

    /**
     * 创建预开发票记录
     *
     * @param vo 预开发票记录
     */
    public void createPreInvoice(InvoicePreRecordVo vo) throws BizException {
        InvoicePreRecord record = cn.hutool.core.bean.BeanUtil.copyProperties(vo, InvoicePreRecord.class);

        // 将List<String>类型的invoiceFileUrl转换为JSON字符串
        if (vo.getInvoiceFileUrl() != null && !vo.getInvoiceFileUrl().isEmpty()) {
            String jsonFileUrls = JsonUtil.toString(vo.getInvoiceFileUrl());
            record.setInvoiceFileUrl(jsonFileUrls);
        }

        if (record.getInvoiceStatus() == null) {
            record.setInvoiceStatus(InvoicePreStatusEnum.UN_FINNISH.getValue());
        }

        // 校验是否存在相同条件且状态为"处理中"的预开票记录
        if (existProcessingPreInvoice(record)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("存在处理中的预开发票，请优先确认");
        }

        invoicePreFacade.createPreInvoice(record);
        Map<String, Object> map = new HashMap<>();
        map.put("employerMchNo", record.getEmployerMchNo());
        map.put("mainstayMchNo", record.getMainstayMchNo());
        AccountInvoice accountInvoice = accountInvoiceManageFacade.getAccountByMap(map);
        if (accountInvoice != null) {
            if (accountInvoice.getInvoicePreAmount() != null) {
                accountInvoice.setInvoicePreAmount(accountInvoice.getInvoicePreAmount().add(record.getInvoiceAmount()));
            } else {
                accountInvoice.setInvoicePreAmount(record.getInvoiceAmount());
            }
            // 更新预开票账户信息
            accountInvoice.setModifyTime(new Date());
            accountInvoiceManageFacade.updateAccount(accountInvoice);
            log.info("预开发票账户信息更新成功：accountNo={}, invoicePreAmount={}", accountInvoice.getAccountNo(), accountInvoice.getInvoicePreAmount());
        } else {
            log.warn("未找到对应的发票账户信息：employerMchNo={}, mainstayMchNo={}", record.getEmployerMchNo(), record.getMainstayMchNo());
        }
    }

    /**
     * 更新预开发票记录
     *
     * @param vo 预开发票记录
     */
    public void updatePreInvoice(InvoicePreRecordVo vo) throws BizException {
        if (vo.getId() == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("记录ID不能为空");
        }
        
        // 先查询数据库中的记录，获取version值
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("id", vo.getId());
        InvoicePreRecord existingRecord = invoicePreFacade.getByEmployerByMap(queryMap);
        if (existingRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未找到ID为" + vo.getId() + "的预开发票记录");
        }
        
        InvoicePreRecord record = cn.hutool.core.bean.BeanUtil.copyProperties(vo, InvoicePreRecord.class);
        
        // 设置版本号，用于乐观锁控制
        record.setVersion(existingRecord.getVersion());
        
        // 将List<String>类型的invoiceFileUrl转换为JSON字符串
        if (vo.getInvoiceFileUrl() != null && !vo.getInvoiceFileUrl().isEmpty()) {
            String jsonFileUrls = JsonUtil.toString(vo.getInvoiceFileUrl());
            record.setInvoiceFileUrl(jsonFileUrls);
        }
        
        invoicePreFacade.updatePreInvoice(record);
    }

    /**
     * 删除预开发票记录
     *
     * @param id 记录ID
     */
    public void deletePreInvoice(Long id) throws BizException {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        InvoicePreRecord record = invoicePreFacade.getByEmployerByMap(map);
        if (record != null) {
            if (record.getInvoiceStatus() == InvoicePreStatusEnum.FINNISH.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("已完成的预开发票不能删除");
            }
            if (record.getInvoiceStatus() == InvoicePreStatusEnum.PROCESSING.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("处理中的预开发票不能删除");
            }
            Map<String, Object> accountInvoicMap = new HashMap<>();
            accountInvoicMap.put("employerMchNo", record.getEmployerMchNo());
            accountInvoicMap.put("mainstayMchNo", record.getMainstayMchNo());
            AccountInvoice accountInvoice = accountInvoiceManageFacade.getAccountByMap(accountInvoicMap);
            if (accountInvoice != null && accountInvoice.getInvoicePreAmount() != null) {
                BigDecimal newAmount = accountInvoice.getInvoicePreAmount().subtract(record.getInvoiceAmount());
                accountInvoice.setInvoicePreAmount(newAmount);
                accountInvoice.setModifyTime(new Date());
                accountInvoiceManageFacade.updateAccount(accountInvoice);
            }
        }
        invoicePreFacade.deletePreInvoice(id);
    }

    /**
     * 检查是否存在相同条件且状态为"处理中"的预开票记录
     *
     * @param record 预开票记录
     * @return true-存在，false-不存在
     */
    private boolean existProcessingPreInvoice(InvoicePreRecord record) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerMchNo", record.getEmployerMchNo());
        paramMap.put("mainstayMchNo", record.getMainstayMchNo());
        paramMap.put("productNo", record.getProductNo());
        paramMap.put("workCategoryCode", record.getWorkCategoryCode());
        paramMap.put("invoiceStatus", InvoicePreStatusEnum.PROCESSING.getValue());

        // 使用专门的exist方法查询
        return invoicePreFacade.existRecord(paramMap);
    }
}
