package com.zhixianghui.web.pms.vo.merchant.employer;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * 修改负责人信息
 * <AUTHOR>
 */
@Data
public class ChangeLeaderVO {

    /**
     * 新负责人手机号
     */
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号不正确")
    private String newLeaderPhone;

    /**
     * 新负责人姓名
     */
    @NotEmpty(message = "新负责人姓名不能为空")
    private String newLeaderName;

    /**
     * 商户编号
     */
    @NotEmpty(message = "商户编号不能为空")
    private String mchNo;
}
