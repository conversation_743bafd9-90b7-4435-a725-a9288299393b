package com.zhixianghui.web.pms.vo.config;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 工作类目vo
 * <AUTHOR>
 * @date 2020/8/13
 **/
@Data
public class WorkCategoryInsertVo {
    /**
     * 工作类目编码
     */
    @NotEmpty(message = "岗位类目编码不能为空")
    @Length(max = 20, message = "岗位类目编码长度不能超过20")
    private String workCategoryCode;

    /**
     * 工作类目名称
     */
    @NotEmpty(message = "岗位类目名称不能为空")
    @Length(max = 50, message = "岗位类目名称长度不能超过50")
    private String workCategoryName;

    /**
     * 父级服务类目编码
     */
    @NotNull(message = "父级岗位类目编码不能为空")
    private Long parentId;


    /**
     * 工作岗位描述
     */
    @NotEmpty(message = "岗位描述不能为空")
    @Length(max = 300, message = "岗位描述长度不能超过300")
    private String workDesc;

    /**
     * 企业从事业务
     */
    @Length(max = 300, message = "企业从事业务长度不能超过300")
    private String businessDesc;

    /**
     * 所得计算描述
     */
    @NotEmpty(message = "所得计算描述不能为空")
    @Length(max = 300, message = "所得计算描述长度不能超过300")
    private String chargeRuleDesc;

    @NotEmpty(message = "发票类目信息不能为空")
    @Valid
    private List<InvoiceCategoryVo> invoiceCategoryList;
}
