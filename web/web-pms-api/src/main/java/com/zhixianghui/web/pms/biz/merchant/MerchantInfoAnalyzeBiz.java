package com.zhixianghui.web.pms.biz.merchant;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.service.MerchantSalerFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.MerchantStat;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.FreelanceStatFacade;
import com.zhixianghui.facade.trade.service.MerchantStatFacade;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;
import com.zhixianghui.facade.trade.vo.MerchantStatVo;
import com.zhixianghui.facade.trade.vo.SignRecordVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import com.zhixianghui.web.pms.controller.flow.CommonFlow;
import com.zhixianghui.web.pms.vo.merchant.DataAnalyzeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Method;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2021/7/27 14:37
 */
@Slf4j
@Service
public class MerchantInfoAnalyzeBiz extends CommonFlow {

    @Reference(timeout = 30000, methods = {@Method(name = "freelanceStat2", timeout = 30000)})
    private FreelanceStatFacade freelanceStat;
    @Reference
    private MerchantStatFacade merchantStat;
    @Reference
    private SignRecordFacade signRecordFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private MerchantSalerFacade merchantSalerFacade;
    @Reference
    private PmsPermissionFacade pmsPermissionFacade;

    @Autowired
    public FastdfsClient fastdfsClient;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private RedisLock redisLock;

    public PageResult<List<FreelanceStatVo>> freelanceStat(DataAnalyzeVo analyzeVo, String sortColumn) {
        log.info("自由职业者列表统计查询参数 : {}", JSONObject.toJSON(analyzeVo));
        if (StringUtils.isNotBlank(analyzeVo.getIdCard())) {
            analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
            analyzeVo.setIdCard(null);
        }
        if (StringUtils.isNotBlank(analyzeVo.getReceiveName())) {
            analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
            analyzeVo.setReceiveName(null);
        }
        List<FreelanceStatVo> result = new ArrayList<>();
        Map<String, Object> param = BeanUtil.toMap(analyzeVo);
        if (StringUtils.isNotBlank(sortColumn)) {
            param.put("sortColumns", sortColumn);
        }
        PageResult<List<Map<String, Object>>> list = freelanceStat.freelanceStat2(param, PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()));
        if (list == null || CollectionUtils.isEmpty(list.getData())) {
            return PageResult.newInstance(PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()), result);
        }
        for (Map<String, Object> stat : list.getData()) {
            FreelanceStatVo statVo = new FreelanceStatVo();
            BeanUtil.mapToObject(statVo,stat);
            if (stat.containsKey("receiveIdCardNo")) {
                statVo.setReceiveIdCardNo(AESUtil.decryptECB(String.valueOf(stat.get("receiveIdCardNo")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (stat.containsKey("receiveName")) {
                statVo.setReceiveName(AESUtil.decryptECB(String.valueOf(stat.get("receiveName")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (stat.containsKey("receivePhoneNo")) {
                statVo.setReceivePhoneNo(AESUtil.decryptECB(String.valueOf(stat.get("receivePhoneNo")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (StringUtils.isBlank(String.valueOf(stat.get("receivePhoneNo")))) {
                statVo.setPhone(OperationEnum.UN_PHONE.getOperation());
            } else {
                statVo.setPhone(OperationEnum.PHONE.getOperation());
            }
            if (Objects.isNull(stat.get("idCardBackUrl"))&&Objects.isNull(stat.get("idCardFrontUrl"))&&Objects.isNull(stat.get("idCardCopyUrl"))) {
                statVo.setIdCard(0);
            }else {
                statVo.setIdCard(1);
            }
            if (stat.get("signStatus")!=null && Integer.valueOf(String.valueOf(stat.get("signStatus")))==SignStatusEnum.SIGN_SUCCESS.getValue()){
                statVo.setSignRecord(1);
            }

            result.add(statVo);
        }
        return PageResult.newInstance(result, PageParam.newInstance(list.getPageCurrent(), list.getPageSize()), list.getTotalRecord());
    }

    /**
     * 销售部员工增加条件判断
     * @param operator 操作员
     * @param paramMap 参数
     */
    private List<Long> putIfSale(PmsOperator operator, Map<String, Object> paramMap) {
        // 销售部只能查自己及下属的商户
        if (operator.getDepartmentId() == null) {
           return null;
        }
        PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
        if (Objects.isNull(department) || !pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
            return null;
        }
        // 获取下属销售
        List<Long> saleIds = new ArrayList<>();
        try{
            List<PmsOperator> sale = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
            saleIds = sale.stream().map(PmsOperator::getId).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            saleIds.add(operator.getId());
        }
        paramMap.put("saleIds", saleIds);
        return saleIds;
    }

    public PageResult<List<MerchantStatVo>> merchantStat(DataAnalyzeVo analyzeVo, String sortColumn, PmsOperator operator) {
        List<MerchantStatVo> result = new ArrayList<>();
        Map<String, Object> param = BeanUtil.toMap(analyzeVo);
        if (StringUtils.isNotBlank(sortColumn)) {
            param.put("sortColumns", sortColumn);
        }
        if (PmsOperatorTypeEnum.ADMIN.getValue() != operator.getType()) {
            putIfSale(operator, param);
        }
        PageResult<List<MerchantStat>> list = merchantStat.merchantStat(param, PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()));
        if (list == null || CollectionUtils.isEmpty(list.getData())) {
            return PageResult.newInstance(PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()), result);
        }
        for (MerchantStat stat : list.getData()) {
            MerchantStatVo statVo = new MerchantStatVo();
            BeanUtil.copyProperties(stat, statVo);
            result.add(statVo);
        }
        return PageResult.newInstance(result, PageParam.newInstance(list.getPageCurrent(), list.getPageSize()), list.getTotalRecord());
    }

    public FreelanceStatVo countFreelance(DataAnalyzeVo analyzeVo) {
        if (StringUtils.isNotBlank(analyzeVo.getReceiveName())) {
            analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
        }
        if (StringUtils.isNotBlank(analyzeVo.getIdCard())) {
            analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
        }
        log.info("自由者职业者统计查询参数 : {}", JSONObject.toJSON(analyzeVo));
        return freelanceStat.count(BeanUtil.toMap(analyzeVo));
    }

    public MerchantStatVo countMerchant(DataAnalyzeVo analyzeVo, PmsOperator operator) {
        log.info("用工企业统计查询参数 : {}", JSONObject.toJSON(analyzeVo));

        Map<String, Object> param = BeanUtil.toMap(analyzeVo);
        if (PmsOperatorTypeEnum.ADMIN.getValue() == operator.getType()) {
            MerchantStatVo vo = merchantStat.count(param);
            Integer freelanceCount = freelanceStat.freelanceCount(param);
            vo.setFreelanceCount(freelanceCount + "");
            return vo;
        }
        List<Long> saleIds =  putIfSale(operator, param);
        log.info("请求参数 : {}-{}", JSONObject.toJSON(param), JSONObject.toJSONString(saleIds));
        if (CollectionUtils.isEmpty(saleIds)) {
            MerchantStatVo vo = merchantStat.count(param);
            Integer freelanceCount = freelanceStat.freelanceCount(param);
            vo.setFreelanceCount(freelanceCount + "");
            return vo;
        }

        MerchantStatVo vo = merchantStat.count(param);
//        List<MerchantStatVo> result = merchantStat.groupBySaleId(param);
//        if (CollectionUtils.isEmpty(result)) {
//            vo.setFreelanceCount(0);
//            return vo;
//        }
//        Integer freelanceCount = 0;
//        for (MerchantStatVo merchantStatVo : result) {
//            param.put("employerNo", merchantStatVo.getEmployerNo());
//            param.put("mainstayNo", merchantStatVo.getMainstayNo());
//            freelanceCount += freelanceStat.freelanceCount(param);
//        }
        vo.setFreelanceCount("......");
        return vo;
    }

    public boolean sign(DataAnalyzeVo analyzeVo) {
        Map<String, Object> param = buildParam(analyzeVo);

        SignRecord signRecord = signRecordFacade.getOne(param);
        if (signRecord!=null && signRecord.getSmsSendTime() != null &&
                DateUtil.subtractDays(new Date(), signRecord.getSmsSendTime()) <= 1) {
            if (signRecord.getSmsSendFrequency() != null && signRecord.getSmsSendFrequency() >= 3) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发送短信24h小时内不能超过3次");
            }
        }

        int page = 1;
        PageResult<List<FreelanceStat>> pageData;
        List<FreelanceStat> list = new ArrayList<>();
        do {
            pageData = freelanceStat.listPage(param, PageParam.newInstance(page, 10));
            if (pageData != null && pageData.getData() != null && !pageData.getData().isEmpty()) {
                list.addAll(pageData.getData());
            }
            page++;
        } while (pageData != null && pageData.getData() != null && !pageData.getData().isEmpty());
        if (list.isEmpty()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到记录...");
        }

        String clientId = null;
        try {
            String lockKey = String.join("-", analyzeVo.getReceiveNameMd5(), analyzeVo.getReceiveIdCardNoMd5(), analyzeVo.getMainstayNo(), analyzeVo.getEmployerNo());
            clientId = redisLock.tryLockLong(lockKey, 0, 1);
            if (clientId == null) {
                log.warn("[{}]重复请求", lockKey);
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("你已成功发起签约, 请不要重复提交");
            }

            boolean flag = false;
            for (FreelanceStat freelanceStat : list) {
                if (!Objects.isNull(signRecord)
                        && !Objects.isNull(signRecord.getSignStatus())
                        && signRecord.getSignStatus().intValue() == SignStatusEnum.SIGN_SUCCESS.getValue()) {
                    for (FreelanceStat stat : list) {
                        stat.setSignId(signRecord.getId());
                        stat.setSignRecord(OperationEnum.SIGN.getOperation());
                        this.freelanceStat.update(stat);
                    }

                    return true;
                }

                if ((analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.MSG.getValue()
                        || analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.URL_CODE.getValue())
                        && StringUtils.isAllBlank(freelanceStat.getReceivePhoneNo(), analyzeVo.getPhone())) {
                    continue;
                }
                    flag = true;
                    SignRecord record = new SignRecordVo().build(freelanceStat);
                    record.setSignType(analyzeVo.getSignType());

//                if (analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.MSG.getValue() || analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.URL_CODE.getValue()) {
                    if (StringUtils.isNotBlank(analyzeVo.getPhone())) {
                        record.setReceivePhoneNoEncrypt(analyzeVo.getPhone());
                    } else {
                        record.setReceivePhoneNoEncrypt(
                                AESUtil.decryptECB(freelanceStat.getReceivePhoneNo(), EncryptKeys.getEncryptKeyById(freelanceStat.getEncryptKeyId()).getEncryptKeyStr())
                        );
                    }
//                }

                    log.info("待签约信息: " + JSONObject.toJSONString(record));
                    signRecordFacade.startSign(record, true);
//                    signRecordFacade.startSign(record, record.getSignType() != ChannelSignTypeEnum.STAMP_SILENCE.getValue());
                    signRecord = signRecordFacade.getOne(param);
                    if (signRecord == null) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约异常");
                    }
                    updateFreelanceStat(list, signRecord.getId());
                    break;
            }

            if (!flag) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请提供手机号进行签约");
            }else {
                return true;
            }
        } finally {
            if (clientId != null) {
                redisLock.unlockLong(clientId);
            }
        }
    }

    private void updateFreelanceStat(List<FreelanceStat> data, Long id) {
        for (FreelanceStat stat : data) {
            stat.setSignId(id);
        }
        freelanceStat.update(data);
    }

    public boolean uploadIdCard(DataAnalyzeVo analyzeVo) {
        FreelanceStat stat = freelanceStat.getById(analyzeVo.getId());
        log.info("上传身份证, 记录 : {}", JSONObject.toJSON(stat));

        /**
        SignRecord signRecord = null;
        if (stat.getSignId() == null || (signRecord = signRecordFacade.getById(stat.getSignId())) == null
                || (signRecord.getSignStatus().intValue() != SignStatusEnum.WAIT_SIGN.getValue()
                && signRecord.getSignStatus().intValue() != SignStatusEnum.SIGN_SUCCESS.getValue())) {
            SignRecord record = new SignRecordVo().build(stat);
            log.info("待签约信息: " + JSONObject.toJSONString(record));
            if (StringUtils.isNotBlank(analyzeVo.getPhone())) {
                record.setReceivePhoneNoEncrypt(analyzeVo.getPhone());
            }else {
                record.setReceivePhoneNoEncrypt(
                        AESUtil.decryptECB(stat.getReceivePhoneNo(), EncryptKeys.getEncryptKeyById(stat.getEncryptKeyId()).getEncryptKeyStr())
                );
            }
            signRecordFacade.startSign(record, false);
            signRecord = signRecordFacade.getOne(new HashMap<String, Object>() {
                private static final long serialVersionUID = 8029339019056839795L;

                {
                put("receiveNameMd5", stat.getReceiveNameMd5());
                put("receiveIdCardNoMd5", stat.getReceiveIdCardNoMd5());
                put("mainstayNo", stat.getMainstayNo());
                put("employerNo", stat.getEmployerNo());
            }});
        }
        if (signRecord == null) {
            log.error("自由职业者签约id : {}", stat.getSignId());
            return false;
        }
        signRecord.setIdCardBackUrl(analyzeVo.getIdCardBackUrl());
        signRecord.setIdCardFrontUrl(analyzeVo.getIdCardFrontUrl());
        signRecord.setCerFaceUrl(analyzeVo.getCerFaceUrl());
        signRecord.setReceiveAccountNoEncrypt(analyzeVo.getBankCardNumber());
        signRecord.setIdCardType(analyzeVo.getIdCardType());
        signRecord.setIdCardCopyUrl(analyzeVo.getIdCardCopyFileUrl());
        if (StringUtils.isNotBlank(analyzeVo.getPhone())){
            signRecord.setReceivePhoneNoEncrypt(analyzeVo.getPhone());
        }

        signRecordFacade.update(signRecord);**/
        UserInfo userInfo = new UserInfo();
        userInfo.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        userInfo.setReceiveNameEncrypt(
                AESUtil.decryptECB(stat.getReceiveName(), EncryptKeys.getEncryptKeyById(stat.getEncryptKeyId()).getEncryptKeyStr())
        );
        userInfo.setReceiveIdCardNoMd5(stat.getReceiveIdCardNoMd5());
        userInfo.setReceiveIdCardNoEncrypt(
                AESUtil.decryptECB(stat.getReceiveIdCardNo(), EncryptKeys.getEncryptKeyById(stat.getEncryptKeyId()).getEncryptKeyStr())
        );
        userInfo.setCerFaceUrl(analyzeVo.getCerFaceUrl());
        userInfo.setIdCardCopyUrl(analyzeVo.getIdCardCopyFileUrl());
        userInfo.setIdCardFrontUrl(analyzeVo.getIdCardFrontUrl());
        userInfo.setIdCardBackUrl(analyzeVo.getIdCardBackUrl());
        userInfo.setCreateTime(new Date());
        userInfo.setUpdateTime(new Date());
        signRecordFacade.addUserImage(userInfo);

        stat.setIdCard(OperationEnum.UPLOAD_ID_CARD.getOperation());
    /**    if (stat.getSignId() == null) {
            stat.setSignId(signRecord.getId());
        }
     **/
        freelanceStat.update(stat);
        return true;
    }

    public Map<String, Object> buildParam(DataAnalyzeVo analyzeVo) {
        analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
        analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
        return new HashMap<String, Object>() {
            private static final long serialVersionUID = 2785284015406866482L;

            {
            put("receiveNameMd5", analyzeVo.getReceiveNameMd5());
            put("receiveIdCardNoMd5", analyzeVo.getReceiveIdCardNoMd5());
            put("mainstayNo", analyzeVo.getMainstayNo());
            put("employerNo", analyzeVo.getEmployerNo());
        }};
    }

    public boolean merchantInfoExport(DataAnalyzeVo analyzeVo, PmsOperator operator) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(analyzeVo.getOperator().getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.MERCHANT_STAT_INFO_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.MERCHANT_STAT_INFO_EXPORT.getValue());
        Map<String, Object> paramMap = BeanUtil.toMap(analyzeVo);

        if (equalRole(operator.getId(), pmsPermissionFacade, SALE_FLAG)) {
            putIfSale(operator, paramMap);
        }
        record.setParamJson(JsonUtil.toString(paramMap));
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_STAT_INFO_EXPORT.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return true;
    }

    private List<String> getEmployerList(Long saleId) {
        List<MerchantSaler> list = merchantSalerFacade.getBatchMerchantSale(Arrays.asList(saleId));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(MerchantSaler :: getMchNo).collect(Collectors.toList());
    }

    public boolean freelanceExport(DataAnalyzeVo analyzeVo, PmsOperator operator) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(analyzeVo.getOperator().getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.FREELANCE_STAT_Export.getFileName());
        record.setReportType(ReportTypeEnum.FREELANCE_STAT_Export.getValue());
        if (StringUtils.isNotBlank(analyzeVo.getIdCard())) {
            analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
            analyzeVo.setIdCard(null);
        }
        if (StringUtils.isNotBlank(analyzeVo.getReceiveName())) {
            analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
            analyzeVo.setReceiveName(null);
        }
        Map<String, Object> paramMap = BeanUtil.toMap(analyzeVo);
//        if (equalRole(operator.getId(), pmsPermissionFacade, SALE_FLAG)) {
//            paramMap.put("employerList", getEmployerList(operator.getId()));
//        }
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.FREELANCE_STAT_Export.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return true;
    }


}
