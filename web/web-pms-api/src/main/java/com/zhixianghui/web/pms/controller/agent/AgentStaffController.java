package com.zhixianghui.web.pms.controller.agent;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.web.pms.vo.agent.req.AgentStaffQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 合伙人员工
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("agentStaff")
@Slf4j
public class AgentStaffController {

    @Reference
    private AgentStaffFacade agentStaffFacade;

    /**
     * 分页查询员工
     */
    @Permission("agent:staff:view")
    @RequestMapping("listPage")
    public RestResult<PageResult<List<AgentStaffVO>>> listPage(@RequestBody AgentStaffQueryVO vo) {
        PageResult<List<AgentStaffVO>> result = agentStaffFacade.listPage(BeanUtil.toMap(vo),
                PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(result);
    }
}
