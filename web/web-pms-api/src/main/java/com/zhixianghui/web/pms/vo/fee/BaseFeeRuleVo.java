package com.zhixianghui.web.pms.vo.fee;

import com.zhixianghui.common.util.validator.EnumValue;
import com.zhixianghui.web.pms.vo.common.SpecialFeeRuleVo;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/11
 **/
@Data
public class BaseFeeRuleVo {
    /**
     * 计费规则类型
     */
    @NotNull(message = "计费规则类型不能为空")
    @EnumValue(intValues = {0}, message = "请选择正确的计费规则类型")
    private Integer chargeType;

    /**
     * 计费公式类型
     */
    @NotNull(message = "计费公式类型不能为空")
    @EnumValue(intValues = {0,1,2}, message = "请选择正确的计费公式类型")
    private Integer formulaType;

    /**
     * 规则类型
     */
    @NotNull(message = "规则类型不能为空")
    @EnumValue(intValues = {0,1}, message = "请选择正确的规则类型")
    private Integer ruleType;

    /**
     * 固定手续费
     */
    @DecimalMin(value = "0", message = "固定手续费不能小于0")
    @DecimalMax(value = "99999999.99", message = "固定手续费不能大于99999999.99")
    private java.math.BigDecimal fixedFee;

    /**
     * 费率
     */
    @DecimalMin(value = "0", message = "费率不能小于0")
    @DecimalMax(value = "100", message = "费率不能大于100")
    private java.math.BigDecimal feeRate;

    /**
     * 最大手续费
     */
    @NotNull
    @DecimalMin(value = "0", message = "最大手续费不能小于0")
    @DecimalMax(value = "99999999.99", message = "最大手续费不能大于99999999.99")
    private java.math.BigDecimal maxFee;

    /**
     * 最小手续费
     */
    @NotNull
    @DecimalMin(value = "0", message = "最小手续费不能小于0")
    @DecimalMax(value = "99999999.99", message = "最小手续费不能大于99999999.99")
    private java.math.BigDecimal minFee;

    /**
     * 优先级
     */
    @NotNull(message = "优先级不能为空")
    //@Max(value = 5, message = "优先级不能大于5")
    private Integer priority;

    /**
     * 特殊计费规则参数
     * 数据库字段，请通过 specialFeeRuleList 来操作修改相应值
     */
    @Valid
    private List<SpecialFeeRuleVo> ruleParam = new ArrayList<>();
}
