package com.zhixianghui.web.pms.controller.flow;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.flow.service.FlowImageFacade;
import com.zhixianghui.facade.flow.service.ProcessDefinitionFacade;
import com.zhixianghui.facade.flow.vo.req.ProcessDefinitionReqVo;
import com.zhixianghui.facade.flow.vo.res.ProcessDefinitionResVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.PageVo;
import org.apache.dubbo.config.annotation.Reference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ProcessDefinitionController
 * @Description 流程定义
 * @Date 2021/4/29 16:12
 */
@RestController
@RequestMapping("procDef")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class ProcessDefinitionController {

    @Reference
    private FlowImageFacade flowImageFacade;

    @Reference
    private ProcessDefinitionFacade processDefinitionFacade;

    /**
     * 根据流程定义id获取流程图
     *
     * @param id 流程定义id
     * @return
     * @throws IOException
     */
    @Permission("pms:procDef:image")
    @GetMapping("/getDefinitionImage")
    public RestResult getDefinitionImage(@RequestParam(name = "id") String id) throws IOException {
        byte[] bytes = flowImageFacade.getDefinitionImage(id);
        return RestResult.success(Base64.getEncoder().encodeToString(bytes));
    }

    /**
     * 部署流程
     *
     * @param file     流程文件，xml
     * @param name     流程名称
     * @param tenantId 租户
     * @return
     * @throws IOException
     */
    @Permission("pms:procDef:ctrl")
    @PostMapping("/deployByUpload")
    public RestResult deployByUpload(@RequestParam(name = "file",required = false) MultipartFile file,
                                     @RequestParam(name = "dataObj") String dataObj,
                                     @RequestParam(name = "triggerAct") String triggerAct,
                                     @RequestParam(name = "name") String name,
                                     @RequestParam(name = "platform") String platform,
                                     @RequestParam(name = "tenantId", required = false) String tenantId,
                                     @RequestParam(name = "description", required = false) String description, @CurrentUser PmsOperator pmsOperator) throws IOException {
        if (file == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("流程文件不能为空");
        }
        processDefinitionFacade.deploy(name, platform, dataObj, triggerAct, file.getBytes(), file.getOriginalFilename(), tenantId, pmsOperator.getRealName(), description);
        return RestResult.success("部署成功");
    }

    /**
     * 分页查询定义列表
     *
     * @param processDefinitionReqVo
     * @param pageVo
     * @return
     */
    @Permission("pms:procDef:ctrl")
    @PostMapping("listProcDef")
    public RestResult<PageResult<List<ProcessDefinitionResVo>>> listProcDef(@RequestBody ProcessDefinitionReqVo processDefinitionReqVo, @RequestBody PageVo pageVo) {
        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(processDefinitionReqVo);
        return RestResult.success(processDefinitionFacade.listProcessDefinition(paramMap, pageVo.toPageParam()));
    }

    /**
     * 根据流程定义id获取详情
     *
     * @param id
     * @return
     */
    @Permission("pms:procDef:ctrl")
    @GetMapping("getById")
    public RestResult<ProcessDefinitionResVo> getById(@RequestParam(name = "id") String id) {
        return RestResult.success(processDefinitionFacade.getById(id));
    }

    /**
     * @param dataObj
     * @param triggerAct
     * @param name
     * @param platform
     * @param desc
     * @param tenantId
     * @return
     */
    @Permission("pms:procDef:ctrl")
    @PostMapping("update")
    public RestResult update(
            @RequestParam(name = "id") String id,
            @RequestParam(name = "dataObj") String dataObj,
            @RequestParam(name = "triggerAct") String triggerAct,
            @RequestParam(name = "name") String name,
            @RequestParam(name = "platform") String platform,
            @RequestParam(name = "description", required = false) String desc,
            @RequestParam(name = "tenantId", required = false) String tenantId, @CurrentUser PmsOperator pmsOperator) {
        processDefinitionFacade.update(id, dataObj, triggerAct, name, platform, desc, tenantId, pmsOperator.getRealName());
        return RestResult.success("流程定义信息修改成功");
    }

    /**
     * 删除流程
     *
     * @param id 流程定义id
     * @return
     */
    @Permission("pms:procDef:ctrl")
    @PostMapping("deleteProcDef")
    public RestResult deleteProcDef(@RequestParam("id") String id) {
        processDefinitionFacade.delete(id);
        return RestResult.success("删除流程成功");
    }

    /**
     * 激活/挂起流程
     *
     * @param id    流程定义id
     * @param state 操作状态
     * @return
     */
    @Permission("pms:procDef:ctrl")
    @PostMapping("updateState")
    public RestResult updateState(@RequestParam("id") String id, @RequestParam("state") Integer state) {
        processDefinitionFacade.updateProcessDefinitionState(id, state);
        return RestResult.success("状态更新完毕");
    }
}
