package com.zhixianghui.web.pms.controller.notify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.notify.entity.RmqMessageRecord;
import com.zhixianghui.facade.notify.service.RmqMessageFacade;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.notify.RmqMessageQueryVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 消息通知Controller
 * @date 2020-09-24 17:16
 **/
@RestController
@RequestMapping("notify")
public class NotifyController {

    @Reference
    private RmqMessageFacade rmqMessageFacade;

    @Permission("notify:msg:view")
    @GetMapping("listRmqMessageRecord")
    public RestResult<PageResult<List<RmqMessageRecord>>> listRmqMessageRecord(RmqMessageQueryVo rmqMessageQueryVo, PageVo pageVo){
        Map<String,Object> params = BeanToMapUtil.beanToMap(rmqMessageQueryVo);
        PageResult<List<RmqMessageRecord>> result = rmqMessageFacade.listRmqMessagePage(params, pageVo.toPageParam());
        return RestResult.success(result);
    }

    @Logger(type = OperateLogTypeEnum.MODIFY, name = "批量补发通知")
    @Permission("notify:msg:batchReissueMessage")
    @PostMapping("batchReissueMessage")
    public RestResult<String> batchReissueMessage(@RequestBody String idListStr){
        JSONObject json = JSON.parseObject(idListStr);
        JSONArray idArray = json.getJSONArray("idList");
        if(idArray == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数idList为空");
        }
        List<Long> idList = idArray.toJavaList(Long.class);
        rmqMessageFacade.batchReissueMessage(idList);
        return RestResult.success("消息补发成功");
    }

    @Logger(type = OperateLogTypeEnum.MODIFY, name = "按条件批量补发通知")
    @Permission("notify:msg:batchReissueMessage")
    @PostMapping("batchReissueMessageNew")
    public RestResult<String> batchReissueMessage(@RequestBody RmqMessageQueryVo rmqMessageQueryVo){
        Map<String,Object> params = BeanToMapUtil.beanToMap(rmqMessageQueryVo);
        rmqMessageFacade.batchReissueMessage(params);
        return RestResult.success("消息补发成功");
    }
}
