package com.zhixianghui.web.pms.utils;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.web.pms.vo.permission.PmsFunctionVO;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;

public class MultipartFileUtil {
	
	private static final Logger log = LoggerFactory.getLogger(MultipartFileUtil.class);

	/**
	 * 限制上传文件大小，单位：M
	 */
	private static final int FILE_SIZE = 5;
	private static final String UNIT = "M";
	private static final String SUFFIX = ".xlsx";

	public static void check(PmsFunctionVO vo) {
		if (vo == null || vo.getFile() == null) {
			throw CommonExceptions.PARAM_INVALID.newWithErrMsg("上传的文件为空...");
		}
		// 检查上传文件
		MultipartFile file = vo.getFile();
		if(!MultipartFileUtil.checkFileSize(file.getSize(), FILE_SIZE, UNIT)) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件大小超过限制");
		}
		if (StringUtils.isBlank(file.getOriginalFilename())) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件名为空");
		}
		if(!file.getOriginalFilename().endsWith(SUFFIX)) {
			throw CommonExceptions.BIZ_INVALID.newWithErrMsg("FileName:" + file.getOriginalFilename() +"文件格式有误，必须为xlsx格式");
		}
	}
	
	/**
     * 判断文件大小
     *
     * @param len
     *            文件长度
     * @param size
     *            限制大小
     * @param unit
     *            限制单位（B,K,M,G）
     * @return 小于返回true 大于返回false
     */
    public static boolean checkFileSize(Long len, int size, String unit) {
        double fileSize = 0;
		switch (unit.toUpperCase()) {
			case "B":
				fileSize = (double) len;
				break;
			case "K":
				fileSize = (double) len / 1024;
				break;
			case "M":
				fileSize = (double) len / 1048576;
				break;
			case "G":
				fileSize = (double) len / 1073741824;
				break;
			default:
				log.error("找不到对应单位");
		}
		return fileSize < size;
	}

}
