package com.zhixianghui.web.pms.vo.permission;

import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class PmsFunctionVO {

    /**
     * id
     */
    private Long id;

    /**
     * 上传的文件
     */
    private MultipartFile file;

    /**
     * 名称
     */
    @NotEmpty(message = "功能名称不能为空")
    @Size(min = 2, max = 30, message = "功能名称长度限定2到30")
    private String name;

    /**
     * 编号
     */
    @NotEmpty(message = "编号不能为空")
    @Size(min = 2, max = 20, message = "编号长度限定2到20")
    private String number;

    /**
     * 父节点id
     */
    private Long parentId;

    /**
     * 权限标识
     */
    @NotEmpty(message = "权限标识不能为空")
    @Size(min = 2, max = 50, message = "权限标识长度限定2到20")
    private String permissionFlag;

    /**
     * 功能类型
     */
    @NotNull(message = "功能类型不能为空")
    private Integer functionType;

    /**
     * 后端API地址
     */
    private String url;

    /**
     * 用于刷新页面的配置
     */
    private String targetName;

    public static PmsFunction buildDto(PmsFunctionVO vo) {
        PmsFunction pmsFunction = new PmsFunction();
        pmsFunction.setId(vo.getId());
        pmsFunction.setName(vo.getName());
        pmsFunction.setNumber(vo.getNumber());
        pmsFunction.setParentId(vo.getParentId());
        pmsFunction.setPermissionFlag(vo.getPermissionFlag());
        pmsFunction.setFunctionType(vo.getFunctionType());
        pmsFunction.setUrl(vo.getUrl());
        pmsFunction.setTargetName(vo.getTargetName());
        return pmsFunction;
    }

    public static PmsFunctionVO buildVo(PmsFunction pmsFunction) {
        PmsFunctionVO vo = new PmsFunctionVO();
        vo.setId(pmsFunction.getId());
        vo.setName(pmsFunction.getName());
        vo.setNumber(pmsFunction.getNumber());
        vo.setParentId(pmsFunction.getParentId());
        vo.setPermissionFlag(pmsFunction.getPermissionFlag());
        vo.setFunctionType(pmsFunction.getFunctionType());
        vo.setUrl(pmsFunction.getUrl());
        vo.setTargetName(pmsFunction.getTargetName());
        return vo;
    }
}
