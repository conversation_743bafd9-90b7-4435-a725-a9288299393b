package com.zhixianghui.web.pms.biz.merchant;

import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.web.pms.biz.CommonBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PmsBiz extends CommonBiz {

    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private PmsPermissionFacade pmsPermissionFacade;


    // 判断当前用户是否为销售部门
    public boolean isSaleDepartment (PmsOperator operator) {
        if (operator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前的操作员为null");
        }
        PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
        if (Objects.isNull(department)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("销售部门未创建");
        }
        if (pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
            return true;
        }
        return false;
    }


    // 获取销售id
    public List<Long> getSaleId(PmsOperator operator) {
        List<Long> saleArrayList = new ArrayList<>();
        try{
            List<PmsOperator> pmsOperatorList = pmsOperatorFacade.getSale(operator.getId());
            saleArrayList = pmsOperatorList.stream().map(PmsOperator::getId).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            saleArrayList.add(operator.getId());
        }
        return saleArrayList;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <V> void saveFunction(List<V> list) {
        pmsPermissionFacade.saveFunction((List<PmsFunction>) list);
    }
}
