package com.zhixianghui.web.pms.vo.report;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-29 11:09
 **/
@Data
public class MainstayChannelRelationVo {
    /**
     * 代征主体编号
     */
    @NotEmpty(message = "代征主体编号不为空")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @NotEmpty(message = "代征主体名称不为空")
    private String mainstayName;

    /**
     * 支付通道信息
     */
    @Valid
    private List<ParentChannelInfoVo> channelInfos;

}
