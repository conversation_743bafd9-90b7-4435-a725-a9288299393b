package com.zhixianghui.web.pms.controller.config;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.tenant.TenantManage;
import com.zhixianghui.facade.common.service.TenantManageFacade;
import com.zhixianghui.facade.common.vo.TenantVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.PageVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName TenantController
 * @Description TODO
 * @Date 2023/3/28 9:20
 */
@RestController
@RequestMapping("tenant")
public class TenantController {

    @Reference
    private TenantManageFacade tenantManageFacade;

    @PostMapping("add")
    public RestResult add(@Validated @RequestBody TenantVo tenantVo, @CurrentUser PmsOperator pmsOperator){
        tenantManageFacade.add(tenantVo,pmsOperator.getRealName());
        return RestResult.success("租户添加成功");
    }

    @PostMapping("delete/{id}")
    public RestResult delete(@PathVariable Long id){
        tenantManageFacade.delete(id);
        return RestResult.success("租户删除成功");
    }

    @PostMapping("update")
    public RestResult update(@Validated @RequestBody TenantVo tenantVo,@CurrentUser PmsOperator pmsOperator){
        tenantManageFacade.update(tenantVo,pmsOperator.getRealName());
        return RestResult.success("租户信息更新成功");
    }

    @PostMapping("updateStatus")
    public RestResult update(@RequestBody TenantManage tenantManage,@CurrentUser PmsOperator pmsOperator){
        if (tenantManage.getId() == null || tenantManage.getStatus() == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数校验失败");
        }
        tenantManageFacade.updateStatus(tenantManage.getId(),tenantManage.getStatus());
        return RestResult.success("状态修改成功");
    }

    @PostMapping("listPage")
    public RestResult listPage(@RequestBody TenantManage tenantManage, @RequestBody PageVo pageVo){
        PageParam pageParam = pageVo.toPageParam();
        PageResult<List<TenantVo>> pageList = tenantManageFacade.selectPage(tenantManage,pageParam.getPageSize(),pageParam.getPageCurrent());
        return RestResult.success(pageList);
    }
}
