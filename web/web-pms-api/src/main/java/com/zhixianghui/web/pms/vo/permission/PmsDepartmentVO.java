package com.zhixianghui.web.pms.vo.permission;

import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
public class PmsDepartmentVO {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 部门名称
     */
    @NotEmpty(message = "部门名称不能为空")
    @Size(min = 2, max = 50, message = "部门名称长度必须为2-50")
    private String departmentName;

    /**
     * 序号
     */
    @NotEmpty(message = "部门编号不能为空")
    @Size(min = 1, max = 50, message = "部门编号长度必须为1-50")
    private String number;

    /**
     * 上级部门id
     */
    @NotNull(message = "上级部门不能为空")
    private Long parentId;

    /**
     * 上级部门名称
     */
    private String parentName;

    /**
     * 部门负责人id
     */
    private Long leaderId;

    /**
     * 部门负责人名称
     */
    private String leaderName;

    public static PmsDepartment buildDto(PmsDepartmentVO vo) {
        PmsDepartment department = new PmsDepartment();
        department.setId(vo.getId());
        department.setCreateTime(vo.getCreateTime());
        department.setDepartmentName(vo.getDepartmentName());
        department.setNumber(vo.getNumber());
        department.setParentId(vo.getParentId());
        department.setParentName(vo.getParentName());
        department.setLeaderId(vo.getLeaderId());
        department.setLeaderName(vo.getLeaderName());
        return department;
    }

    public static PmsDepartmentVO buildVo(PmsDepartment pmsDepartment) {
        PmsDepartmentVO vo = new PmsDepartmentVO();
        vo.setId(pmsDepartment.getId());
        vo.setCreateTime(pmsDepartment.getCreateTime());
        vo.setDepartmentName(pmsDepartment.getDepartmentName());
        vo.setNumber(pmsDepartment.getNumber());
        vo.setParentId(pmsDepartment.getParentId());
        vo.setParentName(pmsDepartment.getParentName());
        vo.setLeaderId(pmsDepartment.getLeaderId());
        vo.setLeaderName(pmsDepartment.getLeaderName());
        return vo;
    }
}
