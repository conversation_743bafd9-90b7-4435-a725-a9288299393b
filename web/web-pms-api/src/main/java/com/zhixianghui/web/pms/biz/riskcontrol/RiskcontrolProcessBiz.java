package com.zhixianghui.web.pms.biz.riskcontrol;

import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.dto.rmq.MsgDto;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.PendingOrderOpeEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.notify.service.NotifyOfNoPersistenceFacade;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolProcessDetail;
import com.zhixianghui.facade.riskcontrol.result.RiskControlResult;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.riskcontrol.vo.SettleRiskControlVo;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 风控处理
 * @author: xingguang li
 * @created: 2020/11/23 16:15
 */
@Service
public class RiskcontrolProcessBiz {

    @Reference
    private NotifyOfNoPersistenceFacade notifyOfNoPersistenceFacade;
    @Reference
    private RiskControlFacade riskControlFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private OfflineOrderItemFacade offlineOrderItemFacade;

    /**
     * 风控挂单处理
     * @param ids
     * @param ids
     * @param operation
     * @param operator
     */
    public void riskcontrolOperate(String ids, Integer operation, String operator) {

        List<Long> idList = Arrays.asList(ids.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        List<RiskcontrolProcessDetail> processDetails = riskControlFacade.getByIds(idList);
        if (CollectionUtils.isEmpty(processDetails)) {
            return;
        }

        //增加状态校验
        //只判断挂单状态的
        processDetails = processDetails.stream().filter(x-> x.getOperation().intValue() == PendingOrderOpeEnum.PENDING.getValue()).collect(Collectors.toList());

        List<RiskcontrolProcessDetail> processDetailsFinal = null;
        if (operation.intValue() == PendingOrderOpeEnum.PASS.getValue()) {
            //先要走一遍风控，没有被风控到才可以给到交易中心
            processDetailsFinal = goRiskControlAgain(processDetails);
        } else {
            processDetailsFinal = processDetails;
        }
        //拼装platTrxNo
        StringBuilder sb = new StringBuilder();
        List<String> platTrxNoList = new ArrayList<>();
        processDetailsFinal.stream().forEach(it->{
            sb.append(String.format("%s,",it.getPlatTrxNo()));
            platTrxNoList.add(it.getPlatTrxNo());
        });

        //更新明细的状态
        Map<String,Object> param = new HashMap<>();
        param.put("list", idList.size() == 0 ? null : idList);
        param.put("platTrxNoList", platTrxNoList.size() == 0 ? null : platTrxNoList);
        param.put("operation", operation);
        param.put("updateUser", operator);
        riskControlFacade.updateStatusByIdList(param);

        if (StringUtils.isEmpty(sb) || CollectionUtils.isEmpty(platTrxNoList)) {
            return;
        }

        String platTrxNos = sb.substring(0, sb.length() - 1);
        //发mq的方式通知到交易中心
        MsgDto<Map<String, Object>> msgDto = new MsgDto<>();
        msgDto.setTopic(MessageMsgDest.TOPIC_RISKCONTROL_NOTIFY_TRADE);
        msgDto.setTags(MessageMsgDest.TAG_RISKCONTROL_NOTIFY_TRADE);
        msgDto.setTrxNo(null);
        Map<String, Object> jsonParam = new HashMap<>();
        jsonParam.put("operation", operation);
        jsonParam.put("operatorName", operator);
        jsonParam.put("platTrxNos", platTrxNos);
        msgDto.setJsonParam(jsonParam);
        notifyOfNoPersistenceFacade.sendOne(msgDto);
    }

    private List<RiskcontrolProcessDetail> goRiskControlAgain(List<RiskcontrolProcessDetail> processDetails){
        List<RiskcontrolProcessDetail> processDetailsFinal = new ArrayList<>();
        for (RiskcontrolProcessDetail x : processDetails) {
            //旧数据没有atomgroupid和ruleid，因此这部分不需要走风控校验
            if (x.getAtomGroupId() != null && x.getAtomGroupId() > 0
                    && x.getObjectId() != null && x.getObjectId() > 0) {

                OrderItem orderItem;
                if (x.getPlatTrxNo().startsWith("OT")) {
                    orderItem = new OrderItem();
                    final OfflineOrderItem item = offlineOrderItemFacade.getOrderItemByPlatTrxNo(x.getPlatTrxNo());
                    BeanUtil.copyProperties(item, orderItem);
                }else {
                    orderItem = orderItemFacade.getByPlatTrxNo(x.getPlatTrxNo());
                }

                RiskcontrolOrderItem riskcontrolOrderItem = riskControlFacade.getRiskOrderItemByPlatTrxNo(orderItem.getPlatTrxNo());

                SettleRiskControlVo settleRiskControlVo = new SettleRiskControlVo();
                settleRiskControlVo.setPlatTrxNo(x.getPlatTrxNo());
                settleRiskControlVo.setEmployerNo(x.getEmployerNo());
                settleRiskControlVo.setOrderAmount(x.getOrderAmount());
                settleRiskControlVo.setOrderNo(x.getMchOrderNo());
                settleRiskControlVo.setSupplierNo(orderItem.getMainstayNo());
                settleRiskControlVo.setUserIdCard(orderItem.getReceiveIdCardNoDecrypt());
                settleRiskControlVo.setUserName(orderItem.getReceiveNameDecrypt());
                settleRiskControlVo.setReceiveAccount(orderItem.getReceiveAccountNoDecrypt());
                settleRiskControlVo.setPhone(orderItem.getReceivePhoneNoDecrypt());
                settleRiskControlVo.setObjectId(x.getObjectId());
                settleRiskControlVo.setObjectWeight(x.getObjectWeight());
                settleRiskControlVo.setAtomGroupId(x.getAtomGroupId());
                settleRiskControlVo.setAtomGroupWeight(x.getAtomGroupWeight());
                settleRiskControlVo.setPayRemark(orderItem.getRemark());
                settleRiskControlVo.setStartTradeTime(riskcontrolOrderItem.getStartTradeTime());
                RiskControlResult riskControlResult = riskControlFacade.processSettleInter(settleRiskControlVo,x.getControlType());
                if (!riskControlResult.getCode().equals(ControlAtomEnum.PASS.getValue())) {
                    //此时需要通知交易那边更新被风控的原因
                    MsgDto<Map<String, Object>> msgDtoAgain = new MsgDto<>();
                    msgDtoAgain.setTopic(MessageMsgDest.TOPIC_RISKCONTROL_NOTIFY_TRADE);
                    msgDtoAgain.setTags(MessageMsgDest.TAG_RISKCONTROL_NOTIFY_TRADE_AGAIN);
                    msgDtoAgain.setTrxNo(x.getPlatTrxNo());
                    Map<String, Object> jsonParam = new HashMap<>();
                    jsonParam.put("reason", riskControlResult.getErrMsg());
                    jsonParam.put("platTrxNo", x.getPlatTrxNo());
                    jsonParam.put("code",riskControlResult.getCode());
                    jsonParam.put("riskType",riskControlResult.getControlType());
                    msgDtoAgain.setJsonParam(jsonParam);
                    notifyOfNoPersistenceFacade.sendOne(msgDtoAgain);
                    continue;
                }
                processDetailsFinal.add(x);
            } else {
                processDetailsFinal.add(x);
            }
        }
        return processDetailsFinal;
    }
}
