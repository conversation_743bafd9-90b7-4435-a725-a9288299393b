package com.zhixianghui.web.pms.vo.report.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 汇聚报备回调Vo
 * @date 2020-10-14 16:58
 **/
@Data
public class JoinPayNotifyVo implements Serializable {

    private static final long serialVersionUID = -9172254052496143790L;

    @JsonProperty("resp_code")
    private String respCode;

    @JsonProperty(value = "resp_msg")
    private String respMsg;

    private String data;

    @JsonProperty(value = "rand_str")
    private String randStr;

    @JsonProperty(value = "sign_type")
    private String signType;

    @JsonProperty(value = "mch_no")
    private String mchNo;

    private String sign;

    @JsonProperty(value = "aes_key")
    private String aesKey;
}
