package com.zhixianghui.web.pms.vo.permission;

import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRole;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 角色VO
 *
 * <AUTHOR>
 * @date 2019/10/31
 */
@Data
public class PmsRoleVO {

    /**
     * id
     */
    private Long id;

    /**
     * 角色名称
     */
    @NotEmpty(message = "角色名称不能为空")
    @Size(min = 1, max = 90, message = "角色名称在3到90个字符")
    private String roleName;

    /**
     * 描述
     */
    @Size( max = 50, message = "描述长度不能超过50")
    private String remark;

    /**
     * 角色类型:0自定义,1预置
     */
    private Integer roleType;

    /**
     * 员工数量
     */
    private Long employerCount;

    @Override
    public String toString() {
        return "PmsRoleVO{" +
                "roleName='" + roleName + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }

    public static PmsRole buildDto(PmsRoleVO vo) {
        PmsRole pmsRole = new PmsRole();
        pmsRole.setId(vo.getId());
        pmsRole.setRoleName(vo.getRoleName());
        pmsRole.setRemark(vo.getRemark());
        pmsRole.setRoleType(RoleTypeEnum.CUSTOMIZE.getType());
        return pmsRole;
    }

    public static PmsRoleVO buildVo(PmsRole pmsRole) {
        PmsRoleVO vo = new PmsRoleVO();
        vo.setId(pmsRole.getId());
        vo.setRoleName(pmsRole.getRoleName());
        vo.setRemark(pmsRole.getRemark());
        vo.setRoleType(pmsRole.getRoleType());
        return vo;
    }

    public AgentRole buildAgentRole(PmsRoleVO roleVO) {
        AgentRole agentRole = new AgentRole();
        agentRole.setName(roleVO.getRoleName());
        agentRole.setRemark(roleVO.getRemark());
        agentRole.setRoleType(RoleTypeEnum.PRESET.getType());
        agentRole.setAgentNo(String.valueOf(RoleTypeEnum.PRESET_AGENT_NO.getType()));
        return agentRole;
    }

    public EmployerRole buildEmployerRole(PmsRoleVO roleVO) {
        EmployerRole employerRole = new EmployerRole();
        employerRole.setName(roleVO.getRoleName());
        employerRole.setRemark(roleVO.getRemark());
        employerRole.setRoleType(RoleTypeEnum.PRESET.getType());
        employerRole.setMchNo(String.valueOf(RoleTypeEnum.PRESET_EMPLOYER_NO.getType()));
        return employerRole;
    }

    public SupplierRole buildSupplierRole(PmsRoleVO roleVO) {
        SupplierRole supplierRole = new SupplierRole();
        supplierRole.setName(roleVO.getRoleName());
        supplierRole.setRemark(roleVO.getRemark());
        supplierRole.setRoleType(RoleTypeEnum.PRESET.getType());
        supplierRole.setMchNo(String.valueOf(RoleTypeEnum.SUPPLIER_EMPLOYER_NO.getType()));
        return supplierRole;
    }
}
