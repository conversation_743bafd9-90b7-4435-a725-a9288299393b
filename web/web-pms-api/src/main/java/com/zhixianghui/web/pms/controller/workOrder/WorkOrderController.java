package com.zhixianghui.web.pms.controller.workOrder;

import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.flow.entity.WorkOrderExt;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.enums.WorkTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.flow.vo.req.WorkOrderVo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerMain;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantEmployerMainFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.MerchantInfoVo;
import com.zhixianghui.web.employer.vo.order.res.MainstayResVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WorkOrderController
 * @Description TODO
 * @Date 2022/4/18 14:25
 */
@RestController
@RequestMapping("workOrder")
public class WorkOrderController {

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    @Reference
    private MerchantEmployerMainFacade merchantEmployerMainFacade;

    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;

    @Reference
    private SequenceFacade sequenceFacade;

    @Reference
    private FlowFacade flowFacade;

    @GetMapping("getWorkOrderMerchant")
    public RestResult getWorkOrderMerchant(@RequestParam String mchNo){
        //查询商户信息
        Map<String,Object> maps = merchantQueryFacade.getBaseInfo(mchNo);
        //查询主体信息
        MerchantEmployerMain employerMain = merchantEmployerMainFacade.getByMchNo(mchNo);
        if (employerMain != null){
            maps.put("managementAddrDetail",employerMain.getManagementAddrDetail());
            maps.put("managementAddrProvince",employerMain.getManagementAddrProvince());
            maps.put("managementAddrCity",employerMain.getManagementAddrCity());
            maps.put("managementAddrTown",employerMain.getManagementAddrTown());
        }

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",mchNo);
        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        List<EmployerMainstayRelation> list = employerMainstayRelationFacade.listBy(paramMap);
        List<MainstayResVo> mainstayList = list.stream().map(
                rel ->{
                    MainstayResVo mainstayResVo = new MainstayResVo();
                    BeanUtils.copyProperties(rel,mainstayResVo);
                    return mainstayResVo;
                }
        ).collect(Collectors.toList());

        maps.put("mainstayNos",mainstayList);
        return RestResult.success(maps);
    }

    @PostMapping("create")
    public RestResult create(@Validated @RequestBody WorkOrderVo workOrderVo, @CurrentUser PmsOperator pmsOperator){
        pmsOperator.setRealName(pmsOperator.getRealName());
        pmsOperator.setId(pmsOperator.getId());
        MerchantInfoVo merchantInfoVo = merchantQueryFacade.convert(workOrderVo.getMchNo());
        Merchant mainstay = null;
        if (StringUtils.isNotBlank(workOrderVo.getMainstayNo())){
             mainstay= merchantQueryFacade.getByMchNo(workOrderVo.getMainstayNo());
        }
        WorkOrderExt workOrderExt = buildWorkOrderExt(workOrderVo,merchantInfoVo,mainstay);
        //创建流程
        ProcessVo processVo = new ProcessVo();
        processVo.setWorkType(WorkTypeEnum.WORK_FLOW.getValue());
        processVo.setBusinessKey(workOrderExt.getWorkOrderNo());
        processVo.setExtInfo(workOrderVo.getFormJson());
        processVo.setFlowTopicType(FlowTypeEnum.COMMON_WORK_ORDER.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-", FlowTypeEnum.COMMON_WORK_ORDER.getDesc(),
                workOrderExt.getEmployerName()));
        //添加抄送人
        FlowUserVo carbonCopyUser = new FlowUserVo();
        carbonCopyUser.setUserId(merchantInfoVo.getSalerId());
        carbonCopyUser.setUserName(merchantInfoVo.getSalerName());
        carbonCopyUser.setPlatform(PlatformSource.OPERATION.getValue());
        processVo.getCarbonCopyList().add(carbonCopyUser);

        if (StringUtils.isNotBlank(workOrderExt.getMainstayName())){
            processVo.setFlowTopicName(processVo.getFlowTopicName() + "-" + workOrderExt.getMainstayName());
        }

        processVo.setProcessDefinitionKey(FlowTypeEnum.COMMON_WORK_ORDER.name());

        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(pmsOperator.getId());
        flowUserVo.setUserName(pmsOperator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());

        Map<String,Object> condition = new HashMap<>();
        //设置流程参数
        condition.put("sign",OpenOffEnum.OFF.getValue());
        condition.put("mchNo",workOrderExt.getEmployerNo());
        if (mainstay != null){
            condition.put("mainstayNo",workOrderExt.getMainstayNo());
        }
        //扩展字段
        processVo.setExtObj(workOrderExt);
        flowFacade.startProcessByProcessDefinitionKey(processVo,flowUserVo,null,condition);
        return RestResult.success("提交成功");
    }

    private WorkOrderExt buildWorkOrderExt(WorkOrderVo workOrderVo,MerchantInfoVo merchantInfoVo, Merchant mainstay) {
        WorkOrderExt workOrderExt = new WorkOrderExt();
        //生成工单编号
        String formNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.FORM_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.FORM_NO_SEQ.getKey(),SequenceBizKeyEnum.FORM_NO_SEQ.getWidth());
        workOrderExt.setWorkOrderNo(formNo)
                .setEmployerNo(merchantInfoVo.getMchNo())
                .setEmployerName(merchantInfoVo.getMchName())
                .setSalerId(merchantInfoVo.getSalerId())
                .setSalerName(merchantInfoVo.getSalerName())
                .setAgentNo(merchantInfoVo.getAgentNo())
                .setAgentName(merchantInfoVo.getAgentName())
                .setStartWorkTime(workOrderVo.getStartWorkTime())
                .setEndWorkTime(workOrderVo.getEndWorkTime())
                .setWorkRangeUrl(workOrderVo.getWorkRangeUrl())
                .setEndDate(workOrderVo.getEndDate())
                .setCreateDate(new Date());
        if (mainstay != null){
            workOrderExt.setMainstayNo(mainstay.getMchNo())
                    .setMainstayName(mainstay.getMchName());
        }
        return workOrderExt;
    }
}
