package com.zhixianghui.web.pms.biz.merchant;

import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.facade.merchant.service.supplier.SupplierFunctionFacade;
import com.zhixianghui.web.pms.biz.CommonBiz;

import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/4/6 20:51
 */
@Service
public class SupplierBiz extends CommonBiz {
    @Reference
    private SupplierFunctionFacade supplierFunctionFacade;

    @Override
    @SuppressWarnings("unchecked")
    public <V> void saveFunction(List<V> list) {
        supplierFunctionFacade.saveFunction((List<SupplierFunction>) list);
    }
}
