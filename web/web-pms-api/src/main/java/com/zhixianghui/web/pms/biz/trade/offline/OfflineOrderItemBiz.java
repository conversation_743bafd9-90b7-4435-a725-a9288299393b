package com.zhixianghui.web.pms.biz.trade.offline;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.OfflineWorkerBillPathUpdateDto;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OfflineOrderItemFail;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.trade.req.OrderItemQueryVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderItemPendingExtRespVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderItemResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 订单明细
 * @date 2020-11-04 15:13
 **/
@Service
@Slf4j
public class OfflineOrderItemBiz {

    @Reference
    private OfflineOrderItemFacade orderItemFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private RiskControlFacade riskControlFacade;
    @Reference
    private UserInfoFacade userInfoFacade;


    public PageResult<List<OrderItemResVo>> listOrderItemPage(OrderItemQueryVo orderItemQueryVo, PageVo pageVo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OfflineOrderItem>> pageResult = orderItemFacade.listPage(paramMap,pageParam);
        List<OrderItemResVo> orderItemResVoList = pageResult.getData().stream().map(
                orderItem ->{
                    OrderItemResVo orderItemResVo = new OrderItemResVo();
                    BeanUtils.copyProperties(orderItem,orderItemResVo);
                    orderItemResVo.setReceiveName(orderItem.getReceiveNameDesensitize());
                    orderItemResVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDesensitize());
                    orderItemResVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDesensitize());
                    orderItemResVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDesensitize());
                    orderItemResVo.setWorkerBillFilePath(orderItem.getWorkerBillFilePath());
                    return orderItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public Long countOrderItem(OrderItemQueryVo orderItemQueryVo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        return orderItemFacade.countOrderItem(paramMap);
    }

    public OrderItemSumBo sumOrderItem(OrderItemQueryVo orderItemQueryVo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        return orderItemFacade.sumOrderItem(paramMap);
    }

    public Long countItemByMchNo(String employerNo, String mchOrderNo) {
        return orderItemFacade.countOrderItemByMchOrderNo(employerNo, mchOrderNo);
    }

    public void saveFailItem(OfflineOrderItemFail orderItemFail) {
        orderItemFacade.saveFail(orderItemFail);
    }

    public List<OfflineOrderItemFail> getItemByPlatBatchNo(String platBatch) {
        return orderItemFacade.getByPlatBatchNo(platBatch);
    }

    public OfflineOrderItem uploadWorkerBillPath(OfflineWorkerBillPathUpdateDto dto) {
        return orderItemFacade.uploadWorkerBillPath(dto);
    }

    public OfflineOrderItem getItemByPlatTrxNo(String platTrxNo) {
        return orderItemFacade.getOrderItemByPlatTrxNo(platTrxNo);
    }

    public void deleteOrderItem(String platTrxNo) {
        orderItemFacade.deleteOrderItem(platTrxNo);
    }

    public RestResult<OrderItemPendingExtRespVo> getOrderItemByPlatTrxNo(String platTrxNo) {
        final OfflineOrderItem orderItem = this.getItemByPlatTrxNo(platTrxNo);

        if (Objects.isNull(orderItem) || StringUtils.isBlank(orderItem.getReceiveIdCardNoMd5())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("平台流水号为{}的订单详情不存在或者详情不存在身份证号");
        }

        OrderItemPendingExtRespVo respVo = new OrderItemPendingExtRespVo();
        BeanUtil.copyProperties(orderItem, respVo);
        if (StringUtils.isNotBlank(orderItem.getReceiveName())) {
            respVo.setReceiveName(orderItem.getReceiveNameDecrypt());
        }
        if (StringUtils.isNotBlank(orderItem.getReceiveIdCardNo())) {
            respVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDecrypt());
        }
        if (StringUtils.isNotBlank(orderItem.getReceiveAccountNo())) {
            respVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDecrypt());
        }
        if (StringUtils.isNotBlank(orderItem.getReceivePhoneNo())) {
            respVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDecrypt());
        }

        //查询用户信息
        UserInfo userInfo = userInfoFacade.getByIdCardNoMd5(orderItem.getReceiveIdCardNoMd5());
        if (userInfo != null){
            respVo.setIdCardBackUrl(userInfo.getIdCardBackUrl());
            respVo.setIdCardCopyUrl(userInfo.getIdCardCopyUrl());
            respVo.setIdCardFrontUrl(userInfo.getIdCardFrontUrl());
        }
        return RestResult.success(respVo);
    }
}
