package com.zhixianghui.web.pms.vo.common;

import com.zhixianghui.facade.common.entity.config.BankInfo;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @Date 2021/4/15 14:33
 */
@Data
public class BankInfoVo implements Serializable {

    private Long id;

    /**
     * 银行编号：银行简称，例如：工商银行(ICBC)
     */
    @NotNull(message = "银行编号不能为空,例如：工商银行(ICBC)")
    @Length(max = 14,message = "银行编号过长")
    private String bankCode;

    /**
     * 银行行号(联行号)
     */
    @NotNull(message = "银行行号(联行号)不能为空")
    @Length(max = 14,message = "联行号过长")
    private String bankChannelNo;

    /**
     * 银行名称
     */
    @NotNull(message = "银行名称不能为空")
    @Length(max = 100,message = "银行名称过长")
    private String bankName;

    /**
     * 银行开户行号
     */
    @Length(max = 14,message = "银行开户行号过长")
    private String openingBankNo;

    /**
     * 省份
     */
    @NotNull(message = "省份不能为空")
    @Length(max = 48,message = "省份过长")
    private String province;

    /**
     * 城市
     */
    @NotNull(message = "城市不能为空")
    @Length(max = 48,message = "城市过长")
    private String city;

    /**
     * 地区编号
     */
    @NotNull(message = "城市编码")
    @Max(value = ********,message = "城市编码过长")
    private Integer cityCode;

    public BankInfo toBankInfo(BankInfoVo bankInfoVo) {

        BankInfo bankInfo = new BankInfo();
        bankInfo.setBankChannelNo(bankInfoVo.getBankChannelNo());
        bankInfo.setBankCode(bankInfoVo.getBankCode());
        bankInfo.setBankName(bankInfoVo.getBankName());
        bankInfo.setOpeningBankNo(bankInfoVo.getOpeningBankNo());
        bankInfo.setProvince(bankInfoVo.getProvince());
        bankInfo.setCity(bankInfoVo.getCity());
        bankInfo.setCityCode(bankInfoVo.getCityCode());
        bankInfo.setId(bankInfoVo.getId());
        return bankInfo;
    }
}
