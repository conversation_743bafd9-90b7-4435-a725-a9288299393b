package com.zhixianghui.web.pms.vo.report.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-28 15:46
 **/
@Data
public class PayChannelReqVo {
    /**
     * ID
     */
    private Long id;

    /**
     * 通道编号
     */
    @NotEmpty(message="通道编号不能为空")
    private String payChannelNo;

    /**
     * 通道名称
     */
    @NotEmpty(message="通道名称不能为空")
    private String payChannelName;

//    /**
//     * 通道类型
//     */
//    @NotNull(message="通道类型不能为空")
//    private Integer channelType;

    @NotNull(message = "通道类型不能为空")
    private List<Integer> channelTypeList;

    /**
     * 状态
     */
    @NotNull(message="状态不能为空")
    private Integer status;

    /**
     * 描述
     */
    private String description;
}
