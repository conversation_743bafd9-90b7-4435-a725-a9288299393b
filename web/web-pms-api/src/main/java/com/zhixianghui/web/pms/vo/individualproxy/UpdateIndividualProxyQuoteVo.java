package com.zhixianghui.web.pms.vo.individualproxy;

import com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class UpdateIndividualProxyQuoteVo implements Serializable {
    private static final long serialVersionUID = -1569055566550949284L;
    @NotNull(message = "代征id不能为空")
    private Integer id;
    private IndividualProxyQuote individualProxyQuote;
}
