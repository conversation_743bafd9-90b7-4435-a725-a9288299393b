package com.zhixianghui.web.pms.vo.trade.res;

import com.zhixianghui.facade.trade.entity.BasePrivateItem;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 挂单订单明细返回Vo
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderItemPendingExtRespVo extends BasePrivateItem {

    private static final long serialVersionUID = -6440070752419970902L;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 发放模式
     */
    private Integer launchWay;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;


    /**
     * 收款账户(卡号/支付宝账号)
     */
    private String receiveAccountNo;


    /**
     * 银行预留手机号
     */
    private String receivePhoneNo;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    private String idCardBackUrl;

    private String idCardFrontUrl;

    private String idCardCopyUrl;
}
