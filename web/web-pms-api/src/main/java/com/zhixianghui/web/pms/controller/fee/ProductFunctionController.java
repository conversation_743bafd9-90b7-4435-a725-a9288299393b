package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.fee.entity.Product;
import com.zhixianghui.facade.fee.service.ProductFunctionFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.fee.ProductFunctionQueryVo;
import com.zhixianghui.web.pms.vo.fee.ProductFunctionVo;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

@RestController
@RequestMapping("product_function_manager")
public class ProductFunctionController extends BaseController {

    @Reference
    private ProductFunctionFacade facade;

    @Permission("fee:product:list")
    @PostMapping("list")
    public RestResult<PageResult<List<Product>>> listProductFunction(@RequestBody @Valid ProductFunctionQueryVo vo) {
        PageResult<List<Product>> pageResult = facade.getProductFunction(BeanUtil.toMap(vo), PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize(),"UPDATE_TIME DESC"));
        return RestResult.success(pageResult);
    }

    @Permission("fee:product:list")
    @GetMapping("listAll")
    public RestResult<List<Product>> listProductFunction() {
        List<Product> pageResult = facade.getAllProductFunction();
        return RestResult.success(pageResult);
    }

    @Permission("fee:product:delete")
    @PostMapping("delete/{id}")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.DELETE, name = "删除数据")
    public RestResult<String> delete(@CurrentUser PmsOperator currentOperator, @PathVariable long id) {
        facade.deleteById(id, currentOperator.getLoginName());

        return RestResult.success("删除成功");

    }

    @Permission("fee:product:edit")
    @PostMapping("edit")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.MODIFY, name = "修改数据")
    public RestResult<String> edit(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid ProductFunctionVo productFunctionVo) {
        LimitUtil.notEmpty(productFunctionVo.getId(), "id不能为空");

        //检查修改目标是否存在
        Product productOld = facade.getProductById(productFunctionVo.getId());
        LimitUtil.notEmpty(productOld, "该产品不存在");

        //2. 如果修改了vendorNo，检查是否被别的供应商占用
        if (StringUtil.isNotEmpty(productFunctionVo.getProductNo())) {
            Product productOther = facade.getProductByProductNo(productFunctionVo.getProductNo());
            if (productOther != null && !Objects.equals(productOther.getId(), productFunctionVo.getId())) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("产品编号已被占用，如有疑问，请联系管理员。");
            }
        }

        productOld.setProductNo(productFunctionVo.getProductNo());
        productOld.setProductName(productFunctionVo.getProductName());
        productOld.setProductType(productFunctionVo.getProductType());
        productOld.setDescription(productFunctionVo.getDescription());
        productOld.setStatus(productFunctionVo.getStatus());
        productOld.setUpdateBy(currentOperator.getLoginName());
        productOld.setUpdateTime(new Date());

        facade.update(productOld);

        return RestResult.success("修改成功");
    }

    @Permission("fee:product:add")
    @PostMapping("add")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.CREATE, name = "修改数据")
    public RestResult<String> add(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid ProductFunctionVo productFunctionVo) {
        Product productOther = facade.getProductByProductNo(productFunctionVo.getProductNo());
        if (productOther != null && !Objects.equals(productOther.getId(), productFunctionVo.getId())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("产品功能编号已被占用，如有疑问，请联系管理员。");
        }

        Product product = new Product();
        product.setProductNo(productFunctionVo.getProductNo());
        product.setProductName(productFunctionVo.getProductName());
        product.setProductType(productFunctionVo.getProductType());
        product.setStatus(PublicStatus.ACTIVE);
        product.setDescription(productFunctionVo.getDescription());
        product.setUpdateBy(currentOperator.getLoginName());
        product.setUpdateTime(new Date());
        product.setCreateBy(currentOperator.getLoginName());
        product.setCreateTime(new Date());

        facade.addProductFunction(product);

        return RestResult.success("添加成功");
    }
}
