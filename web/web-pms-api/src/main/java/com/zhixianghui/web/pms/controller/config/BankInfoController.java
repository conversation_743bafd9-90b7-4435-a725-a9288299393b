package com.zhixianghui.web.pms.controller.config;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.config.BankCardBin;
import com.zhixianghui.facade.common.entity.config.BankInfo;
import com.zhixianghui.facade.common.service.BankCardBinFacade;
import com.zhixianghui.facade.common.service.BankInfoFacade;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.common.BankInfoVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 银行信息
 * @date 2020-08-31 16:43
 **/
@RestController
@RequestMapping("bankInfo")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BankInfoController {
    @Reference
    private BankInfoFacade bankInfoFacade;
    @Reference
    private BankCardBinFacade bankCardBinFacade;

    /**
     * 获取银行信息
     * @param bankChannelNo 联行号 准确查询
     * @param bankNameLike  银行名模糊查询
     * @param pageVo   分页参数
     * @return  银行分页
     */
    @GetMapping("getBankInfo")
    public RestResult<PageResult<List<BankInfo>>> getBankInfo(String bankChannelNo, String bankNameLike, PageVo pageVo){
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("bankChannelNo",bankChannelNo);
        paramMap.put("bankNameLike",bankNameLike);
        return RestResult.success(bankInfoFacade.listPage(paramMap,pageVo.toPageParam()));
    }

    @Permission("pms:bankInfo:add")
    @PostMapping("add")
    public RestResult<String> add(@Valid @RequestBody BankInfoVo bankInfoVo) {
        BankInfo bankInfo = bankInfoVo.toBankInfo(bankInfoVo);
        bankInfoFacade.insert(bankInfo);
        return RestResult.success("添加成功");
    }

    @Permission("pms:bankInfo:del")
    @PostMapping("del")
    public RestResult<String> del(@RequestParam Long id) {
        if (id == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("id参数为空");
        }

        bankInfoFacade.del(id);
        return RestResult.success("添加成功");
    }

    @Permission("pms:bankInfo:edit")
    @PostMapping("edit")
    public RestResult<BankInfo> edit(@RequestParam Long id) {
        if (id == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("id参数为空");
        }
        return RestResult.success(bankInfoFacade.edit(id));
    }

    @Permission("pms:bankInfo:edit")
    @PostMapping("update")
    public RestResult<String> update(@Valid @RequestBody BankInfoVo bankInfoVo) {
        if (bankInfoVo.getId() == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("id参数为空");
        }
        final BankInfo currentBankInfo = bankInfoFacade.getBankInfoById(bankInfoVo.getId());
        BeanUtil.copyProperties(bankInfoVo,currentBankInfo);
        bankInfoFacade.update(currentBankInfo);
        return RestResult.success("更新成功");
    }

    /**
     * 获取卡bin
     * @param cardNo 联行号 准确查询
     * @return  卡bin
     */
    @GetMapping("getBankCardBin")
    public RestResult<BankCardBin> getBankCardBin(@RequestParam String cardNo){
        return RestResult.success(bankCardBinFacade.getCardBinByCardNo(cardNo));
    }
}
