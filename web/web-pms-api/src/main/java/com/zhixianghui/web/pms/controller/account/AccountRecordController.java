package com.zhixianghui.web.pms.controller.account;

import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.report.ReportAccountHistory;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.ReportAccountHistoryFacade;
import com.zhixianghui.facade.common.vo.ReportAccountHistoryVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.vo.account.AccountRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AccountRecordController
 * @Description TODO
 * @Date 2022/12/2 15:32
 */
@RestController
@RequestMapping("accountRecord")
@Slf4j
public class AccountRecordController {

    @Reference
    private ReportAccountHistoryFacade reportAccountHistoryFacade;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    private OrderFacade orderFacade;

    /**
     * 获取商户账户记录
     * @param employerNo
     * @return
     */
    @GetMapping("getRecordByChannelType")
    public RestResult getAccountRecord(@RequestParam("employerNo") String employerNo, @RequestParam("mainstayNo") String mainstayNo,@RequestParam("channelType")Long channelType){
       List<ReportAccountHistory> reportAccountHistoryList = reportAccountHistoryFacade.getByChannelType(employerNo,mainstayNo,channelType);
        return RestResult.success(reportAccountHistoryList);
    }

    @PostMapping("changeStatus")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "修改账户可视状态")
    public RestResult changeStatus(@Valid @RequestBody AccountRecordVo accountRecordVo){
        if (accountRecordVo.getIsShow() == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("状态不能为空");
        }
        reportAccountHistoryFacade.changeStatus(accountRecordVo.getId(),accountRecordVo.getIsShow());
        return RestResult.success("修改成功");
    }

    @PostMapping("changeTitle")
    public RestResult changeTitle(@Valid @RequestBody AccountRecordVo accountRecordVo){
        if (StringUtils.isBlank(accountRecordVo.getTitle())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("标题不能为空");
        }
        reportAccountHistoryFacade.changeTitle(accountRecordVo.getId(),accountRecordVo.getTitle());
        return RestResult.success("修改成功");
    }
}
