package com.zhixianghui.web.pms.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRole;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRoleOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperateLogFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.permission.PmsOperateLogQueryVO;
import com.zhixianghui.web.pms.vo.permission.PmsOperatorEditVO;
import com.zhixianghui.web.pms.vo.permission.PmsOperatorQueryVO;
import com.zhixianghui.web.pms.vo.permission.PmsOperatorVO;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 员工管理
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("pmsOperator")
public class PmsOperatorController extends BaseController {
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private PmsPermissionFacade pmsPermissionFacade;
    @Reference
    private PmsOperateLogFacade pmsOperateLogFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private PmsDepartmentFacade departmentFacade;


    /**
     * 分页查询员工信息
     */
    @Permission("pms:operator:view")
    @RequestMapping("listPmsOperator")
    public RestResult<PageResult<List<PmsOperatorVO>>> listPmsOperator(@RequestBody @Valid PmsOperatorQueryVO vo) {
        return list(vo);
    }

    private RestResult<PageResult<List<PmsOperatorVO>>> list(PmsOperatorQueryVO vo) {
        if (vo.getRoleId() != null) {
            List<PmsRoleOperator> pmsRoleOperator = pmsOperatorFacade.listOperatorByRoleId(vo.getRoleId());
            if (CollectionUtils.isEmpty(pmsRoleOperator)) {
                return RestResult.success(null);
            }
            List<Long> operatorList = pmsRoleOperator.stream().map(PmsRoleOperator::getOperatorId).distinct().collect(Collectors.toList());
            vo.setIdList(operatorList);
        }
        PageResult<List<PmsOperator>> result = pmsOperatorFacade.listOperatorPage(BeanUtil.toMap(vo),
                PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            return RestResult.success(null);
        }
        List<PmsOperatorVO> list = result.getData().stream().map(PmsOperatorVO::buildVo).collect(Collectors.toList());
        list.forEach(item -> {
            List<PmsRole> pmsRoles = pmsPermissionFacade.listRolesByOperatorId(item.getId());
            item.setPmsRolesList(pmsRoles);
        });

        return RestResult.success(PageResult.newInstance(list, result.getPageCurrent(), result.getPageSize(), result.getTotalRecord()));
    }

    @RequestMapping("listSale")
    public RestResult<List<PmsOperatorVO>> listSale(@CurrentUser PmsOperator pmsOperator) {
        Long roleId = pmsOperatorFacade.getSaleRole();
        if (roleId == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到该角色对应的员工");
        }
        PmsOperatorQueryVO vo = new PmsOperatorQueryVO();
        vo.setRoleId(Math.toIntExact(roleId));
//        if (!(pmsOperator.getType() == PmsOperatorTypeEnum.ADMIN.getValue() || pmsOperatorFacade.isAdmin(pmsOperator.getId()))){
//            if (pmsOperator.getRoleIds().contains(roleId)) {
//                PmsDepartment department = departmentFacade.getDepartmentById(pmsOperator.getDepartmentId());
//                if (department.getLeaderId().longValue() != pmsOperator.getId().longValue()) {
//                    vo.setLoginName(pmsOperator.getLoginName());
//                }
//            }else {
//                return RestResult.success(null);
//            }
//        }

        RestResult<PageResult<List<PmsOperatorVO>>> list =  list(vo);
        if (list.getData() == null) {
            return RestResult.success(null);
        }

        return RestResult.success(list.getData().getData());
    }


    /**
     * 新增员工
     */
    @Permission("pms:operator:add")
    @PostMapping("addPmsOperator")
    public RestResult<String> addPmsOperator(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid PmsOperatorVO vo) {
        if (!ValidateUtil.validPassword(vo.getLoginPwd(), 8, 16, true, true, false)) {
            return RestResult.error("登录密码必须由字母、数字组成,8--16位");
        }

        PmsOperator pmsOperator = PmsOperatorVO.buildDto(vo);
        pmsOperator.setCreateTime(new Date());
        pmsOperator.setLoginPwd(DigestUtils.sha1Hex(vo.getLoginPwd()));
        pmsOperator.setStatus(PmsOperatorStatusEnum.UNAUDITED.getValue());
        pmsOperator.setType(PmsOperatorTypeEnum.USER.getValue());
        pmsOperator.setIsChangedPwd(0);
        pmsOperator.setCreator(currentOperator.getLoginName());

        List<Long> assignedRoles = pmsOperator.getRoleIds();
        pmsOperatorFacade.insertOperatorAndAssignRoles(pmsOperator, assignedRoles);
        super.logEdit("新增操作员[" + pmsOperator.getLoginName() + "新增后角色[" + assignedRoles + "]", true);
        return RestResult.success("新增成功");
    }

    /**
     * 查询单个员工信息
     * @param operatorId    员工id
     */
    @GetMapping("getPmsOperatorById")
    public RestResult<PmsOperatorVO> getPmsOperatorById(@RequestParam long operatorId) {
        PmsOperator pmsOperator = pmsOperatorFacade.getOperatorById(operatorId);
        if (pmsOperator == null) {
            return RestResult.error("查询的操作员不存在");
        }

        List<PmsRole> pmsRoles = pmsPermissionFacade.listRolesByOperatorId(operatorId);
        pmsOperator.setRoleIds(pmsRoles.stream().map(PmsRole::getId).collect(Collectors.toList()));
        return RestResult.success(PmsOperatorVO.buildVo(pmsOperator));
    }

    /**
     * 修改员工信息
     */
    @Permission("pms:operator:edit")
    @PostMapping("editPmsOperator")
    public RestResult<String> editPmsOperator(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid PmsOperatorEditVO vo) {
        PmsOperator pmsOperator = pmsOperatorFacade.getOperatorById(vo.getId());
        if (pmsOperator == null) {
            return RestResult.error("操作员不存在");
        }
        // 普通操作员没有修改超级管理员的权限
        if (PmsOperatorTypeEnum.ADMIN.getValue() == pmsOperator.getType()
                && PmsOperatorTypeEnum.ADMIN.getValue() != currentOperator.getType()) {
            return RestResult.error("无权修改超级管理员信息");
        }
        //普通操作员只能修改自己的信息
        pmsOperator.setRealName(vo.getRealName());
        pmsOperator.setMobileNo(vo.getMobileNo());
        pmsOperator.setDepartmentId(vo.getDepartmentId());
        pmsOperator.setRemark(vo.getRemark());
        pmsOperator.setUpdator(currentOperator.getLoginName());

        List<Long> assignedRoles = vo.getRoleIds();
        pmsOperatorFacade.updateOperatorAndAssignRoles(pmsOperator, assignedRoles);
        super.logEdit("修改操作员[" + pmsOperator.getLoginName() + "更改后角色[" + assignedRoles + "]", true);
        return RestResult.success("修改成功");
    }


    /**
     * 删除操作员
     * @param id    员工id
     */
    @Permission("pms:operator:delete")
    @RequestMapping("deletePmsOperator")
    public RestResult<String> deletePmsOperator(@RequestParam Long id) {
        PmsOperator pmsOperator = pmsOperatorFacade.getOperatorById(id); // 查询操作员信息

        if (pmsOperator == null) {
            return RestResult.error("操作员不存在");
        }
        if (Objects.equals(pmsOperator.getType(), PmsOperatorTypeEnum.ADMIN.getValue())) {
            return RestResult.error("超级管理员不可删除");
        }

        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(pmsOperator.getId());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        if (flowFacade.isExistTask(flowUserVo)){
            return RestResult.error("当前员工存在未处理待办，无法删除");
        }

        pmsOperatorFacade.deleteOperatorById(id);
        super.logDelete("删除操作员，名称:" + pmsOperator.getLoginName(), true);
        return RestResult.success("删除操作员成功");
    }


    /**
     * 重置员工密码
     * @param operatorId    员工id
     * @param newPwd        新密码
     */
    @Permission("pms:operator:resetpwd")
    @RequestMapping("resetPmsOperatorPwd")
    public RestResult<String> resetPmsOperatorPwd(@CurrentUser PmsOperator currentOperator, @RequestParam Long operatorId, @RequestParam String newPwd) {
        PmsOperator operator = pmsOperatorFacade.getOperatorById(operatorId);
        if (operator == null) {
            return RestResult.error("操作员不存在");
        }
        // 普通操作员没有修改超级管理员的权限
        if (PmsOperatorTypeEnum.ADMIN.getValue() == operator.getType()
                && PmsOperatorTypeEnum.ADMIN.getValue() != currentOperator.getType()) {
            return RestResult.error("你没有修改超级管理员的权限");
        }
        if (!ValidateUtil.validPassword(newPwd, 8, 16, true, true, false)) {
            return RestResult.error("登录密码必须由字母、数字组成,8--16位");
        }
        pmsOperatorFacade.updateOperatorPwd(operator.getId(), DigestUtils.sha1Hex(newPwd), false);
        super.logEdit("重置操作员[" + operator.getLoginName() + "]的密码", true);
        return RestResult.success("密码重置成功");
    }


    /**
     * 修改员工状态
     * @param id    员工id
     */
    @Permission("pms:operator:changestatus")
    @RequestMapping("changePmsOperatorStatus")
    public RestResult<String> changePmsOperatorStatus(@CurrentUser PmsOperator currentOperator, @RequestParam Long id) {
        if (Objects.equals(currentOperator.getId(), id)) {
            return RestResult.error("不能修改自己账户的状态");
        }
        PmsOperator operator = pmsOperatorFacade.getOperatorById(id);
        if (operator == null) {
            return RestResult.error("操作员不存在");
        }

        // 普通操作员没有修改超级管理员的权限
        if (PmsOperatorTypeEnum.ADMIN.getValue() == operator.getType()) {
            return RestResult.error("超级管理员状态不允许修改");
        }

        if (operator.getStatus() == PmsOperatorStatusEnum.UNAUDITED.getValue() && Objects.equals(currentOperator.getLoginName(), operator.getLoginName())) {
            //如果是未审核
            return RestResult.error("创建人与审核人不能相同");
        }
        Integer oldStatus = operator.getStatus();
        if (oldStatus == PmsOperatorStatusEnum.UNAUDITED.getValue()) {
            operator.setStatus(PmsOperatorStatusEnum.ACTIVE.getValue());
        } else if (oldStatus == PmsOperatorStatusEnum.ACTIVE.getValue()) {
            operator.setStatus(PmsOperatorStatusEnum.INACTIVE.getValue());
        } else if (oldStatus == PmsOperatorStatusEnum.INACTIVE.getValue()) {
            operator.setStatus(PmsOperatorStatusEnum.ACTIVE.getValue());
        }
        pmsOperatorFacade.updateOperator(operator);
        super.logEdit("修改操作员状态成功[" + operator.getLoginName() + "],oldStatus" + oldStatus + ",newStatus:" + operator.getStatus(), true);
        return RestResult.success("操作成功");
    }

    /**
     * 分页查询操作日志
     */
    @Permission("pms:operateLog:view")
    @RequestMapping("listOperateLogPage")
    public RestResult<PageResult<List<PmsOperateLog>>> listOperateLogPage(@RequestBody @Valid PmsOperateLogQueryVO vo) {
        PageResult<List<PmsOperateLog>> result = pmsOperateLogFacade.listOperateLogPage(BeanUtil.toMap(vo),
                PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(result);
    }

    /**
     * 查询部门下的员工
     * @param departmentId  部门id
     */
    @Permission("pms:department:view")
    @GetMapping("listByDepartmentId")
    public RestResult<List<PmsOperatorVO>> listByDepartmentId(@RequestParam long departmentId) {
        List<PmsOperator> operators = pmsOperatorFacade.listByDepartmentId(departmentId);
        return RestResult.success(operators.stream().map(PmsOperatorVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 递归查询部门下的员工
     */
    //注 应操作员有创建商户的权限时，即使没有部门管理权限也要能访问到该接口的要求，所以开发接口权限
    @RequestMapping("listByDepartmentIdRecursive")
    public RestResult<List<PmsOperatorVO>> listByDepartmentIdRecursive(@RequestParam long departmentId) {
        List<PmsOperator> operators = pmsOperatorFacade.listByDepartmentIdRecursive(departmentId);
        return RestResult.success(operators.stream().map(PmsOperatorVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 查询未分配部门的员工
     */
    @Permission("pms:department:view")
    @RequestMapping("listWithoutDepartment")
    public RestResult<List<PmsOperatorVO>> listWithoutDepartment() {
        List<PmsOperator> operators = pmsOperatorFacade.listWithoutDepartment();
        return RestResult.success(operators.stream().map(PmsOperatorVO::buildVo).collect(Collectors.toList()));
    }

//    /**
//     * 校验输入的操作员数据
//     *
//     * @param operator .
//     * @throws BizException .
//     */
//    private void validatePmsOperator(PmsOperator operator) throws BizException {
//        if (!ValidateUtil.isStrLengthValid(operator.getRealName(), 2, 15)) {
//            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("真实姓名长度必须为2--15");
//        } else if (!operator.getRealName().matches("[^\\x00-\\xff]+")) {
//            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("真实姓名必须为中文");
//        } else if (!ValidateUtil.isStrLengthValid(operator.getLoginName(), 3, 50)) {
//            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("登录名长度必须为3--50");
//        } else if (!ValidateUtil.isMobile(operator.getMobileNo())) {
//            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("手机号无效");
//        }
//    }
}
