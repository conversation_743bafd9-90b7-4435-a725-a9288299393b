package com.zhixianghui.web.pms.vo.invoice;

import lombok.Data;

import java.util.List;

@Data
public class InvoicePreRecordVo {

    private Long id;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 用工企业编号
     */
    private String employerMchNo;

    /**
     * 用工企业名称
     */
    private String employerMchName;

    /**
     * 代征主体编号
     */
    private String mainstayMchNo;

    /**
     * 代征主体名称
     */
    private String mainstayMchName;

    /**
     * 岗位类目
     */
    private String workCategoryCode;

    /**
     * 岗位类目名称
     */
    private String workCategoryName;

    /**
     * 开票金额
     */
    private java.math.BigDecimal invoiceAmount;

    /**
     * 发票类目编码
     */
    private String invoiceCategoryCode;

    /**
     * 发票类目名称
     */
    private String invoiceCategoryName;

    /**
     * 发票影像文件url
     */
    private List<String> invoiceFileUrl;

    /**
     * 备注
     */
    private String remark;
    /**
     * 发票状态
     * @see com.zhixianghui.common.statics.enums.invoice.InvoicePreStatusEnum
     */
    private Integer invoiceStatus;
}
