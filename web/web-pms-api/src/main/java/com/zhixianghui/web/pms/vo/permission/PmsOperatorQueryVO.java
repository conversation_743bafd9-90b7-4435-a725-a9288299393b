package com.zhixianghui.web.pms.vo.permission;

import com.zhixianghui.web.pms.vo.PageVo;

import java.util.List;

/**
 * Author: Cmf
 * Date: 2020.1.20
 * Time: 15:43
 * Description: 操作员查询VO
 */
public class PmsOperatorQueryVO extends PageVo {

    /**
     * 登录名
     */
    private String loginName;


    /**
     * 姓名
     */
    private String realName;

    /**
     * 姓名 like
     */
    private String realNameLike;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 角色id
     */
    private Integer roleId;

    private List<Long> idList;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRealNameLike() { return realNameLike; }

    public void setRealNameLike(String realNameLike) { this.realNameLike = realNameLike; }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }
}
