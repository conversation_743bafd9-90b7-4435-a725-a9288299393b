package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.fee.entity.AgentFeeOrder;
import com.zhixianghui.facade.fee.entity.MerchantFeeOrder;
import com.zhixianghui.facade.fee.service.AgentFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.MerchantFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.vo.MerchantFeeStatisticVo;
import com.zhixianghui.facade.fee.vo.MerchantFeeSumVo;
import com.zhixianghui.facade.merchant.entity.MerchantSaler;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantSalerFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.merchant.PmsBiz;
import com.zhixianghui.web.pms.vo.fee.MerchantFeeOrderQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商户计费订单
 */
@Slf4j
@RestController
@RequestMapping("merchantFeeOrder")
public class MerchantFeeOrderController {
    @Reference
    private AgentFeeOrderQueryFacade agentQueryFacade;
    @Reference
    private MerchantFeeOrderQueryFacade queryFacade;
    @Reference
    private MerchantSalerFacade merchantSalerFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Autowired
    private PmsBiz pmsBiz;

    @PostMapping("exportMerchantStatistics")
    public RestResult exportMerchantStatistics(@RequestBody @Valid MerchantFeeOrderQueryVo vo, @CurrentUser PmsOperator operator){
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        // 销售部只能查自己及下属的商户
        List<MerchantSaler> merchantSaleList = null;
        if(pmsBiz.isSaleDepartment(operator)) {
            log.info("[{}]当前人员为销售人员", operator.getId());
            List<Long> result = pmsBiz.getSaleId(operator);
            // 由于跨库, 联表麻烦, 需要把tbl_merchant_saler表的相关salerId也查出来
            merchantSaleList = merchantSalerFacade.getBatchMerchantSale(result);
            if (CollectionUtils.isEmpty(merchantSaleList)) {
                return RestResult.error("查询内容为空，无须导出");
            }
        }
        putConditionIfSale(paramMap, merchantSaleList);
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.PMS_MERCHANT_MERGE_FEE.getFileName());
        record.setReportType(ReportTypeEnum.PMS_MERCHANT_MERGE_FEE.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setDeepPage(true);

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.PMS_MERCHANT_MERGE_FEE.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("merchantStatistics")
    public RestResult<PageResult<List<MerchantFeeStatisticVo>>> merchantStatistics(@RequestBody @Valid MerchantFeeOrderQueryVo vo, @CurrentUser PmsOperator operator){
        PageParam pageParam = PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize());
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        // 销售部只能查自己及下属的商户
        List<MerchantSaler> merchantSaleList = null;
        if(pmsBiz.isSaleDepartment(operator)) {
            log.info("[{}]当前人员为销售人员", operator.getId());
            List<Long> result = pmsBiz.getSaleId(operator);
            // 由于跨库, 联表麻烦, 需要把tbl_merchant_saler表的相关salerId也查出来
            merchantSaleList = merchantSalerFacade.getBatchMerchantSale(result);
            if (CollectionUtils.isEmpty(merchantSaleList)) {
                return RestResult.success(PageResult.newInstance(new ArrayList<>(), vo.getPageCurrent(), vo.getPageSize()));
            }
        }
        putConditionIfSale(paramMap, merchantSaleList);
        PageResult<List<MerchantFeeStatisticVo>> pageResult = queryFacade.merchantFeeStatistics(paramMap, pageParam);
        return RestResult.success(pageResult);
    }

    /**
     * 商户计费订单分页查询
     * @param vo
     * @return
     */
    @Permission("fee:merchantFeeOrder:list")
    @PostMapping("listPage")
    public RestResult<PageResult<List<MerchantFeeOrder>>> listPage(
            @RequestBody @Valid MerchantFeeOrderQueryVo vo, @CurrentUser PmsOperator operator
    ) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        // 销售部只能查自己及下属的商户
        List<MerchantSaler> merchantSaleList = null;
        if(pmsBiz.isSaleDepartment(operator)) {
            log.info("[{}]当前人员为销售人员", operator.getId());
            List<Long> result = pmsBiz.getSaleId(operator);
            // 由于跨库, 联表麻烦, 需要把tbl_merchant_saler表的相关salerId也查出来
            merchantSaleList = merchantSalerFacade.getBatchMerchantSale(result);
            if (CollectionUtils.isEmpty(merchantSaleList)) {
                return RestResult.success(PageResult.newInstance(null, vo.getPageCurrent(), vo.getPageSize()));
            }
        }
        putConditionIfSale(paramMap, merchantSaleList);

        return RestResult.success(queryFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize())));
    }

    @SuppressWarnings("unchecked")
    private List<String> putConditionIfAgent(Map<String, Object> paramMap, MerchantFeeOrderQueryVo vo) {
        PageResult<List<AgentFeeOrder>> pageResult = agentQueryFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getData())) {
            return null;
        }

        List<String> platTrxNoList = pageResult.getData().stream().map(AgentFeeOrder::getPlatTrxNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(platTrxNoList)) {
            return null;
        }
        paramMap.put("platTrxNoList", platTrxNoList);
        return platTrxNoList;
    }

    private void putConditionIfSale(Map<String, Object> paramMap, List<MerchantSaler> merchantSaleList) {
        if (!CollectionUtils.isEmpty(merchantSaleList)) {
            List<String> mchNoList = merchantSaleList.stream().map(MerchantSaler :: getMchNo).collect(Collectors.toList());
            paramMap.put("mchNoList", mchNoList);
        }
    }

    /**
     * 商户计费订单统计
     * @param vo
     * @return
     */
    @Permission("fee:merchantFeeOrder:sum")
    @PostMapping("sumMerchantFeeOrder")
    public RestResult<MerchantFeeSumVo> sumMerchantFeeOrder(@RequestBody @Valid MerchantFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        if(pmsBiz.isSaleDepartment(operator)) {
            log.info("[{}]当前人员为销售人员", operator.getId());
            List<Long> result = pmsBiz.getSaleId(operator);
            List<MerchantSaler> merchantSaleList = merchantSalerFacade.getBatchMerchantSale(result);
            if (!CollectionUtils.isEmpty(merchantSaleList)) {
                putConditionIfSale(paramMap, merchantSaleList);
            }
        }
        MerchantFeeSumVo sumVo = queryFacade.sumMerchantFeeOrder(paramMap);
        return RestResult.success(sumVo);
    }

    /**
     * 商户计费订单导出
     *
     * @return .
     */
    @RequestMapping("exportMerchantFeeOrder")
    @Permission("fee:merchantFeeOrder:export")
    public RestResult<String> exportMerchantFeeOrder(@Valid @RequestBody MerchantFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.MERCHANT_FEE_ORDER.getFileName());
        record.setReportType(ReportTypeEnum.MERCHANT_FEE_ORDER.getValue());

        Map<String,Object> paramMap = BeanUtil.toMap(vo);
        // 销售部只能查自己及下属的商户
        if(pmsBiz.isSaleDepartment(operator)) {
            log.info("[{}]当前人员为销售人员", operator.getId());
            List<Long> result = pmsBiz.getSaleId(operator);
            List<MerchantSaler> merchantSaleList = merchantSalerFacade.getBatchMerchantSale(result);
            if (!CollectionUtils.isEmpty(merchantSaleList)) {
                putConditionIfSale(paramMap, merchantSaleList);
            }else{
                return RestResult.error("没有与销售绑定的商户");
            }
        }

        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_FEE_ORDER.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
