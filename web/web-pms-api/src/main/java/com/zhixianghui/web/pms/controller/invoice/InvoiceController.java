package com.zhixianghui.web.pms.controller.invoice;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.dto.InvoiceRecordDetailDto;
import com.zhixianghui.facade.trade.dto.UpdateInvoiceDetailDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.InvoiceRecordDetailFacade;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.facade.trade.vo.InvoiceEditVo;
import com.zhixianghui.facade.trade.vo.InvoiceUpdateVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.vo.invoice.PmsInvoiceRecordQueryVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 发票业务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("invoice")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class InvoiceController {

    @Reference
    private InvoiceFacade invoiceFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private InvoiceRecordDetailFacade invoiceRecordDetailFacade;

    /**
     * 发票申请记录分页查询
     *
     * @param queryVo 查询vo
     */
    @RequestMapping("listPage")
    @Permission("invoice:view")
    public RestResult<PageResult<List<InvoiceRecord>>> listPage(@RequestBody PmsInvoiceRecordQueryVo queryVo) {
        Map<String, Object> paramMap = BeanUtil.toMap(queryVo);
        /**
         * 如果日期为空，则默认查询当前年份
         */
        if (StringUtils.isAnyBlank(queryVo.getCreateTimeBegin(), queryVo.getCreateTimeEnd())) {
            Date today = new Date();
            String startDate = DateUtil.beginOfYear(today).toString();
            String endDate = DateUtil.endOfDay(today).toString();
            paramMap.put("createTimeBegin", startDate);
            paramMap.put("createTimeEnd", endDate);
            log.info("开票申请记录查询参数:{}",JsonUtil.toStringPretty(paramMap));
        }
        PageResult<List<InvoiceRecord>> pageResult = invoiceFacade.listPage(paramMap, queryVo.getPageParam());
        return RestResult.success(pageResult);
    }

    @PostMapping("exportInvoiceRecord")
    @Permission("invoice:view")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "发票申请记录-导出")
    public RestResult<String> exportRechargeRecord(@RequestBody PmsInvoiceRecordQueryVo queryVo, @CurrentUser PmsOperator operator) {
        String startTime =  queryVo.getCreateTimeBegin();
        String endTime = queryVo.getCreateTimeEnd();
        if (StringUtils.isAnyBlank(startTime, endTime)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("请提供起止创建时间");
        }

        validDate(LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.MERCHANT_INVOICE_RECORD_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.MERCHANT_INVOICE_RECORD_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(queryVo));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_INVOICE_RECORD_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private void validDate(LocalDateTime createBeginDate, LocalDateTime createEndDate) {
        if(createBeginDate.isAfter(createEndDate)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
        }
    }
    /**
     * 根据流水号获取发票申请记录详细信息
     *
     * @param trxNo 发票流水号
     */
    @GetMapping("getByTrxNo")
    @Permission("invoice:view")
    public RestResult<InvoiceRecord> getByTrxNo(@RequestParam String trxNo) {
        InvoiceRecord record = invoiceFacade.getByTrxNo(trxNo);
        return RestResult.success(record);
    }

    /**
     * 更新发票记录状态
     */
    @PostMapping("updateStatus")
    @Permission("invoice:updateStatus")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新发票记录状态")
    public RestResult<String> updateStatus(@Valid @RequestBody InvoiceUpdateVo vo) {
        invoiceFacade.updateStatus(vo);
        return RestResult.success("更新发票状态成功");
    }

    /**
     * 更新发票记录
     */
    @PostMapping("updateRecord")
    @Permission("invoice:updateStatus")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新发票记录")
    public RestResult<String> updateRecord(@Valid @RequestBody InvoiceEditVo vo) {
        invoiceFacade.updateInvoiceRecord(vo);
        return RestResult.success("更新开票信息成功");
    }

    /**
     * 查询普通发票批次详情
     * @param page
     * @param invoiceRecordDetailDto
     * @return
     */
    @PostMapping("invoiceRecordDetailPage")
    public RestResult<IPage<InvoiceDetailGroupByIdCardVo>> invoiceRecordDetailPage(@RequestBody Page<InvoiceDetailGroupByIdCardVo> page, @RequestBody InvoiceRecordDetailDto invoiceRecordDetailDto, @CurrentUser PmsOperator pmsOperator) {

        final IPage<InvoiceDetailGroupByIdCardVo> detailPage = invoiceFacade.listInvoiceDetailGroupByIdCard(page, invoiceRecordDetailDto.getInvoiceTrxNo());

        return RestResult.success(detailPage);
    }

    @PostMapping("confirmInvoiceRecordDetail")
    public RestResult<String> confirmInvoiceRecordDetail(@RequestBody UpdateInvoiceDetailDto updateInvoiceDetailDto, @CurrentUser PmsOperator pmsOperator) {
        invoiceRecordDetailFacade.updateInvoiceRecordDetail(updateInvoiceDetailDto);
        return RestResult.success("操作成功");
    }
}
