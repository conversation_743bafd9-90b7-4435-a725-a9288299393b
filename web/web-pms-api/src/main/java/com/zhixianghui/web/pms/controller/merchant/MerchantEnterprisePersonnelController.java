package com.zhixianghui.web.pms.controller.merchant;


import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantEnterprisePersonnelFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@RestController
@RequestMapping("/merchantEnterprisePersonnel")
public class MerchantEnterprisePersonnelController {

    @Reference
    private MerchantEnterprisePersonnelFacade merchantEnterprisePersonnelFacade;


//    @GetMapping("/syncAllMerchant")
////    @Logger(type = OperateLogTypeEnum.MODIFY, name = "同步企业人员信息")
//    public RestResult syncAllMerchant() {
//         merchantEnterprisePersonnelFacade.syncAllMerchant();
//        return RestResult.success("同步成功");
//    }
}
