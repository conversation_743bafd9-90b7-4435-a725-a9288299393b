package com.zhixianghui.web.pms.controller.report;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.report.EmployerMainstayRelationBiz;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.report.req.EmployerMainstayRelationQueryVo;
import com.zhixianghui.web.pms.vo.report.req.EmployerMainstayRelationReqVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 代征关系Controller
 * @date 2020-09-28 11:09
 **/
@RestController
@RequestMapping("employerMainstayRelation")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmployerMainstayRelationController {

    private final EmployerMainstayRelationBiz employerMainstayRelationBiz;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;


    @Logger(type = OperateLogTypeEnum.MODIFY,name = "批量修改状态")
    @Permission("report:employerMainstayRelation:edit")
    @PostMapping("batchUpdate")
    public RestResult<String> updateAllStatus(@RequestBody Map<String,Object> paramMap,@CurrentUser PmsOperator pmsOperator){
        Integer status = (Integer) paramMap.get("status");
        List<Long> ids = (List<Long>) paramMap.get("ids");
        if (ids == null || ids.size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未选择对应的代征关系");
        }
        employerMainstayRelationBiz.batchUpdate(status,ids);
        return RestResult.success("批量修改状态成功");
    }

    /**
     * 创建代征关系
     *
     * @param employerMainstayRelationReqVo 代征关系
     * @param currentOperator               创建人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.CREATE, name = "创建代征关系")
    @Permission("report:employerMainstayRelation:add")
    @PostMapping("create")
    public RestResult<String> create(@Validated @RequestBody EmployerMainstayRelationReqVo employerMainstayRelationReqVo, @CurrentUser PmsOperator currentOperator) {
        employerMainstayRelationBiz.create(employerMainstayRelationReqVo, currentOperator);
        return RestResult.success("创建成功");
    }

    /**
     * 变更代征关系状态
     *
     * @param id              代征关系id
     * @param status          状态
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "变更代征关系")
    @Permission("report:employerMainstayRelation:edit")
    @PostMapping("changeStatus")
    public RestResult<String> changeStatus(@RequestParam Long id, @RequestParam Integer status, @CurrentUser PmsOperator currentOperator) {
        employerMainstayRelationBiz.changeStatus(id, status, currentOperator.getRealName());
        return RestResult.success("状态修改成功");
    }

    /**
     * 更新代征关系
     *
     * @param employerMainstayRelationReqVo 代征关系
     * @param currentOperator               操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新代征关系")
    @Permission("report:employerMainstayRelation:edit")
    @PostMapping("update")
    public RestResult<String> update(@Validated @RequestBody EmployerMainstayRelationReqVo employerMainstayRelationReqVo, @CurrentUser PmsOperator currentOperator) {
        if (employerMainstayRelationReqVo.getId() == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("id 不能为空");
        }
        employerMainstayRelationBiz.update(employerMainstayRelationReqVo, currentOperator);
        return RestResult.success("更新成功");
    }

    /**
     * 删除代征关系
     *
     * @param id 代征关系id
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.DELETE, name = "删除代征关系")
    @Permission("report:employerMainstayRelation:edit")
    @PostMapping("delete")
    public RestResult<String> delete(@RequestParam Long id) {
        employerMainstayRelationBiz.delete(id);
        return RestResult.success("删除成功");
    }

    /**
     * 获取单个代征关系
     *
     * @param id 代征关系id
     * @return 代征关系详情
     */
    @Permission("report:employerMainstayRelation:view")
    @GetMapping("getById/{id}")
    public RestResult<EmployerMainstayRelation> getById(@PathVariable("id") Long id) {
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationBiz.getById(id);
        return RestResult.success(employerMainstayRelation);
    }

    /**
     * 分页查询代征关系
     *
     * @param queryVo 查询条件
     * @param pageVo  分页参数
     * @return 代征关系列表
     */
    @Permission("report:employerMainstayRelation:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<EmployerMainstayRelation>>> listPage(@RequestBody EmployerMainstayRelationQueryVo queryVo, @RequestBody PageVo pageVo) {
        PageResult<List<EmployerMainstayRelation>> pageResult = employerMainstayRelationBiz.listPage(queryVo, pageVo.toPageParam("UPDATE_TIME DESC"));
        return RestResult.success(pageResult);
    }

    @Permission("report:employerMainstayRelation:view")
    @PostMapping("getRelationListByEmployerNo")
    public RestResult<List<EmployerMainstayRelation>> getRelationListByEmployerNo(@RequestParam String employerNo) {
        List<EmployerMainstayRelation> employerMainstayRelationList = employerMainstayRelationBiz.getRelationListByEmployerNo(employerNo);
        return RestResult.success(employerMainstayRelationList);
    }

    @Permission("report:employerMainstayRelation:view")
    @PostMapping("exportEmployerMainstayRelation")
    public RestResult<String> exportEmployerMainstayRelation(@RequestBody EmployerMainstayRelationQueryVo queryVo, @CurrentUser PmsOperator pmsOperator) {
        Map<String, Object> paramMap = BeanUtil.toMap(queryVo);
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.EMPLOYER_MAINSTAY_RELATION_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.EMPLOYER_MAINSTAY_RELATION_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.EMPLOYER_MAINSTAY_RELATION_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
