package com.zhixianghui.web.pms.vo.fee;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class AgentFeeOrderQueryVo extends PageQueryVo {

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 商户名称
     */
    private String mchNameLike;

    /**
     * 商户编号
     */
    private String agentNo;

    /**
     * 商户名称
     */
    private String agentNameLike;

    /**
     * 产品编码
     */
    private String productNo;

    /**
     * 奖励类型
     */
    @EnumValue(intValues = {0,1}, message = "奖励类型有误")
    private Integer rewardType;

    @NotEmpty(message = "交易时间不能为空")
    private String tradeTimeBegin;

    @NotEmpty(message = "交易时间不能为空")
    private String tradeTimeEnd;

    /**
     * 创建时间起
     */
    private String createTimeBegin;

    /**
     * 创建时间止
     */
    private String createTimeEnd;

}