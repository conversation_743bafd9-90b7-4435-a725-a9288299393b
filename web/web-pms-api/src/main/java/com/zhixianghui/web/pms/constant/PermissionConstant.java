package com.zhixianghui.web.pms.constant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Author: Cmf
 * Date: 2019/10/9
 * Time: 17:08
 * Description: 与权限相关的常量
 */
public class PermissionConstant {

    /**
     * logger.
     */
    private static final Logger logger = LoggerFactory.getLogger(PermissionConstant.class);


    public static final String REQUEST_TOKEN_HEADER = "x-token";

    /**
     * 登录操作员的session键名.
     */
    public static final String OPERATOR_SESSION_KEY = "CURRENT_OPERATOR";

    /**
     * 登录操作员拥有的权限集合的session键名.
     */
    public static final String PERMISSION_SESSION_KEY = "CURRENT_PERMISSIONS";

    /**
     * 保存登录验证码的session键名
     */
    public static final String RANDOM_CODE_SESSION_KEY = "RANDOM_CODE_KEY";

    /**
     * 保存登录验证码生成时间的session键名
     */
    public static final String RANDOM_TIME_SESSION_KEY = "RANDOM_TIME_KEY";

    /**
     * 操作员密码连续输错次数限制(默认5).
     */
    public static int WEB_PWD_INPUT_ERROR_LIMIT = 5;

    /**
     * session过期时间/秒
     */
    public static final int SESSION_EXPIRE_TIME = 3600;

    /**
     * 下载链接url前缀
     */
    public static final String DOWNLOAD_URL_PREFIX = "/download/";

    /**
     * 图形验证码key
     */
    public static final String CAPTCHA_KEY = "CAPTCHA_KEY";

    /**
     * 图形验证码过期时间
     */
    public static final int CAPTCHA_EXPIRE_TIME = 300;

    /**
     * 邮箱验证码key
     */
    public static final String EMAIL_CODE_KEY = "EMAIL_CODE_KEY";

    /**
     * 校验邮箱验证码key
     */
    public static final String VERIFY_EMAIL_CODE_KEY = "VERIFY_EMAIL_CODE_KEY";

    /**
     * 邮箱验证码重发时间
     */
    public static final int EMAIL_CODE_RESEND_TIME = 60;

    /**
     * 邮箱验证码过期时间
     */
    public static final int EMAIL_CODE_EXPIRE_TIME = 300;

    /**
     * 操作员密码连续输错次数限制(默认5).
     */
    public static final int LOGIN_PWD_ERROR_LIMIT = 5;

    /**
     * 用户动态RSA对的临存key
     */
    public static final String USER_DYNAMIC_RSA_KEY = "USER_DYNAMIC_RSA_KEY";

    /**
     * 用户动态RSA对缓存时间
     */
    public static final Integer USER_DYNAMIC_RSA_EXPIRE_TIME = 120;
}
