package com.zhixianghui.web.pms.vo.report;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MerchantAccountExportExcelVo {
    @ExcelProperty(value = "用工企业编号")
    private String employerNo;
    @ExcelProperty(value = "用工企业名称")
    private String employerName;
    @ExcelProperty(value = "账户总可用金额")
    private BigDecimal totalAmount;
}
