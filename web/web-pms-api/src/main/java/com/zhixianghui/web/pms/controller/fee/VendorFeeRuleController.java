package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.fee.entity.VendorFeeRule;
import com.zhixianghui.facade.fee.service.VendorFeeRuleFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.utils.FeeRuleUtil;
import com.zhixianghui.web.pms.vo.fee.VendorFeeRelationQueryVo;
import com.zhixianghui.web.pms.vo.fee.VendorFeeRelationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("vendor_fee_manager")
public class VendorFeeRuleController extends BaseController {

    @Reference
    private VendorFeeRuleFacade facade;

    @Permission("fee:vendorFeeManager:list")
    @PostMapping("list")
    public RestResult<PageResult<List<VendorFeeRule>>> listMerchantFeeRelation(@RequestBody @Valid VendorFeeRelationQueryVo vo) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        PageResult<List<VendorFeeRule>> pageResult = facade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        pageResult.getData().stream().forEach(x ->{
            if(x.getFeeRate() != null){
                x.setFeeRate(AmountUtil.mul(x.getFeeRate(), new BigDecimal(100)));
            }
        });
        return RestResult.success(pageResult);
    }

    @Permission("fee:vendorFeeManager:delete")
    @PostMapping("delete/{id}")
    @Logger(type = OperateLogTypeEnum.DELETE, name = "删除数据")
    public RestResult<Map> delete(@CurrentUser PmsOperator currentOperator, @PathVariable long id) {
        log.info("<<VendorFeeController.delete>> 删除供应商计费管理 id:{}", id);
        facade.delete(id, currentOperator.getLoginName());

        Map responseMap = new HashMap();
        responseMap.put("msg", "删除操作成功");
        return RestResult.success(responseMap);

    }

    @Permission("fee:vendorFeeManager:edit")
    @PostMapping("edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "成本计费规则修改")
    public RestResult<Map> edit(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid VendorFeeRelationVo vo) {
        VendorFeeRule relation = facade.getById(vo.getId());
        LimitUtil.notEmpty(relation, "id不存在");

        // 规则校验
        FeeRuleUtil.checkFeeRule(vo);

        relation.setVendorNo(vo.getVendorNo());
        relation.setVendorName(vo.getVendorName());
        relation.setUpdateBy(currentOperator.getLoginName());
        relation.setUpdateTime(new Date());
        relation.setDescription(vo.getDescription());

        FeeRuleUtil.fillFeeRule(relation, vo);

        facade.update(relation);

        Map responseMap = new HashMap();
        responseMap.put("msg", "修改操作成功");
        return RestResult.success(responseMap);
    }

    @Permission("fee:vendorFeeManager:add")
    @PostMapping("add")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "成本计费规则添加")
    public RestResult<Map> add(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid VendorFeeRelationVo vo) {
        // 规则校验
        FeeRuleUtil.checkFeeRule(vo);

        VendorFeeRule relation = new VendorFeeRule();
        relation.setVendorNo(vo.getVendorNo());
        relation.setVendorName(vo.getVendorName());
        relation.setStatus(PublicStatus.ACTIVE);
        relation.setRemoved(RemovedEnum.NORMAL.getValue());
        relation.setUpdateBy(currentOperator.getLoginName());
        relation.setUpdateTime(new Date());
        relation.setCreateBy(currentOperator.getLoginName());
        relation.setCreateTime(new Date());
        relation.setVersion(0);
        relation.setDescription(vo.getDescription());

        FeeRuleUtil.fillFeeRule(relation, vo);

        facade.addVendorFeeRelation(relation);

        Map responseMap = new HashMap();
        responseMap.put("msg", "新增操作成功");
        return RestResult.success(responseMap);
    }

    @Permission("fee:vendorFeeManager:view")
    @GetMapping("view/{id}")
    public RestResult<VendorFeeRule> view(@PathVariable long id) {
        VendorFeeRule vendorFeeRule = facade.getById(id);
        if(vendorFeeRule.getFeeRate() != null) {
            vendorFeeRule.setFeeRate(AmountUtil.mul(vendorFeeRule.getFeeRate(), new BigDecimal(100)));
        }
        return RestResult.success(vendorFeeRule);
    }

}
