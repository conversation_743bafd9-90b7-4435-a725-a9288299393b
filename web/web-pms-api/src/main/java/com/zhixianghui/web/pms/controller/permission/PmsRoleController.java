package com.zhixianghui.web.pms.controller.permission;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRole;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.permission.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色管理
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("pmsRole")
public class PmsRoleController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(PmsRoleController.class);

    @Reference
    private PmsPermissionFacade pmsPermissionFacade;

    /**
     * 分页查询角色
     */
    @Permission("pms:role:view")
    @PostMapping("listPmsRole")
    public RestResult<PageResult<List<PmsRoleVO>>> listPmsRole(@RequestBody PmsRoleQueryVO vo) {
        Map<String, Object> paramMap = new HashMap<>();
        if (StringUtils.isNotBlank(vo.getRoleName())) {
            paramMap.put("roleName", vo.getRoleName());
        }
        PageResult<List<PmsRole>> result = pmsPermissionFacade.listRolePage(paramMap,
                PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            return RestResult.success(null);
        }
        List<PmsRoleVO> list = result.getData().stream().map(PmsRoleVO::buildVo).collect(Collectors.toList());
        list.forEach(item -> {
            item.setEmployerCount(pmsPermissionFacade.countEmployerByRoleId(item.getId()));
        });

        return RestResult.success(PageResult.newInstance(list, result.getPageCurrent(), result.getPageSize(), result.getTotalRecord()));
    }

    /**
     * 查询所有角色
     */
    @Permission("pms:operator:view")
    @GetMapping("listAllPmsRoles")
    public RestResult<List<PmsRoleVO>> listAllPmsRoles() {
        List<PmsRole> allRoleList = pmsPermissionFacade.listAllRoles();
        return RestResult.success(allRoleList.stream().map(PmsRoleVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 新增角色
     */
    @Permission("pms:role:add")
    @PostMapping("addPmsRole")
    public RestResult<String> addPmsRole(@RequestBody @Valid PmsRoleVO pmsRoleVO) {

        PmsRole pmsRole = PmsRoleVO.buildDto(pmsRoleVO);
        pmsPermissionFacade.createRole(pmsRole);
        // 记录操作员操作日志
        super.logSave("添加角色，角色名称[" + pmsRole.getRoleName() + "]", true);
        return RestResult.success("添加角色成功");
    }

    /**
     * 更新角色
     */
    @Permission("pms:role:edit")
    @PostMapping("editPmsRole")
    public RestResult<String> editPmsRole(@RequestBody @Valid PmsRoleEditVO vo) {
        try {
            PmsRole role = pmsPermissionFacade.getRoleById(vo.getId());
            if (role == null) {
                return RestResult.error("无法获取要修改的角色");
            }
            if (role.getRoleType() == RoleTypeEnum.PRESET.getType()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前角色不允许编辑");
            }
            role.setRemark(vo.getRemark());
            pmsPermissionFacade.updateRole(role);

            super.logDelete("修改角色，名称:" + role.getRoleName(), true);
            return RestResult.success("修改角色成功");
        } catch (Exception e) {
            logger.error("== editPmsRole exception:", e);
            super.logDelete("修改角色出错:" + e.getMessage(), false);
            return RestResult.error("修改失败");
        }
    }

    /**
     * 删除角色
     * @param id    角色id
     */
    @Permission("pms:role:delete")
    @RequestMapping("deletePmsRole")
    public RestResult<String> deletePmsRole(@RequestParam Long id) {
        PmsRole role = pmsPermissionFacade.getRoleById(id);
        if (role == null) {
            return RestResult.error("无法获取要删除的角色");
        }

        pmsPermissionFacade.deleteRoleById(id);
        super.logDelete("删除角色，名称:" + role.getRoleName(), true);
        return RestResult.success("删除角色成功");
    }

    /**
     * 查询角色关联的菜单
     * @param roleId    角色id
     */
    @Permission("pms:role:assignFunction")
    @GetMapping("listPmsRoleFunction")
    public RestResult<List<PmsFunctionVO>> listPmsRoleFunction(@RequestParam Long roleId) {
        List<PmsFunction> functions = pmsPermissionFacade.listFunctionByRoleId(roleId);
        return RestResult.success(functions.stream().map(PmsFunctionVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 为角色分配功能
     */
    @Permission("pms:role:assignFunction")
    @PostMapping("assignPmsRoleFunction")
    public RestResult<String> assignPmsRoleFunction(@RequestBody @Valid PmsRoleAssignFunctionVO vo) {
        PmsRole pmsRole = pmsPermissionFacade.getRoleById(vo.getRoleId());
        if (pmsRole == null) {
            return RestResult.error("无法获取角色信息");
        }

        // 分配菜单权限，功能权限
        pmsPermissionFacade.assignPermission(vo.getRoleId(), vo.getFunctionIds());
        super.logEdit("修改角色[" + pmsRole.getRoleName() + "]的功能，功能ID[" + JSON.toJSONString(vo.getFunctionIds()) + "]", true);
        return RestResult.success("分配功能成功");
    }
}
