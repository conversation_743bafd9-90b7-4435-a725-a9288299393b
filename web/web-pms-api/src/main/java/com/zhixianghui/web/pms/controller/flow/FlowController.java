package com.zhixianghui.web.pms.controller.flow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.flow.dto.CommonFlowLogVo;
import com.zhixianghui.facade.flow.dto.CommonFlowVo;
import com.zhixianghui.facade.flow.entity.CommonFlowLog;
import com.zhixianghui.facade.flow.enums.ComponentTypeEnum;
import com.zhixianghui.facade.flow.enums.WorkTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.service.FlowImageFacade;
import com.zhixianghui.facade.flow.vo.req.*;
import com.zhixianghui.facade.flow.vo.res.TaskResVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.vo.PageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @ClassName ProcessInstanceController
 * @Description TODO
 * @Date 2021/4/30 15:16
 */
@RestController
@RequestMapping("flow")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class FlowController extends CommonFlow {

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private FlowImageFacade flowImageFacade;

    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    @Reference
    private PmsPermissionFacade permissionFacade;

    @Reference
    private ExportRecordFacade exportRecordFacade;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Permission("pms:flow:reply")
    @PostMapping("reply")
    public RestResult<?> reply(@RequestBody @Validated(IReply.class) CommonFlowEditVo commonFlowEditVo,@CurrentUser PmsOperator pmsOperator){
        //构建流程用户
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(pmsOperator.getId());
        flowUserVo.setUserName(pmsOperator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        flowFacade.reply(commonFlowEditVo,flowUserVo);
        return RestResult.success("回复成功");
    }

    @Permission("pms:flow:submit")
    @PostMapping("withDraw")
    public RestResult<?> withDraw(@RequestBody Map<String, Object> map, @CurrentUser PmsOperator pmsOperator) {
        Long commonFlowId = Long.valueOf(String.valueOf(map.get("commonFlowId")));
        String reason = (String) map.get("reason");
        boolean isAdmin = PmsOperatorTypeEnum.ADMIN.getValue() == pmsOperator.getType();
        //构建流程用户
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(pmsOperator.getId());
        flowUserVo.setUserName(pmsOperator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        flowFacade.deleteProcessInstance(commonFlowId, flowUserVo, isAdmin, reason);
        return RestResult.success("流程撤回成功");
    }

    /**
     * 获取流程数据
     *
     * @param commonFlowId
     * @return
     */
    @Permission("pms:flow:view")
    @GetMapping("getByCommonFlowId")
    public RestResult<CommonFlowVo> getByCommonFlowId(@RequestParam("commonFlowId") Long commonFlowId, @RequestParam(value = "taskId", required = false) String taskId,
                                                      @CurrentUser PmsOperator operator) {
        boolean isAdmin = PmsOperatorTypeEnum.ADMIN.getValue() == operator.getType();

        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(operator.getId());
        flowUserVo.setUserName(operator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        return RestResult.success(flowFacade.getCommonFlowById(commonFlowId, taskId, flowUserVo, isAdmin, PlatformSource.OPERATION.getValue()));
    }

    /**
     * 提交任务
     *
     * @param taskHandleVo
     * @param operator
     * @return
     */
    @Permission("pms:flow:submit")
    @PostMapping("submitTask")
    public RestResult<?> submitTask(@RequestBody TaskHandleVo taskHandleVo, @CurrentUser PmsOperator operator) {
        boolean isAdmin = PmsOperatorTypeEnum.ADMIN.getValue() == operator.getType();
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        flowUserVo.setUserId(operator.getId());
        flowUserVo.setUserName(operator.getRealName());
        flowFacade.executeTask(taskHandleVo, flowUserVo, isAdmin);
        return RestResult.success("任务提交完毕");
    }

    /**
     * 我收到的审批
     *
     * @param paramMap    过滤条件
     * @param pageVo      分页
     * @param pmsOperator 当前用户
     * @return
     */
    @Permission("pms:flow:view")
    @PostMapping("handleList")
    public RestResult<PageResult<List<Map<String,Object>>>> handleList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentUser PmsOperator pmsOperator) {
        boolean isAdmin = admin(pmsOperator, permissionFacade);
        PageResult<List<Map<String,Object>>> taskList = flowFacade.handleList(pmsOperator.getId(), PlatformSource.OPERATION.getValue(), isAdmin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 我发起的审批
     *
     * @param paramMap    过滤条件
     * @param pageVo      分页
     * @param pmsOperator 当前用户
     * @return
     */
    @Permission("pms:flow:view")
    @PostMapping("sendList")
    public RestResult<PageResult<List<Map<String,Object>>>> sendList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentUser PmsOperator pmsOperator) {
        boolean isAdmin = admin(pmsOperator, permissionFacade);
        PageResult<List<Map<String,Object>>> taskList = flowFacade.sendList(pmsOperator.getId(), PlatformSource.OPERATION.getValue(), isAdmin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 查询我的抄送
     * @param paramMap
     * @param pageVo
     * @param pmsOperator
     * @return
     */
    @PostMapping("carbonList")
    public RestResult<PageResult<List<Map<String,Object>>>> carbonList(@RequestBody Map<String,Object> paramMap,@RequestBody PageVo pageVo,@CurrentUser PmsOperator pmsOperator){
        PageResult<List<Map<String,Object>>> carbonList = flowFacade.carbonList(pmsOperator.getId(),PlatformSource.OPERATION.getValue(),paramMap,pageVo.toPageParam());
        return RestResult.success(carbonList);
    }

    /**
     * 我的待办
     *
     * @param paramMap    过滤条件
     * @param pageVo      分页
     * @param pmsOperator 当前用户
     * @return
     */
    @Permission("pms:flow:view")
    @PostMapping("todoList")
    public RestResult<PageResult<List<Map<String,Object>>>> todoList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentUser PmsOperator pmsOperator) {
        boolean isAdmin = PmsOperatorTypeEnum.ADMIN.getValue() == pmsOperator.getType();
        PageResult<List<Map<String,Object>>> taskList = flowFacade.todoList(getFlowUserVo(pmsOperator), isAdmin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 获取操作日志
     *
     * @param commonFlowId 流程通用id
     * @return
     */
    @Permission("pms:flow:view")
    @GetMapping("getDetail")
    public RestResult<List<CommonFlowLogVo>> getDetail(@RequestParam(name = "commonFlowId") Long commonFlowId) {
        List<CommonFlowLogVo> commonFlowLogList = flowFacade.getDetailByCommonFlowId(commonFlowId, PlatformSource.OPERATION.getValue());
        return RestResult.success(commonFlowLogList);
    }

    /**
     * 根据流程实例id获取流程追踪图
     *
     * @param processInstanceId 流程实例id
     * @return
     * @throws IOException
     */
    @Permission("pms:flow:image")
    @GetMapping("getInstanceImage")
    public RestResult<?> getInstanceImage(@RequestParam(name = "processInstanceId") String processInstanceId) throws IOException {
        String base64 = flowImageFacade.getInstanceImage(processInstanceId);
        return RestResult.success(base64);
    }

    @Permission("pms:flow:submit")
    @PostMapping("editBusinessVariable")
    public RestResult<?> editBusinessVariable(@RequestBody @Validated(IEditBusinessVariable.class) CommonFlowEditVo commonFlowEditVo, @CurrentUser PmsOperator pmsOperator) {
        boolean isAdmin = admin(pmsOperator, permissionFacade);
        flowFacade.editBusinessVariable(commonFlowEditVo, getFlowUserVo(pmsOperator), isAdmin);
        return RestResult.success("修改信息成功");
    }

    @Permission("pms:flow:submit")
    @PostMapping("transferTask")
    public RestResult<?> transferTask(@RequestBody @Validated(ITransferUser.class) CommonFlowEditVo commonFlowEditVo, @CurrentUser PmsOperator pmsOperator) {
        //获取变更人
        PmsOperator nextOperator = pmsOperatorFacade.getOperatorById(commonFlowEditVo.getNextUserId());
        if (nextOperator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户不存在，请选择其他用户");
        }
        FlowUserVo nextUser = new FlowUserVo();
        nextUser.setUserId(nextOperator.getId());
        nextUser.setPlatform(PlatformSource.OPERATION.getValue());
        nextUser.setUserName(nextOperator.getRealName());
        flowFacade.transferTask(commonFlowEditVo, getFlowUserVo(pmsOperator), nextUser);
        return RestResult.success("变更审批人成功");
    }

    @Logger(type = OperateLogTypeEnum.DELETE,name = "超级管理员删除工单")
    @PostMapping("deleteByCommonFlowId")
    public RestResult deleteByCommonFlowId(@RequestBody Map<String,Object> paramMap,@CurrentUser PmsOperator pmsOperator){
        boolean isAdmin = PmsOperatorTypeEnum.ADMIN.getValue() == pmsOperator.getType();
        if (!isAdmin){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无权限");
        }
        Long commonFlowId = ((Integer) paramMap.get("commonFlowId")).longValue();
        if (commonFlowId == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("流程id不能为空");
        }
        flowFacade.deleteByCommonFlowId(commonFlowId);
        return RestResult.success("流程删除成功");
    }

    private FlowUserVo getFlowUserVo(PmsOperator pmsOperator) {
        //当前用户
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(pmsOperator.getId());
        flowUserVo.setUserName(pmsOperator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        return flowUserVo;
    }

    /**
     * 导出工单列表
     * @param paramMap
     * @param pmsOperator
     * @return
     */
    @PostMapping("exportWorkOrder/{type}")
    public RestResult exportWorkOrder(@PathVariable("type") Integer type,@RequestBody Map<String,Object> paramMap,@CurrentUser PmsOperator pmsOperator){
        paramMap.put("exportFileType",FileTypeEnum.EXECL.getValue());
        ExportRecord record = getRecord(type,paramMap,pmsOperator);
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 一键导出附件
     * @param type
     * @param paramMap
     * @param pmsOperator
     * @return
     */
    @PostMapping("exportWorkOrderFile/{type}")
    public RestResult exportWorkOrderFile(@PathVariable("type") Integer type,@RequestBody Map<String,Object> paramMap,@CurrentUser PmsOperator pmsOperator){
        paramMap.put("exportFileType",FileTypeEnum.ZIP.getValue());
        ExportRecord record = getRecord(type,paramMap,pmsOperator);
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private ExportRecord getRecord(Integer type, Map<String, Object> paramMap,PmsOperator pmsOperator) {
        //固定类型为工单类型
        paramMap.put("workType",WorkTypeEnum.WORK_FLOW.getValue());
        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getEnum(type);
        switch (reportTypeEnum){
            case WORK_FLOW_TODO_EXPORT:
                getTodoParam(paramMap,pmsOperator);
                break;
            case WORK_FLOW_CARBON_EXPORT:
                getCarbonParam(paramMap,pmsOperator);
                break;
            case WORK_FLOW_HANDLE_EXPORT:
                getHandleParam(paramMap,pmsOperator);
                break;
            default:
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无法导出此类型");
        }
        ExportRecord record = ExportRecord.newDefaultInstance();
        String fileNo = exportRecordFacade.genFileNo();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(reportTypeEnum.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        return record;
    }

    private void getHandleParam(Map<String, Object> paramMap, PmsOperator pmsOperator) {
        paramMap.put("isAdmin",admin(pmsOperator, permissionFacade));
        paramMap.put("userId",pmsOperator.getId());
        paramMap.put("platformSource",PlatformSource.OPERATION.getValue());
    }

    private void getCarbonParam(Map<String, Object> paramMap,PmsOperator pmsOperator) {
        paramMap.put("userId",pmsOperator.getId());
        paramMap.put("platformSource",PlatformSource.OPERATION.getValue());
    }

    private void getTodoParam(Map<String, Object> paramMap,PmsOperator pmsOperator) {
        paramMap.put("isAdmin",PmsOperatorTypeEnum.ADMIN.getValue() == pmsOperator.getType());
        paramMap.put("flowUserVo",JsonUtil.toString(getFlowUserVo(pmsOperator)));
    }

}
