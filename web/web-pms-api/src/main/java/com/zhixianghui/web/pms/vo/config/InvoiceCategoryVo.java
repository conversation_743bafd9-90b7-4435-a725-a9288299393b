package com.zhixianghui.web.pms.vo.config;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2020/8/13
 **/
@Data
public class InvoiceCategoryVo {
    /**
     * 发票类目编码
     */
    @NotEmpty(message = "发票类目编码不能为空")
    @Length(max = 20, message = "发票类目编码长度不能超过20")
    private String invoiceCategoryCode;

    /**
     * 发票类目名称
     */
    @NotEmpty(message = "发票类目名称不能为空")
    @Length(max = 50, message = "发票类目名称长度不能超过20")
    private String invoiceCategoryName;

}
