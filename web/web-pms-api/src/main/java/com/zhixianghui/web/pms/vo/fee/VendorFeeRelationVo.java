package com.zhixianghui.web.pms.vo.fee;

import com.zhixianghui.common.util.validator.EnumValue;
import com.zhixianghui.web.pms.vo.common.SpecialFeeRuleVo;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class VendorFeeRelationVo extends BaseFeeRuleVo{
    private Long id;

    /**
     * 供应商编号
     */
    @NotEmpty(message = "供应商编号不能为空")
    private String vendorNo;

    /**
     * 供应商名称
     */
    @NotEmpty(message = "供应商名称不能为空")
    private String vendorName;

    /**
     * 描述
     */
    private String description;
}