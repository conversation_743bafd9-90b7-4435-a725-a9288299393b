package com.zhixianghui.web.pms.vo.trade.req;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 订单查询Vo
 * @date 2020-11-09 09:44
 **/
@Data
public class OrderQueryVo {

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 商户名称
     */
    private String employerNameLike;

    /**
     * 发放模式
     */
    private Integer launchWay;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 发放方式
     */
    private Integer channelType;

    /**
     * 批次状态
     */
    private Integer batchStatus;

    /**
     * 创建起始时间
     */
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    private Date createEndDate;

    /**
     * 完成起始时间
     */
    private Date completeBeginDate;

    /**
     * 完成截止时间
     */
    private Date completeEndDate;

    /**
     * 自由服务者编号
     */
    private String workCategoryCode;

    /**
     * 批次名
     */
    private String batchNameLike;
}
