package com.zhixianghui.web.pms.controller.fee;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.agent.AgentTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.fee.entity.AgentMonthBill;
import com.zhixianghui.facade.fee.enums.AgentMonthBillTypeEnum;
import com.zhixianghui.facade.fee.service.AgentMonthBillFacade;
import com.zhixianghui.facade.fee.vo.AgentMonthBillVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.fee.AgentMonthBillQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 合伙人月账单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-23
 */
@Slf4j
@RestController
@RequestMapping("agentMonthBill")
public class AgentMonthBillController {
    private static final Logger AGENT_MONTH_BILL_CHECK_LOGGER = LoggerFactory.getLogger("AGENT_MONTH_BILL_CHECK");
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentMonthBillController.class);

    @Reference
    private AgentMonthBillFacade agentMonthBillFacade;

    /**
     *
     * @param vo 带查询的条件
     * @return 合伙人月账单列表
     */
    @Permission("fee:agentMonthBill:list")
    @PostMapping("listPage")
    public RestResult<PageResult<List<AgentMonthBillVo>>> listPage(
            @RequestBody @Valid AgentMonthBillQueryVo vo
    ) {
        LOGGER.info("请求参数:" + JSONObject.toJSONString(vo));
        if (vo.getAgentType() == null) {
            vo.setAgentType(AgentTypeEnum.PERSON.getValue());
        }
        Map<String, Object> paramMap = BeanUtil.toMap(vo);

        PageResult<List<AgentMonthBillVo>> pageResult = agentMonthBillFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        LOGGER.info("查询数据:" + JSONArray.toJSON(pageResult.getData()));

        return RestResult.success(pageResult);
    }

    /**
     *
     * @param vo    待更新的字段
     * @param operator  当前登录的用户
     * @return  更新是否为成功
     */
    private static final int LIMIT_LENGTH = 50; // 字数限制
    @Permission("fee:agentMonthBill:check")
    @PostMapping("check")
    public RestResult<?> check(
            @RequestBody AgentMonthBillVo vo,
            @CurrentUser PmsOperator operator
    ) {
        if (CollectionUtils.isEmpty(vo.getIdList())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("id列表参数不合法...");
        }
        List<AgentMonthBill> agentMonthBill = agentMonthBillFacade.getBatchById(vo.getIdList());
        LimitUtil.notEmpty(agentMonthBill, "该记录不存在 : " + vo.getId());
        AGENT_MONTH_BILL_CHECK_LOGGER.info("操作员 : [{}]-[{}], 时间 : {}, 审核记录 : {}",
                operator.getId(), operator.getLoginName(), new Date(), vo.getId()
        );
        if (StringUtils.isNotBlank(vo.getNote())) {
            if (vo.getNote().length() > LIMIT_LENGTH) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("长度不能超过50");
            }
        }
        log.info("状态:" + vo.getSettlementStatus());
        if (vo.getSettlementStatus() != null) {
            checkStatus(agentMonthBill, vo.getSettlementStatus());
        }
        try {
            agentMonthBillFacade.check(vo.toAgentMonthBill(vo, agentMonthBill));
            return RestResult.success("成功");
        } catch (Exception e) {
            AGENT_MONTH_BILL_CHECK_LOGGER.error("[{}]账单状态[{}]更新失败", vo.getId(), vo.getSettlementStatus());
            return RestResult.error("审核失败...");

        }
    }

    /**
     * 待人工结算可改为已人工结算或不结算，已人工结算可改为结算失败，不结算、结算失败状态不可变更
     */
    private void checkStatus(List<AgentMonthBill> agentMonthBill, int expectStatus) {
        for (AgentMonthBill monthBill : agentMonthBill) {
            if (monthBill.getSettlementStatus() == expectStatus) {
                continue;
            }
            if (monthBill.getSettlementStatus() == AgentMonthBillTypeEnum.PENDING_MANUAL_SETTLEMENT.getStatus()) {
                if (expectStatus == AgentMonthBillTypeEnum.MANUALLY_SETTLED.getStatus() ||
                        expectStatus == AgentMonthBillTypeEnum.NO_SETTLEMENT.getStatus()) {
                    continue;
                }
            }
            if (monthBill.getSettlementStatus() == AgentMonthBillTypeEnum.MANUALLY_SETTLED.getStatus()) {
                if (expectStatus == AgentMonthBillTypeEnum.SETTLEMENT_FAILED.getStatus()) {
                    continue;
                }

            }
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前状态["+ AgentMonthBillTypeEnum.getTextByValue(monthBill.getSettlementStatus()) +"]不可变更为:" + AgentMonthBillTypeEnum.getTextByValue(expectStatus));
        }
    }

    @Permission("fee:agentMonthBill:export")
    @PostMapping("exportOrderItem")
    public RestResult<String> exportMonthBill(
            @RequestBody AgentMonthBillVo agentMonthBillVo,
            @CurrentUser PmsOperator operator
    ) {
        agentMonthBillVo.setOperateName(operator.getLoginName());
        agentMonthBillVo.setOperateTime(operator.getCurrLoginTime());
        agentMonthBillVo.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        try {
            agentMonthBillFacade.exportMonthBill(agentMonthBillVo);
            return RestResult.success("成功创建导出任务，请稍后查看");
        } catch (Exception e) {
            AGENT_MONTH_BILL_CHECK_LOGGER.error("[{}]导出任务失败 : {}", agentMonthBillVo.getBillDate(), e);
            return RestResult.error("任务创建失败...");
        }

    }

}
