package com.zhixianghui.web.pms.utils;

import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.entity.BaseCalculateEntity;
import com.zhixianghui.common.statics.enums.fee.FormulaEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.web.pms.vo.fee.BaseFeeRuleVo;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Objects;

/**
 * 计费规则工具类
 * <AUTHOR>
 * @date 2020/11/30
 **/
public class FeeRuleUtil {

    public static void checkFeeRule(BaseFeeRuleVo vo){
        if(Objects.equals(vo.getRuleType(), RuleTypeEnum.GENERAL.getValue())
                && vo.getPriority() != 1){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("通用规则优先级只能为1");
        }

        // 按比例收费
        if(vo.getFormulaType() == FormulaEnum.RATE.getValue()){
            LimitUtil.notEmpty(vo.getFeeRate(), "手续费费率不能为空");
        }
        // 按笔收费
        else if(vo.getFormulaType() == FormulaEnum.FIXED.getValue()){
            LimitUtil.notEmpty(vo.getFixedFee(), "固定手续费不能为空");

        }
        // 按笔+比例收费
        else if(vo.getFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()){
            LimitUtil.notEmpty(vo.getFixedFee(), "固定手续费不能为空");
            LimitUtil.notEmpty(vo.getFeeRate(), "手续费费率不能为空");
        }

        if(vo.getRuleType() == RuleTypeEnum.SPECIAL.getValue()){
            LimitUtil.notEmpty(vo.getRuleParam(), "特殊规则不能为空");
        }
    }
    public static void fillFeeRule(BaseCalculateEntity entity, BaseFeeRuleVo vo){
        entity.setRuleType(vo.getRuleType());
        entity.setChargeType(vo.getChargeType());
        entity.setMaxFee(vo.getMaxFee());
        entity.setFormulaType(vo.getFormulaType());
        entity.setMinFee(vo.getMinFee());
        entity.setPriority(vo.getPriority());
        // 按比例收费
        if(vo.getFormulaType() == FormulaEnum.RATE.getValue()){
            entity.setFixedFee(null);
            entity.setFeeRate(AmountUtil.div(vo.getFeeRate(), new BigDecimal(100)));
        }
        // 按笔收费
        else if(vo.getFormulaType() == FormulaEnum.FIXED.getValue()){
            entity.setFixedFee(vo.getFixedFee());
            entity.setFeeRate(null);
        }
        // 按笔+比例收费
        else if(vo.getFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()){
            entity.setFixedFee(vo.getFixedFee());
            entity.setFeeRate(AmountUtil.div(vo.getFeeRate(), new BigDecimal(100)));
        }

        // 特殊计费设置特殊匹配规则
        entity.setSpecialFeeRuleList(new ArrayList<>());
        if(vo.getRuleType() == RuleTypeEnum.SPECIAL.getValue() && CollectionUtils.isNotEmpty(vo.getRuleParam())){
            vo.getRuleParam().stream().forEach(x -> {
                SpecialRuleDto specialRuleDto = new SpecialRuleDto();
                specialRuleDto.setSpecialRuleType(x.getSpecialRuleType());
                specialRuleDto.setCompareType(x.getCompareType());
                specialRuleDto.setValue(x.getValue());
                entity.getSpecialFeeRuleList().add(specialRuleDto);
            });
        }
    }
}
