package com.zhixianghui.web.pms.controller.notification;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.notification.NotificationReceiverTypeEnum;
import com.zhixianghui.common.statics.enums.notification.PublishStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.dto.NotificationRecordDto;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import com.zhixianghui.facade.common.service.NotificationFacade;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("notification")
public class NotificationController {

    @Reference
    private NotificationFacade notificationFacade;

    @PostMapping("createNotification")
    public RestResult<String> createNotification(@Validated @RequestBody NotificationRecordDto notificationRecordDto) {
        this.notificationFacade.createNotification(notificationRecordDto);
        return RestResult.success("添加成功");
    }

    @PostMapping("listNotifications")
    public RestResult<IPage<NotificationRecord>> listNotifications(@RequestBody Page<Map<String, Object>> page, @RequestBody NotificationRecordDto notificationRecordDto) {
        final Map<String, Object> paramMap = BeanUtil.toMap(notificationRecordDto);
        if (StringUtils.isNotBlank(notificationRecordDto.getNotificationReceivers())) {
            if (notificationRecordDto.getNotificationReceivers().startsWith("M")) {
                paramMap.put("notificationReceiverTypeSub", NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode());
            } else if (notificationRecordDto.getNotificationReceivers().startsWith("S")) {
                paramMap.put("notificationReceiverTypeSub", NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode());
            }
        }
        final IPage<NotificationRecord> notifications = this.notificationFacade.listNotifications(page, paramMap);
        return RestResult.success(notifications);
    }

    @GetMapping("notificationInfoById")
    public RestResult<NotificationRecord> getNotificationInfoById(@RequestParam Long id) {
        return RestResult.success(this.notificationFacade.getNotificationInfoById(id));
    }

    @PostMapping("delete")
    public RestResult<String> deleteNotifications(@RequestParam List<Long> ids) {
        this.notificationFacade.deletNotificationByIds(ids);
        return RestResult.success("操作成功");
    }

    @PostMapping("update")
    public RestResult<NotificationRecord> update(@RequestBody NotificationRecord notificationRecord) {
        final NotificationRecord record = this.notificationFacade.getNotificationInfoById(notificationRecord.getId());
        if (record.getPublishStatus().intValue() == PublishStatusEnum.PUBLISHED.getCode().intValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前消息状态无法修改消息内容");
        }
        BeanUtil.copyProperties(notificationRecord,record);
        this.notificationFacade.updateNotification(record);
        return RestResult.success(record);
    }
}
