package com.zhixianghui.web.pms.biz.report;

import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.facade.banklink.service.report.ChannelReportFacade;
import com.zhixianghui.facade.banklink.vo.report.AltMchSignVo;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.ReportChannelRecord;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.common.service.ReportFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.vo.report.req.ReportModifyVo;
import com.zhixianghui.web.pms.vo.report.req.ReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

@Service
@Slf4j
public class ReportBiz {

    @Reference(retries = -1)
    private ReportFacade reportFacade;
    @Reference
    private ChannelReportFacade channelReportFacade;

    @Async
    public void report(ReportVo reportVo, PmsOperator currentOperator) {
        ReportEntity reportEntity = new ReportEntity();
        BeanUtils.copyProperties(reportVo,reportEntity);
        reportEntity.setReporter(currentOperator.getRealName());
        reportFacade.report(reportEntity);
    }

    public void unsign(ReportVo reportVo) {
        ReportEntity reportEntity = new ReportEntity();
        BeanUtils.copyProperties(reportVo,reportEntity);
        reportFacade.unsign(reportEntity);
    }

    public void modify(ReportModifyVo reportModifyVo,PmsOperator currentOperator) {
        ReportEntity reportEntity = new ReportEntity();
        BeanUtils.copyProperties(reportModifyVo,reportEntity);
        reportEntity.setReporter(currentOperator.getRealName());
        reportFacade.modify(reportEntity);
    }

    public void uploadPic(ReportModifyVo reportVo) {
        ReportEntity reportEntity = new ReportEntity();
        BeanUtils.copyProperties(reportVo,reportEntity);
        reportFacade.uploadPic(reportEntity);
    }

    public Map<String, Map<String,String>> cheackMchSignAndPicUpload(EmployerAccountInfo accountInfo){
        AltMchSignVo altMchSignVo = new AltMchSignVo();
        altMchSignVo.setChannelMchNo(accountInfo.getParentMerchantNo());
        altMchSignVo.setAltMchNo(accountInfo.getSubMerchantNo());
        altMchSignVo.setChannelNo(ChannelNoEnum.JOINPAY.name());


        return channelReportFacade.cheackMchSignAndPicUpload(altMchSignVo);
    }
}
