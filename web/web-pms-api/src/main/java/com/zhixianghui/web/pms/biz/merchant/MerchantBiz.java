package com.zhixianghui.web.pms.biz.merchant;

import com.zhixianghui.common.statics.enums.alipay.AlipaySiteTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.service.employer.EmployerFunctionFacade;
import com.zhixianghui.facade.merchant.vo.IFlexible;
import com.zhixianghui.facade.merchant.vo.IPAY;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerAddVoV2;
import com.zhixianghui.web.pms.biz.CommonBiz;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * 商户
 * <AUTHOR>
 * @date 2020/9/7
 **/
@Component
@Validated
public class MerchantBiz extends CommonBiz {
    @Reference
    private EmployerFunctionFacade employerFunctionFacade;

    @Override
    @SuppressWarnings("unchecked")
    public <V> void saveFunction(List<V> list) {
        employerFunctionFacade.saveFunction((List<EmployerFunction>) list);
    }

    @Validated({IFlexible.class})
    public void validateFlexible(@Valid MerchantEmployerAddVoV2 v2) {
    }

    @Validated({IPAY.class})
    public void validatePay(@Valid MerchantEmployerAddVoV2 v2) {
        for (String s : v2.getServiceType()) {
            AlipaySiteTypeEnum typeEnum = AlipaySiteTypeEnum.getEnum(s);
            switch (typeEnum){
                case MINI_APPS:
                    if (StringUtils.isBlank(v2.getAlipayAppName())){
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付宝小程序名称不能为空");
                    }
                    break;
                case APP:
                    if (StringUtils.isBlank(v2.getAppName())){
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("app名称不能为空");
                    }
                    break;
                case WAP_PAY:
                    if (StringUtils.isBlank(v2.getWapName()) || StringUtils.isBlank(v2.getWapSite())){
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("wap站点信息不能为空");
                    }
                    break;
                case PC_PAY:
                    if (StringUtils.isBlank(v2.getPcName()) || StringUtils.isBlank(v2.getPcSite())){
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("pc站点信息不能为空");
                    }
            }
        }
    }
}
