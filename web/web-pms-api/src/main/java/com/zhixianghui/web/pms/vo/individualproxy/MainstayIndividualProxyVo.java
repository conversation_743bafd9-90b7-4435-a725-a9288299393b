package com.zhixianghui.web.pms.vo.individualproxy;

import com.zhixianghui.facade.common.entity.individualproxy.AreaItem;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
    * 代开供应商管理表
    */
@Data
public class MainstayIndividualProxyVo implements Serializable {
    private static final long serialVersionUID = -8098782901227184919L;
    private Integer id;

    /**
     * 代征主体编号
     */
    @NotBlank(message = "代征主体编号不能为空")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @NotBlank(message = "代征主体名称不能为空")
    private String mainstayName;


    /**
     * 服务费比例
     */
    @NotNull(message = "服务费比例不能为空")
    private BigDecimal serviceFeeRatio = BigDecimal.ZERO;

    /**
     * 单笔开票金额最低
     */
    private BigDecimal singleLimitMin;

    /**
     * 单笔开票金额最大
     */
    private BigDecimal singleLimitMax;

    /**
     * 累计最低开票金额
     */
    private BigDecimal accumulativeLimitMin;

    /**
     * 累计最高开票金额
     */
    private BigDecimal accumulativeLimitMax;

    /**
     * 激活的类型 0：单笔金额 1 累计金额 2 兼有
     */
    private Integer activeLimitType;

    /**
     * 年龄限制-最低
     */
    private Integer ageLimitMin;

    /**
     * 年龄限制-最高
     */
    private Integer ageLimitMax;

    /**
     * 是否限制年龄 0 不限 1 限制
     */
    private Boolean acitiveAgeLimit = Boolean.TRUE;

    /**
     * 十万以下是否免征
     */
    private Boolean levyLessThan10w = Boolean.TRUE;

    /**
     * 业务凭证要求
     */
    @NotNull(message = "业务凭证要求不能为空")
    private List<Integer> requiedBizVouchers;

    /**
     * 是否地域限制
     */
    private Boolean areaLimitStatus;

    /**
     * 限制开票地域
     */
    private List<AreaItem> areaNotSurport;

    /**
     * 特殊说明
     */
    private String specialRemark;


    /**
     * 状态
     */
    private Boolean status = Boolean.TRUE;

}
