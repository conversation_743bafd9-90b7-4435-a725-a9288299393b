package com.zhixianghui.web.pms.vo.approval.res;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.FlowStatus;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 审批流列表展示vo
 * @date 2020-08-17 12:40
 **/
@Data
public class ApprovalListResVo {

    private Long id;
    /**
     * 发起人
     */
    private Long initiatorId;

    /**
     * 发起人名称
     */
    private String initiatorName;

    /**
     * 流程主题(创建商户等)
     */
    private Integer flowTopicType;

    /**
     * 流程主题名称
     */
    private String flowTopicName;


    /**
     * 流程开始时间
     */
    private Date createTime;

    /**
     * 流程更新时间
     */
    private Date updateTime;

    /**
     * 流程结束时间
     */
    private Date endTime;

    /**
     * 流程状态 {@link FlowStatus}
     */
    private Integer status;

    /**
     * 流程耗时（秒）
     */
    private long spentTime;

    public static ApprovalListResVo buildVo(ApprovalFlow approvalFlow) {
        ApprovalListResVo approvalListResVo = new ApprovalListResVo();
        BeanUtils.copyProperties(approvalFlow, approvalListResVo);
        if(approvalFlow.getEndTime() != null){
            long spentTime = approvalFlow.getEndTime().getTime() - approvalFlow.getCreateTime().getTime();
            approvalListResVo.setSpentTime(spentTime/1000);
        }
        return approvalListResVo;
    }
}
