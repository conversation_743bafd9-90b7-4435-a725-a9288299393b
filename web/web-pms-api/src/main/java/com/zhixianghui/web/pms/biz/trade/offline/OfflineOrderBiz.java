package com.zhixianghui.web.pms.biz.trade.offline;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.OfflineOrderFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.trade.req.OrderQueryVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OfflineOrderBiz {

    @Reference
    private OfflineOrderFacade orderFacade;
    @Reference
    private OfflineOrderItemFacade orderItemFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    public PageResult<List<OrderResVo>> listOrderPage(OrderQueryVo orderQueryVo, PageVo pageVo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderQueryVo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        Page<OfflineOrder> pageResult = orderFacade.pageOrder(new Page<>(pageParam.getPageCurrent(),pageParam.getPageSize()),paramMap);
        List<OrderResVo> list = pageResult.getRecords().stream().map(
                order->{
                    OrderResVo orderResVo = new OrderResVo();
                    BeanUtils.copyProperties(order,orderResVo);
                    return orderResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list,Long.valueOf(pageResult.getCurrent()).intValue(),Long.valueOf(pageResult.getSize()).intValue(),pageResult.getTotal());
    }

    public void acceptAgain(String platBatchNo) {
        OfflineOrder order = orderFacade.getByPlatBatchNo(platBatchNo);
        if(order == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("批次订单不存在");
        }
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.IMPORTING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次订单不是受理中,不允许重新受理操作");
        }
        orderFacade.startAccept(order);
    }
}
