package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.enums.fee.ChargeTypeEnum;
import com.zhixianghui.common.statics.enums.fee.FormulaEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.fee.entity.MerchantFeeRule;
import com.zhixianghui.facade.fee.service.MerchantFeeRuleFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.utils.FeeRuleUtil;
import com.zhixianghui.web.pms.vo.fee.MerchantFeeRelationQueryVo;
import com.zhixianghui.web.pms.vo.fee.MerchantFeeRelationVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

@RestController
@RequestMapping("mch_fee_manager")
@Slf4j
public class MerchantFeeRuleController extends BaseController {

    @Reference
    private MerchantFeeRuleFacade merchantFeeRuleFacade;

    /**
     * 查询
     * @param vo
     * @return
     */
    @Permission("fee:mchFeeManager:list")
    @PostMapping("list")
    public RestResult<PageResult<List<MerchantFeeRule>>> listMerchantFeeRelation(@RequestBody @Valid MerchantFeeRelationQueryVo vo) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        PageResult<List<MerchantFeeRule>> pageResult = merchantFeeRuleFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize(),"UPDATE_TIME DESC"));
        pageResult.getData().stream().forEach(x -> {
            if(x.getFeeRate() != null){
                x.setFeeRate(AmountUtil.mul(x.getFeeRate(), new BigDecimal(100)));
            }
        });
        return RestResult.success(pageResult);
    }

    /**
     * 删除
     * @param currentOperator
     * @param id
     * @return
     */
    @Permission("fee:mchFeeManager:delete")
    @PostMapping("delete/{id}")
    @Logger(type = OperateLogTypeEnum.DELETE, name = "删除产品计费")
    public RestResult<String> delete(@CurrentUser PmsOperator currentOperator, @PathVariable("id") long id) {
        merchantFeeRuleFacade.deleteById(id, currentOperator.getLoginName());
        return RestResult.success("删除操作成功");
    }

    /**
     * 编辑
     * @param currentOperator
     * @param vo
     * @return
     */
    @Permission("fee:mchFeeManager:edit")
    @PostMapping("edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "修改产品计费")
    public RestResult<String> edit(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid MerchantFeeRelationVo vo) {
        LimitUtil.notEmpty(vo.getId(), "id不能为空");

        FeeRuleUtil.checkFeeRule(vo);

        MerchantFeeRule relation = merchantFeeRuleFacade.getById(vo.getId());
        LimitUtil.notEmpty(relation, "产品计费不存在");

        relation.setMchNo(vo.getMchNo());
        relation.setMchName(vo.getMchName());
        relation.setProductNo(vo.getProductNo());
        relation.setProductName(vo.getProductName());
        relation.setDescription(vo.getDescription());
        relation.setUpdateBy(currentOperator.getLoginName());
        relation.setUpdateTime(new Date());

        FeeRuleUtil.fillFeeRule(relation, vo);

        merchantFeeRuleFacade.update(relation);

        return RestResult.success("修改数据成功");
    }

    /**
     * 添加
     * @param currentOperator
     * @param vo
     * @return
     */
    @Permission("fee:mchFeeManager:add")
    @PostMapping("add")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "添加产品计费")
    public RestResult<String> add(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid MerchantFeeRelationVo vo) {
        // 规则校验
        FeeRuleUtil.checkFeeRule(vo);

        MerchantFeeRule relation = new MerchantFeeRule();
        relation.setId(vo.getId());
        relation.setMchNo(vo.getMchNo());
        relation.setMchName(vo.getMchName());
        relation.setProductNo(vo.getProductNo());
        relation.setProductName(vo.getProductName());
        relation.setStatus(PublicStatus.ACTIVE);
        relation.setDescription(vo.getDescription());
        relation.setUpdateBy(currentOperator.getLoginName());
        relation.setUpdateTime(new Date());
        relation.setCreateBy(currentOperator.getLoginName());
        relation.setCreateTime(new Date());

        FeeRuleUtil.fillFeeRule(relation, vo);

        merchantFeeRuleFacade.addMerchantFeeRelation(relation);

        return RestResult.success("添加产品计费成功");
    }

    /**
     * 查看
     * @param id
     * @return
     */
    @Permission("fee:mchFeeManager:view")
    @GetMapping("view/{id}")
    public RestResult<MerchantFeeRule> view(@PathVariable long id) {
        MerchantFeeRule merchantFeeRule = merchantFeeRuleFacade.getById(id);
        if(merchantFeeRule.getFeeRate() != null){
            merchantFeeRule.setFeeRate(AmountUtil.mul(merchantFeeRule.getFeeRate(), new BigDecimal(100)));
        }
        return RestResult.success(merchantFeeRule);
    }
}
