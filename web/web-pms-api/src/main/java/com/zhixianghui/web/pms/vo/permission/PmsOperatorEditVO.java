package com.zhixianghui.web.pms.vo.permission;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class PmsOperatorEditVO {

    /**
     * id
     */
    @NotNull(message = "操作员id不能为空")
    private Long id;

    /**
     * 姓名
     */
    @NotEmpty(message = "姓名不能为空")
    private String realName;

    /**
     * 手机号
     */
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号不正确")
    private String mobileNo;

    /**
     * 描述
     */
    private String remark;

    /**
     * 所属部门id
     */
    private Long departmentId;

    /**
     * 分配角色id
     */
    private List<Long> roleIds;
}
