package com.zhixianghui.web.pms.biz.trade;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.RecordItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.enums.ReceiptOrderEnum;
import com.zhixianghui.facade.trade.service.OrderAsyncFacade;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.trade.req.OrderItemQueryVo;
import com.zhixianghui.web.pms.vo.trade.req.OrderQueryVo;
import com.zhixianghui.web.pms.vo.trade.req.RecordItemQueryVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderItemResVo;
import com.zhixianghui.web.pms.vo.trade.res.OrderResVo;
import com.zhixianghui.web.pms.vo.trade.res.RecordItemResVo;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-09 09:35
 **/
@Slf4j
@Service
public class OrderBiz {

    @Reference(retries = -1)
    private OrderFacade orderFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private RecordItemFacade recordItemFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference(retries = -1)
    private OrderAsyncFacade orderAsyncFacade;
    @Autowired
    private RedisClient redisClient;

    public PageResult<List<OrderResVo>> listOrderPage(OrderQueryVo orderQueryVo, PageParam pageParam) {
        PageResult<List<Order>> pageResult = orderFacade.listPage(BeanToMapUtil.beanToMap(orderQueryVo),pageParam);
        List<OrderResVo> orderResVoList = pageResult.getData().stream().map(
                order -> {
                    OrderResVo orderResVo = new OrderResVo();
                    BeanUtils.copyProperties(order,orderResVo);
                    //TODO 批次显示任务金额，由于数据无法修改，增加判断
                    if (StringUtils.isBlank(order.getProductNo()) ||
                            order.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
                        orderResVo.setRequestTaskAmount(order.getRequestNetAmount());
                        orderResVo.setAcceptedTaskAmount(order.getAcceptedNetAmount());
                    }
                    return orderResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public PageResult<List<OrderItemResVo>> listOrderItemPage(OrderItemQueryVo orderItemQueryVo, PageParam pageParam) {
        Map<String,Object> paramMap = BeanToMapUtil.beanToMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo, paramMap);
        PageResult<List<OrderItem>> pageResult = orderItemFacade.listPage(paramMap,pageParam);
        List<OrderItemResVo> orderItemResVoList = pageResult.getData().stream().map(
                orderItem -> {
                    OrderItemResVo orderItemResVo = new OrderItemResVo();
                    BeanUtils.copyProperties(orderItem,orderItemResVo);
                    orderItemResVo.setReceiveName(orderItem.getReceiveNameDesensitize());
                    orderItemResVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDesensitize());
                    orderItemResVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDesensitize());
                    orderItemResVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDesensitize());
                    orderItemResVo.setChannelNo(orderItem.getPayChannelNo());
                    orderItemResVo.setChannelTrxNo(orderItem.getChannelTrxNo());
                    return orderItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public PageResult<List<RecordItemResVo>> listRecordItemPage(RecordItemQueryVo recordItemQueryVo, PageParam pageParam) {
        PageResult<List<RecordItem>> pageResult = recordItemFacade.listPage(BeanToMapUtil.beanToMap(recordItemQueryVo),pageParam);
        List<RecordItemResVo> recordItemResVoList = pageResult.getData().stream().map(
                recordItem -> {
                    RecordItemResVo recordItemResVo = new RecordItemResVo();
                    BeanUtils.copyProperties(recordItem,recordItemResVo);
                    recordItemResVo.setReceiveName(recordItem.getReceiveNameDesensitize());
                    recordItemResVo.setReceiveAccountNo(recordItem.getReceiveAccountNoDesensitize());
                    return recordItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(recordItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }


    public void exportOrderItem(OrderItemQueryVo orderItemQueryVo, PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo,paramMap);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.TRADE_ORDER_ITEM_PMS.getFileName());
        record.setReportType(ReportTypeEnum.TRADE_ORDER_ITEM_PMS.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.TRADE_ORDER_ITEM_PMS.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
    }

    public void exportRecordItem(RecordItemQueryVo recordItemQueryVo, PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(recordItemQueryVo);
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.TRADE_RECORD_ITEM_PMS.getFileName());
        record.setReportType(ReportTypeEnum.TRADE_RECORD_ITEM_PMS.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.TRADE_RECORD_ITEM_PMS.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
    }

    public void exportRechargeRecord(Map<String,Object> paramMap, PmsOperator operator) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getFileName());
        record.setReportType(ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
    }

    private void handlerAmount(OrderItemQueryVo orderItemQueryVo, Map<String, Object> paramMap) {
        String amountMaxStr = orderItemQueryVo.getOrderItemNetAmountMax();
        String amountMinStr = orderItemQueryVo.getOrderItemNetAmountMin();
        if (StringUtil.isNotEmpty(amountMaxStr) && !ValidateUtil.isDoubleAnd2decimals(amountMaxStr)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最大金额必须是数字，且最多两位小数");
        }else {
            paramMap.remove("orderItemNetAmountMax");
        }
        if (StringUtil.isNotEmpty(amountMinStr) && !ValidateUtil.isDoubleAnd2decimals(amountMinStr)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最小金额必须是数字，且最多两位小数");
        }else {
            paramMap.remove("orderItemNetAmountMin");
        }

        if(StringUtil.isNotEmpty(amountMaxStr) && StringUtil.isNotEmpty(amountMinStr)){
            BigDecimal amountMax = new BigDecimal(amountMaxStr);
            BigDecimal amountMin = new BigDecimal(amountMinStr);
            if (amountMax.compareTo(amountMin) < 0) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最大金额不能小于最小金额");
            }
            paramMap.put("orderItemNetAmountMax", amountMax);
            paramMap.put("orderItemNetAmountMin", amountMin);
        }
    }

    public void acceptAgain(String platBatchNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        Order order = orderFacade.getOne(paramMap);
        if(order == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("批次订单不存在");
        }
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.IMPORTING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次订单不是受理中,不允许重新受理操作");
        }
        orderAsyncFacade.acceptAgain(order);
    }

    public void grantAgain(String platBatchNo) {
        //只有批次是发放中(商户确定发放了)才能操作尝试重新发放
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        Order order = orderFacade.getOne(paramMap);
        if(order == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("批次订单不存在");
        }
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.GRANTING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次订单不是发放中,不允许重新发放操作");
        }
        orderAsyncFacade.grantAgain(order);
    }

    public String reverseQuery(String remitPlatTrxNo) {
        return recordItemFacade.reverseQuery(remitPlatTrxNo);
    }

    public Long countOrder(OrderQueryVo orderQueryVo) {
        return orderFacade.countOrder(BeanToMapUtil.beanToMap(orderQueryVo));
    }

    public Long countOrderItem(OrderItemQueryVo orderItemQueryVo) {
        Map<String,Object> paramMap = BeanToMapUtil.beanToMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo, paramMap);
        return orderItemFacade.countOrderItem(paramMap);
    }

    public Long countRecordItem(RecordItemQueryVo recordItemQueryVo) {
        return recordItemFacade.countRecordItem(BeanToMapUtil.beanToMap(recordItemQueryVo));
    }

    public void refundLocalFrozenAmount(String platTrxNo) {
        recordItemFacade.refundLocalFrozenAmount(platTrxNo);
    }

    public OrderItemSumBo sumOrderItem(OrderItemQueryVo orderItemQueryVo) {
        Map<String,Object> paramMap = BeanToMapUtil.beanToMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo, paramMap);
        return orderItemFacade.sumOrderItem(paramMap);
    }

    public OrderItem getItemByPlatTrxNo(String platTrxNo) {
        final OrderItem byPlatTrxNo = orderItemFacade.getByPlatTrxNo(platTrxNo);
        return byPlatTrxNo;
    }

    public void putOrderExpireCache(OrderQueryVo orderQueryVo) {
        Long total = orderFacade.countOrder(BeanToMapUtil.beanToMap(orderQueryVo));
        int totalPage = (int) (total / 100 + 1);
        for (int i = 1; i <= totalPage; i++){
            PageParam pageParam = PageParam.newInstance(i,100);
            pageParam.setIsNeedTotalRecord(false);
            PageResult<List<Order>> pageResult = orderFacade.listPage(BeanToMapUtil.beanToMap(orderQueryVo),pageParam);
            pageResult.getData().stream().forEach(order -> {
                if (order.getBatchStatus() == OrderStatusEnum.PENDING_GRANT.getValue() ||
                order.getBatchStatus() == OrderStatusEnum.IMPORTING.getValue()){
                    Date expireTime = DateUtil.parseJodaDateTime(order.getCreateTime()).plusHours(7*24).toDate();
                    redisClient.zadd(TradeConstant.ORDER_BATCH_ZSET_KEY,expireTime.getTime(),order.getMchBatchNo());
                }

            });
        }
    }

    public void exportReceipt(Integer type, OrderItemQueryVo orderItemQueryVo, PmsOperator pmsOperator) {
        ReportTypeEnum reportTypeEnum = ReceiptOrderEnum.getEnum(type).getReportTypeEnum();
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(reportTypeEnum.getDataName());
        if(dataDictionary == null){
            log.error("运营后台转账回单导出字典未配置");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }

    public void exportRechargeRecordZip(Map<String, Object> paramMap, PmsOperator operator) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName("批量下载回单");
        record.setReportType(ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        exportRecordFacade.insert(record);
    }
}
