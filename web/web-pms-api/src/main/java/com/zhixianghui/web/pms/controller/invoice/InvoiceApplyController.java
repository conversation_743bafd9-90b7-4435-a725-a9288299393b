package com.zhixianghui.web.pms.controller.invoice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.invoice.AccountHandleStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.ApplyTypeEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoicePreStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.InvoiceBookingConfigFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantExpressInfoFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantInvoiceInfoFacade;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.InvoiceCategoryEnum;
import com.zhixianghui.facade.trade.enums.InvoiceSourceEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.InvoicePreFacade;
import com.zhixianghui.facade.trade.service.InvoiceRecordDetailFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.employer.vo.order.res.MainstayResVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.invoice.ApplyInvoiceVo;
import com.zhixianghui.web.pms.vo.invoice.PreCheckApplyInvoiceResp;
import com.zhixianghui.web.pms.vo.invoice.PreCheckApplyInvoiceVo;
import com.zhixianghui.web.pms.vo.invoice.QueryWaitIssueOrderVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * 发票业务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("invoiceApply")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class InvoiceApplyController {
    @Reference
    private InvoiceFacade invoiceFacade;
    @Reference
    private MerchantInvoiceInfoFacade merchantInvoiceInfoFacade;
    @Reference
    private MerchantExpressInfoFacade merchantExpressInfoFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference(timeout = 120000)
    private OrderFacade orderFacade;
    @Reference
    private MerchantEmployerPositionFacade positionFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private MerchantEmployerQuoteFacade merchantEmployerQuoteFacade;
    @Reference
    private InvoiceRecordDetailFacade invoiceRecordDetailFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private InvoiceBookingConfigFacade bookingConfigFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Autowired
    private RedisClient redisClient;
    @Reference
    private MerchantEmployerPositionFacade employerPositionFacade;
    @Reference
    private OfflineOrderItemFacade offlineOrderItemFacade;
    @Reference
    private OfflineOrderFacade offlineOrderFacade;
    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private VendorFacade vendorFacade;
    @Reference
    private MerchantEmployerPositionFacade merchantEmployerPositionFacade;
    @Reference
    private InvoicePreFacade invoicePreFacade;

    /**
     * 获取交易时间段起始时间
     *
     * @param mainstayMchNo
     * @param mchNo
     * @return
     */
    @GetMapping("getApplyTradeCompleteDayBegin")
    public RestResult<String> getApplyTradeCompleteDayBegin(@RequestParam String mainstayMchNo,
                                                            @RequestParam(required = false) Integer category,
                                                            @RequestParam(required = false) String jobId,
                                                            @RequestParam(required = false) Integer source,
                                                            @RequestParam(required = true) String workCategoryCode,
                                                            @RequestParam(required = true) String productNo,
                                                            @RequestParam(required = true) Integer amountType,
                                                            @RequestParam String mchNo) {
        LimitUtil.notEmpty(mainstayMchNo, "代征主体商户编号不能为空");
        String completeTimeBegin = invoiceFacade.getApplyTradeCompleteDayBegin(mchNo, mainstayMchNo, category, jobId,
                source, workCategoryCode, productNo, amountType);
        return RestResult.success(completeTimeBegin);
    }

    /**
     * 查询待开票批次订单
     *
     * @param queryVo
     * @return
     */
    @RequestMapping("listWaitIssueOrderPage")
    public RestResult<PageResult<List<Order>>> listWaitIssueOrderPage(@Valid @RequestBody QueryWaitIssueOrderVo queryVo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(PageResult.newInstance(PageParam.newInstance(0, 0), new ArrayList<>()));
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", queryVo.getEmployerNo());
        paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
        paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
        paramMap.put("productNo", queryVo.getProductNo());
        paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
        paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
        paramMap.put("jobId", queryVo.getJobId());
        paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
        paramMap.put("ignoreZeroAmt", "true");
        paramMap.put("amountType", queryVo.getAmountType());
        queryVo.getPageParam().setIsNeedTotalRecord(false);

        PageResult<List<Order>> pageResult;
        if (queryVo.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
             pageResult = orderFacade.listPage(paramMap, queryVo.getPageParam());
        }else {
            final Page<OfflineOrder> pageOrder = offlineOrderFacade.pageOrder(new Page<>(queryVo.getPageCurrent(), queryVo.getPageSize()), paramMap);

            final List<OfflineOrder> records = pageOrder.getRecords();
            List<Order> orderList = new ArrayList<>();
            if (records != null) {
                for (OfflineOrder record : records) {
                    Order order = new Order();
                    BeanUtil.copyProperties(record, order);
                    orderList.add(order);
                }
            }

            pageResult = PageResult.newInstance(orderList, Long.valueOf(pageOrder.getCurrent()).intValue(), Long.valueOf(pageOrder.getSize()).intValue());
        }

        return RestResult.success(pageResult);
    }

    /**
     * 查询待开票详情订单
     *
     * @param queryVo
     * @return
     */
    @RequestMapping("listWaitIssueOrderItemPage")
    public RestResult<PageResult<List<OrderItem>>> listWaitIssueOrderItemPage(@Valid @RequestBody QueryWaitIssueOrderVo queryVo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(PageResult.newInstance(PageParam.newInstance(0, 0), new ArrayList<>()));
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", queryVo.getEmployerNo());
        paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
        paramMap.put("productNo", ProductNoEnum.CKH.getValue());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
        paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
        paramMap.put("jobId", queryVo.getJobId());
        paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
        queryVo.getPageParam().setIsNeedTotalRecord(false);

        PageResult<List<OrderItem>> pageResult;
        if (queryVo.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
            pageResult = orderItemFacade.listPage(paramMap, queryVo.getPageParam());
        }else {
            final PageResult<List<OfflineOrderItem>> pageOrder = offlineOrderItemFacade.listPage(paramMap, queryVo.getPageParam());

            final List<OfflineOrderItem> records = pageOrder.getData();
            List<OrderItem> orderList = new ArrayList<>();
            if (records != null) {
                for (OfflineOrderItem record : records) {
                    OrderItem order = new OrderItem();
                    BeanUtil.copyProperties(record, order);
                    orderList.add(order);
                }
            }

            pageResult = PageResult.newInstance(orderList, pageOrder.getPageCurrent(), pageOrder.getPageSize());
        }

        return RestResult.success(pageResult);
    }

    /**
     * 查询待开票金额
     *
     * @param queryVo
     * @return
     */
    @RequestMapping("getWaitIssueAmount")
    public RestResult<BigDecimal> getWaitIssueAmount(@Valid @RequestBody QueryWaitIssueOrderVo queryVo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(BigDecimal.ZERO);
        }
        BigDecimal total = BigDecimal.ZERO;
        Map<String, Object> paramMap = new HashMap<>();

        if (queryVo.getApplyType() == ApplyTypeEnum.GRANT_AMOUNT.getValue()) {
            paramMap.put("employerNo", queryVo.getEmployerNo());
            paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
            paramMap.put("productNo", queryVo.getProductNo());
            paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
            paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
            paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
            paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
            paramMap.put("ignoreZeroAmt", "true");
            // 加入创建时间以优化查询数组，创建时间范围为完成时间往前提3个月
            paramMap.put("createBeginDate", DateUtil.addMonth(DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"), -3));
            paramMap.put("createEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
            if (ProductNoEnum.ZXH.getValue().equals(queryVo.getProductNo())) {
                total = orderFacade.sumWaitInvoiceAmount(paramMap);
            } else if (ProductNoEnum.CEP.getValue().equals(queryVo.getProductNo())) {
                total = orderFacade.sumWaitCepInvoiceAmount(queryVo.getAmountType(), paramMap);
            }
        }

        return RestResult.success(total);
    }

    /**
     * 查询创客汇待开票金额
     *
     * @param queryVo
     * @return
     */
    @RequestMapping("sumWaitCkhInvoiceAmount")
    public RestResult<BigDecimal> sumWaitCkhInvoiceAmount(@Valid @RequestBody QueryWaitIssueOrderVo queryVo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(BigDecimal.ZERO);
        }
        BigDecimal total = BigDecimal.ZERO;
        Map<String, Object> paramMap = new HashMap<>();
        if (queryVo.getApplyType() == ApplyTypeEnum.GRANT_AMOUNT.getValue()) {
            paramMap.put("employerNo", queryVo.getEmployerNo());
            paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
            paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
            paramMap.put("productNo", ProductNoEnum.CKH.getValue());
            paramMap.put("jobId", queryVo.getJobId());
            paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
            paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
            paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
            paramMap.put("ignoreZeroAmt", "true");
            // 加入创建时间以优化查询数组，创建时间范围为完成时间往前提3个月
            paramMap.put("createBeginDate", DateUtil.addMonth(DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"), -3));
            paramMap.put("createEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));

            if (queryVo.getSource() == InvoiceSourceEnum.OFF_LINE.getCode()) {
                total = offlineOrderFacade.sumWaitInvoiceAmount(paramMap);
            }else {
                total = orderFacade.sumWaitCkhInvoiceAmount(paramMap);
            }
        }
        return RestResult.success(total);
    }

    /**
     * 查询待开票金额
     *
     * @param queryVo
     * @return
     */
    @RequestMapping("getOrderItemWaitIssueAmount")
    public RestResult<BigDecimal> getOrderItemWaitIssueAmount(@Valid @RequestBody QueryWaitIssueOrderVo queryVo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(BigDecimal.ZERO);
        }
        BigDecimal total = BigDecimal.ZERO;
        Map<String, Object> paramMap = new HashMap<>();
        if (queryVo.getApplyType() == ApplyTypeEnum.GRANT_AMOUNT.getValue()) {
            paramMap.put("employerNo", queryVo.getEmployerNo());
            paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
            paramMap.put("productNo", ProductNoEnum.CKH.getValue());
            paramMap.put("jobId", queryVo.getJobId());
            paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
            paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
            paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
            paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
            // 加入创建时间以优化查询数组，创建时间范围为完成时间往前提3个月
            paramMap.put("createBeginDate", DateUtil.addMonth(DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"), -3));
            paramMap.put("createEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));

            if (queryVo.getSource() == InvoiceSourceEnum.OFF_LINE.getCode()) {
                total = offlineOrderItemFacade.sumOrderItemWaitInvoiceAmount(paramMap);
            }else {
                total = orderItemFacade.sumOrderItemWaitInvoiceAmount(paramMap);
            }
        }
        return RestResult.success(total);
    }

    /**
     * 申请发票（预开票校验）
     *
     * @param vo
     * @return
     */
    @RequestMapping("preCheckApplyInvoice")
    @Permission("invoice:apply")
    public RestResult<PreCheckApplyInvoiceResp> applyInvoice(@Valid @RequestBody PreCheckApplyInvoiceVo vo) {

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerMchNo", vo.getEmployerMchNo());
        paramMap.put("mainstayMchNo", vo.getMainstayMchNo());
        paramMap.put("productNo", vo.getProductNo());
        paramMap.put("workCategoryCode", vo.getWorkCategoryCode());
        paramMap.put("invoiceStatus", InvoicePreStatusEnum.UN_FINNISH.getValue());
        Map<String, Object> result = invoicePreFacade.countInvoiceAmount(paramMap);
        return RestResult.success(PreCheckApplyInvoiceResp.builder().invoicePreAmount(new BigDecimal(result.get("invoiceAmount").toString())).build());
    }

    /**
     * 申请发票
     *
     * @param applyInvoiceVo
     * @return
     */
    @RequestMapping("applyInvoice")
    @Permission("invoice:apply")
    public RestResult<String> applyInvoice(@Valid @RequestBody ApplyInvoiceVo applyInvoiceVo, @CurrentUser PmsOperator pmsOperator) {
        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoFacade.getByMchNo(applyInvoiceVo.getEmployerNo());
        LimitUtil.notEmpty(merchantInvoiceInfo, "开票信息不存在");
        MerchantExpressInfo merchantExpressInfo = merchantExpressInfoFacade.getByMchNo(applyInvoiceVo.getEmployerNo());

        String CKH_INVOICE_MARJOR_MIN = "2000";
        final String marjorMin = dataDictionaryFacade.getSystemConfig("CKH_INVOICE_MARJOR_MIN");
        if (marjorMin != null) {
            CKH_INVOICE_MARJOR_MIN = marjorMin;
        }

        if (StringUtils.equals(applyInvoiceVo.getProductNo(), ProductNoEnum.CKH.getValue())
                && applyInvoiceVo.getInvoiceType().intValue() == InvoiceTypeEnum.MAJOR.getValue()
                && applyInvoiceVo.getInvoiceAmount().compareTo(new BigDecimal(CKH_INVOICE_MARJOR_MIN)) < 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创客汇服务费开票（专票）金额不能小于" + CKH_INVOICE_MARJOR_MIN + "元");
        }

        String trxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getWidth());
        InvoiceRecord record = fillInvoiceRecord(applyInvoiceVo, merchantInvoiceInfo, merchantExpressInfo, trxNo);

        if (StringUtils.equals(applyInvoiceVo.getProductNo(), ProductNoEnum.CKH.getValue())) {
            //如果是创客汇产品，则填充categry字段
            record.setCategory(InvoiceCategoryEnum.SERVICE_FEE_INVOICE.getCode());
        }

        invoiceFacade.applyInvoice(record, applyInvoiceVo.getJobId());
        return RestResult.success("开票申请成功");
    }

    /**
     * 申请发票
     *
     * @param applyInvoiceVo
     * @return
     */
    @RequestMapping("applyItemInvoice")
    @Permission("invoice:apply")
    public RestResult<String> applyItemInvoice(@Valid @RequestBody ApplyInvoiceVo applyInvoiceVo, @CurrentUser PmsOperator pmsOperator) {
        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoFacade.getByMchNo(applyInvoiceVo.getEmployerNo());
        LimitUtil.notEmpty(merchantInvoiceInfo, "开票信息不存在");
        MerchantExpressInfo merchantExpressInfo = merchantExpressInfoFacade.getByMchNo(applyInvoiceVo.getEmployerNo());

        String trxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getWidth());
        InvoiceRecord record = fillInvoiceRecord(applyInvoiceVo, merchantInvoiceInfo, merchantExpressInfo, trxNo);
        record.setCategory(InvoiceCategoryEnum.NATURE_PERSON_INVOICE.getCode());
        invoiceFacade.applyInvoiceOrderItem(record, applyInvoiceVo.getJobId());
        return RestResult.success("开票申请成功");
    }

    private InvoiceRecord fillInvoiceRecord(ApplyInvoiceVo applyInvoiceVo,
                                            MerchantInvoiceInfo merchantInvoiceInfo,
                                            MerchantExpressInfo merchantExpressInfo,
                                            String trxNo) {
        InvoiceRecord record = new InvoiceRecord();
        record.setUpdateTime(new Date());
        record.setProductNo(applyInvoiceVo.getProductNo());
        record.setProductName(applyInvoiceVo.getProductName());
        record.setEmployerMchNo(merchantInvoiceInfo.getMchNo());
        record.setEmployerMchName(merchantInvoiceInfo.getMchName());
        record.setMainstayMchNo(applyInvoiceVo.getMainstayMchNo());
        record.setMainstayMchName(applyInvoiceVo.getMainstayMchName());
        record.setTrxNo(trxNo);
        record.setInvoiceType(applyInvoiceVo.getInvoiceType());
        record.setApplyType(applyInvoiceVo.getApplyType());
        record.setAmountType(applyInvoiceVo.getAmountType());
        record.setInvoiceAmount(applyInvoiceVo.getInvoiceAmount());
        record.setTradeCompleteDayBegin(applyInvoiceVo.getTradeCompleteDayBegin());
        record.setTradeCompleteDayEnd(applyInvoiceVo.getTradeCompleteDayEnd());
        record.setInvoiceCategoryCode(applyInvoiceVo.getInvoiceCategoryCode());
        record.setInvoiceCategoryName(applyInvoiceVo.getInvoiceCategoryName());
        record.setInvoiceStatus(InvoiceStatusEnum.WAIT_ISSUE.getValue());
        record.setAccountHandleStatus(AccountHandleStatusEnum.UN_HANDLE.getValue());
        record.setExpressConsignee(merchantExpressInfo != null ? merchantExpressInfo.getConsignee() : "");
        record.setExpressTelephone(merchantExpressInfo != null ? merchantExpressInfo.getTelephone() : "");
        record.setProvince(merchantExpressInfo != null ? merchantExpressInfo.getProvince() : "");
        record.setCity(merchantExpressInfo != null ? merchantExpressInfo.getCity() : "");
        record.setCounty(merchantExpressInfo != null ? merchantExpressInfo.getCounty() : "");
        record.setAddress(merchantExpressInfo != null ? merchantExpressInfo.getAddress() : "");
        record.setTaxPayerType(merchantInvoiceInfo.getTaxPayerType());
        record.setTaxNo(merchantInvoiceInfo.getTaxNo());
        record.setRegisterAddrInfo(merchantInvoiceInfo.getRegisterAddrInfo());
        record.setAccountNo(merchantInvoiceInfo.getAccountNo());
        record.setBankName(merchantInvoiceInfo.getBankName());
        record.setRemark(applyInvoiceVo.getRemark());
        record.setVersion(0);
        record.setCreateTime(new Date());
        record.setWorkCategoryCode(applyInvoiceVo.getWorkCategoryCode());
        record.setWorkCategoryName(applyInvoiceVo.getWorkCategoryName());
        record.setSource(applyInvoiceVo.getSource() == null ? InvoiceSourceEnum.ON_LINE.getCode() : applyInvoiceVo.getSource());
        return record;
    }

    /**
     * 根据产品和商户查询供应商
     *
     * @param status    状态
     * @param productNo 产品编号
     * @return
     */
    @GetMapping("listOpenMainstayByEmployerNoAndProduct")
    public RestResult<List<MainstayResVo>> listOpenMainstayByEmployerNoAndProduct(@RequestParam(required = true) Integer status, @RequestParam(required = false) String productNo,@RequestParam String employerNo) {
            Map<String,Object> paramMap = Maps.newHashMap();
            paramMap.put("employerNo",employerNo );
            if (status != null) {
                paramMap.put("status", status);
            }
            List<EmployerMainstayRelation> list = employerMainstayRelationFacade.listBy(paramMap);
        final List<MainstayResVo> collect = list.stream().filter(it -> {
            final Vendor vendor = vendorFacade.getVendorByNo(it.getMainstayNo());
            return StringUtils.equals(vendor.getProductNo(), productNo);
        }).map(
                rel -> {
                    final Vendor vendor = vendorFacade.getVendorByNo(rel.getMainstayNo());
                    MainstayResVo mainstayResVo = new MainstayResVo();
                    BeanUtils.copyProperties(rel, mainstayResVo);
                    mainstayResVo.setProductNo(vendor.getProductNo());
                    mainstayResVo.setProductName(vendor.getProductName());
                    return mainstayResVo;
                }
        ).collect(Collectors.toList());
        return RestResult.success(collect);
    }

    @RequestMapping("getInvoiceInfoWithMainstayNo")
    public RestResult getInvoiceInfoWithMainstayNo(@RequestParam String mchNo,@RequestParam String mainstayNo,@RequestParam(required = false) String workCategoryCode){
        MerchantInvoiceInfo invoiceInfo = merchantInvoiceInfoFacade.getByMchNo(mchNo);
        LimitUtil.notEmpty(invoiceInfo, "商户开票信息不存在");

        Map<String, Object> resultMap = BeanUtil.toMap(invoiceInfo);
        List<InvoiceCategoryVo> invoiceCategoryVoList = new ArrayList<>();
        // 获取所有绑定的发票类目信息
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("mchNo",mchNo);
        paramMap.put("workCategoryCode",workCategoryCode);
        paramMap.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        List<MerchantEmployerPosition> positionList = merchantEmployerPositionFacade.listByMchNoWithQuote(paramMap);
        if (CollectionUtils.isNotEmpty(positionList)) {
            positionList.forEach(x -> {
                invoiceCategoryVoList.addAll(x.getJsonEntity().getInvoiceCategoryList());
            });
        }
        // 去重
        List<InvoiceCategoryVo> uniqueInvoiceCategoryVoList = invoiceCategoryVoList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InvoiceCategoryVo::getInvoiceCategoryCode))), ArrayList::new)
        );
        resultMap.put("invoiceCategoryVoList", uniqueInvoiceCategoryVoList);
        return RestResult.success(resultMap);
    }
}
