package com.zhixianghui.web.pms.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.service.agent.AgentRoleFacade;
import com.zhixianghui.facade.merchant.service.agent.AgentStaffRoleFacade;
import com.zhixianghui.facade.merchant.vo.FunctionVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentRoleVo;
import com.zhixianghui.web.pms.vo.permission.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运营后台-合伙角色管理
 *
 */
@RestController
@RequestMapping("pmsAgentRole")
@Log4j2
public class PmsAgentRoleController {

    @Reference
    private AgentRoleFacade agentRoleFacade;
    @Reference
    private AgentStaffRoleFacade agentStaffRoleFacade;
    
    /**
     * 分页查询角色
     */
    @Permission("pms:role:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<AgentRoleVo>>> listPage(@RequestBody PmsRoleQueryVO vo) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", vo.getRoleName());
        PageResult<List<AgentRoleVo>> result = agentRoleFacade.listPage(map, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(result);
    }


    /**
     * 新增角色
     */
    @Permission("pms:role:add")
    @PostMapping("add")
    public RestResult<String> add(@Validated @RequestBody PmsRoleVO roleVO) {
        AgentRole role = roleVO.buildAgentRole(roleVO);
        agentRoleFacade.create(role);
        return RestResult.success("新增角色成功");
    }

    /**
     * 更新角色
     */
    @Permission("pms:role:edit")
    @PostMapping("edit")
    public RestResult<String> edit(@Validated @RequestBody PmsRoleEditVO roleVO) {
        if (roleVO.getId() == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("缺少角色id");
        }
        AgentRole role = agentRoleFacade.getById(roleVO.getId());
        if (role == null || RoleTypeEnum.PRESET.getType() != role.getRoleType()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }
        role.setRemark(roleVO.getRemark() == null ? "" : roleVO.getRemark());
        agentRoleFacade.update(role);
        return RestResult.success("更新角色成功");
    }

    /**
     * 删除角色
     */
    @Permission("pms:role:delete")
    @PostMapping("delete")
    public RestResult<String> delete(@RequestParam Long id) {
        check(id);
        agentRoleFacade.deleteById(id);
        return RestResult.success("删除角色成功");
    }

    /**
     * 查询角色关联的菜单
     * @param roleId    角色id
     */
    @Permission("pms:role:view")
    @GetMapping("listRoleFunction")
    public RestResult<List<FunctionVO>> listRoleFunction(@RequestParam Long roleId) {
        check(roleId);
        return RestResult.success(agentRoleFacade.listFunctionByRoleId(roleId)
                .stream().map(FunctionVO::buildVo).collect(Collectors.toList()));
    }

    public void check(Long roleId) {
        AgentRole role = agentRoleFacade.getById(roleId);
        if (role == null || RoleTypeEnum.PRESET.getType() != role.getRoleType()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("角色不存在");
        }
    }

    /**
     * 为角色分配功能
     */
    @Permission("pms:role:edit")
    @PostMapping("assignFunction")
    public RestResult<String> assignFunction(@RequestBody @Valid PmsRoleAssignFunctionVO vo) {
        check(vo.getRoleId());
        agentRoleFacade.updateFunction(vo.getRoleId(), vo.getFunctionIds() == null ? new ArrayList<>() : vo.getFunctionIds());
        return RestResult.success("操作成功");
    }
}
