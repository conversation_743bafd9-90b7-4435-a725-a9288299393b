package com.zhixianghui.web.pms.vo.individualproxy;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class DeleteIndividualProxyQuoteVo implements Serializable {
    private static final long serialVersionUID = -6544533033854225675L;
    @NotNull(message = "代征id不能为空")
    private Integer id;
    @NotNull(message = "invoiceCategoryCode不能为空")
    private String invoiceCategoryCode;
}
