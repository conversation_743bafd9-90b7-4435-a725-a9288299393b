package com.zhixianghui.web.pms.controller.report;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.dto.PayChannelDto;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.report.PayChannelBiz;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.report.req.PayChannelQueryVo;
import com.zhixianghui.web.pms.vo.report.req.PayChannelReqVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @description 支付通道Controller
 * @date 2020-09-28 11:09
 **/
@RestController
@RequestMapping("payChannel")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PayChannelController {

    private final PayChannelBiz payChannelBiz;

    /**
     * 创建支付通道
     * @param payChannelReqVo 支付通道信息
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.CREATE, name = "创建支付通道")
    @Permission("report:payChannel:add")
    @PostMapping("create")
    public RestResult<String> create(@Validated @RequestBody PayChannelReqVo payChannelReqVo, @CurrentUser PmsOperator currentOperator) {
        payChannelBiz.create(payChannelReqVo,currentOperator);
        return RestResult.success("创建成功");
    }

    /**
     * 变更支付通道状态
     * @param id 通道id
     * @param status 状态
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "变更支付通道状态")
    @Permission("report:payChannel:edit")
    @PostMapping("changeStatus")
    public RestResult<String> changeStatus(@RequestParam Long id, @RequestParam Integer status,@CurrentUser PmsOperator currentOperator) {
        payChannelBiz.changeStatus(id,status,currentOperator.getRealName());
        return RestResult.success("状态修改成功");
    }

    /**
     * 更新通道
     * @param payChannelReqVo 通道信息
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新通道")
    @Permission("report:payChannel:edit")
    @PostMapping("update")
    public RestResult<String> update(@Validated @RequestBody PayChannelReqVo payChannelReqVo, @CurrentUser PmsOperator currentOperator) {
        if(payChannelReqVo.getId() == null){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("id 不能为空");
        }
        payChannelBiz.update(payChannelReqVo,currentOperator);
        return RestResult.success("更新成功");
    }

    /**
     * 删除通道
     * @param id 通道id
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.DELETE, name = "删除通道")
    @Permission("report:payChannel:edit")
    @PostMapping("delete")
    public RestResult<String> delete(@RequestParam Long id) {
        payChannelBiz.delete(id);
        return RestResult.success("删除成功");
    }

    /**
     * 分页查询支付通道列表
     * @param queryVo 查询条件
     * @param pageVo 分页参数
     * @return 支付通道列表
     */
    @Permission({"report:payChannel:view","merchantEmployer:account:view"})
    @PostMapping("listPage")
    public RestResult<PageResult<List<PayChannelDto>>> listPage(@RequestBody PayChannelQueryVo queryVo,@RequestBody PageVo pageVo) {
        //PageResult<List<PayChannel>> pageResult = payChannelBiz.listPage(queryVo,pageVo.toPageParam("UPDATE_TIME DESC"));
        PageResult<List<PayChannelDto>> pageResult = payChannelBiz.listCustomPage(queryVo,pageVo.toPageParam());
        return RestResult.success(pageResult);
    }

    /**
     * 获取单个支付通道信息
     * @param id 通道id
     * @return 支付通道信息
     */
    @Permission("report:payChannel:view")
    @GetMapping("getById/{id}")
    public RestResult<PayChannel> getById(@PathVariable("id") Long id){
        return RestResult.success(payChannelBiz.getById(id));
    }
}
