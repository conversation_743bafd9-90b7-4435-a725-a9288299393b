package com.zhixianghui.web.pms.controller.report;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.report.ReportStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.entity.report.ReportChannelRecord;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.facade.common.service.ReportChannelRecordFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.report.ReportBiz;
import com.zhixianghui.web.pms.vo.report.req.ReportModifyVo;
import com.zhixianghui.web.pms.vo.report.req.ReportVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("report")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReportController {

    private final ReportBiz reportBiz;

    @Reference
    private AlipayFacade alipayFacade;

    @Reference
    private PayChannelFacade payChannelFacade;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    private ReportChannelRecordFacade reportChannelRecordFacade;

    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    @Logger(type = OperateLogTypeEnum.MODIFY,name = "上传分账方图片")
    @PostMapping("uploadPic")
    public RestResult<String> uploadPic(@Validated @RequestBody ReportModifyVo reportVo,@CurrentUser PmsOperator currentOperator){
        reportBiz.uploadPic(reportVo);
        return RestResult.success("分账方图片上传成功");
    }

    /**
     * 报备请求
     * @param reportVo 报备参数
     * @param currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "发起报备请求")
    @Permission("report:employerAccountInfo:edit")
    @PostMapping("report")
    public RestResult<String> report(@Validated @RequestBody ReportVo reportVo,@CurrentUser PmsOperator currentOperator) {

        Merchant merchant = null;
        if (reportVo.getMerchantType().intValue() == MerchantTypeEnum.EMPLOYER.getValue()){
            merchant = merchantQueryFacade.getByMchNo(reportVo.getEmployerNo());
        }else if (reportVo.getMerchantType().intValue() == MerchantTypeEnum.MAINSTAY.getValue()){
            merchant = merchantQueryFacade.getByMchNo(reportVo.getMainstayNo());
        }
        reportVo.setMchName(merchant.getMchName());
        reportVo.setEmployerName(merchant.getMchName());
        // 1. 参数校验
        if (reportVo == null){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数不能为空");
        }
        if (reportVo.getMerchantType() == MerchantTypeEnum.MAINSTAY.getValue()){
            //供应商报备，设置用工企业编号为空字符串
            reportVo.setEmployerNo("");
            reportVo.setEmployerName("");
            //
            //PayChannel payChannel = payChannelFacade.getByChannelNo(reportVo.getPayChannelNo());
//            reportVo.setChannelType(payChannel.getChannelType());
        }

        reportBiz.report(reportVo,currentOperator);
        return RestResult.success("报备已提交，请查看记录");
    }

    @Logger(type = OperateLogTypeEnum.MODIFY,name = "修改通道信息")
    @PostMapping("modify")
    public RestResult modify(@Validated @RequestBody ReportModifyVo reportModifyVo,@CurrentUser PmsOperator pmsOperator){
        reportBiz.modify(reportModifyVo, pmsOperator);
        return RestResult.success("已发起修改请求，请查看报备记录");
    }

    /**
     * 取消签约（仅作重复测试用，后续删除）
     * @return
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "解除签约")
    @PostMapping("unsign")
    public RestResult<String> unsign(@Validated @RequestBody ReportVo reportVo,@CurrentUser PmsOperator currentOperator) {
        reportBiz.unsign(reportVo);
        return RestResult.success("签约已解除");
    }

    @PostMapping("cheackMchSignAndPicUpload")
    public RestResult<Map<String, Map<String,String>>> cheackMchSignAndPicUpload(@RequestBody @Validated ReportModifyVo reportModifyVo){

        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelNo(
                reportModifyVo.getEmployerNo(), reportModifyVo.getMainstayNo(), ChannelNoEnum.JOINPAY.name());
        if (employerAccountInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道信息不存在或者未开通该通道");
        }
        return RestResult.success(reportBiz.cheackMchSignAndPicUpload(employerAccountInfo));
    }
}
