package com.zhixianghui.web.pms.vo.account;

import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票账户调账vo
 * <AUTHOR>
 */
@Data
public class AccountInvoiceAdjustVO implements Serializable {
    @NotEmpty(message = "账户编号不能为空")
    private String accountNo;

    @NotNull(message = "调账金额不能为空")
    @DecimalMin(value = "0", message = "调账金额不能小于0")
    private BigDecimal amount;

    @NotNull(message = "调账类型不能为空")
    @EnumValue(intValues = {4,5}, message = "调账类型有误")
    private Integer accountProcessType;

    @NotEmpty(message = "调账备注不能为空")
    private String remark;
}
