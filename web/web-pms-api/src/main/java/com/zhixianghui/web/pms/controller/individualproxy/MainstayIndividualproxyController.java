package com.zhixianghui.web.pms.controller.individualproxy;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.export.ExportStatusEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.individualproxy.IndividualProxyQuote;
import com.zhixianghui.facade.common.entity.individualproxy.MainstayIndividualProxy;
import com.zhixianghui.facade.common.service.MainstayIndividualProxyFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.controller.fix.helper.MultipartFileUtil;
import com.zhixianghui.web.pms.vo.individualproxy.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;

@RestController
@RequestMapping("individualproxy")
public class MainstayIndividualproxyController {

    @Reference
    private MainstayIndividualProxyFacade mainstayIndividualProxyFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Autowired
    private FastdfsClient fastdfsClient;


    @PostMapping("addIndividualProxyMainstay")
    public RestResult<MainstayIndividualProxy> addIndividualProxyMainstay(@Validated @RequestBody MainstayIndividualProxyVo mainstayIndividualProxyVo, @CurrentUser PmsOperator pmsOperator) {
        MainstayIndividualProxy proxy = new MainstayIndividualProxy();
        BeanUtil.copyProperties(mainstayIndividualProxyVo, proxy);

        proxy.setCreateTime(LocalDateTimeUtil.now());
        proxy.setUpdateTime(LocalDateTimeUtil.now());
        proxy.setCreateBy(pmsOperator.getLoginName());
        proxy.setUpdateBy(pmsOperator.getLoginName());
        final MainstayIndividualProxy individualProxy = mainstayIndividualProxyFacade.addIndividualProxyMainstay(proxy);
        return RestResult.success(individualProxy);
    }

    @PostMapping("updateIndividualProxyMainstay")
    public RestResult<MainstayIndividualProxy> updateIndividualProxyMainstay(@RequestBody MainstayIndividualProxyVo mainstayIndividualProxyVo, @CurrentUser PmsOperator pmsOperator) {
        MainstayIndividualProxy proxy = new MainstayIndividualProxy();
        BeanUtil.copyProperties(mainstayIndividualProxyVo, proxy);

        proxy.setUpdateTime(LocalDateTimeUtil.now());
        proxy.setUpdateBy(pmsOperator.getLoginName());
        final MainstayIndividualProxy individualProxy = mainstayIndividualProxyFacade.updateIndividualProxyMainstay(proxy);
        return RestResult.success(individualProxy);
    }

    @PostMapping("updateStatus")
    public RestResult<MainstayIndividualProxy> updateStatus(@Validated @RequestBody MainstayIndividualProxyUpdateVo vo) {
        final MainstayIndividualProxy proxy = mainstayIndividualProxyFacade.updateStatus(vo.getId(), vo.getStatus());
        return RestResult.success(proxy);
    }

    @PostMapping("addIndividualProxyQuote")
    public RestResult<MainstayIndividualProxy> addIndividualProxyQuote(@Validated @RequestBody AddIndividualProxyQuoteVo vo) {
        final IndividualProxyQuote individualProxyQuote = vo.getIndividualProxyQuote();
        Set<String> codeSet = new HashSet<>();
        if (vo.getParentCateCode() != null && StringUtils.equals(individualProxyQuote.getInvoiceCategoryCode(), vo.getParentCateCode())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("报价类目编号不能重复");
        } else {
            codeSet.add(individualProxyQuote.getInvoiceCategoryCode());
            if (vo.getParentCateCode() != null) {
                codeSet.add(vo.getParentCateCode());
            }
        }
        final List<IndividualProxyQuote> subcategorys = individualProxyQuote.getSubcategorys();
        if (subcategorys != null) {
            for (IndividualProxyQuote subcategory : subcategorys) {
                final String categoryCode = subcategory.getInvoiceCategoryCode();
                if (!codeSet.contains(categoryCode)) {
                    codeSet.add(categoryCode);
                }else {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("报价类目编号不能重复");
                }
            }
        }

        codeSet = null;
        final MainstayIndividualProxy proxy = mainstayIndividualProxyFacade.addIndividualProxyQuote(vo.getId(),vo.getParentCateCode(), vo.getIndividualProxyQuote());
        return RestResult.success(proxy);
    }

    @PostMapping("updateIndividualProxyQuote")
    public RestResult<MainstayIndividualProxy> updateIndividualProxyQuote( @Validated @RequestBody UpdateIndividualProxyQuoteVo vo) {
        final MainstayIndividualProxy proxy = mainstayIndividualProxyFacade.updateIndividualProxyQuote(vo.getId(), vo.getIndividualProxyQuote());
        return RestResult.success(proxy);
    }

    @PostMapping("deleteIndividualProxyQuote")
    public MainstayIndividualProxy deleteIndividualProxyQuote(@Validated @RequestBody DeleteIndividualProxyQuoteVo vo) {
        return mainstayIndividualProxyFacade.deleteIndividualProxyQuote(vo.getId(), vo.getInvoiceCategoryCode());
    }

    @PostMapping("listPage")
    public IPage<MainstayIndividualProxy> listPage(@RequestBody Page<MainstayIndividualProxy> page, @RequestBody Map<String, Object> paramMap) {
        return mainstayIndividualProxyFacade.listPage(page, paramMap);
    }

    @PostMapping("importProxyQuote")
    public RestResult<String> importProxyQuote(@RequestParam("mainstayNo") String mainstayNo, @RequestParam("file") MultipartFile multiPartfile,@CurrentUser PmsOperator pmsOperator) throws IOException {
        File excelFile = MultipartFileUtil.transfer2File(multiPartfile);

        Map<String, List<IndividualProxyQuote>> map = new HashMap<>();
        EasyExcel.read(excelFile,InvoiceCateExcelRow.class, new AnalysisEventListener<InvoiceCateExcelRow>() {
            @Override
            public void invoke(InvoiceCateExcelRow dataRow, AnalysisContext analysisContext) {
                final String firstLevelName = dataRow.getFirstLevelName();
                List<IndividualProxyQuote> quotes = map.get(firstLevelName);
                if (quotes == null) {
                    quotes = new ArrayList<>();
                    map.put(firstLevelName, quotes);
                }
                IndividualProxyQuote quote = new IndividualProxyQuote();
                quote.setInvoiceCategoryCode(IdUtil.fastSimpleUUID());
                quote.setInvoiceCategoryName(dataRow.getSecondLevelName());
                BeanUtil.copyProperties(dataRow, quote);
                quotes.add(quote);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {

            }
        }).sheet().headRowNumber(2).doRead();

        List<IndividualProxyQuote> allQuotes = new ArrayList<>();
        map.forEach((firstLevelName,item)->{
            IndividualProxyQuote proxyQuote = new IndividualProxyQuote();
            proxyQuote.setInvoiceCategoryCode(IdUtil.fastSimpleUUID());
            proxyQuote.setInvoiceCategoryName(firstLevelName);
            proxyQuote.setSubcategorys(item);
            allQuotes.add(proxyQuote);
        });

        final MainstayIndividualProxy mainstayIndividualProxy = mainstayIndividualProxyFacade.getByMainstayNo(mainstayNo);
        mainstayIndividualProxy.setTaxQuote(allQuotes);
        mainstayIndividualProxyFacade.updateIndividualProxyMainstay(mainstayIndividualProxy);

        final String fileId = fastdfsClient.uploadFile(multiPartfile.getBytes(), multiPartfile.getOriginalFilename());

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.COMMON_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.IMPORT_INVOICE_QUOTE.getFileName());
        record.setReportType(ReportTypeEnum.IMPORT_INVOICE_QUOTE.getValue());
        record.setParamJson(new JSONObject().toJSONString());
        record.setExportStatus(ExportStatusEnum.SUCCESS.getValue());
        record.setFileUrl(fileId);
        record.setDirection(1);

        exportRecordFacade.insert(record);

        return RestResult.success("success");
    }
}
