package com.zhixianghui.web.pms.controller.merchant;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.entity.EventsFollow;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.EventFollowFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.merchant.EventsFollowVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("eventsFollow")
@Slf4j
public class EventsFollowController {

    @Reference
    private EventFollowFacade eventFollowFacade;
    @Reference
    private MerchantFacade merchantFacade;


    @GetMapping("getEventFollowByMchNo")
    public RestResult<List<EventsFollow>> getEventFollowByMchNo(String mchNo){

        final Merchant merchant = merchantFacade.getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("商户不存在");
        }


        final List<EventsFollow> eventsFollows = eventFollowFacade.getEventFollowByMchNo(mchNo);

        return RestResult.success(eventsFollows);
    }

    @GetMapping("getEventFollowById")
    public RestResult<EventsFollow> getEventFollowById(Long id){
        final EventsFollow eventsFollow = eventFollowFacade.getEventFollowById(id);
        return RestResult.success(eventsFollow);
    }

    @PostMapping("addEventFollowRecord")
    public RestResult<String> addEventFollowRecord(@Validated @RequestBody EventsFollowVo eventsFollowVo, @CurrentUser PmsOperator operator){
        EventsFollow eventsFollow = new EventsFollow();
        BeanUtil.copyProperties(eventsFollowVo, eventsFollow);
        eventsFollow.setUpdateBy(operator.getRealName());
        eventFollowFacade.addEventFollowRecord(eventsFollow);
        return RestResult.success("success");
    }

    @PostMapping("updateEventFollowRecord")
    public RestResult<String> updateEventFollowRecord(Long id, String content){
        eventFollowFacade.updateEventFollowRecord(id, content);
        return RestResult.success("success");
    }

}
