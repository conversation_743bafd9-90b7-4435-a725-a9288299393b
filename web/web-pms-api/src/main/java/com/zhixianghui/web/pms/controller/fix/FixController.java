package com.zhixianghui.web.pms.controller.fix;

import com.alibaba.excel.EasyExcel;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.RecordItemFacade;
import com.zhixianghui.web.pms.controller.fix.dto.OrderItemFixDto;
import com.zhixianghui.web.pms.controller.fix.helper.AddPhoneExcelData;
import com.zhixianghui.web.pms.controller.fix.helper.AddPhoneListener;
import com.zhixianghui.web.pms.controller.fix.helper.MultipartFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

@RestController
@RequestMapping("fix")
@Slf4j
public class FixController {
    String SUFFIX = ".xlsx";

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private RecordItemFacade recordItemFacade;

    @PostMapping("supplementaryOrderPhoneNumber")
    public RestResult<String> supplementaryOrderPhoneNumber(OrderItemFixDto orderItemFixDto) {
        final String openFix = dataDictionaryFacade.getSystemConfig("OPEN_FIX");
        if (StringUtils.equals("true", openFix)) {
            MultipartFile file = orderItemFixDto.getFile();

            if(!file.getOriginalFilename().endsWith(SUFFIX)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("FileName:" + file.getOriginalFilename() +"文件格式有误，必须为xlsx格式");
            }
            File excelFile = MultipartFileUtil.transfer2File(file);

            EasyExcel.read(excelFile, AddPhoneExcelData.class, new AddPhoneListener(orderItemFacade,recordItemFacade)).sheet().headRowNumber(1).doRead();

        }else {
            log.info("未开放数据修复，跳过");
        }
        return RestResult.success("操作成功");
    }

}
