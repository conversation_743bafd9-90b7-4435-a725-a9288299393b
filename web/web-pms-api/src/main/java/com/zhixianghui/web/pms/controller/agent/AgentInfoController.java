package com.zhixianghui.web.pms.controller.agent;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.agent.AgentTypeEnum;
import com.zhixianghui.common.statics.enums.agent.CalculateModeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.fee.FormulaEnum;
import com.zhixianghui.common.statics.enums.fee.ProductFeeSpecialRuleTypeEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.EditTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.export.vo.ExportVo;
import com.zhixianghui.facade.fee.entity.AgentFeeRule;
import com.zhixianghui.facade.fee.service.AgentFeeRuleFacade;
import com.zhixianghui.facade.flow.dto.FlowStartDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.AgentBankAccount;
import com.zhixianghui.facade.merchant.entity.AgentProductQuote;
import com.zhixianghui.facade.merchant.entity.AgentSaler;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.ChangeSourceEnum;
import com.zhixianghui.facade.merchant.enums.SelfDeclaredEnum;
import com.zhixianghui.facade.merchant.service.MerchantInfoChangeRecordFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentAddVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentBatchSetItem;
import com.zhixianghui.facade.merchant.vo.agent.AgentBatchSetVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentDetailVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentMainVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentProductQuoteVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentQuoteDelVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentResVo;
import com.zhixianghui.facade.merchant.vo.agent.AgentVo;
import com.zhixianghui.facade.merchant.vo.agent.IAgentCompany;
import com.zhixianghui.facade.merchant.vo.agent.IAgentPerson;
import com.zhixianghui.facade.merchant.vo.agent.PrincipalEditVo;
import com.zhixianghui.facade.merchant.vo.agent.SetInviterVo;
import com.zhixianghui.facade.merchant.vo.agent.SetSellerVo;
import com.zhixianghui.facade.merchant.vo.agent.SimpleAgentInfoVo;
import com.zhixianghui.facade.merchant.vo.agent.SpecialFeeRuleVo;
import com.zhixianghui.facade.merchant.vo.record.AgentBaseInfoVo;
import com.zhixianghui.facade.merchant.vo.record.AgentProductFeeVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.agent.AgentBiz;
import com.zhixianghui.web.pms.biz.record.EditHandleContext;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.agent.req.AgentQueryVo;
import com.zhixianghui.web.pms.vo.agent.res.MerchantResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 合伙人
 * @date 2021/2/1 17:13
 **/
@Slf4j
@RestController
@RequestMapping("agent")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgentInfoController {

    private final AgentBiz agentBiz;
    private final EditHandleContext context;

    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private MerchantInfoChangeRecordFacade changeRecordFacade;
    @Reference
    private AgentFeeRuleFacade agentFeeRuleFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;



    /**
     * 创建合伙人(非个人)
     *
     */
    @RequestMapping("createAgentCompany")
    @Permission("agent:create")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "创建合伙人(非个人)")
    public RestResult<String> createAgentCompany(
            @RequestBody @Validated(IAgentCompany.class) AgentAddVo agentVo,
            @CurrentUser PmsOperator operator
    ) {

        Agent agent = agentBiz.generateAgent(agentVo, AgentTypeEnum.COMPANY.getValue(), operator);
        AgentSaler agentSaler = agentBiz.generateAgentSale(agent, agentVo, operator);
        agentBiz.createAgent(agent, agentSaler);

        agentBiz.saveAgentExtInfo(agentVo);

        log.info("合伙人信息:{}, 合伙人销售id:{}", agent.getAgentNo(), agentSaler.getId());
        //构建流程参数
        //当前用户
//        return buildProcess(agent, agentVo, operator, flowStartDto);
        return RestResult.success("success");
    }

    private RestResult<Map<String, Object>> buildProcess(Agent agent, AgentVo agentVo, PmsOperator operator, FlowStartDto<AgentVo> flowStartDto) {
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(operator.getId());
        flowUserVo.setUserName(operator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        flowUserVo.setNo(agent.getAgentNo());
        //流程主体参数
        ProcessVo processVo = new ProcessVo();
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(FlowTypeEnum.PMS_AGENT_CREATE.name());
        processVo.setFlowTopicType(FlowTypeEnum.PMS_AGENT_CREATE.getFlowTopicType());
        processVo.setFlowTopicName(
                String.join("-", FlowTypeEnum.PMS_AGENT_CREATE.getDesc(), agentVo.getAgentName())
        );
        processVo.setBusinessKey(agent.getAgentNo());
        agentVo.setLoginName(operator.getRealName());
        processVo.setExtInfo(JsonUtil.toString(agentVo));
        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo,flowUserVo,flowStartDto.getParticipant(),flowStartDto.getCondition());

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    /**
     * 创建合伙人(个人)
     *
     */
    @PostMapping("createAgentPerson")
    @Permission("agent:create")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "创建合伙人(个人)")
    public RestResult<String> createAgentPerson(
            @RequestBody @Validated(IAgentPerson.class) AgentAddVo agentVo,
            @CurrentUser PmsOperator operator
    ) {

        agentVo.setLoginName(operator.getRealName());
        Agent agent = agentBiz.generateAgent(agentVo, AgentTypeEnum.PERSON.getValue(), operator);
        AgentSaler agentSaler = agentBiz.generateAgentSale(agent, agentVo, operator);
        agentBiz.createAgent(agent, agentSaler);

        agentBiz.saveAgentExtInfo(agentVo);

        log.info("合伙人信息:{}, 合伙人销售id:{}", agent.getAgentNo(), agentSaler.getId());

        return RestResult.success("success");
    }


    /**
     * 更新详细信息 个人
     *
     * @param operator 操作者
     */
    @PostMapping("updateAgentDetailCompanyPerson")
    @Permission("agent:updateAgentDetail")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新非个人合伙人详细信息")
    public RestResult<Map<String, Object>> updateAgentDetailPerson(
            @RequestBody @Validated(IAgentPerson.class) FlowStartDto<AgentVo> flowStartDto,
            @CurrentUser PmsOperator operator
    ) {
        AgentVo agentVo = flowStartDto.getExtObj();
        checkParam(agentVo, true);
        agentBiz.legal(agentVo, operator);
        PmsOperator pmsOperator = pmsOperatorFacade.getOperatorById(agentVo.getSalerId());
        if (pmsOperator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("销售不存在");
        }
        agentVo.setSalerId(agentVo.getSalerId());
        agentVo.setSalerName(pmsOperator.getRealName());
        agentVo.setAgentType(AgentTypeEnum.PERSON.getValue());
        agentVo.setLoginName(operator.getRealName());
        return modifyProcess(agentVo, operator, flowStartDto);
    }

    /**
     * 更新详细信息 非个人
     *
     * @param operator 操作者
     */
    @PostMapping("updateAgentDetailCompany")
    @Permission("agent:updateAgentDetail")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新非个人合伙人详细信息")
    public RestResult<Map<String, Object>> updateAgentDetailCompany(
            @RequestBody @Validated(IAgentPerson.class) FlowStartDto<AgentVo> flowStartDto,
            @CurrentUser PmsOperator operator
    ) {
        AgentVo agentVo = flowStartDto.getExtObj();
        checkParam(agentVo, false);
        agentBiz.legal(agentVo, operator);

        agentVo.setAgentType(AgentTypeEnum.COMPANY.getValue());
        agentVo.setLoginName(operator.getRealName());
        PmsOperator pmsOperator = pmsOperatorFacade.getOperatorById(agentVo.getSalerId());
        if (pmsOperator == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("销售不存在");
        }
        agentVo.setSalerId(agentVo.getSalerId());
        agentVo.setSalerName(pmsOperator.getRealName());
        return modifyProcess(agentVo, operator, flowStartDto);
    }

    private RestResult<Map<String, Object>> modifyProcess(AgentVo agentVo, PmsOperator operator, FlowStartDto<AgentVo> flowStartDto) {
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(operator.getId());
        flowUserVo.setUserName(operator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        flowUserVo.setNo(agentVo.getAgentNo());
        //流程主体参数
        ProcessVo processVo = new ProcessVo();
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(FlowTypeEnum.PMS_AGENT_MODIFY.name());
        processVo.setFlowTopicType(FlowTypeEnum.PMS_AGENT_MODIFY.getFlowTopicType());
        processVo.setFlowTopicName(
                String.join("-", FlowTypeEnum.PMS_AGENT_MODIFY.getDesc(), agentVo.getAgentName())
        );
        processVo.setBusinessKey(agentVo.getAgentNo());
        processVo.setExtInfo(JsonUtil.toString(agentVo));
        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo,flowUserVo,flowStartDto.getParticipant(),flowStartDto.getCondition());
        synchronizedRecord(agentVo, operator, commonFlow.getId());
        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    private void synchronizedRecord(AgentVo agentVo, PmsOperator operator, Long flowId) {
        AgentVo oldInfo = agentBiz.getAgentDetail(agentVo.getAgentNo(), operator);
        AgentBaseInfoVo oldBaseInfo = AgentBaseInfoVo.getInstance();
        BeanUtil.copyProperties(oldInfo, oldBaseInfo);
        AgentBaseInfoVo newBaseInfo = AgentBaseInfoVo.getInstance();
        BeanUtil.copyProperties(agentVo, newBaseInfo);
        AgentVo.setCommonValue(newBaseInfo, operator.getId(), operator.getRealName(), flowId);
        changeRecordFacade.record(newBaseInfo, oldBaseInfo, ChangeSourceEnum.FLOW.getSource());
    }


//    /**
//     * 创建合伙人(非个人)
//     *
//     * @param vo       vo
//     * @param operator 操作者
//     */
//    @PostMapping("createAgentCompany")
//    @Permission("agent:create")
//    @Logger(type = OperateLogTypeEnum.CREATE, name = "创建合伙人(非个人)")
//    public RestResult<Map<String, Object>> createAgentCompany(@RequestBody @Validated(IAgentCompany.class) AgentVo vo, @CurrentUser PmsOperator operator) {
//        return RestResult.success(agentBiz.createAgent(vo, AgentTypeEnum.COMPANY.getValue(), operator));
//    }

    /**
     * 创建合伙人(个人)
     *
     * @param vo       vo
     * @param operator 操作者
     */
//    @PostMapping("createAgentPerson")
//    @Permission("agent:create")
//    @Logger(type = OperateLogTypeEnum.CREATE, name = "创建合伙人(个人)")
//    public RestResult<Map<String, Object>> createAgentPerson(@RequestBody @Validated(IAgentPerson.class) AgentVo vo, @CurrentUser PmsOperator operator) {
//        checkTaxPercent(vo);
//        return RestResult.success(agentBiz.createAgent(vo, AgentTypeEnum.PERSON.getValue(), operator));
//    }

    /**
     * 获取全部合伙人简易信息
     */
    @PostMapping("getAllAgentSimple")
    @Permission("agent:view")
    public RestResult<List<SimpleAgentInfoVo>> getAllAgentSimple() {
        return RestResult.success(agentBiz.getAllAgentSimple());
    }

    /**
     * 根据销售Id获取其非清退/非已创建合伙人简易信息
     * 不传为全部
     */
    @PostMapping("listNotRetreatAgentSimple")
    @Permission("agent:view")
    public RestResult<List<SimpleAgentInfoVo>> listNotRetreatAgentSimple(Long salerId) {
        return RestResult.success(agentBiz.listNotRetreatAgentSimple(salerId));
    }

    /**
     * 合伙人列表
     * 销售只能看自己
     */
    @PostMapping("listAgentPage")
    @Permission("agent:view")
    public RestResult<PageResult<List<AgentResVo>>> listAgentPage(@Validated @RequestBody AgentQueryVo agentQueryVo, @RequestBody PageVo pageVo, @CurrentUser PmsOperator operator) {
        return RestResult.success(agentBiz.listAgentPage(agentQueryVo, pageVo.toPageParam("CREATE_TIME DESC"), operator));
    }

    @PostMapping("exportAgent")
    @Permission("agent:view")
    public RestResult<String> exportAgent(@Validated @RequestBody ExportVo exportVo,@CurrentUser PmsOperator operator) {
        agentBiz.exportAgent(exportVo, operator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 合伙人-商户关系
     */
    @PostMapping("listAgentMerchantRelationPage")
    @Permission("agent:view")
    public RestResult<PageResult<List<MerchantResVo>>> listAgentMerchantRelationPage(@RequestParam String agentNo, @RequestParam Integer relationType, @RequestBody PageVo pageVo, @CurrentUser PmsOperator operator) {
        return RestResult.success(agentBiz.listAgentMerchantRelationPage(agentNo, relationType, pageVo.toPageParam("CREATE_TIME DESC"), operator));
    }

    /**
     * 批量操作-设置销售
     */
    @PostMapping("batchSetSeller")
    @Permission("agent:saler:edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "合伙人批量操作-设置销售")
    public RestResult<Map<String, Object>> batchSetSeller(@Validated @RequestBody FlowStartDto<AgentBatchSetVo> startDto, @CurrentUser PmsOperator operator) {

        AgentBatchSetVo agentBatchSetVo = startDto.getExtObj();

        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow(FlowTypeEnum.AGENT_BATCH_SET_INVITER_SELLER, "batchSetSeller");
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【合伙人批量操作-设置销售】有未完结审批");
        }

        String[] agentNos = new String[agentBatchSetVo.getAgentNoList().size()];
        int i = 0;
        for (AgentBatchSetItem agentItem : agentBatchSetVo.getAgentNoList()) {
            com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(agentItem.getAgentNo());
            agentItem.setBeforeNo(agent.getAgentNo());
            agentItem.setBeforeName(agent.getAgentName());
            agentItem.setBeforeSaleId(agent.getSalerId());
            agentItem.setBeforeSaleName(agent.getSalerName());
            agentNos[i] = agentItem.getAgentNo();
            i++;
        }

        boolean existNotFinishedFlowItem = flowFacade.isExistNotFinishedFlow(FlowTypeEnum.AGENT_SET_SELLER,agentNos);
        if (existNotFinishedFlowItem) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【变更销售负责人】有未完结审批");
        }

        CommonFlow commonFlow = startFlow(operator, FlowTypeEnum.AGENT_BATCH_SET_INVITER_SELLER, "batchSetSeller", "合伙人批量设置销售负责人", startDto, agentBatchSetVo);
        context.getBuilder(EditTypeEnum.AGENT_SET_SELLER.getType()).build(agentBatchSetVo, operator, commonFlow.getId());

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    /**
     * 批量操作-设置邀请方
     */
    @PostMapping("batchSetInviter")
    @Permission("agent:inviter:edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "合伙人批量操作-设置邀请方")
    public RestResult<Map<String, Object>> batchSetInviter(@Validated @RequestBody FlowStartDto<AgentBatchSetVo> startDto, @CurrentUser PmsOperator operator) {
        AgentBatchSetVo agentBatchSetVo = startDto.getExtObj();

        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow(FlowTypeEnum.AGENT_BATCH_SET_INVITER_SELLER, "batchSetInviter");
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【合伙人批量操作-设置邀请方】有未完结审批");
        }

        String[] agentNos = new String[agentBatchSetVo.getAgentNoList().size()];
        int i = 0;
        for (AgentBatchSetItem agentItem : agentBatchSetVo.getAgentNoList()) {
            com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(agentItem.getAgentNo());
            agentItem.setBeforeNo(agent.getInviterNo());
            agentItem.setBeforeName(agent.getInviterName());
            agentNos[i] = agentItem.getAgentNo();
            i++;
        }

        boolean existNotFinishedFlowItem = flowFacade.isExistNotFinishedFlow( FlowTypeEnum.AGENT_SET_INVITER,agentNos);
        if (existNotFinishedFlowItem) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【变更邀请方】有未完结审批");
        }

        CommonFlow commonFlow = startFlow(operator, FlowTypeEnum.AGENT_BATCH_SET_INVITER_SELLER, "batchSetInviter", "合伙人批量设置邀请方", startDto, agentBatchSetVo);
        context.getBuilder(EditTypeEnum.AGENT_SET_INVITER.getType()).build(agentBatchSetVo, operator, commonFlow.getId());

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    /**
     * 改变合伙人状态
     */
    @PostMapping("changeStatus")
    @Permission("agent:edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "改变合伙人状态")
    public RestResult<String> changeStatus(@RequestParam String agentNo, @RequestParam Integer status, @CurrentUser PmsOperator operator) {
        context.getBuilder(EditTypeEnum.AGENT_CHANGE_STATUS.getType()).build(AgentBaseInfoVo.buildAgentStatus(agentNo, status), operator);
        agentBiz.changeStatus(agentNo, status, operator);
        return RestResult.success("操作成功");
    }

    @PostMapping("baseInfo")
    @Permission("agent:baseInfo:view")
    public RestResult<com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo> agentBaseInfo(@RequestParam String agentNo) {
        com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(agentNo);
        return RestResult.success(agent);
    }

    @PostMapping("editBaseInfo")
    @Permission("agent:baseInfo:edit")
    public RestResult<String> editAgentBaseInfo(@RequestBody com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo baseInfoVo,@CurrentUser PmsOperator operator) {
        AgentBaseInfoVo newInfo = AgentBaseInfoVo.getInstance();
        BeanUtil.copyProperties(baseInfoVo, newInfo);
        context.getBuilder(EditTypeEnum.AGENT_EDIT_BASE_INFO.getType()).build(newInfo, operator);
        agentBiz.editAgentBaseInfo(baseInfoVo,operator);

        return RestResult.success("success");
    }


    @PostMapping("mainInfo")
    @Permission("agent:mainInfo:view")
    public RestResult<AgentMainVo> agentMainInfo(@RequestParam String agentNo) {
        return RestResult.success(agentBiz.buildMainInfo(agentNo));
    }

    @PostMapping("agentBankInfo")
    @Permission("agent:bankInfo:view")
    public RestResult<AgentBankAccount> bankInfo(@RequestParam String agentNo) {
        AgentBankAccount agentBankAccount = agentBiz.bankInfo(agentNo);
        return RestResult.success(agentBankAccount);
    }

    @PostMapping("editAgentBankInfo")
    @Permission("agent:bankInfo:edit")
    public RestResult<Map<String, Object>> editAgentBankInfo(@RequestBody FlowStartDto<AgentBankAccount> flowStartDto, @CurrentUser PmsOperator pmsOperator) {

        AgentBankAccount agentBankAccount = flowStartDto.getExtObj();
        if (Objects.isNull(agentBankAccount)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("银行账户信息不能为空");
        }
        com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(agentBankAccount.getAgentNo());
        if (agent == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人基本信息不存在");
        }
        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow( FlowTypeEnum.AGENT_BANKACCT_EDIT,agentBankAccount.getAgentNo());
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【编辑银行信息】有未完结审批");
        }

        CommonFlow commonFlow = startFlow(pmsOperator, FlowTypeEnum.AGENT_BANKACCT_EDIT, agent.getAgentNo(), agent.getAgentName(), flowStartDto, agentBankAccount);
        AgentBaseInfoVo newInfo = AgentBaseInfoVo.getInstance();
        BeanUtil.copyProperties(agentBankAccount, newInfo);
        newInfo.setFlowId(commonFlow.getId());
        context.getBuilder(EditTypeEnum.AGENT_EDIT_BANK_INFO.getType()).build(newInfo, pmsOperator);
        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @PostMapping("editMainInfo")
    @Permission("agent:mainInfo:edit")
    public RestResult<Map<String, Object>> editMainInfo(@RequestBody FlowStartDto<AgentMainVo> flowStartDto,@CurrentUser PmsOperator pmsOperator) {
        AgentMainVo agentMainVo = flowStartDto.getExtObj();
        if (agentMainVo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数缺失");
        }

        com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(agentMainVo.getAgentNo());
        if (agent == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人基本信息不存在");
        }

        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow( FlowTypeEnum.AGENT_MAINiNFO_EDIT,agentMainVo.getAgentNo());
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【编辑主体信息】有未完结审批");
        }

        CommonFlow commonFlow = startFlow(pmsOperator, FlowTypeEnum.AGENT_MAINiNFO_EDIT, agent.getAgentNo(), agent.getAgentName(), flowStartDto, agentMainVo);
        AgentBaseInfoVo newInfo = AgentBaseInfoVo.getInstance();
        BeanUtil.copyProperties(agentMainVo, newInfo);
        newInfo.setFlowId(commonFlow.getId());
        context.getBuilder(EditTypeEnum.AGENT_EDIT_MAIN_INFO.getType()).build(newInfo, pmsOperator);
        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @PostMapping("cooperation")
    @Permission("agent:cooperation:view")
    public RestResult<List<AgentProductQuoteVo>> cooperationInfo(String agentNo) {
        List<AgentProductQuote> agentProductQuotes = agentBiz.cooperationInfo(agentNo);

        List<AgentProductQuoteVo> quoteVos = null;
        if (!CollectionUtils.isEmpty(agentProductQuotes)) {
            quoteVos = agentProductQuotes.stream().map(quote -> {
                AgentProductQuoteVo vo = new AgentProductQuoteVo();

                if (StringUtils.isBlank(quote.getMainstayNo())&&quote.getRuleType()==RuleTypeEnum.SPECIAL.getValue()) {
                    List<SpecialFeeRuleVo> specialFeeRuleVos = JSONUtil.toList(quote.getRuleParam(), SpecialFeeRuleVo.class);
                    for (SpecialFeeRuleVo specialFeeRuleVo : specialFeeRuleVos) {
                        if (specialFeeRuleVo.getSpecialRuleType() == ProductFeeSpecialRuleTypeEnum.VERDOR_NO.getValue()) {
                            String mainstayNo = specialFeeRuleVo.getValue();
                            quote.setMainstayNo(mainstayNo);

                            Merchant mainstay = merchantQueryFacade.getByMchNo(mainstayNo);
                            quote.setMainstayName(mainstay.getMchName());
                        }
                    }
                }

                BeanUtils.copyProperties(quote, vo);
                List<SpecialFeeRuleVo> list = JSONUtil.toList(quote.getRuleParam(), SpecialFeeRuleVo.class);
                vo.setRuleParam(list);
                return vo;
            }).collect(Collectors.toList());
        }
        return RestResult.success(quoteVos);
    }

    @GetMapping("checkCooperation")
    public RestResult<String> checkCooperation(@RequestParam("agentNo") String agentNo, @RequestParam("mainstayNo") String mainstayNo) {
        List<AgentProductQuote> agentProductQuotes = agentBiz.cooperationInfo(agentNo);

        boolean matchRuleType = agentProductQuotes.stream().anyMatch(x -> RuleTypeEnum.GENERAL.getValue() == x.getRuleType());
        if (matchRuleType) {
            return RestResult.success(null);
        }

        boolean matchMainstayNo = agentProductQuotes.stream().anyMatch(x -> mainstayNo.equals(x.getMainstayNo()));
        if (matchMainstayNo) {
            return RestResult.success(null);
        }

        com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(agentNo);
        return RestResult.success(
                String.format("此商户合伙人【%s%s】在该代征主体下未配置合伙人报价单", agentNo, agent.getAgentName())
        );
    }

    private void build(AgentProductQuoteVo quoteVo, AgentFeeRule agentFeeRule) {
        quoteVo.setDescription(agentFeeRule.getDescription());
        if (agentFeeRule.getFirstFeeRate() != null) {
            quoteVo.setFeeRate(AmountUtil.mul(agentFeeRule.getFirstFeeRate(), new BigDecimal(100)));
        }
        quoteVo.setFixedFee(agentFeeRule.getFirstFixedFee());
        quoteVo.setFormulaType(agentFeeRule.getFirstFormulaType());
        quoteVo.setMaxFee(agentFeeRule.getMaxFee());
        quoteVo.setMinFee(agentFeeRule.getMinFee());
        quoteVo.setPriority(agentFeeRule.getPriority());
        quoteVo.setRuleType(agentFeeRule.getRuleType());
        if (agentFeeRule.getSecondFeeRate() != null) {
            quoteVo.setSecondFeeRate(AmountUtil.mul(agentFeeRule.getSecondFeeRate(), new BigDecimal(100)));
        }
        quoteVo.setSecondFixedFee(agentFeeRule.getSecondFixedFee());
        quoteVo.setSecondFormulaType(agentFeeRule.getSecondFormulaType());
        if (!CollectionUtils.isEmpty(agentFeeRule.getSpecialFeeRuleList())) {
            agentFeeRule.getSpecialFeeRuleList().forEach(item -> {
                SpecialFeeRuleVo specialFeeRuleVo = new SpecialFeeRuleVo();
                BeanUtil.copyProperties(specialFeeRuleVo, item);
                quoteVo.getRuleParam().add(specialFeeRuleVo);
            });
        }
    }

    /**
     * 查询详细信息
     *
     * @param agentNo  合伙人编号
     * @param operator 操作者
     */
    @GetMapping("getAgentDetail")
    @Permission("agent:getAgentDetail")
    public RestResult<AgentDetailVo> getAgentDetail(@RequestParam String agentNo, @CurrentUser PmsOperator operator) {
        return RestResult.success(agentBiz.getAgentDetail(agentNo, operator));
    }

//    /**
//     * 更新详细信息 非个人
//     *
//     * @param vo       合伙人信息
//     * @param operator 操作者
//     */
//    @PostMapping("updateAgentDetailCompany")
//    @Permission("agent:updateAgentDetail")
//    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新非个人合伙人详细信息")
//    public RestResult<String> updateAgentDetailCompany(@RequestBody @Validated(IAgentCompany.class) AgentVo vo, @CurrentUser PmsOperator operator) {
//        checkTaxPercent(vo);
//        agentBiz.updateAgentDetail(vo, AgentTypeEnum.COMPANY.getValue(), operator);
//        return RestResult.success("更新成功");
//    }

    private void checkParam(AgentVo agentVo, boolean needTaxPercent) {
        if (agentVo == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("合伙人信息为空...");
        }
        if (needTaxPercent && Objects.equals(agentVo.getSelfDeclared(), SelfDeclaredEnum.PLATFORM.getValue())) {
            if (StringUtils.isBlank(agentVo.getTaxPercent())) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("个人合伙人的代扣税比例不能为空...");
            }
            String taxPercent = agentVo.getTaxPercent();
            if (StringUtils.isBlank(taxPercent)) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("代扣税比例不能为空");
            }
            if (Double.parseDouble(taxPercent) < 0 || Double.parseDouble(taxPercent) > 100) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("代扣税比例要在0~100之间");
            }
        }
        List<AgentProductQuoteVo> list = agentVo.getQuoteVoList();
        if (list ==null || list.isEmpty()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("产品报价信息不能为空");
        }
        list.forEach(item -> {
            item.setRuleType(RuleTypeEnum.SPECIAL.getValue());
            if (item.getFixedFee() == null && item.getFeeRate() == null) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("固定金额或费率不能都为空");
            }
            if (item.getRuleParam().size() == 0) {
                item.getRuleParam().add(new SpecialFeeRuleVo());
            }
            SpecialFeeRuleVo ruleParam = item.getRuleParam().get(0);
            if (StringUtils.isBlank(ruleParam.getValue())) {
                item.getRuleParam().get(0).setValue(item.getMainstayNo());
            }
        });
    }


//    /**
//     * 更新详细信息 个人
//     *
//     * @param vo       合伙人信息
//     * @param operator 操作者
//     */
//    @PostMapping("updateAgentDetailCompanyPerson")
//    @Permission("agent:updateAgentDetail")
//    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新非个人合伙人详细信息")
//    public RestResult<String> updateAgentDetailPerson(@RequestBody @Validated(IAgentPerson.class) AgentVo vo, @CurrentUser PmsOperator operator) {
//        agentBiz.updateAgentDetail(vo, AgentTypeEnum.PERSON.getValue(), operator);
//        return RestResult.success("更新成功");
//    }

    /**
     * 详细信息导出
     *
     * @param exportVo 导出信息
     * @param operator 操作者
     */
    @PostMapping("exportAgentDetail")
    @Permission("agent:exportAgentDetail")
    public RestResult<String> exportAgentDetail(@Valid @RequestBody ExportVo exportVo, @CurrentUser PmsOperator operator) {
        String agentNo = (String) exportVo.getParamMap().get("agentNo");
        if (StringUtil.isEmpty(agentNo)) {
            return RestResult.error("商户号不能为空");
        }

        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGENT_DETAIL.getFileName());
        record.setReportType(ReportTypeEnum.AGENT_DETAIL.getValue());

        record.setParamJson(JsonUtil.toString(exportVo.getParamMap()));
        exportVo.getFieldInfoList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }


    /**
     * 编辑产品报价单
     * @return
     */
    @PostMapping("editQuote")
    @Permission("agent:quote:edit")
    public RestResult<Map<String,Object>> editQuote(@Validated @RequestBody FlowStartDto<List<AgentProductQuoteVo>> quotesFlow, @CurrentUser PmsOperator operator) {
        List<AgentProductQuoteVo> extObjs = quotesFlow.getExtObj();
        log.info("编辑合伙人报价单参数：[{}]",JsonUtil.toString(extObjs));
        if (Objects.isNull(extObjs)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("合伙人产品报价单不能为空");
        }

        List<String> prioritys = new ArrayList<>();
        for (AgentProductQuoteVo extObj : extObjs) {
            checkFeeRule(extObj);
            // 检查是否有相同优先级
            String flag = extObj.getAgentNo() + extObj.getProductNo() + extObj.getRuleType() + extObj.getPriority();
            if (!prioritys.contains(flag)) {
                prioritys.add(flag);
            }else {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提交的报价单不能存在相同优先级");
            }
        }

        String agentNo = extObjs.get(0).getAgentNo();
        String agentName = extObjs.get(0).getAgentName();
        String mainstayNo = extObjs.get(0).getMainstayNo();
        Integer ruleType = extObjs.get(0).getRuleType();

        String bussinessKey = ruleType == 0?agentNo + "-"+ruleType:agentNo + "-"+ruleType+"-" + mainstayNo;

        // 检查是否有审批中的流程
        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow( FlowTypeEnum.PMS_AGENT_QUOTE_EDIT,bussinessKey);
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【编辑合伙人产品报价单】有未完结审批");
        }

        String action = "修改";
        if (Objects.isNull(extObjs.get(0).getId())) {
            action = "新增";

            Map<String, Object> params = new HashMap<>();
            params.put("agentNo", agentNo);
            params.put("productNo", extObjs.get(0).getProductNo());
            params.put("ruleType", ruleType);

            log.info("查询规则入参：{}",JsonUtil.toString(params));

            String errorInfo = "已经存在相同产品的通用规则，请进入编辑或者删除后新增";
            if (ruleType == 1) {
                params.put("mainstayNo", mainstayNo);
                errorInfo = "已经存在相同产品与供应商规则，请进入编辑或者删除后新增";
            }

            List<AgentProductQuote> agentProductQuotes = agentBiz.cooperationInfoByParams(params);
            log.info("查询规则查询结果:{}", JSONUtil.toJsonStr(agentProductQuotes));
            if (agentProductQuotes != null && !agentProductQuotes.isEmpty()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg(errorInfo);
            }
        }

        if (mainstayNo != null) {
            //查询供应商名称
            Merchant mainstay = merchantQueryFacade.getByMchNo(mainstayNo);
            if (mainstay == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("供应商不存在："+mainstayNo);
            }
            for (AgentProductQuoteVo extObj : extObjs) {
                extObj.setMainstayName(mainstay.getMchName());
            }
        }

        CommonFlow commonFlow = startFlow(operator, FlowTypeEnum.PMS_AGENT_QUOTE_EDIT, bussinessKey, agentName, action, quotesFlow, extObjs);

        //提交后记录合伙人产品报价单
        try {
            for (AgentProductQuoteVo extObj : extObjs) {
                agentBiz.preSaveAgentQuote(extObj, operator, commonFlow.getId());
                AgentProductFeeVo newInfo = AgentProductFeeVo.getInstance();
                BeanUtil.copyProperties(extObj, newInfo);
                newInfo.setFlowId(commonFlow.getId());
                context.getBuilder(EditTypeEnum.AGENT_EDIT_QUOTE_INFO.getType()).build(newInfo, operator, extObj.getId());
            }
        } catch (Exception e) {
            FlowUserVo flowUserVo = new FlowUserVo();
            flowUserVo.setUserId(operator.getId());
            flowUserVo.setUserName(operator.getRealName());
            flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
            flowFacade.deleteProcessInstance(commonFlow.getId(),flowUserVo,true,"系统异常回滚");
            log.error("生成报价单失败",e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("生成报价单失败:"+e.getMessage());
        }

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @PostMapping("delQuote")
    @Permission("agent:quote:delete")
    public RestResult<Map<String, Object>> deleteQuote(@RequestBody FlowStartDto<AgentQuoteDelVo> quotesFlow, @CurrentUser PmsOperator operator) {

        AgentQuoteDelVo extObj = quotesFlow.getExtObj();

        List<Map<String, Object>> quoteRateList = (List<Map<String, Object>>) extObj.getDetail().get("quoteRateList");

        String businessKey = extObj.getAgentNo() + "-" + extObj.getProductNo() + "-" + extObj.getRuleType();
        for (Map<String, Object> item : quoteRateList) {
            if (Objects.isNull(item.get("id"))) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("detail必须提供报价单ID");
            }
        }
        log.info("删除合伙人报价单:{}",JsonUtil.toString(extObj));

        // 检查是否有审批中的流程
        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow( FlowTypeEnum.PMS_AGENT_QUOTE_DEL,businessKey);
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【删除合伙人产品报价单】有未完结审批");
        }

        CommonFlow commonFlow = startFlow(operator, FlowTypeEnum.PMS_AGENT_QUOTE_DEL, businessKey, extObj.getAgentName(), quotesFlow, extObj);
        AgentProductFeeVo oldInfo = AgentProductFeeVo.getInstance();
        BeanUtil.mapToObject(oldInfo, extObj.getDetail());
        context.getBuilder(EditTypeEnum.AGENT_DELETE_QUOTE_INFO.getType()).build(oldInfo, operator, commonFlow.getId());
        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    private <T> CommonFlow startFlow(PmsOperator operator,FlowTypeEnum flowTypeEnum,String businessKey,String businessName,FlowStartDto flowStartDto,T param){
        //当前用户
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(operator.getId());
        flowUserVo.setUserName(operator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        //流程主体参数
        ProcessVo processVo = new ProcessVo();
        processVo.setBusinessKey(businessKey);
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(flowTypeEnum.name());
        processVo.setFlowTopicType(flowTypeEnum.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-",flowTypeEnum.getDesc(),businessName));
        processVo.setExtInfo(JsonUtil.toString(param));
        processVo.setRemark(flowStartDto.getRemark());

        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo,flowUserVo,flowStartDto.getParticipant(),flowStartDto.getCondition());
        return commonFlow;
    }

    private <T> CommonFlow startFlow(PmsOperator operator, FlowTypeEnum flowTypeEnum, String businessKey, String businessName, String action, FlowStartDto flowStartDto, T param) {

        //当前用户
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(operator.getId());
        flowUserVo.setUserName(operator.getRealName());
        flowUserVo.setPlatform(PlatformSource.OPERATION.getValue());
        //流程主体参数
        ProcessVo processVo = new ProcessVo();
        processVo.setBusinessKey(businessKey);
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(flowTypeEnum.name());
        processVo.setFlowTopicType(flowTypeEnum.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-", action, flowTypeEnum.getDesc(), businessName));
        processVo.setExtInfo(JsonUtil.toStringWithNull(param));
        processVo.setRemark(flowStartDto.getRemark());

        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo, flowUserVo, flowStartDto.getParticipant(), flowStartDto.getCondition());
        return commonFlow;
    }

    private void checkFeeRule(AgentProductQuoteVo vo) {
        Assert.notNull(vo.getRuleType(), "规则参数不能为空");
        Assert.notNull(vo.getFormulaType(), "一级公式类型不能为空");
        if (Objects.equals(vo.getRuleType(), RuleTypeEnum.GENERAL.getValue())
                && vo.getPriority() != 1) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("通用规则优先级只能为1");
        }
        if (vo.getRuleType() == RuleTypeEnum.SPECIAL.getValue()) {
            LimitUtil.notEmpty(vo.getRuleParam(), "特殊规则不能为空");
        }

        if (vo.getRealProfitRatio() == null || vo.getRealProfitRatio().intValue()<=0 || vo.getRealProfitRatio().intValue()>100) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("非法参数：真实分润比例只能为(0-100]的整数");
        }

        // 一级分佣按比例收费
        if (vo.getFormulaType() == FormulaEnum.RATE.getValue()) {
            LimitUtil.notEmpty(vo.getFeeRate(), "一级分佣手续费费率不能为空");
        }
        // 一级分佣按笔收费
        else if (vo.getFormulaType() == FormulaEnum.FIXED.getValue()) {
            LimitUtil.notEmpty(vo.getFixedFee(), "一级分佣固定手续费不能为空");

        }
        // 一级分佣按笔+比例收费
        else if (vo.getFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()) {
            LimitUtil.notEmpty(vo.getFixedFee(), "一级分佣固定手续费不能为空");
            LimitUtil.notEmpty(vo.getFeeRate(), "一级分佣手续费费率不能为空");
        }

        // 二级分佣按比例收费
        if (vo.getCalculateMode().intValue() == CalculateModeEnum.FORMULA.getValue()) {
            if (vo.getSecondFormulaType() == null) {
                LimitUtil.notEmpty(vo.getSecondFormulaType(), "二级分佣计费公式类型不能为空");
            }
            if (vo.getSecondFormulaType() == FormulaEnum.RATE.getValue()) {
                LimitUtil.notEmpty(vo.getSecondFeeRate(), "二级分佣手续费费率不能为空");
            }
            // 二级分佣按笔收费
            else if (vo.getSecondFormulaType() == FormulaEnum.FIXED.getValue()) {
                LimitUtil.notEmpty(vo.getSecondFixedFee(), "二级分佣固定手续费不能为空");

            }
            // 二级分佣按笔+比例收费
            else if (vo.getSecondFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()) {
                LimitUtil.notEmpty(vo.getSecondFixedFee(), "二级分佣固定手续费不能为空");
                LimitUtil.notEmpty(vo.getSecondFeeRate(), "二级分佣手续费费率不能为空");
            }
        }

        if (vo.getRuleType() == 1) {
            if (vo.getRuleParam() == null || vo.getRuleParam().isEmpty()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("特殊规则缺少规则元素");
            }else {
                for (SpecialFeeRuleVo specialFeeRuleVo : vo.getRuleParam()) {
                    if (specialFeeRuleVo.getSpecialRuleType() == 1) {
                        if (StringUtils.isBlank(specialFeeRuleVo.getValue())) {
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("特殊规则必须提供供应商");
                        }
                    }
                }
            }
        }
    }

    @PostMapping("setSeller")
    @Permission("agent:saler:edit")
    public RestResult<Map<String, Object>> setSaler(@Validated @RequestBody FlowStartDto<SetSellerVo> flowStartDto, @CurrentUser PmsOperator operator) {

        SetSellerVo setSellerVo = flowStartDto.getExtObj();

        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow( FlowTypeEnum.AGENT_SET_SELLER,setSellerVo.getAgentNo());
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【变更销售负责人】有未完结审批");
        }


        com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(setSellerVo.getAgentNo());
        setSellerVo.setBeforeSellerId(agent.getSalerId());
        setSellerVo.setBeforeSellerName(agent.getSalerName());

        CommonFlow commonFlow = startFlow(operator, FlowTypeEnum.AGENT_SET_SELLER, setSellerVo.getAgentNo(), agent.getAgentName(), flowStartDto, setSellerVo);

        context.getBuilder(EditTypeEnum.AGENT_SET_SELLER.getType()).build(setSellerVo, operator, commonFlow.getId());
        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @PostMapping("setInviter")
    @Permission("agent:inviter:edit")
    public RestResult<Map<String, Object>> setInviter(@Validated @RequestBody FlowStartDto<SetInviterVo> flowStartDto, @CurrentUser PmsOperator operator) {

        SetInviterVo setInviterVo = flowStartDto.getExtObj();

        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow( FlowTypeEnum.AGENT_SET_INVITER,setInviterVo.getAgentNo());
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【变更邀请方】有未完结审批");
        }
        com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo inviter = agentBiz.getAgent(setInviterVo.getInviterNo());
        if (inviter == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("邀请人不存在");

        }

        com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(setInviterVo.getAgentNo());
        setInviterVo.setBeforeInviterNo(agent.getInviterNo());
        setInviterVo.setBeforeInviterName(agent.getInviterName());
        setInviterVo.setInviterName(inviter.getAgentName());

        CommonFlow commonFlow = startFlow(operator, FlowTypeEnum.AGENT_SET_INVITER, setInviterVo.getAgentNo(), agent.getAgentName(), flowStartDto, setInviterVo);
        context.getBuilder(EditTypeEnum.AGENT_SET_INVITER.getType()).build(setInviterVo, operator, commonFlow.getId());

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @PostMapping("setPrincipal")
    @Permission("agent:principal:edit")
    public RestResult<Map<String, Object>> editPrincipal(@Validated @RequestBody FlowStartDto<PrincipalEditVo> flowStartDto, @CurrentUser PmsOperator operator) {

        PrincipalEditVo principalEditVo = flowStartDto.getExtObj();

        boolean existNotFinishedFlow = flowFacade.isExistNotFinishedFlow( FlowTypeEnum.AGENT_SET_PRINCIPAL,principalEditVo.getAgentNo());
        if (existNotFinishedFlow) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("【变更合伙人负责人】有未完结审批");
        }

        com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(principalEditVo.getAgentNo());
        principalEditVo.setBeforeContactName(agent.getContactName());
        principalEditVo.setBeforeContactPhone(agent.getContactPhone());
        principalEditVo.setBeforeContactEmail(agent.getContactEmail());

        CommonFlow commonFlow = startFlow(operator, FlowTypeEnum.AGENT_SET_PRINCIPAL, principalEditVo.getAgentNo(), agent.getAgentName(), flowStartDto, principalEditVo);
        context.getBuilder(EditTypeEnum.AGENT_SET_PRINCIPAL.getType()).build(principalEditVo, operator, commonFlow.getId());

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);

    }

    @GetMapping("listActiveAgent")
    public RestResult<List<Agent>> listActiveAgent() {
        final List<Agent> agents = agentBiz.listActiveAgent();
        return RestResult.success(agents);
    }
}
