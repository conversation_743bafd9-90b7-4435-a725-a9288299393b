package com.zhixianghui.web.pms.vo.data;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName MainIndexRep
 * @Description TODO
 * @Date 2021/11/25 17:06
 */
@Data
public class MainIndexRep implements Serializable {

    private static final long serialVersionUID = 1330257407848453087L;

    private BigDecimal thisMonthMoney;

    private BigDecimal lastMonthMoney;

    private Long lastMonthNum;

    private Long thisMonthNum;

    private BigDecimal thisMonthProfit;

    private BigDecimal lastMonthProfit;

    private List<MoneyData> moneyData = new ArrayList<>();

    private List<NumData> numData = new ArrayList<>();

    private List<MoneyData> profitData = new ArrayList<>();

    //金额
    @Data
    public static class MoneyData implements Serializable{

        private Object key;

        private String name;
        //上个月
        private BigDecimal lastMonth = new BigDecimal(0);
        //本月
        private BigDecimal thisMonth = new BigDecimal(0);
    }

    //订单笔数
    @Data
    public static class NumData implements Serializable{

        private Object key;

        private String name;
        //上个月
        private Long lastMonth = 0L;
        //本月
        private Long thisMonth = 0L;
    }
}
