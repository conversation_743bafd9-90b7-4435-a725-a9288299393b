package com.zhixianghui.web.pms.controller.official;

import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.enums.message.EmailTemplateEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.common.constants.ReportConstants;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.pms.enums.OfficialBusinessTypeEnum;
import com.zhixianghui.web.pms.utils.NetUtil;
import com.zhixianghui.web.pms.vo.official.OfficialMailVo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName OfficialController
 * @Description TODO
 * @Date 2021/9/3 17:50
 */
@RestController
@RequestMapping("official")
@Log4j2
public class OfficialController {

    @Value("${official.email}")
    private String officialEmail;

    private final static String MAIL_KEY = "officialMail:";
    private final static int TIME_LIMIT = 1*60;

    @Reference
    private EmailFacade emailFacade;
    @Autowired
    private RedisClient redisClient;

    @PostMapping("sendMail")
    public RestResult sendMail(@RequestBody OfficialMailVo officialMailVo, HttpServletRequest httpServletRequest){

        String ip = NetUtil.getIpAddr(httpServletRequest);
        String hasbeenSend = redisClient.get(MAIL_KEY+ ip);
        if (!StringUtil.isEmpty(hasbeenSend)){
            return RestResult.error("邮件发送过于频繁,请稍后重试");
        }
        EmailParamDto paramDto = new EmailParamDto();
        paramDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        paramDto.setTo(officialEmail);
        paramDto.setSubject("【智享汇】智享汇合作申请");
        paramDto.setTpl(EmailTemplateEnum.REGISITER_APPLY.getName());
        Map<String, Object> emailParamMap = new HashMap<>();
        OfficialBusinessTypeEnum officialBusinessTypeEnum = OfficialBusinessTypeEnum.getEnum(officialMailVo.getType());
        switch (officialBusinessTypeEnum){
            case SERVICE:
                if (StringUtils.isBlank(officialMailVo.getBusinessName())){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("企业名称不能为空");
                }
                break;
            case AGENT:
                if (StringUtils.isBlank(officialMailVo.getIndustry())){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所在行业不能为空");
                }
                break;
            case MERCHANT:
                if (StringUtils.isBlank(officialMailVo.getBusinessName())){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("企业名称不能为空");
                }
                if (StringUtils.isBlank(officialMailVo.getIndustry())){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所在行业不能为空");
                }
        }
        emailParamMap.put("username",officialMailVo.getUsername());
        emailParamMap.put("businessType",officialBusinessTypeEnum.getDesc());
        emailParamMap.put("business", officialMailVo.getBusinessName());
        emailParamMap.put("industry",officialMailVo.getIndustry());
        emailParamMap.put("phone",officialMailVo.getPhone());
        emailParamMap.put("remark",officialMailVo.getRemark());
        emailParamMap.put("createTime", DateUtil.formatDateTime(new Date()));
        paramDto.setTplParam(emailParamMap);
        paramDto.setHtmlFormat(true);
        emailFacade.sendAsync(paramDto);
        //设置邮件发送限制
        redisClient.set(MAIL_KEY + ip,ip,TIME_LIMIT);
        return RestResult.success("邮件发送成功");
    }

}
