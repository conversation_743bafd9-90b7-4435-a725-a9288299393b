package com.zhixianghui.web.pms.biz.sign;

import com.alibaba.fastjson.JSONObject;
import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.TemplateComponent;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.service.SignTemplateFacade;
import com.zhixianghui.facade.trade.vo.DocTemplateAddOrEditVo;
import com.zhixianghui.facade.trade.vo.SignCustomizeTemplateVo;
import com.zhixianghui.facade.trade.vo.SignTemplateResVo;
import com.zhixianghui.facade.trade.vo.sign.StructStyle;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.sign.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/18 15:37
 **/
@Slf4j
@Service
public class SignBiz {
    @Reference
    private SignRecordFacade signRecordFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private SignTemplateFacade signTemplateFacade;


    public PageResult<List<SignRecordResVo>> listPage(SignRecordQueryVo queryVo, PageParam pageParam) {
        Map<String,Object> paramMap = BeanUtil.toMap(queryVo);
        PageResult<List<SignRecord>> pageResult = signRecordFacade.listPage(paramMap,pageParam);
        if(CollectionUtils.isEmpty(pageResult.getData())){
            return PageResult.newInstance(pageParam,null);
        }else {
            List<SignRecordResVo> list = pageResult.getData().stream().map(
                    signRecord ->{
                        SignRecordResVo signRecordResVo = new SignRecordResVo();
                        BeanUtils.copyProperties(signRecord,signRecordResVo);
                        signRecordResVo.setReceiveName(signRecord.getReceiveNameDesensitize());
                        signRecordResVo.setReceiveIdCardNo(signRecord.getReceiveIdCardNoDesensitize());
                        signRecordResVo.setReceivePhoneNo(signRecord.getReceivePhoneNoDesensitize());
                        if (StringUtils.isBlank(signRecord.getFileUrl())){
                            signRecordResVo.setHasFile(false);
                        }else{
                            signRecordResVo.setHasFile(true);
                        }
                        return signRecordResVo;
                    }
            ).collect(Collectors.toList());
            return PageResult.newInstance(list,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
        }
    }

    /**
     * 签约记录导出
     * @param queryVo
     * @param pmsOperator
     */
    public void exportSignRecord(SignRecordQueryVo queryVo, PmsOperator pmsOperator) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(queryVo);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());

        record.setFileName(ReportTypeEnum.SIGN_RECORD_PMS.getFileName());
        record.setReportType(ReportTypeEnum.SIGN_RECORD_PMS.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.SIGN_RECORD_PMS.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
    }

    public DocTemplateAddOrEditVo addTemplate(SignTemplateVo signTemplateVo) {
        return signTemplateFacade.addTemplate(signTemplateVo);
    }

    public SignCustomizeTemplateVo uploadTemplate(MultipartFile file) {
        byte[] fileBytes = null;
        try {
            fileBytes = file.getBytes();
        } catch (IOException e) {
            log.error("上传文件上传出错:");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件上传出错");
        }
        return uploadTemplate(file.getOriginalFilename(), fileBytes);
    }

    @Autowired
    private FastdfsClient fastdfsClient;
    // E:\work-grandview\灵活用工\template\
    private static final String TEMPLATE_FILE_PATH = "/tmp/";
    private static final String PDF_SUFFIX = ".pdf";
    private static final String SPERATOR = ".";

    public SignCustomizeTemplateVo uploadTemplate(String fileName, byte[] fileByte) {
        String realPath = TEMPLATE_FILE_PATH + fileName;
        log.info("初始路径:" + realPath);
        File file = new File(realPath);
        try (
                FileOutputStream fileInputStream = new FileOutputStream(file);
                BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileInputStream);
        )
        {
            bufferedOutputStream.write(fileByte);
        } catch (IOException e) {
            log.error("模板文件转换出错:" + e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板文件转换出错");
        }
        String suffix = fileName.substring(fileName.lastIndexOf(SPERATOR));
        if (PDF_SUFFIX.equalsIgnoreCase(suffix)) {
            String fileUrl = fastdfsClient.uploadFile(fileByte, fileName);
            // 删除本地pdf文件
            return new SignCustomizeTemplateVo(fileUrl, fileUrl, realPath);
        }
        String pdfFileName = fileName.substring(0, fileName.lastIndexOf(SPERATOR));
        // word 转 pdf, 返回pdf路径
        String wordPath = realPath;
        try {
            realPath = TEMPLATE_FILE_PATH + pdfFileName + PDF_SUFFIX;
            wordToPdf(file.getAbsolutePath(), realPath);
        } catch (Exception e) {
            log.error("文件格式错误:" + e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件格式错误");
        }
        String wordFileUrl = fastdfsClient.uploadFile(FileUtils.fileToByteArray(wordPath), fileName);
        String pdfFileUrl = fastdfsClient.uploadFile(FileUtils.fileToByteArray(realPath), pdfFileName + PDF_SUFFIX);
        // 删除本地word文件
        FileUtils.deleteDir(new File(wordPath));
        return new SignCustomizeTemplateVo(wordFileUrl, pdfFileUrl, realPath);
    }

    private void getLicense() throws Exception {
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream("License.xml")) {
            License license = new License();
            license.setLicense(is);
        }
    }

    /**
     * word转pdf
     *
     * @param wordPath word文件保存的路径
     * @param pdfPath  转换后pdf文件保存的路径
     */
    public void wordToPdf(String wordPath, String pdfPath) throws Exception {
        getLicense();

        File file = new File(pdfPath);
        try (FileOutputStream os = new FileOutputStream(file)) {
            Document doc = new Document(wordPath);
            doc.save(os, SaveFormat.PDF);
        }
    }

    public DocTemplateAddOrEditVo modify(SignTemplateVo signTemplateVo) {
        return signTemplateFacade.modify(signTemplateVo,signTemplateVo.getIsChangeFile());
    }

    public PageResult<List<SignTemplateResVo>> list(SignCustomizeTemplateVo queryVo) {
        return signTemplateFacade.list(queryVo);
    }

    public boolean del(Long id) {
        return signTemplateFacade.del(id);

    }

    public List<ComponentVo> componentList() {
        List<TemplateComponent> result = signTemplateFacade.componentList();
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        List<ComponentVo> componentVoList = new ArrayList<>();
        result.forEach(item -> {
            ComponentVo vo = new ComponentVo();
            BeanUtil.copyProperties(item, vo);
            vo.setStructStyle(JSONObject.parseObject(item.getStyle(), StructStyle.class));
            vo.setBehavior(JSONObject.parseArray(item.getHandles(), String.class));
            componentVoList.add(vo);
        });
        return componentVoList;
    }

    public void addSignImages(AddSignImagesVo addSignImagesVo) {
        SignRecord signRecord = signRecordFacade.getById(addSignImagesVo.getSignId());
        if (StringUtils.isNotBlank(addSignImagesVo.getCerFaceUrl())) {
            signRecord.setCerFaceUrl(addSignImagesVo.getCerFaceUrl());
        }
        if (StringUtils.isNotBlank(addSignImagesVo.getIdCardFrontUrl())) {
            signRecord.setIdCardFrontUrl(addSignImagesVo.getIdCardFrontUrl());
        }
        if (StringUtils.isNotBlank(addSignImagesVo.getIdCardBackUrl())) {
            signRecord.setIdCardBackUrl(addSignImagesVo.getIdCardBackUrl());
        }
        if (StringUtils.isNotBlank(addSignImagesVo.getIdCardCopyFileUrl())) {
            signRecord.setIdCardCopyUrl(addSignImagesVo.getIdCardCopyFileUrl());
        }
        if (StringUtils.isNotBlank(addSignImagesVo.getSignatureUrl())) {
            addSignImagesVo.setSignatureUrl(addSignImagesVo.getSignatureUrl());
        }
        signRecord.setIdCardType(addSignImagesVo.getIdCardType());
        signRecordFacade.addSignImages(signRecord);
    }

    public void deleteRecordById(Long id) {
        signRecordFacade.deleteById(id);
    }

    /**
     * 线下签约上传文件
     * @param signRecord
     */
    public void uploadFile(SignRecord signRecord) {
        SignRecord record = signRecordFacade.getById(signRecord.getId());
        if (record == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约记录不存在");
        }
        if (StringUtils.isBlank(signRecord.getFileUrl())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议文件不能为空");
        }

        record.setFileUrl(signRecord.getFileUrl());
        record.setUpdateTime(new Date());
        signRecordFacade.update(record);
    }
}
