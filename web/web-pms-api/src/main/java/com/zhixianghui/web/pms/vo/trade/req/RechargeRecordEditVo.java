package com.zhixianghui.web.pms.vo.trade.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class RechargeRecordEditVo {

    /**
     * 充值订单号
     */
    @NotBlank(message = "充值订单号不能为空")
    private String rechargeOrderId;
    /**
     * 金额
     */
    @NotBlank(message = "金额不能为空")
    private String rechargeAmount;
    /**
     * 回单地址
     */
    private String receiptUrl;
    /**
     * 备注
     */
    private String remark;

    /**
     * 订单状态
     */
    private Integer rechargeStatus;

    /**
     * 通知商户
     */
    private Boolean emailNotify;

}
