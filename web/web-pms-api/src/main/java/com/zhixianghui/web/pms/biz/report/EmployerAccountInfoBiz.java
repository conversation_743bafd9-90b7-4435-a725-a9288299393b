package com.zhixianghui.web.pms.biz.report;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.common.dto.EmployerAccountInfoDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.vo.report.EmployerAccountInfoVo;
import com.zhixianghui.web.pms.vo.report.SubChannelInfoVo;
import com.zhixianghui.web.pms.vo.report.req.EmployerAccountInfoQueryVo;
import com.zhixianghui.web.pms.vo.report.res.EmployerAccountAmountVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-29 10:58
 **/
@Service
@Slf4j
public class EmployerAccountInfoBiz {
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private PayChannelFacade payChannelFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;

    public PageResult<List<EmployerAccountInfoDto>> listPage(EmployerAccountInfoQueryVo queryVo, PageParam pageParam) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayName", queryVo.getMainstayName());
        paramMap.put("mainstayNameLike", queryVo.getMainstayNameLike());
        paramMap.put("mainstayNo",queryVo.getMainstayNo());
        paramMap.put("mchNameLike", queryVo.getMchNameLike());
        paramMap.put("employerNo", queryVo.getEmployerNo());
        paramMap.put("channelNo",queryVo.getPayChannelNo());
        pageParam.setSortColumns("CREATE_TIME DESC,CHANNEL_TYPE ASC");
        return employerAccountInfoFacade.listCustomPage(paramMap,pageParam);
    }

    public void update(EmployerAccountInfoVo vo, PmsOperator currentOperator) {
        // 获取通道状态 用于校验
        Map<String,Integer> channelMap =  payChannelFacade.listAll().stream()
                .collect(Collectors.toMap(PayChannel::getPayChannelNo, PayChannel::getStatus));

        // 获取供应商-通道状态 用于校验
        Map<String,Integer> channelRelationMap = mainstayChannelRelationFacade.listBy(Collections.singletonMap("mainstayNo",vo.getMainstayNo()))
                .stream().collect(Collectors.toMap(x->x.getMainstayNo() + x.getPayChannelNo(), MainstayChannelRelation::getStatus));

        //组装前端信息
        Map<String, EmployerAccountInfo> accountMap = vo.getChannelInfos().stream().map(
                channelInfo -> {
                    //想开就要校验
                    if(channelInfo.getStatus().equals(OpenOffEnum.OPEN.getValue())){
                        //校验支付通道状态
                        Integer channelStatus = channelMap.get(channelInfo.getPayChannelNo());
                        if(channelStatus == null){
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("选择开启通道,无法查询此通道状态，通道编号：" + channelInfo.getPayChannelNo());
                        }
                        if (channelStatus.equals(OpenOffEnum.OFF.getValue())){
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道状态为禁用，请先启用支付通道, 通道编号：" + channelInfo.getPayChannelNo());
                        }

                        //校验供应商-支付通道状态
                        Integer channelRelationStatus = channelRelationMap.get(vo.getMainstayNo() + channelInfo.getPayChannelNo());
                        if(channelRelationStatus == null){
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("选择开启通道,无法查询供应商-支付通道状态，通道编号：" + channelInfo.getPayChannelNo() + ",供应商编号" + vo.getMainstayNo());
                        }
                        if (channelRelationStatus.equals(OpenOffEnum.OFF.getValue())){
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该供应商-支付通道为禁用，请先启用, 通道编号：" + channelInfo.getPayChannelNo() + ",供应商编号" + vo.getMainstayNo());
                        }
                    }

                    EmployerAccountInfo employerAccountInfo = new EmployerAccountInfo();
                    employerAccountInfo.setMchName(vo.getMchName());
                    employerAccountInfo.setEmployerName(channelInfo.getEmployerName());
                    employerAccountInfo.setEmployerNo(vo.getEmployerNo());
                    employerAccountInfo.setMainstayName(vo.getMainstayName());
                    employerAccountInfo.setMainstayNo(vo.getMainstayNo());
                    employerAccountInfo.setUpdateOperator(currentOperator.getRealName());
                    employerAccountInfo.setUpdateTime(new Date());
                    BeanUtils.copyProperties(channelInfo,employerAccountInfo);
                    return employerAccountInfo;
                }
        ).collect(Collectors.toMap(x->x.getMainstayNo() + x.getEmployerNo() + x.getChannelType(),EmployerAccountInfo::getEmployerAccountInfo));

        // 获取旧的数据 用于更新
        List<EmployerAccountInfo> accountList = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(vo.getEmployerNo(),vo.getMainstayNo());

        //替换数据
        accountList.forEach(
                account -> {
                    EmployerAccountInfo updateAccount = accountMap.get(account.getMainstayNo() + account.getEmployerNo() + account.getChannelType());
                    updateAccount.setId(account.getId());
                    updateAccount.setVersion(account.getVersion());
                    updateAccount.setCreateOperator(account.getCreateOperator());
                    updateAccount.setCreateTime(account.getCreateTime());
                    updateAccount.setParentAlipayUserId(account.getParentAlipayUserId());
                    updateAccount.setSubAlipayUserId(account.getSubAlipayUserId());
                    updateAccount.setAlipayCardNo(account.getAlipayCardNo());
                    BeanUtils.copyProperties(updateAccount,account);
                }
        );
        employerAccountInfoFacade.batchUpdate(accountList);
    }

    public EmployerAccountInfoVo getByMainstayNoAndEmployerNo(String mainstayNo, String employerNo) {
        List<EmployerAccountInfo> list = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(employerNo,mainstayNo);
        if(ObjectUtils.isEmpty(list) || list.get(0) == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(
                    "该用工企业在相关供应商下无信息，请先建立代征关系，供应商编号：" + mainstayNo + ",用工企业编号：" + employerNo);
        }
        EmployerAccountInfoVo employerAccountInfoVo = new EmployerAccountInfoVo();
        BeanUtils.copyProperties(list.get(0), employerAccountInfoVo);

        List<SubChannelInfoVo> channelList = list.stream().map(
                record -> {
                    SubChannelInfoVo subChannelInfoVo = new SubChannelInfoVo();
                    BeanUtils.copyProperties(record, subChannelInfoVo);
                    return subChannelInfoVo;
                }
        ).collect(Collectors.toList());
        employerAccountInfoVo.setChannelInfos(channelList);
        return employerAccountInfoVo;
    }

    public EmployerAccountAmountVo getAmountByMainstayNoAndEmployerNo(String mainstayNo, String employerNo) {
        EmployerAccountAmountVo employerAccountAmountVo = new EmployerAccountAmountVo();
        //查询账户信息
        EmployerAccountInfoVo employerAccountInfoVo = getByMainstayNoAndEmployerNo(mainstayNo,employerNo);
        //只查开启的通道余额
        employerAccountInfoVo.getChannelInfos().stream()
                .filter(channelInfo->channelInfo.getStatus().equals(OpenOffEnum.OPEN.getValue()))
                .forEach(
                        channelInfo->{
                            Integer channelType = channelInfo.getChannelType();
                            String amount;
                            try {
                                if (channelInfo.getStatus().equals(OpenOffEnum.OFF.getValue())){
                                    amount = "未开通";
                                }else {
                                    amount = getAmount(channelInfo,employerAccountInfoVo,channelType);
                                    if(StringUtils.isEmpty(amount)){
                                        log.error("[{}]余额查询异常,通道：{}",employerNo,channelInfo.getChannelType());
                                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询异常");
                                    }
                                }
                            }catch (Exception e){
                                amount = "通道余额接口异常";
                            }
                            if (channelType.equals(ChannelTypeEnum.BANK.getValue())) {
                                employerAccountAmountVo.setBankAmount(amount);
                            }else if (channelType.equals(ChannelTypeEnum.ALIPAY.getValue())){
                                employerAccountAmountVo.setAliPayAmount(amount);
                            }else if (channelType.equals(ChannelTypeEnum.WENXIN.getValue())){
                                employerAccountAmountVo.setWeixinAmount(amount);
                            }
                        }
                );
        return employerAccountAmountVo;
    }

    public Map<String, Object> getAmountByEmployerNoAnyMainstayNo(String mainstayNo, String employerNo) {
        Map<String,Object> resultMap = new HashMap<>();
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("employerNo",employerNo);
//        paramMap.put("status",OpenOffEnum.OPEN.getValue());
        List<EmployerMainstayRelation> employerMainstayRelationList = employerMainstayRelationFacade.listBy(paramMap);
        List<Map<String,Object>> amountList = new ArrayList<>();
        employerMainstayRelationList.forEach(employerMainstayRelation -> {
            EmployerAccountInfoVo employerAccountInfoVo = getByMainstayNoAndEmployerNo(employerMainstayRelation.getMainstayNo(),employerNo);
            Map<String,Object> infoMap = new HashMap<>();
            infoMap.put("mainstayName",employerAccountInfoVo.getMainstayName());
            infoMap.put("mainstayNo",employerAccountInfoVo.getMainstayNo());
            employerAccountInfoVo.getChannelInfos().forEach(subChannelInfoVo -> {
                Integer channelType = subChannelInfoVo.getChannelType();
                String jxhOpen="OFF"; //君享汇是否开通 ON开通 OFF未开通
                String amount;
                try {
                    if (subChannelInfoVo.getStatus().equals(OpenOffEnum.OFF.getValue())){
                        amount = "未开通";
                    }else {
                        amount = getAmount(subChannelInfoVo,employerAccountInfoVo,channelType);
                        if(StringUtils.isEmpty(amount)){
                            log.error("[{}]余额查询异常,通道：{}",employerNo,subChannelInfoVo.getChannelType());
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询异常");
                        }
                    }
                }catch (Exception e){
                    amount = "异常";
                }
                if (channelType.equals(ChannelTypeEnum.BANK.getValue())) {
                    infoMap.put("bankAmount",amount);
                    if (org.apache.commons.lang3.StringUtils.equals(subChannelInfoVo.getPayChannelNo(),
                            ChannelNoEnum.JOINPAY_JXH.name())){
                        jxhOpen="ON";
                    }
                    infoMap.put("jxhOpen",jxhOpen);
                }else if (channelType.equals(ChannelTypeEnum.ALIPAY.getValue())){
                    infoMap.put("aliPayAmount",amount);
                }else if (channelType.equals(ChannelTypeEnum.WENXIN.getValue())){
                    infoMap.put("weixinAmount",amount);
                }
            });
            amountList.add(infoMap);
        });
        resultMap.put("amountList",amountList);
        return resultMap;
    }

    /**
     * 获取各通道余额
     * @param channelInfo
     * @param employerAccountInfoVo
     * @param channelType
     * @return
     */
    private String getAmount(SubChannelInfoVo channelInfo,EmployerAccountInfoVo employerAccountInfoVo,Integer channelType) throws Exception{
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setMainstayNo(employerAccountInfoVo.getMainstayNo());
        amountQueryDto.setEmployerNo(employerAccountInfoVo.getEmployerNo());
        amountQueryDto.setChannelType(channelType);
        amountQueryDto.setChannelNo(channelInfo.getPayChannelNo());
        amountQueryDto.setChannelMchNo(channelInfo.getParentMerchantNo());
        amountQueryDto.setSubMerchantNo(channelInfo.getSubMerchantNo());
        amountQueryDto.setAgreementNo(channelInfo.getSubAgreementNo());
        return accountQueryFacade.getAmount(amountQueryDto);
    }

    public PageResult<List<Map<String,String>>> groupByMch(Map<String, Object> paramMap,PageParam pageParam) {
        return employerAccountInfoFacade.groupByMch(paramMap,pageParam);
    }

    public void getCardNo() {
        employerAccountInfoFacade.getAlipayCardNo();
    }
}
