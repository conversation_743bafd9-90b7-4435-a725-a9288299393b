package com.zhixianghui.web.pms.controller.data;

import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.data.service.CkOrderFacade;
import com.zhixianghui.facade.data.vo.MainIndexExportDataVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.MerchantInfoVo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.common.ParamHelper;
import com.zhixianghui.web.pms.biz.file.ExportBiz;
import com.zhixianghui.web.pms.enums.MainIndexExportEnum;
import com.zhixianghui.web.pms.vo.data.MerchantExcelModelVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ExportController
 * @Description TODO
 * @Date 2022/2/25 10:26
 */
@RestController
public class ExportController {

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    @Reference
    private OrderItemFacade orderItemFacade;

    @Reference
    private CkOrderFacade ckOrderFacade;

    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;

    @Autowired
    private ExportBiz exportBiz;
    @Autowired
    private ParamHelper paramHelper;

    @GetMapping("/download/exportData")
    public void exportData(@RequestParam Integer reportType, @RequestParam String endDate, HttpServletResponse response, @CurrentUser PmsOperator pmsOperator){
        //获取时间
        Date now = DateUtil.parse(endDate);
        Date lastDate = DateUtil.addMonth(now,-1);
        Map<String,Object> nowDateMap = MainIndexController.getDate(now);
        Map<String,Object> lastDateMap = MainIndexController.getDate(lastDate);
        this.paramHelper.hanldSaler(pmsOperator, nowDateMap);
        this.paramHelper.hanldSaler(pmsOperator, lastDateMap);
        List data = new ArrayList();
        String fileName = "";
        if (reportType == MainIndexExportEnum.MERCHANT_EXPORT.getValue().intValue()){
            data = exportMerchantData(now,nowDateMap,lastDateMap,pmsOperator);
            fileName = "商户数据【" + DateUtil.formatCompactDate(DateUtil.getFirstOfMonth(now))  + "-" + DateUtil.formatCompactDate(now) + "】";
        }
        exportBiz.export(response,fileName,data, MerchantExcelModelVo.class);
    }

    private List exportMerchantData(Date now,Map<String, Object> nowDateMap, Map<String, Object> lastDateMap,PmsOperator pmsOperator) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchStatus", MchStatusEnum.ACTIVE.getValue());
        paramMap.put("merchantType", MerchantTypeEnum.EMPLOYER.getValue());
        this.paramHelper.hanldSaler(pmsOperator, paramMap);
        //查询所有激活商户数据
        Map<String,MerchantInfoVo> merchantMap = merchantQueryFacade.listAll(paramMap).stream().collect(Collectors.toMap(obj->obj.getMchNo(),obj->obj));
        //查询所有启用的代征关系
        List<EmployerMainstayRelation> employerMainstayRelationList = employerMainstayRelationFacade.listBy(new HashMap<String,Object>(){{put("status", OpenOffEnum.OPEN.getValue());}});
        //查询所有订单发放数据
        Map<String, MainIndexExportDataVo> thisMonth = ckOrderFacade.mapAmountWithMch(nowDateMap);
        Map<String,MainIndexExportDataVo> lastMonth = ckOrderFacade.mapAmountWithMch(lastDateMap);
        List<MerchantExcelModelVo> voList = new ArrayList<>();
        String month = DateUtil.dateYearMonthFormatter(now);
        employerMainstayRelationList.stream().forEach(x->{
            MerchantInfoVo merchantInfoVo = merchantMap.get(x.getEmployerNo());
            if (merchantInfoVo != null){
                MerchantExcelModelVo merchantExcelModelVo = new MerchantExcelModelVo();
                merchantExcelModelVo.setMchNo(merchantInfoVo.getMchNo());
                merchantExcelModelVo.setMchName(merchantInfoVo.getMchName());
                merchantExcelModelVo.setMonth(month);
                merchantExcelModelVo.setSalerName(merchantInfoVo.getSalerName());
                merchantExcelModelVo.setActiveTime(merchantInfoVo.getActiveTime());
                merchantExcelModelVo.setMainstayNo(x.getMainstayNo());
                merchantExcelModelVo.setMainstayName(x.getMainstayName());
                merchantExcelModelVo.setBranchName(merchantInfoVo.getBranchName());
                if (StringUtils.isBlank(merchantInfoVo.getAgentNo())){
                    merchantExcelModelVo.setHasAgent(YesNoCodeEnum.NO.getDesc());
                }else{
                    merchantExcelModelVo.setHasAgent(YesNoCodeEnum.YES.getDesc());
                }
                //处理金额信息
                MainIndexExportDataVo thisMonthVo = thisMonth.get(x.getEmployerNo() + "-" + x.getMainstayNo());
                if (thisMonthVo != null){
                    merchantExcelModelVo.setThisMonth(thisMonthVo.getTotalNetAmount());
                    merchantExcelModelVo.setBusinessIncome(thisMonthVo.getTotalBusinessProfit());
                    merchantExcelModelVo.setThisMonthSalesProfit(thisMonthVo.getTotalSalesProfit());
                    merchantExcelModelVo.setAgentProfit(thisMonthVo.getTotalAgentProfit());
                }
                MainIndexExportDataVo lastMonthVo = lastMonth.get(x.getEmployerNo() + "-" + x.getMainstayNo());
                if (lastMonthVo != null){
                    merchantExcelModelVo.setLastMonth(lastMonthVo.getTotalNetAmount());
                    merchantExcelModelVo.setLastMonthSalesProfit(lastMonthVo.getTotalSalesProfit());
                }
                merchantExcelModelVo.setBalance(merchantExcelModelVo.getThisMonth().subtract(merchantExcelModelVo.getLastMonth()));
                voList.add(merchantExcelModelVo);
            }
        });
        List<MerchantExcelModelVo> sortedList = voList.stream().sorted(Comparator.comparing(MerchantExcelModelVo::getThisMonth).reversed()).collect(Collectors.toList());
        return sortedList;
    }
}
