package com.zhixianghui.web.pms.controller.report;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.report.ReportChannelRecord;
import com.zhixianghui.facade.common.service.ReportChannelRecordFacade;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.report.req.ReportChannelRecordQueryVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 通道报备记录
 * @date 2020-10-21 09:20
 **/
@RestController
@RequestMapping("reportChannelRecord")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReportChannelRecordController {

    @Reference
    private ReportChannelRecordFacade reportChannelRecordFacade;

    /**
     * 分页查询通道报备记录
     * @param queryVo 查询条件
     * @param pageVo 分页参数
     * @return 通道报备记录
     */
    @Permission("report:reportChannelRecord:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<ReportChannelRecord>>> listPage(@RequestBody ReportChannelRecordQueryVo queryVo,@RequestBody PageVo pageVo) {
        Map<String,Object> paramMap = BeanToMapUtil.beanToMap(queryVo);
        PageResult<List<ReportChannelRecord>> pageResult = reportChannelRecordFacade.listPage(paramMap,pageVo.toPageParam("CREATE_TIME DESC"));
        return RestResult.success(pageResult);
    }

}
