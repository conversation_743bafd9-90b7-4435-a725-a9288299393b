package com.zhixianghui.web.pms.biz.merchant;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import com.google.common.collect.Maps;
import com.google.gson.JsonArray;
import com.zhixianghui.common.statics.enums.bankLink.sign.SignChannelStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.*;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.banklink.service.sign.ChannelSignFacade;
import com.zhixianghui.facade.banklink.vo.sign.EsignResVo;
import com.zhixianghui.facade.banklink.vo.sign.createFlow.RevokeFlowVo;
import com.zhixianghui.facade.banklink.vo.sign.getTemplate.*;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.AgreementSignModeEnum;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.vo.AgreementDto;
import com.zhixianghui.facade.merchant.vo.AgreementResVo;
import com.zhixianghui.facade.trade.service.ESignFacade;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.pms.vo.merchant.agreement.AgreementQueryVo;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.merchant.agreement.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-03 15:10
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementBiz {

    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private AgreementFacade agreementFacade;
    @Reference
    private AgreementTemplateFacade agreementTemplateFacade;
    @Reference
    private AgreementFileFacade agreementFileFacade;
    @Reference
    private AgreementSignerFacade agreementSignerFacade;
    @Reference
    private ChannelSignFacade channelSignFacade;
    @Autowired
    private FastdfsClient fastdfsClient;

    protected final static String FILE_SUFFIX = ".pdf";


    public void createAgreement(AgreementReqVo agreementReqVo, PmsOperator currentOperator) {
        AgreementDto agreementDto = new AgreementDto();
        BeanUtils.copyProperties(agreementReqVo, agreementDto);

        if (!CollectionUtils.isEmpty(agreementReqVo.getComponents())){
            agreementDto.setComponentsJson(JsonUtil.toString(agreementReqVo.getComponents()));
        }

        //校验签署模式
        if (agreementReqVo.getSignMode().intValue() == AgreementSignModeEnum.SINGLE_SIGN.getValue() && agreementReqVo.getSingleSignerType() == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("单方签署必须选择签署方");
        }

        //校验协议文件
        if ((StringUtils.isBlank(agreementReqVo.getFileTemplateId()) && agreementReqVo.getFileVoList().size() == 0)
        || (agreementReqVo.getSignType().intValue() == AgreementSignTypeEnum.OFFLINE.getValue() && agreementReqVo.getFileVoList().size() == 0)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议文件不能为空");
        }

        if (StringUtils.isBlank(agreementReqVo.getFileTemplateId()) && agreementReqVo.getSignType().intValue() == AgreementSignTypeEnum.ONLINE.getValue()){
            if (agreementReqVo.getComponents() == null || agreementReqVo.getComponents().size() <= 0){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("至少需要添加一个签署控件");
            }

            if (AgreementSignModeEnum.MULTIPLE_SIGN.getValue() == agreementReqVo.getSignMode().intValue()){
                if (agreementReqVo.getComponents().stream().noneMatch(x->x.getContext().getLabel().equals("甲方签署区"))){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("'甲方'未添加签署控件");
                }


                if (agreementReqVo.getComponents().stream().noneMatch(x->x.getContext().getLabel().equals("乙方签署区"))){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("'乙方'未添加签署控件");
                }
            }else{
                if (agreementReqVo.getComponents().stream().anyMatch(x->x.getContext().getLabel().equals("甲方签署区")) &&
                        agreementReqVo.getComponents().stream().anyMatch(x->x.getContext().getLabel().equals("乙方签署区"))){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("单方签署仅允许添加一方签署区");
                }
            }
        }

        //其余属性处理
        agreementDto.setCreateOperator(currentOperator.getRealName());
        agreementDto.setUpdateOperator(currentOperator.getRealName());
        agreementDto.setStatus(AgreementStatusEnum.CREATING.getValue());

        //签署人列表
        StringBuilder signerName = new StringBuilder();
        List<AgreementSigner> signerList = agreementReqVo.getSignerVoList().stream().map(
                singerVo ->{
                    if(StringUtils.isNotBlank(singerVo.getSignerMchName())){
                        signerName.append(singerVo.getSignerMchName());
                        signerName.append("、");
                    }
                    if (singerVo.getSignerType().intValue() == AgreementSignerTypeEnum.FIRST_PARTY.getValue()){
                        String mchNo = singerVo.getSignerNo();
                        Merchant merchant = merchantFacade.getByMchNo(mchNo);
                        if (merchant.getAuthStatus().intValue() != AuthStatusEnum.SUCCESS.getValue()){
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该商户未完成主体认证");
                        }
                    }
                        return AgreementSignerVo.toEntity(singerVo);
                }
        ).collect(Collectors.toList());
        agreementDto.setSignerName(signerName.toString());
        agreementDto.setSignerList(signerList);

        //文件列表
        if ((agreementDto.getSignType().intValue() == AgreementSignTypeEnum.ONLINE.getValue() && StringUtils.isBlank(agreementReqVo.getFileTemplateId()))
                || agreementDto.getSignType().intValue() == AgreementSignTypeEnum.OFFLINE.getValue()){
            List<AgreementFile> fileList = agreementReqVo.getFileVoList().stream()
                    .map(AgreementFileVo::toEntity).collect(Collectors.toList());
            agreementDto.setFileList(fileList);
        }
        agreementFacade.createAgreement(agreementDto);
    }

    public void archiveAgreement(Long agreementId, List<AgreementFileVo> fileVoList, PmsOperator currentOperator) {
        List<AgreementFile> fileList = fileVoList.stream().map(
                vo -> {
                    if(StringUtils.isBlank(vo.getFileName())|| StringUtils.isBlank(vo.getFileUrl())){
                        throw CommonExceptions.PARAM_INVALID.newWithErrMsg("文件信息不完整");
                    }
                    AgreementFile agreementFile = AgreementFileVo.toEntity(vo);
                    agreementFile.setAgreementId(agreementId);
                    agreementFile.setType(AgreementFileTypeEnum.ARCHIVE.getValue());
                    return agreementFile;
                }
        ).collect(Collectors.toList());
        agreementFacade.archive(fileList,currentOperator.getRealName());
    }

    public void createTemplate(AgreementReqVo agreementReqVo, PmsOperator currentOperator) {
        AgreementTemplate agreementTemplate = new AgreementTemplate();
        BeanUtils.copyProperties(agreementReqVo, agreementTemplate);
        agreementTemplate.setUpdateTime(new Date());
        agreementTemplate.setCreateOperator(currentOperator.getRealName());
        agreementTemplate.setUpdateOperator(currentOperator.getRealName());
        //签署人
        agreementTemplate.setSignerTemp(JSON.toJSONString(agreementReqVo.getSignerVoList()));
        //协议文件
        agreementTemplate.setFileTemp(JSON.toJSONString(agreementReqVo.getFileVoList()));
        agreementTemplateFacade.createAgreementTemplate(agreementTemplate);
    }

    public AgreementResVo getAgreementById(Long id) {
        AgreementResVo agreementResVo = new AgreementResVo();
        Agreement agreement = agreementFacade.getAgreementById(id);
        if(agreement == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("找不到对应协议,协议id:" + id);
        }
        BeanUtils.copyProperties(agreement,agreementResVo);
        agreementResVo.setFileList(agreementFileFacade.listByAgreementId(id));
        agreementResVo.setSignerList(agreementSignerFacade.listByAgreementId(id));
        return agreementResVo;
    }

    public AgreementTemplateResVo getTemplateById(Long id) {
        AgreementTemplateResVo agreementTemplateResVo = new AgreementTemplateResVo();
        AgreementTemplate agreementTemplate = agreementTemplateFacade.getById(id);
        BeanUtils.copyProperties(agreementTemplate,agreementTemplateResVo);
        agreementTemplateResVo.setFileVoList(JSON.parseArray(agreementTemplate.getFileTemp(), AgreementFileVo.class));
        agreementTemplateResVo.setSignerVoList(JSON.parseArray(agreementTemplate.getSignerTemp(),AgreementSignerVo.class));
        return agreementTemplateResVo;
    }

    public PageResult<List<AgreementResVo>> listAgreementPage(AgreementQueryVo agreementQueryVo, PageVo pageVo){
        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(agreementQueryVo);
        PageResult<List<Agreement>> pageResult = agreementFacade.listPage(paramMap,pageVo.toPageParam());
        List<AgreementResVo> list = pageResult.getData().stream().map(
                agreement -> {
                    AgreementResVo agreementResVo = new AgreementResVo();
                    BeanUtils.copyProperties(agreement,agreementResVo);
                    return agreementResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord());
    }

    public PageResult<List<AgreementTemplateResVo>> listTemplatePage(AgreementQueryVo agreementQueryVo, PageVo pageVo) {
        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(agreementQueryVo);
        PageResult<List<AgreementTemplate>> pageResult = agreementTemplateFacade.listPage(paramMap,pageVo.toPageParam());
        List<AgreementTemplateResVo> list = pageResult.getData().stream().map(
                template -> {
                    AgreementTemplateResVo templateResVo = new AgreementTemplateResVo();
                    BeanUtils.copyProperties(template,templateResVo);
                    return templateResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord());
    }

    public void editAgreement(Long agreementId, Date deadline, Date expireTime, PmsOperator currentOperator) {
        agreementFacade.editAgreement(agreementId, deadline, expireTime, currentOperator.getRealName());
    }

    public void editTemplate(AgreementReqVo agreementReqVo, PmsOperator currentOperator) {
        if(agreementReqVo.getId() == null){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("id不能为空");
        }
        AgreementTemplate agreementTemplate = agreementTemplateFacade.getById(agreementReqVo.getId());
        if(ObjectUtils.isEmpty(agreementTemplate)){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此模板");
        }
        BeanUtils.copyProperties(agreementReqVo,agreementTemplate);
        agreementTemplate.setId(agreementReqVo.getId());
        agreementTemplate.setSignerTemp(JSON.toJSONString(agreementReqVo.getSignerVoList()));
        agreementTemplate.setFileTemp(JSON.toJSONString(agreementReqVo.getFileVoList()));
        agreementTemplate.setUpdateOperator(currentOperator.getRealName());
        agreementTemplate.setUpdateTime(new Date());
        agreementTemplateFacade.updateAgreementTemplate(agreementTemplate);
    }

    public void cancelAgreement(List<Long> idsList) {
        agreementFacade.cancelAgreement(idsList);
    }

    public List<String> listArchiveFileUrl(Long id) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("agreementId",id);
        paramMap.put("type",AgreementFileTypeEnum.ARCHIVE.getValue());
        List<AgreementFile> agreementFiles = agreementFileFacade.listBy(paramMap);
        return agreementFiles.stream().map(AgreementFile::getFileUrl).collect(Collectors.toList());
    }

    public void syncTemplate(PmsOperator pmsOperator) {
        String tempPath = System.getProperty("user.dir") + File.separator;
        //删除所有旧模板
        agreementTemplateFacade.deleteAll();
        //同步查询E签宝模板列表
        EsignPageVo esignPageVo = new EsignPageVo();
        int total = 0;
        do {
            EsignResVo<TemplatePageResDataResVo> page = channelSignFacade.getTemplatePage(esignPageVo);
            if (page.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("同步模板文件失败");
            }
            CompletableFuture.runAsync(()->{
                for (FlowTemplateInfoResDataVo x : page.getData().getDocTemplates()) {
                    doAsync(pmsOperator,x,tempPath);
                }
            });
        }while (total > (esignPageVo.getPageNum() * esignPageVo.getPageSize()));
    }

    private void doAsync(PmsOperator pmsOperator,FlowTemplateInfoResDataVo x,String tempPath) {
        //获取详细模板信息-固定只有一个模板
        SignTemplateReqVo templateReqVo = new SignTemplateReqVo(x.getDocTemplateId());
        EsignResVo<SignTemplateResDataVoV3> signTemplateResDataVoEsignResVo = channelSignFacade.getSignTemplate(templateReqVo);
        if (signTemplateResDataVoEsignResVo.getRespStatus() != SignChannelStatusEnum.SUCCESS.getValue()){
            log.error("获取模板失败，流程模板id：[{}]，文件模板id：[{}]",x.getDocTemplateId(),templateReqVo.getTemplateId());
            return;
        }
        SignTemplateResDataVoV3 signTemplateResDataVo = signTemplateResDataVoEsignResVo.getData();
        //外链会超时，下载到本地并上传到fastdfs
        String fullPath = tempPath + signTemplateResDataVo.getDocTemplateId()
                + File.separator + signTemplateResDataVo.getDocTemplateName()+FILE_SUFFIX;
        //下载并上传模板文件
        File saveFile = FileUtils.createFile(fullPath);
        HttpUtil.downloadFile(signTemplateResDataVo.getFileDownloadUrl(), saveFile);
        //上传文件到fastdfs
        String url = fastdfsClient.uploadFile(fullPath,signTemplateResDataVo.getDocTemplateName()+FILE_SUFFIX);
        //插入到数据库
        AgreementTemplate agreementTemplate = new AgreementTemplate();
        agreementTemplate.setCreateTime(new Date());
        agreementTemplate.setUpdateTime(new Date());
        agreementTemplate.setTopic(x.getDocTemplateName());
        agreementTemplate.setUpdateOperator(pmsOperator.getRealName());
        agreementTemplate.setCreateOperator(pmsOperator.getRealName());
        agreementTemplate.setFlowTemplateId(x.getDocTemplateId());
        agreementTemplate.setFileTemplateId(signTemplateResDataVo.getDocTemplateId());
        agreementTemplate.setTemplateFileUrl(url);
        agreementTemplateFacade.createAgreementTemplate(agreementTemplate);
    }

    public PageResult<List<AgreementResVo>> getAgreementPage(AgreementQueryVo agreementQueryVo, PageVo pageVo,List<String> mchNoList) {
        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(agreementQueryVo);
        paramMap.put("signerNoList",agreementQueryVo.getSignerNoList());

        if (!CollectionUtils.isEmpty(mchNoList)){
            paramMap.put("mchNoList", mchNoList);
        }

        PageResult<List<Agreement>> pageResult = agreementFacade.listPage(paramMap,pageVo.toPageParam());
        List<AgreementResVo> list = pageResult.getData().stream().map(
                agreement -> {
                    AgreementResVo agreementResVo = new AgreementResVo();
                    BeanUtils.copyProperties(agreement,agreementResVo);
                    //查询协议方
                    List<AgreementSigner> signerList = agreementSignerFacade.listByAgreementId(agreement.getId());
                    //查询协议文件
                    List<AgreementFile> fileList = agreementFileFacade.listByAgreementId(agreement.getId());
                    agreementResVo.setSignerList(signerList);
                    agreementResVo.setFileList(fileList);

                    AgreementFile agreementFile = fileList.stream().filter(x->x.getType().intValue() == AgreementFileTypeEnum.COMMON_FILE.getValue()).findFirst().orElse(null);
                    if (agreementFile != null){
                        agreementResVo.setFileUrl(agreementFile.getFileUrl());
                    }
                    return agreementResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord());
    }

    public void deleteAgreement(List<Long> idList) {
        agreementFacade.deleteAgreement(idList);
    }

    public void turnToOffline(List<Long> idList) {
        agreementFacade.turnToOffline(idList);
    }

    public void delay(AgreementHandleReqVo agreementHandleReqVo) {
        agreementFacade.delay(agreementHandleReqVo.getId(),agreementHandleReqVo.getDeadLine());
    }

    public List<Agreement> listBy(Map<String, Object> paramMap) {
        return agreementFacade.listBy(paramMap);
    }

    /**
     * 上传模板文件
     * @param bytes
     * @param fileOriginalName
     * @return
     */
    public String handleOnlineTemplate(byte[] bytes, String fileOriginalName) {
        String fastdfsUrl;
        String fileName = FilenameUtils.getBaseName(fileOriginalName);
        //pdf文件直接上传到fastdfs
        if (FilenameUtils.getExtension(fileOriginalName).equals("pdf")){
            fastdfsUrl = fastdfsClient.uploadFile(bytes,fileOriginalName);
            return fastdfsUrl;
        }
        //转换文件
        String wordPath = System.getProperty("user.dir") + File.separator + fileOriginalName;
        String pdfPath = null;
        File file = new File(wordPath);
        try {
            FileOutputStream fileInputStream = new FileOutputStream(file);
            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileInputStream);
            bufferedOutputStream.write(bytes);
            pdfPath = System.getProperty("user.dir") + File.separator + fileName + ".pdf";
            wordToPdf(wordPath,pdfPath);
            fastdfsUrl = fastdfsClient.uploadFile(pdfPath,fileName + ".pdf");
        } catch (Exception e) {
            log.error("模板文件转换出错:" + e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("模板文件转换出错");
        }finally {
            FileUtil.del(wordPath);
            FileUtil.del(pdfPath);
        }
        return fastdfsUrl;
    }

    /**
     * word转pdf
     *
     * @param wordPath word文件保存的路径
     * @param pdfPath  转换后pdf文件保存的路径
     */
    public void wordToPdf(String wordPath, String pdfPath) throws Exception {
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream("License.xml")) {
            License license = new License();
            license.setLicense(is);
        }

        File file = new File(pdfPath);
        try (FileOutputStream os = new FileOutputStream(file)) {
            Document doc = new Document(wordPath);
            doc.save(os, SaveFormat.PDF);
        }
    }

    public void replace(Map<String, Object> paramMap,PmsOperator pmsOperator) {
        Long id = ((Integer) paramMap.get("id")).longValue();
        String fileUrl = (String) paramMap.get("fileUrl");
        String fileName = (String)paramMap.get("fileName");
        Agreement agreement = agreementFacade.getAgreementById(id);
        if (agreement == null || agreement.getStatus().intValue() != AgreementStatusEnum.FINISHED.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议不存在或状态不为已完成");
        }

        agreement.setUpdateTime(new Date());
        agreement.setUpdateOperator(pmsOperator.getRealName());

        AgreementFile agreementFile = agreementFileFacade.getByAgreementIdAndType(id,AgreementFileTypeEnum.ARCHIVE.getValue());
        agreementFile.setFileUrl(fileUrl);
        agreementFile.setFileName(fileName);
        agreementFileFacade.update(agreementFile);
    }
}
