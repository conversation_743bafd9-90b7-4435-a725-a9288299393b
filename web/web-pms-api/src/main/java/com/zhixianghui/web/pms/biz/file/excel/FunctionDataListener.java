package com.zhixianghui.web.pms.biz.file.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierFunction;
import com.zhixianghui.web.pms.biz.CommonBiz;
import com.zhixianghui.web.pms.vo.file.FileVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2021/4/6 15:31
 */
public class FunctionDataListener {


    public class CheckListener<T> extends AnalysisEventListener<T> {
        private final Logger log = LoggerFactory.getLogger(CheckListener.class);
        private final int headRowNum;

        public CheckListener(int headRowNum) {
            this.headRowNum = headRowNum;
        }

        @Override
        public void invoke(T data, AnalysisContext context) {
            // 正常就可以
            Integer rowNum = context.readRowHolder().getRowIndex();
            log.info("测试第{}行通过", rowNum);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            Integer rowNum = context.readRowHolder().getRowIndex();
            if (rowNum < headRowNum) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件内容格式有误，请检查文件!");
            }
        }

        @Override
        public boolean hasNext(AnalysisContext context) {
            Integer rowNum = context.readRowHolder().getRowIndex();
            // 读2行没问题就返回
            int checkNum = 2;
            if (rowNum > checkNum + headRowNum) {
                doAfterAllAnalysed(context);
                return false;
            }
            return super.hasNext(context);
        }
    }

    public class GenerateListenerListener<T, V> extends AnalysisEventListener<T> {
        private final Logger log = LoggerFactory.getLogger(CheckListener.class);
        /**
         * 每隔BATCH_COUNT条存储数据库 然后清理list ，方便内存回收
         */
        private static final int BATCH_COUNT = 500;
        /**
         * 每批BATCH_COUNT拆分成NOTIFY_COUNT条通知受理
         */
        private static final int NOTIFY_COUNT = 100;

        private List<V> list = new ArrayList<>();
        private final int headRowNum;
        private CommonBiz commonBiz;

        public GenerateListenerListener() {
            headRowNum = 1;
        }

        public GenerateListenerListener(int headNumber, CommonBiz commonBiz) {
            this.headRowNum = headNumber;
            this.commonBiz = commonBiz;

        }

        /**
         * 这个每一条数据解析都会来调用
         */
        @Override
        @SuppressWarnings("unchecked")
        public void invoke(T data, AnalysisContext context) {
            if (data == null) {
                return;
            }
            if (data instanceof FileVo.PmsFile) {
                PmsFunction pmsFile = new FileVo.PmsFile().toPmsFunction(data);
                check(pmsFile.getPermissionFlag(), pmsFile.getFunctionType(),
                        pmsFile.getName(), pmsFile.getNumber());
                list.add((V) pmsFile);
            } else if (data instanceof FileVo.EmployerFile) {
                EmployerFunction employerFunction = new FileVo.EmployerFile().toEmployerFunction(data);
                check(employerFunction.getPermissionFlag(), employerFunction.getType(),
                        employerFunction.getName(), employerFunction.getNumber());
                list.add((V) employerFunction);
            } else if (data instanceof FileVo.SupplyFile) {
                SupplierFunction supplierFunction = new FileVo.SupplyFile().toSupplyFileFunction(data);
                check(supplierFunction.getPermissionFlag(), supplierFunction.getType(),
                        supplierFunction.getName(), supplierFunction.getNumber());
                list.add((V) supplierFunction);
            } else if (data instanceof FileVo.AgentFile) {
                AgentFunction agentFunction = new FileVo.AgentFile().toAgentFunction(data);
                check(agentFunction.getPermissionFlag(), agentFunction.getType(),
                        agentFunction.getName(), agentFunction.getNumber());
                list.add((V) agentFunction);
            }
            if (list.size() >= BATCH_COUNT) {
                // TODO 记录成功导入的数据的行数, 导入失败的行数及失败的信息
                commonBiz.saveFunction(list);
                list.clear();
            }
        }

        public void check(String permissionFlag, Integer functionType, String name, String number) {
            if (StringUtils.isBlank(permissionFlag)) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("权限标识不能为空");
            }
            if (functionType == null) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("功能类型不能为空");

            }
            if (StringUtils.isBlank(name)) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("名称不能为空");
            }
            if (StringUtils.isBlank(number)) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("编号不能为空");
            }
        }


        /**
         * 所有数据解析完成了 都会来调用
         */
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 这里也要保存数据，确保最后遗留的数据也存储到数据库
            if (!ObjectUtils.isEmpty(list)) {
                commonBiz.saveFunction(list);
            }
            log.info("所有数据解析完成！");
        }
    }


}
