package com.zhixianghui.web.pms.vo.merchant;

import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/9/8
 **/
@Data
public class MerchantInvoiceInfoVo {
    /**
     * 商户编号
     */
    @NotEmpty(message = "商户编号不能为空")
    private String mchNo;

    /**
     * 企业名称
     */
    @NotEmpty(message = "开票企业名称不能为空")
    @Length(max = 100, message = "开票企业名称长度不能超过100")
    private String mchName;

    /**
     * 纳税人类型
     * @see com.zhixianghui.common.statics.enums.merchant.TaxPayerTypeEnum
     */
    @NotNull(message = "纳税人类型不能为空")
    @EnumValue(intValues = {1,2}, message = "纳税人类型不能有误")
    private Integer taxPayerType;

    /**
     * 税号
     */
    @NotEmpty(message = "税号不能为空")
    @Length(max = 30, message = "税号长度不能超过30")
    private String taxNo;

    /**
     * 注册地址-电话
     */
    @NotEmpty(message = "注册地址-电话不能为空")
    @Length(max = 100, message = "注册地址-电话长度不能超过100")
    private String registerAddrInfo;

    /**
     * 银行账户
     */
    @NotEmpty(message = "银行账户不能为空")
    @Length(max = 50, message = "银行账户长度不能超过100")
    private String accountNo;

    /**
     * 银行名称
     */
    @NotEmpty(message = "银行名称不能为空")
    @Length(max = 50, message = "银行名称长度不能超过100")
    private String bankName;

    /**
     * 默认发票类目编码
     */
    private String defaultInvoiceCategoryCode;

    /**
     * 默认发票类目名称
     */
    private String defaultInvoiceCategoryName;
}
