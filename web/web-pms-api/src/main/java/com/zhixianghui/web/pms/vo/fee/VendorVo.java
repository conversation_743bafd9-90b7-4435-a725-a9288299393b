package com.zhixianghui.web.pms.vo.fee;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@Data
public class VendorVo {
    private Long id;

    @NotBlank(message = "产品编号不能为空")
    private String productNo;

    @NotBlank(message = "产品名称不能为空")
    private String productName;

    /**
     * 供应商编号
     */
    @NotEmpty(message = "供应商编号不能为空")
    private String vendorNo;

    /**
     * 供应商名称
     */
    @NotEmpty(message = "供应商名称不能为空")
    private String vendorName;


    /**
     * 描述
     */
    private String description;
}