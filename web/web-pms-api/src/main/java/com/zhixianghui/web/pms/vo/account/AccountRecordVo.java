package com.zhixianghui.web.pms.vo.account;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName AccountRecordVo
 * @Description TODO
 * @Date 2022/12/5 11:04
 */
@Data
public class AccountRecordVo implements Serializable {

    @NotNull(message = "id不能为空")
    private Long id;

    private Integer isShow;

    @Length(min = 0,max = 50,message = "账户标题最大为50个字符")
    private String title;
}
