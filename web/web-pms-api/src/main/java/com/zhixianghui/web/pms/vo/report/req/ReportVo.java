package com.zhixianghui.web.pms.vo.report.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class ReportVo {

    private String mchName;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 商户名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    @NotEmpty(message="代征主体编号不能为空")
    private String mainstayNo;

    /**
     * 所属供应商名称
     */
    private String mainstayName;

    /**
     * 支付通道编号
     */
    @NotEmpty(message="支付通道编号不能为空")
    private String payChannelNo;

    /**
     * 通道名称
     */
    private String payChannelName;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 操作主体类型
     */
    private Integer merchantType;

}
