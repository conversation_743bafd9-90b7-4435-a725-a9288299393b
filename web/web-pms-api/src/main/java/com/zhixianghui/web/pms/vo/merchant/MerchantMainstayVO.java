package com.zhixianghui.web.pms.vo.merchant;

import com.zhixianghui.common.util.validator.Email;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class MerchantMainstayVO {

    /**
     * 商户名称
     */
    @NotEmpty(message = "商户名称不能为空")
    @Size(min = 2, max = 100, message = "商户名称长度必须为2-100")
    private String mchName;

    /**
     * 销售id
     */
    @NotNull(message = "销售人员不能为空")
    private Long saleId;

    /**
     * 联系人姓名
     */
    @NotEmpty(message = "联系人姓名不能为空")
    @Size(min = 2, max = 50, message = "联系人姓名长度必须为2-50")
    private String contactName;

    /**
     * 联系人电话
     */
    @NotEmpty(message = "联系人电话不能为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号不正确")
    private String contactPhone;

    @NotEmpty(message = "联系人邮箱不能为空")
    @Email(message = "联系人邮箱格式有误")
    @Length(max = 50, message = "联系人邮箱过长，不能超过50个字符")
    private String contactEmail;

    /**
     * 委托协议文件路径
     */
    private String entrustAgreementFileUrl;

    /**
     * B端协议模板
     */
    @NotEmpty(message = "B端协议模板不能为空")
    private String agreementTemplate2BFileUrl;

    /**
     * C端协议模板
     */
    @NotEmpty(message = "C端协议模板不能为空")
    private String agreementTemplate2CFileUrl;

}
