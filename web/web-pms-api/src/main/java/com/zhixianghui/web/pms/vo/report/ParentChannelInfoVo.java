package com.zhixianghui.web.pms.vo.report;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @description 父商户通道信息(代征主体-支付通道)
 * @date 2020-09-29 11:25
 **/
@Data
public class ParentChannelInfoVo {
    /**
     * 支付通道编号
     */
    @NotEmpty(message = "支付通道编号不能为空")
    private String payChannelNo;

    /**
     * 通道商户编号
     */
    private String channelMchNo;

    /**
     * 商户私钥
     */
    private String privateKey;

    /**
     * 商户公钥
     */
    private String publicKey;

    /**
     * 通道密钥
     */
    private String channelKey;

    /**
     * 通道证书
     */
    private String channelCert;

    /**
     * 状态
     */
    private Integer status;

    private String channelLoginUser;

    private String bankName;

    private String subBankName;

    private String channelPlatNo;

    private String accountName;

    private String accountNo;
}
