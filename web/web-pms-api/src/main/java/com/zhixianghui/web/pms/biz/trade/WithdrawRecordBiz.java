package com.zhixianghui.web.pms.biz.trade;

import com.zhixianghui.common.statics.dto.withdraw.WithdrawRecordQueryDto;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.service.WithdrawRecordFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class WithdrawRecordBiz {

    @Reference
    private WithdrawRecordFacade withdrawRecordFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    public PageResult<List<WithdrawRecord>> listWithRecordPage(WithdrawRecordQueryDto withdrawRecordQueryDto, PageParam pageParam) {
        return withdrawRecordFacade.listWithRecordPage(withdrawRecordQueryDto,pageParam);
    }

    public void exportWithRecord(WithdrawRecordQueryDto withdrawRecordQueryDto, PmsOperator pmsOperator) {
        Map<String, Object> paramMap = BeanUtil.toMap(withdrawRecordQueryDto);
        paramMap.put("mainstayNo",withdrawRecordQueryDto.getMainstayNo());

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.WITHDRAW_RECORD_PMS.getFileName());
        record.setReportType(ReportTypeEnum.WITHDRAW_RECORD_PMS.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setMchNo(withdrawRecordQueryDto.getMainstayNo());
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.WITHDRAW_RECORD_PMS.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }

    public Map<String, Object> sumWithdrawRecord(Map<String, Object> paramMap) {
        return withdrawRecordFacade.sumWithdrawRecord(paramMap);
    }
}
