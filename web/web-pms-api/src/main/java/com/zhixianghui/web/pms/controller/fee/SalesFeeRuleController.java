package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.fee.entity.SalesFeeRule;
import com.zhixianghui.facade.fee.service.SalesFeeRuleFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.utils.FeeRuleUtil;
import com.zhixianghui.web.pms.vo.fee.SalesFeeRuleInsertVo;
import com.zhixianghui.web.pms.vo.fee.SalesFeeRuleQueryVO;
import com.zhixianghui.web.pms.vo.fee.SalesFeeRuleUpdateVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

/**
 * 销售管理
 * <AUTHOR>
 * @date 2020-09-11
 */
@RestController
@RequestMapping("salesCostRule")
public class SalesFeeRuleController {
    @Reference
    private SalesFeeRuleFacade ruleFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;

    /**
     * 保存
     * @param vo
     * @param pmsOperator
     */
    @RequestMapping("insert")
    @Permission("sales:salesCostRule:insert")
    public RestResult<String> insert(@Valid @RequestBody SalesFeeRuleInsertVo vo, @CurrentUser PmsOperator pmsOperator) {
        // 规则校验
        FeeRuleUtil.checkFeeRule(vo);

        SalesFeeRule rule = new SalesFeeRule();
        rule.setUpdateTime(new Date());
        rule.setUpdator(pmsOperator.getLoginName());
        rule.setDepartmentId(vo.getDepartmentId());
        rule.setDepartmentName(vo.getDepartmentName());
        rule.setProductNo(vo.getProductNo());
        rule.setProductName(vo.getProductName());
        rule.setStatus(PublicStatus.ACTIVE);
        rule.setDescription(vo.getDescription());
        rule.setVersion(0);
        rule.setCreateTime(new Date());

        FeeRuleUtil.fillFeeRule(rule, vo);

        ruleFacade.insert(rule);

        return RestResult.success("添加成功");
    }

    /**
     * 修改
     * @param vo
     * @param pmsOperator
     */
    @RequestMapping("update")
    @Permission("sales:salesCostRule:update")
    public RestResult<String> update(@Valid @RequestBody SalesFeeRuleUpdateVo vo, @CurrentUser PmsOperator pmsOperator) {
        // 规则校验
        FeeRuleUtil.checkFeeRule(vo);

        SalesFeeRule rule = ruleFacade.getById(vo.getId());
        if(rule == null){
            return RestResult.error("销售成本记录不存在");
        }
        rule.setUpdateTime(new Date());
        rule.setUpdator(pmsOperator.getLoginName());
        rule.setDepartmentId(vo.getDepartmentId());
        rule.setDepartmentName(vo.getDepartmentName());
        rule.setProductNo(vo.getProductNo());
        rule.setProductName(vo.getProductName());
        rule.setDescription(vo.getDescription());

        FeeRuleUtil.fillFeeRule(rule, vo);

        ruleFacade.update(rule);

        return RestResult.success("更新成功");
    }

    /**
     * 删除
     * @param id
     */
    @RequestMapping("delete")
    @Permission("sales:salesCostRule:delete")
    public RestResult<String> delete(Long id, @CurrentUser PmsOperator pmsOperator) {
        ruleFacade.delete(id, pmsOperator.getLoginName());
        return RestResult.success("删除成功");
    }

    /**
     * 列表查询
     * @return
     */
    @RequestMapping("listPage")
    @Permission("sales:salesCostRule:view")
    public RestResult<PageResult<List<SalesFeeRule>>> listPage(@Valid @RequestBody SalesFeeRuleQueryVO vo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);

        // 销售部只能查看当前部门的数据
        if(operator.getDepartmentId() != null){
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> departmentIds = pmsDepartmentFacade.listSubDepartment(operator.getDepartmentId());
                paramMap.put("departmentIds", departmentIds);
            }
        }

        PageResult<List<SalesFeeRule>> page = ruleFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        page.getData().stream().forEach(x -> {
            if(x.getFeeRate() != null){
               x.setFeeRate(AmountUtil.mul(x.getFeeRate(), new BigDecimal(100)));
            }
        });
        return RestResult.success(page);
    }

    /**
     * 详情查看
     * @param id
     * @return
     */
    @GetMapping("getById")
    @Permission("sales:salesCostRule:view")
    public RestResult<SalesFeeRule> getById(@RequestParam Long id) {
        SalesFeeRule rule = ruleFacade.getById(id);
        if(rule.getFeeRate() != null){
            rule.setFeeRate(AmountUtil.mul(rule.getFeeRate(), new BigDecimal(100)));
        }
        return RestResult.success(rule);
    }

}
