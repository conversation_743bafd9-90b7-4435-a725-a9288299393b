package com.zhixianghui.web.pms.controller.taxcertificate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.entity.TaxCertificateRecord;
import com.zhixianghui.facade.trade.service.TaxCertificateRecordFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("taxCertificate")
public class TaxCertificateController {

    @Reference
    private ExportRecordFacade exportRecordFacade;

    @Reference
    private TaxCertificateRecordFacade taxCertificateRecordFacade;


    @PostMapping("add")
    public RestResult<TaxCertificateRecord> add(@RequestBody TaxCertificateRecord taxCertificateRecord, @CurrentUser PmsOperator operator) {
        taxCertificateRecord.setCreateBy(operator.getLoginName());
        taxCertificateRecord.setUpdateBy(operator.getLoginName());
        return RestResult.success(taxCertificateRecordFacade.save(taxCertificateRecord));
    }

    @PostMapping("edit")
    public RestResult<TaxCertificateRecord> edit(@RequestBody TaxCertificateRecord taxCertificateRecord, @CurrentUser PmsOperator pmsOperator) {
        taxCertificateRecord.setUpdateBy(pmsOperator.getLoginName());
        return RestResult.success(taxCertificateRecordFacade.update(taxCertificateRecord));
    }

    @PostMapping("listPage")
    public RestResult<Page<TaxCertificateRecord>> listPage(@RequestBody Page<TaxCertificateRecord> page,@RequestBody Map<String,Object> param) {
        return RestResult.success(taxCertificateRecordFacade.listPage(page, param));
    }

    @PostMapping("delete")
    public RestResult<Boolean> delete(Integer id) {
        return RestResult.success(taxCertificateRecordFacade.delete(id));
    }

    @PostMapping("batchDownLoad")
    public RestResult batchDownLoad(@RequestBody Map<String,Object> param,@CurrentUser PmsOperator pmsOperator){
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.PMS_TAX_CERTIFICATE.getFileName());
        record.setReportType(ReportTypeEnum.PMS_TAX_CERTIFICATE.getValue());
        record.setParamJson(JsonUtil.toString(param));
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

}
