package com.zhixianghui.web.pms.vo.sign;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/18 15:36
 **/
@Data
public class SignRecordQueryVo {
    /**
     * 签约状态
     */
    private Integer signStatus;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 姓名
     */
    private String receiveName;

    /**
     * 身份证号
     */
    private String receiveIdCardNo;

    /**
     * 手机号
     */
    private String receivePhoneNo;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;
    /**
     * 商户名称模糊
     */
    private String employerNameLike;

    /**
     * 创建起始时间
     */
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    private Date createEndDate;
}
