package com.zhixianghui.web.pms.controller.config;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.service.AlipayMccFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName AlipayAccController
 * @Description TODO
 * @Date 2023/6/7 11:23
 */
@RestController
@RequestMapping("alipayMcc")
public class AlipayMccController {

    @Reference
    private AlipayMccFacade alipayMccFacade;

    @GetMapping("listAll")
    public RestResult listAll(){
        return RestResult.success(alipayMccFacade.listAll());
    }
}
