package com.zhixianghui.web.pms.vo.merchant;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class EventsFollowVo {

    @NotBlank(message = "内容不允许为空")
    private String eventContent;

    /**
     * 动态类型
     */
    @NotNull(message = "类型不允许为空")
    private Short eventType;

    private String eventTypeDesc;

    /**
     * 商户编号
     */
    private String mchNo;
}
