package com.zhixianghui.web.pms.controller.trade;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.OrderReexchangeFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.controller.fix.helper.MultipartFileUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年02月10日 16:14:00
 */
@RestController
@RequestMapping("/orderReexchange")
public class OrderReexchangeController {

    @Reference
    private OrderReexchangeFacade orderReexchangeFacade;
    @Reference
    private OrderItemFacade orderItemFacade;

    @Permission("order:reexchange:update")
    @PostMapping("/reexchange/{platTrxNo}")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "订单退汇")
    public RestResult reexchange(@PathVariable String platTrxNo, @CurrentUser PmsOperator operator) {
        if (operator.getType() != PmsOperatorTypeEnum.ADMIN.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂无权限");
        }
        orderReexchangeFacade.orderReexchange(platTrxNo);
        return RestResult.success("退汇成功");
    }

    @Permission("order:reexchangeBatch:update")
    @PostMapping("/uploadReexchangeExcel")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "批量退汇")
    public RestResult reexchangeBatch(@RequestParam("file") MultipartFile file, @RequestParam("type") Integer type,
                                      @CurrentUser PmsOperator operator) {

        if (operator.getType() != PmsOperatorTypeEnum.ADMIN.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂无权限");
        }

        if (Objects.isNull(type)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请传入退汇类型");
        }
        ReexchangeBatchType enumByCode = ReexchangeBatchType.getEnumByCode(type);
        if (Objects.isNull(enumByCode)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无效的退汇类型");
        }

        File excelFile = MultipartFileUtil.transfer2File(file);

        List<String> noList = new ArrayList<>();
        EasyExcel.read(excelFile, new AnalysisEventListener<LinkedHashMap<Integer, String>>() {
            @Override
            public void invoke(LinkedHashMap<Integer, String> data, AnalysisContext context) {
                if (!data.isEmpty()) {
                    noList.add(data.values().iterator().next()); // 读取第一列数据
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().headRowNumber(1).doRead();

        if (CollectionUtils.isNotEmpty(noList)) {
            switch (enumByCode) {
                case MCHORDERNO:
                    for (String mchOrder : noList) {
                        OrderItem orderItem = orderItemFacade.getByMchOrderNo(mchOrder);
                        if (Objects.isNull(orderItem) || Objects.isNull(orderItem.getPlatTrxNo())) {
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(String.format("商户订单号：【%s】不可退汇", mchOrder));
                        }
                        orderReexchangeFacade.orderReexchange(orderItem.getPlatTrxNo());
                    }
                    break;
                case PLATTRXNO:
                    for (String platTrxNo : noList) {
                        orderReexchangeFacade.orderReexchange(platTrxNo);
                    }
                    break;
                default:
                    break;
            }
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("Excel文件没有数据");
        }

        return RestResult.success("批量退汇成功");
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    enum ReexchangeBatchType {
        MCHORDERNO(1, "商户订单号"),
        PLATTRXNO(2, "平台流水号");

        private int code;
        private String name;

        public static ReexchangeBatchType getEnumByCode(int code) {
            for (ReexchangeBatchType type : ReexchangeBatchType.values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }
    }
}
