package com.zhixianghui.web.pms.biz.record;

import com.zhixianghui.common.statics.enums.merchant.EditTypeEnum;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.AgentBankAccount;
import com.zhixianghui.facade.merchant.entity.AgentEmployerMain;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.ChangeSourceEnum;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.vo.agent.*;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.facade.merchant.vo.record.AgentBaseInfoVo;
import com.zhixianghui.facade.merchant.vo.record.AgentProductFeeVo;
import com.zhixianghui.facade.merchant.vo.record.MerchantBaseInfoVo;
import com.zhixianghui.web.pms.biz.agent.AgentBiz;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/7/22 16:37
 */
@Component
public class HandleService {

    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantEmployerFacade merchantEmployerFacade;
    @Reference
    private MerchantEmployerMainFacade mainFacade;
    @Reference
    private AgentFacade agentFacade;
    @Reference
    private MerchantInfoChangeRecordFacade changeRecordFacade;
    @Autowired
    private AgentBiz agentBiz;

    @Service
    @EditType(value = EditTypeEnum.EDIT_MERCHANT_BASE_INFO)
    public class MerchantEditBaseInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            MerchantUpdateVo merchantUpdateVo = (MerchantUpdateVo) o1;
            Map<String,Object> data = merchantQueryFacade.getBaseInfo(merchantUpdateVo.getMchNo());
            handleInfo(data, merchantUpdateVo, pmsOperator);
        }
    }

    @Service
    @EditType(value = EditTypeEnum.EDIT_MERCHANT_COOPERATE_INFO)
    public class MerchantEditCooperateInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            MerchantUpdateVo merchantUpdateVo = (MerchantUpdateVo) o1;
            Map<String, Object> data = merchantEmployerFacade.getCooperateInfo(merchantUpdateVo.getMchNo());
            handleInfo(data, merchantUpdateVo, pmsOperator);
        }
    }

    @Service
    @EditType(value = EditTypeEnum.EDIT_MAINSTAY_COOPERATE_INFO)
    public class EditMainstayCooperateInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            MerchantUpdateVo merchantUpdateVo = (MerchantUpdateVo) o1;
            Map<String, Object> data = merchantEmployerFacade.getCooperateInfo(merchantUpdateVo.getMchNo());
            handleInfo(data, merchantUpdateVo, pmsOperator);
        }
    }

    @Service
    @EditType(value = EditTypeEnum.EDIT_MAIN_INFO)
    public class EditMainInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            MerchantUpdateVo merchantUpdateVo = (MerchantUpdateVo) o1;
            Map<String,Object> data = mainFacade.getMainInfo(merchantUpdateVo.getMchNo());
            handleInfo(data, merchantUpdateVo, pmsOperator);
        }
    }

    @Service
    @EditType(value = EditTypeEnum.EDIT_BUSINESS_INFO)
    public class EditBusinessInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            MerchantUpdateVo merchantUpdateVo = (MerchantUpdateVo) o1;
            Map<String,Object> data = mainFacade.getBusinessInfo(merchantUpdateVo.getMchNo());
            handleInfo(data, merchantUpdateVo, pmsOperator,  EditTypeEnum.EDIT_BUSINESS_INFO.getType());
        }
    }

    private void handleInfo(Map<String, Object> oldMap, MerchantUpdateVo editData, PmsOperator pmsOperator, String type) {
        MerchantBaseInfoVo oldInfo = MerchantBaseInfoVo.newInstance();
        MerchantUpdateVo oldEditData = new MerchantUpdateVo();
        if (!CollectionUtils.isEmpty(oldMap)){
            oldMap.remove("businessType");
            BeanUtil.mapToObject(oldEditData, oldMap);
            BeanUtil.copyProperties(oldEditData, oldInfo);
            if (StringUtils.isNotBlank(type) &&  EditTypeEnum.EDIT_BUSINESS_INFO.getType().equals(type)) {
                oldInfo.setInvoiceAccountNo(oldEditData.getInvoiceAccountNo());
                oldInfo.setInvoiceBankName(oldEditData.getInvoiceBankName());
            }

        }
        MerchantBaseInfoVo newInfo = MerchantBaseInfoVo.newInstance();
        BeanUtil.copyProperties(editData, newInfo);
        newInfo.setOperatorInfo(pmsOperator.getId(), pmsOperator.getRealName());
        changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.OTHER.getSource());
    }

    public void handleInfo(Map<String,Object> oldMap, MerchantUpdateVo editData, PmsOperator pmsOperator) {
        handleInfo(oldMap, editData, pmsOperator, null);
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_CHANGE_STATUS)
    public class AgentChangeStatus implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            AgentBaseInfoVo newInfo = (AgentBaseInfoVo) o1;
            Agent agent = agentFacade.getOne(Collections.singletonMap("agentNo", newInfo.getAgentNo()));
            AgentBaseInfoVo oldInfo = AgentBaseInfoVo.buildAgentStatus(agent.getAgentNo(), agent.getAgentStatus());
            newInfo.setOperatorInfo(pmsOperator.getId(), pmsOperator.getRealName());
            changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.OTHER.getSource());
        }
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_EDIT_BASE_INFO)
    public class AgentEditBaseInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            AgentBaseInfoVo newInfo = (AgentBaseInfoVo) o1;
            com.zhixianghui.web.pms.vo.agent.res.AgentBaseInfoVo agent = agentBiz.getAgent(newInfo.getAgentNo());
            AgentBaseInfoVo oldInfo = AgentBaseInfoVo.getInstance();
            BeanUtil.copyProperties(agent, oldInfo);
            newInfo.setOperatorInfo(pmsOperator.getId(), pmsOperator.getRealName());
            changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.OTHER.getSource());
        }
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_EDIT_BANK_INFO)
    public class AgentEditBankInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            AgentBaseInfoVo newInfo = (AgentBaseInfoVo) o1;
            AgentBankAccount agentBankAccount = agentBiz.bankInfo(newInfo.getAgentNo());
            AgentBaseInfoVo oldInfo = AgentBaseInfoVo.getInstance();
            BeanUtil.copyProperties(agentBankAccount, oldInfo);
            newInfo.setOperatorInfo(pmsOperator.getId(), pmsOperator.getRealName());
            changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
        }
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_EDIT_MAIN_INFO)
    public class AgentEditMainInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator pmsOperator) {
            AgentBaseInfoVo newInfo = (AgentBaseInfoVo) o1;
            AgentMainVo agentMainVo = agentBiz.buildMainInfo(newInfo.getAgentNo());
            AgentBaseInfoVo oldInfo = AgentBaseInfoVo.getInstance();
            BeanUtil.copyProperties(agentMainVo, oldInfo);
            newInfo.setOperatorInfo(pmsOperator.getId(), pmsOperator.getRealName());
            changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
        }
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_EDIT_QUOTE_INFO)
    public class AgentEditQuoteInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator operator, Long quoteId) {
            AgentProductFeeVo feeVo = (AgentProductFeeVo) o1;
            AgentProductFeeVo oldInfo;
            if (quoteId == null) {
                oldInfo = AgentProductFeeVo.getInstance();
            } else {
                AgentProductQuoteVo quoteVo = agentBiz.getAgentProductQuoteVo(quoteId);
                if (quoteVo == null) {
                    oldInfo = AgentProductFeeVo.getInstance();
                } else {
                    oldInfo = AgentProductFeeVo.getInstance();
                    BeanUtil.copyProperties(quoteVo, oldInfo);
                }
            }
            AgentProductFeeVo newInfo = AgentProductFeeVo.getInstance();
            BeanUtil.copyProperties(feeVo, newInfo);
            newInfo.setOperatorInfo(operator.getId(), operator.getRealName());
            if (StringUtils.isNotBlank(oldInfo.getMainstayNo()) && StringUtils.isBlank(newInfo.getMainstayName())) {
                if (oldInfo.getMainstayNo().equals(newInfo.getMainstayNo())) {
                    newInfo.setMainstayName(oldInfo.getMainstayName());
                } else {
                    Merchant merchant = merchantQueryFacade.getByMchNo(newInfo.getMainstayNo());
                    if (merchant != null) {
                        newInfo.setMainstayName(merchant.getMchName());
                    }
                }
            }
            changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
        }
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_DELETE_QUOTE_INFO)
    public class AgentDeleteQuoteInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator operator, Long flowId) {
            AgentProductFeeVo feeVo = (AgentProductFeeVo) o1;
            AgentProductFeeVo oldInfo = AgentProductFeeVo.getInstance();
            BeanUtil.copyProperties(feeVo, oldInfo);

            AgentProductFeeVo newInfo = AgentProductFeeVo.getInstance();
            newInfo.setOperatorInfo(operator.getId(), operator.getRealName(), flowId);
            newInfo.setAgentNo(feeVo.getAgentNo());
            newInfo.setAgentName(feeVo.getAgentName());
            changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
        }
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_SET_SELLER)
    public class AgentEditSaleInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator operator, Long flowId) {
            if (o1 instanceof SetSellerVo) {
                SetSellerVo sellerVo = (SetSellerVo) o1;
                AgentBaseInfoVo oldInfo = AgentBaseInfoVo.getInstance();
                oldInfo.setSalerId(sellerVo.getBeforeSellerId());
                oldInfo.setSalerName(sellerVo.getBeforeSellerName());
                oldInfo.setAgentNo(sellerVo.getAgentNo());

                AgentBaseInfoVo newInfo = AgentBaseInfoVo.getInstance();
                newInfo.setSalerId(sellerVo.getBeforeSellerId());
                newInfo.setSalerName(sellerVo.getSellerName());
                newInfo.setAgentNo(sellerVo.getAgentNo());
                newInfo.setOperatorInfo(operator.getId(), operator.getRealName(), flowId);

                changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
                return;
            }
            AgentBatchSetVo sellerVo = (AgentBatchSetVo) o1;
            if (CollectionUtils.isEmpty(sellerVo.getAgentNoList())) {
                return;
            }
            for (AgentBatchSetItem agentBatchSetItem : sellerVo.getAgentNoList()) {
                AgentBaseInfoVo oldInfo = AgentBaseInfoVo.getInstance();
                oldInfo.setSalerId(agentBatchSetItem.getBeforeSaleId());
                oldInfo.setSalerName(agentBatchSetItem.getBeforeSaleName());
                oldInfo.setAgentNo(agentBatchSetItem.getBeforeNo());
                oldInfo.setAgentName(agentBatchSetItem.getBeforeName());

                AgentBaseInfoVo newInfo = AgentBaseInfoVo.getInstance();
                newInfo.setSalerId(Long.valueOf(sellerVo.getSetNo()));
                newInfo.setSalerName(sellerVo.getSetName());
                newInfo.setOperatorInfo(operator.getId(), operator.getRealName(), flowId);
                newInfo.setAgentNo(agentBatchSetItem.getAgentNo());
                newInfo.setAgentName(agentBatchSetItem.getAgentName());
                changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
            }
        }
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_SET_INVITER)
    public class AgentEditInviterInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator operator, Long flowId) {
            if (o1 instanceof SetInviterVo) {
                SetInviterVo setInviterVo = (SetInviterVo) o1;
                AgentBaseInfoVo oldInfo = AgentBaseInfoVo.getInstance();
                oldInfo.setInviterNo(setInviterVo.getBeforeInviterNo());
                oldInfo.setInviterName(setInviterVo.getBeforeInviterName());
                oldInfo.setAgentNo(setInviterVo.getAgentNo());

                AgentBaseInfoVo newInfo = AgentBaseInfoVo.getInstance();
                newInfo.setInviterNo(setInviterVo.getInviterNo());
                newInfo.setAgentNo(setInviterVo.getAgentNo());
                newInfo.setInviterName(setInviterVo.getInviterName());
                newInfo.setOperatorInfo(operator.getId(), operator.getRealName(), flowId);
                changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
                return;
            }

            AgentBatchSetVo setInviterVo = (AgentBatchSetVo) o1;
            for (AgentBatchSetItem agentBatchSetItem : setInviterVo.getAgentNoList()) {
                AgentBaseInfoVo oldInfo = AgentBaseInfoVo.getInstance();
                oldInfo.setInviterNo(agentBatchSetItem.getBeforeNo());
                oldInfo.setInviterName(agentBatchSetItem.getBeforeName());
                oldInfo.setAgentNo(agentBatchSetItem.getAgentNo());


                AgentBaseInfoVo newInfo = AgentBaseInfoVo.getInstance();
                newInfo.setInviterNo(setInviterVo.getSetNo());
                newInfo.setInviterName(setInviterVo.getSetName());
                newInfo.setOperatorInfo(operator.getId(), operator.getRealName(), flowId);
                newInfo.setAgentNo(agentBatchSetItem.getAgentNo());
                changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());

            }
        }
    }

    @Service
    @EditType(value = EditTypeEnum.AGENT_SET_PRINCIPAL)
    public class AgentEditPrincipalInfo implements Builder {
        @Override
        public <T> void build(T o1, PmsOperator operator, Long flowId) {
            PrincipalEditVo principalEditVo = (PrincipalEditVo) o1;
            AgentBaseInfoVo oldInfo = AgentBaseInfoVo.getInstance();
            oldInfo.setContactName(principalEditVo.getBeforeContactName());
            oldInfo.setContactEmail(principalEditVo.getBeforeContactEmail());
            oldInfo.setContactPhone(principalEditVo.getBeforeContactPhone());
            oldInfo.setAgentNo(principalEditVo.getAgentNo());

            AgentBaseInfoVo newInfo = AgentBaseInfoVo.getInstance();
            newInfo.setContactName(principalEditVo.getContactName());
            newInfo.setContactEmail(principalEditVo.getContactEmail());
            newInfo.setContactPhone(principalEditVo.getContactPhone());
            newInfo.setOperatorInfo(operator.getId(), operator.getRealName(), flowId);
            newInfo.setAgentNo(principalEditVo.getAgentNo());
            changeRecordFacade.record(newInfo, oldInfo, ChangeSourceEnum.FLOW.getSource());
        }
    }
}
