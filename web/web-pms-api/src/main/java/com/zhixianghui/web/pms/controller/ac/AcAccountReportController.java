package com.zhixianghui.web.pms.controller.ac;

import com.zhixianghui.facade.trade.service.AcRechargeAccountFacade;
import com.zhixianghui.web.pms.controller.BaseController;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/***
 * 君享汇报备功能
 */
@RestController
@RequestMapping("acManager")
public class AcAccountReportController extends BaseController {
    @Reference
    private AcRechargeAccountFacade acRechargeAccountFacade;

    /***
     * 商户报备接口
     * @param reportVo
     * @param operator
     * @return
     */
//    @PostMapping("accountReport")
//    public RestResult<AcRechargeAccount> accountReport(@RequestBody AcRechargeAccountReportVo reportVo, @CurrentUser PmsOperator operator) {
//        AcRechargeAccount acAccount = acRechargeAccountFacade.accountReport(BeanUtil.toMap(reportVo));
//        if (acAccount == null) {
//            return RestResult.error("报备失败");
//        } else {
//            return RestResult.success(acAccount);
//        }
//    }
}
