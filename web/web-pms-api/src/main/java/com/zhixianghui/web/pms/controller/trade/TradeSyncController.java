//package com.zhixianghui.web.pms.controller.trade;
//
//import com.zhixianghui.common.statics.annotations.Permission;
//import com.zhixianghui.common.statics.result.PageParam;
//import com.zhixianghui.common.statics.result.PageResult;
//import com.zhixianghui.common.statics.result.RestResult;
//import com.zhixianghui.common.util.utils.BeanUtil;
//import com.zhixianghui.facade.tradesync.entity.PayOrderSync;
//import com.zhixianghui.facade.tradesync.service.PayOrderSyncFacade;
//import com.zhixianghui.web.pms.vo.trade.PayOrderSyncQueryVO;
//import org.apache.dubbo.config.annotation.Reference;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
///**
// * Author: Cmf
// * Date: 2020.3.17
// * Time: 17:39
// * Description:订单同步管理
// */
//@RestController("tradeSyncController")
//@RequestMapping("tradeSync")
//public class TradeSyncController {
//    @Reference
//    private PayOrderSyncFacade payOrderSyncFacade;
//
//    /**
//     * 查询支付订单同步
//     *
//     * @param payOrderSyncQueryVO .
//     * @return .
//     */
//    @RequestMapping("listPayOrderSync")
//    @Permission("order:sync:view")
//    public RestResult<PageResult<List<PayOrderSync>>> listPayOrderSync(@RequestBody PayOrderSyncQueryVO payOrderSyncQueryVO,
//                                                                           @RequestParam int pageCurrent,
//                                                                           @RequestParam int pageSize) {
//        PageResult<List<PayOrderSync>> pageResult = payOrderSyncFacade.listPayOrderPage(BeanUtil.toMap(payOrderSyncQueryVO), PageParam.newInstance(pageCurrent, pageSize));
//        return RestResult.success(pageResult);
//    }
//
//    /**
//     * 根据id查询
//     *
//     * @param id
//     * @return
//     */
//    @RequestMapping("viewPayOrderSync")
//    @Permission("order:sync:view")
//    public RestResult<PayOrderSync> viewPayOrderSync(@RequestParam Long id) {
//        return RestResult.success(payOrderSyncFacade.getById(id));
//    }
//
//
//}
