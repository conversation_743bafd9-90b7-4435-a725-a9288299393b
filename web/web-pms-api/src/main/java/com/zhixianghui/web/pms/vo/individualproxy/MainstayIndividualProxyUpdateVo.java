package com.zhixianghui.web.pms.vo.individualproxy;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class MainstayIndividualProxyUpdateVo implements Serializable {
    private static final long serialVersionUID = -8335968733438739027L;
    @NotNull(message = "代征id不能为空")
    private Integer id;
    @NotNull(message = "状态不能为空")
    private Boolean status;
}
