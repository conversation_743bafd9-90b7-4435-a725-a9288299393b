package com.zhixianghui.web.pms.vo.fee;

import com.alibaba.fastjson.JSONArray;
import com.zhixianghui.common.statics.entity.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class VendorFeeRelationQueryVo{

    /**
     * 供应商编号
     */
    private String vendorNo;

    /**
     * 供应商名称 like
     */
    private String vendorNameLike;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 产品编码
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productNameLike;

    private Integer ruleType;
    /**
     * 状态
     */
    private Integer status;

    private Integer pageCurrent = 1;

    private Integer pageSize = 10;
}