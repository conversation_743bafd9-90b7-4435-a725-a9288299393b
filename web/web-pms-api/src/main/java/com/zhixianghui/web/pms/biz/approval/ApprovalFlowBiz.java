package com.zhixianghui.web.pms.biz.approval;

import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.approval.req.ApprovalFlowQueryVo;
import com.zhixianghui.web.pms.vo.approval.res.ApprovalListResVo;
import com.zhixianghui.web.pms.vo.approval.res.ApprovalResVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description web审批服务逻辑biz
 * @date 2020-08-17 12:34
 **/
@Service
public class ApprovalFlowBiz {
    @Reference
    private ApprovalFlowFacade approvalFlowFacade;

    public PageResult<List<ApprovalListResVo>> listReceivedApproval(ApprovalFlowQueryVo approvalFlowQueryVo, PageVo pageVo, PmsOperator currentOperator) {
        PageResult<List<ApprovalFlow>> pageResult;
        if(PmsOperatorTypeEnum.ADMIN.getValue() == currentOperator.getType()){
            pageResult = approvalFlowFacade.listPage(BeanUtil.toMap(approvalFlowQueryVo), pageVo.toPageParam("CREATE_TIME DESC"));
        }else{
            pageResult = approvalFlowFacade.listByHandlerIdAndPlatformSource(currentOperator.getId(),
                PlatformSource.OPERATION.getValue(),
                BeanUtil.toMap(approvalFlowQueryVo),
                pageVo.toPageParam("CREATE_TIME DESC")
            );
        }
        List<ApprovalListResVo> list = pageResult.getData().stream().map(ApprovalListResVo::buildVo).collect(Collectors.toList());
        return PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord());
    }

    public PageResult<List<ApprovalListResVo>> listSendApproval(ApprovalFlowQueryVo approvalFlowQueryVo, PageVo pageVo, PmsOperator currentOperator) {
        PageResult<List<ApprovalFlow>> pageResult;
        if(PmsOperatorTypeEnum.ADMIN.getValue() == currentOperator.getType()){
            pageResult = approvalFlowFacade.listPage(BeanUtil.toMap(approvalFlowQueryVo), pageVo.toPageParam("CREATE_TIME DESC"));
        }else{
            pageResult = approvalFlowFacade.listByInitiatorIdAndPlatformSource(currentOperator.getId(),
                PlatformSource.OPERATION.getValue(),
                BeanUtil.toMap(approvalFlowQueryVo),
                pageVo.toPageParam("CREATE_TIME DESC"));
        }
        List<ApprovalListResVo> list = pageResult.getData().stream().map(ApprovalListResVo::buildVo).collect(Collectors.toList());
        return PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord());
    }

    public PageResult<List<ApprovalListResVo>> listPendingApproval(ApprovalFlowQueryVo approvalFlowQueryVo, PageVo pageVo, PmsOperator currentOperator) {
        PageResult<List<ApprovalFlow>> pageResult;
        if(PmsOperatorTypeEnum.ADMIN.getValue() == currentOperator.getType()){
            pageResult = approvalFlowFacade.listPage(BeanUtil.toMap(approvalFlowQueryVo), pageVo.toPageParam("CREATE_TIME DESC"));
        }else{
            pageResult = approvalFlowFacade.listPendingByOperatorIdAndPlatformSource(currentOperator.getId(),
                    PlatformSource.OPERATION.getValue(),
                    BeanUtil.toMap(approvalFlowQueryVo),
                    pageVo.toPageParam("CREATE_TIME DESC"));
        }
        List<ApprovalListResVo> list = pageResult.getData().stream().map(ApprovalListResVo::buildVo).collect(Collectors.toList());
        return PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord());
    }

    public ApprovalResVo getSingleApproval(Long approvalFlowId, PmsOperator currentOperator) {
        ApprovalFlow approvalFlow = approvalFlowFacade.getByIdAndHandlerId(approvalFlowId, currentOperator.getId(),PmsOperatorTypeEnum.ADMIN.getValue() == currentOperator.getType());
        return ApprovalResVo.buildVo(approvalFlow);
    }

    public void cancelApproval(Long approvalFlowId, PmsOperator currentOperator) {
        approvalFlowFacade.cancelApproval(approvalFlowId, currentOperator.getId(),currentOperator.getRealName(),PlatformSource.OPERATION.getValue(), PmsOperatorTypeEnum.ADMIN.getValue() == currentOperator.getType());
    }

    public void updateExtInfo(UpdateExtInfoVo updateExtInfoVo, PmsOperator currentOperator) {
        updateExtInfoVo.setHandlerId(currentOperator.getId());
        updateExtInfoVo.setHandlerName(currentOperator.getRealName());
        updateExtInfoVo.setPlatform(PlatformSource.OPERATION.getValue());
        approvalFlowFacade.updateExtInfo(updateExtInfoVo,PmsOperatorTypeEnum.ADMIN.getValue() == currentOperator.getType());
    }
}
