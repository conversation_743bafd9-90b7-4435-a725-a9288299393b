package com.zhixianghui.web.pms.vo.invoice;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/28
 **/
@Data
public class PmsInvoiceRecordQueryVo extends PageQueryVo {
    private static final long serialVersionUID = 7459165251850999164L;
    /**
     * 状态
     */
    private Integer invoiceStatus;
    /**
     * 发票类型
     */
    private Integer invoiceType;
    /**
     * 流水号
     */
    private String trxNo;
    /**
     * 代征主体编号
     */
    private String mainstayMchNo;
    /**
     * 用工企业编号
     */
    private String employerMchNo;
    /**
     * 用工企业名称
     */
    private String employerMchNameLike;
    /**
     * 创建时间起始时间
     */
    private String createTimeBegin;
    /**
     * 创建时间终止时间
     */
    private String createTimeEnd;
    /**
     * 完成时间起始时间
     */
    private String completeTimeBegin;
    /**
     * 完成时间终止时间
     */
    private String completeTimeEnd;

    private String productNo;

    private Integer source;

    private Integer amountType;

    private Integer category;

    private String workCategoryCode;
}
