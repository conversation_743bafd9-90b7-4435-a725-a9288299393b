package com.zhixianghui.web.pms.controller.agent;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.service.agent.AgentOperatorFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentOperatorVO;
import com.zhixianghui.web.pms.vo.agent.req.AgentOperatorQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合伙人操作员
 *
 * <AUTHOR> <PERSON>
 */
@RestController
@RequestMapping("agentOperator")
@Slf4j
public class AgentOperatorController {

    @Reference
    private AgentOperatorFacade agentOperatorFacade;

    /**
     * 分页查询操作员
     */
    @Permission("agent:operator:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<AgentOperatorVO>>> listPage(@RequestBody AgentOperatorQueryVO vo) {
        Map<String, Object> map = new HashMap<>();
        map.put("phone", StringUtil.isEmpty(vo.getPhone()) ? null : vo.getPhone());
        map.put("nameLike", StringUtil.isEmpty(vo.getNameLike()) ? null : vo.getNameLike());
        map.put("status", vo.getStatus());
        map.put("createTimeBegin", vo.getCreateTimeBegin());
        map.put("createTimeEnd", vo.getCreateTimeEnd());
        PageResult<List<AgentOperatorVO>> result = agentOperatorFacade.listPage(map,
                PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(result);
    }
}
