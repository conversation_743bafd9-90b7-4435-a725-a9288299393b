package com.zhixianghui.web.pms.vo.agent.req;
import com.zhixianghui.common.statics.enums.user.agent.AgentOperatorStatusEnum;
import com.zhixianghui.web.pms.vo.PageVo;
import lombok.Data;

import java.util.Date;

@Data
public class AgentOperatorQueryVO extends PageVo {

    /**
     * 操作员姓名
     */
    private String nameLike;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 状态 {@link AgentOperatorStatusEnum#getValue()}
     */
    private Integer status;

    /**
     * 创建时间开始
     */
    private Date createTimeBegin;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;
}
