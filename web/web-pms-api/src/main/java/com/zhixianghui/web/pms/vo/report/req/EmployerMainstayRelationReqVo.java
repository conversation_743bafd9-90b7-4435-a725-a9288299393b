package com.zhixianghui.web.pms.vo.report.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-28 11:14
 **/
@Data
public class EmployerMainstayRelationReqVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 用工企业名称
     */
    @NotEmpty(message="用工企业名称不能为空")
    private String employerName;

    /**
     * 用工企业编号
     */
    @NotEmpty(message="用工企业编号不能为空")
    private String employerNo;

    /**
     * 代征主体名称
     */
    @NotEmpty(message="代征主体名称不能为空")
    private String mainstayName;

    /**
     * 代征主体编号
     */
    @NotEmpty(message="代征主体编号不能为空")
    private String mainstayNo;

    /**
     * 状态
     */
    @NotNull(message="状态不能为空")
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 外部账户密码
     */
    private String externalPassword;

    /**
     * 外部账户帐号
     */
    private String externalUserName;

    /**
     * 是否有外部系统
     */
    private Boolean hasExternalSystem;
}
