package com.zhixianghui.web.pms.biz.file;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.export.ExportStatusEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.pms.vo.invoice.ExportDetailGroupVo;
import com.zhixianghui.web.pms.vo.invoice.ExportDetailVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ExportBiz
 * @Description TODO
 * @Date 2022/2/24 15:28
 */
@Service
public class ExportBiz {

    @Autowired
    private FastdfsClient fastdfsClient;

    @Reference
    private ExportRecordFacade exportRecordFacade;

    public <T> void exportInvoiceDetail(HttpServletResponse response, String fileName, List allGroupData,List allData, Class<ExportDetailGroupVo> clazz0, Class<ExportDetailVo> clazz1){
        ExcelWriter excelWriter = null;
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            excelWriter = EasyExcel.write(response.getOutputStream()).build();
            if (allData != null && !allData.isEmpty()) {
                final WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "关联订单明细").head(clazz1).build();
                excelWriter.write(allData, writeSheet1);
            }

            if (allGroupData != null && !allGroupData.isEmpty()) {
                final WriteSheet writeSheet0 = EasyExcel.writerSheet(0, "开票清单").head(clazz0).build();
                excelWriter.write(allGroupData, writeSheet0);
            }
            excelWriter.finish();
        }catch (Exception e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出失败，系统异常");
        }finally {
            if (excelWriter != null){
                excelWriter.finish();
            }
        }
    }

    public <T> void export(HttpServletResponse response, String fileName, List data, Class<T> clazz){
        ExcelWriter excelWriter = null;
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), clazz).sheet().doWrite(data);
        }catch (Exception e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出失败，系统异常");
        }finally {
            if (excelWriter != null){
                excelWriter.finish();
            }
        }
    }

    public <T> void export(List data, Class<T> clazz, PmsOperator pmsOperator){
        ExcelWriter excelWriter = null;
        File tmpFile = new File(System.getProperty("java.io.tmpdir") + System.getProperty("file.separator") + IdUtil.fastUUID() +".xlsx");
        try {
            excelWriter = EasyExcel.write(tmpFile).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(1, "代开供应商报价单").head(clazz).build();
            excelWriter.write(data, writeSheet);
        }catch (Exception e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出失败，系统异常");
        }finally {
            if (excelWriter != null){
                excelWriter.finish();
            }
        }
        final String uploadFile = fastdfsClient.uploadFile(tmpFile.getAbsolutePath(), tmpFile.getName());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.EXPORT_INVOICE_QUOTE.getFileName());
        record.setReportType(ReportTypeEnum.EXPORT_INVOICE_QUOTE.getValue());
        record.setParamJson(new JSONObject().toJSONString());
        record.setExportStatus(ExportStatusEnum.SUCCESS.getValue());
        record.setFileUrl(uploadFile);

        exportRecordFacade.insert(record);
    }
}
