package com.zhixianghui.web.pms.vo.sign;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/18 15:37
 **/
@Data
public class SignRecordResVo {
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 姓名
     */
    private String receiveName;

    /**
     * 身份证号
     */
    private String receiveIdCardNo;

    /**
     * 手机号
     */
    private String receivePhoneNo;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 信息鉴权状态
     */
    private Integer infoStatus;

    /**
     * 签约状态
     */
    private Integer signStatus;

    /**
     * 错误码
     */
    private String errCode;

    /**
     * 错误信息
     */
    private String errMsg;

    /**
     * 签约文件本地URL
     */
    private String fileUrl;

    /**
     * 身份证背面照链接
     */
    private String idCardBackUrl;

    /**
     * 身份证正面照链接
     */
    private String idCardFrontUrl;

    /**
     * 复印件
     */
    private String idCardCopyUrl;

    /**
     * 半身照
     */
    private String cerFaceUrl;

    private Integer signerType;

    private Integer signType;

    private boolean hasFile;

}
