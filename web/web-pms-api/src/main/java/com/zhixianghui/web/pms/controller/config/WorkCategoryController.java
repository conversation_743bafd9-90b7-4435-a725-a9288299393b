package com.zhixianghui.web.pms.controller.config;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import com.zhixianghui.facade.common.entity.config.WorkCategory;
import com.zhixianghui.facade.common.service.WorkCategoryFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.config.WorkCategoryInsertVo;
import com.zhixianghui.web.pms.vo.config.WorkCategoryUpdateVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 工作岗位类目配置
 * <AUTHOR>
 * @date 2020/8/10
 **/
@RestController
@RequestMapping("workCategory")
public class WorkCategoryController {
    @Reference
    private WorkCategoryFacade workCategoryFacade;

    /**
     * 工作岗位类目保存
     * @param vo
     * @param operator
     * @return
     */
    @PostMapping("save")
    @Permission("config:workCategory:save")
    public RestResult<String> save(@Valid @RequestBody WorkCategoryInsertVo vo, @CurrentUser PmsOperator operator) {
        WorkCategory workCategory = workCategoryFacade.getByCategoryCode(vo.getWorkCategoryCode());
        if(!Objects.isNull(workCategory)){
            return RestResult.success("工作岗位类目编码已存在");
        }
        WorkCategory category = new WorkCategory();
        category.setUpdator(operator.getLoginName());
        category.setUpdateTime(new Date());
        category.setWorkCategoryCode(vo.getWorkCategoryCode());
        category.setWorkCategoryName(vo.getWorkCategoryName());
        category.setParentId(vo.getParentId());
        category.setWorkDesc(vo.getWorkDesc());
        category.setChargeRuleDesc(vo.getChargeRuleDesc());
        category.setVersion(0);
        category.setCreateTime(new Date());
        category.setBusinessDesc(vo.getBusinessDesc());
        vo.getInvoiceCategoryList().forEach(x -> {
            InvoiceCategory invoiceCategory = new InvoiceCategory();
            invoiceCategory.setInvoiceCategoryCode(x.getInvoiceCategoryCode());
            invoiceCategory.setInvoiceCategoryName(x.getInvoiceCategoryName());
            category.getJsonEntity().getInvoiceCategoryList().add(invoiceCategory);
        });
        workCategoryFacade.insert(category);
        return RestResult.success("成功");
    }

    /**
     * 工作岗位类目更新
     * @param vo
     * @param operator
     * @return
     */
    @PostMapping("update")
    @Permission("config:workCategory:update")
    public RestResult<String> update(@Valid @RequestBody WorkCategoryUpdateVo vo, @CurrentUser PmsOperator operator) {
        WorkCategory category = workCategoryFacade.getByCategoryCode(vo.getWorkCategoryCode());
        if(Objects.isNull(category)){
            return RestResult.error("工作岗位类目不存在");
        }
        category.setWorkCategoryName(vo.getWorkCategoryName());
        category.setWorkDesc(vo.getWorkDesc());
        category.setChargeRuleDesc(vo.getChargeRuleDesc());
        category.setBusinessDesc(vo.getBusinessDesc());
        category.setUpdateTime(new Date());
        category.getJsonEntity().setInvoiceCategoryList(new ArrayList<>());
        vo.getInvoiceCategoryList().forEach(x -> {
            InvoiceCategory invoiceCategory = new InvoiceCategory();
            invoiceCategory.setInvoiceCategoryCode(x.getInvoiceCategoryCode());
            invoiceCategory.setInvoiceCategoryName(x.getInvoiceCategoryName());
            category.getJsonEntity().getInvoiceCategoryList().add(invoiceCategory);
        });
        category.setUpdator(operator.getLoginName());
        workCategoryFacade.update(category);
        return RestResult.success("成功");
    }

    /**
     * 工作岗位类目删除
     * @param id
     * @return
     */
    @GetMapping("delete")
    @Permission("config:workCategory:delete")
    public RestResult<String> delete(@RequestParam(name = "id") Long id) {
        List<WorkCategory> categoryList = workCategoryFacade.listSubCategory(id);
        if(CollectionUtils.isNotEmpty(categoryList)){
            return RestResult.error("当前工作岗位类目含有子类目，不允许删除");
        }
        workCategoryFacade.delete(id);
        return RestResult.success("成功");
    }

    /**
     * 根据代码查询工作岗位类目
     * @param workCategoryCode
     * @return
     */
    @PostMapping("getByCategoryCode")
    public RestResult<WorkCategory> getByCategoryCode(@RequestParam(name = "workCategoryCode") String workCategoryCode) {
        WorkCategory category = workCategoryFacade.getByCategoryCode(workCategoryCode);
        return RestResult.success(category);
    }

    /**
     * 查询所有子工作岗位类目
     * @return
     */
    @GetMapping("listAll")
    public RestResult<List<WorkCategory>> listAll() {
        List<WorkCategory> categoryList = workCategoryFacade.listAll();
        return RestResult.success(categoryList);
    }

}
