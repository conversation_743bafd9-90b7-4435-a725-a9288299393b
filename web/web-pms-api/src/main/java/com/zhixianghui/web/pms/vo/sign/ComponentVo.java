package com.zhixianghui.web.pms.vo.sign;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zhixianghui.facade.trade.entity.TemplateComponent;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import com.zhixianghui.facade.trade.vo.sign.StructStyle;
import com.zhixianghui.web.pms.vo.PageVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/15 15:59
 */
@Data
public class ComponentVo extends PageVo {

    /**
     * ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 用工企业编号
     */
    private String label;

    /**
     * 是否必填
     */
    private Integer required;

    /**
     * 是否为预置类型
     */
    private Integer preset;

    @JsonProperty("style")
    private StructStyle structStyle;

    // 输入项组件类型，1-单行文本，2-数字，3-日期，6-签约区，8-多行文本，11-图片
    private Integer type;

    /**
     * 限制组件左右上下拖动
     */
    @JsonProperty("handles")
    private List<String> behavior;

    /**
     * 是否可以缩放
     */
    private Integer resizable;


}
