package com.zhixianghui.web.pms.controller.config;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import com.zhixianghui.facade.common.service.InvoiceCategoryFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.config.InvoiceCategoryQueryVo;
import com.zhixianghui.web.pms.vo.config.InvoiceCategoryVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 发票类目配置
 * <AUTHOR>
 * @date 2020/8/10
 **/
@RestController
@RequestMapping("invoiceCategory")
public class InvoiceCategoryController {
    @Reference
    private InvoiceCategoryFacade invoiceCategoryFacade;

    /**
     * 发票类目保存
     * @param vo
     * @param operator
     * @return
     */
    @PostMapping("save")
    @Permission("config:invoiceCategory:save")
    public RestResult<String> save(@Valid @RequestBody InvoiceCategoryVo vo, @CurrentUser PmsOperator operator) {
        InvoiceCategory invoiceCategory = invoiceCategoryFacade.getByCategoryCode(vo.getInvoiceCategoryCode());
        if(!Objects.isNull(invoiceCategory)){
            return RestResult.error("发票类目编码已存在");
        }

        InvoiceCategory category = new InvoiceCategory();
        category.setUpdator(operator.getLoginName());
        category.setUpdateTime(new Date());
        category.setInvoiceCategoryCode(vo.getInvoiceCategoryCode());
        category.setInvoiceCategoryName(vo.getInvoiceCategoryName());
        category.setVersion(0);
        category.setCreateTime(new Date());

        invoiceCategoryFacade.insert(category);
        return RestResult.success("成功");
    }

    /**
     * 发票类目更新
     * @param vo
     * @param operator
     * @return
     */
    @PostMapping("update")
    @Permission("config:invoiceCategory:update")
    public RestResult<String> update(@Valid @RequestBody InvoiceCategoryVo vo, @CurrentUser PmsOperator operator) {
        InvoiceCategory category = invoiceCategoryFacade.getByCategoryCode(vo.getInvoiceCategoryCode());
        if(Objects.isNull(category)){
            return RestResult.error("发票类目不存在");
        }
        category.setInvoiceCategoryName(vo.getInvoiceCategoryName());
        category.setUpdateTime(new Date());
        category.setUpdator(operator.getLoginName());
        invoiceCategoryFacade.update(category);
        return RestResult.success("成功");
    }

    /**
     * 发票类目删除
     * @param id
     * @return
     */
    @PostMapping("delete")
    @Permission("config:invoiceCategory:delete")
    public RestResult<String> delete(@RequestParam(name = "id") Long id) {
        invoiceCategoryFacade.delete(id);
        return RestResult.success("成功");
    }

    /**
     * 根据发票类目代码查询信息
     * @param categoryCode
     * @return
     */
    @PostMapping("getByCategoryCode")
    public RestResult<InvoiceCategory> getByCategoryCode(@RequestParam(name = "categoryCode") String categoryCode) {
        InvoiceCategory category = invoiceCategoryFacade.getByCategoryCode(categoryCode);
        return RestResult.success(category);
    }

    /**
     * 发票类目分页查询
     * @param queryVo
     * @return
     */
    @PostMapping("listPage")
    public RestResult<PageResult<List<InvoiceCategory>>> listPage(@RequestBody InvoiceCategoryQueryVo queryVo) {
        PageParam pageParam = PageParam.newInstance(queryVo.getPageCurrent(), queryVo.getPageSize());
        PageResult<List<InvoiceCategory>> pageResult = invoiceCategoryFacade.listPage(BeanUtil.toMap(queryVo), pageParam);
        return RestResult.success(pageResult);
    }

    @GetMapping("getAll")
    public RestResult getAll(){
        List<InvoiceCategory> list = invoiceCategoryFacade.listAll();
        return RestResult.success(list);
    }
}
