package com.zhixianghui.web.pms.biz.record;


import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.vo.agent.SetSellerVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.facade.merchant.vo.record.AgentBaseInfoVo;
import com.zhixianghui.facade.merchant.vo.record.AgentProductFeeVo;


/**
 * <AUTHOR>
 * @Date 2021/7/22 16:26
 */
public interface Builder {
    default <T> void build(T o1, PmsOperator pmsOperator) {

    }

    default <T> void build(T o1, PmsOperator operator, Long id) {

    }

}
