package com.zhixianghui.web.pms.controller.trade;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.trade.dto.ChangesFoundsDTO;
import com.zhixianghui.facade.trade.service.ChangesFundsFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月23日 10:34:00
 */
@RestController
@RequestMapping("/changesFunds")
public class ChangesFundsController{

    @Reference
    private ChangesFundsFacade changesFundsFacade;


    @PostMapping("/listPage")
    public RestResult listPage(@RequestBody ChangesFoundsDTO changesFoundsDTO){
        return RestResult.success(changesFundsFacade.listPage(changesFoundsDTO));
    }
}