package com.zhixianghui.web.pms.controller.trade;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.dto.MainstayAdjustmentDTO;
import com.zhixianghui.facade.trade.service.AcMerchantBalanceFacade;
import com.zhixianghui.facade.trade.service.WxMerchantBalanceFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2024年5月21日 17:16:00
 */
@RestController
@RequestMapping("/acMerchantBalance")
public class AcMerchantBalanceController {

    @Reference(retries = -1)
    private AcMerchantBalanceFacade acMerchantBalanceFacade;

    @Reference
    private SequenceFacade sequenceFacade;

    @Permission("ac:mainstay:balance:sync")
    @PostMapping("/mainstayBalanceSyncOne/{mainstayNo}")
    @Logger(type = OperateLogTypeEnum.MODIFY,name = "供应商同步[汇聚支付]的本地余额")
    public RestResult<String> mainstayBalanceSyncOne(@PathVariable("mainstayNo") String mainstayNo) {
        acMerchantBalanceFacade.mainstayBalanceSyncOne(mainstayNo);
        return RestResult.success("同步余额成功");
    }

    /**
     * 商户人工君享汇调账
     * @return
     */
    @Permission("ac:balance:adjustment")
    @PostMapping("/adjustment")
    @Logger(type = OperateLogTypeEnum.MODIFY,name = "商户人工君享汇调账")
    public RestResult adjustment(@Validated @RequestBody AdjustmentDTO adjustmentDTO, @CurrentUser PmsOperator operator) {
        // 生成业务编号
        String logKey = sequenceFacade.nextRedisId(SequenceBizKeyEnum.AC_ADJUSTMENT.getPrefix(),
                SequenceBizKeyEnum.AC_ADJUSTMENT.getKey(),
                SequenceBizKeyEnum.AC_ADJUSTMENT.getWidth());

        adjustmentDTO.setLogKey(logKey);
        adjustmentDTO.setOperator(operator.getRealName());
        acMerchantBalanceFacade.adjustment(adjustmentDTO);
        return RestResult.success("调账申请成功，请耐心等待几分钟后刷新页面查看结果，请勿重复申请调账");
    }

    /**
     * 供应商人工君享汇调账
     * @return
     */
    @Permission("ac:mainstay:balance:adjustment")
    @PostMapping("/mainstayAdjustment")
    @Logger(type = OperateLogTypeEnum.MODIFY,name = "供应商人工君享汇调账")
    public RestResult mainstayAdjustment(@Validated @RequestBody MainstayAdjustmentDTO mainstayAdjustmentDTO, @CurrentUser PmsOperator operator) {
        AdjustmentDTO adjustmentDTO=new AdjustmentDTO();
        BeanUtil.copyProperties(mainstayAdjustmentDTO, adjustmentDTO);

        // 生成业务编号
        String logKey = sequenceFacade.nextRedisId(SequenceBizKeyEnum.AC_MAINSTAY_ADJUSTMENT.getPrefix(),
                SequenceBizKeyEnum.AC_MAINSTAY_ADJUSTMENT.getKey(),
                SequenceBizKeyEnum.AC_MAINSTAY_ADJUSTMENT.getWidth());

        adjustmentDTO.setLogKey(logKey);
        adjustmentDTO.setOperator(operator.getRealName());
        acMerchantBalanceFacade.adjustment(adjustmentDTO);
        return RestResult.success("调账申请成功，请耐心等待几分钟后刷新页面查看结果，请勿重复申请调账");
    }

}