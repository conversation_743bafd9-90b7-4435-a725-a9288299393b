package com.zhixianghui.web.pms.controller.report;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.file.ExportBiz;
import com.zhixianghui.web.pms.biz.report.EmployerAccountInfoBiz;
import com.zhixianghui.web.pms.vo.report.MerchantAccountExportExcelVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class EmployerAccountInfoDownloadController {

    @Autowired
    private EmployerAccountInfoBiz employerAccountInfoBiz;
    @Autowired
    private ExportBiz exportBiz;

    @Logger(type = OperateLogTypeEnum.QUERY, name="导出用工企业账户余额")
    @Permission("report:employerAccountInfo:view")
    @GetMapping("download/exportAmountWithMerchantInfo")
    public void exportAmountWithMerchantInfo(
            @RequestParam(required = false) String mchLike,
            @RequestParam(required = false) Integer mchType,
            HttpServletResponse servletResponse){
        if (StringUtils.isBlank(mchLike) || mchType == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请输入查询信息");
        }

        List<MerchantAccountExportExcelVo> result = new ArrayList<>();

        Map<String, Object> param = new HashMap<>();
        if (mchType.intValue() == MerchantTypeEnum.EMPLOYER.getValue()) {
            param.put("employerLike", mchLike);
            param.put("status", 100);
        } else if (mchType.intValue() == MerchantTypeEnum.MAINSTAY.getValue()) {
            param.put("mainstayLike", mchLike);
            param.put("status", 100);
        }
        final PageResult<List<Map<String, String>>> pageResult = employerAccountInfoBiz.groupByMch(param, PageParam.newInstance(1,999999));
        final List<Map<String, String>> list = pageResult.getData();
        if (list == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂无可导出数据");
        }

        for (Map<String, String> map : list) {
            final String employerNo = map.get("employerNo");
            final String employerName = map.get("employerName");

            MerchantAccountExportExcelVo resultItem = new MerchantAccountExportExcelVo();
            Map<String,Object> accountMap = employerAccountInfoBiz.getAmountByEmployerNoAnyMainstayNo(null,employerNo);
            final List<Map<String,Object>> amountList = (List<Map<String, Object>>) accountMap.get("amountList");
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (Map<String, Object> item : amountList) {
                String bankAmount = (String) item.get("bankAmount");
                String aliPayAmount = (String) item.get("aliPayAmount");
                String weixinAmount = (String) item.get("weixinAmount");
                bankAmount = StringUtil.isDecimal(bankAmount) ? bankAmount : "0";
                aliPayAmount = StringUtil.isDecimal(aliPayAmount) ? aliPayAmount : "0";
                weixinAmount = StringUtil.isDecimal(weixinAmount) ? weixinAmount : "0";
//                log.info("bankAmount={},alipayAmount={},weixinAmount={}",bankAmount,aliPayAmount,weixinAmount);
                BigDecimal mainstayAmount = new BigDecimal(bankAmount).add(new BigDecimal(aliPayAmount)).add(new BigDecimal(weixinAmount));
                totalAmount = totalAmount.add(mainstayAmount);
            }
            resultItem.setTotalAmount(totalAmount);
            resultItem.setEmployerName(employerName);
            resultItem.setEmployerNo(employerNo);
            result.add(resultItem);
        }

        exportBiz.export(servletResponse, "用工企业账户余额", result, MerchantAccountExportExcelVo.class);
    }
}
