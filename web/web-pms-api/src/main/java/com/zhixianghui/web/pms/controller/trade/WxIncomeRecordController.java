package com.zhixianghui.web.pms.controller.trade;


import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.UUIDUitl;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.export.vo.ExportVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.WxIncomeRecord;
import com.zhixianghui.facade.trade.enums.WxAmountChangeLogTypeEnum;
import com.zhixianghui.facade.trade.enums.WxIncomeRecordEnum;
import com.zhixianghui.facade.trade.service.WxIncomeRecordFacade;
import com.zhixianghui.facade.trade.vo.WxAmountChangeLogVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.trade.IncomeRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Slf4j
@RestController
@RequestMapping("/wxIncomeRecord")
public class WxIncomeRecordController {

    @Reference
    private WxIncomeRecordFacade wxIncomeRecordFacade;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    private NotifyFacade notifyFacade;

    @Reference
    private ExportRecordFacade exportRecordFacade;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @PostMapping("listPage")
    public RestResult listPage(@RequestBody Map<String,Object> paramMap,@RequestBody PageVo pageVo){
        return RestResult.success(wxIncomeRecordFacade.listPage(paramMap,pageVo.toPageParam()));
    }

    @Logger(type = OperateLogTypeEnum.MODIFY,name = "人工审核拒绝微信来账通知")
    @PostMapping("cancel")
    public RestResult cancel(@RequestBody @Valid IncomeRecordVo incomeRecordVo,@CurrentUser PmsOperator pmsOperator){
        WxIncomeRecord wxIncomeRecord = wxIncomeRecordFacade.getById(incomeRecordVo.getId());
        wxIncomeRecord.setStatus(WxIncomeRecordEnum.CLOSE.getValue());
        wxIncomeRecord.setUpdator(pmsOperator.getRealName());
        wxIncomeRecordFacade.update(wxIncomeRecord);
        return RestResult.success("此来账通知关闭成功");
    }

    @Permission("wx:incomeRecord:confirm")
    @Logger(type = OperateLogTypeEnum.MODIFY,name = "人工审核通过微信来账通知")
    @PostMapping("confirm")
    public RestResult confirm(@RequestBody @Valid IncomeRecordVo incomeRecordVo, @CurrentUser PmsOperator pmsOperator){
        if (StringUtils.isBlank(incomeRecordVo.getMchNo())){
            return RestResult.error("请选择商户");
        }
        WxIncomeRecord wxIncomeRecord = wxIncomeRecordFacade.getById(incomeRecordVo.getId());
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade
                .getByEmployerNoAndMainstayNoAndChannelNo(incomeRecordVo.getMchNo(),wxIncomeRecord.getMainstayNo(), ChannelNoEnum.WXPAY.name());
        if (employerAccountInfo == null || employerAccountInfo.getStatus() == CommonStatusEnum.INACTIVE.getValue()){
            return RestResult.error("商户微信通道未开通或处于关闭状态");
        }
        wxIncomeRecord.setPayImgUrl(incomeRecordVo.getPayImgUrl());
        wxIncomeRecord.setStatus(WxIncomeRecordEnum.INCOMING.getValue());
        wxIncomeRecord.setUpdator(pmsOperator.getRealName());
        wxIncomeRecord.setMchNo(incomeRecordVo.getMchNo());
        wxIncomeRecord.setMchName(incomeRecordVo.getMchName());
        wxIncomeRecordFacade.update(wxIncomeRecord);

        //记录日志
        WxAmountChangeLogVo wxAmountChangeLogVo = new WxAmountChangeLogVo()
                .setHandler(pmsOperator.getRealName())
                .setChangeType(WxAmountChangeLogTypeEnum.DEPOSIT.getValue())
                .setTotalAmount(wxIncomeRecord.getAmount())
                .setFreezeAmount(0L)
                .setLogKey(wxIncomeRecord.getIncomeRecordId())
                .setIncomeId(wxIncomeRecord.getId())
                .setSubMchId(wxIncomeRecord.getSubMchId())
                .setSuccessTime(wxIncomeRecord.getSuccessTime())
                .setRemark(wxIncomeRecord.getRechargeRemark())
                .setPayImgUrl(wxIncomeRecord.getPayImgUrl())
                .setIncomeType(wxIncomeRecord.getIncomeType());
        log.info("人工处理通过微信来账通知，银行来账订单号：[{}]，日志信息：[{}]",wxAmountChangeLogVo.getLogKey(),JSON.toJSONString(wxAmountChangeLogVo));
        notifyFacade.sendOne(MessageMsgDest.TOPIC_WX_AMOUNT_CHANGE, UUIDUitl.generateString(10),wxAmountChangeLogVo.getLogKey(),
                NotifyTypeEnum.WX_AMOUNT_CHANGE.getValue(),MessageMsgDest.TAG_WX_AMOUNT_CHANGE, JSON.toJSONString(wxAmountChangeLogVo));
        return RestResult.success("入账请求已提交，请稍后查看结果");
    }

    @PostMapping("export")
    public RestResult<String> export(@RequestBody Map<String,Object> paramMap, @CurrentUser PmsOperator operator){
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.WX_INCOME_EXPORT_ENUM.getFileName());
        record.setReportType(ReportTypeEnum.WX_INCOME_EXPORT_ENUM.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.WX_INCOME_EXPORT_ENUM.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
