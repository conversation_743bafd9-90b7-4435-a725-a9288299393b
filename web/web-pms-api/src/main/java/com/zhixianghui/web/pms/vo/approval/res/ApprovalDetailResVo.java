package com.zhixianghui.web.pms.vo.approval.res;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlowDetail;
import com.zhixianghui.facade.common.enums.FlowHandleType;
import com.zhixianghui.facade.common.enums.HandleStatus;
import com.zhixianghui.facade.common.enums.PlatformSource;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-08-17 17:06
 **/
@Data
public class ApprovalDetailResVo {
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 审批流程ID
     */
    private Long approvalFlowId;

    /**
     * 处理人ID
     */
    private Long handlerId;

    /**
     * 处理人名称
     */
    private String handlerName;

    /**
     * 节点类型(会签/或签) 参考{@link FlowHandleType}
     */
    private Integer handlerType;

    /**
     * 步骤序号
     */
    private Integer stepNum;

    /**
     * 节点状态 参考{@link HandleStatus}
     */
    private Integer status;

    /**
     * 执行节点操作的人(主要用于记录管理员)
     */
    private String operatorName;

    /**
     * 额外信息
     */
    private String extInfo;

    /**
     * 是否已处理过(log)
     */
    private Boolean isHistory;

    /**
     * 流程耗时（秒）
     */
    private long spentTime;

    /**
     *  审批意见
     */
    private String approvalOpinion;

    /**
     * 平台来源 参考{@link PlatformSource}
     */
    private Integer platform;

    public static ApprovalDetailResVo buildVo(ApprovalFlowDetail approvalFlowDetail) {
        ApprovalDetailResVo approvalDetailResVo = new ApprovalDetailResVo();
        BeanUtils.copyProperties(approvalFlowDetail, approvalDetailResVo);
        if(approvalFlowDetail.getUpdateTime() != null){
            long spentTime = approvalFlowDetail.getUpdateTime().getTime() - approvalFlowDetail.getCreateTime().getTime();
            approvalDetailResVo.setSpentTime(spentTime/1000);
        }
        return approvalDetailResVo;
    }
}
