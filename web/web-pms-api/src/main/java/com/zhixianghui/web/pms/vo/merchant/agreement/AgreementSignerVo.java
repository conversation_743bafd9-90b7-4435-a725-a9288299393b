package com.zhixianghui.web.pms.vo.merchant.agreement;

import com.zhixianghui.facade.merchant.entity.AgreementSigner;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-03 16:11
 **/
@Data
public class AgreementSignerVo {


    private Integer signerType;

    /**
     * 签署人编号
     */
    @NotBlank(message = "请选择签署商户")
    private String signerNo;

    /**
     * 签署人商户名称
     */
    @NotBlank(message = "请选择签署商户")
    private String signerMchName;

    /**
     * 签署人名
     */
    @NotBlank(message = "协议负责人名称不能为空")
    private String signerName;

    /**
     *签署人手机号
     */
    @NotBlank(message = "协议负责人手机号不能为空")
    private String signerPhone;


    public static AgreementSigner toEntity(AgreementSignerVo agreementSignerVo){
        AgreementSigner agreementSigner = new AgreementSigner();
        BeanUtils.copyProperties(agreementSigner<PERSON>o,agreementSigner);
        return agreementSigner;
    }
}
