package com.zhixianghui.web.pms.controller;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.facade.trade.service.WxIncomeRecordFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.common.CaptchaHelper;
import com.zhixianghui.web.pms.constant.PermissionConstant;
import com.zhixianghui.web.pms.entity.Session;
import com.zhixianghui.web.pms.utils.NetUtil;
import com.zhixianghui.web.pms.vo.permission.PmsFunctionVO;
import com.zhixianghui.web.pms.vo.user.CaptchaVO;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公共接口
 */
@RestController
@RequestMapping("user")
public class UserController extends BaseController {
    private Logger logger = LoggerFactory.getLogger(UserController.class);

    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    @Reference
    private PmsPermissionFacade pmsPermissionFacade;

    @Reference
    private WxIncomeRecordFacade wxIncomeRecordFacade;

    @Value("${loginPrivateKey}")
    private String loginPrivateKey;

    @Autowired
    private CaptchaHelper captchaHelper;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 获取图形验证码
     * @return
     */
    @PostMapping("captcha")
    public RestResult<CaptchaVO> captcha() {
        return RestResult.success(captchaHelper.genCaptcha());
    }

    @GetMapping("emailCode")
    public RestResult<String> emailCode(@RequestParam String email) {

        if (StringUtils.isBlank(email)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("获取验证码邮箱不能为空");
        }

        if (!ValidateUtil.isEmail(email)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("获取验证码邮箱格式不正确");
        }

        final PmsOperator operator = pmsOperatorFacade.getOperatorByLoginName(email);
        if (Objects.isNull(operator)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该邮箱在系统中不存在");
        }

        captchaHelper.sendEmailCode(email);
        return RestResult.success("邮箱验证码发送成功");
    }

    /**
     * 设置登录密码（使用手机验证码）
     * @return
     * 前端请求此接口前，需要先请求/getUserPublickey接口获取动态的公钥
     */
    @PostMapping("resetPwd")
    //@com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.MODIFY)
    public RestResult<String> resetPwd(@RequestParam String email,
                                       @RequestParam String pwd,
                                       @RequestParam String emailCode,
                                       HttpServletRequest request) {

        if (StringUtils.isBlank(email) || StringUtils.isBlank(pwd) || StringUtils.isBlank(emailCode)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数缺失，请检查");
        }

        final PmsOperator operator = pmsOperatorFacade.getOperatorByLoginName(email);

        if (operator == null) {
            return RestResult.error("帐号未注册，请联系客服");
        }

        captchaHelper.verifyEmailCode(email, emailCode);

        // 密码解密
        pwd = RSAUtil.decryptByPrivateKey(pwd, loginPrivateKey);

        if (!ValidateUtil.validPassword(pwd, 8, 16, true, true, false)) {
            return RestResult.error("登录密码必须由字母、数字组成,8--16位");
        }

        pmsOperatorFacade.updateOperatorPwd(operator.getId(), DigestUtils.sha1Hex(pwd), false);
        super.logEdit("重置操作员[" + operator.getLoginName() + "]的密码", true);

        captchaHelper.invalidEmailCode(email);
        return RestResult.success("设置密码成功");
    }

    /**
     * 登录
     */
    @RequestMapping("login")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.LOGIN, name = "用户登录")
    public RestResult<Map<String, Object>> login(@RequestBody JSONObject reqJson, HttpServletRequest request) {
        String loginName = reqJson.getString("username");
        String loginPwd = reqJson.getString("password");//此为加密后的密码
        String captchaId = reqJson.getString("captchaId");
        String captchaValue = reqJson.getString("captchaValue");
        String emailCode = reqJson.getString("emailCode");
        logger.info("运营后台登录，用户名:{}",loginName);
        if (StringUtils.isBlank(captchaId) || StringUtils.isBlank(captchaValue)) {
            return RestResult.error("验证码必填");
        }

        if (StringUtil.isEmpty(loginName) || StringUtil.isEmpty(loginPwd)) {
            return RestResult.error("用户名和密码必填");
        }

        /**
         * 校验验证码
         */
        captchaHelper.verifyCaptcha(captchaId, captchaValue);

        final String opened = dataDictionaryFacade.getSystemConfig("OPEN_EMAIL_VERIFY");
        if (opened != null && StringUtils.equals(opened,"true")) {
            if (!captchaHelper.hitIpWhiteList(NetUtil.getIpAddr(request))) {
                String email = loginName;
                if (StringUtils.equals("admin", loginName)) {
                    email = dataDictionaryFacade.getSystemConfig("ADMIN_EMAIL");
                    if (email == null) {
                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("超管无接收验证码邮箱，请联系技术人员");
                    }
                }
                captchaHelper.verifyEmailCode(email, emailCode);
            }
        }

        PmsOperator operator = pmsOperatorFacade.getOperatorByLoginName(loginName);
        if (operator == null) {
            return RestResult.error("用户名或密码不正确");
        } else if (operator.getStatus() == PmsOperatorStatusEnum.INACTIVE.getValue()) {
            return RestResult.error("该帐号已被冻结");
        } else if (operator.getValidDate() != null && DateUtil.compare(operator.getValidDate(), new Date(), Calendar.DATE) < 0) {
            return RestResult.error("该帐号口令已过有效期");
        }
        try {
            //将rsa加密后的密码解密
            loginPwd = RSAUtil.decryptByPrivateKey(loginPwd, loginPrivateKey);
        } catch (Exception e){}


        // 加密明文密码，验证密码
        if (operator.getLoginPwd().equals(DigestUtils.sha1Hex(loginPwd))) {// 密码正确
            if (PmsOperatorStatusEnum.UNAUDITED.getValue() == operator.getStatus()) {
                return RestResult.error("账号未审核");
            }
            // 更新操作员登录数据
            operator.setLastLoginTime(Optional.ofNullable(operator.getCurrLoginTime()).orElse(new Date()));
            operator.setCurrLoginTime(new Date());
            operator.setPwdErrorCount(0); // 错误次数设为0
            pmsOperatorFacade.updateOperator(operator);
            String token = sessionManager.register(request, operator);
            getCurrentSession().setAttribute(PermissionConstant.OPERATOR_SESSION_KEY, operator);
            return RestResult.success(Collections.singletonMap("token", token));
        } else {
            // 密码错误
            logger.warn("==>帐号【{}】密码错误", operator.getLoginName());
            // 错误次数加1
            operator.setPwdErrorCount(Optional.ofNullable(operator.getPwdErrorCount()).orElse(0) + 1);
            operator.setPwdErrorTime(new Date()); // 设为当前时间
            String msg = "";
            if (operator.getPwdErrorCount() >= PermissionConstant.WEB_PWD_INPUT_ERROR_LIMIT) {
                // 超5次就冻结帐号
                operator.setStatus(PmsOperatorStatusEnum.INACTIVE.getValue());
                msg += "密码已连续输错【" + PermissionConstant.WEB_PWD_INPUT_ERROR_LIMIT + "】次，帐号已被冻结";
                super.logLoginError("登录出错,密码已连续输错【" + PermissionConstant.WEB_PWD_INPUT_ERROR_LIMIT + "】次，帐号已被冻结", operator);
            } else {
                msg += "用户名或密码错误，剩余【" + (PermissionConstant.WEB_PWD_INPUT_ERROR_LIMIT - operator.getPwdErrorCount()) + "】次机会";
                super.logLoginError("登录出错,密码输入错误.剩余[" + (PermissionConstant.WEB_PWD_INPUT_ERROR_LIMIT - operator.getPwdErrorCount()) + "]次机会。", operator);
            }
            pmsOperatorFacade.updateOperator(operator);
            return RestResult.error(msg);
        }
    }

    /**
     * 获取当前登录用户信息
     */
    @RequestMapping("info")
    public RestResult<Map<String, Object>> info(@CurrentUser PmsOperator currentOperator) {
        List<PmsFunction> allFunctions = null;
        if (currentOperator.getType() == PmsOperatorTypeEnum.ADMIN.getValue() ||  pmsOperatorFacade.isAdmin(currentOperator.getId())) {
            allFunctions = pmsPermissionFacade.listAllFunction();

        } else {
            allFunctions = pmsPermissionFacade.listFunctionByOperatorId(currentOperator.getId());
        }
        getCurrentSession().setAttribute(PermissionConstant.PERMISSION_SESSION_KEY, allFunctions.stream().map(PmsFunction::getPermissionFlag).collect(Collectors.toList()));
        Map<String, Object> result = new HashMap<>();
        result.put("functions", allFunctions.stream().map(PmsFunctionVO::buildVo).collect(Collectors.toList()));
        result.put("id", currentOperator.getId());
        result.put("name", currentOperator.getLoginName());
        result.put("realName", currentOperator.getRealName());
        result.put("type", currentOperator.getType());
        return RestResult.success(result);
    }


    /**
     * 退出登录
     */
    @RequestMapping("logout")
    public RestResult<String> logout(HttpServletRequest request, @CurrentUser PmsOperator currentOperator) {
        if (currentOperator != null) {
            super.logLogin("退出系统");
        }
        Session session = sessionManager.getSession(request);
        if (session != null) {
            sessionManager.unRegister(session);           //清空session
        }
        return RestResult.success("");
    }

    @GetMapping("emailVerification")
    public RestResult<Boolean> emailVerification(@RequestParam("email") String email,HttpServletRequest request) {

        final String opened = dataDictionaryFacade.getSystemConfig("OPEN_EMAIL_VERIFY");
        if (opened == null || !StringUtils.equals(opened,"true")) {
            return RestResult.success(false);
        }

        final PmsOperator loginName = pmsOperatorFacade.getOperatorByLoginName(email);
        if (loginName == null) {
            return RestResult.success(true);
        }

        final String ipAddr = NetUtil.getIpAddr(request);

        if (captchaHelper.hitIpWhiteList(ipAddr)) {
            return RestResult.success(false);
        }

        if (StringUtils.equals("admin", email)) {
            email = dataDictionaryFacade.getSystemConfig("ADMIN_EMAIL");
            if (email == null) {
                return RestResult.success(false);
            }
        }
        captchaHelper.sendEmailCode(email);
        return RestResult.success(true);
    }
}
