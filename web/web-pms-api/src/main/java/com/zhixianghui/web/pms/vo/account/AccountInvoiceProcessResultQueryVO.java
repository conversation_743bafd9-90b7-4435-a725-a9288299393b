package com.zhixianghui.web.pms.vo.account;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Author: Cmf
 * Date: 2019.12.2
 * Time: 16:27
 * Description:
 */
@Data
public class AccountInvoiceProcessResultQueryVO implements Serializable {
    private String accountProcessNo; //账务处理流水号
    private String trxNo;           //平台流水号
    private String employerMchNo;
    private String mainstayMchNo;
    private String accountNo;
    private Integer callbackStage;  //发送阶段 com.zhixianghui.common.statics.enums.account.AccountProcessResultCallbackStage
    private Integer auditStage;     //审核阶段com.zhixianghui.common.statics.enums.account.AccountProcessResultAuditStage
    private Integer processResult;  //处理结果 com.zhixianghui.common.statics.constants.common.PublicStatus 1成功，-1失败
    private Date createTimeBegin;   //账务处理结果创建时间-开始
    private Date createTimeEnd;     //账务处理结果创建时间-结束
    private Integer isFromAsync;        //异步账务com.zhixianghui.common.statics.constants.common.PublicStatus 1是，-1否
}
