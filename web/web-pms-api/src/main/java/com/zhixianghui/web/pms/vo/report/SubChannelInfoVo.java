package com.zhixianghui.web.pms.vo.report;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 子商户通道信息 (用工企业-支付通道信息)
 * @date 2020-09-29 14:48
 **/
@Data
public class SubChannelInfoVo {

    /**
     * 出款账户名称
     */
    private String employerName;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 通道名称
     */
    private String payChannelName;

    /**
     * 通道类型
     */
    private Integer channelType;

    /**
     * 支付通道编号
     */
    private String payChannelNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 子商户编号
     */
    private String subMerchantNo;

    /**
     * 父商户编号
     */
    private String parentMerchantNo;

    /**
     * 密钥
     */
    private String employerKey;

    /**
     * 父商户协议号码
     */
    private String parentAgreementNo;

    /**
     * 子商户协议号
     */
    private String subAgreementNo;
}
