package com.zhixianghui.web.pms.biz.report;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.report.MerchantChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantChannel;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantChannelFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.report.req.EmployerMainstayRelationQueryVo;
import com.zhixianghui.web.pms.vo.report.req.EmployerMainstayRelationReqVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-28 11:11
 **/
@Service
public class EmployerMainstayRelationBiz {
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantChannelFacade merchantChannelFacade;

    public void create(EmployerMainstayRelationReqVo employerMainstayRelationReqVo, PmsOperator currentOperator) {
        String employerNo = employerMainstayRelationReqVo.getEmployerNo();
        String mainstayNo = employerMainstayRelationReqVo.getMainstayNo();

        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到用工企业编号为[" + employerNo + "]的商户");
        }

//        if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户状态未激活,编号为[" + employerNo + "]的商户");
//        }
        Merchant mainstay = merchantQueryFacade.getByMchNo(mainstayNo);
        if (mainstay == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到编号为[" + employerNo + "]的代征主体");
        }
        if (employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo, mainstayNo) == null) {
            EmployerMainstayRelation employerMainstayRelation = new EmployerMainstayRelation();
            BeanUtils.copyProperties(employerMainstayRelationReqVo, employerMainstayRelation);
            employerMainstayRelation.setCreateOperator(currentOperator.getRealName());
            employerMainstayRelation.setUpdateTime(new Date());
            employerMainstayRelation.setUpdateOperator(currentOperator.getRealName());
            employerMainstayRelation.setAccountStatus(MerchantChannelTypeEnum.SUCCESS.getType());
            employerMainstayRelation.setHasExternalSystem(Boolean.FALSE);
            employerMainstayRelationFacade.create(employerMainstayRelation);
        }

        MerchantChannel merchantChannel = merchantChannelFacade.getByMchNo(mainstayNo);
        if (merchantChannel == null || merchantChannel.getChannelType() != MerchantChannelTypeEnum.INTERFACE.getType()) {
            return;
        }
        // 如果是走接口报备的代征主体, 还需要同步数据给代征主体
        try {
            merchantChannelFacade.handle(merchantChannel, merchant);
        } catch (Exception e) {
            // 更新为失败状态
            EmployerMainstayRelation relation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo, mainstayNo);
            relation.setAccountStatus(MerchantChannelTypeEnum.FAIL.getType());
            employerMainstayRelationFacade.updateIfNotNull(relation);
        }
    }

    public void changeStatus(Long id, Integer status, String realName) {
        employerMainstayRelationFacade.changeStatus(id, status, realName);
    }

    public void update(EmployerMainstayRelationReqVo employerMainstayRelationReqVo, PmsOperator currentOperator) {
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getById(employerMainstayRelationReqVo.getId());
        if (employerMainstayRelation == null) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此代征关系, id:" + employerMainstayRelationReqVo.getId());
        }
        BeanUtils.copyProperties(employerMainstayRelationReqVo, employerMainstayRelation);
        employerMainstayRelation.setUpdateTime(new Date());
        employerMainstayRelation.setUpdateOperator(currentOperator.getRealName());
        employerMainstayRelationFacade.updateIfNotNull(employerMainstayRelation);
    }

    public void delete(Long id) {
        employerMainstayRelationFacade.deleteById(id);
    }

    public PageResult<List<EmployerMainstayRelation>> listPage(EmployerMainstayRelationQueryVo queryVo, PageParam pageParam) {
        return employerMainstayRelationFacade.listPage(BeanToMapUtil.beanToMap(queryVo), pageParam);
    }

    public EmployerMainstayRelation getById(Long id) {
        return employerMainstayRelationFacade.getById(id);
    }

    public List<EmployerMainstayRelation> getRelationListByEmployerNo(String employerNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo", employerNo);
        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        return employerMainstayRelationFacade.listBy(paramMap);
    }

    public void batchUpdate(Integer status, List<Long> ids) {
        employerMainstayRelationFacade.batchUpdate(status,ids);
    }
}
