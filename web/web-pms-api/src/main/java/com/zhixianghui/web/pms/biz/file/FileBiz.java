package com.zhixianghui.web.pms.biz.file;

import com.alibaba.excel.EasyExcel;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.biz.CommonBiz;
import com.zhixianghui.web.pms.biz.file.excel.FunctionDataListener;
import com.zhixianghui.web.pms.vo.permission.PmsFunctionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;


/**
 * <AUTHOR>
 * @Date 2021/4/6 14:27
 */
@Service
@Slf4j
public class FileBiz<T, V> {

    private static final int HEAD_ROW_NUMBER = 1;

    public void importFile(PmsOperator operator, PmsFunctionVO vo, CommonBiz commonBiz, Class<T> tClass) {

        try {
            EasyExcel.read(vo.getFile().getInputStream(), tClass, new FunctionDataListener().new CheckListener<T>(HEAD_ROW_NUMBER)).
                    sheet().
                    headRowNumber(HEAD_ROW_NUMBER).
                    doRead();
            EasyExcel.read(vo.getFile().getInputStream(), tClass, new FunctionDataListener().new GenerateListenerListener<T, V>(HEAD_ROW_NUMBER, commonBiz)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        } catch (IOException e) {
            log.error("导入文件异常: " + e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("系统异常...");
        }
    }
}
