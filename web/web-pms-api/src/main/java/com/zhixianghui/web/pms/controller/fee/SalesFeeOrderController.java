package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.fee.entity.AgentFeeOrder;
import com.zhixianghui.facade.fee.entity.MerchantFeeOrder;
import com.zhixianghui.facade.fee.entity.SalesFeeOrder;
import com.zhixianghui.facade.fee.service.AgentFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.service.SalesFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.vo.SalesFeeSumVo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.vo.fee.MerchantFeeOrderQueryVo;
import com.zhixianghui.web.pms.vo.fee.SalesFeeOrderQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售计费订单
 */
@Slf4j
@RestController
@RequestMapping("salesFeeOrder")
public class SalesFeeOrderController {
    @Reference
    private AgentFeeOrderQueryFacade agentQueryFacade;
    @Reference
    private SalesFeeOrderQueryFacade queryFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private MerchantFacade merchantFacade;

    /**
     * 销售计费订单分页查询
     * @param vo
     * @return
     */
    @Permission("fee:salesFeeOrder:list")
    @PostMapping("listPage")
    public RestResult<PageResult<List<SalesFeeOrder>>> listPage(@RequestBody @Valid SalesFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);

        // 销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }

        PageResult<List<SalesFeeOrder>> pageResult = queryFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(pageResult);
    }

    /**
     * 销售计费订单分页分组查询
     * @param vo
     * @return
     */
    @Permission("fee:salesFeeOrder:list")
    @PostMapping("listPageGroup")
    public RestResult<PageResult<List<SalesFeeOrder>>> listPageGroup(@RequestBody @Valid SalesFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);

        // 销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }

        PageResult<List<SalesFeeOrder>> pageResult = queryFacade.listPageGroup(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(pageResult);
    }

    /**
     * 销售成本统计
     *
     * @param vo     .
     * @return .
     */
    @RequestMapping("sumSalesFeeOrder")
    @Permission("fee:salesFeeOrder:sum")
    public RestResult<SalesFeeSumVo> sumSalesFeeOrder(@RequestBody @Valid SalesFeeOrderQueryVo vo, @CurrentUser PmsOperator operator){
        Map<String, Object> paramMap = BeanUtil.toMap(vo);

        // 销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }
        SalesFeeSumVo sumVo = queryFacade.sumSalesFeeOrder(paramMap);
        return RestResult.success(sumVo);
    }

    /**
     * 销售成本订单导出
     *
     * @param vo     .
     * @return .
     */
    @RequestMapping("exportSalesFeeOrder")
    @Permission("fee:salesFeeOrder:export")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "销售成本订单明细-导出")
    public RestResult<String> exportSalesFeeOrder(@Valid @RequestBody SalesFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        // 销售部只能查自己及下属的商户
        if(operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }

        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.SALES_FEE_ORDER.getFileName());
        record.setReportType(ReportTypeEnum.SALES_FEE_ORDER.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.SALES_FEE_ORDER.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private List<Long> getSubSalerIds(PmsOperator operator) {
        List<Long> salerIds = new ArrayList<>();
        try{
            List<PmsOperator> salers = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
            salerIds = salers.stream().map(PmsOperator::getId).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            salerIds.add(operator.getId());
        }
        return salerIds;
    }
}
