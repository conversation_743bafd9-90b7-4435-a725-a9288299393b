package com.zhixianghui.web.pms.vo.trade.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-09 14:28
 **/
@Data
public class CertificateQueryVo {


    private String employerNameLike;
    /**
     * 商户名称
     */
    private String employerName;
    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 支付通道
     */
    private String payChannelNo;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;
    /**
     * 收款账户
     */
    private String receiveAccountNo;
    /**
     * 收款姓名
     */
    private String receiveName;

    /**
     * 创建起始时间
     */
    @NotNull(message = "创建起始时间不能为空")
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    @NotNull(message = "创建截止时间不能为空")
    private Date createEndDate;
}
