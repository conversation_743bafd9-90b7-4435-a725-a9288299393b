package com.zhixianghui.web.pms.controller.approval;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.approval.ApprovalFlowDetailBiz;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.approval.req.HandleApprovalDetailReqVo;
import com.zhixianghui.web.pms.vo.approval.res.ApprovalDetailResVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 审批流程节点详情Controller
 * @date 2020-08-17 14:09
 **/
@RestController
@RequestMapping("/approvalDetail")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ApprovalFlowDetailController extends BaseController {

    private final ApprovalFlowDetailBiz approvalFlowDetailBiz;

    /**
     * 审批流程各节点详情
     * @param approvalFlowId 审批流程id
     * @return 审批流程各节点处理详情
     */
    @GetMapping("/getDetail")
    public RestResult<List<ApprovalDetailResVo>> getApprovalDetail(@RequestParam Long approvalFlowId,@CurrentUser PmsOperator currentOperator){
        List<ApprovalDetailResVo> result = approvalFlowDetailBiz.getApprovalDetail(approvalFlowId,currentOperator);
        return RestResult.success(result);
    }

    /**
     * 操作某一节点（同意/不同意）
     * @param handleApprovalDetailReqVo  处理参数vo
     * @param currentOperator 操作人
     * @return 操作结果
     */
    @PostMapping("/handleDetail")
    public RestResult<String> handleApprovalDetail(@Validated @RequestBody HandleApprovalDetailReqVo handleApprovalDetailReqVo, @CurrentUser PmsOperator currentOperator){
        return approvalFlowDetailBiz.handleApprovalDetail(handleApprovalDetailReqVo,currentOperator);
    }
}
