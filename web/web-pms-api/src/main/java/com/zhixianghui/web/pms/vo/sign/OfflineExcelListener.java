package com.zhixianghui.web.pms.vo.sign;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.vo.OfflineExcelRow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OfflineExcelListener
 * @Description TODO
 * @Date 2022/12/14 10:50
 */
@Slf4j
public class OfflineExcelListener extends AnalysisEventListener<OfflineExcelRow> {

    private List<OfflineExcelRow> list = new ArrayList<>();

    private NotifyFacade notifyFacade;

    private String topic;

    private String tag;

    public OfflineExcelListener(NotifyFacade notifyFacade,String topic,String tag){
        this.notifyFacade = notifyFacade;
        this.topic = topic;
        this.tag = tag;
    }

    @Override
    public void invoke(OfflineExcelRow offlineExcelRow, AnalysisContext analysisContext) {
        //仅校验参数
        if (StringUtils.isBlank(offlineExcelRow.getUserName())){
            return;
        }
        if (StringUtils.isBlank(offlineExcelRow.getIdcardNo())){
            return;
        }
        if (StringUtils.isBlank(offlineExcelRow.getMainstayNo())){
            return;
        }
        if (StringUtils.isBlank(offlineExcelRow.getMerchantNo())){
            return;
        }
        list.add(offlineExcelRow);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(list)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("线下导入签约人员失败，没有可导入的数据");
        }
        list.forEach(x->{
            notifyFacade.sendOne(topic,
                    x.getMerchantNo(),
                    x.getIdcardNo(),
                    NotifyTypeEnum.SIGN_NOTIFY.getValue(),
                    tag,
                    JsonUtil.toString(x));
        });
    }
}
