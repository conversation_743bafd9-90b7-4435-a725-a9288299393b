package com.zhixianghui.web.pms.controller.trade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.trade.dto.UserInfoQueryDto;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("userInfo")
public class UserInfoController {

    @Reference
    private UserInfoFacade userInfoFacade;

    @PostMapping("listPage")
    @Permission("userInfo:view")
    public RestResult<IPage<UserInfo>> userInfoPage(@RequestBody Page<UserInfo> page, @RequestBody UserInfoQueryDto userInfoQueryDto) {
        if (page == null) {
            page = new Page<>(1, 20);
        }
        return RestResult.success(userInfoFacade.userInfoIPage(page, userInfoQueryDto));
    }

    @PostMapping("deleteById")
    @Permission("userInfo:edit")
    public RestResult<String> deleteById(@RequestParam(required = true) Long id) {
        userInfoFacade.deleteId(id);
        return RestResult.success("操作成功");
    }
}
