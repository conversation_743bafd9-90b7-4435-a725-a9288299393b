package com.zhixianghui.web.pms.vo.fee;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class SalesFeeOrderQueryVo {

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 供应商编号
     */
    private String vendorNo;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 商户名称模糊查询
     */
    private String mchNameLike;

    /**
     * 产品编码
     */
    private String productNo;

    /**
     * 销售id
     */
    private Long salerId;

    /**
     * 创建时间起
     */
    private String createTimeBegin;

    /**
     * 创建时间止
     */
    private String createTimeEnd;

    @NotEmpty(message = "交易时间起不能为空")
    private String tradeTimeBegin;

    @NotEmpty(message = "交易时间止不能为空")
    private String tradeTimeEnd;

    @NotNull(message = "页码不能为空")
    private Integer pageCurrent = 1;

    @NotNull(message = "每页数量不能为空")
    private Integer pageSize = 10;

    /**
     * 数据库表不存在的字段, 覆盖时记得保留该字段
     * 是否有合伙人
     */
    private Boolean existAgent;
}