package com.zhixianghui.web.pms.vo.report.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @ClassName ReportModifyVo
 * @Description TODO
 * @Date 2023/1/30 16:41
 */
@Data
public class ReportModifyVo {

    @NotBlank(message = "供应商编号不能为空")
    private String mainstayNo;

    @NotBlank(message = "商户编号不能为空")
    private String employerNo;

    // @NotBlank(message = "通道编号不能为空")
    private String payChannelNo;
}
