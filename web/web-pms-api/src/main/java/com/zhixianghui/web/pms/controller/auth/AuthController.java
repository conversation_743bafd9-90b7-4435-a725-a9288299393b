package com.zhixianghui.web.pms.controller.auth;

import com.zhixianghui.common.statics.enums.bankLink.auth.AuthTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.trade.dto.AuthReqDto;
import com.zhixianghui.facade.trade.service.AuthRecordFacade;
import com.zhixianghui.facade.trade.vo.auth.AuthResponseVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("auth")
public class AuthController {

    @Reference
    private AuthRecordFacade authRecordFacade;

    @PostMapping("authTest")
    public RestResult<AuthResponseVo> authTest(@Valid @RequestBody AuthReqDto authReqDto) {
        //参数校验
        if (StringUtils.isBlank(authReqDto.getPhoneNo())) {
            if (authReqDto.getAuthType() == AuthTypeEnum.IDCARD_NAME_PHONE.getValue()||authReqDto.getAuthType() == AuthTypeEnum.IDCARD_NAME_BANKCARD_PHONE.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该鉴权方式手机号码不能为空");
            }
        }else {
            if (!ValidateUtil.isMobile(authReqDto.getPhoneNo())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("手机号码格式不正确");
            }
        }

        if (StringUtils.isBlank(authReqDto.getBankAccountNo())) {
            if (authReqDto.getAuthType() == AuthTypeEnum.IDCARD_NAME_BANKCARD.getValue()||authReqDto.getAuthType() == AuthTypeEnum.IDCARD_NAME_BANKCARD_PHONE.getValue()) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该鉴权方式银行卡号不能为空");
            }
        }else {
            if (!ValidateUtil.isBankCard(authReqDto.getBankAccountNo())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("银行卡号格式不正确");
            }
        }

        return RestResult.success(authRecordFacade.auth(authReqDto));
    }

}
