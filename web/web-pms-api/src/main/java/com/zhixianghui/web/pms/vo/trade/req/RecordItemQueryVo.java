package com.zhixianghui.web.pms.vo.trade.req;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 打款交易流水查询
 * @date 2020-11-12 17:31
 **/
@Data
public class RecordItemQueryVo {

    /**
     * 处理状态
     */
    private Integer processStatus;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 支付通道
     */
    private String payChannelNo;

    /**
     * 通道类型（发放方式）
     */
    private Integer channelType;

    /**
     * 打款请求流水号
     */
    private String remitPlatTrxNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 商户名称
     */
    private String employerNameLike;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 收款账户(卡号/支付宝账号)
     */
    private String receiveAccountNo;

    /**
     * 创建起始时间
     */
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    private Date createEndDate;

    /**
     * 完成起始时间
     */
    private Date completeBeginDate;

    /**
     * 完成截止时间
     */
    private Date completeEndDate;
}
