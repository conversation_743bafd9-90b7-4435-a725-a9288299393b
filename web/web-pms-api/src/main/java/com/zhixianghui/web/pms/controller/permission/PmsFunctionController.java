/**
 *
 */
package com.zhixianghui.web.pms.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.user.pms.PmsFunctionTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsFunction;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.file.FileBiz;
import com.zhixianghui.web.pms.biz.merchant.PmsBiz;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.utils.MultipartFileUtil;
import com.zhixianghui.web.pms.vo.file.FileVo;
import com.zhixianghui.web.pms.vo.permission.PmsFunctionEditVO;
import com.zhixianghui.web.pms.vo.permission.PmsFunctionVO;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 功能管理
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("pmsFunction")
public class PmsFunctionController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(PmsFunctionController.class);
    @Reference
    private PmsPermissionFacade pmsPermissionFacade;
    @Autowired
    private FileBiz<FileVo.PmsFile, PmsFunction> fileBiz;
    @Autowired
    private PmsBiz pmsBiz;

    /**
     * 查询所有功能
     */
    @Permission("pms:function:view")
    @GetMapping("listAllPmsFunction")
    public RestResult<List<PmsFunctionVO>> listAllPmsFunction() {
        List<PmsFunction> allFunctions = pmsPermissionFacade.listAllFunction();
        return RestResult.success(allFunctions.stream().map(PmsFunctionVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 新增功能
     */
    @Permission("pms:function:add")
    @PostMapping("addPmsFunction")
    public RestResult<String> addPmsFunction(@RequestBody @Valid PmsFunctionVO vo) {
        if (vo.getFunctionType() == PmsFunctionTypeEnum.MENU_TYPE.getValue()) {
            // 菜单项需要校验 URL 字段
            if (StringUtil.isEmpty(vo.getUrl())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("URL不能为空");
            }
            if (!ValidateUtil.isStrLengthValid(vo.getUrl(), 2, 100)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("URL长度限定2到100");
            }
        }
        try {
            pmsPermissionFacade.createFunction(PmsFunctionVO.buildDto(vo));
            super.logSave("添加功能[" + vo.getName() + "]", true);
            return RestResult.success("添加功能成功");
        } catch (Exception e) {
            // 记录系统操作日志
            logger.error("addPmsFunction", e);
            super.logSave("权限标识可能已经存在", false);
            return RestResult.error("权限标识可能已经存在");
        }
    }

    /**
     * 修改功能
     */
    @Permission("pms:function:edit")
    @PostMapping("editPmsFunction")
    public RestResult<String> editPmsFunction(@RequestBody @Valid PmsFunctionEditVO vo) {
        try {
            PmsFunction current = pmsPermissionFacade.getFunctionByFlag(vo.getPermissionFlag());
            if (current == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("改权限标识不存在");
            }
            if (current.getFunctionType() == PmsFunctionTypeEnum.MENU_TYPE.getValue()) {
                // 菜单项需要校验 URL 字段
                if (StringUtil.isEmpty(vo.getUrl())) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("URL不能为空");
                }
                if (!ValidateUtil.isStrLengthValid(vo.getUrl(), 2, 100)) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("URL长度限定2到100");
                }
            }
            current.setName(vo.getName());
            current.setNumber(vo.getNumber());
            current.setUrl(vo.getUrl());
            pmsPermissionFacade.updateFunction(current);
            // 记录系统操作日志
            super.logEdit("修改功能，功能名称[" + vo.getName() + "]", true);
            return RestResult.success("修改成功");
        } catch (Exception e) {
            // 记录系统操作日志
            logger.error("editPmsFunction", e);
            super.logEdit("修改功能，功能名称[" + vo.getName() + "]", false);
            return RestResult.error("修改失败");
        }
    }


    /**
     * 删除功能
     * @param id    功能id
     */
    @Permission("pms:function:delete")
    @RequestMapping("deletePmsFunction")
    public RestResult<String> deletePmsFunction(@RequestParam("id") long id) {
        PmsFunction function = null;
        try {
            if ((function = pmsPermissionFacade.getFunctionById(id)) == null) {
                return RestResult.error("无法获取要删除的数据");
            }

            // 先判断此菜单下是否有子菜单
            if (pmsPermissionFacade.listFunctionByParentId(id).stream().anyMatch(p -> p.getFunctionType() == PmsFunction.MENU_TYPE)) {
                return RestResult.error("此功能下关联有个子菜单，不能支接删除!");
            }
            // 删除掉Function以及其子Function（该方法会同时删除与这些function关联的roleFunction）
            pmsPermissionFacade.deleteFunctionById(id);

            // 记录系统操作日志
            super.logDelete("删除功能，功能名称[" + function.getName() + "]", true);
            return RestResult.success("删除成功");
        } catch (Exception e) {
            // 记录系统操作日志
            logger.error("deletePmsFunction", e);
            super.logDelete("删除功能，功能名称[" + Optional.ofNullable(function).map(PmsFunction::getName).orElse("") + "]", false);
            return RestResult.error("删除功能出错");
        }
    }

    /**
     * 权限的导出
     */
    @Permission("pms:function:export")
    @GetMapping("export")
    public RestResult<String> export(@CurrentUser PmsOperator operator) {
        pmsPermissionFacade.export(operator);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }



    /**
     * 权限的导入
     */
    @Permission("pms:function:import")
    @PostMapping("import")
    public RestResult<String> importFile(PmsFunctionVO vo, @CurrentUser PmsOperator operator) {
        MultipartFileUtil.check(vo);
        fileBiz.importFile(operator, vo, pmsBiz, FileVo.PmsFile.class);
        return RestResult.success("导入成功");
    }

}
