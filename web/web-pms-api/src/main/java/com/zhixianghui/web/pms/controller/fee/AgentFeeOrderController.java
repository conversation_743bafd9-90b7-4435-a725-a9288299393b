package com.zhixianghui.web.pms.controller.fee;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.data.service.CkAgentFeeOrderFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.fee.entity.AgentFeeOrder;
import com.zhixianghui.facade.fee.service.AgentFeeOrderQueryFacade;
import com.zhixianghui.facade.fee.vo.AgentFeeOrderSumVo;
import com.zhixianghui.facade.fee.vo.AgentFeeStatisticVo;
import com.zhixianghui.facade.merchant.entity.Agent;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.AgentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.fee.AgentFeeOrderQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 合伙人计费订单表
 *
 * <AUTHOR>
 * @since 2021-02-22
 */
@RestController
@RequestMapping("agentFeeOrder")
@Slf4j
public class AgentFeeOrderController {

    @Reference
    private AgentFeeOrderQueryFacade queryFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;
    @Reference
    private AgentFacade agentFacade;
    @Reference
    private AgentFeeOrderQueryFacade agentFeeOrderQueryFacade;
    @Reference
    private CkAgentFeeOrderFacade agentFeeOrderFacade;


    @PostMapping("exportAgentStatistics")
    public RestResult exportAgentStatistics(@RequestBody @Valid AgentFeeOrderQueryVo vo,@CurrentUser PmsOperator operator){
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        // 销售只查询自己及下属的合伙人信息
        putIfSaler(operator, paramMap);
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.PMS_AGENT_MERGE_FEE.getFileName());
        record.setReportType(ReportTypeEnum.PMS_AGENT_MERGE_FEE.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setDeepPage(true);

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.PMS_AGENT_MERGE_FEE.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("agentStatistics")
    public RestResult<PageResult<List<AgentFeeStatisticVo>>> agentStatistics(@RequestBody @Valid AgentFeeOrderQueryVo vo,@RequestBody Page<Map<String, Object>> page,@CurrentUser PmsOperator operator){
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        PageParam pageParam = PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize());

        // 销售只查询自己及下属的合伙人信息
        putIfSaler(operator, paramMap);
        PageResult<List<AgentFeeStatisticVo>> pageResult = agentFeeOrderQueryFacade.agentFeeStatistics(paramMap, pageParam);
        return RestResult.success(pageResult);
    }

    /**
     * 合伙人计费订单分页查询
     */
    @Permission("fee:agentFeeOrder:list")
    @PostMapping("listPage")
    public RestResult<PageResult<List<AgentFeeOrder>>> listPage(@RequestBody @Valid AgentFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);

        // 销售只查询自己及下属的合伙人信息
        putIfSaler(operator, paramMap);
        PageResult<List<AgentFeeOrder>> pageResult = queryFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getData())) {
            return RestResult.success(pageResult);
        }
        pageResult.getData().forEach(item -> {
            Agent agent = agentFacade.getByAgentNo(item.getAgentNo());
            item.setSelfDeclare(agent.getSelfDeclared());
        });
        return RestResult.success(pageResult);
    }

    /**
     * 合伙人计费订单导出
     */
    @RequestMapping("exportAgentFeeOrder")
    @Permission("fee:agentFeeOrder:export")
    public RestResult<String> exportAgentFeeOrder(@Valid @RequestBody AgentFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGENT_FEE_ORDER.getFileName());
        record.setReportType(ReportTypeEnum.AGENT_FEE_ORDER.getValue());
        Map<String,Object> paramMap = BeanUtil.toMap(vo);
        putIfSaler(operator,paramMap);
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.AGENT_FEE_ORDER.getDataName());
        dataDictionary.getItemList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 合伙人计费订单统计
     */
    @Permission("fee:agentFeeOrder:sum")
    @PostMapping("sumAgentFeeOrder")
    public RestResult<AgentFeeOrderSumVo> sumAgentFeeOrder(@RequestBody @Valid AgentFeeOrderQueryVo vo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);

        // 销售只查询自己及下属的合伙人信息
        putIfSaler(operator, paramMap);
        AgentFeeOrderSumVo sumVo = queryFacade.sumOrder(paramMap);
        return RestResult.success(sumVo);
    }

    /**
     * 销售部员工增加条件判断
     * @param operator
     * @param paramMap
     */
    private void putIfSaler(PmsOperator operator, Map<String, Object> paramMap) {
        // 销售部只能查自己及下属的商户
        if (operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                // 获取下属销售
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }
    }

    private List<Long> getSubSalerIds(PmsOperator operator) {
        List<Long> salerIds = new ArrayList<>();
        try{
            List<PmsOperator> salers = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
            salerIds = salers.stream().map(PmsOperator::getId).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            salerIds.add(operator.getId());
        }
        return salerIds;
    }
}
