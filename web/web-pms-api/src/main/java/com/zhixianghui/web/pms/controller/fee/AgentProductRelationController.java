package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.fee.entity.AgentProductRelation;
import com.zhixianghui.facade.fee.service.AgentProductRelationFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.fee.AgentProductAddVo;
import com.zhixianghui.web.pms.vo.fee.AgentProductRelationQueryVo;
import com.zhixianghui.web.pms.vo.fee.AgentProductRelationVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 合伙人产品开通
 */
@RestController
@RequestMapping("agentProductRelation")
public class AgentProductRelationController extends BaseController {

    @Reference
    private AgentProductRelationFacade facade;

    /**
     * 分页查询
     */
    @Permission("fee:agentProduct:list")
    @PostMapping("list")
    public RestResult<PageResult<List<AgentProductRelation>>> listPage(@Valid @RequestBody AgentProductRelationQueryVo vo) {
        PageResult<List<AgentProductRelation>> pageResult = facade.listPage(BeanUtil.toMap(vo), PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize(),"UPDATE_TIME DESC"));
        return RestResult.success(pageResult);
    }

    /**
     * 删除
     */
    @Permission("fee:agentProduct:delete")
    @PostMapping("delete/{id}")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.DELETE, name = "删除数据")
    public RestResult<String> delete(@CurrentUser PmsOperator currentOperator, @PathVariable long id) {
        facade.delete(id, currentOperator.getLoginName());
        return RestResult.success("删除操作成功");

    }

    /**
     * 编辑
     */
    @Permission("fee:agentProduct:edit")
    @PostMapping("edit")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.MODIFY, name = "修改数据")
    public RestResult<String> edit(@CurrentUser PmsOperator currentOperator, @Valid @RequestBody AgentProductRelationVo vo) {
        LimitUtil.notEmpty(vo.getId(), "id不能为空");
        AgentProductRelation relation = facade.getById(vo.getId());

        relation.setAgentNo(vo.getAgentNo());
        relation.setAgentName(vo.getAgentName());
        relation.setProductNo(vo.getProductNo());
        relation.setProductName(vo.getProductName());
        relation.setDescription(vo.getDescription());
        relation.setUpdateBy(currentOperator.getLoginName());
        relation.setUpdateTime(new Date());

        facade.update(relation);

        return RestResult.success("修改操作成功");
    }

    /**
     * 添加
     */
    @Permission("fee:agentProduct:add")
    @PostMapping("add")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.CREATE, name = "修改数据")
    public RestResult<String> add(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid AgentProductAddVo addVo) {

        if (addVo == null) {
            RestResult restResult = RestResult.error("参数不能为空");
            restResult.setCode(CommonExceptions.BIZ_INVALID.getSysErrorCode());
            return restResult;
        }

        List<AgentProductRelation> relations = new ArrayList<>();
        for (AgentProductAddVo.SimpleAgent simpleAgent : addVo.getAgents()) {
            for (AgentProductAddVo.SimpleProduct simpleProduct : addVo.getProducts()) {
                AgentProductRelation relation = new AgentProductRelation();
                relation.setAgentNo(simpleAgent.getAgentNo());
                relation.setAgentName(simpleAgent.getAgentName());
                relation.setProductNo(simpleProduct.getProductNo());
                relation.setProductName(simpleProduct.getProductName());
                relation.setStatus(PublicStatus.ACTIVE);
                relation.setRemoved(RemovedEnum.NORMAL.getValue());
                relation.setUpdateBy(currentOperator.getLoginName());
                relation.setUpdateTime(new Date());
                relation.setCreateBy(currentOperator.getLoginName());
                relation.setCreateTime(new Date());
                relations.add(relation);
            }
        }

        facade.insert(relations);

        return RestResult.success("新增操作成功");
    }

    /**
     * 更新状态
     */
    @Permission("fee:agentProduct:updateStatus")
    @GetMapping("updateStatus")
    @com.zhixianghui.web.pms.annotation.Logger(type = OperateLogTypeEnum.MODIFY, name = "修改状态")
    public RestResult<String> updateStatus(@RequestParam Long id,
                                           @RequestParam Integer status,
                                           @CurrentUser PmsOperator currentOperator) {
        LimitUtil.notEmpty(id, "id不能为空");
        LimitUtil.notEmpty(status, "状态码不能为空");

        AgentProductRelation relation = facade.getById(id);
        LimitUtil.notEmpty(relation, "产品开通不存在");

        relation.setStatus(status);
        relation.setUpdateBy(currentOperator.getLoginName());
        relation.setUpdateTime(new Date());
        facade.update(relation);
        return RestResult.success("添加成功");
    }


}
