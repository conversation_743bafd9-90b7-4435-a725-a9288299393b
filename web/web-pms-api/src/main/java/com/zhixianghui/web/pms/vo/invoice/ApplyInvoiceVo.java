package com.zhixianghui.web.pms.vo.invoice;

import com.zhixianghui.common.statics.enums.invoice.InvoiceAmountTypeEnum;
import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 申请开票vo
 * <AUTHOR>
 * @date 2020/12/28
 **/
@Data
public class ApplyInvoiceVo {

    @NotBlank(message = "产品编号不能为空")
    private String productNo;

    @NotEmpty(message = "商户编号不能为空")
    private String employerNo;

    @NotBlank(message = "产品名称不能为空")
    private String productName;

    @NotEmpty(message = "代征主体编号 不能为空")
    private String mainstayMchNo;

    @NotEmpty(message = "代征主体名称 不能为空")
    private String mainstayMchName;

    @NotNull(message = "发票类型 不能为空")
    @EnumValue(intValues = {1,2}, message = "发票类型 有误")
    private Integer invoiceType;

    @NotNull(message = "申请方式 不能为空")
    @EnumValue(intValues = {1,2}, message = "申请方式 有误")
    private Integer applyType;

    @NotNull(message = "发票金额 不能为空")
    private java.math.BigDecimal invoiceAmount;

    @NotEmpty(message = "开票交易时间段起始时间 不能为空")
    @Length(min = 10, max = 10, message = "开票交易时间段起始时间 格式有误")
    private String tradeCompleteDayBegin;

    @NotEmpty(message = "开票交易时间段终止时间 不能为空")
    @Length(min = 10, max = 10, message = "开票交易时间段终止时间 格式有误")
    private String tradeCompleteDayEnd;

    @NotEmpty(message = "发票类目编码 不能为空")
    private String invoiceCategoryCode;

    @NotEmpty(message = "发票类目名称 不能为空")
    private String invoiceCategoryName;

    @Length(max = 200, message = "开票说明长度不能超过200")
    private String remark;

    private Integer amountType = InvoiceAmountTypeEnum.ORDER_AMOUNT.getValue();//开票金额类型

    private String jobId;
    private Integer source;
    private String workCategoryCode;
    private String workCategoryName;
}
