package com.zhixianghui.web.pms.biz.report;

import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.common.dto.PayChannelDto;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.entity.report.PayChannelType;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.utils.BeanToMapUtil;
import com.zhixianghui.web.pms.vo.report.req.PayChannelQueryVo;
import com.zhixianghui.web.pms.vo.report.req.PayChannelReqVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-28 11:11
 **/
@Service
public class PayChannelBiz {
    @Reference
    private PayChannelFacade payChannelFacade;

    public void create(PayChannelReqVo payChannelReqVo, PmsOperator currentOperator) {
        PayChannel payChannel = new PayChannel();
        BeanUtils.copyProperties(payChannelReqVo, payChannel);
        payChannel.setCreateOperator(currentOperator.getRealName());
        payChannel.setUpdateTime(new Date());
        payChannel.setUpdateOperator(currentOperator.getRealName());
        List<PayChannelType> payChannelTypeList = new ArrayList<>();
        //添加支付通道类型
        payChannelReqVo.getChannelTypeList().stream().forEach(x->{
            PayChannelType payChannelType = new PayChannelType();
            payChannelType.setPayChannelNo(payChannelReqVo.getPayChannelNo());
            payChannelType.setType(x);
            payChannelTypeList.add(payChannelType);
        });
        payChannelFacade.create(payChannel,payChannelTypeList);
    }

    public void changeStatus(Long id, Integer status, String realName) {
        payChannelFacade.changeStatus(id, status, realName);
    }

    public void update(PayChannelReqVo payChannelReqVo, PmsOperator currentOperator) {
        PayChannel payChannel = payChannelFacade.getById(payChannelReqVo.getId());
        if(payChannel == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("无此支付通道，通道id:" + payChannelReqVo.getId());
        }
        BeanUtils.copyProperties(payChannelReqVo, payChannel);
        payChannel.setUpdateTime(new Date());
        payChannel.setUpdateOperator(currentOperator.getRealName());
        //通道类型表
        List<PayChannelType> payChannelTypeList = new ArrayList<>();
        payChannelReqVo.getChannelTypeList().stream().forEach(x-> {
            PayChannelType payChannelType = new PayChannelType();
            payChannelType.setPayChannelNo(payChannelReqVo.getPayChannelNo());
            payChannelType.setType(x);
            payChannelTypeList.add(payChannelType);
        });
        payChannelFacade.updateIfNotNull(payChannel,payChannelTypeList);
    }

    public void delete(Long id) {
        payChannelFacade.deleteById(id);
    }

    public PageResult<List<PayChannel>> listPage(PayChannelQueryVo queryVo, PageParam pageParam) {
        return payChannelFacade.listPage(BeanToMapUtil.beanToMap(queryVo),pageParam);
    }

    public PageResult<List<PayChannelDto>> listCustomPage(PayChannelQueryVo queryVo,PageParam pageParam){
        return payChannelFacade.listCustomPage(BeanToMapUtil.beanToMap(queryVo),pageParam);
    }

    public PayChannel getById(Long id) {
        return payChannelFacade.getById(id);
    }
}
