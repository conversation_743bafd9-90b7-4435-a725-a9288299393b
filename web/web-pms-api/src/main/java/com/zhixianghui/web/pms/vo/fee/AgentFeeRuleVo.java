package com.zhixianghui.web.pms.vo.fee;

import com.zhixianghui.common.util.validator.EnumValue;
import com.zhixianghui.web.pms.vo.common.SpecialFeeRuleVo;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.ArrayList;
import java.util.List;

@Data
public class AgentFeeRuleVo{
    private Long id;

    /**
     * 商户编号
     */
    @NotEmpty(message = "商户编号不能为空")
    private String agentNo;

    /**
     * 商户名称
     */
    @NotEmpty(message = "商户名称不能为空")
    private String agentName;

    /**
     * 产品编号
     */
    @NotEmpty(message = "产品编号不能为空")
    private String productNo;

    /**
     * 产品名称
     */
    @NotEmpty(message = "产品名称不能为空")
    private String productName;

    /**
     * 描述
     */
    @Length(max = 50, message = "描述不能超过50个字符")
    private String description;

    @NotNull(message = "邀请奖励计费方式不能为空")
    private Integer calculateMode;

    /**
     * 规则类型
     */
    @NotNull(message = "规则类型不能为空")
    @EnumValue(intValues = {0,1}, message = "请选择正确的规则类型")
    private Integer ruleType;

    /**
     * 一级分佣计费公式类型
     */
    @NotNull(message = "一级分佣计费公式类型不能为空")
    @EnumValue(intValues = {0,1,2}, message = "请选择正确的一级分佣计费公式类型")
    private Integer firstFormulaType;

    /**
     * 一级分佣固定手续费
     */
    @DecimalMin(value = "0", message = "一级分佣固定手续费不能小于0")
    @DecimalMax(value = "99999999.99", message = "一级分佣固定手续费不能大于99999999.99")
    private java.math.BigDecimal firstFixedFee;

    /**
     * 一级分佣费率
     */
    @DecimalMin(value = "0", message = "一级分佣费率不能小于0")
    @DecimalMax(value = "100", message = "一级分佣费率不能大于100")
    private java.math.BigDecimal firstFeeRate;

    /**
     * 二级分佣计费公式类型
     */
    @EnumValue(intValues = {0,1,2}, message = "请选择正确的二级分佣计费公式类型")
    private Integer secondFormulaType;

    /**
     * 二级分佣固定手续费
     */
    @DecimalMin(value = "0", message = "二级分佣固定手续费不能小于0")
    @DecimalMax(value = "99999999.99", message = "二级分佣固定手续费不能大于99999999.99")
    private java.math.BigDecimal secondFixedFee;

    /**
     * 二级分佣费率
     */
    @DecimalMin(value = "0", message = "二级分佣费率不能小于0")
    @DecimalMax(value = "100", message = "二级分佣费率不能大于100")
    private java.math.BigDecimal secondFeeRate;

    /**
     * 最大手续费
     */
    @NotNull
    @DecimalMin(value = "0", message = "最大手续费不能小于0")
    @DecimalMax(value = "99999999.99", message = "最大手续费不能大于99999999.99")
    private java.math.BigDecimal maxFee;

    /**
     * 最小手续费
     */
    @NotNull
    @DecimalMin(value = "0", message = "最小手续费不能小于0")
    @DecimalMax(value = "99999999.99", message = "最小手续费不能大于99999999.99")
    private java.math.BigDecimal minFee;

    /**
     * 优先级
     */
    @NotNull(message = "优先级不能为空")
    @Max(value = 5, message = "优先级不能大于5")
    private Integer priority;

    /**
     * 特殊计费规则参数
     * 数据库字段，请通过 specialFeeRuleList 来操作修改相应值
     */
    @Valid
    private List<SpecialFeeRuleVo> ruleParam = new ArrayList<>();

}