package com.zhixianghui.web.pms.vo.merchant.supplier;
import com.zhixianghui.common.statics.enums.user.supplier.SupplierOperatorStatusEnum;
import com.zhixianghui.web.pms.vo.PageVo;
import lombok.Data;

import java.util.Date;

@Data
public class SupplierOperatorQueryVO extends PageVo {

    /**
     * 操作员姓名
     */
    private String nameLike;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 状态 {@link SupplierOperatorStatusEnum#getValue()}
     */
    private Integer status;

    /**
     * 创建时间开始
     */
    private Date createTimeBegin;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;
}
