package com.zhixianghui.web.pms.vo.account;

import com.zhixianghui.common.statics.enums.account.AccountProcessPendingStageEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Author: Cmf
 * Date: 2019.12.2
 * Time: 16:27
 * Description:
 */
@Data
public class AccountInvoiceProcessPendingQueryVO implements Serializable {
    private String accountProcessNo;
    private String trxNo;
    private String employerMchNo;
    private String mainstayMchNo;
    /**
     * {@link AccountProcessPendingStageEnum}
     */
    private Integer processStage;

    private Date createTimeBegin;
    private Date createTimeEnd;

}
