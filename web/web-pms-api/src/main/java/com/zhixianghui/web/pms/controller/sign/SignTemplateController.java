package com.zhixianghui.web.pms.controller.sign;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.entity.TemplateComponent;
import com.zhixianghui.facade.trade.vo.DocTemplateAddOrEditVo;
import com.zhixianghui.facade.trade.vo.SignCustomizeTemplateVo;
import com.zhixianghui.facade.trade.vo.SignTemplateResVo;
import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.sign.SignBiz;
import com.zhixianghui.web.pms.vo.sign.ComponentVo;
import com.zhixianghui.web.pms.vo.sign.SignTemplateVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-06-08
 */
@Slf4j
@RestController
@RequestMapping("/signTemplate")
public class SignTemplateController {
    @Autowired
    private SignBiz signBiz;

    @Autowired
    private RedisClient redisClient;
    private static final long FILE_SIZE = 15 * 1024 * 1024;
    private static final String TEMPLATE_CACHE_PREFIX = "sign:template:";
    private static final String FILE_CACHE_PREFIX = "sign:template:file:";
    private static final int CACHE_TIME = 24 * 3600;

    @PostMapping("/cache")
    public RestResult<String> cache(@RequestBody SignTemplateVo signTemplateVo, @CurrentUser PmsOperator pmsOperator) {
        Assert.notNull(signTemplateVo.getSourceFileUrl(), "文件不能为null");
        String key = TEMPLATE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
        redisClient.set(key, JSONObject.toJSONString(signTemplateVo), CACHE_TIME);
        return RestResult.success("成功");
    }

    @PostMapping("/remove")
    public RestResult<String> remove(@CurrentUser PmsOperator pmsOperator) {
        log.info("当前操作员:" + JSONObject.toJSONString(pmsOperator));
        String key = TEMPLATE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
        redisClient.del(key);
        key = FILE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
        redisClient.del(key);
        return RestResult.success("成功");
    }

    @GetMapping("/get")
    public RestResult<SignTemplateVo> get(@CurrentUser PmsOperator pmsOperator) {
        String key = TEMPLATE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
        SignTemplateVo signTemplateVo = JSONObject.parseObject(redisClient.get(key), SignTemplateVo.class);
        return RestResult.success(signTemplateVo);
    }


    /**
     * 模板列表
     */
    @Permission("sign:template:list")
    @PostMapping("list")
    public RestResult<PageResult<List<SignTemplateResVo>>> list(@RequestBody SignCustomizeTemplateVo queryVo) {
        log.info("列表请求参数:" + JSONObject.toJSONString(queryVo));
        return RestResult.success(signBiz.list(queryVo));
    }

    /**
     * 模板列表
     */
    @Permission("sign:template:del")
    @PostMapping("del")
    public RestResult<?> del(@RequestParam Long id) {
        Assert.notNull(id, "模板id不能为空");
        if (signBiz.del(id)) {
            return RestResult.success("删除成功");
        }
        return RestResult.error("删除失败");
    }

    /**
     * 上传文件
     */

    @Permission("sign:file:upload")
    @PostMapping("upload")
    public RestResult<FileResponse> upload(@RequestParam MultipartFile file, @CurrentUser PmsOperator pmsOperator) {
        Assert.notNull(file, "文件不能为null");
        if (file.getSize() > FILE_SIZE) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件不能超过" + FILE_SIZE/1024/1024 + "M");
        }
        SignCustomizeTemplateVo result = signBiz.uploadTemplate(file);
        String key = FILE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
        redisClient.set(key, JSONObject.toJSONString(result), CACHE_TIME);
        return RestResult.success(new FileResponse(result.getSourceFileUrl(), result.getTargetFileUrl()));
    }

    @Data
    public static class FileResponse {
        private String sourceFileUrl;
        private String targetFileUrl;

        public FileResponse(String sourceFileUrl, String targetFileUrl) {
            this.sourceFileUrl = sourceFileUrl;
            this.targetFileUrl = targetFileUrl;
        }
    }
    /**
     * 新建模板
     */
    @Permission("sign:template:add")
    @PostMapping("add")
    public RestResult<DocTemplateAddOrEditVo> addTemplate(@RequestBody SignTemplateVo signTemplateVo, @CurrentUser PmsOperator pmsOperator) {
        if (signTemplateVo == null || StringUtils.isBlank(signTemplateVo.getTargetFileUrl())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("缺少文件路径");
        }
        String key = FILE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
        SignCustomizeTemplateVo result = JSONObject.parseObject(redisClient.get(key), SignCustomizeTemplateVo.class);
        log.info("添加模板参数:" + JSONObject.toJSONString(result));
        signTemplateVo.setTargetFileUrl(result.getTargetFileUrl());
        signTemplateVo.setSourceFileUrl(result.getSourceFileUrl());
        DocTemplateAddOrEditVo flag;
        try {
            flag = signBiz.addTemplate(signTemplateVo);
        } finally {
            // 删除文件路径缓存
            redisClient.del(key);
            // 删除信息缓存
            key = TEMPLATE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
            redisClient.del(key);
        }
        if (ObjectUtil.isNotEmpty(flag)) {
            return RestResult.success(flag);
        }
        return RestResult.error("添加失败");
    }

    private static final int LABEL_LIMIT = 64;
//    private void checkParam(SignTemplateVo signTemplateVo) {
//        List<StructComponent> list = signTemplateVo.getStructComponent();
//        if (CollectionUtils.isEmpty(list)) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("组件context不能玩为空");
//        }
//        List<String> label = new ArrayList<>();
//        for (StructComponent structComponent : list) {
//            if (structComponent.getContext() == null) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("组件内容不能为空");
//            }
//            if (StringUtils.isBlank(structComponent.getContext().getLabel())) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("组件label不能为空");
//            }
//            if (structComponent.getContext().getLabel().length() > LABEL_LIMIT) {
//                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("label内容不能超过64");
//            }
//            label.add(structComponent.getContext().getLabel());
//        }
//        // 查出必填组件
//        List<String> mustFix = signBiz.componentList().stream().filter(item -> item.getRequired() == 1).
//                map(ComponentVo::getLabel).collect(Collectors.toList());
//        if (!label.containsAll(mustFix)) {
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("没有包含甲方签署区和乙方签署区");
//        }
//    }

    /**
     * 编辑模板
     */
    @Permission("sign:template:modify")
    @PostMapping("modify")
    public RestResult<DocTemplateAddOrEditVo> modify(@RequestBody SignTemplateVo signTemplateVo, @CurrentUser PmsOperator pmsOperator) {
//        checkParam(signTemplateVo);
        String key = FILE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
        String fileInfo = redisClient.get(key);
        if (StringUtils.isNotBlank(fileInfo)) {
            SignCustomizeTemplateVo result = JSONObject.parseObject(fileInfo, SignCustomizeTemplateVo.class);
            signTemplateVo.setTargetFileUrl(result.getTargetFileUrl());
            signTemplateVo.setSourceFileUrl(result.getSourceFileUrl());
        }
        DocTemplateAddOrEditVo flag;
        try {

            flag = signBiz.modify(signTemplateVo);
        } finally {
            // 删除文件和模板信息缓存
            redisClient.del(key);
            key = TEMPLATE_CACHE_PREFIX + pmsOperator.getId() + ":" + pmsOperator.getLoginName();
            redisClient.del(key);
        }
        if (ObjectUtil.isNotEmpty(flag)) {
            return RestResult.success(flag);
        }
        return RestResult.error("修改失败");
    }


    /**
     * 组件列表
     */
    @Permission("sign:template:modify")
    @GetMapping("component")
    public RestResult<List<ComponentVo>> componentList() {
        return RestResult.success(signBiz.componentList());
    }
}
