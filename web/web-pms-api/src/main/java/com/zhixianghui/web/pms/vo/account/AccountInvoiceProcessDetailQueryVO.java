package com.zhixianghui.web.pms.vo.account;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Author: Cmf
 * Date: 2019.12.2
 * Time: 16:27
 * Description:
 */
@Data
public class AccountInvoiceProcessDetailQueryVO implements Serializable {
    private String accountProcessNo; //账务处理流水号
    private String accountNo;      //账户编号
    private String employerMchNo;      //用工商户编号
    private String mainstayMchNo;      //代征主体商户编号
    private String trxNo;           //平台流水号
    private Integer processType;    //账务处理类型 com.zhixianghui.common.statics.enums.account.AccountProcessTypeEnum
    private Date createTimeBegin;   //记账时间-开始
    private Date createTimeEnd;     //记账时间-结束


}
