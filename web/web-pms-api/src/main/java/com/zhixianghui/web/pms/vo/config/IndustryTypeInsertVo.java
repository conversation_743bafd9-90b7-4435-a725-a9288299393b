package com.zhixianghui.web.pms.vo.config;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 行业类型vo
 * <AUTHOR>
 * @date 2020/8/13
 **/
@Data
public class IndustryTypeInsertVo {
    /**
     * 行业类型编码
     */
    @NotEmpty(message = "行业类型编码不能为空")
    @Length(max = 20, message = "行业类型编码长度不能超过20")
    private String industryTypeCode;

    /**
     * 行业类型名称
     */
    @NotEmpty(message = "行业类型名称不能为空")
    @Length(max = 50,  message = "行业类型名称长度不能超过50")
    private String industryTypeName;

    /**
     * 父级行业类型编码
     */
    @NotNull(message = "父级行业类型不能为空")
    private Long parentId;

}
