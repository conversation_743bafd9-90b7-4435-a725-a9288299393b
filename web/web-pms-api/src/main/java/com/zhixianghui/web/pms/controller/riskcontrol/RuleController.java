package com.zhixianghui.web.pms.controller.riskcontrol;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.*;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.riskcontrol.entity.*;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.riskcontrol.service.RiskControlRuleFacade;
import com.zhixianghui.facade.riskcontrol.service.StrategyAtomGroupFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.riskcontrol.RiskcontrolProcessBiz;
import com.zhixianghui.web.pms.vo.riskcontrol.BatchRiskControlRuleVo;
import com.zhixianghui.web.pms.vo.riskcontrol.RiskControlRuleVo;
import com.zhixianghui.web.pms.vo.riskcontrol.StrategyAtomGroupVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 风控管理
 */
@RestController
@RequestMapping("riskcontrol")
@Slf4j
public class RuleController {

    @Reference
    private ExportRecordFacade exportRecordFacade;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @Reference
    private RiskControlRuleFacade riskControlRuleFacade;

    @Reference
    private StrategyAtomGroupFacade strategyAtomGroupFacade;

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    @Reference
    private RiskControlFacade riskControlFacade;

    @Reference
    private DataDictionaryFacade dictionaryFacade;

    @Autowired
    private RiskcontrolProcessBiz riskcontrolProcessBiz;

    /**
     * 查询所有的供应商编号和名称
     * @return
     */
    @GetMapping("getAllSuppliers")
    @Permission("riskcontrol:rule:view")
    public RestResult<List<Map<String, String>>> listAllSuppliers() {
        List<Merchant> suppliers = merchantQueryFacade.listBy(
                Collections.singletonMap("merchantType", MerchantTypeEnum.MAINSTAY.getValue()));

        List<Map<String, String>> res = suppliers.stream().map(supplier -> {
            Map<String, String> supplierVo = new HashMap<>();
            supplierVo.put("mchNo", supplier.getMchNo());
            supplierVo.put("mchName", supplier.getMchName());
            return supplierVo;
        }).collect(Collectors.toList());
        return RestResult.success(res);
    }

    /**
     * 分页查询风控规则
     * @param name  规则名称
     * @param id    规则id
     * @param status    规则状态
     * @param pageSize  每页记录数
     * @param pageCurrent   当前页数
     * @return
     */
    @PostMapping("listRule")
    @Permission("riskcontrol:rule:view")
    public RestResult<PageResult<List<RiskControlRule>>> listRule(@RequestParam(required = false) String name,
                                                                  @RequestParam(required = false) Long id,
                                                                  @RequestParam(required = false) Integer status,
                                                                  @RequestParam(required = false) Integer type,
                                                                  @RequestParam(required = false) String supplierNo,
                                                                  @RequestParam(required = false) String employerNo,
                                                                  @RequestParam(defaultValue = "10") Integer pageSize,
                                                                  @RequestParam(defaultValue = "1") Integer pageCurrent) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("name", name);
        paramMap.put("id", id);
        paramMap.put("status", status);
        paramMap.put("type",type);
        paramMap.put("supplierNo",supplierNo);
        paramMap.put("employerNoListQuery",employerNo);
        return RestResult.success(riskControlRuleFacade.listPage(paramMap, PageParam.newInstance(pageCurrent, pageSize,"t1.update_time DESC")));
    }

    @PostMapping("exportExcel")
    @Permission("riskcontrol:pending:view")
    public RestResult exportExcel(@RequestParam(required = false) String orderNo,
                                  @RequestParam(required = false) String platTrxNo,
                                  @RequestParam(required = false) String employerNo,
                                  @RequestParam(required = false) String employerName,
                                  @RequestParam(required = false) Integer accessPoint,
                                  @RequestParam Integer status,
                                  @RequestParam(required = false) String beginDate,
                                  @RequestParam(required = false) String endDate,@CurrentUser PmsOperator pmsOperator){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("operation", status);
        if (status == PendingOrderOpeEnum.PROCESSED.getValue()) {
            paramMap.put("notOperation", PendingOrderOpeEnum.PENDING.getValue());
            paramMap.put("operation", null);
        }

        paramMap.put("beginDate",beginDate);
        paramMap.put("endDate",endDate);

        //默认查询过去2个月的
        if (StringUtils.isBlank(beginDate) && StringUtils.isBlank(endDate) ){
            paramMap.put("beginDate", DateUtil.addMonth(new Date(), -2));
            paramMap.put("endDate", new Date());
        }

        //只查挂单的
        paramMap.put("mchOrderNo", orderNo);
        paramMap.put("platTrxNo", platTrxNo);
        paramMap.put("employerNo", employerNo);
        paramMap.put("employerName", employerName);
        paramMap.put("accessPoint", accessPoint);

        ExportRecord record = ExportRecord.newDefaultInstance();
        String fileNo = exportRecordFacade.genFileNo();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.RISK_HANDLED_ORDER_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.RISK_HANDLED_ORDER_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.RISK_HANDLED_ORDER_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 风控挂单列表
     * @param orderNo
     * @param platTrxNo
     * @param employerNo
     * @param employerName
     * @param accessPoint
     * @param pageSize
     * @param pageCurrent
     * @return
     */
    @PostMapping("listPendingOrder")
    @Permission("riskcontrol:pending:view")
    public RestResult<PageResult<List<RiskcontrolProcessDetail>>> listPending(@RequestParam(required = false) String orderNo,
                                                                           @RequestParam(required = false) String platTrxNo,
                                                                           @RequestParam(required = false) String employerNo,
                                                                           @RequestParam(required = false) String employerName,
                                                                           @RequestParam(required = false) Integer accessPoint,
                                                                           @RequestParam Integer status,
                                                                           @RequestParam(required = false) String beginDate,
                                                                           @RequestParam(required = false) String endDate,
                                                                           @RequestParam(defaultValue = "10") Integer pageSize,
                                                                           @RequestParam(defaultValue = "1") Integer pageCurrent) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("operation", status);
        if (status == PendingOrderOpeEnum.PROCESSED.getValue()) {
            paramMap.put("notOperation", PendingOrderOpeEnum.PENDING.getValue());
            paramMap.put("operation", null);
        }

        paramMap.put("beginDate",beginDate);
        paramMap.put("endDate",endDate);

        //默认查询过去2个月的
        if (StringUtils.isBlank(beginDate) && StringUtils.isBlank(endDate) ){
            paramMap.put("beginDate", DateUtil.addMonth(new Date(), -2));
            paramMap.put("endDate", new Date());
        }

        //只查挂单的
        paramMap.put("mchOrderNo", orderNo);
        paramMap.put("platTrxNo", platTrxNo);
        paramMap.put("employerNo", employerNo);
        paramMap.put("employerNameLike", employerName);
        paramMap.put("accessPoint", accessPoint);

        return RestResult.success(riskControlFacade.listPage(paramMap, PageParam.newInstance(pageCurrent, pageSize)));
    }

    /**
     * 风控挂单处理
     * @return
     */
    @PostMapping("pendingOperate")
    @Permission("riskcontrol:pending:edit")
    @Logger(name = "风控挂单处理", type = OperateLogTypeEnum.MODIFY)
    public RestResult<String> pendingOperate(@RequestParam @NotEmpty(message = "id列表不能为空") String ids,
                                             @RequestParam @NotNull(message = "操作类型不能为空") Integer operate,
                                             @CurrentUser PmsOperator operator) {
        log.info("后台对风控挂单订单进行操作，其id为{}，其操作为：{}，操作人：{}", ids, operate, operator.getRealName());



        if (!Arrays.asList(PendingOrderOpeEnum.values()).contains(operate)) {
            RestResult.error("operate的值不满足要求");
        }
        riskcontrolProcessBiz.riskcontrolOperate(ids, operate, operator.getRealName());
        return RestResult.success("操作成功");
    }

    /**
     * 根据id查询风控规则
     * @param id    风控规则id
     * @return
     */
    @GetMapping("getRuleById")
    @Permission("riskcontrol:rule:view")
    public RestResult<RiskControlRule> getRuleById(@RequestParam Long id) {
        return RestResult.success(riskControlRuleFacade.getById(id));
    }

    /**
     * 新建风控规则
     * @param vo
     * @return
     */
    @PostMapping("addRule")
    @Permission("riskcontrol:rule:add")
    @Logger(name = "新建风控规则", type = OperateLogTypeEnum.CREATE)
    public RestResult<Map<String,Object>> addRule(@RequestBody @Valid RiskControlRuleVo vo, @CurrentUser PmsOperator operator) {
        log.info("debug RiskControlRuleVo:{}", JSONObject.toJSONString(vo));
        vo.setId(null);
        //参数
        checkRuleParam(vo);
        RiskControlRule riskControlRule = new RiskControlRule();
        riskControlRule.setCreateTime(new Date());
        riskControlRule.setName(vo.getName());
        riskControlRule.setStatus(vo.getStatus());
        riskControlRule.setAccessPoint(vo.getAccessPoint());
        riskControlRule.setType(vo.getType());
        riskControlRule.setSupplierNos(vo.getSupplierNos());
        riskControlRule.setSupplierNames(vo.getSupplierNames());
        riskControlRule.setEmployerNos(vo.getEmployerNos());
        riskControlRule.setEmployerNames(vo.getEmployerNames());
        //规则校验
        //checkRule(riskControlRule);
        if (!CollectionUtils.isEmpty(vo.getRuleRangeList())) {
            riskControlRule.setRuleRangeList(vo.getRuleRangeList().stream().map((range) -> {
                RuleRange ruleRange = new RuleRange();
                ruleRange.setCreateTime(new Date());
                ruleRange.setVariable(range.getVariable());
                ruleRange.setOperator(range.getOperator());
                ruleRange.setConstant(range.getConstant());
                return ruleRange;
            }).collect(Collectors.toList()));
        }
        riskControlRule.setUpdateTime(new Date());
        riskControlRule.setUpdator(operator.getRealName());
        long id = riskControlRuleFacade.create(riskControlRule);
        Map<String,Object> result = new HashMap<>();
        result.put("ruleId", id);
        return RestResult.success(result);
    }

    private void checkRuleParam(RiskControlRuleVo vo) {
        List<String> employerNoList = null;
        if (vo.getType().intValue() == RiskRuleTypeEnum.SPECIAL.getValue()){
            employerNoList = Arrays.asList(vo.getEmployerNos().split(","));
        }

        //部分商户二要素即可，不需要校验要素规则
        boolean isTwoElement = false;
        String twoElementMerchant = dictionaryFacade.getSystemConfig("TWO_ELEMENT_GRANT");
        if (StringUtils.isNotBlank(twoElementMerchant)) {
            ArrayList<String> merchantConfig = ListUtil.toList(twoElementMerchant.split(","));
            if (employerNoList != null && merchantConfig.containsAll(employerNoList)){
                isTwoElement = true;
            }
        }

        //判断是否存在三四要素
        if (!isTwoElement){
            int count = 0;
            for (StrategyAtomGroupVo strategyAtomGroupVo : vo.getAtomTable()) {
                StrategyAtomGroupVo.StrategyAtomVo strategyAtomVo = strategyAtomGroupVo.getStrategyAtomList().stream().filter(x->
                        x.getVariable().intValue() == StrategyAtomVariableEnum.THREE_ELEMENT.getValue() ||
                                x.getVariable().intValue() == StrategyAtomVariableEnum.FOUR_ELEMENT.getValue()).findFirst().orElse(null);
                if (strategyAtomVo == null){
                    count++;
                }
            }

            if (count == vo.getAtomTable().size()){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("没有下发要素规则，创建失败");
            }
        }


        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("type",vo.getType());
        paramMap.put("accessPoint",vo.getAccessPoint());
        paramMap.put("nowId",vo.getId());
        //查询现在存在的数据
        List<RiskControlRule> list = riskControlRuleFacade.listPage(
                paramMap,PageParam.newInstance(1,Integer.MAX_VALUE)).getData();
        List<String> supplierNoList = Arrays.asList(vo.getSupplierNos().split(","));

        for (RiskControlRule riskControlRule : list) {
            List<String> riskSupplierList = new ArrayList<>();
            if (StringUtils.isNotBlank(riskControlRule.getSupplierNos())){
                riskSupplierList.addAll(Arrays.asList(riskControlRule.getSupplierNos().split(",")));
            }
            int supplierSize = riskSupplierList.size();
            riskSupplierList.removeAll(supplierNoList);
            if (vo.getType().intValue() == RiskRuleTypeEnum.GENERAL.getValue()){
                if (supplierSize != riskSupplierList.size()){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("添加失败，存在重复的供应商规则");
                }
            }

            if (vo.getType().intValue() == RiskRuleTypeEnum.SPECIAL.getValue()){
                List<String> riskEmployerList = new ArrayList<>();
                if (StringUtils.isNotBlank(riskControlRule.getEmployerNos())){
                    riskEmployerList.addAll(Arrays.asList(riskControlRule.getEmployerNos().split(",")));
                }
                int employerSize = riskEmployerList.size();
                riskEmployerList.removeAll(employerNoList);
                if (supplierSize != riskSupplierList.size() && employerSize != riskEmployerList.size()){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("添加失败，存在重复的商户-供应商规则");
                }
            }
        }
    }

    /**
     * 更新风控规则
     * @param vo
     * @return
     */
    @PostMapping("updateRule")
    @Permission("riskcontrol:rule:edit")
    @Logger(name = "更新风控规则", type = OperateLogTypeEnum.MODIFY)
    public RestResult<String> updateRule(@RequestBody @Valid RiskControlRuleVo vo, @CurrentUser PmsOperator operator) {
        log.info("debug RiskControlRuleVo:{}", JSONObject.toJSONString(vo));
        //规则校验
        checkRuleParam(vo);

        RiskControlRule riskControlRule = new RiskControlRule();
        riskControlRule.setId(vo.getId());
        riskControlRule.setName(vo.getName());
        riskControlRule.setStatus(vo.getStatus());
        riskControlRule.setAccessPoint(vo.getAccessPoint());
        riskControlRule.setType(vo.getType());
        riskControlRule.setSupplierNos(vo.getSupplierNos());
        riskControlRule.setSupplierNames(vo.getSupplierNames());
        riskControlRule.setEmployerNos(vo.getEmployerNos());
        riskControlRule.setEmployerNames(vo.getEmployerNames());
        riskControlRule.setUpdator(operator.getRealName());

        //checkRule(riskControlRule);
        if (!CollectionUtils.isEmpty(vo.getRuleRangeList())) {
            riskControlRule.setRuleRangeList(vo.getRuleRangeList().stream().map((ruleRangeVo) -> {
                RuleRange ruleRange = new RuleRange();
                ruleRange.setCreateTime(new Date());
                ruleRange.setVariable(ruleRangeVo.getVariable());
                ruleRange.setOperator(ruleRangeVo.getOperator());
                ruleRange.setConstant(ruleRangeVo.getConstant());
                return ruleRange;
            }).collect(Collectors.toList()));
        }
        riskControlRuleFacade.update(riskControlRule);
        return RestResult.success("更新成功");
    }

    /**
     * 根据id删除风控规则
     * @param id    风控规则id
     * @return
     */
    @GetMapping("deleteRuleById")
    @Permission("riskcontrol:rule:delete")
    @Logger(name = "删除风控规则", type = OperateLogTypeEnum.DELETE)
    public RestResult<String> deleteRuleById(@RequestParam Long id) {
        riskControlRuleFacade.deleteById(id);
        return RestResult.success("删除成功");
    }

    /**
     * 根据风控规则id查询策略原子组
     * @param ruleId    风控规则id
     * @return
     */
    @GetMapping("listStrategyAtomGroup")
    @Permission("riskcontrol:rule:view")
    public RestResult<List<StrategyAtomGroup>> listStrategyAtomGroup(@RequestParam Long ruleId) {
        return RestResult.success(strategyAtomGroupFacade.listStrategyAtomGroup(ruleId));
    }

    /**
     * 根据id查询策略原子组
     * @param groupId   原子组id
     * @return
     */
    @GetMapping("getStrategyAtomGroupById")
    @Permission("riskcontrol:rule:view")
    public RestResult<StrategyAtomGroup> getStrategyAtomGroupById(@RequestParam Long groupId) {
        return RestResult.success(strategyAtomGroupFacade.getById(groupId));
    }

    /**
     * 新建策略原子组
     * @param vo
     * @return
     */
    @PostMapping("addStrategyAtomGroup")
    @Permission("riskcontrol:rule:add")
    @Logger(name = "新建策略原子组", type = OperateLogTypeEnum.CREATE)
    public RestResult<String> addStrategyAtomGroup(@RequestBody @Valid StrategyAtomGroupVo vo,
                                                   @CurrentUser PmsOperator operator) {
        addAtomGroup(vo, operator.getRealName());
        return RestResult.success("新建成功");
    }

    /**
     * 批量新建策略原子组
     * @param vos
     * @return
     */
    @PostMapping("addStrategyAtomGroupBatch")
    @Permission("riskcontrol:rule:add")
    @Logger(name = "批量新建策略原子组", type = OperateLogTypeEnum.CREATE)
    public RestResult<Map<String,Object>> addStrategyAtomGroupBatch(@RequestBody @Valid BatchRiskControlRuleVo vos,
                                                                    @CurrentUser PmsOperator operator) {
        int count = 0;
        for (StrategyAtomGroupVo vo : vos.getVos()) {
            addAtomGroup(vo, operator.getRealName());count ++;
        }
        Map<String,Object> result = new HashMap<>();
        result.put("addCount", count);
        return RestResult.success(result);
    }

    /**
     * 更新策略原子组
     * @param vo
     * @return
     */
    @PostMapping("updateStrategyAtomGroup")
    @Permission("riskcontrol:rule:edit")
    @Logger(name = "更新策略原子组", type = OperateLogTypeEnum.MODIFY)
    public RestResult<String> updateStrategyAtomGroup(@RequestBody StrategyAtomGroupVo vo,
                                                      @CurrentUser PmsOperator operator) {
        StrategyAtomGroup strategyAtomGroup = new StrategyAtomGroup();
        strategyAtomGroup.setId(vo.getId());
        strategyAtomGroup.setRuleId(vo.getRuleId());
        strategyAtomGroup.setWeight(vo.getWeight());
        strategyAtomGroup.setControlAtom(vo.getControlAtom());
        strategyAtomGroup.setRemark(vo.getRemark());
        if (!CollectionUtils.isEmpty(vo.getStrategyAtomList())) {
            strategyAtomGroup.setStrategyAtomList(vo.getStrategyAtomList().stream().map(strategyAtomVo -> {
                StrategyAtom strategyAtom = new StrategyAtom();
                strategyAtom.setCreateTime(new Date());
                strategyAtom.setVariable(strategyAtomVo.getVariable());
                strategyAtom.setOperator(strategyAtomVo.getOperator());
                strategyAtom.setConstant(strategyAtomVo.getConstant());
                return strategyAtom;
            }).collect(Collectors.toList()));
        }

        strategyAtomGroupFacade.updateStrategyAtomGroup(strategyAtomGroup, operator.getRealName());
        return RestResult.success("更新成功");
    }

    /**
     * 根据id删除策略原子组
     * @param groupId
     * @return
     */
    @GetMapping("deleteStrategyAtomGroupById")
    @Permission("riskcontrol:rule:delete")
    @Logger(name = "删除策略原子组", type = OperateLogTypeEnum.DELETE)
    public RestResult<String> deleteStrategyAtomGroupById(@RequestParam Long groupId,
                                                          @CurrentUser PmsOperator operator) {
        strategyAtomGroupFacade.deleteStrategyAtomGroup(groupId, operator.getRealName());
        return RestResult.success("删除成功");
    }

    private boolean addAtomGroup(StrategyAtomGroupVo vo, String operator) {
        StrategyAtomGroup strategyAtomGroup = new StrategyAtomGroup();
        strategyAtomGroup.setCreateTime(new Date());
        strategyAtomGroup.setRuleId(vo.getRuleId());
        strategyAtomGroup.setWeight(vo.getWeight());
        strategyAtomGroup.setControlAtom(vo.getControlAtom());
        strategyAtomGroup.setRemark(vo.getRemark());
        if (!CollectionUtils.isEmpty(vo.getStrategyAtomList())) {
            strategyAtomGroup.setStrategyAtomList(vo.getStrategyAtomList().stream().map(strategyAtomVo -> {
                StrategyAtom strategyAtom = new StrategyAtom();
                strategyAtom.setCreateTime(new Date());
                strategyAtom.setVariable(strategyAtomVo.getVariable());
                strategyAtom.setOperator(strategyAtomVo.getOperator());
                strategyAtom.setConstant(strategyAtomVo.getConstant());
                return strategyAtom;
            }).collect(Collectors.toList()));
        }
        strategyAtomGroupFacade.createStrategyAtomGroup(strategyAtomGroup, operator);
        return true;
    }

    /**
     * 对规则做业务校验
     * @param riskControlRule
     */
    public void checkRule(RiskControlRule riskControlRule) {
        if (riskControlRule == null) {
            return;
        }
        //此处为校验特殊类型的供应商只能有一个
        if (riskControlRule.getType().equals(RiskRuleTypeEnum.SPECIAL.getValue())
                && StringUtil.isNotEmpty(riskControlRule.getSupplierNos())) {
            List<String> supplierNos = Arrays.asList(riskControlRule.getSupplierNos().split(","));
            List<String> supplierNames = Arrays.asList(riskControlRule.getSupplierNames().split(","));

            for (int i=0;i<supplierNos.size();i++) {
                String supplierNo = supplierNos.get(i);
                String supplierName = supplierNames.get(i);
                List<RiskControlRule> rules = riskControlRuleFacade.getBySupplierNoType(supplierNo,null);
                //此时说明此供应商已存在，不满足一个特殊的供应商只能有一条
                if (!CollectionUtils.isEmpty(rules)) {
                    if (riskControlRule.getId() == null){//此时为新增记录
                        throw new CommonExceptions().PARAM_INVALID.newWithErrMsg(
                                String.format("选中的供应商【%s】已经存在特殊类型的记录，其ID为：%d，请前去修改而不是新增",supplierName, rules.get(0).getId()));
                    } else {//此时为更新记录，则要判断排除自己后是否还有其他，有则满足抛异常条件
                        rules.stream().forEach(item->{
                            if (!item.getId().equals(riskControlRule.getId())) {
                                throw new CommonExceptions().PARAM_INVALID.newWithErrMsg(
                                        String.format("选中的供应商【%s】已经存在特殊类型的记录，其ID为：%d",supplierName, item.getId()));
                            }
                        });

                    }
                }
            }
        }
        //此处为校验同一接入点的通用规则只能有一条
        if (riskControlRule.getType().equals(RiskRuleTypeEnum.GENERAL.getValue())) {
            List<RiskControlRule> rules = riskControlRuleFacade.getBySupplierNoType(null,RiskRuleTypeEnum.GENERAL.getValue());
            for (RiskControlRule rule : rules) {
                String business = Arrays.asList(RiskAccessPointEnum.values()).stream().
                        filter(item->item.getValue()==rule.getAccessPoint()).findFirst().get().getBusiness();
                //此时为新增的情况
                if (riskControlRule.getId() == null && riskControlRule.getAccessPoint().equals(rule.getAccessPoint())) {
                    throw new CommonExceptions().PARAM_INVALID.newWithErrMsg(
                            String.format("此通用类型的规则已经存在接入点为【%s】的记录，其ID为：%d，请前去修改而不是新增", business, rule.getId()));
                }
                //此时为修改的情况
                if (riskControlRule.getId() != null
                        && riskControlRule.getId() != rule.getId()
                        && riskControlRule.getAccessPoint().equals(rule.getAccessPoint())) {
                        throw new CommonExceptions().PARAM_INVALID.newWithErrMsg(
                                String.format("此通用类型的规则已经存在接入点为【%s】的记录，其ID为：%d",business, rule.getId()));
                }
            }
        }
    }

    @GetMapping("/getSupplierNos")
    public RestResult getSupplierNos() {
        return RestResult.success(riskControlRuleFacade.getSupplierNos());
    }

    @GetMapping("/getNoConfigSuppliers")
    public RestResult getNoConfigSuppliers() {
        return RestResult.success(riskControlRuleFacade.getNoConfigSuppliers());
    }
}
