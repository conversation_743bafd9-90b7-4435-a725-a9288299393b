package com.zhixianghui.web.pms.controller.merchant;

import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;
import com.zhixianghui.facade.trade.vo.MerchantStatVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.merchant.MerchantInfoAnalyzeBiz;
import com.zhixianghui.web.pms.vo.merchant.DataAnalyzeVo;
import com.zhixianghui.web.pms.vo.merchant.SignListVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/7/27 14:19
 */
@Slf4j
@RestController
@RequestMapping("analyze")
public class MerchantInfoAnalyzeController {

    private static final String[] FREELANCE_SORT_COLUMN = {"orderItemNetAmount"};
    private static final String[] MERCHANT_SORT_COLUMN = {"orderItemAmount", "orderItemNetAmount", "receiverNumber", "orderAmount"};
    public static final String[] ORDER = {"asc", "desc"};
    public static final Map<String, Object> SORT_MAP = new HashMap<>();
    static {
        SORT_MAP.put("orderItemNetAmount", "ORDER_ITEM_NET_AMOUNT");
        SORT_MAP.put("orderItemAmount", "ORDER_ITEM_AMOUNT");
        SORT_MAP.put("receiverNumber", "RECEIVER_NUMBER");
        SORT_MAP.put("orderAmount", "ORDER_AMOUNT");
    }

    @Autowired
    private MerchantInfoAnalyzeBiz analyzeBiz;
    @Reference
    private OrderItemFacade orderItemFacade;

//    private void setDate(DataAnalyzeVo analyzeVo) {
//        long flag = System.currentTimeMillis();
//        if (StringUtils.isAnyBlank(analyzeVo.getBeginDate(), analyzeVo.getEndDate())) {
//            log.warn("[{}]查询时间不能为空 : {}, {}", flag, analyzeVo.getBeginDate(), analyzeVo.getEndDate());
//            analyzeVo.setBeginDate(DateUtil.formatYearMonth(new Date()));
//            analyzeVo.setEndDate(DateUtil.formatYearMonth(new Date()));
//        }
//        try {
//            analyzeVo.setStartDate(DateUtil.getFirstOfMonth(DateUtil.yearMonthToDate(analyzeVo.getBeginDate())));
//            analyzeVo.setOverDate(DateUtil.getLastOfMonth(DateUtil.yearMonthToDate(analyzeVo.getEndDate())));
//        } catch (ParseException e) {
//            log.error("[{}]时间参数转成错误 : ", flag, e);
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("内部错误: " + flag);
//        }
//
//    }

    private void checkParam(DataAnalyzeVo analyzeVo) {
        LimitUtil.notEmpty(analyzeVo.getSignType(),"签约方式不能为空");
        LimitUtil.notEmpty(analyzeVo.getIdCard(), "身份证不能为空");
        LimitUtil.notEmpty(analyzeVo.getMainstayNo(), "代征主体编号不能为空");
        LimitUtil.notEmpty(analyzeVo.getEmployerNo(), "用工企业编号不能为空");
        LimitUtil.notEmpty(analyzeVo.getReceiveName(), "姓名不能为空");
        if (analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.MSG.getValue() || analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.URL_CODE.getValue()) {
            LimitUtil.notEmpty(analyzeVo.getPhone(), "该签约模式手机号不能为空");
        }
        if (analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.URL_CODE.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂停对该签约模式的支持");
        }
    }

    private void checkParamList(List<DataAnalyzeVo> dataAnalyzeVos){
        dataAnalyzeVos.forEach(analyzeVo->{
            LimitUtil.notEmpty(analyzeVo.getIdCard(), "身份证不能为空");
            LimitUtil.notEmpty(analyzeVo.getMainstayNo(), "代征主体编号不能为空");
            LimitUtil.notEmpty(analyzeVo.getEmployerNo(), "用工企业编号不能为空");
            LimitUtil.notEmpty(analyzeVo.getReceiveName(), "姓名不能为空");
        });
    }

    @RequestMapping("freelanceList")
    public RestResult<PageResult<List<FreelanceStatVo>>> freelanceList(@RequestBody DataAnalyzeVo analyzeVo) {
        String sortColumn = checkSort(analyzeVo, FREELANCE_SORT_COLUMN);
//        setDate(analyzeVo);
        return RestResult.success(analyzeBiz.freelanceStat(analyzeVo, sortColumn));
    }

    private String checkSort(DataAnalyzeVo analyzeVo, String[] sortColumnArray) {
        String sortColumn = analyzeVo.getSortColumn();
        if (StringUtils.isBlank(sortColumn) || StringUtils.isBlank(analyzeVo.getOrder())) {
            return null;
        }

        if (Arrays.asList(ORDER).contains(analyzeVo.getOrder()) &&
                Arrays.asList(sortColumnArray).contains(analyzeVo.getSortColumn())) {
           return SORT_MAP.get(analyzeVo.getSortColumn()) + " " + analyzeVo.getOrder();
        }
        throw CommonExceptions.PARAM_INVALID.newWithErrMsg("排序字段或规则不合法");
    }

    @RequestMapping("countFreelance")
    public RestResult<FreelanceStatVo> countFreelance(@RequestBody DataAnalyzeVo analyzeVo) {
//        setDate(analyzeVo);
        return RestResult.success(analyzeBiz.countFreelance(analyzeVo));
    }

    @RequestMapping("merchantList")
    public RestResult<PageResult<List<MerchantStatVo>>> list(@RequestBody DataAnalyzeVo analyzeVo, @CurrentUser PmsOperator currentOperator) {
        String sortColumn = checkSort(analyzeVo, MERCHANT_SORT_COLUMN);
//        setDate(analyzeVo);
        return RestResult.success(analyzeBiz.merchantStat(analyzeVo, sortColumn, currentOperator));
    }

    @RequestMapping("countMerchant")
    public RestResult<MerchantStatVo> countMerchant(@RequestBody DataAnalyzeVo analyzeVo, @CurrentUser PmsOperator currentOperator) {
//        setDate(analyzeVo);
        return RestResult.success(analyzeBiz.countMerchant(analyzeVo, currentOperator));
    }

    @RequestMapping("sign")
    public RestResult<String> sign(@RequestBody DataAnalyzeVo analyzeVo) {
        checkParam(analyzeVo);
        if (analyzeBiz.sign(analyzeVo)) {
            return RestResult.success("发起签约成功");
        }
        return RestResult.error("该用户发放时未传手机号码, 请补充手机号码");
    }


    @RequestMapping("signList")
    public RestResult<String> signList(@RequestBody SignListVo signListVo) {
        List<DataAnalyzeVo> dataAnalyzeVos = signListVo.getDataAnalyzeVos();
        if(signListVo.getSignType() == ChannelSignTypeEnum.MSG.getValue() || signListVo.getSignType() == ChannelSignTypeEnum.URL_CODE.getValue()){
            dataAnalyzeVos=dataAnalyzeVos.stream().filter(e->StringUtils.isNoneBlank(e.getPhone())).collect(Collectors.toList());
        }
        checkParamList(dataAnalyzeVos);
        dataAnalyzeVos.forEach(analyzeVo->{
            try {
                analyzeVo.setSignType(signListVo.getSignType());
                analyzeVo.setIdCard(analyzeVo.getReceiveIdCardNo());
                analyzeVo.setPhone(analyzeVo.getReceivePhoneNo());
                analyzeBiz.sign(analyzeVo);
            }catch (BizException e){
                log.error("批量签约异常:",e);
            }
        });
//        checkParam(analyzeVo);
//        if (analyzeBiz.sign(analyzeVo)) {
//            return RestResult.success("发起签约成功");
//        }
        return RestResult.success("操作成功");
    }

    @RequestMapping("uploadIdCard")
    public RestResult<String> uploadIdCard(@RequestBody DataAnalyzeVo analyzeVo) {
        if (!Objects.isNull(analyzeVo.getIdCardType())){
            if (analyzeVo.getIdCardType().intValue() == IdCardTypeEnum.ORIGINAL.getCode().intValue()) {
                LimitUtil.notEmpty(analyzeVo.getIdCardBackUrl(), "身份证背面地址不能为空");
                LimitUtil.notEmpty(analyzeVo.getIdCardFrontUrl(), "身份证正面地址不能为空");
            }else {
                LimitUtil.notEmpty(analyzeVo.getIdCardCopyFileUrl(), "身份证背面地址不能为空");
            }
        }


        LimitUtil.notEmpty(analyzeVo.getId(), "缺少参数id");

        if (analyzeBiz.uploadIdCard(analyzeVo)) {
            return RestResult.success("上传成功");
        }
        return RestResult.error("上传失败");
    }

    @Logger(type = OperateLogTypeEnum.QUERY, name = "导出企业发单数据")
    @RequestMapping("merchantInfoExport")
    public RestResult<String> merchantInfoExport(@RequestBody DataAnalyzeVo analyzeVo, @CurrentUser PmsOperator operator) {
        analyzeVo.setOperator(operator);
        if (analyzeBiz.merchantInfoExport(analyzeVo, operator)) {
            return RestResult.success("导出成功");
        }
        return RestResult.error("导出失败");
    }

    @Logger(type = OperateLogTypeEnum.QUERY, name = "导出个人交付数据")
    @RequestMapping("freelanceExport")
    public RestResult<String> freelanceExport(@RequestBody DataAnalyzeVo analyzeVo, @CurrentUser PmsOperator operator) {
        analyzeVo.setOperator(operator);

        if (analyzeBiz.freelanceExport(analyzeVo, operator)) {
            return RestResult.success("导出成功");
        }
        return RestResult.error("导出失败");
    }

}
