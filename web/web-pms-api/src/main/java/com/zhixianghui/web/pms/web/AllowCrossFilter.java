package com.zhixianghui.web.pms.web;

import com.zhixianghui.web.pms.constant.PermissionConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Author: Cmf
 * Date: 2019/10/24
 * Time: 16:39
 * Description:
 */
@Component
public class AllowCrossFilter implements Filter {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Allow-Method", "*");
        response.addHeader("Access-Control-Allow-Headers", "content-type," + PermissionConstant.REQUEST_TOKEN_HEADER);
//        logger.info("------------------> 请求允许 url: {}", ((HttpServletRequest) servletRequest).getRequestURL().toString());
        filterChain.doFilter(servletRequest, servletResponse);
    }
}
