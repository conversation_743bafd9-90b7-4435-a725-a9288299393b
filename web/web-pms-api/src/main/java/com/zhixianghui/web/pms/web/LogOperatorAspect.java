package com.zhixianghui.web.pms.web;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.enums.LogStatusEnum;
import com.zhixianghui.facade.merchant.service.pms.PmsOperateLogFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.constant.PermissionConstant;
import com.zhixianghui.web.pms.entity.Session;
import com.zhixianghui.web.pms.utils.NetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;

/**
 * @description: 日志记录拦截器
 * @author: xingguang li
 * @created: 2020/09/04 09:58
 */

@Slf4j
@Component
@Aspect
public class LogOperatorAspect {

    private org.slf4j.Logger logger = LoggerFactory.getLogger(LogOperatorAspect.class);

    @Reference
    private PmsOperateLogFacade operateLogFacade;
    @Autowired
    private SessionManager sessionManager;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    @Pointcut("@annotation(com.zhixianghui.web.pms.annotation.Logger)")
    public void saveLog() {
    }

    @Around("saveLog()")
    public Object saveLog(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Logger loggerAnnotation = method.getAnnotation(Logger.class);

        String name = loggerAnnotation == null ? "" : loggerAnnotation.name();
        OperateLogTypeEnum operateTypeEnum = loggerAnnotation.type();
        //参数拼装
        Object[] args = point.getArgs();
        LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
        String[] paramNames = u.getParameterNames(method);
        String params = "";
        String loginName = "";
        if (args != null && paramNames != null) {
            for (int i = 0; i < args.length; i++) {
                //遇到密码的不记录
                if (StringUtil.isNotEmpty(paramNames[i]) &&
                        (paramNames[i].contains("password") || paramNames[i].contains("pwd"))) {
                    continue;
                }
                try {
                    String arg;
                    if (args[i] instanceof JSONObject) {
                        arg = ((JSONObject) args[i]).toJSONString();
                        loginName = ((JSONObject) args[i]).getString("username");
                    } else {
                        arg = String.valueOf(args[i]);
                    }
                    if (StringUtil.isNotEmpty(arg) &&
                            (arg.contains("password") || arg.contains("pwd"))) {
                        continue;
                    }
                }catch (Exception e){}

                params += "  " + paramNames[i] + ": " + args[i];
            }
        }

        PmsOperateLog pmsOperateLog = new PmsOperateLog();

        //当前登录信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Session session = ((Session) request.getAttribute(SessionManager.SESSION_KEY_IN_REQUEST_SCOPE));
        if (session != null) {
            PmsOperator pmsOperator = session.getAttribute(PermissionConstant.OPERATOR_SESSION_KEY, PmsOperator.class);
            pmsOperateLog.setOperatorLoginName(pmsOperator.getLoginName() + "(" + pmsOperator.getMobileNo() + ")");
        } else {
            //未登录的默认用户
            PmsOperator operator = pmsOperatorFacade.getOperatorByLoginName(loginName);
            loginName = operator == null ? loginName : operator.getRealName();
            pmsOperateLog.setOperatorLoginName(StringUtil.isEmpty(loginName) ?"匿名未登录" : loginName);
        }
        String userAgent = request.getHeader("User-Agent");
        String detail = String.format("浏览器信息：%s，请求参数：%s，备注：%s", userAgent, params, name);
        pmsOperateLog.setContent(name);
        pmsOperateLog.setDetail(detail);
        pmsOperateLog.setOperateType(operateTypeEnum.getValue());
        pmsOperateLog.setStatus(LogStatusEnum.CREATED.getCode());
        pmsOperateLog.setCreateTime(new Date());
        pmsOperateLog.setIp(NetUtil.getIpAddr(request));
        //当前系统是pms
        pmsOperateLog.setOperateSource(SystemTypeEnum.BOSS_MANAGEMENT.getValue());

        RestResult restResult;

        try {
            restResult = (RestResult) point.proceed();
            if (RestResult.SUCCESS == restResult.getCode()) {
                pmsOperateLog.setStatus(LogStatusEnum.SUCCESS.getCode());
            } else {
                pmsOperateLog.setStatus(LogStatusEnum.FAIL.getCode());
            }
        } catch (Throwable e) {
            pmsOperateLog.setStatus(LogStatusEnum.FAIL.getCode());
            // 异常抛出，由异常处理器进行处理
            throw e;
        } finally {
            try{
                operateLogFacade.createOperateLog(pmsOperateLog);
            } catch (Throwable e) {
                log.error("登录名：{}，创建操作日志异常，已忽略：{}", loginName, e);
            }
        }
        return restResult;
    }


}
