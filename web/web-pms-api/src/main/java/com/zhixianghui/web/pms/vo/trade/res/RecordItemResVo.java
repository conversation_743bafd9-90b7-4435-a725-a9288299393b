package com.zhixianghui.web.pms.vo.trade.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-12 17:33
 **/
@Data
public class RecordItemResVo {
    /**
     * 打款请求流水号
     */
    private String remitPlatTrxNo;
    /**
     * 商户订单号
     */
    private String mchOrderNo;
    /**
     * 平台流水号
     */
    private String platTrxNo;
    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 收款账户(卡号/支付宝账号)
     */
    private String receiveAccountNo;

    /**
     * 打款明细实发金额
     */
    private BigDecimal orderNetAmount;

    /**
     * 打款明细代征主体服务费
     */
    private BigDecimal orderFee;

    /**
     * 备注
     */
    private String remark;

    /**
     * 通道编号
     */
    private String payChannelNo;

    /**
     * 通道名
     */
    private String channelName;

    /**
     * 处理状态
     */
    private Integer processStatus;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorDesc;
}
