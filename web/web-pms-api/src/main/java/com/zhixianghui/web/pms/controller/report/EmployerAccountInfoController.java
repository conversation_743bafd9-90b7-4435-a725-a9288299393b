package com.zhixianghui.web.pms.controller.report;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.dto.EmployerAccountInfoDto;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.data.dto.AccountDetailQueryDto;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.facade.data.service.CkAccountDetailFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.biz.report.EmployerAccountInfoBiz;
import com.zhixianghui.web.pms.vo.PageVo;
import com.zhixianghui.web.pms.vo.report.EmployerAccountInfoVo;
import com.zhixianghui.web.pms.vo.report.MerchantAccountExportExcelVo;
import com.zhixianghui.web.pms.vo.report.req.EmployerAccountInfoQueryVo;
import com.zhixianghui.web.pms.vo.report.res.EmployerAccountAmountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 用工企业账户Controller
 * @date 2020-09-29 10:56
 **/
@RestController
@RequestMapping("employerAccountInfo")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class EmployerAccountInfoController {

    private final EmployerAccountInfoBiz employerAccountInfoBiz;

    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private CkAccountDetailFacade ckAccountDetailFacade;

    /**
     * 分页查询用工企业账户
     * @param queryVo 查询条件
     * @param pageVo 分页参数
     * @return 用工企业账户列表
     */
    @Permission("report:employerAccountInfo:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<EmployerAccountInfoDto>>> listPage(@RequestBody EmployerAccountInfoQueryVo queryVo,@RequestBody PageVo pageVo) {
        PageResult<List<EmployerAccountInfoDto>> pageResult = employerAccountInfoBiz.listPage(queryVo,pageVo.toPageParam());
        return RestResult.success(pageResult);
    }

    /**
     * 更新用工企业账户
     * @param vo 更新内容
     * @param //currentOperator 操作人
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "更新用工企业账户")
    @Permission("report:employerAccountInfo:edit")
    @PostMapping("update")
    public RestResult<String> update(@Validated @RequestBody EmployerAccountInfoVo vo, @CurrentUser PmsOperator currentOperator) {
        employerAccountInfoBiz.update(vo,currentOperator);
        return RestResult.success("更新成功");
    }

    /**
     * 获取单个用工企业账户
     * @param mainstayNo 供应商编号
     * @param employerNo 用工企业编号
     * @return 用工企业账户
     */
    @Logger(type = OperateLogTypeEnum.QUERY, name = "查询单个用工企业账户")
    @Permission("report:employerAccountInfo:view")
    @PostMapping("getByMainstayNoAndEmployerNo")
    public RestResult<EmployerAccountInfoVo> getByMainstayNoAndEmployerNo(@RequestParam String mainstayNo, @RequestParam String employerNo) {
        EmployerAccountInfoVo employerAccountInfoVo = employerAccountInfoBiz.getByMainstayNoAndEmployerNo(mainstayNo,employerNo);
        return RestResult.success(employerAccountInfoVo);
    }

    /**
     * 查询余额
     * @param mainstayNo 供应商编号
     * @param employerNo 用工企业编号
     * @return 用工企业账户
     */
    @Logger(type = OperateLogTypeEnum.QUERY, name = "查询用工企业账户余额")
    @Permission("report:employerAccountInfo:view")
    @PostMapping("getAmountByMainstayNoAndEmployerNo")
    public RestResult<EmployerAccountAmountVo> getAmountByMainstayNoAndEmployerNo(@RequestParam String mainstayNo, @RequestParam String employerNo) {
        EmployerAccountAmountVo employerAccountAmountVo = employerAccountInfoBiz.getAmountByMainstayNoAndEmployerNo(mainstayNo,employerNo);
        return RestResult.success(employerAccountAmountVo);
    }

    /**
     * 查询用工企业在不同供应商下的账户余额
     * @param mainstayNo
     * @param employerNo
     * @return
     */
    @Logger(type = OperateLogTypeEnum.QUERY, name="查询用工企业在不同供应商下的账户余额")
    @Permission("report:employerAccountInfo:view")
    @PostMapping("getAmountByEmployerNoAnyMainstayNo")
    public RestResult<Map<String,Object>> getAmountByEmployerNoAnyMainstayNo(@RequestParam(required = false) String mainstayNo, @RequestParam String employerNo){
        Map<String,Object> map = employerAccountInfoBiz.getAmountByEmployerNoAnyMainstayNo(mainstayNo,employerNo);
        return RestResult.success(map);
    }

    @Logger(type = OperateLogTypeEnum.QUERY, name="查询用工企业账户余额")
    @Permission("report:employerAccountInfo:view")
    @PostMapping("getAmountWithMerchantInfo")
    public RestResult<PageResult<List<Map<String,Object>>>> getAmountWithMerchantInfo(@RequestParam(required = false) String mchLike,
                                                                          @RequestParam(required = false) Integer mchType,
                                                                          @RequestParam Integer currentPage,
                                                                          @RequestParam Integer pageSize){
        if (StringUtils.isBlank(mchLike) || mchType == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请输入查询信息");
        }

        List<Map<String, Object>> result = new ArrayList<>();

        Map<String, Object> param = new HashMap<>();
        if (mchType.intValue() == MerchantTypeEnum.EMPLOYER.getValue()) {
            param.put("employerLike", mchLike);
            param.put("status", 100);
        } else if (mchType.intValue() == MerchantTypeEnum.MAINSTAY.getValue()) {
            param.put("mainstayLike", mchLike);
            param.put("status", 100);
        }
        final PageResult<List<Map<String, String>>> pageResult = employerAccountInfoBiz.groupByMch(param, PageParam.newInstance(currentPage,pageSize));
        final List<Map<String, String>> list = pageResult.getData();
        for (Map<String, String> map : list) {
            final String employerNo = map.get("employerNo");
            final String employerName = map.get("employerName");

            Map<String, Object> resultItem = new HashMap<>();
            Map<String,Object> accountMap = employerAccountInfoBiz.getAmountByEmployerNoAnyMainstayNo(null,employerNo);
            final List<Map<String,Object>> amountList = (List<Map<String, Object>>) accountMap.get("amountList");
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (Map<String, Object> item : amountList) {
                String bankAmount = (String) item.get("bankAmount");
                String aliPayAmount = (String) item.get("aliPayAmount");
                String weixinAmount = (String) item.get("weixinAmount");
                bankAmount = StringUtil.isDecimal(bankAmount) ? bankAmount : "0";
                aliPayAmount = StringUtil.isDecimal(aliPayAmount) ? aliPayAmount : "0";
                weixinAmount = StringUtil.isDecimal(weixinAmount) ? weixinAmount : "0";
//                log.info("bankAmount={},alipayAmount={},weixinAmount={}",bankAmount,aliPayAmount,weixinAmount);
                BigDecimal mainstayAmount = new BigDecimal(bankAmount).add(new BigDecimal(aliPayAmount)).add(new BigDecimal(weixinAmount));
                totalAmount = totalAmount.add(mainstayAmount);
            }
            resultItem.put("totalAmount", totalAmount);
            resultItem.put("employerName", employerName);
            resultItem.put("employerNo", employerNo);
            result.add(resultItem);
        }

        PageResult<List<Map<String, Object>>> pageRespone = new PageResult<>();
        pageRespone.setData(result);
        pageRespone.setPageCurrent(pageResult.getPageCurrent());
        pageRespone.setPageSize(pageResult.getPageSize());
        pageRespone.setTotalRecord(pageResult.getTotalRecord());
        return RestResult.success(pageRespone);
    }

    @GetMapping("/getCardNo")
    public RestResult getCardNo(){
        employerAccountInfoBiz.getCardNo();
        return RestResult.success("success");
    }

    @Logger(type = OperateLogTypeEnum.QUERY, name="导出用工企业账户余额")
    @Permission("report:employerAccountInfo:view")
    @PostMapping("exportAmountWithMerchantInfo")
    public RestResult<String> exportAmountWithMerchantInfo(
            @RequestParam(required = false) String mchLike,
            @RequestParam(required = false) Integer mchType,
            @CurrentUser PmsOperator pmsOperator,
            HttpServletResponse servletResponse) {

        if (StringUtils.isBlank(mchLike) || mchType == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请输入查询信息");
        }

        List<MerchantAccountExportExcelVo> result = new ArrayList<>();

        Map<String, Object> param = new HashMap<>();
        if (mchType.intValue() == MerchantTypeEnum.EMPLOYER.getValue()) {
            param.put("employerLike", mchLike);
            param.put("status", 100);
        } else if (mchType.intValue() == MerchantTypeEnum.MAINSTAY.getValue()) {
            param.put("mainstayLike", mchLike);
            param.put("status", 100);
        }

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.EMPLOYER_BALANCE_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.EMPLOYER_BALANCE_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(param));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.EMPLOYER_BALANCE_EXPORT.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("listAcctDetail")
    public RestResult<Page<CkAccountDetail>> listAcctDetail(@Validated @RequestBody AccountDetailQueryDto dto, @RequestBody Page page) {

        final Page<CkAccountDetail> accountDetails = ckAccountDetailFacade.listBy(BeanUtil.toMap(dto), page);
        return RestResult.success(accountDetails);
    }

    @Permission("report:employerAccountInfo:view")
    @PostMapping("exportCkAcctDetail")
    public RestResult<String> exportCkAcctDetail(@Validated @RequestBody AccountDetailQueryDto dto,@CurrentUser PmsOperator pmsOperator) {
        List<MerchantAccountExportExcelVo> result = new ArrayList<>();

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(pmsOperator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.CK_ACCTDETAIL_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.CK_ACCTDETAIL_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(dto));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.CK_ACCTDETAIL_EXPORT.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
