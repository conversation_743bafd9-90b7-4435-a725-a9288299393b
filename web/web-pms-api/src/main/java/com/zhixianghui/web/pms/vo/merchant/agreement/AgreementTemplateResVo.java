package com.zhixianghui.web.pms.vo.merchant.agreement;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-07 11:21
 **/
@Data
public class AgreementTemplateResVo {
    private Long id;

    private Date createTime;

    private Date updateTime;

    /**
     * 任务主题
     */
    private String topic;

    private String createOperator;

    private String updateOperator;

    /**
     * 过期时间选项
     */
    private Integer expireTimeType;

    /**
     * 截止时间选项
     */
    private Integer deadlineType;

    /**
     * 签署人名称(全)
     * 用于分页显示
     */
    private String signerName;

    /**
     * 协议文件列表
     */
    private List<AgreementFileVo> fileVoList;

    /**
     * 签署人列表
     */
    private List<AgreementSignerVo> signerVoList;

    /**
     * 额外信息
     */
    private String extInfo;

    /**
     * 流程模板id
     */
    private String flowTemplateId;

    /**
     * 文件模板id
     */
    private String fileTemplateId;

    /**
     * 文件模板地址
     */
    private String templateFileUrl;
}
