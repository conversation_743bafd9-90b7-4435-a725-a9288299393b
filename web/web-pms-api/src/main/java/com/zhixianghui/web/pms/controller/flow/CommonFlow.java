package com.zhixianghui.web.pms.controller.flow;

import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsRole;
import com.zhixianghui.facade.merchant.service.pms.PmsPermissionFacade;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/5 17:29
 */
public class CommonFlow {

    private static final String CRM_FLAG = "CRM观察员";
    protected static final String SALE_FLAG = "销售";

    public boolean crmRole(long operateId, PmsPermissionFacade pmsPermissionFacade) {
        List<PmsRole> roleList = pmsPermissionFacade.listRolesByOperatorId(operateId);
        if (CollectionUtils.isEmpty(roleList)) {
            return false;
        }
        for (PmsRole pmsRole : roleList) {
            if (CRM_FLAG.equals(pmsRole.getRoleName())) {
                return true;
            }
        }
        return false;
    }


    public boolean equalRole(long operateId, PmsPermissionFacade pmsPermissionFacade, String flag) {
        List<PmsRole> roleList = pmsPermissionFacade.listRolesByOperatorId(operateId);
        if (CollectionUtils.isEmpty(roleList)) {
            return false;
        }
        for (PmsRole pmsRole : roleList) {
            if (flag.equals(pmsRole.getRoleName())) {
                return true;
            }
        }
        return false;
    }

    public boolean admin(PmsOperator operator, PmsPermissionFacade permissionFacade) {
        if (PmsOperatorTypeEnum.ADMIN.getValue() == operator.getType()) {
            return true;
        }
        return crmRole(operator.getId(), permissionFacade);
    }
}
