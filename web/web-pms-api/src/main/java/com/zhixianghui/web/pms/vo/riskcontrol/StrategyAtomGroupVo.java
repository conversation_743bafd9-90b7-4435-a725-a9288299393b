package com.zhixianghui.web.pms.vo.riskcontrol;

import com.zhixianghui.common.statics.enums.riskcontrol.ControlAtomEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomOperatorEnum;
import com.zhixianghui.common.statics.enums.riskcontrol.StrategyAtomVariableEnum;
import com.zhixianghui.facade.riskcontrol.entity.RiskControlRule;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

@Data
public class StrategyAtomGroupVo {

    private Long id;

    /**
     * 风控规则id
     * {@link RiskControlRule#getId()}
     */
    @NotNull(message = "风控规则id不能为空")
    private Long ruleId;

    /**
     * 权重
     */
    @NotNull(message = "权重不能为空")
    @Positive(message = "权重必须为正整数")
    private Integer weight;

    /**
     * 管控原子
     * {@link ControlAtomEnum#getValue()}
     */
    @NotNull(message = "管控原子不能为空")
    private Integer controlAtom;

    /**
     * 描述
     */
    @NotEmpty(message = "描述不能为空")
    private String remark;

    /**
     * 策略原子列表，之间为 AND 关系
     */
    @Valid
    private List<StrategyAtomVo> strategyAtomList;

    @Data
    public static class StrategyAtomVo {
        /**
         * 条件变量
         * {@link StrategyAtomVariableEnum#getValue()}
         */
        @NotNull(message = "策略原子条件变量不能为空")
        private Integer variable;

        /**
         * 运算符
         * {@link StrategyAtomOperatorEnum#getValue()}
         */
        @NotNull(message = "策略原子运算符不能为空")
        private Integer operator;

        /**
         * 常量值
         */
        @NotNull(message = "策略原子常量值不能为空")
        //@Digits(integer = 10, fraction = 2, message = "策略原子常量值格式错误")
        private String constant;
    }
}
