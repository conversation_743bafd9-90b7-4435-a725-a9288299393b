package com.zhixianghui.web.pms.vo.fee;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class MerchantProductRelationVo {
    private Long id;

    /**
     * 商户编号
     */
    @NotEmpty(message = "商户编号不能为空")
    private String mchNo;

    /**
     * 商户名称
     */
    @NotEmpty(message = "商户名称不能为空")
    private String mchName;

    /**
     * 产品编号
     */
    @NotEmpty(message = "产品编号不能为空")
    private String productNo;

    /**
     * 产品名称
     */
    @NotEmpty(message = "产品名称不能为空")
    private String productName;

    /**
     * 描述
     */
    private String description;
}