package com.zhixianghui.web.pms.vo.merchant.agreement;

import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.facade.merchant.entity.AgreementSigner;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-03 16:03
 **/
@Data
public class AgreementResVo implements Serializable {
    /**
     * id
     */
    protected Long id;

    /**
     * 创建时间
     */
    protected Date createTime = new Date();

    /**
     * 更新时间
     */
    private Date updateTime = new Date();

    /**
     * 任务主题
     */
    private String topic;

    /**
     * 创建者
     */
    private String createOperator;

    /**
     * 更新者
     */
    private String updateOperator;

    /**
     * 销售ID
     */
    private Long salerId;

    /**
     * 销售人名
     */
    private String salerName;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 协议过期时间
     */
    private Date expireTime;

    /**
     * 签约截止时间
     */
    private Date deadline;

    /**
     * 签署人名称(全)
     */
    private String signerName;

    /**
     * 协议状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String description;

    /**
     * 预留json
     */
    private String extInfo;

    /**
     * 协议文件列表
     */
    private List<AgreementFile> fileList;

    /**
     * 签署人列表
     */
    private List<AgreementSigner> signerList;
}
