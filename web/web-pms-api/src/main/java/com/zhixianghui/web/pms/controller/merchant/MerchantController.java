package com.zhixianghui.web.pms.controller.merchant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.entity.SalesLead;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.MerchantCreateAnonFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.SalesLeadFacade;
import com.zhixianghui.facade.merchant.vo.CreateLinkReqVo;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.vo.merchant.SalesLeadQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年02月10日 17:30:00
 */
@RestController
@RequestMapping("/merchant")
@Slf4j
public class MerchantController {

    @Reference
    private MerchantFacade merchantFacade;

    @Reference
    private MerchantCreateAnonFacade merchantCreateAnonFacade;

    @Reference
    private SalesLeadFacade salesLeadFacade;

    @Permission("merchant:forceDelete:delete")
    @PostMapping("/forceDelete/{mchNo}")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "删除商户")
    public RestResult forceDelete(@PathVariable String mchNo, @CurrentUser PmsOperator operatorp) {
        if (operatorp.getType() != PmsOperatorTypeEnum.ADMIN.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂无权限");
        }
        merchantFacade.forceDelete(mchNo);
        return RestResult.success("删除成功");
    }

    @Permission("merchant:employer:activate")
    @PostMapping("/forceActive/{mchNo}")
    @Logger(type = OperateLogTypeEnum.MODIFY,name = "手动激活商户")
    public RestResult forceActive(@PathVariable String mchNo,@CurrentUser PmsOperator pmsOperator){
        if (pmsOperator.getType() != PmsOperatorTypeEnum.ADMIN.getValue()){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无权限");
        }
        merchantFacade.forceActive(mchNo,pmsOperator.getLoginName());
        return RestResult.success("激活成功");
    }

    @PostMapping("/generateCreateURL")
    public RestResult generateCreateURL(@Valid @RequestBody CreateLinkReqVo reqVo, @CurrentUser PmsOperator pmsOperator){
        String param = merchantCreateAnonFacade.getURL(reqVo);
        return RestResult.success(param);
    }

    @RequestMapping("ListRecommendCustoms")
    public RestResult<IPage<SalesLead>> ListRecommendCustoms(@RequestBody SalesLeadQueryVo queryVo){

        Page page = new Page(queryVo.getPageCurrent(), queryVo.getPageSize());
        SalesLead salesLead = new SalesLead();
        BeanUtil.copyProperties(queryVo, salesLead);
        IPage listByPage = salesLeadFacade.listByPage(page, salesLead);

        return RestResult.success(listByPage);
    }

    @PostMapping("updateRecommendCustomsStatus")
    public RestResult<String> updateRecommendCustomsStatus(@RequestParam Long id,@RequestParam Integer status){

        SalesLead salesLead = salesLeadFacade.getById(id);
        salesLead.setStatus(status.shortValue());
        salesLeadFacade.update(salesLead);
        return RestResult.success("操作成功");
    }
}
