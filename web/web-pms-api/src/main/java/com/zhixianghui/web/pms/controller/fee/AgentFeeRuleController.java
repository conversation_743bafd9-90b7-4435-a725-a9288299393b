package com.zhixianghui.web.pms.controller.fee;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.fee.SpecialRuleDto;
import com.zhixianghui.common.statics.enums.agent.CalculateModeEnum;
import com.zhixianghui.common.statics.enums.fee.FormulaEnum;
import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.enums.fee.RuleTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.AmountUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.fee.entity.AgentFeeRule;
import com.zhixianghui.facade.fee.service.AgentFeeRuleFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.fee.AgentFeeRuleQueryVo;
import com.zhixianghui.web.pms.vo.fee.AgentFeeRuleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

/**
 * 合伙人计费规则
 */
@RestController
@RequestMapping("agentFeeRule")
@Slf4j
public class AgentFeeRuleController extends BaseController {

    @Reference
    private AgentFeeRuleFacade agentFeeRuleFacade;

    /**
     * 查询
     */
    @Permission("fee:agentFeeManager:list")
    @PostMapping("list")
    public RestResult<PageResult<List<AgentFeeRule>>> listPage(@RequestBody @Valid AgentFeeRuleQueryVo vo) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        PageResult<List<AgentFeeRule>> pageResult = agentFeeRuleFacade.listPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize(),"UPDATE_TIME DESC"));
        pageResult.getData().forEach(x -> {
            if(x.getFirstFeeRate() != null){
                x.setFirstFeeRate(AmountUtil.mul(x.getFirstFeeRate(), new BigDecimal(100)));
            }
            if(x.getSecondFeeRate() != null){
                x.setSecondFeeRate(AmountUtil.mul(x.getSecondFeeRate(), new BigDecimal(100)));
            }
        });
        return RestResult.success(pageResult);
    }

    /**
     * 删除
     */
    @Permission("fee:agentFeeManager:delete")
    @PostMapping("delete/{id}")
    @Logger(type = OperateLogTypeEnum.DELETE, name = "删除产品计费")
    public RestResult<String> delete(@CurrentUser PmsOperator currentOperator, @PathVariable("id") long id) {
        agentFeeRuleFacade.deleteById(id, currentOperator.getLoginName());
        return RestResult.success("删除操作成功");
    }

    /**
     * 编辑
     */
    @Permission("fee:agentFeeManager:edit")
    @PostMapping("edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "修改产品计费")
    public RestResult<String> edit(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid AgentFeeRuleVo vo) {
        LimitUtil.notEmpty(vo.getId(), "id不能为空");

        checkFeeRule(vo);

        AgentFeeRule rule = agentFeeRuleFacade.getById(vo.getId());
        LimitUtil.notEmpty(rule, "产品计费不存在");
        rule.setUpdateBy(currentOperator.getLoginName());

        fillFeeRule(rule, vo);

        agentFeeRuleFacade.update(rule);

        return RestResult.success("修改数据成功");
    }

    /**
     * 添加
     */
    @Permission("fee:agentFeeManager:add")
    @PostMapping("add")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "添加产品计费")
    public RestResult<String> add(@CurrentUser PmsOperator currentOperator, @RequestBody @Valid AgentFeeRuleVo vo) {
        // 规则校验
        checkFeeRule(vo);

        AgentFeeRule rule = new AgentFeeRule();
        rule.setId(vo.getId());
        rule.setVersion(0);
        rule.setStatus(PublicStatus.ACTIVE);
        rule.setUpdateBy(currentOperator.getLoginName());
        rule.setCreateBy(currentOperator.getLoginName());
        rule.setCreateTime(new Date());
        rule.setRemoved(RemovedEnum.NORMAL.getValue());

        fillFeeRule(rule, vo);

        agentFeeRuleFacade.save(rule);

        return RestResult.success("添加产品计费成功");
    }

    /**
     * 查看
     */
    @Permission("fee:agentFeeManager:view")
    @GetMapping("view/{id}")
    public RestResult<AgentFeeRule> view(@PathVariable long id) {
        AgentFeeRule agentFeeRule = agentFeeRuleFacade.getById(id);
        if(agentFeeRule.getFirstFeeRate() != null){
            agentFeeRule.setFirstFeeRate(AmountUtil.mul(agentFeeRule.getFirstFeeRate(), new BigDecimal(100)));
        }
        if(agentFeeRule.getSecondFeeRate() != null){
            agentFeeRule.setSecondFeeRate(AmountUtil.mul(agentFeeRule.getSecondFeeRate(), new BigDecimal(100)));
        }
        return RestResult.success(agentFeeRule);
    }

    private void checkFeeRule(AgentFeeRuleVo vo){
        if(Objects.equals(vo.getRuleType(), RuleTypeEnum.GENERAL.getValue())
                && vo.getPriority() != 1){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("通用规则优先级只能为1");
        }

        // 一级分佣按比例收费
        if(vo.getFirstFormulaType() == FormulaEnum.RATE.getValue()){
            LimitUtil.notEmpty(vo.getFirstFeeRate(), "一级分佣手续费费率不能为空");
        }
        // 一级分佣按笔收费
        else if(vo.getFirstFormulaType() == FormulaEnum.FIXED.getValue()){
            LimitUtil.notEmpty(vo.getFirstFixedFee(), "一级分佣固定手续费不能为空");

        }
        // 一级分佣按笔+比例收费
        else if(vo.getFirstFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()){
            LimitUtil.notEmpty(vo.getFirstFixedFee(), "一级分佣固定手续费不能为空");
            LimitUtil.notEmpty(vo.getFirstFeeRate(), "一级分佣手续费费率不能为空");
        }


        //只有按公式计算才需要校验二级比例
        if (vo.getCalculateMode().intValue() == CalculateModeEnum.FORMULA.getValue()){
            // 二级分佣按比例收费
            if(vo.getSecondFormulaType() == FormulaEnum.RATE.getValue()){
                LimitUtil.notEmpty(vo.getSecondFeeRate(), "二级分佣手续费费率不能为空");
            }
            // 二级分佣按笔收费
            else if(vo.getSecondFormulaType() == FormulaEnum.FIXED.getValue()){
                LimitUtil.notEmpty(vo.getSecondFixedFee(), "二级分佣固定手续费不能为空");

            }
            // 二级分佣按笔+比例收费
            else if(vo.getSecondFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()){
                LimitUtil.notEmpty(vo.getSecondFixedFee(), "二级分佣固定手续费不能为空");
                LimitUtil.notEmpty(vo.getSecondFeeRate(), "二级分佣手续费费率不能为空");
            }
        }

        if(vo.getRuleType() == RuleTypeEnum.SPECIAL.getValue()){
            LimitUtil.notEmpty(vo.getRuleParam(), "特殊规则不能为空");
        }
    }

    private void fillFeeRule(AgentFeeRule rule, AgentFeeRuleVo vo){
        rule.setAgentNo(vo.getAgentNo());
        rule.setAgentName(vo.getAgentName());
        rule.setProductNo(vo.getProductNo());
        rule.setProductName(vo.getProductName());
        rule.setDescription(vo.getDescription());
        rule.setUpdateTime(new Date());
        rule.setRuleType(vo.getRuleType());
        rule.setMaxFee(vo.getMaxFee());
        rule.setMinFee(vo.getMinFee());
        rule.setPriority(vo.getPriority());
        rule.setFirstFormulaType(vo.getFirstFormulaType());
        rule.setSecondFormulaType(vo.getSecondFormulaType());
        rule.setCalculateMode(vo.getCalculateMode());
        // 一级分佣按比例收费
        if(vo.getFirstFormulaType() == FormulaEnum.RATE.getValue()){
            rule.setFirstFixedFee(null);
            rule.setFirstFeeRate(AmountUtil.div(vo.getFirstFeeRate(), new BigDecimal(100)));
        }
        // 一级分佣按笔收费
        else if(vo.getFirstFormulaType() == FormulaEnum.FIXED.getValue()){
            rule.setFirstFixedFee(vo.getFirstFixedFee());
            rule.setFirstFeeRate(null);
        }
        // 一级分佣按笔+比例收费
        else if(vo.getFirstFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()){
            rule.setFirstFixedFee(vo.getFirstFixedFee());
            rule.setFirstFeeRate(AmountUtil.div(vo.getFirstFeeRate(), new BigDecimal(100)));
        }

        if (vo.getCalculateMode().intValue() == CalculateModeEnum.FORMULA.getValue()){
            // 二级分佣按比例收费
            if(vo.getSecondFormulaType() == FormulaEnum.RATE.getValue()){
                rule.setSecondFixedFee(null);
                rule.setSecondFeeRate(AmountUtil.div(vo.getSecondFeeRate(), new BigDecimal(100)));
            }
            // 二级分佣按笔收费
            else if(vo.getSecondFormulaType() == FormulaEnum.FIXED.getValue()){
                rule.setSecondFixedFee(vo.getSecondFixedFee());
                rule.setSecondFeeRate(null);
            }
            // 二级分佣按笔+比例收费
            else if(vo.getSecondFormulaType() == FormulaEnum.RATE_AND_FIXED.getValue()){
                rule.setSecondFixedFee(vo.getSecondFixedFee());
                rule.setSecondFeeRate(AmountUtil.div(vo.getSecondFeeRate(), new BigDecimal(100)));
            }

        }
        // 特殊计费设置特殊匹配规则
        rule.setSpecialFeeRuleList(new ArrayList<>());
        if(vo.getRuleType() == RuleTypeEnum.SPECIAL.getValue() && CollectionUtils.isNotEmpty(vo.getRuleParam())){
            vo.getRuleParam().forEach(x -> {
                SpecialRuleDto specialRuleDto = new SpecialRuleDto();
                specialRuleDto.setSpecialRuleType(x.getSpecialRuleType());
                specialRuleDto.setCompareType(x.getCompareType());
                specialRuleDto.setValue(x.getValue());
                rule.getSpecialFeeRuleList().add(specialRuleDto);
            });
        }
    }

    @PostMapping("updateRealProfitRatio")
    public RestResult<AgentFeeRule> updateRealProfitRatio(@RequestParam Long id,@RequestParam Integer realProfitRatioValue,@CurrentUser PmsOperator pmsOperator) {
        if (realProfitRatioValue == null || realProfitRatioValue.intValue()<=0 || realProfitRatioValue.intValue()>100) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("非法参数：只能为(0-100]的整数");
        }

        final AgentFeeRule feeRule = agentFeeRuleFacade.getById(id);
        feeRule.setRealProfitRatio(realProfitRatioValue);
        feeRule.setUpdateBy(pmsOperator.getLoginName());
        feeRule.setUpdateTime(new Date());

        agentFeeRuleFacade.updateRealProfitRatio(feeRule);

        return RestResult.success(feeRule);
    }
}
