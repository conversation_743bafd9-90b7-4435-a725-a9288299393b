package com.zhixianghui.web.pms.controller.employee;

import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.web.pms.annotation.Logger;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年06月29日 14:57:00
 */
@RestController
@RequestMapping("worker")
public class JobWorkerController {

    @Reference
    private UserInfoFacade userInfoFacade;

    @PostMapping("/getIdCardByIDCardNoMd5")
    @Logger(type = OperateLogTypeEnum.QUERY, name = "查询身份证信息")
    public RestResult getIdCardByIDCardNoMd5(@RequestParam String workerIdCardMd5) {
        return RestResult.success(userInfoFacade.getByIdCardNoMd5(workerIdCardMd5));
    }
}
