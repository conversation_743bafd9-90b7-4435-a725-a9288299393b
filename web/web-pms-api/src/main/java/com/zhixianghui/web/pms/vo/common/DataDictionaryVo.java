package com.zhixianghui.web.pms.vo.common;

import com.zhixianghui.facade.common.entity.config.DataDictionary;
import lombok.Data;

import java.util.Date;

@Data
public class DataDictionaryVo {
    private String creator;
    private String dataName;
    private String dataInfo;
    private String remark;
    private String systemType;

    public DataDictionary toDataDictionary(DataDictionaryVo data) {
        DataDictionary dataDictionary = new DataDictionary();
        dataDictionary.setCreator(data.getCreator());
        dataDictionary.setDataName(data.getDataName());
        dataDictionary.setDataInfo(data.getDataInfo());
        dataDictionary.setRemark(data.getRemark());
        dataDictionary.setSystemType(Integer.valueOf(data.getSystemType()));
        dataDictionary.setCreateTime(new Date());
        dataDictionary.setVersion(0);
        dataDictionary.setModifyTime(new Date());
        return dataDictionary;
    }
}