package com.zhixianghui.web.pms.controller.trade;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.vo.FeeOrderVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月12日 10:41:00
 */
@RestController
@RequestMapping("/feeOrder")
public class FeeOrderController {

    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;


    @PostMapping("/listPage")
    public RestResult listPage(@RequestBody FeeOrderVo feeOrderVo, @RequestBody PageParam pageParam) {
        return RestResult.success(feeOrderBatchFacade.listPage(feeOrderVo, pageParam));
    }

    @PostMapping("/getStatistics")
    public RestResult getStatistics(@RequestBody FeeOrderVo feeOrderVo) {
        return RestResult.success(feeOrderBatchFacade.getStatistics(feeOrderVo));
    }

//    @GetMapping("/selectOrderItem/{feeBatchNo}")
//    public RestResult selectOrderItem(@PathVariable("feeBatchNo")String feeBatchNo){
//        return RestResult.success(feeOrderBatchFacade.selectOrderItem(feeBatchNo));
//    }
//
//    @GetMapping("/getByItemNo/{feeItemNo}")
//    public RestResult getByItemNo(@PathVariable("feeItemNo")String feeItemNo){
//        return RestResult.success(feeOrderBatchFacade.getByItemNo(feeItemNo));
//    }
//
//    @PostMapping("/payFee/{feeItemNo}")
//    public RestResult payFee(@PathVariable("feeItemNo")String feeItemNo){
//        return RestResult.success(feeOrderBatchFacade.payFee(feeItemNo));
//    }
//
//    @GetMapping("/getOffLineItem/{feeBatchNo}")
//    public RestResult getOffLineItem(@PathVariable("feeBatchNo") String feeBatchNo){
//        return RestResult.success(feeOrderBatchFacade.getOffLineItem(feeBatchNo));
//    }
//
//    @PostMapping("/complete")
//    public RestResult complete(@RequestBody FeeOrderBatchVo feeOrderBatchVo){
//        feeOrderBatchFacade.complete(feeOrderBatchVo);
//        return RestResult.success("提交成功");
//    }
}