package com.zhixianghui.web.pms.controller.account;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.account.AccountRequestDto;
import com.zhixianghui.common.statics.dto.account.invoice.AccountInvoiceAmountTypeEnum;
import com.zhixianghui.common.statics.dto.account.invoice.AccountInvoiceProcessDto;
import com.zhixianghui.common.statics.enums.account.AccountResultAuditTypeEnum;
import com.zhixianghui.common.statics.enums.account.AccountSysTypeEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.account.invoice.entity.AccountInvoice;
import com.zhixianghui.facade.account.invoice.entity.AccountInvoiceProcessDetail;
import com.zhixianghui.facade.account.invoice.entity.AccountInvoiceProcessPending;
import com.zhixianghui.facade.account.invoice.entity.AccountInvoiceProcessResult;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceManageFacade;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceProcessFacade;
import com.zhixianghui.facade.account.invoice.service.AccountInvoiceProcessManageFacade;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import com.zhixianghui.web.pms.controller.BaseController;
import com.zhixianghui.web.pms.vo.account.*;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Author: Cmf
 * Date: 2020.2.11
 * Time: 15:49
 * Description: 在途账户运营后台接口
 */
@RestController
@RequestMapping("accountInvoice")
public class AccountInvoiceController extends BaseController {
    @Reference
    private AccountInvoiceManageFacade accountInvoiceManageFacade;
    @Reference
    private AccountInvoiceProcessManageFacade accountInvoiceProcessManageFacade;
    @Reference
    private AccountInvoiceProcessFacade accountInvoiceProcessFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;




    @Permission("account:invoice:view")
    @RequestMapping("listAccountInvoice")
    public RestResult<PageResult<List<AccountInvoice>>> listAccountInvoice(@RequestBody AccountInvoiceQueryVO accountInvoiceQueryVO,
                                                                           @RequestParam int pageCurrent,
                                                                           @RequestParam int pageSize) {
        PageResult<List<AccountInvoice>> pageResult = accountInvoiceManageFacade.listAccountPage(BeanUtil.toMap(accountInvoiceQueryVO), PageParam.newInstance(pageCurrent, pageSize));
        return RestResult.success(pageResult);
    }

    @Permission("account:invoice:view")
    @RequestMapping("exportAccountInvoice")
    public RestResult<String> exportAccountInvoice(@RequestBody AccountInvoiceQueryVO accountInvoiceQueryVO,@CurrentUser PmsOperator operator){
        Map<String, Object> paramMap = BeanUtil.toMap(accountInvoiceQueryVO);
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.INVOICE_ACCOUNT_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.INVOICE_ACCOUNT_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.INVOICE_ACCOUNT_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @Permission("account:invoice:view")
    @RequestMapping("statisticsAccountInvoice")
    public RestResult<Map<String,Object>> statisticsAccountInvoice(@RequestBody AccountInvoiceQueryVO accountInvoiceQueryVO) {
        Map<String, Object> paramMap = BeanUtil.toMap(accountInvoiceQueryVO);
        paramMap.put("STATUS", 1);
        return RestResult.success(accountInvoiceManageFacade.statisticsAccountInvoice(paramMap));
    }

    @RequestMapping("listProcessPending")
    public RestResult<PageResult<List<AccountInvoiceProcessPending>>> listProcessPending(@RequestBody AccountInvoiceProcessPendingQueryVO accountInvoiceProcessPendingQueryVO,
                                                                                         @RequestParam int pageCurrent,
                                                                                         @RequestParam int pageSize,
                                                                                         @RequestParam boolean inHistory) {
        PageResult<List<AccountInvoiceProcessPending>> pageResult = accountInvoiceProcessManageFacade.listProcessPendingPage(BeanUtil.toMap(accountInvoiceProcessPendingQueryVO), PageParam.newInstance(pageCurrent, pageSize), inHistory);
        return RestResult.success(pageResult);
    }


    /**
     * 查看待账务处理详情
     *
     * @return
     */
    @RequestMapping("viewProcessPending")
    public RestResult<AccountInvoiceProcessPending> viewProcessPending(@RequestParam long id, @RequestParam boolean inHistory) {
        AccountInvoiceProcessPending processPending = accountInvoiceProcessManageFacade.getProcessPendingById(id, inHistory);
        if (processPending == null) {
            return RestResult.error("记录不存在");
        } else {
            return RestResult.success(processPending);
        }
    }


    /**
     * 分页查询账务处理明细
     *
     * @return .
     */
    @RequestMapping("listProcessDetail")
    public RestResult<PageResult<List<AccountInvoiceProcessDetail>>> listProcessDetail(@RequestBody AccountInvoiceProcessDetailQueryVO accountInvoiceProcessDetailQueryVO,
                                                                                       @RequestParam int pageCurrent,
                                                                                       @RequestParam int pageSize,
                                                                                       @RequestParam boolean inHistory) {
        if (inHistory && accountInvoiceProcessDetailQueryVO.getCreateTimeBegin() == null) {
            return RestResult.error("查询历史表必须输入日期范围");
        }
        PageResult<List<AccountInvoiceProcessDetail>> pageResult = accountInvoiceProcessManageFacade.listProcessDetailPage(BeanUtil.toMap(accountInvoiceProcessDetailQueryVO), PageParam.newInstance(pageCurrent, pageSize), inHistory);
        return RestResult.success(pageResult);
    }


    /**
     * 分页查询账务处理结果表记录
     *
     * @return .
     */
    @RequestMapping("listProcessResult")
    public RestResult<PageResult<List<AccountInvoiceProcessResult>>> listProcessResult(@RequestBody AccountInvoiceProcessResultQueryVO accountInvoiceProcessResultQueryVO,
                                                                                       @RequestParam int pageCurrent,
                                                                                       @RequestParam int pageSize,
                                                                                       @RequestParam boolean inHistory) {
        PageResult<List<AccountInvoiceProcessResult>> pageResult = accountInvoiceProcessManageFacade.listProcessResultPage(BeanUtil.toMap(accountInvoiceProcessResultQueryVO), PageParam.newInstance(pageCurrent, pageSize), inHistory);
        return RestResult.success(pageResult);
    }


    /**
     * 查看账务处理结果处理详情
     *
     * @return .
     */
    @RequestMapping("viewProcessResult")
    public RestResult<AccountInvoiceProcessResult> viewProcessResult(@RequestParam long id, @RequestParam boolean inHistory) {
        AccountInvoiceProcessResult processResult = accountInvoiceProcessManageFacade.getProcessResultById(id, inHistory);
        if (processResult == null) {
            return RestResult.error("记录不存在");
        } else {
            return RestResult.success(processResult);
        }
    }


    @RequestMapping("resendProcessResultCallback")
    public RestResult<String> resendProcessResultCallback(@RequestParam long id, @RequestParam boolean inHistory) {
        if (accountInvoiceProcessManageFacade.sendProcessResultCallbackMsg(id, true, inHistory)) {
            return RestResult.success("发送成功");
        } else {
            return RestResult.error("发送失败");
        }
    }


    /**
     * 账务处理结果审核
     *
     * @return .
     */
    @GetMapping("auditProcessResult")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "账务处理结果审核")
    public RestResult<String> auditProcessResult(@RequestParam List<Long> ids, @RequestParam Integer auditType) {
        if (ids == null || ids.size() == 0) {
            return RestResult.error("账务处理结果ID不能为空");
        }
        int successCount = 0, failCount = 0;
        Map<Boolean, List<Boolean>> successFailGroup = ids.stream().map(id -> accountInvoiceProcessManageFacade.auditProcessResult(id, auditType))
                .collect(Collectors.groupingBy(Boolean::new));
        if (successFailGroup.get(Boolean.TRUE) != null) {
            successCount = successFailGroup.get(Boolean.TRUE).size();
        }
        if (successFailGroup.get(Boolean.FALSE) != null) {
            failCount = successFailGroup.get(Boolean.FALSE).size();
        }
        return RestResult.success("审核为" + AccountResultAuditTypeEnum.getEnum(auditType) + "完成，成功:" + successCount + "笔，失败:" + failCount + "笔");
    }

    /**
     * 发票账户调账
     * @param adjustVO
     * @return
     */
    @RequestMapping("adjustAccount")
    @Permission("accountInvoice:adjustAccount")
    @Logger(type = OperateLogTypeEnum.CREATE, name = "发票账户调账")
    public RestResult<String> adjustAccount(@Valid @RequestBody AccountInvoiceAdjustVO adjustVO){
        AccountInvoice accountInvoice = accountInvoiceManageFacade.getAccountByAccountNo(adjustVO.getAccountNo());
        LimitUtil.notEmpty(accountInvoice, "发票账户不存在");

        String processNo = sequenceFacade.nextRedisIdWithDate(SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_ACCOUNT_TRX_NO_SEQ.getWidth());

        AccountRequestDto requestDto = new AccountRequestDto();
        requestDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        requestDto.setAccountProcessNo(processNo);
        requestDto.setMustSuccess(true);

        AccountInvoiceProcessDto processDto = new AccountInvoiceProcessDto();
        processDto.setAccountSysType(AccountSysTypeEnum.INVOICE_ACCOUNT.getValue());
        processDto.setTrxTime(new Date());
        processDto.setTrxNo(processNo);
        processDto.setMchTrxNo(processNo);
        processDto.setProcessType(adjustVO.getAccountProcessType());
        processDto.setAmountType(AccountInvoiceAmountTypeEnum.INVOICE_AMOUNT.getValue());
        processDto.setEmployerMchNo(accountInvoice.getEmployerMchNo());
        processDto.setMainstayMchNo(accountInvoice.getMainstayMchNo());
        processDto.setAmount(adjustVO.getAmount());
        processDto.setProductNo(ProductNoEnum.ACCOUNT_ADJUST.name());
        processDto.setDesc(adjustVO.getRemark());

        accountInvoiceProcessFacade.executeSync(requestDto, processDto);

        return RestResult.success("调账成功");
    }

}
