package com.zhixianghui.web.pms.vo.fee;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.common.util.validator.EnumValue;
import lombok.Data;



/**
 * <AUTHOR>
 * @Date 2021/3/24 9:50
 */
@Data
public class AgentMonthBillQueryVo extends PageQueryVo {

    private Long id;
    /**
     * 合伙人编号
     */
    private String agentNo;
    /**
     * 合伙人姓名
     */
    private String agentName;
    /**
     * 合伙人类型
     */
    @EnumValue(intValues = {100, 101}, message = "合伙人类型不合法")
    private Integer agentType;
    /**
     * 销售id
     */
    private String salerId;
    /**
     * 账单编号
     */
    private String billNo;
    /**
     * 账单状态, 未结算状态:100, 待人工结算101, 已人工结算102, 不结算103, 结算失败104, 已自动结算105'
     */
    @EnumValue(intValues = {100, 101, 102, 103, 104, 105}, message = "状态不合法")
    private Integer settlementStatus;
    /**
     * 账单月份
     */
    private String billDate;
    /**
     * 排序列
     */
    @EnumValue(strValues = {"totalProfit", "settlementAmount"}, message = "不支持该排序列")
    private String sortColumns;

}
