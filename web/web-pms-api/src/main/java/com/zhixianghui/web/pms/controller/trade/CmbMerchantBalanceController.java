package com.zhixianghui.web.pms.controller.trade;


import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.dto.AdjustAccountParamDTO;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.service.CmbMerchantBalanceFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/8/22 11:09
 */
@Slf4j
@RestController
@RequestMapping("/cmbMerchantBalance")
public class CmbMerchantBalanceController {

    @Reference
    private CmbMerchantBalanceFacade cmbMerchantBalanceFacade;

    /**
     * 商户人工招行调账
     *
     * @param param
     * @param operator
     * @return
     */
    @Permission("cmb:balance:adjustment")
    @PostMapping("/adjustment")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "商户人工招行调账")
    public RestResult<String> adjustment(@Validated @RequestBody AdjustAccountParamDTO param, @CurrentUser PmsOperator operator) {
        param.setOperator(operator.getLoginName());
        param.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        cmbMerchantBalanceFacade.adjustAccount(param);
        return RestResult.success("调账申请成功，请耐心等待几分钟后刷新页面查看结果，请勿重复申请调账");
    }

    /**
     * 供应商人工招行调账
     *
     * @param param
     * @param operator
     * @return
     */
    @Permission("cmb:mainstay:balance:adjustment")
    @PostMapping("/mainstayAdjustment")
    @Logger(type = OperateLogTypeEnum.MODIFY, name = "供应商人工招行调账")
    public RestResult<String> mainstayAdjustment(@Validated @RequestBody AdjustAccountParamDTO param, @CurrentUser PmsOperator operator) {
        param.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        param.setOperator(operator.getLoginName());
        cmbMerchantBalanceFacade.adjustAccount(param);
        return RestResult.success("调账申请成功，请耐心等待几分钟后刷新页面查看结果，请勿重复申请调账");
    }

}
