package com.zhixianghui.web.pms.vo.merchant.agreement;

import com.zhixianghui.facade.trade.vo.sign.StructComponent;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-03 16:03
 **/
@Data
public class AgreementReqVo implements Serializable {

    @NotNull(groups = IAgreementArchive.class, message = "id不能为空")
    private Long id;

    /**
     * 任务主题
     */
    @NotNull(message = "任务主题不能为空")
    private String topic;

    /**
     * 销售ID
     */
    @NotNull(groups = IAgreement.class, message = "销售id不能为空")
    private Long salerId;

    /**
     * 销售人名
     */
    @NotNull(groups = IAgreement.class, message = "销售名不能为空")
    private String salerName;

    /**
     * 协议过期时间
     */
    @NotNull(groups = IAgreement.class, message = "协议过期时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /**
     * 签约截止时间
     */
    @NotNull(groups = IAgreement.class, message = "签约截止时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadline;

    /**
     * 过期时间选项
     */
    @NotNull(groups = IAgreementTemplate.class, message = "签约截止时间不能为空")
    private Integer expireTimeType;

    /**
     * 截止时间选项
     */
    @NotNull(groups = IAgreementTemplate.class, message = "签约截止时间不能为空")
    private Integer deadlineType;

    /**
     * 协议文件列表
     */
    @Valid
    private List<AgreementFileVo> fileVoList;

    @Valid
    @NotNull(groups = IAgreement.class,message = "签署人为空")
    private List<AgreementSignerVo> signerVoList;

    /**
     * 签署类型
     */
    @NotNull(groups = IAgreement.class,message = "签署类型不能为空")
    private Integer signType;

    /**
     * 签署模式
     */
    @NotNull(groups = IAgreement.class,message = "签署模式不能为空")
    private Integer signMode;

    /**
     * 单方签署时必填确定哪方签署
     * {@link com.zhixianghui.common.statics.enums.merchant.AgreementSignerTypeEnum}
     */
    private Integer singleSignerType;

    /**
     * 文件协议模板id
     */
    private String fileTemplateId;

    /**
     * 组件
     */
    private List<StructComponent> components;
}
