package com.zhixianghui.web.pms.controller.trade;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.dto.AdjustmentDTO;
import com.zhixianghui.facade.trade.dto.MainstayAdjustmentDTO;
import com.zhixianghui.facade.trade.entity.WxMerchantBalance;
import com.zhixianghui.facade.trade.service.WxMerchantBalanceFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.annotation.Logger;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.zookeeper.Op;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2021年12月21日 17:16:00
 */
@RestController
@RequestMapping("/wxMerchantBalance")
public class WxMerchantBalanceController {

    @Reference(retries = -1)
    private WxMerchantBalanceFacade wxMerchantBalanceFacade;

    @Reference
    private SequenceFacade sequenceFacade;


    @Permission("wx:balance:adjustment")
    @PostMapping("/adjustment")
    @Logger(type = OperateLogTypeEnum.MODIFY,name = "商户人工微信调账")
    public RestResult adjustment(@Validated @RequestBody AdjustmentDTO adjustmentDTO, @CurrentUser PmsOperator operator) {
        // 生成商户编号
        String logKey = sequenceFacade.nextRedisId(SequenceBizKeyEnum.WX_ADJUSTMENT.getPrefix(),
                SequenceBizKeyEnum.WX_ADJUSTMENT.getKey(),
                SequenceBizKeyEnum.WX_ADJUSTMENT.getWidth());

        adjustmentDTO.setLogKey(logKey);
        adjustmentDTO.setOperator(operator.getRealName());
        wxMerchantBalanceFacade.adjustment(adjustmentDTO);
        return RestResult.success("调账申请成功，请耐心等待几分钟后刷新页面查看结果，请勿重复申请调账");
    }

    /**
     * 供应商人工微信调账
     * @param mainstayAdjustmentDTO
     * @param operator
     * @return
     */
    @Permission("wx:mainstay:balance:adjustment")
    @PostMapping("/mainstayAdjustment")
    @Logger(type = OperateLogTypeEnum.MODIFY,name = "供应商人工微信调账")
    public RestResult mainstayAdjustment(@Validated @RequestBody MainstayAdjustmentDTO mainstayAdjustmentDTO, @CurrentUser PmsOperator operator) {
        AdjustmentDTO adjustmentDTO=new AdjustmentDTO();
        BeanUtil.copyProperties(mainstayAdjustmentDTO, adjustmentDTO);

        // 生成商户编号
        String logKey = sequenceFacade.nextRedisId(SequenceBizKeyEnum.WX_ADJUSTMENT.getPrefix(),
                SequenceBizKeyEnum.WX_ADJUSTMENT.getKey(),
                SequenceBizKeyEnum.WX_ADJUSTMENT.getWidth());

        adjustmentDTO.setLogKey(logKey);
        adjustmentDTO.setOperator(operator.getRealName());
        wxMerchantBalanceFacade.adjustment(adjustmentDTO);
        return RestResult.success("调账申请成功，请耐心等待几分钟后刷新页面查看结果，请勿重复申请调账");
    }

}