package com.zhixianghui.web.pms.controller.invoice;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.trade.entity.InvoicePreRecord;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.biz.InvoiceBiz;
import com.zhixianghui.web.pms.vo.invoice.InvoicePreRecordQueryVo;
import com.zhixianghui.web.pms.vo.invoice.InvoicePreRecordVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 预开发票业务
 */
@Slf4j
@RestController
@RequestMapping("/invoicePre")
@RequiredArgsConstructor
public class InvoicePreController {

    private final InvoiceBiz invoiceBiz;
    
    @Reference
    private ExportRecordFacade exportRecordFacade;
    
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 分页查询预开发票记录
     *
     * @return 分页结果
     */
    @PostMapping("/list")
    @Permission("invoicePre:view")
    public RestResult<PageResult<List<InvoicePreRecord>>> list(@RequestBody InvoicePreRecordQueryVo vo) {
        PageResult<List<InvoicePreRecord>> pageResult = invoiceBiz.listPreInvoice(vo);
        return RestResult.success(pageResult);
    }

    /**
     * 创建预开发票记录
     *
     * @param vo 预开发票记录
     * @return 操作结果
     */
    @PostMapping("/create")
    @Permission("invoicePre:create")
    public RestResult<String> createPreInvoice(@RequestBody InvoicePreRecordVo vo) {
        try {
            invoiceBiz.createPreInvoice(vo);
            return RestResult.success("创建预开发票记录成功");
        } catch (BizException e) {
            log.error("创建预开发票记录失败", e);
            return RestResult.error(e.getErrMsg());
        } catch (Exception e) {
            log.error("创建预开发票记录失败", e);
            return RestResult.error("系统错误，请稍后再试");
        }
    }

    /**
     * 更新预开发票记录
     *
     * @return 操作结果
     */
    @PostMapping("/update")
    @Permission("invoicePre:update")
    public RestResult<String> updatePreInvoice(@RequestBody InvoicePreRecordVo vo) {
        try {
            invoiceBiz.updatePreInvoice(vo);
            return RestResult.success("更新预开发票记录成功");
        } catch (BizException e) {
            log.error("更新预开发票记录失败", e);
            return RestResult.error(e.getErrMsg());
        } catch (Exception e) {
            log.error("更新预开发票记录失败", e);
            return RestResult.error("系统错误，请稍后再试");
        }
    }

    /**
     * 删除预开发票记录
     *
     * @param id 记录ID
     * @return 操作结果
     */
    @PostMapping("/delete/{id}")
    @Permission("invoicePre:delete")
    public RestResult<String> deletePreInvoice(@PathVariable Long id) {
        try {
            invoiceBiz.deletePreInvoice(id);
            return RestResult.success("删除预开发票记录成功");
        } catch (BizException e) {
            log.error("删除预开发票记录失败", e);
            return RestResult.error(e.getErrMsg());
        } catch (Exception e) {
            log.error("删除预开发票记录失败", e);
            return RestResult.error("系统错误，请稍后再试");
        }
    }
    
    /**
     * 导出预开发票记录
     *
     * @param vo 查询参数
     * @return 操作结果
     */
    @PostMapping("/export")
    @Permission("invoicePre:view")
    public RestResult<String> exportPreInvoice(@RequestBody InvoicePreRecordQueryVo vo, @CurrentUser PmsOperator operator) {
        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(operator.getLoginName());
        record.setSystemType(SystemTypeEnum.BOSS_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.INVOICE_PRE_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.INVOICE_PRE_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.INVOICE_PRE_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
