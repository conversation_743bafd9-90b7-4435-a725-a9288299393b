package com.zhixianghui.web.pms.controller.config;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.black.Blacklist;
import com.zhixianghui.facade.common.service.BlacklistFacade;
import com.zhixianghui.facade.common.vo.BlackListVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.PageVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName BlackListController
 * @Description TODO
 * @Date 2023/3/6 11:31
 */
@RestController
@RequestMapping("black")
public class BlackListController {

    @Reference
    private BlacklistFacade blacklistFacade;

    @PostMapping("listPage")
    public RestResult listPage(@RequestBody Blacklist blacklist,@RequestBody PageVo pageVo){
        PageParam pageParam = pageVo.toPageParam();
        PageResult<List<Blacklist>> pageList = blacklistFacade.selectPage(blacklist,pageParam.getPageSize(),pageParam.getPageCurrent());
        return RestResult.success(pageList);
    }

    @PostMapping("add")
    public RestResult add(@Valid @RequestBody BlackListVo blackListVo, @CurrentUser PmsOperator pmsOperator){
        blacklistFacade.add(blackListVo,pmsOperator.getRealName());
        return RestResult.success("黑名单添加成功");
    }

    @PostMapping("delete/{id}")
    public RestResult delete(@PathVariable Long id){
        blacklistFacade.deleteById(id);
        return RestResult.success("黑名单删除成功");
    }

    @PostMapping("update")
    public RestResult update(@RequestBody Blacklist blacklist, @CurrentUser PmsOperator pmsOperator){
        blacklistFacade.update(blacklist);
        return RestResult.success("黑名单更新成功");
    }
}
