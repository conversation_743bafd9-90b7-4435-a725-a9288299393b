package com.zhixianghui.web.pms.vo.permission;

import com.zhixianghui.common.statics.enums.user.pms.PmsOperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.web.pms.vo.PageVo;
import lombok.Data;

import java.util.Date;

/**
 * Author: Cmf
 * Date: 2020.1.20
 * Time: 19:00
 * Description:运营后台操作日志查询VO
 */
@Data
public class PmsOperateLogQueryVO extends PageVo {
    private String operatorLoginNameLike;   //操作人 like

    private Date createTimeBegin;
    private Date createTimeEnd;
    /**
     * {@link PmsOperateLogTypeEnum}
     */
    private Integer operateType;

    /**
     * {@link SystemTypeEnum}
     */
    private Integer operateSource;

}
