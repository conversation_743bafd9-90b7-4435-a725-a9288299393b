package com.zhixianghui.web.pms.controller.system;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.yishui.YishuiFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.file.FileStoreVo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 文件上传
 * <AUTHOR>
 * @date 2020/8/10
 **/
@RestController
@RequestMapping("file")
@Slf4j
public class FileController {

    @Autowired
    public FastdfsClient fastdfsClient;
    @Reference
    public ExportRecordFacade exportRecordFacade;
    @Reference
    public YishuiFacade yishuiFacade;
    @Value("${loginPrivateKey}")
    private String privateKey;

    /**
     * 上传文件
     * @param multiPartfile
     * @return
     * @throws IOException
     */
    @PostMapping("upload")
    public RestResult<String> upload(@RequestParam("file") MultipartFile multiPartfile) throws IOException {
        if(multiPartfile == null){
            return RestResult.error("文件不能为空");
        }

        // 上传
        String fileUrl = fastdfsClient.uploadFile(multiPartfile.getBytes(), multiPartfile.getOriginalFilename());
        return RestResult.success(fileUrl);
    }

    /**
     * 获取源文件名
     * @param path
     * @return
     */
    @GetMapping("getOriginalFileName")
    public RestResult<String> getOriginalFileName(@RequestParam("path") String path) {
        if(path == null){
            return RestResult.error("文件路径不能为空");
        }

        String originalFileName = fastdfsClient.getOriginalFileName(path);
        return RestResult.success(originalFileName);
    }

    @GetMapping("view")
    public void view(@RequestParam("pathEnc") String pathEnc, HttpServletRequest request,
                         HttpServletResponse response) throws IOException {
        String path = checkAndTransferFileStoreVo(pathEnc);

        String originalFileName = fastdfsClient.getOriginalFileName(path);
        if (originalFileName == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件不存在");
        }

        // 清除所有默认响应头
        response.reset();
        
        // 添加跨域相关响应头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "*");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        
        // 其他响应头设置
        response.setStatus(HttpServletResponse.SC_OK);
        response.setHeader("Content-Type", getMimeTypeWithoutCharset(path));
        response.setHeader("Content-Disposition", "inline; filename=" + URLEncoder.encode(originalFileName, StandardCharsets.UTF_8.toString()));
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        
        // 下载并写入文件内容
        try (InputStream inputStream = fastdfsClient.downloadFile(path);
             ServletOutputStream outputStream = response.getOutputStream()) {
            
            // 使用缓冲区直接写入
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    @GetMapping("download")
    public void download(@RequestParam("pathEnc") String pathEnc, HttpServletRequest request,
                        HttpServletResponse response) throws IOException {
        String path = checkAndTransferFileStoreVo(pathEnc);

        String originalFileName = fastdfsClient.getOriginalFileName(path);
        if (originalFileName == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件不存在");
        }

        // 清除所有默认响应头
        response.reset();

        // 添加跨域相关响应头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "*");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        // 其他响应头设置
        response.setStatus(HttpServletResponse.SC_OK);
        response.setHeader("Content-Type", getMimeTypeWithoutCharset(path));
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(originalFileName, StandardCharsets.UTF_8.toString()));
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");

        // 下载并写入文件内容
        try (InputStream inputStream = fastdfsClient.downloadFile(path);
             ServletOutputStream outputStream = response.getOutputStream()) {

            // 使用缓冲区直接写入
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    private String checkAndTransferFileStoreVo(String pathEnc) {
        if (StringUtil.isEmpty(pathEnc)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("文件路径不能为空");
        }
        String fileNameDec = RSAUtil.decryptByPrivateKey(pathEnc, privateKey);
        FileStoreVo fileVo = JsonUtil.toBean(fileNameDec, FileStoreVo.class);
        Long expire = fileVo.getExpire();
        if (expire == null || expire < System.currentTimeMillis()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件地址已过期");
        }
        return fileVo.getName();
    }

    /**
     * 获取MIME类型，不包含字符集参数
     */
    private String getMimeTypeWithoutCharset(String path) {
        String extension = path.toLowerCase();
        if (!extension.contains(".")) {
            return "application/octet-stream";
        }
        extension = extension.substring(extension.lastIndexOf("."));
        
        switch (extension) {
            // Office文档 - 新版本
            case ".xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case ".docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case ".pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            // Office文档 - 旧版本
            case ".xls":
                return "application/vnd.ms-excel";
            case ".doc":
                return "application/msword";
            case ".ppt":
                return "application/vnd.ms-powerpoint";
            // 图片文件
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".bmp":
                return "image/bmp";
            // PDF文件
            case ".pdf":
                return "application/pdf";
            // 压缩文件
            case ".zip":
                return "application/zip";
            case ".rar":
                return "application/x-rar-compressed";
            // 视频文件
            case ".mp4":
                return "video/mp4";
            // XML文件
            case ".xml":
                return "application/xml";
            // 默认二进制流
            default:
                return "application/octet-stream";
        }
    }

    @PostMapping("yishuiUpload")
    public RestResult<Map<String, Object>> yishuiUpload(@RequestParam("file") MultipartFile multiPartfile) throws IOException {
        if(multiPartfile == null){
            return RestResult.error("文件不能为空");
        }

        String fastUrl = fastdfsClient.uploadFile(multiPartfile.getBytes(), multiPartfile.getOriginalFilename());

        String agentToken = yishuiFacade.getAgentToken();
        if (agentToken == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("帐号登录失败");
        }
        String url = yishuiFacade.getUploadUrl();

        OkHttpClient httpClient = new OkHttpClient.Builder().build();
        okhttp3.RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("token", agentToken)
                .addFormDataPart("file", multiPartfile.getOriginalFilename(),
                       okhttp3.RequestBody.create(MediaType.parse("multipart/form-data"),multiPartfile.getBytes()))
                .build();

        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Response response = httpClient.newCall(request).execute();
        if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);
        String string = response.body().string();
        log.info(JSONObject.parseObject(string).toJSONString());
        return RestResult.success(JSONObject.parseObject(string));
    }

    @GetMapping("listExportRecord")
    public RestResult<List<ExportRecord>> listExportRecord(@RequestParam(name="reportType") Integer reportType, @RequestParam(required = false) Integer direction, @CurrentUser PmsOperator operator){
        if(reportType == null){
            return RestResult.error("导出文件类型不能为空");
        }
        List<ExportRecord> list = exportRecordFacade.listBy(SystemTypeEnum.BOSS_MANAGEMENT.getValue(),
                operator.getLoginName(), null, reportType, direction == null ? 0 : direction);
        return RestResult.success(list);
    }

}
