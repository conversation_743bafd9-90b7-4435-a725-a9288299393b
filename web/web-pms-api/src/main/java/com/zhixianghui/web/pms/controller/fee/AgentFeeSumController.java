package com.zhixianghui.web.pms.controller.fee;


import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.fee.QueryTimeTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsDepartmentNumberEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.fee.service.AgentFeeSumFacade;
import com.zhixianghui.facade.fee.vo.AgentFeeSumVo;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsDepartment;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.service.pms.PmsDepartmentFacade;
import com.zhixianghui.facade.merchant.service.pms.PmsOperatorFacade;
import com.zhixianghui.web.pms.annotation.CurrentUser;
import com.zhixianghui.web.pms.vo.fee.AgentFeeSumQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 合伙人分佣统计
 * <AUTHOR>
 * @since 2021-02-25
 */
@RestController
@RequestMapping("agentFeeSum")
@Slf4j
public class AgentFeeSumController {
    @Reference
    private AgentFeeSumFacade agentFeeSumFacade;
    @Reference
    private PmsDepartmentFacade pmsDepartmentFacade;
    @Reference
    private PmsOperatorFacade pmsOperatorFacade;

    public static void main(String[] args) {
        System.out.println(QueryTimeTypeEnum.getTimeByValue(3));
    }
    /**
     * 合伙人分佣统计分页查询
     */
    @Permission("fee:agentFeeOrder:list")
    @PostMapping("listPage")
    public RestResult<PageResult<List<AgentFeeSumVo>>> listPage(@RequestBody @Valid AgentFeeSumQueryVo vo, @CurrentUser PmsOperator operator) {

        Map<String, Object> paramMap = BeanUtil.toMap(vo);
        QueryTimeTypeEnum.TimeInterval timeInterval = QueryTimeTypeEnum.getTimeByValue(vo.getQueryTimeType());
        paramMap.put("tradeDayStar", timeInterval.getStartTime());
        paramMap.put("tradeDayEnd", timeInterval.getEndTime());

        // 销售只查询自己及下属的合伙人信息
        putIfSaler(operator, paramMap);
        // 构造排序字段及排序方式
//        String sortColumn = "";
//        if (StringUtils.isNotBlank(vo.getSortColumns())) {
//            if ("tradeProfit".equals(vo.getSortColumns())) {
//                sortColumn = "TRADE_PROFIT desc";
//            }
//            if ("totalNetAmount".equals(vo.getSortColumns())) {
//                sortColumn = "TOTAL_NET_AMOUNT desc";
//            }
//            if ("totalProfit".equals(vo.getSortColumns())) {
//                sortColumn = "TOTAL_PROFIT desc";
//            }
//        }
        PageResult<List<AgentFeeSumVo>> pageResult = agentFeeSumFacade.listSumPage(paramMap, PageParam.newInstance(vo.getPageCurrent(), vo.getPageSize()));
        return RestResult.success(pageResult);
    }


    /**
     * 销售部员工增加条件判断
     * @param operator
     * @param paramMap
     */
    private void putIfSaler(PmsOperator operator, Map<String, Object> paramMap) {
        // 销售部只能查自己及下属的商户
        if (operator.getDepartmentId() != null) {
            PmsDepartment department = pmsDepartmentFacade.getDepartmentByNumber(PmsDepartmentNumberEnum.SALE.getNumber());
            if (!Objects.isNull(department) && pmsDepartmentFacade.isSubOrSameDepartment(department.getId(), operator.getDepartmentId())) {
                // 获取下属销售
                List<Long> salerIds = getSubSalerIds(operator);
                paramMap.put("salerIds", salerIds);
            }
        }
    }

    private List<Long> getSubSalerIds(PmsOperator operator) {
        List<Long> salerIds = new ArrayList<>();
        try{
            List<PmsOperator> salers = pmsOperatorFacade.listByLeaderIdRecursive(operator.getId());
            salerIds = salers.stream().map(PmsOperator::getId).collect(Collectors.toList());
        } catch (Exception e){
            log.info("[{}]查询销售下属失败,异常信息", operator.getRealName(), e);
            salerIds.add(operator.getId());
        }
        return salerIds;
    }
}
