package com.zhixianghui.web.pms.controller.config;


import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.PersonalIncomeTax;
import com.zhixianghui.facade.merchant.service.PersonalIncomeTaxFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-22
 */
@RestController
@RequestMapping("/personalIncomeTax")
public class PersonalIncomeTaxController {

    @Reference
    private PersonalIncomeTaxFacade personalIncomeTaxFacade;

    @GetMapping("listAll")
    public RestResult listAll(){
        List<PersonalIncomeTax> list = personalIncomeTaxFacade.listAll();
        return RestResult.success(list);
    }
}
