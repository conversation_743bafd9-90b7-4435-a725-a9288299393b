package com.zhixianghui.web.pms.vo.merchant;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.web.pms.vo.PageVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/27 14:35
 */
@Data
public class DataAnalyzeVo extends PageVo implements Serializable {

    private static final long serialVersionUID = 685183002837823214L;
    private String beginDate;
    private String endDate;
    private String currentDate;
    private String mainstayNo;
    private String mainstayName;
    private String employerNo;
    private String employerName;
    private String receiveName;
    private String idCard;
    private String receivePhoneNo;
    private String receiveIdCardNo;
    private String receiveIdCardNoMd5;
    private String receiveNameMd5;
    private String phone;
    private Long signId;
    private Long id;
    private String sortColumn;
    private String order;
    /**
     * @see com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum
     */
    private Integer idCardType;
    private String idCardBackUrl;
    private String idCardFrontUrl;
    /**
     * 身份证复印件url
     */
    private String idCardCopyFileUrl;
    private String cerFaceUrl;
    private String bankCardNumber;
    @JsonIgnore
    private Date startDate;
    @JsonIgnore
    private Date overDate;
    @JsonIgnore
    private PmsOperator operator;

    private Long saleId;
    private String saleName;

    /**
     * 是否上传身份证 1上传,0没有
     */
    private Integer hasUploadIdCard;

    /**
     * 是否签约 1签约,0没签约
     */
    private Integer hasSign;

    private List<String> employerList;

    private Integer signType;

    private String platTrxNo;
}
