//import com.zhixianghui.common.statics.enums.allocate.AllocateTypeEnum;
//import com.zhixianghui.common.statics.enums.tradesync.SyncPayChannelEnum;
//import com.zhixianghui.common.util.utils.RSAUtil;
//import com.zhixianghui.facade.tradesync.dto.PayOrderSyncAcceptDto;
//import org.junit.Test;
//
//import java.math.BigDecimal;
//import java.util.Arrays;
//import java.util.Date;
//
///**
// * Author: Cmf
// * Date: 2020.3.6
// * Time: 22:54
// * Description:
// */
//public class TestPayOrderSync {
//    public static String mchPayTrxNoPrefix = "cai_pay_20200512_";
//    public static String mchAllocTrxNoPrefix = "cai_alloc_20200512_";
//
//
//    @Test
//    public void test1() {
//        int num = 1;
//        int start = 10000;
//        Object[] objects = new Object[num];
//        for (int i = start; i < start + num; i++) {
//            PayOrderSyncAcceptDto acceptDto = new PayOrderSyncAcceptDto();
//            acceptDto.setPayChannel(SyncPayChannelEnum.ALIPAY_OFFICIAL.getChannel());
//            acceptDto.setPaySuccessTime(new Date());
//            acceptDto.setMchPayTrxNo(mchPayTrxNoPrefix + i);
//            acceptDto.setChannelPayTrxNo(mchPayTrxNoPrefix + i);
//            acceptDto.setAllocType(AllocateTypeEnum.DIRECT_ALLOCATE.getValue());
//            acceptDto.setBaseOrderAmount(new BigDecimal(100));
//            acceptDto.setDiscountAmount(new BigDecimal(0));
//            acceptDto.setCallbackUrl("");
//
//            PayOrderSyncAcceptDto.AllocReqInfo.AllocReqItem item1 = new PayOrderSyncAcceptDto.AllocReqInfo.AllocReqItem();
//            item1.setMchNo(MchInfo.MCH_NO);
//            item1.setAmount(new BigDecimal(20));
//            item1.setMchAllocItemTrxNo(mchAllocTrxNoPrefix + i + "_001");
//
//            PayOrderSyncAcceptDto.AllocReqInfo.AllocReqItem item2 = new PayOrderSyncAcceptDto.AllocReqInfo.AllocReqItem();
//            item2.setMchNo(MchInfo.SUB_0);
//            item2.setAmount(new BigDecimal(20));
//            item2.setMchAllocItemTrxNo(mchAllocTrxNoPrefix + i + "_002");
//
//            PayOrderSyncAcceptDto.AllocReqInfo.AllocReqItem item3 = new PayOrderSyncAcceptDto.AllocReqInfo.AllocReqItem();
//            item3.setMchNo(MchInfo.SUB_1);
//            item3.setAmount(new BigDecimal(60));
//            item3.setMchAllocItemTrxNo(mchAllocTrxNoPrefix + i + "_003");
//
//
//            PayOrderSyncAcceptDto.AllocReqInfo reqInfo = new PayOrderSyncAcceptDto.AllocReqInfo();
//            reqInfo.setMchAllocTrxNo(mchAllocTrxNoPrefix + i);
//            reqInfo.setAllocReqItems(Arrays.asList(item1, item2, item3));
//            acceptDto.setAllocReqInfo(reqInfo);
////            acceptDto.setCallbackUrl("http://*************:8080/test1");
//
//            objects[i - start] = acceptDto;
//
//        }
//        ApiTestUtil.doApiTest("http://host.machine:18099/tradeSync", "payOrder.doSync", objects);
//    }
//
//    @Test
//    public void test2() {
//        System.out.println(RSAUtil.genKeyPair());
//    }
//
//}
