//import com.alibaba.fastjson.JSON;
//import com.zhixianghui.api.base.params.RequestParam;
//import com.zhixianghui.api.base.params.ResponseParam;
//import com.zhixianghui.api.base.utils.SignUtil;
//import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
//import org.springframework.web.client.RestTemplate;
//
//import java.util.UUID;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.CyclicBarrier;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//
///**
// * Author: Cmf
// * Date: 2020.4.21
// * Time: 17:25
// * Description:
// */
//public class ApiTestUtil {
//
//    public static void doApiTest(String requestUrl, String requestMethod, Object[] dataArr) {
//        ExecutorService executor = Executors.newFixedThreadPool(dataArr.length);
//        CyclicBarrier barrier = new CyclicBarrier(dataArr.length);
//        CountDownLatch latch = new CountDownLatch(dataArr.length);
//        for (Object data : dataArr) {
//            executor.submit(() -> {
//                try {
//                    RequestParam requestParam = new RequestParam();
//                    requestParam.setMchNo(MchInfo.MCH_NO);
//                    requestParam.setMethod(requestMethod);
//                    requestParam.setVersion("1.0");
//                    requestParam.setRandStr(UUID.randomUUID().toString().substring(0, 32));
//                    requestParam.setSignType(SignTypeEnum.RSA.getValue() + "");
//                    requestParam.setSecKey("");
//                    requestParam.setData(JSON.toJSONString(data));
//                    requestParam.setSign(SignUtil.sign(requestParam, SignTypeEnum.RSA.getValue(), MchInfo.MCH_PRIVATE_KEY));
//                    RestTemplate r = new RestTemplate();
//                    barrier.await();
//                    ResponseParam resp = r.postForObject(requestUrl, requestParam, ResponseParam.class);
//                    System.out.println(JSON.toJSONString(data));
//                    System.out.println(JSON.toJSONString(resp) + "\n***************************************************************\n\n");
//                } catch (Exception ex) {
//                    System.out.println(ex);
//                } finally {
//                    latch.countDown();
//                }
//            });
//        }
//        try {
//            latch.await();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//}
