<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zhixianghui</groupId>
        <artifactId>zhixianghui-web</artifactId>
        <version>1.1-SNAPSHOT</version>
    </parent>
    <artifactId>web-pms-api</artifactId>
    <description>web-pms的接口层项目</description>

    <build>
        <finalName>${appName}</finalName>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-statics</artifactId>

        </dependency>

        <!-- dubbo START -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper</artifactId>
            <type>pom</type>
        </dependency>
        <!-- dubbo END -->


        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>component-starter</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>common-util</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-timer</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-merchant</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-fee</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-export</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-notify</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-risk-control</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-banklink</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-account-invoice</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-trade</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-flow</artifactId>

        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-data</artifactId>

        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>


        <dependency> <!-- 把nacos作为配置中心时需要引入的依赖 -->
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <!--fastdfs-->
        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>fastdfs-client</artifactId>
            <version>${fastdfs-client.version}</version>
        </dependency>
        <!--fsatdfs-->

        <dependency>
            <groupId>aspose</groupId>
            <artifactId>aspose</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhixianghui</groupId>
            <artifactId>facade-employee</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
    </dependencies>


</project>
