package com.zhixianghui.web.supplier.trade;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.web.supplier.component.JwtHelper;
import com.zhixianghui.web.supplier.component.SessionManager;
import com.zhixianghui.web.supplier.constant.PermissionConstant;
import com.zhixianghui.web.supplier.dto.JwtInfo;
import com.zhixianghui.web.supplier.dto.Session;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * <AUTHOR>
 * @description token获取类
 * @date 2020-11-03 16:33
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class TsGetTokenBiz {
    @Autowired
    private SessionManager sessionManager;
    @Autowired
    private JwtHelper jwtHelper;

    private MockHttpServletRequest request;

    @Before
    public void setUp(){
        request = new MockHttpServletRequest();
        request.setCharacterEncoding("UTF-8");
    }

    @Test
    public void getToken(){
        SupplierOperatorVO vo = new SupplierOperatorVO();
        vo.setId(10L);
        vo.setCreateTime(new Date());
        vo.setUpdateTime(new Date());
        vo.setPhone("15521192663");
        vo.setName("15521192663");
        vo.setStatus(1);

        // 创建session
        Session session = sessionManager.createSession(vo.getPhone());
        session.save();
        session.setAttribute(PermissionConstant.OPERATOR_SESSION_KEY, JsonUtil.toString(vo));
        // 生成token
        JwtInfo jwtInfo = new JwtInfo();
        jwtInfo.setUuid(session.getUuid());
        jwtInfo.setSupplierOperatorVO(vo);
        request.addHeader("User-Agent", "PostmanRuntime/7.26.5");
        request.addHeader("Host","0:0:0:0:0:0:0:1");
        request.addHeader("x-forwarded-for","0:0:0:0:0:0:0:1");
        System.out.print("========>token:" + jwtHelper.genToken(request, jwtInfo));
    }
}
