package com.zhixianghui.web.supplier.vo.permission;

import com.zhixianghui.facade.merchant.vo.supplier.SupplierRoleVo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
public class RoleResultVO {

    /**
     * id
     */
    private Long id;

    /**
     * 角色名称
     */
    @NotEmpty(message = "角色名称不能为空")
    @Size(min = 3, max = 90, message = "角色名称在3到90个字符")
    private String roleName;

    /**
     * 描述
     */
    private String remark;

    /**
     * 员工数量
     */
    private Long employerNumber;

    /**
     * 角色类型:0自定义,1预置
     */
    private Integer roleType;

    public static SupplierRoleVo buildDto(SupplierRoleVo vo, String mchNo) {
        SupplierRoleVo role = new SupplierRoleVo();
        role.setId(vo.getId());
        role.setCreateTime(new Date());
        role.setMchNo(mchNo);
        role.setName(vo.getName());
        role.setRemark(vo.getRemark());
        role.setRemark(vo.getRemark());
        role.setEmployerNumber(vo.getEmployerNumber());
        return role;
    }

    public static RoleResultVO buildVo(SupplierRoleVo role) {
        RoleResultVO vo = new RoleResultVO();
        vo.setId(role.getId());
        vo.setRoleName(role.getName());
        vo.setRemark(role.getRemark());
        vo.setEmployerNumber(role.getEmployerNumber());
        vo.setRoleType(role.getRoleType());
        return vo;
    }
}
