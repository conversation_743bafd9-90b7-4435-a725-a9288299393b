package com.zhixianghui.web.supplier.controller.taxcertificate;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.entity.TaxCertificateRecord;
import com.zhixianghui.facade.trade.service.TaxCertificateRecordFacade;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("taxCertificate")
public class TaxCertificateController {

    @Reference
    private TaxCertificateRecordFacade taxCertificateRecordFacade;


    @PostMapping("add")
    public RestResult<TaxCertificateRecord> add(@RequestBody TaxCertificateRecord taxCertificateRecord, @CurrentStaffVo SupplierStaffVO staffVO) {
        taxCertificateRecord.setCreateBy(staffVO.getPhone());
        taxCertificateRecord.setUpdateBy(staffVO.getPhone());
        taxCertificateRecord.setMainstayNo(staffVO.getMchNo());
        taxCertificateRecord.setMainstayName(staffVO.getMchName());
        return RestResult.success(taxCertificateRecordFacade.save(taxCertificateRecord));
    }

    @PostMapping("edit")
    public RestResult<TaxCertificateRecord> edit(@RequestBody TaxCertificateRecord taxCertificateRecord, @CurrentStaffVo SupplierStaffVO staffVO) {
        taxCertificateRecord.setUpdateBy(staffVO.getPhone());
        return RestResult.success(taxCertificateRecordFacade.update(taxCertificateRecord));
    }

    @PostMapping("listPage")
    public RestResult<Page<TaxCertificateRecord>> listPage(@RequestBody Page<TaxCertificateRecord> page, @RequestBody Map<String,Object> param, @CurrentMainstayNo String mainstayNo) {
        param.put("mainstayNo", mainstayNo);
        return RestResult.success(taxCertificateRecordFacade.listPage(page, param));
    }

    @PostMapping("delete")
    public RestResult<Boolean> delete(Integer id) {
        return RestResult.success(taxCertificateRecordFacade.delete(id));
    }

}
