package com.zhixianghui.web.supplier.controller.common;


import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.web.supplier.vo.permission.DataDictionaryVO;
import com.zhixianghui.web.supplier.vo.permission.PortalDataDictionaryQueryVO;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商户后台数据字典A
 */

@RestController
@RequestMapping("portalDataDictionary")
public class DataDictionaryController  {

    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    //@Permission("common:dictionary:view")
    @RequestMapping("listDataDictionary")
    public RestResult<PageResult<List<DataDictionary>>> listDataDictionary(@RequestParam int pageCurrent,
                                                                           @RequestParam int pageSize,
                                                                           @RequestBody(required = false) PortalDataDictionaryQueryVO portalDataDictionaryQueryVO) {
        try {
            Map<String, Object> paramMap = BeanUtil.toMap(portalDataDictionaryQueryVO);
            PageResult<List<DataDictionary>> pageResult = dataDictionaryFacade.listDataDictionaryPage(paramMap, PageParam.newInstance(pageCurrent, pageSize));
            return RestResult.success(pageResult);
        } catch (Exception e) {
            logger.error("== dataDictionaryList exception:", e);
            return RestResult.error("获取数据字典失败");
        }
    }

    //@Permission("common:dictionary:view")
    @RequestMapping("getDataDictionaryVOById")
    public RestResult<DataDictionaryVO> getDataDictionaryVOById(@RequestParam long id) {
        try {
            DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryById(id);
            return RestResult.success(new DataDictionaryVO(dataDictionary));
        } catch (Exception e) {
            logger.error("== getDataDictionaryVOById exception:", e);
            return RestResult.error("获取数据字典失败");
        }
    }


    /**
     * 该接口用于用户运营后台后获取，不添加权限判断
     *
     * @return .
     */
    @RequestMapping("listAllDataDictionaryVO")
    public RestResult<List<DataDictionaryVO>> listAllDataDictionaryVO() {

        List<DataDictionaryVO> list = dataDictionaryFacade.listAllDataDictionary().stream().map(DataDictionaryVO::new).collect(Collectors.toList());
        return RestResult.success(list);
    }
}
