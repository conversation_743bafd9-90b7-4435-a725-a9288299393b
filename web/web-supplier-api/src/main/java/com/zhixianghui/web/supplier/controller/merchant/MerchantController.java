package com.zhixianghui.web.supplier.controller.merchant;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.export.vo.ExportVo;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import com.zhixianghui.facade.merchant.vo.MerchantSalerQueryVO;
import com.zhixianghui.facade.merchant.vo.MerchantSupplierInfoVo;
import com.zhixianghui.facade.merchant.vo.flow.MerchantFlowVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentOperatorVO;
import com.zhixianghui.web.supplier.annotation.Logger;
import com.zhixianghui.web.supplier.utils.BeanToMapUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商户管理
 * <AUTHOR>
 * @date 2021-03-26
 **/
@RestController
@RequestMapping("merchant")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantController {

    @Reference
    private MerchantBankAccountFacade bankAccountFacade;
    @Reference
    private MerchantEmployerQuoteFacade quoteFacade;
    @Reference
    private MerchantEmployerPositionFacade positionFacade;
    @Reference
    private MerchantEmployerCooperateFacade employerCooperateFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantEmployerMainFacade employerMainFacade;
    @Reference
    private MerchantFileFacade merchantFileFacade;
    @Reference
    private MerchantInvoiceInfoFacade invoiceInfoFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private MerchantEmployerFacade merchantEmployerFacade;
    @Reference
    private AgreementFacade agreementFacade;
    @Reference
    private MerchantInfoChangeRecordFacade infoChangeRecordFacade;

    //屏蔽字段
    private final static List<String> BAN_PARAM = Arrays.asList(new String[]{"salerName","salerId","agentNo","agentName",
            "contactName","contactPhone","contactEmail","servicePhone"});

//    @PostMapping("record")
//    public RestResult<List<MerchantInfoChangeRecord>> record(@RequestBody MerchantFlowVo flowVo, @CurrentMainstayNo String mainstayNo){
//        return RestResult.success(infoChangeRecordFacade.list(flowVo.getMchNo()));
//    }

    /**
     * 根据供应商编码查询商户
     * @param mainstayNo
     * @return
     */
    @Logger(type = OperateLogTypeEnum.QUERY, action="根据供应商编码查询商户")
    @Permission("merchantEmployer:plat:view")
    @GetMapping("listMchByMainstayNo")
    public RestResult<List<EmployerMainstayRelation>> listMchByMainstayNo(@CurrentMainstayNo String mainstayNo){
        //查询代征关系所属表
        //根据当前供应商编号和状态为激活的数据进行过滤
        if (StringUtil.isEmpty(mainstayNo)){
            return RestResult.error("当前供应商编号为空");
        }
        Map<String,Object> paramMap = Maps.newHashMap();
//        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        paramMap.put("mainstayNo",mainstayNo);
        return RestResult.success(employerMainstayRelationFacade.listBy(paramMap));
    }

    @Logger(type = OperateLogTypeEnum.QUERY,action = "根据供应商编码分页查询商户数据列表")
    @Permission("merchantEmployer:plat:view")
    @PostMapping("/listMerchantPage")
    public RestResult<PageResult<List<MerchantSupplierInfoVo>>> listMerchantPage(@RequestBody MerchantSalerQueryVO merchantSalerQueryVO,@CurrentMainstayNo String mainstayNo){
        //查询代征关系所属表
        //根据当前供应商编号和状态为激活的数据进行过滤
        if (StringUtil.isEmpty(mainstayNo)){
            return RestResult.error("当前供应商编号为空");
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        if (merchantSalerQueryVO.getBindStatus() != null) {
            paramMap.put("status", merchantSalerQueryVO.getBindStatus());
        }
        paramMap.put("mainstayNo",mainstayNo);
        final List<EmployerMainstayRelation> employerMainstayRelations = employerMainstayRelationFacade.listBy(paramMap);
        List<String> mchNoList = employerMainstayRelations.
                stream().map(EmployerMainstayRelation::getEmployerNo).collect(Collectors.toList());

        Map<String, Integer> map = new HashMap<>();
        for (EmployerMainstayRelation employerMainstayRelation : employerMainstayRelations) {
            map.put(employerMainstayRelation.getEmployerNo(), employerMainstayRelation.getStatus());
        }

        if (mchNoList == null || mchNoList.size() <= 0){
            return RestResult.success(PageResult.newInstance(PageParam.newInstance(merchantSalerQueryVO.getPageCurrent(), merchantSalerQueryVO.getPageSize()),null));
        }
        paramMap.clear();
        paramMap = BeanToMapUtil.beanToMap(merchantSalerQueryVO);
        paramMap.put("mchNos",mchNoList);
        paramMap.put("status",OpenOffEnum.OPEN.getValue());
        //分页查询商户列表
        PageResult<List<MerchantSupplierInfoVo>> pageResult = merchantQueryFacade.listExtSupplierMerchantPage(paramMap, PageParam.newInstance(merchantSalerQueryVO.getPageCurrent(), merchantSalerQueryVO.getPageSize()));
        if (pageResult != null) {
            final List<MerchantSupplierInfoVo> data = pageResult.getData();
            if (data != null) {
                for (MerchantSupplierInfoVo datum : data) {
                    final String mchNo = datum.getMchNo();
                    datum.setBindStatus(map.get(mchNo));
                }
            }
        }

        return RestResult.success(pageResult);
    }

    /**
     * 根据商户编码查询商户详细信息，注意验证供应商是否与商户相关联
     * @param mchNo
     * @param mainstayNo
     * @return
     */
    @Logger(type = OperateLogTypeEnum.QUERY,action = "根据商户编码查询商户详细信息")
    @Permission("merchantEmployer:plat:view")
    @GetMapping("/getMerchantInfoVo")
    public RestResult getMerchantInfoVo(@RequestParam(name = "mchNo")String mchNo,@CurrentMainstayNo String mainstayNo){
        if(!validSupplier(mchNo,mainstayNo)){
            return RestResult.error("没有查询此商户权限");
        }

        //获取合作信息
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        // 合作信息
        Map<String, Object> cooperateMap = new HashMap<>();

        //获取主体信息
        MerchantEmployerMain employerMain = employerMainFacade.getByMchNo(mchNo);
        Map<String,Object> mainMap = BeanUtil.toMap(employerMain);
        mainMap.put("mchName", merchant.getMchName());

        //获取文件信息
        // 文件信息
        //去除身份证信息
        List<MerchantFile> fileList = merchantFileFacade.listByMchNo(mchNo);
        List<String> cooperateFileList = new ArrayList<>();
        List<String> supplementFileUrls = new ArrayList<>();
        fileList.stream().forEach(merchantFile -> {
            if(merchantFile.getFileType() == MerchantFileTypeEnum.COMPANY_LEAFLET.getValue()){
                cooperateFileList.add(merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue()){
                supplementFileUrls.add(merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.BUSINESS_LICENSE.getValue()){
                mainMap.put("businessLicenseFileUrl", merchantFile.getFileUrl());
//            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.ID_CARD_HEAD.getValue()){
//                mainMap.put("idCardHeadFileUrl", merchantFile.getFileUrl());
//            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.ID_CARD_EMBLEM.getValue()){
//                mainMap.put("idCardEmblemFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.ID_CARD_COPY.getValue()){
                mainMap.put("idCardCopyFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.DOOR_PHOTO.getValue()){
                mainMap.put("doorPhotoFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.WORK_INDOOR.getValue()){
                mainMap.put("workIndoorFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.RECEPTION.getValue()){
                mainMap.put("receptionFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.ENTRUST_AGREEMENT.getValue()){
                cooperateMap.put("entrustAgreementFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_B.getValue()){
                cooperateMap.put("agreementTemplate2BFileUrl", merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.AGREEMENT_TEMPLATE_TO_C.getValue()){
                cooperateMap.put("agreementTemplate2CFileUrl", merchantFile.getFileUrl());
            }
        });

        cooperateMap.put("cooperateFileList", cooperateFileList);
        cooperateMap.put("supplementFileUrls", supplementFileUrls);

        // 银行账号信息
        MerchantBankAccount bankAccount = bankAccountFacade.getByMchNo(mchNo);

        // 商户开票信息
        MerchantInvoiceInfo invoiceInfo = invoiceInfoFacade.getByMchNo(mchNo);
        Map<String, Object> invoiceInfoMap = BeanUtil.toMap(invoiceInfo);

        // 用工企业特有信息
        if(merchant.getMerchantType() == MerchantTypeEnum.EMPLOYER.getValue()){
            MerchantEmployerCooperate cooperate = employerCooperateFacade.getByMchNo(mchNo);
            Map<String, Object> cooperateVoMap = BeanUtil.toMap(cooperate);
            cooperateMap.putAll(cooperateVoMap);

            // 岗位信息
            List<MerchantEmployerPosition> positionList = positionFacade.listByMchNo(mchNo);
            cooperateMap.put("positionVoList", positionList);

            // 报价单信息
            Map<String,Object> params = new HashMap<>();
            params.put("mchNo",mchNo);
            params.put("mainstayMchNo",mainstayNo);
            List<MerchantEmployerQuote> quoteList = quoteFacade.listBy(params);
            cooperateMap.put("quoteVoList", quoteList);

            // 获取所有绑定的发票类目信息
            List<InvoiceCategoryVo> invoiceCategoryVoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(positionList)) {
                positionList.forEach(x -> {
                    invoiceCategoryVoList.addAll(x.getJsonEntity().getInvoiceCategoryList());
                });
            }
            // 去重
            List<InvoiceCategoryVo> uniqueInvoiceCategoryVoList = invoiceCategoryVoList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InvoiceCategoryVo::getInvoiceCategoryCode))), ArrayList::new)
            );
            invoiceInfoMap.put("invoiceCategoryVoList", uniqueInvoiceCategoryVoList);
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cooperate", cooperateMap);
        resultMap.put("main", mainMap);
        //暂时屏蔽银行卡信息
        resultMap.put("bankAccount", bankAccount);
        resultMap.put("invoiceInfo", invoiceInfoMap);

        return RestResult.success(resultMap);
    }

    @Permission("merchant:employer:export")
    @RequestMapping("exportMerchantList")
    public RestResult<String> exportMerchantList(@Valid @RequestBody ExportVo exportVo,@CurrentMainstayNo String mainstayNo,@CurrentOperatorVO SupplierOperatorVO vo){
        if (StringUtil.isEmpty(mainstayNo)) {
            return RestResult.error("供应商有误");
        }
        //报价单条件
        exportVo.getParamMap().put("supplierNo",mainstayNo);
        ExportRecord record = generatorRecord(exportVo,vo,mainstayNo,ReportTypeEnum.MAINSTAY_MERCHANT_LIST);
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 商户信息导出
     *
     * @param exportVo .
     * @return .
     */
    @Permission("merchant:employer:export")
    @RequestMapping("exportMerchantInfo")
    public RestResult<String> exportEmployerInfo(@Valid @RequestBody ExportVo exportVo, @CurrentMainstayNo String mainstayNo, @CurrentOperatorVO SupplierOperatorVO vo) {
        String mchNo = (String) exportVo.getParamMap().get("mchNo");
        if (StringUtil.isEmpty(mchNo)) {
            return RestResult.error("商户号不能为空");
        }
        if (StringUtil.isEmpty(mainstayNo)) {
            return RestResult.error("供应商有误");
        }

        //报价单条件
        exportVo.getParamMap().put("supplierNo",mainstayNo);

        ExportRecord record = generatorRecord(exportVo,vo,mainstayNo,ReportTypeEnum.MAINSTAY_MERCHANT_EXPORT);
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private ExportRecord generatorRecord(ExportVo exportVo,SupplierOperatorVO vo,String mainstayNo,ReportTypeEnum reportTypeEnum) {
        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(vo.getPhone());
        //供应商后台编码
        record.setMchNo(mainstayNo);
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());

        record.setParamJson(JsonUtil.toString(exportVo.getParamMap()));
        exportVo.getFieldInfoList().forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        return record;
    }


    /**
     * 验证供应商与商户是否相关联
     * @param mchNo
     * @param mainstayNo
     * @return
     */
    private Boolean validSupplier(String mchNo,String mainstayNo){
        if (StringUtil.isEmpty(mchNo) || StringUtil.isEmpty(mainstayNo)){
            return false;
        }
        Map<String,Object> paramMap = Maps.newHashMap();
//        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        paramMap.put("mainstayNo",mainstayNo);
        List<String> supplierMchList =  employerMainstayRelationFacade.listBy(paramMap).stream().
                map(EmployerMainstayRelation::getEmployerNo).collect(Collectors.toList());
        if (supplierMchList == null || supplierMchList.size() <= 0){
            return false;
        }
        //判断供应商与商户的关系
        if (supplierMchList.contains(mchNo)){
            return true;
        }else {
            return false;
        }
    }
}
