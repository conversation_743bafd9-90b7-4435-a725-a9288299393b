package com.zhixianghui.web.supplier.controller.flow;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.PmsOperatorTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.flow.dto.CommonFlowLogVo;
import com.zhixianghui.facade.flow.dto.CommonFlowVo;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.enums.WorkTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.service.FlowImageFacade;
import com.zhixianghui.facade.flow.vo.req.*;
import com.zhixianghui.facade.flow.vo.res.TaskResVo;
import com.zhixianghui.facade.merchant.service.supplier.SupplierRoleFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.vo.PageVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName FlowController
 * @Description TODO
 * @Date 2021/5/17 9:16
 */
@RestController
@RequestMapping("flow")
public class FlowController extends CommonFlow {

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private FlowImageFacade flowImageFacade;

    @Reference
    private SupplierStaffFacade supplierStaffFacade;

    @Reference
    private SupplierRoleFacade supplierRoleFacade;

    @Reference
    private ExportRecordFacade exportRecordFacade;

    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 我的已办
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("supplier:flow:view")
    @PostMapping("handleList")
    public RestResult<PageResult<List<Map<String,Object>>>> handleList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo SupplierStaffVO staffVO) {
        boolean admin = admin(staffVO, supplierRoleFacade);
        paramMap.put("mchNo", staffVO.getMchNo());
        PageResult<List<Map<String,Object>>> taskList = flowFacade.handleList(staffVO.getId(), PlatformSource.SUPPLIER.getValue(), admin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 我的发起
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("supplier:flow:view")
    @PostMapping("sendList")
    public RestResult<PageResult<List<Map<String,Object>>>> sendList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo SupplierStaffVO staffVO) {
        boolean admin = admin(staffVO, supplierRoleFacade);
        paramMap.put("mchNo", staffVO.getMchNo());
        PageResult<List<Map<String,Object>>> taskList = flowFacade.sendList(staffVO.getId(), PlatformSource.SUPPLIER.getValue(), admin, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 我的待办
     *
     * @param paramMap
     * @param pageVo
     * @param staffVO
     * @return
     */
    @Permission("supplier:flow:view")
    @PostMapping("todoList")
    public RestResult<PageResult<List<Map<String,Object>>>> todoList(@RequestBody Map<String, Object> paramMap, @RequestBody PageVo pageVo, @CurrentStaffVo SupplierStaffVO staffVO) {
        PageResult<List<Map<String,Object>>> taskList = flowFacade.todoList(getFlowUserVo(staffVO), false, paramMap, pageVo.toPageParam());
        return RestResult.success(taskList);
    }

    /**
     * 获取操作日志
     *
     * @param commonFlowId 流程通用id
     * @return
     */
    @Permission("supplier:flow:view")
    @GetMapping("getDetail")
    public RestResult<List<CommonFlowLogVo>> getDetail(@RequestParam(name = "commonFlowId") Long commonFlowId) {
        List<CommonFlowLogVo> commonFlowLogList = flowFacade.getDetailByCommonFlowId(commonFlowId, PlatformSource.SUPPLIER.getValue());
        return RestResult.success(commonFlowLogList);
    }

    /**
     * 根据流程实例id获取流程追踪图
     *
     * @param processInstanceId 流程实例id
     * @return
     * @throws IOException
     */
    @Permission("supplier:flow:image")
    @GetMapping("getInstanceImage")
    public RestResult<?> getInstanceImage(@RequestParam(name = "processInstanceId") String processInstanceId) throws IOException {
        String base64 = flowImageFacade.getInstanceImage(processInstanceId);
        return RestResult.success(base64);
    }

    /**
     * 提交任务
     *
     * @param taskHandleVo
     * @return
     */
    @Permission("supplier:flow:submit")
    @PostMapping("submitTask")
    public RestResult<?> submitTask(@RequestBody TaskHandleVo taskHandleVo, @CurrentStaffVo SupplierStaffVO staffVO) {
        flowFacade.executeTask(taskHandleVo, getFlowUserVo(staffVO), false);
        return RestResult.success("任务提交完毕");
    }

    @Permission("supplier:flow:submit")
    @PostMapping("withDraw")
    public RestResult<?> withDraw(@RequestBody Map<String, Object> map, @CurrentStaffVo SupplierStaffVO staffVO) {
        Long commonFlowId = Long.valueOf(String.valueOf(map.get("commonFlowId")));
        String reason = (String) map.get("reason");
        flowFacade.deleteProcessInstance(commonFlowId, getFlowUserVo(staffVO), false, reason);
        return RestResult.success("流程撤回成功");
    }

    /**
     * 获取流程数据
     *
     * @param commonFlowId
     * @return
     */
    @Permission("supplier:flow:view")
    @GetMapping("getByCommonFlowId")
    public RestResult<CommonFlowVo> getByCommonFlowId(@RequestParam("commonFlowId") Long commonFlowId, @RequestParam(value = "taskId", required = false) String taskId,
                                                      @CurrentStaffVo SupplierStaffVO staffVO) {

        CommonFlowVo commonFlowVo = flowFacade.getCommonFlowById(commonFlowId, taskId, getFlowUserVo(staffVO), false, PlatformSource.SUPPLIER.getValue());
        if (commonFlowVo.getFlowTopicType().equals(FlowTypeEnum.PMS_EMPLOY_MAINSTAY_RELATION_APPLY.getFlowTopicType())) {
            commonFlowVo = putCommonFlowVo(commonFlowVo);
        }
        return RestResult.success(commonFlowVo);
    }

    private CommonFlowVo putCommonFlowVo(CommonFlowVo commonFlowVo) {
        JSONObject jsonObject = JSONObject.parseObject(commonFlowVo.getExtInfo());
        jsonObject.put("salerId", null);
        jsonObject.put("salerName", null);
        jsonObject.put("contactPhone", null);
        jsonObject.put("contactName", null);
        jsonObject.put("agentNo", null);
        jsonObject.put("agentName", null);
        commonFlowVo.setExtInfo(jsonObject.toJSONString());
        return commonFlowVo;
    }

    @Permission("supplier:flow:submit")
    @PostMapping("editBusinessVariable")
    public RestResult<?> editBusinessVariable(@RequestBody @Validated(IEditBusinessVariable.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo SupplierStaffVO staffVO) {
        flowFacade.editBusinessVariable(commonFlowEditVo, getFlowUserVo(staffVO), false);
        return RestResult.success("修改信息成功");
    }

    @Permission("supplier:flow:submit")
    @PostMapping("transferTask")
    public RestResult<?> transferTask(@RequestBody @Validated(ITransferUser.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo SupplierStaffVO staffVO) {
        //获取变更人
        SupplierStaffVO supplierStaffVO = supplierStaffFacade.getById(staffVO.getMchNo(), commonFlowEditVo.getNextUserId());
        if (supplierStaffVO == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("用户不存在，请选择其他用户");
        }
        FlowUserVo nextUser = new FlowUserVo();
        nextUser.setUserId(supplierStaffVO.getId());
        nextUser.setPlatform(PlatformSource.SUPPLIER.getValue());
        nextUser.setUserName(supplierStaffVO.getName());
        nextUser.setNo(supplierStaffVO.getMchNo());
        flowFacade.transferTask(commonFlowEditVo, getFlowUserVo(staffVO), nextUser);
        return RestResult.success("变更审批人成功");
    }

    @Permission("supplier:flow:reply")
    @PostMapping("reply")
    public RestResult<?> reply(@RequestBody @Validated(IReply.class) CommonFlowEditVo commonFlowEditVo, @CurrentStaffVo SupplierStaffVO supplierStaffVO) {
        flowFacade.reply(commonFlowEditVo, getFlowUserVo(supplierStaffVO));
        return RestResult.success("回复成功");
    }

    @Permission("supplier:flow:view")
    @PostMapping("workOrderPage")
    public RestResult workOrderPage(@RequestBody Map<String,Object> paramMap,@RequestBody PageVo pageVo,@CurrentMainstayNo String mainstayNo){
        paramMap.put("mainstayNo",mainstayNo);
        return RestResult.success(flowFacade.workOrderPage(PlatformSource.SUPPLIER.getValue(),paramMap,pageVo.toPageParam()));

    }

    private FlowUserVo getFlowUserVo(SupplierStaffVO staffVO) {
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setPlatform(PlatformSource.SUPPLIER.getValue());
        flowUserVo.setUserId(staffVO.getId());
        flowUserVo.setUserName(StringUtil.isEmpty(staffVO.getName()) ? staffVO.getMchNo() : staffVO.getName());
        flowUserVo.setNo(staffVO.getMchNo());
        return flowUserVo;
    }

    /**
     * 导出工单列表
     * @param paramMap
     * @param pmsOperator
     * @return
     */
    @PostMapping("exportWorkOrder/{type}")
    public RestResult exportWorkOrder(@PathVariable("type") Integer type,@RequestBody Map<String,Object> paramMap,@CurrentStaffVo SupplierStaffVO staffVO){
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        ExportRecord record = getRecord(type,paramMap,staffVO);
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 一键导出附件
     * @param type
     * @param paramMap
     * @param pmsOperator
     * @return
     */
    @PostMapping("exportWorkOrderFile/{type}")
    public RestResult exportWorkOrderFile(@PathVariable("type") Integer type,@RequestBody Map<String,Object> paramMap,@CurrentStaffVo SupplierStaffVO staffVO){
        paramMap.put("exportFileType",FileTypeEnum.ZIP.getValue());
        ExportRecord record = getRecord(type,paramMap,staffVO);
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private ExportRecord getRecord(Integer type, Map<String, Object> paramMap,SupplierStaffVO staffVO) {
        //固定类型为工单类型
        paramMap.put("workType", WorkTypeEnum.WORK_FLOW.getValue());
        ReportTypeEnum reportTypeEnum = ReportTypeEnum.getEnum(type);
        switch (reportTypeEnum){
            case WORK_SUP_FLOW_TODO_EXPORT:
                getTodoParam(paramMap,staffVO);
                break;
            case WORK_SUP_FLOW_HANDLE_EXPORT:
                getHandleParam(paramMap,staffVO);
                break;
            default:
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无法导出此类型");
        }
        ExportRecord record = ExportRecord.newDefaultInstance();
        String fileNo = exportRecordFacade.genFileNo();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setMchNo(staffVO.getMchNo());
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(reportTypeEnum.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        return record;
    }

    private void getHandleParam(Map<String, Object> paramMap, SupplierStaffVO staffVO) {
        paramMap.put("mainstayNo",staffVO.getMchNo());
    }

    private void getTodoParam(Map<String, Object> paramMap, SupplierStaffVO staffVO) {
        paramMap.put("isAdmin",false);
        paramMap.put("flowUserVo",JsonUtil.toString(getFlowUserVo(staffVO)));
    }
}
