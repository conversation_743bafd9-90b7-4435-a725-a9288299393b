package com.zhixianghui.web.supplier.component;

import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.JwtTokenUtil;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierOperator;
import com.zhixianghui.facade.merchant.service.supplier.SupplierOperatorFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.constant.PermissionConstant;
import com.zhixianghui.web.supplier.dto.JwtInfo;
import com.zhixianghui.web.supplier.dto.Session;
import com.zhixianghui.web.supplier.utils.NetUtil;
import com.zhixianghui.web.supplier.vo.permission.FunctionVO;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Log4j2
public class JwtHelper {

    @Value(value = "${jwtPublicKey}")
    private String jwtPublicKey;

    @Value(value = "${jwtPrivateKey}")
    private String jwtPrivateKey;

    private RSAPublicKey publicKey = null;

    private RSAPrivateKey privateKey = null;

    @Reference
    private SupplierOperatorFacade supplierOperatorFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private SupplierStaffFacade supplierStaffFacade;

    @Autowired
    private SessionManager sessionManager;

    @PostConstruct
    public void init() {
        try {
            publicKey = RSAUtil.parsePublicKey(jwtPublicKey);
            privateKey = RSAUtil.parsePrivateKey(jwtPrivateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 生成token
     */
    public String genToken(HttpServletRequest request, JwtInfo jwtInfo) {
        Map<String, String> claims = new HashMap<>();
        claims.put("UserAgent", request.getHeader("User-Agent"));
        claims.put("Host", NetUtil.getIpAddr(request));
        return JwtTokenUtil.genToken(privateKey, jwtInfo, claims);
    }

    /**
     * 校验token
     */
    public JwtInfo verifyToken(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String token = null;
        if (uri.startsWith(PermissionConstant.DOWNLOAD_URL_PREFIX)
                || ("GET".equals(request.getMethod()) && StringUtil.isNotEmpty(request.getParameter(PermissionConstant.REQUEST_TOKEN_HEADER)))) {
            token = request.getParameter(PermissionConstant.REQUEST_TOKEN_HEADER);
        } else {
            token = request.getHeader(PermissionConstant.REQUEST_TOKEN_HEADER);
        }

        /***********************接口测试，不合生产*****************************/
        final String openCaptcha = dataDictionaryFacade.getSystemConfig("OPEN_CAPTCHA");
        if (openCaptcha != null) {
            String loginName = request.getHeader("x-loginName");
            String mchNo = request.getHeader("x-mchNo");
            Session session = null;
            if (StringUtil.isNotEmpty(loginName)) {
                SupplierOperator operator = supplierOperatorFacade.getByPhone(loginName);
                SupplierOperatorVO vo = SupplierOperatorVO.build(operator);
                session = sessionManager.createSession(vo.getPhone());
                session.save();
                session.setAttribute(PermissionConstant.OPERATOR_SESSION_KEY, JsonUtil.toString(vo));

                JwtInfo jwtInfo = new JwtInfo();
                jwtInfo.setUuid(session.getUuid());
                jwtInfo.setSupplierOperatorVO(vo);
                token = genToken(request, jwtInfo);
            }
            if (StringUtil.isNotEmpty(mchNo)) {
                SupplierStaffVO staffVO = supplierStaffFacade.getByPhone(mchNo, loginName);
                List<FunctionVO> functions = supplierStaffFacade.listFunctionByStaffId(mchNo, staffVO.getId()).stream()
                        .map(FunctionVO::buildVo).collect(Collectors.toList());
                // 商户编号、员工信息、权限信息更新至session
                Map<String, String> map = new HashMap<>();
                map.put(PermissionConstant.MCH_NO_SESSION_KEY, mchNo);
                map.put(PermissionConstant.STAFF_SESSION_KEY, JsonUtil.toString(staffVO));
                map.put(PermissionConstant.PERMISSION_SESSION_KEY, JsonUtil.toString(
                        functions.stream().map(FunctionVO::getPermissionFlag).collect(Collectors.toList())));
                session.setAttribute(map);
            }
        }
        /***********************接口测试，不合生产*****************************/

        Map<String, String> claims = new HashMap<>();
        claims.put("UserAgent", request.getHeader("User-Agent"));
        claims.put("Host", NetUtil.getIpAddr(request));

        JwtInfo jwtInfo = null;
        try {
            jwtInfo = JwtTokenUtil.verifyToken(publicKey, token, claims, JwtInfo.class);
        } catch (Exception e) {
            log.error("校验token失败, IP: {}, {},", claims.get("Host"), e.getMessage());
        }
        return jwtInfo;
    }
}
