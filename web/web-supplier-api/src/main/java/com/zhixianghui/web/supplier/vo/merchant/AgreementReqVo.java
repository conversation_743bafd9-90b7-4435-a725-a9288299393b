package com.zhixianghui.web.supplier.vo.merchant;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-03 16:03
 **/
@Data
public class AgreementReqVo implements Serializable {

    @NotNull(groups = IAgreementArchive.class, message = "id不能为空")
    private Long id;

    /**
     * 协议文件列表
     */
    @Valid
    @NotEmpty(message = "归档文件不能为空")
    private List<AgreementFileVo> fileVoList;

}
