package com.zhixianghui.web.supplier.vo.trade.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-09 14:28
 **/
@Data
public class CertificateQueryVo {


    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 商户名称
     */
    private String employerName;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;
    /**
     * 收款账户
     */
    private String receiveAccountNo;

    /**
     * 收款姓名
     */
    private String receiveName;

    /**
     * 创建起始时间
     */
    @NotNull(message = "创建起始时间不能为空")
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    @NotNull(message = "创建截止时间不能为空")
    private Date createEndDate;

    private String employerNameLike;

}
