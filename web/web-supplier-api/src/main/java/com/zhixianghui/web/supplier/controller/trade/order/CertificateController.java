package com.zhixianghui.web.supplier.controller.trade.order;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.SftpUtil;
import com.zhixianghui.common.util.utils.ZipUtil;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.ReceiptOrderEnum;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.vo.trade.req.CertificateQueryVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import sun.misc.BASE64Decoder;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/29
 **/
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CertificateController {
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private OrderItemFacade orderItemFacade;

    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;

    /**
     * 下载凭证文件
     *
     * @return 提示
     */
    @RequestMapping("/certificate/batchDownloadCertificateFile/{type}")
    public RestResult<String> downloadCertificateFile(@PathVariable Integer type,@RequestBody CertificateQueryVo queryVo, @CurrentStaffVo SupplierStaffVO staffVo) {
        Map<String, Object> paramMap = BeanUtil.toMap(queryVo);
        ReportTypeEnum reportTypeEnum = ReceiptOrderEnum.getEnum(type).getReportTypeEnum();
        paramMap.put("exportFileType", FileTypeEnum.ZIP.getValue());
        paramMap.put("mainstayNo", staffVo.getMchNo());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        paramMap.put("type",type);
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setMchNo(staffVo.getMchNo());
        record.setOperatorLoginName(staffVo.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 单笔实时下载电子凭证文件
     *
     * @param platTrxNo
     * @param staffVO
     * @param response
     * @throws Exception
     */
    @GetMapping("/download/downloadCertificateFile")
    public void downloadCertificateFile(@RequestParam String platTrxNo, @RequestParam(name = "type") Integer type, @CurrentStaffVo SupplierStaffVO staffVO, HttpServletResponse response, @CurrentMainstayNo String mainstayNo) throws Exception{
        OrderItem item = orderItemFacade.getByPlatTrxNo(platTrxNo);
        if (item == null || item.getOrderItemStatus() != OrderItemStatusEnum.GRANT_SUCCESS.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("订单不存在或者状态不为成功");
        }
        if (!item.getMainstayNo().equals(mainstayNo)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无下载此回单权限");
        }
        if (type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue() && (item.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())
                ||item.getPayChannelNo().equals(ChannelNoEnum.OUT_SYNC.name()))){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("此通道或产品不支持企业支付回单下载");
        }

        if(type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue() && StringUtils.isNotBlank(item.getProductNo()) && item.getProductNo().equals(ProductNoEnum.CKH.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("此通道或产品不支持企业支付回单下载");
        }

        //外部同步订单特殊处理
        if (StringUtils.equals(item.getPayChannelNo(), ChannelNoEnum.OUT_SYNC.name())) {
            OutputStream os = null;
            try {
                os = response.getOutputStream();
                String fileName = getFileName(type,item);

                Map<String, Object> downloadParam = new HashMap<>();
                downloadParam.put("source", "HJZX");
                downloadParam.put("flowNo", item.getMchOrderNo());
                downloadParam.put("tradeDate", DateUtil.formatDate(item.getCompleteTime()));
                String post = HttpRequest.post("https://open-baseweb.leshuazf.com/external-api/invoiceFlow/download")
                        .header(Header.CONTENT_TYPE, "application/json")
                        .body(JSONUtil.toJsonStr(downloadParam)) //表单内容
                        .timeout(20000) //超时，毫秒
                        .execute().body();
                log.info(post);

                JSONObject jsonObject = JSONUtil.parseObj(post);
                String fileData = jsonObject.getJSONObject("data").getStr("fileData");
                BASE64Decoder decoder = new BASE64Decoder();
                byte[] bytes = decoder.decodeBuffer(fileData);

                response.reset();
                response.setContentType("application/pdf;charset=utf-8");
                response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
                os.write(bytes);
                os.flush();
            }catch (Exception e){
                log.error("下载回单失败:", e);
                String errMsg = e.getMessage().contains("文件不存在")?"凭证文件不存在":"凭证文件下载异常";
                if(os != null){
                    response.reset();
                    response.setContentType("application/json;charset=utf-8");
                    os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes("utf-8"));
                } else {
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("下载电子凭证文件异常");
                }
            }
            return;
        }

        // 文件所在的sftp文件夹
        String tempPath = System.getProperty("user.dir") + File.separator + "download" + File.separator + RandomUtil.get16LenStr();
        ChannelSftp channelSftp = null;
        OutputStream os = null;
        String sftpFilePath = CertificateUtil.getFilePath(dir, item.getMainstayNo(), item.getPlatTrxNo());
        File file =  null;
        try {
            os = response.getOutputStream();
            // 连接sftp
            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            String fileName = getFileName(type,item);
            FileUtils.creatDir(tempPath);
            file = FileUtils.createFile(tempPath + File.separator + fileName);
            SftpUtil.downloadNoClose(sftpFilePath + fileName, file, channelSftp);
            response.reset();
            response.setContentType("application/pdf;charset=utf-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
            os.write(org.apache.commons.io.FileUtils.readFileToByteArray(file));
            os.flush();
        }catch (Exception e){
            log.error("{} 下载电子凭证文件异常：", staffVO.getPhone(), e);
            String errMsg = e.getMessage().contains("文件不存在")?"凭证文件不存在":"凭证文件下载异常";
            if(os != null){
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes("utf-8"));
            } else {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("下载电子凭证文件异常");
            }
        }finally {
            FileUtils.deleteDir(new File(tempPath));
            if (channelSftp != null) {
                SftpUtil.sftpClose(channelSftp);
            }
        }
    }

    private String getFileName(Integer type, OrderItem item) {
        if (type.intValue() == ReceiptOrderEnum.BUSINESS_RECEIPT.getValue()){
            return CertificateUtil.getTransferFileName(item.getMainstayName(), item.getEmployerNo(), item.getPlatTrxNo());
        }else{
            if (StringUtils.isBlank(item.getProductNo())
                    || item.getProductNo().equals(ProductNoEnum.ZXH.getValue())
                    || item.getProductNo().equals(ProductNoEnum.CEP.getValue())
                    || (item.getProductNo().equals(ProductNoEnum.CKH.getValue())&&StringUtils.equals(item.getPayChannelNo(),ChannelNoEnum.JOINPAY.name()))){
                return CertificateUtil.getPayFileName(item.getReceiveNameDecrypt(), item.getMainstayNo(), item.getPlatTrxNo());
            }else{
                return CertificateUtil.getCKHPayFileName(item.getReceiveNameDecrypt(), item.getEmployerNo(), item.getPlatTrxNo());
            }
        }
    }

    private File getCKHBill(OrderItem item, String tempPath, ChannelSftp channelSftp, String sftpFilePath) throws IOException {
        String payFileName = CertificateUtil.getCKHPayFileName(item.getReceiveNameDecrypt(),item.getEmployerNo(),item.getPlatTrxNo());
        FileUtils.creatDir(tempPath);
        File payFile = FileUtils.createFile(tempPath + File.separator + payFileName);
        SftpUtil.downloadNoClose(sftpFilePath + payFileName, payFile, channelSftp);
        File zipFile = ZipUtil.zipFile(tempPath);
        return zipFile;
    }

    private File getZXHBill(OrderItem item,String tempPath, ChannelSftp channelSftp, String sftpFilePath) throws IOException {
        String payFileName = CertificateUtil.getPayFileName(item.getReceiveNameDecrypt(), item.getMainstayNo(), item.getPlatTrxNo());
        String transferFileName = CertificateUtil.getTransferFileName(item.getMainstayName(), item.getEmployerNo(), item.getPlatTrxNo());

        FileUtils.creatDir(tempPath);
        File payFile = FileUtils.createFile(tempPath + File.separator + payFileName);

        //微信支付只有payFile
        if (item.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())||item.getPayChannelNo().equals(ChannelNoEnum.CMB.name())){
            SftpUtil.downloadNoClose(sftpFilePath + payFileName, payFile, channelSftp);
        }else{
            File transferFile = FileUtils.createFile(tempPath + File.separator + transferFileName);
            SftpUtil.downloadNoClose(sftpFilePath + payFileName, payFile, channelSftp);
            SftpUtil.downloadNoClose(sftpFilePath + transferFileName, transferFile, channelSftp);
        }

        File zipFile = ZipUtil.zipFile(tempPath);
        return zipFile;
    }

    @GetMapping("/mainstayDownload/viewCertificateFile")
    public void downloadCertificateFile(@RequestParam String platTrxNo, @RequestParam String mainstayNo, HttpServletResponse response) throws Exception{
        if (StringUtils.isBlank(platTrxNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数错误");
        }
        platTrxNo = AESUtil.decryptECB(platTrxNo, EncryptKeys.getEncryptKeyById(100L).getEncryptKeyStr());

        OrderItem item = orderItemFacade.getByPlatTrxNo(platTrxNo);
        if (item == null || item.getOrderItemStatus() != OrderItemStatusEnum.GRANT_SUCCESS.getValue()) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("订单不存在或者状态不为成功");
        }
        if (!StringUtils.equals(item.getMainstayNo(), mainstayNo)) {
            throw CommonExceptions.UNEXPECT_ERROR.newWithErrMsg("非法请求");
        }
        // 文件所在的sftp文件夹
        String tempPath = System.getProperty("user.dir") + File.separator + "download" + File.separator + RandomUtil.get16LenStr();
        ChannelSftp channelSftp = null;
        File zipFile = null;
        OutputStream os = null;
        String sftpFilePath = CertificateUtil.getFilePath(dir, item.getMainstayNo(), item.getPlatTrxNo());
        try {
            os = response.getOutputStream();
            // 连接sftp
            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            if (StringUtils.isBlank(item.getProductNo()) || item.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
                zipFile = getZXHBill(item,tempPath,channelSftp,sftpFilePath);
            }else if (item.getProductNo().equals(ProductNoEnum.CKH.getValue())){
                zipFile = getCKHBill(item,tempPath,channelSftp,sftpFilePath);
            }else{
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("不支持此产品");
            }
            response.reset();
            response.setContentType("application/x-zip-compressed;charset=utf-8");
            String zipFileName = "电子凭证_" + item.getPlatTrxNo() + ".zip";
            zipFileName = new String(zipFileName.getBytes("gb2312"), "ISO8859-1");
            response.addHeader("Content-Disposition", "attachment;filename=" + zipFileName);
            os.write(org.apache.commons.io.FileUtils.readFileToByteArray(zipFile));
            os.flush();
        }catch (Exception e){
            String errMsg = e.getMessage().contains("文件不存在")?"凭证文件不存在":"凭证文件下载异常";
            if(os != null){
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes("utf-8"));
            } else {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("下载电子凭证文件异常");
            }
        }finally {
            FileUtils.deleteDir(new File(tempPath));
            if (zipFile != null) {
                FileUtils.deleteDir(zipFile);
            }
            if (channelSftp != null) {
                SftpUtil.sftpClose(channelSftp);
            }
        }
    }
}
