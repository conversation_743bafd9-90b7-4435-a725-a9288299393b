package com.zhixianghui.web.supplier.config;

import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.web.supplier.dto.JwtInfo;
import com.zhixianghui.web.supplier.exception.NoSelectSupplierException;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @date 2019/10/31
 */
@RestControllerAdvice
@Log4j2
public class GlobalExceptionHandler {

    @ExceptionHandler(NoSelectSupplierException.class)
    public RestResult<String> handleNoSelectMchException(NoSelectSupplierException e) {
        logError(e);
        return RestResult.noSelectMch("请选择供应商");
    }

    /**
     * 处理参数校验异常
     * @param e     异常
     * @return      response
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public RestResult<String> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        logError(e);
        // 错误信息
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError fieldError : e.getBindingResult().getFieldErrors()) {
            errorMsg.append(fieldError.getField())
                    .append(":")
                    .append(fieldError.getDefaultMessage())
                    .append("|");
        }
        if (errorMsg.length() > 0) {
            errorMsg.deleteCharAt(errorMsg.length() - 1);
        }
        return RestResult.error(errorMsg.toString());
    }

    @ExceptionHandler(BindException.class)
    public RestResult<String> handleBindException(BindException e) {
        // 记录日志
        logError(e);

        // 错误信息
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError fieldError : e.getBindingResult().getFieldErrors()) {
            errorMsg.append(fieldError.getDefaultMessage())
                    .append("|");
        }
        if (errorMsg.length() > 0) {
            errorMsg.deleteCharAt(errorMsg.length() - 1);
        }

        return RestResult.error(errorMsg.toString());
    }

    @ExceptionHandler(HttpMessageConversionException.class)
    public RestResult<String> handleHttpMessageConversionException(HttpMessageConversionException e) {
        logError(e);
        return RestResult.error("参数错误");
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public RestResult<String> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        logError(e);
        return RestResult.error("参数错误");
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public RestResult<String> handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        logError(e);
        return RestResult.error("参数缺失");
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public RestResult<String> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        logError(e);
        return RestResult.error("请求出错");
    }

    @ExceptionHandler(BizException.class)
    public RestResult<String> handleBizException(BizException e) {
        logError(e);
        return RestResult.error(e.getSysErrorCode(), e.getErrMsg());
    }

    @ExceptionHandler(Exception.class)
    public RestResult<String> handleException(Exception e) {
        // 记录日志
        logError(e);
        if(e.getMessage() != null && e.getMessage().contains(BizException.class.getName())){
            try{
                String errorMsg = e.getMessage().split("\n")[0].split("errorMsg=")[1];
                return RestResult.error(errorMsg);
            }catch (Exception splitE){
                // 格式解析错误 返回原错误信息
                return RestResult.error("系统异常");
            }
        }
        return RestResult.error("系统异常");
    }

    /**
     * 记录异常信息、登录名、访问路径
     * @param e
     */
    private void logError(Exception e){
        // 获取请求路径
        HttpServletRequest request =((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String requestPath = request.getRequestURI();

        // 获取登录名
        JwtInfo jwtInfo = (JwtInfo) request.getAttribute("jwtInfo");
        String loginName = jwtInfo == null ? null : jwtInfo.getSupplierOperatorVO().getName();

        if (e instanceof BizException) {
            log.error("登录名：{}，访问路径：{}，{}", loginName, requestPath, ((BizException) e).getErrMsg(), e);
        } else {
            log.error("登录名：{}，访问路径：{}", loginName, requestPath, e);
        }
    }
}
