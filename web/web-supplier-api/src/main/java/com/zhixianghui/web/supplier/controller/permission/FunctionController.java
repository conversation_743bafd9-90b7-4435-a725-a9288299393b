package com.zhixianghui.web.supplier.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.service.supplier.SupplierFunctionFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierStaffFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.vo.permission.FunctionVO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能查询
 *
 * <AUTHOR> <PERSON>
 */
@RestController
@RequestMapping("function")
public class FunctionController {

    @Reference
    private SupplierFunctionFacade supplierFunctionFacade;

    @Reference
    private SupplierStaffFacade supplierStaffFacade;

    /**
     * 查询关联的功能
     * @param staffVO
     * @param mchNo
     * @return
     */
    @GetMapping("list")
    public RestResult<List<FunctionVO>> list(@CurrentStaffVo SupplierStaffVO staffVO,
                                             @CurrentMainstayNo String mchNo) {
        if (StringUtil.isEmpty(mchNo) || staffVO == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请选择商户");
        }

        return RestResult.success(supplierStaffFacade.listFunctionByStaffId(mchNo, staffVO.getId()).stream()
                .map(FunctionVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 查询所有功能
     * @return
     */
    @Permission("pms:role:edit")  // 编辑角色的时候需要查询出所有的功能
    @GetMapping("listAll")
    public RestResult<List<FunctionVO>> listAll() {
        return RestResult.success(supplierFunctionFacade.listAll()
                .stream().map(FunctionVO::buildVo).collect(Collectors.toList()));
    }
}
