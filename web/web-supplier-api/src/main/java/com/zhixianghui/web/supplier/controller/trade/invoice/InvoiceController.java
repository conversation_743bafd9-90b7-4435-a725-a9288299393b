package com.zhixianghui.web.supplier.controller.trade.invoice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.dto.InvoiceRecordDetailDto;
import com.zhixianghui.facade.trade.dto.UpdateInvoiceDetailDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.InvoiceRecordDetailFacade;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.facade.trade.vo.InvoiceUpdateVo;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.annotation.Logger;
import com.zhixianghui.web.supplier.utils.BeanToMapUtil;
import com.zhixianghui.web.supplier.vo.trade.req.InvoiceRecordQueryVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 发票业务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("invoice")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class InvoiceController {

    @Reference
    private InvoiceFacade invoiceFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private InvoiceRecordDetailFacade invoiceRecordDetailFacade;

    /**
     * 发票申请记录分页查询
     *
     * @param queryVo 查询vo
     */
    @RequestMapping("listPage")
    @Permission("invoice:view")
    public RestResult<PageResult<List<InvoiceRecord>>> listPage(@RequestBody InvoiceRecordQueryVo queryVo, @CurrentMainstayNo String mainstayNo) {
        Map<String, Object> paramMap = BeanUtil.toMap(queryVo);
        paramMap.put("mainstayMchNo", mainstayNo);
        PageResult<List<InvoiceRecord>> pageResult = invoiceFacade.listPage(paramMap, queryVo.getPageParam());
        return RestResult.success(pageResult);
    }

    /**
     * 根据流水号获取发票申请记录详细信息
     *
     * @param trxNo 发票流水号
     */
    @GetMapping("getByTrxNo")
    @Permission("invoice:view")
    public RestResult<InvoiceRecord> getByTrxNo(@RequestParam String trxNo, @CurrentMainstayNo String mainstayNo) {
        InvoiceRecord record = invoiceFacade.getByTrxNo(trxNo);
        if(!mainstayNo.equals(record.getMainstayMchNo())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("发票记录不存在");
        }
        return RestResult.success(record);
    }

    /**
     * 更新发票记录状态
     */
    @PostMapping("updateStatus")
    @Permission("invoice:updateStatus")
    @Logger(type= OperateLogTypeEnum.MODIFY, action = "更新发票记录状态")
    public RestResult<String> updateStatus(@Valid @RequestBody InvoiceUpdateVo vo, @CurrentMainstayNo String mainstayNo) {
        InvoiceRecord record = invoiceFacade.getByTrxNo(vo.getTrxNo());
        if(!mainstayNo.equals(record.getMainstayMchNo())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("发票记录不存在");
        }
        invoiceFacade.updateStatus(vo);
        return RestResult.success("更新发票状态成功");
    }

    @PostMapping("exportInvoiceRecord")
    @Permission("invoice:view")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "发票申请记录-导出")
    public RestResult<String> exportRechargeRecord(@RequestBody InvoiceRecordQueryVo queryVo, @CurrentMainstayNo String mainstayNo,@CurrentStaffVo SupplierStaffVO staffVo) {
        String startTime =  queryVo.getCreateTimeBegin();
        String endTime = queryVo.getCreateTimeEnd();
        if (StringUtils.isAnyBlank(startTime, endTime)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("请提供起止创建时间");
        }

        validDate(LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        Map<String, Object> paramMap = BeanUtil.toMap(queryVo);
        paramMap.put("mainstayMchNo",mainstayNo);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVo.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.MERCHANT_INVOICE_RECORD_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.MERCHANT_INVOICE_RECORD_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setMchNo(mainstayNo);

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_INVOICE_RECORD_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private void validDate(LocalDateTime createBeginDate, LocalDateTime createEndDate) {
        if(createBeginDate.isAfter(createEndDate)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
        }
    }

    /**
     * 查询普通发票批次详情
     * @param page
     * @param invoiceRecordDetailDto
     * @param mainstayNo
     * @return
     */
    @PostMapping("invoiceRecordDetailPage")
    public RestResult<IPage<InvoiceDetailGroupByIdCardVo>> invoiceRecordDetailPage(@RequestBody Page<InvoiceDetailGroupByIdCardVo> page, @RequestBody InvoiceRecordDetailDto invoiceRecordDetailDto, @CurrentMainstayNo String mainstayNo) {
        if (mainstayNo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未获取到供应商号，请重新登录");
        }
        invoiceRecordDetailDto.setMainstayNo(mainstayNo);
        final IPage<InvoiceDetailGroupByIdCardVo> detailPage = invoiceFacade.listInvoiceDetailGroupByIdCard(page, invoiceRecordDetailDto.getInvoiceTrxNo());

        return RestResult.success(detailPage);
    }

    /**
     * 查询普通发票批次详情
     * @param mainstayNo
     * @return
     */
    @PostMapping("confirmInvoiceRecordDetail")
    public RestResult<String> confirmInvoiceRecordDetail(@RequestBody UpdateInvoiceDetailDto updateInvoiceDetailDto, @CurrentMainstayNo String mainstayNo) {
        if (mainstayNo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未获取到供应商号，请重新登录");
        }
        updateInvoiceDetailDto.setMainstayNo(mainstayNo);
        invoiceRecordDetailFacade.updateInvoiceRecordDetail(updateInvoiceDetailDto);
        return RestResult.success("操作成功");
    }

    @PostMapping("exportInvoceInfo")
    public RestResult<String> exportInvoceInfo(@Valid @RequestBody InvoiceRecordQueryVo queryVo, @CurrentMainstayNo String mainstayNo,@CurrentStaffVo SupplierStaffVO staffVO) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(queryVo);
        paramMap.put("mainstayMchNo", mainstayNo);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(mainstayNo);
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());

        record.setFileName(ReportTypeEnum.MERCHANT_INVOICE_RECORD_INFO_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.MERCHANT_INVOICE_RECORD_INFO_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_INVOICE_RECORD_INFO_EXPORT.getDataName());
        if(dataDictionary == null){
            log.error("细导出字典未配置");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
