package com.zhixianghui.web.supplier.controller.merchant;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AgreementFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AgreementSignerTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.AgreementStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DesensitizeUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.facade.merchant.entity.AgreementSigner;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierOperator;
import com.zhixianghui.facade.merchant.service.AgreementFacade;
import com.zhixianghui.facade.merchant.service.AgreementFileFacade;
import com.zhixianghui.facade.merchant.service.AgreementSignerFacade;
import com.zhixianghui.facade.merchant.vo.AgreementQuerysVo;
import com.zhixianghui.facade.merchant.vo.AgreementResVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentOperatorVO;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.annotation.Logger;
import com.zhixianghui.web.supplier.biz.AgreementBiz;
import com.zhixianghui.web.supplier.utils.BeanToMapUtil;
import com.zhixianghui.web.supplier.vo.PageVo;
import com.zhixianghui.web.supplier.vo.merchant.AgreementReqFileVo;
import com.zhixianghui.web.supplier.vo.merchant.AgreementReqVo;
import com.zhixianghui.web.supplier.vo.merchant.IAgreementArchive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商户管理-协议
 * <AUTHOR>
 * @date 2021-03-25
 **/
@RestController
@RequestMapping("agreement")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementController {

    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private AgreementFacade agreementFacade;
    @Reference
    private AgreementSignerFacade agreementSignerFacade;
    @Reference
    private AgreementFileFacade agreementFileFacade;
    @Autowired
    private AgreementBiz agreementBiz;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 导出归档文件
     * @return
     */
    @PostMapping("exportFile")
    public RestResult exportFile(@RequestBody String idListStr, @CurrentStaffVo SupplierStaffVO staffVO){
        JSONObject json = JSON.parseObject(idListStr);
        JSONArray idArray = json.getJSONArray("idList");
        if(idArray == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("参数idList为空");
        }
        List<Long> idList = idArray.toJavaList(Long.class);

        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("exportFileType", FileTypeEnum.ZIP.getValue());
        if (idList == null || idList.size() < 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请选中所需导出归档文件");
        }
        paramMap.put("agreementIdList",idList);
        paramMap.put("type",AgreementFileTypeEnum.ARCHIVE.getValue());
        //先校验一下是否包含归档文件
        List<Agreement> agreementList = agreementBiz.listBy(paramMap);
        if (agreementList.size() == 0 || agreementList.stream().noneMatch(x->x.getStatus().intValue() == AgreementStatusEnum.FINISHED.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所选数据没有已完成的协议");
        }
        ExportRecord record = ExportRecord.newDefaultInstance();
        String fileNo = exportRecordFacade.genFileNo();
        record.setFileNo(fileNo);
        record.setMchNo(staffVO.getMchNo());
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGREEMENT_SUPPLIER_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.AGREEMENT_SUPPLIER_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 获取签约地址
     * @param id
     * @param mchNo
     * @return
     */
    @GetMapping("getSignUrl/{id}")
    public RestResult getSignUrl(@PathVariable Long id, @CurrentMainstayNo String mchNo){
        //查询是否存在生效的报价单
        Agreement agreement = agreementFacade.getAgreementById(id);
        if (agreement == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("协议不存在");
        }

        List<AgreementSigner> list = agreementSignerFacade.listByAgreementIdWithSignUrl(id);
        if (list == null || list.size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签署人不存在");
        }

        String employerNo = "";
        String signUrl = "";
        for (AgreementSigner agreementSigner : list) {
            if (agreementSigner.getSignerType().intValue() == AgreementSignerTypeEnum.FIRST_PARTY.getValue()){
                employerNo = agreementSigner.getSignerNo();
            }
            if (agreementSigner.getSignerType().intValue() == AgreementSignerTypeEnum.SECOND_PARTY.getValue()){
                if (!agreementSigner.getSignerNo().equals(mchNo)){
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签署人不存在");
                }
                signUrl = agreementSigner.getSignUrl();
            }
        }

        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo,mchNo);
        if(employerMainstayRelation == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("无法签约，此商户报价单未完成审批");
        }

        return RestResult.success(signUrl);
    }

    @PostMapping("getAgreementPage")
    public RestResult getAgreementPage(@RequestBody AgreementQuerysVo agreementQueryVo,@RequestBody(required=false) PageVo pageVo,@CurrentMainstayNo String mainstayNo){
        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(agreementQueryVo);
        List<String> signerNoList = new ArrayList<>();
        signerNoList.add(mainstayNo);
        if (!CollectionUtils.isEmpty(agreementQueryVo.getSignerNoList())){
            signerNoList.addAll(agreementQueryVo.getSignerNoList());
        }
        paramMap.put("signerNoList",signerNoList);
        PageResult<List<Agreement>> pageResult = agreementFacade.listPageByMerchant(paramMap,pageVo.toPageParam());
        List<AgreementResVo> list = pageResult.getData().stream().map(
                agreement -> {
                    AgreementResVo agreementResVo = new AgreementResVo();
                    BeanUtils.copyProperties(agreement,agreementResVo);
                    agreement.setSalerId(null);
                    agreement.setSalerName(null);
                    //查询协议方
                    List<AgreementSigner> signerList = agreementSignerFacade.listByAgreementId(agreement.getId());
                    signerList.stream().forEach(x->x.setSignerPhone(DesensitizeUtil.handleMobile(x.getSignerPhone())));
                    //查询协议文件
                    List<AgreementFile> fileList = agreementFileFacade.listByAgreementId(agreement.getId());
                    agreementResVo.setSignerList(signerList);
                    agreementResVo.setFileList(fileList);
                    return agreementResVo;
                }
        ).collect(Collectors.toList());
        return RestResult.success(PageResult.newInstance(list, pageResult.getPageCurrent(), pageResult.getPageSize(), pageResult.getTotalRecord()));
    }

    /**
     * 查询商户协议列表
     * @param agreementQuerysVo
     * @param pageVo
     * @return
     */
    @Logger(type = OperateLogTypeEnum.QUERY, action = "查询供应商与商户协议")
    @Permission("merchant:agreement:view")
    @PostMapping("listAgreementPage")
    public RestResult<PageResult<List<AgreementResVo>>> listAgreementPage(
            @RequestBody AgreementQuerysVo agreementQuerysVo, @RequestBody(required=false) PageVo pageVo,@CurrentMainstayNo String mainstayNo) {
        PageParam pageParam = pageVo.toPageParam();
        if (mainstayNo == null){
            return RestResult.success(PageResult.newInstance(pageParam,null));
        }
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap = BeanToMapUtil.beanToMap(agreementQuerysVo);
        paramMap.put("mainstayNo",mainstayNo);
        PageResult pageResult = agreementFacade.listCustomPage(paramMap,pageParam);
        return RestResult.success(pageResult);
    }

    /**
     * 根据协议id查询协议
     * @param id
     * @return
     */
    @Logger(type = OperateLogTypeEnum.QUERY,action = "根据协议id查询协议")
    @Permission("merchant:agreement:view")
    @GetMapping("/getAgreementById/{id}")
    public RestResult<AgreementResVo> getAgreementById(@PathVariable("id")Long id,@CurrentMainstayNo String mainstayNo){
        if (!validSupplier(id,mainstayNo)){
            return RestResult.error("没有操作此协议权限");
        }
        AgreementResVo agreementResVo = new AgreementResVo();
        Agreement agreement = agreementFacade.getAgreementById(id);
        BeanUtils.copyProperties(agreement,agreementResVo);
        agreementResVo.setFileList(agreementFileFacade.listByAgreementId(id));
        agreementResVo.setSalerId(null);
        agreementResVo.setSalerName(null);
        return RestResult.success(agreementResVo);
    }

    @Logger(type = OperateLogTypeEnum.MODIFY, action = "归档协议")
    @PostMapping("archiveAgreement")
    public RestResult<String> archiveAgreement(@Validated @RequestBody AgreementReqVo agreementReqVo, @CurrentStaffVo SupplierStaffVO staffVO){
        agreementBiz.archiveAgreement(agreementReqVo.getId(), agreementReqVo.getFileVoList(), staffVO.getName());
        return RestResult.success("归档成功");
    }

//    /**
//     * 根据供应商发起协议归档
//     * @param agreementReqFileVo
//     * @param supplierOperator
//     * @return
//     */
//    @Logger(type = OperateLogTypeEnum.MODIFY,action = "供应商发起协议归档")
//    @Permission("merchant:agreement:edit")
//    @PostMapping("/archiveAgreement")
//    public RestResult<String> archiveAgreement(@Validated(IAgreementArchive.class) @RequestBody AgreementReqFileVo agreementReqFileVo,@CurrentMainstayNo String mainstayNo,@CurrentOperatorVO SupplierOperatorVO supplierOperator){
//        if (!validSupplier(agreementReqFileVo.getId(),mainstayNo)){
//            return RestResult.error("没有操作此协议权限");
//        }
//        agreementBiz.archiveAgreement(agreementReqFileVo.getId(), agreementReqFileVo.getFileVoList(), supplierOperator);
//        return RestResult.success("归档成功");
//    }

    /**
     * 下载协议归档文件
     * @param id 协议id
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.QUERY, action = "下载协议归档文件")
    @Permission("merchant:agreement:view")
    @PostMapping("downloadArchiveFile")
    public RestResult<String> downloadArchiveFile(@RequestParam Long id,@CurrentMainstayNo String mainstayNo,@CurrentOperatorVO SupplierOperatorVO supplierOperator){
        //验证供应商是否拥有权限
        if(!validSupplier(id,mainstayNo)){
            return RestResult.error("没有操作此协议权限");
        }
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(supplierOperator.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.AGREEMENT_ARCHIVE_FILE.getFileName());
        record.setReportType(ReportTypeEnum.AGREEMENT_ARCHIVE_FILE.getValue());
        //供应商后台编码
        record.setMchNo(mainstayNo);
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("agreementId",id);
        paramMap.put("type", AgreementFileTypeEnum.ARCHIVE.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 验证供应商是否与协议相关
     * @return
     */
    private Boolean validSupplier(Long id,String mainstayNo){
        //根据协议id获取签署商户
        if (id == null || StringUtil.isEmpty(mainstayNo)){
            return false;
        }
        List<String> mchNoList = agreementSignerFacade.listByAgreementId(id).stream().
                map(AgreementSigner::getSignerNo).collect(Collectors.toList());
        if (mchNoList == null || mchNoList.size() <= 0){
            return false;
        }
       return mchNoList.contains(mainstayNo);
    }

}
