package com.zhixianghui.web.supplier.biz.offline;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.web.supplier.utils.BeanToMapUtil;
import com.zhixianghui.web.supplier.vo.PageVo;
import com.zhixianghui.web.supplier.vo.trade.req.OrderItemQueryVo;
import com.zhixianghui.web.supplier.vo.trade.res.OrderItemResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 订单明细
 * @date 2020-11-04 15:13
 **/
@Service
@Slf4j
public class OfflineOrderItemBiz {

    @Reference
    private OfflineOrderItemFacade orderItemFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;


    public PageResult<List<OrderItemResVo>> listOrderItemPage(OrderItemQueryVo orderItemQueryVo, PageVo pageVo, String mainstayNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OfflineOrderItem>> pageResult = orderItemFacade.listPage(paramMap,pageParam);
        List<OrderItemResVo> orderItemResVoList = pageResult.getData().stream().map(
                orderItem ->{
                    OrderItemResVo orderItemResVo = new OrderItemResVo();
                    BeanUtils.copyProperties(orderItem,orderItemResVo);
                    orderItemResVo.setReceiveName(orderItem.getReceiveNameDesensitize());
                    orderItemResVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDesensitize());
                    orderItemResVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDesensitize());
                    orderItemResVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDesensitize());
                    orderItemResVo.setWorkerBillFilePath(orderItem.getWorkerBillFilePath());
                    return orderItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public Long countOrderItem(OrderItemQueryVo orderItemQueryVo,String mainstayNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        return orderItemFacade.countOrderItem(paramMap);
    }

    public OrderItemSumBo sumOrderItem(OrderItemQueryVo orderItemQueryVo,String mainstayNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        return orderItemFacade.sumOrderItem(paramMap);
    }
}
