package com.zhixianghui.web.supplier.controller.trade.order;

import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.vo.FeeOrderVo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月12日 10:41:00
 */
@RestController
@RequestMapping("/feeOrder")
public class FeeOrderController {

    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;


    @PostMapping("/listPage")
    public RestResult listPage(@RequestBody FeeOrderVo feeOrderVo, @RequestBody PageParam pageParam, @CurrentStaffVo SupplierStaffVO staffVo) {
        feeOrderVo.setMainstayNo(staffVo.getMchNo());
        return RestResult.success(feeOrderBatchFacade.listPage(feeOrderVo, pageParam));
    }

    @PostMapping("/getStatistics")
    public RestResult getStatistics(@RequestBody FeeOrderVo feeOrderVo, @CurrentStaffVo SupplierStaffVO staffVo) {
        feeOrderVo.setMainstayNo(staffVo.getMchNo());
        return RestResult.success(feeOrderBatchFacade.getStatistics(feeOrderVo));
    }

}