package com.zhixianghui.web.supplier.vo.permission;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 员工修改VO
 *
 * <AUTHOR>
 */
@Data
public class EditStaffVO {

    /**
     * 员工id
     */
    @NotNull(message = "员工id不能为空")
    private Long id;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 角色id
     */
    private List<Long> roleIds;

    /**
     * 支付密码
     */
    @NotEmpty(message = "支付密码不能为空")
    private String tradePwd;
}
