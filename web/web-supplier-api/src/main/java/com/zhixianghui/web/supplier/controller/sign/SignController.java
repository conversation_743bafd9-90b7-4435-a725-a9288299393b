package com.zhixianghui.web.supplier.controller.sign;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.biz.sign.SignBiz;
import com.zhixianghui.web.supplier.vo.PageVo;
import com.zhixianghui.web.supplier.vo.sign.SignRecordQueryVo;
import com.zhixianghui.web.supplier.vo.sign.SignRecordResVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description 电子签约
 * @date 2021/1/18 15:34
 **/
@RestController
@RequestMapping("sign")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SignController {

    private final SignBiz signBiz;

    @Permission("sign:signRecord:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<SignRecordResVo>>> listPage(@RequestBody SignRecordQueryVo queryVo, @RequestBody PageVo pageVo, @CurrentMainstayNo String mainstayNo) {
        PageResult<List<SignRecordResVo>> pageResult = signBiz.listPage(queryVo,pageVo.toPageParam(),mainstayNo);
        return RestResult.success(pageResult);
    }

    /**
     * 导出电子签约
     */
    @Permission("sign:signRecord:export")
    @PostMapping("exportSignRecord")
    public RestResult<String> exportSignRecord(@RequestBody SignRecordQueryVo queryVo, @CurrentStaffVo SupplierStaffVO staffVO) {
        signBiz.exportSignRecord(queryVo, staffVO);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
