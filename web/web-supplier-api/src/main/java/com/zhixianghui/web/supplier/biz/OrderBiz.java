package com.zhixianghui.web.supplier.biz;

import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.ReceiptOrderEnum;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.web.supplier.utils.BeanToMapUtil;
import com.zhixianghui.web.supplier.vo.trade.req.OrderItemQueryVo;
import com.zhixianghui.web.supplier.vo.trade.req.OrderQueryVo;
import com.zhixianghui.web.supplier.vo.trade.res.OrderItemResVo;
import com.zhixianghui.web.supplier.vo.trade.res.OrderResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-15 10:00
 **/
@Slf4j
@Service
public class OrderBiz {

    @Reference
    private OrderFacade orderFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    public PageResult<List<OrderResVo>> listOrderPage(OrderQueryVo orderQueryVo, String mainstayNo, PageParam pageParam) {
        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(orderQueryVo);
        paramMap.put("mainstayNo",mainstayNo);
        PageResult<List<Order>> pageResult = orderFacade.listPage(paramMap,pageParam);
        List<OrderResVo> orderResVoList = pageResult.getData().stream().map(
                order -> {
                    OrderResVo orderResVo = new OrderResVo();
                    BeanUtils.copyProperties(order,orderResVo);
                    //TODO 批次显示任务金额，由于数据无法修改，增加判断
                    if (StringUtils.isBlank(order.getProductNo()) ||
                            order.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
                        orderResVo.setRequestTaskAmount(order.getRequestNetAmount());
                        orderResVo.setAcceptedTaskAmount(order.getAcceptedNetAmount());
                    }
                    return orderResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public PageResult<List<OrderItemResVo>> listOrderItemPage(OrderItemQueryVo orderItemQueryVo, String mainstayNo, PageParam pageParam) {
        Map<String,Object> paramMap = BeanToMapUtil.beanToMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo, paramMap);
        paramMap.put("mainstayNo",mainstayNo);
        PageResult<List<OrderItem>> pageResult = orderItemFacade.listPage(paramMap,pageParam);
        List<OrderItemResVo> orderItemResVoList = pageResult.getData().stream().map(
                orderItem -> {
                    OrderItemResVo orderItemResVo = new OrderItemResVo();
                    BeanUtils.copyProperties(orderItem,orderItemResVo);
                    orderItemResVo.setReceiveName(orderItem.getReceiveNameDesensitize());
                    orderItemResVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDesensitize());
                    orderItemResVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDesensitize());
                    orderItemResVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDesensitize());
                    orderItemResVo.setChannelNo(orderItem.getPayChannelNo());
                    return orderItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    private void handlerAmount(OrderItemQueryVo orderItemQueryVo, Map<String, Object> paramMap) {
        String amountMaxStr = orderItemQueryVo.getOrderItemNetAmountMax();
        String amountMinStr = orderItemQueryVo.getOrderItemNetAmountMin();
        if (StringUtil.isNotEmpty(amountMaxStr) && !ValidateUtil.isDoubleAnd2decimals(amountMaxStr)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最大金额必须是数字，且最多两位小数");
        }else {
            paramMap.remove("orderItemNetAmountMax");
        }
        if (StringUtil.isNotEmpty(amountMinStr) && !ValidateUtil.isDoubleAnd2decimals(amountMinStr)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最小金额必须是数字，且最多两位小数");
        }else {
            paramMap.remove("orderItemNetAmountMin");
        }

        if(StringUtil.isNotEmpty(amountMaxStr) && StringUtil.isNotEmpty(amountMinStr)){
            BigDecimal amountMax = new BigDecimal(amountMaxStr);
            BigDecimal amountMin = new BigDecimal(amountMinStr);
            if (amountMax.compareTo(amountMin) < 0) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("最大金额不能小于最小金额");
            }
            paramMap.put("orderItemNetAmountMax", amountMax);
            paramMap.put("orderItemNetAmountMin", amountMin);
        }
    }

    public void exportOrderItem(OrderItemQueryVo orderItemQueryVo, SupplierStaffVO staffVo, String mainstayNo) {
        Map<String, Object> paramMap = BeanUtil.toMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo,paramMap);
        paramMap.put("mainstayNo",mainstayNo);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVo.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.TRADE_ORDER_ITEM_SUP.getFileName());
        record.setReportType(ReportTypeEnum.TRADE_ORDER_ITEM_SUP.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setMchNo(mainstayNo);
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.TRADE_ORDER_ITEM_SUP.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }

    public Long countOrder(OrderQueryVo orderQueryVo, String mainstayNo) {
        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(orderQueryVo);
        paramMap.put("mainstayNo",mainstayNo);
        return orderFacade.countOrder(paramMap);
    }

    public Long countOrderItem(OrderItemQueryVo orderItemQueryVo, String mainstayNo) {
        Map<String,Object> paramMap = BeanToMapUtil.beanToMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo, paramMap);
        paramMap.put("mainstayNo",mainstayNo);
        return orderItemFacade.countOrderItem(paramMap);
    }

    public OrderItemSumBo sumOrderItem(OrderItemQueryVo orderItemQueryVo, String mainstayNo) {
        Map<String,Object> paramMap = BeanToMapUtil.beanToMap(orderItemQueryVo);
        handlerAmount(orderItemQueryVo, paramMap);
        paramMap.put("mainstayNo",mainstayNo);
        return orderItemFacade.sumOrderItem(paramMap);
    }

    public WithdrawRecord withdraw(WithdrawDto withdrawDto) {
        return orderFacade.Withdraw(withdrawDto);
    }

    public void exportReceipt(Integer type, OrderItemQueryVo orderItemQueryVo, SupplierStaffVO staffVO,String mainstayNo) {
        ReportTypeEnum reportTypeEnum = ReceiptOrderEnum.getEnum(type).getReportTypeEnum();
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(mainstayNo);
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(reportTypeEnum.getDataName());
        if(dataDictionary == null){
            log.error("供应商后台转账回单导出字典未配置");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
    }
}
