package com.zhixianghui.web.supplier.vo.merchant;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zhixianghui.web.supplier.vo.PageVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/27 14:35
 */
@Data
public class DataAnalyzeVo extends PageVo implements Serializable {

    private static final long serialVersionUID = 685183002837823214L;
    private String beginDate;
    private String endDate;
    private String currentDate;
    private String mainstayNo;
    private String mainstayName;
    private String employerNo;
    private String employerName;
    private String receiveName;
    private String idCard;
    private String receiveIdCardNoMd5;
    private String receiveNameMd5;
    private String phone;
    private Long signId;
    private Long id;
    private String sortColumn;
    private String order;
    /**
     * @see com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum
     */
    private Integer idCardType;
    private String idCardBackUrl;
    private String idCardFrontUrl;
    /**
     * 身份证复印件url
     */
    private String idCardCopyFileUrl;
    private String cerFaceUrl;
    private String bankCardNumber;

    /**
     * 是否上传身份证 1上传,0没有
     */
    private Integer hasUploadIdCard;

    /**
     * 是否签约 1签约,0没签约
     */
    private Integer hasSign;

    /**
     *  id列表
     */
    private List<String> idList;

    /**
     * 金额是否大于2.7万
     */
    private Integer amountLimit;

    private BigDecimal amountMin = new BigDecimal("27000");

}
