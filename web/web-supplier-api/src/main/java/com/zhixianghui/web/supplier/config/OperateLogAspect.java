package com.zhixianghui.web.supplier.config;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperateLog;
import com.zhixianghui.facade.merchant.enums.LogStatusEnum;
import com.zhixianghui.facade.merchant.service.pms.PmsOperateLogFacade;
import com.zhixianghui.web.supplier.annotation.Logger;
import com.zhixianghui.web.supplier.component.JwtHelper;
import com.zhixianghui.web.supplier.dto.JwtInfo;
import com.zhixianghui.web.supplier.utils.NetUtil;
import com.zhixianghui.web.supplier.vo.user.LoginReqVO;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Date;

@Aspect
@Component
@Log4j2
public class OperateLogAspect {

    @Reference
    private PmsOperateLogFacade operateLogFacade;

    @Autowired
    private JwtHelper jwtHelper;

    @Pointcut("@annotation(com.zhixianghui.web.supplier.annotation.Logger)")
    public void saveLog() {}

    @Around("saveLog()")
    public Object saveLog(ProceedingJoinPoint point) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Logger loggerAnnotation = method.getAnnotation(Logger.class);

        String action = loggerAnnotation == null ? "":loggerAnnotation.action();

        // 记录操作日志
        PmsOperateLog operateLog = new PmsOperateLog();
        operateLog.setCreateTime(new Date());
        operateLog.setOperateType(loggerAnnotation.type().getValue());
        operateLog.setContent(action);
        operateLog.setOperateSource(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());

        // 操作员姓名
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        JwtInfo jwtInfo = jwtHelper.verifyToken(request);
        LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
        String[] paramNames = u.getParameterNames(method);
        //此时未登录，则抽取登录用户名
        String loginName = "";
        if (jwtInfo == null) {
            //参数拼装
            Object[] args = point.getArgs();
            if (args != null) {
                for (int i = 0; i < args.length; i++) {
                    try {
                        if (args[i] instanceof JSONObject) {
                            loginName = ((JSONObject) args[i]).getString("phone");
                        } else if (args[i] instanceof LoginReqVO) {
                            loginName = ((LoginReqVO) args[i]).getPhone();
                        } else {
                            if ("phone".equals(paramNames[i])) {
                                loginName = (String) args[i];
                            }
                        }
                    }catch (Exception e){log.error(e);}
                }
            }
        }
        loginName = jwtInfo == null ? loginName : jwtInfo.getSupplierOperatorVO().getName();
        operateLog.setOperatorLoginName(StringUtil.isEmpty(loginName) ? "匿名未登录" : loginName);
        operateLog.setIp(NetUtil.getIpAddr(request));
        operateLog.setDetail(request.getHeader("User-Agent"));
        Object res = null;
        Throwable throwable = null;
        try {
            res = point.proceed();

            if (res instanceof RestResult) {
                operateLog.setStatus(((RestResult) res).getCode() == RestResult.SUCCESS ? LogStatusEnum.SUCCESS.getCode() : LogStatusEnum.FAIL.getCode());
            } else {
                operateLog.setStatus(LogStatusEnum.SUCCESS.getCode());
            }
        } catch (Throwable e) {
            operateLog.setStatus(LogStatusEnum.FAIL.getCode());
            throwable = e;
        } finally {
            try {
                operateLogFacade.createOperateLog(operateLog);
            } catch (Throwable ignored) {

            }
        }

        if (throwable != null) {
            throw throwable;
        } else {
            return res;
        }
    }
}
