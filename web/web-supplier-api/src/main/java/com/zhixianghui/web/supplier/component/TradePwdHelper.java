package com.zhixianghui.web.supplier.component;

import com.zhixianghui.common.statics.enums.user.supplier.SupplierInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.supplier.SupplierOperatorStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierTradePwd;
import com.zhixianghui.facade.merchant.service.supplier.SupplierTradePwdFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.constant.PermissionConstant;
import com.zhixianghui.web.supplier.utils.PwdDigestUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 商户支付密码
 *
 * <AUTHOR> Guangsheng
 */
@Component
@Log4j2
public class TradePwdHelper {

    @Reference
    private SupplierTradePwdFacade supplierTradePwdFacade;

    /**
     * 查询商户是否设置了支付密码
     * @param mchNo
     * @return
     */
    public boolean isInitTradePwd(String mchNo) {
        SupplierTradePwd mchTradePwd = supplierTradePwdFacade.getByMchNo(mchNo);
        return mchTradePwd != null && mchTradePwd.getIsInitPwd() == SupplierInitPwdStatusEnum.INITED.getValue();
    }

    /**
     * 修改支付密码
     */
    public void changeTradePwd(String mchNo, String tradePwd) {
        // 密码复杂度验证
        if(!ValidateUtil.isInteger(tradePwd) ||  tradePwd.length() != 6){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("支付密码必须为6位数字");
        }

        // 密码摘要
        PwdDigestUtil.DigestResult digestResult = PwdDigestUtil.digestFixSaltPwd(tradePwd);

        SupplierTradePwd mchTradePwd = supplierTradePwdFacade.getByMchNo(mchNo);
        if (mchTradePwd == null) {  // 没有支付密码记录则直接创建
            mchTradePwd = new SupplierTradePwd();
            mchTradePwd.setCreateTime(new Date());
            mchTradePwd.setMchNo(mchNo);
            mchTradePwd.setStatus(SupplierOperatorStatusEnum.ACTIVE.getValue());
            mchTradePwd.setIsInitPwd(SupplierInitPwdStatusEnum.INITED.getValue());
            mchTradePwd.setPwd(digestResult.getPwd());
            mchTradePwd.setPwdErrorCount(0);
            mchTradePwd.setPwdErrorTime(null);
            mchTradePwd.getJsonEntity().getHistoryPwdList().add(digestResult.getPwd());
            supplierTradePwdFacade.create(mchTradePwd);
        } else {
            mchTradePwd.setUpdateTime(new Date());
            mchTradePwd.setStatus(SupplierOperatorStatusEnum.ACTIVE.getValue());
            mchTradePwd.setIsInitPwd(SupplierInitPwdStatusEnum.INITED.getValue());
            mchTradePwd.setPwd(digestResult.getPwd());

            // 不能与历史密码重复
            List<String> historyPwdList = mchTradePwd.getJsonEntity().getHistoryPwdList();
            if(historyPwdList.contains(digestResult.getPwd())){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("密码不能与前4次重复");
            }

            // 只存储最近4次密码密文
            if(historyPwdList.size() >= PermissionConstant.MAX_HISTORY_PWD_NUM){
                historyPwdList.add(digestResult.getPwd());
                mchTradePwd.getJsonEntity().setHistoryPwdList(historyPwdList.subList(historyPwdList.size() - PermissionConstant.MAX_HISTORY_PWD_NUM, historyPwdList.size()));
            } else {
                mchTradePwd.getJsonEntity().getHistoryPwdList().add(digestResult.getPwd());
            }

            mchTradePwd.setPwdErrorCount(0);
            supplierTradePwdFacade.update(mchTradePwd);
        }
    }

    /**
     * 验证支付密码
     */
    public void verifyTradePwd(SupplierStaffVO staffVO, String mchNo, String tradePwd) throws BizException {
        SupplierTradePwd mchTradePwd = supplierTradePwdFacade.getByMchNo(mchNo);
        if (mchTradePwd == null || mchTradePwd.getIsInitPwd() == SupplierInitPwdStatusEnum.NO_INIT.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请先设置支付密码");
        }
        if (mchTradePwd.getStatus() == SupplierOperatorStatusEnum.INACTIVE.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付密码已被冻结，请先修改");
        }

        // 验证支付密码
        if (PwdDigestUtil.verifyFixSaltPwd(mchTradePwd.getPwd(), tradePwd)) {
            // 重置错误次数
            mchTradePwd.setPwdErrorCount(0);
            supplierTradePwdFacade.update(mchTradePwd);
        } else {
            // 密码错误
            log.warn("支付密码输入错误，操作员：{}-{}，商户：{}", staffVO.getPhone(), staffVO.getName(), mchNo);
            // 错误次数加1
            mchTradePwd.setPwdErrorCount(mchTradePwd.getPwdErrorCount() + 1);
            mchTradePwd.setPwdErrorTime(new Date());
            // 超过密码输错次数限制，冻结
            String errMsg;
            if (mchTradePwd.getPwdErrorCount() >= PermissionConstant.TRADE_PWD_ERROR_LIMIT) {
                mchTradePwd.setStatus(SupplierOperatorStatusEnum.INACTIVE.getValue());
                errMsg = "支付密码已连续输错【" + PermissionConstant.TRADE_PWD_ERROR_LIMIT + "】次，已被冻结";
            } else {
                errMsg = "支付密码错误，剩余【" + (PermissionConstant.TRADE_PWD_ERROR_LIMIT - mchTradePwd.getPwdErrorCount()) + "】次机会";
            }
            supplierTradePwdFacade.update(mchTradePwd);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(errMsg);
        }
    }
}
