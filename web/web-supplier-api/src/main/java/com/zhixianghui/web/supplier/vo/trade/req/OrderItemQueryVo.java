package com.zhixianghui.web.supplier.vo.trade.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-09 14:28
 **/
@Data
public class OrderItemQueryVo {

    /**
     * 订单明细状态
     */
    private List<Integer> orderItemStatusList;

    /**
     * 订单明细状态
     */
    private Integer orderItemStatus;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 商户名称模糊
     */
    private String employerNameLike;

    /**
     * 商户名称
     */
    private String employerName;

    /**
     * 明细实发金额最小值
     */
    private String orderItemNetAmountMin;

    /**
     * 明细实发金额最大值
     */
    private String orderItemNetAmountMax;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 收款账户
     */
    private String receiveAccountNo;

    /**
     * 银行预留手机号
     */
    private String receivePhoneNo;

    /**
     * 创建起始时间
     */
    @NotNull(message = "创建起始时间不能为空")
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    @NotNull(message = "创建截止时间不能为空")
    private Date createEndDate;

    /**
     * 完成起始时间
     */
    private Date completeBeginDate;

    /**
     * 完成截止时间
     */
    private Date completeEndDate;
}
