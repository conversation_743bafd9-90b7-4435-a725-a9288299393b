package com.zhixianghui.web.supplier.component;

import com.zhixianghui.common.statics.dto.message.EmailParamDto;
import com.zhixianghui.common.statics.enums.common.RedisKeyPrefixEnum;
import com.zhixianghui.common.statics.enums.message.EmailFromEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.message.EmailFacade;
import com.zhixianghui.facade.banklink.service.message.SmsFacade;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.supplier.utils.CaptchaUtil;
import com.zhixianghui.web.supplier.vo.user.CaptchaVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.UUID;

import static com.zhixianghui.web.supplier.constant.PermissionConstant.*;

/**
 * 验证码校验
 *
 * <AUTHOR> Guangsheng
 */
@Component
@Log4j2
public class CaptchaHelper {

    /**
     * 验证码短信模板
     */
    private static final String smsTemplate = "【汇聚智享】您的动态验证码为%s，请在页面输入完成验证。如非本人操作请忽略。";

    @Autowired
    private RedisClient redisClient;

    @Reference
    private SmsFacade smsFacade;

    @Reference
    private EmailFacade emailFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 生成图形验证码
     */
    public CaptchaVO genCaptcha() throws BizException {
        final ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            String captcha = CaptchaUtil.genCaptcha(4);
            BufferedImage image = CaptchaUtil.outputImage(200, 80, captcha);
            ImageIO.write(image, "jpg", os);

            CaptchaVO captchaVO = new CaptchaVO();
            captchaVO.setId(UUID.randomUUID().toString());
            captchaVO.setImg(Base64.getEncoder().encodeToString(os.toByteArray()));
            redisClient.set(RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + CAPTCHA_KEY + ":" + captchaVO.getId(),
                    captcha, CAPTCHA_EXPIRE_TIME);
            return captchaVO;
        } catch (Exception e) {
            log.error("生成图形验证码异常, {}", e.getMessage(), e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("系统异常");
        }
    }

    /**
     * 校验图形验证码，验证码只能使用一次，校验成功后会置为失效
     * @param captchaId
     * @param captcha
     */
    public void verifyCaptcha(String captchaId, String captcha) throws BizException {
        final String openCaptcha = dataDictionaryFacade.getSystemConfig("OPEN_CAPTCHA");
        if (openCaptcha != null && StringUtils.equals(captcha,openCaptcha)) {
            return;
        }
        String key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + CAPTCHA_KEY + ":" + captchaId;
        String value = redisClient.get(key);
        if (StringUtil.isEmpty(value)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("图形验证码失效");
        }
        if (!captcha.equalsIgnoreCase(value)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("图形验证码错误");
        }

        // 验证码校验通过，置为失效，防止重复使用
        redisClient.del(key);
    }

    /**
     * 将图形验证码置为失效
     * @param captchaId
     */
    public void invalidCaptcha(String captchaId) {
        String key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + CAPTCHA_KEY + ":" + captchaId;
        redisClient.del(key);
    }

    /**
     * 发送短信验证码
     * @param phone
     */
    public void sendSmsCode(String phone) throws BizException {
        String key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + SMS_CODE_KEY + ":" + phone;
        // 重发间隔校验
        String value = redisClient.get(key);
        String[] codeAndSendTime;
        if (StringUtil.isNotEmpty(value) && (codeAndSendTime = value.split("-")).length >= 2) {
            long sendTime = Long.parseLong(codeAndSendTime[1]);
            int diff = (int)(System.currentTimeMillis() - sendTime) / 1000;
            if (diff < SMS_CODE_RESEND_TIME) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发送频繁，请于" + (SMS_CODE_RESEND_TIME - diff) + "秒后重试");
            }
        }

        String smsCode = RandomUtil.getDigitStr(6);  // 生成6位随机验证码
        // 短信发送
        smsFacade.send(phone, genSmsMsg(smsCode), "");

        value = smsCode + "-" + System.currentTimeMillis();  // value格式为 "验证码-发送时间"
        log.info("key"+key+"value{}:"+value);
        redisClient.set(key, value, SMS_CODE_EXPIRE_TIME);
    }

    /**
     * 校验短信验证码
     * @param phone
     * @param code
     */
    public void verifySmsCode(String phone, String code) throws BizException {
        // 请求频率拦截，2秒内只能请求一次
        String key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + VERIFY_SMS_CODE_KEY + ":" + phone;
        String value = redisClient.get(key);
        if (StringUtil.isNotEmpty(value)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求频率过快");
        }
        redisClient.set(key, Long.toString(System.currentTimeMillis()), 2);

        // 短信验证码校验
        key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + SMS_CODE_KEY + ":" + phone;
        value = redisClient.get(key);
        String[] codeAndSendTime;
        if (StringUtil.isEmpty(value) || (codeAndSendTime = value.split("-")).length < 2) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("短信验证码已失效");
        }
        String smsCode = codeAndSendTime[0];
        if (!code.equals(smsCode)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("短信验证码错误");
        }
    }

    /**
     * 将短信验证码置为失效
     * @param phone
     */
    public void invalidSmsCode(String phone) {
        String key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + SMS_CODE_KEY + ":" + phone;
        redisClient.del(key);
    }

    /**
     * 发送邮箱验证码
     * @param email
     */
    public void sendEmailCode(String email) throws BizException {
        String key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + EMAIL_CODE_KEY + ":" + email;
        // 重发间隔校验
        String value = redisClient.get(key);
        String[] codeAndSendTime;
        if (StringUtil.isNotEmpty(value) && (codeAndSendTime = value.split("-")).length >= 2) {
            long sendTime = Long.parseLong(codeAndSendTime[1]);
            int diff = (int)(System.currentTimeMillis() - sendTime) / 1000;
            if (diff < EMAIL_CODE_RESEND_TIME) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发送频繁，请于" + (EMAIL_CODE_RESEND_TIME - diff) + "秒后重试");
            }
        }

        String emailCode = RandomUtil.getDigitStr(6);  // 生成6位随机验证码
        // 邮件发送
        EmailParamDto emailParamDto = new EmailParamDto();
        emailParamDto.setFrom(EmailFromEnum.ALIYUN_JOINPAY);
        emailParamDto.setTo(email);
        emailParamDto.setHtmlFormat(false);
        emailParamDto.setSubject("【汇聚智享】邮箱验证码");
        emailParamDto.setContent("验证码为：" + emailCode);
        emailFacade.sendAsync(emailParamDto);

        value = emailCode + "-" + System.currentTimeMillis();  // value格式为 "验证码-发送时间"
        redisClient.set(key, value, SMS_CODE_EXPIRE_TIME);
    }

    /**
     * 校验邮箱验证码
     * @param email
     * @param code
     */
    public void verifyEmailCode(String email, String code) throws BizException {
        // 请求频率拦截，2秒内只能请求一次
        String key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + VERIFY_EMAIL_CODE_KEY + ":" + email;
        String value = redisClient.get(key);
        if (StringUtil.isNotEmpty(value)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求频率过快");
        }
        redisClient.set(key, Long.toString(System.currentTimeMillis()), 2);

        // 邮箱验证码校验
        key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + EMAIL_CODE_KEY + ":" + email;
        value = redisClient.get(key);
        String[] codeAndSendTime;
        if (StringUtil.isEmpty(value) || (codeAndSendTime = value.split("-")).length < 2) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("邮箱验证码已失效");
        }
        String smsCode = codeAndSendTime[0];
        if (!code.equals(smsCode)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("邮箱验证码错误");
        }

        // 验证码校验通过，置为失效，防止重复使用
        redisClient.del(key);
    }

    /**
     * 将邮箱验证码置为失效
     * @param email
     */
    public void invalidEmailCode(String email) {
        String key = RedisKeyPrefixEnum.WEB_SUPPLIER_SESSION_KEY.name() + ":" + EMAIL_CODE_KEY + ":" + email;
        redisClient.del(key);
    }

    /**
     * 短信验证码发送内容
     * @param smsCode
     * @return
     */
    public static String genSmsMsg(String smsCode) {
        return String.format(smsTemplate, smsCode);
    }
}
