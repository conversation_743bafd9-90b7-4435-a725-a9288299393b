package com.zhixianghui.web.supplier.component;

import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.supplier.dto.Session;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Session管理
 *
 * <AUTHOR> <PERSON>
 */
@Component
@Log4j2
public class SessionManager {

    @Autowired
    private RedisClient redisClient;

    public Session createSession(String phone) {
        return new Session(redisClient, phone);
    }

    public void save(Session session) {
        session.save();
    }

    public Session getSession(String phone, String uuid) {
        if (StringUtil.isEmpty(phone)) return null;

        Session session = Session.getByPhone(redisClient, phone);
        if (session.getUuid().equals(uuid))  {
            return session;
        } else {
            return null;
        }
    }

    public void deleteSession(String phone, String uuid) {
        Session session = getSession(phone, uuid);
        if (session != null) {
            Session.deleteByPhone(redisClient, phone);
        }
    }
}
