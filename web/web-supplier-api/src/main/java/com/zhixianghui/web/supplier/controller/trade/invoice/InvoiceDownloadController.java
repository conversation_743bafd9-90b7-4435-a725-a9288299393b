package com.zhixianghui.web.supplier.controller.trade.invoice;

import cn.hutool.core.io.IoUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.ZipUtil;
import com.zhixianghui.facade.trade.dto.InvoiceRecordDetailDto;
import com.zhixianghui.facade.trade.entity.*;
import com.zhixianghui.facade.trade.enums.InvoiceSourceEnum;
import com.zhixianghui.facade.trade.service.*;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.supplier.biz.excel.ExportBiz;
import com.zhixianghui.web.supplier.vo.invoice.ExportDetailGroupVo;
import com.zhixianghui.web.supplier.vo.invoice.ExportDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class InvoiceDownloadController {
    public static final String ID_CARD_COPY = "-身份证复印件";
    public static final String ID_CARD_BACK = "-身份证背面照";
    public static final String ID_CARD_FRONT = "-身份证正面照";
    @Autowired
    private ExportBiz exportBiz;

    @Reference
    private InvoiceRecordDetailFacade invoiceRecordDetailFacade;
    @Reference
    private InvoiceFacade invoiceFacade;

    @Reference
    private UserInfoFacade userInfoFacade;

    @Reference
    private OrderItemFacade orderItemFacade;

    @Reference
    private OfflineOrderItemFacade offlineOrderItemFacade;

    @Autowired
    private FastdfsClient fastdfsClient;

    @GetMapping("download/exportInvoiceDetail")
    public void exportInvoiceDetail(InvoiceRecordDetailDto invoiceRecordDetailDto, HttpServletResponse servletResponse) {

        final InvoiceRecord invoiceRecord = invoiceFacade.getByTrxNo(invoiceRecordDetailDto.getInvoiceTrxNo());

        if (invoiceRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发票记录不存在");
        }

        int current = 1;
        int size = 200;
        IPage<InvoiceRecordDetail> detailPage = null;
        List<ExportDetailVo> allData = new LinkedList<>();
        if (invoiceRecord.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
            do {
                Page<InvoiceRecordDetail> page = new Page<>(current, size);
                detailPage = invoiceRecordDetailFacade.invoiceRecordDetailPage(page, invoiceRecordDetailDto);
                if (detailPage != null && detailPage.getRecords() != null && !detailPage.getRecords().isEmpty()) {
                    allData.addAll(detailPage.getRecords().stream().map(it->{
                        OrderItem orderItem;
                        if (invoiceRecord.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
                            orderItem = orderItemFacade.getByPlatTrxNo(it.getPlatTrxNo());
                        }else {
                            final OfflineOrderItem item = offlineOrderItemFacade.getOrderItemByPlatTrxNo(it.getPlatTrxNo());
                            orderItem = new OrderItem();
                            BeanUtil.copyProperties(item, orderItem);
                        }
                        ExportDetailVo vo = new ExportDetailVo();
                        BeanUtil.copyProperties(orderItem, vo);

                        return vo;
                    }).collect(Collectors.toList()));
                }
                current++;
            } while (detailPage != null && detailPage.getRecords() != null && !detailPage.getRecords().isEmpty());
        }

        IPage<InvoiceDetailGroupByIdCardVo> groupDetailPage = null;
        List<ExportDetailGroupVo> allGroupData = new LinkedList<>();
        current = 1;
        do {
            Page<InvoiceDetailGroupByIdCardVo> page = new Page<>(current, size);
            groupDetailPage = invoiceRecordDetailFacade.listInvoiceDetailGroupByIdCard(page, invoiceRecordDetailDto.getInvoiceTrxNo());

            if (groupDetailPage != null && groupDetailPage.getRecords() != null && !groupDetailPage.getRecords().isEmpty()) {
                allGroupData.addAll(
                        groupDetailPage.getRecords().stream().map(it->{
                            ExportDetailGroupVo vo = new ExportDetailGroupVo();
                            vo.setReceiveNameDecrypt(it.getReceiveName());
                            vo.setReceiveAccountNoDecrypt(it.getReceiveAccountNo());
                            vo.setReceivePhoneNoDecrypt(it.getReceivePhoneNo());
                            vo.setReceiveIdCardNoDecrypt(it.getReceiveIdCardNo());
                            vo.setInvoiceAmount(it.getInvoiceAmount());
                            vo.setInvoiceStatus(InvoiceStatusEnum.getEnum(it.getInvoiceStatus()).getDesc());
                            vo.setInvoiceType(InvoiceTypeEnum.getEnum(it.getInvoiceType()).getDesc());
                            vo.setWorkCategoryName(invoiceRecord.getInvoiceCategoryName());
                            return vo;
                        }).collect(Collectors.toList())
                );
                current++;
            }
        } while (groupDetailPage != null && groupDetailPage.getRecords() != null && !groupDetailPage.getRecords().isEmpty());

        exportBiz.export(servletResponse, "开票清单", allGroupData, allData, ExportDetailGroupVo.class, ExportDetailVo.class);

    }

    @GetMapping("download/downloadInvoiceIdImages")
    public void downloadInvoiceIdImages(InvoiceRecordDetailDto invoiceRecordDetailDto, HttpServletResponse servletResponse) throws IOException {

        final String tempPath = genRandomPath();
        ServletOutputStream out = servletResponse.getOutputStream();
        FileInputStream inputStream = null;
        List<InvoiceRecordDetail> invoiceRecordDetails= invoiceRecordDetailFacade.listInvoiceDetailIdCards(invoiceRecordDetailDto);
        try {
            if (invoiceRecordDetails!=null && !invoiceRecordDetails.isEmpty()) {
                for (InvoiceRecordDetail record : invoiceRecordDetails) {
                    final String idCardNoMd5 = record.getReceiveIdCardNoMd5();
                    final UserInfo userInfo = userInfoFacade.getByIdCardNoMd5(idCardNoMd5);
                    if (userInfo != null) {
                        final String frontUrl = userInfo.getIdCardFrontUrl();
                        final String backUrl = userInfo.getIdCardBackUrl();
                        final String copyUrl = userInfo.getIdCardCopyUrl();
                        final String name = userInfo.getReceiveNameDecrypt();
                        final String idCardNo = userInfo.getReceiveIdCardNoDecrypt();

                        String path = name + "-" + idCardNo;
                        if (StringUtils.isNotBlank(frontUrl)) {
                            String suffix = userInfo.getIdCardFrontUrl().substring(userInfo.getIdCardFrontUrl().lastIndexOf("."));
                            File idCardFront = FileUtils.createFile(tempPath + File.separator + path + File.separator + userInfo.getReceiveNameDecrypt() + ID_CARD_FRONT + suffix);
                            InputStream idCardFroneStream = fastdfsClient.downloadFile(userInfo.getIdCardFrontUrl());
                            org.apache.commons.io.FileUtils.copyInputStreamToFile(idCardFroneStream, idCardFront);
                        }

                        if (StringUtils.isNotBlank(backUrl)) {
                            String suffix = userInfo.getIdCardBackUrl().substring(userInfo.getIdCardBackUrl().lastIndexOf("."));
                            File idCardBack = FileUtils.createFile(tempPath + File.separator + path + File.separator + userInfo.getReceiveNameDecrypt() + ID_CARD_BACK + suffix);
                            InputStream idCardBackStream = fastdfsClient.downloadFile(userInfo.getIdCardBackUrl());
                            org.apache.commons.io.FileUtils.copyInputStreamToFile(idCardBackStream, idCardBack);
                        }

                        if (StringUtils.isNotBlank(copyUrl)) {
                            String suffix = userInfo.getIdCardCopyUrl().substring(userInfo.getIdCardCopyUrl().lastIndexOf("."));
                            File idCardCopy = FileUtils.createFile(tempPath + File.separator + path + File.separator + userInfo.getReceiveNameDecrypt() + ID_CARD_COPY + suffix);
                            InputStream idCardCopyStream = fastdfsClient.downloadFile(userInfo.getIdCardCopyUrl());
                            org.apache.commons.io.FileUtils.copyInputStreamToFile(idCardCopyStream, idCardCopy);
                        }
                        log.info("压缩: {}", tempPath + File.separator + path);
                    }
                }
            }
            File zipFile;
            try {
                zipFile = ZipUtil.zipFileKeepConstruct(tempPath);
            }catch (FileNotFoundException e){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("没有可下载的附件");
            }
            servletResponse.setContentType("application/vnd.ms-excel");
            servletResponse.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(zipFile.getName(), "UTF-8").replaceAll("\\+", "%20");
            servletResponse.setHeader("Content-disposition", "attachment;filename=" + fileName);

            inputStream = new FileInputStream(zipFile);

            IoUtil.copy(inputStream, out);
            out.flush();
        } finally {
            if (out != null) {
                out.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            FileUtils.deleteDir(new File(tempPath));
        }
    }

    private String genRandomPath() {
        try {
            String filePath = System.getProperty("user.dir")
                    + File.separator
                    + "export"
                    + File.separator
                    + RandomUtil.get16LenStr();
            FileUtils.creatDir(filePath);
            return filePath;
        } catch (Exception e) {
            throw CommonExceptions.UNEXPECT_ERROR.newWith("创建文件失败", e);
        }

    }
}
