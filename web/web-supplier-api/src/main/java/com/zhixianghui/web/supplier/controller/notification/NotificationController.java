package com.zhixianghui.web.supplier.controller.notification;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.notification.NotificationReceiverTypeEnum;
import com.zhixianghui.common.statics.enums.notification.PublishStatusEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecord;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.common.service.NotificationFacade;
import com.zhixianghui.facade.common.vo.NotificationDetailFullInfo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.dto.NotificationDetailQueryDto;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("notification")
public class NotificationController {
    @Reference
    private NotificationFacade notificationFacade;

    @GetMapping("getNoReadAccount")
    public RestResult getNoReadAccount(@CurrentMainstayNo String mainstayNo){
        Page page = new Page<>();
        page.setCurrent(1L);
        page.setSize(1L);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("readStatus", YesNoCodeEnum.NO.getValue());
        paramMap.put("mchNo",mainstayNo);
        paramMap.put("publishStatus", PublishStatusEnum.PUBLISHED.getCode());
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_MERCHANT.getCode(),NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode(),NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode(),NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode()});
        IPage readData = notificationFacade.listNotificationRecordFullInfo(page,paramMap);
        return RestResult.success(readData.getTotal());
    }

    @PostMapping("listNotificationRecordFullInfo")
    public RestResult<IPage<NotificationDetailFullInfo>> listNotificationRecordFullInfo(@RequestBody Page page, @RequestBody NotificationDetailQueryDto dto, @CurrentMainstayNo String mainstayNo) {
        final Map<String, Object> paramMap = BeanUtil.toMap(dto);
        paramMap.put("mchNo", mainstayNo);
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_MERCHANT.getCode(),NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode(),NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode(),NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode()});
        final IPage pageData = notificationFacade.listNotificationRecordFullInfo(page, paramMap);
        return RestResult.success(pageData);
    }

    @PostMapping("updateNotificationDetailReadStatus")
    public RestResult<String> updateNotificationDetailReadStatus(@RequestParam List<Long> ids, @RequestParam Integer status, @CurrentStaffVo SupplierStaffVO staffVO) {
        for (Long id : ids) {
            final NotificationRecordDetail notificationRecordDetailById = this.notificationFacade.getNotificationRecordDetailById(id);
            if (notificationRecordDetailById != null) {
                final LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
                notificationRecordDetailById.setReadStatus(status);
                notificationRecordDetailById.setUpdateTime(now);
                notificationRecordDetailById.setReadOperatorId(staffVO.getId());
                notificationRecordDetailById.setReadOperatorName(staffVO.getOperatorName());
                notificationRecordDetailById.setReadTime(now);

                this.notificationFacade.updateNotificationDetailReadStatus(notificationRecordDetailById);
            }
        }

        return RestResult.success("更新成功");
    }

    @GetMapping("notifyList")
    public RestResult notifyList(@CurrentMainstayNo String mainstayNo){

        Page page = new Page<>();
        page.setCurrent(1L);
        page.setSize(10L);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("sortColumns","b.PUBLISH_TIME desc");
        paramMap.put("mchNo",mainstayNo);
        paramMap.put("publishStatus", PublishStatusEnum.PUBLISHED.getCode());
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_MERCHANT.getCode(),NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode(),NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode(),NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode()});
        IPage pageData = notificationFacade.listNotificationRecordFullInfo(page,paramMap);
        List<NotificationDetailFullInfo> list = pageData.getRecords();
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("array",list.stream().map(x->{
            NotificationRecordDetail detail = x.getNotificationRecordDetail();
            return new HashMap<String,Object>(){
                private static final long serialVersionUID = 9161351061083300991L;

                {   put("id",detail.getNotificationId());
                    put("pop", x.getNotificationRecord().isPop());
                    put("readStatus", detail.getReadStatus());
                    put("notificationTitle",detail.getNotificationTitle());
                    put("publishTime",x.getNotificationRecord().getPublishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));}};
        }).collect(Collectors.toList()));

        paramMap.put("readStatus", YesNoCodeEnum.NO.getValue());
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_MERCHANT.getCode(),NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode(),NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode(),NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode()});
        IPage readData = notificationFacade.listNotificationRecordFullInfo(page,paramMap);
        resultMap.put("count",readData.getTotal());
        return RestResult.success(resultMap);
    }

    @GetMapping("notificationInfoById")
    public RestResult<NotificationRecord> getNotificationInfoById(@RequestParam Long id) {
        return RestResult.success(this.notificationFacade.getNotificationInfoById(id));
    }
}
