package com.zhixianghui.web.supplier.vo.trade.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 订单查询Vo
 * @date 2020-11-09 09:44
 **/
@Data
public class OrderQueryVo {

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 商户名称
     */
    private String employerNameLike;

    /**
     * 发放方式
     */
    private Integer channelType;

    /**
     * 批次状态
     */
    private Integer batchStatus;

    /**
     * 创建起始时间
     */
    @NotNull(message = "创建起始时间不能为空")
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    @NotNull(message = "创建截止时间不能为空")
    private Date createEndDate;

    /**
     * 自由服务者类别
     */
    private String workCategoryCode;


    /**
     * 批次名
     */
    private String batchNameLike;
}
