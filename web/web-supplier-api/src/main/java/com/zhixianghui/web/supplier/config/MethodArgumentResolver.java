package com.zhixianghui.web.supplier.config;

import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentOperatorVO;
import com.zhixianghui.web.supplier.annotation.CurrentSession;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.component.JwtHelper;
import com.zhixianghui.web.supplier.component.SessionManager;
import com.zhixianghui.web.supplier.constant.PermissionConstant;
import com.zhixianghui.web.supplier.dto.JwtInfo;
import com.zhixianghui.web.supplier.dto.Session;
import com.zhixianghui.web.supplier.exception.NoSelectSupplierException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

/**
 *
 *
 * <AUTHOR> Guangsheng
 */
@Component
public class MethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private SessionManager sessionManager;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return (parameter.getParameterType().isAssignableFrom(SupplierOperatorVO.class) && parameter.hasParameterAnnotation(CurrentOperatorVO.class))
                || (parameter.getParameterType().isAssignableFrom(SupplierStaffVO.class) && parameter.hasParameterAnnotation(CurrentStaffVo.class))
                || (parameter.getParameterType().isAssignableFrom(String.class) && parameter.hasParameterAnnotation(CurrentMainstayNo.class))
                || (parameter.getParameterType().isAssignableFrom(Session.class) && parameter.hasParameterAnnotation(CurrentSession.class));
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        HttpServletRequest request = (HttpServletRequest) (webRequest.getNativeRequest());
        JwtInfo jwtInfo = (JwtInfo) request.getAttribute("jwtInfo");
        Session session = (Session) request.getAttribute("session");
        if (jwtInfo == null || session == null) {
            return null;
        }

        if (parameter.getParameterType().isAssignableFrom(Session.class) && parameter.hasParameterAnnotation(CurrentSession.class)) {
            return session;
        } else if (parameter.getParameterType().isAssignableFrom(SupplierOperatorVO.class) && parameter.hasParameterAnnotation(CurrentOperatorVO.class)) {
            return session.getAttribute(PermissionConstant.OPERATOR_SESSION_KEY, SupplierOperatorVO.class);
        } else if (parameter.getParameterType().isAssignableFrom(SupplierStaffVO.class) && parameter.hasParameterAnnotation(CurrentStaffVo.class)) {
            SupplierStaffVO staffVO = session.getAttribute(PermissionConstant.STAFF_SESSION_KEY, SupplierStaffVO.class);
            if (staffVO == null) {
                throw new NoSelectSupplierException();
            }
            return staffVO;
        } else if (parameter.getParameterType().isAssignableFrom(String.class) && parameter.hasParameterAnnotation(CurrentMainstayNo.class)) {
            String mchNo = session.getAttribute(PermissionConstant.MCH_NO_SESSION_KEY, String.class);
            if (mchNo == null) {
                throw new NoSelectSupplierException();
            }
            return mchNo;
        }
        return null;
    }
}
