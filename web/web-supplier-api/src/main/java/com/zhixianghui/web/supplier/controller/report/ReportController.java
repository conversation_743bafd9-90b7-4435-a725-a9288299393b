package com.zhixianghui.web.supplier.controller.report;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.common.service.ReportFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.vo.vo.ReportVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName ReportController
 * @Description TODO
 * @Date 2022/5/26 11:56
 */
@RestController
public class ReportController {

    @Reference
    private ReportFacade reportFacade;

    @PostMapping("report/report")
    public RestResult<String> report(@Validated @RequestBody ReportVo reportVo, @CurrentStaffVo SupplierStaffVO vo, @CurrentMainstayNo String mainstayNo) {
        // 1. 参数校验
        if (reportVo == null){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数不能为空");
        }
        if (!StringUtils.equals(reportVo.getPayChannelNo(), ChannelNoEnum.ALIPAY.name())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("暂不支持该通道报备");
        }

        ReportEntity reportEntity = new ReportEntity();
        BeanUtils.copyProperties(reportVo,reportEntity);
        reportEntity.setEmployerNo("");
        reportEntity.setEmployerName("");
        reportEntity.setMainstayNo(mainstayNo);
        reportEntity.setMainstayName(vo.getMchName());
        reportEntity.setReporter(vo.getOperatorName());
        reportEntity.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        return RestResult.success(reportFacade.reportMerchantDiy(reportEntity));
    }
}
