package com.zhixianghui.web.supplier.vo.merchant;

import com.zhixianghui.common.statics.enums.merchant.AgreementFileTypeEnum;
import com.zhixianghui.facade.merchant.entity.AgreementFile;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-04 15:28
 **/
@Data
public class AgreementFileVo {
    /**
     * 文件名
     */
    @NotNull(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件路径
     */
    @NotNull(message = "文件url不能为空")
    private String fileUrl;

    /**
     * vo转换实体
     * @param agreementFileVo vo对象
     * @return 默认返回文件类型为普通的实体，需要的自己更改
     */
    public static AgreementFile toEntity(AgreementFileVo agreementFileVo){
        AgreementFile agreementFile = AgreementFile.builder().type(AgreementFileTypeEnum.COMMON_FILE.getValue()).build();
        BeanUtils.copyProperties(agreementFileVo,agreementFile);
        return agreementFile;
    }
}
