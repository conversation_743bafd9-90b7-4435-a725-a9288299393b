package com.zhixianghui.web.supplier.vo.trade.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-09 10:18
 **/
@Data
public class OrderResVo {

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户编号
     */
    private String employerNo;

    /**
     * 商户名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 发放方式
     */
    private Integer channelType;

    /**
     * 发放模式
     */
    private Integer launchWay;

    /**
     * 请求笔数
     */
    private Integer requestCount;

    /**
     * 请求(总)实发金额
     */
    private BigDecimal requestNetAmount;

    /**
     * 请求（总）任务金额
     */
    private BigDecimal requestTaskAmount;

    /**
     * 已受理笔数
     */
    private Integer acceptedCount;

    /**
     * 已受理(总)实发金额
     */
    private BigDecimal acceptedNetAmount;

    /**
     * 已受理（总）任务金额
     */
    private BigDecimal acceptedTaskAmount;

    /**
     * 已受理代征主体服务费
     */
    private BigDecimal acceptedFee;

    /**
     * 已受理(总)订单金额
     */
    private BigDecimal acceptedOrderAmount;

    /**
     * 批次状态
     */
    private Integer batchStatus;


    /**
     * 自由服务者类别名称
     */
    private String workCategoryName;

}
