package com.zhixianghui.web.supplier.vo.merchant;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-03 16:03
 **/
@Data
public class AgreementReqFileVo implements Serializable {

    @NotNull(groups = IAgreementArchive.class, message = "id不能为空")
    private Long id;
    /**
     * 协议文件列表
     */
    @Valid
    @NotNull(message = "协议文件列表不能为空")
    @NotNull(groups = IAgreementArchive.class, message = "协议文件列表不能为空")
    private List<AgreementFileVo> fileVoList;

}
