package com.zhixianghui.web.supplier.vo.merchant;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商户信息vo，结合多表
 * <AUTHOR>
 * @date 2020/8/14
 **/
@Data
public class MerchantInfoVo implements Serializable {
    private Long id;
    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 激活时间
     */
    private Date activeTime;

    /**
     * 商户状态
     * @see com.zhixianghui.common.statics.enums.merchant.MchStatusEnum
     */
    private Integer mchStatus;

    /**
     * 认证状态
     * @see com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum
     */
    private Integer authStatus;

    /**
     * 商户类型
     * @see com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum
     */
    private Integer merchantType;

}
