package com.zhixianghui.web.supplier.controller.flow;

import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.service.agent.AgentRoleFacade;
import com.zhixianghui.facade.merchant.service.supplier.SupplierRoleFacade;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/5 17:29
 */
public class CommonFlow {

    private static final String CRM_FLAG = "CRM观察员";

    public boolean crmRole(String mchNo, SupplierRoleFacade supplierRoleFacade) {
        List<SupplierRole> roleList = supplierRoleFacade.listAll(mchNo);
        if (CollectionUtils.isEmpty(roleList)) {
            return false;
        }
        for (SupplierRole supplierRole : roleList) {
            if (CRM_FLAG.equals(supplierRole.getName())) {
                return true;
            }
        }
        return false;
    }

    public boolean admin(SupplierStaffVO supplierStaffVO, SupplierRoleFacade supplierRoleFacade) {
        if (supplierStaffVO.getType() == PortalStaffTypeEnum.ADMIN.getValue()) {
            return true;
        }
        return crmRole(supplierStaffVO.getMchNo(), supplierRoleFacade);
    }
}
