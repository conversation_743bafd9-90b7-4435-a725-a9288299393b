package com.zhixianghui.web.supplier.biz;

import com.zhixianghui.common.statics.enums.merchant.AgreementFileTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.merchant.entity.Agreement;
import com.zhixianghui.facade.merchant.entity.AgreementFile;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierOperator;
import com.zhixianghui.facade.merchant.service.AgreementFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.web.supplier.vo.merchant.AgreementFileVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AgreementBiz {
    @Reference
    private AgreementFacade agreementFacade;

//    public void archiveAgreement(Long id, List<AgreementFileVo> fileVoList, SupplierOperatorVO supplierOperator) {
//        List<AgreementFile> fileList = fileVoList.stream().map(
//                vo -> {
//                    if(StringUtils.isBlank(vo.getFileName())|| StringUtils.isBlank(vo.getFileUrl())){
//                        throw CommonExceptions.PARAM_INVALID.newWithErrMsg("文件信息不完整");
//                    }
//                    AgreementFile agreementFile = AgreementFileVo.toEntity(vo);
//                    agreementFile.setAgreementId(id);
//                    agreementFile.setType(AgreementFileTypeEnum.ARCHIVE.getValue());
//                    return agreementFile;
//                }
//        ).collect(Collectors.toList());
//        agreementFacade.archive(fileList,supplierOperator.getName());
//    }

    public List<Agreement> listBy(Map<String, Object> paramMap) {
        return agreementFacade.listBy(paramMap);
    }

    public void archiveAgreement(Long id, List<AgreementFileVo> fileVoList, String name) {
        List<AgreementFile> fileList = fileVoList.stream().map(
                vo -> {
                    if(StringUtils.isBlank(vo.getFileName())|| StringUtils.isBlank(vo.getFileUrl())){
                        throw CommonExceptions.PARAM_INVALID.newWithErrMsg("文件信息不完整");
                    }
                    AgreementFile agreementFile = AgreementFileVo.toEntity(vo);
                    agreementFile.setAgreementId(id);
                    agreementFile.setType(AgreementFileTypeEnum.ARCHIVE.getValue());
                    return agreementFile;
                }
        ).collect(Collectors.toList());
        agreementFacade.archive(fileList,name);
    }
}
