package com.zhixianghui.web.supplier.utils;

import com.google.common.collect.Maps;
import org.springframework.cglib.beans.BeanMap;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020-09-24 17:37
 **/
public class BeanToMapUtil {
    /**
     * 将对象装换为map
     * @param bean bean
     * @return map
     */
    public static Map<String, Object> beanToMap(Object bean) {
        Map<String, Object> map = Maps.newHashMap();
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                map.put(key+"", beanMap.get(key));
            }
        }
        return map;
    }
}
