package com.zhixianghui.web.supplier.controller.merchant;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.banklink.vo.account.MainstayAmountQueryDto;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.entity.report.PayChannelType;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.FlowTopicType;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.facade.common.service.PayChannelTypeFacade;
import com.zhixianghui.facade.common.vo.ApprovalInfoVo;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.flow.dto.FlowStartDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantBankAccount;
import com.zhixianghui.facade.merchant.entity.MerchantFile;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerMainAuthVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantLogoVo;
import com.zhixianghui.facade.merchant.vo.merchant.MerchantUpdateVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.annotation.Logger;
import com.zhixianghui.web.supplier.biz.approval.ApprovalFlowBiz;
import com.zhixianghui.web.supplier.vo.merchant.MainstayChannelRelationVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.*;

/**
 * 代征主体信息
 * <AUTHOR>
 * @date 2020/9/28
 **/
@RestController
@RequestMapping("merchantMainstay")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MainstayController {

    private final ApprovalFlowBiz approvalFlowBiz;

    @Reference
    private MerchantFileFacade merchantFileFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantEmployerFacade merchantEmployerFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private ApprovalFlowFacade approvalFlowFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private PayChannelFacade payChannelFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private MerchantBankAccountFacade merchantBankAccountFacade;
    @Reference
    private PayChannelTypeFacade payChannelTypeFacade;

    @Logger(type = OperateLogTypeEnum.MODIFY, action = "修改供应商Logo")
    @PostMapping("changeLogo")
    public RestResult updateLogo(@RequestBody  MerchantLogoVo merchantLogoVo, @CurrentMainstayNo String mchNo, @CurrentStaffVo SupplierStaffVO staffVO){
        merchantFacade.changeLogo(merchantLogoVo,mchNo,staffVO.getName());
        return RestResult.success("Logo更新成功");
    }

    /**
     * 商户信息查询
     *
     * @return .
     */
    @RequestMapping("getMerchant")
    public RestResult<Map<String,Object>> getMerchant(@CurrentMainstayNo String mchNo) {
        if(StringUtil.isEmpty(mchNo)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前未绑定商户");
        }
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        Map<String,Object> respMap = BeanUtil.toMap(merchant);
        //查询平台logo
        respMap.put("platformLogoUrl","");
        respMap.put("loginLogoUrl","");
        List<MerchantFile> merchantFileList = merchantFileFacade.listByMchNo(mchNo);
        for (MerchantFile merchantFile : merchantFileList) {
            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.PLATFORM_LOGO.getValue()){
                respMap.put("platformLogoUrl",merchantFile.getFileUrl());
                continue;
            }

            if (merchantFile.getFileType().intValue() == MerchantFileTypeEnum.LOGIN_LOGO.getValue()){
                respMap.put("loginLogoUrl",merchantFile.getFileUrl());
            }
        }
        return RestResult.success(respMap);
    }

    /**
     * 主体认证信息
     *
     * @return
     */
    @RequestMapping("mainAuth")
    @Permission("merchant:mainstay:mainAuth")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "主体认证")
    public RestResult<Map<String, Object>> mainAuth(@Valid @RequestBody FlowStartDto<MerchantEmployerMainAuthVo> flowStartDto, @CurrentStaffVo SupplierStaffVO staffVO) {
        MerchantEmployerMainAuthVo authVo = flowStartDto.getExtObj();
        Merchant merchant = merchantQueryFacade.getByMchNo(authVo.getMchNo());
        if(!Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.FAIL.getValue())
                && !Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.UN_AUTH.getValue())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许提交主体认证");
        }

        authVo.setMerchantType(merchant.getMerchantType());
        authVo.setMchName(merchant.getMchName());
        authVo.setContactName(merchant.getContactName());
        authVo.setContactPhone(merchant.getContactPhone());
        authVo.setOperatorLoginName(staffVO.getPhone());
        // 校验字段
        merchantEmployerFacade.validMainAuthVo(authVo);
        log.info("主体认证：{}", JsonUtil.toString(authVo));
        //构建流程参数
        //当前用户
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(staffVO.getId());
        flowUserVo.setUserName(StringUtil.isEmpty(staffVO.getName()) ? staffVO.getMchNo() : staffVO.getName());
        flowUserVo.setPlatform(PlatformSource.SUPPLIER.getValue());
        flowUserVo.setNo(staffVO.getMchNo());
        //流程主体参数
        ProcessVo processVo = new ProcessVo();
        processVo.setBusinessKey(authVo.getMchNo());
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(FlowTypeEnum.MAINSTAY_MCH_MAIN_AUTH.name());
        processVo.setFlowTopicType(FlowTypeEnum.MAINSTAY_MCH_MAIN_AUTH.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-",FlowTypeEnum.MAINSTAY_MCH_MAIN_AUTH.getDesc(),authVo.getMchName()));
        processVo.setExtInfo(JsonUtil.toString(authVo));
        processVo.setRemark(flowStartDto.getRemark());
        //设置流程变量
        flowStartDto.getCondition().put("mchNo",authVo.getMchNo());
        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo,flowUserVo,flowStartDto.getParticipant(),flowStartDto.getCondition());

        // 商户状态更新为审核中
        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
        merchant.setUpdator(staffVO.getPhone());
        merchant.setUpdateTime(new Date());
        merchantFacade.update(merchant);

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

//    /**
//     * 主体认证信息
//     *
//     * @param authVo     .
//     * @return .
//     */
//    @RequestMapping("mainAuth")
//    @Permission("merchant:mainstay:mainAuth")
//    @Logger(type = OperateLogTypeEnum.MODIFY, action = "主体认证")
//    public RestResult<Map<String, Object>> mainAuth(@Valid @RequestBody MerchantEmployerMainAuthVo authVo, @CurrentStaffVo SupplierStaffVO staffVO) {
//        Merchant merchant = merchantQueryFacade.getByMchNo(authVo.getMchNo());
//        if(!Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.FAIL.getValue())
//                && !Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.UN_AUTH.getValue())){
//            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许提交主体认证");
//        }
//
//        authVo.setMerchantType(merchant.getMerchantType());
//        authVo.setMchName(merchant.getMchName());
//        authVo.setContactName(merchant.getContactName());
//        authVo.setContactPhone(merchant.getContactPhone());
//        authVo.setOperatorLoginName(staffVO.getPhone());
//        // 校验字段
//        merchantEmployerFacade.validMainAuthVo(authVo);
//        log.info("主体认证：{}", JsonUtil.toString(authVo));
//        // 保存主体认证审核记录
//        ApprovalFlow approvalFlow = new ApprovalFlow();
//        approvalFlow.setVersion(0);
//        approvalFlow.setStepNum(0);
//        approvalFlow.setInitiatorId(staffVO.getId());
//        approvalFlow.setInitiatorName(staffVO.getName());
//        approvalFlow.setFlowTopicType(FlowTopicType.MERCHANT_VERIFY.getValue());
//        approvalFlow.setFlowTopicName(String.join("-",FlowTopicType.MERCHANT_VERIFY.getDesc(),authVo.getMchName()));
//        approvalFlow.setCreateTime(new Date());
//        approvalFlow.setUpdateTime(new Date());
//        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
//        approvalFlow.setExtInfo(JsonUtil.toString(authVo));
//        approvalFlow.setPlatform(PlatformSource.SUPPLIER.getValue());
//
//        ApprovalInfoVo infoVo = approvalFlowFacade.createFlow(approvalFlow);
//
//        // 商户状态更新为审核中
//        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
//        merchant.setUpdator(staffVO.getPhone());
//        merchant.setUpdateTime(new Date());
//        merchantFacade.update(merchant);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("id", infoVo.getId());
//        result.put("createTime", DateUtil.formatDateTime(approvalFlow.getCreateTime()));
//        result.put("submitName", staffVO.getName());
//        return RestResult.success(result);
//    }

    /**
     *  查询主体认证流程
     * @param staffVO 操作人vo
     * @return 审批流信息
     */
    @RequestMapping("getApprovalFlow")
    @Permission("merchant:mainstay:mainAuth")
    public RestResult<Map<String, Object>> getApprovalFlow(@CurrentStaffVo SupplierStaffVO staffVO) {

        Map<String,Object> paramMap = Maps.newHashMap();
        //一个商户的主体认证非终态审批需保证只有一条
        paramMap.put("mchNo",staffVO.getMchNo());
        paramMap.put("platform",PlatformSource.SUPPLIER.getValue());
        paramMap.put("status",FlowStatus.PENDING.getValue());
        ApprovalFlow approvalFlow = approvalFlowFacade.getOne(paramMap);
        if(approvalFlow == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("该商户主体认证审批不存在");
        }

        Map<String,Object> map = Maps.newHashMap();
        map.put("approvalFlow",approvalFlow);
        map.put("canEdit", approvalFlow.getInitiatorId().equals(staffVO.getId()));
        return RestResult.success(map);
    }

    /**
     * 主体认证审批编辑
     * @param updateExtInfoVo
     * @param staffVO
     * @return
     */
    @RequestMapping("mainAuthApprovalEdit")
    @Permission("merchant:mainstay:mainAuth")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "主体认证审批编辑")
    public RestResult<Map<String, Object>> mainAuthApprovalEdit(@Validated @RequestBody UpdateExtInfoVo updateExtInfoVo, @CurrentStaffVo SupplierStaffVO staffVO) {
        String newAuthStr = updateExtInfoVo.getExtInfo();
        MerchantEmployerMainAuthVo newAuth = JsonUtil.toBean(newAuthStr, MerchantEmployerMainAuthVo.class);

        Merchant merchant = merchantQueryFacade.getByMchNo(staffVO.getMchNo());
        newAuth.setMerchantType(merchant.getMerchantType());
        newAuth.setMchNo(staffVO.getMchNo());
        newAuth.setMchName(merchant.getMchName());
        newAuth.setContactName(merchant.getContactName());
        newAuth.setContactPhone(merchant.getContactPhone());
        newAuth.setOperatorLoginName(staffVO.getPhone());
        if(Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.SUCCESS.getValue())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许编辑提交主体认证");
        }

        // 校验字段
        merchantEmployerFacade.validMainAuthVo(newAuth);
        log.info("主体认证审批编辑：{}", JsonUtil.toString(newAuth));

        // 更新主体认证审核记录
        updateExtInfoVo.setExtInfo(JsonUtil.toString(newAuth));
        approvalFlowBiz.updateExtInfo(updateExtInfoVo, staffVO);

        // 商户状态更新为审核中
        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
        merchant.setUpdator(staffVO.getName());
        merchant.setUpdateTime(new Date());
        merchantFacade.update(merchant);

        Long id = updateExtInfoVo.getApprovalFlowId();
        ApprovalFlow approvalFlow = approvalFlowBiz.getById(id,staffVO.getId());
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("createTime", DateUtil.formatDateTime(approvalFlow.getCreateTime()));
        result.put("submitName", approvalFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @PostMapping("listChannelRelationInfoByMainstayNo")
    public RestResult<List<Map<String,Object>>> getBalanceByMainstayNo(@RequestParam @Validated @NotBlank String mainstayNo) {
        final List<MainstayChannelRelation> mainstayChannelRelations = mainstayChannelRelationFacade.listByMainstayNo(mainstayNo);

        List<Map<String, Object>> channelList = new ArrayList<>();
        mainstayChannelRelations.forEach(it->{
            PayChannel payChannel = payChannelFacade.getByChannelNo(it.getPayChannelNo());
            Map<String, String> amount;
            try {
                if (it.getStatus().equals(OpenOffEnum.OFF.getValue())){
                    Map<String,String> map=new HashMap<>();
                    map.put("useAbleSettAmount", "未开通");
                    map.put("frozenAmount", "未开通");
                    amount=map;
                }else {
                    amount = getAmount(it);
                }
            } catch (Exception e) {
                log.error("查询余额出错",e);
                amount = null;
            }
            Map<String, Object> detailMap = new HashMap<>();
            if (amount == null) {
                detailMap.put("balance", "异常");
                detailMap.put("frozenAmount", "异常");
            } else {
                detailMap.put("balance", amount.get("useAbleSettAmount"));
                detailMap.put("frozenAmount", amount.get("frozenAmount"));
            }
            detailMap.put("payChannelNo",it.getPayChannelNo());
            detailMap.put("mainstayNo",it.getMainstayNo());
            detailMap.put("mainstayName",it.getMainstayName());
            detailMap.put("channelMchNo",it.getChannelMchNo());
            detailMap.put("payChannelName",payChannel.getPayChannelName());
            channelList.add(detailMap);
        });

        return RestResult.success(channelList);
    }

    /**
     * 获取各通道余额
     * @return
     */
    private Map<String, String> getAmount(MainstayChannelRelation channelRelation) throws Exception{
        MainstayAmountQueryDto amountQueryDto = new MainstayAmountQueryDto();
        amountQueryDto.setMainstayNo(channelRelation.getMainstayNo());
        amountQueryDto.setChannelNo(channelRelation.getPayChannelNo());
        amountQueryDto.setChannelMchNo(channelRelation.getChannelMchNo());
        amountQueryDto.setAgreementNo(channelRelation.getAgreementNo());
        Map<String, String> amount = accountQueryFacade.getMainstayAmount(amountQueryDto);
        return amount;
    }

    /**
     * 获取银行卡信息
     * @param mainStayNo
     * @return
     */
    @GetMapping("getBankAccount")
    public RestResult<MerchantBankAccount> getBankAccount(@CurrentMainstayNo String mainStayNo){
        return RestResult.success(merchantBankAccountFacade.getByMchNo(mainStayNo));
    }
}
