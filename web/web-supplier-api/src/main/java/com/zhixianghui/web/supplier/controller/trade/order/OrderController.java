package com.zhixianghui.web.supplier.controller.trade.order;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.withdraw.WithdrawRecordQueryDto;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.utils.TrxNoDateUtil;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentSession;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.annotation.Logger;
import com.zhixianghui.web.supplier.biz.OrderBiz;
import com.zhixianghui.web.supplier.biz.WithdrawRecordBiz;
import com.zhixianghui.web.supplier.component.DataDecryptHelper;
import com.zhixianghui.web.supplier.component.TradePwdHelper;
import com.zhixianghui.web.supplier.constant.PermissionConstant;
import com.zhixianghui.web.supplier.dto.Session;
import com.zhixianghui.web.supplier.vo.PageVo;
import com.zhixianghui.web.supplier.vo.trade.req.OrderItemQueryVo;
import com.zhixianghui.web.supplier.vo.trade.req.OrderQueryVo;
import com.zhixianghui.web.supplier.vo.trade.res.OrderItemResVo;
import com.zhixianghui.web.supplier.vo.trade.res.OrderResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description 供应商后台订单相关Controller
 * @date 2020-12-15 09:50
 **/
@Slf4j
@RestController
@RequestMapping("order")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderController {

    private final OrderBiz orderBiz;
    private final WithdrawRecordBiz withdrawRecordBiz;
    private final TradePwdHelper tradePwdHelper;
    private final DataDecryptHelper dataDecryptHelper;

    @PostMapping("exportExcel/{type}")
    public RestResult exportExcel(@PathVariable Integer type, @Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                  @CurrentStaffVo SupplierStaffVO staffVO,@CurrentMainstayNo String mainstayNo){
        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        orderItemQueryVo.setCreateBeginDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateBeginDate()));
        orderItemQueryVo.setCreateEndDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateEndDate()));
        orderBiz.exportReceipt(type,orderItemQueryVo,staffVO,mainstayNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 发放名单 批次分页查询
     * @param orderQueryVo 查询条件
     * @param pageVo 分页参数
     * @return 分页结果
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderPage")
    public RestResult<PageResult<List<OrderResVo>>> listOrderPage(@Validated @RequestBody OrderQueryVo orderQueryVo,
                                                                  @CurrentMainstayNo String mainstayNo, @RequestBody PageVo pageVo){
        if(DateUtil.compare(orderQueryVo.getCreateBeginDate(), orderQueryVo.getCreateEndDate(), Calendar.SECOND) > 0){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
        }
        orderQueryVo.setCreateBeginDate(TrxNoDateUtil.compareMinWithEndDate(orderQueryVo.getCreateBeginDate()));
        orderQueryVo.setCreateEndDate(TrxNoDateUtil.compareMinWithEndDate(orderQueryVo.getCreateEndDate()));
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OrderResVo>> pageResult = orderBiz.listOrderPage(orderQueryVo,mainstayNo,pageParam);
        return RestResult.success(pageResult);
    }

    /**
     * 发放名单 总条数
     * @param orderQueryVo 查询条件
     * @return 分页结果
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("countOrder")
    public RestResult<Map<String, Object>> countOrder(@Validated @RequestBody OrderQueryVo orderQueryVo,
                                                      @CurrentMainstayNo String mainstayNo){
        if(DateUtil.compare(orderQueryVo.getCreateBeginDate(), orderQueryVo.getCreateEndDate(), Calendar.SECOND) > 0){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
        }
        orderQueryVo.setCreateBeginDate(TrxNoDateUtil.compareMinWithEndDate(orderQueryVo.getCreateBeginDate()));
        orderQueryVo.setCreateEndDate(TrxNoDateUtil.compareMinWithEndDate(orderQueryVo.getCreateEndDate()));

        Long totalRecord = orderBiz.countOrder(orderQueryVo,mainstayNo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 订单明细 分页查询
     * @param orderItemQueryVo 查询条件
     * @param pageVo 分页参数
     * @return 分页结果
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("listOrderItemPage")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemPage(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                                          @CurrentMainstayNo String mainstayNo, @RequestBody PageVo pageVo){
        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        orderItemQueryVo.setCreateBeginDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateBeginDate()));
        orderItemQueryVo.setCreateEndDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateEndDate()));
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OrderItemResVo>> pageResult = orderBiz.listOrderItemPage(orderItemQueryVo,mainstayNo,pageParam);
        return RestResult.success(pageResult);
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @return 总条数
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("countOrderItem")
    public RestResult<Map<String, Object>> countOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                          @CurrentMainstayNo String mainstayNo){
        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        orderItemQueryVo.setCreateBeginDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateBeginDate()));
        orderItemQueryVo.setCreateEndDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateEndDate()));
        Long totalRecord = orderBiz.countOrderItem(orderItemQueryVo,mainstayNo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @return 总条数
     */
    @Permission("order:listOrderItemPage:sum")
    @PostMapping("sumOrderItem")
    public RestResult<OrderItemSumBo> sumOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                   @CurrentMainstayNo String mainstayNo){
        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        orderItemQueryVo.setCreateBeginDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateBeginDate()));
        orderItemQueryVo.setCreateEndDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateEndDate()));
        OrderItemSumBo sumBo = orderBiz.sumOrderItem(orderItemQueryVo, mainstayNo);
        return RestResult.success(sumBo);
    }

    /**
     * 订单明细-导出
     * @param orderItemQueryVo 查询条件
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("exportOrderItem")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "订单明细-导出")
    public RestResult<String> exportOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                              @CurrentMainstayNo String mainstayNo,
                                              @CurrentStaffVo SupplierStaffVO staffVo){
        validDate(orderItemQueryVo.getCreateBeginDate(), orderItemQueryVo.getCreateEndDate(), orderItemQueryVo.getCompleteBeginDate(), orderItemQueryVo.getCompleteEndDate());
        orderItemQueryVo.setCreateBeginDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateBeginDate()));
        orderItemQueryVo.setCreateEndDate(TrxNoDateUtil.compareMinWithEndDate(orderItemQueryVo.getCreateEndDate()));
        orderBiz.exportOrderItem(orderItemQueryVo,staffVo,mainstayNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @Permission("order:withdraw.do")
    @PostMapping("withdraw")
    @Logger(type = OperateLogTypeEnum.CREATE,action = "提现")
    public RestResult<WithdrawRecord> withdraw(@RequestBody WithdrawDto withdrawDto,
                                               @CurrentMainstayNo String mainstayNo,
                                               @CurrentSession Session session,
                                               @CurrentStaffVo SupplierStaffVO staffVo) {

        if (new BigDecimal(withdrawDto.getAmount()).compareTo(new BigDecimal("0.1")) < 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("金额不能低于0.1元");
        }

        if (StringUtils.isBlank(withdrawDto.getMainstayNo())) {
            withdrawDto.setMainstayNo(mainstayNo);
        }
        if (StringUtils.isNotBlank(withdrawDto.getRemark()) && withdrawDto.getRemark().length() > 30) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("备注长度限制30");
        }
        if (StringUtils.isBlank(withdrawDto.getRemark())) {
            withdrawDto.setRemark("提现");
        }

        String payPasswd = withdrawDto.getPayPasswd();
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        payPasswd=dataDecryptHelper.decryptData(privateKey, payPasswd);
        tradePwdHelper.verifyTradePwd(staffVo,mainstayNo,payPasswd);

        return RestResult.success(orderBiz.withdraw(withdrawDto));
    }

    @Permission("order:withdraw.view")
    @PostMapping("listWithdrawRecordPage")
    public RestResult<PageResult<List<WithdrawRecord>>> listWithdrawRecordPage(@RequestBody @Validated WithdrawRecordQueryDto withdrawRecordQueryDto,@CurrentMainstayNo String mainstayNo, @RequestBody PageVo pageVo) {
        validDate(withdrawRecordQueryDto.getCreateBeginDate(), withdrawRecordQueryDto.getCreateEndDate(), null, null);

        if (Objects.isNull(withdrawRecordQueryDto.getMainstayNo())) {
            withdrawRecordQueryDto.setMainstayNo(mainstayNo);
        }
        withdrawRecordQueryDto.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        return RestResult.success(withdrawRecordBiz.listWithRecordPage(withdrawRecordQueryDto,pageParam));
    }

    @Permission("order:withdraw.view")
    @PostMapping("exportWithRecord")
    public RestResult<String> exportWithRecord(@RequestBody @Validated WithdrawRecordQueryDto withdrawRecordQueryDto,@CurrentMainstayNo String mainstayNo, @CurrentStaffVo SupplierStaffVO staffVo) {
        validDate(withdrawRecordQueryDto.getCreateBeginDate(), withdrawRecordQueryDto.getCreateEndDate(), null, null);

        if (Objects.isNull(withdrawRecordQueryDto.getMainstayNo())) {
            withdrawRecordQueryDto.setMainstayNo(mainstayNo);
        }
        withdrawRecordQueryDto.setMerchantType(MerchantTypeEnum.MAINSTAY.getValue());
        withdrawRecordBiz.exportWithRecord(withdrawRecordQueryDto,staffVo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private void validDate(Date createBeginDate, Date createEndDate, Date completeBeginDate, Date completeEndDate) {

        if (createBeginDate != null && createEndDate != null) {
            if(DateUtil.compare(createBeginDate, createEndDate, Calendar.SECOND) > 0){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
            }
        }

        if (completeBeginDate != null || completeEndDate != null) {
            if((completeBeginDate != null && completeEndDate == null) ||
                    ( completeBeginDate == null && completeEndDate != null)){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间必须成对选择");
            }

            if(completeBeginDate != null && DateUtil.compare(completeBeginDate, completeEndDate, Calendar.SECOND) > 0){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间的截止时间不能大于起始时间");
            }
        }

    }

}
