package com.zhixianghui.web.supplier.vo.user;

import com.zhixianghui.common.statics.enums.user.supplier.SupplierLoginTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 登录请求VO
 *
 * <AUTHOR>
 */
@Data
public class LoginReqVO {

    /**
     * 手机号
     */
    @NotEmpty
    private String phone;

    /**
     * 登录方式 {@link SupplierLoginTypeEnum#getValue()}
     */
    @NotNull
    private Integer loginType;

    /**
     * 密码
     */
    private String pwd;

    /**
     * 图形验证码id
     */
    private String captchaId;

    /**
     * 图形验证码
     */
    private String captcha;

    /**
     * 手机验证码
     */
    private String smsCode;
}
