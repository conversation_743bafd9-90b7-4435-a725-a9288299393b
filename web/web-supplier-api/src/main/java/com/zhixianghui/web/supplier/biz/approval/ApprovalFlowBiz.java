package com.zhixianghui.web.supplier.biz.approval;

import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 审批服务逻辑biz
 * @date 2020/11/20
 **/
@Service
public class ApprovalFlowBiz {

    @Reference
    private ApprovalFlowFacade approvalFlowFacade;

    public void updateExtInfo(UpdateExtInfoVo updateExtInfoVo, SupplierStaffVO staffVO) {
        updateExtInfoVo.setHandlerId(staffVO.getId());
        updateExtInfoVo.setHandlerName(staffVO.getName());
        updateExtInfoVo.setPlatform(PlatformSource.SUPPLIER.getValue());
        approvalFlowFacade.updateExtInfo(updateExtInfoVo,false);
    }

    public ApprovalFlow getById(Long approvalFlowId,Long handlerId){
        return approvalFlowFacade.getByIdAndHandlerId(approvalFlowId,handlerId,false);
    }
}

