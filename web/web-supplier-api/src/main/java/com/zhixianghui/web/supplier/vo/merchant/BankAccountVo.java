package com.zhixianghui.web.supplier.vo.merchant;



import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2020/11/9
 **/
@Data
public class BankAccountVo {
    /**
     * 商户编号
     */
    @NotEmpty
    private String mchNo;

    /**
     * 银行账户
     */
    @NotEmpty
    @Length(max = 50)
    private String accountNo;

    /**
     * 银行名称
     */
    @NotEmpty
    @Length(max = 50)
    private String bankName;

    /**
     * 银行行号
     */
    @NotEmpty
    @Length(max = 50)
    private String bankChannelNo;
}
