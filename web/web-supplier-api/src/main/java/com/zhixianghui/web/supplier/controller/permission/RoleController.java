package com.zhixianghui.web.supplier.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.RoleTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.user.supplier.SupplierRole;
import com.zhixianghui.facade.merchant.service.supplier.SupplierRoleFacade;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierRoleVo;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.Logger;
import com.zhixianghui.web.supplier.vo.permission.AssignFunctionVO;
import com.zhixianghui.web.supplier.vo.permission.FunctionVO;
import com.zhixianghui.web.supplier.vo.permission.RoleResultVO;
import com.zhixianghui.web.supplier.vo.permission.RoleVO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色管理
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("role")
@Log4j2
public class RoleController {

    @Reference
    private SupplierRoleFacade supplierRoleFacade;

    /**
     * 分页查询角色
     * @param roleName
     * @param pageCurrent
     * @param pageSize
     * @param mchNo
     * @return
     */
    @Permission("pms:role:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<Object>>> listPage(@RequestParam(required = false) String roleName,
                                                         @RequestParam(defaultValue = "1") Integer pageCurrent,
                                                         @RequestParam(defaultValue = "10") Integer pageSize,
                                                         @CurrentMainstayNo String mchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", roleName);

        PageResult<List<SupplierRoleVo>> result = supplierRoleFacade.listPage(mchNo, map, PageParam.newInstance(pageCurrent, pageSize));
        if (result == null || CollectionUtils.isEmpty(result.getData())) {
            return RestResult.success(null);
        }
        // 处理预置角色的统计
        result.getData().forEach(item -> {
            if (item.getRoleType() != null && item.getRoleType() != RoleTypeEnum.CUSTOMIZE.getType()
                    && item.getEmployerNumber() != null && item.getEmployerNumber() != 0) {
                item.setEmployerNumber(supplierRoleFacade.count(item, mchNo));
            }
        });


        return RestResult.success(PageResult.newInstance(result.getData().stream().map(RoleResultVO::buildVo).collect(Collectors.toList()),
                result.getPageCurrent(), result.getPageSize(), result.getTotalRecord()));
    }

    /**
     * 查询全部角色
     */
    @Permission("pms:role:view")
    @RequestMapping("listAll")
    public RestResult<List<RoleVO>> listAll(@CurrentMainstayNo String mchNo) {
        return RestResult.success(supplierRoleFacade.listAll(mchNo).stream().map(RoleVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 新增角色
     */
    @Permission("pms:role:add")
    @PostMapping("add")
    @Logger(type = OperateLogTypeEnum.CREATE, action = "新增角色")
    public RestResult<String> add(@Validated @RequestBody RoleVO roleVO, @CurrentMainstayNo String mchNo) {
        SupplierRole role = RoleVO.buildDto(roleVO, mchNo);
        supplierRoleFacade.create(role);
        return RestResult.success("新增角色成功");
    }

    /**
     * 更新角色
     */
    @Permission("pms:role:edit")
    @PostMapping("edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "更新角色")
    public RestResult<String> edit(@RequestParam Long id,
                                   @RequestParam(required = false) String remark,
                                   @CurrentMainstayNo String mchNo) {
        if(remark != null && remark.length() >= 50){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("描述长度不可以超过50");
        }
        SupplierRole role = supplierRoleFacade.getById(mchNo, id);
        if (role.getRoleType() != RoleTypeEnum.PRESET.getType()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("当前角色不允许编辑");
        }
        role.setRemark(remark == null ? "" : remark);
        supplierRoleFacade.update(role);
        return RestResult.success("更新角色成功");
    }

    /**
     * 删除角色
     */
    @Permission("pms:role:delete")
    @PostMapping("delete")
    @Logger(type = OperateLogTypeEnum.DELETE, action = "删除角色")
    public RestResult<String> delete(@RequestParam Long id, @CurrentMainstayNo String mchNo) {
        supplierRoleFacade.deleteById(mchNo, id);
        return RestResult.success("删除角色成功");
    }

    /**
     * 查询角色关联的菜单
     * @param roleId    角色id
     */
    @Permission("pms:role:view")
    @GetMapping("listRoleFunction")
    public RestResult<List<FunctionVO>> listRoleFunction(@RequestParam Long roleId,
                                                         @CurrentMainstayNo String mchNo) {
        return RestResult.success(supplierRoleFacade.listFunctionByRoleId(mchNo, roleId)
                .stream().map(FunctionVO::buildVo).collect(Collectors.toList()));
    }

    /**
     * 为角色分配功能
     */
    @Permission("pms:role:edit")
    @PostMapping("assignFunction")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "为角色分配功能")
    public RestResult<String> assignFunction(@RequestBody @Valid AssignFunctionVO vo,
                                             @CurrentMainstayNo String mchNo) {
        supplierRoleFacade.updateFunction(mchNo, vo.getRoleId(),
                vo.getFunctionIds() == null ? new ArrayList<>() : vo.getFunctionIds());
        return RestResult.success("操作成功");
    }
}
