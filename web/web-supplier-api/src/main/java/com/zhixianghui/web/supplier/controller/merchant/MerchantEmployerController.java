package com.zhixianghui.web.supplier.controller.merchant;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.export.vo.ExportVo;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.vo.InvoiceCategoryVo;
import com.zhixianghui.facade.merchant.vo.MerchantSalerQueryVO;
import com.zhixianghui.facade.merchant.vo.MerchantSupplierInfoVo;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentOperatorVO;
import com.zhixianghui.web.supplier.annotation.Logger;
import com.zhixianghui.web.supplier.utils.BeanToMapUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商户管理
 * <AUTHOR>
 * @date 2021-03-26
 **/
@RestController
@RequestMapping("merchantEmployer")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MerchantEmployerController {

    @Reference
    private MerchantBankAccountFacade bankAccountFacade;
    @Reference
    private MerchantEmployerQuoteFacade quoteFacade;
    @Reference
    private MerchantEmployerPositionFacade positionFacade;
    @Reference
    private MerchantEmployerCooperateFacade employerCooperateFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantEmployerMainFacade employerMainFacade;
    @Reference
    private MerchantFileFacade merchantFileFacade;
    @Reference
    private MerchantInvoiceInfoFacade invoiceInfoFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private MerchantEmployerFacade merchantEmployerFacade;
    @Reference
    private AgreementFacade agreementFacade;

    //屏蔽字段
    private final static List<String> BAN_PARAM = Arrays.asList(new String[]{"salerName","salerId","agentNo","agentName",
            "contactName","contactPhone","contactEmail","servicePhone"});

    /**
     * 获取商户基本信息
     * @param mchNo
     * @return
     */
    @GetMapping("getBaseInfo")
    @Permission("merchantEmployer:base:view")
    public RestResult<Map<String,Object>> getBaseInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantQueryFacade.getBaseInfo(mchNo);
        BAN_PARAM.stream().forEach(x-> maps.remove(x));
        return RestResult.success(maps);
    }

    /**
     * 获取商户合作信息
     * @param mchNo
     * @return
     */
    @GetMapping("getCooperateInfo")
    @Permission("merchantEmployer:coop:view")
    public RestResult<Map<String,Object>> getCooperateInfo(@CurrentMainstayNo String mainstayNo,@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantEmployerFacade.getCooperateInfoWithSupplier(mainstayNo,mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取产品报价单
     * @param mchNo
     * @return
     */
    /**
     * 获取产品报价单
     * @param mchNo
     * @return
     */
    @GetMapping("getQuoteInfo")
    @Permission("merchantEmployer:quote:view")
    public RestResult getQuoteInfo(@RequestParam String mchNo,@CurrentMainstayNo String mainstayNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",mchNo);
        paramMap.put("supplierNo",mainstayNo);
        paramMap.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        //供应商只需要看到已生效的报价单
        List<MerchantEmployerQuote> quoteList = quoteFacade.getQuoteListWithSupplier(mchNo,paramMap);
        return RestResult.success(quoteList);
    }

    /**
     * 获取商户主体信息
     * @param mchNo
     * @return
     */
    @GetMapping("getMainInfo")
    @Permission("merchantEmployer:main:view")
    public RestResult<Map<String,Object>> getMainInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = employerMainFacade.getMainInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取经营信息
     * @param mchNo
     * @return
     */
    @GetMapping("getBusinessInfo")
    @Permission("merchantEmployer:business:view")
    public RestResult<Map<String,Object>> getBusinessInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = employerMainFacade.getBusinessInfo(mchNo);
        BAN_PARAM.stream().forEach(x-> maps.remove(x));
        return RestResult.success(maps);
    }

    /**
     * 获取账号信息
     * @param mchNo
     * @return
     */
    @GetMapping("getAccountInfo")
    @Permission("merchantEmployer:account:view")
    public RestResult<Map<String,Object>> getAccountInfo(@CurrentMainstayNo String mainstayNo,@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = bankAccountFacade.mainstayGetAccountInfo(mainstayNo,mchNo);
        return RestResult.success(maps);
    }

    @GetMapping("getAgreementInfo")
    @Permission("merchantEmployer:agreement:view")
    public RestResult getAgreementInfo(@RequestParam String mchNo,@CurrentMainstayNo String mainstayNo){
        Map<String,Object> maps = new HashMap<>();
        maps.put("mchNo",mchNo);
        maps.put("mainstayNo",mainstayNo);
        List<Agreement> pageResult = agreementFacade.getAgreementPageByMchNoAndMainstayNo(maps);
        return RestResult.success(pageResult);
    }
}
