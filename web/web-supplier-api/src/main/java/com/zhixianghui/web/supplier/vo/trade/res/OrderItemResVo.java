package com.zhixianghui.web.supplier.vo.trade.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-09 14:28
 **/
@Data
public class OrderItemResVo {
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 用工企业编号
     */
    private String employerNo;

    /**
     * 用工企业名称
     */
    private String employerName;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 通道名
     */
    private String channelName;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 收款账户
     */
    private String receiveAccountNo;

    /**
     * 订单明细任务金额
     */
    private BigDecimal orderItemTaskAmount;

    /**
     * 订单明细实发金额
     */
    private BigDecimal orderItemNetAmount;

    /**
     * 订单明细代征主体服务费
     */
    private BigDecimal orderItemFee;

    /**
     * 订单明细(总)金额
     */
    private BigDecimal orderItemAmount;

    /**
     * 订单明细个税金额
     */
    private BigDecimal orderItemTaxAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单明细状态
     */
    private Integer orderItemStatus;

    /**
     * 错误描述
     */
    private String errorDesc;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行简码
     */
    private String bankCode;

    /**
     * 银行预留手机号
     */
    private String receivePhoneNo;


    private String appid;

    private String channelNo;

    private String productNo;

    private String productName;

    private List<String> workerBillFilePath;

    /**
     * 渠道流水号
     */
    private String channelTrxNo;
}
