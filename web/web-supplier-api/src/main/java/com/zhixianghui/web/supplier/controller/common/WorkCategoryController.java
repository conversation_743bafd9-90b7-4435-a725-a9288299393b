package com.zhixianghui.web.supplier.controller.common;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.config.WorkCategory;
import com.zhixianghui.facade.common.service.WorkCategoryFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName WorkCategoryController
 * @Description TODO
 * @Date 2022/6/13 14:26
 */
@RestController
@RequestMapping("workCategory")
public class WorkCategoryController {

    @Reference
    private WorkCategoryFacade workCategoryFacade;

    @GetMapping("listAll")
    public RestResult<List<WorkCategory>> listAll() {
        List<WorkCategory> categoryList = workCategoryFacade.listAll();
        return RestResult.success(categoryList);
    }
}
