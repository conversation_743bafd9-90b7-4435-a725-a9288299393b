package com.zhixianghui.web.supplier.biz.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.web.supplier.vo.invoice.ExportDetailGroupVo;
import com.zhixianghui.web.supplier.vo.invoice.ExportDetailVo;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ExportBiz
 * @Description TODO
 * @Date 2022/2/24 15:28
 */
@Service
public class ExportBiz {

    public <T> void export(HttpServletResponse response, String fileName, List allGroupData, List allData, Class<ExportDetailGroupVo> clazz0, Class<ExportDetailVo> clazz1){
        ExcelWriter excelWriter = null;
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

            excelWriter = EasyExcel.write(response.getOutputStream()).build();
            if (allData != null && !allData.isEmpty()) {
                final WriteSheet writeSheet1 = EasyExcel.writerSheet(1, "关联订单明细").head(clazz1).build();
                excelWriter.write(allData, writeSheet1);
            }

            if (allGroupData != null && !allGroupData.isEmpty()) {
                final WriteSheet writeSheet0 = EasyExcel.writerSheet(0, "开票清单").head(clazz0).build();
                excelWriter.write(allGroupData, writeSheet0);
            }

            excelWriter.finish();
        }catch (Exception e){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出失败，系统异常");
        }finally {
            if (excelWriter != null){
                excelWriter.finish();
            }
        }
    }
}
