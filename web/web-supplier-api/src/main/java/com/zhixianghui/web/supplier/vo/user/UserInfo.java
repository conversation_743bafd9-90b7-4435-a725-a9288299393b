package com.zhixianghui.web.supplier.vo.user;

import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.web.supplier.vo.permission.FunctionVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UserInfo {

    /**
     * 当前登录的操作员
     */
    @NotNull
    private SupplierOperatorVO operator;

    /**
     * 当前员工
     */
    private SupplierStaffVO staff;

    /**
     * 当前选择的商户
     */
    private String mchNo;

    /**
     * 关联的功能菜单
     */
    private List<FunctionVO> functions;
}
