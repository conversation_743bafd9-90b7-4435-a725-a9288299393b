package com.zhixianghui.web.supplier.biz.sign;

import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.web.supplier.utils.BeanToMapUtil;
import com.zhixianghui.web.supplier.vo.sign.SignRecordQueryVo;
import com.zhixianghui.web.supplier.vo.sign.SignRecordResVo;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/18 15:37
 **/
@Service
public class SignBiz {
    @Reference
    private SignRecordFacade signRecordFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    public PageResult<List<SignRecordResVo>> listPage(SignRecordQueryVo queryVo, PageParam pageParam, String mainstayNo) {
        Map<String,Object> paramMap = BeanUtil.toMap(queryVo);
        paramMap.put("mainstayNo",mainstayNo);
        PageResult<List<SignRecord>> pageResult = signRecordFacade.listPage(paramMap,pageParam);
        if(CollectionUtils.isEmpty(pageResult.getData())){
            return PageResult.newInstance(pageParam,null);
        }else {
            List<SignRecordResVo> list = pageResult.getData().stream().map(
                    signRecord ->{
                        SignRecordResVo signRecordResVo = new SignRecordResVo();
                        BeanUtils.copyProperties(signRecord,signRecordResVo);
                        signRecordResVo.setReceiveName(signRecord.getReceiveNameDesensitize());
                        signRecordResVo.setReceiveIdCardNo(signRecord.getReceiveIdCardNoDesensitize());
                        signRecordResVo.setReceivePhoneNo(signRecord.getReceivePhoneNoDesensitize());
                        return signRecordResVo;
                    }
            ).collect(Collectors.toList());
            return PageResult.newInstance(list,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
        }
    }
    /**
     * 签约记录导出
     * @param queryVo
     * @param staffVO
     */
    public void exportSignRecord(SignRecordQueryVo queryVo, SupplierStaffVO staffVO) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(queryVo);
        paramMap.put("mainstayNo",staffVO.getMchNo());

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(staffVO.getMchNo());
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());

        record.setFileName(ReportTypeEnum.SIGN_RECORD_SUP.getFileName());
        record.setReportType(ReportTypeEnum.SIGN_RECORD_SUP.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.SIGN_RECORD_SUP.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
    }
}
