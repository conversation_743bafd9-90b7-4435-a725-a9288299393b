package com.zhixianghui.web.supplier.biz.offline;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.service.OfflineOrderFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.web.supplier.utils.BeanToMapUtil;
import com.zhixianghui.web.supplier.vo.PageVo;
import com.zhixianghui.web.supplier.vo.trade.req.OrderQueryVo;
import com.zhixianghui.web.supplier.vo.trade.res.OrderResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OfflineOrderBiz {

    @Reference
    private OfflineOrderFacade orderFacade;
    @Reference
    private OfflineOrderItemFacade orderItemFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    public PageResult<List<OrderResVo>> listOrderPage(OrderQueryVo orderQueryVo, PageVo pageVo, String mainstayNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderQueryVo);
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        Page<OfflineOrder> pageResult = orderFacade.pageOrder(new Page<>(pageParam.getPageCurrent(),pageParam.getPageSize()),paramMap);
        List<OrderResVo> list = pageResult.getRecords().stream().map(
                order->{
                    OrderResVo orderResVo = new OrderResVo();
                    BeanUtils.copyProperties(order,orderResVo);
                    return orderResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list,Long.valueOf(pageResult.getCurrent()).intValue(),Long.valueOf(pageResult.getSize()).intValue(),pageResult.getTotal());
    }
}
