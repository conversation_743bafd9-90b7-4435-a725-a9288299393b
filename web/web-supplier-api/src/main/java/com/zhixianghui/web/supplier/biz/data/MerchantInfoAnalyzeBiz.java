package com.zhixianghui.web.supplier.biz.data;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.entity.MerchantStat;
import com.zhixianghui.facade.trade.service.FreelanceStatFacade;
import com.zhixianghui.facade.trade.service.MerchantStatFacade;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;
import com.zhixianghui.facade.trade.vo.MerchantStatVo;
import com.zhixianghui.web.supplier.vo.merchant.DataAnalyzeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Method;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2021/7/27 14:37
 */
@Slf4j
@Service
public class MerchantInfoAnalyzeBiz {

    @Reference(timeout = 30000, methods = {@Method(name = "freelanceStat2", timeout = 30000)})
    private FreelanceStatFacade freelanceStat;
    @Reference
    private MerchantStatFacade merchantStat;
    @Reference
    private SignRecordFacade signRecordFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private EmployerMainstayRelationFacade mainstayRelationFacade;

    public PageResult<List<FreelanceStatVo>> freelanceStat(DataAnalyzeVo analyzeVo, String sortColumn, String mchNo) {
        // 查询该商户下是否有用工企业, 没有直接返回
        List<String> employerList = getEmployerList(mchNo);
        if (CollectionUtils.isEmpty(employerList)) {
            return PageResult.newInstance(new ArrayList<>(), PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()), 0L);
        }
        if (StringUtils.isNotBlank(analyzeVo.getIdCard())) {
            analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
            analyzeVo.setIdCard(null);
        }
        if (StringUtils.isNotBlank(analyzeVo.getReceiveName())) {
            analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
            analyzeVo.setReceiveName(null);
        }
        log.info("自由职业者列表统计查询参数 : {}-{}", JSONObject.toJSON(analyzeVo), JSONObject.toJSONString(employerList));
        Map<String, Object> param = BeanUtil.toMap(analyzeVo);
        param.put("employerList", employerList);
        param.put("mainstayNo", mchNo);
        if (StringUtils.isNotBlank(sortColumn)) {
            param.put("sortColumns", sortColumn);
        }

        PageResult<List<Map<String, Object>>> list = freelanceStat.freelanceStat2(param, PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()));
        if (list == null || CollectionUtils.isEmpty(list.getData())) {
            return PageResult.newInstance(PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()), new ArrayList<>());
        }

        List<FreelanceStatVo> result = new ArrayList<>();
        for (Map<String, Object> stat : list.getData()) {
            FreelanceStatVo statVo = new FreelanceStatVo();
            BeanUtil.mapToObject(statVo,stat);
            if (stat.containsKey("receiveIdCardNo")) {
                statVo.setReceiveIdCardNo(AESUtil.decryptECB(String.valueOf(stat.get("receiveIdCardNo")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (stat.containsKey("receiveName")) {
                statVo.setReceiveName(AESUtil.decryptECB(String.valueOf(stat.get("receiveName")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (stat.containsKey("receivePhoneNo")) {
                statVo.setReceivePhoneNo(AESUtil.decryptECB(String.valueOf(stat.get("receivePhoneNo")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (StringUtils.isBlank(String.valueOf(stat.get("receivePhoneNo")))) {
                statVo.setPhone(OperationEnum.UN_PHONE.getOperation());
            } else {
                statVo.setPhone(OperationEnum.PHONE.getOperation());
            }
            if (Objects.isNull(stat.get("idCardBackUrl"))&&Objects.isNull(stat.get("idCardFrontUrl"))&&Objects.isNull(stat.get("idCardCopyUrl"))) {
                statVo.setIdCard(0);
            }else {
                statVo.setIdCard(1);
            }
            if (stat.get("signStatus")!=null && Integer.parseInt(String.valueOf(stat.get("signStatus")))==SignStatusEnum.SIGN_SUCCESS.getValue()){
                statVo.setSignRecord(1);
            }
            statVo.setReceiverOrder(null);
            result.add(statVo);
        }
        return PageResult.newInstance(result, PageParam.newInstance(list.getPageCurrent(), list.getPageSize()), list.getTotalRecord());
    }

    private List<String> getEmployerList(String mchNo) {
        List<EmployerMainstayRelation> list = mainstayRelationFacade.listBy(new HashMap<String, Object>() {{
            put("mainstayNo", mchNo);
        }});
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(EmployerMainstayRelation :: getEmployerNo).collect(Collectors.toList());
    }

    public FreelanceStatVo countFreelance(DataAnalyzeVo analyzeVo, String mchNo) {
        List<String> employerList = getEmployerList(mchNo);
        if (CollectionUtils.isEmpty(employerList)) {
            return new FreelanceStatVo();
        }
        if (StringUtils.isNotBlank(analyzeVo.getReceiveName())) {
            analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
        }
        if (StringUtils.isNotBlank(analyzeVo.getIdCard())) {
            analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
        }
        log.info("自由者职业者统计查询参数 : {}-{}", JSONObject.toJSON(analyzeVo), JSONObject.toJSONString(employerList));
        Map<String, Object> param = BeanUtil.toMap(analyzeVo);
        param.put("employerList", employerList);
        param.put("mainstayNo", mchNo);
        return freelanceStat.merchantStatCount(param);
    }

    public PageResult<List<MerchantStatVo>> merchantStat(DataAnalyzeVo analyzeVo, String sortColumn, String mchNo) {
        List<MerchantStatVo> result = new ArrayList<>();
        // 用工企业数据为空, 直接返回
        List<String> employerList = getEmployerList(mchNo);
        if (CollectionUtils.isEmpty(employerList)) {
            return PageResult.newInstance(PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()), result);

        }

        Map<String, Object> param = BeanUtil.toMap(analyzeVo);
        if (StringUtils.isNotBlank(sortColumn)) {
            param.put("sortColumns", sortColumn);
        }

        param.put("employerList", employerList);
        param.put("mainstayNo", mchNo);
        PageResult<List<MerchantStat>> list = merchantStat.merchantStat(param, PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()));
        if (list == null || CollectionUtils.isEmpty(list.getData())) {
            return PageResult.newInstance(PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()), result);
        }
        for (MerchantStat stat : list.getData()) {
            MerchantStatVo statVo = new MerchantStatVo();
            BeanUtil.copyProperties(stat, statVo);
            statVo.setSaleId(null);
            statVo.setSaleName("");
            result.add(statVo);
        }
        return PageResult.newInstance(result, PageParam.newInstance(list.getPageCurrent(), list.getPageSize()), list.getTotalRecord());
    }

    public MerchantStatVo countMerchant(DataAnalyzeVo analyzeVo, String mchNo) {
        // 用工企业数据为空, 直接返回
        List<String> employerList = getEmployerList(mchNo);
        if (CollectionUtils.isEmpty(employerList)) {
            return new MerchantStatVo();

        }
        analyzeVo.setMainstayNo(mchNo);
        log.info("用工企业统计查询参数 : {}", JSONObject.toJSON(analyzeVo));
        Map<String, Object> param = BeanUtil.toMap(analyzeVo);
        param.put("employerList", employerList);

        MerchantStatVo vo = merchantStat.count(param);
        Integer freelanceCount = freelanceStat.freelanceCount(param);
        vo.setFreelanceCount(freelanceCount + "");
        return vo;
    }

    public void downloadCertificateFile(DataAnalyzeVo analyzeVo, SupplierStaffVO supplierStaffVO) {
        // 用工企业数据为空, 直接返回
        List<String> employerList = getEmployerList(supplierStaffVO.getMchNo());
        if (CollectionUtils.isEmpty(employerList)) {
            return;

        }
        analyzeVo.setMainstayNo(supplierStaffVO.getMchNo());
        Map<String, Object> paramMap = BeanUtil.toMap(analyzeVo);
        paramMap.put("employerList", employerList);

        if (paramMap.containsKey("receiveName")) {
            paramMap.put("receiveNameMd5", MD5Util.getMixMd5Str((String) paramMap.get("receiveName")));
            paramMap.remove("receiveName");
        }
        if (paramMap.containsKey("idCard")) {
            paramMap.put("receiveIdCardNoMd5", MD5Util.getMixMd5Str((String) paramMap.get("idCard")));
            paramMap.remove("idCard");
        }

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(supplierStaffVO.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.DOWNLOAD_ID_CARD.getFileName());
        record.setReportType(ReportTypeEnum.DOWNLOAD_ID_CARD.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setMchNo(supplierStaffVO.getMchNo());
        exportRecordFacade.insert(record);
    }

    public boolean merchantInfoExport(DataAnalyzeVo analyzeVo, SupplierStaffVO staffVO) {
        // 用工企业数据为空, 直接返回
        List<String> employerList = getEmployerList(staffVO.getMchNo());
        if (CollectionUtils.isEmpty(employerList)) {
            return false;

        }

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.MERCHANT_STAT_INFO_EXPORT_SUPLIER.getFileName());
        record.setReportType(ReportTypeEnum.MERCHANT_STAT_INFO_EXPORT_SUPLIER.getValue());
        record.setMchNo(staffVO.getMchNo());
        analyzeVo.setMainstayNo(staffVO.getMchNo());
        Map<String, Object> paramMap = BeanUtil.toMap(analyzeVo);
        paramMap.put("employerList", employerList);

        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_STAT_INFO_EXPORT_SUPLIER.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return true;
    }

    public boolean freelanceExport(DataAnalyzeVo analyzeVo, SupplierStaffVO supplierStaffVO, String mchNo) {
        // 用工企业数据为空, 直接返回
        List<String> employerList = getEmployerList(mchNo);
        if (CollectionUtils.isEmpty(employerList)) {
            return false;

        }

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(supplierStaffVO.getPhone());
        record.setSystemType(SystemTypeEnum.SUPPLIER_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.FREELANCE_STAT_Export.getFileName());
        record.setReportType(ReportTypeEnum.FREELANCE_STAT_Export.getValue());
        record.setMchNo(supplierStaffVO.getMchNo());

        if (StringUtils.isNotBlank(analyzeVo.getIdCard())) {
            analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
            analyzeVo.setIdCard(null);
        }
        if (StringUtils.isNotBlank(analyzeVo.getReceiveName())) {
            analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
            analyzeVo.setReceiveName(null);
        }
        analyzeVo.setMainstayNo(mchNo);
        Map<String, Object> paramMap = BeanUtil.toMap(analyzeVo);
        paramMap.put("employerList", employerList);

        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.FREELANCE_STAT_Export.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            if (!"receiverOrder".equals(x.getCode())) {
                info.setFieldCode(x.getCode());
                info.setFieldDesc(x.getDesc());
                record.getFieldInfoList().add(info);
            }

        });

        exportRecordFacade.insert(record);
        return true;
    }
}
