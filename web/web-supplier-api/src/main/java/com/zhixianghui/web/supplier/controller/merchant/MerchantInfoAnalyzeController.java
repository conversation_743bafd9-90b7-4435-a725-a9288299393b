package com.zhixianghui.web.supplier.controller.merchant;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierOperatorVO;
import com.zhixianghui.facade.merchant.vo.supplier.SupplierStaffVO;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;

import com.zhixianghui.facade.trade.vo.MerchantStatVo;
import com.zhixianghui.web.supplier.annotation.CurrentMainstayNo;
import com.zhixianghui.web.supplier.annotation.CurrentOperatorVO;
import com.zhixianghui.web.supplier.annotation.CurrentStaffVo;
import com.zhixianghui.web.supplier.biz.data.MerchantInfoAnalyzeBiz;
import com.zhixianghui.web.supplier.vo.merchant.DataAnalyzeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/7/27 14:19
 */
@Slf4j
@RestController
@RequestMapping("analyze")
public class MerchantInfoAnalyzeController {

    private static final String[] FREELANCE_SORT_COLUMN = {"orderItemNetAmount"};
    private static final String[] MERCHANT_SORT_COLUMN = {"orderItemAmount", "orderItemNetAmount", "receiverNumber", "orderAmount"};
    public static final String[] ORDER = {"asc", "desc"};
    public static final Map<String, Object> SORT_MAP = new HashMap<>();
    static {
        SORT_MAP.put("orderItemNetAmount", "ORDER_ITEM_NET_AMOUNT");
        SORT_MAP.put("orderItemAmount", "ORDER_ITEM_AMOUNT");
        SORT_MAP.put("receiverNumber", "RECEIVER_NUMBER");
        SORT_MAP.put("orderAmount", "ORDER_AMOUNT");
    }

    @Autowired
    private MerchantInfoAnalyzeBiz analyzeBiz;


    @RequestMapping("freelanceList")
    public RestResult<PageResult<List<FreelanceStatVo>>> freelanceList(@RequestBody DataAnalyzeVo analyzeVo, @CurrentMainstayNo String mchNo) {
        LimitUtil.notEmpty(mchNo, "个人交付数据列表查询, 商户号不能为空");
        String sortColumn = checkSort(analyzeVo, FREELANCE_SORT_COLUMN);
        return RestResult.success(analyzeBiz.freelanceStat(analyzeVo, sortColumn, mchNo));
    }

    private String checkSort(DataAnalyzeVo analyzeVo, String[] sortColumnArray) {
        String sortColumn = analyzeVo.getSortColumn();
        if (StringUtils.isBlank(sortColumn) || StringUtils.isBlank(analyzeVo.getOrder())) {
            return null;
        }

        if (Arrays.asList(ORDER).contains(analyzeVo.getOrder()) &&
                Arrays.asList(sortColumnArray).contains(analyzeVo.getSortColumn())) {
            return SORT_MAP.get(analyzeVo.getSortColumn()) + " " + analyzeVo.getOrder();
        }
        throw CommonExceptions.PARAM_INVALID.newWithErrMsg("排序字段或规则不合法");
    }

    @RequestMapping("countFreelance")
    public RestResult<FreelanceStatVo> countFreelance(@RequestBody DataAnalyzeVo analyzeVo, @CurrentMainstayNo String mchNo) {
        LimitUtil.notEmpty(mchNo, "个人交付数据统计查询, 商户号不能为空");
        return RestResult.success(analyzeBiz.countFreelance(analyzeVo, mchNo));
    }

    @RequestMapping("merchantList")
    public RestResult<PageResult<List<MerchantStatVo>>> list(@RequestBody DataAnalyzeVo analyzeVo, @CurrentMainstayNo String mchNo) {
        LimitUtil.notEmpty(mchNo, "企业发单数据查询, 商户号不能为空");
        String sortColumn = checkSort(analyzeVo, MERCHANT_SORT_COLUMN);
        return RestResult.success(analyzeBiz.merchantStat(analyzeVo, sortColumn, mchNo));
    }

    @RequestMapping("countMerchant")
    public RestResult<MerchantStatVo> countMerchant(@RequestBody DataAnalyzeVo analyzeVo, @CurrentMainstayNo String mchNo) {
        LimitUtil.notEmpty(mchNo, "企业发单数据统计, 商户号不能为空");
        return RestResult.success(analyzeBiz.countMerchant(analyzeVo, mchNo));
    }

    /**
     * 批量下载凭证文件
     *
     * @return 提示
     */
    @RequestMapping("idCard/batchDownload")
    public RestResult<String> downloadCertificateFile(@RequestBody DataAnalyzeVo analyzeVo, @CurrentStaffVo SupplierStaffVO currentStaffVO) {
        LimitUtil.notEmpty(currentStaffVO.getMchNo(), "批量下载凭证文件, 商户号不能为空");
        analyzeBiz.downloadCertificateFile(analyzeVo, currentStaffVO);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @RequestMapping("merchantInfoExport")
    public RestResult<String> merchantInfoExport(@RequestBody DataAnalyzeVo analyzeVo, @CurrentStaffVo SupplierStaffVO currentStaffVO) {
        LimitUtil.notEmpty(currentStaffVO.getMchNo(), "企业发单数据导出, 商户号不能为空");
        if (analyzeBiz.merchantInfoExport(analyzeVo, currentStaffVO)) {
            return RestResult.success("导出成功");
        }
        return RestResult.error("用工企业数据为空");
    }

    @RequestMapping("freelanceExport")
    public RestResult<String> freelanceExport(@RequestBody DataAnalyzeVo analyzeVo, @CurrentStaffVo SupplierStaffVO currentStaffVO, @CurrentMainstayNo String mchNo) {
        LimitUtil.notEmpty(mchNo, "个人交付数据导出, 商户号不能为空");
        if (analyzeBiz.freelanceExport(analyzeVo, currentStaffVO, mchNo)) {
            return RestResult.success("导出成功");
        }
        return RestResult.error("导出失败");
    }



}
