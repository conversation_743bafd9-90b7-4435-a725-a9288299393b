package com.zhixianghui.web.employer.vo.order;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 统计Vo
 * @date 2020-11-05 11:43
 **/
@Data
@Builder
public class CountVo {

    /**
     * 请求笔数
     */
    private Integer requestCount;

    /**
     * 请求(总)金额
     */
    private BigDecimal requestNetAmount;

//    /**
//     * 已受理笔数
//     */
//    private Integer acceptedCount;
//
//    /**
//     * 已受理(总)实发金额
//     */
//    private BigDecimal acceptedNetAmount;
//
//    /**
//     * 已受理代征主体服务费
//     */
//    private Integer acceptedFee;
//
//    /**
//     * 已受理(总)订单金额
//     */
//    private BigDecimal acceptedOrderAmount;
//
//    /**
//     * 成功笔数
//     */
//    private Integer successCount;
//
//    /**
//     * 成功实发金额
//     */
//    private BigDecimal successNetAmount;

    /**
     * 失败笔数
     */
    private Integer failCount;

    /**
     * 失败实发金额
     */
    private BigDecimal failNetAmount;
}
