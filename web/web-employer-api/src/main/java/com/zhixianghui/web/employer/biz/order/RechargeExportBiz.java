package com.zhixianghui.web.employer.biz.order;

import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class RechargeExportBiz {

    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    public void exportRechargeRecord(Map<String,Object> paramMap, String loginName,String mchNo) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(loginName);
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getFileName());
        record.setReportType(ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setMchNo(mchNo);

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
    }

    public void exportRechargeRecordZip(Map<String, Object> paramMap, String loginName, String employerNo) {
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(loginName);
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName("批量下载回单");
        record.setReportType(ReportTypeEnum.TRADE_RECHARGE_RECORD_PMS.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));
        record.setMchNo(employerNo);
        exportRecordFacade.insert(record);
    }
}
