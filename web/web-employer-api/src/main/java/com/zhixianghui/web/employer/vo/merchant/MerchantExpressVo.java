package com.zhixianghui.web.employer.vo.merchant;



import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2020/11/9
 **/
@Data
public class MerchantExpressVo {
    /**
     * 收件人
     */
    @NotEmpty(message = "收件人 不能为空")
    @Length(max = 20, message = "收件人 长度不能超过50")
    private String consignee;

    /**
     * 联系电话
     */
    @NotEmpty(message = "联系电话 不能为空")
    @Length(max = 20, message = "联系电话 长度不能超过50")
    private String telephone;

    /**
     * 省
     */
    @NotEmpty(message = "省 不能为空")
    @Length(max = 15, message = "省 长度不能超过50")
    private String province;

    /**
     * 市
     */
    @Length(max = 15, message = "市 长度不能超过50")
    private String city;

    /**
     * 区/县
     */
    @Length(max = 15, message = "区/县 长度不能超过50")
    private String county;

    /**
     * 详细地址
     */
    @NotEmpty(message = "详细地址 不能为空")
    @Length(max = 200, message = "详细地址 长度不能超过50")
    private String address;
}
