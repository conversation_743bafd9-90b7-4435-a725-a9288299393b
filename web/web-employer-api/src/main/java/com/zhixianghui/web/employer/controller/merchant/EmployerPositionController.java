package com.zhixianghui.web.employer.controller.merchant;

import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerPosition;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用工企业岗位信息
 * <AUTHOR>
 * @date 2020/9/28
 **/
@RestController
@RequestMapping("employerPosition")
@Slf4j
public class EmployerPositionController {

    @Reference
    private MerchantEmployerPositionFacade positionFacade;

    @RequestMapping("listPositionWithMainstayNo")
    public RestResult listPositionWithMainstayNo(@CurrentMchNo String mchNo, @RequestParam("mainstayNo") String mainstayNo){
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("mchNo",mchNo);
        paramMap.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        List<MerchantEmployerPosition> positionList = positionFacade.listByMchNoWithQuote(paramMap);
        return RestResult.success(buildResult(positionList));
    }

    /**
     * 获取岗位信息
     * @return .
     */
    @RequestMapping("listPosition")
    public RestResult<List<Map<String, Object>>> listPosition(@CurrentMchNo String mchNo) {
        List<MerchantEmployerPosition> positionList = positionFacade.listByMchNo(mchNo);
        return RestResult.success(buildResult(positionList));
    }

    private List<Map<String,Object>> buildResult(List<MerchantEmployerPosition> positionList) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(positionList)){
            for(MerchantEmployerPosition position : positionList){
                Map<String, Object> map = new HashMap<>();
                map.put("serviceDesc", position.getServiceDesc());
                map.put("workCategoryName", position.getWorkCategoryName());
                map.put("workCategoryCode", position.getWorkCategoryCode());
                map.put("invoiceCategoryList", position.getJsonEntity().getInvoiceCategoryList());
                resultList.add(map);
            }
        }
        return resultList;
    }

}
