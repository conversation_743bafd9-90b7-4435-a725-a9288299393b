package com.zhixianghui.web.employer.biz.order;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.EasyExcel;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.enums.RechargeStatusEnum;
import com.zhixianghui.facade.trade.enums.RechargeTypeEnum;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.service.RechargeQueryFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.employer.biz.async.AsyncBiz;
import com.zhixianghui.web.employer.biz.order.excel.BankGrantRow;
import com.zhixianghui.web.employer.biz.order.excel.GrantDataListener;
import com.zhixianghui.web.employer.biz.order.excel.TestGrantDataListener;
import com.zhixianghui.web.employer.exception.MchOrderNoException;
import com.zhixianghui.web.employer.utils.MultipartFileUtil;
import com.zhixianghui.web.employer.vo.order.CountVo;
import com.zhixianghui.web.employer.vo.order.req.OrderAcceptReqVo;
import com.zhixianghui.web.employer.vo.permission.RechargeVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @description 订单数据检验/处理类
 * @date 2020-11-04 15:23
 **/
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderHandlerBiz {

    /**
     * 限制上传文件大小，单位：M
     */
    private static final int FILE_SIZE = 5;
    private static final String UNIT = "M";
    private static final String SUFFIX = ".xlsx";

    /**
     * excel文件行头数
     */
    private static final int HEAD_ROW_NUMBER = 2;

    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private OrderFacade orderFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private AlipayFacade alipayFacade;
    @Reference
    private RechargeQueryFacade rechargeQueryFacade;
    @Reference
    private DataDictionaryFacade dictionaryFacade;
    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private RiskControlFacade riskControlFacade;

    private final RedisClient redisClient;
    private final OrderItemBiz orderItemBiz;
    private final AsyncBiz asyncBiz;
    private List<String> phoneMustMainstays;

    public Map<String,String> batchOrderUpload(OrderAcceptReqVo orderAcceptReqVo, EmployerOperatorVO operator, String employerNo) throws Exception {

        log.info("批次订单导入。mchNo:{}, operator:{}, orderReqVo:{}",employerNo,operator.getPhone(), orderAcceptReqVo);
        //校验商户状态
        checkMchInfo(employerNo,orderAcceptReqVo.getMainstayNo());

        if (!riskControlFacade.grantValidateExistRule(employerNo,orderAcceptReqVo.getMainstayNo())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应的规则，请联系平台管理员");
        }

        //检查代征关系
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo, orderAcceptReqVo.getMainstayNo());
        if (employerMainstayRelation == null || employerMainstayRelation.getStatus() == OpenOffEnum.OFF.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征关系未激活,请联系客服");
        }
        //校验产品开通

        //用工企业账号信息
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo, orderAcceptReqVo.getMainstayNo(), orderAcceptReqVo.getChannelType());
        if(employerAccountInfo == null || employerAccountInfo.getStatus().equals(OpenOffEnum.OFF.getValue())
                || StringUtils.isBlank(employerAccountInfo.getPayChannelNo())
                || StringUtils.isBlank(employerAccountInfo.getSubMerchantNo())){
            boolean checkChannel=employerAccountInfo.getChannelType()!= ChannelTypeEnum.WENXIN.getValue();
            if(checkChannel&&StringUtils.isBlank(employerAccountInfo.getParentMerchantNo())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道配置信息不对，请联系客服！");
            }
        }

        // 检查上传文件
        MultipartFile file = orderAcceptReqVo.getFile();
        if(!MultipartFileUtil.checkFileSize(file.getSize(), FILE_SIZE, UNIT)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件大小超过限制");
        }

        if(!file.getOriginalFilename().endsWith(SUFFIX)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("FileName:" + file.getOriginalFilename() +"文件格式有误，必须为xlsx格式");
        }
        File excelFile = MultipartFileUtil.transfer2File(file);

        List<BankGrantRow> list = new ArrayList<>();
        EasyExcel.read(excelFile, BankGrantRow.class, new TestGrantDataListener(HEAD_ROW_NUMBER, list)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        log.info("[{}]==>检查文件具体内容通过", employerNo);
        if (CollectionUtils.isEmpty(list) || emptyContent(list)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提交的发放名单不能为空");
        }

        //生成订单
        String batchId = sequenceFacade.nextRedisId("",SequenceBizKeyEnum.ORDER_SEQ.getKey(), SequenceBizKeyEnum.ORDER_SEQ.getWidth());
        Date batchTime = new Date();
        String batchNo = SequenceBizKeyEnum.ORDER_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
        log.info("[{}]==>生成订单批次号:{}", employerNo, batchNo);

        //初始化订单信息
        String logFlag = String.join("-",employerNo,batchNo);
        Order order = fillOrder(batchNo, orderAcceptReqVo,employerAccountInfo,batchTime);
        Long id = orderFacade.insert(order);
        order.setId(id);
        log.info("[{}]==>批次插入成功", logFlag);
        asyncBiz.doAsync(()->{
            doAsync(excelFile,order);
            return true;
        });
        //返回结果
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("platBatchNo", order.getPlatBatchNo());
        log.info("[{}]==>返回结果：{}", logFlag, JsonUtil.toString(paramMap));
        //存入订单批次
        Date expireTime = DateUtil.parseJodaDateTime(order.getCreateTime()).plusHours(7*24).toDate();
        redisClient.zadd(TradeConstant.ORDER_BATCH_ZSET_KEY,expireTime.getTime(),batchNo);
        return paramMap;
    }

    private boolean emptyContent(List<BankGrantRow> list) {
        for (BankGrantRow item : list) {
            if (StringUtils.isNotBlank(item.getReceiveIdCardNo())) {
                return false;
            }
        }
        return true;
    }

    public String recharge(RechargeVo rechargeVo,String employerNo,EmployerOperatorVO operatorVO) {
        checkMchInfo(employerNo,rechargeVo.getMainstayNo());

        EmployerAccountInfo employerAccountInfo = getEmployerAccountInfo(employerNo,rechargeVo.getMainstayNo(),rechargeVo.getChannelType());
        if (employerAccountInfo.getPayChannelNo() == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未正确配置通道编号");
        }
        if (StringUtils.equals(employerAccountInfo.getPayChannelNo(), ChannelNoEnum.ALIPAY.name())) {
            //0. 校验报备记录
            if (!checkReport(employerAccountInfo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道未正确报备");
            }
            //1. 创建充值记录
            RechargeRecord rechargeRecord = fillRechargeRecord(employerAccountInfo, rechargeVo);
            rechargeRecord.setCreateBy(operatorVO.getName());
            rechargeRecord.setRechargeType(RechargeTypeEnum.TRANSFER_RECHARGE.getCode());
            RechargeRecord rechargeRecordNew = orderFacade.addRechargeRecord(rechargeRecord);
            //2. 调用充值接口
            String recharge_url = alipayFacade.recharge(rechargeRecordNew.getPayeeAgreementNo(), rechargeRecordNew.getAccountBookId(), rechargeRecordNew.getPayeeIdentity(), rechargeRecordNew.getRechargeAmount().toPlainString(), rechargeRecordNew.getRechargeOrderId(),DateUtil.formatDateTime(rechargeRecord.getExpireTime()));

            return recharge_url;

        } else if (StringUtils.equals(employerAccountInfo.getPayChannelNo(), ChannelNoEnum.JOINPAY.name())) {
            //1. 创建充值记录
            RechargeRecord rechargeRecord = fillRechargeRecord(employerAccountInfo, rechargeVo);
            rechargeRecord.setCreateBy(operatorVO.getName());
            rechargeRecord.setRechargeType(RechargeTypeEnum.TRANSFER_RECHARGE.getCode());
            rechargeRecord.setRechargeStatus(RechargeStatusEnum.NEW.getCode().shortValue());
//            RechargeRecord rechargeRecordNew = orderFacade.addRechargeRecord(rechargeRecord);

            return "success";
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持该通道充值");
        }
    }

    private RechargeRecord fillRechargeRecord(EmployerAccountInfo employerAccountInfo,RechargeVo rechargeVo) {
        String rechargeOrderId = LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getKey(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getWidth());


        Date now = new Date();
        Date expireDateTime = DateUtil.addMinute(now, 60);

        RechargeRecord rechargeRecord = new RechargeRecord();
        rechargeRecord.setRechargeOrderId(rechargeOrderId);
        rechargeRecord.setChannelName(employerAccountInfo.getPayChannelName());
        rechargeRecord.setChannelType(employerAccountInfo.getChannelType().shortValue());
        rechargeRecord.setRechargeAmount(new BigDecimal(rechargeVo.getRechargeAmount()));
        rechargeRecord.setCreateTime(now);
        rechargeRecord.setChannelCode(employerAccountInfo.getPayChannelNo());
        rechargeRecord.setExpireTime(expireDateTime);
        rechargeRecord.setUpdateTime(now);
        rechargeRecord.setEmployerNo(employerAccountInfo.getEmployerNo());
        rechargeRecord.setEmployerName(employerAccountInfo.getEmployerName());
        rechargeRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
        rechargeRecord.setMainstayName(employerAccountInfo.getMainstayName());
        rechargeRecord.setAccountBookId(employerAccountInfo.getSubMerchantNo());
        rechargeRecord.setPayeeAgreementNo(employerAccountInfo.getSubAgreementNo());
        rechargeRecord.setPayeeIdentity(employerAccountInfo.getSubAlipayUserId());
        rechargeRecord.setPayeeIdentityType("ALIPAY_USER_ID");
        return rechargeRecord;
    }

    private RechargeRecord updateRechargeRecord(EmployerAccountInfo employerAccountInfo,RechargeVo rechargeVo,RechargeRecord latestRechargeRecord) {

        Date now = new Date();
        Date expireDateTime = DateUtil.addMinute(now, 60);

        RechargeRecord rechargeRecord = new RechargeRecord();
        rechargeRecord.setId(latestRechargeRecord.getId());
        rechargeRecord.setRechargeOrderId(latestRechargeRecord.getRechargeOrderId());
        rechargeRecord.setChannelName(employerAccountInfo.getPayChannelName());
        rechargeRecord.setChannelType(employerAccountInfo.getChannelType().shortValue());
        rechargeRecord.setRechargeAmount(new BigDecimal(rechargeVo.getRechargeAmount()));
        rechargeRecord.setChannelCode(employerAccountInfo.getPayChannelNo());
        rechargeRecord.setExpireTime(expireDateTime);
        rechargeRecord.setUpdateTime(now);
        rechargeRecord.setEmployerNo(employerAccountInfo.getEmployerNo());
        rechargeRecord.setEmployerName(employerAccountInfo.getEmployerName());
        rechargeRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
        rechargeRecord.setMainstayName(employerAccountInfo.getMainstayName());
        rechargeRecord.setAccountBookId(employerAccountInfo.getSubMerchantNo());
        rechargeRecord.setPayeeAgreementNo(employerAccountInfo.getSubAgreementNo());
        rechargeRecord.setPayeeIdentity(employerAccountInfo.getSubAlipayUserId());
        rechargeRecord.setPayeeIdentityType("ALIPAY_USER_ID");
        return rechargeRecord;
    }

    private boolean checkReport(EmployerAccountInfo accountInfo) {
        if (StringUtils.isBlank(accountInfo.getParentMerchantNo())
        ||StringUtils.isBlank(accountInfo.getSubMerchantNo())
        ||StringUtils.isBlank(accountInfo.getParentAgreementNo())
        ||StringUtils.isBlank(accountInfo.getSubAgreementNo())
        ||StringUtils.isBlank(accountInfo.getParentAlipayUserId())
        ||StringUtils.isBlank(accountInfo.getSubAlipayUserId()))
        {
            return false;
        }

        return true;
    }

    private void doAsync(File excelFile, Order order) {
        try{
            CountVo countVo = CountVo.builder()
                .requestCount(0)
                .failCount(0)
                .requestNetAmount(BigDecimal.ZERO)
                .failNetAmount(BigDecimal.ZERO).build();

            String phoneMustMainstayConfig = dictionaryFacade.getSystemConfig("PHONE_MUST_MAINSTAY");
            if (StringUtils.isNotBlank(phoneMustMainstayConfig)) {
                this.phoneMustMainstays = ListUtil.toList(phoneMustMainstayConfig.split(","));
            }

            //excel处理
            EasyExcel.read(excelFile, BankGrantRow.class, new GrantDataListener(orderItemBiz,sequenceFacade,notifyFacade,order,countVo,HEAD_ROW_NUMBER,this.phoneMustMainstays)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();

            //汇总信息
            order.setRequestCount(countVo.getRequestCount());
            order.setRequestNetAmount(countVo.getRequestNetAmount());
            order.setFailCount(countVo.getFailCount());
            order.setFailNetAmount(countVo.getFailNetAmount());
            //受理
            orderFacade.startAccept(order);
            log.info("[{}]==>批次受理初始调用结束", order.getPlatBatchNo());
        }catch (BizException e){
            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);
            order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
            order.setErrorDesc(e.getErrMsg());
            order.setErrorCode(String.valueOf(e.getSysErrorCode()));
            orderFacade.update(order);
            orderFacade.cancelOrderItem(order.getPlatBatchNo());
        }catch (MchOrderNoException e){
            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);

            String errorMsg = (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > 200) ? e.getMessage().substring(0, 199) : e.getMessage();
            order.setBatchStatus(OrderStatusEnum.IMPORT_FAIL.getValue());
            order.setErrorDesc(errorMsg);

            orderFacade.update(order);

            orderFacade.cancelOrderItem(order.getPlatBatchNo());
        }catch (Exception e){

            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);

            String errorMsg = (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > 200) ? e.getMessage().substring(0, 199) : e.getMessage();

            if (errorMsg != null && errorMsg.contains("MchOrderNoException")) {
                order.setBatchStatus(OrderStatusEnum.IMPORT_FAIL.getValue());
            }else {
                order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
            }
            order.setErrorDesc(errorMsg);

            orderFacade.update(order);
            orderFacade.cancelOrderItem(order.getPlatBatchNo());
        }finally {
            log.info("批次[{}] 文件使用完毕,进行删除删除,file:{}",order.getPlatBatchNo(),excelFile.getAbsolutePath());
            boolean isDel = FileUtils.deleteDir(excelFile);
            if(isDel){
                log.info("批次[{}] 文件删除成功",order.getPlatBatchNo());
            }else{
                log.info("批次[{}] 文件删除失败,需手动处理",order.getPlatBatchNo());
            }
        }
    }

    private Order fillOrder(String batchNo, OrderAcceptReqVo orderAcceptReqVo, EmployerAccountInfo employerAccountInfo, Date batchTime) {
        Order order = new Order();
        order.setCreateDate(batchTime);
        order.setCreateTime(batchTime);
        order.setUpdateTime(batchTime);
        order.setMchBatchNo(batchNo);
        order.setBatchName(String.join("-", DateUtil.formatDate(new Date()),employerAccountInfo.getEmployerName()));
        order.setPlatBatchNo(batchNo);
        order.setEmployerNo(employerAccountInfo.getEmployerNo());
        order.setEmployerName(employerAccountInfo.getEmployerName());
        order.setMainstayNo(orderAcceptReqVo.getMainstayNo());
        order.setMainstayName(orderAcceptReqVo.getMainstayName());
        order.setChannelType(orderAcceptReqVo.getChannelType());
        order.setPayChannelNo(employerAccountInfo.getPayChannelNo());
        order.setChannelName(employerAccountInfo.getPayChannelName());
        order.setBatchStatus(OrderStatusEnum.IMPORTING.getValue());
        order.setWorkCategoryCode(orderAcceptReqVo.getWorkCategoryCode());
        order.setWorkCategoryName(orderAcceptReqVo.getWorkCategoryName());
        order.setServiceDesc(orderAcceptReqVo.getServiceDesc());
        order.setLaunchWay(LaunchWayEnum.EMPLOYER.getValue());
        order.setProductNo(orderAcceptReqVo.getProductNo());
        order.setProductName(orderAcceptReqVo.getProductName());
        return order;
    }

    private void checkMchInfo(String employerNo,String mainstayNo) {
        log.info("校验商户状态信息,用工企业编号:{},代征主体编号:{}",employerNo,mainstayNo);
        //商户状态
        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("编号[" + employerNo + "] 商户账号不存在,请联系客服");
        }
        if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("您的商户状态未激活,请联系客服");
        }
        //代征主体
        Merchant mainstay = merchantQueryFacade.getByMchNo(mainstayNo);
        if (mainstay == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("编号[" + mainstayNo + "] 所选代征主体不存在,请联系客服");
        }
        if (!mainstay.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所选代征主体状态未激活,请联系客服");
        }
    }

    private EmployerAccountInfo getEmployerAccountInfo(String employerNo,String mainstayNo,Integer channelType) {
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo, mainstayNo, channelType);

        if (employerAccountInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户未开通该通道账户");
        }
        return employerAccountInfo;
    }
}
