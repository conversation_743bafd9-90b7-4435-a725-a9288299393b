package com.zhixianghui.web.employer.vo.order.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-12 19:35
 **/
@Data
public class OrderItemQueryVo {


    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;
    /**
     * 订单明细状态列表
     */
    private List<Integer> orderItemStatusList;

    /**
     * 订单明细状态
     */
    private Integer orderItemStatus;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 收款账户
     */
    private String receiveAccountNo;

    /**
     * 银行预留手机号
     */
    private String receivePhoneNo;

    /**
     * 创建起始时间
     */
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    private Date createEndDate;

    /**
     * 完成起始时间
     */
    private Date completeBeginDate;

    /**
     * 完成截止时间
     */
    private Date completeEndDate;

    private List<String> exportPlatTrxNos;

}
