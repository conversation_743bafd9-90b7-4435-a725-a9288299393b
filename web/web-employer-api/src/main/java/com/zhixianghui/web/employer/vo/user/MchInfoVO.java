package com.zhixianghui.web.employer.vo.user;

import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import lombok.Data;

/**
 * 商户信息vo
 *
 * <AUTHOR>
 */
@Data
public class MchInfoVO {

    /**
     * 商户编号
     */
    private String mchNo;

    /**
     * 商户名称
     */
    private String mchName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 商户状态 {@link MchStatusEnum#getValue()}
     */
    private Integer mchStatus;

    /**
     * 资质状态 {@link AuthStatusEnum#getValue()}
     */
    private Integer authStatus;

    private String shortName;
}
