package com.zhixianghui.web.employer.biz.order.excel;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @description 银行卡发放实体行
 * @date 2020-11-04 10:48
 **/
@Data
public class BankGrantRow {

    /**
     * 持卡人姓名
     */
    @NotEmpty
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 收款账户
     */
    private String receiveAccountNo;

    /**
     * 银行预留手机号
     */
    private String receivePhoneNo;

    /**
     * 请求金额，商户实际传入的金额
     */
    private String requestAmountStr;

    /**
     * 备注
     */
    private String desc;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    private String memo;
    private String appId;
}
