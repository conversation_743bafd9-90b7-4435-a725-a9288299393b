package com.zhixianghui.web.employer.biz.order;

import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.web.employer.biz.order.offline.CKHOfflineOrderHandlerBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName OrderHandlerFactory
 * @Description TODO
 * @Date 2022/6/28 9:58
 */
@Component
public class OrderHandlerFactory {

    @Autowired
    private ZXHOrderHandlerBiz zxhOrderHandlerBiz;

    @Autowired
    private CKHOrderHandlerBiz ckhOrderHandlerBiz;

    @Autowired
    private CEPOrderHandlerBiz cepOrderHandlerBiz;

    @Autowired
    private CKHOfflineOrderHandlerBiz ckhOfflineOrderHandlerBiz;

    public AbstractOrderHandler getHandler(String productNo){
        ProductNoEnum productNoEnum = ProductNoEnum.getEnum(productNo);
        switch (productNoEnum){
            case ZXH:
                return zxhOrderHandlerBiz;
            case CKH:
                return ckhOrderHandlerBiz;
            case CEP:
                return cepOrderHandlerBiz;
            default:
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("产品不存在");
        }
    }

    public AbstractOrderHandler getCkhOfflineOrderHandlerBiz() {
        return ckhOfflineOrderHandlerBiz;
    }
}
