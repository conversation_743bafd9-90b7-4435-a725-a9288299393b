package com.zhixianghui.web.employer.controller.report;

import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.report.ReportEntity;
import com.zhixianghui.facade.common.service.ReportFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.vo.report.ReportVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ReportController {

    @Reference
    private ReportFacade reportFacade;

    @PostMapping("report/report")
    public RestResult<String> report(@Validated @RequestBody ReportVo reportVo, @CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo) {
        // 1. 参数校验
        if (reportVo == null){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("参数不能为空");
        }
        if (!StringUtils.equals(reportVo.getPayChannelNo(), ChannelNoEnum.ALIPAY.name())) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("暂不支持该通道报备");
        }

        ReportEntity reportEntity = new ReportEntity();
        BeanUtils.copyProperties(reportVo,reportEntity);
        reportEntity.setReporter(vo.getOperatorName());
        reportEntity.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        return RestResult.success(reportFacade.reportMerchantDiy(reportEntity));
    }

}
