package com.zhixianghui.web.employer.biz.order.offline;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.OfflineOrderFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.web.employer.utils.BeanToMapUtil;
import com.zhixianghui.web.employer.vo.PageVo;
import com.zhixianghui.web.employer.vo.order.req.OrderQueryVo;
import com.zhixianghui.web.employer.vo.order.res.OrderResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderWithCountResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OfflineOrderBiz {

    @Reference
    private OfflineOrderFacade orderFacade;
    @Reference
    private OfflineOrderItemFacade orderItemFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    public void confirmBatchOrder(String platBatchNo, String employerNo) {
        // 判断订单状态
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
        paramMap.put("platBatchNo",platBatchNo);
        OfflineOrder order = orderFacade.getOne(paramMap);
        checkOrderStatus(order);
        //校验商户信息状态
        checkMchInfo(order.getEmployerNo(),order.getMainstayNo());

        orderFacade.startGrant(platBatchNo);
        log.info("[employerNo:{}  platBatchNo:{}]确认发放请求已异步发出",employerNo,platBatchNo);
    }

    private void checkOrderStatus(OfflineOrder order) {
        if (order == null) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单不存在");
        }
        if(Objects.equals(order.getBatchStatus(), OrderStatusEnum.IMPORTING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该批次未受理完成，请稍后再进行提交");
        }
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.PENDING_GRANT.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次状态已经变更，不能取消发放");
        }
    }

    private void checkMchInfo(String employerNo,String mainstayNo) {
        log.info("校验商户状态信息,用工企业编号:{},代征主体编号:{}",employerNo,mainstayNo);
        //商户状态
        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("编号[" + employerNo + "] 商户账号不存在,请联系客服");
        }
        if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("您的商户状态未激活,请联系客服");
        }
        //代征主体
        Merchant mainstay = merchantQueryFacade.getByMchNo(mainstayNo);
        if (mainstay == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("编号[" + mainstayNo + "] 所选代征主体不存在,请联系客服");
        }
        if (!mainstay.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所选代征主体状态未激活,请联系客服");
        }
    }

    public void initAuthInfo(String employerNo,String platBatchNo){
        orderItemFacade.authInit(platBatchNo);
    }

    public PageResult<List<OrderResVo>> listOrderPage(OrderQueryVo orderQueryVo, PageVo pageVo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        Page<OfflineOrder> pageResult = orderFacade.pageOrder(new Page<>(pageParam.getPageCurrent(),pageParam.getPageSize()),paramMap);
        List<OrderResVo> list = pageResult.getRecords().stream().map(
                order->{
                    OrderResVo orderResVo = new OrderResVo();
                    BeanUtils.copyProperties(order,orderResVo);
                    return orderResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list,Long.valueOf(pageResult.getCurrent()).intValue(),Long.valueOf(pageResult.getSize()).intValue(),pageResult.getTotal());
    }

    public OrderWithCountResVo getOrderByBatchNo(String platBatchNo, String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        paramMap.put("employerNo",employerNo);
        OfflineOrder order = orderFacade.getOne(paramMap);
        OrderWithCountResVo orderWithCountResVo = new OrderWithCountResVo();
        BeanUtils.copyProperties(order,orderWithCountResVo);
        if (order.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
            orderWithCountResVo.setRequestNetAmount(order.getRequestNetAmount());
        }else if (order.getProductNo().equals(ProductNoEnum.CKH.getValue()) || order.getProductNo().equals(ProductNoEnum.JKH.getValue())){
            orderWithCountResVo.setRequestNetAmount(order.getRequestTaskAmount());
        }
        return orderWithCountResVo;
    }

    public void cancelBatchOrder(String platBatchNo, String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
        paramMap.put("platBatchNo",platBatchNo);
        OfflineOrder order = orderFacade.getOne(paramMap);
        if (order == null) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单不存在");
        }
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.PENDING_GRANT.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次状态已经变更，不能取消发放");
        }
        orderFacade.cancelBatchOrder(order);
    }

    public void delete(OrderDeleteDTO orderDeleteDTO){
        orderFacade.delete(orderDeleteDTO);
    }
}
