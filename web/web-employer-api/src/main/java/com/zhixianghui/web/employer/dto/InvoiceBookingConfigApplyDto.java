package com.zhixianghui.web.employer.dto;

import com.zhixianghui.facade.common.enums.InvoiceBookPeriodEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
    * 预约开票配置表
    */
@Data
public class InvoiceBookingConfigApplyDto implements Serializable {
    private static final long serialVersionUID = 8626105021816854294L;

    private Long id;

    /**
     * 代征主体编号
     */
    @NotBlank(message = "代征主体编号不能为空")
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @NotBlank(message = "代征主体名称不能为空")
    private String mainstayName;

    /**
     * @see InvoiceBookPeriodEnum
     * 开票周期
     */
    private Integer applyPeriod = InvoiceBookPeriodEnum.PER_MONTH.getValue();

    /**
     * 固定申请日期
     */
    @NotNull(message = "固定申请日期")
    private Integer applyDate;

    /**
     * @see com.zhixianghui.common.statics.enums.invoice.ApplyTypeEnum
     * 开票模式
     */
    @NotNull(message = "开票模式")
    private Integer applyType;

//    /**
//     * 开票类目
//     */
//    @NotBlank(message = "开票类目不能为空")
//    private String invoiceCategoryCode;
//
//    /**
//     * 发票类目名称
//     */
//    @NotBlank(message = "发票类目名称不能为空")
//    private String invoiceCategoryName;

    private Integer invoiceType;

}
