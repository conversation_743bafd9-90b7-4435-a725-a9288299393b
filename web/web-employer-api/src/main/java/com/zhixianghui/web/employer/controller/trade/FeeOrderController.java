package com.zhixianghui.web.employer.controller.trade;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.dto.FeeOrderBatchVo;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.enums.FeeOrderStatus;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.facade.trade.vo.FeeOrderVo;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月12日 10:41:00
 */
@RestController
@RequestMapping("/feeOrder")
@Slf4j
public class FeeOrderController {

    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;

    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    @PostMapping("/listPage")
    public RestResult listPage(@RequestBody FeeOrderVo feeOrderVo, @RequestBody PageParam pageParam, @CurrentMchNo String mchNo) {
        feeOrderVo.setEmployerNo(mchNo);
        return RestResult.success(feeOrderBatchFacade.listPage(feeOrderVo, pageParam));
    }

    @PostMapping("/getStatistics")
    public RestResult getStatistics(@RequestBody FeeOrderVo feeOrderVo, @CurrentMchNo String mchNo) {
        feeOrderVo.setEmployerNo(mchNo);
        return RestResult.success(feeOrderBatchFacade.getStatistics(feeOrderVo));
    }

    @GetMapping("/selectOrderItem/{feeBatchNo}")
    public RestResult selectOrderItem(@PathVariable("feeBatchNo") String feeBatchNo) {
        return RestResult.success(feeOrderBatchFacade.selectOrderItem(feeBatchNo));
    }

    @GetMapping("/getByItemNo/{feeItemNo}")
    public RestResult getByItemNo(@PathVariable("feeItemNo") String feeItemNo) {
        return RestResult.success(feeOrderBatchFacade.getByItemNo(feeItemNo));
    }

    @PostMapping("/payFee/{feeItemNo}")
    public RestResult payFee(@PathVariable("feeItemNo") String feeItemNo) {
        return RestResult.success(feeOrderBatchFacade.payFee(feeItemNo));
    }

    @GetMapping("/getOffLineItem/{feeBatchNo}")
    public RestResult getOffLineItem(@PathVariable("feeBatchNo") String feeBatchNo) {
        return RestResult.success(feeOrderBatchFacade.getOffLineItem(feeBatchNo));
    }

    @PostMapping("/complete")
    public RestResult complete(@RequestBody FeeOrderBatchVo feeOrderBatchVo) {
        feeOrderBatchFacade.complete(feeOrderBatchVo);
        return RestResult.success("提交成功");
    }


    @PostMapping("/export/{feeBatchNo}")
    public RestResult<String> export(@PathVariable("feeBatchNo") String feeBatchNo, @CurrentStaffVo EmployerStaffVO vo) {
        FeeOrderBatch feeOrderBatch = feeOrderBatchFacade.getByBatchNo(feeBatchNo);
        AssertUtil.notNull(feeOrderBatch,"找不到对应账单");

        // 生成文件编号
        String fileNo = exportRecordFacade.genFileNo();

        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setMchNo(vo.getMchNo());
        record.setOperatorLoginName(vo.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.FEE_ORDER_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.FEE_ORDER_EXPORT.getValue());
        record.setParamJson(JSONObject.toJSONString(feeOrderBatch));
        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.FEE_ORDER_EXPORT.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

}