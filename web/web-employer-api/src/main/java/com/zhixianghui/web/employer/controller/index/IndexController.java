package com.zhixianghui.web.employer.controller.index;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.biz.index.IndexDataBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName IndexController
 * @Description TODO
 * @Date 2022/4/29 10:15
 */
@RestController
@RequestMapping("index")
public class IndexController {

    @Autowired
    private IndexDataBiz indexDataBiz;

    @GetMapping("monthData")
    @Permission("index")
    public RestResult monthData(@CurrentMchNo String mchNo){
        return RestResult.success(indexDataBiz.getMonthData(mchNo));
    }

    @GetMapping("taskData")
    @Permission("index")
    public RestResult taskData(@CurrentStaffVo EmployerStaffVO employerStaffVO){
        return RestResult.success(indexDataBiz.getTaskData(employerStaffVO));
    }

    @GetMapping("notifyList")
    @Permission("index")
    public RestResult notifyList(@CurrentMchNo String mchNo){
        return RestResult.success(indexDataBiz.getNotifyList(mchNo));
    }

    @GetMapping("dynamicList")
    @Permission("index")
    public RestResult dynamicList(@CurrentMchNo String mchNo){
        return RestResult.success(indexDataBiz.dynamicList(mchNo));
    }



}
