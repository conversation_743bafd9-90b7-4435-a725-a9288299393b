package com.zhixianghui.web.employer.controller.trade;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.TimeRangeUtil;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.service.RechargeQueryFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentOperatorVO;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.biz.order.RechargeExportBiz;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("recharge")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class RechargeQueryController {

    @Reference
    private RechargeQueryFacade rechargeQueryFacade;
    final private RechargeExportBiz rechargeExportBiz;

    @PostMapping("listRecharge")
    @Permission("recharge:list:view")
    public RestResult<PageResult<List<RechargeRecord>>> listPage(@RequestBody Map<String, Object> paramMap, @RequestBody PageParam pageParam, @CurrentMchNo String employerNo) {
        paramMap.put("employerNo", employerNo);

        String createBeginDate = (String) paramMap.get("createBeginDate");
        String createEndDate = (String) paramMap.get("createEndDate");

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {

            if (StringUtils.isBlank(((String) paramMap.get("rechargeOrderId")))) {
                paramMap.put("createBeginDate",TimeRangeUtil.latestThreeMonthStartTimeStr());
                paramMap.put("createEndDate",TimeRangeUtil.endTimeStr());
            }
        }

        return RestResult.success(rechargeQueryFacade.list(paramMap, pageParam));
    }

    @PostMapping("getByRechargeId")
    @Permission("recharge:list:view")
    public RestResult<RechargeRecord> getByRechargeId(String rechargeId) {
        return RestResult.success(rechargeQueryFacade.getByRechargeId(rechargeId));
    }

    @PostMapping("getById")
    @Permission("recharge:list:view")
    public RestResult<RechargeRecord> getById(String id) {
        return RestResult.success(rechargeQueryFacade.getByRechargeId(id));
    }

    @PostMapping("exportRechargeRecord")
    @Permission("recharge:list:view")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "充值记录-导出")
    public RestResult<String> exportRechargeRecord(@RequestBody Map<String, Object> paramMap, @CurrentMchNo String employerNo,@CurrentOperatorVO EmployerOperatorVO operatorVO) {
        buildExportParam(paramMap,employerNo,operatorVO);
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        rechargeExportBiz.exportRechargeRecord(paramMap,operatorVO.getPhone(),employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private void buildExportParam(Map<String,Object> paramMap,String employerNo,EmployerOperatorVO operatorVO) {
        String startTime = (String) paramMap.get("createBeginDate");
        String endTime = (String) paramMap.get("createEndDate");
        if (StringUtils.isAnyBlank(startTime, endTime)) {
            if (StringUtils.isBlank(((String) paramMap.get("rechargeOrderId")))) {
                paramMap.put("createBeginDate",TimeRangeUtil.latestThreeMonthStartTimeStr());
                paramMap.put("createEndDate",TimeRangeUtil.endTimeStr());
            }
        }else {
            validDate(LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        log.info("employerNo:{},operatorVO:{}",employerNo, JSON.toJSONString(operatorVO));

        paramMap.put("employerNo", employerNo);
    }

    @PostMapping("exportRechargeRecordZip")
    @Permission("recharge:list:view")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "充值记录-导出回单")
    public RestResult<String> exportRechargeRecordZip(@RequestBody Map<String, Object> paramMap, @CurrentMchNo String employerNo,@CurrentOperatorVO EmployerOperatorVO operatorVO) {
        buildExportParam(paramMap, employerNo, operatorVO);
        paramMap.put("exportFileType",FileTypeEnum.ZIP.getValue());
        rechargeExportBiz.exportRechargeRecordZip(paramMap,operatorVO.getPhone(),employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }


    private void validDate(LocalDateTime createBeginDate, LocalDateTime createEndDate) {
        if(createBeginDate.isAfter(createEndDate)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
        }
    }
}
