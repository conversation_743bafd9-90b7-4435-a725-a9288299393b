package com.zhixianghui.web.employer.vo.order.res;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhixianghui.facade.trade.entity.BasePrivateItem;
import lombok.Data;

import java.util.Date;

@Data
public class OrderItemFailResVo {

    private Long id;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 错误行
     */
    private Integer line;

    /**
     * 错误详情
     */
    private String errorDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Short version;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 收款账户(卡号/支付宝账号)
     */
    private String receiveAccountNo;

    /**
     * 银行预留手机号
     */
    private String receivePhoneNo;

}