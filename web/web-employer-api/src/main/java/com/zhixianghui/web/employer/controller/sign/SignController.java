package com.zhixianghui.web.employer.controller.sign;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.IdCardTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.common.util.utils.TimeRangeUtil;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.biz.sign.SignBiz;
import com.zhixianghui.web.employer.vo.PageVo;
import com.zhixianghui.web.employer.vo.sign.AddSignImagesVo;
import com.zhixianghui.web.employer.vo.sign.SignRecordQueryVo;
import com.zhixianghui.web.employer.vo.sign.SignRecordResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 电子签约
 * @date 2021/1/18 15:34
 **/
@RestController
@RequestMapping("sign")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class SignController {

    private final SignBiz signBiz;

    @Permission("sign:signRecord:view")
    @PostMapping("listPage")
    public RestResult<PageResult<List<SignRecordResVo>>> listPage(@RequestBody SignRecordQueryVo queryVo, @RequestBody PageVo pageVo,@CurrentMchNo String employerNo) {
        Date createBeginDate = queryVo.getCreateBeginDate();
        Date createEndDate = queryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            queryVo.setCreateBeginDate(DateUtil.parseDateTime("2020-10-01 00:00:00").toJdkDate());
            queryVo.setCreateEndDate(TimeRangeUtil.endTime());
        }

        PageResult<List<SignRecordResVo>> pageResult = signBiz.listPage(queryVo,pageVo.toPageParam(),employerNo);
        return RestResult.success(pageResult);
    }

    /**
     * 导出电子签约
     */
    @Permission("sign:signRecord:export")
    @PostMapping("exportSignRecord")
    @Logger(type = OperateLogTypeEnum.QUERY,action = "导出电子签约")
    public RestResult<String> exportSignRecord(@RequestBody SignRecordQueryVo queryVo, @CurrentStaffVo EmployerStaffVO staffVO) {

        Date createBeginDate = queryVo.getCreateBeginDate();
        Date createEndDate = queryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            queryVo.setCreateBeginDate(DateUtil.parseDateTime("2020-10-01 00:00:00").toJdkDate());
            queryVo.setCreateEndDate(TimeRangeUtil.endTime());
        }

        signBiz.exportSignRecord(queryVo, staffVO);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 电子签约上传
     */
    @Permission("sign:signRecord:import")
    @PostMapping("importSignRecord")
    @Logger(type = OperateLogTypeEnum.CREATE,action = "电子签约上传")
    public RestResult<String> importSignRecord(
            @RequestParam("file") MultipartFile multiPartfile,
            @RequestParam("mchNo") String mainstayNo,
            @RequestParam("sms") Integer sms,
            @CurrentStaffVo EmployerStaffVO staffVO) {
        if (multiPartfile == null) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("上传的文件不能为空");
        }
        if (signBiz.importSignRecord(multiPartfile, mainstayNo, staffVO, sms)) {
            return RestResult.success("上传预签约文件成功, 请留意短信通知");
        }
        return RestResult.success("导入失败, 请稍后尝试");
    }

    /**
     * 短信重发
     */
    @Permission("sign:signRecord:resend")
    @PostMapping("resend")
    @Logger(type = OperateLogTypeEnum.MODIFY,action = "短信重发")
    public RestResult<String> resend(@RequestParam("id") Long signId, @CurrentStaffVo EmployerStaffVO staffVO) {
        if (signBiz.resend(signId, staffVO)) {
            return RestResult.success("补发成功, 请留意短信通知");
        }
        return RestResult.success("补发失败");
    }

    /**
     * 三要素修改
     */
    @Permission("sign:signRecord:modify")
    @PostMapping("modify")
    @Logger(type = OperateLogTypeEnum.MODIFY,action = "三要素修改")
    public RestResult<String> modify(@RequestBody SignRecordQueryVo queryVo, @CurrentStaffVo EmployerStaffVO staffVO) {
        if (StringUtils.isBlank(queryVo.getReceivePhoneNo())) {
            return RestResult.error("手机号码不能为空");
        }
        if (StringUtils.isBlank(queryVo.getReceiveIdCardNo())) {
            return RestResult.error("身份证号不能为空");
        }
        if (StringUtils.isBlank(queryVo.getReceiveName())) {
            return RestResult.error("姓名不能为空");
        }
        if (signBiz.exist(queryVo, staffVO)) {
            return RestResult.success("签约记录已经存在");
        }

        if (signBiz.modify(queryVo)) {
            return RestResult.success("预签约成功, 请留意短信通知");
        }
        return RestResult.success("预签约失败, 请稍后重试");
    }

    @Permission("sign:signRecord:modify")
    @PostMapping("addSignImages")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "签约添加图片")
    public RestResult<String> addSignImages(@RequestBody AddSignImagesVo addSignImagesVo) {
        if (addSignImagesVo.getIdCardType().intValue() == IdCardTypeEnum.ORIGINAL.getCode().intValue()) {
            LimitUtil.notEmpty(addSignImagesVo.getIdCardBackUrl(), "身份证背面地址不能为空");
            LimitUtil.notEmpty(addSignImagesVo.getIdCardFrontUrl(), "身份证正面地址不能为空");
        }else {
            LimitUtil.notEmpty(addSignImagesVo.getIdCardCopyFileUrl(), "身份证背面地址不能为空");
        }
        try {
            signBiz.addSignImages(addSignImagesVo);
            return RestResult.success("导入成功");
        } catch (Exception e) {
            log.error("导入失败",e);
            return RestResult.success(StrUtil.format("导入失败:{}",e.getMessage()));
        }
    }
}
