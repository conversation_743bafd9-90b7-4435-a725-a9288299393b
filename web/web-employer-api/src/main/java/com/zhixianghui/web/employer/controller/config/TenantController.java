package com.zhixianghui.web.employer.controller.config;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.service.TenantManageFacade;
import com.zhixianghui.web.employer.vo.config.TenantUriRequestVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName TenantController
 * @Description TODO
 * @Date 2023/3/31 11:32
 */
@RestController
@RequestMapping("/tenant")
public class TenantController {

    @Reference
    private TenantManageFacade tenantManageFacade;

    @PostMapping("getTenant")
    public RestResult getTenant(@Validated @RequestBody TenantUriRequestVo reqVo){
        if (StringUtils.isBlank(reqVo.getUrl())){
            return RestResult.success(null);
        }

        Map<String,Object> map = tenantManageFacade.getTenant(reqVo.getUrl());
        return RestResult.success(map);
    }
}
