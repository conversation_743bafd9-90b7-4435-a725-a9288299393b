package com.zhixianghui.web.employer.vo.permission;

import com.zhixianghui.common.util.validator.Email;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

@Data
public class ChangeEmailVO {

    /**
     * 新常用邮箱
     */
    @Email
    @Length(max = 50, message = "邮箱过长，不能超过50个字符")
    private String newEmail;

    /**
     * 邮箱验证码
     */
    @NotEmpty(message = "邮箱验证码不能为空")
    private String verifyCode;

    /**
     * 支付密码
     */
    @NotEmpty(message = "支付密码不能为空")
    private String tradePwd;
}
