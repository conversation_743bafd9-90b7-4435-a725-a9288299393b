package com.zhixianghui.web.employer.biz.order.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.trade.vo.SignRecordVo;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2021/4/25 11:08
 */
@Slf4j
public class CheckSignRecordListener extends AnalysisEventListener<SignRecordVo> {

    private final int headRowNumber;

    public CheckSignRecordListener(int headRowNumber) {
        this.headRowNumber = headRowNumber;
    }

    @Override
    public void invoke(SignRecordVo data, AnalysisContext context) {
        Integer index = context.readRowHolder().getRowIndex();
        log.info("第 {} 行通过", index);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        Integer rowNum = context.readRowHolder().getRowIndex();
        if(rowNum < headRowNumber ){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件内容格式有误，请检查文件!");
        }
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        Integer rowNum = context.readRowHolder().getRowIndex();
        // 读3行没问题就返回
        int checkNum = 3;
        if (rowNum > checkNum + headRowNumber ){
            doAfterAllAnalysed(context);
            return false;
        }
        return super.hasNext(context);
    }
}
