package com.zhixianghui.web.employer.vo.order.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-12 19:59
 **/
@Data
public class OrderResVo {

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 平台批次号
     */
    private String platBatchNo;

    /**
     * 批次名
     */
    private String batchName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 已受理(总)订单金额
     */
    private BigDecimal acceptedOrderAmount;

    /**
     * 通道类型(发放方式) {@link com.zhixianghui.common.statics.enums.report.ChannelTypeEnum}
     */
    private Integer channelType;

    /**
     * 批次状态 {@link com.zhixianghui.facade.trade.enums.OrderStatusEnum}
     */
    private Integer batchStatus;

    /**
     * 通道编号
     */
    private String payChannelNo;


    private Integer isDelete;

    /**
     * 自由职业者服务编号
     */
    private String workCategoryCode;

    /**
     * 自由职业者服务名称
     */
    private String workCategoryName;
}
