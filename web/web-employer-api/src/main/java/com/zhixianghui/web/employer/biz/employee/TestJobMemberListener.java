package com.zhixianghui.web.employer.biz.employee;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.facade.employee.dto.JobMemberExcelDto;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
@Slf4j
public class TestJobMemberListener extends AnalysisEventListener<JobMemberExcelDto> {

    private final int headRowNum;
    private List<JobMemberExcelDto> list;

    public TestJobMemberListener(int headRowNum, List<JobMemberExcelDto> list) {
        this.headRowNum = headRowNum;
        this.list = list;
    }

    @Override
    public void invoke(JobMemberExcelDto data, AnalysisContext context) {
        if (data == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("上传的数据为空");
        }
        //nothing 正常就可以
        Integer rowNum = context.readRowHolder().getRowIndex();
        log.info("测试第{}行通过:{}",rowNum, JSONObject.toJSON(data));
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (context == null || context.readRowHolder() == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件内容格式有误，请检查文件!");
        }
        Integer rowNum = context.readRowHolder().getRowIndex();
        if(rowNum < headRowNum ){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件内容格式有误，请检查文件!");
        }
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        Integer rowNum = context.readRowHolder().getRowIndex();
        // 读3行没问题就返回
        int checkNum = 3;
        if (rowNum > checkNum + headRowNum ){
            doAfterAllAnalysed(context);
            return false;
        }
        if (rowNum >= headRowNum) {
            JobMemberExcelDto data = (JobMemberExcelDto) context.readRowHolder().getCurrentRowAnalysisResult();
            list.add(data);
        }
        return super.hasNext(context);
    }
}
