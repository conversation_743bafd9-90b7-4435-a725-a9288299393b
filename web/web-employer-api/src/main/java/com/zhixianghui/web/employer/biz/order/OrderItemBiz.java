package com.zhixianghui.web.employer.biz.order;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.enums.export.FileTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.riskcontrol.entity.RiskcontrolOrderItem;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.OrderItemFail;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.ReceiptOrderEnum;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.vo.AuthInfoVo;
import com.zhixianghui.web.employer.utils.BeanToMapUtil;
import com.zhixianghui.web.employer.vo.PageVo;
import com.zhixianghui.web.employer.vo.order.req.HangOrderItemQueryVo;
import com.zhixianghui.web.employer.vo.order.req.OrderItemFailQueryVo;
import com.zhixianghui.web.employer.vo.order.req.OrderItemQueryVo;
import com.zhixianghui.web.employer.vo.order.res.HangOrderItemResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderItemFailResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderItemResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 订单明细
 * @date 2020-11-04 15:13
 **/
@Service
@Slf4j
public class OrderItemBiz {

    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private RiskControlFacade riskControlFacade;

    public void batchInsert(List<OrderItem> list) {
        orderItemFacade.batchInsert(list);
    }

    public PageResult<List<OrderItemResVo>> listOrderItemPage(OrderItemQueryVo orderItemQueryVo, PageVo pageVo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<OrderItem>> pageResult = orderItemFacade.listPage(paramMap,pageParam);
        List<OrderItemResVo> orderItemResVoList = pageResult.getData().stream().map(
                orderItem ->{
                    OrderItemResVo orderItemResVo = new OrderItemResVo();
                    BeanUtils.copyProperties(orderItem,orderItemResVo);
                    orderItemResVo.setReceiveName(orderItem.getReceiveNameDesensitize());
                    orderItemResVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDesensitize());
                    orderItemResVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDesensitize());
                    orderItemResVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDesensitize());
                    orderItemResVo.setChannelNo(orderItem.getPayChannelNo());
                    return orderItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public PageResult<List<OrderItemFailResVo>> getFailItemByBatchVo(OrderItemFailQueryVo queryVo, PageVo pageVo) {
        PageResult<List<OrderItemFail>> pageResult = orderItemFacade.pageByPlatBatchNo(queryVo.getPlatBatchNo(), PageParam.newInstance(pageVo.getPageCurrent(), pageVo.getPageSize()));
        List<OrderItemFailResVo> orderItemResVoList = pageResult.getData().stream().map(
                orderItem ->{
                    OrderItemFailResVo orderItemResVo = new OrderItemFailResVo();
                    BeanUtils.copyProperties(orderItem,orderItemResVo);
                    orderItemResVo.setReceiveName(orderItem.getReceiveNameDesensitize());
                    orderItemResVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDesensitize());
                    orderItemResVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDesensitize());
                    orderItemResVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDesensitize());
                    return orderItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public PageResult<List<OrderItemResVo>> listOrderItemByBatchNoPage(String platBatchNo, PageVo pageVo, String employerNo) {
        return listOrderItemByBatchNoAndStatus(platBatchNo,null,pageVo,employerNo);
    }

    public PageResult<List<HangOrderItemResVo>> listHangOrderItemPage(HangOrderItemQueryVo hangOrderItemQueryVo, PageVo pageVo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(hangOrderItemQueryVo);
        paramMap.put("employerNo",employerNo);
        PageResult<List<RiskcontrolOrderItem>> pageResult = riskControlFacade.listPagePendingOrder(paramMap,pageVo.toPageParam("ID DESC"));
        List<HangOrderItemResVo> hangOrderItemResVoList = pageResult.getData().stream().map(
                orderItem ->{
                    HangOrderItemResVo hangOrderItemResVo = new HangOrderItemResVo();
                    BeanUtils.copyProperties(orderItem,hangOrderItemResVo);
                    hangOrderItemResVo.setReceiveName(orderItem.getReceiveNameDesensitize());
                    hangOrderItemResVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDesensitize());
                    hangOrderItemResVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDesensitize());
                    hangOrderItemResVo.setErrorDesc(orderItem.getFailedReason());
                    return hangOrderItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(hangOrderItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public PageResult<List<OrderItemResVo>> listOrderItemByBatchNoAndStatus(String platBatchNo, Integer orderItemStatus, PageVo pageVo, String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        if(orderItemStatus != null){
            paramMap.put("orderItemStatus",orderItemStatus);
        }
        PageResult<List<OrderItem>> pageResult = orderItemFacade.listPage(paramMap,pageVo.toPageParam("ID DESC"));
        List<OrderItemResVo> orderItemResVoList = pageResult.getData().stream().map(
                orderItem ->{
                    OrderItemResVo orderItemResVo = new OrderItemResVo();
                    BeanUtils.copyProperties(orderItem,orderItemResVo);
                    orderItemResVo.setReceiveName(orderItem.getReceiveNameDesensitize());
                    orderItemResVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDesensitize());
                    orderItemResVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDesensitize());
                    orderItemResVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDesensitize());
                    AuthInfoVo authInfo = orderItemFacade.getAuthInfo(orderItem);
                    orderItemResVo.setAuthStatus(authInfo.getAuth());
                    orderItemResVo.setSignStatus(authInfo.getSign());
                    return orderItemResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(orderItemResVoList,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public void exportOrderItem(OrderItemQueryVo orderItemQueryVo, EmployerStaffVO vo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(employerNo);
        record.setFileNo(fileNo);
        record.setOperatorLoginName(vo.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.TRADE_ORDER_ITEM_MER.getFileName());
        record.setReportType(ReportTypeEnum.TRADE_ORDER_ITEM_MER.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.TRADE_ORDER_ITEM_MER.getDataName());
        if(dataDictionary == null){
            log.error("商户后台订单明细导出字典未配置");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        setField(record, dataDictionary);
        exportRecordFacade.insert(record);
    }

    public void exportOrderItemFail(OrderItemFailQueryVo orderItemFailQueryVo, EmployerStaffVO vo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemFailQueryVo);
        paramMap.put("employerNo",employerNo);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(employerNo);
        record.setFileNo(fileNo);
        record.setOperatorLoginName(vo.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.ORDER_ITEM_FAIL.getFileName());
        record.setReportType(ReportTypeEnum.ORDER_ITEM_FAIL.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.ORDER_ITEM_FAIL.getDataName());
        if(dataDictionary == null){
            log.error("商户后台订单明细导出字典未配置");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        setField(record, dataDictionary);
        exportRecordFacade.insert(record);
    }

    public void exportHangOrderItem(HangOrderItemQueryVo hangOrderItemQueryVo, EmployerStaffVO staffVo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(hangOrderItemQueryVo);
        paramMap.put("employerNo",employerNo);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(employerNo);
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVo.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());

        record.setFileName(ReportTypeEnum.TRADE_HANG_ORDER_MER.getFileName());
        record.setReportType(ReportTypeEnum.TRADE_HANG_ORDER_MER.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.TRADE_HANG_ORDER_MER.getDataName());
        if(dataDictionary == null){
            log.error("商户后台挂单订单明细导出字典未配置");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        setField(record, dataDictionary);
        exportRecordFacade.insert(record);
    }

    private void setField(ExportRecord record, DataDictionary dataDictionary) {
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
    }

    public Long countOrderItem(OrderItemQueryVo orderItemQueryVo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        return orderItemFacade.countOrderItem(paramMap);
    }

    public OrderItemSumBo sumOrderItem(OrderItemQueryVo orderItemQueryVo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        return orderItemFacade.sumOrderItem(paramMap);
    }

    public Long countItemByMchNo(String employerNo, String mchOrderNo) {
        return orderItemFacade.countOrderItemByMchOrderNo(employerNo, mchOrderNo);
    }

    public void saveFailItem(OrderItemFail orderItemFail) {
        orderItemFacade.save(orderItemFail);
    }

    public List<OrderItemFail> getItemByPlatBatchNo(String platBatch) {
        return orderItemFacade.getByPlatBatchNo(platBatch);
    }

    public void exportReceipt(Integer type, OrderItemQueryVo orderItemQueryVo, EmployerStaffVO vo, String employerNo) {
        ReportTypeEnum reportTypeEnum = ReceiptOrderEnum.getEnum(type).getReportTypeEnum();
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderItemQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        paramMap.put("exportFileType", FileTypeEnum.EXECL.getValue());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(employerNo);
        record.setFileNo(fileNo);
        record.setOperatorLoginName(vo.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName(reportTypeEnum.getFileName());
        record.setReportType(reportTypeEnum.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(reportTypeEnum.getDataName());
        if(dataDictionary == null){
            log.error("商户后台转账回单导出字典未配置");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }
        setField(record, dataDictionary);
        exportRecordFacade.insert(record);
    }
}
