package com.zhixianghui.web.employer.vo.order.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-12 19:58
 **/
@Data
public class OrderQueryVo {
    /**
     * 批次状态
     */
    private Integer batchStatus;

    /**
     * 产品编号
     */
    private String productNo;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 发放方式
     */
    private Integer channelType;

    /**
     * 商户批次号
     */
    private String mchBatchNo;

    /**
     * 批次名
     */
    private String batchNameLike;

    /**
     * 创建起始时间
     */
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    private Date createEndDate;

    /**
     * 自由职业者服务编号
     */
    private String workCategoryCode;
}
