package com.zhixianghui.web.employer.biz.sign;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.enums.common.SuccessFailCodeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.SignSmsEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.vo.SignRecordVo;
import com.zhixianghui.web.employer.biz.order.excel.CheckSignRecordListener;
import com.zhixianghui.web.employer.biz.order.excel.SignRecordParsingListener;
import com.zhixianghui.web.employer.utils.BeanToMapUtil;
import com.zhixianghui.web.employer.utils.MultipartFileUtil;
import com.zhixianghui.web.employer.vo.sign.AddSignImagesVo;
import com.zhixianghui.web.employer.vo.sign.SignRecordQueryVo;
import com.zhixianghui.web.employer.vo.sign.SignRecordResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.yaml.snakeyaml.constructor.DuplicateKeyException;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/18 15:37
 **/
@Slf4j
@Service
public class SignBiz {
    @Reference
    private SignRecordFacade signRecordFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private SequenceFacade sequenceFacade;

    public PageResult<List<SignRecordResVo>> listPage(SignRecordQueryVo queryVo, PageParam pageParam,String employerNo) {
        Map<String,Object> paramMap = BeanUtil.toMap(queryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("updateBeginDate", queryVo.getCreateBeginDate());
        paramMap.put("updateEndDate", queryVo.getCreateEndDate());
        paramMap.put("createBeginDate", null);
        paramMap.put("createEndDate", null);
        paramMap.put("sortColumns", "a.UPDATE_TIME DESC");
        PageResult<List<SignRecord>> pageResult = signRecordFacade.listPage(paramMap,pageParam);
        if(CollectionUtils.isEmpty(pageResult.getData())){
            return PageResult.newInstance(pageParam,null);
        }else {
            List<SignRecordResVo> list = pageResult.getData().stream().map(
                    signRecord ->{
                        SignRecordResVo signRecordResVo = new SignRecordResVo();
                        BeanUtils.copyProperties(signRecord,signRecordResVo);
                        signRecordResVo.setReceiveName(signRecord.getReceiveNameDesensitize());
                        signRecordResVo.setReceiveIdCardNo(signRecord.getReceiveIdCardNoDesensitize());
                        signRecordResVo.setReceivePhoneNo(signRecord.getReceivePhoneNoDesensitize());
                        return signRecordResVo;
                    }
            ).collect(Collectors.toList());
            return PageResult.newInstance(list,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
        }
    }

    /**
     * 签约记录导出
     * @param queryVo
     * @param staffVO
     */
    public void exportSignRecord(SignRecordQueryVo queryVo, EmployerStaffVO staffVO) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(queryVo);
        paramMap.put("employerNo",staffVO.getMchNo());

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(staffVO.getMchNo());
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());

        record.setFileName(ReportTypeEnum.SIGN_RECORD_MER.getFileName());
        record.setReportType(ReportTypeEnum.SIGN_RECORD_MER.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.SIGN_RECORD_MER.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
    }

    private static final int FILE_SIZE = 5;
    private static final String UNIT = "M";
    private static final String SUFFIX = ".xlsx";
    /**
     * excel文件行头数
     */
    private static final int HEAD_ROW_NUMBER = 2;

    public boolean importSignRecord(MultipartFile multiPartfile, String mainstayNo, EmployerStaffVO staffVO, Integer sms) {
        // 先检查是否配置模板
        if (!signRecordFacade.isExistTemplate(mainstayNo, staffVO.getMchNo())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未配置模板");
        }
        // 检查上传文件
        if(!MultipartFileUtil.checkFileSize(multiPartfile.getSize(), FILE_SIZE, UNIT)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件大小超过限制");
        }

        if(StringUtils.isBlank(multiPartfile.getOriginalFilename()) ||
                !multiPartfile.getOriginalFilename().endsWith(SUFFIX)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件格式有误，不能为空且必须为xlsx格式");
        }

        File excelFile = MultipartFileUtil.transfer2File(multiPartfile);
        EasyExcel.read(excelFile, SignRecordVo.class, new CheckSignRecordListener(HEAD_ROW_NUMBER)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        log.info("[{}]==>检查文件具体内容通过", JSONObject.toJSON(staffVO));

        sign(excelFile, mainstayNo, staffVO, sms);

        return true;
    }

    private void sign(File excelFile, String mainstayNo, EmployerStaffVO staffVO, Integer sms) {
        List<SignRecordVo> signRecordVoList = new ArrayList<>();

        EasyExcel.read(excelFile, SignRecordVo.class, new SignRecordParsingListener(HEAD_ROW_NUMBER, signRecordVoList, dataDictionaryFacade, sms)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        List<SignRecord> signRecordList = new ArrayList<>();
        for (SignRecordVo item : signRecordVoList) {
            item.setMainstayNo(mainstayNo);
            item.setEmployerNo(staffVO.getMchNo());

            String userId = MD5Util.getMixMd5Str(item.getMainstayNo()+item.getEmployerNo()+item.getIdCard()+item.getName());
            log.info("当前员工信息: {}", JSONObject.toJSON(staffVO));
            SignRecord record = item.toSignRecord(item, userId, mainstayNo, staffVO.getMchName(), staffVO.getMchNo(), sms);
            if (sms == SignSmsEnum.UN_SEND.getStatus()) {
                record.setSignType(ChannelSignTypeEnum.URL_NO_CODE.getValue());
            }
            try {
                if (signRecordFacade.auth(record)) {
                    signRecordList.add(record);
                }
            } catch (DuplicateKeyException e) {
                log.warn("目前已经存在该条数据: {}", record, e);
            }
        }

        if (CollectionUtils.isEmpty(signRecordList)) {
            return;
        }

        signRecordList.forEach(item -> {
            try {
                signRecordFacade.preSign(item, true);
//                signRecordFacade.preSign(item, item.getSignType() != ChannelSignTypeEnum.STAMP_SILENCE.getValue());
            } catch (BizException e) {
                log.error("签约失败：", e);
            }
        });
    }



    private static final int LIMIT_SEND_VALUE = 3;
    public boolean resend(Long signId, EmployerStaffVO staffVO) {
        SignRecord record = signRecordFacade.getById(signId);
        record.setSignType(ChannelSignTypeEnum.MSG.getValue());
        if (record == null) {
            return false;
        }
        if (record.getInfoStatus() != null && record.getInfoStatus() == SuccessFailCodeEnum.SUCCESS.getValue()
                && StringUtils.isNotBlank(record.getFileUrl())) {
            return true;
        }
        if (StringUtils.isBlank(record.getReceivePhoneNoDecrypt())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约手机号不能为空");
        }
        // 两次发送之间的间隔必须大于1天即24h
        log.info("当前用户 : {}, 上一次发送时间 : {}, 累计发送次数 : {}", staffVO.getId(), record.getSmsSendTime(), record.getSmsSendFrequency());
        if (record.getSmsSendTime() != null && DateUtil.subtractDays(new Date(), record.getSmsSendTime()) > 1) {
            record.setSmsSendFrequency(0);
            record.setUpdateTime(new Date());
            signRecordFacade.resetSmsSendFrequency(record);
            record = signRecordFacade.getById(signId);
        }
        if (record.getSmsSendTime() != null &&
                DateUtil.subtractDays(new Date(), record.getSmsSendTime()) <= 1) {
            if (record.getSmsSendFrequency() != null && record.getSmsSendFrequency() >= LIMIT_SEND_VALUE) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发送短信24h小时内不能超过3次");
            }
        }
        return signRecordFacade.resend(record);
    }


    public boolean modify(SignRecordQueryVo queryVo) {
        return signRecordFacade.modify(queryVo.toSignRecordVo(queryVo));
    }

    public void addSignImages(AddSignImagesVo addSignImagesVo) {
        SignRecord signRecord = signRecordFacade.getById(addSignImagesVo.getSignId());
        if (StringUtils.isNotBlank(addSignImagesVo.getCerFaceUrl())) {
            signRecord.setCerFaceUrl(addSignImagesVo.getCerFaceUrl());
        }
        if (StringUtils.isNotBlank(addSignImagesVo.getIdCardFrontUrl())) {
            signRecord.setIdCardFrontUrl(addSignImagesVo.getIdCardFrontUrl());
        }
        if (StringUtils.isNotBlank(addSignImagesVo.getIdCardBackUrl())) {
            signRecord.setIdCardBackUrl(addSignImagesVo.getIdCardBackUrl());
        }
        if (Objects.nonNull(addSignImagesVo.getIdCardType())) {
            signRecord.setIdCardType(addSignImagesVo.getIdCardType());
        }
        if (StringUtils.isNotBlank(addSignImagesVo.getSignatureUrl())) {
            signRecord.setPersonalSignature(addSignImagesVo.getSignatureUrl());
        }
        if (StringUtils.isNotBlank(addSignImagesVo.getIdCardCopyFileUrl())) {
            signRecord.setIdCardCopyUrl(addSignImagesVo.getIdCardCopyFileUrl());
        }
        signRecordFacade.addSignImages(signRecord);
    }

    public boolean exist(SignRecordQueryVo queryVo, EmployerStaffVO staffVO) {
        SignRecord signRecord = signRecordFacade.getById(queryVo.getId());
        if (signRecord == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该记录不存在");
        }
//        SignRecordVo signRecordVo = new SignRecordVo();
//        signRecordVo.setEmployerNo(staffVO.getMchNo());
//        signRecordVo.setName(queryVo.getReceiveName());
//        signRecordVo.setIdCard(queryVo.getReceiveIdCardNo());
//        signRecordVo.setMainstayNo(signRecord.getMainstayNo());
//
//        if (signRecordFacade.isExist(signRecordVo) == null) {
//            return false;
//        }
//        return queryVo.getReceivePhoneNo().equals(signRecord.getReceivePhoneNoDecrypt());
        return false;
    }
}
