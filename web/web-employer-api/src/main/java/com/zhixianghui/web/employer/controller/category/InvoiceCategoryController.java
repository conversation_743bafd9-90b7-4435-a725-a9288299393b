package com.zhixianghui.web.employer.controller.category;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.config.InvoiceCategory;
import com.zhixianghui.facade.common.service.InvoiceCategoryFacade;
import com.zhixianghui.facade.merchant.entity.user.pms.PmsOperator;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 发票类目配置
 * <AUTHOR>
 * @date 2020/8/10
 **/
@RestController
@RequestMapping("invoiceCategory")
public class InvoiceCategoryController {
    @Reference
    private InvoiceCategoryFacade invoiceCategoryFacade;

    @GetMapping("getAll")
    public RestResult getAll(){
        List<InvoiceCategory> list = invoiceCategoryFacade.listAll();
        return RestResult.success(list);
    }
}
