package com.zhixianghui.web.employer.controller.trade;

import com.jcraft.jsch.ChannelSftp;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.service.JobWorkerRecordFacade;
import com.zhixianghui.facade.merchant.enums.BalancedEnum;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.enums.FeeOrderStatus;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.utils.CertificateUtil;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月19日 15:20:00
 */
@RequestMapping("/download")
@RestController
@Slf4j
public class DownloadController {

    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;
    @Reference
    private JobWorkerRecordFacade jobWorkerRecordFacade;

    private static final String SUFFIX = ".zip";

    @Value("${sftp.host}")
    private String host;
    @Value("${sftp.port}")
    private int port;
    @Value("${sftp.userName}")
    private String userName;
    @Value("${sftp.pwd}")
    private String pwd;
    @Value("${sftp.dir}")
    private String dir;
    @Autowired
    private FastdfsClient fastdfsClient;

    @GetMapping("/fastdf")
    public void download(@RequestParam("path")String path,
                         @RequestParam(value = "fileName",defaultValue = "file")String fileName, 
                         HttpServletResponse response) throws IOException {
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            InputStream inputStream = fastdfsClient.downloadFile(path);
            byte[] buffer = new byte[1024*4];
            int n = 0;
            response.reset();
            response.setContentType("application/x-zip-compressed;charset=utf-8");
            fileName=new String(fileName.getBytes("gb2312"), "ISO8859-1");
            response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
            while (-1 != (n = inputStream.read(buffer))) {
                os.write(buffer, 0, n);
            }
            os.flush();
        } catch (Exception e) {
            log.error("文件不存在：", e);
            String errMsg = e.getMessage().contains("文件不存在") ? "文件不存在" : "文件异常";
            if (os != null) {
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes(StandardCharsets.UTF_8));
            } else {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("文件不存在");
            }
        } 
    }

    private String getName(String str){
        return str==null?"未知":str;
    }

    @GetMapping("/jobworker/devFile/{ids}")
    public void downloadDevFile(@PathVariable("ids") List<Long> ids, HttpServletResponse response) throws IOException {
        String tempPath = System.getProperty("user.dir") + File.separator + "download" + File.separator + RandomUtil.get16LenStr();
        FileUtils.creatDir(tempPath);
        File zipFile = null;
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            ids.forEach(id -> {
                try {
                    JobWorkerRecord jobWorkerRecord = jobWorkerRecordFacade.findById(id);
                    String fileUrl = jobWorkerRecord.getDeliverSignatureUrl();
                    if(StringUtil.isNotEmpty(fileUrl)){
                        String fileName = getName(jobWorkerRecord.getWorkerName()) + "_" + jobWorkerRecord.getWorkerPhone() + "_平台交付物明细表.pdf";
                        File file = FileUtils.createFile(tempPath + File.separator + fileName);
                        InputStream inputStream = fastdfsClient.downloadFile(fileUrl);
                        org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
                    }
                } catch (Exception e) {
                    log.error("[{}]下载交付明细异常：", id, e);
                }
            });
            zipFile = ZipUtil.zipFile(tempPath);
            response.reset();
            response.setContentType("application/x-zip-compressed;charset=utf-8");
            String zipFileName = "平台交付物明细表.zip";
            zipFileName = new String(zipFileName.getBytes("gb2312"), "ISO8859-1");
            response.addHeader("Content-Disposition", "attachment;filename=" + zipFileName);
            os.write(org.apache.commons.io.FileUtils.readFileToByteArray(zipFile));
            os.flush();
        } catch (Exception e) {
            log.error("下载交付明细异常：", e);
            String errMsg = e.getMessage().contains("文件不存在") ? "凭证文件不存在" : "下载交付明细异常";
            if (os != null) {
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes(StandardCharsets.UTF_8));
            } else {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("下载交付明细异常");
            }
        } finally {
            FileUtils.deleteDir(new File(tempPath));
            zipFile.delete();
        }

    }

    @GetMapping("/jobworker/resFile/{ids}")
    public void downloadResFile(@PathVariable("ids") List<Long> ids, HttpServletResponse response) throws IOException {
        String tempPath = System.getProperty("user.dir") + File.separator + "download" + File.separator + RandomUtil.get16LenStr();
        FileUtils.creatDir(tempPath);
        File zipFile = null;
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            ids.forEach(id -> {
                try {
                    JobWorkerRecord jobWorkerRecord = jobWorkerRecordFacade.findById(id);
                    String fileUrl = jobWorkerRecord.getResultSignatureUrl();
                    if(StringUtil.isNotEmpty(fileUrl)){
                        String fileName = getName(jobWorkerRecord.getWorkerName()) + "_" + jobWorkerRecord.getWorkerPhone() + "_平台成果验收单.pdf";
                        File file = FileUtils.createFile(tempPath + File.separator + fileName);
                        InputStream inputStream = fastdfsClient.downloadFile(fileUrl);
                        org.apache.commons.io.FileUtils.copyInputStreamToFile(inputStream, file);
                    }
                } catch (Exception e) {
                    log.error("[{}]下载成功验收单异常：", id, e);
                }
            });
            zipFile = ZipUtil.zipFile(tempPath);
            response.reset();
            response.setContentType("application/x-zip-compressed;charset=utf-8");
            String zipFileName = "成果验收单.zip";
            zipFileName = new String(zipFileName.getBytes("gb2312"), "ISO8859-1");
            response.addHeader("Content-Disposition", "attachment;filename=" + zipFileName);
            os.write(org.apache.commons.io.FileUtils.readFileToByteArray(zipFile));
            os.flush();
        } catch (Exception e) {
            log.error("下载成果验收单异常：", e);
            String errMsg = e.getMessage().contains("文件不存在") ? "凭证文件不存在" : "下载成果验收单异常";
            if (os != null) {
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes(StandardCharsets.UTF_8));
            } else {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("下载成果验收单异常");
            }
        } finally {
            FileUtils.deleteDir(new File(tempPath));
            zipFile.delete();
        }

    }

    @GetMapping("/feeOrder/certificateFile")
    public void downloadFeeOrder(@RequestParam("feeBatchNo") String feeBatchNo, HttpServletResponse response) throws
            Exception {
        FeeOrderBatch feeOrderBatch = feeOrderBatchFacade.getByBatchNo(feeBatchNo);
        AssertUtil.notNull(feeOrderBatch, "找不到对应批次号");
        if (!feeOrderBatch.getStatus().equals(FeeOrderStatus.SUCCESS.getCode())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("订单未付款");
        }
        if (feeOrderBatch.getBalancedMode().equals(BalancedEnum.ONLINE_BALANCED.getCode())
                && StringUtils.isBlank(feeOrderBatch.getReceiptUrl())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("电子回单暂未生成");
        }

        String fileName = feeBatchNo + SUFFIX;
        String sftpFilePath = CertificateUtil.getFeeFilePath(dir, feeOrderBatch.getEmployerNo(), feeBatchNo);

        // 创建临时文件夹
        String tempPath = System.getProperty("user.dir") + File.separator + "download" + File.separator + RandomUtil.get16LenStr();
        ChannelSftp channelSftp = null;
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            FileUtils.creatDir(tempPath);
            channelSftp = SftpUtil.connect(host, port, userName, pwd);
            File file = FileUtils.createFile(tempPath + File.separator + fileName);
            SftpUtil.downloadNoClose(sftpFilePath + fileName, file, channelSftp);
            response.reset();
            response.setContentType("application/x-zip-compressed;charset=utf-8");
            String zipFileName = "电子回单_" + feeBatchNo + ".zip";
            zipFileName = new String(zipFileName.getBytes("gb2312"), "ISO8859-1");
            response.addHeader("Content-Disposition", "attachment;filename=" + zipFileName);
            os.write(org.apache.commons.io.FileUtils.readFileToByteArray(file));
            os.flush();
        } catch (Exception e) {
            log.error("{} 下载电子回单异常：", feeBatchNo, e);
            String errMsg = e.getMessage().contains("文件不存在") ? "凭证文件不存在" : "电子回单下载异常";
            if (os != null) {
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                os.write(JsonUtil.toString(RestResult.error(errMsg)).getBytes(StandardCharsets.UTF_8));
            } else {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("下载电子回单文件异常");
            }
        } finally {
            FileUtils.deleteDir(new File(tempPath));
            if (channelSftp != null) {
                SftpUtil.sftpClose(channelSftp);
            }
        }
    }
}