package com.zhixianghui.web.employer.config;

import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.zhixianghui.web.employer.constant.PermissionConstant;
import org.hibernate.validator.HibernateValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Author: Cmf
 * Date: 2019/10/9
 * Time: 17:20
 * Description:
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Autowired
    private PermissionInterceptor permissionInterceptor;

    @Autowired
    private AllowCrossFilter allowCrossFilter;

    @Autowired
    private RequestBodyFilter requestBodyFilter;

    @Autowired
    private MethodArgumentResolver methodArgumentResolver;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(permissionInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/user/login", "/user/captcha", "/user/sendSmsCode", "/user/resetPwd",
                        "/user/getUserStatus", "/error", "/user/getUserPublickey", "/weChat/**",
                        "/tenant/getTenant","/industryType/listAll","/workCategory/listAll","/invoiceCategory/getAll","/anonMerchant/**","/portalDataDictionary/listAllDataDictionaryVO");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/statics/**").addResourceLocations("classpath:/statics/");

    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(methodArgumentResolver);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("*")
                .allowedHeaders("content-type", PermissionConstant.REQUEST_TOKEN_HEADER)
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Bean
    public FilterRegistrationBean<AllowCrossFilter> allowCrossFilterRegistrationBean() {
        FilterRegistrationBean<AllowCrossFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(allowCrossFilter);
        return registrationBean;
    }

    @Bean
    public FilterRegistrationBean<RequestBodyFilter> requestBodyFilterRegistrationBean(){
        FilterRegistrationBean<RequestBodyFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(requestBodyFilter);
        registration.addUrlPatterns("/*");
        registration.setName("requestBodyFilter");
        registration.setOrder(1);
        return registration;
    }

    @Bean
    public Validator validator(){
        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .addProperty( "hibernate.validator.fail_fast", "true" )  // 快速失败模式
                .buildValidatorFactory();

        return validatorFactory.getValidator();
    }

    @Bean(name = "multipartResolver")
    public CommonsMultipartResolver getCommonsMultipartResolver() {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        multipartResolver.setMaxUploadSize(20971520);
        multipartResolver.setMaxInMemorySize(1048576);
        return multipartResolver;
    }

    @Bean
    public Converter<String, LocalDateTime> localDateTimeConverter() {
        return new Converter<String, LocalDateTime>() {
            @Override
            public LocalDateTime convert(String source) {
                return LocalDateTime.parse(source, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        };
    }

    @Bean
    public Converter<String, LocalDate> localDateConverter() {
        return new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(String source) {
                return LocalDate.parse(source, DateTimeFormatter.ofPattern("yyyy-MM_dd"));
            }
        };
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer mapperBuilderCustomizer() {
        return new Jackson2ObjectMapperBuilderCustomizer() {
            @Override
            public void customize(Jackson2ObjectMapperBuilder jacksonObjectMapperBuilder) {
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTimeDeserializer deserializer = new LocalDateTimeDeserializer(dateTimeFormatter);
                LocalDateTimeSerializer serializer = new LocalDateTimeSerializer(dateTimeFormatter);
                jacksonObjectMapperBuilder.serializerByType(LocalDateTime.class, serializer);
                jacksonObjectMapperBuilder.deserializerByType(LocalDateTime.class, deserializer);
            }
        };
    }
}
