package com.zhixianghui.web.employer.biz.order.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @description 发放导出实体行
 * @date 2020-11-04 10:48
 **/
@Data
@ColumnWidth(25)
@HeadFontStyle(fontHeightInPoints = 12)
@HeadStyle(horizontalAlignment = HorizontalAlignment.LEFT, shrinkToFit = true, wrapped = false)
public class BankGrantRowExcel {

    private final static String HEAD = "* 此模版抬头信息内容和顺序请勿修改，并将所有表格内容设成文本格式  " + (char) 10 +
            "1、单批次订单数量建议少于 50000 条" + (char) 10 +
            "2、单人每月的最大代征金额以合作信息为准，如有疑问请联系客户经理" + (char) 10 +
            "3、收款人手机号通常必填，实际以合作代征主体为准" + (char) 10 +
            "4、转账给微信个人账户，限制单笔最低0.3元。";

    /**
     * 持卡人姓名
     */
    @ExcelProperty(value = {HEAD, "姓名（必填）"})
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    @ExcelProperty(value = {HEAD, "身份证号（必填）"})
    private String receiveIdCardNo;

    /**
     * 收款账户
     */
    @ExcelProperty(value = {HEAD, "微信openid（必填）"})
    private String receiveAccountNo;

    /**
     * 银行预留手机号
     */
    @ExcelProperty(value = {HEAD, "收款人手机号（必填）"})
    private String receivePhoneNo;

    /**
     * 请求金额，商户实际传入的金额
     */
    @ExcelProperty(value = {HEAD, "发放金额/元（四舍五入至分，必填）"})
    private String requestAmountStr;

    /**
     * 备注
     */
    @ExcelProperty(value = {HEAD, "备注（30个字符以内，非必填）"})
    private String desc;

    /**
     * 商户订单号
     */
    @ExcelProperty(value = {HEAD, "商户订单号（非必填）"})
    private String mchOrderNo;

    @ExcelProperty(value = {HEAD, "商户备忘录（30个字符以内-非必填）"})
    private String memo;
    @ExcelProperty(value = {HEAD, "微信appid（必填）"})
    private String appId;
}
