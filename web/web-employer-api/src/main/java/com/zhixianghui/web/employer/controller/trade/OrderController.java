package com.zhixianghui.web.employer.controller.trade;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.dto.withdraw.WithdrawRecordQueryDto;
import com.zhixianghui.common.statics.enums.merchant.MerchantTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.TimeRangeUtil;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.fee.entity.MerchantProductRelation;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.bo.EmployerOrderItemSumBo;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.vo.AuthInfoVo;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentOperatorVO;
import com.zhixianghui.web.employer.annotation.CurrentSession;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.biz.order.AuthOrderBiz;
import com.zhixianghui.web.employer.biz.order.OrderBiz;
import com.zhixianghui.web.employer.biz.order.OrderHandlerFactory;
import com.zhixianghui.web.employer.biz.order.OrderItemBiz;
import com.zhixianghui.web.employer.biz.order.RechargeBiz;
import com.zhixianghui.web.employer.biz.order.WithdrawRecordBiz;
import com.zhixianghui.web.employer.component.DataDecryptHelper;
import com.zhixianghui.web.employer.component.TradePwdHelper;
import com.zhixianghui.web.employer.constant.PermissionConstant;
import com.zhixianghui.web.employer.dto.Session;
import com.zhixianghui.web.employer.vo.PageVo;
import com.zhixianghui.web.employer.vo.order.req.HangOrderItemQueryVo;
import com.zhixianghui.web.employer.vo.order.req.OrderAcceptReqVo;
import com.zhixianghui.web.employer.vo.order.req.OrderItemFailQueryVo;
import com.zhixianghui.web.employer.vo.order.req.OrderItemQueryVo;
import com.zhixianghui.web.employer.vo.order.req.OrderQueryVo;
import com.zhixianghui.web.employer.vo.order.res.HangOrderItemResVo;
import com.zhixianghui.web.employer.vo.order.res.MainstayResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderItemFailResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderItemQryResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderItemResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderWithCountResVo;
import com.zhixianghui.web.employer.vo.permission.RechargeVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-03 15:46
 **/
@RestController
@RequestMapping("order")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OrderController {

    //private final OrderHandlerBiz orderHandlerBiz;
    private final OrderItemBiz orderItemBiz;
    private final OrderBiz orderBiz;
    private final DataDecryptHelper dataDecryptHelper;
    private final TradePwdHelper tradePwdHelper;
    private final WithdrawRecordBiz withdrawRecordBiz;
    private final OrderHandlerFactory orderHandlerFactory;
    private final RechargeBiz rechargeBiz;
    private final AuthOrderBiz authOrderBiz;
    private final OfflineOrderItemFacade offlineOrderItemFacade;

    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private OrderItemFacade orderItemFacade;

    @Permission("trade:order:receipt")
    @PostMapping("exportExcel/{type}")
    public RestResult exportExcel(@PathVariable Integer type,@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                  @CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo){
        checkDate(orderItemQueryVo);
        orderItemBiz.exportReceipt(type,orderItemQueryVo,vo,employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 提交发放名单
     * @param orderAcceptReqVo 发放名单信息
     * @param operator 操作人
     * @param employerNo 用工企业
     * @return 批次编号
     * @throws Exception 异常
     */
    @Logger(type = OperateLogTypeEnum.CREATE, action = "提交发放名单")
    @Permission("order:batchOrderUpload:upload")
    @PostMapping("batchOrderUpload")
    public RestResult<Map<String, String>> batchOrderUpload(@Validated OrderAcceptReqVo orderAcceptReqVo, @CurrentOperatorVO EmployerOperatorVO operator, @CurrentMchNo String employerNo) throws Exception {
        Map<String, String> result = orderHandlerFactory.getHandler(orderAcceptReqVo.getProductNo()).orderUpload(orderAcceptReqVo,operator,employerNo);
        authOrderBiz.initAuthInfo(employerNo,result.get("platBatchNo"));
        return RestResult.success(result);
    }


    /**
     * 逻辑删除发放批次
     * @param id 发放id
     * @param beginDate 起始时间
     * @param endDate 结束时间
     * @return 批次编号
     * @throws Exception 异常
     */
    @Permission("order:deleteOrder:delete")
    @PostMapping("deleteOrder")
    public RestResult deleteOrder(@RequestBody OrderDeleteDTO orderDeleteDTO){
        orderBiz.delete(orderDeleteDTO);
        return RestResult.success("删除成功");
    }

    /**
     * 发放名单
     * @param orderQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 发放批次列表
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderPage")
    public RestResult<PageResult<List<OrderResVo>>> listOrderPage(@Validated @RequestBody OrderQueryVo orderQueryVo,
                                                                  @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        orderQueryTimeHandler(orderQueryVo);
        PageResult<List<OrderResVo>> result = orderBiz.listOrderPage(orderQueryVo,pageVo,employerNo);
        return RestResult.success(result);
    }

    /**
     * 发放名单 总条数
     * @param orderQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 总条数
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("countOrder")
    public RestResult<Map<String,Object>> countOrder(@Validated @RequestBody OrderQueryVo orderQueryVo,
                                                        @CurrentMchNo String employerNo) {
        orderQueryTimeHandler(orderQueryVo);
        Long totalRecord = orderBiz.countOrder(orderQueryVo,employerNo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 订单明细
     * @param orderItemQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 订单明细列表
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("listOrderItemPage")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemPage(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        checkDate(orderItemQueryVo);
        PageResult<List<OrderItemResVo>> result = orderItemBiz.listOrderItemPage(orderItemQueryVo,pageVo,employerNo);
        return RestResult.success(result);
    }


    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 总条数
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("countOrderItem")
    public RestResult<Map<String, Object>> countOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                                       @CurrentMchNo String employerNo) {
        checkDate(orderItemQueryVo);
        Long totalRecord = orderItemBiz.countOrderItem(orderItemQueryVo,employerNo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }



    /**
     * 电子回单页面的订单明细
     * @param orderItemQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 订单明细列表
     */
    @Permission("trade:order:receipt")
    @PostMapping("listOrderItemPageForEmployer")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemPageForEmployer(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                                          @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        checkDate(orderItemQueryVo);
        PageResult<List<OrderItemResVo>> result = orderItemBiz.listOrderItemPage(orderItemQueryVo,pageVo,employerNo);
        return RestResult.success(result);
    }


    /**
     * 电子回单页面的订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 总条数
     */
    @Permission("trade:order:receipt")
    @PostMapping("countOrderItemForEmployer")
    public RestResult<Map<String, Object>> countOrderItemForEmployer(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                          @CurrentMchNo String employerNo) {
        checkDate(orderItemQueryVo);
        Long totalRecord = orderItemBiz.countOrderItem(orderItemQueryVo,employerNo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @return 总条数
     */
//    @Permission("order:listOrderItemPage:sum")
    @PostMapping("sumOrderItem")
    public RestResult<EmployerOrderItemSumBo> sumOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                           @CurrentMchNo String employerNo){
        checkDate(orderItemQueryVo);
        OrderItemSumBo sumBo = orderItemBiz.sumOrderItem(orderItemQueryVo, employerNo);

        if (orderItemQueryVo.getOrderItemStatusList() != null && !orderItemQueryVo.getOrderItemStatusList().isEmpty()) {
            return RestResult.success(new EmployerOrderItemSumBo(sumBo,null,null,null));
        }
        orderItemQueryVo.setOrderItemStatus(OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        orderItemQueryVo.setOrderItemStatusList(null);
        OrderItemSumBo successItemSumBo = orderItemBiz.sumOrderItem(orderItemQueryVo, employerNo);

        orderItemQueryVo.setOrderItemStatus(null);
        orderItemQueryVo.setOrderItemStatusList(
                ListUtil.of(OrderItemStatusEnum.GRANT_FAIL.getValue(),
                        OrderItemStatusEnum.GRANT_SPECIAL_FAIL.getValue(),
                        OrderItemStatusEnum.GRANT_CANCEL.getValue())
        );
        OrderItemSumBo failItemSumBo = orderItemBiz.sumOrderItem(orderItemQueryVo, employerNo);

        orderItemQueryVo.setOrderItemStatusList(
                ListUtil.of(OrderItemStatusEnum.ACCEPTED.getValue(),
                        OrderItemStatusEnum.GRANTING.getValue(),
                        OrderItemStatusEnum.CREATE.getValue(),
                        OrderItemStatusEnum.GRANT_HANG.getValue())
        );
        OrderItemSumBo proccessingSumBo = orderItemBiz.sumOrderItem(orderItemQueryVo, employerNo);
        return RestResult.success(new EmployerOrderItemSumBo(sumBo,successItemSumBo,failItemSumBo,proccessingSumBo));
    }

    /**
     * 导出订单明细
     * @param orderItemQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.QUERY, action = "导出订单明细")
    @Permission("order:listOrderItemPage:export")
    @PostMapping("exportOrderItem")
    public RestResult<String> exportOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                              @CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo) {
        checkDate(orderItemQueryVo);
        orderItemBiz.exportOrderItem(orderItemQueryVo,vo, employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private void checkDate(@RequestBody @Validated OrderItemQueryVo orderItemQueryVo) {


        orderItemQueryTimeHandler(orderItemQueryVo);
        Date completeBeginDate = orderItemQueryVo.getCreateBeginDate();
        Date completeEndDate = orderItemQueryVo.getCreateEndDate();
        if ((completeBeginDate != null && completeEndDate == null) ||
                (completeBeginDate == null && completeEndDate != null)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间必须成对选择");
        }

        if (completeBeginDate != null && DateUtil.compare(completeBeginDate, completeEndDate, Calendar.SECOND) > 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间的截止时间不能大于起始时间");
        }
    }

    /**
     * 开始发放：发放明细页
     * @return 发放明细列表
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderItemByBatchNoPage")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemByBatchNoPage(@Validated @RequestBody OrderItemQryResVo qryVo, @CurrentMchNo String employerNo) {
        String platBatchNo = qryVo.getPlatBatchNo();
        PageResult<List<OrderItemResVo>> result = orderItemBiz.listOrderItemByBatchNoPage(platBatchNo,qryVo,employerNo);
        return RestResult.success(result);
    }

    /**
     * 开始发放：发放明细页批次统计信息
     * @param platBatchNo 平台批次号
     * @param employerNo 商户编号
     * @return 批次统计信息
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("getOrderByBatchNo")
    public RestResult<OrderWithCountResVo> getOrderByBatchNo(@RequestParam String platBatchNo,
                                                             @CurrentMchNo String employerNo) {
        OrderWithCountResVo result = orderBiz.getOrderByBatchNo(platBatchNo,employerNo);
        return RestResult.success(result);
    }

    /**
     * 开始发放：发放明细页（根据不同状态）
     * @param platBatchNo 平台批次号
     * @param employerNo 商户编号
     * @return 不同状态的发放明细列表
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderItemByBatchNoAndStatus")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemByBatchNoAndStatus(@RequestParam String platBatchNo, Integer orderItemStatus,
                                                                                   @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        PageResult<List<OrderItemResVo>> result = orderItemBiz.listOrderItemByBatchNoAndStatus(platBatchNo,orderItemStatus,pageVo,employerNo);
        return RestResult.success(result);
    }


    /**
     * 挂起订单
     * @param hangOrderItemQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 挂起的明细
     */
    @Permission("order:listHangOrderItemPage:view")
    @PostMapping("listHangOrderItemPage")
    public RestResult<PageResult<List<HangOrderItemResVo>>> listHangOrderItemPage(@Validated @RequestBody HangOrderItemQueryVo hangOrderItemQueryVo,
                                                                                  @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        hangOrderItemQueryTimeHandler(hangOrderItemQueryVo);

        PageResult<List<HangOrderItemResVo>> result = orderItemBiz.listHangOrderItemPage(hangOrderItemQueryVo,pageVo,employerNo);
        result.getData().forEach(e->{
            OrderItem orderItem;
            if (e.getPlatTrxNo().startsWith("OT")) {
                orderItem = new OrderItem();
                final OfflineOrderItem item = offlineOrderItemFacade.getOrderItemByPlatTrxNo(e.getPlatTrxNo());
                BeanUtil.copyProperties(item, orderItem);
            }else {
                orderItem = orderItemFacade.getByPlatTrxNo(e.getPlatTrxNo());
            }
            AuthInfoVo authInfo = orderItemFacade.getAuthInfo(orderItem);
            e.setAuthStatus(authInfo.getAuth());
            e.setSignStatus(authInfo.getSign());
        });
        return RestResult.success(result);
    }

    /**
     * 根据用工企业编号展示开启状态的代征主体
     *
     * @param employerNo 用工企业编号
     * @return 该商户开启状态的代征主体
     */
    @GetMapping("listOpenMainstayByEmployerNo")
    public RestResult<List<MainstayResVo>> listOpenMainstayByEmployerNo(@RequestParam(value = "status", required = false) Integer status, @CurrentMchNo String employerNo) {
        List<MainstayResVo> result = orderBiz.listOpenMainstayByEmployerNo(employerNo, status);
        return RestResult.success(result);
    }

    @GetMapping("listOpenMainstayByEmployerNoAndProductNo")
    public RestResult<List<MainstayResVo>> listOpenMainstayByEmployerNoAndProductNo(@RequestParam(value = "productNo") String productNo,@RequestParam(value = "status", required = false) Integer status, @CurrentMchNo String employerNo) {
        List<MainstayResVo> result = orderBiz.listOpenMainstayByEmployerNoAndProductNo(productNo,employerNo, status);
        return RestResult.success(result);
    }

    @GetMapping("listAllProduct")
    public RestResult listAllProduct(@CurrentMchNo String mchNo){
        List<MerchantProductRelation> result = orderBiz.listAllProduct(mchNo);
        return RestResult.success(result);
    }

    /**
     * 根据用工企业编号展示所有的代征主体
     * @param employerNo 用工企业编号
     * @return 该商户所有的代征主体
     */
    @GetMapping("listAllMainstayByEmployerNo")
    public RestResult<List<MainstayResVo>> listAllMainstayByEmployerNo(@CurrentMchNo String employerNo){
        List<MainstayResVo> result = orderBiz.listAllMainstayByEmployerNo(employerNo);
        return RestResult.success(result);
    }

    /**
     * 根据用工企业编号展示开启状态的发放方式
     * @param employerNo 用工企业编号
     * @return 该商户开启状态的发放方式
     */
    @GetMapping("listOpenChannelTypeByEmployerNo")
    public RestResult<List<Map<String,Object>>> listOpenChannelTypeByEmployerNo(@RequestParam String mainstayNo,@CurrentMchNo String employerNo){
        List<Map<String,Object>> result = orderBiz.listOpenChannelTypeByEmployerNo(mainstayNo,employerNo);
        return RestResult.success(result);
    }


    /**
     * 取消订单发放
     * @param paramMap 订单批次号map
     * @param employerNo 用工企业编号
     * @return 提示信息
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "取消订单发放")
    @Permission("order:cancelBatchOrderGrant:edit")
    @PostMapping("cancelBatchOrderGrant")
    public RestResult<String> cancelBatchOrderGrant(@RequestBody Map<String, String> paramMap,@CurrentMchNo String employerNo){
        String platBatchNo = paramMap.get("platBatchNo");
        if (StringUtils.isEmpty(platBatchNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("platBatchNo 不能为空");
        }
        orderBiz.cancelBatchOrder(platBatchNo,employerNo);
        return RestResult.success("取消成功");
    }

    /**
     * 确认发放
     * @param paramMap 订单批次号map
     * @param employerNo 用工企业编号
     * @return 提示信息
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "确认发放")
    @Permission("order:confirmBatchOrderGrant:edit")
    @PostMapping("confirmBatchOrderGrant")
    public RestResult<String> confirmBatchOrderGrant(@RequestBody Map<String, String> paramMap,
                                                     @CurrentMchNo String employerNo,
                                                     @CurrentSession Session session,
                                                     @CurrentStaffVo EmployerStaffVO staffVO){
        String tradePwd = paramMap.get("tradePwd");
        String platBatchNo = paramMap.get("platBatchNo");
        if (StringUtils.isEmpty(platBatchNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("platBatchNo 不能为空");
        }
        if (StringUtils.isEmpty(tradePwd)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("交易密码不能为空");
        }
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        //对交易密码做解密操作
        tradePwd = dataDecryptHelper.decryptData(privateKey, tradePwd);
        //校验密码
        tradePwdHelper.verifyTradePwd(staffVO,employerNo,tradePwd);
        //检验信息和发放
        orderBiz.confirmBatchOrder(platBatchNo,employerNo);
        return RestResult.success("提交成功");
    }

    /**
     * 发放页:导出订单明细
     * @param platBatchNo 平台批次号
     * @param employerNo 商户编号
     * @return 导出不同状态的发放明细
     */
    @Logger(type = OperateLogTypeEnum.QUERY, action = "发放页:导出订单明细")
    @Permission("order:listOrderPage:export")
    @PostMapping("exportOrderItemByBatchNoAndStatus")
    public RestResult<String> exportOrderItemByBatchNoAndStatus(@RequestParam String platBatchNo, Integer orderItemStatus,
                                                                @CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo) {
        OrderItemQueryVo orderItemQueryVo = new OrderItemQueryVo();
        orderItemQueryVo.setPlatBatchNo(platBatchNo);
        if(orderItemStatus != null){
            orderItemQueryVo.setOrderItemStatus(orderItemStatus);
        }
        orderItemBiz.exportOrderItem(orderItemQueryVo, vo,employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }


    /**
     * 导出风控订单
     * @param hangOrderItemQueryVo 导出条件
     * @param employerNo 商户号
     * @return 提示
     */
    @Logger(type = OperateLogTypeEnum.QUERY, action = "导出风控订单")
    @Permission("order:listHangOrderItemPage:view")
    @PostMapping("exportHangOrderItem")
    public RestResult<String> exportHangOrderItem(@Validated @RequestBody HangOrderItemQueryVo hangOrderItemQueryVo,
                                                  @CurrentStaffVo EmployerStaffVO staffVo, @CurrentMchNo String employerNo) {

        hangOrderItemQueryTimeHandler(hangOrderItemQueryVo);

        orderItemBiz.exportHangOrderItem(hangOrderItemQueryVo,staffVo,employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @Permission("order:listOrderPage:view")
    @PostMapping("getAmount")
    public RestResult<Map<String, Object>> getAmount(@RequestParam String mainstayNo,@RequestParam Integer channelType,
                                        @RequestParam String payChannelNo,@CurrentMchNo String employerNo){
        String amount = orderBiz.getAmount(mainstayNo,employerNo,channelType,payChannelNo);
        Map<String, Object> result = Maps.newHashMap();
        result.put("amount",amount);
        return RestResult.success(result);
    }

    @PostMapping("recharge")
//    @Permission("order:recharge:add")
    public RestResult<String> recharge(@Validated @RequestBody RechargeVo rechargeVo,
                                                @CurrentMchNo String employerNo,
                                                @CurrentOperatorVO EmployerOperatorVO operatorVO) {

        //充值金额不能低于0.01
        if (new BigDecimal(rechargeVo.getRechargeAmount()).compareTo(new BigDecimal("0.01")) < 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("充值金额不能小于0.01");
        }

        String rechargeUrl = rechargeBiz.recharge(rechargeVo,employerNo,operatorVO);

        return RestResult.success(rechargeUrl);
    }


    @Permission("order:withdraw.do")
    @PostMapping("withdraw")
    @Logger(type = OperateLogTypeEnum.CREATE,action = "提现")
    public RestResult<WithdrawRecord> withdraw(@RequestBody WithdrawDto withdrawDto,
                                               @CurrentMchNo String employerNo,
                                               @CurrentSession Session session,
                                               @CurrentStaffVo EmployerStaffVO staffVo) {
        log.info("接收到提现请求，提现参数为 WithdrawDto：{} employerNo：{} EmployerStaffVO：{}", JsonUtil.toString(withdrawDto), employerNo, JsonUtil.toString(staffVo));
        if (new BigDecimal(withdrawDto.getAmount()).compareTo(new BigDecimal("0.1")) < 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("金额不能低于0.1元");
        }
        if (StringUtils.isNotBlank(withdrawDto.getRemark()) && withdrawDto.getRemark().length() > 30) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("备注长度限制30");
        }
        if (StringUtils.isBlank(withdrawDto.getRemark())) {
            withdrawDto.setRemark("提现");
        }

        String payPasswd = withdrawDto.getPayPasswd();
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        //对交易密码做解密操作
        payPasswd = dataDecryptHelper.decryptData(privateKey, payPasswd);
        tradePwdHelper.verifyTradePwd(staffVo,employerNo,payPasswd);
        if (StringUtils.isBlank(withdrawDto.getEmployerNo())) {
            withdrawDto.setEmployerNo(employerNo);
        }
        WithdrawRecord withdrawRecord = orderBiz.withdraw(withdrawDto);
        log.info("本次提现记录为 WithdrawRecord: {}", JsonUtil.toString(withdrawRecord));
        return RestResult.success(withdrawRecord);
    }

    @Permission("order:withdraw.view")
    @PostMapping("listWithdrawRecordPage")
    public RestResult<PageResult<List<WithdrawRecord>>> listWithdrawRecordPage(@RequestBody @Validated WithdrawRecordQueryDto withdrawRecordQueryDto, @CurrentMchNo String employerNo, @RequestBody PageParam pageParam) {

        Date createBeginDate = withdrawRecordQueryDto.getCreateBeginDate();
        Date createEndDate = withdrawRecordQueryDto.getCreateEndDate();
        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            withdrawRecordQueryDto.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
            withdrawRecordQueryDto.setCreateEndDate(TimeRangeUtil.endTime());
        }


        validDate(withdrawRecordQueryDto.getCreateBeginDate(), withdrawRecordQueryDto.getCreateEndDate(), null, null);

        if (Objects.isNull(withdrawRecordQueryDto.getEmployerNo())) {
            withdrawRecordQueryDto.setEmployerNo(employerNo);
        }
        withdrawRecordQueryDto.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        log.info("查询参数:{}",JSON.toJSONString(withdrawRecordQueryDto));
        return RestResult.success(withdrawRecordBiz.listWithRecordPage(withdrawRecordQueryDto,pageParam));
    }

    @Permission("order:withdraw.view")
    @PostMapping("exportWithRecord")
    public RestResult<String> exportWithRecord(@RequestBody @Validated WithdrawRecordQueryDto withdrawRecordQueryDto,@CurrentMchNo String employerNo, @CurrentStaffVo EmployerStaffVO staffVo) {

        Date createBeginDate = withdrawRecordQueryDto.getCreateBeginDate();
        Date createEndDate = withdrawRecordQueryDto.getCreateEndDate();
        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            withdrawRecordQueryDto.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
            withdrawRecordQueryDto.setCreateEndDate(TimeRangeUtil.endTime());
        }
        validDate(withdrawRecordQueryDto.getCreateBeginDate(), withdrawRecordQueryDto.getCreateEndDate(), null, null);

        if (Objects.isNull(withdrawRecordQueryDto.getEmployerNo())) {
            withdrawRecordQueryDto.setEmployerNo(employerNo);
        }
        withdrawRecordQueryDto.setMerchantType(MerchantTypeEnum.EMPLOYER.getValue());
        withdrawRecordBiz.exportWithRecord(withdrawRecordQueryDto,staffVo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("getFailItemByBatchVo")
    public RestResult<PageResult<List<OrderItemFailResVo>>> getFailItemByBatchVo(@RequestBody OrderItemFailQueryVo queryVo, @RequestBody PageVo pageVo) {
        return RestResult.success(orderItemBiz.getFailItemByBatchVo(queryVo,pageVo));
    }

    @PostMapping("exportFailItemByBatchVo")
    public RestResult<String> exportFailItemByBatchVo(@RequestBody OrderItemFailQueryVo queryVo,@CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo) {
        orderItemBiz.exportOrderItemFail(queryVo, vo, employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private void validDate(Date createBeginDate, Date createEndDate, Date completeBeginDate, Date completeEndDate) {

        if (createBeginDate != null && createEndDate != null) {
            if(DateUtil.compare(createBeginDate, createEndDate, Calendar.SECOND) > 0){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("创建时间的截止时间不能大于起始时间");
            }
        }

        if (completeBeginDate != null || completeEndDate != null) {
            if((completeBeginDate != null && completeEndDate == null) ||
                    ( completeBeginDate == null && completeEndDate != null)){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间必须成对选择");
            }

            if(completeBeginDate != null && DateUtil.compare(completeBeginDate, completeEndDate, Calendar.SECOND) > 0){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间的截止时间不能大于起始时间");
            }
        }
    }

    private void orderQueryTimeHandler(OrderQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchBatchNo())) {
                orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestOneYearStartTime());
                orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
            }else {
                orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
            }
        }
    }

    private void orderItemQueryTimeHandler(OrderItemQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        log.info("原始参数：{}",JSON.toJSONString(orderQueryVo));

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchBatchNo(), orderQueryVo.getMchOrderNo(),orderQueryVo.getPlatBatchNo())) {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestOneYearStartTime());
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }else {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }
        }
        log.info("时间转换后的参数:{}", JSON.toJSONString(orderQueryVo));
    }

    private void hangOrderItemQueryTimeHandler(HangOrderItemQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchOrderNo(),orderQueryVo.getPlatTrxNo())) {
                orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestOneYearStartTime());
                orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
            }else {
                orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
            }
        }
    }
}
