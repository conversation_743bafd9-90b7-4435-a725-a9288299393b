package com.zhixianghui.web.employer.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Author: Cmf
 * Date: 2019/10/30
 * Time: 15:30
 * Description:
 */
public class NetUtil {

    private static final Logger log = LoggerFactory.getLogger(NetUtil.class);

    /**
     * 获取客户端IP地址
     *
     * @param request .
     * @return .
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if (ip.equals("127.0.0.1")) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    log.error("==>WebUtil getIpAddr:", e);
                }
                ip = inet.getHostAddress();
            }
        }
        // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        return ip;
    }

    public static void main(String[] args) {
        String ss = "LoginReqVO(phone=13825191663, loginType=0, pwd=RTEshwSC/RZzOsK1xkAKI6uIZS3d+QL0WEHFsArPsrBkTN+kOs5s8YeJ4rEjCijY5DQm+4XaGJrhLENXqTjZAwboDBN8IOaLY1jEuBxFhMWE4p9afjeIAWBD5tZvD1OT7Qcxoff7QS3wIffM07B67d3uWIZ9KGyOFoJyj9Bclr0=, captchaId=30cc8999-fd08-4bf6-afda-5e3581afdcea, captcha=L7UP, smsCode=null)";
        System.out.println(ss.split("phone=")[1].substring(0,11));
    }
}
