package com.zhixianghui.web.employer.config;

import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentOperatorVO;
import com.zhixianghui.web.employer.annotation.CurrentSession;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.component.JwtHelper;
import com.zhixianghui.web.employer.component.SessionManager;
import com.zhixianghui.web.employer.constant.PermissionConstant;
import com.zhixianghui.web.employer.dto.JwtInfo;
import com.zhixianghui.web.employer.dto.Session;
import com.zhixianghui.web.employer.exception.NoSelectMchException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

/**
 *
 *
 * <AUTHOR> Guangsheng
 */
@Component
public class MethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Autowired
    private JwtHelper jwtHelper;

    @Autowired
    private SessionManager sessionManager;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return (parameter.getParameterType().isAssignableFrom(EmployerOperatorVO.class) && parameter.hasParameterAnnotation(CurrentOperatorVO.class))
                || (parameter.getParameterType().isAssignableFrom(EmployerStaffVO.class) && parameter.hasParameterAnnotation(CurrentStaffVo.class))
                || (parameter.getParameterType().isAssignableFrom(String.class) && parameter.hasParameterAnnotation(CurrentMchNo.class))
                || (parameter.getParameterType().isAssignableFrom(Session.class) && parameter.hasParameterAnnotation(CurrentSession.class));
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        HttpServletRequest request = (HttpServletRequest) (webRequest.getNativeRequest());
        JwtInfo jwtInfo = (JwtInfo) request.getAttribute("jwtInfo");
        Session session = (Session) request.getAttribute("session");
        if (jwtInfo == null || session == null) {
            return null;
        }

        if (parameter.getParameterType().isAssignableFrom(Session.class) && parameter.hasParameterAnnotation(CurrentSession.class)) {
            return session;
        } else if (parameter.getParameterType().isAssignableFrom(EmployerOperatorVO.class) && parameter.hasParameterAnnotation(CurrentOperatorVO.class)) {
            return session.getAttribute(PermissionConstant.OPERATOR_SESSION_KEY, EmployerOperatorVO.class);
        } else if (parameter.getParameterType().isAssignableFrom(EmployerStaffVO.class) && parameter.hasParameterAnnotation(CurrentStaffVo.class)) {
            EmployerStaffVO staffVO = session.getAttribute(PermissionConstant.STAFF_SESSION_KEY, EmployerStaffVO.class);
            if (staffVO == null) {
                throw new NoSelectMchException();
            }
            return staffVO;
        } else if (parameter.getParameterType().isAssignableFrom(String.class) && parameter.hasParameterAnnotation(CurrentMchNo.class)) {
            String mchNo = session.getAttribute(PermissionConstant.MCH_NO_SESSION_KEY, String.class);
            if (mchNo == null) {
                throw new NoSelectMchException();
            }
            return mchNo;
        }
        return null;
    }
}
