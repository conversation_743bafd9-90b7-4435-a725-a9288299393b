package com.zhixianghui.web.employer.controller.common;

import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.trade.service.WeChatUserFacade;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.web.employer.component.JwtHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/2/17 16:42
 */
@RestController
@RequestMapping("weChat")
public class WeChatFileController {
    public static final String REQUEST_TOKEN_HEADER = "authorization";
    private static final Logger LOGGER = LoggerFactory.getLogger(WeChatFileController.class);

    @Autowired
    public FastdfsClient fastdfsClient;
    @Autowired
    private JwtHelper jwtHelper;
    @Reference
    private WeChatUserFacade weChatUserFacade;

    private String parseToken(HttpServletRequest request) {
        String token = request.getHeader(REQUEST_TOKEN_HEADER);
        if (StringUtils.isBlank(token)) {
            return null;
        }
        return jwtHelper.parseToken(token);
    }

    @PostMapping("upload")
    public RestResult<String> upload(@RequestParam("file") MultipartFile multiPartfile, HttpServletRequest request) throws IOException {
        try {
            String userNo = parseToken(request);
            if (userNo == null) {
                return RestResult.error("当前token不合法");
            }
            if (weChatUserFacade.exist(userNo) == null) {
                return RestResult.error("当前用户不存在");
            }
        } catch (Exception e) {
            LOGGER.error("当前token不合法:" + e);
            return RestResult.error("当前token不合法");
        }
        if(multiPartfile == null){
            return RestResult.error("文件不能为空");
        }

        // 上传
        String fileUrl = fastdfsClient.uploadFile(multiPartfile.getBytes(), multiPartfile.getOriginalFilename());
        LOGGER.info("文件上传路径:{}",fileUrl);
        return RestResult.success(fileUrl);
    }
}
