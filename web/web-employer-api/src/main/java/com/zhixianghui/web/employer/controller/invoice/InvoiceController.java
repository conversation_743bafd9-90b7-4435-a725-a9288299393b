package com.zhixianghui.web.employer.controller.invoice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.redis.RedisKeysConstant;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.AccountHandleStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.ApplyTypeEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoicePreStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.invoice.InvoiceTypeEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.entity.config.InvoiceBookingConfig;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.InvoiceBookingConfigFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.entity.MerchantInvoiceInfo;
import com.zhixianghui.facade.merchant.service.MerchantEmployerPositionFacade;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantExpressInfoFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantInvoiceInfoFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.dto.InvoiceRecordDetailDto;
import com.zhixianghui.facade.trade.dto.OfflineInvoiceApplyDto;
import com.zhixianghui.facade.trade.entity.InvoiceRecord;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.enums.InvoiceCategoryEnum;
import com.zhixianghui.facade.trade.enums.InvoiceSourceEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.InvoicePreFacade;
import com.zhixianghui.facade.trade.service.InvoiceRecordDetailFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.vo.InvoiceDetailGroupByIdCardVo;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.biz.excel.ExportBiz;
import com.zhixianghui.web.employer.biz.invoice.InvoiceBiz;
import com.zhixianghui.web.employer.biz.order.OrderBiz;
import com.zhixianghui.web.employer.dto.InvoiceBookingConfigApplyDto;
import com.zhixianghui.web.employer.utils.BeanToMapUtil;
import com.zhixianghui.web.employer.vo.invoice.ApplyInvoiceVo;
import com.zhixianghui.web.employer.vo.invoice.InvoiceRecordQueryVo;
import com.zhixianghui.web.employer.vo.invoice.PreCheckApplyInvoiceResp;
import com.zhixianghui.web.employer.vo.invoice.PreCheckApplyInvoiceVo;
import com.zhixianghui.web.employer.vo.invoice.QueryWaitIssueOrderVo;
import com.zhixianghui.web.employer.vo.order.res.MainstayResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 发票业务
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("invoice")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class InvoiceController {
    @Reference
    private InvoiceFacade invoiceFacade;
    @Reference
    private MerchantInvoiceInfoFacade merchantInvoiceInfoFacade;
    @Reference
    private MerchantExpressInfoFacade merchantExpressInfoFacade;
    @Reference
    private SequenceFacade sequenceFacade;
    @Reference
    private OrderFacade orderFacade;
    @Reference
    private MerchantEmployerPositionFacade positionFacade;
    @Reference
    private OrderItemFacade orderItemFacade;
    @Reference
    private MerchantEmployerQuoteFacade merchantEmployerQuoteFacade;
    @Reference
    private InvoiceRecordDetailFacade invoiceRecordDetailFacade;
    @Autowired
    private ExportBiz exportBiz;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private InvoiceBookingConfigFacade bookingConfigFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Autowired
    private OrderBiz orderBiz;
    @Autowired
    private InvoiceBiz invoiceBiz;
    @Autowired
    private RedisClient redisClient;
    @Reference
    private MerchantEmployerPositionFacade employerPositionFacade;
    @Reference
    private OfflineOrderItemFacade offlineOrderItemFacade;
    @Reference
    private OfflineOrderFacade offlineOrderFacade;
    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;
    @Reference
    private InvoicePreFacade invoicePreFacade;

    /**
     * 发票申请记录分页查询
     *
     * @param queryVo
     * @param mchNo
     * @return
     */
    @RequestMapping("listPage")
    @Permission("invoice:view")
    public RestResult<PageResult<List<InvoiceRecord>>> listPage(@Valid @RequestBody InvoiceRecordQueryVo queryVo,
                                                                @CurrentMchNo String mchNo) {

        Map<String, Object> paramMap = BeanUtil.toMap(queryVo);
        paramMap.put("employerMchNo", mchNo);
        PageResult<List<InvoiceRecord>> pageResult = invoiceFacade.listPage(paramMap, queryVo.getPageParam());
        return RestResult.success(pageResult);
    }

    /**
     * 根据流水号获取发票申请记录详细信息
     *
     * @param trxNo
     * @param mchNo
     * @return
     */
    @GetMapping("getByTrxNo")
    @Permission("invoice:view")
    public RestResult<InvoiceRecord> getByTrxNo(@RequestParam String trxNo, @CurrentMchNo String mchNo) {
        InvoiceRecord record = invoiceFacade.getByTrxNo(trxNo);
        if (!record.getEmployerMchNo().equals(mchNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("发票流水不存在");
        }
        return RestResult.success(record);
    }

    /**
     * 获取交易时间段起始时间
     *
     * @param mainstayMchNo
     * @param mchNo
     * @return
     */
    @GetMapping("getApplyTradeCompleteDayBegin")
    public RestResult<String> getApplyTradeCompleteDayBegin(@RequestParam String mainstayMchNo,
                                                            @RequestParam(required = false) Integer category,
                                                            @RequestParam(required = false) String jobId,
                                                            @RequestParam(required = false) Integer source,
                                                            @RequestParam(required = true) String workCategoryCode,
                                                            @RequestParam(required = true) String productNo,
                                                            @RequestParam(required = true) Integer amountType,
                                                            @CurrentMchNo String mchNo) {
        LimitUtil.notEmpty(mainstayMchNo, "代征主体商户编号不能为空");
        String completeTimeBegin = invoiceFacade.getApplyTradeCompleteDayBegin(mchNo, mainstayMchNo, category, jobId,
                source, workCategoryCode, productNo, amountType);
        return RestResult.success(completeTimeBegin);
    }

    /**
     * 查询待开票批次订单
     *
     * @param queryVo
     * @param mchNo
     * @return
     */
    @RequestMapping("listWaitIssueOrderPage")
    public RestResult<PageResult<List<Order>>> listWaitIssueOrderPage(@Valid @RequestBody QueryWaitIssueOrderVo queryVo,
                                                                      @CurrentMchNo String mchNo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(PageResult.newInstance(PageParam.newInstance(0, 0), new ArrayList<>()));
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", mchNo);
        paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
        paramMap.put("productNo", queryVo.getProductNo());
        paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
        paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
        paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
        paramMap.put("jobId", queryVo.getJobId());
        paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
        paramMap.put("ignoreZeroAmt", "true");
        paramMap.put("amountType", queryVo.getAmountType());
        queryVo.getPageParam().setIsNeedTotalRecord(false);

        PageResult<List<Order>> pageResult;
        if (queryVo.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
            pageResult = orderFacade.listPage(paramMap, queryVo.getPageParam());
        } else {
            final Page<OfflineOrder> pageOrder = offlineOrderFacade.pageOrder(new Page<>(queryVo.getPageCurrent(), queryVo.getPageSize()), paramMap);

            final List<OfflineOrder> records = pageOrder.getRecords();
            List<Order> orderList = new ArrayList<>();
            if (records != null) {
                for (OfflineOrder record : records) {
                    Order order = new Order();
                    BeanUtil.copyProperties(record, order);
                    orderList.add(order);
                }
            }

            pageResult = PageResult.newInstance(orderList, Long.valueOf(pageOrder.getCurrent()).intValue(), Long.valueOf(pageOrder.getSize()).intValue());
        }

        return RestResult.success(pageResult);
    }

    /**
     * 查询待开票详情订单
     *
     * @param queryVo
     * @param mchNo
     * @return
     */
    @RequestMapping("listWaitIssueOrderItemPage")
    public RestResult<PageResult<List<OrderItem>>> listWaitIssueOrderItemPage(@Valid @RequestBody QueryWaitIssueOrderVo queryVo,
                                                                              @CurrentMchNo String mchNo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(PageResult.newInstance(PageParam.newInstance(0, 0), new ArrayList<>()));
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", mchNo);
        paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
        paramMap.put("productNo", ProductNoEnum.CKH.getValue());
        paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
        paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
        paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
        paramMap.put("jobId", queryVo.getJobId());
        paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
        queryVo.getPageParam().setIsNeedTotalRecord(false);

        PageResult<List<OrderItem>> pageResult;
        if (queryVo.getSource().intValue() == InvoiceSourceEnum.ON_LINE.getCode().intValue()) {
            pageResult = orderItemFacade.listPage(paramMap, queryVo.getPageParam());
        } else {
            final PageResult<List<OfflineOrderItem>> pageOrder = offlineOrderItemFacade.listPage(paramMap, queryVo.getPageParam());

            final List<OfflineOrderItem> records = pageOrder.getData();
            List<OrderItem> orderList = new ArrayList<>();
            if (records != null) {
                for (OfflineOrderItem record : records) {
                    OrderItem order = new OrderItem();
                    BeanUtil.copyProperties(record, order);
                    orderList.add(order);
                }
            }

            pageResult = PageResult.newInstance(orderList, pageOrder.getPageCurrent(), pageOrder.getPageSize());
        }

        return RestResult.success(pageResult);
    }

    /**
     * 查询待开票金额
     *
     * @param queryVo
     * @param mchNo
     * @return
     */
    @RequestMapping("getWaitIssueAmount")
    public RestResult<BigDecimal> getWaitIssueAmount(@Valid @RequestBody QueryWaitIssueOrderVo queryVo,
                                                     @CurrentMchNo String mchNo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(BigDecimal.ZERO);
        }

        BigDecimal total = BigDecimal.ZERO;
        Map<String, Object> paramMap = new HashMap<>();
        if (queryVo.getApplyType() == ApplyTypeEnum.GRANT_AMOUNT.getValue()) {
            paramMap.put("employerNo", mchNo);
            paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
            paramMap.put("productNo", queryVo.getProductNo());
            paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
            paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
            paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
            paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
            paramMap.put("ignoreZeroAmt", "true");
            if (ProductNoEnum.ZXH.getValue().equals(queryVo.getProductNo())) {
                total = orderFacade.sumWaitInvoiceAmount(paramMap);
            } else if (ProductNoEnum.CEP.getValue().equals(queryVo.getProductNo())) {
                total = orderFacade.sumWaitCepInvoiceAmount(queryVo.getAmountType(), paramMap);
            }
        }
        return RestResult.success(total);
    }


    /**
     * 查询创客汇待开票金额
     *
     * @param queryVo
     * @param mchNo
     * @return
     */
    @RequestMapping("sumWaitCkhInvoiceAmount")
    public RestResult<BigDecimal> sumWaitCkhInvoiceAmount(@Valid @RequestBody QueryWaitIssueOrderVo queryVo,
                                                          @CurrentMchNo String mchNo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(BigDecimal.ZERO);
        }
        BigDecimal total = BigDecimal.ZERO;
        Map<String, Object> paramMap = new HashMap<>();
        if (queryVo.getApplyType() == ApplyTypeEnum.GRANT_AMOUNT.getValue()) {
            paramMap.put("employerNo", mchNo);
            paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
            paramMap.put("batchStatus", OrderStatusEnum.FINISH_GRANT.getValue());
            paramMap.put("productNo", ProductNoEnum.CKH.getValue());
            paramMap.put("jobId", queryVo.getJobId());
            paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
            paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
            paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
            paramMap.put("ignoreZeroAmt", "true");
            if (queryVo.getSource() == InvoiceSourceEnum.OFF_LINE.getCode()) {
                total = offlineOrderFacade.sumWaitInvoiceAmount(paramMap);
            } else {
                total = orderFacade.sumWaitCkhInvoiceAmount(paramMap);
            }
        }
        return RestResult.success(total);
    }

    /**
     * 查询待开票金额
     *
     * @param queryVo
     * @param mchNo
     * @return
     */
    @RequestMapping("getOrderItemWaitIssueAmount")
    public RestResult<BigDecimal> getOrderItemWaitIssueAmount(@Valid @RequestBody QueryWaitIssueOrderVo queryVo,
                                                              @CurrentMchNo String mchNo) {
        if (DateUtil.parse(queryVo.getTradeCompleteDayBegin()).after(DateUtil.parse(queryVo.getTradeCompleteDayEnd()))) {
            return RestResult.success(BigDecimal.ZERO);
        }
        BigDecimal total = BigDecimal.ZERO;
        Map<String, Object> paramMap = new HashMap<>();
        if (queryVo.getApplyType() == ApplyTypeEnum.GRANT_AMOUNT.getValue()) {
            paramMap.put("employerNo", mchNo);
            paramMap.put("mainstayNo", queryVo.getMainstayMchNo());
            paramMap.put("productNo", ProductNoEnum.CKH.getValue());
            paramMap.put("jobId", queryVo.getJobId());
            paramMap.put("orderItemStatus", OrderItemStatusEnum.GRANT_SUCCESS.getValue());
            paramMap.put("completeBeginDate", DateUtil.parseTime(queryVo.getTradeCompleteDayBegin() + " 00:00:00"));
            paramMap.put("completeEndDate", DateUtil.parseTime(queryVo.getTradeCompleteDayEnd() + " 23:59:59"));
            paramMap.put("workCategoryCode", queryVo.getWorkCategoryCode());
            if (queryVo.getSource() == InvoiceSourceEnum.OFF_LINE.getCode()) {
                total = offlineOrderItemFacade.sumOrderItemWaitInvoiceAmount(paramMap);
            } else {
                total = orderItemFacade.sumOrderItemWaitInvoiceAmount(paramMap);
            }
        }
        return RestResult.success(total);
    }

    /**
     * 申请发票（预开票校验）
     *
     * @param vo
     * @param staffVo
     * @return
     */
    @RequestMapping("preCheckApplyInvoice")
    @Permission("invoice:apply")
    public RestResult<PreCheckApplyInvoiceResp> applyInvoice(@Valid @RequestBody PreCheckApplyInvoiceVo vo, @CurrentStaffVo EmployerStaffVO staffVo) {

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("employerMchNo", staffVo.getMchNo());
        paramMap.put("mainstayMchNo", vo.getMainstayMchNo());
        paramMap.put("productNo", vo.getProductNo());
        paramMap.put("workCategoryCode", vo.getWorkCategoryCode());
        paramMap.put("invoiceStatus", InvoicePreStatusEnum.UN_FINNISH.getValue());
        Map<String, Object> result = invoicePreFacade.countInvoiceAmount(paramMap);
        return RestResult.success(PreCheckApplyInvoiceResp.builder().invoicePreAmount(new BigDecimal(result.get("invoiceAmount").toString())).build());
    }

    /**
     * 申请发票
     *
     * @param applyInvoiceVo
     * @param staffVo
     * @return
     */
    @RequestMapping("applyInvoice")
    @Permission("invoice:apply")
    public RestResult<String> applyInvoice(@Valid @RequestBody ApplyInvoiceVo applyInvoiceVo, @CurrentStaffVo EmployerStaffVO staffVo) {
        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoFacade.getByMchNo(staffVo.getMchNo());
        LimitUtil.notEmpty(merchantInvoiceInfo, "开票信息不存在");
        MerchantExpressInfo merchantExpressInfo = merchantExpressInfoFacade.getByMchNo(staffVo.getMchNo());
        //LimitUtil.notEmpty(merchantExpressInfo, "邮寄信息不存在");

        String CKH_INVOICE_MARJOR_MIN = "2000";
        final String marjorMin = dataDictionaryFacade.getSystemConfig("CKH_INVOICE_MARJOR_MIN");
        if (marjorMin != null) {
            CKH_INVOICE_MARJOR_MIN = marjorMin;
        }
        if (StringUtils.equals(applyInvoiceVo.getProductNo(), ProductNoEnum.CKH.getValue())) {
            if (hasNotpayFee(applyInvoiceVo.getTradeCompleteDayBegin(), applyInvoiceVo.getTradeCompleteDayEnd(), staffVo.getMchNo(), applyInvoiceVo.getMainstayMchNo(), applyInvoiceVo.getSource())) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("开票时间段有未支付的服务费账单，请先支付完成后再重试");
            }
        }

        if (StringUtils.equals(applyInvoiceVo.getProductNo(), ProductNoEnum.CKH.getValue())
                && applyInvoiceVo.getInvoiceType().intValue() == InvoiceTypeEnum.MAJOR.getValue()
                && applyInvoiceVo.getInvoiceAmount().compareTo(new BigDecimal(CKH_INVOICE_MARJOR_MIN)) < 0) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("创客汇服务费开票（专票）金额不能小于" + CKH_INVOICE_MARJOR_MIN + "元");
        }

        String trxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getWidth());
        InvoiceRecord record = fillInvoiceRecord(applyInvoiceVo, staffVo, merchantInvoiceInfo, merchantExpressInfo, trxNo);

        if (StringUtils.equals(applyInvoiceVo.getProductNo(), ProductNoEnum.CKH.getValue())) {
            //如果是创客汇产品，则填充categry字段
            record.setCategory(InvoiceCategoryEnum.SERVICE_FEE_INVOICE.getCode());
        }

        invoiceFacade.applyInvoice(record, applyInvoiceVo.getJobId());
        return RestResult.success("开票申请成功");
    }

    /**
     * 申请发票
     *
     * @param applyInvoiceVo
     * @param staffVo
     * @return
     */
    @RequestMapping("applyItemInvoice")
    @Permission("invoice:apply")
    public RestResult<String> applyItemInvoice(@Valid @RequestBody ApplyInvoiceVo applyInvoiceVo, @CurrentStaffVo EmployerStaffVO staffVo) {
        MerchantInvoiceInfo merchantInvoiceInfo = merchantInvoiceInfoFacade.getByMchNo(staffVo.getMchNo());
        LimitUtil.notEmpty(merchantInvoiceInfo, "开票信息不存在");
        MerchantExpressInfo merchantExpressInfo = merchantExpressInfoFacade.getByMchNo(staffVo.getMchNo());
        //LimitUtil.notEmpty(merchantExpressInfo, "邮寄信息不存在");

        if (hasNotpayFee(applyInvoiceVo.getTradeCompleteDayBegin(), applyInvoiceVo.getTradeCompleteDayEnd(), staffVo.getMchNo(), applyInvoiceVo.getMainstayMchNo(), applyInvoiceVo.getSource())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("开票时间段有未支付的服务费账单，请先支付完成后再重试");
        }

        String trxNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getPrefix(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getKey(),
                SequenceBizKeyEnum.INVOICE_TRX_NO_SEQ.getWidth());
        InvoiceRecord record = fillInvoiceRecord(applyInvoiceVo, staffVo, merchantInvoiceInfo, merchantExpressInfo, trxNo);
        record.setCategory(InvoiceCategoryEnum.NATURE_PERSON_INVOICE.getCode());
        invoiceFacade.applyInvoiceOrderItem(record, applyInvoiceVo.getJobId());
        return RestResult.success("开票申请成功");
    }

    private InvoiceRecord fillInvoiceRecord(ApplyInvoiceVo applyInvoiceVo,
                                            EmployerStaffVO staffVo,
                                            MerchantInvoiceInfo merchantInvoiceInfo,
                                            MerchantExpressInfo merchantExpressInfo,
                                            String trxNo) {
        InvoiceRecord record = new InvoiceRecord();
        record.setUpdateTime(new Date());
        record.setProductNo(applyInvoiceVo.getProductNo());
        record.setProductName(applyInvoiceVo.getProductName());
        record.setEmployerMchNo(staffVo.getMchNo());
        record.setEmployerMchName(staffVo.getMchName());
        record.setMainstayMchNo(applyInvoiceVo.getMainstayMchNo());
        record.setMainstayMchName(applyInvoiceVo.getMainstayMchName());
        record.setTrxNo(trxNo);
        record.setInvoiceType(applyInvoiceVo.getInvoiceType());
        record.setApplyType(applyInvoiceVo.getApplyType());
        record.setAmountType(applyInvoiceVo.getAmountType());
        record.setInvoiceAmount(applyInvoiceVo.getInvoiceAmount());
        record.setTradeCompleteDayBegin(applyInvoiceVo.getTradeCompleteDayBegin());
        record.setTradeCompleteDayEnd(applyInvoiceVo.getTradeCompleteDayEnd());
        record.setInvoiceCategoryCode(applyInvoiceVo.getInvoiceCategoryCode());
        record.setInvoiceCategoryName(applyInvoiceVo.getInvoiceCategoryName());
        record.setInvoiceStatus(InvoiceStatusEnum.WAIT_ISSUE.getValue());
        record.setAccountHandleStatus(AccountHandleStatusEnum.UN_HANDLE.getValue());
        if (Objects.isNull(merchantExpressInfo)) {
            record.setExpressConsignee("");
            record.setExpressTelephone("");
            record.setProvince("");
            record.setCity("");
            record.setCounty("");
            record.setAddress("");
        } else {
            record.setExpressConsignee(merchantExpressInfo.getConsignee());
            record.setExpressTelephone(merchantExpressInfo.getTelephone());
            record.setProvince(merchantExpressInfo.getProvince());
            record.setCity(merchantExpressInfo.getCity());
            record.setCounty(merchantExpressInfo.getCounty());
            record.setAddress(merchantExpressInfo.getAddress());
        }
        record.setTaxPayerType(merchantInvoiceInfo.getTaxPayerType());
        record.setTaxNo(merchantInvoiceInfo.getTaxNo());
        record.setRegisterAddrInfo(merchantInvoiceInfo.getRegisterAddrInfo());
        record.setAccountNo(merchantInvoiceInfo.getAccountNo());
        record.setBankName(merchantInvoiceInfo.getBankName());
        record.setRemark(applyInvoiceVo.getRemark());
        record.setVersion(0);
        record.setCreateTime(new Date());
        record.setSource(applyInvoiceVo.getSource() == null ? InvoiceSourceEnum.ON_LINE.getCode() : applyInvoiceVo.getSource());
        record.setWorkCategoryCode(applyInvoiceVo.getWorkCategoryCode());
        record.setWorkCategoryName(applyInvoiceVo.getWorkCategoryName());
        return record;
    }

    /**
     * 根据产品和商户查询供应商
     *
     * @param status    状态
     * @param productNo 产品编号
     * @param mchNo     商户编号
     * @return
     */
    @GetMapping("listOpenMainstayByEmployerNoAndProduct")
    public RestResult<List<MainstayResVo>> listOpenMainstayByEmployerNoAndProduct(@RequestParam(required = true) Integer status, @RequestParam(required = false) String productNo, @CurrentMchNo String mchNo) {
        return RestResult.success(orderBiz.listOpenMainstayByEmployerNoAndProductNoNew(productNo, mchNo, CommonStatusEnum.ACTIVE.getValue()));
    }

    @GetMapping("listAllMainstayByEmployerNoAndProduct")
    public RestResult listAllMainstayByEmployerNoAndProduct(@RequestParam(required = false) Integer status, @RequestParam String productNo, @CurrentMchNo String mchNo) {
        return RestResult.success(orderBiz.listOpenMainstayByEmployerNoAndProductNoNew(productNo, mchNo, status));
    }

    /**
     * 查询普通发票批次详情
     *
     * @param page
     * @param invoiceRecordDetailDto
     * @param mchNo
     * @return
     */
    @PostMapping("invoiceRecordDetailPage")
    public RestResult<IPage<InvoiceDetailGroupByIdCardVo>> invoiceRecordDetailPage(@RequestBody Page<InvoiceDetailGroupByIdCardVo> page, @RequestBody InvoiceRecordDetailDto invoiceRecordDetailDto, @CurrentMchNo String mchNo) {
        if (mchNo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未获取到商户号，请重新登录");
        }
        if (StringUtils.isBlank(invoiceRecordDetailDto.getInvoiceTrxNo())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("开票流水号不能为空");
        }
        invoiceRecordDetailDto.setMchNo(mchNo);

        final IPage<InvoiceDetailGroupByIdCardVo> detailPage = invoiceFacade.listInvoiceDetailGroupByIdCard(page, invoiceRecordDetailDto.getInvoiceTrxNo());

        return RestResult.success(detailPage);
    }

    /**
     * 预约开票配置
     *
     * @param dto
     * @param mchNo
     * @param staffVO
     * @return
     */
    @PostMapping("applyBookConfig")
    public RestResult<InvoiceBookingConfig> applyBookConfig(@Valid @RequestBody InvoiceBookingConfigApplyDto dto, @CurrentMchNo String mchNo, @CurrentStaffVo EmployerStaffVO staffVO) {
        final Merchant merchant = merchantFacade.getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        log.info("[添加/更新预约开票配置-开始]-商户：[{}],操作员:[{}]", mchNo, staffVO.getOperatorName());

        InvoiceBookingConfig bookingConfig = new InvoiceBookingConfig();
        BeanUtil.copyProperties(dto, bookingConfig);
        bookingConfig.setEmployerNo(mchNo);
        bookingConfig.setEmployerName(merchant.getMchName());
        bookingConfig.setUpdateTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
        bookingConfig.setUpdateBy(staffVO.getOperatorName());
        final InvoiceBookingConfig config = bookingConfigFacade.applyBookConfig(bookingConfig);
        log.info("[添加/更新预约开票配置-完成]-商户：[{}],操作员:[{}]", mchNo, staffVO.getOperatorName());
        return RestResult.success(config);
    }

    /**
     * 查询预约开票配置
     *
     * @param mchNo
     * @return
     */
    @GetMapping("listBookConfig")
    public RestResult<List<InvoiceBookingConfig>> listBookConfig(@CurrentMchNo String mchNo) {

        final List<InvoiceBookingConfig> invoiceBookingConfigs = bookingConfigFacade.listBookConfig(mchNo);
        return RestResult.success(invoiceBookingConfigs);
    }

    /**
     * 根据ID删除配置
     *
     * @param id
     * @return
     */
    @PostMapping("deleteBookConfig")
    public RestResult<String> deleteBookConfig(Long id) {
        if (id == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("必须指定删除数据的ID");
        }
        bookingConfigFacade.deleteBookConfig(id);
        return RestResult.success("操作成功");
    }

    @PostMapping("exportInvoceInfo")
    public RestResult<String> exportInvoceInfo(@Valid @RequestBody InvoiceRecordQueryVo queryVo, @CurrentMchNo String mchNo, @CurrentStaffVo EmployerStaffVO staffVO) {
//        String createBeginDate = queryVo.getCreateTimeBegin();
//        String createEndDate = queryVo.getCreateTimeEnd();
//
//        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
//            if (StringUtils.isBlank(queryVo.getTrxNo())) {
//                queryVo.setCreateTimeBegin(TimeRangeUtil.latestThreeMonthStartTimeStr());
//                queryVo.setCreateTimeEnd(TimeRangeUtil.endTimeStr());
//            }
//        }

        Map<String, Object> paramMap = BeanToMapUtil.beanToMap(queryVo);
        paramMap.put("employerMchNo", mchNo);

        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setMchNo(mchNo);
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());

        record.setFileName(ReportTypeEnum.MERCHANT_INVOICE_RECORD_INFO_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.MERCHANT_INVOICE_RECORD_INFO_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_INVOICE_RECORD_INFO_EXPORT.getDataName());
        if (dataDictionary == null) {
            log.error("细导出字典未配置");
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出相关信息未配置,请联系客服");
        }

        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });
        exportRecordFacade.insert(record);

        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("offlineInvoiceApplyProcessing")
    public RestResult<OfflineInvoiceApplyDto> offlineInvoiceApplyProcessing(@CurrentMchNo String mchNo) {
        return RestResult.success(redisClient.get(RedisKeysConstant.INVOICE_OFFLINE_APPLY_CACHE_PREFIX + mchNo, OfflineInvoiceApplyDto.class));
    }

    @PostMapping("applyOffline")
    public RestResult<String> applyOffline(@Valid @RequestBody OfflineInvoiceApplyDto reqDto, @CurrentStaffVo EmployerStaffVO staffVo) throws IOException {
        return RestResult.success(invoiceBiz.applyOffline(reqDto, staffVo));
    }

    @PostMapping("calculateServiceFee")
    public RestResult<BigDecimal> calculateServiceFee(@RequestParam("mainstayNo") String mainstayNo, @RequestParam("invoiceAmount") BigDecimal invoiceAmount, @CurrentMchNo String mchNo) {
        return RestResult.success(invoiceBiz.calculateServiceFee(mchNo, mainstayNo, invoiceAmount));
    }

    private boolean hasNotpayFee(String startTime, String endTime, String employerNo, String mainstayNo, Integer feeSource) {
        return feeOrderBatchFacade.hasNotpayFee(startTime, endTime, employerNo, mainstayNo, feeSource);
    }

}
