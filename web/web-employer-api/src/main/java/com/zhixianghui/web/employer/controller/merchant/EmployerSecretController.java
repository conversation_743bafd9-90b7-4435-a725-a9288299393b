package com.zhixianghui.web.employer.controller.merchant;

import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.enums.fee.CommonStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.RSAUtil;
import com.zhixianghui.common.util.utils.RandomUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantNotifySet;
import com.zhixianghui.facade.merchant.entity.MerchantSecret;
import com.zhixianghui.facade.merchant.enums.MerchantNotifySetTypeEnum;
import com.zhixianghui.facade.merchant.enums.NotifyUrlTypeEnum;
import com.zhixianghui.facade.merchant.service.MerchantNotifySetFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.service.MerchantSecretFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.component.CaptchaHelper;
import com.zhixianghui.web.employer.vo.merchant.MerchantKeyPairVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @description:
 * @author: xingguang li
 * @created: 2020/12/18 14:49
 */
@RestController
@RequestMapping("employerSecret")
@Slf4j
public class EmployerSecretController {

    @Reference
    private MerchantSecretFacade merchantSecretFacade;
    @Autowired
    private CaptchaHelper captchaHelper;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantNotifySetFacade merchantNotifySetFacade;

    /**
     * 发送短信验证码
     * @return
     */
    @GetMapping("sendSmsCode")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "查询公钥前发送短信验证码")
    public RestResult<String> sendSmsCode(@RequestParam String phone,
                                          @RequestParam String mchNo) {
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        //校验
        verifySecretPre(phone, merchant);
        captchaHelper.sendSmsCode(phone);
        return RestResult.success("短信验证码发送成功");
    }

    /**
     * 获取公私钥信息
     * @param phone
     * @param code
     * @param mchNo
     * @return
     */
    @GetMapping("getSecret")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "获取商户秘钥")
    public RestResult<Map<String,Object>> getSecret(@RequestParam String phone,
                                                    @RequestParam String code,
                                                    @RequestParam String mchNo) {
        //校验短信验证码
        captchaHelper.verifySmsCode(phone, code);
        MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(mchNo);
        //第一次如果没有平台公私钥则生成一份
        if (merchantSecret == null) {
            merchantSecret = new MerchantSecret();
            buildSecret(merchantSecret, mchNo);
        }

        Map<String,Object> result = new HashMap<>();
        result.put("signType", "RSA");
        result.put("platformPublicKey", merchantSecret.getPlatformPublicKey());
        result.put("merchantPublicKey", merchantSecret.getMerchantPublicKey());
        String randomStr = "我是测试字符串" + RandomUtil.getDigitStr(4);
        result.put("randomStr", randomStr);
        result.put("randomStrSign", RSAUtil.sign(randomStr, merchantSecret.getPlatformPrivateKey(), true));
        return RestResult.success(result);
    }

    @PostMapping("/genMerchantKeypair")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "生成商户秘钥对")
    public RestResult<MerchantKeyPairVo> genMerchantKeypair(@CurrentMchNo String mchNo) {
        MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(mchNo);
        if (merchantSecret == null) {
            merchantSecret = new MerchantSecret();
            buildSecret(merchantSecret, mchNo);
        }

        Map<String,String> keyPair = RSAUtil.genKeyPair();
        String pubkey = keyPair.get(RSAUtil.PUBLIC_KEY);
        String privKey = keyPair.get(RSAUtil.PRIVATE_KEY);
        merchantSecret.setMerchantPublicKey(pubkey);
        merchantSecretFacade.update(merchantSecret);

        MerchantKeyPairVo merchantKeyPairVo = new MerchantKeyPairVo().setMerchantPrivateKey(privKey).setEmployerNo(mchNo).setMerchantPublicKey(pubkey);
        return RestResult.success(merchantKeyPairVo);
    }

    /**
     * 获取公私钥信息
     * @param phone
     * @param code
     * @param mchNo
     * @return
     */
    @GetMapping("getNotifyInfo")
    @Logger(type = OperateLogTypeEnum.QUERY, action = "获取商户回调地址")
    public RestResult getNotifyInfo(@RequestParam String phone,
                                                    @RequestParam String code,
                                                    @RequestParam String mchNo) {

        final Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        if (!StringUtils.equals(merchant.getContactPhone(), phone)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("手机号码不匹配");
        }

        //校验短信验证码
        captchaHelper.verifySmsCode(phone, code);
        List<MerchantNotifySet> merchantNotifySetList =  merchantNotifySetFacade.getByMchNo(mchNo);
        return RestResult.success(merchantNotifySetList);
    }

    @PostMapping("updateSecret")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "商户保存公钥")
    public RestResult<String> updateSecret(@RequestParam String publicKey,
                                                @RequestParam String mchNo) {
        if (StringUtil.isEmpty(publicKey)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("新的公钥不可为空");
        }
        //替换掉所有换行符和空格
        publicKey = publicKey.replaceAll("\r\n|\r|\n", "");
        MerchantSecret merchantSecret = merchantSecretFacade.getByMchNo(mchNo);
        //第一次如果没有平台公私钥则生成一份
        if (merchantSecret == null) {
            merchantSecret = new MerchantSecret();
            merchantSecret.setMerchantPublicKey(publicKey);
            buildSecret(merchantSecret, mchNo);
        }
        merchantSecret.setMerchantPublicKey(publicKey);
        merchantSecretFacade.update(merchantSecret);
        return RestResult.success("更新成功");
    }

    @PostMapping("updateNotifyUrl")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "商户保存公钥")
    public RestResult<String> updateNotifyUrl(@RequestBody Map<String,List<MerchantNotifySet>> param,
                                              @CurrentMchNo String mchNo) {
        List<MerchantNotifySet> merchantNotifySetList = param.get("notify");
        if (merchantNotifySetList == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("回调信息不能为空");
        }

        final Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        for (MerchantNotifySet merchantNotifySet : merchantNotifySetList) {
            if (Objects.isNull(merchantNotifySet.getNotifyStatus())) {
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("回调状态不可为空");
            }
            if (merchantNotifySet.getNotifyStatus().intValue() == CommonStatusEnum.ACTIVE.getValue()){
                if (StringUtil.isEmpty(merchantNotifySet.getNotifyUrlType())) {
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("地址类型不可为空");
                }
                if (StringUtil.isEmpty(merchantNotifySet.getNotifyUrl())) {
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("回调地址不可为空");
                }
                if (merchantNotifySet.getNotifyUrl().length() > 200) {
                    throw CommonExceptions.PARAM_INVALID.newWithErrMsg("回调地址过长");
                }
            }
        }

        merchantNotifySetFacade.save(mchNo,merchantNotifySetList);
        return RestResult.success("更新成功");
    }

    private void verifySecretPre(String phone, Merchant merchant) {
        if (!phone.equals(merchant.getContactPhone())) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("手机号码与当前用户不一致");
        }
    }

    /**
     * 第一次生成secret
     * @param merchantSecret
     * @param mchNo
     */
    private void buildSecret(MerchantSecret merchantSecret, String mchNo) {
        Map<String,String> keyPair = RSAUtil.genKeyPair();
        merchantSecret.setPlatformPublicKey(keyPair.get(RSAUtil.PUBLIC_KEY));
        merchantSecret.setPlatformPrivateKey(keyPair.get(RSAUtil.PRIVATE_KEY));
        merchantSecret.setMerchantNo(mchNo);
        merchantSecret.setModifyTime(new Date());
        merchantSecret.setSignType(SignTypeEnum.RSA.getValue());
        merchantSecret.setCreateTime(new Date());
        merchantSecretFacade.insert(merchantSecret);
    }
}
