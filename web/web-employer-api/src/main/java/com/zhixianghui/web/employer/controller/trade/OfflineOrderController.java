package com.zhixianghui.web.employer.controller.trade;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.bo.OrderItemSumBo;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.OfflineWorkerBillPathUpdateDto;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.entity.OfflineOrderItem;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentOperatorVO;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.biz.data.MerchantInfoAnalyzeBiz;
import com.zhixianghui.web.employer.biz.order.OrderHandlerFactory;
import com.zhixianghui.web.employer.biz.order.offline.OfflineOrderBiz;
import com.zhixianghui.web.employer.biz.order.offline.OfflineOrderItemBiz;
import com.zhixianghui.web.employer.vo.PageVo;
import com.zhixianghui.web.employer.vo.merchant.DataAnalyzeVo;
import com.zhixianghui.web.employer.vo.order.req.*;
import com.zhixianghui.web.employer.vo.order.res.OrderItemFailResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderItemResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderWithCountResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("offlineOrder")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OfflineOrderController {
    private final OfflineOrderBiz offlineOrderBiz;
    private final OfflineOrderItemBiz orderItemBiz;
    private final OrderHandlerFactory orderHandlerFactory;
    private final MerchantInfoAnalyzeBiz analyzeBiz;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 提交发放名单
     * @param ckhOrderAcceptReqVo 发放名单信息
     * @param operator 操作人
     * @param employerNo 用工企业
     * @return 批次编号
     * @throws Exception 异常
     */
    @Logger(type = OperateLogTypeEnum.CREATE, action = "提交发放名单")
    @Permission("order:batchOrderUpload:upload")
    @PostMapping("batchOrderUpload")
    public RestResult<Map<String, String>> batchOrderUpload(@Validated CkhOrderAcceptReqVo ckhOrderAcceptReqVo, @CurrentOperatorVO EmployerOperatorVO operator, @CurrentMchNo String employerNo) throws Exception {
        OrderAcceptReqVo acceptReqVo = new OrderAcceptReqVo();
        BeanUtil.copyProperties(ckhOrderAcceptReqVo, acceptReqVo);
        Map<String, String> result = orderHandlerFactory.getCkhOfflineOrderHandlerBiz().orderUpload(acceptReqVo, operator, employerNo);
        offlineOrderBiz.initAuthInfo(employerNo,result.get("platBatchNo"));
        return RestResult.success(result);
    }

    /**
     * 外部订单确认发放
     * @param paramMap 订单批次号map
     * @param employerNo 用工企业编号
     * @return 提示信息
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "确认发放")
    @Permission("order:confirmBatchOrderGrant:edit")
    @PostMapping("confirmBatchOrderGrant")
    public RestResult<String> confirmBatchOrderGrant(@RequestBody Map<String, String> paramMap,
                                                            @CurrentMchNo String employerNo){
        String platBatchNo = paramMap.get("platBatchNo");
        if (StringUtils.isEmpty(platBatchNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("platBatchNo 不能为空");
        }

        //检验信息和发放
        offlineOrderBiz.confirmBatchOrder(platBatchNo,employerNo);
        return RestResult.success("提交成功");
    }

    /**
     * 发放名单
     * @param orderQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 发放批次列表
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderPage")
    public RestResult<PageResult<List<OrderResVo>>> listOrderPage(@Validated @RequestBody OrderQueryVo orderQueryVo,
                                                                  @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        orderQueryTimeHandler(orderQueryVo);
        PageResult<List<OrderResVo>> result = offlineOrderBiz.listOrderPage(orderQueryVo,pageVo,employerNo);
        return RestResult.success(result);
    }

    /**
     * 订单明细
     * @param orderItemQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 订单明细列表
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("listOrderItemPage")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemPage(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                                          @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        checkDate(orderItemQueryVo);
        PageResult<List<OrderItemResVo>> result = orderItemBiz.listOrderItemPage(orderItemQueryVo,pageVo,employerNo);
        return RestResult.success(result);
    }

    /**
     * 发放页:导出订单明细
     * @param platBatchNo 平台批次号
     * @param employerNo 商户编号
     * @return 导出不同状态的发放明细
     */
    @Logger(type = OperateLogTypeEnum.QUERY, action = "发放页:导出订单明细")
    @Permission("order:listOrderPage:export")
    @PostMapping("exportOrderItemByBatchNoAndStatus")
    public RestResult<String> exportOrderItemByBatchNoAndStatus(@RequestParam String platBatchNo, Integer orderItemStatus,
                                                                @CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo) {
        OrderItemQueryVo orderItemQueryVo = new OrderItemQueryVo();
        orderItemQueryVo.setPlatBatchNo(platBatchNo);
        if(orderItemStatus != null){
            orderItemQueryVo.setOrderItemStatus(orderItemStatus);
        }
        orderItemBiz.exportOrderItem(orderItemQueryVo, vo,employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    /**
     * 订单明细-导出
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("exportOrderItem")
    public RestResult<String> exportOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo, @CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo){
        checkDate(orderItemQueryVo);
        Map<String, Object> paramMap = BeanUtil.toMap(orderItemQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setMchNo(employerNo);
        record.setOperatorLoginName(vo.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.MERCHANT_OFFLINE_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.MERCHANT_OFFLINE_EXPORT.getValue());
        record.setParamJson(JsonUtil.toString(paramMap));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.MERCHANT_OFFLINE_EXPORT.getDataName());
        if(dataDictionary == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List <DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    private void orderQueryTimeHandler(OrderQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchBatchNo())) {
                orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestOneYearStartTime());
                orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
            }else {
                orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
            }
        }
    }

    private void checkDate(@RequestBody @Validated OrderItemQueryVo orderItemQueryVo) {


        orderItemQueryTimeHandler(orderItemQueryVo);
        Date completeBeginDate = orderItemQueryVo.getCreateBeginDate();
        Date completeEndDate = orderItemQueryVo.getCreateEndDate();
        if ((completeBeginDate != null && completeEndDate == null) ||
                (completeBeginDate == null && completeEndDate != null)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间必须成对选择");
        }

        if (completeBeginDate != null && DateUtil.compare(completeBeginDate, completeEndDate, Calendar.SECOND) > 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("完成时间的截止时间不能大于起始时间");
        }
    }

    private void orderItemQueryTimeHandler(OrderItemQueryVo orderQueryVo){
        Date createBeginDate = orderQueryVo.getCreateBeginDate();
        Date createEndDate = orderQueryVo.getCreateEndDate();

        log.info("原始参数：{}", JSON.toJSONString(orderQueryVo));

        if (Objects.isNull(createBeginDate) || Objects.isNull(createEndDate)) {
            if (StringUtils.isAllBlank(orderQueryVo.getMchBatchNo(), orderQueryVo.getMchOrderNo(),orderQueryVo.getPlatBatchNo())) {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(TimeRangeUtil.latestOneYearStartTime());
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }else {
                if (orderQueryVo.getCompleteBeginDate() != null && orderQueryVo.getCompleteEndDate() != null) {
                    Date beginDate = DateUtil.addMonth(orderQueryVo.getCompleteBeginDate(), -1);
                    Date endDate = DateUtil.addMonth(orderQueryVo.getCompleteEndDate(), 1);
                    orderQueryVo.setCreateBeginDate(beginDate);
                    orderQueryVo.setCreateEndDate(endDate);
                }else {
                    orderQueryVo.setCreateBeginDate(DateUtil.parseTime("2020-11-01 00:00:00"));
                    orderQueryVo.setCreateEndDate(TimeRangeUtil.endTime());
                }
            }
        }
        log.info("时间转换后的参数:{}", JSON.toJSONString(orderQueryVo));
    }

    /**
     * 提交支付回单
     * @param dto
     * @return
     */
    @PostMapping("uploadWorkerBillPath")
    public RestResult<OrderItemResVo> uploadWorkerBillPath(@Validated @RequestBody OfflineWorkerBillPathUpdateDto dto) {
        final OfflineOrderItem orderItem = orderItemBiz.uploadWorkerBillPath(dto);
        OrderItemResVo resVo = new OrderItemResVo();
        BeanUtil.copyProperties(orderItem,resVo);
        resVo.setReceiveIdCardNo(orderItem.getReceiveIdCardNoDecrypt());
        resVo.setReceiveName(orderItem.getReceiveNameDecrypt());
        resVo.setReceivePhoneNo(orderItem.getReceivePhoneNoDecrypt());
        resVo.setReceiveAccountNo(orderItem.getReceiveAccountNoDecrypt());
        resVo.setWorkerBillFilePath(orderItem.getWorkerBillFilePath());
        return RestResult.success(resVo);
    }

    /**
     * 开始发放：发放明细页批次统计信息
     * @param platBatchNo 平台批次号
     * @param employerNo 商户编号
     * @return 批次统计信息
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("getOrderByBatchNo")
    public RestResult<OrderWithCountResVo> getOrderByBatchNo(@RequestParam String platBatchNo,
                                                             @CurrentMchNo String employerNo) {
        OrderWithCountResVo result = offlineOrderBiz.getOrderByBatchNo(platBatchNo,employerNo);
        return RestResult.success(result);
    }

    /**
     * 开始发放：发放明细页
     * @param platBatchNo 平台批次号
     * @param employerNo 商户编号
     * @return 发放明细列表
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderItemByBatchNoPage")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemByBatchNoPage(@RequestParam String platBatchNo,
                                                                                   @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        PageResult<List<OrderItemResVo>> result = orderItemBiz.listOrderItemByBatchNoPage(platBatchNo,pageVo,employerNo);
        return RestResult.success(result);
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @param employerNo 用工企业编号
     * @return 总条数
     */
    @Permission("order:listOrderItemPage:view")
    @PostMapping("countOrderItem")
    public RestResult<Map<String, Object>> countOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                          @CurrentMchNo String employerNo) {
        checkDate(orderItemQueryVo);
        Long totalRecord = orderItemBiz.countOrderItem(orderItemQueryVo,employerNo);
        return RestResult.success(Collections.singletonMap("totalRecord",totalRecord));
    }

    /**
     * 订单明细 总条数
     * @param orderItemQueryVo 查询条件
     * @return 总条数
     */
    @PostMapping("sumOrderItem")
    public RestResult<OrderItemSumBo> sumOrderItem(@Validated @RequestBody OrderItemQueryVo orderItemQueryVo,
                                                   @CurrentMchNo String employerNo){
        checkDate(orderItemQueryVo);
        OrderItemSumBo sumBo = orderItemBiz.sumOrderItem(orderItemQueryVo, employerNo);
        return RestResult.success(sumBo);
    }

    @PostMapping("deleteOrderItem")
    public RestResult<String> deleteOrderItem(String platTrxNo) {
        if (StringUtils.isBlank(platTrxNo)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("平台流水号不能为空");
        }
        orderItemBiz.deleteOrderItem(platTrxNo);
        return RestResult.success("操作成功");
    }

    /**
     * 逻辑删除发放批次
     * @return 批次编号
     * @throws Exception 异常
     */
    @Permission("order:deleteOrder:delete")
    @PostMapping("deleteOrder")
    public RestResult deleteOrder(@RequestBody OrderDeleteDTO orderDeleteDTO){
        offlineOrderBiz.delete(orderDeleteDTO);
        return RestResult.success("删除成功");
    }

    /**
     * 取消订单发放
     * @param paramMap 订单批次号map
     * @param employerNo 用工企业编号
     * @return 提示信息
     */
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "取消订单发放")
    @Permission("order:cancelBatchOrderGrant:edit")
    @PostMapping("cancelBatchOrderGrant")
    public RestResult<String> cancelBatchOrderGrant(@RequestBody Map<String, String> paramMap,@CurrentMchNo String employerNo){
        String platBatchNo = paramMap.get("platBatchNo");
        if (StringUtils.isEmpty(platBatchNo)) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("platBatchNo 不能为空");
        }
        offlineOrderBiz.cancelBatchOrder(platBatchNo,employerNo);
        return RestResult.success("取消成功");
    }


    /**
     * 开始发放：发放明细页（根据不同状态）
     * @param platBatchNo 平台批次号
     * @param employerNo 商户编号
     * @return 不同状态的发放明细列表
     */
    @Permission("order:listOrderPage:view")
    @PostMapping("listOrderItemByBatchNoAndStatus")
    public RestResult<PageResult<List<OrderItemResVo>>> listOrderItemByBatchNoAndStatus(@RequestParam String platBatchNo, Integer orderItemStatus,
                                                                                        @RequestBody PageVo pageVo, @CurrentMchNo String employerNo) {
        PageResult<List<OrderItemResVo>> result = orderItemBiz.listOrderItemByBatchNoAndStatus(platBatchNo,orderItemStatus,pageVo,employerNo);
        return RestResult.success(result);
    }

    @PostMapping("getFailItemByBatchVo")
    public RestResult<PageResult<List<OrderItemFailResVo>>> getFailItemByBatchVo(@RequestBody OrderItemFailQueryVo queryVo, @RequestBody PageVo pageVo) {
        return RestResult.success(orderItemBiz.getFailItemByBatchVo(queryVo,pageVo));
    }

	@PostMapping("exportFailItemByBatchVo")
    public RestResult<String> exportFailItemByBatchVo(@RequestBody OrderItemFailQueryVo queryVo,@CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo) {
        orderItemBiz.exportOrderItemFail(queryVo, vo, employerNo);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }

    @PostMapping("/signByOrderItem/{platTrxNo}")
    public RestResult<String> signByOrderItem(@PathVariable("platTrxNo") String platTrxNo) {
        DataAnalyzeVo analyzeVo = new DataAnalyzeVo();
        OfflineOrderItem orderItem = orderItemBiz.getOrderItemByPlatTrxNo(platTrxNo);
        log.info("发起签约：{}", orderItem);
        analyzeVo.setIdCard(orderItem.getReceiveIdCardNoDecrypt());
        analyzeVo.setEmployerNo(orderItem.getEmployerNo());
        analyzeVo.setMainstayNo(orderItem.getMainstayNo());
        analyzeVo.setReceiveName(orderItem.getReceiveNameDecrypt());
        analyzeVo.setMainstayName(orderItem.getMainstayName());
        analyzeVo.setEmployerName(orderItem.getEmployerName());
        if (StringUtils.isNoneBlank(orderItem.getReceivePhoneNoDecrypt())) {
            analyzeVo.setSignType(ChannelSignTypeEnum.MSG.getValue());
            analyzeVo.setPhone(orderItem.getReceivePhoneNoDecrypt());
        } else {
            analyzeVo.setSignType(ChannelSignTypeEnum.URL_NO_CODE.getValue());
        }
        checkParam(analyzeVo);
        if (analyzeBiz.signOrder(analyzeVo)) {
            return RestResult.success("发起签约成功");
        }
        return RestResult.error("该用户发放时未传手机号码, 请补充手机号码");
    }

    private void checkParam(DataAnalyzeVo analyzeVo) {
        LimitUtil.notEmpty(analyzeVo.getSignType(), "签约方式不能为空");
        LimitUtil.notEmpty(analyzeVo.getIdCard(), "身份证不能为空");
        LimitUtil.notEmpty(analyzeVo.getMainstayNo(), "代征主体编号不能为空");
        LimitUtil.notEmpty(analyzeVo.getEmployerNo(), "用工企业编号不能为空");
        LimitUtil.notEmpty(analyzeVo.getReceiveName(), "姓名不能为空");
        if (analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.MSG.getValue() || analyzeVo.getSignType().intValue() == ChannelSignTypeEnum.URL_CODE.getValue()) {
            LimitUtil.notEmpty(analyzeVo.getPhone(), "手机号不能为空");
        }
    }
}
