package com.zhixianghui.web.employer.biz.data;

import com.alibaba.fastjson.JSONObject;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.common.SignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.ChannelSignTypeEnum;
import com.zhixianghui.common.statics.enums.sign.SignStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.AESUtil;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.MD5Util;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.enums.OperationEnum;
import com.zhixianghui.facade.trade.entity.FreelanceStat;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.SignRecord;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.FreelanceStatFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.vo.FreelanceStatVo;
import com.zhixianghui.facade.trade.vo.SignRecordVo;
import com.zhixianghui.starter.comp.component.FastdfsClient;
import com.zhixianghui.starter.comp.component.RedisLock;
import com.zhixianghui.web.employer.vo.merchant.DataAnalyzeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Method;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @Date 2021/7/27 14:37
 */
@Slf4j
@Service
public class MerchantInfoAnalyzeBiz {

    @Reference(timeout = 30000, methods = {@Method(name = "freelanceStat2", timeout = 30000)})
    private FreelanceStatFacade freelanceStat;
    @Reference
    private SignRecordFacade signRecordFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;
    @Reference
    private OrderItemFacade orderItemFacade;

    @Autowired
    public FastdfsClient fastdfsClient;
    @Autowired
    private RedisLock redisLock;

    public PageResult<List<FreelanceStatVo>> freelanceStat(DataAnalyzeVo analyzeVo, String sortColumn, String mchNo) {
        if (StringUtils.isBlank(mchNo)) {
            return PageResult.newInstance(new ArrayList<>(), PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()), 0L);
        }
        analyzeVo.setEmployerNo(mchNo);
        if (StringUtils.isNotBlank(analyzeVo.getIdCard())) {
            analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
            analyzeVo.setIdCard(null);
        }
        if (StringUtils.isNotBlank(analyzeVo.getReceiveName())) {
            analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
            analyzeVo.setReceiveName(null);
        }
        log.info("自由职业者列表统计查询参数 : {}", JSONObject.toJSON(analyzeVo));
        Map<String, Object> param = BeanUtil.toMap(analyzeVo);

        if (StringUtils.isNotBlank(sortColumn)) {
            param.put("sortColumns", sortColumn);
        }
        PageResult<List<Map<String, Object>>> list = freelanceStat.freelanceStat2(param, PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()));
        if (list == null || CollectionUtils.isEmpty(list.getData())) {
            return PageResult.newInstance(PageParam.newInstance(analyzeVo.getPageCurrent(), analyzeVo.getPageSize()), new ArrayList<>());
        }

        List<FreelanceStatVo> result = new ArrayList<>();
        for (Map<String, Object> stat : list.getData()) {
            FreelanceStatVo statVo = new FreelanceStatVo();
            BeanUtil.mapToObject(statVo, stat);
            if (stat.containsKey("receiveIdCardNo")) {
                statVo.setReceiveIdCardNo(AESUtil.decryptECB(String.valueOf(stat.get("receiveIdCardNo")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (stat.containsKey("receiveName")) {
                statVo.setReceiveName(AESUtil.decryptECB(String.valueOf(stat.get("receiveName")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (stat.containsKey("receivePhoneNo")) {
                statVo.setReceivePhoneNo(AESUtil.decryptECB(String.valueOf(stat.get("receivePhoneNo")), EncryptKeys.getEncryptKeyById(Long.parseLong(String.valueOf(stat.get("encryptKeyId")))).getEncryptKeyStr()));
            }
            if (StringUtils.isBlank(String.valueOf(stat.get("receivePhoneNo")))) {
                statVo.setPhone(OperationEnum.UN_PHONE.getOperation());
            } else {
                statVo.setPhone(OperationEnum.PHONE.getOperation());
            }
            if (Objects.isNull(stat.get("idCardBackUrl")) && Objects.isNull(stat.get("idCardFrontUrl")) && Objects.isNull(stat.get("idCardCopyUrl"))) {
                statVo.setIdCard(0);
            } else {
                statVo.setIdCard(1);
            }
            if (stat.get("signStatus") != null && Integer.parseInt(String.valueOf(stat.get("signStatus"))) == SignStatusEnum.SIGN_SUCCESS.getValue()) {
                statVo.setSignRecord(1);
            }
            statVo.setReceiverOrder(null);
            result.add(statVo);
        }
        return PageResult.newInstance(result, PageParam.newInstance(list.getPageCurrent(), list.getPageSize()), list.getTotalRecord());
    }


    public FreelanceStatVo countFreelance(DataAnalyzeVo analyzeVo, String mchNo) {
        if (StringUtils.isEmpty(mchNo)) {
            return new FreelanceStatVo();
        }
        analyzeVo.setEmployerNo(mchNo);
        if (StringUtils.isNotBlank(analyzeVo.getReceiveName())) {
            analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
        }
        if (StringUtils.isNotBlank(analyzeVo.getIdCard())) {
            analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
        }
        log.info("自由者职业者统计查询参数 : {}", JSONObject.toJSON(analyzeVo));
        return freelanceStat.merchantStatCount(BeanUtil.toMap(analyzeVo));
    }


    public boolean signOrder(DataAnalyzeVo analyzeVo) {
        Map<String, Object> param = buildParam(analyzeVo);
        String lockKey = String.join("-", analyzeVo.getReceiveNameMd5(), analyzeVo.getReceiveIdCardNoMd5(), analyzeVo.getMainstayNo(), analyzeVo.getEmployerNo());
        String clientId = redisLock.tryLockLong(lockKey, 0, 1);
        try {
            if (clientId == null) {
                log.warn("[{}]重复请求", lockKey);
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("你已成功发起签约, 请不要重复提交");
            }
            SignRecord signRecord = signRecordFacade.getOne(param);
            if (signRecord != null) {
                // 两次发送之间的间隔必须大于1天即24h
                if (signRecord.getSmsSendTime() != null &&
                        DateUtil.subtractDays(new Date(), signRecord.getSmsSendTime()) < 1) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发送短信的间隔必须大于24h");
                }
            }
            SignRecord record = buildSign(analyzeVo);

            if (!ObjectUtils.isEmpty(analyzeVo.getSignType())) {
                record.setSignType(analyzeVo.getSignType());
            }
            if (StringUtils.isNotBlank(analyzeVo.getPhone())) {
                record.setReceivePhoneNoEncrypt(analyzeVo.getPhone());
            }

            log.info("待签约信息: " + JSONObject.toJSONString(record));
            signRecordFacade.startSign(record, true);
//            signRecordFacade.startSign(record, record.getSignType() != ChannelSignTypeEnum.STAMP_SILENCE.getValue());
            signRecord = signRecordFacade.getOne(param);
            if (signRecord == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约异常");
            }
            return true;
        } finally {
            if (clientId != null) {
                redisLock.unlockLong(clientId);
            }
        }

    }

    private SignRecord buildSign(DataAnalyzeVo analyzeVo) {
        SignRecord signRecord = new SignRecord();
        signRecord.setCreateTime(new Date());
        signRecord.setUpdateTime(new Date());
        signRecord.setMainstayNo(analyzeVo.getMainstayNo());
        signRecord.setEmployerNo(analyzeVo.getEmployerNo());
        signRecord.setEmployerName(analyzeVo.getEmployerName());
        signRecord.setMainstayName(analyzeVo.getMainstayName());
        signRecord.setSignStatus(SignStatusEnum.SIGN_CREATE.getValue());
        signRecord.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
//        signRecord.setReceiveIdCardNo(freelanceStat.getReceiveIdCardNo());
        signRecord.setReceiveIdCardNoEncrypt(analyzeVo.getIdCard());
//        signRecord.setReceiveName(freelanceStat.getReceiveName());
        signRecord.setReceiveNameEncrypt(analyzeVo.getReceiveName());
//        signRecord.setReceivePhoneNo(freelanceStat.getReceivePhoneNo());
        signRecord.getJsonEntity().setSignType(String.valueOf(SignTypeEnum.RSA.getValue()));
        signRecord.setSmsSendFrequency(0);
        return signRecord;
    }

    public boolean sign(DataAnalyzeVo analyzeVo) {
        Map<String, Object> param = buildParam(analyzeVo);

        SignRecord signRecord = signRecordFacade.getOne(param);
        if (signRecord != null) {
            // 两次发送之间的间隔必须大于1天即24h
            if (signRecord.getSmsSendTime() != null &&
                    DateUtil.subtractDays(new Date(), signRecord.getSmsSendTime()) < 1) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("发送短信的间隔必须大于24h");
            }
        }
        int page = 1;
        PageResult<List<FreelanceStat>> pageData;
        List<FreelanceStat> list = new ArrayList<>();
        do {
            pageData = freelanceStat.listPage(param, PageParam.newInstance(page, 10));
            if (pageData != null && pageData.getData() != null && !pageData.getData().isEmpty()) {
                list.addAll(pageData.getData());
            }
            page++;
        } while (pageData != null && pageData.getData() != null && !pageData.getData().isEmpty());
        if (list.isEmpty()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到记录...");
        }
        String clientId = null;
        try {
            String lockKey = String.join("-", analyzeVo.getReceiveNameMd5(), analyzeVo.getReceiveIdCardNoMd5(), analyzeVo.getMainstayNo(), analyzeVo.getEmployerNo());
            clientId = redisLock.tryLockLong(lockKey, 0, 1);
            if (clientId == null) {
                log.warn("[{}]重复请求", lockKey);
                throw CommonExceptions.GET_LOCK_ERROR.newWithErrMsg("你已成功发起签约, 请勿重复提交");
            }
            boolean flag = false;
            for (FreelanceStat freelanceStat : list) {
                if (!Objects.isNull(signRecord)
                        && !Objects.isNull(signRecord.getSignStatus())
                        && signRecord.getSignStatus() == SignStatusEnum.SIGN_SUCCESS.getValue()) {
                    for (FreelanceStat stat : list) {
                        stat.setSignId(signRecord.getId());
                        stat.setSignRecord(OperationEnum.SIGN.getOperation());
                        this.freelanceStat.update(stat);
                    }

                    return true;
                }

                if (StringUtils.isAllBlank(freelanceStat.getReceivePhoneNo(), analyzeVo.getPhone())) {
                    continue;
                }
                flag = true;
                SignRecord record = new SignRecordVo().build(freelanceStat);

                record.setSignType(ChannelSignTypeEnum.URL_NO_CODE.getValue());
                if (StringUtils.isNotBlank(analyzeVo.getPhone())) {
                    record.setReceivePhoneNoEncrypt(analyzeVo.getPhone());
                } else {
                    record.setReceivePhoneNoEncrypt(
                            AESUtil.decryptECB(freelanceStat.getReceivePhoneNo(), EncryptKeys.getEncryptKeyById(freelanceStat.getEncryptKeyId()).getEncryptKeyStr())
                    );
                }
                if (StringUtils.isNotBlank(record.getReceivePhoneNo())) {
                    record.setSignType(ChannelSignTypeEnum.MSG.getValue());
                }

                log.info("待签约信息: " + JSONObject.toJSONString(record));
                signRecordFacade.startSign(record, true);
//                signRecordFacade.startSign(record, record.getSignType() != ChannelSignTypeEnum.STAMP_SILENCE.getValue());
                signRecord = signRecordFacade.getOne(param);
                if (signRecord == null) {
                    throw CommonExceptions.BIZ_INVALID.newWithErrMsg("签约异常");
                }
                updateFreelanceStat(list, signRecord.getId());
                break;
            }
            if (!flag) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请提供手机号进行签约");
            } else {
                return true;
            }
        } finally {
            if (clientId != null) {
                redisLock.unlockLong(clientId);
            }
        }
    }

    private void updateFreelanceStat(List<FreelanceStat> data, Long id) {
        for (FreelanceStat stat : data) {
            stat.setSignId(id);
        }
        freelanceStat.update(data);
    }

    public boolean uploadIdCard(DataAnalyzeVo analyzeVo) {
        FreelanceStat stat = freelanceStat.getById(analyzeVo.getId());
        log.info("上传身份证, 记录 : {}", JSONObject.toJSON(stat));
        /**
         SignRecord signRecord = null;
         if (stat.getSignId() == null || (signRecord = signRecordFacade.getById(stat.getSignId())) == null
         || (signRecord.getSignStatus() != SignStatusEnum.WAIT_SIGN.getValue()
         && signRecord.getSignStatus() != SignStatusEnum.SIGN_SUCCESS.getValue())) {
         SignRecord record = new SignRecordVo().build(stat);
         log.info("待签约信息: " + JSONObject.toJSONString(record));
         if (StringUtils.isNotBlank(analyzeVo.getPhone())) {
         record.setReceivePhoneNoEncrypt(analyzeVo.getPhone());
         }else {
         record.setReceivePhoneNoEncrypt(
         AESUtil.decryptECB(stat.getReceivePhoneNo(), EncryptKeys.getEncryptKeyById(stat.getEncryptKeyId()).getEncryptKeyStr())
         );
         }
         signRecordFacade.startSign(record, false);
         signRecord = signRecordFacade.getOne(new HashMap<String, Object>() {
         private static final long serialVersionUID = 8029339019056839795L;

         {
         put("receiveNameMd5", stat.getReceiveNameMd5());
         put("receiveIdCardNoMd5", stat.getReceiveIdCardNoMd5());
         put("mainstayNo", stat.getMainstayNo());
         put("employerNo", stat.getEmployerNo());
         }});
         }
         if (signRecord == null) {
         log.error("自由职业者签约id : {}", stat.getSignId());
         return false;
         }
         signRecord.setIdCardBackUrl(analyzeVo.getIdCardBackUrl());
         signRecord.setIdCardFrontUrl(analyzeVo.getIdCardFrontUrl());
         signRecord.setCerFaceUrl(analyzeVo.getCerFaceUrl());
         signRecord.setReceiveAccountNoEncrypt(analyzeVo.getBankCardNumber());
         signRecord.setIdCardType(analyzeVo.getIdCardType());
         signRecord.setIdCardCopyUrl(analyzeVo.getIdCardCopyFileUrl());
         if (StringUtils.isNotBlank(analyzeVo.getPhone())){
         signRecord.setReceivePhoneNoEncrypt(analyzeVo.getPhone());
         }

         signRecordFacade.update(signRecord);**/
        UserInfo userInfo = new UserInfo();
        userInfo.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        userInfo.setReceiveNameEncrypt(
                AESUtil.decryptECB(stat.getReceiveName(), EncryptKeys.getEncryptKeyById(stat.getEncryptKeyId()).getEncryptKeyStr())
        );
        userInfo.setReceiveIdCardNoMd5(stat.getReceiveIdCardNoMd5());
        userInfo.setReceiveIdCardNoEncrypt(
                AESUtil.decryptECB(stat.getReceiveIdCardNo(), EncryptKeys.getEncryptKeyById(stat.getEncryptKeyId()).getEncryptKeyStr())
        );
        userInfo.setCerFaceUrl(analyzeVo.getCerFaceUrl());
        userInfo.setIdCardCopyUrl(analyzeVo.getIdCardCopyFileUrl());
        userInfo.setIdCardFrontUrl(analyzeVo.getIdCardFrontUrl());
        userInfo.setIdCardBackUrl(analyzeVo.getIdCardBackUrl());
        userInfo.setCreateTime(new Date());
        userInfo.setUpdateTime(new Date());
        signRecordFacade.addUserImage(userInfo);
        stat.setIdCard(OperationEnum.UPLOAD_ID_CARD.getOperation());
//        if (stat.getSignId() == null) {
//            stat.setSignId(signRecord.getId());
//        }
        freelanceStat.update(stat);
        return true;
    }

    public boolean uploadIdCardOrder(DataAnalyzeVo analyzeVo) {
        OrderItem orderItem = orderItemFacade.getByPlatTrxNo(analyzeVo.getPlatTrxNo());
        UserInfo userInfo = new UserInfo();
        userInfo.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        userInfo.setReceiveNameEncrypt(orderItem.getReceiveNameDecrypt());
        userInfo.setReceiveIdCardNoMd5(orderItem.getReceiveIdCardNoMd5());
        userInfo.setReceiveIdCardNoEncrypt(orderItem.getReceiveIdCardNoDecrypt());
        userInfo.setCerFaceUrl(analyzeVo.getCerFaceUrl());
        userInfo.setIdCardCopyUrl(analyzeVo.getIdCardCopyFileUrl());
        userInfo.setIdCardFrontUrl(analyzeVo.getIdCardFrontUrl());
        userInfo.setIdCardBackUrl(analyzeVo.getIdCardBackUrl());
        signRecordFacade.addUserImage(userInfo);
        return true;
    }

    public Map<String, Object> buildParam(DataAnalyzeVo analyzeVo) {
        analyzeVo.setReceiveIdCardNoMd5(MD5Util.getMixMd5Str(analyzeVo.getIdCard()));
        analyzeVo.setReceiveNameMd5(MD5Util.getMixMd5Str(analyzeVo.getReceiveName()));
        return new HashMap<String, Object>() {
            private static final long serialVersionUID = 2785284015406866482L;

            {
                put("receiveNameMd5", analyzeVo.getReceiveNameMd5());
                put("receiveIdCardNoMd5", analyzeVo.getReceiveIdCardNoMd5());
                put("mainstayNo", analyzeVo.getMainstayNo());
                put("employerNo", analyzeVo.getEmployerNo());
            }
        };
    }
}
