package com.zhixianghui.web.employer.biz.order.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.common.SuccessFailEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.enums.rmq.MsgDelayLevelEnum;
import com.zhixianghui.common.statics.exception.ApiExceptions;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.*;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.notify.enums.NotifyTypeEnum;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.OrderItem;
import com.zhixianghui.facade.trade.entity.OrderItemFail;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderItemStatusEnum;
import com.zhixianghui.web.employer.biz.order.OrderItemBiz;
import com.zhixianghui.web.employer.exception.MchOrderNoException;
import com.zhixianghui.web.employer.vo.order.CountVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum.CKH_ORDER_ITEM_SEQ;

/**
 * <AUTHOR>
 * @description GrantDataListener 发放文件解析类
 * @date 2020-11-04 10:55
 **/
@Slf4j
public class CKHGrantDataListener extends AnalysisEventListener<BankGrantRow> implements GrantListenerInterface{

    private final List<OrderItem> list = new ArrayList<>();
    private List<String> platTrxNoList;

    /**
     * 构造函数传入
     */
    private final OrderItemBiz orderItemBiz;
    private final Order order;
    private final CountVo countVo;
    private final SequenceFacade sequenceFacade;
    private final NotifyFacade notifyFacade;
    private final String topic;
    private final String tag;

    private final int headRowNum;
    private Set<String> mchNoSets = new HashSet<>();
    private List<String> phoneMustMainstays;
    private boolean failFlag = false;

    public CKHGrantDataListener(OrderItemBiz orderItemBiz, SequenceFacade sequenceFacade, NotifyFacade notifyFacade, Order order, CountVo countVo, int headRowNum, List<String> phoneMustMainstays,String topic,String tag) {
        this.orderItemBiz = orderItemBiz;
        this.sequenceFacade = sequenceFacade;
        this.notifyFacade = notifyFacade;
        this.order = order;
        this.countVo = countVo;
        this.headRowNum = headRowNum;
        this.phoneMustMainstays = phoneMustMainstays;
        this.topic = topic;
        this.tag = tag;
    }

    /**
     * 这个每一条数据解析都会来调用
     */
    @Override
    public void invoke(@Valid BankGrantRow data, AnalysisContext context) {
        Integer rowNum = context.readRowHolder().getRowIndex();
        OrderItem item = checkRow(data,rowNum+1);
        if(item != null) {
            if (StringUtils.equals(SuccessFailEnum.FAIL.getCode(), item.getErrorCode())) {
                this.failFlag = true;
                return;
            }
            String platTrxNo = sequenceFacade.nextRedisId("",SequenceBizKeyEnum.CKH_ORDER_ITEM_SEQ.getKey(), SequenceBizKeyEnum.CKH_ORDER_ITEM_SEQ.getWidth());
            fillOrderItem(item, order, platTrxNo);
            list.add(item);
            countVo.setRequestCount(countVo.getRequestCount() + 1);
            countVo.setRequestNetAmount(countVo.getRequestNetAmount().add(item.getOrderItemTaskAmount()==null? BigDecimal.ZERO: item.getOrderItemTaskAmount()));
            if(Objects.equals(item.getOrderItemStatus(), OrderItemStatusEnum.GRANT_FAIL.getValue())) {
                countVo.setFailCount(countVo.getFailCount() + 1);
                countVo.setFailNetAmount(countVo.getFailNetAmount().add(item.getOrderItemTaskAmount()==null? BigDecimal.ZERO: item.getOrderItemTaskAmount()));
            }
        }
    }

    private void fillOrderItem(OrderItem item, Order order, String platTrxNo) {
        Date now = new Date();
        item.setCreateDate(now);
        item.setCreateTime(now);
        item.setUpdateTime(now);
        item.setMchBatchNo(order.getMchBatchNo());
        item.setPlatBatchNo(order.getPlatBatchNo());
        item.setProductNo(order.getProductNo());
        item.setProductName(order.getProductName());
        String compPlatTrxNo = CKH_ORDER_ITEM_SEQ.getPrefix() + DateUtil.formatCompactDate(now) + platTrxNo;

        if (StringUtils.isBlank(item.getMchOrderNo())) {
            item.setMchOrderNo(compPlatTrxNo);
        }

        item.setPlatTrxNo(compPlatTrxNo);

        item.setLaunchWay(LaunchWayEnum.EMPLOYER.getValue());
        item.setEmployerNo(order.getEmployerNo());
        item.setEmployerName(order.getEmployerName());
        item.setMainstayNo(order.getMainstayNo());
        item.setMainstayName(order.getMainstayName());
        item.setChannelType(order.getChannelType());
        item.setPayChannelNo(order.getPayChannelNo());
        item.setChannelName(order.getChannelName());
        item.setJobId(order.getJobId());
        item.setJobName(order.getJobName());
        item.setAccessTimes(0);
        item.setLaunchWay(LaunchWayEnum.EMPLOYER.getValue());
        item.setWorkCategoryCode(order.getWorkCategoryCode());
        item.setWorkCategoryName(order.getWorkCategoryName());
    }

    private OrderItem checkRow(BankGrantRow data,Integer rowNum) {
        String mchOrderNo = data.getMchOrderNo();
        String receiveName = data.getReceiveName();
        String receiveAccountNo = data.getReceiveAccountNo();
        String receiveIdCardNo = data.getReceiveIdCardNo();
        String receivePhoneNo = data.getReceivePhoneNo();
        //请求金额 = 任务金额
        String taskAmountStr = data.getRequestAmountStr();
        String desc = data.getDesc();
        String appid=data.getAppId();
        String memo = data.getMemo();

        if(StringUtils.isBlank(receiveName) && StringUtils.isBlank(receiveIdCardNo) && StringUtils.isBlank(receiveAccountNo)) {
            log.info("[{}] 第{}行,收款人姓名、身份证、收款账户均为空 ----> 跳过",order.getPlatBatchNo(),rowNum);
            return null;
        }

        StringBuilder errorDesc = new StringBuilder();
        BigDecimal receiveAmount = null;
        if (StringUtils.isBlank(receiveName)) {
            errorDesc.append("姓名为空;");
            if (receiveName == null) {
                receiveName = "   ";
            }
        }else {
            receiveName =  receiveName.replaceAll(" ", "").trim();
        }
        if (!ValidateUtil.isChineseName(receiveName)) {
            errorDesc.append("receiveName  姓名(" + data.getReceiveName() + ")格式错误");
            if (receiveName.length() > 20) {
                receiveName = receiveName.substring(0, 18);
            }
        }
        if (StringUtils.isEmpty(receiveIdCardNo)) {
            errorDesc.append("身份证号为空;");
            if (receiveIdCardNo == null) {
                receiveIdCardNo = "   ";
            }
        }
        if (StringUtils.isNotBlank(receiveIdCardNo)) {
            receiveIdCardNo = receiveIdCardNo.replaceAll(" ", "").trim();
        }
        if (StringUtils.isNotEmpty(receiveIdCardNo) && !IDCardUtils.verifi(receiveIdCardNo)) {
            errorDesc.append("身份证号[").append(receiveIdCardNo).append("]有误;");
            if (receiveIdCardNo.length() > 18) {
                receiveIdCardNo = receiveIdCardNo.substring(0, 18);
            }
        }
        if (StringUtils.isBlank(receiveAccountNo)) {
            errorDesc.append("收款账号为空;");
            if (receiveAccountNo == null) {
                receiveAccountNo = "   ";
            }
        }else {
            receiveAccountNo = receiveAccountNo.replaceAll(" ", "").trim();
            if (ValidateUtil.isChinese(receiveAccountNo)){
                errorDesc.append("收款账号["+receiveAccountNo+"]不能是中文");
            }
            if (receiveAccountNo.length() > 32) {
                errorDesc.append("收款账号["+receiveAccountNo+"]过长");
                receiveAccountNo = receiveAccountNo.substring(0,20)+"...";
            }
        }

        if (phoneMustMainstays != null && phoneMustMainstays.contains(order.getMainstayNo()) && StringUtils.isBlank(receivePhoneNo)) {
            errorDesc.append("该代征主体收款人手机号必填");
        }

        if (StringUtil.isNotEmpty(receivePhoneNo) && !ValidateUtil.isMobile(receivePhoneNo)) {
            errorDesc.append("手机号[").append(receivePhoneNo).append("]有误;");

            if (ValidateUtil.isChinese(receivePhoneNo)) {
                errorDesc.append("手机号[").append(receivePhoneNo).append("]有误;");
                if (receivePhoneNo.length() > 5) {
                    receivePhoneNo = receivePhoneNo.substring(0, 5);
                }
            }
            if (receivePhoneNo.length() > 11) {
                receivePhoneNo = receivePhoneNo.substring(0,11);
            }
        }

        if (StringUtils.isBlank(taskAmountStr)) {
            errorDesc.append("任务金额为空;");
            if (taskAmountStr == null) {
                taskAmountStr = "0";
            }
        }
        if (StringUtil.isNotEmpty(taskAmountStr) && !ValidateUtil.isDoubleAnd2decimals(taskAmountStr)) {
            errorDesc.append("任务金额[").append(taskAmountStr).append("]必须是数字，且最多两位小数;");
        }
        if(StringUtil.isNotEmpty(taskAmountStr) && ValidateUtil.isDoubleAnd2decimals(taskAmountStr)) {
            receiveAmount = new BigDecimal(taskAmountStr);
            if (receiveAmount.compareTo(new BigDecimal("0.1")) < 0) {
                errorDesc.append("任务金额[").append(taskAmountStr).append("]有误，必须大于等于0.1元;");
            }
        }

        if (StringUtils.length(desc) > 30) {
            log.warn("第{}行-原始备注信息：{}", rowNum, desc);
            errorDesc.append("备注不能超过30字符;");
            desc = desc.substring(0, 25)+"...";
//            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(StrUtil.format("第{}行-备注不能超过30字符",rowNum));
        }
        if (StringUtils.length(memo) > 30) {
            log.warn("第{}行-原始备忘录信息：{}", rowNum, memo);
            errorDesc.append("备忘录信息不能超过30字符;");
            memo = memo.substring(0, 25)+"...";
        }

        if(this.order.getChannelType()== ChannelTypeEnum.WENXIN.getValue()){
            if(StringUtils.isEmpty(appid)){
                errorDesc.append("appid不能为空");
            }
        }

        OrderItem orderItem = new OrderItem();
        orderItem.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        orderItem.setReceiveNameEncrypt(stringValueOf(receiveName));
        orderItem.setReceiveIdCardNoEncrypt(stringValueOf(receiveIdCardNo));
        orderItem.setReceiveAccountNoEncrypt(stringValueOf(receiveAccountNo));
        orderItem.setReceivePhoneNoEncrypt(stringValueOf(receivePhoneNo));
        //设置为任务金额
        orderItem.setOrderItemTaskAmount(receiveAmount != null ? receiveAmount:BigDecimal.ZERO);
        orderItem.setRemark(stringValueOf(desc));
        orderItem.setOrderItemStatus(OrderItemStatusEnum.CREATE.getValue());
        orderItem.setAppid(appid);
        orderItem.setMemo(memo);

        if (StringUtils.isNotBlank(mchOrderNo)) {

            if (mchOrderNo.length() > 32) {
                saveFailItem(data,rowNum,"第"+rowNum+"行，商户订单号["+mchOrderNo+"]不能超过32位");
                orderItem.setErrorCode(SuccessFailEnum.FAIL.getCode());
            } else if (mchNoSets.contains(mchOrderNo)){
                saveFailItem(data,rowNum,"第"+rowNum+"行，商户订单号["+mchOrderNo+"]重复");
                orderItem.setErrorCode(SuccessFailEnum.FAIL.getCode());
            }else {
                final Long count = orderItemBiz.countItemByMchNo(this.order.getEmployerNo(), mchOrderNo);
                if (count > 0L) {
                    saveFailItem(data,rowNum,"第"+rowNum+"行，商户订单号["+mchOrderNo+"]在数据库中已存在");
                    orderItem.setErrorCode(SuccessFailEnum.FAIL.getCode());
                } else {
                    orderItem.setMchOrderNo(mchOrderNo);
                    mchNoSets.add(mchOrderNo);
                }
            }

        }

        if(errorDesc.length() > 0 ) {
            orderItem.setErrorDesc("第" + rowNum + "行:" + errorDesc.toString());
            orderItem.setErrorCode(ApiExceptions.API_BIZ_FAIL.getApiErrorCode());
            orderItem.setOrderItemStatus(OrderItemStatusEnum.GRANT_FAIL.getValue());
        }
        return orderItem;
    }

    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (!this.mchNoSets.isEmpty()) {
            this.mchNoSets.clear();
            this.mchNoSets = null;
        }

        if (!failFlag) {
            if(!ObjectUtils.isEmpty(list)){
                saveData();
                notifyAcceptStart(order.getEmployerNo(),order.getPlatBatchNo(),
                        list.stream().map(OrderItem::getPlatTrxNo).collect(Collectors.toList()));
            }
        }else {
            throw new MchOrderNoException("订单号错误");
        }


        log.info("所有数据解析完成！");

    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", list.size());
        try{
            orderItemBiz.batchInsert(list);
        }catch (Exception e){
            list.clear();
            log.error("[{}]==>批量插入数据库异常",order.getPlatBatchNo(),e);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("数据库导入异常");
        }
        log.info("存储数据库成功！");
    }

    /**
     * 发消息
     */
    private void sendData(String employerNo, String platBatchNo, List<String> notifyList) {
        log.info("通知受理, 批次:[{}],明细范围[{}]至[{}]", platBatchNo, notifyList.get(0), notifyList.get(notifyList.size() - 1));
        Map<String,String> infoMap = Maps.newHashMap();
        infoMap.put("employerNo", employerNo);
        infoMap.put("platBatchNo", platBatchNo);
        infoMap.put("platTrxNos", JSON.toJSONString(notifyList));
        notifyFacade.sendOne(
                topic,
                UUIDUitl.generateString(10),
                platBatchNo,
                NotifyTypeEnum.TRADE_NOTIFY.getValue(),
                tag,JSON.toJSONString(infoMap),
                MsgDelayLevelEnum.S_1.getValue()
        );
        notifyList.clear();
    }

    /**
     * 通知受理
     */
    private void notifyAcceptStart(String employerNo, String platBatchNo, List<String> platTrxNos) {
        if(ObjectUtils.isEmpty(platTrxNos)){
            return;
        }
        log.info("{}条数据，开始分批通知受理！ ", list.size());

        List<String> notifyList = Lists.newArrayList();
        platTrxNos.forEach(
            trxNo->{
                notifyList.add(trxNo);
                if(notifyList.size() >= NOTIFY_COUNT){
                    //满NOTIFY_COUNT 发一次
                    sendData(employerNo, platBatchNo, notifyList);
                }
            }
        );

        //发送剩余的不满NOTIFY_COUNT的
        if(!ObjectUtils.isEmpty(notifyList)){
            sendData(employerNo, platBatchNo, notifyList);
        }

        log.info("分批通知受理成功！");
    }



    /**
     * 在转换异常 获取其他异常下会调用本接口。抛出异常则停止读取。如果这里不抛出异常则 继续读取下一行。
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception{
        // 如果是某一个单元格的转换异常 能获取到具体行号
        // 如果要获取头的信息 配合invokeHeadMap使用
        if (exception instanceof ExcelDataConvertException) {
            log.error("[{}] 遇到解析失败，但是继续解析下一行:{}", order.getPlatBatchNo(),exception.getMessage());
            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException)exception;
            log.error("第{}行，第{}列解析异常", excelDataConvertException.getRowIndex(),
                    excelDataConvertException.getColumnIndex());
        }else if(exception instanceof BizException){
            log.error("[{}] 解析遇到业务异常:{}", order.getPlatBatchNo(),exception.getMessage());
            throw (BizException)exception;
        }else {
            throw exception;
        }
    }

    private static String stringValueOf(Object obj) {
        return (obj == null) ? " " : obj.toString();
    }

    private void saveFailItem(BankGrantRow data,Integer rowNum,String errorInfo) {
        OrderItemFail orderItemFail = new OrderItemFail();
        Date now = new Date();
        orderItemFail.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        orderItemFail.setReceiveNameEncrypt(stringValueOf(data.getReceiveName()));
        orderItemFail.setReceiveIdCardNoEncrypt(stringValueOf(data.getReceiveIdCardNo()));
        orderItemFail.setReceiveAccountNoEncrypt(stringValueOf(data.getReceiveAccountNo()));
        orderItemFail.setReceivePhoneNoEncrypt(stringValueOf(data.getReceivePhoneNo()));
        orderItemFail.setLine(rowNum);
        orderItemFail.setCreateTime(now);
        orderItemFail.setPlatBatchNo(this.order.getPlatBatchNo());
        orderItemFail.setUpdateTime(now);
        orderItemFail.setErrorDesc(errorInfo);
        orderItemFail.setMchOrderNo(data.getMchOrderNo());

        orderItemBiz.saveFailItem(orderItemFail);
    }
}
