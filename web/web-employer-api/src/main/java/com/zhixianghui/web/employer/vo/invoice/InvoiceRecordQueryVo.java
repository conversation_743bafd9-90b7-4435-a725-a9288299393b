package com.zhixianghui.web.employer.vo.invoice;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/28
 **/
@Data
public class InvoiceRecordQueryVo extends PageQueryVo {
    private static final long serialVersionUID = 1482686887160898660L;
    /**
     * 状态
     */
    private Integer invoiceStatus;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 流水号
     */
    private String trxNo;
    /**
     * 商户编号
     */
    private String mainstayMchNo;
    /**
     * 创建时间起始时间
     */
    private String createTimeBegin;
    /**
     * 创建时间终止时间
     */
    private String createTimeEnd;
    /**
     * 完成时间起始时间
     */
    private String completeTimeBegin;
    /**
     * 完成时间终止时间
     */
    private String completeTimeEnd;

    /**
     * 产品编号
     */
    private String productNo;

    private Integer source;

    private Integer amountType;

    private String workCategoryCode;
}
