package com.zhixianghui.web.employer.controller.report;

import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.dto.PayChannelDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.MainstayChannelRelation;
import com.zhixianghui.facade.common.entity.report.PayChannel;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.MainstayChannelRelationFacade;
import com.zhixianghui.facade.common.service.PayChannelFacade;
import com.zhixianghui.facade.trade.entity.AcRechargeAccount;
import com.zhixianghui.facade.trade.enums.AlipayIncomeTypeEnum;
import com.zhixianghui.facade.trade.service.AcRechargeAccountFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("payChannel")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PayChannelController {

    @Reference
    private PayChannelFacade payChannelFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private MainstayChannelRelationFacade mainstayChannelRelationFacade;
    @Reference
    private AcRechargeAccountFacade acRechargeAccountFacade;

    @GetMapping("getByChannelNo")
    public RestResult<PayChannel> getChannelByNo(@RequestParam String channelNo, @RequestParam String mainstayNo,@RequestParam(required = false) Integer type,@RequestParam(required = false) Integer channelType, @CurrentMchNo String mchNo) {
        PayChannel payChannel = payChannelFacade.getByChannelNo(channelNo);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo", mchNo);
        paramMap.put("mainstayNo", mainstayNo);
        paramMap.put("payChannelNo", channelNo);
        paramMap.put("channelType",channelType);
        List<EmployerAccountInfo> employerAccountInfoList = employerAccountInfoFacade.listBy(paramMap);
        if (CollectionUtils.isEmpty(employerAccountInfoList)){
            return RestResult.error("商户对应通道账号不存在");
        }
        EmployerAccountInfo employerAccountInfo = employerAccountInfoList.get(0);
        MainstayChannelRelation channelRelation = mainstayChannelRelationFacade.getByMainstayNoAndPayChannelNo(mainstayNo,payChannel.getPayChannelNo());
        if (payChannel.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name())||payChannel.getPayChannelNo().equals(ChannelNoEnum.CMB.name())){
            //查询供应商
            if (channelRelation == null){
                return RestResult.error("供应商通道信息不存在");
            }
            payChannel.setAccountNo(channelRelation.getAccountNo());

            if (payChannel.getPayChannelNo().equals(ChannelNoEnum.CMB.name())){
                payChannel.setAccountNo(channelRelation.getAccountNo()+employerAccountInfo.getSubMerchantNo());
            }

            payChannel.setAccountName(channelRelation.getAccountName());
            payChannel.setBankName(channelRelation.getBankName());
            payChannel.setSubBankName(channelRelation.getSubBankName());
            payChannel.setJoinBankNo(channelRelation.getJoinBankNo());
            payChannel.setBankAddress(channelRelation.getBankAddress());
        }else if(payChannel.getPayChannelNo().equals(ChannelNoEnum.ALIPAY.name()) && type != null && type == AlipayIncomeTypeEnum.OFFLINE_TO_PUBLIC.getValue()){
            payChannel.setAccountNo(employerAccountInfo.getAlipayCardNo());
            payChannel.setAccountName(employerAccountInfo.getEmployerName());
            payChannel.setSubBankName(channelRelation.getSubBankName());
            payChannel.setBankName(channelRelation.getBankName());
            payChannel.setBankAddress(channelRelation.getBankAddress());
            payChannel.setJoinBankNo(channelRelation.getJoinBankNo());
        }else if (payChannel.getPayChannelNo().equals(ChannelNoEnum.JOINPAY.name())){
            payChannel.setAccountName(employerAccountInfo.getEmployerName());
            payChannel.setAccountNo(employerAccountInfo.getSubMerchantNo());
        } else if (payChannel.getPayChannelNo().equals(ChannelNoEnum.JOINPAY_JXH.name())){
            // 君享汇，查询商户报备后的返回的信息
            AcRechargeAccount account = acRechargeAccountFacade.getRechargeAccountByMchNoAndMainstayNo(mchNo, mainstayNo, channelNo);
            if (account != null) {
                payChannel.setAccountName(account.getAccountName());
                payChannel.setAccountNo(account.getAccountNo());
                payChannel.setBankAddress(account.getAccountLoc());
                payChannel.setBankName(account.getAccountBank());
                payChannel.setBankNo(account.getAccountBkno());
                payChannel.setJoinBankNo(account.getAccountBkno());
                payChannel.setSubBankName(account.getAccountBanch());
            }
        }
        return RestResult.success(payChannel);
    }


    @PostMapping("listChannels")
    public RestResult<PageResult<List<PayChannelDto>>> listlistChannels() {
        PageResult<List<PayChannelDto>> pageResult = payChannelFacade.listCustomPage(new HashMap<String,Object>(), PageParam.newInstance(1,50));
        return RestResult.success(pageResult);
    }

}
