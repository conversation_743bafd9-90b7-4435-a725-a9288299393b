package com.zhixianghui.web.employer.controller.anoncreate;

import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.service.MerchantCreateAnonFacade;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerAddVo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.component.CaptchaHelper;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AnonCreateMerchantController
 * @Description TODO
 * @Date 2023/4/17 18:06
 */
@RestController
@RequestMapping("anonMerchant")
public class AnonCreateMerchantController {

    @Reference
    private SequenceFacade sequenceFacade;

    @Reference
    private MerchantCreateAnonFacade merchantCreateAnonFacade;

    @Autowired
    private CaptchaHelper captchaHelper;

    private final static String KEY_PRE = "anonymous:create:";

    /**
     * 验证码超时1h
     */
    private final static int EXPIRE_TIME = 60 * 60;


    /**
     * 验证码发送间隔60s
     */
    private final static int INTERVAL_TIME = 60;

    @PostMapping("sendHeadSmsCode")
    @Logger(type = OperateLogTypeEnum.LOGIN, action = "发送短信验证码")
    public RestResult sendHeadSmsCode(@RequestParam String contactPhone){
        String key = KEY_PRE + contactPhone;
        captchaHelper.sendSmsCode(contactPhone,key,EXPIRE_TIME,INTERVAL_TIME);
        return RestResult.success("验证码发送成功");
    }

    @PostMapping("appNewMerchant")
    public  RestResult applyNewMerchant(@RequestParam String p,@Valid @RequestBody MerchantEmployerAddVo merchantEmployerAddVo){
        //校验验证码
        String key = KEY_PRE + merchantEmployerAddVo.getContactPhone();
        captchaHelper.verifySmsCode(merchantEmployerAddVo.getContactPhone(),merchantEmployerAddVo.getSmsCode(),key);
        captchaHelper.invalidSmsCode(key,merchantEmployerAddVo.getContactPhone());
        // 生成商户编号
        String mchNo = sequenceFacade.nextRedisId(SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getPrefix(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getKey(),
                SequenceBizKeyEnum.MERCHANT_EMPLOYER_SEQ.getWidth());

        merchantEmployerAddVo.setMchNo(mchNo);
        merchantCreateAnonFacade.applyMerchant(p,merchantEmployerAddVo);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("mchNo",mchNo);
        resultMap.put("mchName",merchantEmployerAddVo.getMchName());
        resultMap.put("contactPhone",merchantEmployerAddVo.getContactPhone());
        return RestResult.success(resultMap);
    }

}
