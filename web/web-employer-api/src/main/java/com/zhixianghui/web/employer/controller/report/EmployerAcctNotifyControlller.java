package com.zhixianghui.web.employer.controller.report;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.entity.report.EmployerAccountNotifyConfig;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.biz.report.EmployerAcctNotifyBiz;
import com.zhixianghui.web.employer.vo.report.EmployerAccountNotifyConfigVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("employerNotify")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmployerAcctNotifyControlller {

    @Reference
    private MerchantFacade merchantFacade;
    final private EmployerAcctNotifyBiz employerAcctNotifyBiz;

    @PostMapping("updateConfig")
    @Permission("employerBalance:notify:update")
    public RestResult updateConfig(@Validated @RequestBody EmployerAccountNotifyConfigVo configVo,
                                   @CurrentStaffVo EmployerStaffVO vo, @CurrentMchNo String employerNo) {

        if (StringUtils.compare(configVo.getNotifyTimeStart(), configVo.getNotifyTimeEnd()) > 0) {
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("结束时间不能早于或等于开始时间");
        }

        final Merchant merchant = merchantFacade.getByMchNo(employerNo);
        if (Objects.isNull(merchant)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        final Merchant mainstay = merchantFacade.getByMchNo(configVo.getMainstayNo());
        if (Objects.isNull(mainstay)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体不存在");
        }

        employerAcctNotifyBiz.updateConfig(configVo, vo,merchant);

        return RestResult.success("更新成功");
    }

    @PostMapping("getConfig")
    @Permission("employerBalance:notify:view")
    public RestResult<EmployerAccountNotifyConfig> getConfig(@RequestParam String mainstayNo, @CurrentMchNo String employerNo) {

        final Merchant merchant = merchantFacade.getByMchNo(employerNo);
        if (Objects.isNull(merchant)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        final Merchant mainstay = merchantFacade.getByMchNo(mainstayNo);
        if (Objects.isNull(mainstay)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体不存在");
        }
        return RestResult.success(employerAcctNotifyBiz.getConfig(mainstayNo, employerNo));
    }

    @Permission("employerBalance:notify:view")
    @PostMapping("listNotifyConfig")
    public RestResult<List<EmployerAccountNotifyConfig>> listNotifyConfig(@RequestParam(required = false) String mainstayNo, @CurrentMchNo String employerNo) {
        return RestResult.success(employerAcctNotifyBiz.listNotifyConfig(employerNo, mainstayNo));
    }
}
