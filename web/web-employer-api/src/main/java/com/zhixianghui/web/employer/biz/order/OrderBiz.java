package com.zhixianghui.web.employer.biz.order;

import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.constants.common.PublicStatus;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.fee.RemovedEnum;
import com.zhixianghui.common.statics.enums.merchant.MchStatusEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.PageResult;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.fee.entity.MerchantProductRelation;
import com.zhixianghui.facade.fee.entity.Vendor;
import com.zhixianghui.facade.fee.service.MerchantProductFacade;
import com.zhixianghui.facade.fee.service.VendorFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.entity.MerchantEmployerQuote;
import com.zhixianghui.facade.merchant.service.MerchantEmployerQuoteFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.dto.OrderDeleteDTO;
import com.zhixianghui.facade.trade.dto.WithdrawDto;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.entity.WithdrawRecord;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.OrderAsyncFacade;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.web.employer.utils.BeanToMapUtil;
import com.zhixianghui.web.employer.vo.PageVo;
import com.zhixianghui.web.employer.vo.order.req.OrderQueryVo;
import com.zhixianghui.web.employer.vo.order.res.MainstayResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderResVo;
import com.zhixianghui.web.employer.vo.order.res.OrderWithCountResVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-03 16:00
 **/
@Service
@Slf4j
public class OrderBiz {
    @Reference(retries = -1)
    private OrderFacade orderFacade;
    @Reference(retries = -1)
    private OrderAsyncFacade orderAsyncFacade;

    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantProductFacade merchantProductFacade;
    @Reference
    private MerchantEmployerQuoteFacade merchantEmployerQuoteFacade;
    @Reference
    private VendorFacade vendorFacade;


    public PageResult<List<OrderResVo>> listOrderPage(OrderQueryVo orderQueryVo, PageVo pageVo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        PageParam pageParam = pageVo.toPageParam("ID DESC");
        pageParam.setIsNeedTotalRecord(false);
        PageResult<List<Order>> pageResult = orderFacade.listPage(paramMap,pageParam);
        List<OrderResVo> list = pageResult.getData().stream().map(
                order->{
                    OrderResVo orderResVo = new OrderResVo();
                    BeanUtils.copyProperties(order,orderResVo);
                    return orderResVo;
                }
        ).collect(Collectors.toList());
        return PageResult.newInstance(list,pageResult.getPageCurrent(),pageResult.getPageSize(),pageResult.getTotalRecord());
    }

    public OrderWithCountResVo getOrderByBatchNo(String platBatchNo, String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("platBatchNo",platBatchNo);
        paramMap.put("employerNo",employerNo);
        Order order = orderFacade.getOne(paramMap);
        OrderWithCountResVo orderWithCountResVo = new OrderWithCountResVo();
        BeanUtils.copyProperties(order,orderWithCountResVo);
        if (order.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
            orderWithCountResVo.setRequestNetAmount(order.getRequestNetAmount());
        }else if (order.getProductNo().equals(ProductNoEnum.CKH.getValue()) || order.getProductNo().equals(ProductNoEnum.JKH.getValue())){
            orderWithCountResVo.setRequestNetAmount(order.getRequestTaskAmount());
        }
        return orderWithCountResVo;
    }

    public List<MainstayResVo> listOpenMainstayByEmployerNoAndProductNo(String productNo,String employerNo,Integer status) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("productNo",productNo);
        paramMap.put("mchNo",employerNo);
        paramMap.put("status",status);

        List<MerchantEmployerQuote> quoteList = merchantEmployerQuoteFacade.listGroupByProductNo(paramMap);
        List<String> mainstayNoList = quoteList.stream().map(x->x.getMainstayMchNo()).collect(Collectors.toList());
        paramMap.clear();
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNoList",mainstayNoList);
        paramMap.put("sortColumns","STATUS,ID asc");
        List<EmployerMainstayRelation> list = employerMainstayRelationFacade.listBy(paramMap);

        return list.stream().map(
                rel ->{
                    MainstayResVo mainstayResVo = new MainstayResVo();
                    BeanUtils.copyProperties(rel,mainstayResVo);
                    return mainstayResVo;
                }
        ).collect(Collectors.toList());
    }

    public List<MainstayResVo> listOpenMainstayByEmployerNo(String employerNo,Integer status) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo );
        if (status != null) {
            paramMap.put("status", status);
        }
        List<EmployerMainstayRelation> list = employerMainstayRelationFacade.listBy(paramMap);
        List<MainstayResVo> mainstayResVoList = new ArrayList<>();
        for (EmployerMainstayRelation rel : list) {
            final Vendor vendor = vendorFacade.getVendorByNo(rel.getMainstayNo());
            if (vendor != null){
                MainstayResVo mainstayResVo = new MainstayResVo();
                BeanUtils.copyProperties(rel, mainstayResVo);
                mainstayResVo.setProductNo(vendor.getProductNo());
                mainstayResVo.setProductName(vendor.getProductName());
                mainstayResVoList.add(mainstayResVo);
            }
        }
        return mainstayResVoList;
    }

    public List<MainstayResVo> listOpenMainstayByEmployerNoAndProductNoNew(String productNo,String employerNo,Integer status) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo );
        if (status != null) {
            paramMap.put("status", status);
        }
        List<EmployerMainstayRelation> list = employerMainstayRelationFacade.listBy(paramMap);
        return list.stream().filter(it->{
            final Vendor vendor = vendorFacade.getVendorByNo(it.getMainstayNo());
            return StringUtils.equals(vendor.getProductNo(), productNo);
        }).map(
                rel ->{
                    final Vendor vendor = vendorFacade.getVendorByNo(rel.getMainstayNo());
                    MainstayResVo mainstayResVo = new MainstayResVo();
                    BeanUtils.copyProperties(rel,mainstayResVo);
                    mainstayResVo.setProductNo(vendor.getProductNo());
                    mainstayResVo.setProductName(vendor.getProductName());
                    return mainstayResVo;
                }
        ).collect(Collectors.toList());
    }

    public List<MainstayResVo> listAllMainstayByEmployerNo(String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
        paramMap.put("status",OpenOffEnum.OPEN.getValue());
        List<EmployerMainstayRelation> list = employerMainstayRelationFacade.listBy(paramMap);
        return list.stream().map(
                rel ->{
                    MainstayResVo mainstayResVo = new MainstayResVo();
                    BeanUtils.copyProperties(rel,mainstayResVo);
                    return mainstayResVo;
                }
        ).collect(Collectors.toList());
    }

    public List<Map<String,Object>> listOpenChannelTypeByEmployerNo(String mainstayNo,String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("status",OpenOffEnum.OPEN.getValue());
        List<EmployerAccountInfo> list = employerAccountInfoFacade.listBy(paramMap);
        return list.stream().map(x->{
                    Map<String,Object> map = new HashMap<>();
                    map.put("channelType",x.getChannelType());
                    map.put("channelNo",x.getPayChannelNo());
                    return map;
        }).collect(Collectors.toList());
    }

    public void cancelBatchOrder(String platBatchNo, String employerNo) {
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
        paramMap.put("platBatchNo",platBatchNo);
        Order order = orderFacade.getOne(paramMap);
        if (order == null) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单不存在");
        }
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.PENDING_GRANT.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次状态已经变更，不能取消发放");
        }
        orderFacade.cancelBatchOrder(order);
    }

    public void confirmBatchOrder(String platBatchNo, String employerNo) {
        // 判断订单状态
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
        paramMap.put("platBatchNo",platBatchNo);
        Order order = orderFacade.getOne(paramMap);
        checkOrderStatus(order);
        //校验商户信息状态
        checkMchInfo(order.getEmployerNo(),order.getMainstayNo());
        // 校验报备账户状态
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(order.getEmployerNo(),order.getMainstayNo(),order.getChannelType());
        checkAccountInfo(employerAccountInfo,order);
        //检查余额
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setMainstayNo(order.getMainstayNo());
        amountQueryDto.setEmployerNo(order.getEmployerNo());
        amountQueryDto.setChannelType(order.getChannelType());
        amountQueryDto.setChannelNo(employerAccountInfo.getPayChannelNo());
        amountQueryDto.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
        amountQueryDto.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
        amountQueryDto.setAgreementNo(employerAccountInfo.getSubAgreementNo());

        //总余额=当日收款余额+待清算金额+可结算余额， 金额以元为单位
        String amount = accountQueryFacade.getAmount(amountQueryDto);
        if(StringUtils.isBlank(amount)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("余额查询失败,请联系客服");
        }
        BigDecimal totalBalance = new BigDecimal(amount);
        //创客汇只需要判断实发金额
        //智享汇需要判断实发金额 + 服务费
        if (order.getProductNo().equals(ProductNoEnum.ZXH.getValue())){
            if(totalBalance.compareTo(order.getAcceptedOrderAmount()) < 0 ){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账户余额不足");
            }
        }else if (order.getProductNo().equals(ProductNoEnum.CEP.getValue())){
            if (totalBalance.compareTo(order.getAcceptedTaskAmount()) < 0 ){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账户余额不足");
            }
        }else if (order.getProductNo().equals(ProductNoEnum.CKH.getValue())){
            if (totalBalance.compareTo(order.getAcceptedTaskAmount()) < 0 ){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("账户余额不足");
            }
        }else{
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("产品不存在");
        }

        orderAsyncFacade.startGrant(order);
        log.info("[employerNo:{}  platBatchNo:{}]确认发放请求已异步发出",employerNo,platBatchNo);
    }

    private void checkAccountInfo(EmployerAccountInfo employerAccountInfo, Order order) {
        if (employerAccountInfo == null) {
            log.info("[{}] ==>没有用工企业帐号对应通道配置 通常为未建立代征关系 EmployerNo:[{}] MainstayNo:[{}] ChannelType:[{}]",order.getPlatBatchNo(), order.getEmployerNo(),order.getMainstayNo(),order.getChannelType());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户状态异常，请联系汇聚智享客服");
        }
        if (employerAccountInfo.getStatus().equals(OpenOffEnum.OFF.getValue())) {
            log.info("[{}] ==> 用工企业帐号对应通道配置为关闭",order.getPlatBatchNo());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户状态异常，请联系汇聚智享客服");
        }
        if(employerAccountInfo.getChannelType()!=ChannelTypeEnum.WENXIN.getValue()&&
                !employerAccountInfo.getPayChannelNo().equals(ChannelNoEnum.WXPAY.name()) ){
            if (StringUtils.isBlank(employerAccountInfo.getParentMerchantNo()) || StringUtils.isBlank(employerAccountInfo.getSubMerchantNo())) {
                log.info("[{}] ==> 用工企业帐号对应通道配置的父子商编号为空", order.getPlatBatchNo());
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户状态异常，请联系汇聚智享客服");
            }
        }
        if (StringUtils.isBlank(employerAccountInfo.getPayChannelNo())) {
            log.info("[{}] ==> 用工企业帐号对应通道配置的通道编号为空 选择通道类型:{}",order.getPlatBatchNo(),order.getChannelType());
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户状态异常，请联系汇聚智享客服");
        }
    }

    private void checkOrderStatus(Order order) {
        if (order == null) {
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("订单不存在");
        }
        if(Objects.equals(order.getBatchStatus(), OrderStatusEnum.IMPORTING.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("该批次未受理完成，请稍后再进行提交");
        }
        if(!Objects.equals(order.getBatchStatus(), OrderStatusEnum.PENDING_GRANT.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("批次状态已经变更，不能取消发放");
        }
        Date createTime = order.getCreateTime();
        Date expiredTime = DateUtil.parseJodaDateTime(createTime).plusHours(7*24).toDate();
        Date now = new Date();
        if(DateUtil.compare(now,expiredTime,Calendar.SECOND) > 0){
            log.info("[{}] ==> 过期未发放 触发关闭",order.getPlatBatchNo());
            //order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
            order.setUpdateTime(now);
            order.setCompleteTime(now);
            orderFacade.cancelBatchOrder(order);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("距批次创建时间已经过去 7*24h,批次已失效,自动关闭订单！");
        }
    }

    private void checkMchInfo(String employerNo,String mainstayNo) {
        log.info("校验商户状态信息,用工企业编号:{},代征主体编号:{}",employerNo,mainstayNo);
        //商户状态
        Merchant merchant = merchantQueryFacade.getByMchNo(employerNo);
        if (merchant == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("编号[" + employerNo + "] 商户账号不存在,请联系客服");
        }
        if (!merchant.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("您的商户状态未激活,请联系客服");
        }
        //代征主体
        Merchant mainstay = merchantQueryFacade.getByMchNo(mainstayNo);
        if (mainstay == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("编号[" + mainstayNo + "] 所选代征主体不存在,请联系客服");
        }
        if (!mainstay.getMchStatus().equals(MchStatusEnum.ACTIVE.getValue())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("所选代征主体状态未激活,请联系客服");
        }
    }

    public String getAmount(String mainstayNo, String employerNo,Integer channelType,String payChannelNo) {
        // 校验报备账户状态
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo,mainstayNo,channelType);
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setMainstayNo(mainstayNo);
        amountQueryDto.setEmployerNo(employerNo);
        amountQueryDto.setChannelType(channelType);
        amountQueryDto.setChannelNo(employerAccountInfo.getPayChannelNo());
        amountQueryDto.setSubMerchantNo(employerAccountInfo.getSubMerchantNo());
        amountQueryDto.setChannelMchNo(employerAccountInfo.getParentMerchantNo());
        amountQueryDto.setAgreementNo(employerAccountInfo.getSubAgreementNo());
        //总余额=当日收款余额+待清算金额+可结算余额， 金额以元为单位
        return accountQueryFacade.getAmount(amountQueryDto);
    }

    public Long countOrder(OrderQueryVo orderQueryVo, String employerNo) {
        Map<String, Object> paramMap =  BeanToMapUtil.beanToMap(orderQueryVo);
        paramMap.put("employerNo",employerNo);
        paramMap.put("isDelete", YesNoCodeEnum.NO.getValue());
        return orderFacade.countOrder(paramMap);
    }

    public WithdrawRecord withdraw(WithdrawDto withdrawDto) {
        return orderFacade.Withdraw(withdrawDto);
    }

    public void delete(OrderDeleteDTO orderDeleteDTO){
        orderFacade.delete(orderDeleteDTO);
    }

    public List<MerchantProductRelation> listAllProduct(String mchNo) {
        PageResult<List<MerchantProductRelation>> result = merchantProductFacade.listPage(new HashMap<String, Object>() {{
            put("mchNo", mchNo);
            put("removed", RemovedEnum.NORMAL.getValue());
            put("status", PublicStatus.ACTIVE);
        }}, PageParam.newInstance(1, 100));
        return result.getData();
    }
}
