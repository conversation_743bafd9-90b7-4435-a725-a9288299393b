package com.zhixianghui.web.employer.vo.report;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class EmployerAccountNotifyConfigVo implements Serializable {

    /**
     * 代征主体编号
     */
    @NotBlank
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    @NotBlank
    private String mainstayName;

    /**
     * 提醒金额
     */
    @NotNull
    private BigDecimal notifyAmount;

    /**
     * 提醒类型
     */
    @NotNull
    private Integer notifyType;

    /**
     * 提醒时间段开始时间
     */
    @NotBlank
    private String notifyTimeStart;

    /**
     * 提醒时间段结束时间
     */
    @NotBlank
    private String notifyTimeEnd;

    /**
     * 提醒次数
     */
    @NotNull
    private Integer notifyTimes;

    /**
     * 接收账号
     */
    @NotNull
    private List<String> receiveAccount;

    @NotNull
    private Boolean status;
}