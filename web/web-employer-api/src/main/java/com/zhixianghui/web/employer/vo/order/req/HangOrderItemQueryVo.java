package com.zhixianghui.web.employer.vo.order.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-12 19:47
 **/
@Data
public class HangOrderItemQueryVo {
    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 发放方式
     */
    private Integer channelType;

    /**
     * 收款账户
     */
    private String receiveAccountNo;

    /**
     * 创建起始时间
     */
    private Date createBeginDate;

    /**
     * 创建截止时间
     */
    private Date createEndDate;
}
