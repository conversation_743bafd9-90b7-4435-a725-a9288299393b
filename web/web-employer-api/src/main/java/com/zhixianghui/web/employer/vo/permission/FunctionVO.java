package com.zhixianghui.web.employer.vo.permission;

import com.zhixianghui.facade.merchant.entity.user.portal.EmployerFunction;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class FunctionVO {

    /**
     * id
     */
    private Long id;

    /**
     * 名称
     */
    @NotEmpty(message = "功能名称不能为空")
    @Size(min = 2, max = 30, message = "功能名称长度限定2到30")
    private String name;

    /**
     * 编号
     */
    @NotEmpty(message = "编号不能为空")
    @Size(min = 2, max = 20, message = "编号长度限定2到20")
    private String number;

    /**
     * 父节点id
     */
    private Long parentId;

    /**
     * 权限标识
     */
    @NotEmpty(message = "权限标识不能为空")
    @Size(min = 2, max = 50, message = "权限标识长度限定2到20")
    private String permissionFlag;

    /**
     * 功能类型
     */
    @NotNull(message = "功能类型不能为空")
    private Integer type;

    /**
     * 后端API地址
     */
    private String url;

    public static EmployerFunction buildDto(FunctionVO vo) {
        EmployerFunction function = new EmployerFunction();
        function.setId(vo.getId());
        function.setName(vo.getName());
        function.setNumber(vo.getNumber());
        function.setParentId(vo.getParentId());
        function.setPermissionFlag(vo.getPermissionFlag());
        function.setType(vo.getType());
        function.setUrl(vo.getUrl());
        return function;
    }

    public static FunctionVO buildVo(EmployerFunction function) {
        FunctionVO vo = new FunctionVO();
        vo.setId(function.getId());
        vo.setName(function.getName());
        vo.setNumber(function.getNumber());
        vo.setParentId(function.getParentId());
        vo.setPermissionFlag(function.getPermissionFlag());
        vo.setType(function.getType());
        vo.setUrl(function.getUrl());
        return vo;
    }
}
