package com.zhixianghui.web.employer.controller.merchant;


import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.LimitUtil;
import com.zhixianghui.facade.merchant.entity.MerchantExpressInfo;
import com.zhixianghui.facade.merchant.service.MerchantExpressInfoFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.vo.merchant.MerchantExpressVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 商户邮寄信息
 * Author: huangshunsi
 * Date: 2020.12.25
 * Time: 11:56
 * Description:
 */
@Slf4j
@RequestMapping("merchantExpressInfo")
@RestController
public class MerchantExpressInfoController {

    @Reference
    private MerchantExpressInfoFacade merchantExpressInfoFacade;

    /**
     * 获取商户开票信息
     *
     * @return .
     */
    @RequestMapping("getExpressInfo")
    public RestResult<MerchantExpressInfo> getExpressInfo(@CurrentMchNo String mchNo) {
        MerchantExpressInfo info = merchantExpressInfoFacade.getByMchNo(mchNo);
        return RestResult.success(info);
    }

    /**
     * 更新商户邮寄信息
     *
     * @return .
     */
    @RequestMapping("updateExpressInfo")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "更新商户邮寄信息")
    @Permission("merchant:expressInfo:update")
    public RestResult<String> updateExpressInfo(@CurrentMchNo String mchNo, @RequestBody MerchantExpressVo vo) {
        MerchantExpressInfo info = merchantExpressInfoFacade.getByMchNo(mchNo);
        if(info == null){
            info = new MerchantExpressInfo();
            info.setCreateTime(new Date());
            info.setVersion(0);
            info.setMchNo(mchNo);
            fillMerchantExpressInfo(info, vo);
            merchantExpressInfoFacade.insert(info);
        } else {
            fillMerchantExpressInfo(info, vo);
            merchantExpressInfoFacade.update(info);
        }
        return RestResult.success("更新商户邮寄信息成功");
    }

    private void fillMerchantExpressInfo(MerchantExpressInfo info, MerchantExpressVo vo){
        info.setUpdateTime(new Date());
        info.setConsignee(vo.getConsignee());
        info.setTelephone(vo.getTelephone());
        info.setProvince(vo.getProvince());
        info.setCity(vo.getCity());
        info.setCounty(vo.getCounty());
        info.setAddress(vo.getAddress());
    }
}
