package com.zhixianghui.web.employer.vo.invoice;

import com.zhixianghui.common.statics.dto.common.PageQueryVo;
import com.zhixianghui.common.statics.enums.invoice.InvoiceAmountTypeEnum;
import com.zhixianghui.common.util.validator.EnumValue;
import com.zhixianghui.facade.trade.enums.InvoiceSourceEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 查询待开发票批次订单vo
 * <AUTHOR>
 * @date 2020/12/28
 **/
@Data
public class QueryWaitIssueOrderVo extends PageQueryVo {
    private static final long serialVersionUID = 6340459432023052376L;
    @NotEmpty(message = "代征主体编号 不能为空")
    private String mainstayMchNo;
    @NotNull(message = "申请方式 不能为空")
    @EnumValue(intValues = {1,2}, message = "申请方式 有误")
    private Integer applyType;
    @NotEmpty(message = "开票交易时间段起始时间 不能为空")
    @Length(min = 10, max = 10, message = "开票交易时间段起始时间 格式有误")
    private String tradeCompleteDayBegin;
    @NotEmpty(message = "开票交易时间段终止时间 不能为空")
    @Length(min = 10, max = 10, message = "开票交易时间段终止时间 格式有误")
    private String tradeCompleteDayEnd;
    @NotEmpty(message = "请输入要查询的产品编号")
    private String productNo;
    private Integer amountType = InvoiceAmountTypeEnum.ORDER_AMOUNT.getValue();

    private String jobId;

    private Integer source=InvoiceSourceEnum.ON_LINE.getCode();

    /**
     * 自由职业者服务编号
     */
    private String workCategoryCode;
}
