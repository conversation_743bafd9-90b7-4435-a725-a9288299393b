package com.zhixianghui.web.employer.vo.merchant;



import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2020/11/9
 **/
@Data
public class BankAccountVo {
    /**
     * 商户编号
     */
    @NotEmpty(message = "商户编号不能为空")
    private String mchNo;

    /**
     * 银行账户
     */
    @NotEmpty(message = "银行账户不能为空")
    @Length(max = 50, message = "银行账户长度不能超过50")
    private String accountNo;

    /**
     * 银行名称
     */
    @NotEmpty(message = "银行名称不能为空")
    @Length(max = 50, message = "银行名称长度不能超过50")
    private String bankName;

    /**
     * 银行行号
     */
    @NotEmpty(message = "银行行号不能为空")
    @Length(max = 50, message = "银行行号长度不能超过50")
    private String bankChannelNo;
}
