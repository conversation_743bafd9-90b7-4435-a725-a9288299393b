package com.zhixianghui.web.employer.vo.order.res;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-11-12 19:48
 **/
@Data
public class HangOrderItemResVo {
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 平台流水号
     */
    private String platTrxNo;

    /**
     * 代征主体编号
     */
    private String mainstayNo;

    /**
     * 代征主体名称
     */
    private String mainstayName;

    /**
     * 通道类型(发放方式)
     */
    private Integer channelType;

    /**
     * 收款账户
     */
    private String receiveAccountNo;

    /**
     * 持卡人姓名
     */
    private String receiveName;

    /**
     * 持卡人身份证号
     */
    private String receiveIdCardNo;

    /**
     * 订单明细实发金额
     */
    private BigDecimal orderItemNetAmount;

    /**
     * 订单明细代征主体服务费
     */
    private BigDecimal orderItemFee;

    /**
     * 订单明细(总)金额
     */
    private BigDecimal orderItemAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单明细状态
     */
    private Integer orderItemStatus;

    /**
     * 错误描述
     */
    private String errorDesc;

    private Integer authStatus;

    private Integer signStatus;
}
