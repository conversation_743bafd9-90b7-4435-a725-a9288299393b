package com.zhixianghui.web.employer.controller.flow;

import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.facade.merchant.entity.user.agent.AgentRole;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.service.agent.AgentRoleFacade;
import com.zhixianghui.facade.merchant.service.employer.EmployerRoleFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.merchant.vo.agent.AgentStaffVO;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/7/5 17:29
 */
public class CommonFlow {

    private static final String CRM_FLAG = "CRM观察员";

    public boolean crmRole(String mchNo, EmployerRoleFacade employerRoleFacade) {
        List<EmployerRole> roleList = employerRoleFacade.listAll(mchNo);
        if (CollectionUtils.isEmpty(roleList)) {
            return false;
        }
        for (EmployerRole employerRole : roleList) {
            if (CRM_FLAG.equals(employerRole.getName())) {
                return true;
            }
        }
        return false;
    }

    public boolean admin(EmployerStaffVO employerStaffVO, EmployerRoleFacade employerRoleFacade) {
        if (employerStaffVO.getType() == PortalStaffTypeEnum.ADMIN.getValue()) {
            return true;
        }
        return crmRole(employerStaffVO.getMchNo(), employerRoleFacade);
    }
}
