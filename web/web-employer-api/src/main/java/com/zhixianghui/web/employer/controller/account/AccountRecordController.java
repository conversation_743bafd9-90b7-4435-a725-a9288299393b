package com.zhixianghui.web.employer.controller.account;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.export.ReportTypeEnum;
import com.zhixianghui.common.statics.enums.user.pms.SystemTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.config.DataDictionary;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.ReportAccountHistoryFacade;
import com.zhixianghui.facade.common.vo.ReportAccountHistoryVo;
import com.zhixianghui.facade.data.dto.AccountDetailQueryDto;
import com.zhixianghui.facade.data.entity.CkAccountDetail;
import com.zhixianghui.facade.data.service.CkAccountDetailFacade;
import com.zhixianghui.facade.export.entity.ExportRecord;
import com.zhixianghui.facade.export.service.ExportRecordFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.annotation.Logger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AccountRecordController
 * @Description TODO
 * @Date 2022/12/2 15:32
 */
@RestController
@RequestMapping("accountRecord")
@Slf4j
public class AccountRecordController {

    @Reference
    private ReportAccountHistoryFacade reportAccountHistoryFacade;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    private OrderFacade orderFacade;
    @Reference
    private CkAccountDetailFacade ckAccountDetailFacade;
    @Reference
    private ExportRecordFacade exportRecordFacade;
    @Reference
    private DataDictionaryFacade dataDictionaryFacade;

    /**
     * 获取商户账户记录
     * @param employerNo
     * @return
     */
    @GetMapping("getAccountRecord")
    public RestResult getAccountRecord(@RequestParam("mainstayNo") String mainstayNo, @CurrentMchNo String employerNo){
        List<ReportAccountHistoryVo> list =  reportAccountHistoryFacade.getMerchantAccount(employerNo,mainstayNo);
        //构建返回参数
        if (list.size() == 0){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂无可切换的支付账户");
        }
        List<String> channelMchNoList = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(employerNo,mainstayNo).stream()
                .map(x->x.getSubMerchantNo()).collect(Collectors.toList());

        Map<Integer,List<ReportAccountHistoryVo>> map = list.stream().map(x->{
            if (channelMchNoList.contains(x.getChannelMerchantNo())){
                x.setIsCurrent(YesNoCodeEnum.YES.getValue());
            }else{
                x.setIsCurrent(YesNoCodeEnum.NO.getValue());
            }
            x.setChannelMerchantNo("");
            return x;
        }).collect(Collectors.groupingBy(ReportAccountHistoryVo::getPayChannelType));
        return RestResult.success(map);
    }

    @PostMapping("changeAccount")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "切换支付账户")
    public RestResult changeAccount(@RequestParam Long id, @CurrentMchNo String employerNo, @CurrentStaffVo EmployerStaffVO staffVO){
        employerAccountInfoFacade.changeAccount(id,employerNo,staffVO.getName());
        return RestResult.success("支付账户已切换");
    }

    @PostMapping("listAcctDetail")
    public RestResult<Page<CkAccountDetail>> listAcctDetail(@RequestBody AccountDetailQueryDto dto, @RequestBody Page page,@CurrentStaffVo EmployerStaffVO staffVO) {
        dto.setEmployerNo(staffVO.getMchNo());
        final Page<CkAccountDetail> accountDetails = ckAccountDetailFacade.listBy(BeanUtil.toMap(dto), page);
        return RestResult.success(accountDetails);
    }

    @Permission("report:employerAccountInfo:view")
    @PostMapping("exportCkAcctDetail")
    public RestResult<String> exportCkAcctDetail(@RequestBody AccountDetailQueryDto dto,@CurrentStaffVo EmployerStaffVO staffVO) {
        dto.setEmployerNo(staffVO.getMchNo());
        String fileNo = exportRecordFacade.genFileNo();
        ExportRecord record = ExportRecord.newDefaultInstance();
        record.setFileNo(fileNo);
        record.setOperatorLoginName(staffVO.getPhone());
        record.setSystemType(SystemTypeEnum.MERCHANT_MANAGEMENT.getValue());
        record.setFileName(ReportTypeEnum.CK_ACCTDETAIL_EXPORT.getFileName());
        record.setReportType(ReportTypeEnum.CK_ACCTDETAIL_EXPORT.getValue());
        record.setMchNo(staffVO.getMchNo());
        record.setParamJson(JsonUtil.toString(dto));

        DataDictionary dataDictionary = dataDictionaryFacade.getDataDictionaryByName(ReportTypeEnum.CK_ACCTDETAIL_EXPORT.getDataName());
        if (dataDictionary == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("导出字段的字典未配置,请在后台配置");
        }
        List<DataDictionary.Item> dataInfo = dataDictionary.getItemList();
        dataInfo.forEach(x -> {
            ExportRecord.FieldInfo info = new ExportRecord.FieldInfo();
            info.setDataName(x.getFlag());
            info.setFieldCode(x.getCode());
            info.setFieldDesc(x.getDesc());
            record.getFieldInfoList().add(info);
        });

        exportRecordFacade.insert(record);
        return RestResult.success("成功创建导出任务，请稍后查看");
    }
}
