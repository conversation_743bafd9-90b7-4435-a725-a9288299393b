package com.zhixianghui.web.employer.biz.order;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.EasyExcel;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.service.DataDictionaryFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.riskcontrol.service.RiskControlFacade;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.employer.biz.async.AsyncBiz;
import com.zhixianghui.web.employer.biz.order.excel.BankGrantRow;
import com.zhixianghui.web.employer.biz.order.excel.TestGrantDataListener;
import com.zhixianghui.web.employer.exception.MchOrderNoException;
import com.zhixianghui.web.employer.utils.MultipartFileUtil;
import com.zhixianghui.web.employer.vo.order.CountVo;
import com.zhixianghui.web.employer.vo.order.req.OrderAcceptReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName AbstractOrderHandler
 * @Description TODO
 * @Date 2022/6/28 9:58
 */
@Slf4j
public abstract class AbstractOrderHandler implements OrderHandlerInterface{

    @Reference
    protected RiskControlFacade riskControlFacade;

    @Reference
    protected EmployerMainstayRelationFacade employerMainstayRelationFacade;

    @Reference
    protected EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    protected OrderFacade orderFacade;

    @Reference
    protected SequenceFacade sequenceFacade;

    @Reference
    protected MerchantQueryFacade merchantQueryFacade;

    @Autowired
    protected AsyncBiz asyncBiz;

    @Autowired
    protected RedisClient redisClient;

    @Reference
    protected DataDictionaryFacade dictionaryFacade;

    @Autowired
    protected OrderItemBiz orderItemBiz;

    @Reference
    protected NotifyFacade notifyFacade;

    @Autowired
    protected TradeCheckBiz tradeCheckBiz;

    protected List<String> phoneMustMainstays;


    public Map<String,String> batchOrderUpload(OrderAcceptReqVo orderAcceptReqVo, EmployerOperatorVO operator, String employerNo) throws Exception {

        //设置产品名称
        orderAcceptReqVo.setProductName(ProductNoEnum.getEnum(orderAcceptReqVo.getProductNo()).getDesc());

        log.info("批次订单导入。mchNo:{}, operator:{}, orderReqVo:{}",employerNo,operator.getPhone(), orderAcceptReqVo);
        //校验商户状态
        tradeCheckBiz.checkMchInfo(employerNo,orderAcceptReqVo.getMainstayNo());

        if (!riskControlFacade.grantValidateExistRule(employerNo,orderAcceptReqVo.getMainstayNo())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应的规则，请联系平台管理员");
        }
        //查询代征主体


        //检查代征关系
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo, orderAcceptReqVo.getMainstayNo());
        if (employerMainstayRelation == null || employerMainstayRelation.getStatus() == OpenOffEnum.OFF.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征关系未激活,请联系客服");
        }
        //校验产品开通
        Merchant mainstay = merchantQueryFacade.getByMchNo(orderAcceptReqVo.getMainstayNo());
        if (mainstay == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体不存在");
        }

        //用工企业账号信息
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo, orderAcceptReqVo.getMainstayNo(), orderAcceptReqVo.getChannelType());
        if(employerAccountInfo == null || employerAccountInfo.getStatus().equals(OpenOffEnum.OFF.getValue())
                || StringUtils.isBlank(employerAccountInfo.getPayChannelNo())
                || StringUtils.isBlank(employerAccountInfo.getSubMerchantNo())){
            boolean checkChannel=employerAccountInfo.getChannelType()!= ChannelTypeEnum.WENXIN.getValue();
            if(checkChannel&&StringUtils.isBlank(employerAccountInfo.getParentMerchantNo())){
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道配置信息不对，请联系客服！");
            }
        }

        // 检查上传文件
        MultipartFile file = orderAcceptReqVo.getFile();
        if(!MultipartFileUtil.checkFileSize(file.getSize(), FILE_SIZE, UNIT)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件大小超过限制");
        }

        if(!file.getOriginalFilename().endsWith(SUFFIX)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("FileName:" + file.getOriginalFilename() +"文件格式有误，必须为xlsx格式");
        }
        File excelFile = MultipartFileUtil.transfer2File(file);

        List<BankGrantRow> list = new ArrayList<>();
        EasyExcel.read(excelFile, BankGrantRow.class, new TestGrantDataListener(HEAD_ROW_NUMBER, list)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        log.info("[{}]==>检查文件具体内容通过", employerNo);
        if (CollectionUtils.isEmpty(list) || emptyContent(list)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提交的发放名单不能为空");
        }

        Date batchTime = new Date();
        String batchNo = getBatchNo(employerNo,batchTime);

        //初始化订单信息
        String logFlag = String.join("-",employerNo,batchNo);
        Order order = fillOrder(batchNo, orderAcceptReqVo,employerAccountInfo,batchTime,mainstay.getMchName());
        Long id = orderFacade.insert(order);
        order.setId(id);
        log.info("[{}]==>批次插入成功", logFlag);
        asyncBiz.doAsync(()->{
            doAsync(excelFile,order);
            return true;
        });
        //返回结果
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("platBatchNo", order.getPlatBatchNo());
        log.info("[{}]==>返回结果：{}", logFlag, JsonUtil.toString(paramMap));
        //存入订单批次
        Date expireTime = DateUtil.parseJodaDateTime(order.getCreateTime()).plusHours(7*24).toDate();
        redisClient.zadd(TradeConstant.ORDER_BATCH_ZSET_KEY,expireTime.getTime(),batchNo);
        return paramMap;
    }

    protected boolean emptyContent(List<BankGrantRow> list) {
        for (BankGrantRow item : list) {
            if (StringUtils.isNotBlank(item.getReceiveIdCardNo())) {
                return false;
            }
        }
        return true;
    }

    private Order fillOrder(String batchNo, OrderAcceptReqVo orderAcceptReqVo, EmployerAccountInfo employerAccountInfo, Date batchTime,String mainstayName) {
        String fileName = orderAcceptReqVo.getFile().getOriginalFilename();
        fileName = fileName.substring(0, fileName.lastIndexOf("."));

        Order order = new Order();
        order.setCreateDate(batchTime);
        order.setCreateTime(batchTime);
        order.setUpdateTime(batchTime);
        order.setMchBatchNo(batchNo);
//        order.setBatchName(String.join("-", DateUtil.formatDate(new Date()),employerAccountInfo.getEmployerName()));
        order.setBatchName(fileName);
        order.setPlatBatchNo(batchNo);
        order.setEmployerNo(employerAccountInfo.getEmployerNo());
        order.setEmployerName(employerAccountInfo.getMchName());
        order.setMainstayNo(orderAcceptReqVo.getMainstayNo());
        order.setMainstayName(mainstayName);
        order.setChannelType(orderAcceptReqVo.getChannelType());
        order.setPayChannelNo(employerAccountInfo.getPayChannelNo());
        order.setChannelName(employerAccountInfo.getPayChannelName());
        order.setBatchStatus(OrderStatusEnum.IMPORTING.getValue());
        order.setWorkCategoryCode(orderAcceptReqVo.getWorkCategoryCode());
        order.setWorkCategoryName(orderAcceptReqVo.getWorkCategoryName());
        order.setServiceDesc(orderAcceptReqVo.getServiceDesc());
        order.setLaunchWay(LaunchWayEnum.EMPLOYER.getValue());
        order.setProductNo(orderAcceptReqVo.getProductNo());
        order.setProductName(orderAcceptReqVo.getProductName());
        order.setJobId(orderAcceptReqVo.getJobId());
        order.setJobName(orderAcceptReqVo.getJobName());
        return order;
    }

    private void doAsync(File excelFile, Order order) {
        try{
            CountVo countVo = CountVo.builder()
                    .requestCount(0)
                    .failCount(0)
                    .requestNetAmount(BigDecimal.ZERO)
                    .failNetAmount(BigDecimal.ZERO).build();

            String phoneMustMainstayConfig = dictionaryFacade.getSystemConfig("PHONE_MUST_MAINSTAY");
            if (StringUtils.isNotBlank(phoneMustMainstayConfig)) {
                this.phoneMustMainstays = ListUtil.toList(phoneMustMainstayConfig.split(","));
            }
            //excel处理
            this.handleExcel(excelFile,order,countVo);
            //受理
            orderFacade.startAccept(order);
            log.info("[{}]==>批次受理初始调用结束", order.getPlatBatchNo());
        }catch (BizException e){
            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);
            order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
            order.setErrorDesc(e.getErrMsg());
            order.setErrorCode(String.valueOf(e.getSysErrorCode()));
            orderFacade.update(order);
            orderFacade.cancelOrderItem(order.getPlatBatchNo());
        }catch (MchOrderNoException e){
            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);

            String errorMsg = (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > 200) ? e.getMessage().substring(0, 199) : e.getMessage();
            order.setBatchStatus(OrderStatusEnum.IMPORT_FAIL.getValue());
            order.setErrorDesc(errorMsg);

            orderFacade.update(order);

            orderFacade.cancelOrderItem(order.getPlatBatchNo());
        }catch (Exception e){

            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);

            String errorMsg = (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > 200) ? e.getMessage().substring(0, 199) : e.getMessage();

            if (errorMsg != null && errorMsg.contains("MchOrderNoException")) {
                order.setBatchStatus(OrderStatusEnum.IMPORT_FAIL.getValue());
            }else {
                order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
            }
            order.setErrorDesc(errorMsg);

            orderFacade.update(order);
            orderFacade.cancelOrderItem(order.getPlatBatchNo());
        }finally {
            log.info("批次[{}] 文件使用完毕,进行删除删除,file:{}",order.getPlatBatchNo(),excelFile.getAbsolutePath());
            boolean isDel = FileUtils.deleteDir(excelFile);
            if(isDel){
                log.info("批次[{}] 文件删除成功",order.getPlatBatchNo());
            }else{
                log.info("批次[{}] 文件删除失败,需手动处理",order.getPlatBatchNo());
            }
        }
    }
}
