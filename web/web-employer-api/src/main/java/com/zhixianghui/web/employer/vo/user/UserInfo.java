package com.zhixianghui.web.employer.vo.user;

import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.vo.permission.FunctionVO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class UserInfo {

    /**
     * 当前登录的操作员
     */
    @NotNull
    private EmployerOperatorVO operator;

    /**
     * 当前员工
     */
    private EmployerStaffVO staff;

    /**
     * 当前选择的商户
     */
    private String mchNo;

    /**
     * 关联的功能菜单
     */
    private List<FunctionVO> functions;
}
