package com.zhixianghui.web.employer.controller.merchant;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantFileTypeEnum;
import com.zhixianghui.common.statics.enums.merchant.MerchantQuoteStatusEnum;
import com.zhixianghui.common.statics.enums.report.ChannelTypeEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.banklink.service.account.AccountQueryFacade;
import com.zhixianghui.facade.banklink.vo.account.AmountQueryDto;
import com.zhixianghui.facade.common.entity.approvalflow.ApprovalFlow;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.common.enums.FlowStatus;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.ApprovalFlowFacade;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.EmployerMainstayRelationFacade;
import com.zhixianghui.facade.common.vo.UpdateExtInfoVo;
import com.zhixianghui.facade.flow.dto.FlowStartDto;
import com.zhixianghui.facade.flow.entity.CommonFlow;
import com.zhixianghui.facade.flow.enums.FlowTypeEnum;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.flow.vo.req.ProcessVo;
import com.zhixianghui.facade.merchant.entity.*;
import com.zhixianghui.facade.merchant.service.*;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.merchant.vo.MerchantEmployerMainAuthVo;
import com.zhixianghui.facade.merchant.vo.merchant.ListMerchantProductVo;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.biz.approval.ApprovalFlowBiz;
import com.zhixianghui.web.employer.vo.merchant.EmployerAccountInfoVo;
import com.zhixianghui.web.employer.vo.merchant.SubChannelInfoVo;
import com.zhixianghui.web.employer.vo.order.res.MainstayResVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用工企业信息
 * <AUTHOR>
 * @date 2020/9/28
 **/
@RestController
@RequestMapping("merchantEmployer")
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EmployerController {

    @Reference
    private EmployerMainstayRelationFacade employerMainstayRelationFacade;
    @Reference
    private MerchantEmployerFacade merchantEmployerFacade;
    @Reference
    private MerchantQueryFacade merchantQueryFacade;
    @Reference
    private MerchantSalerFacade merchantSalerFacade;
    @Reference
    private MerchantFacade merchantFacade;
    @Reference
    private ApprovalFlowFacade approvalFlowFacade;
    @Reference
    private MerchantCacheFacade merchantCacheFacade;
    @Reference
    private AccountQueryFacade accountQueryFacade;
    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;
    @Reference
    private MerchantEmployerCooperateFacade employerCooperateFacade;
    @Reference
    private MerchantEmployerPositionFacade positionFacade;
    @Reference
    private MerchantEmployerQuoteFacade quoteFacade;
    @Reference
    private MerchantFileFacade fileFacade;
    @Reference
    private FlowFacade flowFacade;
    @Reference
    private MerchantBankAccountFacade merchantBankAccountFacade;
    @Reference
    private MerchantEmployerMainFacade merchantEmployerMainFacade;
    @Reference
    private AgreementFacade agreementFacade;

    private final ApprovalFlowBiz approvalFlowBiz;

    /**
     * 主体认证信息
     *
     * @return .
     */
    @RequestMapping("mainAuth")
    @Permission("merchant:employer:mainAuth")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "主体认证")
    public RestResult<Map<String, Object>> mainAuth(@Valid @RequestBody FlowStartDto<MerchantEmployerMainAuthVo> flowStartDto, @CurrentStaffVo EmployerStaffVO staffVO,
                                                    @CurrentMchNo String mchNo) {
        MerchantEmployerMainAuthVo authVo = flowStartDto.getExtObj();
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        if(!Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.FAIL.getValue())
                && !Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.UN_AUTH.getValue())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许提交主体认证");
        }


        /**
         * 检查代征关系是否建立
         */
        final Map<String, List<Map<String,Object>>> accounts = authVo.getAccounts();
        accounts.forEach((mainstayNo, value) -> {
            // 查询代征关系表
            log.info("检查代征关系是否建立:{}-{}", mchNo, mainstayNo);
            final EmployerMainstayRelation emRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(mchNo, mainstayNo);
            if (emRelation == null) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征关系未建立:代征主体编号["+mainstayNo+"]");
            }
        });

        authVo.setMchNo(mchNo);
        authVo.setMerchantType(merchant.getMerchantType());
        authVo.setMchName(merchant.getMchName());
        authVo.setContactName(merchant.getContactName());
        authVo.setContactPhone(merchant.getContactPhone());
        authVo.setOperatorLoginName(staffVO.getPhone());

        // 校验字段
        merchantEmployerFacade.validMainAuthVo(authVo);
        authVo.getPersonnels().forEach(personnel->{
            personnel.setMchNo(authVo.getMchNo());
            personnel.setMchName(authVo.getMchName());
            personnel.setUpdator(staffVO.getOperatorName());
        });
        log.info("主体认证：{}", JsonUtil.toString(authVo));
        //构建流程参数
        //当前用户
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setUserId(staffVO.getId());
        flowUserVo.setUserName(StringUtil.isEmpty(staffVO.getName()) ? staffVO.getMchNo() : staffVO.getName());
        flowUserVo.setPlatform(PlatformSource.MERCHANT.getValue());
        flowUserVo.setNo(staffVO.getMchNo());
        //流程主体参数
        ProcessVo processVo = new ProcessVo();
        processVo.setBusinessKey(authVo.getMchNo());
        //枚举类name为流程key
        processVo.setProcessDefinitionKey(FlowTypeEnum.MERCHANT_MCH_MAIN_AUTH.name());
        processVo.setFlowTopicType(FlowTypeEnum.MERCHANT_MCH_MAIN_AUTH.getFlowTopicType());
        processVo.setFlowTopicName(String.join("-",FlowTypeEnum.MERCHANT_MCH_MAIN_AUTH.getDesc(),authVo.getMchName()));
        processVo.setExtInfo(JsonUtil.toString(authVo));
        processVo.setRemark(flowStartDto.getRemark());
        //设置流程变量
        flowStartDto.getCondition().put("mchNo",mchNo);
        CommonFlow commonFlow = flowFacade.startProcessByProcessDefinitionKey(processVo,flowUserVo,flowStartDto.getParticipant(),flowStartDto.getCondition());

        // 商户状态更新为审核中
        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
        merchant.setUpdator(staffVO.getName());
        merchant.setUpdateTime(new Date());
        merchantFacade.update(merchant);

        Map<String, Object> result = new HashMap<>();
        result.put("commonFlowId", commonFlow.getId());
        result.put("createTime", DateUtil.formatDateTime(commonFlow.getCreateTime()));
        result.put("submitName", commonFlow.getInitiatorName());
        return RestResult.success(result);
    }

//    /**
//     * 主体认证信息
//     *
//     * @param authVo     .
//     * @return .
//     */
//    @RequestMapping("mainAuth")
//    @Permission("merchant:employer:mainAuth")
//    @Logger(type = OperateLogTypeEnum.MODIFY, action = "主体认证")
//    public RestResult<Map<String, Object>> mainAuth(@Valid @RequestBody MerchantEmployerMainAuthVo authVo, @CurrentStaffVo EmployerStaffVO staffVO,
//                                                    @CurrentMchNo String mchNo) {
//        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
//        if(!Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.FAIL.getValue())
//                && !Objects.equals(merchant.getAuthStatus(), AuthStatusEnum.UN_AUTH.getValue())){
//            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许提交主体认证");
//        }
//
//        authVo.setMchNo(mchNo);
//        authVo.setMerchantType(merchant.getMerchantType());
//        authVo.setMchName(merchant.getMchName());
//        authVo.setContactName(merchant.getContactName());
//        authVo.setContactPhone(merchant.getContactPhone());
//        authVo.setOperatorLoginName(staffVO.getPhone());
//
//        // 校验字段
//        merchantEmployerFacade.validMainAuthVo(authVo);
//        log.info("主体认证：{}", JsonUtil.toString(authVo));
//        // 保存主体认证审核记录
//        ApprovalFlow approvalFlow = new ApprovalFlow();
//        approvalFlow.setVersion(0);
//        approvalFlow.setStepNum(0);
//        approvalFlow.setInitiatorId(staffVO.getId());
//        approvalFlow.setInitiatorName(staffVO.getName());
//        approvalFlow.setFlowTopicType(FlowTopicType.MERCHANT_VERIFY.getValue());
//        approvalFlow.setFlowTopicName(String.join("-",FlowTopicType.MERCHANT_VERIFY.getDesc(),authVo.getMchName()));
//        approvalFlow.setCreateTime(new Date());
//        approvalFlow.setUpdateTime(new Date());
//        approvalFlow.setStatus(FlowStatus.PENDING.getValue());
//        approvalFlow.setExtInfo(JsonUtil.toString(authVo));
//        approvalFlow.setPlatform(PlatformSource.MERCHANT.getValue());
//
//        ApprovalInfoVo infoVo = approvalFlowFacade.createFlow(approvalFlow);
//
//        // 商户状态更新为审核中
//        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
//        merchant.setUpdator(staffVO.getName());
//        merchant.setUpdateTime(new Date());
//        merchantFacade.update(merchant);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("id", infoVo.getId());
//        result.put("createTime", DateUtil.formatDateTime(approvalFlow.getCreateTime()));
//        result.put("submitName", staffVO.getName());
//        return RestResult.success(result);
//    }



    /**
     * 商户信息查询
     *
     * @return .
     */
    @RequestMapping("getMerchant")
    public RestResult<Merchant> getMerchant(@CurrentMchNo String mchNo) {
        if(StringUtil.isEmpty(mchNo)){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前未绑定商户");
        }
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        return RestResult.success(merchant);
    }

    @GetMapping("listQuoteByEmployerNo")
    public RestResult<List<MerchantEmployerQuote>> listQuoteByEmployerNo(@CurrentMchNo String mchNo) {
        Map<String,Object> map = new HashMap<>();
        map.put("mchNo",mchNo);
        map.put("status", MerchantQuoteStatusEnum.ACTIVE.getValue());
        List<MerchantEmployerQuote> quoteList = quoteFacade.listBy(map);
        return RestResult.success(quoteList);
    }

    /**
     *  查询商户主体认证流程
     * @param mchNo 商户编号
     * @param staffVO 操作人vo
     * @return 审批流信息
     */
    @RequestMapping("getApprovalFlow")
    @Permission("merchant:employer:mainAuth")
    public RestResult<Map<String, Object>> getApprovalFlow(
            @CurrentMchNo String mchNo, @CurrentStaffVo EmployerStaffVO staffVO) {

        Map<String,Object> paramMap = Maps.newHashMap();
        //一个商户的主体认证非终态审批需保证只有一条
        paramMap.put("mchNo",mchNo);
        paramMap.put("platform",PlatformSource.MERCHANT.getValue());
        paramMap.put("status",FlowStatus.PENDING.getValue());
        ApprovalFlow approvalFlow = approvalFlowFacade.getOne(paramMap);
        if(approvalFlow == null){
            throw CommonExceptions.DB_AFFECT_ROW_NOT_MATCH.newWithErrMsg("该商户主体认证审批不存在");
        }

        Map<String,Object> map = Maps.newHashMap();
        map.put("approvalFlow",approvalFlow);
        map.put("canEdit", approvalFlow.getInitiatorId().equals(staffVO.getId()));
        return RestResult.success(map);
    }

    /**
     * 主体认证审批编辑
     * @param updateExtInfoVo
     * @param staffVO
     * @return
     */
    @RequestMapping("mainAuthApprovalEdit")
    @Permission("merchant:employer:mainAuth")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "主体认证审批编辑")
    public RestResult<Map<String, Object>> mainAuthApprovalEdit(@Validated @RequestBody UpdateExtInfoVo updateExtInfoVo, @CurrentStaffVo EmployerStaffVO staffVO,@CurrentMchNo String mchNo) {
        String newAuthStr = updateExtInfoVo.getExtInfo();
        MerchantEmployerMainAuthVo newAuth = JsonUtil.toBean(newAuthStr, MerchantEmployerMainAuthVo.class);

        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        newAuth.setMchNo(mchNo);
        newAuth.setMchName(merchant.getMchName());
        newAuth.setContactName(merchant.getContactName());
        newAuth.setContactPhone(merchant.getContactPhone());
        newAuth.setOperatorLoginName(staffVO.getPhone());
        newAuth.setMerchantType(merchant.getMerchantType());
        Merchant mch = merchantCacheFacade.getByMchNo(newAuth.getMchNo());
        if(Objects.equals(mch.getAuthStatus(), AuthStatusEnum.SUCCESS.getValue())){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("当前认证状态不允许编辑提交主体认证");
        }

        // 校验字段
        merchantEmployerFacade.validMainAuthVo(newAuth);
        log.info("主体认证审批编辑：{}", JsonUtil.toString(newAuth));

        // 更新主体认证审核记录
        updateExtInfoVo.setExtInfo(JsonUtil.toString(newAuth));
        approvalFlowBiz.updateExtInfo(updateExtInfoVo,staffVO);

        // 商户状态更新为审核中
        merchant.setAuthStatus(AuthStatusEnum.APPROVAL.getValue());
        merchant.setUpdator(staffVO.getName());
        merchant.setUpdateTime(new Date());
        merchantFacade.update(merchant);

        Long id = updateExtInfoVo.getApprovalFlowId();
        ApprovalFlow approvalFlow = approvalFlowBiz.getById(id,staffVO.getId());
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("createTime", DateUtil.formatDateTime(approvalFlow.getCreateTime()));
        result.put("submitName", approvalFlow.getInitiatorName());
        return RestResult.success(result);
    }

    @GetMapping("listAllMainstayByEmployerNo")
    public RestResult<List<MainstayResVo>> listAllMainstayByEmployerNo(@CurrentMchNo String employerNo){
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("employerNo",employerNo);
        paramMap.put("sortColumns","STATUS,ID asc");
        List<EmployerMainstayRelation> list = employerMainstayRelationFacade.listBy(paramMap);
        List<MainstayResVo> result = list.stream().map(
                rel ->{
                    MainstayResVo mainstayResVo = new MainstayResVo();
                    BeanUtils.copyProperties(rel,mainstayResVo);
                    return mainstayResVo;
                }
        ).collect(Collectors.toList());
        return RestResult.success(result);
    }

    /**
     * 查询用工企业在不同供应商下的账户余额
     * @param mainstayNo
     * @param employerNo
     * @return
     */
    @Logger(type = OperateLogTypeEnum.QUERY, action="查询用工企业在不同供应商下的账户余额")
    @Permission("merchant:amount:view")
    @PostMapping("getAmountByEmployerNoAnyMainstayNo")
    public RestResult<Map<String,Object>> getAmountByEmployerNoAnyMainstayNo(@RequestParam(required = false) String mainstayNo,@CurrentMchNo String employerNo){
        Map<String,Object> resultMap = new HashMap<>();
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mainstayNo",mainstayNo);
        paramMap.put("employerNo",employerNo);
        List<EmployerMainstayRelation> employerMainstayRelationList = employerMainstayRelationFacade.listBy(paramMap);
        List<Map<String,Object>> amountList = new ArrayList<>();
        employerMainstayRelationList.forEach(employerMainstayRelation -> {
            EmployerAccountInfoVo employerAccountInfoVo = getByMainstayNoAndEmployerNo(employerMainstayRelation.getMainstayNo(),employerNo);
            Map<String,Object> infoMap = new HashMap<>();
            infoMap.put("mainstayName",employerAccountInfoVo.getMainstayName());
            infoMap.put("mainstayNo",employerAccountInfoVo.getMainstayNo());
            infoMap.put("status",employerMainstayRelation.getStatus());
            employerAccountInfoVo.getChannelInfos().forEach(subChannelInfoVo -> {
                Integer channelType = subChannelInfoVo.getChannelType();
                String amount;
                try {
                    if (subChannelInfoVo.getStatus().equals(OpenOffEnum.OFF.getValue())){
                        amount = "未开通";
                    }else {
                        amount = getAmount(subChannelInfoVo,employerAccountInfoVo,channelType);
                        if(StringUtils.isEmpty(amount)){
                            log.error("[{}]余额查询异常,通道：{}",employerNo,subChannelInfoVo.getChannelType());
                            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询异常");
                        }
                    }
                }catch (Exception e){
                    amount = "异常";
                }
                if (channelType.equals(ChannelTypeEnum.BANK.getValue())) {
                    infoMap.put("bankAmount",amount);
                }else if (channelType.equals(ChannelTypeEnum.ALIPAY.getValue())){
                    infoMap.put("aliPayAmount",amount);
                }else if (channelType.equals(ChannelTypeEnum.WENXIN.getValue())){
                    infoMap.put("weixinAmount",amount);
                }
            });
            amountList.add(infoMap);
        });
        resultMap.put("amountList",amountList);
        return RestResult.success(resultMap);
    }

    /**
     * 查询余额
     * @param mainstayNo 供应商编号
     * @param employerNo 用工企业编号
     * @return 用工企业账户
     */
    @Permission("merchant:amount:view")
    @PostMapping("getAmountByMainstayNoAndEmployerNo")
    public RestResult<Map<String,Object>> getAmountByMainstayNoAndEmployerNo(@RequestParam String mainstayNo, @CurrentMchNo String employerNo) {
        Map<String, Object> paramMap = Maps.newHashMap();
        //查询账户信息
        EmployerAccountInfoVo employerAccountInfoVo = getByMainstayNoAndEmployerNo(mainstayNo,employerNo);
        //只查开启的通道余额
        employerAccountInfoVo.getChannelInfos().stream()
//                .filter(channelInfo->channelInfo.getStatus().equals(OpenOffEnum.OPEN.getValue()))
                .forEach(
                        channelInfo->{
                            Integer channelType = channelInfo.getChannelType();
                            String amount;
                            try{
                                if (channelInfo.getStatus().equals(OpenOffEnum.OFF.getValue())){
                                    amount = "未开通";
                                }else {
                                    amount =  getAmount(channelInfo,employerAccountInfoVo,channelType);
                                    if(StringUtils.isEmpty(amount)){
                                        log.error("[{}]余额查询异常,通道：{}",employerNo,channelInfo.getChannelType());
                                        throw CommonExceptions.BIZ_INVALID.newWithErrMsg("查询异常");
                                    }
                                }
                            }catch (Exception e){
                                log.error("异常",e);
                                amount = "异常";
                            }
                            if (channelType.equals(ChannelTypeEnum.BANK.getValue())) {
                                paramMap.put(ChannelTypeEnum.BANK.name(),amount);
                            }else if (channelType.equals(ChannelTypeEnum.ALIPAY.getValue())){
                                paramMap.put(ChannelTypeEnum.ALIPAY.name(),amount);
                            }else if (channelType.equals(ChannelTypeEnum.WENXIN.getValue())){
                                paramMap.put(ChannelTypeEnum.WENXIN.name(),amount);
                            }
                        }
                );
        return RestResult.success(paramMap);
    }

    /**
     * 获取合作信息
     * 方便前端处理，数据按照请求的分类
     * @param mchNo 当前商户编码
     * @return
     */
    @RequestMapping("getEmployerCooperate")
    @Permission("merchantEmployer:plat:view")
    public RestResult<Map<String, Object>> getEmployerCooperate(@CurrentMchNo String mchNo){
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        MerchantSaler merchantSaler = merchantSalerFacade.getByMchNo(mchNo);

        // 合作信息
        MerchantEmployerCooperate cooperate = employerCooperateFacade.getByMchNo(mchNo);
        Map<String, Object> cooperateMap = BeanUtil.toMap(cooperate);
        cooperateMap.put("contactName", merchant.getContactName());
        cooperateMap.put("contactPhone", merchant.getContactPhone());
        cooperateMap.put("mchName", merchant.getMchName());
        cooperateMap.put("salerId", merchantSaler.getSalerId());
        cooperateMap.put("salerName", merchantSaler.getSalerName());
        cooperateMap.put("branchName", merchant.getBranchName());
        // 岗位信息
        List<MerchantEmployerPosition> positionList = positionFacade.listByMchNo(mchNo);
        cooperateMap.put("positionVoList", positionList);

        // 报价单信息
        List<Map<String,Object>> quoteList = quoteFacade.getQuoteByMchNo(mchNo);
        cooperateMap.put("quoteVoList", quoteList);

        //经营地址
        MerchantEmployerMain main = merchantEmployerMainFacade.getByMchNo(mchNo);
        cooperateMap.put("managementAddrProvince",main.getManagementAddrProvince());
        cooperateMap.put("managementAddrCity",main.getManagementAddrCity());
        cooperateMap.put("managementAddrTown",main.getManagementAddrTown());
        cooperateMap.put("managementAddrDetail",main.getManagementAddrDetail());

        // 文件信息
        List<MerchantFile> fileList = fileFacade.listByMchNo(mchNo);
        List<String> cooperateFileList = new ArrayList<>();
        List<String> supplementFileUrls = new ArrayList<>();
        for (MerchantFile merchantFile : fileList){
            if(merchantFile.getFileType() == MerchantFileTypeEnum.COMPANY_LEAFLET.getValue()){
                cooperateFileList.add(merchantFile.getFileUrl());
            } else if(merchantFile.getFileType() == MerchantFileTypeEnum.SUPPLEMENT_INFO.getValue()){
                supplementFileUrls.add(merchantFile.getFileUrl());
            }
        }
        cooperateMap.put("cooperateFileList", cooperateFileList);
        cooperateMap.put("supplementFileUrls", supplementFileUrls);

        return RestResult.success(cooperateMap);
    }

    @PostMapping("listByEmployerNoAndMainstayNo")
    public RestResult<Map<String, Object>> listByEmployerNoAndMainstayNo(@RequestParam String mainstayNo,@RequestParam String employerNo) {
        EmployerAccountInfoVo employerAccountInfoVo = this.getByMainstayNoAndEmployerNo(employerNo, mainstayNo,OpenOffEnum.OPEN.getValue());
        final List<SubChannelInfoVo> channelInfos = employerAccountInfoVo.getChannelInfos();

        Map<Integer, Object> channelInfoMap = new HashMap<>();
        channelInfos.forEach(item->{
            channelInfoMap.put(item.getChannelType(), item);
        });

        Map<String, Object> map = new HashMap<>();
        map.put("employerNo", employerAccountInfoVo.getEmployerNo());
        map.put("mainstayNo", employerAccountInfoVo.getMainstayNo());
        map.put("employerName", employerAccountInfoVo.getEmployerName());
        map.put("mainstayName", employerAccountInfoVo.getMainstayName());
        map.put("channelList", channelInfoMap);

        return RestResult.success(map);
    }

    private EmployerAccountInfoVo getByMainstayNoAndEmployerNo(String employerNo,String mainstayNo,Integer status) {
        Map<String,Object> map = new HashMap<>();
        map.put("employerNo",employerNo);
        map.put("mainstayNo",mainstayNo);
        map.put("status",status);
        List<EmployerAccountInfo> list = employerAccountInfoFacade.listBy(map);
        if(ObjectUtils.isEmpty(list) || list.get(0) == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(
                    "该用工企业在相关供应商下无信息，请先建立代征关系，供应商编号：" + mainstayNo + ",用工企业编号：" + employerNo);
        }
        EmployerAccountInfoVo employerAccountInfoVo = new EmployerAccountInfoVo();
        BeanUtils.copyProperties(list.get(0), employerAccountInfoVo);

        List<SubChannelInfoVo> channelList = list.stream().map(
                record -> {
                    SubChannelInfoVo subChannelInfoVo = new SubChannelInfoVo();
                    BeanUtils.copyProperties(record, subChannelInfoVo);
                    return subChannelInfoVo;
                }
        ).collect(Collectors.toList());
        employerAccountInfoVo.setChannelInfos(channelList);
        return employerAccountInfoVo;
    }

    private EmployerAccountInfoVo getByMainstayNoAndEmployerNo(String mainstayNo, String employerNo) {
        List<EmployerAccountInfo> list = employerAccountInfoFacade.listByEmployerNoAndMainstayNo(employerNo,mainstayNo);
        if(ObjectUtils.isEmpty(list) || list.get(0) == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(
                    "该用工企业在相关供应商下无信息，请先建立代征关系，供应商编号：" + mainstayNo + ",用工企业编号：" + employerNo);
        }
        EmployerAccountInfoVo employerAccountInfoVo = new EmployerAccountInfoVo();
        BeanUtils.copyProperties(list.get(0), employerAccountInfoVo);

        List<SubChannelInfoVo> channelList = list.stream().map(
                record -> {
                    SubChannelInfoVo subChannelInfoVo = new SubChannelInfoVo();
                    BeanUtils.copyProperties(record, subChannelInfoVo);
                    return subChannelInfoVo;
                }
        ).collect(Collectors.toList());
        employerAccountInfoVo.setChannelInfos(channelList);
        return employerAccountInfoVo;
    }

    /**
     * 根据商户编码获取发票类目
     */
    @GetMapping("getInvoiceList")
    public RestResult<List<Map<String,Object>>> getPositionByMchNo(@RequestParam(name = "mchNo")String mchNo){
        List<MerchantEmployerPosition> positionList = positionFacade.listByMchNo(mchNo);
        List<Map<String, Object>> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(positionList)){
            for(MerchantEmployerPosition position : positionList){
                Map<String, Object> map = new HashMap<>();
                map.put("serviceDesc", position.getServiceDesc());
                map.put("workCategoryName", position.getWorkCategoryName());
                map.put("workCategoryCode", position.getWorkCategoryCode());
                map.put("invoiceCategoryList", position.getJsonEntity().getInvoiceCategoryList());
                resultList.add(map);
            }
        }
        return RestResult.success(resultList);
    }

    /**
     * 获取各通道余额
     * @param channelInfo
     * @param employerAccountInfoVo
     * @param channelType
     * @return
     */
    private String getAmount(SubChannelInfoVo channelInfo,EmployerAccountInfoVo employerAccountInfoVo,Integer channelType) throws Exception{
        AmountQueryDto amountQueryDto = new AmountQueryDto();
        amountQueryDto.setMainstayNo(employerAccountInfoVo.getMainstayNo());
        amountQueryDto.setEmployerNo(employerAccountInfoVo.getEmployerNo());
        amountQueryDto.setChannelType(channelType);
        amountQueryDto.setChannelNo(channelInfo.getPayChannelNo());
        amountQueryDto.setChannelMchNo(channelInfo.getParentMerchantNo());
        amountQueryDto.setSubMerchantNo(channelInfo.getSubMerchantNo());
        amountQueryDto.setAgreementNo(channelInfo.getSubAgreementNo());
        return accountQueryFacade.getAmount(amountQueryDto);
    }

    @Permission("merchant:amount:view")
    @PostMapping("getRelationListByEmployerNo")
    public RestResult<List<EmployerMainstayRelation>> getRelationListByEmployerNo(@CurrentMchNo String employerNo){
        Map<String,Object> paramMap = Maps.newHashMap();
        paramMap.put("employerNo",employerNo);
        paramMap.put("status", OpenOffEnum.OPEN.getValue());
        List<EmployerMainstayRelation> employerMainstayRelationList = employerMainstayRelationFacade.listBy(paramMap);
        return RestResult.success(employerMainstayRelationList);
    }

    /**
     * 获取银行卡信息
     * @param employerNo
     * @return
     */
    @GetMapping("getBankAccount")
    public RestResult<MerchantBankAccount> getBankAccount(@CurrentMchNo String employerNo){
        return RestResult.success(merchantBankAccountFacade.getByMchNo(employerNo));
    }

    @GetMapping("getMainstayAccount")
    public RestResult<MerchantBankAccount> getMainstayAccount(@RequestParam("mainstayNo") String mainstayNo) {
        return RestResult.success(merchantBankAccountFacade.getByMchNo(mainstayNo));
    }

    /**
     * 获取商户基本信息
     * @param mchNo
     * @return
     */
    @GetMapping("getBaseInfo")
    @Permission("merchantEmployer:base:view")
    public RestResult<Map<String,Object>> getBaseInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantQueryFacade.getBaseInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取商户合作信息
     * @param mchNo
     * @return
     */
    @GetMapping("getCooperateInfo")
    @Permission("merchantEmployer:coop:view")
    public RestResult<Map<String,Object>> getCooperateInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantEmployerFacade.getCooperateInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取产品报价单
     * @param mchNo
     * @return
     */
    @GetMapping("getQuoteInfo")
    public RestResult getQuoteInfo(@CurrentMchNo String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("mchNo",mchNo);
        List<MerchantEmployerQuote> quoteList = quoteFacade.getQuoteList(mchNo,paramMap);
        return RestResult.success(quoteList);
    }

    /**
     * 获取商户主体信息
     * @param mchNo
     * @return
     */
    @GetMapping("getMainInfo")
    @Permission("merchantEmployer:main:view")
    public RestResult<Map<String,Object>> getMainInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantEmployerMainFacade.getMainInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取经营信息
     * @param mchNo
     * @return
     */
    @GetMapping("getBusinessInfo")
    @Permission("merchantEmployer:business:view")
    public RestResult<Map<String,Object>> getBusinessInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantEmployerMainFacade.getBusinessInfo(mchNo);
        return RestResult.success(maps);
    }

    /**
     * 获取账号信息
     * @param mchNo
     * @return
     */
    @GetMapping("getAccountInfo")
    @Permission("merchantEmployer:account:view")
    public RestResult<Map<String,Object>> getAccountInfo(@RequestParam String mchNo){
        if (StringUtil.isEmpty(mchNo)){
            return RestResult.error("商户编号不能为空");
        }
        Map<String,Object> maps = merchantBankAccountFacade.getAccountInfo(mchNo);
        return RestResult.success(maps);
    }

    @GetMapping("getAgreementInfo")
    @Permission("merchantEmployer:agreement:view")
    public RestResult getAgreementInfo(@CurrentMchNo String mchNo){
        Map<String,Object> maps = new HashMap<>();
        maps.put("mchNo",mchNo);
        List<Agreement> pageResult = agreementFacade.getAgreementPageByMchNoAndMainstayNo(maps);
        return RestResult.success(pageResult);
    }

    @GetMapping("listMchProduct")
    public RestResult<List<ListMerchantProductVo>> listMchProduct(@CurrentMchNo String mchNo) {
        final HashMap<String, Object> param = MapUtil.of("mchNo", mchNo);
        return RestResult.success(quoteFacade.listMchProduct(param));
    }
}
