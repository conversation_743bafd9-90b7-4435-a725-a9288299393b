package com.zhixianghui.web.employer.biz.order;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.trade.entity.FeeOrderBatch;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.enums.FeeOrderStatus;
import com.zhixianghui.facade.trade.enums.FeeSourceEnum;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.web.employer.biz.order.excel.BankGrantRow;
import com.zhixianghui.web.employer.biz.order.excel.CKHGrantDataListener;
import com.zhixianghui.web.employer.biz.order.excel.GrantListenerInterface;
import com.zhixianghui.web.employer.vo.order.CountVo;
import com.zhixianghui.web.employer.vo.order.req.OrderAcceptReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName CKHOrderHandlerBiz
 * @Description TODO
 * @Date 2022/6/28 10:04
 */
@Slf4j
@Service
public class CKHOrderHandlerBiz extends AbstractOrderHandler{

    @Reference
    private JobFacade jobFacade;

    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;

    @Override
    public Map<String, String> orderUpload(OrderAcceptReqVo orderAcceptReqVo, EmployerOperatorVO operator, String employerNo) throws Exception {

        Job job = jobFacade.getJobByJobId(orderAcceptReqVo.getJobId());
        if (job == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("任务不存在");
        }
        orderAcceptReqVo.setJobName(job.getJobName());

        //查询用工企业账单是否已全部支付
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("productNo", ProductNoEnum.CKH.getValue());
        paramMap.put("notStatus", FeeOrderStatus.SUCCESS.getCode());
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",orderAcceptReqVo.getMainstayNo());
        paramMap.put("feeSource", FeeSourceEnum.PLATFORM_ORDER.getValue());
        if (feeOrderBatchFacade.isExistNotPay(paramMap)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户仍有未支付账单，请先支付后发");
        }
        return this.batchOrderUpload(orderAcceptReqVo,operator,employerNo);
    }

    @Override
    public String getBatchNo(String employerNo,Date batchTime) {
        //生成订单
        String batchId = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.CKH_ORDER_SEQ.getKey(), SequenceBizKeyEnum.CKH_ORDER_SEQ.getWidth());
        String batchNo = SequenceBizKeyEnum.CKH_ORDER_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
        log.info("[{}]==>生成订单批次号:{}", employerNo, batchNo);
        return batchNo;
    }

    @Override
    public void handleExcel(File excelFile, Order order, CountVo countVo) {
        EasyExcel.read(excelFile, BankGrantRow.class, this.getListener(order,countVo)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        //汇总信息
        order.setRequestCount(countVo.getRequestCount());
        order.setRequestTaskAmount(countVo.getRequestNetAmount());
        order.setFailCount(countVo.getFailCount());
        order.setFailTaskAmount(countVo.getFailNetAmount());
    }

    private AnalysisEventListener<BankGrantRow> getListener(Order order, CountVo countVo) {
        return new CKHGrantDataListener(orderItemBiz,sequenceFacade,notifyFacade,order,countVo,HEAD_ROW_NUMBER,
                this.phoneMustMainstays, MessageMsgDest.TOPIC_TRADE_ASYNC_CKH,MessageMsgDest.TAG_TRADE_ACCEPT_CKH);
    }
}
