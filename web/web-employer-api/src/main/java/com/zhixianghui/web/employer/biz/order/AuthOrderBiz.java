package com.zhixianghui.web.employer.biz.order;

import com.zhixianghui.facade.notify.service.NotifyFacade;
import com.zhixianghui.facade.trade.service.OrderItemFacade;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description TODO
 * @createTime 2022年07月25日 10:21:00
 */
@Service
public class AuthOrderBiz {

    @Reference
    private NotifyFacade notifyFacade;
    @Reference
    private OrderItemFacade orderItemFacade;

    public void initAuthInfo(String employerNo,String platBatchNo){
        orderItemFacade.authInit(platBatchNo);
    }
}
