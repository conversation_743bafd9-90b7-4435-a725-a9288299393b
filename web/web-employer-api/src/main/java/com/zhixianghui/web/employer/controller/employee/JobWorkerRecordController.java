package com.zhixianghui.web.employer.controller.employee;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.constants.common.EncryptKeys;
import com.zhixianghui.common.statics.enums.merchant.AuthStatusEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.employee.dto.JobWorkerApprovedDto;
import com.zhixianghui.facade.employee.dto.JobWorkerWebQueryDto;
import com.zhixianghui.facade.employee.dto.WorkerRecordQueryDto;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.ApproveEvent;
import com.zhixianghui.facade.employee.enums.DeliveryStatusEnum;
import com.zhixianghui.facade.employee.enums.JobWorkerStatusEnum;
import com.zhixianghui.facade.employee.service.JobWorkerRecordFacade;
import com.zhixianghui.facade.employee.vo.JobWorkerDetailVo;
import com.zhixianghui.facade.employee.vo.JobWorkerUpdateVo;
import com.zhixianghui.facade.trade.entity.UserInfo;
import com.zhixianghui.facade.trade.service.SignRecordFacade;
import com.zhixianghui.facade.trade.service.UserInfoFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.vo.sign.AddIDCardImagesVo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("worker")
public class JobWorkerRecordController {
    @Reference
    private JobWorkerRecordFacade workerRecordFacade;
    @Reference
    private UserInfoFacade userInfoFacade;
    @Reference
    private SignRecordFacade signRecordFacade;

    @PostMapping("listPage")
    public RestResult<Page<JobWorkerRecord>> listPage(@RequestBody Page page, @RequestBody WorkerRecordQueryDto workerRecordQueryDto, @CurrentMchNo String mchNo) {

        workerRecordQueryDto.setEmployerNo(mchNo);
        if (ObjectUtils.isNotEmpty(workerRecordQueryDto.getJobStatus()) &&
                workerRecordQueryDto.getJobStatus().equals(JobWorkerStatusEnum.PROCESSING.getCode())) {
            workerRecordQueryDto.setDeliverStatusList(Arrays.asList(DeliveryStatusEnum.NOT_DELIVERED.getCode(),DeliveryStatusEnum.FAIL.getCode()));
        }
        final Page<JobWorkerRecord> workerRecordPage = workerRecordFacade.workerRecordPage(page, workerRecordQueryDto);

        if (StringUtils.isNotBlank(workerRecordQueryDto.getMainstayNo())) {
            if (workerRecordPage.getRecords() != null) {
                workerRecordPage.getRecords().forEach(it->{
                    final boolean signed = signRecordFacade.isSigned(workerRecordQueryDto.getMainstayNo(), mchNo, it.getWorkerIdCardMd5());
                    it.setSignStatus(signed);

                    final boolean verified = userInfoFacade.isVerified(it.getWorkerIdCardMd5());
                    if (verified) {
                        it.setAuthStatus(AuthStatusEnum.SUCCESS.getValue());
                    }else {
                        it.setAuthStatus(AuthStatusEnum.FAIL.getValue());
                    }
                });
            }
        }

        return RestResult.success(workerRecordPage);
    }

    @GetMapping("/findById/{id}")
    public RestResult findById(@PathVariable("id") Long id) {
        JobWorkerDetailVo jobWorkerDetailVo = workerRecordFacade.selectJobAndWorkerDetailById(id);

        Map<String, Object> map = BeanUtil.toMap(jobWorkerDetailVo);
        map.put("attachment", JSONArray.parseArray((String)map.get("attachment"),String.class));
        return RestResult.success(map);
    }

    @PostMapping("/updateWorkerStatus/{ids}")
    public RestResult<String> updateWorkerStatus(@PathVariable("ids") List<Long> ids, @RequestParam Integer status) {
        final int updateCount = this.workerRecordFacade.batchUpdateJobWorkerStatus(ids, status);
        return RestResult.success(StrUtil.format("执行成功,共更新{}条数据", updateCount));
    }

    @PostMapping("/batchUpdateStatus/{ids}")
    public RestResult<String> batchUpdateStatus(@PathVariable("ids") List<Long> ids, @RequestBody JobWorkerUpdateVo jobWorkerUpdateVo) {
        final int updateCount = this.workerRecordFacade.batchUpdateStatus(ids, jobWorkerUpdateVo);
        return RestResult.success(StrUtil.format("执行成功,共更新{}条数据", updateCount));
    }

    @PostMapping("/employ/{ids}")
    public RestResult<String> employ(@PathVariable("ids") List<Long> ids, @RequestParam("event") ApproveEvent event) {
        Integer status = null;
        switch (event) {
            case ADOPT:
                status = JobWorkerStatusEnum.PROCESSING.getCode();
                break;
            case FAIL:
                status = JobWorkerStatusEnum.REJECTED.getCode();
                break;
            default:
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("操作异常");
        }
        final int updateCount = this.workerRecordFacade.batchUpdateJobWorkerStatus(ids, status);
        return RestResult.success("操作成功");
    }

    @PostMapping("/approved")
    public RestResult<String> approved(@RequestBody JobWorkerApprovedDto jobWorkerApprovedDto) {
        workerRecordFacade.approved(jobWorkerApprovedDto);
        return RestResult.success("审批成功");
    }

    @PostMapping("/batchApprovedSuccess/{ids}")
    public RestResult<String> batchApproved(@PathVariable("ids") List<Long> ids) {
        workerRecordFacade.batchApprovedSuccess(ids);
        return RestResult.success("审批成功");
    }


    @PostMapping("/getIdCardByIDCardNoMd5")
    public RestResult getIdCardByIDCardNoMd5(@RequestParam String workerIdCardMd5) {
        return RestResult.success(userInfoFacade.getByIdCardNoMd5(workerIdCardMd5));
    }


    @PostMapping("/completeJobWorker/{ids}")
    public RestResult completeJobWorker(@PathVariable("ids") List<Long> ids) {
        workerRecordFacade.completeJobWorker(ids);
        return RestResult.success("操作成功");
    }


    @PostMapping("/submit")
    public RestResult submit(@RequestBody JobWorkerWebQueryDto workerWebQueryDto) {
        workerRecordFacade.submit(workerWebQueryDto);
        return RestResult.success("提交成功");
    }


    @PostMapping("/delete/{id}")
    public RestResult delete(@PathVariable("id") Long id) {
        workerRecordFacade.delete(id);
        return RestResult.success("删除成功");
    }

    /**
     * 上传雇员身份证
     * @param addSignImagesVo
     * @return
     */
    @PostMapping("addIDCardImages")
    public RestResult<String> addIDCardImages(@RequestBody AddIDCardImagesVo addSignImagesVo) {
        final JobWorkerRecord workerRecord = workerRecordFacade.findById(addSignImagesVo.getJobWorkerId());

        UserInfo userInfo = new UserInfo();
        userInfo.setEncryptKeyId(EncryptKeys.getRandomEncryptKeyId());
        userInfo.setCerFaceUrl(addSignImagesVo.getCerFaceUrl());
        userInfo.setCreateTime(new Date());
        userInfo.setEmployerNo(workerRecord.getEmployerNo());
        userInfo.setEmployerName(workerRecord.getEmployerName());
        userInfo.setIdCardBackUrl(addSignImagesVo.getIdCardBackUrl());
        userInfo.setIdCardFrontUrl(addSignImagesVo.getIdCardFrontUrl());
        userInfo.setIdCardCopyUrl(addSignImagesVo.getIdCardCopyFileUrl());
        userInfo.setReceiveNameEncrypt(workerRecord.getWorkerName());
        userInfo.setReceiveIdCardNoEncrypt(workerRecord.getWorkerIdcard());
        userInfo.setUpdateTime(new Date());

        userInfoFacade.addUserInfo(userInfo);
        return RestResult.success("操作成功");
    }

}
