package com.zhixianghui.web.employer.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.common.RedisKeyPrefixEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.common.util.utils.StringUtil;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.service.MerchantFacade;
import com.zhixianghui.facade.merchant.service.MerchantQueryFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.starter.comp.component.RedisClient;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentSession;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.component.CaptchaHelper;
import com.zhixianghui.web.employer.component.DataDecryptHelper;
import com.zhixianghui.web.employer.component.OperatorPwdHelper;
import com.zhixianghui.web.employer.component.TradePwdHelper;
import com.zhixianghui.web.employer.constant.PermissionConstant;
import com.zhixianghui.web.employer.dto.Session;
import com.zhixianghui.web.employer.vo.permission.ChangeEmailVO;
import com.zhixianghui.web.employer.vo.permission.ChangeLeaderVO;
import com.zhixianghui.web.employer.vo.user.SafeSettingResVO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zhixianghui.web.employer.constant.PermissionConstant.CHANGE_LEADER_EMAIL_CODE_KEY;

/**
 * 安全设置
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("safe")
public class SafeController {

    @Autowired
    private CaptchaHelper captchaHelper;

    @Autowired
    private TradePwdHelper tradePwdHelper;

    @Autowired
    private DataDecryptHelper dataDecryptHelper;

    @Autowired
    private RedisClient redisClient;

    @Reference
    private EmployerStaffFacade employerStaffFacade;

    @Reference
    private MerchantFacade merchantFacade;

    @Reference
    private MerchantQueryFacade merchantQueryFacade;

    @Autowired
    private OperatorPwdHelper operatorPwdHelper;

    /**
     * 确认发放查询是否初始化过密码用于权限拆分
     * @param mchNo
     * @return
     */
    @Permission("order:confirmBatchOrderGrant:edit")
    @GetMapping("getTradePwdSetting")
    public RestResult<SafeSettingResVO> getTradePwdSetting(@CurrentMchNo String mchNo) {
        SafeSettingResVO vo = new SafeSettingResVO();
        vo.setSettingTradePwd(tradePwdHelper.isInitTradePwd(mchNo));
        return RestResult.success(vo);
    }

    /**
     * 获取安全设置信息
     * @return
     */
    @Permission("system:safe")
    @GetMapping("getSafeSetting")
    public RestResult<SafeSettingResVO> getSafeSetting(@CurrentMchNo String mchNo) {
        SafeSettingResVO vo = new SafeSettingResVO();
        vo.setAdmin(employerStaffFacade.getAdmin(mchNo));

        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        vo.setEmail(merchant == null ? null : merchant.getContactEmail());
        vo.setSettingTradePwd(tradePwdHelper.isInitTradePwd(mchNo));
        return RestResult.success(vo);
    }

    /**
     * 修改支付密码
     * @param tradePwd      支付密码
     * @param smsCode       短信验证码
     * @return
     */
    @Permission("safe:tradePwd:change")
    @PostMapping("changeTradePwd")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "修改支付密码")
    public RestResult<String> changeTradePwd(@RequestParam String tradePwd,
                                             @RequestParam String smsCode,
                                             @CurrentStaffVo EmployerStaffVO staffVO,
                                             @CurrentMchNo String mchNo,
                                             @CurrentSession Session session) {
        captchaHelper.verifySmsCode(staffVO.getPhone(), smsCode);

        // 密码解密
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        tradePwd = dataDecryptHelper.decryptData(privateKey, tradePwd);

        tradePwdHelper.changeTradePwd(mchNo, tradePwd);

        captchaHelper.invalidSmsCode(staffVO.getPhone());
        return RestResult.success("修改支付密码成功");
    }

    /**
     * 获取商户负责人
     * @return
     */
//    @Permission("safe:leader:get")
    @GetMapping("getLeader")
    public RestResult<EmployerStaffVO> getLeader(@CurrentMchNo String mchNo) {
        return RestResult.success(employerStaffFacade.getAdmin(mchNo));
    }

    /**
     * 更换商户负责人
     * @return
     */
    @Permission("safe:leader:change")
    @PostMapping("changeLeader")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "更换商户负责人")
    public RestResult<String> changeLeader(@CurrentMchNo String mchNo,
                                           @CurrentStaffVo EmployerStaffVO staffVO,
                                           @RequestBody @Valid ChangeLeaderVO vo,
                                           @CurrentSession Session session) {
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        // 验证新负责人手机验证码
        captchaHelper.verifySmsCode(vo.getNewLeaderPhone(), vo.getSmsCode());

        // 商户支付密码校验
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        vo.setTradePwd(dataDecryptHelper.decryptData(privateKey, vo.getTradePwd()));
        tradePwdHelper.verifyTradePwd(staffVO, mchNo, vo.getTradePwd());

        // 修改员工角色、更新到商户信息表
        employerStaffFacade.changeAdmin(mchNo, vo.getNewLeaderPhone(), vo.getNewLeaderName(), staffVO.getName());
        merchant.setContactName(vo.getNewLeaderName());
        merchant.setContactPhone(vo.getNewLeaderPhone());
        merchantFacade.update(merchant);

        captchaHelper.invalidSmsCode(vo.getNewLeaderPhone());
        return RestResult.success("更换负责人成功");
    }

    /**
     * 获取常用邮箱
     */
    @Permission("safe:email:change")
    @GetMapping("getEmail")
    public RestResult<String> getEmail(@CurrentMchNo String mchNo) {
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }
        return RestResult.success(merchant.getContactEmail());
    }

    /**
     * 更换常用邮箱，发送邮箱验证码
     * @return
     */
    @Permission("safe:email:change")
    @PostMapping("changeEmailVerifyCode")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "更换常用邮箱，发送邮箱验证码")
    public RestResult<String> changeEmailVerifyCode(@CurrentMchNo String mchNo,

                                                    @RequestParam String newEmail) {
        // 频率限定，同一商户60秒请求一次
        String key = RedisKeyPrefixEnum.WEB_EMPLOYER_SESSION_KEY.name() + ":" + CHANGE_LEADER_EMAIL_CODE_KEY + ":" + mchNo;
        String value = redisClient.get(key);
        if (StringUtil.isNotEmpty(value)) {
            int diff = (int)(System.currentTimeMillis() - Long.parseLong(value)) / 1000;
            //如果上一次发送验证码的时间距离现在的时间小于60秒

            if (diff<60) {
                int waitSeconds = 60 - diff;
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请求频繁，请于" + (waitSeconds == 0 ? 1 : waitSeconds) + "秒后重试");
            }
        }

        // 发送邮箱
        captchaHelper.sendEmailCode(newEmail);

        // 发送成功，记录验证码发送时间
        redisClient.set(key, Long.toString(System.currentTimeMillis()), 60 * 5);
        return RestResult.success("发送成功");
    }

    /**
     * 更换常用邮箱
     * @return
     */
    @Permission("safe:email:change")
    @PostMapping("changeEmail")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "更换常用邮箱")
    public RestResult<String> changeEmail(@CurrentMchNo String mchNo,
                                          @CurrentStaffVo EmployerStaffVO staffVO,
                                          @RequestBody @Valid ChangeEmailVO vo,
                                          @CurrentSession Session session) {
        Merchant merchant = merchantQueryFacade.getByMchNo(mchNo);
        if (merchant == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户不存在");
        }

        // 邮箱验证码
        captchaHelper.verifyEmailCode(vo.getNewEmail(), vo.getVerifyCode());

        // 商户支付密码校验
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        vo.setTradePwd(dataDecryptHelper.decryptData(privateKey,vo.getTradePwd()));
        tradePwdHelper.verifyTradePwd(staffVO, mchNo, vo.getTradePwd());

        // 更新到商户信息表
        merchant.setContactEmail(vo.getNewEmail());
        merchantFacade.update(merchant);

        captchaHelper.invalidEmailCode(vo.getNewEmail());
        return RestResult.success("更换常用邮箱成功");
    }
}
