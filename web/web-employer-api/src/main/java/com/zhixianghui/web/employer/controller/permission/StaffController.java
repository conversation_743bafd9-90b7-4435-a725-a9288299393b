package com.zhixianghui.web.employer.controller.permission;

import com.zhixianghui.common.statics.annotations.Permission;
import com.zhixianghui.common.statics.constants.common.enums.OperateLogTypeEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import com.zhixianghui.common.statics.result.PageParam;
import com.zhixianghui.common.statics.result.RestResult;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.flow.service.FlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.merchant.entity.user.portal.EmployerRole;
import com.zhixianghui.facade.merchant.service.employer.EmployerStaffFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.annotation.CurrentSession;
import com.zhixianghui.web.employer.annotation.CurrentStaffVo;
import com.zhixianghui.web.employer.annotation.Logger;
import com.zhixianghui.web.employer.component.DataDecryptHelper;
import com.zhixianghui.web.employer.component.TradePwdHelper;
import com.zhixianghui.web.employer.constant.PermissionConstant;
import com.zhixianghui.web.employer.dto.Session;
import com.zhixianghui.web.employer.vo.permission.AddStaffVO;
import com.zhixianghui.web.employer.vo.permission.EditStaffVO;
import com.zhixianghui.web.employer.vo.permission.StaffQueryVO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 员工管理
 *
 * <AUTHOR> Guangsheng
 */
@RestController
@RequestMapping("staff")
public class StaffController {

    @Reference
    private FlowFacade flowFacade;

    @Reference
    private EmployerStaffFacade employerStaffFacade;

    @Autowired
    private TradePwdHelper tradePwdHelper;

    @Autowired
    private DataDecryptHelper dataDecryptHelper;

    /**
     * 员工分页查询
     * @param staffQueryVO
     * @param mchNo
     * @return
     */
    @Permission("pms:staff:view")
    @PostMapping("listPage")
    public RestResult<Object> listPage(@RequestBody StaffQueryVO staffQueryVO,
                                       @CurrentMchNo String mchNo) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("phone", staffQueryVO.getPhone());
        paramMap.put("name", staffQueryVO.getName());
        paramMap.put("nameLike", staffQueryVO.getNameLike());
        paramMap.put("roleIds", staffQueryVO.getRoleIds());
        if (staffQueryVO.getRoleIds() != null){
            paramMap.put("idNum", staffQueryVO.getRoleIds().size());
        }
        return RestResult.success(employerStaffFacade.listPage(mchNo, paramMap, PageParam.newInstance(staffQueryVO.getPageCurrent(), staffQueryVO.getPageSize())));
    }

    /**
     * 新增员工
     * @param addStaffVO
     */
    @Permission("pms:staff:add")
    @PostMapping("add")
    @Logger(type = OperateLogTypeEnum.CREATE, action = "新增员工")
    public RestResult<String> add(@RequestBody @Valid AddStaffVO addStaffVO,
                                  @CurrentStaffVo EmployerStaffVO currentStaffVO,
                                  @CurrentMchNo String mchNo,
                                  @CurrentSession Session session) {
        // 密码解密
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        addStaffVO.setTradePwd(dataDecryptHelper.decryptData(privateKey, addStaffVO.getTradePwd()));

        tradePwdHelper.verifyTradePwd(currentStaffVO, mchNo, addStaffVO.getTradePwd());

        EmployerStaffVO staffVO = new EmployerStaffVO();
        staffVO.setCreator(currentStaffVO.getOperatorName());
        staffVO.setType(PortalStaffTypeEnum.USER.getValue());
        staffVO.setMchNo(mchNo);
        staffVO.setMchName(currentStaffVO.getMchName());
        staffVO.setPhone(addStaffVO.getPhone());
        staffVO.setCreateTime(new Date());
        staffVO.setName(addStaffVO.getName());
        staffVO.setOperatorName(currentStaffVO.getOperatorName());
        employerStaffFacade.createAndAssignRole(staffVO, addStaffVO.getRoleIds());
        return RestResult.success("添加员工成功");
    }

    /**
     * 根据员工id查询员工信息
     * @param id        员工id
     */
    @Permission("pms:staff:add")
    @GetMapping("getById")
    public RestResult<Map<String, Object>> getById(@RequestParam Long id, @CurrentMchNo String mchNo) {
        Map<String, Object> map = new HashMap<>();
        map.put("staff", employerStaffFacade.getById(mchNo, id));
        map.put("roleIds", employerStaffFacade.getRoleByStaffId(mchNo, id).stream().map(EmployerRole::getId).collect(Collectors.toList()));
        return RestResult.success(map);
    }


    /**
     * 修改员工信息
     */
    @Permission("pms:staff:edit")
    @PostMapping("edit")
    @Logger(type = OperateLogTypeEnum.MODIFY, action = "修改员工信息")
    public RestResult<String> edit(@RequestBody @Valid EditStaffVO editStaffVO,
                                   @CurrentStaffVo EmployerStaffVO staffVO,
                                   @CurrentMchNo String mchNo,
                                   @CurrentSession Session session) {
        // 密码解密
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        editStaffVO.setTradePwd(dataDecryptHelper.decryptData(privateKey, editStaffVO.getTradePwd()));

        tradePwdHelper.verifyTradePwd(staffVO, mchNo, editStaffVO.getTradePwd());

        employerStaffFacade.updateRole(mchNo, editStaffVO.getId(), editStaffVO.getRoleIds(),editStaffVO.getName());
        return RestResult.success("操作成功");
    }

    /**
     * 删除员工
     * @param id    员工id
     */
    @Permission("pms:staff:delete")
    @PostMapping("delete")
    @Logger(type = OperateLogTypeEnum.DELETE, action = "删除员工")
    public RestResult<String> delete(@RequestParam Long id,
                                     @RequestParam String tradePwd,
                                     @CurrentStaffVo EmployerStaffVO staffVO,
                                     @CurrentMchNo String mchNo,
                                     @CurrentSession Session session) {

//        FlowUserVo flowUserVo = new FlowUserVo();
//        flowUserVo.setUserId(staffVO.getId());
//        flowUserVo.setPlatform(PlatformSource.MERCHANT.getValue());
//        flowUserVo.setNo(mchNo);
//        if (flowFacade.isExistTask(flowUserVo)){
//            return RestResult.error("当前员工存在未处理待办，无法删除");
//        }

        // 密码解密
        //此时登录状态，从session拿到私钥
        String privateKey = session.getAttribute(PermissionConstant.OPERATOR_SESSION_PRIVATEKEY_KEY, String.class);
        tradePwd = dataDecryptHelper.decryptData(privateKey, tradePwd);

        tradePwdHelper.verifyTradePwd(staffVO, mchNo, tradePwd);

        employerStaffFacade.deleteById(mchNo, id);
        return RestResult.success("删除成功");
    }
}
