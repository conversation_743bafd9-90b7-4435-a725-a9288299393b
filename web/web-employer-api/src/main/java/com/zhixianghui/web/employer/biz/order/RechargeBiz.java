package com.zhixianghui.web.employer.biz.order;

import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.report.ChannelNoEnum;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.banklink.service.alipay.AlipayFacade;
import com.zhixianghui.facade.common.entity.report.EmployerAccountInfo;
import com.zhixianghui.facade.common.service.EmployerAccountInfoFacade;
import com.zhixianghui.facade.common.service.SequenceFacade;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.trade.entity.RechargeRecord;
import com.zhixianghui.facade.trade.enums.IdentityTypeEnum;
import com.zhixianghui.facade.trade.enums.RechargeStatusEnum;
import com.zhixianghui.facade.trade.enums.RechargeTypeEnum;
import com.zhixianghui.facade.trade.service.OrderFacade;
import com.zhixianghui.web.employer.vo.permission.RechargeVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName RechargeBiz
 * @Description TODO
 * @Date 2022/7/4 11:41
 */
@Slf4j
@Service
public class RechargeBiz {

    @Reference
    private OrderFacade orderFacade;

    @Reference
    private EmployerAccountInfoFacade employerAccountInfoFacade;

    @Reference
    private AlipayFacade alipayFacade;

    @Reference
    private SequenceFacade sequenceFacade;

    @Autowired
    private TradeCheckBiz tradeCheckBiz;

    public String recharge(RechargeVo rechargeVo, String employerNo, EmployerOperatorVO operatorVO) {
        tradeCheckBiz.checkMchInfo(employerNo,rechargeVo.getMainstayNo());

        EmployerAccountInfo employerAccountInfo = getEmployerAccountInfo(employerNo,rechargeVo.getMainstayNo(),rechargeVo.getChannelType());
        if (employerAccountInfo.getPayChannelNo() == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("未正确配置通道编号");
        }
        if (StringUtils.equals(employerAccountInfo.getPayChannelNo(), ChannelNoEnum.ALIPAY.name())) {
            //0. 校验报备记录
            if (!tradeCheckBiz.checkReport(employerAccountInfo)) {
                throw CommonExceptions.BIZ_INVALID.newWithErrMsg("通道未正确报备");
            }
            //1. 创建充值记录
            RechargeRecord rechargeRecord = fillRechargeRecord(employerAccountInfo, rechargeVo);
            rechargeRecord.setCreateBy(operatorVO.getName());
            rechargeRecord.setRechargeType(RechargeTypeEnum.TRANSFER_RECHARGE.getCode());
            rechargeRecord.setPayeeName(employerAccountInfo.getEmployerName());

            rechargeRecord.setPayerBankName(ChannelNoEnum.ALIPAY.getDesc());
            rechargeRecord.setPayerIdentity(employerAccountInfo.getSubAlipayUserId());
            rechargeRecord.setPayerIdentityType(IdentityTypeEnum.ALIPAY_USER_ID.getValue());
            rechargeRecord.setPayerName(employerAccountInfo.getEmployerName());

            RechargeRecord rechargeRecordNew = orderFacade.addRechargeRecord(rechargeRecord);
            //2. 调用充值接口
            String recharge_url = alipayFacade.recharge(rechargeRecordNew.getPayeeAgreementNo(), rechargeRecordNew.getAccountBookId(), rechargeRecordNew.getPayerIdentity(), rechargeRecordNew.getRechargeAmount().toPlainString(), rechargeRecordNew.getRechargeOrderId(), DateUtil.formatDateTime(rechargeRecord.getExpireTime()));

            return recharge_url;

        } else if (StringUtils.equals(employerAccountInfo.getPayChannelNo(), ChannelNoEnum.JOINPAY.name())) {
            //1. 创建充值记录
            RechargeRecord rechargeRecord = fillRechargeRecord(employerAccountInfo, rechargeVo);
            rechargeRecord.setCreateBy(operatorVO.getName());
            rechargeRecord.setRechargeType(RechargeTypeEnum.TRANSFER_RECHARGE.getCode());
            rechargeRecord.setRechargeStatus(RechargeStatusEnum.NEW.getCode().shortValue());
//            RechargeRecord rechargeRecordNew = orderFacade.addRechargeRecord(rechargeRecord);

            return "success";
        } else {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("暂不支持该通道充值");
        }
    }

    private RechargeRecord fillRechargeRecord(EmployerAccountInfo employerAccountInfo,RechargeVo rechargeVo) {
        String rechargeOrderId = LocalDateTime.now(ZoneId.of("Asia/Shanghai")).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + sequenceFacade.nextRedisId(SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getPrefix(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getKey(), SequenceBizKeyEnum.ALI_RECHARGE_NO_SEQ.getWidth());


        Date now = new Date();
        Date expireDateTime = DateUtil.addMinute(now, 60);

        RechargeRecord rechargeRecord = new RechargeRecord();
        rechargeRecord.setRechargeOrderId(rechargeOrderId);
        rechargeRecord.setChannelName(employerAccountInfo.getPayChannelName());
        rechargeRecord.setChannelType(employerAccountInfo.getChannelType().shortValue());
        rechargeRecord.setRechargeAmount(new BigDecimal(rechargeVo.getRechargeAmount()));
        rechargeRecord.setCreateTime(now);
        rechargeRecord.setChannelCode(employerAccountInfo.getPayChannelNo());
        rechargeRecord.setExpireTime(expireDateTime);
        rechargeRecord.setUpdateTime(now);
        rechargeRecord.setEmployerNo(employerAccountInfo.getEmployerNo());
        rechargeRecord.setEmployerName(employerAccountInfo.getEmployerName());
        rechargeRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
        rechargeRecord.setMainstayName(employerAccountInfo.getMainstayName());
        rechargeRecord.setAccountBookId(employerAccountInfo.getSubMerchantNo());
        rechargeRecord.setPayeeAgreementNo(employerAccountInfo.getSubAgreementNo());
        rechargeRecord.setPayeeIdentity(employerAccountInfo.getSubMerchantNo());
        rechargeRecord.setPayeeIdentityType(IdentityTypeEnum.ACCOUNT_BOOK_ID.getValue());
        return rechargeRecord;
    }

    private RechargeRecord updateRechargeRecord(EmployerAccountInfo employerAccountInfo,RechargeVo rechargeVo,RechargeRecord latestRechargeRecord) {

        Date now = new Date();
        Date expireDateTime = DateUtil.addMinute(now, 60);

        RechargeRecord rechargeRecord = new RechargeRecord();
        rechargeRecord.setId(latestRechargeRecord.getId());
        rechargeRecord.setRechargeOrderId(latestRechargeRecord.getRechargeOrderId());
        rechargeRecord.setChannelName(employerAccountInfo.getPayChannelName());
        rechargeRecord.setChannelType(employerAccountInfo.getChannelType().shortValue());
        rechargeRecord.setRechargeAmount(new BigDecimal(rechargeVo.getRechargeAmount()));
        rechargeRecord.setChannelCode(employerAccountInfo.getPayChannelNo());
        rechargeRecord.setExpireTime(expireDateTime);
        rechargeRecord.setUpdateTime(now);
        rechargeRecord.setEmployerNo(employerAccountInfo.getEmployerNo());
        rechargeRecord.setEmployerName(employerAccountInfo.getEmployerName());
        rechargeRecord.setMainstayNo(employerAccountInfo.getMainstayNo());
        rechargeRecord.setMainstayName(employerAccountInfo.getMainstayName());
        rechargeRecord.setAccountBookId(employerAccountInfo.getSubMerchantNo());
        rechargeRecord.setPayeeAgreementNo(employerAccountInfo.getSubAgreementNo());
        rechargeRecord.setPayeeIdentity(employerAccountInfo.getSubAlipayUserId());
        rechargeRecord.setPayeeIdentityType("ALIPAY_USER_ID");
        return rechargeRecord;
    }

    private EmployerAccountInfo getEmployerAccountInfo(String employerNo,String mainstayNo,Integer channelType) {
        EmployerAccountInfo employerAccountInfo = employerAccountInfoFacade.getByEmployerNoAndMainstayNoAndChannelType(employerNo, mainstayNo, channelType);

        if (employerAccountInfo == null) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户未开通该通道账户");
        }
        return employerAccountInfo;
    }
}
