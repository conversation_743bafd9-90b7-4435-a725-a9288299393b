package com.zhixianghui.web.employer.biz.index;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.common.statics.enums.invoice.InvoiceStatusEnum;
import com.zhixianghui.common.statics.enums.notification.NotificationReceiverTypeEnum;
import com.zhixianghui.common.statics.enums.notification.PublishStatusEnum;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.facade.common.entity.notificaton.NotificationRecordDetail;
import com.zhixianghui.facade.common.enums.PlatformSource;
import com.zhixianghui.facade.common.service.NotificationFacade;
import com.zhixianghui.facade.common.vo.NotificationDetailFullInfo;
import com.zhixianghui.facade.data.service.CkOrderFacade;
import com.zhixianghui.facade.flow.service.QueryFlowFacade;
import com.zhixianghui.facade.flow.vo.req.FlowUserVo;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.facade.trade.constant.TradeConstant;
import com.zhixianghui.facade.trade.constant.YesNoCodeEnum;
import com.zhixianghui.facade.trade.enums.RechargeStatusEnum;
import com.zhixianghui.facade.trade.service.InvoiceFacade;
import com.zhixianghui.facade.trade.service.RechargeQueryFacade;
import com.zhixianghui.starter.comp.component.RedisClient;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName IndexDataBiz
 * @Description TODO
 * @Date 2022/4/29 10:37
 */
@Service
public class IndexDataBiz {


    @Autowired
    private RedisClient redisClient;

    @Reference
    private CkOrderFacade ckOrderFacade;

    @Reference
    private RechargeQueryFacade rechargeQueryFacade;

    @Reference
    private InvoiceFacade invoiceFacade;

    @Reference
    private QueryFlowFacade queryFlowFacade;

    @Reference
    private NotificationFacade notificationFacade;
    static final String CACHE_PRE = "month:data";

    public Map<String, String> getMonthData(String mchNo) {
        Date endDay = DateUtil.getDayEnd(DateUtil.addDay(new Date(),-1));
        String endDayStr = DateUtil.formatDate(endDay);
        Map<String,String> map = redisClient.hgetAll(CACHE_PRE + ":" + mchNo + ":" + endDayStr);
        if(map != null && map.size() > 0){
            return map;
        }
        Date beginDay = DateUtil.getDayStart(DateUtil.getFirstOfMonth(endDay));
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.putAll(getGrantData(beginDay,endDay,mchNo));
        resultMap.putAll(getInvoiceData(beginDay,endDay,mchNo));
        resultMap.putAll(getChargeData(beginDay,endDay,mchNo));
        resultMap.put("date",endDayStr);
        Map<String,String> resultStrMap = resultMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,x->x.getValue().toString()));
        redisClient.hset(CACHE_PRE + ":" + mchNo + ":" + endDayStr,resultStrMap);
        return resultStrMap;
    }

    private Map<String,Object> getChargeData(Date beginDay, Date endDay, String mchNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("createBeginDate",beginDay);
        paramMap.put("createEndDate",endDay);
        paramMap.put("employerNo",mchNo);
        paramMap.put("rechargeStatus", RechargeStatusEnum.SUCCESS.getCode());
        return rechargeQueryFacade.countRechargeAmount(paramMap);
    }

    private Map<String,Object> getInvoiceData(Date beginDay, Date endDay, String mchNo) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("createTimeBegin",beginDay);
        paramMap.put("createTimeEnd",endDay);
        paramMap.put("employerMchNo",mchNo);
        paramMap.put("invoiceStatus", InvoiceStatusEnum.SENDED.getValue());
        return invoiceFacade.countInvoiceAmount(paramMap);
    }

    private Map<String,Object> getGrantData(Date beginDay,Date endDay, String mchNo) {
        Map<String,Object> paramMap = new HashMap<>();
        //确定分区位置
        paramMap.put("createDateStart",DateUtil.addDay(beginDay,-1));
        paramMap.put("createDateEnd",endDay);
        paramMap.put("completeTimeStart",beginDay);
        paramMap.put("completeTimeEnd",endDay);
        paramMap.put("employerNo",mchNo);
        //Map<String,String> map = ckOrderFacade.countOrderAmont(paramMap).entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,x->x.toString()));
        return ckOrderFacade.countOrderAmont(paramMap);
    }

    public Map<String,Object> getTaskData(EmployerStaffVO employerStaffVO) {
        FlowUserVo flowUserVo = new FlowUserVo();
        flowUserVo.setNo(employerStaffVO.getMchNo());
        flowUserVo.setUserId(employerStaffVO.getId());
        flowUserVo.setUserName(employerStaffVO.getName());
        flowUserVo.setPlatform(PlatformSource.MERCHANT.getValue());
        return queryFlowFacade.countTaskData(flowUserVo);
    }

    public Map<String,Object> getNotifyList(String mchNo) {
        Page page = new Page<>();
        page.setCurrent(1L);
        page.setSize(10L);
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("sortColumns","b.PUBLISH_TIME desc");
        paramMap.put("mchNo",mchNo);
        paramMap.put("publishStatus", PublishStatusEnum.PUBLISHED.getCode());
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_MERCHANT.getCode(),NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode(),NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode(),NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode()});
        IPage pageData = notificationFacade.listNotificationRecordFullInfo(page,paramMap);
        List<NotificationDetailFullInfo> list = pageData.getRecords();
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("array",list.stream().map(x->{
            NotificationRecordDetail detail = x.getNotificationRecordDetail();
            return new HashMap<String,Object>(){
                private static final long serialVersionUID = 9161351061083300991L;

                {   put("id",detail.getNotificationId());
                    put("pop", x.getNotificationRecord().isPop());
                    put("readStatus", detail.getReadStatus());
                    put("notificationTitle",detail.getNotificationTitle());
                    put("publishTime",x.getNotificationRecord().getPublishTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));}};
        }).collect(Collectors.toList()));

        paramMap.put("readStatus", YesNoCodeEnum.NO.getValue());
        paramMap.put("notificationReceiverType", new Integer[]{NotificationReceiverTypeEnum.ALL_MERCHANT.getCode(),NotificationReceiverTypeEnum.ALL_EMPLOYER.getCode(),NotificationReceiverTypeEnum.ALL_MAINSTAY.getCode(),NotificationReceiverTypeEnum.SELECTED_MERCHANT.getCode()});
        IPage readData = notificationFacade.listNotificationRecordFullInfo(page,paramMap);
        resultMap.put("count",readData.getTotal());
        return resultMap;
    }

    public List<String> dynamicList(String mchNo) {
        String date = DateUtil.formatDate(new Date());
        String key = TradeConstant.DYNAMIC_MSG_KEY + mchNo + ":" + date;
        return redisClient.lrange(key,0L,-1L);
    }
}
