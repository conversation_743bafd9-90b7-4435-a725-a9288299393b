package com.zhixianghui.web.employer.biz.order.offline;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zhixianghui.common.statics.constants.rmqdest.MessageMsgDest;
import com.zhixianghui.common.statics.enums.common.OpenOffEnum;
import com.zhixianghui.common.statics.enums.common.SequenceBizKeyEnum;
import com.zhixianghui.common.statics.enums.product.ProductNoEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.DateUtil;
import com.zhixianghui.common.util.utils.FileUtils;
import com.zhixianghui.common.util.utils.JsonUtil;
import com.zhixianghui.facade.common.entity.report.EmployerMainstayRelation;
import com.zhixianghui.facade.employee.entity.Job;
import com.zhixianghui.facade.employee.service.JobFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.vo.EmployerOperatorVO;
import com.zhixianghui.facade.trade.entity.OfflineOrder;
import com.zhixianghui.facade.trade.entity.Order;
import com.zhixianghui.facade.trade.enums.FeeOrderStatus;
import com.zhixianghui.facade.trade.enums.LaunchWayEnum;
import com.zhixianghui.facade.trade.enums.OrderStatusEnum;
import com.zhixianghui.facade.trade.service.FeeOrderBatchFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderFacade;
import com.zhixianghui.facade.trade.service.OfflineOrderItemFacade;
import com.zhixianghui.web.employer.biz.order.AbstractOrderHandler;
import com.zhixianghui.web.employer.biz.order.excel.BankGrantRow;
import com.zhixianghui.web.employer.biz.order.excel.CKHOffLineGrantDataListener;
import com.zhixianghui.web.employer.biz.order.excel.TestGrantDataListener;
import com.zhixianghui.web.employer.exception.MchOrderNoException;
import com.zhixianghui.web.employer.utils.MultipartFileUtil;
import com.zhixianghui.web.employer.vo.order.CountVo;
import com.zhixianghui.web.employer.vo.order.req.OrderAcceptReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName CKHOrderHandlerBiz
 * @Description TODO
 * @Date 2022/6/28 10:04
 */
@Slf4j
@Service
public class CKHOfflineOrderHandlerBiz extends AbstractOrderHandler {

    @Reference
    private JobFacade jobFacade;

    @Reference
    private FeeOrderBatchFacade feeOrderBatchFacade;

    @Reference
    private OfflineOrderFacade offlineOrderFacade;

    @Reference
    private OfflineOrderItemFacade offlineOrderItemFacade;

    @Autowired
    private OfflineOrderItemBiz orderItemBiz;

    @Override
    public Map<String, String> orderUpload(OrderAcceptReqVo orderAcceptReqVo, EmployerOperatorVO operator, String employerNo) throws Exception {

        Job job = jobFacade.getJobByJobId(orderAcceptReqVo.getJobId());
        if (job == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("任务不存在");
        }
        orderAcceptReqVo.setJobName(job.getJobName());

        //查询用工企业账单是否已全部支付
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("productNo", ProductNoEnum.CKH.getValue());
        paramMap.put("notStatus", FeeOrderStatus.SUCCESS.getCode());
        paramMap.put("employerNo",employerNo);
        paramMap.put("mainstayNo",orderAcceptReqVo.getMainstayNo());
        if (feeOrderBatchFacade.isExistNotPay(paramMap)){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("商户仍有未支付账单，请先支付后发");
        }
        return this.batchOrderUpload(orderAcceptReqVo,operator,employerNo);
    }

    @Override
    public Map<String, String> batchOrderUpload(OrderAcceptReqVo orderAcceptReqVo, EmployerOperatorVO operator, String employerNo) throws Exception {

        //设置产品名称
        orderAcceptReqVo.setProductName(ProductNoEnum.getEnum(orderAcceptReqVo.getProductNo()).getDesc());

        log.info("批次订单导入。mchNo:{}, operator:{}, orderReqVo:{}",employerNo,operator.getPhone(), orderAcceptReqVo);
        //校验商户状态
        tradeCheckBiz.checkMchInfo(employerNo,orderAcceptReqVo.getMainstayNo());

        if (!riskControlFacade.grantValidateExistRule(employerNo,orderAcceptReqVo.getMainstayNo())){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("找不到对应的规则，请联系平台管理员");
        }

        //检查代征关系
        EmployerMainstayRelation employerMainstayRelation = employerMainstayRelationFacade.getByEmployerNoAndMchNo(employerNo, orderAcceptReqVo.getMainstayNo());
        if (employerMainstayRelation == null || employerMainstayRelation.getStatus() == OpenOffEnum.OFF.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征关系未激活,请联系客服");
        }
        //校验产品开通
        Merchant mainstay = merchantQueryFacade.getByMchNo(orderAcceptReqVo.getMainstayNo());
        if (mainstay == null){
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("代征主体不存在");
        }

        // 检查上传文件
        MultipartFile file = orderAcceptReqVo.getFile();
        if(!MultipartFileUtil.checkFileSize(file.getSize(), FILE_SIZE, UNIT)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("文件大小超过限制");
        }

        if(!file.getOriginalFilename().endsWith(SUFFIX)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("FileName:" + file.getOriginalFilename() +"文件格式有误，必须为xlsx格式");
        }
        File excelFile = MultipartFileUtil.transfer2File(file);

        List<BankGrantRow> list = new ArrayList<>();
        EasyExcel.read(excelFile, BankGrantRow.class, new TestGrantDataListener(HEAD_ROW_NUMBER, list)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        log.info("[{}]==>检查文件具体内容通过", employerNo);
        if (CollectionUtils.isEmpty(list) || emptyContent(list)) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("提交的发放名单不能为空");
        }

        Date batchTime = new Date();
        String batchNo = getBatchNo(employerNo,batchTime);

        //初始化订单信息
        String logFlag = String.join("-",employerNo,batchNo);
        OfflineOrder order = fillOrder(batchNo, orderAcceptReqVo, employerMainstayRelation, batchTime, mainstay.getMchName());
        offlineOrderFacade.saveOrder(order);

        final OfflineOrder orderSaved = offlineOrderFacade.getByPlatBatchNo(order.getPlatBatchNo());

        log.info("[{}]==>批次插入成功", logFlag);
        asyncBiz.doAsync(()->{
            doAsync(excelFile,orderSaved);
            return true;
        });
        //返回结果
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("platBatchNo", orderSaved.getPlatBatchNo());
        log.info("[{}]==>返回结果：{}", logFlag, JsonUtil.toString(paramMap));
        //存入订单批次
//        Date expireTime = DateUtil.parseJodaDateTime(order.getCreateTime()).plusHours(7*24).toDate();
//        redisClient.zadd(TradeConstant.ORDER_BATCH_ZSET_KEY,expireTime.getTime(),batchNo);
        return paramMap;
    }

    private OfflineOrder fillOrder(String batchNo, OrderAcceptReqVo orderAcceptReqVo,EmployerMainstayRelation employerMainstayRelation, Date batchTime,String mainstayName) {
        String fileName = orderAcceptReqVo.getFile().getOriginalFilename();
        fileName = fileName.substring(0, fileName.lastIndexOf("."));

        OfflineOrder order = new OfflineOrder();
        order.setCreateTime(batchTime);
        order.setUpdateTime(batchTime);
        order.setMchBatchNo(batchNo);
//        order.setBatchName(String.join("-", DateUtil.formatDate(new Date()),employerAccountInfo.getEmployerName()));
        order.setBatchName(fileName);
        order.setPlatBatchNo(batchNo);
        order.setEmployerNo(employerMainstayRelation.getEmployerNo());
        order.setEmployerName(employerMainstayRelation.getEmployerName());
        order.setMainstayNo(orderAcceptReqVo.getMainstayNo());
        order.setMainstayName(mainstayName);
        order.setBatchStatus(OrderStatusEnum.IMPORTING.getValue());
        order.setWorkCategoryCode(orderAcceptReqVo.getWorkCategoryCode());
        order.setWorkCategoryName(orderAcceptReqVo.getWorkCategoryName());
        order.setServiceDesc(orderAcceptReqVo.getServiceDesc());
        order.setLaunchWay(LaunchWayEnum.EMPLOYER.getValue());
        order.setProductNo(orderAcceptReqVo.getProductNo());
        order.setProductName(orderAcceptReqVo.getProductName());
        order.setJobId(orderAcceptReqVo.getJobId());
        order.setJobName(orderAcceptReqVo.getJobName());
        return order;
    }

    @Override
    public String getBatchNo(String employerNo,Date batchTime) {
        //生成订单
        String batchId = sequenceFacade.nextRedisId("", SequenceBizKeyEnum.CKH_OFFLINE_ORDER_SEQ.getKey(), SequenceBizKeyEnum.CKH_OFFLINE_ORDER_SEQ.getWidth());
        String batchNo = SequenceBizKeyEnum.CKH_OFFLINE_ORDER_SEQ.getPrefix()+ DateUtil.formatCompactDate(batchTime) + batchId;
        log.info("[{}]==>生成订单批次号:{}", employerNo, batchNo);
        return batchNo;
    }

    @Override
    public void handleExcel(File excelFile, Order order, CountVo countVo) {

    }

    public void handleExcel(File excelFile, OfflineOrder order, CountVo countVo) {
        EasyExcel.read(excelFile, BankGrantRow.class, this.getListener(order,countVo)).sheet().headRowNumber(HEAD_ROW_NUMBER).doRead();
        //汇总信息
        order.setRequestCount(countVo.getRequestCount());
        order.setRequestTaskAmount(countVo.getRequestNetAmount());
        order.setFailCount(countVo.getFailCount());
        order.setFailTaskAmount(countVo.getFailNetAmount());
    }

    private AnalysisEventListener<BankGrantRow> getListener(OfflineOrder order, CountVo countVo) {
        return new CKHOffLineGrantDataListener(orderItemBiz,sequenceFacade,notifyFacade,order,countVo,HEAD_ROW_NUMBER,
                this.phoneMustMainstays, MessageMsgDest.TOPIC_TRADE_ASYNC_CKH,MessageMsgDest.TAG_TRADE_ACCEPT_CKH);
    }

    private void doAsync(File excelFile, OfflineOrder order) {
        try{
            CountVo countVo = CountVo.builder()
                    .requestCount(0)
                    .failCount(0)
                    .requestNetAmount(BigDecimal.ZERO)
                    .failNetAmount(BigDecimal.ZERO).build();

            String phoneMustMainstayConfig = dictionaryFacade.getSystemConfig("PHONE_MUST_MAINSTAY");
            if (StringUtils.isNotBlank(phoneMustMainstayConfig)) {
                this.phoneMustMainstays = ListUtil.toList(phoneMustMainstayConfig.split(","));
            }
            //excel处理
            this.handleExcel(excelFile,order,countVo);
            //受理
            offlineOrderFacade.startAccept(order);
            log.info("[{}]==>批次受理初始调用结束", order.getPlatBatchNo());
        }catch (BizException e){
            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);
            order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
            order.setErrorDesc(e.getErrMsg());
            order.setErrorCode(String.valueOf(e.getSysErrorCode()));
            offlineOrderFacade.update(order);
            offlineOrderItemFacade.cancelOrderItem(order.getPlatBatchNo());
        }catch (MchOrderNoException e){
            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);

            String errorMsg = (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > 200) ? e.getMessage().substring(0, 199) : e.getMessage();
            order.setBatchStatus(OrderStatusEnum.IMPORT_FAIL.getValue());
            order.setErrorDesc(errorMsg);

            offlineOrderFacade.update(order);

            orderFacade.cancelOrderItem(order.getPlatBatchNo());
        }catch (Exception e){

            log.error("["+ order.getPlatBatchNo() +"]==>导入发放名单出错", e);

            String errorMsg = (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().length() > 200) ? e.getMessage().substring(0, 199) : e.getMessage();

            if (errorMsg != null && errorMsg.contains("MchOrderNoException")) {
                order.setBatchStatus(OrderStatusEnum.IMPORT_FAIL.getValue());
            }else {
                order.setBatchStatus(OrderStatusEnum.CLOSED_GRANT.getValue());
            }
            order.setErrorDesc(errorMsg);

            offlineOrderFacade.update(order);
            orderFacade.cancelOrderItem(order.getPlatBatchNo());
        }finally {
            log.info("批次[{}] 文件使用完毕,进行删除删除,file:{}",order.getPlatBatchNo(),excelFile.getAbsolutePath());
            boolean isDel = FileUtils.deleteDir(excelFile);
            if(isDel){
                log.info("批次[{}] 文件删除成功",order.getPlatBatchNo());
            }else{
                log.info("批次[{}] 文件删除失败,需手动处理",order.getPlatBatchNo());
            }
        }
    }
}
