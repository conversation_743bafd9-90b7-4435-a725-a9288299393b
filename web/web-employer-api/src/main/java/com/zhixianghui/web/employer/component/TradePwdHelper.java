package com.zhixianghui.web.employer.component;

import com.zhixianghui.common.statics.enums.user.portal.PortalInitPwdStatusEnum;
import com.zhixianghui.common.statics.enums.user.portal.PortalOperatorStatusEnum;
import com.zhixianghui.common.statics.exception.BizException;
import com.zhixianghui.common.statics.exception.CommonExceptions;
import com.zhixianghui.common.util.utils.ValidateUtil;
import com.zhixianghui.facade.merchant.entity.user.portal.MerchantTradePwd;
import com.zhixianghui.facade.merchant.service.MerchantTradePwdFacade;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.constant.PermissionConstant;
import com.zhixianghui.web.employer.utils.PwdDigestUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 商户支付密码
 *
 * <AUTHOR> <PERSON>
 */
@Component
@Log4j2
public class TradePwdHelper {

    @Reference
    private MerchantTradePwdFacade merchantTradePwdFacade;

    /**
     * 查询商户是否设置了支付密码
     * @param mchNo
     * @return
     */
    public boolean isInitTradePwd(String mchNo) {
        MerchantTradePwd mchTradePwd = merchantTradePwdFacade.getByMchNo(mchNo);
        return mchTradePwd != null && mchTradePwd.getIsInitPwd() == PortalInitPwdStatusEnum.INITED.getValue();
    }

    /**
     * 修改支付密码
     */
    public void changeTradePwd(String mchNo, String tradePwd) {
        // 密码复杂度验证
        if(!ValidateUtil.isInteger(tradePwd) ||  tradePwd.length() != 6){
            throw CommonExceptions.PARAM_INVALID.newWithErrMsg("支付密码必须为6位数字");
        }

        // 密码摘要
        PwdDigestUtil.DigestResult digestResult = PwdDigestUtil.digestFixSaltPwd(tradePwd);

        MerchantTradePwd mchTradePwd = merchantTradePwdFacade.getByMchNo(mchNo);
        if (mchTradePwd == null) {  // 没有支付密码记录则直接创建
            mchTradePwd = new MerchantTradePwd();
            mchTradePwd.setCreateTime(new Date());
            mchTradePwd.setMchNo(mchNo);
            mchTradePwd.setStatus(PortalOperatorStatusEnum.ACTIVE.getValue());
            mchTradePwd.setIsInitPwd(PortalInitPwdStatusEnum.INITED.getValue());
            mchTradePwd.setPwd(digestResult.getPwd());
            mchTradePwd.setPwdErrorCount(0);
            mchTradePwd.setPwdErrorTime(null);
            mchTradePwd.getJsonEntity().getHistoryPwdList().add(digestResult.getPwd());
            merchantTradePwdFacade.create(mchTradePwd);
        } else {
            mchTradePwd.setUpdateTime(new Date());
            mchTradePwd.setStatus(PortalOperatorStatusEnum.ACTIVE.getValue());
            mchTradePwd.setIsInitPwd(PortalInitPwdStatusEnum.INITED.getValue());
            mchTradePwd.setPwd(digestResult.getPwd());

            // 不能与历史密码重复
            List<String> historyPwdList = mchTradePwd.getJsonEntity().getHistoryPwdList();
            if(historyPwdList.contains(digestResult.getPwd())){
                throw CommonExceptions.PARAM_INVALID.newWithErrMsg("密码不能与前4次重复");
            }

            // 只存储最近4次密码密文
            if(historyPwdList.size() >= PermissionConstant.MAX_HISTORY_PWD_NUM){
                historyPwdList.add(digestResult.getPwd());
                mchTradePwd.getJsonEntity().setHistoryPwdList(historyPwdList.subList(historyPwdList.size() - PermissionConstant.MAX_HISTORY_PWD_NUM, historyPwdList.size()));
            } else {
                mchTradePwd.getJsonEntity().getHistoryPwdList().add(digestResult.getPwd());
            }

            mchTradePwd.setPwdErrorCount(0);
            merchantTradePwdFacade.update(mchTradePwd);
        }
    }

    /**
     * 验证支付密码
     */
    public void verifyTradePwd(EmployerStaffVO staffVO, String mchNo, String tradePwd) throws BizException {
        MerchantTradePwd mchTradePwd = merchantTradePwdFacade.getByMchNo(mchNo);
        if (mchTradePwd == null || mchTradePwd.getIsInitPwd() == PortalInitPwdStatusEnum.NO_INIT.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("请先设置支付密码");
        }
        if (mchTradePwd.getStatus() == PortalOperatorStatusEnum.INACTIVE.getValue()) {
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg("支付密码已被冻结，请先修改");
        }

        // 验证支付密码
        if (PwdDigestUtil.verifyFixSaltPwd(mchTradePwd.getPwd(), tradePwd)) {
            // 重置错误次数
            mchTradePwd.setPwdErrorCount(0);
            merchantTradePwdFacade.update(mchTradePwd);
        } else {
            // 密码错误
            log.error("支付密码输入错误，操作员：{}-{}，商户：{}，错误密码：{}", staffVO.getPhone(), staffVO.getName(), mchNo, tradePwd);
            // 错误次数加1
            mchTradePwd.setPwdErrorCount(mchTradePwd.getPwdErrorCount() + 1);
            mchTradePwd.setPwdErrorTime(new Date());
            // 超过密码输错次数限制，冻结
            String errMsg;
            if (mchTradePwd.getPwdErrorCount() >= PermissionConstant.TRADE_PWD_ERROR_LIMIT) {
                mchTradePwd.setStatus(PortalOperatorStatusEnum.INACTIVE.getValue());
                errMsg = "支付密码已连续输错【" + PermissionConstant.TRADE_PWD_ERROR_LIMIT + "】次，已被冻结";
            } else {
                errMsg = "支付密码错误，剩余【" + (PermissionConstant.TRADE_PWD_ERROR_LIMIT - mchTradePwd.getPwdErrorCount()) + "】次机会";
            }
            merchantTradePwdFacade.update(mchTradePwd);
            throw CommonExceptions.BIZ_INVALID.newWithErrMsg(errMsg);
        }
    }
}
