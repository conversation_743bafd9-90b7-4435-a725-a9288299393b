package com.zhixianghui.web.employer.biz.report;

import com.alibaba.fastjson.JSON;
import com.zhixianghui.common.util.utils.BeanUtil;
import com.zhixianghui.facade.common.entity.report.EmployerAccountNotifyConfig;
import com.zhixianghui.facade.common.service.EmployerAcctNotifyConfigFacade;
import com.zhixianghui.facade.merchant.entity.Merchant;
import com.zhixianghui.facade.merchant.vo.EmployerStaffVO;
import com.zhixianghui.web.employer.vo.report.EmployerAccountNotifyConfigVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class EmployerAcctNotifyBiz {

    @Reference
    private EmployerAcctNotifyConfigFacade configFacade;

    public void updateConfig(EmployerAccountNotifyConfigVo configVo, EmployerStaffVO staffVo, Merchant merchant) {
        final String merchantName = merchant.getMchName();

        //1. 检查是否已经存在记录
        EmployerAccountNotifyConfig notifyConfig = configFacade.getConfig(configVo.getMainstayNo(),merchant.getMchNo());

        Date now = new Date();
        if (Objects.isNull(notifyConfig)) {
            //插入
            notifyConfig = new EmployerAccountNotifyConfig();
            BeanUtil.copyProperties(configVo, notifyConfig);

            notifyConfig.setEmployerNo(merchant.getMchNo());
            notifyConfig.setEmployerName(merchantName);
            notifyConfig.setCreateTime(now);
            notifyConfig.setUpdateTime(now);
            notifyConfig.setCreateBy(staffVo.getName());
            notifyConfig.setUpdateBy(staffVo.getPhone());
            notifyConfig.setReceiveAccount(JSON.toJSONString(configVo.getReceiveAccount()));

            configFacade.addConfig(notifyConfig);
        } else {
            //修改
            BeanUtil.copyProperties(configVo, notifyConfig);
            notifyConfig.setUpdateTime(now);
            notifyConfig.setUpdateBy(staffVo.getPhone());
            notifyConfig.setReceiveAccount(JSON.toJSONString(configVo.getReceiveAccount()));

            configFacade.updateConfig(notifyConfig);
        }
    }

    public EmployerAccountNotifyConfig getConfig(String mainstayNo, String employerNo) {
        return configFacade.getConfig(mainstayNo, employerNo);
    }
    public List<EmployerAccountNotifyConfig> listNotifyConfig(String employerNo, String mainstayNo) {
        return configFacade.listNotifyConfig(employerNo, mainstayNo);
    }
}
