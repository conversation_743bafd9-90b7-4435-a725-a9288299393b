package com.zhixianghui.web.employer.controller.employee;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhixianghui.facade.employee.dto.WorkerRecordQueryDto;
import com.zhixianghui.facade.employee.entity.JobWorkerRecord;
import com.zhixianghui.facade.employee.enums.SettleStatusEnum;
import com.zhixianghui.facade.employee.service.JobWorkerRecordFacade;
import com.zhixianghui.web.employer.annotation.CurrentMchNo;
import com.zhixianghui.web.employer.biz.excel.ExportBiz;
import com.zhixianghui.web.employer.biz.order.excel.BankGrantRowExcel;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class JobDownloadController {

    @Autowired
    private ExportBiz exportBiz;
    @Reference
    private JobWorkerRecordFacade jobWorkerRecordFacade;

    @GetMapping("download/exporWorkerGrantList")
    public void exporGrantList(@RequestParam(required = true)String jobId, @CurrentMchNo String mchNo, HttpServletResponse servletResponse) {

        WorkerRecordQueryDto workerRecordQueryDto = new WorkerRecordQueryDto();

        workerRecordQueryDto.setEmployerNo(mchNo);
        workerRecordQueryDto.setJobId(jobId);
        workerRecordQueryDto.setSettleStatus(SettleStatusEnum.PENDING_SETTLEMENT.getCode().intValue());
        int current = 1;
        long pageSize = 200L;
        List<JobWorkerRecord> records = null;
        List<BankGrantRowExcel> allData = new ArrayList<>();
        do {
            Page page = new Page(current, pageSize);
            final Page<JobWorkerRecord> workerRecordPage = jobWorkerRecordFacade.workerRecordPage(page, workerRecordQueryDto);
            records = workerRecordPage.getRecords();
            if (records != null && !records.isEmpty()) {
                records.forEach(workerRecord -> {
                    BankGrantRowExcel bankGrantRowExcel = new BankGrantRowExcel();
                    bankGrantRowExcel.setAppId(workerRecord.getMiniAppId());
                    bankGrantRowExcel.setReceiveName(workerRecord.getWorkerName());
                    bankGrantRowExcel.setReceiveAccountNo(workerRecord.getMiniOpenId());
                    bankGrantRowExcel.setReceiveIdCardNo(workerRecord.getWorkerIdcard());
                    bankGrantRowExcel.setReceivePhoneNo(workerRecord.getWorkerPhone());

                    allData.add(bankGrantRowExcel);
                });
            }
            current++;
        } while (records != null && !records.isEmpty());

        if (!allData.isEmpty()) {
            String fileName = StrUtil.format("发放名单-{}", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
            exportBiz.export(servletResponse,fileName,allData, BankGrantRowExcel.class);
        }
    }

}
