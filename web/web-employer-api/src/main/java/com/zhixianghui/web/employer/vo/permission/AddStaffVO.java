package com.zhixianghui.web.employer.vo.permission;

import com.zhixianghui.common.statics.enums.user.portal.PortalStaffTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 添加员工VO
 *
 * <AUTHOR>
 */
@Data
public class AddStaffVO {

    /**
     * 手机号
     */
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = "^1[0-9]{10}$", message = "手机号不正确")
    private String phone;


    /**
     * 员工姓名
     */
    @NotEmpty(message = "员工姓名不能为空")
    @Size(min = 1, max = 50, message = "员工姓名长度必须为1-50")
    private String name;

    /**
     * 角色id
     */
    private List<Long> roleIds;

    /**
     * 支付密码
     */
    @NotEmpty(message = "支付密码不能为空")
    private String tradePwd;
}
